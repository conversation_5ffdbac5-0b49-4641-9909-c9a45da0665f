// Code generated by protoc-gen-gogo.
// source: tbean.proto
// DO NOT EDIT!

/*
	Package tbean is a generated protocol buffer package.

	It is generated from these files:
		tbean.proto

	It has these top-level messages:
		ApiResponseHeader
		GetBalanceRequest
		GetBalanceResponseBody
		TradeRequest
		TradeResponseBody
		TradeNotifyRequest
		TradeNotifyResponseBody
		TransferI2CRequest
		TransferI2CResponseBody
		TransferI2CRefundRequest
		TransferI2CRefundResponseBody
		TransferI2CBalanceReq
		TransferI2CBalanceResp
		PresetFreezeReqData
		ClientData
		PresetFreezeReq
		PresetFreezeResp
		StateData
		PresetFreezeRespData
		UnFreezeAndRefundReqData
		UnFreezeAndRefundReq
		UnFreezeAndRefundResp
		UnfreezeAndConsumeReqData
		UnfreezeAndConsumeReq
		UnfreezeAndConsumeResp
		BlackUserReq
		BlackUserResp
		TransferI2CV2Request
*/
package tbean

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"

import io "io"
import fmt1 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type TradeNotifyRequest_Command int32

const (
	TradeNotifyRequest_COMMIT   TradeNotifyRequest_Command = 0
	TradeNotifyRequest_ROLLBACK TradeNotifyRequest_Command = 1
)

var TradeNotifyRequest_Command_name = map[int32]string{
	0: "COMMIT",
	1: "ROLLBACK",
}
var TradeNotifyRequest_Command_value = map[string]int32{
	"COMMIT":   0,
	"ROLLBACK": 1,
}

func (x TradeNotifyRequest_Command) String() string {
	return proto.EnumName(TradeNotifyRequest_Command_name, int32(x))
}
func (TradeNotifyRequest_Command) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorTbean, []int{5, 0}
}

type ApiResponseHeader struct {
	Result  string `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (m *ApiResponseHeader) Reset()                    { *m = ApiResponseHeader{} }
func (m *ApiResponseHeader) String() string            { return proto.CompactTextString(m) }
func (*ApiResponseHeader) ProtoMessage()               {}
func (*ApiResponseHeader) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{0} }

func (m *ApiResponseHeader) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *ApiResponseHeader) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GetBalanceRequest struct {
	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	Type  string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Uid   uint32 `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
}

func (m *GetBalanceRequest) Reset()                    { *m = GetBalanceRequest{} }
func (m *GetBalanceRequest) String() string            { return proto.CompactTextString(m) }
func (*GetBalanceRequest) ProtoMessage()               {}
func (*GetBalanceRequest) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{1} }

func (m *GetBalanceRequest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *GetBalanceRequest) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *GetBalanceRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetBalanceResponseBody struct {
	Balance int32 `protobuf:"varint,1,opt,name=balance,proto3" json:"balance,omitempty"`
}

func (m *GetBalanceResponseBody) Reset()                    { *m = GetBalanceResponseBody{} }
func (m *GetBalanceResponseBody) String() string            { return proto.CompactTextString(m) }
func (*GetBalanceResponseBody) ProtoMessage()               {}
func (*GetBalanceResponseBody) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{2} }

func (m *GetBalanceResponseBody) GetBalance() int32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

// TradeRequest is the request of both
// /rest/api/consume and /rest/api/prepare
type TradeRequest struct {
	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo    string `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	BuyerId       uint32 `protobuf:"varint,3,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	BuyerName     string `protobuf:"bytes,4,opt,name=buyerName,proto3" json:"buyerName,omitempty"`
	CommodityId   string `protobuf:"bytes,5,opt,name=commodityId,proto3" json:"commodityId,omitempty"`
	CommodityName string `protobuf:"bytes,6,opt,name=commodityName,proto3" json:"commodityName,omitempty"`
	Num           uint32 `protobuf:"varint,7,opt,name=num,proto3" json:"num,omitempty"`
	UnitPrice     uint32 `protobuf:"varint,8,opt,name=unitPrice,proto3" json:"unitPrice,omitempty"`
	Price         uint32 `protobuf:"varint,9,opt,name=price,proto3" json:"price,omitempty"`
	Note          string `protobuf:"bytes,10,opt,name=note,proto3" json:"note,omitempty"`
	Platform      string `protobuf:"bytes,11,opt,name=platform,proto3" json:"platform,omitempty"`
	OutOrderTime  string `protobuf:"bytes,12,opt,name=outOrderTime,proto3" json:"outOrderTime,omitempty"`
	ItemId        string `protobuf:"bytes,13,opt,name=itemId,proto3" json:"itemId,omitempty"`
}

func (m *TradeRequest) Reset()                    { *m = TradeRequest{} }
func (m *TradeRequest) String() string            { return proto.CompactTextString(m) }
func (*TradeRequest) ProtoMessage()               {}
func (*TradeRequest) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{3} }

func (m *TradeRequest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TradeRequest) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TradeRequest) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *TradeRequest) GetBuyerName() string {
	if m != nil {
		return m.BuyerName
	}
	return ""
}

func (m *TradeRequest) GetCommodityId() string {
	if m != nil {
		return m.CommodityId
	}
	return ""
}

func (m *TradeRequest) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *TradeRequest) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *TradeRequest) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *TradeRequest) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *TradeRequest) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *TradeRequest) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *TradeRequest) GetOutOrderTime() string {
	if m != nil {
		return m.OutOrderTime
	}
	return ""
}

func (m *TradeRequest) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

type TradeResponseBody struct {
	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo    string `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	BuyerId       uint32 `protobuf:"varint,3,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	BuyerName     string `protobuf:"bytes,4,opt,name=buyerName,proto3" json:"buyerName,omitempty"`
	CommodityId   string `protobuf:"bytes,5,opt,name=commodityId,proto3" json:"commodityId,omitempty"`
	CommodityName string `protobuf:"bytes,6,opt,name=commodityName,proto3" json:"commodityName,omitempty"`
	Num           uint32 `protobuf:"varint,7,opt,name=num,proto3" json:"num,omitempty"`
	UnitPrice     uint32 `protobuf:"varint,8,opt,name=unitPrice,proto3" json:"unitPrice,omitempty"`
	Price         uint32 `protobuf:"varint,9,opt,name=price,proto3" json:"price,omitempty"`
	TradeNo       string `protobuf:"bytes,10,opt,name=tradeNo,proto3" json:"tradeNo,omitempty"`
	Ctime         string `protobuf:"bytes,11,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Balance       int32  `protobuf:"varint,12,opt,name=balance,proto3" json:"balance,omitempty"`
	DealToken     string `protobuf:"bytes,13,opt,name=dealToken,proto3" json:"dealToken,omitempty"`
}

func (m *TradeResponseBody) Reset()                    { *m = TradeResponseBody{} }
func (m *TradeResponseBody) String() string            { return proto.CompactTextString(m) }
func (*TradeResponseBody) ProtoMessage()               {}
func (*TradeResponseBody) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{4} }

func (m *TradeResponseBody) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TradeResponseBody) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TradeResponseBody) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *TradeResponseBody) GetBuyerName() string {
	if m != nil {
		return m.BuyerName
	}
	return ""
}

func (m *TradeResponseBody) GetCommodityId() string {
	if m != nil {
		return m.CommodityId
	}
	return ""
}

func (m *TradeResponseBody) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *TradeResponseBody) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *TradeResponseBody) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *TradeResponseBody) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *TradeResponseBody) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *TradeResponseBody) GetCtime() string {
	if m != nil {
		return m.Ctime
	}
	return ""
}

func (m *TradeResponseBody) GetBalance() int32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func (m *TradeResponseBody) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type TradeNotifyRequest struct {
	AppId      string                     `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo string                     `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	TradeNo    string                     `protobuf:"bytes,3,opt,name=tradeNo,proto3" json:"tradeNo,omitempty"`
	Command    TradeNotifyRequest_Command `protobuf:"varint,4,opt,name=command,proto3,enum=tbean.TradeNotifyRequest_Command" json:"command,omitempty"`
	Platform   string                     `protobuf:"bytes,5,opt,name=platform,proto3" json:"platform,omitempty"`
}

func (m *TradeNotifyRequest) Reset()                    { *m = TradeNotifyRequest{} }
func (m *TradeNotifyRequest) String() string            { return proto.CompactTextString(m) }
func (*TradeNotifyRequest) ProtoMessage()               {}
func (*TradeNotifyRequest) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{5} }

func (m *TradeNotifyRequest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TradeNotifyRequest) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TradeNotifyRequest) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *TradeNotifyRequest) GetCommand() TradeNotifyRequest_Command {
	if m != nil {
		return m.Command
	}
	return TradeNotifyRequest_COMMIT
}

func (m *TradeNotifyRequest) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

type TradeNotifyResponseBody struct {
	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo string `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	TradeNo    string `protobuf:"bytes,3,opt,name=tradeNo,proto3" json:"tradeNo,omitempty"`
	Status     string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	Mtime      string `protobuf:"bytes,5,opt,name=mtime,proto3" json:"mtime,omitempty"`
}

func (m *TradeNotifyResponseBody) Reset()                    { *m = TradeNotifyResponseBody{} }
func (m *TradeNotifyResponseBody) String() string            { return proto.CompactTextString(m) }
func (*TradeNotifyResponseBody) ProtoMessage()               {}
func (*TradeNotifyResponseBody) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{6} }

func (m *TradeNotifyResponseBody) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TradeNotifyResponseBody) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TradeNotifyResponseBody) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *TradeNotifyResponseBody) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *TradeNotifyResponseBody) GetMtime() string {
	if m != nil {
		return m.Mtime
	}
	return ""
}

// Transfer from internal account to custom account
// API: /rest/api/transfer-i2c
type TransferI2CRequest struct {
	AppId      string                   `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo string                   `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	From       uint32                   `protobuf:"varint,3,opt,name=from,proto3" json:"from,omitempty"`
	To         []*TransferI2CRequest_To `protobuf:"bytes,4,rep,name=to" json:"to,omitempty"`
	Notes      string                   `protobuf:"bytes,5,opt,name=notes,proto3" json:"notes,omitempty"`
	SubId      string                   `protobuf:"bytes,6,opt,name=subId,proto3" json:"subId,omitempty"`
}

func (m *TransferI2CRequest) Reset()                    { *m = TransferI2CRequest{} }
func (m *TransferI2CRequest) String() string            { return proto.CompactTextString(m) }
func (*TransferI2CRequest) ProtoMessage()               {}
func (*TransferI2CRequest) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{7} }

func (m *TransferI2CRequest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TransferI2CRequest) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TransferI2CRequest) GetFrom() uint32 {
	if m != nil {
		return m.From
	}
	return 0
}

func (m *TransferI2CRequest) GetTo() []*TransferI2CRequest_To {
	if m != nil {
		return m.To
	}
	return nil
}

func (m *TransferI2CRequest) GetNotes() string {
	if m != nil {
		return m.Notes
	}
	return ""
}

func (m *TransferI2CRequest) GetSubId() string {
	if m != nil {
		return m.SubId
	}
	return ""
}

type TransferI2CRequest_To struct {
	Uid    uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Amount uint64 `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (m *TransferI2CRequest_To) Reset()                    { *m = TransferI2CRequest_To{} }
func (m *TransferI2CRequest_To) String() string            { return proto.CompactTextString(m) }
func (*TransferI2CRequest_To) ProtoMessage()               {}
func (*TransferI2CRequest_To) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{7, 0} }

func (m *TransferI2CRequest_To) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TransferI2CRequest_To) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type TransferI2CResponseBody struct {
	// nothing
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Balance      uint32 `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
	OutTradeNo   string `protobuf:"bytes,3,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	CheckingTime string `protobuf:"bytes,4,opt,name=checkingTime,proto3" json:"checkingTime,omitempty"`
}

func (m *TransferI2CResponseBody) Reset()                    { *m = TransferI2CResponseBody{} }
func (m *TransferI2CResponseBody) String() string            { return proto.CompactTextString(m) }
func (*TransferI2CResponseBody) ProtoMessage()               {}
func (*TransferI2CResponseBody) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{8} }

func (m *TransferI2CResponseBody) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TransferI2CResponseBody) GetBalance() uint32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func (m *TransferI2CResponseBody) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TransferI2CResponseBody) GetCheckingTime() string {
	if m != nil {
		return m.CheckingTime
	}
	return ""
}

// TransferRefund from internal account to custom account
// API: /rest/api/transfer-i2c/refund
type TransferI2CRefundRequest struct {
	AppId string                         `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	From  uint32                         `protobuf:"varint,2,opt,name=from,proto3" json:"from,omitempty"`
	To    []*TransferI2CRefundRequest_To `protobuf:"bytes,3,rep,name=to" json:"to,omitempty"`
}

func (m *TransferI2CRefundRequest) Reset()                    { *m = TransferI2CRefundRequest{} }
func (m *TransferI2CRefundRequest) String() string            { return proto.CompactTextString(m) }
func (*TransferI2CRefundRequest) ProtoMessage()               {}
func (*TransferI2CRefundRequest) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{9} }

func (m *TransferI2CRefundRequest) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TransferI2CRefundRequest) GetFrom() uint32 {
	if m != nil {
		return m.From
	}
	return 0
}

func (m *TransferI2CRefundRequest) GetTo() []*TransferI2CRefundRequest_To {
	if m != nil {
		return m.To
	}
	return nil
}

type TransferI2CRefundRequest_To struct {
	Uid        uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Amount     uint64 `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	OutTradeNo string `protobuf:"bytes,3,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	Notes      string `protobuf:"bytes,4,opt,name=notes,proto3" json:"notes,omitempty"`
}

func (m *TransferI2CRefundRequest_To) Reset()         { *m = TransferI2CRefundRequest_To{} }
func (m *TransferI2CRefundRequest_To) String() string { return proto.CompactTextString(m) }
func (*TransferI2CRefundRequest_To) ProtoMessage()    {}
func (*TransferI2CRefundRequest_To) Descriptor() ([]byte, []int) {
	return fileDescriptorTbean, []int{9, 0}
}

func (m *TransferI2CRefundRequest_To) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TransferI2CRefundRequest_To) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *TransferI2CRefundRequest_To) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TransferI2CRefundRequest_To) GetNotes() string {
	if m != nil {
		return m.Notes
	}
	return ""
}

type TransferI2CRefundResponseBody struct {
	// nothing
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Balance      uint32 `protobuf:"varint,2,opt,name=balance,proto3" json:"balance,omitempty"`
	OutTradeNo   string `protobuf:"bytes,3,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	CheckingTime string `protobuf:"bytes,4,opt,name=checkingTime,proto3" json:"checkingTime,omitempty"`
}

func (m *TransferI2CRefundResponseBody) Reset()         { *m = TransferI2CRefundResponseBody{} }
func (m *TransferI2CRefundResponseBody) String() string { return proto.CompactTextString(m) }
func (*TransferI2CRefundResponseBody) ProtoMessage()    {}
func (*TransferI2CRefundResponseBody) Descriptor() ([]byte, []int) {
	return fileDescriptorTbean, []int{10}
}

func (m *TransferI2CRefundResponseBody) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TransferI2CRefundResponseBody) GetBalance() uint32 {
	if m != nil {
		return m.Balance
	}
	return 0
}

func (m *TransferI2CRefundResponseBody) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *TransferI2CRefundResponseBody) GetCheckingTime() string {
	if m != nil {
		return m.CheckingTime
	}
	return ""
}

type TransferI2CBalanceReq struct {
	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo string `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
}

func (m *TransferI2CBalanceReq) Reset()                    { *m = TransferI2CBalanceReq{} }
func (m *TransferI2CBalanceReq) String() string            { return proto.CompactTextString(m) }
func (*TransferI2CBalanceReq) ProtoMessage()               {}
func (*TransferI2CBalanceReq) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{11} }

func (m *TransferI2CBalanceReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *TransferI2CBalanceReq) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

type TransferI2CBalanceResp struct {
	CrUid     uint32 `protobuf:"varint,1,opt,name=crUid,proto3" json:"crUid,omitempty"`
	CrAmount  uint32 `protobuf:"varint,2,opt,name=crAmount,proto3" json:"crAmount,omitempty"`
	CrBalance uint32 `protobuf:"varint,3,opt,name=crBalance,proto3" json:"crBalance,omitempty"`
	DrUid     uint32 `protobuf:"varint,4,opt,name=drUid,proto3" json:"drUid,omitempty"`
}

func (m *TransferI2CBalanceResp) Reset()                    { *m = TransferI2CBalanceResp{} }
func (m *TransferI2CBalanceResp) String() string            { return proto.CompactTextString(m) }
func (*TransferI2CBalanceResp) ProtoMessage()               {}
func (*TransferI2CBalanceResp) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{12} }

func (m *TransferI2CBalanceResp) GetCrUid() uint32 {
	if m != nil {
		return m.CrUid
	}
	return 0
}

func (m *TransferI2CBalanceResp) GetCrAmount() uint32 {
	if m != nil {
		return m.CrAmount
	}
	return 0
}

func (m *TransferI2CBalanceResp) GetCrBalance() uint32 {
	if m != nil {
		return m.CrBalance
	}
	return 0
}

func (m *TransferI2CBalanceResp) GetDrUid() uint32 {
	if m != nil {
		return m.DrUid
	}
	return 0
}

// T豆冻结 http://s-doc2.ttyuyin.com/web/#/16/957
type PresetFreezeReqData struct {
	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	BuyerId       uint32 `protobuf:"varint,2,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	FreezeBalance uint32 `protobuf:"varint,3,opt,name=freezeBalance,proto3" json:"freezeBalance,omitempty"`
	OutTradeNo    string `protobuf:"bytes,4,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	FreezeTitle   string `protobuf:"bytes,5,opt,name=freezeTitle,proto3" json:"freezeTitle,omitempty"`
	OutOrderTime  string `protobuf:"bytes,6,opt,name=outOrderTime,proto3" json:"outOrderTime,omitempty"`
	SubId         string `protobuf:"bytes,7,opt,name=subId,proto3" json:"subId,omitempty"`
}

func (m *PresetFreezeReqData) Reset()                    { *m = PresetFreezeReqData{} }
func (m *PresetFreezeReqData) String() string            { return proto.CompactTextString(m) }
func (*PresetFreezeReqData) ProtoMessage()               {}
func (*PresetFreezeReqData) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{13} }

func (m *PresetFreezeReqData) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *PresetFreezeReqData) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *PresetFreezeReqData) GetFreezeBalance() uint32 {
	if m != nil {
		return m.FreezeBalance
	}
	return 0
}

func (m *PresetFreezeReqData) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *PresetFreezeReqData) GetFreezeTitle() string {
	if m != nil {
		return m.FreezeTitle
	}
	return ""
}

func (m *PresetFreezeReqData) GetOutOrderTime() string {
	if m != nil {
		return m.OutOrderTime
	}
	return ""
}

func (m *PresetFreezeReqData) GetSubId() string {
	if m != nil {
		return m.SubId
	}
	return ""
}

type ClientData struct {
	Caller string `protobuf:"bytes,1,opt,name=caller,proto3" json:"caller,omitempty"`
}

func (m *ClientData) Reset()                    { *m = ClientData{} }
func (m *ClientData) String() string            { return proto.CompactTextString(m) }
func (*ClientData) ProtoMessage()               {}
func (*ClientData) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{14} }

func (m *ClientData) GetCaller() string {
	if m != nil {
		return m.Caller
	}
	return ""
}

type PresetFreezeReq struct {
	Data    *PresetFreezeReqData `protobuf:"bytes,1,opt,name=data" json:"data,omitempty"`
	Client  *ClientData          `protobuf:"bytes,2,opt,name=client" json:"client,omitempty"`
	Encrypt string               `protobuf:"bytes,3,opt,name=encrypt,proto3" json:"encrypt,omitempty"`
	Sign    string               `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
}

func (m *PresetFreezeReq) Reset()                    { *m = PresetFreezeReq{} }
func (m *PresetFreezeReq) String() string            { return proto.CompactTextString(m) }
func (*PresetFreezeReq) ProtoMessage()               {}
func (*PresetFreezeReq) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{15} }

func (m *PresetFreezeReq) GetData() *PresetFreezeReqData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PresetFreezeReq) GetClient() *ClientData {
	if m != nil {
		return m.Client
	}
	return nil
}

func (m *PresetFreezeReq) GetEncrypt() string {
	if m != nil {
		return m.Encrypt
	}
	return ""
}

func (m *PresetFreezeReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type PresetFreezeResp struct {
	AppId          string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	BuyerId        uint32 `protobuf:"varint,2,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	FreezeBalance  uint32 `protobuf:"varint,3,opt,name=freezeBalance,proto3" json:"freezeBalance,omitempty"`
	OutTradeNo     string `protobuf:"bytes,4,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	FreezeTitle    string `protobuf:"bytes,5,opt,name=freezeTitle,proto3" json:"freezeTitle,omitempty"`
	OutOrderTime   string `protobuf:"bytes,6,opt,name=outOrderTime,proto3" json:"outOrderTime,omitempty"`
	FinishTime     string `protobuf:"bytes,7,opt,name=finishTime,proto3" json:"finishTime,omitempty"`
	Mtime          string `protobuf:"bytes,8,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Ctime          string `protobuf:"bytes,9,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Status         string `protobuf:"bytes,10,opt,name=status,proto3" json:"status,omitempty"`
	ConsumeBalance uint32 `protobuf:"varint,11,opt,name=consumeBalance,proto3" json:"consumeBalance,omitempty"`
	RefundBalance  uint32 `protobuf:"varint,12,opt,name=refundBalance,proto3" json:"refundBalance,omitempty"`
	RemainBalance  uint32 `protobuf:"varint,13,opt,name=remainBalance,proto3" json:"remainBalance,omitempty"`
}

func (m *PresetFreezeResp) Reset()                    { *m = PresetFreezeResp{} }
func (m *PresetFreezeResp) String() string            { return proto.CompactTextString(m) }
func (*PresetFreezeResp) ProtoMessage()               {}
func (*PresetFreezeResp) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{16} }

func (m *PresetFreezeResp) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *PresetFreezeResp) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *PresetFreezeResp) GetFreezeBalance() uint32 {
	if m != nil {
		return m.FreezeBalance
	}
	return 0
}

func (m *PresetFreezeResp) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *PresetFreezeResp) GetFreezeTitle() string {
	if m != nil {
		return m.FreezeTitle
	}
	return ""
}

func (m *PresetFreezeResp) GetOutOrderTime() string {
	if m != nil {
		return m.OutOrderTime
	}
	return ""
}

func (m *PresetFreezeResp) GetFinishTime() string {
	if m != nil {
		return m.FinishTime
	}
	return ""
}

func (m *PresetFreezeResp) GetMtime() string {
	if m != nil {
		return m.Mtime
	}
	return ""
}

func (m *PresetFreezeResp) GetCtime() string {
	if m != nil {
		return m.Ctime
	}
	return ""
}

func (m *PresetFreezeResp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *PresetFreezeResp) GetConsumeBalance() uint32 {
	if m != nil {
		return m.ConsumeBalance
	}
	return 0
}

func (m *PresetFreezeResp) GetRefundBalance() uint32 {
	if m != nil {
		return m.RefundBalance
	}
	return 0
}

func (m *PresetFreezeResp) GetRemainBalance() uint32 {
	if m != nil {
		return m.RemainBalance
	}
	return 0
}

type StateData struct {
	Code    uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (m *StateData) Reset()                    { *m = StateData{} }
func (m *StateData) String() string            { return proto.CompactTextString(m) }
func (*StateData) ProtoMessage()               {}
func (*StateData) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{17} }

func (m *StateData) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *StateData) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type PresetFreezeRespData struct {
	Data  *PresetFreezeResp `protobuf:"bytes,1,opt,name=data" json:"data,omitempty"`
	State *StateData        `protobuf:"bytes,2,opt,name=state" json:"state,omitempty"`
}

func (m *PresetFreezeRespData) Reset()                    { *m = PresetFreezeRespData{} }
func (m *PresetFreezeRespData) String() string            { return proto.CompactTextString(m) }
func (*PresetFreezeRespData) ProtoMessage()               {}
func (*PresetFreezeRespData) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{18} }

func (m *PresetFreezeRespData) GetData() *PresetFreezeResp {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *PresetFreezeRespData) GetState() *StateData {
	if m != nil {
		return m.State
	}
	return nil
}

// T豆解冻
type UnFreezeAndRefundReqData struct {
	AppId      string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	BuyerId    uint32 `protobuf:"varint,2,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	OutTradeNo string `protobuf:"bytes,3,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
}

func (m *UnFreezeAndRefundReqData) Reset()                    { *m = UnFreezeAndRefundReqData{} }
func (m *UnFreezeAndRefundReqData) String() string            { return proto.CompactTextString(m) }
func (*UnFreezeAndRefundReqData) ProtoMessage()               {}
func (*UnFreezeAndRefundReqData) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{19} }

func (m *UnFreezeAndRefundReqData) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *UnFreezeAndRefundReqData) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *UnFreezeAndRefundReqData) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

type UnFreezeAndRefundReq struct {
	Data    *UnFreezeAndRefundReqData `protobuf:"bytes,1,opt,name=data" json:"data,omitempty"`
	Client  *ClientData               `protobuf:"bytes,2,opt,name=client" json:"client,omitempty"`
	Encrypt string                    `protobuf:"bytes,3,opt,name=encrypt,proto3" json:"encrypt,omitempty"`
	Sign    string                    `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
}

func (m *UnFreezeAndRefundReq) Reset()                    { *m = UnFreezeAndRefundReq{} }
func (m *UnFreezeAndRefundReq) String() string            { return proto.CompactTextString(m) }
func (*UnFreezeAndRefundReq) ProtoMessage()               {}
func (*UnFreezeAndRefundReq) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{20} }

func (m *UnFreezeAndRefundReq) GetData() *UnFreezeAndRefundReqData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *UnFreezeAndRefundReq) GetClient() *ClientData {
	if m != nil {
		return m.Client
	}
	return nil
}

func (m *UnFreezeAndRefundReq) GetEncrypt() string {
	if m != nil {
		return m.Encrypt
	}
	return ""
}

func (m *UnFreezeAndRefundReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type UnFreezeAndRefundResp struct {
	AppId          string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	BuyerId        uint32 `protobuf:"varint,2,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	FreezeBalance  uint32 `protobuf:"varint,3,opt,name=freezeBalance,proto3" json:"freezeBalance,omitempty"`
	OutTradeNo     string `protobuf:"bytes,4,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	FreezeTitle    string `protobuf:"bytes,5,opt,name=freezeTitle,proto3" json:"freezeTitle,omitempty"`
	OutOrderTime   string `protobuf:"bytes,6,opt,name=outOrderTime,proto3" json:"outOrderTime,omitempty"`
	FinishTime     string `protobuf:"bytes,7,opt,name=finishTime,proto3" json:"finishTime,omitempty"`
	Mtime          string `protobuf:"bytes,8,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Ctime          string `protobuf:"bytes,9,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Id             string `protobuf:"bytes,10,opt,name=id,proto3" json:"id,omitempty"`
	Status         string `protobuf:"bytes,11,opt,name=status,proto3" json:"status,omitempty"`
	ConsumeBalance uint32 `protobuf:"varint,12,opt,name=consumeBalance,proto3" json:"consumeBalance,omitempty"`
	RefundBalance  uint32 `protobuf:"varint,13,opt,name=refundBalance,proto3" json:"refundBalance,omitempty"`
}

func (m *UnFreezeAndRefundResp) Reset()                    { *m = UnFreezeAndRefundResp{} }
func (m *UnFreezeAndRefundResp) String() string            { return proto.CompactTextString(m) }
func (*UnFreezeAndRefundResp) ProtoMessage()               {}
func (*UnFreezeAndRefundResp) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{21} }

func (m *UnFreezeAndRefundResp) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *UnFreezeAndRefundResp) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *UnFreezeAndRefundResp) GetFreezeBalance() uint32 {
	if m != nil {
		return m.FreezeBalance
	}
	return 0
}

func (m *UnFreezeAndRefundResp) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *UnFreezeAndRefundResp) GetFreezeTitle() string {
	if m != nil {
		return m.FreezeTitle
	}
	return ""
}

func (m *UnFreezeAndRefundResp) GetOutOrderTime() string {
	if m != nil {
		return m.OutOrderTime
	}
	return ""
}

func (m *UnFreezeAndRefundResp) GetFinishTime() string {
	if m != nil {
		return m.FinishTime
	}
	return ""
}

func (m *UnFreezeAndRefundResp) GetMtime() string {
	if m != nil {
		return m.Mtime
	}
	return ""
}

func (m *UnFreezeAndRefundResp) GetCtime() string {
	if m != nil {
		return m.Ctime
	}
	return ""
}

func (m *UnFreezeAndRefundResp) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UnFreezeAndRefundResp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *UnFreezeAndRefundResp) GetConsumeBalance() uint32 {
	if m != nil {
		return m.ConsumeBalance
	}
	return 0
}

func (m *UnFreezeAndRefundResp) GetRefundBalance() uint32 {
	if m != nil {
		return m.RefundBalance
	}
	return 0
}

// T豆消费
type UnfreezeAndConsumeReqData struct {
	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	BuyerId       uint32 `protobuf:"varint,2,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	BuyerName     string `protobuf:"bytes,3,opt,name=buyerName,proto3" json:"buyerName,omitempty"`
	CommodityId   string `protobuf:"bytes,4,opt,name=commodityId,proto3" json:"commodityId,omitempty"`
	CommodityName string `protobuf:"bytes,5,opt,name=commodityName,proto3" json:"commodityName,omitempty"`
	Num           uint32 `protobuf:"varint,6,opt,name=num,proto3" json:"num,omitempty"`
	UnitPrice     uint32 `protobuf:"varint,7,opt,name=unitPrice,proto3" json:"unitPrice,omitempty"`
	Price         uint32 `protobuf:"varint,8,opt,name=price,proto3" json:"price,omitempty"`
	Platform      string `protobuf:"bytes,9,opt,name=platform,proto3" json:"platform,omitempty"`
	OutTradeNo    string `protobuf:"bytes,10,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	Notes         string `protobuf:"bytes,11,opt,name=notes,proto3" json:"notes,omitempty"`
	OutOrderTime  string `protobuf:"bytes,12,opt,name=outOrderTime,proto3" json:"outOrderTime,omitempty"`
	RefundPrice   uint32 `protobuf:"varint,13,opt,name=refundPrice,proto3" json:"refundPrice,omitempty"`
}

func (m *UnfreezeAndConsumeReqData) Reset()                    { *m = UnfreezeAndConsumeReqData{} }
func (m *UnfreezeAndConsumeReqData) String() string            { return proto.CompactTextString(m) }
func (*UnfreezeAndConsumeReqData) ProtoMessage()               {}
func (*UnfreezeAndConsumeReqData) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{22} }

func (m *UnfreezeAndConsumeReqData) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *UnfreezeAndConsumeReqData) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *UnfreezeAndConsumeReqData) GetBuyerName() string {
	if m != nil {
		return m.BuyerName
	}
	return ""
}

func (m *UnfreezeAndConsumeReqData) GetCommodityId() string {
	if m != nil {
		return m.CommodityId
	}
	return ""
}

func (m *UnfreezeAndConsumeReqData) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *UnfreezeAndConsumeReqData) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *UnfreezeAndConsumeReqData) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *UnfreezeAndConsumeReqData) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *UnfreezeAndConsumeReqData) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

func (m *UnfreezeAndConsumeReqData) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *UnfreezeAndConsumeReqData) GetNotes() string {
	if m != nil {
		return m.Notes
	}
	return ""
}

func (m *UnfreezeAndConsumeReqData) GetOutOrderTime() string {
	if m != nil {
		return m.OutOrderTime
	}
	return ""
}

func (m *UnfreezeAndConsumeReqData) GetRefundPrice() uint32 {
	if m != nil {
		return m.RefundPrice
	}
	return 0
}

type UnfreezeAndConsumeReq struct {
	Data    *UnfreezeAndConsumeReqData `protobuf:"bytes,1,opt,name=data" json:"data,omitempty"`
	Client  *ClientData                `protobuf:"bytes,2,opt,name=client" json:"client,omitempty"`
	Encrypt string                     `protobuf:"bytes,3,opt,name=encrypt,proto3" json:"encrypt,omitempty"`
	Sign    string                     `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
}

func (m *UnfreezeAndConsumeReq) Reset()                    { *m = UnfreezeAndConsumeReq{} }
func (m *UnfreezeAndConsumeReq) String() string            { return proto.CompactTextString(m) }
func (*UnfreezeAndConsumeReq) ProtoMessage()               {}
func (*UnfreezeAndConsumeReq) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{23} }

func (m *UnfreezeAndConsumeReq) GetData() *UnfreezeAndConsumeReqData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *UnfreezeAndConsumeReq) GetClient() *ClientData {
	if m != nil {
		return m.Client
	}
	return nil
}

func (m *UnfreezeAndConsumeReq) GetEncrypt() string {
	if m != nil {
		return m.Encrypt
	}
	return ""
}

func (m *UnfreezeAndConsumeReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type UnfreezeAndConsumeResp struct {
	AppId         string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId,omitempty"`
	OutTradeNo    string `protobuf:"bytes,2,opt,name=outTradeNo,proto3" json:"outTradeNo,omitempty"`
	TradeNo       string `protobuf:"bytes,3,opt,name=tradeNo,proto3" json:"tradeNo,omitempty"`
	BuyerId       uint32 `protobuf:"varint,4,opt,name=buyerId,proto3" json:"buyerId,omitempty"`
	BuyerName     string `protobuf:"bytes,5,opt,name=buyerName,proto3" json:"buyerName,omitempty"`
	CommodityId   string `protobuf:"bytes,6,opt,name=commodityId,proto3" json:"commodityId,omitempty"`
	CommodityName string `protobuf:"bytes,7,opt,name=commodityName,proto3" json:"commodityName,omitempty"`
	Num           uint32 `protobuf:"varint,8,opt,name=num,proto3" json:"num,omitempty"`
	UnitPrice     uint32 `protobuf:"varint,9,opt,name=unitPrice,proto3" json:"unitPrice,omitempty"`
	Price         uint64 `protobuf:"varint,10,opt,name=price,proto3" json:"price,omitempty"`
	Ctime         string `protobuf:"bytes,11,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Platform      uint32 `protobuf:"varint,12,opt,name=platform,proto3" json:"platform,omitempty"`
	DealToken     string `protobuf:"bytes,13,opt,name=dealToken,proto3" json:"dealToken,omitempty"`
}

func (m *UnfreezeAndConsumeResp) Reset()                    { *m = UnfreezeAndConsumeResp{} }
func (m *UnfreezeAndConsumeResp) String() string            { return proto.CompactTextString(m) }
func (*UnfreezeAndConsumeResp) ProtoMessage()               {}
func (*UnfreezeAndConsumeResp) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{24} }

func (m *UnfreezeAndConsumeResp) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *UnfreezeAndConsumeResp) GetOutTradeNo() string {
	if m != nil {
		return m.OutTradeNo
	}
	return ""
}

func (m *UnfreezeAndConsumeResp) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *UnfreezeAndConsumeResp) GetBuyerId() uint32 {
	if m != nil {
		return m.BuyerId
	}
	return 0
}

func (m *UnfreezeAndConsumeResp) GetBuyerName() string {
	if m != nil {
		return m.BuyerName
	}
	return ""
}

func (m *UnfreezeAndConsumeResp) GetCommodityId() string {
	if m != nil {
		return m.CommodityId
	}
	return ""
}

func (m *UnfreezeAndConsumeResp) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *UnfreezeAndConsumeResp) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *UnfreezeAndConsumeResp) GetUnitPrice() uint32 {
	if m != nil {
		return m.UnitPrice
	}
	return 0
}

func (m *UnfreezeAndConsumeResp) GetPrice() uint64 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *UnfreezeAndConsumeResp) GetCtime() string {
	if m != nil {
		return m.Ctime
	}
	return ""
}

func (m *UnfreezeAndConsumeResp) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *UnfreezeAndConsumeResp) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type BlackUserReq struct {
	Uid  string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (m *BlackUserReq) Reset()                    { *m = BlackUserReq{} }
func (m *BlackUserReq) String() string            { return proto.CompactTextString(m) }
func (*BlackUserReq) ProtoMessage()               {}
func (*BlackUserReq) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{25} }

func (m *BlackUserReq) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *BlackUserReq) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

type BlackUserResp struct {
	IsBlackUser bool `protobuf:"varint,1,opt,name=isBlackUser,proto3" json:"isBlackUser,omitempty"`
}

func (m *BlackUserResp) Reset()                    { *m = BlackUserResp{} }
func (m *BlackUserResp) String() string            { return proto.CompactTextString(m) }
func (*BlackUserResp) ProtoMessage()               {}
func (*BlackUserResp) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{26} }

func (m *BlackUserResp) GetIsBlackUser() bool {
	if m != nil {
		return m.IsBlackUser
	}
	return false
}

type TransferI2CV2Request struct {
	Data     *TransferI2CV2Request_Data `protobuf:"bytes,1,opt,name=data" json:"data,omitempty"`
	SignedAt int64                      `protobuf:"varint,2,opt,name=signedAt,proto3" json:"signedAt,omitempty"`
	Sign     string                     `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign,omitempty"`
}

func (m *TransferI2CV2Request) Reset()                    { *m = TransferI2CV2Request{} }
func (m *TransferI2CV2Request) String() string            { return proto.CompactTextString(m) }
func (*TransferI2CV2Request) ProtoMessage()               {}
func (*TransferI2CV2Request) Descriptor() ([]byte, []int) { return fileDescriptorTbean, []int{27} }

func (m *TransferI2CV2Request) GetData() *TransferI2CV2Request_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *TransferI2CV2Request) GetSignedAt() int64 {
	if m != nil {
		return m.SignedAt
	}
	return 0
}

func (m *TransferI2CV2Request) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type TransferI2CV2Request_Data struct {
	AppId    int64  `protobuf:"varint,1,opt,name=appId,proto3" json:"appId,omitempty"`
	TradeNo  string `protobuf:"bytes,2,opt,name=tradeNo,proto3" json:"tradeNo,omitempty"`
	Scope    string `protobuf:"bytes,3,opt,name=scope,proto3" json:"scope,omitempty"`
	SubScope string `protobuf:"bytes,4,opt,name=subScope,proto3" json:"subScope,omitempty"`
	FromId   string `protobuf:"bytes,5,opt,name=fromId,proto3" json:"fromId,omitempty"`
	ToId     string `protobuf:"bytes,6,opt,name=toId,proto3" json:"toId,omitempty"`
	Amount   int64  `protobuf:"varint,7,opt,name=amount,proto3" json:"amount,omitempty"`
	Notes    string `protobuf:"bytes,8,opt,name=notes,proto3" json:"notes,omitempty"`
	At       int64  `protobuf:"varint,9,opt,name=at,proto3" json:"at,omitempty"`
}

func (m *TransferI2CV2Request_Data) Reset()         { *m = TransferI2CV2Request_Data{} }
func (m *TransferI2CV2Request_Data) String() string { return proto.CompactTextString(m) }
func (*TransferI2CV2Request_Data) ProtoMessage()    {}
func (*TransferI2CV2Request_Data) Descriptor() ([]byte, []int) {
	return fileDescriptorTbean, []int{27, 0}
}

func (m *TransferI2CV2Request_Data) GetAppId() int64 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *TransferI2CV2Request_Data) GetTradeNo() string {
	if m != nil {
		return m.TradeNo
	}
	return ""
}

func (m *TransferI2CV2Request_Data) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

func (m *TransferI2CV2Request_Data) GetSubScope() string {
	if m != nil {
		return m.SubScope
	}
	return ""
}

func (m *TransferI2CV2Request_Data) GetFromId() string {
	if m != nil {
		return m.FromId
	}
	return ""
}

func (m *TransferI2CV2Request_Data) GetToId() string {
	if m != nil {
		return m.ToId
	}
	return ""
}

func (m *TransferI2CV2Request_Data) GetAmount() int64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *TransferI2CV2Request_Data) GetNotes() string {
	if m != nil {
		return m.Notes
	}
	return ""
}

func (m *TransferI2CV2Request_Data) GetAt() int64 {
	if m != nil {
		return m.At
	}
	return 0
}

func init() {
	proto.RegisterType((*ApiResponseHeader)(nil), "tbean.ApiResponseHeader")
	proto.RegisterType((*GetBalanceRequest)(nil), "tbean.GetBalanceRequest")
	proto.RegisterType((*GetBalanceResponseBody)(nil), "tbean.GetBalanceResponseBody")
	proto.RegisterType((*TradeRequest)(nil), "tbean.TradeRequest")
	proto.RegisterType((*TradeResponseBody)(nil), "tbean.TradeResponseBody")
	proto.RegisterType((*TradeNotifyRequest)(nil), "tbean.TradeNotifyRequest")
	proto.RegisterType((*TradeNotifyResponseBody)(nil), "tbean.TradeNotifyResponseBody")
	proto.RegisterType((*TransferI2CRequest)(nil), "tbean.TransferI2CRequest")
	proto.RegisterType((*TransferI2CRequest_To)(nil), "tbean.TransferI2CRequest.To")
	proto.RegisterType((*TransferI2CResponseBody)(nil), "tbean.TransferI2CResponseBody")
	proto.RegisterType((*TransferI2CRefundRequest)(nil), "tbean.TransferI2CRefundRequest")
	proto.RegisterType((*TransferI2CRefundRequest_To)(nil), "tbean.TransferI2CRefundRequest.To")
	proto.RegisterType((*TransferI2CRefundResponseBody)(nil), "tbean.TransferI2CRefundResponseBody")
	proto.RegisterType((*TransferI2CBalanceReq)(nil), "tbean.TransferI2CBalanceReq")
	proto.RegisterType((*TransferI2CBalanceResp)(nil), "tbean.TransferI2CBalanceResp")
	proto.RegisterType((*PresetFreezeReqData)(nil), "tbean.PresetFreezeReqData")
	proto.RegisterType((*ClientData)(nil), "tbean.ClientData")
	proto.RegisterType((*PresetFreezeReq)(nil), "tbean.PresetFreezeReq")
	proto.RegisterType((*PresetFreezeResp)(nil), "tbean.PresetFreezeResp")
	proto.RegisterType((*StateData)(nil), "tbean.StateData")
	proto.RegisterType((*PresetFreezeRespData)(nil), "tbean.PresetFreezeRespData")
	proto.RegisterType((*UnFreezeAndRefundReqData)(nil), "tbean.UnFreezeAndRefundReqData")
	proto.RegisterType((*UnFreezeAndRefundReq)(nil), "tbean.UnFreezeAndRefundReq")
	proto.RegisterType((*UnFreezeAndRefundResp)(nil), "tbean.UnFreezeAndRefundResp")
	proto.RegisterType((*UnfreezeAndConsumeReqData)(nil), "tbean.UnfreezeAndConsumeReqData")
	proto.RegisterType((*UnfreezeAndConsumeReq)(nil), "tbean.UnfreezeAndConsumeReq")
	proto.RegisterType((*UnfreezeAndConsumeResp)(nil), "tbean.UnfreezeAndConsumeResp")
	proto.RegisterType((*BlackUserReq)(nil), "tbean.BlackUserReq")
	proto.RegisterType((*BlackUserResp)(nil), "tbean.BlackUserResp")
	proto.RegisterType((*TransferI2CV2Request)(nil), "tbean.TransferI2CV2Request")
	proto.RegisterType((*TransferI2CV2Request_Data)(nil), "tbean.TransferI2CV2Request.Data")
	proto.RegisterEnum("tbean.TradeNotifyRequest_Command", TradeNotifyRequest_Command_name, TradeNotifyRequest_Command_value)
}
func (m *ApiResponseHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApiResponseHeader) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Result) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Result)))
		i += copy(dAtA[i:], m.Result)
	}
	if len(m.Message) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Message)))
		i += copy(dAtA[i:], m.Message)
	}
	return i, nil
}

func (m *GetBalanceRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBalanceRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if len(m.Type) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Type)))
		i += copy(dAtA[i:], m.Type)
	}
	if m.Uid != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Uid))
	}
	return i, nil
}

func (m *GetBalanceResponseBody) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBalanceResponseBody) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Balance != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Balance))
	}
	return i, nil
}

func (m *TradeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TradeRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if m.BuyerId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.BuyerId))
	}
	if len(m.BuyerName) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.BuyerName)))
		i += copy(dAtA[i:], m.BuyerName)
	}
	if len(m.CommodityId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CommodityId)))
		i += copy(dAtA[i:], m.CommodityId)
	}
	if len(m.CommodityName) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CommodityName)))
		i += copy(dAtA[i:], m.CommodityName)
	}
	if m.Num != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Num))
	}
	if m.UnitPrice != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.UnitPrice))
	}
	if m.Price != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Price))
	}
	if len(m.Note) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Note)))
		i += copy(dAtA[i:], m.Note)
	}
	if len(m.Platform) > 0 {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if len(m.OutOrderTime) > 0 {
		dAtA[i] = 0x62
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutOrderTime)))
		i += copy(dAtA[i:], m.OutOrderTime)
	}
	if len(m.ItemId) > 0 {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.ItemId)))
		i += copy(dAtA[i:], m.ItemId)
	}
	return i, nil
}

func (m *TradeResponseBody) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TradeResponseBody) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if m.BuyerId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.BuyerId))
	}
	if len(m.BuyerName) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.BuyerName)))
		i += copy(dAtA[i:], m.BuyerName)
	}
	if len(m.CommodityId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CommodityId)))
		i += copy(dAtA[i:], m.CommodityId)
	}
	if len(m.CommodityName) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CommodityName)))
		i += copy(dAtA[i:], m.CommodityName)
	}
	if m.Num != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Num))
	}
	if m.UnitPrice != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.UnitPrice))
	}
	if m.Price != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Price))
	}
	if len(m.TradeNo) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.TradeNo)))
		i += copy(dAtA[i:], m.TradeNo)
	}
	if len(m.Ctime) > 0 {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Ctime)))
		i += copy(dAtA[i:], m.Ctime)
	}
	if m.Balance != 0 {
		dAtA[i] = 0x60
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Balance))
	}
	if len(m.DealToken) > 0 {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.DealToken)))
		i += copy(dAtA[i:], m.DealToken)
	}
	return i, nil
}

func (m *TradeNotifyRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TradeNotifyRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.TradeNo) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.TradeNo)))
		i += copy(dAtA[i:], m.TradeNo)
	}
	if m.Command != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Command))
	}
	if len(m.Platform) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	return i, nil
}

func (m *TradeNotifyResponseBody) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TradeNotifyResponseBody) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.TradeNo) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.TradeNo)))
		i += copy(dAtA[i:], m.TradeNo)
	}
	if len(m.Status) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Status)))
		i += copy(dAtA[i:], m.Status)
	}
	if len(m.Mtime) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Mtime)))
		i += copy(dAtA[i:], m.Mtime)
	}
	return i, nil
}

func (m *TransferI2CRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if m.From != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.From))
	}
	if len(m.To) > 0 {
		for _, msg := range m.To {
			dAtA[i] = 0x22
			i++
			i = encodeVarintTbean(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Notes) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Notes)))
		i += copy(dAtA[i:], m.Notes)
	}
	if len(m.SubId) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.SubId)))
		i += copy(dAtA[i:], m.SubId)
	}
	return i, nil
}

func (m *TransferI2CRequest_To) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CRequest_To) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Uid))
	}
	if m.Amount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Amount))
	}
	return i, nil
}

func (m *TransferI2CResponseBody) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CResponseBody) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Uid))
	}
	if m.Balance != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Balance))
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.CheckingTime) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CheckingTime)))
		i += copy(dAtA[i:], m.CheckingTime)
	}
	return i, nil
}

func (m *TransferI2CRefundRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CRefundRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if m.From != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.From))
	}
	if len(m.To) > 0 {
		for _, msg := range m.To {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintTbean(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *TransferI2CRefundRequest_To) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CRefundRequest_To) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Uid))
	}
	if m.Amount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Amount))
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.Notes) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Notes)))
		i += copy(dAtA[i:], m.Notes)
	}
	return i, nil
}

func (m *TransferI2CRefundResponseBody) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CRefundResponseBody) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Uid))
	}
	if m.Balance != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Balance))
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.CheckingTime) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CheckingTime)))
		i += copy(dAtA[i:], m.CheckingTime)
	}
	return i, nil
}

func (m *TransferI2CBalanceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CBalanceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	return i, nil
}

func (m *TransferI2CBalanceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CBalanceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CrUid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.CrUid))
	}
	if m.CrAmount != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.CrAmount))
	}
	if m.CrBalance != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.CrBalance))
	}
	if m.DrUid != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.DrUid))
	}
	return i, nil
}

func (m *PresetFreezeReqData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresetFreezeReqData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if m.BuyerId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.BuyerId))
	}
	if m.FreezeBalance != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.FreezeBalance))
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.FreezeTitle) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.FreezeTitle)))
		i += copy(dAtA[i:], m.FreezeTitle)
	}
	if len(m.OutOrderTime) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutOrderTime)))
		i += copy(dAtA[i:], m.OutOrderTime)
	}
	if len(m.SubId) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.SubId)))
		i += copy(dAtA[i:], m.SubId)
	}
	return i, nil
}

func (m *ClientData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClientData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Caller) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Caller)))
		i += copy(dAtA[i:], m.Caller)
	}
	return i, nil
}

func (m *PresetFreezeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresetFreezeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Data.Size()))
		n1, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.Client != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Client.Size()))
		n2, err := m.Client.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.Encrypt) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Encrypt)))
		i += copy(dAtA[i:], m.Encrypt)
	}
	if len(m.Sign) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Sign)))
		i += copy(dAtA[i:], m.Sign)
	}
	return i, nil
}

func (m *PresetFreezeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresetFreezeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if m.BuyerId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.BuyerId))
	}
	if m.FreezeBalance != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.FreezeBalance))
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.FreezeTitle) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.FreezeTitle)))
		i += copy(dAtA[i:], m.FreezeTitle)
	}
	if len(m.OutOrderTime) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutOrderTime)))
		i += copy(dAtA[i:], m.OutOrderTime)
	}
	if len(m.FinishTime) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.FinishTime)))
		i += copy(dAtA[i:], m.FinishTime)
	}
	if len(m.Mtime) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Mtime)))
		i += copy(dAtA[i:], m.Mtime)
	}
	if len(m.Ctime) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Ctime)))
		i += copy(dAtA[i:], m.Ctime)
	}
	if len(m.Status) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Status)))
		i += copy(dAtA[i:], m.Status)
	}
	if m.ConsumeBalance != 0 {
		dAtA[i] = 0x58
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.ConsumeBalance))
	}
	if m.RefundBalance != 0 {
		dAtA[i] = 0x60
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.RefundBalance))
	}
	if m.RemainBalance != 0 {
		dAtA[i] = 0x68
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.RemainBalance))
	}
	return i, nil
}

func (m *StateData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StateData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Code != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Code))
	}
	if len(m.Message) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Message)))
		i += copy(dAtA[i:], m.Message)
	}
	return i, nil
}

func (m *PresetFreezeRespData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresetFreezeRespData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Data.Size()))
		n3, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.State != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.State.Size()))
		n4, err := m.State.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *UnFreezeAndRefundReqData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnFreezeAndRefundReqData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if m.BuyerId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.BuyerId))
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	return i, nil
}

func (m *UnFreezeAndRefundReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnFreezeAndRefundReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Data.Size()))
		n5, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.Client != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Client.Size()))
		n6, err := m.Client.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if len(m.Encrypt) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Encrypt)))
		i += copy(dAtA[i:], m.Encrypt)
	}
	if len(m.Sign) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Sign)))
		i += copy(dAtA[i:], m.Sign)
	}
	return i, nil
}

func (m *UnFreezeAndRefundResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnFreezeAndRefundResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if m.BuyerId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.BuyerId))
	}
	if m.FreezeBalance != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.FreezeBalance))
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.FreezeTitle) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.FreezeTitle)))
		i += copy(dAtA[i:], m.FreezeTitle)
	}
	if len(m.OutOrderTime) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutOrderTime)))
		i += copy(dAtA[i:], m.OutOrderTime)
	}
	if len(m.FinishTime) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.FinishTime)))
		i += copy(dAtA[i:], m.FinishTime)
	}
	if len(m.Mtime) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Mtime)))
		i += copy(dAtA[i:], m.Mtime)
	}
	if len(m.Ctime) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Ctime)))
		i += copy(dAtA[i:], m.Ctime)
	}
	if len(m.Id) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Id)))
		i += copy(dAtA[i:], m.Id)
	}
	if len(m.Status) > 0 {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Status)))
		i += copy(dAtA[i:], m.Status)
	}
	if m.ConsumeBalance != 0 {
		dAtA[i] = 0x60
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.ConsumeBalance))
	}
	if m.RefundBalance != 0 {
		dAtA[i] = 0x68
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.RefundBalance))
	}
	return i, nil
}

func (m *UnfreezeAndConsumeReqData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnfreezeAndConsumeReqData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if m.BuyerId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.BuyerId))
	}
	if len(m.BuyerName) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.BuyerName)))
		i += copy(dAtA[i:], m.BuyerName)
	}
	if len(m.CommodityId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CommodityId)))
		i += copy(dAtA[i:], m.CommodityId)
	}
	if len(m.CommodityName) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CommodityName)))
		i += copy(dAtA[i:], m.CommodityName)
	}
	if m.Num != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Num))
	}
	if m.UnitPrice != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.UnitPrice))
	}
	if m.Price != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Price))
	}
	if len(m.Platform) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Platform)))
		i += copy(dAtA[i:], m.Platform)
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x52
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.Notes) > 0 {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Notes)))
		i += copy(dAtA[i:], m.Notes)
	}
	if len(m.OutOrderTime) > 0 {
		dAtA[i] = 0x62
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutOrderTime)))
		i += copy(dAtA[i:], m.OutOrderTime)
	}
	if m.RefundPrice != 0 {
		dAtA[i] = 0x68
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.RefundPrice))
	}
	return i, nil
}

func (m *UnfreezeAndConsumeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnfreezeAndConsumeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Data.Size()))
		n7, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.Client != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Client.Size()))
		n8, err := m.Client.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if len(m.Encrypt) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Encrypt)))
		i += copy(dAtA[i:], m.Encrypt)
	}
	if len(m.Sign) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Sign)))
		i += copy(dAtA[i:], m.Sign)
	}
	return i, nil
}

func (m *UnfreezeAndConsumeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnfreezeAndConsumeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AppId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.AppId)))
		i += copy(dAtA[i:], m.AppId)
	}
	if len(m.OutTradeNo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.OutTradeNo)))
		i += copy(dAtA[i:], m.OutTradeNo)
	}
	if len(m.TradeNo) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.TradeNo)))
		i += copy(dAtA[i:], m.TradeNo)
	}
	if m.BuyerId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.BuyerId))
	}
	if len(m.BuyerName) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.BuyerName)))
		i += copy(dAtA[i:], m.BuyerName)
	}
	if len(m.CommodityId) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CommodityId)))
		i += copy(dAtA[i:], m.CommodityId)
	}
	if len(m.CommodityName) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.CommodityName)))
		i += copy(dAtA[i:], m.CommodityName)
	}
	if m.Num != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Num))
	}
	if m.UnitPrice != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.UnitPrice))
	}
	if m.Price != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Price))
	}
	if len(m.Ctime) > 0 {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Ctime)))
		i += copy(dAtA[i:], m.Ctime)
	}
	if m.Platform != 0 {
		dAtA[i] = 0x60
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Platform))
	}
	if len(m.DealToken) > 0 {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.DealToken)))
		i += copy(dAtA[i:], m.DealToken)
	}
	return i, nil
}

func (m *BlackUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BlackUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Uid) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Uid)))
		i += copy(dAtA[i:], m.Uid)
	}
	if len(m.Type) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Type)))
		i += copy(dAtA[i:], m.Type)
	}
	return i, nil
}

func (m *BlackUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BlackUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.IsBlackUser {
		dAtA[i] = 0x8
		i++
		if m.IsBlackUser {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *TransferI2CV2Request) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CV2Request) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Data.Size()))
		n9, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if m.SignedAt != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.SignedAt))
	}
	if len(m.Sign) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Sign)))
		i += copy(dAtA[i:], m.Sign)
	}
	return i, nil
}

func (m *TransferI2CV2Request_Data) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferI2CV2Request_Data) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AppId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.AppId))
	}
	if len(m.TradeNo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.TradeNo)))
		i += copy(dAtA[i:], m.TradeNo)
	}
	if len(m.Scope) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Scope)))
		i += copy(dAtA[i:], m.Scope)
	}
	if len(m.SubScope) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.SubScope)))
		i += copy(dAtA[i:], m.SubScope)
	}
	if len(m.FromId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.FromId)))
		i += copy(dAtA[i:], m.FromId)
	}
	if len(m.ToId) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.ToId)))
		i += copy(dAtA[i:], m.ToId)
	}
	if m.Amount != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.Amount))
	}
	if len(m.Notes) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintTbean(dAtA, i, uint64(len(m.Notes)))
		i += copy(dAtA[i:], m.Notes)
	}
	if m.At != 0 {
		dAtA[i] = 0x48
		i++
		i = encodeVarintTbean(dAtA, i, uint64(m.At))
	}
	return i, nil
}

func encodeFixed64Tbean(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Tbean(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintTbean(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ApiResponseHeader) Size() (n int) {
	var l int
	_ = l
	l = len(m.Result)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *GetBalanceRequest) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Uid != 0 {
		n += 1 + sovTbean(uint64(m.Uid))
	}
	return n
}

func (m *GetBalanceResponseBody) Size() (n int) {
	var l int
	_ = l
	if m.Balance != 0 {
		n += 1 + sovTbean(uint64(m.Balance))
	}
	return n
}

func (m *TradeRequest) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.BuyerId != 0 {
		n += 1 + sovTbean(uint64(m.BuyerId))
	}
	l = len(m.BuyerName)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CommodityId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CommodityName)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Num != 0 {
		n += 1 + sovTbean(uint64(m.Num))
	}
	if m.UnitPrice != 0 {
		n += 1 + sovTbean(uint64(m.UnitPrice))
	}
	if m.Price != 0 {
		n += 1 + sovTbean(uint64(m.Price))
	}
	l = len(m.Note)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutOrderTime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.ItemId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TradeResponseBody) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.BuyerId != 0 {
		n += 1 + sovTbean(uint64(m.BuyerId))
	}
	l = len(m.BuyerName)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CommodityId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CommodityName)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Num != 0 {
		n += 1 + sovTbean(uint64(m.Num))
	}
	if m.UnitPrice != 0 {
		n += 1 + sovTbean(uint64(m.UnitPrice))
	}
	if m.Price != 0 {
		n += 1 + sovTbean(uint64(m.Price))
	}
	l = len(m.TradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Ctime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Balance != 0 {
		n += 1 + sovTbean(uint64(m.Balance))
	}
	l = len(m.DealToken)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TradeNotifyRequest) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.TradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Command != 0 {
		n += 1 + sovTbean(uint64(m.Command))
	}
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TradeNotifyResponseBody) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.TradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Mtime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TransferI2CRequest) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.From != 0 {
		n += 1 + sovTbean(uint64(m.From))
	}
	if len(m.To) > 0 {
		for _, e := range m.To {
			l = e.Size()
			n += 1 + l + sovTbean(uint64(l))
		}
	}
	l = len(m.Notes)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.SubId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TransferI2CRequest_To) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovTbean(uint64(m.Uid))
	}
	if m.Amount != 0 {
		n += 1 + sovTbean(uint64(m.Amount))
	}
	return n
}

func (m *TransferI2CResponseBody) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovTbean(uint64(m.Uid))
	}
	if m.Balance != 0 {
		n += 1 + sovTbean(uint64(m.Balance))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CheckingTime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TransferI2CRefundRequest) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.From != 0 {
		n += 1 + sovTbean(uint64(m.From))
	}
	if len(m.To) > 0 {
		for _, e := range m.To {
			l = e.Size()
			n += 1 + l + sovTbean(uint64(l))
		}
	}
	return n
}

func (m *TransferI2CRefundRequest_To) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovTbean(uint64(m.Uid))
	}
	if m.Amount != 0 {
		n += 1 + sovTbean(uint64(m.Amount))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Notes)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TransferI2CRefundResponseBody) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovTbean(uint64(m.Uid))
	}
	if m.Balance != 0 {
		n += 1 + sovTbean(uint64(m.Balance))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CheckingTime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TransferI2CBalanceReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TransferI2CBalanceResp) Size() (n int) {
	var l int
	_ = l
	if m.CrUid != 0 {
		n += 1 + sovTbean(uint64(m.CrUid))
	}
	if m.CrAmount != 0 {
		n += 1 + sovTbean(uint64(m.CrAmount))
	}
	if m.CrBalance != 0 {
		n += 1 + sovTbean(uint64(m.CrBalance))
	}
	if m.DrUid != 0 {
		n += 1 + sovTbean(uint64(m.DrUid))
	}
	return n
}

func (m *PresetFreezeReqData) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.BuyerId != 0 {
		n += 1 + sovTbean(uint64(m.BuyerId))
	}
	if m.FreezeBalance != 0 {
		n += 1 + sovTbean(uint64(m.FreezeBalance))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.FreezeTitle)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutOrderTime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.SubId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *ClientData) Size() (n int) {
	var l int
	_ = l
	l = len(m.Caller)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *PresetFreezeReq) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Client != nil {
		l = m.Client.Size()
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Encrypt)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Sign)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *PresetFreezeResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.BuyerId != 0 {
		n += 1 + sovTbean(uint64(m.BuyerId))
	}
	if m.FreezeBalance != 0 {
		n += 1 + sovTbean(uint64(m.FreezeBalance))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.FreezeTitle)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutOrderTime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.FinishTime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Mtime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Ctime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.ConsumeBalance != 0 {
		n += 1 + sovTbean(uint64(m.ConsumeBalance))
	}
	if m.RefundBalance != 0 {
		n += 1 + sovTbean(uint64(m.RefundBalance))
	}
	if m.RemainBalance != 0 {
		n += 1 + sovTbean(uint64(m.RemainBalance))
	}
	return n
}

func (m *StateData) Size() (n int) {
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovTbean(uint64(m.Code))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *PresetFreezeRespData) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.State != nil {
		l = m.State.Size()
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *UnFreezeAndRefundReqData) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.BuyerId != 0 {
		n += 1 + sovTbean(uint64(m.BuyerId))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *UnFreezeAndRefundReq) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Client != nil {
		l = m.Client.Size()
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Encrypt)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Sign)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *UnFreezeAndRefundResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.BuyerId != 0 {
		n += 1 + sovTbean(uint64(m.BuyerId))
	}
	if m.FreezeBalance != 0 {
		n += 1 + sovTbean(uint64(m.FreezeBalance))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.FreezeTitle)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutOrderTime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.FinishTime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Mtime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Ctime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Id)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.ConsumeBalance != 0 {
		n += 1 + sovTbean(uint64(m.ConsumeBalance))
	}
	if m.RefundBalance != 0 {
		n += 1 + sovTbean(uint64(m.RefundBalance))
	}
	return n
}

func (m *UnfreezeAndConsumeReqData) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.BuyerId != 0 {
		n += 1 + sovTbean(uint64(m.BuyerId))
	}
	l = len(m.BuyerName)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CommodityId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CommodityName)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Num != 0 {
		n += 1 + sovTbean(uint64(m.Num))
	}
	if m.UnitPrice != 0 {
		n += 1 + sovTbean(uint64(m.UnitPrice))
	}
	if m.Price != 0 {
		n += 1 + sovTbean(uint64(m.Price))
	}
	l = len(m.Platform)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Notes)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutOrderTime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.RefundPrice != 0 {
		n += 1 + sovTbean(uint64(m.RefundPrice))
	}
	return n
}

func (m *UnfreezeAndConsumeReq) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Client != nil {
		l = m.Client.Size()
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Encrypt)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Sign)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *UnfreezeAndConsumeResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.OutTradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.TradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.BuyerId != 0 {
		n += 1 + sovTbean(uint64(m.BuyerId))
	}
	l = len(m.BuyerName)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CommodityId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.CommodityName)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Num != 0 {
		n += 1 + sovTbean(uint64(m.Num))
	}
	if m.UnitPrice != 0 {
		n += 1 + sovTbean(uint64(m.UnitPrice))
	}
	if m.Price != 0 {
		n += 1 + sovTbean(uint64(m.Price))
	}
	l = len(m.Ctime)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Platform != 0 {
		n += 1 + sovTbean(uint64(m.Platform))
	}
	l = len(m.DealToken)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *BlackUserReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Uid)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *BlackUserResp) Size() (n int) {
	var l int
	_ = l
	if m.IsBlackUser {
		n += 2
	}
	return n
}

func (m *TransferI2CV2Request) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.SignedAt != 0 {
		n += 1 + sovTbean(uint64(m.SignedAt))
	}
	l = len(m.Sign)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	return n
}

func (m *TransferI2CV2Request_Data) Size() (n int) {
	var l int
	_ = l
	if m.AppId != 0 {
		n += 1 + sovTbean(uint64(m.AppId))
	}
	l = len(m.TradeNo)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.Scope)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.SubScope)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.FromId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	l = len(m.ToId)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.Amount != 0 {
		n += 1 + sovTbean(uint64(m.Amount))
	}
	l = len(m.Notes)
	if l > 0 {
		n += 1 + l + sovTbean(uint64(l))
	}
	if m.At != 0 {
		n += 1 + sovTbean(uint64(m.At))
	}
	return n
}

func sovTbean(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozTbean(x uint64) (n int) {
	return sovTbean(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ApiResponseHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ApiResponseHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ApiResponseHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Result = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBalanceRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GetBalanceRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GetBalanceRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBalanceResponseBody) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GetBalanceResponseBody: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GetBalanceResponseBody: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Balance", wireType)
			}
			m.Balance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Balance |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TradeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TradeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TradeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerId", wireType)
			}
			m.BuyerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BuyerName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommodityId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommodityId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommodityName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommodityName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UnitPrice", wireType)
			}
			m.UnitPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnitPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Note", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Note = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutOrderTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutOrderTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ItemId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TradeResponseBody) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TradeResponseBody: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TradeResponseBody: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerId", wireType)
			}
			m.BuyerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BuyerName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommodityId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommodityId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommodityName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommodityName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UnitPrice", wireType)
			}
			m.UnitPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnitPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ctime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ctime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Balance", wireType)
			}
			m.Balance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Balance |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DealToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DealToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TradeNotifyRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TradeNotifyRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TradeNotifyRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Command", wireType)
			}
			m.Command = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Command |= (TradeNotifyRequest_Command(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TradeNotifyResponseBody) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TradeNotifyResponseBody: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TradeNotifyResponseBody: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Mtime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Mtime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TransferI2CRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TransferI2CRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field To", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.To = append(m.To, &TransferI2CRequest_To{})
			if err := m.To[len(m.To)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Notes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Notes = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SubId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SubId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CRequest_To) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: To: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: To: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CResponseBody) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TransferI2CResponseBody: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TransferI2CResponseBody: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Balance", wireType)
			}
			m.Balance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Balance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CheckingTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CheckingTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CRefundRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TransferI2CRefundRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TransferI2CRefundRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field To", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.To = append(m.To, &TransferI2CRefundRequest_To{})
			if err := m.To[len(m.To)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CRefundRequest_To) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: To: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: To: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Notes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Notes = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CRefundResponseBody) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TransferI2CRefundResponseBody: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TransferI2CRefundResponseBody: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Balance", wireType)
			}
			m.Balance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Balance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CheckingTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CheckingTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CBalanceReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TransferI2CBalanceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TransferI2CBalanceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CBalanceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TransferI2CBalanceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TransferI2CBalanceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CrUid", wireType)
			}
			m.CrUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CrUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CrAmount", wireType)
			}
			m.CrAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CrAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CrBalance", wireType)
			}
			m.CrBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CrBalance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DrUid", wireType)
			}
			m.DrUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DrUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresetFreezeReqData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: PresetFreezeReqData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: PresetFreezeReqData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerId", wireType)
			}
			m.BuyerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FreezeBalance", wireType)
			}
			m.FreezeBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreezeBalance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FreezeTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FreezeTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutOrderTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutOrderTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SubId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SubId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClientData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ClientData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ClientData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Caller", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Caller = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresetFreezeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: PresetFreezeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: PresetFreezeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &PresetFreezeReqData{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Client", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Client == nil {
				m.Client = &ClientData{}
			}
			if err := m.Client.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Encrypt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Encrypt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Sign", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sign = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresetFreezeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: PresetFreezeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: PresetFreezeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerId", wireType)
			}
			m.BuyerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FreezeBalance", wireType)
			}
			m.FreezeBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreezeBalance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FreezeTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FreezeTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutOrderTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutOrderTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FinishTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FinishTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Mtime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Mtime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ctime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ctime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ConsumeBalance", wireType)
			}
			m.ConsumeBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeBalance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RefundBalance", wireType)
			}
			m.RefundBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RefundBalance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RemainBalance", wireType)
			}
			m.RemainBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainBalance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StateData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: StateData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: StateData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresetFreezeRespData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: PresetFreezeRespData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: PresetFreezeRespData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &PresetFreezeResp{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.State == nil {
				m.State = &StateData{}
			}
			if err := m.State.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnFreezeAndRefundReqData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UnFreezeAndRefundReqData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UnFreezeAndRefundReqData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerId", wireType)
			}
			m.BuyerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnFreezeAndRefundReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UnFreezeAndRefundReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UnFreezeAndRefundReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &UnFreezeAndRefundReqData{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Client", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Client == nil {
				m.Client = &ClientData{}
			}
			if err := m.Client.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Encrypt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Encrypt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Sign", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sign = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnFreezeAndRefundResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UnFreezeAndRefundResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UnFreezeAndRefundResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerId", wireType)
			}
			m.BuyerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FreezeBalance", wireType)
			}
			m.FreezeBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreezeBalance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FreezeTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FreezeTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutOrderTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutOrderTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FinishTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FinishTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Mtime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Mtime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ctime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ctime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ConsumeBalance", wireType)
			}
			m.ConsumeBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConsumeBalance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RefundBalance", wireType)
			}
			m.RefundBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RefundBalance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnfreezeAndConsumeReqData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UnfreezeAndConsumeReqData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UnfreezeAndConsumeReqData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerId", wireType)
			}
			m.BuyerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BuyerName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommodityId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommodityId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommodityName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommodityName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UnitPrice", wireType)
			}
			m.UnitPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnitPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Platform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Notes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Notes = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutOrderTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutOrderTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field RefundPrice", wireType)
			}
			m.RefundPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RefundPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnfreezeAndConsumeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UnfreezeAndConsumeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UnfreezeAndConsumeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &UnfreezeAndConsumeReqData{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Client", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Client == nil {
				m.Client = &ClientData{}
			}
			if err := m.Client.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Encrypt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Encrypt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Sign", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sign = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnfreezeAndConsumeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: UnfreezeAndConsumeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: UnfreezeAndConsumeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OutTradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OutTradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerId", wireType)
			}
			m.BuyerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BuyerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field BuyerName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BuyerName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommodityId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommodityId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CommodityName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommodityName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field UnitPrice", wireType)
			}
			m.UnitPrice = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UnitPrice |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ctime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ctime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DealToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DealToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BlackUserReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: BlackUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: BlackUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BlackUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: BlackUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: BlackUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsBlackUser", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBlackUser = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CV2Request) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TransferI2CV2Request: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TransferI2CV2Request: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &TransferI2CV2Request_Data{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SignedAt", wireType)
			}
			m.SignedAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SignedAt |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Sign", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sign = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferI2CV2Request_Data) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: Data: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: Data: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TradeNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TradeNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Scope", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Scope = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SubScope", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SubScope = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FromId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ToId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ToId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Notes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTbean
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Notes = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field At", wireType)
			}
			m.At = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.At |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTbean(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTbean
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipTbean(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTbean
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTbean
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthTbean
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowTbean
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipTbean(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthTbean = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTbean   = fmt1.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("tbean.proto", fileDescriptorTbean) }

var fileDescriptorTbean = []byte{
	// 1412 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0x4b, 0x6f, 0xe4, 0xc4,
	0x13, 0x5f, 0x3f, 0xe6, 0x55, 0x33, 0x93, 0x7f, 0xe2, 0x7f, 0x36, 0x6b, 0xa2, 0x25, 0x84, 0x66,
	0xb5, 0x5a, 0x04, 0x1a, 0x89, 0xd9, 0xbd, 0x20, 0x4e, 0x49, 0x78, 0x8d, 0xd8, 0xdd, 0xac, 0xbc,
	0x13, 0xee, 0x8e, 0xdd, 0xb3, 0x6b, 0x32, 0x7e, 0x60, 0xb7, 0x0f, 0xe1, 0xc0, 0x9d, 0xd3, 0x72,
	0x41, 0x48, 0x9c, 0xd1, 0x7e, 0x16, 0x8e, 0xc0, 0x07, 0x40, 0x28, 0x1c, 0xe0, 0x13, 0xc0, 0x0d,
	0xa1, 0xae, 0x6e, 0xdb, 0xed, 0xc4, 0x93, 0x09, 0x59, 0x56, 0x08, 0xc1, 0xcd, 0x55, 0x6e, 0x57,
	0x55, 0xff, 0xea, 0x57, 0xd5, 0x5d, 0x86, 0x3e, 0x3b, 0xa4, 0x6e, 0x34, 0x4a, 0xd2, 0x98, 0xc5,
	0x56, 0x0b, 0x05, 0xf2, 0x0e, 0xac, 0xed, 0x24, 0x81, 0x43, 0xb3, 0x24, 0x8e, 0x32, 0xfa, 0x3e,
	0x75, 0x7d, 0x9a, 0x5a, 0x1b, 0xd0, 0x4e, 0x69, 0x96, 0xcf, 0x99, 0xad, 0x6d, 0x6b, 0xb7, 0x7a,
	0x8e, 0x94, 0x2c, 0x1b, 0x3a, 0x21, 0xcd, 0x32, 0xf7, 0x11, 0xb5, 0x75, 0x7c, 0x51, 0x88, 0x64,
	0x1f, 0xd6, 0xde, 0xa3, 0x6c, 0xd7, 0x9d, 0xbb, 0x91, 0x47, 0x1d, 0xfa, 0x71, 0x4e, 0x33, 0x66,
	0xad, 0x43, 0xcb, 0x4d, 0x92, 0x89, 0x2f, 0xad, 0x08, 0xc1, 0xb2, 0xc0, 0x64, 0xc7, 0x49, 0x61,
	0x01, 0x9f, 0xad, 0x55, 0x30, 0xf2, 0xc0, 0xb7, 0x8d, 0x6d, 0xed, 0xd6, 0xd0, 0xe1, 0x8f, 0x64,
	0x0c, 0x1b, 0xaa, 0x41, 0x11, 0xde, 0x6e, 0xec, 0x1f, 0xf3, 0x20, 0x0e, 0x85, 0x1a, 0xed, 0xb6,
	0x9c, 0x42, 0x24, 0xbf, 0xea, 0x30, 0x98, 0xa6, 0xae, 0xbf, 0x24, 0x80, 0x2d, 0x80, 0x38, 0x67,
	0xb8, 0xf0, 0x7e, 0x2c, 0xc3, 0x50, 0x34, 0xe8, 0x20, 0x3f, 0xa6, 0xe9, 0xa4, 0x08, 0xa8, 0x10,
	0xad, 0xeb, 0xd0, 0xc3, 0xc7, 0xfb, 0x6e, 0x48, 0x6d, 0x13, 0x3f, 0xac, 0x14, 0xd6, 0x36, 0xf4,
	0xbd, 0x38, 0x0c, 0x63, 0x3f, 0x60, 0xc7, 0x13, 0xdf, 0x6e, 0xe1, 0x7b, 0x55, 0x65, 0xdd, 0x80,
	0x61, 0x29, 0xa2, 0x8d, 0x36, 0xae, 0xa9, 0x2b, 0x39, 0x18, 0x51, 0x1e, 0xda, 0x1d, 0x01, 0x46,
	0x94, 0x87, 0xdc, 0x6f, 0x1e, 0x05, 0xec, 0x41, 0x1a, 0x78, 0xd4, 0xee, 0xa2, 0xbe, 0x52, 0xf0,
	0x5d, 0x26, 0xf8, 0xa6, 0x87, 0x6f, 0x84, 0xc0, 0x61, 0x8e, 0x62, 0x46, 0x6d, 0x10, 0x30, 0xf3,
	0x67, 0x6b, 0x13, 0xba, 0xc9, 0xdc, 0x65, 0xb3, 0x38, 0x0d, 0xed, 0x3e, 0xea, 0x4b, 0xd9, 0x22,
	0x30, 0x88, 0x73, 0xb6, 0x9f, 0xfa, 0x34, 0x9d, 0x06, 0x21, 0xb5, 0x07, 0xf8, 0xbe, 0xa6, 0xe3,
	0xbc, 0x08, 0x18, 0x0d, 0x27, 0xbe, 0x3d, 0x14, 0xbc, 0x10, 0x12, 0xf9, 0x4d, 0x87, 0x35, 0x09,
	0xbc, 0x92, 0xa8, 0x7f, 0x33, 0xfa, 0x36, 0x74, 0x98, 0xdc, 0xa2, 0x48, 0x40, 0x21, 0xf2, 0xf5,
	0x1e, 0xe3, 0x00, 0x8b, 0x04, 0x08, 0x41, 0x25, 0xf5, 0xa0, 0x46, 0x6a, 0xee, 0xdd, 0xa7, 0xee,
	0x7c, 0x1a, 0x1f, 0xd1, 0x48, 0xc2, 0x5e, 0x29, 0xc8, 0x89, 0x06, 0x96, 0x44, 0x8e, 0x05, 0xb3,
	0xe3, 0x67, 0x26, 0x7e, 0x11, 0xb4, 0x51, 0x0f, 0xfa, 0x2d, 0xe8, 0x70, 0x94, 0xdc, 0xc8, 0x47,
	0xe0, 0x57, 0xc6, 0x2f, 0x8f, 0x44, 0x2f, 0x39, 0xeb, 0x7b, 0xb4, 0x27, 0x16, 0x3a, 0xc5, 0x17,
	0x35, 0xd6, 0xb5, 0xea, 0xac, 0x23, 0xaf, 0x40, 0x47, 0xae, 0xb7, 0x00, 0xda, 0x7b, 0xfb, 0xf7,
	0xee, 0x4d, 0xa6, 0xab, 0x57, 0xac, 0x01, 0x74, 0x9d, 0xfd, 0xbb, 0x77, 0x77, 0x77, 0xf6, 0x3e,
	0x58, 0xd5, 0xc8, 0x97, 0x1a, 0x5c, 0xab, 0x39, 0xfa, 0x2b, 0x48, 0xb6, 0x60, 0xa7, 0x1b, 0xd0,
	0xce, 0x98, 0xcb, 0xf2, 0x4c, 0x32, 0x4c, 0x4a, 0xdc, 0x4f, 0x88, 0x69, 0x13, 0x3b, 0x10, 0x02,
	0xf9, 0x45, 0xc0, 0x1f, 0x65, 0x33, 0x9a, 0x4e, 0xc6, 0x7b, 0xcf, 0x06, 0xbf, 0x05, 0xe6, 0x2c,
	0x8d, 0x43, 0x49, 0x7b, 0x7c, 0xb6, 0x5e, 0x07, 0x9d, 0xc5, 0xb6, 0xb9, 0x6d, 0xdc, 0xea, 0x8f,
	0xaf, 0x57, 0x98, 0x9f, 0x72, 0x38, 0x9a, 0xc6, 0x8e, 0xce, 0x90, 0x5b, 0xbc, 0xce, 0xb3, 0x22,
	0x48, 0x14, 0xb8, 0x36, 0xcb, 0x0f, 0x27, 0xbe, 0xe4, 0xbb, 0x10, 0x36, 0x47, 0xa0, 0x4f, 0xe3,
	0xa2, 0xf1, 0x6a, 0x65, 0xe3, 0xe5, 0x00, 0xb8, 0x61, 0x9c, 0x47, 0x0c, 0x23, 0x34, 0x1d, 0x29,
	0x91, 0xcf, 0x44, 0x12, 0x2a, 0xcf, 0x4a, 0x12, 0xce, 0x5a, 0x51, 0xf8, 0xac, 0xcb, 0x2a, 0x96,
	0x7c, 0xae, 0xa3, 0x60, 0x9c, 0x41, 0x81, 0xc0, 0xc0, 0x7b, 0x4c, 0xbd, 0xa3, 0x20, 0x7a, 0x84,
	0x7d, 0x48, 0xa4, 0xa1, 0xa6, 0x23, 0x3f, 0x68, 0x60, 0xd7, 0x62, 0x99, 0xe5, 0x91, 0xbf, 0xf4,
	0xd4, 0x41, 0x70, 0x75, 0x05, 0xdc, 0x31, 0x82, 0x6b, 0x20, 0xb8, 0xa4, 0x09, 0x5c, 0xc5, 0xac,
	0x84, 0x78, 0xd3, 0xff, 0x73, 0xb0, 0x2d, 0xdd, 0x6e, 0x99, 0x32, 0x53, 0x49, 0x19, 0x79, 0xa2,
	0xc1, 0x8b, 0x0d, 0x91, 0xfc, 0x8d, 0x90, 0xdf, 0x83, 0xab, 0x4a, 0x40, 0xd5, 0x41, 0x7f, 0x39,
	0xae, 0x93, 0x4f, 0x61, 0xa3, 0xc9, 0x5c, 0x96, 0x60, 0x7f, 0x4c, 0x0f, 0xca, 0xad, 0x09, 0x81,
	0xf7, 0x10, 0x2f, 0xdd, 0xa9, 0x00, 0x1e, 0x3a, 0xa5, 0xcc, 0x3b, 0xa4, 0x97, 0x4a, 0x13, 0xb2,
	0x78, 0x2a, 0x05, 0xb7, 0xe7, 0xa3, 0x3d, 0x53, 0xd8, 0x43, 0x81, 0xfc, 0xac, 0xc1, 0xff, 0x1f,
	0xa4, 0x34, 0xa3, 0xec, 0xdd, 0x94, 0xd2, 0x4f, 0xf8, 0x4e, 0xde, 0x76, 0x99, 0xbb, 0x60, 0x37,
	0xca, 0x99, 0xa4, 0xd7, 0xcf, 0xa4, 0x1b, 0x30, 0x9c, 0xa1, 0x81, 0xba, 0xff, 0xba, 0xf2, 0x14,
	0x1a, 0xe6, 0x99, 0x04, 0x6c, 0x43, 0x5f, 0x7c, 0x30, 0x0d, 0xd8, 0xbc, 0x68, 0x31, 0xaa, 0xea,
	0xcc, 0xe9, 0xdc, 0x6e, 0x38, 0x9d, 0xcb, 0x3a, 0xef, 0x28, 0x75, 0x4e, 0x6e, 0x00, 0xec, 0xcd,
	0x03, 0x1a, 0x31, 0xdc, 0xdf, 0x06, 0xb4, 0x3d, 0x77, 0x3e, 0xa7, 0x69, 0x71, 0xb3, 0x13, 0x12,
	0xf9, 0x4a, 0x83, 0xff, 0x9d, 0xc2, 0xc3, 0x1a, 0x81, 0xe9, 0xbb, 0xcc, 0xc5, 0x95, 0xfd, 0xf1,
	0xa6, 0x2c, 0x90, 0x06, 0xd4, 0x1c, 0x5c, 0x67, 0xbd, 0x0a, 0x6d, 0x0f, 0x3d, 0x21, 0x48, 0xfd,
	0xf1, 0x9a, 0xfc, 0xa2, 0x72, 0xef, 0xc8, 0x05, 0x1c, 0x50, 0x1a, 0x79, 0xe9, 0x71, 0xc2, 0x8a,
	0xfe, 0x2b, 0x45, 0x5e, 0xa7, 0x59, 0xf0, 0x28, 0x92, 0x20, 0xe1, 0x33, 0x79, 0x6a, 0xc0, 0x6a,
	0xdd, 0xad, 0xe0, 0xc9, 0x3f, 0x36, 0x53, 0x5b, 0x00, 0xb3, 0x20, 0x0a, 0xb2, 0xc7, 0xb8, 0x42,
	0xa4, 0x4b, 0xd1, 0x54, 0x87, 0x4d, 0x57, 0x39, 0x6c, 0xaa, 0x9b, 0x43, 0x4f, 0xbd, 0x39, 0x54,
	0x07, 0x16, 0xd4, 0x0e, 0xac, 0x9b, 0xb0, 0xe2, 0xc5, 0x51, 0x96, 0x87, 0xe5, 0x86, 0xfb, 0xb8,
	0xe1, 0x53, 0x5a, 0x8e, 0x4b, 0x8a, 0xed, 0x65, 0x57, 0xb9, 0x7f, 0x0c, 0x9d, 0xba, 0x52, 0xac,
	0x0a, 0xdd, 0x20, 0x2a, 0x56, 0x0d, 0x8b, 0x55, 0x8a, 0x92, 0xbc, 0x09, 0xbd, 0x87, 0xcc, 0x65,
	0x14, 0xa9, 0x66, 0x81, 0xe9, 0xc5, 0x3e, 0x95, 0x75, 0x8c, 0xcf, 0xe7, 0x0c, 0x10, 0x47, 0xb0,
	0x7e, 0x3a, 0xc5, 0x68, 0xe5, 0xb5, 0x1a, 0x09, 0xaf, 0x35, 0x92, 0x30, 0x4b, 0x24, 0x03, 0x6f,
	0x42, 0x8b, 0xef, 0x9e, 0x4a, 0x02, 0xae, 0xca, 0xd5, 0x65, 0x4c, 0x8e, 0x78, 0x4d, 0x3e, 0x02,
	0xfb, 0x20, 0x12, 0x5f, 0xef, 0xf0, 0xbe, 0x2a, 0xfb, 0xfc, 0xa5, 0x3a, 0xc0, 0x92, 0xe6, 0x4a,
	0xbe, 0xd6, 0x60, 0xbd, 0xc9, 0x99, 0x75, 0xbb, 0xb6, 0xb3, 0x97, 0x64, 0xac, 0x8b, 0xe2, 0x7a,
	0xde, 0x35, 0xf6, 0x85, 0x01, 0x57, 0x1b, 0x7c, 0xff, 0x57, 0x68, 0x65, 0xa1, 0xad, 0x80, 0x1e,
	0xf8, 0xb2, 0xc8, 0x74, 0x71, 0xe2, 0xcb, 0xc2, 0xeb, 0x2f, 0x29, 0xbc, 0xc1, 0xc5, 0x0a, 0x6f,
	0xd8, 0x50, 0x78, 0xe4, 0x89, 0x01, 0x2f, 0x1c, 0x44, 0xb3, 0x22, 0x2f, 0x7b, 0xc2, 0xc6, 0x65,
	0xc9, 0x5a, 0x1b, 0xa1, 0x8c, 0x25, 0x23, 0x94, 0x79, 0x81, 0x11, 0xaa, 0x75, 0xce, 0x08, 0xd5,
	0x5e, 0x30, 0x42, 0x75, 0x16, 0x8e, 0x50, 0x5d, 0x75, 0x84, 0x52, 0xc7, 0x86, 0xde, 0xa9, 0x61,
	0xb5, 0xce, 0x1e, 0x58, 0x7c, 0xab, 0xea, 0xab, 0x17, 0xe1, 0x8b, 0x8c, 0xb8, 0xdb, 0xd0, 0x17,
	0x09, 0x10, 0xb1, 0x8a, 0x9c, 0xa8, 0x2a, 0xf2, 0x54, 0xe3, 0x95, 0xd2, 0x90, 0x11, 0xeb, 0x4e,
	0xad, 0xa2, 0xb7, 0xcb, 0x8a, 0x5e, 0x90, 0xbd, 0xe7, 0x5d, 0xd2, 0xbf, 0xeb, 0xb0, 0xd1, 0xe4,
	0x7c, 0x61, 0x4d, 0x5f, 0x7e, 0x6a, 0x52, 0x18, 0x67, 0x9e, 0xc3, 0xb8, 0xd6, 0x12, 0xc6, 0xb5,
	0x2f, 0xc0, 0xb8, 0xce, 0x39, 0x8c, 0xeb, 0x2e, 0x60, 0x5c, 0x6f, 0x21, 0xe3, 0x00, 0x2f, 0xeb,
	0x92, 0x71, 0xcd, 0xa3, 0xb9, 0xca, 0x43, 0x51, 0xc9, 0x15, 0x0f, 0xcf, 0x1f, 0xce, 0xef, 0xc0,
	0x60, 0x77, 0xee, 0x7a, 0x47, 0x07, 0x19, 0x4d, 0x39, 0x3f, 0x94, 0x3b, 0x7b, 0x4f, 0xdc, 0xd9,
	0x1b, 0xfe, 0x85, 0x91, 0x37, 0x60, 0xa8, 0x7c, 0x95, 0x25, 0x1c, 0xa4, 0x20, 0x2b, 0x55, 0xf8,
	0x79, 0xd7, 0x51, 0x55, 0xe4, 0x3b, 0x1d, 0xd6, 0x95, 0xeb, 0xf4, 0x87, 0xe3, 0x62, 0x16, 0x6a,
	0x66, 0x64, 0xd3, 0xd2, 0x91, 0xc2, 0xc8, 0x4d, 0xe8, 0x72, 0x02, 0x51, 0x7f, 0x47, 0x70, 0xd2,
	0x70, 0x4a, 0xb9, 0x24, 0x9a, 0x51, 0x11, 0x6d, 0xf3, 0x7b, 0x0d, 0xcc, 0xb3, 0xed, 0xc8, 0x50,
	0xda, 0x11, 0xab, 0x71, 0x4a, 0xfd, 0x17, 0x92, 0x79, 0x71, 0x52, 0xb4, 0x22, 0x21, 0xa0, 0xfb,
	0xfc, 0xf0, 0x21, 0xbe, 0x10, 0x7c, 0x2e, 0x65, 0xde, 0x74, 0xf9, 0xe8, 0x56, 0xfe, 0xe0, 0x91,
	0x12, 0x02, 0x19, 0x97, 0x0c, 0xc2, 0x67, 0x65, 0x24, 0xeb, 0x60, 0x38, 0xc5, 0x48, 0x56, 0x36,
	0x87, 0xae, 0xda, 0x1c, 0x56, 0x40, 0x77, 0x19, 0x32, 0xc5, 0x70, 0x74, 0x97, 0xed, 0xae, 0x7e,
	0x73, 0xb2, 0xa5, 0x7d, 0x7b, 0xb2, 0xa5, 0xfd, 0x78, 0xb2, 0xa5, 0x7d, 0xfe, 0xd3, 0xd6, 0x95,
	0xc3, 0x36, 0xfe, 0x38, 0xbd, 0xfd, 0x47, 0x00, 0x00, 0x00, 0xff, 0xff, 0xa1, 0xd6, 0x17, 0xb9,
	0x47, 0x15, 0x00, 0x00,
}
