/*
 * @Descripttion:
 * @Date: 2020-05-26 14:00:57
 * @LastEditors: liang
 * @LastEditTime: 2021-06-16 13:43:34
 */
package config

import (
	"fmt"
	"time"

	goredis "github.com/go-redis/redis"
)

// RedisRingConfig ...
type RedisRingConfig struct {
	AddrMap      map[string]string `json:"addr_map" yaml:"addr_map"`
	PingInterval int               `json:"ping_interval" yaml:"ping_interval"`
	PoolSize     int               `json:"pool_size" yaml:"pool_size"`
	Password     string            `json:"password" yaml:"password"`
	DB           int               `json:"database" yaml:"database"`
}

func (cfg *RedisRingConfig) IdleCheckFrequency() time.Duration {
	return time.Duration(cfg.PingInterval) * time.Second
}

func (cfg *RedisRingConfig) Print() string {
	return fmt.Sprintf("host(%v) ping_interval(%v) pool_size(%v) password(%v) database(%v)",
		cfg.AddrMap, cfg.PingInterval, cfg.PoolSize, cfg.Password, cfg.DB)
}

// RedisConfig ...
type RedisConfig struct {
	Host         string `json:"host" yaml:"host"`
	Port         int    `json:"port" yaml:"port"`
	Protocol     string `json:"protocol" yaml:"protocol"`
	PingInterval int    `json:"ping_interval" yaml:"ping_interval"`
	PoolSize     int    `json:"pool_size" yaml:"pool_size"`
	Password     string `json:"password" yaml:"password"`
	DB           int    `json:"database" yaml:"database"`

	DialTimeout  int `json:"dial_timeout" yaml:"dial_timeout"`
	ReadTimeout  int `json:"read_timeout" yaml:"read_timeout"`
	WriteTimeout int `json:"write_timeout" yaml:"write_timeout"`

	RedisSDKConfig
}

type RedisSDKConfig struct {
	EnableCircuitBreaking bool `json:"enable_circuit_breaking" yaml:"enable_circuit_breaking"` // 开启熔断

	EnableApolloConfig bool   `json:"enable_apollo_config" yaml:"enable_apollo_config"` // 开启 Apollo 配置
	ApolloNamespace    string `json:"apollo_namespace" yaml:"apollo_namespace"`         // 关联 Redis Apollo 配置

	EnableDebugLog bool `json:"enable_debug_log" yaml:"enable_debug_log"` // debug 命令日志
	SlowlogTimeMs  int  `json:"slowlog_time_ms" yaml:"slowlog_time_ms"`   // 自定义慢查询日志阈值
}

// NewRedisConfig ...
func NewRedisConfig(conf Configer) *RedisConfig {
	return NewRedisConfigWithSection(conf, "Redis")
}

// NewRedisConfigWithSection ...
func NewRedisConfigWithSection(conf Configer, sectionName string) *RedisConfig {
	c := &RedisConfig{
		Protocol: "tcp",
	}

	c.Read(conf, sectionName)
	return c
}

func (cfg *RedisConfig) Read(conf Configer, sectionName string) {
	cfg.Host = conf.DefaultString(sectionName+"::host", "127.0.0.1")
	cfg.Port = conf.DefaultInt(sectionName+"::port", 6379)
	cfg.PingInterval = conf.DefaultInt(sectionName+"::ping_interval", 300)
	cfg.PoolSize = conf.DefaultInt(sectionName+"::pool_size", 25)
	cfg.Protocol = conf.DefaultString(sectionName+"::protocol", "tcp")
	cfg.Password = conf.DefaultString(sectionName+"::password", "")
	cfg.DB = conf.DefaultInt(sectionName+"::database", 0)
	cfg.DialTimeout = conf.DefaultInt(sectionName+"::dial_timeout", 10)
	cfg.ReadTimeout = conf.DefaultInt(sectionName+"::read_timeout", 10)
	cfg.WriteTimeout = conf.DefaultInt(sectionName+"::write_timeout", 10)

	cfg.RedisSDKConfig.Read(conf, sectionName)
}

func (cfg *RedisSDKConfig) Read(conf Configer, sectionName string) {
	cfg.EnableCircuitBreaking, _ = conf.Bool(sectionName + "::enable_circuit_breaking")
	cfg.EnableApolloConfig, _ = conf.Bool(sectionName + "::enable_apollo_config")
	cfg.ApolloNamespace = conf.String(sectionName + "::apollo_namespace")
	cfg.EnableDebugLog, _ = conf.Bool(sectionName + "::enable_debug_log")
	cfg.SlowlogTimeMs, _ = conf.Int(sectionName + "::slowlog_time_ms")
}

func (cfg *RedisConfig) Addr() string {
	return fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)
}

func (cfg *RedisConfig) IdleCheckFrequency() time.Duration {
	return time.Duration(cfg.PingInterval) * time.Second
}

func (cfg *RedisConfig) Print() string {
	return fmt.Sprintf("host(%v) port(%v) ping_interval(%v) pool_size(%v) protocol(%v) password(%v) database(%v)",
		cfg.Host, cfg.Port, cfg.PingInterval, cfg.PoolSize, cfg.Protocol, cfg.Password, cfg.DB)
}

func (cfg *RedisConfig) NewDefualtGoRedisClient() *goredis.Client {
	return goredis.NewClient(&goredis.Options{
		Network:            cfg.Protocol,
		Addr:               cfg.Addr(),
		PoolSize:           cfg.PoolSize,
		IdleCheckFrequency: cfg.IdleCheckFrequency(),
		DB:                 cfg.DB,
		DialTimeout:        time.Second * 10,
		ReadTimeout:        time.Second * 10,
		WriteTimeout:       time.Second * 10,
	})
}

func TestGoRedisClient() *goredis.Client {
	return goredis.NewClient(&goredis.Options{
		Network:  "tcp",
		Addr:     "*************:6379",
		PoolSize: 1,
	})
}
