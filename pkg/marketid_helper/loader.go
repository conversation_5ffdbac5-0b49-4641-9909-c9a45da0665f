package marketid_helper

import (
	"context"
	"encoding/json"
	"os/user"
	"path"
	"runtime"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/runtime/v2/filters/pkg"
)

const (
	defaultConfigPath = "/data/oss/conf-center/tt/marketid_helper.json"
)

var (
	loader *MarketIdHelperLoader
)

type Value struct {
	MarketId   uint32   `json:"market_id"`
	ClientType uint32   `json:"client_type"`
	AppId      uint32   `json:"app_id"`
	OsType     uint32   `json:"os_type"`
	Value      string   `json:"value"`
	Values     []string `json:"values"`
}

type sceneType struct {
	SceneName      string   `json:"scene_name"`
	Description    string   `json:"description"`
	Values         []*Value `json:"values"`
	marketId2value map[uint32][]*Value
	appId2value    map[uint32][]*Value
}

type ConfigType struct {
	Scenes   []*sceneType `json:"scenes"`
	sceneMap map[string]*sceneType
}

func (s *ConfigType) UnmarshalBinary(data []byte) error {

	err := json.Unmarshal(data, s)
	if err != nil {
		log.Errorf("json.Unmarshal failed, err:%v", err)
		return err
	}

	s.sceneMap = make(map[string]*sceneType)
	for _, scene := range s.Scenes {
		scene.marketId2value = make(map[uint32][]*Value)
		scene.appId2value = make(map[uint32][]*Value)
		for _, value := range scene.Values {
			values := scene.marketId2value[value.MarketId]
			values = append(values, value)
			scene.marketId2value[value.MarketId] = values
			scene.appId2value[value.AppId] = values
		}
		s.sceneMap[scene.SceneName] = scene
	}

	return nil
}

type MarketIdHelperLoader struct {
	configLoader *pkg.ConfigLoader
}

func NewMarketIdHelperLoader(filename string, interval time.Duration) (*MarketIdHelperLoader, error) {
	if filename == "" {
		filename = defaultConfigPath
	}

	configLoader, err := pkg.NewConfigLoaderV2(context.Background(), filename, &ConfigType{}, true, interval)
	if nil != err {
		log.Errorf("pkg.NewConfigLoaderV2 failed, conf:%s, err:%v", filename, err)
		return nil, err
	}

	return &MarketIdHelperLoader{
		configLoader: configLoader,
	}, nil
}

func (s *MarketIdHelperLoader) get() *ConfigType {
	var cfg *ConfigType
	if s.configLoader == nil {
		return cfg
	}

	if v := s.configLoader.Get(); v != nil {
		cfg, _ = v.(*ConfigType)
	}
	if cfg == nil {
		log.Warnf("configType not found")
	}
	return cfg
}

func (s *MarketIdHelperLoader) GetAll(sceneName string) map[uint32][]*Value {
	valueMap := make(map[uint32][]*Value)

	cfg := s.get()
	if cfg == nil {
		return valueMap
	}

	scene, ok := cfg.sceneMap[sceneName]
	if !ok {
		return valueMap
	}

	valueMap = scene.marketId2value
	return valueMap
}

func (s *MarketIdHelperLoader) GetAllByAppId(sceneName string) map[uint32][]*Value {
	valueMap := make(map[uint32][]*Value)

	cfg := s.get()
	if cfg == nil {
		return valueMap
	}

	scene, ok := cfg.sceneMap[sceneName]
	if !ok {
		return valueMap
	}

	valueMap = scene.appId2value
	return valueMap
}

func init() {
	var base string
	if runtime.GOOS == "windows" {
		if u, err := user.Current(); err == nil {
			base = u.HomeDir
		}
	}

	filename := path.Join(base, defaultConfigPath)

	loader, _ = NewMarketIdHelperLoader(filename, time.Second*60)
	if loader == nil {
		loader = &MarketIdHelperLoader{}
	}
}
