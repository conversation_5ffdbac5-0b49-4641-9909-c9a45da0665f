package ttversion

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/ttversion/android"
	"golang.52tt.com/pkg/ttversion/car"
	"golang.52tt.com/pkg/ttversion/iphone"
	"golang.52tt.com/pkg/ttversion/pc"
	"golang.52tt.com/pkg/ttversion/tx_mini"
	"strconv"
	"strings"
)

type Feature struct {
	desc string

	android android.Version
	iphone  iphone.Version
	car     car.Version
	pc      pc.Version
	txMini  tx_mini.Version
}

func (f *Feature) Android() android.Version {
	return f.android
}

func (f *Feature) Iphone() iphone.Version {
	return f.iphone
}

func (f *Feature) Car() car.Version {
	return f.car
}

func (f *Feature) Pc() pc.Version {
	return f.pc
}

func (f *Feature) TxMini() tx_mini.Version {
	return f.txMini
}

func (f *Feature) Desc() string {
	return f.desc
}

func (f *Feature) String() string {
	return fmt.Sprintf("android-%s,ios-%s,pc-%s,car-%s,mini-%s",
		protocol.ClientVersion(f.android),
		protocol.ClientVersion(f.iphone),
		protocol.ClientVersion(f.pc),
		protocol.ClientVersion(f.car),
		protocol.ClientVersion(f.txMini))
}

func New(desc string, versions ...interface{}) *Feature {
	f := &Feature{desc: desc}
	for _, version := range versions {
		switch version.(type) {
		case android.Version:
			f.android = version.(android.Version)
		case iphone.Version:
			f.iphone = version.(iphone.Version)
		case car.Version:
			f.car = version.(car.Version)
		case pc.Version:
			f.pc = version.(pc.Version)
		case tx_mini.Version:
			f.txMini = version.(tx_mini.Version)
		}
	}
	return f
}

func Parse(desc string, versions ...string) *Feature {
	f := &Feature{desc: desc}

	for _, version := range versions {
		var clientType string
		var clientVersion protocol.ClientVersion
		if idx := strings.Index(version, "-"); -1 == idx {
			continue
		} else {
			clientType = version[0:idx]

			// ios-3.0.0
			if 2 != strings.Count(version, ".") {
				continue
			}

			subStrings := strings.Split(version[idx+1:], ".")
			subVers := make([]uint16, 0)
			for _, subString := range subStrings {
				t, err := strconv.ParseUint(subString, 10, 32)
				if err != nil {
					log.Debugf("parse version fail, version: %s, err: %v", version, err)
					continue
				}
				subVers = append(subVers, uint16(t))
			}
			if 3 != len(subVers) {
				continue
			}

			clientVersion = protocol.ClientVersion(protocol.FormatClientVersion(uint8(subVers[0]), uint8(subVers[1]), subVers[2]))
		}

		switch strings.ToLower(clientType) {
		case "android":
			f.android = android.Version(clientVersion)
		case "ios":
			f.iphone = iphone.Version(clientVersion)
		case "pc":
			f.pc = pc.Version(clientVersion)
		}
	}
	return f
}

func (f *Feature) Atleast(clientType uint16, clientVersion uint32) bool {
	switch clientType {
	case protocol.ClientTypeANDROID:
		return uint32(f.android) <= clientVersion
	case protocol.ClientTypeIOS:
		return uint32(f.iphone) <= clientVersion
	case protocol.ClientTypeCAR:
		if 0 == f.car {
			return false
		}
		return uint32(f.car) <= clientVersion
	case protocol.ClientTypePcTT:
		if 0 == f.pc {
			return false
		}
		return uint32(f.pc) <= clientVersion
	case protocol.ClientTypeTX_MINI:
		if 0 == f.txMini {
			return false
		}
		return uint32(f.txMini) <= clientVersion
	default:
		return false
	}
}

func (f *Feature) NoZeroAtleast(clientType uint16, clientVersion uint32) bool {
	switch clientType {
	case protocol.ClientTypeANDROID:
		//如果已经是最大版本号，则任何版本都禁止
		if f.android == android.Version(android.None) {
			log.DebugWithCtx(context.Background(), "hit android max version")
			return false
		}
		return uint32(f.android) <= clientVersion
	case protocol.ClientTypeIOS:
		//如果已经是最大版本号，则任何版本都禁止
		if f.iphone == iphone.Version(iphone.None) {
			return false
		}
		return uint32(f.iphone) <= clientVersion
	case protocol.ClientTypeCAR:
		//如果已经是最大版本号，则任何版本都禁止
		if f.car == car.Version(car.None) {
			return false
		}
		return uint32(f.car) <= clientVersion
	case protocol.ClientTypePcTT:
		//如果已经是最大版本号，则任何版本都禁止
		if f.pc == pc.Version(pc.None) {
			return false
		}
		return uint32(f.pc) <= clientVersion
	case protocol.ClientTypeTX_MINI:
		//如果已经是最大版本号，则任何版本都禁止
		if f.txMini == tx_mini.Version(tx_mini.None) {
			return false
		}
		return uint32(f.txMini) <= clientVersion
	default:
		return false
	}
}
