package ttconfig

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"testing"
	"time"
)

var Dev = &Environment{
	Addr:   "**************:8910",
	Secret: "cea85ae2b410eab65dffa5f3665cc1ca4c5e3abb",
}

func TestReadFile(t *testing.T) {
	filename := "hzy/testapi.json"
	ctx := context.Background()

	fmt.Println("modify before")
	file, err := ReadFile(ctx, Dev, filename)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("Description >>", file.Description)
	fmt.Println("Content >>", file.Content)

	at := time.Now().Format("2006-01-02 15:04:05")
	fmt.Println("modify at:", at)
	err = WriteFile(ctx, Dev, &File{
		Name:        filename,
		Description: at,
		Content:     fmt.Sprintf("{\"at\":\"%s\"}", at),
	})
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("modify after")
	file, err = ReadFile(ctx, Dev, filename)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("Description >>", file.Description)
	fmt.Println("Content >>", file.Content)
}

func TestWriteFileWithType(t *testing.T) {

	var (
		testIniFile = &File{
			Name:        "hzy/testapi.conf",
			Description: "tt config center sdk test",
		}
		ctx = context.Background()
	)

	data, err := ioutil.ReadFile("account_cli.conf")
	if err != nil {
		panic(err)
	}

	testIniFile.Content = string(data)

	err = WriteFile(ctx, Dev, testIniFile)
	if err != nil {
		panic(err)
	}

	file, err := ReadFile(ctx, Dev, testIniFile.Name)
	assert.NotNil(t, file)
	assert.Nil(t, err)
	t.Logf("file:%+v", file)

}
