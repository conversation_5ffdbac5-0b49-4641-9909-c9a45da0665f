package rcmd_report

import (
	"context"
	"errors"
	"github.com/apache/thrift/lib/go/thrift"
	"gitlab.ttyuyin.com/golang/gudetama/oss/flume"
	"gitlab.ttyuyin.com/golang/gudetama/oss/log"
	"runtime/debug"
	"sync"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"

	pb "golang.52tt.com/protocol/services/rcmd/common"
)

type ossRowV2 struct {
	biz  string
	data []byte
}

type ossRowsV2 []*ossRowV2

var (
	evHeaderV2 = map[string]string{}
)

type reporterV2 struct {
	mu sync.Mutex

	started    bool
	ctx        context.Context
	cancelFunc context.CancelFunc
	stopCh     chan interface{}

	socket *thrift.TSocket
	cli    *flume.ThriftSourceProtocolClient

	rows chan *ossRowV2
	data map[string]ossRowsV2
	//mergeData map[string]ossMergeRows

	addr string
	tf   thrift.TTransportFactory
	pf   thrift.TProtocolFactory

	sendingEvents     []*flume.ThriftFlumeEvent
	sendingEventsSize uint64
}

func newReporterV2() *reporterV2 {
	return &reporterV2{
		tf:            thrift.NewTFramedTransportFactory(thrift.NewTTransportFactory()),
		pf:            thrift.NewTCompactProtocolFactory(),
		rows:          make(chan *ossRowV2, 128),
		data:          make(map[string]ossRowsV2),
		sendingEvents: make([]*flume.ThriftFlumeEvent, 0, 100),
	}
}

func (r *reporterV2) Start(ctx context.Context, addr string, reportInterval time.Duration) {
	r.mu.Lock()
	if r.started {
		// already started
		return
	}
	r.started = true
	r.addr = addr
	if r.rows == nil {
		r.rows = make(chan *ossRowV2, 128)
	}
	r.mu.Unlock()

	r.ctx, r.cancelFunc = context.WithCancel(ctx)
	r.stopCh = make(chan interface{})

	// go-routine, read data from queue
	go func() {
		for {
			for row := range r.rows {
				r.mu.Lock()
				r.data[row.biz] = append(r.data[row.biz], row)
				r.mu.Unlock()
			}
		}
	}()

	go r.reportThread(reportInterval)
}

func (r *reporterV2) Stop() {
	r.mu.Lock()
	r.started = false
	stop := r.stopCh
	close(r.rows)
	r.mu.Unlock()
	if stop != nil {
		r.cancelFunc()
		<-stop
	}
}

func (r *reporterV2) reportThread(reportInterval time.Duration) {
	r.resetFlumeConn()
	defer func() {
		r.mu.Lock()
		r.cli = nil
		if r.socket != nil {
			r.socket.Close()
			r.socket = nil
		}

		close(r.stopCh)
		r.stopCh = nil
		r.mu.Unlock()
	}()

	tic := time.NewTicker(reportInterval)
	for {
		select {
		case <-tic.C:
			r.Flush()
		case <-r.ctx.Done():
			r.Flush()
			return
		}
	}
}

func (r *reporterV2) getFlumeEvent(biz string, rows []*ossRowV2) *flume.ThriftFlumeEvent {
	statsEV := &pb.RcmdCommonDataBatchEvent{
		SrcIp:           ossLog.GetLocalIP(),
		ServerTimestamp: uint32(time.Now().Unix()),
		Version:         version,
		BizType:         biz,
		BatchNum:        0, // TODO
	}

	statsEV.Data = make([][]byte, len(rows), len(rows))
	for idx, row := range rows {
		statsEV.Data[idx] = row.data
	}

	flumeEV := flume.NewThriftFlumeEvent()
	flumeEV.Body, _ = proto.Marshal(statsEV)
	return flumeEV
}

func (r *reporterV2) Flush() {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("Recover from err: %v", err)
			log.Errorf("Stack: %s", string(debug.Stack()))
		}
	}()

	if r.cli == nil {
		err := r.resetFlumeConn()
		if err != nil {
			// Cannot flush, failed to reset flume conn
			log.Errorf("Cannot flush, failed to reset connection to FLUME: %v", err)
			return
		}
	}

	//log.Infof("flushing...")

	r.mu.Lock()
	data := r.data
	r.data = make(map[string]ossRowsV2)
	r.mu.Unlock()

	evList := make([]*flume.ThriftFlumeEvent, 0, len(data))
	var evListSize uint64
	for biz, rows := range data {
		for i := 0; i < len(rows); i += maxRowSize {
			var slice []*ossRowV2
			if len(rows) > i+maxRowSize {
				slice = rows[i : i+maxRowSize]
			} else {
				slice = rows[i:]
			}
			flumeEV := r.getFlumeEvent(biz, slice)
			evListSize += uint64(len(flumeEV.Body))
			evList = append(evList, flumeEV)
		}
	}

	r.sendingEvents = append(r.sendingEvents, evList...)
	if len(r.sendingEvents) == 0 {
		log.Errorf("no sending events!") // TODO: remove
		return
	}

	r.sendingEventsSize += evListSize
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	res, err := r.cli.AppendBatch(ctx, r.sendingEvents)
	if err == nil && res == flume.Status_OK {
		// done, clear the sending events
		//log.Infof("%d events, %d bytes have been appended to flume", len(r.sendingEvents), r.sendingEventsSize) // TODO: use debug
		r.sendingEvents = make([]*flume.ThriftFlumeEvent, 0, 100)
		r.sendingEventsSize = 0
	} else {
		const sendingEventSizeThreshold = 5 * 1024 * 1024
		if r.sendingEventsSize > sendingEventSizeThreshold {
			// too many events piled up, clear
			log.Errorf("Too many events was piped up (%d events, %d bytes), clear all, these data will be lost",
				len(r.sendingEvents), r.sendingEventsSize)
			r.sendingEvents = make([]*flume.ThriftFlumeEvent, 0, 100)
			r.sendingEventsSize = 0
		} else {
			// try send next time
			log.Errorf("try to append %d events, %d bytes failed: %v, try next time",
				len(r.sendingEvents), r.sendingEventsSize, err)
		}

		r.resetFlumeConn()
	}
}

func (r *reporterV2) resetFlumeConn() error {
	if r.addr == "" {
		log.Errorf("invalid addr!") // TODO: remove
		return errors.New("Invalid addr")
	}

	t, err := thrift.NewTSocket(r.addr)
	if err != nil {
		log.Errorf("socket error! err=%v", err) // TODO: remove
		return err
	}

	t.SetTimeout(time.Second * 3)
	if err := t.Open(); err != nil {
		log.Errorf("open error! err=%v", err)
		return err
	}

	if r.socket != nil {
		r.socket.Close()
	}

	r.socket = t
	transport, err := r.tf.GetTransport(t)
	if err != nil {
		log.Errorf("transport error! err=%v", err)
		return err
	}
	r.cli = flume.NewThriftSourceProtocolClientFactory(transport, r.pf)
	return nil
}

func (r *reporterV2) reportRow(row *ossRowV2) bool {
	select {
	case r.rows <- row:
	default:
		// not created or full
	}
	return true
}

func (r *reporterV2) Report(bizID string, data []byte) bool {
	return r.reportRow(&ossRowV2{biz: bizID, data: data})
}
