package types

type stringCmd interface {
	Val() string
	Result() (string, error)
	Bytes() ([]byte, error)
	Int() (int, error)
	Int64() (int64, error)
	Uint64() (uint64, error)
	Float32() (float32, error)
	Float64() (float64, error)
	String() string
}

type StringCmd struct {
	stringCmd
	*baseErrCmd
}

func NewStringCmd(base baseCmd, cmd stringCmd) *StringCmd {
	return &StringCmd{
		stringCmd:  cmd,
		baseErrCmd: newBaseErrCmd(base),
	}
}

func (c *StringCmd) Result() (string, error) {
	val, err := c.stringCmd.Result()
	return val, NewErr(err)
}
