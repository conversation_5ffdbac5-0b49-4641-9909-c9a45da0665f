package unnormal

import (
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"testing"

	statemachinePB "golang.52tt.com/protocol/services/statemachine"

	"github.com/stretchr/testify/require"

	"golang.52tt.com/pkg/statemachine/statemachine_builder_factory"
)

var (
	checkCondition = func(ctx interface{}) bool {
		return true
	}

	checkConditionFalse = func(ctx interface{}) bool {
		return false
	}

	doAction = func(from, to statemachinePB.State, event statemachinePB.Event, ctx interface{}) error {
		fmt.Printf("from: %v to: %v event: %v ctx: %v\n", from, to, event, ctx)
		return nil
	}
)

func TestConditionNotMeet(t *testing.T) {
	builder := statemachine_builder_factory.Create()
	builder.ExternalTransition().
		From(statemachinePB.State_TEST_STATE_1).
		To(statemachinePB.State_TEST_STATE_2).
		On(statemachinePB.Event_TEST_EVENT_1).
		When(checkConditionFalse).
		Perform(doAction)

	stateMachine, err := builder.Build("NotMeetConditionMachine")
	require.Nil(t, err)
	target, isTransited, err := stateMachine.FireEvent(statemachinePB.State_TEST_STATE_1, statemachinePB.Event_TEST_EVENT_1, 1)
	require.Nil(t, err)
	require.Equal(t, statemachinePB.State_TEST_STATE_1, target)
	require.False(t, isTransited)
}

func TestDuplicatedTransition(t *testing.T) {
	if runtime.GOOS == "js" {
		t.Skipf("js does not support exec")
	}

	if os.Getenv("TEST_PANIC_DUPLICATED_TRANSITION") != "" {
		builder := statemachine_builder_factory.Create()
		builder.ExternalTransition().
			From(statemachinePB.State_TEST_STATE_1).
			To(statemachinePB.State_TEST_STATE_2).
			On(statemachinePB.Event_TEST_EVENT_1).
			When(checkCondition).
			Perform(doAction)

		builder.ExternalTransition().
			From(statemachinePB.State_TEST_STATE_1).
			To(statemachinePB.State_TEST_STATE_2).
			On(statemachinePB.Event_TEST_EVENT_1).
			When(checkCondition).
			Perform(doAction)
	}

	t.Parallel()

	cmd := exec.Command(os.Args[0], "-test.run="+t.Name(), "-test.v")
	cmd.Env = append(os.Environ(), "TEST_PANIC_DUPLICATED_TRANSITION=1")
	out := new(bytes.Buffer)
	cmd.Stdout = out
	cmd.Stderr = out
	if err := cmd.Start(); err != nil {
		t.Fatal(err)
	}

	err := cmd.Wait()
	t.Logf("%s:\n%s", strings.Join(cmd.Args, " "), out)
	require.NotNil(t, err)
	require.Contains(t, out.String(), "already Exist, you can not add another one")
}

func TestDuplicateMachine(t *testing.T) {
	builder := statemachine_builder_factory.Create()
	builder.ExternalTransition().
		From(statemachinePB.State_TEST_STATE_1).
		To(statemachinePB.State_TEST_STATE_2).
		On(statemachinePB.Event_TEST_EVENT_1).
		When(checkCondition).
		Perform(doAction)

	_, err := builder.Build("DuplicatedMachine")
	require.Nil(t, err)
	_, err = builder.Build("DuplicatedMachine")
	require.NotNil(t, err)
}

func TestNilCondition(t *testing.T) {
	builder := statemachine_builder_factory.Create()
	builder.ExternalTransition().
		From(statemachinePB.State_TEST_STATE_1).
		To(statemachinePB.State_TEST_STATE_2).
		On(statemachinePB.Event_TEST_EVENT_1).
		When(nil).
		Perform(doAction)

	stateMachine, err := builder.Build("NilCondition")
	require.Nil(t, err)
	target, isTransited, err := stateMachine.FireEvent(statemachinePB.State_TEST_STATE_1, statemachinePB.Event_TEST_EVENT_1, 1)
	require.Nil(t, err)
	require.True(t, isTransited)
	require.Equal(t, statemachinePB.State_TEST_STATE_2, target)
}

func TestNilAction(t *testing.T) {
	builder := statemachine_builder_factory.Create()
	builder.ExternalTransition().
		From(statemachinePB.State_TEST_STATE_1).
		To(statemachinePB.State_TEST_STATE_2).
		On(statemachinePB.Event_TEST_EVENT_1).
		When(checkCondition).
		Perform(nil)

	stateMachine, err := builder.Build("NilAction")
	require.Nil(t, err)

	target, isTransited, err := stateMachine.FireEvent(statemachinePB.State_TEST_STATE_1, statemachinePB.Event_TEST_EVENT_1, 1)
	require.Nil(t, err)
	require.True(t, isTransited)
	require.Equal(t, statemachinePB.State_TEST_STATE_2, target)
}

func TestNotExistedState(t *testing.T) {
	builder := statemachine_builder_factory.Create()
	builder.ExternalTransition().
		From(statemachinePB.State_TEST_STATE_1).
		To(statemachinePB.State_TEST_STATE_2).
		On(statemachinePB.Event_TEST_EVENT_1).
		When(checkCondition).
		Perform(doAction)

	stateMachine, err := builder.Build("NotExistedState")
	require.Nil(t, err)
	_, _, err = stateMachine.FireEvent(statemachinePB.State_TEST_STATE_3, statemachinePB.Event_TEST_EVENT_1, 3)
	require.NotNil(t, err)
}
