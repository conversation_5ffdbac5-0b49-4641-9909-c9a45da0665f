syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

//namespace
package Recruit;

// 招募贴信息
message RecruitInfo 
{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required uint32 game_id = 3;
	required string game_server = 4; //入驻游戏服
	required string notice = 5;
	repeated string icon = 6;
	optional uint32 recruitid = 7;      
	optional uint32 ding = 8;        //顶贴数
	optional uint32 currecruit = 9;  //当前完成数
	optional uint32 maxrecruit = 10;  //最大招募数
	optional uint32 phone_model = 11; //机型
}

// 游戏
message GameRecruitInfo
{
	required uint32 game_id = 1;
	required uint32 recruit_num = 2; //招募数
}

// 创建招募贴
message CreateMemberRecuitReq
{
	required RecruitInfo recruit = 1;
}

message CreateMemberRecuitResp
{
	required uint32 recruitid = 1;
}

// 移除招募贴
message RemoveGuildMemRecruitReq
{
	required uint32 recruitid = 1;
}
// 按照工会Id移除招募贴
message RemoveGuildMemRecruitByGuildIdReq
{
	required uint32 guild_id = 1;
}

// 更新招募贴
message UpdateGuildMemRecruitReq
{
	required RecruitInfo recruit = 1;
}

// 根据一组招募ID批量获取招募信息
message GetGuildMemRecruitByIdsReq
{
	repeated uint32 recruitids = 1;
}

message GetGuildMemRecruitByIdsResp
{
	repeated RecruitInfo recruit_list = 1;
}

// 根据一组游戏ID批量获取招募信息列表
message GetGuildMemRecruitByGameIdsReq
{
	repeated uint32 gameids = 1;
}

message GetGuildMemRecruitByGameIdsResp
{
	repeated RecruitInfo recruit_list = 1;
}

// 点赞
message PraiseRecruitReq
{
	required uint32 recruitid = 1;
	required uint32 uid = 2;
}

// 根据一个游戏ID获取该游戏ID下的招募列表
message GetGameRecruitByGameIdReq
{
	required uint32 game_id = 1; // 游戏id
	required uint32 start_idx = 2;  //开始序号
	required uint32 count = 3;    //返回总数
}

message GetGameRecruitByGameIdResp
{
	required uint32 recruit_cnt = 1; //招募总数
	repeated RecruitInfo recruit_list = 2; 
}

// 运营后台编辑招募推荐游戏相关
message RecommendGameInfo
{
	required uint32 game_id = 1;
	required uint32 weight = 2;  //排序权重(小的排在前面)
}

// 获取运营推荐的的游戏列表
message GetRecommendGameListResp
{
	repeated GameRecruitInfo game_list = 1;
}

//设置推荐的游戏列表
message SetRecommendGameListReq
{
	repeated RecommendGameInfo game_info_list = 1;
}

//更新招募进度
message UpdateMemberRecruitProcessReq
{
	required uint32 uid = 1;
	required uint32 recruit_id = 2;
	required uint32 value_added = 3; //增加量
	optional uint32 guild_id = 4; //工会Id
}

message UpdateMemberRecruitProcessResp
{
	required uint32 recruit_id = 1;
	required uint32 guild_id = 2;
	required uint32 game_id = 3;
	required uint32 author_id = 4;
	required uint32 cur_recruit = 5;
	required uint32 max_recruit = 6;
	optional string create_ts = 7;
	optional bool is_update = 8;
}

// Recruit服务
service Recruit {
	option( tlvpickle.Magic ) = 15270;		// 服务监听端口号

    rpc CreateGuildMemRecruit( CreateMemberRecuitReq ) returns( CreateMemberRecuitResp ) {
        option( tlvpickle.CmdID ) = 1;              // 命令号
        option( tlvpickle.OptString ) = "g:u:i:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <game_id> -u <guild_id> -i <icon>";    // 测试工具的命令号帮助
    }

    rpc RemoveGuildMemRecruit( RemoveGuildMemRecruitReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 2;              // 命令号
        option( tlvpickle.OptString ) = "r:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-r <recruitId>";    // 测试工具的命令号帮助
    }

	rpc UpdateGuildMemRecruit( UpdateGuildMemRecruitReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 3;              // 命令号
        option( tlvpickle.OptString ) = "r:g:u:i:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-r <recruitId> -g <game_id> -u <guild_id> -i <icon>";    // 测试工具的命令号帮助
	}

	rpc GetGuildMemRecruitByIds( GetGuildMemRecruitByIdsReq ) returns ( GetGuildMemRecruitByIdsResp ) {
        option( tlvpickle.CmdID ) = 4;              // 命令号
        option( tlvpickle.OptString ) = "r:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-r <recruitId>";    // 测试工具的命令号帮助
	}

	rpc GetGuildMemRecruitByGameIds( GetGuildMemRecruitByGameIdsReq ) returns ( GetGuildMemRecruitByGameIdsResp ) {
        option( tlvpickle.CmdID ) = 5;              // 命令号
        option( tlvpickle.OptString ) = "g:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <gameId>";    // 测试工具的命令号帮助
	}

	rpc PraiseRecruit( PraiseRecruitReq ) returns (  tlvpickle.SKBuiltinEmpty_PB  ) {
        option( tlvpickle.CmdID ) = 6;              // 命令号
        option( tlvpickle.OptString ) = "u:r:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u uid -r <recruitId>";    // 测试工具的命令号帮助
	}

	rpc GetGameRecruitByGameId( GetGameRecruitByGameIdReq ) returns ( GetGameRecruitByGameIdResp  ) {
        option( tlvpickle.CmdID ) = 7;              // 命令号
        option( tlvpickle.OptString ) = "g:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <gameId>";    // 测试工具的命令号帮助
	}

	rpc GetRecommendGameList( tlvpickle.SKBuiltinEmpty_PB ) returns ( GetRecommendGameListResp ) {
        option( tlvpickle.CmdID ) = 8;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
	}

	rpc SetRecommendGameList( SetRecommendGameListReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 9;              // 命令号
        option( tlvpickle.OptString ) = "";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";    // 测试工具的命令号帮助
	}

	rpc UpdateMemberRecruitProcess( UpdateMemberRecruitProcessReq ) returns ( UpdateMemberRecruitProcessResp ) {
        option( tlvpickle.CmdID ) = 10;              // 命令号
        option( tlvpickle.OptString ) = "u:r:a:g:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -r <recruitId> -a <value_added> -g <guild_id>";    // 测试工具的命令号帮助
	}

    rpc RemoveGuildMemRecruitByGuildId( RemoveGuildMemRecruitByGuildIdReq ) returns( tlvpickle.SKBuiltinEmpty_PB ) {
        option( tlvpickle.CmdID ) = 11;              // 命令号
        option( tlvpickle.OptString ) = "g:";   // 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guildId>";    // 测试工具的命令号帮助
    }
}
