syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Timeline;

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词, repeated字段最后增加一个_list

message ExtContentCase {
	repeated uint32 case_uid_list = 1;	// 需要特殊显示的uid
	required string content = 2;		// 对这些特殊显示的文本
}

message ExtContentPart {
	repeated ExtContentCase case_list = 1;		// 各种case
	required string detault_content = 2;		// 默认显示的文本
}

// 群系统消息..
message ExtContent {
	enum OP_TYPE {
		MEM_REMOVE = 1;		// 退群
	}
	repeated ExtContentPart content_part_list = 1;
	optional uint32 op_type = 2;					// 暂时没卵用
	repeated uint32 target_uids = 3;				// 暂时没卵用
}

//------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
//------------------------------------------
message TimelineMsg {
	enum TYPE {
		IM_MSG = 1;
		GUILD_APPLY = 2;
		DEL_IM_MSG = 3;
		GUILD_QUIT = 4;
		IM_GUILD_GROUP_MEM_MODIFY = 5;
		GROUP_TL_INDEX = 6;
	}
	required uint32 type = 1;
	required uint32 seqid = 2;
	required bytes msg_bin = 3;
}

enum PUBLIC_IM_TYPE {
	COMMON_MSG = 1;	//推送公众号所有人
	PREORDER_MSG = 2;//推送预约游戏的人
}

// 废弃定义 后续不再使用
enum Platform {
	Android = 0; // 废弃定义 后续不再使用
	iOS = 1;     // 废弃定义 后续不再使用
	UNSPECIFIED = 65535; // 废弃定义 后续不再使用
}

// 1、IM聊天消息的结构体定义
message ImMsg {
	required uint32 from_id = 1;
	required uint32 to_id = 2;
	required string from_name = 3;
	required string to_name = 4;					// 如果是群消息，那么to_name是群帐号
	required string from_nick = 5;
	required string to_nick = 6;
	required string content = 7;
	required uint32 type = 8;
	required uint32 client_msg_time = 9;
	required uint32 status = 10;
	required uint32 server_msg_id = 11;
	required uint32 server_msg_time = 12;
	optional bytes thumb = 13;
	required bool has_attachment = 14;				// 是否有附件
	optional bytes attachment_property = 15;		// 附件属性
	optional string sender_login_key = 16;			// 发送者的登录key，用户发送者去重消息
	optional uint32 client_msg_id = 17;				// 客户端消息id，用于发送者去重消息
	optional ExtContent special_content = 18;		// 群系统消息
	optional uint32 img_format = 19;			// 图片格式，没有填这这个字段则是默认的jpg格式
	optional uint32 guild_id = 20;				// 公会相关消息（比如公会消息助手），会带有这个字段
	optional uint32 exceed_time = 21;			// 过期时间
	optional string old_ver_content = 22;			// 旧版本兼容显示的文本
	optional bytes ext = 23;				// 扩展用, 可嵌套结构体 -- v1.5
	//v1.8.1公会优化，公会消息带上等级和勋章列表
	optional uint32 level = 24;                         // TT个人等级
	repeated uint32 medal_list = 25;                 // 勋章列表
	optional uint32 origin = 26;					// 消息来源。由客户端上传
	optional uint32 sync_key = 27;					// push消息的时候用的.
	optional uint32 platform = 28;		//消息推送的平台 （废弃字段 改用 app_platform ）
	optional uint32 public_type = 29;	//见PUBLIC_IM_TYPE
	optional uint32 guild_mem_level = 30;            // 公会成员等级
	optional uint32 target_msg_id = 31;			//对方的svr_msg_id,消息撤销用 
	optional uint32 msg_source_type = 32;       //消息来源类型 参照 im.proto MsgSourceType
	optional uint32 msg_sensitive_type = 33;    //  消息敏感类型 参考 im.proto MsgSensitiveType
	optional uint32 label = 34;					// 消息的标签 参考 im.proto MsgLabel
	optional uint32 msg_redpoint_flag = 35;     // 消息是否支持红点的标记
	optional uint32 msg_exposure_flag = 36;     // 消息是否外露的标记

	optional string app_name = 39;				// 消息推送的应用包 可选字段有 “huanyou” “zaiya” “ttvoice” 空字段表明全部App
	optional string app_platform = 40;		    // 消息推送的平台类型（用于替代 platform 字段 ） 可选字段有 “android” “ios” 空字段表明全部平台
	optional uint32 super_player_level = 41;    // 用户的超级会员等级
	repeated uint32 msg_tag_list = 42;     // 各类自定义的消息标签列表 see MsgTag
}

// 各类自定义的消息标签
enum MsgTag {
	// 未定义的各类自定义的消息标签
	MSG_TAG_UNSPECIFIED = 0;
	// sendImBot 发出的消息
	MSG_TAG_SEND_IM_BOT = 1;
	// im 消息自见
	MSG_TAG_IM_NOT_SEEN = 2;
}

message BatchImMsg {
    repeated ImMsg im_msg_list = 1;
}

// 2、用户申请加入工会
message GuildApplyMsg {
	enum STATUS {
		UN_HANDLE = 0;		// 未处理
		DONE = 1;			// 已处理
	}
	required uint32 from_id = 1;
	required string from_name = 2;
	required string from_nick = 3;
	required uint32 guild_id = 4;
	required string verify_msg = 5;
	required uint32 status = 6;
	required uint32 apply_time = 7;
	required uint32 apply_id = 8;		// 根据from_name和apply_id去重
	optional string group_account = 9;		// 如果有这个字段，则表示是工会入群申请
}

// 3、删除IM消息
message DelImMsg {
	required string target_account = 1;
}

// 4、退出工会系统通知
message QuitGuildMsg {
	enum QUIT_TYPE {
		QUIT = 1;		// 主动退出
		KICKED = 2;		// 被踢出公会
	}
	required uint32 guild_id = 1;
	required string guild_account = 2;
	required uint32 quit_uid = 3;
	required string quit_user_account = 4;
	required uint32 op_uid = 5;
	required string op_account = 6;
	required uint32 quit_type = 7;
}

// 5、群成员资料有变化
message ImGuildGroupMemModifyMsg {
	required uint32 guild_id = 1;
	required uint32 group_id = 2;
	required string target_group = 3;
	required uint32 mem_uid = 4;
}

//------------------------------------------
// 读写协议
//------------------------------------------
message WriteTimelineMsgReq {
	required uint32 id = 1;
	required string suffix = 2;
	required TimelineMsg msg = 3;
}

message WriteTimelineMsgResp {
}

message BatchWriteTimelineMsgReq {
	required uint32 id = 1;
	required string suffix = 2;
	repeated TimelineMsg msg_list = 3;
}

message BatchWriteTimelineMsgResp {
}

message PullTimelineMsgReq {
	required uint32 id = 1;
	required string suffix = 2;
	required uint32 start_seqid = 3;
	required uint32 limit = 4;
	optional bool skip_cache = 5;
}

message PullTimelineMsgResp {
	repeated TimelineMsg msg_list = 1;
}

//////////////////
message ImMsgWriteReq {
	required uint32 id = 1;
	required ImMsg msg = 2;
	optional string suffix = 3;
}

message ImMsgWriteRsp {
	required int32 ret = 1;
}

//////////////////
message PullImMsgReq {
	required uint32 id = 1;
	required uint32 start_seqid = 2;
	optional uint32 limit = 3;
	optional string suffix = 4;
}

message PullImMsgRsp {
	required uint32 ret = 1;
	repeated ImMsg msgs = 2;
}

//////////////////
// 已读消息状态
message ReadStatus {
	required string target_account = 1;
	required uint32 svr_msg_id = 2;
}

//////////////////
// 批量设置已读状态
message BatchSetReadStatusReq {
	repeated ReadStatus status = 1;
    optional bool force_replace = 2;    // 如果为true, 则会强制覆盖, 而不与当前值比较取大值
}

message BatchSetReadStatusResp {
	required uint32 ret = 1;
}

//////////////////
// 批量读取已读状态
message BatchGetReadStatusReq {
	repeated string target_account = 1;
}

message BatchGetReadStatusResp {
	required uint32 ret = 1;
	repeated ReadStatus status = 2;
}

message ReadStatusList {
	repeated ReadStatus status_list = 1;
}

message CheckMsgKeyExistReq {
	required string key = 1;
}

message CheckMsgKeyExistResp {
	optional bytes value = 1;
}

message SetMsgKeyReq {
	required string key = 1;
	required bytes value = 2;
	required uint32 ttl = 3;
}

message SetMsgKeyResp {
}

message TimelineKey {
	required uint32 id = 1;
	required string suffix = 2;
}

message TimelineAsyncJobReq {
	repeated TimelineKey key_list = 1;
}

message GroupTimelineIndex {
	required uint32 group_id = 1;
	required uint32 seq_id = 2;
}

message ClearReadStatusReq {
	required uint32 uid = 1;
	required string target_account = 2;
}

message ClearReadStatusResp {
}

//////////////////
// 设置对端已读消息ID
message SetPeerReadStatusReq {
	required uint32 peer_uid = 1;
	required string self_account = 2;
	required uint32 msg_id = 3;	// peer已读的msg_id(self的id空间)
}

message SetPeerReadStatusResp {
	required uint32 peer_seq_id = 1;
}

message BatchGetPeerReadStatusReq {
	required uint32 self_uid = 1;
	repeated string peer_account_list = 2;
	optional uint32 self_seq_id = 3;	// 只获取比seq_id大的
}

message BatchGetPeerReadStatusResp {
	repeated string peer_account_list = 1;
	repeated PeerReadStatus status_list = 2;	// 与peer_account_list一一对应
}

message PeerReadStatus {
	required uint32 msg_id = 1;
	required uint32 seq_id = 2;
}

message ClearGroupTimelineMsgReq{
	required uint32 id = 1;
}


message ClearGroupTimelineMsgResp {
}

//////////////////
service TimeLineSvr {
    option( tlvpickle.Magic ) = 13000;		// 服务监听端口号

    rpc ImMsgWrite(ImMsgWriteReq) returns(ImMsgWriteRsp) {
        option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "u:m:s:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-u <uid> -m <msg> -s <seq_id>";			// 测试工具的命令号帮助
    }

    rpc PullImMsg(PullImMsgReq) returns(PullImMsgRsp) {
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:s:l:";
        option( tlvpickle.Usage ) = "-u <uid> -s <start_seqid> -l <limit>";
    }

    rpc BatchSetReadStatus(BatchSetReadStatusReq) returns(BatchSetReadStatusResp) {
        option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "u:t:i:r:";
        option( tlvpickle.Usage ) = "-u <uid> -t <to_account> -i <id> -r <replace>";
    }

    rpc BatchGetReadStatus(BatchGetReadStatusReq) returns(BatchGetReadStatusResp) {
        option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "u:t:";
        option( tlvpickle.Usage ) = "-u <uid> -t <to_account>";
    }

    rpc CheckMsgKeyExist(CheckMsgKeyExistReq) returns (CheckMsgKeyExistResp) {
        option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "k:";
        option( tlvpickle.Usage ) = "-k <key>";
    }

    rpc SetMsgKey(SetMsgKeyReq) returns (SetMsgKeyResp) {
        option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "k:t:";
        option( tlvpickle.Usage ) = "-k <key> -t <ttl>";
    }

    rpc WriteTimelineMsg(WriteTimelineMsgReq) returns (WriteTimelineMsgResp) {
        option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "i:s:";
        option( tlvpickle.Usage ) = "-i <id> -s <suffix>";
    }

    rpc PullTimelineMsg(PullTimelineMsgReq) returns (PullTimelineMsgResp) {
        option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "i:x:s:l:";
        option( tlvpickle.Usage ) = "-i <id> -x <suffix> -s <seq> -l <limit>";
    }

    rpc BatchWriteTimelineMsg(BatchWriteTimelineMsgReq) returns (BatchWriteTimelineMsgResp) {
        option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = " -- ";
        option( tlvpickle.Usage ) = " -- ";
    }

	rpc ClearReadStatus(ClearReadStatusReq) returns (ClearReadStatusResp) {
		option( tlvpickle.CmdID ) = 10;
		option( tlvpickle.OptString ) = "u:t:";
		option( tlvpickle.Usage ) = "-u <uid> -t <to_account>";
	}

	rpc SetPeerReadStatus(SetPeerReadStatusReq) returns (SetPeerReadStatusResp) {
		option( tlvpickle.CmdID ) = 11;
		option( tlvpickle.OptString ) = "u:t:";
		option( tlvpickle.Usage ) = "-u <peer_uid> -t <self_account>";
	}

	rpc BatchGetPeerReadStatus(BatchGetPeerReadStatusReq) returns (BatchGetPeerReadStatusResp) {
		option( tlvpickle.CmdID ) = 12;
		option( tlvpickle.OptString ) = "u:t:s:";
		option( tlvpickle.Usage ) = "-u <self_uid> -t <peer_account> -s <seq>";
	}

	rpc ClearGroupTimelineMsg(ClearGroupTimelineMsgReq) returns (ClearGroupTimelineMsgResp) {
		option( tlvpickle.CmdID ) = 13;
		option( tlvpickle.OptString ) = "u:";
		option( tlvpickle.Usage ) = "-u <uid>";
	}
}
