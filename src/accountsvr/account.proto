syntax = "proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package Account;

enum USER_SEX {
  USER_SEX_FEMALE = 0;
  USER_SEX_MALE = 1;
}

enum USER_STATUS {
  USER_STATUS_ENABLED = 0x0000;             // 普通用户
  USER_STATUS_DISABLED = 0x0001;            // bit0, 已废弃
  USER_STATUS_UNREGISTER = 0x0002;          // bit1, 已注销
  USER_STATUS_CLEAR_LOCAL_ACCOUNT = 0x0004; // bit2, 退出后清除账号和密码信息
  USER_STATUS_IS_NOBLE  = 0x0008; // bit3, 是否是贵族
}

enum USER_SOURCE {
  USER_SOURCE_TT = 0;        // 来自TT
  USER_SOURCE_SDK = 1;       // 来自游戏SDK
  USER_SOURCE_INTERNAL = 2;  // 内部帐号
  USER_SOURCE_R = 3;         // R Project
  USER_SOURCE_ROBOT = 4;     // 机器人
  USER_SOURCE_TT_SDK = 5;   //
}

enum USER_TYPE {
  USER_TYPE_COMMON = 0;    // 普通用户
  USER_TYPE_OPERATOR = 1;  // 运营
  USER_TYPE_WATCHER = 2;   // 观察者
  USER_TYPE_KEFU = 3;      // 客服
  USER_TYPE_ROBOT = 4;     // 机器人
  USER_TYPE_VIP_KEFU = 5;  // VIP客服
}

enum THIRD_PARTY_TYPE {
    THIRD_PARTY_TYPE_NIL = 0;           //
    THIRD_PARTY_TYPE_QQ = 1;            // QQ OpenID
    THIRD_PARTY_TYPE_WECHAT = 2;        // 微信OpenID
    THIRD_PARTY_TYPE_WECHAT_UNION = 3;  // 微信UnionID
    THIRD_PARTY_TYPE_QQ_UNION = 4;      // QQ UnionID
    THIRD_PARTY_TYPE_APPLE = 5;     //Apple Id

    THIRD_PARTY_TYPE_UNKNOWN = 255;
}

message Cookie {
  optional string authority = 1;
  optional string trace_id = 2;
  optional string span_id = 3;
}

// message定义使用大小写驼峰命名规则， 字段名全小写使用_分割单词,
// repeated字段最后增加一个_list
message CreateUserReq {
  required string phone = 1;
  required string password = 2;
  optional string username = 3;
  optional string nickname = 4;
  optional int32 sex = 5;
  optional uint32 invite_uid = 6;
  optional string deviceid = 7;
  optional uint32 source = 8;          //	来源 USER_SOURCE
  optional string version = 9;         //客户端版本, 未使用
  optional bool isnumberic = 10;       //数字帐号
  optional bool password_is_sys = 11;  // 密码是否是机器生成的 默认为0
  optional string scene = 12;  // 默认为tt
  optional uint32 uid = 13;    // 使用指定uid创建用户
}

message UidResp {
  required uint32 uid = 1;
  optional string username = 2;
}

// 根据phone获取用户id
message GetUidByPhoneReq {
  required string phone = 1;
  optional string scene = 2; // 默认是tt
}

// 根据username(包含alias)获取uid
message GetUidByNameReq {
  enum NameType {
    TYPE_AUTO = 0;
    TYPE_ALIAS = 1;
    TYPE_USERNAME = 2;
  }
  required string username = 1;
  optional uint32 name_type = 2;
}

//根据uid获取用户信息
message UidReq {
  required uint32 uid = 1;
}

message ExistResp {
  required bool exist = 1;
}

//批量uid
message UidsReq {
  repeated uint32 uid_list = 1;
  // 查完mysql不写入redis
  optional bool from_slave = 2;
}

//返回用户完整信息
message UserResp {
  required uint32 uid = 1;
  required string phone = 2;  //如果库中手机号是xxx_disable, 则该字段返回空
  required string username = 3;
  required string alias = 4;
  required string nickname = 5;
  required int32 sex = 6;
  required string signature = 7;

  optional string password = 9;
  required bool verify = 10;
  required uint32 current_guild_id = 11;
  required uint32 last_quit_guild_type = 12;
  required uint32 registered_at = 13;
  required bytes kick_client_device_id = 14;
  optional uint32 question = 15;
  optional uint32 last_quit_guild_id = 16;
  optional uint32 last_quit_guild_time = 17;
  optional uint32 who_invite_uid = 18;
  optional string invite_code = 19;
  optional uint32 status = 20;  //已废弃
  optional uint32 user_type = 21;
  optional string fromid = 22;
  optional uint32 source = 23;  // USER_SOURCE
  optional uint32 last_login_at = 24;
  optional bool password_set = 25;
  optional uint32 prefix_valid = 26;       //是否使用公会马甲
  optional string original_nickname = 27;  //无马甲昵称
  optional string guild_prefix = 28;       // 公会马甲
  optional bool is_unregister = 29;        //是否已注销
  optional bool is_clear_local_account = 30;    // 退出后清除账号和密码信息
  optional bool is_noble = 31; //是否贵族
}

//修改密码
message UpdatePasswordReq {
  required uint32 uid = 1;
  required string password = 2;
}

//更新别名（只能修改一次）
message UpdateAliasReq {
  required uint32 uid = 1;
  required string alias = 2;
  optional bool force_update = 3;
}

//更新用户资料
message UpdateUserInfoReq {
  required uint32 uid = 1;
  required string nickname = 2;
  required int32 sex = 3;
}

//更新昵称
message UpdateUserNicknameReq {
  required uint32 uid = 1;
  required string nickname = 2;
}

//更新签名
message UpdateUserSignatureReq {
  required uint32 uid = 1;
  required string signature = 2;
}

//更新用户状态（启用，禁用)
message UpdateStatusReq {
  required uint32 uid = 1;
  required int32 status = 2;
  required string operator_name = 3;
  required string reason = 4;
}

message UpdateSexReq {
  required uint32 uid = 1;
  required uint32 sex = 2;
}

message UpdateTypeReq {
  required uint32 uid = 1;
  required uint32 type = 2;
}

message UpdateVerifyReq {
  required uint32 uid = 1;
  required bool verify = 2;
}

message CheckVerifyReq {
  required uint32 uid = 1;
}

message CheckVerifyResp {
  required bool verify = 1;
}

message CheckUserHaveGuildReq {
  required uint32 uid = 1;
}

message CheckUserHaveGuildResp {
  required bool have = 1;
  required uint32 guild_id = 2;
}

message UpdateUserCurrentGuildReq {
  required uint32 uid = 1;
  required uint32 guild_id = 2;
}

message UpdateLastLoginAtReq {
  required uint32 uid = 1;
  required uint64 lastLoginAt = 2;
}

//message InstalledGameInfo {
//  required uint32 game_id = 1;
//  required string package = 2;
//  required string name = 3;
//  required string version = 4;
//}

//message AddInstalledGameReq {
//  required uint32 uid = 1;
//  required uint32 game_id = 2;
//  required string package = 3;
//  required string name = 4;
//  required string version = 5;
//}

// 添加用户上报的游戏安装信息 (全量覆盖)
//message BatchAddInstalledGameReq {
//  required uint32 uid = 1;
//  repeated InstalledGameInfo game_list = 2;
//}
//message BatchAddInstalledGameResp {
//  required uint32 last_game_size = 1;  // 被全量覆盖前 该用户安装游戏的数量
//}

// 清除用户上报的游戏安装信息
//message ClearInstalledGameReq {
//  required uint32 uid = 1;
//}
//message ClearInstalledGameResp {
//  required uint32 last_game_size = 1;  // 被清除前 该用户安装游戏的数量
//}

//message RemoveInstalledGameReq {
//  required uint32 uid = 1;
//  required uint32 game_id = 2;
//}

//message GetInstalledGameListReq {
//  required uint32 uid = 1;
//}

//message StUserInstalledGame {
//  required uint32 game_id = 1;
//  required uint32 last_report_at = 2;
//}

//message GetInstalledGameListResp {
//  repeated StUserInstalledGame games = 1;
//}

//message GetGameInstalledUserListReq {
//  required uint32 game_id = 1;
//}

//message GetGameInstalledUserListResp {
//  repeated uint32 uids = 1;
//}

message UpdateUserTickDeviceIdReq {
  required bytes device_id = 1;
  optional uint32 uid = 2;
}

message UpdateUserTickDeviceIdResp {}

//message UpdateUserPasswordByMibaoReq {
//  required string new_password = 1;
//  required uint32 question = 2;
//  required string answer = 3;
//}

//message UpdateUserPasswordByMibaoResp {}

//message InitMibaoReq {
//  required uint32 question = 1;
//  required string answer = 2;
//}

//message InitMibaoResp {}

message UsersResp {
  repeated UserResp user_list = 1;
}

message LogUserBehaviorReq {
  required uint32 type = 1;
  required string detail = 2;
}

message LogUserBehaviorResp {}

message GetTodayUserBehaviorReq {
  required uint32 type = 1;
}

message GetTodayUserBehaviorResp {
  repeated string detail = 1;
}

//message UpdateMibaoReq {
//  required uint32 question = 1;
//  required string answer = 2;
//}

//message UpdateMibaoResp {}

message UpdateUserInviteCodeReq {
  required string invite_code = 1;
}

message UpdateUserInviteCodeResp {}

message WriteRegInviteHistoryReq {
  required uint32 inviter = 1;
  required uint32 inviter_guild_id = 2;
  required uint32 reg_at = 3;
  required uint32 pkg_type = 4;
  required bytes my_device_id = 5;
  optional bool invalid = 6;
}

message WriteRegInviteHistoryResp {}

message GetRegInviteHistoryReq {
  required bool is_invitee =
      1;  // true表示查询的uid为被邀请人uid; 否则为邀请人的uid
}

message StRegisterInviteHistory {
  required uint32 uid = 1;
  required uint32 inviter = 2;
  required uint32 inviter_guild_id = 3;
  required uint32 reg_at = 4;
  required uint32 pkg_type = 5;
  required bytes device_id = 6;
  required bool invalid = 7;
}

message GetRegInviteHistoryResp {
  repeated StRegisterInviteHistory invite_history_list = 1;
}

// 查用户邀请用户入会的人数
message GetUserRegInviteCountReq {
  optional uint32 ts_begin = 1;
  optional uint32 ts_end = 2;
  optional uint32 uid = 3;
}

message GetUserRegInviteCountResp {
  required uint32 count = 1;
}

// 更新用户状态
message UpdateUserStatusReq {
  enum OP_STATUS {
    SET = 1;    //置标志
    UNSET = 2;  //移除状态
  }
  required uint32 uid = 1;
  required uint32 status_bit_flag = 2;
  required uint32 op = 3;
}

message UpdateUserStatusResp {}

// 通过第三方账号openid获取uid
message GetUidByOpenidReq {
  required uint32 third_party_type = 1;  // enum THIRD_PARTY_TYPE 第三方账号类型
  required string openid = 2;
  optional string scene = 3;
}

message GetUidByOpenidResp {
  required uint32 uid = 1;
}

// 通过第三方账号注册用户
message CreateUserForOpenidReq {
  required uint32 third_party_type = 1;  // enum THIRD_PARTY_TYPE 第三方账号类型
  required string openid = 2;
  required string password = 3;
  required string nickname = 4;
  required int32 sex = 5;
  required uint32 invite_uid = 6;
  required string deviceid = 7; //未用?
  required uint32 source = 8;   //	来源
  required string version = 9;  //客户端版本, 未使用
  optional string phone = 10;
  optional string scene = 11;  // app帐号的位面场景, 默认 tt
}

message CreateUserForOpenidResp {
  required uint32 uid = 1;
  required string username = 2;
}

message BindPhoneReq {
  required string phone = 1;
  required bool isunbindold = 2;
  optional string scene = 3;
}

message BindPhoneResp {}

message AddUidNumbericReq {}

message AddUidNumbericResp {}

message BatchQueryUidListReq {
  enum QueryType {
    QUERY_BY_ACCOUNT = 1;
    QUERY_BY_ALIAS = 2;
    QUERY_BY_PHONE = 3;
  }
  repeated string key_list = 1;
  required uint32 query_type = 2;
}

message KeyToUidPair {
  required string key = 1;
  required uint32 uid = 2;
}

message BatchQueryUidListResp {
  repeated KeyToUidPair result_list = 1;
}

message AutoBatchCreateUserReq {
  required uint32 count = 1;
  required uint32 source = 2;             //
  repeated string password_md5_list = 3;  // size of password_md5_list >= count
}

message AutoBatchCreateUserResp {
  repeated UserResp user_list =
      1;  // 创建成功的用户列表, 密码严格按照password_md5_list的顺序
}

message GetLastLoginTimeAndNicknameResp {
  required string logintime = 1;
  required string nickname = 2;
}

message GetUsersByAccsReq {
  repeated string acc_list = 1;
}

message AccInfo {
  required uint32 uid = 1;
  required string username = 2;
  required string alias = 3;
  required string nickname = 4;
  required int32 sex = 5;
  required int32 current_guild_id = 6;
  optional uint32 user_type = 7;
}

message GetUsersByAccsResp {
  repeated AccInfo accinfo_list = 1;
}

message BannedRecord {
  required uint32 uid = 1;
  required uint32 time = 2;
  required string operator_name = 3;
  required string reason = 4;
}

message GetBannedRecordResp {
  repeated BannedRecord banned_record_list = 1;
}

enum PhoneBindType {
  UNKNOWN_PHONE_BIND_TYPE = 0;
  LOGIN_PHONE = 1;   // 登录手机
  SECURE_PHONE = 2;  // 安全手机
}

message BindPhoneWithUidReq {
  required uint32 uid = 1;
  required string phone = 2;
  optional string update_password = 3;  // deprecated, server will ignore this field
  optional PhoneBindType bind_type = 4;
  optional string scene = 5;
}

message UnbindPhoneWithUidReq {
  required uint32 uid = 1;
  required string phone = 2;
  optional string scene = 3;
}

message RebindPhoneWithUidReq {
  required uint32 uid = 1;
  required string phone = 2;
  required bool is_bind = 3;  // 0.解绑 1.绑定
  optional PhoneBindType bind_type = 4;  // is_bind=true时有效
  optional string scene = 5;
}

message GetBannedReasonReq {
  required uint32 uid = 1;
}

message GetBannedReasonResp {
  required string reason = 1;
}

message GetWechatUnionIdReq {
  required uint32 uid = 1;
}

message GetWechatUnionIdResp {
  required string union_id = 1;
}

message SetWechatUnionIdReq {
  required uint32 uid = 1;
  required string union_id = 2;
}

message GetUidByWechatUnionIdReq {
  required string union_id = 1;
}

message GetUidByWechatUnionIdResp {
  required uint32 uid = 1;
}

//用户激活信息
message UserActivateInfo {
  required uint32 uid = 1;
  required uint64 activate_time = 2;  //激活(首次登陆)时间
  required string channel = 3;        //渠道
  optional string source = 4;         //来源，优先于渠道
}

message InsertUserActivateInfoReq {
  required uint32 uid = 1;
  required uint64 activate_time = 2;
  required string channel = 3;  //渠道
  optional string source = 4;   //来源，优先于渠道
}

message InsertUserActivateInfoRsp {}

message GetUserActivateInfoReq {
  required uint32 uid = 1;
}

message GetUserActivateInfoRsp {
  optional UserActivateInfo info = 1;
}

message BatchGetUserActivateInfoReq {
  repeated uint32 uid_list = 1;
}

message BatchGetUserActivateInfoRsp {
  repeated UserActivateInfo info_list = 1;
}

message DetachThirdPartyReq {
  required uint32 uid = 1;
  optional string scene = 2;
}

message DetachThirdPartyRsp {
  repeated string thirdparty_id_list = 1;
}

message UpdateUserPrefixValidReq {
  required uint32 uid = 1;
  required uint32 prefix_valid = 2;
}

//用户相册
message UpdatePhotoAlbumReq {
  required uint32 uid = 1;
  required string img_keys = 2;
}

message GetPhotoAlbumReq {
  required uint32 uid = 2;
}

message GetPhotoAlbumResp {
  required string img_keys = 1;
}

message UpdateUserQuitGuildInfoReq {
  required uint32 uid = 1;
  required uint32 last_quit_guild_id = 2;
  required uint32 last_quit_guild_type = 3;
  required string last_quit_guild_time = 4;
  required bool reset_current_guildid = 5;
}

message UpdateUserQuitGuildInfoResp {}

message BatchUpdateUserQuitGuildInfoReq {
  repeated uint32 uids = 1;
  required uint32 last_quit_guild_id = 2;
  required uint32 last_quit_guild_type = 3;
  required string last_quit_guild_time = 4;
  required bool reset_current_guildid = 5;
}

message BatchUpdateUserQuitGuildInfoResp {}

message UpdateUserGuildPrefixByUidReq {
  required uint32 uid = 1;
  required string guild_prefix = 2;
}

message UpdateUserGuildPrefixByUidResp {}

message UpdateUserGuildPrefixByGuildIdReq {
  required uint32 guild_id = 1;
  required string guild_prefix = 2;
}

message UpdateUserGuildPrefixByGuildIdResp {}

message RecycleUsernameReq {
  required string username = 1;
}

message RecycleUsernameResp {
  required bool account_recycled = 1;
  required bool alias_recycled = 2;
  required string new_username = 3;  // 分配的新账号, 可能是alias或者account
}

message UpdateWhoInviteUidReq {
  required uint32 uid = 1;
  required uint32 who_invite_uid = 2;
}

message BindThirdpartyIDReq {
  required uint32 uid = 1;
  required uint32 thirdparty_type = 2;  // thirdparty_type
  required string thirdparty_id = 3;    // open_id / union_id
  required bool rebind_if_exists = 4;  // true则会重新绑定，覆盖原有的uid
  required uint32 rebind_check_uid = 5;  // rebind为true时, 需要指定原有uid
}

message BindThirdpartyIDResp {
  required bool affected = 1;
}

message OfficialCertifyInfo {
  optional uint32 uid = 1;
  optional string title = 2;
  optional string intro = 3;
  optional string style = 4;
}

message GetUserOfficialCertifyReq {
  required uint32 uid = 1;
}

message GetUserOfficialCertifyListResp {
  repeated OfficialCertifyInfo info_list = 1;
}

message GetUserOfficialCertifyByUidsReq {
  repeated uint32 uids = 1;
}

// 根据UID 获取 用户注册时的第三方OPENid 列表
message GetUserThirdpartyIDsReq {
  required uint32 uid = 1;
}
message GetUserThirdpartyIDsResp {
  repeated string thirdparty_id_list = 1;
}

message GetSecurePhoneBindUidListReq {
  required string phone = 1;
  required bool query_login_uid = 2;
}

message GetSecurePhoneBindUidListResp {
  optional uint32 login_uid =
      1;  // returns if query_login_uid in GetSecurePhoneBindUidListReq is true
  repeated uint32 uid_list = 2;
}

message GetUidListByPhoneReq {
  required string phone = 1;
  repeated string scene_list = 2;
}

message PhoneUidInfo {
  required string phone = 1;
  required uint64 uid = 2;
  required string scene = 3;
}

message GetUidListByPhoneResp {
  repeated PhoneUidInfo list = 1;
}

message GetUidListByOpenidReq {
  required uint32 third_party_type = 1;
  required string openid = 2;
  repeated string scene_list = 3;
}

message OpenidUidInfo {
  required uint32 third_party_type = 1;
  required string openid = 2;
  required uint64 uid = 3;
  required string scene = 4;
}

message GetUidListByOpenidResp {
  repeated OpenidUidInfo list = 1;
}

message GetNextUidReq {

}

message GetNextUidResp {
  optional uint64  uid = 1;
}

service Account {
  option (tlvpickle.Magic) = 14001;             // 服务监听端口号

  rpc CreateUser(CreateUserReq) returns (UidResp) {
    option (tlvpickle.CmdID) = 1;  // 命令号
    option (tlvpickle.OptString) =
        "t:n:p:i:s:d:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-t <telephone> -n <username> -p <password> "
                               "-i <nickname> -s <sex> -d "
                               "<device>";  // 测试工具的命令号帮助
  }

  rpc GetUidByPhone(GetUidByPhoneReq) returns (UidResp) {
    option (tlvpickle.CmdID) = 2;  // 命令号
    option (tlvpickle.OptString) = "t:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-t <telephone> ";  // 测试工具的命令号帮助
  }

  rpc GetUidByName(GetUidByNameReq) returns (UidResp) {
    option (tlvpickle.CmdID) = 3;  // 命令号
    option (tlvpickle.OptString) =
        "n:t:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-n <username> -t <name_type>";  // 测试工具的命令号帮助
  }

  rpc GetUserByUid(UidReq) returns (UserResp) {
    option (tlvpickle.CmdID) = 4;
    option (tlvpickle.OptString) = "u:n:p:"; 
    option (tlvpickle.Usage) = "[-u <uid>] [-n <username>] [-p phone]"; 
  }

  rpc UpdatePassword(UpdatePasswordReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 5;  // 命令号
    option (tlvpickle.OptString) =
        "u:p:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid> -p <password>";
  }

  rpc UpdateAlias(UpdateAliasReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 6;  // 命令号
    option (tlvpickle.OptString) =
        "u:n:k:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid> -n <new alias> -k <force_update> ";
  }

  //已废弃
  //rpc UpdateStatus(UpdateStatusReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
  //  option (tlvpickle.CmdID) = 8;  // 命令号
  //  option (tlvpickle.OptString) = "u:UR:N:";  // 测试工具的命令号参数， 注意最后的冒号
  //  option (tlvpickle.Usage) = "-u <uid>  -U <Unregister> -L <CleaR Local account after exit> -N <noble flag>";
  //}

  rpc GetNicknameByUid(UidReq) returns (tlvpickle.SKBuiltinString_PB) {
    option (tlvpickle.CmdID) = 9;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc CheckNameExist(GetUidByNameReq) returns (ExistResp) {
    option (tlvpickle.CmdID) = 10;
    option (tlvpickle.OptString) = "n:";
    option (tlvpickle.Usage) = "-n <name>";
  }

  rpc UpdateUserInfo(UpdateUserInfoReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 11;  // 命令号
    option (tlvpickle.OptString) =
        "u:i:s:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid> -i <nickname> -s <sex> ";
  }

  //[> 不再用，废弃<]
  rpc CreateUserWithExistPhone(CreateUserReq) returns (UidResp) {
    option (tlvpickle.CmdID) = 12;  // 命令号
    option (tlvpickle.OptString) =
        "t:n:p:i:s:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-t <telephone> -n <username> -p <password> -i "
                               "<nickname> -s <sex>";  // 测试工具的命令号帮助
  }

  rpc UpdateUserNickname(UpdateUserNicknameReq)
      returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 13;  // 命令号
    option (tlvpickle.OptString) =
        "u:n:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) =
        "-u <uid> -n <nickname>";  // 测试工具的命令号帮助
  }

  rpc UpdateUserSignature(UpdateUserSignatureReq)
      returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 14;  // 命令号
    option (tlvpickle.OptString) =
        "u:s:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) =
        "-u <uid> -s <signature>";  // 测试工具的命令号帮助
  }

  // 不再应用: 加好友是否要验证, 2021.12.5
  rpc UpdateVerify(UpdateVerifyReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 15;  // 命令号
    option (tlvpickle.OptString) =
        "u:v:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) =
        "-u <uid> -v <should verify>";  // 测试工具的命令号帮助
  }

  // 不再应用: 加好友是否要验证, 2021.12.5
  rpc CheckVerify(CheckVerifyReq) returns (CheckVerifyResp) {
    option (tlvpickle.CmdID) = 16;  // 命令号
    option (tlvpickle.OptString) =
        "u:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid>";  // 测试工具的命令号帮助
  }

  rpc CheckUserHaveGuild(CheckUserHaveGuildReq)
      returns (CheckUserHaveGuildResp) {
    option (tlvpickle.CmdID) = 17;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc UpdateUserCurrentGuild(UpdateUserCurrentGuildReq)
      returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 18;  // 命令号
    option (tlvpickle.OptString) =
        "u:g:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid> -g <guild id>";
  }

  rpc UpdateLastLoginAt(UpdateLastLoginAtReq)
      returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 19;  // 命令号
    option (tlvpickle.OptString) =
        "u:l:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid> -l <last login at>";
  }

  //已废弃，使用 installedgame服
  //rpc AddInstalledGame(AddInstalledGameReq)
  //    returns (tlvpickle.SKBuiltinEmpty_PB) {
  //  option (tlvpickle.CmdID) = 20;  // 命令号
  //  option (tlvpickle.OptString) =
  //      "u:g:";  // 测试工具的命令号参数， 注意最后的冒号
  //  option (tlvpickle.Usage) = "-u <uid> -g <game id>";
  //}

  //已废弃，使用 installedgame服
  //rpc RemoveInstalledGame(RemoveInstalledGameReq)
  //    returns (tlvpickle.SKBuiltinEmpty_PB) {
  //  option (tlvpickle.CmdID) = 21;  // 命令号
  //  option (tlvpickle.OptString) =
  //      "u:g:";  // 测试工具的命令号参数， 注意最后的冒号
  //  option (tlvpickle.Usage) = "-u <uid> -g <game id>";
  //}

  //已废弃，使用 installedgame服
  //rpc GetInstalledGameList(GetInstalledGameListReq)
  //    returns (GetInstalledGameListResp) {
  //  option (tlvpickle.CmdID) = 22;  // 命令号
  //  option (tlvpickle.OptString) =
  //      "u:";  // 测试工具的命令号参数， 注意最后的冒号
  //  option (tlvpickle.Usage) = "-u <uid>";
  //}

  rpc GetUsernameByUid(UidReq) returns (tlvpickle.SKBuiltinString_PB) {
    option (tlvpickle.CmdID) = 23;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc UpdateSex(UpdateSexReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 24;  // 命令号
    option (tlvpickle.OptString) =
        "u:s:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid> -s <sex>";
  }

  rpc UpdateUserTickDeviceId(UpdateUserTickDeviceIdReq)
      returns (UpdateUserTickDeviceIdResp) {
    option (tlvpickle.CmdID) = 25;  // 命令号
    option (tlvpickle.OptString) =
        "u:s:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid> -s <device_id>";
  }

  //rpc UpdateUserPasswordByMibao(UpdateUserPasswordByMibaoReq)
  //    returns (UpdateUserPasswordByMibaoResp) {
  //  option (tlvpickle.CmdID) = 26;  // 命令号
  //  option (tlvpickle.OptString) =
  //      "u:p:q:a:";  // 测试工具的命令号参数， 注意最后的冒号
  //  option (tlvpickle.Usage) =
  //      "-u <uid> -p <password> -q <question> -a <answer>";
  //}

  //rpc InitMibao(InitMibaoReq) returns (InitMibaoResp) {
  //  option (tlvpickle.CmdID) = 27;  // 命令号
  //  option (tlvpickle.OptString) =
  //      "u:q:a:";  // 测试工具的命令号参数， 注意最后的冒号
  //  option (tlvpickle.Usage) = "-u <uid> -q <question> -a <answer>";
  //}

  rpc GetUsersByUids(UidsReq) returns (UsersResp) {
    option (tlvpickle.CmdID) = 28;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid1,uid2,...>";
  }

  rpc LogUserBehavior(LogUserBehaviorReq) returns (LogUserBehaviorResp) {
    option (tlvpickle.CmdID) = 29;
    option (tlvpickle.OptString) = "u:t:d:";
    option (tlvpickle.Usage) = "-u <uid> -t <type> -d <detail>";
  }

  rpc GetTodayUserBehavior(GetTodayUserBehaviorReq)
      returns (GetTodayUserBehaviorResp) {
    option (tlvpickle.CmdID) = 30;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  //rpc UpdateMibao(UpdateMibaoReq) returns (UpdateMibaoResp) {
  //  option (tlvpickle.CmdID) = 31;
  //  option (tlvpickle.OptString) = "u:q:a:";
  //  option (tlvpickle.Usage) = "-u <uid> -q <question> -a <answer>";
  //}

  rpc UpdateUserInviteCode(UpdateUserInviteCodeReq)
      returns (UpdateUserInviteCodeResp) {
    option (tlvpickle.CmdID) = 32;
    option (tlvpickle.OptString) = "u:i:";
    option (tlvpickle.Usage) = "-u <uid> -i <invite_code>";
  }

  rpc WriteRegInviteHistory(WriteRegInviteHistoryReq)
      returns (WriteRegInviteHistoryResp) {
    option (tlvpickle.CmdID) = 33;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  rpc GetUserRegInviteCount(GetUserRegInviteCountReq)
      returns (GetUserRegInviteCountResp) {
    option (tlvpickle.CmdID) = 34;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc UpdateUserStatus(UpdateUserStatusReq) returns (UpdateUserStatusResp) {
    option (tlvpickle.CmdID) = 35;
    option (tlvpickle.OptString) = "u:s:o:";
    option (tlvpickle.Usage) = "-u <uid> -s <status> -o <operation>";
  }

  rpc GetUidByOpenid(GetUidByOpenidReq) returns (GetUidByOpenidResp) {
    option (tlvpickle.CmdID) = 36;
    option (tlvpickle.OptString) = "t:o:";
    option (tlvpickle.Usage) = "-t <third_party_type> -o <openid>";
  }

  rpc CreateUserForOpenid(CreateUserForOpenidReq) returns (CreateUserForOpenidResp) {
    option (tlvpickle.CmdID) = 37;  // 命令号
    option (tlvpickle.OptString) = "t:o:p:i:s:d:e:r:b:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-t <third_party_type> -o <openid> -p <password> -s <sex> -r <source> [-i <nickname>] [-d <device>] [-e <invite_uid>]  [-b <phone>]";
  } 
  rpc BindPhone(BindPhoneReq) returns (BindPhoneResp) {
    option (tlvpickle.CmdID) = 38;  // 命令号
    option (tlvpickle.OptString) = "u:p:b:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid> -p <phone> -b <isunbindold>";
  }
  //没有使用?
  rpc AddUidNumberic(AddUidNumbericReq) returns (AddUidNumbericResp) {
    option (tlvpickle.CmdID) = 39;  // 命令号
    option (tlvpickle.OptString) =
        "u:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc GetRegInviteHistory(GetRegInviteHistoryReq)
      returns (GetRegInviteHistoryResp) {
    option (tlvpickle.CmdID) = 40;  // 命令号
    option (tlvpickle.OptString) =
        "u:i:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid> -i <is_invitee>";
  }

  rpc BatchQueryUidList(BatchQueryUidListReq) returns (BatchQueryUidListResp) {
    option (tlvpickle.CmdID) = 41;  // 命令号
    option (tlvpickle.OptString) =
        "k:t:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-k <key1,key2,...> -t <type>";
  }

  rpc AutoBatchCreateUser(AutoBatchCreateUserReq)
      returns (AutoBatchCreateUserResp) {
    option (tlvpickle.CmdID) = 42;  // 命令号
    option (tlvpickle.OptString) =
        "n:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-n <count_no_more_than_50>";
  }

  rpc UpdateUserType(UpdateTypeReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 43;
    option (tlvpickle.OptString) = "u:t:";
    option (tlvpickle.Usage) = "-u <uid> -t <type>";
  }

  rpc GetLastLoginTimeAndNickname(UidReq)
      returns (GetLastLoginTimeAndNicknameResp) {
    option (tlvpickle.CmdID) = 44;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc GetUsersByAccs(GetUsersByAccsReq) returns (GetUsersByAccsResp) {
    option (tlvpickle.CmdID) = 45;
    option (tlvpickle.OptString) = "n:";
    option (tlvpickle.Usage) = "-n <account>";
  }

  rpc BindPhoneWithUid(BindPhoneWithUidReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 46;
    option (tlvpickle.OptString) = "u:p:t:";
    option (tlvpickle.Usage) = "-u <uid> -p <phone> -t <type 1:LOGIN 2:SECURE>";
  }

  rpc UnbindPhoneWithUid(UnbindPhoneWithUidReq) returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 47;
    option (tlvpickle.OptString) = "u:p:s:";
    option (tlvpickle.Usage) = "-u <uid> -p <phone> -s <scene,default:tt>";
  }

  //rpc GetBannedRecord(tlvpickle.SKBuiltinEmpty_PB)
  //    returns (GetBannedRecordResp) {
  //  option (tlvpickle.CmdID) = 48;
  //  option (tlvpickle.OptString) = "";
  //  option (tlvpickle.Usage) = "";
  //}

  rpc GetUserTypeByUid(UidReq) returns (tlvpickle.SKBuiltinUint32_PB) {
    option (tlvpickle.CmdID) = 49;  // 命令号
    option (tlvpickle.OptString) =
        "u:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid>";  // 测试工具的命令号帮助
  }

  //rpc GetBannedReason(GetBannedReasonReq) returns (GetBannedReasonResp) {
  //  option (tlvpickle.CmdID) = 50;
  //  option (tlvpickle.OptString) = "";
  //  option (tlvpickle.Usage) = "";
  //}

  rpc GetWechatUnionId(GetWechatUnionIdReq) returns (GetWechatUnionIdResp) {
    option (tlvpickle.CmdID) = 51;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  rpc SetWechatUnionId(SetWechatUnionIdReq)
      returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 52;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

  //已废弃，使用 installedgame服
  // 用户上报的游戏安装信息 (全量覆盖)
  //rpc BatchAddInstalledGame(BatchAddInstalledGameReq)
  //    returns (BatchAddInstalledGameResp) {
  //  option (tlvpickle.CmdID) = 53;
  //  option (tlvpickle.OptString) = "u:g:p:";
  //  option (tlvpickle.Usage) = "-u <uid> -g <game id> -p <pack name>";
  //}
  //已废弃，使用 installedgame服
  // 清除用户上报的游戏安装信息
  //rpc ClearInstalledGame(ClearInstalledGameReq)
  //    returns (ClearInstalledGameResp) {
  //  option (tlvpickle.CmdID) = 54;
  //  option (tlvpickle.OptString) = "u:";
  //  option (tlvpickle.Usage) = "-u <uid>";
  //}

  //用户激活信息
  rpc InsertUserActivateInfo(InsertUserActivateInfoReq)
      returns (InsertUserActivateInfoRsp) {
    option (tlvpickle.CmdID) = 55;
    option (tlvpickle.OptString) = "u:t:q:s:";
    option (tlvpickle.Usage) =
        "-u <uid> [-t <activate time>] -q <channel> [-s <source>]";
  }

  rpc GetUserActivateInfo(GetUserActivateInfoReq)
      returns (GetUserActivateInfoRsp) {
    option (tlvpickle.CmdID) = 56;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid>";
  }

  rpc BatchGetUserActivateInfo(BatchGetUserActivateInfoReq)
      returns (BatchGetUserActivateInfoRsp) {
    option (tlvpickle.CmdID) = 57;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid1,uid2,uid3>";
  }

  // detach第三方账号
  rpc DetachThirdParty(DetachThirdPartyReq) returns (DetachThirdPartyRsp) {
    option (tlvpickle.CmdID) = 58;
    option (tlvpickle.OptString) = "u:s:";
    option (tlvpickle.Usage) = "-u <uid> -s <scene,default:tt>";
  }

  rpc UpdateUserPrefixValid(UpdateUserPrefixValidReq)
      returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 59;
    option (tlvpickle.OptString) = "u:p:";
    option (tlvpickle.Usage) = "-u <uid> -p <prefix_valid>";
  }

  //已废弃，使用 installedgame服
  //rpc GetGameInstalledUserList(GetGameInstalledUserListReq)
  //    returns (GetGameInstalledUserListResp) {
  //  option (tlvpickle.CmdID) = 60;
  //  option (tlvpickle.OptString) = "g:";
  //  option (tlvpickle.Usage) = "-g <game_id>";
  //}

  //rpc UpdatePhotoAlbum(UpdatePhotoAlbumReq)
  //    returns (tlvpickle.SKBuiltinEmpty_PB) {
  //  option (tlvpickle.CmdID) = 70;
  //  option (tlvpickle.OptString) = "u:l:";
  //  option (tlvpickle.Usage) = "-u <uid> -l <img_list>";
  //}

  //rpc GetPhotoAlbum(GetPhotoAlbumReq) returns (GetPhotoAlbumResp) {
  //  option (tlvpickle.CmdID) = 71;
  //  option (tlvpickle.OptString) = "u:";
  //  option (tlvpickle.Usage) = "-u <uid>";
  //}

  rpc UpdateUserQuitGuildInfo(UpdateUserQuitGuildInfoReq)
      returns (UpdateUserQuitGuildInfoResp) {
    option (tlvpickle.CmdID) = 72;
    option (tlvpickle.OptString) = "u:q:t:T:r:";
    option (tlvpickle.Usage) =
        "-u <uid> -q <last_quit_guild_id> -t <last_quit_guild_type> -T "
        "<last_quit_guild_time> -r <reset_current_guildid>";
  }

  rpc BatchUpdateUserQuitGuildInfo(BatchUpdateUserQuitGuildInfoReq)
      returns (BatchUpdateUserQuitGuildInfoResp) {
    option (tlvpickle.CmdID) = 73;
    option (tlvpickle.OptString) = "u:q:t:T:r:";
    option (tlvpickle.Usage) =
        "-u <uid1,uid2,uid3...> -q <last_quit_guild_id> -t "
        "<last_quit_guild_type> -T <last_quit_guild_time> -r "
        "<reset_current_guildid>";
  }

  rpc GetUserByUidNoCache(UidReq) returns (UserResp) {
    option (tlvpickle.CmdID) = 74;  // 命令号
    option (tlvpickle.OptString) =
        "u:";  // 测试工具的命令号参数， 注意最后的冒号
    option (tlvpickle.Usage) = "-u <uid>";  // 测试工具的命令号帮助
  }

  rpc GetUsersByUidsNoCache(UidsReq) returns (UsersResp) {
    option (tlvpickle.CmdID) = 75;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u <uid1,uid2,...>";
  }

  rpc UpdateUserGuildPrefixByUid(UpdateUserGuildPrefixByUidReq)
      returns (UpdateUserGuildPrefixByUidResp) {
    option (tlvpickle.CmdID) = 76;
    option (tlvpickle.OptString) = "u:s:";
    option (tlvpickle.Usage) = "-u <uid> -s <prefix>";
  }

  rpc UpdateUserGuildPrefixByGuildId(UpdateUserGuildPrefixByGuildIdReq)
      returns (UpdateUserGuildPrefixByGuildIdResp) {
    option (tlvpickle.CmdID) = 77;
    option (tlvpickle.OptString) = "g:s:";
    option (tlvpickle.Usage) = "-g <guild_id> -s <prefix>";
  }

  rpc RebindPhoneWithUid(RebindPhoneWithUidReq)
      returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 78;
    option (tlvpickle.OptString) = "u:p:t:";
    option (tlvpickle.Usage) = "-u <uid> -p <phone> -t <0.unbind 1.bind>";
  }

  rpc RecycleUsername(RecycleUsernameReq) returns (RecycleUsernameResp) {
    option (tlvpickle.CmdID) = 79;
    option (tlvpickle.OptString) = "n:";
    option (tlvpickle.Usage) = "-n <username(account/alias)>";
  }

  rpc UpdateWhoInviteUid(UpdateWhoInviteUidReq)
      returns (tlvpickle.SKBuiltinEmpty_PB) {
    option (tlvpickle.CmdID) = 80;
    option (tlvpickle.OptString) = "u:w:";
    option (tlvpickle.Usage) = "-u <uid> -w <who_invite_uid>";
  }

  rpc BindThirdpartyID(BindThirdpartyIDReq) returns (BindThirdpartyIDResp) {
    option (tlvpickle.CmdID) = 81;
    option (tlvpickle.OptString) = "u:t:n:r:k:";
    option (tlvpickle.Usage) = "-u <uid> -t <type> -n <id> -r "
                               "<rebind_if_exists> -k <rebind_check_uid>";
  }

  //rpc GetUserOfficialCertify(GetUserOfficialCertifyReq)
  //    returns (OfficialCertifyInfo) {
  //  option (tlvpickle.CmdID) = 82;
  //  option (tlvpickle.OptString) = "u:";
  //  option (tlvpickle.Usage) = "-u <uid>";
  //}

  //rpc GetUserOfficialCertifyList(tlvpickle.SKBuiltinEmpty_PB)
  //    returns (GetUserOfficialCertifyListResp) {
  //  option (tlvpickle.CmdID) = 83;
  //  option (tlvpickle.OptString) = "";
  //  option (tlvpickle.Usage) = "";
  //}

  //rpc SetUserOfficialCertify(OfficialCertifyInfo)
  //    returns (tlvpickle.SKBuiltinEmpty_PB) {
  //  option (tlvpickle.CmdID) = 84;
  //  option (tlvpickle.OptString) = "u:t:i:";
  //  option (tlvpickle.Usage) = "-u<uid> -t<title> -i<intro>";
  //}

  //rpc DelUserOfficialCertify(GetUserOfficialCertifyReq)
  //    returns (tlvpickle.SKBuiltinEmpty_PB) {
  //  option (tlvpickle.CmdID) = 85;
  //  option (tlvpickle.OptString) = "u:";
  //  option (tlvpickle.Usage) = "-u<uid>";
  //}

  //rpc GetUserOfficialCertifyByUids(GetUserOfficialCertifyByUidsReq)
  //    returns (GetUserOfficialCertifyListResp) {
  //  option (tlvpickle.CmdID) = 86;
  //  option (tlvpickle.OptString) = "u:";
  //  option (tlvpickle.Usage) = "-u<uid>";
  //}

  rpc GetUserThirdpartyIDs(GetUserThirdpartyIDsReq)
      returns (GetUserThirdpartyIDsResp) {
    option (tlvpickle.CmdID) = 89;
    option (tlvpickle.OptString) = "u:";
    option (tlvpickle.Usage) = "-u<uid>";
  }

  rpc GetSecurePhoneBindUidList(GetSecurePhoneBindUidListReq)
      returns (GetSecurePhoneBindUidListResp) {
    option (tlvpickle.CmdID) = 90;
    option (tlvpickle.OptString) = "p:";
    option (tlvpickle.Usage) = "-p <phone>";
  }

  rpc GetUidListByPhone(GetUidListByPhoneReq) returns (GetUidListByPhoneResp) {
    option (tlvpickle.CmdID) = 91;
    option (tlvpickle.OptString) = "p:";
    option (tlvpickle.Usage) = "-p <phone>";
  }

  rpc GetUidListByOpenid(GetUidListByOpenidReq) returns (GetUidListByOpenidResp) {
    option (tlvpickle.CmdID) = 92;
    option (tlvpickle.OptString) = "t:o:";
    option (tlvpickle.Usage) = "-t <third_party_type> -o <openid>";
  }

  rpc GetNextUid(GetNextUidReq) returns (GetNextUidResp) {
    option (tlvpickle.CmdID) = 93;
    option (tlvpickle.OptString) = "";
    option (tlvpickle.Usage) = "";
  }

}
