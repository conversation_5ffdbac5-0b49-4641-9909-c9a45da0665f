syntax="proto2";


package apicenter;

import "common/tlvpickle/skbuiltintype.proto";

message BaseReq{
    optional uint32 appid = 1;
}

message BaseResp{
    enum Result{
        SUCCESS = 0;
    }
    required int32 ret = 1;
    optional string err_msg = 2;
}

//user info manager
message UserInfo{
    required uint32 uid = 1;
    required string phone = 2;
    required string name = 3;
    optional string nickname = 4;
    optional uint32 sex = 5;
    optional string signature = 6;
    optional uint32 guild_id = 7;
    optional uint32 registered_time = 8;
    optional string invite_code = 9;
    optional uint32 last_login_time = 10;
    optional uint32 last_quit_guild_id = 11;
    optional uint32 last_quit_guild_time = 12;
}

message GetUserByUidReq{
    required BaseReq base_req = 1;
    repeated uint32 uid_list = 2;
}

message GetUserByUidRsp{
    required BaseResp base_resp = 1;
    repeated UserInfo user_list = 2;
}

//用户激活信息

message UserActivateInfo{
    required uint32 uid = 1;
    required uint64 activate_time = 2;  //激活(首次登陆)时间
    required string channel = 3;        //渠道
}

message GetUserActivateInfoReq{
    required BaseReq base_req = 1;
    repeated uint32 uid_list = 2;
}

message GetUserActivateInfoRsp{
    required BaseResp base_resp = 1;
    repeated UserActivateInfo info_list = 2;
}

//用户成长信息/红钻
message GetGrowthProfileReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    optional bool with_reddiamond = 3;

}
message GetGrowthProfileRsp {
    required BaseResp base_resp = 1;
    optional uint32 uid = 2;
    optional int32 reddiamond = 3;
}

//红钻交易
message TransactRedDiamondReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required string transact_id = 3;
    required string transact_desc = 4;
    required int32 amount = 5;
    required uint32 op_uid = 6;
}
message TransactRedDiamondRsp {
    required BaseResp base_resp = 1;
}
//红钻查询
message CheckRedDiamondTransactReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required string transact_id = 3;
}
message CheckRedDiamondTransactRsp {
    required BaseResp base_resp = 1;
    optional bool exist = 2;
}

//红钻交易 new
message RedDiamondTransaction{
    enum TYPE{
        CONSUME = 1;
        REWARD = 2;
    }
    required uint32 uid = 1;
    required string transact_id = 2;
    required string transact_desc = 3;
    required uint32 amount = 4;
    required uint32 transact_type = 5;  //TYPE
    optional uint32 op_uid = 6;
}

message ConsumeRedDiamondReq{
    required BaseReq base_req = 1;
    required RedDiamondTransaction transact = 2;
    optional bool to_stock = 3;
}

message ConsumeRedDiamondRsp{
    required BaseResp base_resp = 1;
}

message RewardRedDiamondReq{
    required BaseReq base_req = 1;
    required RedDiamondTransaction transact = 2;
    optional bool from_stock = 3;
}

message RewardRedDiamondRsp{
    required BaseResp base_resp = 1;
}

message GetStockRedDiamondReq{
    required BaseReq base_req = 1;
}
message GetStockRedDiamondRsp{
    required BaseResp base_resp = 1;
    optional uint64 amount=2;
}

//channel manager
enum CHANNEL_BIND_TYPE{
    CHANNEL_BIND_GUILD = 1;
    CHANNEL_BIND_FREE = 2;
}

message ChannelInfo{
    required uint32 channel_id = 1;
    optional string name = 2;
    optional uint32 bind_type = 3;
    optional uint32 bind_id = 4;
    optional uint32 app_id = 5;
    optional uint32 session_id = 6; //=sdkid, allocate by channelsrv for every AppId, now equal channel_id
    optional uint32 creator_uid = 7;
    optional uint32 create_ts = 8;
    optional uint32 member_count = 9;
    optional uint32 member_capacity = 10;
    optional string passwd = 11;
}

message CreateChannelReq{
    required BaseReq base_req = 1;
    required string name = 2;
    required uint32 bind_type = 3;
    optional uint32 bind_id = 4;
    optional string passwd = 5;
}

message CreateChannelRsp{
    required BaseResp base_resp = 1;
    optional ChannelInfo channel_info = 2;
}

message DismissChannelReq{
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message DismissChannelRsp{
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
}

message ModifyChannelReq{
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    optional string name = 3;
    optional uint32 mod_passwd = 4; // true/false
    optional string passwd = 5;
}

message ModifyChannelRsp{
    required BaseResp base_resp = 1;
    optional ChannelInfo channel_info = 2;
}

message GetChannelReq{
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelRsp{
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    optional ChannelInfo channel_info = 3;
}

message GetChannelPasswdReq{
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}

message GetChannelPasswdRsp{
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    optional string passwd = 3;
}

message JoinChannelReq{
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    optional string passwd = 3;
}
message JoinChannelRsp{
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    optional ChannelInfo channel_info = 3;
}
message QuitChannelReq{
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
}
message QuitChannelRsp{
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
}
message GetChannelMemberListReq{
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 offset   = 3;
    required uint32 limit    = 4;
}

message ChannelMemberInfo{
    required uint32 uid       = 1;
    required string account   = 2;
    required string nick_name = 3;
}

message GetChannelMemberListRsp{
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    required uint32 offset   = 3;
    repeated ChannelMemberInfo member_list = 4;
}

message KickoutChannelMemberReq{
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    optional uint32 ban_enter_second = 3;   //enter again after 'ban_enter_second'
    repeated uint32	target_uid_list	= 4;

    optional uint32 kick_type = 5;  // 1: 管理员踢人 2: 内审平台踢人
    optional string	kick_text = 6;  // 踢人消息
}
message KickoutChannelMemberRsp{
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
    repeated uint32	kickouted_uid_list	= 3;
}

message UserChannelInfo{
    required uint32 uid       = 1;
    required uint32 join_channel = 2;   //=true/false
    optional uint32 channel_id = 3;
}

message GetUserChannelIdReq{
    required BaseReq base_req = 1;
    repeated uint32 uid_list = 2;
}
message GetUserChannelIdRsp{
    required BaseResp base_resp = 1;
    repeated UserChannelInfo user_channelinfo_list = 2;
}


message GetUserAdminChannelListReq{
    required BaseReq base_req = 1;
    required uint32 admin_type = 2;
    optional uint32 channel_type = 3;
    optional uint32 start_idx = 4;
    optional uint32 limit = 5;
}
message GetUserAdminChannelListResp{
    required BaseResp base_resp = 1;
    repeated ChannelInfo channel_list = 2;
}

//push & notification serverice

enum PROXY_NOTIFICATION_TYPE{
    PROXY_NT_TYPE_PUSH = 1;
    PROXY_NT_TYPE_TRANSMIT_PUSH = 2;
    PROXY_NT_TYPE_BUTT = 3;
}
message ProxyNotificationContent{
    required uint32 type = 1;   //PROXY_NOTIFICATION_TYPE
    required bytes data = 2;
}

message ApnsLocalizable{
    required string key = 1;
    repeated string args = 2;
}

message ApnsAlertDict{
    required string body = 1;
    optional string title = 2;
    optional ApnsLocalizable title_loc = 3;
    optional string action_loc_key = 4;
    optional ApnsLocalizable message_loc = 5;
    optional string launch_image = 6;
}

message ApnsAlart{
    optional string simple = 1;
    optional ApnsAlertDict dict = 2;
}

enum APNS_SOUND_TYPE {
    APNS_SOUND_TYPE_NO = 0;
    APNS_SOUND_TYPE_NO_MSG = 1;
    APNS_SOUND_TYPE_NO_VOIP = 2;
}

message ApnsNotificationContent{
    optional string category = 1;
    required uint32 seq = 2;
    optional uint32 time = 3;
    required ApnsAlart alert = 4;
    optional uint32 badge = 5;
    optional uint32 expiry = 6;
    optional string ext_json = 7;
    required uint32 sound_type = 8;
}

message NotificationContent{
    required uint32 seq = 1;
    repeated uint32 terminal_type = 2;
    optional ProxyNotificationContent proxy_notification = 3;
    optional ApnsNotificationContent apns_notification = 4;
}

message PushWithUidListReq{
    required BaseReq base_req = 1;
    repeated uint32 uid_list = 2;
    required NotificationContent notification_content = 3;
}

message PushWithUidListRsp{
    required BaseResp base_resp = 1;
}

message MulticastAccountInfo{
    required uint32 id = 1;
    required string account = 2;
}

message PushWithMulticastRelationReq{
    required BaseReq base_req = 1;
    required MulticastAccountInfo multicast_account = 2;
    required NotificationContent notification_content = 3;
}

message PushWithMulticastRelationRsp{
    required BaseResp base_resp = 1;
}


message SystemMsg{
    required uint32 terminal_type = 1;
    required uint32 type_mask = 2;
    required uint32 act_id = 3;
    required string title = 4;
    optional string jump_url = 5;
    optional string act_sub_title = 6;
    optional string act_pic_url = 7;
}

message PushSystemMsgWithUidListReq{
    required BaseReq base_req = 1;
    repeated uint32 uid_list = 2;
    required SystemMsg system_msg = 3;
}
message PushSystemMsgWithUidListRsp{
    required BaseResp base_resp = 1;

}
message PushSystemMsgWithMulticastRelationReq{
    required BaseReq base_req = 1;
    required MulticastAccountInfo multicast_account = 2;
    required SystemMsg system_msg = 3;
}
message PushSystemMsgWithMulticastRelationRsp{
    required BaseResp base_resp = 1;
}

// 全网房间推送
message PushMsgToAllChannelsReq{
    required BaseReq base_req = 1;
    required uint32 type = 2;       // 推送类型 see push_.proto 中 PushMessage::CMD_TYPE
    required bytes data = 3;        // type 对应数据 see push_.proto
}
message PushMsgToAllChannelsRsp{
    required BaseResp base_resp = 1;
}

// 推送房间消息
message PushMsgToChannelReq{
    required BaseReq base_req = 1;
    required uint32 type = 2;    // see channel_.proto 的 ChannelMsgType
    required uint32 uid = 3;
    required uint32 channel_id = 4;
    required bytes data = 5;       // type 对应数据   push_.proto
    optional string content = 6;
}
message PushMsgToChannelResp{
    required BaseResp base_resp = 1;
}

// 2020白色情人节房间送礼弹窗
message PushValentineActPopUpReq {
    required BaseReq base_req = 1;
    required uint32 from_uid = 2;
    required uint32 target_uid = 3;
    required uint32 channel_id = 4;
    optional uint32 send_ts = 5;    // 时间戳
    required string lottie_url = 6; // Lottie 动效 url
    required string lottie_md5 = 7; // Lottie 动效 md5a.
    optional uint32 expire_ts = 8;  // 展示时间 可不填，不填则为默认
    optional string text = 9;       // 文字显示 （送礼时间）
    optional string left_name_color = 10;
    optional string right_name_color = 11;
    optional string content_color = 12;
    optional string extend_json = 13;
    optional string vap_url = 14; // vap 动效 url
    optional string vap_md5 = 15; // vap 动效 md5
}

message PushValentineActPopUpResp { required BaseResp base_resp = 1; }

// 年度盛典榜单结算全服公告
message PushYearActBreakingNewsReq{
    enum VIEW_TYPE{
        VIEW_TYPE_RICHER = 1;   // 神壕榜
        VIEW_TYPE_PERSONAL = 2; // 个人榜
        VIEW_TYPE_CP = 3;       // CP榜
        VIEW_TYPE_GUILD = 4;    // 公会榜
    }
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required string jump_url = 3;   // 跳转url
    required string content = 4;    // 文本内容
    required uint32 view_type = 5;  // 展示样式类型 see enum:VIEW_TYPE
    optional uint32 guild_id = 6;   // 公会id（当type为VIEW_TYPE_GUILD时有效）
    optional string guild_name = 7; // 公会名称（当type为VIEW_TYPE_GUILD时有效）
    optional uint32 cp_uid = 8;     // cp 对象uid（当type为VIEW_TYPE_CP时有效）
}
message PushYearActBreakingNewsRsp{
    required BaseResp base_resp = 1;
}

// 年度盛典活动弹窗
message PushYearActPopUpReq{
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    required uint32 type = 3;            // 弹窗类型  see push_.proto 中 YearActPopUpMsg::POP_UP_MSG_TYPE
    required string content = 4;         // 文本内容
    required uint32 expire_ts = 5;       // 过期时间，过期后还没弹就不弹了 单位（s）
    optional string button_jump_url = 6; // 按钮跳转url
    optional string title = 7;           // 标题
}
message PushYearActPopUpRsp{
    required BaseResp base_resp = 1;
}

// 全服公告
message PushCommonBreakingNewsReq {
    required BaseReq base_req = 1;
    required uint32 op_uid = 2;
    required bytes break_news_pb_content = 3; // push_.proto中CommonBreakingNewsV3 序列化
}

message PushCommonBreakingNewsResp {
    required BaseResp base_resp = 1;
}

// guild

message GuildInfo{
    required uint32 guild_id = 1;
    required uint32 short_id = 2;
    required string name = 3;
    required string intro = 4;
    required uint32 member_count = 5;
    required uint32 owner_uid = 6;     //会长
    required uint32 creator_uid = 7;   //会长
    required uint32 create_time = 8;
}

message GetGuildReq{
    required BaseReq base_req = 1;
    repeated uint32 guildid_list = 2;
}

message GetGuildRsp{
    required BaseResp base_resp = 1;
    repeated GuildInfo guild_list = 2;
}

message GuildMemberInfo{
    required uint32 uid = 1;
    required uint32 role = 2;
}

message GetGuildMemberListReq{
    required BaseReq base_req = 1;
    required uint32 guild_id = 2;
    required uint32 offset   = 3;
    required uint32 limit    = 4;
}

message GetGuildMemberListRsp{
    required BaseResp base_resp = 1;
    required uint32 guild_id = 2;
    required uint32 offset   = 3;
    repeated GuildMemberInfo member_list = 4;
}

//公会星级
message GetGuildStarLevelReq{
    required BaseReq base_req = 1;
    repeated uint32 guildid_list = 2;
}

message GuildStarLevel{
    required uint32 guild_id = 1;
    required uint32 star_level = 2;
}

message GetGuildStarLevelRsp{
    required BaseResp base_resp = 1;
    repeated GuildStarLevel star_level_list = 2;
}

// 公会状态
message GuildStatus{
    //公会有效性
    enum E_GUILD_AVAILABILITY
    {
        E_GUILD_NIL = 0;
        E_GUILD_NORMAL = 1;
        E_GUILD_DISMISSED = 2;
        E_GUILD_NOT_EXIST = 3;
    }
    required uint32 guild_id = 1;
    optional uint32 availability = 2;   //E_GUILD_AVAILABILITY
}
message GetGuildStatusReq{
    required BaseReq base_req = 1;
    repeated uint32 guildid_list = 2;
}

message GetGuildStatusRsp{
    required BaseResp base_resp = 1;
    repeated GuildStatus status_list = 2;
}

//任务
message HcIncreaseMissionCountReq {
    required uint32 uid = 1;
    required uint32 mission_id = 2;
    required uint32 count = 3;
}


message HcConsume {
    required string channel = 1;		// 渠道
    required uint32 fee = 2;			// 消费金额（单位：分）
    required uint32 user_cnt = 3;		// 消费用户数
}

message PostHcConsumeReq {
    required BaseReq base_req = 1;
    required string date = 2;			// 时间日期："2016-07-21"
    repeated HcConsume consume = 3;		// 渠道消费列表
}

message PostHcConsumeResp {
    required BaseResp base_resp = 1;
}

//IM Message
enum IM_SENDER_TYPE{ //发送者类型
    IM_SENDER_NIL = 0;
    IM_SENDER_NORMAL = 1;
    IM_SENDER_ASSISTANT_OneYuanWinTreasure = 2;
    IM_SENDER_PUBLIC_ACCOUNT = 3;
}

enum IM_RECEIVER_TYPE{ //接收者类型
    IM_RECEIVER_NIL = 0;
    IM_RECEIVER_USER = 1;
    IM_RECEIVER_PUBLIC_ACCOUNT = 2;
    IM_RECEIVER_BROADCAST = 3;
}

enum IM_CONTENT_TYPE{   //消息内容类型
    IM_CONTENT_NIL = 0;     //

    //最普通的文本，对应 ~/appsvr/src/proto/pbfile/im.proto TEXT_MSG (= 1)
    IM_CONTENT_TEXT = 1;

    //带高亮和跳转的文本，对应 ~/appsvr/src/proto/pbfile/im.proto TT_COMMON_TEXT_NOTIFY (= 37)
    IM_CONTENT_TEXT_WITH_HL_URL = 2;

    //带图片、标题、跳转、内容等的公众号推送消息，对应 ~/appsvr/src/proto/pbfile/im.proto OFFICAIL_MESSAGE_SINGLE/OFFICAIL_MESSAGE_BUNCH (=22/23)
    IM_CONTENT_OFFICIAL_MESSAGE = 3;

    //扩展字段 对应~/appsvr/src/proto/pbfile/im.proto NEW_EXTENDED (= 21)
    IM_CONTENT_NEW_EXTENDED = 4;

    //扩展字段 对应~/appsvr/src/proto/pbfile/im.proto EXTENDED_MSG (= 5)
    IM_CONTENT_EXTENDED = 5;
}

//IM_CONTENT_TEXT
message ImTextNormal{
    required string content = 1;
}

//IM_CONTENT_TEXT_WITH_HL_URL
message ImTextWithHighlightUrl{
    required string content = 1;
    required string hightlight = 2;
    required string url = 3;
}

//IM_CONTENT_OFFICIAL_MESSAGE

//action 对应 proto/pbfile/ga_base.proto : ControlBlock
message OfficialMessageAction{
    enum Action{
        IN_APP_NAVIGATION = 1;   //内部跳转
        GLOBAL_NAVIGATION = 2;   //外部跳转
    }
    required uint32 action = 1;
    optional string uri = 2;    //全路径
}

// 对应 proto/pbfile/ga_base.proto : OfficialMessageBody
message OfficialMessageBody{
    required string title = 1;
    required string content = 2;
    required uint32 at = 3;
    optional string image = 4;  //url
    optional OfficialMessageAction action = 5;
}

//对应 proto/pbfile/ga_base.proto : OfficialMessageSingle、OfficialMessageBunch
message ImOfficialMessage{
    required uint32 message_id = 1; //未使用，但是OfficialMessageSingle/OfficialMessageBunch有，先加上
    repeated OfficialMessageBody messages = 2;
}

message ImType{
    required uint32 sender_type = 1;
    required uint32 receiver_type = 2;
    required uint32 content_type = 3;
}

message ImContent{
    optional ImTextNormal text_normal = 1;  //IM_CONTENT_TEXT && IM_CONTENT_NEW_EXTENDED
    optional ImTextWithHighlightUrl text_hl_url = 2;
    optional ImOfficialMessage official_message = 3;
}

// 废弃
enum Platform {
    Android = 0;            // 仅安卓接收(废弃)
    iOS = 1;                // 仅iOS接收(废弃)
    PC = 2;                // 仅pc接收(废弃)
    UNSPECIFIED = 65535;    // 不指定，全平台接收(废弃)
}

message ImMsg{
    required ImType im_type = 1;
    optional uint32 from_uid = 2;       // 应为from_id, 根据ImType::sender_type决定id类型
    repeated uint32 to_id_list = 3;     // to_id_list, 根据ImType::receiver_type决定id类型
    required ImContent im_content = 4;
    optional uint32 client_msg_id = 5;
    optional uint32 client_msg_time = 6;
    optional uint32 server_msg_id = 7;      // client指定server_msg_id进行推送, 如未指定, 则由服务器生成
    optional uint32 server_msg_time = 8;    // 如未指定, 则由服务器生成

    optional Platform platform = 9;         // 指定接收的平台 （废弃 新版改用app_platform字段）
    optional uint32 expires_at = 10;        // 消息的过期时间

    optional string app_name = 11;        //指定接收的应用包 不传或者为空代表全平台， 欢游：huanyou，TT ：ttvoice，在呀：zaiya
    optional string app_platform = 12;    //指定接收的平台（用于代替旧版的platform字段，为空或者不传表示旧版本不使用该字段  ）可以填的值有 "all" 或者"ios" 或者 "android", "pc"
    optional bool b_present_both = 13;    // 是否在双方IM列表显示消息
}

message SendImMsgReq{
    required BaseReq base_req = 1;
    repeated ImMsg msg_list = 2;
    optional bool with_notify = 3;
}

message SendImMsgRsp{
    required BaseResp base_resp = 1;
    optional string withdraw_token = 2;     // 撤销凭证，如果未返回，则该消息不支持撤回
}

message MultiWithdrawToken {
    message WithdrawToken {
        required uint32 id = 1;
        required string suffix = 2;
        required uint32 seq_id = 3;
    }
    repeated WithdrawToken tokens = 1;
}

message WithdrawImMsgReq {
    required BaseReq base_req = 1;
    required string withdraw_token = 2;     // 撤销凭证
}

message WithdrawImMsgResp {
    required BaseResp base_resp = 1;
}

// 触发用户成长信息（经验/财富魅力值/红钻）sync notify通知
message NotifyGrowInfoSyncReq {
    required uint32 uid = 1;
}

message NotifyGrowInfoSyncResp {

}

// 简单通用的sync notify
message NotifySimpleSyncReq{
    required uint32 uid = 1;
    required uint32 sync_type = 2;
}


message NotifySimpleSyncResp{
}


// 踢用户下线
message KickoutUserReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
    optional string reason = 3;		// 用于客户端展示，告诉用户为啥被踢下线
}

message KickoutUserResp {
    required BaseResp base_resp = 1;
}

enum AUDIT_OP_TYPE {
    AUDIT_OP_NIL = 0;
    AUDIT_OP_PASS = 1;
    AUDIT_OP_DENY = 2;
}

message AuditUnregApplyReq {
    required uint32 apply_id = 1;       //注销提交id
    required uint32 uid = 2;
    required uint32 op_type = 3;        //审核操作类型
    optional string operator_id = 4;    //操作人
}

message AuditUnregApplyRsp {
    required BaseResp base_resp = 1;
}

enum AUDIT_STATUS {
    AUDIT_NIL = 0;
    AUDIT_WAIT = 1;
    AUDIT_OK = 2;
    AUDIT_DENY = 3;
}

message GetUnregApplyAuditRecordReq {   //时间选项暂不提供
    optional uint32 status = 1;
    optional uint32 uid = 2;
    optional uint32 offset = 3;
    optional uint32 limit = 4;
}

message UnregApplyAuditInfo {
    required uint32 apply_id = 1;
    required uint32 uid = 2;
    required uint32 apply_at = 3;    //提交时间
    required uint32 status = 4;      //审核状态
    optional uint32 op_at = 5;       //操作时间
    optional string operator_id = 6; //操作人
}

message GetUnregApplyAuditRecordRsp {
    required BaseResp base_resp = 1;
    repeated UnregApplyAuditInfo info_list = 2;
    required uint32 total = 3;
}


// 触发客户端配置文件SYNC。触发 CLIENT_FILE SYNC
message UpdateAppConfigFileSyncReq {
    required string file_name = 1;
    required string file_url = 2;
    required string file_md5 = 3;    //提交时间
    optional string app_platform = 4; //指定接收的平台可以填的值有 "ios" 或者 "android"
}

message UpdateAppConfigFileSyncResp {
    required BaseResp base_resp = 1;
}

enum ESecurityOperSource
{
    ENUM_SECURITY_OP_FROM_APPEAL_WEB = 1;  // 客服申诉web后台
    ENUM_SECURITY_OP_FROM_OPERATION_WEB  = 2;   // 运营web后台
    ENUM_SECURITY_OP_FROM_APPEAL_SVR = 3;  // 申诉服务
}

// 重置账户密码
message ResetPasswrodReq {
    required uint32 uid = 1;
    required uint32 req_source_type = 2; //  ESecurityOperSource
    required string mobile_phone = 3; //  发短信用的手机号，如果不填那么去查找用户当前绑定的手机号
    optional uint32 market_id = 4;   // 用来区分短信签名前缀
}

message ResetPasswrodResp {
    required BaseResp base_resp = 1;
}

// 解绑三方账户
message DetachThirdpartReq {
    required uint32 uid = 1;
    required uint32 req_source_type = 2; //  ESecurityOperSource
}
message DetachThirdpartResp {
    required BaseResp base_resp = 1;
}
// 解绑手机号
message UnbindPhoneReq {
    required uint32 uid = 1;
    required uint32 req_source_type = 2; //  ESecurityOperSource
}
message UnbindPhoneResp {
    required BaseResp base_resp = 1;
}

// 重置密保问题
message ClearSecurityQuestionReq {
    required uint32 uid = 1;
    required uint32 req_source_type = 2; //  ESecurityOperSource
}
message ClearSecurityQuestionResp {
    required BaseResp base_resp = 1;
}

// 关注/取关语音主播服务号
message FollowOrUnFollowYuYinFuWuHaoReq {
    required uint32 actor_uid = 1;
    optional bool is_follow = 2;
}
message FollowOrUnFollowYuYinFuWuHaoResp {
    required BaseResp base_resp = 1;
}

// 关注/取关公众号
message FollowPublicReq {
    enum PublicType {
        UNKNOWN = 0;
        SYSTEM = 1;
    }
    optional uint32 uid = 1;
    optional PublicType public_type = 2;
    optional uint32 binded_id = 3;
    optional bool is_follow = 4;
    optional bool with_welcome = 5;
}
message FollowPublicResp { 
    required BaseResp base_resp = 1;
}

//新的注销流程接口
message GetAutoProcUnregRecordReq {
    required uint32 status = 1;
    optional uint32 uid = 2;
    optional string ttid = 3;
    optional bool   sort = 4 ;   //true 为时间由近到远
    optional uint32 offset = 5;
    optional uint32 limit = 6;
}

message AutoProcUnregInfo {
    required uint32 id = 1;
    required uint32 uid = 2;
    required string ttid = 3;
    required uint32 status = 4;           //审核状态
    required uint32 apply_at = 5;         //提交时间
    required uint32 remain_wait = 6;   //剩余等待时间,秒
    optional bool   visit = 7;            //是否已回访
    optional string unreg_reason = 8 ;    //注销原因
}

message GetAutoProcUnregRecordRsp {
    required BaseResp base_resp = 1;
    repeated AutoProcUnregInfo info_list = 2;
    required uint32 total = 3;
}

//注销回访
message UpdateUnregVisitReq{
    required uint32 id =1;
    required bool visit =2 ;          //是否已回访
    required string unreg_reason = 3;  //注销原因
}

message UpdateUnregVisitRsp {
    required BaseResp base_resp = 1;
}

// 触发公会信息变更 sync notify通知
message GuildInfoUpdateSyncReq {
    enum NotifyType {
        ENUM_GROUP_SYNC = 1;   // 触发更新公会群组变化
        ENUM_GUILD_BASE_DATA_SYNC = 2;   // 触发更新公会基本信息变化
        ENUM_GUILD_ALL_MEMBER_SYNC = 4;   // 触发公会主群广播
    }
    optional uint32 notify_type = 1;
    optional uint32 guild_id = 2;
    optional uint32 group_id = 3;
}
message GuildInfoUpdateSyncResp {
}

// 封禁账号
message BanUserReq {
    optional string account = 1;
    optional string reason = 2;
    optional string operator_name = 3;
    optional int64  auto_recovery_at = 4;
    optional bool   with_device = 5;
    optional string proof_pic = 6;
    optional string reason_detail = 7;
}
message BanUserResp {
}

//获取用户信息
message GetVipLevelReq {
    required BaseReq base_req = 1;
    required uint32 uid = 2;
}

message GetVipLevelResp {
    required BaseResp base_resp = 1;
    required uint32 vip_level = 2;
    required uint64 rich_value = 3;
}

service apicenter{
    option( tlvpickle.Magic ) = 15300;		// 服务监听端口号
    // user
    rpc GetUserByUid( GetUserByUidReq ) returns ( GetUserByUidRsp ){
        option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetUserActivateInfo ( GetUserActivateInfoReq ) returns ( GetUserActivateInfoRsp ){
        option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid1,uid2,uid3,...>";
    }

    //用户成长
    rpc GetGrowthProfile ( GetGrowthProfileReq ) returns ( GetGrowthProfileRsp ) {
        option( tlvpickle.CmdID ) = 30;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    //红钻交易
    rpc TransactRedDiamond ( TransactRedDiamondReq ) returns ( TransactRedDiamondRsp ) {
        option( tlvpickle.CmdID ) = 31;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    //查询红钻交易是否存在
    rpc CheckRedDiamondTransact( CheckRedDiamondTransactReq ) returns ( CheckRedDiamondTransactRsp){
        option( tlvpickle.CmdID ) = 32;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc ConsumeRedDiamond ( ConsumeRedDiamondReq ) returns ( ConsumeRedDiamondRsp ) {
        option( tlvpickle.CmdID ) = 33;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc RewardRedDiamond ( RewardRedDiamondReq ) returns ( RewardRedDiamondRsp ) {
        option( tlvpickle.CmdID ) = 34;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetStockRedDiamond ( GetStockRedDiamondReq ) returns ( GetStockRedDiamondRsp ) {
        option( tlvpickle.CmdID ) = 35;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    // user online
    rpc KickoutUser ( KickoutUserReq ) returns ( KickoutUserResp ) {
        option( tlvpickle.CmdID ) = 51;
        option( tlvpickle.OptString ) = "u:s:";
        option( tlvpickle.Usage ) = "-u <uid> -s <reason>";
    }

    // channel
    rpc CreateChannel( CreateChannelReq ) returns ( CreateChannelRsp ){
        option( tlvpickle.CmdID ) = 101;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc DismissChannel( DismissChannelReq ) returns ( DismissChannelRsp ){
        option( tlvpickle.CmdID ) = 102;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc ModifyChannel( ModifyChannelReq ) returns ( ModifyChannelRsp ){
        option( tlvpickle.CmdID ) = 103;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc GetChannel( GetChannelReq ) returns ( GetChannelRsp ){
        option( tlvpickle.CmdID ) = 104;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc GetChannelGetPasswd( GetChannelPasswdReq ) returns ( GetChannelPasswdRsp ){
        option( tlvpickle.CmdID ) = 105;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc JoinChannel( JoinChannelReq ) returns ( JoinChannelRsp ){
        option( tlvpickle.CmdID ) = 106;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc QuitChannel( QuitChannelReq ) returns ( QuitChannelRsp ){
        option( tlvpickle.CmdID ) = 107;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc GetChannelMemberList( GetChannelMemberListReq ) returns ( GetChannelMemberListRsp ){
        option( tlvpickle.CmdID ) = 108;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc KickoutChannelMember( KickoutChannelMemberReq ) returns ( KickoutChannelMemberRsp ){
        option( tlvpickle.CmdID ) = 109;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc GetUserChannelId( GetUserChannelIdReq ) returns ( GetUserChannelIdRsp ){
        option( tlvpickle.CmdID ) = 110;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetUserAdminChannelList( GetUserAdminChannelListReq ) returns ( GetUserAdminChannelListResp ){
        option( tlvpickle.CmdID ) = 111;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    //push
    rpc PushWithUidList( PushWithUidListReq ) returns ( PushWithUidListRsp ){
        option( tlvpickle.CmdID ) = 210;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc PushWithMulticastRelation( PushWithMulticastRelationReq ) returns ( PushWithMulticastRelationRsp ) {
        option( tlvpickle.CmdID ) = 211;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc PushSystemMsgWithUidList( PushSystemMsgWithUidListReq ) returns ( PushSystemMsgWithUidListRsp ){
        option( tlvpickle.CmdID ) = 212;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    rpc PushSystemMsgWithMulticastRelation( PushSystemMsgWithMulticastRelationReq ) returns ( PushSystemMsgWithMulticastRelationRsp ) {
        option( tlvpickle.CmdID ) = 213;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    // 推送年度盛典榜单结算全服公告
    rpc PushYearActBreakingNews( PushYearActBreakingNewsReq ) returns ( PushYearActBreakingNewsRsp ) {
        option( tlvpickle.CmdID ) = 214;
        option( tlvpickle.OptString ) = "u:j:x:t:p:g:n:";
        option( tlvpickle.Usage ) = "-u<uid> -j<jump_url> -x<content> -t<view_type> -p<cp_uid> -g<guild_id> -n<guild_name>";
    }

    // 年度盛典活动弹窗
    rpc PushYearActPopUp( PushYearActPopUpReq ) returns ( PushYearActPopUpRsp ) {
        option( tlvpickle.CmdID ) = 215;
        option( tlvpickle.OptString ) = "u:j:x:t:e:p:";
        option( tlvpickle.Usage ) = "-u<uid> -j<jump_url> -x<content> -t<title> -e<expire_ts> -p<type>";
    }

    // 推送全网数据
    rpc PushMsgToAllChannels( PushMsgToAllChannelsReq ) returns ( PushMsgToAllChannelsRsp ) {
        option( tlvpickle.CmdID ) = 216;
        option( tlvpickle.OptString ) = "t:";
        option( tlvpickle.Usage ) = "t<ranking_type>";
    }

    // 推送全服公告
    rpc  PushCommonBreakingNews(  PushCommonBreakingNewsReq ) returns (  PushCommonBreakingNewsResp ){
        option( tlvpickle.CmdID ) = 217;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc SendImMsg ( SendImMsgReq ) returns ( SendImMsgRsp ){
        option( tlvpickle.CmdID ) = 230;
        option( tlvpickle.OptString ) = "u:t:m:b:";
        option( tlvpickle.Usage ) = "-u <uid> -t <target uid> -m <msg> -b <is present both>";
    }

    // 撤回IM消息
    rpc WithdrawImMsg ( WithdrawImMsgReq ) returns( WithdrawImMsgResp ) {
        option( tlvpickle.CmdID ) = 231;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    //guild
    //
    rpc GetGuild( GetGuildReq ) returns ( GetGuildRsp ){
        option( tlvpickle.CmdID ) = 241;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    rpc GetGuildMemberList( GetGuildMemberListReq ) returns ( GetGuildMemberListRsp ){
        option( tlvpickle.CmdID ) = 242;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }
    // 公会状态
    rpc GetGuildStatus( GetGuildStatusReq ) returns ( GetGuildStatusRsp ){
        option( tlvpickle.CmdID ) = 243;										// 命令号
        option( tlvpickle.OptString ) = "g:s";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-g <guild_id1,guild_id2,...>";
    }

    rpc GetGuildStarLevel ( GetGuildStarLevelReq ) returns ( GetGuildStarLevelRsp ) {
        option( tlvpickle.CmdID ) = 246;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    // 弃用，先屏蔽，看后续是否有外部调用
    //	rpc HcIncreaseMissionCount ( HcIncreaseMissionCountReq ) returns ( tlvpickle.SKBuiltinEmpty_PB ) {
    //        option( tlvpickle.CmdID ) = 250;
    //        option( tlvpickle.OptString ) = "u:i:";
    //        option( tlvpickle.Usage ) = "-u<uid> -i<missionid>";
    //    }

    // statis
    rpc PostHcConsume( PostHcConsumeReq ) returns ( PostHcConsumeResp) {
        option( tlvpickle.CmdID ) = 270;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    // 通知客户端 用户成长信息（经验/财富魅力值/红钻值）有变化，可以来sync了
    // 这个接口单独写 因为成长信息需要写入timeline 不仅仅是发一个通知
    rpc NotifyGrowInfoSync( NotifyGrowInfoSyncReq ) returns (NotifyGrowInfoSyncResp) {
        option( tlvpickle.CmdID ) = 280;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    // 通知客户端 用户某种类型有变化，可以来sync了
    // 仅仅是发一个sync通知给客户端
    rpc NotifySimpleSync( NotifySimpleSyncReq ) returns (NotifySimpleSyncResp) {
        option( tlvpickle.CmdID ) = 281;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }


    rpc AuditUnregApply( AuditUnregApplyReq ) returns ( AuditUnregApplyRsp ) {
        option( tlvpickle.CmdID ) = 290;
        option( tlvpickle.OptString ) = "a:u:t:n:";
        option( tlvpickle.Usage ) = "-a <apply_id> -u <uid> -t <op_type> -n <operator_id>";
    }

    rpc GetUnregApplyAuditRecord( GetUnregApplyAuditRecordReq ) returns ( GetUnregApplyAuditRecordRsp ) {
        option( tlvpickle.CmdID ) = 291;
        option( tlvpickle.OptString ) = "u:s:o:l:n:";
        option( tlvpickle.Usage ) = "-u <uid> -s <status> -o <offset> -l <limit> -n <operator_id>";
    }

    // User Account
    // 更新客户端配置文件
    rpc UpdateAppConfigFileSync( UpdateAppConfigFileSyncReq ) returns ( UpdateAppConfigFileSyncResp ) {
        option( tlvpickle.CmdID ) = 301;
        option( tlvpickle.OptString ) = "m:u";
        option( tlvpickle.Usage ) = "-f <fileName> -u <url> -m <md5> -x <platfrom all andirod ios>";
    }

    // 重置密码
    rpc ResetPasswrod( ResetPasswrodReq ) returns ( ResetPasswrodResp ) {
        option( tlvpickle.CmdID ) = 302;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid> ";
    }
    // 解绑三方账户
    rpc DetachThirdpart( DetachThirdpartReq ) returns ( DetachThirdpartResp ) {
        option( tlvpickle.CmdID ) = 303;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid> ";
    }

    // 解绑手机号
    rpc UnbindPhone( UnbindPhoneReq ) returns ( UnbindPhoneResp ) {
        option( tlvpickle.CmdID ) = 304;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid> ";
    }

    // 重置密保问题
    rpc ClearSecurityQuestion( ClearSecurityQuestionReq ) returns ( ClearSecurityQuestionResp ) {
        option( tlvpickle.CmdID ) = 305;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid> ";
    }

    // 推送2020白色情人节房间送礼弹窗消息
    rpc PushValentineActPopUp(PushValentineActPopUpReq)
        returns (PushValentineActPopUpResp) {
        option (tlvpickle.CmdID) = 306;
        option (tlvpickle.OptString) = "u:t:a:s:l:m:e:x:";
        option (tlvpickle.Usage) =
            "-u <uid> -t <target uid> -a <channel id> -s <sent ts> -l <lottie url> -m <lottie md5> -e <expire ts> -x <text>";
    }

    // 关注/取关公众号
    rpc FollowPublic( FollowPublicReq ) returns ( FollowPublicResp ){
        option( tlvpickle.CmdID ) = 309;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

    // 关注/取关语音主播会长服务号
    rpc FollowOrUnFollowYuYinFuWuHao( FollowOrUnFollowYuYinFuWuHaoReq ) returns ( FollowOrUnFollowYuYinFuWuHaoResp ){
        option( tlvpickle.CmdID ) = 310;
        option( tlvpickle.OptString ) = "u:b:";
        option( tlvpickle.Usage ) = "-u <actor_uid> -b<is_follow>";
    }

    //新的注销流程接口
    rpc GetAutoProcUnregRecord( GetAutoProcUnregRecordReq ) returns ( GetAutoProcUnregRecordRsp ) {
        option( tlvpickle.CmdID ) = 311;
        option( tlvpickle.OptString ) = "s:u:t:r:o:l";
        option( tlvpickle.Usage ) = "-s <status> -u <uid> -t <ttid> -r <sort> -o <offset> -l <limit>";
    }
    rpc UpdateUnregVisit(UpdateUnregVisitReq) returns (UpdateUnregVisitRsp) {
        option( tlvpickle.CmdID ) = 312;
        option( tlvpickle.OptString ) = "u:i:s:r";
        option( tlvpickle.Usage ) = "-u <uid> -i <id> -s <visit> -r <unreg_reason>";
    }

    // 推送房间消息
    rpc PushMsgToChannel(PushMsgToChannelReq) returns (PushMsgToChannelResp) {
        option( tlvpickle.CmdID ) = 313;
        option( tlvpickle.OptString ) = "u:i:s:r";
        option( tlvpickle.Usage ) = "-u <uid> -i <id> -s <visit> -r <unreg_reason>";
    }

    // 触发公会信息变更 sync notify通知
    rpc GuildInfoUpdateSync(GuildInfoUpdateSyncReq) returns (GuildInfoUpdateSyncResp) {
        option( tlvpickle.CmdID ) = 314;
        option( tlvpickle.OptString ) = "t:u:g:";
        option( tlvpickle.Usage ) = "-t <notify_type> -u <guild_id> -g <group_id>";
    }

    // 封禁账号
    rpc BanUser(BanUserReq) returns (BanUserResp) {
        option( tlvpickle.CmdID ) = 315;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }

    rpc GetVipLevel ( GetVipLevelReq ) returns ( GetVipLevelResp ) {
        option( tlvpickle.CmdID ) = 316;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
    }
}
