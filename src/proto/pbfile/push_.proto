syntax="proto2";

package ga;

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/push";

import "ga_base.proto";

//------------------------push message--------------------------------
message PushMessage{
    enum CMD_TYPE {
        PULL_LOG = 1;
        CIRCLE_UPDATE = 2;
        CHANNEL_MSG = 3;               // 房间带seq的广播消息 协议定义在 channel_.proto ChannelMsg
        BULLETIN = 5;                  //运维公告
        TT_ACTIVITY_MULTI_MESSAGE = 6;	// tt活动推送消息
        TT_COMMON_SYSTEM_MULTI_MESSAGE = 7;	// tt通用系统栏消息通知 TTCommonSystemMultiMessage
        RAID_RICHES_UPDATE = 8;
        CHANNEL_CONVENE = 9;	    // 频道召集
        ONLINE_EVENT_PUSH = 10;	    // 在线事件通知 包含的协议定义在 friendol_.proto OnlineEventPushMsg
        MESSAGE_READ_BY_PEER = 11;  // 消息被对方已读通知, 对应MessageReadByPeerMessage(in im.proto)

        CHANNEL_MSG_BRO = 12;   // 房间广播消息 协议定义在 channel_.proto ChannelBroadcastMsg
		USERRECOMMEND_MSG = 13;	// 用户推荐 消息推送
		PRESENT_MSG = 14;		// 用户收到礼物的个人消息推送

        TTLIVE_PUBLISHING_MSG = 15;  // 直播通知
		
		PRESENT_BREAKING_EVENT= 16;	 // 礼物大事件 礼物大喇叭 用于全网推送 ChannelPresentBreakingNews
		MY_HEADWEAR_MSG = 17;	     // 我的头像框装饰事件 当用户新获得头像框 或者 头像框升级时 推送给目标用户 MyHeadwareMsg
		COMMON_BREAKING_EVENT= 18;	 // 通用大事件 大喇叭 用于全网推送 CommonBreakingNews

        QUICK_MATCH_RESULT_MSG = 19; // 快速匹配结果通知 find_friends_.proto QuickMatchResultNotification

        TBEAN_UPDATE_MSG = 20;          // 用户T豆变化
		
		TT_NOTIFY_BAR_MESSAGE = 21;	// tt通知栏 通知 TTNotifyBarMessage

		GAME_RECRUIT_INVITE = 22;	//游戏招募邀请(content消息里面包含了team_.proto里 GameRecruitDetail定义的内容)

		GAME_RECRUIT_MEMBER_CHANGE = 23; // 队员变更通知 (content消息里面包含了team_.proto里 GameRecruitMemberChange定义的内容)

		GAME_RECRUIT_INTO_CHANNEL = 24; //  you房间跳转通知(content消息里面包含了team_.proto里 GameRecruitChannelInfo定义的内容)

        TBEAN_RECHARGE_MSG = 25;        // 首充活动

		COMMON_POP_UP_MSG = 26;	// 通用弹窗消息(content消息里面包含了 CommonPopUpMsg 定义的内容)
		
        DATING_BREAKING_EVENT= 27;	    // 相亲游戏大事件 礼物大喇叭 用于全网推送 DatingBreakingNews
        RICH_CHARM_RANKING_CHANGE = 28; // 我的 财务榜/魅力榜 变化 RankChangeMsg
		COMMON_BREAKING_EVENT_V2 = 29;	// 通用全服 大喇叭v2 用于全网推送 CommonBreakingNewsV2
		
		COMMON_WEB_ACTIIVE_BREAKING_REPORT = 30; // 全网活动 事件播报  CommonWebActiveBreakingReport
		COMMON_WEB_ACTIIVE_BREAKING_TOPN = 31;	 // 全网活动 排名播报  CommonWebActiveBreakingTopN
		
		MY_DECORATION_MSG = 32;	     // 我的房间装饰事件 当用户新获得房间坐骑时 推送给目标用户 MyDecorationMsg

        KNOCK_PUSH = 33;   //敲门时推送给客户端
    }
    required uint32 cmd = 1;
    required bytes content = 2;
    optional uint32 seq_id = 3;     // for Reliable Push
}


// Reliable Push
message MessagesReceivedAckReq {
    required BaseReq base_req = 1;
    repeated uint32 seq_id_list = 2;
}

message MessagesReceivedAckResp {
    required BaseResp base_resp = 1;
}

message PullOfflineMessagesReq {
    required BaseReq base_req = 1;
    required uint32 begin_seq_id = 2;//闭区间[begin, end]
    required uint32 end_seq_id = 3;//闭区间[begin, end]
}

message PullOfflineMessagesResp {
    required BaseResp base_resp = 1;
    repeated PushMessage messages = 2;
    required uint32 latest_seq_id = 3;
}

//////////////////////////////////////////////////////////
// Push Message
// cmd: PULL_LOG
message PullLogMessage {
    optional string start_time = 1;	// 时间区间起始
    optional string end_time = 2;	// 时间区间结束
    optional bool force = 3;        // 强制拉取
    optional bytes pull_log_context = 4;    // 客户端上报时原样带回
    optional string original_command = 5;   // 原始拉取命令
}

// 游戏圈有新信息流notify
// cmd: CIRCLE_UPDATE
message CircleUpdateMessage {
    required TopicUpdateInfo topic_update_info = 1 ;
}

//cmd: CHANNEL_MSG
//@see channel_proto # ChannelMsg 只是用于自己被踢出频道、踢下麦的通知


//cmd: BULLETIN
//
message BulletinMessage{
    enum BulletinType{
        SERVER_MAINTENANC_FINISH = 0; //服务器已完成维护
        SERVER_WILL_MAINTENANC   = 1; //服务器即将要进行维护
    }
    required uint32 type = 1;
    required uint32 eventtime = 2;      //事件唯一时间戳，同一时间戳表示同一事件
    optional string eventsubject = 3;		//运维公告标题摘要, type为1时才带有
    optional string eventurl = 4;		    //特别指定运维公告地址
}

//cmd: CHANNEL_CONVENE
//
message ChannelConveneMessage{
    enum ChannelConveneType{
        START_CONVENT = 1;	//开启频道召集(发给已在频道的用户)
        CANCEL_CONVENE = 2;	//取消频道召集(发给已在频道/已响应频道召集的用户)
        CONFIRM_COUNT = 3; 	//召集人数变化(发给已在频道的用户)

        USER_CONVENE = 10;	//用户被召集(发给已收藏频道的用户)
        USER_URGE = 11;		//用户被“催一下”(发给已收藏频道的用户)
    }

    required uint32 type = 1;	//ChannelConveneType
    required uint32 channel_id = 2;
    optional uint32 confirm_count = 3;	//已召集人数
    optional uint32 convene_ts = 4;	// 当次召集时间
    optional uint32 valid_convene_ts = 5;	//可发起召集时间
}

message TTActivityMessage {
    enum PLATFORM {
        ANDROID = 0;
        IOS = 1;
    }
    enum TYPE_MASK {
        NORMAL = 0;					// 普通状态
        DISAPPEAR_ONLY_CLICK = 1;	// 仅点击才会消失（滑动不消失）
    }
    required uint32 act_id = 1;
    required string act_title = 2;
    required string act_url = 3;
    optional uint32 platform = 4; // SEE PLATFORM
    optional uint32 type_mask = 5;
    optional string act_sub_title = 6;
    optional string act_pic_url = 7;
}

message TTActivityMultiMessage {
    repeated TTActivityMessage msg_list = 1;
}

message TTCommonSystemMultiMessage {
    repeated TTActivityMessage msg_list = 1;
}

message TTNotifyBarMessage {
	optional uint32 app_id = 1;  // see EAppID. ex: 0=TT 11=HC
    repeated TTActivityMessage msg_list = 2;
}

// 全服礼物大事件
message ChannelPresentBreakingNews {
    required uint32 from_uid        = 1;    // 送礼者
    required string from_account    = 2;
    required string from_nick       = 3;

    optional uint32 target_uid      = 4;   // 收礼者
    optional string target_account	= 5;
    optional string target_nick		= 6;  

	optional uint32 channel_id = 7;           // 涉及大事件的 房间ID
	optional uint32 channel_displayid = 8;    // 涉及大事件的 房间displya_ID
	optional uint32 channel_bindid = 9;       // 涉及大事件的 房间bind ID
	optional uint32 channel_type = 10;        // 涉及大事件的 房间类型
	optional string channel_name = 11;        // 涉及大事件的 房间名称
	
	optional string gift_name = 12;         // 涉及大事件的 礼物名称
	optional uint32 gift_id = 13;           // 涉及大事件的 礼物ID
	optional string news_prefix = 14;		// 涉及大事件的	文案前缀
	
	optional string from_face_md5 = 15;		// 送礼者的头像md5 如果为空 表明 这个用户没有设置头像
	optional string target_face_md5 = 16;	// 收礼者的头像md5 如果为空 表明 这个用户没有设置头像
	optional uint32 delay_secs = 17;		 // 延迟n秒后再播放大事件（客户端那边还没实现，坑货产品又说暂时不用做。。。）
}

// 我的头像装饰框消息（获得 or 升级）
message MyHeadwareMsg {

	required uint32 uid        = 1;   
	optional UserHeadwearInfo headwrar_info = 2;
	
}

// 全服通用大事件
message CommonBreakingNews {

    required string title		= 1;  // 标题
	optional string jump_url 	= 2;  // 跳转地址 

}

// 相亲全服大事件
message DatingBreakingNews 
{
    required uint32 from_uid        = 1; 
    required string from_account    = 2;
    required string from_nick       = 3;
	optional string from_face_md5   = 4;		// 头像md5 如果为空 表明 这个用户没有设置头像
	
	
    optional uint32 target_uid      = 5;   
    optional string target_account	= 6;
    optional string target_nick		= 7;  
	optional string target_face_md5 = 8;	// 头像md5 如果为空 表明 这个用户没有设置头像
	
	optional string dating_scene_name = 9;    // 相亲场景 
	
	optional uint32 channel_id = 10;           // 涉及大事件的 房间ID
	optional uint32 channel_displayid = 11;    // 涉及大事件的 房间displya_ID
	optional uint32 channel_bindid = 12;       // 涉及大事件的 房间bind ID
	optional uint32 channel_type = 13;        // 涉及大事件的 房间类型
	optional string channel_name = 14;        // 涉及大事件的 房间名称
	optional uint32 delay_secs = 15;		 // 延迟n秒后再播放大事件（客户端那边还没实现，坑货产品又说暂时不用做。。。）
}


// 通用全服大事件
message CommonBreakingNewsV2 
{
    optional uint32 from_uid        = 1; 
    optional string from_account    = 2;
    optional string from_nick       = 3;
	optional string from_face_md5   = 4;	  // 头像md5 如果为空 表明 这个用户没有设置头像
	
	optional uint32 channel_id = 5;           //  涉及大事件的 房间ID
	optional uint32 channel_displayid = 6;    //  涉及大事件的 房间displya_ID
	optional uint32 channel_bindid = 7;       //  涉及大事件的 房间bind ID
	optional uint32 channel_type = 8;         //  涉及大事件的 房间类型
	optional string channel_name = 9;         //  涉及大事件的 房间名称
	
	optional string news_prefix = 10;		 // 涉及大事件的 文案前缀
	optional string news_content = 11;		 // 涉及大事件的 文案	
	
	optional string jump_url = 12;           // 跳转URL
	optional uint32 delay_secs = 13;		 // 延迟n秒后再播放大事件（客户端那边还没实现，坑货产品又说暂时不用做。。。）
}

message TBeanUpdateMsg{
    required uint32 uid     = 1;
    optional uint32 amount   = 2; // T豆数量
}

message RankChangeMsg {
    enum RankChangeTypeMask {
        DAY_CHARM = 1;
        DAY_RICH = 2;
    }
    required uint32 type = 1; // mask
}

// 通用弹窗消息
message CommonPopUpMsg{
	enum POP_UP_MSG_TYPE {
        MISSION_DAILY_CHECKIN = 1;		// 每日签到任务
    }

    required uint32 uid     = 1;
	required uint32 msg_type = 2;			// POP_UP_MSG_TYPE
    required string title   = 3; 			// 标题
	optional string award_icon_url = 4; 	// 标题下面的图标url
	optional string award_msg_up = 5;		// 奖励描述1
	optional string award_msg_down = 6;		// 奖励描述2
	optional string bonus_award_msg = 7;	// 额外的奖励描述(经验奖励下面的那行黄字)

	// 另外的特殊奖励（如每日签到的连签奖励）
	optional string extra_award_msg = 8;		// 特殊奖励的描述(已废弃)
	optional string extra_award_icon_url = 9;	// 特殊奖励的图标url(已废弃)
	optional uint32 extra_award_count = 10;		// 特殊奖励的数量(已废弃)
	optional uint32 extend_value = 11;			// (已废弃)
	
	optional string main_page_icon_url = 12;	// 主页图标url（每日签到中，先展示主页，用户点击后再显示奖励页）
	optional string background_url = 13;		// 背景url
	optional string award_button_url = 14;	// 奖励按钮url
	
	// 原有的奖励描述要加上颜色。。。
	optional string award_msg_up_color = 15;		// 奖励描述1
	optional string award_msg_down_color = 16;		// 奖励描述2
	optional string bonus_award_msg_color = 17;		// 额外的奖励描述
}

message ActiveBreakingReportMsg 
{
	optional uint32 timestamp = 1;
	optional string msg = 2;       // 

}
message CommonWebActiveBreakingReport 
{
	repeated ActiveBreakingReportMsg msg_list = 1;       // 
	
	optional string back_img_url = 2;       // 底图URL
	optional string icon_img_url = 3;       // ICON URL

	optional string jump_url = 4;           // 跳转URL
}

message CommonWebActiveBreakingTopN
{
	repeated GenericMember topn_member_list = 1;      // 
	optional string back_img_url = 2;       // 底图URL
	optional string icon_img_url = 3;       // ICON URL
	
	optional string jump_url = 4;           // 跳转URL
}

message GetCommonWebActiveBreakingInfoReq {
	required BaseReq base_req = 1;
}

message GetCommonWebActiveBreakingInfoResp {
	required BaseResp base_resp = 1;
	optional CommonWebActiveBreakingReport report_info = 2;
	optional CommonWebActiveBreakingTopN rank_topn_info = 3;
}

message MyDecorationMsg
{
	required uint32 uid = 1;
	optional UserDecorationInfo user_decoration = 2;
	optional uint32 rich_level = 3;
}
message AckNotificationReq {
    enum Event {
        ARRIVAL = 0;    //消息到达传
        CLICK = 1;      //消息到达点击传
    }
    required BaseReq base_req = 1;
    required string task_id = 2;        //推送消息体带的task id
    required Event event = 3;
    optional string device_id = 4;      //
}

message AckNotificationResp {
    required BaseResp base_resp = 1;
}