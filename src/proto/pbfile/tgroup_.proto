syntax="proto2";

package ga;
import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/group";

//-------------------群组定义----v1.8.0----------------------------------

message TGroupType {
    enum Types {
        GAME_HOBBY = 1;     // 游戏兴趣群
        GAME_OFFICIAL = 2;  // 游戏的官方T群
    }
}

message Location {
    required string code = 1;
    required string city = 2;
}

message TGroupSimpleInfo {
    required uint32 group_id              = 1;      // 群id
    required string group_name            = 2;		// 群名称
    required string portrait_md5          = 3; 		// 群头像
    required uint32 group_mem_count       = 4;		// 群成员数
    required uint32 group_mem_count_limit = 5;		// 群成员人数上限
    required string group_owner_account   = 6;		// 群主account
    required string group_desc            = 7;		// 群简介
    required string group_game_name       = 8;      // 群游戏名字
    required string group_location_code   = 9;      // 群绑定城市位置码
    optional uint32 group_type            = 10;     // see TGroupType
}

message TGroupInfo {
    required TGroupSimpleInfo simple_info = 1;      // 群简单信息
    required uint32 create_time           = 2; 		// 创建时间
    required uint32 group_number          = 3;      // 群号码
    required string group_name_pinyin     = 4;		// 群名称拼音
    required uint32 group_game_id         = 5;      // 群游戏id
    required string my_group_card         = 6;      // 我的群名片
    required string group_city            = 7;      // 群绑定城市
    repeated string group_admin_account   = 8;      // 群管理员列表
}

message GameSimpleInfo {
    required uint32 game_id = 1;
    required string game_name = 2;
    required string portrait_url = 3;
}

message TGroupMemberInfo {
    enum Role {
        OWNER = 1;		// 群主
        ADMIN = 2;      // 管理员
        COMMON = 3;		// 普通成员
    }
    required string account     = 1;
    required uint32 sex         = 2;
    required string user_nick   = 3;
    required string nick_pinyin = 4;
    required uint32 role        = 5;
    required string face_md5    = 6;	// 头像md5
    required bool is_muted      = 7;    //用户是否被禁言
    required uint32 uid         = 8;
	
	optional uint32 online_status      = 9;    // 在线状态 0 离线 1在线
    optional uint32 online_status_ts   = 10;   // 在线状态变化的时间 只有离线用户才会有值 表示最后离线的时间
}

message TGroupDetailInfo {
    required uint32 group_id              = 2;      // 群id
    required string group_name            = 3;		// 群名称
    required uint32 group_mem_count       = 4;		// 群成员数
    required uint32 group_mem_count_limit = 5;		// 群成员人数上限
    required string group_owner_account   = 6;		// 群主account
    required string group_desc            = 7;		// 群简介
    required string group_game_name       = 8;      // 群游戏名字
    required string group_location_code   = 9;      // 群绑定城市位置码
    repeated string group_admin_account   = 10;     // 群管理员列表
    required string portrait_md5          = 11; 	// 群头像
    required uint32 create_time           = 12; 	// 创建时间
    required uint32 group_number          = 13;     // 群号码
    required string group_name_pinyin     = 14;		// 群名称拼音
    required uint32 group_game_id         = 15;     // 群游戏id
    required string my_group_card         = 16;     // 我的群名片
    required string group_city            = 17;     // 群绑定城市
    required string group_owner_nickname  = 18;		// 群主名称
    required string group_owner_guild_name= 19;		// 群主所在的公会名
    required GrowInfo group_owner_growinfo= 20;		// 群主成长信息
    optional uint32 need_verify		 	  = 21;	 	// 是否需要验证
    optional uint32 group_type            = 22;     // see TGroupType
	optional uint32 all_mute			  = 23;		// 是否全员禁言
}

//群组创建条件 BEGIN
message TGroupCreatePermissionReq {
    required BaseReq base_req = 1;
}

message TGroupCreatePermissionResp {
    required BaseResp base_resp           = 1;
    required uint32 create_group_capacity = 2;   //还能建几个群
    optional uint32 max_member            = 3;  //最大人数
}
//群组创建条件 END

//搜索群组 BEGIN

message TGroupSearchResult {
	required string reason = 1;
	repeated TGroupSimpleInfo group_list = 2;
}

message SearchTGroupReq {
    required BaseReq base_req = 1;
    required string key_word  = 2; 	// 游戏名称(模糊搜索)/群号码(精确匹配)
    required Location location = 3;	// 地理位置
}

message SearchTGroupResp {
	enum RESULT_TYPE {
		NONE = 0;			// 无结果
		GAME_LIST = 1;		// 游戏列表
		TGROUP_LIST = 2;	// 群组列表
		TGROUP_DETAIL = 3;	// 群组详情页面
	}
    required BaseResp base_resp             = 1;
    required uint32 result_type				= 2;
    repeated SimpleGame game_list 			= 3;
    repeated TGroupSearchResult group_list	= 4;
    optional TGroupDetailInfo group_detail	= 5;
    required string key_word_match			= 6;	// 匹配到多个结果以上,返回req.key_word；匹配到单个结果，则自动补全匹配结果（比如只搜索到一个游戏，则补全为游戏名）
}

message SearchTGroupByGameIdReq {
	required BaseReq base_req = 1;
	required uint32 game_id = 2;
    required Location group_location = 3;    //用户所在位置
}

message SearchTGroupByGameIdResp {
	required BaseResp base_resp = 1;
	repeated TGroupSearchResult group_list = 2;
}


//搜索群组 END

//热门游戏列表 BEGIN
message HotGameListReq{
    required BaseReq base_req = 1;
}

message HotGameListResp{
    required BaseResp base_resp       = 1;
    repeated GameSimpleInfo game_list = 2;   //煎蛋版本的游戏
}
//热门游戏列表 END

//创建群组 BEGIN
message CreateTGroupReq {
    required BaseReq base_req        = 1;
    required string group_name       = 2;    //群名称
    required uint32 game_id          = 3;    //绑定游戏id
    required string group_desc       = 4;    //群描述
    required Location group_location = 5;    //群所在位置
    required uint32 need_verify		 = 6;	 //是否需要验证
}

message CreateTGroupResp {
    required BaseResp base_resp    = 1;
    required TGroupInfo group_info = 2;
    required string create_message = 3;		// 创建群成功的文案
}

//创建成功后再上传群头像
//创建群组 END

//群组推荐 BEGIN
message TGroupRecommendedReq {
    required BaseReq base_req  = 1;
    required Location location = 2;
    repeated uint32 game_id_list = 3;
}

message TGroupRecommend {
	required string reason = 1;
	repeated TGroupSimpleInfo group_list = 2;
}

message TGroupRecommendedResp {
    required BaseResp base_resp              = 1;
    repeated TGroupRecommend recommend_list  = 2;
}
//群组推荐 END

//加入群组 BEGIN
message JoinTGroupReq {
    enum JOIN_FROM_TYPE {
        FROM_SERACH          = 1;
        FROM_CITY_RECOMMEND  = 2;
        FROM_OTHER_RECOMMEND = 3;
    }
    required BaseReq base_req      = 1;
    required string group_account  = 2;
    required string verify_msg     = 3;
    required uint32 come_from_type = 4;   //加群来源: 搜索 1 /推荐：同城 2/其他 3
}

message JoinTGroupResp {
    required BaseResp base_resp = 1;
}
//加入群组 END

//退出群组 BEGIN
message QuitTGroupReq {
    required BaseReq base_req     = 1;
    required string group_account = 2;
}

message QuitTGroupResp {
    required BaseResp base_resp   = 1;
    required string group_account = 2;      //todo 退出之后是否要删UI？
}
//退出群组 END

//拉人进群组 BEGIN
message TGroupAddMemberReq {
    required BaseReq base_req       = 1;
    required string group_account   = 2;
    repeated string target_account  = 3;   //进群朋友的account
}

message TGroupAddMemberResp {
    required BaseResp base_resp          = 1;
    required string group_account        = 2;
    repeated string success_account_list = 3;
    repeated string failed_account_list  = 4;
}
//拉人进群组 END

//踢人出群组 BEGIN
message TGroupRemoveMemberReq {
    required BaseReq base_req       = 1;
    required string group_account   = 2;
    required string target_account  = 3;   //进群朋友的account
}

message TGroupRemoveMemberResp {
    required BaseResp base_resp     = 1;
    required string group_account   = 2;
    required string target_account  = 3;
}
//踢人出群组 END

// 修改群名称 BEGIN
message TGroupModifyNameReq {
    required BaseReq base_req     = 1;
    required string group_account = 2;
    required string group_name    = 3;
}

message TGroupModifyNameResp {
    required BaseResp base_resp   = 1;
    required string group_account = 2;
    required string group_name    = 3;
}
// 修改群名称 END

//获取群成员列表 BEGIN
message TGroupGetMemberListReq {
    required BaseReq base_req     = 1;
    required string group_account = 2;
	
	optional uint32 begin_postion      = 3;    // 
    optional uint32 count   = 4;    // 

    optional bool admin_only = 5;   // 拉所有管理员
}

message TGroupGetMemberListResp {
    required BaseResp base_resp       = 1;
    required string group_account     = 2;
    repeated TGroupMemberInfo members = 3;
}
//获取群成员列表 END

//审批用户进群 BEGIN
message TGroupApproveReq {
    required BaseReq base_req     = 1;

    required uint32 apply_id      = 3;
    required bool   accept        = 4;
    required string apply_account = 5;
}

message TGroupApproveResp {
    required BaseResp base_resp   = 1;

    required uint32 apply_id      = 3;
    required bool   accept        = 4;
    required string apply_account = 5;
}
//审批用户进群 END


// 查群组详情
message TGroupGetDetailInfoReq {
	required BaseReq base_req 	= 1;
	required uint32 group_id 	= 2;
}

message TGroupGetDetailInfoResp
{
	required BaseResp base_resp 		  = 1;
	required TGroupDetailInfo detail_info = 2;
}

// 解散群组
message TGroupDismissReq {
	required BaseReq base_req		= 1;
	required string group_account	= 2;
}

message TGroupDismissResp {
	required BaseResp base_resp		= 1;
	required string group_account	= 2;
}

// 修改群名片
message TGroupModifyMyCardReq {
	required BaseReq base_req		= 1;
	required string group_account	= 2;
	required string group_card		= 3;
}

message TGroupModifyMyCardResp {
	required BaseResp base_resp		= 1;
	required string group_account	= 2;
	required string group_card		= 3;
}

// 修改群描述(描述 地理位置 游戏)
// 被修改的数据不能为空 为空或者字符串为空字符表示不修改
message TGroupModifyGroupDescReq {
	enum TGROUP_MODIFY_TYPE
	{
		TGROUP_MODIFY_DESC = 1;
		TGROUP_MODIFY_GAME = 2;
		TGROUP_MODIFY_LOCATION = 3;
		TGROUP_MODIFY_GAME_AND_LOCATION = 4;
	}
	required BaseReq base_req		= 1;
	required string group_account	= 2;
	required string group_desc		= 3;
	
	optional Location group_location = 4;    //群所在位置
	optional uint32 game_id          = 5;    //绑定游戏id
	optional uint32 modify_type      = 6;    //TGROUP_MODIFY_TYPE 仅做上下文判断，服务器不使用该字段
}

message TGroupModifyGroupDescResp {
	required BaseResp base_resp		= 1;
	required string group_account	= 2;
	required string group_desc		= 3;
	
	optional Location group_location = 4;    //群所在位置
	optional uint32 game_id          = 5;    //绑定游戏id
	optional string game_name        = 6;    //绑定游戏name
	optional uint32 modify_type    = 7;    //TGROUP_MODIFY_TYPE 从resp中原样带回
}

// 搜索游戏圈游戏
message TGroupSearchCircleGameReq {
	required BaseReq base_req		= 1;
	required string game_name		= 2;
}

message TGroupSearchCircleGameResp {
	required BaseResp base_resp			= 1;
	repeated GameSimpleInfo game_list	= 2;
}

message TGroupSetNeedVerifyReq {
	required BaseReq base_req		= 1;
	required string tgroup_account	= 2;
	required uint32 need_verify		= 3;
}

message TGroupSetNeedVerifyResp {
	required BaseResp base_resp		= 1;
	required string tgroup_account	= 2;
	required uint32 need_verify		= 3;
}

message TGroupMuteMemberReq{
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
    repeated uint32 uids = 3;
}

message TGroupMuteMemberResp{
    required BaseResp base_resp = 1;
    required uint32 group_id = 2;
    repeated uint32 uids = 3;
}

message TGroupUnmuteMemberReq{
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
    repeated uint32 uids = 3;
}

message TGroupUnmuteMemberResp{
    required BaseResp base_resp = 1;
    required uint32 group_id = 2;
    repeated uint32 uids = 3;
}

message TGroupAddAdminReq {
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
    repeated uint32 uids = 3;
}

message TGroupAddAdminResp {
    required BaseResp base_resp = 1;
    required uint32 group_id = 2;
    repeated uint32 uids = 3;
}

message TGroupRemoveAdminReq {
    required BaseReq base_req = 1;
    required uint32 group_id = 2;
    repeated uint32 uids = 3;
}

message TGroupRemoveAdminResp {
    required BaseResp base_resp = 1;
    required uint32 group_id = 2;
    repeated uint32 uids = 3;
}

message TGroupSetAllMuteReq {
	required BaseReq base_req = 1;
	required uint32 group_id = 2;
	required bool is_all_mute = 3;
}


message TGroupSetAllMuteResp {
	required BaseResp base_resp = 1;
	required uint32 group_id = 2;
	required bool is_all_mute = 3;
}


message TGroupGetMuteListReq {
    required BaseReq base_req     = 1;
    required uint32 group_id = 2;
}

message MuteUserInfo {
	required uint32 uid = 1;
	required string account = 2;
	required string nickname = 3;
}

message TGroupGetMuteListResp {
    required BaseResp base_resp   = 1;
    required uint32 group_id = 2;
    repeated MuteUserInfo mute_list = 3;     // 禁言uid列表

}

//邀请人进群组 BEGIN
message TGroupInviteMemberReq {
    required BaseReq base_req       = 1;
    required string group_account   = 2;
    repeated string target_account  = 3;   //进群朋友的account
}

message TGroupInviteMemberResp {
    required BaseResp base_resp          = 1;
    required string group_account        = 2;
    repeated string success_account_list = 3;
    repeated string failed_account_list  = 4;
}

//被邀请人同意进群协议
message TGroupAcceptJoinReq{
    required BaseReq base_req     = 1;
    required string group_account   = 2;
    required string  verify_info     = 3;    //来自邀请消息，校验是否受到邀请
}

message TGroupAcceptJoinResp{
    required BaseResp base_resp   = 1;
}

//服务端用，非客户端协议
message TGroupInviteVerifyInfo{
    required uint32 version = 1;
    required string  info    = 2;    // 加密过的TGroupInviteMsg
}

//服务端用，非客户端协议
message TGroupInviteMsg{
    required uint32 uid = 1;            //受邀请人uid
    required uint32 inviter_uid = 2;    //邀请人uid
    required uint32 group_id = 3;       //群组id
    required uint32 timestamp = 4;      //邀请时间戳
}
