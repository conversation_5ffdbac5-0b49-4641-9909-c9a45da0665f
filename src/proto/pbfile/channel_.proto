syntax="proto2";

package ga;

import "ga_base.proto";

option java_package ="com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channel";

//-------------语音频道---------------------//

enum ChannelMsgType {
    CHANNEL_TEXT_MSG = 1;   // 纯文本 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelImOpt 信息）
    CHANNEL_ENTER_MSG = 2;  // 加入房间 消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelEnterOpt 信息
    CHANNEL_EXIT_MSG = 3;   // 退出房间 消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelExitOpt 信息

    EN_MIC_QUEUE = 4;       // 上麦  （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelMicOpt 信息）
    DE_MIC_QUEUE = 5;       // 下麦  （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelMicOpt 信息）
    CHANNEL_AIR_TICKET = 6; // 频道飞机票
	CHANNEL_IMAGE_MSG = 7;     // 图片消息 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelImOpt 信息）
	CHANNEL_EMOTICON_MSG = 8;    // 表情消息
	CHANNEL_HTML_MSG = 9;        // html文本 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelImOpt 信息）

    //如果push没收到，一般是客户端断开连接了，重连之后马上请求一次GetChannelMsgReq，如果服务器发现客户端不在频道了会通过错误码告诉客户端
    CHANNEL_KICKED = 10;        // 被踢出频道
    CHANNEL_KICKED_MIC = 11;    // 被踢下麦位 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelMicOpt 信息）
    CHANNEL_DISMISS = 12;       // 频道被解散
	CHANNEL_MIC_ENTRY_DISABLE = 13;   // 麦位设置为禁用状态 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelMicOpt 信息）
	CHANNEL_MIC_ENTRY_ENABLE = 14;    // 麦位设置为正常状态 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelMicOpt 信息）

	CHANNEL_CONFIG_MODIFY_MSG = 15;         // 房间配置信息有变化 包括 房间名称 密码 麦模式 房间图标 房间描述 等配置 具体内容参见 ChannelMsgSubType 此时ChannelMsg的content字段为JSON格式
	CHANNEL_MUSIC_INFO = 16;                // 房间音乐信息 此时ChannelMsg的content字段为JSON格式
	CHANNEL_FREE_MIC_MODE_MUTE_USER = 17;   // 房间自由模式时 禁止某个用户发言
	CHANNEL_SEND_PRESENT = 18;	// 赠送礼物

	CHANNEL_TEXT_SYS_MSG = 19;	// 纯文本系统消息

	CHANNEL_MUSIC_LIST_CHANGED = 20;		// 音乐列表变化
	CHANNEL_MUSIC_VOLUME_CHANGED = 21;		// 音量变化
	CHANNEL_MUSIC_USER_PLAY_MUSIC = 22;		// 指定用户播某个歌曲

	CHANNEL_FUN_GAME_RESULT_MSG=23;	//房间小游戏消息

	CHANNEL_MUSIC_CAN_SHARE_CHANGED = 24;	// 房间音乐可贡献开关变化

	CHANNEL_PRESENT_MSG = 25;	              // 房间礼物消息
    CHANNEL_MIC_ENTRY_MUTED = 26;             // 麦位设置为闭麦状态（闭麦状态为可以上麦但是不能说话） att_content 字段为全量麦位json字段
	CHANNEL_MEMBER_LIST_SORT_CHANGED = 27;    // 房间内"在线成员的排序值"有变化
	CHANNEL_ADMIN_MEMBER_CHANGED = 28;        // 个人房房间管理员有变化  可能是增加或者移除
	CHANNEL_MAGIC_EXPRESSION_MSG = 29;        // 房间麦上魔法表情消息
	CHANNEL_CHANGE_MIC_POS = 30;              // 用户换了个麦位 (消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelChangeMicPosOpt 信息)
	CHANNEL_TAKE_USER_HOLD_MIC = 31;          // 让指定用户持有指定麦 (消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelMicOpt 信息)

	CHANNEL_MUSIC_SOMEONE_PLAY_MUSIC = 32;	  // 同22功能,区别是广播消息

	CHANNEL_MUSIC_FREE_MODE_CHANGED = 33;	  // 自由模式变化
	CHANNEL_MEMBER_OPT_INFO_CHANGED = 34;	  // 用户信息变化 一般是土豪魅力等级升级 消息的opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelMemberOpt 信息
	CHANNEL_WARNING = 35;	                  // 房间警告信息 警告的内容字符串放在content字段

	CHANNEL_USER_HEADWEAR_CHANGED = 36;	      // 用户头饰变化 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelUserHeadwearOpt 信息）

	CHANNEL_TRIVIA_GAME_TIMER = 37;	           // 房间知识问答游戏定时器动画 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的ChannelTriviaGameTimerOpt信息）
    CHANNEL_TRIVIA_GAME_ANIMATION = 38;        // 房间知识问答游戏的动画 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的ChannelTriviaGameAnimationOpt信息)
	CHANNEL_TRIVIA_GAME_QUESTION = 39;         // 房间知识问答游戏 的 出题 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的ChannelTriviaGameQuestionOpt 信息）
	CHANNEL_TRIVIA_GAME_WINNER = 40;	       // 房间知识问答游戏 的 答案 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的ChannelTriviaGameWinnerListOpt信息）
    CHANNEL_TRIVIA_GAME_PHASE_FORWARD = 41;    // 房间知识问答游戏 推送给主持人的阶段变化 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的ChannelTriviaGamePhaseUpdateOpt信息）
    CHANNEL_TRIVIA_GAME_END = 42;              // 房间知识问答游戏 通知客户端踢用户出房间

	CHANNEL_CONNECT_MIC_APPLY_MSG  = 43;       // 游客发起连麦请求（包括自己申请连麦和自己取消连麦）（消息的pb_opt_content字段中会携带 channel_opt_.proto ChannelLiveConnectMicApplyOpt 信息 ）
	CHANNEL_CONNECT_MIC_HANDLE_MSG = 44;       // 主播对连麦请求回应（包括同意连麦和拒绝连麦）（消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelLiveConnectMicHandleOpt 信息 ）
	CHANNEL_CONNECT_MIC_APPLY_EXPIRE_MSG = 45; // 系统对房间内连麦请求进行超时处理（消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelLiveConnectMicApplyExpireOpt 信息 ）

	CHANNEL_GUILD_BULLETIN_CREATE_MSG = 46;    //   当公会有新公告发布时 公会主房间会收到一条推送 （消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelGuildBulletinOpt 信息 ）

	CHANNEL_GAME_RECRUIT_PUBLISH = 47;		   //  推送游戏招募消息（消息的pb_opt_content字段中会携带 team_.proto 中定义的 GameRecruitDetail 信息 ）

	CHANNEL_LIVE_FIN_MSG = 48;                 //  表明上一轮直播结束 仅在直播麦位模式下 才有可能发送
	CHANNEL_GUILD_MEMBER_JOIN_MSG = 49;        //  当公会有新人加入的时候 公会主房间会收到一条推送

	CHANNEL_DATING_GAME_HAT = 50;	        // 相亲房 用户获得帽子 消息的 pb_opt_content 字段中会携带 channel_dating_game_.proto 中定义的 GetDatingGameHatUser 信息 ）
	CHANNEL_DATING_GAME_VIP_HOLD_MIC = 51;	// 相亲房 VIP麦位上的用户更新
	CHANNEL_DATING_GAME_LIKE_BEAT_VAL = 52;	// 相亲房 麦位上用户心动值更新
	CHANNEL_DATING_GAME_SELECT_STATUS = 53; // 相亲房 选择状态更新
	CHANNEL_DATING_GAME_OPEN_LIKE_USER = 54;// 相亲房 公布心动对象
	CHANNEL_DATING_GAME_APPLY_MIC = 55;		// 相亲房 连麦位申请
	CHANNEL_DATING_GAME_VIP_MSG = 56;	    // 相亲房的可上vip麦位通知 协议定义在 channel_dating_game_.proto DatingGameVipMsg
	CHANNEL_DATING_SHOWOFF = 57;		    // 公布结果后的，动画效果推送协议定义在 channel_dating_game_.proto DatingGameShowoff

	CHANNEL_DATING_PHASE_CHANGE = 58;		// 相亲活动 阶段变化 ，pb_opt_content 协议定义在 channel_dating_game_.proto DatingGamePhasePushNotifyOpt

    CHANNEL_PRESENT_COUNT_CHANGE = 59;          // 房间送礼统计 推送新的统计值（麦上用户收到礼物时推送，以及刚上麦的用户会推送） 推送协议定义在 channel_opt_.proto ChannelPresentCountNotifyMsg
    CHANNEL_PRESENT_COUNT_STATUS_CHANGE = 60;   // 房间送礼统计开关，开或关 pb_opt_content 协议定义在 channel_opt_.proto ChannelPresentCountStatusMsg
	
	CHANNEL_MEMBER_VIP_LEVEL_NOTIFY = 61;       // 房间成员VIP等级提醒 推送协议定义在 channel_opt_.proto ChannelMemberVipNotifyOpt

	CHANNEL_BATCH_SEND_PRESENT_NOTIFY = 62;	    // 批量赠送礼物附加信息 消息的content字段中会携带userpresent_.proto中定义的PresentBatchInfoMsg

	CHANNEL_MEMBER_VIP_LEVEL_UPGRADE = 63;      // 房间成员VIP等级升级 推送协议定义在 channel_opt_.proto ChannelMemberVipNotifyOpt
	
	CHANNEL_PRESENT_RUNWAY_ROCKET_CHANGE = 64;		// 房间礼物跑道的火箭变化
	CHANNEL_PRESENT_RUNWAY_ROCKET_MSG = 65;			// 房间礼物跑道的火箭跑道公屏消息
	
	CHANNEL_POP_UP_MSG = 66;					// 房间弹窗消息 推送协议定义在 channel_opt_.proto  ChannelPopUpMsgOpt
	
    CHANNEL_2019NEWYEAR_BEAT_AWARD = 67;        // 房间打地鼠游戏 发奖    推送协议定义在 activity_.proto NewYear2019BeatAward
    CHANNEL_2019NEWYEAR_BEAT_UNAWARD = 68;      // 房间打地鼠游戏 未中奖  推送协议定义在 activity_.proto NewYear2019BeatAward 
	
	CHANNEL_PK_CHANGED = 69;      //  房间PK，排名有变化（包括开始 结束 和 有变化）   推送协议 ChannelVotePKPushNotifyInfo
	CHANNEL_DRAW_GAME  = 70;      //房间画布推送（添加线，取消线，删除画 推送协议） ChannelDrawGamePushNotifyInfo
	
	CHANNEL_NORMAL_QUEUE_UP_MIC = 71; // 普通房间的排麦变化事件（消息的pb_opt_content字段中会携带 channel_opt_.proto 中定义的 ChannelQueueUpMicOpt 信息 ）
	CHANNEL_COLOR_NOTIDY_MSG = 72; //房间有颜色配置公屏消息

	CHANNEL_CREATE_TOPIC_CHANNEL = 73;		// 创建主题房时公屏消息 推送协议定义在 channel_opt_.proto 中定义的 CreateTopicChannel

	CHANNEL_MODIFY_MSG = 74; //更改客户端的房间消息

	TOPIC_CHANNEL_HIDDEN = 75; //主题房不在大厅展示了的公屏消息，推送协议定义在 channel_opt_.proto 中定义的 HideTopicChannelMsg

	KNOCK_CHANNEL_RESULT = 76; //处理敲门返回给公屏的结果信息 knocklogic_.proto 中定义的 ScreenPush
	
	CHANNEL_DO_MUTE   = 77; // 禁言
	CHANNEL_UNDO_MUTE = 78; // 解除禁言

	CHANNEL_CURRENT_BACKGROUND_CHANGE = 79; // 当前房间背景变化  推送协议见 channelbackgroundlogic.proto 中的 ChannelBackgroundInfo

	CHANNEL_DRESS_CHANGE = 80; // 房主切换会员套装  推送协议定义在 channel_opt_.proto  ChannelDressChangeNofityOpt

}

// 房间类型
enum ChannelType {
	INVALID_CHANNEL_TYPE = 0; // 无效值
    GUILD_TYPE = 1;          // 被公会绑定的房间 = 普通公会房
    FREE_TYPE = 2;           // 自由房间         = 欢城房间
	USER_CHANNEL_TYPE = 3;   // 被用户绑定的房间 = 个人房
	GUILD_PUBLIC_FUN_CHANNEL_TYPE = 4;   // 被公会绑定的公开娱乐房间
	TRIVIA_GAME_CHANNEL_TYPE = 5;   // 被用户绑定的 知识问答游戏房 (只能使用 单麦位模式)
	TEMP_KH_CHANNEL_TYPE = 6;       // 临时开黑房间(只能使用 高音质开黑麦位模式)
	RADIO_LIVE_CHANNEL_TYPE = 7;    // 电台直播房间(只能使用 电台麦位模式)
	GUILD_HOME_CHANNEL_TYPE = 8;    // 公会 主房间(只能使用 电台麦位模式)
	OFFICIAL_LIVE_CHANNEL_TYPE = 9; // 官方语音直播频道
}

enum ChannelMsgSubType {
    CHANNEL_MSG_SUB_CHANGE_NAME = 1;
	CHANNEL_MSG_SUB_CHANGE_PASSWORD = 2;
	CHANNEL_MSG_SUB_CHANGE_MIC_MODE = 3;
	CHANNEL_MSG_SUB_CHANGE_ICON = 4; // 房间图标
	CHANNEL_MSG_SUB_CHANGE_DESC = 5; // 房间话题描述（标题）
	CHANNEL_MSG_SUB_CHANGE_RECOMMEND_SWITCH = 6;        // 房间推荐开关
	CHANNEL_MSG_SUB_CHANGE_LOCKSCREEN_SWITCH = 7;       // 房间锁屏开关
	CHANNEL_MSG_SUB_CHANGE_AUTO_DISABLE_MIC_SWITCH = 8; // 房间自动锁麦开关
	CHANNEL_MSG_SUB_CHANGE_TAG_ID = 9; // 房间标签变化
	CHANNEL_MSG_SUB_CHANGE_DISABLE_ATTACHMENT_MSG_SWITCH = 10;       // 禁止公屏发图片开关
	CHANNEL_MSG_SUB_CHANGE_DISABLE_LEVEL_LMT_SWITCH = 11; // 禁止等级限制的用户在公屏发言开关
	CHANNEL_MSG_SUB_CHANGE_OPEN_LIVE_CONNECT_MIC_SWITCH = 12; // 语音直播房 连麦 开关
	CHANNEL_MSG_SUB_CHANGE_OPEN_QUEUE_UP_MIC_SWITCH = 13;     // 普通房间的 排麦 开关
	CHANNEL_MSG_SUB_CHANGE_TOPIC_CHANNEL_TAB_ID = 14; // 约玩主题房间的房间标签变化
}

//　麦位模式
enum EChannelMicMode {
	INVALID_MIC_SPACE_MODE = 0; // 无效值
	HAVE_MIC_SPACE_MODE = 1;              // 开黑主席麦位模式
	FREE_MIC_SPACE_MODE = 2;              // 开黑自由模式
	FUN_MIC_SPACE_MODE = 3;               // 娱乐房麦位模式
	SINGLE_MIC_SPACE_MODE = 4;            // 单麦位模式(仅针对答题房间)
	HQ_KH_MIC_SPACE_MODE = 5;             // 高音质开黑麦位模式
	LIVE_MIC_SPACE_MODE = 6;              // 直播麦位模式
	DATING_MIC_SPACE_MODE = 7;            // 相亲麦位模式
}

// 广播消息1
message ChannelMsg {
    required uint32 seq             = 1;
    required uint32 from_uid        = 2;    // 操作者的uid
    required string from_account    = 3;
    required string from_nick       = 4;
    required uint64 time            = 5;
    required uint32 to_channel_id   = 6;
    required uint32 type            = 7;    // ChannelMsgType
    required string content         = 8;    // 消息的明文UTF8内容部分(不包括附件内容) 如果是type是文本消息那么是文本内容;如果是系统消息可能是json信息;
    optional uint32 origin          = 9;    // 消息来源,0/1：App， 2：语音球
    optional uint32 target_uid      = 10;   // 被操作者uid，假如是被踢出频道，就是自己的uid
    optional string target_account	= 11;
    optional string target_nick		= 12;
	optional bytes att_content		= 13;   // 附件内容 (ps 由于IOS旧版本的实现问题 他们将该字段强行解析为ChannelImageMsgContent 那么该字段以后只能放 ChannelImageMsgContent 结构体)
	optional bytes opt_content		= 14;   // 附加内容 （新增 务必注意该字段 可能存放任何类型的消息 务必根据type进行判断）
	optional bytes pb_opt_content	= 15;   // 附加内容 （新增 妈蛋 因为 原先 opt_content 里面放的多是json json传输数据太大了 后续改用pb, 不同type 会有不同pb协议 定义在channel_opt_.proto）
}

// 广播消息2 与 ChannelMsg的区别 只是没有seq 不存储在channelim
message ChannelBroadcastMsg {
    required uint32 from_uid        = 1;    // 操作者的uid
    required string from_account    = 2;
    required string from_nick       = 3;
    required uint64 time            = 4;
    required uint32 to_channel_id   = 6;
    required uint32 type            = 7;    // ChannelMsgType
    required bytes content          = 8;

    optional uint32 target_uid      = 9;   // 被操作者uid，假如是被踢出频道，就是自己的uid
    optional string target_account	= 10;
    optional string target_nick		= 11;

	optional bytes att_content		= 12;   // 附件内容 （务必注意该字段 可能存放任何类型的消息 务必根据type进行判断）
	optional bytes pb_opt_content	= 13;   // 附加内容 （新增 妈蛋 因为 原先 opt_content 里面放的多是json json传输数据太大了 后续改用pb 不同 type 会有不同pb协议 定义在 channel_opt_.proto）
}


// （3.1.0该结构已经废弃）麦上用户信息
message MicInfo {
    required GenericMember member   = 1;
}


// 3.1.0 后新的麦位信息
message MicrSpace
{
	enum EMicrSpaceState {
		MIC_SPACE_NOMAL = 1;      // 正常
		MIC_SPACE_DISABLE = 2;    // 锁麦位 此时麦位不能用
		MIC_SPACE_MUTE = 3;       // 麦位禁言 可以上麦但是不能说话
	}
	required uint32 mic_id = 1;              // 麦位ID 1 - 9
	optional uint32 mic_state = 2;           // EMicrSpaceState 麦位状态 1 正常 2 禁用 3 不可发言
	optional GenericMember mic_member = 3;   // 麦上用户信息 如果麦上有人的话
	optional string headware_key = 4;    	 // 麦上用户的头像装饰key 如果麦上有人的话
	optional int32 sex = 5;    	 		     // 麦上用户的性别
	optional uint32 hold_ts = 6;    	     // 麦上用户的上麦时间

	// 7,8字段仅在公会主房间时会有填写
	optional string guild_official_name        = 7;  // 在公会内的角色名字(比如会长/副会长...)
	optional bool is_guild_channel_permission  = 8;  // 是否有公会的房间管理权限

	//官方认证信息
    optional string certify_title = 9;          // 官方认证(仅在拉取麦列表请求中填写)
    optional string certify_intro = 10;         // 认证介绍(仅在拉取麦列表请求中填写)
	
	// 等级
	optional ChannelMemberVipLevel vip_level = 11;     // 房间内VIP等级 定义在ga_base 中
	optional string mic_facemd5 = 12;                  // 用户的头像md5 (仅在拉取麦列表请求中填写)
}


message ChannelInfo {
    required uint32 channel_id              = 1;  // 频道id
    required string channel_name            = 2;  // 频道名字
    required uint32 channel_member_count    = 3;  // 频道人数
    required uint32 session_id              = 4;  // SDK语音房间id
    required uint32 channel_member_capacity = 5;  // 频道容纳最大人数
	optional bool has_pwd                   = 6;  // 是否有密码
	optional uint32 channel_display_id      = 7;  // 房间的显示ID
	optional uint32 channel_type            = 8;  // 频道类型, 跟DetailInfo的一致 see enum ChannelType
	optional bool has_collected				= 9;  // 是否已收藏
	optional string channel_icon            = 10; // 房间图标的MD5
	optional string channel_desc            = 11; // 房间话题的描述(标题)
}

message ChannelDetailInfo {
    required ChannelInfo channel_info       = 1;  // 频道信息
    required uint32 channel_creator_uid     = 2;  // 频道创建者uid
    required string channel_creator_account = 3;  // 频道创建者account (在进房请求中 不填写)
    required uint32 mic_capacity            = 4;  // 最多可以有多少个麦位 包括被关闭的 (废弃 新版 不再使用)
    required uint32 channel_type            = 5;  // 频道类型 see enum ChannelType
	optional uint32 mic_entry_closesize     = 6;  // 麦位被关掉了几个 (废弃 新版 不再使用)
	optional uint32 mic_mode     			= 7;  // 麦位模式 see EChannelMicMode
	optional string channel_creator_nick    = 8;  // 创建者昵称 (在进房请求中 不填写)
	optional uint32 channel_bind_id         = 9;  // 房间的关联ID，如果是公会房 这个是公会的长ID 如果是个人房这个是个人UID

	optional bool is_open_recommend_switch    = 10;       // 是否打开了房间推荐开关 (PS: 目前仅在进房请求和拉取详情请求中会返回该字段内容)
	optional bool is_open_lock_screen_switch  = 11;       // 是否打开了房间锁屏开关 (PS: 目前仅在进房请求和拉取详情请求中会返回该字段内容)
	optional bool is_open_auto_disable_mic_switch = 12;   // 是否打开了房间自动锁麦开关 (PS: 目前仅在进房请求和拉取详情请求中会返回该字段内容)
	optional bool is_open_disable_attachment_msg_switch = 13; // 是否打开了房间禁止发图开关 (PS: 目前仅在进房请求和拉取详情请求中会返回该字段内容)
	optional bool is_open_disable_level_lmt_switch = 14;      // 是否打开了房间发言用户等级限制开关 (PS: 目前仅在进房请求和拉取详情请求中会返回该字段内容)
	optional bool is_open_live_connect_mic_switch = 15;       // 是否开启电台直播连麦功能，0关闭 1开启; (PS: 目前仅在进房请求和拉取详情请求中会返回该字段内容)

	optional int32 channel_creator_sex = 16;     // 频道创建者de性别(在进房请求中不填写)
	
	optional bool is_open_normal_queue_up_mic_switch = 17;       // 是否开启普通房间的排麦功能，0关闭 1开启; (PS: 目前仅在进房请求和拉取详情请求中会返回该字段内容)
}

//
message GetChannelMemberInfoReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required uint32 target_uid = 3;     // 消息的明文UTF8内容部分 比如 如果消息是个文本消息 那么这个字段是纯文本 
}

message GetChannelMemberInfoResp {
    required BaseResp base_resp = 1;
	
	optional ChannelMemberVipLevel vip_level = 2;     // 房间内VIP等级 定义在ga_base 中
}

// 房间成员
message ChannelMemberInfo {
    required string account   = 1;        // 成员账号
    required uint32 uid       = 2;        // 成员uid
    required bool is_muted    = 3;        // 是否禁言 ( 字段已经废弃 是否禁言由禁言列表返回 )
    required string nick_name = 4;        // 成员昵称

    optional uint32 guild_role = 5;       // ( 字段已经废弃 ) 频道所在公会的角色 (不存在表示是频道没有绑定公会 或者 用户是公会普通成员)  1是会长 2公会官员 3普通公会成员
    optional uint32 group_role = 6;       // ( 字段已经废弃 ) 频道所在公会的群的角色(不存在表示是频道没有绑定公会 或者 用户是群普通成员) 1是群主 2是群管理员 3普通群成员

    optional bool isHoldMic = 7;               // 是否在麦上
	optional uint32 sendgift_reddiamond   = 8; // 房间消费数
	optional uint32 sex   = 9;                 // 性别

    optional uint32 new_rich_level          = 10;    // 用户的财富等级
	optional uint32 new_charm_level         = 11;    // 用户的魅力等级
    optional bool is_newbie                 = 12;    // 是否是新人

	// 在房间类型为 GUILD_HOME_CHANNEL_TYPE 的 公会主房间里面 才有(13，14)字段
	optional string guild_official_name        = 13;   // 在公会内的职位名字(比如会长/副会长...)
	optional bool is_guild_channel_permission  = 14;   // 是否有公会的房间管理权限
	
	optional ChannelMemberVipLevel vip_level = 15;     // 房间内VIP等级 定义在ga_base 中
}


// 根据ChannelType带上用户的guild_id和group_id，以便服务器检查权限
// 该结构后续不需要使用
message ChannelPermissionInfo {
    required uint32 channel_type = 1;   // see enum ChannelType
    optional uint32 guild_id     = 2;   // channel_type == GUILD_TYPE 带guild id
    optional uint32 group_id     = 3;   // 群管理的时候带groupid (这个参数现在不需要了)
}

// 请求发送房间消息
message SendChannelTextMsgReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required string content = 3;     // 消息的明文UTF8内容部分 比如 如果消息是个文本消息 那么这个字段是纯文本 
    optional uint32 origin = 4;      // 消息来源,0/1：App， 2：语音球
    optional uint32 type = 5;		 // 0:普通文本, 6:频道飞机票 （ChannelMsgType）
	optional bytes opt_content	= 6; // 附加内容 （新增 务必注意该字段 可能存放任何类型的消息 务必根据type进行判断）
	
	optional uint32 target_uid = 7;   //新增 1对1发送时目标的UID
}

message SendChannelTextMsgResp {
    required BaseResp base_resp = 1;

}

message SendChannelAttachmentMsgReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
    required bytes  att_content = 3;  // 附件内容 如果是图片则是图片的二进制
    optional uint32 origin = 4;       // 消息来源,0/1：App， 2：语音球
    optional uint32 type = 5;		  // 消息类型 ChannelMsgType
}

message SendChannelAttachmentMsgResp {
    required BaseResp base_resp = 1;
	optional string image_format = 2;       // 'PNG' 'GIF' 'JPG' 'JPEG'
	optional string msg_attachment_key = 3; // 附件key 当 is_exist_sourceimage==ture时可以用于下载
	optional bool is_exist_sourceimage = 4; // 表示是否还有原始数据  false表示没有原始数据了 无需下载 
	optional uint32 channel_id = 5;
}

// 房间图片消息内容
message ChannelImageMsgContent {
	required bytes image_bin = 1;            // 图片的缩略图的二进制流 如果  is_exist_sourceimage == false 表示没有大图
	optional string image_format = 2;        // 'PNG' 'GIF' 'JPG' 'JPEG'
	optional string msg_attachment_key = 3;  // 附件key 当 is_exist_sourceimage==ture时可以用于下载
	optional string msg_attachment_url = 4;  // 预留， 表示大图附件的url，可能为空 为空则使用key去下载附件
	optional bool is_exist_sourceimage = 5;  // 表示是否还有原始数据  false表示没有原始数据了 无需下载 
}


// 根据附件KEY拉取消息附件内容
message DownloadChannelMsgAttachmentReq {
    required BaseReq base_req = 1;
    required uint32 channel_id = 2;
	required uint32 msg_type = 3;		      // 消息类型 ChannelMsgType
	required string msg_attachment_key = 4;   // 附件key 用于下载
}

message DownloadChannelMsgAttachmentResp {
    required BaseResp base_resp = 1;
	required uint32 channel_id = 2;
	required uint32 msg_type = 3;		    // 消息类型 ChannelMsgType 从请求中原样带回
	required bytes attachment_content = 4;  // 附件内容 如果是图片就是完整的图片二进制
	optional string msg_attachment_key = 5; // 附件key 用于下载 从请求中原样带回
}

//拉取频道缺失的seq的内容
message GetChannelMsgReq {
    required BaseReq base_req                      = 1;
    required uint32 channel_id                     = 2;
    required uint32 begin_seq                      = 3;
    required uint32 end_seq                        = 4;
	optional bool is_init                          = 5;  // true表明是初始化拉取历史消息，此时只返回部分历史消息列表
}

message GetChannelMsgResp {
    required BaseResp base_resp             = 1;
    required uint32 channel_id              = 2;
    required uint32 begin_seq               = 3;  // 如果 is_init == true 该字段无意义
    required uint32 end_seq                 = 4;  // 如果 is_init == true 该字段无意义
    repeated ChannelMsg channel_msg_list    = 5;  // 如果 is_init == true 将只会返回特定类型的消息
	
	// 附件信息
    repeated MicInfo mic_list               = 6;  // 上麦的用户列表 (该字段 在 is_init==true 情况下没有数据) (3.0以上新版客户端 不返回该字段)
    required uint32 channel_user_count      = 7;
	optional uint32 mic_entry_closesize     = 8;  // 开黑模式下 麦位被关掉了几个 (3.0以上新版客户端 不返回该字段)
	optional bool is_init                   = 9;  // true表明是初始化拉取历史消息，此时只返回部分历史消息列表
	
	repeated MicrSpace full_mic_list = 10; // 当前房间的完整的麦位信息 (该字段 在 is_init==true 情况下没有数据)
	optional uint32 mic_mode = 11;         // 当前房间麦模式           (该字段 在 is_init==true 情况下没有数据)
}

message CreateChannelOpt{
	optional uint32 max_member_count = 1;
}
// 创建频道
message CreateChannelReq {
    required BaseReq base_req                      = 1;
    required string channel_name                   = 2;  // 频道名字
    required ChannelPermissionInfo permission_info = 3;  // 用户权限信息
	optional string passwd                         = 4;  // 密码
	optional CreateChannelOpt	create_opt	= 5;//额外选项
}

message CreateChannelResp {
    required BaseResp base_resp             = 1;
    required ChannelDetailInfo channel_info = 2;    // 频道信息
    required string create_message          = 3;	// 创建频道成功的文案
	
	// 公会普通房 参数
	optional uint32 guild_nomal_remain = 4;   // 当前公会普通房剩余创建数量 
	
	// 公会公开娱乐房 的参数
	optional uint32 guild_pub_remain = 5;     // 当前公会公开娱乐房剩余创建数量 ,  当创建类型type = REQUEST_GUILD_PUB_FUN_ALL 时有效
	optional string guild_pub_band_text = 6;  // 公会公开娱乐房禁止创建的描述语句, 当创建类型type = REQUEST_GUILD_PUB_FUN_ALL 且 guild_pub_remain = 0 时有效
}

// 解散频道
message ChannelDismissReq {
    required BaseReq base_req                      = 1;
    required uint32 channel_id                     = 2;   // 频道账号
    required ChannelPermissionInfo permission_info = 3;   // 用户权限信息
}

message ChannelDismissResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id  = 2;   // 频道id
}

// 修改频道名字
message ChannelModifyNameReq {
    required BaseReq base_req                      = 1;
    required uint32 channel_id                     = 2;  // 频道id
    required string channel_name                   = 3;  // 频道名字
    required ChannelPermissionInfo permission_info = 4;  // 用户权限信息
}

message ChannelModifyNameResp {
    required BaseResp base_resp  = 1;
    required uint32 channel_id   = 2;     // 频道id
    required string channel_name = 3;     // 频道新名字
}

// 修改频道密码
message ChannelModifyPasswdReq {
    required BaseReq base_req                      = 1;
    required uint32 channel_id                     = 2;  // 频道id
    required ChannelPermissionInfo permission_info = 3;  // 用户权限信息
	optional string passwd                         = 4;  // 密码
}

message ChannelModifyPasswdResp {
    required BaseResp base_resp  = 1;
    required uint32 channel_id   = 2;     // 频道id
    required string passwd       = 3;     // 密码
}

// 获取密码
message ChannelGetPasswdReq {
    required BaseReq base_req                      = 1;
	required uint32 channel_id                     = 2;
    required ChannelPermissionInfo permission_info = 3;  // 用户权限信息
}

message ChannelGetPasswdResp {
    required BaseResp base_resp  = 1;
    required uint32 channel_id   = 2;     // 频道id
    required string passwd       = 3;     // 密码
}

enum ESpecialOptSwitchBitMap {
		ENUM_SPC_OPT_SWITCH_NONE = 0;      // NONE
		ENUM_SPC_OPT_SWITCH_STERAO = 1;    // 立體聲
}


// 进入频道
message ChannelEnterReq {
	enum EChannelEnterSource
	{
		ENUM_CHANNEL_ENTER_NORMAL = 0;
		ENUM_CHANNEL_ENTER_TEMP_KH_PAIR= 1; // 从 临时开黑配对 入口
		ENUM_CHANNEL_ENTER_NEAR_BY = 2; 	// 从 附近的人 入口
		ENUM_CHANNEL_ENTER_KH_TEAM = 3; 	// 从 开黑组队 入口
		ENUM_CHANNEL_ENTER_LIVE = 4; 	    // 从 语音直播 入口
		ENUM_CHANNEL_ENTER_ROBOT = 5; 	    // 从 机器人 入口(表明这是个机器人)
		ENUM_CHANNEL_ENTER_APICENTER = 6; 	// 从 api center 入口
		ENUM_CHANNEL_ENTER_CHANNEL_GAME_RECRUIT = 7; 	// 从 房间游戏招募 入口
		ENUM_CHANNEL_ENTER_USER_ASYNC_NEWS_FEED = 8; 	// 从 异步内容动态 入口
		
		ENUM_CHANNEL_ENTER_IM = 9; 	            // 从单个IM聊天界面 跟随
		ENUM_CHANNEL_ENTER_CH_COLLECT = 10; 	// 从 房间收藏
		ENUM_CHANNEL_ENTER_FUN_TAB = 11; 	    // 从 娱乐TAB
		ENUM_CHANNEL_ENTER_SEARCH  = 12; 	    // 从 搜索 入口
		ENUM_CHANNEL_ENTER_FINDPLAY_TAB = 13; 	// 从 约玩 TAB
		
		ENUM_CHANNEL_ENTER_WEB = 14; 	         // 从 WEB进入
		ENUM_CHANNEL_ENTER_FRIENDOL_BANNER = 15; // 从 好友在线banner 跟随
		
		ENUM_CHANNEL_ENTER_PUBLISH_TOPICROOM = 16;      // 从 发布主题房
		ENUM_CHANNEL_ENTER_FRIENDLIST_TAB = 17;         // 从 玩伴列表页
		ENUM_CHANNEL_ENTER_FINDPLAY_TAB_OFFICIAL = 18; 	// 从 约玩 TAB 官方推荐进去的

	}

    required BaseReq base_req  = 1;
    required uint32 channel_id = 2;
	optional ChannelPermissionInfo permission_info = 3; // (可以不用填) 用于支持频道成员列表按照管理员级别排序 如果没有表示 该用户是普通成员
	optional string passwd = 4;             // 密码
	optional uint32 channel_show_id  = 5;   // 房间的显示ID, 如果 channel_id 为0 可以根据channel_show_id进入房间
	optional uint32 follow_friend_uid  = 6; // 如果是跟随某人进房间 这里填写跟随的目标用户的UID
	
	optional uint32 enter_source  = 7;   //  see EChannelEnterSource
	
	optional string knock_door_sign  = 8; // 敲门进房带的加密签名信息
	
	optional string enter_source_second  = 9;   // 二级子来源
}

message ChannelEnterResp {
    required BaseResp base_resp              = 1;
    required ChannelDetailInfo channel_info  = 2; // 此时返回的房间详细信息里 创建者的信息包括昵称和帐号信息均为空
    optional uint32 channel_id = 3;
    optional uint32 user_confirm_status = 4;	// MemberConfirmStatus 用于房间召集状态
    optional uint32 sdk_version  = 5;           // sdk版本 默认不填为空 1表示旧版本 2表示新版本(废弃 不能用了)
    optional uint32 server_time  = 6;           // 32bit 秒级 服务器时间
	
    optional bytes pb_opt_content = 7;   // 附加内容 （定义在channel_opt_.proto 的 ChannelEnterOpt）
    optional bytes enter_msg	  = 8;   // 附加文本
    optional bytes welcome_msg	  = 9;   // 欢迎语文本
	
    optional uint32 special_opt_switch_bitmap  = 10;  // 特殊的開關類型 ESpecialOptSwitchBitMap 這是個bitmap 哦！！！！
    optional uint32 enter_source  = 11; //   see EChannelEnterSource
    optional bool is_live_start = 12;   //   直播房是否开播中
    optional bool recommend_channel = 13;     // 房间是否为推荐房
}

// 用户的PC助手进入房间 （前提是用户已经进入了某个房间）
message ChannelPcHelperEnterReq 
{
    required BaseReq base_req  = 1;
}

message ChannelPcHelperEnterResp 
{
    required BaseResp base_resp = 1;
	required uint32 channel_id = 2;
}

// PC助手 检查用户是否在麦位上以及麦位状态
message ChannelPcHelperMicCheckReq 
{
    required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
}

message ChannelPcHelperMicCheckResp
{
    required BaseResp base_resp = 1;
	optional MicrSpace mic_info = 2;
	optional uint32 mic_mode = 3;   // 麦位模式 see EChannelMicMode
}

// 退出频道
message ChannelQuitReq {
    required BaseReq base_req  = 1;
    required uint32 channel_id = 2;
	optional bool need_collected = 3;
}

message ChannelQuitResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id  = 2;
}

enum ChannelRequestType {
    REQUEST_HOME_PAGE = 1; // 公会首页的 普通房间列表
    REQUEST_DETAIL    = 2; // 公会完整的 普通房间列表
	REQUEST_DETAIL_ALLMEMBERSIZE  = 3; // 公会完整的 普通房间列表的全部成员的总数
	REQUEST_GUILD_PUB_FUN_ALL  = 4;    // 公会全部公开娱乐房列表 
}

// 获取频道列表（仅用于公会房间列表获取）
message GetChannelListReq {
    required BaseReq base_req    = 1;
    required uint32 request_type = 2; // see ChannelRequestType
}

message GetChannelListResp {
    required BaseResp base_resp       = 1;
    required uint32 request_type      = 2;  // see enum ChannelRequestType
    repeated ChannelInfo channel_list = 3;  // 频道列表, 当request_type = REQUEST_DETAIL_ALLMEMBERSIZE时该值为空
    optional uint32 all_channel_count = 4;  // 全部频道列表的数量, 当request_type = REQUEST_HOME_PAGE 和 REQUEST_DETAIL_ALLMEMBERSIZE 时有效
	optional uint32 all_member_count = 5;   // 全部频道的全体成员数量,  当request_type = REQUEST_DETAIL_ALLMEMBERSIZE时有效
	
	// 公会公开娱乐房 的参数
	optional uint32 guild_pub_remain = 6;     // 当前公会公开娱乐房剩余创建数量 ,  当request_type = REQUEST_GUILD_PUB_FUN_ALL 时有效
	optional string guild_pub_band_text = 7;  // 公会公开娱乐房禁止创建的描述语句, 当request_type = REQUEST_GUILD_PUB_FUN_ALL 且 guild_pub_remain = 0 时有效
}

// 获取频道详情
message GetChannelDetailReq {
    required BaseReq base_req  = 1;
    required uint32 channel_id = 2;
	optional uint32 channel_show_id  = 3; // 房间的显示ID, 如果 channel_id 为0 可以根据channel_show_id 获取房间信息
}

message GetChannelDetailResp {
    required BaseResp base_resp             = 1;
    required ChannelDetailInfo channel_info = 2;
}

// 获取频道成员列表
message ChannelGetMemberListReq {
    required BaseReq base_req  = 1;
    required uint32 channel_id = 2;
    required uint32 begin_id   = 3;  // 开始查询的索引编号 用于翻页 第一页可以填0
    required uint32 req_cnt    = 4;  // 每次获取列表最多需要多少条 一般填50
}

message ChannelGetMemberListResp {
    required BaseResp base_resp                    = 1;
    required uint32 channel_id                     = 2;
    repeated ChannelMemberInfo channel_member_list = 3; // 成员列表
    required uint32 begin_id                       = 4; // 本次返回的成员列表的起始索引序号
	required uint32 all_member_cnt                 = 5; // 当前房间内所有成员数目
}

// 房间消费榜
message ChannelGetConsumTopNReq {
    required BaseReq base_req  = 1;
    required uint32 channel_id = 2;
    required uint32 begin_id   = 3;  // 开始查询的索引编号 用于翻页 第一页可以填0
    required uint32 req_cnt    = 4;  // 每次获取列表最多需要多少条 一般填50
}

message ChannelGetConsumTopNResp {
    required BaseResp base_resp                    = 1;
    required uint32 channel_id                     = 2;
	required uint32 begin_id                       = 3; // 本次返回的成员列表的起始索引序号
    repeated ChannelMemberInfo member_list = 4;         //  列表 
}

// 频道禁言成员列表
message ChannelGetMutedMemberListReq {
    required BaseReq base_req  = 1;
    required uint32 channel_id = 2;
}

message ChannelGetMutedMemberListResp {
    required BaseResp base_resp        = 1;
    required uint32 channel_id         = 2;
    repeated GenericMember member_list = 3;
}

// 禁言
message ChannelMemberMuteReq {
    required BaseReq base_req                      = 1;
    required uint32 channel_id                     = 2;
    repeated uint32 uid_list                       = 3;
    required ChannelPermissionInfo permission_info = 4; // 用户权限信息 现在没什么用了...
}

message ChannelMemberMuteResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id  = 2;
    repeated uint32 uid_list    = 3;
}

// 解除禁言
message ChannelMemberUnmuteReq {
    required BaseReq base_req                      = 1;
    required uint32 channel_id                     = 2;
    repeated uint32 uid_list                       = 3;
    required ChannelPermissionInfo permission_info = 4; // 用户权限信息 现在没什么用了...
}

message ChannelMemberUnmuteResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id  = 2;
    repeated uint32 uid_list    = 3;
}

// 上麦
message ChannelGetMicReq {
    required BaseReq base_req                      = 1;
    required uint32 channel_id                     = 2;
    required ChannelPermissionInfo permission_info = 3; // 用户权限信息 现在没什么用了...
	optional MicrSpace micr_info = 4; // 上麦的麦位信息 必填
	optional bool is_force = 5;       // 是否需要强行上麦 必须指定麦位信息 如果指定的麦位上有人则踢人下麦 非管理员不允许强行上麦
}

message ChannelGetMicResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id  = 2;
	optional MicrSpace micr_info = 3; // 上麦的麦位信息
}

// 下麦
message ChannelReleaseMicReq {
    required BaseReq base_req                      = 1;
    required uint32 channel_id                     = 2;
    required ChannelPermissionInfo permission_info = 3; // 用户权限信息 现在没什么用了...
}

message ChannelReleaseMicResp {
    required BaseResp base_resp = 1;
    required uint32 channel_id  = 2;
}

// 获取麦列表
message ChannelGetMicListReq {
    required BaseReq base_req                      = 1;
    required uint32 channel_id                     = 2;
}

message ChannelGetMicListResp {
    required BaseResp base_resp     = 1;
    required uint32 channel_id      = 2;
	
    repeated MicInfo mic_list       = 3;      // 上麦的用户列表  (字段3.1废弃)
    optional uint32 mic_entry_closesize = 4;  // 麦位被关掉了几个(字段3.1废弃)
	
	repeated MicrSpace all_micr_list = 5;     // 全体麦位信息列表 包括关闭的/没有人上的 3.1.0新增
	optional uint32 mic_mode = 6;             // 当前房间麦模式
	optional uint64 server_time_ms  = 7;      // 64bit 毫秒级 服务器时间
}

message QuickJoinChannelReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
}

message QuickJoinChannelResp {
	required BaseResp base_resp 	= 1;
	required uint32	channel_id		= 2;
	optional uint32 sdk_version  = 3;     // sdk版本 默认不填为空 1表示旧版本 2表示新版本
	
	repeated MicrSpace full_mic_list = 4; // 当前房间 完整的麦位信息
	optional uint32 mic_mode = 5;         // 当前房间麦模式
	optional bool is_live_start = 6;      // 当前房间直播是否开始
}



// 将用户踢出频道
message KickoutChannelReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
	required ChannelPermissionInfo permission_info = 3; // 执行操作的用户权限信息
	repeated uint32	targetuid_list		= 4;
}

message KickoutChannelResp {
	required BaseResp base_resp 	= 1;
	required uint32	channel_id		= 2;
	repeated uint32	tickout_list	= 3;
}

// 关闭麦位
message CloseChannelMicEntryReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
	required ChannelPermissionInfo permission_info = 3; // 执行操作的用户权限信息 废弃字段 不用了
	optional uint32	target_uid		= 4;  // 如果麦上有用户 需要将该用户踢出
	optional uint32	mic_pos_id		= 5;  // 被关闭的麦位编号 可以先填上 1-9
}

message CloseChannelMicEntryResp {
	required BaseResp base_resp 	= 1;
	required uint32	channel_id		= 2;
	optional uint32	mic_pos_id		= 3;       // 被关闭的麦位编号 可以先填上
	optional uint32 mic_entry_closesize  = 4;  // 新增 当前已经有多少个麦位被关掉了
	optional uint32	target_uid		= 5;  // 如果麦上有用户 需要将该用户踢出
}

// 打开麦位
message OpenChannelMicEntryReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
	required ChannelPermissionInfo permission_info = 3; // 执行操作的用户权限信息 废弃字段 不用了
	optional uint32	mic_pos_id		= 4;  // 被打开的麦位编号 可以先填上 1-9
}

message OpenChannelMicEntryResp {
	required BaseResp base_resp 	= 1;
	required uint32	channel_id		= 2;
	optional uint32	mic_pos_id		= 3;       // 被打开的麦位编号 可以先填上
	optional uint32 mic_entry_closesize  = 4;  // 新增 当前还有多少个麦位被关掉了
}

// 设置麦位状态 比如打开/关闭/禁言 麦位 (可以取代上面的麦位开启和麦位关闭命令)
message SetChannelMicSpaceStatusReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
	required MicrSpace	target_mic_info	= 3; 
}

message SetChannelMicSpaceStatusResp {
	required BaseResp base_resp 	 = 1;
	required uint32	channel_id		 = 2;
	repeated MicrSpace full_mic_list = 3; // 操作完之后 完整的麦位信息
}



// 将用户踢下麦
message KickoutChannelMicReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
	required ChannelPermissionInfo permission_info = 3; // 执行操作的用户权限信息
	repeated uint32	targetuid_list		= 4;
}

message KickoutChannelMicResp {
	required BaseResp base_resp 	= 1;
	required uint32	channel_id		= 2;
	repeated uint32	tickout_list	= 3;  // 被踢下麦的用户UID列表
	repeated MicrSpace full_mic_list = 4; // 操作完之后 完整的全量麦位列表
}




// 切换房间麦模式
// 有麦模式(主席模式是有麦模式的特例 即全部麦位被锁上的麦模式) 或者 自由模式 或者 娱乐模式
message SetChannelMicModeReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
	required uint32	mic_mode		= 3; // 麦模式 see EChannelMicMode
	optional bool is_disable_all_mic = 4; // 是否锁定所有的麦位 仅在 HAVE_MIC_SPACE_MODE 模式下有效
	optional bool is_need_hold_mic = 5;   // 本人是否需要上麦 仅在 HAVE_MIC_SPACE_MODE 模式下有效
    required ChannelPermissionInfo permission_info = 6; // 执行操作的用户权限信息
}

message SetChannelMicModeResp {
	required BaseResp base_resp 	= 1;
	required uint32	channel_id		= 2;
	required uint32	mic_mode		= 3; // 麦模式 see EChannelMicMode
	
	optional bool is_disable_all_mic = 4; // 是否锁定所有的麦位 仅在 HAVE_MIC_SPACE_MODE 模式下有效
	optional bool is_need_hold_mic = 5;   // 本人是否需要上麦 仅在 HAVE_MIC_SPACE_MODE 模式下有效
	
	repeated MicrSpace full_mic_list = 6; // 操作完之后 完整的麦位信息
}


enum EChannelAdminRoleType {
	CHANNEL_ROLE_INVALID = 0;   // 无效
	CHANNEL_ROLE_OWNER = 1;   // 房主
	CHANNEL_ROLE_ADMIN = 2;   // 管理员
	CHANNEL_ROLE_NOMAL = 3;   // 普通用户
	CHANNEL_ROLE_SUPER_ADMIN = 4;   // 超级管理员
}


// 获取我管理的房间列表
message GetUserAdminChannelListReq {
	required BaseReq base_req 		= 1;
	required uint32	admin_role		= 2; // 指定获取某种类型的管理列表 see EChannelAdminRoleType, 如果为0表示获取所有有管理权限的房间
}

message GetUserAdminChannelListResp {
	required BaseResp base_resp 	= 1;
	required uint32	admin_role		= 2;    // 指定获取某种类型的管理列表 see EChannelAdminRoleType, 如果为0表示获取所有有管理权限的房间
	repeated ChannelInfo channel_list = 3;  // 频道列表
}

// 搜索房间
message SearchChannelReq {
	required BaseReq base_req 		= 1;
	required string keyword         = 2;
	
	enum EKeywordResultType
	{
		ENUM_KEYWORD_CHANNEL = 1;
		ENUM_KEYWORD_TAG = 2;
	};
	
	optional uint32 keyword_type = 3; // SEE EKeywordResultType 表示搜索的结果是什么
	optional uint32 page = 4;
	optional uint32 page_count = 5;
}

message SearchChannelResp {
	required BaseResp base_resp 	= 1;
    required string keyword         = 2;
	
	// 搜索结果
	repeated ChannelDetailInfo channel_list = 3;  // 频道列表
	repeated SCTagInfo sc_tag_list = 4;			  // 房间标签列表
	
	// 翻页属性
	optional uint32 keyword_type = 5; // SEE EKeywordResultType
	optional uint32 page = 6;
	optional bool is_reach_end = 7;
}

// 批量获取房间信息
message BatchGetChannelListReq {
	required BaseReq base_req 		= 1;
	repeated uint32 channel_id_list = 2; 
}

message BatchGetChannelListResp {
	required BaseResp base_resp 	= 1;
	repeated ChannelInfo channel_list = 2; 
}

enum ESpecialMusicID {
    CHANNEL_SPECIAL_MUSIC_ID_PERSONAL = 1;                   // 特殊的musicID 表示用户自定义播放的音乐
}

// 房间音乐(废弃)
message ChannelMusic
{
	required uint32 music_id = 1;
	required string music_name = 2; 
	optional string artist_name = 3;
	optional string album_name = 4;
	optional string play_web_url = 5;
	optional string download_url = 6; // 只有在播放时这个字段才会被填写
	optional string img_url = 7;
	optional uint32 music_type = 8; //1原唱、2伴奏
}

// 播放音乐(废弃)
message StartChannelMusicReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2; 
	required ChannelMusic music_info = 3;
}

message StartChannelMusicResp {
	required BaseResp base_resp = 1;
}

// 停止播放音乐(废弃)
message StopChannelMusicReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2; 
	required ChannelMusic music_info = 3;
}

message StopChannelMusicResp {
	required BaseResp base_resp = 1;
}

// 获取房间当前音乐播放信息(废弃)
message GetChannelCurrMusicReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2; 
}

message GetChannelCurrMusicResp {
	required BaseResp base_resp = 1;
	optional GenericMember start_music_user = 2;
	optional ChannelMusic music_info = 3;
}


// 搜索音乐(废弃)
message ChannelMusicProvider
{
	required string provider_ico = 1;  // "xx音乐"的ico url
	required string provider_info = 2; // 本功能有"xx音乐"提供技术支持 之类的文本描述
}

message SearchChannelMusicReq {
	required BaseReq base_req 		= 1;
	required string keyword         = 2;
}

message SearchChannelMusicResp {
	required BaseResp base_resp 	= 1;
    required string keyword         = 2;
	repeated ChannelMusic music_list = 3;  // 列表
	optional ChannelMusicProvider provider_info = 4; // 音乐提供方信息 比如"xx音乐"
}

// 获取房间播放列表
message GetChannelMusicListReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2; 
}

message GetChannelMusicListResp {
	required BaseResp base_resp = 1;
    required uint32 channel_id = 2; 
	repeated ChannelMusic history_music_list = 3;    // 历史列表
	repeated ChannelMusic recommend_music_list = 4;  // 推荐列表
	optional ChannelMusicProvider provider_info = 5; // 音乐提供方信息
}

// 根据musicID获取音乐下载地址(废弃)
message GetChannelMusicInfoByMidReq {
	required BaseReq base_req  = 1;
	required uint32 music_id = 2; 
}

message GetChannelMusicInfoByMidResp {
	required BaseResp base_resp = 1;
    required ChannelMusic music_info = 2;
}

// 更新频道的收藏状态
message UpdateChannelCollectStatusReq {
	required BaseReq base_req  = 1;
	required bool is_collected = 2;
	required uint32 channel_id = 3;
}

message UpdateChannelCollectStatusResp {
	required BaseResp base_resp = 1;
	required bool is_collected = 2;
	required uint32 channel_id = 3;
}

enum ChannelCollectionsRequestType {
    REQUEST_COLLECT_HOME_PAGE = 1; // 收藏频道首页的频道列表
    REQUEST_COLLECT_DETAIL    = 2; // 收藏频道完整的频道列表
}

// 获取收藏房间列表
message GetChannelCollectionListReq {
	required BaseReq base_req  = 1;
	required uint32 request_type = 2;	// ChannelCollectionsRequestType
}

message GetChannelCollectionListResp {
	required BaseResp base_resp  = 1;
	repeated ChannelDetailInfo channel_list = 2;
	required uint32 request_type = 3;	// ChannelCollectionsRequestType
	required uint32 total_channel_count = 4;
	repeated uint32 admin_channel_list = 5; // 管理的频道列表
}

// 发起频道召集
message ConveneChannelReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2; 
	required string convene_msg = 3;	// 召集描述
}

message ConveneChannelResp {
	required BaseResp base_resp = 1;
	required uint32 convene_ts = 2;
	required uint32 channel_id = 3;
}

// 取消频道召集
message DisableConveneChannelReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2; 
}

message DisableConveneChannelResp {
	required BaseResp base_resp  = 1;
	required uint32 channel_id = 2;
}

enum MemberConfirmStatus
{
	CONVENE_NOT_RECIEVE = 0;		// 未收到召集
	CONVENE_RECIEVE = 1;			// 【已送达】，未对召集令进行操作
	CONVENE_HOLDING = 2;			// 【我正在忙，稍后就来】，召集令中选择“等一下”
	CONVENE_REJECT = 3;				// 【未响应召集】，关闭召集按钮
	ENTER_CHANNEL = 4;				// 【已进房间】
}
	
// 频道召集用户信息
message ChannelConveneMember {
	required uint32 channel_id = 1;
	required string account = 2;			// 账号
    required uint32 uid = 3;				// uid
    required string nick_name = 4;			// 名字
	required uint32 confirm_status = 5;		// MemberConfirmStatus
}

// 获取响应频道召集的用户列表
message GetChannelConfirmMemberListReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2; 
}

message GetChannelConfirmMemberListResp {
	required BaseResp base_resp  = 1; 
	repeated ChannelConveneMember member_list = 2;
	optional uint32 total_enter_count = 3;	// 已通过召集进房间的总人数
	optional uint32 total_confirm_count = 4;	// 收到召集的总成员数
	optional uint32 channel_id = 5;
	optional uint32 total_collect_count = 6;	// 收藏房间的总人数
}

// 频道召集提示
message ChannelConveneMsg {
	required uint32 channel_id = 1;
	required uint32 convene_ts = 2;
	required string convene_msg = 3;
	required uint32 duration_min = 4;	// 召集有效时间(分钟)
	optional ChannelInfo channel_info = 5;
}

// 获取正在召集指定用户的频道
message GetUserConveneChannelListReq {
	required BaseReq base_req  = 1;
}

message GetUserConveneChannelListResp {
	required BaseResp base_resp  = 1; 
	repeated ChannelConveneMsg convene_list = 2;
}

// 更新频道召集的用户的响应状态
message UpdateMemberConfirmStatusReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	required uint32 confirm_status = 3;	// MemberConfirmStatus
}

message UpdateMemberConfirmStatusResp {
	required BaseResp base_resp  = 1; 
	required uint32 confirm_status = 2;	// MemberConfirmStatus
	required uint32 channel_id = 3;
}

// 获取频道的召集信息
message GetChannelConveneInfoReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
}

message GetChannelConveneInfoResp {
	required BaseResp base_resp  = 1;
	required uint32 channel_id = 2;
	required uint32 valid_convene_ts = 3;  // 可发起下次召集的时间
	required uint32 convene_status = 4;	// 0.未召集 1.召集中
	required uint32 confirm_count = 5;	// 响应召集的人数
	required uint32 convene_duration_min = 6;	// 召集有效时间(分钟)
	optional uint32 last_convene_ts = 7;	// 上次发起召集时间
	optional uint32 last_disable_ts = 8;	// 上次取消召集时间
	optional string last_convene_msg = 9;	// 上次的召集描述
}

enum MusicInfoStats
{
	MUSIC_INFO_NORMAL = 1;
	MUSIC_INFO_PLAYING = 2;
	MUSIC_INFO_NEXT = 3;
}
// 音乐V2
message MusicInfoV2
{
	required string client_key = 1; // 客户端唯一标识
	required string name = 2;	    // 歌曲名
	required string author = 3;     // 作者
	required uint32 uid = 4;       // 上传者id
	optional uint32 volume = 5;    // 音量
	optional int64 key = 6;        // 服务器标识
	optional string account = 7;   // 上传者account
	optional uint32 is_local = 8;  // 是否是客户端本地歌曲 1-是 0-否
	optional uint32 status = 9;    // 播放状态
	required string nickname = 10; // 上传者名字
	optional uint32 music_type = 11; //音乐类型 1原唱、2伴奏

}
// 获取音乐列表
message GetChannelMusicListV2Req{
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
}
message GetChannelMusicListV2Resp{
	required BaseResp base_resp  = 1;
	repeated MusicInfoV2 music_list = 2;	
}

// 设置可分享歌曲
message SetChannelMusicV2CanShareReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	required bool can_share = 3;
}
message SetChannelMusicV2CanShareResp {
	required BaseResp base_resp  = 1;
}

// 设置自由模式
message SetChannelMusicV2FreeModeReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	required bool free_mode = 3;
}
message SetChannelMusicV2FreeModeResp {
	required BaseResp base_resp  = 1;
}

// 播放状态
message ChannelMusicStatus{
	required uint32 play_mode = 1;
	required uint32 volume = 2;
	required bool can_share = 3;
	required bool is_playing = 4;
	optional int64 current_playing = 5;
	optional bool free_mode = 6;
}
message GetChannelMusicV2StatusReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
}
message GetChannelMusicV2StatusResp {
	required BaseResp base_resp  = 1;
	required ChannelMusicStatus channel_music_status = 2;
}

// 添加歌曲
message AddChannelMusicV2Req {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	repeated MusicInfoV2 music_list = 3;
	optional uint32 is_local = 4; //1 从本地上传到播放列表 
}
message AddChannelMusicV2Resp {
	required BaseResp base_resp  = 1;
	repeated MusicInfoV2 music_added_list = 2;
}

// 审核上传歌曲的歌名和作者名字
message CheckAddMusicV2Req {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	repeated MusicInfoV2 music_list = 3;
}
message CheckAddMusicV2Resp {
	required BaseResp base_resp  = 1;
	repeated MusicInfoV2 music_pass_list = 2;
}

// 删除歌曲
message RemoveChannelMusicV2Req {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	repeated int64 music_list = 3;
}
message RemoveChannelMusicV2Resp {
	required BaseResp base_resp  = 1;
	repeated int64 music_removed_list = 2;
}
// 设定下一首
message SetNextMusicV2Req {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	required int64 music_id = 3;// 服务器标识
}
message SetNextMusicV2Resp {
	required BaseResp base_resp  = 1;
}
// 播放指定歌曲
message PlayMusicV2MusicReq{
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	required int64 music_id = 3;// 服务器标识
}
message PlayMusicV2MusicResp {
	required BaseResp base_resp  = 1;
} 

// 播放命令
enum ChannelMusicCtrl
{
	MUSIC_CTRL_START = 1;
	MUSIC_CTRL_STOP = 2;
	MUSIC_CTRL_NEXT = 3;
	MUSIC_CTRL_PREV = 4;
}

message ChanneMusicV2CommandReq {
	required BaseReq base_req  = 1;
	required uint32 uid = 2;
	required uint32 channel_id = 3;
	required uint32 command_ctrl = 4;
}
message ChanneMusicV2CommandResp {
	required BaseResp base_resp  = 1;
	optional MusicInfoV2 next_music = 2;
}

// 播放模式
enum ChannelMusicPlayMode {
	MUSIC_PLAY_MODE_ORDER = 1;
	MUSIC_PLAY_MODE_RANDOM = 2;
	MUSIC_PLAY_MODE_LOOP = 3;
}
message SetChannelMusicV2PlayModeReq {
	required BaseReq base_req  = 1;
	required uint32 uid = 2;
	required uint32 channel_id = 3;
	required uint32 play_mode = 4;
}
message SetChannelMusicV2PlayModeResp {
	required BaseResp base_resp  = 1;
}
// 音量
message SetChannelMusicV2VolumeReq {
	required BaseReq base_req  = 1;
	required uint32 uid = 2;
	required uint32 channel_id = 3;
	required uint32 volume = 4;
}
message SetChannelMusicV2VolumeResp {
	required BaseResp base_resp  = 1;
}

// 心跳
message ChanneMusicV2HeartBeatReq 
{
	enum ECLIENT_EVENT
	{
		ECLIENT_EVENT_NORMAL = 0;
		ECLIENT_EVENT_NOT_PLAY_NETWORK = 1; // 网络原因无法播放
		ECLIENT_EVENT_NOT_PLAY_FILE = 2;    // 文件原因无法播放
	};
	
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	required int64 music_key = 3;
	required uint32 percent = 4; // 播放进度,最大100
	
	optional uint32 client_event = 5; // ECLIENT_EVENT
}
message ChanneMusicV2HeartBeatResp {
	required BaseResp base_resp  = 1;
	required uint32 uid = 2;
	required uint32 channel_id = 3;
	required uint32 volume = 4;
	optional int64 music_id = 5;

}

// 快速组队信息
message GetGameMatchOptionsReq {
	required BaseReq base_req 		= 1;
	required uint32 version = 2;
}
message GameMatchOptions {	
	required uint32 game_id = 1;
	required string game_name = 2;
	required string icon_url = 3;
	repeated uint32 member_count = 4;
	repeated string ann_list = 5;
	required uint32 channel_tag_id = 6;
}
message GetGameMatchOptionsResp {	
	required BaseResp base_resp 	= 1;	
	repeated GameMatchOptions game_match_opt_list = 2;
	required uint32 version = 3;
}
// 发起组队
message StartGameMatchReq {
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;
	required string channel_ann = 3;
	required uint32 max_member_count = 4;
	required uint32 tag_id = 5;
    optional uint32 recommend_status = 6;   // 新人推荐位展示状态 see NOVICE_RECOMMEND_CHANNEL_DISPLY_STATUS
}

message StartGameMatchResp {
	required BaseResp base_resp = 1;
}

// 组队房相关
message GameMatchMember {
	required uint32 uid = 1;
	required uint32 sex = 2;
	required string account = 3;
}
message GameMatchChannel {
	required ChannelDetailInfo channel_detail = 1;
	required uint32 max_member_count = 2;
	required uint32 cur_member_count = 3;
	repeated GameMatchMember game_match_member_list = 4;
	optional string game_name = 5;
	optional string icon_url = 6;
	required uint32 create_ts = 7;
}
// 组队首页
message GetGameMatchListHomePageReq {
	required BaseReq base_req 		= 1;
	required uint32 page = 2;
	required uint32 page_count = 3;
}
message GetGameMatchListHomePageResp {	
	required BaseResp base_resp 	= 1;	
	repeated GameMatchChannel game_match_channel_list = 2;
	required uint32 page = 3;     // 请求是第几页
	required bool reach_end = 4;  // 是否还存在后续页 ture表示到底了 没有了
    repeated GameMatchChannel novice_recommend_gm_channel_list = 5;     // 针对新用户的推荐房列表，当page==0 时有值。有值时与 game_match_member_list 无交集

}
message GetGameMatchListByTagIdReq {
	required BaseReq base_req 		= 1;
	required uint32 page = 2;
	required uint32 page_count = 3;
	required uint32 tag_id = 4;
}
message GetGameMatchListByTagIdResp {	
	required BaseResp base_resp 	= 1;	
	repeated GameMatchChannel game_match_channel_list = 2;
	required uint32 page = 3;     // 请求是第几页
	required bool reach_end = 4;  // 是否还存在后续页 ture表示到底了 没有了
	required uint32 tag_id = 5;
    repeated GameMatchChannel novice_recommend_gm_channel_list = 6;     // 针对新用户的推荐房列表，当page==0 时有值。有值时与 game_match_member_list 无交集
}

// 房间历史 （看看谁来过）
message ChannelHistoryInfo {
	required uint32 histroy_type  = 1;
	required uint32 ts = 2;
	required GenericMember member = 3;
}

message GetChannelHistoryReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	required uint32 begin_idx = 3;
	required uint32 limit = 4;
}

message GetChannelHistoryResp {
	required BaseResp base_resp  = 1;
	required uint32 channel_id = 2;
	repeated ChannelHistoryInfo history_list = 3;
	required uint32 all_history_cnt = 4;
}


// 房间礼物历史消费信息
message GetChannelGiftHistoryReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
}

message GetChannelGiftHistoryResp {
	required BaseResp base_resp  = 1;
	required uint32 channel_id = 2;
	required PresentSummary present_info = 3;
}


// 房间统计信息
message ChannelStatistics
{
	optional uint32 max_online = 1;              // 最高在线人数
	optional uint32 max_online_ts = 2;           // 最高在线人数 出现的时间
	optional uint32 collect_cnt = 3;             // 当前收藏房间的用户数
}
// 房间管理员信息 (个人房间才有独立的管理员)
message ChannelAdminInfo {
	required GenericMember member = 1;
	required uint32 admin_role   = 2; // see EChannelAdminRoleType
}

// 房间消费
message ChannelMemberConsumeInfo {
	required GenericMember member   = 1;
	required uint32 red_diamond_consume   = 2;
}

// 房间附加信息的bitmap类型 值为0x1 0x2 0x4 0x8 ...
enum EChannelExtendType
{
	ENUM_EXTEND_STAT = 1;  // 统计信息
	ENUM_EXTEND_ADMIN = 2; // 管理员列表 仅对个人房有效
	ENUM_EXTEND_CONSUM_TOPN = 4;     // 土豪榜 返回前N个用户 N由服务器控制 现在是3
	ENUM_EXTEND_HISTORY_TOPN = 8;    // 房间历史 历史进房间的用户TOP N的数量由服务器控制 现在是3
	ENUM_EXTEND_TOPIC_DETAIL = 16;   // 房间话题详情
	ENUM_EXTEND_WELCOME_MSG = 32;    // 房间欢迎语
}

message GetChannelExtendInfoReq {
	required BaseReq base_req  = 1;
	required uint32 channel_id = 2;
	required uint32 extend_type_bitmap = 3; // 基于 EChannelExtendType 的bitmap, 如果要请求统计信息就填0x1 如果要统计信息和管理员列表就填（0x1|0x2）= 0x3
}

message GetChannelExtendInfoResp {
	required BaseResp base_resp  = 1;
	required uint32 channel_id = 2;
	required uint32 extend_type_bitmap = 3; // 基于 EChannelExtendType 的bitmap, 如果要请求统计信息就填0x1 如果要统计信息和管理员列表就填（0x1|0x2）= 0x3
	
	optional ChannelStatistics  stat_info = 4;              // 统计信息
	repeated ChannelAdminInfo admin_list = 5;               // 管理员列表
	repeated ChannelMemberConsumeInfo consum_topn_list = 6; // 房间的消费榜
	repeated ChannelHistoryInfo history_list = 7; // 房间的历史列表
	optional string topic_detail = 8;             // 房间话题的详细内容
	optional string welcome_msg = 9;              // 房间欢迎语
}

// 设置管理员 目前只对 个人房 和 公会公开房 有效
message OperChannelAdminReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
	required uint32	oper_type    	= 3; // 1是增加管理员 2是删除管理员 
	required uint32	target_uid    	= 4;
	optional uint32	admin_role    	= 5; // 管理员类型 see EChannelAdminRoleType
	
	optional string	target_user_account = 6; // 如果 target_uid == 0 可以使用 target_user_account进行查询
}

message OperChannelAdminResp {
	required BaseResp base_resp 	 = 1;
	required uint32	channel_id		 = 2;
	required uint32	oper_type    	= 3; // 1是增加管理员 2是删除管理员 
	required uint32	target_uid    	= 4;
}


// 换自己的麦的位置
message ChannelChangeMicReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
	required uint32 target_mic_id = 3;   // 换麦位的目标麦位
}

message ChannelChangeMicResp {
	required BaseResp base_resp 	 = 1;
	required uint32	channel_id		 = 2;
	required MicrSpace target_mic_info      = 3; // 如果换成功 目标麦位的状态信息 只有UID信息和MICid信息
}

// 将指定用户放到指定麦位上
message TakeUserToChannelMicReq {
	required BaseReq base_req 		= 1;
	required uint32	channel_id		= 2;
	required uint32	target_uid    	= 3;
	required uint32 target_mic_id   = 4;
	
	optional string	target_ttid    	= 5;
}

message TakeUserToChannelMicResp {
	required BaseResp base_resp 	 = 1;
	required uint32	channel_id		 = 2;
	required uint32	target_uid    	 = 3;
	required MicrSpace mic_info      = 4;
}



//房间小游戏
enum ChannelGameType
{
	GAME_DRAW = 1;//抽签
	GAME_DICE = 2;//摇骰子
}

message ChannelGameReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;			
	required uint32 game_id = 3;								
}

message ChannelGameResp
{
	required BaseResp base_resp = 1;
}

//娱乐房麦上魔法表情
message SendMagicExpressionReq				
{
	required BaseReq base_req = 1;
	required uint32 channel_id=2;			//房间id
	required uint32 magic_id =3;			//表情id
	optional uint32 opt_max_num =4;	
	repeated uint32 magic_num_list=5;	
}

message SendMagicExpressionResp
{
	required BaseResp base_resp = 1;
}


// 修改房间属性(图标 描述 标签 ...)
enum EChannelModifyBitmapType
{
	ENUM_CHANNEL_MODIFY_ICON = 1;   // 图标
	ENUM_CHANNEL_MODIFY_DESC = 2;   // 标题标题
	ENUM_CHANNEL_MODIFY_LABEL = 4;  // 标签
	
	ENUM_CHANNEL_MODIFY_RECOMMEND_SWITCH_FLAG = 8;         // 是否加入推荐的开关
	ENUM_CHANNEL_MODIFY_LOCKSCREEN_SWITCH_FLAG = 16;       // 是否开启锁屏的开关
	ENUM_CHANNEL_MODIFY_AUTO_DISABLE_MIC_SWITCH_FLAG = 32; // 是否开启自动锁麦开关
	
	ENUM_CHANNEL_MODIFY_TOPIC_DETAIL = 64; // 话题详情
	ENUM_CHANNEL_MODIFY_WELCOME_MSG = 128; // 房间欢迎语
	
	ENUM_CHANNEL_MODIFY_DISABLE_ATTACHMENT_MSG_FLAG = 256; // 是否开启禁止公屏发图片开关
	ENUM_CHANNEL_MODIFY_DISABLE_LEVEL_LMT_FLAG = 512;     // 是否开启禁止5级以下用户在公屏发言开关
	ENUM_CHANNEL_MODIFY_LIVE_CONNECT_MIC_FLAG  = 1024;    // 是否开启开启电台直播连麦功能的开关
	ENUM_CHANNEL_MODIFY_NORMAL_QUEUE_UP_MIC_FLAG  = 2048; // 是否开启开启 普通房的 排麦功能的开关
}
message ModifyChannelExtentReq				
{
	required BaseReq base_req = 1;
	required uint32 channel_id=2;			// 房间id
	required uint32 modify_bitmap = 3;		// EChannelModifyBitmapType 的 bitmap
	optional bytes  icon = 4;               // 如果是要修改图标 这里是图标的二进制数据
	optional string desc = 5;               // 如果是修改话题标题 这里填话题标题 内容
	
	optional bool recommend_switch_flag = 6;        // 是否加入推荐的开关 0 关闭 1开启
	optional bool lockscreen_switch_flag = 7;       // 是否开启锁屏的开关 0 关闭 1开启
	optional bool auto_disable_mic_switch_flag = 8; // 是否开启自动锁麦开关 0 关闭 1开启
	
	optional string topic_detail = 9;               // 如果是修改话题详情 这里填话题详情内容
	optional string welcome_msg = 10;               // 如果是修改房间欢迎语 这里填房间欢迎语内容
	
	optional bool disable_attachment_msg_switch_flag = 11;  // 是否开启禁止公屏发图片开关 0 关闭 1开启
	optional bool disable_level_lmt_switch_flag = 12;       // 是否开启禁止5级以下用户在公屏发言开关 0 关闭 1开启
	optional bool open_live_connect_mic_switch_flag = 13;   // 是否开启电台直播连麦功能，0关闭 1开启
	optional bool open_normal_queue_up_mic_switch_flag = 14;   // 是否开启开启 普通房的 排麦功能，0关闭 1开启
}

message ModifyChannelExtentResp
{
	required BaseResp base_resp = 1;
	required uint32 channel_id=2;			// 房间id
	required uint32 modify_bitmap = 3;		// EChannelModifyBitmapType 的 bitmap
	optional string  icon_md5 = 4;
	optional string  desc = 5;              // 房间话题描述（标题）
	
	optional bool recommend_switch_flag = 6;        // 是否加入推荐的开关 0 关闭 1开启
	optional bool lockscreen_switch_flag = 7;       // 是否开启锁屏的开关 0 关闭 1开启
	optional bool auto_disable_mic_switch_flag = 8; // 是否开启自动锁麦开关 0 关闭 1开启
	
	optional string topic_detail = 9;               // 如果是修改话题详情 这里填话题详情内容
	optional string welcome_msg = 10;               // 如果是修改房间欢迎语 这里填房间欢迎语内容
	
	optional bool disable_attachment_msg_switch_flag = 11;      // 是否开启禁止公屏发图片开关 0 关闭 1开启
	optional bool disable_level_lmt_switch_flag = 12;       	// 是否开启禁止5级以下用户在公屏发言开关 0 关闭 1开启
	optional bool open_live_connect_mic_switch_flag = 13;       // 是否开启电台直播连麦功能，0关闭 1开启
	optional bool open_normal_queue_up_mic_switch_flag = 14;    // 是否开启开启 普通房的 排麦功能，0关闭 1开启
	
}

// 获取推荐频道列表
message GetRecommendChannelListReq {
    required BaseReq base_req    = 1;
    required uint32 page = 2;       // 第几页
	required uint32 page_count = 3; // 每页多少条记录
}

message ChannelTagInfo
{
	required uint32 channel_id = 1;
	required string	tag_info = 2;
	required string bk_color = 3;
}

message GetRecommendChannelListResp {
    required BaseResp base_resp       = 1;    
    repeated ChannelDetailInfo channel_list = 2;
	required uint32 page = 3;     // 请求是第几页
	required bool reach_end = 4;  // 是否还存在后续页 ture表示到底了 没有了
	
	repeated uint32 official_activity_list = 5;	// 官方活动列表
	repeated uint32 official_recommend_list = 6; // 官方推荐列表
	repeated ChannelTagInfo channel_taginfo_list = 7; // 标签信息
	repeated uint32 newuser_recommend_list = 8; //新用户推荐channelid列表
}

message ChannelRecommendInfo
{
	required ChannelDetailInfo detail_info = 1;
	required string	tag_name = 2;
	required string tag_color = 3;
	required bool is_highlight = 4;
}

message GetRecommendChannelListV2Req {
    required BaseReq base_req    = 1;
    required uint32 page = 2;       // 第几页
	required uint32 page_count = 3; // 每页多少条记录
}

message GetRecommendChannelListV2Resp {
    required BaseResp base_resp       = 1;    
	required uint32 page = 2;       // 请求是第几页
	required uint32 page_count = 3; // 每页多少条记录
	required bool reach_end = 4;    // 是否还存在后续页 ture表示到底了 没有了
	
	repeated ChannelRecommendInfo office_list = 5;         // 官方推荐列表 仅在page=0时有效
	repeated ChannelRecommendInfo special_top_list    = 6; // 特殊置顶列表 仅在page=0时有效
	
	repeated ChannelRecommendInfo channel_nomal_list = 7; // 普通列表
}

// 标签相关
enum FCTagType {
	FCTT_FUNNY_ROOM = 1;
	FCTT_GAME = 2;
}
message CardInfo { // 卡片信息
	required string card_name = 1;
	required string card_image = 2;
	required string back_image = 3;
	required string client_link = 4;
	optional uint32 tag_id = 5;
	optional uint32 user_count = 6;
}
message FCTagInfo { // 一级标签
	required string tag_name = 1;
	required uint32 tag_id = 2;
	optional uint32 fc_tag_type = 3;
}
message SCTagInfo { // 二级标签
	enum SCTagFlag
	{
		SCTF_HIDE_SETTING = 1;
		SCTF_HIDE_TAB = 2;
	}
	required string tag_name = 1;
	required string icon = 2;
	required uint32 tag_id = 3;
	optional bool is_show = 4; // 兼并到ext_flags
	optional string jump_url = 5;
	optional uint32 ext_flag = 6;//如果有,优先使用这个字段,兼容字段4,bit or SCTagFlag
	optional string bk_color = 7; //背景颜色
}
message TagInfo {
	required FCTagInfo fc_tag_info = 1;
	repeated SCTagInfo sc_tag_info_list = 2;
}

// 获取卡片
message GetChannelCardReq{
	required BaseReq base_req    = 1;
}
message GetChannelCardResp{
	required BaseResp base_resp	= 1;
	repeated CardInfo card_info_list = 2;
	required SCTagInfo sc_game_tag_info = 3;	
	optional bool game_on_top = 4;
	optional bool has_visit_game = 5;
}
// 获取标签列表
message GetChannelTagListReq{
	required BaseReq base_req    = 1;
}
message GetChannelTagListResp{
	required BaseResp base_resp	= 1;
	repeated TagInfo tag_info_list = 2;
}

// 首页 推荐语音直播房
message GetRecommendLiveChannelReq{
	required BaseReq base_req = 1;
}

message GetRecommendLiveChannelResp{
	required BaseResp base_resp	= 1;
	repeated ChannelDetailInfo channel_list = 2;
}

// 二级页 语音直播房
message GetLiveChannelListReq{
	required BaseReq base_req = 1;
	required uint32 page = 2;
	required uint32 page_count = 3;
}
message GetLiveChannelListResp{
	required BaseResp base_resp	= 1;
	repeated ChannelDetailInfo channel_list = 2;
	required uint32 page = 3;
	optional bool reach_end = 4;
	optional uint32 user_count = 5;
}

// 获取标签下的房间列表
message GetChannelListByTagIdReq{
	required BaseReq base_req    = 1;
	required uint32 tag_id = 2;
	required uint32 page = 3;
	required uint32 page_count = 4;
}
message GetChannelListByTagIdResp{
	required BaseResp base_resp	= 1;
	repeated ChannelDetailInfo channel_list = 2;
	required uint32 page = 3;
	required uint32 tag_id = 4;
	optional bool reach_end = 5;
	optional uint32 user_count = 6;
	repeated uint32 recommend_channel_list = 7;
}
// 获取房间标签
message GetChannelTagIdReq {
	required BaseReq base_req    = 1;
	required uint32 channel_id = 2;
}
message GetChannelTagIdResp{
	required BaseResp base_resp	= 1;
	required SCTagInfo sc_tag_info = 2;	
	required bool is_set = 3;
}
// 设置房间标签
message SetChannelTagIdReq {
	required BaseReq base_req    = 1;
	required uint32 channel_id = 2;
	required uint32 tag_id = 3;
}
message SetChannelTagIdResp{
	required BaseResp base_resp	= 1;
} 

// 刷新剩余cd
message GetChannelRefreshCDReq {
	required BaseReq base_req    = 1;
	required uint32 channel_id = 2;
}
message GetChannelRefreshCDResp {
	required BaseResp base_resp	= 1;
	required uint32 remain_seconds = 2;
}

// 刷新房间时间
message RefreshChannelReq {
	required BaseReq base_req    = 1;
	required uint32 channel_id = 2;
}
message RefreshChannelResp {
	required BaseResp base_resp	= 1;
	required uint32 remain_seconds = 2;
}

// 获取房间广告位
enum CHANNEL_ADV_TYPE
{
	CAT_CHANNEL_FUNNY = 0;
	CAT_CHANNEL_HOMEPAGE = 1;
}
message GetChannelAdvReq {
	required BaseReq base_req    = 1;
	optional uint32 adv_type = 2;
}
message ChannelAdv {
	required string pic_url =1;
	required string adv_url = 2;
}
message GetChannelAdvResp {
	required BaseResp base_resp	= 1;
	repeated ChannelAdv adv_list = 2;
}

message GetChannelHomeDetailReq{
	required BaseReq base_req    = 1;
}


message GetChannelHomeDetailResp{
	required BaseResp base_resp    = 1;
	repeated ChannelAdv adv_list = 2;
	repeated ChannelRecommendInfo channel_recommend_info_list = 3;
}

// 获取热门频道列表
message GetHotChannelListReq {
    required BaseReq base_req    = 1;
	optional uint32 count_limit  = 2; // 列表数量 默认10
}

message GetHotChannelListResp {
    required BaseResp base_resp       = 1;    
    repeated ChannelDetailInfo channel_list = 2;
	repeated ChannelTagInfo channel_taginfo_list = 3; // 标签信息
	repeated uint32 official_activity_list = 4;	// 官方活动列表
	repeated uint32 official_recommend_list = 5; // 官方推荐列表
}

enum EChannelShowSwitchType {
	ENUM_CHANNEL_SHOW_SWITCH_HOT = 1;	// 热门频道
}

// 获取房间tab的频道列表开关
message GetChannelShowSwitchReq {
	required BaseReq base_req	= 1;
	required uint32 switch_type = 2;	// EChannelShowSwitchType
}

message GetChannelShowSwitchResp {
	required BaseResp base_resp	= 1;
	required uint32 switch_type = 2;	// EChannelShowSwitchType
	required uint32 switch_status = 3;	// 0.off 1.on
}



// 结束直播 
message FinishChannelLiveReq
{
	required BaseReq base_req	= 1;
	required uint32 channel_id = 2;
}

message FinishChannelLiveResp
{
	required BaseResp base_resp	= 1;
	
	required uint32 channel_id = 2;
	required uint32 user_cnt = 3;   //从直播开始到结束时 用户的人数
	required uint32 enter_cnt = 4;  //从直播开始到结束时 进房的次数
	required uint32 tcoin_cnt = 5;  //从直播开始到结束时 T豆的数
	required uint32 time_second = 6;//从直播开始到结束时 持续的秒数
}

// 发起连麦申请
message LiveChannelMicApplyReq {
	required BaseReq base_req	= 1;
	required uint32 channel_id = 2;
	required bool is_cancel = 3; // ture表示 取消之前的申请
}

message LiveChannelMicApplyResp {
	required BaseResp base_resp	= 1;
	required uint32 channel_id = 2;
	required bool is_cancel = 3; // ture表示 取消之前的申请
}

// 处理连麦请求
message LiveChannelMicHandleReq {
	required BaseReq base_req	= 1;
	required uint32 channel_id = 2;
	repeated uint32 uid_list = 3; 
	required bool is_allow = 4;
}

message LiveChannelMicHandleResp {
	required BaseResp base_resp	= 1;
	required uint32 channel_id = 2;
	repeated uint32 success_uid_list = 3;
	required bool is_allow = 4;
}

// 获取连麦申请人列表
message LiveChannelApplyMember
{
    required string account   = 1;        // 成员账号
    required uint32 uid       = 2;        // 成员uid
    required string nick_name = 3;        // 成员昵称
	optional uint32 sex   = 4;            // 性别
}

message GetLiveChannelMicApplyUserListReq
{
	required BaseReq base_req	= 1;
	required uint32 channel_id = 2;
	optional uint32 limit_cnt = 3;
}

message GetLiveChannelMicApplyUserListResp
{
	required BaseResp base_resp	= 1;
	repeated LiveChannelApplyMember	member_list = 2;
	optional uint32 limit_cnt = 3;      // 请求值
	optional uint32 all_apply_cnt = 4 ; // 总的申请人数量
}

//获取用户的语音直播房信息
message GetUserChannelLiveReq
{
    required BaseReq base_req   = 1;
}

message GetUserChannelLiveResp
{
    required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
}
// 房间送礼统计
message ChannelPresentCountReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;
	required bool on_or_off = 3; //ture on; false off;
}

message ChannelPresentCountResp
{
	required BaseResp base_resp = 1;
}

// 用户进房时查询房间是否开启送礼统计
message GetChannelPresentCountStateReq
{
    required BaseReq base_req   = 1;
    required uint32 channel_id = 2;
}

// 房间当前麦上用户的礼物统计
message ChannelMicPresentCountInfo
{
    required uint32 uid = 1;
    required uint32 price = 2;
}

message GetChannelPresentCountStateResp
{
	required BaseResp base_resp = 1;
    required bool status = 2;//true ,已开启;false,已关闭
    repeated ChannelMicPresentCountInfo present_count_infos = 3;//房间当前各麦位用户的礼物统计
}

// 房间送礼的火箭跑道信息
message ChannelPresentRunwayInfo
{
	required uint32 uid = 1;
	required string account = 2;
	required string nickname = 3;
	optional string face_md5 = 4;
	required uint32 channel_id = 5;
	required uint32 level = 6;				// 火箭/跑道等级 
	required uint32 expired_time = 7;
	required uint32 present_value = 8;		// 当前礼物值
	required uint32 present_value_min = 9;	// 此等级的最低礼物值
	required uint32 present_value_max = 10;	// 此等级的最高礼物值
	optional uint32 present_add_value = 11;	// 礼物的增加值(进房退房之类的情况下，这里会是0)
	optional uint32 item_id = 12;			// 导致火箭变化的礼物id
}

// 跑道特效
message ChannelPresentRunwayEffect
{
	required uint32 runway_present_base = 1;	// 单次每送出指定值的礼物，可额外增加跑道时间
	required uint32 runway_add_seconds = 2;	// 跑道在礼物的播放时间基础上额外再增加的时间（秒）
	required uint32 max_runway_add_seconds = 3; // 跑道最多可增加的时间（秒）
}

// 获取房间内的火箭跑道信息
message GetChannelPresentRunwayListReq
{
	required BaseReq base_req   = 1;
    required uint32 channel_id = 2;
}

message GetChannelPresentRunwayListResp
{
	required BaseResp base_resp = 1;
	repeated ChannelPresentRunwayInfo runway_list = 2;
	optional ChannelPresentRunwayEffect runway_effect = 3;
}

// 房间投票PK

//PK类型
enum ChannelVotePkType
{
	ChannelVotePkType_INVALID_TYPE = 0;    // 无效
	ChannelVotePkType_VOTE_PK_TYPE = 1;    // 观众点击投票
	ChannelVotePkType_DIAMOND_PK_TYPE = 2; // 用送红钻礼物方式投票
    ChannelVotePkType_TBEAN_PK_TYPE = 3;   // 用送T豆礼物方式投票
}

enum ChannelVotePKNumType
{
	ChannelVotePKNum_INVALID_TYPE = 0; //无效
	ChannelVotePKNum_TWO_PERSON = 1;   //两人
	ChannelVotePKNum_MUTI_PERSON = 2;  //多人
}

//排行榜中玩家排名信息
message ChannelVotePkCompetitor
{
	required uint32 uid = 1; 
	required string nickname = 2; 
	required string account = 3; 
	optional string face_md5 = 4; 
	
	required uint32 vote = 5; //得分
	required uint32 rank = 6; //排名
	
}

//PK基本信息
message ChannelPkInfo
{
	required uint32 uid = 1; //发起玩家
	required uint32 channel_id = 2; 
	required uint32 type = 3; //enum ChannelVotePkType PK类型
	required uint32 duration_min = 4; 
	required uint32 start_timestamp  = 5; //开始时间戳
	required uint32 person_type = 6; //ChannelVotePKNumType
	optional uint32 vote_cnt = 7; //如果是投票类型的情况，每个观众的票数
	optional string pk_name = 8; //PK名字
}

//PK全量信息
message ChannelVotePKRankInfo
{
	required ChannelPkInfo info = 1; //PK基本信息
	repeated ChannelVotePkCompetitor competiter_list = 2; //排行榜列表
}

//PK全量PUSH信息
message ChannelVotePKPushNotifyInfo
{
	enum EPKNotifyStatus
	{
		ENUM_PK_NOTIFY_STATUS_START = 1;   // PK 开始 
		ENUM_PK_NOTIFY_STATUS_ING   = 2;   // PK 进行中
		ENUM_PK_NOTIFY_STATUS_TIMEOUT = 3; // PK 时间到结束
		ENUM_PK_NOTIFY_STATUS_CANCEL = 4;  // PK 被取消
	}
	
	required ChannelPkInfo info = 1;                      //PK基本信息
	repeated ChannelVotePkCompetitor competiter_list = 2; //排行榜列表
	
	optional uint32 notify_status = 3;   // EPKNotifyStatus  

	// 操作者数据 仅在PK 开始和被取消时 需要填写
	optional uint32 op_uid = 5;           // 操作者UID
	optional string op_nickname = 6;      // 操作者昵称
	optional string op_account = 7; 
	optional string op_face_md5 = 8;      // 操作者头像MD5
}

//房管设置PK开始
message ChannelVotePkStartReq
{
	required BaseReq base_req   = 1;
	required uint32 channel_id = 2;
	required uint32 duration_min = 3; // 持续分钟 单位分钟
	required uint32 type = 4;         // enum ChannelVotePkType PK类型
	repeated uint32 uid_list = 5;     // 参与UID
	optional uint32 vote_cnt = 6;     // 每个观众的票数
	optional string pk_name = 7;      //PK名
}

message ChannelVotePkStartResp
{
	required BaseResp base_resp = 1;
	required uint32 channel_id = 2;
	required uint32 duration_min = 3; //持续分钟
	required uint32 start_timestamp = 4; //服务端时间，PK开始时间戳  channel_id和start_timestamp唯一确定一个PK对象
	required uint32 left_vote = 5; //自己剩余票数
}

//房管取消PK
message ChannelPkCancelReq
{
	required BaseReq base_req   = 1;
	required uint32 channel_id = 2;
	required uint32 start_timestamp = 3;
}

message ChannelPKCancelResp
{
	required BaseResp base_resp = 1;
}

//  获取PK全量信息,刚进房的时候使用
message GetChannelVotePKInfoReq
{
	required BaseReq base_req   = 1;
	required uint32 channel_id = 2;
}

message GetChannelVotePKInfoResp
{
	required BaseResp base_resp = 1;
	optional uint32 left_vote = 2; // 如果是观众点击投票的PK 这类显示观众的剩余票数
	optional ChannelVotePKRankInfo pk_info = 3; //PK全量信息
}

// 观众点击投票
message ChannelVotePkVoteReq
{
	required BaseReq base_req   = 1;
	required uint32 to_uid = 2; //目标玩家
	required uint32 vote_cnt = 3; //投票数量
	required uint32 channel_id = 4; 
	required uint32 start_timestamp  = 5; //开始时间戳
}

message ChannelVotePkVoteResp
{
	required BaseResp base_resp = 1;
	required uint32 left_vote = 2; //剩余票数
}


// 房间画布
message Point
{
	required uint32 x = 1;
	required uint32 y = 2;
}

enum LineType
{
	PURE_COLOR = 0; //纯色线
	MULT_COLOR = 1; //荧光笔
	ICON_IMAGE = 2; //icon
	RUBBER = 3;     //橡皮擦
}

//线结构，由很多点组成。
message Line
{
	required uint32 lineID = 1;
	required uint32 uid  = 2;
	required uint32 size = 3;
	required string color = 4;
	repeated Point  PointList = 5;
	optional string para_id = 6; //LinePara结构ID
	optional uint32 line_type = 7; //enum LineType
}

//画结构，由很多线组成
message Picture 
{
	required uint32 channelID = 1;
	repeated Line   lineList   = 2;
}

//撤回一条线
message CancelLineReq
{
	required BaseReq base_req   = 1;
	required uint32 channelID = 2;
	required uint32 lineID  = 3;
}

message CancelLineResp
{
	required BaseResp base_resp = 1;
}

//拿整幅画
message GetPictureReq
{
	required BaseReq base_req   = 1;
	required uint32 channelID = 2;
}

message GetPictureResp
{
	required BaseResp base_resp = 1;
	optional Picture  picture = 2;
	optional uint32   operID  = 3;
}

//房管用于删除整幅画
message RemovePictureReq
{
	required BaseReq base_req   = 1;
	required uint32 channelID = 2;
}

message RemovePictureResp
{
	required BaseResp base_resp = 1;
}

//创建线
message CreatLineReq
{
	required BaseReq base_req   = 1;
	required uint32 channelID = 2;
	required uint32 size = 3;
	required string color = 4; //para_id没有的情况下，客户端给透明色，兼容旧版本
	optional string para_id = 5; //LinePara结构para_id
	optional uint32 line_type = 6; //enum LineType
}

message CreatLineResp
{
	required BaseResp base_resp = 1;
	required uint32 channelID = 2;
	required uint32 lineID = 3;
	required uint32 operID = 4;
}

//向线中加点
message AddPointListReq
{
	required BaseReq base_req   = 1;
	required uint32 channelID = 2;
	required uint32 lineID = 3;
	repeated Point pointList = 4;
}

message AddPointListResp
{
	required BaseResp base_resp = 1;
}

//清楚某个玩家画的所有线
message CancelByUidReq
{
	required BaseReq base_req   = 1;
	required uint32 channelID =2;
}

message CancelByUidResp
{
	required BaseResp base_resp = 1;
}

message LinePara
{
	required string para_id = 1; 
	required string name = 2;
	required string preview_url = 3; //预览图URL
	required string icon_url = 4; //小图资源 ZIP URL
	repeated string color_list = 5; // 荧光笔颜色
	required uint32 type = 6;  //enum LineType
}

//取画笔配置列表
message GetLineParaListReq
{
	required BaseReq base_req   = 1;
}

message GetLineParaListResp
{
	required BaseResp base_resp = 1;
	repeated LinePara line_list = 2;
}

enum DRAW_GAME_SWITCH_STATUS
{
	ENUM_DRAW_GAME_UNKUNOW = 0;
	ENUM_DRAW_GAME_OFF = 1;
	ENUM_DRAW_GAME_ON  = 2;
}

//玩家开始关闭画板。用于确定玩家的画画状态
message SetDrawStatusReq
{
	required BaseReq base_req   = 1;
	required uint32 channelID = 2;
	required uint32 status = 3; // DRAW_GAME_SWITCH_STATUS
}

message SetDrawStatusResp
{
	required BaseResp base_resp = 1;
	required uint32 status    = 2;
}

//房管开启关闭画板功能
message SetBoardStatusReq
{
	required BaseReq base_req   = 1;
	required uint32 channelID = 2;
	required uint32 status    = 3;// DRAW_GAME_SWITCH_STATUS
}

message SetBoardStatusResp
{
	required BaseResp base_resp = 1;
	required uint32 status    = 2;
}


message GetBoardStatusReq
{
	required BaseReq base_req   = 1;
	required uint32 channelID = 2;
}

message GetBoardStatusResp
{
	required BaseResp base_resp = 1;
	required uint32 channelID = 2;
	required uint32 status    = 3;
	optional uint32 interval = 4; //加点协议间隔
}

//拿正在绘画UID列表
message GetDrawStatusListReq
{
	required BaseReq base_req = 1;
	required uint32 channelID = 2;
}

message GetDrawStatusListResp
{
	required BaseResp base_resp = 1;
	repeated uint32 uidList = 2;
}

message ChannelDrawGamePushNotifyInfo
{
	enum ENUM_DrawGameNotifyEvent
	{
		ENUM_DRAW_GAME_NOTIFY_EVENT_ADD_LINE = 1;   // 添加线
		ENUM_DRAW_GAME_NOTIFY_EVENT_CANCEL  = 2;   // 取消线
		ENUM_DRAW_GAME_NOTIFY_EVENT_REMOVE = 3; // 清空图
		ENUM_DRAW_GAME_NOTIFY_EVENT_ADD_POINT = 4; //向线中加点
		ENUM_DRAW_GAME_NOTIFY_EVENT_CANCEL_BY_UID = 5; //删除UID对应的全部线
		ENUM_DRAW_GAME_NOTIFY_EVENT_STATUS_EVENT  = 6; //房主开启关闭画板事件
		ENUM_DRAW_GAME_NOTIFY_EVENT_DRAW_STATUS = 7;   //玩家正在画画状态
		ENUM_DRAW_GAME_NOTIFY_EVENT_NOTIFY    = 8;   //10分钟后关闭公屏提示
	}
	
	required uint32 channelID = 1;
	required uint32 event_type = 2;   // ENUM_DrawGameNotifyEvent
	required uint32 operID = 3;		  //操作ID，每次操作都+1
	
	repeated Point  pointList = 4;
	optional uint32 uid    = 5;       //操作用户UID
	optional uint32 lineID    = 6;
	optional Line   line      = 7;
	optional uint32 broadStatus = 8;  //画板开始、关闭状态 DRAW_GAME_SWITCH_STATUS
	optional uint32 drawStatus  = 9; //玩家正在画画状态 DRAW_GAME_SWITCH_STATUS
}

//有颜色配置的公屏消息
message ChannelColorNotifyMsg
{
	required uint32 channelID = 1;
	required string msg = 2;
	required string color = 3;
	optional uint32 uid = 4;
	optional string account = 5;
	optional string nickname = 6;
	optional string title = 7;
}

// 普通房间的排麦申请 （或者取消申请）
message ChannelNormalQueueUpMicApplyReq
{
	required BaseReq base_req = 1;
    required uint32 channel_id = 2;
	required bool is_cancel = 3;			// 0 申请 1 取消
}

message ChannelNormalQueueUpMicApplyResp
{
	required BaseResp base_resp = 1;
	required bool is_cancel = 2;
	repeated LiveChannelApplyMember apply_user_list = 3;
}

// 获取普通房间的排麦列表
message GetChannelNormalQueueUpMicListReq
{
	required BaseReq base_req = 1;
	required uint32 channel_id = 2;
	required uint32 offset = 3;
	required uint32 limit = 4;
}
message GetChannelNormalQueueUpMicListResp
{
	required BaseResp base_resp = 1;
    required uint32 channel_id = 2;
	repeated LiveChannelApplyMember apply_user_list = 3;
	required uint32 all_apply_cnt = 4;
	required uint32 offset = 5;
	required uint32 limit = 6;
}

/*------------------------------------------------ 开黑新人推荐房 房主设置展示状态 ------------------------------*/
enum NOVICE_RECOMMEND_CHANNEL_DISPLY_STATUS
{
  ENUM_NOT_RECOMMEND = 0;           // 不是新人开黑推荐房
  ENUM_RECOMMEND_DISPLAY = 1;       // 在新人推荐位展示
  ENUM_RECOMMEND_HIDE =  2;         // 不在新人推荐位展示
}

message GetNoviceRecommendChannelStatusReq
{
	required BaseReq base_req = 1;
    required uint32 cid = 2;
}

message GetNoviceRecommendChannelStatusResp
{
	required BaseResp base_resp = 1;
    required uint32 recommend_status = 2;   // see NOVICE_RECOMMEND_CHANNEL_DISPLY_STATUS
}

/*------------------------------------------------ 开黑新人推荐房 房主设置展示状态 ------------------------------*/

message ModifyChannelIMMsg
{
	required uint32 seq_id = 1; //消息序列
	required uint32 channel_id = 2; //房间消息
	required bytes bin_msg = 3; //channelim::ChannelCommonMsg 序列化;
}

/*------------entertainmentRecommendSvr begin----------------*/

enum ChannelCategory
{
	Activity_BIG = 0; //大活动房
	Activity_SAMLL = 1; //小活动房
	Used_Enter = 2; //曾经进入过
	Normal_Level = 3; //普通等级房
	Hot_Channel  = 4; //热门房
}

//房间0麦位玩家信息
message McSimpleInfo
{
	required uint32 uid = 1;

	optional string account = 2;
	optional string nick_name = 3;
	optional string head_img = 4;
}

//房间头像、房间名等channel服务数据
message ChannelCommonBaseInfo
{
	required uint32 channel_id 		= 1;
	required string name 			= 2;
	required string icon_md5 		= 3; //房间头像
	required uint32 online_cnt 		= 4; //在线人数
	required uint32 display_id 		= 5;  // 显示Id

	optional uint32 app_id          = 6;  // 频道对应的APPID
	optional bool   has_pwd         = 7;  // 是否有密码
	optional uint32 channel_type    = 8;  // 频道类型 see ga::ChannelType
	optional uint32 bind_id         = 9;
	optional uint32 switch_flag     = 10;  // 各种房间开关的标记 
	optional uint32 creater_uid     = 11;
	optional uint32 create_ts       = 12;

	optional string topic_title     = 13;  // 房间话题描述（标题）
	optional string passwd          = 14;  // 密码 明文
}

//房间等级、tagID等信息，entertainmentRecommend服务数据
message ChannelRecommendSimpleInfo
{
	required uint32 channel_level = 1; //enum ChannelLevel
	required uint32 category = 2;  //enum ChannelCategory 房间类型
	required uint32 tag_id   = 3;  //tag_id
	optional string sub_tag  = 4;  //配置的一个标签，TOP10什么的
}

message RecommendChannelInfo
{
	required ChannelCommonBaseInfo channel_base_info = 1;
	required ChannelRecommendSimpleInfo channel_simple_info = 2;
	optional McSimpleInfo mc_simple_info = 3;
}

//tag配置
message ChannelTagConfigInfo
{
	required uint32 tag_id = 1;
	required string name = 2;
	required string bk_color = 3;
	optional string icon = 4;
}

//请求推荐房间数据协议
message GetRecommonChannelListReq
{
	required BaseReq base_req = 1;
	required uint32 start = 2; //偏移
	required uint32 count = 3; //数量
}

message GetRecommonChannelListResp
{
	required BaseResp base_resp = 1;
	repeated RecommendChannelInfo recommon_channel_list = 2;
	required bool is_end = 3; //是否到头了
}

//按tagID请求房间数据
message GetRecommonChannelListByTagIdReq
{
	required BaseReq base_req = 1;
	required uint32 start = 2; //偏移
	required uint32 count = 3; //数量
	required uint32 tag_id = 4;
}

message GetRecommonChannelListByTagIdResp
{
	required BaseResp base_resp = 1;
	repeated RecommendChannelInfo recommon_channel_list = 2;
	required bool is_end = 3; // 是否到头了
}

//拿tag数据
message GetChannelTagInfoReq
{
	required BaseReq base_req = 1;
}

message GetChannelTagInfoResp
{
	required BaseResp base_resp = 1;
	repeated ChannelTagConfigInfo channel_tag_list = 2;
}

//拿热门数据
message GetHotChannelReq
{
	required BaseReq base_req = 1;
}

message GetHotChannelResp
{
	required BaseResp base_resp = 1;
	repeated RecommendChannelInfo recommon_channel_list = 2;
	optional bool is_end = 3;
}
/*------------entertainmentRecommendSvr end----------------*/
