syntax = "proto3";

package cybros.arbiter.v2;

import "v2/global.proto";

message Callback {
  message HTTPCallback {
    string url = 1;
  }

  message GRPCCallback {
    string url = 1;
  }

  message KafkaCallback {
  }

  oneof callback {
    HTTPCallback http_callback = 1;
    GRPCCallback grpc_callback = 2;
    KafkaCallback kafka_callback = 3;
  }

  map<string, string> params = 4;
}

message CallbackData {
  TaskContext task_context = 1;
  ScanResult scan_result = 2;
  map<string, string> params = 3;
}