syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package gamerecommendsvr;


/******************************************************************/
// 游戏标签
/******************************************************************/
message TagInfo
{
	optional uint32 tag_id = 1;
	optional string tag_name= 2;
	optional uint32 tag_type = 3; // cardDef.h E_RECOMMEND_TAG_TYPE
}

// 获取游戏标签列表
message GetAllGameTagInfoListReq 
{
}

message GetAllGameTagInfoListResp 
{
    repeated TagInfo tag_list = 1; 
}

message GetGameTagsByIdListReq 
{
	repeated uint32 tag_id_list = 1;
}

//根据标签名称查询标签
message GetGameTagInfoListByNameReq 
{
	required string tag_name = 1;
}


// 添加游戏标签
message CreateGameTagInfoReq 
{
	required string tag_name= 1;
	optional uint32 tag_type = 2; // cardDef.h E_RECOMMEND_TAG_TYPE
}
message CreateGameTagInfoResp
{
	required TagInfo tag_info = 1; 
}

// 删除游戏标签
message DelGameTagInfoReq 
{
	required uint32 tag_id = 1;
}
message DelGameTagInfoResp
{
}

// 根据游戏ID获取与该游戏具有相同标签的同类游戏
message GetSameTagGameListReq 
{
	required uint32 game_id = 1; // 请求的游戏ID
	required uint32 count = 2;
}
message GetSameTagGameListResp
{
	repeated uint32 game_id_list = 1;  // 与请求的游戏ID具有某种相同tag信息的同类游戏
	optional TagInfo tag_info = 2; 
}

/******************************************************************/
// 卡片信息
/******************************************************************/
//
message GameActivityInfo
{
	required string title = 1;
	required string url = 2;          // 活动链接
	required string img_url = 3;      // 配图链接
	required string sub_title = 4;    // 副标题 或者 描述
	required uint32 last_update_ts = 5; // 最后更新时间
	required uint32 begin_time = 6;     // 活动启动时间
	required uint32 end_time = 7;       // 活动下线时间
}

// 复合游戏卡(2.9.2版本之后不显示这种卡片)
message ComplexGameCard
{
	required uint64 card_id = 1;
	required uint32 game_id = 2;
	repeated TagInfo tag_list = 3;
	optional string img_url = 4;            // 卡片配图链接 如果有的话
	optional GameActivityInfo act_info = 5; // 
}


// 游戏下载卡(2.9.2版本之后不显示这种卡片)
message SimpleGameCard
{
	required uint64 card_id = 1;
	required uint32 game_id = 2;
	optional string img_url = 3;      // 卡片配图链接 如果有的话
	repeated TagInfo tag_list = 4;
}

// 固定榜单卡
message FixedTopListCard
{
	required uint64 card_id = 1;
	required string title = 2;
	repeated uint32 gameid_list = 3;
	optional uint32 last_update_ts = 4;      // 榜单最后更新时间
	optional uint32 spec_toplist_type = 5;     // E_RECOMMEND_TOPLIST_TYPE , 3:特殊位置的固定榜单 仅运营后台需要该字段
}

// 标签榜单卡
message TagTopListCard
{
	required uint64 card_id = 1;
	required string title = 2;
	required TagInfo tag = 3;     // 标签
	repeated uint32 gameid_list = 4;
	optional uint32 last_update_ts = 5;      // 榜单最后更新时间
	optional uint32 spec_toplist_type = 6;     // E_RECOMMEND_TOPLIST_TYPE , 3:特殊位置的固定榜单 仅运营后台需要该字段
}

// 活动卡
message OperActivityCard
{
	required uint64 card_id = 1;
	required GameActivityInfo act_info = 2; // 
	optional uint32 game_id = 3;           // 活动对应的游戏ID
	optional string narrow_img_url = 4;    // 附加的窄板的活动图片地址 仅用于运营后台录入 logic不要使用该字段
	optional string narrow_jump_url = 5;   // 附加的窄板的活动跳转地址 仅用于运营后台录入 logic不要使用该字段
}

message RecommendIntefaceCard
{
	required uint32 card_type = 1; // see ga::RecommendCardType
	optional bytes  card_detail = 2; // 即序列化后的【本文件内的】各个卡片协议 ComplexGameCard / SimpleGameCard / FixedTopListCard / TagTopListCard / OperActivityCard
}


// 获取榜单类型卡列表
message GetAllTopListCardReq 
{ 
	required uint32 card_type = 1; // see ga::RecommendCardType 仅支持 CARD_TYPE_FIXED_LIST / CARD_TYPE_TAG_LIST两种
}
message GetAllTopListCardResp
{
	repeated RecommendIntefaceCard card_list = 1;
}

// 获取榜单类型卡列表
message GetAllTopListCardByTagReq 
{ 
	required uint32 tag_id = 1;
}

// 添加榜单类型卡
message AddTopListCardReq 
{
	required uint32 card_type = 1; // see ga::RecommendCardType 仅支持 CARD_TYPE_FIXED_LIST / CARD_TYPE_TAG_LIST两种
	required string title = 2;
	repeated uint32 gameid_list = 3;
	optional TagInfo tag = 4;               // 标签 类型为 CARD_TYPE_TAG_LIST 时需要填写
	optional uint32 spec_toplist_type = 5;  // E_RECOMMEND_TOPLIST_TYPE , 3:特殊位置的固定榜单
}

message AddTopListCardResp
{
	required uint64 card_id = 1;
}

// 删除榜单类型卡
message DelTopListCardReq 
{
	required uint64 card_id = 1;
}

message DelTopListCardResp
{
}

// 全量新榜单类型卡中的游戏列表
message FullUpdateTopListCardGameListReq 
{
	required uint64 card_id = 1;
	repeated uint32 gameid_list = 2;
}

message FullUpdateTopListCardGameListResp
{
}

// 获取全量的活动卡
message GetAllOperActivityCardReq
{
}
message GetAllOperActivityCardResp
{
	repeated OperActivityCard card_list = 1;
}

// 添加活动类型卡
message AddOperActivityCardReq 
{
	required GameActivityInfo act_info = 1; // 
	optional uint32 game_id = 2;            // 活动对应的游戏ID
	optional string narrow_img_url = 3;    // 附加的窄板的活动图片地址 仅用于运营后台录入
	optional string narrow_jump_url = 4;   // 附加的窄板的活动跳转地址 仅用于运营后台录入
}

message AddOperActivityCardResp
{
	required uint64 card_id = 1;
}

// 删除活动类型卡
message DelOperActivityCardReq 
{
	required uint64 card_id = 1;
}

message DelOperActivityCardResp
{
	
}

// 取一个活动卡
message GetOperActivityCardReq
{
	required uint64 card_id = 1;
}
message GetOperActivityCardResp
{
	required OperActivityCard card_info = 1;
}

// 更新一个活动卡
message UpdateOperActivityCardReq
{
	required OperActivityCard card_info = 1;
}
message UpdateOperActivityCardResp
{
}

// 获取用户的卡片列表
message GetUserCardListReq 
{
    required uint32 uid = 1;             // 
    required uint32 start_idx = 2;
	required uint32 count_limit = 3;
	optional uint32 ex_gameid = 4;       // 附加携带的 一定要取的复合游戏卡对应的游戏ID 
	optional uint32 version_id = 5;      // 版本ID 默认为0，2.9.2以上用户为1
}

message GetUserCardListResp 
{
    repeated RecommendIntefaceCard card_list = 1; // 用户的游戏推荐卡片列表
}

// account 服务触发 用户安装的游戏更新 
message NotifyUserInstallGameUpdateReq 
{
    required uint32 uid = 1;             // 
    repeated uint32 install_gameid_list = 2;
	repeated uint32 uninstall_gameid_list = 3;
}

message NotifyUserInstallGameUpdateResp 
{
}


// 根据游戏ID获取标签信息
message GetGameTagListReq 
{
    required uint32 game_id = 1;       // 
}




service gamerecommendsvr{
    option( tlvpickle.Magic ) = 15345;

	// 游戏标签列表
	rpc GetAllGameTagInfoList ( GetAllGameTagInfoListReq ) returns ( GetAllGameTagInfoListResp ) {
        option( tlvpickle.CmdID ) = 1;										
	    option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid> ";	
    }    
	
	rpc CreateGameTagInfo ( CreateGameTagInfoReq ) returns ( CreateGameTagInfoResp ) {
        option( tlvpickle.CmdID ) = 2;										
	    option( tlvpickle.OptString ) = "t:x:";							
        option( tlvpickle.Usage ) = "-t <tag name> -x <tag type 1:game 2:toplist>";	
    }   
	
	rpc DelGameTagInfo ( DelGameTagInfoReq ) returns ( DelGameTagInfoResp ) {
        option( tlvpickle.CmdID ) = 3;										
	    option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <tag id> ";	
    }   
	
	// 榜单类型的卡
	rpc GetAllTopListCard ( GetAllTopListCardReq ) returns ( GetAllTopListCardResp ) {
        option( tlvpickle.CmdID ) = 6;										
	    option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <Card Type, only suppot 14:tag_list 16:fixed_list>";	
    }   
	
	rpc AddTopListCard ( AddTopListCardReq ) returns ( AddTopListCardResp ) {
        option( tlvpickle.CmdID ) = 7;										
	    option( tlvpickle.OptString ) = "t:x:g:e:";							
        option( tlvpickle.Usage ) = "-t <Card Type, only suppot 14:tag_list 16:fixed_list> -x <TopList Name> -g <gameID_list can use,split> -e <tag id>";	
    } 
	
	rpc DelTopListCard ( DelTopListCardReq ) returns ( DelTopListCardResp ) {
        option( tlvpickle.CmdID ) = 8;										
	    option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <TopListCard ID>";	
    } 
	
    // 获取用户的卡片列表
	rpc GetUserCardList ( GetUserCardListReq ) returns ( GetUserCardListResp ) {
        option( tlvpickle.CmdID ) = 9;										
	    option( tlvpickle.OptString ) = "u:x:e:t:";							
        option( tlvpickle.Usage ) = "-u <uid> -x <begin idx> -e <ex gameid> -t <versionID>";	
    }
	
	// 根据TagID获取标签信息
	rpc GetGameTagsByIdList ( GetGameTagsByIdListReq ) returns ( GetAllGameTagInfoListResp ) {
        option( tlvpickle.CmdID ) = 10;										
	    option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <tag_id 1> ";	
    }
	
	// 更新用户安装的游戏(account_logic调用)
	rpc NotifyUserInstallGameUpdate ( NotifyUserInstallGameUpdateReq ) returns ( NotifyUserInstallGameUpdateResp ) {
        option( tlvpickle.CmdID ) = 11;										
	    option( tlvpickle.OptString ) = "u:x:g:";							
        option( tlvpickle.Usage ) = "-u <uid> -x <install game id> -g <uninstall game id> ";	
    }
	
	// 全量更新 指定榜单类型卡中的游戏列表
	rpc FullUpdateTopListCardGameList ( FullUpdateTopListCardGameListReq ) returns ( FullUpdateTopListCardGameListResp ) {
        option( tlvpickle.CmdID ) = 12;										
	    option( tlvpickle.OptString ) = "u:x:g:";							
        option( tlvpickle.Usage ) = "-u <uid> -t <toplist card id> -g <gameID_list can use,split> ";	
    }
	
	// 获取所有运营活动类型的卡片
	rpc GetAllOperActivityCard ( GetAllOperActivityCardReq ) returns ( GetAllOperActivityCardResp ) {
        option( tlvpickle.CmdID ) = 14;										
	    option( tlvpickle.OptString ) = "u:";							
        option( tlvpickle.Usage ) = "-u <uid>";	
    }   
	
	//创建一个运营活动类型的卡片
	rpc AddOperActivityCard ( AddOperActivityCardReq ) returns ( AddOperActivityCardResp ) {
        option( tlvpickle.CmdID ) = 15;										
	    option( tlvpickle.OptString ) = "x:g:m:j:b:e:";							
        option( tlvpickle.Usage ) = "-x <name> -g <gameID> -m <img_url> -j <jump url> -b <begin timestamp> -e <end timestamp>";	
    } 
	
	// 删除指定运营活动类型的卡片
	rpc DelOperActivityCard ( DelOperActivityCardReq ) returns ( DelOperActivityCardResp ) {
        option( tlvpickle.CmdID ) = 16;										
	    option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <OperActivityCard id>";	
    } 
	
	// 获取指定运营活动类型的卡片
	rpc GetOperActivityCard ( GetOperActivityCardReq ) returns ( GetOperActivityCardResp ) {
        option( tlvpickle.CmdID ) = 17;										
	    option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <OperActivityCard id>";	
    }   
	
	// 更新指定运营活动类型的卡片
	rpc UpdateOperActivityCard ( UpdateOperActivityCardReq ) returns ( UpdateOperActivityCardResp ) {
        option( tlvpickle.CmdID ) = 18;										
	    option( tlvpickle.OptString ) = "t:x:g:m:j:b:e:";							
        option( tlvpickle.Usage ) = "-t <OperActivityCard id> -x <name>  -g <gameID> -m <img_url> -j <jump url> -b <begin timestamp> -e <end timestamp>";	
    }   
	
	//根据标签名称查询标签
	rpc GetGameTagInfoListByName ( GetGameTagInfoListByNameReq ) returns ( GetAllGameTagInfoListResp ) {
        option( tlvpickle.CmdID ) = 19;										
	    option( tlvpickle.OptString ) = "n:";							
        option( tlvpickle.Usage ) = "-n <tag_name>";	
    }   
	
	//根据标签查询榜单类型卡列表
	rpc GetAllTopListCardByTag ( GetAllTopListCardByTagReq ) returns ( GetAllTopListCardResp ) {
        option( tlvpickle.CmdID ) = 20;										
	    option( tlvpickle.OptString ) = "t:";							
        option( tlvpickle.Usage ) = "-t <tag_id>";	
    } 
	
	// 根据游戏ID获取标签列表
	rpc GetGameTagList ( GetGameTagListReq ) returns ( GetAllGameTagInfoListResp ) {
        option( tlvpickle.CmdID ) = 21;										
	    option( tlvpickle.OptString ) = "g:";							
        option( tlvpickle.Usage ) = "-g <gameid>";	
    } 

	// 根据游戏ID获取与该游戏具有相同标签的同类游戏
	rpc GetSameTagGameList ( GetSameTagGameListReq ) returns ( GetSameTagGameListResp ) {
        option( tlvpickle.CmdID ) = 22;										
	    option( tlvpickle.OptString ) = "g:";							
        option( tlvpickle.Usage ) = "-g <game id>";	
    } 
}
