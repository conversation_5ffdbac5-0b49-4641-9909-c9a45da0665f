syntax="proto2";

// 必须import
import "common/tlvpickle/skbuiltintype.proto";

// namespace
package car;

message VisitReq {
    required uint32 behavior = 1;
    optional uint32 uid = 2;
    optional string device_id = 3;
    optional string client_ip = 4;
    optional uint32 guild_id = 5;
    optional string other = 6;      //访问者的其他属性
    optional string target = 7;     //访问目标
    optional int32 increment = 8;   //更新访问次数的步长
    optional bool is_clear = 9;     //清空访问次数
}

message CheckInfo {
    required uint32 obj_type = 1;
    optional bool is_limit = 2;      //是否被限制访问
    optional string obj_value = 3;   //访问者被限制的属性值
    optional uint32 cur_count = 4;
    optional string cur_flag = 5;
    optional int32 error_code = 6;
    optional string obj_type_name = 7;
}

message CheckResp {
    required bool permit = 1;
    repeated CheckInfo info_list = 2;
}

message UpdateInfo {
    required uint32 obj_type = 1;
    optional uint32 new_count = 2;
    optional uint32 threshold = 3;
}

message UpdateResp {
    repeated UpdateInfo info_list = 1;
}

message CheckAndUpdateResp {
    required CheckResp check_resp = 1;
    required UpdateResp update_resp = 2;
}

message ReloadConfigReq {
}
message ReloadConfigResp {
}

message GetNormConfigReq {
    optional string behavior = 1;
}
message NormInfo {
    required string behavior = 1;
    required uint32 object_type = 2;
    required uint32 action_type = 3;
    required string action_name = 4;
    optional uint32 priority = 5;
    optional string match_rule_name = 6;
    optional string time_limit_name = 7;
    optional int32 error_code = 8;
    optional string description = 9;
}
message GetNormConfigResp {
    repeated NormInfo info_list = 1;
}

message GetCarConfigReq {
}
message CarInfo {
    required string name = 1;
    optional uint32 threshold = 2;
    optional uint32 duration = 3;
    optional string description = 4;
}
message GetCarConfigResp {
    repeated CarInfo info_list = 1;
}

message GetFlagConfigReq {
}
message FlagInfo {
    required string name = 1;
    optional uint32 duration = 2;
    optional string description = 3;
}
message GetFlagConfigResp {
    repeated FlagInfo info_list = 1;
}

message GetTimeLimitConfigReq {
}
message TimeLimitInfo {
    required string name = 1;
    optional uint32 type = 2;
    optional uint32 ttl = 3;
    optional uint32 day = 4;
    optional uint32 hour = 5;
    optional string description = 6;
}
message GetTimeLimitConfigResp {
    repeated TimeLimitInfo info_list = 1;
}

message GetMatchRuleConfigReq {
}
message MatchRuleInfo {
    required string name = 1;
    optional string rule = 2;
    optional string description = 3;
}
message GetMatchRuleConfigResp {
    repeated MatchRuleInfo info_list = 1;
}


message UpdateNormConfigReq {
    required NormInfo info = 1;
    optional bool is_delete = 2;
}
message UpdateNormConfigResp {
    optional string err_msg = 1;
}

message UpdateCarConfigReq {
    required CarInfo info = 1;
    optional bool is_delete = 2;
}
message UpdateCarConfigResp {
    optional string err_msg = 1;
}

message UpdateFlagConfigReq {
    required FlagInfo info = 1;
    optional bool is_delete = 2;
}
message UpdateFlagConfigResp {
}

message UpdateTimeLimitConfigReq {
    required TimeLimitInfo info = 1;
    optional bool is_delete = 2;
}
message UpdateTimeLimitConfigResp {
    optional string err_msg = 1;
}

message UpdateMatchRuleConfigReq {
    required MatchRuleInfo info = 1;
    optional bool is_delete = 2;
}
message UpdateMatchRuleConfigResp {
}

service car {
	option( tlvpickle.Magic ) = 15652;		// 服务监听端口号

    rpc CheckBehaviorVisit ( VisitReq ) returns ( CheckResp ) {
    	option( tlvpickle.CmdID ) = 1;										// 命令号
        option( tlvpickle.OptString ) = "b:u:d:h:g:o:t:i:w";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-b <behavior> [-u <uid> -d <device_id> -h <client_ip> -g <guild_id> -o <other> -t <target> -i <increment> -w <is_clear>";	// 测试工具的命令号帮助
	}

    rpc UpdateBehaviorVisit ( VisitReq ) returns ( UpdateResp ) {
    	option( tlvpickle.CmdID ) = 2;										// 命令号
        option( tlvpickle.OptString ) = "b:u:d:h:g:o:t:i:w";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-b <behavior> [-u <uid> -d <device_id> -h <client_ip> -g <guild_id> -o <other> -t <target> -i <increment> -w <is_clear>";	// 测试工具的命令号帮助
	}

    rpc CheckAndUpdateBehaviorVisit ( VisitReq ) returns ( CheckAndUpdateResp ) {
        option( tlvpickle.CmdID ) = 3;										// 命令号
        option( tlvpickle.OptString ) = "b:u:d:h:g:o:t:i:w";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-b <behavior> [-u <uid> -d <device_id> -h <client_ip> -g <guild_id> -o <other> -t <target> -i <increment> -w <is_clear>";	// 测试工具的命令号帮助
	}

    rpc ReloadConfig ( ReloadConfigReq ) returns ( ReloadConfigResp ) {
        option( tlvpickle.CmdID ) = 4;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
    }

    rpc GetNormConfig ( GetNormConfigReq ) returns ( GetNormConfigResp ) {
        option( tlvpickle.CmdID ) = 5;										// 命令号
        option( tlvpickle.OptString ) = "b:";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "[-b <behavior>]";
    }

    rpc GetCarConfig ( GetCarConfigReq ) returns ( GetCarConfigResp ) {
        option( tlvpickle.CmdID ) = 6;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
    }

    rpc GetFlagConfig ( GetFlagConfigReq ) returns ( GetFlagConfigResp ) {
        option( tlvpickle.CmdID ) = 7;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
    }

    rpc GetTimeLimitConfig ( GetTimeLimitConfigReq ) returns ( GetTimeLimitConfigResp ) {
        option( tlvpickle.CmdID ) = 8;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
    }

    rpc GetMatchRuleConfig ( GetMatchRuleConfigReq ) returns ( GetMatchRuleConfigResp ) {
        option( tlvpickle.CmdID ) = 9;										// 命令号
        option( tlvpickle.OptString ) = "";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "";
    }

    rpc UpdateNormConfig ( UpdateNormConfigReq ) returns ( UpdateNormConfigResp ) {
        option( tlvpickle.CmdID ) = 10;										// 命令号
        option( tlvpickle.OptString ) = "b:o:a:n:p:r:l:e:i:d";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-b <behavior eg:'AddFriend'> -o <object_type 1.user 2.device 3.ip 4.guild 5.other> -a <action_type 1.filter 2.car 3.unique_car 4.flag> -n <action_name>\\n                                                                     [-p <priority> -r <match_rule> -l <time_limit_name> -e <error_code> -i <description> -d <is_delete>]";
    }

    rpc UpdateCarConfig ( UpdateCarConfigReq ) returns ( UpdateCarConfigResp ) {
        option( tlvpickle.CmdID ) = 11;										// 命令号
        option( tlvpickle.OptString ) = "n:t:u:i:d";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <name> [-t <threshold> -u <duration> -i <description> -d <is_delete>]";
    }

    rpc UpdateFlagConfig ( UpdateFlagConfigReq ) returns ( UpdateFlagConfigResp ) {
        option( tlvpickle.CmdID ) = 12;										// 命令号
        option( tlvpickle.OptString ) = "n:u:i:d";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <name> [-u <duration> -i <description> -d <is_delete>]";
    }

    rpc UpdateTimeLimitConfig ( UpdateTimeLimitConfigReq ) returns ( UpdateTimeLimitConfigResp ) {
        option( tlvpickle.CmdID ) = 13;										// 命令号
        option( tlvpickle.OptString ) = "n:x:t:a:h:i:d";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <name> [-x <type: 1.relative 2.absolute> -t <ttl> -a <day> -h <hour> -i <description> -d <is_delete>]";
    }

    rpc UpdateMatchRuleConfig ( UpdateMatchRuleConfigReq ) returns ( UpdateMatchRuleConfigResp ) {
        option( tlvpickle.CmdID ) = 14;										// 命令号
        option( tlvpickle.OptString ) = "n:r:i:d";							// 测试工具的命令号参数， 注意最后的冒号
        option( tlvpickle.Usage ) = "-n <name> [-r <rule> -i <description> -d <is_delete>]";
    }
}




