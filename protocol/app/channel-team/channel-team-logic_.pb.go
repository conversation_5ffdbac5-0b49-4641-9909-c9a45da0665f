// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-team-logic_.proto

package channel_team // import "golang.52tt.com/protocol/app/channel-team"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UiTypeType int32

const (
	UiTypeType_UNKOWN UiTypeType = 0
	UiTypeType_WZRY   UiTypeType = 1
	UiTypeType_HPJY   UiTypeType = 2
)

var UiTypeType_name = map[int32]string{
	0: "UNKOWN",
	1: "WZRY",
	2: "HPJY",
}
var UiTypeType_value = map[string]int32{
	"UNKOWN": 0,
	"WZRY":   1,
	"HPJY":   2,
}

func (x UiTypeType) String() string {
	return proto.EnumName(UiTypeType_name, int32(x))
}
func (UiTypeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{0}
}

type UpdateType int32

const (
	UpdateType_UPDATE UpdateType = 0
	UpdateType_CREATE UpdateType = 1
)

var UpdateType_name = map[int32]string{
	0: "UPDATE",
	1: "CREATE",
}
var UpdateType_value = map[string]int32{
	"UPDATE": 0,
	"CREATE": 1,
}

func (x UpdateType) String() string {
	return proto.EnumName(UpdateType_name, int32(x))
}
func (UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{1}
}

type CtrlType int32

const (
	// 未知
	CtrlType_NORMAL CtrlType = 0
	// 踢出小队
	CtrlType_TICK_OUT CtrlType = 1
	// 解散队伍
	CtrlType_BREAK CtrlType = 2
	// 加入队伍
	CtrlType_ADD CtrlType = 3
	// 主动退出小队
	CtrlType_QUIT CtrlType = 4
)

var CtrlType_name = map[int32]string{
	0: "NORMAL",
	1: "TICK_OUT",
	2: "BREAK",
	3: "ADD",
	4: "QUIT",
}
var CtrlType_value = map[string]int32{
	"NORMAL":   0,
	"TICK_OUT": 1,
	"BREAK":    2,
	"ADD":      3,
	"QUIT":     4,
}

func (x CtrlType) String() string {
	return proto.EnumName(CtrlType_name, int32(x))
}
func (CtrlType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{2}
}

type JoinChannelTeamReq_Scene int32

const (
	// 其他
	JoinChannelTeamReq_OTHER JoinChannelTeamReq_Scene = 0
	// 麦位抱上小队
	JoinChannelTeamReq_HOLD_ON JoinChannelTeamReq_Scene = 1
	// 选ta加入小队
	JoinChannelTeamReq_PICK_UP JoinChannelTeamReq_Scene = 2
)

var JoinChannelTeamReq_Scene_name = map[int32]string{
	0: "OTHER",
	1: "HOLD_ON",
	2: "PICK_UP",
}
var JoinChannelTeamReq_Scene_value = map[string]int32{
	"OTHER":   0,
	"HOLD_ON": 1,
	"PICK_UP": 2,
}

func (x JoinChannelTeamReq_Scene) String() string {
	return proto.EnumName(JoinChannelTeamReq_Scene_name, int32(x))
}
func (JoinChannelTeamReq_Scene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{0, 0}
}

// 用户进入小队
type JoinChannelTeamReq struct {
	BaseReq   *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	JoinUid   uint32       `protobuf:"varint,3,opt,name=join_uid,json=joinUid,proto3" json:"join_uid,omitempty"`
	// 申请位置的索引位，从0开始计算
	LocationIndex uint32 `protobuf:"varint,4,opt,name=location_index,json=locationIndex,proto3" json:"location_index,omitempty"`
	// 申请位置名称
	LocationName         string                   `protobuf:"bytes,5,opt,name=location_name,json=locationName,proto3" json:"location_name,omitempty"`
	Type                 JoinChannelTeamReq_Scene `protobuf:"varint,6,opt,name=type,proto3,enum=ga.channel_team.JoinChannelTeamReq_Scene" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *JoinChannelTeamReq) Reset()         { *m = JoinChannelTeamReq{} }
func (m *JoinChannelTeamReq) String() string { return proto.CompactTextString(m) }
func (*JoinChannelTeamReq) ProtoMessage()    {}
func (*JoinChannelTeamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{0}
}
func (m *JoinChannelTeamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelTeamReq.Unmarshal(m, b)
}
func (m *JoinChannelTeamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelTeamReq.Marshal(b, m, deterministic)
}
func (dst *JoinChannelTeamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelTeamReq.Merge(dst, src)
}
func (m *JoinChannelTeamReq) XXX_Size() int {
	return xxx_messageInfo_JoinChannelTeamReq.Size(m)
}
func (m *JoinChannelTeamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelTeamReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelTeamReq proto.InternalMessageInfo

func (m *JoinChannelTeamReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinChannelTeamReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinChannelTeamReq) GetJoinUid() uint32 {
	if m != nil {
		return m.JoinUid
	}
	return 0
}

func (m *JoinChannelTeamReq) GetLocationIndex() uint32 {
	if m != nil {
		return m.LocationIndex
	}
	return 0
}

func (m *JoinChannelTeamReq) GetLocationName() string {
	if m != nil {
		return m.LocationName
	}
	return ""
}

func (m *JoinChannelTeamReq) GetType() JoinChannelTeamReq_Scene {
	if m != nil {
		return m.Type
	}
	return JoinChannelTeamReq_OTHER
}

type JoinChannelTeamResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *JoinChannelTeamResp) Reset()         { *m = JoinChannelTeamResp{} }
func (m *JoinChannelTeamResp) String() string { return proto.CompactTextString(m) }
func (*JoinChannelTeamResp) ProtoMessage()    {}
func (*JoinChannelTeamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{1}
}
func (m *JoinChannelTeamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelTeamResp.Unmarshal(m, b)
}
func (m *JoinChannelTeamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelTeamResp.Marshal(b, m, deterministic)
}
func (dst *JoinChannelTeamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelTeamResp.Merge(dst, src)
}
func (m *JoinChannelTeamResp) XXX_Size() int {
	return xxx_messageInfo_JoinChannelTeamResp.Size(m)
}
func (m *JoinChannelTeamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelTeamResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelTeamResp proto.InternalMessageInfo

func (m *JoinChannelTeamResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 房主获取申请列表
type GetChannelTeamApplyListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32       `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Count                uint32       `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelTeamApplyListReq) Reset()         { *m = GetChannelTeamApplyListReq{} }
func (m *GetChannelTeamApplyListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTeamApplyListReq) ProtoMessage()    {}
func (*GetChannelTeamApplyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{2}
}
func (m *GetChannelTeamApplyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTeamApplyListReq.Unmarshal(m, b)
}
func (m *GetChannelTeamApplyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTeamApplyListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTeamApplyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTeamApplyListReq.Merge(dst, src)
}
func (m *GetChannelTeamApplyListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTeamApplyListReq.Size(m)
}
func (m *GetChannelTeamApplyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTeamApplyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTeamApplyListReq proto.InternalMessageInfo

func (m *GetChannelTeamApplyListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelTeamApplyListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelTeamApplyListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetChannelTeamApplyListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetChannelTeamApplyListResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ApplyList            []*ApplyMember `protobuf:"bytes,2,rep,name=apply_list,json=applyList,proto3" json:"apply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetChannelTeamApplyListResp) Reset()         { *m = GetChannelTeamApplyListResp{} }
func (m *GetChannelTeamApplyListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTeamApplyListResp) ProtoMessage()    {}
func (*GetChannelTeamApplyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{3}
}
func (m *GetChannelTeamApplyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTeamApplyListResp.Unmarshal(m, b)
}
func (m *GetChannelTeamApplyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTeamApplyListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTeamApplyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTeamApplyListResp.Merge(dst, src)
}
func (m *GetChannelTeamApplyListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTeamApplyListResp.Size(m)
}
func (m *GetChannelTeamApplyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTeamApplyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTeamApplyListResp proto.InternalMessageInfo

func (m *GetChannelTeamApplyListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelTeamApplyListResp) GetApplyList() []*ApplyMember {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

type ApplyMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Dan                  string   `protobuf:"bytes,5,opt,name=dan,proto3" json:"dan,omitempty"`
	DanUrl               string   `protobuf:"bytes,6,opt,name=dan_url,json=danUrl,proto3" json:"dan_url,omitempty"`
	Desc                 string   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	LocationName         string   `protobuf:"bytes,8,opt,name=location_name,json=locationName,proto3" json:"location_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyMember) Reset()         { *m = ApplyMember{} }
func (m *ApplyMember) String() string { return proto.CompactTextString(m) }
func (*ApplyMember) ProtoMessage()    {}
func (*ApplyMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{4}
}
func (m *ApplyMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyMember.Unmarshal(m, b)
}
func (m *ApplyMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyMember.Marshal(b, m, deterministic)
}
func (dst *ApplyMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyMember.Merge(dst, src)
}
func (m *ApplyMember) XXX_Size() int {
	return xxx_messageInfo_ApplyMember.Size(m)
}
func (m *ApplyMember) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyMember.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyMember proto.InternalMessageInfo

func (m *ApplyMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ApplyMember) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ApplyMember) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ApplyMember) GetDan() string {
	if m != nil {
		return m.Dan
	}
	return ""
}

func (m *ApplyMember) GetDanUrl() string {
	if m != nil {
		return m.DanUrl
	}
	return ""
}

func (m *ApplyMember) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ApplyMember) GetLocationName() string {
	if m != nil {
		return m.LocationName
	}
	return ""
}

// 房主同意用户申请入队
type AgreeChannelTeamApplyReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	JoinUid              uint32       `protobuf:"varint,3,opt,name=join_uid,json=joinUid,proto3" json:"join_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AgreeChannelTeamApplyReq) Reset()         { *m = AgreeChannelTeamApplyReq{} }
func (m *AgreeChannelTeamApplyReq) String() string { return proto.CompactTextString(m) }
func (*AgreeChannelTeamApplyReq) ProtoMessage()    {}
func (*AgreeChannelTeamApplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{5}
}
func (m *AgreeChannelTeamApplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AgreeChannelTeamApplyReq.Unmarshal(m, b)
}
func (m *AgreeChannelTeamApplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AgreeChannelTeamApplyReq.Marshal(b, m, deterministic)
}
func (dst *AgreeChannelTeamApplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AgreeChannelTeamApplyReq.Merge(dst, src)
}
func (m *AgreeChannelTeamApplyReq) XXX_Size() int {
	return xxx_messageInfo_AgreeChannelTeamApplyReq.Size(m)
}
func (m *AgreeChannelTeamApplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AgreeChannelTeamApplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_AgreeChannelTeamApplyReq proto.InternalMessageInfo

func (m *AgreeChannelTeamApplyReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AgreeChannelTeamApplyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AgreeChannelTeamApplyReq) GetJoinUid() uint32 {
	if m != nil {
		return m.JoinUid
	}
	return 0
}

type AgreeChannelTeamApplyResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AgreeChannelTeamApplyResp) Reset()         { *m = AgreeChannelTeamApplyResp{} }
func (m *AgreeChannelTeamApplyResp) String() string { return proto.CompactTextString(m) }
func (*AgreeChannelTeamApplyResp) ProtoMessage()    {}
func (*AgreeChannelTeamApplyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{6}
}
func (m *AgreeChannelTeamApplyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AgreeChannelTeamApplyResp.Unmarshal(m, b)
}
func (m *AgreeChannelTeamApplyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AgreeChannelTeamApplyResp.Marshal(b, m, deterministic)
}
func (dst *AgreeChannelTeamApplyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AgreeChannelTeamApplyResp.Merge(dst, src)
}
func (m *AgreeChannelTeamApplyResp) XXX_Size() int {
	return xxx_messageInfo_AgreeChannelTeamApplyResp.Size(m)
}
func (m *AgreeChannelTeamApplyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AgreeChannelTeamApplyResp.DiscardUnknown(m)
}

var xxx_messageInfo_AgreeChannelTeamApplyResp proto.InternalMessageInfo

func (m *AgreeChannelTeamApplyResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 用户退出小队或者房主踢用户退出小队
type TickChannelTeamMemberReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TickUid              uint32       `protobuf:"varint,3,opt,name=tick_uid,json=tickUid,proto3" json:"tick_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TickChannelTeamMemberReq) Reset()         { *m = TickChannelTeamMemberReq{} }
func (m *TickChannelTeamMemberReq) String() string { return proto.CompactTextString(m) }
func (*TickChannelTeamMemberReq) ProtoMessage()    {}
func (*TickChannelTeamMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{7}
}
func (m *TickChannelTeamMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TickChannelTeamMemberReq.Unmarshal(m, b)
}
func (m *TickChannelTeamMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TickChannelTeamMemberReq.Marshal(b, m, deterministic)
}
func (dst *TickChannelTeamMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TickChannelTeamMemberReq.Merge(dst, src)
}
func (m *TickChannelTeamMemberReq) XXX_Size() int {
	return xxx_messageInfo_TickChannelTeamMemberReq.Size(m)
}
func (m *TickChannelTeamMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TickChannelTeamMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_TickChannelTeamMemberReq proto.InternalMessageInfo

func (m *TickChannelTeamMemberReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *TickChannelTeamMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TickChannelTeamMemberReq) GetTickUid() uint32 {
	if m != nil {
		return m.TickUid
	}
	return 0
}

type TickChannelTeamMemberResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *TickChannelTeamMemberResp) Reset()         { *m = TickChannelTeamMemberResp{} }
func (m *TickChannelTeamMemberResp) String() string { return proto.CompactTextString(m) }
func (*TickChannelTeamMemberResp) ProtoMessage()    {}
func (*TickChannelTeamMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{8}
}
func (m *TickChannelTeamMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TickChannelTeamMemberResp.Unmarshal(m, b)
}
func (m *TickChannelTeamMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TickChannelTeamMemberResp.Marshal(b, m, deterministic)
}
func (dst *TickChannelTeamMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TickChannelTeamMemberResp.Merge(dst, src)
}
func (m *TickChannelTeamMemberResp) XXX_Size() int {
	return xxx_messageInfo_TickChannelTeamMemberResp.Size(m)
}
func (m *TickChannelTeamMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TickChannelTeamMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_TickChannelTeamMemberResp proto.InternalMessageInfo

func (m *TickChannelTeamMemberResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取小队成员信息
type GetChannelTeamMemberListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	LocationList         []string     `protobuf:"bytes,4,rep,name=location_list,json=locationList,proto3" json:"location_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelTeamMemberListReq) Reset()         { *m = GetChannelTeamMemberListReq{} }
func (m *GetChannelTeamMemberListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTeamMemberListReq) ProtoMessage()    {}
func (*GetChannelTeamMemberListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{9}
}
func (m *GetChannelTeamMemberListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTeamMemberListReq.Unmarshal(m, b)
}
func (m *GetChannelTeamMemberListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTeamMemberListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelTeamMemberListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTeamMemberListReq.Merge(dst, src)
}
func (m *GetChannelTeamMemberListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelTeamMemberListReq.Size(m)
}
func (m *GetChannelTeamMemberListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTeamMemberListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTeamMemberListReq proto.InternalMessageInfo

func (m *GetChannelTeamMemberListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelTeamMemberListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelTeamMemberListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetChannelTeamMemberListReq) GetLocationList() []string {
	if m != nil {
		return m.LocationList
	}
	return nil
}

type GetChannelTeamMemberListResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelTeamInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetChannelTeamMemberListResp) Reset()         { *m = GetChannelTeamMemberListResp{} }
func (m *GetChannelTeamMemberListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTeamMemberListResp) ProtoMessage()    {}
func (*GetChannelTeamMemberListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{10}
}
func (m *GetChannelTeamMemberListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTeamMemberListResp.Unmarshal(m, b)
}
func (m *GetChannelTeamMemberListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTeamMemberListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelTeamMemberListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTeamMemberListResp.Merge(dst, src)
}
func (m *GetChannelTeamMemberListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelTeamMemberListResp.Size(m)
}
func (m *GetChannelTeamMemberListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTeamMemberListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTeamMemberListResp proto.InternalMessageInfo

func (m *GetChannelTeamMemberListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelTeamMemberListResp) GetInfo() *ChannelTeamInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 获取开黑过的用户列表
type GetGangUpHistoryReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32       `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Count                uint32       `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGangUpHistoryReq) Reset()         { *m = GetGangUpHistoryReq{} }
func (m *GetGangUpHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGangUpHistoryReq) ProtoMessage()    {}
func (*GetGangUpHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{11}
}
func (m *GetGangUpHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangUpHistoryReq.Unmarshal(m, b)
}
func (m *GetGangUpHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangUpHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetGangUpHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangUpHistoryReq.Merge(dst, src)
}
func (m *GetGangUpHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetGangUpHistoryReq.Size(m)
}
func (m *GetGangUpHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangUpHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangUpHistoryReq proto.InternalMessageInfo

func (m *GetGangUpHistoryReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGangUpHistoryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetGangUpHistoryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGangUpHistoryReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetGangUpHistoryResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Record               []*GangUpRecord `protobuf:"bytes,2,rep,name=record,proto3" json:"record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGangUpHistoryResp) Reset()         { *m = GetGangUpHistoryResp{} }
func (m *GetGangUpHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGangUpHistoryResp) ProtoMessage()    {}
func (*GetGangUpHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{12}
}
func (m *GetGangUpHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGangUpHistoryResp.Unmarshal(m, b)
}
func (m *GetGangUpHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGangUpHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetGangUpHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGangUpHistoryResp.Merge(dst, src)
}
func (m *GetGangUpHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetGangUpHistoryResp.Size(m)
}
func (m *GetGangUpHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGangUpHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGangUpHistoryResp proto.InternalMessageInfo

func (m *GetGangUpHistoryResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGangUpHistoryResp) GetRecord() []*GangUpRecord {
	if m != nil {
		return m.Record
	}
	return nil
}

type GangUpRecord struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UpdateTime           int64    `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	GameNameList         []string `protobuf:"bytes,3,rep,name=game_name_list,json=gameNameList,proto3" json:"game_name_list,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Nickname             string   `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,6,opt,name=account,proto3" json:"account,omitempty"`
	IsOnline             bool     `protobuf:"varint,7,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GangUpRecord) Reset()         { *m = GangUpRecord{} }
func (m *GangUpRecord) String() string { return proto.CompactTextString(m) }
func (*GangUpRecord) ProtoMessage()    {}
func (*GangUpRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{13}
}
func (m *GangUpRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GangUpRecord.Unmarshal(m, b)
}
func (m *GangUpRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GangUpRecord.Marshal(b, m, deterministic)
}
func (dst *GangUpRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GangUpRecord.Merge(dst, src)
}
func (m *GangUpRecord) XXX_Size() int {
	return xxx_messageInfo_GangUpRecord.Size(m)
}
func (m *GangUpRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GangUpRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GangUpRecord proto.InternalMessageInfo

func (m *GangUpRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GangUpRecord) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *GangUpRecord) GetGameNameList() []string {
	if m != nil {
		return m.GameNameList
	}
	return nil
}

func (m *GangUpRecord) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *GangUpRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GangUpRecord) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GangUpRecord) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

// 获取所有的房间用户
type GetAllChannelMemberReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32       `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Count                uint32       `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllChannelMemberReq) Reset()         { *m = GetAllChannelMemberReq{} }
func (m *GetAllChannelMemberReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelMemberReq) ProtoMessage()    {}
func (*GetAllChannelMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{14}
}
func (m *GetAllChannelMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelMemberReq.Unmarshal(m, b)
}
func (m *GetAllChannelMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelMemberReq.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelMemberReq.Merge(dst, src)
}
func (m *GetAllChannelMemberReq) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelMemberReq.Size(m)
}
func (m *GetAllChannelMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelMemberReq proto.InternalMessageInfo

func (m *GetAllChannelMemberReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAllChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetAllChannelMemberReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAllChannelMemberReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetAllChannelMemberResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MemberList           []*ChannelMember `protobuf:"bytes,2,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAllChannelMemberResp) Reset()         { *m = GetAllChannelMemberResp{} }
func (m *GetAllChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelMemberResp) ProtoMessage()    {}
func (*GetAllChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{15}
}
func (m *GetAllChannelMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelMemberResp.Unmarshal(m, b)
}
func (m *GetAllChannelMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelMemberResp.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelMemberResp.Merge(dst, src)
}
func (m *GetAllChannelMemberResp) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelMemberResp.Size(m)
}
func (m *GetAllChannelMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelMemberResp proto.InternalMessageInfo

func (m *GetAllChannelMemberResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllChannelMemberResp) GetMemberList() []*ChannelMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

type ChannelMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Dan                  string   `protobuf:"bytes,5,opt,name=dan,proto3" json:"dan,omitempty"`
	DanUrl               string   `protobuf:"bytes,6,opt,name=dan_url,json=danUrl,proto3" json:"dan_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMember) Reset()         { *m = ChannelMember{} }
func (m *ChannelMember) String() string { return proto.CompactTextString(m) }
func (*ChannelMember) ProtoMessage()    {}
func (*ChannelMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{16}
}
func (m *ChannelMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMember.Unmarshal(m, b)
}
func (m *ChannelMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMember.Marshal(b, m, deterministic)
}
func (dst *ChannelMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMember.Merge(dst, src)
}
func (m *ChannelMember) XXX_Size() int {
	return xxx_messageInfo_ChannelMember.Size(m)
}
func (m *ChannelMember) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMember.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMember proto.InternalMessageInfo

func (m *ChannelMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelMember) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelMember) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChannelMember) GetDan() string {
	if m != nil {
		return m.Dan
	}
	return ""
}

func (m *ChannelMember) GetDanUrl() string {
	if m != nil {
		return m.DanUrl
	}
	return ""
}

// 设置房间小队昵称
type SetGameNicknameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameNickname         string       `protobuf:"bytes,3,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetGameNicknameReq) Reset()         { *m = SetGameNicknameReq{} }
func (m *SetGameNicknameReq) String() string { return proto.CompactTextString(m) }
func (*SetGameNicknameReq) ProtoMessage()    {}
func (*SetGameNicknameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{17}
}
func (m *SetGameNicknameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameNicknameReq.Unmarshal(m, b)
}
func (m *SetGameNicknameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameNicknameReq.Marshal(b, m, deterministic)
}
func (dst *SetGameNicknameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameNicknameReq.Merge(dst, src)
}
func (m *SetGameNicknameReq) XXX_Size() int {
	return xxx_messageInfo_SetGameNicknameReq.Size(m)
}
func (m *SetGameNicknameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameNicknameReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameNicknameReq proto.InternalMessageInfo

func (m *SetGameNicknameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetGameNicknameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetGameNicknameReq) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

type SetGameNicknameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetGameNicknameResp) Reset()         { *m = SetGameNicknameResp{} }
func (m *SetGameNicknameResp) String() string { return proto.CompactTextString(m) }
func (*SetGameNicknameResp) ProtoMessage()    {}
func (*SetGameNicknameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{18}
}
func (m *SetGameNicknameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetGameNicknameResp.Unmarshal(m, b)
}
func (m *SetGameNicknameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetGameNicknameResp.Marshal(b, m, deterministic)
}
func (dst *SetGameNicknameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetGameNicknameResp.Merge(dst, src)
}
func (m *SetGameNicknameResp) XXX_Size() int {
	return xxx_messageInfo_SetGameNicknameResp.Size(m)
}
func (m *SetGameNicknameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetGameNicknameResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetGameNicknameResp proto.InternalMessageInfo

func (m *SetGameNicknameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 查询房间小队情况
type ChannelTeamInfo struct {
	// 房主uid
	OwnerUid uint32 `protobuf:"varint,1,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	// 房间成员信息
	MemberList []*MemberInfo `protobuf:"bytes,2,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	// 房间小队创建时间
	CreateTime int64 `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 房间小队ui类型，UiTypeType
	UiType uint32 `protobuf:"varint,4,opt,name=ui_type,json=uiType,proto3" json:"ui_type,omitempty"`
	// 房间名称
	TeamName string `protobuf:"bytes,5,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"`
	// 游戏名称
	GameName             string   `protobuf:"bytes,6,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelTeamInfo) Reset()         { *m = ChannelTeamInfo{} }
func (m *ChannelTeamInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelTeamInfo) ProtoMessage()    {}
func (*ChannelTeamInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{19}
}
func (m *ChannelTeamInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTeamInfo.Unmarshal(m, b)
}
func (m *ChannelTeamInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTeamInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelTeamInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTeamInfo.Merge(dst, src)
}
func (m *ChannelTeamInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelTeamInfo.Size(m)
}
func (m *ChannelTeamInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTeamInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTeamInfo proto.InternalMessageInfo

func (m *ChannelTeamInfo) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

func (m *ChannelTeamInfo) GetMemberList() []*MemberInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *ChannelTeamInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ChannelTeamInfo) GetUiType() uint32 {
	if m != nil {
		return m.UiType
	}
	return 0
}

func (m *ChannelTeamInfo) GetTeamName() string {
	if m != nil {
		return m.TeamName
	}
	return ""
}

func (m *ChannelTeamInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type MemberInfo struct {
	Uid      uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account  string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex      int32  `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Dan      string `protobuf:"bytes,5,opt,name=dan,proto3" json:"dan,omitempty"`
	DanUrl   string `protobuf:"bytes,6,opt,name=dan_url,json=danUrl,proto3" json:"dan_url,omitempty"`
	// 用户游戏id
	GameNickname string `protobuf:"bytes,7,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	LocationName string `protobuf:"bytes,8,opt,name=location_name,json=locationName,proto3" json:"location_name,omitempty"`
	// 位置图标
	LocationIcon string `protobuf:"bytes,9,opt,name=location_icon,json=locationIcon,proto3" json:"location_icon,omitempty"`
	// 位置不可用
	Disabled             bool     `protobuf:"varint,10,opt,name=disabled,proto3" json:"disabled,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberInfo) Reset()         { *m = MemberInfo{} }
func (m *MemberInfo) String() string { return proto.CompactTextString(m) }
func (*MemberInfo) ProtoMessage()    {}
func (*MemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{20}
}
func (m *MemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberInfo.Unmarshal(m, b)
}
func (m *MemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberInfo.Marshal(b, m, deterministic)
}
func (dst *MemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberInfo.Merge(dst, src)
}
func (m *MemberInfo) XXX_Size() int {
	return xxx_messageInfo_MemberInfo.Size(m)
}
func (m *MemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MemberInfo proto.InternalMessageInfo

func (m *MemberInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MemberInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MemberInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *MemberInfo) GetDan() string {
	if m != nil {
		return m.Dan
	}
	return ""
}

func (m *MemberInfo) GetDanUrl() string {
	if m != nil {
		return m.DanUrl
	}
	return ""
}

func (m *MemberInfo) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *MemberInfo) GetLocationName() string {
	if m != nil {
		return m.LocationName
	}
	return ""
}

func (m *MemberInfo) GetLocationIcon() string {
	if m != nil {
		return m.LocationIcon
	}
	return ""
}

func (m *MemberInfo) GetDisabled() bool {
	if m != nil {
		return m.Disabled
	}
	return false
}

type PushChannelTeamUpdate struct {
	ChannelTeamInfo      *ChannelTeamInfo `protobuf:"bytes,1,opt,name=channel_team_info,json=channelTeamInfo,proto3" json:"channel_team_info,omitempty"`
	PushTime             int64            `protobuf:"varint,2,opt,name=push_time,json=pushTime,proto3" json:"push_time,omitempty"`
	Type                 uint32           `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PushChannelTeamUpdate) Reset()         { *m = PushChannelTeamUpdate{} }
func (m *PushChannelTeamUpdate) String() string { return proto.CompactTextString(m) }
func (*PushChannelTeamUpdate) ProtoMessage()    {}
func (*PushChannelTeamUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{21}
}
func (m *PushChannelTeamUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushChannelTeamUpdate.Unmarshal(m, b)
}
func (m *PushChannelTeamUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushChannelTeamUpdate.Marshal(b, m, deterministic)
}
func (dst *PushChannelTeamUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushChannelTeamUpdate.Merge(dst, src)
}
func (m *PushChannelTeamUpdate) XXX_Size() int {
	return xxx_messageInfo_PushChannelTeamUpdate.Size(m)
}
func (m *PushChannelTeamUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_PushChannelTeamUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_PushChannelTeamUpdate proto.InternalMessageInfo

func (m *PushChannelTeamUpdate) GetChannelTeamInfo() *ChannelTeamInfo {
	if m != nil {
		return m.ChannelTeamInfo
	}
	return nil
}

func (m *PushChannelTeamUpdate) GetPushTime() int64 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *PushChannelTeamUpdate) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type CtrlMsg struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CtrlMsg) Reset()         { *m = CtrlMsg{} }
func (m *CtrlMsg) String() string { return proto.CompactTextString(m) }
func (*CtrlMsg) ProtoMessage()    {}
func (*CtrlMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_team_logic__4f3ca131d0a6494e, []int{22}
}
func (m *CtrlMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CtrlMsg.Unmarshal(m, b)
}
func (m *CtrlMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CtrlMsg.Marshal(b, m, deterministic)
}
func (dst *CtrlMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CtrlMsg.Merge(dst, src)
}
func (m *CtrlMsg) XXX_Size() int {
	return xxx_messageInfo_CtrlMsg.Size(m)
}
func (m *CtrlMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_CtrlMsg.DiscardUnknown(m)
}

var xxx_messageInfo_CtrlMsg proto.InternalMessageInfo

func (m *CtrlMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CtrlMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func init() {
	proto.RegisterType((*JoinChannelTeamReq)(nil), "ga.channel_team.JoinChannelTeamReq")
	proto.RegisterType((*JoinChannelTeamResp)(nil), "ga.channel_team.JoinChannelTeamResp")
	proto.RegisterType((*GetChannelTeamApplyListReq)(nil), "ga.channel_team.GetChannelTeamApplyListReq")
	proto.RegisterType((*GetChannelTeamApplyListResp)(nil), "ga.channel_team.GetChannelTeamApplyListResp")
	proto.RegisterType((*ApplyMember)(nil), "ga.channel_team.ApplyMember")
	proto.RegisterType((*AgreeChannelTeamApplyReq)(nil), "ga.channel_team.AgreeChannelTeamApplyReq")
	proto.RegisterType((*AgreeChannelTeamApplyResp)(nil), "ga.channel_team.AgreeChannelTeamApplyResp")
	proto.RegisterType((*TickChannelTeamMemberReq)(nil), "ga.channel_team.TickChannelTeamMemberReq")
	proto.RegisterType((*TickChannelTeamMemberResp)(nil), "ga.channel_team.TickChannelTeamMemberResp")
	proto.RegisterType((*GetChannelTeamMemberListReq)(nil), "ga.channel_team.GetChannelTeamMemberListReq")
	proto.RegisterType((*GetChannelTeamMemberListResp)(nil), "ga.channel_team.GetChannelTeamMemberListResp")
	proto.RegisterType((*GetGangUpHistoryReq)(nil), "ga.channel_team.GetGangUpHistoryReq")
	proto.RegisterType((*GetGangUpHistoryResp)(nil), "ga.channel_team.GetGangUpHistoryResp")
	proto.RegisterType((*GangUpRecord)(nil), "ga.channel_team.GangUpRecord")
	proto.RegisterType((*GetAllChannelMemberReq)(nil), "ga.channel_team.GetAllChannelMemberReq")
	proto.RegisterType((*GetAllChannelMemberResp)(nil), "ga.channel_team.GetAllChannelMemberResp")
	proto.RegisterType((*ChannelMember)(nil), "ga.channel_team.ChannelMember")
	proto.RegisterType((*SetGameNicknameReq)(nil), "ga.channel_team.SetGameNicknameReq")
	proto.RegisterType((*SetGameNicknameResp)(nil), "ga.channel_team.SetGameNicknameResp")
	proto.RegisterType((*ChannelTeamInfo)(nil), "ga.channel_team.ChannelTeamInfo")
	proto.RegisterType((*MemberInfo)(nil), "ga.channel_team.MemberInfo")
	proto.RegisterType((*PushChannelTeamUpdate)(nil), "ga.channel_team.PushChannelTeamUpdate")
	proto.RegisterType((*CtrlMsg)(nil), "ga.channel_team.CtrlMsg")
	proto.RegisterEnum("ga.channel_team.UiTypeType", UiTypeType_name, UiTypeType_value)
	proto.RegisterEnum("ga.channel_team.UpdateType", UpdateType_name, UpdateType_value)
	proto.RegisterEnum("ga.channel_team.CtrlType", CtrlType_name, CtrlType_value)
	proto.RegisterEnum("ga.channel_team.JoinChannelTeamReq_Scene", JoinChannelTeamReq_Scene_name, JoinChannelTeamReq_Scene_value)
}

func init() {
	proto.RegisterFile("channel-team-logic_.proto", fileDescriptor_channel_team_logic__4f3ca131d0a6494e)
}

var fileDescriptor_channel_team_logic__4f3ca131d0a6494e = []byte{
	// 1178 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0x36, 0xf5, 0x4b, 0x8d, 0xec, 0x98, 0xdd, 0xfc, 0x98, 0xb6, 0x93, 0x56, 0x60, 0xd2, 0xc2,
	0x36, 0x12, 0x19, 0x70, 0x1b, 0xf4, 0xd0, 0x16, 0xad, 0xfc, 0x53, 0x5b, 0xb1, 0x2d, 0xb9, 0x8c,
	0x84, 0x20, 0xb9, 0x10, 0x2b, 0x72, 0x2d, 0x6f, 0x4d, 0xee, 0xd2, 0x24, 0x85, 0x46, 0x40, 0x80,
	0xf6, 0xd2, 0x4b, 0x81, 0x1e, 0x0a, 0x14, 0x3d, 0xf5, 0xd0, 0x37, 0xe8, 0x83, 0xb4, 0x0f, 0xd1,
	0x47, 0x29, 0x76, 0x49, 0xc9, 0x94, 0x69, 0x03, 0x11, 0x20, 0xb4, 0xb9, 0xed, 0xcc, 0x2c, 0x67,
	0x67, 0xe6, 0xfb, 0x66, 0xb8, 0x0b, 0xcb, 0xf6, 0x19, 0x66, 0x8c, 0xb8, 0x4f, 0x22, 0x82, 0xbd,
	0x27, 0x2e, 0xef, 0x53, 0xdb, 0xaa, 0xfb, 0x01, 0x8f, 0x38, 0x5a, 0xec, 0xe3, 0x7a, 0x62, 0xb5,
	0x84, 0x75, 0x65, 0xa1, 0x8f, 0xad, 0x1e, 0x0e, 0x49, 0x6c, 0x37, 0xfe, 0xcc, 0x01, 0x7a, 0xc6,
	0x29, 0xdb, 0x89, 0xf7, 0x74, 0x08, 0xf6, 0x4c, 0x72, 0x81, 0x3e, 0x02, 0x55, 0x6c, 0xb2, 0x02,
	0x72, 0xa1, 0x2b, 0x35, 0x65, 0xad, 0xba, 0x55, 0xad, 0xf7, 0x71, 0x7d, 0x1b, 0x87, 0xc4, 0x24,
	0x17, 0x66, 0xb9, 0x17, 0x2f, 0xd0, 0x03, 0x80, 0x91, 0x77, 0xea, 0xe8, 0xb9, 0x9a, 0xb2, 0xb6,
	0x60, 0x56, 0x12, 0x4d, 0xd3, 0x41, 0xcb, 0xa0, 0x7e, 0xcb, 0x29, 0xb3, 0x06, 0xd4, 0xd1, 0xf3,
	0xd2, 0x58, 0x16, 0x72, 0x97, 0x3a, 0xe8, 0x43, 0xb8, 0xe5, 0x72, 0x1b, 0x47, 0x94, 0x33, 0x8b,
	0x32, 0x87, 0xbc, 0xd6, 0x0b, 0x72, 0xc3, 0xc2, 0x48, 0xdb, 0x14, 0x4a, 0xf4, 0x10, 0xc6, 0x0a,
	0x8b, 0x61, 0x8f, 0xe8, 0xc5, 0x9a, 0xb2, 0x56, 0x31, 0xe7, 0x47, 0xca, 0x16, 0xf6, 0x08, 0xfa,
	0x02, 0x0a, 0xd1, 0xd0, 0x27, 0x7a, 0xa9, 0xa6, 0xac, 0xdd, 0xda, 0x5a, 0xaf, 0x5f, 0xc9, 0xb9,
	0x9e, 0x4d, 0xb0, 0xfe, 0xdc, 0x26, 0x8c, 0x98, 0xf2, 0x33, 0xe3, 0x31, 0x14, 0xa5, 0x88, 0x2a,
	0x50, 0x6c, 0x77, 0x0e, 0xf6, 0x4c, 0x6d, 0x0e, 0x55, 0xa1, 0x7c, 0xd0, 0x3e, 0xda, 0xb5, 0xda,
	0x2d, 0x4d, 0x11, 0xc2, 0x49, 0x73, 0xe7, 0xd0, 0xea, 0x9e, 0x68, 0x39, 0xe3, 0x2b, 0xb8, 0x9d,
	0xf1, 0x17, 0xfa, 0x68, 0x1d, 0x2a, 0x49, 0xc5, 0x42, 0x3f, 0x29, 0xd9, 0xfc, 0x65, 0xc9, 0x42,
	0xdf, 0x54, 0x7b, 0xc9, 0xca, 0xf8, 0x45, 0x81, 0x95, 0x7d, 0x12, 0xa5, 0x3c, 0x34, 0x7c, 0xdf,
	0x1d, 0x1e, 0xd1, 0x30, 0x9a, 0x61, 0xed, 0xef, 0x41, 0x89, 0x9f, 0x9e, 0x86, 0x24, 0x4a, 0x2a,
	0x9f, 0x48, 0xe8, 0x0e, 0x14, 0x6d, 0x3e, 0x60, 0x51, 0x52, 0xef, 0x58, 0x30, 0x7e, 0x54, 0x60,
	0xf5, 0xc6, 0x98, 0xa6, 0x4a, 0x0f, 0x7d, 0x06, 0x80, 0xc5, 0xb7, 0x96, 0x4b, 0xc3, 0x48, 0xcf,
	0xd5, 0xf2, 0x6b, 0xd5, 0xad, 0xfb, 0x19, 0x4c, 0xa4, 0xfb, 0x63, 0xe2, 0xf5, 0x48, 0x60, 0x56,
	0xf0, 0xe8, 0x2c, 0xe3, 0x2f, 0x05, 0xaa, 0x29, 0x13, 0xd2, 0x20, 0x2f, 0xc8, 0xa3, 0xc8, 0x58,
	0xc5, 0x12, 0xe9, 0x50, 0xc6, 0x76, 0x9c, 0x41, 0x4e, 0x72, 0x61, 0x24, 0xa2, 0x15, 0x50, 0x19,
	0xb5, 0xcf, 0x25, 0x4d, 0xf2, 0xd2, 0x34, 0x96, 0x85, 0x9f, 0x30, 0xe1, 0x58, 0xd1, 0x14, 0x4b,
	0xa1, 0x71, 0x30, 0x4b, 0xf8, 0x24, 0x96, 0x68, 0x09, 0xca, 0x0e, 0x66, 0xd6, 0x20, 0x70, 0x25,
	0x93, 0x2a, 0x66, 0xc9, 0xc1, 0xac, 0x1b, 0xb8, 0x08, 0x41, 0xc1, 0x21, 0xa1, 0xad, 0x97, 0xa5,
	0x56, 0xae, 0xb3, 0xc4, 0x54, 0xb3, 0xc4, 0x34, 0xde, 0x80, 0xde, 0xe8, 0x07, 0x84, 0x5c, 0x2d,
	0xeb, 0x7f, 0xd2, 0x62, 0xc6, 0xd7, 0xb0, 0x7c, 0xc3, 0xe9, 0xd3, 0xf1, 0xf5, 0x0d, 0xe8, 0x1d,
	0x6a, 0x9f, 0xa7, 0xdc, 0x24, 0xb8, 0xcd, 0x34, 0x8b, 0x88, 0xda, 0xe7, 0xe9, 0x2c, 0x84, 0x9c,
	0x64, 0x71, 0xc3, 0xe9, 0xd3, 0x65, 0xf1, 0x47, 0x86, 0xe1, 0xb1, 0x9f, 0x19, 0xb7, 0xdd, 0x5d,
	0x28, 0x45, 0xb8, 0x67, 0x8d, 0xf3, 0x28, 0x46, 0xb8, 0xd7, 0x74, 0x26, 0xe8, 0x22, 0xfb, 0xa2,
	0x50, 0xcb, 0xa7, 0xe9, 0x22, 0xc9, 0xff, 0x3d, 0xdc, 0xbf, 0x39, 0xc2, 0xe9, 0x9a, 0xf0, 0x13,
	0x28, 0x50, 0x76, 0xca, 0x65, 0x7c, 0xd5, 0xad, 0x5a, 0xa6, 0xfd, 0x52, 0x87, 0x34, 0xd9, 0x29,
	0x37, 0xe5, 0x6e, 0xe3, 0x27, 0x05, 0x6e, 0xef, 0x93, 0x68, 0x1f, 0xb3, 0x7e, 0xd7, 0x3f, 0xa0,
	0x61, 0xc4, 0x83, 0xe1, 0xff, 0x36, 0x92, 0x5e, 0xc3, 0x9d, 0x6c, 0x2c, 0xd3, 0x55, 0xe1, 0x29,
	0x94, 0x02, 0x62, 0xf3, 0xc0, 0x49, 0xc6, 0xd0, 0x83, 0x4c, 0x1d, 0x62, 0xf7, 0xa6, 0xdc, 0x64,
	0x26, 0x9b, 0x8d, 0xbf, 0x15, 0x98, 0x4f, 0x1b, 0xae, 0x99, 0x42, 0x1f, 0x40, 0x75, 0xe0, 0x3b,
	0x38, 0x22, 0x56, 0x44, 0x3d, 0x22, 0x53, 0xcd, 0x9b, 0x10, 0xab, 0x3a, 0xd4, 0x23, 0xe8, 0x11,
	0xdc, 0xea, 0x63, 0x8f, 0xc8, 0xd9, 0x10, 0x23, 0x9e, 0x8f, 0x11, 0x17, 0x5a, 0x31, 0x1c, 0x04,
	0xaa, 0xd7, 0x8c, 0xa5, 0xf4, 0x10, 0x2b, 0x5e, 0x19, 0x62, 0xa9, 0xd1, 0x57, 0x9a, 0x1c, 0x7d,
	0xab, 0x50, 0xa1, 0xa1, 0xc5, 0x99, 0x4b, 0x19, 0x91, 0x63, 0x4a, 0x35, 0x55, 0x1a, 0xb6, 0xa5,
	0x6c, 0xfc, 0xac, 0xc0, 0xbd, 0x7d, 0x12, 0x35, 0x5c, 0x37, 0x41, 0x7d, 0xe6, 0xed, 0x3b, 0xf5,
	0xbf, 0x66, 0xe9, 0xda, 0x78, 0xa6, 0x03, 0xf7, 0x4b, 0xa8, 0x7a, 0xf2, 0xc3, 0xf4, 0x8f, 0xe6,
	0xfd, 0x9b, 0x98, 0x9e, 0x9c, 0x01, 0xde, 0xb8, 0xa5, 0x8c, 0x5f, 0x15, 0x58, 0x98, 0xb0, 0xbe,
	0x13, 0x7f, 0x1b, 0xe3, 0x07, 0x05, 0xd0, 0x73, 0x41, 0x7c, 0x8f, 0xb4, 0x12, 0x87, 0x33, 0x84,
	0xea, 0x21, 0x2c, 0xc4, 0xbc, 0x9c, 0x8c, 0x3d, 0xa6, 0x65, 0xa2, 0x13, 0x77, 0x9c, 0x4c, 0x04,
	0xd3, 0x4d, 0xdb, 0x7f, 0x14, 0x58, 0xbc, 0x32, 0x63, 0x04, 0x49, 0xf9, 0x77, 0x8c, 0x04, 0xd6,
	0x65, 0x8d, 0x55, 0xa9, 0x10, 0xf7, 0xc1, 0xcf, 0xaf, 0x43, 0x73, 0x35, 0x83, 0x66, 0x0c, 0x94,
	0x1c, 0x59, 0x29, 0x28, 0x45, 0x3b, 0xda, 0x01, 0x19, 0xb7, 0x63, 0x3e, 0x6e, 0xc7, 0x58, 0x25,
	0xdb, 0x71, 0x09, 0xca, 0x03, 0x6a, 0xc9, 0x5b, 0x62, 0xcc, 0xc5, 0xd2, 0x80, 0x76, 0x86, 0x3e,
	0x11, 0x41, 0x09, 0xc7, 0xe9, 0xcb, 0xa5, 0x2a, 0x14, 0xf2, 0x62, 0xb9, 0x0a, 0x95, 0x71, 0x13,
	0x27, 0x28, 0xa9, 0xa3, 0xfe, 0x35, 0x7e, 0xcf, 0x01, 0x5c, 0x86, 0xf3, 0x6e, 0xdc, 0x54, 0x32,
	0xe8, 0x96, 0xb3, 0xe8, 0xbe, 0xd5, 0xd5, 0x65, 0x62, 0x13, 0xb5, 0x39, 0xd3, 0x2b, 0x93, 0x9b,
	0x9a, 0x36, 0x67, 0x22, 0x0f, 0x87, 0x86, 0xb8, 0xe7, 0x12, 0x47, 0x87, 0x78, 0xea, 0x8c, 0x64,
	0xe3, 0x37, 0x05, 0xee, 0x9e, 0x0c, 0xc2, 0xb3, 0x14, 0x0b, 0xba, 0x72, 0x3c, 0xa2, 0x23, 0x78,
	0x2f, 0x8d, 0xa9, 0x25, 0x7f, 0x54, 0xca, 0x5b, 0xfe, 0xa8, 0x16, 0xed, 0x2c, 0xab, 0xfc, 0x41,
	0x78, 0x96, 0x9e, 0xc3, 0xaa, 0x50, 0x48, 0xd8, 0x51, 0xf2, 0x32, 0x88, 0xc7, 0x52, 0x7c, 0xdd,
	0xff, 0x14, 0xca, 0x3b, 0x51, 0xe0, 0x1e, 0x87, 0xfd, 0xb1, 0x59, 0xb9, 0x34, 0x0b, 0xd4, 0x6c,
	0xce, 0x22, 0x72, 0x89, 0x5a, 0x22, 0x6e, 0x3c, 0x06, 0xe8, 0x4a, 0xd2, 0x48, 0xe2, 0x00, 0x94,
	0xba, 0xad, 0xc3, 0xf6, 0x8b, 0x96, 0x36, 0x87, 0x54, 0x28, 0xbc, 0x78, 0x65, 0xbe, 0xd4, 0x14,
	0xb1, 0x3a, 0x38, 0x79, 0xf6, 0x52, 0xcb, 0x6d, 0x3c, 0x02, 0x88, 0xf3, 0x1d, 0xef, 0x3e, 0xd9,
	0x6d, 0x74, 0xf6, 0xb4, 0x39, 0xb1, 0xde, 0x31, 0xf7, 0xc4, 0x5a, 0xd9, 0xd8, 0x06, 0x55, 0x04,
	0x33, 0xda, 0xd3, 0x6a, 0x9b, 0xc7, 0x8d, 0x23, 0x6d, 0x0e, 0xcd, 0x83, 0xda, 0x11, 0x4f, 0x8e,
	0x76, 0xb7, 0xa3, 0x29, 0xe2, 0x61, 0xb2, 0x6d, 0xee, 0x35, 0x0e, 0xb5, 0x1c, 0x2a, 0x43, 0xbe,
	0xb1, 0xbb, 0xab, 0xe5, 0xc5, 0x49, 0xdf, 0x74, 0x9b, 0x1d, 0xad, 0xb0, 0xbd, 0x07, 0xba, 0xcd,
	0xbd, 0xfa, 0x90, 0x0e, 0xf9, 0x40, 0xd4, 0xcf, 0xe3, 0x0e, 0x71, 0xe3, 0xf7, 0xdd, 0xab, 0xf5,
	0x3e, 0x77, 0x31, 0xeb, 0xd7, 0x9f, 0x6e, 0x45, 0x51, 0xdd, 0xe6, 0xde, 0xa6, 0x54, 0xdb, 0xdc,
	0xdd, 0xc4, 0xbe, 0xbf, 0x99, 0x7e, 0x37, 0xf6, 0x4a, 0xd2, 0xf4, 0xf1, 0xbf, 0x01, 0x00, 0x00,
	0xff, 0xff, 0x13, 0xb4, 0x76, 0x73, 0x4e, 0x0e, 0x00, 0x00,
}
