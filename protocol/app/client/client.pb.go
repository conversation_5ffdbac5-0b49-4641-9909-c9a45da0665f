// Code generated by protoc-gen-gogo.
// source: client.proto
// DO NOT EDIT!

/*
	Package client is a generated protocol buffer package.

	It is generated from these files:
		client.proto

	It has these top-level messages:
		AttachmentDescription
		SyncConfig
		GameCircleConfig
		RootAlertConfig
		GameConfigLastModify
		ServerTimeSync
		MagicMaskSync
		LatestPushSeq
		LatestAppVersionInfo
*/
package client

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"

import "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ClientCMDType int32

const (
	ClientCMDType_TYPE_SYNC_CFG             ClientCMDType = 1
	ClientCMDType_TYPE_APP_VERSION          ClientCMDType = 2
	ClientCMDType_TYPE_GAME_CIRCLE_CFG      ClientCMDType = 3
	ClientCMDType_TYPE_UNBIND_BOTH          ClientCMDType = 4
	ClientCMDType_TYPE_ROOT_ALERT_CFG       ClientCMDType = 5
	ClientCMDType_TYPE_GAME_CFG_LAST_MODIFY ClientCMDType = 6
	ClientCMDType_TYPE_SERVER_TIME_SYNC     ClientCMDType = 7
	ClientCMDType_TYPE_MODIFY_SYNC_KEYS     ClientCMDType = 9
	ClientCMDType_TYPE_PLUGIN_URL           ClientCMDType = 10
	ClientCMDType_TYPE_MAGIC_MASK           ClientCMDType = 11
	ClientCMDType_TYPE_LATEST_PUSH_SEQ      ClientCMDType = 12
	ClientCMDType_TYPE_QINIU_LOG_TOKEN      ClientCMDType = 13
	ClientCMDType_TYPE_WEB_SECURE_TOKEN     ClientCMDType = 14
	ClientCMDType_TYPE_TRANSPORT_CONFIG     ClientCMDType = 15
)

var ClientCMDType_name = map[int32]string{
	1:  "TYPE_SYNC_CFG",
	2:  "TYPE_APP_VERSION",
	3:  "TYPE_GAME_CIRCLE_CFG",
	4:  "TYPE_UNBIND_BOTH",
	5:  "TYPE_ROOT_ALERT_CFG",
	6:  "TYPE_GAME_CFG_LAST_MODIFY",
	7:  "TYPE_SERVER_TIME_SYNC",
	9:  "TYPE_MODIFY_SYNC_KEYS",
	10: "TYPE_PLUGIN_URL",
	11: "TYPE_MAGIC_MASK",
	12: "TYPE_LATEST_PUSH_SEQ",
	13: "TYPE_QINIU_LOG_TOKEN",
	14: "TYPE_WEB_SECURE_TOKEN",
	15: "TYPE_TRANSPORT_CONFIG",
}
var ClientCMDType_value = map[string]int32{
	"TYPE_SYNC_CFG":             1,
	"TYPE_APP_VERSION":          2,
	"TYPE_GAME_CIRCLE_CFG":      3,
	"TYPE_UNBIND_BOTH":          4,
	"TYPE_ROOT_ALERT_CFG":       5,
	"TYPE_GAME_CFG_LAST_MODIFY": 6,
	"TYPE_SERVER_TIME_SYNC":     7,
	"TYPE_MODIFY_SYNC_KEYS":     9,
	"TYPE_PLUGIN_URL":           10,
	"TYPE_MAGIC_MASK":           11,
	"TYPE_LATEST_PUSH_SEQ":      12,
	"TYPE_QINIU_LOG_TOKEN":      13,
	"TYPE_WEB_SECURE_TOKEN":     14,
	"TYPE_TRANSPORT_CONFIG":     15,
}

func (x ClientCMDType) Enum() *ClientCMDType {
	p := new(ClientCMDType)
	*p = x
	return p
}
func (x ClientCMDType) String() string {
	return proto.EnumName(ClientCMDType_name, int32(x))
}
func (x *ClientCMDType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ClientCMDType_value, data, "ClientCMDType")
	if err != nil {
		return err
	}
	*x = ClientCMDType(value)
	return nil
}
func (ClientCMDType) EnumDescriptor() ([]byte, []int) { return fileDescriptorClient, []int{0} }

type GameCircleState int32

const (
	GameCircleState_STATE_HIDEALL GameCircleState = 0
	GameCircleState_STATE_SHOWALL GameCircleState = 1
)

var GameCircleState_name = map[int32]string{
	0: "STATE_HIDEALL",
	1: "STATE_SHOWALL",
}
var GameCircleState_value = map[string]int32{
	"STATE_HIDEALL": 0,
	"STATE_SHOWALL": 1,
}

func (x GameCircleState) Enum() *GameCircleState {
	p := new(GameCircleState)
	*p = x
	return p
}
func (x GameCircleState) String() string {
	return proto.EnumName(GameCircleState_name, int32(x))
}
func (x *GameCircleState) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GameCircleState_value, data, "GameCircleState")
	if err != nil {
		return err
	}
	*x = GameCircleState(value)
	return nil
}
func (GameCircleState) EnumDescriptor() ([]byte, []int) { return fileDescriptorClient, []int{1} }

type AttachmentDescription struct {
	Type        uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	FileSize    uint32 `protobuf:"varint,2,req,name=file_size,json=fileSize" json:"file_size"`
	VoiceLength int64  `protobuf:"varint,3,opt,name=voice_length,json=voiceLength" json:"voice_length"`
	ReadStatus  uint32 `protobuf:"varint,4,opt,name=read_status,json=readStatus" json:"read_status"`
}

func (m *AttachmentDescription) Reset()                    { *m = AttachmentDescription{} }
func (m *AttachmentDescription) String() string            { return proto.CompactTextString(m) }
func (*AttachmentDescription) ProtoMessage()               {}
func (*AttachmentDescription) Descriptor() ([]byte, []int) { return fileDescriptorClient, []int{0} }

func (m *AttachmentDescription) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AttachmentDescription) GetFileSize() uint32 {
	if m != nil {
		return m.FileSize
	}
	return 0
}

func (m *AttachmentDescription) GetVoiceLength() int64 {
	if m != nil {
		return m.VoiceLength
	}
	return 0
}

func (m *AttachmentDescription) GetReadStatus() uint32 {
	if m != nil {
		return m.ReadStatus
	}
	return 0
}

// 其实是黑名单包名
type SyncConfig struct {
	CfgList []string `protobuf:"bytes,1,rep,name=cfg_list,json=cfgList" json:"cfg_list,omitempty"`
}

func (m *SyncConfig) Reset()                    { *m = SyncConfig{} }
func (m *SyncConfig) String() string            { return proto.CompactTextString(m) }
func (*SyncConfig) ProtoMessage()               {}
func (*SyncConfig) Descriptor() ([]byte, []int) { return fileDescriptorClient, []int{1} }

func (m *SyncConfig) GetCfgList() []string {
	if m != nil {
		return m.CfgList
	}
	return nil
}

// 游戏圈配置
type GameCircleConfig struct {
	State uint32 `protobuf:"varint,1,req,name=state" json:"state"`
}

func (m *GameCircleConfig) Reset()                    { *m = GameCircleConfig{} }
func (m *GameCircleConfig) String() string            { return proto.CompactTextString(m) }
func (*GameCircleConfig) ProtoMessage()               {}
func (*GameCircleConfig) Descriptor() ([]byte, []int) { return fileDescriptorClient, []int{2} }

func (m *GameCircleConfig) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

// 其他应用Root权限警告配置
type RootAlertConfig struct {
	State uint32 `protobuf:"varint,1,req,name=state" json:"state"`
}

func (m *RootAlertConfig) Reset()                    { *m = RootAlertConfig{} }
func (m *RootAlertConfig) String() string            { return proto.CompactTextString(m) }
func (*RootAlertConfig) ProtoMessage()               {}
func (*RootAlertConfig) Descriptor() ([]byte, []int) { return fileDescriptorClient, []int{3} }

func (m *RootAlertConfig) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

// 浮窗游戏黑白名单
type GameConfigLastModify struct {
	LastUpdate uint32 `protobuf:"varint,1,req,name=last_update,json=lastUpdate" json:"last_update"`
}

func (m *GameConfigLastModify) Reset()                    { *m = GameConfigLastModify{} }
func (m *GameConfigLastModify) String() string            { return proto.CompactTextString(m) }
func (*GameConfigLastModify) ProtoMessage()               {}
func (*GameConfigLastModify) Descriptor() ([]byte, []int) { return fileDescriptorClient, []int{4} }

func (m *GameConfigLastModify) GetLastUpdate() uint32 {
	if m != nil {
		return m.LastUpdate
	}
	return 0
}

type ServerTimeSync struct {
	SrvTime uint32 `protobuf:"varint,1,req,name=srv_time,json=srvTime" json:"srv_time"`
}

func (m *ServerTimeSync) Reset()                    { *m = ServerTimeSync{} }
func (m *ServerTimeSync) String() string            { return proto.CompactTextString(m) }
func (*ServerTimeSync) ProtoMessage()               {}
func (*ServerTimeSync) Descriptor() ([]byte, []int) { return fileDescriptorClient, []int{5} }

func (m *ServerTimeSync) GetSrvTime() uint32 {
	if m != nil {
		return m.SrvTime
	}
	return 0
}

type MagicMaskSync struct {
	MagicMask uint32 `protobuf:"varint,1,req,name=magic_mask,json=magicMask" json:"magic_mask"`
}

func (m *MagicMaskSync) Reset()                    { *m = MagicMaskSync{} }
func (m *MagicMaskSync) String() string            { return proto.CompactTextString(m) }
func (*MagicMaskSync) ProtoMessage()               {}
func (*MagicMaskSync) Descriptor() ([]byte, []int) { return fileDescriptorClient, []int{6} }

func (m *MagicMaskSync) GetMagicMask() uint32 {
	if m != nil {
		return m.MagicMask
	}
	return 0
}

type LatestPushSeq struct {
	LatestSeq uint32 `protobuf:"varint,1,req,name=latest_seq,json=latestSeq" json:"latest_seq"`
}

func (m *LatestPushSeq) Reset()                    { *m = LatestPushSeq{} }
func (m *LatestPushSeq) String() string            { return proto.CompactTextString(m) }
func (*LatestPushSeq) ProtoMessage()               {}
func (*LatestPushSeq) Descriptor() ([]byte, []int) { return fileDescriptorClient, []int{7} }

func (m *LatestPushSeq) GetLatestSeq() uint32 {
	if m != nil {
		return m.LatestSeq
	}
	return 0
}

type LatestAppVersionInfo struct {
	Version     string `protobuf:"bytes,1,req,name=version" json:"version"`
	VersionCode uint32 `protobuf:"varint,2,req,name=version_code,json=versionCode" json:"version_code"`
}

func (m *LatestAppVersionInfo) Reset()                    { *m = LatestAppVersionInfo{} }
func (m *LatestAppVersionInfo) String() string            { return proto.CompactTextString(m) }
func (*LatestAppVersionInfo) ProtoMessage()               {}
func (*LatestAppVersionInfo) Descriptor() ([]byte, []int) { return fileDescriptorClient, []int{8} }

func (m *LatestAppVersionInfo) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *LatestAppVersionInfo) GetVersionCode() uint32 {
	if m != nil {
		return m.VersionCode
	}
	return 0
}

func init() {
	proto.RegisterType((*AttachmentDescription)(nil), "ga.AttachmentDescription")
	proto.RegisterType((*SyncConfig)(nil), "ga.SyncConfig")
	proto.RegisterType((*GameCircleConfig)(nil), "ga.GameCircleConfig")
	proto.RegisterType((*RootAlertConfig)(nil), "ga.RootAlertConfig")
	proto.RegisterType((*GameConfigLastModify)(nil), "ga.GameConfigLastModify")
	proto.RegisterType((*ServerTimeSync)(nil), "ga.ServerTimeSync")
	proto.RegisterType((*MagicMaskSync)(nil), "ga.MagicMaskSync")
	proto.RegisterType((*LatestPushSeq)(nil), "ga.LatestPushSeq")
	proto.RegisterType((*LatestAppVersionInfo)(nil), "ga.LatestAppVersionInfo")
	proto.RegisterEnum("ga.ClientCMDType", ClientCMDType_name, ClientCMDType_value)
	proto.RegisterEnum("ga.GameCircleState", GameCircleState_name, GameCircleState_value)
}
func (m *AttachmentDescription) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AttachmentDescription) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.FileSize))
	dAtA[i] = 0x18
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.VoiceLength))
	dAtA[i] = 0x20
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.ReadStatus))
	return i, nil
}

func (m *SyncConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SyncConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CfgList) > 0 {
		for _, s := range m.CfgList {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GameCircleConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameCircleConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.State))
	return i, nil
}

func (m *RootAlertConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RootAlertConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.State))
	return i, nil
}

func (m *GameConfigLastModify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameConfigLastModify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.LastUpdate))
	return i, nil
}

func (m *ServerTimeSync) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServerTimeSync) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.SrvTime))
	return i, nil
}

func (m *MagicMaskSync) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MagicMaskSync) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.MagicMask))
	return i, nil
}

func (m *LatestPushSeq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LatestPushSeq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.LatestSeq))
	return i, nil
}

func (m *LatestAppVersionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LatestAppVersionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintClient(dAtA, i, uint64(len(m.Version)))
	i += copy(dAtA[i:], m.Version)
	dAtA[i] = 0x10
	i++
	i = encodeVarintClient(dAtA, i, uint64(m.VersionCode))
	return i, nil
}

func encodeFixed64Client(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Client(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintClient(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AttachmentDescription) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClient(uint64(m.Type))
	n += 1 + sovClient(uint64(m.FileSize))
	n += 1 + sovClient(uint64(m.VoiceLength))
	n += 1 + sovClient(uint64(m.ReadStatus))
	return n
}

func (m *SyncConfig) Size() (n int) {
	var l int
	_ = l
	if len(m.CfgList) > 0 {
		for _, s := range m.CfgList {
			l = len(s)
			n += 1 + l + sovClient(uint64(l))
		}
	}
	return n
}

func (m *GameCircleConfig) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClient(uint64(m.State))
	return n
}

func (m *RootAlertConfig) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClient(uint64(m.State))
	return n
}

func (m *GameConfigLastModify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClient(uint64(m.LastUpdate))
	return n
}

func (m *ServerTimeSync) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClient(uint64(m.SrvTime))
	return n
}

func (m *MagicMaskSync) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClient(uint64(m.MagicMask))
	return n
}

func (m *LatestPushSeq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovClient(uint64(m.LatestSeq))
	return n
}

func (m *LatestAppVersionInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Version)
	n += 1 + l + sovClient(uint64(l))
	n += 1 + sovClient(uint64(m.VersionCode))
	return n
}

func sovClient(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozClient(x uint64) (n int) {
	return sovClient(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *AttachmentDescription) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClient
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: AttachmentDescription: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: AttachmentDescription: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field FileSize", wireType)
			}
			m.FileSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FileSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field VoiceLength", wireType)
			}
			m.VoiceLength = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoiceLength |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ReadStatus", wireType)
			}
			m.ReadStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReadStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipClient(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClient
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("file_size")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SyncConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClient
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: SyncConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: SyncConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CfgList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClient
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CfgList = append(m.CfgList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipClient(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClient
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameCircleConfig) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClient
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GameCircleConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GameCircleConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipClient(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClient
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("state")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RootAlertConfig) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClient
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: RootAlertConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: RootAlertConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipClient(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClient
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("state")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameConfigLastModify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClient
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: GameConfigLastModify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: GameConfigLastModify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LastUpdate", wireType)
			}
			m.LastUpdate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipClient(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClient
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("last_update")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServerTimeSync) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClient
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: ServerTimeSync: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: ServerTimeSync: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SrvTime", wireType)
			}
			m.SrvTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SrvTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipClient(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClient
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("srv_time")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MagicMaskSync) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClient
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: MagicMaskSync: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: MagicMaskSync: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MagicMask", wireType)
			}
			m.MagicMask = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MagicMask |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipClient(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClient
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("magic_mask")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LatestPushSeq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClient
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: LatestPushSeq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: LatestPushSeq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LatestSeq", wireType)
			}
			m.LatestSeq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LatestSeq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipClient(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClient
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("latest_seq")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LatestAppVersionInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowClient
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: LatestAppVersionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: LatestAppVersionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthClient
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Version = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field VersionCode", wireType)
			}
			m.VersionCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowClient
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VersionCode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipClient(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthClient
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("version")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("version_code")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipClient(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowClient
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowClient
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowClient
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthClient
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowClient
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipClient(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthClient = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowClient   = fmt1.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("client.proto", fileDescriptorClient) }

var fileDescriptorClient = []byte{
	// 730 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x94, 0x4f, 0x6e, 0xdb, 0x46,
	0x14, 0xc6, 0x4d, 0xd9, 0xa9, 0xad, 0x67, 0x2b, 0x66, 0x19, 0x07, 0x95, 0x0b, 0x54, 0x71, 0x15,
	0x14, 0x31, 0x02, 0x54, 0x46, 0x8b, 0x16, 0x5d, 0x75, 0x41, 0x51, 0x63, 0x9a, 0x30, 0xff, 0x28,
	0x1c, 0xca, 0x81, 0xbb, 0x19, 0x10, 0xd4, 0x88, 0x1e, 0x84, 0xe4, 0xd0, 0x9c, 0x91, 0x00, 0xe5,
	0x14, 0x3d, 0x44, 0x0f, 0x93, 0x65, 0x4f, 0x50, 0x14, 0xee, 0xae, 0xa7, 0x28, 0x38, 0x54, 0x18,
	0xab, 0xab, 0xec, 0xa4, 0xdf, 0xf7, 0x7d, 0x8f, 0xef, 0x91, 0x6f, 0x06, 0x8e, 0x92, 0x8c, 0xd1,
	0x42, 0x8e, 0xca, 0x8a, 0x4b, 0x6e, 0x74, 0xd2, 0x78, 0xf8, 0x87, 0x06, 0xcf, 0x4d, 0x29, 0xe3,
	0xe4, 0x2e, 0xa7, 0x85, 0x9c, 0x50, 0x91, 0x54, 0xac, 0x94, 0x8c, 0x17, 0x46, 0x1f, 0xf6, 0xe4,
	0xba, 0xa4, 0x7d, 0xed, 0xac, 0x73, 0xde, 0x1b, 0xef, 0x7d, 0xf8, 0xeb, 0xc5, 0x4e, 0xa8, 0x88,
	0xf1, 0x2d, 0x74, 0x17, 0x2c, 0xa3, 0x44, 0xb0, 0xf7, 0xb4, 0xdf, 0x79, 0x24, 0x1f, 0xd4, 0x18,
	0xb3, 0xf7, 0xd4, 0x78, 0x05, 0x47, 0x2b, 0xce, 0x12, 0x4a, 0x32, 0x5a, 0xa4, 0xf2, 0xae, 0xbf,
	0x7b, 0xa6, 0x9d, 0xef, 0x6e, 0x5c, 0x87, 0x4a, 0x71, 0x95, 0x60, 0x7c, 0x07, 0x87, 0x15, 0x8d,
	0xe7, 0x44, 0xc8, 0x58, 0x2e, 0x45, 0x7f, 0xef, 0x4c, 0x6b, 0xab, 0x41, 0x2d, 0x60, 0xc5, 0x87,
	0xaf, 0x00, 0xf0, 0xba, 0x48, 0x2c, 0x5e, 0x2c, 0x58, 0x6a, 0x9c, 0xc2, 0x41, 0xb2, 0x48, 0x49,
	0xc6, 0x84, 0xec, 0x6b, 0x67, 0xbb, 0xe7, 0xdd, 0x70, 0x3f, 0x59, 0xa4, 0x2e, 0x13, 0x72, 0x38,
	0x02, 0xdd, 0x8e, 0x73, 0x6a, 0xb1, 0x2a, 0xc9, 0xe8, 0xc6, 0xfe, 0x35, 0x3c, 0xa9, 0xcb, 0x6f,
	0x8f, 0xd2, 0xa0, 0xe1, 0xf7, 0x70, 0x1c, 0x72, 0x2e, 0xcd, 0x8c, 0x56, 0xf2, 0x33, 0xec, 0xbf,
	0xc2, 0x89, 0x2a, 0xaf, 0x9c, 0x6e, 0x2c, 0xa4, 0xc7, 0xe7, 0x6c, 0xb1, 0xae, 0xc7, 0xc8, 0x62,
	0x21, 0xc9, 0xb2, 0x9c, 0xff, 0x3f, 0x09, 0xb5, 0x30, 0x53, 0x7c, 0xf8, 0x03, 0x3c, 0xc5, 0xb4,
	0x5a, 0xd1, 0x2a, 0x62, 0x39, 0xad, 0x07, 0x32, 0x5e, 0xc0, 0x81, 0xa8, 0x56, 0x44, 0xb2, 0x7c,
	0x3b, 0xb5, 0x2f, 0xaa, 0x55, 0x6d, 0x1a, 0xfe, 0x04, 0x3d, 0x2f, 0x4e, 0x59, 0xe2, 0xc5, 0xe2,
	0x9d, 0x4a, 0xbc, 0x04, 0xc8, 0x6b, 0x40, 0xf2, 0x58, 0xbc, 0xdb, 0xca, 0x74, 0xf3, 0x8f, 0xc6,
	0x3a, 0xe5, 0xc6, 0x92, 0x0a, 0x39, 0x5d, 0x8a, 0x3b, 0x4c, 0xef, 0xeb, 0x54, 0xa6, 0x00, 0x11,
	0xf4, 0x7e, 0x3b, 0xd5, 0x70, 0x4c, 0xef, 0x87, 0x04, 0x4e, 0x9a, 0x94, 0x59, 0x96, 0x37, 0xb4,
	0x12, 0x8c, 0x17, 0x4e, 0xb1, 0xe0, 0xc6, 0x00, 0xf6, 0x57, 0xcd, 0x5f, 0x95, 0xec, 0x7e, 0xec,
	0x71, 0x03, 0xd5, 0xd7, 0x6e, 0x7e, 0x92, 0x84, 0xcf, 0xb7, 0x77, 0xe2, 0x70, 0xa3, 0x58, 0x7c,
	0x4e, 0x5f, 0xff, 0xdb, 0x81, 0x9e, 0xa5, 0x56, 0xd0, 0xf2, 0x26, 0x51, 0xbd, 0x4b, 0x5f, 0x42,
	0x2f, 0xba, 0x9d, 0x22, 0x82, 0x6f, 0x7d, 0x8b, 0x58, 0x97, 0xb6, 0xae, 0x19, 0x27, 0xa0, 0x2b,
	0x64, 0x4e, 0xa7, 0xe4, 0x06, 0x85, 0xd8, 0x09, 0x7c, 0xbd, 0x63, 0xf4, 0xe1, 0x44, 0x51, 0xdb,
	0xf4, 0x10, 0xb1, 0x9c, 0xd0, 0x72, 0x91, 0xf2, 0xef, 0xb6, 0xfe, 0x99, 0x3f, 0x76, 0xfc, 0x09,
	0x19, 0x07, 0xd1, 0x95, 0xbe, 0x67, 0x7c, 0x05, 0xcf, 0x14, 0x0d, 0x83, 0x20, 0x22, 0xa6, 0x8b,
	0xc2, 0x48, 0xd9, 0x9f, 0x18, 0xdf, 0xc0, 0xe9, 0xa3, 0x42, 0x97, 0x36, 0x71, 0x4d, 0x1c, 0x11,
	0x2f, 0x98, 0x38, 0x97, 0xb7, 0xfa, 0x17, 0xc6, 0x29, 0x3c, 0x6f, 0x1a, 0x42, 0xe1, 0x0d, 0x0a,
	0x49, 0xe4, 0x78, 0x4d, 0x73, 0xfa, 0x7e, 0x2b, 0x35, 0xde, 0xa6, 0xe5, 0x6b, 0x74, 0x8b, 0xf5,
	0xae, 0xf1, 0x0c, 0x8e, 0x95, 0x34, 0x75, 0x67, 0xb6, 0xe3, 0x93, 0x59, 0xe8, 0xea, 0xd0, 0x42,
	0xcf, 0xb4, 0x1d, 0x8b, 0x78, 0x26, 0xbe, 0xd6, 0x0f, 0xdb, 0x39, 0x5c, 0x33, 0x42, 0x38, 0x22,
	0xd3, 0x19, 0xbe, 0x22, 0x18, 0xbd, 0xd1, 0x8f, 0x5a, 0xe5, 0x8d, 0xe3, 0x3b, 0x33, 0xe2, 0x06,
	0x36, 0x89, 0x82, 0x6b, 0xe4, 0xeb, 0xbd, 0xf6, 0xc1, 0x6f, 0xd1, 0x98, 0x60, 0x64, 0xcd, 0x42,
	0xb4, 0x91, 0x9e, 0xb6, 0x52, 0x14, 0x9a, 0x3e, 0x9e, 0x06, 0xf5, 0x94, 0x81, 0x7f, 0xe9, 0xd8,
	0xfa, 0xf1, 0xeb, 0x5f, 0xe0, 0xf8, 0xd3, 0x51, 0xa8, 0xcf, 0x91, 0x7a, 0xdb, 0x38, 0x32, 0x23,
	0x44, 0xae, 0x9c, 0x09, 0x32, 0x5d, 0x57, 0xdf, 0xf9, 0x84, 0xf0, 0x55, 0xf0, 0xb6, 0x46, 0xda,
	0x38, 0xf8, 0xf0, 0x30, 0xd0, 0xfe, 0x7c, 0x18, 0x68, 0x7f, 0x3f, 0x0c, 0xb4, 0xdf, 0xff, 0x19,
	0xec, 0x40, 0x3f, 0xe1, 0xf9, 0x68, 0xcd, 0xd6, 0x7c, 0x39, 0x4a, 0xe3, 0x51, 0xce, 0xe7, 0x34,
	0x6b, 0xee, 0x90, 0xdf, 0x5e, 0xa6, 0x3c, 0x8b, 0x8b, 0x74, 0xf4, 0xf3, 0x8f, 0x52, 0x8e, 0x12,
	0x9e, 0x5f, 0x28, 0x9c, 0xf0, 0xec, 0x22, 0x2e, 0xcb, 0x8b, 0xe6, 0xba, 0xf9, 0x2f, 0x00, 0x00,
	0xff, 0xff, 0x15, 0x45, 0x1e, 0x15, 0x77, 0x04, 0x00, 0x00,
}
