// Code generated by protoc-gen-go. DO NOT EDIT.
// source: im-promote-logic_.proto

package impromotelogic // import "golang.52tt.com/protocol/app/impromotelogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PushButtonType int32

const (
	PushButtonType_Default         PushButtonType = 0
	PushButtonType_CommentAndAt    PushButtonType = 1
	PushButtonType_FANS            PushButtonType = 2
	PushButtonType_Careful         PushButtonType = 3
	PushButtonType_LiveStart       PushButtonType = 4
	PushButtonType_Attitude        PushButtonType = 5
	PushButtonType_DiyRecommend    PushButtonType = 6
	PushButtonType_EnterRoomNotify PushButtonType = 7
)

var PushButtonType_name = map[int32]string{
	0: "Default",
	1: "CommentAndAt",
	2: "FANS",
	3: "Careful",
	4: "LiveStart",
	5: "Attitude",
	6: "DiyRecommend",
	7: "EnterRoomNotify",
}
var PushButtonType_value = map[string]int32{
	"Default":         0,
	"CommentAndAt":    1,
	"FANS":            2,
	"Careful":         3,
	"LiveStart":       4,
	"Attitude":        5,
	"DiyRecommend":    6,
	"EnterRoomNotify": 7,
}

func (x PushButtonType) String() string {
	return proto.EnumName(PushButtonType_name, int32(x))
}
func (PushButtonType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{0}
}

type OpenPageType int32

const (
	OpenPageType_MsgType     OpenPageType = 0
	OpenPageType_ConcealType OpenPageType = 1
)

var OpenPageType_name = map[int32]string{
	0: "MsgType",
	1: "ConcealType",
}
var OpenPageType_value = map[string]int32{
	"MsgType":     0,
	"ConcealType": 1,
}

func (x OpenPageType) String() string {
	return proto.EnumName(OpenPageType_name, int32(x))
}
func (OpenPageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{1}
}

// 一起玩客户端透传的消息结构
type ImPlayTogetherMsg struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CipherText           string   `protobuf:"bytes,2,opt,name=cipher_text,json=cipherText,proto3" json:"cipher_text,omitempty"`
	RoomType             int32    `protobuf:"varint,3,opt,name=room_type,json=roomType,proto3" json:"room_type,omitempty"`
	TimeOutEndTime       int64    `protobuf:"varint,4,opt,name=time_out_end_time,json=timeOutEndTime,proto3" json:"time_out_end_time,omitempty"`
	FollowUid            uint32   `protobuf:"varint,5,opt,name=follow_uid,json=followUid,proto3" json:"follow_uid,omitempty"`
	TabName              string   `protobuf:"bytes,6,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TabLogoUrl           string   `protobuf:"bytes,7,opt,name=tab_logo_url,json=tabLogoUrl,proto3" json:"tab_logo_url,omitempty"`
	BgColor              string   `protobuf:"bytes,8,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImPlayTogetherMsg) Reset()         { *m = ImPlayTogetherMsg{} }
func (m *ImPlayTogetherMsg) String() string { return proto.CompactTextString(m) }
func (*ImPlayTogetherMsg) ProtoMessage()    {}
func (*ImPlayTogetherMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{0}
}
func (m *ImPlayTogetherMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImPlayTogetherMsg.Unmarshal(m, b)
}
func (m *ImPlayTogetherMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImPlayTogetherMsg.Marshal(b, m, deterministic)
}
func (dst *ImPlayTogetherMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImPlayTogetherMsg.Merge(dst, src)
}
func (m *ImPlayTogetherMsg) XXX_Size() int {
	return xxx_messageInfo_ImPlayTogetherMsg.Size(m)
}
func (m *ImPlayTogetherMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ImPlayTogetherMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ImPlayTogetherMsg proto.InternalMessageInfo

func (m *ImPlayTogetherMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ImPlayTogetherMsg) GetCipherText() string {
	if m != nil {
		return m.CipherText
	}
	return ""
}

func (m *ImPlayTogetherMsg) GetRoomType() int32 {
	if m != nil {
		return m.RoomType
	}
	return 0
}

func (m *ImPlayTogetherMsg) GetTimeOutEndTime() int64 {
	if m != nil {
		return m.TimeOutEndTime
	}
	return 0
}

func (m *ImPlayTogetherMsg) GetFollowUid() uint32 {
	if m != nil {
		return m.FollowUid
	}
	return 0
}

func (m *ImPlayTogetherMsg) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ImPlayTogetherMsg) GetTabLogoUrl() string {
	if m != nil {
		return m.TabLogoUrl
	}
	return ""
}

func (m *ImPlayTogetherMsg) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

type ImPromoteLogicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ImPromoteLogicReq) Reset()         { *m = ImPromoteLogicReq{} }
func (m *ImPromoteLogicReq) String() string { return proto.CompactTextString(m) }
func (*ImPromoteLogicReq) ProtoMessage()    {}
func (*ImPromoteLogicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{1}
}
func (m *ImPromoteLogicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImPromoteLogicReq.Unmarshal(m, b)
}
func (m *ImPromoteLogicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImPromoteLogicReq.Marshal(b, m, deterministic)
}
func (dst *ImPromoteLogicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImPromoteLogicReq.Merge(dst, src)
}
func (m *ImPromoteLogicReq) XXX_Size() int {
	return xxx_messageInfo_ImPromoteLogicReq.Size(m)
}
func (m *ImPromoteLogicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ImPromoteLogicReq.DiscardUnknown(m)
}

var xxx_messageInfo_ImPromoteLogicReq proto.InternalMessageInfo

func (m *ImPromoteLogicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ImPromoteLogicResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ImPromoteLogicResp) Reset()         { *m = ImPromoteLogicResp{} }
func (m *ImPromoteLogicResp) String() string { return proto.CompactTextString(m) }
func (*ImPromoteLogicResp) ProtoMessage()    {}
func (*ImPromoteLogicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{2}
}
func (m *ImPromoteLogicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImPromoteLogicResp.Unmarshal(m, b)
}
func (m *ImPromoteLogicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImPromoteLogicResp.Marshal(b, m, deterministic)
}
func (dst *ImPromoteLogicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImPromoteLogicResp.Merge(dst, src)
}
func (m *ImPromoteLogicResp) XXX_Size() int {
	return xxx_messageInfo_ImPromoteLogicResp.Size(m)
}
func (m *ImPromoteLogicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ImPromoteLogicResp.DiscardUnknown(m)
}

var xxx_messageInfo_ImPromoteLogicResp proto.InternalMessageInfo

func (m *ImPromoteLogicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取免密进房token
type GetImPromoteLogicTokenReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FollowAccount        string       `protobuf:"bytes,2,opt,name=follow_account,json=followAccount,proto3" json:"follow_account,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetImPromoteLogicTokenReq) Reset()         { *m = GetImPromoteLogicTokenReq{} }
func (m *GetImPromoteLogicTokenReq) String() string { return proto.CompactTextString(m) }
func (*GetImPromoteLogicTokenReq) ProtoMessage()    {}
func (*GetImPromoteLogicTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{3}
}
func (m *GetImPromoteLogicTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetImPromoteLogicTokenReq.Unmarshal(m, b)
}
func (m *GetImPromoteLogicTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetImPromoteLogicTokenReq.Marshal(b, m, deterministic)
}
func (dst *GetImPromoteLogicTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetImPromoteLogicTokenReq.Merge(dst, src)
}
func (m *GetImPromoteLogicTokenReq) XXX_Size() int {
	return xxx_messageInfo_GetImPromoteLogicTokenReq.Size(m)
}
func (m *GetImPromoteLogicTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetImPromoteLogicTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetImPromoteLogicTokenReq proto.InternalMessageInfo

func (m *GetImPromoteLogicTokenReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetImPromoteLogicTokenReq) GetFollowAccount() string {
	if m != nil {
		return m.FollowAccount
	}
	return ""
}

func (m *GetImPromoteLogicTokenReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetImPromoteLogicTokenResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Sign                 string        `protobuf:"bytes,2,opt,name=sign,proto3" json:"sign,omitempty"`
	TimeOut              int64         `protobuf:"varint,3,opt,name=time_out,json=timeOut,proto3" json:"time_out,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetImPromoteLogicTokenResp) Reset()         { *m = GetImPromoteLogicTokenResp{} }
func (m *GetImPromoteLogicTokenResp) String() string { return proto.CompactTextString(m) }
func (*GetImPromoteLogicTokenResp) ProtoMessage()    {}
func (*GetImPromoteLogicTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{4}
}
func (m *GetImPromoteLogicTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetImPromoteLogicTokenResp.Unmarshal(m, b)
}
func (m *GetImPromoteLogicTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetImPromoteLogicTokenResp.Marshal(b, m, deterministic)
}
func (dst *GetImPromoteLogicTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetImPromoteLogicTokenResp.Merge(dst, src)
}
func (m *GetImPromoteLogicTokenResp) XXX_Size() int {
	return xxx_messageInfo_GetImPromoteLogicTokenResp.Size(m)
}
func (m *GetImPromoteLogicTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetImPromoteLogicTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetImPromoteLogicTokenResp proto.InternalMessageInfo

func (m *GetImPromoteLogicTokenResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetImPromoteLogicTokenResp) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func (m *GetImPromoteLogicTokenResp) GetTimeOut() int64 {
	if m != nil {
		return m.TimeOut
	}
	return 0
}

type GetPlayTogetherTabListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ToAccount            string       `protobuf:"bytes,2,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPlayTogetherTabListReq) Reset()         { *m = GetPlayTogetherTabListReq{} }
func (m *GetPlayTogetherTabListReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayTogetherTabListReq) ProtoMessage()    {}
func (*GetPlayTogetherTabListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{5}
}
func (m *GetPlayTogetherTabListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayTogetherTabListReq.Unmarshal(m, b)
}
func (m *GetPlayTogetherTabListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayTogetherTabListReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayTogetherTabListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayTogetherTabListReq.Merge(dst, src)
}
func (m *GetPlayTogetherTabListReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayTogetherTabListReq.Size(m)
}
func (m *GetPlayTogetherTabListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayTogetherTabListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayTogetherTabListReq proto.InternalMessageInfo

func (m *GetPlayTogetherTabListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPlayTogetherTabListReq) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

type PlayTogetherTab struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TabType              uint32   `protobuf:"varint,2,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	ImageUri             string   `protobuf:"bytes,4,opt,name=image_uri,json=imageUri,proto3" json:"image_uri,omitempty"`
	NameColor            string   `protobuf:"bytes,5,opt,name=name_color,json=nameColor,proto3" json:"name_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlayTogetherTab) Reset()         { *m = PlayTogetherTab{} }
func (m *PlayTogetherTab) String() string { return proto.CompactTextString(m) }
func (*PlayTogetherTab) ProtoMessage()    {}
func (*PlayTogetherTab) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{6}
}
func (m *PlayTogetherTab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayTogetherTab.Unmarshal(m, b)
}
func (m *PlayTogetherTab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayTogetherTab.Marshal(b, m, deterministic)
}
func (dst *PlayTogetherTab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayTogetherTab.Merge(dst, src)
}
func (m *PlayTogetherTab) XXX_Size() int {
	return xxx_messageInfo_PlayTogetherTab.Size(m)
}
func (m *PlayTogetherTab) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayTogetherTab.DiscardUnknown(m)
}

var xxx_messageInfo_PlayTogetherTab proto.InternalMessageInfo

func (m *PlayTogetherTab) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *PlayTogetherTab) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

func (m *PlayTogetherTab) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PlayTogetherTab) GetImageUri() string {
	if m != nil {
		return m.ImageUri
	}
	return ""
}

func (m *PlayTogetherTab) GetNameColor() string {
	if m != nil {
		return m.NameColor
	}
	return ""
}

type GetPlayTogetherTabListResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*PlayTogetherTab `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetPlayTogetherTabListResp) Reset()         { *m = GetPlayTogetherTabListResp{} }
func (m *GetPlayTogetherTabListResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayTogetherTabListResp) ProtoMessage()    {}
func (*GetPlayTogetherTabListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{7}
}
func (m *GetPlayTogetherTabListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayTogetherTabListResp.Unmarshal(m, b)
}
func (m *GetPlayTogetherTabListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayTogetherTabListResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayTogetherTabListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayTogetherTabListResp.Merge(dst, src)
}
func (m *GetPlayTogetherTabListResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayTogetherTabListResp.Size(m)
}
func (m *GetPlayTogetherTabListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayTogetherTabListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayTogetherTabListResp proto.InternalMessageInfo

func (m *GetPlayTogetherTabListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayTogetherTabListResp) GetList() []*PlayTogetherTab {
	if m != nil {
		return m.List
	}
	return nil
}

type GetPlayTogetherMatchConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPlayTogetherMatchConfigReq) Reset()         { *m = GetPlayTogetherMatchConfigReq{} }
func (m *GetPlayTogetherMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayTogetherMatchConfigReq) ProtoMessage()    {}
func (*GetPlayTogetherMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{8}
}
func (m *GetPlayTogetherMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayTogetherMatchConfigReq.Unmarshal(m, b)
}
func (m *GetPlayTogetherMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayTogetherMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayTogetherMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayTogetherMatchConfigReq.Merge(dst, src)
}
func (m *GetPlayTogetherMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayTogetherMatchConfigReq.Size(m)
}
func (m *GetPlayTogetherMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayTogetherMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayTogetherMatchConfigReq proto.InternalMessageInfo

func (m *GetPlayTogetherMatchConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type PlayTogetherMatchConfigItem struct {
	Tab                  *PlayTogetherTab `protobuf:"bytes,1,opt,name=tab,proto3" json:"tab,omitempty"`
	MatchItem            []string         `protobuf:"bytes,2,rep,name=matchItem,proto3" json:"matchItem,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PlayTogetherMatchConfigItem) Reset()         { *m = PlayTogetherMatchConfigItem{} }
func (m *PlayTogetherMatchConfigItem) String() string { return proto.CompactTextString(m) }
func (*PlayTogetherMatchConfigItem) ProtoMessage()    {}
func (*PlayTogetherMatchConfigItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{9}
}
func (m *PlayTogetherMatchConfigItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayTogetherMatchConfigItem.Unmarshal(m, b)
}
func (m *PlayTogetherMatchConfigItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayTogetherMatchConfigItem.Marshal(b, m, deterministic)
}
func (dst *PlayTogetherMatchConfigItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayTogetherMatchConfigItem.Merge(dst, src)
}
func (m *PlayTogetherMatchConfigItem) XXX_Size() int {
	return xxx_messageInfo_PlayTogetherMatchConfigItem.Size(m)
}
func (m *PlayTogetherMatchConfigItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayTogetherMatchConfigItem.DiscardUnknown(m)
}

var xxx_messageInfo_PlayTogetherMatchConfigItem proto.InternalMessageInfo

func (m *PlayTogetherMatchConfigItem) GetTab() *PlayTogetherTab {
	if m != nil {
		return m.Tab
	}
	return nil
}

func (m *PlayTogetherMatchConfigItem) GetMatchItem() []string {
	if m != nil {
		return m.MatchItem
	}
	return nil
}

type GetPlayTogetherMatchConfigResp struct {
	BaseResp             *app.BaseResp                  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ConfigList           []*PlayTogetherMatchConfigItem `protobuf:"bytes,2,rep,name=config_list,json=configList,proto3" json:"config_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetPlayTogetherMatchConfigResp) Reset()         { *m = GetPlayTogetherMatchConfigResp{} }
func (m *GetPlayTogetherMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayTogetherMatchConfigResp) ProtoMessage()    {}
func (*GetPlayTogetherMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{10}
}
func (m *GetPlayTogetherMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayTogetherMatchConfigResp.Unmarshal(m, b)
}
func (m *GetPlayTogetherMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayTogetherMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayTogetherMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayTogetherMatchConfigResp.Merge(dst, src)
}
func (m *GetPlayTogetherMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayTogetherMatchConfigResp.Size(m)
}
func (m *GetPlayTogetherMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayTogetherMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayTogetherMatchConfigResp proto.InternalMessageInfo

func (m *GetPlayTogetherMatchConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayTogetherMatchConfigResp) GetConfigList() []*PlayTogetherMatchConfigItem {
	if m != nil {
		return m.ConfigList
	}
	return nil
}

type CueConfig struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TypeName             string   `protobuf:"bytes,2,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"`
	EntrancePic          string   `protobuf:"bytes,3,opt,name=entrance_pic,json=entrancePic,proto3" json:"entrance_pic,omitempty"`
	GuideTextFrom        string   `protobuf:"bytes,4,opt,name=guide_text_from,json=guideTextFrom,proto3" json:"guide_text_from,omitempty"`
	GuideTextTo          string   `protobuf:"bytes,5,opt,name=guide_text_to,json=guideTextTo,proto3" json:"guide_text_to,omitempty"`
	Lottie               string   `protobuf:"bytes,6,opt,name=lottie,proto3" json:"lottie,omitempty"`
	MsgPicLeft           string   `protobuf:"bytes,7,opt,name=msg_pic_left,json=msgPicLeft,proto3" json:"msg_pic_left,omitempty"`
	MsgPicRight          string   `protobuf:"bytes,8,opt,name=msg_pic_right,json=msgPicRight,proto3" json:"msg_pic_right,omitempty"`
	PreviewMsg           string   `protobuf:"bytes,9,opt,name=preview_msg,json=previewMsg,proto3" json:"preview_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CueConfig) Reset()         { *m = CueConfig{} }
func (m *CueConfig) String() string { return proto.CompactTextString(m) }
func (*CueConfig) ProtoMessage()    {}
func (*CueConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{11}
}
func (m *CueConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CueConfig.Unmarshal(m, b)
}
func (m *CueConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CueConfig.Marshal(b, m, deterministic)
}
func (dst *CueConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CueConfig.Merge(dst, src)
}
func (m *CueConfig) XXX_Size() int {
	return xxx_messageInfo_CueConfig.Size(m)
}
func (m *CueConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CueConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CueConfig proto.InternalMessageInfo

func (m *CueConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CueConfig) GetTypeName() string {
	if m != nil {
		return m.TypeName
	}
	return ""
}

func (m *CueConfig) GetEntrancePic() string {
	if m != nil {
		return m.EntrancePic
	}
	return ""
}

func (m *CueConfig) GetGuideTextFrom() string {
	if m != nil {
		return m.GuideTextFrom
	}
	return ""
}

func (m *CueConfig) GetGuideTextTo() string {
	if m != nil {
		return m.GuideTextTo
	}
	return ""
}

func (m *CueConfig) GetLottie() string {
	if m != nil {
		return m.Lottie
	}
	return ""
}

func (m *CueConfig) GetMsgPicLeft() string {
	if m != nil {
		return m.MsgPicLeft
	}
	return ""
}

func (m *CueConfig) GetMsgPicRight() string {
	if m != nil {
		return m.MsgPicRight
	}
	return ""
}

func (m *CueConfig) GetPreviewMsg() string {
	if m != nil {
		return m.PreviewMsg
	}
	return ""
}

type GetCueConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCueConfigReq) Reset()         { *m = GetCueConfigReq{} }
func (m *GetCueConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetCueConfigReq) ProtoMessage()    {}
func (*GetCueConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{12}
}
func (m *GetCueConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCueConfigReq.Unmarshal(m, b)
}
func (m *GetCueConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCueConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetCueConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCueConfigReq.Merge(dst, src)
}
func (m *GetCueConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetCueConfigReq.Size(m)
}
func (m *GetCueConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCueConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCueConfigReq proto.InternalMessageInfo

func (m *GetCueConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetCueConfigResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Conf                 []*CueConfig  `protobuf:"bytes,2,rep,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCueConfigResp) Reset()         { *m = GetCueConfigResp{} }
func (m *GetCueConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetCueConfigResp) ProtoMessage()    {}
func (*GetCueConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{13}
}
func (m *GetCueConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCueConfigResp.Unmarshal(m, b)
}
func (m *GetCueConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCueConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetCueConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCueConfigResp.Merge(dst, src)
}
func (m *GetCueConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetCueConfigResp.Size(m)
}
func (m *GetCueConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCueConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCueConfigResp proto.InternalMessageInfo

func (m *GetCueConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCueConfigResp) GetConf() []*CueConfig {
	if m != nil {
		return m.Conf
	}
	return nil
}

type PushButtonInfo struct {
	ButtonType           PushButtonType `protobuf:"varint,1,opt,name=button_type,json=buttonType,proto3,enum=ga.impromotelogic.PushButtonType" json:"button_type,omitempty"`
	On                   bool           `protobuf:"varint,2,opt,name=on,proto3" json:"on,omitempty"`
	Desc                 string         `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PushButtonInfo) Reset()         { *m = PushButtonInfo{} }
func (m *PushButtonInfo) String() string { return proto.CompactTextString(m) }
func (*PushButtonInfo) ProtoMessage()    {}
func (*PushButtonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{14}
}
func (m *PushButtonInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushButtonInfo.Unmarshal(m, b)
}
func (m *PushButtonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushButtonInfo.Marshal(b, m, deterministic)
}
func (dst *PushButtonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushButtonInfo.Merge(dst, src)
}
func (m *PushButtonInfo) XXX_Size() int {
	return xxx_messageInfo_PushButtonInfo.Size(m)
}
func (m *PushButtonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PushButtonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PushButtonInfo proto.InternalMessageInfo

func (m *PushButtonInfo) GetButtonType() PushButtonType {
	if m != nil {
		return m.ButtonType
	}
	return PushButtonType_Default
}

func (m *PushButtonInfo) GetOn() bool {
	if m != nil {
		return m.On
	}
	return false
}

func (m *PushButtonInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type PushButtonsReq struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PushButtonInfos      []*PushButtonInfo `protobuf:"bytes,2,rep,name=push_button_infos,json=pushButtonInfos,proto3" json:"push_button_infos,omitempty"`
	PageType             OpenPageType      `protobuf:"varint,3,opt,name=page_type,json=pageType,proto3,enum=ga.impromotelogic.OpenPageType" json:"page_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *PushButtonsReq) Reset()         { *m = PushButtonsReq{} }
func (m *PushButtonsReq) String() string { return proto.CompactTextString(m) }
func (*PushButtonsReq) ProtoMessage()    {}
func (*PushButtonsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{15}
}
func (m *PushButtonsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushButtonsReq.Unmarshal(m, b)
}
func (m *PushButtonsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushButtonsReq.Marshal(b, m, deterministic)
}
func (dst *PushButtonsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushButtonsReq.Merge(dst, src)
}
func (m *PushButtonsReq) XXX_Size() int {
	return xxx_messageInfo_PushButtonsReq.Size(m)
}
func (m *PushButtonsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushButtonsReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushButtonsReq proto.InternalMessageInfo

func (m *PushButtonsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PushButtonsReq) GetPushButtonInfos() []*PushButtonInfo {
	if m != nil {
		return m.PushButtonInfos
	}
	return nil
}

func (m *PushButtonsReq) GetPageType() OpenPageType {
	if m != nil {
		return m.PageType
	}
	return OpenPageType_MsgType
}

type PushButtonsRsp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PushButtonsRsp) Reset()         { *m = PushButtonsRsp{} }
func (m *PushButtonsRsp) String() string { return proto.CompactTextString(m) }
func (*PushButtonsRsp) ProtoMessage()    {}
func (*PushButtonsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{16}
}
func (m *PushButtonsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushButtonsRsp.Unmarshal(m, b)
}
func (m *PushButtonsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushButtonsRsp.Marshal(b, m, deterministic)
}
func (dst *PushButtonsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushButtonsRsp.Merge(dst, src)
}
func (m *PushButtonsRsp) XXX_Size() int {
	return xxx_messageInfo_PushButtonsRsp.Size(m)
}
func (m *PushButtonsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushButtonsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PushButtonsRsp proto.InternalMessageInfo

func (m *PushButtonsRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetPushButtonsReq struct {
	BaseReq              *app.BaseReq     `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PageType             OpenPageType     `protobuf:"varint,2,opt,name=page_type,json=pageType,proto3,enum=ga.impromotelogic.OpenPageType" json:"page_type,omitempty"`
	Buttons              []PushButtonType `protobuf:"varint,3,rep,packed,name=buttons,proto3,enum=ga.impromotelogic.PushButtonType" json:"buttons,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPushButtonsReq) Reset()         { *m = GetPushButtonsReq{} }
func (m *GetPushButtonsReq) String() string { return proto.CompactTextString(m) }
func (*GetPushButtonsReq) ProtoMessage()    {}
func (*GetPushButtonsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{17}
}
func (m *GetPushButtonsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPushButtonsReq.Unmarshal(m, b)
}
func (m *GetPushButtonsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPushButtonsReq.Marshal(b, m, deterministic)
}
func (dst *GetPushButtonsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPushButtonsReq.Merge(dst, src)
}
func (m *GetPushButtonsReq) XXX_Size() int {
	return xxx_messageInfo_GetPushButtonsReq.Size(m)
}
func (m *GetPushButtonsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPushButtonsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPushButtonsReq proto.InternalMessageInfo

func (m *GetPushButtonsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPushButtonsReq) GetPageType() OpenPageType {
	if m != nil {
		return m.PageType
	}
	return OpenPageType_MsgType
}

func (m *GetPushButtonsReq) GetButtons() []PushButtonType {
	if m != nil {
		return m.Buttons
	}
	return nil
}

type GetPushButtonsRsp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PushButtonInfos      []*PushButtonInfo `protobuf:"bytes,2,rep,name=push_button_infos,json=pushButtonInfos,proto3" json:"push_button_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetPushButtonsRsp) Reset()         { *m = GetPushButtonsRsp{} }
func (m *GetPushButtonsRsp) String() string { return proto.CompactTextString(m) }
func (*GetPushButtonsRsp) ProtoMessage()    {}
func (*GetPushButtonsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_im_promote_logic__562a21e56b3b572b, []int{18}
}
func (m *GetPushButtonsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPushButtonsRsp.Unmarshal(m, b)
}
func (m *GetPushButtonsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPushButtonsRsp.Marshal(b, m, deterministic)
}
func (dst *GetPushButtonsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPushButtonsRsp.Merge(dst, src)
}
func (m *GetPushButtonsRsp) XXX_Size() int {
	return xxx_messageInfo_GetPushButtonsRsp.Size(m)
}
func (m *GetPushButtonsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPushButtonsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPushButtonsRsp proto.InternalMessageInfo

func (m *GetPushButtonsRsp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPushButtonsRsp) GetPushButtonInfos() []*PushButtonInfo {
	if m != nil {
		return m.PushButtonInfos
	}
	return nil
}

func init() {
	proto.RegisterType((*ImPlayTogetherMsg)(nil), "ga.impromotelogic.ImPlayTogetherMsg")
	proto.RegisterType((*ImPromoteLogicReq)(nil), "ga.impromotelogic.ImPromoteLogicReq")
	proto.RegisterType((*ImPromoteLogicResp)(nil), "ga.impromotelogic.ImPromoteLogicResp")
	proto.RegisterType((*GetImPromoteLogicTokenReq)(nil), "ga.impromotelogic.GetImPromoteLogicTokenReq")
	proto.RegisterType((*GetImPromoteLogicTokenResp)(nil), "ga.impromotelogic.GetImPromoteLogicTokenResp")
	proto.RegisterType((*GetPlayTogetherTabListReq)(nil), "ga.impromotelogic.GetPlayTogetherTabListReq")
	proto.RegisterType((*PlayTogetherTab)(nil), "ga.impromotelogic.PlayTogetherTab")
	proto.RegisterType((*GetPlayTogetherTabListResp)(nil), "ga.impromotelogic.GetPlayTogetherTabListResp")
	proto.RegisterType((*GetPlayTogetherMatchConfigReq)(nil), "ga.impromotelogic.GetPlayTogetherMatchConfigReq")
	proto.RegisterType((*PlayTogetherMatchConfigItem)(nil), "ga.impromotelogic.PlayTogetherMatchConfigItem")
	proto.RegisterType((*GetPlayTogetherMatchConfigResp)(nil), "ga.impromotelogic.GetPlayTogetherMatchConfigResp")
	proto.RegisterType((*CueConfig)(nil), "ga.impromotelogic.CueConfig")
	proto.RegisterType((*GetCueConfigReq)(nil), "ga.impromotelogic.GetCueConfigReq")
	proto.RegisterType((*GetCueConfigResp)(nil), "ga.impromotelogic.GetCueConfigResp")
	proto.RegisterType((*PushButtonInfo)(nil), "ga.impromotelogic.PushButtonInfo")
	proto.RegisterType((*PushButtonsReq)(nil), "ga.impromotelogic.PushButtonsReq")
	proto.RegisterType((*PushButtonsRsp)(nil), "ga.impromotelogic.PushButtonsRsp")
	proto.RegisterType((*GetPushButtonsReq)(nil), "ga.impromotelogic.GetPushButtonsReq")
	proto.RegisterType((*GetPushButtonsRsp)(nil), "ga.impromotelogic.GetPushButtonsRsp")
	proto.RegisterEnum("ga.impromotelogic.PushButtonType", PushButtonType_name, PushButtonType_value)
	proto.RegisterEnum("ga.impromotelogic.OpenPageType", OpenPageType_name, OpenPageType_value)
}

func init() {
	proto.RegisterFile("im-promote-logic_.proto", fileDescriptor_im_promote_logic__562a21e56b3b572b)
}

var fileDescriptor_im_promote_logic__562a21e56b3b572b = []byte{
	// 1136 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0x4b, 0x6f, 0xdb, 0x46,
	0x10, 0x0e, 0x25, 0xd9, 0x12, 0x47, 0x4f, 0x6f, 0xd1, 0x56, 0x79, 0xb8, 0x51, 0x08, 0x34, 0x70,
	0xd2, 0x46, 0x2e, 0xdc, 0x07, 0x50, 0xb8, 0x40, 0x61, 0x2b, 0x89, 0x61, 0xc0, 0x2f, 0x30, 0xf2,
	0xa5, 0x17, 0x62, 0x49, 0xae, 0x56, 0x8b, 0x72, 0xb9, 0x34, 0xb9, 0x74, 0xac, 0x53, 0xcf, 0x2d,
	0xd0, 0x4b, 0x7b, 0xed, 0xb9, 0x3f, 0xa1, 0xff, 0xa0, 0xff, 0xab, 0xd8, 0x5d, 0x4a, 0xb6, 0x14,
	0x27, 0x0d, 0x8b, 0xde, 0x66, 0x67, 0x67, 0x66, 0xbf, 0xf9, 0xe6, 0xd3, 0x50, 0xf0, 0x31, 0xe3,
	0xcf, 0x92, 0x54, 0x70, 0x21, 0xc9, 0xb3, 0x48, 0x50, 0x16, 0x78, 0xc3, 0x24, 0x15, 0x52, 0xa0,
	0x0d, 0x8a, 0x87, 0x8c, 0x17, 0x57, 0xfa, 0xe6, 0x5e, 0x9b, 0x62, 0xcf, 0xc7, 0x19, 0x31, 0x11,
	0xce, 0x6f, 0x15, 0xd8, 0x38, 0xe4, 0x67, 0x11, 0x9e, 0x8d, 0x05, 0x25, 0x72, 0x4a, 0xd2, 0xe3,
	0x8c, 0xa2, 0x4d, 0x80, 0x60, 0x8a, 0xe3, 0x98, 0x44, 0x1e, 0x0b, 0xfb, 0xd6, 0xc0, 0xda, 0x6a,
	0xbb, 0x76, 0xe1, 0x39, 0x0c, 0xd1, 0x43, 0x68, 0x06, 0x2c, 0x99, 0x92, 0xd4, 0x93, 0xe4, 0x4a,
	0xf6, 0x2b, 0x03, 0x6b, 0xcb, 0x76, 0xc1, 0xb8, 0xc6, 0xe4, 0x4a, 0xa2, 0xfb, 0x60, 0xa7, 0x42,
	0x70, 0x4f, 0xce, 0x12, 0xd2, 0xaf, 0x0e, 0xac, 0xad, 0x35, 0xb7, 0xa1, 0x1c, 0xe3, 0x59, 0x42,
	0xd0, 0x13, 0xd8, 0x90, 0x8c, 0x13, 0x4f, 0xe4, 0xd2, 0x23, 0x71, 0xe8, 0xa9, 0x43, 0xbf, 0x36,
	0xb0, 0xb6, 0xaa, 0x6e, 0x47, 0xd9, 0xa7, 0xb9, 0x7c, 0x11, 0x87, 0x63, 0xc6, 0x89, 0xc2, 0x31,
	0x11, 0x51, 0x24, 0x5e, 0x7b, 0x39, 0x0b, 0xfb, 0x6b, 0x06, 0x87, 0xf1, 0x9c, 0xb3, 0x10, 0xdd,
	0x85, 0x86, 0xc4, 0xbe, 0x17, 0x63, 0x4e, 0xfa, 0xeb, 0x1a, 0x44, 0x5d, 0x62, 0xff, 0x04, 0x73,
	0x82, 0x06, 0xd0, 0x52, 0x57, 0x91, 0xa0, 0xc2, 0xcb, 0xd3, 0xa8, 0x5f, 0x37, 0x18, 0x25, 0xf6,
	0x8f, 0x04, 0x15, 0xe7, 0x69, 0xa4, 0x92, 0x7d, 0xea, 0x05, 0x22, 0x12, 0x69, 0xbf, 0x61, 0x92,
	0x7d, 0x3a, 0x52, 0x47, 0x67, 0x57, 0x73, 0x62, 0x58, 0x3b, 0x52, 0xac, 0xb9, 0xe4, 0x02, 0x3d,
	0x86, 0x86, 0xe2, 0xcd, 0x4b, 0xc9, 0x85, 0x66, 0xa4, 0xb9, 0xd3, 0x1c, 0x52, 0x3c, 0xdc, 0xc7,
	0x19, 0x71, 0xc9, 0x85, 0x5b, 0xf7, 0x8d, 0xe1, 0x7c, 0x0f, 0x68, 0x35, 0x39, 0x4b, 0xd0, 0x13,
	0xb0, 0x8b, 0xec, 0x2c, 0x29, 0xd2, 0x5b, 0xd7, 0xe9, 0x59, 0xe2, 0x36, 0xfc, 0xc2, 0x72, 0x7e,
	0xb6, 0xe0, 0xee, 0x01, 0x91, 0xcb, 0x45, 0xc6, 0xe2, 0x47, 0x12, 0x97, 0x80, 0x81, 0x3e, 0x85,
	0x4e, 0x41, 0x1d, 0x0e, 0x02, 0x91, 0xc7, 0xf3, 0x31, 0xb5, 0x8d, 0x77, 0xcf, 0x38, 0x57, 0x26,
	0x5d, 0x5d, 0x99, 0xb4, 0x73, 0x09, 0xf7, 0xde, 0x06, 0xa5, 0x54, 0x53, 0x08, 0x41, 0x2d, 0x63,
	0x34, 0x2e, 0x40, 0x68, 0x5b, 0x8f, 0xaf, 0x10, 0x82, 0x7e, 0xb9, 0xea, 0xd6, 0x8b, 0xf9, 0x3b,
	0xbe, 0xa6, 0xe0, 0xa6, 0x2c, 0xc7, 0xd8, 0x3f, 0x62, 0x99, 0x2c, 0x43, 0xc1, 0x26, 0x80, 0x14,
	0x2b, 0xed, 0xdb, 0x52, 0x14, 0xad, 0x3b, 0xbf, 0x5b, 0xd0, 0x5d, 0x79, 0x01, 0x7d, 0x08, 0xeb,
	0x12, 0xd3, 0x6b, 0xd1, 0xaf, 0x49, 0x4c, 0x0f, 0x17, 0x42, 0xd3, 0x72, 0xae, 0xe8, 0x0b, 0x25,
	0x34, 0xad, 0x66, 0x04, 0x35, 0xad, 0xbf, 0xaa, 0x69, 0x4c, 0xd9, 0x4a, 0xfe, 0x8c, 0x63, 0x4a,
	0xbc, 0x3c, 0x65, 0x5a, 0xd9, 0xb6, 0xdb, 0xd0, 0x8e, 0xf3, 0x94, 0x29, 0x54, 0x2a, 0xa8, 0x50,
	0xde, 0x9a, 0x41, 0xa5, 0x3c, 0x46, 0x7b, 0x3f, 0x69, 0xc6, 0x6f, 0xed, 0xbc, 0x1c, 0xe3, 0xdf,
	0x40, 0x2d, 0x62, 0x99, 0xea, 0xbb, 0xba, 0xd5, 0xdc, 0x71, 0x86, 0x6f, 0xac, 0x82, 0xe1, 0xca,
	0x23, 0xae, 0x8e, 0x77, 0x0e, 0x60, 0x73, 0x05, 0xc0, 0x31, 0x96, 0xc1, 0x74, 0x24, 0xe2, 0x09,
	0xa3, 0x65, 0x7e, 0x08, 0x17, 0x70, 0xff, 0x2d, 0x55, 0x0e, 0x25, 0xe1, 0xe8, 0x2b, 0xa8, 0x4a,
	0xec, 0x17, 0x15, 0xde, 0x07, 0x9e, 0x0a, 0x47, 0x0f, 0xc0, 0xe6, 0xaa, 0x90, 0x2a, 0xa1, 0x5b,
	0xb3, 0xdd, 0x6b, 0x87, 0xf3, 0x87, 0x05, 0x9f, 0xbc, 0x0b, 0x7c, 0x39, 0x06, 0x4f, 0xa1, 0x19,
	0xe8, 0x44, 0xef, 0x06, 0x91, 0xc3, 0x7f, 0x41, 0xba, 0xd2, 0xa6, 0x0b, 0xa6, 0x84, 0x9a, 0xa0,
	0xf3, 0x67, 0x05, 0xec, 0x51, 0x4e, 0xcc, 0x2d, 0xea, 0x40, 0x65, 0xa1, 0xb3, 0x0a, 0x0b, 0x95,
	0x6a, 0x94, 0xc0, 0xcc, 0x3a, 0x33, 0x6a, 0x6d, 0x28, 0x87, 0xde, 0x67, 0x8f, 0xa0, 0x45, 0x62,
	0x99, 0xe2, 0x38, 0x20, 0x5e, 0xc2, 0x82, 0x42, 0x6e, 0xcd, 0xb9, 0xef, 0x8c, 0x05, 0xe8, 0x31,
	0x74, 0x69, 0xce, 0x42, 0xa2, 0x97, 0xb2, 0x37, 0x49, 0x05, 0x2f, 0xb4, 0xd7, 0xd6, 0x6e, 0xb5,
	0x98, 0x5f, 0xa6, 0x82, 0x23, 0x07, 0xda, 0x37, 0xe2, 0xa4, 0x28, 0x34, 0xd8, 0x5c, 0x44, 0x8d,
	0x05, 0xfa, 0x08, 0xd6, 0x23, 0x21, 0x25, 0x9b, 0xef, 0xd5, 0xe2, 0xa4, 0xd6, 0x2a, 0xcf, 0xa8,
	0x42, 0xe0, 0x45, 0x64, 0x22, 0xe7, 0x6b, 0x95, 0x67, 0xf4, 0x8c, 0x05, 0x47, 0x64, 0x22, 0x55,
	0xf5, 0x79, 0x44, 0xca, 0xe8, 0x54, 0x16, 0xbb, 0xb5, 0x69, 0x42, 0x5c, 0xe5, 0x52, 0xdf, 0x8f,
	0x24, 0x25, 0x97, 0x8c, 0xbc, 0xf6, 0x78, 0x46, 0xfb, 0xb6, 0x29, 0x52, 0xb8, 0x8e, 0x33, 0xea,
	0x7c, 0x0b, 0xdd, 0x03, 0x22, 0x17, 0x54, 0x95, 0x51, 0x9d, 0x80, 0xde, 0x72, 0x6a, 0xb9, 0x99,
	0x7f, 0x01, 0x35, 0x35, 0xb0, 0x62, 0xd8, 0x0f, 0x6e, 0x19, 0xf6, 0x75, 0x69, 0x1d, 0xe9, 0x5c,
	0x41, 0xe7, 0x2c, 0xcf, 0xa6, 0xfb, 0xb9, 0x94, 0x22, 0x3e, 0x8c, 0x27, 0x02, 0xed, 0x43, 0xd3,
	0xd7, 0x27, 0xb3, 0x30, 0xd4, 0x83, 0x9d, 0x9d, 0x47, 0xb7, 0xe9, 0x66, 0x91, 0xa7, 0x56, 0x89,
	0x0b, 0xfe, 0xc2, 0x56, 0xe2, 0x10, 0x66, 0x5b, 0x36, 0xdc, 0x8a, 0x88, 0xd5, 0x9a, 0x09, 0x49,
	0x36, 0x9f, 0xbb, 0xb6, 0x9d, 0xbf, 0xad, 0x9b, 0x4f, 0x67, 0x65, 0x56, 0xe3, 0x31, 0x6c, 0x24,
	0x79, 0x36, 0xf5, 0x0a, 0x9c, 0x2c, 0x9e, 0x88, 0xac, 0xe8, 0xf9, 0xdd, 0x40, 0x55, 0x83, 0x6e,
	0x37, 0x59, 0x3a, 0x67, 0xe8, 0x3b, 0xb0, 0x13, 0xb5, 0xef, 0x16, 0xdf, 0xfb, 0xce, 0xce, 0xc3,
	0x5b, 0xca, 0x9c, 0x26, 0x24, 0x3e, 0xc3, 0x94, 0xe8, 0x6e, 0x1b, 0x49, 0x61, 0x39, 0xbb, 0xcb,
	0x6d, 0x94, 0xfb, 0x5a, 0xfe, 0x65, 0xc1, 0x86, 0xfa, 0xc9, 0xff, 0x37, 0x1e, 0x96, 0x80, 0x57,
	0x4a, 0x02, 0x47, 0xbb, 0x50, 0x37, 0x04, 0x66, 0xfd, 0xea, 0xa0, 0xfa, 0x7e, 0x43, 0x9e, 0x67,
	0x38, 0xbf, 0xbe, 0x09, 0xbc, 0x9c, 0x54, 0xff, 0xdf, 0x19, 0x3e, 0xfd, 0x65, 0x49, 0x4d, 0xba,
	0xbf, 0x26, 0xd4, 0x9f, 0x93, 0x09, 0xce, 0x23, 0xd9, 0xbb, 0x83, 0x7a, 0xd0, 0x1a, 0x09, 0xce,
	0x49, 0x2c, 0xf7, 0xe2, 0x70, 0x4f, 0xf6, 0x2c, 0xd4, 0x80, 0xda, 0xcb, 0xbd, 0x93, 0x57, 0xbd,
	0x8a, 0x0a, 0x1c, 0xe1, 0x94, 0x4c, 0xf2, 0xa8, 0x57, 0x45, 0x6d, 0xb0, 0x8f, 0xd8, 0x25, 0x79,
	0x25, 0x71, 0x2a, 0x7b, 0x35, 0xd4, 0x82, 0xc6, 0x9e, 0x94, 0x4c, 0xe6, 0x21, 0xe9, 0xad, 0xa9,
	0x2a, 0xcf, 0xd9, 0xcc, 0x25, 0x81, 0x2e, 0x15, 0xf6, 0xd6, 0xd1, 0x07, 0xd0, 0x7d, 0x11, 0x4b,
	0x92, 0xba, 0x42, 0xf0, 0x13, 0x21, 0xd9, 0x64, 0xd6, 0xab, 0x3f, 0xfd, 0x1c, 0x5a, 0x37, 0x39,
	0x57, 0x0f, 0x1c, 0x67, 0x54, 0x99, 0xbd, 0x3b, 0xa8, 0x0b, 0xcd, 0x91, 0x88, 0x03, 0x82, 0x23,
	0xed, 0xb0, 0xf6, 0x0f, 0xa0, 0x1f, 0x08, 0x3e, 0x9c, 0xb1, 0x99, 0xc8, 0x55, 0xe7, 0x5c, 0x84,
	0x24, 0x32, 0x7f, 0x70, 0x7f, 0xf8, 0x8c, 0x8a, 0x08, 0xc7, 0x74, 0xf8, 0xf5, 0x8e, 0x94, 0xc3,
	0x40, 0xf0, 0x6d, 0xed, 0x0e, 0x44, 0xb4, 0x8d, 0x93, 0x64, 0x7b, 0x99, 0x23, 0x7f, 0x5d, 0x5f,
	0x7e, 0xf9, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x32, 0x99, 0x2e, 0x56, 0x51, 0x0b, 0x00, 0x00,
}
