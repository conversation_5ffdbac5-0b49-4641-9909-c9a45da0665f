// Code generated by protoc-gen-go. DO NOT EDIT.
// source: smash-egg-logic_.proto

package smash_egg // import "golang.52tt.com/protocol/app/smash-egg"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 砸蛋方式
type SmashEgg_Source int32

const (
	SmashEgg_Source_Manual SmashEgg_Source = 0
	SmashEgg_Source_AUTO   SmashEgg_Source = 1
)

var SmashEgg_Source_name = map[int32]string{
	0: "Manual",
	1: "AUTO",
}
var SmashEgg_Source_value = map[string]int32{
	"Manual": 0,
	"AUTO":   1,
}

func (x SmashEgg_Source) String() string {
	return proto.EnumName(SmashEgg_Source_name, int32(x))
}
func (SmashEgg_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{0}
}

// 砸蛋状态
type SmashEgg_Flag int32

const (
	SmashEgg_Flag_NORMAL SmashEgg_Flag = 0
	SmashEgg_Flag_MORPH  SmashEgg_Flag = 1
)

var SmashEgg_Flag_name = map[int32]string{
	0: "NORMAL",
	1: "MORPH",
}
var SmashEgg_Flag_value = map[string]int32{
	"NORMAL": 0,
	"MORPH":  1,
}

func (x SmashEgg_Flag) String() string {
	return proto.EnumName(SmashEgg_Flag_name, int32(x))
}
func (SmashEgg_Flag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{1}
}

// 砸蛋方式
type SmashEgg_Mode int32

const (
	SmashEgg_Mode_NORMAL_MODE SmashEgg_Mode = 0
	SmashEgg_Mode_GOLD_MODE   SmashEgg_Mode = 1
)

var SmashEgg_Mode_name = map[int32]string{
	0: "NORMAL_MODE",
	1: "GOLD_MODE",
}
var SmashEgg_Mode_value = map[string]int32{
	"NORMAL_MODE": 0,
	"GOLD_MODE":   1,
}

func (x SmashEgg_Mode) String() string {
	return proto.EnumName(SmashEgg_Mode_name, int32(x))
}
func (SmashEgg_Mode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{2}
}

// 平台
type SmashEgg_Platform int32

const (
	SmashEgg_Platform_Unknown_Platform SmashEgg_Platform = 0
	SmashEgg_Platform_Android          SmashEgg_Platform = 1
	SmashEgg_Platform_iOS              SmashEgg_Platform = 2
)

var SmashEgg_Platform_name = map[int32]string{
	0: "Unknown_Platform",
	1: "Android",
	2: "iOS",
}
var SmashEgg_Platform_value = map[string]int32{
	"Unknown_Platform": 0,
	"Android":          1,
	"iOS":              2,
}

func (x SmashEgg_Platform) String() string {
	return proto.EnumName(SmashEgg_Platform_name, int32(x))
}
func (SmashEgg_Platform) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{3}
}

// 应用
type SmashEgg_App int32

const (
	SmashEgg_App_Unknown_App SmashEgg_App = 0
	SmashEgg_App_TT          SmashEgg_App = 1
	SmashEgg_App_HUANYOU     SmashEgg_App = 2
	SmashEgg_App_ZAIYA       SmashEgg_App = 3
)

var SmashEgg_App_name = map[int32]string{
	0: "Unknown_App",
	1: "TT",
	2: "HUANYOU",
	3: "ZAIYA",
}
var SmashEgg_App_value = map[string]int32{
	"Unknown_App": 0,
	"TT":          1,
	"HUANYOU":     2,
	"ZAIYA":       3,
}

func (x SmashEgg_App) String() string {
	return proto.EnumName(SmashEgg_App_name, int32(x))
}
func (SmashEgg_App) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{4}
}

type CurActivityType int32

const (
	CurActivityType_CUR_ACTIVITY_TYPE_UNSPECIFIED CurActivityType = 0
	CurActivityType_CUR_ACTIVITY_TYPE_A           CurActivityType = 1
	CurActivityType_CUR_ACTIVITY_TYPE_B           CurActivityType = 2
)

var CurActivityType_name = map[int32]string{
	0: "CUR_ACTIVITY_TYPE_UNSPECIFIED",
	1: "CUR_ACTIVITY_TYPE_A",
	2: "CUR_ACTIVITY_TYPE_B",
}
var CurActivityType_value = map[string]int32{
	"CUR_ACTIVITY_TYPE_UNSPECIFIED": 0,
	"CUR_ACTIVITY_TYPE_A":           1,
	"CUR_ACTIVITY_TYPE_B":           2,
}

func (x CurActivityType) String() string {
	return proto.EnumName(CurActivityType_name, int32(x))
}
func (CurActivityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{5}
}

type SmashAwardInfo_GiftFlag int32

const (
	SmashAwardInfo_NORMAL SmashAwardInfo_GiftFlag = 0
	SmashAwardInfo_SCARCE SmashAwardInfo_GiftFlag = 1
	SmashAwardInfo_BEST   SmashAwardInfo_GiftFlag = 2
)

var SmashAwardInfo_GiftFlag_name = map[int32]string{
	0: "NORMAL",
	1: "SCARCE",
	2: "BEST",
}
var SmashAwardInfo_GiftFlag_value = map[string]int32{
	"NORMAL": 0,
	"SCARCE": 1,
	"BEST":   2,
}

func (x SmashAwardInfo_GiftFlag) String() string {
	return proto.EnumName(SmashAwardInfo_GiftFlag_name, int32(x))
}
func (SmashAwardInfo_GiftFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{2, 0}
}

// 消费记录
type SmashEgg_ConsumeRecord struct {
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Amount               uint32   `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Fee                  uint32   `protobuf:"varint,4,opt,name=fee,proto3" json:"fee,omitempty"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SmashEgg_ConsumeRecord) Reset()         { *m = SmashEgg_ConsumeRecord{} }
func (m *SmashEgg_ConsumeRecord) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_ConsumeRecord) ProtoMessage()    {}
func (*SmashEgg_ConsumeRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{0}
}
func (m *SmashEgg_ConsumeRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_ConsumeRecord.Unmarshal(m, b)
}
func (m *SmashEgg_ConsumeRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_ConsumeRecord.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_ConsumeRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_ConsumeRecord.Merge(dst, src)
}
func (m *SmashEgg_ConsumeRecord) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_ConsumeRecord.Size(m)
}
func (m *SmashEgg_ConsumeRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_ConsumeRecord.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_ConsumeRecord proto.InternalMessageInfo

func (m *SmashEgg_ConsumeRecord) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SmashEgg_ConsumeRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SmashEgg_ConsumeRecord) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *SmashEgg_ConsumeRecord) GetFee() uint32 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *SmashEgg_ConsumeRecord) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *SmashEgg_ConsumeRecord) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

// 中奖记录
type SmashEgg_WinningRecord struct {
	Id                   uint64          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32          `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string          `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Source               SmashEgg_Source `protobuf:"varint,4,opt,name=source,proto3,enum=ga.smash_egg.SmashEgg_Source" json:"source,omitempty"`
	Flag                 SmashEgg_Flag   `protobuf:"varint,5,opt,name=flag,proto3,enum=ga.smash_egg.SmashEgg_Flag" json:"flag,omitempty"`
	PackId               uint32          `protobuf:"varint,6,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackName             string          `protobuf:"bytes,7,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string          `protobuf:"bytes,8,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackAmount           uint32          `protobuf:"varint,9,opt,name=pack_amount,json=packAmount,proto3" json:"pack_amount,omitempty"`
	CreateTime           uint32          `protobuf:"varint,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	PackDesc             string          `protobuf:"bytes,11,opt,name=pack_desc,json=packDesc,proto3" json:"pack_desc,omitempty"`
	NotifyPrefix         uint32          `protobuf:"varint,12,opt,name=notify_prefix,json=notifyPrefix,proto3" json:"notify_prefix,omitempty"`
	NotifyXxx            string          `protobuf:"bytes,13,opt,name=notify_xxx,json=notifyXxx,proto3" json:"notify_xxx,omitempty"`
	NotifyXxxFormat      string          `protobuf:"bytes,14,opt,name=notify_xxx_format,json=notifyXxxFormat,proto3" json:"notify_xxx_format,omitempty"`
	Mode                 SmashEgg_Mode   `protobuf:"varint,15,opt,name=mode,proto3,enum=ga.smash_egg.SmashEgg_Mode" json:"mode,omitempty"`
	NotifyResource       string          `protobuf:"bytes,16,opt,name=notify_resource,json=notifyResource,proto3" json:"notify_resource,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SmashEgg_WinningRecord) Reset()         { *m = SmashEgg_WinningRecord{} }
func (m *SmashEgg_WinningRecord) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_WinningRecord) ProtoMessage()    {}
func (*SmashEgg_WinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{1}
}
func (m *SmashEgg_WinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_WinningRecord.Unmarshal(m, b)
}
func (m *SmashEgg_WinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_WinningRecord.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_WinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_WinningRecord.Merge(dst, src)
}
func (m *SmashEgg_WinningRecord) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_WinningRecord.Size(m)
}
func (m *SmashEgg_WinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_WinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_WinningRecord proto.InternalMessageInfo

func (m *SmashEgg_WinningRecord) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SmashEgg_WinningRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SmashEgg_WinningRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *SmashEgg_WinningRecord) GetSource() SmashEgg_Source {
	if m != nil {
		return m.Source
	}
	return SmashEgg_Source_Manual
}

func (m *SmashEgg_WinningRecord) GetFlag() SmashEgg_Flag {
	if m != nil {
		return m.Flag
	}
	return SmashEgg_Flag_NORMAL
}

func (m *SmashEgg_WinningRecord) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *SmashEgg_WinningRecord) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *SmashEgg_WinningRecord) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *SmashEgg_WinningRecord) GetPackAmount() uint32 {
	if m != nil {
		return m.PackAmount
	}
	return 0
}

func (m *SmashEgg_WinningRecord) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *SmashEgg_WinningRecord) GetPackDesc() string {
	if m != nil {
		return m.PackDesc
	}
	return ""
}

func (m *SmashEgg_WinningRecord) GetNotifyPrefix() uint32 {
	if m != nil {
		return m.NotifyPrefix
	}
	return 0
}

func (m *SmashEgg_WinningRecord) GetNotifyXxx() string {
	if m != nil {
		return m.NotifyXxx
	}
	return ""
}

func (m *SmashEgg_WinningRecord) GetNotifyXxxFormat() string {
	if m != nil {
		return m.NotifyXxxFormat
	}
	return ""
}

func (m *SmashEgg_WinningRecord) GetMode() SmashEgg_Mode {
	if m != nil {
		return m.Mode
	}
	return SmashEgg_Mode_NORMAL_MODE
}

func (m *SmashEgg_WinningRecord) GetNotifyResource() string {
	if m != nil {
		return m.NotifyResource
	}
	return ""
}

type SmashAwardInfo struct {
	Id                   uint32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Pic                  string                  `protobuf:"bytes,3,opt,name=pic,proto3" json:"pic,omitempty"`
	Amount               uint32                  `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	GiftFlag             SmashAwardInfo_GiftFlag `protobuf:"varint,5,opt,name=gift_flag,json=giftFlag,proto3,enum=ga.smash_egg.SmashAwardInfo_GiftFlag" json:"gift_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SmashAwardInfo) Reset()         { *m = SmashAwardInfo{} }
func (m *SmashAwardInfo) String() string { return proto.CompactTextString(m) }
func (*SmashAwardInfo) ProtoMessage()    {}
func (*SmashAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{2}
}
func (m *SmashAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashAwardInfo.Unmarshal(m, b)
}
func (m *SmashAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashAwardInfo.Marshal(b, m, deterministic)
}
func (dst *SmashAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashAwardInfo.Merge(dst, src)
}
func (m *SmashAwardInfo) XXX_Size() int {
	return xxx_messageInfo_SmashAwardInfo.Size(m)
}
func (m *SmashAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SmashAwardInfo proto.InternalMessageInfo

func (m *SmashAwardInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SmashAwardInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SmashAwardInfo) GetPic() string {
	if m != nil {
		return m.Pic
	}
	return ""
}

func (m *SmashAwardInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *SmashAwardInfo) GetGiftFlag() SmashAwardInfo_GiftFlag {
	if m != nil {
		return m.GiftFlag
	}
	return SmashAwardInfo_NORMAL
}

type SmashEgg_WinningRecordV2 struct {
	Id                   uint64            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Source               SmashEgg_Source   `protobuf:"varint,3,opt,name=source,proto3,enum=ga.smash_egg.SmashEgg_Source" json:"source,omitempty"`
	Flag                 SmashEgg_Flag     `protobuf:"varint,4,opt,name=flag,proto3,enum=ga.smash_egg.SmashEgg_Flag" json:"flag,omitempty"`
	Mode                 SmashEgg_Mode     `protobuf:"varint,5,opt,name=mode,proto3,enum=ga.smash_egg.SmashEgg_Mode" json:"mode,omitempty"`
	PackId               uint32            `protobuf:"varint,6,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	CreateTime           uint32            `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	AwardList            []*SmashAwardInfo `protobuf:"bytes,8,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	NotifyPrefix         uint32            `protobuf:"varint,9,opt,name=notify_prefix,json=notifyPrefix,proto3" json:"notify_prefix,omitempty"`
	NotifyXxx            string            `protobuf:"bytes,10,opt,name=notify_xxx,json=notifyXxx,proto3" json:"notify_xxx,omitempty"`
	NotifyXxxFormat      string            `protobuf:"bytes,11,opt,name=notify_xxx_format,json=notifyXxxFormat,proto3" json:"notify_xxx_format,omitempty"`
	NotifyResource       string            `protobuf:"bytes,12,opt,name=notify_resource,json=notifyResource,proto3" json:"notify_resource,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SmashEgg_WinningRecordV2) Reset()         { *m = SmashEgg_WinningRecordV2{} }
func (m *SmashEgg_WinningRecordV2) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_WinningRecordV2) ProtoMessage()    {}
func (*SmashEgg_WinningRecordV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{3}
}
func (m *SmashEgg_WinningRecordV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_WinningRecordV2.Unmarshal(m, b)
}
func (m *SmashEgg_WinningRecordV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_WinningRecordV2.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_WinningRecordV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_WinningRecordV2.Merge(dst, src)
}
func (m *SmashEgg_WinningRecordV2) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_WinningRecordV2.Size(m)
}
func (m *SmashEgg_WinningRecordV2) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_WinningRecordV2.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_WinningRecordV2 proto.InternalMessageInfo

func (m *SmashEgg_WinningRecordV2) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SmashEgg_WinningRecordV2) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SmashEgg_WinningRecordV2) GetSource() SmashEgg_Source {
	if m != nil {
		return m.Source
	}
	return SmashEgg_Source_Manual
}

func (m *SmashEgg_WinningRecordV2) GetFlag() SmashEgg_Flag {
	if m != nil {
		return m.Flag
	}
	return SmashEgg_Flag_NORMAL
}

func (m *SmashEgg_WinningRecordV2) GetMode() SmashEgg_Mode {
	if m != nil {
		return m.Mode
	}
	return SmashEgg_Mode_NORMAL_MODE
}

func (m *SmashEgg_WinningRecordV2) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *SmashEgg_WinningRecordV2) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *SmashEgg_WinningRecordV2) GetAwardList() []*SmashAwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *SmashEgg_WinningRecordV2) GetNotifyPrefix() uint32 {
	if m != nil {
		return m.NotifyPrefix
	}
	return 0
}

func (m *SmashEgg_WinningRecordV2) GetNotifyXxx() string {
	if m != nil {
		return m.NotifyXxx
	}
	return ""
}

func (m *SmashEgg_WinningRecordV2) GetNotifyXxxFormat() string {
	if m != nil {
		return m.NotifyXxxFormat
	}
	return ""
}

func (m *SmashEgg_WinningRecordV2) GetNotifyResource() string {
	if m != nil {
		return m.NotifyResource
	}
	return ""
}

type SmashEgg_GetConsumeRecordReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Offset               uint64       `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            uint32       `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32       `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SmashEgg_GetConsumeRecordReq) Reset()         { *m = SmashEgg_GetConsumeRecordReq{} }
func (m *SmashEgg_GetConsumeRecordReq) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetConsumeRecordReq) ProtoMessage()    {}
func (*SmashEgg_GetConsumeRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{4}
}
func (m *SmashEgg_GetConsumeRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetConsumeRecordReq.Unmarshal(m, b)
}
func (m *SmashEgg_GetConsumeRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetConsumeRecordReq.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetConsumeRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetConsumeRecordReq.Merge(dst, src)
}
func (m *SmashEgg_GetConsumeRecordReq) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetConsumeRecordReq.Size(m)
}
func (m *SmashEgg_GetConsumeRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetConsumeRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetConsumeRecordReq proto.InternalMessageInfo

func (m *SmashEgg_GetConsumeRecordReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SmashEgg_GetConsumeRecordReq) GetOffset() uint64 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SmashEgg_GetConsumeRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SmashEgg_GetConsumeRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SmashEgg_GetConsumeRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type SmashEgg_GetConsumeRecordResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ConsumeRecordList    []*SmashEgg_ConsumeRecord `protobuf:"bytes,2,rep,name=consume_record_list,json=consumeRecordList,proto3" json:"consume_record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SmashEgg_GetConsumeRecordResp) Reset()         { *m = SmashEgg_GetConsumeRecordResp{} }
func (m *SmashEgg_GetConsumeRecordResp) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetConsumeRecordResp) ProtoMessage()    {}
func (*SmashEgg_GetConsumeRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{5}
}
func (m *SmashEgg_GetConsumeRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetConsumeRecordResp.Unmarshal(m, b)
}
func (m *SmashEgg_GetConsumeRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetConsumeRecordResp.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetConsumeRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetConsumeRecordResp.Merge(dst, src)
}
func (m *SmashEgg_GetConsumeRecordResp) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetConsumeRecordResp.Size(m)
}
func (m *SmashEgg_GetConsumeRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetConsumeRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetConsumeRecordResp proto.InternalMessageInfo

func (m *SmashEgg_GetConsumeRecordResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SmashEgg_GetConsumeRecordResp) GetConsumeRecordList() []*SmashEgg_ConsumeRecord {
	if m != nil {
		return m.ConsumeRecordList
	}
	return nil
}

type SmashEgg_GetRecentWinningRecordReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Limit                uint32          `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	CurActivityType      CurActivityType `protobuf:"varint,3,opt,name=cur_activity_type,json=curActivityType,proto3,enum=ga.smash_egg.CurActivityType" json:"cur_activity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SmashEgg_GetRecentWinningRecordReq) Reset()         { *m = SmashEgg_GetRecentWinningRecordReq{} }
func (m *SmashEgg_GetRecentWinningRecordReq) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetRecentWinningRecordReq) ProtoMessage()    {}
func (*SmashEgg_GetRecentWinningRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{6}
}
func (m *SmashEgg_GetRecentWinningRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetRecentWinningRecordReq.Unmarshal(m, b)
}
func (m *SmashEgg_GetRecentWinningRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetRecentWinningRecordReq.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetRecentWinningRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetRecentWinningRecordReq.Merge(dst, src)
}
func (m *SmashEgg_GetRecentWinningRecordReq) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetRecentWinningRecordReq.Size(m)
}
func (m *SmashEgg_GetRecentWinningRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetRecentWinningRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetRecentWinningRecordReq proto.InternalMessageInfo

func (m *SmashEgg_GetRecentWinningRecordReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SmashEgg_GetRecentWinningRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SmashEgg_GetRecentWinningRecordReq) GetCurActivityType() CurActivityType {
	if m != nil {
		return m.CurActivityType
	}
	return CurActivityType_CUR_ACTIVITY_TYPE_UNSPECIFIED
}

type SmashEgg_GetRecentWinningRecordResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	WinningRecordList    []*SmashEgg_WinningRecord `protobuf:"bytes,2,rep,name=winning_record_list,json=winningRecordList,proto3" json:"winning_record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SmashEgg_GetRecentWinningRecordResp) Reset()         { *m = SmashEgg_GetRecentWinningRecordResp{} }
func (m *SmashEgg_GetRecentWinningRecordResp) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetRecentWinningRecordResp) ProtoMessage()    {}
func (*SmashEgg_GetRecentWinningRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{7}
}
func (m *SmashEgg_GetRecentWinningRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetRecentWinningRecordResp.Unmarshal(m, b)
}
func (m *SmashEgg_GetRecentWinningRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetRecentWinningRecordResp.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetRecentWinningRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetRecentWinningRecordResp.Merge(dst, src)
}
func (m *SmashEgg_GetRecentWinningRecordResp) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetRecentWinningRecordResp.Size(m)
}
func (m *SmashEgg_GetRecentWinningRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetRecentWinningRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetRecentWinningRecordResp proto.InternalMessageInfo

func (m *SmashEgg_GetRecentWinningRecordResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SmashEgg_GetRecentWinningRecordResp) GetWinningRecordList() []*SmashEgg_WinningRecord {
	if m != nil {
		return m.WinningRecordList
	}
	return nil
}

type SmashEgg_GetWinningRecordReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Offset               uint64          `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            uint32          `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32          `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Rare                 bool            `protobuf:"varint,6,opt,name=rare,proto3" json:"rare,omitempty"`
	Page                 string          `protobuf:"bytes,7,opt,name=page,proto3" json:"page,omitempty"`
	CurActivityType      CurActivityType `protobuf:"varint,8,opt,name=cur_activity_type,json=curActivityType,proto3,enum=ga.smash_egg.CurActivityType" json:"cur_activity_type,omitempty"`
	Flag                 SmashEgg_Flag   `protobuf:"varint,9,opt,name=flag,proto3,enum=ga.smash_egg.SmashEgg_Flag" json:"flag,omitempty"`
	Mode                 SmashEgg_Mode   `protobuf:"varint,10,opt,name=mode,proto3,enum=ga.smash_egg.SmashEgg_Mode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SmashEgg_GetWinningRecordReq) Reset()         { *m = SmashEgg_GetWinningRecordReq{} }
func (m *SmashEgg_GetWinningRecordReq) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetWinningRecordReq) ProtoMessage()    {}
func (*SmashEgg_GetWinningRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{8}
}
func (m *SmashEgg_GetWinningRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetWinningRecordReq.Unmarshal(m, b)
}
func (m *SmashEgg_GetWinningRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetWinningRecordReq.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetWinningRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetWinningRecordReq.Merge(dst, src)
}
func (m *SmashEgg_GetWinningRecordReq) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetWinningRecordReq.Size(m)
}
func (m *SmashEgg_GetWinningRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetWinningRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetWinningRecordReq proto.InternalMessageInfo

func (m *SmashEgg_GetWinningRecordReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SmashEgg_GetWinningRecordReq) GetOffset() uint64 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SmashEgg_GetWinningRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SmashEgg_GetWinningRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *SmashEgg_GetWinningRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SmashEgg_GetWinningRecordReq) GetRare() bool {
	if m != nil {
		return m.Rare
	}
	return false
}

func (m *SmashEgg_GetWinningRecordReq) GetPage() string {
	if m != nil {
		return m.Page
	}
	return ""
}

func (m *SmashEgg_GetWinningRecordReq) GetCurActivityType() CurActivityType {
	if m != nil {
		return m.CurActivityType
	}
	return CurActivityType_CUR_ACTIVITY_TYPE_UNSPECIFIED
}

func (m *SmashEgg_GetWinningRecordReq) GetFlag() SmashEgg_Flag {
	if m != nil {
		return m.Flag
	}
	return SmashEgg_Flag_NORMAL
}

func (m *SmashEgg_GetWinningRecordReq) GetMode() SmashEgg_Mode {
	if m != nil {
		return m.Mode
	}
	return SmashEgg_Mode_NORMAL_MODE
}

type SmashEgg_GetWinningRecordResp struct {
	BaseResp             *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	WinningRecordList    []*SmashEgg_WinningRecord   `protobuf:"bytes,2,rep,name=winning_record_list,json=winningRecordList,proto3" json:"winning_record_list,omitempty"`
	Page                 string                      `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
	WinningRecordListV2  []*SmashEgg_WinningRecordV2 `protobuf:"bytes,4,rep,name=winning_record_list_v2,json=winningRecordListV2,proto3" json:"winning_record_list_v2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *SmashEgg_GetWinningRecordResp) Reset()         { *m = SmashEgg_GetWinningRecordResp{} }
func (m *SmashEgg_GetWinningRecordResp) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetWinningRecordResp) ProtoMessage()    {}
func (*SmashEgg_GetWinningRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{9}
}
func (m *SmashEgg_GetWinningRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetWinningRecordResp.Unmarshal(m, b)
}
func (m *SmashEgg_GetWinningRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetWinningRecordResp.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetWinningRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetWinningRecordResp.Merge(dst, src)
}
func (m *SmashEgg_GetWinningRecordResp) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetWinningRecordResp.Size(m)
}
func (m *SmashEgg_GetWinningRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetWinningRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetWinningRecordResp proto.InternalMessageInfo

func (m *SmashEgg_GetWinningRecordResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SmashEgg_GetWinningRecordResp) GetWinningRecordList() []*SmashEgg_WinningRecord {
	if m != nil {
		return m.WinningRecordList
	}
	return nil
}

func (m *SmashEgg_GetWinningRecordResp) GetPage() string {
	if m != nil {
		return m.Page
	}
	return ""
}

func (m *SmashEgg_GetWinningRecordResp) GetWinningRecordListV2() []*SmashEgg_WinningRecordV2 {
	if m != nil {
		return m.WinningRecordListV2
	}
	return nil
}

type SmashEgg_RechargeReq struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Amount               uint32            `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	ChannelId            uint32            `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32            `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Platform             SmashEgg_Platform `protobuf:"varint,5,opt,name=platform,proto3,enum=ga.smash_egg.SmashEgg_Platform" json:"platform,omitempty"`
	CurActivityType      CurActivityType   `protobuf:"varint,6,opt,name=cur_activity_type,json=curActivityType,proto3,enum=ga.smash_egg.CurActivityType" json:"cur_activity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SmashEgg_RechargeReq) Reset()         { *m = SmashEgg_RechargeReq{} }
func (m *SmashEgg_RechargeReq) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_RechargeReq) ProtoMessage()    {}
func (*SmashEgg_RechargeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{10}
}
func (m *SmashEgg_RechargeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_RechargeReq.Unmarshal(m, b)
}
func (m *SmashEgg_RechargeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_RechargeReq.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_RechargeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_RechargeReq.Merge(dst, src)
}
func (m *SmashEgg_RechargeReq) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_RechargeReq.Size(m)
}
func (m *SmashEgg_RechargeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_RechargeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_RechargeReq proto.InternalMessageInfo

func (m *SmashEgg_RechargeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SmashEgg_RechargeReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *SmashEgg_RechargeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SmashEgg_RechargeReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SmashEgg_RechargeReq) GetPlatform() SmashEgg_Platform {
	if m != nil {
		return m.Platform
	}
	return SmashEgg_Platform_Unknown_Platform
}

func (m *SmashEgg_RechargeReq) GetCurActivityType() CurActivityType {
	if m != nil {
		return m.CurActivityType
	}
	return CurActivityType_CUR_ACTIVITY_TYPE_UNSPECIFIED
}

type SmashEgg_RechargeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Amount               uint32        `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Balance              uint64        `protobuf:"varint,3,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SmashEgg_RechargeResp) Reset()         { *m = SmashEgg_RechargeResp{} }
func (m *SmashEgg_RechargeResp) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_RechargeResp) ProtoMessage()    {}
func (*SmashEgg_RechargeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{11}
}
func (m *SmashEgg_RechargeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_RechargeResp.Unmarshal(m, b)
}
func (m *SmashEgg_RechargeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_RechargeResp.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_RechargeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_RechargeResp.Merge(dst, src)
}
func (m *SmashEgg_RechargeResp) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_RechargeResp.Size(m)
}
func (m *SmashEgg_RechargeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_RechargeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_RechargeResp proto.InternalMessageInfo

func (m *SmashEgg_RechargeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SmashEgg_RechargeResp) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *SmashEgg_RechargeResp) GetBalance() uint64 {
	if m != nil {
		return m.Balance
	}
	return 0
}

type SmashEgg_SmashReq struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Amount               uint32            `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	ChannelId            uint32            `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32            `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Source               SmashEgg_Source   `protobuf:"varint,5,opt,name=source,proto3,enum=ga.smash_egg.SmashEgg_Source" json:"source,omitempty"`
	Platform             SmashEgg_Platform `protobuf:"varint,6,opt,name=platform,proto3,enum=ga.smash_egg.SmashEgg_Platform" json:"platform,omitempty"`
	Mode                 SmashEgg_Mode     `protobuf:"varint,7,opt,name=mode,proto3,enum=ga.smash_egg.SmashEgg_Mode" json:"mode,omitempty"`
	CurActivityType      CurActivityType   `protobuf:"varint,8,opt,name=cur_activity_type,json=curActivityType,proto3,enum=ga.smash_egg.CurActivityType" json:"cur_activity_type,omitempty"`
	MorphFlag            SmashEgg_Flag     `protobuf:"varint,9,opt,name=morph_flag,json=morphFlag,proto3,enum=ga.smash_egg.SmashEgg_Flag" json:"morph_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SmashEgg_SmashReq) Reset()         { *m = SmashEgg_SmashReq{} }
func (m *SmashEgg_SmashReq) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_SmashReq) ProtoMessage()    {}
func (*SmashEgg_SmashReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{12}
}
func (m *SmashEgg_SmashReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_SmashReq.Unmarshal(m, b)
}
func (m *SmashEgg_SmashReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_SmashReq.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_SmashReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_SmashReq.Merge(dst, src)
}
func (m *SmashEgg_SmashReq) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_SmashReq.Size(m)
}
func (m *SmashEgg_SmashReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_SmashReq.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_SmashReq proto.InternalMessageInfo

func (m *SmashEgg_SmashReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SmashEgg_SmashReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *SmashEgg_SmashReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SmashEgg_SmashReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SmashEgg_SmashReq) GetSource() SmashEgg_Source {
	if m != nil {
		return m.Source
	}
	return SmashEgg_Source_Manual
}

func (m *SmashEgg_SmashReq) GetPlatform() SmashEgg_Platform {
	if m != nil {
		return m.Platform
	}
	return SmashEgg_Platform_Unknown_Platform
}

func (m *SmashEgg_SmashReq) GetMode() SmashEgg_Mode {
	if m != nil {
		return m.Mode
	}
	return SmashEgg_Mode_NORMAL_MODE
}

func (m *SmashEgg_SmashReq) GetCurActivityType() CurActivityType {
	if m != nil {
		return m.CurActivityType
	}
	return CurActivityType_CUR_ACTIVITY_TYPE_UNSPECIFIED
}

func (m *SmashEgg_SmashReq) GetMorphFlag() SmashEgg_Flag {
	if m != nil {
		return m.MorphFlag
	}
	return SmashEgg_Flag_NORMAL
}

type SmashEgg_SmashResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainChance         uint32                    `protobuf:"varint,2,opt,name=remain_chance,json=remainChance,proto3" json:"remain_chance,omitempty"`
	WinningRecordList    []*SmashEgg_WinningRecord `protobuf:"bytes,3,rep,name=winning_record_list,json=winningRecordList,proto3" json:"winning_record_list,omitempty"`
	CurrentHits          uint32                    `protobuf:"varint,4,opt,name=current_hits,json=currentHits,proto3" json:"current_hits,omitempty"`
	Speed                uint32                    `protobuf:"varint,5,opt,name=speed,proto3" json:"speed,omitempty"`
	OverallSpeed         uint32                    `protobuf:"varint,6,opt,name=overall_speed,json=overallSpeed,proto3" json:"overall_speed,omitempty"`
	IsBingo              bool                      `protobuf:"varint,7,opt,name=isBingo,proto3" json:"isBingo,omitempty"`
	GuaranteedVal        uint32                    `protobuf:"varint,8,opt,name=guaranteed_val,json=guaranteedVal,proto3" json:"guaranteed_val,omitempty"`
	OverallGuaranteedVal uint32                    `protobuf:"varint,9,opt,name=overall_guaranteed_val,json=overallGuaranteedVal,proto3" json:"overall_guaranteed_val,omitempty"`
	Winning              *SmashEgg_WinningRecordV2 `protobuf:"bytes,10,opt,name=winning,proto3" json:"winning,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SmashEgg_SmashResp) Reset()         { *m = SmashEgg_SmashResp{} }
func (m *SmashEgg_SmashResp) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_SmashResp) ProtoMessage()    {}
func (*SmashEgg_SmashResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{13}
}
func (m *SmashEgg_SmashResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_SmashResp.Unmarshal(m, b)
}
func (m *SmashEgg_SmashResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_SmashResp.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_SmashResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_SmashResp.Merge(dst, src)
}
func (m *SmashEgg_SmashResp) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_SmashResp.Size(m)
}
func (m *SmashEgg_SmashResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_SmashResp.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_SmashResp proto.InternalMessageInfo

func (m *SmashEgg_SmashResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SmashEgg_SmashResp) GetRemainChance() uint32 {
	if m != nil {
		return m.RemainChance
	}
	return 0
}

func (m *SmashEgg_SmashResp) GetWinningRecordList() []*SmashEgg_WinningRecord {
	if m != nil {
		return m.WinningRecordList
	}
	return nil
}

func (m *SmashEgg_SmashResp) GetCurrentHits() uint32 {
	if m != nil {
		return m.CurrentHits
	}
	return 0
}

func (m *SmashEgg_SmashResp) GetSpeed() uint32 {
	if m != nil {
		return m.Speed
	}
	return 0
}

func (m *SmashEgg_SmashResp) GetOverallSpeed() uint32 {
	if m != nil {
		return m.OverallSpeed
	}
	return 0
}

func (m *SmashEgg_SmashResp) GetIsBingo() bool {
	if m != nil {
		return m.IsBingo
	}
	return false
}

func (m *SmashEgg_SmashResp) GetGuaranteedVal() uint32 {
	if m != nil {
		return m.GuaranteedVal
	}
	return 0
}

func (m *SmashEgg_SmashResp) GetOverallGuaranteedVal() uint32 {
	if m != nil {
		return m.OverallGuaranteedVal
	}
	return 0
}

func (m *SmashEgg_SmashResp) GetWinning() *SmashEgg_WinningRecordV2 {
	if m != nil {
		return m.Winning
	}
	return nil
}

type SmashEgg_GetSmashStatusReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CurActivityType      CurActivityType `protobuf:"varint,2,opt,name=cur_activity_type,json=curActivityType,proto3,enum=ga.smash_egg.CurActivityType" json:"cur_activity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SmashEgg_GetSmashStatusReq) Reset()         { *m = SmashEgg_GetSmashStatusReq{} }
func (m *SmashEgg_GetSmashStatusReq) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetSmashStatusReq) ProtoMessage()    {}
func (*SmashEgg_GetSmashStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{14}
}
func (m *SmashEgg_GetSmashStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetSmashStatusReq.Unmarshal(m, b)
}
func (m *SmashEgg_GetSmashStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetSmashStatusReq.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetSmashStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetSmashStatusReq.Merge(dst, src)
}
func (m *SmashEgg_GetSmashStatusReq) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetSmashStatusReq.Size(m)
}
func (m *SmashEgg_GetSmashStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetSmashStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetSmashStatusReq proto.InternalMessageInfo

func (m *SmashEgg_GetSmashStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SmashEgg_GetSmashStatusReq) GetCurActivityType() CurActivityType {
	if m != nil {
		return m.CurActivityType
	}
	return CurActivityType_CUR_ACTIVITY_TYPE_UNSPECIFIED
}

type SmashEgg_GuaranteedInfo struct {
	Mode                 SmashEgg_Mode `protobuf:"varint,1,opt,name=mode,proto3,enum=ga.smash_egg.SmashEgg_Mode" json:"mode,omitempty"`
	MorphFlag            SmashEgg_Flag `protobuf:"varint,2,opt,name=morph_flag,json=morphFlag,proto3,enum=ga.smash_egg.SmashEgg_Flag" json:"morph_flag,omitempty"`
	GuaranteedVal        uint32        `protobuf:"varint,3,opt,name=guaranteed_val,json=guaranteedVal,proto3" json:"guaranteed_val,omitempty"`
	OverallGuaranteedVal uint32        `protobuf:"varint,4,opt,name=overall_guaranteed_val,json=overallGuaranteedVal,proto3" json:"overall_guaranteed_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SmashEgg_GuaranteedInfo) Reset()         { *m = SmashEgg_GuaranteedInfo{} }
func (m *SmashEgg_GuaranteedInfo) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GuaranteedInfo) ProtoMessage()    {}
func (*SmashEgg_GuaranteedInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{15}
}
func (m *SmashEgg_GuaranteedInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GuaranteedInfo.Unmarshal(m, b)
}
func (m *SmashEgg_GuaranteedInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GuaranteedInfo.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GuaranteedInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GuaranteedInfo.Merge(dst, src)
}
func (m *SmashEgg_GuaranteedInfo) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GuaranteedInfo.Size(m)
}
func (m *SmashEgg_GuaranteedInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GuaranteedInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GuaranteedInfo proto.InternalMessageInfo

func (m *SmashEgg_GuaranteedInfo) GetMode() SmashEgg_Mode {
	if m != nil {
		return m.Mode
	}
	return SmashEgg_Mode_NORMAL_MODE
}

func (m *SmashEgg_GuaranteedInfo) GetMorphFlag() SmashEgg_Flag {
	if m != nil {
		return m.MorphFlag
	}
	return SmashEgg_Flag_NORMAL
}

func (m *SmashEgg_GuaranteedInfo) GetGuaranteedVal() uint32 {
	if m != nil {
		return m.GuaranteedVal
	}
	return 0
}

func (m *SmashEgg_GuaranteedInfo) GetOverallGuaranteedVal() uint32 {
	if m != nil {
		return m.OverallGuaranteedVal
	}
	return 0
}

type SmashEgg_GetSmashStatusResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CurrentHits          uint32                     `protobuf:"varint,2,opt,name=current_hits,json=currentHits,proto3" json:"current_hits,omitempty"`
	MyRemainHits         uint32                     `protobuf:"varint,3,opt,name=my_remain_hits,json=myRemainHits,proto3" json:"my_remain_hits,omitempty"`
	MyTodayHits          uint32                     `protobuf:"varint,4,opt,name=my_today_hits,json=myTodayHits,proto3" json:"my_today_hits,omitempty"`
	MorphFlag            SmashEgg_Flag              `protobuf:"varint,5,opt,name=morph_flag,json=morphFlag,proto3,enum=ga.smash_egg.SmashEgg_Flag" json:"morph_flag,omitempty"`
	MorphEndTime         uint32                     `protobuf:"varint,6,opt,name=morph_end_time,json=morphEndTime,proto3" json:"morph_end_time,omitempty"`
	Speed                uint32                     `protobuf:"varint,7,opt,name=speed,proto3" json:"speed,omitempty"`
	OverallSpeed         uint32                     `protobuf:"varint,8,opt,name=overall_speed,json=overallSpeed,proto3" json:"overall_speed,omitempty"`
	GuaranteedInfoList   []*SmashEgg_GuaranteedInfo `protobuf:"bytes,9,rep,name=guaranteed_info_list,json=guaranteedInfoList,proto3" json:"guaranteed_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *SmashEgg_GetSmashStatusResp) Reset()         { *m = SmashEgg_GetSmashStatusResp{} }
func (m *SmashEgg_GetSmashStatusResp) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetSmashStatusResp) ProtoMessage()    {}
func (*SmashEgg_GetSmashStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{16}
}
func (m *SmashEgg_GetSmashStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetSmashStatusResp.Unmarshal(m, b)
}
func (m *SmashEgg_GetSmashStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetSmashStatusResp.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetSmashStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetSmashStatusResp.Merge(dst, src)
}
func (m *SmashEgg_GetSmashStatusResp) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetSmashStatusResp.Size(m)
}
func (m *SmashEgg_GetSmashStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetSmashStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetSmashStatusResp proto.InternalMessageInfo

func (m *SmashEgg_GetSmashStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SmashEgg_GetSmashStatusResp) GetCurrentHits() uint32 {
	if m != nil {
		return m.CurrentHits
	}
	return 0
}

func (m *SmashEgg_GetSmashStatusResp) GetMyRemainHits() uint32 {
	if m != nil {
		return m.MyRemainHits
	}
	return 0
}

func (m *SmashEgg_GetSmashStatusResp) GetMyTodayHits() uint32 {
	if m != nil {
		return m.MyTodayHits
	}
	return 0
}

func (m *SmashEgg_GetSmashStatusResp) GetMorphFlag() SmashEgg_Flag {
	if m != nil {
		return m.MorphFlag
	}
	return SmashEgg_Flag_NORMAL
}

func (m *SmashEgg_GetSmashStatusResp) GetMorphEndTime() uint32 {
	if m != nil {
		return m.MorphEndTime
	}
	return 0
}

func (m *SmashEgg_GetSmashStatusResp) GetSpeed() uint32 {
	if m != nil {
		return m.Speed
	}
	return 0
}

func (m *SmashEgg_GetSmashStatusResp) GetOverallSpeed() uint32 {
	if m != nil {
		return m.OverallSpeed
	}
	return 0
}

func (m *SmashEgg_GetSmashStatusResp) GetGuaranteedInfoList() []*SmashEgg_GuaranteedInfo {
	if m != nil {
		return m.GuaranteedInfoList
	}
	return nil
}

type SmashEgg_GetConfigReq struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Platform             SmashEgg_Platform `protobuf:"varint,2,opt,name=platform,proto3,enum=ga.smash_egg.SmashEgg_Platform" json:"platform,omitempty"`
	ChannelId            uint32            `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SmashEgg_GetConfigReq) Reset()         { *m = SmashEgg_GetConfigReq{} }
func (m *SmashEgg_GetConfigReq) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetConfigReq) ProtoMessage()    {}
func (*SmashEgg_GetConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{17}
}
func (m *SmashEgg_GetConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetConfigReq.Unmarshal(m, b)
}
func (m *SmashEgg_GetConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetConfigReq.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetConfigReq.Merge(dst, src)
}
func (m *SmashEgg_GetConfigReq) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetConfigReq.Size(m)
}
func (m *SmashEgg_GetConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetConfigReq proto.InternalMessageInfo

func (m *SmashEgg_GetConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SmashEgg_GetConfigReq) GetPlatform() SmashEgg_Platform {
	if m != nil {
		return m.Platform
	}
	return SmashEgg_Platform_Unknown_Platform
}

func (m *SmashEgg_GetConfigReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SmashEgg_GetConfigResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RechargeGiftId       uint32        `protobuf:"varint,2,opt,name=recharge_gift_id,json=rechargeGiftId,proto3" json:"recharge_gift_id,omitempty"`
	RechargeGiftType     uint32        `protobuf:"varint,3,opt,name=recharge_gift_type,json=rechargeGiftType,proto3" json:"recharge_gift_type,omitempty"`
	RechargeGiftPrice    uint32        `protobuf:"varint,4,opt,name=recharge_gift_price,json=rechargeGiftPrice,proto3" json:"recharge_gift_price,omitempty"`
	RechargeGiftName     string        `protobuf:"bytes,5,opt,name=recharge_gift_name,json=rechargeGiftName,proto3" json:"recharge_gift_name,omitempty"`
	RechargeGiftPic      string        `protobuf:"bytes,6,opt,name=recharge_gift_pic,json=rechargeGiftPic,proto3" json:"recharge_gift_pic,omitempty"`
	MorphHits            uint32        `protobuf:"varint,7,opt,name=morph_hits,json=morphHits,proto3" json:"morph_hits,omitempty"`
	MorphDuration        uint32        `protobuf:"varint,8,opt,name=morph_duration,json=morphDuration,proto3" json:"morph_duration,omitempty"`
	DailyLimit           uint32        `protobuf:"varint,9,opt,name=daily_limit,json=dailyLimit,proto3" json:"daily_limit,omitempty"`
	IsShow               bool          `protobuf:"varint,10,opt,name=is_show,json=isShow,proto3" json:"is_show,omitempty"`
	IsShowGold           bool          `protobuf:"varint,11,opt,name=is_show_gold,json=isShowGold,proto3" json:"is_show_gold,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SmashEgg_GetConfigResp) Reset()         { *m = SmashEgg_GetConfigResp{} }
func (m *SmashEgg_GetConfigResp) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetConfigResp) ProtoMessage()    {}
func (*SmashEgg_GetConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{18}
}
func (m *SmashEgg_GetConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetConfigResp.Unmarshal(m, b)
}
func (m *SmashEgg_GetConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetConfigResp.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetConfigResp.Merge(dst, src)
}
func (m *SmashEgg_GetConfigResp) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetConfigResp.Size(m)
}
func (m *SmashEgg_GetConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetConfigResp proto.InternalMessageInfo

func (m *SmashEgg_GetConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SmashEgg_GetConfigResp) GetRechargeGiftId() uint32 {
	if m != nil {
		return m.RechargeGiftId
	}
	return 0
}

func (m *SmashEgg_GetConfigResp) GetRechargeGiftType() uint32 {
	if m != nil {
		return m.RechargeGiftType
	}
	return 0
}

func (m *SmashEgg_GetConfigResp) GetRechargeGiftPrice() uint32 {
	if m != nil {
		return m.RechargeGiftPrice
	}
	return 0
}

func (m *SmashEgg_GetConfigResp) GetRechargeGiftName() string {
	if m != nil {
		return m.RechargeGiftName
	}
	return ""
}

func (m *SmashEgg_GetConfigResp) GetRechargeGiftPic() string {
	if m != nil {
		return m.RechargeGiftPic
	}
	return ""
}

func (m *SmashEgg_GetConfigResp) GetMorphHits() uint32 {
	if m != nil {
		return m.MorphHits
	}
	return 0
}

func (m *SmashEgg_GetConfigResp) GetMorphDuration() uint32 {
	if m != nil {
		return m.MorphDuration
	}
	return 0
}

func (m *SmashEgg_GetConfigResp) GetDailyLimit() uint32 {
	if m != nil {
		return m.DailyLimit
	}
	return 0
}

func (m *SmashEgg_GetConfigResp) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

func (m *SmashEgg_GetConfigResp) GetIsShowGold() bool {
	if m != nil {
		return m.IsShowGold
	}
	return false
}

// 客户端记录res_version,与每次获取的对应版本号比较，如果版本号不一致，则重新加载新的视觉资源
// 获取活动主题资源
type SmashEgg_GetResourceConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SmashEgg_GetResourceConfigReq) Reset()         { *m = SmashEgg_GetResourceConfigReq{} }
func (m *SmashEgg_GetResourceConfigReq) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetResourceConfigReq) ProtoMessage()    {}
func (*SmashEgg_GetResourceConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{19}
}
func (m *SmashEgg_GetResourceConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetResourceConfigReq.Unmarshal(m, b)
}
func (m *SmashEgg_GetResourceConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetResourceConfigReq.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetResourceConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetResourceConfigReq.Merge(dst, src)
}
func (m *SmashEgg_GetResourceConfigReq) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetResourceConfigReq.Size(m)
}
func (m *SmashEgg_GetResourceConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetResourceConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetResourceConfigReq proto.InternalMessageInfo

func (m *SmashEgg_GetResourceConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// 各模式配置
type SmashEgg_ModeCfg struct {
	Mode                   SmashEgg_Mode `protobuf:"varint,1,opt,name=mode,proto3,enum=ga.smash_egg.SmashEgg_Mode" json:"mode,omitempty"`
	MorphFlag              SmashEgg_Flag `protobuf:"varint,2,opt,name=morph_flag,json=morphFlag,proto3,enum=ga.smash_egg.SmashEgg_Flag" json:"morph_flag,omitempty"`
	RechargePropNumOptions []uint32      `protobuf:"varint,3,rep,packed,name=recharge_prop_num_options,json=rechargePropNumOptions,proto3" json:"recharge_prop_num_options,omitempty"`
	SmashCostPropNum       uint32        `protobuf:"varint,4,opt,name=smash_cost_prop_num,json=smashCostPropNum,proto3" json:"smash_cost_prop_num,omitempty"`
	GuaranteedGiftPic      string        `protobuf:"bytes,5,opt,name=guaranteed_gift_pic,json=guaranteedGiftPic,proto3" json:"guaranteed_gift_pic,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}      `json:"-"`
	XXX_unrecognized       []byte        `json:"-"`
	XXX_sizecache          int32         `json:"-"`
}

func (m *SmashEgg_ModeCfg) Reset()         { *m = SmashEgg_ModeCfg{} }
func (m *SmashEgg_ModeCfg) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_ModeCfg) ProtoMessage()    {}
func (*SmashEgg_ModeCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{20}
}
func (m *SmashEgg_ModeCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_ModeCfg.Unmarshal(m, b)
}
func (m *SmashEgg_ModeCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_ModeCfg.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_ModeCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_ModeCfg.Merge(dst, src)
}
func (m *SmashEgg_ModeCfg) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_ModeCfg.Size(m)
}
func (m *SmashEgg_ModeCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_ModeCfg.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_ModeCfg proto.InternalMessageInfo

func (m *SmashEgg_ModeCfg) GetMode() SmashEgg_Mode {
	if m != nil {
		return m.Mode
	}
	return SmashEgg_Mode_NORMAL_MODE
}

func (m *SmashEgg_ModeCfg) GetMorphFlag() SmashEgg_Flag {
	if m != nil {
		return m.MorphFlag
	}
	return SmashEgg_Flag_NORMAL
}

func (m *SmashEgg_ModeCfg) GetRechargePropNumOptions() []uint32 {
	if m != nil {
		return m.RechargePropNumOptions
	}
	return nil
}

func (m *SmashEgg_ModeCfg) GetSmashCostPropNum() uint32 {
	if m != nil {
		return m.SmashCostPropNum
	}
	return 0
}

func (m *SmashEgg_ModeCfg) GetGuaranteedGiftPic() string {
	if m != nil {
		return m.GuaranteedGiftPic
	}
	return ""
}

type SmashEgg_GetResourceConfigResp struct {
	BaseResp            *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CurActivityType     CurActivityType `protobuf:"varint,2,opt,name=cur_activity_type,json=curActivityType,proto3,enum=ga.smash_egg.CurActivityType" json:"cur_activity_type,omitempty"`
	ResVersion          uint32          `protobuf:"varint,3,opt,name=res_version,json=resVersion,proto3" json:"res_version,omitempty"`
	EntryPic            string          `protobuf:"bytes,4,opt,name=entry_pic,json=entryPic,proto3" json:"entry_pic,omitempty"`
	EntryTitle          string          `protobuf:"bytes,5,opt,name=entry_title,json=entryTitle,proto3" json:"entry_title,omitempty"`
	ActivityResource    string          `protobuf:"bytes,6,opt,name=activity_resource,json=activityResource,proto3" json:"activity_resource,omitempty"`
	ActivityResourceMd5 string          `protobuf:"bytes,7,opt,name=activity_resource_md5,json=activityResourceMd5,proto3" json:"activity_resource_md5,omitempty"`
	TextConfig          string          `protobuf:"bytes,8,opt,name=text_config,json=textConfig,proto3" json:"text_config,omitempty"`
	// 购买道具相关配置
	RechargeGiftPic   string `protobuf:"bytes,9,opt,name=recharge_gift_pic,json=rechargeGiftPic,proto3" json:"recharge_gift_pic,omitempty"`
	RechargeGiftName  string `protobuf:"bytes,10,opt,name=recharge_gift_name,json=rechargeGiftName,proto3" json:"recharge_gift_name,omitempty"`
	RechargePropPic   string `protobuf:"bytes,11,opt,name=recharge_prop_pic,json=rechargePropPic,proto3" json:"recharge_prop_pic,omitempty"`
	RechargePropName  string `protobuf:"bytes,12,opt,name=recharge_prop_name,json=rechargePropName,proto3" json:"recharge_prop_name,omitempty"`
	RechargeReminder  string `protobuf:"bytes,13,opt,name=recharge_reminder,json=rechargeReminder,proto3" json:"recharge_reminder,omitempty"`
	RechargeGiftPrice uint32 `protobuf:"varint,14,opt,name=recharge_gift_price,json=rechargeGiftPrice,proto3" json:"recharge_gift_price,omitempty"`
	// 规则页资源, 资源后缀，客户端根据不同马甲包自行拼接
	ActivityRules        string              `protobuf:"bytes,15,opt,name=activity_rules,json=activityRules,proto3" json:"activity_rules,omitempty"`
	RegularPrizePool     string              `protobuf:"bytes,16,opt,name=regular_prize_pool,json=regularPrizePool,proto3" json:"regular_prize_pool,omitempty"`
	GoldPrizePool        string              `protobuf:"bytes,17,opt,name=gold_prize_pool,json=goldPrizePool,proto3" json:"gold_prize_pool,omitempty"`
	MorphRules           string              `protobuf:"bytes,18,opt,name=morph_rules,json=morphRules,proto3" json:"morph_rules,omitempty"`
	LuckPointRules       string              `protobuf:"bytes,19,opt,name=luck_point_rules,json=luckPointRules,proto3" json:"luck_point_rules,omitempty"`
	ActivityGoldRules    string              `protobuf:"bytes,20,opt,name=activity_gold_rules,json=activityGoldRules,proto3" json:"activity_gold_rules,omitempty"`
	GoldLuckPointRules   string              `protobuf:"bytes,21,opt,name=gold_luck_point_rules,json=goldLuckPointRules,proto3" json:"gold_luck_point_rules,omitempty"`
	ModeCfgList          []*SmashEgg_ModeCfg `protobuf:"bytes,22,rep,name=mode_cfg_list,json=modeCfgList,proto3" json:"mode_cfg_list,omitempty"`
	MorphPrizePool       string              `protobuf:"bytes,23,opt,name=morph_prize_pool,json=morphPrizePool,proto3" json:"morph_prize_pool,omitempty"`
	MorphLuckPointRules  string              `protobuf:"bytes,24,opt,name=morph_luck_point_rules,json=morphLuckPointRules,proto3" json:"morph_luck_point_rules,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SmashEgg_GetResourceConfigResp) Reset()         { *m = SmashEgg_GetResourceConfigResp{} }
func (m *SmashEgg_GetResourceConfigResp) String() string { return proto.CompactTextString(m) }
func (*SmashEgg_GetResourceConfigResp) ProtoMessage()    {}
func (*SmashEgg_GetResourceConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{21}
}
func (m *SmashEgg_GetResourceConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SmashEgg_GetResourceConfigResp.Unmarshal(m, b)
}
func (m *SmashEgg_GetResourceConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SmashEgg_GetResourceConfigResp.Marshal(b, m, deterministic)
}
func (dst *SmashEgg_GetResourceConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SmashEgg_GetResourceConfigResp.Merge(dst, src)
}
func (m *SmashEgg_GetResourceConfigResp) XXX_Size() int {
	return xxx_messageInfo_SmashEgg_GetResourceConfigResp.Size(m)
}
func (m *SmashEgg_GetResourceConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SmashEgg_GetResourceConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_SmashEgg_GetResourceConfigResp proto.InternalMessageInfo

func (m *SmashEgg_GetResourceConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SmashEgg_GetResourceConfigResp) GetCurActivityType() CurActivityType {
	if m != nil {
		return m.CurActivityType
	}
	return CurActivityType_CUR_ACTIVITY_TYPE_UNSPECIFIED
}

func (m *SmashEgg_GetResourceConfigResp) GetResVersion() uint32 {
	if m != nil {
		return m.ResVersion
	}
	return 0
}

func (m *SmashEgg_GetResourceConfigResp) GetEntryPic() string {
	if m != nil {
		return m.EntryPic
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetEntryTitle() string {
	if m != nil {
		return m.EntryTitle
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetActivityResource() string {
	if m != nil {
		return m.ActivityResource
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetActivityResourceMd5() string {
	if m != nil {
		return m.ActivityResourceMd5
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetTextConfig() string {
	if m != nil {
		return m.TextConfig
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetRechargeGiftPic() string {
	if m != nil {
		return m.RechargeGiftPic
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetRechargeGiftName() string {
	if m != nil {
		return m.RechargeGiftName
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetRechargePropPic() string {
	if m != nil {
		return m.RechargePropPic
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetRechargePropName() string {
	if m != nil {
		return m.RechargePropName
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetRechargeReminder() string {
	if m != nil {
		return m.RechargeReminder
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetRechargeGiftPrice() uint32 {
	if m != nil {
		return m.RechargeGiftPrice
	}
	return 0
}

func (m *SmashEgg_GetResourceConfigResp) GetActivityRules() string {
	if m != nil {
		return m.ActivityRules
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetRegularPrizePool() string {
	if m != nil {
		return m.RegularPrizePool
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetGoldPrizePool() string {
	if m != nil {
		return m.GoldPrizePool
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetMorphRules() string {
	if m != nil {
		return m.MorphRules
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetLuckPointRules() string {
	if m != nil {
		return m.LuckPointRules
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetActivityGoldRules() string {
	if m != nil {
		return m.ActivityGoldRules
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetGoldLuckPointRules() string {
	if m != nil {
		return m.GoldLuckPointRules
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetModeCfgList() []*SmashEgg_ModeCfg {
	if m != nil {
		return m.ModeCfgList
	}
	return nil
}

func (m *SmashEgg_GetResourceConfigResp) GetMorphPrizePool() string {
	if m != nil {
		return m.MorphPrizePool
	}
	return ""
}

func (m *SmashEgg_GetResourceConfigResp) GetMorphLuckPointRules() string {
	if m != nil {
		return m.MorphLuckPointRules
	}
	return ""
}

// 道具时效
type PropTimeliness struct {
	Num                  uint32   `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
	FinTimestamp         int64    `protobuf:"varint,2,opt,name=fin_timestamp,json=finTimestamp,proto3" json:"fin_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PropTimeliness) Reset()         { *m = PropTimeliness{} }
func (m *PropTimeliness) String() string { return proto.CompactTextString(m) }
func (*PropTimeliness) ProtoMessage()    {}
func (*PropTimeliness) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{22}
}
func (m *PropTimeliness) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropTimeliness.Unmarshal(m, b)
}
func (m *PropTimeliness) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropTimeliness.Marshal(b, m, deterministic)
}
func (dst *PropTimeliness) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropTimeliness.Merge(dst, src)
}
func (m *PropTimeliness) XXX_Size() int {
	return xxx_messageInfo_PropTimeliness.Size(m)
}
func (m *PropTimeliness) XXX_DiscardUnknown() {
	xxx_messageInfo_PropTimeliness.DiscardUnknown(m)
}

var xxx_messageInfo_PropTimeliness proto.InternalMessageInfo

func (m *PropTimeliness) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *PropTimeliness) GetFinTimestamp() int64 {
	if m != nil {
		return m.FinTimestamp
	}
	return 0
}

type GetUserPropListReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SortAsc              bool            `protobuf:"varint,2,opt,name=sort_asc,json=sortAsc,proto3" json:"sort_asc,omitempty"`
	CurActivityType      CurActivityType `protobuf:"varint,3,opt,name=cur_activity_type,json=curActivityType,proto3,enum=ga.smash_egg.CurActivityType" json:"cur_activity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserPropListReq) Reset()         { *m = GetUserPropListReq{} }
func (m *GetUserPropListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserPropListReq) ProtoMessage()    {}
func (*GetUserPropListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{23}
}
func (m *GetUserPropListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropListReq.Unmarshal(m, b)
}
func (m *GetUserPropListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserPropListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropListReq.Merge(dst, src)
}
func (m *GetUserPropListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserPropListReq.Size(m)
}
func (m *GetUserPropListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropListReq proto.InternalMessageInfo

func (m *GetUserPropListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserPropListReq) GetSortAsc() bool {
	if m != nil {
		return m.SortAsc
	}
	return false
}

func (m *GetUserPropListReq) GetCurActivityType() CurActivityType {
	if m != nil {
		return m.CurActivityType
	}
	return CurActivityType_CUR_ACTIVITY_TYPE_UNSPECIFIED
}

type GetUserPropListResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PropList             []*PropTimeliness `protobuf:"bytes,2,rep,name=prop_list,json=propList,proto3" json:"prop_list,omitempty"`
	TotalPropNum         uint32            `protobuf:"varint,3,opt,name=total_prop_num,json=totalPropNum,proto3" json:"total_prop_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserPropListResp) Reset()         { *m = GetUserPropListResp{} }
func (m *GetUserPropListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserPropListResp) ProtoMessage()    {}
func (*GetUserPropListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{24}
}
func (m *GetUserPropListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPropListResp.Unmarshal(m, b)
}
func (m *GetUserPropListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPropListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserPropListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPropListResp.Merge(dst, src)
}
func (m *GetUserPropListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserPropListResp.Size(m)
}
func (m *GetUserPropListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPropListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPropListResp proto.InternalMessageInfo

func (m *GetUserPropListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserPropListResp) GetPropList() []*PropTimeliness {
	if m != nil {
		return m.PropList
	}
	return nil
}

func (m *GetUserPropListResp) GetTotalPropNum() uint32 {
	if m != nil {
		return m.TotalPropNum
	}
	return 0
}

type GetUserExpirePropNotifyReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserExpirePropNotifyReq) Reset()         { *m = GetUserExpirePropNotifyReq{} }
func (m *GetUserExpirePropNotifyReq) String() string { return proto.CompactTextString(m) }
func (*GetUserExpirePropNotifyReq) ProtoMessage()    {}
func (*GetUserExpirePropNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{25}
}
func (m *GetUserExpirePropNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExpirePropNotifyReq.Unmarshal(m, b)
}
func (m *GetUserExpirePropNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExpirePropNotifyReq.Marshal(b, m, deterministic)
}
func (dst *GetUserExpirePropNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExpirePropNotifyReq.Merge(dst, src)
}
func (m *GetUserExpirePropNotifyReq) XXX_Size() int {
	return xxx_messageInfo_GetUserExpirePropNotifyReq.Size(m)
}
func (m *GetUserExpirePropNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExpirePropNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExpirePropNotifyReq proto.InternalMessageInfo

func (m *GetUserExpirePropNotifyReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserExpirePropNotifyResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NeedNotify           bool          `protobuf:"varint,2,opt,name=need_notify,json=needNotify,proto3" json:"need_notify,omitempty"`
	NotifyContent        string        `protobuf:"bytes,3,opt,name=notify_content,json=notifyContent,proto3" json:"notify_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserExpirePropNotifyResp) Reset()         { *m = GetUserExpirePropNotifyResp{} }
func (m *GetUserExpirePropNotifyResp) String() string { return proto.CompactTextString(m) }
func (*GetUserExpirePropNotifyResp) ProtoMessage()    {}
func (*GetUserExpirePropNotifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_smash_egg_logic__f091fec36bba56a5, []int{26}
}
func (m *GetUserExpirePropNotifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExpirePropNotifyResp.Unmarshal(m, b)
}
func (m *GetUserExpirePropNotifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExpirePropNotifyResp.Marshal(b, m, deterministic)
}
func (dst *GetUserExpirePropNotifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExpirePropNotifyResp.Merge(dst, src)
}
func (m *GetUserExpirePropNotifyResp) XXX_Size() int {
	return xxx_messageInfo_GetUserExpirePropNotifyResp.Size(m)
}
func (m *GetUserExpirePropNotifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExpirePropNotifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExpirePropNotifyResp proto.InternalMessageInfo

func (m *GetUserExpirePropNotifyResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserExpirePropNotifyResp) GetNeedNotify() bool {
	if m != nil {
		return m.NeedNotify
	}
	return false
}

func (m *GetUserExpirePropNotifyResp) GetNotifyContent() string {
	if m != nil {
		return m.NotifyContent
	}
	return ""
}

func init() {
	proto.RegisterType((*SmashEgg_ConsumeRecord)(nil), "ga.smash_egg.SmashEgg_ConsumeRecord")
	proto.RegisterType((*SmashEgg_WinningRecord)(nil), "ga.smash_egg.SmashEgg_WinningRecord")
	proto.RegisterType((*SmashAwardInfo)(nil), "ga.smash_egg.SmashAwardInfo")
	proto.RegisterType((*SmashEgg_WinningRecordV2)(nil), "ga.smash_egg.SmashEgg_WinningRecordV2")
	proto.RegisterType((*SmashEgg_GetConsumeRecordReq)(nil), "ga.smash_egg.SmashEgg_GetConsumeRecordReq")
	proto.RegisterType((*SmashEgg_GetConsumeRecordResp)(nil), "ga.smash_egg.SmashEgg_GetConsumeRecordResp")
	proto.RegisterType((*SmashEgg_GetRecentWinningRecordReq)(nil), "ga.smash_egg.SmashEgg_GetRecentWinningRecordReq")
	proto.RegisterType((*SmashEgg_GetRecentWinningRecordResp)(nil), "ga.smash_egg.SmashEgg_GetRecentWinningRecordResp")
	proto.RegisterType((*SmashEgg_GetWinningRecordReq)(nil), "ga.smash_egg.SmashEgg_GetWinningRecordReq")
	proto.RegisterType((*SmashEgg_GetWinningRecordResp)(nil), "ga.smash_egg.SmashEgg_GetWinningRecordResp")
	proto.RegisterType((*SmashEgg_RechargeReq)(nil), "ga.smash_egg.SmashEgg_RechargeReq")
	proto.RegisterType((*SmashEgg_RechargeResp)(nil), "ga.smash_egg.SmashEgg_RechargeResp")
	proto.RegisterType((*SmashEgg_SmashReq)(nil), "ga.smash_egg.SmashEgg_SmashReq")
	proto.RegisterType((*SmashEgg_SmashResp)(nil), "ga.smash_egg.SmashEgg_SmashResp")
	proto.RegisterType((*SmashEgg_GetSmashStatusReq)(nil), "ga.smash_egg.SmashEgg_GetSmashStatusReq")
	proto.RegisterType((*SmashEgg_GuaranteedInfo)(nil), "ga.smash_egg.SmashEgg_GuaranteedInfo")
	proto.RegisterType((*SmashEgg_GetSmashStatusResp)(nil), "ga.smash_egg.SmashEgg_GetSmashStatusResp")
	proto.RegisterType((*SmashEgg_GetConfigReq)(nil), "ga.smash_egg.SmashEgg_GetConfigReq")
	proto.RegisterType((*SmashEgg_GetConfigResp)(nil), "ga.smash_egg.SmashEgg_GetConfigResp")
	proto.RegisterType((*SmashEgg_GetResourceConfigReq)(nil), "ga.smash_egg.SmashEgg_GetResourceConfigReq")
	proto.RegisterType((*SmashEgg_ModeCfg)(nil), "ga.smash_egg.SmashEgg_ModeCfg")
	proto.RegisterType((*SmashEgg_GetResourceConfigResp)(nil), "ga.smash_egg.SmashEgg_GetResourceConfigResp")
	proto.RegisterType((*PropTimeliness)(nil), "ga.smash_egg.PropTimeliness")
	proto.RegisterType((*GetUserPropListReq)(nil), "ga.smash_egg.GetUserPropListReq")
	proto.RegisterType((*GetUserPropListResp)(nil), "ga.smash_egg.GetUserPropListResp")
	proto.RegisterType((*GetUserExpirePropNotifyReq)(nil), "ga.smash_egg.GetUserExpirePropNotifyReq")
	proto.RegisterType((*GetUserExpirePropNotifyResp)(nil), "ga.smash_egg.GetUserExpirePropNotifyResp")
	proto.RegisterEnum("ga.smash_egg.SmashEgg_Source", SmashEgg_Source_name, SmashEgg_Source_value)
	proto.RegisterEnum("ga.smash_egg.SmashEgg_Flag", SmashEgg_Flag_name, SmashEgg_Flag_value)
	proto.RegisterEnum("ga.smash_egg.SmashEgg_Mode", SmashEgg_Mode_name, SmashEgg_Mode_value)
	proto.RegisterEnum("ga.smash_egg.SmashEgg_Platform", SmashEgg_Platform_name, SmashEgg_Platform_value)
	proto.RegisterEnum("ga.smash_egg.SmashEgg_App", SmashEgg_App_name, SmashEgg_App_value)
	proto.RegisterEnum("ga.smash_egg.CurActivityType", CurActivityType_name, CurActivityType_value)
	proto.RegisterEnum("ga.smash_egg.SmashAwardInfo_GiftFlag", SmashAwardInfo_GiftFlag_name, SmashAwardInfo_GiftFlag_value)
}

func init() {
	proto.RegisterFile("smash-egg-logic_.proto", fileDescriptor_smash_egg_logic__f091fec36bba56a5)
}

var fileDescriptor_smash_egg_logic__f091fec36bba56a5 = []byte{
	// 2428 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x59, 0x4f, 0x77, 0xdb, 0xc6,
	0x11, 0x17, 0x48, 0x4a, 0x24, 0x87, 0x7f, 0x04, 0xad, 0x64, 0x99, 0xb6, 0xe3, 0xd8, 0x61, 0x62,
	0x47, 0x55, 0x12, 0xf9, 0x55, 0xa9, 0x0f, 0x69, 0x0e, 0x29, 0x45, 0x29, 0x8a, 0xde, 0xb3, 0x2c,
	0x3d, 0x88, 0x52, 0xea, 0xf4, 0x80, 0x07, 0x01, 0x4b, 0x68, 0x9f, 0xf1, 0x2f, 0x00, 0x68, 0x89,
	0xbd, 0xf6, 0x03, 0xb4, 0x3d, 0xf6, 0xf5, 0xe2, 0xf6, 0xd0, 0x4b, 0x5f, 0x7b, 0xee, 0xb5, 0xa7,
	0x5e, 0xdb, 0x4b, 0xbf, 0x40, 0x5f, 0xbf, 0x47, 0xdf, 0xce, 0x2e, 0x40, 0x80, 0x22, 0x15, 0xd2,
	0xf5, 0xcb, 0xcb, 0x6d, 0x77, 0x76, 0x76, 0x76, 0x76, 0xe6, 0x37, 0x33, 0x8b, 0x01, 0xac, 0x47,
	0xae, 0x11, 0x5d, 0x7c, 0x42, 0x6d, 0xfb, 0x13, 0xc7, 0xb7, 0x99, 0xa9, 0x6f, 0x05, 0xa1, 0x1f,
	0xfb, 0xa4, 0x6e, 0x1b, 0x5b, 0xb8, 0xa4, 0x53, 0xdb, 0xbe, 0xdb, 0xb0, 0x0d, 0xfd, 0xdc, 0x88,
	0xa8, 0x58, 0x6c, 0xbf, 0x56, 0x60, 0xfd, 0x84, 0x2f, 0xee, 0xd9, 0xb6, 0xde, 0xf5, 0xbd, 0x68,
	0xe0, 0x52, 0x8d, 0x9a, 0x7e, 0x68, 0x91, 0x26, 0x14, 0x98, 0xd5, 0x52, 0x1e, 0x2a, 0x1b, 0x25,
	0xad, 0xc0, 0x2c, 0xa2, 0x42, 0x71, 0xc0, 0xac, 0x56, 0xe1, 0xa1, 0xb2, 0xd1, 0xd0, 0xf8, 0x90,
	0xac, 0xc3, 0x92, 0xe1, 0xfa, 0x03, 0x2f, 0x6e, 0x15, 0x91, 0x28, 0x67, 0x9c, 0xb3, 0x4f, 0x69,
	0xab, 0x24, 0x38, 0xfb, 0x94, 0x92, 0x3b, 0x50, 0xf1, 0x43, 0x8b, 0x86, 0x3a, 0xb3, 0x5a, 0x8b,
	0x0f, 0x95, 0x8d, 0xaa, 0x56, 0xc6, 0xf9, 0x81, 0x45, 0x1e, 0x40, 0xcd, 0x0c, 0xa9, 0x11, 0x53,
	0x3d, 0x66, 0x2e, 0x6d, 0x2d, 0xe1, 0x26, 0x10, 0xa4, 0x1e, 0x73, 0x69, 0xfb, 0x6f, 0xa5, 0x8c,
	0x8a, 0x5f, 0x33, 0xcf, 0x63, 0x9e, 0x3d, 0xb3, 0x8a, 0x77, 0xa1, 0xe2, 0x31, 0xf3, 0xa5, 0x67,
	0xb8, 0x14, 0x95, 0xac, 0x6a, 0xe9, 0x9c, 0x3c, 0x85, 0xa5, 0xc8, 0x1f, 0x84, 0xa6, 0xd0, 0xb4,
	0xb9, 0x7d, 0x7f, 0x2b, 0x6b, 0xa9, 0xad, 0xf4, 0xcc, 0x13, 0x64, 0xd2, 0x24, 0x33, 0x79, 0x02,
	0xa5, 0xbe, 0x63, 0xd8, 0x78, 0x8f, 0xe6, 0xf6, 0xbd, 0x29, 0x9b, 0xbe, 0x74, 0x0c, 0x5b, 0x43,
	0x46, 0x72, 0x1b, 0xca, 0x81, 0x61, 0xbe, 0xe4, 0x77, 0x17, 0xb7, 0x5b, 0xe2, 0xd3, 0x03, 0x8b,
	0xdc, 0x83, 0x2a, 0x2e, 0xa0, 0x76, 0x65, 0xa1, 0x1d, 0x27, 0x3c, 0xe7, 0xda, 0xdd, 0x01, 0x1c,
	0xeb, 0x01, 0x33, 0x5b, 0x15, 0x61, 0x32, 0x3e, 0x3f, 0x66, 0x26, 0x37, 0x19, 0x2e, 0x49, 0xe3,
	0x57, 0x85, 0xc9, 0x38, 0xa9, 0x23, 0x1c, 0x30, 0x66, 0x53, 0x18, 0xb7, 0x69, 0x7a, 0xb2, 0x45,
	0x23, 0xb3, 0x55, 0x1b, 0x9d, 0xbc, 0x4b, 0x23, 0x93, 0xbc, 0x0f, 0x0d, 0xcf, 0x8f, 0x59, 0x7f,
	0xa8, 0x07, 0x21, 0xed, 0xb3, 0xab, 0x56, 0x1d, 0xf7, 0xd7, 0x05, 0xf1, 0x18, 0x69, 0xe4, 0x3e,
	0x80, 0x64, 0xba, 0xba, 0xba, 0x6a, 0x35, 0x50, 0x44, 0x55, 0x50, 0x7e, 0x7e, 0x75, 0x45, 0x36,
	0x61, 0x65, 0xb4, 0xac, 0xf7, 0xfd, 0xd0, 0x35, 0xe2, 0x56, 0x13, 0xb9, 0x96, 0x53, 0xae, 0x2f,
	0x91, 0xcc, 0x0d, 0xea, 0xfa, 0x16, 0x6d, 0x2d, 0xdf, 0x68, 0xd0, 0x43, 0xdf, 0xa2, 0x1a, 0x32,
	0x92, 0x0f, 0x41, 0xca, 0xd0, 0x43, 0x2a, 0x3d, 0xa8, 0xa2, 0xe8, 0xa6, 0x20, 0x6b, 0x92, 0xda,
	0xfe, 0xa7, 0x02, 0x4d, 0x14, 0xd0, 0xb9, 0x34, 0x42, 0xeb, 0xc0, 0xeb, 0xfb, 0x19, 0xc8, 0x34,
	0x10, 0x32, 0x04, 0x4a, 0x68, 0xfe, 0x02, 0x0a, 0xc0, 0x31, 0x87, 0x11, 0xb7, 0xba, 0xc0, 0x0b,
	0x1f, 0x66, 0x90, 0x5e, 0xca, 0x21, 0x7d, 0x07, 0xaa, 0x36, 0xeb, 0xc7, 0x7a, 0x06, 0x10, 0x8f,
	0x26, 0xe8, 0x9f, 0x1e, 0xbf, 0xb5, 0xcf, 0xfa, 0x31, 0x42, 0xa3, 0x62, 0xcb, 0x51, 0xfb, 0x63,
	0xa8, 0x24, 0x54, 0x02, 0xb0, 0xf4, 0xfc, 0x48, 0x3b, 0xec, 0x3c, 0x53, 0x17, 0xf8, 0xf8, 0xa4,
	0xdb, 0xd1, 0xba, 0x7b, 0xaa, 0x42, 0x2a, 0x50, 0xda, 0xd9, 0x3b, 0xe9, 0xa9, 0x85, 0xf6, 0x7f,
	0x8b, 0xd0, 0x9a, 0x1c, 0x0d, 0x67, 0xdb, 0x33, 0xc4, 0xc3, 0x08, 0xf3, 0xc5, 0x37, 0xc1, 0x7c,
	0x69, 0x56, 0xcc, 0x27, 0x3e, 0x5d, 0x9c, 0xd5, 0xa7, 0x53, 0x83, 0x64, 0x0c, 0xcb, 0xe5, 0x6b,
	0x58, 0xfe, 0x1c, 0xc0, 0xe0, 0xf6, 0xd5, 0x1d, 0x16, 0xc5, 0xad, 0xca, 0xc3, 0xe2, 0x46, 0x6d,
	0xfb, 0x9d, 0x9b, 0x9c, 0xa0, 0x55, 0x91, 0xff, 0x19, 0x8b, 0xe2, 0xeb, 0x58, 0xaf, 0x7e, 0x27,
	0xd6, 0x61, 0x26, 0xac, 0xd7, 0x26, 0x63, 0x7d, 0x02, 0x74, 0xeb, 0x13, 0xa1, 0xfb, 0x57, 0x05,
	0xde, 0x49, 0xed, 0xb4, 0x4f, 0xe3, 0x5c, 0x6e, 0xd6, 0xe8, 0xb7, 0xe4, 0x31, 0x54, 0x78, 0x1e,
	0xd7, 0x43, 0xfa, 0x2d, 0x7a, 0xbc, 0xb6, 0x5d, 0xe3, 0x97, 0xde, 0x31, 0x22, 0xaa, 0xd1, 0x6f,
	0xb5, 0xf2, 0xb9, 0x18, 0x70, 0xe8, 0xfa, 0xfd, 0x7e, 0x44, 0x63, 0x84, 0x41, 0x49, 0x93, 0x33,
	0xb2, 0x06, 0x8b, 0x0e, 0x73, 0x59, 0x92, 0xbb, 0xc5, 0x84, 0x5f, 0xf5, 0x9c, 0xda, 0xcc, 0x13,
	0xc6, 0x16, 0x60, 0xaf, 0x22, 0x05, 0x6d, 0x7d, 0x07, 0x2a, 0xd4, 0xb3, 0xc4, 0xe2, 0x22, 0x2e,
	0x96, 0xa9, 0x67, 0x61, 0x9a, 0x7e, 0xad, 0xc0, 0xfd, 0x1b, 0x14, 0x8e, 0x02, 0xf2, 0x23, 0xa8,
	0x4a, 0x8d, 0xa3, 0x40, 0xaa, 0x5c, 0x1f, 0xa9, 0x1c, 0x05, 0x5a, 0xe5, 0x5c, 0x8e, 0x48, 0x0f,
	0x56, 0x4d, 0xb1, 0x5f, 0x0f, 0x51, 0x80, 0x70, 0x6e, 0x01, 0x9d, 0xfb, 0xc1, 0x14, 0x34, 0xe5,
	0x4f, 0x5c, 0x31, 0xb3, 0x53, 0xee, 0xec, 0xf6, 0x5f, 0x14, 0x68, 0x67, 0x55, 0xd4, 0xa8, 0x49,
	0xbd, 0x38, 0x17, 0x44, 0xf3, 0x58, 0x36, 0xb5, 0x60, 0x21, 0x6b, 0xc1, 0x03, 0x58, 0x31, 0x07,
	0xa1, 0x6e, 0x98, 0x31, 0x7b, 0xc5, 0xe2, 0xa1, 0x1e, 0x0f, 0x83, 0x29, 0xc1, 0xd6, 0x1d, 0x84,
	0x1d, 0xc9, 0xd5, 0x1b, 0x06, 0x54, 0x5b, 0x36, 0xf3, 0x84, 0xf6, 0x9f, 0x14, 0x78, 0xff, 0x3b,
	0xf5, 0x9d, 0xdb, 0xb0, 0x97, 0x62, 0xff, 0x1c, 0x86, 0xcd, 0x9f, 0xb8, 0x72, 0x99, 0x9d, 0xa2,
	0x61, 0x7f, 0x55, 0xcc, 0x83, 0xf5, 0x8d, 0x4d, 0xfa, 0x3d, 0x81, 0x95, 0x67, 0xfd, 0xd0, 0x08,
	0xc5, 0x6b, 0xa3, 0xa2, 0xe1, 0x98, 0xd3, 0x02, 0xc3, 0x4e, 0x0a, 0x31, 0x8e, 0x27, 0x3b, 0xb3,
	0xf2, 0x26, 0xce, 0x4c, 0x53, 0x68, 0x75, 0xde, 0x14, 0x0a, 0x33, 0xa6, 0xd0, 0xf6, 0x6f, 0x0b,
	0xf9, 0x08, 0xfc, 0xe1, 0x01, 0x25, 0xb5, 0x71, 0x31, 0x63, 0xe3, 0x5f, 0xc0, 0xfa, 0x84, 0x93,
	0xf4, 0x57, 0xdb, 0xad, 0x12, 0x1e, 0xf6, 0x78, 0x96, 0xc3, 0xce, 0xb6, 0xb5, 0xd5, 0x6b, 0xc7,
	0x9d, 0x6d, 0xb7, 0x5f, 0x17, 0x60, 0x2d, 0xdd, 0xa1, 0x51, 0xf3, 0xc2, 0x08, 0x6d, 0x3a, 0x27,
	0x22, 0x65, 0xe5, 0x2f, 0xe4, 0x2a, 0xff, 0x7d, 0x00, 0xf3, 0xc2, 0xf0, 0x3c, 0xea, 0xf0, 0x92,
	0x25, 0x60, 0x59, 0x95, 0x94, 0x03, 0x8b, 0xbc, 0x07, 0xf5, 0x64, 0x19, 0x31, 0x23, 0xc0, 0x59,
	0x93, 0x34, 0x04, 0xc4, 0xe7, 0x50, 0x09, 0x1c, 0x23, 0xe6, 0xf5, 0x42, 0x96, 0xc9, 0x07, 0x53,
	0x6e, 0x7a, 0x2c, 0xd9, 0xb4, 0x74, 0xc3, 0x64, 0x60, 0x2e, 0xbd, 0x51, 0x96, 0x89, 0xe1, 0xd6,
	0x04, 0x0b, 0xcd, 0x87, 0x96, 0x69, 0x56, 0x6a, 0x41, 0xf9, 0xdc, 0x70, 0x0c, 0x4f, 0xbe, 0x37,
	0x4a, 0x5a, 0x32, 0x6d, 0xff, 0xbd, 0x08, 0x2b, 0xa3, 0xd7, 0x06, 0x1f, 0xfc, 0x30, 0xbc, 0x32,
	0x7a, 0x20, 0x2d, 0xce, 0xf3, 0x40, 0xca, 0x3a, 0x73, 0x69, 0x5e, 0x67, 0x26, 0x91, 0x5e, 0x9e,
	0xf5, 0xb1, 0xf4, 0x16, 0xd3, 0xd2, 0x4f, 0x01, 0x5c, 0x3f, 0x0c, 0x2e, 0xf4, 0x59, 0x93, 0x53,
	0x15, 0xd9, 0xf1, 0xe5, 0xfa, 0xaf, 0x22, 0x90, 0x71, 0x1f, 0xce, 0x87, 0x9b, 0xf7, 0xa1, 0x11,
	0x52, 0xd7, 0x60, 0x9e, 0xce, 0x7d, 0x60, 0x52, 0xe9, 0xce, 0xba, 0x20, 0x76, 0x91, 0x36, 0x2d,
	0x15, 0x15, 0xff, 0xbf, 0x54, 0xc4, 0xb1, 0x30, 0x08, 0x43, 0xea, 0xc5, 0xfa, 0x05, 0x8b, 0xa3,
	0x14, 0x0b, 0x82, 0xf6, 0x15, 0x8b, 0x23, 0x5e, 0x75, 0xa2, 0x80, 0x52, 0x4b, 0x56, 0x0f, 0x31,
	0xe1, 0x3a, 0xfb, 0xaf, 0x68, 0x68, 0x38, 0x8e, 0x2e, 0x56, 0xc5, 0x7b, 0xb5, 0x2e, 0x89, 0x27,
	0xc8, 0xd4, 0x82, 0x32, 0x8b, 0x76, 0x98, 0x67, 0xfb, 0xe8, 0xd5, 0x8a, 0x96, 0x4c, 0xc9, 0x23,
	0x68, 0xda, 0x03, 0x23, 0x34, 0xbc, 0x98, 0x52, 0x4b, 0x7f, 0x65, 0x38, 0xe8, 0xb8, 0x86, 0xd6,
	0x18, 0x51, 0xcf, 0x0c, 0x87, 0xfc, 0x04, 0xd6, 0x93, 0x53, 0xc6, 0xd8, 0xc5, 0x0b, 0x75, 0x4d,
	0xae, 0xee, 0xe7, 0x76, 0xfd, 0x0c, 0xca, 0xf2, 0xa6, 0x58, 0x36, 0x66, 0x4f, 0x9e, 0xc9, 0xb6,
	0xf6, 0xaf, 0x15, 0xb8, 0x9b, 0x2d, 0x22, 0x38, 0x3e, 0x89, 0x8d, 0x78, 0x10, 0xcd, 0x13, 0xa0,
	0x13, 0x11, 0x5a, 0x78, 0xa3, 0xfc, 0xf4, 0x1f, 0x05, 0x6e, 0x8f, 0x34, 0x4a, 0xaf, 0x8b, 0x5f,
	0x73, 0x49, 0xe4, 0x28, 0xb3, 0x46, 0x4e, 0x1e, 0xee, 0x85, 0x79, 0xe0, 0x3e, 0xc1, 0x73, 0xc5,
	0xf9, 0x3c, 0x57, 0x9a, 0xee, 0xb9, 0xf6, 0x9f, 0x8b, 0x70, 0x6f, 0xaa, 0xdd, 0xe7, 0x0b, 0xaa,
	0x71, 0x64, 0x17, 0xae, 0x23, 0xfb, 0x03, 0x68, 0xba, 0xfc, 0x13, 0x04, 0x43, 0x0f, 0x99, 0xc4,
	0x55, 0xea, 0xee, 0x50, 0x43, 0x22, 0x72, 0xb5, 0xa1, 0xe1, 0x0e, 0xf5, 0xd8, 0xb7, 0x8c, 0x61,
	0x2e, 0x46, 0xdc, 0x61, 0x8f, 0xd3, 0x90, 0x27, 0x6f, 0xd0, 0xc5, 0xb9, 0x0c, 0xca, 0xb5, 0xc0,
	0xbd, 0xe9, 0x33, 0x4d, 0x86, 0x12, 0x52, 0xf7, 0xe4, 0x5b, 0x2d, 0x8d, 0xc2, 0xf2, 0x8d, 0x51,
	0x58, 0x99, 0x10, 0x85, 0x5f, 0xc3, 0x5a, 0xc6, 0x05, 0xcc, 0xeb, 0xfb, 0x22, 0x75, 0x54, 0x31,
	0x75, 0x3c, 0x9a, 0xa2, 0x66, 0x1e, 0x63, 0x1a, 0xb1, 0x73, 0x73, 0x7c, 0xf0, 0xfe, 0x5e, 0xc9,
	0x14, 0x4d, 0xf1, 0xb1, 0xd3, 0x67, 0xf6, 0x3c, 0x01, 0x92, 0x2d, 0x18, 0x85, 0x79, 0x0b, 0xc6,
	0xcd, 0x65, 0xae, 0xfd, 0x8f, 0x62, 0xa6, 0x63, 0x96, 0xd1, 0x6e, 0x3e, 0x18, 0x6d, 0x80, 0x1a,
	0xca, 0xe7, 0x80, 0x8e, 0x4d, 0x8e, 0xb4, 0x93, 0xd0, 0x4c, 0xe8, 0xfb, 0xac, 0x1f, 0x1f, 0x58,
	0xe4, 0x63, 0x20, 0x79, 0xce, 0xf4, 0x9b, 0xa7, 0xa1, 0xa9, 0x59, 0x5e, 0xac, 0x38, 0x5b, 0xb0,
	0x9a, 0xe7, 0x0e, 0x42, 0x66, 0x26, 0xb5, 0x78, 0x25, 0xcb, 0x7e, 0xcc, 0x17, 0xae, 0x4b, 0xc7,
	0x7e, 0x8d, 0xe8, 0x22, 0xe6, 0xa4, 0x63, 0xdb, 0x6c, 0x13, 0x56, 0xc6, 0xa4, 0x33, 0x13, 0x61,
	0x55, 0xd5, 0x96, 0x73, 0xb2, 0x99, 0xc9, 0xcd, 0x28, 0xf0, 0x87, 0xe0, 0x16, 0xf0, 0x12, 0xf0,
	0x44, 0x68, 0x3f, 0x4a, 0xe0, 0x69, 0x0d, 0x42, 0x23, 0x66, 0xbe, 0x97, 0x64, 0x6a, 0xa4, 0xee,
	0x4a, 0x22, 0x79, 0x00, 0x35, 0xcb, 0x60, 0xce, 0x50, 0x17, 0x5f, 0x28, 0xb2, 0x1b, 0x87, 0xa4,
	0x67, 0xf8, 0x99, 0x72, 0x9b, 0xd7, 0x02, 0x3d, 0xba, 0xf0, 0x2f, 0x31, 0x29, 0x57, 0xb4, 0x25,
	0x16, 0x9d, 0x5c, 0xf8, 0x97, 0xe4, 0x21, 0xd4, 0xe5, 0x82, 0x6e, 0xfb, 0x8e, 0x85, 0x3d, 0x83,
	0x8a, 0x06, 0x62, 0x75, 0xdf, 0x77, 0xac, 0xf6, 0x7e, 0xfe, 0x45, 0x9f, 0x74, 0x07, 0xe6, 0x86,
	0x5b, 0xfb, 0x77, 0x05, 0x50, 0x73, 0xf9, 0xb0, 0xdb, 0xb7, 0xbf, 0xdf, 0xec, 0xf9, 0x19, 0xdc,
	0x49, 0x1d, 0x13, 0x84, 0x7e, 0xa0, 0x7b, 0x03, 0x57, 0xf7, 0x03, 0x6e, 0xc2, 0x08, 0x6b, 0x79,
	0x43, 0x5b, 0x4f, 0x18, 0x8e, 0x43, 0x3f, 0x78, 0x3e, 0x70, 0x8f, 0xc4, 0x2a, 0xf9, 0x04, 0x56,
	0xc5, 0x01, 0xa6, 0x1f, 0xc5, 0xe9, 0x66, 0x89, 0x18, 0x15, 0x97, 0xba, 0x7e, 0x14, 0xcb, 0x5d,
	0x1c, 0x60, 0x99, 0xa8, 0x4f, 0x41, 0x20, 0x10, 0xb3, 0x32, 0x5a, 0x92, 0x30, 0x68, 0xff, 0xbb,
	0x02, 0xef, 0xde, 0x64, 0xe5, 0xf9, 0xc2, 0xe6, 0xed, 0x55, 0x3e, 0x8e, 0xac, 0x90, 0x46, 0xfa,
	0x2b, 0x1a, 0x46, 0x1c, 0x7d, 0x22, 0xa0, 0x20, 0xa4, 0xd1, 0x99, 0xa0, 0x90, 0x7b, 0x50, 0xa5,
	0x5e, 0x1c, 0x0e, 0xf1, 0x7e, 0x25, 0xd1, 0xc6, 0x45, 0x82, 0xec, 0x12, 0x8b, 0xc5, 0x98, 0xc5,
	0x4e, 0x12, 0x30, 0x80, 0xa4, 0x1e, 0xa7, 0x90, 0x8f, 0x60, 0x25, 0xd5, 0x32, 0xed, 0x46, 0x89,
	0x50, 0x51, 0x93, 0x85, 0xc4, 0x16, 0x64, 0x1b, 0x6e, 0x5d, 0x63, 0xd6, 0x5d, 0xeb, 0xa9, 0xfc,
	0x5c, 0x5e, 0x1d, 0xdf, 0x70, 0x68, 0x3d, 0xe5, 0x1a, 0xc4, 0xf4, 0x2a, 0xd6, 0x4d, 0x34, 0xa4,
	0xec, 0x62, 0x03, 0x27, 0x09, 0xd3, 0x4e, 0x0e, 0xd6, 0xea, 0xe4, 0x60, 0x9d, 0x9c, 0x06, 0x60,
	0x86, 0x34, 0x80, 0x80, 0xe1, 0x92, 0x6b, 0x79, 0xc9, 0x1c, 0x2f, 0xe3, 0x92, 0x05, 0xb8, 0xb8,
	0xe4, 0x7a, 0x5e, 0x32, 0x82, 0x8b, 0x4b, 0xfe, 0x28, 0x23, 0x39, 0xa4, 0x2e, 0xf3, 0x2c, 0x1a,
	0xca, 0xfe, 0x77, 0xca, 0xac, 0x49, 0xfa, 0xb4, 0x5c, 0xd7, 0x9c, 0x96, 0xeb, 0x1e, 0x41, 0x73,
	0x64, 0xe5, 0x81, 0x43, 0x23, 0x6c, 0x8a, 0x57, 0xb5, 0x46, 0x6a, 0x5e, 0x4e, 0x14, 0x1a, 0xdb,
	0x03, 0xc7, 0x08, 0xb9, 0xc0, 0x5f, 0x52, 0x3d, 0xf0, 0x7d, 0x47, 0xf6, 0xc0, 0x55, 0xb9, 0x72,
	0xcc, 0x17, 0x8e, 0x7d, 0xdf, 0x21, 0x8f, 0x61, 0x99, 0xa7, 0x97, 0x2c, 0xeb, 0x8a, 0x90, 0xca,
	0xc9, 0x23, 0xbe, 0x07, 0x50, 0x13, 0xd1, 0x2d, 0x4e, 0x26, 0xc2, 0x5d, 0x48, 0x12, 0xc7, 0x6e,
	0x80, 0xea, 0x0c, 0xcc, 0x97, 0x7a, 0xe0, 0x33, 0x2f, 0x96, 0x5c, 0xab, 0xa2, 0x7b, 0xc9, 0xe9,
	0xc7, 0x9c, 0x2c, 0x38, 0xb7, 0x20, 0x05, 0x04, 0xa6, 0x36, 0xc9, 0xbc, 0x26, 0x42, 0x30, 0x59,
	0xe2, 0x29, 0x4e, 0xf0, 0xff, 0x18, 0x6e, 0x21, 0xdb, 0x35, 0xf1, 0xb7, 0x70, 0x07, 0xe1, 0x8b,
	0xcf, 0xf2, 0x47, 0xec, 0x40, 0x83, 0xe7, 0x24, 0xdd, 0xec, 0xdb, 0xa2, 0xa8, 0xaf, 0x63, 0x51,
	0x7f, 0xf7, 0x86, 0x2c, 0xd6, 0xed, 0xdb, 0x5a, 0xcd, 0x15, 0x03, 0xfc, 0x06, 0xd8, 0x00, 0x55,
	0xdc, 0x38, 0x63, 0x9a, 0xdb, 0xe2, 0x42, 0x48, 0x1f, 0xd9, 0xe6, 0x53, 0x58, 0x17, 0x9c, 0xd7,
	0x34, 0x6c, 0x09, 0xfc, 0xe3, 0x6a, 0x5e, 0xc5, 0xf6, 0x3e, 0x34, 0x39, 0x6c, 0xf8, 0x2b, 0xc6,
	0x61, 0x1e, 0x8d, 0x22, 0xa2, 0x42, 0x91, 0x67, 0x2e, 0xf1, 0xfb, 0x81, 0x0f, 0xf9, 0x3b, 0xa6,
	0x2f, 0x3b, 0x58, 0x51, 0x6c, 0xb8, 0x01, 0xa6, 0x8a, 0xa2, 0x56, 0xef, 0x8b, 0x26, 0x16, 0xd2,
	0xda, 0x7f, 0x54, 0x80, 0xec, 0xd3, 0xf8, 0x34, 0xa2, 0x21, 0x17, 0xc8, 0x75, 0x9f, 0xe7, 0xad,
	0x71, 0x07, 0x2a, 0x91, 0x1f, 0xc6, 0xba, 0x11, 0x99, 0x28, 0xbe, 0xa2, 0x95, 0xf9, 0xbc, 0x13,
	0x99, 0x6f, 0xb3, 0x5b, 0xf9, 0x07, 0x05, 0x56, 0xaf, 0x29, 0x39, 0x5f, 0xee, 0xfc, 0x0c, 0xaa,
	0x18, 0x80, 0x99, 0x56, 0xd3, 0x58, 0x27, 0x3f, 0x6f, 0x4f, 0xad, 0x12, 0xc8, 0x93, 0xf8, 0x5b,
	0x32, 0xf6, 0x63, 0xc3, 0x19, 0x95, 0x07, 0xf9, 0xa2, 0x45, 0xaa, 0x2c, 0x0d, 0xed, 0x5d, 0xb8,
	0x2b, 0x55, 0xdc, 0xbb, 0x0a, 0x58, 0x28, 0xa2, 0x5a, 0x36, 0xde, 0x67, 0x2f, 0xa6, 0xbf, 0x51,
	0xe0, 0xde, 0x54, 0x31, 0xf3, 0xdd, 0xf8, 0x01, 0xd4, 0x3c, 0x5e, 0xa5, 0x44, 0xf7, 0x5f, 0x7a,
	0x07, 0x38, 0x49, 0xc8, 0xe3, 0x19, 0x41, 0xfe, 0x30, 0x30, 0x7d, 0x2f, 0xa6, 0xf2, 0x5f, 0x6b,
	0x55, 0x93, 0xbf, 0x2d, 0xba, 0x82, 0xb8, 0xf9, 0x21, 0x2c, 0x8f, 0xb5, 0x26, 0x08, 0xc0, 0xd2,
	0xa1, 0xe1, 0x0d, 0x0c, 0x47, 0x5d, 0x20, 0x15, 0x28, 0x75, 0x4e, 0x7b, 0x47, 0xaa, 0xb2, 0xf9,
	0x18, 0x1a, 0xb9, 0x12, 0x9d, 0xfb, 0xe5, 0x54, 0x85, 0xc5, 0xc3, 0x23, 0xed, 0xf8, 0x2b, 0x55,
	0xd9, 0x7c, 0x92, 0xe1, 0xe3, 0xb1, 0x43, 0x96, 0xa1, 0x26, 0xf8, 0xf4, 0xc3, 0xa3, 0xdd, 0x3d,
	0x75, 0x81, 0x34, 0xa0, 0xba, 0x7f, 0xf4, 0x6c, 0x57, 0x4c, 0x95, 0xcd, 0x2f, 0x32, 0xfd, 0x9c,
	0xe4, 0xc9, 0x4a, 0xd6, 0x40, 0x3d, 0xf5, 0x5e, 0x7a, 0xfe, 0xa5, 0x97, 0xd2, 0xd4, 0x05, 0x52,
	0x83, 0x72, 0xc7, 0xb3, 0x42, 0x9f, 0x59, 0xaa, 0x42, 0xca, 0x50, 0x64, 0x47, 0x27, 0x6a, 0x61,
	0xf3, 0x0b, 0xa8, 0xa7, 0x02, 0x3a, 0x41, 0xc0, 0x0f, 0x4c, 0xf6, 0x76, 0x82, 0x40, 0x5d, 0x20,
	0x4b, 0x50, 0xe8, 0xf5, 0x54, 0x85, 0x6f, 0xff, 0xea, 0xb4, 0xf3, 0xfc, 0xc5, 0xd1, 0xa9, 0x5a,
	0xe0, 0x2a, 0x7f, 0xd3, 0x39, 0x78, 0xd1, 0x51, 0x8b, 0x9b, 0x7d, 0x58, 0x1e, 0x03, 0x29, 0x79,
	0x0f, 0xee, 0x77, 0x4f, 0x35, 0xbd, 0xd3, 0xed, 0x1d, 0x9c, 0x1d, 0xf4, 0x5e, 0xe8, 0xbd, 0x17,
	0xc7, 0x7b, 0xfa, 0xe9, 0xf3, 0x93, 0xe3, 0xbd, 0xee, 0xc1, 0x97, 0x07, 0x7b, 0xbb, 0xea, 0x02,
	0xb9, 0x0d, 0xab, 0xd7, 0x59, 0x3a, 0xaa, 0x32, 0x79, 0x61, 0x47, 0x2d, 0xec, 0xec, 0x40, 0xcb,
	0xf4, 0xdd, 0xad, 0x21, 0x1b, 0xfa, 0x03, 0xee, 0x56, 0x9e, 0x51, 0x1c, 0xf1, 0x3f, 0xfd, 0x9b,
	0xc7, 0xb6, 0xef, 0x18, 0x9e, 0xbd, 0xf5, 0x74, 0x3b, 0x8e, 0xb7, 0x4c, 0xdf, 0x7d, 0x82, 0x64,
	0xd3, 0x77, 0x9e, 0x18, 0x41, 0xf0, 0x24, 0xfd, 0x43, 0x7f, 0xbe, 0x84, 0xf4, 0x4f, 0xff, 0x17,
	0x00, 0x00, 0xff, 0xff, 0x12, 0x88, 0xdd, 0xb0, 0xb5, 0x1f, 0x00, 0x00,
}
