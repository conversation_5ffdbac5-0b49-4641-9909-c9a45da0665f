// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: game_card_.proto

/*
	Package game_card is a generated protocol buffer package.

	It is generated from these files:
		game_card_.proto

	It has these top-level messages:
		ModifyGameCardOpt
		ModifyGameCardInChannelReq
		ModifyGameCardInChannelResp
		GameCardLevelConf
		GameCardOptWithPropConf
		GameCardInputConf
		GameCardOptConf
		GameCardConfInfo
		NickNameType
		UGameInfo
		GetAllGameCardConfReq
		GetAllGameCardConfResp
		GameScreenshot
		GameCardOpt
		GameCardInfo
		GameCardInputVal
		GameNickNameInfo
		GetGameCardReq
		GetGameCardResp
		CreateGameCardReq
		CreateGameCardResp
		SetGameCardReq
		SetGameCardResp
		DeleteGameCardReq
		DeleteGameCardResp
		CreateGameCardInRegisterReq
		CreateGameCardInRegisterResp
		GetGameCardByTabReq
		GetGameCardByTabResp
		GetGameCardConfReq
		GetGameCardConfResp
		BatchCreateGameCardReq
		BatchCreateGameCardResp
		GetGameCardConfByTabIdsReq
		GetGameCardConfByTabIdsResp
		NewGameCardConfInfo
*/
package game_card

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import ga "golang.52tt.com/protocol/app"

import io "io"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 游戏卡改版重构----------------------------------------------------------------------------------------------
type GameCardType int32

const (
	GameCardType_CARD_TYPE_INVALID GameCardType = 0
	GameCardType_CARD_TYPE_GAME    GameCardType = 1
	GameCardType_CARD_TYPE_MUSIC   GameCardType = 2
)

var GameCardType_name = map[int32]string{
	0: "CARD_TYPE_INVALID",
	1: "CARD_TYPE_GAME",
	2: "CARD_TYPE_MUSIC",
}
var GameCardType_value = map[string]int32{
	"CARD_TYPE_INVALID": 0,
	"CARD_TYPE_GAME":    1,
	"CARD_TYPE_MUSIC":   2,
}

func (x GameCardType) Enum() *GameCardType {
	p := new(GameCardType)
	*p = x
	return p
}
func (x GameCardType) String() string {
	return proto.EnumName(GameCardType_name, int32(x))
}
func (x *GameCardType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GameCardType_value, data, "GameCardType")
	if err != nil {
		return err
	}
	*x = GameCardType(value)
	return nil
}
func (GameCardType) EnumDescriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{0} }

type GameCardOptType int32

const (
	GameCardOptType_GAME_CARD_OPT_INVALID   GameCardOptType = 0
	GameCardOptType_GAME_CARD_OPT_COMMON    GameCardOptType = 1
	GameCardOptType_GAME_CARD_OPT_LEVEL     GameCardOptType = 2
	GameCardOptType_GAME_CARD_OPT_WITH_PROP GameCardOptType = 3
	GameCardOptType_GAME_CARD_OPT_INPUT     GameCardOptType = 4
)

var GameCardOptType_name = map[int32]string{
	0: "GAME_CARD_OPT_INVALID",
	1: "GAME_CARD_OPT_COMMON",
	2: "GAME_CARD_OPT_LEVEL",
	3: "GAME_CARD_OPT_WITH_PROP",
	4: "GAME_CARD_OPT_INPUT",
}
var GameCardOptType_value = map[string]int32{
	"GAME_CARD_OPT_INVALID":   0,
	"GAME_CARD_OPT_COMMON":    1,
	"GAME_CARD_OPT_LEVEL":     2,
	"GAME_CARD_OPT_WITH_PROP": 3,
	"GAME_CARD_OPT_INPUT":     4,
}

func (x GameCardOptType) Enum() *GameCardOptType {
	p := new(GameCardOptType)
	*p = x
	return p
}
func (x GameCardOptType) String() string {
	return proto.EnumName(GameCardOptType_name, int32(x))
}
func (x *GameCardOptType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GameCardOptType_value, data, "GameCardOptType")
	if err != nil {
		return err
	}
	*x = GameCardOptType(value)
	return nil
}
func (GameCardOptType) EnumDescriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{1} }

type RegisterJumpPage int32

const (
	RegisterJumpPage_HomePage   RegisterJumpPage = 0
	RegisterJumpPage_QuickMatch RegisterJumpPage = 1
)

var RegisterJumpPage_name = map[int32]string{
	0: "HomePage",
	1: "QuickMatch",
}
var RegisterJumpPage_value = map[string]int32{
	"HomePage":   0,
	"QuickMatch": 1,
}

func (x RegisterJumpPage) Enum() *RegisterJumpPage {
	p := new(RegisterJumpPage)
	*p = x
	return p
}
func (x RegisterJumpPage) String() string {
	return proto.EnumName(RegisterJumpPage_name, int32(x))
}
func (x *RegisterJumpPage) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RegisterJumpPage_value, data, "RegisterJumpPage")
	if err != nil {
		return err
	}
	*x = RegisterJumpPage(value)
	return nil
}
func (RegisterJumpPage) EnumDescriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{2} }

type EScreenShotAuditStatus int32

const (
	EScreenShotAuditStatus_SCREEN_SHOT_AUDITING     EScreenShotAuditStatus = 0
	EScreenShotAuditStatus_SCREEN_SHOT_AUDIT_REJECT EScreenShotAuditStatus = 1
	EScreenShotAuditStatus_SCREEN_SHOT_AUDIT_PASS   EScreenShotAuditStatus = 2
)

var EScreenShotAuditStatus_name = map[int32]string{
	0: "SCREEN_SHOT_AUDITING",
	1: "SCREEN_SHOT_AUDIT_REJECT",
	2: "SCREEN_SHOT_AUDIT_PASS",
}
var EScreenShotAuditStatus_value = map[string]int32{
	"SCREEN_SHOT_AUDITING":     0,
	"SCREEN_SHOT_AUDIT_REJECT": 1,
	"SCREEN_SHOT_AUDIT_PASS":   2,
}

func (x EScreenShotAuditStatus) Enum() *EScreenShotAuditStatus {
	p := new(EScreenShotAuditStatus)
	*p = x
	return p
}
func (x EScreenShotAuditStatus) String() string {
	return proto.EnumName(EScreenShotAuditStatus_name, int32(x))
}
func (x *EScreenShotAuditStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EScreenShotAuditStatus_value, data, "EScreenShotAuditStatus")
	if err != nil {
		return err
	}
	*x = EScreenShotAuditStatus(value)
	return nil
}
func (EScreenShotAuditStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{3}
}

type GameCardFilterEntrance int32

const (
	GameCardFilterEntrance_UNKNOWN_ENTRANCE GameCardFilterEntrance = 0
	GameCardFilterEntrance_HOME_PAGE        GameCardFilterEntrance = 1
	GameCardFilterEntrance_GAME_ZONE        GameCardFilterEntrance = 2
	GameCardFilterEntrance_MINIGAME_ZONE    GameCardFilterEntrance = 3
)

var GameCardFilterEntrance_name = map[int32]string{
	0: "UNKNOWN_ENTRANCE",
	1: "HOME_PAGE",
	2: "GAME_ZONE",
	3: "MINIGAME_ZONE",
}
var GameCardFilterEntrance_value = map[string]int32{
	"UNKNOWN_ENTRANCE": 0,
	"HOME_PAGE":        1,
	"GAME_ZONE":        2,
	"MINIGAME_ZONE":    3,
}

func (x GameCardFilterEntrance) Enum() *GameCardFilterEntrance {
	p := new(GameCardFilterEntrance)
	*p = x
	return p
}
func (x GameCardFilterEntrance) String() string {
	return proto.EnumName(GameCardFilterEntrance_name, int32(x))
}
func (x *GameCardFilterEntrance) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GameCardFilterEntrance_value, data, "GameCardFilterEntrance")
	if err != nil {
		return err
	}
	*x = GameCardFilterEntrance(value)
	return nil
}
func (GameCardFilterEntrance) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{4}
}

type ModifyGameCardOpt struct {
	OptName   string   `protobuf:"bytes,1,req,name=opt_name,json=optName" json:"opt_name"`
	OptId     uint32   `protobuf:"varint,2,req,name=opt_id,json=optId" json:"opt_id"`
	ValueList []string `protobuf:"bytes,3,rep,name=value_list,json=valueList" json:"value_list,omitempty"`
}

func (m *ModifyGameCardOpt) Reset()                    { *m = ModifyGameCardOpt{} }
func (m *ModifyGameCardOpt) String() string            { return proto.CompactTextString(m) }
func (*ModifyGameCardOpt) ProtoMessage()               {}
func (*ModifyGameCardOpt) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{0} }

func (m *ModifyGameCardOpt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *ModifyGameCardOpt) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *ModifyGameCardOpt) GetValueList() []string {
	if m != nil {
		return m.ValueList
	}
	return nil
}

// ugc房间里修改游戏卡
type ModifyGameCardInChannelReq struct {
	BaseReq      *ga.BaseReq          `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameCardId   uint32               `protobuf:"varint,2,req,name=game_card_id,json=gameCardId" json:"game_card_id"`
	GameCardName string               `protobuf:"bytes,3,req,name=game_card_name,json=gameCardName" json:"game_card_name"`
	UGameId      uint32               `protobuf:"varint,4,req,name=u_game_id,json=uGameId" json:"u_game_id"`
	OptList      []*ModifyGameCardOpt `protobuf:"bytes,5,rep,name=opt_list,json=optList" json:"opt_list,omitempty"`
}

func (m *ModifyGameCardInChannelReq) Reset()         { *m = ModifyGameCardInChannelReq{} }
func (m *ModifyGameCardInChannelReq) String() string { return proto.CompactTextString(m) }
func (*ModifyGameCardInChannelReq) ProtoMessage()    {}
func (*ModifyGameCardInChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{1}
}

func (m *ModifyGameCardInChannelReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ModifyGameCardInChannelReq) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

func (m *ModifyGameCardInChannelReq) GetGameCardName() string {
	if m != nil {
		return m.GameCardName
	}
	return ""
}

func (m *ModifyGameCardInChannelReq) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *ModifyGameCardInChannelReq) GetOptList() []*ModifyGameCardOpt {
	if m != nil {
		return m.OptList
	}
	return nil
}

type ModifyGameCardInChannelResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ModifyGameCardInChannelResp) Reset()         { *m = ModifyGameCardInChannelResp{} }
func (m *ModifyGameCardInChannelResp) String() string { return proto.CompactTextString(m) }
func (*ModifyGameCardInChannelResp) ProtoMessage()    {}
func (*ModifyGameCardInChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{2}
}

func (m *ModifyGameCardInChannelResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 段位选项配置
type GameCardLevelConf struct {
	LevelName      string `protobuf:"bytes,1,req,name=level_name,json=levelName" json:"level_name"`
	LevelImgUrl    string `protobuf:"bytes,2,req,name=level_img_url,json=levelImgUrl" json:"level_img_url"`
	LevelImgUrlMic string `protobuf:"bytes,3,req,name=level_img_url_mic,json=levelImgUrlMic" json:"level_img_url_mic"`
}

func (m *GameCardLevelConf) Reset()                    { *m = GameCardLevelConf{} }
func (m *GameCardLevelConf) String() string            { return proto.CompactTextString(m) }
func (*GameCardLevelConf) ProtoMessage()               {}
func (*GameCardLevelConf) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{3} }

func (m *GameCardLevelConf) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *GameCardLevelConf) GetLevelImgUrl() string {
	if m != nil {
		return m.LevelImgUrl
	}
	return ""
}

func (m *GameCardLevelConf) GetLevelImgUrlMic() string {
	if m != nil {
		return m.LevelImgUrlMic
	}
	return ""
}

// 带属性的选项配置
type GameCardOptWithPropConf struct {
	Prop      string   `protobuf:"bytes,1,req,name=prop" json:"prop"`
	ValueList []string `protobuf:"bytes,2,rep,name=value_list,json=valueList" json:"value_list,omitempty"`
}

func (m *GameCardOptWithPropConf) Reset()         { *m = GameCardOptWithPropConf{} }
func (m *GameCardOptWithPropConf) String() string { return proto.CompactTextString(m) }
func (*GameCardOptWithPropConf) ProtoMessage()    {}
func (*GameCardOptWithPropConf) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{4}
}

func (m *GameCardOptWithPropConf) GetProp() string {
	if m != nil {
		return m.Prop
	}
	return ""
}

func (m *GameCardOptWithPropConf) GetValueList() []string {
	if m != nil {
		return m.ValueList
	}
	return nil
}

// 新增的数值类型的配置
type GameCardInputConf struct {
	Title  string `protobuf:"bytes,1,opt,name=title" json:"title"`
	MinNum uint32 `protobuf:"varint,2,opt,name=min_num,json=minNum" json:"min_num"`
	MaxNum uint32 `protobuf:"varint,3,opt,name=max_num,json=maxNum" json:"max_num"`
}

func (m *GameCardInputConf) Reset()                    { *m = GameCardInputConf{} }
func (m *GameCardInputConf) String() string            { return proto.CompactTextString(m) }
func (*GameCardInputConf) ProtoMessage()               {}
func (*GameCardInputConf) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{5} }

func (m *GameCardInputConf) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GameCardInputConf) GetMinNum() uint32 {
	if m != nil {
		return m.MinNum
	}
	return 0
}

func (m *GameCardInputConf) GetMaxNum() uint32 {
	if m != nil {
		return m.MaxNum
	}
	return 0
}

type GameCardOptConf struct {
	OptName              string                     `protobuf:"bytes,1,req,name=opt_name,json=optName" json:"opt_name"`
	OptId                uint32                     `protobuf:"varint,2,req,name=opt_id,json=optId" json:"opt_id"`
	MaxSetCnt            uint32                     `protobuf:"varint,3,req,name=max_set_cnt,json=maxSetCnt" json:"max_set_cnt"`
	IsMust               bool                       `protobuf:"varint,4,req,name=is_must,json=isMust" json:"is_must"`
	TextBoxStyle         uint32                     `protobuf:"varint,5,req,name=text_box_style,json=textBoxStyle" json:"text_box_style"`
	DisplayInChannel     bool                       `protobuf:"varint,6,req,name=display_in_channel,json=displayInChannel" json:"display_in_channel"`
	OptType              GameCardOptType            `protobuf:"varint,7,req,name=opt_type,json=optType,enum=ga.GameCardOptType" json:"opt_type"`
	OptConfValueList     []string                   `protobuf:"bytes,8,rep,name=opt_conf_value_list,json=optConfValueList" json:"opt_conf_value_list,omitempty"`
	OptWithPropValueList []*GameCardOptWithPropConf `protobuf:"bytes,9,rep,name=opt_with_prop_value_list,json=optWithPropValueList" json:"opt_with_prop_value_list,omitempty"`
	LevelConfValueList   []*GameCardLevelConf       `protobuf:"bytes,10,rep,name=level_conf_value_list,json=levelConfValueList" json:"level_conf_value_list,omitempty"`
	InputConfValueList   []*GameCardInputConf       `protobuf:"bytes,11,rep,name=input_conf_value_list,json=inputConfValueList" json:"input_conf_value_list,omitempty"`
}

func (m *GameCardOptConf) Reset()                    { *m = GameCardOptConf{} }
func (m *GameCardOptConf) String() string            { return proto.CompactTextString(m) }
func (*GameCardOptConf) ProtoMessage()               {}
func (*GameCardOptConf) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{6} }

func (m *GameCardOptConf) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *GameCardOptConf) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *GameCardOptConf) GetMaxSetCnt() uint32 {
	if m != nil {
		return m.MaxSetCnt
	}
	return 0
}

func (m *GameCardOptConf) GetIsMust() bool {
	if m != nil {
		return m.IsMust
	}
	return false
}

func (m *GameCardOptConf) GetTextBoxStyle() uint32 {
	if m != nil {
		return m.TextBoxStyle
	}
	return 0
}

func (m *GameCardOptConf) GetDisplayInChannel() bool {
	if m != nil {
		return m.DisplayInChannel
	}
	return false
}

func (m *GameCardOptConf) GetOptType() GameCardOptType {
	if m != nil {
		return m.OptType
	}
	return GameCardOptType_GAME_CARD_OPT_INVALID
}

func (m *GameCardOptConf) GetOptConfValueList() []string {
	if m != nil {
		return m.OptConfValueList
	}
	return nil
}

func (m *GameCardOptConf) GetOptWithPropValueList() []*GameCardOptWithPropConf {
	if m != nil {
		return m.OptWithPropValueList
	}
	return nil
}

func (m *GameCardOptConf) GetLevelConfValueList() []*GameCardLevelConf {
	if m != nil {
		return m.LevelConfValueList
	}
	return nil
}

func (m *GameCardOptConf) GetInputConfValueList() []*GameCardInputConf {
	if m != nil {
		return m.InputConfValueList
	}
	return nil
}

type GameCardConfInfo struct {
	GameCardId             uint32             `protobuf:"varint,1,req,name=game_card_id,json=gameCardId" json:"game_card_id"`
	GameCardName           string             `protobuf:"bytes,2,req,name=game_card_name,json=gameCardName" json:"game_card_name"`
	UGameId                uint32             `protobuf:"varint,3,req,name=u_game_id,json=uGameId" json:"u_game_id"`
	HasNickName            bool               `protobuf:"varint,4,req,name=has_nick_name,json=hasNickName" json:"has_nick_name"`
	HasScreenshot          bool               `protobuf:"varint,5,req,name=has_screenshot,json=hasScreenshot" json:"has_screenshot"`
	GameThumbImgUrl        string             `protobuf:"bytes,6,req,name=game_thumb_img_url,json=gameThumbImgUrl" json:"game_thumb_img_url"`
	GameBackImgUrl         string             `protobuf:"bytes,7,req,name=game_back_img_url,json=gameBackImgUrl" json:"game_back_img_url"`
	GameIconImgUrl         string             `protobuf:"bytes,8,req,name=game_icon_img_url,json=gameIconImgUrl" json:"game_icon_img_url"`
	GameCornerMarkImgUrl   string             `protobuf:"bytes,9,req,name=game_corner_mark_img_url,json=gameCornerMarkImgUrl" json:"game_corner_mark_img_url"`
	GameBackImgMiniUrl     string             `protobuf:"bytes,10,req,name=game_back_img_mini_url,json=gameBackImgMiniUrl" json:"game_back_img_mini_url"`
	DefaultGameLevelImgUrl string             `protobuf:"bytes,11,req,name=default_game_level_img_url,json=defaultGameLevelImgUrl" json:"default_game_level_img_url"`
	GameBackColorNum       uint32             `protobuf:"varint,12,req,name=game_back_color_num,json=gameBackColorNum" json:"game_back_color_num"`
	OptConfList            []*GameCardOptConf `protobuf:"bytes,13,rep,name=opt_conf_list,json=optConfList" json:"opt_conf_list,omitempty"`
	CardType               uint32             `protobuf:"varint,14,req,name=card_type,json=cardType" json:"card_type"`
	RegisterJumpPage       RegisterJumpPage   `protobuf:"varint,15,req,name=register_jump_page,json=registerJumpPage,enum=ga.RegisterJumpPage" json:"register_jump_page"`
	ShowTeamNum            bool               `protobuf:"varint,16,opt,name=show_team_num,json=showTeamNum" json:"show_team_num"`
	ShowTeamText           string             `protobuf:"bytes,17,opt,name=show_team_text,json=showTeamText" json:"show_team_text"`
	Accounts               []string           `protobuf:"bytes,18,rep,name=accounts" json:"accounts,omitempty"`
	UGameInfo              []*UGameInfo       `protobuf:"bytes,19,rep,name=u_game_info,json=uGameInfo" json:"u_game_info,omitempty"`
	NicknameTypeList       []*NickNameType    `protobuf:"bytes,20,rep,name=nickname_type_list,json=nicknameTypeList" json:"nickname_type_list,omitempty"`
	HasQuickMatchEntrance  bool               `protobuf:"varint,21,req,name=has_quick_match_entrance,json=hasQuickMatchEntrance" json:"has_quick_match_entrance"`
}

func (m *GameCardConfInfo) Reset()                    { *m = GameCardConfInfo{} }
func (m *GameCardConfInfo) String() string            { return proto.CompactTextString(m) }
func (*GameCardConfInfo) ProtoMessage()               {}
func (*GameCardConfInfo) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{7} }

func (m *GameCardConfInfo) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

func (m *GameCardConfInfo) GetGameCardName() string {
	if m != nil {
		return m.GameCardName
	}
	return ""
}

func (m *GameCardConfInfo) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *GameCardConfInfo) GetHasNickName() bool {
	if m != nil {
		return m.HasNickName
	}
	return false
}

func (m *GameCardConfInfo) GetHasScreenshot() bool {
	if m != nil {
		return m.HasScreenshot
	}
	return false
}

func (m *GameCardConfInfo) GetGameThumbImgUrl() string {
	if m != nil {
		return m.GameThumbImgUrl
	}
	return ""
}

func (m *GameCardConfInfo) GetGameBackImgUrl() string {
	if m != nil {
		return m.GameBackImgUrl
	}
	return ""
}

func (m *GameCardConfInfo) GetGameIconImgUrl() string {
	if m != nil {
		return m.GameIconImgUrl
	}
	return ""
}

func (m *GameCardConfInfo) GetGameCornerMarkImgUrl() string {
	if m != nil {
		return m.GameCornerMarkImgUrl
	}
	return ""
}

func (m *GameCardConfInfo) GetGameBackImgMiniUrl() string {
	if m != nil {
		return m.GameBackImgMiniUrl
	}
	return ""
}

func (m *GameCardConfInfo) GetDefaultGameLevelImgUrl() string {
	if m != nil {
		return m.DefaultGameLevelImgUrl
	}
	return ""
}

func (m *GameCardConfInfo) GetGameBackColorNum() uint32 {
	if m != nil {
		return m.GameBackColorNum
	}
	return 0
}

func (m *GameCardConfInfo) GetOptConfList() []*GameCardOptConf {
	if m != nil {
		return m.OptConfList
	}
	return nil
}

func (m *GameCardConfInfo) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *GameCardConfInfo) GetRegisterJumpPage() RegisterJumpPage {
	if m != nil {
		return m.RegisterJumpPage
	}
	return RegisterJumpPage_HomePage
}

func (m *GameCardConfInfo) GetShowTeamNum() bool {
	if m != nil {
		return m.ShowTeamNum
	}
	return false
}

func (m *GameCardConfInfo) GetShowTeamText() string {
	if m != nil {
		return m.ShowTeamText
	}
	return ""
}

func (m *GameCardConfInfo) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *GameCardConfInfo) GetUGameInfo() []*UGameInfo {
	if m != nil {
		return m.UGameInfo
	}
	return nil
}

func (m *GameCardConfInfo) GetNicknameTypeList() []*NickNameType {
	if m != nil {
		return m.NicknameTypeList
	}
	return nil
}

func (m *GameCardConfInfo) GetHasQuickMatchEntrance() bool {
	if m != nil {
		return m.HasQuickMatchEntrance
	}
	return false
}

type NickNameType struct {
	TypeId   uint32 `protobuf:"varint,1,opt,name=type_id,json=typeId" json:"type_id"`
	TypeName string `protobuf:"bytes,2,opt,name=type_name,json=typeName" json:"type_name"`
}

func (m *NickNameType) Reset()                    { *m = NickNameType{} }
func (m *NickNameType) String() string            { return proto.CompactTextString(m) }
func (*NickNameType) ProtoMessage()               {}
func (*NickNameType) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{8} }

func (m *NickNameType) GetTypeId() uint32 {
	if m != nil {
		return m.TypeId
	}
	return 0
}

func (m *NickNameType) GetTypeName() string {
	if m != nil {
		return m.TypeName
	}
	return ""
}

type UGameInfo struct {
	UGameId   uint32 `protobuf:"varint,1,opt,name=u_game_id,json=uGameId" json:"u_game_id"`
	UGameName string `protobuf:"bytes,2,opt,name=u_game_name,json=uGameName" json:"u_game_name"`
}

func (m *UGameInfo) Reset()                    { *m = UGameInfo{} }
func (m *UGameInfo) String() string            { return proto.CompactTextString(m) }
func (*UGameInfo) ProtoMessage()               {}
func (*UGameInfo) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{9} }

func (m *UGameInfo) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *UGameInfo) GetUGameName() string {
	if m != nil {
		return m.UGameName
	}
	return ""
}

// 1.游戏卡配置
type GetAllGameCardConfReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	IsShowInputOpt bool        `protobuf:"varint,2,opt,name=is_show_input_opt,json=isShowInputOpt" json:"is_show_input_opt"`
}

func (m *GetAllGameCardConfReq) Reset()                    { *m = GetAllGameCardConfReq{} }
func (m *GetAllGameCardConfReq) String() string            { return proto.CompactTextString(m) }
func (*GetAllGameCardConfReq) ProtoMessage()               {}
func (*GetAllGameCardConfReq) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{10} }

func (m *GetAllGameCardConfReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAllGameCardConfReq) GetIsShowInputOpt() bool {
	if m != nil {
		return m.IsShowInputOpt
	}
	return false
}

type GetAllGameCardConfResp struct {
	BaseResp         *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GameCardConfList []*GameCardConfInfo `protobuf:"bytes,2,rep,name=game_card_conf_list,json=gameCardConfList" json:"game_card_conf_list,omitempty"`
	MaxGameCardNum   uint32              `protobuf:"varint,3,opt,name=max_game_card_num,json=maxGameCardNum" json:"max_game_card_num"`
}

func (m *GetAllGameCardConfResp) Reset()         { *m = GetAllGameCardConfResp{} }
func (m *GetAllGameCardConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllGameCardConfResp) ProtoMessage()    {}
func (*GetAllGameCardConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{11}
}

func (m *GetAllGameCardConfResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllGameCardConfResp) GetGameCardConfList() []*GameCardConfInfo {
	if m != nil {
		return m.GameCardConfList
	}
	return nil
}

func (m *GetAllGameCardConfResp) GetMaxGameCardNum() uint32 {
	if m != nil {
		return m.MaxGameCardNum
	}
	return 0
}

type GameScreenshot struct {
	AuditStatus uint32 `protobuf:"varint,1,req,name=audit_status,json=auditStatus" json:"audit_status"`
	ImgUrl      string `protobuf:"bytes,2,req,name=img_url,json=imgUrl" json:"img_url"`
	Index       uint32 `protobuf:"varint,3,req,name=index" json:"index"`
	NewObs      bool   `protobuf:"varint,4,opt,name=new_obs,json=newObs" json:"new_obs"`
}

func (m *GameScreenshot) Reset()                    { *m = GameScreenshot{} }
func (m *GameScreenshot) String() string            { return proto.CompactTextString(m) }
func (*GameScreenshot) ProtoMessage()               {}
func (*GameScreenshot) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{12} }

func (m *GameScreenshot) GetAuditStatus() uint32 {
	if m != nil {
		return m.AuditStatus
	}
	return 0
}

func (m *GameScreenshot) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *GameScreenshot) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GameScreenshot) GetNewObs() bool {
	if m != nil {
		return m.NewObs
	}
	return false
}

type GameCardOpt struct {
	OptName   string              `protobuf:"bytes,1,req,name=opt_name,json=optName" json:"opt_name"`
	OptId     uint32              `protobuf:"varint,2,req,name=opt_id,json=optId" json:"opt_id"`
	ValueList []string            `protobuf:"bytes,3,rep,name=value_list,json=valueList" json:"value_list,omitempty"`
	OptType   GameCardOptType     `protobuf:"varint,4,opt,name=opt_type,json=optType,enum=ga.GameCardOptType" json:"opt_type"`
	InputVal  []*GameCardInputVal `protobuf:"bytes,5,rep,name=input_val,json=inputVal" json:"input_val,omitempty"`
}

func (m *GameCardOpt) Reset()                    { *m = GameCardOpt{} }
func (m *GameCardOpt) String() string            { return proto.CompactTextString(m) }
func (*GameCardOpt) ProtoMessage()               {}
func (*GameCardOpt) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{13} }

func (m *GameCardOpt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *GameCardOpt) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *GameCardOpt) GetValueList() []string {
	if m != nil {
		return m.ValueList
	}
	return nil
}

func (m *GameCardOpt) GetOptType() GameCardOptType {
	if m != nil {
		return m.OptType
	}
	return GameCardOptType_GAME_CARD_OPT_INVALID
}

func (m *GameCardOpt) GetInputVal() []*GameCardInputVal {
	if m != nil {
		return m.InputVal
	}
	return nil
}

type GameCardInfo struct {
	GameCardId       uint32              `protobuf:"varint,1,req,name=game_card_id,json=gameCardId" json:"game_card_id"`
	GameCardName     string              `protobuf:"bytes,2,req,name=game_card_name,json=gameCardName" json:"game_card_name"`
	UGameId          uint32              `protobuf:"varint,3,req,name=u_game_id,json=uGameId" json:"u_game_id"`
	GameNickname     string              `protobuf:"bytes,4,req,name=game_nickname,json=gameNickname" json:"game_nickname"`
	ScreenshotList   []*GameScreenshot   `protobuf:"bytes,5,rep,name=screenshot_list,json=screenshotList" json:"screenshot_list,omitempty"`
	OptList          []*GameCardOpt      `protobuf:"bytes,6,rep,name=opt_list,json=optList" json:"opt_list,omitempty"`
	Conf             *GameCardConfInfo   `protobuf:"bytes,7,opt,name=conf" json:"conf,omitempty"`
	GameNicknameList []*GameNickNameInfo `protobuf:"bytes,8,rep,name=game_nickname_list,json=gameNicknameList" json:"game_nickname_list,omitempty"`
}

func (m *GameCardInfo) Reset()                    { *m = GameCardInfo{} }
func (m *GameCardInfo) String() string            { return proto.CompactTextString(m) }
func (*GameCardInfo) ProtoMessage()               {}
func (*GameCardInfo) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{14} }

func (m *GameCardInfo) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

func (m *GameCardInfo) GetGameCardName() string {
	if m != nil {
		return m.GameCardName
	}
	return ""
}

func (m *GameCardInfo) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *GameCardInfo) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *GameCardInfo) GetScreenshotList() []*GameScreenshot {
	if m != nil {
		return m.ScreenshotList
	}
	return nil
}

func (m *GameCardInfo) GetOptList() []*GameCardOpt {
	if m != nil {
		return m.OptList
	}
	return nil
}

func (m *GameCardInfo) GetConf() *GameCardConfInfo {
	if m != nil {
		return m.Conf
	}
	return nil
}

func (m *GameCardInfo) GetGameNicknameList() []*GameNickNameInfo {
	if m != nil {
		return m.GameNicknameList
	}
	return nil
}

type GameCardInputVal struct {
	ElemTitle string `protobuf:"bytes,1,opt,name=elem_title,json=elemTitle" json:"elem_title"`
	ElemVal   string `protobuf:"bytes,2,opt,name=elem_val,json=elemVal" json:"elem_val"`
}

func (m *GameCardInputVal) Reset()                    { *m = GameCardInputVal{} }
func (m *GameCardInputVal) String() string            { return proto.CompactTextString(m) }
func (*GameCardInputVal) ProtoMessage()               {}
func (*GameCardInputVal) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{15} }

func (m *GameCardInputVal) GetElemTitle() string {
	if m != nil {
		return m.ElemTitle
	}
	return ""
}

func (m *GameCardInputVal) GetElemVal() string {
	if m != nil {
		return m.ElemVal
	}
	return ""
}

type GameNickNameInfo struct {
	NicknameType *NickNameType `protobuf:"bytes,1,req,name=nickname_type,json=nicknameType" json:"nickname_type,omitempty"`
	Nickname     string        `protobuf:"bytes,2,req,name=nickname" json:"nickname"`
}

func (m *GameNickNameInfo) Reset()                    { *m = GameNickNameInfo{} }
func (m *GameNickNameInfo) String() string            { return proto.CompactTextString(m) }
func (*GameNickNameInfo) ProtoMessage()               {}
func (*GameNickNameInfo) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{16} }

func (m *GameNickNameInfo) GetNicknameType() *NickNameType {
	if m != nil {
		return m.NicknameType
	}
	return nil
}

func (m *GameNickNameInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

// 2.get
type GetGameCardReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetUid      uint32      `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
	IsShowInputOpt bool        `protobuf:"varint,3,opt,name=is_show_input_opt,json=isShowInputOpt" json:"is_show_input_opt"`
}

func (m *GetGameCardReq) Reset()                    { *m = GetGameCardReq{} }
func (m *GetGameCardReq) String() string            { return proto.CompactTextString(m) }
func (*GetGameCardReq) ProtoMessage()               {}
func (*GetGameCardReq) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{17} }

func (m *GetGameCardReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameCardReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetGameCardReq) GetIsShowInputOpt() bool {
	if m != nil {
		return m.IsShowInputOpt
	}
	return false
}

type GetGameCardResp struct {
	BaseResp     *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TargetUid    uint32          `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
	GameCardList []*GameCardInfo `protobuf:"bytes,3,rep,name=game_card_list,json=gameCardList" json:"game_card_list,omitempty"`
}

func (m *GetGameCardResp) Reset()                    { *m = GetGameCardResp{} }
func (m *GetGameCardResp) String() string            { return proto.CompactTextString(m) }
func (*GetGameCardResp) ProtoMessage()               {}
func (*GetGameCardResp) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{18} }

func (m *GetGameCardResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameCardResp) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetGameCardResp) GetGameCardList() []*GameCardInfo {
	if m != nil {
		return m.GameCardList
	}
	return nil
}

// 3.创建游戏卡
type CreateGameCardReq struct {
	BaseReq  *ga.BaseReq   `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameCard *GameCardInfo `protobuf:"bytes,2,req,name=game_card,json=gameCard" json:"game_card,omitempty"`
}

func (m *CreateGameCardReq) Reset()                    { *m = CreateGameCardReq{} }
func (m *CreateGameCardReq) String() string            { return proto.CompactTextString(m) }
func (*CreateGameCardReq) ProtoMessage()               {}
func (*CreateGameCardReq) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{19} }

func (m *CreateGameCardReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CreateGameCardReq) GetGameCard() *GameCardInfo {
	if m != nil {
		return m.GameCard
	}
	return nil
}

type CreateGameCardResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *CreateGameCardResp) Reset()                    { *m = CreateGameCardResp{} }
func (m *CreateGameCardResp) String() string            { return proto.CompactTextString(m) }
func (*CreateGameCardResp) ProtoMessage()               {}
func (*CreateGameCardResp) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{20} }

func (m *CreateGameCardResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 4.修改游戏卡,非覆盖式，只需填改了的那个字段(例如改了段位则只需填段位，改了昵称或截图则只需填昵称或截图),以及是否删除了昵称或截图
type SetGameCardReq struct {
	BaseReq     *ga.BaseReq   `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameCard    *GameCardInfo `protobuf:"bytes,2,req,name=game_card,json=gameCard" json:"game_card,omitempty"`
	DelGameNick bool          `protobuf:"varint,3,req,name=del_game_nick,json=delGameNick" json:"del_game_nick"`
}

func (m *SetGameCardReq) Reset()                    { *m = SetGameCardReq{} }
func (m *SetGameCardReq) String() string            { return proto.CompactTextString(m) }
func (*SetGameCardReq) ProtoMessage()               {}
func (*SetGameCardReq) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{21} }

func (m *SetGameCardReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetGameCardReq) GetGameCard() *GameCardInfo {
	if m != nil {
		return m.GameCard
	}
	return nil
}

func (m *SetGameCardReq) GetDelGameNick() bool {
	if m != nil {
		return m.DelGameNick
	}
	return false
}

type SetGameCardResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *SetGameCardResp) Reset()                    { *m = SetGameCardResp{} }
func (m *SetGameCardResp) String() string            { return proto.CompactTextString(m) }
func (*SetGameCardResp) ProtoMessage()               {}
func (*SetGameCardResp) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{22} }

func (m *SetGameCardResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 5.删除游戏卡
type DeleteGameCardReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameCardId uint32      `protobuf:"varint,2,req,name=game_card_id,json=gameCardId" json:"game_card_id"`
}

func (m *DeleteGameCardReq) Reset()                    { *m = DeleteGameCardReq{} }
func (m *DeleteGameCardReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteGameCardReq) ProtoMessage()               {}
func (*DeleteGameCardReq) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{23} }

func (m *DeleteGameCardReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DeleteGameCardReq) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

type DeleteGameCardResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *DeleteGameCardResp) Reset()                    { *m = DeleteGameCardResp{} }
func (m *DeleteGameCardResp) String() string            { return proto.CompactTextString(m) }
func (*DeleteGameCardResp) ProtoMessage()               {}
func (*DeleteGameCardResp) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{24} }

func (m *DeleteGameCardResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 6.注册阶段创建的游戏卡
type CreateGameCardInRegisterReq struct {
	BaseReq      *ga.BaseReq     `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameCardList []*GameCardInfo `protobuf:"bytes,2,rep,name=game_card_list,json=gameCardList" json:"game_card_list,omitempty"`
	BirthDay     string          `protobuf:"bytes,3,opt,name=birth_day,json=birthDay" json:"birth_day"`
}

func (m *CreateGameCardInRegisterReq) Reset()         { *m = CreateGameCardInRegisterReq{} }
func (m *CreateGameCardInRegisterReq) String() string { return proto.CompactTextString(m) }
func (*CreateGameCardInRegisterReq) ProtoMessage()    {}
func (*CreateGameCardInRegisterReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{25}
}

func (m *CreateGameCardInRegisterReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CreateGameCardInRegisterReq) GetGameCardList() []*GameCardInfo {
	if m != nil {
		return m.GameCardList
	}
	return nil
}

func (m *CreateGameCardInRegisterReq) GetBirthDay() string {
	if m != nil {
		return m.BirthDay
	}
	return ""
}

type CreateGameCardInRegisterResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *CreateGameCardInRegisterResp) Reset()         { *m = CreateGameCardInRegisterResp{} }
func (m *CreateGameCardInRegisterResp) String() string { return proto.CompactTextString(m) }
func (*CreateGameCardInRegisterResp) ProtoMessage()    {}
func (*CreateGameCardInRegisterResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{26}
}

func (m *CreateGameCardInRegisterResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetGameCardByTabReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TabId          uint32      `protobuf:"varint,2,opt,name=tab_id,json=tabId" json:"tab_id"`
	IsShowInputOpt bool        `protobuf:"varint,3,opt,name=is_show_input_opt,json=isShowInputOpt" json:"is_show_input_opt"`
}

func (m *GetGameCardByTabReq) Reset()                    { *m = GetGameCardByTabReq{} }
func (m *GetGameCardByTabReq) String() string            { return proto.CompactTextString(m) }
func (*GetGameCardByTabReq) ProtoMessage()               {}
func (*GetGameCardByTabReq) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{27} }

func (m *GetGameCardByTabReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameCardByTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetGameCardByTabReq) GetIsShowInputOpt() bool {
	if m != nil {
		return m.IsShowInputOpt
	}
	return false
}

type GetGameCardByTabResp struct {
	BaseResp     *ga.BaseResp  `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GameCardList *GameCardInfo `protobuf:"bytes,2,opt,name=game_card_list,json=gameCardList" json:"game_card_list,omitempty"`
}

func (m *GetGameCardByTabResp) Reset()                    { *m = GetGameCardByTabResp{} }
func (m *GetGameCardByTabResp) String() string            { return proto.CompactTextString(m) }
func (*GetGameCardByTabResp) ProtoMessage()               {}
func (*GetGameCardByTabResp) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{28} }

func (m *GetGameCardByTabResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameCardByTabResp) GetGameCardList() *GameCardInfo {
	if m != nil {
		return m.GameCardList
	}
	return nil
}

type GetGameCardConfReq struct {
	BaseReq        *ga.BaseReq            `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	FilterEntrance GameCardFilterEntrance `protobuf:"varint,2,req,name=filter_entrance,json=filterEntrance,enum=ga.GameCardFilterEntrance" json:"filter_entrance"`
	IsShowInputOpt bool                   `protobuf:"varint,3,opt,name=is_show_input_opt,json=isShowInputOpt" json:"is_show_input_opt"`
}

func (m *GetGameCardConfReq) Reset()                    { *m = GetGameCardConfReq{} }
func (m *GetGameCardConfReq) String() string            { return proto.CompactTextString(m) }
func (*GetGameCardConfReq) ProtoMessage()               {}
func (*GetGameCardConfReq) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{29} }

func (m *GetGameCardConfReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameCardConfReq) GetFilterEntrance() GameCardFilterEntrance {
	if m != nil {
		return m.FilterEntrance
	}
	return GameCardFilterEntrance_UNKNOWN_ENTRANCE
}

func (m *GetGameCardConfReq) GetIsShowInputOpt() bool {
	if m != nil {
		return m.IsShowInputOpt
	}
	return false
}

type GetGameCardConfResp struct {
	BaseResp         *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GameCardConfList []*GameCardConfInfo `protobuf:"bytes,2,rep,name=game_card_conf_list,json=gameCardConfList" json:"game_card_conf_list,omitempty"`
	CurGameCardNum   uint32              `protobuf:"varint,3,opt,name=cur_game_card_num,json=curGameCardNum" json:"cur_game_card_num"`
	ShowRecentPlay   bool                `protobuf:"varint,4,opt,name=show_recent_play,json=showRecentPlay" json:"show_recent_play"`
	MaxGameCardNum   uint32              `protobuf:"varint,5,opt,name=max_game_card_num,json=maxGameCardNum" json:"max_game_card_num"`
}

func (m *GetGameCardConfResp) Reset()                    { *m = GetGameCardConfResp{} }
func (m *GetGameCardConfResp) String() string            { return proto.CompactTextString(m) }
func (*GetGameCardConfResp) ProtoMessage()               {}
func (*GetGameCardConfResp) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{30} }

func (m *GetGameCardConfResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameCardConfResp) GetGameCardConfList() []*GameCardConfInfo {
	if m != nil {
		return m.GameCardConfList
	}
	return nil
}

func (m *GetGameCardConfResp) GetCurGameCardNum() uint32 {
	if m != nil {
		return m.CurGameCardNum
	}
	return 0
}

func (m *GetGameCardConfResp) GetShowRecentPlay() bool {
	if m != nil {
		return m.ShowRecentPlay
	}
	return false
}

func (m *GetGameCardConfResp) GetMaxGameCardNum() uint32 {
	if m != nil {
		return m.MaxGameCardNum
	}
	return 0
}

type BatchCreateGameCardReq struct {
	BaseReq  *ga.BaseReq     `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameCard []*GameCardInfo `protobuf:"bytes,2,rep,name=game_card,json=gameCard" json:"game_card,omitempty"`
}

func (m *BatchCreateGameCardReq) Reset()         { *m = BatchCreateGameCardReq{} }
func (m *BatchCreateGameCardReq) String() string { return proto.CompactTextString(m) }
func (*BatchCreateGameCardReq) ProtoMessage()    {}
func (*BatchCreateGameCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{31}
}

func (m *BatchCreateGameCardReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchCreateGameCardReq) GetGameCard() []*GameCardInfo {
	if m != nil {
		return m.GameCard
	}
	return nil
}

type BatchCreateGameCardResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *BatchCreateGameCardResp) Reset()         { *m = BatchCreateGameCardResp{} }
func (m *BatchCreateGameCardResp) String() string { return proto.CompactTextString(m) }
func (*BatchCreateGameCardResp) ProtoMessage()    {}
func (*BatchCreateGameCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{32}
}

func (m *BatchCreateGameCardResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetGameCardConfByTabIdsReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TabIds  []uint32    `protobuf:"varint,2,rep,name=tab_ids,json=tabIds" json:"tab_ids,omitempty"`
}

func (m *GetGameCardConfByTabIdsReq) Reset()         { *m = GetGameCardConfByTabIdsReq{} }
func (m *GetGameCardConfByTabIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetGameCardConfByTabIdsReq) ProtoMessage()    {}
func (*GetGameCardConfByTabIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{33}
}

func (m *GetGameCardConfByTabIdsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameCardConfByTabIdsReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

type GetGameCardConfByTabIdsResp struct {
	BaseResp         *ga.BaseResp           `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GameCardConfList []*NewGameCardConfInfo `protobuf:"bytes,2,rep,name=game_card_conf_list,json=gameCardConfList" json:"game_card_conf_list,omitempty"`
	MaxGameCardNum   uint32                 `protobuf:"varint,3,opt,name=max_game_card_num,json=maxGameCardNum" json:"max_game_card_num"`
}

func (m *GetGameCardConfByTabIdsResp) Reset()         { *m = GetGameCardConfByTabIdsResp{} }
func (m *GetGameCardConfByTabIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetGameCardConfByTabIdsResp) ProtoMessage()    {}
func (*GetGameCardConfByTabIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGameCard_, []int{34}
}

func (m *GetGameCardConfByTabIdsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameCardConfByTabIdsResp) GetGameCardConfList() []*NewGameCardConfInfo {
	if m != nil {
		return m.GameCardConfList
	}
	return nil
}

func (m *GetGameCardConfByTabIdsResp) GetMaxGameCardNum() uint32 {
	if m != nil {
		return m.MaxGameCardNum
	}
	return 0
}

type NewGameCardConfInfo struct {
	GameCardId   uint32 `protobuf:"varint,1,req,name=game_card_id,json=gameCardId" json:"game_card_id"`
	GameCardName string `protobuf:"bytes,2,req,name=game_card_name,json=gameCardName" json:"game_card_name"`
	UGameId      uint32 `protobuf:"varint,3,req,name=u_game_id,json=uGameId" json:"u_game_id"`
	TabId        uint32 `protobuf:"varint,4,req,name=tab_id,json=tabId" json:"tab_id"`
}

func (m *NewGameCardConfInfo) Reset()                    { *m = NewGameCardConfInfo{} }
func (m *NewGameCardConfInfo) String() string            { return proto.CompactTextString(m) }
func (*NewGameCardConfInfo) ProtoMessage()               {}
func (*NewGameCardConfInfo) Descriptor() ([]byte, []int) { return fileDescriptorGameCard_, []int{35} }

func (m *NewGameCardConfInfo) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

func (m *NewGameCardConfInfo) GetGameCardName() string {
	if m != nil {
		return m.GameCardName
	}
	return ""
}

func (m *NewGameCardConfInfo) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *NewGameCardConfInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func init() {
	proto.RegisterType((*ModifyGameCardOpt)(nil), "ga.ModifyGameCardOpt")
	proto.RegisterType((*ModifyGameCardInChannelReq)(nil), "ga.ModifyGameCardInChannelReq")
	proto.RegisterType((*ModifyGameCardInChannelResp)(nil), "ga.ModifyGameCardInChannelResp")
	proto.RegisterType((*GameCardLevelConf)(nil), "ga.GameCardLevelConf")
	proto.RegisterType((*GameCardOptWithPropConf)(nil), "ga.GameCardOptWithPropConf")
	proto.RegisterType((*GameCardInputConf)(nil), "ga.GameCardInputConf")
	proto.RegisterType((*GameCardOptConf)(nil), "ga.GameCardOptConf")
	proto.RegisterType((*GameCardConfInfo)(nil), "ga.GameCardConfInfo")
	proto.RegisterType((*NickNameType)(nil), "ga.NickNameType")
	proto.RegisterType((*UGameInfo)(nil), "ga.UGameInfo")
	proto.RegisterType((*GetAllGameCardConfReq)(nil), "ga.GetAllGameCardConfReq")
	proto.RegisterType((*GetAllGameCardConfResp)(nil), "ga.GetAllGameCardConfResp")
	proto.RegisterType((*GameScreenshot)(nil), "ga.GameScreenshot")
	proto.RegisterType((*GameCardOpt)(nil), "ga.GameCardOpt")
	proto.RegisterType((*GameCardInfo)(nil), "ga.GameCardInfo")
	proto.RegisterType((*GameCardInputVal)(nil), "ga.GameCardInputVal")
	proto.RegisterType((*GameNickNameInfo)(nil), "ga.GameNickNameInfo")
	proto.RegisterType((*GetGameCardReq)(nil), "ga.GetGameCardReq")
	proto.RegisterType((*GetGameCardResp)(nil), "ga.GetGameCardResp")
	proto.RegisterType((*CreateGameCardReq)(nil), "ga.CreateGameCardReq")
	proto.RegisterType((*CreateGameCardResp)(nil), "ga.CreateGameCardResp")
	proto.RegisterType((*SetGameCardReq)(nil), "ga.SetGameCardReq")
	proto.RegisterType((*SetGameCardResp)(nil), "ga.SetGameCardResp")
	proto.RegisterType((*DeleteGameCardReq)(nil), "ga.DeleteGameCardReq")
	proto.RegisterType((*DeleteGameCardResp)(nil), "ga.DeleteGameCardResp")
	proto.RegisterType((*CreateGameCardInRegisterReq)(nil), "ga.CreateGameCardInRegisterReq")
	proto.RegisterType((*CreateGameCardInRegisterResp)(nil), "ga.CreateGameCardInRegisterResp")
	proto.RegisterType((*GetGameCardByTabReq)(nil), "ga.GetGameCardByTabReq")
	proto.RegisterType((*GetGameCardByTabResp)(nil), "ga.GetGameCardByTabResp")
	proto.RegisterType((*GetGameCardConfReq)(nil), "ga.GetGameCardConfReq")
	proto.RegisterType((*GetGameCardConfResp)(nil), "ga.GetGameCardConfResp")
	proto.RegisterType((*BatchCreateGameCardReq)(nil), "ga.BatchCreateGameCardReq")
	proto.RegisterType((*BatchCreateGameCardResp)(nil), "ga.BatchCreateGameCardResp")
	proto.RegisterType((*GetGameCardConfByTabIdsReq)(nil), "ga.GetGameCardConfByTabIdsReq")
	proto.RegisterType((*GetGameCardConfByTabIdsResp)(nil), "ga.GetGameCardConfByTabIdsResp")
	proto.RegisterType((*NewGameCardConfInfo)(nil), "ga.NewGameCardConfInfo")
	proto.RegisterEnum("ga.GameCardType", GameCardType_name, GameCardType_value)
	proto.RegisterEnum("ga.GameCardOptType", GameCardOptType_name, GameCardOptType_value)
	proto.RegisterEnum("ga.RegisterJumpPage", RegisterJumpPage_name, RegisterJumpPage_value)
	proto.RegisterEnum("ga.EScreenShotAuditStatus", EScreenShotAuditStatus_name, EScreenShotAuditStatus_value)
	proto.RegisterEnum("ga.GameCardFilterEntrance", GameCardFilterEntrance_name, GameCardFilterEntrance_value)
}
func (m *ModifyGameCardOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyGameCardOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.OptName)))
	i += copy(dAtA[i:], m.OptName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.OptId))
	if len(m.ValueList) > 0 {
		for _, s := range m.ValueList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *ModifyGameCardInChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyGameCardInChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.GameCardId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameCardName)))
	i += copy(dAtA[i:], m.GameCardName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.UGameId))
	if len(m.OptList) > 0 {
		for _, msg := range m.OptList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ModifyGameCardInChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyGameCardInChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *GameCardLevelConf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameCardLevelConf) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.LevelName)))
	i += copy(dAtA[i:], m.LevelName)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.LevelImgUrl)))
	i += copy(dAtA[i:], m.LevelImgUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.LevelImgUrlMic)))
	i += copy(dAtA[i:], m.LevelImgUrlMic)
	return i, nil
}

func (m *GameCardOptWithPropConf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameCardOptWithPropConf) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.Prop)))
	i += copy(dAtA[i:], m.Prop)
	if len(m.ValueList) > 0 {
		for _, s := range m.ValueList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GameCardInputConf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameCardInputConf) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.MinNum))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.MaxNum))
	return i, nil
}

func (m *GameCardOptConf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameCardOptConf) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.OptName)))
	i += copy(dAtA[i:], m.OptName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.OptId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.MaxSetCnt))
	dAtA[i] = 0x20
	i++
	if m.IsMust {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.TextBoxStyle))
	dAtA[i] = 0x30
	i++
	if m.DisplayInChannel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.OptType))
	if len(m.OptConfValueList) > 0 {
		for _, s := range m.OptConfValueList {
			dAtA[i] = 0x42
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.OptWithPropValueList) > 0 {
		for _, msg := range m.OptWithPropValueList {
			dAtA[i] = 0x4a
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.LevelConfValueList) > 0 {
		for _, msg := range m.LevelConfValueList {
			dAtA[i] = 0x52
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.InputConfValueList) > 0 {
		for _, msg := range m.InputConfValueList {
			dAtA[i] = 0x5a
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GameCardConfInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameCardConfInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.GameCardId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameCardName)))
	i += copy(dAtA[i:], m.GameCardName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.UGameId))
	dAtA[i] = 0x20
	i++
	if m.HasNickName {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	if m.HasScreenshot {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x32
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameThumbImgUrl)))
	i += copy(dAtA[i:], m.GameThumbImgUrl)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameBackImgUrl)))
	i += copy(dAtA[i:], m.GameBackImgUrl)
	dAtA[i] = 0x42
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameIconImgUrl)))
	i += copy(dAtA[i:], m.GameIconImgUrl)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameCornerMarkImgUrl)))
	i += copy(dAtA[i:], m.GameCornerMarkImgUrl)
	dAtA[i] = 0x52
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameBackImgMiniUrl)))
	i += copy(dAtA[i:], m.GameBackImgMiniUrl)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.DefaultGameLevelImgUrl)))
	i += copy(dAtA[i:], m.DefaultGameLevelImgUrl)
	dAtA[i] = 0x60
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.GameBackColorNum))
	if len(m.OptConfList) > 0 {
		for _, msg := range m.OptConfList {
			dAtA[i] = 0x6a
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x70
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.CardType))
	dAtA[i] = 0x78
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.RegisterJumpPage))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	if m.ShowTeamNum {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x8a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.ShowTeamText)))
	i += copy(dAtA[i:], m.ShowTeamText)
	if len(m.Accounts) > 0 {
		for _, s := range m.Accounts {
			dAtA[i] = 0x92
			i++
			dAtA[i] = 0x1
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.UGameInfo) > 0 {
		for _, msg := range m.UGameInfo {
			dAtA[i] = 0x9a
			i++
			dAtA[i] = 0x1
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.NicknameTypeList) > 0 {
		for _, msg := range m.NicknameTypeList {
			dAtA[i] = 0xa2
			i++
			dAtA[i] = 0x1
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0xa8
	i++
	dAtA[i] = 0x1
	i++
	if m.HasQuickMatchEntrance {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *NickNameType) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NickNameType) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.TypeId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.TypeName)))
	i += copy(dAtA[i:], m.TypeName)
	return i, nil
}

func (m *UGameInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UGameInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.UGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.UGameName)))
	i += copy(dAtA[i:], m.UGameName)
	return i, nil
}

func (m *GetAllGameCardConfReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllGameCardConfReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	if m.IsShowInputOpt {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetAllGameCardConfResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllGameCardConfResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if len(m.GameCardConfList) > 0 {
		for _, msg := range m.GameCardConfList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.MaxGameCardNum))
	return i, nil
}

func (m *GameScreenshot) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameScreenshot) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.AuditStatus))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.ImgUrl)))
	i += copy(dAtA[i:], m.ImgUrl)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x20
	i++
	if m.NewObs {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GameCardOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameCardOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.OptName)))
	i += copy(dAtA[i:], m.OptName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.OptId))
	if len(m.ValueList) > 0 {
		for _, s := range m.ValueList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.OptType))
	if len(m.InputVal) > 0 {
		for _, msg := range m.InputVal {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GameCardInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameCardInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.GameCardId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameCardName)))
	i += copy(dAtA[i:], m.GameCardName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.UGameId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameNickname)))
	i += copy(dAtA[i:], m.GameNickname)
	if len(m.ScreenshotList) > 0 {
		for _, msg := range m.ScreenshotList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.OptList) > 0 {
		for _, msg := range m.OptList {
			dAtA[i] = 0x32
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Conf != nil {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.Conf.Size()))
		n5, err := m.Conf.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if len(m.GameNicknameList) > 0 {
		for _, msg := range m.GameNicknameList {
			dAtA[i] = 0x42
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GameCardInputVal) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameCardInputVal) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.ElemTitle)))
	i += copy(dAtA[i:], m.ElemTitle)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.ElemVal)))
	i += copy(dAtA[i:], m.ElemVal)
	return i, nil
}

func (m *GameNickNameInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameNickNameInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.NicknameType == nil {
		return 0, proto.NewRequiredNotSetError("nickname_type")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.NicknameType.Size()))
		n6, err := m.NicknameType.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	return i, nil
}

func (m *GetGameCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x18
	i++
	if m.IsShowInputOpt {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetGameCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.TargetUid))
	if len(m.GameCardList) > 0 {
		for _, msg := range m.GameCardList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CreateGameCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateGameCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if m.GameCard == nil {
		return 0, proto.NewRequiredNotSetError("game_card")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.GameCard.Size()))
		n10, err := m.GameCard.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *CreateGameCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateGameCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n11, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *SetGameCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGameCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n12, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.GameCard == nil {
		return 0, proto.NewRequiredNotSetError("game_card")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.GameCard.Size()))
		n13, err := m.GameCard.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x18
	i++
	if m.DelGameNick {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetGameCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGameCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n14, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	return i, nil
}

func (m *DeleteGameCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteGameCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n15, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.GameCardId))
	return i, nil
}

func (m *DeleteGameCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteGameCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n16, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	return i, nil
}

func (m *CreateGameCardInRegisterReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateGameCardInRegisterReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n17, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	if len(m.GameCardList) > 0 {
		for _, msg := range m.GameCardList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.BirthDay)))
	i += copy(dAtA[i:], m.BirthDay)
	return i, nil
}

func (m *CreateGameCardInRegisterResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateGameCardInRegisterResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n18, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	return i, nil
}

func (m *GetGameCardByTabReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameCardByTabReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.TabId))
	dAtA[i] = 0x18
	i++
	if m.IsShowInputOpt {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetGameCardByTabResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameCardByTabResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n20, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	if m.GameCardList != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.GameCardList.Size()))
		n21, err := m.GameCardList.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	return i, nil
}

func (m *GetGameCardConfReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameCardConfReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n22, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.FilterEntrance))
	dAtA[i] = 0x18
	i++
	if m.IsShowInputOpt {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetGameCardConfResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameCardConfResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n23, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	if len(m.GameCardConfList) > 0 {
		for _, msg := range m.GameCardConfList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.CurGameCardNum))
	dAtA[i] = 0x20
	i++
	if m.ShowRecentPlay {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.MaxGameCardNum))
	return i, nil
}

func (m *BatchCreateGameCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchCreateGameCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n24, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	if len(m.GameCard) > 0 {
		for _, msg := range m.GameCard {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchCreateGameCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchCreateGameCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n25, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	return i, nil
}

func (m *GetGameCardConfByTabIdsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameCardConfByTabIdsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, proto.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseReq.Size()))
		n26, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	if len(m.TabIds) > 0 {
		for _, num := range m.TabIds {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetGameCardConfByTabIdsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameCardConfByTabIdsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, proto.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGameCard_(dAtA, i, uint64(m.BaseResp.Size()))
		n27, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	if len(m.GameCardConfList) > 0 {
		for _, msg := range m.GameCardConfList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGameCard_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.MaxGameCardNum))
	return i, nil
}

func (m *NewGameCardConfInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NewGameCardConfInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.GameCardId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(len(m.GameCardName)))
	i += copy(dAtA[i:], m.GameCardName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.UGameId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGameCard_(dAtA, i, uint64(m.TabId))
	return i, nil
}

func encodeVarintGameCard_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ModifyGameCardOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.OptName)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.OptId))
	if len(m.ValueList) > 0 {
		for _, s := range m.ValueList {
			l = len(s)
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	return n
}

func (m *ModifyGameCardInChannelReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	n += 1 + sovGameCard_(uint64(m.GameCardId))
	l = len(m.GameCardName)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.UGameId))
	if len(m.OptList) > 0 {
		for _, e := range m.OptList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	return n
}

func (m *ModifyGameCardInChannelResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	return n
}

func (m *GameCardLevelConf) Size() (n int) {
	var l int
	_ = l
	l = len(m.LevelName)
	n += 1 + l + sovGameCard_(uint64(l))
	l = len(m.LevelImgUrl)
	n += 1 + l + sovGameCard_(uint64(l))
	l = len(m.LevelImgUrlMic)
	n += 1 + l + sovGameCard_(uint64(l))
	return n
}

func (m *GameCardOptWithPropConf) Size() (n int) {
	var l int
	_ = l
	l = len(m.Prop)
	n += 1 + l + sovGameCard_(uint64(l))
	if len(m.ValueList) > 0 {
		for _, s := range m.ValueList {
			l = len(s)
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	return n
}

func (m *GameCardInputConf) Size() (n int) {
	var l int
	_ = l
	l = len(m.Title)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.MinNum))
	n += 1 + sovGameCard_(uint64(m.MaxNum))
	return n
}

func (m *GameCardOptConf) Size() (n int) {
	var l int
	_ = l
	l = len(m.OptName)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.OptId))
	n += 1 + sovGameCard_(uint64(m.MaxSetCnt))
	n += 2
	n += 1 + sovGameCard_(uint64(m.TextBoxStyle))
	n += 2
	n += 1 + sovGameCard_(uint64(m.OptType))
	if len(m.OptConfValueList) > 0 {
		for _, s := range m.OptConfValueList {
			l = len(s)
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	if len(m.OptWithPropValueList) > 0 {
		for _, e := range m.OptWithPropValueList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	if len(m.LevelConfValueList) > 0 {
		for _, e := range m.LevelConfValueList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	if len(m.InputConfValueList) > 0 {
		for _, e := range m.InputConfValueList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	return n
}

func (m *GameCardConfInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGameCard_(uint64(m.GameCardId))
	l = len(m.GameCardName)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.UGameId))
	n += 2
	n += 2
	l = len(m.GameThumbImgUrl)
	n += 1 + l + sovGameCard_(uint64(l))
	l = len(m.GameBackImgUrl)
	n += 1 + l + sovGameCard_(uint64(l))
	l = len(m.GameIconImgUrl)
	n += 1 + l + sovGameCard_(uint64(l))
	l = len(m.GameCornerMarkImgUrl)
	n += 1 + l + sovGameCard_(uint64(l))
	l = len(m.GameBackImgMiniUrl)
	n += 1 + l + sovGameCard_(uint64(l))
	l = len(m.DefaultGameLevelImgUrl)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.GameBackColorNum))
	if len(m.OptConfList) > 0 {
		for _, e := range m.OptConfList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	n += 1 + sovGameCard_(uint64(m.CardType))
	n += 1 + sovGameCard_(uint64(m.RegisterJumpPage))
	n += 3
	l = len(m.ShowTeamText)
	n += 2 + l + sovGameCard_(uint64(l))
	if len(m.Accounts) > 0 {
		for _, s := range m.Accounts {
			l = len(s)
			n += 2 + l + sovGameCard_(uint64(l))
		}
	}
	if len(m.UGameInfo) > 0 {
		for _, e := range m.UGameInfo {
			l = e.Size()
			n += 2 + l + sovGameCard_(uint64(l))
		}
	}
	if len(m.NicknameTypeList) > 0 {
		for _, e := range m.NicknameTypeList {
			l = e.Size()
			n += 2 + l + sovGameCard_(uint64(l))
		}
	}
	n += 3
	return n
}

func (m *NickNameType) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGameCard_(uint64(m.TypeId))
	l = len(m.TypeName)
	n += 1 + l + sovGameCard_(uint64(l))
	return n
}

func (m *UGameInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGameCard_(uint64(m.UGameId))
	l = len(m.UGameName)
	n += 1 + l + sovGameCard_(uint64(l))
	return n
}

func (m *GetAllGameCardConfReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	n += 2
	return n
}

func (m *GetAllGameCardConfResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if len(m.GameCardConfList) > 0 {
		for _, e := range m.GameCardConfList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	n += 1 + sovGameCard_(uint64(m.MaxGameCardNum))
	return n
}

func (m *GameScreenshot) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGameCard_(uint64(m.AuditStatus))
	l = len(m.ImgUrl)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.Index))
	n += 2
	return n
}

func (m *GameCardOpt) Size() (n int) {
	var l int
	_ = l
	l = len(m.OptName)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.OptId))
	if len(m.ValueList) > 0 {
		for _, s := range m.ValueList {
			l = len(s)
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	n += 1 + sovGameCard_(uint64(m.OptType))
	if len(m.InputVal) > 0 {
		for _, e := range m.InputVal {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	return n
}

func (m *GameCardInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGameCard_(uint64(m.GameCardId))
	l = len(m.GameCardName)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.UGameId))
	l = len(m.GameNickname)
	n += 1 + l + sovGameCard_(uint64(l))
	if len(m.ScreenshotList) > 0 {
		for _, e := range m.ScreenshotList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	if len(m.OptList) > 0 {
		for _, e := range m.OptList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	if m.Conf != nil {
		l = m.Conf.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if len(m.GameNicknameList) > 0 {
		for _, e := range m.GameNicknameList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	return n
}

func (m *GameCardInputVal) Size() (n int) {
	var l int
	_ = l
	l = len(m.ElemTitle)
	n += 1 + l + sovGameCard_(uint64(l))
	l = len(m.ElemVal)
	n += 1 + l + sovGameCard_(uint64(l))
	return n
}

func (m *GameNickNameInfo) Size() (n int) {
	var l int
	_ = l
	if m.NicknameType != nil {
		l = m.NicknameType.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	l = len(m.Nickname)
	n += 1 + l + sovGameCard_(uint64(l))
	return n
}

func (m *GetGameCardReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	n += 1 + sovGameCard_(uint64(m.TargetUid))
	n += 2
	return n
}

func (m *GetGameCardResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	n += 1 + sovGameCard_(uint64(m.TargetUid))
	if len(m.GameCardList) > 0 {
		for _, e := range m.GameCardList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	return n
}

func (m *CreateGameCardReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if m.GameCard != nil {
		l = m.GameCard.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	return n
}

func (m *CreateGameCardResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	return n
}

func (m *SetGameCardReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if m.GameCard != nil {
		l = m.GameCard.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	n += 2
	return n
}

func (m *SetGameCardResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	return n
}

func (m *DeleteGameCardReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	n += 1 + sovGameCard_(uint64(m.GameCardId))
	return n
}

func (m *DeleteGameCardResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	return n
}

func (m *CreateGameCardInRegisterReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if len(m.GameCardList) > 0 {
		for _, e := range m.GameCardList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	l = len(m.BirthDay)
	n += 1 + l + sovGameCard_(uint64(l))
	return n
}

func (m *CreateGameCardInRegisterResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	return n
}

func (m *GetGameCardByTabReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	n += 1 + sovGameCard_(uint64(m.TabId))
	n += 2
	return n
}

func (m *GetGameCardByTabResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if m.GameCardList != nil {
		l = m.GameCardList.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	return n
}

func (m *GetGameCardConfReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	n += 1 + sovGameCard_(uint64(m.FilterEntrance))
	n += 2
	return n
}

func (m *GetGameCardConfResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if len(m.GameCardConfList) > 0 {
		for _, e := range m.GameCardConfList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	n += 1 + sovGameCard_(uint64(m.CurGameCardNum))
	n += 2
	n += 1 + sovGameCard_(uint64(m.MaxGameCardNum))
	return n
}

func (m *BatchCreateGameCardReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if len(m.GameCard) > 0 {
		for _, e := range m.GameCard {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	return n
}

func (m *BatchCreateGameCardResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	return n
}

func (m *GetGameCardConfByTabIdsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if len(m.TabIds) > 0 {
		for _, e := range m.TabIds {
			n += 1 + sovGameCard_(uint64(e))
		}
	}
	return n
}

func (m *GetGameCardConfByTabIdsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGameCard_(uint64(l))
	}
	if len(m.GameCardConfList) > 0 {
		for _, e := range m.GameCardConfList {
			l = e.Size()
			n += 1 + l + sovGameCard_(uint64(l))
		}
	}
	n += 1 + sovGameCard_(uint64(m.MaxGameCardNum))
	return n
}

func (m *NewGameCardConfInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGameCard_(uint64(m.GameCardId))
	l = len(m.GameCardName)
	n += 1 + l + sovGameCard_(uint64(l))
	n += 1 + sovGameCard_(uint64(m.UGameId))
	n += 1 + sovGameCard_(uint64(m.TabId))
	return n
}

func sovGameCard_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGameCard_(x uint64) (n int) {
	return sovGameCard_(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ModifyGameCardOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ModifyGameCardOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ModifyGameCardOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptId", wireType)
			}
			m.OptId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ValueList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ValueList = append(m.ValueList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("opt_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("opt_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyGameCardInChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ModifyGameCardInChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ModifyGameCardInChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardId", wireType)
			}
			m.GameCardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameCardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UGameId", wireType)
			}
			m.UGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptList = append(m.OptList, &ModifyGameCardOpt{})
			if err := m.OptList[len(m.OptList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("game_card_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return proto.NewRequiredNotSetError("game_card_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return proto.NewRequiredNotSetError("u_game_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyGameCardInChannelResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ModifyGameCardInChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ModifyGameCardInChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameCardLevelConf) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameCardLevelConf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameCardLevelConf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LevelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LevelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LevelImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LevelImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LevelImgUrlMic", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LevelImgUrlMic = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("level_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("level_img_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return proto.NewRequiredNotSetError("level_img_url_mic")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameCardOptWithPropConf) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameCardOptWithPropConf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameCardOptWithPropConf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Prop", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Prop = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ValueList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ValueList = append(m.ValueList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("prop")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameCardInputConf) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameCardInputConf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameCardInputConf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinNum", wireType)
			}
			m.MinNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxNum", wireType)
			}
			m.MaxNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameCardOptConf) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameCardOptConf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameCardOptConf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptId", wireType)
			}
			m.OptId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxSetCnt", wireType)
			}
			m.MaxSetCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxSetCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsMust", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMust = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TextBoxStyle", wireType)
			}
			m.TextBoxStyle = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TextBoxStyle |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DisplayInChannel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DisplayInChannel = bool(v != 0)
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptType", wireType)
			}
			m.OptType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptType |= (GameCardOptType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptConfValueList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptConfValueList = append(m.OptConfValueList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptWithPropValueList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptWithPropValueList = append(m.OptWithPropValueList, &GameCardOptWithPropConf{})
			if err := m.OptWithPropValueList[len(m.OptWithPropValueList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LevelConfValueList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LevelConfValueList = append(m.LevelConfValueList, &GameCardLevelConf{})
			if err := m.LevelConfValueList[len(m.LevelConfValueList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InputConfValueList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InputConfValueList = append(m.InputConfValueList, &GameCardInputConf{})
			if err := m.InputConfValueList[len(m.InputConfValueList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("opt_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("opt_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return proto.NewRequiredNotSetError("max_set_cnt")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return proto.NewRequiredNotSetError("is_must")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return proto.NewRequiredNotSetError("text_box_style")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return proto.NewRequiredNotSetError("display_in_channel")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return proto.NewRequiredNotSetError("opt_type")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameCardConfInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameCardConfInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameCardConfInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardId", wireType)
			}
			m.GameCardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameCardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UGameId", wireType)
			}
			m.UGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HasNickName", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasNickName = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HasScreenshot", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasScreenshot = bool(v != 0)
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameThumbImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameThumbImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameBackImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameBackImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameIconImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameIconImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCornerMarkImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCornerMarkImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameBackImgMiniUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameBackImgMiniUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultGameLevelImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DefaultGameLevelImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameBackColorNum", wireType)
			}
			m.GameBackColorNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameBackColorNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptConfList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptConfList = append(m.OptConfList, &GameCardOptConf{})
			if err := m.OptConfList[len(m.OptConfList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CardType", wireType)
			}
			m.CardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00001000)
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegisterJumpPage", wireType)
			}
			m.RegisterJumpPage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegisterJumpPage |= (RegisterJumpPage(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00002000)
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShowTeamNum", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShowTeamNum = bool(v != 0)
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShowTeamText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShowTeamText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Accounts", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Accounts = append(m.Accounts, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UGameInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UGameInfo = append(m.UGameInfo, &UGameInfo{})
			if err := m.UGameInfo[len(m.UGameInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NicknameTypeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.NicknameTypeList = append(m.NicknameTypeList, &NickNameType{})
			if err := m.NicknameTypeList[len(m.NicknameTypeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 21:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HasQuickMatchEntrance", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasQuickMatchEntrance = bool(v != 0)
			hasFields[0] |= uint64(0x00004000)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("game_card_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("game_card_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return proto.NewRequiredNotSetError("u_game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return proto.NewRequiredNotSetError("has_nick_name")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return proto.NewRequiredNotSetError("has_screenshot")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return proto.NewRequiredNotSetError("game_thumb_img_url")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return proto.NewRequiredNotSetError("game_back_img_url")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return proto.NewRequiredNotSetError("game_icon_img_url")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return proto.NewRequiredNotSetError("game_corner_mark_img_url")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return proto.NewRequiredNotSetError("game_back_img_mini_url")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return proto.NewRequiredNotSetError("default_game_level_img_url")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return proto.NewRequiredNotSetError("game_back_color_num")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return proto.NewRequiredNotSetError("card_type")
	}
	if hasFields[0]&uint64(0x00002000) == 0 {
		return proto.NewRequiredNotSetError("register_jump_page")
	}
	if hasFields[0]&uint64(0x00004000) == 0 {
		return proto.NewRequiredNotSetError("has_quick_match_entrance")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *NickNameType) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NickNameType: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NickNameType: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TypeId", wireType)
			}
			m.TypeId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TypeId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TypeName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TypeName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UGameInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UGameInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UGameInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UGameId", wireType)
			}
			m.UGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UGameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UGameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllGameCardConfReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllGameCardConfReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllGameCardConfReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsShowInputOpt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShowInputOpt = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllGameCardConfResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllGameCardConfResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllGameCardConfResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardConfList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCardConfList = append(m.GameCardConfList, &GameCardConfInfo{})
			if err := m.GameCardConfList[len(m.GameCardConfList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxGameCardNum", wireType)
			}
			m.MaxGameCardNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxGameCardNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameScreenshot) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameScreenshot: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameScreenshot: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AuditStatus", wireType)
			}
			m.AuditStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AuditStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewObs", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.NewObs = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("audit_status")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("img_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return proto.NewRequiredNotSetError("index")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameCardOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameCardOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameCardOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptId", wireType)
			}
			m.OptId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ValueList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ValueList = append(m.ValueList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptType", wireType)
			}
			m.OptType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OptType |= (GameCardOptType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InputVal", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InputVal = append(m.InputVal, &GameCardInputVal{})
			if err := m.InputVal[len(m.InputVal)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("opt_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("opt_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameCardInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameCardInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameCardInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardId", wireType)
			}
			m.GameCardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameCardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UGameId", wireType)
			}
			m.UGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ScreenshotList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ScreenshotList = append(m.ScreenshotList, &GameScreenshot{})
			if err := m.ScreenshotList[len(m.ScreenshotList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OptList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OptList = append(m.OptList, &GameCardOpt{})
			if err := m.OptList[len(m.OptList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Conf", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Conf == nil {
				m.Conf = &GameCardConfInfo{}
			}
			if err := m.Conf.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameNicknameList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameNicknameList = append(m.GameNicknameList, &GameNickNameInfo{})
			if err := m.GameNicknameList[len(m.GameNicknameList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("game_card_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("game_card_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return proto.NewRequiredNotSetError("u_game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return proto.NewRequiredNotSetError("game_nickname")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameCardInputVal) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameCardInputVal: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameCardInputVal: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ElemTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ElemTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ElemVal", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ElemVal = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameNickNameInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GameNickNameInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GameNickNameInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NicknameType", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.NicknameType == nil {
				m.NicknameType = &NickNameType{}
			}
			if err := m.NicknameType.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("nickname_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("nickname")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGameCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGameCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsShowInputOpt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShowInputOpt = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGameCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGameCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCardList = append(m.GameCardList, &GameCardInfo{})
			if err := m.GameCardList[len(m.GameCardList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateGameCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateGameCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateGameCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCard", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GameCard == nil {
				m.GameCard = &GameCardInfo{}
			}
			if err := m.GameCard.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("game_card")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateGameCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateGameCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateGameCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGameCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetGameCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetGameCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCard", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GameCard == nil {
				m.GameCard = &GameCardInfo{}
			}
			if err := m.GameCard.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DelGameNick", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DelGameNick = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("game_card")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return proto.NewRequiredNotSetError("del_game_nick")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGameCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetGameCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetGameCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteGameCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteGameCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteGameCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardId", wireType)
			}
			m.GameCardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameCardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("game_card_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteGameCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteGameCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteGameCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateGameCardInRegisterReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateGameCardInRegisterReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateGameCardInRegisterReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCardList = append(m.GameCardList, &GameCardInfo{})
			if err := m.GameCardList[len(m.GameCardList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BirthDay", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BirthDay = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateGameCardInRegisterResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateGameCardInRegisterResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateGameCardInRegisterResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameCardByTabReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGameCardByTabReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGameCardByTabReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TabId", wireType)
			}
			m.TabId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TabId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsShowInputOpt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShowInputOpt = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameCardByTabResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGameCardByTabResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGameCardByTabResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GameCardList == nil {
				m.GameCardList = &GameCardInfo{}
			}
			if err := m.GameCardList.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameCardConfReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGameCardConfReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGameCardConfReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FilterEntrance", wireType)
			}
			m.FilterEntrance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FilterEntrance |= (GameCardFilterEntrance(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsShowInputOpt", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShowInputOpt = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("filter_entrance")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameCardConfResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGameCardConfResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGameCardConfResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardConfList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCardConfList = append(m.GameCardConfList, &GameCardConfInfo{})
			if err := m.GameCardConfList[len(m.GameCardConfList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurGameCardNum", wireType)
			}
			m.CurGameCardNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurGameCardNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShowRecentPlay", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShowRecentPlay = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxGameCardNum", wireType)
			}
			m.MaxGameCardNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxGameCardNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchCreateGameCardReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchCreateGameCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchCreateGameCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCard", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCard = append(m.GameCard, &GameCardInfo{})
			if err := m.GameCard[len(m.GameCard)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchCreateGameCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchCreateGameCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchCreateGameCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameCardConfByTabIdsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGameCardConfByTabIdsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGameCardConfByTabIdsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGameCard_
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TabIds = append(m.TabIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGameCard_
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGameCard_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGameCard_
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TabIds = append(m.TabIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field TabIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameCardConfByTabIdsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGameCardConfByTabIdsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGameCardConfByTabIdsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardConfList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCardConfList = append(m.GameCardConfList, &NewGameCardConfInfo{})
			if err := m.GameCardConfList[len(m.GameCardConfList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxGameCardNum", wireType)
			}
			m.MaxGameCardNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxGameCardNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *NewGameCardConfInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NewGameCardConfInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NewGameCardConfInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardId", wireType)
			}
			m.GameCardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameCardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GameCardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGameCard_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GameCardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UGameId", wireType)
			}
			m.UGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TabId", wireType)
			}
			m.TabId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TabId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGameCard_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGameCard_
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return proto.NewRequiredNotSetError("game_card_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return proto.NewRequiredNotSetError("game_card_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return proto.NewRequiredNotSetError("u_game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return proto.NewRequiredNotSetError("tab_id")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGameCard_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGameCard_
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGameCard_
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGameCard_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGameCard_
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGameCard_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGameCard_ = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGameCard_   = fmt.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("game_card_.proto", fileDescriptorGameCard_) }

var fileDescriptorGameCard_ = []byte{
	// 2215 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x59, 0xdb, 0x6f, 0xe3, 0x58,
	0x19, 0xaf, 0x93, 0x5e, 0x92, 0x2f, 0x97, 0xba, 0xa7, 0x37, 0xd3, 0xee, 0xce, 0x94, 0xec, 0x6a,
	0xe8, 0x16, 0x4d, 0xbb, 0x3b, 0xb0, 0x80, 0xc4, 0x72, 0x69, 0xd3, 0xec, 0xd4, 0x4b, 0x73, 0xc1,
	0x49, 0x67, 0x60, 0x25, 0x64, 0x9d, 0x38, 0xa7, 0x89, 0x77, 0x7c, 0x1b, 0xfb, 0x78, 0xda, 0xfc,
	0x03, 0xbc, 0x20, 0x21, 0xd0, 0x0a, 0x21, 0xde, 0x78, 0x84, 0xff, 0x00, 0x1e, 0xe1, 0x69, 0x1f,
	0x91, 0x78, 0x47, 0x68, 0x78, 0xe5, 0x8f, 0x40, 0xe7, 0x38, 0xb6, 0x8f, 0x93, 0xce, 0xd0, 0x94,
	0xcb, 0xbc, 0x39, 0xdf, 0xf7, 0x9d, 0xef, 0xfe, 0xfd, 0xfc, 0x1d, 0x07, 0xe4, 0x21, 0xb6, 0x89,
	0x6e, 0x60, 0x7f, 0xa0, 0x1f, 0x7a, 0xbe, 0x4b, 0x5d, 0x94, 0x1b, 0xe2, 0x9d, 0xca, 0x10, 0xeb,
	0x7d, 0x1c, 0x90, 0x88, 0x54, 0xf3, 0x60, 0xad, 0xe9, 0x0e, 0xcc, 0xcb, 0xf1, 0x63, 0x6c, 0x93,
	0x3a, 0xf6, 0x07, 0x6d, 0x8f, 0xa2, 0xfb, 0x50, 0x70, 0x3d, 0xaa, 0x3b, 0xd8, 0x26, 0x8a, 0xb4,
	0x97, 0xdb, 0x2f, 0x9e, 0x2c, 0x7e, 0xf1, 0xb7, 0xfb, 0x0b, 0xda, 0x8a, 0xeb, 0xd1, 0x16, 0xb6,
	0x09, 0xda, 0x85, 0x65, 0x26, 0x60, 0x0e, 0x94, 0xdc, 0x5e, 0x6e, 0xbf, 0x32, 0x61, 0x2f, 0xb9,
	0x1e, 0x55, 0x07, 0xe8, 0x6d, 0x80, 0x17, 0xd8, 0x0a, 0x89, 0x6e, 0x99, 0x01, 0x55, 0xf2, 0x7b,
	0xf9, 0xfd, 0xa2, 0x56, 0xe4, 0x94, 0x73, 0x33, 0xa0, 0xb5, 0x7f, 0x4a, 0xb0, 0x93, 0x35, 0xa9,
	0x3a, 0xf5, 0x11, 0x76, 0x1c, 0x62, 0x69, 0xe4, 0x39, 0x7a, 0x00, 0x05, 0xe6, 0x9e, 0xee, 0x93,
	0xe7, 0xdc, 0x76, 0xe9, 0x51, 0xe9, 0x70, 0x88, 0x0f, 0x4f, 0x70, 0x40, 0x34, 0xf2, 0x5c, 0x5b,
	0xe9, 0x47, 0x0f, 0xe8, 0x01, 0x94, 0xd3, 0xf8, 0xa6, 0x1c, 0x81, 0x61, 0xac, 0x79, 0x80, 0x0e,
	0xa0, 0x9a, 0xca, 0xf1, 0x88, 0xf2, 0x42, 0x44, 0xe5, 0x58, 0x92, 0x87, 0xb5, 0x07, 0xc5, 0x50,
	0xe7, 0xd2, 0xe6, 0x40, 0x59, 0x14, 0x14, 0xae, 0x84, 0xcc, 0x57, 0x75, 0x80, 0xde, 0x8f, 0x32,
	0xc3, 0x23, 0x5b, 0xda, 0xcb, 0xef, 0x97, 0x1e, 0x6d, 0x32, 0xef, 0x66, 0x52, 0xc8, 0x53, 0xc5,
	0xc3, 0x3d, 0x83, 0xdd, 0x57, 0x46, 0x1b, 0x78, 0xe8, 0x3d, 0x28, 0x4e, 0xc2, 0x0d, 0xbc, 0x49,
	0xbc, 0xe5, 0x34, 0xde, 0xc0, 0xd3, 0x0a, 0xfd, 0xc9, 0x53, 0xed, 0x73, 0x09, 0xd6, 0x62, 0x25,
	0xe7, 0xe4, 0x05, 0xb1, 0xea, 0xae, 0x73, 0x89, 0xde, 0x01, 0xb0, 0xd8, 0x8f, 0xd9, 0x6a, 0x15,
	0x39, 0x9d, 0x07, 0xb6, 0x0f, 0x95, 0x48, 0xc8, 0xb4, 0x87, 0x7a, 0xe8, 0x5b, 0x3c, 0x5b, 0xb1,
	0x5c, 0x89, 0xb3, 0x54, 0x7b, 0x78, 0xe1, 0x5b, 0xe8, 0x08, 0xd6, 0x32, 0x92, 0xba, 0x6d, 0x1a,
	0x99, 0x8c, 0x55, 0x05, 0xe9, 0xa6, 0x69, 0xd4, 0x34, 0xd8, 0x16, 0xe2, 0x7e, 0x6a, 0xd2, 0x51,
	0xc7, 0x77, 0x3d, 0xee, 0x9a, 0x02, 0x8b, 0x9e, 0xef, 0x7a, 0x19, 0xa7, 0x38, 0x65, 0xaa, 0x45,
	0x72, 0xd3, 0x2d, 0x62, 0xa7, 0x81, 0xaa, 0x8e, 0x17, 0x52, 0xae, 0x6d, 0x07, 0x96, 0xa8, 0x49,
	0x2d, 0x16, 0xa3, 0x94, 0xa8, 0x8b, 0x48, 0xe8, 0x6d, 0x58, 0xb1, 0x4d, 0x47, 0x77, 0x42, 0x5b,
	0xc9, 0xed, 0x49, 0x49, 0xd9, 0x96, 0x6d, 0xd3, 0x69, 0x85, 0x36, 0x67, 0xe3, 0x6b, 0xce, 0xce,
	0x67, 0xd8, 0xf8, 0xba, 0x15, 0xda, 0xb5, 0x3f, 0x2f, 0xc2, 0xaa, 0x10, 0x03, 0xb7, 0xf6, 0x9f,
	0x8d, 0xc0, 0xbb, 0x50, 0x62, 0x06, 0x03, 0x42, 0x75, 0xc3, 0xa1, 0x3c, 0x7f, 0xb1, 0x44, 0xd1,
	0xc6, 0xd7, 0x5d, 0x42, 0xeb, 0x0e, 0x65, 0x6e, 0x99, 0x81, 0x6e, 0x87, 0x01, 0xe5, 0xcd, 0x56,
	0x88, 0xdd, 0x32, 0x83, 0x66, 0x18, 0x50, 0xd6, 0xb9, 0x94, 0x5c, 0x53, 0xbd, 0xef, 0x5e, 0xeb,
	0x01, 0x1d, 0x5b, 0x44, 0x59, 0x12, 0xf4, 0x94, 0x19, 0xef, 0xc4, 0xbd, 0xee, 0x32, 0x0e, 0x7a,
	0x04, 0x68, 0x60, 0x06, 0x9e, 0x85, 0xc7, 0xba, 0xe9, 0xe8, 0x46, 0xd4, 0x60, 0xca, 0xb2, 0xa0,
	0x55, 0x9e, 0xf0, 0x93, 0xf6, 0x43, 0x5f, 0x8f, 0x42, 0xa4, 0x63, 0x8f, 0x28, 0x2b, 0x7b, 0xb9,
	0xfd, 0xea, 0xa3, 0x75, 0xd6, 0x79, 0x42, 0x26, 0x7a, 0x63, 0x8f, 0x08, 0x71, 0xb3, 0x9f, 0xe8,
	0x21, 0xac, 0xb3, 0x53, 0x86, 0xeb, 0x5c, 0xea, 0x42, 0x0d, 0x0b, 0xbc, 0x86, 0xb2, 0x1b, 0xa5,
	0xef, 0x49, 0x5c, 0x4a, 0xd4, 0x05, 0x85, 0x89, 0x5f, 0x99, 0x74, 0xa4, 0xb3, 0xd2, 0x8b, 0x67,
	0x8a, 0x7c, 0x80, 0x76, 0xa7, 0x8c, 0x8a, 0x2d, 0xa4, 0x6d, 0xb8, 0x29, 0x21, 0x55, 0x7a, 0x06,
	0x9b, 0x51, 0x93, 0x4e, 0x7b, 0x01, 0xe9, 0x48, 0xce, 0x4c, 0x8a, 0x86, 0xac, 0xf8, 0x31, 0xa3,
	0xc9, 0x64, 0x1d, 0x36, 0xa3, 0xa9, 0x34, 0xab, 0x29, 0x69, 0x45, 0x0d, 0x99, 0xf1, 0x63, 0xa2,
	0xa9, 0xf6, 0xfb, 0x02, 0xc8, 0xb1, 0x24, 0xe3, 0xa8, 0xce, 0xa5, 0x3b, 0x03, 0x52, 0xd2, 0xad,
	0x41, 0x2a, 0x77, 0x3b, 0x90, 0xca, 0xdf, 0x04, 0x52, 0xfb, 0x50, 0x19, 0xe1, 0x40, 0x77, 0x4c,
	0xe3, 0x59, 0xa4, 0x4c, 0xec, 0xae, 0xd2, 0x08, 0x07, 0x2d, 0xd3, 0x78, 0xc6, 0x75, 0x7d, 0x15,
	0xaa, 0x4c, 0x32, 0x30, 0x7c, 0x42, 0x9c, 0x60, 0xe4, 0x52, 0xde, 0x62, 0xb1, 0x28, 0xd3, 0xd2,
	0x4d, 0x58, 0xe8, 0x03, 0x40, 0xdc, 0x2c, 0x1d, 0x85, 0x76, 0x3f, 0x41, 0x92, 0x65, 0xc1, 0xd1,
	0x55, 0xc6, 0xef, 0x31, 0x76, 0x8a, 0x26, 0xfc, 0x48, 0x1f, 0x1b, 0xcf, 0x92, 0x13, 0x2b, 0x22,
	0x9a, 0x30, 0xf6, 0x09, 0x36, 0x9e, 0x4d, 0x1d, 0x30, 0x0d, 0xd7, 0x49, 0x0e, 0x14, 0xa6, 0x0f,
	0xa8, 0x86, 0xeb, 0x4c, 0x0e, 0x7c, 0x04, 0x4a, 0x94, 0x39, 0xd7, 0x77, 0x88, 0xaf, 0xdb, 0xd8,
	0x4f, 0x0d, 0x15, 0x85, 0x73, 0x1b, 0x3c, 0x87, 0x5c, 0xa8, 0x89, 0xfd, 0xd8, 0xdc, 0xb7, 0x60,
	0x2b, 0xeb, 0x9f, 0x6d, 0x3a, 0x26, 0x3f, 0x0b, 0xc2, 0x59, 0x24, 0x38, 0xd9, 0x34, 0x1d, 0x93,
	0x9d, 0xfc, 0x3e, 0xec, 0x0c, 0xc8, 0x25, 0x0e, 0x2d, 0x1a, 0xd5, 0x22, 0x0b, 0xaf, 0x25, 0xe1,
	0xf4, 0xd6, 0x44, 0x8e, 0x15, 0xe7, 0x5c, 0x40, 0xda, 0xaf, 0xc1, 0x7a, 0x6a, 0xdb, 0x70, 0x2d,
	0xd7, 0xe7, 0x00, 0x55, 0x16, 0x2a, 0x2a, 0xc7, 0x86, 0xeb, 0x8c, 0xcd, 0x90, 0xec, 0x9b, 0x50,
	0x49, 0xa6, 0x8f, 0xf7, 0x69, 0x85, 0xf7, 0xe9, 0xf4, 0xe0, 0xf2, 0x2e, 0x2d, 0x4d, 0x86, 0x91,
	0x37, 0xfa, 0x97, 0xa1, 0xc8, 0x9b, 0x8b, 0x4f, 0x7b, 0x55, 0xb0, 0x51, 0x60, 0x64, 0x3e, 0xd9,
	0x67, 0x80, 0x7c, 0x32, 0x34, 0x03, 0x4a, 0x7c, 0xfd, 0xb3, 0xd0, 0xf6, 0x74, 0x0f, 0x0f, 0x89,
	0xb2, 0xca, 0x91, 0x61, 0x83, 0x19, 0xd0, 0x26, 0xdc, 0x4f, 0x42, 0xdb, 0xeb, 0xe0, 0x61, 0x0c,
	0x0d, 0xb2, 0x3f, 0x45, 0x67, 0x0d, 0x18, 0x8c, 0xdc, 0x2b, 0x9d, 0x12, 0x6c, 0xf3, 0xa0, 0xe4,
	0x3d, 0x29, 0x6d, 0x40, 0xc6, 0xea, 0x11, 0x6c, 0xb3, 0x78, 0x0e, 0xa0, 0x9a, 0x4a, 0x32, 0x44,
	0x53, 0xd6, 0x04, 0x74, 0x2f, 0xc7, 0xa2, 0x3d, 0x72, 0x4d, 0xd1, 0x0e, 0x14, 0xb0, 0x61, 0xb8,
	0xa1, 0x43, 0x03, 0x05, 0x71, 0xb8, 0x49, 0x7e, 0xa3, 0x87, 0x50, 0x8a, 0x87, 0xc2, 0xb9, 0x74,
	0x95, 0x75, 0x9e, 0x95, 0x0a, 0x73, 0xfa, 0x82, 0x0f, 0x85, 0x73, 0xe9, 0x6a, 0xc5, 0x30, 0x7e,
	0x44, 0xdf, 0x05, 0xc4, 0xa6, 0xc3, 0xe1, 0xed, 0x3c, 0xf6, 0x26, 0x33, 0xbf, 0xc1, 0x4f, 0xc9,
	0xec, 0x54, 0x3c, 0x21, 0x2c, 0x31, 0x9a, 0x1c, 0xcb, 0xb2, 0x5f, 0x3c, 0x9b, 0xdf, 0x01, 0x85,
	0xcd, 0xcd, 0xf3, 0x90, 0x8d, 0x98, 0x8d, 0xa9, 0x31, 0xd2, 0x89, 0x43, 0x7d, 0xec, 0x18, 0x44,
	0xd9, 0x14, 0x26, 0x68, 0x73, 0x84, 0x83, 0x1f, 0x32, 0xa1, 0x26, 0x93, 0x69, 0x4c, 0x44, 0x6a,
	0x1d, 0x28, 0x8b, 0x06, 0xd8, 0x8b, 0x80, 0x7b, 0xc1, 0x11, 0x42, 0x78, 0x3f, 0x31, 0xa2, 0x3a,
	0x60, 0xb5, 0xe3, 0xec, 0x09, 0x30, 0xa4, 0xf9, 0x29, 0x30, 0x32, 0xd3, 0x52, 0xeb, 0x42, 0x31,
	0x09, 0x34, 0x8b, 0x10, 0xa2, 0xc2, 0x04, 0x21, 0xde, 0x4d, 0xd2, 0x35, 0xa3, 0x33, 0xca, 0x12,
	0x57, 0xea, 0xc1, 0xe6, 0x63, 0x42, 0x8f, 0x2d, 0x4b, 0xc4, 0xb5, 0x79, 0x76, 0xb4, 0x23, 0x58,
	0x33, 0x03, 0x9d, 0x17, 0x38, 0x42, 0x59, 0xd7, 0xa3, 0xdc, 0x58, 0x9c, 0x9f, 0xaa, 0x19, 0x74,
	0x47, 0xee, 0x15, 0x47, 0xd6, 0xb6, 0x47, 0x6b, 0x7f, 0x94, 0x60, 0xeb, 0x26, 0x93, 0x73, 0x2d,
	0x4a, 0xa8, 0x3e, 0x99, 0x2c, 0xde, 0xf0, 0xe9, 0xa8, 0xe4, 0x78, 0x79, 0x37, 0xc4, 0x51, 0x89,
	0x81, 0x3a, 0x9a, 0xb4, 0x98, 0xc2, 0x4b, 0x7c, 0x04, 0x6b, 0xec, 0x15, 0x2e, 0xc0, 0xf2, 0xd4,
	0xf6, 0x50, 0xb5, 0xf1, 0x75, 0xac, 0x87, 0x6d, 0x11, 0x9f, 0x4b, 0x50, 0x65, 0xbf, 0x05, 0xc4,
	0xfc, 0x0a, 0x94, 0x71, 0x38, 0x30, 0xa9, 0x1e, 0x50, 0x4c, 0xc3, 0x20, 0x03, 0xff, 0x25, 0xce,
	0xe9, 0x72, 0x06, 0xdf, 0x04, 0x6e, 0xd8, 0xcc, 0x96, 0xcd, 0x08, 0x2a, 0x76, 0x60, 0xc9, 0x74,
	0x06, 0xe4, 0x3a, 0x03, 0xf7, 0x11, 0x89, 0x1d, 0x75, 0xc8, 0x95, 0xee, 0xf6, 0x03, 0x65, 0x51,
	0xc8, 0xec, 0xb2, 0x43, 0xae, 0xda, 0xfd, 0xa0, 0xf6, 0x57, 0x09, 0x4a, 0xff, 0xaf, 0xd5, 0x3e,
	0xb3, 0x51, 0x30, 0x67, 0x6e, 0xb7, 0x51, 0x7c, 0x00, 0xc5, 0xa8, 0x3b, 0x5e, 0x60, 0x6b, 0xb2,
	0x54, 0x6f, 0xcc, 0xbc, 0x77, 0x9f, 0x60, 0x4b, 0x2b, 0x98, 0x93, 0xa7, 0xda, 0x4f, 0xf3, 0x50,
	0x4e, 0xd9, 0x6f, 0xec, 0x45, 0xfb, 0x1e, 0x54, 0xa2, 0x21, 0x9a, 0xe0, 0x03, 0x7f, 0xd1, 0x66,
	0x94, 0xb5, 0x26, 0x1c, 0xf4, 0x6d, 0x58, 0x4d, 0xdf, 0xb2, 0xe2, 0xfd, 0x01, 0xc5, 0xa1, 0xa6,
	0x7d, 0xa3, 0x55, 0x53, 0x51, 0x9e, 0xd7, 0x03, 0xe1, 0xd6, 0xb1, 0xcc, 0x4f, 0xad, 0x4e, 0xe5,
	0x35, 0xb9, 0x6f, 0xa0, 0x7d, 0x58, 0x64, 0x2d, 0xaf, 0xac, 0xec, 0x49, 0xaf, 0xec, 0x76, 0x2e,
	0x81, 0x4e, 0x26, 0xef, 0xf3, 0x04, 0x09, 0x93, 0x45, 0x4e, 0x38, 0x17, 0xe3, 0x54, 0x3a, 0x25,
	0x71, 0x48, 0x7c, 0xeb, 0xf9, 0x51, 0xba, 0xf4, 0xc4, 0x65, 0x62, 0x37, 0x12, 0x62, 0x11, 0x5b,
	0x9f, 0xdd, 0xd6, 0x8b, 0x8c, 0xde, 0xe3, 0x1b, 0xfb, 0x7d, 0x28, 0x70, 0x21, 0x56, 0x73, 0x11,
	0x7e, 0x56, 0x18, 0x95, 0x95, 0xf8, 0x59, 0xa4, 0x59, 0xb4, 0x8f, 0x3e, 0x84, 0x4a, 0x06, 0xb6,
	0x27, 0x38, 0x30, 0x8b, 0xd8, 0x65, 0x11, 0xb1, 0xd1, 0x1e, 0x14, 0x92, 0x0a, 0x89, 0xe5, 0x4e,
	0xa8, 0xb5, 0x9f, 0xb3, 0xd9, 0x25, 0x34, 0x0e, 0x65, 0x1e, 0x8c, 0x7b, 0x07, 0x80, 0x62, 0x7f,
	0x48, 0xa8, 0x1e, 0x4e, 0xcd, 0x4c, 0x31, 0xa2, 0x5f, 0x98, 0x83, 0x9b, 0x81, 0x30, 0xff, 0x1a,
	0x20, 0xfc, 0x8d, 0x04, 0xab, 0x19, 0x87, 0xe6, 0x43, 0xc0, 0x5b, 0x39, 0xf5, 0x0d, 0x71, 0x16,
	0x92, 0x81, 0x9e, 0xa4, 0x53, 0x9c, 0xae, 0x74, 0x2e, 0x78, 0xcd, 0x3f, 0x83, 0xb5, 0xba, 0x4f,
	0x30, 0x25, 0x77, 0x49, 0xd7, 0x43, 0x28, 0x26, 0x46, 0xb9, 0x63, 0x37, 0xd9, 0x2b, 0xc4, 0xf6,
	0x6a, 0xdf, 0x03, 0x34, 0x6d, 0x6b, 0xbe, 0x4b, 0xf3, 0x2f, 0x25, 0xa8, 0x76, 0xef, 0x56, 0xd9,
	0xf9, 0x5c, 0x65, 0x4b, 0xcf, 0x80, 0x58, 0x7a, 0x32, 0x52, 0x1c, 0x32, 0x92, 0xa5, 0x67, 0x40,
	0xac, 0xb8, 0x9d, 0x6b, 0x1f, 0xc1, 0x6a, 0xf7, 0xce, 0xb5, 0xad, 0x19, 0xb0, 0x76, 0x4a, 0x2c,
	0x72, 0xb7, 0xf4, 0xdf, 0xf2, 0xab, 0x09, 0xcb, 0xfb, 0xb4, 0x91, 0xf9, 0xbc, 0xfc, 0xad, 0x04,
	0xbb, 0xd9, 0xca, 0xa9, 0x4e, 0xbc, 0x3f, 0xce, 0xe3, 0xf0, 0x6c, 0x93, 0xe6, 0x6e, 0xd3, 0xa4,
	0x6c, 0x67, 0xea, 0x9b, 0x3e, 0x1d, 0xe9, 0x03, 0x3c, 0xe6, 0x93, 0x96, 0x0c, 0x3d, 0x27, 0x9f,
	0xe2, 0x71, 0x4d, 0x85, 0xb7, 0x5e, 0xed, 0xe1, 0x7c, 0xd1, 0xfe, 0x4c, 0x82, 0x75, 0x61, 0x5c,
	0x4f, 0xc6, 0x3d, 0xdc, 0x9f, 0x27, 0xca, 0x5d, 0x58, 0xa6, 0xb8, 0x1f, 0x15, 0x24, 0xdd, 0x30,
	0x96, 0x28, 0xee, 0xab, 0x77, 0x00, 0x8f, 0x31, 0x6c, 0xcc, 0x3a, 0x33, 0x1f, 0x80, 0xdc, 0x94,
	0x76, 0xe9, 0x16, 0xd8, 0xf0, 0x07, 0x09, 0x90, 0x60, 0x7b, 0xde, 0x85, 0x51, 0x85, 0xd5, 0x4b,
	0xd3, 0x62, 0x17, 0x90, 0x64, 0x9d, 0xce, 0xf1, 0xfb, 0xc7, 0x8e, 0x68, 0xf7, 0x63, 0x2e, 0x12,
	0x6f, 0xd3, 0x71, 0x12, 0x2e, 0x33, 0xd4, 0xf9, 0xb3, 0xf6, 0xeb, 0x5c, 0xa6, 0x86, 0x6f, 0x72,
	0xf1, 0x34, 0x42, 0xff, 0x75, 0x8b, 0xa7, 0x11, 0xfa, 0xc2, 0xe2, 0x89, 0x0e, 0x41, 0xe6, 0x61,
	0xfa, 0xc4, 0x20, 0x0e, 0xd5, 0x3d, 0x0b, 0x8f, 0x33, 0xab, 0x20, 0xbf, 0x61, 0x69, 0x9c, 0xd9,
	0xb1, 0xf0, 0xf8, 0xe6, 0xcd, 0x76, 0xe9, 0x35, 0x9b, 0xad, 0x0b, 0x5b, 0x27, 0xec, 0xfe, 0xf2,
	0x5f, 0x43, 0xfd, 0xfc, 0xbf, 0x41, 0xfd, 0x53, 0xd8, 0xbe, 0xd1, 0xe0, 0x7c, 0x43, 0xf9, 0x13,
	0xd8, 0x99, 0xaa, 0x27, 0x1f, 0x05, 0x75, 0x10, 0xcc, 0xe3, 0xfa, 0x36, 0xac, 0x44, 0xa3, 0x19,
	0x70, 0xc7, 0x2b, 0xda, 0x32, 0x9f, 0xca, 0xa0, 0xf6, 0x27, 0x09, 0x76, 0x5f, 0xa9, 0x7f, 0xbe,
	0xbe, 0xf9, 0xf8, 0x75, 0x7d, 0xb3, 0xcd, 0xb7, 0x1b, 0x72, 0xf5, 0xbf, 0xb8, 0xb3, 0xfc, 0x4e,
	0x82, 0xf5, 0x1b, 0x54, 0xbf, 0xa1, 0x75, 0x3a, 0x45, 0x41, 0xf1, 0xdb, 0x7b, 0x84, 0x82, 0x07,
	0xad, 0x74, 0xe3, 0xe7, 0x4b, 0xdd, 0x26, 0xac, 0xd5, 0x8f, 0xb5, 0x53, 0xbd, 0xf7, 0xe3, 0x4e,
	0x43, 0x57, 0x5b, 0x4f, 0x8e, 0xcf, 0xd5, 0x53, 0x79, 0x01, 0x21, 0xa8, 0xa6, 0xe4, 0xc7, 0xc7,
	0xcd, 0x86, 0x2c, 0xa1, 0x75, 0x58, 0x4d, 0x69, 0xcd, 0x8b, 0xae, 0x5a, 0x97, 0x73, 0x07, 0xbf,
	0x92, 0x32, 0x1f, 0x7d, 0xb9, 0xce, 0x2f, 0xc1, 0x26, 0x3b, 0xa2, 0x73, 0xe9, 0x76, 0xa7, 0x27,
	0xe8, 0x55, 0x60, 0x23, 0xcb, 0xaa, 0xb7, 0x9b, 0xcd, 0x76, 0x4b, 0x96, 0xd0, 0x36, 0xac, 0x67,
	0x39, 0xe7, 0x8d, 0x27, 0x8d, 0x73, 0x39, 0x87, 0x76, 0x61, 0x3b, 0xcb, 0x78, 0xaa, 0xf6, 0xce,
	0xf4, 0x8e, 0xd6, 0xee, 0xc8, 0xf9, 0xd9, 0x53, 0x6a, 0xab, 0x73, 0xd1, 0x93, 0x17, 0x0f, 0xde,
	0x07, 0x79, 0xfa, 0x3b, 0x0b, 0x2a, 0x43, 0xe1, 0xcc, 0xb5, 0x09, 0x7b, 0x96, 0x17, 0x50, 0x15,
	0x20, 0xfd, 0xa6, 0x20, 0x4b, 0x07, 0x16, 0x6c, 0x35, 0xa2, 0xcb, 0x43, 0x77, 0xe4, 0xd2, 0x63,
	0xe1, 0x5a, 0xa9, 0xc0, 0x46, 0xb7, 0xae, 0x35, 0x1a, 0x2d, 0xbd, 0x7b, 0xd6, 0xee, 0xe9, 0xc7,
	0x17, 0xa7, 0x6a, 0x4f, 0x6d, 0x3d, 0x96, 0x17, 0xd0, 0x5b, 0xa0, 0xcc, 0x70, 0x74, 0xad, 0xf1,
	0x49, 0xa3, 0xde, 0x93, 0x25, 0xb4, 0x03, 0x5b, 0xb3, 0xdc, 0xce, 0x71, 0xb7, 0x2b, 0xe7, 0x0e,
	0x74, 0xd8, 0xba, 0x19, 0x87, 0xd1, 0x06, 0xc8, 0x17, 0xad, 0x1f, 0xb4, 0xda, 0x4f, 0x5b, 0x7a,
	0xa3, 0xd5, 0xd3, 0x8e, 0x5b, 0xf5, 0x86, 0xbc, 0x80, 0x2a, 0x50, 0x3c, 0x6b, 0x37, 0x1b, 0x7a,
	0xe7, 0xf8, 0x31, 0xab, 0x45, 0x05, 0x8a, 0x3c, 0xee, 0x4f, 0xdb, 0xad, 0x86, 0x9c, 0x43, 0x6b,
	0x50, 0x69, 0xaa, 0x2d, 0x35, 0x25, 0xe5, 0x4f, 0xb4, 0x2f, 0x5e, 0xde, 0x93, 0xfe, 0xf2, 0xf2,
	0x9e, 0xf4, 0xf7, 0x97, 0xf7, 0xa4, 0x5f, 0xfc, 0xe3, 0xde, 0x02, 0x28, 0x86, 0x6b, 0x1f, 0x8e,
	0xcd, 0xb1, 0x1b, 0xb2, 0x51, 0xb0, 0xdd, 0x01, 0xb1, 0xa2, 0x7f, 0xaf, 0x3e, 0x7d, 0x30, 0x74,
	0x2d, 0xec, 0x0c, 0x0f, 0x3f, 0x7c, 0x44, 0xe9, 0xa1, 0xe1, 0xda, 0x47, 0x9c, 0x6c, 0xb8, 0xd6,
	0x11, 0xf6, 0xbc, 0x23, 0xd6, 0x6c, 0x0f, 0x59, 0x6b, 0xfe, 0x2b, 0x00, 0x00, 0xff, 0xff, 0x81,
	0x00, 0x32, 0x22, 0x0b, 0x1b, 0x00, 0x00,
}
