// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc_non_public/ugc_non_public.proto

package ugc_non_public // import "golang.52tt.com/protocol/app/ugc_non_public"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 附件
type NonPublicAttachmentType int32

const (
	NonPublicAttachmentType_NON_PUBLIC_ATTACHMENT_TYPE_UNSPECIFIED NonPublicAttachmentType = 0
	NonPublicAttachmentType_NON_PUBLIC_ATTACHMENT_TYPE_IMAGE       NonPublicAttachmentType = 1
	NonPublicAttachmentType_NON_PUBLIC_ATTACHMENT_TYPE_GIF         NonPublicAttachmentType = 2
	NonPublicAttachmentType_NON_PUBLIC_ATTACHMENT_TYPE_VIDEO       NonPublicAttachmentType = 3
	NonPublicAttachmentType_NON_PUBLIC_ATTACHMENT_TYPE_URL_CARD    NonPublicAttachmentType = 4
)

var NonPublicAttachmentType_name = map[int32]string{
	0: "NON_PUBLIC_ATTACHMENT_TYPE_UNSPECIFIED",
	1: "NON_PUBLIC_ATTACHMENT_TYPE_IMAGE",
	2: "NON_PUBLIC_ATTACHMENT_TYPE_GIF",
	3: "NON_PUBLIC_ATTACHMENT_TYPE_VIDEO",
	4: "NON_PUBLIC_ATTACHMENT_TYPE_URL_CARD",
}
var NonPublicAttachmentType_value = map[string]int32{
	"NON_PUBLIC_ATTACHMENT_TYPE_UNSPECIFIED": 0,
	"NON_PUBLIC_ATTACHMENT_TYPE_IMAGE":       1,
	"NON_PUBLIC_ATTACHMENT_TYPE_GIF":         2,
	"NON_PUBLIC_ATTACHMENT_TYPE_VIDEO":       3,
	"NON_PUBLIC_ATTACHMENT_TYPE_URL_CARD":    4,
}

func (x NonPublicAttachmentType) String() string {
	return proto.EnumName(NonPublicAttachmentType_name, int32(x))
}
func (NonPublicAttachmentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{0}
}

type NonPublicAbnormalStatus int32

const (
	NonPublicAbnormalStatus_NON_PUBLIC_ABNORMAL_STATUS_UNSPECIFIED  NonPublicAbnormalStatus = 0
	NonPublicAbnormalStatus_NON_PUBLIC_ABNORMAL_STATUS_BANNED       NonPublicAbnormalStatus = 1
	NonPublicAbnormalStatus_NON_PUBLIC_ABNORMAL_STATUS_UNDER_REVIEW NonPublicAbnormalStatus = 2
)

var NonPublicAbnormalStatus_name = map[int32]string{
	0: "NON_PUBLIC_ABNORMAL_STATUS_UNSPECIFIED",
	1: "NON_PUBLIC_ABNORMAL_STATUS_BANNED",
	2: "NON_PUBLIC_ABNORMAL_STATUS_UNDER_REVIEW",
}
var NonPublicAbnormalStatus_value = map[string]int32{
	"NON_PUBLIC_ABNORMAL_STATUS_UNSPECIFIED":  0,
	"NON_PUBLIC_ABNORMAL_STATUS_BANNED":       1,
	"NON_PUBLIC_ABNORMAL_STATUS_UNDER_REVIEW": 2,
}

func (x NonPublicAbnormalStatus) String() string {
	return proto.EnumName(NonPublicAbnormalStatus_name, int32(x))
}
func (NonPublicAbnormalStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{1}
}

type NonPublicAttachmentDownloadPrivacy int32

const (
	NonPublicAttachmentDownloadPrivacy_NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_UNSPECIFIED NonPublicAttachmentDownloadPrivacy = 0
	NonPublicAttachmentDownloadPrivacy_NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_PRIVATE     NonPublicAttachmentDownloadPrivacy = 1
	NonPublicAttachmentDownloadPrivacy_NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_PUBLIC      NonPublicAttachmentDownloadPrivacy = 2
)

var NonPublicAttachmentDownloadPrivacy_name = map[int32]string{
	0: "NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_UNSPECIFIED",
	1: "NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_PRIVATE",
	2: "NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_PUBLIC",
}
var NonPublicAttachmentDownloadPrivacy_value = map[string]int32{
	"NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_UNSPECIFIED": 0,
	"NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_PRIVATE":     1,
	"NON_PUBLIC_ATTACHMENT_DOWNLOAD_PRIVACY_PUBLIC":      2,
}

func (x NonPublicAttachmentDownloadPrivacy) String() string {
	return proto.EnumName(NonPublicAttachmentDownloadPrivacy_name, int32(x))
}
func (NonPublicAttachmentDownloadPrivacy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{2}
}

type SceneStreamType int32

const (
	SceneStreamType_SCENE_STREAM_TYPE_UNSPECIFIED                SceneStreamType = 0
	SceneStreamType_SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_CATEGORY  SceneStreamType = 1
	SceneStreamType_SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_TALK      SceneStreamType = 2
	SceneStreamType_SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_KNOWLEDGE SceneStreamType = 3
)

var SceneStreamType_name = map[int32]string{
	0: "SCENE_STREAM_TYPE_UNSPECIFIED",
	1: "SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_CATEGORY",
	2: "SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_TALK",
	3: "SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_KNOWLEDGE",
}
var SceneStreamType_value = map[string]int32{
	"SCENE_STREAM_TYPE_UNSPECIFIED":                0,
	"SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_CATEGORY":  1,
	"SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_TALK":      2,
	"SCENE_STREAM_TYPE_SOCIAL_COMMUNITY_KNOWLEDGE": 3,
}

func (x SceneStreamType) String() string {
	return proto.EnumName(SceneStreamType_name, int32(x))
}
func (SceneStreamType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{3}
}

type NonPublicPostPrivacyPolicy int32

const (
	NonPublicPostPrivacyPolicy_NON_PUBLIC_POST_PRIVACY_POLICY_UNSPECIFIED NonPublicPostPrivacyPolicy = 0
	NonPublicPostPrivacyPolicy_NON_PUBLIC_POST_PRIVACY_POLICY_PRIVATE     NonPublicPostPrivacyPolicy = 1
)

var NonPublicPostPrivacyPolicy_name = map[int32]string{
	0: "NON_PUBLIC_POST_PRIVACY_POLICY_UNSPECIFIED",
	1: "NON_PUBLIC_POST_PRIVACY_POLICY_PRIVATE",
}
var NonPublicPostPrivacyPolicy_value = map[string]int32{
	"NON_PUBLIC_POST_PRIVACY_POLICY_UNSPECIFIED": 0,
	"NON_PUBLIC_POST_PRIVACY_POLICY_PRIVATE":     1,
}

func (x NonPublicPostPrivacyPolicy) String() string {
	return proto.EnumName(NonPublicPostPrivacyPolicy_name, int32(x))
}
func (NonPublicPostPrivacyPolicy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{4}
}

type NonPublicPostMachineStatus int32

const (
	NonPublicPostMachineStatus_NON_PUBLIC_POST_MACHINE_STATUS_UNSPECIFIED NonPublicPostMachineStatus = 0
	NonPublicPostMachineStatus_NON_PUBLIC_POST_MACHINE_STATUS_SUSPICIOUS  NonPublicPostMachineStatus = 1
	NonPublicPostMachineStatus_NON_PUBLIC_POST_MACHINE_STATUS_NORMAL      NonPublicPostMachineStatus = 2
	NonPublicPostMachineStatus_NON_PUBLIC_POST_MACHINE_STATUS_REJECT      NonPublicPostMachineStatus = 3
)

var NonPublicPostMachineStatus_name = map[int32]string{
	0: "NON_PUBLIC_POST_MACHINE_STATUS_UNSPECIFIED",
	1: "NON_PUBLIC_POST_MACHINE_STATUS_SUSPICIOUS",
	2: "NON_PUBLIC_POST_MACHINE_STATUS_NORMAL",
	3: "NON_PUBLIC_POST_MACHINE_STATUS_REJECT",
}
var NonPublicPostMachineStatus_value = map[string]int32{
	"NON_PUBLIC_POST_MACHINE_STATUS_UNSPECIFIED": 0,
	"NON_PUBLIC_POST_MACHINE_STATUS_SUSPICIOUS":  1,
	"NON_PUBLIC_POST_MACHINE_STATUS_NORMAL":      2,
	"NON_PUBLIC_POST_MACHINE_STATUS_REJECT":      3,
}

func (x NonPublicPostMachineStatus) String() string {
	return proto.EnumName(NonPublicPostMachineStatus_name, int32(x))
}
func (NonPublicPostMachineStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{5}
}

type NonPublicInteractiveType int32

const (
	NonPublicInteractiveType_NON_PUBLIC_INTERACTIVE_TYPE_UNSPECIFIED               NonPublicInteractiveType = 0
	NonPublicInteractiveType_NON_PUBLIC_INTERACTIVE_TYPE_SOCIAL_COMMUNITY_COMMENT  NonPublicInteractiveType = 1
	NonPublicInteractiveType_NON_PUBLIC_INTERACTIVE_TYPE_SOCIAL_COMMUNITY_ATTITUDE NonPublicInteractiveType = 2
)

var NonPublicInteractiveType_name = map[int32]string{
	0: "NON_PUBLIC_INTERACTIVE_TYPE_UNSPECIFIED",
	1: "NON_PUBLIC_INTERACTIVE_TYPE_SOCIAL_COMMUNITY_COMMENT",
	2: "NON_PUBLIC_INTERACTIVE_TYPE_SOCIAL_COMMUNITY_ATTITUDE",
}
var NonPublicInteractiveType_value = map[string]int32{
	"NON_PUBLIC_INTERACTIVE_TYPE_UNSPECIFIED":               0,
	"NON_PUBLIC_INTERACTIVE_TYPE_SOCIAL_COMMUNITY_COMMENT":  1,
	"NON_PUBLIC_INTERACTIVE_TYPE_SOCIAL_COMMUNITY_ATTITUDE": 2,
}

func (x NonPublicInteractiveType) String() string {
	return proto.EnumName(NonPublicInteractiveType_name, int32(x))
}
func (NonPublicInteractiveType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{6}
}

type NonPublicPostOrigin int32

const (
	NonPublicPostOrigin_NON_PUBLIC_POST_ORIGIN_UNSPECIFIED      NonPublicPostOrigin = 0
	NonPublicPostOrigin_NON_PUBLIC_POST_ORIGIN_SOCIAL_COMMUNITY NonPublicPostOrigin = 1
)

var NonPublicPostOrigin_name = map[int32]string{
	0: "NON_PUBLIC_POST_ORIGIN_UNSPECIFIED",
	1: "NON_PUBLIC_POST_ORIGIN_SOCIAL_COMMUNITY",
}
var NonPublicPostOrigin_value = map[string]int32{
	"NON_PUBLIC_POST_ORIGIN_UNSPECIFIED":      0,
	"NON_PUBLIC_POST_ORIGIN_SOCIAL_COMMUNITY": 1,
}

func (x NonPublicPostOrigin) String() string {
	return proto.EnumName(NonPublicPostOrigin_name, int32(x))
}
func (NonPublicPostOrigin) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{7}
}

type SceneStreamOpt int32

const (
	SceneStreamOpt_SCENE_STREAM_OPT_UNSPECIFIED SceneStreamOpt = 0
	SceneStreamOpt_SCENE_STREAM_OPT_REQUIRED    SceneStreamOpt = 1
	SceneStreamOpt_SCENE_STREAM_OPT_OPTIONAL    SceneStreamOpt = 2
)

var SceneStreamOpt_name = map[int32]string{
	0: "SCENE_STREAM_OPT_UNSPECIFIED",
	1: "SCENE_STREAM_OPT_REQUIRED",
	2: "SCENE_STREAM_OPT_OPTIONAL",
}
var SceneStreamOpt_value = map[string]int32{
	"SCENE_STREAM_OPT_UNSPECIFIED": 0,
	"SCENE_STREAM_OPT_REQUIRED":    1,
	"SCENE_STREAM_OPT_OPTIONAL":    2,
}

func (x SceneStreamOpt) String() string {
	return proto.EnumName(SceneStreamOpt_name, int32(x))
}
func (SceneStreamOpt) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{8}
}

// 角标信息
type CornerType int32

const (
	CornerType_CORNER_TYPE_UNSPECIFIED CornerType = 0
	CornerType_CORNER_TYPE_CATEGORY    CornerType = 1
)

var CornerType_name = map[int32]string{
	0: "CORNER_TYPE_UNSPECIFIED",
	1: "CORNER_TYPE_CATEGORY",
}
var CornerType_value = map[string]int32{
	"CORNER_TYPE_UNSPECIFIED": 0,
	"CORNER_TYPE_CATEGORY":    1,
}

func (x CornerType) String() string {
	return proto.EnumName(CornerType_name, int32(x))
}
func (CornerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{9}
}

type PublishTextStyleType int32

const (
	PublishTextStyleType_PUBLISH_TEXT_STYLE_TYPE_UNSPECIFIED PublishTextStyleType = 0
	PublishTextStyleType_PUBLISH_TEXT_STYLE_TYPE_ROLE        PublishTextStyleType = 1
	PublishTextStyleType_PUBLISH_TEXT_STYLE_TYPE_CHANNEL     PublishTextStyleType = 2
)

var PublishTextStyleType_name = map[int32]string{
	0: "PUBLISH_TEXT_STYLE_TYPE_UNSPECIFIED",
	1: "PUBLISH_TEXT_STYLE_TYPE_ROLE",
	2: "PUBLISH_TEXT_STYLE_TYPE_CHANNEL",
}
var PublishTextStyleType_value = map[string]int32{
	"PUBLISH_TEXT_STYLE_TYPE_UNSPECIFIED": 0,
	"PUBLISH_TEXT_STYLE_TYPE_ROLE":        1,
	"PUBLISH_TEXT_STYLE_TYPE_CHANNEL":     2,
}

func (x PublishTextStyleType) String() string {
	return proto.EnumName(PublishTextStyleType_name, int32(x))
}
func (PublishTextStyleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{10}
}

// 表态
type NonPublicAttitudeType int32

const (
	NonPublicAttitudeType_NON_PUBLIC_ATTITUDE_TYPE_UNSPECIFIED   NonPublicAttitudeType = 0
	NonPublicAttitudeType_NON_PUBLIC_ATTITUDE_TYPE_LIKE          NonPublicAttitudeType = 1
	NonPublicAttitudeType_NON_PUBLIC_ATTITUDE_TYPE_ATTITUDE_NONE NonPublicAttitudeType = 2
)

var NonPublicAttitudeType_name = map[int32]string{
	0: "NON_PUBLIC_ATTITUDE_TYPE_UNSPECIFIED",
	1: "NON_PUBLIC_ATTITUDE_TYPE_LIKE",
	2: "NON_PUBLIC_ATTITUDE_TYPE_ATTITUDE_NONE",
}
var NonPublicAttitudeType_value = map[string]int32{
	"NON_PUBLIC_ATTITUDE_TYPE_UNSPECIFIED":   0,
	"NON_PUBLIC_ATTITUDE_TYPE_LIKE":          1,
	"NON_PUBLIC_ATTITUDE_TYPE_ATTITUDE_NONE": 2,
}

func (x NonPublicAttitudeType) String() string {
	return proto.EnumName(NonPublicAttitudeType_name, int32(x))
}
func (NonPublicAttitudeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{11}
}

type NonPublicStepOn int32

const (
	NonPublicStepOn_NON_PUBLIC_STEP_ON_UNSPECIFIED NonPublicStepOn = 0
	NonPublicStepOn_NON_PUBLIC_STEP_ON_STEP        NonPublicStepOn = 1
	NonPublicStepOn_NON_PUBLIC_STEP_ON_NONE        NonPublicStepOn = 2
)

var NonPublicStepOn_name = map[int32]string{
	0: "NON_PUBLIC_STEP_ON_UNSPECIFIED",
	1: "NON_PUBLIC_STEP_ON_STEP",
	2: "NON_PUBLIC_STEP_ON_NONE",
}
var NonPublicStepOn_value = map[string]int32{
	"NON_PUBLIC_STEP_ON_UNSPECIFIED": 0,
	"NON_PUBLIC_STEP_ON_STEP":        1,
	"NON_PUBLIC_STEP_ON_NONE":        2,
}

func (x NonPublicStepOn) String() string {
	return proto.EnumName(NonPublicStepOn_name, int32(x))
}
func (NonPublicStepOn) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{12}
}

type NonPublicStepType int32

const (
	NonPublicStepType_NON_PUBLIC_STEP_TYPE_UNSPECIFIED NonPublicStepType = 0
	NonPublicStepType_NON_PUBLIC_STEP_TYPE_MUSIC_STEP  NonPublicStepType = 1
)

var NonPublicStepType_name = map[int32]string{
	0: "NON_PUBLIC_STEP_TYPE_UNSPECIFIED",
	1: "NON_PUBLIC_STEP_TYPE_MUSIC_STEP",
}
var NonPublicStepType_value = map[string]int32{
	"NON_PUBLIC_STEP_TYPE_UNSPECIFIED": 0,
	"NON_PUBLIC_STEP_TYPE_MUSIC_STEP":  1,
}

func (x NonPublicStepType) String() string {
	return proto.EnumName(NonPublicStepType_name, int32(x))
}
func (NonPublicStepType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{13}
}

type ContentStreamPermission int32

const (
	ContentStreamPermission_CONTENT_STREAM_PERMISSION_UNSPECIFIED ContentStreamPermission = 0
	ContentStreamPermission_CONTENT_STREAM_PERMISSION_READ        ContentStreamPermission = 1
	ContentStreamPermission_CONTENT_STREAM_PERMISSION_PUBLISH     ContentStreamPermission = 2
	ContentStreamPermission_CONTENT_STREAM_PERMISSION_COMMENT     ContentStreamPermission = 4
	ContentStreamPermission_CONTENT_STREAM_PERMISSION_ATTITUDE    ContentStreamPermission = 8
)

var ContentStreamPermission_name = map[int32]string{
	0: "CONTENT_STREAM_PERMISSION_UNSPECIFIED",
	1: "CONTENT_STREAM_PERMISSION_READ",
	2: "CONTENT_STREAM_PERMISSION_PUBLISH",
	4: "CONTENT_STREAM_PERMISSION_COMMENT",
	8: "CONTENT_STREAM_PERMISSION_ATTITUDE",
}
var ContentStreamPermission_value = map[string]int32{
	"CONTENT_STREAM_PERMISSION_UNSPECIFIED": 0,
	"CONTENT_STREAM_PERMISSION_READ":        1,
	"CONTENT_STREAM_PERMISSION_PUBLISH":     2,
	"CONTENT_STREAM_PERMISSION_COMMENT":     4,
	"CONTENT_STREAM_PERMISSION_ATTITUDE":    8,
}

func (x ContentStreamPermission) String() string {
	return proto.EnumName(ContentStreamPermission_name, int32(x))
}
func (ContentStreamPermission) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{14}
}

type NonPublicPostInfo_PostStatus int32

const (
	NonPublicPostInfo_POST_STATUS_UNSPECIFIED NonPublicPostInfo_PostStatus = 0
	NonPublicPostInfo_POST_STATUS_SUSPICIOUS  NonPublicPostInfo_PostStatus = 1
	NonPublicPostInfo_POST_STATUS_FAIL        NonPublicPostInfo_PostStatus = 2
)

var NonPublicPostInfo_PostStatus_name = map[int32]string{
	0: "POST_STATUS_UNSPECIFIED",
	1: "POST_STATUS_SUSPICIOUS",
	2: "POST_STATUS_FAIL",
}
var NonPublicPostInfo_PostStatus_value = map[string]int32{
	"POST_STATUS_UNSPECIFIED": 0,
	"POST_STATUS_SUSPICIOUS":  1,
	"POST_STATUS_FAIL":        2,
}

func (x NonPublicPostInfo_PostStatus) String() string {
	return proto.EnumName(NonPublicPostInfo_PostStatus_name, int32(x))
}
func (NonPublicPostInfo_PostStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{10, 0}
}

type NonPublicAttachment struct {
	AttachmentType    uint32 `protobuf:"varint,1,opt,name=attachment_type,json=attachmentType,proto3" json:"attachment_type,omitempty"`
	AttachmentContent string `protobuf:"bytes,2,opt,name=attachment_content,json=attachmentContent,proto3" json:"attachment_content,omitempty"`
	ExtraInfo         string `protobuf:"bytes,3,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	CreateAt          uint64 `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	Key               string `protobuf:"bytes,5,opt,name=key,proto3" json:"key,omitempty"`
	VmContent         string `protobuf:"bytes,6,opt,name=vm_content,json=vmContent,proto3" json:"vm_content,omitempty"`
	// 只有type = VIDEO 才有的
	Param string `protobuf:"bytes,7,opt,name=param,proto3" json:"param,omitempty"`
	// 原视频封面
	OriginVideoCover string `protobuf:"bytes,8,opt,name=origin_video_cover,json=originVideoCover,proto3" json:"origin_video_cover,omitempty"`
	// extra 额外信息
	Extra                *NonPublicExtra `protobuf:"bytes,9,opt,name=extra,proto3" json:"extra,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *NonPublicAttachment) Reset()         { *m = NonPublicAttachment{} }
func (m *NonPublicAttachment) String() string { return proto.CompactTextString(m) }
func (*NonPublicAttachment) ProtoMessage()    {}
func (*NonPublicAttachment) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{0}
}
func (m *NonPublicAttachment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicAttachment.Unmarshal(m, b)
}
func (m *NonPublicAttachment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicAttachment.Marshal(b, m, deterministic)
}
func (dst *NonPublicAttachment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicAttachment.Merge(dst, src)
}
func (m *NonPublicAttachment) XXX_Size() int {
	return xxx_messageInfo_NonPublicAttachment.Size(m)
}
func (m *NonPublicAttachment) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicAttachment.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicAttachment proto.InternalMessageInfo

func (m *NonPublicAttachment) GetAttachmentType() uint32 {
	if m != nil {
		return m.AttachmentType
	}
	return 0
}

func (m *NonPublicAttachment) GetAttachmentContent() string {
	if m != nil {
		return m.AttachmentContent
	}
	return ""
}

func (m *NonPublicAttachment) GetExtraInfo() string {
	if m != nil {
		return m.ExtraInfo
	}
	return ""
}

func (m *NonPublicAttachment) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *NonPublicAttachment) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *NonPublicAttachment) GetVmContent() string {
	if m != nil {
		return m.VmContent
	}
	return ""
}

func (m *NonPublicAttachment) GetParam() string {
	if m != nil {
		return m.Param
	}
	return ""
}

func (m *NonPublicAttachment) GetOriginVideoCover() string {
	if m != nil {
		return m.OriginVideoCover
	}
	return ""
}

func (m *NonPublicAttachment) GetExtra() *NonPublicExtra {
	if m != nil {
		return m.Extra
	}
	return nil
}

type NonPublicExtra struct {
	Width                uint32   `protobuf:"varint,1,opt,name=width,proto3" json:"width,omitempty"`
	Heigth               uint32   `protobuf:"varint,2,opt,name=heigth,proto3" json:"heigth,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicExtra) Reset()         { *m = NonPublicExtra{} }
func (m *NonPublicExtra) String() string { return proto.CompactTextString(m) }
func (*NonPublicExtra) ProtoMessage()    {}
func (*NonPublicExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{1}
}
func (m *NonPublicExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicExtra.Unmarshal(m, b)
}
func (m *NonPublicExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicExtra.Marshal(b, m, deterministic)
}
func (dst *NonPublicExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicExtra.Merge(dst, src)
}
func (m *NonPublicExtra) XXX_Size() int {
	return xxx_messageInfo_NonPublicExtra.Size(m)
}
func (m *NonPublicExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicExtra.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicExtra proto.InternalMessageInfo

func (m *NonPublicExtra) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *NonPublicExtra) GetHeigth() uint32 {
	if m != nil {
		return m.Heigth
	}
	return 0
}

type NonPublicRichTextWords struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicRichTextWords) Reset()         { *m = NonPublicRichTextWords{} }
func (m *NonPublicRichTextWords) String() string { return proto.CompactTextString(m) }
func (*NonPublicRichTextWords) ProtoMessage()    {}
func (*NonPublicRichTextWords) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{2}
}
func (m *NonPublicRichTextWords) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicRichTextWords.Unmarshal(m, b)
}
func (m *NonPublicRichTextWords) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicRichTextWords.Marshal(b, m, deterministic)
}
func (dst *NonPublicRichTextWords) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicRichTextWords.Merge(dst, src)
}
func (m *NonPublicRichTextWords) XXX_Size() int {
	return xxx_messageInfo_NonPublicRichTextWords.Size(m)
}
func (m *NonPublicRichTextWords) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicRichTextWords.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicRichTextWords proto.InternalMessageInfo

func (m *NonPublicRichTextWords) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type NonPublicURLCard struct {
	ShowText             string   `protobuf:"bytes,1,opt,name=show_text,json=showText,proto3" json:"show_text,omitempty"`
	ShowIconUrl          string   `protobuf:"bytes,2,opt,name=show_icon_url,json=showIconUrl,proto3" json:"show_icon_url,omitempty"`
	ShowPlatformName     string   `protobuf:"bytes,3,opt,name=show_platform_name,json=showPlatformName,proto3" json:"show_platform_name,omitempty"`
	ShowPlatformIcon     string   `protobuf:"bytes,4,opt,name=show_platform_icon,json=showPlatformIcon,proto3" json:"show_platform_icon,omitempty"`
	RealUrlAddr          string   `protobuf:"bytes,5,opt,name=real_url_addr,json=realUrlAddr,proto3" json:"real_url_addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicURLCard) Reset()         { *m = NonPublicURLCard{} }
func (m *NonPublicURLCard) String() string { return proto.CompactTextString(m) }
func (*NonPublicURLCard) ProtoMessage()    {}
func (*NonPublicURLCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{3}
}
func (m *NonPublicURLCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicURLCard.Unmarshal(m, b)
}
func (m *NonPublicURLCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicURLCard.Marshal(b, m, deterministic)
}
func (dst *NonPublicURLCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicURLCard.Merge(dst, src)
}
func (m *NonPublicURLCard) XXX_Size() int {
	return xxx_messageInfo_NonPublicURLCard.Size(m)
}
func (m *NonPublicURLCard) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicURLCard.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicURLCard proto.InternalMessageInfo

func (m *NonPublicURLCard) GetShowText() string {
	if m != nil {
		return m.ShowText
	}
	return ""
}

func (m *NonPublicURLCard) GetShowIconUrl() string {
	if m != nil {
		return m.ShowIconUrl
	}
	return ""
}

func (m *NonPublicURLCard) GetShowPlatformName() string {
	if m != nil {
		return m.ShowPlatformName
	}
	return ""
}

func (m *NonPublicURLCard) GetShowPlatformIcon() string {
	if m != nil {
		return m.ShowPlatformIcon
	}
	return ""
}

func (m *NonPublicURLCard) GetRealUrlAddr() string {
	if m != nil {
		return m.RealUrlAddr
	}
	return ""
}

type NonPublicRichTextElement struct {
	// Types that are valid to be assigned to Content:
	//	*NonPublicRichTextElement_Words
	//	*NonPublicRichTextElement_MultiMedia
	//	*NonPublicRichTextElement_UrlCard
	Content              isNonPublicRichTextElement_Content `protobuf_oneof:"content"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *NonPublicRichTextElement) Reset()         { *m = NonPublicRichTextElement{} }
func (m *NonPublicRichTextElement) String() string { return proto.CompactTextString(m) }
func (*NonPublicRichTextElement) ProtoMessage()    {}
func (*NonPublicRichTextElement) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{4}
}
func (m *NonPublicRichTextElement) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicRichTextElement.Unmarshal(m, b)
}
func (m *NonPublicRichTextElement) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicRichTextElement.Marshal(b, m, deterministic)
}
func (dst *NonPublicRichTextElement) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicRichTextElement.Merge(dst, src)
}
func (m *NonPublicRichTextElement) XXX_Size() int {
	return xxx_messageInfo_NonPublicRichTextElement.Size(m)
}
func (m *NonPublicRichTextElement) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicRichTextElement.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicRichTextElement proto.InternalMessageInfo

type isNonPublicRichTextElement_Content interface {
	isNonPublicRichTextElement_Content()
}

type NonPublicRichTextElement_Words struct {
	Words *NonPublicRichTextWords `protobuf:"bytes,1,opt,name=words,proto3,oneof"`
}

type NonPublicRichTextElement_MultiMedia struct {
	MultiMedia *NonPublicAttachment `protobuf:"bytes,2,opt,name=multi_media,json=multiMedia,proto3,oneof"`
}

type NonPublicRichTextElement_UrlCard struct {
	UrlCard *NonPublicURLCard `protobuf:"bytes,3,opt,name=url_card,json=urlCard,proto3,oneof"`
}

func (*NonPublicRichTextElement_Words) isNonPublicRichTextElement_Content() {}

func (*NonPublicRichTextElement_MultiMedia) isNonPublicRichTextElement_Content() {}

func (*NonPublicRichTextElement_UrlCard) isNonPublicRichTextElement_Content() {}

func (m *NonPublicRichTextElement) GetContent() isNonPublicRichTextElement_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *NonPublicRichTextElement) GetWords() *NonPublicRichTextWords {
	if x, ok := m.GetContent().(*NonPublicRichTextElement_Words); ok {
		return x.Words
	}
	return nil
}

func (m *NonPublicRichTextElement) GetMultiMedia() *NonPublicAttachment {
	if x, ok := m.GetContent().(*NonPublicRichTextElement_MultiMedia); ok {
		return x.MultiMedia
	}
	return nil
}

func (m *NonPublicRichTextElement) GetUrlCard() *NonPublicURLCard {
	if x, ok := m.GetContent().(*NonPublicRichTextElement_UrlCard); ok {
		return x.UrlCard
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*NonPublicRichTextElement) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _NonPublicRichTextElement_OneofMarshaler, _NonPublicRichTextElement_OneofUnmarshaler, _NonPublicRichTextElement_OneofSizer, []interface{}{
		(*NonPublicRichTextElement_Words)(nil),
		(*NonPublicRichTextElement_MultiMedia)(nil),
		(*NonPublicRichTextElement_UrlCard)(nil),
	}
}

func _NonPublicRichTextElement_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*NonPublicRichTextElement)
	// content
	switch x := m.Content.(type) {
	case *NonPublicRichTextElement_Words:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Words); err != nil {
			return err
		}
	case *NonPublicRichTextElement_MultiMedia:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.MultiMedia); err != nil {
			return err
		}
	case *NonPublicRichTextElement_UrlCard:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UrlCard); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("NonPublicRichTextElement.Content has unexpected type %T", x)
	}
	return nil
}

func _NonPublicRichTextElement_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*NonPublicRichTextElement)
	switch tag {
	case 1: // content.words
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(NonPublicRichTextWords)
		err := b.DecodeMessage(msg)
		m.Content = &NonPublicRichTextElement_Words{msg}
		return true, err
	case 2: // content.multi_media
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(NonPublicAttachment)
		err := b.DecodeMessage(msg)
		m.Content = &NonPublicRichTextElement_MultiMedia{msg}
		return true, err
	case 3: // content.url_card
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(NonPublicURLCard)
		err := b.DecodeMessage(msg)
		m.Content = &NonPublicRichTextElement_UrlCard{msg}
		return true, err
	default:
		return false, nil
	}
}

func _NonPublicRichTextElement_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*NonPublicRichTextElement)
	// content
	switch x := m.Content.(type) {
	case *NonPublicRichTextElement_Words:
		s := proto.Size(x.Words)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *NonPublicRichTextElement_MultiMedia:
		s := proto.Size(x.MultiMedia)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *NonPublicRichTextElement_UrlCard:
		s := proto.Size(x.UrlCard)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type SceneStream struct {
	StreamId               string   `protobuf:"bytes,1,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	ShortName              string   `protobuf:"bytes,2,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty"`
	CompleteText           string   `protobuf:"bytes,3,opt,name=complete_text,json=completeText,proto3" json:"complete_text,omitempty"`
	Logo                   string   `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`
	StreamType             uint32   `protobuf:"varint,5,opt,name=stream_type,json=streamType,proto3" json:"stream_type,omitempty"`
	Opt                    uint32   `protobuf:"varint,6,opt,name=opt,proto3" json:"opt,omitempty"`
	CategoryTypeSimpleDesc string   `protobuf:"bytes,7,opt,name=category_type_simple_desc,json=categoryTypeSimpleDesc,proto3" json:"category_type_simple_desc,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *SceneStream) Reset()         { *m = SceneStream{} }
func (m *SceneStream) String() string { return proto.CompactTextString(m) }
func (*SceneStream) ProtoMessage()    {}
func (*SceneStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{5}
}
func (m *SceneStream) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SceneStream.Unmarshal(m, b)
}
func (m *SceneStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SceneStream.Marshal(b, m, deterministic)
}
func (dst *SceneStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SceneStream.Merge(dst, src)
}
func (m *SceneStream) XXX_Size() int {
	return xxx_messageInfo_SceneStream.Size(m)
}
func (m *SceneStream) XXX_DiscardUnknown() {
	xxx_messageInfo_SceneStream.DiscardUnknown(m)
}

var xxx_messageInfo_SceneStream proto.InternalMessageInfo

func (m *SceneStream) GetStreamId() string {
	if m != nil {
		return m.StreamId
	}
	return ""
}

func (m *SceneStream) GetShortName() string {
	if m != nil {
		return m.ShortName
	}
	return ""
}

func (m *SceneStream) GetCompleteText() string {
	if m != nil {
		return m.CompleteText
	}
	return ""
}

func (m *SceneStream) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *SceneStream) GetStreamType() uint32 {
	if m != nil {
		return m.StreamType
	}
	return 0
}

func (m *SceneStream) GetOpt() uint32 {
	if m != nil {
		return m.Opt
	}
	return 0
}

func (m *SceneStream) GetCategoryTypeSimpleDesc() string {
	if m != nil {
		return m.CategoryTypeSimpleDesc
	}
	return ""
}

// 帖子
type SceneFeedPost struct {
	Post                 *NonPublicPostInfo `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	TopComment           *TopComment        `protobuf:"bytes,2,opt,name=top_comment,json=topComment,proto3" json:"top_comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SceneFeedPost) Reset()         { *m = SceneFeedPost{} }
func (m *SceneFeedPost) String() string { return proto.CompactTextString(m) }
func (*SceneFeedPost) ProtoMessage()    {}
func (*SceneFeedPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{6}
}
func (m *SceneFeedPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SceneFeedPost.Unmarshal(m, b)
}
func (m *SceneFeedPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SceneFeedPost.Marshal(b, m, deterministic)
}
func (dst *SceneFeedPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SceneFeedPost.Merge(dst, src)
}
func (m *SceneFeedPost) XXX_Size() int {
	return xxx_messageInfo_SceneFeedPost.Size(m)
}
func (m *SceneFeedPost) XXX_DiscardUnknown() {
	xxx_messageInfo_SceneFeedPost.DiscardUnknown(m)
}

var xxx_messageInfo_SceneFeedPost proto.InternalMessageInfo

func (m *SceneFeedPost) GetPost() *NonPublicPostInfo {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *SceneFeedPost) GetTopComment() *TopComment {
	if m != nil {
		return m.TopComment
	}
	return nil
}

// SceneFeed
type SceneFeed struct {
	// Types that are valid to be assigned to FeedData:
	//	*SceneFeed_ScenePost
	FeedData             isSceneFeed_FeedData `protobuf_oneof:"feed_data"`
	FeedId               string               `protobuf:"bytes,30,opt,name=feed_id,json=feedId,proto3" json:"feed_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SceneFeed) Reset()         { *m = SceneFeed{} }
func (m *SceneFeed) String() string { return proto.CompactTextString(m) }
func (*SceneFeed) ProtoMessage()    {}
func (*SceneFeed) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{7}
}
func (m *SceneFeed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SceneFeed.Unmarshal(m, b)
}
func (m *SceneFeed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SceneFeed.Marshal(b, m, deterministic)
}
func (dst *SceneFeed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SceneFeed.Merge(dst, src)
}
func (m *SceneFeed) XXX_Size() int {
	return xxx_messageInfo_SceneFeed.Size(m)
}
func (m *SceneFeed) XXX_DiscardUnknown() {
	xxx_messageInfo_SceneFeed.DiscardUnknown(m)
}

var xxx_messageInfo_SceneFeed proto.InternalMessageInfo

type isSceneFeed_FeedData interface {
	isSceneFeed_FeedData()
}

type SceneFeed_ScenePost struct {
	ScenePost *SceneFeedPost `protobuf:"bytes,1,opt,name=scene_post,json=scenePost,proto3,oneof"`
}

func (*SceneFeed_ScenePost) isSceneFeed_FeedData() {}

func (m *SceneFeed) GetFeedData() isSceneFeed_FeedData {
	if m != nil {
		return m.FeedData
	}
	return nil
}

func (m *SceneFeed) GetScenePost() *SceneFeedPost {
	if x, ok := m.GetFeedData().(*SceneFeed_ScenePost); ok {
		return x.ScenePost
	}
	return nil
}

func (m *SceneFeed) GetFeedId() string {
	if m != nil {
		return m.FeedId
	}
	return ""
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*SceneFeed) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _SceneFeed_OneofMarshaler, _SceneFeed_OneofUnmarshaler, _SceneFeed_OneofSizer, []interface{}{
		(*SceneFeed_ScenePost)(nil),
	}
}

func _SceneFeed_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*SceneFeed)
	// feed_data
	switch x := m.FeedData.(type) {
	case *SceneFeed_ScenePost:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ScenePost); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("SceneFeed.FeedData has unexpected type %T", x)
	}
	return nil
}

func _SceneFeed_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*SceneFeed)
	switch tag {
	case 1: // feed_data.scene_post
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SceneFeedPost)
		err := b.DecodeMessage(msg)
		m.FeedData = &SceneFeed_ScenePost{msg}
		return true, err
	default:
		return false, nil
	}
}

func _SceneFeed_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*SceneFeed)
	// feed_data
	switch x := m.FeedData.(type) {
	case *SceneFeed_ScenePost:
		s := proto.Size(x.ScenePost)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type LastFeedInfo struct {
	Time                 uint64   `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Score                float64  `protobuf:"fixed64,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LastFeedInfo) Reset()         { *m = LastFeedInfo{} }
func (m *LastFeedInfo) String() string { return proto.CompactTextString(m) }
func (*LastFeedInfo) ProtoMessage()    {}
func (*LastFeedInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{8}
}
func (m *LastFeedInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LastFeedInfo.Unmarshal(m, b)
}
func (m *LastFeedInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LastFeedInfo.Marshal(b, m, deterministic)
}
func (dst *LastFeedInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LastFeedInfo.Merge(dst, src)
}
func (m *LastFeedInfo) XXX_Size() int {
	return xxx_messageInfo_LastFeedInfo.Size(m)
}
func (m *LastFeedInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LastFeedInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LastFeedInfo proto.InternalMessageInfo

func (m *LastFeedInfo) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *LastFeedInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *LastFeedInfo) GetScore() float64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type NonPublicNewFeedsLoadMore struct {
	FeedsName            string   `protobuf:"bytes,1,opt,name=feeds_name,json=feedsName,proto3" json:"feeds_name,omitempty"`
	LastFeedInfo         []byte   `protobuf:"bytes,2,opt,name=last_feed_info,json=lastFeedInfo,proto3" json:"last_feed_info,omitempty"`
	LastPage             uint32   `protobuf:"varint,3,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,4,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	FollowingFeedEmpty   bool     `protobuf:"varint,10,opt,name=following_feed_empty,json=followingFeedEmpty,proto3" json:"following_feed_empty,omitempty"`
	RequestRecommendFeed bool     `protobuf:"varint,11,opt,name=request_recommend_feed,json=requestRecommendFeed,proto3" json:"request_recommend_feed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicNewFeedsLoadMore) Reset()         { *m = NonPublicNewFeedsLoadMore{} }
func (m *NonPublicNewFeedsLoadMore) String() string { return proto.CompactTextString(m) }
func (*NonPublicNewFeedsLoadMore) ProtoMessage()    {}
func (*NonPublicNewFeedsLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{9}
}
func (m *NonPublicNewFeedsLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicNewFeedsLoadMore.Unmarshal(m, b)
}
func (m *NonPublicNewFeedsLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicNewFeedsLoadMore.Marshal(b, m, deterministic)
}
func (dst *NonPublicNewFeedsLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicNewFeedsLoadMore.Merge(dst, src)
}
func (m *NonPublicNewFeedsLoadMore) XXX_Size() int {
	return xxx_messageInfo_NonPublicNewFeedsLoadMore.Size(m)
}
func (m *NonPublicNewFeedsLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicNewFeedsLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicNewFeedsLoadMore proto.InternalMessageInfo

func (m *NonPublicNewFeedsLoadMore) GetFeedsName() string {
	if m != nil {
		return m.FeedsName
	}
	return ""
}

func (m *NonPublicNewFeedsLoadMore) GetLastFeedInfo() []byte {
	if m != nil {
		return m.LastFeedInfo
	}
	return nil
}

func (m *NonPublicNewFeedsLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *NonPublicNewFeedsLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

func (m *NonPublicNewFeedsLoadMore) GetFollowingFeedEmpty() bool {
	if m != nil {
		return m.FollowingFeedEmpty
	}
	return false
}

func (m *NonPublicNewFeedsLoadMore) GetRequestRecommendFeed() bool {
	if m != nil {
		return m.RequestRecommendFeed
	}
	return false
}

// 帖子详情
type NonPublicPostInfo struct {
	PostId         string             `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommonUserInfo *CommonUserUGCInfo `protobuf:"bytes,2,opt,name=common_user_info,json=commonUserInfo,proto3" json:"common_user_info,omitempty"`
	// Types that are valid to be assigned to ExtendUserInfo:
	//	*NonPublicPostInfo_UserInfo
	ExtendUserInfo isNonPublicPostInfo_ExtendUserInfo `protobuf_oneof:"extend_user_info"`
	// Types that are valid to be assigned to PostExtendInfo:
	//	*NonPublicPostInfo_SocialCommunityExtendInfo
	PostExtendInfo isNonPublicPostInfo_PostExtendInfo `protobuf_oneof:"post_extend_info"`
	Title          string                             `protobuf:"bytes,100,opt,name=title,proto3" json:"title,omitempty"`
	ContentList    []*NonPublicRichTextElement        `protobuf:"bytes,101,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	PostTime       uint64                             `protobuf:"varint,102,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	PostUpdateTime uint64                             `protobuf:"varint,103,opt,name=post_update_time,json=postUpdateTime,proto3" json:"post_update_time,omitempty"`
	CommentCount   uint32                             `protobuf:"varint,104,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	AttitudeCount  uint32                             `protobuf:"varint,105,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	ViewCount      uint32                             `protobuf:"varint,106,opt,name=view_count,json=viewCount,proto3" json:"view_count,omitempty"`
	MyAttitude     uint32                             `protobuf:"varint,107,opt,name=my_attitude,json=myAttitude,proto3" json:"my_attitude,omitempty"`
	IsDeleted      bool                               `protobuf:"varint,108,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	ShareCount     uint32                             `protobuf:"varint,109,opt,name=share_count,json=shareCount,proto3" json:"share_count,omitempty"`
	HadFavoured    bool                               `protobuf:"varint,110,opt,name=had_favoured,json=hadFavoured,proto3" json:"had_favoured,omitempty"`
	IsSticky       bool                               `protobuf:"varint,111,opt,name=is_sticky,json=isSticky,proto3" json:"is_sticky,omitempty"`
	AbnormalState  uint32                             `protobuf:"varint,112,opt,name=abnormal_state,json=abnormalState,proto3" json:"abnormal_state,omitempty"`
	PostSource     uint32                             `protobuf:"varint,113,opt,name=post_source,json=postSource,proto3" json:"post_source,omitempty"`
	Privacy        uint32                             `protobuf:"varint,114,opt,name=privacy,proto3" json:"privacy,omitempty"`
	PageSource     uint32                             `protobuf:"varint,115,opt,name=page_source,json=pageSource,proto3" json:"page_source,omitempty"`
	// TT5.4.2
	PostPrivacyPolicy uint32 `protobuf:"varint,116,opt,name=post_privacy_policy,json=postPrivacyPolicy,proto3" json:"post_privacy_policy,omitempty"`
	// TT5.4.3 发帖来源
	Origin            uint32 `protobuf:"varint,117,opt,name=origin,proto3" json:"origin,omitempty"`
	PostStatus        uint32 `protobuf:"varint,118,opt,name=post_status,json=postStatus,proto3" json:"post_status,omitempty"`
	PostMachineStatus uint32 `protobuf:"varint,119,opt,name=post_machine_status,json=postMachineStatus,proto3" json:"post_machine_status,omitempty"`
	LabelRemind       string `protobuf:"bytes,123,opt,name=label_remind,json=labelRemind,proto3" json:"label_remind,omitempty"`
	// ip 归属地
	IpLocation           string   `protobuf:"bytes,124,opt,name=ip_location,json=ipLocation,proto3" json:"ip_location,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicPostInfo) Reset()         { *m = NonPublicPostInfo{} }
func (m *NonPublicPostInfo) String() string { return proto.CompactTextString(m) }
func (*NonPublicPostInfo) ProtoMessage()    {}
func (*NonPublicPostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{10}
}
func (m *NonPublicPostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicPostInfo.Unmarshal(m, b)
}
func (m *NonPublicPostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicPostInfo.Marshal(b, m, deterministic)
}
func (dst *NonPublicPostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicPostInfo.Merge(dst, src)
}
func (m *NonPublicPostInfo) XXX_Size() int {
	return xxx_messageInfo_NonPublicPostInfo.Size(m)
}
func (m *NonPublicPostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicPostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicPostInfo proto.InternalMessageInfo

func (m *NonPublicPostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *NonPublicPostInfo) GetCommonUserInfo() *CommonUserUGCInfo {
	if m != nil {
		return m.CommonUserInfo
	}
	return nil
}

type isNonPublicPostInfo_ExtendUserInfo interface {
	isNonPublicPostInfo_ExtendUserInfo()
}

type NonPublicPostInfo_UserInfo struct {
	UserInfo *SocialCommunityUserUGCInfo `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3,oneof"`
}

func (*NonPublicPostInfo_UserInfo) isNonPublicPostInfo_ExtendUserInfo() {}

func (m *NonPublicPostInfo) GetExtendUserInfo() isNonPublicPostInfo_ExtendUserInfo {
	if m != nil {
		return m.ExtendUserInfo
	}
	return nil
}

func (m *NonPublicPostInfo) GetUserInfo() *SocialCommunityUserUGCInfo {
	if x, ok := m.GetExtendUserInfo().(*NonPublicPostInfo_UserInfo); ok {
		return x.UserInfo
	}
	return nil
}

type isNonPublicPostInfo_PostExtendInfo interface {
	isNonPublicPostInfo_PostExtendInfo()
}

type NonPublicPostInfo_SocialCommunityExtendInfo struct {
	SocialCommunityExtendInfo *PostExtendSocialCommunity `protobuf:"bytes,50,opt,name=social_community_extend_info,json=socialCommunityExtendInfo,proto3,oneof"`
}

func (*NonPublicPostInfo_SocialCommunityExtendInfo) isNonPublicPostInfo_PostExtendInfo() {}

func (m *NonPublicPostInfo) GetPostExtendInfo() isNonPublicPostInfo_PostExtendInfo {
	if m != nil {
		return m.PostExtendInfo
	}
	return nil
}

func (m *NonPublicPostInfo) GetSocialCommunityExtendInfo() *PostExtendSocialCommunity {
	if x, ok := m.GetPostExtendInfo().(*NonPublicPostInfo_SocialCommunityExtendInfo); ok {
		return x.SocialCommunityExtendInfo
	}
	return nil
}

func (m *NonPublicPostInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NonPublicPostInfo) GetContentList() []*NonPublicRichTextElement {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *NonPublicPostInfo) GetPostTime() uint64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostUpdateTime() uint64 {
	if m != nil {
		return m.PostUpdateTime
	}
	return 0
}

func (m *NonPublicPostInfo) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *NonPublicPostInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *NonPublicPostInfo) GetViewCount() uint32 {
	if m != nil {
		return m.ViewCount
	}
	return 0
}

func (m *NonPublicPostInfo) GetMyAttitude() uint32 {
	if m != nil {
		return m.MyAttitude
	}
	return 0
}

func (m *NonPublicPostInfo) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *NonPublicPostInfo) GetShareCount() uint32 {
	if m != nil {
		return m.ShareCount
	}
	return 0
}

func (m *NonPublicPostInfo) GetHadFavoured() bool {
	if m != nil {
		return m.HadFavoured
	}
	return false
}

func (m *NonPublicPostInfo) GetIsSticky() bool {
	if m != nil {
		return m.IsSticky
	}
	return false
}

func (m *NonPublicPostInfo) GetAbnormalState() uint32 {
	if m != nil {
		return m.AbnormalState
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostSource() uint32 {
	if m != nil {
		return m.PostSource
	}
	return 0
}

func (m *NonPublicPostInfo) GetPrivacy() uint32 {
	if m != nil {
		return m.Privacy
	}
	return 0
}

func (m *NonPublicPostInfo) GetPageSource() uint32 {
	if m != nil {
		return m.PageSource
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostPrivacyPolicy() uint32 {
	if m != nil {
		return m.PostPrivacyPolicy
	}
	return 0
}

func (m *NonPublicPostInfo) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostStatus() uint32 {
	if m != nil {
		return m.PostStatus
	}
	return 0
}

func (m *NonPublicPostInfo) GetPostMachineStatus() uint32 {
	if m != nil {
		return m.PostMachineStatus
	}
	return 0
}

func (m *NonPublicPostInfo) GetLabelRemind() string {
	if m != nil {
		return m.LabelRemind
	}
	return ""
}

func (m *NonPublicPostInfo) GetIpLocation() string {
	if m != nil {
		return m.IpLocation
	}
	return ""
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*NonPublicPostInfo) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _NonPublicPostInfo_OneofMarshaler, _NonPublicPostInfo_OneofUnmarshaler, _NonPublicPostInfo_OneofSizer, []interface{}{
		(*NonPublicPostInfo_UserInfo)(nil),
		(*NonPublicPostInfo_SocialCommunityExtendInfo)(nil),
	}
}

func _NonPublicPostInfo_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*NonPublicPostInfo)
	// extend_user_info
	switch x := m.ExtendUserInfo.(type) {
	case *NonPublicPostInfo_UserInfo:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("NonPublicPostInfo.ExtendUserInfo has unexpected type %T", x)
	}
	// post_extend_info
	switch x := m.PostExtendInfo.(type) {
	case *NonPublicPostInfo_SocialCommunityExtendInfo:
		b.EncodeVarint(50<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SocialCommunityExtendInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("NonPublicPostInfo.PostExtendInfo has unexpected type %T", x)
	}
	return nil
}

func _NonPublicPostInfo_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*NonPublicPostInfo)
	switch tag {
	case 3: // extend_user_info.user_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityUserUGCInfo)
		err := b.DecodeMessage(msg)
		m.ExtendUserInfo = &NonPublicPostInfo_UserInfo{msg}
		return true, err
	case 50: // post_extend_info.social_community_extend_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PostExtendSocialCommunity)
		err := b.DecodeMessage(msg)
		m.PostExtendInfo = &NonPublicPostInfo_SocialCommunityExtendInfo{msg}
		return true, err
	default:
		return false, nil
	}
}

func _NonPublicPostInfo_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*NonPublicPostInfo)
	// extend_user_info
	switch x := m.ExtendUserInfo.(type) {
	case *NonPublicPostInfo_UserInfo:
		s := proto.Size(x.UserInfo)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	// post_extend_info
	switch x := m.PostExtendInfo.(type) {
	case *NonPublicPostInfo_SocialCommunityExtendInfo:
		s := proto.Size(x.SocialCommunityExtendInfo)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type CornerInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CornerType           uint32   `protobuf:"varint,2,opt,name=corner_type,json=cornerType,proto3" json:"corner_type,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	IconUrl              string   `protobuf:"bytes,4,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CornerInfo) Reset()         { *m = CornerInfo{} }
func (m *CornerInfo) String() string { return proto.CompactTextString(m) }
func (*CornerInfo) ProtoMessage()    {}
func (*CornerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{11}
}
func (m *CornerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CornerInfo.Unmarshal(m, b)
}
func (m *CornerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CornerInfo.Marshal(b, m, deterministic)
}
func (dst *CornerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CornerInfo.Merge(dst, src)
}
func (m *CornerInfo) XXX_Size() int {
	return xxx_messageInfo_CornerInfo.Size(m)
}
func (m *CornerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CornerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CornerInfo proto.InternalMessageInfo

func (m *CornerInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CornerInfo) GetCornerType() uint32 {
	if m != nil {
		return m.CornerType
	}
	return 0
}

func (m *CornerInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *CornerInfo) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

// 社群信息
type PostExtendSocialCommunity struct {
	SocialCommunityId    string        `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	Name                 string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Logo                 string        `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`
	CornerList           []*CornerInfo `protobuf:"bytes,4,rep,name=corner_list,json=cornerList,proto3" json:"corner_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PostExtendSocialCommunity) Reset()         { *m = PostExtendSocialCommunity{} }
func (m *PostExtendSocialCommunity) String() string { return proto.CompactTextString(m) }
func (*PostExtendSocialCommunity) ProtoMessage()    {}
func (*PostExtendSocialCommunity) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{12}
}
func (m *PostExtendSocialCommunity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostExtendSocialCommunity.Unmarshal(m, b)
}
func (m *PostExtendSocialCommunity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostExtendSocialCommunity.Marshal(b, m, deterministic)
}
func (dst *PostExtendSocialCommunity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostExtendSocialCommunity.Merge(dst, src)
}
func (m *PostExtendSocialCommunity) XXX_Size() int {
	return xxx_messageInfo_PostExtendSocialCommunity.Size(m)
}
func (m *PostExtendSocialCommunity) XXX_DiscardUnknown() {
	xxx_messageInfo_PostExtendSocialCommunity.DiscardUnknown(m)
}

var xxx_messageInfo_PostExtendSocialCommunity proto.InternalMessageInfo

func (m *PostExtendSocialCommunity) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *PostExtendSocialCommunity) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PostExtendSocialCommunity) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *PostExtendSocialCommunity) GetCornerList() []*CornerInfo {
	if m != nil {
		return m.CornerList
	}
	return nil
}

// 私域ugc中的用户信息
type CommonUserUGCInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Alias                string   `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Gender               uint32   `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`
	Signature            string   `protobuf:"bytes,6,opt,name=signature,proto3" json:"signature,omitempty"`
	FaceMd5              string   `protobuf:"bytes,7,opt,name=face_md5,json=faceMd5,proto3" json:"face_md5,omitempty"`
	Following            bool     `protobuf:"varint,8,opt,name=following,proto3" json:"following,omitempty"`
	FollowMe             bool     `protobuf:"varint,9,opt,name=follow_me,json=followMe,proto3" json:"follow_me,omitempty"`
	Online               uint32   `protobuf:"varint,10,opt,name=online,proto3" json:"online,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonUserUGCInfo) Reset()         { *m = CommonUserUGCInfo{} }
func (m *CommonUserUGCInfo) String() string { return proto.CompactTextString(m) }
func (*CommonUserUGCInfo) ProtoMessage()    {}
func (*CommonUserUGCInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{13}
}
func (m *CommonUserUGCInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonUserUGCInfo.Unmarshal(m, b)
}
func (m *CommonUserUGCInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonUserUGCInfo.Marshal(b, m, deterministic)
}
func (dst *CommonUserUGCInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonUserUGCInfo.Merge(dst, src)
}
func (m *CommonUserUGCInfo) XXX_Size() int {
	return xxx_messageInfo_CommonUserUGCInfo.Size(m)
}
func (m *CommonUserUGCInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonUserUGCInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommonUserUGCInfo proto.InternalMessageInfo

func (m *CommonUserUGCInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommonUserUGCInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CommonUserUGCInfo) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *CommonUserUGCInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CommonUserUGCInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *CommonUserUGCInfo) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *CommonUserUGCInfo) GetFaceMd5() string {
	if m != nil {
		return m.FaceMd5
	}
	return ""
}

func (m *CommonUserUGCInfo) GetFollowing() bool {
	if m != nil {
		return m.Following
	}
	return false
}

func (m *CommonUserUGCInfo) GetFollowMe() bool {
	if m != nil {
		return m.FollowMe
	}
	return false
}

func (m *CommonUserUGCInfo) GetOnline() uint32 {
	if m != nil {
		return m.Online
	}
	return 0
}

// 社群ugc中的用户信息
type SocialCommunityUserUGCInfo struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	RoleText             string   `protobuf:"bytes,2,opt,name=role_text,json=roleText,proto3" json:"role_text,omitempty"`
	TextColor            []string `protobuf:"bytes,3,rep,name=text_color,json=textColor,proto3" json:"text_color,omitempty"`
	BackgroundColor      []string `protobuf:"bytes,4,rep,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityUserUGCInfo) Reset()         { *m = SocialCommunityUserUGCInfo{} }
func (m *SocialCommunityUserUGCInfo) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityUserUGCInfo) ProtoMessage()    {}
func (*SocialCommunityUserUGCInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{14}
}
func (m *SocialCommunityUserUGCInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityUserUGCInfo.Unmarshal(m, b)
}
func (m *SocialCommunityUserUGCInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityUserUGCInfo.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityUserUGCInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityUserUGCInfo.Merge(dst, src)
}
func (m *SocialCommunityUserUGCInfo) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityUserUGCInfo.Size(m)
}
func (m *SocialCommunityUserUGCInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityUserUGCInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityUserUGCInfo proto.InternalMessageInfo

func (m *SocialCommunityUserUGCInfo) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *SocialCommunityUserUGCInfo) GetRoleText() string {
	if m != nil {
		return m.RoleText
	}
	return ""
}

func (m *SocialCommunityUserUGCInfo) GetTextColor() []string {
	if m != nil {
		return m.TextColor
	}
	return nil
}

func (m *SocialCommunityUserUGCInfo) GetBackgroundColor() []string {
	if m != nil {
		return m.BackgroundColor
	}
	return nil
}

type SocialCommunityInfo struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityInfo) Reset()         { *m = SocialCommunityInfo{} }
func (m *SocialCommunityInfo) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityInfo) ProtoMessage()    {}
func (*SocialCommunityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{15}
}
func (m *SocialCommunityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityInfo.Unmarshal(m, b)
}
func (m *SocialCommunityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityInfo.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityInfo.Merge(dst, src)
}
func (m *SocialCommunityInfo) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityInfo.Size(m)
}
func (m *SocialCommunityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityInfo proto.InternalMessageInfo

func (m *SocialCommunityInfo) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

// 发布器的社群信息（用户身份）
type SocialCommunityPublishOpt struct {
	TextStyleType        uint32   `protobuf:"varint,1,opt,name=text_style_type,json=textStyleType,proto3" json:"text_style_type,omitempty"`
	PrefixText           string   `protobuf:"bytes,2,opt,name=prefix_text,json=prefixText,proto3" json:"prefix_text,omitempty"`
	Logo                 string   `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`
	SuffixText           string   `protobuf:"bytes,4,opt,name=suffix_text,json=suffixText,proto3" json:"suffix_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityPublishOpt) Reset()         { *m = SocialCommunityPublishOpt{} }
func (m *SocialCommunityPublishOpt) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityPublishOpt) ProtoMessage()    {}
func (*SocialCommunityPublishOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{16}
}
func (m *SocialCommunityPublishOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityPublishOpt.Unmarshal(m, b)
}
func (m *SocialCommunityPublishOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityPublishOpt.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityPublishOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityPublishOpt.Merge(dst, src)
}
func (m *SocialCommunityPublishOpt) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityPublishOpt.Size(m)
}
func (m *SocialCommunityPublishOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityPublishOpt.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityPublishOpt proto.InternalMessageInfo

func (m *SocialCommunityPublishOpt) GetTextStyleType() uint32 {
	if m != nil {
		return m.TextStyleType
	}
	return 0
}

func (m *SocialCommunityPublishOpt) GetPrefixText() string {
	if m != nil {
		return m.PrefixText
	}
	return ""
}

func (m *SocialCommunityPublishOpt) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *SocialCommunityPublishOpt) GetSuffixText() string {
	if m != nil {
		return m.SuffixText
	}
	return ""
}

type NonPublicUploadAttachmentInfo struct {
	AttachmentType       uint32   `protobuf:"varint,1,opt,name=attachment_type,json=attachmentType,proto3" json:"attachment_type,omitempty"`
	AttachmentKey        string   `protobuf:"bytes,2,opt,name=attachment_key,json=attachmentKey,proto3" json:"attachment_key,omitempty"`
	ExtraInfo            string   `protobuf:"bytes,3,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicUploadAttachmentInfo) Reset()         { *m = NonPublicUploadAttachmentInfo{} }
func (m *NonPublicUploadAttachmentInfo) String() string { return proto.CompactTextString(m) }
func (*NonPublicUploadAttachmentInfo) ProtoMessage()    {}
func (*NonPublicUploadAttachmentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{17}
}
func (m *NonPublicUploadAttachmentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicUploadAttachmentInfo.Unmarshal(m, b)
}
func (m *NonPublicUploadAttachmentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicUploadAttachmentInfo.Marshal(b, m, deterministic)
}
func (dst *NonPublicUploadAttachmentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicUploadAttachmentInfo.Merge(dst, src)
}
func (m *NonPublicUploadAttachmentInfo) XXX_Size() int {
	return xxx_messageInfo_NonPublicUploadAttachmentInfo.Size(m)
}
func (m *NonPublicUploadAttachmentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicUploadAttachmentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicUploadAttachmentInfo proto.InternalMessageInfo

func (m *NonPublicUploadAttachmentInfo) GetAttachmentType() uint32 {
	if m != nil {
		return m.AttachmentType
	}
	return 0
}

func (m *NonPublicUploadAttachmentInfo) GetAttachmentKey() string {
	if m != nil {
		return m.AttachmentKey
	}
	return ""
}

func (m *NonPublicUploadAttachmentInfo) GetExtraInfo() string {
	if m != nil {
		return m.ExtraInfo
	}
	return ""
}

type NewAttitudeMsg struct {
	Attitude             uint32                `protobuf:"varint,1,opt,name=attitude,proto3" json:"attitude,omitempty"`
	PostObject           *NonPublicPostInfo    `protobuf:"bytes,2,opt,name=post_object,json=postObject,proto3" json:"post_object,omitempty"`
	CommentObject        *NonPublicCommentInfo `protobuf:"bytes,3,opt,name=comment_object,json=commentObject,proto3" json:"comment_object,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *NewAttitudeMsg) Reset()         { *m = NewAttitudeMsg{} }
func (m *NewAttitudeMsg) String() string { return proto.CompactTextString(m) }
func (*NewAttitudeMsg) ProtoMessage()    {}
func (*NewAttitudeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{18}
}
func (m *NewAttitudeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewAttitudeMsg.Unmarshal(m, b)
}
func (m *NewAttitudeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewAttitudeMsg.Marshal(b, m, deterministic)
}
func (dst *NewAttitudeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewAttitudeMsg.Merge(dst, src)
}
func (m *NewAttitudeMsg) XXX_Size() int {
	return xxx_messageInfo_NewAttitudeMsg.Size(m)
}
func (m *NewAttitudeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_NewAttitudeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_NewAttitudeMsg proto.InternalMessageInfo

func (m *NewAttitudeMsg) GetAttitude() uint32 {
	if m != nil {
		return m.Attitude
	}
	return 0
}

func (m *NewAttitudeMsg) GetPostObject() *NonPublicPostInfo {
	if m != nil {
		return m.PostObject
	}
	return nil
}

func (m *NewAttitudeMsg) GetCommentObject() *NonPublicCommentInfo {
	if m != nil {
		return m.CommentObject
	}
	return nil
}

type NewCommentMsg struct {
	CommentInfo          *NonPublicCommentInfo `protobuf:"bytes,1,opt,name=comment_info,json=commentInfo,proto3" json:"comment_info,omitempty"`
	PostObject           *NonPublicPostInfo    `protobuf:"bytes,2,opt,name=post_object,json=postObject,proto3" json:"post_object,omitempty"`
	CommentObject        *NonPublicCommentInfo `protobuf:"bytes,3,opt,name=comment_object,json=commentObject,proto3" json:"comment_object,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *NewCommentMsg) Reset()         { *m = NewCommentMsg{} }
func (m *NewCommentMsg) String() string { return proto.CompactTextString(m) }
func (*NewCommentMsg) ProtoMessage()    {}
func (*NewCommentMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{19}
}
func (m *NewCommentMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewCommentMsg.Unmarshal(m, b)
}
func (m *NewCommentMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewCommentMsg.Marshal(b, m, deterministic)
}
func (dst *NewCommentMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewCommentMsg.Merge(dst, src)
}
func (m *NewCommentMsg) XXX_Size() int {
	return xxx_messageInfo_NewCommentMsg.Size(m)
}
func (m *NewCommentMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_NewCommentMsg.DiscardUnknown(m)
}

var xxx_messageInfo_NewCommentMsg proto.InternalMessageInfo

func (m *NewCommentMsg) GetCommentInfo() *NonPublicCommentInfo {
	if m != nil {
		return m.CommentInfo
	}
	return nil
}

func (m *NewCommentMsg) GetPostObject() *NonPublicPostInfo {
	if m != nil {
		return m.PostObject
	}
	return nil
}

func (m *NewCommentMsg) GetCommentObject() *NonPublicCommentInfo {
	if m != nil {
		return m.CommentObject
	}
	return nil
}

type TopComment struct {
	HotComment           *NonPublicCommentInfo `protobuf:"bytes,1,opt,name=hot_comment,json=hotComment,proto3" json:"hot_comment,omitempty"`
	ReplyingComment      *NonPublicCommentInfo `protobuf:"bytes,2,opt,name=replying_comment,json=replyingComment,proto3" json:"replying_comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *TopComment) Reset()         { *m = TopComment{} }
func (m *TopComment) String() string { return proto.CompactTextString(m) }
func (*TopComment) ProtoMessage()    {}
func (*TopComment) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{20}
}
func (m *TopComment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopComment.Unmarshal(m, b)
}
func (m *TopComment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopComment.Marshal(b, m, deterministic)
}
func (dst *TopComment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopComment.Merge(dst, src)
}
func (m *TopComment) XXX_Size() int {
	return xxx_messageInfo_TopComment.Size(m)
}
func (m *TopComment) XXX_DiscardUnknown() {
	xxx_messageInfo_TopComment.DiscardUnknown(m)
}

var xxx_messageInfo_TopComment proto.InternalMessageInfo

func (m *TopComment) GetHotComment() *NonPublicCommentInfo {
	if m != nil {
		return m.HotComment
	}
	return nil
}

func (m *TopComment) GetReplyingComment() *NonPublicCommentInfo {
	if m != nil {
		return m.ReplyingComment
	}
	return nil
}

type NonPublicCommentInfo struct {
	PostId               string                  `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ConversationId       string                  `protobuf:"bytes,2,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	CommentId            string                  `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	FromUser             *CommonUserUGCInfo      `protobuf:"bytes,4,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	Content              string                  `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	Attachments          []*NonPublicAttachment  `protobuf:"bytes,6,rep,name=attachments,proto3" json:"attachments,omitempty"`
	MyAttitude           uint32                  `protobuf:"varint,7,opt,name=my_attitude,json=myAttitude,proto3" json:"my_attitude,omitempty"`
	ToUser               *CommonUserUGCInfo      `protobuf:"bytes,8,opt,name=to_user,json=toUser,proto3" json:"to_user,omitempty"`
	TotalSubCommentCount uint32                  `protobuf:"varint,9,opt,name=total_sub_comment_count,json=totalSubCommentCount,proto3" json:"total_sub_comment_count,omitempty"`
	SubComments          []*NonPublicCommentInfo `protobuf:"bytes,10,rep,name=sub_comments,json=subComments,proto3" json:"sub_comments,omitempty"`
	AttitudeCount        uint32                  `protobuf:"varint,11,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	CreateAt             uint64                  `protobuf:"varint,12,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	IsDeleted            bool                    `protobuf:"varint,13,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	MyStep               uint32                  `protobuf:"varint,15,opt,name=my_step,json=myStep,proto3" json:"my_step,omitempty"`
	StepType             uint32                  `protobuf:"varint,16,opt,name=step_type,json=stepType,proto3" json:"step_type,omitempty"`
	StepCount            uint32                  `protobuf:"varint,17,opt,name=step_count,json=stepCount,proto3" json:"step_count,omitempty"`
	IpLocation           string                  `protobuf:"bytes,18,opt,name=ip_location,json=ipLocation,proto3" json:"ip_location,omitempty"`
	// 社群属性「」
	//
	// Types that are valid to be assigned to ExtendUserInfo:
	//	*NonPublicCommentInfo_UserInfo
	ExtendUserInfo       isNonPublicCommentInfo_ExtendUserInfo `protobuf_oneof:"extend_user_info"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *NonPublicCommentInfo) Reset()         { *m = NonPublicCommentInfo{} }
func (m *NonPublicCommentInfo) String() string { return proto.CompactTextString(m) }
func (*NonPublicCommentInfo) ProtoMessage()    {}
func (*NonPublicCommentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{21}
}
func (m *NonPublicCommentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicCommentInfo.Unmarshal(m, b)
}
func (m *NonPublicCommentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicCommentInfo.Marshal(b, m, deterministic)
}
func (dst *NonPublicCommentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicCommentInfo.Merge(dst, src)
}
func (m *NonPublicCommentInfo) XXX_Size() int {
	return xxx_messageInfo_NonPublicCommentInfo.Size(m)
}
func (m *NonPublicCommentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicCommentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicCommentInfo proto.InternalMessageInfo

func (m *NonPublicCommentInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *NonPublicCommentInfo) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *NonPublicCommentInfo) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *NonPublicCommentInfo) GetFromUser() *CommonUserUGCInfo {
	if m != nil {
		return m.FromUser
	}
	return nil
}

func (m *NonPublicCommentInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *NonPublicCommentInfo) GetAttachments() []*NonPublicAttachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

func (m *NonPublicCommentInfo) GetMyAttitude() uint32 {
	if m != nil {
		return m.MyAttitude
	}
	return 0
}

func (m *NonPublicCommentInfo) GetToUser() *CommonUserUGCInfo {
	if m != nil {
		return m.ToUser
	}
	return nil
}

func (m *NonPublicCommentInfo) GetTotalSubCommentCount() uint32 {
	if m != nil {
		return m.TotalSubCommentCount
	}
	return 0
}

func (m *NonPublicCommentInfo) GetSubComments() []*NonPublicCommentInfo {
	if m != nil {
		return m.SubComments
	}
	return nil
}

func (m *NonPublicCommentInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *NonPublicCommentInfo) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *NonPublicCommentInfo) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *NonPublicCommentInfo) GetMyStep() uint32 {
	if m != nil {
		return m.MyStep
	}
	return 0
}

func (m *NonPublicCommentInfo) GetStepType() uint32 {
	if m != nil {
		return m.StepType
	}
	return 0
}

func (m *NonPublicCommentInfo) GetStepCount() uint32 {
	if m != nil {
		return m.StepCount
	}
	return 0
}

func (m *NonPublicCommentInfo) GetIpLocation() string {
	if m != nil {
		return m.IpLocation
	}
	return ""
}

type isNonPublicCommentInfo_ExtendUserInfo interface {
	isNonPublicCommentInfo_ExtendUserInfo()
}

type NonPublicCommentInfo_UserInfo struct {
	UserInfo *SocialCommunityUserUGCInfo `protobuf:"bytes,50,opt,name=user_info,json=userInfo,proto3,oneof"`
}

func (*NonPublicCommentInfo_UserInfo) isNonPublicCommentInfo_ExtendUserInfo() {}

func (m *NonPublicCommentInfo) GetExtendUserInfo() isNonPublicCommentInfo_ExtendUserInfo {
	if m != nil {
		return m.ExtendUserInfo
	}
	return nil
}

func (m *NonPublicCommentInfo) GetUserInfo() *SocialCommunityUserUGCInfo {
	if x, ok := m.GetExtendUserInfo().(*NonPublicCommentInfo_UserInfo); ok {
		return x.UserInfo
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*NonPublicCommentInfo) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _NonPublicCommentInfo_OneofMarshaler, _NonPublicCommentInfo_OneofUnmarshaler, _NonPublicCommentInfo_OneofSizer, []interface{}{
		(*NonPublicCommentInfo_UserInfo)(nil),
	}
}

func _NonPublicCommentInfo_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*NonPublicCommentInfo)
	// extend_user_info
	switch x := m.ExtendUserInfo.(type) {
	case *NonPublicCommentInfo_UserInfo:
		b.EncodeVarint(50<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("NonPublicCommentInfo.ExtendUserInfo has unexpected type %T", x)
	}
	return nil
}

func _NonPublicCommentInfo_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*NonPublicCommentInfo)
	switch tag {
	case 50: // extend_user_info.user_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityUserUGCInfo)
		err := b.DecodeMessage(msg)
		m.ExtendUserInfo = &NonPublicCommentInfo_UserInfo{msg}
		return true, err
	default:
		return false, nil
	}
}

func _NonPublicCommentInfo_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*NonPublicCommentInfo)
	// extend_user_info
	switch x := m.ExtendUserInfo.(type) {
	case *NonPublicCommentInfo_UserInfo:
		s := proto.Size(x.UserInfo)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 发帖（私域）
type PostNonPublicPostReq struct {
	BaseReq           *app.BaseReq   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SceneId           string         `protobuf:"bytes,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	SceneStreamList   []*SceneStream `protobuf:"bytes,3,rep,name=scene_stream_list,json=sceneStreamList,proto3" json:"scene_stream_list,omitempty"`
	OriginSceneStream *SceneStream   `protobuf:"bytes,4,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	// Types that are valid to be assigned to SceneDetail:
	//	*PostNonPublicPostReq_SocialCommunityInfo
	SceneDetail          isPostNonPublicPostReq_SceneDetail `protobuf_oneof:"scene_detail"`
	Title                string                             `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	ContentList          []*NonPublicRichTextElement        `protobuf:"bytes,6,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	Origin               uint32                             `protobuf:"varint,7,opt,name=origin,proto3" json:"origin,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *PostNonPublicPostReq) Reset()         { *m = PostNonPublicPostReq{} }
func (m *PostNonPublicPostReq) String() string { return proto.CompactTextString(m) }
func (*PostNonPublicPostReq) ProtoMessage()    {}
func (*PostNonPublicPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{22}
}
func (m *PostNonPublicPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostNonPublicPostReq.Unmarshal(m, b)
}
func (m *PostNonPublicPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostNonPublicPostReq.Marshal(b, m, deterministic)
}
func (dst *PostNonPublicPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostNonPublicPostReq.Merge(dst, src)
}
func (m *PostNonPublicPostReq) XXX_Size() int {
	return xxx_messageInfo_PostNonPublicPostReq.Size(m)
}
func (m *PostNonPublicPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PostNonPublicPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_PostNonPublicPostReq proto.InternalMessageInfo

func (m *PostNonPublicPostReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PostNonPublicPostReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *PostNonPublicPostReq) GetSceneStreamList() []*SceneStream {
	if m != nil {
		return m.SceneStreamList
	}
	return nil
}

func (m *PostNonPublicPostReq) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

type isPostNonPublicPostReq_SceneDetail interface {
	isPostNonPublicPostReq_SceneDetail()
}

type PostNonPublicPostReq_SocialCommunityInfo struct {
	SocialCommunityInfo *SocialCommunityInfo `protobuf:"bytes,100,opt,name=social_community_info,json=socialCommunityInfo,proto3,oneof"`
}

func (*PostNonPublicPostReq_SocialCommunityInfo) isPostNonPublicPostReq_SceneDetail() {}

func (m *PostNonPublicPostReq) GetSceneDetail() isPostNonPublicPostReq_SceneDetail {
	if m != nil {
		return m.SceneDetail
	}
	return nil
}

func (m *PostNonPublicPostReq) GetSocialCommunityInfo() *SocialCommunityInfo {
	if x, ok := m.GetSceneDetail().(*PostNonPublicPostReq_SocialCommunityInfo); ok {
		return x.SocialCommunityInfo
	}
	return nil
}

func (m *PostNonPublicPostReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PostNonPublicPostReq) GetContentList() []*NonPublicRichTextElement {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *PostNonPublicPostReq) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*PostNonPublicPostReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _PostNonPublicPostReq_OneofMarshaler, _PostNonPublicPostReq_OneofUnmarshaler, _PostNonPublicPostReq_OneofSizer, []interface{}{
		(*PostNonPublicPostReq_SocialCommunityInfo)(nil),
	}
}

func _PostNonPublicPostReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*PostNonPublicPostReq)
	// scene_detail
	switch x := m.SceneDetail.(type) {
	case *PostNonPublicPostReq_SocialCommunityInfo:
		b.EncodeVarint(100<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SocialCommunityInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("PostNonPublicPostReq.SceneDetail has unexpected type %T", x)
	}
	return nil
}

func _PostNonPublicPostReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*PostNonPublicPostReq)
	switch tag {
	case 100: // scene_detail.social_community_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityInfo)
		err := b.DecodeMessage(msg)
		m.SceneDetail = &PostNonPublicPostReq_SocialCommunityInfo{msg}
		return true, err
	default:
		return false, nil
	}
}

func _PostNonPublicPostReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*PostNonPublicPostReq)
	// scene_detail
	switch x := m.SceneDetail.(type) {
	case *PostNonPublicPostReq_SocialCommunityInfo:
		s := proto.Size(x.SocialCommunityInfo)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type PostNonPublicPostResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PostId               string        `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	ImageToken           string        `protobuf:"bytes,3,opt,name=image_token,json=imageToken,proto3" json:"image_token,omitempty"`
	VideoToken           string        `protobuf:"bytes,4,opt,name=video_token,json=videoToken,proto3" json:"video_token,omitempty"`
	ImageKeys            []string      `protobuf:"bytes,5,rep,name=image_keys,json=imageKeys,proto3" json:"image_keys,omitempty"`
	VideoKeys            []string      `protobuf:"bytes,6,rep,name=video_keys,json=videoKeys,proto3" json:"video_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PostNonPublicPostResp) Reset()         { *m = PostNonPublicPostResp{} }
func (m *PostNonPublicPostResp) String() string { return proto.CompactTextString(m) }
func (*PostNonPublicPostResp) ProtoMessage()    {}
func (*PostNonPublicPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{23}
}
func (m *PostNonPublicPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostNonPublicPostResp.Unmarshal(m, b)
}
func (m *PostNonPublicPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostNonPublicPostResp.Marshal(b, m, deterministic)
}
func (dst *PostNonPublicPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostNonPublicPostResp.Merge(dst, src)
}
func (m *PostNonPublicPostResp) XXX_Size() int {
	return xxx_messageInfo_PostNonPublicPostResp.Size(m)
}
func (m *PostNonPublicPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PostNonPublicPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_PostNonPublicPostResp proto.InternalMessageInfo

func (m *PostNonPublicPostResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PostNonPublicPostResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostNonPublicPostResp) GetImageToken() string {
	if m != nil {
		return m.ImageToken
	}
	return ""
}

func (m *PostNonPublicPostResp) GetVideoToken() string {
	if m != nil {
		return m.VideoToken
	}
	return ""
}

func (m *PostNonPublicPostResp) GetImageKeys() []string {
	if m != nil {
		return m.ImageKeys
	}
	return nil
}

func (m *PostNonPublicPostResp) GetVideoKeys() []string {
	if m != nil {
		return m.VideoKeys
	}
	return nil
}

// 发帖详情页扩展信息
type GetNonPublicPostPublishExtendReq struct {
	BaseReq         *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CurrSceneStream *SceneStream `protobuf:"bytes,2,opt,name=curr_scene_stream,json=currSceneStream,proto3" json:"curr_scene_stream,omitempty"`
	// Types that are valid to be assigned to UserOptInfo:
	//	*GetNonPublicPostPublishExtendReq_SocialCommunity
	UserOptInfo          isGetNonPublicPostPublishExtendReq_UserOptInfo `protobuf_oneof:"user_opt_info"`
	XXX_NoUnkeyedLiteral struct{}                                       `json:"-"`
	XXX_unrecognized     []byte                                         `json:"-"`
	XXX_sizecache        int32                                          `json:"-"`
}

func (m *GetNonPublicPostPublishExtendReq) Reset()         { *m = GetNonPublicPostPublishExtendReq{} }
func (m *GetNonPublicPostPublishExtendReq) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicPostPublishExtendReq) ProtoMessage()    {}
func (*GetNonPublicPostPublishExtendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{24}
}
func (m *GetNonPublicPostPublishExtendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicPostPublishExtendReq.Unmarshal(m, b)
}
func (m *GetNonPublicPostPublishExtendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicPostPublishExtendReq.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicPostPublishExtendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicPostPublishExtendReq.Merge(dst, src)
}
func (m *GetNonPublicPostPublishExtendReq) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicPostPublishExtendReq.Size(m)
}
func (m *GetNonPublicPostPublishExtendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicPostPublishExtendReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicPostPublishExtendReq proto.InternalMessageInfo

func (m *GetNonPublicPostPublishExtendReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNonPublicPostPublishExtendReq) GetCurrSceneStream() *SceneStream {
	if m != nil {
		return m.CurrSceneStream
	}
	return nil
}

type isGetNonPublicPostPublishExtendReq_UserOptInfo interface {
	isGetNonPublicPostPublishExtendReq_UserOptInfo()
}

type GetNonPublicPostPublishExtendReq_SocialCommunity struct {
	SocialCommunity *SocialCommunityInfo `protobuf:"bytes,20,opt,name=social_community,json=socialCommunity,proto3,oneof"`
}

func (*GetNonPublicPostPublishExtendReq_SocialCommunity) isGetNonPublicPostPublishExtendReq_UserOptInfo() {
}

func (m *GetNonPublicPostPublishExtendReq) GetUserOptInfo() isGetNonPublicPostPublishExtendReq_UserOptInfo {
	if m != nil {
		return m.UserOptInfo
	}
	return nil
}

func (m *GetNonPublicPostPublishExtendReq) GetSocialCommunity() *SocialCommunityInfo {
	if x, ok := m.GetUserOptInfo().(*GetNonPublicPostPublishExtendReq_SocialCommunity); ok {
		return x.SocialCommunity
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GetNonPublicPostPublishExtendReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GetNonPublicPostPublishExtendReq_OneofMarshaler, _GetNonPublicPostPublishExtendReq_OneofUnmarshaler, _GetNonPublicPostPublishExtendReq_OneofSizer, []interface{}{
		(*GetNonPublicPostPublishExtendReq_SocialCommunity)(nil),
	}
}

func _GetNonPublicPostPublishExtendReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GetNonPublicPostPublishExtendReq)
	// user_opt_info
	switch x := m.UserOptInfo.(type) {
	case *GetNonPublicPostPublishExtendReq_SocialCommunity:
		b.EncodeVarint(20<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SocialCommunity); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GetNonPublicPostPublishExtendReq.UserOptInfo has unexpected type %T", x)
	}
	return nil
}

func _GetNonPublicPostPublishExtendReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GetNonPublicPostPublishExtendReq)
	switch tag {
	case 20: // user_opt_info.social_community
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityInfo)
		err := b.DecodeMessage(msg)
		m.UserOptInfo = &GetNonPublicPostPublishExtendReq_SocialCommunity{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GetNonPublicPostPublishExtendReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GetNonPublicPostPublishExtendReq)
	// user_opt_info
	switch x := m.UserOptInfo.(type) {
	case *GetNonPublicPostPublishExtendReq_SocialCommunity:
		s := proto.Size(x.SocialCommunity)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type GetNonPublicPostPublishExtendResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OriginSceneStream    *SceneStream   `protobuf:"bytes,2,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	SceneStreamList      []*SceneStream `protobuf:"bytes,3,rep,name=scene_stream_list,json=sceneStreamList,proto3" json:"scene_stream_list,omitempty"`
	HasPublishPermission uint32         `protobuf:"varint,4,opt,name=has_publish_permission,json=hasPublishPermission,proto3" json:"has_publish_permission,omitempty"`
	// Types that are valid to be assigned to PublishOptInfo:
	//	*GetNonPublicPostPublishExtendResp_SocialCommunityOpt
	PublishOptInfo       isGetNonPublicPostPublishExtendResp_PublishOptInfo `protobuf_oneof:"publish_opt_info"`
	WhiteList            []*WhiteListSites                                  `protobuf:"bytes,5,rep,name=white_list,json=whiteList,proto3" json:"white_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                           `json:"-"`
	XXX_unrecognized     []byte                                             `json:"-"`
	XXX_sizecache        int32                                              `json:"-"`
}

func (m *GetNonPublicPostPublishExtendResp) Reset()         { *m = GetNonPublicPostPublishExtendResp{} }
func (m *GetNonPublicPostPublishExtendResp) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicPostPublishExtendResp) ProtoMessage()    {}
func (*GetNonPublicPostPublishExtendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{25}
}
func (m *GetNonPublicPostPublishExtendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicPostPublishExtendResp.Unmarshal(m, b)
}
func (m *GetNonPublicPostPublishExtendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicPostPublishExtendResp.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicPostPublishExtendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicPostPublishExtendResp.Merge(dst, src)
}
func (m *GetNonPublicPostPublishExtendResp) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicPostPublishExtendResp.Size(m)
}
func (m *GetNonPublicPostPublishExtendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicPostPublishExtendResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicPostPublishExtendResp proto.InternalMessageInfo

func (m *GetNonPublicPostPublishExtendResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNonPublicPostPublishExtendResp) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

func (m *GetNonPublicPostPublishExtendResp) GetSceneStreamList() []*SceneStream {
	if m != nil {
		return m.SceneStreamList
	}
	return nil
}

func (m *GetNonPublicPostPublishExtendResp) GetHasPublishPermission() uint32 {
	if m != nil {
		return m.HasPublishPermission
	}
	return 0
}

type isGetNonPublicPostPublishExtendResp_PublishOptInfo interface {
	isGetNonPublicPostPublishExtendResp_PublishOptInfo()
}

type GetNonPublicPostPublishExtendResp_SocialCommunityOpt struct {
	SocialCommunityOpt *SocialCommunityPublishOpt `protobuf:"bytes,20,opt,name=social_community_opt,json=socialCommunityOpt,proto3,oneof"`
}

func (*GetNonPublicPostPublishExtendResp_SocialCommunityOpt) isGetNonPublicPostPublishExtendResp_PublishOptInfo() {
}

func (m *GetNonPublicPostPublishExtendResp) GetPublishOptInfo() isGetNonPublicPostPublishExtendResp_PublishOptInfo {
	if m != nil {
		return m.PublishOptInfo
	}
	return nil
}

func (m *GetNonPublicPostPublishExtendResp) GetSocialCommunityOpt() *SocialCommunityPublishOpt {
	if x, ok := m.GetPublishOptInfo().(*GetNonPublicPostPublishExtendResp_SocialCommunityOpt); ok {
		return x.SocialCommunityOpt
	}
	return nil
}

func (m *GetNonPublicPostPublishExtendResp) GetWhiteList() []*WhiteListSites {
	if m != nil {
		return m.WhiteList
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GetNonPublicPostPublishExtendResp) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GetNonPublicPostPublishExtendResp_OneofMarshaler, _GetNonPublicPostPublishExtendResp_OneofUnmarshaler, _GetNonPublicPostPublishExtendResp_OneofSizer, []interface{}{
		(*GetNonPublicPostPublishExtendResp_SocialCommunityOpt)(nil),
	}
}

func _GetNonPublicPostPublishExtendResp_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GetNonPublicPostPublishExtendResp)
	// publish_opt_info
	switch x := m.PublishOptInfo.(type) {
	case *GetNonPublicPostPublishExtendResp_SocialCommunityOpt:
		b.EncodeVarint(20<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SocialCommunityOpt); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GetNonPublicPostPublishExtendResp.PublishOptInfo has unexpected type %T", x)
	}
	return nil
}

func _GetNonPublicPostPublishExtendResp_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GetNonPublicPostPublishExtendResp)
	switch tag {
	case 20: // publish_opt_info.social_community_opt
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityPublishOpt)
		err := b.DecodeMessage(msg)
		m.PublishOptInfo = &GetNonPublicPostPublishExtendResp_SocialCommunityOpt{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GetNonPublicPostPublishExtendResp_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GetNonPublicPostPublishExtendResp)
	// publish_opt_info
	switch x := m.PublishOptInfo.(type) {
	case *GetNonPublicPostPublishExtendResp_SocialCommunityOpt:
		s := proto.Size(x.SocialCommunityOpt)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type WhiteListSites struct {
	ShowPlatformName     string   `protobuf:"bytes,1,opt,name=show_platform_name,json=showPlatformName,proto3" json:"show_platform_name,omitempty"`
	ShowPlatformIcon     string   `protobuf:"bytes,2,opt,name=show_platform_icon,json=showPlatformIcon,proto3" json:"show_platform_icon,omitempty"`
	RealmName            string   `protobuf:"bytes,3,opt,name=realm_name,json=realmName,proto3" json:"realm_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WhiteListSites) Reset()         { *m = WhiteListSites{} }
func (m *WhiteListSites) String() string { return proto.CompactTextString(m) }
func (*WhiteListSites) ProtoMessage()    {}
func (*WhiteListSites) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{26}
}
func (m *WhiteListSites) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WhiteListSites.Unmarshal(m, b)
}
func (m *WhiteListSites) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WhiteListSites.Marshal(b, m, deterministic)
}
func (dst *WhiteListSites) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WhiteListSites.Merge(dst, src)
}
func (m *WhiteListSites) XXX_Size() int {
	return xxx_messageInfo_WhiteListSites.Size(m)
}
func (m *WhiteListSites) XXX_DiscardUnknown() {
	xxx_messageInfo_WhiteListSites.DiscardUnknown(m)
}

var xxx_messageInfo_WhiteListSites proto.InternalMessageInfo

func (m *WhiteListSites) GetShowPlatformName() string {
	if m != nil {
		return m.ShowPlatformName
	}
	return ""
}

func (m *WhiteListSites) GetShowPlatformIcon() string {
	if m != nil {
		return m.ShowPlatformIcon
	}
	return ""
}

func (m *WhiteListSites) GetRealmName() string {
	if m != nil {
		return m.RealmName
	}
	return ""
}

// 附件上传完成
type MarkNonPublicPostAttachmentUploadedReq struct {
	BaseReq        *app.BaseReq                     `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId         string                           `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AttachmentList []*NonPublicUploadAttachmentInfo `protobuf:"bytes,3,rep,name=attachment_list,json=attachmentList,proto3" json:"attachment_list,omitempty"`
	// V3.4.2 - 支持图片评论，如果是上传评论的附件，则带上评论的id
	CommentId            string   `protobuf:"bytes,4,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkNonPublicPostAttachmentUploadedReq) Reset() {
	*m = MarkNonPublicPostAttachmentUploadedReq{}
}
func (m *MarkNonPublicPostAttachmentUploadedReq) String() string { return proto.CompactTextString(m) }
func (*MarkNonPublicPostAttachmentUploadedReq) ProtoMessage()    {}
func (*MarkNonPublicPostAttachmentUploadedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{27}
}
func (m *MarkNonPublicPostAttachmentUploadedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkNonPublicPostAttachmentUploadedReq.Unmarshal(m, b)
}
func (m *MarkNonPublicPostAttachmentUploadedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkNonPublicPostAttachmentUploadedReq.Marshal(b, m, deterministic)
}
func (dst *MarkNonPublicPostAttachmentUploadedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkNonPublicPostAttachmentUploadedReq.Merge(dst, src)
}
func (m *MarkNonPublicPostAttachmentUploadedReq) XXX_Size() int {
	return xxx_messageInfo_MarkNonPublicPostAttachmentUploadedReq.Size(m)
}
func (m *MarkNonPublicPostAttachmentUploadedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkNonPublicPostAttachmentUploadedReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkNonPublicPostAttachmentUploadedReq proto.InternalMessageInfo

func (m *MarkNonPublicPostAttachmentUploadedReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MarkNonPublicPostAttachmentUploadedReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *MarkNonPublicPostAttachmentUploadedReq) GetAttachmentList() []*NonPublicUploadAttachmentInfo {
	if m != nil {
		return m.AttachmentList
	}
	return nil
}

func (m *MarkNonPublicPostAttachmentUploadedReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type MarkNonPublicPostAttachmentUploadedResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MarkNonPublicPostAttachmentUploadedResp) Reset() {
	*m = MarkNonPublicPostAttachmentUploadedResp{}
}
func (m *MarkNonPublicPostAttachmentUploadedResp) String() string { return proto.CompactTextString(m) }
func (*MarkNonPublicPostAttachmentUploadedResp) ProtoMessage()    {}
func (*MarkNonPublicPostAttachmentUploadedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{28}
}
func (m *MarkNonPublicPostAttachmentUploadedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkNonPublicPostAttachmentUploadedResp.Unmarshal(m, b)
}
func (m *MarkNonPublicPostAttachmentUploadedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkNonPublicPostAttachmentUploadedResp.Marshal(b, m, deterministic)
}
func (dst *MarkNonPublicPostAttachmentUploadedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkNonPublicPostAttachmentUploadedResp.Merge(dst, src)
}
func (m *MarkNonPublicPostAttachmentUploadedResp) XXX_Size() int {
	return xxx_messageInfo_MarkNonPublicPostAttachmentUploadedResp.Size(m)
}
func (m *MarkNonPublicPostAttachmentUploadedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkNonPublicPostAttachmentUploadedResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkNonPublicPostAttachmentUploadedResp proto.InternalMessageInfo

func (m *MarkNonPublicPostAttachmentUploadedResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 拉流（私域）
type GetNonPublicNewsFeedsReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SceneId string       `protobuf:"bytes,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	// Types that are valid to be assigned to RequestType:
	//	*GetNonPublicNewsFeedsReq_SceneStreamReq
	RequestType          isGetNonPublicNewsFeedsReq_RequestType `protobuf_oneof:"request_type"`
	LoadMore             *NonPublicNewFeedsLoadMore             `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32                                 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-"`
	XXX_unrecognized     []byte                                 `json:"-"`
	XXX_sizecache        int32                                  `json:"-"`
}

func (m *GetNonPublicNewsFeedsReq) Reset()         { *m = GetNonPublicNewsFeedsReq{} }
func (m *GetNonPublicNewsFeedsReq) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicNewsFeedsReq) ProtoMessage()    {}
func (*GetNonPublicNewsFeedsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{29}
}
func (m *GetNonPublicNewsFeedsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicNewsFeedsReq.Unmarshal(m, b)
}
func (m *GetNonPublicNewsFeedsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicNewsFeedsReq.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicNewsFeedsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicNewsFeedsReq.Merge(dst, src)
}
func (m *GetNonPublicNewsFeedsReq) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicNewsFeedsReq.Size(m)
}
func (m *GetNonPublicNewsFeedsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicNewsFeedsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicNewsFeedsReq proto.InternalMessageInfo

func (m *GetNonPublicNewsFeedsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNonPublicNewsFeedsReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

type isGetNonPublicNewsFeedsReq_RequestType interface {
	isGetNonPublicNewsFeedsReq_RequestType()
}

type GetNonPublicNewsFeedsReq_SceneStreamReq struct {
	SceneStreamReq *SceneStream `protobuf:"bytes,50,opt,name=scene_stream_req,json=sceneStreamReq,proto3,oneof"`
}

func (*GetNonPublicNewsFeedsReq_SceneStreamReq) isGetNonPublicNewsFeedsReq_RequestType() {}

func (m *GetNonPublicNewsFeedsReq) GetRequestType() isGetNonPublicNewsFeedsReq_RequestType {
	if m != nil {
		return m.RequestType
	}
	return nil
}

func (m *GetNonPublicNewsFeedsReq) GetSceneStreamReq() *SceneStream {
	if x, ok := m.GetRequestType().(*GetNonPublicNewsFeedsReq_SceneStreamReq); ok {
		return x.SceneStreamReq
	}
	return nil
}

func (m *GetNonPublicNewsFeedsReq) GetLoadMore() *NonPublicNewFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetNonPublicNewsFeedsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GetNonPublicNewsFeedsReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GetNonPublicNewsFeedsReq_OneofMarshaler, _GetNonPublicNewsFeedsReq_OneofUnmarshaler, _GetNonPublicNewsFeedsReq_OneofSizer, []interface{}{
		(*GetNonPublicNewsFeedsReq_SceneStreamReq)(nil),
	}
}

func _GetNonPublicNewsFeedsReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GetNonPublicNewsFeedsReq)
	// request_type
	switch x := m.RequestType.(type) {
	case *GetNonPublicNewsFeedsReq_SceneStreamReq:
		b.EncodeVarint(50<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SceneStreamReq); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GetNonPublicNewsFeedsReq.RequestType has unexpected type %T", x)
	}
	return nil
}

func _GetNonPublicNewsFeedsReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GetNonPublicNewsFeedsReq)
	switch tag {
	case 50: // request_type.scene_stream_req
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SceneStream)
		err := b.DecodeMessage(msg)
		m.RequestType = &GetNonPublicNewsFeedsReq_SceneStreamReq{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GetNonPublicNewsFeedsReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GetNonPublicNewsFeedsReq)
	// request_type
	switch x := m.RequestType.(type) {
	case *GetNonPublicNewsFeedsReq_SceneStreamReq:
		s := proto.Size(x.SceneStreamReq)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type GetNonPublicNewsFeedsResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Feeds                []*SceneFeed               `protobuf:"bytes,2,rep,name=feeds,proto3" json:"feeds,omitempty"`
	LoadMore             *NonPublicNewFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Group                string                     `protobuf:"bytes,4,opt,name=group,proto3" json:"group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetNonPublicNewsFeedsResp) Reset()         { *m = GetNonPublicNewsFeedsResp{} }
func (m *GetNonPublicNewsFeedsResp) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicNewsFeedsResp) ProtoMessage()    {}
func (*GetNonPublicNewsFeedsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{30}
}
func (m *GetNonPublicNewsFeedsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicNewsFeedsResp.Unmarshal(m, b)
}
func (m *GetNonPublicNewsFeedsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicNewsFeedsResp.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicNewsFeedsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicNewsFeedsResp.Merge(dst, src)
}
func (m *GetNonPublicNewsFeedsResp) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicNewsFeedsResp.Size(m)
}
func (m *GetNonPublicNewsFeedsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicNewsFeedsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicNewsFeedsResp proto.InternalMessageInfo

func (m *GetNonPublicNewsFeedsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNonPublicNewsFeedsResp) GetFeeds() []*SceneFeed {
	if m != nil {
		return m.Feeds
	}
	return nil
}

func (m *GetNonPublicNewsFeedsResp) GetLoadMore() *NonPublicNewFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetNonPublicNewsFeedsResp) GetGroup() string {
	if m != nil {
		return m.Group
	}
	return ""
}

// 获取帖子详情
type GetNonPublicPostReq struct {
	BaseReq           *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId            string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	OriginSceneStream *SceneStream `protobuf:"bytes,3,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	// 社群id
	//
	// Types that are valid to be assigned to SceneDetail:
	//	*GetNonPublicPostReq_SocialCommunityInfo
	SceneDetail          isGetNonPublicPostReq_SceneDetail `protobuf_oneof:"scene_detail"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetNonPublicPostReq) Reset()         { *m = GetNonPublicPostReq{} }
func (m *GetNonPublicPostReq) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicPostReq) ProtoMessage()    {}
func (*GetNonPublicPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{31}
}
func (m *GetNonPublicPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicPostReq.Unmarshal(m, b)
}
func (m *GetNonPublicPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicPostReq.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicPostReq.Merge(dst, src)
}
func (m *GetNonPublicPostReq) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicPostReq.Size(m)
}
func (m *GetNonPublicPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicPostReq proto.InternalMessageInfo

func (m *GetNonPublicPostReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNonPublicPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetNonPublicPostReq) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

type isGetNonPublicPostReq_SceneDetail interface {
	isGetNonPublicPostReq_SceneDetail()
}

type GetNonPublicPostReq_SocialCommunityInfo struct {
	SocialCommunityInfo *SocialCommunityInfo `protobuf:"bytes,30,opt,name=social_community_info,json=socialCommunityInfo,proto3,oneof"`
}

func (*GetNonPublicPostReq_SocialCommunityInfo) isGetNonPublicPostReq_SceneDetail() {}

func (m *GetNonPublicPostReq) GetSceneDetail() isGetNonPublicPostReq_SceneDetail {
	if m != nil {
		return m.SceneDetail
	}
	return nil
}

func (m *GetNonPublicPostReq) GetSocialCommunityInfo() *SocialCommunityInfo {
	if x, ok := m.GetSceneDetail().(*GetNonPublicPostReq_SocialCommunityInfo); ok {
		return x.SocialCommunityInfo
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*GetNonPublicPostReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _GetNonPublicPostReq_OneofMarshaler, _GetNonPublicPostReq_OneofUnmarshaler, _GetNonPublicPostReq_OneofSizer, []interface{}{
		(*GetNonPublicPostReq_SocialCommunityInfo)(nil),
	}
}

func _GetNonPublicPostReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*GetNonPublicPostReq)
	// scene_detail
	switch x := m.SceneDetail.(type) {
	case *GetNonPublicPostReq_SocialCommunityInfo:
		b.EncodeVarint(30<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SocialCommunityInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("GetNonPublicPostReq.SceneDetail has unexpected type %T", x)
	}
	return nil
}

func _GetNonPublicPostReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*GetNonPublicPostReq)
	switch tag {
	case 30: // scene_detail.social_community_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityInfo)
		err := b.DecodeMessage(msg)
		m.SceneDetail = &GetNonPublicPostReq_SocialCommunityInfo{msg}
		return true, err
	default:
		return false, nil
	}
}

func _GetNonPublicPostReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*GetNonPublicPostReq)
	// scene_detail
	switch x := m.SceneDetail.(type) {
	case *GetNonPublicPostReq_SocialCommunityInfo:
		s := proto.Size(x.SocialCommunityInfo)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type GetNonPublicPostResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PostInfo             *NonPublicPostInfo `protobuf:"bytes,2,opt,name=post_info,json=postInfo,proto3" json:"post_info,omitempty"`
	HasPublishPermission uint32             `protobuf:"varint,3,opt,name=has_publish_permission,json=hasPublishPermission,proto3" json:"has_publish_permission,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetNonPublicPostResp) Reset()         { *m = GetNonPublicPostResp{} }
func (m *GetNonPublicPostResp) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicPostResp) ProtoMessage()    {}
func (*GetNonPublicPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{32}
}
func (m *GetNonPublicPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicPostResp.Unmarshal(m, b)
}
func (m *GetNonPublicPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicPostResp.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicPostResp.Merge(dst, src)
}
func (m *GetNonPublicPostResp) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicPostResp.Size(m)
}
func (m *GetNonPublicPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicPostResp proto.InternalMessageInfo

func (m *GetNonPublicPostResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNonPublicPostResp) GetPostInfo() *NonPublicPostInfo {
	if m != nil {
		return m.PostInfo
	}
	return nil
}

func (m *GetNonPublicPostResp) GetHasPublishPermission() uint32 {
	if m != nil {
		return m.HasPublishPermission
	}
	return 0
}

// 发评论/回复评论
type PostNonPublicCommentReq struct {
	BaseReq           *app.BaseReq           `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OriginSceneStream *SceneStream           `protobuf:"bytes,2,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	PostId            string                 `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId         string                 `protobuf:"bytes,4,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	ConversationId    string                 `protobuf:"bytes,5,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
	Content           string                 `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	Attachments       []*NonPublicAttachment `protobuf:"bytes,7,rep,name=attachments,proto3" json:"attachments,omitempty"`
	// V3.4.2 - 支持图片评论（协议预留支持多图，目前只支持单图）
	// 社群id
	//
	// Types that are valid to be assigned to SceneDetail:
	//	*PostNonPublicCommentReq_SocialCommunityInfo
	SceneDetail          isPostNonPublicCommentReq_SceneDetail `protobuf_oneof:"scene_detail"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *PostNonPublicCommentReq) Reset()         { *m = PostNonPublicCommentReq{} }
func (m *PostNonPublicCommentReq) String() string { return proto.CompactTextString(m) }
func (*PostNonPublicCommentReq) ProtoMessage()    {}
func (*PostNonPublicCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{33}
}
func (m *PostNonPublicCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostNonPublicCommentReq.Unmarshal(m, b)
}
func (m *PostNonPublicCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostNonPublicCommentReq.Marshal(b, m, deterministic)
}
func (dst *PostNonPublicCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostNonPublicCommentReq.Merge(dst, src)
}
func (m *PostNonPublicCommentReq) XXX_Size() int {
	return xxx_messageInfo_PostNonPublicCommentReq.Size(m)
}
func (m *PostNonPublicCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PostNonPublicCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_PostNonPublicCommentReq proto.InternalMessageInfo

func (m *PostNonPublicCommentReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PostNonPublicCommentReq) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

func (m *PostNonPublicCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostNonPublicCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *PostNonPublicCommentReq) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *PostNonPublicCommentReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PostNonPublicCommentReq) GetAttachments() []*NonPublicAttachment {
	if m != nil {
		return m.Attachments
	}
	return nil
}

type isPostNonPublicCommentReq_SceneDetail interface {
	isPostNonPublicCommentReq_SceneDetail()
}

type PostNonPublicCommentReq_SocialCommunityInfo struct {
	SocialCommunityInfo *SocialCommunityInfo `protobuf:"bytes,30,opt,name=social_community_info,json=socialCommunityInfo,proto3,oneof"`
}

func (*PostNonPublicCommentReq_SocialCommunityInfo) isPostNonPublicCommentReq_SceneDetail() {}

func (m *PostNonPublicCommentReq) GetSceneDetail() isPostNonPublicCommentReq_SceneDetail {
	if m != nil {
		return m.SceneDetail
	}
	return nil
}

func (m *PostNonPublicCommentReq) GetSocialCommunityInfo() *SocialCommunityInfo {
	if x, ok := m.GetSceneDetail().(*PostNonPublicCommentReq_SocialCommunityInfo); ok {
		return x.SocialCommunityInfo
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*PostNonPublicCommentReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _PostNonPublicCommentReq_OneofMarshaler, _PostNonPublicCommentReq_OneofUnmarshaler, _PostNonPublicCommentReq_OneofSizer, []interface{}{
		(*PostNonPublicCommentReq_SocialCommunityInfo)(nil),
	}
}

func _PostNonPublicCommentReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*PostNonPublicCommentReq)
	// scene_detail
	switch x := m.SceneDetail.(type) {
	case *PostNonPublicCommentReq_SocialCommunityInfo:
		b.EncodeVarint(30<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SocialCommunityInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("PostNonPublicCommentReq.SceneDetail has unexpected type %T", x)
	}
	return nil
}

func _PostNonPublicCommentReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*PostNonPublicCommentReq)
	switch tag {
	case 30: // scene_detail.social_community_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityInfo)
		err := b.DecodeMessage(msg)
		m.SceneDetail = &PostNonPublicCommentReq_SocialCommunityInfo{msg}
		return true, err
	default:
		return false, nil
	}
}

func _PostNonPublicCommentReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*PostNonPublicCommentReq)
	// scene_detail
	switch x := m.SceneDetail.(type) {
	case *PostNonPublicCommentReq_SocialCommunityInfo:
		s := proto.Size(x.SocialCommunityInfo)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type PostNonPublicCommentResp struct {
	BaseResp  *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PostId    string                `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId string                `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	MyComment *NonPublicCommentInfo `protobuf:"bytes,4,opt,name=my_comment,json=myComment,proto3" json:"my_comment,omitempty"`
	// V3.4.2 - 上传图片用的token及key（协议预留支持多图，目前只支持单图）
	ImageToken           string   `protobuf:"bytes,5,opt,name=image_token,json=imageToken,proto3" json:"image_token,omitempty"`
	ImageKeys            []string `protobuf:"bytes,6,rep,name=image_keys,json=imageKeys,proto3" json:"image_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostNonPublicCommentResp) Reset()         { *m = PostNonPublicCommentResp{} }
func (m *PostNonPublicCommentResp) String() string { return proto.CompactTextString(m) }
func (*PostNonPublicCommentResp) ProtoMessage()    {}
func (*PostNonPublicCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{34}
}
func (m *PostNonPublicCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostNonPublicCommentResp.Unmarshal(m, b)
}
func (m *PostNonPublicCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostNonPublicCommentResp.Marshal(b, m, deterministic)
}
func (dst *PostNonPublicCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostNonPublicCommentResp.Merge(dst, src)
}
func (m *PostNonPublicCommentResp) XXX_Size() int {
	return xxx_messageInfo_PostNonPublicCommentResp.Size(m)
}
func (m *PostNonPublicCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PostNonPublicCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_PostNonPublicCommentResp proto.InternalMessageInfo

func (m *PostNonPublicCommentResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PostNonPublicCommentResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostNonPublicCommentResp) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *PostNonPublicCommentResp) GetMyComment() *NonPublicCommentInfo {
	if m != nil {
		return m.MyComment
	}
	return nil
}

func (m *PostNonPublicCommentResp) GetImageToken() string {
	if m != nil {
		return m.ImageToken
	}
	return ""
}

func (m *PostNonPublicCommentResp) GetImageKeys() []string {
	if m != nil {
		return m.ImageKeys
	}
	return nil
}

// 2.4.8 获取评论列表,热门评论
type GetNonPublicCommentListReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId  string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	// 如果传了，表示拉子评论
	CommentId            string   `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	LoadMore             string   `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	QueryReplyingComment bool     `protobuf:"varint,6,opt,name=query_replying_comment,json=queryReplyingComment,proto3" json:"query_replying_comment,omitempty"`
	ExtraCommentId       string   `protobuf:"bytes,7,opt,name=extra_comment_id,json=extraCommentId,proto3" json:"extra_comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNonPublicCommentListReq) Reset()         { *m = GetNonPublicCommentListReq{} }
func (m *GetNonPublicCommentListReq) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicCommentListReq) ProtoMessage()    {}
func (*GetNonPublicCommentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{35}
}
func (m *GetNonPublicCommentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicCommentListReq.Unmarshal(m, b)
}
func (m *GetNonPublicCommentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicCommentListReq.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicCommentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicCommentListReq.Merge(dst, src)
}
func (m *GetNonPublicCommentListReq) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicCommentListReq.Size(m)
}
func (m *GetNonPublicCommentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicCommentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicCommentListReq proto.InternalMessageInfo

func (m *GetNonPublicCommentListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNonPublicCommentListReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetNonPublicCommentListReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *GetNonPublicCommentListReq) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *GetNonPublicCommentListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetNonPublicCommentListReq) GetQueryReplyingComment() bool {
	if m != nil {
		return m.QueryReplyingComment
	}
	return false
}

func (m *GetNonPublicCommentListReq) GetExtraCommentId() string {
	if m != nil {
		return m.ExtraCommentId
	}
	return ""
}

type GetNonPublicCommentListResp struct {
	BaseResp   *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PostId     string                  `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Comments   []*NonPublicCommentInfo `protobuf:"bytes,3,rep,name=comments,proto3" json:"comments,omitempty"`
	LoadMore   string                  `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	TopComment []*TopComment           `protobuf:"bytes,5,rep,name=top_comment,json=topComment,proto3" json:"top_comment,omitempty"`
	// 首次拉取会拉取所有的TopComment+指定数量的normalcomment
	ReplyingComment      *NonPublicCommentInfo `protobuf:"bytes,6,opt,name=replying_comment,json=replyingComment,proto3" json:"replying_comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetNonPublicCommentListResp) Reset()         { *m = GetNonPublicCommentListResp{} }
func (m *GetNonPublicCommentListResp) String() string { return proto.CompactTextString(m) }
func (*GetNonPublicCommentListResp) ProtoMessage()    {}
func (*GetNonPublicCommentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{36}
}
func (m *GetNonPublicCommentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNonPublicCommentListResp.Unmarshal(m, b)
}
func (m *GetNonPublicCommentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNonPublicCommentListResp.Marshal(b, m, deterministic)
}
func (dst *GetNonPublicCommentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNonPublicCommentListResp.Merge(dst, src)
}
func (m *GetNonPublicCommentListResp) XXX_Size() int {
	return xxx_messageInfo_GetNonPublicCommentListResp.Size(m)
}
func (m *GetNonPublicCommentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNonPublicCommentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNonPublicCommentListResp proto.InternalMessageInfo

func (m *GetNonPublicCommentListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNonPublicCommentListResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetNonPublicCommentListResp) GetComments() []*NonPublicCommentInfo {
	if m != nil {
		return m.Comments
	}
	return nil
}

func (m *GetNonPublicCommentListResp) GetLoadMore() string {
	if m != nil {
		return m.LoadMore
	}
	return ""
}

func (m *GetNonPublicCommentListResp) GetTopComment() []*TopComment {
	if m != nil {
		return m.TopComment
	}
	return nil
}

func (m *GetNonPublicCommentListResp) GetReplyingComment() *NonPublicCommentInfo {
	if m != nil {
		return m.ReplyingComment
	}
	return nil
}

// 点赞
type NonPublicExpressAttitudeReq struct {
	BaseReq           *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OriginSceneStream *SceneStream `protobuf:"bytes,2,opt,name=origin_scene_stream,json=originSceneStream,proto3" json:"origin_scene_stream,omitempty"`
	PostId            string       `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId         string       `protobuf:"bytes,4,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Attitude          int32        `protobuf:"varint,5,opt,name=attitude,proto3" json:"attitude,omitempty"`
	StepOn            int32        `protobuf:"varint,6,opt,name=step_on,json=stepOn,proto3" json:"step_on,omitempty"`
	// Types that are valid to be assigned to SceneDetail:
	//	*NonPublicExpressAttitudeReq_SocialCommunityInfo
	SceneDetail          isNonPublicExpressAttitudeReq_SceneDetail `protobuf_oneof:"scene_detail"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *NonPublicExpressAttitudeReq) Reset()         { *m = NonPublicExpressAttitudeReq{} }
func (m *NonPublicExpressAttitudeReq) String() string { return proto.CompactTextString(m) }
func (*NonPublicExpressAttitudeReq) ProtoMessage()    {}
func (*NonPublicExpressAttitudeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{37}
}
func (m *NonPublicExpressAttitudeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicExpressAttitudeReq.Unmarshal(m, b)
}
func (m *NonPublicExpressAttitudeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicExpressAttitudeReq.Marshal(b, m, deterministic)
}
func (dst *NonPublicExpressAttitudeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicExpressAttitudeReq.Merge(dst, src)
}
func (m *NonPublicExpressAttitudeReq) XXX_Size() int {
	return xxx_messageInfo_NonPublicExpressAttitudeReq.Size(m)
}
func (m *NonPublicExpressAttitudeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicExpressAttitudeReq.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicExpressAttitudeReq proto.InternalMessageInfo

func (m *NonPublicExpressAttitudeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *NonPublicExpressAttitudeReq) GetOriginSceneStream() *SceneStream {
	if m != nil {
		return m.OriginSceneStream
	}
	return nil
}

func (m *NonPublicExpressAttitudeReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *NonPublicExpressAttitudeReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *NonPublicExpressAttitudeReq) GetAttitude() int32 {
	if m != nil {
		return m.Attitude
	}
	return 0
}

func (m *NonPublicExpressAttitudeReq) GetStepOn() int32 {
	if m != nil {
		return m.StepOn
	}
	return 0
}

type isNonPublicExpressAttitudeReq_SceneDetail interface {
	isNonPublicExpressAttitudeReq_SceneDetail()
}

type NonPublicExpressAttitudeReq_SocialCommunityInfo struct {
	SocialCommunityInfo *SocialCommunityInfo `protobuf:"bytes,7,opt,name=social_community_info,json=socialCommunityInfo,proto3,oneof"`
}

func (*NonPublicExpressAttitudeReq_SocialCommunityInfo) isNonPublicExpressAttitudeReq_SceneDetail() {}

func (m *NonPublicExpressAttitudeReq) GetSceneDetail() isNonPublicExpressAttitudeReq_SceneDetail {
	if m != nil {
		return m.SceneDetail
	}
	return nil
}

func (m *NonPublicExpressAttitudeReq) GetSocialCommunityInfo() *SocialCommunityInfo {
	if x, ok := m.GetSceneDetail().(*NonPublicExpressAttitudeReq_SocialCommunityInfo); ok {
		return x.SocialCommunityInfo
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*NonPublicExpressAttitudeReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _NonPublicExpressAttitudeReq_OneofMarshaler, _NonPublicExpressAttitudeReq_OneofUnmarshaler, _NonPublicExpressAttitudeReq_OneofSizer, []interface{}{
		(*NonPublicExpressAttitudeReq_SocialCommunityInfo)(nil),
	}
}

func _NonPublicExpressAttitudeReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*NonPublicExpressAttitudeReq)
	// scene_detail
	switch x := m.SceneDetail.(type) {
	case *NonPublicExpressAttitudeReq_SocialCommunityInfo:
		b.EncodeVarint(7<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SocialCommunityInfo); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("NonPublicExpressAttitudeReq.SceneDetail has unexpected type %T", x)
	}
	return nil
}

func _NonPublicExpressAttitudeReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*NonPublicExpressAttitudeReq)
	switch tag {
	case 7: // scene_detail.social_community_info
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityInfo)
		err := b.DecodeMessage(msg)
		m.SceneDetail = &NonPublicExpressAttitudeReq_SocialCommunityInfo{msg}
		return true, err
	default:
		return false, nil
	}
}

func _NonPublicExpressAttitudeReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*NonPublicExpressAttitudeReq)
	// scene_detail
	switch x := m.SceneDetail.(type) {
	case *NonPublicExpressAttitudeReq_SocialCommunityInfo:
		s := proto.Size(x.SocialCommunityInfo)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type NonPublicExpressAttitudeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	AttitudeCount        int32         `protobuf:"varint,2,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	HasPublishPermission uint32        `protobuf:"varint,3,opt,name=has_publish_permission,json=hasPublishPermission,proto3" json:"has_publish_permission,omitempty"`
	StepCount            int32         `protobuf:"varint,4,opt,name=step_count,json=stepCount,proto3" json:"step_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *NonPublicExpressAttitudeResp) Reset()         { *m = NonPublicExpressAttitudeResp{} }
func (m *NonPublicExpressAttitudeResp) String() string { return proto.CompactTextString(m) }
func (*NonPublicExpressAttitudeResp) ProtoMessage()    {}
func (*NonPublicExpressAttitudeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{38}
}
func (m *NonPublicExpressAttitudeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicExpressAttitudeResp.Unmarshal(m, b)
}
func (m *NonPublicExpressAttitudeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicExpressAttitudeResp.Marshal(b, m, deterministic)
}
func (dst *NonPublicExpressAttitudeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicExpressAttitudeResp.Merge(dst, src)
}
func (m *NonPublicExpressAttitudeResp) XXX_Size() int {
	return xxx_messageInfo_NonPublicExpressAttitudeResp.Size(m)
}
func (m *NonPublicExpressAttitudeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicExpressAttitudeResp.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicExpressAttitudeResp proto.InternalMessageInfo

func (m *NonPublicExpressAttitudeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *NonPublicExpressAttitudeResp) GetAttitudeCount() int32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

func (m *NonPublicExpressAttitudeResp) GetHasPublishPermission() uint32 {
	if m != nil {
		return m.HasPublishPermission
	}
	return 0
}

func (m *NonPublicExpressAttitudeResp) GetStepCount() int32 {
	if m != nil {
		return m.StepCount
	}
	return 0
}

type NonPublicCommentLoadMore struct {
	NextCommentId        string   `protobuf:"bytes,1,opt,name=next_comment_id,json=nextCommentId,proto3" json:"next_comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NonPublicCommentLoadMore) Reset()         { *m = NonPublicCommentLoadMore{} }
func (m *NonPublicCommentLoadMore) String() string { return proto.CompactTextString(m) }
func (*NonPublicCommentLoadMore) ProtoMessage()    {}
func (*NonPublicCommentLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{39}
}
func (m *NonPublicCommentLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonPublicCommentLoadMore.Unmarshal(m, b)
}
func (m *NonPublicCommentLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonPublicCommentLoadMore.Marshal(b, m, deterministic)
}
func (dst *NonPublicCommentLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonPublicCommentLoadMore.Merge(dst, src)
}
func (m *NonPublicCommentLoadMore) XXX_Size() int {
	return xxx_messageInfo_NonPublicCommentLoadMore.Size(m)
}
func (m *NonPublicCommentLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_NonPublicCommentLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_NonPublicCommentLoadMore proto.InternalMessageInfo

func (m *NonPublicCommentLoadMore) GetNextCommentId() string {
	if m != nil {
		return m.NextCommentId
	}
	return ""
}

type MarkUserStreamRecordReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	StreamId             string       `protobuf:"bytes,2,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	SceneId              string       `protobuf:"bytes,3,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MarkUserStreamRecordReq) Reset()         { *m = MarkUserStreamRecordReq{} }
func (m *MarkUserStreamRecordReq) String() string { return proto.CompactTextString(m) }
func (*MarkUserStreamRecordReq) ProtoMessage()    {}
func (*MarkUserStreamRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{40}
}
func (m *MarkUserStreamRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkUserStreamRecordReq.Unmarshal(m, b)
}
func (m *MarkUserStreamRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkUserStreamRecordReq.Marshal(b, m, deterministic)
}
func (dst *MarkUserStreamRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkUserStreamRecordReq.Merge(dst, src)
}
func (m *MarkUserStreamRecordReq) XXX_Size() int {
	return xxx_messageInfo_MarkUserStreamRecordReq.Size(m)
}
func (m *MarkUserStreamRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkUserStreamRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkUserStreamRecordReq proto.InternalMessageInfo

func (m *MarkUserStreamRecordReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MarkUserStreamRecordReq) GetStreamId() string {
	if m != nil {
		return m.StreamId
	}
	return ""
}

func (m *MarkUserStreamRecordReq) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

type MarkUserStreamRecordResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MarkUserStreamRecordResp) Reset()         { *m = MarkUserStreamRecordResp{} }
func (m *MarkUserStreamRecordResp) String() string { return proto.CompactTextString(m) }
func (*MarkUserStreamRecordResp) ProtoMessage()    {}
func (*MarkUserStreamRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8, []int{41}
}
func (m *MarkUserStreamRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkUserStreamRecordResp.Unmarshal(m, b)
}
func (m *MarkUserStreamRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkUserStreamRecordResp.Marshal(b, m, deterministic)
}
func (dst *MarkUserStreamRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkUserStreamRecordResp.Merge(dst, src)
}
func (m *MarkUserStreamRecordResp) XXX_Size() int {
	return xxx_messageInfo_MarkUserStreamRecordResp.Size(m)
}
func (m *MarkUserStreamRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkUserStreamRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkUserStreamRecordResp proto.InternalMessageInfo

func (m *MarkUserStreamRecordResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*NonPublicAttachment)(nil), "ga.ugc_non_public.NonPublicAttachment")
	proto.RegisterType((*NonPublicExtra)(nil), "ga.ugc_non_public.NonPublicExtra")
	proto.RegisterType((*NonPublicRichTextWords)(nil), "ga.ugc_non_public.NonPublicRichTextWords")
	proto.RegisterType((*NonPublicURLCard)(nil), "ga.ugc_non_public.NonPublicURLCard")
	proto.RegisterType((*NonPublicRichTextElement)(nil), "ga.ugc_non_public.NonPublicRichTextElement")
	proto.RegisterType((*SceneStream)(nil), "ga.ugc_non_public.SceneStream")
	proto.RegisterType((*SceneFeedPost)(nil), "ga.ugc_non_public.SceneFeedPost")
	proto.RegisterType((*SceneFeed)(nil), "ga.ugc_non_public.SceneFeed")
	proto.RegisterType((*LastFeedInfo)(nil), "ga.ugc_non_public.LastFeedInfo")
	proto.RegisterType((*NonPublicNewFeedsLoadMore)(nil), "ga.ugc_non_public.NonPublicNewFeedsLoadMore")
	proto.RegisterType((*NonPublicPostInfo)(nil), "ga.ugc_non_public.NonPublicPostInfo")
	proto.RegisterType((*CornerInfo)(nil), "ga.ugc_non_public.CornerInfo")
	proto.RegisterType((*PostExtendSocialCommunity)(nil), "ga.ugc_non_public.PostExtendSocialCommunity")
	proto.RegisterType((*CommonUserUGCInfo)(nil), "ga.ugc_non_public.CommonUserUGCInfo")
	proto.RegisterType((*SocialCommunityUserUGCInfo)(nil), "ga.ugc_non_public.SocialCommunityUserUGCInfo")
	proto.RegisterType((*SocialCommunityInfo)(nil), "ga.ugc_non_public.SocialCommunityInfo")
	proto.RegisterType((*SocialCommunityPublishOpt)(nil), "ga.ugc_non_public.SocialCommunityPublishOpt")
	proto.RegisterType((*NonPublicUploadAttachmentInfo)(nil), "ga.ugc_non_public.NonPublicUploadAttachmentInfo")
	proto.RegisterType((*NewAttitudeMsg)(nil), "ga.ugc_non_public.NewAttitudeMsg")
	proto.RegisterType((*NewCommentMsg)(nil), "ga.ugc_non_public.NewCommentMsg")
	proto.RegisterType((*TopComment)(nil), "ga.ugc_non_public.TopComment")
	proto.RegisterType((*NonPublicCommentInfo)(nil), "ga.ugc_non_public.NonPublicCommentInfo")
	proto.RegisterType((*PostNonPublicPostReq)(nil), "ga.ugc_non_public.PostNonPublicPostReq")
	proto.RegisterType((*PostNonPublicPostResp)(nil), "ga.ugc_non_public.PostNonPublicPostResp")
	proto.RegisterType((*GetNonPublicPostPublishExtendReq)(nil), "ga.ugc_non_public.GetNonPublicPostPublishExtendReq")
	proto.RegisterType((*GetNonPublicPostPublishExtendResp)(nil), "ga.ugc_non_public.GetNonPublicPostPublishExtendResp")
	proto.RegisterType((*WhiteListSites)(nil), "ga.ugc_non_public.WhiteListSites")
	proto.RegisterType((*MarkNonPublicPostAttachmentUploadedReq)(nil), "ga.ugc_non_public.MarkNonPublicPostAttachmentUploadedReq")
	proto.RegisterType((*MarkNonPublicPostAttachmentUploadedResp)(nil), "ga.ugc_non_public.MarkNonPublicPostAttachmentUploadedResp")
	proto.RegisterType((*GetNonPublicNewsFeedsReq)(nil), "ga.ugc_non_public.GetNonPublicNewsFeedsReq")
	proto.RegisterType((*GetNonPublicNewsFeedsResp)(nil), "ga.ugc_non_public.GetNonPublicNewsFeedsResp")
	proto.RegisterType((*GetNonPublicPostReq)(nil), "ga.ugc_non_public.GetNonPublicPostReq")
	proto.RegisterType((*GetNonPublicPostResp)(nil), "ga.ugc_non_public.GetNonPublicPostResp")
	proto.RegisterType((*PostNonPublicCommentReq)(nil), "ga.ugc_non_public.PostNonPublicCommentReq")
	proto.RegisterType((*PostNonPublicCommentResp)(nil), "ga.ugc_non_public.PostNonPublicCommentResp")
	proto.RegisterType((*GetNonPublicCommentListReq)(nil), "ga.ugc_non_public.GetNonPublicCommentListReq")
	proto.RegisterType((*GetNonPublicCommentListResp)(nil), "ga.ugc_non_public.GetNonPublicCommentListResp")
	proto.RegisterType((*NonPublicExpressAttitudeReq)(nil), "ga.ugc_non_public.NonPublicExpressAttitudeReq")
	proto.RegisterType((*NonPublicExpressAttitudeResp)(nil), "ga.ugc_non_public.NonPublicExpressAttitudeResp")
	proto.RegisterType((*NonPublicCommentLoadMore)(nil), "ga.ugc_non_public.NonPublicCommentLoadMore")
	proto.RegisterType((*MarkUserStreamRecordReq)(nil), "ga.ugc_non_public.MarkUserStreamRecordReq")
	proto.RegisterType((*MarkUserStreamRecordResp)(nil), "ga.ugc_non_public.MarkUserStreamRecordResp")
	proto.RegisterEnum("ga.ugc_non_public.NonPublicAttachmentType", NonPublicAttachmentType_name, NonPublicAttachmentType_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicAbnormalStatus", NonPublicAbnormalStatus_name, NonPublicAbnormalStatus_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicAttachmentDownloadPrivacy", NonPublicAttachmentDownloadPrivacy_name, NonPublicAttachmentDownloadPrivacy_value)
	proto.RegisterEnum("ga.ugc_non_public.SceneStreamType", SceneStreamType_name, SceneStreamType_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicPostPrivacyPolicy", NonPublicPostPrivacyPolicy_name, NonPublicPostPrivacyPolicy_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicPostMachineStatus", NonPublicPostMachineStatus_name, NonPublicPostMachineStatus_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicInteractiveType", NonPublicInteractiveType_name, NonPublicInteractiveType_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicPostOrigin", NonPublicPostOrigin_name, NonPublicPostOrigin_value)
	proto.RegisterEnum("ga.ugc_non_public.SceneStreamOpt", SceneStreamOpt_name, SceneStreamOpt_value)
	proto.RegisterEnum("ga.ugc_non_public.CornerType", CornerType_name, CornerType_value)
	proto.RegisterEnum("ga.ugc_non_public.PublishTextStyleType", PublishTextStyleType_name, PublishTextStyleType_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicAttitudeType", NonPublicAttitudeType_name, NonPublicAttitudeType_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicStepOn", NonPublicStepOn_name, NonPublicStepOn_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicStepType", NonPublicStepType_name, NonPublicStepType_value)
	proto.RegisterEnum("ga.ugc_non_public.ContentStreamPermission", ContentStreamPermission_name, ContentStreamPermission_value)
	proto.RegisterEnum("ga.ugc_non_public.NonPublicPostInfo_PostStatus", NonPublicPostInfo_PostStatus_name, NonPublicPostInfo_PostStatus_value)
}

func init() {
	proto.RegisterFile("ugc_non_public/ugc_non_public.proto", fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8)
}

var fileDescriptor_ugc_non_public_d9b09b6b7d87c7e8 = []byte{
	// 3971 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x5b, 0xcd, 0x73, 0xe3, 0xd8,
	0x56, 0x6f, 0xd9, 0xf9, 0xb0, 0x8f, 0xf3, 0xe1, 0xa8, 0x33, 0x1d, 0xf5, 0x47, 0xa6, 0xd3, 0xea,
	0xe9, 0xaf, 0x4c, 0x4f, 0x66, 0x08, 0xd3, 0xf0, 0xde, 0x82, 0xa9, 0xe7, 0x38, 0xee, 0x8e, 0xbb,
	0x13, 0x3b, 0xc8, 0x4e, 0x37, 0x0d, 0x14, 0x42, 0x91, 0x6e, 0x6c, 0x4d, 0xcb, 0x92, 0x5a, 0x92,
	0x3b, 0xed, 0x82, 0x25, 0x50, 0x45, 0x51, 0x05, 0x2c, 0x60, 0xf1, 0xf8, 0x13, 0xd8, 0xb2, 0xa3,
	0xde, 0xee, 0x3d, 0xfe, 0x00, 0x8a, 0xc5, 0x83, 0xa2, 0x8a, 0x2a, 0x8a, 0x7f, 0xe0, 0x15, 0x55,
	0xec, 0x58, 0x50, 0xe7, 0x5c, 0x49, 0x96, 0x6c, 0xd9, 0x89, 0x67, 0x02, 0xc5, 0x5b, 0xc5, 0xf7,
	0x9c, 0x73, 0x3f, 0xce, 0x3d, 0x1f, 0xf7, 0x77, 0xee, 0x55, 0xe0, 0x7e, 0xbf, 0xa3, 0xab, 0xb6,
	0x63, 0xab, 0x6e, 0xff, 0xd4, 0x32, 0xf5, 0x2f, 0xd3, 0xcd, 0x1d, 0xd7, 0x73, 0x02, 0x47, 0x5c,
	0xeb, 0x68, 0x3b, 0x69, 0xc6, 0xad, 0xe5, 0x8e, 0xa6, 0x9e, 0x6a, 0x3e, 0xe3, 0x12, 0xf2, 0xcf,
	0x73, 0x70, 0xbd, 0xe1, 0xd8, 0xc7, 0xc4, 0xac, 0x04, 0x81, 0xa6, 0x77, 0x7b, 0xcc, 0x0e, 0xc4,
	0x47, 0xb0, 0xaa, 0xc5, 0x2d, 0x35, 0x18, 0xb8, 0x4c, 0x12, 0xb6, 0x84, 0xc7, 0xcb, 0xca, 0xca,
	0x90, 0xdc, 0x1e, 0xb8, 0x4c, 0xfc, 0x02, 0xc4, 0x84, 0xa0, 0xee, 0xd8, 0x01, 0xb3, 0x03, 0x29,
	0xb7, 0x25, 0x3c, 0x2e, 0x2a, 0x6b, 0x43, 0x4e, 0x95, 0x33, 0xc4, 0x4d, 0x00, 0xf6, 0x31, 0xf0,
	0x34, 0xd5, 0xb4, 0xcf, 0x1c, 0x29, 0x4f, 0x62, 0x45, 0xa2, 0xd4, 0xed, 0x33, 0x47, 0xbc, 0x0d,
	0x45, 0xdd, 0x63, 0x5a, 0xc0, 0x54, 0x2d, 0x90, 0xe6, 0xb6, 0x84, 0xc7, 0x73, 0x4a, 0x81, 0x13,
	0x2a, 0x81, 0x58, 0x86, 0xfc, 0x3b, 0x36, 0x90, 0xe6, 0xa9, 0x13, 0xfe, 0xc4, 0xd1, 0x3e, 0xf4,
	0xe2, 0x49, 0x17, 0xf8, 0x68, 0x1f, 0x7a, 0xd1, 0x64, 0xeb, 0x30, 0xef, 0x6a, 0x9e, 0xd6, 0x93,
	0x16, 0x89, 0xc3, 0x1b, 0xe2, 0x53, 0x10, 0x1d, 0xcf, 0xec, 0x98, 0xb6, 0xfa, 0xc1, 0x34, 0x98,
	0xa3, 0xea, 0xce, 0x07, 0xe6, 0x49, 0x05, 0x12, 0x29, 0x73, 0xce, 0x6b, 0x64, 0x54, 0x91, 0x2e,
	0xfe, 0x3a, 0xcc, 0xd3, 0xf2, 0xa4, 0xe2, 0x96, 0xf0, 0xb8, 0xb4, 0x7b, 0x6f, 0x67, 0x6c, 0x4b,
	0x77, 0xe2, 0xfd, 0xab, 0xa1, 0xa0, 0xc2, 0xe5, 0xe5, 0x6f, 0x60, 0x25, 0xcd, 0xc0, 0xe5, 0x9c,
	0x9b, 0x46, 0xd0, 0x0d, 0x77, 0x92, 0x37, 0xc4, 0x1b, 0xb0, 0xd0, 0x65, 0x66, 0x27, 0xe8, 0xd2,
	0xa6, 0x2d, 0x2b, 0x61, 0x4b, 0x7e, 0x0a, 0x37, 0xe2, 0xfe, 0x8a, 0xa9, 0x77, 0xdb, 0xec, 0x63,
	0xf0, 0xc6, 0xf1, 0x0c, 0x5f, 0x14, 0x61, 0x2e, 0x60, 0x1f, 0x03, 0x1a, 0xa6, 0xa8, 0xd0, 0x6f,
	0xf9, 0x9f, 0x04, 0x28, 0xc7, 0xe2, 0x27, 0xca, 0x61, 0x55, 0xf3, 0x0c, 0xdc, 0x4d, 0xbf, 0xeb,
	0x9c, 0xab, 0x09, 0xe9, 0x02, 0x12, 0x70, 0x28, 0x51, 0x86, 0x65, 0x62, 0x9a, 0xba, 0x63, 0xab,
	0x7d, 0xcf, 0x0a, 0x6d, 0x56, 0x42, 0x62, 0x5d, 0x77, 0xec, 0x13, 0xcf, 0xc2, 0xad, 0x22, 0x19,
	0xd7, 0xd2, 0x82, 0x33, 0xc7, 0xeb, 0xa9, 0xb6, 0xd6, 0x63, 0xa1, 0xd5, 0xca, 0xc8, 0x39, 0x0e,
	0x19, 0x0d, 0xad, 0xc7, 0xc6, 0xa5, 0x71, 0x68, 0xb2, 0xe2, 0x88, 0x34, 0x0e, 0x8f, 0xf3, 0x7b,
	0x4c, 0xb3, 0x70, 0x6a, 0x55, 0x33, 0x0c, 0x2f, 0xb4, 0x6b, 0x09, 0x89, 0x27, 0x9e, 0x55, 0x31,
	0x0c, 0x4f, 0xfe, 0x2f, 0x01, 0xa4, 0xb1, 0x4d, 0xa8, 0x59, 0x8c, 0x5c, 0xb4, 0x02, 0xf3, 0xe7,
	0xb8, 0x1f, 0xa4, 0x59, 0x69, 0xf7, 0xc9, 0x34, 0xcb, 0xa4, 0x36, 0xf0, 0xe0, 0x9a, 0xc2, 0x7b,
	0x8a, 0x75, 0x28, 0xf5, 0xfa, 0x56, 0x60, 0xaa, 0x3d, 0x66, 0x98, 0x1a, 0xed, 0x40, 0x69, 0xf7,
	0xe1, 0xb4, 0x81, 0x86, 0x21, 0x72, 0x70, 0x4d, 0x01, 0xea, 0x7c, 0x84, 0x7d, 0xc5, 0x1f, 0x41,
	0x01, 0x35, 0xd1, 0x35, 0xcf, 0xa0, 0x0d, 0x2a, 0xed, 0xde, 0x9f, 0x36, 0x4e, 0x68, 0xa2, 0x83,
	0x6b, 0xca, 0x62, 0xdf, 0xb3, 0xf0, 0xe7, 0x5e, 0x11, 0x16, 0x43, 0x4f, 0x96, 0x7f, 0x21, 0x40,
	0xa9, 0xa5, 0x33, 0x9b, 0xb5, 0x02, 0x8f, 0x69, 0x3d, 0x32, 0x24, 0xfd, 0x52, 0x4d, 0x23, 0x36,
	0x24, 0x11, 0xea, 0x06, 0x06, 0x81, 0xdf, 0x75, 0xbc, 0x80, 0x1b, 0x87, 0x5b, 0xb1, 0x48, 0x14,
	0xb2, 0xca, 0x7d, 0x58, 0xd6, 0x9d, 0x9e, 0x6b, 0xb1, 0x80, 0x71, 0x47, 0xe0, 0xe6, 0x5b, 0x8a,
	0x88, 0xe4, 0x0c, 0x22, 0xcc, 0x59, 0x4e, 0xc7, 0x09, 0x8d, 0x45, 0xbf, 0xc5, 0xbb, 0x50, 0x0a,
	0x27, 0xa5, 0xf0, 0x9f, 0x27, 0xef, 0x04, 0x4e, 0xa2, 0xd0, 0x2f, 0x43, 0xde, 0x71, 0x79, 0xd8,
	0x2d, 0x2b, 0xf8, 0x53, 0xfc, 0x21, 0xdc, 0xd4, 0xb5, 0x80, 0x75, 0x1c, 0x6f, 0x40, 0x9d, 0x54,
	0xdf, 0xc4, 0x49, 0x54, 0x83, 0xf9, 0x7a, 0x18, 0x84, 0x37, 0x22, 0x01, 0x1c, 0xa2, 0x45, 0xec,
	0x7d, 0xe6, 0xeb, 0xf2, 0x9f, 0x0a, 0xb0, 0x4c, 0x2a, 0x3f, 0x67, 0xcc, 0x38, 0x76, 0xfc, 0x40,
	0xfc, 0x01, 0xcc, 0xb9, 0x8e, 0x1f, 0x84, 0xe6, 0xfd, 0x6c, 0xda, 0x6e, 0xa2, 0x3c, 0xe6, 0x0f,
	0x85, 0x7a, 0x88, 0xdf, 0x40, 0x29, 0x70, 0x5c, 0x55, 0x77, 0x7a, 0xbd, 0x28, 0x19, 0x95, 0x76,
	0x37, 0x33, 0x06, 0x68, 0x3b, 0x6e, 0x95, 0x0b, 0x29, 0x10, 0xc4, 0xbf, 0xe5, 0x00, 0x8a, 0xf1,
	0x52, 0xc4, 0x0a, 0x80, 0x8f, 0x0d, 0x35, 0xb1, 0x98, 0xad, 0x8c, 0xb1, 0x52, 0x8b, 0x3f, 0xb8,
	0xa6, 0x14, 0xa9, 0x17, 0x69, 0xb2, 0x01, 0x8b, 0x67, 0x8c, 0x19, 0x68, 0xbc, 0x4f, 0x69, 0x13,
	0x16, 0xb0, 0x59, 0x37, 0xf6, 0x4a, 0x50, 0x24, 0x86, 0xa1, 0x05, 0x9a, 0x7c, 0x00, 0x4b, 0x87,
	0x9a, 0x1f, 0xe0, 0x10, 0x94, 0x0b, 0x31, 0xcc, 0xcd, 0x1e, 0xcf, 0xbb, 0x73, 0x0a, 0xfd, 0x16,
	0x57, 0x20, 0x67, 0x1a, 0xa1, 0x8d, 0x73, 0xa6, 0x81, 0x29, 0xc5, 0xd7, 0x1d, 0x8f, 0xc7, 0xa4,
	0xa0, 0xf0, 0x86, 0xfc, 0x47, 0x39, 0xb8, 0x19, 0xef, 0x4d, 0x83, 0x9d, 0xe3, 0x90, 0xfe, 0xa1,
	0xa3, 0x19, 0x47, 0x8e, 0xc7, 0xd0, 0x5f, 0x70, 0x52, 0x9f, 0xfb, 0x0b, 0xf7, 0x26, 0x5a, 0x86,
	0x4f, 0xfe, 0xf2, 0x19, 0xac, 0x58, 0x9a, 0x1f, 0xa8, 0x7c, 0xc5, 0x98, 0xa5, 0x71, 0xba, 0x25,
	0x65, 0xc9, 0x4a, 0x2e, 0xee, 0x36, 0x14, 0x49, 0xca, 0xd5, 0x3a, 0x7c, 0xf2, 0x65, 0xa5, 0x80,
	0x84, 0x63, 0xad, 0x43, 0x33, 0x10, 0x53, 0x77, 0xfa, 0x36, 0x4f, 0xe3, 0xcb, 0x0a, 0x89, 0x57,
	0x91, 0x20, 0x7e, 0x05, 0xeb, 0x67, 0x8e, 0x65, 0x39, 0xe7, 0xa6, 0xdd, 0xe1, 0xd3, 0xb0, 0x9e,
	0x1b, 0x0c, 0x24, 0xd8, 0x12, 0x1e, 0x17, 0x14, 0x31, 0xe6, 0xe1, 0x64, 0x35, 0xe4, 0x88, 0x5f,
	0xc3, 0x0d, 0x8f, 0xbd, 0xef, 0x33, 0x3f, 0x50, 0x3d, 0xc6, 0xcd, 0x6a, 0x50, 0x4f, 0xa9, 0x44,
	0x7d, 0xd6, 0x43, 0xae, 0x12, 0x31, 0xb1, 0xab, 0xfc, 0x2f, 0x45, 0x58, 0x1b, 0x73, 0x11, 0x34,
	0x06, 0x5a, 0x72, 0x18, 0x49, 0x0b, 0xd8, 0xac, 0x1b, 0x62, 0x03, 0xca, 0xd8, 0x1b, 0xb3, 0xa1,
	0xcf, 0xbc, 0xa1, 0xea, 0xd9, 0xbe, 0x57, 0x25, 0xd1, 0x13, 0x9f, 0x79, 0x27, 0x2f, 0xaa, 0xe4,
	0x7b, 0x2b, 0x7a, 0x4c, 0xa2, 0x89, 0x0e, 0xa1, 0x38, 0x1c, 0x88, 0xa7, 0x84, 0x2f, 0xb2, 0xfc,
	0xc6, 0xd1, 0x4d, 0xcd, 0xc2, 0xe1, 0xfa, 0xb6, 0x19, 0x0c, 0x12, 0x23, 0x1e, 0x5c, 0x53, 0x0a,
	0xfd, 0x68, 0x34, 0x07, 0xee, 0xf8, 0x24, 0x49, 0x6e, 0x4d, 0xa2, 0x2a, 0xfb, 0x18, 0xe0, 0x46,
	0xd0, 0x04, 0xbb, 0x34, 0xc1, 0xd3, 0x8c, 0x09, 0x50, 0xf3, 0x1a, 0x49, 0x8e, 0x4c, 0x75, 0x20,
	0x28, 0x37, 0xfd, 0x34, 0x89, 0xcb, 0xd1, 0x84, 0xeb, 0x30, 0x1f, 0x98, 0x81, 0xc5, 0x24, 0x83,
	0x1f, 0x9e, 0xd4, 0x10, 0x1b, 0xb0, 0x14, 0x26, 0x29, 0xd5, 0x32, 0xfd, 0x40, 0x62, 0x5b, 0xf9,
	0xc7, 0xa5, 0xdd, 0xcf, 0x2f, 0x93, 0x7b, 0xc3, 0xbc, 0xad, 0x94, 0xc2, 0x01, 0x0e, 0x4d, 0x3f,
	0x40, 0x3f, 0x22, 0x6b, 0x90, 0xa7, 0x9f, 0xf1, 0x03, 0x1f, 0x09, 0x6d, 0xf4, 0xf6, 0xc7, 0x50,
	0x26, 0x66, 0xdf, 0x35, 0x10, 0x12, 0x90, 0x4c, 0x87, 0x64, 0x56, 0x90, 0x7e, 0x42, 0x64, 0x92,
	0xe4, 0x49, 0x2e, 0x84, 0x20, 0xe8, 0x74, 0x5d, 0x72, 0xba, 0xa5, 0x90, 0xc8, 0xfd, 0xee, 0x01,
	0x20, 0x78, 0x31, 0x83, 0xbe, 0xc1, 0x42, 0x29, 0x93, 0xa4, 0x96, 0x23, 0x2a, 0x17, 0x43, 0x50,
	0x61, 0xb2, 0xf3, 0x50, 0xe4, 0x5b, 0xee, 0xbd, 0x48, 0xe1, 0xec, 0xbb, 0x50, 0xea, 0x0d, 0xd4,
	0xa8, 0x8b, 0xf4, 0x8e, 0xa7, 0xc5, 0xde, 0xa0, 0x12, 0x52, 0xb0, 0xbf, 0xe9, 0xab, 0x06, 0xc3,
	0xe4, 0x6a, 0x48, 0x16, 0x39, 0x68, 0xd1, 0xf4, 0xf7, 0x39, 0x81, 0xd2, 0x6a, 0x57, 0xf3, 0xa2,
	0x25, 0xf4, 0xc2, 0xb4, 0x8a, 0x24, 0x3e, 0xc1, 0x3d, 0x58, 0xea, 0x6a, 0x86, 0x7a, 0xa6, 0x7d,
	0x70, 0xfa, 0x1e, 0x33, 0x24, 0x9b, 0x46, 0x28, 0x75, 0x35, 0xe3, 0x79, 0x48, 0xc2, 0x5d, 0x33,
	0x7d, 0xd5, 0x0f, 0x4c, 0xfd, 0xdd, 0x40, 0x72, 0x88, 0x5f, 0x30, 0xfd, 0x16, 0xb5, 0x49, 0xcd,
	0x53, 0xdb, 0xf1, 0x7a, 0x9a, 0xa5, 0xfa, 0x81, 0x16, 0x30, 0xc9, 0x0d, 0xd5, 0x0c, 0xa9, 0x2d,
	0x24, 0xe2, 0x3a, 0x68, 0x73, 0x7d, 0xa7, 0xef, 0xe9, 0x4c, 0x7a, 0xcf, 0xd7, 0x81, 0xa4, 0x16,
	0x51, 0x44, 0x09, 0x16, 0x5d, 0xcf, 0xfc, 0xa0, 0xe9, 0x03, 0xc9, 0x23, 0x66, 0xd4, 0xa4, 0xae,
	0x5a, 0x87, 0x45, 0x5d, 0xfd, 0xb0, 0xab, 0xd6, 0x61, 0x61, 0xd7, 0x1d, 0xb8, 0x4e, 0x63, 0x87,
	0x1d, 0x54, 0xd7, 0xb1, 0x4c, 0x7d, 0x20, 0x05, 0x24, 0xb8, 0x86, 0xac, 0x63, 0xce, 0x39, 0x26,
	0x06, 0x62, 0x20, 0x0e, 0xbc, 0xa4, 0x3e, 0xc7, 0x40, 0xbc, 0x35, 0x5c, 0x63, 0xa0, 0x05, 0x7d,
	0x5f, 0xfa, 0x90, 0x58, 0x23, 0x51, 0xe2, 0x89, 0x7a, 0x9a, 0xde, 0x35, 0x6d, 0x16, 0x09, 0x9e,
	0x0f, 0x27, 0x3a, 0xe2, 0x9c, 0x50, 0xfe, 0x1e, 0x2c, 0x59, 0xda, 0x29, 0xb3, 0x54, 0x8f, 0xf5,
	0x4c, 0xdb, 0x90, 0xfe, 0x80, 0x63, 0x0e, 0xa2, 0x29, 0x44, 0xc2, 0x39, 0x4d, 0x57, 0xb5, 0x1c,
	0x5d, 0x0b, 0x4c, 0xc7, 0x96, 0xfe, 0x90, 0x24, 0xc0, 0x74, 0x0f, 0x43, 0x8a, 0xfc, 0x3b, 0x00,
	0xc7, 0xc3, 0x15, 0xdc, 0x86, 0x8d, 0xe3, 0x66, 0xab, 0xad, 0xb6, 0xda, 0x95, 0xf6, 0x49, 0x4b,
	0x3d, 0x69, 0xb4, 0x8e, 0x6b, 0xd5, 0xfa, 0xf3, 0x7a, 0x6d, 0xbf, 0x7c, 0x4d, 0xbc, 0x05, 0x37,
	0x92, 0xcc, 0xd6, 0x49, 0xeb, 0xb8, 0x5e, 0xad, 0x37, 0x4f, 0x5a, 0x65, 0x41, 0x5c, 0x87, 0x72,
	0x92, 0xf7, 0xbc, 0x52, 0x3f, 0x2c, 0xe7, 0xf6, 0x44, 0x28, 0x87, 0x51, 0x1d, 0xe7, 0x0e, 0xa4,
	0x91, 0x92, 0x89, 0x70, 0x97, 0x2d, 0x80, 0xaa, 0xe3, 0xd9, 0x61, 0x72, 0xe0, 0xc7, 0x82, 0x10,
	0x1f, 0x0b, 0x77, 0xa1, 0xa4, 0x13, 0x97, 0x1f, 0xdd, 0x1c, 0x58, 0x02, 0x27, 0xd1, 0xd1, 0x1d,
	0x41, 0xc8, 0xfc, 0x10, 0x42, 0x8a, 0x37, 0xa1, 0x10, 0x63, 0x41, 0x8e, 0x03, 0x16, 0x4d, 0x8e,
	0x03, 0xe5, 0xbf, 0x13, 0xe0, 0xe6, 0xc4, 0x34, 0x82, 0x46, 0x18, 0x4b, 0x4d, 0xf1, 0x72, 0xd6,
	0x46, 0x32, 0x4c, 0xdd, 0xc0, 0xc9, 0x13, 0x50, 0x85, 0x7e, 0xc7, 0x00, 0x24, 0x9f, 0x00, 0x20,
	0xdf, 0xc4, 0x5a, 0x50, 0xaa, 0x99, 0xa3, 0x54, 0xb3, 0x99, 0x99, 0x8b, 0xa3, 0x9d, 0x88, 0x94,
	0xc4, 0xdc, 0x22, 0xff, 0x75, 0x0e, 0xd6, 0xc6, 0xd2, 0x34, 0xa2, 0x96, 0x7e, 0xb8, 0xba, 0x65,
	0x05, 0x7f, 0xa2, 0xa3, 0x6b, 0x3a, 0x8f, 0x46, 0xbe, 0xa4, 0xa8, 0x89, 0x39, 0x50, 0xb3, 0x4c,
	0xcd, 0x0f, 0x97, 0xc5, 0x1b, 0xe2, 0x2d, 0x28, 0xd8, 0xa6, 0xfe, 0x8e, 0x74, 0xe0, 0x1b, 0x15,
	0xb7, 0xd1, 0x93, 0x3b, 0xcc, 0x36, 0x98, 0x17, 0xe2, 0xa5, 0xb0, 0x25, 0xde, 0x81, 0xa2, 0x6f,
	0x76, 0x6c, 0x2d, 0xe8, 0x7b, 0x2c, 0x2a, 0x54, 0x62, 0x02, 0x6e, 0xfd, 0x99, 0xa6, 0x33, 0xb5,
	0x67, 0x3c, 0x0b, 0x61, 0xd2, 0x22, 0xb6, 0x8f, 0x8c, 0x67, 0xd8, 0x31, 0x3e, 0x10, 0xa9, 0x48,
	0x29, 0x28, 0x43, 0x02, 0x26, 0x02, 0xde, 0x50, 0x7b, 0x8c, 0x2a, 0x94, 0x82, 0x52, 0xe0, 0x84,
	0x23, 0x5a, 0x8b, 0x63, 0x5b, 0xa6, 0xcd, 0xe8, 0x64, 0xc5, 0xa8, 0xa2, 0x16, 0x5a, 0xf3, 0xd6,
	0xe4, 0x53, 0x67, 0x66, 0x73, 0xde, 0x86, 0xa2, 0xe7, 0x58, 0x21, 0xb8, 0xe4, 0x1b, 0x58, 0x40,
	0x02, 0x01, 0xcb, 0x4d, 0x00, 0xa4, 0xab, 0xba, 0x63, 0x39, 0x9e, 0x94, 0xdf, 0xca, 0xa3, 0xe2,
	0x48, 0xa9, 0x22, 0x41, 0x7c, 0x02, 0xe5, 0x53, 0x4d, 0x7f, 0xd7, 0xf1, 0x9c, 0xbe, 0x6d, 0x84,
	0x42, 0x73, 0x24, 0xb4, 0x3a, 0xa4, 0x93, 0xa8, 0x5c, 0x83, 0xeb, 0x23, 0x8b, 0xfe, 0x2e, 0xab,
	0x95, 0x7f, 0x2c, 0xc0, 0xcd, 0x91, 0x71, 0xe8, 0x98, 0xf2, 0xbb, 0x4d, 0x37, 0x10, 0x1f, 0xc2,
	0x2a, 0x2d, 0xd7, 0x0f, 0x06, 0xa8, 0xd1, 0xb0, 0xec, 0x5d, 0x46, 0x72, 0x0b, 0xa9, 0x14, 0x3f,
	0x98, 0x98, 0x3c, 0x76, 0x66, 0x7e, 0x4c, 0x6a, 0x0d, 0x9c, 0x94, 0x02, 0xd4, 0xf9, 0x11, 0x40,
	0xdd, 0x3f, 0x8b, 0x3b, 0x71, 0xd7, 0x01, 0x4e, 0xc2, 0x4e, 0xf2, 0x9f, 0x0b, 0xb0, 0x39, 0xac,
	0x10, 0x5c, 0xcb, 0xd1, 0x8c, 0x61, 0xbd, 0x41, 0xda, 0x5e, 0xba, 0x2c, 0xe7, 0x67, 0x5d, 0x24,
	0x88, 0x65, 0x33, 0x5f, 0xe3, 0xf2, 0x90, 0xfa, 0x8a, 0x17, 0xd0, 0x53, 0xca, 0x71, 0xf9, 0xa7,
	0x02, 0xac, 0x34, 0xd8, 0x79, 0x74, 0xb4, 0x1d, 0xf9, 0x1d, 0x74, 0xfe, 0xf8, 0xec, 0xe3, 0x53,
	0xc7, 0x6d, 0xb1, 0x16, 0xa6, 0x6b, 0xe7, 0xf4, 0x5b, 0xa6, 0x07, 0x53, 0xc0, 0xd3, 0x38, 0x70,
	0xa7, 0xa4, 0xde, 0xa4, 0x7e, 0x62, 0x03, 0x56, 0xa2, 0xc3, 0x3c, 0x1c, 0x89, 0xa3, 0xa7, 0x47,
	0xd3, 0x46, 0x0a, 0xb1, 0x3b, 0x0d, 0x16, 0x61, 0x01, 0x3e, 0x9e, 0xfc, 0x9f, 0x02, 0x2c, 0x37,
	0xf0, 0xf8, 0x26, 0x22, 0x2a, 0xf1, 0x12, 0x22, 0x64, 0xc0, 0x15, 0x17, 0x66, 0x1b, 0xbf, 0xa4,
	0x0f, 0x1b, 0xff, 0x5f, 0x95, 0xfe, 0x5b, 0x01, 0x60, 0x58, 0xde, 0x88, 0x07, 0x50, 0xea, 0x3a,
	0x41, 0x5c, 0x12, 0xcd, 0xa8, 0x30, 0x74, 0x9d, 0x20, 0x1a, 0x49, 0x81, 0xb2, 0xc7, 0x5c, 0x6b,
	0x80, 0xe0, 0x3d, 0x5d, 0x61, 0x5d, 0x7a, 0xb8, 0xd5, 0x68, 0x80, 0xa8, 0xe0, 0xfa, 0xc9, 0x02,
	0xac, 0x67, 0x49, 0x4e, 0x06, 0xeb, 0x8f, 0x60, 0x55, 0x77, 0xec, 0x0f, 0xcc, 0xf3, 0xe9, 0x50,
	0x56, 0xe3, 0xaa, 0x68, 0x25, 0x49, 0xe6, 0xd5, 0x71, 0x6c, 0x6a, 0x23, 0xf2, 0xf0, 0xc8, 0x7e,
	0x58, 0xdd, 0x15, 0xcf, 0x3c, 0xa7, 0x47, 0xa7, 0x2d, 0x45, 0xe4, 0x65, 0xd1, 0x7e, 0x01, 0xbb,
	0x21, 0x01, 0x8f, 0x8f, 0xe8, 0x06, 0x8a, 0x5f, 0x61, 0x44, 0x4d, 0xdc, 0xf4, 0x61, 0xb8, 0xf9,
	0xd2, 0x02, 0x1d, 0x60, 0x97, 0xbc, 0x5e, 0x50, 0x92, 0x5d, 0x47, 0x41, 0xe7, 0xe2, 0x18, 0xe8,
	0xfc, 0x0d, 0x58, 0x0c, 0x1c, 0xae, 0x45, 0x61, 0x06, 0x2d, 0x16, 0x02, 0x87, 0x74, 0x78, 0x06,
	0x1b, 0x81, 0x13, 0x20, 0x60, 0xec, 0x9f, 0xaa, 0x69, 0x24, 0x5d, 0xa4, 0xb9, 0xd6, 0x89, 0xdd,
	0xea, 0x9f, 0x56, 0x93, 0x88, 0xfa, 0x25, 0x2c, 0x25, 0x3a, 0xf8, 0x12, 0x90, 0x86, 0x97, 0x8f,
	0x23, 0x3f, 0x1e, 0xcf, 0xcf, 0x40, 0xe7, 0xa5, 0x2c, 0x74, 0x9e, 0xba, 0x21, 0x5c, 0x1a, 0xb9,
	0x21, 0x4c, 0x43, 0xef, 0xe5, 0x51, 0xe8, 0xbd, 0x01, 0x8b, 0xbd, 0x81, 0xea, 0x07, 0xcc, 0x95,
	0x56, 0xf9, 0x89, 0xd8, 0x1b, 0xb4, 0x02, 0xe6, 0xf2, 0xfb, 0x15, 0xe6, 0xf2, 0x84, 0x5a, 0xe6,
	0x59, 0x0d, 0x09, 0x94, 0x4a, 0x37, 0x01, 0x88, 0xc9, 0x17, 0xb5, 0xc6, 0xeb, 0x01, 0xa4, 0xc4,
	0xf5, 0x40, 0x12, 0x2f, 0x8a, 0xa3, 0x78, 0x31, 0x5d, 0x07, 0xee, 0x7e, 0xcf, 0x3a, 0x30, 0x0b,
	0x20, 0xca, 0xff, 0x9e, 0x87, 0x75, 0x4c, 0x2a, 0xa9, 0x0c, 0xa3, 0xb0, 0xf7, 0xe2, 0x43, 0x28,
	0x9c, 0x6a, 0x3e, 0x53, 0x3d, 0xf6, 0x3e, 0x0c, 0xf9, 0x12, 0xce, 0xbc, 0xa7, 0xf9, 0x4c, 0x61,
	0xef, 0x95, 0xc5, 0x53, 0xfe, 0x03, 0xf1, 0x07, 0xbf, 0xe3, 0x88, 0xc3, 0x68, 0x91, 0xda, 0x75,
	0x43, 0x7c, 0x09, 0x6b, 0x9c, 0x15, 0xde, 0x05, 0x11, 0x14, 0xcb, 0x93, 0x9d, 0x3f, 0x9d, 0x74,
	0x0b, 0xc2, 0x6f, 0xad, 0x94, 0x55, 0x7f, 0xd8, 0xa0, 0x62, 0xaf, 0x01, 0xd7, 0xc3, 0x9b, 0xd7,
	0xe4, 0x90, 0x61, 0xd8, 0x5d, 0x34, 0xda, 0x1a, 0xef, 0x9a, 0xbc, 0x16, 0xfb, 0x5d, 0xf8, 0x64,
	0xfc, 0xec, 0xc7, 0x5d, 0x36, 0x26, 0x5e, 0xe4, 0x65, 0x40, 0x88, 0x83, 0x6b, 0xca, 0x75, 0x3f,
	0x03, 0x59, 0xc4, 0x05, 0xf0, 0xfc, 0xb4, 0x02, 0x78, 0xe1, 0x7b, 0x16, 0xc0, 0xc3, 0xd2, 0x67,
	0x31, 0x59, 0xfa, 0xec, 0xad, 0xc0, 0x12, 0xdf, 0x24, 0x83, 0x05, 0x9a, 0x69, 0xc9, 0xff, 0x2a,
	0xc0, 0x27, 0x19, 0x36, 0xf6, 0x5d, 0xf1, 0x09, 0x14, 0x43, 0x23, 0xfb, 0x6e, 0x68, 0xe5, 0xa5,
	0xa1, 0x95, 0x7d, 0x57, 0x29, 0x9c, 0x86, 0xbf, 0x92, 0xe9, 0x34, 0x97, 0x4a, 0xa7, 0xe8, 0xc4,
	0x3d, 0x2c, 0xe9, 0x02, 0xe7, 0x1d, 0xb3, 0xc3, 0x34, 0x09, 0x44, 0x6a, 0x23, 0x05, 0x05, 0xf8,
	0x6d, 0x39, 0x17, 0x08, 0xb1, 0x0b, 0x91, 0xb8, 0x00, 0x86, 0x1e, 0x8d, 0xf0, 0x8e, 0x0d, 0x7c,
	0x69, 0x9e, 0x03, 0x3d, 0xa2, 0xbc, 0x62, 0x03, 0x9f, 0x17, 0xd5, 0xd8, 0x9f, 0xd8, 0x0b, 0x9c,
	0x4d, 0x14, 0x64, 0xcb, 0xff, 0x2d, 0xc0, 0xd6, 0x0b, 0x96, 0x56, 0x2e, 0x84, 0x65, 0xbc, 0xe6,
	0x98, 0xc5, 0x9b, 0x5f, 0xc2, 0x9a, 0xde, 0xf7, 0xbc, 0xb4, 0x93, 0xe5, 0x2e, 0xe5, 0x64, 0xab,
	0xd8, 0x31, 0xe9, 0x62, 0x2d, 0x28, 0x8f, 0xba, 0x98, 0xb4, 0x3e, 0xa3, 0x77, 0xad, 0x8e, 0x78,
	0xd7, 0xde, 0x2a, 0x2c, 0x53, 0xf0, 0x3a, 0x2e, 0xc7, 0x1f, 0xf2, 0x3f, 0xe7, 0xe1, 0xde, 0x05,
	0xea, 0xcf, 0x66, 0xe8, 0x09, 0x91, 0x96, 0xfb, 0xae, 0x91, 0x76, 0x95, 0x59, 0xe0, 0x6b, 0xb8,
	0xd1, 0xd5, 0x7c, 0x2e, 0xea, 0x77, 0x55, 0x97, 0x79, 0x3d, 0xd3, 0xf7, 0xcd, 0xf0, 0xa9, 0x60,
	0x59, 0x59, 0xef, 0x6a, 0x7e, 0xa8, 0xfc, 0x71, 0xcc, 0x13, 0x7f, 0x1f, 0xd6, 0xc7, 0x62, 0xdd,
	0x71, 0x83, 0xd0, 0x18, 0x4f, 0x2f, 0x36, 0xc6, 0x10, 0xe5, 0x1f, 0x5c, 0x53, 0xc4, 0x11, 0x93,
	0x20, 0xf6, 0xff, 0x11, 0xc0, 0x79, 0xd7, 0x0c, 0x18, 0x57, 0x6e, 0x9e, 0x94, 0xcb, 0x7a, 0xee,
	0x79, 0x83, 0x42, 0xa8, 0x49, 0xcb, 0x0c, 0x98, 0xaf, 0x14, 0xcf, 0xa3, 0x36, 0x15, 0xea, 0xa1,
	0x56, 0xb1, 0x69, 0xff, 0x4c, 0x80, 0x95, 0x74, 0x8f, 0x09, 0xaf, 0x2a, 0xc2, 0x4c, 0xaf, 0x2a,
	0xb9, 0x09, 0xaf, 0x2a, 0x9b, 0x00, 0x1e, 0xd3, 0xac, 0xd4, 0x4b, 0x4d, 0x91, 0x28, 0x38, 0x98,
	0xfc, 0x1f, 0x02, 0x3c, 0x3c, 0xd2, 0xbc, 0x77, 0x29, 0x4f, 0x1b, 0x82, 0x0e, 0x5e, 0x73, 0xb0,
	0x99, 0xa2, 0x6d, 0x62, 0x4e, 0x79, 0x9b, 0xaa, 0x55, 0x12, 0x1e, 0xf3, 0xd5, 0xd4, 0x87, 0x91,
	0x8c, 0xb2, 0x27, 0x59, 0xdd, 0x90, 0x0b, 0xa5, 0x41, 0xdd, 0xdc, 0x08, 0xa8, 0x93, 0xdb, 0xf0,
	0xe8, 0x52, 0x4a, 0xce, 0x14, 0x53, 0xf2, 0xdf, 0xe4, 0x40, 0x4a, 0x06, 0x69, 0x83, 0x9d, 0xfb,
	0x74, 0xb3, 0x7e, 0x65, 0x27, 0x6d, 0x39, 0x15, 0x63, 0x38, 0xd4, 0xee, 0x65, 0x02, 0xf6, 0xe0,
	0x9a, 0xb2, 0x92, 0x08, 0x32, 0x9c, 0xa6, 0x0e, 0x45, 0x54, 0x52, 0xed, 0x45, 0x6f, 0x03, 0xd9,
	0x21, 0x32, 0xf1, 0x91, 0x40, 0x29, 0x58, 0xd1, 0x73, 0xc1, 0x3a, 0xcc, 0x27, 0xef, 0xf1, 0x79,
	0x03, 0x8f, 0xa7, 0xe8, 0x46, 0x1e, 0x41, 0x13, 0x1e, 0x4f, 0x37, 0x27, 0x6c, 0xce, 0x6c, 0x99,
	0x6b, 0x17, 0xe6, 0xe9, 0x2d, 0x42, 0xca, 0x91, 0xaf, 0xdc, 0x99, 0xf6, 0xd2, 0xa2, 0x70, 0xd1,
	0x2b, 0xd6, 0xb6, 0xe3, 0x39, 0x7d, 0x37, 0x74, 0x2a, 0xde, 0x90, 0xff, 0x2a, 0x07, 0xd7, 0x47,
	0xf3, 0xf3, 0x95, 0xc4, 0xc8, 0x84, 0x3c, 0x9d, 0xbf, 0x72, 0x44, 0xf4, 0xe9, 0x15, 0x20, 0xa2,
	0x31, 0x4c, 0xf2, 0xf7, 0x02, 0xac, 0x8f, 0x6f, 0xcb, 0x6c, 0xf6, 0xae, 0x84, 0x0f, 0x00, 0x17,
	0x3c, 0xb7, 0x8c, 0x17, 0xcf, 0xf4, 0x4c, 0x40, 0x40, 0x6d, 0xf2, 0x81, 0x92, 0x9f, 0x7c, 0xa0,
	0xc8, 0x3f, 0xcb, 0xc3, 0x46, 0x0a, 0x50, 0x45, 0x2f, 0x81, 0x33, 0xd8, 0xf5, 0xaa, 0x8f, 0xd9,
	0x84, 0x9f, 0xe4, 0x53, 0x7e, 0x32, 0x3d, 0xe1, 0x65, 0x55, 0xc3, 0xf3, 0x99, 0xd5, 0x70, 0xa2,
	0x56, 0x5d, 0x98, 0x5a, 0xab, 0x2e, 0x7e, 0xf7, 0x5a, 0xf5, 0xff, 0xd6, 0x07, 0xff, 0x38, 0x07,
	0x52, 0xb6, 0x19, 0xaf, 0x08, 0x1a, 0x5f, 0x70, 0x81, 0xf0, 0x1c, 0xa0, 0x37, 0x88, 0x2f, 0x42,
	0xe6, 0x66, 0xbb, 0x08, 0x29, 0xf6, 0x06, 0xd1, 0xb5, 0xca, 0x08, 0x02, 0x9f, 0x1f, 0x43, 0xe0,
	0x69, 0x80, 0xbd, 0x30, 0x02, 0xb0, 0xe5, 0xbf, 0xc8, 0xc1, 0xad, 0x64, 0x2c, 0x86, 0xe3, 0xe2,
	0x71, 0x79, 0x25, 0x99, 0xea, 0x82, 0x6d, 0xb8, 0x9d, 0x4c, 0xc1, 0xe1, 0xa5, 0xf8, 0xf8, 0x11,
	0x32, 0x9f, 0x38, 0x42, 0x30, 0x6c, 0xdf, 0xf7, 0x99, 0x37, 0x50, 0xc7, 0xae, 0x93, 0x16, 0xf8,
	0xa3, 0x2e, 0x71, 0x95, 0xf4, 0x55, 0x91, 0xf8, 0x98, 0xea, 0x5f, 0x4f, 0x53, 0x13, 0xab, 0xe1,
	0x57, 0xe6, 0x2b, 0x44, 0xaf, 0xc6, 0x28, 0xe0, 0xe7, 0x39, 0xb8, 0x3d, 0x71, 0x47, 0xae, 0xc8,
	0x39, 0xaa, 0x50, 0x88, 0x2f, 0x3f, 0xf2, 0xb3, 0x5d, 0x7e, 0xc4, 0x1d, 0xa7, 0xef, 0xdd, 0xc8,
	0xb7, 0x0c, 0xf3, 0x13, 0x1f, 0x41, 0xb2, 0xbf, 0x65, 0xc8, 0xbc, 0xae, 0x5b, 0xf8, 0x9e, 0xd7,
	0x75, 0xbf, 0xc8, 0xc1, 0xed, 0xc4, 0xb7, 0x4d, 0xae, 0xc7, 0x7c, 0x3f, 0xba, 0x89, 0xfa, 0x65,
	0x4c, 0x9f, 0xc9, 0x3b, 0x6d, 0x74, 0xd1, 0xf9, 0xc4, 0x9d, 0xf6, 0x06, 0x2c, 0xd2, 0xed, 0x8f,
	0x63, 0xd3, 0xb6, 0xcd, 0x2b, 0x0b, 0xd8, 0x6c, 0xda, 0x93, 0xd3, 0xdc, 0xe2, 0xff, 0x46, 0x9a,
	0xfb, 0x07, 0x01, 0xee, 0x4c, 0xde, 0xf2, 0xd9, 0xbc, 0x79, 0xfc, 0xa6, 0x2d, 0x47, 0x9a, 0x8d,
	0xdc, 0xb4, 0x7d, 0xa7, 0x63, 0x75, 0xe4, 0xb6, 0x6c, 0x8e, 0x06, 0x1e, 0xde, 0x96, 0xc9, 0x7b,
	0x89, 0x0f, 0xba, 0xa2, 0x80, 0x8c, 0x5c, 0xfd, 0x21, 0xac, 0xda, 0xfc, 0xad, 0x28, 0x36, 0x15,
	0x2f, 0x8a, 0x96, 0x6d, 0x7a, 0x30, 0x8a, 0x02, 0x7b, 0x00, 0x1b, 0x08, 0xef, 0x4f, 0x7c, 0xe6,
	0x45, 0x88, 0x57, 0x77, 0xbc, 0x99, 0x8a, 0x96, 0xd4, 0x07, 0x55, 0xb9, 0x91, 0x0f, 0xaa, 0x92,
	0x18, 0x3d, 0x9f, 0xc2, 0xe8, 0x72, 0x0d, 0xa4, 0xec, 0xa9, 0x67, 0xb2, 0xc0, 0xf6, 0xbf, 0x09,
	0xb0, 0x91, 0x71, 0x8e, 0xd2, 0x75, 0xe3, 0x36, 0x3c, 0x6c, 0x34, 0x1b, 0xea, 0xf1, 0xc9, 0xde,
	0x61, 0xbd, 0xaa, 0x56, 0xda, 0xed, 0x4a, 0xf5, 0xe0, 0xa8, 0xd6, 0x68, 0xab, 0xed, 0xb7, 0xc7,
	0xb5, 0x91, 0xf7, 0xe5, 0xcf, 0x60, 0x6b, 0x8a, 0x6c, 0xfd, 0xa8, 0xf2, 0xa2, 0x56, 0x16, 0x44,
	0x19, 0x3e, 0x9d, 0x22, 0xf5, 0xa2, 0xfe, 0xbc, 0x9c, 0xbb, 0x60, 0xa4, 0xd7, 0xf5, 0xfd, 0x5a,
	0xb3, 0x9c, 0x17, 0x1f, 0xc1, 0xfd, 0x69, 0x6b, 0x53, 0x0e, 0xd5, 0x6a, 0x45, 0xd9, 0x2f, 0xcf,
	0x6d, 0xff, 0x38, 0xa5, 0x60, 0xe2, 0xbb, 0x83, 0xbe, 0x3f, 0xaa, 0xe0, 0x5e, 0xa3, 0xa9, 0x1c,
	0x55, 0x0e, 0xb3, 0x1f, 0xd0, 0x1f, 0xc0, 0xbd, 0x29, 0xb2, 0x7b, 0x95, 0x46, 0xa3, 0xb6, 0x5f,
	0x16, 0xc4, 0xcf, 0xe1, 0xd1, 0xd4, 0x21, 0xf7, 0x6b, 0x8a, 0xaa, 0xd4, 0x5e, 0xd7, 0x6b, 0x6f,
	0xca, 0xb9, 0xed, 0x9f, 0x0a, 0x20, 0x67, 0x6c, 0xfe, 0xbe, 0x73, 0x6e, 0x63, 0xd6, 0x0d, 0xbf,
	0x4c, 0x10, 0x7f, 0x0d, 0x76, 0xb3, 0x75, 0xdd, 0x6f, 0xbe, 0x69, 0x1c, 0x36, 0x2b, 0xfb, 0xea,
	0xb1, 0x52, 0x7f, 0x5d, 0xa9, 0xbe, 0x1d, 0x59, 0xf2, 0x2e, 0xec, 0x5c, 0xb2, 0x1f, 0xfd, 0x6d,
	0xa3, 0x85, 0x7e, 0x05, 0xbe, 0xb8, 0x6c, 0x1f, 0x12, 0x29, 0xe7, 0xb6, 0x7f, 0x26, 0xc0, 0x6a,
	0x22, 0xf7, 0x91, 0xeb, 0xdc, 0x83, 0xcd, 0x56, 0xb5, 0xd6, 0xa8, 0xa9, 0xad, 0xb6, 0x52, 0xab,
	0x1c, 0x65, 0x79, 0xcc, 0x97, 0xf0, 0xf9, 0xb8, 0x48, 0xab, 0x59, 0xad, 0x57, 0x0e, 0xd5, 0x6a,
	0xf3, 0xe8, 0xe8, 0xa4, 0x51, 0x6f, 0xbf, 0x55, 0xab, 0x95, 0x76, 0xed, 0x45, 0x53, 0x79, 0xcb,
	0xb7, 0xf6, 0x12, 0x1d, 0xda, 0x95, 0xc3, 0x57, 0xe5, 0x9c, 0xf8, 0x15, 0x3c, 0xbd, 0x84, 0xf0,
	0xab, 0x46, 0xf3, 0xcd, 0x61, 0x6d, 0xff, 0x45, 0xad, 0x9c, 0xdf, 0xfe, 0x08, 0xb7, 0xd2, 0xb7,
	0x5e, 0xa9, 0xef, 0x42, 0x76, 0x60, 0x3b, 0xb1, 0x2f, 0xf4, 0xb9, 0x44, 0xbc, 0x11, 0xcd, 0xc3,
	0xfa, 0xd8, 0xde, 0xa7, 0x5d, 0x2b, 0x4b, 0x3e, 0xde, 0xf3, 0xed, 0x7f, 0x14, 0x46, 0xa6, 0x4e,
	0x7f, 0x29, 0x92, 0x31, 0xf5, 0x51, 0xa5, 0x7a, 0x50, 0x27, 0xe5, 0x32, 0x3c, 0xf5, 0x0b, 0x78,
	0x72, 0x81, 0x7c, 0xea, 0xeb, 0x8f, 0x27, 0xf0, 0xe0, 0x02, 0x71, 0xee, 0xc3, 0xe5, 0xdc, 0x25,
	0x44, 0x95, 0xda, 0xcb, 0x5a, 0xb5, 0x5d, 0xce, 0x6f, 0xff, 0x24, 0xf9, 0xbd, 0x6c, 0xdd, 0x0e,
	0x98, 0xa7, 0xe9, 0x81, 0xf9, 0x81, 0xbf, 0x59, 0xa7, 0x83, 0xa4, 0xde, 0x68, 0xd7, 0x94, 0x4a,
	0xb5, 0x5d, 0x7f, 0x5d, 0xcb, 0xf2, 0x93, 0x1f, 0xc0, 0xd7, 0xd3, 0x84, 0xc7, 0x3d, 0xa6, 0x79,
	0x84, 0x0e, 0x5b, 0x16, 0xc4, 0x1f, 0xc2, 0xb3, 0x99, 0x7a, 0x56, 0xda, 0xed, 0x7a, 0xfb, 0x64,
	0xbf, 0x56, 0xce, 0x6d, 0x7f, 0x9b, 0xf8, 0x16, 0x1d, 0x2d, 0xd2, 0xe4, 0x5f, 0x01, 0x3d, 0x04,
	0x79, 0x74, 0x03, 0x9a, 0x4a, 0xfd, 0x45, 0xbd, 0x31, 0xb2, 0xe6, 0xb4, 0x82, 0x49, 0xb9, 0xd1,
	0x49, 0xcb, 0xc2, 0xb6, 0x0b, 0x2b, 0x89, 0xf0, 0x69, 0xba, 0x81, 0xb8, 0x05, 0x77, 0x52, 0xce,
	0xdb, 0x3c, 0x6e, 0x8f, 0x4c, 0xb0, 0x09, 0x37, 0xc7, 0x24, 0x94, 0xda, 0x6f, 0x9e, 0xd4, 0x15,
	0xca, 0x42, 0x59, 0xec, 0xe6, 0x71, 0xbb, 0xde, 0x6c, 0xa0, 0x1d, 0xb7, 0xab, 0xd1, 0x27, 0x3b,
	0x64, 0x8d, 0xdb, 0xb0, 0x51, 0x6d, 0x2a, 0x8d, 0x9a, 0x92, 0xb5, 0xfb, 0x12, 0xac, 0x27, 0x99,
	0xc3, 0x70, 0xdc, 0xfe, 0x13, 0x01, 0xd6, 0xc3, 0x43, 0xb7, 0x9d, 0xfa, 0x22, 0xe1, 0x11, 0xdc,
	0x27, 0xc5, 0x5b, 0x07, 0x6a, 0xbb, 0xf6, 0x5b, 0x6d, 0xb5, 0xd5, 0x7e, 0x7b, 0x98, 0x69, 0xd9,
	0x2d, 0xb8, 0x33, 0x49, 0x50, 0x69, 0x1e, 0x62, 0x36, 0xba, 0x0f, 0x77, 0x27, 0x49, 0x54, 0x0f,
	0x30, 0xe5, 0xa2, 0x36, 0x7f, 0x29, 0xc0, 0x27, 0xc9, 0x2c, 0x4a, 0xc0, 0x81, 0x56, 0xf2, 0x18,
	0x3e, 0x4b, 0x27, 0x33, 0x32, 0x6f, 0xd6, 0x52, 0xee, 0xc1, 0xe6, 0x44, 0xc9, 0xc3, 0xfa, 0x2b,
	0x5c, 0xcb, 0xd8, 0x69, 0x98, 0x10, 0x89, 0x5b, 0x8d, 0x66, 0x03, 0xdd, 0xc7, 0x81, 0xd5, 0x78,
	0x45, 0x2d, 0x0e, 0xd2, 0xd2, 0x47, 0x5f, 0xab, 0x5d, 0x3b, 0x56, 0x9b, 0xa3, 0x6e, 0x73, 0x1b,
	0x36, 0x32, 0x64, 0xf0, 0x6f, 0x59, 0x98, 0xc0, 0x0c, 0x27, 0xfc, 0xbd, 0xc4, 0xf7, 0xa5, 0xad,
	0xe8, 0xb9, 0x30, 0x7d, 0x92, 0x52, 0x8f, 0x0c, 0xd5, 0xef, 0xc3, 0xdd, 0x4c, 0xa9, 0xa3, 0x93,
	0x56, 0xd8, 0x2e, 0x0b, 0x04, 0x13, 0xc2, 0xff, 0x65, 0xe0, 0x6e, 0x9a, 0xc0, 0x59, 0x4f, 0xe0,
	0x41, 0xb5, 0xd9, 0x68, 0xe3, 0x21, 0x11, 0xba, 0xdb, 0x71, 0x4d, 0x39, 0xaa, 0xb7, 0x5a, 0xf5,
	0x31, 0x05, 0x65, 0xf8, 0x74, 0xb2, 0xa8, 0x52, 0xab, 0xa0, 0xef, 0x3e, 0x80, 0x7b, 0x93, 0x65,
	0x42, 0x6f, 0x28, 0xe7, 0xa6, 0x8b, 0x45, 0x39, 0x60, 0x0e, 0x23, 0x76, 0xb2, 0x58, 0x1c, 0xf0,
	0x85, 0xbd, 0x17, 0x20, 0xe9, 0x4e, 0x6f, 0x67, 0x60, 0x0e, 0x9c, 0x3e, 0x42, 0xa5, 0x9e, 0x63,
	0x30, 0x8b, 0xff, 0x67, 0xca, 0x6f, 0x7f, 0xde, 0x71, 0x2c, 0xcd, 0xee, 0xec, 0x3c, 0xdb, 0x0d,
	0x82, 0x1d, 0xdd, 0xe9, 0x7d, 0x49, 0x64, 0xdd, 0xb1, 0xbe, 0xd4, 0x5c, 0x77, 0xe4, 0xdf, 0x5d,
	0x4e, 0x17, 0x88, 0xf9, 0xab, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff, 0xdd, 0xa1, 0xb0, 0x52, 0x16,
	0x33, 0x00, 0x00,
}
