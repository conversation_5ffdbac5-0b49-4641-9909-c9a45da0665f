// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel_recommend_logic/channel_recommend_logic_.proto

package channel_recommend_logic // import "golang.52tt.com/protocol/app/channel-recommend-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RevenueSwitchHubType int32

const (
	RevenueSwitchHubType_REVENUE_SWITCH_HUB_TYPE_UNSPECIFIED RevenueSwitchHubType = 0
	RevenueSwitchHubType_REVENUE_SWITCH_HUB_TYPE_SNACKBAR    RevenueSwitchHubType = 1
)

var RevenueSwitchHubType_name = map[int32]string{
	0: "REVENUE_SWITCH_HUB_TYPE_UNSPECIFIED",
	1: "REVENUE_SWITCH_HUB_TYPE_SNACKBAR",
}
var RevenueSwitchHubType_value = map[string]int32{
	"REVENUE_SWITCH_HUB_TYPE_UNSPECIFIED": 0,
	"REVENUE_SWITCH_HUB_TYPE_SNACKBAR":    1,
}

func (x RevenueSwitchHubType) String() string {
	return proto.EnumName(RevenueSwitchHubType_name, int32(x))
}
func (RevenueSwitchHubType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{0}
}

// 顶部浮窗类型
type ETopOverLayType int32

const (
	ETopOverLayType_E_TOP_OVER_LAY_TYPE_UNSPECIFIED ETopOverLayType = 0
	ETopOverLayType_E_TOP_OVER_LAY_TYPE_CHANNEL     ETopOverLayType = 1
	ETopOverLayType_E_TOP_OVER_LAY_TYPE_USER        ETopOverLayType = 2
	ETopOverLayType_E_TOP_OVER_LAY_TYPE_ESPORT_GOD  ETopOverLayType = 3
)

var ETopOverLayType_name = map[int32]string{
	0: "E_TOP_OVER_LAY_TYPE_UNSPECIFIED",
	1: "E_TOP_OVER_LAY_TYPE_CHANNEL",
	2: "E_TOP_OVER_LAY_TYPE_USER",
	3: "E_TOP_OVER_LAY_TYPE_ESPORT_GOD",
}
var ETopOverLayType_value = map[string]int32{
	"E_TOP_OVER_LAY_TYPE_UNSPECIFIED": 0,
	"E_TOP_OVER_LAY_TYPE_CHANNEL":     1,
	"E_TOP_OVER_LAY_TYPE_USER":        2,
	"E_TOP_OVER_LAY_TYPE_ESPORT_GOD":  3,
}

func (x ETopOverLayType) String() string {
	return proto.EnumName(ETopOverLayType_name, int32(x))
}
func (ETopOverLayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{1}
}

// 浮窗类型
type ChannelTopOverLay_EOverLayType int32

const (
	ChannelTopOverLay_E_OVER_LAY_TYPE_UNSPECIFIED ChannelTopOverLay_EOverLayType = 0
	ChannelTopOverLay_E_OVER_LAY_TYPE_CHANNEL     ChannelTopOverLay_EOverLayType = 1
	ChannelTopOverLay_E_OVER_LAY_TYPE_USER        ChannelTopOverLay_EOverLayType = 2
)

var ChannelTopOverLay_EOverLayType_name = map[int32]string{
	0: "E_OVER_LAY_TYPE_UNSPECIFIED",
	1: "E_OVER_LAY_TYPE_CHANNEL",
	2: "E_OVER_LAY_TYPE_USER",
}
var ChannelTopOverLay_EOverLayType_value = map[string]int32{
	"E_OVER_LAY_TYPE_UNSPECIFIED": 0,
	"E_OVER_LAY_TYPE_CHANNEL":     1,
	"E_OVER_LAY_TYPE_USER":        2,
}

func (x ChannelTopOverLay_EOverLayType) String() string {
	return proto.EnumName(ChannelTopOverLay_EOverLayType_name, int32(x))
}
func (ChannelTopOverLay_EOverLayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{5, 0}
}

// 推荐抽奖房间信息
type RecLotteryChInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconMd5              string   `protobuf:"bytes,3,opt,name=icon_md5,json=iconMd5,proto3" json:"icon_md5,omitempty"`
	LotteryEndTs         uint32   `protobuf:"varint,4,opt,name=lottery_end_ts,json=lotteryEndTs,proto3" json:"lottery_end_ts,omitempty"`
	GiftName             string   `protobuf:"bytes,5,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftIcon             string   `protobuf:"bytes,6,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	GiftCnt              uint32   `protobuf:"varint,7,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt,omitempty"`
	GiftValueText        string   `protobuf:"bytes,8,opt,name=gift_value_text,json=giftValueText,proto3" json:"gift_value_text,omitempty"`
	ChannelType          uint32   `protobuf:"varint,9,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	BindId               uint32   `protobuf:"varint,10,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecLotteryChInfo) Reset()         { *m = RecLotteryChInfo{} }
func (m *RecLotteryChInfo) String() string { return proto.CompactTextString(m) }
func (*RecLotteryChInfo) ProtoMessage()    {}
func (*RecLotteryChInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{0}
}
func (m *RecLotteryChInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecLotteryChInfo.Unmarshal(m, b)
}
func (m *RecLotteryChInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecLotteryChInfo.Marshal(b, m, deterministic)
}
func (dst *RecLotteryChInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecLotteryChInfo.Merge(dst, src)
}
func (m *RecLotteryChInfo) XXX_Size() int {
	return xxx_messageInfo_RecLotteryChInfo.Size(m)
}
func (m *RecLotteryChInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecLotteryChInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecLotteryChInfo proto.InternalMessageInfo

func (m *RecLotteryChInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RecLotteryChInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RecLotteryChInfo) GetIconMd5() string {
	if m != nil {
		return m.IconMd5
	}
	return ""
}

func (m *RecLotteryChInfo) GetLotteryEndTs() uint32 {
	if m != nil {
		return m.LotteryEndTs
	}
	return 0
}

func (m *RecLotteryChInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *RecLotteryChInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *RecLotteryChInfo) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

func (m *RecLotteryChInfo) GetGiftValueText() string {
	if m != nil {
		return m.GiftValueText
	}
	return ""
}

func (m *RecLotteryChInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *RecLotteryChInfo) GetBindId() uint32 {
	if m != nil {
		return m.BindId
	}
	return 0
}

// 获取抽奖房间推荐列表
type GetRecLotteryChListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Page                 uint32       `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32       `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRecLotteryChListRequest) Reset()         { *m = GetRecLotteryChListRequest{} }
func (m *GetRecLotteryChListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecLotteryChListRequest) ProtoMessage()    {}
func (*GetRecLotteryChListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{1}
}
func (m *GetRecLotteryChListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecLotteryChListRequest.Unmarshal(m, b)
}
func (m *GetRecLotteryChListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecLotteryChListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecLotteryChListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecLotteryChListRequest.Merge(dst, src)
}
func (m *GetRecLotteryChListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecLotteryChListRequest.Size(m)
}
func (m *GetRecLotteryChListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecLotteryChListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecLotteryChListRequest proto.InternalMessageInfo

func (m *GetRecLotteryChListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRecLotteryChListRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetRecLotteryChListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetRecLotteryChListResponse struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChList               []*RecLotteryChInfo `protobuf:"bytes,2,rep,name=ch_list,json=chList,proto3" json:"ch_list,omitempty"`
	NextPage             uint32              `protobuf:"varint,3,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetRecLotteryChListResponse) Reset()         { *m = GetRecLotteryChListResponse{} }
func (m *GetRecLotteryChListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecLotteryChListResponse) ProtoMessage()    {}
func (*GetRecLotteryChListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{2}
}
func (m *GetRecLotteryChListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecLotteryChListResponse.Unmarshal(m, b)
}
func (m *GetRecLotteryChListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecLotteryChListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecLotteryChListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecLotteryChListResponse.Merge(dst, src)
}
func (m *GetRecLotteryChListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecLotteryChListResponse.Size(m)
}
func (m *GetRecLotteryChListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecLotteryChListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecLotteryChListResponse proto.InternalMessageInfo

func (m *GetRecLotteryChListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecLotteryChListResponse) GetChList() []*RecLotteryChInfo {
	if m != nil {
		return m.ChList
	}
	return nil
}

func (m *GetRecLotteryChListResponse) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

// 房间基础信息
type ChannelBaseInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IconMd5              string   `protobuf:"bytes,3,opt,name=icon_md5,json=iconMd5,proto3" json:"icon_md5,omitempty"`
	ChannelType          uint32   `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	BindId               uint32   `protobuf:"varint,5,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelBaseInfo) Reset()         { *m = ChannelBaseInfo{} }
func (m *ChannelBaseInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelBaseInfo) ProtoMessage()    {}
func (*ChannelBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{3}
}
func (m *ChannelBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBaseInfo.Unmarshal(m, b)
}
func (m *ChannelBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBaseInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBaseInfo.Merge(dst, src)
}
func (m *ChannelBaseInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelBaseInfo.Size(m)
}
func (m *ChannelBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBaseInfo proto.InternalMessageInfo

func (m *ChannelBaseInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelBaseInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelBaseInfo) GetIconMd5() string {
	if m != nil {
		return m.IconMd5
	}
	return ""
}

func (m *ChannelBaseInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelBaseInfo) GetBindId() uint32 {
	if m != nil {
		return m.BindId
	}
	return 0
}

// 用户基础信息
type UserBaseInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	NickName             string   `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	HeadImg              string   `protobuf:"bytes,3,opt,name=head_img,json=headImg,proto3" json:"head_img,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Account              string   `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBaseInfo) Reset()         { *m = UserBaseInfo{} }
func (m *UserBaseInfo) String() string { return proto.CompactTextString(m) }
func (*UserBaseInfo) ProtoMessage()    {}
func (*UserBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{4}
}
func (m *UserBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBaseInfo.Unmarshal(m, b)
}
func (m *UserBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBaseInfo.Marshal(b, m, deterministic)
}
func (dst *UserBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBaseInfo.Merge(dst, src)
}
func (m *UserBaseInfo) XXX_Size() int {
	return xxx_messageInfo_UserBaseInfo.Size(m)
}
func (m *UserBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserBaseInfo proto.InternalMessageInfo

func (m *UserBaseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBaseInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *UserBaseInfo) GetHeadImg() string {
	if m != nil {
		return m.HeadImg
	}
	return ""
}

func (m *UserBaseInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserBaseInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

// 房间顶部浮窗
type ChannelTopOverLay struct {
	Type                 uint32           `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ChannelInfo          *ChannelBaseInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	UserInfo             *UserBaseInfo    `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	FollowedUserInfo     *UserBaseInfo    `protobuf:"bytes,4,opt,name=followed_user_info,json=followedUserInfo,proto3" json:"followed_user_info,omitempty"`
	ChannelTagText       string           `protobuf:"bytes,5,opt,name=channel_tag_text,json=channelTagText,proto3" json:"channel_tag_text,omitempty"`
	RecText              string           `protobuf:"bytes,6,opt,name=rec_text,json=recText,proto3" json:"rec_text,omitempty"`
	RecPoorSource        uint32           `protobuf:"varint,7,opt,name=rec_poor_source,json=recPoorSource,proto3" json:"rec_poor_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ChannelTopOverLay) Reset()         { *m = ChannelTopOverLay{} }
func (m *ChannelTopOverLay) String() string { return proto.CompactTextString(m) }
func (*ChannelTopOverLay) ProtoMessage()    {}
func (*ChannelTopOverLay) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{5}
}
func (m *ChannelTopOverLay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelTopOverLay.Unmarshal(m, b)
}
func (m *ChannelTopOverLay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelTopOverLay.Marshal(b, m, deterministic)
}
func (dst *ChannelTopOverLay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelTopOverLay.Merge(dst, src)
}
func (m *ChannelTopOverLay) XXX_Size() int {
	return xxx_messageInfo_ChannelTopOverLay.Size(m)
}
func (m *ChannelTopOverLay) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelTopOverLay.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelTopOverLay proto.InternalMessageInfo

func (m *ChannelTopOverLay) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ChannelTopOverLay) GetChannelInfo() *ChannelBaseInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *ChannelTopOverLay) GetUserInfo() *UserBaseInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *ChannelTopOverLay) GetFollowedUserInfo() *UserBaseInfo {
	if m != nil {
		return m.FollowedUserInfo
	}
	return nil
}

func (m *ChannelTopOverLay) GetChannelTagText() string {
	if m != nil {
		return m.ChannelTagText
	}
	return ""
}

func (m *ChannelTopOverLay) GetRecText() string {
	if m != nil {
		return m.RecText
	}
	return ""
}

func (m *ChannelTopOverLay) GetRecPoorSource() uint32 {
	if m != nil {
		return m.RecPoorSource
	}
	return 0
}

// 房间顶部浮窗
type GetChannelTopOverLayRequest struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RefreshCnt           uint32            `protobuf:"varint,2,opt,name=refresh_cnt,json=refreshCnt,proto3" json:"refresh_cnt,omitempty"`
	MapTypeCnt           map[uint32]uint32 `protobuf:"bytes,3,rep,name=map_type_cnt,json=mapTypeCnt,proto3" json:"map_type_cnt,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChannelTopOverLayRequest) Reset()         { *m = GetChannelTopOverLayRequest{} }
func (m *GetChannelTopOverLayRequest) String() string { return proto.CompactTextString(m) }
func (*GetChannelTopOverLayRequest) ProtoMessage()    {}
func (*GetChannelTopOverLayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{6}
}
func (m *GetChannelTopOverLayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTopOverLayRequest.Unmarshal(m, b)
}
func (m *GetChannelTopOverLayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTopOverLayRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelTopOverLayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTopOverLayRequest.Merge(dst, src)
}
func (m *GetChannelTopOverLayRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelTopOverLayRequest.Size(m)
}
func (m *GetChannelTopOverLayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTopOverLayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTopOverLayRequest proto.InternalMessageInfo

func (m *GetChannelTopOverLayRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelTopOverLayRequest) GetRefreshCnt() uint32 {
	if m != nil {
		return m.RefreshCnt
	}
	return 0
}

func (m *GetChannelTopOverLayRequest) GetMapTypeCnt() map[uint32]uint32 {
	if m != nil {
		return m.MapTypeCnt
	}
	return nil
}

type GetChannelTopOverLayResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TopOverLay           *ChannelTopOverLay `protobuf:"bytes,2,opt,name=top_over_lay,json=topOverLay,proto3" json:"top_over_lay,omitempty"`
	IntervalTs           uint32             `protobuf:"varint,3,opt,name=interval_ts,json=intervalTs,proto3" json:"interval_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChannelTopOverLayResponse) Reset()         { *m = GetChannelTopOverLayResponse{} }
func (m *GetChannelTopOverLayResponse) String() string { return proto.CompactTextString(m) }
func (*GetChannelTopOverLayResponse) ProtoMessage()    {}
func (*GetChannelTopOverLayResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{7}
}
func (m *GetChannelTopOverLayResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelTopOverLayResponse.Unmarshal(m, b)
}
func (m *GetChannelTopOverLayResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelTopOverLayResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelTopOverLayResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelTopOverLayResponse.Merge(dst, src)
}
func (m *GetChannelTopOverLayResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelTopOverLayResponse.Size(m)
}
func (m *GetChannelTopOverLayResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelTopOverLayResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelTopOverLayResponse proto.InternalMessageInfo

func (m *GetChannelTopOverLayResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelTopOverLayResponse) GetTopOverLay() *ChannelTopOverLay {
	if m != nil {
		return m.TopOverLay
	}
	return nil
}

func (m *GetChannelTopOverLayResponse) GetIntervalTs() uint32 {
	if m != nil {
		return m.IntervalTs
	}
	return 0
}

// 房间跟随浮层信息
type ChannelFollowFloatMsg struct {
	FollowUserInfo       *UserBaseInfo `protobuf:"bytes,1,opt,name=follow_user_info,json=followUserInfo,proto3" json:"follow_user_info,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MsgText              string        `protobuf:"bytes,3,opt,name=msg_text,json=msgText,proto3" json:"msg_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelFollowFloatMsg) Reset()         { *m = ChannelFollowFloatMsg{} }
func (m *ChannelFollowFloatMsg) String() string { return proto.CompactTextString(m) }
func (*ChannelFollowFloatMsg) ProtoMessage()    {}
func (*ChannelFollowFloatMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{8}
}
func (m *ChannelFollowFloatMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelFollowFloatMsg.Unmarshal(m, b)
}
func (m *ChannelFollowFloatMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelFollowFloatMsg.Marshal(b, m, deterministic)
}
func (dst *ChannelFollowFloatMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelFollowFloatMsg.Merge(dst, src)
}
func (m *ChannelFollowFloatMsg) XXX_Size() int {
	return xxx_messageInfo_ChannelFollowFloatMsg.Size(m)
}
func (m *ChannelFollowFloatMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelFollowFloatMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelFollowFloatMsg proto.InternalMessageInfo

func (m *ChannelFollowFloatMsg) GetFollowUserInfo() *UserBaseInfo {
	if m != nil {
		return m.FollowUserInfo
	}
	return nil
}

func (m *ChannelFollowFloatMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelFollowFloatMsg) GetMsgText() string {
	if m != nil {
		return m.MsgText
	}
	return ""
}

// 营收开关
type GetRevenueSwitchHubRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRevenueSwitchHubRequest) Reset()         { *m = GetRevenueSwitchHubRequest{} }
func (m *GetRevenueSwitchHubRequest) String() string { return proto.CompactTextString(m) }
func (*GetRevenueSwitchHubRequest) ProtoMessage()    {}
func (*GetRevenueSwitchHubRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{9}
}
func (m *GetRevenueSwitchHubRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueSwitchHubRequest.Unmarshal(m, b)
}
func (m *GetRevenueSwitchHubRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueSwitchHubRequest.Marshal(b, m, deterministic)
}
func (dst *GetRevenueSwitchHubRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueSwitchHubRequest.Merge(dst, src)
}
func (m *GetRevenueSwitchHubRequest) XXX_Size() int {
	return xxx_messageInfo_GetRevenueSwitchHubRequest.Size(m)
}
func (m *GetRevenueSwitchHubRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueSwitchHubRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueSwitchHubRequest proto.InternalMessageInfo

func (m *GetRevenueSwitchHubRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetRevenueSwitchHubResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsOpenMap            map[uint32]bool `protobuf:"bytes,2,rep,name=is_open_map,json=isOpenMap,proto3" json:"is_open_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRevenueSwitchHubResponse) Reset()         { *m = GetRevenueSwitchHubResponse{} }
func (m *GetRevenueSwitchHubResponse) String() string { return proto.CompactTextString(m) }
func (*GetRevenueSwitchHubResponse) ProtoMessage()    {}
func (*GetRevenueSwitchHubResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{10}
}
func (m *GetRevenueSwitchHubResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRevenueSwitchHubResponse.Unmarshal(m, b)
}
func (m *GetRevenueSwitchHubResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRevenueSwitchHubResponse.Marshal(b, m, deterministic)
}
func (dst *GetRevenueSwitchHubResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRevenueSwitchHubResponse.Merge(dst, src)
}
func (m *GetRevenueSwitchHubResponse) XXX_Size() int {
	return xxx_messageInfo_GetRevenueSwitchHubResponse.Size(m)
}
func (m *GetRevenueSwitchHubResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRevenueSwitchHubResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRevenueSwitchHubResponse proto.InternalMessageInfo

func (m *GetRevenueSwitchHubResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRevenueSwitchHubResponse) GetIsOpenMap() map[uint32]bool {
	if m != nil {
		return m.IsOpenMap
	}
	return nil
}

// 营收开关
type SetRevenueSwitchHubRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SwitchType           uint32       `protobuf:"varint,2,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	IsOpen               bool         `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetRevenueSwitchHubRequest) Reset()         { *m = SetRevenueSwitchHubRequest{} }
func (m *SetRevenueSwitchHubRequest) String() string { return proto.CompactTextString(m) }
func (*SetRevenueSwitchHubRequest) ProtoMessage()    {}
func (*SetRevenueSwitchHubRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{11}
}
func (m *SetRevenueSwitchHubRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRevenueSwitchHubRequest.Unmarshal(m, b)
}
func (m *SetRevenueSwitchHubRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRevenueSwitchHubRequest.Marshal(b, m, deterministic)
}
func (dst *SetRevenueSwitchHubRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRevenueSwitchHubRequest.Merge(dst, src)
}
func (m *SetRevenueSwitchHubRequest) XXX_Size() int {
	return xxx_messageInfo_SetRevenueSwitchHubRequest.Size(m)
}
func (m *SetRevenueSwitchHubRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRevenueSwitchHubRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetRevenueSwitchHubRequest proto.InternalMessageInfo

func (m *SetRevenueSwitchHubRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetRevenueSwitchHubRequest) GetSwitchType() uint32 {
	if m != nil {
		return m.SwitchType
	}
	return 0
}

func (m *SetRevenueSwitchHubRequest) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetRevenueSwitchHubResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetRevenueSwitchHubResponse) Reset()         { *m = SetRevenueSwitchHubResponse{} }
func (m *SetRevenueSwitchHubResponse) String() string { return proto.CompactTextString(m) }
func (*SetRevenueSwitchHubResponse) ProtoMessage()    {}
func (*SetRevenueSwitchHubResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{12}
}
func (m *SetRevenueSwitchHubResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRevenueSwitchHubResponse.Unmarshal(m, b)
}
func (m *SetRevenueSwitchHubResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRevenueSwitchHubResponse.Marshal(b, m, deterministic)
}
func (dst *SetRevenueSwitchHubResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRevenueSwitchHubResponse.Merge(dst, src)
}
func (m *SetRevenueSwitchHubResponse) XXX_Size() int {
	return xxx_messageInfo_SetRevenueSwitchHubResponse.Size(m)
}
func (m *SetRevenueSwitchHubResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRevenueSwitchHubResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetRevenueSwitchHubResponse proto.InternalMessageInfo

func (m *SetRevenueSwitchHubResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 顶部浮窗
type GetGlobalTopOverLayRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGlobalTopOverLayRequest) Reset()         { *m = GetGlobalTopOverLayRequest{} }
func (m *GetGlobalTopOverLayRequest) String() string { return proto.CompactTextString(m) }
func (*GetGlobalTopOverLayRequest) ProtoMessage()    {}
func (*GetGlobalTopOverLayRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{13}
}
func (m *GetGlobalTopOverLayRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGlobalTopOverLayRequest.Unmarshal(m, b)
}
func (m *GetGlobalTopOverLayRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGlobalTopOverLayRequest.Marshal(b, m, deterministic)
}
func (dst *GetGlobalTopOverLayRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGlobalTopOverLayRequest.Merge(dst, src)
}
func (m *GetGlobalTopOverLayRequest) XXX_Size() int {
	return xxx_messageInfo_GetGlobalTopOverLayRequest.Size(m)
}
func (m *GetGlobalTopOverLayRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGlobalTopOverLayRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGlobalTopOverLayRequest proto.InternalMessageInfo

func (m *GetGlobalTopOverLayRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetGlobalTopOverLayResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Type                 uint32        `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	UiXml                string        `protobuf:"bytes,3,opt,name=ui_xml,json=uiXml,proto3" json:"ui_xml,omitempty"`
	FollowedUserInfo     *UserBaseInfo `protobuf:"bytes,4,opt,name=followed_user_info,json=followedUserInfo,proto3" json:"followed_user_info,omitempty"`
	ReportJsonData       string        `protobuf:"bytes,5,opt,name=report_json_data,json=reportJsonData,proto3" json:"report_json_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetGlobalTopOverLayResponse) Reset()         { *m = GetGlobalTopOverLayResponse{} }
func (m *GetGlobalTopOverLayResponse) String() string { return proto.CompactTextString(m) }
func (*GetGlobalTopOverLayResponse) ProtoMessage()    {}
func (*GetGlobalTopOverLayResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{14}
}
func (m *GetGlobalTopOverLayResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGlobalTopOverLayResponse.Unmarshal(m, b)
}
func (m *GetGlobalTopOverLayResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGlobalTopOverLayResponse.Marshal(b, m, deterministic)
}
func (dst *GetGlobalTopOverLayResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGlobalTopOverLayResponse.Merge(dst, src)
}
func (m *GetGlobalTopOverLayResponse) XXX_Size() int {
	return xxx_messageInfo_GetGlobalTopOverLayResponse.Size(m)
}
func (m *GetGlobalTopOverLayResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGlobalTopOverLayResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGlobalTopOverLayResponse proto.InternalMessageInfo

func (m *GetGlobalTopOverLayResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGlobalTopOverLayResponse) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetGlobalTopOverLayResponse) GetUiXml() string {
	if m != nil {
		return m.UiXml
	}
	return ""
}

func (m *GetGlobalTopOverLayResponse) GetFollowedUserInfo() *UserBaseInfo {
	if m != nil {
		return m.FollowedUserInfo
	}
	return nil
}

func (m *GetGlobalTopOverLayResponse) GetReportJsonData() string {
	if m != nil {
		return m.ReportJsonData
	}
	return ""
}

// 获取推荐列表房间反馈配置
type GetRecFeedbackConfigRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRecFeedbackConfigRequest) Reset()         { *m = GetRecFeedbackConfigRequest{} }
func (m *GetRecFeedbackConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecFeedbackConfigRequest) ProtoMessage()    {}
func (*GetRecFeedbackConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{15}
}
func (m *GetRecFeedbackConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecFeedbackConfigRequest.Unmarshal(m, b)
}
func (m *GetRecFeedbackConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecFeedbackConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecFeedbackConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecFeedbackConfigRequest.Merge(dst, src)
}
func (m *GetRecFeedbackConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecFeedbackConfigRequest.Size(m)
}
func (m *GetRecFeedbackConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecFeedbackConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecFeedbackConfigRequest proto.InternalMessageInfo

func (m *GetRecFeedbackConfigRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetRecFeedbackConfigResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ReasonList           []string      `protobuf:"bytes,2,rep,name=reason_list,json=reasonList,proto3" json:"reason_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRecFeedbackConfigResponse) Reset()         { *m = GetRecFeedbackConfigResponse{} }
func (m *GetRecFeedbackConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecFeedbackConfigResponse) ProtoMessage()    {}
func (*GetRecFeedbackConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{16}
}
func (m *GetRecFeedbackConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecFeedbackConfigResponse.Unmarshal(m, b)
}
func (m *GetRecFeedbackConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecFeedbackConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecFeedbackConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecFeedbackConfigResponse.Merge(dst, src)
}
func (m *GetRecFeedbackConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecFeedbackConfigResponse.Size(m)
}
func (m *GetRecFeedbackConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecFeedbackConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecFeedbackConfigResponse proto.InternalMessageInfo

func (m *GetRecFeedbackConfigResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecFeedbackConfigResponse) GetReasonList() []string {
	if m != nil {
		return m.ReasonList
	}
	return nil
}

// 推荐房间反馈
type DoRecFeedbackRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ReasonList           []string     `protobuf:"bytes,3,rep,name=reason_list,json=reasonList,proto3" json:"reason_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DoRecFeedbackRequest) Reset()         { *m = DoRecFeedbackRequest{} }
func (m *DoRecFeedbackRequest) String() string { return proto.CompactTextString(m) }
func (*DoRecFeedbackRequest) ProtoMessage()    {}
func (*DoRecFeedbackRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{17}
}
func (m *DoRecFeedbackRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoRecFeedbackRequest.Unmarshal(m, b)
}
func (m *DoRecFeedbackRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoRecFeedbackRequest.Marshal(b, m, deterministic)
}
func (dst *DoRecFeedbackRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoRecFeedbackRequest.Merge(dst, src)
}
func (m *DoRecFeedbackRequest) XXX_Size() int {
	return xxx_messageInfo_DoRecFeedbackRequest.Size(m)
}
func (m *DoRecFeedbackRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DoRecFeedbackRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DoRecFeedbackRequest proto.InternalMessageInfo

func (m *DoRecFeedbackRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DoRecFeedbackRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DoRecFeedbackRequest) GetReasonList() []string {
	if m != nil {
		return m.ReasonList
	}
	return nil
}

type DoRecFeedbackResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DoRecFeedbackResponse) Reset()         { *m = DoRecFeedbackResponse{} }
func (m *DoRecFeedbackResponse) String() string { return proto.CompactTextString(m) }
func (*DoRecFeedbackResponse) ProtoMessage()    {}
func (*DoRecFeedbackResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_recommend_logic__7e937e549808f720, []int{18}
}
func (m *DoRecFeedbackResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoRecFeedbackResponse.Unmarshal(m, b)
}
func (m *DoRecFeedbackResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoRecFeedbackResponse.Marshal(b, m, deterministic)
}
func (dst *DoRecFeedbackResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoRecFeedbackResponse.Merge(dst, src)
}
func (m *DoRecFeedbackResponse) XXX_Size() int {
	return xxx_messageInfo_DoRecFeedbackResponse.Size(m)
}
func (m *DoRecFeedbackResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DoRecFeedbackResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DoRecFeedbackResponse proto.InternalMessageInfo

func (m *DoRecFeedbackResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*RecLotteryChInfo)(nil), "ga.channel_recommend_logic.RecLotteryChInfo")
	proto.RegisterType((*GetRecLotteryChListRequest)(nil), "ga.channel_recommend_logic.GetRecLotteryChListRequest")
	proto.RegisterType((*GetRecLotteryChListResponse)(nil), "ga.channel_recommend_logic.GetRecLotteryChListResponse")
	proto.RegisterType((*ChannelBaseInfo)(nil), "ga.channel_recommend_logic.ChannelBaseInfo")
	proto.RegisterType((*UserBaseInfo)(nil), "ga.channel_recommend_logic.UserBaseInfo")
	proto.RegisterType((*ChannelTopOverLay)(nil), "ga.channel_recommend_logic.ChannelTopOverLay")
	proto.RegisterType((*GetChannelTopOverLayRequest)(nil), "ga.channel_recommend_logic.GetChannelTopOverLayRequest")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.channel_recommend_logic.GetChannelTopOverLayRequest.MapTypeCntEntry")
	proto.RegisterType((*GetChannelTopOverLayResponse)(nil), "ga.channel_recommend_logic.GetChannelTopOverLayResponse")
	proto.RegisterType((*ChannelFollowFloatMsg)(nil), "ga.channel_recommend_logic.ChannelFollowFloatMsg")
	proto.RegisterType((*GetRevenueSwitchHubRequest)(nil), "ga.channel_recommend_logic.GetRevenueSwitchHubRequest")
	proto.RegisterType((*GetRevenueSwitchHubResponse)(nil), "ga.channel_recommend_logic.GetRevenueSwitchHubResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "ga.channel_recommend_logic.GetRevenueSwitchHubResponse.IsOpenMapEntry")
	proto.RegisterType((*SetRevenueSwitchHubRequest)(nil), "ga.channel_recommend_logic.SetRevenueSwitchHubRequest")
	proto.RegisterType((*SetRevenueSwitchHubResponse)(nil), "ga.channel_recommend_logic.SetRevenueSwitchHubResponse")
	proto.RegisterType((*GetGlobalTopOverLayRequest)(nil), "ga.channel_recommend_logic.GetGlobalTopOverLayRequest")
	proto.RegisterType((*GetGlobalTopOverLayResponse)(nil), "ga.channel_recommend_logic.GetGlobalTopOverLayResponse")
	proto.RegisterType((*GetRecFeedbackConfigRequest)(nil), "ga.channel_recommend_logic.GetRecFeedbackConfigRequest")
	proto.RegisterType((*GetRecFeedbackConfigResponse)(nil), "ga.channel_recommend_logic.GetRecFeedbackConfigResponse")
	proto.RegisterType((*DoRecFeedbackRequest)(nil), "ga.channel_recommend_logic.DoRecFeedbackRequest")
	proto.RegisterType((*DoRecFeedbackResponse)(nil), "ga.channel_recommend_logic.DoRecFeedbackResponse")
	proto.RegisterEnum("ga.channel_recommend_logic.RevenueSwitchHubType", RevenueSwitchHubType_name, RevenueSwitchHubType_value)
	proto.RegisterEnum("ga.channel_recommend_logic.ETopOverLayType", ETopOverLayType_name, ETopOverLayType_value)
	proto.RegisterEnum("ga.channel_recommend_logic.ChannelTopOverLay_EOverLayType", ChannelTopOverLay_EOverLayType_name, ChannelTopOverLay_EOverLayType_value)
}

func init() {
	proto.RegisterFile("channel_recommend_logic/channel_recommend_logic_.proto", fileDescriptor_channel_recommend_logic__7e937e549808f720)
}

var fileDescriptor_channel_recommend_logic__7e937e549808f720 = []byte{
	// 1311 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x57, 0xcf, 0x73, 0xdb, 0xc4,
	0x17, 0xff, 0xca, 0x4e, 0x62, 0xfb, 0xd9, 0x49, 0xfc, 0xd5, 0xa4, 0x53, 0x35, 0x29, 0x24, 0xa8,
	0x9d, 0x12, 0x0a, 0x75, 0x67, 0x02, 0x65, 0x18, 0x06, 0x0e, 0x89, 0xa3, 0x24, 0x86, 0xc4, 0xf6,
	0xc8, 0x4e, 0xa0, 0x5c, 0x76, 0xd6, 0xd2, 0x5a, 0x51, 0x23, 0x69, 0x55, 0xed, 0x3a, 0x8d, 0x7b,
	0xe8, 0x89, 0x3f, 0x80, 0x2b, 0xc3, 0x99, 0x1b, 0xff, 0x01, 0x07, 0xfe, 0x20, 0x66, 0xb8, 0x71,
	0x67, 0x76, 0xb5, 0xb2, 0x5d, 0xe7, 0x47, 0x31, 0xd0, 0x93, 0x77, 0xdf, 0xae, 0x3e, 0xef, 0xed,
	0x67, 0x3f, 0xef, 0xbd, 0x35, 0x7c, 0xea, 0x9c, 0xe2, 0x28, 0x22, 0x01, 0x4a, 0x88, 0x43, 0xc3,
	0x90, 0x44, 0x2e, 0x0a, 0xa8, 0xe7, 0x3b, 0x8f, 0xaf, 0xb1, 0xa3, 0x5a, 0x9c, 0x50, 0x4e, 0xf5,
	0x55, 0x0f, 0xd7, 0xae, 0xd9, 0xb2, 0xba, 0xe8, 0x61, 0xd4, 0xc3, 0x8c, 0xa4, 0x5b, 0xcd, 0xdf,
	0x72, 0x50, 0xb5, 0x89, 0x73, 0x48, 0x39, 0x27, 0xc9, 0xb0, 0x7e, 0xda, 0x88, 0xfa, 0x54, 0x7f,
	0x07, 0x20, 0xfb, 0xdc, 0x77, 0x0d, 0x6d, 0x43, 0xdb, 0x5c, 0xb4, 0x4b, 0xca, 0xd2, 0x70, 0x75,
	0x1d, 0xe6, 0x22, 0x1c, 0x12, 0x23, 0xb7, 0xa1, 0x6d, 0x96, 0x6c, 0x39, 0xd6, 0xef, 0x40, 0xd1,
	0x77, 0x68, 0x84, 0x42, 0xf7, 0x89, 0x91, 0x97, 0xf6, 0x82, 0x98, 0x1f, 0xb9, 0x4f, 0xf4, 0xfb,
	0xb0, 0x14, 0xa4, 0xf0, 0x48, 0x84, 0xc1, 0x99, 0x31, 0x27, 0x11, 0x2b, 0xca, 0x6a, 0x45, 0x6e,
	0x97, 0xe9, 0x6b, 0x50, 0xf2, 0xfc, 0x3e, 0x47, 0x12, 0x79, 0x5e, 0x22, 0x14, 0x85, 0xa1, 0x29,
	0xd0, 0xb3, 0x45, 0x01, 0x69, 0x2c, 0x8c, 0x17, 0x1b, 0x0e, 0x8d, 0x84, 0x6b, 0xb9, 0xe8, 0x44,
	0xdc, 0x28, 0x48, 0xe4, 0x82, 0x98, 0xd7, 0x23, 0xae, 0x3f, 0x80, 0x65, 0xb9, 0x74, 0x8e, 0x83,
	0x01, 0x41, 0x9c, 0x5c, 0x70, 0xa3, 0x28, 0xbf, 0x5e, 0x14, 0xe6, 0x13, 0x61, 0xed, 0x92, 0x0b,
	0xae, 0xbf, 0x07, 0x95, 0xec, 0xc0, 0x7c, 0x18, 0x13, 0xa3, 0x24, 0x61, 0xca, 0xca, 0xd6, 0x1d,
	0xc6, 0x44, 0xbf, 0x0d, 0x85, 0x9e, 0x1f, 0xb9, 0x82, 0x10, 0x90, 0xab, 0x0b, 0x62, 0xda, 0x70,
	0xcd, 0x01, 0xac, 0xee, 0x13, 0x3e, 0xc9, 0xe1, 0xa1, 0xcf, 0xb8, 0x4d, 0x9e, 0x0f, 0x08, 0x13,
	0x11, 0x14, 0x05, 0xdb, 0x28, 0x21, 0xcf, 0x25, 0x91, 0xe5, 0xad, 0x72, 0xcd, 0xc3, 0xb5, 0x1d,
	0xcc, 0x88, 0x4d, 0x9e, 0xdb, 0x85, 0x5e, 0x3a, 0x10, 0x9c, 0xc6, 0xd8, 0x4b, 0x39, 0x5d, 0xb4,
	0xe5, 0x58, 0x9c, 0x5a, 0xfc, 0x22, 0xe6, 0xbf, 0x24, 0x92, 0xd4, 0x45, 0xbb, 0x28, 0x0c, 0x1d,
	0xff, 0x25, 0x31, 0x7f, 0xd1, 0x60, 0xed, 0x4a, 0xbf, 0x2c, 0xa6, 0x11, 0x23, 0xfa, 0x07, 0x50,
	0x52, 0x8e, 0x59, 0xac, 0x3c, 0x57, 0xc6, 0x9e, 0x59, 0x6c, 0x17, 0x7b, 0x6a, 0xa4, 0x5b, 0x50,
	0x70, 0x4e, 0x51, 0xe0, 0x33, 0x6e, 0xe4, 0x36, 0xf2, 0x9b, 0xe5, 0xad, 0x8f, 0x6a, 0xd7, 0x0b,
	0xa8, 0x36, 0xad, 0x16, 0x7b, 0xc1, 0x91, 0x9e, 0x45, 0xb8, 0x11, 0xb9, 0xe0, 0x48, 0x9e, 0x43,
	0x85, 0x2b, 0x0c, 0x6d, 0xec, 0x11, 0xf3, 0x27, 0x0d, 0x96, 0xeb, 0x29, 0xa2, 0x88, 0xe0, 0x2d,
	0xc8, 0x6c, 0xfa, 0x0e, 0xe7, 0x6e, 0xbc, 0xc3, 0xf9, 0xd7, 0xee, 0xf0, 0x7b, 0x0d, 0x2a, 0xc7,
	0x8c, 0x24, 0xa3, 0xd0, 0xaa, 0x90, 0x1f, 0x8c, 0x62, 0x12, 0x43, 0x79, 0x3a, 0xdf, 0x39, 0x43,
	0x13, 0x21, 0x15, 0x85, 0xa1, 0xa9, 0xc2, 0x3a, 0x25, 0xd8, 0x45, 0x7e, 0xe8, 0x65, 0x61, 0x89,
	0x79, 0x23, 0xf4, 0x04, 0x12, 0x23, 0x17, 0x32, 0x9a, 0x79, 0x5b, 0x0c, 0x75, 0x03, 0x0a, 0xd8,
	0x71, 0xe8, 0x20, 0xe2, 0x4a, 0xe7, 0xd9, 0xd4, 0xfc, 0x3d, 0x0f, 0xff, 0x57, 0x24, 0x75, 0x69,
	0xdc, 0x3a, 0x27, 0xc9, 0x21, 0x1e, 0x0a, 0x1e, 0xe4, 0x81, 0xd2, 0x60, 0xe4, 0x58, 0x6f, 0x8e,
	0x0f, 0xeb, 0x47, 0x7d, 0x2a, 0x03, 0x2a, 0x6f, 0x7d, 0x78, 0xd3, 0xbd, 0x4d, 0xb1, 0x3f, 0x62,
	0x46, 0x9e, 0xd7, 0x82, 0xd2, 0x80, 0x91, 0x24, 0x05, 0xcb, 0x4b, 0xb0, 0xcd, 0x9b, 0xc0, 0x26,
	0xc9, 0xb2, 0x8b, 0xe2, 0x53, 0x09, 0x73, 0x02, 0x7a, 0x9f, 0x06, 0x01, 0x7d, 0x41, 0x5c, 0x34,
	0xc6, 0x9b, 0x9b, 0x11, 0xaf, 0x9a, 0x61, 0x1c, 0x67, 0xb8, 0x9b, 0x50, 0x1d, 0xdd, 0x2d, 0xf6,
	0xd2, 0x44, 0x4e, 0xb9, 0x5b, 0xca, 0xee, 0x17, 0x7b, 0x32, 0x93, 0xef, 0x40, 0x31, 0x21, 0x4e,
	0xba, 0x23, 0x2d, 0x14, 0x85, 0x84, 0x38, 0x72, 0xe9, 0x01, 0x2c, 0x8b, 0xa5, 0x98, 0xd2, 0x04,
	0x31, 0x3a, 0x48, 0x1c, 0xa2, 0xca, 0xc5, 0x62, 0x42, 0x9c, 0x36, 0xa5, 0x49, 0x47, 0x1a, 0xcd,
	0x3e, 0x54, 0x2c, 0xc5, 0xbd, 0x54, 0xcd, 0x3a, 0xac, 0x59, 0xa8, 0x75, 0x62, 0xd9, 0xe8, 0x70,
	0xfb, 0x29, 0xea, 0x3e, 0x6d, 0x5b, 0xe8, 0xb8, 0xd9, 0x69, 0x5b, 0xf5, 0xc6, 0x5e, 0xc3, 0xda,
	0xad, 0xfe, 0x4f, 0x5f, 0x83, 0xdb, 0xd3, 0x1b, 0xea, 0x07, 0xdb, 0xcd, 0xa6, 0x75, 0x58, 0xd5,
	0x74, 0x03, 0x56, 0x2e, 0x7d, 0xdd, 0xb1, 0xec, 0x6a, 0xce, 0xfc, 0x21, 0x27, 0x33, 0xf8, 0xd2,
	0x85, 0xcf, 0x5a, 0x3a, 0xd6, 0xa1, 0x9c, 0x90, 0x7e, 0x42, 0xd8, 0xa9, 0x2c, 0x81, 0x69, 0x05,
	0x01, 0x65, 0x12, 0x55, 0xd0, 0x87, 0x4a, 0x88, 0x63, 0x99, 0x15, 0x72, 0x47, 0x5e, 0x26, 0xf9,
	0xfe, 0x4d, 0xf7, 0x71, 0x43, 0x5c, 0xb5, 0x23, 0x1c, 0x0b, 0x5e, 0xea, 0x11, 0xb7, 0x22, 0x9e,
	0x0c, 0x6d, 0x08, 0x47, 0x86, 0xd5, 0x2f, 0x61, 0x79, 0x6a, 0x59, 0x24, 0xc0, 0x19, 0x19, 0x66,
	0xa9, 0x74, 0x46, 0x86, 0xfa, 0x0a, 0xcc, 0xcb, 0x82, 0xac, 0x42, 0x4d, 0x27, 0x9f, 0xe7, 0x3e,
	0xd3, 0xcc, 0x5f, 0x35, 0xb8, 0x7b, 0xb5, 0xeb, 0xd9, 0xab, 0x5a, 0x0b, 0x2a, 0x9c, 0xc6, 0x88,
	0x9e, 0x93, 0x04, 0x05, 0x78, 0xa8, 0x52, 0xe4, 0xd1, 0xdf, 0x48, 0x91, 0x09, 0xbf, 0xc0, 0xc7,
	0x79, 0xb8, 0x0e, 0x65, 0x3f, 0xe2, 0x24, 0x39, 0xc7, 0x81, 0x68, 0x62, 0x69, 0x85, 0x83, 0xcc,
	0xd4, 0x65, 0xe6, 0xcf, 0x1a, 0xdc, 0x52, 0x10, 0x7b, 0x52, 0xc1, 0x7b, 0x01, 0xc5, 0xfc, 0x88,
	0x79, 0xba, 0x0d, 0x4a, 0xd3, 0x13, 0x59, 0xa1, 0xcd, 0x98, 0x15, 0x4b, 0x29, 0xc2, 0x28, 0x27,
	0x5e, 0xaf, 0x9e, 0xb9, 0xe9, 0xea, 0x79, 0x07, 0x8a, 0x21, 0x53, 0xa9, 0xa2, 0x4a, 0x52, 0xc8,
	0x64, 0x8e, 0x98, 0xbb, 0xaa, 0x63, 0x9d, 0x93, 0x68, 0x40, 0x3a, 0x2f, 0x7c, 0xee, 0x9c, 0x1e,
	0x0c, 0x7a, 0x33, 0xca, 0xce, 0xfc, 0x23, 0x6b, 0x40, 0xd3, 0x30, 0xb3, 0x5f, 0x55, 0x1f, 0xca,
	0x3e, 0x43, 0x34, 0x26, 0x11, 0x0a, 0x71, 0xac, 0x9a, 0xd0, 0xde, 0x1b, 0xf4, 0x79, 0x9d, 0xe3,
	0x5a, 0x83, 0xb5, 0x62, 0x12, 0x1d, 0xe1, 0x38, 0x95, 0x67, 0xc9, 0xcf, 0xe6, 0xab, 0x5f, 0xc0,
	0xd2, 0xeb, 0x8b, 0x6f, 0x12, 0x67, 0x71, 0x52, 0x9c, 0xaf, 0x60, 0xb5, 0xf3, 0xaf, 0x69, 0x13,
	0x2a, 0x62, 0xf2, 0xdb, 0xb4, 0x4b, 0xa9, 0x6c, 0x4d, 0x4d, 0x59, 0x93, 0x52, 0x64, 0xc8, 0x7b,
	0x2b, 0xda, 0x0b, 0xe9, 0x01, 0xcc, 0x03, 0x58, 0xeb, 0xfc, 0x27, 0x7c, 0x2b, 0x01, 0xec, 0x07,
	0xb4, 0x87, 0xff, 0x79, 0xdd, 0x31, 0xff, 0x4c, 0x05, 0x70, 0x19, 0x66, 0x76, 0x01, 0x64, 0x2d,
	0x2e, 0x37, 0xd1, 0xe2, 0x6e, 0xc1, 0xc2, 0xc0, 0x47, 0x17, 0x61, 0xa0, 0xe4, 0x3b, 0x3f, 0xf0,
	0xbf, 0x0d, 0x83, 0xb7, 0xd9, 0x62, 0x12, 0x12, 0xd3, 0x84, 0xa3, 0x67, 0x8c, 0x46, 0xc8, 0xc5,
	0x1c, 0x67, 0x2d, 0x26, 0xb5, 0x7f, 0xc5, 0x68, 0xb4, 0x8b, 0x39, 0x36, 0xad, 0xec, 0xe1, 0xb5,
	0x47, 0x88, 0xdb, 0xc3, 0xce, 0x59, 0x9d, 0x46, 0x7d, 0xdf, 0x9b, 0x95, 0xbe, 0x67, 0xb2, 0xd4,
	0x5d, 0x01, 0x33, 0x3b, 0x7d, 0xb2, 0x03, 0x60, 0x11, 0xf6, 0xe8, 0x11, 0x57, 0x12, 0x1d, 0x40,
	0x98, 0xc4, 0xd3, 0xcc, 0x7c, 0x05, 0x2b, 0xbb, 0x74, 0xc2, 0xd5, 0xac, 0xa2, 0x7d, 0x43, 0xad,
	0x99, 0xf2, 0x9f, 0xbf, 0xe4, 0x7f, 0x07, 0x6e, 0x4d, 0xf9, 0x9f, 0xf9, 0x90, 0x0f, 0x09, 0xac,
	0x4c, 0x6b, 0x5f, 0xe6, 0xcb, 0xfb, 0x70, 0xcf, 0xb6, 0x4e, 0xac, 0xe6, 0xb1, 0x85, 0x3a, 0xdf,
	0x34, 0xba, 0xf5, 0x03, 0x74, 0x70, 0xbc, 0x73, 0x55, 0x9b, 0xbe, 0x0f, 0x1b, 0xd7, 0x6d, 0xec,
	0x34, 0xb7, 0xeb, 0x5f, 0xef, 0x6c, 0xdb, 0x55, 0xed, 0xe1, 0x8f, 0x1a, 0x2c, 0x5b, 0x63, 0x35,
	0x4b, 0x17, 0xf7, 0x60, 0xdd, 0x42, 0xdd, 0x56, 0xfb, 0xc6, 0x57, 0x80, 0x7c, 0x26, 0x5c, 0xde,
	0x34, 0x7e, 0x09, 0xdc, 0x05, 0xe3, 0x4a, 0x14, 0xf9, 0x1a, 0xd0, 0x4d, 0x78, 0xf7, 0xaa, 0x55,
	0xab, 0xd3, 0x6e, 0xd9, 0x5d, 0xb4, 0xdf, 0xda, 0xad, 0xe6, 0x77, 0xda, 0x60, 0x38, 0x34, 0xac,
	0x0d, 0xfd, 0x21, 0x1d, 0x08, 0x96, 0x42, 0xea, 0x92, 0x20, 0xfd, 0x23, 0xf7, 0xdd, 0x27, 0x1e,
	0x0d, 0x70, 0xe4, 0xd5, 0x9e, 0x6c, 0x71, 0x5e, 0x73, 0x68, 0xf8, 0x58, 0x9a, 0x1d, 0x1a, 0x3c,
	0xc6, 0x71, 0x9c, 0xfd, 0x61, 0x7c, 0x34, 0x4a, 0x8a, 0x47, 0x32, 0x29, 0x7a, 0x0b, 0x72, 0xd7,
	0xc7, 0x7f, 0x05, 0x00, 0x00, 0xff, 0xff, 0x5a, 0x33, 0xf4, 0x86, 0x6a, 0x0e, 0x00, 0x00,
}
