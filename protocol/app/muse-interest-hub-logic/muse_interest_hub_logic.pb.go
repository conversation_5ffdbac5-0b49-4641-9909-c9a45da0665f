// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse_interest_hub_logic/muse_interest_hub_logic.proto

package muse_interest_hub_logic // import "golang.52tt.com/protocol/app/muse-interest-hub-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SwitchHubType int32

const (
	SwitchHubType_SWITCH_HUB_TYPE_UNSPECIFIED       SwitchHubType = 0
	SwitchHubType_SWITCH_HUB_TYPE_SAME_CITY_VISIBLE SwitchHubType = 1
	SwitchHubType_SWITCH_HUB_TYPE_CHANNEL_ALLOCATE  SwitchHubType = 2
	SwitchHubType_SWITCH_HUB_TYPE_AGE_TAG           SwitchHubType = 3
)

var SwitchHubType_name = map[int32]string{
	0: "SWITCH_HUB_TYPE_UNSPECIFIED",
	1: "SWITCH_HUB_TYPE_SAME_CITY_VISIBLE",
	2: "SWITCH_HUB_TYPE_CHANNEL_ALLOCATE",
	3: "SWITCH_HUB_TYPE_AGE_TAG",
}
var SwitchHubType_value = map[string]int32{
	"SWITCH_HUB_TYPE_UNSPECIFIED":       0,
	"SWITCH_HUB_TYPE_SAME_CITY_VISIBLE": 1,
	"SWITCH_HUB_TYPE_CHANNEL_ALLOCATE":  2,
	"SWITCH_HUB_TYPE_AGE_TAG":           3,
}

func (x SwitchHubType) String() string {
	return proto.EnumName(SwitchHubType_name, int32(x))
}
func (SwitchHubType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{0}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type STRATEGY_TYPE int32

const (
	STRATEGY_TYPE_STRATEGY_TYPE_UNSPECIFIED                   STRATEGY_TYPE = 0
	STRATEGY_TYPE_STRATEGY_TYPE_INVITE_IN_CHANNEL_MT          STRATEGY_TYPE = 1
	STRATEGY_TYPE_STRATEGY_TYPE_HOME_MIX_CHANNEL_RCMD         STRATEGY_TYPE = 2
	STRATEGY_TYPE_STRATEGY_TYPE_HOME_FOLLOW_FLOAT             STRATEGY_TYPE = 3
	STRATEGY_TYPE_STRATEGY_TYPE_HOME_PGC_RCMD_CHANNEL_HIDE    STRATEGY_TYPE = 4
	STRATEGY_TYPE_STRATEGY_TYPE_PUBLIC_SCREEN_QUICK_MSG       STRATEGY_TYPE = 5
	STRATEGY_TYPE_STRATEGY_TYPE_PUBLIC_SCREEN_SEND_EMOJI_PACK STRATEGY_TYPE = 6
	STRATEGY_TYPE_STRATEGY_TYPE_PUBLIC_SCREEN_MSG_PLUS_ONE    STRATEGY_TYPE = 7
	STRATEGY_TYPE_STRATEGY_TYPE_PUBLIC_SCREEN_GREETING_MSG    STRATEGY_TYPE = 8
	STRATEGY_TYPE_STRATEGY_TYPE_AUTO_INVITE_ON_MIC            STRATEGY_TYPE = 9
	STRATEGY_TYPE_STRATEGY_TYPE_ONE_CLICK_INVITE_ON_MIC       STRATEGY_TYPE = 10
	STRATEGY_TYPE_STRATEGY_TYPE_CHANNEL_GUIDE_FOLLOW          STRATEGY_TYPE = 11
	//  STRATEGY_TYPE_NEW_HOT_GAME = 12; // 新热聊挑战
	STRATEGY_TYPE_STRATEGY_TYPE_HOME_MIX_SAME_TYPE_CHANNEL_INSERT STRATEGY_TYPE = 12
	STRATEGY_TYPE_STRATEGY_TYPE_GUIDE_ON_MIC                      STRATEGY_TYPE = 13
	STRATEGY_TYPE_STRATEGY_TYPE_USER_MEGAPHONE                    STRATEGY_TYPE = 14
	STRATEGY_TYPE_STRATEGY_TYPE_ICE_BREAKER_POPUP                 STRATEGY_TYPE = 15
	STRATEGY_TYPE_STRATEGY_TYPE_SHINING_POINT                     STRATEGY_TYPE = 16
	STRATEGY_TYPE_STRATEGY_TYPE_TODAY_COUPLE                      STRATEGY_TYPE = 17
	STRATEGY_TYPE_STRATEGY_TYPE_CHANNEL_PREFERENCE_KEYWORD        STRATEGY_TYPE = 18
	STRATEGY_TYPE_STRATEGY_TYPE_CHANNEL_CATEGORY_TYPE             STRATEGY_TYPE = 19
	STRATEGY_TYPE_STRATEGY_TYPE_PUBLIC_SCREEN_USER_BASE_MSG       STRATEGY_TYPE = 20
	STRATEGY_TYPE_STRATEGY_TYPE_PUBLIC_SCREEN_SHORTCUT_IMAGE      STRATEGY_TYPE = 21
	STRATEGY_TYPE_STRATEGY_TYPE_PUBLIC_ICE_BREAKER_POPUP          STRATEGY_TYPE = 22
	STRATEGY_TYPE_STRATEGY_TYPE_NEW_VERSION_ICE_BREAKER_POPUP     STRATEGY_TYPE = 23
	STRATEGY_TYPE_STRATEGY_TYPE_ICE_BREAKER_POPUP_PUSH            STRATEGY_TYPE = 24
	STRATEGY_TYPE_STRATEGY_TYPE_AI_CUPID                          STRATEGY_TYPE = 25
	STRATEGY_TYPE_STRATEGY_TYPE_AI_Inspiration                    STRATEGY_TYPE = 26
	STRATEGY_TYPE_STRATEGY_TYPE_FLASH_CHAT                        STRATEGY_TYPE = 27
	STRATEGY_TYPE_STRATEGY_TYPE_ROLE_PLAY                         STRATEGY_TYPE = 28
)

var STRATEGY_TYPE_name = map[int32]string{
	0:  "STRATEGY_TYPE_UNSPECIFIED",
	1:  "STRATEGY_TYPE_INVITE_IN_CHANNEL_MT",
	2:  "STRATEGY_TYPE_HOME_MIX_CHANNEL_RCMD",
	3:  "STRATEGY_TYPE_HOME_FOLLOW_FLOAT",
	4:  "STRATEGY_TYPE_HOME_PGC_RCMD_CHANNEL_HIDE",
	5:  "STRATEGY_TYPE_PUBLIC_SCREEN_QUICK_MSG",
	6:  "STRATEGY_TYPE_PUBLIC_SCREEN_SEND_EMOJI_PACK",
	7:  "STRATEGY_TYPE_PUBLIC_SCREEN_MSG_PLUS_ONE",
	8:  "STRATEGY_TYPE_PUBLIC_SCREEN_GREETING_MSG",
	9:  "STRATEGY_TYPE_AUTO_INVITE_ON_MIC",
	10: "STRATEGY_TYPE_ONE_CLICK_INVITE_ON_MIC",
	11: "STRATEGY_TYPE_CHANNEL_GUIDE_FOLLOW",
	12: "STRATEGY_TYPE_HOME_MIX_SAME_TYPE_CHANNEL_INSERT",
	13: "STRATEGY_TYPE_GUIDE_ON_MIC",
	14: "STRATEGY_TYPE_USER_MEGAPHONE",
	15: "STRATEGY_TYPE_ICE_BREAKER_POPUP",
	16: "STRATEGY_TYPE_SHINING_POINT",
	17: "STRATEGY_TYPE_TODAY_COUPLE",
	18: "STRATEGY_TYPE_CHANNEL_PREFERENCE_KEYWORD",
	19: "STRATEGY_TYPE_CHANNEL_CATEGORY_TYPE",
	20: "STRATEGY_TYPE_PUBLIC_SCREEN_USER_BASE_MSG",
	21: "STRATEGY_TYPE_PUBLIC_SCREEN_SHORTCUT_IMAGE",
	22: "STRATEGY_TYPE_PUBLIC_ICE_BREAKER_POPUP",
	23: "STRATEGY_TYPE_NEW_VERSION_ICE_BREAKER_POPUP",
	24: "STRATEGY_TYPE_ICE_BREAKER_POPUP_PUSH",
	25: "STRATEGY_TYPE_AI_CUPID",
	26: "STRATEGY_TYPE_AI_Inspiration",
	27: "STRATEGY_TYPE_FLASH_CHAT",
	28: "STRATEGY_TYPE_ROLE_PLAY",
}
var STRATEGY_TYPE_value = map[string]int32{
	"STRATEGY_TYPE_UNSPECIFIED":                       0,
	"STRATEGY_TYPE_INVITE_IN_CHANNEL_MT":              1,
	"STRATEGY_TYPE_HOME_MIX_CHANNEL_RCMD":             2,
	"STRATEGY_TYPE_HOME_FOLLOW_FLOAT":                 3,
	"STRATEGY_TYPE_HOME_PGC_RCMD_CHANNEL_HIDE":        4,
	"STRATEGY_TYPE_PUBLIC_SCREEN_QUICK_MSG":           5,
	"STRATEGY_TYPE_PUBLIC_SCREEN_SEND_EMOJI_PACK":     6,
	"STRATEGY_TYPE_PUBLIC_SCREEN_MSG_PLUS_ONE":        7,
	"STRATEGY_TYPE_PUBLIC_SCREEN_GREETING_MSG":        8,
	"STRATEGY_TYPE_AUTO_INVITE_ON_MIC":                9,
	"STRATEGY_TYPE_ONE_CLICK_INVITE_ON_MIC":           10,
	"STRATEGY_TYPE_CHANNEL_GUIDE_FOLLOW":              11,
	"STRATEGY_TYPE_HOME_MIX_SAME_TYPE_CHANNEL_INSERT": 12,
	"STRATEGY_TYPE_GUIDE_ON_MIC":                      13,
	"STRATEGY_TYPE_USER_MEGAPHONE":                    14,
	"STRATEGY_TYPE_ICE_BREAKER_POPUP":                 15,
	"STRATEGY_TYPE_SHINING_POINT":                     16,
	"STRATEGY_TYPE_TODAY_COUPLE":                      17,
	"STRATEGY_TYPE_CHANNEL_PREFERENCE_KEYWORD":        18,
	"STRATEGY_TYPE_CHANNEL_CATEGORY_TYPE":             19,
	"STRATEGY_TYPE_PUBLIC_SCREEN_USER_BASE_MSG":       20,
	"STRATEGY_TYPE_PUBLIC_SCREEN_SHORTCUT_IMAGE":      21,
	"STRATEGY_TYPE_PUBLIC_ICE_BREAKER_POPUP":          22,
	"STRATEGY_TYPE_NEW_VERSION_ICE_BREAKER_POPUP":     23,
	"STRATEGY_TYPE_ICE_BREAKER_POPUP_PUSH":            24,
	"STRATEGY_TYPE_AI_CUPID":                          25,
	"STRATEGY_TYPE_AI_Inspiration":                    26,
	"STRATEGY_TYPE_FLASH_CHAT":                        27,
	"STRATEGY_TYPE_ROLE_PLAY":                         28,
}

func (x STRATEGY_TYPE) String() string {
	return proto.EnumName(STRATEGY_TYPE_name, int32(x))
}
func (STRATEGY_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{1}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type STRATEGY_TYPE_VALUE int32

const (
	STRATEGY_TYPE_VALUE_STRATEGY_TYPE_VALUE_UNSPECIFIED STRATEGY_TYPE_VALUE = 0
	STRATEGY_TYPE_VALUE_STRATEGY_TYPE_VALUE_CLOSE       STRATEGY_TYPE_VALUE = 1
	STRATEGY_TYPE_VALUE_STRATEGY_TYPE_VALUE_OPEN        STRATEGY_TYPE_VALUE = 2
)

var STRATEGY_TYPE_VALUE_name = map[int32]string{
	0: "STRATEGY_TYPE_VALUE_UNSPECIFIED",
	1: "STRATEGY_TYPE_VALUE_CLOSE",
	2: "STRATEGY_TYPE_VALUE_OPEN",
}
var STRATEGY_TYPE_VALUE_value = map[string]int32{
	"STRATEGY_TYPE_VALUE_UNSPECIFIED": 0,
	"STRATEGY_TYPE_VALUE_CLOSE":       1,
	"STRATEGY_TYPE_VALUE_OPEN":        2,
}

func (x STRATEGY_TYPE_VALUE) String() string {
	return proto.EnumName(STRATEGY_TYPE_VALUE_name, int32(x))
}
func (STRATEGY_TYPE_VALUE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{2}
}

type MuseCommonXMLMsgType int32

const (
	MuseCommonXMLMsgType_MUSE_COMMON_XML_MSG_TYPE_UNSPECIFIED                             MuseCommonXMLMsgType = 0
	MuseCommonXMLMsgType_MUSE_COMMON_XML_MSG_TYPE_COMMON_MSG                              MuseCommonXMLMsgType = 1
	MuseCommonXMLMsgType_MUSE_COMMON_XML_MSG_TYPE_HOT_GAME_FLOW_WEIGHT_ENTER_CHANNEL_TIPS MuseCommonXMLMsgType = 2
)

var MuseCommonXMLMsgType_name = map[int32]string{
	0: "MUSE_COMMON_XML_MSG_TYPE_UNSPECIFIED",
	1: "MUSE_COMMON_XML_MSG_TYPE_COMMON_MSG",
	2: "MUSE_COMMON_XML_MSG_TYPE_HOT_GAME_FLOW_WEIGHT_ENTER_CHANNEL_TIPS",
}
var MuseCommonXMLMsgType_value = map[string]int32{
	"MUSE_COMMON_XML_MSG_TYPE_UNSPECIFIED":                             0,
	"MUSE_COMMON_XML_MSG_TYPE_COMMON_MSG":                              1,
	"MUSE_COMMON_XML_MSG_TYPE_HOT_GAME_FLOW_WEIGHT_ENTER_CHANNEL_TIPS": 2,
}

func (x MuseCommonXMLMsgType) String() string {
	return proto.EnumName(MuseCommonXMLMsgType_name, int32(x))
}
func (MuseCommonXMLMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{3}
}

type MuseCommonXMLMsgContentType int32

const (
	MuseCommonXMLMsgContentType_MUSE_COMMON_XML_MSG_CONTENT_TYPE_UNSPECIFIED                             MuseCommonXMLMsgContentType = 0
	MuseCommonXMLMsgContentType_MUSE_COMMON_XML_MSG_CONTENT_TYPE_HOT_GAME_SYSTEM_AWARD                   MuseCommonXMLMsgContentType = 1
	MuseCommonXMLMsgContentType_MUSE_COMMON_XML_MSG_CONTENT_TYPE_HOT_GAME_FLOW_WEIGHT_ENTER_CHANNEL_TIPS MuseCommonXMLMsgContentType = 2
	MuseCommonXMLMsgContentType_MUSE_COMMON_XML_MSG_CONTENT_TYPE_SOCIAL_COMMUNITY_LIKE_ACTIVITY          MuseCommonXMLMsgContentType = 3
)

var MuseCommonXMLMsgContentType_name = map[int32]string{
	0: "MUSE_COMMON_XML_MSG_CONTENT_TYPE_UNSPECIFIED",
	1: "MUSE_COMMON_XML_MSG_CONTENT_TYPE_HOT_GAME_SYSTEM_AWARD",
	2: "MUSE_COMMON_XML_MSG_CONTENT_TYPE_HOT_GAME_FLOW_WEIGHT_ENTER_CHANNEL_TIPS",
	3: "MUSE_COMMON_XML_MSG_CONTENT_TYPE_SOCIAL_COMMUNITY_LIKE_ACTIVITY",
}
var MuseCommonXMLMsgContentType_value = map[string]int32{
	"MUSE_COMMON_XML_MSG_CONTENT_TYPE_UNSPECIFIED":                             0,
	"MUSE_COMMON_XML_MSG_CONTENT_TYPE_HOT_GAME_SYSTEM_AWARD":                   1,
	"MUSE_COMMON_XML_MSG_CONTENT_TYPE_HOT_GAME_FLOW_WEIGHT_ENTER_CHANNEL_TIPS": 2,
	"MUSE_COMMON_XML_MSG_CONTENT_TYPE_SOCIAL_COMMUNITY_LIKE_ACTIVITY":          3,
}

func (x MuseCommonXMLMsgContentType) String() string {
	return proto.EnumName(MuseCommonXMLMsgContentType_name, int32(x))
}
func (MuseCommonXMLMsgContentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{4}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type CHANNEL_ADMIN_TYPE int32

const (
	CHANNEL_ADMIN_TYPE_CHANNEL_ADMIN_TYPE_UNSPECIFIED CHANNEL_ADMIN_TYPE = 0
	CHANNEL_ADMIN_TYPE_CHANNEL_ADMIN_TYPE_OWNER       CHANNEL_ADMIN_TYPE = 1
	CHANNEL_ADMIN_TYPE_CHANNEL_ADMIN_TYPE_ADMIN       CHANNEL_ADMIN_TYPE = 2
	CHANNEL_ADMIN_TYPE_CHANNEL_ADMIN_TYPE_NORMAL      CHANNEL_ADMIN_TYPE = 3
	CHANNEL_ADMIN_TYPE_CHANNEL_ADMIN_TYPE_SUPER       CHANNEL_ADMIN_TYPE = 4
	CHANNEL_ADMIN_TYPE_CHANNEL_ADMIN_TYPE_CAPTAIN     CHANNEL_ADMIN_TYPE = 5
)

var CHANNEL_ADMIN_TYPE_name = map[int32]string{
	0: "CHANNEL_ADMIN_TYPE_UNSPECIFIED",
	1: "CHANNEL_ADMIN_TYPE_OWNER",
	2: "CHANNEL_ADMIN_TYPE_ADMIN",
	3: "CHANNEL_ADMIN_TYPE_NORMAL",
	4: "CHANNEL_ADMIN_TYPE_SUPER",
	5: "CHANNEL_ADMIN_TYPE_CAPTAIN",
}
var CHANNEL_ADMIN_TYPE_value = map[string]int32{
	"CHANNEL_ADMIN_TYPE_UNSPECIFIED": 0,
	"CHANNEL_ADMIN_TYPE_OWNER":       1,
	"CHANNEL_ADMIN_TYPE_ADMIN":       2,
	"CHANNEL_ADMIN_TYPE_NORMAL":      3,
	"CHANNEL_ADMIN_TYPE_SUPER":       4,
	"CHANNEL_ADMIN_TYPE_CAPTAIN":     5,
}

func (x CHANNEL_ADMIN_TYPE) String() string {
	return proto.EnumName(CHANNEL_ADMIN_TYPE_name, int32(x))
}
func (CHANNEL_ADMIN_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{5}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type FOLLOW_STATUS_TYPE int32

const (
	FOLLOW_STATUS_TYPE_FOLLOW_STATUS_TYPE_UNSPECIFIED FOLLOW_STATUS_TYPE = 0
	FOLLOW_STATUS_TYPE_FOLLOW_STATUS_TYPE_FOLLOW      FOLLOW_STATUS_TYPE = 1
	FOLLOW_STATUS_TYPE_FOLLOW_STATUS_TYPE_FOLLOWED    FOLLOW_STATUS_TYPE = 2
)

var FOLLOW_STATUS_TYPE_name = map[int32]string{
	0: "FOLLOW_STATUS_TYPE_UNSPECIFIED",
	1: "FOLLOW_STATUS_TYPE_FOLLOW",
	2: "FOLLOW_STATUS_TYPE_FOLLOWED",
}
var FOLLOW_STATUS_TYPE_value = map[string]int32{
	"FOLLOW_STATUS_TYPE_UNSPECIFIED": 0,
	"FOLLOW_STATUS_TYPE_FOLLOW":      1,
	"FOLLOW_STATUS_TYPE_FOLLOWED":    2,
}

func (x FOLLOW_STATUS_TYPE) String() string {
	return proto.EnumName(FOLLOW_STATUS_TYPE_name, int32(x))
}
func (FOLLOW_STATUS_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{6}
}

type IceBreakerPushSource int32

const (
	IceBreakerPushSource_ICE_BREAKER_PUSH_SOURCE_UNSPECIFIED         IceBreakerPushSource = 0
	IceBreakerPushSource_ICE_BREAKER_PUSH_SOURCE_PUBLIC_CHANNEL      IceBreakerPushSource = 1
	IceBreakerPushSource_ICE_BREAKER_PUSH_SOURCE_AUDIT_REJECT        IceBreakerPushSource = 2
	IceBreakerPushSource_ICE_BREAKER_PUSH_SOURCE_OWNER_ENTER_CHANNEL IceBreakerPushSource = 3
)

var IceBreakerPushSource_name = map[int32]string{
	0: "ICE_BREAKER_PUSH_SOURCE_UNSPECIFIED",
	1: "ICE_BREAKER_PUSH_SOURCE_PUBLIC_CHANNEL",
	2: "ICE_BREAKER_PUSH_SOURCE_AUDIT_REJECT",
	3: "ICE_BREAKER_PUSH_SOURCE_OWNER_ENTER_CHANNEL",
}
var IceBreakerPushSource_value = map[string]int32{
	"ICE_BREAKER_PUSH_SOURCE_UNSPECIFIED":         0,
	"ICE_BREAKER_PUSH_SOURCE_PUBLIC_CHANNEL":      1,
	"ICE_BREAKER_PUSH_SOURCE_AUDIT_REJECT":        2,
	"ICE_BREAKER_PUSH_SOURCE_OWNER_ENTER_CHANNEL": 3,
}

func (x IceBreakerPushSource) String() string {
	return proto.EnumName(IceBreakerPushSource_name, int32(x))
}
func (IceBreakerPushSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{7}
}

type IceBreakerQuestionType int32

const (
	IceBreakerQuestionType_ICE_BREAKER_QUESTION_TYPE_UNSPECIFIED     IceBreakerQuestionType = 0
	IceBreakerQuestionType_ICE_BREAKER_QUESTION_TYPE_SYSTEM_QUESTION IceBreakerQuestionType = 1
	IceBreakerQuestionType_ICE_BREAKER_QUESTION_TYPE_CUSTOM_QUESTION IceBreakerQuestionType = 2
)

var IceBreakerQuestionType_name = map[int32]string{
	0: "ICE_BREAKER_QUESTION_TYPE_UNSPECIFIED",
	1: "ICE_BREAKER_QUESTION_TYPE_SYSTEM_QUESTION",
	2: "ICE_BREAKER_QUESTION_TYPE_CUSTOM_QUESTION",
}
var IceBreakerQuestionType_value = map[string]int32{
	"ICE_BREAKER_QUESTION_TYPE_UNSPECIFIED":     0,
	"ICE_BREAKER_QUESTION_TYPE_SYSTEM_QUESTION": 1,
	"ICE_BREAKER_QUESTION_TYPE_CUSTOM_QUESTION": 2,
}

func (x IceBreakerQuestionType) String() string {
	return proto.EnumName(IceBreakerQuestionType_name, int32(x))
}
func (IceBreakerQuestionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{8}
}

// Muse兴趣内容通用上报接口
type MuseCommonReportRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// Types that are valid to be assigned to ReportInfo:
	//	*MuseCommonReportRequest_UserLocationAuth_
	//	*MuseCommonReportRequest_UserStayInChannel_
	//	*MuseCommonReportRequest_UserOpenMicAndVoice_
	//	*MuseCommonReportRequest_UserLocationFlashChatCpToday_
	//	*MuseCommonReportRequest_SocialCommunityChannelTask_
	//	*MuseCommonReportRequest_SocialCommunityOpenMicTask_
	ReportInfo           isMuseCommonReportRequest_ReportInfo `protobuf_oneof:"report_info"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *MuseCommonReportRequest) Reset()         { *m = MuseCommonReportRequest{} }
func (m *MuseCommonReportRequest) String() string { return proto.CompactTextString(m) }
func (*MuseCommonReportRequest) ProtoMessage()    {}
func (*MuseCommonReportRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{0}
}
func (m *MuseCommonReportRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommonReportRequest.Unmarshal(m, b)
}
func (m *MuseCommonReportRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommonReportRequest.Marshal(b, m, deterministic)
}
func (dst *MuseCommonReportRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommonReportRequest.Merge(dst, src)
}
func (m *MuseCommonReportRequest) XXX_Size() int {
	return xxx_messageInfo_MuseCommonReportRequest.Size(m)
}
func (m *MuseCommonReportRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommonReportRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommonReportRequest proto.InternalMessageInfo

func (m *MuseCommonReportRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type isMuseCommonReportRequest_ReportInfo interface {
	isMuseCommonReportRequest_ReportInfo()
}

type MuseCommonReportRequest_UserLocationAuth_ struct {
	UserLocationAuth *MuseCommonReportRequest_UserLocationAuth `protobuf:"bytes,2,opt,name=user_location_auth,json=userLocationAuth,proto3,oneof"`
}

type MuseCommonReportRequest_UserStayInChannel_ struct {
	UserStayInChannel *MuseCommonReportRequest_UserStayInChannel `protobuf:"bytes,3,opt,name=user_stay_in_channel,json=userStayInChannel,proto3,oneof"`
}

type MuseCommonReportRequest_UserOpenMicAndVoice_ struct {
	UserOpenMicAndVoice *MuseCommonReportRequest_UserOpenMicAndVoice `protobuf:"bytes,4,opt,name=user_open_mic_and_voice,json=userOpenMicAndVoice,proto3,oneof"`
}

type MuseCommonReportRequest_UserLocationFlashChatCpToday_ struct {
	UserLocationFlashChatCpToday *MuseCommonReportRequest_UserLocationFlashChatCpToday `protobuf:"bytes,5,opt,name=user_location_flash_chat_cp_today,json=userLocationFlashChatCpToday,proto3,oneof"`
}

type MuseCommonReportRequest_SocialCommunityChannelTask_ struct {
	SocialCommunityChannelTask *MuseCommonReportRequest_SocialCommunityChannelTask `protobuf:"bytes,6,opt,name=social_community_channel_task,json=socialCommunityChannelTask,proto3,oneof"`
}

type MuseCommonReportRequest_SocialCommunityOpenMicTask_ struct {
	SocialCommunityOpenMicTask *MuseCommonReportRequest_SocialCommunityOpenMicTask `protobuf:"bytes,7,opt,name=social_community_open_mic_task,json=socialCommunityOpenMicTask,proto3,oneof"`
}

func (*MuseCommonReportRequest_UserLocationAuth_) isMuseCommonReportRequest_ReportInfo() {}

func (*MuseCommonReportRequest_UserStayInChannel_) isMuseCommonReportRequest_ReportInfo() {}

func (*MuseCommonReportRequest_UserOpenMicAndVoice_) isMuseCommonReportRequest_ReportInfo() {}

func (*MuseCommonReportRequest_UserLocationFlashChatCpToday_) isMuseCommonReportRequest_ReportInfo() {
}

func (*MuseCommonReportRequest_SocialCommunityChannelTask_) isMuseCommonReportRequest_ReportInfo() {}

func (*MuseCommonReportRequest_SocialCommunityOpenMicTask_) isMuseCommonReportRequest_ReportInfo() {}

func (m *MuseCommonReportRequest) GetReportInfo() isMuseCommonReportRequest_ReportInfo {
	if m != nil {
		return m.ReportInfo
	}
	return nil
}

func (m *MuseCommonReportRequest) GetUserLocationAuth() *MuseCommonReportRequest_UserLocationAuth {
	if x, ok := m.GetReportInfo().(*MuseCommonReportRequest_UserLocationAuth_); ok {
		return x.UserLocationAuth
	}
	return nil
}

func (m *MuseCommonReportRequest) GetUserStayInChannel() *MuseCommonReportRequest_UserStayInChannel {
	if x, ok := m.GetReportInfo().(*MuseCommonReportRequest_UserStayInChannel_); ok {
		return x.UserStayInChannel
	}
	return nil
}

func (m *MuseCommonReportRequest) GetUserOpenMicAndVoice() *MuseCommonReportRequest_UserOpenMicAndVoice {
	if x, ok := m.GetReportInfo().(*MuseCommonReportRequest_UserOpenMicAndVoice_); ok {
		return x.UserOpenMicAndVoice
	}
	return nil
}

func (m *MuseCommonReportRequest) GetUserLocationFlashChatCpToday() *MuseCommonReportRequest_UserLocationFlashChatCpToday {
	if x, ok := m.GetReportInfo().(*MuseCommonReportRequest_UserLocationFlashChatCpToday_); ok {
		return x.UserLocationFlashChatCpToday
	}
	return nil
}

func (m *MuseCommonReportRequest) GetSocialCommunityChannelTask() *MuseCommonReportRequest_SocialCommunityChannelTask {
	if x, ok := m.GetReportInfo().(*MuseCommonReportRequest_SocialCommunityChannelTask_); ok {
		return x.SocialCommunityChannelTask
	}
	return nil
}

func (m *MuseCommonReportRequest) GetSocialCommunityOpenMicTask() *MuseCommonReportRequest_SocialCommunityOpenMicTask {
	if x, ok := m.GetReportInfo().(*MuseCommonReportRequest_SocialCommunityOpenMicTask_); ok {
		return x.SocialCommunityOpenMicTask
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*MuseCommonReportRequest) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _MuseCommonReportRequest_OneofMarshaler, _MuseCommonReportRequest_OneofUnmarshaler, _MuseCommonReportRequest_OneofSizer, []interface{}{
		(*MuseCommonReportRequest_UserLocationAuth_)(nil),
		(*MuseCommonReportRequest_UserStayInChannel_)(nil),
		(*MuseCommonReportRequest_UserOpenMicAndVoice_)(nil),
		(*MuseCommonReportRequest_UserLocationFlashChatCpToday_)(nil),
		(*MuseCommonReportRequest_SocialCommunityChannelTask_)(nil),
		(*MuseCommonReportRequest_SocialCommunityOpenMicTask_)(nil),
	}
}

func _MuseCommonReportRequest_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*MuseCommonReportRequest)
	// report_info
	switch x := m.ReportInfo.(type) {
	case *MuseCommonReportRequest_UserLocationAuth_:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserLocationAuth); err != nil {
			return err
		}
	case *MuseCommonReportRequest_UserStayInChannel_:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserStayInChannel); err != nil {
			return err
		}
	case *MuseCommonReportRequest_UserOpenMicAndVoice_:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserOpenMicAndVoice); err != nil {
			return err
		}
	case *MuseCommonReportRequest_UserLocationFlashChatCpToday_:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserLocationFlashChatCpToday); err != nil {
			return err
		}
	case *MuseCommonReportRequest_SocialCommunityChannelTask_:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SocialCommunityChannelTask); err != nil {
			return err
		}
	case *MuseCommonReportRequest_SocialCommunityOpenMicTask_:
		b.EncodeVarint(7<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.SocialCommunityOpenMicTask); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("MuseCommonReportRequest.ReportInfo has unexpected type %T", x)
	}
	return nil
}

func _MuseCommonReportRequest_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*MuseCommonReportRequest)
	switch tag {
	case 2: // report_info.user_location_auth
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseCommonReportRequest_UserLocationAuth)
		err := b.DecodeMessage(msg)
		m.ReportInfo = &MuseCommonReportRequest_UserLocationAuth_{msg}
		return true, err
	case 3: // report_info.user_stay_in_channel
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseCommonReportRequest_UserStayInChannel)
		err := b.DecodeMessage(msg)
		m.ReportInfo = &MuseCommonReportRequest_UserStayInChannel_{msg}
		return true, err
	case 4: // report_info.user_open_mic_and_voice
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseCommonReportRequest_UserOpenMicAndVoice)
		err := b.DecodeMessage(msg)
		m.ReportInfo = &MuseCommonReportRequest_UserOpenMicAndVoice_{msg}
		return true, err
	case 5: // report_info.user_location_flash_chat_cp_today
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseCommonReportRequest_UserLocationFlashChatCpToday)
		err := b.DecodeMessage(msg)
		m.ReportInfo = &MuseCommonReportRequest_UserLocationFlashChatCpToday_{msg}
		return true, err
	case 6: // report_info.social_community_channel_task
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseCommonReportRequest_SocialCommunityChannelTask)
		err := b.DecodeMessage(msg)
		m.ReportInfo = &MuseCommonReportRequest_SocialCommunityChannelTask_{msg}
		return true, err
	case 7: // report_info.social_community_open_mic_task
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseCommonReportRequest_SocialCommunityOpenMicTask)
		err := b.DecodeMessage(msg)
		m.ReportInfo = &MuseCommonReportRequest_SocialCommunityOpenMicTask_{msg}
		return true, err
	default:
		return false, nil
	}
}

func _MuseCommonReportRequest_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*MuseCommonReportRequest)
	// report_info
	switch x := m.ReportInfo.(type) {
	case *MuseCommonReportRequest_UserLocationAuth_:
		s := proto.Size(x.UserLocationAuth)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseCommonReportRequest_UserStayInChannel_:
		s := proto.Size(x.UserStayInChannel)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseCommonReportRequest_UserOpenMicAndVoice_:
		s := proto.Size(x.UserOpenMicAndVoice)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseCommonReportRequest_UserLocationFlashChatCpToday_:
		s := proto.Size(x.UserLocationFlashChatCpToday)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseCommonReportRequest_SocialCommunityChannelTask_:
		s := proto.Size(x.SocialCommunityChannelTask)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseCommonReportRequest_SocialCommunityOpenMicTask_:
		s := proto.Size(x.SocialCommunityOpenMicTask)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type MuseCommonReportRequest_UserLocationAuth struct {
	IsOpenAuth           bool     `protobuf:"varint,1,opt,name=is_open_auth,json=isOpenAuth,proto3" json:"is_open_auth,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseCommonReportRequest_UserLocationAuth) Reset() {
	*m = MuseCommonReportRequest_UserLocationAuth{}
}
func (m *MuseCommonReportRequest_UserLocationAuth) String() string { return proto.CompactTextString(m) }
func (*MuseCommonReportRequest_UserLocationAuth) ProtoMessage()    {}
func (*MuseCommonReportRequest_UserLocationAuth) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{0, 0}
}
func (m *MuseCommonReportRequest_UserLocationAuth) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommonReportRequest_UserLocationAuth.Unmarshal(m, b)
}
func (m *MuseCommonReportRequest_UserLocationAuth) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommonReportRequest_UserLocationAuth.Marshal(b, m, deterministic)
}
func (dst *MuseCommonReportRequest_UserLocationAuth) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommonReportRequest_UserLocationAuth.Merge(dst, src)
}
func (m *MuseCommonReportRequest_UserLocationAuth) XXX_Size() int {
	return xxx_messageInfo_MuseCommonReportRequest_UserLocationAuth.Size(m)
}
func (m *MuseCommonReportRequest_UserLocationAuth) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommonReportRequest_UserLocationAuth.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommonReportRequest_UserLocationAuth proto.InternalMessageInfo

func (m *MuseCommonReportRequest_UserLocationAuth) GetIsOpenAuth() bool {
	if m != nil {
		return m.IsOpenAuth
	}
	return false
}

// 用户停留在房间内时长上报 客户端默认1分钟上报一次
type MuseCommonReportRequest_UserStayInChannel struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseCommonReportRequest_UserStayInChannel) Reset() {
	*m = MuseCommonReportRequest_UserStayInChannel{}
}
func (m *MuseCommonReportRequest_UserStayInChannel) String() string {
	return proto.CompactTextString(m)
}
func (*MuseCommonReportRequest_UserStayInChannel) ProtoMessage() {}
func (*MuseCommonReportRequest_UserStayInChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{0, 1}
}
func (m *MuseCommonReportRequest_UserStayInChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommonReportRequest_UserStayInChannel.Unmarshal(m, b)
}
func (m *MuseCommonReportRequest_UserStayInChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommonReportRequest_UserStayInChannel.Marshal(b, m, deterministic)
}
func (dst *MuseCommonReportRequest_UserStayInChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommonReportRequest_UserStayInChannel.Merge(dst, src)
}
func (m *MuseCommonReportRequest_UserStayInChannel) XXX_Size() int {
	return xxx_messageInfo_MuseCommonReportRequest_UserStayInChannel.Size(m)
}
func (m *MuseCommonReportRequest_UserStayInChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommonReportRequest_UserStayInChannel.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommonReportRequest_UserStayInChannel proto.InternalMessageInfo

func (m *MuseCommonReportRequest_UserStayInChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type MuseCommonReportRequest_UserOpenMicAndVoice struct {
	ChannelId            uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UserList             []*MuseMicUserInfo `protobuf:"bytes,2,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MuseCommonReportRequest_UserOpenMicAndVoice) Reset() {
	*m = MuseCommonReportRequest_UserOpenMicAndVoice{}
}
func (m *MuseCommonReportRequest_UserOpenMicAndVoice) String() string {
	return proto.CompactTextString(m)
}
func (*MuseCommonReportRequest_UserOpenMicAndVoice) ProtoMessage() {}
func (*MuseCommonReportRequest_UserOpenMicAndVoice) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{0, 2}
}
func (m *MuseCommonReportRequest_UserOpenMicAndVoice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommonReportRequest_UserOpenMicAndVoice.Unmarshal(m, b)
}
func (m *MuseCommonReportRequest_UserOpenMicAndVoice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommonReportRequest_UserOpenMicAndVoice.Marshal(b, m, deterministic)
}
func (dst *MuseCommonReportRequest_UserOpenMicAndVoice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommonReportRequest_UserOpenMicAndVoice.Merge(dst, src)
}
func (m *MuseCommonReportRequest_UserOpenMicAndVoice) XXX_Size() int {
	return xxx_messageInfo_MuseCommonReportRequest_UserOpenMicAndVoice.Size(m)
}
func (m *MuseCommonReportRequest_UserOpenMicAndVoice) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommonReportRequest_UserOpenMicAndVoice.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommonReportRequest_UserOpenMicAndVoice proto.InternalMessageInfo

func (m *MuseCommonReportRequest_UserOpenMicAndVoice) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseCommonReportRequest_UserOpenMicAndVoice) GetUserList() []*MuseMicUserInfo {
	if m != nil {
		return m.UserList
	}
	return nil
}

// 今日CP和即时闪聊定位数据上报
type MuseCommonReportRequest_UserLocationFlashChatCpToday struct {
	IsOpenAuth           bool     `protobuf:"varint,1,opt,name=is_open_auth,json=isOpenAuth,proto3" json:"is_open_auth,omitempty"`
	Longitude            float32  `protobuf:"fixed32,2,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Latitude             float32  `protobuf:"fixed32,3,opt,name=latitude,proto3" json:"latitude,omitempty"`
	City                 string   `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	Province             string   `protobuf:"bytes,5,opt,name=province,proto3" json:"province,omitempty"`
	LongitudeV2          float64  `protobuf:"fixed64,6,opt,name=longitude_v2,json=longitudeV2,proto3" json:"longitude_v2,omitempty"`
	LatitudeV2           float64  `protobuf:"fixed64,7,opt,name=latitude_v2,json=latitudeV2,proto3" json:"latitude_v2,omitempty"`
	IsOpenAppAuth        bool     `protobuf:"varint,8,opt,name=is_open_app_auth,json=isOpenAppAuth,proto3" json:"is_open_app_auth,omitempty"`
	Country              string   `protobuf:"bytes,9,opt,name=country,proto3" json:"country,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) Reset() {
	*m = MuseCommonReportRequest_UserLocationFlashChatCpToday{}
}
func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) String() string {
	return proto.CompactTextString(m)
}
func (*MuseCommonReportRequest_UserLocationFlashChatCpToday) ProtoMessage() {}
func (*MuseCommonReportRequest_UserLocationFlashChatCpToday) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{0, 3}
}
func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommonReportRequest_UserLocationFlashChatCpToday.Unmarshal(m, b)
}
func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommonReportRequest_UserLocationFlashChatCpToday.Marshal(b, m, deterministic)
}
func (dst *MuseCommonReportRequest_UserLocationFlashChatCpToday) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommonReportRequest_UserLocationFlashChatCpToday.Merge(dst, src)
}
func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) XXX_Size() int {
	return xxx_messageInfo_MuseCommonReportRequest_UserLocationFlashChatCpToday.Size(m)
}
func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommonReportRequest_UserLocationFlashChatCpToday.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommonReportRequest_UserLocationFlashChatCpToday proto.InternalMessageInfo

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) GetIsOpenAuth() bool {
	if m != nil {
		return m.IsOpenAuth
	}
	return false
}

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) GetLongitude() float32 {
	if m != nil {
		return m.Longitude
	}
	return 0
}

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) GetLatitude() float32 {
	if m != nil {
		return m.Latitude
	}
	return 0
}

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) GetLongitudeV2() float64 {
	if m != nil {
		return m.LongitudeV2
	}
	return 0
}

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) GetLatitudeV2() float64 {
	if m != nil {
		return m.LatitudeV2
	}
	return 0
}

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) GetIsOpenAppAuth() bool {
	if m != nil {
		return m.IsOpenAppAuth
	}
	return false
}

func (m *MuseCommonReportRequest_UserLocationFlashChatCpToday) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

// 社群的房间任务
type MuseCommonReportRequest_SocialCommunityChannelTask struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseCommonReportRequest_SocialCommunityChannelTask) Reset() {
	*m = MuseCommonReportRequest_SocialCommunityChannelTask{}
}
func (m *MuseCommonReportRequest_SocialCommunityChannelTask) String() string {
	return proto.CompactTextString(m)
}
func (*MuseCommonReportRequest_SocialCommunityChannelTask) ProtoMessage() {}
func (*MuseCommonReportRequest_SocialCommunityChannelTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{0, 4}
}
func (m *MuseCommonReportRequest_SocialCommunityChannelTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommonReportRequest_SocialCommunityChannelTask.Unmarshal(m, b)
}
func (m *MuseCommonReportRequest_SocialCommunityChannelTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommonReportRequest_SocialCommunityChannelTask.Marshal(b, m, deterministic)
}
func (dst *MuseCommonReportRequest_SocialCommunityChannelTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommonReportRequest_SocialCommunityChannelTask.Merge(dst, src)
}
func (m *MuseCommonReportRequest_SocialCommunityChannelTask) XXX_Size() int {
	return xxx_messageInfo_MuseCommonReportRequest_SocialCommunityChannelTask.Size(m)
}
func (m *MuseCommonReportRequest_SocialCommunityChannelTask) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommonReportRequest_SocialCommunityChannelTask.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommonReportRequest_SocialCommunityChannelTask proto.InternalMessageInfo

func (m *MuseCommonReportRequest_SocialCommunityChannelTask) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type MuseCommonReportRequest_SocialCommunityOpenMicTask struct {
	ChannelId            uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicUserList          []*MuseMicUserInfo `protobuf:"bytes,2,rep,name=mic_user_list,json=micUserList,proto3" json:"mic_user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MuseCommonReportRequest_SocialCommunityOpenMicTask) Reset() {
	*m = MuseCommonReportRequest_SocialCommunityOpenMicTask{}
}
func (m *MuseCommonReportRequest_SocialCommunityOpenMicTask) String() string {
	return proto.CompactTextString(m)
}
func (*MuseCommonReportRequest_SocialCommunityOpenMicTask) ProtoMessage() {}
func (*MuseCommonReportRequest_SocialCommunityOpenMicTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{0, 5}
}
func (m *MuseCommonReportRequest_SocialCommunityOpenMicTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommonReportRequest_SocialCommunityOpenMicTask.Unmarshal(m, b)
}
func (m *MuseCommonReportRequest_SocialCommunityOpenMicTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommonReportRequest_SocialCommunityOpenMicTask.Marshal(b, m, deterministic)
}
func (dst *MuseCommonReportRequest_SocialCommunityOpenMicTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommonReportRequest_SocialCommunityOpenMicTask.Merge(dst, src)
}
func (m *MuseCommonReportRequest_SocialCommunityOpenMicTask) XXX_Size() int {
	return xxx_messageInfo_MuseCommonReportRequest_SocialCommunityOpenMicTask.Size(m)
}
func (m *MuseCommonReportRequest_SocialCommunityOpenMicTask) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommonReportRequest_SocialCommunityOpenMicTask.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommonReportRequest_SocialCommunityOpenMicTask proto.InternalMessageInfo

func (m *MuseCommonReportRequest_SocialCommunityOpenMicTask) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseCommonReportRequest_SocialCommunityOpenMicTask) GetMicUserList() []*MuseMicUserInfo {
	if m != nil {
		return m.MicUserList
	}
	return nil
}

type MuseMicUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OpenMic              bool     `protobuf:"varint,2,opt,name=open_mic,json=openMic,proto3" json:"open_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseMicUserInfo) Reset()         { *m = MuseMicUserInfo{} }
func (m *MuseMicUserInfo) String() string { return proto.CompactTextString(m) }
func (*MuseMicUserInfo) ProtoMessage()    {}
func (*MuseMicUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{1}
}
func (m *MuseMicUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseMicUserInfo.Unmarshal(m, b)
}
func (m *MuseMicUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseMicUserInfo.Marshal(b, m, deterministic)
}
func (dst *MuseMicUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseMicUserInfo.Merge(dst, src)
}
func (m *MuseMicUserInfo) XXX_Size() int {
	return xxx_messageInfo_MuseMicUserInfo.Size(m)
}
func (m *MuseMicUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseMicUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseMicUserInfo proto.InternalMessageInfo

func (m *MuseMicUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MuseMicUserInfo) GetOpenMic() bool {
	if m != nil {
		return m.OpenMic
	}
	return false
}

type MuseCommonReportResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsStopReport         bool          `protobuf:"varint,2,opt,name=is_stop_report,json=isStopReport,proto3" json:"is_stop_report,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseCommonReportResponse) Reset()         { *m = MuseCommonReportResponse{} }
func (m *MuseCommonReportResponse) String() string { return proto.CompactTextString(m) }
func (*MuseCommonReportResponse) ProtoMessage()    {}
func (*MuseCommonReportResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{2}
}
func (m *MuseCommonReportResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommonReportResponse.Unmarshal(m, b)
}
func (m *MuseCommonReportResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommonReportResponse.Marshal(b, m, deterministic)
}
func (dst *MuseCommonReportResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommonReportResponse.Merge(dst, src)
}
func (m *MuseCommonReportResponse) XXX_Size() int {
	return xxx_messageInfo_MuseCommonReportResponse.Size(m)
}
func (m *MuseCommonReportResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommonReportResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommonReportResponse proto.InternalMessageInfo

func (m *MuseCommonReportResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseCommonReportResponse) GetIsStopReport() bool {
	if m != nil {
		return m.IsStopReport
	}
	return false
}

// 兴趣内容相关 开关合集
type GetMuseSwitchHubRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMuseSwitchHubRequest) Reset()         { *m = GetMuseSwitchHubRequest{} }
func (m *GetMuseSwitchHubRequest) String() string { return proto.CompactTextString(m) }
func (*GetMuseSwitchHubRequest) ProtoMessage()    {}
func (*GetMuseSwitchHubRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{3}
}
func (m *GetMuseSwitchHubRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseSwitchHubRequest.Unmarshal(m, b)
}
func (m *GetMuseSwitchHubRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseSwitchHubRequest.Marshal(b, m, deterministic)
}
func (dst *GetMuseSwitchHubRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseSwitchHubRequest.Merge(dst, src)
}
func (m *GetMuseSwitchHubRequest) XXX_Size() int {
	return xxx_messageInfo_GetMuseSwitchHubRequest.Size(m)
}
func (m *GetMuseSwitchHubRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseSwitchHubRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseSwitchHubRequest proto.InternalMessageInfo

func (m *GetMuseSwitchHubRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetMuseSwitchHubResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsOpenMap            map[uint32]bool `protobuf:"bytes,2,rep,name=is_open_map,json=isOpenMap,proto3" json:"is_open_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetMuseSwitchHubResponse) Reset()         { *m = GetMuseSwitchHubResponse{} }
func (m *GetMuseSwitchHubResponse) String() string { return proto.CompactTextString(m) }
func (*GetMuseSwitchHubResponse) ProtoMessage()    {}
func (*GetMuseSwitchHubResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{4}
}
func (m *GetMuseSwitchHubResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseSwitchHubResponse.Unmarshal(m, b)
}
func (m *GetMuseSwitchHubResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseSwitchHubResponse.Marshal(b, m, deterministic)
}
func (dst *GetMuseSwitchHubResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseSwitchHubResponse.Merge(dst, src)
}
func (m *GetMuseSwitchHubResponse) XXX_Size() int {
	return xxx_messageInfo_GetMuseSwitchHubResponse.Size(m)
}
func (m *GetMuseSwitchHubResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseSwitchHubResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseSwitchHubResponse proto.InternalMessageInfo

func (m *GetMuseSwitchHubResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMuseSwitchHubResponse) GetIsOpenMap() map[uint32]bool {
	if m != nil {
		return m.IsOpenMap
	}
	return nil
}

type SetMuseSwitchHubRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SwitchType           uint32       `protobuf:"varint,2,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	IsOpen               bool         `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetMuseSwitchHubRequest) Reset()         { *m = SetMuseSwitchHubRequest{} }
func (m *SetMuseSwitchHubRequest) String() string { return proto.CompactTextString(m) }
func (*SetMuseSwitchHubRequest) ProtoMessage()    {}
func (*SetMuseSwitchHubRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{5}
}
func (m *SetMuseSwitchHubRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMuseSwitchHubRequest.Unmarshal(m, b)
}
func (m *SetMuseSwitchHubRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMuseSwitchHubRequest.Marshal(b, m, deterministic)
}
func (dst *SetMuseSwitchHubRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMuseSwitchHubRequest.Merge(dst, src)
}
func (m *SetMuseSwitchHubRequest) XXX_Size() int {
	return xxx_messageInfo_SetMuseSwitchHubRequest.Size(m)
}
func (m *SetMuseSwitchHubRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMuseSwitchHubRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetMuseSwitchHubRequest proto.InternalMessageInfo

func (m *SetMuseSwitchHubRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetMuseSwitchHubRequest) GetSwitchType() uint32 {
	if m != nil {
		return m.SwitchType
	}
	return 0
}

func (m *SetMuseSwitchHubRequest) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetMuseSwitchHubResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetMuseSwitchHubResponse) Reset()         { *m = SetMuseSwitchHubResponse{} }
func (m *SetMuseSwitchHubResponse) String() string { return proto.CompactTextString(m) }
func (*SetMuseSwitchHubResponse) ProtoMessage()    {}
func (*SetMuseSwitchHubResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{6}
}
func (m *SetMuseSwitchHubResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMuseSwitchHubResponse.Unmarshal(m, b)
}
func (m *SetMuseSwitchHubResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMuseSwitchHubResponse.Marshal(b, m, deterministic)
}
func (dst *SetMuseSwitchHubResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMuseSwitchHubResponse.Merge(dst, src)
}
func (m *SetMuseSwitchHubResponse) XXX_Size() int {
	return xxx_messageInfo_SetMuseSwitchHubResponse.Size(m)
}
func (m *SetMuseSwitchHubResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMuseSwitchHubResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetMuseSwitchHubResponse proto.InternalMessageInfo

func (m *SetMuseSwitchHubResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 麦上用户城市信息
type UserCityInChannel struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CityName             string   `protobuf:"bytes,2,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserCityInChannel) Reset()         { *m = UserCityInChannel{} }
func (m *UserCityInChannel) String() string { return proto.CompactTextString(m) }
func (*UserCityInChannel) ProtoMessage()    {}
func (*UserCityInChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{7}
}
func (m *UserCityInChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCityInChannel.Unmarshal(m, b)
}
func (m *UserCityInChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCityInChannel.Marshal(b, m, deterministic)
}
func (dst *UserCityInChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCityInChannel.Merge(dst, src)
}
func (m *UserCityInChannel) XXX_Size() int {
	return xxx_messageInfo_UserCityInChannel.Size(m)
}
func (m *UserCityInChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCityInChannel.DiscardUnknown(m)
}

var xxx_messageInfo_UserCityInChannel proto.InternalMessageInfo

func (m *UserCityInChannel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserCityInChannel) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

type TopicChannelSameCityInfo struct {
	CityName             string               `protobuf:"bytes,1,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	UserCityList         []*UserCityInChannel `protobuf:"bytes,2,rep,name=user_city_list,json=userCityList,proto3" json:"user_city_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *TopicChannelSameCityInfo) Reset()         { *m = TopicChannelSameCityInfo{} }
func (m *TopicChannelSameCityInfo) String() string { return proto.CompactTextString(m) }
func (*TopicChannelSameCityInfo) ProtoMessage()    {}
func (*TopicChannelSameCityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{8}
}
func (m *TopicChannelSameCityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicChannelSameCityInfo.Unmarshal(m, b)
}
func (m *TopicChannelSameCityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicChannelSameCityInfo.Marshal(b, m, deterministic)
}
func (dst *TopicChannelSameCityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicChannelSameCityInfo.Merge(dst, src)
}
func (m *TopicChannelSameCityInfo) XXX_Size() int {
	return xxx_messageInfo_TopicChannelSameCityInfo.Size(m)
}
func (m *TopicChannelSameCityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicChannelSameCityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicChannelSameCityInfo proto.InternalMessageInfo

func (m *TopicChannelSameCityInfo) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *TopicChannelSameCityInfo) GetUserCityList() []*UserCityInChannel {
	if m != nil {
		return m.UserCityList
	}
	return nil
}

type CheckJumpSquarePageRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckJumpSquarePageRequest) Reset()         { *m = CheckJumpSquarePageRequest{} }
func (m *CheckJumpSquarePageRequest) String() string { return proto.CompactTextString(m) }
func (*CheckJumpSquarePageRequest) ProtoMessage()    {}
func (*CheckJumpSquarePageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{9}
}
func (m *CheckJumpSquarePageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckJumpSquarePageRequest.Unmarshal(m, b)
}
func (m *CheckJumpSquarePageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckJumpSquarePageRequest.Marshal(b, m, deterministic)
}
func (dst *CheckJumpSquarePageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckJumpSquarePageRequest.Merge(dst, src)
}
func (m *CheckJumpSquarePageRequest) XXX_Size() int {
	return xxx_messageInfo_CheckJumpSquarePageRequest.Size(m)
}
func (m *CheckJumpSquarePageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckJumpSquarePageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckJumpSquarePageRequest proto.InternalMessageInfo

func (m *CheckJumpSquarePageRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckJumpSquarePageRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckJumpSquarePageResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NeedJump             bool          `protobuf:"varint,2,opt,name=need_jump,json=needJump,proto3" json:"need_jump,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckJumpSquarePageResponse) Reset()         { *m = CheckJumpSquarePageResponse{} }
func (m *CheckJumpSquarePageResponse) String() string { return proto.CompactTextString(m) }
func (*CheckJumpSquarePageResponse) ProtoMessage()    {}
func (*CheckJumpSquarePageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{10}
}
func (m *CheckJumpSquarePageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckJumpSquarePageResponse.Unmarshal(m, b)
}
func (m *CheckJumpSquarePageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckJumpSquarePageResponse.Marshal(b, m, deterministic)
}
func (dst *CheckJumpSquarePageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckJumpSquarePageResponse.Merge(dst, src)
}
func (m *CheckJumpSquarePageResponse) XXX_Size() int {
	return xxx_messageInfo_CheckJumpSquarePageResponse.Size(m)
}
func (m *CheckJumpSquarePageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckJumpSquarePageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckJumpSquarePageResponse proto.InternalMessageInfo

func (m *CheckJumpSquarePageResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckJumpSquarePageResponse) GetNeedJump() bool {
	if m != nil {
		return m.NeedJump
	}
	return false
}

// 获取MT实验策略内容
type GetMTExperimentStrategyRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	StrategyList         []uint32     `protobuf:"varint,2,rep,packed,name=strategy_list,json=strategyList,proto3" json:"strategy_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMTExperimentStrategyRequest) Reset()         { *m = GetMTExperimentStrategyRequest{} }
func (m *GetMTExperimentStrategyRequest) String() string { return proto.CompactTextString(m) }
func (*GetMTExperimentStrategyRequest) ProtoMessage()    {}
func (*GetMTExperimentStrategyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{11}
}
func (m *GetMTExperimentStrategyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMTExperimentStrategyRequest.Unmarshal(m, b)
}
func (m *GetMTExperimentStrategyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMTExperimentStrategyRequest.Marshal(b, m, deterministic)
}
func (dst *GetMTExperimentStrategyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMTExperimentStrategyRequest.Merge(dst, src)
}
func (m *GetMTExperimentStrategyRequest) XXX_Size() int {
	return xxx_messageInfo_GetMTExperimentStrategyRequest.Size(m)
}
func (m *GetMTExperimentStrategyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMTExperimentStrategyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMTExperimentStrategyRequest proto.InternalMessageInfo

func (m *GetMTExperimentStrategyRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMTExperimentStrategyRequest) GetStrategyList() []uint32 {
	if m != nil {
		return m.StrategyList
	}
	return nil
}

type GetMTExperimentStrategyResponse struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	StrategyValueMap     map[uint32]uint32 `protobuf:"bytes,2,rep,name=strategy_value_map,json=strategyValueMap,proto3" json:"strategy_value_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	NextUpdateInternal   uint32            `protobuf:"varint,3,opt,name=next_update_internal,json=nextUpdateInternal,proto3" json:"next_update_internal,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetMTExperimentStrategyResponse) Reset()         { *m = GetMTExperimentStrategyResponse{} }
func (m *GetMTExperimentStrategyResponse) String() string { return proto.CompactTextString(m) }
func (*GetMTExperimentStrategyResponse) ProtoMessage()    {}
func (*GetMTExperimentStrategyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{12}
}
func (m *GetMTExperimentStrategyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMTExperimentStrategyResponse.Unmarshal(m, b)
}
func (m *GetMTExperimentStrategyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMTExperimentStrategyResponse.Marshal(b, m, deterministic)
}
func (dst *GetMTExperimentStrategyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMTExperimentStrategyResponse.Merge(dst, src)
}
func (m *GetMTExperimentStrategyResponse) XXX_Size() int {
	return xxx_messageInfo_GetMTExperimentStrategyResponse.Size(m)
}
func (m *GetMTExperimentStrategyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMTExperimentStrategyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMTExperimentStrategyResponse proto.InternalMessageInfo

func (m *GetMTExperimentStrategyResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMTExperimentStrategyResponse) GetStrategyValueMap() map[uint32]uint32 {
	if m != nil {
		return m.StrategyValueMap
	}
	return nil
}

func (m *GetMTExperimentStrategyResponse) GetNextUpdateInternal() uint32 {
	if m != nil {
		return m.NextUpdateInternal
	}
	return 0
}

// 获取优先展示的首页跟随进房信息
type GetHomeFollowFloatRequest struct {
	BaseReq              *app.BaseReq               `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	UidList              []*FollowUserChannelInfo   `protobuf:"bytes,2,rep,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"` // Deprecated: Do not use.
	UidListV2            []*FollowUserChannelInfoV2 `protobuf:"bytes,3,rep,name=uid_list_v2,json=uidListV2,proto3" json:"uid_list_v2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetHomeFollowFloatRequest) Reset()         { *m = GetHomeFollowFloatRequest{} }
func (m *GetHomeFollowFloatRequest) String() string { return proto.CompactTextString(m) }
func (*GetHomeFollowFloatRequest) ProtoMessage()    {}
func (*GetHomeFollowFloatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{13}
}
func (m *GetHomeFollowFloatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomeFollowFloatRequest.Unmarshal(m, b)
}
func (m *GetHomeFollowFloatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomeFollowFloatRequest.Marshal(b, m, deterministic)
}
func (dst *GetHomeFollowFloatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomeFollowFloatRequest.Merge(dst, src)
}
func (m *GetHomeFollowFloatRequest) XXX_Size() int {
	return xxx_messageInfo_GetHomeFollowFloatRequest.Size(m)
}
func (m *GetHomeFollowFloatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomeFollowFloatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomeFollowFloatRequest proto.InternalMessageInfo

func (m *GetHomeFollowFloatRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// Deprecated: Do not use.
func (m *GetHomeFollowFloatRequest) GetUidList() []*FollowUserChannelInfo {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetHomeFollowFloatRequest) GetUidListV2() []*FollowUserChannelInfoV2 {
	if m != nil {
		return m.UidListV2
	}
	return nil
}

type UserSimpleInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Gender               uint32   `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`
	FollowStatus         uint32   `protobuf:"varint,5,opt,name=follow_status,json=followStatus,proto3" json:"follow_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSimpleInfo) Reset()         { *m = UserSimpleInfo{} }
func (m *UserSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*UserSimpleInfo) ProtoMessage()    {}
func (*UserSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{14}
}
func (m *UserSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSimpleInfo.Unmarshal(m, b)
}
func (m *UserSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *UserSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSimpleInfo.Merge(dst, src)
}
func (m *UserSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_UserSimpleInfo.Size(m)
}
func (m *UserSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSimpleInfo proto.InternalMessageInfo

func (m *UserSimpleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserSimpleInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserSimpleInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserSimpleInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *UserSimpleInfo) GetFollowStatus() uint32 {
	if m != nil {
		return m.FollowStatus
	}
	return 0
}

type GetHomeFollowFloatResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	User                 *UserSimpleInfo `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	FindPlayingText      string          `protobuf:"bytes,3,opt,name=find_playing_text,json=findPlayingText,proto3" json:"find_playing_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetHomeFollowFloatResponse) Reset()         { *m = GetHomeFollowFloatResponse{} }
func (m *GetHomeFollowFloatResponse) String() string { return proto.CompactTextString(m) }
func (*GetHomeFollowFloatResponse) ProtoMessage()    {}
func (*GetHomeFollowFloatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{15}
}
func (m *GetHomeFollowFloatResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHomeFollowFloatResponse.Unmarshal(m, b)
}
func (m *GetHomeFollowFloatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHomeFollowFloatResponse.Marshal(b, m, deterministic)
}
func (dst *GetHomeFollowFloatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHomeFollowFloatResponse.Merge(dst, src)
}
func (m *GetHomeFollowFloatResponse) XXX_Size() int {
	return xxx_messageInfo_GetHomeFollowFloatResponse.Size(m)
}
func (m *GetHomeFollowFloatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHomeFollowFloatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHomeFollowFloatResponse proto.InternalMessageInfo

func (m *GetHomeFollowFloatResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetHomeFollowFloatResponse) GetUser() *UserSimpleInfo {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *GetHomeFollowFloatResponse) GetFindPlayingText() string {
	if m != nil {
		return m.FindPlayingText
	}
	return ""
}

type FollowUserChannelInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            string   `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FollowUserChannelInfo) Reset()         { *m = FollowUserChannelInfo{} }
func (m *FollowUserChannelInfo) String() string { return proto.CompactTextString(m) }
func (*FollowUserChannelInfo) ProtoMessage()    {}
func (*FollowUserChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{16}
}
func (m *FollowUserChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowUserChannelInfo.Unmarshal(m, b)
}
func (m *FollowUserChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowUserChannelInfo.Marshal(b, m, deterministic)
}
func (dst *FollowUserChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowUserChannelInfo.Merge(dst, src)
}
func (m *FollowUserChannelInfo) XXX_Size() int {
	return xxx_messageInfo_FollowUserChannelInfo.Size(m)
}
func (m *FollowUserChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowUserChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FollowUserChannelInfo proto.InternalMessageInfo

func (m *FollowUserChannelInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FollowUserChannelInfo) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

type FollowUserChannelInfoV2 struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FollowUserChannelInfoV2) Reset()         { *m = FollowUserChannelInfoV2{} }
func (m *FollowUserChannelInfoV2) String() string { return proto.CompactTextString(m) }
func (*FollowUserChannelInfoV2) ProtoMessage()    {}
func (*FollowUserChannelInfoV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{17}
}
func (m *FollowUserChannelInfoV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowUserChannelInfoV2.Unmarshal(m, b)
}
func (m *FollowUserChannelInfoV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowUserChannelInfoV2.Marshal(b, m, deterministic)
}
func (dst *FollowUserChannelInfoV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowUserChannelInfoV2.Merge(dst, src)
}
func (m *FollowUserChannelInfoV2) XXX_Size() int {
	return xxx_messageInfo_FollowUserChannelInfoV2.Size(m)
}
func (m *FollowUserChannelInfoV2) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowUserChannelInfoV2.DiscardUnknown(m)
}

var xxx_messageInfo_FollowUserChannelInfoV2 proto.InternalMessageInfo

func (m *FollowUserChannelInfoV2) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FollowUserChannelInfoV2) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 跟随进房半屏页用户列表
type FollowChannelHalfScreenUserListRequest struct {
	BaseReq              *app.BaseReq               `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	UidList              []*FollowUserChannelInfo   `protobuf:"bytes,2,rep,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"` // Deprecated: Do not use.
	UidListV2            []*FollowUserChannelInfoV2 `protobuf:"bytes,3,rep,name=uid_list_v2,json=uidListV2,proto3" json:"uid_list_v2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *FollowChannelHalfScreenUserListRequest) Reset() {
	*m = FollowChannelHalfScreenUserListRequest{}
}
func (m *FollowChannelHalfScreenUserListRequest) String() string { return proto.CompactTextString(m) }
func (*FollowChannelHalfScreenUserListRequest) ProtoMessage()    {}
func (*FollowChannelHalfScreenUserListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{18}
}
func (m *FollowChannelHalfScreenUserListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowChannelHalfScreenUserListRequest.Unmarshal(m, b)
}
func (m *FollowChannelHalfScreenUserListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowChannelHalfScreenUserListRequest.Marshal(b, m, deterministic)
}
func (dst *FollowChannelHalfScreenUserListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowChannelHalfScreenUserListRequest.Merge(dst, src)
}
func (m *FollowChannelHalfScreenUserListRequest) XXX_Size() int {
	return xxx_messageInfo_FollowChannelHalfScreenUserListRequest.Size(m)
}
func (m *FollowChannelHalfScreenUserListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowChannelHalfScreenUserListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FollowChannelHalfScreenUserListRequest proto.InternalMessageInfo

func (m *FollowChannelHalfScreenUserListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// Deprecated: Do not use.
func (m *FollowChannelHalfScreenUserListRequest) GetUidList() []*FollowUserChannelInfo {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *FollowChannelHalfScreenUserListRequest) GetUidListV2() []*FollowUserChannelInfoV2 {
	if m != nil {
		return m.UidListV2
	}
	return nil
}

// 自定义认证
type InterestHubPersonalCert struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Color                []string `protobuf:"bytes,3,rep,name=color,proto3" json:"color,omitempty"`
	TextShadowColor      string   `protobuf:"bytes,4,opt,name=text_shadow_color,json=textShadowColor,proto3" json:"text_shadow_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InterestHubPersonalCert) Reset()         { *m = InterestHubPersonalCert{} }
func (m *InterestHubPersonalCert) String() string { return proto.CompactTextString(m) }
func (*InterestHubPersonalCert) ProtoMessage()    {}
func (*InterestHubPersonalCert) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{19}
}
func (m *InterestHubPersonalCert) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InterestHubPersonalCert.Unmarshal(m, b)
}
func (m *InterestHubPersonalCert) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InterestHubPersonalCert.Marshal(b, m, deterministic)
}
func (dst *InterestHubPersonalCert) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InterestHubPersonalCert.Merge(dst, src)
}
func (m *InterestHubPersonalCert) XXX_Size() int {
	return xxx_messageInfo_InterestHubPersonalCert.Size(m)
}
func (m *InterestHubPersonalCert) XXX_DiscardUnknown() {
	xxx_messageInfo_InterestHubPersonalCert.DiscardUnknown(m)
}

var xxx_messageInfo_InterestHubPersonalCert proto.InternalMessageInfo

func (m *InterestHubPersonalCert) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *InterestHubPersonalCert) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *InterestHubPersonalCert) GetColor() []string {
	if m != nil {
		return m.Color
	}
	return nil
}

func (m *InterestHubPersonalCert) GetTextShadowColor() string {
	if m != nil {
		return m.TextShadowColor
	}
	return ""
}

// 房间玩法信息
type FollowFriendsChannelSimpleInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelTabIcon       string   `protobuf:"bytes,3,opt,name=channel_tab_icon,json=channelTabIcon,proto3" json:"channel_tab_icon,omitempty"`
	ChannelTabText       string   `protobuf:"bytes,4,opt,name=channel_tab_text,json=channelTabText,proto3" json:"channel_tab_text,omitempty"`
	MemberCount          uint32   `protobuf:"varint,5,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	IsMemberCountRed     bool     `protobuf:"varint,6,opt,name=is_member_count_red,json=isMemberCountRed,proto3" json:"is_member_count_red,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FollowFriendsChannelSimpleInfo) Reset()         { *m = FollowFriendsChannelSimpleInfo{} }
func (m *FollowFriendsChannelSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*FollowFriendsChannelSimpleInfo) ProtoMessage()    {}
func (*FollowFriendsChannelSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{20}
}
func (m *FollowFriendsChannelSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowFriendsChannelSimpleInfo.Unmarshal(m, b)
}
func (m *FollowFriendsChannelSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowFriendsChannelSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *FollowFriendsChannelSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowFriendsChannelSimpleInfo.Merge(dst, src)
}
func (m *FollowFriendsChannelSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_FollowFriendsChannelSimpleInfo.Size(m)
}
func (m *FollowFriendsChannelSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowFriendsChannelSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FollowFriendsChannelSimpleInfo proto.InternalMessageInfo

func (m *FollowFriendsChannelSimpleInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FollowFriendsChannelSimpleInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *FollowFriendsChannelSimpleInfo) GetChannelTabIcon() string {
	if m != nil {
		return m.ChannelTabIcon
	}
	return ""
}

func (m *FollowFriendsChannelSimpleInfo) GetChannelTabText() string {
	if m != nil {
		return m.ChannelTabText
	}
	return ""
}

func (m *FollowFriendsChannelSimpleInfo) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *FollowFriendsChannelSimpleInfo) GetIsMemberCountRed() bool {
	if m != nil {
		return m.IsMemberCountRed
	}
	return false
}

type FriendRcmdInfo struct {
	RcmdText             string   `protobuf:"bytes,1,opt,name=rcmd_text,json=rcmdText,proto3" json:"rcmd_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FriendRcmdInfo) Reset()         { *m = FriendRcmdInfo{} }
func (m *FriendRcmdInfo) String() string { return proto.CompactTextString(m) }
func (*FriendRcmdInfo) ProtoMessage()    {}
func (*FriendRcmdInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{21}
}
func (m *FriendRcmdInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FriendRcmdInfo.Unmarshal(m, b)
}
func (m *FriendRcmdInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FriendRcmdInfo.Marshal(b, m, deterministic)
}
func (dst *FriendRcmdInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FriendRcmdInfo.Merge(dst, src)
}
func (m *FriendRcmdInfo) XXX_Size() int {
	return xxx_messageInfo_FriendRcmdInfo.Size(m)
}
func (m *FriendRcmdInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FriendRcmdInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FriendRcmdInfo proto.InternalMessageInfo

func (m *FriendRcmdInfo) GetRcmdText() string {
	if m != nil {
		return m.RcmdText
	}
	return ""
}

type UserSimpleMegaphoneInfo struct {
	MegaphoneId          string   `protobuf:"bytes,1,opt,name=megaphone_id,json=megaphoneId,proto3" json:"megaphone_id,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSimpleMegaphoneInfo) Reset()         { *m = UserSimpleMegaphoneInfo{} }
func (m *UserSimpleMegaphoneInfo) String() string { return proto.CompactTextString(m) }
func (*UserSimpleMegaphoneInfo) ProtoMessage()    {}
func (*UserSimpleMegaphoneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{22}
}
func (m *UserSimpleMegaphoneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSimpleMegaphoneInfo.Unmarshal(m, b)
}
func (m *UserSimpleMegaphoneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSimpleMegaphoneInfo.Marshal(b, m, deterministic)
}
func (dst *UserSimpleMegaphoneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSimpleMegaphoneInfo.Merge(dst, src)
}
func (m *UserSimpleMegaphoneInfo) XXX_Size() int {
	return xxx_messageInfo_UserSimpleMegaphoneInfo.Size(m)
}
func (m *UserSimpleMegaphoneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSimpleMegaphoneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserSimpleMegaphoneInfo proto.InternalMessageInfo

func (m *UserSimpleMegaphoneInfo) GetMegaphoneId() string {
	if m != nil {
		return m.MegaphoneId
	}
	return ""
}

func (m *UserSimpleMegaphoneInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type FollowChannelHalfScreenUserList struct {
	User                 *UserSimpleInfo                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	PersonalCert         *InterestHubPersonalCert        `protobuf:"bytes,2,opt,name=personal_cert,json=personalCert,proto3" json:"personal_cert,omitempty"`
	ChannelInfo          *FollowFriendsChannelSimpleInfo `protobuf:"bytes,3,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	RcmdInfo             *FriendRcmdInfo                 `protobuf:"bytes,4,opt,name=rcmd_info,json=rcmdInfo,proto3" json:"rcmd_info,omitempty"`
	UserMegaphoneInfo    *UserSimpleMegaphoneInfo        `protobuf:"bytes,5,opt,name=user_megaphone_info,json=userMegaphoneInfo,proto3" json:"user_megaphone_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *FollowChannelHalfScreenUserList) Reset()         { *m = FollowChannelHalfScreenUserList{} }
func (m *FollowChannelHalfScreenUserList) String() string { return proto.CompactTextString(m) }
func (*FollowChannelHalfScreenUserList) ProtoMessage()    {}
func (*FollowChannelHalfScreenUserList) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{23}
}
func (m *FollowChannelHalfScreenUserList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowChannelHalfScreenUserList.Unmarshal(m, b)
}
func (m *FollowChannelHalfScreenUserList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowChannelHalfScreenUserList.Marshal(b, m, deterministic)
}
func (dst *FollowChannelHalfScreenUserList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowChannelHalfScreenUserList.Merge(dst, src)
}
func (m *FollowChannelHalfScreenUserList) XXX_Size() int {
	return xxx_messageInfo_FollowChannelHalfScreenUserList.Size(m)
}
func (m *FollowChannelHalfScreenUserList) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowChannelHalfScreenUserList.DiscardUnknown(m)
}

var xxx_messageInfo_FollowChannelHalfScreenUserList proto.InternalMessageInfo

func (m *FollowChannelHalfScreenUserList) GetUser() *UserSimpleInfo {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *FollowChannelHalfScreenUserList) GetPersonalCert() *InterestHubPersonalCert {
	if m != nil {
		return m.PersonalCert
	}
	return nil
}

func (m *FollowChannelHalfScreenUserList) GetChannelInfo() *FollowFriendsChannelSimpleInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *FollowChannelHalfScreenUserList) GetRcmdInfo() *FriendRcmdInfo {
	if m != nil {
		return m.RcmdInfo
	}
	return nil
}

func (m *FollowChannelHalfScreenUserList) GetUserMegaphoneInfo() *UserSimpleMegaphoneInfo {
	if m != nil {
		return m.UserMegaphoneInfo
	}
	return nil
}

type FollowChannelHalfScreenUserListResponse struct {
	BaseResp             *app.BaseResp                      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserList             []*FollowChannelHalfScreenUserList `protobuf:"bytes,2,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *FollowChannelHalfScreenUserListResponse) Reset() {
	*m = FollowChannelHalfScreenUserListResponse{}
}
func (m *FollowChannelHalfScreenUserListResponse) String() string { return proto.CompactTextString(m) }
func (*FollowChannelHalfScreenUserListResponse) ProtoMessage()    {}
func (*FollowChannelHalfScreenUserListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{24}
}
func (m *FollowChannelHalfScreenUserListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FollowChannelHalfScreenUserListResponse.Unmarshal(m, b)
}
func (m *FollowChannelHalfScreenUserListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FollowChannelHalfScreenUserListResponse.Marshal(b, m, deterministic)
}
func (dst *FollowChannelHalfScreenUserListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FollowChannelHalfScreenUserListResponse.Merge(dst, src)
}
func (m *FollowChannelHalfScreenUserListResponse) XXX_Size() int {
	return xxx_messageInfo_FollowChannelHalfScreenUserListResponse.Size(m)
}
func (m *FollowChannelHalfScreenUserListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FollowChannelHalfScreenUserListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FollowChannelHalfScreenUserListResponse proto.InternalMessageInfo

func (m *FollowChannelHalfScreenUserListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FollowChannelHalfScreenUserListResponse) GetUserList() []*FollowChannelHalfScreenUserList {
	if m != nil {
		return m.UserList
	}
	return nil
}

type ChannelInviteFriendPreprocessingRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Friend  uint32       `protobuf:"varint,2,opt,name=friend,proto3" json:"friend,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channelId,proto3" json:"channelId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelInviteFriendPreprocessingRequest) Reset() {
	*m = ChannelInviteFriendPreprocessingRequest{}
}
func (m *ChannelInviteFriendPreprocessingRequest) String() string { return proto.CompactTextString(m) }
func (*ChannelInviteFriendPreprocessingRequest) ProtoMessage()    {}
func (*ChannelInviteFriendPreprocessingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{25}
}
func (m *ChannelInviteFriendPreprocessingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInviteFriendPreprocessingRequest.Unmarshal(m, b)
}
func (m *ChannelInviteFriendPreprocessingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInviteFriendPreprocessingRequest.Marshal(b, m, deterministic)
}
func (dst *ChannelInviteFriendPreprocessingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInviteFriendPreprocessingRequest.Merge(dst, src)
}
func (m *ChannelInviteFriendPreprocessingRequest) XXX_Size() int {
	return xxx_messageInfo_ChannelInviteFriendPreprocessingRequest.Size(m)
}
func (m *ChannelInviteFriendPreprocessingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInviteFriendPreprocessingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInviteFriendPreprocessingRequest proto.InternalMessageInfo

func (m *ChannelInviteFriendPreprocessingRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelInviteFriendPreprocessingRequest) GetFriend() uint32 {
	if m != nil {
		return m.Friend
	}
	return 0
}

func (m *ChannelInviteFriendPreprocessingRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelInviteFriendPreprocessingResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelInviteFriendPreprocessingResponse) Reset() {
	*m = ChannelInviteFriendPreprocessingResponse{}
}
func (m *ChannelInviteFriendPreprocessingResponse) String() string { return proto.CompactTextString(m) }
func (*ChannelInviteFriendPreprocessingResponse) ProtoMessage()    {}
func (*ChannelInviteFriendPreprocessingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{26}
}
func (m *ChannelInviteFriendPreprocessingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInviteFriendPreprocessingResponse.Unmarshal(m, b)
}
func (m *ChannelInviteFriendPreprocessingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInviteFriendPreprocessingResponse.Marshal(b, m, deterministic)
}
func (dst *ChannelInviteFriendPreprocessingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInviteFriendPreprocessingResponse.Merge(dst, src)
}
func (m *ChannelInviteFriendPreprocessingResponse) XXX_Size() int {
	return xxx_messageInfo_ChannelInviteFriendPreprocessingResponse.Size(m)
}
func (m *ChannelInviteFriendPreprocessingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInviteFriendPreprocessingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInviteFriendPreprocessingResponse proto.InternalMessageInfo

func (m *ChannelInviteFriendPreprocessingResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChannelSpecialFriendListRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSpecialFriendListRequest) Reset()         { *m = ChannelSpecialFriendListRequest{} }
func (m *ChannelSpecialFriendListRequest) String() string { return proto.CompactTextString(m) }
func (*ChannelSpecialFriendListRequest) ProtoMessage()    {}
func (*ChannelSpecialFriendListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{27}
}
func (m *ChannelSpecialFriendListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSpecialFriendListRequest.Unmarshal(m, b)
}
func (m *ChannelSpecialFriendListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSpecialFriendListRequest.Marshal(b, m, deterministic)
}
func (dst *ChannelSpecialFriendListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSpecialFriendListRequest.Merge(dst, src)
}
func (m *ChannelSpecialFriendListRequest) XXX_Size() int {
	return xxx_messageInfo_ChannelSpecialFriendListRequest.Size(m)
}
func (m *ChannelSpecialFriendListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSpecialFriendListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSpecialFriendListRequest proto.InternalMessageInfo

func (m *ChannelSpecialFriendListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelSpecialFriendListRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelSpecialFriendListResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Tips                 string        `protobuf:"bytes,2,opt,name=tips,proto3" json:"tips,omitempty"`
	UidList              []uint32      `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelSpecialFriendListResponse) Reset()         { *m = ChannelSpecialFriendListResponse{} }
func (m *ChannelSpecialFriendListResponse) String() string { return proto.CompactTextString(m) }
func (*ChannelSpecialFriendListResponse) ProtoMessage()    {}
func (*ChannelSpecialFriendListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{28}
}
func (m *ChannelSpecialFriendListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSpecialFriendListResponse.Unmarshal(m, b)
}
func (m *ChannelSpecialFriendListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSpecialFriendListResponse.Marshal(b, m, deterministic)
}
func (dst *ChannelSpecialFriendListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSpecialFriendListResponse.Merge(dst, src)
}
func (m *ChannelSpecialFriendListResponse) XXX_Size() int {
	return xxx_messageInfo_ChannelSpecialFriendListResponse.Size(m)
}
func (m *ChannelSpecialFriendListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSpecialFriendListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSpecialFriendListResponse proto.InternalMessageInfo

func (m *ChannelSpecialFriendListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelSpecialFriendListResponse) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

func (m *ChannelSpecialFriendListResponse) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type OneClickInviteAllInfoRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneClickInviteAllInfoRequest) Reset()         { *m = OneClickInviteAllInfoRequest{} }
func (m *OneClickInviteAllInfoRequest) String() string { return proto.CompactTextString(m) }
func (*OneClickInviteAllInfoRequest) ProtoMessage()    {}
func (*OneClickInviteAllInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{29}
}
func (m *OneClickInviteAllInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneClickInviteAllInfoRequest.Unmarshal(m, b)
}
func (m *OneClickInviteAllInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneClickInviteAllInfoRequest.Marshal(b, m, deterministic)
}
func (dst *OneClickInviteAllInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneClickInviteAllInfoRequest.Merge(dst, src)
}
func (m *OneClickInviteAllInfoRequest) XXX_Size() int {
	return xxx_messageInfo_OneClickInviteAllInfoRequest.Size(m)
}
func (m *OneClickInviteAllInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OneClickInviteAllInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OneClickInviteAllInfoRequest proto.InternalMessageInfo

func (m *OneClickInviteAllInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OneClickInviteAllInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type OneClickInviteAllInfoResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	RemainCount          uint32   `protobuf:"varint,3,opt,name=remain_count,json=remainCount,proto3" json:"remain_count,omitempty"`
	Tips                 string   `protobuf:"bytes,4,opt,name=tips,proto3" json:"tips,omitempty"`
	Background           string   `protobuf:"bytes,5,opt,name=background,proto3" json:"background,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneClickInviteAllInfoResponse) Reset()         { *m = OneClickInviteAllInfoResponse{} }
func (m *OneClickInviteAllInfoResponse) String() string { return proto.CompactTextString(m) }
func (*OneClickInviteAllInfoResponse) ProtoMessage()    {}
func (*OneClickInviteAllInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{30}
}
func (m *OneClickInviteAllInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneClickInviteAllInfoResponse.Unmarshal(m, b)
}
func (m *OneClickInviteAllInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneClickInviteAllInfoResponse.Marshal(b, m, deterministic)
}
func (dst *OneClickInviteAllInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneClickInviteAllInfoResponse.Merge(dst, src)
}
func (m *OneClickInviteAllInfoResponse) XXX_Size() int {
	return xxx_messageInfo_OneClickInviteAllInfoResponse.Size(m)
}
func (m *OneClickInviteAllInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OneClickInviteAllInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OneClickInviteAllInfoResponse proto.InternalMessageInfo

func (m *OneClickInviteAllInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OneClickInviteAllInfoResponse) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OneClickInviteAllInfoResponse) GetRemainCount() uint32 {
	if m != nil {
		return m.RemainCount
	}
	return 0
}

func (m *OneClickInviteAllInfoResponse) GetTips() string {
	if m != nil {
		return m.Tips
	}
	return ""
}

func (m *OneClickInviteAllInfoResponse) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

type OneClickInviteAllRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneClickInviteAllRequest) Reset()         { *m = OneClickInviteAllRequest{} }
func (m *OneClickInviteAllRequest) String() string { return proto.CompactTextString(m) }
func (*OneClickInviteAllRequest) ProtoMessage()    {}
func (*OneClickInviteAllRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{31}
}
func (m *OneClickInviteAllRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneClickInviteAllRequest.Unmarshal(m, b)
}
func (m *OneClickInviteAllRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneClickInviteAllRequest.Marshal(b, m, deterministic)
}
func (dst *OneClickInviteAllRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneClickInviteAllRequest.Merge(dst, src)
}
func (m *OneClickInviteAllRequest) XXX_Size() int {
	return xxx_messageInfo_OneClickInviteAllRequest.Size(m)
}
func (m *OneClickInviteAllRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OneClickInviteAllRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OneClickInviteAllRequest proto.InternalMessageInfo

func (m *OneClickInviteAllRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OneClickInviteAllRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type OneClickInviteAllResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	RemainCount          uint32   `protobuf:"varint,3,opt,name=remain_count,json=remainCount,proto3" json:"remain_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneClickInviteAllResponse) Reset()         { *m = OneClickInviteAllResponse{} }
func (m *OneClickInviteAllResponse) String() string { return proto.CompactTextString(m) }
func (*OneClickInviteAllResponse) ProtoMessage()    {}
func (*OneClickInviteAllResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{32}
}
func (m *OneClickInviteAllResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneClickInviteAllResponse.Unmarshal(m, b)
}
func (m *OneClickInviteAllResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneClickInviteAllResponse.Marshal(b, m, deterministic)
}
func (dst *OneClickInviteAllResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneClickInviteAllResponse.Merge(dst, src)
}
func (m *OneClickInviteAllResponse) XXX_Size() int {
	return xxx_messageInfo_OneClickInviteAllResponse.Size(m)
}
func (m *OneClickInviteAllResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OneClickInviteAllResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OneClickInviteAllResponse proto.InternalMessageInfo

func (m *OneClickInviteAllResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OneClickInviteAllResponse) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OneClickInviteAllResponse) GetRemainCount() uint32 {
	if m != nil {
		return m.RemainCount
	}
	return 0
}

type OneClickInviteMsg struct {
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Context              string   `protobuf:"bytes,2,opt,name=context,proto3" json:"context,omitempty"`
	ShowSeconds          uint32   `protobuf:"varint,3,opt,name=show_seconds,json=showSeconds,proto3" json:"show_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneClickInviteMsg) Reset()         { *m = OneClickInviteMsg{} }
func (m *OneClickInviteMsg) String() string { return proto.CompactTextString(m) }
func (*OneClickInviteMsg) ProtoMessage()    {}
func (*OneClickInviteMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{33}
}
func (m *OneClickInviteMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneClickInviteMsg.Unmarshal(m, b)
}
func (m *OneClickInviteMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneClickInviteMsg.Marshal(b, m, deterministic)
}
func (dst *OneClickInviteMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneClickInviteMsg.Merge(dst, src)
}
func (m *OneClickInviteMsg) XXX_Size() int {
	return xxx_messageInfo_OneClickInviteMsg.Size(m)
}
func (m *OneClickInviteMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_OneClickInviteMsg.DiscardUnknown(m)
}

var xxx_messageInfo_OneClickInviteMsg proto.InternalMessageInfo

func (m *OneClickInviteMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OneClickInviteMsg) GetContext() string {
	if m != nil {
		return m.Context
	}
	return ""
}

func (m *OneClickInviteMsg) GetShowSeconds() uint32 {
	if m != nil {
		return m.ShowSeconds
	}
	return 0
}

type CheckIsInOtherUsersBlackListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uids                 []uint32     `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckIsInOtherUsersBlackListRequest) Reset()         { *m = CheckIsInOtherUsersBlackListRequest{} }
func (m *CheckIsInOtherUsersBlackListRequest) String() string { return proto.CompactTextString(m) }
func (*CheckIsInOtherUsersBlackListRequest) ProtoMessage()    {}
func (*CheckIsInOtherUsersBlackListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{34}
}
func (m *CheckIsInOtherUsersBlackListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIsInOtherUsersBlackListRequest.Unmarshal(m, b)
}
func (m *CheckIsInOtherUsersBlackListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIsInOtherUsersBlackListRequest.Marshal(b, m, deterministic)
}
func (dst *CheckIsInOtherUsersBlackListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIsInOtherUsersBlackListRequest.Merge(dst, src)
}
func (m *CheckIsInOtherUsersBlackListRequest) XXX_Size() int {
	return xxx_messageInfo_CheckIsInOtherUsersBlackListRequest.Size(m)
}
func (m *CheckIsInOtherUsersBlackListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIsInOtherUsersBlackListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIsInOtherUsersBlackListRequest proto.InternalMessageInfo

func (m *CheckIsInOtherUsersBlackListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckIsInOtherUsersBlackListRequest) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type CheckIsInOtherUsersBlackListResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	BlackListUserMap     map[uint32]bool `protobuf:"bytes,2,rep,name=blackList_user_map,json=blackListUserMap,proto3" json:"blackList_user_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CheckIsInOtherUsersBlackListResponse) Reset()         { *m = CheckIsInOtherUsersBlackListResponse{} }
func (m *CheckIsInOtherUsersBlackListResponse) String() string { return proto.CompactTextString(m) }
func (*CheckIsInOtherUsersBlackListResponse) ProtoMessage()    {}
func (*CheckIsInOtherUsersBlackListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{35}
}
func (m *CheckIsInOtherUsersBlackListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIsInOtherUsersBlackListResponse.Unmarshal(m, b)
}
func (m *CheckIsInOtherUsersBlackListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIsInOtherUsersBlackListResponse.Marshal(b, m, deterministic)
}
func (dst *CheckIsInOtherUsersBlackListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIsInOtherUsersBlackListResponse.Merge(dst, src)
}
func (m *CheckIsInOtherUsersBlackListResponse) XXX_Size() int {
	return xxx_messageInfo_CheckIsInOtherUsersBlackListResponse.Size(m)
}
func (m *CheckIsInOtherUsersBlackListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIsInOtherUsersBlackListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIsInOtherUsersBlackListResponse proto.InternalMessageInfo

func (m *CheckIsInOtherUsersBlackListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckIsInOtherUsersBlackListResponse) GetBlackListUserMap() map[uint32]bool {
	if m != nil {
		return m.BlackListUserMap
	}
	return nil
}

type GetUserCurrentChannelIdRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uids                 []uint32     `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserCurrentChannelIdRequest) Reset()         { *m = GetUserCurrentChannelIdRequest{} }
func (m *GetUserCurrentChannelIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrentChannelIdRequest) ProtoMessage()    {}
func (*GetUserCurrentChannelIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{36}
}
func (m *GetUserCurrentChannelIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrentChannelIdRequest.Unmarshal(m, b)
}
func (m *GetUserCurrentChannelIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrentChannelIdRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrentChannelIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrentChannelIdRequest.Merge(dst, src)
}
func (m *GetUserCurrentChannelIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrentChannelIdRequest.Size(m)
}
func (m *GetUserCurrentChannelIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrentChannelIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrentChannelIdRequest proto.InternalMessageInfo

func (m *GetUserCurrentChannelIdRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserCurrentChannelIdRequest) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetUserCurrentChannelIdResponse struct {
	BaseResp              *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserCurrentChannelMap map[uint32]uint32 `protobuf:"bytes,2,rep,name=user_current_channel_map,json=userCurrentChannelMap,proto3" json:"user_current_channel_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral  struct{}          `json:"-"`
	XXX_unrecognized      []byte            `json:"-"`
	XXX_sizecache         int32             `json:"-"`
}

func (m *GetUserCurrentChannelIdResponse) Reset()         { *m = GetUserCurrentChannelIdResponse{} }
func (m *GetUserCurrentChannelIdResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrentChannelIdResponse) ProtoMessage()    {}
func (*GetUserCurrentChannelIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{37}
}
func (m *GetUserCurrentChannelIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrentChannelIdResponse.Unmarshal(m, b)
}
func (m *GetUserCurrentChannelIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrentChannelIdResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrentChannelIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrentChannelIdResponse.Merge(dst, src)
}
func (m *GetUserCurrentChannelIdResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrentChannelIdResponse.Size(m)
}
func (m *GetUserCurrentChannelIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrentChannelIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrentChannelIdResponse proto.InternalMessageInfo

func (m *GetUserCurrentChannelIdResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserCurrentChannelIdResponse) GetUserCurrentChannelMap() map[uint32]uint32 {
	if m != nil {
		return m.UserCurrentChannelMap
	}
	return nil
}

type OneClickInviteAllGetMicIdRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	BeginMicId           uint32   `protobuf:"varint,3,opt,name=begin_mic_id,json=beginMicId,proto3" json:"begin_mic_id,omitempty"`
	EndMicId             uint32   `protobuf:"varint,4,opt,name=end_mic_id,json=endMicId,proto3" json:"end_mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneClickInviteAllGetMicIdRequest) Reset()         { *m = OneClickInviteAllGetMicIdRequest{} }
func (m *OneClickInviteAllGetMicIdRequest) String() string { return proto.CompactTextString(m) }
func (*OneClickInviteAllGetMicIdRequest) ProtoMessage()    {}
func (*OneClickInviteAllGetMicIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{38}
}
func (m *OneClickInviteAllGetMicIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneClickInviteAllGetMicIdRequest.Unmarshal(m, b)
}
func (m *OneClickInviteAllGetMicIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneClickInviteAllGetMicIdRequest.Marshal(b, m, deterministic)
}
func (dst *OneClickInviteAllGetMicIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneClickInviteAllGetMicIdRequest.Merge(dst, src)
}
func (m *OneClickInviteAllGetMicIdRequest) XXX_Size() int {
	return xxx_messageInfo_OneClickInviteAllGetMicIdRequest.Size(m)
}
func (m *OneClickInviteAllGetMicIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OneClickInviteAllGetMicIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OneClickInviteAllGetMicIdRequest proto.InternalMessageInfo

func (m *OneClickInviteAllGetMicIdRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OneClickInviteAllGetMicIdRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OneClickInviteAllGetMicIdRequest) GetBeginMicId() uint32 {
	if m != nil {
		return m.BeginMicId
	}
	return 0
}

func (m *OneClickInviteAllGetMicIdRequest) GetEndMicId() uint32 {
	if m != nil {
		return m.EndMicId
	}
	return 0
}

type OneClickInviteAllGetMicIdResponse struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId uint32 `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	MicId                uint32   `protobuf:"varint,3,opt,name=micId,proto3" json:"micId,omitempty"`
	MicToken             string   `protobuf:"bytes,4,opt,name=mic_token,json=micToken,proto3" json:"mic_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OneClickInviteAllGetMicIdResponse) Reset()         { *m = OneClickInviteAllGetMicIdResponse{} }
func (m *OneClickInviteAllGetMicIdResponse) String() string { return proto.CompactTextString(m) }
func (*OneClickInviteAllGetMicIdResponse) ProtoMessage()    {}
func (*OneClickInviteAllGetMicIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{39}
}
func (m *OneClickInviteAllGetMicIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OneClickInviteAllGetMicIdResponse.Unmarshal(m, b)
}
func (m *OneClickInviteAllGetMicIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OneClickInviteAllGetMicIdResponse.Marshal(b, m, deterministic)
}
func (dst *OneClickInviteAllGetMicIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OneClickInviteAllGetMicIdResponse.Merge(dst, src)
}
func (m *OneClickInviteAllGetMicIdResponse) XXX_Size() int {
	return xxx_messageInfo_OneClickInviteAllGetMicIdResponse.Size(m)
}
func (m *OneClickInviteAllGetMicIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OneClickInviteAllGetMicIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OneClickInviteAllGetMicIdResponse proto.InternalMessageInfo

func (m *OneClickInviteAllGetMicIdResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OneClickInviteAllGetMicIdResponse) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OneClickInviteAllGetMicIdResponse) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *OneClickInviteAllGetMicIdResponse) GetMicToken() string {
	if m != nil {
		return m.MicToken
	}
	return ""
}

type AutoInviteMicPanelRequest struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	ManagerUid           uint32   `protobuf:"varint,3,opt,name=manager_uid,json=managerUid,proto3" json:"manager_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AutoInviteMicPanelRequest) Reset()         { *m = AutoInviteMicPanelRequest{} }
func (m *AutoInviteMicPanelRequest) String() string { return proto.CompactTextString(m) }
func (*AutoInviteMicPanelRequest) ProtoMessage()    {}
func (*AutoInviteMicPanelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{40}
}
func (m *AutoInviteMicPanelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoInviteMicPanelRequest.Unmarshal(m, b)
}
func (m *AutoInviteMicPanelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoInviteMicPanelRequest.Marshal(b, m, deterministic)
}
func (dst *AutoInviteMicPanelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoInviteMicPanelRequest.Merge(dst, src)
}
func (m *AutoInviteMicPanelRequest) XXX_Size() int {
	return xxx_messageInfo_AutoInviteMicPanelRequest.Size(m)
}
func (m *AutoInviteMicPanelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoInviteMicPanelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AutoInviteMicPanelRequest proto.InternalMessageInfo

func (m *AutoInviteMicPanelRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AutoInviteMicPanelRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AutoInviteMicPanelRequest) GetManagerUid() uint32 {
	if m != nil {
		return m.ManagerUid
	}
	return 0
}

type AutoInviteMicPanelResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Owner                *UserSimpleInfo `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
	ChannelOwnerRole     string          `protobuf:"bytes,3,opt,name=channel_owner_role,json=channelOwnerRole,proto3" json:"channel_owner_role,omitempty"`
	Tags                 []string        `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AutoInviteMicPanelResponse) Reset()         { *m = AutoInviteMicPanelResponse{} }
func (m *AutoInviteMicPanelResponse) String() string { return proto.CompactTextString(m) }
func (*AutoInviteMicPanelResponse) ProtoMessage()    {}
func (*AutoInviteMicPanelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{41}
}
func (m *AutoInviteMicPanelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AutoInviteMicPanelResponse.Unmarshal(m, b)
}
func (m *AutoInviteMicPanelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AutoInviteMicPanelResponse.Marshal(b, m, deterministic)
}
func (dst *AutoInviteMicPanelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AutoInviteMicPanelResponse.Merge(dst, src)
}
func (m *AutoInviteMicPanelResponse) XXX_Size() int {
	return xxx_messageInfo_AutoInviteMicPanelResponse.Size(m)
}
func (m *AutoInviteMicPanelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AutoInviteMicPanelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AutoInviteMicPanelResponse proto.InternalMessageInfo

func (m *AutoInviteMicPanelResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *AutoInviteMicPanelResponse) GetOwner() *UserSimpleInfo {
	if m != nil {
		return m.Owner
	}
	return nil
}

func (m *AutoInviteMicPanelResponse) GetChannelOwnerRole() string {
	if m != nil {
		return m.ChannelOwnerRole
	}
	return ""
}

func (m *AutoInviteMicPanelResponse) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

// 兴趣内容通用xml消息通知
type MuseCommonXMLMsgNotify struct {
	MsgType              uint32   `protobuf:"varint,1,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"`
	XmlMsg               string   `protobuf:"bytes,2,opt,name=xml_msg,json=xmlMsg,proto3" json:"xml_msg,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MsgContentType       uint32   `protobuf:"varint,4,opt,name=msg_content_type,json=msgContentType,proto3" json:"msg_content_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseCommonXMLMsgNotify) Reset()         { *m = MuseCommonXMLMsgNotify{} }
func (m *MuseCommonXMLMsgNotify) String() string { return proto.CompactTextString(m) }
func (*MuseCommonXMLMsgNotify) ProtoMessage()    {}
func (*MuseCommonXMLMsgNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{42}
}
func (m *MuseCommonXMLMsgNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommonXMLMsgNotify.Unmarshal(m, b)
}
func (m *MuseCommonXMLMsgNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommonXMLMsgNotify.Marshal(b, m, deterministic)
}
func (dst *MuseCommonXMLMsgNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommonXMLMsgNotify.Merge(dst, src)
}
func (m *MuseCommonXMLMsgNotify) XXX_Size() int {
	return xxx_messageInfo_MuseCommonXMLMsgNotify.Size(m)
}
func (m *MuseCommonXMLMsgNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommonXMLMsgNotify.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommonXMLMsgNotify proto.InternalMessageInfo

func (m *MuseCommonXMLMsgNotify) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *MuseCommonXMLMsgNotify) GetXmlMsg() string {
	if m != nil {
		return m.XmlMsg
	}
	return ""
}

func (m *MuseCommonXMLMsgNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseCommonXMLMsgNotify) GetMsgContentType() uint32 {
	if m != nil {
		return m.MsgContentType
	}
	return 0
}

type HoldUserOnMicMsg struct {
	Target               *UserSimpleInfo `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	Context              string          `protobuf:"bytes,2,opt,name=context,proto3" json:"context,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *HoldUserOnMicMsg) Reset()         { *m = HoldUserOnMicMsg{} }
func (m *HoldUserOnMicMsg) String() string { return proto.CompactTextString(m) }
func (*HoldUserOnMicMsg) ProtoMessage()    {}
func (*HoldUserOnMicMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{43}
}
func (m *HoldUserOnMicMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HoldUserOnMicMsg.Unmarshal(m, b)
}
func (m *HoldUserOnMicMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HoldUserOnMicMsg.Marshal(b, m, deterministic)
}
func (dst *HoldUserOnMicMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HoldUserOnMicMsg.Merge(dst, src)
}
func (m *HoldUserOnMicMsg) XXX_Size() int {
	return xxx_messageInfo_HoldUserOnMicMsg.Size(m)
}
func (m *HoldUserOnMicMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_HoldUserOnMicMsg.DiscardUnknown(m)
}

var xxx_messageInfo_HoldUserOnMicMsg proto.InternalMessageInfo

func (m *HoldUserOnMicMsg) GetTarget() *UserSimpleInfo {
	if m != nil {
		return m.Target
	}
	return nil
}

func (m *HoldUserOnMicMsg) GetContext() string {
	if m != nil {
		return m.Context
	}
	return ""
}

type GetUserRelationshipRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32       `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserRelationshipRequest) Reset()         { *m = GetUserRelationshipRequest{} }
func (m *GetUserRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserRelationshipRequest) ProtoMessage()    {}
func (*GetUserRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{44}
}
func (m *GetUserRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRelationshipRequest.Unmarshal(m, b)
}
func (m *GetUserRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRelationshipRequest.Merge(dst, src)
}
func (m *GetUserRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserRelationshipRequest.Size(m)
}
func (m *GetUserRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRelationshipRequest proto.InternalMessageInfo

func (m *GetUserRelationshipRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserRelationshipRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRelationshipRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetUserRelationshipResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsFollowTarget       bool          `protobuf:"varint,2,opt,name=is_follow_target,json=isFollowTarget,proto3" json:"is_follow_target,omitempty"`
	TargetIsFollow       bool          `protobuf:"varint,3,opt,name=target_is_follow,json=targetIsFollow,proto3" json:"target_is_follow,omitempty"`
	RelationText         string        `protobuf:"bytes,4,opt,name=relation_text,json=relationText,proto3" json:"relation_text,omitempty"`
	IsHitRule            bool          `protobuf:"varint,5,opt,name=is_hit_rule,json=isHitRule,proto3" json:"is_hit_rule,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserRelationshipResponse) Reset()         { *m = GetUserRelationshipResponse{} }
func (m *GetUserRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserRelationshipResponse) ProtoMessage()    {}
func (*GetUserRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{45}
}
func (m *GetUserRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRelationshipResponse.Unmarshal(m, b)
}
func (m *GetUserRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRelationshipResponse.Merge(dst, src)
}
func (m *GetUserRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserRelationshipResponse.Size(m)
}
func (m *GetUserRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRelationshipResponse proto.InternalMessageInfo

func (m *GetUserRelationshipResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserRelationshipResponse) GetIsFollowTarget() bool {
	if m != nil {
		return m.IsFollowTarget
	}
	return false
}

func (m *GetUserRelationshipResponse) GetTargetIsFollow() bool {
	if m != nil {
		return m.TargetIsFollow
	}
	return false
}

func (m *GetUserRelationshipResponse) GetRelationText() string {
	if m != nil {
		return m.RelationText
	}
	return ""
}

func (m *GetUserRelationshipResponse) GetIsHitRule() bool {
	if m != nil {
		return m.IsHitRule
	}
	return false
}

type NonMicUserListOrderRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	NonMicUids           []uint32     `protobuf:"varint,2,rep,packed,name=non_mic_uids,json=nonMicUids,proto3" json:"non_mic_uids,omitempty"`
	InviteUids           []uint32     `protobuf:"varint,3,rep,packed,name=invite_uids,json=inviteUids,proto3" json:"invite_uids,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *NonMicUserListOrderRequest) Reset()         { *m = NonMicUserListOrderRequest{} }
func (m *NonMicUserListOrderRequest) String() string { return proto.CompactTextString(m) }
func (*NonMicUserListOrderRequest) ProtoMessage()    {}
func (*NonMicUserListOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{46}
}
func (m *NonMicUserListOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonMicUserListOrderRequest.Unmarshal(m, b)
}
func (m *NonMicUserListOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonMicUserListOrderRequest.Marshal(b, m, deterministic)
}
func (dst *NonMicUserListOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonMicUserListOrderRequest.Merge(dst, src)
}
func (m *NonMicUserListOrderRequest) XXX_Size() int {
	return xxx_messageInfo_NonMicUserListOrderRequest.Size(m)
}
func (m *NonMicUserListOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NonMicUserListOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NonMicUserListOrderRequest proto.InternalMessageInfo

func (m *NonMicUserListOrderRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *NonMicUserListOrderRequest) GetNonMicUids() []uint32 {
	if m != nil {
		return m.NonMicUids
	}
	return nil
}

func (m *NonMicUserListOrderRequest) GetInviteUids() []uint32 {
	if m != nil {
		return m.InviteUids
	}
	return nil
}

func (m *NonMicUserListOrderRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type NonMicUserListOrderResponse struct {
	BaseResp             *app.BaseResp                       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NonMicUsers          []*NonMicUserListOrderResponse_User `protobuf:"bytes,2,rep,name=non_mic_users,json=nonMicUsers,proto3" json:"non_mic_users,omitempty"`
	InviteUsers          []*NonMicUserListOrderResponse_User `protobuf:"bytes,3,rep,name=invite_users,json=inviteUsers,proto3" json:"invite_users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *NonMicUserListOrderResponse) Reset()         { *m = NonMicUserListOrderResponse{} }
func (m *NonMicUserListOrderResponse) String() string { return proto.CompactTextString(m) }
func (*NonMicUserListOrderResponse) ProtoMessage()    {}
func (*NonMicUserListOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{47}
}
func (m *NonMicUserListOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonMicUserListOrderResponse.Unmarshal(m, b)
}
func (m *NonMicUserListOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonMicUserListOrderResponse.Marshal(b, m, deterministic)
}
func (dst *NonMicUserListOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonMicUserListOrderResponse.Merge(dst, src)
}
func (m *NonMicUserListOrderResponse) XXX_Size() int {
	return xxx_messageInfo_NonMicUserListOrderResponse.Size(m)
}
func (m *NonMicUserListOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NonMicUserListOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NonMicUserListOrderResponse proto.InternalMessageInfo

func (m *NonMicUserListOrderResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *NonMicUserListOrderResponse) GetNonMicUsers() []*NonMicUserListOrderResponse_User {
	if m != nil {
		return m.NonMicUsers
	}
	return nil
}

func (m *NonMicUserListOrderResponse) GetInviteUsers() []*NonMicUserListOrderResponse_User {
	if m != nil {
		return m.InviteUsers
	}
	return nil
}

type NonMicUserListOrderResponse_User struct {
	UserInfo             *UserSimpleInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	ChannelRoleText      string          `protobuf:"bytes,2,opt,name=channel_role_text,json=channelRoleText,proto3" json:"channel_role_text,omitempty"`
	ReunionText          string          `protobuf:"bytes,3,opt,name=reunion_text,json=reunionText,proto3" json:"reunion_text,omitempty"`
	FriendText           string          `protobuf:"bytes,4,opt,name=friend_text,json=friendText,proto3" json:"friend_text,omitempty"`
	InAbtest             bool            `protobuf:"varint,5,opt,name=in_abtest,json=inAbtest,proto3" json:"in_abtest,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *NonMicUserListOrderResponse_User) Reset()         { *m = NonMicUserListOrderResponse_User{} }
func (m *NonMicUserListOrderResponse_User) String() string { return proto.CompactTextString(m) }
func (*NonMicUserListOrderResponse_User) ProtoMessage()    {}
func (*NonMicUserListOrderResponse_User) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{47, 0}
}
func (m *NonMicUserListOrderResponse_User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NonMicUserListOrderResponse_User.Unmarshal(m, b)
}
func (m *NonMicUserListOrderResponse_User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NonMicUserListOrderResponse_User.Marshal(b, m, deterministic)
}
func (dst *NonMicUserListOrderResponse_User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NonMicUserListOrderResponse_User.Merge(dst, src)
}
func (m *NonMicUserListOrderResponse_User) XXX_Size() int {
	return xxx_messageInfo_NonMicUserListOrderResponse_User.Size(m)
}
func (m *NonMicUserListOrderResponse_User) XXX_DiscardUnknown() {
	xxx_messageInfo_NonMicUserListOrderResponse_User.DiscardUnknown(m)
}

var xxx_messageInfo_NonMicUserListOrderResponse_User proto.InternalMessageInfo

func (m *NonMicUserListOrderResponse_User) GetUserInfo() *UserSimpleInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *NonMicUserListOrderResponse_User) GetChannelRoleText() string {
	if m != nil {
		return m.ChannelRoleText
	}
	return ""
}

func (m *NonMicUserListOrderResponse_User) GetReunionText() string {
	if m != nil {
		return m.ReunionText
	}
	return ""
}

func (m *NonMicUserListOrderResponse_User) GetFriendText() string {
	if m != nil {
		return m.FriendText
	}
	return ""
}

func (m *NonMicUserListOrderResponse_User) GetInAbtest() bool {
	if m != nil {
		return m.InAbtest
	}
	return false
}

type ShowRoomExitFollowPopupRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uids                 []uint32     `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ShowRoomExitFollowPopupRequest) Reset()         { *m = ShowRoomExitFollowPopupRequest{} }
func (m *ShowRoomExitFollowPopupRequest) String() string { return proto.CompactTextString(m) }
func (*ShowRoomExitFollowPopupRequest) ProtoMessage()    {}
func (*ShowRoomExitFollowPopupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{48}
}
func (m *ShowRoomExitFollowPopupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowRoomExitFollowPopupRequest.Unmarshal(m, b)
}
func (m *ShowRoomExitFollowPopupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowRoomExitFollowPopupRequest.Marshal(b, m, deterministic)
}
func (dst *ShowRoomExitFollowPopupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowRoomExitFollowPopupRequest.Merge(dst, src)
}
func (m *ShowRoomExitFollowPopupRequest) XXX_Size() int {
	return xxx_messageInfo_ShowRoomExitFollowPopupRequest.Size(m)
}
func (m *ShowRoomExitFollowPopupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowRoomExitFollowPopupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShowRoomExitFollowPopupRequest proto.InternalMessageInfo

func (m *ShowRoomExitFollowPopupRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ShowRoomExitFollowPopupRequest) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type ShowRoomExitFollowPopupResponse struct {
	BaseResp        *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ShowFollowPopup bool          `protobuf:"varint,2,opt,name=show_follow_popup,json=showFollowPopup,proto3" json:"show_follow_popup,omitempty"`
	// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
	DailyPopupLimit      int32    `protobuf:"varint,3,opt,name=DailyPopupLimit,proto3" json:"DailyPopupLimit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowRoomExitFollowPopupResponse) Reset()         { *m = ShowRoomExitFollowPopupResponse{} }
func (m *ShowRoomExitFollowPopupResponse) String() string { return proto.CompactTextString(m) }
func (*ShowRoomExitFollowPopupResponse) ProtoMessage()    {}
func (*ShowRoomExitFollowPopupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{49}
}
func (m *ShowRoomExitFollowPopupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowRoomExitFollowPopupResponse.Unmarshal(m, b)
}
func (m *ShowRoomExitFollowPopupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowRoomExitFollowPopupResponse.Marshal(b, m, deterministic)
}
func (dst *ShowRoomExitFollowPopupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowRoomExitFollowPopupResponse.Merge(dst, src)
}
func (m *ShowRoomExitFollowPopupResponse) XXX_Size() int {
	return xxx_messageInfo_ShowRoomExitFollowPopupResponse.Size(m)
}
func (m *ShowRoomExitFollowPopupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowRoomExitFollowPopupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ShowRoomExitFollowPopupResponse proto.InternalMessageInfo

func (m *ShowRoomExitFollowPopupResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ShowRoomExitFollowPopupResponse) GetShowFollowPopup() bool {
	if m != nil {
		return m.ShowFollowPopup
	}
	return false
}

func (m *ShowRoomExitFollowPopupResponse) GetDailyPopupLimit() int32 {
	if m != nil {
		return m.DailyPopupLimit
	}
	return 0
}

type GetRoomExitGuideFollowListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uids                 []uint32     `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRoomExitGuideFollowListRequest) Reset()         { *m = GetRoomExitGuideFollowListRequest{} }
func (m *GetRoomExitGuideFollowListRequest) String() string { return proto.CompactTextString(m) }
func (*GetRoomExitGuideFollowListRequest) ProtoMessage()    {}
func (*GetRoomExitGuideFollowListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{50}
}
func (m *GetRoomExitGuideFollowListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomExitGuideFollowListRequest.Unmarshal(m, b)
}
func (m *GetRoomExitGuideFollowListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomExitGuideFollowListRequest.Marshal(b, m, deterministic)
}
func (dst *GetRoomExitGuideFollowListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomExitGuideFollowListRequest.Merge(dst, src)
}
func (m *GetRoomExitGuideFollowListRequest) XXX_Size() int {
	return xxx_messageInfo_GetRoomExitGuideFollowListRequest.Size(m)
}
func (m *GetRoomExitGuideFollowListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomExitGuideFollowListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomExitGuideFollowListRequest proto.InternalMessageInfo

func (m *GetRoomExitGuideFollowListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRoomExitGuideFollowListRequest) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *GetRoomExitGuideFollowListRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetRoomExitGuideFollowListResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GuideFollowUser      []*GuideFollowUser `protobuf:"bytes,2,rep,name=guide_follow_user,json=guideFollowUser,proto3" json:"guide_follow_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetRoomExitGuideFollowListResponse) Reset()         { *m = GetRoomExitGuideFollowListResponse{} }
func (m *GetRoomExitGuideFollowListResponse) String() string { return proto.CompactTextString(m) }
func (*GetRoomExitGuideFollowListResponse) ProtoMessage()    {}
func (*GetRoomExitGuideFollowListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{51}
}
func (m *GetRoomExitGuideFollowListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomExitGuideFollowListResponse.Unmarshal(m, b)
}
func (m *GetRoomExitGuideFollowListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomExitGuideFollowListResponse.Marshal(b, m, deterministic)
}
func (dst *GetRoomExitGuideFollowListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomExitGuideFollowListResponse.Merge(dst, src)
}
func (m *GetRoomExitGuideFollowListResponse) XXX_Size() int {
	return xxx_messageInfo_GetRoomExitGuideFollowListResponse.Size(m)
}
func (m *GetRoomExitGuideFollowListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomExitGuideFollowListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomExitGuideFollowListResponse proto.InternalMessageInfo

func (m *GetRoomExitGuideFollowListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRoomExitGuideFollowListResponse) GetGuideFollowUser() []*GuideFollowUser {
	if m != nil {
		return m.GuideFollowUser
	}
	return nil
}

type GuideFollowUser struct {
	UserInfo             *UserSimpleInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	Admin                *AdminInfo      `protobuf:"bytes,2,opt,name=admin,proto3" json:"admin,omitempty"`
	Tags                 []string        `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty"`
	RelationText         string          `protobuf:"bytes,4,opt,name=relation_text,json=relationText,proto3" json:"relation_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GuideFollowUser) Reset()         { *m = GuideFollowUser{} }
func (m *GuideFollowUser) String() string { return proto.CompactTextString(m) }
func (*GuideFollowUser) ProtoMessage()    {}
func (*GuideFollowUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{52}
}
func (m *GuideFollowUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuideFollowUser.Unmarshal(m, b)
}
func (m *GuideFollowUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuideFollowUser.Marshal(b, m, deterministic)
}
func (dst *GuideFollowUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuideFollowUser.Merge(dst, src)
}
func (m *GuideFollowUser) XXX_Size() int {
	return xxx_messageInfo_GuideFollowUser.Size(m)
}
func (m *GuideFollowUser) XXX_DiscardUnknown() {
	xxx_messageInfo_GuideFollowUser.DiscardUnknown(m)
}

var xxx_messageInfo_GuideFollowUser proto.InternalMessageInfo

func (m *GuideFollowUser) GetUserInfo() *UserSimpleInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GuideFollowUser) GetAdmin() *AdminInfo {
	if m != nil {
		return m.Admin
	}
	return nil
}

func (m *GuideFollowUser) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GuideFollowUser) GetRelationText() string {
	if m != nil {
		return m.RelationText
	}
	return ""
}

type AdminInfo struct {
	AdminText            string   `protobuf:"bytes,1,opt,name=admin_text,json=adminText,proto3" json:"admin_text,omitempty"`
	AdminRole            uint32   `protobuf:"varint,2,opt,name=admin_role,json=adminRole,proto3" json:"admin_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdminInfo) Reset()         { *m = AdminInfo{} }
func (m *AdminInfo) String() string { return proto.CompactTextString(m) }
func (*AdminInfo) ProtoMessage()    {}
func (*AdminInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{53}
}
func (m *AdminInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdminInfo.Unmarshal(m, b)
}
func (m *AdminInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdminInfo.Marshal(b, m, deterministic)
}
func (dst *AdminInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminInfo.Merge(dst, src)
}
func (m *AdminInfo) XXX_Size() int {
	return xxx_messageInfo_AdminInfo.Size(m)
}
func (m *AdminInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AdminInfo proto.InternalMessageInfo

func (m *AdminInfo) GetAdminText() string {
	if m != nil {
		return m.AdminText
	}
	return ""
}

func (m *AdminInfo) GetAdminRole() uint32 {
	if m != nil {
		return m.AdminRole
	}
	return 0
}

type GetRoomIcebreakerPopupRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OwnerUid             uint32       `protobuf:"varint,3,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRoomIcebreakerPopupRequest) Reset()         { *m = GetRoomIcebreakerPopupRequest{} }
func (m *GetRoomIcebreakerPopupRequest) String() string { return proto.CompactTextString(m) }
func (*GetRoomIcebreakerPopupRequest) ProtoMessage()    {}
func (*GetRoomIcebreakerPopupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{54}
}
func (m *GetRoomIcebreakerPopupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomIcebreakerPopupRequest.Unmarshal(m, b)
}
func (m *GetRoomIcebreakerPopupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomIcebreakerPopupRequest.Marshal(b, m, deterministic)
}
func (dst *GetRoomIcebreakerPopupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomIcebreakerPopupRequest.Merge(dst, src)
}
func (m *GetRoomIcebreakerPopupRequest) XXX_Size() int {
	return xxx_messageInfo_GetRoomIcebreakerPopupRequest.Size(m)
}
func (m *GetRoomIcebreakerPopupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomIcebreakerPopupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomIcebreakerPopupRequest proto.InternalMessageInfo

func (m *GetRoomIcebreakerPopupRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRoomIcebreakerPopupRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetRoomIcebreakerPopupRequest) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

type QnAPair struct {
	Question             string        `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	Answer               []string      `protobuf:"bytes,2,rep,name=answer,proto3" json:"answer,omitempty"`
	AnswerInfo           []*AnswerInfo `protobuf:"bytes,3,rep,name=answer_info,json=answerInfo,proto3" json:"answer_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QnAPair) Reset()         { *m = QnAPair{} }
func (m *QnAPair) String() string { return proto.CompactTextString(m) }
func (*QnAPair) ProtoMessage()    {}
func (*QnAPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{55}
}
func (m *QnAPair) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QnAPair.Unmarshal(m, b)
}
func (m *QnAPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QnAPair.Marshal(b, m, deterministic)
}
func (dst *QnAPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QnAPair.Merge(dst, src)
}
func (m *QnAPair) XXX_Size() int {
	return xxx_messageInfo_QnAPair.Size(m)
}
func (m *QnAPair) XXX_DiscardUnknown() {
	xxx_messageInfo_QnAPair.DiscardUnknown(m)
}

var xxx_messageInfo_QnAPair proto.InternalMessageInfo

func (m *QnAPair) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *QnAPair) GetAnswer() []string {
	if m != nil {
		return m.Answer
	}
	return nil
}

func (m *QnAPair) GetAnswerInfo() []*AnswerInfo {
	if m != nil {
		return m.AnswerInfo
	}
	return nil
}

type AnswerInfo struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnswerInfo) Reset()         { *m = AnswerInfo{} }
func (m *AnswerInfo) String() string { return proto.CompactTextString(m) }
func (*AnswerInfo) ProtoMessage()    {}
func (*AnswerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{56}
}
func (m *AnswerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnswerInfo.Unmarshal(m, b)
}
func (m *AnswerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnswerInfo.Marshal(b, m, deterministic)
}
func (dst *AnswerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnswerInfo.Merge(dst, src)
}
func (m *AnswerInfo) XXX_Size() int {
	return xxx_messageInfo_AnswerInfo.Size(m)
}
func (m *AnswerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnswerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnswerInfo proto.InternalMessageInfo

func (m *AnswerInfo) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AnswerInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type GetRoomIcebreakerPopupResponse struct {
	BaseResp                     *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	QnaPair                      *QnAPair      `protobuf:"bytes,2,opt,name=qna_pair,json=qnaPair,proto3" json:"qna_pair,omitempty"`
	LocationTag                  string        `protobuf:"bytes,3,opt,name=location_tag,json=locationTag,proto3" json:"location_tag,omitempty"`
	ReleationTag                 string        `protobuf:"bytes,4,opt,name=releation_tag,json=releationTag,proto3" json:"releation_tag,omitempty"`
	Age                          int32         `protobuf:"varint,5,opt,name=age,proto3" json:"age,omitempty"` // Deprecated: Do not use.
	OwnerInRoom                  bool          `protobuf:"varint,6,opt,name=owner_in_room,json=ownerInRoom,proto3" json:"owner_in_room,omitempty"`
	FollowStatus                 uint32        `protobuf:"varint,7,opt,name=follow_status,json=followStatus,proto3" json:"follow_status,omitempty"` // Deprecated: Do not use.
	Birthday                     string        `protobuf:"bytes,8,opt,name=birthday,proto3" json:"birthday,omitempty"`
	IsFollowTarget               bool          `protobuf:"varint,9,opt,name=is_follow_target,json=isFollowTarget,proto3" json:"is_follow_target,omitempty"`
	IsFollowedByTarget           bool          `protobuf:"varint,10,opt,name=is_followed_by_target,json=isFollowedByTarget,proto3" json:"is_followed_by_target,omitempty"`
	CustomIcebreakerSwitchStatus bool          `protobuf:"varint,11,opt,name=custom_icebreaker_switch_status,json=customIcebreakerSwitchStatus,proto3" json:"custom_icebreaker_switch_status,omitempty"`
	IceBreakerQuestionType       int32         `protobuf:"varint,12,opt,name=ice_breaker_question_type,json=iceBreakerQuestionType,proto3" json:"ice_breaker_question_type,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}      `json:"-"`
	XXX_unrecognized             []byte        `json:"-"`
	XXX_sizecache                int32         `json:"-"`
}

func (m *GetRoomIcebreakerPopupResponse) Reset()         { *m = GetRoomIcebreakerPopupResponse{} }
func (m *GetRoomIcebreakerPopupResponse) String() string { return proto.CompactTextString(m) }
func (*GetRoomIcebreakerPopupResponse) ProtoMessage()    {}
func (*GetRoomIcebreakerPopupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{57}
}
func (m *GetRoomIcebreakerPopupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomIcebreakerPopupResponse.Unmarshal(m, b)
}
func (m *GetRoomIcebreakerPopupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomIcebreakerPopupResponse.Marshal(b, m, deterministic)
}
func (dst *GetRoomIcebreakerPopupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomIcebreakerPopupResponse.Merge(dst, src)
}
func (m *GetRoomIcebreakerPopupResponse) XXX_Size() int {
	return xxx_messageInfo_GetRoomIcebreakerPopupResponse.Size(m)
}
func (m *GetRoomIcebreakerPopupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomIcebreakerPopupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomIcebreakerPopupResponse proto.InternalMessageInfo

func (m *GetRoomIcebreakerPopupResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRoomIcebreakerPopupResponse) GetQnaPair() *QnAPair {
	if m != nil {
		return m.QnaPair
	}
	return nil
}

func (m *GetRoomIcebreakerPopupResponse) GetLocationTag() string {
	if m != nil {
		return m.LocationTag
	}
	return ""
}

func (m *GetRoomIcebreakerPopupResponse) GetReleationTag() string {
	if m != nil {
		return m.ReleationTag
	}
	return ""
}

// Deprecated: Do not use.
func (m *GetRoomIcebreakerPopupResponse) GetAge() int32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *GetRoomIcebreakerPopupResponse) GetOwnerInRoom() bool {
	if m != nil {
		return m.OwnerInRoom
	}
	return false
}

// Deprecated: Do not use.
func (m *GetRoomIcebreakerPopupResponse) GetFollowStatus() uint32 {
	if m != nil {
		return m.FollowStatus
	}
	return 0
}

func (m *GetRoomIcebreakerPopupResponse) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *GetRoomIcebreakerPopupResponse) GetIsFollowTarget() bool {
	if m != nil {
		return m.IsFollowTarget
	}
	return false
}

func (m *GetRoomIcebreakerPopupResponse) GetIsFollowedByTarget() bool {
	if m != nil {
		return m.IsFollowedByTarget
	}
	return false
}

func (m *GetRoomIcebreakerPopupResponse) GetCustomIcebreakerSwitchStatus() bool {
	if m != nil {
		return m.CustomIcebreakerSwitchStatus
	}
	return false
}

func (m *GetRoomIcebreakerPopupResponse) GetIceBreakerQuestionType() int32 {
	if m != nil {
		return m.IceBreakerQuestionType
	}
	return 0
}

type PushLikeActivityMsg struct {
	LikeActivityUrl      string   `protobuf:"bytes,1,opt,name=like_activity_url,json=likeActivityUrl,proto3" json:"like_activity_url,omitempty"`
	SocialCommunityId    string   `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushLikeActivityMsg) Reset()         { *m = PushLikeActivityMsg{} }
func (m *PushLikeActivityMsg) String() string { return proto.CompactTextString(m) }
func (*PushLikeActivityMsg) ProtoMessage()    {}
func (*PushLikeActivityMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{58}
}
func (m *PushLikeActivityMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushLikeActivityMsg.Unmarshal(m, b)
}
func (m *PushLikeActivityMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushLikeActivityMsg.Marshal(b, m, deterministic)
}
func (dst *PushLikeActivityMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushLikeActivityMsg.Merge(dst, src)
}
func (m *PushLikeActivityMsg) XXX_Size() int {
	return xxx_messageInfo_PushLikeActivityMsg.Size(m)
}
func (m *PushLikeActivityMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PushLikeActivityMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PushLikeActivityMsg proto.InternalMessageInfo

func (m *PushLikeActivityMsg) GetLikeActivityUrl() string {
	if m != nil {
		return m.LikeActivityUrl
	}
	return ""
}

func (m *PushLikeActivityMsg) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type SendPublicMessageDirectlyRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FromUid              uint32       `protobuf:"varint,2,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Content              string       `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendPublicMessageDirectlyRequest) Reset()         { *m = SendPublicMessageDirectlyRequest{} }
func (m *SendPublicMessageDirectlyRequest) String() string { return proto.CompactTextString(m) }
func (*SendPublicMessageDirectlyRequest) ProtoMessage()    {}
func (*SendPublicMessageDirectlyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{59}
}
func (m *SendPublicMessageDirectlyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPublicMessageDirectlyRequest.Unmarshal(m, b)
}
func (m *SendPublicMessageDirectlyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPublicMessageDirectlyRequest.Marshal(b, m, deterministic)
}
func (dst *SendPublicMessageDirectlyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPublicMessageDirectlyRequest.Merge(dst, src)
}
func (m *SendPublicMessageDirectlyRequest) XXX_Size() int {
	return xxx_messageInfo_SendPublicMessageDirectlyRequest.Size(m)
}
func (m *SendPublicMessageDirectlyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPublicMessageDirectlyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendPublicMessageDirectlyRequest proto.InternalMessageInfo

func (m *SendPublicMessageDirectlyRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendPublicMessageDirectlyRequest) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *SendPublicMessageDirectlyRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendPublicMessageDirectlyRequest) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type SendPublicMessageDirectlyResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendPublicMessageDirectlyResponse) Reset()         { *m = SendPublicMessageDirectlyResponse{} }
func (m *SendPublicMessageDirectlyResponse) String() string { return proto.CompactTextString(m) }
func (*SendPublicMessageDirectlyResponse) ProtoMessage()    {}
func (*SendPublicMessageDirectlyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{60}
}
func (m *SendPublicMessageDirectlyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendPublicMessageDirectlyResponse.Unmarshal(m, b)
}
func (m *SendPublicMessageDirectlyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendPublicMessageDirectlyResponse.Marshal(b, m, deterministic)
}
func (dst *SendPublicMessageDirectlyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendPublicMessageDirectlyResponse.Merge(dst, src)
}
func (m *SendPublicMessageDirectlyResponse) XXX_Size() int {
	return xxx_messageInfo_SendPublicMessageDirectlyResponse.Size(m)
}
func (m *SendPublicMessageDirectlyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendPublicMessageDirectlyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendPublicMessageDirectlyResponse proto.InternalMessageInfo

func (m *SendPublicMessageDirectlyResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetPublicScreenShortcutImageRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPublicScreenShortcutImageRequest) Reset()         { *m = GetPublicScreenShortcutImageRequest{} }
func (m *GetPublicScreenShortcutImageRequest) String() string { return proto.CompactTextString(m) }
func (*GetPublicScreenShortcutImageRequest) ProtoMessage()    {}
func (*GetPublicScreenShortcutImageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{61}
}
func (m *GetPublicScreenShortcutImageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicScreenShortcutImageRequest.Unmarshal(m, b)
}
func (m *GetPublicScreenShortcutImageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicScreenShortcutImageRequest.Marshal(b, m, deterministic)
}
func (dst *GetPublicScreenShortcutImageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicScreenShortcutImageRequest.Merge(dst, src)
}
func (m *GetPublicScreenShortcutImageRequest) XXX_Size() int {
	return xxx_messageInfo_GetPublicScreenShortcutImageRequest.Size(m)
}
func (m *GetPublicScreenShortcutImageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicScreenShortcutImageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicScreenShortcutImageRequest proto.InternalMessageInfo

func (m *GetPublicScreenShortcutImageRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetPublicScreenShortcutImageResponse struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ShortcutImageList    []*ShortcutImageMsg `protobuf:"bytes,2,rep,name=shortcut_image_list,json=shortcutImageList,proto3" json:"shortcut_image_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPublicScreenShortcutImageResponse) Reset()         { *m = GetPublicScreenShortcutImageResponse{} }
func (m *GetPublicScreenShortcutImageResponse) String() string { return proto.CompactTextString(m) }
func (*GetPublicScreenShortcutImageResponse) ProtoMessage()    {}
func (*GetPublicScreenShortcutImageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{62}
}
func (m *GetPublicScreenShortcutImageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPublicScreenShortcutImageResponse.Unmarshal(m, b)
}
func (m *GetPublicScreenShortcutImageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPublicScreenShortcutImageResponse.Marshal(b, m, deterministic)
}
func (dst *GetPublicScreenShortcutImageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPublicScreenShortcutImageResponse.Merge(dst, src)
}
func (m *GetPublicScreenShortcutImageResponse) XXX_Size() int {
	return xxx_messageInfo_GetPublicScreenShortcutImageResponse.Size(m)
}
func (m *GetPublicScreenShortcutImageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPublicScreenShortcutImageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetPublicScreenShortcutImageResponse proto.InternalMessageInfo

func (m *GetPublicScreenShortcutImageResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPublicScreenShortcutImageResponse) GetShortcutImageList() []*ShortcutImageMsg {
	if m != nil {
		return m.ShortcutImageList
	}
	return nil
}

type ShortcutImageMsg struct {
	ImageUrl             string   `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	ImageText            string   `protobuf:"bytes,2,opt,name=image_text,json=imageText,proto3" json:"image_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShortcutImageMsg) Reset()         { *m = ShortcutImageMsg{} }
func (m *ShortcutImageMsg) String() string { return proto.CompactTextString(m) }
func (*ShortcutImageMsg) ProtoMessage()    {}
func (*ShortcutImageMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{63}
}
func (m *ShortcutImageMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShortcutImageMsg.Unmarshal(m, b)
}
func (m *ShortcutImageMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShortcutImageMsg.Marshal(b, m, deterministic)
}
func (dst *ShortcutImageMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShortcutImageMsg.Merge(dst, src)
}
func (m *ShortcutImageMsg) XXX_Size() int {
	return xxx_messageInfo_ShortcutImageMsg.Size(m)
}
func (m *ShortcutImageMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ShortcutImageMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ShortcutImageMsg proto.InternalMessageInfo

func (m *ShortcutImageMsg) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *ShortcutImageMsg) GetImageText() string {
	if m != nil {
		return m.ImageText
	}
	return ""
}

type GetHiddenHomePageZoneConfigRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetHiddenHomePageZoneConfigRequest) Reset()         { *m = GetHiddenHomePageZoneConfigRequest{} }
func (m *GetHiddenHomePageZoneConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetHiddenHomePageZoneConfigRequest) ProtoMessage()    {}
func (*GetHiddenHomePageZoneConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{64}
}
func (m *GetHiddenHomePageZoneConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHiddenHomePageZoneConfigRequest.Unmarshal(m, b)
}
func (m *GetHiddenHomePageZoneConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHiddenHomePageZoneConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetHiddenHomePageZoneConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHiddenHomePageZoneConfigRequest.Merge(dst, src)
}
func (m *GetHiddenHomePageZoneConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetHiddenHomePageZoneConfigRequest.Size(m)
}
func (m *GetHiddenHomePageZoneConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHiddenHomePageZoneConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHiddenHomePageZoneConfigRequest proto.InternalMessageInfo

func (m *GetHiddenHomePageZoneConfigRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetHiddenHomePageZoneConfigResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ZoneIds              []string      `protobuf:"bytes,2,rep,name=zone_ids,json=zoneIds,proto3" json:"zone_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetHiddenHomePageZoneConfigResponse) Reset()         { *m = GetHiddenHomePageZoneConfigResponse{} }
func (m *GetHiddenHomePageZoneConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetHiddenHomePageZoneConfigResponse) ProtoMessage()    {}
func (*GetHiddenHomePageZoneConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{65}
}
func (m *GetHiddenHomePageZoneConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHiddenHomePageZoneConfigResponse.Unmarshal(m, b)
}
func (m *GetHiddenHomePageZoneConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHiddenHomePageZoneConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetHiddenHomePageZoneConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHiddenHomePageZoneConfigResponse.Merge(dst, src)
}
func (m *GetHiddenHomePageZoneConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetHiddenHomePageZoneConfigResponse.Size(m)
}
func (m *GetHiddenHomePageZoneConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHiddenHomePageZoneConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHiddenHomePageZoneConfigResponse proto.InternalMessageInfo

func (m *GetHiddenHomePageZoneConfigResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetHiddenHomePageZoneConfigResponse) GetZoneIds() []string {
	if m != nil {
		return m.ZoneIds
	}
	return nil
}

type GetHiddenChannelCategoryTypeConfigRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetHiddenChannelCategoryTypeConfigRequest) Reset() {
	*m = GetHiddenChannelCategoryTypeConfigRequest{}
}
func (m *GetHiddenChannelCategoryTypeConfigRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetHiddenChannelCategoryTypeConfigRequest) ProtoMessage() {}
func (*GetHiddenChannelCategoryTypeConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{66}
}
func (m *GetHiddenChannelCategoryTypeConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHiddenChannelCategoryTypeConfigRequest.Unmarshal(m, b)
}
func (m *GetHiddenChannelCategoryTypeConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHiddenChannelCategoryTypeConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetHiddenChannelCategoryTypeConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHiddenChannelCategoryTypeConfigRequest.Merge(dst, src)
}
func (m *GetHiddenChannelCategoryTypeConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetHiddenChannelCategoryTypeConfigRequest.Size(m)
}
func (m *GetHiddenChannelCategoryTypeConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHiddenChannelCategoryTypeConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHiddenChannelCategoryTypeConfigRequest proto.InternalMessageInfo

func (m *GetHiddenChannelCategoryTypeConfigRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetHiddenChannelCategoryTypeConfigResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CategoryIds          []uint32      `protobuf:"varint,2,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetHiddenChannelCategoryTypeConfigResponse) Reset() {
	*m = GetHiddenChannelCategoryTypeConfigResponse{}
}
func (m *GetHiddenChannelCategoryTypeConfigResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetHiddenChannelCategoryTypeConfigResponse) ProtoMessage() {}
func (*GetHiddenChannelCategoryTypeConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{67}
}
func (m *GetHiddenChannelCategoryTypeConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHiddenChannelCategoryTypeConfigResponse.Unmarshal(m, b)
}
func (m *GetHiddenChannelCategoryTypeConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHiddenChannelCategoryTypeConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetHiddenChannelCategoryTypeConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHiddenChannelCategoryTypeConfigResponse.Merge(dst, src)
}
func (m *GetHiddenChannelCategoryTypeConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetHiddenChannelCategoryTypeConfigResponse.Size(m)
}
func (m *GetHiddenChannelCategoryTypeConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHiddenChannelCategoryTypeConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHiddenChannelCategoryTypeConfigResponse proto.InternalMessageInfo

func (m *GetHiddenChannelCategoryTypeConfigResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetHiddenChannelCategoryTypeConfigResponse) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

type GetMuseRecommendEmojisRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ReqSource            uint32       `protobuf:"varint,2,opt,name=req_source,json=reqSource,proto3" json:"req_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMuseRecommendEmojisRequest) Reset()         { *m = GetMuseRecommendEmojisRequest{} }
func (m *GetMuseRecommendEmojisRequest) String() string { return proto.CompactTextString(m) }
func (*GetMuseRecommendEmojisRequest) ProtoMessage()    {}
func (*GetMuseRecommendEmojisRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{68}
}
func (m *GetMuseRecommendEmojisRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseRecommendEmojisRequest.Unmarshal(m, b)
}
func (m *GetMuseRecommendEmojisRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseRecommendEmojisRequest.Marshal(b, m, deterministic)
}
func (dst *GetMuseRecommendEmojisRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseRecommendEmojisRequest.Merge(dst, src)
}
func (m *GetMuseRecommendEmojisRequest) XXX_Size() int {
	return xxx_messageInfo_GetMuseRecommendEmojisRequest.Size(m)
}
func (m *GetMuseRecommendEmojisRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseRecommendEmojisRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseRecommendEmojisRequest proto.InternalMessageInfo

func (m *GetMuseRecommendEmojisRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMuseRecommendEmojisRequest) GetReqSource() uint32 {
	if m != nil {
		return m.ReqSource
	}
	return 0
}

type GetMuseRecommendEmojisResponse struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Emojis               []*CommonEmoji `protobuf:"bytes,2,rep,name=emojis,proto3" json:"emojis,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetMuseRecommendEmojisResponse) Reset()         { *m = GetMuseRecommendEmojisResponse{} }
func (m *GetMuseRecommendEmojisResponse) String() string { return proto.CompactTextString(m) }
func (*GetMuseRecommendEmojisResponse) ProtoMessage()    {}
func (*GetMuseRecommendEmojisResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{69}
}
func (m *GetMuseRecommendEmojisResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseRecommendEmojisResponse.Unmarshal(m, b)
}
func (m *GetMuseRecommendEmojisResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseRecommendEmojisResponse.Marshal(b, m, deterministic)
}
func (dst *GetMuseRecommendEmojisResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseRecommendEmojisResponse.Merge(dst, src)
}
func (m *GetMuseRecommendEmojisResponse) XXX_Size() int {
	return xxx_messageInfo_GetMuseRecommendEmojisResponse.Size(m)
}
func (m *GetMuseRecommendEmojisResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseRecommendEmojisResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseRecommendEmojisResponse proto.InternalMessageInfo

func (m *GetMuseRecommendEmojisResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMuseRecommendEmojisResponse) GetEmojis() []*CommonEmoji {
	if m != nil {
		return m.Emojis
	}
	return nil
}

type CommonEmoji struct {
	EmojiId              string     `protobuf:"bytes,1,opt,name=emoji_id,json=emojiId,proto3" json:"emoji_id,omitempty"`
	Md5                  string     `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	OriginEmojiInfo      *BaseEmoji `protobuf:"bytes,3,opt,name=origin_emoji_info,json=originEmojiInfo,proto3" json:"origin_emoji_info,omitempty"`
	ThumbEmojiInfo       *BaseEmoji `protobuf:"bytes,4,opt,name=thumb_emoji_info,json=thumbEmojiInfo,proto3" json:"thumb_emoji_info,omitempty"`
	IsCache              bool       `protobuf:"varint,5,opt,name=is_cache,json=isCache,proto3" json:"is_cache,omitempty"`
	ObsKey               string     `protobuf:"bytes,6,opt,name=obs_key,json=obsKey,proto3" json:"obs_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CommonEmoji) Reset()         { *m = CommonEmoji{} }
func (m *CommonEmoji) String() string { return proto.CompactTextString(m) }
func (*CommonEmoji) ProtoMessage()    {}
func (*CommonEmoji) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{70}
}
func (m *CommonEmoji) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonEmoji.Unmarshal(m, b)
}
func (m *CommonEmoji) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonEmoji.Marshal(b, m, deterministic)
}
func (dst *CommonEmoji) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonEmoji.Merge(dst, src)
}
func (m *CommonEmoji) XXX_Size() int {
	return xxx_messageInfo_CommonEmoji.Size(m)
}
func (m *CommonEmoji) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonEmoji.DiscardUnknown(m)
}

var xxx_messageInfo_CommonEmoji proto.InternalMessageInfo

func (m *CommonEmoji) GetEmojiId() string {
	if m != nil {
		return m.EmojiId
	}
	return ""
}

func (m *CommonEmoji) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *CommonEmoji) GetOriginEmojiInfo() *BaseEmoji {
	if m != nil {
		return m.OriginEmojiInfo
	}
	return nil
}

func (m *CommonEmoji) GetThumbEmojiInfo() *BaseEmoji {
	if m != nil {
		return m.ThumbEmojiInfo
	}
	return nil
}

func (m *CommonEmoji) GetIsCache() bool {
	if m != nil {
		return m.IsCache
	}
	return false
}

func (m *CommonEmoji) GetObsKey() string {
	if m != nil {
		return m.ObsKey
	}
	return ""
}

// 表情信息
type BaseEmoji struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Height               uint32   `protobuf:"varint,2,opt,name=height,proto3" json:"height,omitempty"`
	Width                uint32   `protobuf:"varint,3,opt,name=width,proto3" json:"width,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BaseEmoji) Reset()         { *m = BaseEmoji{} }
func (m *BaseEmoji) String() string { return proto.CompactTextString(m) }
func (*BaseEmoji) ProtoMessage()    {}
func (*BaseEmoji) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{71}
}
func (m *BaseEmoji) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseEmoji.Unmarshal(m, b)
}
func (m *BaseEmoji) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseEmoji.Marshal(b, m, deterministic)
}
func (dst *BaseEmoji) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseEmoji.Merge(dst, src)
}
func (m *BaseEmoji) XXX_Size() int {
	return xxx_messageInfo_BaseEmoji.Size(m)
}
func (m *BaseEmoji) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseEmoji.DiscardUnknown(m)
}

var xxx_messageInfo_BaseEmoji proto.InternalMessageInfo

func (m *BaseEmoji) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *BaseEmoji) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *BaseEmoji) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

type GetCustomIcebreakerPopupSwitchRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCustomIcebreakerPopupSwitchRequest) Reset()         { *m = GetCustomIcebreakerPopupSwitchRequest{} }
func (m *GetCustomIcebreakerPopupSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*GetCustomIcebreakerPopupSwitchRequest) ProtoMessage()    {}
func (*GetCustomIcebreakerPopupSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{72}
}
func (m *GetCustomIcebreakerPopupSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomIcebreakerPopupSwitchRequest.Unmarshal(m, b)
}
func (m *GetCustomIcebreakerPopupSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomIcebreakerPopupSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *GetCustomIcebreakerPopupSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomIcebreakerPopupSwitchRequest.Merge(dst, src)
}
func (m *GetCustomIcebreakerPopupSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_GetCustomIcebreakerPopupSwitchRequest.Size(m)
}
func (m *GetCustomIcebreakerPopupSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomIcebreakerPopupSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomIcebreakerPopupSwitchRequest proto.InternalMessageInfo

func (m *GetCustomIcebreakerPopupSwitchRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCustomIcebreakerPopupSwitchRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetCustomIcebreakerPopupSwitchResponse struct {
	BaseResp                   *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SwitchStatus               bool                        `protobuf:"varint,2,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	Question                   string                      `protobuf:"bytes,3,opt,name=question,proto3" json:"question,omitempty"`
	Intro                      string                      `protobuf:"bytes,4,opt,name=intro,proto3" json:"intro,omitempty"`
	IsAlreadySet               bool                        `protobuf:"varint,5,opt,name=is_already_set,json=isAlreadySet,proto3" json:"is_already_set,omitempty"`
	IceBreakerPublicSwitchInfo *IceBreakerPublicSwitchInfo `protobuf:"bytes,6,opt,name=ice_breaker_public_switch_info,json=iceBreakerPublicSwitchInfo,proto3" json:"ice_breaker_public_switch_info,omitempty"`
	IceBreakerQuestionType     int32                       `protobuf:"varint,7,opt,name=ice_breaker_question_type,json=iceBreakerQuestionType,proto3" json:"ice_breaker_question_type,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                    `json:"-"`
	XXX_unrecognized           []byte                      `json:"-"`
	XXX_sizecache              int32                       `json:"-"`
}

func (m *GetCustomIcebreakerPopupSwitchResponse) Reset() {
	*m = GetCustomIcebreakerPopupSwitchResponse{}
}
func (m *GetCustomIcebreakerPopupSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*GetCustomIcebreakerPopupSwitchResponse) ProtoMessage()    {}
func (*GetCustomIcebreakerPopupSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{73}
}
func (m *GetCustomIcebreakerPopupSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomIcebreakerPopupSwitchResponse.Unmarshal(m, b)
}
func (m *GetCustomIcebreakerPopupSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomIcebreakerPopupSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *GetCustomIcebreakerPopupSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomIcebreakerPopupSwitchResponse.Merge(dst, src)
}
func (m *GetCustomIcebreakerPopupSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_GetCustomIcebreakerPopupSwitchResponse.Size(m)
}
func (m *GetCustomIcebreakerPopupSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomIcebreakerPopupSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomIcebreakerPopupSwitchResponse proto.InternalMessageInfo

func (m *GetCustomIcebreakerPopupSwitchResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCustomIcebreakerPopupSwitchResponse) GetSwitchStatus() bool {
	if m != nil {
		return m.SwitchStatus
	}
	return false
}

func (m *GetCustomIcebreakerPopupSwitchResponse) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *GetCustomIcebreakerPopupSwitchResponse) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *GetCustomIcebreakerPopupSwitchResponse) GetIsAlreadySet() bool {
	if m != nil {
		return m.IsAlreadySet
	}
	return false
}

func (m *GetCustomIcebreakerPopupSwitchResponse) GetIceBreakerPublicSwitchInfo() *IceBreakerPublicSwitchInfo {
	if m != nil {
		return m.IceBreakerPublicSwitchInfo
	}
	return nil
}

func (m *GetCustomIcebreakerPopupSwitchResponse) GetIceBreakerQuestionType() int32 {
	if m != nil {
		return m.IceBreakerQuestionType
	}
	return 0
}

type SetCustomIcebreakerPopupSwitchRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SwitchStatus         bool         `protobuf:"varint,2,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetCustomIcebreakerPopupSwitchRequest) Reset()         { *m = SetCustomIcebreakerPopupSwitchRequest{} }
func (m *SetCustomIcebreakerPopupSwitchRequest) String() string { return proto.CompactTextString(m) }
func (*SetCustomIcebreakerPopupSwitchRequest) ProtoMessage()    {}
func (*SetCustomIcebreakerPopupSwitchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{74}
}
func (m *SetCustomIcebreakerPopupSwitchRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCustomIcebreakerPopupSwitchRequest.Unmarshal(m, b)
}
func (m *SetCustomIcebreakerPopupSwitchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCustomIcebreakerPopupSwitchRequest.Marshal(b, m, deterministic)
}
func (dst *SetCustomIcebreakerPopupSwitchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCustomIcebreakerPopupSwitchRequest.Merge(dst, src)
}
func (m *SetCustomIcebreakerPopupSwitchRequest) XXX_Size() int {
	return xxx_messageInfo_SetCustomIcebreakerPopupSwitchRequest.Size(m)
}
func (m *SetCustomIcebreakerPopupSwitchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCustomIcebreakerPopupSwitchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetCustomIcebreakerPopupSwitchRequest proto.InternalMessageInfo

func (m *SetCustomIcebreakerPopupSwitchRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetCustomIcebreakerPopupSwitchRequest) GetSwitchStatus() bool {
	if m != nil {
		return m.SwitchStatus
	}
	return false
}

func (m *SetCustomIcebreakerPopupSwitchRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SetCustomIcebreakerPopupSwitchResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetCustomIcebreakerPopupSwitchResponse) Reset() {
	*m = SetCustomIcebreakerPopupSwitchResponse{}
}
func (m *SetCustomIcebreakerPopupSwitchResponse) String() string { return proto.CompactTextString(m) }
func (*SetCustomIcebreakerPopupSwitchResponse) ProtoMessage()    {}
func (*SetCustomIcebreakerPopupSwitchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{75}
}
func (m *SetCustomIcebreakerPopupSwitchResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCustomIcebreakerPopupSwitchResponse.Unmarshal(m, b)
}
func (m *SetCustomIcebreakerPopupSwitchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCustomIcebreakerPopupSwitchResponse.Marshal(b, m, deterministic)
}
func (dst *SetCustomIcebreakerPopupSwitchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCustomIcebreakerPopupSwitchResponse.Merge(dst, src)
}
func (m *SetCustomIcebreakerPopupSwitchResponse) XXX_Size() int {
	return xxx_messageInfo_SetCustomIcebreakerPopupSwitchResponse.Size(m)
}
func (m *SetCustomIcebreakerPopupSwitchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCustomIcebreakerPopupSwitchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetCustomIcebreakerPopupSwitchResponse proto.InternalMessageInfo

func (m *SetCustomIcebreakerPopupSwitchResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type UpdateCustomIcebreakerPopupRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	QnaPair              *QnAPair     `protobuf:"bytes,2,opt,name=qna_pair,json=qnaPair,proto3" json:"qna_pair,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateCustomIcebreakerPopupRequest) Reset()         { *m = UpdateCustomIcebreakerPopupRequest{} }
func (m *UpdateCustomIcebreakerPopupRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateCustomIcebreakerPopupRequest) ProtoMessage()    {}
func (*UpdateCustomIcebreakerPopupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{76}
}
func (m *UpdateCustomIcebreakerPopupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCustomIcebreakerPopupRequest.Unmarshal(m, b)
}
func (m *UpdateCustomIcebreakerPopupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCustomIcebreakerPopupRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateCustomIcebreakerPopupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCustomIcebreakerPopupRequest.Merge(dst, src)
}
func (m *UpdateCustomIcebreakerPopupRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateCustomIcebreakerPopupRequest.Size(m)
}
func (m *UpdateCustomIcebreakerPopupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCustomIcebreakerPopupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCustomIcebreakerPopupRequest proto.InternalMessageInfo

func (m *UpdateCustomIcebreakerPopupRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdateCustomIcebreakerPopupRequest) GetQnaPair() *QnAPair {
	if m != nil {
		return m.QnaPair
	}
	return nil
}

func (m *UpdateCustomIcebreakerPopupRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type UpdateCustomIcebreakerPopupResponse struct {
	BaseResp                   *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Question                   string                      `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	SwitchStatus               bool                        `protobuf:"varint,3,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	IsAlreadySet               bool                        `protobuf:"varint,4,opt,name=is_already_set,json=isAlreadySet,proto3" json:"is_already_set,omitempty"`
	IceBreakerPublicSwitchInfo *IceBreakerPublicSwitchInfo `protobuf:"bytes,5,opt,name=ice_breaker_public_switch_info,json=iceBreakerPublicSwitchInfo,proto3" json:"ice_breaker_public_switch_info,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                    `json:"-"`
	XXX_unrecognized           []byte                      `json:"-"`
	XXX_sizecache              int32                       `json:"-"`
}

func (m *UpdateCustomIcebreakerPopupResponse) Reset()         { *m = UpdateCustomIcebreakerPopupResponse{} }
func (m *UpdateCustomIcebreakerPopupResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateCustomIcebreakerPopupResponse) ProtoMessage()    {}
func (*UpdateCustomIcebreakerPopupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{77}
}
func (m *UpdateCustomIcebreakerPopupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCustomIcebreakerPopupResponse.Unmarshal(m, b)
}
func (m *UpdateCustomIcebreakerPopupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCustomIcebreakerPopupResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateCustomIcebreakerPopupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCustomIcebreakerPopupResponse.Merge(dst, src)
}
func (m *UpdateCustomIcebreakerPopupResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateCustomIcebreakerPopupResponse.Size(m)
}
func (m *UpdateCustomIcebreakerPopupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCustomIcebreakerPopupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCustomIcebreakerPopupResponse proto.InternalMessageInfo

func (m *UpdateCustomIcebreakerPopupResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UpdateCustomIcebreakerPopupResponse) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *UpdateCustomIcebreakerPopupResponse) GetSwitchStatus() bool {
	if m != nil {
		return m.SwitchStatus
	}
	return false
}

func (m *UpdateCustomIcebreakerPopupResponse) GetIsAlreadySet() bool {
	if m != nil {
		return m.IsAlreadySet
	}
	return false
}

func (m *UpdateCustomIcebreakerPopupResponse) GetIceBreakerPublicSwitchInfo() *IceBreakerPublicSwitchInfo {
	if m != nil {
		return m.IceBreakerPublicSwitchInfo
	}
	return nil
}

type GetCustomIcebreakerConfigInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCustomIcebreakerConfigInfoRequest) Reset()         { *m = GetCustomIcebreakerConfigInfoRequest{} }
func (m *GetCustomIcebreakerConfigInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetCustomIcebreakerConfigInfoRequest) ProtoMessage()    {}
func (*GetCustomIcebreakerConfigInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{78}
}
func (m *GetCustomIcebreakerConfigInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomIcebreakerConfigInfoRequest.Unmarshal(m, b)
}
func (m *GetCustomIcebreakerConfigInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomIcebreakerConfigInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetCustomIcebreakerConfigInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomIcebreakerConfigInfoRequest.Merge(dst, src)
}
func (m *GetCustomIcebreakerConfigInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetCustomIcebreakerConfigInfoRequest.Size(m)
}
func (m *GetCustomIcebreakerConfigInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomIcebreakerConfigInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomIcebreakerConfigInfoRequest proto.InternalMessageInfo

func (m *GetCustomIcebreakerConfigInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCustomIcebreakerConfigInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetCustomIcebreakerConfigInfoResponse struct {
	BaseResp                        *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CurrentQnaPair                  *QnAPair      `protobuf:"bytes,2,opt,name=current_qna_pair,json=currentQnaPair,proto3" json:"current_qna_pair,omitempty"`
	ConfigQuestion                  []string      `protobuf:"bytes,3,rep,name=config_question,json=configQuestion,proto3" json:"config_question,omitempty"`
	ConfigAnswer                    []string      `protobuf:"bytes,4,rep,name=config_answer,json=configAnswer,proto3" json:"config_answer,omitempty"`
	ConfigurableAnswersNumber       uint32        `protobuf:"varint,5,opt,name=configurable_answers_number,json=configurableAnswersNumber,proto3" json:"configurable_answers_number,omitempty"`
	RecommendedAnswersDisplayNumber uint32        `protobuf:"varint,6,opt,name=recommended_answers_display_number,json=recommendedAnswersDisplayNumber,proto3" json:"recommended_answers_display_number,omitempty"`
	ConfigAnswerInfo                []*AnswerInfo `protobuf:"bytes,7,rep,name=config_answer_info,json=configAnswerInfo,proto3" json:"config_answer_info,omitempty"`
	IceBreakerQuestionType          int32         `protobuf:"varint,8,opt,name=ice_breaker_question_type,json=iceBreakerQuestionType,proto3" json:"ice_breaker_question_type,omitempty"`
	IsAlreadySet                    bool          `protobuf:"varint,9,opt,name=is_already_set,json=isAlreadySet,proto3" json:"is_already_set,omitempty"`
	XXX_NoUnkeyedLiteral            struct{}      `json:"-"`
	XXX_unrecognized                []byte        `json:"-"`
	XXX_sizecache                   int32         `json:"-"`
}

func (m *GetCustomIcebreakerConfigInfoResponse) Reset()         { *m = GetCustomIcebreakerConfigInfoResponse{} }
func (m *GetCustomIcebreakerConfigInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetCustomIcebreakerConfigInfoResponse) ProtoMessage()    {}
func (*GetCustomIcebreakerConfigInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{79}
}
func (m *GetCustomIcebreakerConfigInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomIcebreakerConfigInfoResponse.Unmarshal(m, b)
}
func (m *GetCustomIcebreakerConfigInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomIcebreakerConfigInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetCustomIcebreakerConfigInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomIcebreakerConfigInfoResponse.Merge(dst, src)
}
func (m *GetCustomIcebreakerConfigInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetCustomIcebreakerConfigInfoResponse.Size(m)
}
func (m *GetCustomIcebreakerConfigInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomIcebreakerConfigInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomIcebreakerConfigInfoResponse proto.InternalMessageInfo

func (m *GetCustomIcebreakerConfigInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCustomIcebreakerConfigInfoResponse) GetCurrentQnaPair() *QnAPair {
	if m != nil {
		return m.CurrentQnaPair
	}
	return nil
}

func (m *GetCustomIcebreakerConfigInfoResponse) GetConfigQuestion() []string {
	if m != nil {
		return m.ConfigQuestion
	}
	return nil
}

func (m *GetCustomIcebreakerConfigInfoResponse) GetConfigAnswer() []string {
	if m != nil {
		return m.ConfigAnswer
	}
	return nil
}

func (m *GetCustomIcebreakerConfigInfoResponse) GetConfigurableAnswersNumber() uint32 {
	if m != nil {
		return m.ConfigurableAnswersNumber
	}
	return 0
}

func (m *GetCustomIcebreakerConfigInfoResponse) GetRecommendedAnswersDisplayNumber() uint32 {
	if m != nil {
		return m.RecommendedAnswersDisplayNumber
	}
	return 0
}

func (m *GetCustomIcebreakerConfigInfoResponse) GetConfigAnswerInfo() []*AnswerInfo {
	if m != nil {
		return m.ConfigAnswerInfo
	}
	return nil
}

func (m *GetCustomIcebreakerConfigInfoResponse) GetIceBreakerQuestionType() int32 {
	if m != nil {
		return m.IceBreakerQuestionType
	}
	return 0
}

func (m *GetCustomIcebreakerConfigInfoResponse) GetIsAlreadySet() bool {
	if m != nil {
		return m.IsAlreadySet
	}
	return false
}

type IceBreakerPushMsg struct {
	Source               int32    `protobuf:"varint,1,opt,name=source,proto3" json:"source,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IceBreakerPushMsg) Reset()         { *m = IceBreakerPushMsg{} }
func (m *IceBreakerPushMsg) String() string { return proto.CompactTextString(m) }
func (*IceBreakerPushMsg) ProtoMessage()    {}
func (*IceBreakerPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{80}
}
func (m *IceBreakerPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IceBreakerPushMsg.Unmarshal(m, b)
}
func (m *IceBreakerPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IceBreakerPushMsg.Marshal(b, m, deterministic)
}
func (dst *IceBreakerPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IceBreakerPushMsg.Merge(dst, src)
}
func (m *IceBreakerPushMsg) XXX_Size() int {
	return xxx_messageInfo_IceBreakerPushMsg.Size(m)
}
func (m *IceBreakerPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_IceBreakerPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_IceBreakerPushMsg proto.InternalMessageInfo

func (m *IceBreakerPushMsg) GetSource() int32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *IceBreakerPushMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type IceBreakerPublicSwitchInfo struct {
	SwitchStatus         bool     `protobuf:"varint,2,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	Question             string   `protobuf:"bytes,3,opt,name=question,proto3" json:"question,omitempty"`
	Intro                string   `protobuf:"bytes,4,opt,name=intro,proto3" json:"intro,omitempty"`
	IsAlreadySet         bool     `protobuf:"varint,5,opt,name=is_already_set,json=isAlreadySet,proto3" json:"is_already_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IceBreakerPublicSwitchInfo) Reset()         { *m = IceBreakerPublicSwitchInfo{} }
func (m *IceBreakerPublicSwitchInfo) String() string { return proto.CompactTextString(m) }
func (*IceBreakerPublicSwitchInfo) ProtoMessage()    {}
func (*IceBreakerPublicSwitchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_interest_hub_logic_47e4f97931216f65, []int{81}
}
func (m *IceBreakerPublicSwitchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IceBreakerPublicSwitchInfo.Unmarshal(m, b)
}
func (m *IceBreakerPublicSwitchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IceBreakerPublicSwitchInfo.Marshal(b, m, deterministic)
}
func (dst *IceBreakerPublicSwitchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IceBreakerPublicSwitchInfo.Merge(dst, src)
}
func (m *IceBreakerPublicSwitchInfo) XXX_Size() int {
	return xxx_messageInfo_IceBreakerPublicSwitchInfo.Size(m)
}
func (m *IceBreakerPublicSwitchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_IceBreakerPublicSwitchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_IceBreakerPublicSwitchInfo proto.InternalMessageInfo

func (m *IceBreakerPublicSwitchInfo) GetSwitchStatus() bool {
	if m != nil {
		return m.SwitchStatus
	}
	return false
}

func (m *IceBreakerPublicSwitchInfo) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *IceBreakerPublicSwitchInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *IceBreakerPublicSwitchInfo) GetIsAlreadySet() bool {
	if m != nil {
		return m.IsAlreadySet
	}
	return false
}

func init() {
	proto.RegisterType((*MuseCommonReportRequest)(nil), "ga.muse_interest_hub_logic.MuseCommonReportRequest")
	proto.RegisterType((*MuseCommonReportRequest_UserLocationAuth)(nil), "ga.muse_interest_hub_logic.MuseCommonReportRequest.UserLocationAuth")
	proto.RegisterType((*MuseCommonReportRequest_UserStayInChannel)(nil), "ga.muse_interest_hub_logic.MuseCommonReportRequest.UserStayInChannel")
	proto.RegisterType((*MuseCommonReportRequest_UserOpenMicAndVoice)(nil), "ga.muse_interest_hub_logic.MuseCommonReportRequest.UserOpenMicAndVoice")
	proto.RegisterType((*MuseCommonReportRequest_UserLocationFlashChatCpToday)(nil), "ga.muse_interest_hub_logic.MuseCommonReportRequest.UserLocationFlashChatCpToday")
	proto.RegisterType((*MuseCommonReportRequest_SocialCommunityChannelTask)(nil), "ga.muse_interest_hub_logic.MuseCommonReportRequest.SocialCommunityChannelTask")
	proto.RegisterType((*MuseCommonReportRequest_SocialCommunityOpenMicTask)(nil), "ga.muse_interest_hub_logic.MuseCommonReportRequest.SocialCommunityOpenMicTask")
	proto.RegisterType((*MuseMicUserInfo)(nil), "ga.muse_interest_hub_logic.MuseMicUserInfo")
	proto.RegisterType((*MuseCommonReportResponse)(nil), "ga.muse_interest_hub_logic.MuseCommonReportResponse")
	proto.RegisterType((*GetMuseSwitchHubRequest)(nil), "ga.muse_interest_hub_logic.GetMuseSwitchHubRequest")
	proto.RegisterType((*GetMuseSwitchHubResponse)(nil), "ga.muse_interest_hub_logic.GetMuseSwitchHubResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "ga.muse_interest_hub_logic.GetMuseSwitchHubResponse.IsOpenMapEntry")
	proto.RegisterType((*SetMuseSwitchHubRequest)(nil), "ga.muse_interest_hub_logic.SetMuseSwitchHubRequest")
	proto.RegisterType((*SetMuseSwitchHubResponse)(nil), "ga.muse_interest_hub_logic.SetMuseSwitchHubResponse")
	proto.RegisterType((*UserCityInChannel)(nil), "ga.muse_interest_hub_logic.UserCityInChannel")
	proto.RegisterType((*TopicChannelSameCityInfo)(nil), "ga.muse_interest_hub_logic.TopicChannelSameCityInfo")
	proto.RegisterType((*CheckJumpSquarePageRequest)(nil), "ga.muse_interest_hub_logic.CheckJumpSquarePageRequest")
	proto.RegisterType((*CheckJumpSquarePageResponse)(nil), "ga.muse_interest_hub_logic.CheckJumpSquarePageResponse")
	proto.RegisterType((*GetMTExperimentStrategyRequest)(nil), "ga.muse_interest_hub_logic.GetMTExperimentStrategyRequest")
	proto.RegisterType((*GetMTExperimentStrategyResponse)(nil), "ga.muse_interest_hub_logic.GetMTExperimentStrategyResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.muse_interest_hub_logic.GetMTExperimentStrategyResponse.StrategyValueMapEntry")
	proto.RegisterType((*GetHomeFollowFloatRequest)(nil), "ga.muse_interest_hub_logic.GetHomeFollowFloatRequest")
	proto.RegisterType((*UserSimpleInfo)(nil), "ga.muse_interest_hub_logic.UserSimpleInfo")
	proto.RegisterType((*GetHomeFollowFloatResponse)(nil), "ga.muse_interest_hub_logic.GetHomeFollowFloatResponse")
	proto.RegisterType((*FollowUserChannelInfo)(nil), "ga.muse_interest_hub_logic.FollowUserChannelInfo")
	proto.RegisterType((*FollowUserChannelInfoV2)(nil), "ga.muse_interest_hub_logic.FollowUserChannelInfoV2")
	proto.RegisterType((*FollowChannelHalfScreenUserListRequest)(nil), "ga.muse_interest_hub_logic.FollowChannelHalfScreenUserListRequest")
	proto.RegisterType((*InterestHubPersonalCert)(nil), "ga.muse_interest_hub_logic.InterestHubPersonalCert")
	proto.RegisterType((*FollowFriendsChannelSimpleInfo)(nil), "ga.muse_interest_hub_logic.FollowFriendsChannelSimpleInfo")
	proto.RegisterType((*FriendRcmdInfo)(nil), "ga.muse_interest_hub_logic.FriendRcmdInfo")
	proto.RegisterType((*UserSimpleMegaphoneInfo)(nil), "ga.muse_interest_hub_logic.UserSimpleMegaphoneInfo")
	proto.RegisterType((*FollowChannelHalfScreenUserList)(nil), "ga.muse_interest_hub_logic.FollowChannelHalfScreenUserList")
	proto.RegisterType((*FollowChannelHalfScreenUserListResponse)(nil), "ga.muse_interest_hub_logic.FollowChannelHalfScreenUserListResponse")
	proto.RegisterType((*ChannelInviteFriendPreprocessingRequest)(nil), "ga.muse_interest_hub_logic.ChannelInviteFriendPreprocessingRequest")
	proto.RegisterType((*ChannelInviteFriendPreprocessingResponse)(nil), "ga.muse_interest_hub_logic.ChannelInviteFriendPreprocessingResponse")
	proto.RegisterType((*ChannelSpecialFriendListRequest)(nil), "ga.muse_interest_hub_logic.ChannelSpecialFriendListRequest")
	proto.RegisterType((*ChannelSpecialFriendListResponse)(nil), "ga.muse_interest_hub_logic.ChannelSpecialFriendListResponse")
	proto.RegisterType((*OneClickInviteAllInfoRequest)(nil), "ga.muse_interest_hub_logic.OneClickInviteAllInfoRequest")
	proto.RegisterType((*OneClickInviteAllInfoResponse)(nil), "ga.muse_interest_hub_logic.OneClickInviteAllInfoResponse")
	proto.RegisterType((*OneClickInviteAllRequest)(nil), "ga.muse_interest_hub_logic.OneClickInviteAllRequest")
	proto.RegisterType((*OneClickInviteAllResponse)(nil), "ga.muse_interest_hub_logic.OneClickInviteAllResponse")
	proto.RegisterType((*OneClickInviteMsg)(nil), "ga.muse_interest_hub_logic.OneClickInviteMsg")
	proto.RegisterType((*CheckIsInOtherUsersBlackListRequest)(nil), "ga.muse_interest_hub_logic.CheckIsInOtherUsersBlackListRequest")
	proto.RegisterType((*CheckIsInOtherUsersBlackListResponse)(nil), "ga.muse_interest_hub_logic.CheckIsInOtherUsersBlackListResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "ga.muse_interest_hub_logic.CheckIsInOtherUsersBlackListResponse.BlackListUserMapEntry")
	proto.RegisterType((*GetUserCurrentChannelIdRequest)(nil), "ga.muse_interest_hub_logic.GetUserCurrentChannelIdRequest")
	proto.RegisterType((*GetUserCurrentChannelIdResponse)(nil), "ga.muse_interest_hub_logic.GetUserCurrentChannelIdResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.muse_interest_hub_logic.GetUserCurrentChannelIdResponse.UserCurrentChannelMapEntry")
	proto.RegisterType((*OneClickInviteAllGetMicIdRequest)(nil), "ga.muse_interest_hub_logic.OneClickInviteAllGetMicIdRequest")
	proto.RegisterType((*OneClickInviteAllGetMicIdResponse)(nil), "ga.muse_interest_hub_logic.OneClickInviteAllGetMicIdResponse")
	proto.RegisterType((*AutoInviteMicPanelRequest)(nil), "ga.muse_interest_hub_logic.AutoInviteMicPanelRequest")
	proto.RegisterType((*AutoInviteMicPanelResponse)(nil), "ga.muse_interest_hub_logic.AutoInviteMicPanelResponse")
	proto.RegisterType((*MuseCommonXMLMsgNotify)(nil), "ga.muse_interest_hub_logic.MuseCommonXMLMsgNotify")
	proto.RegisterType((*HoldUserOnMicMsg)(nil), "ga.muse_interest_hub_logic.HoldUserOnMicMsg")
	proto.RegisterType((*GetUserRelationshipRequest)(nil), "ga.muse_interest_hub_logic.GetUserRelationshipRequest")
	proto.RegisterType((*GetUserRelationshipResponse)(nil), "ga.muse_interest_hub_logic.GetUserRelationshipResponse")
	proto.RegisterType((*NonMicUserListOrderRequest)(nil), "ga.muse_interest_hub_logic.NonMicUserListOrderRequest")
	proto.RegisterType((*NonMicUserListOrderResponse)(nil), "ga.muse_interest_hub_logic.NonMicUserListOrderResponse")
	proto.RegisterType((*NonMicUserListOrderResponse_User)(nil), "ga.muse_interest_hub_logic.NonMicUserListOrderResponse.User")
	proto.RegisterType((*ShowRoomExitFollowPopupRequest)(nil), "ga.muse_interest_hub_logic.ShowRoomExitFollowPopupRequest")
	proto.RegisterType((*ShowRoomExitFollowPopupResponse)(nil), "ga.muse_interest_hub_logic.ShowRoomExitFollowPopupResponse")
	proto.RegisterType((*GetRoomExitGuideFollowListRequest)(nil), "ga.muse_interest_hub_logic.GetRoomExitGuideFollowListRequest")
	proto.RegisterType((*GetRoomExitGuideFollowListResponse)(nil), "ga.muse_interest_hub_logic.GetRoomExitGuideFollowListResponse")
	proto.RegisterType((*GuideFollowUser)(nil), "ga.muse_interest_hub_logic.GuideFollowUser")
	proto.RegisterType((*AdminInfo)(nil), "ga.muse_interest_hub_logic.AdminInfo")
	proto.RegisterType((*GetRoomIcebreakerPopupRequest)(nil), "ga.muse_interest_hub_logic.GetRoomIcebreakerPopupRequest")
	proto.RegisterType((*QnAPair)(nil), "ga.muse_interest_hub_logic.QnAPair")
	proto.RegisterType((*AnswerInfo)(nil), "ga.muse_interest_hub_logic.AnswerInfo")
	proto.RegisterType((*GetRoomIcebreakerPopupResponse)(nil), "ga.muse_interest_hub_logic.GetRoomIcebreakerPopupResponse")
	proto.RegisterType((*PushLikeActivityMsg)(nil), "ga.muse_interest_hub_logic.PushLikeActivityMsg")
	proto.RegisterType((*SendPublicMessageDirectlyRequest)(nil), "ga.muse_interest_hub_logic.SendPublicMessageDirectlyRequest")
	proto.RegisterType((*SendPublicMessageDirectlyResponse)(nil), "ga.muse_interest_hub_logic.SendPublicMessageDirectlyResponse")
	proto.RegisterType((*GetPublicScreenShortcutImageRequest)(nil), "ga.muse_interest_hub_logic.GetPublicScreenShortcutImageRequest")
	proto.RegisterType((*GetPublicScreenShortcutImageResponse)(nil), "ga.muse_interest_hub_logic.GetPublicScreenShortcutImageResponse")
	proto.RegisterType((*ShortcutImageMsg)(nil), "ga.muse_interest_hub_logic.ShortcutImageMsg")
	proto.RegisterType((*GetHiddenHomePageZoneConfigRequest)(nil), "ga.muse_interest_hub_logic.GetHiddenHomePageZoneConfigRequest")
	proto.RegisterType((*GetHiddenHomePageZoneConfigResponse)(nil), "ga.muse_interest_hub_logic.GetHiddenHomePageZoneConfigResponse")
	proto.RegisterType((*GetHiddenChannelCategoryTypeConfigRequest)(nil), "ga.muse_interest_hub_logic.GetHiddenChannelCategoryTypeConfigRequest")
	proto.RegisterType((*GetHiddenChannelCategoryTypeConfigResponse)(nil), "ga.muse_interest_hub_logic.GetHiddenChannelCategoryTypeConfigResponse")
	proto.RegisterType((*GetMuseRecommendEmojisRequest)(nil), "ga.muse_interest_hub_logic.GetMuseRecommendEmojisRequest")
	proto.RegisterType((*GetMuseRecommendEmojisResponse)(nil), "ga.muse_interest_hub_logic.GetMuseRecommendEmojisResponse")
	proto.RegisterType((*CommonEmoji)(nil), "ga.muse_interest_hub_logic.CommonEmoji")
	proto.RegisterType((*BaseEmoji)(nil), "ga.muse_interest_hub_logic.BaseEmoji")
	proto.RegisterType((*GetCustomIcebreakerPopupSwitchRequest)(nil), "ga.muse_interest_hub_logic.GetCustomIcebreakerPopupSwitchRequest")
	proto.RegisterType((*GetCustomIcebreakerPopupSwitchResponse)(nil), "ga.muse_interest_hub_logic.GetCustomIcebreakerPopupSwitchResponse")
	proto.RegisterType((*SetCustomIcebreakerPopupSwitchRequest)(nil), "ga.muse_interest_hub_logic.SetCustomIcebreakerPopupSwitchRequest")
	proto.RegisterType((*SetCustomIcebreakerPopupSwitchResponse)(nil), "ga.muse_interest_hub_logic.SetCustomIcebreakerPopupSwitchResponse")
	proto.RegisterType((*UpdateCustomIcebreakerPopupRequest)(nil), "ga.muse_interest_hub_logic.UpdateCustomIcebreakerPopupRequest")
	proto.RegisterType((*UpdateCustomIcebreakerPopupResponse)(nil), "ga.muse_interest_hub_logic.UpdateCustomIcebreakerPopupResponse")
	proto.RegisterType((*GetCustomIcebreakerConfigInfoRequest)(nil), "ga.muse_interest_hub_logic.GetCustomIcebreakerConfigInfoRequest")
	proto.RegisterType((*GetCustomIcebreakerConfigInfoResponse)(nil), "ga.muse_interest_hub_logic.GetCustomIcebreakerConfigInfoResponse")
	proto.RegisterType((*IceBreakerPushMsg)(nil), "ga.muse_interest_hub_logic.IceBreakerPushMsg")
	proto.RegisterType((*IceBreakerPublicSwitchInfo)(nil), "ga.muse_interest_hub_logic.IceBreakerPublicSwitchInfo")
	proto.RegisterEnum("ga.muse_interest_hub_logic.SwitchHubType", SwitchHubType_name, SwitchHubType_value)
	proto.RegisterEnum("ga.muse_interest_hub_logic.STRATEGY_TYPE", STRATEGY_TYPE_name, STRATEGY_TYPE_value)
	proto.RegisterEnum("ga.muse_interest_hub_logic.STRATEGY_TYPE_VALUE", STRATEGY_TYPE_VALUE_name, STRATEGY_TYPE_VALUE_value)
	proto.RegisterEnum("ga.muse_interest_hub_logic.MuseCommonXMLMsgType", MuseCommonXMLMsgType_name, MuseCommonXMLMsgType_value)
	proto.RegisterEnum("ga.muse_interest_hub_logic.MuseCommonXMLMsgContentType", MuseCommonXMLMsgContentType_name, MuseCommonXMLMsgContentType_value)
	proto.RegisterEnum("ga.muse_interest_hub_logic.CHANNEL_ADMIN_TYPE", CHANNEL_ADMIN_TYPE_name, CHANNEL_ADMIN_TYPE_value)
	proto.RegisterEnum("ga.muse_interest_hub_logic.FOLLOW_STATUS_TYPE", FOLLOW_STATUS_TYPE_name, FOLLOW_STATUS_TYPE_value)
	proto.RegisterEnum("ga.muse_interest_hub_logic.IceBreakerPushSource", IceBreakerPushSource_name, IceBreakerPushSource_value)
	proto.RegisterEnum("ga.muse_interest_hub_logic.IceBreakerQuestionType", IceBreakerQuestionType_name, IceBreakerQuestionType_value)
}

func init() {
	proto.RegisterFile("muse_interest_hub_logic/muse_interest_hub_logic.proto", fileDescriptor_muse_interest_hub_logic_47e4f97931216f65)
}

var fileDescriptor_muse_interest_hub_logic_47e4f97931216f65 = []byte{
	// 4946 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3c, 0x4b, 0x8f, 0x1b, 0xc9,
	0x79, 0xdb, 0x9c, 0x17, 0xf9, 0xcd, 0xab, 0xa7, 0xa5, 0x95, 0x46, 0x94, 0x56, 0x8f, 0xd6, 0xae,
	0xa4, 0x1d, 0xaf, 0x66, 0x6d, 0xf9, 0x81, 0xc4, 0x36, 0x6c, 0x73, 0x38, 0x2d, 0xb2, 0x57, 0x7c,
	0xa9, 0x9b, 0x1c, 0xad, 0x0c, 0x07, 0xe5, 0x66, 0x77, 0x0d, 0x59, 0x1e, 0xb2, 0x9b, 0xea, 0x87,
	0x24, 0x6e, 0x02, 0x1b, 0x88, 0x83, 0x38, 0x07, 0x03, 0x01, 0xe2, 0x38, 0x40, 0x12, 0xc4, 0x41,
	0x72, 0x08, 0x0c, 0x04, 0x88, 0x61, 0xf8, 0x07, 0xe4, 0x12, 0x04, 0x01, 0x72, 0x49, 0xee, 0xb9,
	0xe4, 0x16, 0xf8, 0x07, 0x24, 0xd7, 0xa0, 0x1e, 0xdd, 0x7c, 0x93, 0xe2, 0x68, 0x92, 0x00, 0x39,
	0x89, 0xf5, 0xd5, 0x57, 0xdf, 0xf7, 0xd5, 0xf7, 0xae, 0xaa, 0xd6, 0xc0, 0x97, 0xbb, 0x51, 0x80,
	0x11, 0x71, 0x43, 0xec, 0xe3, 0x20, 0x44, 0xed, 0xa8, 0x89, 0x3a, 0x5e, 0x8b, 0xd8, 0x1f, 0xcf,
	0x80, 0x1f, 0xf6, 0x7c, 0x2f, 0xf4, 0x94, 0x6c, 0xcb, 0x3a, 0x9c, 0x81, 0x91, 0xdd, 0x6e, 0x59,
	0xa8, 0x69, 0x05, 0x98, 0xa3, 0xaa, 0xff, 0xb9, 0x05, 0x57, 0xcb, 0x51, 0x80, 0xf3, 0x5e, 0xb7,
	0xeb, 0xb9, 0x06, 0xee, 0x79, 0x7e, 0x68, 0xe0, 0x17, 0x11, 0x0e, 0x42, 0xe5, 0x1e, 0xa4, 0x29,
	0x26, 0xf2, 0xf1, 0x8b, 0x7d, 0xe9, 0xb6, 0xf4, 0x60, 0xf3, 0xd1, 0xe6, 0x61, 0xcb, 0x3a, 0x3c,
	0xb2, 0x02, 0x6c, 0xe0, 0x17, 0xc6, 0x46, 0x93, 0xff, 0x50, 0x42, 0x50, 0xa2, 0x00, 0xfb, 0xa8,
	0xe3, 0xd9, 0x56, 0x48, 0x3c, 0x17, 0x59, 0x51, 0xd8, 0xde, 0x4f, 0xb1, 0x15, 0xc7, 0x87, 0xb3,
	0x65, 0x39, 0x9c, 0xc1, 0xf8, 0xb0, 0x11, 0x60, 0xbf, 0x24, 0x88, 0xe5, 0xa2, 0xb0, 0x5d, 0x7c,
	0xc7, 0x90, 0xa3, 0x31, 0x98, 0xf2, 0x1a, 0x2e, 0x33, 0xae, 0x41, 0x68, 0xf5, 0x11, 0x71, 0x91,
	0xdd, 0xb6, 0x5c, 0x17, 0x77, 0xf6, 0x57, 0x18, 0x5f, 0xed, 0xbc, 0x7c, 0xcd, 0xd0, 0xea, 0xeb,
	0x6e, 0x9e, 0x13, 0x2b, 0xbe, 0x63, 0xec, 0x45, 0xe3, 0x40, 0xe5, 0x07, 0x70, 0x95, 0x71, 0xf6,
	0x7a, 0xd8, 0x45, 0x5d, 0x62, 0x23, 0xcb, 0x75, 0xd0, 0x4b, 0x8f, 0xd8, 0x78, 0x7f, 0x95, 0x31,
	0x2f, 0x9c, 0x97, 0x79, 0xb5, 0x87, 0xdd, 0x32, 0xb1, 0x73, 0xae, 0x73, 0x42, 0xc9, 0x15, 0xdf,
	0x31, 0x2e, 0x45, 0x93, 0x60, 0xe5, 0xcf, 0x25, 0xb8, 0x33, 0xaa, 0xf1, 0xd3, 0x8e, 0x15, 0xb4,
	0xa9, 0x0a, 0x42, 0x64, 0xf7, 0x50, 0xe8, 0x39, 0x56, 0x7f, 0x7f, 0x8d, 0xc9, 0x52, 0x7b, 0x5b,
	0x03, 0x3c, 0xa6, 0xa4, 0xf3, 0x6d, 0x2b, 0xcc, 0xf7, 0xea, 0x94, 0x6e, 0xf1, 0x1d, 0xe3, 0x46,
	0x34, 0x67, 0x5e, 0xf9, 0x89, 0x04, 0xef, 0x05, 0x9e, 0x4d, 0xac, 0x0e, 0xb2, 0xbd, 0x6e, 0x37,
	0x72, 0x49, 0xd8, 0x8f, 0xad, 0x83, 0x42, 0x2b, 0x38, 0xdb, 0x5f, 0x67, 0x92, 0x55, 0xce, 0x23,
	0x99, 0xc9, 0x08, 0xe7, 0x63, 0xba, 0xc2, 0x24, 0x75, 0x2b, 0x38, 0x2b, 0xbe, 0x63, 0x64, 0x83,
	0x99, 0xb3, 0xca, 0x1f, 0x4b, 0x70, 0x73, 0x42, 0xaa, 0xc4, 0x82, 0x4c, 0xac, 0x8d, 0x0b, 0x13,
	0x4b, 0x18, 0x6c, 0x86, 0x58, 0x43, 0xb3, 0xd9, 0x2f, 0x81, 0x3c, 0xee, 0xed, 0xca, 0x6d, 0xd8,
	0x22, 0x01, 0x97, 0x8d, 0x45, 0x12, 0x8d, 0xbd, 0xb4, 0x01, 0x24, 0xa0, 0x0b, 0x29, 0x46, 0xf6,
	0x11, 0xec, 0x4d, 0xf8, 0xaa, 0xf2, 0x1e, 0x40, 0xac, 0x65, 0xe2, 0xb0, 0x45, 0xdb, 0x46, 0x46,
	0x40, 0x74, 0x27, 0xfb, 0x7d, 0xb8, 0x34, 0xc5, 0xc5, 0x16, 0xac, 0x52, 0x8a, 0x90, 0xe1, 0x9e,
	0x46, 0x82, 0x70, 0x3f, 0x75, 0x7b, 0xe5, 0xc1, 0xe6, 0xa3, 0xcf, 0x2d, 0x52, 0x50, 0x99, 0xd8,
	0x94, 0x93, 0xee, 0x9e, 0x7a, 0x46, 0x9a, 0xb9, 0x0a, 0x09, 0xc2, 0xec, 0x2f, 0x53, 0x70, 0x63,
	0x9e, 0x5f, 0x2d, 0xde, 0xb6, 0x72, 0x03, 0x32, 0x1d, 0xcf, 0x6d, 0x91, 0x30, 0x72, 0x30, 0xcb,
	0x2f, 0x29, 0x63, 0x00, 0x50, 0xb2, 0x90, 0xee, 0x58, 0x21, 0x9f, 0x5c, 0x61, 0x93, 0xc9, 0x58,
	0x51, 0x60, 0xd5, 0x26, 0x61, 0x9f, 0xc5, 0x67, 0xc6, 0x60, 0xbf, 0x29, 0x7e, 0xcf, 0xf7, 0x5e,
	0x12, 0xd7, 0xc6, 0x2c, 0x56, 0x32, 0x46, 0x32, 0x56, 0xee, 0xc0, 0x56, 0x42, 0x18, 0xbd, 0x7c,
	0xc4, 0x3c, 0x56, 0x32, 0x36, 0x13, 0xd8, 0xc9, 0x23, 0xe5, 0x16, 0x6c, 0xc6, 0xe4, 0x29, 0xc6,
	0x06, 0xc3, 0x80, 0x18, 0x74, 0xf2, 0x48, 0xb9, 0x0f, 0x72, 0xb2, 0x9f, 0x5e, 0x8f, 0xef, 0x29,
	0xcd, 0xf6, 0xb4, 0x2d, 0xf6, 0xd4, 0xeb, 0xb1, 0x6d, 0xed, 0xc3, 0x86, 0xed, 0x45, 0x6e, 0xe8,
	0xf7, 0xf7, 0x33, 0x4c, 0x8e, 0x78, 0x98, 0xfd, 0x1a, 0x64, 0x67, 0x3b, 0xfc, 0x22, 0x83, 0xff,
	0x58, 0x9a, 0x58, 0x3d, 0xe4, 0x79, 0x8b, 0x0c, 0x5f, 0x85, 0x6d, 0x1a, 0x18, 0x6f, 0x65, 0xfc,
	0xcd, 0x2e, 0x1f, 0x50, 0xfb, 0x1f, 0x6d, 0xc3, 0xa6, 0xcf, 0x62, 0x06, 0x11, 0xf7, 0xd4, 0x53,
	0xbf, 0x01, 0xbb, 0x63, 0xe8, 0x8a, 0x0c, 0x2b, 0x51, 0x22, 0x0a, 0xfd, 0xa9, 0x5c, 0x83, 0x74,
	0x1c, 0xa2, 0xcc, 0xde, 0x69, 0x63, 0xc3, 0xe3, 0x5b, 0x50, 0xcf, 0x60, 0x7f, 0x32, 0x18, 0x83,
	0x9e, 0xe7, 0x06, 0x58, 0xf9, 0x10, 0x32, 0xa2, 0x70, 0x05, 0x3d, 0x51, 0xb9, 0xb6, 0x06, 0x95,
	0x2b, 0xe8, 0x19, 0xe9, 0xa6, 0xf8, 0xa5, 0xbc, 0x0f, 0x3b, 0x24, 0x40, 0x41, 0xe8, 0xf5, 0x10,
	0x97, 0x4e, 0xf0, 0xd9, 0x22, 0x81, 0x19, 0x7a, 0x3d, 0x4e, 0x58, 0xcd, 0xc1, 0xd5, 0x02, 0x0e,
	0x29, 0x3f, 0xf3, 0x15, 0x09, 0xed, 0x76, 0x31, 0x6a, 0x2e, 0x59, 0x24, 0xd5, 0xff, 0x90, 0x60,
	0x7f, 0x92, 0xc6, 0xf2, 0x02, 0xdb, 0xb0, 0x19, 0x7b, 0x55, 0xd7, 0xea, 0x09, 0xab, 0xe4, 0xe7,
	0x59, 0x65, 0x16, 0xd7, 0x43, 0x9d, 0x79, 0x61, 0xd9, 0xea, 0x69, 0xd4, 0xd9, 0x8c, 0x0c, 0x89,
	0xc7, 0xd9, 0xaf, 0xc3, 0xce, 0xe8, 0x24, 0xb5, 0xcd, 0x19, 0xee, 0xc7, 0xb6, 0x39, 0xc3, 0x7d,
	0xe5, 0x32, 0xac, 0xbd, 0xb4, 0x3a, 0x11, 0x16, 0x0a, 0xe3, 0x83, 0xaf, 0xa6, 0x7e, 0x43, 0x52,
	0x7f, 0x1b, 0xae, 0x9a, 0x6f, 0xa7, 0x2d, 0x1a, 0x5c, 0x01, 0x5b, 0x8b, 0xc2, 0x7e, 0x8f, 0xb3,
	0xd8, 0x36, 0x80, 0x83, 0xea, 0xfd, 0x1e, 0x56, 0xae, 0xc2, 0x86, 0x50, 0x03, 0x8b, 0xf5, 0xb4,
	0xb1, 0xce, 0xa5, 0x57, 0x35, 0xd8, 0x37, 0xdf, 0x5e, 0xcd, 0xea, 0x11, 0xcf, 0xb0, 0x79, 0x12,
	0x0e, 0x65, 0xd8, 0x49, 0x07, 0xbd, 0x0e, 0x19, 0x9a, 0x4b, 0x90, 0x6b, 0x75, 0xb9, 0x94, 0x19,
	0x23, 0x4d, 0x01, 0x15, 0xab, 0x8b, 0xd5, 0x1f, 0x4b, 0xb0, 0x5f, 0xf7, 0x7a, 0xc4, 0x16, 0xeb,
	0x4d, 0xab, 0x8b, 0x39, 0xc1, 0x53, 0x6f, 0x74, 0xa5, 0x34, 0xba, 0x52, 0x31, 0x61, 0x87, 0x05,
	0x1e, 0xc3, 0x18, 0x8a, 0xbe, 0x87, 0xf3, 0xec, 0x3c, 0x21, 0xaf, 0xb1, 0x15, 0x09, 0x10, 0x0d,
	0x40, 0xf5, 0x04, 0xb2, 0xf9, 0x36, 0xb6, 0xcf, 0x3e, 0x89, 0xba, 0x3d, 0xf3, 0x45, 0x64, 0xf9,
	0xb8, 0x66, 0xb5, 0xf0, 0xb2, 0x96, 0x11, 0x3a, 0x48, 0x25, 0x3a, 0x50, 0x31, 0x5c, 0x9f, 0x4a,
	0x77, 0x79, 0xdf, 0xbe, 0x0e, 0x19, 0x17, 0x63, 0x07, 0x7d, 0x2f, 0xea, 0xf6, 0x84, 0x5b, 0xa5,
	0x29, 0x80, 0x52, 0x56, 0xbb, 0x70, 0x93, 0x7a, 0x72, 0x5d, 0x7b, 0xdd, 0xc3, 0x3e, 0xe9, 0x62,
	0x37, 0x34, 0x43, 0xdf, 0x0a, 0x71, 0xab, 0xbf, 0xec, 0x16, 0xee, 0xc2, 0x76, 0x20, 0x96, 0x0e,
	0x94, 0xbb, 0x6d, 0x6c, 0xc5, 0x40, 0xa6, 0xad, 0x7f, 0x48, 0xc1, 0xad, 0x99, 0xfc, 0x96, 0xdf,
	0xda, 0x0f, 0x40, 0x49, 0x78, 0xb2, 0x48, 0x19, 0x8a, 0xde, 0xa7, 0x8b, 0xa2, 0x77, 0x8e, 0x0c,
	0x87, 0x31, 0xe0, 0x84, 0x12, 0x4d, 0x62, 0x59, 0x0e, 0xc6, 0xc0, 0xca, 0xe7, 0xe1, 0xb2, 0x8b,
	0x5f, 0x87, 0x28, 0xea, 0x39, 0x56, 0x28, 0x38, 0xb9, 0x16, 0x6f, 0x97, 0xb7, 0x0d, 0x85, 0xce,
	0x35, 0xd8, 0x94, 0x2e, 0x66, 0xb2, 0x79, 0x78, 0x77, 0x2a, 0xf1, 0x45, 0xb9, 0x60, 0x7b, 0x38,
	0x17, 0xfc, 0x5a, 0x82, 0x6b, 0x05, 0x1c, 0x16, 0xbd, 0x2e, 0x7e, 0xec, 0x75, 0x3a, 0xde, 0xab,
	0xc7, 0x1d, 0xcf, 0x5a, 0xfa, 0x84, 0x51, 0x83, 0x74, 0x44, 0x9c, 0xe1, 0x48, 0xf8, 0xc2, 0x3c,
	0x9d, 0x71, 0x4e, 0x2c, 0x1e, 0x44, 0x3d, 0x73, 0x4f, 0xbd, 0xa3, 0xd4, 0xbe, 0x64, 0x6c, 0x44,
	0xc4, 0xa1, 0xe6, 0x55, 0x4c, 0xd8, 0x8c, 0x29, 0xd2, 0xea, 0xbd, 0xc2, 0x88, 0x7e, 0x71, 0x69,
	0xa2, 0x27, 0x8f, 0x8c, 0x8c, 0x20, 0x79, 0xf2, 0x48, 0xfd, 0xa9, 0x04, 0x3b, 0xac, 0x2f, 0x23,
	0xdd, 0x5e, 0x07, 0xcf, 0xa8, 0x69, 0xfb, 0xb0, 0x61, 0xd9, 0xac, 0xc0, 0x8b, 0x84, 0x11, 0x0f,
	0x69, 0x43, 0xe2, 0x12, 0xfb, 0x8c, 0x65, 0x84, 0x15, 0x9e, 0x11, 0xe2, 0xb1, 0x72, 0x05, 0xd6,
	0x5b, 0xd8, 0x75, 0xb0, 0xcf, 0x5a, 0x98, 0x6d, 0x43, 0x8c, 0xa8, 0x2f, 0x9f, 0x32, 0xc1, 0xe8,
	0x39, 0x28, 0x8c, 0x02, 0xd6, 0xc9, 0x6c, 0x1b, 0x5b, 0x1c, 0x68, 0x32, 0x98, 0xfa, 0x2b, 0x09,
	0xb2, 0xd3, 0x8c, 0xb0, 0xbc, 0x1b, 0x7f, 0x03, 0x56, 0x69, 0x4e, 0x11, 0x87, 0xbb, 0x83, 0x45,
	0xe9, 0x68, 0xa0, 0x08, 0x83, 0xad, 0x53, 0x0e, 0x60, 0xef, 0x94, 0xb8, 0x0e, 0xea, 0x75, 0xac,
	0x3e, 0x71, 0x5b, 0x28, 0xc4, 0xaf, 0x43, 0xb1, 0xd7, 0x5d, 0x3a, 0x51, 0xe3, 0xf0, 0x3a, 0x7e,
	0x1d, 0xaa, 0x45, 0x78, 0x77, 0xaa, 0xce, 0xa7, 0xe8, 0x74, 0xb4, 0x97, 0xe1, 0x6a, 0x1d, 0xf4,
	0x32, 0xea, 0x27, 0x70, 0x75, 0x86, 0xf5, 0xde, 0x88, 0xd6, 0x70, 0x5f, 0xa4, 0xfe, 0x97, 0x04,
	0xf7, 0x38, 0xb1, 0xf8, 0x8c, 0x68, 0x75, 0x4e, 0x4d, 0xdb, 0xc7, 0xd8, 0x8d, 0x5b, 0x9d, 0xff,
	0xa7, 0xde, 0xfd, 0x43, 0x09, 0xae, 0xea, 0x62, 0x65, 0x31, 0x6a, 0xd6, 0xb0, 0x1f, 0x78, 0xae,
	0xd5, 0xc9, 0x63, 0x3f, 0xa4, 0xfd, 0x35, 0xb1, 0x3d, 0x57, 0x14, 0x32, 0xf6, 0x9b, 0xc2, 0x98,
	0x79, 0xb9, 0x39, 0xd8, 0x6f, 0x9a, 0x28, 0x6c, 0xaf, 0xe3, 0xf9, 0x4c, 0xa4, 0x8c, 0xc1, 0x07,
	0xd4, 0x2b, 0xe8, 0x2c, 0x0a, 0xda, 0x96, 0xe3, 0xbd, 0x42, 0x1c, 0x83, 0xb7, 0xea, 0xbb, 0x74,
	0xc2, 0x64, 0xf0, 0x3c, 0x05, 0xab, 0x7f, 0x90, 0x82, 0x9b, 0xc2, 0x89, 0x7d, 0x82, 0x5d, 0x27,
	0x88, 0x8b, 0xeb, 0x20, 0xe6, 0x16, 0x74, 0xb6, 0x77, 0x60, 0x2b, 0x9e, 0x1e, 0x2a, 0xdb, 0x9b,
	0x02, 0xc6, 0xea, 0xef, 0x03, 0x90, 0x07, 0x07, 0xd6, 0x26, 0x62, 0x5b, 0xe3, 0x5e, 0xba, 0x63,
	0xc7, 0x0d, 0x78, 0x53, 0xa7, 0x9b, 0x1c, 0xc3, 0x64, 0x1b, 0x5e, 0x1d, 0xc7, 0xa4, 0xee, 0x4c,
	0xd9, 0x76, 0x71, 0xb7, 0x49, 0xab, 0x3a, 0x0b, 0x7e, 0x1e, 0xa8, 0x9b, 0x1c, 0x96, 0x67, 0x09,
	0xe0, 0x21, 0x5c, 0x22, 0x01, 0x1a, 0xc6, 0x42, 0x3e, 0x76, 0xd8, 0xe1, 0x23, 0x6d, 0xc8, 0x24,
	0x28, 0x0f, 0x70, 0x0d, 0xec, 0xa8, 0x0f, 0x61, 0x87, 0xeb, 0xc0, 0xb0, 0xbb, 0x4e, 0xdc, 0x54,
	0xf8, 0x76, 0xd7, 0xe1, 0x62, 0x88, 0xa6, 0x82, 0x02, 0x58, 0x3c, 0x9d, 0xc0, 0xd5, 0x41, 0x4c,
	0x96, 0x71, 0xcb, 0xea, 0xb5, 0x3d, 0x97, 0x6b, 0x8c, 0xc9, 0x26, 0x00, 0xb1, 0xce, 0x32, 0x54,
	0xb6, 0x18, 0xc9, 0xe1, 0x87, 0x14, 0x37, 0xc4, 0x83, 0xb4, 0x25, 0x86, 0xea, 0x3f, 0xad, 0xc0,
	0xad, 0x05, 0x11, 0x91, 0xe4, 0x0d, 0xe9, 0x9c, 0x79, 0xe3, 0x53, 0xd8, 0xee, 0x09, 0x7f, 0x43,
	0x36, 0x16, 0x5d, 0xfa, 0x02, 0x97, 0x9e, 0xe1, 0xab, 0xc6, 0x56, 0x6f, 0xd8, 0x73, 0x7f, 0x6b,
	0xe0, 0x0d, 0xf4, 0x5c, 0x22, 0xae, 0x8f, 0xbe, 0xba, 0x38, 0x56, 0x66, 0xb9, 0x5f, 0xe2, 0x49,
	0x4c, 0xb3, 0x05, 0x61, 0x11, 0x46, 0x7b, 0x75, 0xf1, 0xee, 0x47, 0x0d, 0xca, 0xad, 0xc7, 0x08,
	0xd9, 0xc0, 0xae, 0x82, 0xd0, 0x90, 0x9d, 0x28, 0xc9, 0xb5, 0xc5, 0x7a, 0x98, 0x61, 0x74, 0x7e,
	0xb3, 0x35, 0x02, 0x52, 0xff, 0x4e, 0x82, 0xfb, 0x0b, 0x93, 0xdb, 0xf2, 0x55, 0xe3, 0xd3, 0xc9,
	0x4b, 0x84, 0xaf, 0x2d, 0x56, 0xf0, 0x6c, 0x11, 0x92, 0x4b, 0x05, 0xf5, 0x47, 0x12, 0xdc, 0x4f,
	0x12, 0xd6, 0x4b, 0x12, 0x62, 0xae, 0xbf, 0x9a, 0x8f, 0x7b, 0xbe, 0x67, 0xe3, 0x20, 0x20, 0x6e,
	0x6b, 0xd9, 0x74, 0x7c, 0x05, 0xd6, 0x4f, 0x19, 0x15, 0x91, 0xfc, 0xc5, 0x48, 0xb9, 0x01, 0x83,
	0x24, 0x22, 0xda, 0xa6, 0xa1, 0xba, 0xd0, 0x80, 0x07, 0x8b, 0x05, 0x59, 0xfe, 0x1c, 0xd2, 0x82,
	0x5b, 0xb1, 0x87, 0xf5, 0x30, 0x3d, 0xcb, 0x73, 0xba, 0xe7, 0x29, 0x33, 0x23, 0xf2, 0x4f, 0xd4,
	0xb5, 0xdf, 0x81, 0xdb, 0xb3, 0x19, 0x2d, 0x6f, 0x72, 0x9a, 0xfc, 0x49, 0x2f, 0x48, 0x92, 0x3f,
	0xe9, 0x05, 0xf4, 0x34, 0x9f, 0xd4, 0xb9, 0x15, 0xd6, 0x72, 0xc7, 0x05, 0x4b, 0x75, 0xe0, 0x46,
	0xd5, 0xc5, 0xf9, 0x0e, 0xb1, 0xcf, 0xb8, 0xfa, 0x72, 0x1d, 0x16, 0x3f, 0x17, 0xbb, 0xc7, 0xbf,
	0x97, 0xe0, 0xbd, 0x19, 0x6c, 0x96, 0xdf, 0xe1, 0x5c, 0x56, 0x34, 0xa3, 0xfa, 0xb8, 0x6b, 0x11,
	0x57, 0x64, 0x7b, 0xee, 0x2f, 0x9b, 0x1c, 0xc6, 0xb3, 0x7d, 0xac, 0xa2, 0xd5, 0x21, 0x15, 0xdd,
	0x04, 0x68, 0x5a, 0xf6, 0x59, 0xcb, 0xf7, 0x22, 0xd7, 0x11, 0xb7, 0x52, 0x43, 0x10, 0xf5, 0xbb,
	0xb0, 0x3f, 0xb1, 0x81, 0x8b, 0xd5, 0xd1, 0x8f, 0x24, 0xb8, 0x36, 0x85, 0xc5, 0xff, 0xbe, 0x7e,
	0x54, 0x17, 0xf6, 0x46, 0x05, 0x29, 0x07, 0xad, 0x51, 0xaa, 0x13, 0xa5, 0x3d, 0x2e, 0x52, 0xaf,
	0x47, 0x8b, 0x14, 0xaf, 0xbe, 0x41, 0x9b, 0x76, 0xc9, 0xd8, 0xf6, 0x5c, 0x27, 0x88, 0xf9, 0x51,
	0x98, 0xc9, 0x41, 0xaa, 0x05, 0x77, 0xd9, 0x39, 0x56, 0x0f, 0x74, 0xb7, 0x1a, 0xb6, 0xb1, 0x4f,
	0xf3, 0x4d, 0x70, 0xd4, 0xb1, 0xec, 0xb3, 0xf3, 0x84, 0x9b, 0x02, 0xab, 0x11, 0x71, 0x02, 0x71,
	0xb8, 0x64, 0xbf, 0xd5, 0xbf, 0x48, 0xc1, 0xfb, 0xf3, 0x79, 0x2c, 0xaf, 0xe7, 0xdf, 0x93, 0x40,
	0x69, 0xc6, 0x04, 0xf8, 0x7d, 0xdd, 0xe0, 0x68, 0x79, 0x32, 0x2f, 0xcd, 0xbe, 0x89, 0x24, 0x87,
	0x09, 0x84, 0xce, 0x0f, 0xce, 0x97, 0xcd, 0x31, 0x30, 0x3d, 0x2d, 0x4e, 0x45, 0x5d, 0xea, 0xe6,
	0xe8, 0x3b, 0xec, 0x8c, 0xcf, 0xba, 0xd0, 0xc8, 0xf7, 0xb1, 0x1b, 0xc6, 0x29, 0xd5, 0xb9, 0x08,
	0xed, 0xff, 0x9c, 0x1f, 0xe9, 0xa7, 0x93, 0x5f, 0x5e, 0xf1, 0x7f, 0x28, 0xc1, 0x3e, 0xbf, 0xa5,
	0xe1, 0xc4, 0x92, 0x37, 0x8e, 0x37, 0x54, 0xff, 0x02, 0x51, 0x0e, 0x27, 0x27, 0x13, 0xf5, 0xbf,
	0x1b, 0x4d, 0x9b, 0xcb, 0x16, 0x21, 0x3b, 0x7b, 0xd1, 0x52, 0xc7, 0xf6, 0x9f, 0x4b, 0x70, 0x7b,
	0x22, 0x0b, 0x14, 0x70, 0x58, 0x26, 0xf6, 0xf2, 0xb6, 0x98, 0x9f, 0x09, 0x6e, 0xc3, 0x56, 0x13,
	0xb7, 0x08, 0x7f, 0x87, 0x21, 0x71, 0x65, 0x05, 0x06, 0x63, 0xec, 0x94, 0x1b, 0x00, 0xd8, 0x75,
	0xe2, 0x79, 0x7e, 0xfe, 0x4d, 0x63, 0xd7, 0x61, 0xb3, 0xea, 0x5f, 0x4a, 0x70, 0x67, 0x8e, 0xa8,
	0x17, 0x9d, 0xb8, 0x2e, 0xc3, 0x5a, 0x97, 0x52, 0x16, 0x72, 0xf2, 0x01, 0x6d, 0xbc, 0xd9, 0x33,
	0x92, 0x77, 0x86, 0x5d, 0x91, 0xd0, 0xd3, 0x5d, 0x62, 0xd7, 0xe9, 0x58, 0xfd, 0x5d, 0x09, 0xae,
	0xe5, 0xa2, 0xd0, 0x13, 0x59, 0x8c, 0xd8, 0x35, 0xcb, 0xc5, 0x17, 0x9b, 0xb6, 0x95, 0x5b, 0xb0,
	0xd9, 0xb5, 0x5c, 0xab, 0x85, 0x7d, 0x14, 0x0d, 0x94, 0x28, 0x40, 0x0d, 0xe2, 0xa8, 0xff, 0x2c,
	0x41, 0x76, 0x9a, 0x10, 0xcb, 0xeb, 0xe7, 0x5b, 0xb0, 0xe6, 0xbd, 0x72, 0xcf, 0x75, 0x09, 0xc0,
	0x17, 0x2a, 0x1f, 0x81, 0x12, 0xc7, 0x0a, 0x03, 0x20, 0xdf, 0xeb, 0xc4, 0x57, 0x1e, 0xf1, 0x71,
	0xaa, 0x4a, 0x27, 0x0c, 0xaf, 0xc3, 0xde, 0x6e, 0x42, 0xab, 0x45, 0xeb, 0xe4, 0x0a, 0xab, 0x93,
	0x56, 0x2b, 0x50, 0x7f, 0x2a, 0xc1, 0x95, 0xc1, 0xf5, 0xff, 0xa7, 0xe5, 0x52, 0x39, 0x68, 0x55,
	0xbc, 0x90, 0x9c, 0xf6, 0x69, 0x97, 0xd1, 0x0d, 0x5a, 0xfc, 0xde, 0x98, 0xfb, 0xfa, 0x46, 0x37,
	0x68, 0xc5, 0x97, 0xc6, 0xaf, 0xbb, 0x1d, 0xd4, 0x0d, 0x5a, 0xa2, 0x3c, 0xac, 0xbf, 0xee, 0x76,
	0x68, 0x55, 0x19, 0x3d, 0x31, 0x8e, 0xf7, 0x76, 0xf4, 0x90, 0x47, 0x49, 0x8a, 0x03, 0x0f, 0x27,
	0xcd, 0xdd, 0x70, 0xa7, 0x1b, 0xb4, 0xf2, 0x1c, 0x4c, 0x39, 0xa8, 0x3d, 0x90, 0x8b, 0x5e, 0xc7,
	0x61, 0x0f, 0x6d, 0xd4, 0x7b, 0x29, 0xf1, 0x23, 0x58, 0x0f, 0x2d, 0xbf, 0x85, 0xc3, 0x73, 0x9c,
	0x7e, 0xc4, 0xca, 0xd9, 0x85, 0x4d, 0x8d, 0xd8, 0xd5, 0x0e, 0x5d, 0x66, 0xe0, 0x0e, 0x7b, 0x58,
	0x0b, 0xda, 0xa4, 0xf7, 0xd6, 0xb7, 0xba, 0x54, 0x25, 0x9c, 0xf7, 0x90, 0x3f, 0x65, 0x38, 0x84,
	0xba, 0xd3, 0xbf, 0x4b, 0x70, 0x7d, 0x2a, 0xdf, 0xe5, 0xfd, 0xe9, 0x01, 0x7b, 0x27, 0x13, 0xb7,
	0x58, 0x42, 0x53, 0xbc, 0x32, 0xec, 0x90, 0x80, 0x1f, 0x05, 0xea, 0x5c, 0x0b, 0x0f, 0x40, 0x16,
	0x32, 0x25, 0x0b, 0xc4, 0xed, 0xff, 0x0e, 0x87, 0xeb, 0x02, 0x5f, 0xb9, 0x0b, 0xdb, 0xbe, 0x10,
	0x6b, 0xf8, 0x4c, 0xbe, 0x15, 0x03, 0xd9, 0x89, 0xfc, 0x26, 0x7b, 0x4a, 0x69, 0x93, 0x10, 0xf9,
	0x51, 0x87, 0xbf, 0x01, 0xa6, 0x8d, 0x0c, 0x09, 0x8a, 0x24, 0x34, 0xa2, 0x0e, 0x56, 0xff, 0x46,
	0x82, 0x6c, 0xc5, 0x73, 0xcb, 0x83, 0x47, 0xac, 0xaa, 0xef, 0xd0, 0xfd, 0x2e, 0xa7, 0xdb, 0xdb,
	0xb0, 0xe5, 0x7a, 0x3c, 0xbd, 0x0d, 0x95, 0x24, 0x70, 0x39, 0x65, 0xe2, 0x04, 0x34, 0x78, 0x09,
	0x0b, 0x4b, 0x8e, 0xc0, 0x7b, 0x63, 0xe0, 0x20, 0x86, 0x30, 0xea, 0x9f, 0xab, 0xe3, 0x3d, 0xdb,
	0xaf, 0x57, 0xe0, 0xfa, 0x54, 0x41, 0x97, 0x37, 0xc6, 0x77, 0x61, 0x3b, 0x11, 0x96, 0x36, 0x04,
	0xa2, 0x90, 0x7d, 0x7d, 0x9e, 0xcf, 0xce, 0x61, 0xcd, 0xfc, 0xd9, 0xd8, 0x74, 0x13, 0x8c, 0x40,
	0x41, 0xb0, 0x15, 0x6f, 0x96, 0x31, 0x58, 0xb9, 0x08, 0x06, 0x42, 0x57, 0x94, 0x60, 0xf6, 0xdf,
	0x24, 0x58, 0xa5, 0xbf, 0xe8, 0xd9, 0x9b, 0xd5, 0x67, 0x76, 0x50, 0x5e, 0x3e, 0xf6, 0xd8, 0x29,
	0x93, 0x9d, 0xbd, 0x0f, 0x60, 0x2f, 0x56, 0x3f, 0xcd, 0x54, 0x68, 0x28, 0x0e, 0x77, 0xc5, 0x04,
	0xcd, 0x54, 0xf1, 0x35, 0x8f, 0x8f, 0x23, 0x37, 0x71, 0x3c, 0x9e, 0xd5, 0x36, 0x05, 0x8c, 0xa1,
	0xdc, 0x82, 0x4d, 0x7e, 0xa4, 0x1c, 0x76, 0x4d, 0xe0, 0x20, 0x86, 0x70, 0x1d, 0x32, 0xc4, 0x45,
	0x56, 0x33, 0xc4, 0x41, 0x28, 0xdc, 0x32, 0x4d, 0xdc, 0x1c, 0x1b, 0xd3, 0x1e, 0xc9, 0x6c, 0x7b,
	0xaf, 0x0c, 0xcf, 0xeb, 0x6a, 0xaf, 0x49, 0xc8, 0x1d, 0xbe, 0xe6, 0xf5, 0xa2, 0xde, 0x45, 0xf4,
	0x48, 0x7f, 0x2d, 0xc1, 0xad, 0x99, 0xe4, 0x97, 0x77, 0xa7, 0x03, 0xd8, 0x63, 0x6d, 0xb7, 0x88,
	0xee, 0x1e, 0xa5, 0x23, 0x82, 0x7b, 0x97, 0x4e, 0x0c, 0x91, 0x57, 0x1e, 0xc0, 0xee, 0xb1, 0x45,
	0x3a, 0x7d, 0x36, 0x2a, 0x91, 0x2e, 0xe1, 0xca, 0x5b, 0x33, 0xc6, 0xc1, 0xea, 0xf7, 0xe1, 0x4e,
	0x01, 0x87, 0xb1, 0x88, 0x85, 0x88, 0x38, 0xe2, 0x5e, 0xfb, 0x82, 0xfa, 0xf4, 0x05, 0xf5, 0x80,
	0x76, 0x47, 0xea, 0x3c, 0x01, 0x96, 0xd7, 0xd3, 0x33, 0xd8, 0x6b, 0x51, 0x2a, 0xb1, 0xa2, 0xc4,
	0x25, 0xfb, 0xc2, 0x17, 0xf7, 0x21, 0xd6, 0x2c, 0x10, 0x76, 0x5b, 0xa3, 0x00, 0xf5, 0x5f, 0x24,
	0xd8, 0x1d, 0x43, 0xba, 0xb8, 0xb8, 0xf8, 0x1a, 0xac, 0x59, 0x4e, 0x97, 0xb8, 0xa2, 0x13, 0xf8,
	0x60, 0x1e, 0x91, 0x1c, 0x45, 0xe4, 0x4d, 0x00, 0x5b, 0x93, 0x94, 0xf5, 0x95, 0x41, 0x59, 0x7f,
	0xa3, 0xb4, 0xad, 0xea, 0x90, 0x49, 0x88, 0x51, 0x4b, 0x31, 0x72, 0xc3, 0x57, 0x9e, 0x19, 0x06,
	0x61, 0x91, 0x94, 0x4c, 0xb3, 0x0e, 0x43, 0x74, 0x4d, 0x0c, 0x42, 0x03, 0x56, 0xfd, 0xa1, 0x04,
	0xef, 0x09, 0x43, 0xea, 0x36, 0x6e, 0xfa, 0xd8, 0x3a, 0xc3, 0xfe, 0xb9, 0x62, 0x69, 0xfe, 0xab,
	0x01, 0x8d, 0x68, 0xde, 0xe9, 0x0c, 0x8a, 0x69, 0x9a, 0x01, 0x68, 0x2d, 0xfd, 0x7d, 0x09, 0x36,
	0x9e, 0xba, 0xb9, 0x9a, 0x45, 0x7c, 0x25, 0x0b, 0x69, 0xc6, 0x98, 0x24, 0x97, 0xe9, 0xc9, 0x58,
	0xb9, 0x02, 0xeb, 0x96, 0x1b, 0xbc, 0x12, 0x9e, 0x91, 0x31, 0xc4, 0x48, 0x29, 0xc0, 0x26, 0xff,
	0x15, 0xdf, 0x60, 0x52, 0xb7, 0xb9, 0x37, 0xd7, 0x18, 0x0c, 0x9d, 0x59, 0x03, 0xac, 0xe4, 0xb7,
	0xfa, 0x79, 0x80, 0xc1, 0x8c, 0xb2, 0x03, 0x29, 0x71, 0x15, 0xbc, 0x66, 0xa4, 0x88, 0x43, 0x0d,
	0x36, 0x74, 0x5f, 0xce, 0x7e, 0xab, 0xbf, 0x5c, 0x65, 0x27, 0xb6, 0xa9, 0x0a, 0x3c, 0xcf, 0xeb,
	0x52, 0xfa, 0x85, 0x6b, 0xa1, 0x9e, 0x45, 0xe2, 0xe6, 0xf2, 0xee, 0xbc, 0x5d, 0x08, 0x9d, 0x19,
	0x1b, 0x2f, 0x5c, 0x8b, 0x29, 0x8f, 0x7d, 0xb5, 0x23, 0xbe, 0x88, 0x0b, 0xad, 0x56, 0x9c, 0x7b,
	0x63, 0x58, 0xdd, 0x6a, 0x09, 0x0f, 0xc3, 0x03, 0x9c, 0x81, 0x87, 0xe1, 0x04, 0xe9, 0x32, 0xac,
	0x58, 0x2d, 0xde, 0x10, 0xac, 0xb1, 0x87, 0x15, 0x3a, 0x54, 0x54, 0xd8, 0xe6, 0x36, 0x64, 0xee,
	0xe4, 0x75, 0xc5, 0xbd, 0xfc, 0x26, 0x03, 0xea, 0x2e, 0xd5, 0x81, 0x72, 0x7f, 0xfc, 0x39, 0x6e,
	0x83, 0xda, 0x9a, 0xd1, 0x18, 0x79, 0x92, 0xa3, 0x76, 0x6e, 0x12, 0x3f, 0x6c, 0x3b, 0x56, 0x9f,
	0x7d, 0x14, 0x94, 0x31, 0x92, 0xf1, 0xd4, 0x86, 0x28, 0x33, 0xb5, 0x21, 0xfa, 0x02, 0xbc, 0x9b,
	0x60, 0x62, 0x07, 0x35, 0xfb, 0x31, 0x3a, 0x30, 0x74, 0x25, 0x46, 0xc7, 0xce, 0x51, 0x5f, 0x2c,
	0xd1, 0xe0, 0x96, 0x1d, 0x05, 0xa1, 0xd7, 0x45, 0x24, 0x31, 0x18, 0x12, 0xdf, 0x5a, 0x08, 0x99,
	0x37, 0xd9, 0xe2, 0x1b, 0x1c, 0x6d, 0x60, 0x56, 0xfe, 0x3d, 0x85, 0x90, 0xff, 0x37, 0xe1, 0x1a,
	0xb1, 0x31, 0x8a, 0x09, 0xc4, 0x3e, 0xca, 0x7b, 0xe3, 0x2d, 0xe6, 0x33, 0x57, 0x88, 0x8d, 0x8f,
	0xf8, 0xfc, 0x53, 0x31, 0xcd, 0x7a, 0xe4, 0x17, 0x70, 0xa9, 0x16, 0x05, 0xed, 0x12, 0x39, 0xc3,
	0x39, 0x3b, 0x24, 0x2f, 0x49, 0xd8, 0xa7, 0x6d, 0xf2, 0x01, 0xec, 0x75, 0xc8, 0x19, 0x46, 0x96,
	0x80, 0xa1, 0xc8, 0xef, 0x88, 0x10, 0xd8, 0xed, 0x0c, 0xe1, 0x36, 0xfc, 0x8e, 0x72, 0x08, 0x97,
	0x26, 0xbe, 0xe5, 0x4b, 0x1e, 0xfe, 0xf6, 0xc6, 0x3e, 0xb7, 0xd3, 0x1d, 0xf5, 0x67, 0x12, 0xdc,
	0x36, 0xb1, 0xeb, 0xd4, 0xa2, 0x66, 0x87, 0xd8, 0x65, 0x1c, 0x04, 0x56, 0x0b, 0x1f, 0x13, 0x1f,
	0xdb, 0x61, 0x67, 0xe9, 0xcf, 0x07, 0xae, 0x41, 0xfa, 0xd4, 0xf7, 0xba, 0x68, 0xd0, 0x30, 0x6f,
	0xd0, 0x71, 0x63, 0xe2, 0xed, 0x70, 0x65, 0xd6, 0xf5, 0x94, 0x1b, 0x27, 0xb6, 0xe4, 0x0d, 0xa5,
	0x02, 0x77, 0xe6, 0xc8, 0xb7, 0xfc, 0xb5, 0x71, 0x19, 0xee, 0x16, 0x70, 0xc8, 0xc9, 0xf1, 0xcb,
	0x73, 0xb3, 0xed, 0xf9, 0xa1, 0x1d, 0x85, 0x7a, 0x77, 0xf9, 0x8f, 0x3e, 0xd4, 0x5f, 0x48, 0xf0,
	0xfe, 0x7c, 0x7a, 0xcb, 0x07, 0xfb, 0x77, 0xe0, 0x52, 0x20, 0x68, 0x20, 0x42, 0x89, 0x0c, 0x3f,
	0x0f, 0x7c, 0x34, 0x2f, 0xee, 0x47, 0x58, 0x97, 0x83, 0x96, 0xb1, 0x17, 0x0c, 0x43, 0xd8, 0x85,
	0x72, 0x05, 0xe4, 0x71, 0x34, 0xd6, 0x56, 0x31, 0x46, 0x03, 0xcf, 0x4a, 0x33, 0x00, 0x75, 0xa9,
	0xf7, 0x00, 0xf8, 0xe4, 0x50, 0x73, 0xc7, 0xd1, 0x59, 0xd1, 0x29, 0xb1, 0x8a, 0x5f, 0x24, 0x8e,
	0x83, 0xdd, 0xa2, 0xd7, 0x65, 0x9f, 0xb8, 0x7c, 0xdb, 0x73, 0x71, 0xde, 0x73, 0x4f, 0xc9, 0xb2,
	0x4f, 0x0c, 0xea, 0x19, 0x33, 0xcf, 0x6c, 0x6a, 0xcb, 0x6b, 0xf3, 0x1a, 0xa4, 0x3f, 0xe3, 0x8f,
	0x77, 0x81, 0xa8, 0x0e, 0x1b, 0x9f, 0xb1, 0x87, 0xbb, 0x40, 0x35, 0xe1, 0xc3, 0x84, 0x99, 0xb8,
	0x13, 0xca, 0x5b, 0x21, 0x6e, 0x79, 0x7e, 0x9f, 0xc6, 0xe3, 0xf9, 0x76, 0xf0, 0x19, 0x1c, 0xbc,
	0x09, 0xd1, 0xe5, 0x37, 0x72, 0x07, 0xb6, 0x6c, 0x41, 0x08, 0x0d, 0xda, 0xb2, 0xcd, 0x18, 0x46,
	0x37, 0x74, 0xca, 0x8a, 0x36, 0x3d, 0xfe, 0x1b, 0x98, 0xc6, 0x3f, 0x76, 0x1d, 0xad, 0xeb, 0x7d,
	0x8f, 0x04, 0xe7, 0x28, 0xda, 0x3e, 0x7e, 0x81, 0x02, 0x2f, 0xf2, 0xed, 0xa4, 0x3b, 0xf0, 0xf1,
	0x0b, 0x93, 0x01, 0xd4, 0x1f, 0x4b, 0xfc, 0x93, 0xa3, 0x69, 0x8c, 0x96, 0xdf, 0xd8, 0x37, 0x61,
	0x1d, 0xb3, 0xc5, 0xc2, 0xc5, 0xef, 0xcf, 0xbd, 0x9a, 0x65, 0xf7, 0x1a, 0x8c, 0x99, 0x21, 0x96,
	0xa9, 0x7f, 0x94, 0x82, 0xcd, 0x21, 0x38, 0x35, 0x39, 0x9b, 0x19, 0x3c, 0xd8, 0x6e, 0xb0, 0xb1,
	0xee, 0xd0, 0xe3, 0x7c, 0xd7, 0xf9, 0xb2, 0xf0, 0x62, 0xfa, 0x53, 0x79, 0x0a, 0x7b, 0x9e, 0x4f,
	0x5a, 0xc4, 0x45, 0x62, 0xcd, 0xe0, 0xad, 0x73, 0x6e, 0xdb, 0x46, 0x37, 0xc2, 0xc5, 0xd8, 0xe5,
	0xeb, 0xd9, 0x80, 0xf5, 0x07, 0x55, 0x90, 0xc3, 0x76, 0xd4, 0x6d, 0x0e, 0x53, 0x5c, 0x5d, 0x86,
	0xe2, 0x0e, 0x5b, 0x3e, 0x20, 0x78, 0x0d, 0xd2, 0x24, 0x40, 0xb6, 0x65, 0xb7, 0xe3, 0xc3, 0xf8,
	0x06, 0x09, 0xf2, 0x74, 0xa8, 0x5c, 0x85, 0x0d, 0xaf, 0x19, 0xa0, 0x33, 0xdc, 0x67, 0x55, 0x37,
	0x63, 0xac, 0x7b, 0xcd, 0xe0, 0x09, 0xee, 0xab, 0x4f, 0x20, 0x93, 0x10, 0x64, 0xb7, 0x18, 0x49,
	0x68, 0xd3, 0x9f, 0xb4, 0x65, 0x6a, 0x63, 0xd2, 0x6a, 0x87, 0xf1, 0x5b, 0x1e, 0x1f, 0x29, 0x97,
	0x61, 0xed, 0x15, 0x71, 0xc2, 0x76, 0x7c, 0x8b, 0xc7, 0x06, 0xaa, 0x0b, 0x1f, 0x14, 0x70, 0x98,
	0x1f, 0xab, 0x7b, 0xac, 0x9d, 0xe1, 0xc5, 0xef, 0x62, 0xbb, 0x42, 0xf5, 0x47, 0x2b, 0x70, 0x6f,
	0x11, 0xc3, 0xe5, 0x1d, 0xed, 0x2e, 0x6c, 0x8f, 0xd6, 0x73, 0xf1, 0x45, 0x6b, 0x30, 0x5c, 0xbf,
	0x87, 0xfb, 0xcc, 0x95, 0xb1, 0x3e, 0xf3, 0x32, 0xac, 0x11, 0x37, 0xf4, 0x3d, 0x51, 0xa4, 0xf8,
	0x40, 0x7c, 0x29, 0x6b, 0x75, 0x7c, 0x6c, 0x39, 0x7d, 0x14, 0xe0, 0xf8, 0x64, 0xba, 0x45, 0x82,
	0x1c, 0x07, 0x9a, 0x38, 0x54, 0x3e, 0x83, 0x9b, 0xc3, 0x7d, 0x41, 0x8f, 0x55, 0x8c, 0xb8, 0xbf,
	0x60, 0x2e, 0xc2, 0x3f, 0xfe, 0xff, 0xca, 0xdc, 0x97, 0xfb, 0xa4, 0x71, 0x10, 0x15, 0x87, 0x2d,
	0x67, 0xed, 0x6a, 0x96, 0xcc, 0x9c, 0x9b, 0xdf, 0x93, 0x6c, 0xcc, 0xed, 0x49, 0x7e, 0x22, 0xc1,
	0x07, 0xe6, 0x85, 0x9a, 0xfe, 0x8d, 0xac, 0xb0, 0xe0, 0x9c, 0x69, 0xc2, 0x3d, 0xf3, 0xa2, 0xdd,
	0x43, 0xfd, 0x5b, 0x09, 0x54, 0xfe, 0xa5, 0xdf, 0x54, 0xc2, 0xcb, 0xee, 0xf3, 0x6d, 0x7b, 0xf6,
	0x05, 0x2a, 0xf8, 0x55, 0x0a, 0xee, 0xce, 0x95, 0x76, 0xf9, 0xf8, 0x18, 0x76, 0xfd, 0xd4, 0x98,
	0xeb, 0x4f, 0x58, 0x6d, 0x65, 0x8a, 0xd5, 0x26, 0x23, 0x61, 0xf5, 0x5c, 0x91, 0xb0, 0xf6, 0x3f,
	0x15, 0x09, 0x6a, 0x97, 0xb5, 0x6b, 0xe3, 0x1a, 0xe3, 0x45, 0xf9, 0x3c, 0xcf, 0xea, 0x0b, 0xf2,
	0xd8, 0xaf, 0x56, 0xa7, 0x26, 0xce, 0x61, 0x7e, 0xcb, 0x9b, 0xa9, 0x0c, 0x72, 0xfc, 0xb0, 0x76,
	0x1e, 0x07, 0xdb, 0x11, 0x8b, 0x9f, 0x0a, 0x3f, 0xbb, 0x0f, 0xbb, 0x36, 0x93, 0x07, 0x0d, 0xe5,
	0xbd, 0x15, 0xf6, 0x9d, 0x16, 0x03, 0x3f, 0x1d, 0x72, 0x01, 0x81, 0x28, 0x0e, 0xdb, 0xfc, 0xdd,
	0x61, 0x8b, 0x03, 0xf9, 0xf9, 0x58, 0xf9, 0x06, 0x5c, 0xe7, 0xe3, 0xc8, 0xb7, 0x9a, 0x1d, 0x2c,
	0x50, 0x03, 0xe4, 0x46, 0xdd, 0x26, 0xf6, 0xc5, 0xb7, 0x5d, 0xd7, 0x86, 0x51, 0xf8, 0xc2, 0xa0,
	0xc2, 0x10, 0x94, 0x27, 0xa0, 0xfa, 0x71, 0x4b, 0x81, 0x9d, 0x64, 0xb9, 0x43, 0x82, 0x5e, 0xc7,
	0xea, 0xc7, 0x64, 0xd6, 0x19, 0x99, 0x5b, 0x43, 0x98, 0x82, 0xca, 0x31, 0xc7, 0x13, 0xc4, 0xea,
	0xa0, 0x8c, 0x48, 0xcc, 0xbd, 0x6b, 0x63, 0xa9, 0x6b, 0x00, 0x79, 0x78, 0x7b, 0x8b, 0xb3, 0x69,
	0x7a, 0x5e, 0x36, 0x9d, 0x12, 0x20, 0x99, 0xc9, 0x00, 0x51, 0x35, 0xd8, 0x1b, 0x76, 0xef, 0xa0,
	0x4d, 0x7b, 0xf4, 0x2b, 0xb0, 0x2e, 0xda, 0x31, 0x7e, 0xf1, 0x20, 0x46, 0x73, 0x3e, 0x3f, 0xfb,
	0x53, 0x09, 0xb2, 0xb3, 0xc3, 0xe4, 0xff, 0xb4, 0x1a, 0x1e, 0xfc, 0x99, 0x04, 0xdb, 0xc9, 0x7f,
	0x43, 0x60, 0xaa, 0xb9, 0x05, 0xd7, 0xcd, 0x67, 0x7a, 0x3d, 0x5f, 0x44, 0xc5, 0xc6, 0x11, 0xaa,
	0x3f, 0xaf, 0x69, 0xa8, 0x51, 0x31, 0x6b, 0x5a, 0x5e, 0x7f, 0xac, 0x6b, 0xc7, 0xf2, 0x3b, 0xca,
	0x07, 0x70, 0x67, 0x1c, 0xc1, 0xcc, 0x95, 0x35, 0x94, 0xd7, 0xeb, 0xcf, 0xd1, 0x89, 0x6e, 0xea,
	0x47, 0x25, 0x4d, 0x96, 0x94, 0xf7, 0xe1, 0xf6, 0x38, 0x5a, 0xbe, 0x98, 0xab, 0x54, 0xb4, 0x12,
	0xca, 0x95, 0x4a, 0xd5, 0x7c, 0xae, 0xae, 0xc9, 0x29, 0xe5, 0x3a, 0x5c, 0x1d, 0xc7, 0xca, 0x15,
	0x34, 0x54, 0xcf, 0x15, 0xe4, 0x95, 0x83, 0x3f, 0xc9, 0xc0, 0xb6, 0x59, 0x37, 0x72, 0x75, 0xad,
	0xf0, 0x9c, 0xcd, 0x29, 0xef, 0xc1, 0xb5, 0x11, 0xc0, 0x98, 0x68, 0xf7, 0x40, 0x1d, 0x9d, 0xd6,
	0x2b, 0x27, 0x7a, 0x9d, 0xfe, 0x93, 0xf0, 0x2e, 0xd7, 0x65, 0x49, 0xb9, 0x0f, 0x77, 0x47, 0xf1,
	0x8a, 0xd5, 0xb2, 0x86, 0xca, 0xfa, 0xa7, 0x09, 0x9a, 0x91, 0x2f, 0x1f, 0xcb, 0x29, 0xe5, 0x2e,
	0xdc, 0x9a, 0x82, 0xf8, 0xb8, 0x5a, 0x2a, 0x55, 0x9f, 0xa1, 0xc7, 0xa5, 0x6a, 0xae, 0x2e, 0xaf,
	0x28, 0x1f, 0xc1, 0x83, 0x29, 0x48, 0xb5, 0x42, 0x9e, 0x51, 0x49, 0x48, 0x16, 0xf5, 0x63, 0x4d,
	0x5e, 0x55, 0x3e, 0x84, 0x0f, 0x46, 0xb1, 0x6b, 0x8d, 0xa3, 0x92, 0x9e, 0x47, 0x66, 0xde, 0xd0,
	0xb4, 0x0a, 0x7a, 0xda, 0xd0, 0xf3, 0x4f, 0x50, 0xd9, 0x2c, 0xc8, 0x6b, 0xca, 0xc7, 0xf0, 0xb9,
	0x79, 0xa8, 0xa6, 0x56, 0x39, 0x46, 0x5a, 0xb9, 0xfa, 0x89, 0x8e, 0x6a, 0xb9, 0xfc, 0x13, 0x79,
	0x7d, 0x52, 0x92, 0xd1, 0x05, 0x65, 0xb3, 0x80, 0x6a, 0xa5, 0x86, 0x89, 0xaa, 0x15, 0x4d, 0xde,
	0x58, 0x84, 0x5d, 0x30, 0x34, 0xad, 0xae, 0x57, 0x0a, 0x4c, 0x98, 0x34, 0xb3, 0xe7, 0x08, 0x76,
	0xae, 0x51, 0xaf, 0xc6, 0x0a, 0xae, 0x56, 0x50, 0x59, 0xcf, 0xcb, 0x99, 0xc9, 0xdd, 0x55, 0x2b,
	0x1a, 0xca, 0x97, 0xe8, 0x9e, 0x46, 0x51, 0x61, 0xd2, 0x58, 0xb1, 0xa2, 0x0a, 0x0d, 0xfd, 0x38,
	0x56, 0xb2, 0xbc, 0xa9, 0x7c, 0x11, 0x3e, 0x9e, 0x61, 0x2c, 0xe6, 0x76, 0x23, 0x4b, 0xf5, 0x8a,
	0xa9, 0x19, 0x75, 0x79, 0x4b, 0xb9, 0x09, 0xd9, 0xd1, 0x45, 0x9c, 0xa8, 0x60, 0xbe, 0xad, 0xdc,
	0x86, 0x1b, 0x63, 0x8e, 0x64, 0x6a, 0x06, 0x2a, 0x6b, 0x85, 0x5c, 0xad, 0x48, 0xb5, 0xb3, 0x33,
	0x69, 0x7a, 0x3d, 0xaf, 0xa1, 0x23, 0x43, 0xcb, 0x3d, 0xd1, 0x0c, 0x54, 0xab, 0xd6, 0x1a, 0x35,
	0x79, 0x97, 0x05, 0xcb, 0x08, 0x92, 0x59, 0xd4, 0x2b, 0x54, 0x67, 0xb5, 0xaa, 0x5e, 0xa9, 0xcb,
	0xf2, 0xa4, 0x1c, 0xf5, 0xea, 0x71, 0xee, 0x39, 0xca, 0x57, 0x1b, 0xb5, 0x92, 0x26, 0xef, 0x4d,
	0xda, 0x20, 0xde, 0x49, 0xcd, 0xd0, 0x1e, 0x6b, 0x86, 0x56, 0xc9, 0x6b, 0xe8, 0x89, 0xf6, 0xfc,
	0x59, 0xd5, 0x38, 0x96, 0x95, 0x49, 0xbf, 0x8d, 0xb1, 0x69, 0x34, 0x15, 0xaa, 0x06, 0x87, 0xca,
	0x97, 0x94, 0x87, 0xf0, 0xe1, 0x3c, 0xd3, 0xb2, 0xcd, 0x1e, 0xe5, 0x4c, 0x8d, 0xd9, 0xf6, 0xb2,
	0x72, 0x08, 0x07, 0x73, 0x1d, 0xad, 0x58, 0x35, 0xea, 0xf9, 0x46, 0x1d, 0xe9, 0xe5, 0x5c, 0x41,
	0x93, 0xdf, 0x55, 0x0e, 0xe0, 0xde, 0x54, 0xfc, 0x49, 0x15, 0x5d, 0x99, 0x74, 0xe2, 0x8a, 0xf6,
	0x0c, 0x9d, 0x68, 0x86, 0xa9, 0x57, 0x2b, 0x53, 0x16, 0x5c, 0x55, 0x1e, 0xc0, 0xfb, 0x0b, 0x14,
	0x8f, 0x6a, 0x0d, 0xb3, 0x28, 0xef, 0x2b, 0x59, 0xb8, 0x32, 0xe6, 0x92, 0x3a, 0xca, 0x37, 0x6a,
	0xfa, 0xb1, 0x7c, 0x6d, 0xd2, 0xc0, 0x39, 0x1d, 0xe9, 0x6e, 0xd0, 0x23, 0x3e, 0xbb, 0x44, 0x95,
	0xb3, 0xca, 0x0d, 0xd8, 0x1f, 0xc5, 0x78, 0x5c, 0xca, 0x99, 0x45, 0xaa, 0xd2, 0xba, 0x7c, 0x9d,
	0x25, 0xa6, 0x91, 0x59, 0xa3, 0x5a, 0xd2, 0x50, 0xad, 0x94, 0x7b, 0x2e, 0xdf, 0x38, 0x78, 0x05,
	0x97, 0x46, 0x27, 0x4f, 0x72, 0xa5, 0x86, 0x36, 0xe9, 0x32, 0x0c, 0x3c, 0x96, 0xa3, 0x26, 0x52,
	0x18, 0x47, 0xca, 0x97, 0xaa, 0x26, 0x4d, 0x9b, 0x13, 0x52, 0xf1, 0xe9, 0x6a, 0x4d, 0xab, 0xc8,
	0xa9, 0x83, 0x5f, 0x48, 0x70, 0x79, 0xfc, 0xab, 0x02, 0x96, 0xb5, 0x1f, 0xc0, 0xfb, 0xe5, 0x86,
	0xa9, 0xa1, 0x7c, 0xb5, 0x5c, 0xae, 0x56, 0xd0, 0xa7, 0xe5, 0x12, 0x8b, 0xf6, 0x29, 0x39, 0xf2,
	0x3e, 0xdc, 0x9d, 0x89, 0x29, 0x60, 0xd4, 0x29, 0x24, 0xe5, 0x18, 0xbe, 0x35, 0x13, 0xb1, 0x58,
	0xad, 0xa3, 0x02, 0x8d, 0xbe, 0xc7, 0x34, 0x11, 0x3e, 0xd3, 0xf4, 0x42, 0xb1, 0x8e, 0xb4, 0x4a,
	0x5d, 0x33, 0x12, 0x97, 0xac, 0xeb, 0x35, 0x53, 0x4e, 0x1d, 0xfc, 0x2c, 0x05, 0xd7, 0xc7, 0x25,
	0x1e, 0xfa, 0x1e, 0x41, 0xf9, 0x3c, 0x7c, 0x34, 0x8d, 0x4b, 0xbe, 0x5a, 0xa9, 0x6b, 0x95, 0xfa,
	0xb4, 0x0d, 0x7c, 0x15, 0xbe, 0xb2, 0x70, 0x45, 0x22, 0x9f, 0xf9, 0xdc, 0xac, 0x6b, 0x65, 0x94,
	0x7b, 0x96, 0x33, 0x8e, 0x65, 0x49, 0x29, 0x41, 0xf1, 0xcd, 0xd7, 0x2e, 0xda, 0x9b, 0x92, 0x87,
	0x6f, 0x2e, 0xa4, 0x66, 0x56, 0xf3, 0x7a, 0xae, 0xc4, 0x50, 0x1a, 0x15, 0x5a, 0x21, 0x4b, 0xfa,
	0x13, 0x0d, 0xe5, 0xf2, 0x75, 0xfd, 0x44, 0xaf, 0x3f, 0x97, 0x57, 0x0e, 0xfe, 0x55, 0x02, 0x25,
	0x29, 0x8c, 0xc7, 0x65, 0xbd, 0xc2, 0x2b, 0x9d, 0x0a, 0x37, 0x27, 0xa1, 0x63, 0x9a, 0xb8, 0x01,
	0xfb, 0x53, 0x70, 0xaa, 0xcf, 0x2a, 0x9a, 0xc1, 0x3d, 0x69, 0xca, 0x2c, 0xfb, 0x29, 0xa7, 0xa8,
	0x1b, 0x4e, 0x99, 0xad, 0x54, 0x8d, 0x72, 0xae, 0x24, 0xaf, 0xcc, 0x58, 0x6c, 0x36, 0x6a, 0x9a,
	0x21, 0xaf, 0xd2, 0xac, 0x36, 0x65, 0x36, 0x9f, 0xab, 0xd5, 0x73, 0x7a, 0x45, 0x5e, 0x3b, 0x78,
	0x0d, 0x8a, 0xa8, 0x91, 0x66, 0x3d, 0x57, 0x6f, 0x98, 0xc9, 0x96, 0x26, 0xa1, 0x93, 0xd1, 0x31,
	0x05, 0x47, 0xd4, 0x02, 0x89, 0xe6, 0xdb, 0x99, 0xd3, 0xda, 0xb1, 0x9c, 0x3a, 0xf8, 0x47, 0x09,
	0x2e, 0x8f, 0xf6, 0x6c, 0xfc, 0xaa, 0x8c, 0xba, 0xfd, 0x48, 0x1e, 0x69, 0x98, 0x45, 0x64, 0x56,
	0x1b, 0x46, 0x7e, 0x5c, 0x82, 0x03, 0xb8, 0x37, 0x0b, 0x51, 0x64, 0x39, 0xb1, 0x75, 0x59, 0xa2,
	0x51, 0x37, 0x0b, 0x37, 0xd7, 0x38, 0xd6, 0xeb, 0xc8, 0xd0, 0x3e, 0xd1, 0xf2, 0x75, 0x39, 0x45,
	0xb3, 0xe0, 0x2c, 0x4c, 0x66, 0xaf, 0x51, 0x07, 0x93, 0x57, 0x0e, 0xfe, 0x4a, 0x82, 0x2b, 0xfa,
	0xf4, 0xe6, 0xf5, 0x43, 0xf8, 0x60, 0x98, 0xd6, 0xd3, 0x86, 0x66, 0xd6, 0x69, 0x32, 0x9d, 0xa2,
	0xce, 0x87, 0xf0, 0xe1, 0x6c, 0x54, 0x11, 0x1b, 0x31, 0x50, 0x96, 0xe6, 0xa3, 0xe7, 0x1b, 0x66,
	0xbd, 0x3a, 0x84, 0x9e, 0x3a, 0xaa, 0xc1, 0xbe, 0xed, 0x75, 0x0f, 0xfb, 0xa4, 0xef, 0x45, 0xac,
	0x8b, 0xf7, 0x1c, 0xdc, 0xe1, 0x7f, 0xb6, 0xe3, 0xdb, 0x5f, 0x6a, 0x79, 0x1d, 0xcb, 0x6d, 0x1d,
	0x7e, 0xf9, 0x51, 0x18, 0x1e, 0xda, 0x5e, 0xf7, 0x63, 0x06, 0xb6, 0xbd, 0xce, 0xc7, 0x56, 0xaf,
	0xc7, 0xfe, 0x3a, 0xc8, 0xc3, 0xb8, 0xdf, 0x7f, 0xd8, 0x8e, 0x9a, 0x0f, 0x59, 0xbf, 0xdf, 0x5c,
	0x67, 0x58, 0x5f, 0xfc, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf9, 0x16, 0x00, 0x7d, 0x57, 0x44,
	0x00, 0x00,
}
