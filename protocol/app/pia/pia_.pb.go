// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pia_.proto

package pia // import "golang.52tt.com/protocol/app/pia"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 阶段类型
type PiaPhaseType int32

const (
	PiaPhaseType_PIA_PHASE_CLOSE   PiaPhaseType = 0
	PiaPhaseType_PIA_PHASE_ON_PLAY PiaPhaseType = 1
)

var PiaPhaseType_name = map[int32]string{
	0: "PIA_PHASE_CLOSE",
	1: "PIA_PHASE_ON_PLAY",
}
var PiaPhaseType_value = map[string]int32{
	"PIA_PHASE_CLOSE":   0,
	"PIA_PHASE_ON_PLAY": 1,
}

func (x PiaPhaseType) String() string {
	return proto.EnumName(PiaPhaseType_name, int32(x))
}
func (PiaPhaseType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{0}
}

type DramaRoomUserSex int32

const (
	DramaRoomUserSex_unknown    DramaRoomUserSex = 0
	DramaRoomUserSex_Sex_Male   DramaRoomUserSex = 1
	DramaRoomUserSex_Sex_Female DramaRoomUserSex = 2
)

var DramaRoomUserSex_name = map[int32]string{
	0: "unknown",
	1: "Sex_Male",
	2: "Sex_Female",
}
var DramaRoomUserSex_value = map[string]int32{
	"unknown":    0,
	"Sex_Male":   1,
	"Sex_Female": 2,
}

func (x DramaRoomUserSex) String() string {
	return proto.EnumName(DramaRoomUserSex_name, int32(x))
}
func (DramaRoomUserSex) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{1}
}

// 走本阶段
type DramaPhase int32

const (
	DramaPhase_DRAMA_PHASE_UNSPECIFIED DramaPhase = 0
	DramaPhase_DRAMA_PHASE_SELECT_ROLE DramaPhase = 1
	DramaPhase_DRAMA_PHASE_PLAY        DramaPhase = 2
	DramaPhase_DRAMA_PHASE_END         DramaPhase = 3
	DramaPhase_DRAMA_PHASE_PAUSE       DramaPhase = 4
)

var DramaPhase_name = map[int32]string{
	0: "DRAMA_PHASE_UNSPECIFIED",
	1: "DRAMA_PHASE_SELECT_ROLE",
	2: "DRAMA_PHASE_PLAY",
	3: "DRAMA_PHASE_END",
	4: "DRAMA_PHASE_PAUSE",
}
var DramaPhase_value = map[string]int32{
	"DRAMA_PHASE_UNSPECIFIED": 0,
	"DRAMA_PHASE_SELECT_ROLE": 1,
	"DRAMA_PHASE_PLAY":        2,
	"DRAMA_PHASE_END":         3,
	"DRAMA_PHASE_PAUSE":       4,
}

func (x DramaPhase) String() string {
	return proto.EnumName(DramaPhase_name, int32(x))
}
func (DramaPhase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{2}
}

// 操作类型
type DramaOperationType int32

const (
	DramaOperationType_DRAMA_OPERATION_TYPE_UNSPECIFIED DramaOperationType = 0
	DramaOperationType_DRAMA_OPERATION_TYPE_PLAY        DramaOperationType = 1
	DramaOperationType_DRAMA_OPERATION_TYPE_PAUSE       DramaOperationType = 2
	DramaOperationType_DRAMA_OPERATION_TYPE_END         DramaOperationType = 3
)

var DramaOperationType_name = map[int32]string{
	0: "DRAMA_OPERATION_TYPE_UNSPECIFIED",
	1: "DRAMA_OPERATION_TYPE_PLAY",
	2: "DRAMA_OPERATION_TYPE_PAUSE",
	3: "DRAMA_OPERATION_TYPE_END",
}
var DramaOperationType_value = map[string]int32{
	"DRAMA_OPERATION_TYPE_UNSPECIFIED": 0,
	"DRAMA_OPERATION_TYPE_PLAY":        1,
	"DRAMA_OPERATION_TYPE_PAUSE":       2,
	"DRAMA_OPERATION_TYPE_END":         3,
}

func (x DramaOperationType) String() string {
	return proto.EnumName(DramaOperationType_name, int32(x))
}
func (DramaOperationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{3}
}

// BGM状态
type DramaBGMPhase int32

const (
	DramaBGMPhase_DRAMA_BGM_PHASE_UNSPECIFIED DramaBGMPhase = 0
	DramaBGMPhase_DRAMA_BGM_PHASE_PLAY        DramaBGMPhase = 1
	DramaBGMPhase_DRAMA_BGM_PHASE_PAUSE       DramaBGMPhase = 2
)

var DramaBGMPhase_name = map[int32]string{
	0: "DRAMA_BGM_PHASE_UNSPECIFIED",
	1: "DRAMA_BGM_PHASE_PLAY",
	2: "DRAMA_BGM_PHASE_PAUSE",
}
var DramaBGMPhase_value = map[string]int32{
	"DRAMA_BGM_PHASE_UNSPECIFIED": 0,
	"DRAMA_BGM_PHASE_PLAY":        1,
	"DRAMA_BGM_PHASE_PAUSE":       2,
}

func (x DramaBGMPhase) String() string {
	return proto.EnumName(DramaBGMPhase_name, int32(x))
}
func (DramaBGMPhase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{4}
}

// BGM操作类型
type DramaBGMOperationType int32

const (
	DramaBGMOperationType_DRAMA_BGM_OPERATION_TYPE_UNSPECIFIED DramaBGMOperationType = 0
	DramaBGMOperationType_DRAMA_BGM_OPERATION_TYPE_PLAY        DramaBGMOperationType = 1
	DramaBGMOperationType_DRAMA_BGM_OPERATION_TYPE_PAUSE       DramaBGMOperationType = 2
)

var DramaBGMOperationType_name = map[int32]string{
	0: "DRAMA_BGM_OPERATION_TYPE_UNSPECIFIED",
	1: "DRAMA_BGM_OPERATION_TYPE_PLAY",
	2: "DRAMA_BGM_OPERATION_TYPE_PAUSE",
}
var DramaBGMOperationType_value = map[string]int32{
	"DRAMA_BGM_OPERATION_TYPE_UNSPECIFIED": 0,
	"DRAMA_BGM_OPERATION_TYPE_PLAY":        1,
	"DRAMA_BGM_OPERATION_TYPE_PAUSE":       2,
}

func (x DramaBGMOperationType) String() string {
	return proto.EnumName(DramaBGMOperationType_name, int32(x))
}
func (DramaBGMOperationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{5}
}

// 播放类型
type PiaPlayType int32

const (
	PiaPlayType_PLAY_TYPE_UNKNOWN PiaPlayType = 0
	PiaPlayType_PLAY_TYPE_AUTO    PiaPlayType = 1
	PiaPlayType_PLAY_TYPE_MANUAL  PiaPlayType = 2
)

var PiaPlayType_name = map[int32]string{
	0: "PLAY_TYPE_UNKNOWN",
	1: "PLAY_TYPE_AUTO",
	2: "PLAY_TYPE_MANUAL",
}
var PiaPlayType_value = map[string]int32{
	"PLAY_TYPE_UNKNOWN": 0,
	"PLAY_TYPE_AUTO":    1,
	"PLAY_TYPE_MANUAL":  2,
}

func (x PiaPlayType) String() string {
	return proto.EnumName(PiaPlayType_name, int32(x))
}
func (PiaPlayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{6}
}

// 在玩房间状态
type PiaChannelStatusType int32

const (
	PiaChannelStatusType_PIA_CHANNEL_STATUS_INVALID PiaChannelStatusType = 0
	PiaChannelStatusType_PIA_CHANNEL_STATUS_PLAY    PiaChannelStatusType = 1
	PiaChannelStatusType_PIA_CHANNEL_STATUS_WAIT    PiaChannelStatusType = 2
	PiaChannelStatusType_PIA_CHANNEL_STATUS_HOT     PiaChannelStatusType = 3
)

var PiaChannelStatusType_name = map[int32]string{
	0: "PIA_CHANNEL_STATUS_INVALID",
	1: "PIA_CHANNEL_STATUS_PLAY",
	2: "PIA_CHANNEL_STATUS_WAIT",
	3: "PIA_CHANNEL_STATUS_HOT",
}
var PiaChannelStatusType_value = map[string]int32{
	"PIA_CHANNEL_STATUS_INVALID": 0,
	"PIA_CHANNEL_STATUS_PLAY":    1,
	"PIA_CHANNEL_STATUS_WAIT":    2,
	"PIA_CHANNEL_STATUS_HOT":     3,
}

func (x PiaChannelStatusType) String() string {
	return proto.EnumName(PiaChannelStatusType_name, int32(x))
}
func (PiaChannelStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{7}
}

type PiaRankingListType int32

const (
	PiaRankingListType_PIA_RANKING_LIST_INVALID PiaRankingListType = 0
	PiaRankingListType_PIA_RANKING_LIST_COLLECT PiaRankingListType = 1
	PiaRankingListType_PIA_RANKING_LIST_RISE    PiaRankingListType = 2
)

var PiaRankingListType_name = map[int32]string{
	0: "PIA_RANKING_LIST_INVALID",
	1: "PIA_RANKING_LIST_COLLECT",
	2: "PIA_RANKING_LIST_RISE",
}
var PiaRankingListType_value = map[string]int32{
	"PIA_RANKING_LIST_INVALID": 0,
	"PIA_RANKING_LIST_COLLECT": 1,
	"PIA_RANKING_LIST_RISE":    2,
}

func (x PiaRankingListType) String() string {
	return proto.EnumName(PiaRankingListType_name, int32(x))
}
func (PiaRankingListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{8}
}

type CollectOpType int32

const (
	CollectOpType_PIA_DRAMA_COLLECT_INVALID CollectOpType = 0
	CollectOpType_PIA_DRAMA_COLLECT_SELECT  CollectOpType = 1
	CollectOpType_PIA_DRAMA_COLLECT_CANCEL  CollectOpType = 2
)

var CollectOpType_name = map[int32]string{
	0: "PIA_DRAMA_COLLECT_INVALID",
	1: "PIA_DRAMA_COLLECT_SELECT",
	2: "PIA_DRAMA_COLLECT_CANCEL",
}
var CollectOpType_value = map[string]int32{
	"PIA_DRAMA_COLLECT_INVALID": 0,
	"PIA_DRAMA_COLLECT_SELECT":  1,
	"PIA_DRAMA_COLLECT_CANCEL":  2,
}

func (x CollectOpType) String() string {
	return proto.EnumName(CollectOpType_name, int32(x))
}
func (CollectOpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{9}
}

type PiaAuthorWorksListSortType int32

const (
	PiaAuthorWorksListSortType_PIA_AUTHOR_WORKS_LIST_SORT_TYPE_INVALID         PiaAuthorWorksListSortType = 0
	PiaAuthorWorksListSortType_PIA_AUTHOR_WORKS_LIST_SORT_TYPE_COLLECTED_COUNT PiaAuthorWorksListSortType = 1
	PiaAuthorWorksListSortType_PIA_AUTHOR_WORKS_LIST_SORT_TYPE_CREATE_TIME     PiaAuthorWorksListSortType = 2
)

var PiaAuthorWorksListSortType_name = map[int32]string{
	0: "PIA_AUTHOR_WORKS_LIST_SORT_TYPE_INVALID",
	1: "PIA_AUTHOR_WORKS_LIST_SORT_TYPE_COLLECTED_COUNT",
	2: "PIA_AUTHOR_WORKS_LIST_SORT_TYPE_CREATE_TIME",
}
var PiaAuthorWorksListSortType_value = map[string]int32{
	"PIA_AUTHOR_WORKS_LIST_SORT_TYPE_INVALID":         0,
	"PIA_AUTHOR_WORKS_LIST_SORT_TYPE_COLLECTED_COUNT": 1,
	"PIA_AUTHOR_WORKS_LIST_SORT_TYPE_CREATE_TIME":     2,
}

func (x PiaAuthorWorksListSortType) String() string {
	return proto.EnumName(PiaAuthorWorksListSortType_name, int32(x))
}
func (PiaAuthorWorksListSortType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{10}
}

// 麦位枚举值
type PiaMicType int32

const (
	PiaMicType_PIA_MIC_TYPE_NIL     PiaMicType = 0
	PiaMicType_PIA_MIC_TYPE_VIRTUAL PiaMicType = 1
	PiaMicType_PIA_MIC_TYPE_ZERO    PiaMicType = 2
	PiaMicType_PIA_MIC_TYPE_ONE     PiaMicType = 4
	PiaMicType_PIA_MIC_TYPE_TWO     PiaMicType = 8
	PiaMicType_PIA_MIC_TYPE_THREE   PiaMicType = 16
	PiaMicType_PIA_MIC_TYPE_FOUR    PiaMicType = 32
	PiaMicType_PIA_MIC_TYPE_FIVE    PiaMicType = 64
	PiaMicType_PIA_MIC_TYPE_SIX     PiaMicType = 128
	PiaMicType_PIA_MIC_TYPE_SEVEN   PiaMicType = 256
	PiaMicType_PIA_MIC_TYPE_EIGHT   PiaMicType = 512
)

var PiaMicType_name = map[int32]string{
	0:   "PIA_MIC_TYPE_NIL",
	1:   "PIA_MIC_TYPE_VIRTUAL",
	2:   "PIA_MIC_TYPE_ZERO",
	4:   "PIA_MIC_TYPE_ONE",
	8:   "PIA_MIC_TYPE_TWO",
	16:  "PIA_MIC_TYPE_THREE",
	32:  "PIA_MIC_TYPE_FOUR",
	64:  "PIA_MIC_TYPE_FIVE",
	128: "PIA_MIC_TYPE_SIX",
	256: "PIA_MIC_TYPE_SEVEN",
	512: "PIA_MIC_TYPE_EIGHT",
}
var PiaMicType_value = map[string]int32{
	"PIA_MIC_TYPE_NIL":     0,
	"PIA_MIC_TYPE_VIRTUAL": 1,
	"PIA_MIC_TYPE_ZERO":    2,
	"PIA_MIC_TYPE_ONE":     4,
	"PIA_MIC_TYPE_TWO":     8,
	"PIA_MIC_TYPE_THREE":   16,
	"PIA_MIC_TYPE_FOUR":    32,
	"PIA_MIC_TYPE_FIVE":    64,
	"PIA_MIC_TYPE_SIX":     128,
	"PIA_MIC_TYPE_SEVEN":   256,
	"PIA_MIC_TYPE_EIGHT":   512,
}

func (x PiaMicType) String() string {
	return proto.EnumName(PiaMicType_name, int32(x))
}
func (PiaMicType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{11}
}

type DramaRoomBaseInfo_PiaStage int32

const (
	DramaRoomBaseInfo_PIA_STAGE_NONE        DramaRoomBaseInfo_PiaStage = 0
	DramaRoomBaseInfo_PIA_STAGE_SELECT_ROLE DramaRoomBaseInfo_PiaStage = 1
	DramaRoomBaseInfo_PIA_STAGE_PLAYING     DramaRoomBaseInfo_PiaStage = 2
)

var DramaRoomBaseInfo_PiaStage_name = map[int32]string{
	0: "PIA_STAGE_NONE",
	1: "PIA_STAGE_SELECT_ROLE",
	2: "PIA_STAGE_PLAYING",
}
var DramaRoomBaseInfo_PiaStage_value = map[string]int32{
	"PIA_STAGE_NONE":        0,
	"PIA_STAGE_SELECT_ROLE": 1,
	"PIA_STAGE_PLAYING":     2,
}

func (x DramaRoomBaseInfo_PiaStage) String() string {
	return proto.EnumName(DramaRoomBaseInfo_PiaStage_name, int32(x))
}
func (DramaRoomBaseInfo_PiaStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{37, 0}
}

type GetSearchOptionGroupV2Req_SearchOptionType int32

const (
	GetSearchOptionGroupV2Req_SEARCH_OPTION_TYPE_DRAMA GetSearchOptionGroupV2Req_SearchOptionType = 0
	GetSearchOptionGroupV2Req_SEARCH_OPTION_TYPE_COPY  GetSearchOptionGroupV2Req_SearchOptionType = 1
)

var GetSearchOptionGroupV2Req_SearchOptionType_name = map[int32]string{
	0: "SEARCH_OPTION_TYPE_DRAMA",
	1: "SEARCH_OPTION_TYPE_COPY",
}
var GetSearchOptionGroupV2Req_SearchOptionType_value = map[string]int32{
	"SEARCH_OPTION_TYPE_DRAMA": 0,
	"SEARCH_OPTION_TYPE_COPY":  1,
}

func (x GetSearchOptionGroupV2Req_SearchOptionType) String() string {
	return proto.EnumName(GetSearchOptionGroupV2Req_SearchOptionType_name, int32(x))
}
func (GetSearchOptionGroupV2Req_SearchOptionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{79, 0}
}

type PiaChannelDramaPlayingType_PlayingTypeRole int32

const (
	PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_INVALID PiaChannelDramaPlayingType_PlayingTypeRole = 0
	PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE    PiaChannelDramaPlayingType_PlayingTypeRole = 1
	PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS     PiaChannelDramaPlayingType_PlayingTypeRole = 2
)

var PiaChannelDramaPlayingType_PlayingTypeRole_name = map[int32]string{
	0: "PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_INVALID",
	1: "PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE",
	2: "PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS",
}
var PiaChannelDramaPlayingType_PlayingTypeRole_value = map[string]int32{
	"PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_INVALID": 0,
	"PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE":    1,
	"PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS":     2,
}

func (x PiaChannelDramaPlayingType_PlayingTypeRole) String() string {
	return proto.EnumName(PiaChannelDramaPlayingType_PlayingTypeRole_name, int32(x))
}
func (PiaChannelDramaPlayingType_PlayingTypeRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{95, 0}
}

type PiaChannelDramaPlayingType_PlayingTypeTime int32

const (
	PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_INVALID PiaChannelDramaPlayingType_PlayingTypeTime = 0
	PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE    PiaChannelDramaPlayingType_PlayingTypeTime = 1
	PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS     PiaChannelDramaPlayingType_PlayingTypeTime = 2
)

var PiaChannelDramaPlayingType_PlayingTypeTime_name = map[int32]string{
	0: "PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_INVALID",
	1: "PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE",
	2: "PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS",
}
var PiaChannelDramaPlayingType_PlayingTypeTime_value = map[string]int32{
	"PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_INVALID": 0,
	"PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE":    1,
	"PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS":     2,
}

func (x PiaChannelDramaPlayingType_PlayingTypeTime) String() string {
	return proto.EnumName(PiaChannelDramaPlayingType_PlayingTypeTime_name, int32(x))
}
func (PiaChannelDramaPlayingType_PlayingTypeTime) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{95, 1}
}

type SetDramaCopyStatusReq_DramaCopyStatus int32

const (
	SetDramaCopyStatusReq_DRAMA_COPY_STATUS_INVALID SetDramaCopyStatusReq_DramaCopyStatus = 0
	SetDramaCopyStatusReq_DRAMA_COPY_STATUS_PUBLIC  SetDramaCopyStatusReq_DramaCopyStatus = 1
	SetDramaCopyStatusReq_DRAMA_COPY_STATUS_PRIVATE SetDramaCopyStatusReq_DramaCopyStatus = 2
)

var SetDramaCopyStatusReq_DramaCopyStatus_name = map[int32]string{
	0: "DRAMA_COPY_STATUS_INVALID",
	1: "DRAMA_COPY_STATUS_PUBLIC",
	2: "DRAMA_COPY_STATUS_PRIVATE",
}
var SetDramaCopyStatusReq_DramaCopyStatus_value = map[string]int32{
	"DRAMA_COPY_STATUS_INVALID": 0,
	"DRAMA_COPY_STATUS_PUBLIC":  1,
	"DRAMA_COPY_STATUS_PRIVATE": 2,
}

func (x SetDramaCopyStatusReq_DramaCopyStatus) String() string {
	return proto.EnumName(SetDramaCopyStatusReq_DramaCopyStatus_name, int32(x))
}
func (SetDramaCopyStatusReq_DramaCopyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{117, 0}
}

// 获取房间Pia戏开启状态
type GetChannelPiaStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelPiaStatusReq) Reset()         { *m = GetChannelPiaStatusReq{} }
func (m *GetChannelPiaStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelPiaStatusReq) ProtoMessage()    {}
func (*GetChannelPiaStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{0}
}
func (m *GetChannelPiaStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPiaStatusReq.Unmarshal(m, b)
}
func (m *GetChannelPiaStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPiaStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelPiaStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPiaStatusReq.Merge(dst, src)
}
func (m *GetChannelPiaStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelPiaStatusReq.Size(m)
}
func (m *GetChannelPiaStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPiaStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPiaStatusReq proto.InternalMessageInfo

func (m *GetChannelPiaStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelPiaStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelPiaStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Entry                bool          `protobuf:"varint,3,opt,name=entry,proto3" json:"entry,omitempty"`
	IsOpen               bool          `protobuf:"varint,4,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelPiaStatusResp) Reset()         { *m = GetChannelPiaStatusResp{} }
func (m *GetChannelPiaStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelPiaStatusResp) ProtoMessage()    {}
func (*GetChannelPiaStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{1}
}
func (m *GetChannelPiaStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelPiaStatusResp.Unmarshal(m, b)
}
func (m *GetChannelPiaStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelPiaStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelPiaStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelPiaStatusResp.Merge(dst, src)
}
func (m *GetChannelPiaStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelPiaStatusResp.Size(m)
}
func (m *GetChannelPiaStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelPiaStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelPiaStatusResp proto.InternalMessageInfo

func (m *GetChannelPiaStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelPiaStatusResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelPiaStatusResp) GetEntry() bool {
	if m != nil {
		return m.Entry
	}
	return false
}

func (m *GetChannelPiaStatusResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

// 设置主持麦位
type SetCompereMicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Mic                  uint32       `protobuf:"varint,3,opt,name=mic,proto3" json:"mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetCompereMicReq) Reset()         { *m = SetCompereMicReq{} }
func (m *SetCompereMicReq) String() string { return proto.CompactTextString(m) }
func (*SetCompereMicReq) ProtoMessage()    {}
func (*SetCompereMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{2}
}
func (m *SetCompereMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCompereMicReq.Unmarshal(m, b)
}
func (m *SetCompereMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCompereMicReq.Marshal(b, m, deterministic)
}
func (dst *SetCompereMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCompereMicReq.Merge(dst, src)
}
func (m *SetCompereMicReq) XXX_Size() int {
	return xxx_messageInfo_SetCompereMicReq.Size(m)
}
func (m *SetCompereMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCompereMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCompereMicReq proto.InternalMessageInfo

func (m *SetCompereMicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetCompereMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetCompereMicReq) GetMic() uint32 {
	if m != nil {
		return m.Mic
	}
	return 0
}

type SetCompereMicResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetCompereMicResp) Reset()         { *m = SetCompereMicResp{} }
func (m *SetCompereMicResp) String() string { return proto.CompactTextString(m) }
func (*SetCompereMicResp) ProtoMessage()    {}
func (*SetCompereMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{3}
}
func (m *SetCompereMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCompereMicResp.Unmarshal(m, b)
}
func (m *SetCompereMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCompereMicResp.Marshal(b, m, deterministic)
}
func (dst *SetCompereMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCompereMicResp.Merge(dst, src)
}
func (m *SetCompereMicResp) XXX_Size() int {
	return xxx_messageInfo_SetCompereMicResp.Size(m)
}
func (m *SetCompereMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCompereMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCompereMicResp proto.InternalMessageInfo

func (m *SetCompereMicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 切换成Pia戏模式
type SetPiaSwitchReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IsOpen               bool         `protobuf:"varint,3,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetPiaSwitchReq) Reset()         { *m = SetPiaSwitchReq{} }
func (m *SetPiaSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetPiaSwitchReq) ProtoMessage()    {}
func (*SetPiaSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{4}
}
func (m *SetPiaSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPiaSwitchReq.Unmarshal(m, b)
}
func (m *SetPiaSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPiaSwitchReq.Marshal(b, m, deterministic)
}
func (dst *SetPiaSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPiaSwitchReq.Merge(dst, src)
}
func (m *SetPiaSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetPiaSwitchReq.Size(m)
}
func (m *SetPiaSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPiaSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPiaSwitchReq proto.InternalMessageInfo

func (m *SetPiaSwitchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetPiaSwitchReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPiaSwitchReq) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

type SetPiaSwitchResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetPiaSwitchResp) Reset()         { *m = SetPiaSwitchResp{} }
func (m *SetPiaSwitchResp) String() string { return proto.CompactTextString(m) }
func (*SetPiaSwitchResp) ProtoMessage()    {}
func (*SetPiaSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{5}
}
func (m *SetPiaSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPiaSwitchResp.Unmarshal(m, b)
}
func (m *SetPiaSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPiaSwitchResp.Marshal(b, m, deterministic)
}
func (dst *SetPiaSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPiaSwitchResp.Merge(dst, src)
}
func (m *SetPiaSwitchResp) XXX_Size() int {
	return xxx_messageInfo_SetPiaSwitchResp.Size(m)
}
func (m *SetPiaSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPiaSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPiaSwitchResp proto.InternalMessageInfo

func (m *SetPiaSwitchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取剧本概要
type GetDramaReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SearchOption         []*SearchOption `protobuf:"bytes,2,rep,name=search_option,json=searchOption,proto3" json:"search_option,omitempty"`
	PageToken            uint32          `protobuf:"varint,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32          `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetDramaReq) Reset()         { *m = GetDramaReq{} }
func (m *GetDramaReq) String() string { return proto.CompactTextString(m) }
func (*GetDramaReq) ProtoMessage()    {}
func (*GetDramaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{6}
}
func (m *GetDramaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaReq.Unmarshal(m, b)
}
func (m *GetDramaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaReq.Marshal(b, m, deterministic)
}
func (dst *GetDramaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaReq.Merge(dst, src)
}
func (m *GetDramaReq) XXX_Size() int {
	return xxx_messageInfo_GetDramaReq.Size(m)
}
func (m *GetDramaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaReq proto.InternalMessageInfo

func (m *GetDramaReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetDramaReq) GetSearchOption() []*SearchOption {
	if m != nil {
		return m.SearchOption
	}
	return nil
}

func (m *GetDramaReq) GetPageToken() uint32 {
	if m != nil {
		return m.PageToken
	}
	return 0
}

func (m *GetDramaReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 剧本信息
type Drama struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	CoverUrl             string   `protobuf:"bytes,3,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Author               string   `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
	Desc                 string   `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	MaleCnt              uint32   `protobuf:"varint,6,opt,name=male_cnt,json=maleCnt,proto3" json:"male_cnt,omitempty"`
	FemaleCnt            uint32   `protobuf:"varint,7,opt,name=female_cnt,json=femaleCnt,proto3" json:"female_cnt,omitempty"`
	WordCnt              uint32   `protobuf:"varint,8,opt,name=word_cnt,json=wordCnt,proto3" json:"word_cnt,omitempty"`
	BgmUrl               []string `protobuf:"bytes,9,rep,name=bgm_url,json=bgmUrl,proto3" json:"bgm_url,omitempty"`
	Tags                 []string `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty"`
	HasPlaying           bool     `protobuf:"varint,11,opt,name=has_playing,json=hasPlaying,proto3" json:"has_playing,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Drama) Reset()         { *m = Drama{} }
func (m *Drama) String() string { return proto.CompactTextString(m) }
func (*Drama) ProtoMessage()    {}
func (*Drama) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{7}
}
func (m *Drama) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Drama.Unmarshal(m, b)
}
func (m *Drama) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Drama.Marshal(b, m, deterministic)
}
func (dst *Drama) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Drama.Merge(dst, src)
}
func (m *Drama) XXX_Size() int {
	return xxx_messageInfo_Drama.Size(m)
}
func (m *Drama) XXX_DiscardUnknown() {
	xxx_messageInfo_Drama.DiscardUnknown(m)
}

var xxx_messageInfo_Drama proto.InternalMessageInfo

func (m *Drama) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Drama) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Drama) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *Drama) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *Drama) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *Drama) GetMaleCnt() uint32 {
	if m != nil {
		return m.MaleCnt
	}
	return 0
}

func (m *Drama) GetFemaleCnt() uint32 {
	if m != nil {
		return m.FemaleCnt
	}
	return 0
}

func (m *Drama) GetWordCnt() uint32 {
	if m != nil {
		return m.WordCnt
	}
	return 0
}

func (m *Drama) GetBgmUrl() []string {
	if m != nil {
		return m.BgmUrl
	}
	return nil
}

func (m *Drama) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *Drama) GetHasPlaying() bool {
	if m != nil {
		return m.HasPlaying
	}
	return false
}

// 获取剧本概要
type GetDramaResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaList            []*Drama      `protobuf:"bytes,2,rep,name=drama_list,json=dramaList,proto3" json:"drama_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetDramaResp) Reset()         { *m = GetDramaResp{} }
func (m *GetDramaResp) String() string { return proto.CompactTextString(m) }
func (*GetDramaResp) ProtoMessage()    {}
func (*GetDramaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{8}
}
func (m *GetDramaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaResp.Unmarshal(m, b)
}
func (m *GetDramaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaResp.Marshal(b, m, deterministic)
}
func (dst *GetDramaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaResp.Merge(dst, src)
}
func (m *GetDramaResp) XXX_Size() int {
	return xxx_messageInfo_GetDramaResp.Size(m)
}
func (m *GetDramaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaResp proto.InternalMessageInfo

func (m *GetDramaResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetDramaResp) GetDramaList() []*Drama {
	if m != nil {
		return m.DramaList
	}
	return nil
}

// 获取筛选标签组
type GetSearchOptionGroupReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSearchOptionGroupReq) Reset()         { *m = GetSearchOptionGroupReq{} }
func (m *GetSearchOptionGroupReq) String() string { return proto.CompactTextString(m) }
func (*GetSearchOptionGroupReq) ProtoMessage()    {}
func (*GetSearchOptionGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{9}
}
func (m *GetSearchOptionGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchOptionGroupReq.Unmarshal(m, b)
}
func (m *GetSearchOptionGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchOptionGroupReq.Marshal(b, m, deterministic)
}
func (dst *GetSearchOptionGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchOptionGroupReq.Merge(dst, src)
}
func (m *GetSearchOptionGroupReq) XXX_Size() int {
	return xxx_messageInfo_GetSearchOptionGroupReq.Size(m)
}
func (m *GetSearchOptionGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchOptionGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchOptionGroupReq proto.InternalMessageInfo

func (m *GetSearchOptionGroupReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// 筛选标签组
type SearchOptionGroup struct {
	GroupName            string                  `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	SubSearchOptionGroup []*SubSearchOptionGroup `protobuf:"bytes,2,rep,name=sub_search_option_group,json=subSearchOptionGroup,proto3" json:"sub_search_option_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SearchOptionGroup) Reset()         { *m = SearchOptionGroup{} }
func (m *SearchOptionGroup) String() string { return proto.CompactTextString(m) }
func (*SearchOptionGroup) ProtoMessage()    {}
func (*SearchOptionGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{10}
}
func (m *SearchOptionGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchOptionGroup.Unmarshal(m, b)
}
func (m *SearchOptionGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchOptionGroup.Marshal(b, m, deterministic)
}
func (dst *SearchOptionGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchOptionGroup.Merge(dst, src)
}
func (m *SearchOptionGroup) XXX_Size() int {
	return xxx_messageInfo_SearchOptionGroup.Size(m)
}
func (m *SearchOptionGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchOptionGroup.DiscardUnknown(m)
}

var xxx_messageInfo_SearchOptionGroup proto.InternalMessageInfo

func (m *SearchOptionGroup) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *SearchOptionGroup) GetSubSearchOptionGroup() []*SubSearchOptionGroup {
	if m != nil {
		return m.SubSearchOptionGroup
	}
	return nil
}

// 筛选标签子分组名
type SubSearchOptionGroup struct {
	SearchType           uint32          `protobuf:"varint,1,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	GroupName            string          `protobuf:"bytes,2,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	SearchOption         []*SearchOption `protobuf:"bytes,3,rep,name=search_option,json=searchOption,proto3" json:"search_option,omitempty"`
	DisplayFormat        string          `protobuf:"bytes,4,opt,name=display_format,json=displayFormat,proto3" json:"display_format,omitempty"`
	DisplaySeparator     string          `protobuf:"bytes,5,opt,name=display_separator,json=displaySeparator,proto3" json:"display_separator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SubSearchOptionGroup) Reset()         { *m = SubSearchOptionGroup{} }
func (m *SubSearchOptionGroup) String() string { return proto.CompactTextString(m) }
func (*SubSearchOptionGroup) ProtoMessage()    {}
func (*SubSearchOptionGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{11}
}
func (m *SubSearchOptionGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubSearchOptionGroup.Unmarshal(m, b)
}
func (m *SubSearchOptionGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubSearchOptionGroup.Marshal(b, m, deterministic)
}
func (dst *SubSearchOptionGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubSearchOptionGroup.Merge(dst, src)
}
func (m *SubSearchOptionGroup) XXX_Size() int {
	return xxx_messageInfo_SubSearchOptionGroup.Size(m)
}
func (m *SubSearchOptionGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_SubSearchOptionGroup.DiscardUnknown(m)
}

var xxx_messageInfo_SubSearchOptionGroup proto.InternalMessageInfo

func (m *SubSearchOptionGroup) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *SubSearchOptionGroup) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *SubSearchOptionGroup) GetSearchOption() []*SearchOption {
	if m != nil {
		return m.SearchOption
	}
	return nil
}

func (m *SubSearchOptionGroup) GetDisplayFormat() string {
	if m != nil {
		return m.DisplayFormat
	}
	return ""
}

func (m *SubSearchOptionGroup) GetDisplaySeparator() string {
	if m != nil {
		return m.DisplaySeparator
	}
	return ""
}

// 获取筛选标签组
type GetSearchOptionGroupResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SearchOptionGroup    []*SearchOptionGroup `protobuf:"bytes,2,rep,name=search_option_group,json=searchOptionGroup,proto3" json:"search_option_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetSearchOptionGroupResp) Reset()         { *m = GetSearchOptionGroupResp{} }
func (m *GetSearchOptionGroupResp) String() string { return proto.CompactTextString(m) }
func (*GetSearchOptionGroupResp) ProtoMessage()    {}
func (*GetSearchOptionGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{12}
}
func (m *GetSearchOptionGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchOptionGroupResp.Unmarshal(m, b)
}
func (m *GetSearchOptionGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchOptionGroupResp.Marshal(b, m, deterministic)
}
func (dst *GetSearchOptionGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchOptionGroupResp.Merge(dst, src)
}
func (m *GetSearchOptionGroupResp) XXX_Size() int {
	return xxx_messageInfo_GetSearchOptionGroupResp.Size(m)
}
func (m *GetSearchOptionGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchOptionGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchOptionGroupResp proto.InternalMessageInfo

func (m *GetSearchOptionGroupResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSearchOptionGroupResp) GetSearchOptionGroup() []*SearchOptionGroup {
	if m != nil {
		return m.SearchOptionGroup
	}
	return nil
}

// 筛选项
type SearchOption struct {
	SearchType           uint32   `protobuf:"varint,1,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	Label                string   `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchOption) Reset()         { *m = SearchOption{} }
func (m *SearchOption) String() string { return proto.CompactTextString(m) }
func (*SearchOption) ProtoMessage()    {}
func (*SearchOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{13}
}
func (m *SearchOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchOption.Unmarshal(m, b)
}
func (m *SearchOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchOption.Marshal(b, m, deterministic)
}
func (dst *SearchOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchOption.Merge(dst, src)
}
func (m *SearchOption) XXX_Size() int {
	return xxx_messageInfo_SearchOption.Size(m)
}
func (m *SearchOption) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchOption.DiscardUnknown(m)
}

var xxx_messageInfo_SearchOption proto.InternalMessageInfo

func (m *SearchOption) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *SearchOption) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

// 获取当前房间Pia戏信息
type GetCurrentPiaInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCurrentPiaInfoReq) Reset()         { *m = GetCurrentPiaInfoReq{} }
func (m *GetCurrentPiaInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetCurrentPiaInfoReq) ProtoMessage()    {}
func (*GetCurrentPiaInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{14}
}
func (m *GetCurrentPiaInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrentPiaInfoReq.Unmarshal(m, b)
}
func (m *GetCurrentPiaInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrentPiaInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetCurrentPiaInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrentPiaInfoReq.Merge(dst, src)
}
func (m *GetCurrentPiaInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetCurrentPiaInfoReq.Size(m)
}
func (m *GetCurrentPiaInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrentPiaInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrentPiaInfoReq proto.InternalMessageInfo

func (m *GetCurrentPiaInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCurrentPiaInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// Pia戏信息, (广播类型见channel_.proto ChannelMsgType.PIA_INFO)
type PiaInfo struct {
	Drama                *Drama   `protobuf:"bytes,1,opt,name=drama,proto3" json:"drama,omitempty"`
	Phase                uint32   `protobuf:"varint,2,opt,name=phase,proto3" json:"phase,omitempty"`
	Progress             string   `protobuf:"bytes,3,opt,name=progress,proto3" json:"progress,omitempty"`
	CompereMic           uint32   `protobuf:"varint,4,opt,name=compere_mic,json=compereMic,proto3" json:"compere_mic,omitempty"`
	AllMicProgress       []string `protobuf:"bytes,5,rep,name=all_mic_progress,json=allMicProgress,proto3" json:"all_mic_progress,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaInfo) Reset()         { *m = PiaInfo{} }
func (m *PiaInfo) String() string { return proto.CompactTextString(m) }
func (*PiaInfo) ProtoMessage()    {}
func (*PiaInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{15}
}
func (m *PiaInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaInfo.Unmarshal(m, b)
}
func (m *PiaInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaInfo.Marshal(b, m, deterministic)
}
func (dst *PiaInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaInfo.Merge(dst, src)
}
func (m *PiaInfo) XXX_Size() int {
	return xxx_messageInfo_PiaInfo.Size(m)
}
func (m *PiaInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PiaInfo proto.InternalMessageInfo

func (m *PiaInfo) GetDrama() *Drama {
	if m != nil {
		return m.Drama
	}
	return nil
}

func (m *PiaInfo) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *PiaInfo) GetProgress() string {
	if m != nil {
		return m.Progress
	}
	return ""
}

func (m *PiaInfo) GetCompereMic() uint32 {
	if m != nil {
		return m.CompereMic
	}
	return 0
}

func (m *PiaInfo) GetAllMicProgress() []string {
	if m != nil {
		return m.AllMicProgress
	}
	return nil
}

// 获取当前房间Pia戏信息
type GetCurrentPiaInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PiaInfo              *PiaInfo      `protobuf:"bytes,2,opt,name=pia_info,json=piaInfo,proto3" json:"pia_info,omitempty"`
	MsgForScreen         string        `protobuf:"bytes,3,opt,name=msg_for_screen,json=msgForScreen,proto3" json:"msg_for_screen,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCurrentPiaInfoResp) Reset()         { *m = GetCurrentPiaInfoResp{} }
func (m *GetCurrentPiaInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetCurrentPiaInfoResp) ProtoMessage()    {}
func (*GetCurrentPiaInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{16}
}
func (m *GetCurrentPiaInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurrentPiaInfoResp.Unmarshal(m, b)
}
func (m *GetCurrentPiaInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurrentPiaInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetCurrentPiaInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurrentPiaInfoResp.Merge(dst, src)
}
func (m *GetCurrentPiaInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetCurrentPiaInfoResp.Size(m)
}
func (m *GetCurrentPiaInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurrentPiaInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurrentPiaInfoResp proto.InternalMessageInfo

func (m *GetCurrentPiaInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCurrentPiaInfoResp) GetPiaInfo() *PiaInfo {
	if m != nil {
		return m.PiaInfo
	}
	return nil
}

func (m *GetCurrentPiaInfoResp) GetMsgForScreen() string {
	if m != nil {
		return m.MsgForScreen
	}
	return ""
}

// 设置Pia戏阶段
type SetPiaPhaseReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DramaId              uint32       `protobuf:"varint,3,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	Phase                uint32       `protobuf:"varint,4,opt,name=phase,proto3" json:"phase,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetPiaPhaseReq) Reset()         { *m = SetPiaPhaseReq{} }
func (m *SetPiaPhaseReq) String() string { return proto.CompactTextString(m) }
func (*SetPiaPhaseReq) ProtoMessage()    {}
func (*SetPiaPhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{17}
}
func (m *SetPiaPhaseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPiaPhaseReq.Unmarshal(m, b)
}
func (m *SetPiaPhaseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPiaPhaseReq.Marshal(b, m, deterministic)
}
func (dst *SetPiaPhaseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPiaPhaseReq.Merge(dst, src)
}
func (m *SetPiaPhaseReq) XXX_Size() int {
	return xxx_messageInfo_SetPiaPhaseReq.Size(m)
}
func (m *SetPiaPhaseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPiaPhaseReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPiaPhaseReq proto.InternalMessageInfo

func (m *SetPiaPhaseReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetPiaPhaseReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPiaPhaseReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

func (m *SetPiaPhaseReq) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

// 设置Pia戏阶段
type SetPiaPhaseResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetPiaPhaseResp) Reset()         { *m = SetPiaPhaseResp{} }
func (m *SetPiaPhaseResp) String() string { return proto.CompactTextString(m) }
func (*SetPiaPhaseResp) ProtoMessage()    {}
func (*SetPiaPhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{18}
}
func (m *SetPiaPhaseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPiaPhaseResp.Unmarshal(m, b)
}
func (m *SetPiaPhaseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPiaPhaseResp.Marshal(b, m, deterministic)
}
func (dst *SetPiaPhaseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPiaPhaseResp.Merge(dst, src)
}
func (m *SetPiaPhaseResp) XXX_Size() int {
	return xxx_messageInfo_SetPiaPhaseResp.Size(m)
}
func (m *SetPiaPhaseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPiaPhaseResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPiaPhaseResp proto.InternalMessageInfo

func (m *SetPiaPhaseResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 设置Pia戏进度
type SetPiaProgressReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Progress             string       `protobuf:"bytes,4,opt,name=progress,proto3" json:"progress,omitempty"`
	OpMic                uint32       `protobuf:"varint,5,opt,name=op_mic,json=opMic,proto3" json:"op_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetPiaProgressReq) Reset()         { *m = SetPiaProgressReq{} }
func (m *SetPiaProgressReq) String() string { return proto.CompactTextString(m) }
func (*SetPiaProgressReq) ProtoMessage()    {}
func (*SetPiaProgressReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{19}
}
func (m *SetPiaProgressReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPiaProgressReq.Unmarshal(m, b)
}
func (m *SetPiaProgressReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPiaProgressReq.Marshal(b, m, deterministic)
}
func (dst *SetPiaProgressReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPiaProgressReq.Merge(dst, src)
}
func (m *SetPiaProgressReq) XXX_Size() int {
	return xxx_messageInfo_SetPiaProgressReq.Size(m)
}
func (m *SetPiaProgressReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPiaProgressReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPiaProgressReq proto.InternalMessageInfo

func (m *SetPiaProgressReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetPiaProgressReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPiaProgressReq) GetProgress() string {
	if m != nil {
		return m.Progress
	}
	return ""
}

func (m *SetPiaProgressReq) GetOpMic() uint32 {
	if m != nil {
		return m.OpMic
	}
	return 0
}

// 设置Pia戏进度
type SetPiaProgressResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetPiaProgressResp) Reset()         { *m = SetPiaProgressResp{} }
func (m *SetPiaProgressResp) String() string { return proto.CompactTextString(m) }
func (*SetPiaProgressResp) ProtoMessage()    {}
func (*SetPiaProgressResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{20}
}
func (m *SetPiaProgressResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPiaProgressResp.Unmarshal(m, b)
}
func (m *SetPiaProgressResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPiaProgressResp.Marshal(b, m, deterministic)
}
func (dst *SetPiaProgressResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPiaProgressResp.Merge(dst, src)
}
func (m *SetPiaProgressResp) XXX_Size() int {
	return xxx_messageInfo_SetPiaProgressResp.Size(m)
}
func (m *SetPiaProgressResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPiaProgressResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPiaProgressResp proto.InternalMessageInfo

func (m *SetPiaProgressResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 设置bgm进度
type SetBgmInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BgmInfo              string       `protobuf:"bytes,3,opt,name=bgm_info,json=bgmInfo,proto3" json:"bgm_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetBgmInfoReq) Reset()         { *m = SetBgmInfoReq{} }
func (m *SetBgmInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetBgmInfoReq) ProtoMessage()    {}
func (*SetBgmInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{21}
}
func (m *SetBgmInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBgmInfoReq.Unmarshal(m, b)
}
func (m *SetBgmInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBgmInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetBgmInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBgmInfoReq.Merge(dst, src)
}
func (m *SetBgmInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetBgmInfoReq.Size(m)
}
func (m *SetBgmInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBgmInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBgmInfoReq proto.InternalMessageInfo

func (m *SetBgmInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetBgmInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetBgmInfoReq) GetBgmInfo() string {
	if m != nil {
		return m.BgmInfo
	}
	return ""
}

// 设置bgm进度
type SetBgmInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetBgmInfoResp) Reset()         { *m = SetBgmInfoResp{} }
func (m *SetBgmInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetBgmInfoResp) ProtoMessage()    {}
func (*SetBgmInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{22}
}
func (m *SetBgmInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBgmInfoResp.Unmarshal(m, b)
}
func (m *SetBgmInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBgmInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetBgmInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBgmInfoResp.Merge(dst, src)
}
func (m *SetBgmInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetBgmInfoResp.Size(m)
}
func (m *SetBgmInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBgmInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBgmInfoResp proto.InternalMessageInfo

func (m *SetBgmInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取bgm进度
type GetBgmInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBgmInfoReq) Reset()         { *m = GetBgmInfoReq{} }
func (m *GetBgmInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBgmInfoReq) ProtoMessage()    {}
func (*GetBgmInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{23}
}
func (m *GetBgmInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBgmInfoReq.Unmarshal(m, b)
}
func (m *GetBgmInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBgmInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBgmInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBgmInfoReq.Merge(dst, src)
}
func (m *GetBgmInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBgmInfoReq.Size(m)
}
func (m *GetBgmInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBgmInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBgmInfoReq proto.InternalMessageInfo

func (m *GetBgmInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBgmInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 获取bgm进度
type GetBgmInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BgmInfo              string        `protobuf:"bytes,2,opt,name=bgm_info,json=bgmInfo,proto3" json:"bgm_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBgmInfoResp) Reset()         { *m = GetBgmInfoResp{} }
func (m *GetBgmInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBgmInfoResp) ProtoMessage()    {}
func (*GetBgmInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{24}
}
func (m *GetBgmInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBgmInfoResp.Unmarshal(m, b)
}
func (m *GetBgmInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBgmInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBgmInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBgmInfoResp.Merge(dst, src)
}
func (m *GetBgmInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBgmInfoResp.Size(m)
}
func (m *GetBgmInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBgmInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBgmInfoResp proto.InternalMessageInfo

func (m *GetBgmInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBgmInfoResp) GetBgmInfo() string {
	if m != nil {
		return m.BgmInfo
	}
	return ""
}

// 获取正在玩的房间
type GetPlayingChannelReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	DramaId              uint32       `protobuf:"varint,2,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPlayingChannelReq) Reset()         { *m = GetPlayingChannelReq{} }
func (m *GetPlayingChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayingChannelReq) ProtoMessage()    {}
func (*GetPlayingChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{25}
}
func (m *GetPlayingChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayingChannelReq.Unmarshal(m, b)
}
func (m *GetPlayingChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayingChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayingChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayingChannelReq.Merge(dst, src)
}
func (m *GetPlayingChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayingChannelReq.Size(m)
}
func (m *GetPlayingChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayingChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayingChannelReq proto.InternalMessageInfo

func (m *GetPlayingChannelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPlayingChannelReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

// 获取正在玩的房间
type PlayingChannelInfo struct {
	ChannelId             uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName           string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelMemberCount    uint32   `protobuf:"varint,3,opt,name=channel_member_count,json=channelMemberCount,proto3" json:"channel_member_count,omitempty"`
	ChannelDisplayId      uint32   `protobuf:"varint,4,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	ChannelType           uint32   `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelIcon           string   `protobuf:"bytes,6,opt,name=channel_icon,json=channelIcon,proto3" json:"channel_icon,omitempty"`
	ChannelDesc           string   `protobuf:"bytes,7,opt,name=channel_desc,json=channelDesc,proto3" json:"channel_desc,omitempty"`
	ChannelCreatorAccount string   `protobuf:"bytes,8,opt,name=channel_creator_account,json=channelCreatorAccount,proto3" json:"channel_creator_account,omitempty"`
	IsPlaying             bool     `protobuf:"varint,9,opt,name=is_playing,json=isPlaying,proto3" json:"is_playing,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *PlayingChannelInfo) Reset()         { *m = PlayingChannelInfo{} }
func (m *PlayingChannelInfo) String() string { return proto.CompactTextString(m) }
func (*PlayingChannelInfo) ProtoMessage()    {}
func (*PlayingChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{26}
}
func (m *PlayingChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayingChannelInfo.Unmarshal(m, b)
}
func (m *PlayingChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayingChannelInfo.Marshal(b, m, deterministic)
}
func (dst *PlayingChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayingChannelInfo.Merge(dst, src)
}
func (m *PlayingChannelInfo) XXX_Size() int {
	return xxx_messageInfo_PlayingChannelInfo.Size(m)
}
func (m *PlayingChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayingChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PlayingChannelInfo proto.InternalMessageInfo

func (m *PlayingChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PlayingChannelInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PlayingChannelInfo) GetChannelMemberCount() uint32 {
	if m != nil {
		return m.ChannelMemberCount
	}
	return 0
}

func (m *PlayingChannelInfo) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *PlayingChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *PlayingChannelInfo) GetChannelIcon() string {
	if m != nil {
		return m.ChannelIcon
	}
	return ""
}

func (m *PlayingChannelInfo) GetChannelDesc() string {
	if m != nil {
		return m.ChannelDesc
	}
	return ""
}

func (m *PlayingChannelInfo) GetChannelCreatorAccount() string {
	if m != nil {
		return m.ChannelCreatorAccount
	}
	return ""
}

func (m *PlayingChannelInfo) GetIsPlaying() bool {
	if m != nil {
		return m.IsPlaying
	}
	return false
}

// 获取正在玩的房间
type GetPlayingChannelResp struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PlayingChannels      []*PlayingChannelInfo `protobuf:"bytes,2,rep,name=playing_channels,json=playingChannels,proto3" json:"playing_channels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPlayingChannelResp) Reset()         { *m = GetPlayingChannelResp{} }
func (m *GetPlayingChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayingChannelResp) ProtoMessage()    {}
func (*GetPlayingChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{27}
}
func (m *GetPlayingChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayingChannelResp.Unmarshal(m, b)
}
func (m *GetPlayingChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayingChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayingChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayingChannelResp.Merge(dst, src)
}
func (m *GetPlayingChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayingChannelResp.Size(m)
}
func (m *GetPlayingChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayingChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayingChannelResp proto.InternalMessageInfo

func (m *GetPlayingChannelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayingChannelResp) GetPlayingChannels() []*PlayingChannelInfo {
	if m != nil {
		return m.PlayingChannels
	}
	return nil
}

// Pia戏模式切换广播, 见channel_.proto ChannelMsgType.PIA_SWITCH
type PiaSwitch struct {
	IsOpen               bool     `protobuf:"varint,1,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	PiaInfo              *PiaInfo `protobuf:"bytes,2,opt,name=pia_info,json=piaInfo,proto3" json:"pia_info,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Hint                 string   `protobuf:"bytes,4,opt,name=hint,proto3" json:"hint,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaSwitch) Reset()         { *m = PiaSwitch{} }
func (m *PiaSwitch) String() string { return proto.CompactTextString(m) }
func (*PiaSwitch) ProtoMessage()    {}
func (*PiaSwitch) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{28}
}
func (m *PiaSwitch) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaSwitch.Unmarshal(m, b)
}
func (m *PiaSwitch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaSwitch.Marshal(b, m, deterministic)
}
func (dst *PiaSwitch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaSwitch.Merge(dst, src)
}
func (m *PiaSwitch) XXX_Size() int {
	return xxx_messageInfo_PiaSwitch.Size(m)
}
func (m *PiaSwitch) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaSwitch.DiscardUnknown(m)
}

var xxx_messageInfo_PiaSwitch proto.InternalMessageInfo

func (m *PiaSwitch) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *PiaSwitch) GetPiaInfo() *PiaInfo {
	if m != nil {
		return m.PiaInfo
	}
	return nil
}

func (m *PiaSwitch) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *PiaSwitch) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

// 选本
type SelectDramaReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DramaId              uint32       `protobuf:"varint,3,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SelectDramaReq) Reset()         { *m = SelectDramaReq{} }
func (m *SelectDramaReq) String() string { return proto.CompactTextString(m) }
func (*SelectDramaReq) ProtoMessage()    {}
func (*SelectDramaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{29}
}
func (m *SelectDramaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectDramaReq.Unmarshal(m, b)
}
func (m *SelectDramaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectDramaReq.Marshal(b, m, deterministic)
}
func (dst *SelectDramaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectDramaReq.Merge(dst, src)
}
func (m *SelectDramaReq) XXX_Size() int {
	return xxx_messageInfo_SelectDramaReq.Size(m)
}
func (m *SelectDramaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectDramaReq.DiscardUnknown(m)
}

var xxx_messageInfo_SelectDramaReq proto.InternalMessageInfo

func (m *SelectDramaReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SelectDramaReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SelectDramaReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

// 选本
type SelectDramaResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SelectDramaResp) Reset()         { *m = SelectDramaResp{} }
func (m *SelectDramaResp) String() string { return proto.CompactTextString(m) }
func (*SelectDramaResp) ProtoMessage()    {}
func (*SelectDramaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{30}
}
func (m *SelectDramaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectDramaResp.Unmarshal(m, b)
}
func (m *SelectDramaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectDramaResp.Marshal(b, m, deterministic)
}
func (dst *SelectDramaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectDramaResp.Merge(dst, src)
}
func (m *SelectDramaResp) XXX_Size() int {
	return xxx_messageInfo_SelectDramaResp.Size(m)
}
func (m *SelectDramaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectDramaResp.DiscardUnknown(m)
}

var xxx_messageInfo_SelectDramaResp proto.InternalMessageInfo

func (m *SelectDramaResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取优质剧场列表-请求
type GetQualityDramaListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetQualityDramaListReq) Reset()         { *m = GetQualityDramaListReq{} }
func (m *GetQualityDramaListReq) String() string { return proto.CompactTextString(m) }
func (*GetQualityDramaListReq) ProtoMessage()    {}
func (*GetQualityDramaListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{31}
}
func (m *GetQualityDramaListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQualityDramaListReq.Unmarshal(m, b)
}
func (m *GetQualityDramaListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQualityDramaListReq.Marshal(b, m, deterministic)
}
func (dst *GetQualityDramaListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQualityDramaListReq.Merge(dst, src)
}
func (m *GetQualityDramaListReq) XXX_Size() int {
	return xxx_messageInfo_GetQualityDramaListReq.Size(m)
}
func (m *GetQualityDramaListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQualityDramaListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQualityDramaListReq proto.InternalMessageInfo

func (m *GetQualityDramaListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// 获取优质剧场列表-响应
type GetQualityDramaListResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*QualityDrama `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	TabId                uint32          `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetQualityDramaListResp) Reset()         { *m = GetQualityDramaListResp{} }
func (m *GetQualityDramaListResp) String() string { return proto.CompactTextString(m) }
func (*GetQualityDramaListResp) ProtoMessage()    {}
func (*GetQualityDramaListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{32}
}
func (m *GetQualityDramaListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQualityDramaListResp.Unmarshal(m, b)
}
func (m *GetQualityDramaListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQualityDramaListResp.Marshal(b, m, deterministic)
}
func (dst *GetQualityDramaListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQualityDramaListResp.Merge(dst, src)
}
func (m *GetQualityDramaListResp) XXX_Size() int {
	return xxx_messageInfo_GetQualityDramaListResp.Size(m)
}
func (m *GetQualityDramaListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQualityDramaListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQualityDramaListResp proto.InternalMessageInfo

func (m *GetQualityDramaListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetQualityDramaListResp) GetList() []*QualityDrama {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetQualityDramaListResp) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

// 获取排练列表-请求
type GetPracticeDramaListReq struct {
	BaseReq              *app.BaseReq     `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PageSize             int32            `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken            string           `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	ChannelPackageId     string           `protobuf:"bytes,4,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	Sex                  DramaRoomUserSex `protobuf:"varint,6,opt,name=sex,proto3,enum=ga.pia.DramaRoomUserSex" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPracticeDramaListReq) Reset()         { *m = GetPracticeDramaListReq{} }
func (m *GetPracticeDramaListReq) String() string { return proto.CompactTextString(m) }
func (*GetPracticeDramaListReq) ProtoMessage()    {}
func (*GetPracticeDramaListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{33}
}
func (m *GetPracticeDramaListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPracticeDramaListReq.Unmarshal(m, b)
}
func (m *GetPracticeDramaListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPracticeDramaListReq.Marshal(b, m, deterministic)
}
func (dst *GetPracticeDramaListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPracticeDramaListReq.Merge(dst, src)
}
func (m *GetPracticeDramaListReq) XXX_Size() int {
	return xxx_messageInfo_GetPracticeDramaListReq.Size(m)
}
func (m *GetPracticeDramaListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPracticeDramaListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPracticeDramaListReq proto.InternalMessageInfo

func (m *GetPracticeDramaListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPracticeDramaListReq) GetPageSize() int32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetPracticeDramaListReq) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *GetPracticeDramaListReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *GetPracticeDramaListReq) GetSex() DramaRoomUserSex {
	if m != nil {
		return m.Sex
	}
	return DramaRoomUserSex_unknown
}

// 获取排练列表-响应
type GetPracticeDramaListResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*PracticeDrama `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	NextPageToken        string           `protobuf:"bytes,3,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	TabId                uint32           `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetPracticeDramaListResp) Reset()         { *m = GetPracticeDramaListResp{} }
func (m *GetPracticeDramaListResp) String() string { return proto.CompactTextString(m) }
func (*GetPracticeDramaListResp) ProtoMessage()    {}
func (*GetPracticeDramaListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{34}
}
func (m *GetPracticeDramaListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPracticeDramaListResp.Unmarshal(m, b)
}
func (m *GetPracticeDramaListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPracticeDramaListResp.Marshal(b, m, deterministic)
}
func (dst *GetPracticeDramaListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPracticeDramaListResp.Merge(dst, src)
}
func (m *GetPracticeDramaListResp) XXX_Size() int {
	return xxx_messageInfo_GetPracticeDramaListResp.Size(m)
}
func (m *GetPracticeDramaListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPracticeDramaListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPracticeDramaListResp proto.InternalMessageInfo

func (m *GetPracticeDramaListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPracticeDramaListResp) GetList() []*PracticeDrama {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetPracticeDramaListResp) GetNextPageToken() string {
	if m != nil {
		return m.NextPageToken
	}
	return ""
}

func (m *GetPracticeDramaListResp) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

// 剧本房间信息
type DramaRoom struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Avatar               string   `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	OwnerAvatar          string   `protobuf:"bytes,4,opt,name=owner_avatar,json=ownerAvatar,proto3" json:"owner_avatar,omitempty"`
	OwnerName            string   `protobuf:"bytes,5,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`
	UserNum              int32    `protobuf:"varint,6,opt,name=user_num,json=userNum,proto3" json:"user_num,omitempty"`
	Heat                 int32    `protobuf:"varint,7,opt,name=heat,proto3" json:"heat,omitempty"`
	Tags                 string   `protobuf:"bytes,8,opt,name=tags,proto3" json:"tags,omitempty"`
	Sex                  int32    `protobuf:"varint,9,opt,name=sex,proto3" json:"sex,omitempty"`
	ChannelBindId        uint32   `protobuf:"varint,10,opt,name=channel_bind_id,json=channelBindId,proto3" json:"channel_bind_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,11,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DramaRoom) Reset()         { *m = DramaRoom{} }
func (m *DramaRoom) String() string { return proto.CompactTextString(m) }
func (*DramaRoom) ProtoMessage()    {}
func (*DramaRoom) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{35}
}
func (m *DramaRoom) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaRoom.Unmarshal(m, b)
}
func (m *DramaRoom) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaRoom.Marshal(b, m, deterministic)
}
func (dst *DramaRoom) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaRoom.Merge(dst, src)
}
func (m *DramaRoom) XXX_Size() int {
	return xxx_messageInfo_DramaRoom.Size(m)
}
func (m *DramaRoom) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaRoom.DiscardUnknown(m)
}

var xxx_messageInfo_DramaRoom proto.InternalMessageInfo

func (m *DramaRoom) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DramaRoom) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *DramaRoom) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DramaRoom) GetOwnerAvatar() string {
	if m != nil {
		return m.OwnerAvatar
	}
	return ""
}

func (m *DramaRoom) GetOwnerName() string {
	if m != nil {
		return m.OwnerName
	}
	return ""
}

func (m *DramaRoom) GetUserNum() int32 {
	if m != nil {
		return m.UserNum
	}
	return 0
}

func (m *DramaRoom) GetHeat() int32 {
	if m != nil {
		return m.Heat
	}
	return 0
}

func (m *DramaRoom) GetTags() string {
	if m != nil {
		return m.Tags
	}
	return ""
}

func (m *DramaRoom) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *DramaRoom) GetChannelBindId() uint32 {
	if m != nil {
		return m.ChannelBindId
	}
	return 0
}

func (m *DramaRoom) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

// 剧本信息
type DramaInfoForAggPage struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type                 string   `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Tags                 []string `protobuf:"bytes,4,rep,name=tags,proto3" json:"tags,omitempty"`
	Summary              string   `protobuf:"bytes,5,opt,name=summary,proto3" json:"summary,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DramaInfoForAggPage) Reset()         { *m = DramaInfoForAggPage{} }
func (m *DramaInfoForAggPage) String() string { return proto.CompactTextString(m) }
func (*DramaInfoForAggPage) ProtoMessage()    {}
func (*DramaInfoForAggPage) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{36}
}
func (m *DramaInfoForAggPage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaInfoForAggPage.Unmarshal(m, b)
}
func (m *DramaInfoForAggPage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaInfoForAggPage.Marshal(b, m, deterministic)
}
func (dst *DramaInfoForAggPage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaInfoForAggPage.Merge(dst, src)
}
func (m *DramaInfoForAggPage) XXX_Size() int {
	return xxx_messageInfo_DramaInfoForAggPage.Size(m)
}
func (m *DramaInfoForAggPage) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaInfoForAggPage.DiscardUnknown(m)
}

var xxx_messageInfo_DramaInfoForAggPage proto.InternalMessageInfo

func (m *DramaInfoForAggPage) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DramaInfoForAggPage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DramaInfoForAggPage) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *DramaInfoForAggPage) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *DramaInfoForAggPage) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

// 剧本房间的基础信息
type DramaRoomBaseInfo struct {
	DramaPhase           PiaPhaseType               `protobuf:"varint,1,opt,name=drama_phase,json=dramaPhase,proto3,enum=ga.pia.PiaPhaseType" json:"drama_phase,omitempty"`
	RoomInfo             *DramaRoom                 `protobuf:"bytes,2,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"`
	DramaInfo            *DramaInfoForAggPage       `protobuf:"bytes,3,opt,name=drama_info,json=dramaInfo,proto3" json:"drama_info,omitempty"`
	TabId                uint32                     `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	PiaStage             DramaRoomBaseInfo_PiaStage `protobuf:"varint,5,opt,name=pia_stage,json=piaStage,proto3,enum=ga.pia.DramaRoomBaseInfo_PiaStage" json:"pia_stage,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *DramaRoomBaseInfo) Reset()         { *m = DramaRoomBaseInfo{} }
func (m *DramaRoomBaseInfo) String() string { return proto.CompactTextString(m) }
func (*DramaRoomBaseInfo) ProtoMessage()    {}
func (*DramaRoomBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{37}
}
func (m *DramaRoomBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaRoomBaseInfo.Unmarshal(m, b)
}
func (m *DramaRoomBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaRoomBaseInfo.Marshal(b, m, deterministic)
}
func (dst *DramaRoomBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaRoomBaseInfo.Merge(dst, src)
}
func (m *DramaRoomBaseInfo) XXX_Size() int {
	return xxx_messageInfo_DramaRoomBaseInfo.Size(m)
}
func (m *DramaRoomBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaRoomBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DramaRoomBaseInfo proto.InternalMessageInfo

func (m *DramaRoomBaseInfo) GetDramaPhase() PiaPhaseType {
	if m != nil {
		return m.DramaPhase
	}
	return PiaPhaseType_PIA_PHASE_CLOSE
}

func (m *DramaRoomBaseInfo) GetRoomInfo() *DramaRoom {
	if m != nil {
		return m.RoomInfo
	}
	return nil
}

func (m *DramaRoomBaseInfo) GetDramaInfo() *DramaInfoForAggPage {
	if m != nil {
		return m.DramaInfo
	}
	return nil
}

func (m *DramaRoomBaseInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *DramaRoomBaseInfo) GetPiaStage() DramaRoomBaseInfo_PiaStage {
	if m != nil {
		return m.PiaStage
	}
	return DramaRoomBaseInfo_PIA_STAGE_NONE
}

// 优质剧场房间信息
type QualityDrama struct {
	BaseInfo             *DramaRoomBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *QualityDrama) Reset()         { *m = QualityDrama{} }
func (m *QualityDrama) String() string { return proto.CompactTextString(m) }
func (*QualityDrama) ProtoMessage()    {}
func (*QualityDrama) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{38}
}
func (m *QualityDrama) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QualityDrama.Unmarshal(m, b)
}
func (m *QualityDrama) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QualityDrama.Marshal(b, m, deterministic)
}
func (dst *QualityDrama) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QualityDrama.Merge(dst, src)
}
func (m *QualityDrama) XXX_Size() int {
	return xxx_messageInfo_QualityDrama.Size(m)
}
func (m *QualityDrama) XXX_DiscardUnknown() {
	xxx_messageInfo_QualityDrama.DiscardUnknown(m)
}

var xxx_messageInfo_QualityDrama proto.InternalMessageInfo

func (m *QualityDrama) GetBaseInfo() *DramaRoomBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

// 排练房间信息
type PracticeDrama struct {
	BaseInfo             *DramaRoomBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PracticeDrama) Reset()         { *m = PracticeDrama{} }
func (m *PracticeDrama) String() string { return proto.CompactTextString(m) }
func (*PracticeDrama) ProtoMessage()    {}
func (*PracticeDrama) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{39}
}
func (m *PracticeDrama) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PracticeDrama.Unmarshal(m, b)
}
func (m *PracticeDrama) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PracticeDrama.Marshal(b, m, deterministic)
}
func (dst *PracticeDrama) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PracticeDrama.Merge(dst, src)
}
func (m *PracticeDrama) XXX_Size() int {
	return xxx_messageInfo_PracticeDrama.Size(m)
}
func (m *PracticeDrama) XXX_DiscardUnknown() {
	xxx_messageInfo_PracticeDrama.DiscardUnknown(m)
}

var xxx_messageInfo_PracticeDrama proto.InternalMessageInfo

func (m *PracticeDrama) GetBaseInfo() *DramaRoomBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

// 已点列表，(广播类型见channel_.proto ChannelMsgType.PIA_DRAMA_ORDER_LIST)
type DramaOrderList struct {
	List                 []*DramaOrderList_UserOrderInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	ChannelId            uint32                          `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Version              int64                           `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *DramaOrderList) Reset()         { *m = DramaOrderList{} }
func (m *DramaOrderList) String() string { return proto.CompactTextString(m) }
func (*DramaOrderList) ProtoMessage()    {}
func (*DramaOrderList) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{40}
}
func (m *DramaOrderList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaOrderList.Unmarshal(m, b)
}
func (m *DramaOrderList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaOrderList.Marshal(b, m, deterministic)
}
func (dst *DramaOrderList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaOrderList.Merge(dst, src)
}
func (m *DramaOrderList) XXX_Size() int {
	return xxx_messageInfo_DramaOrderList.Size(m)
}
func (m *DramaOrderList) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaOrderList.DiscardUnknown(m)
}

var xxx_messageInfo_DramaOrderList proto.InternalMessageInfo

func (m *DramaOrderList) GetList() []*DramaOrderList_UserOrderInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *DramaOrderList) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DramaOrderList) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type DramaOrderList_UserOrderInfo struct {
	DramaSubInfo         *DramaSubInfo        `protobuf:"bytes,1,opt,name=drama_sub_info,json=dramaSubInfo,proto3" json:"drama_sub_info,omitempty"`
	UserId               uint32               `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserName             string               `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	UserAvatar           string               `protobuf:"bytes,4,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar,omitempty"`
	UserSex              uint32               `protobuf:"varint,5,opt,name=user_sex,json=userSex,proto3" json:"user_sex,omitempty"`
	IndexId              int64                `protobuf:"varint,6,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	CreatorInfo          *PiaDramaCreatorInfo `protobuf:"bytes,7,opt,name=creator_info,json=creatorInfo,proto3" json:"creator_info,omitempty"`
	IsCopyDrama          bool                 `protobuf:"varint,8,opt,name=is_copy_drama,json=isCopyDrama,proto3" json:"is_copy_drama,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *DramaOrderList_UserOrderInfo) Reset()         { *m = DramaOrderList_UserOrderInfo{} }
func (m *DramaOrderList_UserOrderInfo) String() string { return proto.CompactTextString(m) }
func (*DramaOrderList_UserOrderInfo) ProtoMessage()    {}
func (*DramaOrderList_UserOrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{40, 0}
}
func (m *DramaOrderList_UserOrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaOrderList_UserOrderInfo.Unmarshal(m, b)
}
func (m *DramaOrderList_UserOrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaOrderList_UserOrderInfo.Marshal(b, m, deterministic)
}
func (dst *DramaOrderList_UserOrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaOrderList_UserOrderInfo.Merge(dst, src)
}
func (m *DramaOrderList_UserOrderInfo) XXX_Size() int {
	return xxx_messageInfo_DramaOrderList_UserOrderInfo.Size(m)
}
func (m *DramaOrderList_UserOrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaOrderList_UserOrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DramaOrderList_UserOrderInfo proto.InternalMessageInfo

func (m *DramaOrderList_UserOrderInfo) GetDramaSubInfo() *DramaSubInfo {
	if m != nil {
		return m.DramaSubInfo
	}
	return nil
}

func (m *DramaOrderList_UserOrderInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *DramaOrderList_UserOrderInfo) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *DramaOrderList_UserOrderInfo) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *DramaOrderList_UserOrderInfo) GetUserSex() uint32 {
	if m != nil {
		return m.UserSex
	}
	return 0
}

func (m *DramaOrderList_UserOrderInfo) GetIndexId() int64 {
	if m != nil {
		return m.IndexId
	}
	return 0
}

func (m *DramaOrderList_UserOrderInfo) GetCreatorInfo() *PiaDramaCreatorInfo {
	if m != nil {
		return m.CreatorInfo
	}
	return nil
}

func (m *DramaOrderList_UserOrderInfo) GetIsCopyDrama() bool {
	if m != nil {
		return m.IsCopyDrama
	}
	return false
}

// 点本请求
type OrderDramaReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	DramaId              uint32       `protobuf:"varint,2,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OrderDramaReq) Reset()         { *m = OrderDramaReq{} }
func (m *OrderDramaReq) String() string { return proto.CompactTextString(m) }
func (*OrderDramaReq) ProtoMessage()    {}
func (*OrderDramaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{41}
}
func (m *OrderDramaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderDramaReq.Unmarshal(m, b)
}
func (m *OrderDramaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderDramaReq.Marshal(b, m, deterministic)
}
func (dst *OrderDramaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderDramaReq.Merge(dst, src)
}
func (m *OrderDramaReq) XXX_Size() int {
	return xxx_messageInfo_OrderDramaReq.Size(m)
}
func (m *OrderDramaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderDramaReq.DiscardUnknown(m)
}

var xxx_messageInfo_OrderDramaReq proto.InternalMessageInfo

func (m *OrderDramaReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OrderDramaReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

func (m *OrderDramaReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 点本响应
type OrderDramaResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OrderList            *DramaOrderList `protobuf:"bytes,2,opt,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *OrderDramaResp) Reset()         { *m = OrderDramaResp{} }
func (m *OrderDramaResp) String() string { return proto.CompactTextString(m) }
func (*OrderDramaResp) ProtoMessage()    {}
func (*OrderDramaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{42}
}
func (m *OrderDramaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderDramaResp.Unmarshal(m, b)
}
func (m *OrderDramaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderDramaResp.Marshal(b, m, deterministic)
}
func (dst *OrderDramaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderDramaResp.Merge(dst, src)
}
func (m *OrderDramaResp) XXX_Size() int {
	return xxx_messageInfo_OrderDramaResp.Size(m)
}
func (m *OrderDramaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderDramaResp.DiscardUnknown(m)
}

var xxx_messageInfo_OrderDramaResp proto.InternalMessageInfo

func (m *OrderDramaResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OrderDramaResp) GetOrderList() *DramaOrderList {
	if m != nil {
		return m.OrderList
	}
	return nil
}

// 获取已点列表请求
type GetOrderDramaListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOrderDramaListReq) Reset()         { *m = GetOrderDramaListReq{} }
func (m *GetOrderDramaListReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderDramaListReq) ProtoMessage()    {}
func (*GetOrderDramaListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{43}
}
func (m *GetOrderDramaListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderDramaListReq.Unmarshal(m, b)
}
func (m *GetOrderDramaListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderDramaListReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderDramaListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderDramaListReq.Merge(dst, src)
}
func (m *GetOrderDramaListReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderDramaListReq.Size(m)
}
func (m *GetOrderDramaListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderDramaListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderDramaListReq proto.InternalMessageInfo

func (m *GetOrderDramaListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetOrderDramaListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 获取已点列表响应
type GetOrderDramaListResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OrderList            *DramaOrderList `protobuf:"bytes,2,opt,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetOrderDramaListResp) Reset()         { *m = GetOrderDramaListResp{} }
func (m *GetOrderDramaListResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderDramaListResp) ProtoMessage()    {}
func (*GetOrderDramaListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{44}
}
func (m *GetOrderDramaListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderDramaListResp.Unmarshal(m, b)
}
func (m *GetOrderDramaListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderDramaListResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderDramaListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderDramaListResp.Merge(dst, src)
}
func (m *GetOrderDramaListResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderDramaListResp.Size(m)
}
func (m *GetOrderDramaListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderDramaListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderDramaListResp proto.InternalMessageInfo

func (m *GetOrderDramaListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetOrderDramaListResp) GetOrderList() *DramaOrderList {
	if m != nil {
		return m.OrderList
	}
	return nil
}

// 删除已点记录请求
type DeleteOrderDramaReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	IndexIdList          []int64      `protobuf:"varint,2,rep,packed,name=index_id_list,json=indexIdList,proto3" json:"index_id_list,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DeleteOrderDramaReq) Reset()         { *m = DeleteOrderDramaReq{} }
func (m *DeleteOrderDramaReq) String() string { return proto.CompactTextString(m) }
func (*DeleteOrderDramaReq) ProtoMessage()    {}
func (*DeleteOrderDramaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{45}
}
func (m *DeleteOrderDramaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteOrderDramaReq.Unmarshal(m, b)
}
func (m *DeleteOrderDramaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteOrderDramaReq.Marshal(b, m, deterministic)
}
func (dst *DeleteOrderDramaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteOrderDramaReq.Merge(dst, src)
}
func (m *DeleteOrderDramaReq) XXX_Size() int {
	return xxx_messageInfo_DeleteOrderDramaReq.Size(m)
}
func (m *DeleteOrderDramaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteOrderDramaReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteOrderDramaReq proto.InternalMessageInfo

func (m *DeleteOrderDramaReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DeleteOrderDramaReq) GetIndexIdList() []int64 {
	if m != nil {
		return m.IndexIdList
	}
	return nil
}

func (m *DeleteOrderDramaReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 删除已点记录响应
type DeleteOrderDramaResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OrderList            *DramaOrderList `protobuf:"bytes,2,opt,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *DeleteOrderDramaResp) Reset()         { *m = DeleteOrderDramaResp{} }
func (m *DeleteOrderDramaResp) String() string { return proto.CompactTextString(m) }
func (*DeleteOrderDramaResp) ProtoMessage()    {}
func (*DeleteOrderDramaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{46}
}
func (m *DeleteOrderDramaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteOrderDramaResp.Unmarshal(m, b)
}
func (m *DeleteOrderDramaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteOrderDramaResp.Marshal(b, m, deterministic)
}
func (dst *DeleteOrderDramaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteOrderDramaResp.Merge(dst, src)
}
func (m *DeleteOrderDramaResp) XXX_Size() int {
	return xxx_messageInfo_DeleteOrderDramaResp.Size(m)
}
func (m *DeleteOrderDramaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteOrderDramaResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteOrderDramaResp proto.InternalMessageInfo

func (m *DeleteOrderDramaResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *DeleteOrderDramaResp) GetOrderList() *DramaOrderList {
	if m != nil {
		return m.OrderList
	}
	return nil
}

// 麦位绑定的角色列表，(广播类型见channel_.proto ChannelMsgType.PIA_MIC_ROLE_MAP)
type MicRoleMap struct {
	Map                  map[uint32]*MicRoleMap_RoleInfoList `protobuf:"bytes,1,rep,name=map,proto3" json:"map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Version              int64                               `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	Skip                 bool                                `protobuf:"varint,3,opt,name=skip,proto3" json:"skip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *MicRoleMap) Reset()         { *m = MicRoleMap{} }
func (m *MicRoleMap) String() string { return proto.CompactTextString(m) }
func (*MicRoleMap) ProtoMessage()    {}
func (*MicRoleMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{47}
}
func (m *MicRoleMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicRoleMap.Unmarshal(m, b)
}
func (m *MicRoleMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicRoleMap.Marshal(b, m, deterministic)
}
func (dst *MicRoleMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicRoleMap.Merge(dst, src)
}
func (m *MicRoleMap) XXX_Size() int {
	return xxx_messageInfo_MicRoleMap.Size(m)
}
func (m *MicRoleMap) XXX_DiscardUnknown() {
	xxx_messageInfo_MicRoleMap.DiscardUnknown(m)
}

var xxx_messageInfo_MicRoleMap proto.InternalMessageInfo

func (m *MicRoleMap) GetMap() map[uint32]*MicRoleMap_RoleInfoList {
	if m != nil {
		return m.Map
	}
	return nil
}

func (m *MicRoleMap) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *MicRoleMap) GetSkip() bool {
	if m != nil {
		return m.Skip
	}
	return false
}

type MicRoleMap_RoleInfoList struct {
	Id                   []string `protobuf:"bytes,1,rep,name=id,proto3" json:"id,omitempty"`
	JoinTime             int64    `protobuf:"varint,2,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicRoleMap_RoleInfoList) Reset()         { *m = MicRoleMap_RoleInfoList{} }
func (m *MicRoleMap_RoleInfoList) String() string { return proto.CompactTextString(m) }
func (*MicRoleMap_RoleInfoList) ProtoMessage()    {}
func (*MicRoleMap_RoleInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{47, 0}
}
func (m *MicRoleMap_RoleInfoList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicRoleMap_RoleInfoList.Unmarshal(m, b)
}
func (m *MicRoleMap_RoleInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicRoleMap_RoleInfoList.Marshal(b, m, deterministic)
}
func (dst *MicRoleMap_RoleInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicRoleMap_RoleInfoList.Merge(dst, src)
}
func (m *MicRoleMap_RoleInfoList) XXX_Size() int {
	return xxx_messageInfo_MicRoleMap_RoleInfoList.Size(m)
}
func (m *MicRoleMap_RoleInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_MicRoleMap_RoleInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_MicRoleMap_RoleInfoList proto.InternalMessageInfo

func (m *MicRoleMap_RoleInfoList) GetId() []string {
	if m != nil {
		return m.Id
	}
	return nil
}

func (m *MicRoleMap_RoleInfoList) GetJoinTime() int64 {
	if m != nil {
		return m.JoinTime
	}
	return 0
}

// 选择角色请求
type PiaSelectRoleReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	DramaId              uint32       `protobuf:"varint,2,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoleId               string       `protobuf:"bytes,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	MicNumber            uint32       `protobuf:"varint,5,opt,name=mic_number,json=micNumber,proto3" json:"mic_number,omitempty"`
	RoundId              int64        `protobuf:"varint,6,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaSelectRoleReq) Reset()         { *m = PiaSelectRoleReq{} }
func (m *PiaSelectRoleReq) String() string { return proto.CompactTextString(m) }
func (*PiaSelectRoleReq) ProtoMessage()    {}
func (*PiaSelectRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{48}
}
func (m *PiaSelectRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaSelectRoleReq.Unmarshal(m, b)
}
func (m *PiaSelectRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaSelectRoleReq.Marshal(b, m, deterministic)
}
func (dst *PiaSelectRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaSelectRoleReq.Merge(dst, src)
}
func (m *PiaSelectRoleReq) XXX_Size() int {
	return xxx_messageInfo_PiaSelectRoleReq.Size(m)
}
func (m *PiaSelectRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaSelectRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaSelectRoleReq proto.InternalMessageInfo

func (m *PiaSelectRoleReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaSelectRoleReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

func (m *PiaSelectRoleReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaSelectRoleReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *PiaSelectRoleReq) GetMicNumber() uint32 {
	if m != nil {
		return m.MicNumber
	}
	return 0
}

func (m *PiaSelectRoleReq) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// 选择角色响应
type PiaSelectRoleResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MicRoleMap           *MicRoleMap   `protobuf:"bytes,2,opt,name=mic_role_map,json=micRoleMap,proto3" json:"mic_role_map,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaSelectRoleResp) Reset()         { *m = PiaSelectRoleResp{} }
func (m *PiaSelectRoleResp) String() string { return proto.CompactTextString(m) }
func (*PiaSelectRoleResp) ProtoMessage()    {}
func (*PiaSelectRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{49}
}
func (m *PiaSelectRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaSelectRoleResp.Unmarshal(m, b)
}
func (m *PiaSelectRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaSelectRoleResp.Marshal(b, m, deterministic)
}
func (dst *PiaSelectRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaSelectRoleResp.Merge(dst, src)
}
func (m *PiaSelectRoleResp) XXX_Size() int {
	return xxx_messageInfo_PiaSelectRoleResp.Size(m)
}
func (m *PiaSelectRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaSelectRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaSelectRoleResp proto.InternalMessageInfo

func (m *PiaSelectRoleResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaSelectRoleResp) GetMicRoleMap() *MicRoleMap {
	if m != nil {
		return m.MicRoleMap
	}
	return nil
}

// 角色取消选择请求
type PiaCancelSelectRoleReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	DramaId              uint32       `protobuf:"varint,2,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoleId               string       `protobuf:"bytes,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	MicNumber            uint32       `protobuf:"varint,5,opt,name=mic_number,json=micNumber,proto3" json:"mic_number,omitempty"`
	RoundId              int64        `protobuf:"varint,6,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaCancelSelectRoleReq) Reset()         { *m = PiaCancelSelectRoleReq{} }
func (m *PiaCancelSelectRoleReq) String() string { return proto.CompactTextString(m) }
func (*PiaCancelSelectRoleReq) ProtoMessage()    {}
func (*PiaCancelSelectRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{50}
}
func (m *PiaCancelSelectRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCancelSelectRoleReq.Unmarshal(m, b)
}
func (m *PiaCancelSelectRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCancelSelectRoleReq.Marshal(b, m, deterministic)
}
func (dst *PiaCancelSelectRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCancelSelectRoleReq.Merge(dst, src)
}
func (m *PiaCancelSelectRoleReq) XXX_Size() int {
	return xxx_messageInfo_PiaCancelSelectRoleReq.Size(m)
}
func (m *PiaCancelSelectRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCancelSelectRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCancelSelectRoleReq proto.InternalMessageInfo

func (m *PiaCancelSelectRoleReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaCancelSelectRoleReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

func (m *PiaCancelSelectRoleReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaCancelSelectRoleReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *PiaCancelSelectRoleReq) GetMicNumber() uint32 {
	if m != nil {
		return m.MicNumber
	}
	return 0
}

func (m *PiaCancelSelectRoleReq) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// 角色取消选择响应
type PiaCancelSelectRoleResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MicRoleMap           *MicRoleMap   `protobuf:"bytes,2,opt,name=mic_role_map,json=micRoleMap,proto3" json:"mic_role_map,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaCancelSelectRoleResp) Reset()         { *m = PiaCancelSelectRoleResp{} }
func (m *PiaCancelSelectRoleResp) String() string { return proto.CompactTextString(m) }
func (*PiaCancelSelectRoleResp) ProtoMessage()    {}
func (*PiaCancelSelectRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{51}
}
func (m *PiaCancelSelectRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCancelSelectRoleResp.Unmarshal(m, b)
}
func (m *PiaCancelSelectRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCancelSelectRoleResp.Marshal(b, m, deterministic)
}
func (dst *PiaCancelSelectRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCancelSelectRoleResp.Merge(dst, src)
}
func (m *PiaCancelSelectRoleResp) XXX_Size() int {
	return xxx_messageInfo_PiaCancelSelectRoleResp.Size(m)
}
func (m *PiaCancelSelectRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCancelSelectRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCancelSelectRoleResp proto.InternalMessageInfo

func (m *PiaCancelSelectRoleResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaCancelSelectRoleResp) GetMicRoleMap() *MicRoleMap {
	if m != nil {
		return m.MicRoleMap
	}
	return nil
}

type ChannelDramaProgress struct {
	CurIndex             uint32   `protobuf:"varint,1,opt,name=cur_index,json=curIndex,proto3" json:"cur_index,omitempty"`
	TimeOffset           int64    `protobuf:"varint,2,opt,name=time_offset,json=timeOffset,proto3" json:"time_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelDramaProgress) Reset()         { *m = ChannelDramaProgress{} }
func (m *ChannelDramaProgress) String() string { return proto.CompactTextString(m) }
func (*ChannelDramaProgress) ProtoMessage()    {}
func (*ChannelDramaProgress) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{52}
}
func (m *ChannelDramaProgress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelDramaProgress.Unmarshal(m, b)
}
func (m *ChannelDramaProgress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelDramaProgress.Marshal(b, m, deterministic)
}
func (dst *ChannelDramaProgress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelDramaProgress.Merge(dst, src)
}
func (m *ChannelDramaProgress) XXX_Size() int {
	return xxx_messageInfo_ChannelDramaProgress.Size(m)
}
func (m *ChannelDramaProgress) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelDramaProgress.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelDramaProgress proto.InternalMessageInfo

func (m *ChannelDramaProgress) GetCurIndex() uint32 {
	if m != nil {
		return m.CurIndex
	}
	return 0
}

func (m *ChannelDramaProgress) GetTimeOffset() int64 {
	if m != nil {
		return m.TimeOffset
	}
	return 0
}

// 房间剧本状态，(广播类型见channel_.proto ChannelMsgType.PIA_CHANNEL_DRAMA_STATUS)
type ChannelDramaStatus struct {
	DramaPhase           DramaPhase                  `protobuf:"varint,1,opt,name=drama_phase,json=dramaPhase,proto3,enum=ga.pia.DramaPhase" json:"drama_phase,omitempty"`
	ChannelId            uint32                      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DramaInfo            *DramaV2                    `protobuf:"bytes,3,opt,name=drama_info,json=dramaInfo,proto3" json:"drama_info,omitempty"`
	Version              int64                       `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	Progress             *ChannelDramaProgress       `protobuf:"bytes,5,opt,name=progress,proto3" json:"progress,omitempty"`
	StartTime            int64                       `protobuf:"varint,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	RoundId              int64                       `protobuf:"varint,7,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	Skip                 bool                        `protobuf:"varint,8,opt,name=skip,proto3" json:"skip,omitempty"`
	PlayingType          *PiaChannelDramaPlayingType `protobuf:"bytes,9,opt,name=playing_type,json=playingType,proto3" json:"playing_type,omitempty"`
	CanChangePlayingType bool                        `protobuf:"varint,10,opt,name=can_change_playing_type,json=canChangePlayingType,proto3" json:"can_change_playing_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *ChannelDramaStatus) Reset()         { *m = ChannelDramaStatus{} }
func (m *ChannelDramaStatus) String() string { return proto.CompactTextString(m) }
func (*ChannelDramaStatus) ProtoMessage()    {}
func (*ChannelDramaStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{53}
}
func (m *ChannelDramaStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelDramaStatus.Unmarshal(m, b)
}
func (m *ChannelDramaStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelDramaStatus.Marshal(b, m, deterministic)
}
func (dst *ChannelDramaStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelDramaStatus.Merge(dst, src)
}
func (m *ChannelDramaStatus) XXX_Size() int {
	return xxx_messageInfo_ChannelDramaStatus.Size(m)
}
func (m *ChannelDramaStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelDramaStatus.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelDramaStatus proto.InternalMessageInfo

func (m *ChannelDramaStatus) GetDramaPhase() DramaPhase {
	if m != nil {
		return m.DramaPhase
	}
	return DramaPhase_DRAMA_PHASE_UNSPECIFIED
}

func (m *ChannelDramaStatus) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelDramaStatus) GetDramaInfo() *DramaV2 {
	if m != nil {
		return m.DramaInfo
	}
	return nil
}

func (m *ChannelDramaStatus) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *ChannelDramaStatus) GetProgress() *ChannelDramaProgress {
	if m != nil {
		return m.Progress
	}
	return nil
}

func (m *ChannelDramaStatus) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ChannelDramaStatus) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *ChannelDramaStatus) GetSkip() bool {
	if m != nil {
		return m.Skip
	}
	return false
}

func (m *ChannelDramaStatus) GetPlayingType() *PiaChannelDramaPlayingType {
	if m != nil {
		return m.PlayingType
	}
	return nil
}

func (m *ChannelDramaStatus) GetCanChangePlayingType() bool {
	if m != nil {
		return m.CanChangePlayingType
	}
	return false
}

// 选本操作请求
type SelectDramaV2Req struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	DramaId              uint32       `protobuf:"varint,2,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IndexId              int64        `protobuf:"varint,4,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SelectDramaV2Req) Reset()         { *m = SelectDramaV2Req{} }
func (m *SelectDramaV2Req) String() string { return proto.CompactTextString(m) }
func (*SelectDramaV2Req) ProtoMessage()    {}
func (*SelectDramaV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{54}
}
func (m *SelectDramaV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectDramaV2Req.Unmarshal(m, b)
}
func (m *SelectDramaV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectDramaV2Req.Marshal(b, m, deterministic)
}
func (dst *SelectDramaV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectDramaV2Req.Merge(dst, src)
}
func (m *SelectDramaV2Req) XXX_Size() int {
	return xxx_messageInfo_SelectDramaV2Req.Size(m)
}
func (m *SelectDramaV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectDramaV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_SelectDramaV2Req proto.InternalMessageInfo

func (m *SelectDramaV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SelectDramaV2Req) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

func (m *SelectDramaV2Req) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SelectDramaV2Req) GetIndexId() int64 {
	if m != nil {
		return m.IndexId
	}
	return 0
}

// 选本操作响应
type SelectDramaV2Resp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaStatus          *ChannelDramaStatus `protobuf:"bytes,2,opt,name=drama_status,json=dramaStatus,proto3" json:"drama_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SelectDramaV2Resp) Reset()         { *m = SelectDramaV2Resp{} }
func (m *SelectDramaV2Resp) String() string { return proto.CompactTextString(m) }
func (*SelectDramaV2Resp) ProtoMessage()    {}
func (*SelectDramaV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{55}
}
func (m *SelectDramaV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectDramaV2Resp.Unmarshal(m, b)
}
func (m *SelectDramaV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectDramaV2Resp.Marshal(b, m, deterministic)
}
func (dst *SelectDramaV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectDramaV2Resp.Merge(dst, src)
}
func (m *SelectDramaV2Resp) XXX_Size() int {
	return xxx_messageInfo_SelectDramaV2Resp.Size(m)
}
func (m *SelectDramaV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectDramaV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_SelectDramaV2Resp proto.InternalMessageInfo

func (m *SelectDramaV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SelectDramaV2Resp) GetDramaStatus() *ChannelDramaStatus {
	if m != nil {
		return m.DramaStatus
	}
	return nil
}

// 走本操作请求
type PiaOperateDramaReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32             `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OperationType        DramaOperationType `protobuf:"varint,3,opt,name=operation_type,json=operationType,proto3,enum=ga.pia.DramaOperationType" json:"operation_type,omitempty"`
	DramaSectionIndex    uint32             `protobuf:"varint,4,opt,name=drama_section_index,json=dramaSectionIndex,proto3" json:"drama_section_index,omitempty"`
	DelayTime            uint32             `protobuf:"varint,5,opt,name=delay_time,json=delayTime,proto3" json:"delay_time,omitempty"`
	RoundId              int64              `protobuf:"varint,6,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PiaOperateDramaReq) Reset()         { *m = PiaOperateDramaReq{} }
func (m *PiaOperateDramaReq) String() string { return proto.CompactTextString(m) }
func (*PiaOperateDramaReq) ProtoMessage()    {}
func (*PiaOperateDramaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{56}
}
func (m *PiaOperateDramaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaOperateDramaReq.Unmarshal(m, b)
}
func (m *PiaOperateDramaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaOperateDramaReq.Marshal(b, m, deterministic)
}
func (dst *PiaOperateDramaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaOperateDramaReq.Merge(dst, src)
}
func (m *PiaOperateDramaReq) XXX_Size() int {
	return xxx_messageInfo_PiaOperateDramaReq.Size(m)
}
func (m *PiaOperateDramaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaOperateDramaReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaOperateDramaReq proto.InternalMessageInfo

func (m *PiaOperateDramaReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaOperateDramaReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaOperateDramaReq) GetOperationType() DramaOperationType {
	if m != nil {
		return m.OperationType
	}
	return DramaOperationType_DRAMA_OPERATION_TYPE_UNSPECIFIED
}

func (m *PiaOperateDramaReq) GetDramaSectionIndex() uint32 {
	if m != nil {
		return m.DramaSectionIndex
	}
	return 0
}

func (m *PiaOperateDramaReq) GetDelayTime() uint32 {
	if m != nil {
		return m.DelayTime
	}
	return 0
}

func (m *PiaOperateDramaReq) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// 走本操作响应
type PiaOperateDramaResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaStatus          *ChannelDramaStatus `protobuf:"bytes,2,opt,name=drama_status,json=dramaStatus,proto3" json:"drama_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PiaOperateDramaResp) Reset()         { *m = PiaOperateDramaResp{} }
func (m *PiaOperateDramaResp) String() string { return proto.CompactTextString(m) }
func (*PiaOperateDramaResp) ProtoMessage()    {}
func (*PiaOperateDramaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{57}
}
func (m *PiaOperateDramaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaOperateDramaResp.Unmarshal(m, b)
}
func (m *PiaOperateDramaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaOperateDramaResp.Marshal(b, m, deterministic)
}
func (dst *PiaOperateDramaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaOperateDramaResp.Merge(dst, src)
}
func (m *PiaOperateDramaResp) XXX_Size() int {
	return xxx_messageInfo_PiaOperateDramaResp.Size(m)
}
func (m *PiaOperateDramaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaOperateDramaResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaOperateDramaResp proto.InternalMessageInfo

func (m *PiaOperateDramaResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaOperateDramaResp) GetDramaStatus() *ChannelDramaStatus {
	if m != nil {
		return m.DramaStatus
	}
	return nil
}

// 当前房间走本详细信息
type ChannelDramaInfoDetail struct {
	ChannelId            uint32              `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DramaStatus          *ChannelDramaStatus `protobuf:"bytes,2,opt,name=drama_status,json=dramaStatus,proto3" json:"drama_status,omitempty"`
	DramaBgmStatus       *DramaBgmStatus     `protobuf:"bytes,3,opt,name=drama_bgm_status,json=dramaBgmStatus,proto3" json:"drama_bgm_status,omitempty"`
	MicRoleMap           *MicRoleMap         `protobuf:"bytes,4,opt,name=mic_role_map,json=micRoleMap,proto3" json:"mic_role_map,omitempty"`
	DramaBgmVolStatus    *DramaBgmVolStatus  `protobuf:"bytes,5,opt,name=drama_bgm_vol_status,json=dramaBgmVolStatus,proto3" json:"drama_bgm_vol_status,omitempty"`
	OriginDramaInfo      *DramaV2            `protobuf:"bytes,6,opt,name=origin_drama_info,json=originDramaInfo,proto3" json:"origin_drama_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ChannelDramaInfoDetail) Reset()         { *m = ChannelDramaInfoDetail{} }
func (m *ChannelDramaInfoDetail) String() string { return proto.CompactTextString(m) }
func (*ChannelDramaInfoDetail) ProtoMessage()    {}
func (*ChannelDramaInfoDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{58}
}
func (m *ChannelDramaInfoDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelDramaInfoDetail.Unmarshal(m, b)
}
func (m *ChannelDramaInfoDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelDramaInfoDetail.Marshal(b, m, deterministic)
}
func (dst *ChannelDramaInfoDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelDramaInfoDetail.Merge(dst, src)
}
func (m *ChannelDramaInfoDetail) XXX_Size() int {
	return xxx_messageInfo_ChannelDramaInfoDetail.Size(m)
}
func (m *ChannelDramaInfoDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelDramaInfoDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelDramaInfoDetail proto.InternalMessageInfo

func (m *ChannelDramaInfoDetail) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelDramaInfoDetail) GetDramaStatus() *ChannelDramaStatus {
	if m != nil {
		return m.DramaStatus
	}
	return nil
}

func (m *ChannelDramaInfoDetail) GetDramaBgmStatus() *DramaBgmStatus {
	if m != nil {
		return m.DramaBgmStatus
	}
	return nil
}

func (m *ChannelDramaInfoDetail) GetMicRoleMap() *MicRoleMap {
	if m != nil {
		return m.MicRoleMap
	}
	return nil
}

func (m *ChannelDramaInfoDetail) GetDramaBgmVolStatus() *DramaBgmVolStatus {
	if m != nil {
		return m.DramaBgmVolStatus
	}
	return nil
}

func (m *ChannelDramaInfoDetail) GetOriginDramaInfo() *DramaV2 {
	if m != nil {
		return m.OriginDramaInfo
	}
	return nil
}

// 获取当前房间走本详情请求
type PiaGetDramaStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaGetDramaStatusReq) Reset()         { *m = PiaGetDramaStatusReq{} }
func (m *PiaGetDramaStatusReq) String() string { return proto.CompactTextString(m) }
func (*PiaGetDramaStatusReq) ProtoMessage()    {}
func (*PiaGetDramaStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{59}
}
func (m *PiaGetDramaStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetDramaStatusReq.Unmarshal(m, b)
}
func (m *PiaGetDramaStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetDramaStatusReq.Marshal(b, m, deterministic)
}
func (dst *PiaGetDramaStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetDramaStatusReq.Merge(dst, src)
}
func (m *PiaGetDramaStatusReq) XXX_Size() int {
	return xxx_messageInfo_PiaGetDramaStatusReq.Size(m)
}
func (m *PiaGetDramaStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetDramaStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetDramaStatusReq proto.InternalMessageInfo

func (m *PiaGetDramaStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaGetDramaStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 获取当前房间走本详情响应
type PiaGetDramaStatusResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaInfoDetail      *ChannelDramaInfoDetail `protobuf:"bytes,2,opt,name=drama_info_detail,json=dramaInfoDetail,proto3" json:"drama_info_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *PiaGetDramaStatusResp) Reset()         { *m = PiaGetDramaStatusResp{} }
func (m *PiaGetDramaStatusResp) String() string { return proto.CompactTextString(m) }
func (*PiaGetDramaStatusResp) ProtoMessage()    {}
func (*PiaGetDramaStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{60}
}
func (m *PiaGetDramaStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetDramaStatusResp.Unmarshal(m, b)
}
func (m *PiaGetDramaStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetDramaStatusResp.Marshal(b, m, deterministic)
}
func (dst *PiaGetDramaStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetDramaStatusResp.Merge(dst, src)
}
func (m *PiaGetDramaStatusResp) XXX_Size() int {
	return xxx_messageInfo_PiaGetDramaStatusResp.Size(m)
}
func (m *PiaGetDramaStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetDramaStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetDramaStatusResp proto.InternalMessageInfo

func (m *PiaGetDramaStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaGetDramaStatusResp) GetDramaInfoDetail() *ChannelDramaInfoDetail {
	if m != nil {
		return m.DramaInfoDetail
	}
	return nil
}

// 获取剧本副本ID请求
type PiaGetDramaCopyIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OriginDramaId        uint32       `protobuf:"varint,2,opt,name=origin_drama_id,json=originDramaId,proto3" json:"origin_drama_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaGetDramaCopyIdReq) Reset()         { *m = PiaGetDramaCopyIdReq{} }
func (m *PiaGetDramaCopyIdReq) String() string { return proto.CompactTextString(m) }
func (*PiaGetDramaCopyIdReq) ProtoMessage()    {}
func (*PiaGetDramaCopyIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{61}
}
func (m *PiaGetDramaCopyIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetDramaCopyIdReq.Unmarshal(m, b)
}
func (m *PiaGetDramaCopyIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetDramaCopyIdReq.Marshal(b, m, deterministic)
}
func (dst *PiaGetDramaCopyIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetDramaCopyIdReq.Merge(dst, src)
}
func (m *PiaGetDramaCopyIdReq) XXX_Size() int {
	return xxx_messageInfo_PiaGetDramaCopyIdReq.Size(m)
}
func (m *PiaGetDramaCopyIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetDramaCopyIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetDramaCopyIdReq proto.InternalMessageInfo

func (m *PiaGetDramaCopyIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaGetDramaCopyIdReq) GetOriginDramaId() uint32 {
	if m != nil {
		return m.OriginDramaId
	}
	return 0
}

// 获取剧本副本ID响应
type PiaGetDramaCopyIdResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CopyDramaId          uint32        `protobuf:"varint,2,opt,name=copy_drama_id,json=copyDramaId,proto3" json:"copy_drama_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaGetDramaCopyIdResp) Reset()         { *m = PiaGetDramaCopyIdResp{} }
func (m *PiaGetDramaCopyIdResp) String() string { return proto.CompactTextString(m) }
func (*PiaGetDramaCopyIdResp) ProtoMessage()    {}
func (*PiaGetDramaCopyIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{62}
}
func (m *PiaGetDramaCopyIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetDramaCopyIdResp.Unmarshal(m, b)
}
func (m *PiaGetDramaCopyIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetDramaCopyIdResp.Marshal(b, m, deterministic)
}
func (dst *PiaGetDramaCopyIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetDramaCopyIdResp.Merge(dst, src)
}
func (m *PiaGetDramaCopyIdResp) XXX_Size() int {
	return xxx_messageInfo_PiaGetDramaCopyIdResp.Size(m)
}
func (m *PiaGetDramaCopyIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetDramaCopyIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetDramaCopyIdResp proto.InternalMessageInfo

func (m *PiaGetDramaCopyIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaGetDramaCopyIdResp) GetCopyDramaId() uint32 {
	if m != nil {
		return m.CopyDramaId
	}
	return 0
}

// 生成剧本副本请求，如果剧本还未结束，则用当前时间结束剧本。
type PiaCreateDramaCopyReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64        `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaCreateDramaCopyReq) Reset()         { *m = PiaCreateDramaCopyReq{} }
func (m *PiaCreateDramaCopyReq) String() string { return proto.CompactTextString(m) }
func (*PiaCreateDramaCopyReq) ProtoMessage()    {}
func (*PiaCreateDramaCopyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{63}
}
func (m *PiaCreateDramaCopyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCreateDramaCopyReq.Unmarshal(m, b)
}
func (m *PiaCreateDramaCopyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCreateDramaCopyReq.Marshal(b, m, deterministic)
}
func (dst *PiaCreateDramaCopyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCreateDramaCopyReq.Merge(dst, src)
}
func (m *PiaCreateDramaCopyReq) XXX_Size() int {
	return xxx_messageInfo_PiaCreateDramaCopyReq.Size(m)
}
func (m *PiaCreateDramaCopyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCreateDramaCopyReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCreateDramaCopyReq proto.InternalMessageInfo

func (m *PiaCreateDramaCopyReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaCreateDramaCopyReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaCreateDramaCopyReq) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// 生成剧本副本响应
type PiaCreateDramaCopyResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CopyDramaId          uint32        `protobuf:"varint,2,opt,name=copy_drama_id,json=copyDramaId,proto3" json:"copy_drama_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaCreateDramaCopyResp) Reset()         { *m = PiaCreateDramaCopyResp{} }
func (m *PiaCreateDramaCopyResp) String() string { return proto.CompactTextString(m) }
func (*PiaCreateDramaCopyResp) ProtoMessage()    {}
func (*PiaCreateDramaCopyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{64}
}
func (m *PiaCreateDramaCopyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCreateDramaCopyResp.Unmarshal(m, b)
}
func (m *PiaCreateDramaCopyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCreateDramaCopyResp.Marshal(b, m, deterministic)
}
func (dst *PiaCreateDramaCopyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCreateDramaCopyResp.Merge(dst, src)
}
func (m *PiaCreateDramaCopyResp) XXX_Size() int {
	return xxx_messageInfo_PiaCreateDramaCopyResp.Size(m)
}
func (m *PiaCreateDramaCopyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCreateDramaCopyResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCreateDramaCopyResp proto.InternalMessageInfo

func (m *PiaCreateDramaCopyResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaCreateDramaCopyResp) GetCopyDramaId() uint32 {
	if m != nil {
		return m.CopyDramaId
	}
	return 0
}

// 剧本BGM状态，(广播类型见channel_.proto ChannelMsgType.PIA_CHANNEL_DRAMA_BGM_STATUS)
type DramaBgmStatus struct {
	BgmId                string        `protobuf:"bytes,1,opt,name=bgm_id,json=bgmId,proto3" json:"bgm_id,omitempty"`
	BgmPhase             DramaBGMPhase `protobuf:"varint,2,opt,name=bgm_phase,json=bgmPhase,proto3,enum=ga.pia.DramaBGMPhase" json:"bgm_phase,omitempty"`
	BgmProgress          int64         `protobuf:"varint,3,opt,name=bgm_progress,json=bgmProgress,proto3" json:"bgm_progress,omitempty"`
	Version              int64         `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	OperationTime        int64         `protobuf:"varint,5,opt,name=operation_time,json=operationTime,proto3" json:"operation_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DramaBgmStatus) Reset()         { *m = DramaBgmStatus{} }
func (m *DramaBgmStatus) String() string { return proto.CompactTextString(m) }
func (*DramaBgmStatus) ProtoMessage()    {}
func (*DramaBgmStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{65}
}
func (m *DramaBgmStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaBgmStatus.Unmarshal(m, b)
}
func (m *DramaBgmStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaBgmStatus.Marshal(b, m, deterministic)
}
func (dst *DramaBgmStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaBgmStatus.Merge(dst, src)
}
func (m *DramaBgmStatus) XXX_Size() int {
	return xxx_messageInfo_DramaBgmStatus.Size(m)
}
func (m *DramaBgmStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaBgmStatus.DiscardUnknown(m)
}

var xxx_messageInfo_DramaBgmStatus proto.InternalMessageInfo

func (m *DramaBgmStatus) GetBgmId() string {
	if m != nil {
		return m.BgmId
	}
	return ""
}

func (m *DramaBgmStatus) GetBgmPhase() DramaBGMPhase {
	if m != nil {
		return m.BgmPhase
	}
	return DramaBGMPhase_DRAMA_BGM_PHASE_UNSPECIFIED
}

func (m *DramaBgmStatus) GetBgmProgress() int64 {
	if m != nil {
		return m.BgmProgress
	}
	return 0
}

func (m *DramaBgmStatus) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *DramaBgmStatus) GetOperationTime() int64 {
	if m != nil {
		return m.OperationTime
	}
	return 0
}

// 剧本BGM音量状态，(广播类型见channel_.proto ChannelMsgType.PIA_CHANNEL_DRAMA_BGM_STATUS)
type DramaBgmVolStatus struct {
	Vol                  int32    `protobuf:"varint,6,opt,name=vol,proto3" json:"vol,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DramaBgmVolStatus) Reset()         { *m = DramaBgmVolStatus{} }
func (m *DramaBgmVolStatus) String() string { return proto.CompactTextString(m) }
func (*DramaBgmVolStatus) ProtoMessage()    {}
func (*DramaBgmVolStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{66}
}
func (m *DramaBgmVolStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaBgmVolStatus.Unmarshal(m, b)
}
func (m *DramaBgmVolStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaBgmVolStatus.Marshal(b, m, deterministic)
}
func (dst *DramaBgmVolStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaBgmVolStatus.Merge(dst, src)
}
func (m *DramaBgmVolStatus) XXX_Size() int {
	return xxx_messageInfo_DramaBgmVolStatus.Size(m)
}
func (m *DramaBgmVolStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaBgmVolStatus.DiscardUnknown(m)
}

var xxx_messageInfo_DramaBgmVolStatus proto.InternalMessageInfo

func (m *DramaBgmVolStatus) GetVol() int32 {
	if m != nil {
		return m.Vol
	}
	return 0
}

// BGM操作请求
type PiaOperateBgmReq struct {
	BaseReq              *app.BaseReq          `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OperationType        DramaBGMOperationType `protobuf:"varint,3,opt,name=operation_type,json=operationType,proto3,enum=ga.pia.DramaBGMOperationType" json:"operation_type,omitempty"`
	BgmId                string                `protobuf:"bytes,4,opt,name=bgm_id,json=bgmId,proto3" json:"bgm_id,omitempty"`
	CurProgress          int64                 `protobuf:"varint,5,opt,name=cur_progress,json=curProgress,proto3" json:"cur_progress,omitempty"`
	NextProgress         int64                 `protobuf:"varint,6,opt,name=next_progress,json=nextProgress,proto3" json:"next_progress,omitempty"`
	RoundId              int64                 `protobuf:"varint,7,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *PiaOperateBgmReq) Reset()         { *m = PiaOperateBgmReq{} }
func (m *PiaOperateBgmReq) String() string { return proto.CompactTextString(m) }
func (*PiaOperateBgmReq) ProtoMessage()    {}
func (*PiaOperateBgmReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{67}
}
func (m *PiaOperateBgmReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaOperateBgmReq.Unmarshal(m, b)
}
func (m *PiaOperateBgmReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaOperateBgmReq.Marshal(b, m, deterministic)
}
func (dst *PiaOperateBgmReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaOperateBgmReq.Merge(dst, src)
}
func (m *PiaOperateBgmReq) XXX_Size() int {
	return xxx_messageInfo_PiaOperateBgmReq.Size(m)
}
func (m *PiaOperateBgmReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaOperateBgmReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaOperateBgmReq proto.InternalMessageInfo

func (m *PiaOperateBgmReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaOperateBgmReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaOperateBgmReq) GetOperationType() DramaBGMOperationType {
	if m != nil {
		return m.OperationType
	}
	return DramaBGMOperationType_DRAMA_BGM_OPERATION_TYPE_UNSPECIFIED
}

func (m *PiaOperateBgmReq) GetBgmId() string {
	if m != nil {
		return m.BgmId
	}
	return ""
}

func (m *PiaOperateBgmReq) GetCurProgress() int64 {
	if m != nil {
		return m.CurProgress
	}
	return 0
}

func (m *PiaOperateBgmReq) GetNextProgress() int64 {
	if m != nil {
		return m.NextProgress
	}
	return 0
}

func (m *PiaOperateBgmReq) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// BGM操作响应
type PiaOperateBgmResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BgmStatus            *DramaBgmStatus `protobuf:"bytes,2,opt,name=bgm_status,json=bgmStatus,proto3" json:"bgm_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PiaOperateBgmResp) Reset()         { *m = PiaOperateBgmResp{} }
func (m *PiaOperateBgmResp) String() string { return proto.CompactTextString(m) }
func (*PiaOperateBgmResp) ProtoMessage()    {}
func (*PiaOperateBgmResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{68}
}
func (m *PiaOperateBgmResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaOperateBgmResp.Unmarshal(m, b)
}
func (m *PiaOperateBgmResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaOperateBgmResp.Marshal(b, m, deterministic)
}
func (dst *PiaOperateBgmResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaOperateBgmResp.Merge(dst, src)
}
func (m *PiaOperateBgmResp) XXX_Size() int {
	return xxx_messageInfo_PiaOperateBgmResp.Size(m)
}
func (m *PiaOperateBgmResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaOperateBgmResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaOperateBgmResp proto.InternalMessageInfo

func (m *PiaOperateBgmResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaOperateBgmResp) GetBgmStatus() *DramaBgmStatus {
	if m != nil {
		return m.BgmStatus
	}
	return nil
}

// BGM音量操作请求
type PiaOperateBgmVolReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Vol                  int32        `protobuf:"varint,3,opt,name=vol,proto3" json:"vol,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaOperateBgmVolReq) Reset()         { *m = PiaOperateBgmVolReq{} }
func (m *PiaOperateBgmVolReq) String() string { return proto.CompactTextString(m) }
func (*PiaOperateBgmVolReq) ProtoMessage()    {}
func (*PiaOperateBgmVolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{69}
}
func (m *PiaOperateBgmVolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaOperateBgmVolReq.Unmarshal(m, b)
}
func (m *PiaOperateBgmVolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaOperateBgmVolReq.Marshal(b, m, deterministic)
}
func (dst *PiaOperateBgmVolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaOperateBgmVolReq.Merge(dst, src)
}
func (m *PiaOperateBgmVolReq) XXX_Size() int {
	return xxx_messageInfo_PiaOperateBgmVolReq.Size(m)
}
func (m *PiaOperateBgmVolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaOperateBgmVolReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaOperateBgmVolReq proto.InternalMessageInfo

func (m *PiaOperateBgmVolReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaOperateBgmVolReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaOperateBgmVolReq) GetVol() int32 {
	if m != nil {
		return m.Vol
	}
	return 0
}

// BGM音量操作响应
type PiaOperateBgmVolResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BgmVolStatus         *DramaBgmVolStatus `protobuf:"bytes,2,opt,name=bgm_vol_status,json=bgmVolStatus,proto3" json:"bgm_vol_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PiaOperateBgmVolResp) Reset()         { *m = PiaOperateBgmVolResp{} }
func (m *PiaOperateBgmVolResp) String() string { return proto.CompactTextString(m) }
func (*PiaOperateBgmVolResp) ProtoMessage()    {}
func (*PiaOperateBgmVolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{70}
}
func (m *PiaOperateBgmVolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaOperateBgmVolResp.Unmarshal(m, b)
}
func (m *PiaOperateBgmVolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaOperateBgmVolResp.Marshal(b, m, deterministic)
}
func (dst *PiaOperateBgmVolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaOperateBgmVolResp.Merge(dst, src)
}
func (m *PiaOperateBgmVolResp) XXX_Size() int {
	return xxx_messageInfo_PiaOperateBgmVolResp.Size(m)
}
func (m *PiaOperateBgmVolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaOperateBgmVolResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaOperateBgmVolResp proto.InternalMessageInfo

func (m *PiaOperateBgmVolResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaOperateBgmVolResp) GetBgmVolStatus() *DramaBgmVolStatus {
	if m != nil {
		return m.BgmVolStatus
	}
	return nil
}

// pia戏角色
type PiaRole struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Avatar               string   `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Introduction         string   `protobuf:"bytes,5,opt,name=introduction,proto3" json:"introduction,omitempty"`
	Color                string   `protobuf:"bytes,6,opt,name=color,proto3" json:"color,omitempty"`
	DialogueRatio        float64  `protobuf:"fixed64,7,opt,name=dialogue_ratio,json=dialogueRatio,proto3" json:"dialogue_ratio,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaRole) Reset()         { *m = PiaRole{} }
func (m *PiaRole) String() string { return proto.CompactTextString(m) }
func (*PiaRole) ProtoMessage()    {}
func (*PiaRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{71}
}
func (m *PiaRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaRole.Unmarshal(m, b)
}
func (m *PiaRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaRole.Marshal(b, m, deterministic)
}
func (dst *PiaRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaRole.Merge(dst, src)
}
func (m *PiaRole) XXX_Size() int {
	return xxx_messageInfo_PiaRole.Size(m)
}
func (m *PiaRole) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaRole.DiscardUnknown(m)
}

var xxx_messageInfo_PiaRole proto.InternalMessageInfo

func (m *PiaRole) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PiaRole) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PiaRole) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *PiaRole) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *PiaRole) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *PiaRole) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func (m *PiaRole) GetDialogueRatio() float64 {
	if m != nil {
		return m.DialogueRatio
	}
	return 0
}

type PiaDuration struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaDuration) Reset()         { *m = PiaDuration{} }
func (m *PiaDuration) String() string { return proto.CompactTextString(m) }
func (*PiaDuration) ProtoMessage()    {}
func (*PiaDuration) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{72}
}
func (m *PiaDuration) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaDuration.Unmarshal(m, b)
}
func (m *PiaDuration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaDuration.Marshal(b, m, deterministic)
}
func (dst *PiaDuration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaDuration.Merge(dst, src)
}
func (m *PiaDuration) XXX_Size() int {
	return xxx_messageInfo_PiaDuration.Size(m)
}
func (m *PiaDuration) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaDuration.DiscardUnknown(m)
}

var xxx_messageInfo_PiaDuration proto.InternalMessageInfo

func (m *PiaDuration) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *PiaDuration) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 剧本内容（段落）
type PiaContent struct {
	Id                   string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RoleId               string       `protobuf:"bytes,2,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Dialogue             string       `protobuf:"bytes,3,opt,name=dialogue,proto3" json:"dialogue,omitempty"`
	Duration             *PiaDuration `protobuf:"bytes,4,opt,name=duration,proto3" json:"duration,omitempty"`
	RoleName             string       `protobuf:"bytes,5,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	Color                string       `protobuf:"bytes,6,opt,name=color,proto3" json:"color,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaContent) Reset()         { *m = PiaContent{} }
func (m *PiaContent) String() string { return proto.CompactTextString(m) }
func (*PiaContent) ProtoMessage()    {}
func (*PiaContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{73}
}
func (m *PiaContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaContent.Unmarshal(m, b)
}
func (m *PiaContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaContent.Marshal(b, m, deterministic)
}
func (dst *PiaContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaContent.Merge(dst, src)
}
func (m *PiaContent) XXX_Size() int {
	return xxx_messageInfo_PiaContent.Size(m)
}
func (m *PiaContent) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaContent.DiscardUnknown(m)
}

var xxx_messageInfo_PiaContent proto.InternalMessageInfo

func (m *PiaContent) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PiaContent) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *PiaContent) GetDialogue() string {
	if m != nil {
		return m.Dialogue
	}
	return ""
}

func (m *PiaContent) GetDuration() *PiaDuration {
	if m != nil {
		return m.Duration
	}
	return nil
}

func (m *PiaContent) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *PiaContent) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

type PiaPicture struct {
	Id                   string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url                  string       `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Duration             *PiaDuration `protobuf:"bytes,3,opt,name=duration,proto3" json:"duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaPicture) Reset()         { *m = PiaPicture{} }
func (m *PiaPicture) String() string { return proto.CompactTextString(m) }
func (*PiaPicture) ProtoMessage()    {}
func (*PiaPicture) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{74}
}
func (m *PiaPicture) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaPicture.Unmarshal(m, b)
}
func (m *PiaPicture) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaPicture.Marshal(b, m, deterministic)
}
func (dst *PiaPicture) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaPicture.Merge(dst, src)
}
func (m *PiaPicture) XXX_Size() int {
	return xxx_messageInfo_PiaPicture.Size(m)
}
func (m *PiaPicture) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaPicture.DiscardUnknown(m)
}

var xxx_messageInfo_PiaPicture proto.InternalMessageInfo

func (m *PiaPicture) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PiaPicture) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PiaPicture) GetDuration() *PiaDuration {
	if m != nil {
		return m.Duration
	}
	return nil
}

type PiaBGM struct {
	Id                   string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Url                  string       `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	Duration             *PiaDuration `protobuf:"bytes,4,opt,name=duration,proto3" json:"duration,omitempty"`
	Length               uint32       `protobuf:"varint,5,opt,name=length,proto3" json:"length,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaBGM) Reset()         { *m = PiaBGM{} }
func (m *PiaBGM) String() string { return proto.CompactTextString(m) }
func (*PiaBGM) ProtoMessage()    {}
func (*PiaBGM) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{75}
}
func (m *PiaBGM) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaBGM.Unmarshal(m, b)
}
func (m *PiaBGM) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaBGM.Marshal(b, m, deterministic)
}
func (dst *PiaBGM) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaBGM.Merge(dst, src)
}
func (m *PiaBGM) XXX_Size() int {
	return xxx_messageInfo_PiaBGM.Size(m)
}
func (m *PiaBGM) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaBGM.DiscardUnknown(m)
}

var xxx_messageInfo_PiaBGM proto.InternalMessageInfo

func (m *PiaBGM) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PiaBGM) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *PiaBGM) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *PiaBGM) GetDuration() *PiaDuration {
	if m != nil {
		return m.Duration
	}
	return nil
}

func (m *PiaBGM) GetLength() uint32 {
	if m != nil {
		return m.Length
	}
	return 0
}

// 剧本子信息(概要信息)
type DramaSubInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	CoverUrl             string   `protobuf:"bytes,3,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Author               string   `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
	Desc                 string   `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	MaleCnt              uint32   `protobuf:"varint,6,opt,name=male_cnt,json=maleCnt,proto3" json:"male_cnt,omitempty"`
	FemaleCnt            uint32   `protobuf:"varint,7,opt,name=female_cnt,json=femaleCnt,proto3" json:"female_cnt,omitempty"`
	WordCnt              uint32   `protobuf:"varint,8,opt,name=word_cnt,json=wordCnt,proto3" json:"word_cnt,omitempty"`
	TagList              []string `protobuf:"bytes,9,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	Type                 string   `protobuf:"bytes,10,opt,name=type,proto3" json:"type,omitempty"`
	DisplayId            uint64   `protobuf:"varint,11,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	AuthorId             uint32   `protobuf:"varint,12,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	Duration             uint32   `protobuf:"varint,13,opt,name=duration,proto3" json:"duration,omitempty"`
	CreateTime           int64    `protobuf:"varint,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	RelatedDramaId       uint32   `protobuf:"varint,15,opt,name=related_drama_id,json=relatedDramaId,proto3" json:"related_drama_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DramaSubInfo) Reset()         { *m = DramaSubInfo{} }
func (m *DramaSubInfo) String() string { return proto.CompactTextString(m) }
func (*DramaSubInfo) ProtoMessage()    {}
func (*DramaSubInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{76}
}
func (m *DramaSubInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaSubInfo.Unmarshal(m, b)
}
func (m *DramaSubInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaSubInfo.Marshal(b, m, deterministic)
}
func (dst *DramaSubInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaSubInfo.Merge(dst, src)
}
func (m *DramaSubInfo) XXX_Size() int {
	return xxx_messageInfo_DramaSubInfo.Size(m)
}
func (m *DramaSubInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaSubInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DramaSubInfo proto.InternalMessageInfo

func (m *DramaSubInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DramaSubInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *DramaSubInfo) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *DramaSubInfo) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *DramaSubInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *DramaSubInfo) GetMaleCnt() uint32 {
	if m != nil {
		return m.MaleCnt
	}
	return 0
}

func (m *DramaSubInfo) GetFemaleCnt() uint32 {
	if m != nil {
		return m.FemaleCnt
	}
	return 0
}

func (m *DramaSubInfo) GetWordCnt() uint32 {
	if m != nil {
		return m.WordCnt
	}
	return 0
}

func (m *DramaSubInfo) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *DramaSubInfo) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *DramaSubInfo) GetDisplayId() uint64 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *DramaSubInfo) GetAuthorId() uint32 {
	if m != nil {
		return m.AuthorId
	}
	return 0
}

func (m *DramaSubInfo) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *DramaSubInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *DramaSubInfo) GetRelatedDramaId() uint32 {
	if m != nil {
		return m.RelatedDramaId
	}
	return 0
}

// 带剧本状态的信息
type DramaInfoWithStatus struct {
	DramaSubInfo         *DramaSubInfo `protobuf:"bytes,1,opt,name=drama_sub_info,json=dramaSubInfo,proto3" json:"drama_sub_info,omitempty"`
	HasPlay              bool          `protobuf:"varint,2,opt,name=has_play,json=hasPlay,proto3" json:"has_play,omitempty"`
	StickTag             string        `protobuf:"bytes,3,opt,name=stick_tag,json=stickTag,proto3" json:"stick_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DramaInfoWithStatus) Reset()         { *m = DramaInfoWithStatus{} }
func (m *DramaInfoWithStatus) String() string { return proto.CompactTextString(m) }
func (*DramaInfoWithStatus) ProtoMessage()    {}
func (*DramaInfoWithStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{77}
}
func (m *DramaInfoWithStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaInfoWithStatus.Unmarshal(m, b)
}
func (m *DramaInfoWithStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaInfoWithStatus.Marshal(b, m, deterministic)
}
func (dst *DramaInfoWithStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaInfoWithStatus.Merge(dst, src)
}
func (m *DramaInfoWithStatus) XXX_Size() int {
	return xxx_messageInfo_DramaInfoWithStatus.Size(m)
}
func (m *DramaInfoWithStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaInfoWithStatus.DiscardUnknown(m)
}

var xxx_messageInfo_DramaInfoWithStatus proto.InternalMessageInfo

func (m *DramaInfoWithStatus) GetDramaSubInfo() *DramaSubInfo {
	if m != nil {
		return m.DramaSubInfo
	}
	return nil
}

func (m *DramaInfoWithStatus) GetHasPlay() bool {
	if m != nil {
		return m.HasPlay
	}
	return false
}

func (m *DramaInfoWithStatus) GetStickTag() string {
	if m != nil {
		return m.StickTag
	}
	return ""
}

// 新剧本信息
type DramaV2 struct {
	DramaSubInfo         *DramaSubInfo        `protobuf:"bytes,1,opt,name=drama_sub_info,json=dramaSubInfo,proto3" json:"drama_sub_info,omitempty"`
	RoleList             []*PiaRole           `protobuf:"bytes,2,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	ContentList          []*PiaContent        `protobuf:"bytes,3,rep,name=content_list,json=contentList,proto3" json:"content_list,omitempty"`
	BgmUrl               []*PiaBGM            `protobuf:"bytes,4,rep,name=bgm_url,json=bgmUrl,proto3" json:"bgm_url,omitempty"`
	PictureList          []*PiaPicture        `protobuf:"bytes,5,rep,name=picture_list,json=pictureList,proto3" json:"picture_list,omitempty"`
	PlayType             PiaPlayType          `protobuf:"varint,6,opt,name=play_type,json=playType,proto3,enum=ga.pia.PiaPlayType" json:"play_type,omitempty"`
	CreatorInfo          *PiaDramaCreatorInfo `protobuf:"bytes,7,opt,name=creator_info,json=creatorInfo,proto3" json:"creator_info,omitempty"`
	IsCopyDrama          bool                 `protobuf:"varint,8,opt,name=is_copy_drama,json=isCopyDrama,proto3" json:"is_copy_drama,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *DramaV2) Reset()         { *m = DramaV2{} }
func (m *DramaV2) String() string { return proto.CompactTextString(m) }
func (*DramaV2) ProtoMessage()    {}
func (*DramaV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{78}
}
func (m *DramaV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DramaV2.Unmarshal(m, b)
}
func (m *DramaV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DramaV2.Marshal(b, m, deterministic)
}
func (dst *DramaV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DramaV2.Merge(dst, src)
}
func (m *DramaV2) XXX_Size() int {
	return xxx_messageInfo_DramaV2.Size(m)
}
func (m *DramaV2) XXX_DiscardUnknown() {
	xxx_messageInfo_DramaV2.DiscardUnknown(m)
}

var xxx_messageInfo_DramaV2 proto.InternalMessageInfo

func (m *DramaV2) GetDramaSubInfo() *DramaSubInfo {
	if m != nil {
		return m.DramaSubInfo
	}
	return nil
}

func (m *DramaV2) GetRoleList() []*PiaRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

func (m *DramaV2) GetContentList() []*PiaContent {
	if m != nil {
		return m.ContentList
	}
	return nil
}

func (m *DramaV2) GetBgmUrl() []*PiaBGM {
	if m != nil {
		return m.BgmUrl
	}
	return nil
}

func (m *DramaV2) GetPictureList() []*PiaPicture {
	if m != nil {
		return m.PictureList
	}
	return nil
}

func (m *DramaV2) GetPlayType() PiaPlayType {
	if m != nil {
		return m.PlayType
	}
	return PiaPlayType_PLAY_TYPE_UNKNOWN
}

func (m *DramaV2) GetCreatorInfo() *PiaDramaCreatorInfo {
	if m != nil {
		return m.CreatorInfo
	}
	return nil
}

func (m *DramaV2) GetIsCopyDrama() bool {
	if m != nil {
		return m.IsCopyDrama
	}
	return false
}

// 获取筛选标签组 新版
type GetSearchOptionGroupV2Req struct {
	BaseReq              *app.BaseReq                               `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SearchOptionType     GetSearchOptionGroupV2Req_SearchOptionType `protobuf:"varint,2,opt,name=search_option_type,json=searchOptionType,proto3,enum=ga.pia.GetSearchOptionGroupV2Req_SearchOptionType" json:"search_option_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *GetSearchOptionGroupV2Req) Reset()         { *m = GetSearchOptionGroupV2Req{} }
func (m *GetSearchOptionGroupV2Req) String() string { return proto.CompactTextString(m) }
func (*GetSearchOptionGroupV2Req) ProtoMessage()    {}
func (*GetSearchOptionGroupV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{79}
}
func (m *GetSearchOptionGroupV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchOptionGroupV2Req.Unmarshal(m, b)
}
func (m *GetSearchOptionGroupV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchOptionGroupV2Req.Marshal(b, m, deterministic)
}
func (dst *GetSearchOptionGroupV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchOptionGroupV2Req.Merge(dst, src)
}
func (m *GetSearchOptionGroupV2Req) XXX_Size() int {
	return xxx_messageInfo_GetSearchOptionGroupV2Req.Size(m)
}
func (m *GetSearchOptionGroupV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchOptionGroupV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchOptionGroupV2Req proto.InternalMessageInfo

func (m *GetSearchOptionGroupV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSearchOptionGroupV2Req) GetSearchOptionType() GetSearchOptionGroupV2Req_SearchOptionType {
	if m != nil {
		return m.SearchOptionType
	}
	return GetSearchOptionGroupV2Req_SEARCH_OPTION_TYPE_DRAMA
}

// 获取筛选标签组 新版
type GetSearchOptionGroupV2Resp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SearchOptionGroup    []*SearchOptionGroup `protobuf:"bytes,2,rep,name=search_option_group,json=searchOptionGroup,proto3" json:"search_option_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetSearchOptionGroupV2Resp) Reset()         { *m = GetSearchOptionGroupV2Resp{} }
func (m *GetSearchOptionGroupV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetSearchOptionGroupV2Resp) ProtoMessage()    {}
func (*GetSearchOptionGroupV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{80}
}
func (m *GetSearchOptionGroupV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchOptionGroupV2Resp.Unmarshal(m, b)
}
func (m *GetSearchOptionGroupV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchOptionGroupV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetSearchOptionGroupV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchOptionGroupV2Resp.Merge(dst, src)
}
func (m *GetSearchOptionGroupV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetSearchOptionGroupV2Resp.Size(m)
}
func (m *GetSearchOptionGroupV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchOptionGroupV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchOptionGroupV2Resp proto.InternalMessageInfo

func (m *GetSearchOptionGroupV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSearchOptionGroupV2Resp) GetSearchOptionGroup() []*SearchOptionGroup {
	if m != nil {
		return m.SearchOptionGroup
	}
	return nil
}

// 分页获取剧本库列表
type GetDramaListReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SearchOption         []*SearchOption `protobuf:"bytes,2,rep,name=search_option,json=searchOption,proto3" json:"search_option,omitempty"`
	PageToken            string          `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32          `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	ChannelId            uint32          `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetDramaListReq) Reset()         { *m = GetDramaListReq{} }
func (m *GetDramaListReq) String() string { return proto.CompactTextString(m) }
func (*GetDramaListReq) ProtoMessage()    {}
func (*GetDramaListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{81}
}
func (m *GetDramaListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaListReq.Unmarshal(m, b)
}
func (m *GetDramaListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaListReq.Marshal(b, m, deterministic)
}
func (dst *GetDramaListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaListReq.Merge(dst, src)
}
func (m *GetDramaListReq) XXX_Size() int {
	return xxx_messageInfo_GetDramaListReq.Size(m)
}
func (m *GetDramaListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaListReq proto.InternalMessageInfo

func (m *GetDramaListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetDramaListReq) GetSearchOption() []*SearchOption {
	if m != nil {
		return m.SearchOption
	}
	return nil
}

func (m *GetDramaListReq) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *GetDramaListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetDramaListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 分页获取剧本库列表
type GetDramaListResp struct {
	BaseResp                *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaInfoWithStatusList []*DramaInfoWithStatus `protobuf:"bytes,2,rep,name=drama_info_with_status_list,json=dramaInfoWithStatusList,proto3" json:"drama_info_with_status_list,omitempty"`
	PageToken               string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}               `json:"-"`
	XXX_unrecognized        []byte                 `json:"-"`
	XXX_sizecache           int32                  `json:"-"`
}

func (m *GetDramaListResp) Reset()         { *m = GetDramaListResp{} }
func (m *GetDramaListResp) String() string { return proto.CompactTextString(m) }
func (*GetDramaListResp) ProtoMessage()    {}
func (*GetDramaListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{82}
}
func (m *GetDramaListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaListResp.Unmarshal(m, b)
}
func (m *GetDramaListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaListResp.Marshal(b, m, deterministic)
}
func (dst *GetDramaListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaListResp.Merge(dst, src)
}
func (m *GetDramaListResp) XXX_Size() int {
	return xxx_messageInfo_GetDramaListResp.Size(m)
}
func (m *GetDramaListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaListResp proto.InternalMessageInfo

func (m *GetDramaListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetDramaListResp) GetDramaInfoWithStatusList() []*DramaInfoWithStatus {
	if m != nil {
		return m.DramaInfoWithStatusList
	}
	return nil
}

func (m *GetDramaListResp) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

// 剧本详情
type GetDramaDetailByIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Id                   uint32       `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetDramaDetailByIdReq) Reset()         { *m = GetDramaDetailByIdReq{} }
func (m *GetDramaDetailByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetDramaDetailByIdReq) ProtoMessage()    {}
func (*GetDramaDetailByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{83}
}
func (m *GetDramaDetailByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaDetailByIdReq.Unmarshal(m, b)
}
func (m *GetDramaDetailByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaDetailByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetDramaDetailByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaDetailByIdReq.Merge(dst, src)
}
func (m *GetDramaDetailByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetDramaDetailByIdReq.Size(m)
}
func (m *GetDramaDetailByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaDetailByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaDetailByIdReq proto.InternalMessageInfo

func (m *GetDramaDetailByIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetDramaDetailByIdReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 用户uid、accout信息
type PiaUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Accout               string   `protobuf:"bytes,3,opt,name=accout,proto3" json:"accout,omitempty"`
	Username             string   `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Sex                  int32    `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaUserInfo) Reset()         { *m = PiaUserInfo{} }
func (m *PiaUserInfo) String() string { return proto.CompactTextString(m) }
func (*PiaUserInfo) ProtoMessage()    {}
func (*PiaUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{84}
}
func (m *PiaUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaUserInfo.Unmarshal(m, b)
}
func (m *PiaUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaUserInfo.Marshal(b, m, deterministic)
}
func (dst *PiaUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaUserInfo.Merge(dst, src)
}
func (m *PiaUserInfo) XXX_Size() int {
	return xxx_messageInfo_PiaUserInfo.Size(m)
}
func (m *PiaUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PiaUserInfo proto.InternalMessageInfo

func (m *PiaUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PiaUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PiaUserInfo) GetAccout() string {
	if m != nil {
		return m.Accout
	}
	return ""
}

func (m *PiaUserInfo) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *PiaUserInfo) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type GetDramaDetailByIdResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Drama                *DramaV2      `protobuf:"bytes,2,opt,name=drama,proto3" json:"drama,omitempty"`
	CollectNum           uint32        `protobuf:"varint,3,opt,name=collect_num,json=collectNum,proto3" json:"collect_num,omitempty"`
	IsCollected          bool          `protobuf:"varint,4,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty"`
	HasPlay              bool          `protobuf:"varint,5,opt,name=has_play,json=hasPlay,proto3" json:"has_play,omitempty"`
	AuthorInfo           *PiaUserInfo  `protobuf:"bytes,6,opt,name=author_info,json=authorInfo,proto3" json:"author_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetDramaDetailByIdResp) Reset()         { *m = GetDramaDetailByIdResp{} }
func (m *GetDramaDetailByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetDramaDetailByIdResp) ProtoMessage()    {}
func (*GetDramaDetailByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{85}
}
func (m *GetDramaDetailByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDramaDetailByIdResp.Unmarshal(m, b)
}
func (m *GetDramaDetailByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDramaDetailByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetDramaDetailByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDramaDetailByIdResp.Merge(dst, src)
}
func (m *GetDramaDetailByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetDramaDetailByIdResp.Size(m)
}
func (m *GetDramaDetailByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDramaDetailByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDramaDetailByIdResp proto.InternalMessageInfo

func (m *GetDramaDetailByIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetDramaDetailByIdResp) GetDrama() *DramaV2 {
	if m != nil {
		return m.Drama
	}
	return nil
}

func (m *GetDramaDetailByIdResp) GetCollectNum() uint32 {
	if m != nil {
		return m.CollectNum
	}
	return 0
}

func (m *GetDramaDetailByIdResp) GetIsCollected() bool {
	if m != nil {
		return m.IsCollected
	}
	return false
}

func (m *GetDramaDetailByIdResp) GetHasPlay() bool {
	if m != nil {
		return m.HasPlay
	}
	return false
}

func (m *GetDramaDetailByIdResp) GetAuthorInfo() *PiaUserInfo {
	if m != nil {
		return m.AuthorInfo
	}
	return nil
}

// 获取正在玩的房间v2
type PlayingChannelInfoV2 struct {
	ChannelId             uint32               `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName           string               `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelMemberCount    uint32               `protobuf:"varint,3,opt,name=channel_member_count,json=channelMemberCount,proto3" json:"channel_member_count,omitempty"`
	ChannelDisplayId      uint32               `protobuf:"varint,4,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	ChannelType           uint32               `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelIcon           string               `protobuf:"bytes,6,opt,name=channel_icon,json=channelIcon,proto3" json:"channel_icon,omitempty"`
	ChannelStatusType     PiaChannelStatusType `protobuf:"varint,7,opt,name=channel_status_type,json=channelStatusType,proto3,enum=ga.pia.PiaChannelStatusType" json:"channel_status_type,omitempty"`
	ChannelDesc           string               `protobuf:"bytes,8,opt,name=channel_desc,json=channelDesc,proto3" json:"channel_desc,omitempty"`
	ChannelCreatorAccount string               `protobuf:"bytes,9,opt,name=channel_creator_account,json=channelCreatorAccount,proto3" json:"channel_creator_account,omitempty"`
	ChannelBindId         uint32               `protobuf:"varint,10,opt,name=channel_bind_id,json=channelBindId,proto3" json:"channel_bind_id,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *PlayingChannelInfoV2) Reset()         { *m = PlayingChannelInfoV2{} }
func (m *PlayingChannelInfoV2) String() string { return proto.CompactTextString(m) }
func (*PlayingChannelInfoV2) ProtoMessage()    {}
func (*PlayingChannelInfoV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{86}
}
func (m *PlayingChannelInfoV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayingChannelInfoV2.Unmarshal(m, b)
}
func (m *PlayingChannelInfoV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayingChannelInfoV2.Marshal(b, m, deterministic)
}
func (dst *PlayingChannelInfoV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayingChannelInfoV2.Merge(dst, src)
}
func (m *PlayingChannelInfoV2) XXX_Size() int {
	return xxx_messageInfo_PlayingChannelInfoV2.Size(m)
}
func (m *PlayingChannelInfoV2) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayingChannelInfoV2.DiscardUnknown(m)
}

var xxx_messageInfo_PlayingChannelInfoV2 proto.InternalMessageInfo

func (m *PlayingChannelInfoV2) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PlayingChannelInfoV2) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PlayingChannelInfoV2) GetChannelMemberCount() uint32 {
	if m != nil {
		return m.ChannelMemberCount
	}
	return 0
}

func (m *PlayingChannelInfoV2) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *PlayingChannelInfoV2) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *PlayingChannelInfoV2) GetChannelIcon() string {
	if m != nil {
		return m.ChannelIcon
	}
	return ""
}

func (m *PlayingChannelInfoV2) GetChannelStatusType() PiaChannelStatusType {
	if m != nil {
		return m.ChannelStatusType
	}
	return PiaChannelStatusType_PIA_CHANNEL_STATUS_INVALID
}

func (m *PlayingChannelInfoV2) GetChannelDesc() string {
	if m != nil {
		return m.ChannelDesc
	}
	return ""
}

func (m *PlayingChannelInfoV2) GetChannelCreatorAccount() string {
	if m != nil {
		return m.ChannelCreatorAccount
	}
	return ""
}

func (m *PlayingChannelInfoV2) GetChannelBindId() uint32 {
	if m != nil {
		return m.ChannelBindId
	}
	return 0
}

// 获取正在玩的房间v2
type GetPlayingChannelV2Req struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	DramaId              uint32       `protobuf:"varint,2,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	PageToken            string       `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32       `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPlayingChannelV2Req) Reset()         { *m = GetPlayingChannelV2Req{} }
func (m *GetPlayingChannelV2Req) String() string { return proto.CompactTextString(m) }
func (*GetPlayingChannelV2Req) ProtoMessage()    {}
func (*GetPlayingChannelV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{87}
}
func (m *GetPlayingChannelV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayingChannelV2Req.Unmarshal(m, b)
}
func (m *GetPlayingChannelV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayingChannelV2Req.Marshal(b, m, deterministic)
}
func (dst *GetPlayingChannelV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayingChannelV2Req.Merge(dst, src)
}
func (m *GetPlayingChannelV2Req) XXX_Size() int {
	return xxx_messageInfo_GetPlayingChannelV2Req.Size(m)
}
func (m *GetPlayingChannelV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayingChannelV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayingChannelV2Req proto.InternalMessageInfo

func (m *GetPlayingChannelV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPlayingChannelV2Req) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

func (m *GetPlayingChannelV2Req) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *GetPlayingChannelV2Req) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetPlayingChannelV2Resp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PlayingChannels      []*PlayingChannelInfoV2 `protobuf:"bytes,2,rep,name=playing_channels,json=playingChannels,proto3" json:"playing_channels,omitempty"`
	PageToken            string                  `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetPlayingChannelV2Resp) Reset()         { *m = GetPlayingChannelV2Resp{} }
func (m *GetPlayingChannelV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetPlayingChannelV2Resp) ProtoMessage()    {}
func (*GetPlayingChannelV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{88}
}
func (m *GetPlayingChannelV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayingChannelV2Resp.Unmarshal(m, b)
}
func (m *GetPlayingChannelV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayingChannelV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetPlayingChannelV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayingChannelV2Resp.Merge(dst, src)
}
func (m *GetPlayingChannelV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetPlayingChannelV2Resp.Size(m)
}
func (m *GetPlayingChannelV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayingChannelV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayingChannelV2Resp proto.InternalMessageInfo

func (m *GetPlayingChannelV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayingChannelV2Resp) GetPlayingChannels() []*PlayingChannelInfoV2 {
	if m != nil {
		return m.PlayingChannels
	}
	return nil
}

func (m *GetPlayingChannelV2Resp) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

// 获取排行榜剧本列表Req
type PiaGetRankingListReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RankingListType      PiaRankingListType `protobuf:"varint,2,opt,name=ranking_list_type,json=rankingListType,proto3,enum=ga.pia.PiaRankingListType" json:"ranking_list_type,omitempty"`
	PageToken            string             `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32             `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PiaGetRankingListReq) Reset()         { *m = PiaGetRankingListReq{} }
func (m *PiaGetRankingListReq) String() string { return proto.CompactTextString(m) }
func (*PiaGetRankingListReq) ProtoMessage()    {}
func (*PiaGetRankingListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{89}
}
func (m *PiaGetRankingListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetRankingListReq.Unmarshal(m, b)
}
func (m *PiaGetRankingListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetRankingListReq.Marshal(b, m, deterministic)
}
func (dst *PiaGetRankingListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetRankingListReq.Merge(dst, src)
}
func (m *PiaGetRankingListReq) XXX_Size() int {
	return xxx_messageInfo_PiaGetRankingListReq.Size(m)
}
func (m *PiaGetRankingListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetRankingListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetRankingListReq proto.InternalMessageInfo

func (m *PiaGetRankingListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaGetRankingListReq) GetRankingListType() PiaRankingListType {
	if m != nil {
		return m.RankingListType
	}
	return PiaRankingListType_PIA_RANKING_LIST_INVALID
}

func (m *PiaGetRankingListReq) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *PiaGetRankingListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 获取排行榜剧本列表Resp
type PiaGetRankingListResp struct {
	BaseResp                *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaInfoWithStatusList []*DramaInfoWithStatus `protobuf:"bytes,2,rep,name=drama_info_with_status_list,json=dramaInfoWithStatusList,proto3" json:"drama_info_with_status_list,omitempty"`
	PageToken               string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}               `json:"-"`
	XXX_unrecognized        []byte                 `json:"-"`
	XXX_sizecache           int32                  `json:"-"`
}

func (m *PiaGetRankingListResp) Reset()         { *m = PiaGetRankingListResp{} }
func (m *PiaGetRankingListResp) String() string { return proto.CompactTextString(m) }
func (*PiaGetRankingListResp) ProtoMessage()    {}
func (*PiaGetRankingListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{90}
}
func (m *PiaGetRankingListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetRankingListResp.Unmarshal(m, b)
}
func (m *PiaGetRankingListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetRankingListResp.Marshal(b, m, deterministic)
}
func (dst *PiaGetRankingListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetRankingListResp.Merge(dst, src)
}
func (m *PiaGetRankingListResp) XXX_Size() int {
	return xxx_messageInfo_PiaGetRankingListResp.Size(m)
}
func (m *PiaGetRankingListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetRankingListResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetRankingListResp proto.InternalMessageInfo

func (m *PiaGetRankingListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaGetRankingListResp) GetDramaInfoWithStatusList() []*DramaInfoWithStatus {
	if m != nil {
		return m.DramaInfoWithStatusList
	}
	return nil
}

func (m *PiaGetRankingListResp) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

// 剧本收藏、取消收藏实现
type DoUserDramaCollectReq struct {
	BaseReq              *app.BaseReq  `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	DramaId              uint32        `protobuf:"varint,2,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	Op                   CollectOpType `protobuf:"varint,3,opt,name=op,proto3,enum=ga.pia.CollectOpType" json:"op,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DoUserDramaCollectReq) Reset()         { *m = DoUserDramaCollectReq{} }
func (m *DoUserDramaCollectReq) String() string { return proto.CompactTextString(m) }
func (*DoUserDramaCollectReq) ProtoMessage()    {}
func (*DoUserDramaCollectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{91}
}
func (m *DoUserDramaCollectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoUserDramaCollectReq.Unmarshal(m, b)
}
func (m *DoUserDramaCollectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoUserDramaCollectReq.Marshal(b, m, deterministic)
}
func (dst *DoUserDramaCollectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoUserDramaCollectReq.Merge(dst, src)
}
func (m *DoUserDramaCollectReq) XXX_Size() int {
	return xxx_messageInfo_DoUserDramaCollectReq.Size(m)
}
func (m *DoUserDramaCollectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DoUserDramaCollectReq.DiscardUnknown(m)
}

var xxx_messageInfo_DoUserDramaCollectReq proto.InternalMessageInfo

func (m *DoUserDramaCollectReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DoUserDramaCollectReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

func (m *DoUserDramaCollectReq) GetOp() CollectOpType {
	if m != nil {
		return m.Op
	}
	return CollectOpType_PIA_DRAMA_COLLECT_INVALID
}

type DoUserDramaCollectResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CollectNum           uint32        `protobuf:"varint,2,opt,name=collect_num,json=collectNum,proto3" json:"collect_num,omitempty"`
	IsCollected          bool          `protobuf:"varint,3,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DoUserDramaCollectResp) Reset()         { *m = DoUserDramaCollectResp{} }
func (m *DoUserDramaCollectResp) String() string { return proto.CompactTextString(m) }
func (*DoUserDramaCollectResp) ProtoMessage()    {}
func (*DoUserDramaCollectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{92}
}
func (m *DoUserDramaCollectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DoUserDramaCollectResp.Unmarshal(m, b)
}
func (m *DoUserDramaCollectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DoUserDramaCollectResp.Marshal(b, m, deterministic)
}
func (dst *DoUserDramaCollectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DoUserDramaCollectResp.Merge(dst, src)
}
func (m *DoUserDramaCollectResp) XXX_Size() int {
	return xxx_messageInfo_DoUserDramaCollectResp.Size(m)
}
func (m *DoUserDramaCollectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DoUserDramaCollectResp.DiscardUnknown(m)
}

var xxx_messageInfo_DoUserDramaCollectResp proto.InternalMessageInfo

func (m *DoUserDramaCollectResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *DoUserDramaCollectResp) GetCollectNum() uint32 {
	if m != nil {
		return m.CollectNum
	}
	return 0
}

func (m *DoUserDramaCollectResp) GetIsCollected() bool {
	if m != nil {
		return m.IsCollected
	}
	return false
}

// 查看用户的剧本收藏列表
type GetUserDramaCollectionReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SearchOption         []*SearchOption `protobuf:"bytes,2,rep,name=search_option,json=searchOption,proto3" json:"search_option,omitempty"`
	PageSize             uint32          `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken            string          `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserDramaCollectionReq) Reset()         { *m = GetUserDramaCollectionReq{} }
func (m *GetUserDramaCollectionReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDramaCollectionReq) ProtoMessage()    {}
func (*GetUserDramaCollectionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{93}
}
func (m *GetUserDramaCollectionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDramaCollectionReq.Unmarshal(m, b)
}
func (m *GetUserDramaCollectionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDramaCollectionReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDramaCollectionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDramaCollectionReq.Merge(dst, src)
}
func (m *GetUserDramaCollectionReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDramaCollectionReq.Size(m)
}
func (m *GetUserDramaCollectionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDramaCollectionReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDramaCollectionReq proto.InternalMessageInfo

func (m *GetUserDramaCollectionReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserDramaCollectionReq) GetSearchOption() []*SearchOption {
	if m != nil {
		return m.SearchOption
	}
	return nil
}

func (m *GetUserDramaCollectionReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetUserDramaCollectionReq) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

type GetUserDramaCollectionResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaList            []*DramaInfoWithStatus `protobuf:"bytes,2,rep,name=drama_list,json=dramaList,proto3" json:"drama_list,omitempty"`
	PageToken            string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserDramaCollectionResp) Reset()         { *m = GetUserDramaCollectionResp{} }
func (m *GetUserDramaCollectionResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDramaCollectionResp) ProtoMessage()    {}
func (*GetUserDramaCollectionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{94}
}
func (m *GetUserDramaCollectionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDramaCollectionResp.Unmarshal(m, b)
}
func (m *GetUserDramaCollectionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDramaCollectionResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDramaCollectionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDramaCollectionResp.Merge(dst, src)
}
func (m *GetUserDramaCollectionResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDramaCollectionResp.Size(m)
}
func (m *GetUserDramaCollectionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDramaCollectionResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDramaCollectionResp proto.InternalMessageInfo

func (m *GetUserDramaCollectionResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserDramaCollectionResp) GetDramaList() []*DramaInfoWithStatus {
	if m != nil {
		return m.DramaList
	}
	return nil
}

func (m *GetUserDramaCollectionResp) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

type PiaChannelDramaPlayingType struct {
	SupportPlayingTypeRoles []PiaChannelDramaPlayingType_PlayingTypeRole `protobuf:"varint,1,rep,packed,name=support_playing_type_roles,json=supportPlayingTypeRoles,proto3,enum=ga.pia.PiaChannelDramaPlayingType_PlayingTypeRole" json:"support_playing_type_roles,omitempty"`
	SupportPlayingTypeTimes []PiaChannelDramaPlayingType_PlayingTypeTime `protobuf:"varint,2,rep,packed,name=support_playing_type_times,json=supportPlayingTypeTimes,proto3,enum=ga.pia.PiaChannelDramaPlayingType_PlayingTypeTime" json:"support_playing_type_times,omitempty"`
	CurrentPlayingTypeRole  PiaChannelDramaPlayingType_PlayingTypeRole   `protobuf:"varint,3,opt,name=current_playing_type_role,json=currentPlayingTypeRole,proto3,enum=ga.pia.PiaChannelDramaPlayingType_PlayingTypeRole" json:"current_playing_type_role,omitempty"`
	CurrentPlayingTypeTime  PiaChannelDramaPlayingType_PlayingTypeTime   `protobuf:"varint,4,opt,name=current_playing_type_time,json=currentPlayingTypeTime,proto3,enum=ga.pia.PiaChannelDramaPlayingType_PlayingTypeTime" json:"current_playing_type_time,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                                     `json:"-"`
	XXX_unrecognized        []byte                                       `json:"-"`
	XXX_sizecache           int32                                        `json:"-"`
}

func (m *PiaChannelDramaPlayingType) Reset()         { *m = PiaChannelDramaPlayingType{} }
func (m *PiaChannelDramaPlayingType) String() string { return proto.CompactTextString(m) }
func (*PiaChannelDramaPlayingType) ProtoMessage()    {}
func (*PiaChannelDramaPlayingType) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{95}
}
func (m *PiaChannelDramaPlayingType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaChannelDramaPlayingType.Unmarshal(m, b)
}
func (m *PiaChannelDramaPlayingType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaChannelDramaPlayingType.Marshal(b, m, deterministic)
}
func (dst *PiaChannelDramaPlayingType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaChannelDramaPlayingType.Merge(dst, src)
}
func (m *PiaChannelDramaPlayingType) XXX_Size() int {
	return xxx_messageInfo_PiaChannelDramaPlayingType.Size(m)
}
func (m *PiaChannelDramaPlayingType) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaChannelDramaPlayingType.DiscardUnknown(m)
}

var xxx_messageInfo_PiaChannelDramaPlayingType proto.InternalMessageInfo

func (m *PiaChannelDramaPlayingType) GetSupportPlayingTypeRoles() []PiaChannelDramaPlayingType_PlayingTypeRole {
	if m != nil {
		return m.SupportPlayingTypeRoles
	}
	return nil
}

func (m *PiaChannelDramaPlayingType) GetSupportPlayingTypeTimes() []PiaChannelDramaPlayingType_PlayingTypeTime {
	if m != nil {
		return m.SupportPlayingTypeTimes
	}
	return nil
}

func (m *PiaChannelDramaPlayingType) GetCurrentPlayingTypeRole() PiaChannelDramaPlayingType_PlayingTypeRole {
	if m != nil {
		return m.CurrentPlayingTypeRole
	}
	return PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_INVALID
}

func (m *PiaChannelDramaPlayingType) GetCurrentPlayingTypeTime() PiaChannelDramaPlayingType_PlayingTypeTime {
	if m != nil {
		return m.CurrentPlayingTypeTime
	}
	return PiaChannelDramaPlayingType_PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_INVALID
}

// PiaChangePlayType 切换玩法的推送
type PiaChangePlayType struct {
	ChannelId            uint32                      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Version              int64                       `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	Progress             *ChannelDramaProgress       `protobuf:"bytes,3,opt,name=progress,proto3" json:"progress,omitempty"`
	StartTime            int64                       `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	RoundId              int64                       `protobuf:"varint,5,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	PlayingType          *PiaChannelDramaPlayingType `protobuf:"bytes,6,opt,name=playing_type,json=playingType,proto3" json:"playing_type,omitempty"`
	MicRoleMap           *MicRoleMap                 `protobuf:"bytes,7,opt,name=mic_role_map,json=micRoleMap,proto3" json:"mic_role_map,omitempty"`
	BgmStatus            *DramaBgmStatus             `protobuf:"bytes,8,opt,name=bgm_status,json=bgmStatus,proto3" json:"bgm_status,omitempty"`
	BgmVolStatus         *DramaBgmVolStatus          `protobuf:"bytes,9,opt,name=bgm_vol_status,json=bgmVolStatus,proto3" json:"bgm_vol_status,omitempty"`
	DramaPhase           DramaPhase                  `protobuf:"varint,10,opt,name=drama_phase,json=dramaPhase,proto3,enum=ga.pia.DramaPhase" json:"drama_phase,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *PiaChangePlayType) Reset()         { *m = PiaChangePlayType{} }
func (m *PiaChangePlayType) String() string { return proto.CompactTextString(m) }
func (*PiaChangePlayType) ProtoMessage()    {}
func (*PiaChangePlayType) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{96}
}
func (m *PiaChangePlayType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaChangePlayType.Unmarshal(m, b)
}
func (m *PiaChangePlayType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaChangePlayType.Marshal(b, m, deterministic)
}
func (dst *PiaChangePlayType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaChangePlayType.Merge(dst, src)
}
func (m *PiaChangePlayType) XXX_Size() int {
	return xxx_messageInfo_PiaChangePlayType.Size(m)
}
func (m *PiaChangePlayType) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaChangePlayType.DiscardUnknown(m)
}

var xxx_messageInfo_PiaChangePlayType proto.InternalMessageInfo

func (m *PiaChangePlayType) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaChangePlayType) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *PiaChangePlayType) GetProgress() *ChannelDramaProgress {
	if m != nil {
		return m.Progress
	}
	return nil
}

func (m *PiaChangePlayType) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *PiaChangePlayType) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *PiaChangePlayType) GetPlayingType() *PiaChannelDramaPlayingType {
	if m != nil {
		return m.PlayingType
	}
	return nil
}

func (m *PiaChangePlayType) GetMicRoleMap() *MicRoleMap {
	if m != nil {
		return m.MicRoleMap
	}
	return nil
}

func (m *PiaChangePlayType) GetBgmStatus() *DramaBgmStatus {
	if m != nil {
		return m.BgmStatus
	}
	return nil
}

func (m *PiaChangePlayType) GetBgmVolStatus() *DramaBgmVolStatus {
	if m != nil {
		return m.BgmVolStatus
	}
	return nil
}

func (m *PiaChangePlayType) GetDramaPhase() DramaPhase {
	if m != nil {
		return m.DramaPhase
	}
	return DramaPhase_DRAMA_PHASE_UNSPECIFIED
}

// 剧本切换玩法请求
type PiaChangePlayTypeReq struct {
	BaseReq                 *app.BaseReq                `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelDramaPlayingType *PiaChannelDramaPlayingType `protobuf:"bytes,2,opt,name=channel_drama_playing_type,json=channelDramaPlayingType,proto3" json:"channel_drama_playing_type,omitempty"`
	ChannelId               uint32                      `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId                 int64                       `protobuf:"varint,4,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                    `json:"-"`
	XXX_unrecognized        []byte                      `json:"-"`
	XXX_sizecache           int32                       `json:"-"`
}

func (m *PiaChangePlayTypeReq) Reset()         { *m = PiaChangePlayTypeReq{} }
func (m *PiaChangePlayTypeReq) String() string { return proto.CompactTextString(m) }
func (*PiaChangePlayTypeReq) ProtoMessage()    {}
func (*PiaChangePlayTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{97}
}
func (m *PiaChangePlayTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaChangePlayTypeReq.Unmarshal(m, b)
}
func (m *PiaChangePlayTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaChangePlayTypeReq.Marshal(b, m, deterministic)
}
func (dst *PiaChangePlayTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaChangePlayTypeReq.Merge(dst, src)
}
func (m *PiaChangePlayTypeReq) XXX_Size() int {
	return xxx_messageInfo_PiaChangePlayTypeReq.Size(m)
}
func (m *PiaChangePlayTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaChangePlayTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaChangePlayTypeReq proto.InternalMessageInfo

func (m *PiaChangePlayTypeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaChangePlayTypeReq) GetChannelDramaPlayingType() *PiaChannelDramaPlayingType {
	if m != nil {
		return m.ChannelDramaPlayingType
	}
	return nil
}

func (m *PiaChangePlayTypeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaChangePlayTypeReq) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// 剧本切换玩法响应
type PiaChangePlayTypeResp struct {
	BaseResp                *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelDramaPlayingType *PiaChannelDramaPlayingType `protobuf:"bytes,2,opt,name=channel_drama_playing_type,json=channelDramaPlayingType,proto3" json:"channel_drama_playing_type,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                    `json:"-"`
	XXX_unrecognized        []byte                      `json:"-"`
	XXX_sizecache           int32                       `json:"-"`
}

func (m *PiaChangePlayTypeResp) Reset()         { *m = PiaChangePlayTypeResp{} }
func (m *PiaChangePlayTypeResp) String() string { return proto.CompactTextString(m) }
func (*PiaChangePlayTypeResp) ProtoMessage()    {}
func (*PiaChangePlayTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{98}
}
func (m *PiaChangePlayTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaChangePlayTypeResp.Unmarshal(m, b)
}
func (m *PiaChangePlayTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaChangePlayTypeResp.Marshal(b, m, deterministic)
}
func (dst *PiaChangePlayTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaChangePlayTypeResp.Merge(dst, src)
}
func (m *PiaChangePlayTypeResp) XXX_Size() int {
	return xxx_messageInfo_PiaChangePlayTypeResp.Size(m)
}
func (m *PiaChangePlayTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaChangePlayTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaChangePlayTypeResp proto.InternalMessageInfo

func (m *PiaChangePlayTypeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaChangePlayTypeResp) GetChannelDramaPlayingType() *PiaChannelDramaPlayingType {
	if m != nil {
		return m.ChannelDramaPlayingType
	}
	return nil
}

// 获取我的参演记录列表请求
type GetMyDramaPlayingRecordReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PageSize             uint32          `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken            string          `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	SearchOption         []*SearchOption `protobuf:"bytes,4,rep,name=search_option,json=searchOption,proto3" json:"search_option,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetMyDramaPlayingRecordReq) Reset()         { *m = GetMyDramaPlayingRecordReq{} }
func (m *GetMyDramaPlayingRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetMyDramaPlayingRecordReq) ProtoMessage()    {}
func (*GetMyDramaPlayingRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{99}
}
func (m *GetMyDramaPlayingRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyDramaPlayingRecordReq.Unmarshal(m, b)
}
func (m *GetMyDramaPlayingRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyDramaPlayingRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetMyDramaPlayingRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyDramaPlayingRecordReq.Merge(dst, src)
}
func (m *GetMyDramaPlayingRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetMyDramaPlayingRecordReq.Size(m)
}
func (m *GetMyDramaPlayingRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyDramaPlayingRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyDramaPlayingRecordReq proto.InternalMessageInfo

func (m *GetMyDramaPlayingRecordReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMyDramaPlayingRecordReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetMyDramaPlayingRecordReq) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *GetMyDramaPlayingRecordReq) GetSearchOption() []*SearchOption {
	if m != nil {
		return m.SearchOption
	}
	return nil
}

// 获取我的参演记录列表响应
type GetMyDramaPlayingRecordResp struct {
	BaseResp               *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaPlayingRecordList []*PiaDramaPlayingRecord `protobuf:"bytes,2,rep,name=drama_playing_record_list,json=dramaPlayingRecordList,proto3" json:"drama_playing_record_list,omitempty"`
	PageToken              string                   `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                 `json:"-"`
	XXX_unrecognized       []byte                   `json:"-"`
	XXX_sizecache          int32                    `json:"-"`
}

func (m *GetMyDramaPlayingRecordResp) Reset()         { *m = GetMyDramaPlayingRecordResp{} }
func (m *GetMyDramaPlayingRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetMyDramaPlayingRecordResp) ProtoMessage()    {}
func (*GetMyDramaPlayingRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{100}
}
func (m *GetMyDramaPlayingRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyDramaPlayingRecordResp.Unmarshal(m, b)
}
func (m *GetMyDramaPlayingRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyDramaPlayingRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetMyDramaPlayingRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyDramaPlayingRecordResp.Merge(dst, src)
}
func (m *GetMyDramaPlayingRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetMyDramaPlayingRecordResp.Size(m)
}
func (m *GetMyDramaPlayingRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyDramaPlayingRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyDramaPlayingRecordResp proto.InternalMessageInfo

func (m *GetMyDramaPlayingRecordResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMyDramaPlayingRecordResp) GetDramaPlayingRecordList() []*PiaDramaPlayingRecord {
	if m != nil {
		return m.DramaPlayingRecordList
	}
	return nil
}

func (m *GetMyDramaPlayingRecordResp) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

// 我的参演记录
type PiaDramaPlayingRecord struct {
	DramaSubInfo         *DramaSubInfo `protobuf:"bytes,1,opt,name=drama_sub_info,json=dramaSubInfo,proto3" json:"drama_sub_info,omitempty"`
	PlayingTime          int64         `protobuf:"varint,2,opt,name=playing_time,json=playingTime,proto3" json:"playing_time,omitempty"`
	IsPullOff            bool          `protobuf:"varint,3,opt,name=is_pull_off,json=isPullOff,proto3" json:"is_pull_off,omitempty"`
	Id                   string        `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
	IsCopy               bool          `protobuf:"varint,5,opt,name=is_copy,json=isCopy,proto3" json:"is_copy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaDramaPlayingRecord) Reset()         { *m = PiaDramaPlayingRecord{} }
func (m *PiaDramaPlayingRecord) String() string { return proto.CompactTextString(m) }
func (*PiaDramaPlayingRecord) ProtoMessage()    {}
func (*PiaDramaPlayingRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{101}
}
func (m *PiaDramaPlayingRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaDramaPlayingRecord.Unmarshal(m, b)
}
func (m *PiaDramaPlayingRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaDramaPlayingRecord.Marshal(b, m, deterministic)
}
func (dst *PiaDramaPlayingRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaDramaPlayingRecord.Merge(dst, src)
}
func (m *PiaDramaPlayingRecord) XXX_Size() int {
	return xxx_messageInfo_PiaDramaPlayingRecord.Size(m)
}
func (m *PiaDramaPlayingRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaDramaPlayingRecord.DiscardUnknown(m)
}

var xxx_messageInfo_PiaDramaPlayingRecord proto.InternalMessageInfo

func (m *PiaDramaPlayingRecord) GetDramaSubInfo() *DramaSubInfo {
	if m != nil {
		return m.DramaSubInfo
	}
	return nil
}

func (m *PiaDramaPlayingRecord) GetPlayingTime() int64 {
	if m != nil {
		return m.PlayingTime
	}
	return 0
}

func (m *PiaDramaPlayingRecord) GetIsPullOff() bool {
	if m != nil {
		return m.IsPullOff
	}
	return false
}

func (m *PiaDramaPlayingRecord) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PiaDramaPlayingRecord) GetIsCopy() bool {
	if m != nil {
		return m.IsCopy
	}
	return false
}

// 批量删除我的参演记录请求
type PiaBatchDeleteMyPlayingRecordReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RecordIdList         []string     `protobuf:"bytes,2,rep,name=record_id_list,json=recordIdList,proto3" json:"record_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaBatchDeleteMyPlayingRecordReq) Reset()         { *m = PiaBatchDeleteMyPlayingRecordReq{} }
func (m *PiaBatchDeleteMyPlayingRecordReq) String() string { return proto.CompactTextString(m) }
func (*PiaBatchDeleteMyPlayingRecordReq) ProtoMessage()    {}
func (*PiaBatchDeleteMyPlayingRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{102}
}
func (m *PiaBatchDeleteMyPlayingRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaBatchDeleteMyPlayingRecordReq.Unmarshal(m, b)
}
func (m *PiaBatchDeleteMyPlayingRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaBatchDeleteMyPlayingRecordReq.Marshal(b, m, deterministic)
}
func (dst *PiaBatchDeleteMyPlayingRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaBatchDeleteMyPlayingRecordReq.Merge(dst, src)
}
func (m *PiaBatchDeleteMyPlayingRecordReq) XXX_Size() int {
	return xxx_messageInfo_PiaBatchDeleteMyPlayingRecordReq.Size(m)
}
func (m *PiaBatchDeleteMyPlayingRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaBatchDeleteMyPlayingRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaBatchDeleteMyPlayingRecordReq proto.InternalMessageInfo

func (m *PiaBatchDeleteMyPlayingRecordReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaBatchDeleteMyPlayingRecordReq) GetRecordIdList() []string {
	if m != nil {
		return m.RecordIdList
	}
	return nil
}

// 批量删除我的参演记录响应
type PiaBatchDeleteMyPlayingRecordResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaBatchDeleteMyPlayingRecordResp) Reset()         { *m = PiaBatchDeleteMyPlayingRecordResp{} }
func (m *PiaBatchDeleteMyPlayingRecordResp) String() string { return proto.CompactTextString(m) }
func (*PiaBatchDeleteMyPlayingRecordResp) ProtoMessage()    {}
func (*PiaBatchDeleteMyPlayingRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{103}
}
func (m *PiaBatchDeleteMyPlayingRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaBatchDeleteMyPlayingRecordResp.Unmarshal(m, b)
}
func (m *PiaBatchDeleteMyPlayingRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaBatchDeleteMyPlayingRecordResp.Marshal(b, m, deterministic)
}
func (dst *PiaBatchDeleteMyPlayingRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaBatchDeleteMyPlayingRecordResp.Merge(dst, src)
}
func (m *PiaBatchDeleteMyPlayingRecordResp) XXX_Size() int {
	return xxx_messageInfo_PiaBatchDeleteMyPlayingRecordResp.Size(m)
}
func (m *PiaBatchDeleteMyPlayingRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaBatchDeleteMyPlayingRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaBatchDeleteMyPlayingRecordResp proto.InternalMessageInfo

func (m *PiaBatchDeleteMyPlayingRecordResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 根据筛选条件获取所有相关参演记录id列表
type PiaGetMyPlayingRecordIdListReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SearchOption         []*SearchOption `protobuf:"bytes,2,rep,name=search_option,json=searchOption,proto3" json:"search_option,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PiaGetMyPlayingRecordIdListReq) Reset()         { *m = PiaGetMyPlayingRecordIdListReq{} }
func (m *PiaGetMyPlayingRecordIdListReq) String() string { return proto.CompactTextString(m) }
func (*PiaGetMyPlayingRecordIdListReq) ProtoMessage()    {}
func (*PiaGetMyPlayingRecordIdListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{104}
}
func (m *PiaGetMyPlayingRecordIdListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetMyPlayingRecordIdListReq.Unmarshal(m, b)
}
func (m *PiaGetMyPlayingRecordIdListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetMyPlayingRecordIdListReq.Marshal(b, m, deterministic)
}
func (dst *PiaGetMyPlayingRecordIdListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetMyPlayingRecordIdListReq.Merge(dst, src)
}
func (m *PiaGetMyPlayingRecordIdListReq) XXX_Size() int {
	return xxx_messageInfo_PiaGetMyPlayingRecordIdListReq.Size(m)
}
func (m *PiaGetMyPlayingRecordIdListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetMyPlayingRecordIdListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetMyPlayingRecordIdListReq proto.InternalMessageInfo

func (m *PiaGetMyPlayingRecordIdListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaGetMyPlayingRecordIdListReq) GetSearchOption() []*SearchOption {
	if m != nil {
		return m.SearchOption
	}
	return nil
}

// 根据筛选条件获取所有相关参演记录id列表响应
type PiaGetMyPlayingRecordIdListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RecordIdList         []string      `protobuf:"bytes,2,rep,name=record_id_list,json=recordIdList,proto3" json:"record_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaGetMyPlayingRecordIdListResp) Reset()         { *m = PiaGetMyPlayingRecordIdListResp{} }
func (m *PiaGetMyPlayingRecordIdListResp) String() string { return proto.CompactTextString(m) }
func (*PiaGetMyPlayingRecordIdListResp) ProtoMessage()    {}
func (*PiaGetMyPlayingRecordIdListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{105}
}
func (m *PiaGetMyPlayingRecordIdListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetMyPlayingRecordIdListResp.Unmarshal(m, b)
}
func (m *PiaGetMyPlayingRecordIdListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetMyPlayingRecordIdListResp.Marshal(b, m, deterministic)
}
func (dst *PiaGetMyPlayingRecordIdListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetMyPlayingRecordIdListResp.Merge(dst, src)
}
func (m *PiaGetMyPlayingRecordIdListResp) XXX_Size() int {
	return xxx_messageInfo_PiaGetMyPlayingRecordIdListResp.Size(m)
}
func (m *PiaGetMyPlayingRecordIdListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetMyPlayingRecordIdListResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetMyPlayingRecordIdListResp proto.InternalMessageInfo

func (m *PiaGetMyPlayingRecordIdListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaGetMyPlayingRecordIdListResp) GetRecordIdList() []string {
	if m != nil {
		return m.RecordIdList
	}
	return nil
}

type PiaAuthorGenInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	AuthorId             uint32       `protobuf:"varint,2,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaAuthorGenInfoReq) Reset()         { *m = PiaAuthorGenInfoReq{} }
func (m *PiaAuthorGenInfoReq) String() string { return proto.CompactTextString(m) }
func (*PiaAuthorGenInfoReq) ProtoMessage()    {}
func (*PiaAuthorGenInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{106}
}
func (m *PiaAuthorGenInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaAuthorGenInfoReq.Unmarshal(m, b)
}
func (m *PiaAuthorGenInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaAuthorGenInfoReq.Marshal(b, m, deterministic)
}
func (dst *PiaAuthorGenInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaAuthorGenInfoReq.Merge(dst, src)
}
func (m *PiaAuthorGenInfoReq) XXX_Size() int {
	return xxx_messageInfo_PiaAuthorGenInfoReq.Size(m)
}
func (m *PiaAuthorGenInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaAuthorGenInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaAuthorGenInfoReq proto.InternalMessageInfo

func (m *PiaAuthorGenInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaAuthorGenInfoReq) GetAuthorId() uint32 {
	if m != nil {
		return m.AuthorId
	}
	return 0
}

type PiaAuthorGenInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	AuthorId             uint32        `protobuf:"varint,2,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	Nickname             string        `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AuthorBindingInfo    *PiaUserInfo  `protobuf:"bytes,4,opt,name=author_binding_info,json=authorBindingInfo,proto3" json:"author_binding_info,omitempty"`
	DramaCount           uint32        `protobuf:"varint,5,opt,name=drama_count,json=dramaCount,proto3" json:"drama_count,omitempty"`
	CollectedCount       uint32        `protobuf:"varint,6,opt,name=collected_count,json=collectedCount,proto3" json:"collected_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaAuthorGenInfoResp) Reset()         { *m = PiaAuthorGenInfoResp{} }
func (m *PiaAuthorGenInfoResp) String() string { return proto.CompactTextString(m) }
func (*PiaAuthorGenInfoResp) ProtoMessage()    {}
func (*PiaAuthorGenInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{107}
}
func (m *PiaAuthorGenInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaAuthorGenInfoResp.Unmarshal(m, b)
}
func (m *PiaAuthorGenInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaAuthorGenInfoResp.Marshal(b, m, deterministic)
}
func (dst *PiaAuthorGenInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaAuthorGenInfoResp.Merge(dst, src)
}
func (m *PiaAuthorGenInfoResp) XXX_Size() int {
	return xxx_messageInfo_PiaAuthorGenInfoResp.Size(m)
}
func (m *PiaAuthorGenInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaAuthorGenInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaAuthorGenInfoResp proto.InternalMessageInfo

func (m *PiaAuthorGenInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaAuthorGenInfoResp) GetAuthorId() uint32 {
	if m != nil {
		return m.AuthorId
	}
	return 0
}

func (m *PiaAuthorGenInfoResp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PiaAuthorGenInfoResp) GetAuthorBindingInfo() *PiaUserInfo {
	if m != nil {
		return m.AuthorBindingInfo
	}
	return nil
}

func (m *PiaAuthorGenInfoResp) GetDramaCount() uint32 {
	if m != nil {
		return m.DramaCount
	}
	return 0
}

func (m *PiaAuthorGenInfoResp) GetCollectedCount() uint32 {
	if m != nil {
		return m.CollectedCount
	}
	return 0
}

type PiaAuthorWorksListReq struct {
	BaseReq              *app.BaseReq               `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PageToken            string                     `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32                     `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	AuthorId             uint32                     `protobuf:"varint,4,opt,name=author_id,json=authorId,proto3" json:"author_id,omitempty"`
	SortType             PiaAuthorWorksListSortType `protobuf:"varint,5,opt,name=sort_type,json=sortType,proto3,enum=ga.pia.PiaAuthorWorksListSortType" json:"sort_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PiaAuthorWorksListReq) Reset()         { *m = PiaAuthorWorksListReq{} }
func (m *PiaAuthorWorksListReq) String() string { return proto.CompactTextString(m) }
func (*PiaAuthorWorksListReq) ProtoMessage()    {}
func (*PiaAuthorWorksListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{108}
}
func (m *PiaAuthorWorksListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaAuthorWorksListReq.Unmarshal(m, b)
}
func (m *PiaAuthorWorksListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaAuthorWorksListReq.Marshal(b, m, deterministic)
}
func (dst *PiaAuthorWorksListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaAuthorWorksListReq.Merge(dst, src)
}
func (m *PiaAuthorWorksListReq) XXX_Size() int {
	return xxx_messageInfo_PiaAuthorWorksListReq.Size(m)
}
func (m *PiaAuthorWorksListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaAuthorWorksListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaAuthorWorksListReq proto.InternalMessageInfo

func (m *PiaAuthorWorksListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaAuthorWorksListReq) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *PiaAuthorWorksListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *PiaAuthorWorksListReq) GetAuthorId() uint32 {
	if m != nil {
		return m.AuthorId
	}
	return 0
}

func (m *PiaAuthorWorksListReq) GetSortType() PiaAuthorWorksListSortType {
	if m != nil {
		return m.SortType
	}
	return PiaAuthorWorksListSortType_PIA_AUTHOR_WORKS_LIST_SORT_TYPE_INVALID
}

type PiaAuthorWorksListResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaList            []*DramaInfoWithStatus `protobuf:"bytes,2,rep,name=drama_list,json=dramaList,proto3" json:"drama_list,omitempty"`
	NextPageToken        string                 `protobuf:"bytes,3,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *PiaAuthorWorksListResp) Reset()         { *m = PiaAuthorWorksListResp{} }
func (m *PiaAuthorWorksListResp) String() string { return proto.CompactTextString(m) }
func (*PiaAuthorWorksListResp) ProtoMessage()    {}
func (*PiaAuthorWorksListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{109}
}
func (m *PiaAuthorWorksListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaAuthorWorksListResp.Unmarshal(m, b)
}
func (m *PiaAuthorWorksListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaAuthorWorksListResp.Marshal(b, m, deterministic)
}
func (dst *PiaAuthorWorksListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaAuthorWorksListResp.Merge(dst, src)
}
func (m *PiaAuthorWorksListResp) XXX_Size() int {
	return xxx_messageInfo_PiaAuthorWorksListResp.Size(m)
}
func (m *PiaAuthorWorksListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaAuthorWorksListResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaAuthorWorksListResp proto.InternalMessageInfo

func (m *PiaAuthorWorksListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaAuthorWorksListResp) GetDramaList() []*DramaInfoWithStatus {
	if m != nil {
		return m.DramaList
	}
	return nil
}

func (m *PiaAuthorWorksListResp) GetNextPageToken() string {
	if m != nil {
		return m.NextPageToken
	}
	return ""
}

type PiaDramaCreatorInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaDramaCreatorInfo) Reset()         { *m = PiaDramaCreatorInfo{} }
func (m *PiaDramaCreatorInfo) String() string { return proto.CompactTextString(m) }
func (*PiaDramaCreatorInfo) ProtoMessage()    {}
func (*PiaDramaCreatorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{110}
}
func (m *PiaDramaCreatorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaDramaCreatorInfo.Unmarshal(m, b)
}
func (m *PiaDramaCreatorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaDramaCreatorInfo.Marshal(b, m, deterministic)
}
func (dst *PiaDramaCreatorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaDramaCreatorInfo.Merge(dst, src)
}
func (m *PiaDramaCreatorInfo) XXX_Size() int {
	return xxx_messageInfo_PiaDramaCreatorInfo.Size(m)
}
func (m *PiaDramaCreatorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaDramaCreatorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PiaDramaCreatorInfo proto.InternalMessageInfo

func (m *PiaDramaCreatorInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PiaDramaCreatorInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PiaDramaCreatorInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type PiaCopyDramaInfoWithStatus struct {
	DramaSubInfo         *DramaSubInfo        `protobuf:"bytes,1,opt,name=drama_sub_info,json=dramaSubInfo,proto3" json:"drama_sub_info,omitempty"`
	CreatorInfo          *PiaDramaCreatorInfo `protobuf:"bytes,2,opt,name=creator_info,json=creatorInfo,proto3" json:"creator_info,omitempty"`
	IsPublic             bool                 `protobuf:"varint,3,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	IsDelete             bool                 `protobuf:"varint,4,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	UseCount             uint32               `protobuf:"varint,5,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *PiaCopyDramaInfoWithStatus) Reset()         { *m = PiaCopyDramaInfoWithStatus{} }
func (m *PiaCopyDramaInfoWithStatus) String() string { return proto.CompactTextString(m) }
func (*PiaCopyDramaInfoWithStatus) ProtoMessage()    {}
func (*PiaCopyDramaInfoWithStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{111}
}
func (m *PiaCopyDramaInfoWithStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCopyDramaInfoWithStatus.Unmarshal(m, b)
}
func (m *PiaCopyDramaInfoWithStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCopyDramaInfoWithStatus.Marshal(b, m, deterministic)
}
func (dst *PiaCopyDramaInfoWithStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCopyDramaInfoWithStatus.Merge(dst, src)
}
func (m *PiaCopyDramaInfoWithStatus) XXX_Size() int {
	return xxx_messageInfo_PiaCopyDramaInfoWithStatus.Size(m)
}
func (m *PiaCopyDramaInfoWithStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCopyDramaInfoWithStatus.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCopyDramaInfoWithStatus proto.InternalMessageInfo

func (m *PiaCopyDramaInfoWithStatus) GetDramaSubInfo() *DramaSubInfo {
	if m != nil {
		return m.DramaSubInfo
	}
	return nil
}

func (m *PiaCopyDramaInfoWithStatus) GetCreatorInfo() *PiaDramaCreatorInfo {
	if m != nil {
		return m.CreatorInfo
	}
	return nil
}

func (m *PiaCopyDramaInfoWithStatus) GetIsPublic() bool {
	if m != nil {
		return m.IsPublic
	}
	return false
}

func (m *PiaCopyDramaInfoWithStatus) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *PiaCopyDramaInfoWithStatus) GetUseCount() uint32 {
	if m != nil {
		return m.UseCount
	}
	return 0
}

// 具体某个副本的副本库列表
type PiaCopyDramaListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PageToken            string       `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32       `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	DramaId              uint32       `protobuf:"varint,4,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	IsPublic             bool         `protobuf:"varint,6,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	ExcludeUid           uint32       `protobuf:"varint,7,opt,name=exclude_uid,json=excludeUid,proto3" json:"exclude_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaCopyDramaListReq) Reset()         { *m = PiaCopyDramaListReq{} }
func (m *PiaCopyDramaListReq) String() string { return proto.CompactTextString(m) }
func (*PiaCopyDramaListReq) ProtoMessage()    {}
func (*PiaCopyDramaListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{112}
}
func (m *PiaCopyDramaListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCopyDramaListReq.Unmarshal(m, b)
}
func (m *PiaCopyDramaListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCopyDramaListReq.Marshal(b, m, deterministic)
}
func (dst *PiaCopyDramaListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCopyDramaListReq.Merge(dst, src)
}
func (m *PiaCopyDramaListReq) XXX_Size() int {
	return xxx_messageInfo_PiaCopyDramaListReq.Size(m)
}
func (m *PiaCopyDramaListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCopyDramaListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCopyDramaListReq proto.InternalMessageInfo

func (m *PiaCopyDramaListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaCopyDramaListReq) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *PiaCopyDramaListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *PiaCopyDramaListReq) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

func (m *PiaCopyDramaListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PiaCopyDramaListReq) GetIsPublic() bool {
	if m != nil {
		return m.IsPublic
	}
	return false
}

func (m *PiaCopyDramaListReq) GetExcludeUid() uint32 {
	if m != nil {
		return m.ExcludeUid
	}
	return 0
}

type PiaCopyDramaListResp struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CopyDramaList        []*PiaCopyDramaInfoWithStatus `protobuf:"bytes,2,rep,name=copy_drama_list,json=copyDramaList,proto3" json:"copy_drama_list,omitempty"`
	NextPageToken        string                        `protobuf:"bytes,3,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *PiaCopyDramaListResp) Reset()         { *m = PiaCopyDramaListResp{} }
func (m *PiaCopyDramaListResp) String() string { return proto.CompactTextString(m) }
func (*PiaCopyDramaListResp) ProtoMessage()    {}
func (*PiaCopyDramaListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{113}
}
func (m *PiaCopyDramaListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCopyDramaListResp.Unmarshal(m, b)
}
func (m *PiaCopyDramaListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCopyDramaListResp.Marshal(b, m, deterministic)
}
func (dst *PiaCopyDramaListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCopyDramaListResp.Merge(dst, src)
}
func (m *PiaCopyDramaListResp) XXX_Size() int {
	return xxx_messageInfo_PiaCopyDramaListResp.Size(m)
}
func (m *PiaCopyDramaListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCopyDramaListResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCopyDramaListResp proto.InternalMessageInfo

func (m *PiaCopyDramaListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaCopyDramaListResp) GetCopyDramaList() []*PiaCopyDramaInfoWithStatus {
	if m != nil {
		return m.CopyDramaList
	}
	return nil
}

func (m *PiaCopyDramaListResp) GetNextPageToken() string {
	if m != nil {
		return m.NextPageToken
	}
	return ""
}

// 获取我的副本库列表，带搜索条件
type GetMyDramaCopyListReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SearchOption         []*SearchOption `protobuf:"bytes,2,rep,name=search_option,json=searchOption,proto3" json:"search_option,omitempty"`
	PageToken            string          `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32          `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetMyDramaCopyListReq) Reset()         { *m = GetMyDramaCopyListReq{} }
func (m *GetMyDramaCopyListReq) String() string { return proto.CompactTextString(m) }
func (*GetMyDramaCopyListReq) ProtoMessage()    {}
func (*GetMyDramaCopyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{114}
}
func (m *GetMyDramaCopyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyDramaCopyListReq.Unmarshal(m, b)
}
func (m *GetMyDramaCopyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyDramaCopyListReq.Marshal(b, m, deterministic)
}
func (dst *GetMyDramaCopyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyDramaCopyListReq.Merge(dst, src)
}
func (m *GetMyDramaCopyListReq) XXX_Size() int {
	return xxx_messageInfo_GetMyDramaCopyListReq.Size(m)
}
func (m *GetMyDramaCopyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyDramaCopyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyDramaCopyListReq proto.InternalMessageInfo

func (m *GetMyDramaCopyListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMyDramaCopyListReq) GetSearchOption() []*SearchOption {
	if m != nil {
		return m.SearchOption
	}
	return nil
}

func (m *GetMyDramaCopyListReq) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *GetMyDramaCopyListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type PiaCopyDramaItem struct {
	DramaSubInfo         *DramaSubInfo `protobuf:"bytes,1,opt,name=drama_sub_info,json=dramaSubInfo,proto3" json:"drama_sub_info,omitempty"`
	IsPublic             bool          `protobuf:"varint,2,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	IsPullOff            bool          `protobuf:"varint,3,opt,name=is_pull_off,json=isPullOff,proto3" json:"is_pull_off,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaCopyDramaItem) Reset()         { *m = PiaCopyDramaItem{} }
func (m *PiaCopyDramaItem) String() string { return proto.CompactTextString(m) }
func (*PiaCopyDramaItem) ProtoMessage()    {}
func (*PiaCopyDramaItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{115}
}
func (m *PiaCopyDramaItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCopyDramaItem.Unmarshal(m, b)
}
func (m *PiaCopyDramaItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCopyDramaItem.Marshal(b, m, deterministic)
}
func (dst *PiaCopyDramaItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCopyDramaItem.Merge(dst, src)
}
func (m *PiaCopyDramaItem) XXX_Size() int {
	return xxx_messageInfo_PiaCopyDramaItem.Size(m)
}
func (m *PiaCopyDramaItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCopyDramaItem.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCopyDramaItem proto.InternalMessageInfo

func (m *PiaCopyDramaItem) GetDramaSubInfo() *DramaSubInfo {
	if m != nil {
		return m.DramaSubInfo
	}
	return nil
}

func (m *PiaCopyDramaItem) GetIsPublic() bool {
	if m != nil {
		return m.IsPublic
	}
	return false
}

func (m *PiaCopyDramaItem) GetIsPullOff() bool {
	if m != nil {
		return m.IsPullOff
	}
	return false
}

// 获取我的副本库列表 响应
type GetMyDramaCopyListResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CopyDramaItemList    []*PiaCopyDramaItem `protobuf:"bytes,2,rep,name=copy_drama_item_list,json=copyDramaItemList,proto3" json:"copy_drama_item_list,omitempty"`
	NextPageToken        string              `protobuf:"bytes,3,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetMyDramaCopyListResp) Reset()         { *m = GetMyDramaCopyListResp{} }
func (m *GetMyDramaCopyListResp) String() string { return proto.CompactTextString(m) }
func (*GetMyDramaCopyListResp) ProtoMessage()    {}
func (*GetMyDramaCopyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{116}
}
func (m *GetMyDramaCopyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyDramaCopyListResp.Unmarshal(m, b)
}
func (m *GetMyDramaCopyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyDramaCopyListResp.Marshal(b, m, deterministic)
}
func (dst *GetMyDramaCopyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyDramaCopyListResp.Merge(dst, src)
}
func (m *GetMyDramaCopyListResp) XXX_Size() int {
	return xxx_messageInfo_GetMyDramaCopyListResp.Size(m)
}
func (m *GetMyDramaCopyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyDramaCopyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyDramaCopyListResp proto.InternalMessageInfo

func (m *GetMyDramaCopyListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMyDramaCopyListResp) GetCopyDramaItemList() []*PiaCopyDramaItem {
	if m != nil {
		return m.CopyDramaItemList
	}
	return nil
}

func (m *GetMyDramaCopyListResp) GetNextPageToken() string {
	if m != nil {
		return m.NextPageToken
	}
	return ""
}

// 副本状态管理
type SetDramaCopyStatusReq struct {
	BaseReq              *app.BaseReq                          `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Id                   uint32                                `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	DramaCopyStatus      SetDramaCopyStatusReq_DramaCopyStatus `protobuf:"varint,3,opt,name=drama_copy_status,json=dramaCopyStatus,proto3,enum=ga.pia.SetDramaCopyStatusReq_DramaCopyStatus" json:"drama_copy_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *SetDramaCopyStatusReq) Reset()         { *m = SetDramaCopyStatusReq{} }
func (m *SetDramaCopyStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetDramaCopyStatusReq) ProtoMessage()    {}
func (*SetDramaCopyStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{117}
}
func (m *SetDramaCopyStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDramaCopyStatusReq.Unmarshal(m, b)
}
func (m *SetDramaCopyStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDramaCopyStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetDramaCopyStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDramaCopyStatusReq.Merge(dst, src)
}
func (m *SetDramaCopyStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetDramaCopyStatusReq.Size(m)
}
func (m *SetDramaCopyStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDramaCopyStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDramaCopyStatusReq proto.InternalMessageInfo

func (m *SetDramaCopyStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetDramaCopyStatusReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SetDramaCopyStatusReq) GetDramaCopyStatus() SetDramaCopyStatusReq_DramaCopyStatus {
	if m != nil {
		return m.DramaCopyStatus
	}
	return SetDramaCopyStatusReq_DRAMA_COPY_STATUS_INVALID
}

// 副本状态管理resp
type SetDramaCopyStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetDramaCopyStatusResp) Reset()         { *m = SetDramaCopyStatusResp{} }
func (m *SetDramaCopyStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetDramaCopyStatusResp) ProtoMessage()    {}
func (*SetDramaCopyStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{118}
}
func (m *SetDramaCopyStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDramaCopyStatusResp.Unmarshal(m, b)
}
func (m *SetDramaCopyStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDramaCopyStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetDramaCopyStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDramaCopyStatusResp.Merge(dst, src)
}
func (m *SetDramaCopyStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetDramaCopyStatusResp.Size(m)
}
func (m *SetDramaCopyStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDramaCopyStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDramaCopyStatusResp proto.InternalMessageInfo

func (m *SetDramaCopyStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 删除副本req
type DeleteDramaCopyReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	IdList               []uint32     `protobuf:"varint,2,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DeleteDramaCopyReq) Reset()         { *m = DeleteDramaCopyReq{} }
func (m *DeleteDramaCopyReq) String() string { return proto.CompactTextString(m) }
func (*DeleteDramaCopyReq) ProtoMessage()    {}
func (*DeleteDramaCopyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{119}
}
func (m *DeleteDramaCopyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteDramaCopyReq.Unmarshal(m, b)
}
func (m *DeleteDramaCopyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteDramaCopyReq.Marshal(b, m, deterministic)
}
func (dst *DeleteDramaCopyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteDramaCopyReq.Merge(dst, src)
}
func (m *DeleteDramaCopyReq) XXX_Size() int {
	return xxx_messageInfo_DeleteDramaCopyReq.Size(m)
}
func (m *DeleteDramaCopyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteDramaCopyReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteDramaCopyReq proto.InternalMessageInfo

func (m *DeleteDramaCopyReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DeleteDramaCopyReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

// 删除副本resp
type DeleteDramaCopyResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DeleteDramaCopyResp) Reset()         { *m = DeleteDramaCopyResp{} }
func (m *DeleteDramaCopyResp) String() string { return proto.CompactTextString(m) }
func (*DeleteDramaCopyResp) ProtoMessage()    {}
func (*DeleteDramaCopyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{120}
}
func (m *DeleteDramaCopyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteDramaCopyResp.Unmarshal(m, b)
}
func (m *DeleteDramaCopyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteDramaCopyResp.Marshal(b, m, deterministic)
}
func (dst *DeleteDramaCopyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteDramaCopyResp.Merge(dst, src)
}
func (m *DeleteDramaCopyResp) XXX_Size() int {
	return xxx_messageInfo_DeleteDramaCopyResp.Size(m)
}
func (m *DeleteDramaCopyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteDramaCopyResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteDramaCopyResp proto.InternalMessageInfo

func (m *DeleteDramaCopyResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type PiaConfirmCoverCopyDramaReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ConfirmCopyDramaId   string       `protobuf:"bytes,2,opt,name=confirm_copy_drama_id,json=confirmCopyDramaId,proto3" json:"confirm_copy_drama_id,omitempty"`
	CoveredCopyDramaId   uint32       `protobuf:"varint,3,opt,name=covered_copy_drama_id,json=coveredCopyDramaId,proto3" json:"covered_copy_drama_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaConfirmCoverCopyDramaReq) Reset()         { *m = PiaConfirmCoverCopyDramaReq{} }
func (m *PiaConfirmCoverCopyDramaReq) String() string { return proto.CompactTextString(m) }
func (*PiaConfirmCoverCopyDramaReq) ProtoMessage()    {}
func (*PiaConfirmCoverCopyDramaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{121}
}
func (m *PiaConfirmCoverCopyDramaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaConfirmCoverCopyDramaReq.Unmarshal(m, b)
}
func (m *PiaConfirmCoverCopyDramaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaConfirmCoverCopyDramaReq.Marshal(b, m, deterministic)
}
func (dst *PiaConfirmCoverCopyDramaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaConfirmCoverCopyDramaReq.Merge(dst, src)
}
func (m *PiaConfirmCoverCopyDramaReq) XXX_Size() int {
	return xxx_messageInfo_PiaConfirmCoverCopyDramaReq.Size(m)
}
func (m *PiaConfirmCoverCopyDramaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaConfirmCoverCopyDramaReq.DiscardUnknown(m)
}

var xxx_messageInfo_PiaConfirmCoverCopyDramaReq proto.InternalMessageInfo

func (m *PiaConfirmCoverCopyDramaReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaConfirmCoverCopyDramaReq) GetConfirmCopyDramaId() string {
	if m != nil {
		return m.ConfirmCopyDramaId
	}
	return ""
}

func (m *PiaConfirmCoverCopyDramaReq) GetCoveredCopyDramaId() uint32 {
	if m != nil {
		return m.CoveredCopyDramaId
	}
	return 0
}

type PiaConfirmCoverCopyDramaResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaConfirmCoverCopyDramaResp) Reset()         { *m = PiaConfirmCoverCopyDramaResp{} }
func (m *PiaConfirmCoverCopyDramaResp) String() string { return proto.CompactTextString(m) }
func (*PiaConfirmCoverCopyDramaResp) ProtoMessage()    {}
func (*PiaConfirmCoverCopyDramaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{122}
}
func (m *PiaConfirmCoverCopyDramaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaConfirmCoverCopyDramaResp.Unmarshal(m, b)
}
func (m *PiaConfirmCoverCopyDramaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaConfirmCoverCopyDramaResp.Marshal(b, m, deterministic)
}
func (dst *PiaConfirmCoverCopyDramaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaConfirmCoverCopyDramaResp.Merge(dst, src)
}
func (m *PiaConfirmCoverCopyDramaResp) XXX_Size() int {
	return xxx_messageInfo_PiaConfirmCoverCopyDramaResp.Size(m)
}
func (m *PiaConfirmCoverCopyDramaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaConfirmCoverCopyDramaResp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaConfirmCoverCopyDramaResp proto.InternalMessageInfo

func (m *PiaConfirmCoverCopyDramaResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 生成剧本副本请求，如果剧本还未结束，则用当前时间结束剧本。
type PiaCreateDramaCopyV2Req struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64        `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	IsPublic             bool         `protobuf:"varint,4,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaCreateDramaCopyV2Req) Reset()         { *m = PiaCreateDramaCopyV2Req{} }
func (m *PiaCreateDramaCopyV2Req) String() string { return proto.CompactTextString(m) }
func (*PiaCreateDramaCopyV2Req) ProtoMessage()    {}
func (*PiaCreateDramaCopyV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{123}
}
func (m *PiaCreateDramaCopyV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCreateDramaCopyV2Req.Unmarshal(m, b)
}
func (m *PiaCreateDramaCopyV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCreateDramaCopyV2Req.Marshal(b, m, deterministic)
}
func (dst *PiaCreateDramaCopyV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCreateDramaCopyV2Req.Merge(dst, src)
}
func (m *PiaCreateDramaCopyV2Req) XXX_Size() int {
	return xxx_messageInfo_PiaCreateDramaCopyV2Req.Size(m)
}
func (m *PiaCreateDramaCopyV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCreateDramaCopyV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCreateDramaCopyV2Req proto.InternalMessageInfo

func (m *PiaCreateDramaCopyV2Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaCreateDramaCopyV2Req) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaCreateDramaCopyV2Req) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *PiaCreateDramaCopyV2Req) GetIsPublic() bool {
	if m != nil {
		return m.IsPublic
	}
	return false
}

// 生成剧本副本响应
type PiaCreateDramaCopyV2Resp struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CopyDramaId          uint32                        `protobuf:"varint,2,opt,name=copy_drama_id,json=copyDramaId,proto3" json:"copy_drama_id,omitempty"`
	IsOverSingleLimit    bool                          `protobuf:"varint,3,opt,name=is_over_single_limit,json=isOverSingleLimit,proto3" json:"is_over_single_limit,omitempty"`
	DramaList            []*PiaCopyDramaInfoWithStatus `protobuf:"bytes,4,rep,name=drama_list,json=dramaList,proto3" json:"drama_list,omitempty"`
	ConfirmCopyDramaId   string                        `protobuf:"bytes,5,opt,name=confirm_copy_drama_id,json=confirmCopyDramaId,proto3" json:"confirm_copy_drama_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *PiaCreateDramaCopyV2Resp) Reset()         { *m = PiaCreateDramaCopyV2Resp{} }
func (m *PiaCreateDramaCopyV2Resp) String() string { return proto.CompactTextString(m) }
func (*PiaCreateDramaCopyV2Resp) ProtoMessage()    {}
func (*PiaCreateDramaCopyV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{124}
}
func (m *PiaCreateDramaCopyV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaCreateDramaCopyV2Resp.Unmarshal(m, b)
}
func (m *PiaCreateDramaCopyV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaCreateDramaCopyV2Resp.Marshal(b, m, deterministic)
}
func (dst *PiaCreateDramaCopyV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaCreateDramaCopyV2Resp.Merge(dst, src)
}
func (m *PiaCreateDramaCopyV2Resp) XXX_Size() int {
	return xxx_messageInfo_PiaCreateDramaCopyV2Resp.Size(m)
}
func (m *PiaCreateDramaCopyV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaCreateDramaCopyV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_PiaCreateDramaCopyV2Resp proto.InternalMessageInfo

func (m *PiaCreateDramaCopyV2Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaCreateDramaCopyV2Resp) GetCopyDramaId() uint32 {
	if m != nil {
		return m.CopyDramaId
	}
	return 0
}

func (m *PiaCreateDramaCopyV2Resp) GetIsOverSingleLimit() bool {
	if m != nil {
		return m.IsOverSingleLimit
	}
	return false
}

func (m *PiaCreateDramaCopyV2Resp) GetDramaList() []*PiaCopyDramaInfoWithStatus {
	if m != nil {
		return m.DramaList
	}
	return nil
}

func (m *PiaCreateDramaCopyV2Resp) GetConfirmCopyDramaId() string {
	if m != nil {
		return m.ConfirmCopyDramaId
	}
	return ""
}

type PiaPerformDramaRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DramaId              uint32       `protobuf:"varint,4,opt,name=drama_id,json=dramaId,proto3" json:"drama_id,omitempty"`
	IndexId              int64        `protobuf:"varint,5,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	IsCopyDrama          bool         `protobuf:"varint,6,opt,name=is_copy_drama,json=isCopyDrama,proto3" json:"is_copy_drama,omitempty"`
	RoundId              int64        `protobuf:"varint,7,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaPerformDramaRequest) Reset()         { *m = PiaPerformDramaRequest{} }
func (m *PiaPerformDramaRequest) String() string { return proto.CompactTextString(m) }
func (*PiaPerformDramaRequest) ProtoMessage()    {}
func (*PiaPerformDramaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{125}
}
func (m *PiaPerformDramaRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaPerformDramaRequest.Unmarshal(m, b)
}
func (m *PiaPerformDramaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaPerformDramaRequest.Marshal(b, m, deterministic)
}
func (dst *PiaPerformDramaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaPerformDramaRequest.Merge(dst, src)
}
func (m *PiaPerformDramaRequest) XXX_Size() int {
	return xxx_messageInfo_PiaPerformDramaRequest.Size(m)
}
func (m *PiaPerformDramaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaPerformDramaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PiaPerformDramaRequest proto.InternalMessageInfo

func (m *PiaPerformDramaRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaPerformDramaRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaPerformDramaRequest) GetDramaId() uint32 {
	if m != nil {
		return m.DramaId
	}
	return 0
}

func (m *PiaPerformDramaRequest) GetIndexId() int64 {
	if m != nil {
		return m.IndexId
	}
	return 0
}

func (m *PiaPerformDramaRequest) GetIsCopyDrama() bool {
	if m != nil {
		return m.IsCopyDrama
	}
	return false
}

func (m *PiaPerformDramaRequest) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type PiaPerformDramaResponse struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DramaStatus          *ChannelDramaStatus `protobuf:"bytes,2,opt,name=drama_status,json=dramaStatus,proto3" json:"drama_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PiaPerformDramaResponse) Reset()         { *m = PiaPerformDramaResponse{} }
func (m *PiaPerformDramaResponse) String() string { return proto.CompactTextString(m) }
func (*PiaPerformDramaResponse) ProtoMessage()    {}
func (*PiaPerformDramaResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{126}
}
func (m *PiaPerformDramaResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaPerformDramaResponse.Unmarshal(m, b)
}
func (m *PiaPerformDramaResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaPerformDramaResponse.Marshal(b, m, deterministic)
}
func (dst *PiaPerformDramaResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaPerformDramaResponse.Merge(dst, src)
}
func (m *PiaPerformDramaResponse) XXX_Size() int {
	return xxx_messageInfo_PiaPerformDramaResponse.Size(m)
}
func (m *PiaPerformDramaResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaPerformDramaResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PiaPerformDramaResponse proto.InternalMessageInfo

func (m *PiaPerformDramaResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaPerformDramaResponse) GetDramaStatus() *ChannelDramaStatus {
	if m != nil {
		return m.DramaStatus
	}
	return nil
}

// 发送台词定位请求
type PiaSendDialogueIndexRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DialogueIndex        uint32       `protobuf:"varint,3,opt,name=dialogue_index,json=dialogueIndex,proto3" json:"dialogue_index,omitempty"`
	UidList              []uint32     `protobuf:"varint,4,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	RoundId              int64        `protobuf:"varint,5,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaSendDialogueIndexRequest) Reset()         { *m = PiaSendDialogueIndexRequest{} }
func (m *PiaSendDialogueIndexRequest) String() string { return proto.CompactTextString(m) }
func (*PiaSendDialogueIndexRequest) ProtoMessage()    {}
func (*PiaSendDialogueIndexRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{127}
}
func (m *PiaSendDialogueIndexRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaSendDialogueIndexRequest.Unmarshal(m, b)
}
func (m *PiaSendDialogueIndexRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaSendDialogueIndexRequest.Marshal(b, m, deterministic)
}
func (dst *PiaSendDialogueIndexRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaSendDialogueIndexRequest.Merge(dst, src)
}
func (m *PiaSendDialogueIndexRequest) XXX_Size() int {
	return xxx_messageInfo_PiaSendDialogueIndexRequest.Size(m)
}
func (m *PiaSendDialogueIndexRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaSendDialogueIndexRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PiaSendDialogueIndexRequest proto.InternalMessageInfo

func (m *PiaSendDialogueIndexRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaSendDialogueIndexRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaSendDialogueIndexRequest) GetDialogueIndex() uint32 {
	if m != nil {
		return m.DialogueIndex
	}
	return 0
}

func (m *PiaSendDialogueIndexRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *PiaSendDialogueIndexRequest) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// 发送台词定位请求
type PiaSendDialogueIndexResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaSendDialogueIndexResponse) Reset()         { *m = PiaSendDialogueIndexResponse{} }
func (m *PiaSendDialogueIndexResponse) String() string { return proto.CompactTextString(m) }
func (*PiaSendDialogueIndexResponse) ProtoMessage()    {}
func (*PiaSendDialogueIndexResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{128}
}
func (m *PiaSendDialogueIndexResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaSendDialogueIndexResponse.Unmarshal(m, b)
}
func (m *PiaSendDialogueIndexResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaSendDialogueIndexResponse.Marshal(b, m, deterministic)
}
func (dst *PiaSendDialogueIndexResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaSendDialogueIndexResponse.Merge(dst, src)
}
func (m *PiaSendDialogueIndexResponse) XXX_Size() int {
	return xxx_messageInfo_PiaSendDialogueIndexResponse.Size(m)
}
func (m *PiaSendDialogueIndexResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaSendDialogueIndexResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PiaSendDialogueIndexResponse proto.InternalMessageInfo

func (m *PiaSendDialogueIndexResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 发起跟随请求
type PiaFollowMicRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64        `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	TargetMic            PiaMicType   `protobuf:"varint,4,opt,name=target_mic,json=targetMic,proto3,enum=ga.pia.PiaMicType" json:"target_mic,omitempty"`
	MyMic                PiaMicType   `protobuf:"varint,5,opt,name=my_mic,json=myMic,proto3,enum=ga.pia.PiaMicType" json:"my_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaFollowMicRequest) Reset()         { *m = PiaFollowMicRequest{} }
func (m *PiaFollowMicRequest) String() string { return proto.CompactTextString(m) }
func (*PiaFollowMicRequest) ProtoMessage()    {}
func (*PiaFollowMicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{129}
}
func (m *PiaFollowMicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaFollowMicRequest.Unmarshal(m, b)
}
func (m *PiaFollowMicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaFollowMicRequest.Marshal(b, m, deterministic)
}
func (dst *PiaFollowMicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaFollowMicRequest.Merge(dst, src)
}
func (m *PiaFollowMicRequest) XXX_Size() int {
	return xxx_messageInfo_PiaFollowMicRequest.Size(m)
}
func (m *PiaFollowMicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaFollowMicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PiaFollowMicRequest proto.InternalMessageInfo

func (m *PiaFollowMicRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaFollowMicRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaFollowMicRequest) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *PiaFollowMicRequest) GetTargetMic() PiaMicType {
	if m != nil {
		return m.TargetMic
	}
	return PiaMicType_PIA_MIC_TYPE_NIL
}

func (m *PiaFollowMicRequest) GetMyMic() PiaMicType {
	if m != nil {
		return m.MyMic
	}
	return PiaMicType_PIA_MIC_TYPE_NIL
}

// 发起跟随响应
type PiaFollowMicResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DialogueIndex        uint32        `protobuf:"varint,2,opt,name=dialogue_index,json=dialogueIndex,proto3" json:"dialogue_index,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaFollowMicResponse) Reset()         { *m = PiaFollowMicResponse{} }
func (m *PiaFollowMicResponse) String() string { return proto.CompactTextString(m) }
func (*PiaFollowMicResponse) ProtoMessage()    {}
func (*PiaFollowMicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{130}
}
func (m *PiaFollowMicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaFollowMicResponse.Unmarshal(m, b)
}
func (m *PiaFollowMicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaFollowMicResponse.Marshal(b, m, deterministic)
}
func (dst *PiaFollowMicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaFollowMicResponse.Merge(dst, src)
}
func (m *PiaFollowMicResponse) XXX_Size() int {
	return xxx_messageInfo_PiaFollowMicResponse.Size(m)
}
func (m *PiaFollowMicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaFollowMicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PiaFollowMicResponse proto.InternalMessageInfo

func (m *PiaFollowMicResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaFollowMicResponse) GetDialogueIndex() uint32 {
	if m != nil {
		return m.DialogueIndex
	}
	return 0
}

// 取消跟随 用户发生滑动时调用（仅麦上用户调用）
type PiaUnFollowMicRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64        `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	MyMic                PiaMicType   `protobuf:"varint,4,opt,name=my_mic,json=myMic,proto3,enum=ga.pia.PiaMicType" json:"my_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaUnFollowMicRequest) Reset()         { *m = PiaUnFollowMicRequest{} }
func (m *PiaUnFollowMicRequest) String() string { return proto.CompactTextString(m) }
func (*PiaUnFollowMicRequest) ProtoMessage()    {}
func (*PiaUnFollowMicRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{131}
}
func (m *PiaUnFollowMicRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaUnFollowMicRequest.Unmarshal(m, b)
}
func (m *PiaUnFollowMicRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaUnFollowMicRequest.Marshal(b, m, deterministic)
}
func (dst *PiaUnFollowMicRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaUnFollowMicRequest.Merge(dst, src)
}
func (m *PiaUnFollowMicRequest) XXX_Size() int {
	return xxx_messageInfo_PiaUnFollowMicRequest.Size(m)
}
func (m *PiaUnFollowMicRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaUnFollowMicRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PiaUnFollowMicRequest proto.InternalMessageInfo

func (m *PiaUnFollowMicRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaUnFollowMicRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaUnFollowMicRequest) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *PiaUnFollowMicRequest) GetMyMic() PiaMicType {
	if m != nil {
		return m.MyMic
	}
	return PiaMicType_PIA_MIC_TYPE_NIL
}

type PiaUnFollowMicResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaUnFollowMicResponse) Reset()         { *m = PiaUnFollowMicResponse{} }
func (m *PiaUnFollowMicResponse) String() string { return proto.CompactTextString(m) }
func (*PiaUnFollowMicResponse) ProtoMessage()    {}
func (*PiaUnFollowMicResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{132}
}
func (m *PiaUnFollowMicResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaUnFollowMicResponse.Unmarshal(m, b)
}
func (m *PiaUnFollowMicResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaUnFollowMicResponse.Marshal(b, m, deterministic)
}
func (dst *PiaUnFollowMicResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaUnFollowMicResponse.Merge(dst, src)
}
func (m *PiaUnFollowMicResponse) XXX_Size() int {
	return xxx_messageInfo_PiaUnFollowMicResponse.Size(m)
}
func (m *PiaUnFollowMicResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaUnFollowMicResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PiaUnFollowMicResponse proto.InternalMessageInfo

func (m *PiaUnFollowMicResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 上报段落请求
type PiaReportDialogueIndexRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64        `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	DialogueIndex        uint32       `protobuf:"varint,4,opt,name=dialogue_index,json=dialogueIndex,proto3" json:"dialogue_index,omitempty"`
	MyMic                PiaMicType   `protobuf:"varint,5,opt,name=my_mic,json=myMic,proto3,enum=ga.pia.PiaMicType" json:"my_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaReportDialogueIndexRequest) Reset()         { *m = PiaReportDialogueIndexRequest{} }
func (m *PiaReportDialogueIndexRequest) String() string { return proto.CompactTextString(m) }
func (*PiaReportDialogueIndexRequest) ProtoMessage()    {}
func (*PiaReportDialogueIndexRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{133}
}
func (m *PiaReportDialogueIndexRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaReportDialogueIndexRequest.Unmarshal(m, b)
}
func (m *PiaReportDialogueIndexRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaReportDialogueIndexRequest.Marshal(b, m, deterministic)
}
func (dst *PiaReportDialogueIndexRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaReportDialogueIndexRequest.Merge(dst, src)
}
func (m *PiaReportDialogueIndexRequest) XXX_Size() int {
	return xxx_messageInfo_PiaReportDialogueIndexRequest.Size(m)
}
func (m *PiaReportDialogueIndexRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaReportDialogueIndexRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PiaReportDialogueIndexRequest proto.InternalMessageInfo

func (m *PiaReportDialogueIndexRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaReportDialogueIndexRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaReportDialogueIndexRequest) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *PiaReportDialogueIndexRequest) GetDialogueIndex() uint32 {
	if m != nil {
		return m.DialogueIndex
	}
	return 0
}

func (m *PiaReportDialogueIndexRequest) GetMyMic() PiaMicType {
	if m != nil {
		return m.MyMic
	}
	return PiaMicType_PIA_MIC_TYPE_NIL
}

// 上报段落请求响应
type PiaReportDialogueIndexResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaReportDialogueIndexResponse) Reset()         { *m = PiaReportDialogueIndexResponse{} }
func (m *PiaReportDialogueIndexResponse) String() string { return proto.CompactTextString(m) }
func (*PiaReportDialogueIndexResponse) ProtoMessage()    {}
func (*PiaReportDialogueIndexResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{134}
}
func (m *PiaReportDialogueIndexResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaReportDialogueIndexResponse.Unmarshal(m, b)
}
func (m *PiaReportDialogueIndexResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaReportDialogueIndexResponse.Marshal(b, m, deterministic)
}
func (dst *PiaReportDialogueIndexResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaReportDialogueIndexResponse.Merge(dst, src)
}
func (m *PiaReportDialogueIndexResponse) XXX_Size() int {
	return xxx_messageInfo_PiaReportDialogueIndexResponse.Size(m)
}
func (m *PiaReportDialogueIndexResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaReportDialogueIndexResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PiaReportDialogueIndexResponse proto.InternalMessageInfo

func (m *PiaReportDialogueIndexResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取上个用户的段落记录请求
type PiaGetPreviousDialogueIndexRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64        `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	MyMic                PiaMicType   `protobuf:"varint,4,opt,name=my_mic,json=myMic,proto3,enum=ga.pia.PiaMicType" json:"my_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaGetPreviousDialogueIndexRequest) Reset()         { *m = PiaGetPreviousDialogueIndexRequest{} }
func (m *PiaGetPreviousDialogueIndexRequest) String() string { return proto.CompactTextString(m) }
func (*PiaGetPreviousDialogueIndexRequest) ProtoMessage()    {}
func (*PiaGetPreviousDialogueIndexRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{135}
}
func (m *PiaGetPreviousDialogueIndexRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetPreviousDialogueIndexRequest.Unmarshal(m, b)
}
func (m *PiaGetPreviousDialogueIndexRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetPreviousDialogueIndexRequest.Marshal(b, m, deterministic)
}
func (dst *PiaGetPreviousDialogueIndexRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetPreviousDialogueIndexRequest.Merge(dst, src)
}
func (m *PiaGetPreviousDialogueIndexRequest) XXX_Size() int {
	return xxx_messageInfo_PiaGetPreviousDialogueIndexRequest.Size(m)
}
func (m *PiaGetPreviousDialogueIndexRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetPreviousDialogueIndexRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetPreviousDialogueIndexRequest proto.InternalMessageInfo

func (m *PiaGetPreviousDialogueIndexRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaGetPreviousDialogueIndexRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaGetPreviousDialogueIndexRequest) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *PiaGetPreviousDialogueIndexRequest) GetMyMic() PiaMicType {
	if m != nil {
		return m.MyMic
	}
	return PiaMicType_PIA_MIC_TYPE_NIL
}

// 获取上个用户的段落记录请求响应
type PiaGetPreviousDialogueIndexResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DialogueIndex        uint32        `protobuf:"varint,2,opt,name=dialogue_index,json=dialogueIndex,proto3" json:"dialogue_index,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaGetPreviousDialogueIndexResponse) Reset()         { *m = PiaGetPreviousDialogueIndexResponse{} }
func (m *PiaGetPreviousDialogueIndexResponse) String() string { return proto.CompactTextString(m) }
func (*PiaGetPreviousDialogueIndexResponse) ProtoMessage()    {}
func (*PiaGetPreviousDialogueIndexResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{136}
}
func (m *PiaGetPreviousDialogueIndexResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetPreviousDialogueIndexResponse.Unmarshal(m, b)
}
func (m *PiaGetPreviousDialogueIndexResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetPreviousDialogueIndexResponse.Marshal(b, m, deterministic)
}
func (dst *PiaGetPreviousDialogueIndexResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetPreviousDialogueIndexResponse.Merge(dst, src)
}
func (m *PiaGetPreviousDialogueIndexResponse) XXX_Size() int {
	return xxx_messageInfo_PiaGetPreviousDialogueIndexResponse.Size(m)
}
func (m *PiaGetPreviousDialogueIndexResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetPreviousDialogueIndexResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetPreviousDialogueIndexResponse proto.InternalMessageInfo

func (m *PiaGetPreviousDialogueIndexResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaGetPreviousDialogueIndexResponse) GetDialogueIndex() uint32 {
	if m != nil {
		return m.DialogueIndex
	}
	return 0
}

// 获取我的跟随状态请求
type PiaGetMyFollowInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64        `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaGetMyFollowInfoRequest) Reset()         { *m = PiaGetMyFollowInfoRequest{} }
func (m *PiaGetMyFollowInfoRequest) String() string { return proto.CompactTextString(m) }
func (*PiaGetMyFollowInfoRequest) ProtoMessage()    {}
func (*PiaGetMyFollowInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{137}
}
func (m *PiaGetMyFollowInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetMyFollowInfoRequest.Unmarshal(m, b)
}
func (m *PiaGetMyFollowInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetMyFollowInfoRequest.Marshal(b, m, deterministic)
}
func (dst *PiaGetMyFollowInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetMyFollowInfoRequest.Merge(dst, src)
}
func (m *PiaGetMyFollowInfoRequest) XXX_Size() int {
	return xxx_messageInfo_PiaGetMyFollowInfoRequest.Size(m)
}
func (m *PiaGetMyFollowInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetMyFollowInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetMyFollowInfoRequest proto.InternalMessageInfo

func (m *PiaGetMyFollowInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaGetMyFollowInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaGetMyFollowInfoRequest) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// 获取我的跟随状态响应
type PiaGetMyFollowInfoResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TargetMic            PiaMicType    `protobuf:"varint,2,opt,name=target_mic,json=targetMic,proto3,enum=ga.pia.PiaMicType" json:"target_mic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PiaGetMyFollowInfoResponse) Reset()         { *m = PiaGetMyFollowInfoResponse{} }
func (m *PiaGetMyFollowInfoResponse) String() string { return proto.CompactTextString(m) }
func (*PiaGetMyFollowInfoResponse) ProtoMessage()    {}
func (*PiaGetMyFollowInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{138}
}
func (m *PiaGetMyFollowInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetMyFollowInfoResponse.Unmarshal(m, b)
}
func (m *PiaGetMyFollowInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetMyFollowInfoResponse.Marshal(b, m, deterministic)
}
func (dst *PiaGetMyFollowInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetMyFollowInfoResponse.Merge(dst, src)
}
func (m *PiaGetMyFollowInfoResponse) XXX_Size() int {
	return xxx_messageInfo_PiaGetMyFollowInfoResponse.Size(m)
}
func (m *PiaGetMyFollowInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetMyFollowInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetMyFollowInfoResponse proto.InternalMessageInfo

func (m *PiaGetMyFollowInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaGetMyFollowInfoResponse) GetTargetMic() PiaMicType {
	if m != nil {
		return m.TargetMic
	}
	return PiaMicType_PIA_MIC_TYPE_NIL
}

// 获取pia戏房间各个麦位的跟随状态请求
type PiaGetFollowedStatusOfMicListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64        `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PiaGetFollowedStatusOfMicListRequest) Reset()         { *m = PiaGetFollowedStatusOfMicListRequest{} }
func (m *PiaGetFollowedStatusOfMicListRequest) String() string { return proto.CompactTextString(m) }
func (*PiaGetFollowedStatusOfMicListRequest) ProtoMessage()    {}
func (*PiaGetFollowedStatusOfMicListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{139}
}
func (m *PiaGetFollowedStatusOfMicListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetFollowedStatusOfMicListRequest.Unmarshal(m, b)
}
func (m *PiaGetFollowedStatusOfMicListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetFollowedStatusOfMicListRequest.Marshal(b, m, deterministic)
}
func (dst *PiaGetFollowedStatusOfMicListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetFollowedStatusOfMicListRequest.Merge(dst, src)
}
func (m *PiaGetFollowedStatusOfMicListRequest) XXX_Size() int {
	return xxx_messageInfo_PiaGetFollowedStatusOfMicListRequest.Size(m)
}
func (m *PiaGetFollowedStatusOfMicListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetFollowedStatusOfMicListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetFollowedStatusOfMicListRequest proto.InternalMessageInfo

func (m *PiaGetFollowedStatusOfMicListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PiaGetFollowedStatusOfMicListRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaGetFollowedStatusOfMicListRequest) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

// 获取当前房间各个麦位的跟随状态响应
type PiaGetFollowedStatusOfMicListResponse struct {
	BaseResp             *app.BaseResp                                         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MicStatusList        []*PiaGetFollowedStatusOfMicListResponse_PiaMicStatus `protobuf:"bytes,2,rep,name=mic_status_list,json=micStatusList,proto3" json:"mic_status_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                              `json:"-"`
	XXX_unrecognized     []byte                                                `json:"-"`
	XXX_sizecache        int32                                                 `json:"-"`
}

func (m *PiaGetFollowedStatusOfMicListResponse) Reset()         { *m = PiaGetFollowedStatusOfMicListResponse{} }
func (m *PiaGetFollowedStatusOfMicListResponse) String() string { return proto.CompactTextString(m) }
func (*PiaGetFollowedStatusOfMicListResponse) ProtoMessage()    {}
func (*PiaGetFollowedStatusOfMicListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{140}
}
func (m *PiaGetFollowedStatusOfMicListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse.Unmarshal(m, b)
}
func (m *PiaGetFollowedStatusOfMicListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse.Marshal(b, m, deterministic)
}
func (dst *PiaGetFollowedStatusOfMicListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse.Merge(dst, src)
}
func (m *PiaGetFollowedStatusOfMicListResponse) XXX_Size() int {
	return xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse.Size(m)
}
func (m *PiaGetFollowedStatusOfMicListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse proto.InternalMessageInfo

func (m *PiaGetFollowedStatusOfMicListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PiaGetFollowedStatusOfMicListResponse) GetMicStatusList() []*PiaGetFollowedStatusOfMicListResponse_PiaMicStatus {
	if m != nil {
		return m.MicStatusList
	}
	return nil
}

type PiaGetFollowedStatusOfMicListResponse_PiaMicStatus struct {
	MicNum               uint32   `protobuf:"varint,1,opt,name=mic_num,json=micNum,proto3" json:"mic_num,omitempty"`
	CanBeFollowed        bool     `protobuf:"varint,2,opt,name=can_be_followed,json=canBeFollowed,proto3" json:"can_be_followed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) Reset() {
	*m = PiaGetFollowedStatusOfMicListResponse_PiaMicStatus{}
}
func (m *PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) String() string {
	return proto.CompactTextString(m)
}
func (*PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) ProtoMessage() {}
func (*PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{140, 0}
}
func (m *PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse_PiaMicStatus.Unmarshal(m, b)
}
func (m *PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse_PiaMicStatus.Marshal(b, m, deterministic)
}
func (dst *PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse_PiaMicStatus.Merge(dst, src)
}
func (m *PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) XXX_Size() int {
	return xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse_PiaMicStatus.Size(m)
}
func (m *PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse_PiaMicStatus.DiscardUnknown(m)
}

var xxx_messageInfo_PiaGetFollowedStatusOfMicListResponse_PiaMicStatus proto.InternalMessageInfo

func (m *PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) GetMicNum() uint32 {
	if m != nil {
		return m.MicNum
	}
	return 0
}

func (m *PiaGetFollowedStatusOfMicListResponse_PiaMicStatus) GetCanBeFollowed() bool {
	if m != nil {
		return m.CanBeFollowed
	}
	return false
}

// 台词定位推送
type PiaDialogueIndexLocationMsg struct {
	DialogueIndex        uint32   `protobuf:"varint,1,opt,name=dialogue_index,json=dialogueIndex,proto3" json:"dialogue_index,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64    `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	Version              int64    `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaDialogueIndexLocationMsg) Reset()         { *m = PiaDialogueIndexLocationMsg{} }
func (m *PiaDialogueIndexLocationMsg) String() string { return proto.CompactTextString(m) }
func (*PiaDialogueIndexLocationMsg) ProtoMessage()    {}
func (*PiaDialogueIndexLocationMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{141}
}
func (m *PiaDialogueIndexLocationMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaDialogueIndexLocationMsg.Unmarshal(m, b)
}
func (m *PiaDialogueIndexLocationMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaDialogueIndexLocationMsg.Marshal(b, m, deterministic)
}
func (dst *PiaDialogueIndexLocationMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaDialogueIndexLocationMsg.Merge(dst, src)
}
func (m *PiaDialogueIndexLocationMsg) XXX_Size() int {
	return xxx_messageInfo_PiaDialogueIndexLocationMsg.Size(m)
}
func (m *PiaDialogueIndexLocationMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaDialogueIndexLocationMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PiaDialogueIndexLocationMsg proto.InternalMessageInfo

func (m *PiaDialogueIndexLocationMsg) GetDialogueIndex() uint32 {
	if m != nil {
		return m.DialogueIndex
	}
	return 0
}

func (m *PiaDialogueIndexLocationMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaDialogueIndexLocationMsg) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *PiaDialogueIndexLocationMsg) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

// 走本跟随推送
type PiaDialogueIndexFollowMsg struct {
	MicMask              uint32   `protobuf:"varint,1,opt,name=mic_mask,json=micMask,proto3" json:"mic_mask,omitempty"`
	DialogueIndex        uint32   `protobuf:"varint,2,opt,name=dialogue_index,json=dialogueIndex,proto3" json:"dialogue_index,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              int64    `protobuf:"varint,4,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	Version              int64    `protobuf:"varint,5,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PiaDialogueIndexFollowMsg) Reset()         { *m = PiaDialogueIndexFollowMsg{} }
func (m *PiaDialogueIndexFollowMsg) String() string { return proto.CompactTextString(m) }
func (*PiaDialogueIndexFollowMsg) ProtoMessage()    {}
func (*PiaDialogueIndexFollowMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_pia__81139eb59425d195, []int{142}
}
func (m *PiaDialogueIndexFollowMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PiaDialogueIndexFollowMsg.Unmarshal(m, b)
}
func (m *PiaDialogueIndexFollowMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PiaDialogueIndexFollowMsg.Marshal(b, m, deterministic)
}
func (dst *PiaDialogueIndexFollowMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PiaDialogueIndexFollowMsg.Merge(dst, src)
}
func (m *PiaDialogueIndexFollowMsg) XXX_Size() int {
	return xxx_messageInfo_PiaDialogueIndexFollowMsg.Size(m)
}
func (m *PiaDialogueIndexFollowMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PiaDialogueIndexFollowMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PiaDialogueIndexFollowMsg proto.InternalMessageInfo

func (m *PiaDialogueIndexFollowMsg) GetMicMask() uint32 {
	if m != nil {
		return m.MicMask
	}
	return 0
}

func (m *PiaDialogueIndexFollowMsg) GetDialogueIndex() uint32 {
	if m != nil {
		return m.DialogueIndex
	}
	return 0
}

func (m *PiaDialogueIndexFollowMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PiaDialogueIndexFollowMsg) GetRoundId() int64 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *PiaDialogueIndexFollowMsg) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func init() {
	proto.RegisterType((*GetChannelPiaStatusReq)(nil), "ga.pia.GetChannelPiaStatusReq")
	proto.RegisterType((*GetChannelPiaStatusResp)(nil), "ga.pia.GetChannelPiaStatusResp")
	proto.RegisterType((*SetCompereMicReq)(nil), "ga.pia.SetCompereMicReq")
	proto.RegisterType((*SetCompereMicResp)(nil), "ga.pia.SetCompereMicResp")
	proto.RegisterType((*SetPiaSwitchReq)(nil), "ga.pia.SetPiaSwitchReq")
	proto.RegisterType((*SetPiaSwitchResp)(nil), "ga.pia.SetPiaSwitchResp")
	proto.RegisterType((*GetDramaReq)(nil), "ga.pia.GetDramaReq")
	proto.RegisterType((*Drama)(nil), "ga.pia.Drama")
	proto.RegisterType((*GetDramaResp)(nil), "ga.pia.GetDramaResp")
	proto.RegisterType((*GetSearchOptionGroupReq)(nil), "ga.pia.GetSearchOptionGroupReq")
	proto.RegisterType((*SearchOptionGroup)(nil), "ga.pia.SearchOptionGroup")
	proto.RegisterType((*SubSearchOptionGroup)(nil), "ga.pia.SubSearchOptionGroup")
	proto.RegisterType((*GetSearchOptionGroupResp)(nil), "ga.pia.GetSearchOptionGroupResp")
	proto.RegisterType((*SearchOption)(nil), "ga.pia.SearchOption")
	proto.RegisterType((*GetCurrentPiaInfoReq)(nil), "ga.pia.GetCurrentPiaInfoReq")
	proto.RegisterType((*PiaInfo)(nil), "ga.pia.PiaInfo")
	proto.RegisterType((*GetCurrentPiaInfoResp)(nil), "ga.pia.GetCurrentPiaInfoResp")
	proto.RegisterType((*SetPiaPhaseReq)(nil), "ga.pia.SetPiaPhaseReq")
	proto.RegisterType((*SetPiaPhaseResp)(nil), "ga.pia.SetPiaPhaseResp")
	proto.RegisterType((*SetPiaProgressReq)(nil), "ga.pia.SetPiaProgressReq")
	proto.RegisterType((*SetPiaProgressResp)(nil), "ga.pia.SetPiaProgressResp")
	proto.RegisterType((*SetBgmInfoReq)(nil), "ga.pia.SetBgmInfoReq")
	proto.RegisterType((*SetBgmInfoResp)(nil), "ga.pia.SetBgmInfoResp")
	proto.RegisterType((*GetBgmInfoReq)(nil), "ga.pia.GetBgmInfoReq")
	proto.RegisterType((*GetBgmInfoResp)(nil), "ga.pia.GetBgmInfoResp")
	proto.RegisterType((*GetPlayingChannelReq)(nil), "ga.pia.GetPlayingChannelReq")
	proto.RegisterType((*PlayingChannelInfo)(nil), "ga.pia.PlayingChannelInfo")
	proto.RegisterType((*GetPlayingChannelResp)(nil), "ga.pia.GetPlayingChannelResp")
	proto.RegisterType((*PiaSwitch)(nil), "ga.pia.PiaSwitch")
	proto.RegisterType((*SelectDramaReq)(nil), "ga.pia.SelectDramaReq")
	proto.RegisterType((*SelectDramaResp)(nil), "ga.pia.SelectDramaResp")
	proto.RegisterType((*GetQualityDramaListReq)(nil), "ga.pia.GetQualityDramaListReq")
	proto.RegisterType((*GetQualityDramaListResp)(nil), "ga.pia.GetQualityDramaListResp")
	proto.RegisterType((*GetPracticeDramaListReq)(nil), "ga.pia.GetPracticeDramaListReq")
	proto.RegisterType((*GetPracticeDramaListResp)(nil), "ga.pia.GetPracticeDramaListResp")
	proto.RegisterType((*DramaRoom)(nil), "ga.pia.DramaRoom")
	proto.RegisterType((*DramaInfoForAggPage)(nil), "ga.pia.DramaInfoForAggPage")
	proto.RegisterType((*DramaRoomBaseInfo)(nil), "ga.pia.DramaRoomBaseInfo")
	proto.RegisterType((*QualityDrama)(nil), "ga.pia.QualityDrama")
	proto.RegisterType((*PracticeDrama)(nil), "ga.pia.PracticeDrama")
	proto.RegisterType((*DramaOrderList)(nil), "ga.pia.DramaOrderList")
	proto.RegisterType((*DramaOrderList_UserOrderInfo)(nil), "ga.pia.DramaOrderList.UserOrderInfo")
	proto.RegisterType((*OrderDramaReq)(nil), "ga.pia.OrderDramaReq")
	proto.RegisterType((*OrderDramaResp)(nil), "ga.pia.OrderDramaResp")
	proto.RegisterType((*GetOrderDramaListReq)(nil), "ga.pia.GetOrderDramaListReq")
	proto.RegisterType((*GetOrderDramaListResp)(nil), "ga.pia.GetOrderDramaListResp")
	proto.RegisterType((*DeleteOrderDramaReq)(nil), "ga.pia.DeleteOrderDramaReq")
	proto.RegisterType((*DeleteOrderDramaResp)(nil), "ga.pia.DeleteOrderDramaResp")
	proto.RegisterType((*MicRoleMap)(nil), "ga.pia.MicRoleMap")
	proto.RegisterMapType((map[uint32]*MicRoleMap_RoleInfoList)(nil), "ga.pia.MicRoleMap.MapEntry")
	proto.RegisterType((*MicRoleMap_RoleInfoList)(nil), "ga.pia.MicRoleMap.RoleInfoList")
	proto.RegisterType((*PiaSelectRoleReq)(nil), "ga.pia.PiaSelectRoleReq")
	proto.RegisterType((*PiaSelectRoleResp)(nil), "ga.pia.PiaSelectRoleResp")
	proto.RegisterType((*PiaCancelSelectRoleReq)(nil), "ga.pia.PiaCancelSelectRoleReq")
	proto.RegisterType((*PiaCancelSelectRoleResp)(nil), "ga.pia.PiaCancelSelectRoleResp")
	proto.RegisterType((*ChannelDramaProgress)(nil), "ga.pia.ChannelDramaProgress")
	proto.RegisterType((*ChannelDramaStatus)(nil), "ga.pia.ChannelDramaStatus")
	proto.RegisterType((*SelectDramaV2Req)(nil), "ga.pia.SelectDramaV2Req")
	proto.RegisterType((*SelectDramaV2Resp)(nil), "ga.pia.SelectDramaV2Resp")
	proto.RegisterType((*PiaOperateDramaReq)(nil), "ga.pia.PiaOperateDramaReq")
	proto.RegisterType((*PiaOperateDramaResp)(nil), "ga.pia.PiaOperateDramaResp")
	proto.RegisterType((*ChannelDramaInfoDetail)(nil), "ga.pia.ChannelDramaInfoDetail")
	proto.RegisterType((*PiaGetDramaStatusReq)(nil), "ga.pia.PiaGetDramaStatusReq")
	proto.RegisterType((*PiaGetDramaStatusResp)(nil), "ga.pia.PiaGetDramaStatusResp")
	proto.RegisterType((*PiaGetDramaCopyIdReq)(nil), "ga.pia.PiaGetDramaCopyIdReq")
	proto.RegisterType((*PiaGetDramaCopyIdResp)(nil), "ga.pia.PiaGetDramaCopyIdResp")
	proto.RegisterType((*PiaCreateDramaCopyReq)(nil), "ga.pia.PiaCreateDramaCopyReq")
	proto.RegisterType((*PiaCreateDramaCopyResp)(nil), "ga.pia.PiaCreateDramaCopyResp")
	proto.RegisterType((*DramaBgmStatus)(nil), "ga.pia.DramaBgmStatus")
	proto.RegisterType((*DramaBgmVolStatus)(nil), "ga.pia.DramaBgmVolStatus")
	proto.RegisterType((*PiaOperateBgmReq)(nil), "ga.pia.PiaOperateBgmReq")
	proto.RegisterType((*PiaOperateBgmResp)(nil), "ga.pia.PiaOperateBgmResp")
	proto.RegisterType((*PiaOperateBgmVolReq)(nil), "ga.pia.PiaOperateBgmVolReq")
	proto.RegisterType((*PiaOperateBgmVolResp)(nil), "ga.pia.PiaOperateBgmVolResp")
	proto.RegisterType((*PiaRole)(nil), "ga.pia.PiaRole")
	proto.RegisterType((*PiaDuration)(nil), "ga.pia.PiaDuration")
	proto.RegisterType((*PiaContent)(nil), "ga.pia.PiaContent")
	proto.RegisterType((*PiaPicture)(nil), "ga.pia.PiaPicture")
	proto.RegisterType((*PiaBGM)(nil), "ga.pia.PiaBGM")
	proto.RegisterType((*DramaSubInfo)(nil), "ga.pia.DramaSubInfo")
	proto.RegisterType((*DramaInfoWithStatus)(nil), "ga.pia.DramaInfoWithStatus")
	proto.RegisterType((*DramaV2)(nil), "ga.pia.DramaV2")
	proto.RegisterType((*GetSearchOptionGroupV2Req)(nil), "ga.pia.GetSearchOptionGroupV2Req")
	proto.RegisterType((*GetSearchOptionGroupV2Resp)(nil), "ga.pia.GetSearchOptionGroupV2Resp")
	proto.RegisterType((*GetDramaListReq)(nil), "ga.pia.GetDramaListReq")
	proto.RegisterType((*GetDramaListResp)(nil), "ga.pia.GetDramaListResp")
	proto.RegisterType((*GetDramaDetailByIdReq)(nil), "ga.pia.GetDramaDetailByIdReq")
	proto.RegisterType((*PiaUserInfo)(nil), "ga.pia.PiaUserInfo")
	proto.RegisterType((*GetDramaDetailByIdResp)(nil), "ga.pia.GetDramaDetailByIdResp")
	proto.RegisterType((*PlayingChannelInfoV2)(nil), "ga.pia.PlayingChannelInfoV2")
	proto.RegisterType((*GetPlayingChannelV2Req)(nil), "ga.pia.GetPlayingChannelV2Req")
	proto.RegisterType((*GetPlayingChannelV2Resp)(nil), "ga.pia.GetPlayingChannelV2Resp")
	proto.RegisterType((*PiaGetRankingListReq)(nil), "ga.pia.PiaGetRankingListReq")
	proto.RegisterType((*PiaGetRankingListResp)(nil), "ga.pia.PiaGetRankingListResp")
	proto.RegisterType((*DoUserDramaCollectReq)(nil), "ga.pia.DoUserDramaCollectReq")
	proto.RegisterType((*DoUserDramaCollectResp)(nil), "ga.pia.DoUserDramaCollectResp")
	proto.RegisterType((*GetUserDramaCollectionReq)(nil), "ga.pia.GetUserDramaCollectionReq")
	proto.RegisterType((*GetUserDramaCollectionResp)(nil), "ga.pia.GetUserDramaCollectionResp")
	proto.RegisterType((*PiaChannelDramaPlayingType)(nil), "ga.pia.PiaChannelDramaPlayingType")
	proto.RegisterType((*PiaChangePlayType)(nil), "ga.pia.PiaChangePlayType")
	proto.RegisterType((*PiaChangePlayTypeReq)(nil), "ga.pia.PiaChangePlayTypeReq")
	proto.RegisterType((*PiaChangePlayTypeResp)(nil), "ga.pia.PiaChangePlayTypeResp")
	proto.RegisterType((*GetMyDramaPlayingRecordReq)(nil), "ga.pia.GetMyDramaPlayingRecordReq")
	proto.RegisterType((*GetMyDramaPlayingRecordResp)(nil), "ga.pia.GetMyDramaPlayingRecordResp")
	proto.RegisterType((*PiaDramaPlayingRecord)(nil), "ga.pia.PiaDramaPlayingRecord")
	proto.RegisterType((*PiaBatchDeleteMyPlayingRecordReq)(nil), "ga.pia.PiaBatchDeleteMyPlayingRecordReq")
	proto.RegisterType((*PiaBatchDeleteMyPlayingRecordResp)(nil), "ga.pia.PiaBatchDeleteMyPlayingRecordResp")
	proto.RegisterType((*PiaGetMyPlayingRecordIdListReq)(nil), "ga.pia.PiaGetMyPlayingRecordIdListReq")
	proto.RegisterType((*PiaGetMyPlayingRecordIdListResp)(nil), "ga.pia.PiaGetMyPlayingRecordIdListResp")
	proto.RegisterType((*PiaAuthorGenInfoReq)(nil), "ga.pia.PiaAuthorGenInfoReq")
	proto.RegisterType((*PiaAuthorGenInfoResp)(nil), "ga.pia.PiaAuthorGenInfoResp")
	proto.RegisterType((*PiaAuthorWorksListReq)(nil), "ga.pia.PiaAuthorWorksListReq")
	proto.RegisterType((*PiaAuthorWorksListResp)(nil), "ga.pia.PiaAuthorWorksListResp")
	proto.RegisterType((*PiaDramaCreatorInfo)(nil), "ga.pia.PiaDramaCreatorInfo")
	proto.RegisterType((*PiaCopyDramaInfoWithStatus)(nil), "ga.pia.PiaCopyDramaInfoWithStatus")
	proto.RegisterType((*PiaCopyDramaListReq)(nil), "ga.pia.PiaCopyDramaListReq")
	proto.RegisterType((*PiaCopyDramaListResp)(nil), "ga.pia.PiaCopyDramaListResp")
	proto.RegisterType((*GetMyDramaCopyListReq)(nil), "ga.pia.GetMyDramaCopyListReq")
	proto.RegisterType((*PiaCopyDramaItem)(nil), "ga.pia.PiaCopyDramaItem")
	proto.RegisterType((*GetMyDramaCopyListResp)(nil), "ga.pia.GetMyDramaCopyListResp")
	proto.RegisterType((*SetDramaCopyStatusReq)(nil), "ga.pia.SetDramaCopyStatusReq")
	proto.RegisterType((*SetDramaCopyStatusResp)(nil), "ga.pia.SetDramaCopyStatusResp")
	proto.RegisterType((*DeleteDramaCopyReq)(nil), "ga.pia.DeleteDramaCopyReq")
	proto.RegisterType((*DeleteDramaCopyResp)(nil), "ga.pia.DeleteDramaCopyResp")
	proto.RegisterType((*PiaConfirmCoverCopyDramaReq)(nil), "ga.pia.PiaConfirmCoverCopyDramaReq")
	proto.RegisterType((*PiaConfirmCoverCopyDramaResp)(nil), "ga.pia.PiaConfirmCoverCopyDramaResp")
	proto.RegisterType((*PiaCreateDramaCopyV2Req)(nil), "ga.pia.PiaCreateDramaCopyV2Req")
	proto.RegisterType((*PiaCreateDramaCopyV2Resp)(nil), "ga.pia.PiaCreateDramaCopyV2Resp")
	proto.RegisterType((*PiaPerformDramaRequest)(nil), "ga.pia.PiaPerformDramaRequest")
	proto.RegisterType((*PiaPerformDramaResponse)(nil), "ga.pia.PiaPerformDramaResponse")
	proto.RegisterType((*PiaSendDialogueIndexRequest)(nil), "ga.pia.PiaSendDialogueIndexRequest")
	proto.RegisterType((*PiaSendDialogueIndexResponse)(nil), "ga.pia.PiaSendDialogueIndexResponse")
	proto.RegisterType((*PiaFollowMicRequest)(nil), "ga.pia.PiaFollowMicRequest")
	proto.RegisterType((*PiaFollowMicResponse)(nil), "ga.pia.PiaFollowMicResponse")
	proto.RegisterType((*PiaUnFollowMicRequest)(nil), "ga.pia.PiaUnFollowMicRequest")
	proto.RegisterType((*PiaUnFollowMicResponse)(nil), "ga.pia.PiaUnFollowMicResponse")
	proto.RegisterType((*PiaReportDialogueIndexRequest)(nil), "ga.pia.PiaReportDialogueIndexRequest")
	proto.RegisterType((*PiaReportDialogueIndexResponse)(nil), "ga.pia.PiaReportDialogueIndexResponse")
	proto.RegisterType((*PiaGetPreviousDialogueIndexRequest)(nil), "ga.pia.PiaGetPreviousDialogueIndexRequest")
	proto.RegisterType((*PiaGetPreviousDialogueIndexResponse)(nil), "ga.pia.PiaGetPreviousDialogueIndexResponse")
	proto.RegisterType((*PiaGetMyFollowInfoRequest)(nil), "ga.pia.PiaGetMyFollowInfoRequest")
	proto.RegisterType((*PiaGetMyFollowInfoResponse)(nil), "ga.pia.PiaGetMyFollowInfoResponse")
	proto.RegisterType((*PiaGetFollowedStatusOfMicListRequest)(nil), "ga.pia.PiaGetFollowedStatusOfMicListRequest")
	proto.RegisterType((*PiaGetFollowedStatusOfMicListResponse)(nil), "ga.pia.PiaGetFollowedStatusOfMicListResponse")
	proto.RegisterType((*PiaGetFollowedStatusOfMicListResponse_PiaMicStatus)(nil), "ga.pia.PiaGetFollowedStatusOfMicListResponse.PiaMicStatus")
	proto.RegisterType((*PiaDialogueIndexLocationMsg)(nil), "ga.pia.PiaDialogueIndexLocationMsg")
	proto.RegisterType((*PiaDialogueIndexFollowMsg)(nil), "ga.pia.PiaDialogueIndexFollowMsg")
	proto.RegisterEnum("ga.pia.PiaPhaseType", PiaPhaseType_name, PiaPhaseType_value)
	proto.RegisterEnum("ga.pia.DramaRoomUserSex", DramaRoomUserSex_name, DramaRoomUserSex_value)
	proto.RegisterEnum("ga.pia.DramaPhase", DramaPhase_name, DramaPhase_value)
	proto.RegisterEnum("ga.pia.DramaOperationType", DramaOperationType_name, DramaOperationType_value)
	proto.RegisterEnum("ga.pia.DramaBGMPhase", DramaBGMPhase_name, DramaBGMPhase_value)
	proto.RegisterEnum("ga.pia.DramaBGMOperationType", DramaBGMOperationType_name, DramaBGMOperationType_value)
	proto.RegisterEnum("ga.pia.PiaPlayType", PiaPlayType_name, PiaPlayType_value)
	proto.RegisterEnum("ga.pia.PiaChannelStatusType", PiaChannelStatusType_name, PiaChannelStatusType_value)
	proto.RegisterEnum("ga.pia.PiaRankingListType", PiaRankingListType_name, PiaRankingListType_value)
	proto.RegisterEnum("ga.pia.CollectOpType", CollectOpType_name, CollectOpType_value)
	proto.RegisterEnum("ga.pia.PiaAuthorWorksListSortType", PiaAuthorWorksListSortType_name, PiaAuthorWorksListSortType_value)
	proto.RegisterEnum("ga.pia.PiaMicType", PiaMicType_name, PiaMicType_value)
	proto.RegisterEnum("ga.pia.DramaRoomBaseInfo_PiaStage", DramaRoomBaseInfo_PiaStage_name, DramaRoomBaseInfo_PiaStage_value)
	proto.RegisterEnum("ga.pia.GetSearchOptionGroupV2Req_SearchOptionType", GetSearchOptionGroupV2Req_SearchOptionType_name, GetSearchOptionGroupV2Req_SearchOptionType_value)
	proto.RegisterEnum("ga.pia.PiaChannelDramaPlayingType_PlayingTypeRole", PiaChannelDramaPlayingType_PlayingTypeRole_name, PiaChannelDramaPlayingType_PlayingTypeRole_value)
	proto.RegisterEnum("ga.pia.PiaChannelDramaPlayingType_PlayingTypeTime", PiaChannelDramaPlayingType_PlayingTypeTime_name, PiaChannelDramaPlayingType_PlayingTypeTime_value)
	proto.RegisterEnum("ga.pia.SetDramaCopyStatusReq_DramaCopyStatus", SetDramaCopyStatusReq_DramaCopyStatus_name, SetDramaCopyStatusReq_DramaCopyStatus_value)
}

func init() { proto.RegisterFile("pia_.proto", fileDescriptor_pia__81139eb59425d195) }

var fileDescriptor_pia__81139eb59425d195 = []byte{
	// 6133 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x3d, 0x4b, 0x6c, 0x23, 0xc9,
	0x75, 0xdb, 0xfc, 0x48, 0xe4, 0xa3, 0x28, 0x51, 0x3d, 0xd2, 0xe8, 0x33, 0x3b, 0x3b, 0xb3, 0xed,
	0x5d, 0x7b, 0x3c, 0x5e, 0x6b, 0x6c, 0xd9, 0x63, 0x6c, 0xd6, 0x9f, 0x5d, 0x8a, 0xe2, 0x68, 0xe8,
	0x95, 0x48, 0xa6, 0x29, 0xcd, 0x78, 0x0c, 0x18, 0x9d, 0x56, 0xb3, 0x44, 0xb5, 0x45, 0x76, 0x73,
	0xbb, 0x9b, 0x33, 0xa3, 0x0d, 0x8c, 0xac, 0x63, 0x04, 0x5e, 0x23, 0x89, 0x63, 0x18, 0x01, 0x1c,
	0x03, 0x09, 0x10, 0x24, 0x06, 0x82, 0xc0, 0x06, 0x8c, 0x1c, 0x9c, 0x18, 0x89, 0x01, 0xc3, 0xbe,
	0xe4, 0x14, 0xf8, 0x90, 0x04, 0x41, 0x6e, 0x39, 0x24, 0x97, 0x5c, 0x72, 0x09, 0x92, 0xa3, 0x83,
	0x7a, 0x55, 0xdd, 0xac, 0xfe, 0x50, 0x52, 0x6b, 0xb4, 0x32, 0xec, 0xdc, 0xba, 0x5e, 0x55, 0x57,
	0xbd, 0xf7, 0xea, 0xd5, 0xfb, 0xd5, 0x6b, 0x12, 0x60, 0x68, 0xea, 0xda, 0xda, 0xd0, 0xb1, 0x3d,
	0x5b, 0x9e, 0xea, 0xe9, 0x6b, 0x43, 0x53, 0x5f, 0x2d, 0xf7, 0x74, 0x6d, 0x5f, 0x77, 0x09, 0x03,
	0x2b, 0x1a, 0x5c, 0xdd, 0x22, 0x5e, 0xed, 0x50, 0xb7, 0x2c, 0xd2, 0x6f, 0x9b, 0x7a, 0xc7, 0xd3,
	0xbd, 0x91, 0xab, 0x92, 0xb7, 0xe4, 0xf7, 0x43, 0x81, 0x8e, 0xd3, 0x1c, 0xf2, 0xd6, 0xb2, 0x74,
	0x53, 0xba, 0x55, 0x5a, 0x2f, 0xad, 0xf5, 0xf4, 0xb5, 0x0d, 0xdd, 0x25, 0x2a, 0x79, 0x4b, 0x9d,
	0xde, 0x67, 0x0f, 0xf2, 0x75, 0x00, 0x83, 0xbd, 0xae, 0x99, 0xdd, 0xe5, 0xcc, 0x4d, 0xe9, 0x56,
	0x59, 0x2d, 0x72, 0x48, 0xa3, 0xab, 0x7c, 0x53, 0x82, 0xa5, 0xc4, 0x15, 0xdc, 0xa1, 0xfc, 0x41,
	0x28, 0xf2, 0x25, 0xdc, 0x21, 0x5f, 0x63, 0x66, 0xbc, 0x86, 0x3b, 0x54, 0x0b, 0xfb, 0xfc, 0xe9,
	0x94, 0x55, 0xe4, 0x05, 0xc8, 0x13, 0xcb, 0x73, 0x8e, 0x97, 0xb3, 0x37, 0xa5, 0x5b, 0x05, 0x95,
	0x35, 0xe4, 0x25, 0x98, 0x36, 0x5d, 0xcd, 0x1e, 0x12, 0x6b, 0x39, 0x87, 0xf0, 0x29, 0xd3, 0x6d,
	0x0d, 0x89, 0xa5, 0x1c, 0x41, 0xa5, 0x43, 0xbc, 0x9a, 0x3d, 0x18, 0x12, 0x87, 0xec, 0x98, 0xc6,
	0xc5, 0xd1, 0x2b, 0x57, 0x20, 0x3b, 0x30, 0x0d, 0xc4, 0xa3, 0xac, 0xd2, 0x47, 0xe5, 0x33, 0x30,
	0x1f, 0x59, 0x2c, 0x15, 0xe9, 0xca, 0x5b, 0x30, 0xd7, 0x21, 0x1e, 0xe5, 0xdc, 0x13, 0xd3, 0x33,
	0x0e, 0x2f, 0x10, 0x57, 0x81, 0x3f, 0xd9, 0x10, 0x7f, 0x3e, 0x8d, 0xfc, 0x11, 0x96, 0x4c, 0x87,
	0xf1, 0xf7, 0x24, 0x28, 0x6d, 0x11, 0x6f, 0xd3, 0xd1, 0x07, 0x7a, 0x1a, 0x74, 0x7f, 0x0d, 0xca,
	0x2e, 0xd1, 0x1d, 0xe3, 0x50, 0xb3, 0x87, 0x9e, 0x69, 0x5b, 0xcb, 0x99, 0x9b, 0xd9, 0x5b, 0xa5,
	0xf5, 0x85, 0x35, 0x26, 0xbb, 0x6b, 0x1d, 0xec, 0x6c, 0x61, 0x9f, 0x3a, 0xe3, 0x0a, 0x2d, 0x4a,
	0xe9, 0x50, 0xef, 0x11, 0xcd, 0xb3, 0x8f, 0x38, 0x35, 0x65, 0xb5, 0x48, 0x21, 0xbb, 0x14, 0x20,
	0x5f, 0x03, 0x6c, 0x68, 0xae, 0xf9, 0x36, 0x41, 0x59, 0x28, 0xab, 0x05, 0x0a, 0xe8, 0x98, 0x6f,
	0x13, 0xe5, 0xdb, 0x19, 0xc8, 0x23, 0xae, 0xf2, 0x2c, 0x64, 0xcc, 0x2e, 0xa2, 0x58, 0x56, 0x33,
	0x26, 0x8a, 0x95, 0x67, 0x7a, 0x7d, 0x82, 0xac, 0x2b, 0xaa, 0xac, 0x41, 0x27, 0x33, 0xec, 0xc7,
	0xc4, 0xd1, 0x46, 0x4e, 0x1f, 0x97, 0x2a, 0xaa, 0x05, 0x04, 0xec, 0x39, 0x7d, 0xf9, 0x2a, 0x4c,
	0xe9, 0x23, 0xef, 0xd0, 0x76, 0x70, 0x99, 0xa2, 0xca, 0x5b, 0xb2, 0x0c, 0xb9, 0x2e, 0x71, 0x8d,
	0xe5, 0x3c, 0x42, 0xf1, 0x59, 0x5e, 0x81, 0xc2, 0x40, 0xef, 0x13, 0xcd, 0xb0, 0xbc, 0xe5, 0x29,
	0x5c, 0x74, 0x9a, 0xb6, 0x6b, 0x96, 0x47, 0xe9, 0x39, 0x20, 0x41, 0xe7, 0x34, 0xa3, 0x87, 0x41,
	0x68, 0xf7, 0x0a, 0x14, 0x9e, 0xd8, 0x4e, 0x17, 0x3b, 0x0b, 0xec, 0x4d, 0xda, 0xa6, 0x5d, 0x4b,
	0x30, 0xbd, 0xdf, 0x1b, 0x20, 0x6e, 0xc5, 0x9b, 0x59, 0x8a, 0xc1, 0x7e, 0x6f, 0x40, 0x31, 0x93,
	0x21, 0xe7, 0xe9, 0x3d, 0x77, 0x19, 0x10, 0x8a, 0xcf, 0xf2, 0x0d, 0x28, 0x1d, 0xea, 0xae, 0x36,
	0xec, 0xeb, 0xc7, 0xa6, 0xd5, 0x5b, 0x2e, 0xa1, 0x14, 0xc0, 0xa1, 0xee, 0xb6, 0x19, 0x44, 0xe9,
	0xc1, 0xcc, 0x78, 0x27, 0xd3, 0x1d, 0xd9, 0x57, 0x00, 0xba, 0xf4, 0x3d, 0xad, 0x6f, 0xba, 0x1e,
	0xdf, 0xca, 0xb2, 0xbf, 0x95, 0x6c, 0xc6, 0x22, 0x0e, 0xd8, 0x36, 0x5d, 0x4f, 0xa9, 0xa2, 0x9a,
	0x10, 0x77, 0x78, 0xcb, 0xb1, 0x47, 0xc3, 0x14, 0xe2, 0xa3, 0x7c, 0x55, 0xa2, 0x27, 0x2d, 0x32,
	0x01, 0xe5, 0x64, 0x8f, 0x3e, 0x68, 0x96, 0x3e, 0x20, 0xf8, 0x7e, 0x51, 0x2d, 0x22, 0xa4, 0xa9,
	0x0f, 0x88, 0xdc, 0x81, 0x25, 0x77, 0xb4, 0xaf, 0x85, 0xe4, 0x4e, 0xc3, 0x6e, 0x8e, 0xf2, 0xf3,
	0x81, 0xf4, 0x8d, 0xf6, 0xe3, 0xe8, 0x2d, 0xb8, 0x09, 0x50, 0xe5, 0x3f, 0x24, 0x58, 0x48, 0x1a,
	0x4e, 0xf9, 0xcd, 0x57, 0xf2, 0x8e, 0x87, 0x84, 0x4b, 0x1a, 0x30, 0xd0, 0xee, 0xf1, 0x90, 0x44,
	0xb0, 0xcd, 0x44, 0xb1, 0x8d, 0x9d, 0x90, 0xec, 0x99, 0x4f, 0xc8, 0xcb, 0x30, 0xdb, 0x35, 0x5d,
	0xba, 0xd3, 0xda, 0x81, 0xed, 0x0c, 0x74, 0x8f, 0x0b, 0x68, 0x99, 0x43, 0xef, 0x21, 0x50, 0xfe,
	0x10, 0xcc, 0xfb, 0xc3, 0x5c, 0x32, 0xd4, 0x1d, 0xdd, 0xb3, 0x1d, 0x2e, 0xb4, 0x15, 0xde, 0xd1,
	0xf1, 0xe1, 0xca, 0x37, 0x24, 0x58, 0x4e, 0xde, 0xb5, 0x74, 0xa2, 0xd2, 0x80, 0x2b, 0x93, 0x37,
	0x60, 0x25, 0x89, 0x38, 0xb6, 0xcc, 0xbc, 0x1b, 0x63, 0x7d, 0x1d, 0x66, 0xc4, 0x71, 0xa7, 0x73,
	0x7c, 0x01, 0xf2, 0x7d, 0x7d, 0x9f, 0xf4, 0xfd, 0x33, 0x8e, 0x0d, 0xe5, 0x0b, 0xb0, 0x40, 0xad,
	0xd6, 0xc8, 0x71, 0x88, 0x45, 0x15, 0x61, 0xc3, 0x3a, 0xb0, 0x2f, 0xd0, 0x2a, 0x7e, 0x57, 0x82,
	0x69, 0x3e, 0xab, 0xfc, 0x3e, 0xc8, 0xe3, 0x31, 0xe0, 0xf3, 0x45, 0x8e, 0x08, 0xeb, 0xa3, 0x58,
	0x0e, 0x0f, 0x75, 0x97, 0xf0, 0xa9, 0x58, 0x43, 0x5e, 0x85, 0xc2, 0xd0, 0xb1, 0x7b, 0x0e, 0x71,
	0x5d, 0x5f, 0x11, 0xf9, 0x6d, 0x4a, 0xb8, 0xc1, 0x6c, 0x8e, 0x46, 0x0d, 0x12, 0x53, 0x7a, 0x60,
	0x04, 0x66, 0x48, 0xbe, 0x05, 0x15, 0xbd, 0xdf, 0xa7, 0x9d, 0x5a, 0x30, 0x49, 0x1e, 0x75, 0xc3,
	0xac, 0xde, 0xef, 0xef, 0x98, 0x46, 0x9b, 0x43, 0x95, 0x3f, 0x94, 0x60, 0x31, 0x81, 0x1b, 0xe9,
	0xf6, 0xf8, 0x36, 0x14, 0xa8, 0x3b, 0x62, 0x5a, 0x07, 0x36, 0x12, 0x51, 0x5a, 0x9f, 0xf3, 0x29,
	0xf5, 0x67, 0x9c, 0x1e, 0x72, 0x96, 0xbc, 0x04, 0xb3, 0x03, 0xb7, 0x47, 0xe5, 0x54, 0x73, 0x0d,
	0x87, 0x70, 0x8d, 0x5e, 0x54, 0x67, 0x06, 0x6e, 0xef, 0x9e, 0xed, 0x74, 0x10, 0xa6, 0xbc, 0x2b,
	0xc1, 0x2c, 0x33, 0x53, 0xed, 0x43, 0xce, 0xf6, 0x0b, 0x32, 0x8c, 0x2b, 0x50, 0x60, 0xaa, 0xcb,
	0xec, 0x72, 0x5b, 0x32, 0x8d, 0x6d, 0xe6, 0x69, 0xb0, 0x8d, 0xc8, 0x09, 0x1b, 0xa1, 0x7c, 0xca,
	0xb7, 0xd1, 0x1c, 0x93, 0x74, 0xf6, 0xf2, 0xf7, 0x51, 0x71, 0xe1, 0xeb, 0x9c, 0xe5, 0x17, 0x48,
	0x8b, 0x28, 0x23, 0xb9, 0x88, 0x8c, 0x2c, 0xc2, 0x94, 0x3d, 0x44, 0xf1, 0xc8, 0x33, 0x6a, 0xec,
	0xe1, 0x8e, 0x69, 0x28, 0xaf, 0x83, 0x1c, 0x45, 0x27, 0xad, 0xcb, 0x52, 0xee, 0x10, 0x6f, 0xa3,
	0x37, 0xb8, 0xd8, 0x63, 0x43, 0xf7, 0x85, 0xda, 0x36, 0x94, 0x21, 0x26, 0x11, 0xd4, 0xd6, 0xd1,
	0x45, 0x94, 0x4f, 0xa2, 0x2c, 0x04, 0x4b, 0xa6, 0xc3, 0xf7, 0x01, 0x94, 0xb7, 0xde, 0x03, 0x7c,
	0x95, 0x07, 0x30, 0xbb, 0x75, 0x5e, 0xa4, 0x42, 0xc4, 0x66, 0xc2, 0xc4, 0x3e, 0x42, 0xed, 0xc4,
	0x6d, 0x34, 0x77, 0xad, 0xd3, 0xa0, 0x2d, 0xca, 0x77, 0x26, 0x24, 0xdf, 0xca, 0xff, 0x64, 0x40,
	0x0e, 0x4f, 0x8c, 0x27, 0x32, 0x4c, 0xa8, 0x14, 0xdd, 0x98, 0x17, 0x61, 0xc6, 0xef, 0x16, 0x0c,
	0x57, 0x89, 0xc3, 0xd0, 0x74, 0x7d, 0x04, 0x16, 0xfc, 0x21, 0x03, 0x32, 0xd8, 0x27, 0x8e, 0x66,
	0xd8, 0x23, 0xcb, 0xe3, 0xe7, 0x4b, 0xe6, 0x7d, 0x3b, 0xd8, 0x55, 0xa3, 0x3d, 0xf2, 0x2b, 0xe0,
	0x43, 0x35, 0xdf, 0x24, 0x99, 0x5d, 0x7e, 0xee, 0x2a, 0xbc, 0x67, 0x93, 0x75, 0x84, 0x51, 0x40,
	0x4d, 0xcf, 0x24, 0xda, 0x47, 0x01, 0x55, 0xbd, 0x30, 0xc4, 0x34, 0x6c, 0x0b, 0x7d, 0xae, 0x31,
	0x96, 0x0d, 0xc3, 0xb6, 0xc4, 0x21, 0xe8, 0xae, 0x4d, 0x87, 0x86, 0x6c, 0x52, 0xaf, 0xed, 0x13,
	0xb0, 0xe4, 0x0f, 0x31, 0x1c, 0x42, 0xed, 0xa0, 0xa6, 0x1b, 0x8c, 0x96, 0x02, 0x8e, 0x5e, 0xe4,
	0xdd, 0x35, 0xd6, 0x5b, 0x65, 0x9d, 0x94, 0x85, 0xe6, 0xd8, 0xd5, 0x2a, 0xa2, 0xab, 0x55, 0x34,
	0x03, 0x4f, 0xeb, 0x6b, 0x4c, 0xc9, 0x46, 0x37, 0x35, 0x9d, 0xcc, 0xd4, 0xa1, 0xc2, 0x17, 0xd0,
	0x38, 0x12, 0x2e, 0xb7, 0xa2, 0xab, 0x81, 0xb2, 0x8d, 0x6d, 0xae, 0x3a, 0x37, 0x0c, 0xc1, 0x5c,
	0xe5, 0x29, 0x14, 0x03, 0xe7, 0x5f, 0x8c, 0x12, 0x24, 0x31, 0x4a, 0x48, 0xa5, 0xd1, 0x7d, 0xf7,
	0x37, 0x2b, 0xb8, 0xbf, 0x32, 0xe4, 0x0e, 0x4d, 0xcb, 0xf7, 0x43, 0xf0, 0x59, 0x71, 0xe8, 0x31,
	0xee, 0x13, 0x23, 0x7d, 0xf0, 0x70, 0x6e, 0x95, 0xce, 0x94, 0xb7, 0xb0, 0x66, 0x3a, 0xdd, 0xf1,
	0x06, 0x46, 0xd0, 0xbf, 0x3e, 0xd2, 0xfb, 0xa6, 0x77, 0xbc, 0xe9, 0xfb, 0xb3, 0x69, 0xfc, 0xd6,
	0xdf, 0x61, 0x21, 0x72, 0x7c, 0x8a, 0x74, 0x7b, 0x7f, 0x0b, 0x72, 0x82, 0xa7, 0x1d, 0xb8, 0x84,
	0xe2, 0xb4, 0x2a, 0x8e, 0xa0, 0x6a, 0xdf, 0xd3, 0xf7, 0xc7, 0x9c, 0xc8, 0x7b, 0xfa, 0x7e, 0xa3,
	0xab, 0xfc, 0x0b, 0xc3, 0xa3, 0xed, 0xe8, 0x86, 0x67, 0x1a, 0xe4, 0x3c, 0xb4, 0x84, 0x03, 0x2d,
	0xba, 0x09, 0xf9, 0x71, 0xa0, 0x95, 0x10, 0xa4, 0x15, 0xc5, 0x20, 0x4d, 0x38, 0xef, 0x43, 0xdd,
	0x38, 0xa2, 0x23, 0xf9, 0x79, 0x2f, 0x06, 0xe7, 0xbd, 0xcd, 0x3a, 0x1a, 0x5d, 0xf9, 0x36, 0x64,
	0x5d, 0xf2, 0x14, 0xcf, 0xf0, 0xec, 0xfa, 0x72, 0xd8, 0x69, 0xb2, 0xed, 0xc1, 0x9e, 0x4b, 0x9c,
	0x0e, 0x79, 0xaa, 0xd2, 0x41, 0xca, 0xf7, 0x99, 0x9f, 0x9a, 0x40, 0x59, 0x3a, 0x16, 0x7f, 0x30,
	0xc4, 0xe2, 0xc5, 0x40, 0xda, 0xc5, 0x79, 0x39, 0x8f, 0xdf, 0x0f, 0x73, 0x16, 0x79, 0xea, 0x69,
	0x31, 0x82, 0xcb, 0x14, 0xdc, 0x0e, 0x88, 0x1e, 0xef, 0x45, 0x4e, 0xdc, 0x8b, 0xbf, 0xcc, 0x40,
	0x31, 0xa0, 0x25, 0x16, 0x97, 0xd2, 0x20, 0xf3, 0xb1, 0xee, 0xe9, 0x0e, 0x57, 0xb4, 0xbc, 0x45,
	0x4f, 0x14, 0xaa, 0x5f, 0x7e, 0xca, 0xe8, 0x33, 0xd5, 0x68, 0xf6, 0x13, 0x8b, 0x38, 0x1a, 0x7f,
	0x83, 0xf1, 0xb3, 0x84, 0xb0, 0x2a, 0x7b, 0xed, 0x3a, 0x00, 0x1b, 0x82, 0x2f, 0x33, 0x67, 0xbf,
	0x88, 0x10, 0xd4, 0xdc, 0x2b, 0x50, 0x18, 0xb9, 0xb4, 0x77, 0x34, 0x40, 0x76, 0xe7, 0xd5, 0x69,
	0xda, 0x6e, 0x8e, 0x06, 0x78, 0x84, 0x89, 0xce, 0x02, 0xd4, 0xbc, 0x8a, 0xcf, 0x41, 0x9c, 0xc9,
	0x94, 0x21, 0x8b, 0x33, 0x2b, 0x6c, 0xb3, 0x8a, 0x38, 0x8c, 0x3e, 0x52, 0xfe, 0xf8, 0x9b, 0xbd,
	0x6f, 0x5a, 0x5d, 0xca, 0x00, 0x40, 0xfa, 0xca, 0x1c, 0xbc, 0x61, 0x5a, 0xdd, 0x04, 0xb5, 0x5e,
	0x8a, 0xa9, 0x75, 0xe5, 0x37, 0xe1, 0x0a, 0xb2, 0x8a, 0x2a, 0x9a, 0x7b, 0xb6, 0x53, 0xed, 0xf5,
	0x28, 0x7b, 0x63, 0x4c, 0xf3, 0x99, 0x93, 0x11, 0x98, 0x43, 0x71, 0xa5, 0xb3, 0x72, 0x86, 0xd1,
	0xe7, 0x00, 0xff, 0x9c, 0x10, 0x27, 0x2f, 0xc3, 0xb4, 0x3b, 0x1a, 0x0c, 0x74, 0xe7, 0x98, 0xb3,
	0xc7, 0x6f, 0x2a, 0xff, 0x96, 0x81, 0xf9, 0x60, 0xa3, 0xa8, 0xc8, 0xa0, 0xba, 0xbb, 0x0b, 0x25,
	0xa6, 0x6d, 0x98, 0xaf, 0x28, 0xa1, 0x90, 0x2e, 0x08, 0xda, 0x11, 0xfd, 0x44, 0x8a, 0xbd, 0xca,
	0x82, 0x64, 0x6c, 0xcb, 0x6b, 0x50, 0x74, 0x6c, 0x7b, 0x20, 0xaa, 0xd4, 0xf9, 0x98, 0x64, 0xab,
	0x05, 0x3a, 0x06, 0x97, 0x79, 0xcd, 0x0f, 0xb1, 0x03, 0x8f, 0xa8, 0xb4, 0x7e, 0x2d, 0xf4, 0x42,
	0x98, 0x27, 0x3c, 0xe0, 0xc6, 0x77, 0x93, 0x05, 0x4f, 0x7e, 0x1d, 0x8a, 0x54, 0xa9, 0xbb, 0x9e,
	0xde, 0x63, 0xa2, 0x30, 0xbb, 0xae, 0xc4, 0x50, 0xf0, 0xe9, 0x5c, 0x63, 0xf9, 0xbc, 0x1e, 0x51,
	0xa9, 0x25, 0xc0, 0x27, 0xa5, 0x0d, 0x05, 0x1f, 0x2a, 0xcb, 0x30, 0xdb, 0x6e, 0x54, 0xb5, 0xce,
	0x6e, 0x75, 0xab, 0xae, 0x35, 0x5b, 0xcd, 0x7a, 0xe5, 0x39, 0x79, 0x05, 0x16, 0xc7, 0xb0, 0x4e,
	0x7d, 0xbb, 0x5e, 0xdb, 0xd5, 0xd4, 0xd6, 0x76, 0xbd, 0x22, 0xc9, 0x8b, 0x30, 0x3f, 0xee, 0x6a,
	0x6f, 0x57, 0x1f, 0x35, 0x9a, 0x5b, 0x95, 0x8c, 0x72, 0x0f, 0x66, 0x44, 0x25, 0x26, 0x7f, 0x82,
	0x1f, 0x58, 0x24, 0x9a, 0x1d, 0xd8, 0x95, 0x89, 0x28, 0xb2, 0xd3, 0x8b, 0x5e, 0xd3, 0x16, 0x94,
	0x43, 0x27, 0xf5, 0xdc, 0x13, 0xfd, 0x34, 0x0b, 0xb3, 0xd8, 0xdf, 0x72, 0xba, 0xc4, 0xa1, 0x8a,
	0x44, 0x7e, 0x95, 0x6b, 0x06, 0x09, 0x35, 0xc3, 0x4b, 0xa1, 0x59, 0x82, 0x51, 0x6b, 0x54, 0x29,
	0x61, 0x0b, 0x27, 0x64, 0x8a, 0xe2, 0x14, 0xbb, 0xb5, 0x0c, 0xd3, 0x8f, 0x89, 0xe3, 0xb2, 0x58,
	0x5f, 0xba, 0x95, 0x55, 0xfd, 0xe6, 0xea, 0x8f, 0x32, 0x50, 0x0e, 0x4d, 0x28, 0xbf, 0x06, 0xb3,
	0x4c, 0x1c, 0xdc, 0xd1, 0xbe, 0x48, 0xd4, 0x42, 0x08, 0x9d, 0xce, 0x68, 0x1f, 0x97, 0x9f, 0xe9,
	0x0a, 0x2d, 0x6a, 0xe5, 0xf1, 0x90, 0x07, 0x38, 0x4c, 0xd1, 0x66, 0xa3, 0x4b, 0x35, 0x3a, 0x3b,
	0xfd, 0x63, 0xc5, 0x82, 0xea, 0x00, 0x55, 0xc3, 0x0d, 0x28, 0x61, 0x67, 0x48, 0xb7, 0x00, 0x05,
	0x71, 0xd5, 0xe2, 0xeb, 0x0e, 0x7a, 0xfa, 0x99, 0x47, 0x86, 0xcb, 0x74, 0xc8, 0x53, 0xda, 0x65,
	0x5a, 0x5d, 0xf2, 0x94, 0x2e, 0x39, 0xc5, 0x48, 0xc3, 0x76, 0xa3, 0x2b, 0x7f, 0x06, 0x66, 0x7c,
	0xd7, 0x0a, 0xc9, 0x98, 0x0e, 0x4b, 0x76, 0xdb, 0xd4, 0x91, 0x12, 0xee, 0x60, 0x21, 0x35, 0x25,
	0x63, 0xdc, 0x90, 0x15, 0x28, 0x9b, 0xae, 0x66, 0xd8, 0xc3, 0x63, 0x8d, 0x85, 0xd6, 0x05, 0x74,
	0x5c, 0x4a, 0xa6, 0x5b, 0xb3, 0x87, 0x4c, 0x8a, 0x68, 0x8c, 0x82, 0x9c, 0x4b, 0xed, 0x68, 0x4c,
	0x76, 0x9e, 0x23, 0x7b, 0x99, 0x8d, 0x86, 0x03, 0x0e, 0xcc, 0x8a, 0x4b, 0xa6, 0xb3, 0x3d, 0x77,
	0x01, 0x6c, 0xfa, 0xb2, 0x9f, 0x4e, 0xa3, 0x63, 0xaf, 0x26, 0xcb, 0x99, 0x5a, 0xb4, 0xfd, 0x47,
	0x9e, 0xc8, 0x18, 0x2f, 0x9b, 0xd6, 0xa0, 0x9f, 0x12, 0xe1, 0x1c, 0xa3, 0xd3, 0x1a, 0x9d, 0xfe,
	0x52, 0x28, 0x7b, 0x47, 0x82, 0x2b, 0x9b, 0xa4, 0x4f, 0x3c, 0x72, 0xbe, 0x7d, 0xa4, 0x42, 0xc2,
	0xe5, 0x6f, 0x9c, 0xa2, 0xcc, 0xaa, 0x25, 0x2e, 0x84, 0xdb, 0xf1, 0xc3, 0x19, 0xdb, 0xd0, 0xa7,
	0xb0, 0x10, 0xc7, 0xe0, 0x52, 0x88, 0xff, 0xb9, 0x04, 0xb0, 0x63, 0x1a, 0xaa, 0xdd, 0x27, 0x3b,
	0xfa, 0x50, 0xfe, 0x30, 0x64, 0x07, 0xfa, 0x90, 0x6b, 0x9f, 0xe0, 0x9c, 0x8c, 0x07, 0xac, 0xed,
	0xe8, 0xc3, 0xba, 0xe5, 0x39, 0xc7, 0x2a, 0x1d, 0x27, 0x2a, 0x95, 0x4c, 0x48, 0xa9, 0x50, 0xe3,
	0xe7, 0x1e, 0x99, 0x43, 0x7e, 0x1f, 0x80, 0xcf, 0xab, 0x9f, 0x84, 0x19, 0x3a, 0x0d, 0x3d, 0x59,
	0xc8, 0x14, 0xdf, 0xb0, 0x52, 0xf3, 0x48, 0x0d, 0xeb, 0x35, 0x28, 0x7e, 0xd1, 0x36, 0x2d, 0xcd,
	0x33, 0xb9, 0x75, 0xcd, 0xaa, 0x05, 0x0a, 0xd8, 0x35, 0x07, 0x64, 0xf5, 0x21, 0x14, 0xfc, 0xb5,
	0xa9, 0x17, 0x70, 0x44, 0x8e, 0xb9, 0x49, 0xa6, 0x8f, 0xf2, 0x5d, 0xc8, 0x3f, 0xd6, 0xfb, 0x23,
	0xc2, 0x09, 0xbf, 0x91, 0x80, 0xb9, 0xb8, 0xb4, 0xca, 0x46, 0xbf, 0x96, 0x79, 0x55, 0x52, 0xfe,
	0x5e, 0x82, 0x0a, 0x35, 0x34, 0xe8, 0xb9, 0xd3, 0x41, 0x97, 0x72, 0x86, 0xa9, 0x9e, 0x74, 0xec,
	0xbe, 0xe0, 0x99, 0x4e, 0xd1, 0x26, 0x7b, 0x6f, 0x60, 0x1a, 0xd4, 0x49, 0xda, 0x27, 0x0e, 0xd7,
	0x75, 0xc5, 0x81, 0x69, 0x34, 0x11, 0x40, 0x57, 0x74, 0xec, 0x11, 0x73, 0x74, 0xb8, 0xb6, 0xc3,
	0x76, 0xa3, 0xab, 0x78, 0x30, 0x1f, 0x21, 0x24, 0x9d, 0x08, 0x7d, 0x1c, 0x66, 0xe8, 0xca, 0x88,
	0x16, 0x95, 0x02, 0xc6, 0x4b, 0x39, 0xce, 0x4b, 0x95, 0x62, 0xc8, 0x9f, 0x95, 0x7f, 0x90, 0xe0,
	0x6a, 0xdb, 0xd4, 0x6b, 0xba, 0x65, 0x90, 0xfe, 0xaf, 0x00, 0x17, 0xdf, 0x86, 0xa5, 0x44, 0x72,
	0x2e, 0x83, 0x97, 0xbb, 0xb0, 0xc0, 0x63, 0x67, 0x3c, 0xb1, 0x7e, 0xda, 0x0c, 0x6f, 0x8a, 0x46,
	0xd4, 0x86, 0x75, 0xc9, 0x53, 0x2e, 0xf6, 0x05, 0x63, 0xe4, 0x34, 0x68, 0x9b, 0xda, 0x4e, 0x7a,
	0x62, 0x34, 0xfb, 0xe0, 0xc0, 0x25, 0x1e, 0x3f, 0x38, 0x40, 0x41, 0x2d, 0x84, 0x28, 0x3f, 0xcc,
	0x82, 0x2c, 0x4e, 0xcb, 0x6e, 0x4e, 0xe5, 0x8f, 0x25, 0xf9, 0x96, 0x72, 0x48, 0x65, 0xb0, 0x2c,
	0xa4, 0xe8, 0x59, 0x9e, 0xe2, 0x65, 0xac, 0x25, 0x38, 0x92, 0x73, 0xa1, 0x29, 0x1f, 0xac, 0x8b,
	0xce, 0xa3, 0xa0, 0x40, 0x72, 0x61, 0x05, 0xf2, 0xaa, 0x90, 0x6e, 0xcc, 0xe3, 0x3c, 0xc1, 0x05,
	0x4a, 0x12, 0x8b, 0x84, 0x64, 0xe4, 0x75, 0x00, 0xd7, 0xd3, 0x1d, 0x8f, 0xe9, 0x11, 0xb6, 0xbb,
	0x45, 0x84, 0x50, 0x45, 0x12, 0xda, 0xfa, 0xe9, 0xd0, 0xd6, 0x07, 0x4a, 0xab, 0x30, 0x56, 0x5a,
	0x72, 0x1d, 0x66, 0xfc, 0x4c, 0x08, 0x7a, 0xf8, 0x45, 0xc4, 0x45, 0x11, 0x5c, 0x88, 0x10, 0x3a,
	0x6c, 0x28, 0x3a, 0xe4, 0xa5, 0xe1, 0xb8, 0x21, 0xdf, 0x85, 0x25, 0x43, 0xb7, 0x30, 0x99, 0xd2,
	0x23, 0x5a, 0x68, 0x46, 0xc0, 0xd5, 0x16, 0x0c, 0xdd, 0xaa, 0x61, 0xaf, 0x30, 0x87, 0xf2, 0x07,
	0x12, 0x54, 0x84, 0x9c, 0xc2, 0x83, 0xf5, 0xcb, 0x39, 0x56, 0xa2, 0x4b, 0x95, 0x0b, 0xb9, 0x54,
	0xca, 0x97, 0x60, 0x3e, 0x82, 0x50, 0xba, 0x83, 0xf1, 0x69, 0x98, 0xe1, 0xbe, 0x25, 0x4a, 0x21,
	0x3f, 0x18, 0xab, 0x49, 0x7b, 0xcb, 0x6f, 0xf8, 0x99, 0x94, 0xb2, 0x86, 0xf2, 0x6e, 0x06, 0xe4,
	0xb6, 0xa9, 0xb7, 0x86, 0xc4, 0xd1, 0x3d, 0x72, 0xd1, 0xc9, 0x9d, 0x2a, 0xcc, 0xda, 0x38, 0xb3,
	0x69, 0x5b, 0x5a, 0x10, 0xd0, 0xcd, 0x8e, 0xd1, 0x63, 0x86, 0xd4, 0x1f, 0x82, 0xdb, 0x5c, 0xb6,
	0xc5, 0xa6, 0xbc, 0x06, 0x57, 0x38, 0x7d, 0xc4, 0xc0, 0x69, 0xd8, 0xa1, 0x65, 0xb1, 0xd1, 0x3c,
	0x23, 0x85, 0xf5, 0xb0, 0xd3, 0x7b, 0x1d, 0xa0, 0x4b, 0xfa, 0xfa, 0x31, 0x93, 0x56, 0xae, 0xa8,
	0x10, 0x12, 0x93, 0xd6, 0x88, 0xa2, 0xfa, 0x2d, 0xb8, 0x12, 0xe3, 0xc4, 0xa5, 0xee, 0xc5, 0xff,
	0x66, 0xe0, 0xaa, 0x38, 0x86, 0x9e, 0xe8, 0x4d, 0xe2, 0xe9, 0x66, 0xff, 0xb4, 0x34, 0xef, 0xb3,
	0x2d, 0x2c, 0xbf, 0x01, 0x15, 0xf6, 0xfa, 0x7e, 0x6f, 0xe0, 0x4f, 0x91, 0x4d, 0xf0, 0x78, 0x36,
	0x7a, 0x03, 0xfe, 0x3a, 0x8b, 0x67, 0x82, 0x76, 0x4c, 0x3d, 0xe7, 0xce, 0xa2, 0x9e, 0xe5, 0xcf,
	0xc2, 0xc2, 0x78, 0xdd, 0xc7, 0x76, 0xdf, 0x5f, 0x3b, 0x9f, 0x10, 0xf2, 0x6d, 0xf4, 0x06, 0x0f,
	0xec, 0x3e, 0x5f, 0x7e, 0xbe, 0x1b, 0x05, 0xc9, 0x9f, 0x84, 0x79, 0xdb, 0x31, 0x7b, 0xa6, 0xa5,
	0x09, 0x0a, 0x73, 0x2a, 0x59, 0x61, 0xce, 0xb1, 0x91, 0x01, 0x93, 0xa9, 0x33, 0xde, 0x36, 0x75,
	0xff, 0x42, 0xfd, 0xc2, 0x6b, 0x6d, 0xbe, 0x2e, 0xc1, 0x62, 0xc2, 0xfc, 0xe9, 0x84, 0xeb, 0xb3,
	0x30, 0x3f, 0xa6, 0x4c, 0xeb, 0xa2, 0x5c, 0xf0, 0x8d, 0x7e, 0x21, 0x69, 0xa3, 0xc7, 0xd2, 0xa3,
	0xce, 0x75, 0xc3, 0x00, 0xe5, 0x20, 0x44, 0x2f, 0x8d, 0xbd, 0x1a, 0xdd, 0x34, 0xf4, 0xbe, 0x1f,
	0xe6, 0xc2, 0xcc, 0xf6, 0x89, 0x2e, 0x8b, 0x9c, 0xed, 0x2a, 0x07, 0x21, 0xba, 0xfd, 0x75, 0xd2,
	0xd1, 0xad, 0x40, 0x79, 0x1c, 0x30, 0x8e, 0x57, 0x2a, 0x19, 0x7e, 0xc4, 0xc8, 0xa2, 0x1d, 0x6a,
	0x38, 0x68, 0xa4, 0x49, 0x82, 0xa5, 0x2e, 0x36, 0x49, 0x1d, 0x68, 0x8d, 0x6c, 0x58, 0x6b, 0xf4,
	0x98, 0xb7, 0x16, 0x5d, 0xfa, 0xe2, 0x69, 0xfc, 0xb1, 0xc4, 0x93, 0x1b, 0xe3, 0x53, 0xb7, 0x08,
	0x53, 0x78, 0x13, 0xd5, 0xe5, 0xe5, 0x13, 0xf9, 0xfd, 0xde, 0xa0, 0xd1, 0x95, 0xd7, 0xa1, 0x48,
	0xc1, 0xe3, 0x7b, 0xe9, 0xd9, 0x71, 0x4a, 0x94, 0xcd, 0xb0, 0xb5, 0xc3, 0x3c, 0x91, 0xc2, 0x7e,
	0x6f, 0xc0, 0xfc, 0x90, 0x17, 0x61, 0x06, 0xdf, 0x11, 0x6f, 0xad, 0xb3, 0x6a, 0x89, 0xf6, 0xfb,
	0x7e, 0xc0, 0x64, 0xdf, 0xe2, 0xe5, 0x90, 0x9a, 0xf7, 0xf5, 0x6e, 0x56, 0x54, 0xe5, 0xe6, 0x80,
	0x28, 0x2f, 0xf3, 0x8c, 0x5c, 0xe8, 0xdc, 0x56, 0x20, 0xfb, 0xd8, 0xee, 0xf3, 0xfc, 0x25, 0x7d,
	0x54, 0xbe, 0x9d, 0xc1, 0x00, 0x82, 0x2b, 0xe2, 0x8d, 0xde, 0xe0, 0x02, 0x37, 0x72, 0x73, 0x82,
	0x41, 0xba, 0x1e, 0xe5, 0xcf, 0x89, 0x36, 0x69, 0xcc, 0xf7, 0x9c, 0xc8, 0xf7, 0x17, 0x61, 0x86,
	0x7a, 0x95, 0x21, 0x37, 0x2b, 0xab, 0x96, 0x8c, 0x91, 0x13, 0xf0, 0xf0, 0x7d, 0x50, 0x66, 0xd9,
	0x67, 0x7f, 0x0c, 0xb3, 0x41, 0x33, 0x98, 0x7b, 0xf6, 0x07, 0x4d, 0xf6, 0xa8, 0x94, 0x11, 0x86,
	0x24, 0x22, 0x6b, 0x52, 0x47, 0xb5, 0x82, 0x8e, 0xcf, 0x9c, 0xa8, 0xe3, 0xa9, 0x10, 0x71, 0xcb,
	0x64, 0x89, 0xa6, 0x91, 0x6d, 0xdf, 0xc5, 0x96, 0xe6, 0x51, 0x11, 0xc8, 0x8e, 0x45, 0xe0, 0xb7,
	0x25, 0x54, 0x50, 0x91, 0x05, 0xd3, 0x91, 0xfa, 0x3a, 0xcc, 0x46, 0xcc, 0x4a, 0xe6, 0x34, 0xb3,
	0x42, 0x8f, 0x40, 0xd0, 0x52, 0x7e, 0xc8, 0x6a, 0x41, 0xa8, 0xb1, 0x12, 0x72, 0xd6, 0xc5, 0x89,
	0x39, 0x6b, 0x9e, 0x4b, 0xe7, 0x15, 0x86, 0x2e, 0x79, 0x2a, 0x5c, 0x07, 0xe4, 0x42, 0xd7, 0x01,
	0x0a, 0xcc, 0x98, 0x96, 0xe7, 0xd8, 0xdd, 0x11, 0x3a, 0x2e, 0x3c, 0x75, 0x1d, 0x82, 0xc9, 0x0b,
	0x90, 0x37, 0xec, 0xbe, 0xed, 0xf0, 0xcb, 0x50, 0xd6, 0x60, 0xc5, 0x42, 0x7a, 0xdf, 0xee, 0x8d,
	0x88, 0x86, 0x02, 0x89, 0x02, 0x22, 0xa9, 0x65, 0x1f, 0xaa, 0x52, 0xa0, 0xb2, 0x05, 0xa5, 0xb6,
	0xa9, 0x6f, 0x8e, 0x98, 0xcc, 0x52, 0xfe, 0xef, 0x93, 0x9e, 0x9f, 0x09, 0x90, 0x98, 0x07, 0x8f,
	0x10, 0xdf, 0x27, 0x22, 0x56, 0x57, 0x4c, 0x13, 0x4c, 0x13, 0xab, 0x8b, 0x47, 0xf6, 0x07, 0x12,
	0x00, 0x55, 0x6f, 0xb6, 0xe5, 0x11, 0xcb, 0x8b, 0xb1, 0x41, 0x08, 0x17, 0x33, 0xa1, 0x70, 0x71,
	0x15, 0x0a, 0x3e, 0x46, 0x7e, 0x6e, 0xd2, 0x6f, 0xcb, 0x77, 0xa0, 0xd0, 0xe5, 0x98, 0x71, 0x3f,
	0xe1, 0x8a, 0x98, 0x40, 0xe4, 0x5d, 0x6a, 0x30, 0x88, 0x46, 0x6b, 0xb8, 0x8a, 0x70, 0x0b, 0x52,
	0xa0, 0x00, 0xcc, 0x74, 0x26, 0xf2, 0x49, 0xd1, 0x10, 0xed, 0xb6, 0x69, 0x78, 0x23, 0x27, 0xbe,
	0x7b, 0x15, 0xc8, 0x8e, 0x1c, 0xbf, 0xb0, 0x88, 0x3e, 0x86, 0x70, 0xca, 0x9e, 0x01, 0x27, 0xe5,
	0xab, 0x12, 0x4c, 0xb5, 0x4d, 0xaa, 0x29, 0xce, 0x2a, 0x1b, 0xe3, 0xa2, 0xc4, 0xd8, 0x8a, 0x67,
	0xe2, 0xc2, 0x55, 0x98, 0xea, 0x13, 0xab, 0xe7, 0x1d, 0x72, 0xa7, 0x96, 0xb7, 0x94, 0xef, 0x64,
	0x61, 0x46, 0xcc, 0x1f, 0xff, 0x4a, 0x14, 0x4b, 0xae, 0x40, 0xc1, 0xd3, 0x7b, 0x2c, 0xf7, 0xc6,
	0xaa, 0x25, 0xa7, 0x3d, 0xbd, 0x87, 0x59, 0x2e, 0xff, 0x6a, 0x08, 0x84, 0xab, 0x21, 0xea, 0xf4,
	0x8f, 0x2b, 0x11, 0x4a, 0x37, 0xa5, 0x5b, 0x39, 0xb5, 0xd8, 0x0d, 0x4a, 0x10, 0xae, 0x41, 0x91,
	0x11, 0x40, 0x7b, 0x67, 0x58, 0xb8, 0xcf, 0x00, 0x5c, 0x54, 0xfd, 0x8d, 0x28, 0xb3, 0xbe, 0x80,
	0xe7, 0x37, 0x80, 0xa5, 0xaf, 0x09, 0x3b, 0x1c, 0xb3, 0x2c, 0x15, 0xc0, 0x40, 0x78, 0x74, 0x6e,
	0x41, 0xc5, 0x21, 0x7d, 0xdd, 0x23, 0xdd, 0xb1, 0xed, 0x9e, 0xc3, 0x49, 0x66, 0x39, 0xdc, 0x37,
	0xdf, 0x5f, 0x93, 0x84, 0xdb, 0xb0, 0x87, 0xa6, 0x77, 0xc8, 0xed, 0xdf, 0xb3, 0xdc, 0x0d, 0xac,
	0x40, 0xc1, 0xaf, 0x12, 0xc5, 0xcd, 0x2d, 0xa8, 0xd3, 0xbc, 0x44, 0x94, 0x92, 0xec, 0x7a, 0xa6,
	0x71, 0xa4, 0x79, 0x7a, 0xcf, 0xdf, 0x5e, 0x04, 0xec, 0xea, 0x3d, 0xe5, 0x4f, 0xb3, 0x30, 0xcd,
	0x7d, 0xe1, 0x67, 0x5a, 0xff, 0x15, 0x7e, 0x30, 0x85, 0xbb, 0x57, 0xb1, 0xd2, 0x00, 0xf3, 0x3c,
	0x78, 0x52, 0x71, 0xe3, 0xee, 0xc2, 0x8c, 0xc1, 0xf4, 0x08, 0x7b, 0x81, 0x95, 0x48, 0xca, 0x62,
	0xe4, 0xcf, 0xba, 0xa9, 0xdf, 0x83, 0x0f, 0xf8, 0xda, 0x07, 0xc6, 0x75, 0xb3, 0x39, 0x7c, 0x63,
	0x56, 0x78, 0x63, 0x63, 0x6b, 0x27, 0xa8, 0xa3, 0xbd, 0x0b, 0x33, 0x43, 0x76, 0xe0, 0xd9, 0xfc,
	0xf9, 0xd8, 0xfc, 0x5c, 0x1f, 0xa8, 0x25, 0x3e, 0x0e, 0xe7, 0xff, 0x08, 0x14, 0x51, 0x70, 0x50,
	0xa8, 0xd8, 0xad, 0xb5, 0x78, 0x12, 0x29, 0x37, 0xd1, 0x07, 0x28, 0x0c, 0xf9, 0xd3, 0xa5, 0xdc,
	0x82, 0xfc, 0xb7, 0x04, 0x2b, 0x49, 0x15, 0x9c, 0xe9, 0x32, 0x16, 0xbf, 0x01, 0x72, 0xb8, 0x7e,
	0x13, 0x89, 0x64, 0x2e, 0xe1, 0xba, 0x8f, 0xef, 0xc4, 0x65, 0x42, 0x85, 0x9d, 0xc8, 0x83, 0x8a,
	0x1b, 0x81, 0x28, 0x3b, 0x50, 0x89, 0x8e, 0x92, 0x9f, 0x87, 0xe5, 0x4e, 0xbd, 0xaa, 0xd6, 0xee,
	0x6b, 0xad, 0xf6, 0x6e, 0xa3, 0xd5, 0xd4, 0x76, 0x1f, 0xb5, 0xeb, 0xda, 0xa6, 0x5a, 0xdd, 0xa9,
	0x56, 0x9e, 0x93, 0xaf, 0xc1, 0x52, 0x42, 0x6f, 0xad, 0xd5, 0x7e, 0x54, 0x91, 0x94, 0x6f, 0x4a,
	0xb0, 0x3a, 0x09, 0x9f, 0x5f, 0x58, 0xe9, 0xea, 0xcf, 0x24, 0x98, 0xf3, 0x63, 0x98, 0xb4, 0xd7,
	0x34, 0x17, 0x5a, 0x3a, 0x5f, 0x3c, 0x6b, 0xe9, 0x7c, 0xc4, 0xe3, 0xca, 0x47, 0x03, 0xd2, 0xbf,
	0x91, 0xa0, 0x12, 0xa6, 0x28, 0x1d, 0x73, 0x1f, 0xc1, 0x35, 0x21, 0x16, 0x7d, 0x62, 0x7a, 0x87,
	0xdc, 0xc7, 0x12, 0x55, 0x41, 0xfc, 0xc2, 0x7b, 0xac, 0xf6, 0xd4, 0xa5, 0x6e, 0x1c, 0xe8, 0xdf,
	0xec, 0x9c, 0x40, 0xb5, 0xd2, 0xc2, 0x7b, 0x2d, 0x9c, 0x91, 0xc5, 0xb2, 0x1b, 0x29, 0x43, 0x57,
	0x66, 0x1d, 0x33, 0xbe, 0x75, 0x54, 0xbe, 0x2c, 0xa1, 0xaf, 0xb4, 0xe7, 0xf2, 0xbb, 0x5a, 0x6a,
	0xa9, 0x03, 0xf3, 0x49, 0x1f, 0xa9, 0x81, 0xb0, 0x4c, 0xe3, 0x48, 0xb0, 0xe9, 0x41, 0x1b, 0x0d,
	0xa5, 0x61, 0xd8, 0x23, 0x8f, 0x63, 0xca, 0x5b, 0xf4, 0x9d, 0x91, 0x4b, 0x1c, 0x7c, 0x27, 0x37,
	0xbe, 0x9b, 0x15, 0xfd, 0xc4, 0x7c, 0x50, 0x73, 0xa1, 0xfc, 0x5c, 0xc2, 0x5a, 0xa5, 0x18, 0x55,
	0xe9, 0x36, 0xe5, 0x65, 0xbf, 0x5e, 0x39, 0x93, 0x9c, 0xf5, 0xe0, 0x15, 0xcb, 0x58, 0x7f, 0xdc,
	0xef, 0x13, 0xc3, 0xc3, 0xc2, 0x91, 0xac, 0x5f, 0x7f, 0x8c, 0xa0, 0xe6, 0x68, 0x40, 0xc3, 0x18,
	0x54, 0x4f, 0x08, 0x20, 0x5d, 0xfe, 0x89, 0x0e, 0x6a, 0x27, 0x0e, 0x0a, 0x19, 0x9e, 0x7c, 0xd8,
	0xf0, 0x7c, 0x1c, 0x4a, 0xbe, 0xad, 0x1d, 0x67, 0x60, 0x44, 0x85, 0xea, 0x73, 0x5a, 0x05, 0x6e,
	0x82, 0xad, 0x03, 0x5b, 0xf9, 0x59, 0x16, 0x16, 0xe2, 0x05, 0x70, 0x0f, 0xd6, 0xff, 0xdf, 0xd4,
	0x37, 0x6e, 0xc3, 0x15, 0x7f, 0x08, 0x3f, 0x49, 0x38, 0xd9, 0x34, 0xaa, 0xea, 0xe7, 0xe3, 0xd9,
	0x71, 0x76, 0x62, 0x50, 0x29, 0xcf, 0x1b, 0x51, 0x50, 0xac, 0x5a, 0xb2, 0x90, 0xaa, 0x5a, 0xb2,
	0x78, 0x52, 0xb5, 0xe4, 0x19, 0xeb, 0x83, 0x94, 0x6f, 0x31, 0x99, 0x0e, 0x6f, 0xea, 0x45, 0xe6,
	0xdb, 0xcf, 0xab, 0x1b, 0x95, 0xef, 0xf1, 0x72, 0xba, 0x18, 0x66, 0xe9, 0x8e, 0xdb, 0xd6, 0xc4,
	0x92, 0xce, 0xe7, 0x27, 0x97, 0x74, 0x3e, 0x58, 0x8f, 0x15, 0x75, 0x9e, 0xa6, 0xf1, 0x7e, 0x2a,
	0xf9, 0xc9, 0x3a, 0x55, 0xb7, 0x8e, 0x4c, 0xab, 0x97, 0xd6, 0x04, 0xdd, 0x83, 0x79, 0x87, 0xbd,
	0x89, 0xda, 0x59, 0xf4, 0x01, 0x56, 0x45, 0x6f, 0x6d, 0x3c, 0x3b, 0x8a, 0xd5, 0x9c, 0x13, 0x06,
	0x3c, 0x13, 0xcf, 0x7f, 0x14, 0x64, 0x40, 0x43, 0x44, 0xfc, 0xb2, 0x58, 0x9d, 0x2f, 0x4b, 0xb0,
	0xb8, 0x69, 0x53, 0xcd, 0xc5, 0x53, 0x7c, 0xa8, 0x08, 0x2f, 0x48, 0x96, 0x5f, 0x86, 0x8c, 0x3d,
	0xe4, 0x79, 0xa8, 0x20, 0x4f, 0xc7, 0x97, 0x68, 0x0d, 0x71, 0x2f, 0x32, 0xf6, 0x90, 0x46, 0x9c,
	0x57, 0x93, 0x70, 0x48, 0xc7, 0xc3, 0x88, 0xf6, 0xcf, 0x9c, 0xaa, 0xfd, 0xb3, 0x31, 0xed, 0xaf,
	0xfc, 0x2d, 0xf3, 0x4d, 0xa3, 0xa8, 0x50, 0x0f, 0xe6, 0x72, 0x3c, 0xa3, 0x90, 0xa8, 0x65, 0xe3,
	0xae, 0x8f, 0xb0, 0x95, 0xb9, 0xe8, 0x56, 0xfe, 0x19, 0xf3, 0x30, 0x13, 0x91, 0x4f, 0xc7, 0xca,
	0xd7, 0x12, 0xbe, 0xa3, 0x3b, 0x51, 0xfa, 0xc6, 0x5f, 0xd5, 0x9d, 0x26, 0x6f, 0x7f, 0x3c, 0x05,
	0xab, 0x93, 0x6f, 0x42, 0x65, 0x1b, 0x56, 0xdd, 0xd1, 0x70, 0x68, 0x3b, 0x5e, 0xe8, 0xe6, 0x13,
	0xaf, 0x5f, 0x5c, 0x2c, 0x00, 0x11, 0xdc, 0xfb, 0xc9, 0xf3, 0xac, 0x89, 0xb7, 0xab, 0x34, 0x56,
	0x5b, 0xe2, 0xb3, 0x46, 0xe0, 0xee, 0xc4, 0x05, 0x69, 0x54, 0xcc, 0xb4, 0x5e, 0xea, 0x05, 0x69,
	0xf8, 0x9c, 0xb4, 0x20, 0x85, 0xbb, 0xf2, 0x00, 0x56, 0x0c, 0xf6, 0x55, 0x53, 0x9c, 0x42, 0x7e,
	0x54, 0xce, 0x43, 0xe0, 0x55, 0x3e, 0x69, 0x04, 0x3e, 0x71, 0x39, 0x8c, 0xfa, 0x73, 0xe7, 0x59,
	0x0e, 0xc9, 0x4b, 0x58, 0x0e, 0xb3, 0x6a, 0x7f, 0x22, 0xc1, 0x5c, 0x14, 0x85, 0x3b, 0xf0, 0xa1,
	0x76, 0xa3, 0xaa, 0xd5, 0xee, 0x57, 0x9b, 0xcd, 0xfa, 0x36, 0x8b, 0x96, 0xfc, 0x5a, 0x4b, 0x16,
	0x22, 0xa9, 0xad, 0xed, 0xba, 0xd6, 0x68, 0x3e, 0xa8, 0x6e, 0x37, 0x36, 0x2b, 0xcf, 0xc9, 0xaf,
	0xc0, 0xad, 0xb3, 0xbc, 0x80, 0xd5, 0x9d, 0x92, 0xfc, 0x21, 0xf8, 0xc0, 0x59, 0x46, 0xdf, 0xaf,
	0x76, 0x2a, 0x99, 0x28, 0x7e, 0x98, 0xe9, 0x38, 0x1d, 0xbf, 0xdd, 0xc6, 0x4e, 0x3a, 0xfc, 0xf0,
	0x85, 0x33, 0xe3, 0x87, 0xa3, 0x19, 0x7e, 0xff, 0x99, 0xc5, 0x34, 0xf8, 0xf8, 0x7a, 0xdf, 0xb7,
	0x4f, 0x27, 0xb9, 0x8a, 0x93, 0x6b, 0xab, 0x5e, 0x8d, 0x7c, 0xad, 0x77, 0xde, 0xd2, 0x88, 0xdc,
	0x49, 0xa5, 0x11, 0xf9, 0x70, 0x69, 0x44, 0xb4, 0x0c, 0x62, 0xea, 0x7c, 0x65, 0x10, 0xd1, 0x7b,
	0xd7, 0xe9, 0x33, 0xdd, 0xbb, 0x86, 0x6f, 0x01, 0x0a, 0x67, 0xbc, 0x05, 0x48, 0xc8, 0xa8, 0x17,
	0x53, 0x65, 0xd4, 0xa3, 0x15, 0x32, 0x70, 0x96, 0x0a, 0x19, 0xe5, 0x5f, 0x99, 0xff, 0x13, 0xde,
	0xec, 0x34, 0x86, 0x46, 0x83, 0xd5, 0xc0, 0x19, 0x66, 0xab, 0x8b, 0x8c, 0xcf, 0x9c, 0x99, 0xf1,
	0xbe, 0xbf, 0x1c, 0x53, 0xc7, 0xa7, 0x17, 0x7f, 0x04, 0x52, 0x90, 0x0b, 0x5f, 0xe7, 0xfc, 0x39,
	0x73, 0x8b, 0xa2, 0xb4, 0xa5, 0xb3, 0x43, 0xef, 0x35, 0x7d, 0xca, 0xdf, 0x31, 0x93, 0xb9, 0x73,
	0x2c, 0xf6, 0xa8, 0xc4, 0xb0, 0x9d, 0xee, 0x33, 0x7d, 0x82, 0x52, 0x3e, 0xfb, 0x27, 0x28, 0x31,
	0x67, 0x21, 0x77, 0x56, 0x67, 0x41, 0xf9, 0x89, 0x04, 0xd7, 0x26, 0x62, 0x9f, 0x8e, 0xd3, 0x9f,
	0x83, 0x95, 0x30, 0x87, 0x1d, 0x9c, 0x46, 0x74, 0x00, 0xae, 0x47, 0xb3, 0x80, 0xe1, 0x05, 0xaf,
	0x76, 0x63, 0xb0, 0xb3, 0xf8, 0x03, 0x3f, 0x66, 0x72, 0x12, 0x9f, 0xf0, 0x99, 0xd2, 0xb7, 0x2f,
	0x0a, 0x3a, 0x68, 0x7c, 0xf7, 0x13, 0xe8, 0x17, 0xaa, 0xc1, 0x5e, 0x80, 0x92, 0xe9, 0x6a, 0xc3,
	0x51, 0xbf, 0xaf, 0xd9, 0x07, 0x07, 0xdc, 0x19, 0x2c, 0x9a, 0x6e, 0x7b, 0xd4, 0xef, 0xb7, 0x0e,
	0x0e, 0x78, 0x36, 0x25, 0x27, 0x5e, 0x08, 0xf1, 0xd4, 0x26, 0xcf, 0x0b, 0x4c, 0xb1, 0xa4, 0xa6,
	0x32, 0x84, 0x9b, 0x6d, 0x53, 0xdf, 0xd0, 0x3d, 0xe3, 0x90, 0x55, 0xe6, 0xee, 0x1c, 0x9f, 0x5b,
	0x90, 0x5e, 0x82, 0x59, 0xce, 0x78, 0xb1, 0x42, 0xb8, 0xa8, 0xce, 0x30, 0x28, 0x2b, 0x11, 0x56,
	0x9a, 0xf0, 0xe2, 0x29, 0x2b, 0xa6, 0xfb, 0x9e, 0xec, 0x2b, 0x12, 0xbc, 0xc0, 0x42, 0x98, 0xc8,
	0x44, 0x6c, 0xbd, 0xcb, 0x71, 0x7d, 0x15, 0x07, 0x6e, 0x9c, 0x88, 0x44, 0x3a, 0x81, 0x3e, 0x1b,
	0x27, 0x3f, 0x8f, 0xb7, 0xbf, 0x55, 0xcc, 0xd6, 0x6c, 0x11, 0x2b, 0xed, 0xb7, 0xb8, 0xa1, 0xdb,
	0x97, 0x4c, 0xf8, 0xf6, 0x45, 0xf9, 0x5a, 0x06, 0xb5, 0x7b, 0x64, 0xf2, 0x74, 0x54, 0x9c, 0xb4,
	0x40, 0x28, 0x7b, 0x97, 0x8d, 0x64, 0xef, 0x6a, 0x70, 0x85, 0xbf, 0xb8, 0x6f, 0x5a, 0x5d, 0x7a,
	0x0e, 0xf0, 0x04, 0xe5, 0x26, 0xe7, 0xac, 0xe6, 0xd9, 0xf8, 0x0d, 0x36, 0x1c, 0x4f, 0xd1, 0x0d,
	0xdf, 0xa8, 0xb1, 0xe4, 0x09, 0x4b, 0xff, 0x30, 0x03, 0xc6, 0xd2, 0x49, 0x1f, 0x80, 0xb9, 0x20,
	0x9c, 0xe2, 0x83, 0xd8, 0x3d, 0xd9, 0x6c, 0x00, 0xc6, 0x81, 0xd4, 0xd2, 0x2d, 0x06, 0xbc, 0x78,
	0x68, 0x3b, 0x47, 0xee, 0x39, 0x3e, 0x0a, 0x10, 0xd4, 0x48, 0xe6, 0xc4, 0x10, 0x3d, 0x1a, 0x37,
	0x85, 0xb8, 0x98, 0x8b, 0x70, 0xf1, 0x75, 0x28, 0xba, 0xd4, 0xfb, 0x0f, 0x32, 0x5c, 0xb3, 0x21,
	0x93, 0x12, 0x41, 0xb9, 0x63, 0x3b, 0x2c, 0x87, 0x50, 0x70, 0xf9, 0x93, 0xf2, 0x17, 0xac, 0xaa,
	0x39, 0x46, 0xdb, 0xe5, 0x85, 0x5c, 0x67, 0xfc, 0xf0, 0x4f, 0xf9, 0x02, 0x4a, 0x7b, 0xf4, 0x06,
	0x27, 0x65, 0x5e, 0x78, 0x19, 0xa6, 0xfd, 0x6c, 0x1a, 0xff, 0x1e, 0x9e, 0x37, 0x95, 0xff, 0x92,
	0x58, 0x68, 0x17, 0x54, 0xf6, 0x5c, 0xdc, 0x75, 0x60, 0xf4, 0x5e, 0x2a, 0x93, 0xf2, 0x5e, 0xea,
	0x1a, 0x14, 0x51, 0xd9, 0xef, 0xf7, 0xf9, 0x0f, 0x25, 0x15, 0xd4, 0x02, 0x55, 0xf5, 0xb4, 0xcd,
	0x3b, 0xbb, 0xa8, 0x49, 0x79, 0x4a, 0xb8, 0x60, 0xba, 0x4c, 0xb3, 0xf2, 0x6f, 0x91, 0x42, 0x27,
	0xa0, 0x30, 0x72, 0x09, 0x13, 0xeb, 0x7f, 0x97, 0x90, 0xa3, 0x01, 0xc5, 0x97, 0x29, 0xd4, 0x62,
	0xda, 0x25, 0x17, 0x4e, 0xbb, 0xf0, 0x0d, 0xcd, 0x8f, 0x37, 0x34, 0x44, 0xff, 0x54, 0x84, 0xfe,
	0x1b, 0x50, 0x22, 0x4f, 0x8d, 0xfe, 0xa8, 0x4b, 0xb4, 0x11, 0xaf, 0xcb, 0x29, 0xab, 0xc0, 0x41,
	0x7b, 0x66, 0x57, 0xf9, 0x01, 0xf7, 0x53, 0xc3, 0x64, 0xa6, 0xad, 0xf1, 0x9b, 0x13, 0xea, 0xc0,
	0x04, 0x21, 0x0f, 0xf9, 0x6f, 0xc9, 0xa2, 0xa3, 0x96, 0x0d, 0x71, 0xe9, 0x33, 0xcb, 0xfb, 0x5f,
	0xb3, 0xef, 0xdb, 0xb9, 0x7f, 0x44, 0x27, 0xff, 0xe5, 0xb8, 0xe3, 0x52, 0x7e, 0x97, 0x7d, 0x68,
	0x32, 0x66, 0x87, 0x47, 0x06, 0xcf, 0x74, 0x7e, 0x42, 0xfb, 0x9f, 0x89, 0xec, 0xff, 0x29, 0x9e,
	0x10, 0x65, 0xe3, 0xd5, 0x24, 0x36, 0xa6, 0xbd, 0xb5, 0x5c, 0x10, 0x0b, 0x01, 0x3d, 0x32, 0x10,
	0xa5, 0x60, 0x39, 0x51, 0x0a, 0x3c, 0x32, 0x50, 0xe7, 0x0d, 0xb1, 0x99, 0x6a, 0xff, 0xbf, 0x9d,
	0x81, 0xc5, 0x8e, 0x50, 0xa1, 0x99, 0xbe, 0xfa, 0x35, 0x72, 0xa5, 0x26, 0x3f, 0xf2, 0x2b, 0x55,
	0x91, 0x14, 0xa1, 0x9e, 0x78, 0x76, 0xfd, 0xc3, 0x63, 0x99, 0x48, 0x58, 0x71, 0x2d, 0x0a, 0x9a,
	0xeb, 0x86, 0x01, 0xca, 0x00, 0xe6, 0x22, 0x63, 0xe4, 0xeb, 0xb0, 0xc2, 0xb2, 0x05, 0xb5, 0x56,
	0xfb, 0x91, 0xd6, 0xd9, 0xad, 0xee, 0xee, 0x75, 0x84, 0x34, 0xc4, 0xf3, 0xb0, 0x1c, 0xef, 0x6e,
	0xef, 0x6d, 0x6c, 0x37, 0x6a, 0x15, 0x29, 0xf9, 0xe5, 0xb6, 0xda, 0x78, 0x50, 0xdd, 0xad, 0x57,
	0x32, 0x4a, 0x0d, 0xae, 0x26, 0x21, 0x9a, 0xce, 0x71, 0xdc, 0x03, 0x99, 0xa9, 0xc9, 0x73, 0x55,
	0xa6, 0x52, 0x8f, 0x5a, 0xf0, 0xcd, 0xca, 0xea, 0x94, 0xc9, 0xbc, 0xb2, 0x37, 0xfc, 0xaf, 0xec,
	0xce, 0x5b, 0x75, 0xaa, 0x7c, 0x57, 0x82, 0x6b, 0xac, 0xea, 0xe2, 0xc0, 0x74, 0x06, 0x35, 0xfb,
	0x31, 0x71, 0x02, 0xa9, 0x4a, 0x83, 0xe2, 0x47, 0x61, 0xd1, 0x60, 0x73, 0x68, 0xf1, 0x2a, 0xd6,
	0xa2, 0x2a, 0x1b, 0xfe, 0x02, 0x41, 0x31, 0x2b, 0x7b, 0xe5, 0x31, 0x71, 0xd0, 0x23, 0x12, 0x5f,
	0xf1, 0x6f, 0xe5, 0x58, 0xa7, 0xf0, 0x8a, 0xd2, 0x80, 0xe7, 0x27, 0x23, 0x9b, 0x8e, 0xf0, 0x3f,
	0x92, 0xd8, 0x37, 0x49, 0xe1, 0xa2, 0xdd, 0x74, 0x97, 0x53, 0xe7, 0xae, 0x18, 0x0e, 0xab, 0x99,
	0x5c, 0x58, 0xcd, 0x28, 0xdf, 0xc8, 0xc0, 0x72, 0x32, 0x6a, 0x17, 0x5e, 0x51, 0x2c, 0xdf, 0x81,
	0x05, 0xd3, 0xd5, 0xb0, 0x08, 0xcc, 0x35, 0xad, 0x1e, 0x16, 0xf2, 0x0c, 0x4c, 0x8f, 0xeb, 0xb6,
	0x79, 0xd3, 0x6d, 0x3d, 0x26, 0x4e, 0x07, 0x7b, 0xb6, 0x69, 0x87, 0x5c, 0x0d, 0xb9, 0x5f, 0xb9,
	0x33, 0x5b, 0x26, 0xc1, 0x0b, 0x9b, 0x28, 0x2b, 0xf9, 0x49, 0xb2, 0xa2, 0xfc, 0x13, 0x73, 0x1d,
	0xdb, 0xc4, 0x39, 0xb0, 0x9d, 0x81, 0x2f, 0x9d, 0x23, 0x82, 0x3a, 0xee, 0xc2, 0x7f, 0x83, 0x24,
	0xe2, 0x25, 0x88, 0x5f, 0xee, 0xe4, 0xc3, 0x1f, 0x43, 0xc7, 0xca, 0x78, 0xa6, 0x62, 0x65, 0x3c,
	0x27, 0x95, 0xf2, 0x7e, 0x85, 0x09, 0x61, 0x98, 0x2c, 0x77, 0x68, 0x5b, 0x2e, 0xb9, 0xc4, 0x6f,
	0x4e, 0x7e, 0xc2, 0x74, 0x40, 0x87, 0x58, 0xdd, 0x4d, 0x5e, 0xa0, 0x89, 0xdf, 0xd1, 0x5c, 0x30,
	0x87, 0xc5, 0xba, 0x55, 0xf6, 0x01, 0x0f, 0x3b, 0xe8, 0x41, 0xdd, 0x2a, 0xfb, 0x78, 0x67, 0x05,
	0x0a, 0x23, 0x5f, 0xdb, 0xe5, 0x50, 0xdb, 0x4d, 0x8f, 0x98, 0xba, 0x3b, 0x21, 0x97, 0xca, 0x35,
	0x43, 0x02, 0x09, 0xa9, 0xb9, 0xa9, 0xfc, 0x33, 0xf3, 0x55, 0xef, 0xd9, 0xfd, 0xbe, 0xfd, 0x84,
	0xfd, 0x00, 0xe9, 0x05, 0x0b, 0xda, 0x24, 0xad, 0xf0, 0x51, 0x00, 0x4f, 0x77, 0x7a, 0xc4, 0x0b,
	0x7e, 0x15, 0x6e, 0x36, 0x54, 0xbb, 0xb6, 0x63, 0x1a, 0x18, 0x52, 0x15, 0xd9, 0xa8, 0x1d, 0xd3,
	0x90, 0x3f, 0x08, 0x53, 0x83, 0xe3, 0xe0, 0x57, 0xc2, 0x92, 0x87, 0xe7, 0x07, 0xc7, 0x3b, 0xa6,
	0xa1, 0x1c, 0xa2, 0x6f, 0x2a, 0x90, 0x95, 0x5e, 0xd0, 0xe2, 0x5b, 0x98, 0x49, 0xd8, 0x42, 0xe5,
	0x3b, 0x2c, 0x88, 0xdd, 0xb3, 0x7e, 0x01, 0x3c, 0x1c, 0x33, 0x24, 0x77, 0x1a, 0x43, 0x6a, 0xa8,
	0x53, 0x42, 0x58, 0xa6, 0x97, 0x96, 0x7f, 0x94, 0xe0, 0x7a, 0xdb, 0xd4, 0x55, 0x32, 0xb4, 0x1d,
	0xef, 0xbd, 0x3c, 0x3e, 0x27, 0xd0, 0x1c, 0xdf, 0x96, 0x5c, 0xd2, 0xc9, 0x4a, 0x21, 0x2b, 0x6f,
	0x62, 0x9e, 0x2b, 0x91, 0xa8, 0xf4, 0x2c, 0xfa, 0x2b, 0x09, 0x14, 0x96, 0xb0, 0x6a, 0x3b, 0xe4,
	0xb1, 0x69, 0x8f, 0xdc, 0x5f, 0x10, 0x9f, 0x52, 0xc8, 0xc6, 0x13, 0x78, 0xdf, 0x89, 0x28, 0xbf,
	0x67, 0x67, 0xe7, 0x4b, 0xb0, 0xe2, 0x27, 0xf7, 0x98, 0x5c, 0xf2, 0x54, 0xdb, 0xe5, 0xb0, 0x48,
	0x79, 0x1b, 0x33, 0x13, 0xb1, 0xe5, 0xd3, 0x93, 0x1b, 0xd6, 0x65, 0x99, 0x33, 0xe8, 0x32, 0xe5,
	0x5d, 0x09, 0x5e, 0x62, 0x8b, 0xb3, 0xa5, 0x49, 0x97, 0x19, 0xa8, 0xd6, 0xc1, 0x8e, 0x69, 0xf0,
	0x80, 0xf4, 0x92, 0xd8, 0xf0, 0xd5, 0x0c, 0xbc, 0x7c, 0x0a, 0x2a, 0xe9, 0x59, 0xb2, 0x0f, 0x73,
	0x03, 0xd3, 0x48, 0xa8, 0x57, 0x79, 0x4d, 0xe0, 0xcb, 0xe9, 0x4b, 0x72, 0xee, 0xf9, 0x11, 0xff,
	0xc0, 0x7f, 0xa4, 0x63, 0x56, 0x5b, 0x30, 0x23, 0x76, 0xd3, 0xd0, 0x81, 0x7f, 0xb3, 0xcf, 0xd3,
	0x56, 0x53, 0xec, 0x83, 0x7d, 0xac, 0xe1, 0xd2, 0x2d, 0x6d, 0x9f, 0x68, 0x07, 0x7c, 0x39, 0x1e,
	0xee, 0x96, 0x0d, 0xdd, 0xda, 0x20, 0x3e, 0x0e, 0xd4, 0x4f, 0xa6, 0xce, 0x41, 0x48, 0xfc, 0xb7,
	0x6d, 0x03, 0x6b, 0xe3, 0x77, 0xdc, 0x5e, 0x82, 0x58, 0x4b, 0x49, 0xba, 0xe7, 0xfc, 0x87, 0x76,
	0xe2, 0x27, 0x67, 0x54, 0xaf, 0xac, 0x44, 0x51, 0xe3, 0xba, 0xdc, 0xed, 0xe1, 0x37, 0x09, 0xa6,
	0xa1, 0x0d, 0x74, 0xf7, 0x88, 0xa3, 0x44, 0x39, 0xb1, 0xa3, 0xbb, 0x47, 0x67, 0x3c, 0x8a, 0xe7,
	0xbf, 0xd3, 0x13, 0x71, 0xce, 0x87, 0x70, 0xbe, 0xfd, 0x1a, 0xee, 0x4f, 0xf0, 0x0b, 0x53, 0xf2,
	0x15, 0x98, 0x6b, 0x37, 0xaa, 0x5a, 0xfb, 0x7e, 0xb5, 0x53, 0xd7, 0x6a, 0xdb, 0xad, 0x4e, 0xbd,
	0xf2, 0x9c, 0xff, 0x5b, 0x4b, 0x0c, 0xd8, 0x6a, 0xe2, 0x1d, 0x78, 0x45, 0xba, 0xfd, 0x69, 0xa8,
	0x44, 0x7f, 0x42, 0x4d, 0x2e, 0xc1, 0xf4, 0xc8, 0x3a, 0xb2, 0xec, 0x27, 0x56, 0xe5, 0x39, 0x79,
	0x06, 0x0a, 0x1d, 0xf2, 0x54, 0xdb, 0xd1, 0xfb, 0xa4, 0x22, 0xc9, 0xb3, 0x00, 0xb4, 0x75, 0x0f,
	0xbf, 0xb5, 0xa8, 0x64, 0x6e, 0xbf, 0x2b, 0x01, 0x8c, 0xef, 0x57, 0xe5, 0x6b, 0xb0, 0xc4, 0x6f,
	0xd8, 0x71, 0x99, 0xbd, 0x66, 0xa7, 0x5d, 0xaf, 0x35, 0xee, 0x35, 0xea, 0x9b, 0xac, 0x40, 0x5b,
	0xec, 0x0c, 0xff, 0x14, 0xd4, 0x02, 0x54, 0xc4, 0x4e, 0xc4, 0x2e, 0x43, 0x29, 0x11, 0xa1, 0xf5,
	0xe6, 0x66, 0x25, 0x4b, 0x29, 0x09, 0x0d, 0xad, 0xee, 0x75, 0xea, 0x95, 0xdc, 0xed, 0x6f, 0x49,
	0x20, 0xc7, 0x3f, 0xfb, 0x96, 0x5f, 0x82, 0x9b, 0x6c, 0x74, 0xab, 0x5d, 0x57, 0xab, 0xe3, 0xc2,
	0xf0, 0x30, 0x6e, 0x41, 0xbc, 0x1e, 0x19, 0xc5, 0xb8, 0x24, 0xbf, 0x00, 0xab, 0xc9, 0xdd, 0xb8,
	0x76, 0x66, 0x9c, 0x0c, 0x88, 0xf4, 0x23, 0xc2, 0xb7, 0x09, 0x94, 0x43, 0x9f, 0x47, 0xca, 0x37,
	0xe0, 0x1a, 0x1b, 0xbe, 0xb1, 0xb5, 0x93, 0xc8, 0xaa, 0x65, 0x58, 0x88, 0x0e, 0xe0, 0x98, 0xac,
	0xc0, 0x62, 0xac, 0x87, 0x21, 0x41, 0xf7, 0x62, 0x31, 0xf1, 0x33, 0x43, 0xf9, 0x16, 0xbc, 0x34,
	0x7e, 0xe9, 0x44, 0x3e, 0xbc, 0x08, 0xd7, 0x27, 0x8e, 0xe4, 0x18, 0x28, 0xf0, 0xc2, 0xe4, 0x21,
	0x1c, 0x95, 0x26, 0xd6, 0x3e, 0x07, 0x15, 0x14, 0x54, 0xf6, 0xb6, 0xab, 0x8f, 0xfc, 0x05, 0xdf,
	0x6c, 0xb6, 0x1e, 0x36, 0x2b, 0xcf, 0xe1, 0xaf, 0x85, 0x05, 0xe0, 0xea, 0xde, 0x6e, 0x8b, 0xc9,
	0xc1, 0x18, 0xb6, 0x53, 0x6d, 0xee, 0x55, 0xb7, 0x2b, 0x99, 0xdb, 0xbf, 0x37, 0xbe, 0xab, 0x0f,
	0x17, 0xa4, 0xbe, 0x00, 0xab, 0x62, 0x79, 0x47, 0x2c, 0x4b, 0x73, 0x0d, 0x96, 0x12, 0xfa, 0x39,
	0x25, 0xc9, 0x9d, 0x0f, 0xab, 0x8d, 0xdd, 0x4a, 0x46, 0x5e, 0x85, 0xab, 0x09, 0x9d, 0xf7, 0x5b,
	0xbb, 0x95, 0xec, 0xed, 0x23, 0xfc, 0x6d, 0x83, 0x48, 0x61, 0x23, 0x15, 0x02, 0xfa, 0x86, 0x5a,
	0x6d, 0xbe, 0xd9, 0x68, 0x6e, 0x69, 0xdb, 0x8d, 0xce, 0x6e, 0x38, 0x5f, 0x14, 0xeb, 0xad, 0xb5,
	0xb6, 0xe9, 0x19, 0x60, 0xdb, 0x1a, 0xeb, 0x55, 0x1b, 0xc8, 0xcb, 0x2f, 0x42, 0x39, 0x54, 0xb4,
	0x47, 0x65, 0x95, 0x8e, 0xf5, 0xf3, 0x4b, 0x38, 0x45, 0x7c, 0xa1, 0x70, 0x37, 0x3b, 0x6c, 0x15,
	0x29, 0xb9, 0xb7, 0x56, 0x6d, 0xd6, 0xea, 0x94, 0xcf, 0xdf, 0x67, 0x97, 0x08, 0x13, 0xae, 0x5d,
	0xfc, 0x62, 0x9a, 0xea, 0xde, 0xee, 0xfd, 0x96, 0xaa, 0x3d, 0x6c, 0xa9, 0x6f, 0x76, 0x18, 0xaa,
	0x9d, 0x96, 0xba, 0xcb, 0xb6, 0x6c, 0x8c, 0xc7, 0xc7, 0xe0, 0xce, 0x69, 0x83, 0xf9, 0xfa, 0xf5,
	0x4d, 0xad, 0xd6, 0xda, 0x6b, 0x52, 0xf4, 0x78, 0x35, 0xd0, 0x89, 0x2f, 0xa9, 0xf5, 0xea, 0x2e,
	0x2b, 0xdb, 0xa9, 0x64, 0x6e, 0x7f, 0x3d, 0x83, 0x5f, 0xe4, 0x71, 0xcb, 0x8f, 0xe2, 0xd3, 0xa8,
	0x6a, 0x3b, 0x8d, 0x1a, 0x1b, 0xdc, 0x6c, 0x6c, 0xb3, 0xe3, 0x14, 0x82, 0x3e, 0x68, 0xa8, 0xbb,
	0x54, 0xb0, 0x82, 0x5f, 0xa0, 0x0b, 0x7a, 0x3e, 0x5f, 0x57, 0x5b, 0x95, 0x4c, 0x6c, 0x9a, 0x56,
	0xb3, 0x5e, 0xc9, 0xc5, 0xa0, 0xbb, 0x0f, 0x5b, 0x95, 0x82, 0x7c, 0x15, 0xe4, 0x30, 0xf4, 0xbe,
	0x5a, 0xaf, 0x57, 0x2a, 0xb1, 0xa9, 0xef, 0xb5, 0xf6, 0xd4, 0xca, 0xcd, 0x38, 0xb8, 0xf1, 0xa0,
	0x5e, 0x79, 0x43, 0x5e, 0x8c, 0xcc, 0xdd, 0x69, 0x7c, 0xae, 0xf2, 0x8e, 0x24, 0x2f, 0x45, 0x26,
	0xef, 0xd4, 0x1f, 0xd4, 0x9b, 0x95, 0x77, 0x32, 0xb1, 0x8e, 0x7a, 0x63, 0xeb, 0xfe, 0x6e, 0xe5,
	0x9d, 0xdc, 0xc6, 0xa7, 0x60, 0xd9, 0xb0, 0x07, 0x6b, 0xc7, 0xe6, 0xb1, 0x3d, 0xa2, 0x2e, 0xc0,
	0xc0, 0xee, 0x92, 0x3e, 0xfb, 0xf3, 0x8f, 0xcf, 0xdf, 0xec, 0xd9, 0x7d, 0xdd, 0xea, 0xad, 0xdd,
	0x5d, 0xf7, 0xbc, 0x35, 0xc3, 0x1e, 0xdc, 0x41, 0xb0, 0x61, 0xf7, 0xef, 0xe8, 0xc3, 0xe1, 0x9d,
	0xa1, 0xa9, 0xef, 0x4f, 0x21, 0xe4, 0x63, 0xff, 0x17, 0x00, 0x00, 0xff, 0xff, 0x95, 0xbd, 0x3f,
	0xf0, 0x4a, 0x64, 0x00, 0x00,
}
