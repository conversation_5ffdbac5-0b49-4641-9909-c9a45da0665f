// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-movie_.proto

package channel_movie // import "golang.52tt.com/protocol/app/channel-movie"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChangeChannelMovieStatusReq_OperationType int32

const (
	// 默认
	ChangeChannelMovieStatusReq_UNKOWN ChangeChannelMovieStatusReq_OperationType = 0
	// 播放
	ChangeChannelMovieStatusReq_PLAY ChangeChannelMovieStatusReq_OperationType = 1
	// 暂停
	ChangeChannelMovieStatusReq_STOP ChangeChannelMovieStatusReq_OperationType = 2
	// 拖动
	ChangeChannelMovieStatusReq_DRAG ChangeChannelMovieStatusReq_OperationType = 3
)

var ChangeChannelMovieStatusReq_OperationType_name = map[int32]string{
	0: "UNKOWN",
	1: "PLAY",
	2: "STOP",
	3: "DRAG",
}
var ChangeChannelMovieStatusReq_OperationType_value = map[string]int32{
	"UNKOWN": 0,
	"PLAY":   1,
	"STOP":   2,
	"DRAG":   3,
}

func (x ChangeChannelMovieStatusReq_OperationType) String() string {
	return proto.EnumName(ChangeChannelMovieStatusReq_OperationType_name, int32(x))
}
func (ChangeChannelMovieStatusReq_OperationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{17, 0}
}

type ChannelMovieInfo_StatusType int32

const (
	// 默认
	ChannelMovieInfo_UNKOWN ChannelMovieInfo_StatusType = 0
	// 播放
	ChannelMovieInfo_PLAY ChannelMovieInfo_StatusType = 1
	// 暂停
	ChannelMovieInfo_STOP ChannelMovieInfo_StatusType = 2
	// 结束
	ChannelMovieInfo_END ChannelMovieInfo_StatusType = 3
)

var ChannelMovieInfo_StatusType_name = map[int32]string{
	0: "UNKOWN",
	1: "PLAY",
	2: "STOP",
	3: "END",
}
var ChannelMovieInfo_StatusType_value = map[string]int32{
	"UNKOWN": 0,
	"PLAY":   1,
	"STOP":   2,
	"END":    3,
}

func (x ChannelMovieInfo_StatusType) String() string {
	return proto.EnumName(ChannelMovieInfo_StatusType_name, int32(x))
}
func (ChannelMovieInfo_StatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{21, 0}
}

// 获取电影分类列表
type GetMovieCategoryReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMovieCategoryReq) Reset()         { *m = GetMovieCategoryReq{} }
func (m *GetMovieCategoryReq) String() string { return proto.CompactTextString(m) }
func (*GetMovieCategoryReq) ProtoMessage()    {}
func (*GetMovieCategoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{0}
}
func (m *GetMovieCategoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieCategoryReq.Unmarshal(m, b)
}
func (m *GetMovieCategoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieCategoryReq.Marshal(b, m, deterministic)
}
func (dst *GetMovieCategoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieCategoryReq.Merge(dst, src)
}
func (m *GetMovieCategoryReq) XXX_Size() int {
	return xxx_messageInfo_GetMovieCategoryReq.Size(m)
}
func (m *GetMovieCategoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieCategoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieCategoryReq proto.InternalMessageInfo

func (m *GetMovieCategoryReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type MovieCategoryInfo struct {
	CategoryName         string   `protobuf:"bytes,1,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	CategoryId           uint32   `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MovieCategoryInfo) Reset()         { *m = MovieCategoryInfo{} }
func (m *MovieCategoryInfo) String() string { return proto.CompactTextString(m) }
func (*MovieCategoryInfo) ProtoMessage()    {}
func (*MovieCategoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{1}
}
func (m *MovieCategoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MovieCategoryInfo.Unmarshal(m, b)
}
func (m *MovieCategoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MovieCategoryInfo.Marshal(b, m, deterministic)
}
func (dst *MovieCategoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MovieCategoryInfo.Merge(dst, src)
}
func (m *MovieCategoryInfo) XXX_Size() int {
	return xxx_messageInfo_MovieCategoryInfo.Size(m)
}
func (m *MovieCategoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MovieCategoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MovieCategoryInfo proto.InternalMessageInfo

func (m *MovieCategoryInfo) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *MovieCategoryInfo) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

type GetMovieCategoryResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CategoryList         []*MovieCategoryInfo `protobuf:"bytes,2,rep,name=category_list,json=categoryList,proto3" json:"category_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMovieCategoryResp) Reset()         { *m = GetMovieCategoryResp{} }
func (m *GetMovieCategoryResp) String() string { return proto.CompactTextString(m) }
func (*GetMovieCategoryResp) ProtoMessage()    {}
func (*GetMovieCategoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{2}
}
func (m *GetMovieCategoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieCategoryResp.Unmarshal(m, b)
}
func (m *GetMovieCategoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieCategoryResp.Marshal(b, m, deterministic)
}
func (dst *GetMovieCategoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieCategoryResp.Merge(dst, src)
}
func (m *GetMovieCategoryResp) XXX_Size() int {
	return xxx_messageInfo_GetMovieCategoryResp.Size(m)
}
func (m *GetMovieCategoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieCategoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieCategoryResp proto.InternalMessageInfo

func (m *GetMovieCategoryResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMovieCategoryResp) GetCategoryList() []*MovieCategoryInfo {
	if m != nil {
		return m.CategoryList
	}
	return nil
}

// 影片排行榜接口
type GetMovieRankListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CategoryId           uint32       `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Offset               uint32       `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Size                 uint32       `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMovieRankListReq) Reset()         { *m = GetMovieRankListReq{} }
func (m *GetMovieRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetMovieRankListReq) ProtoMessage()    {}
func (*GetMovieRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{3}
}
func (m *GetMovieRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieRankListReq.Unmarshal(m, b)
}
func (m *GetMovieRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetMovieRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieRankListReq.Merge(dst, src)
}
func (m *GetMovieRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetMovieRankListReq.Size(m)
}
func (m *GetMovieRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieRankListReq proto.InternalMessageInfo

func (m *GetMovieRankListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMovieRankListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMovieRankListReq) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *GetMovieRankListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMovieRankListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type MovieRankInfo struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MovieType            uint32   `protobuf:"varint,3,opt,name=movie_type,json=movieType,proto3" json:"movie_type,omitempty"`
	Playing              bool     `protobuf:"varint,4,opt,name=playing,proto3" json:"playing,omitempty"`
	CoverUrl             string   `protobuf:"bytes,5,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Duration             uint32   `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	Score                float32  `protobuf:"fixed32,7,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MovieRankInfo) Reset()         { *m = MovieRankInfo{} }
func (m *MovieRankInfo) String() string { return proto.CompactTextString(m) }
func (*MovieRankInfo) ProtoMessage()    {}
func (*MovieRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{4}
}
func (m *MovieRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MovieRankInfo.Unmarshal(m, b)
}
func (m *MovieRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MovieRankInfo.Marshal(b, m, deterministic)
}
func (dst *MovieRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MovieRankInfo.Merge(dst, src)
}
func (m *MovieRankInfo) XXX_Size() int {
	return xxx_messageInfo_MovieRankInfo.Size(m)
}
func (m *MovieRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MovieRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MovieRankInfo proto.InternalMessageInfo

func (m *MovieRankInfo) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *MovieRankInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MovieRankInfo) GetMovieType() uint32 {
	if m != nil {
		return m.MovieType
	}
	return 0
}

func (m *MovieRankInfo) GetPlaying() bool {
	if m != nil {
		return m.Playing
	}
	return false
}

func (m *MovieRankInfo) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *MovieRankInfo) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *MovieRankInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type ChangeRankInfo struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	Rank                 uint32   `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeRankInfo) Reset()         { *m = ChangeRankInfo{} }
func (m *ChangeRankInfo) String() string { return proto.CompactTextString(m) }
func (*ChangeRankInfo) ProtoMessage()    {}
func (*ChangeRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{5}
}
func (m *ChangeRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeRankInfo.Unmarshal(m, b)
}
func (m *ChangeRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeRankInfo.Marshal(b, m, deterministic)
}
func (dst *ChangeRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeRankInfo.Merge(dst, src)
}
func (m *ChangeRankInfo) XXX_Size() int {
	return xxx_messageInfo_ChangeRankInfo.Size(m)
}
func (m *ChangeRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeRankInfo proto.InternalMessageInfo

func (m *ChangeRankInfo) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *ChangeRankInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type GetMovieRankListResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	End                  bool              `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	MovieRankList        []*MovieRankInfo  `protobuf:"bytes,3,rep,name=movie_rank_list,json=movieRankList,proto3" json:"movie_rank_list,omitempty"`
	ChangeRankInfo       []*ChangeRankInfo `protobuf:"bytes,4,rep,name=change_rank_info,json=changeRankInfo,proto3" json:"change_rank_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetMovieRankListResp) Reset()         { *m = GetMovieRankListResp{} }
func (m *GetMovieRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetMovieRankListResp) ProtoMessage()    {}
func (*GetMovieRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{6}
}
func (m *GetMovieRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieRankListResp.Unmarshal(m, b)
}
func (m *GetMovieRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetMovieRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieRankListResp.Merge(dst, src)
}
func (m *GetMovieRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetMovieRankListResp.Size(m)
}
func (m *GetMovieRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieRankListResp proto.InternalMessageInfo

func (m *GetMovieRankListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMovieRankListResp) GetEnd() bool {
	if m != nil {
		return m.End
	}
	return false
}

func (m *GetMovieRankListResp) GetMovieRankList() []*MovieRankInfo {
	if m != nil {
		return m.MovieRankList
	}
	return nil
}

func (m *GetMovieRankListResp) GetChangeRankInfo() []*ChangeRankInfo {
	if m != nil {
		return m.ChangeRankInfo
	}
	return nil
}

// 影片搜索接口
type SearchMovieReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Key                  string       `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Offset               uint32       `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Size                 uint32       `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SearchMovieReq) Reset()         { *m = SearchMovieReq{} }
func (m *SearchMovieReq) String() string { return proto.CompactTextString(m) }
func (*SearchMovieReq) ProtoMessage()    {}
func (*SearchMovieReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{7}
}
func (m *SearchMovieReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchMovieReq.Unmarshal(m, b)
}
func (m *SearchMovieReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchMovieReq.Marshal(b, m, deterministic)
}
func (dst *SearchMovieReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchMovieReq.Merge(dst, src)
}
func (m *SearchMovieReq) XXX_Size() int {
	return xxx_messageInfo_SearchMovieReq.Size(m)
}
func (m *SearchMovieReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchMovieReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchMovieReq proto.InternalMessageInfo

func (m *SearchMovieReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SearchMovieReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SearchMovieReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *SearchMovieReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

type SearchMovieInfo struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MovieType            uint32   `protobuf:"varint,3,opt,name=movie_type,json=movieType,proto3" json:"movie_type,omitempty"`
	CoverUrl             string   `protobuf:"bytes,4,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Duration             uint32   `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`
	Score                float32  `protobuf:"fixed32,6,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchMovieInfo) Reset()         { *m = SearchMovieInfo{} }
func (m *SearchMovieInfo) String() string { return proto.CompactTextString(m) }
func (*SearchMovieInfo) ProtoMessage()    {}
func (*SearchMovieInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{8}
}
func (m *SearchMovieInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchMovieInfo.Unmarshal(m, b)
}
func (m *SearchMovieInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchMovieInfo.Marshal(b, m, deterministic)
}
func (dst *SearchMovieInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchMovieInfo.Merge(dst, src)
}
func (m *SearchMovieInfo) XXX_Size() int {
	return xxx_messageInfo_SearchMovieInfo.Size(m)
}
func (m *SearchMovieInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchMovieInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SearchMovieInfo proto.InternalMessageInfo

func (m *SearchMovieInfo) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *SearchMovieInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchMovieInfo) GetMovieType() uint32 {
	if m != nil {
		return m.MovieType
	}
	return 0
}

func (m *SearchMovieInfo) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *SearchMovieInfo) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *SearchMovieInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type SearchMovieResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Key                  string             `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	End                  bool               `protobuf:"varint,3,opt,name=end,proto3" json:"end,omitempty"`
	MovieList            []*SearchMovieInfo `protobuf:"bytes,4,rep,name=movie_list,json=movieList,proto3" json:"movie_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SearchMovieResp) Reset()         { *m = SearchMovieResp{} }
func (m *SearchMovieResp) String() string { return proto.CompactTextString(m) }
func (*SearchMovieResp) ProtoMessage()    {}
func (*SearchMovieResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{9}
}
func (m *SearchMovieResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchMovieResp.Unmarshal(m, b)
}
func (m *SearchMovieResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchMovieResp.Marshal(b, m, deterministic)
}
func (dst *SearchMovieResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchMovieResp.Merge(dst, src)
}
func (m *SearchMovieResp) XXX_Size() int {
	return xxx_messageInfo_SearchMovieResp.Size(m)
}
func (m *SearchMovieResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchMovieResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchMovieResp proto.InternalMessageInfo

func (m *SearchMovieResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SearchMovieResp) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SearchMovieResp) GetEnd() bool {
	if m != nil {
		return m.End
	}
	return false
}

func (m *SearchMovieResp) GetMovieList() []*SearchMovieInfo {
	if m != nil {
		return m.MovieList
	}
	return nil
}

// 影片详情接口
type GetMovieDetailReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	MovieId              uint32       `protobuf:"varint,2,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMovieDetailReq) Reset()         { *m = GetMovieDetailReq{} }
func (m *GetMovieDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetMovieDetailReq) ProtoMessage()    {}
func (*GetMovieDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{10}
}
func (m *GetMovieDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieDetailReq.Unmarshal(m, b)
}
func (m *GetMovieDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetMovieDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieDetailReq.Merge(dst, src)
}
func (m *GetMovieDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetMovieDetailReq.Size(m)
}
func (m *GetMovieDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieDetailReq proto.InternalMessageInfo

func (m *GetMovieDetailReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMovieDetailReq) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

type MovieDetailInfo struct {
	MovieId              uint32   `protobuf:"varint,1,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MovieType            uint32   `protobuf:"varint,3,opt,name=movie_type,json=movieType,proto3" json:"movie_type,omitempty"`
	ReleaseDate          string   `protobuf:"bytes,4,opt,name=release_date,json=releaseDate,proto3" json:"release_date,omitempty"`
	Category             string   `protobuf:"bytes,5,opt,name=category,proto3" json:"category,omitempty"`
	Language             string   `protobuf:"bytes,6,opt,name=language,proto3" json:"language,omitempty"`
	Actors               string   `protobuf:"bytes,7,opt,name=actors,proto3" json:"actors,omitempty"`
	Score                float32  `protobuf:"fixed32,8,opt,name=score,proto3" json:"score,omitempty"`
	Description          string   `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`
	CoverUrl             string   `protobuf:"bytes,10,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Duration             uint32   `protobuf:"varint,11,opt,name=duration,proto3" json:"duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MovieDetailInfo) Reset()         { *m = MovieDetailInfo{} }
func (m *MovieDetailInfo) String() string { return proto.CompactTextString(m) }
func (*MovieDetailInfo) ProtoMessage()    {}
func (*MovieDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{11}
}
func (m *MovieDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MovieDetailInfo.Unmarshal(m, b)
}
func (m *MovieDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MovieDetailInfo.Marshal(b, m, deterministic)
}
func (dst *MovieDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MovieDetailInfo.Merge(dst, src)
}
func (m *MovieDetailInfo) XXX_Size() int {
	return xxx_messageInfo_MovieDetailInfo.Size(m)
}
func (m *MovieDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MovieDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MovieDetailInfo proto.InternalMessageInfo

func (m *MovieDetailInfo) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *MovieDetailInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MovieDetailInfo) GetMovieType() uint32 {
	if m != nil {
		return m.MovieType
	}
	return 0
}

func (m *MovieDetailInfo) GetReleaseDate() string {
	if m != nil {
		return m.ReleaseDate
	}
	return ""
}

func (m *MovieDetailInfo) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *MovieDetailInfo) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *MovieDetailInfo) GetActors() string {
	if m != nil {
		return m.Actors
	}
	return ""
}

func (m *MovieDetailInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *MovieDetailInfo) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *MovieDetailInfo) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *MovieDetailInfo) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

type GetMovieDetailResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MovieInfo            *MovieDetailInfo `protobuf:"bytes,2,opt,name=movie_info,json=movieInfo,proto3" json:"movie_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetMovieDetailResp) Reset()         { *m = GetMovieDetailResp{} }
func (m *GetMovieDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetMovieDetailResp) ProtoMessage()    {}
func (*GetMovieDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{12}
}
func (m *GetMovieDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMovieDetailResp.Unmarshal(m, b)
}
func (m *GetMovieDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMovieDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetMovieDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMovieDetailResp.Merge(dst, src)
}
func (m *GetMovieDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetMovieDetailResp.Size(m)
}
func (m *GetMovieDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMovieDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMovieDetailResp proto.InternalMessageInfo

func (m *GetMovieDetailResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMovieDetailResp) GetMovieInfo() *MovieDetailInfo {
	if m != nil {
		return m.MovieInfo
	}
	return nil
}

// 获取房间播放状态
type GetChannelMovieStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelMovieStatusReq) Reset()         { *m = GetChannelMovieStatusReq{} }
func (m *GetChannelMovieStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMovieStatusReq) ProtoMessage()    {}
func (*GetChannelMovieStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{13}
}
func (m *GetChannelMovieStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMovieStatusReq.Unmarshal(m, b)
}
func (m *GetChannelMovieStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMovieStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMovieStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMovieStatusReq.Merge(dst, src)
}
func (m *GetChannelMovieStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMovieStatusReq.Size(m)
}
func (m *GetChannelMovieStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMovieStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMovieStatusReq proto.InternalMessageInfo

func (m *GetChannelMovieStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelMovieStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMovieStatusResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelMovieInfo     *ChannelMovieInfo `protobuf:"bytes,2,opt,name=channel_movie_info,json=channelMovieInfo,proto3" json:"channel_movie_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChannelMovieStatusResp) Reset()         { *m = GetChannelMovieStatusResp{} }
func (m *GetChannelMovieStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMovieStatusResp) ProtoMessage()    {}
func (*GetChannelMovieStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{14}
}
func (m *GetChannelMovieStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMovieStatusResp.Unmarshal(m, b)
}
func (m *GetChannelMovieStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMovieStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMovieStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMovieStatusResp.Merge(dst, src)
}
func (m *GetChannelMovieStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMovieStatusResp.Size(m)
}
func (m *GetChannelMovieStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMovieStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMovieStatusResp proto.InternalMessageInfo

func (m *GetChannelMovieStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelMovieStatusResp) GetChannelMovieInfo() *ChannelMovieInfo {
	if m != nil {
		return m.ChannelMovieInfo
	}
	return nil
}

// 播放视频
type PlayChannelMovieReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MovieId              uint32       `protobuf:"varint,3,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PlayChannelMovieReq) Reset()         { *m = PlayChannelMovieReq{} }
func (m *PlayChannelMovieReq) String() string { return proto.CompactTextString(m) }
func (*PlayChannelMovieReq) ProtoMessage()    {}
func (*PlayChannelMovieReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{15}
}
func (m *PlayChannelMovieReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayChannelMovieReq.Unmarshal(m, b)
}
func (m *PlayChannelMovieReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayChannelMovieReq.Marshal(b, m, deterministic)
}
func (dst *PlayChannelMovieReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayChannelMovieReq.Merge(dst, src)
}
func (m *PlayChannelMovieReq) XXX_Size() int {
	return xxx_messageInfo_PlayChannelMovieReq.Size(m)
}
func (m *PlayChannelMovieReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayChannelMovieReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlayChannelMovieReq proto.InternalMessageInfo

func (m *PlayChannelMovieReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PlayChannelMovieReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PlayChannelMovieReq) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

type PlayChannelMovieResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PlayChannelMovieResp) Reset()         { *m = PlayChannelMovieResp{} }
func (m *PlayChannelMovieResp) String() string { return proto.CompactTextString(m) }
func (*PlayChannelMovieResp) ProtoMessage()    {}
func (*PlayChannelMovieResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{16}
}
func (m *PlayChannelMovieResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayChannelMovieResp.Unmarshal(m, b)
}
func (m *PlayChannelMovieResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayChannelMovieResp.Marshal(b, m, deterministic)
}
func (dst *PlayChannelMovieResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayChannelMovieResp.Merge(dst, src)
}
func (m *PlayChannelMovieResp) XXX_Size() int {
	return xxx_messageInfo_PlayChannelMovieResp.Size(m)
}
func (m *PlayChannelMovieResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayChannelMovieResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlayChannelMovieResp proto.InternalMessageInfo

func (m *PlayChannelMovieResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 暂停/开启
type ChangeChannelMovieStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Type                 uint32       `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChangeChannelMovieStatusReq) Reset()         { *m = ChangeChannelMovieStatusReq{} }
func (m *ChangeChannelMovieStatusReq) String() string { return proto.CompactTextString(m) }
func (*ChangeChannelMovieStatusReq) ProtoMessage()    {}
func (*ChangeChannelMovieStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{17}
}
func (m *ChangeChannelMovieStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeChannelMovieStatusReq.Unmarshal(m, b)
}
func (m *ChangeChannelMovieStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeChannelMovieStatusReq.Marshal(b, m, deterministic)
}
func (dst *ChangeChannelMovieStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeChannelMovieStatusReq.Merge(dst, src)
}
func (m *ChangeChannelMovieStatusReq) XXX_Size() int {
	return xxx_messageInfo_ChangeChannelMovieStatusReq.Size(m)
}
func (m *ChangeChannelMovieStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeChannelMovieStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeChannelMovieStatusReq proto.InternalMessageInfo

func (m *ChangeChannelMovieStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChangeChannelMovieStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChangeChannelMovieStatusReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type ChangeChannelMovieStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChangeChannelMovieStatusResp) Reset()         { *m = ChangeChannelMovieStatusResp{} }
func (m *ChangeChannelMovieStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChangeChannelMovieStatusResp) ProtoMessage()    {}
func (*ChangeChannelMovieStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{18}
}
func (m *ChangeChannelMovieStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeChannelMovieStatusResp.Unmarshal(m, b)
}
func (m *ChangeChannelMovieStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeChannelMovieStatusResp.Marshal(b, m, deterministic)
}
func (dst *ChangeChannelMovieStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeChannelMovieStatusResp.Merge(dst, src)
}
func (m *ChangeChannelMovieStatusResp) XXX_Size() int {
	return xxx_messageInfo_ChangeChannelMovieStatusResp.Size(m)
}
func (m *ChangeChannelMovieStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeChannelMovieStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeChannelMovieStatusResp proto.InternalMessageInfo

func (m *ChangeChannelMovieStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 播放条拖动
type DragChannelMovieBarReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OffsetTime           uint32       `protobuf:"varint,3,opt,name=offset_time,json=offsetTime,proto3" json:"offset_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DragChannelMovieBarReq) Reset()         { *m = DragChannelMovieBarReq{} }
func (m *DragChannelMovieBarReq) String() string { return proto.CompactTextString(m) }
func (*DragChannelMovieBarReq) ProtoMessage()    {}
func (*DragChannelMovieBarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{19}
}
func (m *DragChannelMovieBarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DragChannelMovieBarReq.Unmarshal(m, b)
}
func (m *DragChannelMovieBarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DragChannelMovieBarReq.Marshal(b, m, deterministic)
}
func (dst *DragChannelMovieBarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DragChannelMovieBarReq.Merge(dst, src)
}
func (m *DragChannelMovieBarReq) XXX_Size() int {
	return xxx_messageInfo_DragChannelMovieBarReq.Size(m)
}
func (m *DragChannelMovieBarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DragChannelMovieBarReq.DiscardUnknown(m)
}

var xxx_messageInfo_DragChannelMovieBarReq proto.InternalMessageInfo

func (m *DragChannelMovieBarReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DragChannelMovieBarReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DragChannelMovieBarReq) GetOffsetTime() uint32 {
	if m != nil {
		return m.OffsetTime
	}
	return 0
}

type DragChannelMovieBarResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DragChannelMovieBarResp) Reset()         { *m = DragChannelMovieBarResp{} }
func (m *DragChannelMovieBarResp) String() string { return proto.CompactTextString(m) }
func (*DragChannelMovieBarResp) ProtoMessage()    {}
func (*DragChannelMovieBarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{20}
}
func (m *DragChannelMovieBarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DragChannelMovieBarResp.Unmarshal(m, b)
}
func (m *DragChannelMovieBarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DragChannelMovieBarResp.Marshal(b, m, deterministic)
}
func (dst *DragChannelMovieBarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DragChannelMovieBarResp.Merge(dst, src)
}
func (m *DragChannelMovieBarResp) XXX_Size() int {
	return xxx_messageInfo_DragChannelMovieBarResp.Size(m)
}
func (m *DragChannelMovieBarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DragChannelMovieBarResp.DiscardUnknown(m)
}

var xxx_messageInfo_DragChannelMovieBarResp proto.InternalMessageInfo

func (m *DragChannelMovieBarResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChannelMovieInfo struct {
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	OffsetTime           uint32   `protobuf:"varint,2,opt,name=offset_time,json=offsetTime,proto3" json:"offset_time,omitempty"`
	MovieId              uint32   `protobuf:"varint,3,opt,name=movie_id,json=movieId,proto3" json:"movie_id,omitempty"`
	MovieDuration        uint32   `protobuf:"varint,4,opt,name=movie_duration,json=movieDuration,proto3" json:"movie_duration,omitempty"`
	MovieUrl             string   `protobuf:"bytes,5,opt,name=movie_url,json=movieUrl,proto3" json:"movie_url,omitempty"`
	MovieName            string   `protobuf:"bytes,6,opt,name=movie_name,json=movieName,proto3" json:"movie_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMovieInfo) Reset()         { *m = ChannelMovieInfo{} }
func (m *ChannelMovieInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelMovieInfo) ProtoMessage()    {}
func (*ChannelMovieInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie__0a6edc16fded4c33, []int{21}
}
func (m *ChannelMovieInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMovieInfo.Unmarshal(m, b)
}
func (m *ChannelMovieInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMovieInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelMovieInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMovieInfo.Merge(dst, src)
}
func (m *ChannelMovieInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelMovieInfo.Size(m)
}
func (m *ChannelMovieInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMovieInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMovieInfo proto.InternalMessageInfo

func (m *ChannelMovieInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelMovieInfo) GetOffsetTime() uint32 {
	if m != nil {
		return m.OffsetTime
	}
	return 0
}

func (m *ChannelMovieInfo) GetMovieId() uint32 {
	if m != nil {
		return m.MovieId
	}
	return 0
}

func (m *ChannelMovieInfo) GetMovieDuration() uint32 {
	if m != nil {
		return m.MovieDuration
	}
	return 0
}

func (m *ChannelMovieInfo) GetMovieUrl() string {
	if m != nil {
		return m.MovieUrl
	}
	return ""
}

func (m *ChannelMovieInfo) GetMovieName() string {
	if m != nil {
		return m.MovieName
	}
	return ""
}

func init() {
	proto.RegisterType((*GetMovieCategoryReq)(nil), "ga.GetMovieCategoryReq")
	proto.RegisterType((*MovieCategoryInfo)(nil), "ga.MovieCategoryInfo")
	proto.RegisterType((*GetMovieCategoryResp)(nil), "ga.GetMovieCategoryResp")
	proto.RegisterType((*GetMovieRankListReq)(nil), "ga.GetMovieRankListReq")
	proto.RegisterType((*MovieRankInfo)(nil), "ga.MovieRankInfo")
	proto.RegisterType((*ChangeRankInfo)(nil), "ga.ChangeRankInfo")
	proto.RegisterType((*GetMovieRankListResp)(nil), "ga.GetMovieRankListResp")
	proto.RegisterType((*SearchMovieReq)(nil), "ga.SearchMovieReq")
	proto.RegisterType((*SearchMovieInfo)(nil), "ga.SearchMovieInfo")
	proto.RegisterType((*SearchMovieResp)(nil), "ga.SearchMovieResp")
	proto.RegisterType((*GetMovieDetailReq)(nil), "ga.GetMovieDetailReq")
	proto.RegisterType((*MovieDetailInfo)(nil), "ga.MovieDetailInfo")
	proto.RegisterType((*GetMovieDetailResp)(nil), "ga.GetMovieDetailResp")
	proto.RegisterType((*GetChannelMovieStatusReq)(nil), "ga.GetChannelMovieStatusReq")
	proto.RegisterType((*GetChannelMovieStatusResp)(nil), "ga.GetChannelMovieStatusResp")
	proto.RegisterType((*PlayChannelMovieReq)(nil), "ga.PlayChannelMovieReq")
	proto.RegisterType((*PlayChannelMovieResp)(nil), "ga.PlayChannelMovieResp")
	proto.RegisterType((*ChangeChannelMovieStatusReq)(nil), "ga.ChangeChannelMovieStatusReq")
	proto.RegisterType((*ChangeChannelMovieStatusResp)(nil), "ga.ChangeChannelMovieStatusResp")
	proto.RegisterType((*DragChannelMovieBarReq)(nil), "ga.DragChannelMovieBarReq")
	proto.RegisterType((*DragChannelMovieBarResp)(nil), "ga.DragChannelMovieBarResp")
	proto.RegisterType((*ChannelMovieInfo)(nil), "ga.ChannelMovieInfo")
	proto.RegisterEnum("ga.ChangeChannelMovieStatusReq_OperationType", ChangeChannelMovieStatusReq_OperationType_name, ChangeChannelMovieStatusReq_OperationType_value)
	proto.RegisterEnum("ga.ChannelMovieInfo_StatusType", ChannelMovieInfo_StatusType_name, ChannelMovieInfo_StatusType_value)
}

func init() {
	proto.RegisterFile("channel-movie_.proto", fileDescriptor_channel_movie__0a6edc16fded4c33)
}

var fileDescriptor_channel_movie__0a6edc16fded4c33 = []byte{
	// 996 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x57, 0xcd, 0x6e, 0xe4, 0x44,
	0x10, 0xc6, 0xf6, 0x24, 0x33, 0xae, 0xc9, 0x24, 0x4e, 0x67, 0x58, 0xbc, 0x1b, 0x10, 0x83, 0x11,
	0x28, 0x20, 0x31, 0x91, 0x82, 0xf6, 0xb0, 0x08, 0x84, 0x92, 0x0c, 0x44, 0x23, 0x96, 0x24, 0x72,
	0xb2, 0xa0, 0xe5, 0x32, 0xea, 0xd8, 0x1d, 0xc7, 0x8a, 0xff, 0x62, 0xf7, 0x04, 0x0d, 0x12, 0x12,
	0x67, 0xce, 0xbc, 0x04, 0x17, 0x2e, 0x3c, 0x02, 0x12, 0x27, 0x1e, 0x0a, 0xf5, 0x8f, 0x7f, 0xb3,
	0x89, 0xd6, 0xab, 0xe4, 0x56, 0x55, 0x2e, 0x77, 0x7f, 0x55, 0xdf, 0xd7, 0xd5, 0x36, 0x0c, 0x9d,
	0x0b, 0x1c, 0x45, 0x24, 0xf8, 0x2c, 0x8c, 0xaf, 0x7d, 0x32, 0x1b, 0x27, 0x69, 0x4c, 0x63, 0xa4,
	0x7a, 0xf8, 0xc9, 0xc0, 0xc3, 0xb3, 0x33, 0x9c, 0x11, 0x11, 0xb2, 0xbe, 0x82, 0x8d, 0x03, 0x42,
	0xbf, 0x67, 0x59, 0xfb, 0x98, 0x12, 0x2f, 0x4e, 0x17, 0x36, 0xb9, 0x42, 0x1f, 0x43, 0x8f, 0x25,
	0xcd, 0x52, 0x72, 0x65, 0x2a, 0x23, 0x65, 0xab, 0xbf, 0xd3, 0x1f, 0x7b, 0x78, 0xbc, 0x87, 0x33,
	0x62, 0x93, 0x2b, 0xbb, 0x7b, 0x26, 0x0c, 0xeb, 0x25, 0xac, 0xd7, 0xde, 0x9d, 0x46, 0xe7, 0x31,
	0xfa, 0x10, 0x06, 0x8e, 0xf4, 0x67, 0x11, 0x0e, 0x09, 0x5f, 0x41, 0xb7, 0x57, 0xf2, 0xe0, 0x21,
	0x0e, 0x09, 0x7a, 0x1f, 0xfa, 0x45, 0x92, 0xef, 0x9a, 0xea, 0x48, 0xd9, 0x1a, 0xd8, 0x90, 0x87,
	0xa6, 0xae, 0xf5, 0x2b, 0x0c, 0x6f, 0x22, 0xcb, 0x12, 0xf4, 0x09, 0xe8, 0x12, 0x5a, 0x96, 0x48,
	0x6c, 0x2b, 0x25, 0xb6, 0x2c, 0xb1, 0x7b, 0x67, 0xd2, 0x42, 0x5f, 0x54, 0x80, 0x04, 0x7e, 0x46,
	0x4d, 0x75, 0xa4, 0x6d, 0xf5, 0x77, 0xde, 0x66, 0xe9, 0x37, 0x60, 0x97, 0xf8, 0x9e, 0xfb, 0x19,
	0xb5, 0xfe, 0x54, 0xca, 0xce, 0xd8, 0x38, 0xba, 0x64, 0xc1, 0x16, 0x9d, 0x41, 0xef, 0x01, 0x48,
	0x0e, 0xca, 0xf2, 0x74, 0x19, 0x99, 0xba, 0xcd, 0xf2, 0xb5, 0x66, 0xf9, 0xe8, 0x11, 0x2c, 0xc7,
	0xe7, 0xe7, 0x19, 0xa1, 0x66, 0x87, 0x3f, 0x93, 0x1e, 0x42, 0xd0, 0xc9, 0xfc, 0x5f, 0x88, 0xb9,
	0xc4, 0xa3, 0xdc, 0xb6, 0xfe, 0x55, 0x60, 0x50, 0x00, 0xe5, 0x14, 0x3c, 0x86, 0x9e, 0x60, 0xde,
	0x77, 0x39, 0xca, 0x81, 0xdd, 0xe5, 0xfe, 0xd4, 0x65, 0x0b, 0x70, 0x52, 0x54, 0x4e, 0x0a, 0xb7,
	0x19, 0x58, 0x91, 0x4e, 0x17, 0x09, 0x91, 0x60, 0x74, 0x1e, 0x39, 0x5d, 0x24, 0x04, 0x99, 0xd0,
	0x4d, 0x02, 0xbc, 0xf0, 0x23, 0x8f, 0x83, 0xe9, 0xd9, 0xb9, 0x8b, 0x36, 0x41, 0x77, 0xe2, 0x6b,
	0x92, 0xce, 0xe6, 0x69, 0xc0, 0x21, 0xe9, 0x76, 0x8f, 0x07, 0x5e, 0xa4, 0x01, 0x7a, 0x02, 0x3d,
	0x77, 0x9e, 0x62, 0xea, 0xc7, 0x91, 0xb9, 0xcc, 0xd7, 0x2c, 0x7c, 0x34, 0x84, 0xa5, 0xcc, 0x89,
	0x53, 0x62, 0x76, 0x47, 0xca, 0x96, 0x6a, 0x0b, 0xc7, 0xfa, 0x1a, 0x56, 0xf7, 0x2f, 0x70, 0xe4,
	0xbd, 0x6e, 0x21, 0x29, 0x8e, 0x2e, 0x65, 0x6f, 0xb9, 0x6d, 0xfd, 0xa7, 0x94, 0xaa, 0x29, 0x59,
	0x6b, 0xa7, 0x1a, 0x03, 0x34, 0x12, 0x09, 0xca, 0x7a, 0x36, 0x33, 0xd1, 0x33, 0x58, 0x13, 0x20,
	0xd8, 0x1e, 0x42, 0x49, 0x1a, 0x57, 0xd2, 0x7a, 0xa1, 0xa4, 0x1c, 0xb0, 0x3d, 0x08, 0xab, 0x7b,
	0xa3, 0x2f, 0xc1, 0x70, 0x78, 0x45, 0xe2, 0x5d, 0x3f, 0x3a, 0x8f, 0xcd, 0x0e, 0x7f, 0x17, 0xb1,
	0x77, 0xeb, 0xd5, 0xda, 0xab, 0x4e, 0xcd, 0xb7, 0xae, 0x61, 0xf5, 0x84, 0xe0, 0xd4, 0xb9, 0x10,
	0x7b, 0xb4, 0x90, 0x9f, 0x01, 0xda, 0x25, 0x59, 0x48, 0x92, 0x99, 0x59, 0x11, 0x94, 0xf6, 0x4a,
	0x41, 0x75, 0x2a, 0x82, 0xfa, 0x4b, 0x81, 0xb5, 0xca, 0xc6, 0x0f, 0x20, 0xa9, 0x9a, 0x70, 0x3a,
	0x77, 0x08, 0x67, 0xe9, 0x36, 0xe1, 0x2c, 0x57, 0x85, 0xf3, 0x47, 0x1d, 0xf0, 0x1b, 0x50, 0xde,
	0xe8, 0x96, 0x14, 0x81, 0x56, 0x8a, 0x60, 0x27, 0x2f, 0x88, 0xf3, 0x2f, 0x38, 0xdc, 0x60, 0xeb,
	0x35, 0x1a, 0x25, 0xab, 0xe4, 0x43, 0xe4, 0x07, 0x58, 0xcf, 0xd5, 0x38, 0x21, 0x14, 0xfb, 0x41,
	0x1b, 0x0a, 0xab, 0x0d, 0x57, 0x6b, 0x0d, 0xb7, 0xfe, 0x51, 0x61, 0xad, 0xb2, 0xea, 0x03, 0xf0,
	0xf3, 0x01, 0xac, 0xa4, 0x24, 0x20, 0x0c, 0xa7, 0x8b, 0x29, 0x91, 0x14, 0xf5, 0x65, 0x6c, 0x82,
	0x29, 0x61, 0x2c, 0xe5, 0xf3, 0xaa, 0x38, 0xfa, 0xd2, 0x67, 0xcf, 0x02, 0x1c, 0x79, 0x73, 0xec,
	0x09, 0xa2, 0x74, 0xbb, 0xf0, 0x99, 0x10, 0xb1, 0x43, 0xe3, 0x34, 0xe3, 0x67, 0x5f, 0xb7, 0xa5,
	0x57, 0x32, 0xdb, 0xab, 0x30, 0x8b, 0x46, 0xd0, 0x77, 0x49, 0xe6, 0xa4, 0x7e, 0xc2, 0xe5, 0xa0,
	0x0b, 0x1c, 0x95, 0x50, 0x5d, 0x4a, 0x70, 0x87, 0x94, 0xfa, 0x75, 0x29, 0x59, 0x19, 0xa0, 0x26,
	0x3b, 0xed, 0x64, 0x53, 0x48, 0x82, 0x1f, 0x6b, 0x95, 0xe7, 0x6e, 0x14, 0x23, 0xa1, 0xe4, 0x46,
	0x36, 0x96, 0x1f, 0x69, 0x0c, 0xe6, 0x01, 0xa1, 0xfb, 0xe2, 0x22, 0xe0, 0x79, 0x27, 0x14, 0xd3,
	0x79, 0x76, 0x7f, 0x77, 0x8b, 0xf5, 0xbb, 0x02, 0x8f, 0x6f, 0xd9, 0xa3, 0x5d, 0x7d, 0x7b, 0x80,
	0xf2, 0x7d, 0x6e, 0xd4, 0x39, 0xcc, 0xc7, 0x57, 0xbe, 0x05, 0x2f, 0xd4, 0x70, 0x1a, 0x11, 0xeb,
	0x67, 0xd8, 0x38, 0x0e, 0xf0, 0xa2, 0x9a, 0x79, 0x8f, 0xd7, 0x68, 0x55, 0xf4, 0x5a, 0xfd, 0x8c,
	0xec, 0xc2, 0xf0, 0xe6, 0xc6, 0xad, 0xea, 0xb7, 0xfe, 0x56, 0x60, 0x53, 0x4c, 0xe8, 0x87, 0xe4,
	0x8b, 0x1d, 0xcf, 0xca, 0x21, 0xe4, 0xb6, 0xf5, 0x0c, 0x06, 0x47, 0x09, 0x11, 0x42, 0xe5, 0x07,
	0x12, 0x60, 0xf9, 0xc5, 0xe1, 0x77, 0x47, 0x3f, 0x1e, 0x1a, 0x6f, 0xa1, 0x1e, 0x74, 0x8e, 0x9f,
	0xef, 0xbe, 0x34, 0x14, 0x66, 0x9d, 0x9c, 0x1e, 0x1d, 0x1b, 0x2a, 0xb3, 0x26, 0xf6, 0xee, 0x81,
	0xa1, 0x59, 0x53, 0x78, 0xf7, 0x76, 0xd0, 0xed, 0x1a, 0xf0, 0x9b, 0x02, 0x8f, 0x26, 0x29, 0xf6,
	0xaa, 0x2b, 0xed, 0xe1, 0xf4, 0x7e, 0xbf, 0x83, 0xc4, 0x3d, 0x34, 0xa3, 0x7e, 0x98, 0xb7, 0x00,
	0x44, 0xe8, 0xd4, 0x0f, 0x89, 0x35, 0x81, 0x77, 0x5e, 0x89, 0xa0, 0x65, 0x21, 0x2a, 0x18, 0x4d,
	0xb1, 0xb2, 0x41, 0x94, 0xf1, 0xb6, 0xc8, 0x79, 0x29, 0xbd, 0x26, 0x26, 0xb5, 0x89, 0xe9, 0x0e,
	0xd5, 0xa1, 0x8f, 0x60, 0x55, 0x3c, 0x2a, 0xa6, 0x8e, 0xb8, 0x57, 0xc5, 0x67, 0xc1, 0x24, 0xbf,
	0xc5, 0x36, 0x41, 0x8c, 0x84, 0xea, 0x77, 0x13, 0x0f, 0xb0, 0x99, 0x55, 0x8c, 0x66, 0x3e, 0xb4,
	0xc5, 0xf8, 0x14, 0xe9, 0xec, 0xcb, 0xd9, 0x7a, 0x0a, 0x20, 0xd8, 0x7c, 0x2d, 0x5d, 0x74, 0x41,
	0xfb, 0xe6, 0x70, 0x62, 0x68, 0x7b, 0xdf, 0x82, 0xe9, 0xc4, 0xe1, 0x78, 0xe1, 0x2f, 0xe2, 0x39,
	0xeb, 0x52, 0x18, 0xbb, 0x24, 0x10, 0x7f, 0x01, 0x3f, 0x7d, 0xea, 0xc5, 0x6c, 0x3c, 0x8f, 0x9f,
	0xee, 0x50, 0x3a, 0x76, 0xe2, 0x70, 0x9b, 0x87, 0x9d, 0x38, 0xd8, 0xc6, 0x49, 0xb2, 0x5d, 0xfb,
	0x97, 0x38, 0x5b, 0xe6, 0xcf, 0x3e, 0xff, 0x3f, 0x00, 0x00, 0xff, 0xff, 0x5e, 0x7a, 0x4e, 0x2b,
	0x63, 0x0c, 0x00, 0x00,
}
