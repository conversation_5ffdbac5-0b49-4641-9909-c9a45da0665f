// Code generated by protoc-gen-go. DO NOT EDIT.
// source: obs/obs.proto

package obs // import "golang.52tt.com/protocol/app/obs"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ClaimTokenRequest struct {
	BaseReq    *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	App        string       `protobuf:"bytes,2,opt,name=app,proto3" json:"app,omitempty"`
	Scope      string       `protobuf:"bytes,3,opt,name=scope,proto3" json:"scope,omitempty"`
	Key        string       `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	Expiration int32        `protobuf:"varint,5,opt,name=expiration,proto3" json:"expiration,omitempty"`
	// 只支持申请单个权限
	WithUploadPriv       bool     `protobuf:"varint,6,opt,name=with_upload_priv,json=withUploadPriv,proto3" json:"with_upload_priv,omitempty"`
	WithDownloadPriv     bool     `protobuf:"varint,7,opt,name=with_download_priv,json=withDownloadPriv,proto3" json:"with_download_priv,omitempty"`
	WithDeletePriv       bool     `protobuf:"varint,8,opt,name=with_delete_priv,json=withDeletePriv,proto3" json:"with_delete_priv,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClaimTokenRequest) Reset()         { *m = ClaimTokenRequest{} }
func (m *ClaimTokenRequest) String() string { return proto.CompactTextString(m) }
func (*ClaimTokenRequest) ProtoMessage()    {}
func (*ClaimTokenRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_bcea0b3f25532c85, []int{0}
}
func (m *ClaimTokenRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimTokenRequest.Unmarshal(m, b)
}
func (m *ClaimTokenRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimTokenRequest.Marshal(b, m, deterministic)
}
func (dst *ClaimTokenRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimTokenRequest.Merge(dst, src)
}
func (m *ClaimTokenRequest) XXX_Size() int {
	return xxx_messageInfo_ClaimTokenRequest.Size(m)
}
func (m *ClaimTokenRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimTokenRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimTokenRequest proto.InternalMessageInfo

func (m *ClaimTokenRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ClaimTokenRequest) GetApp() string {
	if m != nil {
		return m.App
	}
	return ""
}

func (m *ClaimTokenRequest) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

func (m *ClaimTokenRequest) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ClaimTokenRequest) GetExpiration() int32 {
	if m != nil {
		return m.Expiration
	}
	return 0
}

func (m *ClaimTokenRequest) GetWithUploadPriv() bool {
	if m != nil {
		return m.WithUploadPriv
	}
	return false
}

func (m *ClaimTokenRequest) GetWithDownloadPriv() bool {
	if m != nil {
		return m.WithDownloadPriv
	}
	return false
}

func (m *ClaimTokenRequest) GetWithDeletePriv() bool {
	if m != nil {
		return m.WithDeletePriv
	}
	return false
}

type ClaimTokenResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Token                string        `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	ExpireAt             int64         `protobuf:"varint,3,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ClaimTokenResponse) Reset()         { *m = ClaimTokenResponse{} }
func (m *ClaimTokenResponse) String() string { return proto.CompactTextString(m) }
func (*ClaimTokenResponse) ProtoMessage()    {}
func (*ClaimTokenResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_obs_bcea0b3f25532c85, []int{1}
}
func (m *ClaimTokenResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClaimTokenResponse.Unmarshal(m, b)
}
func (m *ClaimTokenResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClaimTokenResponse.Marshal(b, m, deterministic)
}
func (dst *ClaimTokenResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClaimTokenResponse.Merge(dst, src)
}
func (m *ClaimTokenResponse) XXX_Size() int {
	return xxx_messageInfo_ClaimTokenResponse.Size(m)
}
func (m *ClaimTokenResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ClaimTokenResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ClaimTokenResponse proto.InternalMessageInfo

func (m *ClaimTokenResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ClaimTokenResponse) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *ClaimTokenResponse) GetExpireAt() int64 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

func init() {
	proto.RegisterType((*ClaimTokenRequest)(nil), "ga.obs.ClaimTokenRequest")
	proto.RegisterType((*ClaimTokenResponse)(nil), "ga.obs.ClaimTokenResponse")
}

func init() { proto.RegisterFile("obs/obs.proto", fileDescriptor_obs_bcea0b3f25532c85) }

var fileDescriptor_obs_bcea0b3f25532c85 = []byte{
	// 342 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x4c, 0x91, 0xc1, 0x6a, 0xe3, 0x30,
	0x10, 0x86, 0x71, 0xb2, 0x49, 0x1c, 0x65, 0xb3, 0x64, 0xc5, 0x1e, 0xcc, 0x16, 0x8a, 0xc9, 0xa1,
	0xb8, 0x50, 0x1c, 0x48, 0xe9, 0xad, 0x97, 0xa6, 0x79, 0x80, 0x22, 0xda, 0x4b, 0x2f, 0x46, 0xb2,
	0x07, 0x57, 0xc4, 0xf6, 0x28, 0x96, 0x92, 0xd4, 0x4f, 0xd2, 0xd7, 0x2d, 0x92, 0x5c, 0x92, 0xdb,
	0xcc, 0xe7, 0xcf, 0x3f, 0xe8, 0x1f, 0x32, 0x47, 0xa1, 0x57, 0x28, 0x74, 0xaa, 0x5a, 0x34, 0x48,
	0xc7, 0x25, 0x4f, 0x51, 0xe8, 0xff, 0xf3, 0x92, 0x67, 0x82, 0x6b, 0xf0, 0x78, 0xf9, 0x35, 0x20,
	0x7f, 0x9f, 0x2b, 0x2e, 0xeb, 0x57, 0xdc, 0x41, 0xc3, 0x60, 0x7f, 0x00, 0x6d, 0xe8, 0x0d, 0x09,
	0xad, 0x93, 0xb5, 0xb0, 0x8f, 0x82, 0x38, 0x48, 0x66, 0xeb, 0x59, 0x5a, 0xf2, 0x74, 0xc3, 0x35,
	0x30, 0xd8, 0xb3, 0x89, 0xf0, 0x03, 0x5d, 0x90, 0x21, 0x57, 0x2a, 0x1a, 0xc4, 0x41, 0x32, 0x65,
	0x76, 0xa4, 0xff, 0xc8, 0x48, 0xe7, 0xa8, 0x20, 0x1a, 0x3a, 0xe6, 0x17, 0xeb, 0xed, 0xa0, 0x8b,
	0x7e, 0x79, 0x6f, 0x07, 0x1d, 0xbd, 0x26, 0x04, 0x3e, 0x95, 0x6c, 0xb9, 0x91, 0xd8, 0x44, 0xa3,
	0x38, 0x48, 0x46, 0xec, 0x82, 0xd0, 0x84, 0x2c, 0x4e, 0xd2, 0x7c, 0x64, 0x07, 0x55, 0x21, 0x2f,
	0x32, 0xd5, 0xca, 0x63, 0x34, 0x8e, 0x83, 0x24, 0x64, 0x7f, 0x2c, 0x7f, 0x73, 0xf8, 0xa5, 0x95,
	0x47, 0x7a, 0x47, 0xa8, 0x33, 0x0b, 0x3c, 0x35, 0x67, 0x77, 0xe2, 0x5c, 0x97, 0xb1, 0xed, 0x3f,
	0x38, 0xfb, 0x27, 0xb7, 0x80, 0x0a, 0x0c, 0x78, 0x37, 0x3c, 0xe7, 0x6e, 0x1d, 0xb6, 0xe6, 0xb2,
	0x25, 0xf4, 0xb2, 0x18, 0xad, 0xb0, 0xd1, 0x40, 0x6f, 0xc9, 0xb4, 0x6f, 0x46, 0xab, 0xbe, 0x9a,
	0xdf, 0xe7, 0x6a, 0xb4, 0x62, 0xa1, 0xe8, 0x27, 0x5b, 0x85, 0xb1, 0xff, 0xf6, 0xf5, 0xf8, 0x85,
	0x5e, 0x91, 0xa9, 0x7b, 0x26, 0x64, 0xdc, 0xb8, 0x92, 0x86, 0x2c, 0xf4, 0xe0, 0xc9, 0x6c, 0x1e,
	0x49, 0x94, 0x63, 0x9d, 0x76, 0xb2, 0xc3, 0x83, 0x4d, 0xad, 0xb1, 0x80, 0xca, 0x5f, 0xea, 0x3d,
	0x2e, 0xb1, 0xe2, 0x4d, 0x99, 0x3e, 0xac, 0x8d, 0x49, 0x73, 0xac, 0x57, 0x0e, 0xe7, 0x58, 0xad,
	0xb8, 0x52, 0xf6, 0xd0, 0x62, 0xec, 0xc8, 0xfd, 0x77, 0x00, 0x00, 0x00, 0xff, 0xff, 0xeb, 0xb4,
	0x43, 0xda, 0xfa, 0x01, 0x00, 0x00,
}
