// Code generated by protoc-gen-gogo.
// source: activity_.proto
// DO NOT EDIT!

/*
	Package activity is a generated protocol buffer package.

	It is generated from these files:
		activity_.proto

	It has these top-level messages:
		GetMyFirstVoucherReq
		FirstVoucherInfo
		GetMyFirstVoucherResp
		AwardUserTTGiftPkgReq
		AwardUserTTGiftPkgResp
		FirstRechargeLevelConfig
		FirstRechargeActivitiEntry
		NormalActivitiEntry
		FollowActivitiEntry
		PersonalMainPageEntry
		BaseActivitiEntry
		CheckUserFirstRechargeActEntryReq
		CheckUserFirstRechargeActEntryResp
		GetMutiLocationActEntryReq
		GetMutiLocationActEntryResp
		NewYear2019BeatActConf
		ChannelRoomMoleAttackGamePeriod
		ChannelRoomMoleAttackGameConf
		ReportNewYear2019BeatResultReq
		ReportNewYear2019BeatResultResp
		ReportMoleBeatResultReq
		ReportMoleBeatResultRsp
		GetNewYear2019BeatLotteryResultReq
		GetNewYear2019BeatLotteryResultResp
		NewYear2019BeatAward
		BeatAwardNotify
		RankingTitle
		Get2019YearActRankingListEntryReq
		Get2019YearActRankingListEntryResp
		RecommendFollowedInfo
		CommAdvInfo
		GetGangupTabAdvInfoRep
		GetGangupTabAdvInfoResp
		RankingListStyle
		ActRankingListConfig
		AnchorRankWarConfig
		GetCommonRankingListConfigReq
		GetCommonRankingListConfigResp
		AnchorRankingListConfig
		GetAnchorRankingListConfigReq
		GetAnchorRankingListConfigResp
*/
package activity

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ECommActivityEntryType int32

const (
	ECommActivityEntryType_ENUM_ACTIVITYENTRY_NONE              ECommActivityEntryType = 0
	ECommActivityEntryType_ENUM_ACTIVITYENTRY_FIRSTRECHARGE     ECommActivityEntryType = 1
	ECommActivityEntryType_ENUM_ACTIVITYENTRY_NEWBIE            ECommActivityEntryType = 2
	ECommActivityEntryType_ENUM_ACTIVITYENTRY_NORMAL            ECommActivityEntryType = 3
	ECommActivityEntryType_ENUM_ACTIVITYENTRY_FOLLOW            ECommActivityEntryType = 4
	ECommActivityEntryType_ENUM_ACTIVITYENTRY_PERSONAL_MAINPAGE ECommActivityEntryType = 5
)

var ECommActivityEntryType_name = map[int32]string{
	0: "ENUM_ACTIVITYENTRY_NONE",
	1: "ENUM_ACTIVITYENTRY_FIRSTRECHARGE",
	2: "ENUM_ACTIVITYENTRY_NEWBIE",
	3: "ENUM_ACTIVITYENTRY_NORMAL",
	4: "ENUM_ACTIVITYENTRY_FOLLOW",
	5: "ENUM_ACTIVITYENTRY_PERSONAL_MAINPAGE",
}
var ECommActivityEntryType_value = map[string]int32{
	"ENUM_ACTIVITYENTRY_NONE":              0,
	"ENUM_ACTIVITYENTRY_FIRSTRECHARGE":     1,
	"ENUM_ACTIVITYENTRY_NEWBIE":            2,
	"ENUM_ACTIVITYENTRY_NORMAL":            3,
	"ENUM_ACTIVITYENTRY_FOLLOW":            4,
	"ENUM_ACTIVITYENTRY_PERSONAL_MAINPAGE": 5,
}

func (x ECommActivityEntryType) Enum() *ECommActivityEntryType {
	p := new(ECommActivityEntryType)
	*p = x
	return p
}
func (x ECommActivityEntryType) String() string {
	return proto.EnumName(ECommActivityEntryType_name, int32(x))
}
func (x *ECommActivityEntryType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ECommActivityEntryType_value, data, "ECommActivityEntryType")
	if err != nil {
		return err
	}
	*x = ECommActivityEntryType(value)
	return nil
}
func (ECommActivityEntryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{0}
}

type MoleGameType int32

const (
	MoleGameType_InvalidGame     MoleGameType = 0
	MoleGameType_SeptemberGame   MoleGameType = 1
	MoleGameType_NewYearGame     MoleGameType = 2
	MoleGameType_NewYearGame2022 MoleGameType = 3
)

var MoleGameType_name = map[int32]string{
	0: "InvalidGame",
	1: "SeptemberGame",
	2: "NewYearGame",
	3: "NewYearGame2022",
}
var MoleGameType_value = map[string]int32{
	"InvalidGame":     0,
	"SeptemberGame":   1,
	"NewYearGame":     2,
	"NewYearGame2022": 3,
}

func (x MoleGameType) Enum() *MoleGameType {
	p := new(MoleGameType)
	*p = x
	return p
}
func (x MoleGameType) String() string {
	return proto.EnumName(MoleGameType_name, int32(x))
}
func (x *MoleGameType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MoleGameType_value, data, "MoleGameType")
	if err != nil {
		return err
	}
	*x = MoleGameType(value)
	return nil
}
func (MoleGameType) EnumDescriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{1} }

type FirstRechargeActivitiEntry_EFirstRechargeConfigType int32

const (
	FirstRechargeActivitiEntry_ENUM_RECHARGE_CONFIG_COMMON FirstRechargeActivitiEntry_EFirstRechargeConfigType = 0
	FirstRechargeActivitiEntry_ENUM_RECHARGE_CONFIG_LIST   FirstRechargeActivitiEntry_EFirstRechargeConfigType = 1
)

var FirstRechargeActivitiEntry_EFirstRechargeConfigType_name = map[int32]string{
	0: "ENUM_RECHARGE_CONFIG_COMMON",
	1: "ENUM_RECHARGE_CONFIG_LIST",
}
var FirstRechargeActivitiEntry_EFirstRechargeConfigType_value = map[string]int32{
	"ENUM_RECHARGE_CONFIG_COMMON": 0,
	"ENUM_RECHARGE_CONFIG_LIST":   1,
}

func (x FirstRechargeActivitiEntry_EFirstRechargeConfigType) Enum() *FirstRechargeActivitiEntry_EFirstRechargeConfigType {
	p := new(FirstRechargeActivitiEntry_EFirstRechargeConfigType)
	*p = x
	return p
}
func (x FirstRechargeActivitiEntry_EFirstRechargeConfigType) String() string {
	return proto.EnumName(FirstRechargeActivitiEntry_EFirstRechargeConfigType_name, int32(x))
}
func (x *FirstRechargeActivitiEntry_EFirstRechargeConfigType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FirstRechargeActivitiEntry_EFirstRechargeConfigType_value, data, "FirstRechargeActivitiEntry_EFirstRechargeConfigType")
	if err != nil {
		return err
	}
	*x = FirstRechargeActivitiEntry_EFirstRechargeConfigType(value)
	return nil
}
func (FirstRechargeActivitiEntry_EFirstRechargeConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{6, 0}
}

type GetMutiLocationActEntryReq_EActivityEntryLocationType int32

const (
	GetMutiLocationActEntryReq_ENUM_ENTRY_LOCATION_NONE              GetMutiLocationActEntryReq_EActivityEntryLocationType = 0
	GetMutiLocationActEntryReq_ENUM_ENTRY_LOCATION_ROOM              GetMutiLocationActEntryReq_EActivityEntryLocationType = 1
	GetMutiLocationActEntryReq_ENUM_ENTRY_LOCATION_MAINPAGE          GetMutiLocationActEntryReq_EActivityEntryLocationType = 2
	GetMutiLocationActEntryReq_ENUM_ENTRY_LOCATION_UGC               GetMutiLocationActEntryReq_EActivityEntryLocationType = 3
	GetMutiLocationActEntryReq_ENUM_ENTRY_LOCATION_FOLLOW            GetMutiLocationActEntryReq_EActivityEntryLocationType = 4
	GetMutiLocationActEntryReq_ENUM_ENTRY_LOCATION_PERSONAL_MAINPAGE GetMutiLocationActEntryReq_EActivityEntryLocationType = 5
)

var GetMutiLocationActEntryReq_EActivityEntryLocationType_name = map[int32]string{
	0: "ENUM_ENTRY_LOCATION_NONE",
	1: "ENUM_ENTRY_LOCATION_ROOM",
	2: "ENUM_ENTRY_LOCATION_MAINPAGE",
	3: "ENUM_ENTRY_LOCATION_UGC",
	4: "ENUM_ENTRY_LOCATION_FOLLOW",
	5: "ENUM_ENTRY_LOCATION_PERSONAL_MAINPAGE",
}
var GetMutiLocationActEntryReq_EActivityEntryLocationType_value = map[string]int32{
	"ENUM_ENTRY_LOCATION_NONE":              0,
	"ENUM_ENTRY_LOCATION_ROOM":              1,
	"ENUM_ENTRY_LOCATION_MAINPAGE":          2,
	"ENUM_ENTRY_LOCATION_UGC":               3,
	"ENUM_ENTRY_LOCATION_FOLLOW":            4,
	"ENUM_ENTRY_LOCATION_PERSONAL_MAINPAGE": 5,
}

func (x GetMutiLocationActEntryReq_EActivityEntryLocationType) Enum() *GetMutiLocationActEntryReq_EActivityEntryLocationType {
	p := new(GetMutiLocationActEntryReq_EActivityEntryLocationType)
	*p = x
	return p
}
func (x GetMutiLocationActEntryReq_EActivityEntryLocationType) String() string {
	return proto.EnumName(GetMutiLocationActEntryReq_EActivityEntryLocationType_name, int32(x))
}
func (x *GetMutiLocationActEntryReq_EActivityEntryLocationType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetMutiLocationActEntryReq_EActivityEntryLocationType_value, data, "GetMutiLocationActEntryReq_EActivityEntryLocationType")
	if err != nil {
		return err
	}
	*x = GetMutiLocationActEntryReq_EActivityEntryLocationType(value)
	return nil
}
func (GetMutiLocationActEntryReq_EActivityEntryLocationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{13, 0}
}

type GetGangupTabAdvInfoRep_ADV_SORT int32

const (
	GetGangupTabAdvInfoRep_E_ADV_SORT_DEFAULT        GetGangupTabAdvInfoRep_ADV_SORT = 0
	GetGangupTabAdvInfoRep_E_ADV_SORT_IM_PANEL       GetGangupTabAdvInfoRep_ADV_SORT = 1
	GetGangupTabAdvInfoRep_E_ADV_SORT_IM_PANEL_WHEEL GetGangupTabAdvInfoRep_ADV_SORT = 2
)

var GetGangupTabAdvInfoRep_ADV_SORT_name = map[int32]string{
	0: "E_ADV_SORT_DEFAULT",
	1: "E_ADV_SORT_IM_PANEL",
	2: "E_ADV_SORT_IM_PANEL_WHEEL",
}
var GetGangupTabAdvInfoRep_ADV_SORT_value = map[string]int32{
	"E_ADV_SORT_DEFAULT":        0,
	"E_ADV_SORT_IM_PANEL":       1,
	"E_ADV_SORT_IM_PANEL_WHEEL": 2,
}

func (x GetGangupTabAdvInfoRep_ADV_SORT) Enum() *GetGangupTabAdvInfoRep_ADV_SORT {
	p := new(GetGangupTabAdvInfoRep_ADV_SORT)
	*p = x
	return p
}
func (x GetGangupTabAdvInfoRep_ADV_SORT) String() string {
	return proto.EnumName(GetGangupTabAdvInfoRep_ADV_SORT_name, int32(x))
}
func (x *GetGangupTabAdvInfoRep_ADV_SORT) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetGangupTabAdvInfoRep_ADV_SORT_value, data, "GetGangupTabAdvInfoRep_ADV_SORT")
	if err != nil {
		return err
	}
	*x = GetGangupTabAdvInfoRep_ADV_SORT(value)
	return nil
}
func (GetGangupTabAdvInfoRep_ADV_SORT) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{31, 0}
}

// 顶部logo配色
type GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR int32

const (
	GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR_DEFAULT GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR = 0
	GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR_BLACK   GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR = 1
	GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR_WHITE   GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR = 2
)

var GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR_name = map[int32]string{
	0: "E_TOP_LOGO_COLOR_DEFAULT",
	1: "E_TOP_LOGO_COLOR_BLACK",
	2: "E_TOP_LOGO_COLOR_WHITE",
}
var GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR_value = map[string]int32{
	"E_TOP_LOGO_COLOR_DEFAULT": 0,
	"E_TOP_LOGO_COLOR_BLACK":   1,
	"E_TOP_LOGO_COLOR_WHITE":   2,
}

func (x GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR) Enum() *GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR {
	p := new(GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR)
	*p = x
	return p
}
func (x GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR) String() string {
	return proto.EnumName(GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR_name, int32(x))
}
func (x *GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR_value, data, "GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR")
	if err != nil {
		return err
	}
	*x = GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR(value)
	return nil
}
func (GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{32, 0}
}

// 跳转类型 （注：目前仅填跳转小游戏拉队友页面）
type GetGangupTabAdvInfoResp_E_JUMP_TYPE int32

const (
	GetGangupTabAdvInfoResp_E_JUMP_TYPE_DEFAULT GetGangupTabAdvInfoResp_E_JUMP_TYPE = 0
	GetGangupTabAdvInfoResp_E_JUMP_TYPE_TTLINK  GetGangupTabAdvInfoResp_E_JUMP_TYPE = 1
)

var GetGangupTabAdvInfoResp_E_JUMP_TYPE_name = map[int32]string{
	0: "E_JUMP_TYPE_DEFAULT",
	1: "E_JUMP_TYPE_TTLINK",
}
var GetGangupTabAdvInfoResp_E_JUMP_TYPE_value = map[string]int32{
	"E_JUMP_TYPE_DEFAULT": 0,
	"E_JUMP_TYPE_TTLINK":  1,
}

func (x GetGangupTabAdvInfoResp_E_JUMP_TYPE) Enum() *GetGangupTabAdvInfoResp_E_JUMP_TYPE {
	p := new(GetGangupTabAdvInfoResp_E_JUMP_TYPE)
	*p = x
	return p
}
func (x GetGangupTabAdvInfoResp_E_JUMP_TYPE) String() string {
	return proto.EnumName(GetGangupTabAdvInfoResp_E_JUMP_TYPE_name, int32(x))
}
func (x *GetGangupTabAdvInfoResp_E_JUMP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetGangupTabAdvInfoResp_E_JUMP_TYPE_value, data, "GetGangupTabAdvInfoResp_E_JUMP_TYPE")
	if err != nil {
		return err
	}
	*x = GetGangupTabAdvInfoResp_E_JUMP_TYPE(value)
	return nil
}
func (GetGangupTabAdvInfoResp_E_JUMP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{32, 1}
}

type GetMyFirstVoucherReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid     uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetMyFirstVoucherReq) Reset()                    { *m = GetMyFirstVoucherReq{} }
func (m *GetMyFirstVoucherReq) String() string            { return proto.CompactTextString(m) }
func (*GetMyFirstVoucherReq) ProtoMessage()               {}
func (*GetMyFirstVoucherReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{0} }

func (m *GetMyFirstVoucherReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMyFirstVoucherReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type FirstVoucherInfo struct {
	ProductId       uint32 `protobuf:"varint,1,req,name=product_id,json=productId" json:"product_id"`
	Worth           uint32 `protobuf:"varint,2,req,name=worth" json:"worth"`
	GameName        string `protobuf:"bytes,3,req,name=game_name,json=gameName" json:"game_name"`
	VoucherAccount  string `protobuf:"bytes,4,req,name=voucher_account,json=voucherAccount" json:"voucher_account"`
	VoucherPassword string `protobuf:"bytes,5,req,name=voucher_password,json=voucherPassword" json:"voucher_password"`
	PurchaseTime    uint32 `protobuf:"varint,6,req,name=purchase_time,json=purchaseTime" json:"purchase_time"`
	GameIcon        string `protobuf:"bytes,7,req,name=game_icon,json=gameIcon" json:"game_icon"`
}

func (m *FirstVoucherInfo) Reset()                    { *m = FirstVoucherInfo{} }
func (m *FirstVoucherInfo) String() string            { return proto.CompactTextString(m) }
func (*FirstVoucherInfo) ProtoMessage()               {}
func (*FirstVoucherInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{1} }

func (m *FirstVoucherInfo) GetProductId() uint32 {
	if m != nil {
		return m.ProductId
	}
	return 0
}

func (m *FirstVoucherInfo) GetWorth() uint32 {
	if m != nil {
		return m.Worth
	}
	return 0
}

func (m *FirstVoucherInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *FirstVoucherInfo) GetVoucherAccount() string {
	if m != nil {
		return m.VoucherAccount
	}
	return ""
}

func (m *FirstVoucherInfo) GetVoucherPassword() string {
	if m != nil {
		return m.VoucherPassword
	}
	return ""
}

func (m *FirstVoucherInfo) GetPurchaseTime() uint32 {
	if m != nil {
		return m.PurchaseTime
	}
	return 0
}

func (m *FirstVoucherInfo) GetGameIcon() string {
	if m != nil {
		return m.GameIcon
	}
	return ""
}

type GetMyFirstVoucherResp struct {
	BaseResp         *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	FirstVoucherList []*FirstVoucherInfo `protobuf:"bytes,2,rep,name=first_voucher_list,json=firstVoucherList" json:"first_voucher_list,omitempty"`
}

func (m *GetMyFirstVoucherResp) Reset()                    { *m = GetMyFirstVoucherResp{} }
func (m *GetMyFirstVoucherResp) String() string            { return proto.CompactTextString(m) }
func (*GetMyFirstVoucherResp) ProtoMessage()               {}
func (*GetMyFirstVoucherResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{2} }

func (m *GetMyFirstVoucherResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMyFirstVoucherResp) GetFirstVoucherList() []*FirstVoucherInfo {
	if m != nil {
		return m.FirstVoucherList
	}
	return nil
}

type AwardUserTTGiftPkgReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetUid uint32      `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *AwardUserTTGiftPkgReq) Reset()                    { *m = AwardUserTTGiftPkgReq{} }
func (m *AwardUserTTGiftPkgReq) String() string            { return proto.CompactTextString(m) }
func (*AwardUserTTGiftPkgReq) ProtoMessage()               {}
func (*AwardUserTTGiftPkgReq) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{3} }

func (m *AwardUserTTGiftPkgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AwardUserTTGiftPkgReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type AwardUserTTGiftPkgResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *AwardUserTTGiftPkgResp) Reset()                    { *m = AwardUserTTGiftPkgResp{} }
func (m *AwardUserTTGiftPkgResp) String() string            { return proto.CompactTextString(m) }
func (*AwardUserTTGiftPkgResp) ProtoMessage()               {}
func (*AwardUserTTGiftPkgResp) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{4} }

func (m *AwardUserTTGiftPkgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 首充活动入口档位配置
type FirstRechargeLevelConfig struct {
	LevelNum uint32 `protobuf:"varint,1,req,name=level_num,json=levelNum" json:"level_num"`
	ImgUrl   string `protobuf:"bytes,2,req,name=img_url,json=imgUrl" json:"img_url"`
}

func (m *FirstRechargeLevelConfig) Reset()         { *m = FirstRechargeLevelConfig{} }
func (m *FirstRechargeLevelConfig) String() string { return proto.CompactTextString(m) }
func (*FirstRechargeLevelConfig) ProtoMessage()    {}
func (*FirstRechargeLevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{5}
}

func (m *FirstRechargeLevelConfig) GetLevelNum() uint32 {
	if m != nil {
		return m.LevelNum
	}
	return 0
}

func (m *FirstRechargeLevelConfig) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

type FirstRechargeActivitiEntry struct {
	EntryIconUrl     string                      `protobuf:"bytes,1,req,name=entry_icon_url,json=entryIconUrl" json:"entry_icon_url"`
	BackImgUrl       string                      `protobuf:"bytes,2,req,name=back_img_url,json=backImgUrl" json:"back_img_url"`
	FinishTs         uint32                      `protobuf:"varint,3,req,name=finish_ts,json=finishTs" json:"finish_ts"`
	PopUpFrequence   uint32                      `protobuf:"varint,4,opt,name=pop_up_frequence,json=popUpFrequence" json:"pop_up_frequence"`
	LevelConfigList  []*FirstRechargeLevelConfig `protobuf:"bytes,5,rep,name=level_config_list,json=levelConfigList" json:"level_config_list,omitempty"`
	ConfigType       uint32                      `protobuf:"varint,6,opt,name=config_type,json=configType" json:"config_type"`
	EntryDesc        string                      `protobuf:"bytes,7,opt,name=entry_desc,json=entryDesc" json:"entry_desc"`
	EntryImgUrl      string                      `protobuf:"bytes,8,opt,name=entry_img_url,json=entryImgUrl" json:"entry_img_url"`
	EntryPresentName string                      `protobuf:"bytes,9,opt,name=entry_present_name,json=entryPresentName" json:"entry_present_name"`
}

func (m *FirstRechargeActivitiEntry) Reset()         { *m = FirstRechargeActivitiEntry{} }
func (m *FirstRechargeActivitiEntry) String() string { return proto.CompactTextString(m) }
func (*FirstRechargeActivitiEntry) ProtoMessage()    {}
func (*FirstRechargeActivitiEntry) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{6}
}

func (m *FirstRechargeActivitiEntry) GetEntryIconUrl() string {
	if m != nil {
		return m.EntryIconUrl
	}
	return ""
}

func (m *FirstRechargeActivitiEntry) GetBackImgUrl() string {
	if m != nil {
		return m.BackImgUrl
	}
	return ""
}

func (m *FirstRechargeActivitiEntry) GetFinishTs() uint32 {
	if m != nil {
		return m.FinishTs
	}
	return 0
}

func (m *FirstRechargeActivitiEntry) GetPopUpFrequence() uint32 {
	if m != nil {
		return m.PopUpFrequence
	}
	return 0
}

func (m *FirstRechargeActivitiEntry) GetLevelConfigList() []*FirstRechargeLevelConfig {
	if m != nil {
		return m.LevelConfigList
	}
	return nil
}

func (m *FirstRechargeActivitiEntry) GetConfigType() uint32 {
	if m != nil {
		return m.ConfigType
	}
	return 0
}

func (m *FirstRechargeActivitiEntry) GetEntryDesc() string {
	if m != nil {
		return m.EntryDesc
	}
	return ""
}

func (m *FirstRechargeActivitiEntry) GetEntryImgUrl() string {
	if m != nil {
		return m.EntryImgUrl
	}
	return ""
}

func (m *FirstRechargeActivitiEntry) GetEntryPresentName() string {
	if m != nil {
		return m.EntryPresentName
	}
	return ""
}

type NormalActivitiEntry struct {
	EntryIconUrl string `protobuf:"bytes,1,req,name=entry_icon_url,json=entryIconUrl" json:"entry_icon_url"`
	JumpUrl      string `protobuf:"bytes,2,req,name=jump_url,json=jumpUrl" json:"jump_url"`
	FinishTs     uint32 `protobuf:"varint,3,req,name=finish_ts,json=finishTs" json:"finish_ts"`
}

func (m *NormalActivitiEntry) Reset()                    { *m = NormalActivitiEntry{} }
func (m *NormalActivitiEntry) String() string            { return proto.CompactTextString(m) }
func (*NormalActivitiEntry) ProtoMessage()               {}
func (*NormalActivitiEntry) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{7} }

func (m *NormalActivitiEntry) GetEntryIconUrl() string {
	if m != nil {
		return m.EntryIconUrl
	}
	return ""
}

func (m *NormalActivitiEntry) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *NormalActivitiEntry) GetFinishTs() uint32 {
	if m != nil {
		return m.FinishTs
	}
	return 0
}

type FollowActivitiEntry struct {
	EntryIconUrl string   `protobuf:"bytes,1,req,name=entry_icon_url,json=entryIconUrl" json:"entry_icon_url"`
	EntryIconMd5 string   `protobuf:"bytes,2,req,name=entry_icon_md5,json=entryIconMd5" json:"entry_icon_md5"`
	EntryText    string   `protobuf:"bytes,3,opt,name=entry_text,json=entryText" json:"entry_text"`
	JumpRoomId   []uint32 `protobuf:"varint,4,rep,name=jump_room_id,json=jumpRoomId" json:"jump_room_id,omitempty"`
	JumpUrl      []string `protobuf:"bytes,5,rep,name=jump_url,json=jumpUrl" json:"jump_url,omitempty"`
	FinishTs     uint32   `protobuf:"varint,6,req,name=finish_ts,json=finishTs" json:"finish_ts"`
}

func (m *FollowActivitiEntry) Reset()                    { *m = FollowActivitiEntry{} }
func (m *FollowActivitiEntry) String() string            { return proto.CompactTextString(m) }
func (*FollowActivitiEntry) ProtoMessage()               {}
func (*FollowActivitiEntry) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{8} }

func (m *FollowActivitiEntry) GetEntryIconUrl() string {
	if m != nil {
		return m.EntryIconUrl
	}
	return ""
}

func (m *FollowActivitiEntry) GetEntryIconMd5() string {
	if m != nil {
		return m.EntryIconMd5
	}
	return ""
}

func (m *FollowActivitiEntry) GetEntryText() string {
	if m != nil {
		return m.EntryText
	}
	return ""
}

func (m *FollowActivitiEntry) GetJumpRoomId() []uint32 {
	if m != nil {
		return m.JumpRoomId
	}
	return nil
}

func (m *FollowActivitiEntry) GetJumpUrl() []string {
	if m != nil {
		return m.JumpUrl
	}
	return nil
}

func (m *FollowActivitiEntry) GetFinishTs() uint32 {
	if m != nil {
		return m.FinishTs
	}
	return 0
}

type PersonalMainPageEntry struct {
	AdvId        string `protobuf:"bytes,1,req,name=adv_id,json=advId" json:"adv_id"`
	EntryIconUrl string `protobuf:"bytes,2,req,name=entry_icon_url,json=entryIconUrl" json:"entry_icon_url"`
	JumpUrl      string `protobuf:"bytes,3,req,name=jump_url,json=jumpUrl" json:"jump_url"`
	EndTime      uint32 `protobuf:"varint,4,req,name=end_time,json=endTime" json:"end_time"`
}

func (m *PersonalMainPageEntry) Reset()                    { *m = PersonalMainPageEntry{} }
func (m *PersonalMainPageEntry) String() string            { return proto.CompactTextString(m) }
func (*PersonalMainPageEntry) ProtoMessage()               {}
func (*PersonalMainPageEntry) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{9} }

func (m *PersonalMainPageEntry) GetAdvId() string {
	if m != nil {
		return m.AdvId
	}
	return ""
}

func (m *PersonalMainPageEntry) GetEntryIconUrl() string {
	if m != nil {
		return m.EntryIconUrl
	}
	return ""
}

func (m *PersonalMainPageEntry) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *PersonalMainPageEntry) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type BaseActivitiEntry struct {
	EntryType          uint32                      `protobuf:"varint,1,req,name=entry_type,json=entryType" json:"entry_type"`
	FirstRechargeEntry *FirstRechargeActivitiEntry `protobuf:"bytes,2,opt,name=first_recharge_entry,json=firstRechargeEntry" json:"first_recharge_entry,omitempty"`
	NormalEntry        *NormalActivitiEntry        `protobuf:"bytes,3,opt,name=normal_entry,json=normalEntry" json:"normal_entry,omitempty"`
	FollowEntry        *FollowActivitiEntry        `protobuf:"bytes,4,opt,name=follow_entry,json=followEntry" json:"follow_entry,omitempty"`
	MainPageEntry      *PersonalMainPageEntry      `protobuf:"bytes,5,opt,name=main_page_entry,json=mainPageEntry" json:"main_page_entry,omitempty"`
}

func (m *BaseActivitiEntry) Reset()                    { *m = BaseActivitiEntry{} }
func (m *BaseActivitiEntry) String() string            { return proto.CompactTextString(m) }
func (*BaseActivitiEntry) ProtoMessage()               {}
func (*BaseActivitiEntry) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{10} }

func (m *BaseActivitiEntry) GetEntryType() uint32 {
	if m != nil {
		return m.EntryType
	}
	return 0
}

func (m *BaseActivitiEntry) GetFirstRechargeEntry() *FirstRechargeActivitiEntry {
	if m != nil {
		return m.FirstRechargeEntry
	}
	return nil
}

func (m *BaseActivitiEntry) GetNormalEntry() *NormalActivitiEntry {
	if m != nil {
		return m.NormalEntry
	}
	return nil
}

func (m *BaseActivitiEntry) GetFollowEntry() *FollowActivitiEntry {
	if m != nil {
		return m.FollowEntry
	}
	return nil
}

func (m *BaseActivitiEntry) GetMainPageEntry() *PersonalMainPageEntry {
	if m != nil {
		return m.MainPageEntry
	}
	return nil
}

// 首次充值资格查询（资格 与 实际充值时间之间 是有时间间隔的）
type CheckUserFirstRechargeActEntryReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *CheckUserFirstRechargeActEntryReq) Reset()         { *m = CheckUserFirstRechargeActEntryReq{} }
func (m *CheckUserFirstRechargeActEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserFirstRechargeActEntryReq) ProtoMessage()    {}
func (*CheckUserFirstRechargeActEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{11}
}

func (m *CheckUserFirstRechargeActEntryReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type CheckUserFirstRechargeActEntryResp struct {
	BaseResp           *ga.BaseResp                `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	IsFirstRechargeFin bool                        `protobuf:"varint,2,req,name=is_first_recharge_fin,json=isFirstRechargeFin" json:"is_first_recharge_fin"`
	EntryInfo          *FirstRechargeActivitiEntry `protobuf:"bytes,3,opt,name=entry_info,json=entryInfo" json:"entry_info,omitempty"`
}

func (m *CheckUserFirstRechargeActEntryResp) Reset()         { *m = CheckUserFirstRechargeActEntryResp{} }
func (m *CheckUserFirstRechargeActEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserFirstRechargeActEntryResp) ProtoMessage()    {}
func (*CheckUserFirstRechargeActEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{12}
}

func (m *CheckUserFirstRechargeActEntryResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckUserFirstRechargeActEntryResp) GetIsFirstRechargeFin() bool {
	if m != nil {
		return m.IsFirstRechargeFin
	}
	return false
}

func (m *CheckUserFirstRechargeActEntryResp) GetEntryInfo() *FirstRechargeActivitiEntry {
	if m != nil {
		return m.EntryInfo
	}
	return nil
}

// 活动广告位获取接口
type GetMutiLocationActEntryReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	LocationType uint32      `protobuf:"varint,2,req,name=location_type,json=locationType" json:"location_type"`
	ChannelPack  string      `protobuf:"bytes,3,opt,name=channel_pack,json=channelPack" json:"channel_pack"`
}

func (m *GetMutiLocationActEntryReq) Reset()         { *m = GetMutiLocationActEntryReq{} }
func (m *GetMutiLocationActEntryReq) String() string { return proto.CompactTextString(m) }
func (*GetMutiLocationActEntryReq) ProtoMessage()    {}
func (*GetMutiLocationActEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{13}
}

func (m *GetMutiLocationActEntryReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMutiLocationActEntryReq) GetLocationType() uint32 {
	if m != nil {
		return m.LocationType
	}
	return 0
}

func (m *GetMutiLocationActEntryReq) GetChannelPack() string {
	if m != nil {
		return m.ChannelPack
	}
	return ""
}

type GetMutiLocationActEntryResp struct {
	BaseResp     *ga.BaseResp         `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	LocationType uint32               `protobuf:"varint,2,req,name=location_type,json=locationType" json:"location_type"`
	EntryList    []*BaseActivitiEntry `protobuf:"bytes,3,rep,name=entry_list,json=entryList" json:"entry_list,omitempty"`
}

func (m *GetMutiLocationActEntryResp) Reset()         { *m = GetMutiLocationActEntryResp{} }
func (m *GetMutiLocationActEntryResp) String() string { return proto.CompactTextString(m) }
func (*GetMutiLocationActEntryResp) ProtoMessage()    {}
func (*GetMutiLocationActEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{14}
}

func (m *GetMutiLocationActEntryResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMutiLocationActEntryResp) GetLocationType() uint32 {
	if m != nil {
		return m.LocationType
	}
	return 0
}

func (m *GetMutiLocationActEntryResp) GetEntryList() []*BaseActivitiEntry {
	if m != nil {
		return m.EntryList
	}
	return nil
}

// 2019新年打年兽活动配置
type NewYear2019BeatActConf struct {
	ActBeginTs                     uint32 `protobuf:"varint,1,req,name=act_begin_ts,json=actBeginTs" json:"act_begin_ts"`
	ActFinishTs                    uint32 `protobuf:"varint,2,req,name=act_finish_ts,json=actFinishTs" json:"act_finish_ts"`
	IntervalPeriodTs               uint32 `protobuf:"varint,3,req,name=interval_period_ts,json=intervalPeriodTs" json:"interval_period_ts"`
	PayedNewyearCallsTs            uint32 `protobuf:"varint,4,opt,name=payed_newyear_calls_ts,json=payedNewyearCallsTs" json:"payed_newyear_calls_ts"`
	RandomGamestartDelaySecond     uint32 `protobuf:"varint,5,opt,name=random_gamestart_delay_second,json=randomGamestartDelaySecond" json:"random_gamestart_delay_second"`
	RandomGamefinReportDelaySecond uint32 `protobuf:"varint,6,opt,name=random_gamefin_report_delay_second,json=randomGamefinReportDelaySecond" json:"random_gamefin_report_delay_second"`
	CatchAtleastBeatCnt            uint32 `protobuf:"varint,7,opt,name=catch_atleast_beat_cnt,json=catchAtleastBeatCnt" json:"catch_atleast_beat_cnt"`
}

func (m *NewYear2019BeatActConf) Reset()                    { *m = NewYear2019BeatActConf{} }
func (m *NewYear2019BeatActConf) String() string            { return proto.CompactTextString(m) }
func (*NewYear2019BeatActConf) ProtoMessage()               {}
func (*NewYear2019BeatActConf) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{15} }

func (m *NewYear2019BeatActConf) GetActBeginTs() uint32 {
	if m != nil {
		return m.ActBeginTs
	}
	return 0
}

func (m *NewYear2019BeatActConf) GetActFinishTs() uint32 {
	if m != nil {
		return m.ActFinishTs
	}
	return 0
}

func (m *NewYear2019BeatActConf) GetIntervalPeriodTs() uint32 {
	if m != nil {
		return m.IntervalPeriodTs
	}
	return 0
}

func (m *NewYear2019BeatActConf) GetPayedNewyearCallsTs() uint32 {
	if m != nil {
		return m.PayedNewyearCallsTs
	}
	return 0
}

func (m *NewYear2019BeatActConf) GetRandomGamestartDelaySecond() uint32 {
	if m != nil {
		return m.RandomGamestartDelaySecond
	}
	return 0
}

func (m *NewYear2019BeatActConf) GetRandomGamefinReportDelaySecond() uint32 {
	if m != nil {
		return m.RandomGamefinReportDelaySecond
	}
	return 0
}

func (m *NewYear2019BeatActConf) GetCatchAtleastBeatCnt() uint32 {
	if m != nil {
		return m.CatchAtleastBeatCnt
	}
	return 0
}

// 打地鼠类型的活动配置
type ChannelRoomMoleAttackGamePeriod struct {
	PeriodBeginTs    uint32 `protobuf:"varint,1,req,name=period_begin_ts,json=periodBeginTs" json:"period_begin_ts"`
	PeriodFinishTs   uint32 `protobuf:"varint,2,req,name=period_finish_ts,json=periodFinishTs" json:"period_finish_ts"`
	PeriodIntervalTs uint32 `protobuf:"varint,3,req,name=period_interval_ts,json=periodIntervalTs" json:"period_interval_ts"`
}

func (m *ChannelRoomMoleAttackGamePeriod) Reset()         { *m = ChannelRoomMoleAttackGamePeriod{} }
func (m *ChannelRoomMoleAttackGamePeriod) String() string { return proto.CompactTextString(m) }
func (*ChannelRoomMoleAttackGamePeriod) ProtoMessage()    {}
func (*ChannelRoomMoleAttackGamePeriod) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{16}
}

func (m *ChannelRoomMoleAttackGamePeriod) GetPeriodBeginTs() uint32 {
	if m != nil {
		return m.PeriodBeginTs
	}
	return 0
}

func (m *ChannelRoomMoleAttackGamePeriod) GetPeriodFinishTs() uint32 {
	if m != nil {
		return m.PeriodFinishTs
	}
	return 0
}

func (m *ChannelRoomMoleAttackGamePeriod) GetPeriodIntervalTs() uint32 {
	if m != nil {
		return m.PeriodIntervalTs
	}
	return 0
}

type ChannelRoomMoleAttackGameConf struct {
	GameName                       string                             `protobuf:"bytes,1,req,name=game_name,json=gameName" json:"game_name"`
	GameBeginTs                    uint32                             `protobuf:"varint,2,req,name=game_begin_ts,json=gameBeginTs" json:"game_begin_ts"`
	GameFinishTs                   uint32                             `protobuf:"varint,3,req,name=game_finish_ts,json=gameFinishTs" json:"game_finish_ts"`
	PeriodConfList                 []*ChannelRoomMoleAttackGamePeriod `protobuf:"bytes,4,rep,name=period_conf_list,json=periodConfList" json:"period_conf_list,omitempty"`
	RandomGamestartDelaySecond     uint32                             `protobuf:"varint,5,opt,name=random_gamestart_delay_second,json=randomGamestartDelaySecond" json:"random_gamestart_delay_second"`
	RandomGamefinReportDelaySecond uint32                             `protobuf:"varint,6,opt,name=random_gamefin_report_delay_second,json=randomGamefinReportDelaySecond" json:"random_gamefin_report_delay_second"`
	GamePrepareSecond              uint32                             `protobuf:"varint,7,opt,name=game_prepare_second,json=gamePrepareSecond" json:"game_prepare_second"`
	GameDurationSecond             uint32                             `protobuf:"varint,8,opt,name=game_duration_second,json=gameDurationSecond" json:"game_duration_second"`
	MoleAppearCnt                  uint32                             `protobuf:"varint,9,opt,name=mole_appear_cnt,json=moleAppearCnt" json:"mole_appear_cnt"`
	AttackAtleastCnt               uint32                             `protobuf:"varint,10,opt,name=attack_atleast_cnt,json=attackAtleastCnt" json:"attack_atleast_cnt"`
	NewYearTime                    int64                              `protobuf:"varint,11,opt,name=new_year_time,json=newYearTime" json:"new_year_time"`
	NeweYearSecond                 uint32                             `protobuf:"varint,12,opt,name=newe_year_second,json=neweYearSecond" json:"newe_year_second"`
	MoleGameType                   uint32                             `protobuf:"varint,13,req,name=mole_game_type,json=moleGameType" json:"mole_game_type"`
}

func (m *ChannelRoomMoleAttackGameConf) Reset()         { *m = ChannelRoomMoleAttackGameConf{} }
func (m *ChannelRoomMoleAttackGameConf) String() string { return proto.CompactTextString(m) }
func (*ChannelRoomMoleAttackGameConf) ProtoMessage()    {}
func (*ChannelRoomMoleAttackGameConf) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{17}
}

func (m *ChannelRoomMoleAttackGameConf) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *ChannelRoomMoleAttackGameConf) GetGameBeginTs() uint32 {
	if m != nil {
		return m.GameBeginTs
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetGameFinishTs() uint32 {
	if m != nil {
		return m.GameFinishTs
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetPeriodConfList() []*ChannelRoomMoleAttackGamePeriod {
	if m != nil {
		return m.PeriodConfList
	}
	return nil
}

func (m *ChannelRoomMoleAttackGameConf) GetRandomGamestartDelaySecond() uint32 {
	if m != nil {
		return m.RandomGamestartDelaySecond
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetRandomGamefinReportDelaySecond() uint32 {
	if m != nil {
		return m.RandomGamefinReportDelaySecond
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetGamePrepareSecond() uint32 {
	if m != nil {
		return m.GamePrepareSecond
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetGameDurationSecond() uint32 {
	if m != nil {
		return m.GameDurationSecond
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetMoleAppearCnt() uint32 {
	if m != nil {
		return m.MoleAppearCnt
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetAttackAtleastCnt() uint32 {
	if m != nil {
		return m.AttackAtleastCnt
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetNewYearTime() int64 {
	if m != nil {
		return m.NewYearTime
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetNeweYearSecond() uint32 {
	if m != nil {
		return m.NeweYearSecond
	}
	return 0
}

func (m *ChannelRoomMoleAttackGameConf) GetMoleGameType() uint32 {
	if m != nil {
		return m.MoleGameType
	}
	return 0
}

// 客户端上报打年兽的结果
type ReportNewYear2019BeatResultReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	BeatCnt   uint32      `protobuf:"varint,3,req,name=beat_cnt,json=beatCnt" json:"beat_cnt"`
}

func (m *ReportNewYear2019BeatResultReq) Reset()         { *m = ReportNewYear2019BeatResultReq{} }
func (m *ReportNewYear2019BeatResultReq) String() string { return proto.CompactTextString(m) }
func (*ReportNewYear2019BeatResultReq) ProtoMessage()    {}
func (*ReportNewYear2019BeatResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{18}
}

func (m *ReportNewYear2019BeatResultReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportNewYear2019BeatResultReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportNewYear2019BeatResultReq) GetBeatCnt() uint32 {
	if m != nil {
		return m.BeatCnt
	}
	return 0
}

type ReportNewYear2019BeatResultResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ReportNewYear2019BeatResultResp) Reset()         { *m = ReportNewYear2019BeatResultResp{} }
func (m *ReportNewYear2019BeatResultResp) String() string { return proto.CompactTextString(m) }
func (*ReportNewYear2019BeatResultResp) ProtoMessage()    {}
func (*ReportNewYear2019BeatResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{19}
}

func (m *ReportNewYear2019BeatResultResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 2020打年兽
type ReportMoleBeatResultReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId    uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	BeatCnt      uint32      `protobuf:"varint,3,req,name=beat_cnt,json=beatCnt" json:"beat_cnt"`
	MoleGameType uint32      `protobuf:"varint,4,req,name=mole_game_type,json=moleGameType" json:"mole_game_type"`
}

func (m *ReportMoleBeatResultReq) Reset()         { *m = ReportMoleBeatResultReq{} }
func (m *ReportMoleBeatResultReq) String() string { return proto.CompactTextString(m) }
func (*ReportMoleBeatResultReq) ProtoMessage()    {}
func (*ReportMoleBeatResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{20}
}

func (m *ReportMoleBeatResultReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportMoleBeatResultReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportMoleBeatResultReq) GetBeatCnt() uint32 {
	if m != nil {
		return m.BeatCnt
	}
	return 0
}

func (m *ReportMoleBeatResultReq) GetMoleGameType() uint32 {
	if m != nil {
		return m.MoleGameType
	}
	return 0
}

type ReportMoleBeatResultRsp struct {
	BaseResp  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	AwardName string       `protobuf:"bytes,2,req,name=award_name,json=awardName" json:"award_name"`
	AwardUrl  string       `protobuf:"bytes,3,req,name=award_url,json=awardUrl" json:"award_url"`
}

func (m *ReportMoleBeatResultRsp) Reset()         { *m = ReportMoleBeatResultRsp{} }
func (m *ReportMoleBeatResultRsp) String() string { return proto.CompactTextString(m) }
func (*ReportMoleBeatResultRsp) ProtoMessage()    {}
func (*ReportMoleBeatResultRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{21}
}

func (m *ReportMoleBeatResultRsp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ReportMoleBeatResultRsp) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *ReportMoleBeatResultRsp) GetAwardUrl() string {
	if m != nil {
		return m.AwardUrl
	}
	return ""
}

// 客户端 如果超时还没有收到奖励结果的PUSH 可以调用该接口来拉取
type GetNewYear2019BeatLotteryResultReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetNewYear2019BeatLotteryResultReq) Reset()         { *m = GetNewYear2019BeatLotteryResultReq{} }
func (m *GetNewYear2019BeatLotteryResultReq) String() string { return proto.CompactTextString(m) }
func (*GetNewYear2019BeatLotteryResultReq) ProtoMessage()    {}
func (*GetNewYear2019BeatLotteryResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{22}
}

func (m *GetNewYear2019BeatLotteryResultReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNewYear2019BeatLotteryResultReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetNewYear2019BeatLotteryResultResp struct {
	BaseResp  *ga.BaseResp          `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	AwardInfo *NewYear2019BeatAward `protobuf:"bytes,2,req,name=award_info,json=awardInfo" json:"award_info,omitempty"`
}

func (m *GetNewYear2019BeatLotteryResultResp) Reset()         { *m = GetNewYear2019BeatLotteryResultResp{} }
func (m *GetNewYear2019BeatLotteryResultResp) String() string { return proto.CompactTextString(m) }
func (*GetNewYear2019BeatLotteryResultResp) ProtoMessage()    {}
func (*GetNewYear2019BeatLotteryResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{23}
}

func (m *GetNewYear2019BeatLotteryResultResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNewYear2019BeatLotteryResultResp) GetAwardInfo() *NewYear2019BeatAward {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

// 打年兽活动的奖励
type NewYear2019BeatAward struct {
	ChannelId     uint32            `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid           uint32            `protobuf:"varint,2,req,name=uid" json:"uid"`
	AwardName     string            `protobuf:"bytes,3,opt,name=award_name,json=awardName" json:"award_name"`
	AwardUrl      string            `protobuf:"bytes,4,opt,name=award_url,json=awardUrl" json:"award_url"`
	RecommendUser *ga.GenericMember `protobuf:"bytes,5,opt,name=recommend_user,json=recommendUser" json:"recommend_user,omitempty"`
}

func (m *NewYear2019BeatAward) Reset()                    { *m = NewYear2019BeatAward{} }
func (m *NewYear2019BeatAward) String() string            { return proto.CompactTextString(m) }
func (*NewYear2019BeatAward) ProtoMessage()               {}
func (*NewYear2019BeatAward) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{24} }

func (m *NewYear2019BeatAward) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NewYear2019BeatAward) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NewYear2019BeatAward) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *NewYear2019BeatAward) GetAwardUrl() string {
	if m != nil {
		return m.AwardUrl
	}
	return ""
}

func (m *NewYear2019BeatAward) GetRecommendUser() *ga.GenericMember {
	if m != nil {
		return m.RecommendUser
	}
	return nil
}

// 2020年后通用的打年兽活动
type BeatAwardNotify struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	AwardName string `protobuf:"bytes,3,opt,name=award_name,json=awardName" json:"award_name"`
	AwardUrl  string `protobuf:"bytes,4,opt,name=award_url,json=awardUrl" json:"award_url"`
}

func (m *BeatAwardNotify) Reset()                    { *m = BeatAwardNotify{} }
func (m *BeatAwardNotify) String() string            { return proto.CompactTextString(m) }
func (*BeatAwardNotify) ProtoMessage()               {}
func (*BeatAwardNotify) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{25} }

func (m *BeatAwardNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BeatAwardNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BeatAwardNotify) GetAwardName() string {
	if m != nil {
		return m.AwardName
	}
	return ""
}

func (m *BeatAwardNotify) GetAwardUrl() string {
	if m != nil {
		return m.AwardUrl
	}
	return ""
}

// 2019年度盛典榜单配置相关
// 榜单标题
type RankingTitle struct {
	RankingType  uint32 `protobuf:"varint,1,req,name=ranking_type,json=rankingType" json:"ranking_type"`
	RankingTitle string `protobuf:"bytes,2,req,name=ranking_title,json=rankingTitle" json:"ranking_title"`
}

func (m *RankingTitle) Reset()                    { *m = RankingTitle{} }
func (m *RankingTitle) String() string            { return proto.CompactTextString(m) }
func (*RankingTitle) ProtoMessage()               {}
func (*RankingTitle) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{26} }

func (m *RankingTitle) GetRankingType() uint32 {
	if m != nil {
		return m.RankingType
	}
	return 0
}

func (m *RankingTitle) GetRankingTitle() string {
	if m != nil {
		return m.RankingTitle
	}
	return ""
}

type Get2019YearActRankingListEntryReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *Get2019YearActRankingListEntryReq) Reset()         { *m = Get2019YearActRankingListEntryReq{} }
func (m *Get2019YearActRankingListEntryReq) String() string { return proto.CompactTextString(m) }
func (*Get2019YearActRankingListEntryReq) ProtoMessage()    {}
func (*Get2019YearActRankingListEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{27}
}

func (m *Get2019YearActRankingListEntryReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type Get2019YearActRankingListEntryResp struct {
	BaseResp          *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RankingListUrl    string          `protobuf:"bytes,2,opt,name=ranking_list_url,json=rankingListUrl" json:"ranking_list_url"`
	UserRankingUrl    string          `protobuf:"bytes,3,opt,name=user_ranking_url,json=userRankingUrl" json:"user_ranking_url"`
	CompeteRankingUrl string          `protobuf:"bytes,4,opt,name=compete_ranking_url,json=competeRankingUrl" json:"compete_ranking_url"`
	BeginTs           uint32          `protobuf:"varint,5,opt,name=begin_ts,json=beginTs" json:"begin_ts"`
	EndTs             uint32          `protobuf:"varint,6,opt,name=end_ts,json=endTs" json:"end_ts"`
	RankingTitleList  []*RankingTitle `protobuf:"bytes,7,rep,name=ranking_title_list,json=rankingTitleList" json:"ranking_title_list,omitempty"`
	FreshenDelaySec   uint32          `protobuf:"varint,8,opt,name=freshen_delay_sec,json=freshenDelaySec" json:"freshen_delay_sec"`
}

func (m *Get2019YearActRankingListEntryResp) Reset()         { *m = Get2019YearActRankingListEntryResp{} }
func (m *Get2019YearActRankingListEntryResp) String() string { return proto.CompactTextString(m) }
func (*Get2019YearActRankingListEntryResp) ProtoMessage()    {}
func (*Get2019YearActRankingListEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{28}
}

func (m *Get2019YearActRankingListEntryResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *Get2019YearActRankingListEntryResp) GetRankingListUrl() string {
	if m != nil {
		return m.RankingListUrl
	}
	return ""
}

func (m *Get2019YearActRankingListEntryResp) GetUserRankingUrl() string {
	if m != nil {
		return m.UserRankingUrl
	}
	return ""
}

func (m *Get2019YearActRankingListEntryResp) GetCompeteRankingUrl() string {
	if m != nil {
		return m.CompeteRankingUrl
	}
	return ""
}

func (m *Get2019YearActRankingListEntryResp) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *Get2019YearActRankingListEntryResp) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *Get2019YearActRankingListEntryResp) GetRankingTitleList() []*RankingTitle {
	if m != nil {
		return m.RankingTitleList
	}
	return nil
}

func (m *Get2019YearActRankingListEntryResp) GetFreshenDelaySec() uint32 {
	if m != nil {
		return m.FreshenDelaySec
	}
	return 0
}

// 推荐关注者信息
type RecommendFollowedInfo struct {
	RecommendFollowedUid      uint32 `protobuf:"varint,1,opt,name=recommend_followed_uid,json=recommendFollowedUid" json:"recommend_followed_uid"`
	RecommendFollowedTtid     string `protobuf:"bytes,2,opt,name=recommend_followed_ttid,json=recommendFollowedTtid" json:"recommend_followed_ttid"`
	RecommendFollowedNickname string `protobuf:"bytes,3,opt,name=recommend_followed_nickname,json=recommendFollowedNickname" json:"recommend_followed_nickname"`
	RecommendFollowedPicUrl   string `protobuf:"bytes,4,opt,name=recommend_followed_pic_url,json=recommendFollowedPicUrl" json:"recommend_followed_pic_url"`
	RecommendFollowedPicMd5   string `protobuf:"bytes,5,opt,name=recommend_followed_pic_md5,json=recommendFollowedPicMd5" json:"recommend_followed_pic_md5"`
	RecommendFollowedPicText  string `protobuf:"bytes,6,opt,name=recommend_followed_pic_text,json=recommendFollowedPicText" json:"recommend_followed_pic_text"`
}

func (m *RecommendFollowedInfo) Reset()                    { *m = RecommendFollowedInfo{} }
func (m *RecommendFollowedInfo) String() string            { return proto.CompactTextString(m) }
func (*RecommendFollowedInfo) ProtoMessage()               {}
func (*RecommendFollowedInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{29} }

func (m *RecommendFollowedInfo) GetRecommendFollowedUid() uint32 {
	if m != nil {
		return m.RecommendFollowedUid
	}
	return 0
}

func (m *RecommendFollowedInfo) GetRecommendFollowedTtid() string {
	if m != nil {
		return m.RecommendFollowedTtid
	}
	return ""
}

func (m *RecommendFollowedInfo) GetRecommendFollowedNickname() string {
	if m != nil {
		return m.RecommendFollowedNickname
	}
	return ""
}

func (m *RecommendFollowedInfo) GetRecommendFollowedPicUrl() string {
	if m != nil {
		return m.RecommendFollowedPicUrl
	}
	return ""
}

func (m *RecommendFollowedInfo) GetRecommendFollowedPicMd5() string {
	if m != nil {
		return m.RecommendFollowedPicMd5
	}
	return ""
}

func (m *RecommendFollowedInfo) GetRecommendFollowedPicText() string {
	if m != nil {
		return m.RecommendFollowedPicText
	}
	return ""
}

type CommAdvInfo struct {
	AdPicUrl      string `protobuf:"bytes,1,opt,name=ad_pic_url,json=adPicUrl" json:"ad_pic_url"`
	AdPicMd5      string `protobuf:"bytes,2,opt,name=ad_pic_md5,json=adPicMd5" json:"ad_pic_md5"`
	RoomChannelId uint32 `protobuf:"varint,3,opt,name=room_channel_id,json=roomChannelId" json:"room_channel_id"`
	ActiveUrl     string `protobuf:"bytes,4,opt,name=active_url,json=activeUrl" json:"active_url"`
	BeginTs       uint32 `protobuf:"varint,5,opt,name=begin_ts,json=beginTs" json:"begin_ts"`
	EndTs         uint32 `protobuf:"varint,6,opt,name=end_ts,json=endTs" json:"end_ts"`
	AdId          uint32 `protobuf:"varint,7,opt,name=ad_id,json=adId" json:"ad_id"`
	Index         uint32 `protobuf:"varint,8,opt,name=index" json:"index"`
}

func (m *CommAdvInfo) Reset()                    { *m = CommAdvInfo{} }
func (m *CommAdvInfo) String() string            { return proto.CompactTextString(m) }
func (*CommAdvInfo) ProtoMessage()               {}
func (*CommAdvInfo) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{30} }

func (m *CommAdvInfo) GetAdPicUrl() string {
	if m != nil {
		return m.AdPicUrl
	}
	return ""
}

func (m *CommAdvInfo) GetAdPicMd5() string {
	if m != nil {
		return m.AdPicMd5
	}
	return ""
}

func (m *CommAdvInfo) GetRoomChannelId() uint32 {
	if m != nil {
		return m.RoomChannelId
	}
	return 0
}

func (m *CommAdvInfo) GetActiveUrl() string {
	if m != nil {
		return m.ActiveUrl
	}
	return ""
}

func (m *CommAdvInfo) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *CommAdvInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *CommAdvInfo) GetAdId() uint32 {
	if m != nil {
		return m.AdId
	}
	return 0
}

func (m *CommAdvInfo) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

// 首页开黑tab广告位配置
type GetGangupTabAdvInfoRep struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelPackTag string      `protobuf:"bytes,2,req,name=channel_pack_tag,json=channelPackTag" json:"channel_pack_tag"`
	AdvSort        uint32      `protobuf:"varint,3,opt,name=adv_sort,json=advSort" json:"adv_sort"`
}

func (m *GetGangupTabAdvInfoRep) Reset()                    { *m = GetGangupTabAdvInfoRep{} }
func (m *GetGangupTabAdvInfoRep) String() string            { return proto.CompactTextString(m) }
func (*GetGangupTabAdvInfoRep) ProtoMessage()               {}
func (*GetGangupTabAdvInfoRep) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{31} }

func (m *GetGangupTabAdvInfoRep) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGangupTabAdvInfoRep) GetChannelPackTag() string {
	if m != nil {
		return m.ChannelPackTag
	}
	return ""
}

func (m *GetGangupTabAdvInfoRep) GetAdvSort() uint32 {
	if m != nil {
		return m.AdvSort
	}
	return 0
}

// 首页开黑tab广告位配置
type GetGangupTabAdvInfoResp struct {
	BaseResp              *ga.BaseResp           `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	AdPicUrl              string                 `protobuf:"bytes,2,opt,name=ad_pic_url,json=adPicUrl" json:"ad_pic_url"`
	AdPicMd5              string                 `protobuf:"bytes,3,opt,name=ad_pic_md5,json=adPicMd5" json:"ad_pic_md5"`
	AdPicText             string                 `protobuf:"bytes,4,opt,name=ad_pic_text,json=adPicText" json:"ad_pic_text"`
	TextColor             string                 `protobuf:"bytes,5,opt,name=text_color,json=textColor" json:"text_color"`
	TopLogoColor          uint32                 `protobuf:"varint,6,opt,name=top_logo_color,json=topLogoColor" json:"top_logo_color"`
	RoomChannelId         uint32                 `protobuf:"varint,7,opt,name=room_channel_id,json=roomChannelId" json:"room_channel_id"`
	RecommendFollowedInfo *RecommendFollowedInfo `protobuf:"bytes,8,opt,name=recommend_followed_info,json=recommendFollowedInfo" json:"recommend_followed_info,omitempty"`
	ActiveUrl             string                 `protobuf:"bytes,9,opt,name=active_url,json=activeUrl" json:"active_url"`
	BeginTs               uint32                 `protobuf:"varint,10,opt,name=begin_ts,json=beginTs" json:"begin_ts"`
	EndTs                 uint32                 `protobuf:"varint,11,opt,name=end_ts,json=endTs" json:"end_ts"`
	AdId                  uint32                 `protobuf:"varint,12,opt,name=ad_id,json=adId" json:"ad_id"`
	CommAdvInfo           []*CommAdvInfo         `protobuf:"bytes,13,rep,name=commAdvInfo" json:"commAdvInfo,omitempty"`
	JumpType              uint32                 `protobuf:"varint,14,opt,name=jump_type,json=jumpType" json:"jump_type"`
	TTLink                string                 `protobuf:"bytes,15,opt,name=TTLink" json:"TTLink"`
	TTLinkExtraParams     string                 `protobuf:"bytes,16,opt,name=TTLinkExtraParams" json:"TTLinkExtraParams"`
}

func (m *GetGangupTabAdvInfoResp) Reset()         { *m = GetGangupTabAdvInfoResp{} }
func (m *GetGangupTabAdvInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGangupTabAdvInfoResp) ProtoMessage()    {}
func (*GetGangupTabAdvInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{32}
}

func (m *GetGangupTabAdvInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGangupTabAdvInfoResp) GetAdPicUrl() string {
	if m != nil {
		return m.AdPicUrl
	}
	return ""
}

func (m *GetGangupTabAdvInfoResp) GetAdPicMd5() string {
	if m != nil {
		return m.AdPicMd5
	}
	return ""
}

func (m *GetGangupTabAdvInfoResp) GetAdPicText() string {
	if m != nil {
		return m.AdPicText
	}
	return ""
}

func (m *GetGangupTabAdvInfoResp) GetTextColor() string {
	if m != nil {
		return m.TextColor
	}
	return ""
}

func (m *GetGangupTabAdvInfoResp) GetTopLogoColor() uint32 {
	if m != nil {
		return m.TopLogoColor
	}
	return 0
}

func (m *GetGangupTabAdvInfoResp) GetRoomChannelId() uint32 {
	if m != nil {
		return m.RoomChannelId
	}
	return 0
}

func (m *GetGangupTabAdvInfoResp) GetRecommendFollowedInfo() *RecommendFollowedInfo {
	if m != nil {
		return m.RecommendFollowedInfo
	}
	return nil
}

func (m *GetGangupTabAdvInfoResp) GetActiveUrl() string {
	if m != nil {
		return m.ActiveUrl
	}
	return ""
}

func (m *GetGangupTabAdvInfoResp) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetGangupTabAdvInfoResp) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetGangupTabAdvInfoResp) GetAdId() uint32 {
	if m != nil {
		return m.AdId
	}
	return 0
}

func (m *GetGangupTabAdvInfoResp) GetCommAdvInfo() []*CommAdvInfo {
	if m != nil {
		return m.CommAdvInfo
	}
	return nil
}

func (m *GetGangupTabAdvInfoResp) GetJumpType() uint32 {
	if m != nil {
		return m.JumpType
	}
	return 0
}

func (m *GetGangupTabAdvInfoResp) GetTTLink() string {
	if m != nil {
		return m.TTLink
	}
	return ""
}

func (m *GetGangupTabAdvInfoResp) GetTTLinkExtraParams() string {
	if m != nil {
		return m.TTLinkExtraParams
	}
	return ""
}

// 榜单样式
type RankingListStyle struct {
	FontBrightColor  string `protobuf:"bytes,1,opt,name=font_bright_color,json=fontBrightColor" json:"font_bright_color"`
	FontDarkColor    string `protobuf:"bytes,2,opt,name=font_dark_color,json=fontDarkColor" json:"font_dark_color"`
	ChannelButtonUrl string `protobuf:"bytes,3,opt,name=channel_button_url,json=channelButtonUrl" json:"channel_button_url"`
	RefreshButtonUrl string `protobuf:"bytes,4,opt,name=refresh_button_url,json=refreshButtonUrl" json:"refresh_button_url"`
	EntryBgUrl       string `protobuf:"bytes,5,opt,name=entry_bg_url,json=entryBgUrl" json:"entry_bg_url"`
	HalfScreenBgUrl  string `protobuf:"bytes,6,opt,name=half_screen_bg_url,json=halfScreenBgUrl" json:"half_screen_bg_url"`
	EntryTitleUrl    string `protobuf:"bytes,7,opt,name=entry_title_url,json=entryTitleUrl" json:"entry_title_url"`
	RankColor        string `protobuf:"bytes,8,opt,name=rank_color,json=rankColor" json:"rank_color"`
	HalfFloatColor   string `protobuf:"bytes,9,opt,name=half_float_color,json=halfFloatColor" json:"half_float_color"`
}

func (m *RankingListStyle) Reset()                    { *m = RankingListStyle{} }
func (m *RankingListStyle) String() string            { return proto.CompactTextString(m) }
func (*RankingListStyle) ProtoMessage()               {}
func (*RankingListStyle) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{33} }

func (m *RankingListStyle) GetFontBrightColor() string {
	if m != nil {
		return m.FontBrightColor
	}
	return ""
}

func (m *RankingListStyle) GetFontDarkColor() string {
	if m != nil {
		return m.FontDarkColor
	}
	return ""
}

func (m *RankingListStyle) GetChannelButtonUrl() string {
	if m != nil {
		return m.ChannelButtonUrl
	}
	return ""
}

func (m *RankingListStyle) GetRefreshButtonUrl() string {
	if m != nil {
		return m.RefreshButtonUrl
	}
	return ""
}

func (m *RankingListStyle) GetEntryBgUrl() string {
	if m != nil {
		return m.EntryBgUrl
	}
	return ""
}

func (m *RankingListStyle) GetHalfScreenBgUrl() string {
	if m != nil {
		return m.HalfScreenBgUrl
	}
	return ""
}

func (m *RankingListStyle) GetEntryTitleUrl() string {
	if m != nil {
		return m.EntryTitleUrl
	}
	return ""
}

func (m *RankingListStyle) GetRankColor() string {
	if m != nil {
		return m.RankColor
	}
	return ""
}

func (m *RankingListStyle) GetHalfFloatColor() string {
	if m != nil {
		return m.HalfFloatColor
	}
	return ""
}

// 榜单配置信息
type ActRankingListConfig struct {
	ActName         string            `protobuf:"bytes,1,opt,name=act_name,json=actName" json:"act_name"`
	DisCtypeList    []uint32          `protobuf:"varint,2,rep,name=dis_ctype_list,json=disCtypeList" json:"dis_ctype_list,omitempty"`
	BeginTs         uint32            `protobuf:"varint,3,opt,name=begin_ts,json=beginTs" json:"begin_ts"`
	EndTs           uint32            `protobuf:"varint,4,opt,name=end_ts,json=endTs" json:"end_ts"`
	EntryUrl        string            `protobuf:"bytes,5,opt,name=entry_url,json=entryUrl" json:"entry_url"`
	HalfScreenUrl   string            `protobuf:"bytes,6,opt,name=half_screen_url,json=halfScreenUrl" json:"half_screen_url"`
	UserRankUrl     string            `protobuf:"bytes,7,opt,name=user_rank_url,json=userRankUrl" json:"user_rank_url"`
	RankStyle       *RankingListStyle `protobuf:"bytes,8,opt,name=rank_style,json=rankStyle" json:"rank_style,omitempty"`
	FreshenDelaySec uint32            `protobuf:"varint,9,opt,name=freshen_delay_sec,json=freshenDelaySec" json:"freshen_delay_sec"`
}

func (m *ActRankingListConfig) Reset()                    { *m = ActRankingListConfig{} }
func (m *ActRankingListConfig) String() string            { return proto.CompactTextString(m) }
func (*ActRankingListConfig) ProtoMessage()               {}
func (*ActRankingListConfig) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{34} }

func (m *ActRankingListConfig) GetActName() string {
	if m != nil {
		return m.ActName
	}
	return ""
}

func (m *ActRankingListConfig) GetDisCtypeList() []uint32 {
	if m != nil {
		return m.DisCtypeList
	}
	return nil
}

func (m *ActRankingListConfig) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ActRankingListConfig) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ActRankingListConfig) GetEntryUrl() string {
	if m != nil {
		return m.EntryUrl
	}
	return ""
}

func (m *ActRankingListConfig) GetHalfScreenUrl() string {
	if m != nil {
		return m.HalfScreenUrl
	}
	return ""
}

func (m *ActRankingListConfig) GetUserRankUrl() string {
	if m != nil {
		return m.UserRankUrl
	}
	return ""
}

func (m *ActRankingListConfig) GetRankStyle() *RankingListStyle {
	if m != nil {
		return m.RankStyle
	}
	return nil
}

func (m *ActRankingListConfig) GetFreshenDelaySec() uint32 {
	if m != nil {
		return m.FreshenDelaySec
	}
	return 0
}

// 主播排位战配置
type AnchorRankWarConfig struct {
	EntryBgUrl        string `protobuf:"bytes,1,opt,name=entry_bg_url,json=entryBgUrl" json:"entry_bg_url"`
	EntryTitleColor   string `protobuf:"bytes,2,opt,name=entry_title_color,json=entryTitleColor" json:"entry_title_color"`
	EntryContentColor string `protobuf:"bytes,3,opt,name=entry_content_color,json=entryContentColor" json:"entry_content_color"`
}

func (m *AnchorRankWarConfig) Reset()                    { *m = AnchorRankWarConfig{} }
func (m *AnchorRankWarConfig) String() string            { return proto.CompactTextString(m) }
func (*AnchorRankWarConfig) ProtoMessage()               {}
func (*AnchorRankWarConfig) Descriptor() ([]byte, []int) { return fileDescriptorActivity_, []int{35} }

func (m *AnchorRankWarConfig) GetEntryBgUrl() string {
	if m != nil {
		return m.EntryBgUrl
	}
	return ""
}

func (m *AnchorRankWarConfig) GetEntryTitleColor() string {
	if m != nil {
		return m.EntryTitleColor
	}
	return ""
}

func (m *AnchorRankWarConfig) GetEntryContentColor() string {
	if m != nil {
		return m.EntryContentColor
	}
	return ""
}

// 获取通用实时榜单配置
type GetCommonRankingListConfigReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetCommonRankingListConfigReq) Reset()         { *m = GetCommonRankingListConfigReq{} }
func (m *GetCommonRankingListConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetCommonRankingListConfigReq) ProtoMessage()    {}
func (*GetCommonRankingListConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{36}
}

func (m *GetCommonRankingListConfigReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetCommonRankingListConfigResp struct {
	BaseResp     *ga.BaseResp            `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RankConfList []*ActRankingListConfig `protobuf:"bytes,2,rep,name=rank_conf_list,json=rankConfList" json:"rank_conf_list,omitempty"`
}

func (m *GetCommonRankingListConfigResp) Reset()         { *m = GetCommonRankingListConfigResp{} }
func (m *GetCommonRankingListConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetCommonRankingListConfigResp) ProtoMessage()    {}
func (*GetCommonRankingListConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{37}
}

func (m *GetCommonRankingListConfigResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCommonRankingListConfigResp) GetRankConfList() []*ActRankingListConfig {
	if m != nil {
		return m.RankConfList
	}
	return nil
}

type AnchorRankingListConfig struct {
	RankConf    *ActRankingListConfig `protobuf:"bytes,1,opt,name=rank_conf,json=rankConf" json:"rank_conf,omitempty"`
	RankWarConf *AnchorRankWarConfig  `protobuf:"bytes,2,opt,name=rank_war_conf,json=rankWarConf" json:"rank_war_conf,omitempty"`
}

func (m *AnchorRankingListConfig) Reset()         { *m = AnchorRankingListConfig{} }
func (m *AnchorRankingListConfig) String() string { return proto.CompactTextString(m) }
func (*AnchorRankingListConfig) ProtoMessage()    {}
func (*AnchorRankingListConfig) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{38}
}

func (m *AnchorRankingListConfig) GetRankConf() *ActRankingListConfig {
	if m != nil {
		return m.RankConf
	}
	return nil
}

func (m *AnchorRankingListConfig) GetRankWarConf() *AnchorRankWarConfig {
	if m != nil {
		return m.RankWarConf
	}
	return nil
}

// 获取主播实时榜单配置
type GetAnchorRankingListConfigReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetAnchorRankingListConfigReq) Reset()         { *m = GetAnchorRankingListConfigReq{} }
func (m *GetAnchorRankingListConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorRankingListConfigReq) ProtoMessage()    {}
func (*GetAnchorRankingListConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{39}
}

func (m *GetAnchorRankingListConfigReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetAnchorRankingListConfigResp struct {
	BaseResp     *ga.BaseResp               `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RankConfList []*AnchorRankingListConfig `protobuf:"bytes,2,rep,name=rank_conf_list,json=rankConfList" json:"rank_conf_list,omitempty"`
}

func (m *GetAnchorRankingListConfigResp) Reset()         { *m = GetAnchorRankingListConfigResp{} }
func (m *GetAnchorRankingListConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorRankingListConfigResp) ProtoMessage()    {}
func (*GetAnchorRankingListConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptorActivity_, []int{40}
}

func (m *GetAnchorRankingListConfigResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAnchorRankingListConfigResp) GetRankConfList() []*AnchorRankingListConfig {
	if m != nil {
		return m.RankConfList
	}
	return nil
}

func init() {
	proto.RegisterType((*GetMyFirstVoucherReq)(nil), "ga.GetMyFirstVoucherReq")
	proto.RegisterType((*FirstVoucherInfo)(nil), "ga.FirstVoucherInfo")
	proto.RegisterType((*GetMyFirstVoucherResp)(nil), "ga.GetMyFirstVoucherResp")
	proto.RegisterType((*AwardUserTTGiftPkgReq)(nil), "ga.AwardUserTTGiftPkgReq")
	proto.RegisterType((*AwardUserTTGiftPkgResp)(nil), "ga.AwardUserTTGiftPkgResp")
	proto.RegisterType((*FirstRechargeLevelConfig)(nil), "ga.FirstRechargeLevelConfig")
	proto.RegisterType((*FirstRechargeActivitiEntry)(nil), "ga.FirstRechargeActivitiEntry")
	proto.RegisterType((*NormalActivitiEntry)(nil), "ga.NormalActivitiEntry")
	proto.RegisterType((*FollowActivitiEntry)(nil), "ga.FollowActivitiEntry")
	proto.RegisterType((*PersonalMainPageEntry)(nil), "ga.PersonalMainPageEntry")
	proto.RegisterType((*BaseActivitiEntry)(nil), "ga.BaseActivitiEntry")
	proto.RegisterType((*CheckUserFirstRechargeActEntryReq)(nil), "ga.CheckUserFirstRechargeActEntryReq")
	proto.RegisterType((*CheckUserFirstRechargeActEntryResp)(nil), "ga.CheckUserFirstRechargeActEntryResp")
	proto.RegisterType((*GetMutiLocationActEntryReq)(nil), "ga.GetMutiLocationActEntryReq")
	proto.RegisterType((*GetMutiLocationActEntryResp)(nil), "ga.GetMutiLocationActEntryResp")
	proto.RegisterType((*NewYear2019BeatActConf)(nil), "ga.NewYear2019BeatActConf")
	proto.RegisterType((*ChannelRoomMoleAttackGamePeriod)(nil), "ga.ChannelRoomMoleAttackGamePeriod")
	proto.RegisterType((*ChannelRoomMoleAttackGameConf)(nil), "ga.ChannelRoomMoleAttackGameConf")
	proto.RegisterType((*ReportNewYear2019BeatResultReq)(nil), "ga.ReportNewYear2019BeatResultReq")
	proto.RegisterType((*ReportNewYear2019BeatResultResp)(nil), "ga.ReportNewYear2019BeatResultResp")
	proto.RegisterType((*ReportMoleBeatResultReq)(nil), "ga.ReportMoleBeatResultReq")
	proto.RegisterType((*ReportMoleBeatResultRsp)(nil), "ga.ReportMoleBeatResultRsp")
	proto.RegisterType((*GetNewYear2019BeatLotteryResultReq)(nil), "ga.GetNewYear2019BeatLotteryResultReq")
	proto.RegisterType((*GetNewYear2019BeatLotteryResultResp)(nil), "ga.GetNewYear2019BeatLotteryResultResp")
	proto.RegisterType((*NewYear2019BeatAward)(nil), "ga.NewYear2019BeatAward")
	proto.RegisterType((*BeatAwardNotify)(nil), "ga.BeatAwardNotify")
	proto.RegisterType((*RankingTitle)(nil), "ga.RankingTitle")
	proto.RegisterType((*Get2019YearActRankingListEntryReq)(nil), "ga.Get2019YearActRankingListEntryReq")
	proto.RegisterType((*Get2019YearActRankingListEntryResp)(nil), "ga.Get2019YearActRankingListEntryResp")
	proto.RegisterType((*RecommendFollowedInfo)(nil), "ga.RecommendFollowedInfo")
	proto.RegisterType((*CommAdvInfo)(nil), "ga.CommAdvInfo")
	proto.RegisterType((*GetGangupTabAdvInfoRep)(nil), "ga.GetGangupTabAdvInfoRep")
	proto.RegisterType((*GetGangupTabAdvInfoResp)(nil), "ga.GetGangupTabAdvInfoResp")
	proto.RegisterType((*RankingListStyle)(nil), "ga.RankingListStyle")
	proto.RegisterType((*ActRankingListConfig)(nil), "ga.ActRankingListConfig")
	proto.RegisterType((*AnchorRankWarConfig)(nil), "ga.AnchorRankWarConfig")
	proto.RegisterType((*GetCommonRankingListConfigReq)(nil), "ga.GetCommonRankingListConfigReq")
	proto.RegisterType((*GetCommonRankingListConfigResp)(nil), "ga.GetCommonRankingListConfigResp")
	proto.RegisterType((*AnchorRankingListConfig)(nil), "ga.AnchorRankingListConfig")
	proto.RegisterType((*GetAnchorRankingListConfigReq)(nil), "ga.GetAnchorRankingListConfigReq")
	proto.RegisterType((*GetAnchorRankingListConfigResp)(nil), "ga.GetAnchorRankingListConfigResp")
	proto.RegisterEnum("ga.ECommActivityEntryType", ECommActivityEntryType_name, ECommActivityEntryType_value)
	proto.RegisterEnum("ga.MoleGameType", MoleGameType_name, MoleGameType_value)
	proto.RegisterEnum("ga.FirstRechargeActivitiEntry_EFirstRechargeConfigType", FirstRechargeActivitiEntry_EFirstRechargeConfigType_name, FirstRechargeActivitiEntry_EFirstRechargeConfigType_value)
	proto.RegisterEnum("ga.GetMutiLocationActEntryReq_EActivityEntryLocationType", GetMutiLocationActEntryReq_EActivityEntryLocationType_name, GetMutiLocationActEntryReq_EActivityEntryLocationType_value)
	proto.RegisterEnum("ga.GetGangupTabAdvInfoRep_ADV_SORT", GetGangupTabAdvInfoRep_ADV_SORT_name, GetGangupTabAdvInfoRep_ADV_SORT_value)
	proto.RegisterEnum("ga.GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR", GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR_name, GetGangupTabAdvInfoResp_E_TOP_LOGO_COLOR_value)
	proto.RegisterEnum("ga.GetGangupTabAdvInfoResp_E_JUMP_TYPE", GetGangupTabAdvInfoResp_E_JUMP_TYPE_name, GetGangupTabAdvInfoResp_E_JUMP_TYPE_value)
}
func (m *GetMyFirstVoucherReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMyFirstVoucherReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *FirstVoucherInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstVoucherInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.ProductId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.Worth))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.VoucherAccount)))
	i += copy(dAtA[i:], m.VoucherAccount)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.VoucherPassword)))
	i += copy(dAtA[i:], m.VoucherPassword)
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.PurchaseTime))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.GameIcon)))
	i += copy(dAtA[i:], m.GameIcon)
	return i, nil
}

func (m *GetMyFirstVoucherResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMyFirstVoucherResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.FirstVoucherList) > 0 {
		for _, msg := range m.FirstVoucherList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AwardUserTTGiftPkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardUserTTGiftPkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *AwardUserTTGiftPkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardUserTTGiftPkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *FirstRechargeLevelConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstRechargeLevelConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.LevelNum))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.ImgUrl)))
	i += copy(dAtA[i:], m.ImgUrl)
	return i, nil
}

func (m *FirstRechargeActivitiEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstRechargeActivitiEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryIconUrl)))
	i += copy(dAtA[i:], m.EntryIconUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.BackImgUrl)))
	i += copy(dAtA[i:], m.BackImgUrl)
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.FinishTs))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.PopUpFrequence))
	if len(m.LevelConfigList) > 0 {
		for _, msg := range m.LevelConfigList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.ConfigType))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryDesc)))
	i += copy(dAtA[i:], m.EntryDesc)
	dAtA[i] = 0x42
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryImgUrl)))
	i += copy(dAtA[i:], m.EntryImgUrl)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryPresentName)))
	i += copy(dAtA[i:], m.EntryPresentName)
	return i, nil
}

func (m *NormalActivitiEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NormalActivitiEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryIconUrl)))
	i += copy(dAtA[i:], m.EntryIconUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.FinishTs))
	return i, nil
}

func (m *FollowActivitiEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FollowActivitiEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryIconUrl)))
	i += copy(dAtA[i:], m.EntryIconUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryIconMd5)))
	i += copy(dAtA[i:], m.EntryIconMd5)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryText)))
	i += copy(dAtA[i:], m.EntryText)
	if len(m.JumpRoomId) > 0 {
		for _, num := range m.JumpRoomId {
			dAtA[i] = 0x20
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(num))
		}
	}
	if len(m.JumpUrl) > 0 {
		for _, s := range m.JumpUrl {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.FinishTs))
	return i, nil
}

func (m *PersonalMainPageEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PersonalMainPageEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AdvId)))
	i += copy(dAtA[i:], m.AdvId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryIconUrl)))
	i += copy(dAtA[i:], m.EntryIconUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.EndTime))
	return i, nil
}

func (m *BaseActivitiEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BaseActivitiEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.EntryType))
	if m.FirstRechargeEntry != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.FirstRechargeEntry.Size()))
		n5, err := m.FirstRechargeEntry.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.NormalEntry != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.NormalEntry.Size()))
		n6, err := m.NormalEntry.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.FollowEntry != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.FollowEntry.Size()))
		n7, err := m.FollowEntry.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.MainPageEntry != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.MainPageEntry.Size()))
		n8, err := m.MainPageEntry.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *CheckUserFirstRechargeActEntryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFirstRechargeActEntryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *CheckUserFirstRechargeActEntryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserFirstRechargeActEntryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	if m.IsFirstRechargeFin {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.EntryInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.EntryInfo.Size()))
		n11, err := m.EntryInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *GetMutiLocationActEntryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMutiLocationActEntryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n12, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.LocationType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.ChannelPack)))
	i += copy(dAtA[i:], m.ChannelPack)
	return i, nil
}

func (m *GetMutiLocationActEntryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMutiLocationActEntryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n13, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.LocationType))
	if len(m.EntryList) > 0 {
		for _, msg := range m.EntryList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *NewYear2019BeatActConf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NewYear2019BeatActConf) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.ActBeginTs))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.ActFinishTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.IntervalPeriodTs))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.PayedNewyearCallsTs))
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.RandomGamestartDelaySecond))
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.RandomGamefinReportDelaySecond))
	dAtA[i] = 0x38
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.CatchAtleastBeatCnt))
	return i, nil
}

func (m *ChannelRoomMoleAttackGamePeriod) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelRoomMoleAttackGamePeriod) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.PeriodBeginTs))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.PeriodFinishTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.PeriodIntervalTs))
	return i, nil
}

func (m *ChannelRoomMoleAttackGameConf) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelRoomMoleAttackGameConf) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.GameBeginTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.GameFinishTs))
	if len(m.PeriodConfList) > 0 {
		for _, msg := range m.PeriodConfList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.RandomGamestartDelaySecond))
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.RandomGamefinReportDelaySecond))
	dAtA[i] = 0x38
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.GamePrepareSecond))
	dAtA[i] = 0x40
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.GameDurationSecond))
	dAtA[i] = 0x48
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.MoleAppearCnt))
	dAtA[i] = 0x50
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.AttackAtleastCnt))
	dAtA[i] = 0x58
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.NewYearTime))
	dAtA[i] = 0x60
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.NeweYearSecond))
	dAtA[i] = 0x68
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.MoleGameType))
	return i, nil
}

func (m *ReportNewYear2019BeatResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportNewYear2019BeatResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n14, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.BeatCnt))
	return i, nil
}

func (m *ReportNewYear2019BeatResultResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportNewYear2019BeatResultResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n15, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	return i, nil
}

func (m *ReportMoleBeatResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportMoleBeatResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n16, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.BeatCnt))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.MoleGameType))
	return i, nil
}

func (m *ReportMoleBeatResultRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportMoleBeatResultRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n17, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AwardName)))
	i += copy(dAtA[i:], m.AwardName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AwardUrl)))
	i += copy(dAtA[i:], m.AwardUrl)
	return i, nil
}

func (m *GetNewYear2019BeatLotteryResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNewYear2019BeatLotteryResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n18, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetNewYear2019BeatLotteryResultResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNewYear2019BeatLotteryResultResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n19, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	if m.AwardInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("award_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.AwardInfo.Size()))
		n20, err := m.AwardInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	return i, nil
}

func (m *NewYear2019BeatAward) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NewYear2019BeatAward) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AwardName)))
	i += copy(dAtA[i:], m.AwardName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AwardUrl)))
	i += copy(dAtA[i:], m.AwardUrl)
	if m.RecommendUser != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.RecommendUser.Size()))
		n21, err := m.RecommendUser.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	return i, nil
}

func (m *BeatAwardNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BeatAwardNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AwardName)))
	i += copy(dAtA[i:], m.AwardName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AwardUrl)))
	i += copy(dAtA[i:], m.AwardUrl)
	return i, nil
}

func (m *RankingTitle) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RankingTitle) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.RankingType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.RankingTitle)))
	i += copy(dAtA[i:], m.RankingTitle)
	return i, nil
}

func (m *Get2019YearActRankingListEntryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Get2019YearActRankingListEntryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n22, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	return i, nil
}

func (m *Get2019YearActRankingListEntryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Get2019YearActRankingListEntryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n23, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.RankingListUrl)))
	i += copy(dAtA[i:], m.RankingListUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.UserRankingUrl)))
	i += copy(dAtA[i:], m.UserRankingUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.CompeteRankingUrl)))
	i += copy(dAtA[i:], m.CompeteRankingUrl)
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.EndTs))
	if len(m.RankingTitleList) > 0 {
		for _, msg := range m.RankingTitleList {
			dAtA[i] = 0x3a
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.FreshenDelaySec))
	return i, nil
}

func (m *RecommendFollowedInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecommendFollowedInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.RecommendFollowedUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.RecommendFollowedTtid)))
	i += copy(dAtA[i:], m.RecommendFollowedTtid)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.RecommendFollowedNickname)))
	i += copy(dAtA[i:], m.RecommendFollowedNickname)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.RecommendFollowedPicUrl)))
	i += copy(dAtA[i:], m.RecommendFollowedPicUrl)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.RecommendFollowedPicMd5)))
	i += copy(dAtA[i:], m.RecommendFollowedPicMd5)
	dAtA[i] = 0x32
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.RecommendFollowedPicText)))
	i += copy(dAtA[i:], m.RecommendFollowedPicText)
	return i, nil
}

func (m *CommAdvInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommAdvInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AdPicUrl)))
	i += copy(dAtA[i:], m.AdPicUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AdPicMd5)))
	i += copy(dAtA[i:], m.AdPicMd5)
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.RoomChannelId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.ActiveUrl)))
	i += copy(dAtA[i:], m.ActiveUrl)
	dAtA[i] = 0x28
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.EndTs))
	dAtA[i] = 0x38
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.AdId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.Index))
	return i, nil
}

func (m *GetGangupTabAdvInfoRep) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGangupTabAdvInfoRep) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n24, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.ChannelPackTag)))
	i += copy(dAtA[i:], m.ChannelPackTag)
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.AdvSort))
	return i, nil
}

func (m *GetGangupTabAdvInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGangupTabAdvInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n25, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AdPicUrl)))
	i += copy(dAtA[i:], m.AdPicUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AdPicMd5)))
	i += copy(dAtA[i:], m.AdPicMd5)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.AdPicText)))
	i += copy(dAtA[i:], m.AdPicText)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.TextColor)))
	i += copy(dAtA[i:], m.TextColor)
	dAtA[i] = 0x30
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.TopLogoColor))
	dAtA[i] = 0x38
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.RoomChannelId))
	if m.RecommendFollowedInfo != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.RecommendFollowedInfo.Size()))
		n26, err := m.RecommendFollowedInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	dAtA[i] = 0x4a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.ActiveUrl)))
	i += copy(dAtA[i:], m.ActiveUrl)
	dAtA[i] = 0x50
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x58
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.EndTs))
	dAtA[i] = 0x60
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.AdId))
	if len(m.CommAdvInfo) > 0 {
		for _, msg := range m.CommAdvInfo {
			dAtA[i] = 0x6a
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x70
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.JumpType))
	dAtA[i] = 0x7a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.TTLink)))
	i += copy(dAtA[i:], m.TTLink)
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.TTLinkExtraParams)))
	i += copy(dAtA[i:], m.TTLinkExtraParams)
	return i, nil
}

func (m *RankingListStyle) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RankingListStyle) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.FontBrightColor)))
	i += copy(dAtA[i:], m.FontBrightColor)
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.FontDarkColor)))
	i += copy(dAtA[i:], m.FontDarkColor)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.ChannelButtonUrl)))
	i += copy(dAtA[i:], m.ChannelButtonUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.RefreshButtonUrl)))
	i += copy(dAtA[i:], m.RefreshButtonUrl)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryBgUrl)))
	i += copy(dAtA[i:], m.EntryBgUrl)
	dAtA[i] = 0x32
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.HalfScreenBgUrl)))
	i += copy(dAtA[i:], m.HalfScreenBgUrl)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryTitleUrl)))
	i += copy(dAtA[i:], m.EntryTitleUrl)
	dAtA[i] = 0x42
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.RankColor)))
	i += copy(dAtA[i:], m.RankColor)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.HalfFloatColor)))
	i += copy(dAtA[i:], m.HalfFloatColor)
	return i, nil
}

func (m *ActRankingListConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActRankingListConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.ActName)))
	i += copy(dAtA[i:], m.ActName)
	if len(m.DisCtypeList) > 0 {
		for _, num := range m.DisCtypeList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x20
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.EndTs))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryUrl)))
	i += copy(dAtA[i:], m.EntryUrl)
	dAtA[i] = 0x32
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.HalfScreenUrl)))
	i += copy(dAtA[i:], m.HalfScreenUrl)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.UserRankUrl)))
	i += copy(dAtA[i:], m.UserRankUrl)
	if m.RankStyle != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.RankStyle.Size()))
		n27, err := m.RankStyle.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	dAtA[i] = 0x48
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(m.FreshenDelaySec))
	return i, nil
}

func (m *AnchorRankWarConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AnchorRankWarConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryBgUrl)))
	i += copy(dAtA[i:], m.EntryBgUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryTitleColor)))
	i += copy(dAtA[i:], m.EntryTitleColor)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintActivity_(dAtA, i, uint64(len(m.EntryContentColor)))
	i += copy(dAtA[i:], m.EntryContentColor)
	return i, nil
}

func (m *GetCommonRankingListConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommonRankingListConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n28, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	return i, nil
}

func (m *GetCommonRankingListConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommonRankingListConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n29, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	if len(m.RankConfList) > 0 {
		for _, msg := range m.RankConfList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AnchorRankingListConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AnchorRankingListConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RankConf != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.RankConf.Size()))
		n30, err := m.RankConf.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	if m.RankWarConf != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.RankWarConf.Size()))
		n31, err := m.RankWarConf.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	return i, nil
}

func (m *GetAnchorRankingListConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAnchorRankingListConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseReq.Size()))
		n32, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n32
	}
	return i, nil
}

func (m *GetAnchorRankingListConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAnchorRankingListConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintActivity_(dAtA, i, uint64(m.BaseResp.Size()))
		n33, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n33
	}
	if len(m.RankConfList) > 0 {
		for _, msg := range m.RankConfList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintActivity_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Activity_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Activity_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintActivity_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetMyFirstVoucherReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	n += 1 + sovActivity_(uint64(m.Uid))
	return n
}

func (m *FirstVoucherInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity_(uint64(m.ProductId))
	n += 1 + sovActivity_(uint64(m.Worth))
	l = len(m.GameName)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.VoucherAccount)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.VoucherPassword)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.PurchaseTime))
	l = len(m.GameIcon)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *GetMyFirstVoucherResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	if len(m.FirstVoucherList) > 0 {
		for _, e := range m.FirstVoucherList {
			l = e.Size()
			n += 1 + l + sovActivity_(uint64(l))
		}
	}
	return n
}

func (m *AwardUserTTGiftPkgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	n += 1 + sovActivity_(uint64(m.TargetUid))
	return n
}

func (m *AwardUserTTGiftPkgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *FirstRechargeLevelConfig) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity_(uint64(m.LevelNum))
	l = len(m.ImgUrl)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *FirstRechargeActivitiEntry) Size() (n int) {
	var l int
	_ = l
	l = len(m.EntryIconUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.BackImgUrl)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.FinishTs))
	n += 1 + sovActivity_(uint64(m.PopUpFrequence))
	if len(m.LevelConfigList) > 0 {
		for _, e := range m.LevelConfigList {
			l = e.Size()
			n += 1 + l + sovActivity_(uint64(l))
		}
	}
	n += 1 + sovActivity_(uint64(m.ConfigType))
	l = len(m.EntryDesc)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.EntryImgUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.EntryPresentName)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *NormalActivitiEntry) Size() (n int) {
	var l int
	_ = l
	l = len(m.EntryIconUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.FinishTs))
	return n
}

func (m *FollowActivitiEntry) Size() (n int) {
	var l int
	_ = l
	l = len(m.EntryIconUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.EntryIconMd5)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.EntryText)
	n += 1 + l + sovActivity_(uint64(l))
	if len(m.JumpRoomId) > 0 {
		for _, e := range m.JumpRoomId {
			n += 1 + sovActivity_(uint64(e))
		}
	}
	if len(m.JumpUrl) > 0 {
		for _, s := range m.JumpUrl {
			l = len(s)
			n += 1 + l + sovActivity_(uint64(l))
		}
	}
	n += 1 + sovActivity_(uint64(m.FinishTs))
	return n
}

func (m *PersonalMainPageEntry) Size() (n int) {
	var l int
	_ = l
	l = len(m.AdvId)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.EntryIconUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.EndTime))
	return n
}

func (m *BaseActivitiEntry) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity_(uint64(m.EntryType))
	if m.FirstRechargeEntry != nil {
		l = m.FirstRechargeEntry.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	if m.NormalEntry != nil {
		l = m.NormalEntry.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	if m.FollowEntry != nil {
		l = m.FollowEntry.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	if m.MainPageEntry != nil {
		l = m.MainPageEntry.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *CheckUserFirstRechargeActEntryReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *CheckUserFirstRechargeActEntryResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	n += 2
	if m.EntryInfo != nil {
		l = m.EntryInfo.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *GetMutiLocationActEntryReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	n += 1 + sovActivity_(uint64(m.LocationType))
	l = len(m.ChannelPack)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *GetMutiLocationActEntryResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	n += 1 + sovActivity_(uint64(m.LocationType))
	if len(m.EntryList) > 0 {
		for _, e := range m.EntryList {
			l = e.Size()
			n += 1 + l + sovActivity_(uint64(l))
		}
	}
	return n
}

func (m *NewYear2019BeatActConf) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity_(uint64(m.ActBeginTs))
	n += 1 + sovActivity_(uint64(m.ActFinishTs))
	n += 1 + sovActivity_(uint64(m.IntervalPeriodTs))
	n += 1 + sovActivity_(uint64(m.PayedNewyearCallsTs))
	n += 1 + sovActivity_(uint64(m.RandomGamestartDelaySecond))
	n += 1 + sovActivity_(uint64(m.RandomGamefinReportDelaySecond))
	n += 1 + sovActivity_(uint64(m.CatchAtleastBeatCnt))
	return n
}

func (m *ChannelRoomMoleAttackGamePeriod) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity_(uint64(m.PeriodBeginTs))
	n += 1 + sovActivity_(uint64(m.PeriodFinishTs))
	n += 1 + sovActivity_(uint64(m.PeriodIntervalTs))
	return n
}

func (m *ChannelRoomMoleAttackGameConf) Size() (n int) {
	var l int
	_ = l
	l = len(m.GameName)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.GameBeginTs))
	n += 1 + sovActivity_(uint64(m.GameFinishTs))
	if len(m.PeriodConfList) > 0 {
		for _, e := range m.PeriodConfList {
			l = e.Size()
			n += 1 + l + sovActivity_(uint64(l))
		}
	}
	n += 1 + sovActivity_(uint64(m.RandomGamestartDelaySecond))
	n += 1 + sovActivity_(uint64(m.RandomGamefinReportDelaySecond))
	n += 1 + sovActivity_(uint64(m.GamePrepareSecond))
	n += 1 + sovActivity_(uint64(m.GameDurationSecond))
	n += 1 + sovActivity_(uint64(m.MoleAppearCnt))
	n += 1 + sovActivity_(uint64(m.AttackAtleastCnt))
	n += 1 + sovActivity_(uint64(m.NewYearTime))
	n += 1 + sovActivity_(uint64(m.NeweYearSecond))
	n += 1 + sovActivity_(uint64(m.MoleGameType))
	return n
}

func (m *ReportNewYear2019BeatResultReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	n += 1 + sovActivity_(uint64(m.ChannelId))
	n += 1 + sovActivity_(uint64(m.BeatCnt))
	return n
}

func (m *ReportNewYear2019BeatResultResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *ReportMoleBeatResultReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	n += 1 + sovActivity_(uint64(m.ChannelId))
	n += 1 + sovActivity_(uint64(m.BeatCnt))
	n += 1 + sovActivity_(uint64(m.MoleGameType))
	return n
}

func (m *ReportMoleBeatResultRsp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	l = len(m.AwardName)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.AwardUrl)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *GetNewYear2019BeatLotteryResultReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	n += 1 + sovActivity_(uint64(m.ChannelId))
	return n
}

func (m *GetNewYear2019BeatLotteryResultResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	if m.AwardInfo != nil {
		l = m.AwardInfo.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *NewYear2019BeatAward) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity_(uint64(m.ChannelId))
	n += 1 + sovActivity_(uint64(m.Uid))
	l = len(m.AwardName)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.AwardUrl)
	n += 1 + l + sovActivity_(uint64(l))
	if m.RecommendUser != nil {
		l = m.RecommendUser.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *BeatAwardNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity_(uint64(m.ChannelId))
	n += 1 + sovActivity_(uint64(m.Uid))
	l = len(m.AwardName)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.AwardUrl)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *RankingTitle) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity_(uint64(m.RankingType))
	l = len(m.RankingTitle)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *Get2019YearActRankingListEntryReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *Get2019YearActRankingListEntryResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	l = len(m.RankingListUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.UserRankingUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.CompeteRankingUrl)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.BeginTs))
	n += 1 + sovActivity_(uint64(m.EndTs))
	if len(m.RankingTitleList) > 0 {
		for _, e := range m.RankingTitleList {
			l = e.Size()
			n += 1 + l + sovActivity_(uint64(l))
		}
	}
	n += 1 + sovActivity_(uint64(m.FreshenDelaySec))
	return n
}

func (m *RecommendFollowedInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovActivity_(uint64(m.RecommendFollowedUid))
	l = len(m.RecommendFollowedTtid)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.RecommendFollowedNickname)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.RecommendFollowedPicUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.RecommendFollowedPicMd5)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.RecommendFollowedPicText)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *CommAdvInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.AdPicUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.AdPicMd5)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.RoomChannelId))
	l = len(m.ActiveUrl)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.BeginTs))
	n += 1 + sovActivity_(uint64(m.EndTs))
	n += 1 + sovActivity_(uint64(m.AdId))
	n += 1 + sovActivity_(uint64(m.Index))
	return n
}

func (m *GetGangupTabAdvInfoRep) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	l = len(m.ChannelPackTag)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.AdvSort))
	return n
}

func (m *GetGangupTabAdvInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	l = len(m.AdPicUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.AdPicMd5)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.AdPicText)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.TextColor)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.TopLogoColor))
	n += 1 + sovActivity_(uint64(m.RoomChannelId))
	if m.RecommendFollowedInfo != nil {
		l = m.RecommendFollowedInfo.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	l = len(m.ActiveUrl)
	n += 1 + l + sovActivity_(uint64(l))
	n += 1 + sovActivity_(uint64(m.BeginTs))
	n += 1 + sovActivity_(uint64(m.EndTs))
	n += 1 + sovActivity_(uint64(m.AdId))
	if len(m.CommAdvInfo) > 0 {
		for _, e := range m.CommAdvInfo {
			l = e.Size()
			n += 1 + l + sovActivity_(uint64(l))
		}
	}
	n += 1 + sovActivity_(uint64(m.JumpType))
	l = len(m.TTLink)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.TTLinkExtraParams)
	n += 2 + l + sovActivity_(uint64(l))
	return n
}

func (m *RankingListStyle) Size() (n int) {
	var l int
	_ = l
	l = len(m.FontBrightColor)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.FontDarkColor)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.ChannelButtonUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.RefreshButtonUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.EntryBgUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.HalfScreenBgUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.EntryTitleUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.RankColor)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.HalfFloatColor)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *ActRankingListConfig) Size() (n int) {
	var l int
	_ = l
	l = len(m.ActName)
	n += 1 + l + sovActivity_(uint64(l))
	if len(m.DisCtypeList) > 0 {
		for _, e := range m.DisCtypeList {
			n += 1 + sovActivity_(uint64(e))
		}
	}
	n += 1 + sovActivity_(uint64(m.BeginTs))
	n += 1 + sovActivity_(uint64(m.EndTs))
	l = len(m.EntryUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.HalfScreenUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.UserRankUrl)
	n += 1 + l + sovActivity_(uint64(l))
	if m.RankStyle != nil {
		l = m.RankStyle.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	n += 1 + sovActivity_(uint64(m.FreshenDelaySec))
	return n
}

func (m *AnchorRankWarConfig) Size() (n int) {
	var l int
	_ = l
	l = len(m.EntryBgUrl)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.EntryTitleColor)
	n += 1 + l + sovActivity_(uint64(l))
	l = len(m.EntryContentColor)
	n += 1 + l + sovActivity_(uint64(l))
	return n
}

func (m *GetCommonRankingListConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *GetCommonRankingListConfigResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	if len(m.RankConfList) > 0 {
		for _, e := range m.RankConfList {
			l = e.Size()
			n += 1 + l + sovActivity_(uint64(l))
		}
	}
	return n
}

func (m *AnchorRankingListConfig) Size() (n int) {
	var l int
	_ = l
	if m.RankConf != nil {
		l = m.RankConf.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	if m.RankWarConf != nil {
		l = m.RankWarConf.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *GetAnchorRankingListConfigReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	return n
}

func (m *GetAnchorRankingListConfigResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovActivity_(uint64(l))
	}
	if len(m.RankConfList) > 0 {
		for _, e := range m.RankConfList {
			l = e.Size()
			n += 1 + l + sovActivity_(uint64(l))
		}
	}
	return n
}

func sovActivity_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozActivity_(x uint64) (n int) {
	return sovActivity_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GetMyFirstVoucherReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMyFirstVoucherReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMyFirstVoucherReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstVoucherInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstVoucherInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstVoucherInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProductId", wireType)
			}
			m.ProductId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProductId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Worth", wireType)
			}
			m.Worth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Worth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoucherAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherPassword", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoucherPassword = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PurchaseTime", wireType)
			}
			m.PurchaseTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PurchaseTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIcon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameIcon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("product_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("worth")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("voucher_account")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("voucher_password")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("purchase_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_icon")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMyFirstVoucherResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMyFirstVoucherResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMyFirstVoucherResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FirstVoucherList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FirstVoucherList = append(m.FirstVoucherList, &FirstVoucherInfo{})
			if err := m.FirstVoucherList[len(m.FirstVoucherList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwardUserTTGiftPkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardUserTTGiftPkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardUserTTGiftPkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwardUserTTGiftPkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardUserTTGiftPkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardUserTTGiftPkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstRechargeLevelConfig) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstRechargeLevelConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstRechargeLevelConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelNum", wireType)
			}
			m.LevelNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LevelNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("level_num")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("img_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstRechargeActivitiEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstRechargeActivitiEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstRechargeActivitiEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryIconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryIconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BackImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BackImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinishTs", wireType)
			}
			m.FinishTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PopUpFrequence", wireType)
			}
			m.PopUpFrequence = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PopUpFrequence |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelConfigList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LevelConfigList = append(m.LevelConfigList, &FirstRechargeLevelConfig{})
			if err := m.LevelConfigList[len(m.LevelConfigList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConfigType", wireType)
			}
			m.ConfigType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfigType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryPresentName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryPresentName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("entry_icon_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("back_img_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("finish_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NormalActivitiEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NormalActivitiEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NormalActivitiEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryIconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryIconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinishTs", wireType)
			}
			m.FinishTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("entry_icon_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("jump_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("finish_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FollowActivitiEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FollowActivitiEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FollowActivitiEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryIconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryIconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryIconMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryIconMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.JumpRoomId = append(m.JumpRoomId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthActivity_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowActivity_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.JumpRoomId = append(m.JumpRoomId, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpRoomId", wireType)
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = append(m.JumpUrl, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinishTs", wireType)
			}
			m.FinishTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("entry_icon_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("entry_icon_md5")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("finish_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PersonalMainPageEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PersonalMainPageEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PersonalMainPageEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdvId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryIconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryIconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("adv_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("entry_icon_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("jump_url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BaseActivitiEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BaseActivitiEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BaseActivitiEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryType", wireType)
			}
			m.EntryType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EntryType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FirstRechargeEntry", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FirstRechargeEntry == nil {
				m.FirstRechargeEntry = &FirstRechargeActivitiEntry{}
			}
			if err := m.FirstRechargeEntry.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NormalEntry", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NormalEntry == nil {
				m.NormalEntry = &NormalActivitiEntry{}
			}
			if err := m.NormalEntry.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowEntry", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FollowEntry == nil {
				m.FollowEntry = &FollowActivitiEntry{}
			}
			if err := m.FollowEntry.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MainPageEntry", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MainPageEntry == nil {
				m.MainPageEntry = &PersonalMainPageEntry{}
			}
			if err := m.MainPageEntry.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("entry_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFirstRechargeActEntryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFirstRechargeActEntryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFirstRechargeActEntryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserFirstRechargeActEntryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserFirstRechargeActEntryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserFirstRechargeActEntryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsFirstRechargeFin", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsFirstRechargeFin = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.EntryInfo == nil {
				m.EntryInfo = &FirstRechargeActivitiEntry{}
			}
			if err := m.EntryInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_first_recharge_fin")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMutiLocationActEntryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMutiLocationActEntryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMutiLocationActEntryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LocationType", wireType)
			}
			m.LocationType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LocationType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelPack", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelPack = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("location_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMutiLocationActEntryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMutiLocationActEntryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMutiLocationActEntryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LocationType", wireType)
			}
			m.LocationType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LocationType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryList = append(m.EntryList, &BaseActivitiEntry{})
			if err := m.EntryList[len(m.EntryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("location_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NewYear2019BeatActConf) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NewYear2019BeatActConf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NewYear2019BeatActConf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActBeginTs", wireType)
			}
			m.ActBeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActBeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActFinishTs", wireType)
			}
			m.ActFinishTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActFinishTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IntervalPeriodTs", wireType)
			}
			m.IntervalPeriodTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IntervalPeriodTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PayedNewyearCallsTs", wireType)
			}
			m.PayedNewyearCallsTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PayedNewyearCallsTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RandomGamestartDelaySecond", wireType)
			}
			m.RandomGamestartDelaySecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RandomGamestartDelaySecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RandomGamefinReportDelaySecond", wireType)
			}
			m.RandomGamefinReportDelaySecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RandomGamefinReportDelaySecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CatchAtleastBeatCnt", wireType)
			}
			m.CatchAtleastBeatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CatchAtleastBeatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("act_begin_ts")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("act_finish_ts")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("interval_period_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelRoomMoleAttackGamePeriod) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelRoomMoleAttackGamePeriod: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelRoomMoleAttackGamePeriod: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeriodBeginTs", wireType)
			}
			m.PeriodBeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeriodBeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeriodFinishTs", wireType)
			}
			m.PeriodFinishTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeriodFinishTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeriodIntervalTs", wireType)
			}
			m.PeriodIntervalTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeriodIntervalTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("period_begin_ts")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("period_finish_ts")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("period_interval_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelRoomMoleAttackGameConf) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelRoomMoleAttackGameConf: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelRoomMoleAttackGameConf: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameBeginTs", wireType)
			}
			m.GameBeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameBeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameFinishTs", wireType)
			}
			m.GameFinishTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameFinishTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeriodConfList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PeriodConfList = append(m.PeriodConfList, &ChannelRoomMoleAttackGamePeriod{})
			if err := m.PeriodConfList[len(m.PeriodConfList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RandomGamestartDelaySecond", wireType)
			}
			m.RandomGamestartDelaySecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RandomGamestartDelaySecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RandomGamefinReportDelaySecond", wireType)
			}
			m.RandomGamefinReportDelaySecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RandomGamefinReportDelaySecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GamePrepareSecond", wireType)
			}
			m.GamePrepareSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GamePrepareSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameDurationSecond", wireType)
			}
			m.GameDurationSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameDurationSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MoleAppearCnt", wireType)
			}
			m.MoleAppearCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MoleAppearCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AttackAtleastCnt", wireType)
			}
			m.AttackAtleastCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AttackAtleastCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewYearTime", wireType)
			}
			m.NewYearTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewYearTime |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NeweYearSecond", wireType)
			}
			m.NeweYearSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NeweYearSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MoleGameType", wireType)
			}
			m.MoleGameType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MoleGameType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_begin_ts")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_finish_ts")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("mole_game_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportNewYear2019BeatResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportNewYear2019BeatResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportNewYear2019BeatResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeatCnt", wireType)
			}
			m.BeatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("beat_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportNewYear2019BeatResultResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportNewYear2019BeatResultResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportNewYear2019BeatResultResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportMoleBeatResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportMoleBeatResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportMoleBeatResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeatCnt", wireType)
			}
			m.BeatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MoleGameType", wireType)
			}
			m.MoleGameType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MoleGameType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("beat_cnt")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("mole_game_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportMoleBeatResultRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportMoleBeatResultRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportMoleBeatResultRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("award_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("award_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNewYear2019BeatLotteryResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNewYear2019BeatLotteryResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNewYear2019BeatLotteryResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNewYear2019BeatLotteryResultResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNewYear2019BeatLotteryResultResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNewYear2019BeatLotteryResultResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AwardInfo == nil {
				m.AwardInfo = &NewYear2019BeatAward{}
			}
			if err := m.AwardInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("award_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NewYear2019BeatAward) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NewYear2019BeatAward: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NewYear2019BeatAward: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendUser", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RecommendUser == nil {
				m.RecommendUser = &ga.GenericMember{}
			}
			if err := m.RecommendUser.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BeatAwardNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BeatAwardNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BeatAwardNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RankingTitle) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RankingTitle: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RankingTitle: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankingType", wireType)
			}
			m.RankingType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankingType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankingTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankingTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("ranking_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("ranking_title")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Get2019YearActRankingListEntryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Get2019YearActRankingListEntryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Get2019YearActRankingListEntryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Get2019YearActRankingListEntryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Get2019YearActRankingListEntryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Get2019YearActRankingListEntryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankingListUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankingListUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserRankingUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserRankingUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CompeteRankingUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CompeteRankingUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankingTitleList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankingTitleList = append(m.RankingTitleList, &RankingTitle{})
			if err := m.RankingTitleList[len(m.RankingTitleList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FreshenDelaySec", wireType)
			}
			m.FreshenDelaySec = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreshenDelaySec |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecommendFollowedInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecommendFollowedInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecommendFollowedInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendFollowedUid", wireType)
			}
			m.RecommendFollowedUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecommendFollowedUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendFollowedTtid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendFollowedTtid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendFollowedNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendFollowedNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendFollowedPicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendFollowedPicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendFollowedPicMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendFollowedPicMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendFollowedPicText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendFollowedPicText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommAdvInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CommAdvInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CommAdvInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdPicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdPicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdPicMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdPicMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomChannelId", wireType)
			}
			m.RoomChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActiveUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdId", wireType)
			}
			m.AdId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGangupTabAdvInfoRep) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGangupTabAdvInfoRep: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGangupTabAdvInfoRep: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelPackTag", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelPackTag = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvSort", wireType)
			}
			m.AdvSort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdvSort |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_pack_tag")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGangupTabAdvInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGangupTabAdvInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGangupTabAdvInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdPicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdPicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdPicMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdPicMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdPicText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdPicText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TextColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopLogoColor", wireType)
			}
			m.TopLogoColor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopLogoColor |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomChannelId", wireType)
			}
			m.RoomChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendFollowedInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RecommendFollowedInfo == nil {
				m.RecommendFollowedInfo = &RecommendFollowedInfo{}
			}
			if err := m.RecommendFollowedInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActiveUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdId", wireType)
			}
			m.AdId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommAdvInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommAdvInfo = append(m.CommAdvInfo, &CommAdvInfo{})
			if err := m.CommAdvInfo[len(m.CommAdvInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpType", wireType)
			}
			m.JumpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.JumpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TTLink", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TTLink = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TTLinkExtraParams", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TTLinkExtraParams = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RankingListStyle) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RankingListStyle: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RankingListStyle: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FontBrightColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FontBrightColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FontDarkColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FontDarkColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelButtonUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelButtonUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RefreshButtonUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RefreshButtonUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryBgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryBgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HalfScreenBgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HalfScreenBgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryTitleUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryTitleUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HalfFloatColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HalfFloatColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActRankingListConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActRankingListConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActRankingListConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.DisCtypeList = append(m.DisCtypeList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowActivity_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthActivity_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowActivity_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.DisCtypeList = append(m.DisCtypeList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisCtypeList", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HalfScreenUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HalfScreenUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserRankUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserRankUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankStyle", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RankStyle == nil {
				m.RankStyle = &RankingListStyle{}
			}
			if err := m.RankStyle.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FreshenDelaySec", wireType)
			}
			m.FreshenDelaySec = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreshenDelaySec |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AnchorRankWarConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AnchorRankWarConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AnchorRankWarConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryBgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryBgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryTitleColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryTitleColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntryContentColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntryContentColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommonRankingListConfigReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommonRankingListConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommonRankingListConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommonRankingListConfigResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommonRankingListConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommonRankingListConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankConfList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankConfList = append(m.RankConfList, &ActRankingListConfig{})
			if err := m.RankConfList[len(m.RankConfList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AnchorRankingListConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AnchorRankingListConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AnchorRankingListConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankConf", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RankConf == nil {
				m.RankConf = &ActRankingListConfig{}
			}
			if err := m.RankConf.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankWarConf", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RankWarConf == nil {
				m.RankWarConf = &AnchorRankWarConfig{}
			}
			if err := m.RankWarConf.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAnchorRankingListConfigReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAnchorRankingListConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAnchorRankingListConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAnchorRankingListConfigResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAnchorRankingListConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAnchorRankingListConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankConfList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthActivity_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RankConfList = append(m.RankConfList, &AnchorRankingListConfig{})
			if err := m.RankConfList[len(m.RankConfList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipActivity_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthActivity_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipActivity_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowActivity_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowActivity_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthActivity_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowActivity_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipActivity_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthActivity_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowActivity_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("activity_.proto", fileDescriptorActivity_) }

var fileDescriptorActivity_ = []byte{
	// 3285 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0xcd, 0x6f, 0x1b, 0xc9,
	0x95, 0x77, 0x93, 0xfa, 0x7c, 0x14, 0xc5, 0x56, 0x4b, 0x96, 0x68, 0x79, 0x2c, 0xcb, 0x6d, 0xcf,
	0xac, 0x6c, 0x78, 0x65, 0x8f, 0xd6, 0xde, 0xdd, 0x99, 0xdd, 0x1d, 0x80, 0xa2, 0x29, 0x9a, 0x3b,
	0xfc, 0x9a, 0x16, 0x65, 0xaf, 0x07, 0x0b, 0x34, 0x4a, 0xdd, 0x45, 0xaa, 0xa3, 0x66, 0x77, 0xbb,
	0xbb, 0x28, 0x59, 0x7f, 0x40, 0x02, 0x04, 0x49, 0x06, 0x01, 0x82, 0xe4, 0x96, 0xcb, 0x20, 0x39,
	0xe4, 0x94, 0xc3, 0x5c, 0xf2, 0x0f, 0x04, 0x99, 0x63, 0x0e, 0x09, 0x90, 0x53, 0x10, 0x38, 0xe7,
	0x5c, 0x72, 0x1f, 0x20, 0xa8, 0x8f, 0x26, 0x8b, 0x64, 0x53, 0x36, 0x27, 0x08, 0x32, 0x37, 0xf1,
	0x7d, 0x75, 0xbd, 0xf7, 0x7e, 0xef, 0xd5, 0xab, 0x2a, 0x41, 0x0e, 0x59, 0xc4, 0x39, 0x73, 0xc8,
	0x85, 0xb9, 0x1b, 0x84, 0x3e, 0xf1, 0xb5, 0x54, 0x07, 0x6d, 0x66, 0x3b, 0xc8, 0x3c, 0x46, 0x11,
	0xe6, 0x24, 0xfd, 0x19, 0xac, 0x95, 0x31, 0xa9, 0x5d, 0x1c, 0x38, 0x61, 0x44, 0x9e, 0xf9, 0x3d,
	0xeb, 0x04, 0x87, 0x06, 0x7e, 0xa9, 0xbd, 0x07, 0x0b, 0x54, 0xca, 0x0c, 0xf1, 0xcb, 0xbc, 0xb2,
	0x9d, 0xda, 0xc9, 0xec, 0x65, 0x76, 0x3b, 0x68, 0x77, 0x1f, 0x45, 0xd8, 0xc0, 0x2f, 0x8d, 0xf9,
	0x63, 0xfe, 0x87, 0xb6, 0x0e, 0xe9, 0x9e, 0x63, 0xe7, 0x53, 0xdb, 0xa9, 0x9d, 0xec, 0xfe, 0xcc,
	0x97, 0x7f, 0xbc, 0x79, 0xc5, 0xa0, 0x04, 0xfd, 0x67, 0x29, 0x50, 0x65, 0x9b, 0x15, 0xaf, 0xed,
	0x6b, 0xb7, 0x01, 0x82, 0xd0, 0xb7, 0x7b, 0x16, 0x31, 0x1d, 0x9b, 0x99, 0x8d, 0x75, 0x16, 0x05,
	0xbd, 0x62, 0x6b, 0x9b, 0x30, 0x7b, 0xee, 0x87, 0xe4, 0x64, 0xc8, 0x26, 0x27, 0x69, 0xb7, 0x60,
	0xb1, 0x83, 0xba, 0xd8, 0xf4, 0x50, 0x17, 0xe7, 0xd3, 0xdb, 0xa9, 0x9d, 0x45, 0xc1, 0x5f, 0xa0,
	0xe4, 0x3a, 0xea, 0x62, 0xed, 0x5f, 0x21, 0x77, 0xc6, 0x3f, 0x69, 0x22, 0xcb, 0xf2, 0x7b, 0x1e,
	0xc9, 0xcf, 0x48, 0x82, 0xcb, 0x82, 0x59, 0xe0, 0x3c, 0xed, 0x01, 0xa8, 0xb1, 0x78, 0x80, 0xa2,
	0xe8, 0xdc, 0x0f, 0xed, 0xfc, 0xac, 0x24, 0x1f, 0x1b, 0x6b, 0x0a, 0xa6, 0x76, 0x17, 0xb2, 0x41,
	0x2f, 0xb4, 0x4e, 0x68, 0x70, 0x88, 0xd3, 0xc5, 0xf9, 0x39, 0x69, 0x99, 0x4b, 0x31, 0xab, 0xe5,
	0x74, 0x71, 0x7f, 0xb5, 0x8e, 0xe5, 0x7b, 0xf9, 0xf9, 0xd1, 0xd5, 0x56, 0x2c, 0xdf, 0xd3, 0xbf,
	0xa3, 0xc0, 0xd5, 0x84, 0xf8, 0x47, 0x81, 0x76, 0x17, 0x16, 0x45, 0x02, 0xa2, 0x40, 0x64, 0x60,
	0x69, 0x90, 0x81, 0x28, 0x30, 0x16, 0x8e, 0xc5, 0x5f, 0xda, 0x3e, 0x68, 0x6d, 0xaa, 0x6e, 0xc6,
	0x9e, 0xb8, 0x4e, 0x44, 0xf2, 0xa9, 0xed, 0xf4, 0x4e, 0x66, 0x6f, 0x8d, 0xea, 0x8c, 0x26, 0xc2,
	0x50, 0xdb, 0x12, 0xa5, 0xea, 0x44, 0x44, 0xb7, 0xe1, 0x6a, 0xe1, 0x1c, 0x85, 0xf6, 0x51, 0x84,
	0xc3, 0x56, 0xab, 0xec, 0xb4, 0x49, 0xf3, 0xb4, 0x33, 0x0d, 0x10, 0x6e, 0x03, 0x10, 0x14, 0x76,
	0x30, 0x31, 0x47, 0xf1, 0xb0, 0xc8, 0xe9, 0x47, 0x8e, 0xad, 0x17, 0x61, 0x3d, 0xe9, 0x2b, 0x53,
	0xb9, 0xab, 0xff, 0x3f, 0xe4, 0x99, 0x43, 0x06, 0xb6, 0x4e, 0xa8, 0xe5, 0x2a, 0x3e, 0xc3, 0x6e,
	0xd1, 0xf7, 0xda, 0x4e, 0x87, 0x86, 0xdc, 0xa5, 0x3f, 0x4d, 0xaf, 0xd7, 0x1d, 0x02, 0xd8, 0x02,
	0x23, 0xd7, 0x7b, 0x5d, 0xed, 0x06, 0xcc, 0x3b, 0xdd, 0x8e, 0xd9, 0x0b, 0x5d, 0xb6, 0xca, 0x38,
	0x27, 0x73, 0x4e, 0xb7, 0x73, 0x14, 0xba, 0xfa, 0x8f, 0x66, 0x60, 0x73, 0xc8, 0x7c, 0x81, 0x17,
	0x91, 0x53, 0xf2, 0x48, 0x78, 0xa1, 0xdd, 0x83, 0x65, 0x4c, 0xff, 0x60, 0x49, 0x65, 0x46, 0x14,
	0xc9, 0xc8, 0x12, 0xe3, 0xd1, 0xcc, 0x1e, 0x85, 0xae, 0xf6, 0x1e, 0x2c, 0x1d, 0x23, 0xeb, 0xd4,
	0x4c, 0xfa, 0x1c, 0x50, 0x4e, 0x85, 0x7d, 0x92, 0x2e, 0xba, 0xed, 0x78, 0x4e, 0x74, 0x62, 0x92,
	0x88, 0xa1, 0xba, 0xbf, 0x68, 0x4e, 0x6e, 0x45, 0xda, 0x2e, 0xa8, 0x81, 0x1f, 0x98, 0xbd, 0xc0,
	0x6c, 0x87, 0xf8, 0x65, 0x0f, 0x7b, 0x16, 0xce, 0xcf, 0x6c, 0x2b, 0x7d, 0xc9, 0xe5, 0xc0, 0x0f,
	0x8e, 0x82, 0x83, 0x98, 0xa7, 0x3d, 0x85, 0x15, 0x1e, 0x07, 0x8b, 0xc5, 0x85, 0x23, 0x62, 0x96,
	0x21, 0xe2, 0x9d, 0x3e, 0x22, 0x12, 0x02, 0x68, 0xe4, 0xdc, 0xc1, 0x0f, 0x0a, 0x0c, 0xed, 0x5d,
	0xc8, 0x08, 0x1b, 0xe4, 0x22, 0xa0, 0x68, 0x1f, 0x7c, 0x14, 0x38, 0xa3, 0x75, 0x11, 0x60, 0x9a,
	0x7e, 0x1e, 0x17, 0x1b, 0x47, 0x56, 0x7e, 0x7e, 0x5b, 0xe9, 0x7b, 0xba, 0xc8, 0xe8, 0x4f, 0x70,
	0x64, 0x69, 0x3b, 0x90, 0x15, 0xc1, 0x13, 0x11, 0x59, 0x90, 0xe4, 0x32, 0x3c, 0x76, 0x3c, 0x24,
	0x7b, 0xa0, 0x71, 0xc9, 0x20, 0xc4, 0x11, 0xf6, 0x08, 0xaf, 0xf8, 0x45, 0x49, 0x5c, 0x65, 0xfc,
	0x26, 0x67, 0xd3, 0xca, 0xd7, 0x3f, 0x85, 0x7c, 0x69, 0xc8, 0xaf, 0xe2, 0x60, 0x79, 0x37, 0xe1,
	0x7a, 0xa9, 0x7e, 0x54, 0x33, 0x8d, 0x52, 0xf1, 0x69, 0xc1, 0x28, 0x97, 0xcc, 0x62, 0xa3, 0x7e,
	0x50, 0x29, 0x9b, 0xc5, 0x46, 0xad, 0xd6, 0xa8, 0xab, 0x57, 0xb4, 0x1b, 0x70, 0x2d, 0x51, 0xa0,
	0x5a, 0x39, 0x6c, 0xa9, 0x8a, 0xfe, 0x6d, 0x05, 0x56, 0xeb, 0x7e, 0xd8, 0x45, 0xee, 0xd7, 0x87,
	0xc3, 0x4d, 0x58, 0xf8, 0x56, 0xaf, 0x1b, 0x8c, 0x41, 0x61, 0x9e, 0x52, 0xdf, 0x0e, 0x07, 0xfa,
	0x5f, 0x15, 0x58, 0x3d, 0xf0, 0x5d, 0xd7, 0x3f, 0xff, 0xfa, 0xeb, 0x18, 0x96, 0xed, 0xda, 0x8f,
	0x87, 0x56, 0x33, 0x90, 0xad, 0xd9, 0x8f, 0x07, 0x69, 0x25, 0xf8, 0x15, 0xc9, 0xa7, 0xc7, 0xd2,
	0xda, 0xc2, 0xaf, 0x88, 0xb6, 0x0d, 0x4b, 0xcc, 0xb1, 0xd0, 0xf7, 0xbb, 0xb4, 0xb1, 0xcf, 0x6c,
	0xa7, 0x77, 0xb2, 0x06, 0x50, 0x9a, 0xe1, 0xfb, 0xdd, 0x8a, 0xad, 0x5d, 0x93, 0x5c, 0xa7, 0x28,
	0x5c, 0x9c, 0xe0, 0xf4, 0x5c, 0xa2, 0xd3, 0x9f, 0x2b, 0x70, 0xb5, 0x89, 0xc3, 0xc8, 0xf7, 0x90,
	0x5b, 0x43, 0x8e, 0xd7, 0x44, 0x1d, 0xcc, 0xdd, 0xbe, 0x0e, 0x73, 0xc8, 0x3e, 0x8b, 0x37, 0x93,
	0x78, 0x69, 0xb3, 0xc8, 0x3e, 0xab, 0xd8, 0x09, 0x31, 0x49, 0xbd, 0x55, 0x6e, 0xd2, 0x49, 0xb9,
	0xb9, 0x09, 0x0b, 0xd8, 0xb3, 0x79, 0xc7, 0x9f, 0x91, 0x56, 0x39, 0x8f, 0x3d, 0x9b, 0x36, 0x7b,
	0xfd, 0xd7, 0x29, 0x58, 0xa1, 0xcd, 0x6a, 0x38, 0x2f, 0x83, 0xf8, 0xd1, 0xe2, 0x19, 0xda, 0xf1,
	0x78, 0xfc, 0x28, 0x38, 0x9b, 0xb0, 0xc6, 0xfb, 0x77, 0x28, 0x80, 0x6b, 0x32, 0x5e, 0x3e, 0xb5,
	0xad, 0xec, 0x64, 0xf6, 0xb6, 0xc6, 0xea, 0x75, 0xe8, 0x13, 0x06, 0xef, 0xfd, 0x31, 0x8f, 0x7f,
	0xf6, 0x43, 0x58, 0xf2, 0x18, 0x5a, 0x85, 0xa5, 0x34, 0xb3, 0xb4, 0x41, 0x2d, 0x25, 0xa0, 0xd8,
	0xc8, 0x70, 0xe1, 0xbe, 0x6e, 0x9b, 0x21, 0x4c, 0xe8, 0xce, 0x0c, 0x74, 0x13, 0x90, 0x67, 0x64,
	0xb8, 0x30, 0xd7, 0x2d, 0x40, 0xae, 0x8b, 0x1c, 0xcf, 0x0c, 0x50, 0xdf, 0x89, 0x59, 0xa6, 0x7e,
	0x8d, 0xaa, 0x27, 0xe6, 0xd0, 0xc8, 0x76, 0xe5, 0x9f, 0xfa, 0xc7, 0x70, 0xab, 0x78, 0x82, 0xad,
	0x53, 0xba, 0x45, 0x8c, 0x7a, 0xcd, 0x15, 0xde, 0x7e, 0x53, 0xd2, 0x7f, 0xa3, 0x80, 0xfe, 0x26,
	0x6b, 0xd3, 0xed, 0xb5, 0xff, 0x01, 0x57, 0x9d, 0xc8, 0x1c, 0x49, 0x57, 0xdb, 0xf1, 0x18, 0xb6,
	0x16, 0x44, 0x6e, 0x35, 0x27, 0x1a, 0xfa, 0xd8, 0x81, 0xe3, 0x69, 0xff, 0x13, 0x23, 0xc1, 0xf1,
	0xda, 0xbe, 0x48, 0xc8, 0x9b, 0x52, 0xcb, 0x31, 0x42, 0x77, 0x6c, 0xfd, 0xab, 0x14, 0x6c, 0xd2,
	0x41, 0xa1, 0x47, 0x9c, 0xaa, 0x6f, 0x21, 0xe2, 0xf8, 0xde, 0xd7, 0x08, 0x08, 0x9d, 0x5e, 0x5c,
	0xa1, 0xce, 0x21, 0x29, 0x6f, 0xd4, 0x4b, 0x31, 0x8b, 0xa1, 0xf2, 0x5f, 0x60, 0xc9, 0x3a, 0x41,
	0x9e, 0x87, 0x5d, 0x33, 0x40, 0xd6, 0xe9, 0x50, 0xf1, 0x67, 0x04, 0xa7, 0x89, 0xac, 0x53, 0xfd,
	0xb5, 0x02, 0x9b, 0x25, 0xb1, 0xf0, 0x0b, 0xb6, 0xa2, 0xaa, 0x6c, 0xe7, 0x1d, 0xc8, 0xb3, 0xce,
	0x5a, 0xaa, 0xb7, 0x8c, 0x17, 0x66, 0xb5, 0x51, 0x2c, 0xb4, 0x2a, 0x8d, 0xba, 0x59, 0x6f, 0xd4,
	0x4b, 0xea, 0x95, 0x49, 0x5c, 0xa3, 0xd1, 0xa8, 0xa9, 0x8a, 0xb6, 0x0d, 0xef, 0x24, 0x71, 0x6b,
	0x85, 0x4a, 0xbd, 0x59, 0x28, 0x97, 0xd4, 0x94, 0x76, 0x1d, 0x36, 0x92, 0x24, 0x8e, 0xca, 0x45,
	0x35, 0xad, 0x6d, 0xc1, 0x66, 0x12, 0xf3, 0xa0, 0x51, 0xad, 0x36, 0x9e, 0xab, 0x33, 0xda, 0x5d,
	0x78, 0x37, 0x89, 0xdf, 0x2c, 0x19, 0x87, 0x8d, 0x7a, 0xa1, 0x3a, 0xf8, 0xce, 0xac, 0xfe, 0x0b,
	0x05, 0xae, 0x4f, 0x8c, 0xff, 0x74, 0x10, 0x9a, 0x22, 0x07, 0x8f, 0x62, 0xd0, 0xb0, 0xfd, 0x3b,
	0xcd, 0xf6, 0xef, 0xab, 0xb1, 0xd9, 0x24, 0xac, 0xb0, 0x59, 0xee, 0x97, 0x69, 0x58, 0xaf, 0xe3,
	0xf3, 0x17, 0x18, 0x85, 0x7b, 0x0f, 0xdf, 0xff, 0x60, 0x1f, 0x23, 0x52, 0xb0, 0x08, 0xdd, 0x0d,
	0xe9, 0x48, 0x82, 0x2c, 0x62, 0x1e, 0xe3, 0x8e, 0xe3, 0xd1, 0x86, 0x2b, 0x77, 0x24, 0x40, 0x16,
	0xd9, 0xa7, 0x8c, 0x56, 0x44, 0x77, 0x6a, 0x2a, 0x37, 0xe8, 0xcc, 0xf2, 0x1a, 0x33, 0xc8, 0x22,
	0x07, 0xf1, 0x64, 0xb2, 0x07, 0x9a, 0xe3, 0x11, 0x1c, 0x9e, 0x21, 0xd7, 0x0c, 0x70, 0xe8, 0xf8,
	0xf6, 0xe8, 0xee, 0xa5, 0xc6, 0xfc, 0x26, 0x63, 0xb7, 0x22, 0xed, 0x03, 0x58, 0x0f, 0xd0, 0x05,
	0xb6, 0x4d, 0x0f, 0x9f, 0x5f, 0x60, 0x14, 0x9a, 0x16, 0x72, 0xdd, 0x88, 0xea, 0xc9, 0x33, 0xcd,
	0x2a, 0x93, 0xa9, 0x73, 0x91, 0x22, 0x95, 0x68, 0x45, 0x5a, 0x19, 0x6e, 0x84, 0xc8, 0xb3, 0xfd,
	0xae, 0x49, 0x67, 0xe8, 0x88, 0xa0, 0x90, 0x98, 0x36, 0x76, 0xd1, 0x85, 0x19, 0x61, 0xcb, 0xf7,
	0x6c, 0xd6, 0x6f, 0x62, 0x0b, 0x9b, 0x5c, 0xb4, 0x1c, 0x4b, 0x3e, 0xa1, 0x82, 0x87, 0x4c, 0x4e,
	0x6b, 0x82, 0x2e, 0x19, 0x6a, 0x3b, 0x9e, 0x19, 0xe2, 0xc0, 0x1f, 0xb5, 0x26, 0x8f, 0x3b, 0x5b,
	0x03, 0x6b, 0x6d, 0xc7, 0x33, 0x98, 0xb4, 0x6c, 0xf1, 0x03, 0x58, 0xb7, 0x10, 0xb1, 0x4e, 0x4c,
	0x44, 0x5c, 0x8c, 0x22, 0x1a, 0x65, 0x44, 0x4c, 0xcb, 0x23, 0x6c, 0x1c, 0xea, 0x7b, 0xc5, 0x64,
	0x0a, 0x5c, 0x84, 0xa6, 0xa6, 0xe8, 0x11, 0xfd, 0x0b, 0x05, 0x6e, 0x16, 0x79, 0x49, 0xd1, 0x1d,
	0xb3, 0xe6, 0xbb, 0xb8, 0x40, 0x08, 0xb2, 0x4e, 0xe9, 0xc7, 0x78, 0xd8, 0xb4, 0xfb, 0x90, 0x13,
	0xf1, 0x4d, 0xcc, 0x5e, 0x96, 0x33, 0xe3, 0x04, 0xd2, 0x81, 0x91, 0x4b, 0x27, 0xe7, 0x70, 0x99,
	0x73, 0xe5, 0x34, 0x0a, 0xf9, 0x7e, 0x36, 0x47, 0xd3, 0xc8, 0xf9, 0x15, 0xc1, 0x6e, 0x45, 0xfa,
	0x1f, 0x66, 0xe1, 0xc6, 0xc4, 0x55, 0x33, 0xb8, 0x0d, 0x9d, 0xd7, 0x94, 0xc4, 0xf3, 0xda, 0x0e,
	0x64, 0x99, 0x48, 0xdf, 0xa9, 0x21, 0xa4, 0x51, 0x56, 0xec, 0xd2, 0x3d, 0x58, 0x66, 0x92, 0xc9,
	0x33, 0xd2, 0x12, 0xe5, 0xf5, 0xdd, 0xa9, 0xf5, 0xdd, 0xa7, 0x33, 0x2a, 0x2f, 0x9f, 0x19, 0x56,
	0x3e, 0xb7, 0x69, 0xf9, 0xbc, 0x21, 0xd6, 0x71, 0x74, 0xa8, 0x0f, 0x6c, 0x08, 0xfe, 0x06, 0xa3,
	0xee, 0x11, 0xac, 0xb2, 0xa8, 0x04, 0x21, 0x0e, 0x50, 0x88, 0x63, 0x13, 0x32, 0xe4, 0x56, 0xa8,
	0x40, 0x93, 0xf3, 0x85, 0xd6, 0xbf, 0xc3, 0x1a, 0xd3, 0xb2, 0x7b, 0x21, 0x6f, 0x44, 0x42, 0x6d,
	0x41, 0x52, 0xd3, 0xa8, 0xc4, 0x13, 0x21, 0x20, 0xf4, 0xee, 0x43, 0xae, 0xeb, 0xbb, 0xd8, 0x44,
	0x41, 0xc0, 0xea, 0xd6, 0x23, 0x6c, 0x28, 0xef, 0x83, 0x90, 0x32, 0x0b, 0x8c, 0x57, 0xf4, 0x08,
	0x05, 0x15, 0x62, 0xa1, 0xed, 0x97, 0x04, 0x55, 0x00, 0x49, 0x41, 0xe5, 0x7c, 0x51, 0x0e, 0x54,
	0x67, 0x07, 0xb2, 0x1e, 0x3e, 0x37, 0x59, 0x5b, 0x60, 0xd3, 0x56, 0x66, 0x5b, 0xd9, 0x49, 0xc7,
	0x78, 0xf0, 0x78, 0x5b, 0x63, 0xc7, 0xeb, 0x5d, 0x50, 0x3d, 0x7c, 0x8e, 0xb9, 0xa8, 0x58, 0xff,
	0x92, 0x7c, 0x26, 0xa2, 0x5c, 0x2a, 0x2d, 0xd6, 0x7e, 0x0f, 0x96, 0xd9, 0xda, 0x99, 0xe3, 0xac,
	0xf1, 0x66, 0x65, 0xfc, 0x50, 0x1e, 0x8d, 0x32, 0x6d, 0xbc, 0xfa, 0x67, 0x0a, 0x6c, 0xf1, 0x58,
	0x8f, 0x34, 0x52, 0x03, 0x47, 0x3d, 0x97, 0x4c, 0x79, 0x30, 0x8e, 0xf7, 0xd1, 0xd1, 0x83, 0xb1,
	0xa0, 0x57, 0x6c, 0x3a, 0x5e, 0xf6, 0xbb, 0x85, 0x8c, 0xea, 0xf9, 0x63, 0xd1, 0x21, 0xaa, 0x70,
	0xf3, 0xd2, 0xf5, 0x4c, 0x77, 0x84, 0xfe, 0x42, 0x81, 0x0d, 0x6e, 0x8e, 0xc2, 0xff, 0x9f, 0xe8,
	0x57, 0x42, 0x52, 0x66, 0x26, 0x26, 0xe5, 0xfb, 0x93, 0x56, 0x3d, 0xdd, 0xfe, 0x7b, 0x1b, 0x00,
	0x9d, 0xa3, 0xd0, 0xe6, 0x5d, 0x49, 0x3e, 0x13, 0x2c, 0x32, 0x3a, 0x6b, 0x4b, 0xb7, 0x80, 0xff,
	0x18, 0x3b, 0x11, 0x2c, 0x30, 0xf2, 0x51, 0xe8, 0xea, 0x2f, 0x41, 0x2f, 0xe3, 0xd1, 0x7c, 0x54,
	0x7d, 0x42, 0x30, 0x9b, 0x09, 0xfe, 0x01, 0xe1, 0xd4, 0xbf, 0xab, 0xc0, 0xed, 0x37, 0x7e, 0x73,
	0xda, 0x81, 0x56, 0x44, 0x83, 0xcd, 0xa5, 0x29, 0x26, 0x9b, 0x67, 0x07, 0x85, 0x91, 0x09, 0x82,
	0x0a, 0x89, 0x08, 0xb1, 0x89, 0xf4, 0xf7, 0x0a, 0xac, 0x25, 0xc9, 0x8c, 0x78, 0xa2, 0x24, 0x03,
	0x63, 0xc2, 0xbd, 0xe1, 0x48, 0x72, 0x86, 0x0e, 0x9c, 0x13, 0x92, 0x33, 0x23, 0xc9, 0xf4, 0x93,
	0xa3, 0xfd, 0x27, 0x2c, 0x87, 0xd8, 0xf2, 0xbb, 0x5d, 0x7a, 0x6a, 0xeb, 0x45, 0x38, 0x14, 0x07,
	0x91, 0x15, 0xea, 0x5a, 0x19, 0x7b, 0x38, 0x74, 0xac, 0x1a, 0xee, 0x1e, 0xe3, 0xd0, 0xc8, 0xf6,
	0x05, 0xe9, 0x29, 0x41, 0xff, 0x89, 0x02, 0xb9, 0xbe, 0x33, 0x75, 0x9f, 0x38, 0xed, 0x8b, 0x6f,
	0x84, 0x4b, 0xfa, 0x31, 0x2c, 0x19, 0xc8, 0x3b, 0x75, 0xbc, 0x4e, 0xcb, 0x21, 0x2e, 0x1b, 0xd0,
	0x43, 0xfe, 0x7b, 0xfc, 0x74, 0x99, 0x11, 0x1c, 0x36, 0x45, 0xde, 0x85, 0x6c, 0x5f, 0x90, 0x6a,
	0x0e, 0x9f, 0x83, 0x43, 0xc9, 0x26, 0x3d, 0x7d, 0x95, 0x31, 0xa1, 0xf9, 0xa4, 0x79, 0x2d, 0x58,
	0x44, 0x7c, 0x91, 0xee, 0x82, 0x53, 0x9f, 0xbe, 0x7e, 0x9c, 0x66, 0x15, 0x72, 0xa9, 0xb5, 0xe9,
	0xc0, 0xba, 0x0b, 0x6a, 0xec, 0x09, 0xdd, 0xd2, 0xc5, 0xa1, 0x7e, 0x10, 0xac, 0xe5, 0x70, 0xf0,
	0x01, 0x8a, 0x82, 0x5d, 0x50, 0x69, 0xee, 0xcd, 0x58, 0x89, 0x17, 0xb3, 0x24, 0x4f, 0xb9, 0x62,
	0x51, 0x54, 0xfe, 0x11, 0xac, 0x5a, 0x7e, 0x37, 0xc0, 0x04, 0x0f, 0xa9, 0xc8, 0xf9, 0x58, 0x11,
	0x02, 0x92, 0x16, 0x6b, 0x72, 0x62, 0x7a, 0x91, 0x07, 0x81, 0xf9, 0x63, 0x31, 0xb9, 0x5c, 0x87,
	0x39, 0x76, 0x79, 0x10, 0x0d, 0xed, 0xec, 0xb3, 0xd8, 0xa3, 0xc3, 0xf0, 0x47, 0xa0, 0x0d, 0x65,
	0x87, 0x0f, 0x2b, 0xf3, 0x6c, 0x58, 0x51, 0x69, 0x1c, 0xe4, 0xa4, 0x1b, 0xaa, 0x9c, 0x2e, 0x36,
	0x9b, 0x3c, 0x84, 0x95, 0x76, 0x88, 0xa3, 0x13, 0xec, 0x0d, 0x86, 0x88, 0xa1, 0x7d, 0x3c, 0x27,
	0xd8, 0xf1, 0xd4, 0xa0, 0x7f, 0x9e, 0x86, 0xab, 0x46, 0x8c, 0x79, 0x7e, 0xa6, 0xc7, 0xac, 0xa6,
	0xb5, 0x0f, 0x61, 0x7d, 0x50, 0x35, 0x6d, 0xc1, 0x61, 0x17, 0xba, 0x8a, 0x64, 0x70, 0x2d, 0x1c,
	0x55, 0x3e, 0x72, 0x6c, 0xed, 0xbf, 0x61, 0x23, 0x41, 0x97, 0x10, 0x56, 0x12, 0x83, 0xf8, 0x5d,
	0x1d, 0x53, 0x6e, 0x11, 0xc7, 0xd6, 0x9e, 0xc0, 0xf5, 0x04, 0x6d, 0xcf, 0xb1, 0x4e, 0xc7, 0xaa,
	0xe6, 0xda, 0x98, 0x85, 0xba, 0x10, 0xd3, 0x0a, 0xb0, 0x99, 0x60, 0x25, 0x70, 0xac, 0xb1, 0x34,
	0x6e, 0x8c, 0x19, 0x69, 0x3a, 0x16, 0x4d, 0xe6, 0x64, 0x13, 0x5d, 0xfb, 0x31, 0x4b, 0xef, 0xa5,
	0x26, 0x6a, 0xf6, 0x63, 0xad, 0x98, 0xe8, 0x0b, 0x35, 0xc1, 0x6e, 0xd1, 0xe6, 0x24, 0x1b, 0xf9,
	0x24, 0x1b, 0x2d, 0xfc, 0x8a, 0xe8, 0x3f, 0x4d, 0x41, 0xa6, 0xe8, 0x77, 0xbb, 0x05, 0xfb, 0x8c,
	0xa5, 0x46, 0x07, 0x40, 0x03, 0x57, 0x94, 0xa1, 0x0e, 0x11, 0xaf, 0x7d, 0x20, 0xc3, 0x6f, 0xf5,
	0x46, 0x65, 0xe8, 0xe2, 0xee, 0x43, 0x8e, 0xdd, 0xd3, 0x49, 0xfd, 0x2c, 0x2d, 0x4f, 0x70, 0x94,
	0x59, 0xec, 0xf7, 0x34, 0xda, 0xbb, 0xe8, 0x31, 0x13, 0x8f, 0x05, 0x70, 0x91, 0xd3, 0xff, 0x7e,
	0xfc, 0x5f, 0x83, 0x59, 0x64, 0xd3, 0x65, 0xc8, 0x23, 0xeb, 0x0c, 0xb2, 0xf9, 0x53, 0x90, 0xe3,
	0xd9, 0xf8, 0xd5, 0x10, 0x9c, 0x39, 0x49, 0xff, 0x8b, 0x02, 0xeb, 0x65, 0x4c, 0xca, 0xc8, 0xeb,
	0xf4, 0x82, 0x16, 0x3a, 0x16, 0x71, 0x32, 0x70, 0xf0, 0xd6, 0x5b, 0xee, 0x2e, 0xa8, 0xf2, 0x0d,
	0x87, 0x49, 0x50, 0x67, 0xa8, 0x35, 0x2e, 0x4b, 0xb7, 0x1c, 0x2d, 0xd4, 0xa1, 0x7e, 0x22, 0xfb,
	0xcc, 0x8c, 0xfc, 0x90, 0x0c, 0xc5, 0x6c, 0x1e, 0xd9, 0x67, 0x87, 0x7e, 0x48, 0xf4, 0x4f, 0x61,
	0xa1, 0xf0, 0xe4, 0x99, 0x79, 0xd8, 0x30, 0x5a, 0xda, 0x3a, 0x68, 0x25, 0x33, 0xfe, 0x65, 0x3e,
	0x29, 0x1d, 0x14, 0x8e, 0xaa, 0x2d, 0xf5, 0x8a, 0xb6, 0x01, 0xab, 0x12, 0xbd, 0x52, 0x33, 0x9b,
	0x85, 0x7a, 0xa9, 0xaa, 0x2a, 0xec, 0x06, 0x7a, 0x9c, 0x61, 0x3e, 0x7f, 0x5a, 0x2a, 0x55, 0xd5,
	0x94, 0xfe, 0xbb, 0x39, 0xd8, 0x48, 0xf4, 0x77, 0xba, 0x0e, 0x3a, 0x0c, 0xa3, 0xd4, 0x5b, 0xc0,
	0x28, 0x9d, 0x08, 0xa3, 0x3b, 0x90, 0x41, 0x12, 0xa6, 0x87, 0x91, 0x11, 0x83, 0x98, 0x3d, 0x0a,
	0xe1, 0x57, 0xc4, 0xb4, 0x7c, 0xd7, 0x0f, 0x87, 0x8a, 0x67, 0x91, 0xd2, 0x8b, 0x94, 0x4c, 0x47,
	0x40, 0xe2, 0x07, 0xa6, 0xeb, 0x77, 0x7c, 0x21, 0x28, 0xa3, 0x64, 0x89, 0xf8, 0x41, 0xd5, 0xef,
	0xf8, 0x5c, 0x36, 0x01, 0xbd, 0xf3, 0x93, 0xd1, 0xfb, 0x49, 0x62, 0x4b, 0x62, 0x83, 0xce, 0xc2,
	0xe0, 0x5a, 0x32, 0xb1, 0x15, 0x26, 0xf4, 0xa9, 0xf8, 0x09, 0x53, 0x2a, 0x88, 0xc5, 0x37, 0x17,
	0x04, 0x5c, 0x5e, 0x10, 0x99, 0x4b, 0x0a, 0x62, 0x69, 0xac, 0x20, 0xde, 0x87, 0x8c, 0x35, 0xe8,
	0x09, 0xf9, 0x2c, 0xdb, 0x24, 0x72, 0xec, 0x44, 0x3b, 0x20, 0x1b, 0xb2, 0x0c, 0x1d, 0x2c, 0xd8,
	0xcd, 0x36, 0x1b, 0x11, 0x96, 0x25, 0x8b, 0xec, 0xc2, 0x5b, 0xdc, 0xd0, 0xcd, 0xb5, 0x5a, 0x55,
	0xc7, 0x3b, 0xcd, 0xe7, 0x24, 0x7f, 0x04, 0x4d, 0xdb, 0x83, 0x15, 0xfe, 0x57, 0xe9, 0x15, 0x09,
	0x51, 0x13, 0x85, 0xa8, 0x1b, 0xe5, 0x55, 0x79, 0x47, 0x1c, 0x63, 0xeb, 0x27, 0xa0, 0x96, 0xcc,
	0x56, 0xa3, 0x69, 0x56, 0x1b, 0xe5, 0x86, 0x59, 0x6c, 0x54, 0x1b, 0x06, 0xbb, 0xe9, 0x1b, 0xa1,
	0x49, 0x65, 0xb1, 0x09, 0xeb, 0x63, 0xdc, 0xfd, 0x6a, 0xa1, 0xf8, 0xb1, 0xaa, 0x24, 0xf2, 0x9e,
	0x3f, 0xad, 0xb4, 0x4a, 0x6a, 0x4a, 0xff, 0x08, 0x32, 0x25, 0xf3, 0x7f, 0x8f, 0x6a, 0x4d, 0xb3,
	0xf5, 0xa2, 0x59, 0xe2, 0xd5, 0xd5, 0xff, 0x29, 0xd9, 0x67, 0xe5, 0x38, 0x60, 0xb4, 0x5a, 0xd5,
	0x4a, 0xfd, 0x63, 0x55, 0xd1, 0x7f, 0x95, 0x06, 0x55, 0x9a, 0x4a, 0x0e, 0xc9, 0x85, 0x8b, 0xd9,
	0x96, 0xea, 0x7b, 0xc4, 0x3c, 0x0e, 0x9d, 0xce, 0x49, 0x8c, 0x5e, 0xb9, 0xe5, 0xe6, 0x28, 0x7b,
	0x9f, 0x71, 0xfb, 0xb8, 0x64, 0x1a, 0x36, 0x0a, 0x4f, 0x85, 0xbc, 0x5c, 0x5b, 0x59, 0xca, 0x7c,
	0x82, 0xc2, 0x53, 0x2e, 0xbd, 0x07, 0x5a, 0x0c, 0xe0, 0xe3, 0x1e, 0x21, 0xe2, 0x75, 0x42, 0x2e,
	0xb4, 0xb8, 0x31, 0xed, 0x33, 0xb6, 0x78, 0x11, 0x0b, 0x31, 0xdb, 0xc9, 0x65, 0x1d, 0xb9, 0xee,
	0x54, 0xc1, 0x1f, 0xe8, 0xbc, 0x07, 0xfc, 0x95, 0xc3, 0x3c, 0xee, 0x88, 0xa7, 0x97, 0x81, 0x34,
	0xbf, 0x58, 0xdc, 0x67, 0x03, 0xcc, 0xfb, 0xa0, 0x9d, 0x20, 0xb7, 0x6d, 0x46, 0x56, 0x88, 0xb1,
	0x17, 0x4b, 0xcb, 0xfb, 0x54, 0x8e, 0xf2, 0x0f, 0x19, 0x9b, 0xab, 0xdc, 0x87, 0x9c, 0x78, 0xd8,
	0x60, 0x33, 0x0b, 0x95, 0x97, 0x1f, 0xfd, 0xf8, 0x3b, 0x1f, 0x1b, 0x52, 0xa8, 0xf4, 0x6d, 0x00,
	0x3a, 0xb7, 0x88, 0xc8, 0xc8, 0xaf, 0x7e, 0x8b, 0x94, 0xce, 0xa3, 0xb2, 0x0b, 0x2a, 0x5b, 0x45,
	0xdb, 0xf5, 0x51, 0x1c, 0x74, 0xb9, 0xc0, 0x96, 0x29, 0xf7, 0x80, 0x32, 0x99, 0xbc, 0xfe, 0x55,
	0x0a, 0xd6, 0x86, 0x67, 0x4a, 0xf1, 0x08, 0x4c, 0xfb, 0xb4, 0x45, 0xe2, 0x4b, 0xa7, 0x81, 0x81,
	0x79, 0x64, 0xb1, 0x97, 0x42, 0xed, 0x0e, 0x2c, 0xdb, 0x4e, 0x64, 0x5a, 0xb4, 0x26, 0x06, 0x8f,
	0xe5, 0x59, 0x63, 0xc9, 0x76, 0xa2, 0x22, 0x25, 0xb2, 0xc1, 0x4a, 0xae, 0xe2, 0xf4, 0xe5, 0x55,
	0x3c, 0x33, 0x5e, 0xc5, 0xb7, 0x80, 0xdf, 0xc8, 0x8e, 0x05, 0x7e, 0x81, 0x91, 0x45, 0x0c, 0xe5,
	0xb0, 0x8f, 0xc6, 0x3c, 0x3b, 0x88, 0x39, 0x95, 0xde, 0x81, 0x6c, 0x7f, 0x96, 0x1d, 0x8b, 0x77,
	0x26, 0x1e, 0x64, 0xa9, 0xe4, 0xbf, 0x89, 0x68, 0x47, 0x14, 0xcc, 0xa2, 0xd3, 0xad, 0x49, 0x93,
	0x64, 0x1f, 0xe8, 0x3c, 0xfa, 0x03, 0xcc, 0x8f, 0x8d, 0x91, 0x8b, 0x97, 0x8d, 0x91, 0x3f, 0x57,
	0x60, 0xb5, 0xe0, 0x59, 0x27, 0x3e, 0xfb, 0xf0, 0x73, 0x14, 0x8a, 0xf0, 0x8f, 0xa2, 0x4e, 0x99,
	0x80, 0xba, 0x87, 0xb0, 0x22, 0x43, 0x68, 0xbc, 0x6a, 0x72, 0x03, 0x10, 0x71, 0x84, 0x3c, 0x82,
	0x55, 0xae, 0x61, 0xf9, 0x1e, 0xc1, 0x5e, 0x0c, 0x12, 0xb9, 0x70, 0xb8, 0xc9, 0x22, 0xe7, 0x73,
	0x9c, 0x94, 0xe1, 0x46, 0x19, 0x13, 0xda, 0x20, 0x7d, 0x6f, 0x0c, 0x2c, 0xd3, 0x9c, 0x67, 0xbe,
	0xa7, 0xc0, 0xd6, 0x65, 0x96, 0xa6, 0xdb, 0x89, 0x3f, 0x82, 0x65, 0x51, 0x13, 0xf1, 0x05, 0x25,
	0xff, 0x8f, 0x0d, 0x76, 0xf8, 0x4e, 0xc2, 0x35, 0x3f, 0xaa, 0xc5, 0x77, 0x92, 0xfa, 0x0f, 0x14,
	0xd8, 0x18, 0x84, 0x7f, 0xb8, 0x02, 0x1e, 0xc3, 0x62, 0xdf, 0x36, 0x8b, 0xff, 0x65, 0x66, 0x17,
	0x62, 0xb3, 0xda, 0x7f, 0xf1, 0x83, 0xa2, 0x79, 0x8e, 0x42, 0xae, 0x9a, 0x1a, 0xbc, 0xfd, 0x25,
	0x64, 0x9a, 0x9f, 0x32, 0xc5, 0x4f, 0x11, 0xe6, 0x09, 0x2b, 0x9a, 0x26, 0xcc, 0x9f, 0xf1, 0x30,
	0x4f, 0xb4, 0x34, 0x5d, 0x98, 0x0b, 0x13, 0xc2, 0x7c, 0x7d, 0xd8, 0xa9, 0x4b, 0x22, 0x7d, 0xef,
	0xb5, 0x02, 0xeb, 0x25, 0xb6, 0xc1, 0xca, 0x8f, 0x5c, 0x6c, 0xeb, 0x8c, 0x9f, 0x9f, 0x0a, 0xc5,
	0x56, 0xe5, 0x59, 0xa5, 0xf5, 0x82, 0xbf, 0x24, 0x89, 0xb7, 0xad, 0x3b, 0xb0, 0x9d, 0xc0, 0x3c,
	0xa8, 0x18, 0x87, 0xad, 0xf8, 0xdf, 0x0c, 0xc4, 0xdc, 0x97, 0x60, 0xa2, 0xf4, 0x7c, 0xbf, 0x52,
	0x52, 0x53, 0x93, 0xd8, 0x0d, 0xa3, 0x56, 0xa8, 0xaa, 0xe9, 0x09, 0xec, 0xfe, 0x0b, 0xd7, 0x0e,
	0xdc, 0x49, 0x60, 0x27, 0x3c, 0x70, 0xdd, 0xfb, 0x3f, 0x58, 0xaa, 0x49, 0x97, 0x6d, 0x5a, 0x0e,
	0x32, 0x15, 0xef, 0x0c, 0xb9, 0x8e, 0x4d, 0x49, 0xea, 0x15, 0x6d, 0x05, 0xb2, 0x87, 0x38, 0x20,
	0xec, 0xce, 0x84, 0x91, 0x14, 0x2a, 0x23, 0x6e, 0x80, 0x18, 0x21, 0xa5, 0xad, 0x42, 0x4e, 0x22,
	0xec, 0x3d, 0xdc, 0xdb, 0x53, 0xd3, 0xfb, 0x9f, 0x7c, 0xf9, 0x7a, 0x4b, 0xf9, 0xed, 0xeb, 0x2d,
	0xe5, 0x4f, 0xaf, 0xb7, 0x94, 0x1f, 0xfe, 0x79, 0xeb, 0x0a, 0xe4, 0x2d, 0xbf, 0xbb, 0x7b, 0xe1,
	0x5c, 0xf8, 0x3d, 0x9a, 0x84, 0xae, 0x6f, 0x63, 0x97, 0xff, 0x3b, 0xda, 0xa7, 0xef, 0x76, 0x7c,
	0x17, 0x79, 0x9d, 0xdd, 0xc7, 0x7b, 0x84, 0xec, 0x5a, 0x7e, 0xf7, 0x01, 0x23, 0x5b, 0xbe, 0xfb,
	0x00, 0x05, 0xc1, 0x83, 0xf8, 0xff, 0xd9, 0xfe, 0x16, 0x00, 0x00, 0xff, 0xff, 0x97, 0xc7, 0x91,
	0xa5, 0xda, 0x26, 0x00, 0x00,
}
