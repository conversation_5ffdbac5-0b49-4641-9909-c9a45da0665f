// Code generated by protoc-gen-go. DO NOT EDIT.
// source: concert-logic_.proto

package concert_logic // import "golang.52tt.com/protocol/app/concert-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 获取歌单
type ConcertSongType int32

const (
	ConcertSongType_ConcertSongType_UNDEFINED ConcertSongType = 0
	ConcertSongType_ConcertSongType_HOT       ConcertSongType = 1
	ConcertSongType_ConcertSongType_RCMD      ConcertSongType = 2
)

var ConcertSongType_name = map[int32]string{
	0: "ConcertSongType_UNDEFINED",
	1: "ConcertSongType_HOT",
	2: "ConcertSongType_RCMD",
}
var ConcertSongType_value = map[string]int32{
	"ConcertSongType_UNDEFINED": 0,
	"ConcertSongType_HOT":       1,
	"ConcertSongType_RCMD":      2,
}

func (x ConcertSongType) String() string {
	return proto.EnumName(ConcertSongType_name, int32(x))
}
func (ConcertSongType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{0}
}

// 舞台类型
type Stagetype int32

const (
	Stagetype_Stagetype_UNDEFINED Stagetype = 0
	Stagetype_Stagetype_mild      Stagetype = 1
	Stagetype_Stagetype_Excited   Stagetype = 2
)

var Stagetype_name = map[int32]string{
	0: "Stagetype_UNDEFINED",
	1: "Stagetype_mild",
	2: "Stagetype_Excited",
}
var Stagetype_value = map[string]int32{
	"Stagetype_UNDEFINED": 0,
	"Stagetype_mild":      1,
	"Stagetype_Excited":   2,
}

func (x Stagetype) String() string {
	return proto.EnumName(Stagetype_name, int32(x))
}
func (Stagetype) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{1}
}

type ConcertGameStage int32

const (
	ConcertGameStage_ConcertGameStage_UNDEFINED   ConcertGameStage = 0
	ConcertGameStage_ConcertGameStage_DOWNLOADING ConcertGameStage = 1
	ConcertGameStage_ConcertGameStage_Singing     ConcertGameStage = 2
)

var ConcertGameStage_name = map[int32]string{
	0: "ConcertGameStage_UNDEFINED",
	1: "ConcertGameStage_DOWNLOADING",
	2: "ConcertGameStage_Singing",
}
var ConcertGameStage_value = map[string]int32{
	"ConcertGameStage_UNDEFINED":   0,
	"ConcertGameStage_DOWNLOADING": 1,
	"ConcertGameStage_Singing":     2,
}

func (x ConcertGameStage) String() string {
	return proto.EnumName(ConcertGameStage_name, int32(x))
}
func (ConcertGameStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{2}
}

type KeyMapType int32

const (
	KeyMapType_KeyMapType_UNDEFINED KeyMapType = 0
	KeyMapType_KeyMapType_CLICK     KeyMapType = 1
	KeyMapType_KeyMapType_PRESS     KeyMapType = 2
	KeyMapType_KeyMapType_SLIDE     KeyMapType = 3
)

var KeyMapType_name = map[int32]string{
	0: "KeyMapType_UNDEFINED",
	1: "KeyMapType_CLICK",
	2: "KeyMapType_PRESS",
	3: "KeyMapType_SLIDE",
}
var KeyMapType_value = map[string]int32{
	"KeyMapType_UNDEFINED": 0,
	"KeyMapType_CLICK":     1,
	"KeyMapType_PRESS":     2,
	"KeyMapType_SLIDE":     3,
}

func (x KeyMapType) String() string {
	return proto.EnumName(KeyMapType_name, int32(x))
}
func (KeyMapType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{3}
}

type BandRole int32

const (
	BandRole_BandRole_UNDEFINED     BandRole = 0
	BandRole_BandRole_MAIN_SINGER   BandRole = 1
	BandRole_BandRole_LEAD_GUITAR   BandRole = 2
	BandRole_BandRole_KEYBOARD      BandRole = 3
	BandRole_BandRole_BASSIST       BandRole = 4
	BandRole_BandRole_DRUMMER       BandRole = 5
	BandRole_BandRole_RHYTHM_GUITAR BandRole = 6
)

var BandRole_name = map[int32]string{
	0: "BandRole_UNDEFINED",
	1: "BandRole_MAIN_SINGER",
	2: "BandRole_LEAD_GUITAR",
	3: "BandRole_KEYBOARD",
	4: "BandRole_BASSIST",
	5: "BandRole_DRUMMER",
	6: "BandRole_RHYTHM_GUITAR",
}
var BandRole_value = map[string]int32{
	"BandRole_UNDEFINED":     0,
	"BandRole_MAIN_SINGER":   1,
	"BandRole_LEAD_GUITAR":   2,
	"BandRole_KEYBOARD":      3,
	"BandRole_BASSIST":       4,
	"BandRole_DRUMMER":       5,
	"BandRole_RHYTHM_GUITAR": 6,
}

func (x BandRole) String() string {
	return proto.EnumName(BandRole_name, int32(x))
}
func (BandRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{4}
}

type ReportConcertGradeType int32

const (
	ReportConcertGradeType_MISS    ReportConcertGradeType = 0
	ReportConcertGradeType_PERFECT ReportConcertGradeType = 1
	ReportConcertGradeType_GREAT   ReportConcertGradeType = 2
)

var ReportConcertGradeType_name = map[int32]string{
	0: "MISS",
	1: "PERFECT",
	2: "GREAT",
}
var ReportConcertGradeType_value = map[string]int32{
	"MISS":    0,
	"PERFECT": 1,
	"GREAT":   2,
}

func (x ReportConcertGradeType) String() string {
	return proto.EnumName(ReportConcertGradeType_name, int32(x))
}
func (ReportConcertGradeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{5}
}

// 获取歌曲选项
type GetConcertSongOptsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConcertSongOptsReq) Reset()         { *m = GetConcertSongOptsReq{} }
func (m *GetConcertSongOptsReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongOptsReq) ProtoMessage()    {}
func (*GetConcertSongOptsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{0}
}
func (m *GetConcertSongOptsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongOptsReq.Unmarshal(m, b)
}
func (m *GetConcertSongOptsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongOptsReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongOptsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongOptsReq.Merge(dst, src)
}
func (m *GetConcertSongOptsReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongOptsReq.Size(m)
}
func (m *GetConcertSongOptsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongOptsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongOptsReq proto.InternalMessageInfo

func (m *GetConcertSongOptsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetConcertSongOptsResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Opts                 []*ConcertSongOpt `protobuf:"bytes,2,rep,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetConcertSongOptsResp) Reset()         { *m = GetConcertSongOptsResp{} }
func (m *GetConcertSongOptsResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongOptsResp) ProtoMessage()    {}
func (*GetConcertSongOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{1}
}
func (m *GetConcertSongOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongOptsResp.Unmarshal(m, b)
}
func (m *GetConcertSongOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongOptsResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongOptsResp.Merge(dst, src)
}
func (m *GetConcertSongOptsResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongOptsResp.Size(m)
}
func (m *GetConcertSongOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongOptsResp proto.InternalMessageInfo

func (m *GetConcertSongOptsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetConcertSongOptsResp) GetOpts() []*ConcertSongOpt {
	if m != nil {
		return m.Opts
	}
	return nil
}

type ConcertSongOpt struct {
	Id                   string                `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Elems                []*ConcertSongOptElem `protobuf:"bytes,3,rep,name=elems,proto3" json:"elems,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ConcertSongOpt) Reset()         { *m = ConcertSongOpt{} }
func (m *ConcertSongOpt) String() string { return proto.CompactTextString(m) }
func (*ConcertSongOpt) ProtoMessage()    {}
func (*ConcertSongOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{2}
}
func (m *ConcertSongOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSongOpt.Unmarshal(m, b)
}
func (m *ConcertSongOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSongOpt.Marshal(b, m, deterministic)
}
func (dst *ConcertSongOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSongOpt.Merge(dst, src)
}
func (m *ConcertSongOpt) XXX_Size() int {
	return xxx_messageInfo_ConcertSongOpt.Size(m)
}
func (m *ConcertSongOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSongOpt.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSongOpt proto.InternalMessageInfo

func (m *ConcertSongOpt) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ConcertSongOpt) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ConcertSongOpt) GetElems() []*ConcertSongOptElem {
	if m != nil {
		return m.Elems
	}
	return nil
}

type ConcertSongOptElem struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertSongOptElem) Reset()         { *m = ConcertSongOptElem{} }
func (m *ConcertSongOptElem) String() string { return proto.CompactTextString(m) }
func (*ConcertSongOptElem) ProtoMessage()    {}
func (*ConcertSongOptElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{3}
}
func (m *ConcertSongOptElem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSongOptElem.Unmarshal(m, b)
}
func (m *ConcertSongOptElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSongOptElem.Marshal(b, m, deterministic)
}
func (dst *ConcertSongOptElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSongOptElem.Merge(dst, src)
}
func (m *ConcertSongOptElem) XXX_Size() int {
	return xxx_messageInfo_ConcertSongOptElem.Size(m)
}
func (m *ConcertSongOptElem) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSongOptElem.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSongOptElem proto.InternalMessageInfo

func (m *ConcertSongOptElem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ConcertSongOptElem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type SearchConcertSongReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	KeyWord              string       `protobuf:"bytes,2,opt,name=key_word,json=keyWord,proto3" json:"key_word,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SearchConcertSongReq) Reset()         { *m = SearchConcertSongReq{} }
func (m *SearchConcertSongReq) String() string { return proto.CompactTextString(m) }
func (*SearchConcertSongReq) ProtoMessage()    {}
func (*SearchConcertSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{4}
}
func (m *SearchConcertSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchConcertSongReq.Unmarshal(m, b)
}
func (m *SearchConcertSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchConcertSongReq.Marshal(b, m, deterministic)
}
func (dst *SearchConcertSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchConcertSongReq.Merge(dst, src)
}
func (m *SearchConcertSongReq) XXX_Size() int {
	return xxx_messageInfo_SearchConcertSongReq.Size(m)
}
func (m *SearchConcertSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchConcertSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchConcertSongReq proto.InternalMessageInfo

func (m *SearchConcertSongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SearchConcertSongReq) GetKeyWord() string {
	if m != nil {
		return m.KeyWord
	}
	return ""
}

type SearchConcertSongResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Songs                []*ConcertSong `protobuf:"bytes,2,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SearchConcertSongResp) Reset()         { *m = SearchConcertSongResp{} }
func (m *SearchConcertSongResp) String() string { return proto.CompactTextString(m) }
func (*SearchConcertSongResp) ProtoMessage()    {}
func (*SearchConcertSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{5}
}
func (m *SearchConcertSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchConcertSongResp.Unmarshal(m, b)
}
func (m *SearchConcertSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchConcertSongResp.Marshal(b, m, deterministic)
}
func (dst *SearchConcertSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchConcertSongResp.Merge(dst, src)
}
func (m *SearchConcertSongResp) XXX_Size() int {
	return xxx_messageInfo_SearchConcertSongResp.Size(m)
}
func (m *SearchConcertSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchConcertSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchConcertSongResp proto.InternalMessageInfo

func (m *SearchConcertSongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SearchConcertSongResp) GetSongs() []*ConcertSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

type GetConcertSongListReq struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Opts                 []*ConcertSongOpt `protobuf:"bytes,2,rep,name=opts,proto3" json:"opts,omitempty"`
	SongType             ConcertSongType   `protobuf:"varint,3,opt,name=song_type,json=songType,proto3,enum=ga.concert_logic.ConcertSongType" json:"song_type,omitempty"`
	LoadMore             *ConcertLoadMore  `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32            `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetConcertSongListReq) Reset()         { *m = GetConcertSongListReq{} }
func (m *GetConcertSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongListReq) ProtoMessage()    {}
func (*GetConcertSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{6}
}
func (m *GetConcertSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongListReq.Unmarshal(m, b)
}
func (m *GetConcertSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongListReq.Merge(dst, src)
}
func (m *GetConcertSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongListReq.Size(m)
}
func (m *GetConcertSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongListReq proto.InternalMessageInfo

func (m *GetConcertSongListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetConcertSongListReq) GetOpts() []*ConcertSongOpt {
	if m != nil {
		return m.Opts
	}
	return nil
}

func (m *GetConcertSongListReq) GetSongType() ConcertSongType {
	if m != nil {
		return m.SongType
	}
	return ConcertSongType_ConcertSongType_UNDEFINED
}

func (m *GetConcertSongListReq) GetLoadMore() *ConcertLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetConcertSongListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetConcertSongListResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Songs                []*ConcertSong   `protobuf:"bytes,2,rep,name=songs,proto3" json:"songs,omitempty"`
	LoadMore             *ConcertLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetConcertSongListResp) Reset()         { *m = GetConcertSongListResp{} }
func (m *GetConcertSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongListResp) ProtoMessage()    {}
func (*GetConcertSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{7}
}
func (m *GetConcertSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongListResp.Unmarshal(m, b)
}
func (m *GetConcertSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongListResp.Merge(dst, src)
}
func (m *GetConcertSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongListResp.Size(m)
}
func (m *GetConcertSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongListResp proto.InternalMessageInfo

func (m *GetConcertSongListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetConcertSongListResp) GetSongs() []*ConcertSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *GetConcertSongListResp) GetLoadMore() *ConcertLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type ConcertSong struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Bg                   string   `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	Author               string   `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
	HotVal               uint32   `protobuf:"varint,5,opt,name=hot_val,json=hotVal,proto3" json:"hot_val,omitempty"`
	Singer               string   `protobuf:"bytes,6,opt,name=singer,proto3" json:"singer,omitempty"`
	RoleNames            []string `protobuf:"bytes,7,rep,name=role_names,json=roleNames,proto3" json:"role_names,omitempty"`
	IsNew                bool     `protobuf:"varint,8,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertSong) Reset()         { *m = ConcertSong{} }
func (m *ConcertSong) String() string { return proto.CompactTextString(m) }
func (*ConcertSong) ProtoMessage()    {}
func (*ConcertSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{8}
}
func (m *ConcertSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSong.Unmarshal(m, b)
}
func (m *ConcertSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSong.Marshal(b, m, deterministic)
}
func (dst *ConcertSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSong.Merge(dst, src)
}
func (m *ConcertSong) XXX_Size() int {
	return xxx_messageInfo_ConcertSong.Size(m)
}
func (m *ConcertSong) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSong.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSong proto.InternalMessageInfo

func (m *ConcertSong) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ConcertSong) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ConcertSong) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ConcertSong) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *ConcertSong) GetHotVal() uint32 {
	if m != nil {
		return m.HotVal
	}
	return 0
}

func (m *ConcertSong) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *ConcertSong) GetRoleNames() []string {
	if m != nil {
		return m.RoleNames
	}
	return nil
}

func (m *ConcertSong) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

type ConcertLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertLoadMore) Reset()         { *m = ConcertLoadMore{} }
func (m *ConcertLoadMore) String() string { return proto.CompactTextString(m) }
func (*ConcertLoadMore) ProtoMessage()    {}
func (*ConcertLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{9}
}
func (m *ConcertLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertLoadMore.Unmarshal(m, b)
}
func (m *ConcertLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertLoadMore.Marshal(b, m, deterministic)
}
func (dst *ConcertLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertLoadMore.Merge(dst, src)
}
func (m *ConcertLoadMore) XXX_Size() int {
	return xxx_messageInfo_ConcertLoadMore.Size(m)
}
func (m *ConcertLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertLoadMore proto.InternalMessageInfo

func (m *ConcertLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *ConcertLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

// 获取演唱资源
type GetAllConcertResourceReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Version              uint32       `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllConcertResourceReq) Reset()         { *m = GetAllConcertResourceReq{} }
func (m *GetAllConcertResourceReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertResourceReq) ProtoMessage()    {}
func (*GetAllConcertResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{10}
}
func (m *GetAllConcertResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertResourceReq.Unmarshal(m, b)
}
func (m *GetAllConcertResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertResourceReq.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertResourceReq.Merge(dst, src)
}
func (m *GetAllConcertResourceReq) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertResourceReq.Size(m)
}
func (m *GetAllConcertResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertResourceReq proto.InternalMessageInfo

func (m *GetAllConcertResourceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAllConcertResourceReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetAllConcertResourceResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Version              uint32                    `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	ResList              []*InstrumentRes          `protobuf:"bytes,3,rep,name=res_list,json=resList,proto3" json:"res_list,omitempty"`
	Decorations          []*ConcertStageDecoration `protobuf:"bytes,4,rep,name=decorations,proto3" json:"decorations,omitempty"`
	DefaultUrl           string                    `protobuf:"bytes,5,opt,name=default_url,json=defaultUrl,proto3" json:"default_url,omitempty"`
	DefaultMd5           string                    `protobuf:"bytes,6,opt,name=default_md5,json=defaultMd5,proto3" json:"default_md5,omitempty"`
	DefaultTransitionImg string                    `protobuf:"bytes,7,opt,name=default_transition_img,json=defaultTransitionImg,proto3" json:"default_transition_img,omitempty"`
	SonicWave            *ConcertRes               `protobuf:"bytes,8,opt,name=sonic_wave,json=sonicWave,proto3" json:"sonic_wave,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetAllConcertResourceResp) Reset()         { *m = GetAllConcertResourceResp{} }
func (m *GetAllConcertResourceResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertResourceResp) ProtoMessage()    {}
func (*GetAllConcertResourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{11}
}
func (m *GetAllConcertResourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertResourceResp.Unmarshal(m, b)
}
func (m *GetAllConcertResourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertResourceResp.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertResourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertResourceResp.Merge(dst, src)
}
func (m *GetAllConcertResourceResp) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertResourceResp.Size(m)
}
func (m *GetAllConcertResourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertResourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertResourceResp proto.InternalMessageInfo

func (m *GetAllConcertResourceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllConcertResourceResp) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *GetAllConcertResourceResp) GetResList() []*InstrumentRes {
	if m != nil {
		return m.ResList
	}
	return nil
}

func (m *GetAllConcertResourceResp) GetDecorations() []*ConcertStageDecoration {
	if m != nil {
		return m.Decorations
	}
	return nil
}

func (m *GetAllConcertResourceResp) GetDefaultUrl() string {
	if m != nil {
		return m.DefaultUrl
	}
	return ""
}

func (m *GetAllConcertResourceResp) GetDefaultMd5() string {
	if m != nil {
		return m.DefaultMd5
	}
	return ""
}

func (m *GetAllConcertResourceResp) GetDefaultTransitionImg() string {
	if m != nil {
		return m.DefaultTransitionImg
	}
	return ""
}

func (m *GetAllConcertResourceResp) GetSonicWave() *ConcertRes {
	if m != nil {
		return m.SonicWave
	}
	return nil
}

// 字段名是按设计的切图名称来的
type ConcertStageDecoration struct {
	Stagetype            Stagetype   `protobuf:"varint,1,opt,name=stagetype,proto3,enum=ga.concert_logic.Stagetype" json:"stagetype,omitempty"`
	LightOnBg            *ConcertRes `protobuf:"bytes,2,opt,name=light_on_bg,json=lightOnBg,proto3" json:"light_on_bg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ConcertStageDecoration) Reset()         { *m = ConcertStageDecoration{} }
func (m *ConcertStageDecoration) String() string { return proto.CompactTextString(m) }
func (*ConcertStageDecoration) ProtoMessage()    {}
func (*ConcertStageDecoration) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{12}
}
func (m *ConcertStageDecoration) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertStageDecoration.Unmarshal(m, b)
}
func (m *ConcertStageDecoration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertStageDecoration.Marshal(b, m, deterministic)
}
func (dst *ConcertStageDecoration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertStageDecoration.Merge(dst, src)
}
func (m *ConcertStageDecoration) XXX_Size() int {
	return xxx_messageInfo_ConcertStageDecoration.Size(m)
}
func (m *ConcertStageDecoration) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertStageDecoration.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertStageDecoration proto.InternalMessageInfo

func (m *ConcertStageDecoration) GetStagetype() Stagetype {
	if m != nil {
		return m.Stagetype
	}
	return Stagetype_Stagetype_UNDEFINED
}

func (m *ConcertStageDecoration) GetLightOnBg() *ConcertRes {
	if m != nil {
		return m.LightOnBg
	}
	return nil
}

// 点歌
type StartConcertSingingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartConcertSingingReq) Reset()         { *m = StartConcertSingingReq{} }
func (m *StartConcertSingingReq) String() string { return proto.CompactTextString(m) }
func (*StartConcertSingingReq) ProtoMessage()    {}
func (*StartConcertSingingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{13}
}
func (m *StartConcertSingingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartConcertSingingReq.Unmarshal(m, b)
}
func (m *StartConcertSingingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartConcertSingingReq.Marshal(b, m, deterministic)
}
func (dst *StartConcertSingingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartConcertSingingReq.Merge(dst, src)
}
func (m *StartConcertSingingReq) XXX_Size() int {
	return xxx_messageInfo_StartConcertSingingReq.Size(m)
}
func (m *StartConcertSingingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartConcertSingingReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartConcertSingingReq proto.InternalMessageInfo

func (m *StartConcertSingingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartConcertSingingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartConcertSingingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type StartConcertSingingResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartConcertSingingResp) Reset()         { *m = StartConcertSingingResp{} }
func (m *StartConcertSingingResp) String() string { return proto.CompactTextString(m) }
func (*StartConcertSingingResp) ProtoMessage()    {}
func (*StartConcertSingingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{14}
}
func (m *StartConcertSingingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartConcertSingingResp.Unmarshal(m, b)
}
func (m *StartConcertSingingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartConcertSingingResp.Marshal(b, m, deterministic)
}
func (dst *StartConcertSingingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartConcertSingingResp.Merge(dst, src)
}
func (m *StartConcertSingingResp) XXX_Size() int {
	return xxx_messageInfo_StartConcertSingingResp.Size(m)
}
func (m *StartConcertSingingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartConcertSingingResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartConcertSingingResp proto.InternalMessageInfo

func (m *StartConcertSingingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 下载资源通知
type PreloadNotify struct {
	Info                 *SingingInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Lrc                  string       `protobuf:"bytes,2,opt,name=lrc,proto3" json:"lrc,omitempty"`
	BackingTrack         *ConcertRes  `protobuf:"bytes,3,opt,name=backing_track,json=backingTrack,proto3" json:"backing_track,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PreloadNotify) Reset()         { *m = PreloadNotify{} }
func (m *PreloadNotify) String() string { return proto.CompactTextString(m) }
func (*PreloadNotify) ProtoMessage()    {}
func (*PreloadNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{15}
}
func (m *PreloadNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreloadNotify.Unmarshal(m, b)
}
func (m *PreloadNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreloadNotify.Marshal(b, m, deterministic)
}
func (dst *PreloadNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreloadNotify.Merge(dst, src)
}
func (m *PreloadNotify) XXX_Size() int {
	return xxx_messageInfo_PreloadNotify.Size(m)
}
func (m *PreloadNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_PreloadNotify.DiscardUnknown(m)
}

var xxx_messageInfo_PreloadNotify proto.InternalMessageInfo

func (m *PreloadNotify) GetInfo() *SingingInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *PreloadNotify) GetLrc() string {
	if m != nil {
		return m.Lrc
	}
	return ""
}

func (m *PreloadNotify) GetBackingTrack() *ConcertRes {
	if m != nil {
		return m.BackingTrack
	}
	return nil
}

// 获取乐谱
type GetMusicBookReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32       `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicBookReq) Reset()         { *m = GetMusicBookReq{} }
func (m *GetMusicBookReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicBookReq) ProtoMessage()    {}
func (*GetMusicBookReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{16}
}
func (m *GetMusicBookReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicBookReq.Unmarshal(m, b)
}
func (m *GetMusicBookReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicBookReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicBookReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicBookReq.Merge(dst, src)
}
func (m *GetMusicBookReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicBookReq.Size(m)
}
func (m *GetMusicBookReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicBookReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicBookReq proto.InternalMessageInfo

func (m *GetMusicBookReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicBookReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMusicBookReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetMusicBookResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MusicBook            *MusicBook    `protobuf:"bytes,2,opt,name=music_book,json=musicBook,proto3" json:"music_book,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetMusicBookResp) Reset()         { *m = GetMusicBookResp{} }
func (m *GetMusicBookResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicBookResp) ProtoMessage()    {}
func (*GetMusicBookResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{17}
}
func (m *GetMusicBookResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicBookResp.Unmarshal(m, b)
}
func (m *GetMusicBookResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicBookResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicBookResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicBookResp.Merge(dst, src)
}
func (m *GetMusicBookResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicBookResp.Size(m)
}
func (m *GetMusicBookResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicBookResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicBookResp proto.InternalMessageInfo

func (m *GetMusicBookResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicBookResp) GetMusicBook() *MusicBook {
	if m != nil {
		return m.MusicBook
	}
	return nil
}

type SingingInfo struct {
	ChannelId            uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32             `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Song                 *ConcertSimpleSong `protobuf:"bytes,3,opt,name=song,proto3" json:"song,omitempty"`
	Members              []*BandMember      `protobuf:"bytes,4,rep,name=members,proto3" json:"members,omitempty"`
	Stage                ConcertGameStage   `protobuf:"varint,5,opt,name=stage,proto3,enum=ga.concert_logic.ConcertGameStage" json:"stage,omitempty"`
	StageUpdatedAt       uint32             `protobuf:"varint,6,opt,name=stage_updated_at,json=stageUpdatedAt,proto3" json:"stage_updated_at,omitempty"`
	Version              uint64             `protobuf:"varint,7,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SingingInfo) Reset()         { *m = SingingInfo{} }
func (m *SingingInfo) String() string { return proto.CompactTextString(m) }
func (*SingingInfo) ProtoMessage()    {}
func (*SingingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{18}
}
func (m *SingingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingInfo.Unmarshal(m, b)
}
func (m *SingingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingInfo.Marshal(b, m, deterministic)
}
func (dst *SingingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingInfo.Merge(dst, src)
}
func (m *SingingInfo) XXX_Size() int {
	return xxx_messageInfo_SingingInfo.Size(m)
}
func (m *SingingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SingingInfo proto.InternalMessageInfo

func (m *SingingInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SingingInfo) GetSong() *ConcertSimpleSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *SingingInfo) GetMembers() []*BandMember {
	if m != nil {
		return m.Members
	}
	return nil
}

func (m *SingingInfo) GetStage() ConcertGameStage {
	if m != nil {
		return m.Stage
	}
	return ConcertGameStage_ConcertGameStage_UNDEFINED
}

func (m *SingingInfo) GetStageUpdatedAt() uint32 {
	if m != nil {
		return m.StageUpdatedAt
	}
	return 0
}

func (m *SingingInfo) GetVersion() uint64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type ConcertSimpleSong struct {
	SongId               string     `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName             string     `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	StageOpenDuration    uint32     `protobuf:"varint,3,opt,name=stage_open_duration,json=stageOpenDuration,proto3" json:"stage_open_duration,omitempty"`
	FirstWordMs          uint32     `protobuf:"varint,4,opt,name=first_word_ms,json=firstWordMs,proto3" json:"first_word_ms,omitempty"`
	TotalDuration        uint32     `protobuf:"varint,5,opt,name=total_duration,json=totalDuration,proto3" json:"total_duration,omitempty"`
	Stagetype            Stagetype  `protobuf:"varint,6,opt,name=stagetype,proto3,enum=ga.concert_logic.Stagetype" json:"stagetype,omitempty"`
	ChosenBy             uint32     `protobuf:"varint,7,opt,name=ChosenBy,proto3" json:"ChosenBy,omitempty"`
	OpenBackingTrack     bool       `protobuf:"varint,8,opt,name=open_backing_track,json=openBackingTrack,proto3" json:"open_backing_track,omitempty"`
	BandRoles            []BandRole `protobuf:"varint,9,rep,packed,name=band_roles,json=bandRoles,proto3,enum=ga.concert_logic.BandRole" json:"band_roles,omitempty"`
	Deviation            uint32     `protobuf:"varint,10,opt,name=deviation,proto3" json:"deviation,omitempty"`
	ReactionTime         uint32     `protobuf:"varint,11,opt,name=reaction_time,json=reactionTime,proto3" json:"reaction_time,omitempty"`
	SecondaryDeviation   uint32     `protobuf:"varint,12,opt,name=secondary_deviation,json=secondaryDeviation,proto3" json:"secondary_deviation,omitempty"`
	LongDecisionInterval uint32     `protobuf:"varint,13,opt,name=long_decision_interval,json=longDecisionInterval,proto3" json:"long_decision_interval,omitempty"`
	JudgeAllCtrlZero     bool       `protobuf:"varint,14,opt,name=judge_all_ctrl_zero,json=judgeAllCtrlZero,proto3" json:"judge_all_ctrl_zero,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ConcertSimpleSong) Reset()         { *m = ConcertSimpleSong{} }
func (m *ConcertSimpleSong) String() string { return proto.CompactTextString(m) }
func (*ConcertSimpleSong) ProtoMessage()    {}
func (*ConcertSimpleSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{19}
}
func (m *ConcertSimpleSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSimpleSong.Unmarshal(m, b)
}
func (m *ConcertSimpleSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSimpleSong.Marshal(b, m, deterministic)
}
func (dst *ConcertSimpleSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSimpleSong.Merge(dst, src)
}
func (m *ConcertSimpleSong) XXX_Size() int {
	return xxx_messageInfo_ConcertSimpleSong.Size(m)
}
func (m *ConcertSimpleSong) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSimpleSong.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSimpleSong proto.InternalMessageInfo

func (m *ConcertSimpleSong) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ConcertSimpleSong) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *ConcertSimpleSong) GetStageOpenDuration() uint32 {
	if m != nil {
		return m.StageOpenDuration
	}
	return 0
}

func (m *ConcertSimpleSong) GetFirstWordMs() uint32 {
	if m != nil {
		return m.FirstWordMs
	}
	return 0
}

func (m *ConcertSimpleSong) GetTotalDuration() uint32 {
	if m != nil {
		return m.TotalDuration
	}
	return 0
}

func (m *ConcertSimpleSong) GetStagetype() Stagetype {
	if m != nil {
		return m.Stagetype
	}
	return Stagetype_Stagetype_UNDEFINED
}

func (m *ConcertSimpleSong) GetChosenBy() uint32 {
	if m != nil {
		return m.ChosenBy
	}
	return 0
}

func (m *ConcertSimpleSong) GetOpenBackingTrack() bool {
	if m != nil {
		return m.OpenBackingTrack
	}
	return false
}

func (m *ConcertSimpleSong) GetBandRoles() []BandRole {
	if m != nil {
		return m.BandRoles
	}
	return nil
}

func (m *ConcertSimpleSong) GetDeviation() uint32 {
	if m != nil {
		return m.Deviation
	}
	return 0
}

func (m *ConcertSimpleSong) GetReactionTime() uint32 {
	if m != nil {
		return m.ReactionTime
	}
	return 0
}

func (m *ConcertSimpleSong) GetSecondaryDeviation() uint32 {
	if m != nil {
		return m.SecondaryDeviation
	}
	return 0
}

func (m *ConcertSimpleSong) GetLongDecisionInterval() uint32 {
	if m != nil {
		return m.LongDecisionInterval
	}
	return 0
}

func (m *ConcertSimpleSong) GetJudgeAllCtrlZero() bool {
	if m != nil {
		return m.JudgeAllCtrlZero
	}
	return false
}

type BandMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 BandRole `protobuf:"varint,2,opt,name=role,proto3,enum=ga.concert_logic.BandRole" json:"role,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	CanPushStream        bool     `protobuf:"varint,6,opt,name=can_push_stream,json=canPushStream,proto3" json:"can_push_stream,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BandMember) Reset()         { *m = BandMember{} }
func (m *BandMember) String() string { return proto.CompactTextString(m) }
func (*BandMember) ProtoMessage()    {}
func (*BandMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{20}
}
func (m *BandMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BandMember.Unmarshal(m, b)
}
func (m *BandMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BandMember.Marshal(b, m, deterministic)
}
func (dst *BandMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BandMember.Merge(dst, src)
}
func (m *BandMember) XXX_Size() int {
	return xxx_messageInfo_BandMember.Size(m)
}
func (m *BandMember) XXX_DiscardUnknown() {
	xxx_messageInfo_BandMember.DiscardUnknown(m)
}

var xxx_messageInfo_BandMember proto.InternalMessageInfo

func (m *BandMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BandMember) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *BandMember) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *BandMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *BandMember) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *BandMember) GetCanPushStream() bool {
	if m != nil {
		return m.CanPushStream
	}
	return false
}

// 乐队成员下麦/推流用户变更/开始演唱
type SingingInfoNotify struct {
	Info                 *SingingInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SingingInfoNotify) Reset()         { *m = SingingInfoNotify{} }
func (m *SingingInfoNotify) String() string { return proto.CompactTextString(m) }
func (*SingingInfoNotify) ProtoMessage()    {}
func (*SingingInfoNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{21}
}
func (m *SingingInfoNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingInfoNotify.Unmarshal(m, b)
}
func (m *SingingInfoNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingInfoNotify.Marshal(b, m, deterministic)
}
func (dst *SingingInfoNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingInfoNotify.Merge(dst, src)
}
func (m *SingingInfoNotify) XXX_Size() int {
	return xxx_messageInfo_SingingInfoNotify.Size(m)
}
func (m *SingingInfoNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingInfoNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingInfoNotify proto.InternalMessageInfo

func (m *SingingInfoNotify) GetInfo() *SingingInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 成绩更新推送
type UpdateGradeNotify struct {
	ChannelId            uint32          `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32          `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GradeList            []*ConcertGrade `protobuf:"bytes,3,rep,name=grade_list,json=gradeList,proto3" json:"grade_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateGradeNotify) Reset()         { *m = UpdateGradeNotify{} }
func (m *UpdateGradeNotify) String() string { return proto.CompactTextString(m) }
func (*UpdateGradeNotify) ProtoMessage()    {}
func (*UpdateGradeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{22}
}
func (m *UpdateGradeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGradeNotify.Unmarshal(m, b)
}
func (m *UpdateGradeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGradeNotify.Marshal(b, m, deterministic)
}
func (dst *UpdateGradeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGradeNotify.Merge(dst, src)
}
func (m *UpdateGradeNotify) XXX_Size() int {
	return xxx_messageInfo_UpdateGradeNotify.Size(m)
}
func (m *UpdateGradeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGradeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGradeNotify proto.InternalMessageInfo

func (m *UpdateGradeNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateGradeNotify) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UpdateGradeNotify) GetGradeList() []*ConcertGrade {
	if m != nil {
		return m.GradeList
	}
	return nil
}

type ConcertResultRole struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 BandRole      `protobuf:"varint,2,opt,name=role,proto3,enum=ga.concert_logic.BandRole" json:"role,omitempty"`
	RoleName             string        `protobuf:"bytes,3,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	Nickname             string        `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string        `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	Sex                  uint32        `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	MicId                uint32        `protobuf:"varint,7,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	IsMvp                bool          `protobuf:"varint,8,opt,name=is_mvp,json=isMvp,proto3" json:"is_mvp,omitempty"`
	Grade                *ConcertGrade `protobuf:"bytes,9,opt,name=grade,proto3" json:"grade,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ConcertResultRole) Reset()         { *m = ConcertResultRole{} }
func (m *ConcertResultRole) String() string { return proto.CompactTextString(m) }
func (*ConcertResultRole) ProtoMessage()    {}
func (*ConcertResultRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{23}
}
func (m *ConcertResultRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertResultRole.Unmarshal(m, b)
}
func (m *ConcertResultRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertResultRole.Marshal(b, m, deterministic)
}
func (dst *ConcertResultRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertResultRole.Merge(dst, src)
}
func (m *ConcertResultRole) XXX_Size() int {
	return xxx_messageInfo_ConcertResultRole.Size(m)
}
func (m *ConcertResultRole) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertResultRole.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertResultRole proto.InternalMessageInfo

func (m *ConcertResultRole) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConcertResultRole) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *ConcertResultRole) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *ConcertResultRole) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ConcertResultRole) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ConcertResultRole) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ConcertResultRole) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *ConcertResultRole) GetIsMvp() bool {
	if m != nil {
		return m.IsMvp
	}
	return false
}

func (m *ConcertResultRole) GetGrade() *ConcertGrade {
	if m != nil {
		return m.Grade
	}
	return nil
}

type ConcertGrade struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	AccuracyRate         float32  `protobuf:"fixed32,2,opt,name=accuracy_rate,json=accuracyRate,proto3" json:"accuracy_rate,omitempty"`
	HitsNum              uint32   `protobuf:"varint,3,opt,name=hits_num,json=hitsNum,proto3" json:"hits_num,omitempty"`
	PerfectNum           uint32   `protobuf:"varint,4,opt,name=perfect_num,json=perfectNum,proto3" json:"perfect_num,omitempty"`
	GreatNum             uint32   `protobuf:"varint,5,opt,name=great_num,json=greatNum,proto3" json:"great_num,omitempty"`
	Miss                 uint32   `protobuf:"varint,6,opt,name=miss,proto3" json:"miss,omitempty"`
	Rate                 string   `protobuf:"bytes,7,opt,name=rate,proto3" json:"rate,omitempty"`
	Uid                  uint32   `protobuf:"varint,8,opt,name=uid,proto3" json:"uid,omitempty"`
	RateName             string   `protobuf:"bytes,9,opt,name=rate_name,json=rateName,proto3" json:"rate_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertGrade) Reset()         { *m = ConcertGrade{} }
func (m *ConcertGrade) String() string { return proto.CompactTextString(m) }
func (*ConcertGrade) ProtoMessage()    {}
func (*ConcertGrade) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{24}
}
func (m *ConcertGrade) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertGrade.Unmarshal(m, b)
}
func (m *ConcertGrade) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertGrade.Marshal(b, m, deterministic)
}
func (dst *ConcertGrade) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertGrade.Merge(dst, src)
}
func (m *ConcertGrade) XXX_Size() int {
	return xxx_messageInfo_ConcertGrade.Size(m)
}
func (m *ConcertGrade) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertGrade.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertGrade proto.InternalMessageInfo

func (m *ConcertGrade) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ConcertGrade) GetAccuracyRate() float32 {
	if m != nil {
		return m.AccuracyRate
	}
	return 0
}

func (m *ConcertGrade) GetHitsNum() uint32 {
	if m != nil {
		return m.HitsNum
	}
	return 0
}

func (m *ConcertGrade) GetPerfectNum() uint32 {
	if m != nil {
		return m.PerfectNum
	}
	return 0
}

func (m *ConcertGrade) GetGreatNum() uint32 {
	if m != nil {
		return m.GreatNum
	}
	return 0
}

func (m *ConcertGrade) GetMiss() uint32 {
	if m != nil {
		return m.Miss
	}
	return 0
}

func (m *ConcertGrade) GetRate() string {
	if m != nil {
		return m.Rate
	}
	return ""
}

func (m *ConcertGrade) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConcertGrade) GetRateName() string {
	if m != nil {
		return m.RateName
	}
	return ""
}

// 结束演唱通知
type SongResultNotify struct {
	Info                 *SingingInfo         `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Roles                []*ConcertResultRole `protobuf:"bytes,2,rep,name=roles,proto3" json:"roles,omitempty"`
	SongName             string               `protobuf:"bytes,3,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	TacitUnderstanding   uint32               `protobuf:"varint,4,opt,name=tacit_understanding,json=tacitUnderstanding,proto3" json:"tacit_understanding,omitempty"`
	GameId               uint32               `protobuf:"varint,5,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	SongId               string               `protobuf:"bytes,6,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	RecordingVideo       *ConcertRes          `protobuf:"bytes,7,opt,name=recording_video,json=recordingVideo,proto3" json:"recording_video,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SongResultNotify) Reset()         { *m = SongResultNotify{} }
func (m *SongResultNotify) String() string { return proto.CompactTextString(m) }
func (*SongResultNotify) ProtoMessage()    {}
func (*SongResultNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{25}
}
func (m *SongResultNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongResultNotify.Unmarshal(m, b)
}
func (m *SongResultNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongResultNotify.Marshal(b, m, deterministic)
}
func (dst *SongResultNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongResultNotify.Merge(dst, src)
}
func (m *SongResultNotify) XXX_Size() int {
	return xxx_messageInfo_SongResultNotify.Size(m)
}
func (m *SongResultNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SongResultNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SongResultNotify proto.InternalMessageInfo

func (m *SongResultNotify) GetInfo() *SingingInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *SongResultNotify) GetRoles() []*ConcertResultRole {
	if m != nil {
		return m.Roles
	}
	return nil
}

func (m *SongResultNotify) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *SongResultNotify) GetTacitUnderstanding() uint32 {
	if m != nil {
		return m.TacitUnderstanding
	}
	return 0
}

func (m *SongResultNotify) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SongResultNotify) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *SongResultNotify) GetRecordingVideo() *ConcertRes {
	if m != nil {
		return m.RecordingVideo
	}
	return nil
}

// 完成下载
type CompleteDownloadingMusicBookReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32       `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CompleteDownloadingMusicBookReq) Reset()         { *m = CompleteDownloadingMusicBookReq{} }
func (m *CompleteDownloadingMusicBookReq) String() string { return proto.CompactTextString(m) }
func (*CompleteDownloadingMusicBookReq) ProtoMessage()    {}
func (*CompleteDownloadingMusicBookReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{26}
}
func (m *CompleteDownloadingMusicBookReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompleteDownloadingMusicBookReq.Unmarshal(m, b)
}
func (m *CompleteDownloadingMusicBookReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompleteDownloadingMusicBookReq.Marshal(b, m, deterministic)
}
func (dst *CompleteDownloadingMusicBookReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompleteDownloadingMusicBookReq.Merge(dst, src)
}
func (m *CompleteDownloadingMusicBookReq) XXX_Size() int {
	return xxx_messageInfo_CompleteDownloadingMusicBookReq.Size(m)
}
func (m *CompleteDownloadingMusicBookReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CompleteDownloadingMusicBookReq.DiscardUnknown(m)
}

var xxx_messageInfo_CompleteDownloadingMusicBookReq proto.InternalMessageInfo

func (m *CompleteDownloadingMusicBookReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CompleteDownloadingMusicBookReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CompleteDownloadingMusicBookReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type CompleteDownloadingMusicBookResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CompleteDownloadingMusicBookResp) Reset()         { *m = CompleteDownloadingMusicBookResp{} }
func (m *CompleteDownloadingMusicBookResp) String() string { return proto.CompactTextString(m) }
func (*CompleteDownloadingMusicBookResp) ProtoMessage()    {}
func (*CompleteDownloadingMusicBookResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{27}
}
func (m *CompleteDownloadingMusicBookResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompleteDownloadingMusicBookResp.Unmarshal(m, b)
}
func (m *CompleteDownloadingMusicBookResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompleteDownloadingMusicBookResp.Marshal(b, m, deterministic)
}
func (dst *CompleteDownloadingMusicBookResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompleteDownloadingMusicBookResp.Merge(dst, src)
}
func (m *CompleteDownloadingMusicBookResp) XXX_Size() int {
	return xxx_messageInfo_CompleteDownloadingMusicBookResp.Size(m)
}
func (m *CompleteDownloadingMusicBookResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CompleteDownloadingMusicBookResp.DiscardUnknown(m)
}

var xxx_messageInfo_CompleteDownloadingMusicBookResp proto.InternalMessageInfo

func (m *CompleteDownloadingMusicBookResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 停止演唱
type StopConcertSingingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32       `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StopConcertSingingReq) Reset()         { *m = StopConcertSingingReq{} }
func (m *StopConcertSingingReq) String() string { return proto.CompactTextString(m) }
func (*StopConcertSingingReq) ProtoMessage()    {}
func (*StopConcertSingingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{28}
}
func (m *StopConcertSingingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopConcertSingingReq.Unmarshal(m, b)
}
func (m *StopConcertSingingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopConcertSingingReq.Marshal(b, m, deterministic)
}
func (dst *StopConcertSingingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopConcertSingingReq.Merge(dst, src)
}
func (m *StopConcertSingingReq) XXX_Size() int {
	return xxx_messageInfo_StopConcertSingingReq.Size(m)
}
func (m *StopConcertSingingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopConcertSingingReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopConcertSingingReq proto.InternalMessageInfo

func (m *StopConcertSingingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StopConcertSingingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StopConcertSingingReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type StopConcertSingingResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StopConcertSingingResp) Reset()         { *m = StopConcertSingingResp{} }
func (m *StopConcertSingingResp) String() string { return proto.CompactTextString(m) }
func (*StopConcertSingingResp) ProtoMessage()    {}
func (*StopConcertSingingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{29}
}
func (m *StopConcertSingingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopConcertSingingResp.Unmarshal(m, b)
}
func (m *StopConcertSingingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopConcertSingingResp.Marshal(b, m, deterministic)
}
func (dst *StopConcertSingingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopConcertSingingResp.Merge(dst, src)
}
func (m *StopConcertSingingResp) XXX_Size() int {
	return xxx_messageInfo_StopConcertSingingResp.Size(m)
}
func (m *StopConcertSingingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopConcertSingingResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopConcertSingingResp proto.InternalMessageInfo

func (m *StopConcertSingingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取演唱信息
type GetConcertInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConcertInfoReq) Reset()         { *m = GetConcertInfoReq{} }
func (m *GetConcertInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertInfoReq) ProtoMessage()    {}
func (*GetConcertInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{30}
}
func (m *GetConcertInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertInfoReq.Unmarshal(m, b)
}
func (m *GetConcertInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertInfoReq.Merge(dst, src)
}
func (m *GetConcertInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertInfoReq.Size(m)
}
func (m *GetConcertInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertInfoReq proto.InternalMessageInfo

func (m *GetConcertInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetConcertInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetConcertInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *SingingInfo  `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	Lrc                  string        `protobuf:"bytes,3,opt,name=lrc,proto3" json:"lrc,omitempty"`
	BackingTrack         *ConcertRes   `protobuf:"bytes,4,opt,name=backing_track,json=backingTrack,proto3" json:"backing_track,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetConcertInfoResp) Reset()         { *m = GetConcertInfoResp{} }
func (m *GetConcertInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertInfoResp) ProtoMessage()    {}
func (*GetConcertInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{31}
}
func (m *GetConcertInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertInfoResp.Unmarshal(m, b)
}
func (m *GetConcertInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertInfoResp.Merge(dst, src)
}
func (m *GetConcertInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertInfoResp.Size(m)
}
func (m *GetConcertInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertInfoResp proto.InternalMessageInfo

func (m *GetConcertInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetConcertInfoResp) GetInfo() *SingingInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetConcertInfoResp) GetLrc() string {
	if m != nil {
		return m.Lrc
	}
	return ""
}

func (m *GetConcertInfoResp) GetBackingTrack() *ConcertRes {
	if m != nil {
		return m.BackingTrack
	}
	return nil
}

type UpdateBackingTrackStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32       `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	OpenBackingTrack     bool         `protobuf:"varint,4,opt,name=open_backing_track,json=openBackingTrack,proto3" json:"open_backing_track,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateBackingTrackStatusReq) Reset()         { *m = UpdateBackingTrackStatusReq{} }
func (m *UpdateBackingTrackStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBackingTrackStatusReq) ProtoMessage()    {}
func (*UpdateBackingTrackStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{32}
}
func (m *UpdateBackingTrackStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBackingTrackStatusReq.Unmarshal(m, b)
}
func (m *UpdateBackingTrackStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBackingTrackStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBackingTrackStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBackingTrackStatusReq.Merge(dst, src)
}
func (m *UpdateBackingTrackStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBackingTrackStatusReq.Size(m)
}
func (m *UpdateBackingTrackStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBackingTrackStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBackingTrackStatusReq proto.InternalMessageInfo

func (m *UpdateBackingTrackStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdateBackingTrackStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateBackingTrackStatusReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UpdateBackingTrackStatusReq) GetOpenBackingTrack() bool {
	if m != nil {
		return m.OpenBackingTrack
	}
	return false
}

type UpdateBackingTrackStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateBackingTrackStatusResp) Reset()         { *m = UpdateBackingTrackStatusResp{} }
func (m *UpdateBackingTrackStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBackingTrackStatusResp) ProtoMessage()    {}
func (*UpdateBackingTrackStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{33}
}
func (m *UpdateBackingTrackStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBackingTrackStatusResp.Unmarshal(m, b)
}
func (m *UpdateBackingTrackStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBackingTrackStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBackingTrackStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBackingTrackStatusResp.Merge(dst, src)
}
func (m *UpdateBackingTrackStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBackingTrackStatusResp.Size(m)
}
func (m *UpdateBackingTrackStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBackingTrackStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBackingTrackStatusResp proto.InternalMessageInfo

func (m *UpdateBackingTrackStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type MusicBook struct {
	Role                 BandRole             `protobuf:"varint,1,opt,name=role,proto3,enum=ga.concert_logic.BandRole" json:"role,omitempty"`
	KeyMapRhythmPoints   []*KeyMapRhythmPoint `protobuf:"bytes,2,rep,name=key_map_rhythm_points,json=keyMapRhythmPoints,proto3" json:"key_map_rhythm_points,omitempty"`
	BackingTrack         *ConcertRes          `protobuf:"bytes,3,opt,name=backing_track,json=backingTrack,proto3" json:"backing_track,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *MusicBook) Reset()         { *m = MusicBook{} }
func (m *MusicBook) String() string { return proto.CompactTextString(m) }
func (*MusicBook) ProtoMessage()    {}
func (*MusicBook) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{34}
}
func (m *MusicBook) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicBook.Unmarshal(m, b)
}
func (m *MusicBook) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicBook.Marshal(b, m, deterministic)
}
func (dst *MusicBook) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicBook.Merge(dst, src)
}
func (m *MusicBook) XXX_Size() int {
	return xxx_messageInfo_MusicBook.Size(m)
}
func (m *MusicBook) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicBook.DiscardUnknown(m)
}

var xxx_messageInfo_MusicBook proto.InternalMessageInfo

func (m *MusicBook) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *MusicBook) GetKeyMapRhythmPoints() []*KeyMapRhythmPoint {
	if m != nil {
		return m.KeyMapRhythmPoints
	}
	return nil
}

func (m *MusicBook) GetBackingTrack() *ConcertRes {
	if m != nil {
		return m.BackingTrack
	}
	return nil
}

type ConcertRes struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertRes) Reset()         { *m = ConcertRes{} }
func (m *ConcertRes) String() string { return proto.CompactTextString(m) }
func (*ConcertRes) ProtoMessage()    {}
func (*ConcertRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{35}
}
func (m *ConcertRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertRes.Unmarshal(m, b)
}
func (m *ConcertRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertRes.Marshal(b, m, deterministic)
}
func (dst *ConcertRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertRes.Merge(dst, src)
}
func (m *ConcertRes) XXX_Size() int {
	return xxx_messageInfo_ConcertRes.Size(m)
}
func (m *ConcertRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertRes.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertRes proto.InternalMessageInfo

func (m *ConcertRes) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ConcertRes) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type InstrumentRes struct {
	Role                 BandRole           `protobuf:"varint,1,opt,name=role,proto3,enum=ga.concert_logic.BandRole" json:"role,omitempty"`
	MicId                uint32             `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Name                 string             `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Audios               []*InstrumentAudio `protobuf:"bytes,4,rep,name=audios,proto3" json:"audios,omitempty"`
	HoldMicAudio         *InstrumentAudio   `protobuf:"bytes,5,opt,name=hold_mic_audio,json=holdMicAudio,proto3" json:"hold_mic_audio,omitempty"`
	NormalMicSeat        string             `protobuf:"bytes,6,opt,name=normal_mic_seat,json=normalMicSeat,proto3" json:"normal_mic_seat,omitempty"`
	HoldMicSeat          string             `protobuf:"bytes,7,opt,name=hold_mic_seat,json=holdMicSeat,proto3" json:"hold_mic_seat,omitempty"`
	NormalMicLottie      *ConcertRes        `protobuf:"bytes,8,opt,name=normal_mic_lottie,json=normalMicLottie,proto3" json:"normal_mic_lottie,omitempty"`
	PlayingMicLottie     *ConcertRes        `protobuf:"bytes,9,opt,name=playing_mic_lottie,json=playingMicLottie,proto3" json:"playing_mic_lottie,omitempty"`
	EmptySeatBg          string             `protobuf:"bytes,10,opt,name=empty_seat_bg,json=emptySeatBg,proto3" json:"empty_seat_bg,omitempty"`
	HoldSeatBg           string             `protobuf:"bytes,11,opt,name=hold_seat_bg,json=holdSeatBg,proto3" json:"hold_seat_bg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *InstrumentRes) Reset()         { *m = InstrumentRes{} }
func (m *InstrumentRes) String() string { return proto.CompactTextString(m) }
func (*InstrumentRes) ProtoMessage()    {}
func (*InstrumentRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{36}
}
func (m *InstrumentRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InstrumentRes.Unmarshal(m, b)
}
func (m *InstrumentRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InstrumentRes.Marshal(b, m, deterministic)
}
func (dst *InstrumentRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InstrumentRes.Merge(dst, src)
}
func (m *InstrumentRes) XXX_Size() int {
	return xxx_messageInfo_InstrumentRes.Size(m)
}
func (m *InstrumentRes) XXX_DiscardUnknown() {
	xxx_messageInfo_InstrumentRes.DiscardUnknown(m)
}

var xxx_messageInfo_InstrumentRes proto.InternalMessageInfo

func (m *InstrumentRes) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *InstrumentRes) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *InstrumentRes) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *InstrumentRes) GetAudios() []*InstrumentAudio {
	if m != nil {
		return m.Audios
	}
	return nil
}

func (m *InstrumentRes) GetHoldMicAudio() *InstrumentAudio {
	if m != nil {
		return m.HoldMicAudio
	}
	return nil
}

func (m *InstrumentRes) GetNormalMicSeat() string {
	if m != nil {
		return m.NormalMicSeat
	}
	return ""
}

func (m *InstrumentRes) GetHoldMicSeat() string {
	if m != nil {
		return m.HoldMicSeat
	}
	return ""
}

func (m *InstrumentRes) GetNormalMicLottie() *ConcertRes {
	if m != nil {
		return m.NormalMicLottie
	}
	return nil
}

func (m *InstrumentRes) GetPlayingMicLottie() *ConcertRes {
	if m != nil {
		return m.PlayingMicLottie
	}
	return nil
}

func (m *InstrumentRes) GetEmptySeatBg() string {
	if m != nil {
		return m.EmptySeatBg
	}
	return ""
}

func (m *InstrumentRes) GetHoldSeatBg() string {
	if m != nil {
		return m.HoldSeatBg
	}
	return ""
}

type KeyMapRhythmPoint struct {
	KeyMapType           KeyMapType `protobuf:"varint,1,opt,name=key_map_type,json=keyMapType,proto3,enum=ga.concert_logic.KeyMapType" json:"key_map_type,omitempty"`
	NoteOnAt             uint32     `protobuf:"varint,2,opt,name=note_on_at,json=noteOnAt,proto3" json:"note_on_at,omitempty"`
	NoteOffAt            uint32     `protobuf:"varint,3,opt,name=note_off_at,json=noteOffAt,proto3" json:"note_off_at,omitempty"`
	FromKeyMap           uint32     `protobuf:"varint,4,opt,name=from_key_map,json=fromKeyMap,proto3" json:"from_key_map,omitempty"`
	ToKeyMap             uint32     `protobuf:"varint,5,opt,name=to_key_map,json=toKeyMap,proto3" json:"to_key_map,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *KeyMapRhythmPoint) Reset()         { *m = KeyMapRhythmPoint{} }
func (m *KeyMapRhythmPoint) String() string { return proto.CompactTextString(m) }
func (*KeyMapRhythmPoint) ProtoMessage()    {}
func (*KeyMapRhythmPoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{37}
}
func (m *KeyMapRhythmPoint) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeyMapRhythmPoint.Unmarshal(m, b)
}
func (m *KeyMapRhythmPoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeyMapRhythmPoint.Marshal(b, m, deterministic)
}
func (dst *KeyMapRhythmPoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyMapRhythmPoint.Merge(dst, src)
}
func (m *KeyMapRhythmPoint) XXX_Size() int {
	return xxx_messageInfo_KeyMapRhythmPoint.Size(m)
}
func (m *KeyMapRhythmPoint) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyMapRhythmPoint.DiscardUnknown(m)
}

var xxx_messageInfo_KeyMapRhythmPoint proto.InternalMessageInfo

func (m *KeyMapRhythmPoint) GetKeyMapType() KeyMapType {
	if m != nil {
		return m.KeyMapType
	}
	return KeyMapType_KeyMapType_UNDEFINED
}

func (m *KeyMapRhythmPoint) GetNoteOnAt() uint32 {
	if m != nil {
		return m.NoteOnAt
	}
	return 0
}

func (m *KeyMapRhythmPoint) GetNoteOffAt() uint32 {
	if m != nil {
		return m.NoteOffAt
	}
	return 0
}

func (m *KeyMapRhythmPoint) GetFromKeyMap() uint32 {
	if m != nil {
		return m.FromKeyMap
	}
	return 0
}

func (m *KeyMapRhythmPoint) GetToKeyMap() uint32 {
	if m != nil {
		return m.ToKeyMap
	}
	return 0
}

type ChordAudio struct {
	Chord                string             `protobuf:"bytes,1,opt,name=chord,proto3" json:"chord,omitempty"`
	Audios               []*InstrumentAudio `protobuf:"bytes,2,rep,name=audios,proto3" json:"audios,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ChordAudio) Reset()         { *m = ChordAudio{} }
func (m *ChordAudio) String() string { return proto.CompactTextString(m) }
func (*ChordAudio) ProtoMessage()    {}
func (*ChordAudio) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{38}
}
func (m *ChordAudio) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChordAudio.Unmarshal(m, b)
}
func (m *ChordAudio) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChordAudio.Marshal(b, m, deterministic)
}
func (dst *ChordAudio) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChordAudio.Merge(dst, src)
}
func (m *ChordAudio) XXX_Size() int {
	return xxx_messageInfo_ChordAudio.Size(m)
}
func (m *ChordAudio) XXX_DiscardUnknown() {
	xxx_messageInfo_ChordAudio.DiscardUnknown(m)
}

var xxx_messageInfo_ChordAudio proto.InternalMessageInfo

func (m *ChordAudio) GetChord() string {
	if m != nil {
		return m.Chord
	}
	return ""
}

func (m *ChordAudio) GetAudios() []*InstrumentAudio {
	if m != nil {
		return m.Audios
	}
	return nil
}

type InstrumentAudio struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	KeyMap               uint32   `protobuf:"varint,2,opt,name=key_map,json=keyMap,proto3" json:"key_map,omitempty"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InstrumentAudio) Reset()         { *m = InstrumentAudio{} }
func (m *InstrumentAudio) String() string { return proto.CompactTextString(m) }
func (*InstrumentAudio) ProtoMessage()    {}
func (*InstrumentAudio) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{39}
}
func (m *InstrumentAudio) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InstrumentAudio.Unmarshal(m, b)
}
func (m *InstrumentAudio) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InstrumentAudio.Marshal(b, m, deterministic)
}
func (dst *InstrumentAudio) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InstrumentAudio.Merge(dst, src)
}
func (m *InstrumentAudio) XXX_Size() int {
	return xxx_messageInfo_InstrumentAudio.Size(m)
}
func (m *InstrumentAudio) XXX_DiscardUnknown() {
	xxx_messageInfo_InstrumentAudio.DiscardUnknown(m)
}

var xxx_messageInfo_InstrumentAudio proto.InternalMessageInfo

func (m *InstrumentAudio) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *InstrumentAudio) GetKeyMap() uint32 {
	if m != nil {
		return m.KeyMap
	}
	return 0
}

func (m *InstrumentAudio) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *InstrumentAudio) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type ReportConcertSuccCountReq struct {
	BaseReq              *app.BaseReq             `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32                   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GradeList            []ReportConcertGradeType `protobuf:"varint,4,rep,packed,name=grade_list,json=gradeList,proto3,enum=ga.concert_logic.ReportConcertGradeType" json:"grade_list,omitempty"`
	IsReconnect          bool                     `protobuf:"varint,5,opt,name=is_reconnect,json=isReconnect,proto3" json:"is_reconnect,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ReportConcertSuccCountReq) Reset()         { *m = ReportConcertSuccCountReq{} }
func (m *ReportConcertSuccCountReq) String() string { return proto.CompactTextString(m) }
func (*ReportConcertSuccCountReq) ProtoMessage()    {}
func (*ReportConcertSuccCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{40}
}
func (m *ReportConcertSuccCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportConcertSuccCountReq.Unmarshal(m, b)
}
func (m *ReportConcertSuccCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportConcertSuccCountReq.Marshal(b, m, deterministic)
}
func (dst *ReportConcertSuccCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportConcertSuccCountReq.Merge(dst, src)
}
func (m *ReportConcertSuccCountReq) XXX_Size() int {
	return xxx_messageInfo_ReportConcertSuccCountReq.Size(m)
}
func (m *ReportConcertSuccCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportConcertSuccCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportConcertSuccCountReq proto.InternalMessageInfo

func (m *ReportConcertSuccCountReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportConcertSuccCountReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportConcertSuccCountReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ReportConcertSuccCountReq) GetGradeList() []ReportConcertGradeType {
	if m != nil {
		return m.GradeList
	}
	return nil
}

func (m *ReportConcertSuccCountReq) GetIsReconnect() bool {
	if m != nil {
		return m.IsReconnect
	}
	return false
}

type ReportConcertSuccCountResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Interval             uint32        `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportConcertSuccCountResp) Reset()         { *m = ReportConcertSuccCountResp{} }
func (m *ReportConcertSuccCountResp) String() string { return proto.CompactTextString(m) }
func (*ReportConcertSuccCountResp) ProtoMessage()    {}
func (*ReportConcertSuccCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{41}
}
func (m *ReportConcertSuccCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportConcertSuccCountResp.Unmarshal(m, b)
}
func (m *ReportConcertSuccCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportConcertSuccCountResp.Marshal(b, m, deterministic)
}
func (dst *ReportConcertSuccCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportConcertSuccCountResp.Merge(dst, src)
}
func (m *ReportConcertSuccCountResp) XXX_Size() int {
	return xxx_messageInfo_ReportConcertSuccCountResp.Size(m)
}
func (m *ReportConcertSuccCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportConcertSuccCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportConcertSuccCountResp proto.InternalMessageInfo

func (m *ReportConcertSuccCountResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ReportConcertSuccCountResp) GetInterval() uint32 {
	if m != nil {
		return m.Interval
	}
	return 0
}

type JoinConcertReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32       `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *JoinConcertReq) Reset()         { *m = JoinConcertReq{} }
func (m *JoinConcertReq) String() string { return proto.CompactTextString(m) }
func (*JoinConcertReq) ProtoMessage()    {}
func (*JoinConcertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{42}
}
func (m *JoinConcertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinConcertReq.Unmarshal(m, b)
}
func (m *JoinConcertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinConcertReq.Marshal(b, m, deterministic)
}
func (dst *JoinConcertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinConcertReq.Merge(dst, src)
}
func (m *JoinConcertReq) XXX_Size() int {
	return xxx_messageInfo_JoinConcertReq.Size(m)
}
func (m *JoinConcertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinConcertReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinConcertReq proto.InternalMessageInfo

func (m *JoinConcertReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinConcertReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinConcertReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type JoinConcertResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *SingingInfo  `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *JoinConcertResp) Reset()         { *m = JoinConcertResp{} }
func (m *JoinConcertResp) String() string { return proto.CompactTextString(m) }
func (*JoinConcertResp) ProtoMessage()    {}
func (*JoinConcertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{43}
}
func (m *JoinConcertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinConcertResp.Unmarshal(m, b)
}
func (m *JoinConcertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinConcertResp.Marshal(b, m, deterministic)
}
func (dst *JoinConcertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinConcertResp.Merge(dst, src)
}
func (m *JoinConcertResp) XXX_Size() int {
	return xxx_messageInfo_JoinConcertResp.Size(m)
}
func (m *JoinConcertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinConcertResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinConcertResp proto.InternalMessageInfo

func (m *JoinConcertResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *JoinConcertResp) GetInfo() *SingingInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type ConcertImage struct {
	Id                   string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Img                  string             `protobuf:"bytes,2,opt,name=img,proto3" json:"img,omitempty"`
	UnselectedImg        string             `protobuf:"bytes,3,opt,name=unselected_img,json=unselectedImg,proto3" json:"unselected_img,omitempty"`
	Thumbnail            string             `protobuf:"bytes,4,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	Images               []*ConcertImageRes `protobuf:"bytes,5,rep,name=images,proto3" json:"images,omitempty"`
	Sex                  uint32             `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	Level                uint32             `protobuf:"varint,7,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ConcertImage) Reset()         { *m = ConcertImage{} }
func (m *ConcertImage) String() string { return proto.CompactTextString(m) }
func (*ConcertImage) ProtoMessage()    {}
func (*ConcertImage) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{44}
}
func (m *ConcertImage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertImage.Unmarshal(m, b)
}
func (m *ConcertImage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertImage.Marshal(b, m, deterministic)
}
func (dst *ConcertImage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertImage.Merge(dst, src)
}
func (m *ConcertImage) XXX_Size() int {
	return xxx_messageInfo_ConcertImage.Size(m)
}
func (m *ConcertImage) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertImage.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertImage proto.InternalMessageInfo

func (m *ConcertImage) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ConcertImage) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *ConcertImage) GetUnselectedImg() string {
	if m != nil {
		return m.UnselectedImg
	}
	return ""
}

func (m *ConcertImage) GetThumbnail() string {
	if m != nil {
		return m.Thumbnail
	}
	return ""
}

func (m *ConcertImage) GetImages() []*ConcertImageRes {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *ConcertImage) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ConcertImage) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type ConcertImageRes struct {
	Role                 BandRole    `protobuf:"varint,1,opt,name=role,proto3,enum=ga.concert_logic.BandRole" json:"role,omitempty"`
	Res_1                *ConcertRes `protobuf:"bytes,2,opt,name=res_1,json=res1,proto3" json:"res_1,omitempty"`
	Res_2                *ConcertRes `protobuf:"bytes,3,opt,name=res_2,json=res2,proto3" json:"res_2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ConcertImageRes) Reset()         { *m = ConcertImageRes{} }
func (m *ConcertImageRes) String() string { return proto.CompactTextString(m) }
func (*ConcertImageRes) ProtoMessage()    {}
func (*ConcertImageRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{45}
}
func (m *ConcertImageRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertImageRes.Unmarshal(m, b)
}
func (m *ConcertImageRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertImageRes.Marshal(b, m, deterministic)
}
func (dst *ConcertImageRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertImageRes.Merge(dst, src)
}
func (m *ConcertImageRes) XXX_Size() int {
	return xxx_messageInfo_ConcertImageRes.Size(m)
}
func (m *ConcertImageRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertImageRes.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertImageRes proto.InternalMessageInfo

func (m *ConcertImageRes) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *ConcertImageRes) GetRes_1() *ConcertRes {
	if m != nil {
		return m.Res_1
	}
	return nil
}

func (m *ConcertImageRes) GetRes_2() *ConcertRes {
	if m != nil {
		return m.Res_2
	}
	return nil
}

// 获取形象配置
type GetAllConcertImageReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllConcertImageReq) Reset()         { *m = GetAllConcertImageReq{} }
func (m *GetAllConcertImageReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertImageReq) ProtoMessage()    {}
func (*GetAllConcertImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{46}
}
func (m *GetAllConcertImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertImageReq.Unmarshal(m, b)
}
func (m *GetAllConcertImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertImageReq.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertImageReq.Merge(dst, src)
}
func (m *GetAllConcertImageReq) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertImageReq.Size(m)
}
func (m *GetAllConcertImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertImageReq proto.InternalMessageInfo

func (m *GetAllConcertImageReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetAllConcertImageResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Images               []*ConcertImage `protobuf:"bytes,2,rep,name=images,proto3" json:"images,omitempty"`
	ChosenImageId        string          `protobuf:"bytes,3,opt,name=chosen_image_id,json=chosenImageId,proto3" json:"chosen_image_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllConcertImageResp) Reset()         { *m = GetAllConcertImageResp{} }
func (m *GetAllConcertImageResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertImageResp) ProtoMessage()    {}
func (*GetAllConcertImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{47}
}
func (m *GetAllConcertImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertImageResp.Unmarshal(m, b)
}
func (m *GetAllConcertImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertImageResp.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertImageResp.Merge(dst, src)
}
func (m *GetAllConcertImageResp) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertImageResp.Size(m)
}
func (m *GetAllConcertImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertImageResp proto.InternalMessageInfo

func (m *GetAllConcertImageResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllConcertImageResp) GetImages() []*ConcertImage {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *GetAllConcertImageResp) GetChosenImageId() string {
	if m != nil {
		return m.ChosenImageId
	}
	return ""
}

// 设置用户形象
type SetConcertUserImageReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ImageId              string       `protobuf:"bytes,3,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetConcertUserImageReq) Reset()         { *m = SetConcertUserImageReq{} }
func (m *SetConcertUserImageReq) String() string { return proto.CompactTextString(m) }
func (*SetConcertUserImageReq) ProtoMessage()    {}
func (*SetConcertUserImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{48}
}
func (m *SetConcertUserImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetConcertUserImageReq.Unmarshal(m, b)
}
func (m *SetConcertUserImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetConcertUserImageReq.Marshal(b, m, deterministic)
}
func (dst *SetConcertUserImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetConcertUserImageReq.Merge(dst, src)
}
func (m *SetConcertUserImageReq) XXX_Size() int {
	return xxx_messageInfo_SetConcertUserImageReq.Size(m)
}
func (m *SetConcertUserImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetConcertUserImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetConcertUserImageReq proto.InternalMessageInfo

func (m *SetConcertUserImageReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetConcertUserImageReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetConcertUserImageReq) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

type SetConcertUserImageResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetConcertUserImageResp) Reset()         { *m = SetConcertUserImageResp{} }
func (m *SetConcertUserImageResp) String() string { return proto.CompactTextString(m) }
func (*SetConcertUserImageResp) ProtoMessage()    {}
func (*SetConcertUserImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{49}
}
func (m *SetConcertUserImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetConcertUserImageResp.Unmarshal(m, b)
}
func (m *SetConcertUserImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetConcertUserImageResp.Marshal(b, m, deterministic)
}
func (dst *SetConcertUserImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetConcertUserImageResp.Merge(dst, src)
}
func (m *SetConcertUserImageResp) XXX_Size() int {
	return xxx_messageInfo_SetConcertUserImageResp.Size(m)
}
func (m *SetConcertUserImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetConcertUserImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetConcertUserImageResp proto.InternalMessageInfo

func (m *SetConcertUserImageResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取麦上用户形象
type GetAllConcertOnMicUserImageReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllConcertOnMicUserImageReq) Reset()         { *m = GetAllConcertOnMicUserImageReq{} }
func (m *GetAllConcertOnMicUserImageReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertOnMicUserImageReq) ProtoMessage()    {}
func (*GetAllConcertOnMicUserImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{50}
}
func (m *GetAllConcertOnMicUserImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertOnMicUserImageReq.Unmarshal(m, b)
}
func (m *GetAllConcertOnMicUserImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertOnMicUserImageReq.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertOnMicUserImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertOnMicUserImageReq.Merge(dst, src)
}
func (m *GetAllConcertOnMicUserImageReq) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertOnMicUserImageReq.Size(m)
}
func (m *GetAllConcertOnMicUserImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertOnMicUserImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertOnMicUserImageReq proto.InternalMessageInfo

func (m *GetAllConcertOnMicUserImageReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAllConcertOnMicUserImageReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetAllConcertOnMicUserImageResp struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserImage            map[uint32]*ConcertImage `protobuf:"bytes,2,rep,name=user_image,json=userImage,proto3" json:"user_image,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetAllConcertOnMicUserImageResp) Reset()         { *m = GetAllConcertOnMicUserImageResp{} }
func (m *GetAllConcertOnMicUserImageResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertOnMicUserImageResp) ProtoMessage()    {}
func (*GetAllConcertOnMicUserImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{51}
}
func (m *GetAllConcertOnMicUserImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertOnMicUserImageResp.Unmarshal(m, b)
}
func (m *GetAllConcertOnMicUserImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertOnMicUserImageResp.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertOnMicUserImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertOnMicUserImageResp.Merge(dst, src)
}
func (m *GetAllConcertOnMicUserImageResp) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertOnMicUserImageResp.Size(m)
}
func (m *GetAllConcertOnMicUserImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertOnMicUserImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertOnMicUserImageResp proto.InternalMessageInfo

func (m *GetAllConcertOnMicUserImageResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllConcertOnMicUserImageResp) GetUserImage() map[uint32]*ConcertImage {
	if m != nil {
		return m.UserImage
	}
	return nil
}

// 用户上麦/更新状态时推送
type ConcertImageNotify struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Image                *ConcertImage `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	ChannelId            uint32        `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ConcertImageNotify) Reset()         { *m = ConcertImageNotify{} }
func (m *ConcertImageNotify) String() string { return proto.CompactTextString(m) }
func (*ConcertImageNotify) ProtoMessage()    {}
func (*ConcertImageNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{52}
}
func (m *ConcertImageNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertImageNotify.Unmarshal(m, b)
}
func (m *ConcertImageNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertImageNotify.Marshal(b, m, deterministic)
}
func (dst *ConcertImageNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertImageNotify.Merge(dst, src)
}
func (m *ConcertImageNotify) XXX_Size() int {
	return xxx_messageInfo_ConcertImageNotify.Size(m)
}
func (m *ConcertImageNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertImageNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertImageNotify proto.InternalMessageInfo

func (m *ConcertImageNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConcertImageNotify) GetImage() *ConcertImage {
	if m != nil {
		return m.Image
	}
	return nil
}

func (m *ConcertImageNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 获取乐响图鉴
type GetConcertSongTaskProgressListReq struct {
	BaseReq              *app.BaseReq     `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	LoadMore             *ConcertLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32           `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetConcertSongTaskProgressListReq) Reset()         { *m = GetConcertSongTaskProgressListReq{} }
func (m *GetConcertSongTaskProgressListReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongTaskProgressListReq) ProtoMessage()    {}
func (*GetConcertSongTaskProgressListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{53}
}
func (m *GetConcertSongTaskProgressListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongTaskProgressListReq.Unmarshal(m, b)
}
func (m *GetConcertSongTaskProgressListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongTaskProgressListReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongTaskProgressListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongTaskProgressListReq.Merge(dst, src)
}
func (m *GetConcertSongTaskProgressListReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongTaskProgressListReq.Size(m)
}
func (m *GetConcertSongTaskProgressListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongTaskProgressListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongTaskProgressListReq proto.InternalMessageInfo

func (m *GetConcertSongTaskProgressListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetConcertSongTaskProgressListReq) GetLoadMore() *ConcertLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetConcertSongTaskProgressListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetConcertSongTaskProgressListResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LevelDetail          *ConcertLevelDetail        `protobuf:"bytes,2,opt,name=level_detail,json=levelDetail,proto3" json:"level_detail,omitempty"`
	HaveStarSongNum      uint32                     `protobuf:"varint,3,opt,name=have_star_song_num,json=haveStarSongNum,proto3" json:"have_star_song_num,omitempty"`
	TotalSongNum         uint32                     `protobuf:"varint,4,opt,name=total_song_num,json=totalSongNum,proto3" json:"total_song_num,omitempty"`
	List                 []*ConcertSongTaskProgress `protobuf:"bytes,5,rep,name=list,proto3" json:"list,omitempty"`
	LoadMore             *ConcertLoadMore           `protobuf:"bytes,6,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetConcertSongTaskProgressListResp) Reset()         { *m = GetConcertSongTaskProgressListResp{} }
func (m *GetConcertSongTaskProgressListResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongTaskProgressListResp) ProtoMessage()    {}
func (*GetConcertSongTaskProgressListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{54}
}
func (m *GetConcertSongTaskProgressListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongTaskProgressListResp.Unmarshal(m, b)
}
func (m *GetConcertSongTaskProgressListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongTaskProgressListResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongTaskProgressListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongTaskProgressListResp.Merge(dst, src)
}
func (m *GetConcertSongTaskProgressListResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongTaskProgressListResp.Size(m)
}
func (m *GetConcertSongTaskProgressListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongTaskProgressListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongTaskProgressListResp proto.InternalMessageInfo

func (m *GetConcertSongTaskProgressListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetConcertSongTaskProgressListResp) GetLevelDetail() *ConcertLevelDetail {
	if m != nil {
		return m.LevelDetail
	}
	return nil
}

func (m *GetConcertSongTaskProgressListResp) GetHaveStarSongNum() uint32 {
	if m != nil {
		return m.HaveStarSongNum
	}
	return 0
}

func (m *GetConcertSongTaskProgressListResp) GetTotalSongNum() uint32 {
	if m != nil {
		return m.TotalSongNum
	}
	return 0
}

func (m *GetConcertSongTaskProgressListResp) GetList() []*ConcertSongTaskProgress {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetConcertSongTaskProgressListResp) GetLoadMore() *ConcertLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type ConcertSongTaskProgress struct {
	SongId               string   `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Bg                   string   `protobuf:"bytes,2,opt,name=bg,proto3" json:"bg,omitempty"`
	CurrStarNum          uint32   `protobuf:"varint,3,opt,name=curr_star_num,json=currStarNum,proto3" json:"curr_star_num,omitempty"`
	TotalStarNum         uint32   `protobuf:"varint,4,opt,name=total_star_num,json=totalStarNum,proto3" json:"total_star_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertSongTaskProgress) Reset()         { *m = ConcertSongTaskProgress{} }
func (m *ConcertSongTaskProgress) String() string { return proto.CompactTextString(m) }
func (*ConcertSongTaskProgress) ProtoMessage()    {}
func (*ConcertSongTaskProgress) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{55}
}
func (m *ConcertSongTaskProgress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSongTaskProgress.Unmarshal(m, b)
}
func (m *ConcertSongTaskProgress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSongTaskProgress.Marshal(b, m, deterministic)
}
func (dst *ConcertSongTaskProgress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSongTaskProgress.Merge(dst, src)
}
func (m *ConcertSongTaskProgress) XXX_Size() int {
	return xxx_messageInfo_ConcertSongTaskProgress.Size(m)
}
func (m *ConcertSongTaskProgress) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSongTaskProgress.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSongTaskProgress proto.InternalMessageInfo

func (m *ConcertSongTaskProgress) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ConcertSongTaskProgress) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ConcertSongTaskProgress) GetCurrStarNum() uint32 {
	if m != nil {
		return m.CurrStarNum
	}
	return 0
}

func (m *ConcertSongTaskProgress) GetTotalStarNum() uint32 {
	if m != nil {
		return m.TotalStarNum
	}
	return 0
}

type ConcertLevelDetail struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	StarNum              uint32   `protobuf:"varint,2,opt,name=star_num,json=starNum,proto3" json:"star_num,omitempty"`
	StarNumToNextLevel   uint32   `protobuf:"varint,3,opt,name=star_num_to_next_level,json=starNumToNextLevel,proto3" json:"star_num_to_next_level,omitempty"`
	Ratio                uint32   `protobuf:"varint,4,opt,name=ratio,proto3" json:"ratio,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertLevelDetail) Reset()         { *m = ConcertLevelDetail{} }
func (m *ConcertLevelDetail) String() string { return proto.CompactTextString(m) }
func (*ConcertLevelDetail) ProtoMessage()    {}
func (*ConcertLevelDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{56}
}
func (m *ConcertLevelDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertLevelDetail.Unmarshal(m, b)
}
func (m *ConcertLevelDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertLevelDetail.Marshal(b, m, deterministic)
}
func (dst *ConcertLevelDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertLevelDetail.Merge(dst, src)
}
func (m *ConcertLevelDetail) XXX_Size() int {
	return xxx_messageInfo_ConcertLevelDetail.Size(m)
}
func (m *ConcertLevelDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertLevelDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertLevelDetail proto.InternalMessageInfo

func (m *ConcertLevelDetail) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ConcertLevelDetail) GetStarNum() uint32 {
	if m != nil {
		return m.StarNum
	}
	return 0
}

func (m *ConcertLevelDetail) GetStarNumToNextLevel() uint32 {
	if m != nil {
		return m.StarNumToNextLevel
	}
	return 0
}

func (m *ConcertLevelDetail) GetRatio() uint32 {
	if m != nil {
		return m.Ratio
	}
	return 0
}

// 获取歌曲任务进度详情
type GetConcertSongTaskProgressDetailsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SongId               string       `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConcertSongTaskProgressDetailsReq) Reset()         { *m = GetConcertSongTaskProgressDetailsReq{} }
func (m *GetConcertSongTaskProgressDetailsReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongTaskProgressDetailsReq) ProtoMessage()    {}
func (*GetConcertSongTaskProgressDetailsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{57}
}
func (m *GetConcertSongTaskProgressDetailsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongTaskProgressDetailsReq.Unmarshal(m, b)
}
func (m *GetConcertSongTaskProgressDetailsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongTaskProgressDetailsReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongTaskProgressDetailsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongTaskProgressDetailsReq.Merge(dst, src)
}
func (m *GetConcertSongTaskProgressDetailsReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongTaskProgressDetailsReq.Size(m)
}
func (m *GetConcertSongTaskProgressDetailsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongTaskProgressDetailsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongTaskProgressDetailsReq proto.InternalMessageInfo

func (m *GetConcertSongTaskProgressDetailsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetConcertSongTaskProgressDetailsReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type GetConcertSongTaskProgressDetailsResp struct {
	BaseResp             *app.BaseResp                  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LastDetail           *ConcertSongTaskProgressDetail `protobuf:"bytes,2,opt,name=last_detail,json=lastDetail,proto3" json:"last_detail,omitempty"`
	CurrDetail           *ConcertSongTaskProgressDetail `protobuf:"bytes,3,opt,name=curr_detail,json=currDetail,proto3" json:"curr_detail,omitempty"`
	NextDetail           *ConcertSongTaskProgressDetail `protobuf:"bytes,4,opt,name=next_detail,json=nextDetail,proto3" json:"next_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *GetConcertSongTaskProgressDetailsResp) Reset()         { *m = GetConcertSongTaskProgressDetailsResp{} }
func (m *GetConcertSongTaskProgressDetailsResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongTaskProgressDetailsResp) ProtoMessage()    {}
func (*GetConcertSongTaskProgressDetailsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{58}
}
func (m *GetConcertSongTaskProgressDetailsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongTaskProgressDetailsResp.Unmarshal(m, b)
}
func (m *GetConcertSongTaskProgressDetailsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongTaskProgressDetailsResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongTaskProgressDetailsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongTaskProgressDetailsResp.Merge(dst, src)
}
func (m *GetConcertSongTaskProgressDetailsResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongTaskProgressDetailsResp.Size(m)
}
func (m *GetConcertSongTaskProgressDetailsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongTaskProgressDetailsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongTaskProgressDetailsResp proto.InternalMessageInfo

func (m *GetConcertSongTaskProgressDetailsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetConcertSongTaskProgressDetailsResp) GetLastDetail() *ConcertSongTaskProgressDetail {
	if m != nil {
		return m.LastDetail
	}
	return nil
}

func (m *GetConcertSongTaskProgressDetailsResp) GetCurrDetail() *ConcertSongTaskProgressDetail {
	if m != nil {
		return m.CurrDetail
	}
	return nil
}

func (m *GetConcertSongTaskProgressDetailsResp) GetNextDetail() *ConcertSongTaskProgressDetail {
	if m != nil {
		return m.NextDetail
	}
	return nil
}

type ConcertSongTaskProgressDetail struct {
	Progress             *ConcertSongTaskProgress `protobuf:"bytes,1,opt,name=progress,proto3" json:"progress,omitempty"`
	TaskNumToNextStar    uint32                   `protobuf:"varint,2,opt,name=task_num_to_next_star,json=taskNumToNextStar,proto3" json:"task_num_to_next_star,omitempty"`
	UncompletedTasks     []*ConcertSongTask       `protobuf:"bytes,3,rep,name=uncompleted_tasks,json=uncompletedTasks,proto3" json:"uncompleted_tasks,omitempty"`
	CompletedTasks       []*ConcertSongTask       `protobuf:"bytes,4,rep,name=completed_tasks,json=completedTasks,proto3" json:"completed_tasks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ConcertSongTaskProgressDetail) Reset()         { *m = ConcertSongTaskProgressDetail{} }
func (m *ConcertSongTaskProgressDetail) String() string { return proto.CompactTextString(m) }
func (*ConcertSongTaskProgressDetail) ProtoMessage()    {}
func (*ConcertSongTaskProgressDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{59}
}
func (m *ConcertSongTaskProgressDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSongTaskProgressDetail.Unmarshal(m, b)
}
func (m *ConcertSongTaskProgressDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSongTaskProgressDetail.Marshal(b, m, deterministic)
}
func (dst *ConcertSongTaskProgressDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSongTaskProgressDetail.Merge(dst, src)
}
func (m *ConcertSongTaskProgressDetail) XXX_Size() int {
	return xxx_messageInfo_ConcertSongTaskProgressDetail.Size(m)
}
func (m *ConcertSongTaskProgressDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSongTaskProgressDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSongTaskProgressDetail proto.InternalMessageInfo

func (m *ConcertSongTaskProgressDetail) GetProgress() *ConcertSongTaskProgress {
	if m != nil {
		return m.Progress
	}
	return nil
}

func (m *ConcertSongTaskProgressDetail) GetTaskNumToNextStar() uint32 {
	if m != nil {
		return m.TaskNumToNextStar
	}
	return 0
}

func (m *ConcertSongTaskProgressDetail) GetUncompletedTasks() []*ConcertSongTask {
	if m != nil {
		return m.UncompletedTasks
	}
	return nil
}

func (m *ConcertSongTaskProgressDetail) GetCompletedTasks() []*ConcertSongTask {
	if m != nil {
		return m.CompletedTasks
	}
	return nil
}

type ConcertSongTask struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertSongTask) Reset()         { *m = ConcertSongTask{} }
func (m *ConcertSongTask) String() string { return proto.CompactTextString(m) }
func (*ConcertSongTask) ProtoMessage()    {}
func (*ConcertSongTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{60}
}
func (m *ConcertSongTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSongTask.Unmarshal(m, b)
}
func (m *ConcertSongTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSongTask.Marshal(b, m, deterministic)
}
func (dst *ConcertSongTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSongTask.Merge(dst, src)
}
func (m *ConcertSongTask) XXX_Size() int {
	return xxx_messageInfo_ConcertSongTask.Size(m)
}
func (m *ConcertSongTask) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSongTask.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSongTask proto.InternalMessageInfo

func (m *ConcertSongTask) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ConcertSongTask) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ConcertSongTask) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

// 用于强插到点歌列表
type GetConcertSongByIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SongId               string       `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConcertSongByIdReq) Reset()         { *m = GetConcertSongByIdReq{} }
func (m *GetConcertSongByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongByIdReq) ProtoMessage()    {}
func (*GetConcertSongByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{61}
}
func (m *GetConcertSongByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongByIdReq.Unmarshal(m, b)
}
func (m *GetConcertSongByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongByIdReq.Merge(dst, src)
}
func (m *GetConcertSongByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongByIdReq.Size(m)
}
func (m *GetConcertSongByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongByIdReq proto.InternalMessageInfo

func (m *GetConcertSongByIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetConcertSongByIdReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type GetConcertSongByIdResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Song                 *ConcertSong  `protobuf:"bytes,2,opt,name=song,proto3" json:"song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetConcertSongByIdResp) Reset()         { *m = GetConcertSongByIdResp{} }
func (m *GetConcertSongByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongByIdResp) ProtoMessage()    {}
func (*GetConcertSongByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{62}
}
func (m *GetConcertSongByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongByIdResp.Unmarshal(m, b)
}
func (m *GetConcertSongByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongByIdResp.Merge(dst, src)
}
func (m *GetConcertSongByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongByIdResp.Size(m)
}
func (m *GetConcertSongByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongByIdResp proto.InternalMessageInfo

func (m *GetConcertSongByIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetConcertSongByIdResp) GetSong() *ConcertSong {
	if m != nil {
		return m.Song
	}
	return nil
}

// 星级变化通知
type ConcertSongTaskProgressUpdateNotify struct {
	Progress             *ConcertSongTaskProgress `protobuf:"bytes,1,opt,name=progress,proto3" json:"progress,omitempty"`
	Title                string                   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ConcertSongTaskProgressUpdateNotify) Reset()         { *m = ConcertSongTaskProgressUpdateNotify{} }
func (m *ConcertSongTaskProgressUpdateNotify) String() string { return proto.CompactTextString(m) }
func (*ConcertSongTaskProgressUpdateNotify) ProtoMessage()    {}
func (*ConcertSongTaskProgressUpdateNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{63}
}
func (m *ConcertSongTaskProgressUpdateNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSongTaskProgressUpdateNotify.Unmarshal(m, b)
}
func (m *ConcertSongTaskProgressUpdateNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSongTaskProgressUpdateNotify.Marshal(b, m, deterministic)
}
func (dst *ConcertSongTaskProgressUpdateNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSongTaskProgressUpdateNotify.Merge(dst, src)
}
func (m *ConcertSongTaskProgressUpdateNotify) XXX_Size() int {
	return xxx_messageInfo_ConcertSongTaskProgressUpdateNotify.Size(m)
}
func (m *ConcertSongTaskProgressUpdateNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSongTaskProgressUpdateNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSongTaskProgressUpdateNotify proto.InternalMessageInfo

func (m *ConcertSongTaskProgressUpdateNotify) GetProgress() *ConcertSongTaskProgress {
	if m != nil {
		return m.Progress
	}
	return nil
}

func (m *ConcertSongTaskProgressUpdateNotify) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type GetRecentUploadedSongReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRecentUploadedSongReq) Reset()         { *m = GetRecentUploadedSongReq{} }
func (m *GetRecentUploadedSongReq) String() string { return proto.CompactTextString(m) }
func (*GetRecentUploadedSongReq) ProtoMessage()    {}
func (*GetRecentUploadedSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{64}
}
func (m *GetRecentUploadedSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentUploadedSongReq.Unmarshal(m, b)
}
func (m *GetRecentUploadedSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentUploadedSongReq.Marshal(b, m, deterministic)
}
func (dst *GetRecentUploadedSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentUploadedSongReq.Merge(dst, src)
}
func (m *GetRecentUploadedSongReq) XXX_Size() int {
	return xxx_messageInfo_GetRecentUploadedSongReq.Size(m)
}
func (m *GetRecentUploadedSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentUploadedSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentUploadedSongReq proto.InternalMessageInfo

func (m *GetRecentUploadedSongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetRecentUploadedSongResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Song                 *ConcertSong  `protobuf:"bytes,2,opt,name=song,proto3" json:"song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRecentUploadedSongResp) Reset()         { *m = GetRecentUploadedSongResp{} }
func (m *GetRecentUploadedSongResp) String() string { return proto.CompactTextString(m) }
func (*GetRecentUploadedSongResp) ProtoMessage()    {}
func (*GetRecentUploadedSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_logic__28bdbe5805ae1062, []int{65}
}
func (m *GetRecentUploadedSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentUploadedSongResp.Unmarshal(m, b)
}
func (m *GetRecentUploadedSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentUploadedSongResp.Marshal(b, m, deterministic)
}
func (dst *GetRecentUploadedSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentUploadedSongResp.Merge(dst, src)
}
func (m *GetRecentUploadedSongResp) XXX_Size() int {
	return xxx_messageInfo_GetRecentUploadedSongResp.Size(m)
}
func (m *GetRecentUploadedSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentUploadedSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentUploadedSongResp proto.InternalMessageInfo

func (m *GetRecentUploadedSongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecentUploadedSongResp) GetSong() *ConcertSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func init() {
	proto.RegisterType((*GetConcertSongOptsReq)(nil), "ga.concert_logic.GetConcertSongOptsReq")
	proto.RegisterType((*GetConcertSongOptsResp)(nil), "ga.concert_logic.GetConcertSongOptsResp")
	proto.RegisterType((*ConcertSongOpt)(nil), "ga.concert_logic.ConcertSongOpt")
	proto.RegisterType((*ConcertSongOptElem)(nil), "ga.concert_logic.ConcertSongOptElem")
	proto.RegisterType((*SearchConcertSongReq)(nil), "ga.concert_logic.SearchConcertSongReq")
	proto.RegisterType((*SearchConcertSongResp)(nil), "ga.concert_logic.SearchConcertSongResp")
	proto.RegisterType((*GetConcertSongListReq)(nil), "ga.concert_logic.GetConcertSongListReq")
	proto.RegisterType((*GetConcertSongListResp)(nil), "ga.concert_logic.GetConcertSongListResp")
	proto.RegisterType((*ConcertSong)(nil), "ga.concert_logic.ConcertSong")
	proto.RegisterType((*ConcertLoadMore)(nil), "ga.concert_logic.ConcertLoadMore")
	proto.RegisterType((*GetAllConcertResourceReq)(nil), "ga.concert_logic.GetAllConcertResourceReq")
	proto.RegisterType((*GetAllConcertResourceResp)(nil), "ga.concert_logic.GetAllConcertResourceResp")
	proto.RegisterType((*ConcertStageDecoration)(nil), "ga.concert_logic.ConcertStageDecoration")
	proto.RegisterType((*StartConcertSingingReq)(nil), "ga.concert_logic.StartConcertSingingReq")
	proto.RegisterType((*StartConcertSingingResp)(nil), "ga.concert_logic.StartConcertSingingResp")
	proto.RegisterType((*PreloadNotify)(nil), "ga.concert_logic.PreloadNotify")
	proto.RegisterType((*GetMusicBookReq)(nil), "ga.concert_logic.GetMusicBookReq")
	proto.RegisterType((*GetMusicBookResp)(nil), "ga.concert_logic.GetMusicBookResp")
	proto.RegisterType((*SingingInfo)(nil), "ga.concert_logic.SingingInfo")
	proto.RegisterType((*ConcertSimpleSong)(nil), "ga.concert_logic.ConcertSimpleSong")
	proto.RegisterType((*BandMember)(nil), "ga.concert_logic.BandMember")
	proto.RegisterType((*SingingInfoNotify)(nil), "ga.concert_logic.SingingInfoNotify")
	proto.RegisterType((*UpdateGradeNotify)(nil), "ga.concert_logic.UpdateGradeNotify")
	proto.RegisterType((*ConcertResultRole)(nil), "ga.concert_logic.ConcertResultRole")
	proto.RegisterType((*ConcertGrade)(nil), "ga.concert_logic.ConcertGrade")
	proto.RegisterType((*SongResultNotify)(nil), "ga.concert_logic.SongResultNotify")
	proto.RegisterType((*CompleteDownloadingMusicBookReq)(nil), "ga.concert_logic.CompleteDownloadingMusicBookReq")
	proto.RegisterType((*CompleteDownloadingMusicBookResp)(nil), "ga.concert_logic.CompleteDownloadingMusicBookResp")
	proto.RegisterType((*StopConcertSingingReq)(nil), "ga.concert_logic.StopConcertSingingReq")
	proto.RegisterType((*StopConcertSingingResp)(nil), "ga.concert_logic.StopConcertSingingResp")
	proto.RegisterType((*GetConcertInfoReq)(nil), "ga.concert_logic.GetConcertInfoReq")
	proto.RegisterType((*GetConcertInfoResp)(nil), "ga.concert_logic.GetConcertInfoResp")
	proto.RegisterType((*UpdateBackingTrackStatusReq)(nil), "ga.concert_logic.UpdateBackingTrackStatusReq")
	proto.RegisterType((*UpdateBackingTrackStatusResp)(nil), "ga.concert_logic.UpdateBackingTrackStatusResp")
	proto.RegisterType((*MusicBook)(nil), "ga.concert_logic.MusicBook")
	proto.RegisterType((*ConcertRes)(nil), "ga.concert_logic.ConcertRes")
	proto.RegisterType((*InstrumentRes)(nil), "ga.concert_logic.InstrumentRes")
	proto.RegisterType((*KeyMapRhythmPoint)(nil), "ga.concert_logic.KeyMapRhythmPoint")
	proto.RegisterType((*ChordAudio)(nil), "ga.concert_logic.ChordAudio")
	proto.RegisterType((*InstrumentAudio)(nil), "ga.concert_logic.InstrumentAudio")
	proto.RegisterType((*ReportConcertSuccCountReq)(nil), "ga.concert_logic.ReportConcertSuccCountReq")
	proto.RegisterType((*ReportConcertSuccCountResp)(nil), "ga.concert_logic.ReportConcertSuccCountResp")
	proto.RegisterType((*JoinConcertReq)(nil), "ga.concert_logic.JoinConcertReq")
	proto.RegisterType((*JoinConcertResp)(nil), "ga.concert_logic.JoinConcertResp")
	proto.RegisterType((*ConcertImage)(nil), "ga.concert_logic.ConcertImage")
	proto.RegisterType((*ConcertImageRes)(nil), "ga.concert_logic.ConcertImageRes")
	proto.RegisterType((*GetAllConcertImageReq)(nil), "ga.concert_logic.GetAllConcertImageReq")
	proto.RegisterType((*GetAllConcertImageResp)(nil), "ga.concert_logic.GetAllConcertImageResp")
	proto.RegisterType((*SetConcertUserImageReq)(nil), "ga.concert_logic.SetConcertUserImageReq")
	proto.RegisterType((*SetConcertUserImageResp)(nil), "ga.concert_logic.SetConcertUserImageResp")
	proto.RegisterType((*GetAllConcertOnMicUserImageReq)(nil), "ga.concert_logic.GetAllConcertOnMicUserImageReq")
	proto.RegisterType((*GetAllConcertOnMicUserImageResp)(nil), "ga.concert_logic.GetAllConcertOnMicUserImageResp")
	proto.RegisterMapType((map[uint32]*ConcertImage)(nil), "ga.concert_logic.GetAllConcertOnMicUserImageResp.UserImageEntry")
	proto.RegisterType((*ConcertImageNotify)(nil), "ga.concert_logic.ConcertImageNotify")
	proto.RegisterType((*GetConcertSongTaskProgressListReq)(nil), "ga.concert_logic.GetConcertSongTaskProgressListReq")
	proto.RegisterType((*GetConcertSongTaskProgressListResp)(nil), "ga.concert_logic.GetConcertSongTaskProgressListResp")
	proto.RegisterType((*ConcertSongTaskProgress)(nil), "ga.concert_logic.ConcertSongTaskProgress")
	proto.RegisterType((*ConcertLevelDetail)(nil), "ga.concert_logic.ConcertLevelDetail")
	proto.RegisterType((*GetConcertSongTaskProgressDetailsReq)(nil), "ga.concert_logic.GetConcertSongTaskProgressDetailsReq")
	proto.RegisterType((*GetConcertSongTaskProgressDetailsResp)(nil), "ga.concert_logic.GetConcertSongTaskProgressDetailsResp")
	proto.RegisterType((*ConcertSongTaskProgressDetail)(nil), "ga.concert_logic.ConcertSongTaskProgressDetail")
	proto.RegisterType((*ConcertSongTask)(nil), "ga.concert_logic.ConcertSongTask")
	proto.RegisterType((*GetConcertSongByIdReq)(nil), "ga.concert_logic.GetConcertSongByIdReq")
	proto.RegisterType((*GetConcertSongByIdResp)(nil), "ga.concert_logic.GetConcertSongByIdResp")
	proto.RegisterType((*ConcertSongTaskProgressUpdateNotify)(nil), "ga.concert_logic.ConcertSongTaskProgressUpdateNotify")
	proto.RegisterType((*GetRecentUploadedSongReq)(nil), "ga.concert_logic.GetRecentUploadedSongReq")
	proto.RegisterType((*GetRecentUploadedSongResp)(nil), "ga.concert_logic.GetRecentUploadedSongResp")
	proto.RegisterEnum("ga.concert_logic.ConcertSongType", ConcertSongType_name, ConcertSongType_value)
	proto.RegisterEnum("ga.concert_logic.Stagetype", Stagetype_name, Stagetype_value)
	proto.RegisterEnum("ga.concert_logic.ConcertGameStage", ConcertGameStage_name, ConcertGameStage_value)
	proto.RegisterEnum("ga.concert_logic.KeyMapType", KeyMapType_name, KeyMapType_value)
	proto.RegisterEnum("ga.concert_logic.BandRole", BandRole_name, BandRole_value)
	proto.RegisterEnum("ga.concert_logic.ReportConcertGradeType", ReportConcertGradeType_name, ReportConcertGradeType_value)
}

func init() {
	proto.RegisterFile("concert-logic_.proto", fileDescriptor_concert_logic__28bdbe5805ae1062)
}

var fileDescriptor_concert_logic__28bdbe5805ae1062 = []byte{
	// 3432 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x3a, 0x4b, 0x73, 0x23, 0x49,
	0x5a, 0x5b, 0x25, 0xc9, 0x96, 0x3e, 0x59, 0xb6, 0x9c, 0xed, 0x76, 0xab, 0x1f, 0x33, 0xe3, 0xae,
	0x79, 0x84, 0xb7, 0x61, 0xdd, 0x3b, 0xde, 0xee, 0x65, 0xa6, 0x61, 0x17, 0x6c, 0x4b, 0xe3, 0xd1,
	0x4c, 0xcb, 0x76, 0x94, 0xec, 0x19, 0x66, 0x62, 0xa1, 0x22, 0x5d, 0x95, 0x2e, 0x15, 0xae, 0xaa,
	0x54, 0x57, 0xa5, 0xdc, 0xad, 0xe5, 0x04, 0x47, 0x96, 0x08, 0x82, 0x0b, 0x9c, 0x20, 0x82, 0xe0,
	0xc0, 0x81, 0xeb, 0x12, 0xc1, 0x89, 0xe0, 0x36, 0x5c, 0xb9, 0x10, 0x5c, 0x38, 0x13, 0xc1, 0x3f,
	0xe0, 0x46, 0xe4, 0xa3, 0x4a, 0x55, 0xb2, 0x64, 0xbb, 0x9a, 0xee, 0xb9, 0x65, 0x7e, 0xaf, 0xcc,
	0xfc, 0x5e, 0xf9, 0xe5, 0x03, 0xd6, 0x6c, 0x1a, 0xda, 0x24, 0x62, 0x3f, 0xf2, 0xa9, 0xeb, 0xd9,
	0xd6, 0xd6, 0x30, 0xa2, 0x8c, 0xa2, 0xa6, 0x8b, 0xb7, 0x14, 0xc2, 0x12, 0x88, 0x7b, 0x0d, 0x17,
	0x5b, 0xa7, 0x38, 0x26, 0x92, 0xc0, 0xf8, 0x5d, 0xb8, 0xbd, 0x4f, 0xd8, 0x9e, 0x24, 0xe9, 0xd3,
	0xd0, 0x3d, 0x1c, 0xb2, 0xd8, 0x24, 0x2f, 0xd0, 0x47, 0x50, 0xe5, 0x64, 0x56, 0x44, 0x5e, 0xb4,
	0xb4, 0x0d, 0x6d, 0xb3, 0xbe, 0x5d, 0xdf, 0x72, 0xf1, 0xd6, 0x2e, 0x8e, 0x89, 0x49, 0x5e, 0x98,
	0x8b, 0xa7, 0xb2, 0x61, 0x8c, 0x61, 0x7d, 0x96, 0x80, 0x78, 0x88, 0x7e, 0x08, 0x35, 0x25, 0x21,
	0x1e, 0x2a, 0x11, 0x4b, 0x13, 0x11, 0xf1, 0xd0, 0xac, 0x9e, 0xaa, 0x16, 0x7a, 0x02, 0x65, 0x3a,
	0x64, 0x71, 0x4b, 0xdf, 0x28, 0x6d, 0xd6, 0xb7, 0x37, 0xb6, 0xa6, 0x67, 0xbd, 0x95, 0x97, 0x6f,
	0x0a, 0x6a, 0x63, 0x08, 0xcb, 0x79, 0x38, 0x5a, 0x06, 0xdd, 0x73, 0xc4, 0x58, 0x35, 0x53, 0xf7,
	0x1c, 0x84, 0xa0, 0x1c, 0xe2, 0x80, 0xb4, 0x74, 0x01, 0x11, 0x6d, 0xf4, 0x0c, 0x2a, 0xc4, 0x27,
	0x41, 0xdc, 0x2a, 0x89, 0xc1, 0x3e, 0xb8, 0x6e, 0xb0, 0x8e, 0x4f, 0x02, 0x53, 0xb2, 0x18, 0x9f,
	0x00, 0xba, 0x8c, 0xbc, 0xc9, 0xa8, 0xc6, 0x37, 0xb0, 0xd6, 0x27, 0x38, 0xb2, 0x07, 0x19, 0xfe,
	0x02, 0x6a, 0x46, 0x77, 0xa1, 0x7a, 0x4e, 0xc6, 0xd6, 0x4b, 0x1a, 0x39, 0x4a, 0xee, 0xe2, 0x39,
	0x19, 0x7f, 0x4d, 0x23, 0xc7, 0x78, 0x09, 0xb7, 0x67, 0x88, 0x2e, 0x66, 0x80, 0x9f, 0x40, 0x25,
	0xa6, 0xa1, 0x9b, 0x58, 0xe0, 0x9d, 0x2b, 0x95, 0x62, 0x4a, 0x5a, 0xe3, 0x57, 0xfa, 0xb4, 0xf3,
	0x3c, 0xf7, 0x62, 0x56, 0x64, 0x55, 0xaf, 0x65, 0x77, 0xf4, 0x73, 0xa8, 0xf1, 0x09, 0x58, 0x6c,
	0x3c, 0x24, 0xad, 0xd2, 0x86, 0xb6, 0xb9, 0xbc, 0xfd, 0xf0, 0x4a, 0xd6, 0xe3, 0xf1, 0x90, 0x98,
	0xd5, 0x58, 0xb5, 0x38, 0xbf, 0x4f, 0xb1, 0x63, 0x05, 0x34, 0x22, 0xad, 0xb2, 0x98, 0xde, 0x7c,
	0xfe, 0xe7, 0x14, 0x3b, 0x3d, 0x1a, 0x11, 0xb3, 0xea, 0xab, 0x16, 0x5a, 0x83, 0x8a, 0x4d, 0x47,
	0x21, 0x6b, 0x55, 0x36, 0xb4, 0xcd, 0x86, 0x29, 0x3b, 0xc6, 0x3f, 0x6b, 0xd3, 0x91, 0x20, 0xb5,
	0xf1, 0xf6, 0x0d, 0x91, 0x5f, 0x50, 0xa9, 0xf0, 0x82, 0x8c, 0x7f, 0xd5, 0xa0, 0x9e, 0x11, 0x7b,
	0xa3, 0x30, 0x5a, 0x06, 0xfd, 0xd4, 0x15, 0x83, 0xd5, 0x4c, 0xfd, 0xd4, 0x45, 0xeb, 0xb0, 0x80,
	0x47, 0x6c, 0x40, 0x23, 0xa1, 0xd1, 0x9a, 0xa9, 0x7a, 0xe8, 0x0e, 0x2c, 0x0e, 0x28, 0xb3, 0x2e,
	0xb0, 0xaf, 0xd4, 0xb5, 0x30, 0xa0, 0xec, 0x2b, 0xec, 0x73, 0x86, 0xd8, 0x0b, 0x5d, 0x12, 0xb5,
	0x16, 0x24, 0x83, 0xec, 0xa1, 0x77, 0x00, 0x22, 0xea, 0x13, 0x8b, 0x8f, 0x12, 0xb7, 0x16, 0x37,
	0x4a, 0x9b, 0x35, 0xb3, 0xc6, 0x21, 0x07, 0x1c, 0x80, 0x6e, 0xc3, 0x82, 0x17, 0x5b, 0x21, 0x79,
	0xd9, 0xaa, 0x6e, 0x68, 0x9b, 0x55, 0xb3, 0xe2, 0xc5, 0x07, 0xe4, 0xa5, 0xd1, 0x83, 0x95, 0xa9,
	0xf5, 0xa1, 0xfb, 0x50, 0xf3, 0x71, 0xcc, 0xac, 0x21, 0x76, 0x89, 0x58, 0x4c, 0xc3, 0xac, 0x72,
	0xc0, 0x11, 0x76, 0x09, 0x1f, 0x45, 0x20, 0xa5, 0x21, 0x75, 0x81, 0x15, 0xe4, 0x7b, 0xc2, 0x98,
	0xbf, 0x80, 0xd6, 0x3e, 0x61, 0x3b, 0xbe, 0xaf, 0x84, 0x9a, 0x24, 0xa6, 0xa3, 0xc8, 0x26, 0x45,
	0x9c, 0xbb, 0x05, 0x8b, 0x17, 0x24, 0x8a, 0x3d, 0x1a, 0x2a, 0xf9, 0x49, 0xd7, 0xf8, 0x9b, 0x12,
	0xdc, 0x9d, 0x23, 0xbe, 0x98, 0xb7, 0xcc, 0x1d, 0x02, 0x3d, 0x83, 0x6a, 0x44, 0x62, 0xcb, 0xf7,
	0x62, 0xa6, 0x12, 0xdd, 0x7b, 0x97, 0x3d, 0xa2, 0x1b, 0xc6, 0x2c, 0x1a, 0x05, 0x24, 0xe4, 0x13,
	0x30, 0x17, 0x23, 0x12, 0x73, 0x97, 0x45, 0x5f, 0x40, 0xdd, 0x21, 0x36, 0x8d, 0x30, 0xf3, 0x68,
	0x18, 0xb7, 0xca, 0x82, 0x7d, 0x73, 0xbe, 0x27, 0x32, 0xec, 0x92, 0x76, 0xca, 0x60, 0x66, 0x99,
	0xd1, 0x7b, 0x5c, 0xd6, 0x19, 0x1e, 0xf9, 0xcc, 0x1a, 0x45, 0xd2, 0x05, 0x6a, 0x26, 0x28, 0xd0,
	0x49, 0xe4, 0x67, 0x09, 0x02, 0xe7, 0xa9, 0xf2, 0x85, 0x84, 0xa0, 0xe7, 0x3c, 0x45, 0x4f, 0x60,
	0x3d, 0x21, 0x60, 0x11, 0x0e, 0x63, 0x8f, 0x0b, 0xb6, 0xbc, 0xc0, 0x6d, 0x2d, 0x0a, 0xda, 0x35,
	0x85, 0x3d, 0x4e, 0x91, 0xdd, 0xc0, 0x45, 0xbf, 0x0d, 0x10, 0xd3, 0xd0, 0xb3, 0xad, 0x97, 0xf8,
	0x82, 0x08, 0x57, 0xa9, 0x6f, 0x3f, 0x98, 0xbb, 0x04, 0xbe, 0xfc, 0x9a, 0xa0, 0xff, 0x1a, 0x5f,
	0x10, 0xe3, 0x2f, 0x35, 0x58, 0x9f, 0xbd, 0x38, 0xf4, 0x29, 0xd4, 0x62, 0x0e, 0x12, 0xb9, 0x47,
	0x13, 0xb9, 0xe7, 0xfe, 0x65, 0xb1, 0xfd, 0x84, 0xc4, 0x9c, 0x50, 0xa3, 0xdf, 0x81, 0xba, 0xef,
	0xb9, 0x03, 0x66, 0xd1, 0xd0, 0x3a, 0x75, 0x85, 0xc1, 0xae, 0x9d, 0x93, 0x60, 0x38, 0x0c, 0x77,
	0x5d, 0xe3, 0x15, 0xac, 0xf7, 0x19, 0x8e, 0xd2, 0xfc, 0xe2, 0x85, 0xae, 0x57, 0x6c, 0x0b, 0x79,
	0x07, 0xc0, 0x1e, 0xe0, 0x30, 0x24, 0xbe, 0xe5, 0x39, 0x89, 0xcb, 0x2b, 0x48, 0xd7, 0xe1, 0x81,
	0x2a, 0xb2, 0xaa, 0xe7, 0xa8, 0xa8, 0x5e, 0xe0, 0xdd, 0xae, 0x63, 0xb4, 0xe1, 0xce, 0xcc, 0x91,
	0x0b, 0xb9, 0xaa, 0xf1, 0xd7, 0x1a, 0x34, 0x8e, 0x22, 0xc2, 0x73, 0xce, 0x01, 0x65, 0xde, 0xd9,
	0x18, 0x7d, 0x0c, 0x65, 0x2f, 0x3c, 0xa3, 0x8a, 0x6f, 0x46, 0xa6, 0x53, 0x23, 0x75, 0xc3, 0x33,
	0x6a, 0x0a, 0x52, 0xd4, 0x84, 0x92, 0x1f, 0xd9, 0x2a, 0x0f, 0xf1, 0x26, 0xda, 0x81, 0xc6, 0x29,
	0xb6, 0xcf, 0x3d, 0xbe, 0x1d, 0x44, 0xd8, 0x3e, 0x57, 0xe9, 0xef, 0x6a, 0xb5, 0x2e, 0x29, 0x96,
	0x63, 0xce, 0x61, 0xbc, 0x80, 0x95, 0x7d, 0xc2, 0x7a, 0xa3, 0xd8, 0xb3, 0x77, 0x29, 0x3d, 0x7f,
	0xb3, 0x2a, 0x75, 0x71, 0x40, 0x12, 0x95, 0x36, 0xcc, 0x05, 0xde, 0xed, 0x3a, 0xc6, 0x18, 0x9a,
	0xf9, 0x21, 0x8b, 0x85, 0xfd, 0x33, 0x80, 0x80, 0xf3, 0x5a, 0xa7, 0x94, 0x9e, 0x2b, 0x47, 0x9a,
	0xe1, 0x85, 0x13, 0xf9, 0xb5, 0x20, 0x69, 0x1a, 0xbf, 0xd6, 0xa1, 0x9e, 0x51, 0xec, 0xd4, 0x12,
	0xb4, 0x2b, 0x96, 0xa0, 0x67, 0x97, 0x80, 0x7e, 0x0b, 0xca, 0xdc, 0x3f, 0x94, 0xbe, 0xdf, 0x9f,
	0x9f, 0x1d, 0xbc, 0x60, 0xe8, 0x13, 0xb1, 0x5b, 0x09, 0x06, 0xf4, 0x53, 0x58, 0x0c, 0x48, 0x70,
	0x4a, 0xa2, 0x24, 0xb3, 0xcc, 0xb0, 0xd5, 0x2e, 0x0e, 0x9d, 0x9e, 0x20, 0x32, 0x13, 0x62, 0xf4,
	0x09, 0x54, 0x44, 0x2c, 0x89, 0x1c, 0xb2, 0xbc, 0x6d, 0xcc, 0x1d, 0x71, 0x1f, 0x07, 0x44, 0x04,
	0xa0, 0x29, 0x19, 0xd0, 0x26, 0x34, 0x45, 0xc3, 0x1a, 0x0d, 0x1d, 0xcc, 0x88, 0x63, 0x61, 0x26,
	0xf2, 0x4c, 0xc3, 0x5c, 0x16, 0xf0, 0x13, 0x09, 0xde, 0x61, 0xd9, 0x7c, 0xca, 0x93, 0x4b, 0x79,
	0x92, 0xb2, 0xff, 0xab, 0x0c, 0xab, 0x97, 0x56, 0x94, 0x8d, 0x19, 0x2d, 0x1b, 0x33, 0x7c, 0xef,
	0x11, 0x88, 0xcc, 0xb6, 0x29, 0xea, 0x0f, 0xbe, 0x87, 0xa1, 0x2d, 0xb8, 0x25, 0xe7, 0x43, 0x87,
	0x24, 0xb4, 0x9c, 0x91, 0x4c, 0x2d, 0xca, 0x45, 0x56, 0x05, 0xea, 0x70, 0x48, 0xc2, 0xb6, 0x42,
	0x20, 0x03, 0x1a, 0x67, 0x5e, 0x14, 0x33, 0x51, 0xfd, 0x59, 0x41, 0x2c, 0x76, 0xd8, 0x86, 0x59,
	0x17, 0x40, 0x5e, 0x02, 0xf6, 0x62, 0xf4, 0x21, 0x2c, 0x33, 0xca, 0xb0, 0x3f, 0x11, 0x27, 0x77,
	0xdb, 0x86, 0x80, 0xa6, 0xa2, 0x72, 0xe9, 0x6b, 0xa1, 0x50, 0xfa, 0xba, 0x07, 0xd5, 0xbd, 0x01,
	0x8d, 0x49, 0xb8, 0x3b, 0x16, 0xca, 0x69, 0x98, 0x69, 0x1f, 0xfd, 0x26, 0x20, 0xb1, 0x96, 0x7c,
	0x28, 0xca, 0x0d, 0xba, 0xc9, 0x31, 0xbb, 0x99, 0x80, 0x43, 0x9f, 0x02, 0x9c, 0xe2, 0xd0, 0xb1,
	0xf8, 0xa6, 0x1e, 0xb7, 0x6a, 0x1b, 0xa5, 0xcd, 0xe5, 0xed, 0x7b, 0xb3, 0x9d, 0xc0, 0xa4, 0x3e,
	0x31, 0x6b, 0xa7, 0xaa, 0x15, 0xa3, 0x07, 0x50, 0x73, 0xc8, 0x85, 0x27, 0x57, 0x08, 0xd2, 0x59,
	0x53, 0x00, 0x7a, 0x1f, 0x1a, 0x11, 0xc1, 0xb6, 0xd8, 0x20, 0x98, 0x17, 0x90, 0x56, 0x5d, 0x50,
	0x2c, 0x25, 0xc0, 0x63, 0x2f, 0x20, 0xe8, 0x31, 0xdc, 0x8a, 0x89, 0x4d, 0x43, 0x07, 0x47, 0x63,
	0x6b, 0x22, 0x6c, 0x49, 0x90, 0xa2, 0x14, 0xd5, 0x4e, 0xa5, 0x3e, 0x81, 0x75, 0x9f, 0xdb, 0xd2,
	0x21, 0xb6, 0x17, 0x8b, 0xbd, 0x27, 0x64, 0x24, 0xe2, 0x05, 0x4d, 0x43, 0xf0, 0xac, 0x71, 0x6c,
	0x5b, 0x21, 0xbb, 0x0a, 0x87, 0x7e, 0x04, 0xb7, 0xfe, 0x68, 0xe4, 0xb8, 0xc4, 0xc2, 0xbe, 0x6f,
	0xd9, 0x2c, 0xf2, 0xad, 0x5f, 0x92, 0x88, 0xb6, 0x96, 0xa5, 0x4e, 0x04, 0x8a, 0xef, 0xff, 0x2c,
	0xf2, 0xbf, 0x25, 0x11, 0x35, 0xfe, 0x45, 0x03, 0x98, 0x78, 0x3d, 0x4f, 0x74, 0xa3, 0x34, 0x1c,
	0x79, 0x13, 0x6d, 0x41, 0x99, 0xeb, 0x4b, 0x38, 0xd3, 0xd5, 0xea, 0x12, 0x74, 0xdc, 0x5c, 0xa1,
	0x67, 0x9f, 0x0b, 0x07, 0x94, 0xf9, 0x3c, 0xed, 0x73, 0x37, 0xc7, 0xb6, 0xac, 0x7c, 0x64, 0xb1,
	0x96, 0x74, 0xf9, 0xb8, 0x31, 0x79, 0xa5, 0x7c, 0x87, 0x37, 0xd1, 0x47, 0xb0, 0x62, 0xe3, 0xd0,
	0x1a, 0x8e, 0xe2, 0x81, 0x15, 0xb3, 0x88, 0xe0, 0x40, 0xf8, 0x4d, 0xd5, 0x6c, 0xd8, 0x38, 0x3c,
	0x1a, 0xc5, 0x83, 0xbe, 0x00, 0x1a, 0x9f, 0xc1, 0x6a, 0x26, 0xad, 0xbc, 0x76, 0x8a, 0x37, 0xfe,
	0x4c, 0x83, 0x55, 0x19, 0x90, 0xfb, 0x11, 0x76, 0x88, 0x12, 0xf4, 0xba, 0x59, 0xea, 0x67, 0x00,
	0x2e, 0x17, 0x93, 0x2d, 0x84, 0xde, 0x9d, 0x9f, 0x39, 0x38, 0xa9, 0x59, 0x13, 0x1c, 0xbc, 0x12,
	0x32, 0xfe, 0x56, 0x4f, 0xa3, 0xde, 0x24, 0xf1, 0xc8, 0x67, 0x5c, 0xc1, 0x6f, 0xc0, 0x38, 0xf7,
	0xa1, 0x96, 0xd6, 0xb8, 0x89, 0x75, 0x92, 0x12, 0x37, 0x67, 0xb9, 0xf2, 0x7c, 0xcb, 0x55, 0x66,
	0x5a, 0x6e, 0x61, 0x62, 0xb9, 0xdb, 0xb0, 0x10, 0x78, 0x36, 0xd7, 0x89, 0x0c, 0xd7, 0x4a, 0xe0,
	0xd9, 0x5d, 0x47, 0x15, 0xd0, 0xc1, 0xc5, 0x70, 0x52, 0x40, 0xf7, 0x2e, 0xf8, 0x11, 0xbc, 0x22,
	0xd6, 0xdd, 0xaa, 0x09, 0x5b, 0x5d, 0xa7, 0x24, 0x49, 0x6c, 0xfc, 0xaf, 0x06, 0x4b, 0x59, 0xf8,
	0xe4, 0x6c, 0xa4, 0x65, 0xce, 0x46, 0x3c, 0x30, 0xb1, 0x6d, 0x8f, 0x22, 0x6c, 0x8f, 0xad, 0x08,
	0x33, 0xa9, 0x28, 0xdd, 0x5c, 0x4a, 0x80, 0x26, 0x66, 0x84, 0x1f, 0x71, 0x07, 0x1e, 0x8b, 0xad,
	0x70, 0x14, 0xa8, 0x5c, 0xb8, 0xc8, 0xfb, 0x07, 0xa3, 0x80, 0x17, 0x89, 0x43, 0x12, 0x9d, 0x11,
	0x9b, 0x09, 0xac, 0xcc, 0x7f, 0xa0, 0x40, 0x9c, 0xe0, 0x3e, 0xd4, 0xdc, 0x88, 0x60, 0x89, 0x96,
	0xde, 0x5b, 0x15, 0x00, 0x8e, 0x44, 0x50, 0x0e, 0xbc, 0x38, 0x56, 0xba, 0x11, 0x6d, 0x0e, 0x13,
	0x13, 0x91, 0x35, 0xa4, 0x68, 0x27, 0x76, 0xad, 0x4e, 0xec, 0xca, 0xed, 0x84, 0x99, 0xb2, 0x53,
	0x4d, 0xd9, 0x09, 0x33, 0x61, 0x27, 0xe3, 0x3b, 0x1d, 0x9a, 0xea, 0xac, 0x3d, 0xf2, 0xd9, 0xeb,
	0x17, 0x35, 0x9f, 0x42, 0x45, 0x66, 0x42, 0x79, 0xe4, 0x7b, 0xff, 0xaa, 0xd2, 0x45, 0xb9, 0xa0,
	0x29, 0x39, 0xf2, 0xdb, 0x4c, 0x69, 0x6a, 0x9b, 0x79, 0x0c, 0xb7, 0x18, 0xb6, 0x3d, 0x66, 0x8d,
	0x42, 0x87, 0x44, 0x31, 0xc3, 0xa1, 0xe3, 0x85, 0xae, 0x52, 0x1e, 0x12, 0xa8, 0x93, 0x2c, 0x26,
	0x1b, 0x45, 0x95, 0x5c, 0x14, 0x65, 0xb6, 0xb9, 0x85, 0xdc, 0x36, 0xd7, 0x81, 0x95, 0x88, 0xd7,
	0xc6, 0x9c, 0xdd, 0xba, 0xf0, 0x1c, 0x42, 0x85, 0x42, 0xaf, 0xab, 0xbf, 0x96, 0x53, 0xa6, 0xaf,
	0x38, 0x8f, 0xf1, 0x27, 0x1a, 0xbc, 0xb7, 0x47, 0xf9, 0xae, 0xca, 0x48, 0x9b, 0xbe, 0x0c, 0x79,
	0x91, 0xe8, 0x85, 0xee, 0xf7, 0x5a, 0x92, 0xf5, 0x60, 0xe3, 0xea, 0x29, 0x14, 0x2b, 0x77, 0x5f,
	0xc2, 0xed, 0x3e, 0xa3, 0xc3, 0xb7, 0x59, 0xad, 0xcf, 0x5e, 0xc7, 0x1e, 0x3f, 0x27, 0x5c, 0x1e,
	0xb8, 0xd8, 0xec, 0xbf, 0x85, 0xd5, 0xc9, 0x55, 0x86, 0x70, 0xd5, 0x37, 0x36, 0x73, 0xe3, 0x3b,
	0x0d, 0xd0, 0xb4, 0xf0, 0x62, 0xe5, 0x6f, 0x12, 0x63, 0x7a, 0xe1, 0x83, 0x43, 0xe9, 0x8a, 0x83,
	0x43, 0xb9, 0xf0, 0xc1, 0xe1, 0x1f, 0x35, 0xb8, 0x2f, 0xb7, 0xaa, 0x6c, 0x79, 0xd3, 0x67, 0x98,
	0x8d, 0xe2, 0xef, 0xc1, 0xd4, 0x73, 0xaa, 0xae, 0xf2, 0xec, 0xaa, 0xcb, 0xe8, 0xc2, 0x83, 0xf9,
	0x93, 0x2d, 0xe6, 0x1e, 0xff, 0xa1, 0x41, 0x2d, 0x8d, 0x8c, 0x74, 0xf3, 0xd3, 0x6e, 0xb8, 0xf9,
	0x7d, 0x05, 0xb7, 0xcf, 0xc9, 0xd8, 0x0a, 0xf0, 0xd0, 0x8a, 0x06, 0x63, 0x36, 0x08, 0xac, 0x21,
	0xf5, 0x42, 0x76, 0x45, 0xfe, 0xfb, 0x92, 0x8c, 0x7b, 0x78, 0x68, 0x0a, 0xe2, 0x23, 0x4e, 0x6b,
	0xa2, 0xf3, 0x69, 0x50, 0xfc, 0x26, 0x8e, 0x82, 0x3f, 0x06, 0x98, 0xe0, 0xc4, 0x7e, 0x10, 0xf9,
	0xaa, 0xb2, 0xe7, 0x4d, 0x0e, 0x09, 0x9c, 0xa7, 0xc9, 0xf9, 0x33, 0x70, 0x9e, 0x1a, 0x7f, 0x55,
	0x86, 0x46, 0xee, 0x1a, 0xa5, 0xb0, 0x3a, 0x26, 0xdb, 0xb4, 0x9e, 0xdd, 0xa6, 0x93, 0x3b, 0xb7,
	0x52, 0xe6, 0xce, 0xed, 0x53, 0x58, 0xc0, 0x23, 0xc7, 0xa3, 0xc9, 0xc9, 0xe9, 0xe1, 0x55, 0x57,
	0x3a, 0x3b, 0x9c, 0xd2, 0x54, 0x0c, 0x68, 0x1f, 0x96, 0x07, 0xd4, 0x77, 0x2c, 0x3e, 0x94, 0x00,
	0x89, 0x14, 0x7f, 0x23, 0x11, 0x4b, 0x9c, 0xb1, 0xe7, 0xd9, 0xa2, 0xc7, 0xeb, 0xc1, 0x90, 0x46,
	0x01, 0xf6, 0x85, 0xa8, 0x98, 0xa8, 0xb3, 0x54, 0xcd, 0x6c, 0x48, 0x70, 0xcf, 0xb3, 0xfb, 0x04,
	0x33, 0x7e, 0x68, 0x49, 0x07, 0x14, 0x54, 0x72, 0xa7, 0xad, 0x2b, 0x61, 0x82, 0xe6, 0x73, 0x58,
	0xcd, 0xc8, 0xf2, 0x29, 0x63, 0xde, 0xcd, 0xee, 0x6a, 0x56, 0xd2, 0xb1, 0x9e, 0x0b, 0x26, 0xf4,
	0x05, 0xa0, 0xa1, 0x8f, 0xc7, 0xdc, 0xf6, 0x19, 0x51, 0xb5, 0x1b, 0x88, 0x6a, 0x2a, 0xbe, 0x89,
	0x2c, 0x03, 0x1a, 0x24, 0x18, 0xb2, 0xb1, 0x98, 0xb6, 0x75, 0xea, 0x8a, 0x73, 0x46, 0xcd, 0xac,
	0x0b, 0x20, 0x9f, 0xf7, 0xae, 0x8b, 0x36, 0x40, 0x68, 0x25, 0x25, 0xa9, 0xcb, 0x6b, 0x2b, 0x0e,
	0x93, 0x14, 0xc6, 0xbf, 0x6b, 0xb0, 0x7a, 0xc9, 0x6f, 0xd1, 0xcf, 0x61, 0x29, 0xf1, 0xfd, 0xcc,
	0x0d, 0xd2, 0x83, 0x79, 0x2e, 0x2f, 0x2e, 0xae, 0xe1, 0x3c, 0x6d, 0xa3, 0x07, 0x00, 0x21, 0x65,
	0xc4, 0xa2, 0x21, 0x3f, 0xc4, 0x4a, 0x87, 0xa9, 0x72, 0xc8, 0x61, 0xb8, 0xc3, 0xd0, 0xbb, 0x50,
	0x97, 0xd8, 0xb3, 0x33, 0x8e, 0x96, 0xd9, 0xa2, 0x26, 0xd0, 0x67, 0x67, 0x3b, 0x8c, 0xcf, 0xfa,
	0x2c, 0xa2, 0x81, 0xa5, 0xa6, 0x90, 0xd4, 0x51, 0x1c, 0x26, 0xc7, 0xe3, 0xf2, 0x19, 0x4d, 0xf1,
	0xaa, 0x90, 0x62, 0x54, 0x62, 0x8d, 0x3f, 0x00, 0xd8, 0x1b, 0xd0, 0xc8, 0x91, 0x9e, 0xc0, 0x4b,
	0x3d, 0xde, 0x53, 0x01, 0x22, 0x3b, 0x19, 0x1f, 0xd5, 0x0b, 0xfa, 0xa8, 0xf1, 0x87, 0xb0, 0x32,
	0x85, 0xba, 0x74, 0x13, 0x7d, 0x07, 0x16, 0x93, 0xc9, 0xa9, 0x42, 0x5f, 0x2a, 0x27, 0x89, 0xd5,
	0xd2, 0xa5, 0x58, 0x2d, 0x4f, 0x62, 0xf5, 0xbf, 0x35, 0xb8, 0x6b, 0x92, 0x21, 0x9d, 0x5c, 0x65,
	0x8d, 0x6c, 0x5b, 0xdc, 0xf7, 0x7e, 0x1f, 0xd9, 0x7a, 0x3f, 0x77, 0x14, 0x29, 0x8b, 0x53, 0xef,
	0x8c, 0x4b, 0xd5, 0xdc, 0x04, 0x45, 0x4d, 0x2d, 0x9c, 0x60, 0x72, 0x28, 0x41, 0x0f, 0x61, 0xc9,
	0x8b, 0x2d, 0x5e, 0x42, 0x85, 0x21, 0xb1, 0xe5, 0x41, 0xa0, 0x6a, 0xd6, 0xbd, 0xd8, 0x4c, 0x40,
	0x86, 0x0d, 0xf7, 0xe6, 0x2d, 0xb4, 0xd8, 0x56, 0x7b, 0x0f, 0xaa, 0xe9, 0x69, 0x57, 0x79, 0x5b,
	0xd2, 0x37, 0x86, 0xb0, 0xfc, 0x05, 0xf5, 0xc2, 0x34, 0x96, 0xde, 0x7e, 0x6d, 0x43, 0x61, 0x25,
	0x37, 0xe2, 0xdb, 0x2e, 0x1b, 0x8c, 0xff, 0x9c, 0x1c, 0x6f, 0xba, 0x01, 0x76, 0xc9, 0x25, 0x7f,
	0x6c, 0x42, 0xc9, 0x0b, 0xdc, 0x64, 0x43, 0xf0, 0x02, 0x17, 0x7d, 0x08, 0xcb, 0xa3, 0x30, 0x26,
	0x3e, 0xb1, 0x19, 0x71, 0xc4, 0x35, 0xb5, 0xf4, 0xc9, 0xc6, 0x04, 0xda, 0x0d, 0x5c, 0xf4, 0x00,
	0x6a, 0x6c, 0x30, 0x0a, 0x4e, 0x43, 0xec, 0xf9, 0xca, 0x47, 0x27, 0x00, 0x1e, 0x44, 0x1e, 0x1f,
	0x2f, 0x6e, 0x55, 0xe6, 0x05, 0x51, 0x76, 0x5a, 0x3c, 0x8f, 0x29, 0x86, 0x19, 0xe7, 0xc0, 0x35,
	0xa8, 0xf8, 0xe4, 0x82, 0xf8, 0xc9, 0x31, 0x50, 0x74, 0x8c, 0x7f, 0xd0, 0xd2, 0x17, 0x93, 0x44,
	0x46, 0xe1, 0xad, 0xeb, 0x63, 0xa8, 0x44, 0x24, 0xb6, 0x3e, 0xbe, 0xd1, 0x5d, 0x76, 0x39, 0x22,
	0xf1, 0xc7, 0x09, 0xcb, 0xf6, 0x8d, 0x36, 0x67, 0xce, 0xb2, 0xad, 0x9e, 0xa8, 0x27, 0x8f, 0x25,
	0x6a, 0xba, 0x37, 0x7f, 0xa2, 0xfe, 0x7b, 0xf9, 0x32, 0x77, 0x49, 0x42, 0x31, 0xf7, 0xf9, 0x69,
	0x6a, 0x13, 0xfd, 0x9a, 0x6b, 0x04, 0x29, 0x3e, 0x31, 0xc8, 0x47, 0xb0, 0x62, 0x8b, 0x7b, 0x32,
	0x4b, 0x00, 0x26, 0xf7, 0xeb, 0x0d, 0x09, 0x16, 0xd4, 0x5d, 0xc7, 0xf8, 0x25, 0xac, 0xf7, 0xd3,
	0xb2, 0xf8, 0x24, 0x26, 0x51, 0xd1, 0x75, 0x5e, 0x17, 0x56, 0x77, 0xa1, 0x3a, 0x35, 0x83, 0x45,
	0x4f, 0x8d, 0xdd, 0x86, 0x3b, 0x33, 0xc7, 0x2e, 0x56, 0x16, 0xba, 0xf0, 0x6e, 0x4e, 0xcd, 0x87,
	0x61, 0xcf, 0xb3, 0xdf, 0xc2, 0x4a, 0x8c, 0x5f, 0xe9, 0xf0, 0xde, 0x95, 0x23, 0x15, 0xb3, 0xac,
	0x05, 0x30, 0x8a, 0x49, 0x24, 0xed, 0xa3, 0xac, 0xfb, 0x7b, 0x97, 0xad, 0x7b, 0xcd, 0x88, 0x5b,
	0x69, 0xaf, 0x13, 0xb2, 0x68, 0x6c, 0xd6, 0x46, 0x49, 0xff, 0xde, 0x2f, 0x60, 0x39, 0x8f, 0xe4,
	0x51, 0x7a, 0x4e, 0xc6, 0xc9, 0x15, 0xd2, 0x39, 0x19, 0xa3, 0x27, 0x50, 0xb9, 0xc0, 0xfe, 0x88,
	0xa8, 0x58, 0xba, 0xce, 0xbb, 0x24, 0xf1, 0x33, 0xfd, 0x13, 0xcd, 0xf8, 0xe3, 0xf4, 0x53, 0x82,
	0x40, 0xa9, 0x8b, 0x88, 0xcb, 0x97, 0x54, 0x4f, 0xa0, 0x92, 0xac, 0xf0, 0x46, 0x23, 0x08, 0xe2,
	0x29, 0x53, 0x94, 0xa6, 0x4d, 0xf1, 0x77, 0x1a, 0x3c, 0xcc, 0xbf, 0x7a, 0x1f, 0xe3, 0xf8, 0xfc,
	0x28, 0xa2, 0x6e, 0x44, 0xe2, 0xb8, 0xe8, 0x7f, 0x80, 0xdc, 0x43, 0xb6, 0xfe, 0xff, 0x78, 0x99,
	0x2f, 0x65, 0x5f, 0xe6, 0xff, 0x47, 0x07, 0xe3, 0xba, 0x39, 0x16, 0xf3, 0x98, 0x7d, 0x58, 0x12,
	0x59, 0xd4, 0x72, 0x08, 0xe3, 0x09, 0x5c, 0x4e, 0x75, 0xfe, 0x57, 0x92, 0xe7, 0x9c, 0xb8, 0x2d,
	0x68, 0xcd, 0xba, 0x3f, 0xe9, 0xa0, 0xdf, 0x00, 0x34, 0xc0, 0x17, 0xc4, 0x8a, 0x19, 0x8e, 0x2c,
	0x79, 0x95, 0x93, 0xde, 0x7e, 0xad, 0x70, 0x4c, 0x9f, 0xe1, 0x88, 0xcf, 0xf8, 0x60, 0x14, 0xa0,
	0x0f, 0x92, 0x3b, 0xfe, 0x94, 0x50, 0x16, 0x70, 0x4b, 0x02, 0x9a, 0x50, 0xfd, 0x0c, 0xca, 0xa2,
	0xc2, 0x90, 0x3b, 0xc7, 0x0f, 0xaf, 0xfe, 0x18, 0x91, 0xd1, 0x83, 0x29, 0xd8, 0xf2, 0x26, 0x58,
	0x28, 0xfe, 0x97, 0xe0, 0xcf, 0x35, 0xb8, 0x33, 0x67, 0x84, 0xf9, 0xcf, 0x25, 0xf2, 0x33, 0x81,
	0x9e, 0x7e, 0x26, 0x30, 0xa0, 0x61, 0x8f, 0xa2, 0x48, 0xaa, 0x65, 0xa2, 0x91, 0x3a, 0x07, 0x72,
	0x8d, 0xe4, 0xb5, 0x91, 0x10, 0xe5, 0xb4, 0x21, 0xa9, 0x8c, 0xbf, 0xd0, 0xd2, 0xe8, 0xc8, 0x18,
	0x61, 0xb2, 0x27, 0x6a, 0x99, 0x3d, 0x91, 0x67, 0xc8, 0x54, 0x98, 0x7a, 0x4f, 0x8f, 0xd5, 0x68,
	0xdb, 0xb0, 0x9e, 0xa0, 0x2c, 0x46, 0xad, 0x90, 0xbc, 0x62, 0x96, 0x94, 0x50, 0x52, 0x0f, 0x07,
	0x92, 0xf0, 0x98, 0x1e, 0x90, 0x57, 0x72, 0x28, 0x3e, 0x88, 0x78, 0x76, 0x51, 0x13, 0x93, 0x1d,
	0xc3, 0x85, 0x0f, 0xe6, 0x3b, 0xa3, 0x9c, 0x5e, 0xa1, 0xdb, 0x83, 0x8c, 0x52, 0xf5, 0xdc, 0xbb,
	0xed, 0xbf, 0xe9, 0xf0, 0xe1, 0x0d, 0x46, 0x2a, 0xe6, 0xf9, 0x47, 0x50, 0x17, 0xff, 0x26, 0x72,
	0x8e, 0xff, 0xf8, 0xc6, 0x4e, 0xa6, 0x62, 0x40, 0xfc, 0xbd, 0x50, 0xa6, 0x38, 0x02, 0x61, 0xd6,
	0x44, 0x62, 0xe9, 0x35, 0x25, 0x72, 0x19, 0x13, 0x89, 0xc2, 0x3e, 0x4a, 0x62, 0xf9, 0x35, 0x25,
	0x72, 0x19, 0xb2, 0x6d, 0xfc, 0x5a, 0x87, 0x77, 0xae, 0xa4, 0x46, 0x1d, 0xa8, 0x0e, 0x15, 0x44,
	0x69, 0xb0, 0x40, 0xe4, 0xa5, 0xac, 0xe8, 0xc7, 0x70, 0x9b, 0xe1, 0xf8, 0x3c, 0xe7, 0x66, 0xdc,
	0xb3, 0x94, 0x3b, 0xae, 0x72, 0x64, 0xea, 0x65, 0xdc, 0xc7, 0xd1, 0x01, 0xac, 0x8e, 0x42, 0x5b,
	0xdd, 0x5c, 0x3a, 0x16, 0x27, 0x48, 0xbe, 0xb6, 0x3d, 0xbc, 0x76, 0x06, 0x66, 0x33, 0xc3, 0xcb,
	0x01, 0x31, 0xfa, 0x02, 0x56, 0xa6, 0xa5, 0x95, 0x6f, 0x2a, 0x6d, 0x39, 0x2f, 0xcb, 0x38, 0x4c,
	0x4b, 0xcc, 0x84, 0x84, 0xc7, 0x04, 0xf3, 0x98, 0xaa, 0x31, 0x6b, 0xa6, 0xec, 0x20, 0x04, 0x65,
	0x87, 0xc4, 0xc9, 0xc3, 0xbe, 0x68, 0x73, 0x98, 0x67, 0xab, 0x67, 0xd1, 0x9a, 0x29, 0xda, 0xc6,
	0xef, 0x4f, 0x7f, 0x38, 0xdb, 0x1d, 0x77, 0x9d, 0x37, 0x12, 0x2c, 0x17, 0xd3, 0x9f, 0xb7, 0xa4,
	0xe4, 0xc2, 0x27, 0x0c, 0xf1, 0x26, 0x3e, 0xf7, 0x84, 0x91, 0xfd, 0xbb, 0x25, 0x48, 0x8d, 0x3f,
	0xd5, 0xe0, 0xfd, 0x39, 0x6e, 0x21, 0x6f, 0xeb, 0xd4, 0x76, 0xfe, 0x86, 0xfc, 0x2b, 0x55, 0xbf,
	0x9e, 0x51, 0xbf, 0xb1, 0x2b, 0x7e, 0x3b, 0x99, 0xc4, 0x26, 0x21, 0x3b, 0x19, 0xf2, 0x54, 0x4e,
	0x9c, 0x82, 0x1f, 0x14, 0x8d, 0xb1, 0xf8, 0xd2, 0x34, 0x4b, 0xc6, 0xdb, 0xd6, 0xe1, 0x23, 0x3b,
	0xef, 0x66, 0xe3, 0x21, 0xaf, 0x5a, 0xee, 0x4e, 0x81, 0xac, 0x93, 0x83, 0x76, 0xe7, 0xb3, 0xee,
	0x41, 0xa7, 0xdd, 0xfc, 0x01, 0xba, 0x03, 0xb7, 0xa6, 0xd1, 0x9f, 0x1f, 0x1e, 0x37, 0x35, 0xd4,
	0x82, 0xb5, 0x69, 0x84, 0xb9, 0xd7, 0x6b, 0x37, 0xf5, 0x47, 0x87, 0x50, 0x4b, 0x9f, 0xc5, 0x39,
	0x7f, 0xda, 0xc9, 0x09, 0x46, 0xb0, 0x3c, 0x41, 0x04, 0x9e, 0xef, 0x34, 0x35, 0x74, 0x1b, 0x56,
	0x27, 0xb0, 0xce, 0x2b, 0xdb, 0x63, 0xc4, 0x69, 0xea, 0x8f, 0x22, 0x68, 0x4e, 0x7f, 0x58, 0x40,
	0xef, 0xc2, 0xbd, 0x69, 0x58, 0x4e, 0xfc, 0x06, 0x3c, 0xb8, 0x84, 0x6f, 0x1f, 0x7e, 0x7d, 0xf0,
	0xfc, 0x70, 0xa7, 0xdd, 0x3d, 0xd8, 0x6f, 0x6a, 0xe8, 0x01, 0xb4, 0x2e, 0x51, 0xa8, 0x73, 0x6d,
	0x53, 0x7f, 0x34, 0x00, 0x98, 0x5c, 0x2c, 0xf1, 0xc5, 0x4e, 0x7a, 0xb9, 0x71, 0xd6, 0xa0, 0x99,
	0xc1, 0xec, 0x3d, 0xef, 0xee, 0x7d, 0xd9, 0xd4, 0xa6, 0xa0, 0x47, 0x66, 0xa7, 0xdf, 0x6f, 0xea,
	0x53, 0xd0, 0xfe, 0xf3, 0x6e, 0xbb, 0xd3, 0x2c, 0x3d, 0xfa, 0x27, 0x0d, 0xaa, 0xc9, 0x69, 0x11,
	0xad, 0x03, 0x4a, 0xda, 0xb9, 0x61, 0x5a, 0xb0, 0x96, 0xc2, 0x7b, 0x3b, 0xdd, 0x03, 0xab, 0xdf,
	0x3d, 0xd8, 0xef, 0x98, 0xd2, 0x0e, 0x29, 0xe6, 0x79, 0x67, 0xa7, 0x6d, 0xed, 0x9f, 0x74, 0x8f,
	0x77, 0xcc, 0xa6, 0xce, 0xb5, 0x99, 0x62, 0xbe, 0xec, 0x7c, 0xb3, 0x7b, 0xb8, 0x63, 0xb6, 0x9b,
	0x25, 0x3e, 0x8b, 0x14, 0xbc, 0xbb, 0xd3, 0xef, 0x77, 0xfb, 0xc7, 0xcd, 0x72, 0x0e, 0xda, 0x36,
	0x4f, 0x7a, 0xbd, 0x8e, 0xd9, 0xac, 0xa0, 0x7b, 0xb0, 0x9e, 0x42, 0xcd, 0xcf, 0xbf, 0x39, 0xfe,
	0xbc, 0x97, 0x88, 0x5f, 0x78, 0xf4, 0x0c, 0xd6, 0x67, 0xdf, 0xc0, 0xa0, 0x2a, 0x94, 0x7b, 0xdd,
	0x7e, 0xbf, 0xf9, 0x03, 0x54, 0x87, 0xc5, 0xa3, 0x8e, 0xf9, 0x59, 0x67, 0x8f, 0x7b, 0x4c, 0x0d,
	0x2a, 0xfb, 0x66, 0x67, 0xe7, 0xb8, 0xa9, 0xef, 0x7e, 0x06, 0x2d, 0x9b, 0x06, 0x5b, 0x63, 0x6f,
	0x4c, 0x47, 0xdc, 0x6f, 0x03, 0xea, 0x10, 0x5f, 0xfe, 0xb3, 0xfe, 0xf6, 0x91, 0x4b, 0x7d, 0x1c,
	0xba, 0x5b, 0x4f, 0xb7, 0x19, 0xdb, 0xb2, 0x69, 0xf0, 0x58, 0x80, 0x6d, 0xea, 0x3f, 0xc6, 0xc3,
	0xe1, 0xe3, 0xdc, 0xdf, 0xed, 0xd3, 0x05, 0x81, 0xfb, 0xc9, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff,
	0x61, 0x8c, 0x40, 0x0b, 0xd3, 0x2d, 0x00, 0x00,
}
