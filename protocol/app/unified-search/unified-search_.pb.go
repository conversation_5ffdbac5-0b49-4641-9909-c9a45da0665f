// Code generated by protoc-gen-go. DO NOT EDIT.
// source: unified-search_.proto

package unified_search // import "golang.52tt.com/protocol/app/unified-search"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import ancient_search "golang.52tt.com/protocol/app/ancient-search"
import channel "golang.52tt.com/protocol/app/channel"
import channel_play "golang.52tt.com/protocol/app/channel-play"
import game_card "golang.52tt.com/protocol/app/game-card"
import guild "golang.52tt.com/protocol/app/guild"
import super_channel "golang.52tt.com/protocol/app/super-channel"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UnifiedSearchType int32

const (
	UnifiedSearchType_UNIFIED_SEARCH_TYPE_UNKNOWN UnifiedSearchType = 0
	UnifiedSearchType_UNIFIED_SEARCH_TYPE_USER    UnifiedSearchType = 1
	UnifiedSearchType_UNIFIED_SEARCH_TYPE_GUILD   UnifiedSearchType = 2
	UnifiedSearchType_UNIFIED_SEARCH_TYPE_CHANNEL UnifiedSearchType = 3
	UnifiedSearchType_UNIFIED_SEARCH_TYPE_CPL     UnifiedSearchType = 4
	UnifiedSearchType_UNIFIED_SEARCH_TYPE_AREA    UnifiedSearchType = 5
	UnifiedSearchType_UNIFIED_SEARCH_TYPE_POST    UnifiedSearchType = 6
	UnifiedSearchType_UNIFIED_SEARCH_TYPE_ALL     UnifiedSearchType = 9999
)

var UnifiedSearchType_name = map[int32]string{
	0:    "UNIFIED_SEARCH_TYPE_UNKNOWN",
	1:    "UNIFIED_SEARCH_TYPE_USER",
	2:    "UNIFIED_SEARCH_TYPE_GUILD",
	3:    "UNIFIED_SEARCH_TYPE_CHANNEL",
	4:    "UNIFIED_SEARCH_TYPE_CPL",
	5:    "UNIFIED_SEARCH_TYPE_AREA",
	6:    "UNIFIED_SEARCH_TYPE_POST",
	9999: "UNIFIED_SEARCH_TYPE_ALL",
}
var UnifiedSearchType_value = map[string]int32{
	"UNIFIED_SEARCH_TYPE_UNKNOWN": 0,
	"UNIFIED_SEARCH_TYPE_USER":    1,
	"UNIFIED_SEARCH_TYPE_GUILD":   2,
	"UNIFIED_SEARCH_TYPE_CHANNEL": 3,
	"UNIFIED_SEARCH_TYPE_CPL":     4,
	"UNIFIED_SEARCH_TYPE_AREA":    5,
	"UNIFIED_SEARCH_TYPE_POST":    6,
	"UNIFIED_SEARCH_TYPE_ALL":     9999,
}

func (x UnifiedSearchType) String() string {
	return proto.EnumName(UnifiedSearchType_name, int32(x))
}
func (UnifiedSearchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{0}
}

type SiftType int32

const (
	SiftType_UNDEFINED_SIFT_TYPE SiftType = 0
	SiftType_SIFT_TYPE_RECOMMEND SiftType = 1
	SiftType_SIFT_TYPE_LATEST    SiftType = 2
)

var SiftType_name = map[int32]string{
	0: "UNDEFINED_SIFT_TYPE",
	1: "SIFT_TYPE_RECOMMEND",
	2: "SIFT_TYPE_LATEST",
}
var SiftType_value = map[string]int32{
	"UNDEFINED_SIFT_TYPE": 0,
	"SIFT_TYPE_RECOMMEND": 1,
	"SIFT_TYPE_LATEST":    2,
}

func (x SiftType) String() string {
	return proto.EnumName(SiftType_name, int32(x))
}
func (SiftType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{1}
}

type WordType int32

const (
	WordType_Default            WordType = 0
	WordType_AssociateLabel     WordType = 1
	WordType_AssociateUserName  WordType = 2
	WordType_GuideRegLabel      WordType = 3
	WordType_GuideRealtimeLabel WordType = 4
	WordType_GuideHistoryLabel  WordType = 5
	WordType_HistoryWord        WordType = 6
	WordType_PreferWord         WordType = 7
	WordType_AssociateTopic     WordType = 8
	WordType_GuideTopicLabel    WordType = 9
)

var WordType_name = map[int32]string{
	0: "Default",
	1: "AssociateLabel",
	2: "AssociateUserName",
	3: "GuideRegLabel",
	4: "GuideRealtimeLabel",
	5: "GuideHistoryLabel",
	6: "HistoryWord",
	7: "PreferWord",
	8: "AssociateTopic",
	9: "GuideTopicLabel",
}
var WordType_value = map[string]int32{
	"Default":            0,
	"AssociateLabel":     1,
	"AssociateUserName":  2,
	"GuideRegLabel":      3,
	"GuideRealtimeLabel": 4,
	"GuideHistoryLabel":  5,
	"HistoryWord":        6,
	"PreferWord":         7,
	"AssociateTopic":     8,
	"GuideTopicLabel":    9,
}

func (x WordType) String() string {
	return proto.EnumName(WordType_name, int32(x))
}
func (WordType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{2}
}

// 房间类型
type CommonSearchChannelBrief_ChannelStyleType int32

const (
	CommonSearchChannelBrief_UNIFIED_CHANNEL_TYPE_TYPE CommonSearchChannelBrief_ChannelStyleType = 0
	CommonSearchChannelBrief_CHANNEL_TYPE_COMMON       CommonSearchChannelBrief_ChannelStyleType = 1
	CommonSearchChannelBrief_CHANNEL_TYPE_TOPIC        CommonSearchChannelBrief_ChannelStyleType = 2
)

var CommonSearchChannelBrief_ChannelStyleType_name = map[int32]string{
	0: "UNIFIED_CHANNEL_TYPE_TYPE",
	1: "CHANNEL_TYPE_COMMON",
	2: "CHANNEL_TYPE_TOPIC",
}
var CommonSearchChannelBrief_ChannelStyleType_value = map[string]int32{
	"UNIFIED_CHANNEL_TYPE_TYPE": 0,
	"CHANNEL_TYPE_COMMON":       1,
	"CHANNEL_TYPE_TOPIC":        2,
}

func (x CommonSearchChannelBrief_ChannelStyleType) String() string {
	return proto.EnumName(CommonSearchChannelBrief_ChannelStyleType_name, int32(x))
}
func (CommonSearchChannelBrief_ChannelStyleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{20, 0}
}

type SearchGuideWordsReq_AreaType int32

const (
	SearchGuideWordsReq_UNIFIED_AREA_TYPE_TYPE  SearchGuideWordsReq_AreaType = 0
	SearchGuideWordsReq_AREA_TYPE_MATCH         SearchGuideWordsReq_AreaType = 1
	SearchGuideWordsReq_AREA_TYPE_MUSIC         SearchGuideWordsReq_AreaType = 2
	SearchGuideWordsReq_AREA_TYPE_ENTERTAINMENT SearchGuideWordsReq_AreaType = 3
	SearchGuideWordsReq_AREA_TYPE_IM            SearchGuideWordsReq_AreaType = 4
	SearchGuideWordsReq_AREA_TYPE_MAIN_PAGE     SearchGuideWordsReq_AreaType = 5
	SearchGuideWordsReq_AREA_TYPE_MAIN_GAME     SearchGuideWordsReq_AreaType = 6
)

var SearchGuideWordsReq_AreaType_name = map[int32]string{
	0: "UNIFIED_AREA_TYPE_TYPE",
	1: "AREA_TYPE_MATCH",
	2: "AREA_TYPE_MUSIC",
	3: "AREA_TYPE_ENTERTAINMENT",
	4: "AREA_TYPE_IM",
	5: "AREA_TYPE_MAIN_PAGE",
	6: "AREA_TYPE_MAIN_GAME",
}
var SearchGuideWordsReq_AreaType_value = map[string]int32{
	"UNIFIED_AREA_TYPE_TYPE":  0,
	"AREA_TYPE_MATCH":         1,
	"AREA_TYPE_MUSIC":         2,
	"AREA_TYPE_ENTERTAINMENT": 3,
	"AREA_TYPE_IM":            4,
	"AREA_TYPE_MAIN_PAGE":     5,
	"AREA_TYPE_MAIN_GAME":     6,
}

func (x SearchGuideWordsReq_AreaType) String() string {
	return proto.EnumName(SearchGuideWordsReq_AreaType_name, int32(x))
}
func (SearchGuideWordsReq_AreaType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{25, 0}
}

type UnifiedSearchReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Keyword              string       `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Page                 uint32       `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32       `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	TypeList             []uint32     `protobuf:"varint,5,rep,packed,name=type_list,json=typeList,proto3" json:"type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UnifiedSearchReq) Reset()         { *m = UnifiedSearchReq{} }
func (m *UnifiedSearchReq) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchReq) ProtoMessage()    {}
func (*UnifiedSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{0}
}
func (m *UnifiedSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchReq.Unmarshal(m, b)
}
func (m *UnifiedSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchReq.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchReq.Merge(dst, src)
}
func (m *UnifiedSearchReq) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchReq.Size(m)
}
func (m *UnifiedSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchReq proto.InternalMessageInfo

func (m *UnifiedSearchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UnifiedSearchReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *UnifiedSearchReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *UnifiedSearchReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *UnifiedSearchReq) GetTypeList() []uint32 {
	if m != nil {
		return m.TypeList
	}
	return nil
}

type UnifiedSearchResp struct {
	BaseResp             *app.BaseResp                   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Keyword              string                          `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	ForceLink            string                          `protobuf:"bytes,3,opt,name=force_link,json=forceLink,proto3" json:"force_link,omitempty"`
	ResultList           map[uint32]*UnifiedSearchResult `protobuf:"bytes,4,rep,name=result_list,json=resultList,proto3" json:"result_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *UnifiedSearchResp) Reset()         { *m = UnifiedSearchResp{} }
func (m *UnifiedSearchResp) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchResp) ProtoMessage()    {}
func (*UnifiedSearchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{1}
}
func (m *UnifiedSearchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchResp.Unmarshal(m, b)
}
func (m *UnifiedSearchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchResp.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchResp.Merge(dst, src)
}
func (m *UnifiedSearchResp) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchResp.Size(m)
}
func (m *UnifiedSearchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchResp proto.InternalMessageInfo

func (m *UnifiedSearchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UnifiedSearchResp) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *UnifiedSearchResp) GetForceLink() string {
	if m != nil {
		return m.ForceLink
	}
	return ""
}

func (m *UnifiedSearchResp) GetResultList() map[uint32]*UnifiedSearchResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

type UnifiedSearchResult struct {
	// Types that are valid to be assigned to Result:
	//	*UnifiedSearchResult_UserResult
	//	*UnifiedSearchResult_GuildResult
	//	*UnifiedSearchResult_ChannelResult
	//	*UnifiedSearchResult_CplResult
	//	*UnifiedSearchResult_AreaResult
	//	*UnifiedSearchResult_PostResult
	Result               isUnifiedSearchResult_Result `protobuf_oneof:"result"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UnifiedSearchResult) Reset()         { *m = UnifiedSearchResult{} }
func (m *UnifiedSearchResult) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchResult) ProtoMessage()    {}
func (*UnifiedSearchResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{2}
}
func (m *UnifiedSearchResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchResult.Unmarshal(m, b)
}
func (m *UnifiedSearchResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchResult.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchResult.Merge(dst, src)
}
func (m *UnifiedSearchResult) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchResult.Size(m)
}
func (m *UnifiedSearchResult) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchResult.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchResult proto.InternalMessageInfo

type isUnifiedSearchResult_Result interface {
	isUnifiedSearchResult_Result()
}

type UnifiedSearchResult_UserResult struct {
	UserResult *UnifiedSearchUserResult `protobuf:"bytes,1,opt,name=user_result,json=userResult,proto3,oneof"`
}

type UnifiedSearchResult_GuildResult struct {
	GuildResult *UnifiedSearchGuildResult `protobuf:"bytes,2,opt,name=guild_result,json=guildResult,proto3,oneof"`
}

type UnifiedSearchResult_ChannelResult struct {
	ChannelResult *UnifiedSearchChannelResult `protobuf:"bytes,3,opt,name=channel_result,json=channelResult,proto3,oneof"`
}

type UnifiedSearchResult_CplResult struct {
	CplResult *UnifiedSearchCPLResult `protobuf:"bytes,4,opt,name=cpl_result,json=cplResult,proto3,oneof"`
}

type UnifiedSearchResult_AreaResult struct {
	AreaResult *CommonSearchAreaResult `protobuf:"bytes,5,opt,name=area_result,json=areaResult,proto3,oneof"`
}

type UnifiedSearchResult_PostResult struct {
	PostResult *CommonSearchPostResult `protobuf:"bytes,6,opt,name=post_result,json=postResult,proto3,oneof"`
}

func (*UnifiedSearchResult_UserResult) isUnifiedSearchResult_Result() {}

func (*UnifiedSearchResult_GuildResult) isUnifiedSearchResult_Result() {}

func (*UnifiedSearchResult_ChannelResult) isUnifiedSearchResult_Result() {}

func (*UnifiedSearchResult_CplResult) isUnifiedSearchResult_Result() {}

func (*UnifiedSearchResult_AreaResult) isUnifiedSearchResult_Result() {}

func (*UnifiedSearchResult_PostResult) isUnifiedSearchResult_Result() {}

func (m *UnifiedSearchResult) GetResult() isUnifiedSearchResult_Result {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *UnifiedSearchResult) GetUserResult() *UnifiedSearchUserResult {
	if x, ok := m.GetResult().(*UnifiedSearchResult_UserResult); ok {
		return x.UserResult
	}
	return nil
}

func (m *UnifiedSearchResult) GetGuildResult() *UnifiedSearchGuildResult {
	if x, ok := m.GetResult().(*UnifiedSearchResult_GuildResult); ok {
		return x.GuildResult
	}
	return nil
}

func (m *UnifiedSearchResult) GetChannelResult() *UnifiedSearchChannelResult {
	if x, ok := m.GetResult().(*UnifiedSearchResult_ChannelResult); ok {
		return x.ChannelResult
	}
	return nil
}

func (m *UnifiedSearchResult) GetCplResult() *UnifiedSearchCPLResult {
	if x, ok := m.GetResult().(*UnifiedSearchResult_CplResult); ok {
		return x.CplResult
	}
	return nil
}

func (m *UnifiedSearchResult) GetAreaResult() *CommonSearchAreaResult {
	if x, ok := m.GetResult().(*UnifiedSearchResult_AreaResult); ok {
		return x.AreaResult
	}
	return nil
}

func (m *UnifiedSearchResult) GetPostResult() *CommonSearchPostResult {
	if x, ok := m.GetResult().(*UnifiedSearchResult_PostResult); ok {
		return x.PostResult
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*UnifiedSearchResult) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _UnifiedSearchResult_OneofMarshaler, _UnifiedSearchResult_OneofUnmarshaler, _UnifiedSearchResult_OneofSizer, []interface{}{
		(*UnifiedSearchResult_UserResult)(nil),
		(*UnifiedSearchResult_GuildResult)(nil),
		(*UnifiedSearchResult_ChannelResult)(nil),
		(*UnifiedSearchResult_CplResult)(nil),
		(*UnifiedSearchResult_AreaResult)(nil),
		(*UnifiedSearchResult_PostResult)(nil),
	}
}

func _UnifiedSearchResult_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*UnifiedSearchResult)
	// result
	switch x := m.Result.(type) {
	case *UnifiedSearchResult_UserResult:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserResult); err != nil {
			return err
		}
	case *UnifiedSearchResult_GuildResult:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GuildResult); err != nil {
			return err
		}
	case *UnifiedSearchResult_ChannelResult:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ChannelResult); err != nil {
			return err
		}
	case *UnifiedSearchResult_CplResult:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.CplResult); err != nil {
			return err
		}
	case *UnifiedSearchResult_AreaResult:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.AreaResult); err != nil {
			return err
		}
	case *UnifiedSearchResult_PostResult:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PostResult); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("UnifiedSearchResult.Result has unexpected type %T", x)
	}
	return nil
}

func _UnifiedSearchResult_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*UnifiedSearchResult)
	switch tag {
	case 1: // result.user_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UnifiedSearchUserResult)
		err := b.DecodeMessage(msg)
		m.Result = &UnifiedSearchResult_UserResult{msg}
		return true, err
	case 2: // result.guild_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UnifiedSearchGuildResult)
		err := b.DecodeMessage(msg)
		m.Result = &UnifiedSearchResult_GuildResult{msg}
		return true, err
	case 3: // result.channel_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UnifiedSearchChannelResult)
		err := b.DecodeMessage(msg)
		m.Result = &UnifiedSearchResult_ChannelResult{msg}
		return true, err
	case 4: // result.cpl_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UnifiedSearchCPLResult)
		err := b.DecodeMessage(msg)
		m.Result = &UnifiedSearchResult_CplResult{msg}
		return true, err
	case 5: // result.area_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(CommonSearchAreaResult)
		err := b.DecodeMessage(msg)
		m.Result = &UnifiedSearchResult_AreaResult{msg}
		return true, err
	case 6: // result.post_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(CommonSearchPostResult)
		err := b.DecodeMessage(msg)
		m.Result = &UnifiedSearchResult_PostResult{msg}
		return true, err
	default:
		return false, nil
	}
}

func _UnifiedSearchResult_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*UnifiedSearchResult)
	// result
	switch x := m.Result.(type) {
	case *UnifiedSearchResult_UserResult:
		s := proto.Size(x.UserResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *UnifiedSearchResult_GuildResult:
		s := proto.Size(x.GuildResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *UnifiedSearchResult_ChannelResult:
		s := proto.Size(x.ChannelResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *UnifiedSearchResult_CplResult:
		s := proto.Size(x.CplResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *UnifiedSearchResult_AreaResult:
		s := proto.Size(x.AreaResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *UnifiedSearchResult_PostResult:
		s := proto.Size(x.PostResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 统一搜索结果下的子消息体
type UnifiedSearchGuildBrief struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildDisplayId       uint32   `protobuf:"varint,2,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	NeedVerify           uint32   `protobuf:"varint,4,opt,name=need_verify,json=needVerify,proto3" json:"need_verify,omitempty"`
	MemCount             uint32   `protobuf:"varint,5,opt,name=mem_count,json=memCount,proto3" json:"mem_count,omitempty"`
	GiftPkgCount         uint32   `protobuf:"varint,6,opt,name=gift_pkg_count,json=giftPkgCount,proto3" json:"gift_pkg_count,omitempty"`
	GuildManifesto       string   `protobuf:"bytes,7,opt,name=guild_manifesto,json=guildManifesto,proto3" json:"guild_manifesto,omitempty"`
	GameGiftPkgCount     uint32   `protobuf:"varint,8,opt,name=game_gift_pkg_count,json=gameGiftPkgCount,proto3" json:"game_gift_pkg_count,omitempty"`
	GuildStarLevel       uint32   `protobuf:"varint,9,opt,name=guild_star_level,json=guildStarLevel,proto3" json:"guild_star_level,omitempty"`
	HomeChannelId        uint32   `protobuf:"varint,10,opt,name=home_channel_id,json=homeChannelId,proto3" json:"home_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedSearchGuildBrief) Reset()         { *m = UnifiedSearchGuildBrief{} }
func (m *UnifiedSearchGuildBrief) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchGuildBrief) ProtoMessage()    {}
func (*UnifiedSearchGuildBrief) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{3}
}
func (m *UnifiedSearchGuildBrief) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchGuildBrief.Unmarshal(m, b)
}
func (m *UnifiedSearchGuildBrief) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchGuildBrief.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchGuildBrief) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchGuildBrief.Merge(dst, src)
}
func (m *UnifiedSearchGuildBrief) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchGuildBrief.Size(m)
}
func (m *UnifiedSearchGuildBrief) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchGuildBrief.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchGuildBrief proto.InternalMessageInfo

func (m *UnifiedSearchGuildBrief) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UnifiedSearchGuildBrief) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *UnifiedSearchGuildBrief) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UnifiedSearchGuildBrief) GetNeedVerify() uint32 {
	if m != nil {
		return m.NeedVerify
	}
	return 0
}

func (m *UnifiedSearchGuildBrief) GetMemCount() uint32 {
	if m != nil {
		return m.MemCount
	}
	return 0
}

func (m *UnifiedSearchGuildBrief) GetGiftPkgCount() uint32 {
	if m != nil {
		return m.GiftPkgCount
	}
	return 0
}

func (m *UnifiedSearchGuildBrief) GetGuildManifesto() string {
	if m != nil {
		return m.GuildManifesto
	}
	return ""
}

func (m *UnifiedSearchGuildBrief) GetGameGiftPkgCount() uint32 {
	if m != nil {
		return m.GameGiftPkgCount
	}
	return 0
}

func (m *UnifiedSearchGuildBrief) GetGuildStarLevel() uint32 {
	if m != nil {
		return m.GuildStarLevel
	}
	return 0
}

func (m *UnifiedSearchGuildBrief) GetHomeChannelId() uint32 {
	if m != nil {
		return m.HomeChannelId
	}
	return 0
}

// 定义了用户搜索结果中的用户信息的数据类型
type UnifiedSearchUserBrief struct {
	// 用户头像md5值
	FaceMd5 string `protobuf:"bytes,1,opt,name=face_md5,json=faceMd5,proto3" json:"face_md5,omitempty"`
	Sex     uint32 `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	// 用户签名
	Signature string `protobuf:"bytes,3,opt,name=signature,proto3" json:"signature,omitempty"`
	// 用户昵称
	NickName string `protobuf:"bytes,4,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	// 用户账号
	Account string `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	// 用户ID
	Uid uint32 `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	// 用户账号别名
	AccountAlias string `protobuf:"bytes,7,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias,omitempty"`
	// 官方认证标题
	CertifyTitle string `protobuf:"bytes,8,opt,name=certify_title,json=certifyTitle,proto3" json:"certify_title,omitempty"`
	// 大V类型
	CertifyStyle string `protobuf:"bytes,9,opt,name=certify_style,json=certifyStyle,proto3" json:"certify_style,omitempty"`
	// 直播房间ID
	ChannelId uint32 `protobuf:"varint,10,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// PK状态，枚举类型
	PkStatus uint32 `protobuf:"varint,11,opt,name=pk_status,json=pkStatus,proto3" json:"pk_status,omitempty"`
	// 大V特别图标动效
	CertifySpecialEffectIcon string `protobuf:"bytes,12,opt,name=certify_special_effect_icon,json=certifySpecialEffectIcon,proto3" json:"certify_special_effect_icon,omitempty"`
	// 大V认证类型
	VcertifyStyle        string   `protobuf:"bytes,13,opt,name=vcertify_style,json=vcertifyStyle,proto3" json:"vcertify_style,omitempty"`
	IsFollow             bool     `protobuf:"varint,14,opt,name=is_follow,json=isFollow,proto3" json:"is_follow,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedSearchUserBrief) Reset()         { *m = UnifiedSearchUserBrief{} }
func (m *UnifiedSearchUserBrief) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchUserBrief) ProtoMessage()    {}
func (*UnifiedSearchUserBrief) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{4}
}
func (m *UnifiedSearchUserBrief) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchUserBrief.Unmarshal(m, b)
}
func (m *UnifiedSearchUserBrief) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchUserBrief.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchUserBrief) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchUserBrief.Merge(dst, src)
}
func (m *UnifiedSearchUserBrief) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchUserBrief.Size(m)
}
func (m *UnifiedSearchUserBrief) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchUserBrief.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchUserBrief proto.InternalMessageInfo

func (m *UnifiedSearchUserBrief) GetFaceMd5() string {
	if m != nil {
		return m.FaceMd5
	}
	return ""
}

func (m *UnifiedSearchUserBrief) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UnifiedSearchUserBrief) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *UnifiedSearchUserBrief) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *UnifiedSearchUserBrief) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UnifiedSearchUserBrief) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UnifiedSearchUserBrief) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UnifiedSearchUserBrief) GetCertifyTitle() string {
	if m != nil {
		return m.CertifyTitle
	}
	return ""
}

func (m *UnifiedSearchUserBrief) GetCertifyStyle() string {
	if m != nil {
		return m.CertifyStyle
	}
	return ""
}

func (m *UnifiedSearchUserBrief) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UnifiedSearchUserBrief) GetPkStatus() uint32 {
	if m != nil {
		return m.PkStatus
	}
	return 0
}

func (m *UnifiedSearchUserBrief) GetCertifySpecialEffectIcon() string {
	if m != nil {
		return m.CertifySpecialEffectIcon
	}
	return ""
}

func (m *UnifiedSearchUserBrief) GetVcertifyStyle() string {
	if m != nil {
		return m.VcertifyStyle
	}
	return ""
}

func (m *UnifiedSearchUserBrief) GetIsFollow() bool {
	if m != nil {
		return m.IsFollow
	}
	return false
}

// 定义了房间搜索结果中的房间信息数据类型
type UnifiedSearchChannelBrief struct {
	// 房间详情信息
	ChannelDetailInfo *channel.ChannelDetailInfo `protobuf:"bytes,1,opt,name=channel_detail_info,json=channelDetailInfo,proto3" json:"channel_detail_info,omitempty"`
	// 房间标签
	TagInfo              *channel.SCTagInfo             `protobuf:"bytes,2,opt,name=tag_info,json=tagInfo,proto3" json:"tag_info,omitempty"`
	TopicChannel         *channel_play.TopicChannelItem `protobuf:"bytes,3,opt,name=topic_channel,json=topicChannel,proto3" json:"topic_channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *UnifiedSearchChannelBrief) Reset()         { *m = UnifiedSearchChannelBrief{} }
func (m *UnifiedSearchChannelBrief) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchChannelBrief) ProtoMessage()    {}
func (*UnifiedSearchChannelBrief) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{5}
}
func (m *UnifiedSearchChannelBrief) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchChannelBrief.Unmarshal(m, b)
}
func (m *UnifiedSearchChannelBrief) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchChannelBrief.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchChannelBrief) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchChannelBrief.Merge(dst, src)
}
func (m *UnifiedSearchChannelBrief) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchChannelBrief.Size(m)
}
func (m *UnifiedSearchChannelBrief) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchChannelBrief.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchChannelBrief proto.InternalMessageInfo

func (m *UnifiedSearchChannelBrief) GetChannelDetailInfo() *channel.ChannelDetailInfo {
	if m != nil {
		return m.ChannelDetailInfo
	}
	return nil
}

func (m *UnifiedSearchChannelBrief) GetTagInfo() *channel.SCTagInfo {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

func (m *UnifiedSearchChannelBrief) GetTopicChannel() *channel_play.TopicChannelItem {
	if m != nil {
		return m.TopicChannel
	}
	return nil
}

// 定义了活动搜索结果的数据类型
type UnifiedSearchActivityBrief struct {
	// 活动标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 活动图标
	Icon string `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	// 活动描述
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// 跳转链接
	JumpUrl              string   `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedSearchActivityBrief) Reset()         { *m = UnifiedSearchActivityBrief{} }
func (m *UnifiedSearchActivityBrief) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchActivityBrief) ProtoMessage()    {}
func (*UnifiedSearchActivityBrief) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{6}
}
func (m *UnifiedSearchActivityBrief) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchActivityBrief.Unmarshal(m, b)
}
func (m *UnifiedSearchActivityBrief) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchActivityBrief.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchActivityBrief) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchActivityBrief.Merge(dst, src)
}
func (m *UnifiedSearchActivityBrief) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchActivityBrief.Size(m)
}
func (m *UnifiedSearchActivityBrief) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchActivityBrief.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchActivityBrief proto.InternalMessageInfo

func (m *UnifiedSearchActivityBrief) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UnifiedSearchActivityBrief) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *UnifiedSearchActivityBrief) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UnifiedSearchActivityBrief) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

// 定义了专区搜索结果的数据类型
type UnifiedSearchAreaBrief struct {
	// 专区标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 专区图标
	Icon string `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	// 专区描述
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// 跳转链接
	JumpUrl              string   `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnifiedSearchAreaBrief) Reset()         { *m = UnifiedSearchAreaBrief{} }
func (m *UnifiedSearchAreaBrief) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchAreaBrief) ProtoMessage()    {}
func (*UnifiedSearchAreaBrief) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{7}
}
func (m *UnifiedSearchAreaBrief) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchAreaBrief.Unmarshal(m, b)
}
func (m *UnifiedSearchAreaBrief) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchAreaBrief.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchAreaBrief) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchAreaBrief.Merge(dst, src)
}
func (m *UnifiedSearchAreaBrief) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchAreaBrief.Size(m)
}
func (m *UnifiedSearchAreaBrief) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchAreaBrief.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchAreaBrief proto.InternalMessageInfo

func (m *UnifiedSearchAreaBrief) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UnifiedSearchAreaBrief) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *UnifiedSearchAreaBrief) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *UnifiedSearchAreaBrief) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

// 定义了用户、房间、活动搜索结果中通用的 CPL(Composite Page Layout) 数据类型
type UnifiedSearchCPLBrief struct {
	// 标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 背景
	Bg string `protobuf:"bytes,2,opt,name=bg,proto3" json:"bg,omitempty"`
	// 以下 4 个字段中只会出现一个，分别代表用户、房间、活动信息
	//
	// Types that are valid to be assigned to Value:
	//	*UnifiedSearchCPLBrief_UserBrief
	//	*UnifiedSearchCPLBrief_ChannelBrief
	//	*UnifiedSearchCPLBrief_ActivityBrief
	Value                isUnifiedSearchCPLBrief_Value `protobuf_oneof:"value"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *UnifiedSearchCPLBrief) Reset()         { *m = UnifiedSearchCPLBrief{} }
func (m *UnifiedSearchCPLBrief) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchCPLBrief) ProtoMessage()    {}
func (*UnifiedSearchCPLBrief) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{8}
}
func (m *UnifiedSearchCPLBrief) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchCPLBrief.Unmarshal(m, b)
}
func (m *UnifiedSearchCPLBrief) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchCPLBrief.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchCPLBrief) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchCPLBrief.Merge(dst, src)
}
func (m *UnifiedSearchCPLBrief) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchCPLBrief.Size(m)
}
func (m *UnifiedSearchCPLBrief) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchCPLBrief.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchCPLBrief proto.InternalMessageInfo

func (m *UnifiedSearchCPLBrief) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UnifiedSearchCPLBrief) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

type isUnifiedSearchCPLBrief_Value interface {
	isUnifiedSearchCPLBrief_Value()
}

type UnifiedSearchCPLBrief_UserBrief struct {
	UserBrief *UnifiedSearchUserBrief `protobuf:"bytes,3,opt,name=user_brief,json=userBrief,proto3,oneof"`
}

type UnifiedSearchCPLBrief_ChannelBrief struct {
	ChannelBrief *UnifiedSearchChannelBrief `protobuf:"bytes,4,opt,name=channel_brief,json=channelBrief,proto3,oneof"`
}

type UnifiedSearchCPLBrief_ActivityBrief struct {
	ActivityBrief *UnifiedSearchActivityBrief `protobuf:"bytes,5,opt,name=activity_brief,json=activityBrief,proto3,oneof"`
}

func (*UnifiedSearchCPLBrief_UserBrief) isUnifiedSearchCPLBrief_Value() {}

func (*UnifiedSearchCPLBrief_ChannelBrief) isUnifiedSearchCPLBrief_Value() {}

func (*UnifiedSearchCPLBrief_ActivityBrief) isUnifiedSearchCPLBrief_Value() {}

func (m *UnifiedSearchCPLBrief) GetValue() isUnifiedSearchCPLBrief_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *UnifiedSearchCPLBrief) GetUserBrief() *UnifiedSearchUserBrief {
	if x, ok := m.GetValue().(*UnifiedSearchCPLBrief_UserBrief); ok {
		return x.UserBrief
	}
	return nil
}

func (m *UnifiedSearchCPLBrief) GetChannelBrief() *UnifiedSearchChannelBrief {
	if x, ok := m.GetValue().(*UnifiedSearchCPLBrief_ChannelBrief); ok {
		return x.ChannelBrief
	}
	return nil
}

func (m *UnifiedSearchCPLBrief) GetActivityBrief() *UnifiedSearchActivityBrief {
	if x, ok := m.GetValue().(*UnifiedSearchCPLBrief_ActivityBrief); ok {
		return x.ActivityBrief
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*UnifiedSearchCPLBrief) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _UnifiedSearchCPLBrief_OneofMarshaler, _UnifiedSearchCPLBrief_OneofUnmarshaler, _UnifiedSearchCPLBrief_OneofSizer, []interface{}{
		(*UnifiedSearchCPLBrief_UserBrief)(nil),
		(*UnifiedSearchCPLBrief_ChannelBrief)(nil),
		(*UnifiedSearchCPLBrief_ActivityBrief)(nil),
	}
}

func _UnifiedSearchCPLBrief_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*UnifiedSearchCPLBrief)
	// value
	switch x := m.Value.(type) {
	case *UnifiedSearchCPLBrief_UserBrief:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserBrief); err != nil {
			return err
		}
	case *UnifiedSearchCPLBrief_ChannelBrief:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ChannelBrief); err != nil {
			return err
		}
	case *UnifiedSearchCPLBrief_ActivityBrief:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ActivityBrief); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("UnifiedSearchCPLBrief.Value has unexpected type %T", x)
	}
	return nil
}

func _UnifiedSearchCPLBrief_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*UnifiedSearchCPLBrief)
	switch tag {
	case 3: // value.user_brief
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UnifiedSearchUserBrief)
		err := b.DecodeMessage(msg)
		m.Value = &UnifiedSearchCPLBrief_UserBrief{msg}
		return true, err
	case 4: // value.channel_brief
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UnifiedSearchChannelBrief)
		err := b.DecodeMessage(msg)
		m.Value = &UnifiedSearchCPLBrief_ChannelBrief{msg}
		return true, err
	case 5: // value.activity_brief
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UnifiedSearchActivityBrief)
		err := b.DecodeMessage(msg)
		m.Value = &UnifiedSearchCPLBrief_ActivityBrief{msg}
		return true, err
	default:
		return false, nil
	}
}

func _UnifiedSearchCPLBrief_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*UnifiedSearchCPLBrief)
	// value
	switch x := m.Value.(type) {
	case *UnifiedSearchCPLBrief_UserBrief:
		s := proto.Size(x.UserBrief)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *UnifiedSearchCPLBrief_ChannelBrief:
		s := proto.Size(x.ChannelBrief)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *UnifiedSearchCPLBrief_ActivityBrief:
		s := proto.Size(x.ActivityBrief)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 定义了用户搜索结果的数据类型
type UnifiedSearchUserResult struct {
	// 是否为最后一页
	LastPage bool `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	// 用户信息列表
	UserBriefList        []*UnifiedSearchUserBrief `protobuf:"bytes,2,rep,name=user_brief_list,json=userBriefList,proto3" json:"user_brief_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UnifiedSearchUserResult) Reset()         { *m = UnifiedSearchUserResult{} }
func (m *UnifiedSearchUserResult) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchUserResult) ProtoMessage()    {}
func (*UnifiedSearchUserResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{9}
}
func (m *UnifiedSearchUserResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchUserResult.Unmarshal(m, b)
}
func (m *UnifiedSearchUserResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchUserResult.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchUserResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchUserResult.Merge(dst, src)
}
func (m *UnifiedSearchUserResult) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchUserResult.Size(m)
}
func (m *UnifiedSearchUserResult) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchUserResult.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchUserResult proto.InternalMessageInfo

func (m *UnifiedSearchUserResult) GetLastPage() bool {
	if m != nil {
		return m.LastPage
	}
	return false
}

func (m *UnifiedSearchUserResult) GetUserBriefList() []*UnifiedSearchUserBrief {
	if m != nil {
		return m.UserBriefList
	}
	return nil
}

// 定义了公会搜索结果的数据类型
type UnifiedSearchGuildResult struct {
	// 是否为最后一页
	LastPage bool `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	// 公会信息列表
	GuildBriefList       []*UnifiedSearchGuildBrief `protobuf:"bytes,2,rep,name=guild_brief_list,json=guildBriefList,proto3" json:"guild_brief_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UnifiedSearchGuildResult) Reset()         { *m = UnifiedSearchGuildResult{} }
func (m *UnifiedSearchGuildResult) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchGuildResult) ProtoMessage()    {}
func (*UnifiedSearchGuildResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{10}
}
func (m *UnifiedSearchGuildResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchGuildResult.Unmarshal(m, b)
}
func (m *UnifiedSearchGuildResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchGuildResult.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchGuildResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchGuildResult.Merge(dst, src)
}
func (m *UnifiedSearchGuildResult) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchGuildResult.Size(m)
}
func (m *UnifiedSearchGuildResult) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchGuildResult.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchGuildResult proto.InternalMessageInfo

func (m *UnifiedSearchGuildResult) GetLastPage() bool {
	if m != nil {
		return m.LastPage
	}
	return false
}

func (m *UnifiedSearchGuildResult) GetGuildBriefList() []*UnifiedSearchGuildBrief {
	if m != nil {
		return m.GuildBriefList
	}
	return nil
}

// 定义了房间搜索结果的数据类型
type UnifiedSearchChannelResult struct {
	// 是否为最后一页
	LastPage bool `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	// 房间信息列表
	ChannelBriefList     []*UnifiedSearchChannelBrief `protobuf:"bytes,2,rep,name=channel_brief_list,json=channelBriefList,proto3" json:"channel_brief_list,omitempty"`
	DisplayChannelMap    map[uint32]string            `protobuf:"bytes,3,rep,name=display_channel_map,json=displayChannelMap,proto3" json:"display_channel_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UnifiedSearchChannelResult) Reset()         { *m = UnifiedSearchChannelResult{} }
func (m *UnifiedSearchChannelResult) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchChannelResult) ProtoMessage()    {}
func (*UnifiedSearchChannelResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{11}
}
func (m *UnifiedSearchChannelResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchChannelResult.Unmarshal(m, b)
}
func (m *UnifiedSearchChannelResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchChannelResult.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchChannelResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchChannelResult.Merge(dst, src)
}
func (m *UnifiedSearchChannelResult) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchChannelResult.Size(m)
}
func (m *UnifiedSearchChannelResult) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchChannelResult.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchChannelResult proto.InternalMessageInfo

func (m *UnifiedSearchChannelResult) GetLastPage() bool {
	if m != nil {
		return m.LastPage
	}
	return false
}

func (m *UnifiedSearchChannelResult) GetChannelBriefList() []*UnifiedSearchChannelBrief {
	if m != nil {
		return m.ChannelBriefList
	}
	return nil
}

func (m *UnifiedSearchChannelResult) GetDisplayChannelMap() map[uint32]string {
	if m != nil {
		return m.DisplayChannelMap
	}
	return nil
}

// 定义了通用的搜索结果 CPL 数据类型
type UnifiedSearchCPLResult struct {
	// 配置ID
	ConfigId string `protobuf:"bytes,1,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	// CPL 列表
	CplBriefList         []*UnifiedSearchCPLBrief `protobuf:"bytes,2,rep,name=cpl_brief_list,json=cplBriefList,proto3" json:"cpl_brief_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UnifiedSearchCPLResult) Reset()         { *m = UnifiedSearchCPLResult{} }
func (m *UnifiedSearchCPLResult) String() string { return proto.CompactTextString(m) }
func (*UnifiedSearchCPLResult) ProtoMessage()    {}
func (*UnifiedSearchCPLResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{12}
}
func (m *UnifiedSearchCPLResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedSearchCPLResult.Unmarshal(m, b)
}
func (m *UnifiedSearchCPLResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedSearchCPLResult.Marshal(b, m, deterministic)
}
func (dst *UnifiedSearchCPLResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedSearchCPLResult.Merge(dst, src)
}
func (m *UnifiedSearchCPLResult) XXX_Size() int {
	return xxx_messageInfo_UnifiedSearchCPLResult.Size(m)
}
func (m *UnifiedSearchCPLResult) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedSearchCPLResult.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedSearchCPLResult proto.InternalMessageInfo

func (m *UnifiedSearchCPLResult) GetConfigId() string {
	if m != nil {
		return m.ConfigId
	}
	return ""
}

func (m *UnifiedSearchCPLResult) GetCplBriefList() []*UnifiedSearchCPLBrief {
	if m != nil {
		return m.CplBriefList
	}
	return nil
}

type CommonSearchReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Keyword              string       `protobuf:"bytes,2,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Offset               uint32       `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Type                 uint32       `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	Id                   string       `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
	NoBrowseChannelList  []uint32     `protobuf:"varint,6,rep,packed,name=no_browse_channel_list,json=noBrowseChannelList,proto3" json:"no_browse_channel_list,omitempty"`
	WordType             WordType     `protobuf:"varint,7,opt,name=word_type,json=wordType,proto3,enum=ga.WordType" json:"word_type,omitempty"`
	SiftType             uint32       `protobuf:"varint,8,opt,name=sift_type,json=siftType,proto3" json:"sift_type,omitempty"`
	NoViewedPostIdList   []string     `protobuf:"bytes,9,rep,name=no_viewed_post_id_list,json=noViewedPostIdList,proto3" json:"no_viewed_post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CommonSearchReq) Reset()         { *m = CommonSearchReq{} }
func (m *CommonSearchReq) String() string { return proto.CompactTextString(m) }
func (*CommonSearchReq) ProtoMessage()    {}
func (*CommonSearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{13}
}
func (m *CommonSearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchReq.Unmarshal(m, b)
}
func (m *CommonSearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchReq.Marshal(b, m, deterministic)
}
func (dst *CommonSearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchReq.Merge(dst, src)
}
func (m *CommonSearchReq) XXX_Size() int {
	return xxx_messageInfo_CommonSearchReq.Size(m)
}
func (m *CommonSearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchReq proto.InternalMessageInfo

func (m *CommonSearchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommonSearchReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *CommonSearchReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *CommonSearchReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CommonSearchReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CommonSearchReq) GetNoBrowseChannelList() []uint32 {
	if m != nil {
		return m.NoBrowseChannelList
	}
	return nil
}

func (m *CommonSearchReq) GetWordType() WordType {
	if m != nil {
		return m.WordType
	}
	return WordType_Default
}

func (m *CommonSearchReq) GetSiftType() uint32 {
	if m != nil {
		return m.SiftType
	}
	return 0
}

func (m *CommonSearchReq) GetNoViewedPostIdList() []string {
	if m != nil {
		return m.NoViewedPostIdList
	}
	return nil
}

type CommonSearchResp struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ResultList           []*CommonSearchResult `protobuf:"bytes,2,rep,name=result_list,json=resultList,proto3" json:"result_list,omitempty"`
	ForceLink            string                `protobuf:"bytes,3,opt,name=force_link,json=forceLink,proto3" json:"force_link,omitempty"`
	Keyword              string                `protobuf:"bytes,4,opt,name=keyword,proto3" json:"keyword,omitempty"`
	NextOffset           uint32                `protobuf:"varint,5,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	NextId               string                `protobuf:"bytes,6,opt,name=next_id,json=nextId,proto3" json:"next_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CommonSearchResp) Reset()         { *m = CommonSearchResp{} }
func (m *CommonSearchResp) String() string { return proto.CompactTextString(m) }
func (*CommonSearchResp) ProtoMessage()    {}
func (*CommonSearchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{14}
}
func (m *CommonSearchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchResp.Unmarshal(m, b)
}
func (m *CommonSearchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchResp.Marshal(b, m, deterministic)
}
func (dst *CommonSearchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchResp.Merge(dst, src)
}
func (m *CommonSearchResp) XXX_Size() int {
	return xxx_messageInfo_CommonSearchResp.Size(m)
}
func (m *CommonSearchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchResp proto.InternalMessageInfo

func (m *CommonSearchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CommonSearchResp) GetResultList() []*CommonSearchResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

func (m *CommonSearchResp) GetForceLink() string {
	if m != nil {
		return m.ForceLink
	}
	return ""
}

func (m *CommonSearchResp) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *CommonSearchResp) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

func (m *CommonSearchResp) GetNextId() string {
	if m != nil {
		return m.NextId
	}
	return ""
}

type CommonSearchResult struct {
	// Types that are valid to be assigned to Result:
	//	*CommonSearchResult_UserResult
	//	*CommonSearchResult_GuildResult
	//	*CommonSearchResult_ChannelResult
	//	*CommonSearchResult_CplResult
	//	*CommonSearchResult_AreaResult
	//	*CommonSearchResult_PostResult
	Result               isCommonSearchResult_Result `protobuf_oneof:"result"`
	SearchType           uint32                      `protobuf:"varint,6,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CommonSearchResult) Reset()         { *m = CommonSearchResult{} }
func (m *CommonSearchResult) String() string { return proto.CompactTextString(m) }
func (*CommonSearchResult) ProtoMessage()    {}
func (*CommonSearchResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{15}
}
func (m *CommonSearchResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchResult.Unmarshal(m, b)
}
func (m *CommonSearchResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchResult.Marshal(b, m, deterministic)
}
func (dst *CommonSearchResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchResult.Merge(dst, src)
}
func (m *CommonSearchResult) XXX_Size() int {
	return xxx_messageInfo_CommonSearchResult.Size(m)
}
func (m *CommonSearchResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchResult.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchResult proto.InternalMessageInfo

type isCommonSearchResult_Result interface {
	isCommonSearchResult_Result()
}

type CommonSearchResult_UserResult struct {
	UserResult *CommonSearchUserResult `protobuf:"bytes,1,opt,name=user_result,json=userResult,proto3,oneof"`
}

type CommonSearchResult_GuildResult struct {
	GuildResult *CommonSearchGuildResult `protobuf:"bytes,2,opt,name=guild_result,json=guildResult,proto3,oneof"`
}

type CommonSearchResult_ChannelResult struct {
	ChannelResult *CommonSearchChannelResult `protobuf:"bytes,3,opt,name=channel_result,json=channelResult,proto3,oneof"`
}

type CommonSearchResult_CplResult struct {
	CplResult *CommonSearchCPLResult `protobuf:"bytes,4,opt,name=cpl_result,json=cplResult,proto3,oneof"`
}

type CommonSearchResult_AreaResult struct {
	AreaResult *CommonSearchAreaResult `protobuf:"bytes,5,opt,name=area_result,json=areaResult,proto3,oneof"`
}

type CommonSearchResult_PostResult struct {
	PostResult *CommonSearchPostResult `protobuf:"bytes,7,opt,name=post_result,json=postResult,proto3,oneof"`
}

func (*CommonSearchResult_UserResult) isCommonSearchResult_Result() {}

func (*CommonSearchResult_GuildResult) isCommonSearchResult_Result() {}

func (*CommonSearchResult_ChannelResult) isCommonSearchResult_Result() {}

func (*CommonSearchResult_CplResult) isCommonSearchResult_Result() {}

func (*CommonSearchResult_AreaResult) isCommonSearchResult_Result() {}

func (*CommonSearchResult_PostResult) isCommonSearchResult_Result() {}

func (m *CommonSearchResult) GetResult() isCommonSearchResult_Result {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *CommonSearchResult) GetUserResult() *CommonSearchUserResult {
	if x, ok := m.GetResult().(*CommonSearchResult_UserResult); ok {
		return x.UserResult
	}
	return nil
}

func (m *CommonSearchResult) GetGuildResult() *CommonSearchGuildResult {
	if x, ok := m.GetResult().(*CommonSearchResult_GuildResult); ok {
		return x.GuildResult
	}
	return nil
}

func (m *CommonSearchResult) GetChannelResult() *CommonSearchChannelResult {
	if x, ok := m.GetResult().(*CommonSearchResult_ChannelResult); ok {
		return x.ChannelResult
	}
	return nil
}

func (m *CommonSearchResult) GetCplResult() *CommonSearchCPLResult {
	if x, ok := m.GetResult().(*CommonSearchResult_CplResult); ok {
		return x.CplResult
	}
	return nil
}

func (m *CommonSearchResult) GetAreaResult() *CommonSearchAreaResult {
	if x, ok := m.GetResult().(*CommonSearchResult_AreaResult); ok {
		return x.AreaResult
	}
	return nil
}

func (m *CommonSearchResult) GetPostResult() *CommonSearchPostResult {
	if x, ok := m.GetResult().(*CommonSearchResult_PostResult); ok {
		return x.PostResult
	}
	return nil
}

func (m *CommonSearchResult) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*CommonSearchResult) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _CommonSearchResult_OneofMarshaler, _CommonSearchResult_OneofUnmarshaler, _CommonSearchResult_OneofSizer, []interface{}{
		(*CommonSearchResult_UserResult)(nil),
		(*CommonSearchResult_GuildResult)(nil),
		(*CommonSearchResult_ChannelResult)(nil),
		(*CommonSearchResult_CplResult)(nil),
		(*CommonSearchResult_AreaResult)(nil),
		(*CommonSearchResult_PostResult)(nil),
	}
}

func _CommonSearchResult_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*CommonSearchResult)
	// result
	switch x := m.Result.(type) {
	case *CommonSearchResult_UserResult:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.UserResult); err != nil {
			return err
		}
	case *CommonSearchResult_GuildResult:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GuildResult); err != nil {
			return err
		}
	case *CommonSearchResult_ChannelResult:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ChannelResult); err != nil {
			return err
		}
	case *CommonSearchResult_CplResult:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.CplResult); err != nil {
			return err
		}
	case *CommonSearchResult_AreaResult:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.AreaResult); err != nil {
			return err
		}
	case *CommonSearchResult_PostResult:
		b.EncodeVarint(7<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PostResult); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("CommonSearchResult.Result has unexpected type %T", x)
	}
	return nil
}

func _CommonSearchResult_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*CommonSearchResult)
	switch tag {
	case 1: // result.user_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(CommonSearchUserResult)
		err := b.DecodeMessage(msg)
		m.Result = &CommonSearchResult_UserResult{msg}
		return true, err
	case 2: // result.guild_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(CommonSearchGuildResult)
		err := b.DecodeMessage(msg)
		m.Result = &CommonSearchResult_GuildResult{msg}
		return true, err
	case 3: // result.channel_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(CommonSearchChannelResult)
		err := b.DecodeMessage(msg)
		m.Result = &CommonSearchResult_ChannelResult{msg}
		return true, err
	case 4: // result.cpl_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(CommonSearchCPLResult)
		err := b.DecodeMessage(msg)
		m.Result = &CommonSearchResult_CplResult{msg}
		return true, err
	case 5: // result.area_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(CommonSearchAreaResult)
		err := b.DecodeMessage(msg)
		m.Result = &CommonSearchResult_AreaResult{msg}
		return true, err
	case 7: // result.post_result
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(CommonSearchPostResult)
		err := b.DecodeMessage(msg)
		m.Result = &CommonSearchResult_PostResult{msg}
		return true, err
	default:
		return false, nil
	}
}

func _CommonSearchResult_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*CommonSearchResult)
	// result
	switch x := m.Result.(type) {
	case *CommonSearchResult_UserResult:
		s := proto.Size(x.UserResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *CommonSearchResult_GuildResult:
		s := proto.Size(x.GuildResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *CommonSearchResult_ChannelResult:
		s := proto.Size(x.ChannelResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *CommonSearchResult_CplResult:
		s := proto.Size(x.CplResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *CommonSearchResult_AreaResult:
		s := proto.Size(x.AreaResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *CommonSearchResult_PostResult:
		s := proto.Size(x.PostResult)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type CommonSearchPostResult struct {
	// 是否为最后一页
	LastPage bool `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	// 动态搜索结果列表
	PostResultBinList    [][]byte `protobuf:"bytes,2,rep,name=post_result_bin_list,json=postResultBinList,proto3" json:"post_result_bin_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommonSearchPostResult) Reset()         { *m = CommonSearchPostResult{} }
func (m *CommonSearchPostResult) String() string { return proto.CompactTextString(m) }
func (*CommonSearchPostResult) ProtoMessage()    {}
func (*CommonSearchPostResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{16}
}
func (m *CommonSearchPostResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchPostResult.Unmarshal(m, b)
}
func (m *CommonSearchPostResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchPostResult.Marshal(b, m, deterministic)
}
func (dst *CommonSearchPostResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchPostResult.Merge(dst, src)
}
func (m *CommonSearchPostResult) XXX_Size() int {
	return xxx_messageInfo_CommonSearchPostResult.Size(m)
}
func (m *CommonSearchPostResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchPostResult.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchPostResult proto.InternalMessageInfo

func (m *CommonSearchPostResult) GetLastPage() bool {
	if m != nil {
		return m.LastPage
	}
	return false
}

func (m *CommonSearchPostResult) GetPostResultBinList() [][]byte {
	if m != nil {
		return m.PostResultBinList
	}
	return nil
}

// 定义了用户搜索结果的数据类型
type CommonSearchUserResult struct {
	// 是否为最后一页
	LastPage bool `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	// 用户信息列表
	UserBriefList        []*ancient_search.ContactBrief `protobuf:"bytes,2,rep,name=user_brief_list,json=userBriefList,proto3" json:"user_brief_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *CommonSearchUserResult) Reset()         { *m = CommonSearchUserResult{} }
func (m *CommonSearchUserResult) String() string { return proto.CompactTextString(m) }
func (*CommonSearchUserResult) ProtoMessage()    {}
func (*CommonSearchUserResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{17}
}
func (m *CommonSearchUserResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchUserResult.Unmarshal(m, b)
}
func (m *CommonSearchUserResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchUserResult.Marshal(b, m, deterministic)
}
func (dst *CommonSearchUserResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchUserResult.Merge(dst, src)
}
func (m *CommonSearchUserResult) XXX_Size() int {
	return xxx_messageInfo_CommonSearchUserResult.Size(m)
}
func (m *CommonSearchUserResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchUserResult.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchUserResult proto.InternalMessageInfo

func (m *CommonSearchUserResult) GetLastPage() bool {
	if m != nil {
		return m.LastPage
	}
	return false
}

func (m *CommonSearchUserResult) GetUserBriefList() []*ancient_search.ContactBrief {
	if m != nil {
		return m.UserBriefList
	}
	return nil
}

// 定义了公会搜索结果的数据类型
type CommonSearchGuildResult struct {
	// 是否为最后一页
	LastPage bool `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	// 公会信息列表
	GuildBriefList       []*guild.GuildBaseInfo `protobuf:"bytes,2,rep,name=guild_brief_list,json=guildBriefList,proto3" json:"guild_brief_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *CommonSearchGuildResult) Reset()         { *m = CommonSearchGuildResult{} }
func (m *CommonSearchGuildResult) String() string { return proto.CompactTextString(m) }
func (*CommonSearchGuildResult) ProtoMessage()    {}
func (*CommonSearchGuildResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{18}
}
func (m *CommonSearchGuildResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchGuildResult.Unmarshal(m, b)
}
func (m *CommonSearchGuildResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchGuildResult.Marshal(b, m, deterministic)
}
func (dst *CommonSearchGuildResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchGuildResult.Merge(dst, src)
}
func (m *CommonSearchGuildResult) XXX_Size() int {
	return xxx_messageInfo_CommonSearchGuildResult.Size(m)
}
func (m *CommonSearchGuildResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchGuildResult.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchGuildResult proto.InternalMessageInfo

func (m *CommonSearchGuildResult) GetLastPage() bool {
	if m != nil {
		return m.LastPage
	}
	return false
}

func (m *CommonSearchGuildResult) GetGuildBriefList() []*guild.GuildBaseInfo {
	if m != nil {
		return m.GuildBriefList
	}
	return nil
}

type CommonSearchChannelResult struct {
	// 是否为最后一页
	LastPage bool `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	// 房间信息列表
	ChannelBriefList     []*CommonSearchChannelBrief `protobuf:"bytes,2,rep,name=channel_brief_list,json=channelBriefList,proto3" json:"channel_brief_list,omitempty"`
	DisplayChannelMap    map[uint32]string           `protobuf:"bytes,3,rep,name=display_channel_map,json=displayChannelMap,proto3" json:"display_channel_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CommonSearchChannelResult) Reset()         { *m = CommonSearchChannelResult{} }
func (m *CommonSearchChannelResult) String() string { return proto.CompactTextString(m) }
func (*CommonSearchChannelResult) ProtoMessage()    {}
func (*CommonSearchChannelResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{19}
}
func (m *CommonSearchChannelResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchChannelResult.Unmarshal(m, b)
}
func (m *CommonSearchChannelResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchChannelResult.Marshal(b, m, deterministic)
}
func (dst *CommonSearchChannelResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchChannelResult.Merge(dst, src)
}
func (m *CommonSearchChannelResult) XXX_Size() int {
	return xxx_messageInfo_CommonSearchChannelResult.Size(m)
}
func (m *CommonSearchChannelResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchChannelResult.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchChannelResult proto.InternalMessageInfo

func (m *CommonSearchChannelResult) GetLastPage() bool {
	if m != nil {
		return m.LastPage
	}
	return false
}

func (m *CommonSearchChannelResult) GetChannelBriefList() []*CommonSearchChannelBrief {
	if m != nil {
		return m.ChannelBriefList
	}
	return nil
}

func (m *CommonSearchChannelResult) GetDisplayChannelMap() map[uint32]string {
	if m != nil {
		return m.DisplayChannelMap
	}
	return nil
}

// 定义了房间搜索结果中的房间信息数据类型
type CommonSearchChannelBrief struct {
	ChannelStyleType  uint32                         `protobuf:"varint,1,opt,name=channel_style_type,json=channelStyleType,proto3" json:"channel_style_type,omitempty"`
	CommonChannelInfo *channel.ChannelDetailInfo     `protobuf:"bytes,2,opt,name=common_channel_info,json=commonChannelInfo,proto3" json:"common_channel_info,omitempty"`
	TopicChannel      *channel_play.TopicChannelItem `protobuf:"bytes,3,opt,name=topic_channel,json=topicChannel,proto3" json:"topic_channel,omitempty"`
	// 房间标签
	CommonChannelTagInfo *channel.SCTagInfo `protobuf:"bytes,4,opt,name=common_channel_tag_info,json=commonChannelTagInfo,proto3" json:"common_channel_tag_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CommonSearchChannelBrief) Reset()         { *m = CommonSearchChannelBrief{} }
func (m *CommonSearchChannelBrief) String() string { return proto.CompactTextString(m) }
func (*CommonSearchChannelBrief) ProtoMessage()    {}
func (*CommonSearchChannelBrief) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{20}
}
func (m *CommonSearchChannelBrief) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchChannelBrief.Unmarshal(m, b)
}
func (m *CommonSearchChannelBrief) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchChannelBrief.Marshal(b, m, deterministic)
}
func (dst *CommonSearchChannelBrief) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchChannelBrief.Merge(dst, src)
}
func (m *CommonSearchChannelBrief) XXX_Size() int {
	return xxx_messageInfo_CommonSearchChannelBrief.Size(m)
}
func (m *CommonSearchChannelBrief) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchChannelBrief.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchChannelBrief proto.InternalMessageInfo

func (m *CommonSearchChannelBrief) GetChannelStyleType() uint32 {
	if m != nil {
		return m.ChannelStyleType
	}
	return 0
}

func (m *CommonSearchChannelBrief) GetCommonChannelInfo() *channel.ChannelDetailInfo {
	if m != nil {
		return m.CommonChannelInfo
	}
	return nil
}

func (m *CommonSearchChannelBrief) GetTopicChannel() *channel_play.TopicChannelItem {
	if m != nil {
		return m.TopicChannel
	}
	return nil
}

func (m *CommonSearchChannelBrief) GetCommonChannelTagInfo() *channel.SCTagInfo {
	if m != nil {
		return m.CommonChannelTagInfo
	}
	return nil
}

type CommonSearchCPLResult struct {
	ConfigId             string                                        `protobuf:"bytes,1,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	ResultList           []*super_channel.SuperChannelSearchResultItem `protobuf:"bytes,2,rep,name=result_list,json=resultList,proto3" json:"result_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *CommonSearchCPLResult) Reset()         { *m = CommonSearchCPLResult{} }
func (m *CommonSearchCPLResult) String() string { return proto.CompactTextString(m) }
func (*CommonSearchCPLResult) ProtoMessage()    {}
func (*CommonSearchCPLResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{21}
}
func (m *CommonSearchCPLResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchCPLResult.Unmarshal(m, b)
}
func (m *CommonSearchCPLResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchCPLResult.Marshal(b, m, deterministic)
}
func (dst *CommonSearchCPLResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchCPLResult.Merge(dst, src)
}
func (m *CommonSearchCPLResult) XXX_Size() int {
	return xxx_messageInfo_CommonSearchCPLResult.Size(m)
}
func (m *CommonSearchCPLResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchCPLResult.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchCPLResult proto.InternalMessageInfo

func (m *CommonSearchCPLResult) GetConfigId() string {
	if m != nil {
		return m.ConfigId
	}
	return ""
}

func (m *CommonSearchCPLResult) GetResultList() []*super_channel.SuperChannelSearchResultItem {
	if m != nil {
		return m.ResultList
	}
	return nil
}

type CommonSearchAreaBrief struct {
	GameCardResult       *game_card.GameCardConfInfo `protobuf:"bytes,1,opt,name=game_card_result,json=gameCardResult,proto3" json:"game_card_result,omitempty"`
	TabId                uint32                      `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *CommonSearchAreaBrief) Reset()         { *m = CommonSearchAreaBrief{} }
func (m *CommonSearchAreaBrief) String() string { return proto.CompactTextString(m) }
func (*CommonSearchAreaBrief) ProtoMessage()    {}
func (*CommonSearchAreaBrief) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{22}
}
func (m *CommonSearchAreaBrief) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchAreaBrief.Unmarshal(m, b)
}
func (m *CommonSearchAreaBrief) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchAreaBrief.Marshal(b, m, deterministic)
}
func (dst *CommonSearchAreaBrief) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchAreaBrief.Merge(dst, src)
}
func (m *CommonSearchAreaBrief) XXX_Size() int {
	return xxx_messageInfo_CommonSearchAreaBrief.Size(m)
}
func (m *CommonSearchAreaBrief) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchAreaBrief.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchAreaBrief proto.InternalMessageInfo

func (m *CommonSearchAreaBrief) GetGameCardResult() *game_card.GameCardConfInfo {
	if m != nil {
		return m.GameCardResult
	}
	return nil
}

func (m *CommonSearchAreaBrief) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type CommonSearchAreaResult struct {
	GameCardResultList   []*CommonSearchAreaBrief `protobuf:"bytes,1,rep,name=game_card_result_list,json=gameCardResultList,proto3" json:"game_card_result_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *CommonSearchAreaResult) Reset()         { *m = CommonSearchAreaResult{} }
func (m *CommonSearchAreaResult) String() string { return proto.CompactTextString(m) }
func (*CommonSearchAreaResult) ProtoMessage()    {}
func (*CommonSearchAreaResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{23}
}
func (m *CommonSearchAreaResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonSearchAreaResult.Unmarshal(m, b)
}
func (m *CommonSearchAreaResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonSearchAreaResult.Marshal(b, m, deterministic)
}
func (dst *CommonSearchAreaResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonSearchAreaResult.Merge(dst, src)
}
func (m *CommonSearchAreaResult) XXX_Size() int {
	return xxx_messageInfo_CommonSearchAreaResult.Size(m)
}
func (m *CommonSearchAreaResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonSearchAreaResult.DiscardUnknown(m)
}

var xxx_messageInfo_CommonSearchAreaResult proto.InternalMessageInfo

func (m *CommonSearchAreaResult) GetGameCardResultList() []*CommonSearchAreaBrief {
	if m != nil {
		return m.GameCardResultList
	}
	return nil
}

// 搜索引导词数据类型
type SearchGuideWords struct {
	// 引导词内容
	Content              string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Type                 WordType `protobuf:"varint,2,opt,name=type,proto3,enum=ga.WordType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchGuideWords) Reset()         { *m = SearchGuideWords{} }
func (m *SearchGuideWords) String() string { return proto.CompactTextString(m) }
func (*SearchGuideWords) ProtoMessage()    {}
func (*SearchGuideWords) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{24}
}
func (m *SearchGuideWords) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGuideWords.Unmarshal(m, b)
}
func (m *SearchGuideWords) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGuideWords.Marshal(b, m, deterministic)
}
func (dst *SearchGuideWords) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGuideWords.Merge(dst, src)
}
func (m *SearchGuideWords) XXX_Size() int {
	return xxx_messageInfo_SearchGuideWords.Size(m)
}
func (m *SearchGuideWords) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGuideWords.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGuideWords proto.InternalMessageInfo

func (m *SearchGuideWords) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SearchGuideWords) GetType() WordType {
	if m != nil {
		return m.Type
	}
	return WordType_Default
}

// 搜索引导词请求数据类型
type SearchGuideWordsReq struct {
	BaseReq *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	// 当前页面所在的专区类型：1 匹配 2 音乐 3 娱乐 ...
	AreaType             uint32   `protobuf:"varint,2,opt,name=area_type,json=areaType,proto3" json:"area_type,omitempty"`
	TabId                uint32   `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchGuideWordsReq) Reset()         { *m = SearchGuideWordsReq{} }
func (m *SearchGuideWordsReq) String() string { return proto.CompactTextString(m) }
func (*SearchGuideWordsReq) ProtoMessage()    {}
func (*SearchGuideWordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{25}
}
func (m *SearchGuideWordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGuideWordsReq.Unmarshal(m, b)
}
func (m *SearchGuideWordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGuideWordsReq.Marshal(b, m, deterministic)
}
func (dst *SearchGuideWordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGuideWordsReq.Merge(dst, src)
}
func (m *SearchGuideWordsReq) XXX_Size() int {
	return xxx_messageInfo_SearchGuideWordsReq.Size(m)
}
func (m *SearchGuideWordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGuideWordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGuideWordsReq proto.InternalMessageInfo

func (m *SearchGuideWordsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SearchGuideWordsReq) GetAreaType() uint32 {
	if m != nil {
		return m.AreaType
	}
	return 0
}

func (m *SearchGuideWordsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

// 搜索引导词响应数据类型
type SearchGuideWordsResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 引导词列表
	GuideWordsList       []*SearchGuideWords `protobuf:"bytes,2,rep,name=guide_words_list,json=guideWordsList,proto3" json:"guide_words_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SearchGuideWordsResp) Reset()         { *m = SearchGuideWordsResp{} }
func (m *SearchGuideWordsResp) String() string { return proto.CompactTextString(m) }
func (*SearchGuideWordsResp) ProtoMessage()    {}
func (*SearchGuideWordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{26}
}
func (m *SearchGuideWordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchGuideWordsResp.Unmarshal(m, b)
}
func (m *SearchGuideWordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchGuideWordsResp.Marshal(b, m, deterministic)
}
func (dst *SearchGuideWordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchGuideWordsResp.Merge(dst, src)
}
func (m *SearchGuideWordsResp) XXX_Size() int {
	return xxx_messageInfo_SearchGuideWordsResp.Size(m)
}
func (m *SearchGuideWordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchGuideWordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchGuideWordsResp proto.InternalMessageInfo

func (m *SearchGuideWordsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SearchGuideWordsResp) GetGuideWordsList() []*SearchGuideWords {
	if m != nil {
		return m.GuideWordsList
	}
	return nil
}

// ---------------------------------------------------------------
// 联想词
type SearchSuggestReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Query                string       `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SearchSuggestReq) Reset()         { *m = SearchSuggestReq{} }
func (m *SearchSuggestReq) String() string { return proto.CompactTextString(m) }
func (*SearchSuggestReq) ProtoMessage()    {}
func (*SearchSuggestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{27}
}
func (m *SearchSuggestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchSuggestReq.Unmarshal(m, b)
}
func (m *SearchSuggestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchSuggestReq.Marshal(b, m, deterministic)
}
func (dst *SearchSuggestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchSuggestReq.Merge(dst, src)
}
func (m *SearchSuggestReq) XXX_Size() int {
	return xxx_messageInfo_SearchSuggestReq.Size(m)
}
func (m *SearchSuggestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchSuggestReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchSuggestReq proto.InternalMessageInfo

func (m *SearchSuggestReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SearchSuggestReq) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

type SearchSuggestResp struct {
	BaseResp  *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Suggest   []*SuggestWords `protobuf:"bytes,2,rep,name=suggest,proto3" json:"suggest,omitempty"`
	ForceLink string          `protobuf:"bytes,3,opt,name=force_link,json=forceLink,proto3" json:"force_link,omitempty"`
	// 关键词
	ImportantWord        string   `protobuf:"bytes,4,opt,name=important_word,json=importantWord,proto3" json:"important_word,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchSuggestResp) Reset()         { *m = SearchSuggestResp{} }
func (m *SearchSuggestResp) String() string { return proto.CompactTextString(m) }
func (*SearchSuggestResp) ProtoMessage()    {}
func (*SearchSuggestResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{28}
}
func (m *SearchSuggestResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchSuggestResp.Unmarshal(m, b)
}
func (m *SearchSuggestResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchSuggestResp.Marshal(b, m, deterministic)
}
func (dst *SearchSuggestResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchSuggestResp.Merge(dst, src)
}
func (m *SearchSuggestResp) XXX_Size() int {
	return xxx_messageInfo_SearchSuggestResp.Size(m)
}
func (m *SearchSuggestResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchSuggestResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchSuggestResp proto.InternalMessageInfo

func (m *SearchSuggestResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SearchSuggestResp) GetSuggest() []*SuggestWords {
	if m != nil {
		return m.Suggest
	}
	return nil
}

func (m *SearchSuggestResp) GetForceLink() string {
	if m != nil {
		return m.ForceLink
	}
	return ""
}

func (m *SearchSuggestResp) GetImportantWord() string {
	if m != nil {
		return m.ImportantWord
	}
	return ""
}

type SuggestWords struct {
	Word                 string   `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"`
	Type                 WordType `protobuf:"varint,2,opt,name=type,proto3,enum=ga.WordType" json:"type,omitempty"`
	IsFollow             bool     `protobuf:"varint,3,opt,name=is_follow,json=isFollow,proto3" json:"is_follow,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SuggestWords) Reset()         { *m = SuggestWords{} }
func (m *SuggestWords) String() string { return proto.CompactTextString(m) }
func (*SuggestWords) ProtoMessage()    {}
func (*SuggestWords) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search__648ea8154ee4e10c, []int{29}
}
func (m *SuggestWords) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SuggestWords.Unmarshal(m, b)
}
func (m *SuggestWords) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SuggestWords.Marshal(b, m, deterministic)
}
func (dst *SuggestWords) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuggestWords.Merge(dst, src)
}
func (m *SuggestWords) XXX_Size() int {
	return xxx_messageInfo_SuggestWords.Size(m)
}
func (m *SuggestWords) XXX_DiscardUnknown() {
	xxx_messageInfo_SuggestWords.DiscardUnknown(m)
}

var xxx_messageInfo_SuggestWords proto.InternalMessageInfo

func (m *SuggestWords) GetWord() string {
	if m != nil {
		return m.Word
	}
	return ""
}

func (m *SuggestWords) GetType() WordType {
	if m != nil {
		return m.Type
	}
	return WordType_Default
}

func (m *SuggestWords) GetIsFollow() bool {
	if m != nil {
		return m.IsFollow
	}
	return false
}

func init() {
	proto.RegisterType((*UnifiedSearchReq)(nil), "ga.UnifiedSearchReq")
	proto.RegisterType((*UnifiedSearchResp)(nil), "ga.UnifiedSearchResp")
	proto.RegisterMapType((map[uint32]*UnifiedSearchResult)(nil), "ga.UnifiedSearchResp.ResultListEntry")
	proto.RegisterType((*UnifiedSearchResult)(nil), "ga.UnifiedSearchResult")
	proto.RegisterType((*UnifiedSearchGuildBrief)(nil), "ga.UnifiedSearchGuildBrief")
	proto.RegisterType((*UnifiedSearchUserBrief)(nil), "ga.UnifiedSearchUserBrief")
	proto.RegisterType((*UnifiedSearchChannelBrief)(nil), "ga.UnifiedSearchChannelBrief")
	proto.RegisterType((*UnifiedSearchActivityBrief)(nil), "ga.UnifiedSearchActivityBrief")
	proto.RegisterType((*UnifiedSearchAreaBrief)(nil), "ga.UnifiedSearchAreaBrief")
	proto.RegisterType((*UnifiedSearchCPLBrief)(nil), "ga.UnifiedSearchCPLBrief")
	proto.RegisterType((*UnifiedSearchUserResult)(nil), "ga.UnifiedSearchUserResult")
	proto.RegisterType((*UnifiedSearchGuildResult)(nil), "ga.UnifiedSearchGuildResult")
	proto.RegisterType((*UnifiedSearchChannelResult)(nil), "ga.UnifiedSearchChannelResult")
	proto.RegisterMapType((map[uint32]string)(nil), "ga.UnifiedSearchChannelResult.DisplayChannelMapEntry")
	proto.RegisterType((*UnifiedSearchCPLResult)(nil), "ga.UnifiedSearchCPLResult")
	proto.RegisterType((*CommonSearchReq)(nil), "ga.CommonSearchReq")
	proto.RegisterType((*CommonSearchResp)(nil), "ga.CommonSearchResp")
	proto.RegisterType((*CommonSearchResult)(nil), "ga.CommonSearchResult")
	proto.RegisterType((*CommonSearchPostResult)(nil), "ga.CommonSearchPostResult")
	proto.RegisterType((*CommonSearchUserResult)(nil), "ga.CommonSearchUserResult")
	proto.RegisterType((*CommonSearchGuildResult)(nil), "ga.CommonSearchGuildResult")
	proto.RegisterType((*CommonSearchChannelResult)(nil), "ga.CommonSearchChannelResult")
	proto.RegisterMapType((map[uint32]string)(nil), "ga.CommonSearchChannelResult.DisplayChannelMapEntry")
	proto.RegisterType((*CommonSearchChannelBrief)(nil), "ga.CommonSearchChannelBrief")
	proto.RegisterType((*CommonSearchCPLResult)(nil), "ga.CommonSearchCPLResult")
	proto.RegisterType((*CommonSearchAreaBrief)(nil), "ga.CommonSearchAreaBrief")
	proto.RegisterType((*CommonSearchAreaResult)(nil), "ga.CommonSearchAreaResult")
	proto.RegisterType((*SearchGuideWords)(nil), "ga.SearchGuideWords")
	proto.RegisterType((*SearchGuideWordsReq)(nil), "ga.SearchGuideWordsReq")
	proto.RegisterType((*SearchGuideWordsResp)(nil), "ga.SearchGuideWordsResp")
	proto.RegisterType((*SearchSuggestReq)(nil), "ga.SearchSuggestReq")
	proto.RegisterType((*SearchSuggestResp)(nil), "ga.SearchSuggestResp")
	proto.RegisterType((*SuggestWords)(nil), "ga.SuggestWords")
	proto.RegisterEnum("ga.UnifiedSearchType", UnifiedSearchType_name, UnifiedSearchType_value)
	proto.RegisterEnum("ga.SiftType", SiftType_name, SiftType_value)
	proto.RegisterEnum("ga.WordType", WordType_name, WordType_value)
	proto.RegisterEnum("ga.CommonSearchChannelBrief_ChannelStyleType", CommonSearchChannelBrief_ChannelStyleType_name, CommonSearchChannelBrief_ChannelStyleType_value)
	proto.RegisterEnum("ga.SearchGuideWordsReq_AreaType", SearchGuideWordsReq_AreaType_name, SearchGuideWordsReq_AreaType_value)
}

func init() {
	proto.RegisterFile("unified-search_.proto", fileDescriptor_unified_search__648ea8154ee4e10c)
}

var fileDescriptor_unified_search__648ea8154ee4e10c = []byte{
	// 2467 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0xcd, 0x6e, 0x23, 0xc7,
	0xf1, 0x5f, 0x92, 0xa2, 0x48, 0x16, 0x3f, 0x34, 0x6a, 0x7d, 0x51, 0xd2, 0xee, 0xdf, 0xfa, 0x33,
	0xb1, 0x23, 0x6f, 0xb2, 0x5a, 0x60, 0x9d, 0x45, 0x0c, 0x1b, 0x76, 0x42, 0x51, 0x94, 0x96, 0x31,
	0x45, 0x09, 0x43, 0x6a, 0x8d, 0xe4, 0x32, 0x68, 0xce, 0x34, 0xb9, 0x13, 0x0d, 0x67, 0x66, 0x67,
	0x9a, 0xd2, 0x32, 0x87, 0x20, 0x3e, 0xe6, 0x14, 0x20, 0x97, 0x1c, 0x72, 0x4e, 0xce, 0x0e, 0xf2,
	0x02, 0x41, 0x1e, 0x20, 0xa7, 0x9c, 0xf2, 0x06, 0x79, 0x8b, 0xa0, 0x3f, 0x86, 0xf3, 0xc1, 0x21,
	0xd7, 0x0b, 0x38, 0xbe, 0x10, 0xdd, 0xd5, 0xd5, 0x55, 0xd5, 0xd5, 0xd5, 0x55, 0xbf, 0x1a, 0xc2,
	0xce, 0xd4, 0x36, 0x47, 0x26, 0x31, 0x9e, 0xf8, 0x04, 0x7b, 0xfa, 0x2b, 0xed, 0xc4, 0xf5, 0x1c,
	0xea, 0xa0, 0xec, 0x18, 0x1f, 0x54, 0xc7, 0x58, 0x1b, 0x62, 0x9f, 0x08, 0xd2, 0x41, 0x4d, 0x7f,
	0x85, 0x6d, 0x9b, 0x58, 0x92, 0xe5, 0x60, 0x4b, 0xce, 0x9f, 0xb8, 0x16, 0x9e, 0x05, 0xc4, 0x1d,
	0x6c, 0xeb, 0x26, 0xb1, 0x69, 0x5c, 0xdc, 0xc1, 0xb6, 0x3f, 0x75, 0x89, 0xf7, 0x24, 0x21, 0x41,
	0x19, 0xe3, 0x09, 0xd1, 0x74, 0xec, 0x19, 0x01, 0xa5, 0x32, 0x9e, 0x9a, 0x56, 0x30, 0x6b, 0xfc,
	0x29, 0x03, 0xca, 0x8d, 0x30, 0xaf, 0xcf, 0xc5, 0xa9, 0xe4, 0x35, 0xfa, 0x00, 0x8a, 0xcc, 0x28,
	0xcd, 0x23, 0xaf, 0xeb, 0x99, 0xa3, 0xcc, 0x71, 0xf9, 0x59, 0xf9, 0x64, 0x8c, 0x4f, 0x4e, 0xb1,
	0x4f, 0x54, 0xf2, 0x5a, 0x2d, 0x0c, 0xc5, 0x00, 0xd5, 0xa1, 0x70, 0x4b, 0x66, 0xf7, 0x8e, 0x67,
	0xd4, 0xb3, 0x47, 0x99, 0xe3, 0x92, 0x1a, 0x4c, 0x11, 0x82, 0x35, 0x17, 0x8f, 0x49, 0x3d, 0x77,
	0x94, 0x39, 0xae, 0xaa, 0x7c, 0x8c, 0xb6, 0x21, 0xaf, 0x3b, 0x53, 0x9b, 0xd6, 0xd7, 0x38, 0x51,
	0x4c, 0xd0, 0x21, 0x94, 0xe8, 0xcc, 0x25, 0x9a, 0x65, 0xfa, 0xb4, 0x9e, 0x3f, 0xca, 0x1d, 0x57,
	0xd5, 0x22, 0x23, 0x74, 0x4d, 0x9f, 0x36, 0xfe, 0x98, 0x85, 0xcd, 0x84, 0x75, 0xbe, 0x8b, 0x3e,
	0x84, 0x92, 0x34, 0xcf, 0x77, 0xa5, 0x7d, 0x95, 0xd0, 0x3e, 0xdf, 0x55, 0x8b, 0x43, 0x39, 0x5a,
	0x61, 0xe1, 0x23, 0x80, 0x91, 0xe3, 0xe9, 0x4c, 0xb1, 0x7d, 0xcb, 0xed, 0x2c, 0xa9, 0x25, 0x4e,
	0xe9, 0x9a, 0xf6, 0x2d, 0x3a, 0x87, 0xb2, 0x47, 0xfc, 0xa9, 0x45, 0x85, 0x61, 0x6b, 0x47, 0xb9,
	0xe3, 0xf2, 0xb3, 0xf7, 0x99, 0x96, 0x05, 0x7b, 0x4e, 0x54, 0xce, 0xc8, 0x0c, 0x6e, 0xdb, 0xd4,
	0x9b, 0xa9, 0xe0, 0xcd, 0x09, 0x07, 0x2f, 0x61, 0x23, 0xb1, 0x8c, 0x14, 0xc8, 0xdd, 0x92, 0x19,
	0x37, 0xbc, 0xaa, 0xb2, 0x21, 0x7a, 0x02, 0xf9, 0x3b, 0x6c, 0x4d, 0x09, 0xb7, 0xb1, 0xfc, 0x6c,
	0x2f, 0x4d, 0xcd, 0xd4, 0xa2, 0xaa, 0xe0, 0xfa, 0x24, 0xfb, 0x71, 0xa6, 0xf1, 0x97, 0x1c, 0x6c,
	0xa5, 0xb0, 0xa0, 0xcf, 0xa1, 0x3c, 0xf5, 0x89, 0xa7, 0x09, 0x13, 0xa4, 0x77, 0x0e, 0x17, 0x04,
	0xde, 0xf8, 0xc4, 0x13, 0x3b, 0x5e, 0x3c, 0x50, 0x61, 0x3a, 0x9f, 0xa1, 0x26, 0xc8, 0xf8, 0x90,
	0x02, 0x84, 0x45, 0x0f, 0x17, 0x04, 0x5c, 0x30, 0xa6, 0xb9, 0x84, 0xf2, 0x38, 0x9c, 0xa2, 0x0b,
	0x98, 0x87, 0xb1, 0x14, 0x92, 0xe3, 0x42, 0xfe, 0x6f, 0x41, 0x48, 0x4b, 0xb0, 0xcd, 0xc5, 0x54,
	0xf5, 0x28, 0x01, 0x7d, 0x0a, 0xa0, 0xbb, 0x73, 0x21, 0x6b, 0x5c, 0xc8, 0xc1, 0xa2, 0x90, 0xeb,
	0xee, 0x5c, 0x40, 0x49, 0x77, 0x83, 0xcd, 0x9f, 0x41, 0x19, 0x7b, 0x04, 0x07, 0xbb, 0xf3, 0xe1,
	0xee, 0x96, 0x33, 0x99, 0x38, 0xb6, 0xd8, 0xdc, 0xf4, 0x08, 0x0e, 0xfd, 0x80, 0xe7, 0x33, 0xb6,
	0xdd, 0x75, 0x7c, 0x1a, 0x6c, 0x5f, 0x4f, 0xdf, 0x7e, 0xed, 0xf8, 0x34, 0xdc, 0xee, 0xce, 0x67,
	0xa7, 0x45, 0x58, 0x17, 0x3b, 0x1b, 0xbf, 0xcd, 0xc1, 0xde, 0xa2, 0xe7, 0x4e, 0x3d, 0x93, 0x8c,
	0xd0, 0x3e, 0x14, 0x85, 0xb3, 0x4d, 0x43, 0x86, 0x43, 0x81, 0xcf, 0x3b, 0x06, 0x3a, 0x06, 0x45,
	0x2c, 0x19, 0xa6, 0xcf, 0x1f, 0xbf, 0x29, 0x22, 0xb8, 0xaa, 0xd6, 0x38, 0xfd, 0x4c, 0x90, 0x3b,
	0xfc, 0xa9, 0xd9, 0x78, 0x42, 0x64, 0x08, 0xf3, 0x31, 0x7a, 0x0f, 0xca, 0x36, 0x21, 0x86, 0x76,
	0x47, 0x3c, 0x73, 0x34, 0x93, 0x0f, 0x0e, 0x18, 0xe9, 0x25, 0xa7, 0xb0, 0x57, 0x37, 0x21, 0x13,
	0x4d, 0xbc, 0xc7, 0x3c, 0x5f, 0x2e, 0x4e, 0xc8, 0xa4, 0xc5, 0x9f, 0xe4, 0xf7, 0xa1, 0x36, 0x36,
	0x47, 0x54, 0x73, 0x6f, 0xc7, 0x92, 0x63, 0x9d, 0x73, 0x54, 0x18, 0xf5, 0xfa, 0x76, 0x2c, 0xb8,
	0x7e, 0x00, 0x1b, 0xc2, 0xc2, 0x09, 0xb6, 0xcd, 0x11, 0xf1, 0xa9, 0x53, 0x2f, 0x70, 0x13, 0x84,
	0x81, 0x97, 0x01, 0x15, 0x3d, 0x81, 0x2d, 0x9e, 0x84, 0x12, 0x32, 0x8b, 0x5c, 0x26, 0xcf, 0x4f,
	0x17, 0x51, 0xb9, 0xf3, 0x93, 0xfb, 0x14, 0x7b, 0x9a, 0x45, 0xee, 0x88, 0x55, 0x2f, 0x45, 0x4e,
	0xde, 0xa7, 0xd8, 0xeb, 0x32, 0x2a, 0xfa, 0x00, 0x36, 0x5e, 0x39, 0x2c, 0xbb, 0xc9, 0x68, 0x33,
	0x8d, 0x3a, 0x70, 0xc6, 0x2a, 0x23, 0xcb, 0xe0, 0xea, 0x18, 0x8d, 0x7f, 0xe7, 0x60, 0x77, 0x21,
	0xfa, 0xe7, 0x37, 0x30, 0xc2, 0x3a, 0xd1, 0x26, 0xc6, 0x73, 0x7e, 0x03, 0x25, 0xb5, 0xc0, 0xe6,
	0x97, 0xc6, 0x73, 0xf6, 0x4c, 0x7d, 0xf2, 0x46, 0x3a, 0x9d, 0x0d, 0xd1, 0x43, 0x28, 0xf9, 0xe6,
	0xd8, 0xc6, 0x74, 0xea, 0x05, 0xee, 0x0e, 0x09, 0xcc, 0xa5, 0xb6, 0xa9, 0xdf, 0x6a, 0xfc, 0x32,
	0xd6, 0xf8, 0x6a, 0x91, 0x11, 0x7a, 0xec, 0x42, 0xea, 0x50, 0xc0, 0x7a, 0xe8, 0xed, 0x92, 0x1a,
	0x4c, 0x99, 0x9a, 0xa9, 0x69, 0x48, 0x0f, 0xb3, 0x21, 0xfa, 0x1e, 0x54, 0xe5, 0xa2, 0x86, 0x2d,
	0x13, 0xfb, 0xd2, 0xad, 0x15, 0x49, 0x6c, 0x32, 0x1a, 0x63, 0xd2, 0x89, 0x47, 0xcd, 0xd1, 0x4c,
	0xa3, 0x26, 0xb5, 0x08, 0x77, 0x67, 0x49, 0xad, 0x48, 0xe2, 0x80, 0xd1, 0xa2, 0x4c, 0x3e, 0x9d,
	0x59, 0x84, 0xfb, 0x31, 0x64, 0xea, 0x33, 0x1a, 0x4b, 0x84, 0x0b, 0x0e, 0x2c, 0xe9, 0x81, 0xf3,
	0xd8, 0xb1, 0xdc, 0x5b, 0x76, 0x17, 0x74, 0xea, 0xd7, 0xcb, 0x22, 0x52, 0xdc, 0xdb, 0x3e, 0x9f,
	0xa3, 0xcf, 0xe0, 0x70, 0xae, 0xc0, 0x25, 0xba, 0x89, 0x2d, 0x8d, 0x8c, 0x46, 0x44, 0xa7, 0x9a,
	0xa9, 0x3b, 0x76, 0xbd, 0xc2, 0xd5, 0xd5, 0x03, 0x75, 0x82, 0xa3, 0xcd, 0x19, 0x3a, 0xba, 0x63,
	0xa3, 0xf7, 0xa1, 0x76, 0x17, 0x37, 0xb0, 0xca, 0x77, 0x54, 0xef, 0x62, 0x16, 0x1e, 0x42, 0xc9,
	0xf4, 0xb5, 0x91, 0x63, 0x59, 0xce, 0x7d, 0xbd, 0x76, 0x94, 0x39, 0x2e, 0xaa, 0x45, 0xd3, 0x3f,
	0xe7, 0xf3, 0xc6, 0xbf, 0x32, 0xb0, 0x9f, 0x96, 0x54, 0xc4, 0xfd, 0xb6, 0x21, 0x28, 0xa1, 0x9a,
	0x41, 0x28, 0x36, 0x2d, 0xcd, 0xb4, 0x47, 0x8e, 0x4c, 0x8b, 0x3b, 0xfc, 0x39, 0x8b, 0xe5, 0x33,
	0xbe, 0xda, 0xb1, 0x47, 0x8e, 0xba, 0xa9, 0x27, 0x49, 0xe8, 0x18, 0x8a, 0x14, 0x8f, 0xc5, 0x5e,
	0x91, 0x11, 0xab, 0x6c, 0x6f, 0xbf, 0x35, 0xc0, 0x63, 0xbe, 0xa7, 0x40, 0xc5, 0x00, 0x9d, 0x43,
	0x95, 0x3a, 0xae, 0xa9, 0x07, 0x41, 0x29, 0x73, 0xdf, 0xff, 0x33, 0xf6, 0xc0, 0x12, 0xf6, 0x70,
	0x4f, 0x06, 0x8c, 0x2b, 0x88, 0x51, 0x4a, 0x26, 0x6a, 0x85, 0x46, 0x28, 0x8d, 0x29, 0x1c, 0xc4,
	0x4e, 0xd5, 0xd4, 0xa9, 0x79, 0x67, 0xd2, 0x99, 0x38, 0xd6, 0x36, 0xe4, 0xc5, 0xad, 0x8b, 0x98,
	0x15, 0x13, 0x96, 0x09, 0xb8, 0xdb, 0x45, 0xa5, 0xe3, 0x63, 0x46, 0x33, 0x88, 0xaf, 0x07, 0xd9,
	0x81, 0x8d, 0x59, 0xd0, 0xff, 0x6a, 0x3a, 0x71, 0xb5, 0xa9, 0x67, 0xc9, 0x40, 0x2d, 0xb0, 0xf9,
	0x8d, 0x67, 0x35, 0x5e, 0x27, 0x5e, 0x0a, 0xcb, 0x8f, 0xff, 0x63, 0x95, 0x7f, 0xc8, 0xc2, 0x4e,
	0x32, 0xa1, 0xaf, 0x52, 0x59, 0x83, 0xec, 0x70, 0x2c, 0x15, 0x66, 0x87, 0x63, 0x56, 0x25, 0x78,
	0xc5, 0x1b, 0xb2, 0x3d, 0xd2, 0xdd, 0x07, 0xa9, 0x05, 0x8f, 0x4b, 0x65, 0x55, 0x62, 0x3a, 0x7f,
	0xff, 0x67, 0x10, 0xd4, 0x1c, 0xb9, 0x5f, 0x54, 0x99, 0x47, 0xcb, 0x4a, 0x55, 0x20, 0xa2, 0xa2,
	0x47, 0xa3, 0xec, 0x02, 0x6a, 0x58, 0xde, 0x8f, 0x14, 0x93, 0x5f, 0x52, 0xf1, 0x62, 0xd7, 0xc8,
	0x2a, 0x1e, 0x8e, 0x12, 0x4e, 0x0b, 0x12, 0x08, 0x34, 0x7e, 0x9d, 0x28, 0x1a, 0x61, 0xbd, 0x66,
	0xaf, 0xc1, 0xc2, 0x3e, 0xd5, 0x38, 0xbe, 0xca, 0x88, 0xd7, 0xc0, 0x08, 0xd7, 0x0c, 0x63, 0x9d,
	0xc2, 0x46, 0xe8, 0x0c, 0x01, 0x5d, 0xb2, 0x1c, 0xba, 0xac, 0xf0, 0x88, 0x5a, 0x9d, 0xfb, 0x83,
	0x83, 0xae, 0xdf, 0x40, 0x7d, 0x59, 0xa9, 0x5f, 0xad, 0xbc, 0x1d, 0x64, 0xee, 0x05, 0xed, 0x87,
	0xe9, 0xf8, 0x41, 0xa8, 0x17, 0x69, 0x3d, 0xd4, 0xff, 0xb7, 0x6c, 0x22, 0xf6, 0x63, 0x30, 0x61,
	0xb5, 0x09, 0x5f, 0x00, 0x8a, 0xdd, 0x67, 0xd4, 0x88, 0xd5, 0x97, 0xaa, 0x2a, 0xd1, 0x2b, 0x65,
	0x86, 0x20, 0x02, 0x5b, 0x41, 0xf5, 0x0d, 0x84, 0x4e, 0xb0, 0x5b, 0xcf, 0x71, 0x69, 0xcf, 0x57,
	0xa3, 0x99, 0x13, 0x59, 0xa0, 0x25, 0xf1, 0x12, 0xbb, 0x02, 0x1b, 0x6e, 0x1a, 0x49, 0xfa, 0xc1,
	0x19, 0xec, 0xa6, 0x33, 0xa7, 0x20, 0xc5, 0xed, 0x28, 0x52, 0x2c, 0x45, 0x01, 0xe1, 0x5d, 0xe2,
	0xe5, 0xce, 0x61, 0x11, 0x73, 0x98, 0xee, 0xd8, 0x23, 0x73, 0x1c, 0xc0, 0x8c, 0x92, 0x5a, 0x14,
	0x84, 0x8e, 0x81, 0x7e, 0x0a, 0x35, 0x86, 0xb1, 0x16, 0x9c, 0xb5, 0x9f, 0x86, 0xb3, 0x84, 0xa3,
	0x2a, 0xba, 0x1b, 0x3a, 0xa9, 0xf1, 0xf7, 0x2c, 0x6c, 0x44, 0x21, 0xd1, 0xb7, 0xd3, 0x3f, 0xec,
	0xc2, 0xba, 0x33, 0x1a, 0xf9, 0x84, 0xca, 0x0e, 0x42, 0xce, 0x58, 0x6e, 0x61, 0xcd, 0x81, 0x44,
	0x34, 0x7c, 0xcc, 0x12, 0x82, 0x69, 0xc8, 0xb2, 0x9a, 0x35, 0x0d, 0xf4, 0x11, 0xec, 0xda, 0x8e,
	0x36, 0xf4, 0x9c, 0x7b, 0x3f, 0xc4, 0x06, 0xfc, 0x68, 0xeb, 0xbc, 0xbd, 0xd8, 0xb2, 0x9d, 0x53,
	0xbe, 0x28, 0xdd, 0xcd, 0xef, 0xfa, 0x43, 0x28, 0x31, 0xc5, 0x1a, 0x97, 0xce, 0x0a, 0x6e, 0x4d,
	0xf4, 0x14, 0x5f, 0x3a, 0x9e, 0x31, 0x98, 0xb9, 0x44, 0x2d, 0xde, 0xcb, 0x11, 0xf3, 0xa7, 0xcf,
	0xa0, 0x0c, 0x67, 0x15, 0x28, 0xa6, 0xc8, 0x08, 0x7c, 0xf1, 0x19, 0x57, 0x7e, 0x67, 0x92, 0x7b,
	0x62, 0x68, 0x1c, 0x41, 0x9a, 0x86, 0x50, 0x5e, 0x3a, 0xca, 0x1d, 0x97, 0x54, 0x64, 0x3b, 0x2f,
	0xf9, 0x22, 0x83, 0x8e, 0x1d, 0x83, 0xbb, 0xf0, 0x3f, 0x19, 0x50, 0xe2, 0x2e, 0x7c, 0xb7, 0x26,
	0xe7, 0x27, 0xf1, 0x5e, 0x45, 0x5c, 0xe0, 0x6e, 0x12, 0xab, 0xca, 0x1e, 0x22, 0xd2, 0x9c, 0xbc,
	0xad, 0x07, 0x8a, 0x5c, 0xcf, 0x5a, 0xfc, 0x7a, 0x38, 0xbe, 0x7c, 0x43, 0x35, 0x79, 0x47, 0xf9,
	0x00, 0x5f, 0xbe, 0xa1, 0x57, 0xe2, 0x9e, 0xf6, 0xa0, 0xc0, 0x19, 0x24, 0xb2, 0x29, 0xa9, 0xeb,
	0x6c, 0xda, 0x31, 0x1a, 0xff, 0xc8, 0x01, 0x5a, 0xb4, 0x8a, 0xc1, 0xed, 0xc5, 0xb6, 0x65, 0x01,
	0x6e, 0x2f, 0xed, 0x5a, 0x7e, 0x96, 0xda, 0xb5, 0x1c, 0x26, 0xf7, 0xaf, 0x68, 0x5a, 0xce, 0x97,
	0x34, 0x2d, 0x8f, 0x92, 0x32, 0xde, 0xd2, 0xb3, 0x7c, 0x92, 0xd2, 0xb3, 0xec, 0x2f, 0xc8, 0xf8,
	0x2e, 0x5a, 0x96, 0xc2, 0xbb, 0xb5, 0x2c, 0xec, 0x4e, 0xe5, 0x07, 0x05, 0x1e, 0xd8, 0x02, 0x90,
	0x82, 0x20, 0xb1, 0xd0, 0x8e, 0xf4, 0x34, 0x23, 0xd8, 0x4d, 0x17, 0xb9, 0x3a, 0x39, 0x3f, 0x85,
	0xed, 0x88, 0x81, 0xda, 0xd0, 0xb4, 0xc3, 0x80, 0xad, 0xa8, 0x9b, 0x91, 0xf6, 0xc9, 0xb4, 0xf9,
	0xc3, 0x70, 0xe2, 0x7a, 0xbe, 0x69, 0x11, 0xfc, 0x78, 0x59, 0x11, 0x54, 0x84, 0x33, 0x6c, 0x8a,
	0x75, 0x9a, 0x5a, 0xfa, 0x7c, 0xd8, 0x5b, 0x12, 0x2f, 0xab, 0x35, 0x7e, 0xba, 0xb4, 0xf2, 0x6d,
	0x32, 0x95, 0xa2, 0xd8, 0x61, 0x9f, 0x70, 0xac, 0x98, 0xac, 0x77, 0x5f, 0x67, 0x61, 0x7f, 0x69,
	0x84, 0xad, 0xd6, 0xfb, 0xf3, 0x15, 0xe5, 0xee, 0xe1, 0x92, 0xc8, 0x5d, 0x56, 0xed, 0x8c, 0x55,
	0xd5, 0xee, 0xc7, 0x2b, 0x9f, 0xc1, 0x77, 0x5e, 0xec, 0xbe, 0xca, 0x41, 0x7d, 0xd9, 0xd1, 0xd0,
	0x8f, 0x42, 0xa7, 0xf0, 0xa6, 0x42, 0xc4, 0xb3, 0x90, 0x1b, 0x1c, 0x9b, 0x37, 0x16, 0x3c, 0x61,
	0xb3, 0x0e, 0x81, 0x4b, 0x0a, 0xdb, 0xc8, 0x10, 0xe5, 0x2f, 0xed, 0x10, 0xf8, 0x8e, 0x00, 0xbd,
	0x7f, 0x8b, 0xb8, 0x1f, 0x9d, 0xc1, 0x5e, 0xc2, 0x9c, 0x79, 0xe3, 0xb1, 0x96, 0xd6, 0x78, 0x6c,
	0xc7, 0x4c, 0x91, 0xd4, 0xc6, 0x10, 0x94, 0x56, 0xf2, 0xa0, 0x8f, 0x60, 0xff, 0xa6, 0xd7, 0x39,
	0xef, 0xb4, 0xcf, 0xb4, 0xd6, 0x8b, 0x66, 0xaf, 0xd7, 0xee, 0x6a, 0x83, 0x5f, 0x5c, 0xb7, 0xf9,
	0x8f, 0xf2, 0x00, 0xed, 0xc1, 0x56, 0x8c, 0xdc, 0xba, 0xba, 0xbc, 0xbc, 0xea, 0x29, 0x19, 0xb4,
	0x0b, 0x28, 0xce, 0x7f, 0x75, 0xdd, 0x69, 0x29, 0xd9, 0xc6, 0x3d, 0xec, 0xa4, 0xe6, 0xb4, 0xd5,
	0x78, 0xa3, 0x99, 0x56, 0xab, 0x8e, 0xf8, 0x99, 0xa6, 0x2e, 0xf1, 0x02, 0xab, 0x23, 0xb5, 0x81,
	0x3b, 0x29, 0x52, 0xb5, 0x1a, 0x76, 0x5c, 0x71, 0xd8, 0xa2, 0x7c, 0x0e, 0x91, 0xaf, 0x9d, 0xb1,
	0x4a, 0xb2, 0xcd, 0x5f, 0x21, 0x9e, 0x90, 0x16, 0xf6, 0x8c, 0x96, 0x63, 0x8f, 0xe4, 0x43, 0x94,
	0x14, 0x69, 0xf8, 0x0e, 0xac, 0x53, 0x3c, 0x0c, 0xbf, 0xb4, 0xe4, 0x29, 0x1e, 0x76, 0x8c, 0x64,
	0xb6, 0x0b, 0xf3, 0x2f, 0xea, 0xc2, 0x4e, 0x52, 0xa1, 0x38, 0x56, 0x26, 0xc4, 0x50, 0xa9, 0xa6,
	0xaa, 0x28, 0xae, 0x9a, 0x9f, 0xab, 0x07, 0xca, 0x3c, 0xed, 0x18, 0x84, 0x01, 0x0f, 0x9f, 0x95,
	0x60, 0xdd, 0xb1, 0x29, 0xb1, 0x69, 0xf0, 0x79, 0x42, 0x4e, 0xd1, 0x91, 0x44, 0x42, 0xd9, 0x14,
	0xac, 0xc2, 0x57, 0x1a, 0x7f, 0xce, 0xc2, 0x56, 0x52, 0xe0, 0xbb, 0xa0, 0xb3, 0x43, 0x28, 0xf1,
	0x72, 0x34, 0x57, 0x53, 0x55, 0x8b, 0x8c, 0xc0, 0xa3, 0x29, 0xf4, 0x55, 0x2e, 0xea, 0xab, 0xbf,
	0x66, 0xa0, 0xd8, 0x0c, 0x78, 0x0e, 0x60, 0x37, 0x88, 0xb8, 0xa6, 0xda, 0x6e, 0xc6, 0xc2, 0x6d,
	0x0b, 0x36, 0x42, 0xda, 0x65, 0x73, 0xd0, 0x7a, 0xa1, 0x64, 0x12, 0xc4, 0x9b, 0x3e, 0x8b, 0x33,
	0x74, 0x08, 0x7b, 0x21, 0xb1, 0xdd, 0x1b, 0xb4, 0xd5, 0x41, 0xb3, 0xd3, 0xbb, 0x6c, 0xf7, 0x06,
	0x4a, 0x0e, 0x29, 0x50, 0x09, 0x17, 0x3b, 0x97, 0xca, 0x1a, 0x8b, 0xe3, 0xa8, 0xe0, 0x4e, 0x4f,
	0xbb, 0x6e, 0x5e, 0xb4, 0x95, 0x7c, 0xca, 0xc2, 0x45, 0xf3, 0xb2, 0xad, 0xac, 0x37, 0xbe, 0xca,
	0xc0, 0xf6, 0xa2, 0x9f, 0xde, 0x0d, 0x82, 0x7d, 0xce, 0x0b, 0x80, 0x41, 0x34, 0x06, 0x8f, 0xfc,
	0x68, 0x6c, 0xf3, 0xd0, 0x5b, 0x10, 0x5f, 0x1b, 0xcf, 0xc7, 0xfc, 0xee, 0xaf, 0x83, 0xbb, 0xef,
	0x4f, 0xc7, 0x63, 0xc2, 0xea, 0xe0, 0x37, 0xbf, 0xa7, 0x6d, 0xc8, 0xbf, 0x9e, 0x12, 0x6f, 0x16,
	0xa4, 0x49, 0x3e, 0x69, 0x7c, 0x9d, 0x81, 0xcd, 0x84, 0xc8, 0x77, 0x3b, 0xd2, 0x63, 0x28, 0xf8,
	0x62, 0x67, 0xb4, 0x7a, 0x4a, 0x61, 0xe2, 0x14, 0x01, 0xc3, 0xdb, 0x80, 0xe4, 0xfb, 0x50, 0x33,
	0x27, 0xae, 0xe3, 0x51, 0x6c, 0x53, 0x2d, 0x82, 0x27, 0xab, 0x73, 0x2a, 0x93, 0xd7, 0xc0, 0x50,
	0x89, 0x8a, 0x67, 0x60, 0x9f, 0x33, 0x8b, 0xc8, 0xe7, 0xe3, 0xb7, 0x87, 0x7d, 0xfc, 0x6b, 0x51,
	0x2e, 0xfe, 0xb5, 0xe8, 0xf1, 0xef, 0x92, 0x7f, 0x28, 0xf0, 0x40, 0x7d, 0x0f, 0x0e, 0x83, 0x40,
	0xed, 0xb7, 0x9b, 0x6a, 0xeb, 0x85, 0x08, 0x92, 0x9b, 0xde, 0x17, 0xbd, 0xab, 0x2f, 0x7b, 0xca,
	0x03, 0xf4, 0x10, 0xea, 0xa9, 0x0c, 0xfd, 0xb6, 0xaa, 0x64, 0xa2, 0x99, 0x35, 0xba, 0x7a, 0x71,
	0xd3, 0xe9, 0x9e, 0x29, 0xd9, 0x65, 0xd2, 0x65, 0x52, 0x55, 0x72, 0x2c, 0xc2, 0x53, 0x19, 0xae,
	0xbb, 0xca, 0xda, 0x32, 0xd5, 0x2c, 0x94, 0x95, 0xfc, 0xb2, 0xd5, 0xeb, 0xab, 0xfe, 0x40, 0x59,
	0x47, 0x0f, 0xd3, 0x05, 0x37, 0xbb, 0x5d, 0xe5, 0xf7, 0xbd, 0xc7, 0x2a, 0x14, 0xfb, 0x41, 0xdb,
	0xb2, 0x07, 0x5b, 0x37, 0xbd, 0xb3, 0xf6, 0x79, 0xa7, 0xc7, 0x78, 0x3b, 0xe7, 0x83, 0x48, 0x59,
	0x98, 0x4f, 0x35, 0xb5, 0xcd, 0xaa, 0x42, 0xbb, 0x77, 0xa6, 0x64, 0xd0, 0x36, 0x28, 0xe1, 0x42,
	0xb7, 0x39, 0x68, 0xf7, 0x07, 0x4a, 0xf6, 0xf1, 0x3f, 0x33, 0x50, 0x0c, 0xee, 0x03, 0x95, 0xa1,
	0x70, 0x46, 0x46, 0x78, 0x6a, 0x51, 0xe5, 0x01, 0x42, 0x50, 0x6b, 0xfa, 0xbe, 0xa3, 0x9b, 0x98,
	0x92, 0x2e, 0x1e, 0x12, 0x4b, 0xc9, 0xa0, 0x1d, 0xd8, 0x9c, 0xd3, 0x18, 0xb8, 0xeb, 0xe1, 0x09,
	0x51, 0xb2, 0x68, 0x13, 0xaa, 0xfc, 0xa9, 0xa8, 0x64, 0x2c, 0x38, 0x73, 0xac, 0x08, 0x49, 0x12,
	0xb6, 0xa8, 0x39, 0x91, 0x12, 0xd6, 0x98, 0x04, 0x4e, 0x7f, 0x61, 0xfa, 0xd4, 0xf1, 0x66, 0x82,
	0x9c, 0x47, 0x1b, 0x50, 0x96, 0x14, 0x66, 0x8c, 0xb2, 0x8e, 0x6a, 0x00, 0xd7, 0x1e, 0x19, 0x11,
	0x8f, 0xcf, 0x0b, 0x31, 0x6b, 0x78, 0x45, 0x56, 0x8a, 0x2c, 0xfb, 0x70, 0x59, 0x7c, 0x2e, 0x24,
	0x95, 0x4e, 0x2f, 0xa0, 0xae, 0x3b, 0x93, 0x93, 0x99, 0x39, 0x73, 0xa6, 0x2c, 0xd8, 0x26, 0x8e,
	0x41, 0x2c, 0xf1, 0xdf, 0xd9, 0x2f, 0x7f, 0x38, 0x76, 0x2c, 0x6c, 0x8f, 0x4f, 0x9e, 0x3f, 0xa3,
	0xf4, 0x44, 0x77, 0x26, 0x4f, 0x39, 0x59, 0x77, 0xac, 0xa7, 0xd8, 0x75, 0x9f, 0xc6, 0xff, 0xf4,
	0x1b, 0xae, 0xf3, 0xc5, 0x8f, 0xfe, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xb7, 0x03, 0x9d, 0x88, 0x0d,
	0x1c, 0x00, 0x00,
}
