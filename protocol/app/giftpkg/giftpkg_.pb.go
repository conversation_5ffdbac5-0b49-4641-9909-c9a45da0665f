// Code generated by protoc-gen-gogo.
// source: giftpkg_.proto
// DO NOT EDIT!

/*
	Package giftpkg is a generated protocol buffer package.

	It is generated from these files:
		giftpkg_.proto

	It has these top-level messages:
		GetMyGuildGiftpkgListReq
		GuildGiftpkg
		GetMyGuildGiftpkgListResp
		GetMyGiftpkgListReq
		MyGiftpkgSerial
		GetMyGiftpkgListResp
		FetchGuildGiftpkgReq
		FetchGuildGiftpkgResp
		FetchRedpkgReq
		FetchRedpkgResp
		SendGuildRedpkgReq
		SendGuildRedpkgResp
		GetGuildRedpkgDetailReq
		GetGuildRedpkgDetailResp
		RedPkgFetchedUserInfo
		GetGuildRedpkgFetchDetailReq
		GetGuildRedpkgFetchDetailResp
		GuildTaohaoReq
		GuildTaohaoResp
		GuildGetAppliableGiftpkgReq
		GuildGetAppliableGiftpkgResp
		GuildApplyGiftpkgReq
		GuildApplyGiftpkgResp
		GetGiftpkgDetailReq
		GetGiftpkgDetailResp
		GetGiftpkgApplyHistoryReq
		GiftpkgApply
		GetGiftpkgApplyHistoryResp
		GetGiftpkgApplyDetailReq
		GetGiftpkgApplyDetailResp
		GetGuildStorageGiftpkgListReq
		GuildStorageGiftpkg
		GetGuildStorageGiftpkgListResp
		ReportUseTaohaoReq
		ReportUseTaohaoResp
		GetGuildGiftpkgPriceReq
		GetGuildGiftpkgPriceResp
		FetchGiftpkgCostRedDiamondReq
		FetchGiftpkgCostRedDiamondResp
*/
package giftpkg

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type GIFT_PKG_STATUS int32

const (
	GIFT_PKG_STATUS_NONE              GIFT_PKG_STATUS = 0
	GIFT_PKG_STATUS_GUILD_FETCHED     GIFT_PKG_STATUS = 1
	GIFT_PKG_STATUS_USER_FETCHED      GIFT_PKG_STATUS = 2
	GIFT_PKG_STATUS_GUILD_APPLYING    GIFT_PKG_STATUS = 3
	GIFT_PKG_STATUS_OFFICAL_REJECT    GIFT_PKG_STATUS = 4
	GIFT_PKG_STATUS_OFFICAL_ACCEPT    GIFT_PKG_STATUS = 5
	GIFT_PKG_STATUS_USER_CAN_FETCHED  GIFT_PKG_STATUS = 6
	GIFT_PKG_STATUS_GIFT_PKG_EXCEED   GIFT_PKG_STATUS = 7
	GIFT_PKG_STATUS_GUILD_CAN_APPLY   GIFT_PKG_STATUS = 8
	GIFT_PKG_STATUS_TAOHAO_OVER       GIFT_PKG_STATUS = 9
	GIFT_PKG_STATUS_USER_FETCH_REDPKG GIFT_PKG_STATUS = 10
	GIFT_PKG_STATUS_PAYING            GIFT_PKG_STATUS = 11
)

var GIFT_PKG_STATUS_name = map[int32]string{
	0:  "NONE",
	1:  "GUILD_FETCHED",
	2:  "USER_FETCHED",
	3:  "GUILD_APPLYING",
	4:  "OFFICAL_REJECT",
	5:  "OFFICAL_ACCEPT",
	6:  "USER_CAN_FETCHED",
	7:  "GIFT_PKG_EXCEED",
	8:  "GUILD_CAN_APPLY",
	9:  "TAOHAO_OVER",
	10: "USER_FETCH_REDPKG",
	11: "PAYING",
}
var GIFT_PKG_STATUS_value = map[string]int32{
	"NONE":              0,
	"GUILD_FETCHED":     1,
	"USER_FETCHED":      2,
	"GUILD_APPLYING":    3,
	"OFFICAL_REJECT":    4,
	"OFFICAL_ACCEPT":    5,
	"USER_CAN_FETCHED":  6,
	"GIFT_PKG_EXCEED":   7,
	"GUILD_CAN_APPLY":   8,
	"TAOHAO_OVER":       9,
	"USER_FETCH_REDPKG": 10,
	"PAYING":            11,
}

func (x GIFT_PKG_STATUS) Enum() *GIFT_PKG_STATUS {
	p := new(GIFT_PKG_STATUS)
	*p = x
	return p
}
func (x GIFT_PKG_STATUS) String() string {
	return proto.EnumName(GIFT_PKG_STATUS_name, int32(x))
}
func (x *GIFT_PKG_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GIFT_PKG_STATUS_value, data, "GIFT_PKG_STATUS")
	if err != nil {
		return err
	}
	*x = GIFT_PKG_STATUS(value)
	return nil
}
func (GIFT_PKG_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{0} }

type GetGuildRedpkgDetailResp_STATUS int32

const (
	GetGuildRedpkgDetailResp_NONE GetGuildRedpkgDetailResp_STATUS = 0
)

var GetGuildRedpkgDetailResp_STATUS_name = map[int32]string{
	0: "NONE",
}
var GetGuildRedpkgDetailResp_STATUS_value = map[string]int32{
	"NONE": 0,
}

func (x GetGuildRedpkgDetailResp_STATUS) Enum() *GetGuildRedpkgDetailResp_STATUS {
	p := new(GetGuildRedpkgDetailResp_STATUS)
	*p = x
	return p
}
func (x GetGuildRedpkgDetailResp_STATUS) String() string {
	return proto.EnumName(GetGuildRedpkgDetailResp_STATUS_name, int32(x))
}
func (x *GetGuildRedpkgDetailResp_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GetGuildRedpkgDetailResp_STATUS_value, data, "GetGuildRedpkgDetailResp_STATUS")
	if err != nil {
		return err
	}
	*x = GetGuildRedpkgDetailResp_STATUS(value)
	return nil
}
func (GetGuildRedpkgDetailResp_STATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{13, 0}
}

type GiftpkgApply_STATUS int32

const (
	GiftpkgApply_TODO GiftpkgApply_STATUS = 0
)

var GiftpkgApply_STATUS_name = map[int32]string{
	0: "TODO",
}
var GiftpkgApply_STATUS_value = map[string]int32{
	"TODO": 0,
}

func (x GiftpkgApply_STATUS) Enum() *GiftpkgApply_STATUS {
	p := new(GiftpkgApply_STATUS)
	*p = x
	return p
}
func (x GiftpkgApply_STATUS) String() string {
	return proto.EnumName(GiftpkgApply_STATUS_name, int32(x))
}
func (x *GiftpkgApply_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GiftpkgApply_STATUS_value, data, "GiftpkgApply_STATUS")
	if err != nil {
		return err
	}
	*x = GiftpkgApply_STATUS(value)
	return nil
}
func (GiftpkgApply_STATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{26, 0}
}

// ----------------------------------------
// 查看我的公会的礼包列表
// ----------------------------------------
type GetMyGuildGiftpkgListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetMyGuildGiftpkgListReq) Reset()                    { *m = GetMyGuildGiftpkgListReq{} }
func (m *GetMyGuildGiftpkgListReq) String() string            { return proto.CompactTextString(m) }
func (*GetMyGuildGiftpkgListReq) ProtoMessage()               {}
func (*GetMyGuildGiftpkgListReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{0} }

func (m *GetMyGuildGiftpkgListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GuildGiftpkg struct {
	Pkg          *ga.GiftPackage `protobuf:"bytes,1,req,name=pkg" json:"pkg,omitempty"`
	RemainNum    uint32          `protobuf:"varint,2,req,name=remain_num,json=remainNum" json:"remain_num"`
	RecycleCount uint32          `protobuf:"varint,3,req,name=recycle_count,json=recycleCount" json:"recycle_count"`
	TotalNum     uint32          `protobuf:"varint,4,req,name=total_num,json=totalNum" json:"total_num"`
	MyStatus     uint32          `protobuf:"varint,5,req,name=my_status,json=myStatus" json:"my_status"`
	GuildStatus  uint32          `protobuf:"varint,6,req,name=guild_status,json=guildStatus" json:"guild_status"`
	MySerial     string          `protobuf:"bytes,7,opt,name=my_serial,json=mySerial" json:"my_serial"`
}

func (m *GuildGiftpkg) Reset()                    { *m = GuildGiftpkg{} }
func (m *GuildGiftpkg) String() string            { return proto.CompactTextString(m) }
func (*GuildGiftpkg) ProtoMessage()               {}
func (*GuildGiftpkg) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{1} }

func (m *GuildGiftpkg) GetPkg() *ga.GiftPackage {
	if m != nil {
		return m.Pkg
	}
	return nil
}

func (m *GuildGiftpkg) GetRemainNum() uint32 {
	if m != nil {
		return m.RemainNum
	}
	return 0
}

func (m *GuildGiftpkg) GetRecycleCount() uint32 {
	if m != nil {
		return m.RecycleCount
	}
	return 0
}

func (m *GuildGiftpkg) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

func (m *GuildGiftpkg) GetMyStatus() uint32 {
	if m != nil {
		return m.MyStatus
	}
	return 0
}

func (m *GuildGiftpkg) GetGuildStatus() uint32 {
	if m != nil {
		return m.GuildStatus
	}
	return 0
}

func (m *GuildGiftpkg) GetMySerial() string {
	if m != nil {
		return m.MySerial
	}
	return ""
}

type GetMyGuildGiftpkgListResp struct {
	BaseResp    *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GiftPkgList []*GuildGiftpkg `protobuf:"bytes,2,rep,name=gift_pkg_list,json=giftPkgList" json:"gift_pkg_list,omitempty"`
}

func (m *GetMyGuildGiftpkgListResp) Reset()         { *m = GetMyGuildGiftpkgListResp{} }
func (m *GetMyGuildGiftpkgListResp) String() string { return proto.CompactTextString(m) }
func (*GetMyGuildGiftpkgListResp) ProtoMessage()    {}
func (*GetMyGuildGiftpkgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{2}
}

func (m *GetMyGuildGiftpkgListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMyGuildGiftpkgListResp) GetGiftPkgList() []*GuildGiftpkg {
	if m != nil {
		return m.GiftPkgList
	}
	return nil
}

// ----------------------------------------
// 查看我的礼包列表 (2.9.0废弃)
// ----------------------------------------
type GetMyGiftpkgListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetMyGiftpkgListReq) Reset()                    { *m = GetMyGiftpkgListReq{} }
func (m *GetMyGiftpkgListReq) String() string            { return proto.CompactTextString(m) }
func (*GetMyGiftpkgListReq) ProtoMessage()               {}
func (*GetMyGiftpkgListReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{3} }

func (m *GetMyGiftpkgListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// 序列号
type MyGiftpkgSerial struct {
	Pkg        *ga.GiftPackage `protobuf:"bytes,1,req,name=pkg" json:"pkg,omitempty"`
	SerialCode string          `protobuf:"bytes,2,req,name=serial_code,json=serialCode" json:"serial_code"`
	Status     uint32          `protobuf:"varint,3,req,name=status" json:"status"`
}

func (m *MyGiftpkgSerial) Reset()                    { *m = MyGiftpkgSerial{} }
func (m *MyGiftpkgSerial) String() string            { return proto.CompactTextString(m) }
func (*MyGiftpkgSerial) ProtoMessage()               {}
func (*MyGiftpkgSerial) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{4} }

func (m *MyGiftpkgSerial) GetPkg() *ga.GiftPackage {
	if m != nil {
		return m.Pkg
	}
	return nil
}

func (m *MyGiftpkgSerial) GetSerialCode() string {
	if m != nil {
		return m.SerialCode
	}
	return ""
}

func (m *MyGiftpkgSerial) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetMyGiftpkgListResp struct {
	BaseResp    *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GiftPkgList []*MyGiftpkgSerial `protobuf:"bytes,2,rep,name=gift_pkg_list,json=giftPkgList" json:"gift_pkg_list,omitempty"`
}

func (m *GetMyGiftpkgListResp) Reset()                    { *m = GetMyGiftpkgListResp{} }
func (m *GetMyGiftpkgListResp) String() string            { return proto.CompactTextString(m) }
func (*GetMyGiftpkgListResp) ProtoMessage()               {}
func (*GetMyGiftpkgListResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{5} }

func (m *GetMyGiftpkgListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMyGiftpkgListResp) GetGiftPkgList() []*MyGiftpkgSerial {
	if m != nil {
		return m.GiftPkgList
	}
	return nil
}

// ----------------------------------------
// 会员领取公会礼包 (2.9.0废弃)
// ----------------------------------------
type FetchGuildGiftpkgReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *FetchGuildGiftpkgReq) Reset()                    { *m = FetchGuildGiftpkgReq{} }
func (m *FetchGuildGiftpkgReq) String() string            { return proto.CompactTextString(m) }
func (*FetchGuildGiftpkgReq) ProtoMessage()               {}
func (*FetchGuildGiftpkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{6} }

func (m *FetchGuildGiftpkgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *FetchGuildGiftpkgReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

type FetchGuildGiftpkgResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SerialCode string       `protobuf:"bytes,2,req,name=serial_code,json=serialCode" json:"serial_code"`
	GameId     uint32       `protobuf:"varint,3,req,name=game_id,json=gameId" json:"game_id"`
	GiftPkgId  uint32       `protobuf:"varint,4,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *FetchGuildGiftpkgResp) Reset()                    { *m = FetchGuildGiftpkgResp{} }
func (m *FetchGuildGiftpkgResp) String() string            { return proto.CompactTextString(m) }
func (*FetchGuildGiftpkgResp) ProtoMessage()               {}
func (*FetchGuildGiftpkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{7} }

func (m *FetchGuildGiftpkgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FetchGuildGiftpkgResp) GetSerialCode() string {
	if m != nil {
		return m.SerialCode
	}
	return ""
}

func (m *FetchGuildGiftpkgResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *FetchGuildGiftpkgResp) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

// ----------------------------------------
// 会员抢红包 (2.9.0废弃)
// ----------------------------------------
type FetchRedpkgReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	RedPkgId  uint32      `protobuf:"varint,3,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
	GuildId   uint32      `protobuf:"varint,4,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *FetchRedpkgReq) Reset()                    { *m = FetchRedpkgReq{} }
func (m *FetchRedpkgReq) String() string            { return proto.CompactTextString(m) }
func (*FetchRedpkgReq) ProtoMessage()               {}
func (*FetchRedpkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{8} }

func (m *FetchRedpkgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *FetchRedpkgReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *FetchRedpkgReq) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

func (m *FetchRedpkgReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type FetchRedpkgResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SerialCode string       `protobuf:"bytes,2,req,name=serial_code,json=serialCode" json:"serial_code"`
	GameId     uint32       `protobuf:"varint,3,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *FetchRedpkgResp) Reset()                    { *m = FetchRedpkgResp{} }
func (m *FetchRedpkgResp) String() string            { return proto.CompactTextString(m) }
func (*FetchRedpkgResp) ProtoMessage()               {}
func (*FetchRedpkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{9} }

func (m *FetchRedpkgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FetchRedpkgResp) GetSerialCode() string {
	if m != nil {
		return m.SerialCode
	}
	return ""
}

func (m *FetchRedpkgResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// ----------------------------------------
// 会长发红包 (2.9.0废弃)
// ----------------------------------------
type SendGuildRedpkgReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	Num       uint32      `protobuf:"varint,3,req,name=num" json:"num"`
	ToAccount string      `protobuf:"bytes,4,req,name=to_account,json=toAccount" json:"to_account"`
}

func (m *SendGuildRedpkgReq) Reset()                    { *m = SendGuildRedpkgReq{} }
func (m *SendGuildRedpkgReq) String() string            { return proto.CompactTextString(m) }
func (*SendGuildRedpkgReq) ProtoMessage()               {}
func (*SendGuildRedpkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{10} }

func (m *SendGuildRedpkgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendGuildRedpkgReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *SendGuildRedpkgReq) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *SendGuildRedpkgReq) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

type SendGuildRedpkgResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *SendGuildRedpkgResp) Reset()                    { *m = SendGuildRedpkgResp{} }
func (m *SendGuildRedpkgResp) String() string            { return proto.CompactTextString(m) }
func (*SendGuildRedpkgResp) ProtoMessage()               {}
func (*SendGuildRedpkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{11} }

func (m *SendGuildRedpkgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// ----------------------------------------
// 查红包详情 (2.9.0废弃)
// ----------------------------------------
type GetGuildRedpkgDetailReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	RedPkgId  uint32      `protobuf:"varint,3,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
}

func (m *GetGuildRedpkgDetailReq) Reset()                    { *m = GetGuildRedpkgDetailReq{} }
func (m *GetGuildRedpkgDetailReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildRedpkgDetailReq) ProtoMessage()               {}
func (*GetGuildRedpkgDetailReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{12} }

func (m *GetGuildRedpkgDetailReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGuildRedpkgDetailReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *GetGuildRedpkgDetailReq) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

type GetGuildRedpkgDetailResp struct {
	BaseResp      *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Status        uint32       `protobuf:"varint,2,req,name=status" json:"status"`
	LeftNum       uint32       `protobuf:"varint,3,req,name=left_num,json=leftNum" json:"left_num"`
	TotalNum      uint32       `protobuf:"varint,4,req,name=total_num,json=totalNum" json:"total_num"`
	SenderAccount string       `protobuf:"bytes,5,req,name=sender_account,json=senderAccount" json:"sender_account"`
	GiftPkgId     uint32       `protobuf:"varint,6,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	RedPkgId      uint32       `protobuf:"varint,7,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
	GameId        uint32       `protobuf:"varint,8,req,name=game_id,json=gameId" json:"game_id"`
	GiftPkgName   string       `protobuf:"bytes,9,req,name=gift_pkg_name,json=giftPkgName" json:"gift_pkg_name"`
	ExchangeBegin uint32       `protobuf:"varint,10,req,name=exchange_begin,json=exchangeBegin" json:"exchange_begin"`
	ExchangeEnd   uint32       `protobuf:"varint,11,req,name=exchange_end,json=exchangeEnd" json:"exchange_end"`
	GiftPkgIntro  string       `protobuf:"bytes,12,req,name=gift_pkg_intro,json=giftPkgIntro" json:"gift_pkg_intro"`
	SerialCode    string       `protobuf:"bytes,13,req,name=serial_code,json=serialCode" json:"serial_code"`
}

func (m *GetGuildRedpkgDetailResp) Reset()         { *m = GetGuildRedpkgDetailResp{} }
func (m *GetGuildRedpkgDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildRedpkgDetailResp) ProtoMessage()    {}
func (*GetGuildRedpkgDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{13}
}

func (m *GetGuildRedpkgDetailResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGuildRedpkgDetailResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetGuildRedpkgDetailResp) GetLeftNum() uint32 {
	if m != nil {
		return m.LeftNum
	}
	return 0
}

func (m *GetGuildRedpkgDetailResp) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

func (m *GetGuildRedpkgDetailResp) GetSenderAccount() string {
	if m != nil {
		return m.SenderAccount
	}
	return ""
}

func (m *GetGuildRedpkgDetailResp) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *GetGuildRedpkgDetailResp) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

func (m *GetGuildRedpkgDetailResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGuildRedpkgDetailResp) GetGiftPkgName() string {
	if m != nil {
		return m.GiftPkgName
	}
	return ""
}

func (m *GetGuildRedpkgDetailResp) GetExchangeBegin() uint32 {
	if m != nil {
		return m.ExchangeBegin
	}
	return 0
}

func (m *GetGuildRedpkgDetailResp) GetExchangeEnd() uint32 {
	if m != nil {
		return m.ExchangeEnd
	}
	return 0
}

func (m *GetGuildRedpkgDetailResp) GetGiftPkgIntro() string {
	if m != nil {
		return m.GiftPkgIntro
	}
	return ""
}

func (m *GetGuildRedpkgDetailResp) GetSerialCode() string {
	if m != nil {
		return m.SerialCode
	}
	return ""
}

// ----------------------------------------
// 查红包领取详情 (2.9.0废弃)
// ----------------------------------------
type RedPkgFetchedUserInfo struct {
	Account   string `protobuf:"bytes,1,req,name=account" json:"account"`
	FetchTime uint32 `protobuf:"varint,2,req,name=fetch_time,json=fetchTime" json:"fetch_time"`
}

func (m *RedPkgFetchedUserInfo) Reset()                    { *m = RedPkgFetchedUserInfo{} }
func (m *RedPkgFetchedUserInfo) String() string            { return proto.CompactTextString(m) }
func (*RedPkgFetchedUserInfo) ProtoMessage()               {}
func (*RedPkgFetchedUserInfo) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{14} }

func (m *RedPkgFetchedUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RedPkgFetchedUserInfo) GetFetchTime() uint32 {
	if m != nil {
		return m.FetchTime
	}
	return 0
}

type GetGuildRedpkgFetchDetailReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	RedPkgId  uint32      `protobuf:"varint,3,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
}

func (m *GetGuildRedpkgFetchDetailReq) Reset()         { *m = GetGuildRedpkgFetchDetailReq{} }
func (m *GetGuildRedpkgFetchDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildRedpkgFetchDetailReq) ProtoMessage()    {}
func (*GetGuildRedpkgFetchDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{15}
}

func (m *GetGuildRedpkgFetchDetailReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGuildRedpkgFetchDetailReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *GetGuildRedpkgFetchDetailReq) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

type GetGuildRedpkgFetchDetailResp struct {
	BaseResp         *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	FetchAccountList []*RedPkgFetchedUserInfo `protobuf:"bytes,2,rep,name=fetch_account_list,json=fetchAccountList" json:"fetch_account_list,omitempty"`
	LeftNum          uint32                   `protobuf:"varint,3,req,name=left_num,json=leftNum" json:"left_num"`
	TotalNum         uint32                   `protobuf:"varint,4,req,name=total_num,json=totalNum" json:"total_num"`
	GiftPkgId        uint32                   `protobuf:"varint,5,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	RedPkgId         uint32                   `protobuf:"varint,6,req,name=red_pkg_id,json=redPkgId" json:"red_pkg_id"`
}

func (m *GetGuildRedpkgFetchDetailResp) Reset()         { *m = GetGuildRedpkgFetchDetailResp{} }
func (m *GetGuildRedpkgFetchDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildRedpkgFetchDetailResp) ProtoMessage()    {}
func (*GetGuildRedpkgFetchDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{16}
}

func (m *GetGuildRedpkgFetchDetailResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGuildRedpkgFetchDetailResp) GetFetchAccountList() []*RedPkgFetchedUserInfo {
	if m != nil {
		return m.FetchAccountList
	}
	return nil
}

func (m *GetGuildRedpkgFetchDetailResp) GetLeftNum() uint32 {
	if m != nil {
		return m.LeftNum
	}
	return 0
}

func (m *GetGuildRedpkgFetchDetailResp) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

func (m *GetGuildRedpkgFetchDetailResp) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *GetGuildRedpkgFetchDetailResp) GetRedPkgId() uint32 {
	if m != nil {
		return m.RedPkgId
	}
	return 0
}

// ----------------------------------------
// 淘号 (2.9.0废弃)
// ----------------------------------------
type GuildTaohaoReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *GuildTaohaoReq) Reset()                    { *m = GuildTaohaoReq{} }
func (m *GuildTaohaoReq) String() string            { return proto.CompactTextString(m) }
func (*GuildTaohaoReq) ProtoMessage()               {}
func (*GuildTaohaoReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{17} }

func (m *GuildTaohaoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildTaohaoReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

type GuildTaohaoResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SerialCode string       `protobuf:"bytes,2,req,name=serial_code,json=serialCode" json:"serial_code"`
	GameId     uint32       `protobuf:"varint,3,req,name=game_id,json=gameId" json:"game_id"`
	GiftPkgId  uint32       `protobuf:"varint,4,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *GuildTaohaoResp) Reset()                    { *m = GuildTaohaoResp{} }
func (m *GuildTaohaoResp) String() string            { return proto.CompactTextString(m) }
func (*GuildTaohaoResp) ProtoMessage()               {}
func (*GuildTaohaoResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{18} }

func (m *GuildTaohaoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildTaohaoResp) GetSerialCode() string {
	if m != nil {
		return m.SerialCode
	}
	return ""
}

func (m *GuildTaohaoResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GuildTaohaoResp) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

// ----------------------------------------
// 查询公会可申请的礼包 (2.9.0废弃)
// ----------------------------------------
type GuildGetAppliableGiftpkgReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameId  []uint32    `protobuf:"varint,2,rep,name=game_id,json=gameId" json:"game_id,omitempty"`
}

func (m *GuildGetAppliableGiftpkgReq) Reset()         { *m = GuildGetAppliableGiftpkgReq{} }
func (m *GuildGetAppliableGiftpkgReq) String() string { return proto.CompactTextString(m) }
func (*GuildGetAppliableGiftpkgReq) ProtoMessage()    {}
func (*GuildGetAppliableGiftpkgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{19}
}

func (m *GuildGetAppliableGiftpkgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildGetAppliableGiftpkgReq) GetGameId() []uint32 {
	if m != nil {
		return m.GameId
	}
	return nil
}

type GuildGetAppliableGiftpkgResp struct {
	BaseResp    *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GiftPkgList []*GuildGiftpkg `protobuf:"bytes,2,rep,name=gift_pkg_list,json=giftPkgList" json:"gift_pkg_list,omitempty"`
}

func (m *GuildGetAppliableGiftpkgResp) Reset()         { *m = GuildGetAppliableGiftpkgResp{} }
func (m *GuildGetAppliableGiftpkgResp) String() string { return proto.CompactTextString(m) }
func (*GuildGetAppliableGiftpkgResp) ProtoMessage()    {}
func (*GuildGetAppliableGiftpkgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{20}
}

func (m *GuildGetAppliableGiftpkgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildGetAppliableGiftpkgResp) GetGiftPkgList() []*GuildGiftpkg {
	if m != nil {
		return m.GiftPkgList
	}
	return nil
}

// ----------------------------------------
// 公会申请礼包 (2.9.0废弃)
// ----------------------------------------
type GuildApplyGiftpkgReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *GuildApplyGiftpkgReq) Reset()                    { *m = GuildApplyGiftpkgReq{} }
func (m *GuildApplyGiftpkgReq) String() string            { return proto.CompactTextString(m) }
func (*GuildApplyGiftpkgReq) ProtoMessage()               {}
func (*GuildApplyGiftpkgReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{21} }

func (m *GuildApplyGiftpkgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildApplyGiftpkgReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

type GuildApplyGiftpkgResp struct {
	BaseResp  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GiftPkgId uint32       `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *GuildApplyGiftpkgResp) Reset()                    { *m = GuildApplyGiftpkgResp{} }
func (m *GuildApplyGiftpkgResp) String() string            { return proto.CompactTextString(m) }
func (*GuildApplyGiftpkgResp) ProtoMessage()               {}
func (*GuildApplyGiftpkgResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{22} }

func (m *GuildApplyGiftpkgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildApplyGiftpkgResp) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

// ----------------------------------------
// 公会查询礼包详情 (2.9.0废弃)
// ----------------------------------------
type GetGiftpkgDetailReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftpkgId uint32      `protobuf:"varint,2,req,name=giftpkg_id,json=giftpkgId" json:"giftpkg_id"`
}

func (m *GetGiftpkgDetailReq) Reset()                    { *m = GetGiftpkgDetailReq{} }
func (m *GetGiftpkgDetailReq) String() string            { return proto.CompactTextString(m) }
func (*GetGiftpkgDetailReq) ProtoMessage()               {}
func (*GetGiftpkgDetailReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{23} }

func (m *GetGiftpkgDetailReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGiftpkgDetailReq) GetGiftpkgId() uint32 {
	if m != nil {
		return m.GiftpkgId
	}
	return 0
}

type GetGiftpkgDetailResp struct {
	BaseResp *ga.BaseResp  `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Pkg      *GuildGiftpkg `protobuf:"bytes,2,req,name=pkg" json:"pkg,omitempty"`
	Content  string        `protobuf:"bytes,3,req,name=content" json:"content"`
	Usage    string        `protobuf:"bytes,4,req,name=usage" json:"usage"`
}

func (m *GetGiftpkgDetailResp) Reset()                    { *m = GetGiftpkgDetailResp{} }
func (m *GetGiftpkgDetailResp) String() string            { return proto.CompactTextString(m) }
func (*GetGiftpkgDetailResp) ProtoMessage()               {}
func (*GetGiftpkgDetailResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{24} }

func (m *GetGiftpkgDetailResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGiftpkgDetailResp) GetPkg() *GuildGiftpkg {
	if m != nil {
		return m.Pkg
	}
	return nil
}

func (m *GetGiftpkgDetailResp) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GetGiftpkgDetailResp) GetUsage() string {
	if m != nil {
		return m.Usage
	}
	return ""
}

// ----------------------------------------
// 公会查询申请礼包历史记录 (2.9.0废弃)
// ----------------------------------------
type GetGiftpkgApplyHistoryReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetGiftpkgApplyHistoryReq) Reset()         { *m = GetGiftpkgApplyHistoryReq{} }
func (m *GetGiftpkgApplyHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGiftpkgApplyHistoryReq) ProtoMessage()    {}
func (*GetGiftpkgApplyHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{25}
}

func (m *GetGiftpkgApplyHistoryReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GiftpkgApply struct {
	Pkg       *ga.GiftPackage `protobuf:"bytes,1,req,name=pkg" json:"pkg,omitempty"`
	ApplyId   uint32          `protobuf:"varint,2,req,name=apply_id,json=applyId" json:"apply_id"`
	Status    uint32          `protobuf:"varint,3,req,name=status" json:"status"`
	ApplyTime uint32          `protobuf:"varint,4,req,name=apply_time,json=applyTime" json:"apply_time"`
}

func (m *GiftpkgApply) Reset()                    { *m = GiftpkgApply{} }
func (m *GiftpkgApply) String() string            { return proto.CompactTextString(m) }
func (*GiftpkgApply) ProtoMessage()               {}
func (*GiftpkgApply) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{26} }

func (m *GiftpkgApply) GetPkg() *ga.GiftPackage {
	if m != nil {
		return m.Pkg
	}
	return nil
}

func (m *GiftpkgApply) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *GiftpkgApply) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GiftpkgApply) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

type GetGiftpkgApplyHistoryResp struct {
	BaseResp  *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ApplyList []*GiftpkgApply `protobuf:"bytes,2,rep,name=apply_list,json=applyList" json:"apply_list,omitempty"`
}

func (m *GetGiftpkgApplyHistoryResp) Reset()         { *m = GetGiftpkgApplyHistoryResp{} }
func (m *GetGiftpkgApplyHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGiftpkgApplyHistoryResp) ProtoMessage()    {}
func (*GetGiftpkgApplyHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{27}
}

func (m *GetGiftpkgApplyHistoryResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGiftpkgApplyHistoryResp) GetApplyList() []*GiftpkgApply {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

type GetGiftpkgApplyDetailReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ApplyId uint32      `protobuf:"varint,2,req,name=apply_id,json=applyId" json:"apply_id"`
}

func (m *GetGiftpkgApplyDetailReq) Reset()         { *m = GetGiftpkgApplyDetailReq{} }
func (m *GetGiftpkgApplyDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetGiftpkgApplyDetailReq) ProtoMessage()    {}
func (*GetGiftpkgApplyDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{28}
}

func (m *GetGiftpkgApplyDetailReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGiftpkgApplyDetailReq) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

type GetGiftpkgApplyDetailResp struct {
	BaseResp *ga.BaseResp  `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Apply    *GiftpkgApply `protobuf:"bytes,2,req,name=apply" json:"apply,omitempty"`
	Content  string        `protobuf:"bytes,3,req,name=content" json:"content"`
	Usage    string        `protobuf:"bytes,4,req,name=usage" json:"usage"`
}

func (m *GetGiftpkgApplyDetailResp) Reset()         { *m = GetGiftpkgApplyDetailResp{} }
func (m *GetGiftpkgApplyDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetGiftpkgApplyDetailResp) ProtoMessage()    {}
func (*GetGiftpkgApplyDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{29}
}

func (m *GetGiftpkgApplyDetailResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGiftpkgApplyDetailResp) GetApply() *GiftpkgApply {
	if m != nil {
		return m.Apply
	}
	return nil
}

func (m *GetGiftpkgApplyDetailResp) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GetGiftpkgApplyDetailResp) GetUsage() string {
	if m != nil {
		return m.Usage
	}
	return ""
}

// ----------------------------------------
// 查看我的公会仓库的礼包列表 (2.9.0废弃)
// ----------------------------------------
type GetGuildStorageGiftpkgListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetGuildStorageGiftpkgListReq) Reset()         { *m = GetGuildStorageGiftpkgListReq{} }
func (m *GetGuildStorageGiftpkgListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildStorageGiftpkgListReq) ProtoMessage()    {}
func (*GetGuildStorageGiftpkgListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{30}
}

func (m *GetGuildStorageGiftpkgListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GuildStorageGiftpkg struct {
	Pkg           *ga.GiftPackage `protobuf:"bytes,1,req,name=pkg" json:"pkg,omitempty"`
	RemainNum     uint32          `protobuf:"varint,2,req,name=remain_num,json=remainNum" json:"remain_num"`
	TotalNum      uint32          `protobuf:"varint,3,req,name=total_num,json=totalNum" json:"total_num"`
	ApplyPassTime uint32          `protobuf:"varint,4,req,name=apply_pass_time,json=applyPassTime" json:"apply_pass_time"`
}

func (m *GuildStorageGiftpkg) Reset()                    { *m = GuildStorageGiftpkg{} }
func (m *GuildStorageGiftpkg) String() string            { return proto.CompactTextString(m) }
func (*GuildStorageGiftpkg) ProtoMessage()               {}
func (*GuildStorageGiftpkg) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{31} }

func (m *GuildStorageGiftpkg) GetPkg() *ga.GiftPackage {
	if m != nil {
		return m.Pkg
	}
	return nil
}

func (m *GuildStorageGiftpkg) GetRemainNum() uint32 {
	if m != nil {
		return m.RemainNum
	}
	return 0
}

func (m *GuildStorageGiftpkg) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

func (m *GuildStorageGiftpkg) GetApplyPassTime() uint32 {
	if m != nil {
		return m.ApplyPassTime
	}
	return 0
}

type GetGuildStorageGiftpkgListResp struct {
	BaseResp    *ga.BaseResp           `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GiftPkgList []*GuildStorageGiftpkg `protobuf:"bytes,2,rep,name=gift_pkg_list,json=giftPkgList" json:"gift_pkg_list,omitempty"`
}

func (m *GetGuildStorageGiftpkgListResp) Reset()         { *m = GetGuildStorageGiftpkgListResp{} }
func (m *GetGuildStorageGiftpkgListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildStorageGiftpkgListResp) ProtoMessage()    {}
func (*GetGuildStorageGiftpkgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{32}
}

func (m *GetGuildStorageGiftpkgListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGuildStorageGiftpkgListResp) GetGiftPkgList() []*GuildStorageGiftpkg {
	if m != nil {
		return m.GiftPkgList
	}
	return nil
}

// ----------------------------------------
// 上报使用淘号 (2.9.0废弃)
// ----------------------------------------
type ReportUseTaohaoReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId  uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	GuildId    uint32      `protobuf:"varint,3,req,name=guild_id,json=guildId" json:"guild_id"`
	SerialCode string      `protobuf:"bytes,4,req,name=serial_code,json=serialCode" json:"serial_code"`
}

func (m *ReportUseTaohaoReq) Reset()                    { *m = ReportUseTaohaoReq{} }
func (m *ReportUseTaohaoReq) String() string            { return proto.CompactTextString(m) }
func (*ReportUseTaohaoReq) ProtoMessage()               {}
func (*ReportUseTaohaoReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{33} }

func (m *ReportUseTaohaoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportUseTaohaoReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *ReportUseTaohaoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ReportUseTaohaoReq) GetSerialCode() string {
	if m != nil {
		return m.SerialCode
	}
	return ""
}

type ReportUseTaohaoResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ReportUseTaohaoResp) Reset()                    { *m = ReportUseTaohaoResp{} }
func (m *ReportUseTaohaoResp) String() string            { return proto.CompactTextString(m) }
func (*ReportUseTaohaoResp) ProtoMessage()               {}
func (*ReportUseTaohaoResp) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{34} }

func (m *ReportUseTaohaoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// ----------------------------------------
// 查公会礼包价格 (2.9.0废弃)
// ----------------------------------------
type GetGuildGiftpkgPriceReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
}

func (m *GetGuildGiftpkgPriceReq) Reset()                    { *m = GetGuildGiftpkgPriceReq{} }
func (m *GetGuildGiftpkgPriceReq) String() string            { return proto.CompactTextString(m) }
func (*GetGuildGiftpkgPriceReq) ProtoMessage()               {}
func (*GetGuildGiftpkgPriceReq) Descriptor() ([]byte, []int) { return fileDescriptorGiftpkg_, []int{35} }

func (m *GetGuildGiftpkgPriceReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGuildGiftpkgPriceReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

type GetGuildGiftpkgPriceResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GiftPkgId  uint32       `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	RedDiamond uint32       `protobuf:"varint,3,req,name=red_diamond,json=redDiamond" json:"red_diamond"`
}

func (m *GetGuildGiftpkgPriceResp) Reset()         { *m = GetGuildGiftpkgPriceResp{} }
func (m *GetGuildGiftpkgPriceResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildGiftpkgPriceResp) ProtoMessage()    {}
func (*GetGuildGiftpkgPriceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{36}
}

func (m *GetGuildGiftpkgPriceResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGuildGiftpkgPriceResp) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *GetGuildGiftpkgPriceResp) GetRedDiamond() uint32 {
	if m != nil {
		return m.RedDiamond
	}
	return 0
}

// ----------------------------------------
// 消耗红钻领礼包 (2.9.0废弃)
// ----------------------------------------
type FetchGiftpkgCostRedDiamondReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GiftPkgId  uint32      `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	RedDiamond uint32      `protobuf:"varint,3,req,name=red_diamond,json=redDiamond" json:"red_diamond"`
}

func (m *FetchGiftpkgCostRedDiamondReq) Reset()         { *m = FetchGiftpkgCostRedDiamondReq{} }
func (m *FetchGiftpkgCostRedDiamondReq) String() string { return proto.CompactTextString(m) }
func (*FetchGiftpkgCostRedDiamondReq) ProtoMessage()    {}
func (*FetchGiftpkgCostRedDiamondReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{37}
}

func (m *FetchGiftpkgCostRedDiamondReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *FetchGiftpkgCostRedDiamondReq) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *FetchGiftpkgCostRedDiamondReq) GetRedDiamond() uint32 {
	if m != nil {
		return m.RedDiamond
	}
	return 0
}

type FetchGiftpkgCostRedDiamondResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GiftPkgId  uint32       `protobuf:"varint,2,req,name=gift_pkg_id,json=giftPkgId" json:"gift_pkg_id"`
	SerialCode string       `protobuf:"bytes,3,req,name=serial_code,json=serialCode" json:"serial_code"`
	GameId     uint32       `protobuf:"varint,4,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *FetchGiftpkgCostRedDiamondResp) Reset()         { *m = FetchGiftpkgCostRedDiamondResp{} }
func (m *FetchGiftpkgCostRedDiamondResp) String() string { return proto.CompactTextString(m) }
func (*FetchGiftpkgCostRedDiamondResp) ProtoMessage()    {}
func (*FetchGiftpkgCostRedDiamondResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGiftpkg_, []int{38}
}

func (m *FetchGiftpkgCostRedDiamondResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FetchGiftpkgCostRedDiamondResp) GetGiftPkgId() uint32 {
	if m != nil {
		return m.GiftPkgId
	}
	return 0
}

func (m *FetchGiftpkgCostRedDiamondResp) GetSerialCode() string {
	if m != nil {
		return m.SerialCode
	}
	return ""
}

func (m *FetchGiftpkgCostRedDiamondResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func init() {
	proto.RegisterType((*GetMyGuildGiftpkgListReq)(nil), "ga.GetMyGuildGiftpkgListReq")
	proto.RegisterType((*GuildGiftpkg)(nil), "ga.GuildGiftpkg")
	proto.RegisterType((*GetMyGuildGiftpkgListResp)(nil), "ga.GetMyGuildGiftpkgListResp")
	proto.RegisterType((*GetMyGiftpkgListReq)(nil), "ga.GetMyGiftpkgListReq")
	proto.RegisterType((*MyGiftpkgSerial)(nil), "ga.MyGiftpkgSerial")
	proto.RegisterType((*GetMyGiftpkgListResp)(nil), "ga.GetMyGiftpkgListResp")
	proto.RegisterType((*FetchGuildGiftpkgReq)(nil), "ga.FetchGuildGiftpkgReq")
	proto.RegisterType((*FetchGuildGiftpkgResp)(nil), "ga.FetchGuildGiftpkgResp")
	proto.RegisterType((*FetchRedpkgReq)(nil), "ga.FetchRedpkgReq")
	proto.RegisterType((*FetchRedpkgResp)(nil), "ga.FetchRedpkgResp")
	proto.RegisterType((*SendGuildRedpkgReq)(nil), "ga.SendGuildRedpkgReq")
	proto.RegisterType((*SendGuildRedpkgResp)(nil), "ga.SendGuildRedpkgResp")
	proto.RegisterType((*GetGuildRedpkgDetailReq)(nil), "ga.GetGuildRedpkgDetailReq")
	proto.RegisterType((*GetGuildRedpkgDetailResp)(nil), "ga.GetGuildRedpkgDetailResp")
	proto.RegisterType((*RedPkgFetchedUserInfo)(nil), "ga.RedPkgFetchedUserInfo")
	proto.RegisterType((*GetGuildRedpkgFetchDetailReq)(nil), "ga.GetGuildRedpkgFetchDetailReq")
	proto.RegisterType((*GetGuildRedpkgFetchDetailResp)(nil), "ga.GetGuildRedpkgFetchDetailResp")
	proto.RegisterType((*GuildTaohaoReq)(nil), "ga.GuildTaohaoReq")
	proto.RegisterType((*GuildTaohaoResp)(nil), "ga.GuildTaohaoResp")
	proto.RegisterType((*GuildGetAppliableGiftpkgReq)(nil), "ga.GuildGetAppliableGiftpkgReq")
	proto.RegisterType((*GuildGetAppliableGiftpkgResp)(nil), "ga.GuildGetAppliableGiftpkgResp")
	proto.RegisterType((*GuildApplyGiftpkgReq)(nil), "ga.GuildApplyGiftpkgReq")
	proto.RegisterType((*GuildApplyGiftpkgResp)(nil), "ga.GuildApplyGiftpkgResp")
	proto.RegisterType((*GetGiftpkgDetailReq)(nil), "ga.GetGiftpkgDetailReq")
	proto.RegisterType((*GetGiftpkgDetailResp)(nil), "ga.GetGiftpkgDetailResp")
	proto.RegisterType((*GetGiftpkgApplyHistoryReq)(nil), "ga.GetGiftpkgApplyHistoryReq")
	proto.RegisterType((*GiftpkgApply)(nil), "ga.GiftpkgApply")
	proto.RegisterType((*GetGiftpkgApplyHistoryResp)(nil), "ga.GetGiftpkgApplyHistoryResp")
	proto.RegisterType((*GetGiftpkgApplyDetailReq)(nil), "ga.GetGiftpkgApplyDetailReq")
	proto.RegisterType((*GetGiftpkgApplyDetailResp)(nil), "ga.GetGiftpkgApplyDetailResp")
	proto.RegisterType((*GetGuildStorageGiftpkgListReq)(nil), "ga.GetGuildStorageGiftpkgListReq")
	proto.RegisterType((*GuildStorageGiftpkg)(nil), "ga.GuildStorageGiftpkg")
	proto.RegisterType((*GetGuildStorageGiftpkgListResp)(nil), "ga.GetGuildStorageGiftpkgListResp")
	proto.RegisterType((*ReportUseTaohaoReq)(nil), "ga.ReportUseTaohaoReq")
	proto.RegisterType((*ReportUseTaohaoResp)(nil), "ga.ReportUseTaohaoResp")
	proto.RegisterType((*GetGuildGiftpkgPriceReq)(nil), "ga.GetGuildGiftpkgPriceReq")
	proto.RegisterType((*GetGuildGiftpkgPriceResp)(nil), "ga.GetGuildGiftpkgPriceResp")
	proto.RegisterType((*FetchGiftpkgCostRedDiamondReq)(nil), "ga.FetchGiftpkgCostRedDiamondReq")
	proto.RegisterType((*FetchGiftpkgCostRedDiamondResp)(nil), "ga.FetchGiftpkgCostRedDiamondResp")
	proto.RegisterEnum("ga.GIFT_PKG_STATUS", GIFT_PKG_STATUS_name, GIFT_PKG_STATUS_value)
	proto.RegisterEnum("ga.GetGuildRedpkgDetailResp_STATUS", GetGuildRedpkgDetailResp_STATUS_name, GetGuildRedpkgDetailResp_STATUS_value)
	proto.RegisterEnum("ga.GiftpkgApply_STATUS", GiftpkgApply_STATUS_name, GiftpkgApply_STATUS_value)
}
func (m *GetMyGuildGiftpkgListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMyGuildGiftpkgListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GuildGiftpkg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildGiftpkg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Pkg == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Pkg.Size()))
		n2, err := m.Pkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RemainNum))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RecycleCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.TotalNum))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.MyStatus))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GuildStatus))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.MySerial)))
	i += copy(dAtA[i:], m.MySerial)
	return i, nil
}

func (m *GetMyGuildGiftpkgListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMyGuildGiftpkgListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n3, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if len(m.GiftPkgList) > 0 {
		for _, msg := range m.GiftPkgList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGiftpkg_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetMyGiftpkgListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMyGiftpkgListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n4, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *MyGiftpkgSerial) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MyGiftpkgSerial) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Pkg == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Pkg.Size()))
		n5, err := m.Pkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.SerialCode)))
	i += copy(dAtA[i:], m.SerialCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Status))
	return i, nil
}

func (m *GetMyGiftpkgListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMyGiftpkgListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if len(m.GiftPkgList) > 0 {
		for _, msg := range m.GiftPkgList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGiftpkg_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FetchGuildGiftpkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchGuildGiftpkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *FetchGuildGiftpkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchGuildGiftpkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.SerialCode)))
	i += copy(dAtA[i:], m.SerialCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *FetchRedpkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchRedpkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RedPkgId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *FetchRedpkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchRedpkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.SerialCode)))
	i += copy(dAtA[i:], m.SerialCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *SendGuildRedpkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendGuildRedpkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Num))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.ToAccount)))
	i += copy(dAtA[i:], m.ToAccount)
	return i, nil
}

func (m *SendGuildRedpkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendGuildRedpkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *GetGuildRedpkgDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildRedpkgDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n13, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RedPkgId))
	return i, nil
}

func (m *GetGuildRedpkgDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildRedpkgDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n14, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.LeftNum))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.TotalNum))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.SenderAccount)))
	i += copy(dAtA[i:], m.SenderAccount)
	dAtA[i] = 0x30
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RedPkgId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.GiftPkgName)))
	i += copy(dAtA[i:], m.GiftPkgName)
	dAtA[i] = 0x50
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.ExchangeBegin))
	dAtA[i] = 0x58
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.ExchangeEnd))
	dAtA[i] = 0x62
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.GiftPkgIntro)))
	i += copy(dAtA[i:], m.GiftPkgIntro)
	dAtA[i] = 0x6a
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.SerialCode)))
	i += copy(dAtA[i:], m.SerialCode)
	return i, nil
}

func (m *RedPkgFetchedUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RedPkgFetchedUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.FetchTime))
	return i, nil
}

func (m *GetGuildRedpkgFetchDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildRedpkgFetchDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n15, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RedPkgId))
	return i, nil
}

func (m *GetGuildRedpkgFetchDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildRedpkgFetchDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n16, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	if len(m.FetchAccountList) > 0 {
		for _, msg := range m.FetchAccountList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGiftpkg_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.LeftNum))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.TotalNum))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RedPkgId))
	return i, nil
}

func (m *GuildTaohaoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildTaohaoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n17, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *GuildTaohaoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildTaohaoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n18, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.SerialCode)))
	i += copy(dAtA[i:], m.SerialCode)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *GuildGetAppliableGiftpkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildGetAppliableGiftpkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	if len(m.GameId) > 0 {
		for _, num := range m.GameId {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGiftpkg_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GuildGetAppliableGiftpkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildGetAppliableGiftpkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n20, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	if len(m.GiftPkgList) > 0 {
		for _, msg := range m.GiftPkgList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGiftpkg_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GuildApplyGiftpkgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildApplyGiftpkgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n21, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *GuildApplyGiftpkgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildApplyGiftpkgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n22, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *GetGiftpkgDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftpkgDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n23, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftpkgId))
	return i, nil
}

func (m *GetGiftpkgDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftpkgDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n24, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	if m.Pkg == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Pkg.Size()))
		n25, err := m.Pkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.Usage)))
	i += copy(dAtA[i:], m.Usage)
	return i, nil
}

func (m *GetGiftpkgApplyHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftpkgApplyHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n26, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	return i, nil
}

func (m *GiftpkgApply) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GiftpkgApply) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Pkg == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Pkg.Size()))
		n27, err := m.Pkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.ApplyId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.ApplyTime))
	return i, nil
}

func (m *GetGiftpkgApplyHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftpkgApplyHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n28, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	if len(m.ApplyList) > 0 {
		for _, msg := range m.ApplyList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGiftpkg_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGiftpkgApplyDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftpkgApplyDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n29, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.ApplyId))
	return i, nil
}

func (m *GetGiftpkgApplyDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGiftpkgApplyDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n30, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	if m.Apply == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Apply.Size()))
		n31, err := m.Apply.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.Usage)))
	i += copy(dAtA[i:], m.Usage)
	return i, nil
}

func (m *GetGuildStorageGiftpkgListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildStorageGiftpkgListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n32, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n32
	}
	return i, nil
}

func (m *GuildStorageGiftpkg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildStorageGiftpkg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Pkg == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.Pkg.Size()))
		n33, err := m.Pkg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n33
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RemainNum))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.TotalNum))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.ApplyPassTime))
	return i, nil
}

func (m *GetGuildStorageGiftpkgListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildStorageGiftpkgListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n34, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n34
	}
	if len(m.GiftPkgList) > 0 {
		for _, msg := range m.GiftPkgList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGiftpkg_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ReportUseTaohaoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportUseTaohaoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n35, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n35
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.SerialCode)))
	i += copy(dAtA[i:], m.SerialCode)
	return i, nil
}

func (m *ReportUseTaohaoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportUseTaohaoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n36, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n36
	}
	return i, nil
}

func (m *GetGuildGiftpkgPriceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftpkgPriceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n37, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n37
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	return i, nil
}

func (m *GetGuildGiftpkgPriceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildGiftpkgPriceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n38, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n38
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RedDiamond))
	return i, nil
}

func (m *FetchGiftpkgCostRedDiamondReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchGiftpkgCostRedDiamondReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseReq.Size()))
		n39, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n39
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.RedDiamond))
	return i, nil
}

func (m *FetchGiftpkgCostRedDiamondResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchGiftpkgCostRedDiamondResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGiftpkg_(dAtA, i, uint64(m.BaseResp.Size()))
		n40, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n40
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GiftPkgId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(len(m.SerialCode)))
	i += copy(dAtA[i:], m.SerialCode)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGiftpkg_(dAtA, i, uint64(m.GameId))
	return i, nil
}

func encodeFixed64Giftpkg_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Giftpkg_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGiftpkg_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetMyGuildGiftpkgListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	return n
}

func (m *GuildGiftpkg) Size() (n int) {
	var l int
	_ = l
	if m.Pkg != nil {
		l = m.Pkg.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.RemainNum))
	n += 1 + sovGiftpkg_(uint64(m.RecycleCount))
	n += 1 + sovGiftpkg_(uint64(m.TotalNum))
	n += 1 + sovGiftpkg_(uint64(m.MyStatus))
	n += 1 + sovGiftpkg_(uint64(m.GuildStatus))
	l = len(m.MySerial)
	n += 1 + l + sovGiftpkg_(uint64(l))
	return n
}

func (m *GetMyGuildGiftpkgListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	if len(m.GiftPkgList) > 0 {
		for _, e := range m.GiftPkgList {
			l = e.Size()
			n += 1 + l + sovGiftpkg_(uint64(l))
		}
	}
	return n
}

func (m *GetMyGiftpkgListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	return n
}

func (m *MyGiftpkgSerial) Size() (n int) {
	var l int
	_ = l
	if m.Pkg != nil {
		l = m.Pkg.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	l = len(m.SerialCode)
	n += 1 + l + sovGiftpkg_(uint64(l))
	n += 1 + sovGiftpkg_(uint64(m.Status))
	return n
}

func (m *GetMyGiftpkgListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	if len(m.GiftPkgList) > 0 {
		for _, e := range m.GiftPkgList {
			l = e.Size()
			n += 1 + l + sovGiftpkg_(uint64(l))
		}
	}
	return n
}

func (m *FetchGuildGiftpkgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	return n
}

func (m *FetchGuildGiftpkgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	l = len(m.SerialCode)
	n += 1 + l + sovGiftpkg_(uint64(l))
	n += 1 + sovGiftpkg_(uint64(m.GameId))
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	return n
}

func (m *FetchRedpkgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg_(uint64(m.RedPkgId))
	n += 1 + sovGiftpkg_(uint64(m.GuildId))
	return n
}

func (m *FetchRedpkgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	l = len(m.SerialCode)
	n += 1 + l + sovGiftpkg_(uint64(l))
	n += 1 + sovGiftpkg_(uint64(m.GameId))
	return n
}

func (m *SendGuildRedpkgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg_(uint64(m.Num))
	l = len(m.ToAccount)
	n += 1 + l + sovGiftpkg_(uint64(l))
	return n
}

func (m *SendGuildRedpkgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	return n
}

func (m *GetGuildRedpkgDetailReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg_(uint64(m.RedPkgId))
	return n
}

func (m *GetGuildRedpkgDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.Status))
	n += 1 + sovGiftpkg_(uint64(m.LeftNum))
	n += 1 + sovGiftpkg_(uint64(m.TotalNum))
	l = len(m.SenderAccount)
	n += 1 + l + sovGiftpkg_(uint64(l))
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg_(uint64(m.RedPkgId))
	n += 1 + sovGiftpkg_(uint64(m.GameId))
	l = len(m.GiftPkgName)
	n += 1 + l + sovGiftpkg_(uint64(l))
	n += 1 + sovGiftpkg_(uint64(m.ExchangeBegin))
	n += 1 + sovGiftpkg_(uint64(m.ExchangeEnd))
	l = len(m.GiftPkgIntro)
	n += 1 + l + sovGiftpkg_(uint64(l))
	l = len(m.SerialCode)
	n += 1 + l + sovGiftpkg_(uint64(l))
	return n
}

func (m *RedPkgFetchedUserInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Account)
	n += 1 + l + sovGiftpkg_(uint64(l))
	n += 1 + sovGiftpkg_(uint64(m.FetchTime))
	return n
}

func (m *GetGuildRedpkgFetchDetailReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg_(uint64(m.RedPkgId))
	return n
}

func (m *GetGuildRedpkgFetchDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	if len(m.FetchAccountList) > 0 {
		for _, e := range m.FetchAccountList {
			l = e.Size()
			n += 1 + l + sovGiftpkg_(uint64(l))
		}
	}
	n += 1 + sovGiftpkg_(uint64(m.LeftNum))
	n += 1 + sovGiftpkg_(uint64(m.TotalNum))
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg_(uint64(m.RedPkgId))
	return n
}

func (m *GuildTaohaoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	return n
}

func (m *GuildTaohaoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	l = len(m.SerialCode)
	n += 1 + l + sovGiftpkg_(uint64(l))
	n += 1 + sovGiftpkg_(uint64(m.GameId))
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	return n
}

func (m *GuildGetAppliableGiftpkgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	if len(m.GameId) > 0 {
		for _, e := range m.GameId {
			n += 1 + sovGiftpkg_(uint64(e))
		}
	}
	return n
}

func (m *GuildGetAppliableGiftpkgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	if len(m.GiftPkgList) > 0 {
		for _, e := range m.GiftPkgList {
			l = e.Size()
			n += 1 + l + sovGiftpkg_(uint64(l))
		}
	}
	return n
}

func (m *GuildApplyGiftpkgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	return n
}

func (m *GuildApplyGiftpkgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	return n
}

func (m *GetGiftpkgDetailReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftpkgId))
	return n
}

func (m *GetGiftpkgDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	if m.Pkg != nil {
		l = m.Pkg.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	l = len(m.Content)
	n += 1 + l + sovGiftpkg_(uint64(l))
	l = len(m.Usage)
	n += 1 + l + sovGiftpkg_(uint64(l))
	return n
}

func (m *GetGiftpkgApplyHistoryReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	return n
}

func (m *GiftpkgApply) Size() (n int) {
	var l int
	_ = l
	if m.Pkg != nil {
		l = m.Pkg.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.ApplyId))
	n += 1 + sovGiftpkg_(uint64(m.Status))
	n += 1 + sovGiftpkg_(uint64(m.ApplyTime))
	return n
}

func (m *GetGiftpkgApplyHistoryResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	if len(m.ApplyList) > 0 {
		for _, e := range m.ApplyList {
			l = e.Size()
			n += 1 + l + sovGiftpkg_(uint64(l))
		}
	}
	return n
}

func (m *GetGiftpkgApplyDetailReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.ApplyId))
	return n
}

func (m *GetGiftpkgApplyDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	if m.Apply != nil {
		l = m.Apply.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	l = len(m.Content)
	n += 1 + l + sovGiftpkg_(uint64(l))
	l = len(m.Usage)
	n += 1 + l + sovGiftpkg_(uint64(l))
	return n
}

func (m *GetGuildStorageGiftpkgListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	return n
}

func (m *GuildStorageGiftpkg) Size() (n int) {
	var l int
	_ = l
	if m.Pkg != nil {
		l = m.Pkg.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.RemainNum))
	n += 1 + sovGiftpkg_(uint64(m.TotalNum))
	n += 1 + sovGiftpkg_(uint64(m.ApplyPassTime))
	return n
}

func (m *GetGuildStorageGiftpkgListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	if len(m.GiftPkgList) > 0 {
		for _, e := range m.GiftPkgList {
			l = e.Size()
			n += 1 + l + sovGiftpkg_(uint64(l))
		}
	}
	return n
}

func (m *ReportUseTaohaoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg_(uint64(m.GuildId))
	l = len(m.SerialCode)
	n += 1 + l + sovGiftpkg_(uint64(l))
	return n
}

func (m *ReportUseTaohaoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	return n
}

func (m *GetGuildGiftpkgPriceReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	return n
}

func (m *GetGuildGiftpkgPriceResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg_(uint64(m.RedDiamond))
	return n
}

func (m *FetchGiftpkgCostRedDiamondReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	n += 1 + sovGiftpkg_(uint64(m.RedDiamond))
	return n
}

func (m *FetchGiftpkgCostRedDiamondResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGiftpkg_(uint64(l))
	}
	n += 1 + sovGiftpkg_(uint64(m.GiftPkgId))
	l = len(m.SerialCode)
	n += 1 + l + sovGiftpkg_(uint64(l))
	n += 1 + sovGiftpkg_(uint64(m.GameId))
	return n
}

func sovGiftpkg_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGiftpkg_(x uint64) (n int) {
	return sovGiftpkg_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GetMyGuildGiftpkgListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMyGuildGiftpkgListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMyGuildGiftpkgListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildGiftpkg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildGiftpkg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildGiftpkg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Pkg == nil {
				m.Pkg = &ga.GiftPackage{}
			}
			if err := m.Pkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainNum", wireType)
			}
			m.RemainNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecycleCount", wireType)
			}
			m.RecycleCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecycleCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalNum", wireType)
			}
			m.TotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyStatus", wireType)
			}
			m.MyStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MyStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildStatus", wireType)
			}
			m.GuildStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MySerial", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MySerial = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("pkg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("remain_num")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recycle_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total_num")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("my_status")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMyGuildGiftpkgListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMyGuildGiftpkgListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMyGuildGiftpkgListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftPkgList = append(m.GiftPkgList, &GuildGiftpkg{})
			if err := m.GiftPkgList[len(m.GiftPkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMyGiftpkgListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMyGiftpkgListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMyGiftpkgListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MyGiftpkgSerial) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MyGiftpkgSerial: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MyGiftpkgSerial: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Pkg == nil {
				m.Pkg = &ga.GiftPackage{}
			}
			if err := m.Pkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("pkg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("serial_code")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMyGiftpkgListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMyGiftpkgListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMyGiftpkgListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftPkgList = append(m.GiftPkgList, &MyGiftpkgSerial{})
			if err := m.GiftPkgList[len(m.GiftPkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchGuildGiftpkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchGuildGiftpkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchGuildGiftpkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchGuildGiftpkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchGuildGiftpkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchGuildGiftpkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("serial_code")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchRedpkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchRedpkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchRedpkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("red_pkg_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchRedpkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchRedpkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchRedpkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("serial_code")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendGuildRedpkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendGuildRedpkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendGuildRedpkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Num", wireType)
			}
			m.Num = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Num |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ToAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("num")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("to_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendGuildRedpkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendGuildRedpkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendGuildRedpkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildRedpkgDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildRedpkgDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildRedpkgDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("red_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildRedpkgDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildRedpkgDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildRedpkgDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeftNum", wireType)
			}
			m.LeftNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeftNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalNum", wireType)
			}
			m.TotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SenderAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SenderAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftPkgName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeBegin", wireType)
			}
			m.ExchangeBegin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeBegin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExchangeEnd", wireType)
			}
			m.ExchangeEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExchangeEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgIntro", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftPkgIntro = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00001000)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("left_num")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total_num")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("sender_account")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("red_pkg_id")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_name")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("exchange_begin")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("exchange_end")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_intro")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("serial_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RedPkgFetchedUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RedPkgFetchedUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RedPkgFetchedUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchTime", wireType)
			}
			m.FetchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("fetch_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildRedpkgFetchDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildRedpkgFetchDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildRedpkgFetchDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("red_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildRedpkgFetchDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildRedpkgFetchDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildRedpkgFetchDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchAccountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FetchAccountList = append(m.FetchAccountList, &RedPkgFetchedUserInfo{})
			if err := m.FetchAccountList[len(m.FetchAccountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeftNum", wireType)
			}
			m.LeftNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeftNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalNum", wireType)
			}
			m.TotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedPkgId", wireType)
			}
			m.RedPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("left_num")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total_num")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("red_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildTaohaoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildTaohaoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildTaohaoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildTaohaoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildTaohaoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildTaohaoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("serial_code")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildGetAppliableGiftpkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildGetAppliableGiftpkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildGetAppliableGiftpkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameId = append(m.GameId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGiftpkg_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGiftpkg_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGiftpkg_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameId = append(m.GameId, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildGetAppliableGiftpkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildGetAppliableGiftpkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildGetAppliableGiftpkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftPkgList = append(m.GiftPkgList, &GuildGiftpkg{})
			if err := m.GiftPkgList[len(m.GiftPkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildApplyGiftpkgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildApplyGiftpkgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildApplyGiftpkgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildApplyGiftpkgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildApplyGiftpkgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildApplyGiftpkgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftpkgDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftpkgDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftpkgDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftpkgId", wireType)
			}
			m.GiftpkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftpkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("giftpkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftpkgDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftpkgDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftpkgDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Pkg == nil {
				m.Pkg = &GuildGiftpkg{}
			}
			if err := m.Pkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Usage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Usage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("pkg")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("usage")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftpkgApplyHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftpkgApplyHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftpkgApplyHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GiftpkgApply) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GiftpkgApply: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GiftpkgApply: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Pkg == nil {
				m.Pkg = &ga.GiftPackage{}
			}
			if err := m.Pkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyTime", wireType)
			}
			m.ApplyTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("pkg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("apply_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("apply_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftpkgApplyHistoryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftpkgApplyHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftpkgApplyHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyList = append(m.ApplyList, &GiftpkgApply{})
			if err := m.ApplyList[len(m.ApplyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftpkgApplyDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftpkgApplyDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftpkgApplyDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("apply_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGiftpkgApplyDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGiftpkgApplyDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGiftpkgApplyDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Apply", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Apply == nil {
				m.Apply = &GiftpkgApply{}
			}
			if err := m.Apply.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Usage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Usage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("apply")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("usage")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildStorageGiftpkgListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildStorageGiftpkgListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildStorageGiftpkgListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildStorageGiftpkg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildStorageGiftpkg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildStorageGiftpkg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Pkg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Pkg == nil {
				m.Pkg = &ga.GiftPackage{}
			}
			if err := m.Pkg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainNum", wireType)
			}
			m.RemainNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalNum", wireType)
			}
			m.TotalNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyPassTime", wireType)
			}
			m.ApplyPassTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyPassTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("pkg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("remain_num")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total_num")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("apply_pass_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildStorageGiftpkgListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildStorageGiftpkgListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildStorageGiftpkgListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GiftPkgList = append(m.GiftPkgList, &GuildStorageGiftpkg{})
			if err := m.GiftPkgList[len(m.GiftPkgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportUseTaohaoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportUseTaohaoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportUseTaohaoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("serial_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportUseTaohaoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportUseTaohaoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportUseTaohaoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftpkgPriceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftpkgPriceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftpkgPriceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildGiftpkgPriceResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildGiftpkgPriceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildGiftpkgPriceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamond", wireType)
			}
			m.RedDiamond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("red_diamond")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchGiftpkgCostRedDiamondReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchGiftpkgCostRedDiamondReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchGiftpkgCostRedDiamondReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamond", wireType)
			}
			m.RedDiamond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("red_diamond")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchGiftpkgCostRedDiamondResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchGiftpkgCostRedDiamondResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchGiftpkgCostRedDiamondResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftPkgId", wireType)
			}
			m.GiftPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SerialCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SerialCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGiftpkg_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGiftpkg_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gift_pkg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("serial_code")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGiftpkg_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGiftpkg_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGiftpkg_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGiftpkg_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGiftpkg_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGiftpkg_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGiftpkg_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGiftpkg_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("giftpkg_.proto", fileDescriptorGiftpkg_) }

var fileDescriptorGiftpkg_ = []byte{
	// 1482 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x58, 0xdf, 0x6f, 0x1b, 0xc5,
	0x13, 0xcf, 0xd9, 0x8e, 0x7f, 0x8c, 0xed, 0xe4, 0xba, 0x49, 0xbe, 0x75, 0xfb, 0x6d, 0xdd, 0xf4,
	0x5a, 0x4a, 0x5a, 0x50, 0x22, 0x55, 0x20, 0x1e, 0x10, 0x52, 0x1d, 0xdb, 0x71, 0x4d, 0x43, 0x6c,
	0x5d, 0x1c, 0x04, 0x08, 0xf5, 0xb4, 0xb9, 0xdb, 0x5c, 0x4e, 0xf5, 0xdd, 0xb9, 0xbe, 0xb3, 0x54,
	0x23, 0x24, 0x24, 0x9e, 0x90, 0x78, 0xa9, 0x80, 0x77, 0x84, 0xe0, 0x01, 0xfa, 0xd0, 0x57, 0xfe,
	0x85, 0x3e, 0xf2, 0x17, 0x20, 0x54, 0xfe, 0x01, 0xfe, 0x04, 0xb4, 0xbb, 0x67, 0x7b, 0xcf, 0xbe,
	0xa4, 0x76, 0x48, 0x08, 0x6f, 0xa7, 0x99, 0xd9, 0x99, 0xd9, 0xcf, 0xcc, 0xce, 0x8f, 0x83, 0x05,
	0xd3, 0x3a, 0xf0, 0x3b, 0x8f, 0x4c, 0x6d, 0xbd, 0xd3, 0x75, 0x7d, 0x17, 0xc5, 0x4c, 0x7c, 0x39,
	0x6f, 0x62, 0x6d, 0x1f, 0x7b, 0x84, 0x93, 0x94, 0x4d, 0x28, 0xd4, 0x88, 0xff, 0x41, 0xbf, 0xd6,
	0xb3, 0xda, 0x46, 0x8d, 0x8b, 0x6f, 0x5b, 0x9e, 0xaf, 0x92, 0xc7, 0xe8, 0x16, 0xa4, 0xa9, 0xa4,
	0xd6, 0x25, 0x8f, 0x0b, 0xd2, 0x6a, 0x6c, 0x2d, 0x7b, 0x37, 0xbb, 0x6e, 0xe2, 0xf5, 0x4d, 0xec,
	0x11, 0x95, 0x3c, 0x56, 0x53, 0xfb, 0xfc, 0x43, 0xf9, 0x36, 0x06, 0x39, 0xf1, 0x3c, 0xba, 0x0e,
	0xf1, 0xce, 0x23, 0x33, 0x38, 0xb3, 0x48, 0xcf, 0x50, 0x4e, 0x13, 0xeb, 0x8f, 0xb0, 0x49, 0x54,
	0xca, 0x43, 0x37, 0x00, 0xba, 0xc4, 0xc6, 0x96, 0xa3, 0x39, 0x3d, 0xbb, 0x10, 0x5b, 0x8d, 0xad,
	0xe5, 0x37, 0x13, 0x2f, 0x7e, 0xbf, 0x36, 0xa7, 0x66, 0x38, 0x7d, 0xa7, 0x67, 0xa3, 0xdb, 0x90,
	0xef, 0x12, 0xbd, 0xaf, 0xb7, 0x89, 0xa6, 0xbb, 0x3d, 0xc7, 0x2f, 0xc4, 0x05, 0xb9, 0x5c, 0xc0,
	0x2a, 0x53, 0x0e, 0xba, 0x0e, 0x19, 0xdf, 0xf5, 0x71, 0x9b, 0xa9, 0x4b, 0x08, 0x62, 0x69, 0x46,
	0xa6, 0xda, 0xae, 0x43, 0xc6, 0xee, 0x6b, 0x9e, 0x8f, 0xfd, 0x9e, 0x57, 0x98, 0x17, 0x45, 0xec,
	0xfe, 0x2e, 0xa3, 0xa2, 0xd7, 0x21, 0x67, 0xd2, 0x8b, 0x0c, 0xa4, 0x92, 0x82, 0x54, 0x96, 0x71,
	0x02, 0xc1, 0x40, 0x17, 0xe9, 0x5a, 0xb8, 0x5d, 0x48, 0xad, 0x4a, 0x6b, 0x19, 0x41, 0x17, 0xa3,
	0x2a, 0x9f, 0xc3, 0xa5, 0x23, 0x90, 0xf5, 0x3a, 0xe8, 0x36, 0x64, 0x02, 0x68, 0xbd, 0x4e, 0x80,
	0x53, 0x6e, 0x84, 0xad, 0xd7, 0x51, 0xd3, 0xfb, 0xc1, 0x17, 0x7a, 0x0b, 0xf2, 0x34, 0x8c, 0x1a,
	0x8d, 0x63, 0xdb, 0xf2, 0xfc, 0x42, 0x6c, 0x35, 0xbe, 0x96, 0xbd, 0x2b, 0x33, 0x58, 0x05, 0xdd,
	0x6a, 0x96, 0x8a, 0x35, 0xb9, 0x11, 0xe5, 0x3d, 0x58, 0xe2, 0xd6, 0x4f, 0x16, 0xd2, 0x3e, 0x2c,
	0x0e, 0xcf, 0xf2, 0xfb, 0x4c, 0x13, 0xd4, 0xd7, 0x20, 0xcb, 0x21, 0xd1, 0x74, 0xd7, 0x20, 0x2c,
	0xaa, 0x03, 0x5c, 0x80, 0x33, 0xca, 0xae, 0x41, 0xd0, 0x15, 0x48, 0x06, 0xf8, 0x8a, 0xf1, 0x0c,
	0x68, 0xca, 0x67, 0xb0, 0x3c, 0xe9, 0xf9, 0x6c, 0x90, 0xbd, 0x13, 0x0d, 0xd9, 0x12, 0x15, 0x1f,
	0xbb, 0x56, 0x18, 0x35, 0x03, 0x96, 0xb7, 0x88, 0xaf, 0x1f, 0x86, 0x70, 0x9d, 0x1e, 0x36, 0x74,
	0x13, 0xb2, 0x43, 0xc3, 0x96, 0x11, 0x4e, 0xeb, 0xc0, 0x4e, 0xdd, 0x50, 0x9e, 0x4b, 0xb0, 0x12,
	0x61, 0x66, 0xb6, 0x3b, 0x4e, 0x89, 0xf5, 0x55, 0x48, 0x99, 0xd8, 0x26, 0xd4, 0x9b, 0x10, 0xd8,
	0x94, 0x58, 0x37, 0xc6, 0x1d, 0x4e, 0x44, 0x3b, 0xfc, 0xa3, 0x04, 0x0b, 0xcc, 0x61, 0x95, 0x18,
	0x67, 0x81, 0x08, 0x52, 0x68, 0x35, 0x30, 0x06, 0x42, 0xa2, 0xa3, 0xe9, 0x2e, 0x31, 0xb8, 0xcc,
	0x35, 0x48, 0xf3, 0xb7, 0x39, 0xe6, 0x67, 0x8a, 0x51, 0xeb, 0x86, 0xf2, 0xa5, 0x04, 0x8b, 0x21,
	0x2f, 0xcf, 0x01, 0x50, 0xe5, 0x7b, 0x09, 0xd0, 0x2e, 0x71, 0x0c, 0x16, 0xda, 0xb3, 0x82, 0xeb,
	0x7f, 0x10, 0xa7, 0x65, 0x4e, 0xb4, 0x4f, 0x09, 0xb4, 0xa8, 0xfa, 0xae, 0x86, 0x75, 0x5e, 0x2c,
	0x13, 0xc2, 0x0d, 0x32, 0xbe, 0x5b, 0xe2, 0x64, 0xe5, 0x1e, 0x2c, 0x4d, 0x38, 0x38, 0x13, 0x52,
	0xca, 0xd7, 0x12, 0x5c, 0xac, 0x11, 0x5f, 0xd0, 0x50, 0x21, 0x3e, 0xb6, 0xda, 0xe7, 0x92, 0x17,
	0xca, 0x4f, 0x09, 0xd6, 0xc2, 0x22, 0xbc, 0x99, 0x2d, 0xfe, 0xa3, 0xaa, 0x14, 0x9b, 0xac, 0x4a,
	0x34, 0xfb, 0xda, 0xe4, 0xc0, 0xd7, 0xc6, 0x71, 0x4f, 0x51, 0x6a, 0xd0, 0x5d, 0x5e, 0xd5, 0x80,
	0xde, 0x80, 0x05, 0x8f, 0x38, 0x06, 0xe9, 0x0e, 0x43, 0x34, 0x2f, 0x84, 0x28, 0xcf, 0x79, 0x41,
	0x98, 0xc6, 0x01, 0x4a, 0x4e, 0x03, 0x50, 0x2a, 0xf2, 0xe1, 0x08, 0x19, 0x9b, 0x8e, 0x28, 0x01,
	0x6b, 0x42, 0xb1, 0x74, 0xb0, 0x4d, 0x0a, 0x19, 0xc1, 0xa9, 0x41, 0x75, 0xdc, 0xc1, 0x36, 0xa1,
	0xfe, 0x93, 0x27, 0xfa, 0x21, 0x76, 0x4c, 0xa2, 0xed, 0x13, 0xd3, 0x72, 0x0a, 0x20, 0xe8, 0xcb,
	0x0f, 0x78, 0x9b, 0x94, 0x45, 0x5b, 0xe9, 0x50, 0x98, 0x38, 0x46, 0x21, 0x2b, 0xb6, 0xd2, 0x01,
	0xa7, 0xea, 0x18, 0xe8, 0x0e, 0x1f, 0x53, 0xf8, 0x1d, 0x1c, 0xbf, 0xeb, 0x16, 0x72, 0x82, 0x03,
	0xb9, 0xc1, 0x5d, 0x29, 0x67, 0xfc, 0x8d, 0xe6, 0xa3, 0xdf, 0xa8, 0x82, 0x20, 0xb9, 0xdb, 0x2a,
	0xb5, 0xf6, 0x76, 0x51, 0x1a, 0x12, 0x3b, 0x8d, 0x9d, 0xaa, 0x3c, 0xa7, 0x7c, 0x0a, 0x2b, 0x2a,
	0x43, 0x84, 0x95, 0x08, 0x62, 0xec, 0x79, 0xa4, 0x5b, 0x77, 0x0e, 0x5c, 0x54, 0x84, 0xd4, 0x20,
	0x1c, 0x92, 0xa0, 0x6f, 0x40, 0xa4, 0x8f, 0xea, 0x80, 0x1e, 0xd1, 0x7c, 0xcb, 0x26, 0xe1, 0x44,
	0x65, 0xf4, 0x96, 0x65, 0x13, 0xe5, 0xa9, 0x04, 0x57, 0xc2, 0x49, 0xc8, 0xcc, 0x9c, 0xe7, 0xbb,
	0xf8, 0x21, 0x06, 0x57, 0x8f, 0x71, 0x69, 0xb6, 0xc7, 0x51, 0x03, 0xc4, 0x41, 0x08, 0x50, 0x11,
	0xdb, 0xea, 0x25, 0x7a, 0x26, 0x12, 0x5b, 0x55, 0x66, 0x87, 0x82, 0x94, 0xa6, 0x1d, 0xf6, 0x54,
	0xde, 0xd1, 0x18, 0x46, 0xf3, 0xd3, 0x60, 0x94, 0x8c, 0xc4, 0xe8, 0x21, 0x2c, 0x30, 0x7c, 0x5a,
	0xd8, 0x3d, 0xc4, 0xee, 0xe9, 0x77, 0xfa, 0x9f, 0x25, 0x58, 0x0c, 0x19, 0xf8, 0xef, 0xf6, 0xf8,
	0x87, 0xf0, 0x7f, 0x3e, 0x8e, 0x10, 0xbf, 0xd4, 0xe9, 0xb4, 0x2d, 0xbc, 0xdf, 0x26, 0x27, 0x98,
	0x80, 0x2e, 0x8e, 0x7c, 0xa1, 0xd9, 0x91, 0x1f, 0x36, 0xc6, 0x2f, 0xe0, 0xca, 0xd1, 0xfa, 0xff,
	0x8d, 0x89, 0xd8, 0x80, 0x65, 0xc6, 0xa4, 0xd6, 0xfb, 0x67, 0x36, 0xdb, 0x1d, 0xc2, 0x4a, 0x84,
	0x95, 0xd9, 0xee, 0x37, 0x9d, 0xa5, 0x7d, 0x36, 0xe1, 0x07, 0x26, 0x66, 0x2f, 0x34, 0x37, 0x00,
	0x06, 0xdb, 0x61, 0x94, 0x8d, 0x0e, 0xaf, 0x21, 0x12, 0x1b, 0xc6, 0xc7, 0x8c, 0xcc, 0x76, 0x1b,
	0x85, 0xef, 0x0d, 0x31, 0x26, 0x34, 0x19, 0x23, 0xb6, 0x38, 0x14, 0x21, 0xa5, 0xbb, 0x8e, 0x4f,
	0x82, 0x15, 0x6f, 0x58, 0x83, 0x03, 0x22, 0xba, 0x0c, 0xf3, 0x3d, 0x0f, 0x9b, 0x24, 0x34, 0xd3,
	0x70, 0x92, 0x52, 0x66, 0x7b, 0x56, 0xa0, 0x8e, 0xc1, 0x7e, 0xdf, 0xf2, 0x7c, 0xb7, 0xdb, 0x9f,
	0x65, 0xdf, 0xf9, 0x45, 0x82, 0x9c, 0xa8, 0x62, 0x9a, 0x6d, 0xe7, 0x1a, 0xa4, 0x31, 0x95, 0x1d,
	0xc7, 0x2f, 0xc5, 0xa8, 0x75, 0xe3, 0xf8, 0x3d, 0x87, 0x06, 0x80, 0x1f, 0x67, 0x7d, 0x25, 0xf4,
	0x2a, 0x19, 0x9d, 0xf5, 0x95, 0x50, 0x27, 0x6b, 0x35, 0x2a, 0x0d, 0x79, 0x4e, 0x79, 0x02, 0x97,
	0x8f, 0xba, 0xf0, 0x6c, 0x91, 0xd9, 0x18, 0x78, 0x30, 0xf1, 0x88, 0x04, 0xdd, 0x81, 0x37, 0xec,
	0x09, 0xe9, 0x7c, 0xd2, 0x12, 0xb8, 0xb3, 0xe7, 0xdd, 0xab, 0x50, 0x53, 0x9e, 0x49, 0x13, 0x01,
	0x3d, 0x59, 0xe2, 0xdd, 0x82, 0x79, 0xa6, 0x33, 0x94, 0x7a, 0xe2, 0xcd, 0x38, 0xfb, 0x1f, 0x25,
	0x5f, 0x6d, 0xd4, 0x63, 0x77, 0x7d, 0xb7, 0x8b, 0x4d, 0x72, 0xc2, 0x85, 0xfb, 0xb9, 0x04, 0x4b,
	0x11, 0x6a, 0x4e, 0xed, 0x57, 0x4a, 0xa8, 0xad, 0xc6, 0x23, 0xdb, 0xea, 0x9b, 0xb0, 0xc8, 0x23,
	0xd3, 0xc1, 0x9e, 0x37, 0x99, 0x95, 0x79, 0xc6, 0x6c, 0x62, 0xcf, 0x63, 0x99, 0xf9, 0x95, 0x04,
	0xc5, 0xe3, 0xae, 0x3e, 0x5b, 0xac, 0xde, 0x8d, 0x2e, 0xe9, 0x17, 0x87, 0xe5, 0x22, 0x6c, 0x22,
	0x5c, 0xd9, 0x9f, 0x49, 0x80, 0x54, 0xd2, 0x71, 0xbb, 0xfe, 0x9e, 0x47, 0xce, 0xa8, 0x95, 0x87,
	0xd6, 0xcf, 0x78, 0xc4, 0xfa, 0x39, 0xde, 0xac, 0x13, 0x47, 0xcc, 0xa6, 0xf7, 0x60, 0x69, 0xc2,
	0xd7, 0xd9, 0xd6, 0x2f, 0x73, 0xb4, 0x7d, 0x05, 0x70, 0x34, 0xbb, 0x96, 0x4e, 0x4e, 0xbf, 0x97,
	0x7d, 0x23, 0x8d, 0x36, 0xab, 0xb0, 0xa5, 0x33, 0xe8, 0x67, 0x14, 0x3f, 0x3a, 0xaf, 0x19, 0x16,
	0xb6, 0x5d, 0x27, 0x8c, 0x31, 0x1d, 0xe4, 0x2a, 0x9c, 0xae, 0x7c, 0x27, 0xc1, 0x55, 0xfe, 0xf3,
	0x84, 0x7b, 0x54, 0x76, 0x69, 0xb6, 0x0d, 0xd8, 0xa7, 0x1f, 0xf7, 0x29, 0xdd, 0xfa, 0x55, 0x82,
	0xe2, 0x71, 0x6e, 0x9d, 0x11, 0x62, 0x62, 0xc6, 0xc5, 0x5f, 0x3d, 0x1e, 0x26, 0x26, 0xc7, 0xc3,
	0x3b, 0x7f, 0xd1, 0x19, 0xb5, 0xbe, 0xd5, 0xd2, 0x9a, 0x0f, 0x6a, 0xda, 0xf8, 0xda, 0x84, 0x2e,
	0x40, 0xbe, 0xb6, 0x57, 0xdf, 0xae, 0x68, 0x5b, 0xd5, 0x56, 0xf9, 0x7e, 0xb5, 0x22, 0x4b, 0x48,
	0x86, 0xdc, 0xde, 0x6e, 0x55, 0x1d, 0x52, 0x62, 0x08, 0xc1, 0x02, 0x17, 0x2a, 0x35, 0x9b, 0xdb,
	0x1f, 0xd7, 0x77, 0x6a, 0x72, 0x9c, 0xd2, 0x1a, 0x5b, 0x5b, 0xf5, 0x72, 0x69, 0x5b, 0x53, 0xab,
	0xef, 0x57, 0xcb, 0x2d, 0x39, 0x21, 0xd2, 0x4a, 0xe5, 0x72, 0xb5, 0xd9, 0x92, 0xe7, 0xd1, 0x32,
	0xc8, 0x4c, 0x5b, 0xb9, 0xb4, 0x33, 0xd4, 0x98, 0x44, 0x4b, 0x82, 0x4f, 0xd5, 0x8f, 0xca, 0xd5,
	0x6a, 0x45, 0x4e, 0x31, 0x22, 0x33, 0x43, 0x65, 0x99, 0x29, 0x39, 0x8d, 0x16, 0x21, 0xdb, 0x2a,
	0x35, 0xee, 0x97, 0x1a, 0x5a, 0xe3, 0xc3, 0xaa, 0x2a, 0x67, 0xd0, 0x0a, 0x5c, 0x18, 0xb9, 0xa7,
	0xa9, 0xd5, 0x4a, 0xf3, 0x41, 0x4d, 0x06, 0x04, 0x90, 0x6c, 0x96, 0x98, 0x6f, 0xd9, 0xcd, 0xe6,
	0x8b, 0x97, 0x45, 0xe9, 0xb7, 0x97, 0x45, 0xe9, 0x8f, 0x97, 0x45, 0xe9, 0xe9, 0x9f, 0xc5, 0x39,
	0x28, 0xe8, 0xae, 0xbd, 0xde, 0xb7, 0xfa, 0x6e, 0x8f, 0x46, 0xc4, 0x76, 0x0d, 0xd2, 0xe6, 0x3f,
	0xc8, 0x3f, 0xb9, 0x69, 0xba, 0x6d, 0xec, 0x98, 0xeb, 0x6f, 0xdf, 0xf5, 0xfd, 0x75, 0xdd, 0xb5,
	0x37, 0x18, 0x59, 0x77, 0xdb, 0x1b, 0xb8, 0xd3, 0xd9, 0x08, 0x46, 0xa5, 0xbf, 0x03, 0x00, 0x00,
	0xff, 0xff, 0x99, 0x89, 0x41, 0x30, 0x6a, 0x17, 0x00, 0x00,
}
