// Code generated by protoc-gen-go. DO NOT EDIT.
// source: slip-note-logic_.proto

package slipnotelogic // import "golang.52tt.com/protocol/app/slipnotelogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import chatcardlogic "golang.52tt.com/protocol/app/chatcardlogic"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SlipNoteStatus int32

const (
	SlipNoteStatus_SlipNoteStatus_UNDEFINED SlipNoteStatus = 0
	SlipNoteStatus_SlipNoteStatus_MISSED    SlipNoteStatus = 1
	SlipNoteStatus_SlipNoteStatus_OPENED    SlipNoteStatus = 2
)

var SlipNoteStatus_name = map[int32]string{
	0: "SlipNoteStatus_UNDEFINED",
	1: "SlipNoteStatus_MISSED",
	2: "SlipNoteStatus_OPENED",
}
var SlipNoteStatus_value = map[string]int32{
	"SlipNoteStatus_UNDEFINED": 0,
	"SlipNoteStatus_MISSED":    1,
	"SlipNoteStatus_OPENED":    2,
}

func (x SlipNoteStatus) String() string {
	return proto.EnumName(SlipNoteStatus_name, int32(x))
}
func (SlipNoteStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{0}
}

type SlipNotePublishStatus int32

const (
	SlipNotePublishStatus_SlipNoteWithoutCheck SlipNotePublishStatus = 0
	SlipNotePublishStatus_SlipNotePassCheck    SlipNotePublishStatus = 1
	SlipNotePublishStatus_SlipNoteAbnormal     SlipNotePublishStatus = 2
	SlipNotePublishStatus_SlipNoteClosed       SlipNotePublishStatus = 3
	SlipNotePublishStatus_SlipNoteAfterPicked  SlipNotePublishStatus = 4
	SlipNotePublishStatus_SlipNoteExpired      SlipNotePublishStatus = 5
)

var SlipNotePublishStatus_name = map[int32]string{
	0: "SlipNoteWithoutCheck",
	1: "SlipNotePassCheck",
	2: "SlipNoteAbnormal",
	3: "SlipNoteClosed",
	4: "SlipNoteAfterPicked",
	5: "SlipNoteExpired",
}
var SlipNotePublishStatus_value = map[string]int32{
	"SlipNoteWithoutCheck": 0,
	"SlipNotePassCheck":    1,
	"SlipNoteAbnormal":     2,
	"SlipNoteClosed":       3,
	"SlipNoteAfterPicked":  4,
	"SlipNoteExpired":      5,
}

func (x SlipNotePublishStatus) String() string {
	return proto.EnumName(SlipNotePublishStatus_name, int32(x))
}
func (SlipNotePublishStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{1}
}

// 推送
type SlipNoteCommentNotifyType int32

const (
	SlipNoteCommentNotifyType_BePicked       SlipNoteCommentNotifyType = 0
	SlipNoteCommentNotifyType_Failed         SlipNoteCommentNotifyType = 1
	SlipNoteCommentNotifyType_CommentToOwner SlipNoteCommentNotifyType = 2
	SlipNoteCommentNotifyType_ExamineFailed  SlipNoteCommentNotifyType = 3
)

var SlipNoteCommentNotifyType_name = map[int32]string{
	0: "BePicked",
	1: "Failed",
	2: "CommentToOwner",
	3: "ExamineFailed",
}
var SlipNoteCommentNotifyType_value = map[string]int32{
	"BePicked":       0,
	"Failed":         1,
	"CommentToOwner": 2,
	"ExamineFailed":  3,
}

func (x SlipNoteCommentNotifyType) String() string {
	return proto.EnumName(SlipNoteCommentNotifyType_name, int32(x))
}
func (SlipNoteCommentNotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{2}
}

type ReportSlipNoteStatusReq struct {
	BaseReq              *app.BaseReq   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SlipNoteId           string         `protobuf:"bytes,2,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	Status               SlipNoteStatus `protobuf:"varint,3,opt,name=status,proto3,enum=ga.slipnotelogic.SlipNoteStatus" json:"status,omitempty"`
	IsReceiver           bool           `protobuf:"varint,4,opt,name=is_receiver,json=isReceiver,proto3" json:"is_receiver,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ReportSlipNoteStatusReq) Reset()         { *m = ReportSlipNoteStatusReq{} }
func (m *ReportSlipNoteStatusReq) String() string { return proto.CompactTextString(m) }
func (*ReportSlipNoteStatusReq) ProtoMessage()    {}
func (*ReportSlipNoteStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{0}
}
func (m *ReportSlipNoteStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportSlipNoteStatusReq.Unmarshal(m, b)
}
func (m *ReportSlipNoteStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportSlipNoteStatusReq.Marshal(b, m, deterministic)
}
func (dst *ReportSlipNoteStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportSlipNoteStatusReq.Merge(dst, src)
}
func (m *ReportSlipNoteStatusReq) XXX_Size() int {
	return xxx_messageInfo_ReportSlipNoteStatusReq.Size(m)
}
func (m *ReportSlipNoteStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportSlipNoteStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportSlipNoteStatusReq proto.InternalMessageInfo

func (m *ReportSlipNoteStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportSlipNoteStatusReq) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

func (m *ReportSlipNoteStatusReq) GetStatus() SlipNoteStatus {
	if m != nil {
		return m.Status
	}
	return SlipNoteStatus_SlipNoteStatus_UNDEFINED
}

func (m *ReportSlipNoteStatusReq) GetIsReceiver() bool {
	if m != nil {
		return m.IsReceiver
	}
	return false
}

type ReportSlipNoteStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportSlipNoteStatusResp) Reset()         { *m = ReportSlipNoteStatusResp{} }
func (m *ReportSlipNoteStatusResp) String() string { return proto.CompactTextString(m) }
func (*ReportSlipNoteStatusResp) ProtoMessage()    {}
func (*ReportSlipNoteStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{1}
}
func (m *ReportSlipNoteStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportSlipNoteStatusResp.Unmarshal(m, b)
}
func (m *ReportSlipNoteStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportSlipNoteStatusResp.Marshal(b, m, deterministic)
}
func (dst *ReportSlipNoteStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportSlipNoteStatusResp.Merge(dst, src)
}
func (m *ReportSlipNoteStatusResp) XXX_Size() int {
	return xxx_messageInfo_ReportSlipNoteStatusResp.Size(m)
}
func (m *ReportSlipNoteStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportSlipNoteStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportSlipNoteStatusResp proto.InternalMessageInfo

func (m *ReportSlipNoteStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type SlipNoteBaseInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	SlipNoteId           string   `protobuf:"bytes,3,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	EndTime              uint32   `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Nickname             string   `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SlipNoteBaseInfo) Reset()         { *m = SlipNoteBaseInfo{} }
func (m *SlipNoteBaseInfo) String() string { return proto.CompactTextString(m) }
func (*SlipNoteBaseInfo) ProtoMessage()    {}
func (*SlipNoteBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{2}
}
func (m *SlipNoteBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SlipNoteBaseInfo.Unmarshal(m, b)
}
func (m *SlipNoteBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SlipNoteBaseInfo.Marshal(b, m, deterministic)
}
func (dst *SlipNoteBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SlipNoteBaseInfo.Merge(dst, src)
}
func (m *SlipNoteBaseInfo) XXX_Size() int {
	return xxx_messageInfo_SlipNoteBaseInfo.Size(m)
}
func (m *SlipNoteBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SlipNoteBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SlipNoteBaseInfo proto.InternalMessageInfo

func (m *SlipNoteBaseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SlipNoteBaseInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SlipNoteBaseInfo) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

func (m *SlipNoteBaseInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SlipNoteBaseInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SlipNoteBaseInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

// 纸条push
type SlipNoteNotify struct {
	SlipNoteInfo         *SlipNoteBaseInfo `protobuf:"bytes,1,opt,name=slip_note_info,json=slipNoteInfo,proto3" json:"slip_note_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SlipNoteNotify) Reset()         { *m = SlipNoteNotify{} }
func (m *SlipNoteNotify) String() string { return proto.CompactTextString(m) }
func (*SlipNoteNotify) ProtoMessage()    {}
func (*SlipNoteNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{3}
}
func (m *SlipNoteNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SlipNoteNotify.Unmarshal(m, b)
}
func (m *SlipNoteNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SlipNoteNotify.Marshal(b, m, deterministic)
}
func (dst *SlipNoteNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SlipNoteNotify.Merge(dst, src)
}
func (m *SlipNoteNotify) XXX_Size() int {
	return xxx_messageInfo_SlipNoteNotify.Size(m)
}
func (m *SlipNoteNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SlipNoteNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SlipNoteNotify proto.InternalMessageInfo

func (m *SlipNoteNotify) GetSlipNoteInfo() *SlipNoteBaseInfo {
	if m != nil {
		return m.SlipNoteInfo
	}
	return nil
}

// 审核失败push
type SlipNoteExamineFailedNotify struct {
	SlipNoteId           string   `protobuf:"bytes,1,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	Tip                  string   `protobuf:"bytes,2,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SlipNoteExamineFailedNotify) Reset()         { *m = SlipNoteExamineFailedNotify{} }
func (m *SlipNoteExamineFailedNotify) String() string { return proto.CompactTextString(m) }
func (*SlipNoteExamineFailedNotify) ProtoMessage()    {}
func (*SlipNoteExamineFailedNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{4}
}
func (m *SlipNoteExamineFailedNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SlipNoteExamineFailedNotify.Unmarshal(m, b)
}
func (m *SlipNoteExamineFailedNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SlipNoteExamineFailedNotify.Marshal(b, m, deterministic)
}
func (dst *SlipNoteExamineFailedNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SlipNoteExamineFailedNotify.Merge(dst, src)
}
func (m *SlipNoteExamineFailedNotify) XXX_Size() int {
	return xxx_messageInfo_SlipNoteExamineFailedNotify.Size(m)
}
func (m *SlipNoteExamineFailedNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SlipNoteExamineFailedNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SlipNoteExamineFailedNotify proto.InternalMessageInfo

func (m *SlipNoteExamineFailedNotify) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

func (m *SlipNoteExamineFailedNotify) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

// 纸条配置
type GetSlipNoteConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSlipNoteConfigReq) Reset()         { *m = GetSlipNoteConfigReq{} }
func (m *GetSlipNoteConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetSlipNoteConfigReq) ProtoMessage()    {}
func (*GetSlipNoteConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{5}
}
func (m *GetSlipNoteConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSlipNoteConfigReq.Unmarshal(m, b)
}
func (m *GetSlipNoteConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSlipNoteConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetSlipNoteConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSlipNoteConfigReq.Merge(dst, src)
}
func (m *GetSlipNoteConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetSlipNoteConfigReq.Size(m)
}
func (m *GetSlipNoteConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSlipNoteConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSlipNoteConfigReq proto.InternalMessageInfo

func (m *GetSlipNoteConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetSlipNoteConfigResp struct {
	BaseResp          *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Text              []string      `protobuf:"bytes,2,rep,name=text,proto3" json:"text,omitempty"`
	PublishTimesLimit uint32        `protobuf:"varint,3,opt,name=publish_times_limit,json=publishTimesLimit,proto3" json:"publish_times_limit,omitempty"`
	//    uint32 fetch_times_limit = 4; /*最多捞纸条次数*/
	HoursSlipNoteDisappear uint32             `protobuf:"varint,4,opt,name=hours_slip_note_disappear,json=hoursSlipNoteDisappear,proto3" json:"hours_slip_note_disappear,omitempty"`
	IsBlackUser            bool               `protobuf:"varint,5,opt,name=is_black_user,json=isBlackUser,proto3" json:"is_black_user,omitempty"`
	TotalSlipNoteWaitTime  uint32             `protobuf:"varint,6,opt,name=total_slip_note_wait_time,json=totalSlipNoteWaitTime,proto3" json:"total_slip_note_wait_time,omitempty"`
	SlipNoteTipConfig      *SlipNoteTipConfig `protobuf:"bytes,7,opt,name=slip_note_tip_config,json=slipNoteTipConfig,proto3" json:"slip_note_tip_config,omitempty"`
	ExitSlipNotePageTimes  uint32             `protobuf:"varint,8,opt,name=exit_slip_note_page_times,json=exitSlipNotePageTimes,proto3" json:"exit_slip_note_page_times,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}           `json:"-"`
	XXX_unrecognized       []byte             `json:"-"`
	XXX_sizecache          int32              `json:"-"`
}

func (m *GetSlipNoteConfigResp) Reset()         { *m = GetSlipNoteConfigResp{} }
func (m *GetSlipNoteConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetSlipNoteConfigResp) ProtoMessage()    {}
func (*GetSlipNoteConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{6}
}
func (m *GetSlipNoteConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSlipNoteConfigResp.Unmarshal(m, b)
}
func (m *GetSlipNoteConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSlipNoteConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetSlipNoteConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSlipNoteConfigResp.Merge(dst, src)
}
func (m *GetSlipNoteConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetSlipNoteConfigResp.Size(m)
}
func (m *GetSlipNoteConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSlipNoteConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSlipNoteConfigResp proto.InternalMessageInfo

func (m *GetSlipNoteConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSlipNoteConfigResp) GetText() []string {
	if m != nil {
		return m.Text
	}
	return nil
}

func (m *GetSlipNoteConfigResp) GetPublishTimesLimit() uint32 {
	if m != nil {
		return m.PublishTimesLimit
	}
	return 0
}

func (m *GetSlipNoteConfigResp) GetHoursSlipNoteDisappear() uint32 {
	if m != nil {
		return m.HoursSlipNoteDisappear
	}
	return 0
}

func (m *GetSlipNoteConfigResp) GetIsBlackUser() bool {
	if m != nil {
		return m.IsBlackUser
	}
	return false
}

func (m *GetSlipNoteConfigResp) GetTotalSlipNoteWaitTime() uint32 {
	if m != nil {
		return m.TotalSlipNoteWaitTime
	}
	return 0
}

func (m *GetSlipNoteConfigResp) GetSlipNoteTipConfig() *SlipNoteTipConfig {
	if m != nil {
		return m.SlipNoteTipConfig
	}
	return nil
}

func (m *GetSlipNoteConfigResp) GetExitSlipNotePageTimes() uint32 {
	if m != nil {
		return m.ExitSlipNotePageTimes
	}
	return 0
}

// （注册三天内）新用户触发了6min内退房3次，则在最近一次曝光收小纸条入口时飘出
type SlipNoteTipConfig struct {
	RegisterInterval     uint32   `protobuf:"varint,1,opt,name=register_interval,json=registerInterval,proto3" json:"register_interval,omitempty"`
	LeaveRoomInterval    uint32   `protobuf:"varint,2,opt,name=leave_room_interval,json=leaveRoomInterval,proto3" json:"leave_room_interval,omitempty"`
	LeaveRoomTimes       uint32   `protobuf:"varint,3,opt,name=leave_room_times,json=leaveRoomTimes,proto3" json:"leave_room_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SlipNoteTipConfig) Reset()         { *m = SlipNoteTipConfig{} }
func (m *SlipNoteTipConfig) String() string { return proto.CompactTextString(m) }
func (*SlipNoteTipConfig) ProtoMessage()    {}
func (*SlipNoteTipConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{7}
}
func (m *SlipNoteTipConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SlipNoteTipConfig.Unmarshal(m, b)
}
func (m *SlipNoteTipConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SlipNoteTipConfig.Marshal(b, m, deterministic)
}
func (dst *SlipNoteTipConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SlipNoteTipConfig.Merge(dst, src)
}
func (m *SlipNoteTipConfig) XXX_Size() int {
	return xxx_messageInfo_SlipNoteTipConfig.Size(m)
}
func (m *SlipNoteTipConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SlipNoteTipConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SlipNoteTipConfig proto.InternalMessageInfo

func (m *SlipNoteTipConfig) GetRegisterInterval() uint32 {
	if m != nil {
		return m.RegisterInterval
	}
	return 0
}

func (m *SlipNoteTipConfig) GetLeaveRoomInterval() uint32 {
	if m != nil {
		return m.LeaveRoomInterval
	}
	return 0
}

func (m *SlipNoteTipConfig) GetLeaveRoomTimes() uint32 {
	if m != nil {
		return m.LeaveRoomTimes
	}
	return 0
}

// 发布纸条
type PublishSlipNoteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Content              string       `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PublishSlipNoteReq) Reset()         { *m = PublishSlipNoteReq{} }
func (m *PublishSlipNoteReq) String() string { return proto.CompactTextString(m) }
func (*PublishSlipNoteReq) ProtoMessage()    {}
func (*PublishSlipNoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{8}
}
func (m *PublishSlipNoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishSlipNoteReq.Unmarshal(m, b)
}
func (m *PublishSlipNoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishSlipNoteReq.Marshal(b, m, deterministic)
}
func (dst *PublishSlipNoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishSlipNoteReq.Merge(dst, src)
}
func (m *PublishSlipNoteReq) XXX_Size() int {
	return xxx_messageInfo_PublishSlipNoteReq.Size(m)
}
func (m *PublishSlipNoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishSlipNoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_PublishSlipNoteReq proto.InternalMessageInfo

func (m *PublishSlipNoteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PublishSlipNoteReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type PublishSlipNoteResp struct {
	BaseResp              *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SlipNoteId            string        `protobuf:"bytes,2,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	EndTime               uint32        `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	PublishRemainingTimes uint32        `protobuf:"varint,4,opt,name=publish_remaining_times,json=publishRemainingTimes,proto3" json:"publish_remaining_times,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}      `json:"-"`
	XXX_unrecognized      []byte        `json:"-"`
	XXX_sizecache         int32         `json:"-"`
}

func (m *PublishSlipNoteResp) Reset()         { *m = PublishSlipNoteResp{} }
func (m *PublishSlipNoteResp) String() string { return proto.CompactTextString(m) }
func (*PublishSlipNoteResp) ProtoMessage()    {}
func (*PublishSlipNoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{9}
}
func (m *PublishSlipNoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublishSlipNoteResp.Unmarshal(m, b)
}
func (m *PublishSlipNoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublishSlipNoteResp.Marshal(b, m, deterministic)
}
func (dst *PublishSlipNoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublishSlipNoteResp.Merge(dst, src)
}
func (m *PublishSlipNoteResp) XXX_Size() int {
	return xxx_messageInfo_PublishSlipNoteResp.Size(m)
}
func (m *PublishSlipNoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PublishSlipNoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_PublishSlipNoteResp proto.InternalMessageInfo

func (m *PublishSlipNoteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PublishSlipNoteResp) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

func (m *PublishSlipNoteResp) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *PublishSlipNoteResp) GetPublishRemainingTimes() uint32 {
	if m != nil {
		return m.PublishRemainingTimes
	}
	return 0
}

// 关闭纸条
type CloseSlipNoteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SlipNoteId           string       `protobuf:"bytes,2,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CloseSlipNoteReq) Reset()         { *m = CloseSlipNoteReq{} }
func (m *CloseSlipNoteReq) String() string { return proto.CompactTextString(m) }
func (*CloseSlipNoteReq) ProtoMessage()    {}
func (*CloseSlipNoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{10}
}
func (m *CloseSlipNoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseSlipNoteReq.Unmarshal(m, b)
}
func (m *CloseSlipNoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseSlipNoteReq.Marshal(b, m, deterministic)
}
func (dst *CloseSlipNoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseSlipNoteReq.Merge(dst, src)
}
func (m *CloseSlipNoteReq) XXX_Size() int {
	return xxx_messageInfo_CloseSlipNoteReq.Size(m)
}
func (m *CloseSlipNoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseSlipNoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CloseSlipNoteReq proto.InternalMessageInfo

func (m *CloseSlipNoteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CloseSlipNoteReq) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

type CloseSlipNoteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainingTimes       uint32        `protobuf:"varint,2,opt,name=remaining_times,json=remainingTimes,proto3" json:"remaining_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CloseSlipNoteResp) Reset()         { *m = CloseSlipNoteResp{} }
func (m *CloseSlipNoteResp) String() string { return proto.CompactTextString(m) }
func (*CloseSlipNoteResp) ProtoMessage()    {}
func (*CloseSlipNoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{11}
}
func (m *CloseSlipNoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseSlipNoteResp.Unmarshal(m, b)
}
func (m *CloseSlipNoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseSlipNoteResp.Marshal(b, m, deterministic)
}
func (dst *CloseSlipNoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseSlipNoteResp.Merge(dst, src)
}
func (m *CloseSlipNoteResp) XXX_Size() int {
	return xxx_messageInfo_CloseSlipNoteResp.Size(m)
}
func (m *CloseSlipNoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseSlipNoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_CloseSlipNoteResp proto.InternalMessageInfo

func (m *CloseSlipNoteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CloseSlipNoteResp) GetRemainingTimes() uint32 {
	if m != nil {
		return m.RemainingTimes
	}
	return 0
}

// 纸条状态
type GetSlipNoteStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SlipNoteId           string       `protobuf:"bytes,2,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSlipNoteStatusReq) Reset()         { *m = GetSlipNoteStatusReq{} }
func (m *GetSlipNoteStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetSlipNoteStatusReq) ProtoMessage()    {}
func (*GetSlipNoteStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{12}
}
func (m *GetSlipNoteStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSlipNoteStatusReq.Unmarshal(m, b)
}
func (m *GetSlipNoteStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSlipNoteStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetSlipNoteStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSlipNoteStatusReq.Merge(dst, src)
}
func (m *GetSlipNoteStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetSlipNoteStatusReq.Size(m)
}
func (m *GetSlipNoteStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSlipNoteStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSlipNoteStatusReq proto.InternalMessageInfo

func (m *GetSlipNoteStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSlipNoteStatusReq) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

type GetSlipNoteStatusResp struct {
	BaseResp              *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Status                uint32        `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	PublishRemainingTimes uint32        `protobuf:"varint,3,opt,name=publish_remaining_times,json=publishRemainingTimes,proto3" json:"publish_remaining_times,omitempty"`
	//    uint32 fetch_remaining_times = 4; /*剩余捞纸条次数*/
	OnForToday           bool     `protobuf:"varint,4,opt,name=on_for_today,json=onForToday,proto3" json:"on_for_today,omitempty"`
	PickedCommentId      string   `protobuf:"bytes,5,opt,name=picked_comment_id,json=pickedCommentId,proto3" json:"picked_comment_id,omitempty"`
	CanFetch             bool     `protobuf:"varint,6,opt,name=can_fetch,json=canFetch,proto3" json:"can_fetch,omitempty"`
	PickedCommentIds     []string `protobuf:"bytes,7,rep,name=picked_comment_ids,json=pickedCommentIds,proto3" json:"picked_comment_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSlipNoteStatusResp) Reset()         { *m = GetSlipNoteStatusResp{} }
func (m *GetSlipNoteStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetSlipNoteStatusResp) ProtoMessage()    {}
func (*GetSlipNoteStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{13}
}
func (m *GetSlipNoteStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSlipNoteStatusResp.Unmarshal(m, b)
}
func (m *GetSlipNoteStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSlipNoteStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetSlipNoteStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSlipNoteStatusResp.Merge(dst, src)
}
func (m *GetSlipNoteStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetSlipNoteStatusResp.Size(m)
}
func (m *GetSlipNoteStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSlipNoteStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSlipNoteStatusResp proto.InternalMessageInfo

func (m *GetSlipNoteStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSlipNoteStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetSlipNoteStatusResp) GetPublishRemainingTimes() uint32 {
	if m != nil {
		return m.PublishRemainingTimes
	}
	return 0
}

func (m *GetSlipNoteStatusResp) GetOnForToday() bool {
	if m != nil {
		return m.OnForToday
	}
	return false
}

func (m *GetSlipNoteStatusResp) GetPickedCommentId() string {
	if m != nil {
		return m.PickedCommentId
	}
	return ""
}

func (m *GetSlipNoteStatusResp) GetCanFetch() bool {
	if m != nil {
		return m.CanFetch
	}
	return false
}

func (m *GetSlipNoteStatusResp) GetPickedCommentIds() []string {
	if m != nil {
		return m.PickedCommentIds
	}
	return nil
}

// 评论
type SlipNoteCommentInfo struct {
	SlipNoteId           string                       `protobuf:"bytes,1,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	Content              string                       `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	CommentId            string                       `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	CommentUid           uint32                       `protobuf:"varint,4,opt,name=comment_uid,json=commentUid,proto3" json:"comment_uid,omitempty"`
	CommentAccount       string                       `protobuf:"bytes,5,opt,name=comment_account,json=commentAccount,proto3" json:"comment_account,omitempty"`
	Tags                 []*chatcardlogic.ChatCardTag `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *SlipNoteCommentInfo) Reset()         { *m = SlipNoteCommentInfo{} }
func (m *SlipNoteCommentInfo) String() string { return proto.CompactTextString(m) }
func (*SlipNoteCommentInfo) ProtoMessage()    {}
func (*SlipNoteCommentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{14}
}
func (m *SlipNoteCommentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SlipNoteCommentInfo.Unmarshal(m, b)
}
func (m *SlipNoteCommentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SlipNoteCommentInfo.Marshal(b, m, deterministic)
}
func (dst *SlipNoteCommentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SlipNoteCommentInfo.Merge(dst, src)
}
func (m *SlipNoteCommentInfo) XXX_Size() int {
	return xxx_messageInfo_SlipNoteCommentInfo.Size(m)
}
func (m *SlipNoteCommentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SlipNoteCommentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SlipNoteCommentInfo proto.InternalMessageInfo

func (m *SlipNoteCommentInfo) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

func (m *SlipNoteCommentInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SlipNoteCommentInfo) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *SlipNoteCommentInfo) GetCommentUid() uint32 {
	if m != nil {
		return m.CommentUid
	}
	return 0
}

func (m *SlipNoteCommentInfo) GetCommentAccount() string {
	if m != nil {
		return m.CommentAccount
	}
	return ""
}

func (m *SlipNoteCommentInfo) GetTags() []*chatcardlogic.ChatCardTag {
	if m != nil {
		return m.Tags
	}
	return nil
}

type CommentToSlipNoteReq struct {
	BaseReq              *app.BaseReq         `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Comment              *SlipNoteCommentInfo `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CommentToSlipNoteReq) Reset()         { *m = CommentToSlipNoteReq{} }
func (m *CommentToSlipNoteReq) String() string { return proto.CompactTextString(m) }
func (*CommentToSlipNoteReq) ProtoMessage()    {}
func (*CommentToSlipNoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{15}
}
func (m *CommentToSlipNoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentToSlipNoteReq.Unmarshal(m, b)
}
func (m *CommentToSlipNoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentToSlipNoteReq.Marshal(b, m, deterministic)
}
func (dst *CommentToSlipNoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentToSlipNoteReq.Merge(dst, src)
}
func (m *CommentToSlipNoteReq) XXX_Size() int {
	return xxx_messageInfo_CommentToSlipNoteReq.Size(m)
}
func (m *CommentToSlipNoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentToSlipNoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommentToSlipNoteReq proto.InternalMessageInfo

func (m *CommentToSlipNoteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommentToSlipNoteReq) GetComment() *SlipNoteCommentInfo {
	if m != nil {
		return m.Comment
	}
	return nil
}

type CommentToSlipNoteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CommentId            string        `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CommentToSlipNoteResp) Reset()         { *m = CommentToSlipNoteResp{} }
func (m *CommentToSlipNoteResp) String() string { return proto.CompactTextString(m) }
func (*CommentToSlipNoteResp) ProtoMessage()    {}
func (*CommentToSlipNoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{16}
}
func (m *CommentToSlipNoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentToSlipNoteResp.Unmarshal(m, b)
}
func (m *CommentToSlipNoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentToSlipNoteResp.Marshal(b, m, deterministic)
}
func (dst *CommentToSlipNoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentToSlipNoteResp.Merge(dst, src)
}
func (m *CommentToSlipNoteResp) XXX_Size() int {
	return xxx_messageInfo_CommentToSlipNoteResp.Size(m)
}
func (m *CommentToSlipNoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentToSlipNoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommentToSlipNoteResp proto.InternalMessageInfo

func (m *CommentToSlipNoteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CommentToSlipNoteResp) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

// 查看纸条评论
type PullSlipNoteCommentListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SlipNoteId           string       `protobuf:"bytes,2,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	Offset               uint32       `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32       `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PullSlipNoteCommentListReq) Reset()         { *m = PullSlipNoteCommentListReq{} }
func (m *PullSlipNoteCommentListReq) String() string { return proto.CompactTextString(m) }
func (*PullSlipNoteCommentListReq) ProtoMessage()    {}
func (*PullSlipNoteCommentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{17}
}
func (m *PullSlipNoteCommentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullSlipNoteCommentListReq.Unmarshal(m, b)
}
func (m *PullSlipNoteCommentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullSlipNoteCommentListReq.Marshal(b, m, deterministic)
}
func (dst *PullSlipNoteCommentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullSlipNoteCommentListReq.Merge(dst, src)
}
func (m *PullSlipNoteCommentListReq) XXX_Size() int {
	return xxx_messageInfo_PullSlipNoteCommentListReq.Size(m)
}
func (m *PullSlipNoteCommentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PullSlipNoteCommentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_PullSlipNoteCommentListReq proto.InternalMessageInfo

func (m *PullSlipNoteCommentListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PullSlipNoteCommentListReq) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

func (m *PullSlipNoteCommentListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *PullSlipNoteCommentListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type PullSlipNoteCommentListResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CommentList          []*SlipNoteCommentInfo `protobuf:"bytes,2,rep,name=comment_list,json=commentList,proto3" json:"comment_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *PullSlipNoteCommentListResp) Reset()         { *m = PullSlipNoteCommentListResp{} }
func (m *PullSlipNoteCommentListResp) String() string { return proto.CompactTextString(m) }
func (*PullSlipNoteCommentListResp) ProtoMessage()    {}
func (*PullSlipNoteCommentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{18}
}
func (m *PullSlipNoteCommentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullSlipNoteCommentListResp.Unmarshal(m, b)
}
func (m *PullSlipNoteCommentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullSlipNoteCommentListResp.Marshal(b, m, deterministic)
}
func (dst *PullSlipNoteCommentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullSlipNoteCommentListResp.Merge(dst, src)
}
func (m *PullSlipNoteCommentListResp) XXX_Size() int {
	return xxx_messageInfo_PullSlipNoteCommentListResp.Size(m)
}
func (m *PullSlipNoteCommentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PullSlipNoteCommentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_PullSlipNoteCommentListResp proto.InternalMessageInfo

func (m *PullSlipNoteCommentListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *PullSlipNoteCommentListResp) GetCommentList() []*SlipNoteCommentInfo {
	if m != nil {
		return m.CommentList
	}
	return nil
}

// 选择一个评论
type PickSlipNoteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SlipNoteId           string       `protobuf:"bytes,2,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	CommentId            string       `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	KeepNote             bool         `protobuf:"varint,4,opt,name=keep_note,json=keepNote,proto3" json:"keep_note,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PickSlipNoteReq) Reset()         { *m = PickSlipNoteReq{} }
func (m *PickSlipNoteReq) String() string { return proto.CompactTextString(m) }
func (*PickSlipNoteReq) ProtoMessage()    {}
func (*PickSlipNoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{19}
}
func (m *PickSlipNoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickSlipNoteReq.Unmarshal(m, b)
}
func (m *PickSlipNoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickSlipNoteReq.Marshal(b, m, deterministic)
}
func (dst *PickSlipNoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickSlipNoteReq.Merge(dst, src)
}
func (m *PickSlipNoteReq) XXX_Size() int {
	return xxx_messageInfo_PickSlipNoteReq.Size(m)
}
func (m *PickSlipNoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PickSlipNoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_PickSlipNoteReq proto.InternalMessageInfo

func (m *PickSlipNoteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PickSlipNoteReq) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

func (m *PickSlipNoteReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *PickSlipNoteReq) GetKeepNote() bool {
	if m != nil {
		return m.KeepNote
	}
	return false
}

type PickSlipNoteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PickSlipNoteResp) Reset()         { *m = PickSlipNoteResp{} }
func (m *PickSlipNoteResp) String() string { return proto.CompactTextString(m) }
func (*PickSlipNoteResp) ProtoMessage()    {}
func (*PickSlipNoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{20}
}
func (m *PickSlipNoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PickSlipNoteResp.Unmarshal(m, b)
}
func (m *PickSlipNoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PickSlipNoteResp.Marshal(b, m, deterministic)
}
func (dst *PickSlipNoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PickSlipNoteResp.Merge(dst, src)
}
func (m *PickSlipNoteResp) XXX_Size() int {
	return xxx_messageInfo_PickSlipNoteResp.Size(m)
}
func (m *PickSlipNoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PickSlipNoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_PickSlipNoteResp proto.InternalMessageInfo

func (m *PickSlipNoteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 开关纸条
type SetSlipNoteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OnForToday           bool         `protobuf:"varint,2,opt,name=on_for_today,json=onForToday,proto3" json:"on_for_today,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetSlipNoteReq) Reset()         { *m = SetSlipNoteReq{} }
func (m *SetSlipNoteReq) String() string { return proto.CompactTextString(m) }
func (*SetSlipNoteReq) ProtoMessage()    {}
func (*SetSlipNoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{21}
}
func (m *SetSlipNoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSlipNoteReq.Unmarshal(m, b)
}
func (m *SetSlipNoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSlipNoteReq.Marshal(b, m, deterministic)
}
func (dst *SetSlipNoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSlipNoteReq.Merge(dst, src)
}
func (m *SetSlipNoteReq) XXX_Size() int {
	return xxx_messageInfo_SetSlipNoteReq.Size(m)
}
func (m *SetSlipNoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSlipNoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSlipNoteReq proto.InternalMessageInfo

func (m *SetSlipNoteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetSlipNoteReq) GetOnForToday() bool {
	if m != nil {
		return m.OnForToday
	}
	return false
}

type SetSlipNoteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetSlipNoteResp) Reset()         { *m = SetSlipNoteResp{} }
func (m *SetSlipNoteResp) String() string { return proto.CompactTextString(m) }
func (*SetSlipNoteResp) ProtoMessage()    {}
func (*SetSlipNoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{22}
}
func (m *SetSlipNoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSlipNoteResp.Unmarshal(m, b)
}
func (m *SetSlipNoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSlipNoteResp.Marshal(b, m, deterministic)
}
func (dst *SetSlipNoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSlipNoteResp.Merge(dst, src)
}
func (m *SetSlipNoteResp) XXX_Size() int {
	return xxx_messageInfo_SetSlipNoteResp.Size(m)
}
func (m *SetSlipNoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSlipNoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSlipNoteResp proto.InternalMessageInfo

func (m *SetSlipNoteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 捞纸条
type FetchSlipNoteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Reacquire            bool         `protobuf:"varint,2,opt,name=reacquire,proto3" json:"reacquire,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *FetchSlipNoteReq) Reset()         { *m = FetchSlipNoteReq{} }
func (m *FetchSlipNoteReq) String() string { return proto.CompactTextString(m) }
func (*FetchSlipNoteReq) ProtoMessage()    {}
func (*FetchSlipNoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{23}
}
func (m *FetchSlipNoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FetchSlipNoteReq.Unmarshal(m, b)
}
func (m *FetchSlipNoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FetchSlipNoteReq.Marshal(b, m, deterministic)
}
func (dst *FetchSlipNoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FetchSlipNoteReq.Merge(dst, src)
}
func (m *FetchSlipNoteReq) XXX_Size() int {
	return xxx_messageInfo_FetchSlipNoteReq.Size(m)
}
func (m *FetchSlipNoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FetchSlipNoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_FetchSlipNoteReq proto.InternalMessageInfo

func (m *FetchSlipNoteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *FetchSlipNoteReq) GetReacquire() bool {
	if m != nil {
		return m.Reacquire
	}
	return false
}

type FetchSlipNoteResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SlipNoteInfo         *SlipNoteBaseInfo `protobuf:"bytes,2,opt,name=slip_note_info,json=slipNoteInfo,proto3" json:"slip_note_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *FetchSlipNoteResp) Reset()         { *m = FetchSlipNoteResp{} }
func (m *FetchSlipNoteResp) String() string { return proto.CompactTextString(m) }
func (*FetchSlipNoteResp) ProtoMessage()    {}
func (*FetchSlipNoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{24}
}
func (m *FetchSlipNoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FetchSlipNoteResp.Unmarshal(m, b)
}
func (m *FetchSlipNoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FetchSlipNoteResp.Marshal(b, m, deterministic)
}
func (dst *FetchSlipNoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FetchSlipNoteResp.Merge(dst, src)
}
func (m *FetchSlipNoteResp) XXX_Size() int {
	return xxx_messageInfo_FetchSlipNoteResp.Size(m)
}
func (m *FetchSlipNoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FetchSlipNoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_FetchSlipNoteResp proto.InternalMessageInfo

func (m *FetchSlipNoteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FetchSlipNoteResp) GetSlipNoteInfo() *SlipNoteBaseInfo {
	if m != nil {
		return m.SlipNoteInfo
	}
	return nil
}

type SlipNoteCommentNotify struct {
	SlipNoteId           string                       `protobuf:"bytes,1,opt,name=slip_note_id,json=slipNoteId,proto3" json:"slip_note_id,omitempty"`
	CommentId            string                       `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	PushType             uint32                       `protobuf:"varint,3,opt,name=push_type,json=pushType,proto3" json:"push_type,omitempty"`
	Text                 string                       `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	SlipNoteOwnerAccount string                       `protobuf:"bytes,5,opt,name=slip_note_owner_account,json=slipNoteOwnerAccount,proto3" json:"slip_note_owner_account,omitempty"`
	CommentOwnerAccount  string                       `protobuf:"bytes,6,opt,name=comment_owner_account,json=commentOwnerAccount,proto3" json:"comment_owner_account,omitempty"`
	Tags                 []*chatcardlogic.ChatCardTag `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *SlipNoteCommentNotify) Reset()         { *m = SlipNoteCommentNotify{} }
func (m *SlipNoteCommentNotify) String() string { return proto.CompactTextString(m) }
func (*SlipNoteCommentNotify) ProtoMessage()    {}
func (*SlipNoteCommentNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{25}
}
func (m *SlipNoteCommentNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SlipNoteCommentNotify.Unmarshal(m, b)
}
func (m *SlipNoteCommentNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SlipNoteCommentNotify.Marshal(b, m, deterministic)
}
func (dst *SlipNoteCommentNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SlipNoteCommentNotify.Merge(dst, src)
}
func (m *SlipNoteCommentNotify) XXX_Size() int {
	return xxx_messageInfo_SlipNoteCommentNotify.Size(m)
}
func (m *SlipNoteCommentNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SlipNoteCommentNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SlipNoteCommentNotify proto.InternalMessageInfo

func (m *SlipNoteCommentNotify) GetSlipNoteId() string {
	if m != nil {
		return m.SlipNoteId
	}
	return ""
}

func (m *SlipNoteCommentNotify) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *SlipNoteCommentNotify) GetPushType() uint32 {
	if m != nil {
		return m.PushType
	}
	return 0
}

func (m *SlipNoteCommentNotify) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SlipNoteCommentNotify) GetSlipNoteOwnerAccount() string {
	if m != nil {
		return m.SlipNoteOwnerAccount
	}
	return ""
}

func (m *SlipNoteCommentNotify) GetCommentOwnerAccount() string {
	if m != nil {
		return m.CommentOwnerAccount
	}
	return ""
}

func (m *SlipNoteCommentNotify) GetTags() []*chatcardlogic.ChatCardTag {
	if m != nil {
		return m.Tags
	}
	return nil
}

// 获取account，用于轮播
type GetAccountsForSlipNoteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Cnt                  uint32       `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAccountsForSlipNoteReq) Reset()         { *m = GetAccountsForSlipNoteReq{} }
func (m *GetAccountsForSlipNoteReq) String() string { return proto.CompactTextString(m) }
func (*GetAccountsForSlipNoteReq) ProtoMessage()    {}
func (*GetAccountsForSlipNoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{26}
}
func (m *GetAccountsForSlipNoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccountsForSlipNoteReq.Unmarshal(m, b)
}
func (m *GetAccountsForSlipNoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccountsForSlipNoteReq.Marshal(b, m, deterministic)
}
func (dst *GetAccountsForSlipNoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccountsForSlipNoteReq.Merge(dst, src)
}
func (m *GetAccountsForSlipNoteReq) XXX_Size() int {
	return xxx_messageInfo_GetAccountsForSlipNoteReq.Size(m)
}
func (m *GetAccountsForSlipNoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccountsForSlipNoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccountsForSlipNoteReq proto.InternalMessageInfo

func (m *GetAccountsForSlipNoteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAccountsForSlipNoteReq) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type GetAccountsForSlipNoteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	AccountList          []string      `protobuf:"bytes,2,rep,name=account_list,json=accountList,proto3" json:"account_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetAccountsForSlipNoteResp) Reset()         { *m = GetAccountsForSlipNoteResp{} }
func (m *GetAccountsForSlipNoteResp) String() string { return proto.CompactTextString(m) }
func (*GetAccountsForSlipNoteResp) ProtoMessage()    {}
func (*GetAccountsForSlipNoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_slip_note_logic__7705fdf2e9a4cc69, []int{27}
}
func (m *GetAccountsForSlipNoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccountsForSlipNoteResp.Unmarshal(m, b)
}
func (m *GetAccountsForSlipNoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccountsForSlipNoteResp.Marshal(b, m, deterministic)
}
func (dst *GetAccountsForSlipNoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccountsForSlipNoteResp.Merge(dst, src)
}
func (m *GetAccountsForSlipNoteResp) XXX_Size() int {
	return xxx_messageInfo_GetAccountsForSlipNoteResp.Size(m)
}
func (m *GetAccountsForSlipNoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccountsForSlipNoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccountsForSlipNoteResp proto.InternalMessageInfo

func (m *GetAccountsForSlipNoteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAccountsForSlipNoteResp) GetAccountList() []string {
	if m != nil {
		return m.AccountList
	}
	return nil
}

func init() {
	proto.RegisterType((*ReportSlipNoteStatusReq)(nil), "ga.slipnotelogic.ReportSlipNoteStatusReq")
	proto.RegisterType((*ReportSlipNoteStatusResp)(nil), "ga.slipnotelogic.ReportSlipNoteStatusResp")
	proto.RegisterType((*SlipNoteBaseInfo)(nil), "ga.slipnotelogic.SlipNoteBaseInfo")
	proto.RegisterType((*SlipNoteNotify)(nil), "ga.slipnotelogic.SlipNoteNotify")
	proto.RegisterType((*SlipNoteExamineFailedNotify)(nil), "ga.slipnotelogic.SlipNoteExamineFailedNotify")
	proto.RegisterType((*GetSlipNoteConfigReq)(nil), "ga.slipnotelogic.GetSlipNoteConfigReq")
	proto.RegisterType((*GetSlipNoteConfigResp)(nil), "ga.slipnotelogic.GetSlipNoteConfigResp")
	proto.RegisterType((*SlipNoteTipConfig)(nil), "ga.slipnotelogic.SlipNoteTipConfig")
	proto.RegisterType((*PublishSlipNoteReq)(nil), "ga.slipnotelogic.PublishSlipNoteReq")
	proto.RegisterType((*PublishSlipNoteResp)(nil), "ga.slipnotelogic.PublishSlipNoteResp")
	proto.RegisterType((*CloseSlipNoteReq)(nil), "ga.slipnotelogic.CloseSlipNoteReq")
	proto.RegisterType((*CloseSlipNoteResp)(nil), "ga.slipnotelogic.CloseSlipNoteResp")
	proto.RegisterType((*GetSlipNoteStatusReq)(nil), "ga.slipnotelogic.GetSlipNoteStatusReq")
	proto.RegisterType((*GetSlipNoteStatusResp)(nil), "ga.slipnotelogic.GetSlipNoteStatusResp")
	proto.RegisterType((*SlipNoteCommentInfo)(nil), "ga.slipnotelogic.SlipNoteCommentInfo")
	proto.RegisterType((*CommentToSlipNoteReq)(nil), "ga.slipnotelogic.CommentToSlipNoteReq")
	proto.RegisterType((*CommentToSlipNoteResp)(nil), "ga.slipnotelogic.CommentToSlipNoteResp")
	proto.RegisterType((*PullSlipNoteCommentListReq)(nil), "ga.slipnotelogic.PullSlipNoteCommentListReq")
	proto.RegisterType((*PullSlipNoteCommentListResp)(nil), "ga.slipnotelogic.PullSlipNoteCommentListResp")
	proto.RegisterType((*PickSlipNoteReq)(nil), "ga.slipnotelogic.PickSlipNoteReq")
	proto.RegisterType((*PickSlipNoteResp)(nil), "ga.slipnotelogic.PickSlipNoteResp")
	proto.RegisterType((*SetSlipNoteReq)(nil), "ga.slipnotelogic.SetSlipNoteReq")
	proto.RegisterType((*SetSlipNoteResp)(nil), "ga.slipnotelogic.SetSlipNoteResp")
	proto.RegisterType((*FetchSlipNoteReq)(nil), "ga.slipnotelogic.FetchSlipNoteReq")
	proto.RegisterType((*FetchSlipNoteResp)(nil), "ga.slipnotelogic.FetchSlipNoteResp")
	proto.RegisterType((*SlipNoteCommentNotify)(nil), "ga.slipnotelogic.SlipNoteCommentNotify")
	proto.RegisterType((*GetAccountsForSlipNoteReq)(nil), "ga.slipnotelogic.GetAccountsForSlipNoteReq")
	proto.RegisterType((*GetAccountsForSlipNoteResp)(nil), "ga.slipnotelogic.GetAccountsForSlipNoteResp")
	proto.RegisterEnum("ga.slipnotelogic.SlipNoteStatus", SlipNoteStatus_name, SlipNoteStatus_value)
	proto.RegisterEnum("ga.slipnotelogic.SlipNotePublishStatus", SlipNotePublishStatus_name, SlipNotePublishStatus_value)
	proto.RegisterEnum("ga.slipnotelogic.SlipNoteCommentNotifyType", SlipNoteCommentNotifyType_name, SlipNoteCommentNotifyType_value)
}

func init() {
	proto.RegisterFile("slip-note-logic_.proto", fileDescriptor_slip_note_logic__7705fdf2e9a4cc69)
}

var fileDescriptor_slip_note_logic__7705fdf2e9a4cc69 = []byte{
	// 1490 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0x4b, 0x6f, 0x1c, 0xc5,
	0x13, 0xcf, 0xec, 0xfa, 0xb1, 0x5b, 0xeb, 0xc7, 0x6c, 0xfb, 0x35, 0xb6, 0x13, 0xc5, 0xff, 0xf9,
	0x0b, 0x30, 0x86, 0x6c, 0x84, 0x51, 0x50, 0x90, 0x78, 0x28, 0x7e, 0x25, 0x96, 0x82, 0x63, 0xc6,
	0x36, 0xa0, 0x28, 0xd2, 0xd0, 0x9e, 0xe9, 0xdd, 0x6d, 0x3c, 0x3b, 0x3d, 0x9e, 0xee, 0x4d, 0xe2,
	0x13, 0x57, 0xce, 0x08, 0x09, 0x89, 0x13, 0x37, 0x0e, 0x1c, 0x10, 0x07, 0xae, 0x7c, 0x23, 0xbe,
	0x03, 0xea, 0xee, 0xe9, 0x9d, 0x9d, 0xb5, 0x1d, 0x67, 0x2d, 0x72, 0x9b, 0xae, 0xea, 0xaa, 0xae,
	0x57, 0xff, 0xaa, 0x7a, 0x60, 0x9e, 0x47, 0x34, 0xb9, 0x13, 0x33, 0x41, 0xee, 0x44, 0xac, 0x45,
	0x03, 0xbf, 0x91, 0xa4, 0x4c, 0x30, 0x64, 0xb7, 0x70, 0x43, 0xb2, 0x24, 0x47, 0x31, 0x96, 0x26,
	0x5b, 0xd8, 0x3f, 0xc6, 0x9c, 0xe8, 0x0d, 0x4b, 0xf3, 0x41, 0x1b, 0x8b, 0x3b, 0x01, 0x4e, 0xc3,
	0x82, 0xa0, 0xfb, 0xb7, 0x05, 0x0b, 0x1e, 0x49, 0x58, 0x2a, 0x0e, 0x22, 0x9a, 0xec, 0x31, 0x41,
	0x0e, 0x04, 0x16, 0x5d, 0xee, 0x91, 0x53, 0xf4, 0x36, 0x54, 0xa4, 0x06, 0x3f, 0x25, 0xa7, 0x8e,
	0xb5, 0x62, 0xad, 0xd6, 0xd6, 0x6b, 0x8d, 0x16, 0x6e, 0x6c, 0x60, 0x4e, 0x3c, 0x72, 0xea, 0x8d,
	0x1f, 0xeb, 0x0f, 0xb4, 0x02, 0x13, 0xf2, 0x6c, 0x5f, 0x1e, 0xee, 0xd3, 0xd0, 0x29, 0xad, 0x58,
	0xab, 0x55, 0x0f, 0x78, 0xa6, 0x70, 0x37, 0x44, 0xf7, 0x61, 0x8c, 0x2b, 0xb5, 0x4e, 0x79, 0xc5,
	0x5a, 0x9d, 0x5a, 0x5f, 0x69, 0x0c, 0xda, 0xdb, 0x18, 0x38, 0x3e, 0xdb, 0x8f, 0x6e, 0x43, 0x8d,
	0x72, 0x3f, 0x25, 0x01, 0xa1, 0xcf, 0x49, 0xea, 0x8c, 0xac, 0x58, 0xab, 0x15, 0x0f, 0x28, 0xf7,
	0x32, 0x8a, 0xbb, 0x0d, 0xce, 0xc5, 0xf6, 0xf3, 0x04, 0xbd, 0x0b, 0xd5, 0xcc, 0x01, 0x9e, 0x64,
	0x1e, 0x4c, 0xe4, 0x1e, 0xf0, 0xc4, 0xab, 0x1c, 0x67, 0x5f, 0xee, 0x9f, 0x16, 0xd8, 0x46, 0x83,
	0x64, 0xef, 0xc6, 0x4d, 0x86, 0x6c, 0x28, 0x77, 0x69, 0xa8, 0x24, 0x27, 0x3d, 0xf9, 0x89, 0x1c,
	0x18, 0xc7, 0x41, 0xc0, 0xba, 0xb1, 0xc8, 0xbc, 0x34, 0xcb, 0x73, 0x41, 0x28, 0x9f, 0x0b, 0x82,
	0x03, 0xe3, 0x01, 0x8b, 0x05, 0x89, 0x85, 0x72, 0xa3, 0xea, 0x99, 0x25, 0x5a, 0x84, 0x0a, 0x89,
	0x43, 0x5f, 0xd0, 0x0e, 0x71, 0x46, 0xd5, 0x61, 0xe3, 0x24, 0x0e, 0x0f, 0x69, 0x87, 0xa0, 0x25,
	0xa8, 0xc4, 0x34, 0x38, 0x89, 0x71, 0x87, 0x38, 0x63, 0x4a, 0xaa, 0xb7, 0x76, 0x9f, 0xc2, 0x94,
	0x31, 0x79, 0x8f, 0x09, 0xda, 0x3c, 0x43, 0x8f, 0x60, 0xaa, 0xcf, 0x88, 0xb8, 0xc9, 0x32, 0xaf,
	0xdd, 0xcb, 0xe3, 0x6d, 0x9c, 0xf5, 0x26, 0x7a, 0xa6, 0xc6, 0x4d, 0xe6, 0x7e, 0x09, 0xcb, 0x66,
	0xc7, 0xf6, 0x4b, 0xdc, 0xa1, 0x31, 0xd9, 0xc1, 0x34, 0x22, 0x61, 0x76, 0xd0, 0xa0, 0xb7, 0xd6,
	0x39, 0x6f, 0x6d, 0x28, 0x0b, 0x9a, 0x64, 0x51, 0x92, 0x9f, 0xee, 0x67, 0x30, 0xfb, 0x90, 0xf4,
	0xd2, 0xb4, 0xc9, 0xe2, 0x26, 0x6d, 0x0d, 0x51, 0x66, 0xee, 0x1f, 0x65, 0x98, 0xbb, 0x40, 0xc1,
	0x50, 0x79, 0x46, 0x08, 0x46, 0x04, 0x79, 0x29, 0xb3, 0x57, 0x5e, 0xad, 0x7a, 0xea, 0x1b, 0x35,
	0x60, 0x26, 0xe9, 0x1e, 0x47, 0x94, 0xb7, 0x55, 0x0a, 0xb8, 0x1f, 0xd1, 0x0e, 0x15, 0x2a, 0x83,
	0x93, 0x5e, 0x3d, 0x63, 0xc9, 0x6c, 0xf0, 0xc7, 0x92, 0x81, 0x3e, 0x86, 0xc5, 0x36, 0xeb, 0xa6,
	0xdc, 0xcf, 0x43, 0x10, 0x52, 0x8e, 0x93, 0x84, 0x60, 0x5d, 0xa1, 0x93, 0xde, 0xbc, 0xda, 0x60,
	0x4c, 0xdd, 0x32, 0x5c, 0xe4, 0xc2, 0x24, 0xe5, 0xfe, 0x71, 0x84, 0x83, 0x13, 0xbf, 0xcb, 0x49,
	0xaa, 0xd2, 0x5d, 0xf1, 0x6a, 0x94, 0x6f, 0x48, 0xda, 0x11, 0x27, 0x29, 0xba, 0x0f, 0x8b, 0x82,
	0x09, 0x1c, 0xf5, 0xa9, 0x7f, 0x81, 0xa9, 0xd0, 0xe5, 0x31, 0xa6, 0xd4, 0xcf, 0xa9, 0x0d, 0x46,
	0xfd, 0xd7, 0x98, 0x0a, 0x55, 0x2c, 0x87, 0x30, 0x9b, 0xcb, 0x08, 0x9a, 0xf8, 0x81, 0x8a, 0x91,
	0x33, 0xae, 0x42, 0xf2, 0xff, 0xcb, 0x8b, 0xe0, 0x90, 0x26, 0x59, 0x38, 0xeb, 0x7c, 0x90, 0x24,
	0xed, 0x21, 0x2f, 0xa9, 0xe8, 0x33, 0x27, 0xc1, 0x2d, 0xa2, 0x43, 0xe5, 0x54, 0xb4, 0x3d, 0x72,
	0x83, 0x51, 0xb6, 0x8f, 0x5b, 0x44, 0x45, 0xcb, 0xfd, 0xc5, 0x82, 0xfa, 0xb9, 0x23, 0xd0, 0x7b,
	0x50, 0x4f, 0x49, 0x8b, 0x72, 0x41, 0x52, 0x9f, 0xc6, 0x82, 0xa4, 0xcf, 0x71, 0x94, 0xdd, 0x31,
	0xdb, 0x30, 0x76, 0x33, 0xba, 0xcc, 0x4d, 0x44, 0xf0, 0x73, 0xe2, 0xa7, 0x8c, 0x75, 0xf2, 0xed,
	0x25, 0x9d, 0x1b, 0xc5, 0xf2, 0x18, 0xeb, 0xf4, 0xf6, 0xaf, 0x82, 0xdd, 0xb7, 0x5f, 0xdb, 0xa8,
	0x13, 0x39, 0xd5, 0xdb, 0xac, 0x8d, 0xfb, 0x0a, 0xd0, 0xbe, 0x4e, 0xad, 0x31, 0x71, 0x18, 0xcc,
	0xeb, 0xbb, 0xcc, 0xa5, 0xc2, 0x65, 0x76, 0xff, 0xb2, 0x60, 0xe6, 0x9c, 0xe2, 0xe1, 0x8a, 0xf4,
	0x6a, 0x40, 0xed, 0x47, 0x8c, 0x72, 0x11, 0x31, 0x3e, 0x82, 0x05, 0x53, 0xcd, 0x29, 0xe9, 0x60,
	0x1a, 0xd3, 0xb8, 0x95, 0x05, 0x42, 0xd7, 0xe6, 0x5c, 0xc6, 0xf6, 0x0c, 0x57, 0xc7, 0xe3, 0x19,
	0xd8, 0x9b, 0x11, 0xe3, 0xe4, 0x3a, 0xd1, 0xb8, 0xd2, 0x60, 0xb7, 0x05, 0xf5, 0x01, 0xed, 0xc3,
	0x85, 0xe4, 0x1d, 0x98, 0x1e, 0xf4, 0x46, 0xd7, 0xc0, 0x54, 0x5a, 0x74, 0xe3, 0xdb, 0x02, 0xca,
	0xbc, 0x81, 0x66, 0xe6, 0xfe, 0x5e, 0x2a, 0xe0, 0xd0, 0xb5, 0xfa, 0x0d, 0x9a, 0xef, 0x75, 0x44,
	0xed, 0x86, 0xe9, 0x77, 0xaf, 0xc8, 0x5e, 0xf9, 0x15, 0xd9, 0x93, 0x66, 0xb3, 0xd8, 0x6f, 0xb2,
	0xd4, 0x17, 0x2c, 0xc4, 0x67, 0xa6, 0x51, 0xb2, 0x78, 0x87, 0xa5, 0x87, 0x92, 0x82, 0xd6, 0xa0,
	0x9e, 0xd0, 0xe0, 0x84, 0x84, 0x7e, 0xc0, 0x3a, 0x1d, 0x12, 0x0b, 0xe9, 0xdd, 0xa8, 0xf2, 0x6e,
	0x5a, 0x33, 0x36, 0x35, 0x7d, 0x37, 0x44, 0xcb, 0x50, 0x0d, 0x70, 0xec, 0x37, 0x89, 0x08, 0xda,
	0x0a, 0x72, 0x2a, 0x5e, 0x25, 0xc0, 0xf1, 0x8e, 0x5c, 0xa3, 0xf7, 0x01, 0x9d, 0x53, 0xc4, 0x9d,
	0x71, 0x05, 0xa8, 0xf6, 0x80, 0x26, 0xee, 0xfe, 0x63, 0xc1, 0x4c, 0x0e, 0xd9, 0x9a, 0x2c, 0x7b,
	0xeb, 0xd5, 0x1d, 0xe4, 0xd2, 0x2b, 0x86, 0x6e, 0x01, 0xf4, 0xf9, 0xa0, 0x3b, 0x6d, 0x35, 0xe8,
	0x59, 0x7f, 0x1b, 0x6a, 0x86, 0x2d, 0xdb, 0xb7, 0xae, 0x7a, 0x23, 0x71, 0x44, 0x43, 0x59, 0x4c,
	0x66, 0x83, 0xe9, 0xe6, 0x3a, 0x10, 0x53, 0x19, 0xf9, 0x41, 0xd6, 0xd4, 0x3f, 0x80, 0x11, 0x81,
	0x5b, 0xdc, 0x19, 0x5b, 0x29, 0xaf, 0xd6, 0xd6, 0x6f, 0xc9, 0x5c, 0xca, 0x39, 0x4a, 0x8e, 0x51,
	0x1a, 0x40, 0x37, 0xdb, 0x58, 0x6c, 0xe2, 0x34, 0x3c, 0xc4, 0x2d, 0x4f, 0x6d, 0x75, 0xbf, 0x87,
	0xd9, 0xcc, 0xcd, 0x43, 0x76, 0x9d, 0xab, 0xf4, 0xb9, 0xf4, 0x5a, 0xc9, 0x2b, 0xaf, 0x6b, 0xeb,
	0x6f, 0x5d, 0x0e, 0xdb, 0x7d, 0xf1, 0xf4, 0x8c, 0x94, 0x8b, 0x61, 0xee, 0x02, 0x03, 0x86, 0xab,
	0xce, 0x62, 0x80, 0x4b, 0x03, 0x01, 0x76, 0x7f, 0xb2, 0x60, 0x69, 0xbf, 0x1b, 0x45, 0x03, 0x76,
	0x3c, 0xa6, 0x5c, 0xfc, 0xb7, 0x73, 0xe3, 0x3c, 0x8c, 0xb1, 0x66, 0x93, 0x13, 0xd3, 0x8c, 0xb3,
	0x15, 0x9a, 0x85, 0x51, 0xdd, 0xa3, 0x75, 0x6e, 0xf5, 0xc2, 0xfd, 0xd1, 0x82, 0xe5, 0x4b, 0xcd,
	0x1a, 0x2e, 0x00, 0x8f, 0x60, 0xc2, 0x04, 0x20, 0xa2, 0x5c, 0x8f, 0x0b, 0xaf, 0x9d, 0x0a, 0x53,
	0x7d, 0xf2, 0x60, 0xf7, 0x67, 0x0b, 0xa6, 0xf7, 0x69, 0x70, 0xf2, 0x46, 0x60, 0xf5, 0xaa, 0x9b,
	0xb0, 0x0c, 0xd5, 0x13, 0x42, 0xb4, 0x82, 0x0c, 0x12, 0x2a, 0x92, 0x20, 0xa5, 0xdd, 0x4f, 0xc1,
	0x2e, 0x1a, 0x36, 0xdc, 0xc4, 0x2c, 0xa7, 0xcf, 0x1c, 0x05, 0x87, 0x74, 0xab, 0x80, 0x55, 0xa5,
	0x41, 0xac, 0x72, 0x3f, 0x81, 0xe9, 0x82, 0xee, 0xe1, 0x2c, 0xfb, 0x06, 0x6c, 0x85, 0x54, 0xd7,
	0xb1, 0xed, 0x26, 0x54, 0x53, 0x82, 0x83, 0xd3, 0x2e, 0x4d, 0x49, 0x66, 0x58, 0x4e, 0x70, 0x7f,
	0xb0, 0xa0, 0x3e, 0xa0, 0x7a, 0xd8, 0xba, 0x1a, 0x1c, 0xd0, 0x4b, 0xd7, 0x1c, 0xd0, 0x7f, 0x2b,
	0xc1, 0xdc, 0x40, 0xf1, 0xbd, 0xf6, 0x6c, 0xfe, 0xea, 0xeb, 0x2d, 0xab, 0x26, 0xe9, 0xca, 0x61,
	0xf8, 0x2c, 0x31, 0xd3, 0x45, 0x45, 0x12, 0x0e, 0xcf, 0x12, 0xd2, 0x1b, 0xa0, 0xf5, 0x13, 0x46,
	0x0f, 0xd0, 0xf7, 0x60, 0x21, 0x3f, 0x91, 0xbd, 0x88, 0x49, 0x3a, 0x80, 0xab, 0xb3, 0xe6, 0xf0,
	0x27, 0x92, 0x69, 0xd0, 0x75, 0x1d, 0xe6, 0x8c, 0x19, 0x45, 0x21, 0xfd, 0xd0, 0x99, 0xc9, 0x98,
	0x05, 0x19, 0x83, 0xc8, 0xe3, 0xaf, 0x8f, 0xc8, 0x47, 0xb0, 0xf8, 0x90, 0x18, 0x48, 0xe7, 0x3b,
	0x2c, 0xbd, 0x4e, 0x5d, 0xd8, 0x50, 0x0e, 0x32, 0x48, 0x9e, 0xf4, 0xe4, 0xa7, 0xfb, 0x1d, 0x2c,
	0x5d, 0xa6, 0x76, 0xb8, 0x9a, 0xf8, 0x1f, 0x4c, 0x64, 0x8e, 0xe7, 0x58, 0x53, 0xf5, 0x6a, 0x19,
	0x4d, 0x82, 0xc8, 0x5a, 0x98, 0xbf, 0xf4, 0xf4, 0xb8, 0x81, 0x6e, 0x82, 0x53, 0xa4, 0xf8, 0x47,
	0x7b, 0x5b, 0xdb, 0x3b, 0xbb, 0x7b, 0xdb, 0x5b, 0xf6, 0x0d, 0xb4, 0x98, 0xd7, 0x46, 0xc6, 0xfd,
	0x62, 0xf7, 0xe0, 0x60, 0x7b, 0xcb, 0xb6, 0x2e, 0x60, 0x3d, 0xd9, 0xdf, 0x96, 0x52, 0xa5, 0xb5,
	0x5f, 0xad, 0x9c, 0x67, 0x26, 0x58, 0x7d, 0x9a, 0x03, 0xb3, 0xbd, 0xc7, 0x06, 0x15, 0x6d, 0xd6,
	0x15, 0x9b, 0x6d, 0x12, 0x9c, 0xd8, 0x37, 0xd0, 0x5c, 0x3e, 0xe1, 0xef, 0x63, 0xce, 0x35, 0xd9,
	0x42, 0xb3, 0xf9, 0x6b, 0xfa, 0xc1, 0x71, 0xcc, 0xd2, 0x0e, 0x8e, 0xec, 0x12, 0x42, 0xb9, 0x1b,
	0x6a, 0x18, 0x0c, 0xed, 0x32, 0x5a, 0xc8, 0xc7, 0x83, 0x07, 0x4d, 0x41, 0xd2, 0x7d, 0x35, 0x40,
	0xd8, 0x23, 0x68, 0x06, 0xa6, 0xf3, 0x17, 0x68, 0x42, 0x53, 0x12, 0xda, 0xa3, 0x6b, 0xcf, 0x60,
	0xf1, 0xc2, 0xa2, 0x57, 0xa5, 0x39, 0x01, 0x95, 0x0d, 0x92, 0xc9, 0xdf, 0x40, 0x00, 0x63, 0xfa,
	0xc9, 0x6a, 0x5b, 0xf2, 0xe0, 0x5e, 0x4f, 0x54, 0xe5, 0x64, 0x97, 0x50, 0x1d, 0x26, 0x0b, 0x2f,
	0x5b, 0xbb, 0xbc, 0xb1, 0x03, 0x4e, 0xc0, 0x3a, 0x8d, 0x33, 0x7a, 0xc6, 0xba, 0x32, 0x59, 0x1d,
	0x16, 0x92, 0x48, 0xff, 0x28, 0x79, 0xba, 0xd6, 0x62, 0x11, 0x8e, 0x5b, 0x8d, 0x7b, 0xeb, 0x42,
	0x34, 0x02, 0xd6, 0xb9, 0xab, 0xc8, 0x01, 0x8b, 0xee, 0xe2, 0x24, 0xb9, 0x5b, 0xb8, 0xba, 0xc7,
	0x63, 0x8a, 0xf7, 0xe1, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x7c, 0x0b, 0xbf, 0xb1, 0xae, 0x11,
	0x00, 0x00,
}
