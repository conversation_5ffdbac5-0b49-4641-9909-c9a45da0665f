// Code generated by protoc-gen-go. DO NOT EDIT.
// source: unclaimed_.proto

package unclaimed // import "golang.52tt.com/protocol/app/unclaimed"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BlackWhiteBoxSuperviseSwitchStatus int32

const (
	BlackWhiteBoxSuperviseSwitchStatus_None  BlackWhiteBoxSuperviseSwitchStatus = 0
	BlackWhiteBoxSuperviseSwitchStatus_White BlackWhiteBoxSuperviseSwitchStatus = 1
	BlackWhiteBoxSuperviseSwitchStatus_Black BlackWhiteBoxSuperviseSwitchStatus = 2
)

var BlackWhiteBoxSuperviseSwitchStatus_name = map[int32]string{
	0: "None",
	1: "White",
	2: "Black",
}
var BlackWhiteBoxSuperviseSwitchStatus_value = map[string]int32{
	"None":  0,
	"White": 1,
	"Black": 2,
}

func (x BlackWhiteBoxSuperviseSwitchStatus) String() string {
	return proto.EnumName(BlackWhiteBoxSuperviseSwitchStatus_name, int32(x))
}
func (BlackWhiteBoxSuperviseSwitchStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{0}
}

type UserIdentityType int32

const (
	UserIdentityType_UserIdentityType_UNDEFINED UserIdentityType = 0
	UserIdentityType_UserIdentityType_REGULATOR UserIdentityType = 1
)

var UserIdentityType_name = map[int32]string{
	0: "UserIdentityType_UNDEFINED",
	1: "UserIdentityType_REGULATOR",
}
var UserIdentityType_value = map[string]int32{
	"UserIdentityType_UNDEFINED": 0,
	"UserIdentityType_REGULATOR": 1,
}

func (x UserIdentityType) String() string {
	return proto.EnumName(UserIdentityType_name, int32(x))
}
func (UserIdentityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{1}
}

type XunfeiAstSignatureReq_SignatureScene int32

const (
	XunfeiAstSignatureReq_UGC  XunfeiAstSignatureReq_SignatureScene = 0
	XunfeiAstSignatureReq_GAME XunfeiAstSignatureReq_SignatureScene = 1
)

var XunfeiAstSignatureReq_SignatureScene_name = map[int32]string{
	0: "UGC",
	1: "GAME",
}
var XunfeiAstSignatureReq_SignatureScene_value = map[string]int32{
	"UGC":  0,
	"GAME": 1,
}

func (x XunfeiAstSignatureReq_SignatureScene) String() string {
	return proto.EnumName(XunfeiAstSignatureReq_SignatureScene_name, int32(x))
}
func (XunfeiAstSignatureReq_SignatureScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{0, 0}
}

// 根据入口类型获取对应的扩展数据
type ExpandEntrance_ExpandEntranceType int32

const (
	ExpandEntrance_WEB_GAME ExpandEntrance_ExpandEntranceType = 0
)

var ExpandEntrance_ExpandEntranceType_name = map[int32]string{
	0: "WEB_GAME",
}
var ExpandEntrance_ExpandEntranceType_value = map[string]int32{
	"WEB_GAME": 0,
}

func (x ExpandEntrance_ExpandEntranceType) String() string {
	return proto.EnumName(ExpandEntrance_ExpandEntranceType_name, int32(x))
}
func (ExpandEntrance_ExpandEntranceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{4, 0}
}

type XunfeiAstSignatureReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Scene                uint32       `protobuf:"varint,2,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *XunfeiAstSignatureReq) Reset()         { *m = XunfeiAstSignatureReq{} }
func (m *XunfeiAstSignatureReq) String() string { return proto.CompactTextString(m) }
func (*XunfeiAstSignatureReq) ProtoMessage()    {}
func (*XunfeiAstSignatureReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{0}
}
func (m *XunfeiAstSignatureReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_XunfeiAstSignatureReq.Unmarshal(m, b)
}
func (m *XunfeiAstSignatureReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_XunfeiAstSignatureReq.Marshal(b, m, deterministic)
}
func (dst *XunfeiAstSignatureReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_XunfeiAstSignatureReq.Merge(dst, src)
}
func (m *XunfeiAstSignatureReq) XXX_Size() int {
	return xxx_messageInfo_XunfeiAstSignatureReq.Size(m)
}
func (m *XunfeiAstSignatureReq) XXX_DiscardUnknown() {
	xxx_messageInfo_XunfeiAstSignatureReq.DiscardUnknown(m)
}

var xxx_messageInfo_XunfeiAstSignatureReq proto.InternalMessageInfo

func (m *XunfeiAstSignatureReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *XunfeiAstSignatureReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

type XunfeiAstSignatureResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	AuthString           string        `protobuf:"bytes,2,opt,name=auth_string,json=authString,proto3" json:"auth_string,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *XunfeiAstSignatureResp) Reset()         { *m = XunfeiAstSignatureResp{} }
func (m *XunfeiAstSignatureResp) String() string { return proto.CompactTextString(m) }
func (*XunfeiAstSignatureResp) ProtoMessage()    {}
func (*XunfeiAstSignatureResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{1}
}
func (m *XunfeiAstSignatureResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_XunfeiAstSignatureResp.Unmarshal(m, b)
}
func (m *XunfeiAstSignatureResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_XunfeiAstSignatureResp.Marshal(b, m, deterministic)
}
func (dst *XunfeiAstSignatureResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_XunfeiAstSignatureResp.Merge(dst, src)
}
func (m *XunfeiAstSignatureResp) XXX_Size() int {
	return xxx_messageInfo_XunfeiAstSignatureResp.Size(m)
}
func (m *XunfeiAstSignatureResp) XXX_DiscardUnknown() {
	xxx_messageInfo_XunfeiAstSignatureResp.DiscardUnknown(m)
}

var xxx_messageInfo_XunfeiAstSignatureResp proto.InternalMessageInfo

func (m *XunfeiAstSignatureResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *XunfeiAstSignatureResp) GetAuthString() string {
	if m != nil {
		return m.AuthString
	}
	return ""
}

// h5game 扩展数据
type ExpandEntranceWebGameOpt struct {
	CpId                 uint32   `protobuf:"varint,1,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameUrl              string   `protobuf:"bytes,3,opt,name=game_url,json=gameUrl,proto3" json:"game_url,omitempty"`
	GameLink             string   `protobuf:"bytes,4,opt,name=game_link,json=gameLink,proto3" json:"game_link,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpandEntranceWebGameOpt) Reset()         { *m = ExpandEntranceWebGameOpt{} }
func (m *ExpandEntranceWebGameOpt) String() string { return proto.CompactTextString(m) }
func (*ExpandEntranceWebGameOpt) ProtoMessage()    {}
func (*ExpandEntranceWebGameOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{2}
}
func (m *ExpandEntranceWebGameOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpandEntranceWebGameOpt.Unmarshal(m, b)
}
func (m *ExpandEntranceWebGameOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpandEntranceWebGameOpt.Marshal(b, m, deterministic)
}
func (dst *ExpandEntranceWebGameOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpandEntranceWebGameOpt.Merge(dst, src)
}
func (m *ExpandEntranceWebGameOpt) XXX_Size() int {
	return xxx_messageInfo_ExpandEntranceWebGameOpt.Size(m)
}
func (m *ExpandEntranceWebGameOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpandEntranceWebGameOpt.DiscardUnknown(m)
}

var xxx_messageInfo_ExpandEntranceWebGameOpt proto.InternalMessageInfo

func (m *ExpandEntranceWebGameOpt) GetCpId() uint32 {
	if m != nil {
		return m.CpId
	}
	return 0
}

func (m *ExpandEntranceWebGameOpt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ExpandEntranceWebGameOpt) GetGameUrl() string {
	if m != nil {
		return m.GameUrl
	}
	return ""
}

func (m *ExpandEntranceWebGameOpt) GetGameLink() string {
	if m != nil {
		return m.GameLink
	}
	return ""
}

// 红点推送
type PushRedPointReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CreateTime           uint32   `protobuf:"varint,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	EventId              uint32   `protobuf:"varint,3,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	Openid               string   `protobuf:"bytes,4,opt,name=openid,proto3" json:"openid,omitempty"`
	EventMessage         string   `protobuf:"bytes,5,opt,name=event_message,json=eventMessage,proto3" json:"event_message,omitempty"`
	EventTimestamp       int64    `protobuf:"varint,6,opt,name=event_timestamp,json=eventTimestamp,proto3" json:"event_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushRedPointReq) Reset()         { *m = PushRedPointReq{} }
func (m *PushRedPointReq) String() string { return proto.CompactTextString(m) }
func (*PushRedPointReq) ProtoMessage()    {}
func (*PushRedPointReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{3}
}
func (m *PushRedPointReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushRedPointReq.Unmarshal(m, b)
}
func (m *PushRedPointReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushRedPointReq.Marshal(b, m, deterministic)
}
func (dst *PushRedPointReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushRedPointReq.Merge(dst, src)
}
func (m *PushRedPointReq) XXX_Size() int {
	return xxx_messageInfo_PushRedPointReq.Size(m)
}
func (m *PushRedPointReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushRedPointReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushRedPointReq proto.InternalMessageInfo

func (m *PushRedPointReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PushRedPointReq) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PushRedPointReq) GetEventId() uint32 {
	if m != nil {
		return m.EventId
	}
	return 0
}

func (m *PushRedPointReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *PushRedPointReq) GetEventMessage() string {
	if m != nil {
		return m.EventMessage
	}
	return ""
}

func (m *PushRedPointReq) GetEventTimestamp() int64 {
	if m != nil {
		return m.EventTimestamp
	}
	return 0
}

type ExpandEntrance struct {
	EntranceType         ExpandEntrance_ExpandEntranceType `protobuf:"varint,1,opt,name=entrance_type,json=entranceType,proto3,enum=ga.unclaimed.ExpandEntrance_ExpandEntranceType" json:"entrance_type,omitempty"`
	EntranceName         string                            `protobuf:"bytes,2,opt,name=entrance_name,json=entranceName,proto3" json:"entrance_name,omitempty"`
	EntranceOpt          []byte                            `protobuf:"bytes,3,opt,name=entrance_opt,json=entranceOpt,proto3" json:"entrance_opt,omitempty"`
	LastEventUpdateTime  int64                             `protobuf:"varint,4,opt,name=last_event_update_time,json=lastEventUpdateTime,proto3" json:"last_event_update_time,omitempty"`
	EntranceIcon         string                            `protobuf:"bytes,5,opt,name=entrance_icon,json=entranceIcon,proto3" json:"entrance_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ExpandEntrance) Reset()         { *m = ExpandEntrance{} }
func (m *ExpandEntrance) String() string { return proto.CompactTextString(m) }
func (*ExpandEntrance) ProtoMessage()    {}
func (*ExpandEntrance) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{4}
}
func (m *ExpandEntrance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpandEntrance.Unmarshal(m, b)
}
func (m *ExpandEntrance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpandEntrance.Marshal(b, m, deterministic)
}
func (dst *ExpandEntrance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpandEntrance.Merge(dst, src)
}
func (m *ExpandEntrance) XXX_Size() int {
	return xxx_messageInfo_ExpandEntrance.Size(m)
}
func (m *ExpandEntrance) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpandEntrance.DiscardUnknown(m)
}

var xxx_messageInfo_ExpandEntrance proto.InternalMessageInfo

func (m *ExpandEntrance) GetEntranceType() ExpandEntrance_ExpandEntranceType {
	if m != nil {
		return m.EntranceType
	}
	return ExpandEntrance_WEB_GAME
}

func (m *ExpandEntrance) GetEntranceName() string {
	if m != nil {
		return m.EntranceName
	}
	return ""
}

func (m *ExpandEntrance) GetEntranceOpt() []byte {
	if m != nil {
		return m.EntranceOpt
	}
	return nil
}

func (m *ExpandEntrance) GetLastEventUpdateTime() int64 {
	if m != nil {
		return m.LastEventUpdateTime
	}
	return 0
}

func (m *ExpandEntrance) GetEntranceIcon() string {
	if m != nil {
		return m.EntranceIcon
	}
	return ""
}

// 获取扩展入口
type GetExpandEntranceListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetExpandEntranceListReq) Reset()         { *m = GetExpandEntranceListReq{} }
func (m *GetExpandEntranceListReq) String() string { return proto.CompactTextString(m) }
func (*GetExpandEntranceListReq) ProtoMessage()    {}
func (*GetExpandEntranceListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{5}
}
func (m *GetExpandEntranceListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExpandEntranceListReq.Unmarshal(m, b)
}
func (m *GetExpandEntranceListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExpandEntranceListReq.Marshal(b, m, deterministic)
}
func (dst *GetExpandEntranceListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExpandEntranceListReq.Merge(dst, src)
}
func (m *GetExpandEntranceListReq) XXX_Size() int {
	return xxx_messageInfo_GetExpandEntranceListReq.Size(m)
}
func (m *GetExpandEntranceListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExpandEntranceListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExpandEntranceListReq proto.InternalMessageInfo

func (m *GetExpandEntranceListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetExpandEntranceListResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	EntranceList         []*ExpandEntrance `protobuf:"bytes,2,rep,name=entrance_list,json=entranceList,proto3" json:"entrance_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetExpandEntranceListResp) Reset()         { *m = GetExpandEntranceListResp{} }
func (m *GetExpandEntranceListResp) String() string { return proto.CompactTextString(m) }
func (*GetExpandEntranceListResp) ProtoMessage()    {}
func (*GetExpandEntranceListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{6}
}
func (m *GetExpandEntranceListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExpandEntranceListResp.Unmarshal(m, b)
}
func (m *GetExpandEntranceListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExpandEntranceListResp.Marshal(b, m, deterministic)
}
func (dst *GetExpandEntranceListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExpandEntranceListResp.Merge(dst, src)
}
func (m *GetExpandEntranceListResp) XXX_Size() int {
	return xxx_messageInfo_GetExpandEntranceListResp.Size(m)
}
func (m *GetExpandEntranceListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExpandEntranceListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExpandEntranceListResp proto.InternalMessageInfo

func (m *GetExpandEntranceListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetExpandEntranceListResp) GetEntranceList() []*ExpandEntrance {
	if m != nil {
		return m.EntranceList
	}
	return nil
}

// 获取游戏授权信息
type GetOpenGameAuthorizationReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CpId                 uint32       `protobuf:"varint,2,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	GameId               uint32       `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOpenGameAuthorizationReq) Reset()         { *m = GetOpenGameAuthorizationReq{} }
func (m *GetOpenGameAuthorizationReq) String() string { return proto.CompactTextString(m) }
func (*GetOpenGameAuthorizationReq) ProtoMessage()    {}
func (*GetOpenGameAuthorizationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{7}
}
func (m *GetOpenGameAuthorizationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenGameAuthorizationReq.Unmarshal(m, b)
}
func (m *GetOpenGameAuthorizationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenGameAuthorizationReq.Marshal(b, m, deterministic)
}
func (dst *GetOpenGameAuthorizationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenGameAuthorizationReq.Merge(dst, src)
}
func (m *GetOpenGameAuthorizationReq) XXX_Size() int {
	return xxx_messageInfo_GetOpenGameAuthorizationReq.Size(m)
}
func (m *GetOpenGameAuthorizationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenGameAuthorizationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenGameAuthorizationReq proto.InternalMessageInfo

func (m *GetOpenGameAuthorizationReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetOpenGameAuthorizationReq) GetCpId() uint32 {
	if m != nil {
		return m.CpId
	}
	return 0
}

func (m *GetOpenGameAuthorizationReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetOpenGameAuthorizationResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OpenId               string        `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	Code                 string        `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetOpenGameAuthorizationResp) Reset()         { *m = GetOpenGameAuthorizationResp{} }
func (m *GetOpenGameAuthorizationResp) String() string { return proto.CompactTextString(m) }
func (*GetOpenGameAuthorizationResp) ProtoMessage()    {}
func (*GetOpenGameAuthorizationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{8}
}
func (m *GetOpenGameAuthorizationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenGameAuthorizationResp.Unmarshal(m, b)
}
func (m *GetOpenGameAuthorizationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenGameAuthorizationResp.Marshal(b, m, deterministic)
}
func (dst *GetOpenGameAuthorizationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenGameAuthorizationResp.Merge(dst, src)
}
func (m *GetOpenGameAuthorizationResp) XXX_Size() int {
	return xxx_messageInfo_GetOpenGameAuthorizationResp.Size(m)
}
func (m *GetOpenGameAuthorizationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenGameAuthorizationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenGameAuthorizationResp proto.InternalMessageInfo

func (m *GetOpenGameAuthorizationResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetOpenGameAuthorizationResp) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GetOpenGameAuthorizationResp) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

// 设置房间多媒体信息，如直播流
type SetChannelMediaReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	VideoLink            string       `protobuf:"bytes,3,opt,name=video_link,json=videoLink,proto3" json:"video_link,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetChannelMediaReq) Reset()         { *m = SetChannelMediaReq{} }
func (m *SetChannelMediaReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMediaReq) ProtoMessage()    {}
func (*SetChannelMediaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{9}
}
func (m *SetChannelMediaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMediaReq.Unmarshal(m, b)
}
func (m *SetChannelMediaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMediaReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelMediaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMediaReq.Merge(dst, src)
}
func (m *SetChannelMediaReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelMediaReq.Size(m)
}
func (m *SetChannelMediaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMediaReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMediaReq proto.InternalMessageInfo

func (m *SetChannelMediaReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetChannelMediaReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMediaReq) GetVideoLink() string {
	if m != nil {
		return m.VideoLink
	}
	return ""
}

type SetChannelMediaResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChannelMediaResp) Reset()         { *m = SetChannelMediaResp{} }
func (m *SetChannelMediaResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMediaResp) ProtoMessage()    {}
func (*SetChannelMediaResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{10}
}
func (m *SetChannelMediaResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelMediaResp.Unmarshal(m, b)
}
func (m *SetChannelMediaResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelMediaResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelMediaResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelMediaResp.Merge(dst, src)
}
func (m *SetChannelMediaResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelMediaResp.Size(m)
}
func (m *SetChannelMediaResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelMediaResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelMediaResp proto.InternalMessageInfo

func (m *SetChannelMediaResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChannelMediaChangeMsg struct {
	VideoLink            string   `protobuf:"bytes,1,opt,name=video_link,json=videoLink,proto3" json:"video_link,omitempty"`
	UpdateTime           int64    `protobuf:"varint,2,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMediaChangeMsg) Reset()         { *m = ChannelMediaChangeMsg{} }
func (m *ChannelMediaChangeMsg) String() string { return proto.CompactTextString(m) }
func (*ChannelMediaChangeMsg) ProtoMessage()    {}
func (*ChannelMediaChangeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{11}
}
func (m *ChannelMediaChangeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMediaChangeMsg.Unmarshal(m, b)
}
func (m *ChannelMediaChangeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMediaChangeMsg.Marshal(b, m, deterministic)
}
func (dst *ChannelMediaChangeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMediaChangeMsg.Merge(dst, src)
}
func (m *ChannelMediaChangeMsg) XXX_Size() int {
	return xxx_messageInfo_ChannelMediaChangeMsg.Size(m)
}
func (m *ChannelMediaChangeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMediaChangeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMediaChangeMsg proto.InternalMessageInfo

func (m *ChannelMediaChangeMsg) GetVideoLink() string {
	if m != nil {
		return m.VideoLink
	}
	return ""
}

func (m *ChannelMediaChangeMsg) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetObsUploadTokenReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Scope                string       `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetObsUploadTokenReq) Reset()         { *m = GetObsUploadTokenReq{} }
func (m *GetObsUploadTokenReq) String() string { return proto.CompactTextString(m) }
func (*GetObsUploadTokenReq) ProtoMessage()    {}
func (*GetObsUploadTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{12}
}
func (m *GetObsUploadTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetObsUploadTokenReq.Unmarshal(m, b)
}
func (m *GetObsUploadTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetObsUploadTokenReq.Marshal(b, m, deterministic)
}
func (dst *GetObsUploadTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetObsUploadTokenReq.Merge(dst, src)
}
func (m *GetObsUploadTokenReq) XXX_Size() int {
	return xxx_messageInfo_GetObsUploadTokenReq.Size(m)
}
func (m *GetObsUploadTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetObsUploadTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetObsUploadTokenReq proto.InternalMessageInfo

func (m *GetObsUploadTokenReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetObsUploadTokenReq) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

type GetObsUploadTokenResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Token                string        `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	ExpireAt             uint32        `protobuf:"varint,3,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetObsUploadTokenResp) Reset()         { *m = GetObsUploadTokenResp{} }
func (m *GetObsUploadTokenResp) String() string { return proto.CompactTextString(m) }
func (*GetObsUploadTokenResp) ProtoMessage()    {}
func (*GetObsUploadTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{13}
}
func (m *GetObsUploadTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetObsUploadTokenResp.Unmarshal(m, b)
}
func (m *GetObsUploadTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetObsUploadTokenResp.Marshal(b, m, deterministic)
}
func (dst *GetObsUploadTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetObsUploadTokenResp.Merge(dst, src)
}
func (m *GetObsUploadTokenResp) XXX_Size() int {
	return xxx_messageInfo_GetObsUploadTokenResp.Size(m)
}
func (m *GetObsUploadTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetObsUploadTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetObsUploadTokenResp proto.InternalMessageInfo

func (m *GetObsUploadTokenResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetObsUploadTokenResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *GetObsUploadTokenResp) GetExpireAt() uint32 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

type GetMarketingUserInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMarketingUserInfoReq) Reset()         { *m = GetMarketingUserInfoReq{} }
func (m *GetMarketingUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMarketingUserInfoReq) ProtoMessage()    {}
func (*GetMarketingUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{14}
}
func (m *GetMarketingUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMarketingUserInfoReq.Unmarshal(m, b)
}
func (m *GetMarketingUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMarketingUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetMarketingUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMarketingUserInfoReq.Merge(dst, src)
}
func (m *GetMarketingUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetMarketingUserInfoReq.Size(m)
}
func (m *GetMarketingUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMarketingUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMarketingUserInfoReq proto.InternalMessageInfo

func (m *GetMarketingUserInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetMarketingUserInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Type                 string        `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetMarketingUserInfoResp) Reset()         { *m = GetMarketingUserInfoResp{} }
func (m *GetMarketingUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMarketingUserInfoResp) ProtoMessage()    {}
func (*GetMarketingUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{15}
}
func (m *GetMarketingUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMarketingUserInfoResp.Unmarshal(m, b)
}
func (m *GetMarketingUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMarketingUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetMarketingUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMarketingUserInfoResp.Merge(dst, src)
}
func (m *GetMarketingUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetMarketingUserInfoResp.Size(m)
}
func (m *GetMarketingUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMarketingUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMarketingUserInfoResp proto.InternalMessageInfo

func (m *GetMarketingUserInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMarketingUserInfoResp) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

type GetBlackWhiteBoxSuperviseSwitchReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBlackWhiteBoxSuperviseSwitchReq) Reset()         { *m = GetBlackWhiteBoxSuperviseSwitchReq{} }
func (m *GetBlackWhiteBoxSuperviseSwitchReq) String() string { return proto.CompactTextString(m) }
func (*GetBlackWhiteBoxSuperviseSwitchReq) ProtoMessage()    {}
func (*GetBlackWhiteBoxSuperviseSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{16}
}
func (m *GetBlackWhiteBoxSuperviseSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchReq.Unmarshal(m, b)
}
func (m *GetBlackWhiteBoxSuperviseSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchReq.Marshal(b, m, deterministic)
}
func (dst *GetBlackWhiteBoxSuperviseSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchReq.Merge(dst, src)
}
func (m *GetBlackWhiteBoxSuperviseSwitchReq) XXX_Size() int {
	return xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchReq.Size(m)
}
func (m *GetBlackWhiteBoxSuperviseSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchReq proto.InternalMessageInfo

func (m *GetBlackWhiteBoxSuperviseSwitchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetBlackWhiteBoxSuperviseSwitchResp struct {
	BaseResp             *app.BaseResp                      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Status               BlackWhiteBoxSuperviseSwitchStatus `protobuf:"varint,2,opt,name=status,proto3,enum=ga.unclaimed.BlackWhiteBoxSuperviseSwitchStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *GetBlackWhiteBoxSuperviseSwitchResp) Reset()         { *m = GetBlackWhiteBoxSuperviseSwitchResp{} }
func (m *GetBlackWhiteBoxSuperviseSwitchResp) String() string { return proto.CompactTextString(m) }
func (*GetBlackWhiteBoxSuperviseSwitchResp) ProtoMessage()    {}
func (*GetBlackWhiteBoxSuperviseSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{17}
}
func (m *GetBlackWhiteBoxSuperviseSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchResp.Unmarshal(m, b)
}
func (m *GetBlackWhiteBoxSuperviseSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchResp.Marshal(b, m, deterministic)
}
func (dst *GetBlackWhiteBoxSuperviseSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchResp.Merge(dst, src)
}
func (m *GetBlackWhiteBoxSuperviseSwitchResp) XXX_Size() int {
	return xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchResp.Size(m)
}
func (m *GetBlackWhiteBoxSuperviseSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlackWhiteBoxSuperviseSwitchResp proto.InternalMessageInfo

func (m *GetBlackWhiteBoxSuperviseSwitchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBlackWhiteBoxSuperviseSwitchResp) GetStatus() BlackWhiteBoxSuperviseSwitchStatus {
	if m != nil {
		return m.Status
	}
	return BlackWhiteBoxSuperviseSwitchStatus_None
}

type CheckUserIdentityReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Oaid                 string       `protobuf:"bytes,2,opt,name=oaid,proto3" json:"oaid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckUserIdentityReq) Reset()         { *m = CheckUserIdentityReq{} }
func (m *CheckUserIdentityReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserIdentityReq) ProtoMessage()    {}
func (*CheckUserIdentityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{18}
}
func (m *CheckUserIdentityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserIdentityReq.Unmarshal(m, b)
}
func (m *CheckUserIdentityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserIdentityReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserIdentityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserIdentityReq.Merge(dst, src)
}
func (m *CheckUserIdentityReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserIdentityReq.Size(m)
}
func (m *CheckUserIdentityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserIdentityReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserIdentityReq proto.InternalMessageInfo

func (m *CheckUserIdentityReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckUserIdentityReq) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

type CheckUserIdentityResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Identities           []UserIdentityType `protobuf:"varint,2,rep,packed,name=identities,proto3,enum=ga.unclaimed.UserIdentityType" json:"identities,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CheckUserIdentityResp) Reset()         { *m = CheckUserIdentityResp{} }
func (m *CheckUserIdentityResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserIdentityResp) ProtoMessage()    {}
func (*CheckUserIdentityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unclaimed__8e8ffef668b8b9d0, []int{19}
}
func (m *CheckUserIdentityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserIdentityResp.Unmarshal(m, b)
}
func (m *CheckUserIdentityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserIdentityResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserIdentityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserIdentityResp.Merge(dst, src)
}
func (m *CheckUserIdentityResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserIdentityResp.Size(m)
}
func (m *CheckUserIdentityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserIdentityResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserIdentityResp proto.InternalMessageInfo

func (m *CheckUserIdentityResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckUserIdentityResp) GetIdentities() []UserIdentityType {
	if m != nil {
		return m.Identities
	}
	return nil
}

func init() {
	proto.RegisterType((*XunfeiAstSignatureReq)(nil), "ga.unclaimed.XunfeiAstSignatureReq")
	proto.RegisterType((*XunfeiAstSignatureResp)(nil), "ga.unclaimed.XunfeiAstSignatureResp")
	proto.RegisterType((*ExpandEntranceWebGameOpt)(nil), "ga.unclaimed.ExpandEntranceWebGameOpt")
	proto.RegisterType((*PushRedPointReq)(nil), "ga.unclaimed.PushRedPointReq")
	proto.RegisterType((*ExpandEntrance)(nil), "ga.unclaimed.ExpandEntrance")
	proto.RegisterType((*GetExpandEntranceListReq)(nil), "ga.unclaimed.GetExpandEntranceListReq")
	proto.RegisterType((*GetExpandEntranceListResp)(nil), "ga.unclaimed.GetExpandEntranceListResp")
	proto.RegisterType((*GetOpenGameAuthorizationReq)(nil), "ga.unclaimed.GetOpenGameAuthorizationReq")
	proto.RegisterType((*GetOpenGameAuthorizationResp)(nil), "ga.unclaimed.GetOpenGameAuthorizationResp")
	proto.RegisterType((*SetChannelMediaReq)(nil), "ga.unclaimed.SetChannelMediaReq")
	proto.RegisterType((*SetChannelMediaResp)(nil), "ga.unclaimed.SetChannelMediaResp")
	proto.RegisterType((*ChannelMediaChangeMsg)(nil), "ga.unclaimed.ChannelMediaChangeMsg")
	proto.RegisterType((*GetObsUploadTokenReq)(nil), "ga.unclaimed.GetObsUploadTokenReq")
	proto.RegisterType((*GetObsUploadTokenResp)(nil), "ga.unclaimed.GetObsUploadTokenResp")
	proto.RegisterType((*GetMarketingUserInfoReq)(nil), "ga.unclaimed.GetMarketingUserInfoReq")
	proto.RegisterType((*GetMarketingUserInfoResp)(nil), "ga.unclaimed.GetMarketingUserInfoResp")
	proto.RegisterType((*GetBlackWhiteBoxSuperviseSwitchReq)(nil), "ga.unclaimed.GetBlackWhiteBoxSuperviseSwitchReq")
	proto.RegisterType((*GetBlackWhiteBoxSuperviseSwitchResp)(nil), "ga.unclaimed.GetBlackWhiteBoxSuperviseSwitchResp")
	proto.RegisterType((*CheckUserIdentityReq)(nil), "ga.unclaimed.CheckUserIdentityReq")
	proto.RegisterType((*CheckUserIdentityResp)(nil), "ga.unclaimed.CheckUserIdentityResp")
	proto.RegisterEnum("ga.unclaimed.BlackWhiteBoxSuperviseSwitchStatus", BlackWhiteBoxSuperviseSwitchStatus_name, BlackWhiteBoxSuperviseSwitchStatus_value)
	proto.RegisterEnum("ga.unclaimed.UserIdentityType", UserIdentityType_name, UserIdentityType_value)
	proto.RegisterEnum("ga.unclaimed.XunfeiAstSignatureReq_SignatureScene", XunfeiAstSignatureReq_SignatureScene_name, XunfeiAstSignatureReq_SignatureScene_value)
	proto.RegisterEnum("ga.unclaimed.ExpandEntrance_ExpandEntranceType", ExpandEntrance_ExpandEntranceType_name, ExpandEntrance_ExpandEntranceType_value)
}

func init() { proto.RegisterFile("unclaimed_.proto", fileDescriptor_unclaimed__8e8ffef668b8b9d0) }

var fileDescriptor_unclaimed__8e8ffef668b8b9d0 = []byte{
	// 1028 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x56, 0xdb, 0x72, 0xe2, 0x46,
	0x10, 0xb5, 0x00, 0xdb, 0xd0, 0xc6, 0x2c, 0x35, 0xbe, 0xb1, 0x97, 0x6c, 0x1c, 0xb9, 0x6a, 0xe3,
	0xec, 0x03, 0x4e, 0xb1, 0x95, 0xd7, 0x54, 0xc0, 0x26, 0x84, 0x2a, 0x5f, 0xb6, 0x04, 0x94, 0x93,
	0xbc, 0xa8, 0x06, 0xa9, 0x57, 0x4c, 0x21, 0x66, 0xb4, 0x9a, 0x91, 0x63, 0xfb, 0x29, 0x95, 0xb7,
	0xfc, 0x42, 0xbe, 0x21, 0xbf, 0x91, 0xff, 0x4a, 0xcd, 0x48, 0x60, 0x7c, 0xd9, 0xdd, 0xd2, 0x5b,
	0xf7, 0xe9, 0xe9, 0x3e, 0xd3, 0xad, 0x39, 0x0d, 0x50, 0x4f, 0xb8, 0x17, 0x52, 0x36, 0x43, 0xdf,
	0x6d, 0x46, 0xb1, 0x50, 0x82, 0x54, 0x03, 0xda, 0x5c, 0x80, 0x2f, 0x36, 0x03, 0xea, 0x8e, 0xa9,
	0xc4, 0x34, 0x68, 0xdf, 0xc2, 0xce, 0xaf, 0x09, 0xff, 0x80, 0xac, 0x2d, 0xd5, 0x80, 0x05, 0x9c,
	0xaa, 0x24, 0x46, 0x07, 0x3f, 0x92, 0x37, 0x50, 0xd6, 0xc7, 0xdc, 0x18, 0x3f, 0x36, 0xac, 0x7d,
	0xeb, 0x70, 0xa3, 0xb5, 0xd1, 0x0c, 0x68, 0xb3, 0x43, 0xa5, 0x0e, 0x3b, 0xeb, 0xe3, 0xd4, 0x20,
	0xdb, 0xb0, 0x2a, 0x3d, 0xe4, 0xd8, 0x28, 0xec, 0x5b, 0x87, 0x9b, 0x4e, 0xea, 0xd8, 0x07, 0x50,
	0x5b, 0x54, 0x1b, 0x68, 0x84, 0xac, 0x43, 0x71, 0xd4, 0x3b, 0xae, 0xaf, 0x90, 0x32, 0x94, 0x7a,
	0xed, 0xb3, 0x6e, 0xdd, 0xb2, 0x7d, 0xd8, 0x7d, 0x8a, 0x5b, 0x46, 0xe4, 0x3b, 0xa8, 0x64, 0xe4,
	0x32, 0xca, 0xd8, 0xab, 0x77, 0xec, 0x32, 0x72, 0xca, 0xe3, 0xcc, 0x22, 0x5f, 0xc3, 0x06, 0x4d,
	0xd4, 0xc4, 0x95, 0x2a, 0x66, 0x3c, 0x30, 0xb7, 0xa8, 0x38, 0xa0, 0xa1, 0x81, 0x41, 0xec, 0x3f,
	0x2d, 0x68, 0x74, 0xaf, 0x23, 0xca, 0xfd, 0x2e, 0x57, 0x31, 0xe5, 0x1e, 0x5e, 0xe2, 0xb8, 0x47,
	0x67, 0x78, 0x11, 0x29, 0xb2, 0x05, 0xab, 0x5e, 0xe4, 0x32, 0xdf, 0x90, 0x6c, 0x3a, 0x25, 0x2f,
	0xea, 0xfb, 0x64, 0x0f, 0xd6, 0x03, 0x3a, 0x43, 0x0d, 0xa7, 0x4d, 0xad, 0x69, 0xb7, 0xef, 0x93,
	0xe7, 0x50, 0x36, 0x81, 0x24, 0x0e, 0x1b, 0x45, 0x43, 0x64, 0x0e, 0x8e, 0xe2, 0x90, 0xbc, 0x84,
	0x8a, 0x09, 0x85, 0x8c, 0x4f, 0x1b, 0x25, 0x13, 0x33, 0x67, 0x4f, 0x19, 0x9f, 0xda, 0xff, 0x59,
	0xf0, 0xec, 0x7d, 0x22, 0x27, 0x0e, 0xfa, 0xef, 0x05, 0xe3, 0x4a, 0xcf, 0xad, 0x0e, 0xc5, 0x64,
	0xc1, 0xab, 0x4d, 0xdd, 0x89, 0x17, 0x23, 0x55, 0xe8, 0x2a, 0x36, 0x9b, 0xcf, 0x13, 0x52, 0x68,
	0xc8, 0x66, 0xa8, 0xe9, 0xf1, 0x0a, 0xb9, 0xd2, 0x17, 0x2b, 0x9a, 0xe8, 0xba, 0xf1, 0xfb, 0x3e,
	0xd9, 0x85, 0x35, 0x11, 0x21, 0x67, 0x7e, 0xc6, 0x9d, 0x79, 0xe4, 0x00, 0x36, 0xd3, 0x94, 0x19,
	0x4a, 0x49, 0x03, 0x6c, 0xac, 0x9a, 0x70, 0xd5, 0x80, 0x67, 0x29, 0x46, 0xbe, 0x85, 0x67, 0xe9,
	0x21, 0xcd, 0x2b, 0x15, 0x9d, 0x45, 0x8d, 0xb5, 0x7d, 0xeb, 0xb0, 0xe8, 0xd4, 0x0c, 0x3c, 0x9c,
	0xa3, 0xf6, 0xbf, 0x05, 0xa8, 0xdd, 0x1f, 0x25, 0x19, 0xc2, 0x26, 0x66, 0xb6, 0xab, 0x6e, 0x22,
	0x34, 0x0d, 0xd5, 0x5a, 0x47, 0xcd, 0xe5, 0x47, 0xd7, 0xbc, 0x9f, 0xf4, 0xc0, 0x1d, 0xde, 0x44,
	0xe8, 0x54, 0x71, 0xc9, 0x33, 0xd7, 0x9e, 0x57, 0xe5, 0x34, 0x1b, 0x46, 0xe5, 0xee, 0xd0, 0x39,
	0x9d, 0x21, 0xf9, 0x06, 0x16, 0xbe, 0x2b, 0x22, 0x65, 0x46, 0x52, 0x75, 0x36, 0xe6, 0x98, 0xfe,
	0xbc, 0xef, 0x60, 0x37, 0xa4, 0x52, 0xb9, 0x69, 0x7b, 0x49, 0xe4, 0x2f, 0xa6, 0x5b, 0x32, 0x0d,
	0x6e, 0xe9, 0x68, 0x57, 0x07, 0x47, 0x26, 0x66, 0xc6, 0xbc, 0x4c, 0xce, 0x3c, 0xc1, 0x17, 0x33,
	0xcb, 0xc0, 0xbe, 0x27, 0xb8, 0x6d, 0x03, 0x79, 0xdc, 0x05, 0xa9, 0x42, 0xf9, 0xb2, 0xdb, 0x71,
	0xcd, 0xfb, 0x5e, 0xb1, 0x3b, 0xd0, 0xe8, 0xa1, 0xba, 0x7f, 0xec, 0x94, 0x49, 0x95, 0x43, 0x5e,
	0xf6, 0xdf, 0x16, 0x3c, 0xff, 0x44, 0x91, 0x7c, 0x3a, 0x69, 0x2f, 0x75, 0x15, 0x32, 0xa9, 0x1a,
	0x85, 0xfd, 0xe2, 0xe1, 0x46, 0xeb, 0xd5, 0xe7, 0x3e, 0xd4, 0x5d, 0xcf, 0x9a, 0xd1, 0x96, 0xf0,
	0xb2, 0x87, 0xea, 0x22, 0x42, 0xae, 0xe5, 0xd3, 0x4e, 0xd4, 0x44, 0xc4, 0xec, 0x96, 0x2a, 0x26,
	0x78, 0x9e, 0x8d, 0xb1, 0xd0, 0x5c, 0xe1, 0x69, 0xcd, 0x15, 0x97, 0x35, 0x67, 0x5f, 0xc1, 0xab,
	0x4f, 0x93, 0xe6, 0x1b, 0xc1, 0x1e, 0xac, 0x6b, 0x59, 0xcc, 0xa9, 0x33, 0x95, 0xf4, 0x7d, 0x42,
	0xa0, 0xe4, 0x09, 0x1f, 0x33, 0x4d, 0x1b, 0xdb, 0xbe, 0x05, 0x32, 0x40, 0x75, 0x3c, 0xa1, 0x9c,
	0x63, 0x78, 0x86, 0x3e, 0xa3, 0x79, 0x7a, 0xfc, 0x0a, 0xc0, 0x4b, 0x53, 0xef, 0x1a, 0xad, 0x64,
	0x48, 0xdf, 0xd7, 0xe1, 0x2b, 0xe6, 0xa3, 0x48, 0xd7, 0x45, 0x4a, 0x5b, 0x31, 0x88, 0xd9, 0x17,
	0x3f, 0xc1, 0xd6, 0x23, 0xee, 0x5c, 0xad, 0xda, 0x97, 0xb0, 0xb3, 0x9c, 0xae, 0xed, 0x00, 0xcf,
	0x64, 0xf0, 0x80, 0xd9, 0x7a, 0xc0, 0xac, 0x77, 0xd0, 0xb2, 0x4a, 0x0a, 0x46, 0x25, 0x90, 0x2c,
	0xc4, 0x61, 0x0f, 0x61, 0x5b, 0x7f, 0x8e, 0xb1, 0x1c, 0x45, 0xa1, 0xa0, 0xfe, 0x50, 0x4c, 0x91,
	0xe7, 0xfe, 0xb9, 0x10, 0xd1, 0x5c, 0xd1, 0xa9, 0x63, 0x27, 0xb0, 0xf3, 0x44, 0xd5, 0x7c, 0x5f,
	0x77, 0x1b, 0x56, 0x95, 0xce, 0x9b, 0x57, 0x36, 0x8e, 0xde, 0xcb, 0x78, 0x1d, 0xb1, 0x18, 0x5d,
	0xaa, 0xb2, 0x97, 0x55, 0x4e, 0x81, 0xb6, 0xb2, 0xdb, 0xb0, 0xd7, 0x43, 0x75, 0x46, 0xe3, 0x29,
	0x2a, 0xc6, 0x83, 0x91, 0xc4, 0xb8, 0xcf, 0x3f, 0x88, 0x3c, 0xfa, 0xfc, 0xcd, 0x68, 0xfc, 0x89,
	0x12, 0xf9, 0x2e, 0x4f, 0xa0, 0x64, 0xb6, 0x67, 0x7a, 0x77, 0x63, 0xdb, 0xa7, 0x60, 0xf7, 0x50,
	0x75, 0x42, 0xea, 0x4d, 0x2f, 0x27, 0x4c, 0x61, 0x47, 0x5c, 0x0f, 0x92, 0x08, 0xe3, 0x2b, 0x26,
	0x71, 0xf0, 0x07, 0x53, 0xde, 0x24, 0xcf, 0x45, 0xff, 0xb1, 0xe0, 0xe0, 0x8b, 0xe5, 0xf2, 0x5d,
	0xfa, 0x17, 0x58, 0x93, 0x8a, 0xaa, 0x44, 0x9a, 0x6b, 0xd7, 0x5a, 0xdf, 0xdf, 0xdf, 0x25, 0x9f,
	0xa3, 0x1a, 0x98, 0x3c, 0x27, 0xcb, 0xb7, 0x1d, 0xd8, 0x3e, 0x9e, 0xa0, 0x37, 0x35, 0xe3, 0xf3,
	0x91, 0x2b, 0xa6, 0x6e, 0xf2, 0xbc, 0x2a, 0x02, 0x25, 0x41, 0x17, 0xb2, 0x36, 0xb6, 0xfd, 0x97,
	0xa5, 0x35, 0xf0, 0xa8, 0x68, 0xbe, 0x16, 0x7f, 0x04, 0x60, 0x69, 0x2a, 0x43, 0x69, 0x56, 0x66,
	0xad, 0xf5, 0xfa, 0x7e, 0x9b, 0xcb, 0xe5, 0xcd, 0x4f, 0xd9, 0x52, 0xc6, 0xdb, 0x13, 0xb0, 0xbf,
	0x3c, 0x06, 0xfd, 0x97, 0xe8, 0x5c, 0x70, 0xac, 0xaf, 0x90, 0x0a, 0xac, 0x9a, 0xa3, 0x75, 0x4b,
	0x9b, 0x26, 0xb5, 0x5e, 0x78, 0xeb, 0x40, 0xfd, 0x21, 0x0b, 0x79, 0x0d, 0x2f, 0x1e, 0x62, 0xee,
	0xe8, 0xfc, 0xa4, 0xfb, 0x73, 0xff, 0xbc, 0x7b, 0x52, 0x5f, 0x79, 0x32, 0xee, 0x74, 0x7b, 0xa3,
	0xd3, 0xf6, 0xf0, 0xc2, 0xa9, 0x5b, 0x9d, 0x0e, 0x34, 0x3c, 0x31, 0x6b, 0xde, 0xb0, 0x1b, 0x91,
	0xe8, 0x86, 0x66, 0xc2, 0xc7, 0x30, 0xfd, 0x53, 0xf8, 0xfb, 0x9b, 0x40, 0x84, 0x94, 0x07, 0xcd,
	0x1f, 0x5a, 0x4a, 0x35, 0x3d, 0x31, 0x3b, 0x32, 0xb0, 0x27, 0xc2, 0x23, 0x1a, 0x45, 0x47, 0x8b,
	0xd6, 0xc7, 0x6b, 0x06, 0x7f, 0xf7, 0x7f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x2a, 0xaa, 0x0d, 0x1a,
	0x74, 0x0a, 0x00, 0x00,
}
