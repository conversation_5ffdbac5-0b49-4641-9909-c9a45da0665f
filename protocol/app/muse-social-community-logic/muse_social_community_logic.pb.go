// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse_social_community_logic/muse_social_community_logic.proto

package muse_social_community_logic // import "golang.52tt.com/protocol/app/muse-social-community-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import music_topic_channel "golang.52tt.com/protocol/app/music-topic-channel"
import sync "golang.52tt.com/protocol/app/sync"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MuseSocialCommunityNavBarFlag int32

const (
	MuseSocialCommunityNavBarFlag_MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_UNSPECIFIED MuseSocialCommunityNavBarFlag = 0
	MuseSocialCommunityNavBarFlag_MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_MINE        MuseSocialCommunityNavBarFlag = 1
	MuseSocialCommunityNavBarFlag_MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_RECENT      MuseSocialCommunityNavBarFlag = 2
	MuseSocialCommunityNavBarFlag_MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_TOURIST     MuseSocialCommunityNavBarFlag = 3
)

var MuseSocialCommunityNavBarFlag_name = map[int32]string{
	0: "MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_UNSPECIFIED",
	1: "MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_MINE",
	2: "MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_RECENT",
	3: "MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_TOURIST",
}
var MuseSocialCommunityNavBarFlag_value = map[string]int32{
	"MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_UNSPECIFIED": 0,
	"MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_MINE":        1,
	"MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_RECENT":      2,
	"MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_TOURIST":     3,
}

func (x MuseSocialCommunityNavBarFlag) String() string {
	return proto.EnumName(MuseSocialCommunityNavBarFlag_name, int32(x))
}
func (MuseSocialCommunityNavBarFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{0}
}

type BrandChannelType int32

const (
	BrandChannelType_BRAND_CHANNEL_TYPE_UNSPECIFIED BrandChannelType = 0
	BrandChannelType_BRAND_CHANNEL_TYPE_CHAT        BrandChannelType = 1
	BrandChannelType_BRAND_CHANNEL_TYPE_SHOW        BrandChannelType = 2
)

var BrandChannelType_name = map[int32]string{
	0: "BRAND_CHANNEL_TYPE_UNSPECIFIED",
	1: "BRAND_CHANNEL_TYPE_CHAT",
	2: "BRAND_CHANNEL_TYPE_SHOW",
}
var BrandChannelType_value = map[string]int32{
	"BRAND_CHANNEL_TYPE_UNSPECIFIED": 0,
	"BRAND_CHANNEL_TYPE_CHAT":        1,
	"BRAND_CHANNEL_TYPE_SHOW":        2,
}

func (x BrandChannelType) String() string {
	return proto.EnumName(BrandChannelType_name, int32(x))
}
func (BrandChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{1}
}

// 用户社群角色
type BrandMemberRoleV2 int32

const (
	BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_UNSPECIFIED  BrandMemberRoleV2 = 0
	BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_CAPTAIN      BrandMemberRoleV2 = 1
	BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_KERNEL       BrandMemberRoleV2 = 2
	BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_VICE_CAPTAIN BrandMemberRoleV2 = 3
	BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_PRODUCER     BrandMemberRoleV2 = 4
	BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_FANS         BrandMemberRoleV2 = 5
)

var BrandMemberRoleV2_name = map[int32]string{
	0: "BRAND_MEMBER_ROLE_V2_UNSPECIFIED",
	1: "BRAND_MEMBER_ROLE_V2_CAPTAIN",
	2: "BRAND_MEMBER_ROLE_V2_KERNEL",
	3: "BRAND_MEMBER_ROLE_V2_VICE_CAPTAIN",
	4: "BRAND_MEMBER_ROLE_V2_PRODUCER",
	5: "BRAND_MEMBER_ROLE_V2_FANS",
}
var BrandMemberRoleV2_value = map[string]int32{
	"BRAND_MEMBER_ROLE_V2_UNSPECIFIED":  0,
	"BRAND_MEMBER_ROLE_V2_CAPTAIN":      1,
	"BRAND_MEMBER_ROLE_V2_KERNEL":       2,
	"BRAND_MEMBER_ROLE_V2_VICE_CAPTAIN": 3,
	"BRAND_MEMBER_ROLE_V2_PRODUCER":     4,
	"BRAND_MEMBER_ROLE_V2_FANS":         5,
}

func (x BrandMemberRoleV2) String() string {
	return proto.EnumName(BrandMemberRoleV2_name, int32(x))
}
func (BrandMemberRoleV2) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{2}
}

type SocialCommunityRankType int32

const (
	SocialCommunityRankType_SOCIAL_COMMUNITY_RANK_TYPE_UNSPECIFIED SocialCommunityRankType = 0
	SocialCommunityRankType_SOCIAL_COMMUNITY_RANK_TYPE_WEEK        SocialCommunityRankType = 1
	SocialCommunityRankType_SOCIAL_COMMUNITY_RANK_TYPE_HOUR        SocialCommunityRankType = 2
)

var SocialCommunityRankType_name = map[int32]string{
	0: "SOCIAL_COMMUNITY_RANK_TYPE_UNSPECIFIED",
	1: "SOCIAL_COMMUNITY_RANK_TYPE_WEEK",
	2: "SOCIAL_COMMUNITY_RANK_TYPE_HOUR",
}
var SocialCommunityRankType_value = map[string]int32{
	"SOCIAL_COMMUNITY_RANK_TYPE_UNSPECIFIED": 0,
	"SOCIAL_COMMUNITY_RANK_TYPE_WEEK":        1,
	"SOCIAL_COMMUNITY_RANK_TYPE_HOUR":        2,
}

func (x SocialCommunityRankType) String() string {
	return proto.EnumName(SocialCommunityRankType_name, int32(x))
}
func (SocialCommunityRankType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{3}
}

type MemberStatus int32

const (
	MemberStatus_MEMBER_STATUS_UNSPECIFIED MemberStatus = 0
	MemberStatus_MEMBER_STATUS_REVOKE      MemberStatus = 1
	MemberStatus_MEMBER_STATUS_EXIT        MemberStatus = 2
)

var MemberStatus_name = map[int32]string{
	0: "MEMBER_STATUS_UNSPECIFIED",
	1: "MEMBER_STATUS_REVOKE",
	2: "MEMBER_STATUS_EXIT",
}
var MemberStatus_value = map[string]int32{
	"MEMBER_STATUS_UNSPECIFIED": 0,
	"MEMBER_STATUS_REVOKE":      1,
	"MEMBER_STATUS_EXIT":        2,
}

func (x MemberStatus) String() string {
	return proto.EnumName(MemberStatus_name, int32(x))
}
func (MemberStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{4}
}

type SocialCommunityOwnerStatus int32

const (
	SocialCommunityOwnerStatus_SOCIAL_COMMUNITY_OWNER_STATUS_NONE  SocialCommunityOwnerStatus = 0
	SocialCommunityOwnerStatus_SOCIAL_COMMUNITY_OWNER_STATUS_HAS   SocialCommunityOwnerStatus = 1
	SocialCommunityOwnerStatus_SOCIAL_COMMUNITY_OWNER_STATUS_AUDIT SocialCommunityOwnerStatus = 2
)

var SocialCommunityOwnerStatus_name = map[int32]string{
	0: "SOCIAL_COMMUNITY_OWNER_STATUS_NONE",
	1: "SOCIAL_COMMUNITY_OWNER_STATUS_HAS",
	2: "SOCIAL_COMMUNITY_OWNER_STATUS_AUDIT",
}
var SocialCommunityOwnerStatus_value = map[string]int32{
	"SOCIAL_COMMUNITY_OWNER_STATUS_NONE":  0,
	"SOCIAL_COMMUNITY_OWNER_STATUS_HAS":   1,
	"SOCIAL_COMMUNITY_OWNER_STATUS_AUDIT": 2,
}

func (x SocialCommunityOwnerStatus) String() string {
	return proto.EnumName(SocialCommunityOwnerStatus_name, int32(x))
}
func (SocialCommunityOwnerStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{5}
}

type BrandProfession int32

const (
	BrandProfession_BRAND_PROFESSION_NONE         BrandProfession = 0
	BrandProfession_BRAND_PROFESSION_PROFESSIONAL BrandProfession = 1
	BrandProfession_BRAND_PROFESSION_AMATEUR      BrandProfession = 2
)

var BrandProfession_name = map[int32]string{
	0: "BRAND_PROFESSION_NONE",
	1: "BRAND_PROFESSION_PROFESSIONAL",
	2: "BRAND_PROFESSION_AMATEUR",
}
var BrandProfession_value = map[string]int32{
	"BRAND_PROFESSION_NONE":         0,
	"BRAND_PROFESSION_PROFESSIONAL": 1,
	"BRAND_PROFESSION_AMATEUR":      2,
}

func (x BrandProfession) String() string {
	return proto.EnumName(BrandProfession_name, int32(x))
}
func (BrandProfession) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{6}
}

type SocialCommunityViewStatus int32

const (
	SocialCommunityViewStatus_SOCIAL_COMMUNITY_VIEW_STATUS_NONE   SocialCommunityViewStatus = 0
	SocialCommunityViewStatus_SOCIAL_COMMUNITY_VIEW_STATUS_AUDIT  SocialCommunityViewStatus = 1
	SocialCommunityViewStatus_SOCIAL_COMMUNITY_VIEW_STATUS_REJECT SocialCommunityViewStatus = 2
)

var SocialCommunityViewStatus_name = map[int32]string{
	0: "SOCIAL_COMMUNITY_VIEW_STATUS_NONE",
	1: "SOCIAL_COMMUNITY_VIEW_STATUS_AUDIT",
	2: "SOCIAL_COMMUNITY_VIEW_STATUS_REJECT",
}
var SocialCommunityViewStatus_value = map[string]int32{
	"SOCIAL_COMMUNITY_VIEW_STATUS_NONE":   0,
	"SOCIAL_COMMUNITY_VIEW_STATUS_AUDIT":  1,
	"SOCIAL_COMMUNITY_VIEW_STATUS_REJECT": 2,
}

func (x SocialCommunityViewStatus) String() string {
	return proto.EnumName(SocialCommunityViewStatus_name, int32(x))
}
func (SocialCommunityViewStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{7}
}

type SocialCommunityViewType int32

const (
	SocialCommunityViewType_SOCIAL_COMMUNITY_VIEW_TYPE_NONE     SocialCommunityViewType = 0
	SocialCommunityViewType_SOCIAL_COMMUNITY_VIEW_TYPE_AUDIT    SocialCommunityViewType = 1
	SocialCommunityViewType_SOCIAL_COMMUNITY_VIEW_TYPE_ANNOUNCE SocialCommunityViewType = 2
	SocialCommunityViewType_SOCIAL_COMMUNITY_VIEW_TYPE_ROOM     SocialCommunityViewType = 3
	SocialCommunityViewType_SOCIAL_COMMUNITY_VIEW_TYPE_GROUP    SocialCommunityViewType = 4
)

var SocialCommunityViewType_name = map[int32]string{
	0: "SOCIAL_COMMUNITY_VIEW_TYPE_NONE",
	1: "SOCIAL_COMMUNITY_VIEW_TYPE_AUDIT",
	2: "SOCIAL_COMMUNITY_VIEW_TYPE_ANNOUNCE",
	3: "SOCIAL_COMMUNITY_VIEW_TYPE_ROOM",
	4: "SOCIAL_COMMUNITY_VIEW_TYPE_GROUP",
}
var SocialCommunityViewType_value = map[string]int32{
	"SOCIAL_COMMUNITY_VIEW_TYPE_NONE":     0,
	"SOCIAL_COMMUNITY_VIEW_TYPE_AUDIT":    1,
	"SOCIAL_COMMUNITY_VIEW_TYPE_ANNOUNCE": 2,
	"SOCIAL_COMMUNITY_VIEW_TYPE_ROOM":     3,
	"SOCIAL_COMMUNITY_VIEW_TYPE_GROUP":    4,
}

func (x SocialCommunityViewType) String() string {
	return proto.EnumName(SocialCommunityViewType_name, int32(x))
}
func (SocialCommunityViewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{8}
}

type BrandProfessionalism int32

const (
	BrandProfessionalism_BRAND_PROFESSIONALISM_BRAND_NONE         BrandProfessionalism = 0
	BrandProfessionalism_BRAND_PROFESSIONALISM_BRAND_PROFESSIONAL BrandProfessionalism = 1
	BrandProfessionalism_BRAND_PROFESSIONALISM_BRAND_AMATEUR      BrandProfessionalism = 2
)

var BrandProfessionalism_name = map[int32]string{
	0: "BRAND_PROFESSIONALISM_BRAND_NONE",
	1: "BRAND_PROFESSIONALISM_BRAND_PROFESSIONAL",
	2: "BRAND_PROFESSIONALISM_BRAND_AMATEUR",
}
var BrandProfessionalism_value = map[string]int32{
	"BRAND_PROFESSIONALISM_BRAND_NONE":         0,
	"BRAND_PROFESSIONALISM_BRAND_PROFESSIONAL": 1,
	"BRAND_PROFESSIONALISM_BRAND_AMATEUR":      2,
}

func (x BrandProfessionalism) String() string {
	return proto.EnumName(BrandProfessionalism_name, int32(x))
}
func (BrandProfessionalism) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{9}
}

type ListAnnouncesType int32

const (
	ListAnnouncesType_LIST_ANNOUNCES_TYPE_ALL        ListAnnouncesType = 0
	ListAnnouncesType_LIST_ANNOUNCES_TYPE_MINE       ListAnnouncesType = 1
	ListAnnouncesType_LIST_ANNOUNCES_TYPE_INTERESTED ListAnnouncesType = 2
)

var ListAnnouncesType_name = map[int32]string{
	0: "LIST_ANNOUNCES_TYPE_ALL",
	1: "LIST_ANNOUNCES_TYPE_MINE",
	2: "LIST_ANNOUNCES_TYPE_INTERESTED",
}
var ListAnnouncesType_value = map[string]int32{
	"LIST_ANNOUNCES_TYPE_ALL":        0,
	"LIST_ANNOUNCES_TYPE_MINE":       1,
	"LIST_ANNOUNCES_TYPE_INTERESTED": 2,
}

func (x ListAnnouncesType) String() string {
	return proto.EnumName(ListAnnouncesType_name, int32(x))
}
func (ListAnnouncesType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{10}
}

type MuseAnnounceDestinationType int32

const (
	MuseAnnounceDestinationType_MUSE_ANNOUNCE_DESTINATION_TYPE_NONE             MuseAnnounceDestinationType = 0
	MuseAnnounceDestinationType_MUSE_ANNOUNCE_DESTINATION_TYPE_GROUP            MuseAnnounceDestinationType = 1
	MuseAnnounceDestinationType_MUSE_ANNOUNCE_DESTINATION_TYPE_CHAT_CHANNEL     MuseAnnounceDestinationType = 2
	MuseAnnounceDestinationType_MUSE_ANNOUNCE_DESTINATION_TYPE_SHOW_CHANNEL     MuseAnnounceDestinationType = 3
	MuseAnnounceDestinationType_MUSE_ANNOUNCE_DESTINATION_TYPE_PERSONAL_CHANNEL MuseAnnounceDestinationType = 4
)

var MuseAnnounceDestinationType_name = map[int32]string{
	0: "MUSE_ANNOUNCE_DESTINATION_TYPE_NONE",
	1: "MUSE_ANNOUNCE_DESTINATION_TYPE_GROUP",
	2: "MUSE_ANNOUNCE_DESTINATION_TYPE_CHAT_CHANNEL",
	3: "MUSE_ANNOUNCE_DESTINATION_TYPE_SHOW_CHANNEL",
	4: "MUSE_ANNOUNCE_DESTINATION_TYPE_PERSONAL_CHANNEL",
}
var MuseAnnounceDestinationType_value = map[string]int32{
	"MUSE_ANNOUNCE_DESTINATION_TYPE_NONE":             0,
	"MUSE_ANNOUNCE_DESTINATION_TYPE_GROUP":            1,
	"MUSE_ANNOUNCE_DESTINATION_TYPE_CHAT_CHANNEL":     2,
	"MUSE_ANNOUNCE_DESTINATION_TYPE_SHOW_CHANNEL":     3,
	"MUSE_ANNOUNCE_DESTINATION_TYPE_PERSONAL_CHANNEL": 4,
}

func (x MuseAnnounceDestinationType) String() string {
	return proto.EnumName(MuseAnnounceDestinationType_name, int32(x))
}
func (MuseAnnounceDestinationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{11}
}

type SetInterestType int32

const (
	SetInterestType_SET_INTEREST_TYPE_INTERESTED   SetInterestType = 0
	SetInterestType_SET_INTEREST_TYPE_UNINTERESTED SetInterestType = 1
)

var SetInterestType_name = map[int32]string{
	0: "SET_INTEREST_TYPE_INTERESTED",
	1: "SET_INTEREST_TYPE_UNINTERESTED",
}
var SetInterestType_value = map[string]int32{
	"SET_INTEREST_TYPE_INTERESTED":   0,
	"SET_INTEREST_TYPE_UNINTERESTED": 1,
}

func (x SetInterestType) String() string {
	return proto.EnumName(SetInterestType_name, int32(x))
}
func (SetInterestType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{12}
}

type SocialCommunitySystemMessageType int32

const (
	SocialCommunitySystemMessageType_SOCIAL_COMMUNITY_SYSTEM_MESSAGE_TYPE_NONE SocialCommunitySystemMessageType = 0
	SocialCommunitySystemMessageType_SOCIAL_COMMUNITY_SYSTEM_MESSAGE_TYPE_JOIN SocialCommunitySystemMessageType = 1
)

var SocialCommunitySystemMessageType_name = map[int32]string{
	0: "SOCIAL_COMMUNITY_SYSTEM_MESSAGE_TYPE_NONE",
	1: "SOCIAL_COMMUNITY_SYSTEM_MESSAGE_TYPE_JOIN",
}
var SocialCommunitySystemMessageType_value = map[string]int32{
	"SOCIAL_COMMUNITY_SYSTEM_MESSAGE_TYPE_NONE": 0,
	"SOCIAL_COMMUNITY_SYSTEM_MESSAGE_TYPE_JOIN": 1,
}

func (x SocialCommunitySystemMessageType) String() string {
	return proto.EnumName(SocialCommunitySystemMessageType_name, int32(x))
}
func (SocialCommunitySystemMessageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{13}
}

type AdditionSocialCommunityMode int32

const (
	AdditionSocialCommunityMode_ADDITION_SOCIAL_COMMUNITY_MODE_NONE        AdditionSocialCommunityMode = 0
	AdditionSocialCommunityMode_ADDITION_SOCIAL_COMMUNITY_MODE_APPLY_JOIN  AdditionSocialCommunityMode = 1
	AdditionSocialCommunityMode_ADDITION_SOCIAL_COMMUNITY_MODE_REJECT_JOIN AdditionSocialCommunityMode = 2
)

var AdditionSocialCommunityMode_name = map[int32]string{
	0: "ADDITION_SOCIAL_COMMUNITY_MODE_NONE",
	1: "ADDITION_SOCIAL_COMMUNITY_MODE_APPLY_JOIN",
	2: "ADDITION_SOCIAL_COMMUNITY_MODE_REJECT_JOIN",
}
var AdditionSocialCommunityMode_value = map[string]int32{
	"ADDITION_SOCIAL_COMMUNITY_MODE_NONE":        0,
	"ADDITION_SOCIAL_COMMUNITY_MODE_APPLY_JOIN":  1,
	"ADDITION_SOCIAL_COMMUNITY_MODE_REJECT_JOIN": 2,
}

func (x AdditionSocialCommunityMode) String() string {
	return proto.EnumName(AdditionSocialCommunityMode_name, int32(x))
}
func (AdditionSocialCommunityMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{14}
}

type JoinSocialCommunityMessageStatus int32

const (
	JoinSocialCommunityMessageStatus_JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_NONE   JoinSocialCommunityMessageStatus = 0
	JoinSocialCommunityMessageStatus_JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_REVIEW JoinSocialCommunityMessageStatus = 1
	JoinSocialCommunityMessageStatus_JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_PASS   JoinSocialCommunityMessageStatus = 2
	JoinSocialCommunityMessageStatus_JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_REJECT JoinSocialCommunityMessageStatus = 3
	JoinSocialCommunityMessageStatus_JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_EXPIRE JoinSocialCommunityMessageStatus = 4
)

var JoinSocialCommunityMessageStatus_name = map[int32]string{
	0: "JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_NONE",
	1: "JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_REVIEW",
	2: "JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_PASS",
	3: "JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_REJECT",
	4: "JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_EXPIRE",
}
var JoinSocialCommunityMessageStatus_value = map[string]int32{
	"JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_NONE":   0,
	"JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_REVIEW": 1,
	"JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_PASS":   2,
	"JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_REJECT": 3,
	"JOIN_SOCIAL_COMMUNITY_MESSAGE_STATUS_EXPIRE": 4,
}

func (x JoinSocialCommunityMessageStatus) String() string {
	return proto.EnumName(JoinSocialCommunityMessageStatus_name, int32(x))
}
func (JoinSocialCommunityMessageStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{15}
}

type TaskViewType int32

const (
	TaskViewType_TASK_VIEW_TYPE_NORMAL   TaskViewType = 0
	TaskViewType_TASK_VIEW_TYPE_CHECK_IN TaskViewType = 1
	TaskViewType_TASK_VIEW_TYPE_STEP     TaskViewType = 2
)

var TaskViewType_name = map[int32]string{
	0: "TASK_VIEW_TYPE_NORMAL",
	1: "TASK_VIEW_TYPE_CHECK_IN",
	2: "TASK_VIEW_TYPE_STEP",
}
var TaskViewType_value = map[string]int32{
	"TASK_VIEW_TYPE_NORMAL":   0,
	"TASK_VIEW_TYPE_CHECK_IN": 1,
	"TASK_VIEW_TYPE_STEP":     2,
}

func (x TaskViewType) String() string {
	return proto.EnumName(TaskViewType_name, int32(x))
}
func (TaskViewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{16}
}

type MuseSocialGroupMemberInfo_Role int32

const (
	MuseSocialGroupMemberInfo_ROLE_NONE   MuseSocialGroupMemberInfo_Role = 0
	MuseSocialGroupMemberInfo_ROLE_OWNER  MuseSocialGroupMemberInfo_Role = 1
	MuseSocialGroupMemberInfo_ROLE_ADMIN  MuseSocialGroupMemberInfo_Role = 2
	MuseSocialGroupMemberInfo_ROLE_COMMON MuseSocialGroupMemberInfo_Role = 3
)

var MuseSocialGroupMemberInfo_Role_name = map[int32]string{
	0: "ROLE_NONE",
	1: "ROLE_OWNER",
	2: "ROLE_ADMIN",
	3: "ROLE_COMMON",
}
var MuseSocialGroupMemberInfo_Role_value = map[string]int32{
	"ROLE_NONE":   0,
	"ROLE_OWNER":  1,
	"ROLE_ADMIN":  2,
	"ROLE_COMMON": 3,
}

func (x MuseSocialGroupMemberInfo_Role) String() string {
	return proto.EnumName(MuseSocialGroupMemberInfo_Role_name, int32(x))
}
func (MuseSocialGroupMemberInfo_Role) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{88, 0}
}

type ListMuseSocialCommunityNavBarsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ExtraNavId           string       `protobuf:"bytes,3,opt,name=extra_nav_id,json=extraNavId,proto3" json:"extra_nav_id,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,4,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListMuseSocialCommunityNavBarsRequest) Reset()         { *m = ListMuseSocialCommunityNavBarsRequest{} }
func (m *ListMuseSocialCommunityNavBarsRequest) String() string { return proto.CompactTextString(m) }
func (*ListMuseSocialCommunityNavBarsRequest) ProtoMessage()    {}
func (*ListMuseSocialCommunityNavBarsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{0}
}
func (m *ListMuseSocialCommunityNavBarsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialCommunityNavBarsRequest.Unmarshal(m, b)
}
func (m *ListMuseSocialCommunityNavBarsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialCommunityNavBarsRequest.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialCommunityNavBarsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialCommunityNavBarsRequest.Merge(dst, src)
}
func (m *ListMuseSocialCommunityNavBarsRequest) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialCommunityNavBarsRequest.Size(m)
}
func (m *ListMuseSocialCommunityNavBarsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialCommunityNavBarsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialCommunityNavBarsRequest proto.InternalMessageInfo

func (m *ListMuseSocialCommunityNavBarsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListMuseSocialCommunityNavBarsRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListMuseSocialCommunityNavBarsRequest) GetExtraNavId() string {
	if m != nil {
		return m.ExtraNavId
	}
	return ""
}

func (m *ListMuseSocialCommunityNavBarsRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type ListMuseSocialCommunityNavBarsResponse struct {
	BaseResp               *app.BaseResp                `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Bars                   []*MuseSocialCommunityNavBar `protobuf:"bytes,2,rep,name=bars,proto3" json:"bars,omitempty"`
	UnreadInteractMsgCount int64                        `protobuf:"varint,3,opt,name=unread_interact_msg_count,json=unreadInteractMsgCount,proto3" json:"unread_interact_msg_count,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                     `json:"-"`
	XXX_unrecognized       []byte                       `json:"-"`
	XXX_sizecache          int32                        `json:"-"`
}

func (m *ListMuseSocialCommunityNavBarsResponse) Reset() {
	*m = ListMuseSocialCommunityNavBarsResponse{}
}
func (m *ListMuseSocialCommunityNavBarsResponse) String() string { return proto.CompactTextString(m) }
func (*ListMuseSocialCommunityNavBarsResponse) ProtoMessage()    {}
func (*ListMuseSocialCommunityNavBarsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{1}
}
func (m *ListMuseSocialCommunityNavBarsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialCommunityNavBarsResponse.Unmarshal(m, b)
}
func (m *ListMuseSocialCommunityNavBarsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialCommunityNavBarsResponse.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialCommunityNavBarsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialCommunityNavBarsResponse.Merge(dst, src)
}
func (m *ListMuseSocialCommunityNavBarsResponse) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialCommunityNavBarsResponse.Size(m)
}
func (m *ListMuseSocialCommunityNavBarsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialCommunityNavBarsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialCommunityNavBarsResponse proto.InternalMessageInfo

func (m *ListMuseSocialCommunityNavBarsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListMuseSocialCommunityNavBarsResponse) GetBars() []*MuseSocialCommunityNavBar {
	if m != nil {
		return m.Bars
	}
	return nil
}

func (m *ListMuseSocialCommunityNavBarsResponse) GetUnreadInteractMsgCount() int64 {
	if m != nil {
		return m.UnreadInteractMsgCount
	}
	return 0
}

type ListMuseSocialCommunityNavSecondaryBarsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Id                   string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListMuseSocialCommunityNavSecondaryBarsRequest) Reset() {
	*m = ListMuseSocialCommunityNavSecondaryBarsRequest{}
}
func (m *ListMuseSocialCommunityNavSecondaryBarsRequest) String() string {
	return proto.CompactTextString(m)
}
func (*ListMuseSocialCommunityNavSecondaryBarsRequest) ProtoMessage() {}
func (*ListMuseSocialCommunityNavSecondaryBarsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{2}
}
func (m *ListMuseSocialCommunityNavSecondaryBarsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsRequest.Unmarshal(m, b)
}
func (m *ListMuseSocialCommunityNavSecondaryBarsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsRequest.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialCommunityNavSecondaryBarsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsRequest.Merge(dst, src)
}
func (m *ListMuseSocialCommunityNavSecondaryBarsRequest) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsRequest.Size(m)
}
func (m *ListMuseSocialCommunityNavSecondaryBarsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsRequest proto.InternalMessageInfo

func (m *ListMuseSocialCommunityNavSecondaryBarsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListMuseSocialCommunityNavSecondaryBarsRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type ListMuseSocialCommunityNavSecondaryBarsResponse struct {
	BaseResp             *app.BaseResp                         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SecondaryBars        []*MuseSocialCommunityNavSecondaryBar `protobuf:"bytes,2,rep,name=secondary_bars,json=secondaryBars,proto3" json:"secondary_bars,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *ListMuseSocialCommunityNavSecondaryBarsResponse) Reset() {
	*m = ListMuseSocialCommunityNavSecondaryBarsResponse{}
}
func (m *ListMuseSocialCommunityNavSecondaryBarsResponse) String() string {
	return proto.CompactTextString(m)
}
func (*ListMuseSocialCommunityNavSecondaryBarsResponse) ProtoMessage() {}
func (*ListMuseSocialCommunityNavSecondaryBarsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{3}
}
func (m *ListMuseSocialCommunityNavSecondaryBarsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsResponse.Unmarshal(m, b)
}
func (m *ListMuseSocialCommunityNavSecondaryBarsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsResponse.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialCommunityNavSecondaryBarsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsResponse.Merge(dst, src)
}
func (m *ListMuseSocialCommunityNavSecondaryBarsResponse) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsResponse.Size(m)
}
func (m *ListMuseSocialCommunityNavSecondaryBarsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialCommunityNavSecondaryBarsResponse proto.InternalMessageInfo

func (m *ListMuseSocialCommunityNavSecondaryBarsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListMuseSocialCommunityNavSecondaryBarsResponse) GetSecondaryBars() []*MuseSocialCommunityNavSecondaryBar {
	if m != nil {
		return m.SecondaryBars
	}
	return nil
}

type MuseSocialCommunityNavBar struct {
	SocialCommunityId      string                                `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	Name                   string                                `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Logo                   string                                `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`
	BgLogo                 string                                `protobuf:"bytes,4,opt,name=bg_logo,json=bgLogo,proto3" json:"bg_logo,omitempty"`
	Flag                   MuseSocialCommunityNavBarFlag         `protobuf:"varint,5,opt,name=flag,proto3,enum=ga.muse_social_community_logic.MuseSocialCommunityNavBarFlag" json:"flag,omitempty"`
	CategoryTypeSimpleDesc string                                `protobuf:"bytes,6,opt,name=category_type_simple_desc,json=categoryTypeSimpleDesc,proto3" json:"category_type_simple_desc,omitempty"`
	SecondaryBars          []*MuseSocialCommunityNavSecondaryBar `protobuf:"bytes,7,rep,name=secondary_bars,json=secondaryBars,proto3" json:"secondary_bars,omitempty"`
	BrandProfessionalism   uint32                                `protobuf:"varint,8,opt,name=brand_professionalism,json=brandProfessionalism,proto3" json:"brand_professionalism,omitempty"`
	Level                  uint32                                `protobuf:"varint,9,opt,name=level,proto3" json:"level,omitempty"`
	LevelLogo              string                                `protobuf:"bytes,10,opt,name=level_logo,json=levelLogo,proto3" json:"level_logo,omitempty"`
	UnreadMsgCount         int64                                 `protobuf:"varint,11,opt,name=unread_msg_count,json=unreadMsgCount,proto3" json:"unread_msg_count,omitempty"`
	ProfessionalismInfo    *Professionalism                      `protobuf:"bytes,12,opt,name=professionalism_info,json=professionalismInfo,proto3" json:"professionalism_info,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                              `json:"-"`
	XXX_unrecognized       []byte                                `json:"-"`
	XXX_sizecache          int32                                 `json:"-"`
}

func (m *MuseSocialCommunityNavBar) Reset()         { *m = MuseSocialCommunityNavBar{} }
func (m *MuseSocialCommunityNavBar) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityNavBar) ProtoMessage()    {}
func (*MuseSocialCommunityNavBar) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{4}
}
func (m *MuseSocialCommunityNavBar) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityNavBar.Unmarshal(m, b)
}
func (m *MuseSocialCommunityNavBar) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityNavBar.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityNavBar) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityNavBar.Merge(dst, src)
}
func (m *MuseSocialCommunityNavBar) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityNavBar.Size(m)
}
func (m *MuseSocialCommunityNavBar) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityNavBar.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityNavBar proto.InternalMessageInfo

func (m *MuseSocialCommunityNavBar) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *MuseSocialCommunityNavBar) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseSocialCommunityNavBar) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *MuseSocialCommunityNavBar) GetBgLogo() string {
	if m != nil {
		return m.BgLogo
	}
	return ""
}

func (m *MuseSocialCommunityNavBar) GetFlag() MuseSocialCommunityNavBarFlag {
	if m != nil {
		return m.Flag
	}
	return MuseSocialCommunityNavBarFlag_MUSE_SOCIAL_COMMUNITY_NAV_BAR_FLAG_UNSPECIFIED
}

func (m *MuseSocialCommunityNavBar) GetCategoryTypeSimpleDesc() string {
	if m != nil {
		return m.CategoryTypeSimpleDesc
	}
	return ""
}

func (m *MuseSocialCommunityNavBar) GetSecondaryBars() []*MuseSocialCommunityNavSecondaryBar {
	if m != nil {
		return m.SecondaryBars
	}
	return nil
}

func (m *MuseSocialCommunityNavBar) GetBrandProfessionalism() uint32 {
	if m != nil {
		return m.BrandProfessionalism
	}
	return 0
}

func (m *MuseSocialCommunityNavBar) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *MuseSocialCommunityNavBar) GetLevelLogo() string {
	if m != nil {
		return m.LevelLogo
	}
	return ""
}

func (m *MuseSocialCommunityNavBar) GetUnreadMsgCount() int64 {
	if m != nil {
		return m.UnreadMsgCount
	}
	return 0
}

func (m *MuseSocialCommunityNavBar) GetProfessionalismInfo() *Professionalism {
	if m != nil {
		return m.ProfessionalismInfo
	}
	return nil
}

type MuseSocialCommunityNavSecondaryBar struct {
	// Types that are valid to be assigned to Content:
	//	*MuseSocialCommunityNavSecondaryBar_Channel
	//	*MuseSocialCommunityNavSecondaryBar_Group
	//	*MuseSocialCommunityNavSecondaryBar_Announce
	//	*MuseSocialCommunityNavSecondaryBar_ContentStream
	Content              isMuseSocialCommunityNavSecondaryBar_Content `protobuf_oneof:"content"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *MuseSocialCommunityNavSecondaryBar) Reset()         { *m = MuseSocialCommunityNavSecondaryBar{} }
func (m *MuseSocialCommunityNavSecondaryBar) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityNavSecondaryBar) ProtoMessage()    {}
func (*MuseSocialCommunityNavSecondaryBar) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{5}
}
func (m *MuseSocialCommunityNavSecondaryBar) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityNavSecondaryBar.Unmarshal(m, b)
}
func (m *MuseSocialCommunityNavSecondaryBar) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityNavSecondaryBar.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityNavSecondaryBar) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityNavSecondaryBar.Merge(dst, src)
}
func (m *MuseSocialCommunityNavSecondaryBar) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityNavSecondaryBar.Size(m)
}
func (m *MuseSocialCommunityNavSecondaryBar) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityNavSecondaryBar.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityNavSecondaryBar proto.InternalMessageInfo

type isMuseSocialCommunityNavSecondaryBar_Content interface {
	isMuseSocialCommunityNavSecondaryBar_Content()
}

type MuseSocialCommunityNavSecondaryBar_Channel struct {
	Channel *MuseSocialCommunityChannel `protobuf:"bytes,1,opt,name=channel,proto3,oneof"`
}

type MuseSocialCommunityNavSecondaryBar_Group struct {
	Group *MuseSocialCommunityGroup `protobuf:"bytes,2,opt,name=group,proto3,oneof"`
}

type MuseSocialCommunityNavSecondaryBar_Announce struct {
	Announce *MuseSocialCommunityAnnounce `protobuf:"bytes,3,opt,name=announce,proto3,oneof"`
}

type MuseSocialCommunityNavSecondaryBar_ContentStream struct {
	ContentStream *MuseSocialCommunityContentStream `protobuf:"bytes,4,opt,name=content_stream,json=contentStream,proto3,oneof"`
}

func (*MuseSocialCommunityNavSecondaryBar_Channel) isMuseSocialCommunityNavSecondaryBar_Content() {}

func (*MuseSocialCommunityNavSecondaryBar_Group) isMuseSocialCommunityNavSecondaryBar_Content() {}

func (*MuseSocialCommunityNavSecondaryBar_Announce) isMuseSocialCommunityNavSecondaryBar_Content() {}

func (*MuseSocialCommunityNavSecondaryBar_ContentStream) isMuseSocialCommunityNavSecondaryBar_Content() {
}

func (m *MuseSocialCommunityNavSecondaryBar) GetContent() isMuseSocialCommunityNavSecondaryBar_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *MuseSocialCommunityNavSecondaryBar) GetChannel() *MuseSocialCommunityChannel {
	if x, ok := m.GetContent().(*MuseSocialCommunityNavSecondaryBar_Channel); ok {
		return x.Channel
	}
	return nil
}

func (m *MuseSocialCommunityNavSecondaryBar) GetGroup() *MuseSocialCommunityGroup {
	if x, ok := m.GetContent().(*MuseSocialCommunityNavSecondaryBar_Group); ok {
		return x.Group
	}
	return nil
}

func (m *MuseSocialCommunityNavSecondaryBar) GetAnnounce() *MuseSocialCommunityAnnounce {
	if x, ok := m.GetContent().(*MuseSocialCommunityNavSecondaryBar_Announce); ok {
		return x.Announce
	}
	return nil
}

func (m *MuseSocialCommunityNavSecondaryBar) GetContentStream() *MuseSocialCommunityContentStream {
	if x, ok := m.GetContent().(*MuseSocialCommunityNavSecondaryBar_ContentStream); ok {
		return x.ContentStream
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*MuseSocialCommunityNavSecondaryBar) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _MuseSocialCommunityNavSecondaryBar_OneofMarshaler, _MuseSocialCommunityNavSecondaryBar_OneofUnmarshaler, _MuseSocialCommunityNavSecondaryBar_OneofSizer, []interface{}{
		(*MuseSocialCommunityNavSecondaryBar_Channel)(nil),
		(*MuseSocialCommunityNavSecondaryBar_Group)(nil),
		(*MuseSocialCommunityNavSecondaryBar_Announce)(nil),
		(*MuseSocialCommunityNavSecondaryBar_ContentStream)(nil),
	}
}

func _MuseSocialCommunityNavSecondaryBar_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*MuseSocialCommunityNavSecondaryBar)
	// content
	switch x := m.Content.(type) {
	case *MuseSocialCommunityNavSecondaryBar_Channel:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Channel); err != nil {
			return err
		}
	case *MuseSocialCommunityNavSecondaryBar_Group:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Group); err != nil {
			return err
		}
	case *MuseSocialCommunityNavSecondaryBar_Announce:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Announce); err != nil {
			return err
		}
	case *MuseSocialCommunityNavSecondaryBar_ContentStream:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ContentStream); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("MuseSocialCommunityNavSecondaryBar.Content has unexpected type %T", x)
	}
	return nil
}

func _MuseSocialCommunityNavSecondaryBar_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*MuseSocialCommunityNavSecondaryBar)
	switch tag {
	case 1: // content.channel
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseSocialCommunityChannel)
		err := b.DecodeMessage(msg)
		m.Content = &MuseSocialCommunityNavSecondaryBar_Channel{msg}
		return true, err
	case 2: // content.group
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseSocialCommunityGroup)
		err := b.DecodeMessage(msg)
		m.Content = &MuseSocialCommunityNavSecondaryBar_Group{msg}
		return true, err
	case 3: // content.announce
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseSocialCommunityAnnounce)
		err := b.DecodeMessage(msg)
		m.Content = &MuseSocialCommunityNavSecondaryBar_Announce{msg}
		return true, err
	case 4: // content.content_stream
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseSocialCommunityContentStream)
		err := b.DecodeMessage(msg)
		m.Content = &MuseSocialCommunityNavSecondaryBar_ContentStream{msg}
		return true, err
	default:
		return false, nil
	}
}

func _MuseSocialCommunityNavSecondaryBar_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*MuseSocialCommunityNavSecondaryBar)
	// content
	switch x := m.Content.(type) {
	case *MuseSocialCommunityNavSecondaryBar_Channel:
		s := proto.Size(x.Channel)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseSocialCommunityNavSecondaryBar_Group:
		s := proto.Size(x.Group)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseSocialCommunityNavSecondaryBar_Announce:
		s := proto.Size(x.Announce)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseSocialCommunityNavSecondaryBar_ContentStream:
		s := proto.Size(x.ContentStream)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type MuseSocialCommunityContentStream struct {
	ContentStreamId      string   `protobuf:"bytes,1,opt,name=content_stream_id,json=contentStreamId,proto3" json:"content_stream_id,omitempty"`
	StreamType           uint32   `protobuf:"varint,2,opt,name=stream_type,json=streamType,proto3" json:"stream_type,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Logo                 string   `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`
	UnreadMsgCount       int64    `protobuf:"varint,5,opt,name=unread_msg_count,json=unreadMsgCount,proto3" json:"unread_msg_count,omitempty"`
	IsCategoryCircle     bool     `protobuf:"varint,6,opt,name=is_category_circle,json=isCategoryCircle,proto3" json:"is_category_circle,omitempty"`
	UserPermission       uint32   `protobuf:"varint,7,opt,name=user_permission,json=userPermission,proto3" json:"user_permission,omitempty"`
	SceneId              string   `protobuf:"bytes,8,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSocialCommunityContentStream) Reset()         { *m = MuseSocialCommunityContentStream{} }
func (m *MuseSocialCommunityContentStream) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityContentStream) ProtoMessage()    {}
func (*MuseSocialCommunityContentStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{6}
}
func (m *MuseSocialCommunityContentStream) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityContentStream.Unmarshal(m, b)
}
func (m *MuseSocialCommunityContentStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityContentStream.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityContentStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityContentStream.Merge(dst, src)
}
func (m *MuseSocialCommunityContentStream) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityContentStream.Size(m)
}
func (m *MuseSocialCommunityContentStream) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityContentStream.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityContentStream proto.InternalMessageInfo

func (m *MuseSocialCommunityContentStream) GetContentStreamId() string {
	if m != nil {
		return m.ContentStreamId
	}
	return ""
}

func (m *MuseSocialCommunityContentStream) GetStreamType() uint32 {
	if m != nil {
		return m.StreamType
	}
	return 0
}

func (m *MuseSocialCommunityContentStream) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseSocialCommunityContentStream) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *MuseSocialCommunityContentStream) GetUnreadMsgCount() int64 {
	if m != nil {
		return m.UnreadMsgCount
	}
	return 0
}

func (m *MuseSocialCommunityContentStream) GetIsCategoryCircle() bool {
	if m != nil {
		return m.IsCategoryCircle
	}
	return false
}

func (m *MuseSocialCommunityContentStream) GetUserPermission() uint32 {
	if m != nil {
		return m.UserPermission
	}
	return 0
}

func (m *MuseSocialCommunityContentStream) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

type MuseSocialCommunityAnnounce struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Logo                 string   `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`
	Lottie               string   `protobuf:"bytes,4,opt,name=lottie,proto3" json:"lottie,omitempty"`
	LottieMd5            string   `protobuf:"bytes,5,opt,name=lottie_md5,json=lottieMd5,proto3" json:"lottie_md5,omitempty"`
	BgColor              string   `protobuf:"bytes,6,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	UnreadMsgCount       int64    `protobuf:"varint,7,opt,name=unread_msg_count,json=unreadMsgCount,proto3" json:"unread_msg_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSocialCommunityAnnounce) Reset()         { *m = MuseSocialCommunityAnnounce{} }
func (m *MuseSocialCommunityAnnounce) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityAnnounce) ProtoMessage()    {}
func (*MuseSocialCommunityAnnounce) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{7}
}
func (m *MuseSocialCommunityAnnounce) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityAnnounce.Unmarshal(m, b)
}
func (m *MuseSocialCommunityAnnounce) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityAnnounce.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityAnnounce) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityAnnounce.Merge(dst, src)
}
func (m *MuseSocialCommunityAnnounce) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityAnnounce.Size(m)
}
func (m *MuseSocialCommunityAnnounce) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityAnnounce.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityAnnounce proto.InternalMessageInfo

func (m *MuseSocialCommunityAnnounce) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseSocialCommunityAnnounce) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *MuseSocialCommunityAnnounce) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *MuseSocialCommunityAnnounce) GetLottie() string {
	if m != nil {
		return m.Lottie
	}
	return ""
}

func (m *MuseSocialCommunityAnnounce) GetLottieMd5() string {
	if m != nil {
		return m.LottieMd5
	}
	return ""
}

func (m *MuseSocialCommunityAnnounce) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

func (m *MuseSocialCommunityAnnounce) GetUnreadMsgCount() int64 {
	if m != nil {
		return m.UnreadMsgCount
	}
	return 0
}

type MuseSocialCommunityChannel struct {
	ChannelId            uint32           `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Name                 string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string           `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Logo                 string           `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`
	Lottie               string           `protobuf:"bytes,5,opt,name=lottie,proto3" json:"lottie,omitempty"`
	LottieMd5            string           `protobuf:"bytes,6,opt,name=lottie_md5,json=lottieMd5,proto3" json:"lottie_md5,omitempty"`
	ChannelType          BrandChannelType `protobuf:"varint,7,opt,name=channel_type,json=channelType,proto3,enum=ga.muse_social_community_logic.BrandChannelType" json:"channel_type,omitempty"`
	BgColor              string           `protobuf:"bytes,8,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MuseSocialCommunityChannel) Reset()         { *m = MuseSocialCommunityChannel{} }
func (m *MuseSocialCommunityChannel) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityChannel) ProtoMessage()    {}
func (*MuseSocialCommunityChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{8}
}
func (m *MuseSocialCommunityChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityChannel.Unmarshal(m, b)
}
func (m *MuseSocialCommunityChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityChannel.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityChannel.Merge(dst, src)
}
func (m *MuseSocialCommunityChannel) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityChannel.Size(m)
}
func (m *MuseSocialCommunityChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityChannel.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityChannel proto.InternalMessageInfo

func (m *MuseSocialCommunityChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseSocialCommunityChannel) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseSocialCommunityChannel) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *MuseSocialCommunityChannel) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *MuseSocialCommunityChannel) GetLottie() string {
	if m != nil {
		return m.Lottie
	}
	return ""
}

func (m *MuseSocialCommunityChannel) GetLottieMd5() string {
	if m != nil {
		return m.LottieMd5
	}
	return ""
}

func (m *MuseSocialCommunityChannel) GetChannelType() BrandChannelType {
	if m != nil {
		return m.ChannelType
	}
	return BrandChannelType_BRAND_CHANNEL_TYPE_UNSPECIFIED
}

func (m *MuseSocialCommunityChannel) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

type MuseSocialCommunityGroup struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Logo                 string   `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`
	Lottie               string   `protobuf:"bytes,5,opt,name=lottie,proto3" json:"lottie,omitempty"`
	LottieMd5            string   `protobuf:"bytes,6,opt,name=lottie_md5,json=lottieMd5,proto3" json:"lottie_md5,omitempty"`
	BgColor              string   `protobuf:"bytes,7,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSocialCommunityGroup) Reset()         { *m = MuseSocialCommunityGroup{} }
func (m *MuseSocialCommunityGroup) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityGroup) ProtoMessage()    {}
func (*MuseSocialCommunityGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{9}
}
func (m *MuseSocialCommunityGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityGroup.Unmarshal(m, b)
}
func (m *MuseSocialCommunityGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityGroup.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityGroup.Merge(dst, src)
}
func (m *MuseSocialCommunityGroup) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityGroup.Size(m)
}
func (m *MuseSocialCommunityGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityGroup.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityGroup proto.InternalMessageInfo

func (m *MuseSocialCommunityGroup) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialCommunityGroup) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseSocialCommunityGroup) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *MuseSocialCommunityGroup) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *MuseSocialCommunityGroup) GetLottie() string {
	if m != nil {
		return m.Lottie
	}
	return ""
}

func (m *MuseSocialCommunityGroup) GetLottieMd5() string {
	if m != nil {
		return m.LottieMd5
	}
	return ""
}

func (m *MuseSocialCommunityGroup) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

type MuseSocialCommunityNavHome struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSocialCommunityNavHome) Reset()         { *m = MuseSocialCommunityNavHome{} }
func (m *MuseSocialCommunityNavHome) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityNavHome) ProtoMessage()    {}
func (*MuseSocialCommunityNavHome) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{10}
}
func (m *MuseSocialCommunityNavHome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityNavHome.Unmarshal(m, b)
}
func (m *MuseSocialCommunityNavHome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityNavHome.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityNavHome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityNavHome.Merge(dst, src)
}
func (m *MuseSocialCommunityNavHome) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityNavHome.Size(m)
}
func (m *MuseSocialCommunityNavHome) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityNavHome.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityNavHome proto.InternalMessageInfo

func (m *MuseSocialCommunityNavHome) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type MuseSocialCommunityNavChatChannel struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSocialCommunityNavChatChannel) Reset()         { *m = MuseSocialCommunityNavChatChannel{} }
func (m *MuseSocialCommunityNavChatChannel) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityNavChatChannel) ProtoMessage()    {}
func (*MuseSocialCommunityNavChatChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{11}
}
func (m *MuseSocialCommunityNavChatChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityNavChatChannel.Unmarshal(m, b)
}
func (m *MuseSocialCommunityNavChatChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityNavChatChannel.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityNavChatChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityNavChatChannel.Merge(dst, src)
}
func (m *MuseSocialCommunityNavChatChannel) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityNavChatChannel.Size(m)
}
func (m *MuseSocialCommunityNavChatChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityNavChatChannel.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityNavChatChannel proto.InternalMessageInfo

func (m *MuseSocialCommunityNavChatChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseSocialCommunityNavChatChannel) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseSocialCommunityNavChatChannel) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type MuseSocialCommunityNavShowChannel struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsShowing            bool     `protobuf:"varint,3,opt,name=is_showing,json=isShowing,proto3" json:"is_showing,omitempty"`
	Desc                 string   `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSocialCommunityNavShowChannel) Reset()         { *m = MuseSocialCommunityNavShowChannel{} }
func (m *MuseSocialCommunityNavShowChannel) String() string { return proto.CompactTextString(m) }
func (*MuseSocialCommunityNavShowChannel) ProtoMessage()    {}
func (*MuseSocialCommunityNavShowChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{12}
}
func (m *MuseSocialCommunityNavShowChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialCommunityNavShowChannel.Unmarshal(m, b)
}
func (m *MuseSocialCommunityNavShowChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialCommunityNavShowChannel.Marshal(b, m, deterministic)
}
func (dst *MuseSocialCommunityNavShowChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialCommunityNavShowChannel.Merge(dst, src)
}
func (m *MuseSocialCommunityNavShowChannel) XXX_Size() int {
	return xxx_messageInfo_MuseSocialCommunityNavShowChannel.Size(m)
}
func (m *MuseSocialCommunityNavShowChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialCommunityNavShowChannel.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialCommunityNavShowChannel proto.InternalMessageInfo

func (m *MuseSocialCommunityNavShowChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseSocialCommunityNavShowChannel) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseSocialCommunityNavShowChannel) GetIsShowing() bool {
	if m != nil {
		return m.IsShowing
	}
	return false
}

func (m *MuseSocialCommunityNavShowChannel) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type BrandMember struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleText             string            `protobuf:"bytes,2,opt,name=role_text,json=roleText,proto3" json:"role_text,omitempty"`
	Role                 BrandMemberRoleV2 `protobuf:"varint,3,opt,name=role,proto3,enum=ga.muse_social_community_logic.BrandMemberRoleV2" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BrandMember) Reset()         { *m = BrandMember{} }
func (m *BrandMember) String() string { return proto.CompactTextString(m) }
func (*BrandMember) ProtoMessage()    {}
func (*BrandMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{13}
}
func (m *BrandMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandMember.Unmarshal(m, b)
}
func (m *BrandMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandMember.Marshal(b, m, deterministic)
}
func (dst *BrandMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandMember.Merge(dst, src)
}
func (m *BrandMember) XXX_Size() int {
	return xxx_messageInfo_BrandMember.Size(m)
}
func (m *BrandMember) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandMember.DiscardUnknown(m)
}

var xxx_messageInfo_BrandMember proto.InternalMessageInfo

func (m *BrandMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BrandMember) GetRoleText() string {
	if m != nil {
		return m.RoleText
	}
	return ""
}

func (m *BrandMember) GetRole() BrandMemberRoleV2 {
	if m != nil {
		return m.Role
	}
	return BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_UNSPECIFIED
}

// 获取用户社群角色
type BatGetMuseSocialCommunityUsersRoleRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32     `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,4,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatGetMuseSocialCommunityUsersRoleRequest) Reset() {
	*m = BatGetMuseSocialCommunityUsersRoleRequest{}
}
func (m *BatGetMuseSocialCommunityUsersRoleRequest) String() string {
	return proto.CompactTextString(m)
}
func (*BatGetMuseSocialCommunityUsersRoleRequest) ProtoMessage() {}
func (*BatGetMuseSocialCommunityUsersRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{14}
}
func (m *BatGetMuseSocialCommunityUsersRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleRequest.Unmarshal(m, b)
}
func (m *BatGetMuseSocialCommunityUsersRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetMuseSocialCommunityUsersRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleRequest.Merge(dst, src)
}
func (m *BatGetMuseSocialCommunityUsersRoleRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleRequest.Size(m)
}
func (m *BatGetMuseSocialCommunityUsersRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleRequest proto.InternalMessageInfo

func (m *BatGetMuseSocialCommunityUsersRoleRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatGetMuseSocialCommunityUsersRoleRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatGetMuseSocialCommunityUsersRoleRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatGetMuseSocialCommunityUsersRoleRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type BatGetMuseSocialCommunityUsersRoleResponse struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UidRoleMap           map[uint32]*BrandMember `protobuf:"bytes,2,rep,name=uid_role_map,json=uidRoleMap,proto3" json:"uid_role_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CategoryTypeName     string                  `protobuf:"bytes,3,opt,name=category_type_name,json=categoryTypeName,proto3" json:"category_type_name,omitempty"`
	SocialCommunityId    string                  `protobuf:"bytes,4,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatGetMuseSocialCommunityUsersRoleResponse) Reset() {
	*m = BatGetMuseSocialCommunityUsersRoleResponse{}
}
func (m *BatGetMuseSocialCommunityUsersRoleResponse) String() string {
	return proto.CompactTextString(m)
}
func (*BatGetMuseSocialCommunityUsersRoleResponse) ProtoMessage() {}
func (*BatGetMuseSocialCommunityUsersRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{15}
}
func (m *BatGetMuseSocialCommunityUsersRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleResponse.Unmarshal(m, b)
}
func (m *BatGetMuseSocialCommunityUsersRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetMuseSocialCommunityUsersRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleResponse.Merge(dst, src)
}
func (m *BatGetMuseSocialCommunityUsersRoleResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleResponse.Size(m)
}
func (m *BatGetMuseSocialCommunityUsersRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetMuseSocialCommunityUsersRoleResponse proto.InternalMessageInfo

func (m *BatGetMuseSocialCommunityUsersRoleResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatGetMuseSocialCommunityUsersRoleResponse) GetUidRoleMap() map[uint32]*BrandMember {
	if m != nil {
		return m.UidRoleMap
	}
	return nil
}

func (m *BatGetMuseSocialCommunityUsersRoleResponse) GetCategoryTypeName() string {
	if m != nil {
		return m.CategoryTypeName
	}
	return ""
}

func (m *BatGetMuseSocialCommunityUsersRoleResponse) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type GetSocialCommunityDetailRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,3,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityDetailRequest) Reset()         { *m = GetSocialCommunityDetailRequest{} }
func (m *GetSocialCommunityDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityDetailRequest) ProtoMessage()    {}
func (*GetSocialCommunityDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{16}
}
func (m *GetSocialCommunityDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityDetailRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityDetailRequest.Merge(dst, src)
}
func (m *GetSocialCommunityDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityDetailRequest.Size(m)
}
func (m *GetSocialCommunityDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityDetailRequest proto.InternalMessageInfo

func (m *GetSocialCommunityDetailRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialCommunityDetailRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetSocialCommunityDetailRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type GetSocialCommunityDetailResponse struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SocialCommunity      *SocialCommunityDetail `protobuf:"bytes,2,opt,name=social_community,json=socialCommunity,proto3" json:"social_community,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetSocialCommunityDetailResponse) Reset()         { *m = GetSocialCommunityDetailResponse{} }
func (m *GetSocialCommunityDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityDetailResponse) ProtoMessage()    {}
func (*GetSocialCommunityDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{17}
}
func (m *GetSocialCommunityDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityDetailResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityDetailResponse.Merge(dst, src)
}
func (m *GetSocialCommunityDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityDetailResponse.Size(m)
}
func (m *GetSocialCommunityDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityDetailResponse proto.InternalMessageInfo

func (m *GetSocialCommunityDetailResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityDetailResponse) GetSocialCommunity() *SocialCommunityDetail {
	if m != nil {
		return m.SocialCommunity
	}
	return nil
}

type SocialCommunityDetail struct {
	SocialCommunityId      string           `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SocialCommunityLogo    string           `protobuf:"bytes,2,opt,name=social_community_logo,json=socialCommunityLogo,proto3" json:"social_community_logo,omitempty"`
	SocialCommunityName    string           `protobuf:"bytes,3,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	CategoryName           string           `protobuf:"bytes,4,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	Captain                *SimpleUserInfo  `protobuf:"bytes,5,opt,name=captain,proto3" json:"captain,omitempty"`
	SocialCommunityIntro   string           `protobuf:"bytes,6,opt,name=social_community_intro,json=socialCommunityIntro,proto3" json:"social_community_intro,omitempty"`
	MemberCount            uint32           `protobuf:"varint,7,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	IntegralCount          int32            `protobuf:"varint,8,opt,name=integral_count,json=integralCount,proto3" json:"integral_count,omitempty"`
	SignCount              int32            `protobuf:"varint,9,opt,name=sign_count,json=signCount,proto3" json:"sign_count,omitempty"`
	CategoryTypeSimpleDesc string           `protobuf:"bytes,10,opt,name=category_type_simple_desc,json=categoryTypeSimpleDesc,proto3" json:"category_type_simple_desc,omitempty"`
	BrandProfessionalism   uint32           `protobuf:"varint,11,opt,name=brand_professionalism,json=brandProfessionalism,proto3" json:"brand_professionalism,omitempty"`
	Vision                 string           `protobuf:"bytes,12,opt,name=vision,proto3" json:"vision,omitempty"`
	Level                  uint32           `protobuf:"varint,13,opt,name=level,proto3" json:"level,omitempty"`
	LevelLogo              string           `protobuf:"bytes,14,opt,name=level_logo,json=levelLogo,proto3" json:"level_logo,omitempty"`
	CategoryId             string           `protobuf:"bytes,15,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	ProfessionalismInfo    *Professionalism `protobuf:"bytes,16,opt,name=professionalism_info,json=professionalismInfo,proto3" json:"professionalism_info,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}         `json:"-"`
	XXX_unrecognized       []byte           `json:"-"`
	XXX_sizecache          int32            `json:"-"`
}

func (m *SocialCommunityDetail) Reset()         { *m = SocialCommunityDetail{} }
func (m *SocialCommunityDetail) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityDetail) ProtoMessage()    {}
func (*SocialCommunityDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{18}
}
func (m *SocialCommunityDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityDetail.Unmarshal(m, b)
}
func (m *SocialCommunityDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityDetail.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityDetail.Merge(dst, src)
}
func (m *SocialCommunityDetail) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityDetail.Size(m)
}
func (m *SocialCommunityDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityDetail.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityDetail proto.InternalMessageInfo

func (m *SocialCommunityDetail) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *SocialCommunityDetail) GetSocialCommunityLogo() string {
	if m != nil {
		return m.SocialCommunityLogo
	}
	return ""
}

func (m *SocialCommunityDetail) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *SocialCommunityDetail) GetCategoryName() string {
	if m != nil {
		return m.CategoryName
	}
	return ""
}

func (m *SocialCommunityDetail) GetCaptain() *SimpleUserInfo {
	if m != nil {
		return m.Captain
	}
	return nil
}

func (m *SocialCommunityDetail) GetSocialCommunityIntro() string {
	if m != nil {
		return m.SocialCommunityIntro
	}
	return ""
}

func (m *SocialCommunityDetail) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *SocialCommunityDetail) GetIntegralCount() int32 {
	if m != nil {
		return m.IntegralCount
	}
	return 0
}

func (m *SocialCommunityDetail) GetSignCount() int32 {
	if m != nil {
		return m.SignCount
	}
	return 0
}

func (m *SocialCommunityDetail) GetCategoryTypeSimpleDesc() string {
	if m != nil {
		return m.CategoryTypeSimpleDesc
	}
	return ""
}

func (m *SocialCommunityDetail) GetBrandProfessionalism() uint32 {
	if m != nil {
		return m.BrandProfessionalism
	}
	return 0
}

func (m *SocialCommunityDetail) GetVision() string {
	if m != nil {
		return m.Vision
	}
	return ""
}

func (m *SocialCommunityDetail) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SocialCommunityDetail) GetLevelLogo() string {
	if m != nil {
		return m.LevelLogo
	}
	return ""
}

func (m *SocialCommunityDetail) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *SocialCommunityDetail) GetProfessionalismInfo() *Professionalism {
	if m != nil {
		return m.ProfessionalismInfo
	}
	return nil
}

type JoinSocialCommunityFansRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	PushMsgChannelId     uint32       `protobuf:"varint,3,opt,name=push_msg_channel_id,json=pushMsgChannelId,proto3" json:"push_msg_channel_id,omitempty"`
	GroupId              uint32       `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *JoinSocialCommunityFansRequest) Reset()         { *m = JoinSocialCommunityFansRequest{} }
func (m *JoinSocialCommunityFansRequest) String() string { return proto.CompactTextString(m) }
func (*JoinSocialCommunityFansRequest) ProtoMessage()    {}
func (*JoinSocialCommunityFansRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{19}
}
func (m *JoinSocialCommunityFansRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinSocialCommunityFansRequest.Unmarshal(m, b)
}
func (m *JoinSocialCommunityFansRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinSocialCommunityFansRequest.Marshal(b, m, deterministic)
}
func (dst *JoinSocialCommunityFansRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinSocialCommunityFansRequest.Merge(dst, src)
}
func (m *JoinSocialCommunityFansRequest) XXX_Size() int {
	return xxx_messageInfo_JoinSocialCommunityFansRequest.Size(m)
}
func (m *JoinSocialCommunityFansRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinSocialCommunityFansRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JoinSocialCommunityFansRequest proto.InternalMessageInfo

func (m *JoinSocialCommunityFansRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinSocialCommunityFansRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *JoinSocialCommunityFansRequest) GetPushMsgChannelId() uint32 {
	if m != nil {
		return m.PushMsgChannelId
	}
	return 0
}

func (m *JoinSocialCommunityFansRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type JoinSocialCommunityFansResponse struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	AddedGroups          []*SocialSimpleGroup `protobuf:"bytes,2,rep,name=added_groups,json=addedGroups,proto3" json:"added_groups,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *JoinSocialCommunityFansResponse) Reset()         { *m = JoinSocialCommunityFansResponse{} }
func (m *JoinSocialCommunityFansResponse) String() string { return proto.CompactTextString(m) }
func (*JoinSocialCommunityFansResponse) ProtoMessage()    {}
func (*JoinSocialCommunityFansResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{20}
}
func (m *JoinSocialCommunityFansResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinSocialCommunityFansResponse.Unmarshal(m, b)
}
func (m *JoinSocialCommunityFansResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinSocialCommunityFansResponse.Marshal(b, m, deterministic)
}
func (dst *JoinSocialCommunityFansResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinSocialCommunityFansResponse.Merge(dst, src)
}
func (m *JoinSocialCommunityFansResponse) XXX_Size() int {
	return xxx_messageInfo_JoinSocialCommunityFansResponse.Size(m)
}
func (m *JoinSocialCommunityFansResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinSocialCommunityFansResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JoinSocialCommunityFansResponse proto.InternalMessageInfo

func (m *JoinSocialCommunityFansResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *JoinSocialCommunityFansResponse) GetAddedGroups() []*SocialSimpleGroup {
	if m != nil {
		return m.AddedGroups
	}
	return nil
}

// 用户成功加入粉丝团的房间推送
type SocialCommunityChannelPush struct {
	UserInfo             *SimpleUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	Text                 string          `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	SocialCommunityId    string          `protobuf:"bytes,3,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SocialCommunityName  string          `protobuf:"bytes,4,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	CategoryTypeName     string          `protobuf:"bytes,5,opt,name=category_type_name,json=categoryTypeName,proto3" json:"category_type_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SocialCommunityChannelPush) Reset()         { *m = SocialCommunityChannelPush{} }
func (m *SocialCommunityChannelPush) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityChannelPush) ProtoMessage()    {}
func (*SocialCommunityChannelPush) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{21}
}
func (m *SocialCommunityChannelPush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityChannelPush.Unmarshal(m, b)
}
func (m *SocialCommunityChannelPush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityChannelPush.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityChannelPush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityChannelPush.Merge(dst, src)
}
func (m *SocialCommunityChannelPush) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityChannelPush.Size(m)
}
func (m *SocialCommunityChannelPush) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityChannelPush.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityChannelPush proto.InternalMessageInfo

func (m *SocialCommunityChannelPush) GetUserInfo() *SimpleUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *SocialCommunityChannelPush) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SocialCommunityChannelPush) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *SocialCommunityChannelPush) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *SocialCommunityChannelPush) GetCategoryTypeName() string {
	if m != nil {
		return m.CategoryTypeName
	}
	return ""
}

type SimpleUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SimpleUserInfo) Reset()         { *m = SimpleUserInfo{} }
func (m *SimpleUserInfo) String() string { return proto.CompactTextString(m) }
func (*SimpleUserInfo) ProtoMessage()    {}
func (*SimpleUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{22}
}
func (m *SimpleUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleUserInfo.Unmarshal(m, b)
}
func (m *SimpleUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleUserInfo.Marshal(b, m, deterministic)
}
func (dst *SimpleUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleUserInfo.Merge(dst, src)
}
func (m *SimpleUserInfo) XXX_Size() int {
	return xxx_messageInfo_SimpleUserInfo.Size(m)
}
func (m *SimpleUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleUserInfo proto.InternalMessageInfo

func (m *SimpleUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SimpleUserInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *SimpleUserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type GetMySocialCommunityRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMySocialCommunityRequest) Reset()         { *m = GetMySocialCommunityRequest{} }
func (m *GetMySocialCommunityRequest) String() string { return proto.CompactTextString(m) }
func (*GetMySocialCommunityRequest) ProtoMessage()    {}
func (*GetMySocialCommunityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{23}
}
func (m *GetMySocialCommunityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMySocialCommunityRequest.Unmarshal(m, b)
}
func (m *GetMySocialCommunityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMySocialCommunityRequest.Marshal(b, m, deterministic)
}
func (dst *GetMySocialCommunityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMySocialCommunityRequest.Merge(dst, src)
}
func (m *GetMySocialCommunityRequest) XXX_Size() int {
	return xxx_messageInfo_GetMySocialCommunityRequest.Size(m)
}
func (m *GetMySocialCommunityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMySocialCommunityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMySocialCommunityRequest proto.InternalMessageInfo

func (m *GetMySocialCommunityRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetMySocialCommunityResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SocialCommunityId    string        `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SocialCommunityName  string        `protobuf:"bytes,3,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	SocialCommunityLogo  string        `protobuf:"bytes,4,opt,name=social_community_logo,json=socialCommunityLogo,proto3" json:"social_community_logo,omitempty"`
	ChannelId            uint32        `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MemberCnt            uint32        `protobuf:"varint,6,opt,name=member_cnt,json=memberCnt,proto3" json:"member_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetMySocialCommunityResponse) Reset()         { *m = GetMySocialCommunityResponse{} }
func (m *GetMySocialCommunityResponse) String() string { return proto.CompactTextString(m) }
func (*GetMySocialCommunityResponse) ProtoMessage()    {}
func (*GetMySocialCommunityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{24}
}
func (m *GetMySocialCommunityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMySocialCommunityResponse.Unmarshal(m, b)
}
func (m *GetMySocialCommunityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMySocialCommunityResponse.Marshal(b, m, deterministic)
}
func (dst *GetMySocialCommunityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMySocialCommunityResponse.Merge(dst, src)
}
func (m *GetMySocialCommunityResponse) XXX_Size() int {
	return xxx_messageInfo_GetMySocialCommunityResponse.Size(m)
}
func (m *GetMySocialCommunityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMySocialCommunityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMySocialCommunityResponse proto.InternalMessageInfo

func (m *GetMySocialCommunityResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMySocialCommunityResponse) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *GetMySocialCommunityResponse) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *GetMySocialCommunityResponse) GetSocialCommunityLogo() string {
	if m != nil {
		return m.SocialCommunityLogo
	}
	return ""
}

func (m *GetMySocialCommunityResponse) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMySocialCommunityResponse) GetMemberCnt() uint32 {
	if m != nil {
		return m.MemberCnt
	}
	return 0
}

type SendWelcomePushRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ToUid                uint32       `protobuf:"varint,2,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,3,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendWelcomePushRequest) Reset()         { *m = SendWelcomePushRequest{} }
func (m *SendWelcomePushRequest) String() string { return proto.CompactTextString(m) }
func (*SendWelcomePushRequest) ProtoMessage()    {}
func (*SendWelcomePushRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{25}
}
func (m *SendWelcomePushRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWelcomePushRequest.Unmarshal(m, b)
}
func (m *SendWelcomePushRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWelcomePushRequest.Marshal(b, m, deterministic)
}
func (dst *SendWelcomePushRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWelcomePushRequest.Merge(dst, src)
}
func (m *SendWelcomePushRequest) XXX_Size() int {
	return xxx_messageInfo_SendWelcomePushRequest.Size(m)
}
func (m *SendWelcomePushRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWelcomePushRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SendWelcomePushRequest proto.InternalMessageInfo

func (m *SendWelcomePushRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendWelcomePushRequest) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *SendWelcomePushRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type SendWelcomePushResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendWelcomePushResponse) Reset()         { *m = SendWelcomePushResponse{} }
func (m *SendWelcomePushResponse) String() string { return proto.CompactTextString(m) }
func (*SendWelcomePushResponse) ProtoMessage()    {}
func (*SendWelcomePushResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{26}
}
func (m *SendWelcomePushResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendWelcomePushResponse.Unmarshal(m, b)
}
func (m *SendWelcomePushResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendWelcomePushResponse.Marshal(b, m, deterministic)
}
func (dst *SendWelcomePushResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendWelcomePushResponse.Merge(dst, src)
}
func (m *SendWelcomePushResponse) XXX_Size() int {
	return xxx_messageInfo_SendWelcomePushResponse.Size(m)
}
func (m *SendWelcomePushResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SendWelcomePushResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SendWelcomePushResponse proto.InternalMessageInfo

func (m *SendWelcomePushResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type JoinFansWelcomePush struct {
	FromUserInfo         *SimpleUserInfo `protobuf:"bytes,1,opt,name=from_user_info,json=fromUserInfo,proto3" json:"from_user_info,omitempty"`
	ToUserInfo           *SimpleUserInfo `protobuf:"bytes,2,opt,name=to_user_info,json=toUserInfo,proto3" json:"to_user_info,omitempty"`
	SocialCommunityName  string          `protobuf:"bytes,3,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	RoleName             string          `protobuf:"bytes,4,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	CategoryTypeName     string          `protobuf:"bytes,5,opt,name=category_type_name,json=categoryTypeName,proto3" json:"category_type_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *JoinFansWelcomePush) Reset()         { *m = JoinFansWelcomePush{} }
func (m *JoinFansWelcomePush) String() string { return proto.CompactTextString(m) }
func (*JoinFansWelcomePush) ProtoMessage()    {}
func (*JoinFansWelcomePush) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{27}
}
func (m *JoinFansWelcomePush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinFansWelcomePush.Unmarshal(m, b)
}
func (m *JoinFansWelcomePush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinFansWelcomePush.Marshal(b, m, deterministic)
}
func (dst *JoinFansWelcomePush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinFansWelcomePush.Merge(dst, src)
}
func (m *JoinFansWelcomePush) XXX_Size() int {
	return xxx_messageInfo_JoinFansWelcomePush.Size(m)
}
func (m *JoinFansWelcomePush) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinFansWelcomePush.DiscardUnknown(m)
}

var xxx_messageInfo_JoinFansWelcomePush proto.InternalMessageInfo

func (m *JoinFansWelcomePush) GetFromUserInfo() *SimpleUserInfo {
	if m != nil {
		return m.FromUserInfo
	}
	return nil
}

func (m *JoinFansWelcomePush) GetToUserInfo() *SimpleUserInfo {
	if m != nil {
		return m.ToUserInfo
	}
	return nil
}

func (m *JoinFansWelcomePush) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *JoinFansWelcomePush) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *JoinFansWelcomePush) GetCategoryTypeName() string {
	if m != nil {
		return m.CategoryTypeName
	}
	return ""
}

type GetSocialCommunityProfilePagesRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityProfilePagesRequest) Reset()         { *m = GetSocialCommunityProfilePagesRequest{} }
func (m *GetSocialCommunityProfilePagesRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityProfilePagesRequest) ProtoMessage()    {}
func (*GetSocialCommunityProfilePagesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{28}
}
func (m *GetSocialCommunityProfilePagesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityProfilePagesRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityProfilePagesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityProfilePagesRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityProfilePagesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityProfilePagesRequest.Merge(dst, src)
}
func (m *GetSocialCommunityProfilePagesRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityProfilePagesRequest.Size(m)
}
func (m *GetSocialCommunityProfilePagesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityProfilePagesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityProfilePagesRequest proto.InternalMessageInfo

func (m *GetSocialCommunityProfilePagesRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type SocialCommunityMemberInfo struct {
	UserInfo             *SimpleUserInfo   `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	Intro                string            `protobuf:"bytes,2,opt,name=intro,proto3" json:"intro,omitempty"`
	Role                 BrandMemberRoleV2 `protobuf:"varint,3,opt,name=role,proto3,enum=ga.muse_social_community_logic.BrandMemberRoleV2" json:"role,omitempty"`
	MemberText           string            `protobuf:"bytes,4,opt,name=member_text,json=memberText,proto3" json:"member_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SocialCommunityMemberInfo) Reset()         { *m = SocialCommunityMemberInfo{} }
func (m *SocialCommunityMemberInfo) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityMemberInfo) ProtoMessage()    {}
func (*SocialCommunityMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{29}
}
func (m *SocialCommunityMemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityMemberInfo.Unmarshal(m, b)
}
func (m *SocialCommunityMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityMemberInfo.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityMemberInfo.Merge(dst, src)
}
func (m *SocialCommunityMemberInfo) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityMemberInfo.Size(m)
}
func (m *SocialCommunityMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityMemberInfo proto.InternalMessageInfo

func (m *SocialCommunityMemberInfo) GetUserInfo() *SimpleUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *SocialCommunityMemberInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *SocialCommunityMemberInfo) GetRole() BrandMemberRoleV2 {
	if m != nil {
		return m.Role
	}
	return BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_UNSPECIFIED
}

func (m *SocialCommunityMemberInfo) GetMemberText() string {
	if m != nil {
		return m.MemberText
	}
	return ""
}

type SocialCommunityPhotoAlbumKeyURL struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityPhotoAlbumKeyURL) Reset()         { *m = SocialCommunityPhotoAlbumKeyURL{} }
func (m *SocialCommunityPhotoAlbumKeyURL) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityPhotoAlbumKeyURL) ProtoMessage()    {}
func (*SocialCommunityPhotoAlbumKeyURL) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{30}
}
func (m *SocialCommunityPhotoAlbumKeyURL) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityPhotoAlbumKeyURL.Unmarshal(m, b)
}
func (m *SocialCommunityPhotoAlbumKeyURL) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityPhotoAlbumKeyURL.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityPhotoAlbumKeyURL) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityPhotoAlbumKeyURL.Merge(dst, src)
}
func (m *SocialCommunityPhotoAlbumKeyURL) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityPhotoAlbumKeyURL.Size(m)
}
func (m *SocialCommunityPhotoAlbumKeyURL) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityPhotoAlbumKeyURL.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityPhotoAlbumKeyURL proto.InternalMessageInfo

func (m *SocialCommunityPhotoAlbumKeyURL) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SocialCommunityPhotoAlbumKeyURL) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type UserInfoInTheCommunity struct {
	UserInfo             *SimpleUserInfo   `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	JoiningDuration      uint32            `protobuf:"varint,2,opt,name=joining_duration,json=joiningDuration,proto3" json:"joining_duration,omitempty"`
	Role                 BrandMemberRoleV2 `protobuf:"varint,3,opt,name=role,proto3,enum=ga.muse_social_community_logic.BrandMemberRoleV2" json:"role,omitempty"`
	MemberText           string            `protobuf:"bytes,4,opt,name=member_text,json=memberText,proto3" json:"member_text,omitempty"`
	CountDownText        string            `protobuf:"bytes,5,opt,name=count_down_text,json=countDownText,proto3" json:"count_down_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserInfoInTheCommunity) Reset()         { *m = UserInfoInTheCommunity{} }
func (m *UserInfoInTheCommunity) String() string { return proto.CompactTextString(m) }
func (*UserInfoInTheCommunity) ProtoMessage()    {}
func (*UserInfoInTheCommunity) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{31}
}
func (m *UserInfoInTheCommunity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoInTheCommunity.Unmarshal(m, b)
}
func (m *UserInfoInTheCommunity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoInTheCommunity.Marshal(b, m, deterministic)
}
func (dst *UserInfoInTheCommunity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoInTheCommunity.Merge(dst, src)
}
func (m *UserInfoInTheCommunity) XXX_Size() int {
	return xxx_messageInfo_UserInfoInTheCommunity.Size(m)
}
func (m *UserInfoInTheCommunity) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoInTheCommunity.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoInTheCommunity proto.InternalMessageInfo

func (m *UserInfoInTheCommunity) GetUserInfo() *SimpleUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *UserInfoInTheCommunity) GetJoiningDuration() uint32 {
	if m != nil {
		return m.JoiningDuration
	}
	return 0
}

func (m *UserInfoInTheCommunity) GetRole() BrandMemberRoleV2 {
	if m != nil {
		return m.Role
	}
	return BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_UNSPECIFIED
}

func (m *UserInfoInTheCommunity) GetMemberText() string {
	if m != nil {
		return m.MemberText
	}
	return ""
}

func (m *UserInfoInTheCommunity) GetCountDownText() string {
	if m != nil {
		return m.CountDownText
	}
	return ""
}

type InviteMembersDisplay struct {
	DissolutionCountdown int64    `protobuf:"varint,1,opt,name=dissolution_countdown,json=dissolutionCountdown,proto3" json:"dissolution_countdown,omitempty"`
	InvitedMembers       uint32   `protobuf:"varint,2,opt,name=invited_members,json=invitedMembers,proto3" json:"invited_members,omitempty"`
	InviteMemberTotal    uint32   `protobuf:"varint,3,opt,name=invite_member_total,json=inviteMemberTotal,proto3" json:"invite_member_total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InviteMembersDisplay) Reset()         { *m = InviteMembersDisplay{} }
func (m *InviteMembersDisplay) String() string { return proto.CompactTextString(m) }
func (*InviteMembersDisplay) ProtoMessage()    {}
func (*InviteMembersDisplay) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{32}
}
func (m *InviteMembersDisplay) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InviteMembersDisplay.Unmarshal(m, b)
}
func (m *InviteMembersDisplay) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InviteMembersDisplay.Marshal(b, m, deterministic)
}
func (dst *InviteMembersDisplay) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InviteMembersDisplay.Merge(dst, src)
}
func (m *InviteMembersDisplay) XXX_Size() int {
	return xxx_messageInfo_InviteMembersDisplay.Size(m)
}
func (m *InviteMembersDisplay) XXX_DiscardUnknown() {
	xxx_messageInfo_InviteMembersDisplay.DiscardUnknown(m)
}

var xxx_messageInfo_InviteMembersDisplay proto.InternalMessageInfo

func (m *InviteMembersDisplay) GetDissolutionCountdown() int64 {
	if m != nil {
		return m.DissolutionCountdown
	}
	return 0
}

func (m *InviteMembersDisplay) GetInvitedMembers() uint32 {
	if m != nil {
		return m.InvitedMembers
	}
	return 0
}

func (m *InviteMembersDisplay) GetInviteMemberTotal() uint32 {
	if m != nil {
		return m.InviteMemberTotal
	}
	return 0
}

type GetSocialCommunityProfilePagesResponse struct {
	BaseResp                      *app.BaseResp                         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SecondaryBars                 []*MuseSocialCommunityNavSecondaryBar `protobuf:"bytes,2,rep,name=secondary_bars,json=secondaryBars,proto3" json:"secondary_bars,omitempty"`
	DetailInfo                    *SocialCommunityDetail                `protobuf:"bytes,3,opt,name=detail_info,json=detailInfo,proto3" json:"detail_info,omitempty"`
	MemberList                    []*SocialCommunityMemberInfo          `protobuf:"bytes,4,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	PhotoList                     []*SocialCommunityPhotoAlbumKeyURL    `protobuf:"bytes,5,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
	SocialCommunityBackgroundLogo string                                `protobuf:"bytes,6,opt,name=social_community_background_logo,json=socialCommunityBackgroundLogo,proto3" json:"social_community_background_logo,omitempty"`
	RobotUrl                      []string                              `protobuf:"bytes,7,rep,name=robot_url,json=robotUrl,proto3" json:"robot_url,omitempty"`
	TopicId                       string                                `protobuf:"bytes,8,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ViewCount                     uint32                                `protobuf:"varint,9,opt,name=view_count,json=viewCount,proto3" json:"view_count,omitempty"`
	UserInfo                      *UserInfoInTheCommunity               `protobuf:"bytes,10,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	InSharePicUrl                 string                                `protobuf:"bytes,11,opt,name=in_share_pic_url,json=inSharePicUrl,proto3" json:"in_share_pic_url,omitempty"`
	HonorInfo                     *SocialRankHonorSignInfo              `protobuf:"bytes,12,opt,name=honor_info,json=honorInfo,proto3" json:"honor_info,omitempty"`
	SocialCommunityOwnerStatus    uint32                                `protobuf:"varint,13,opt,name=social_community_owner_status,json=socialCommunityOwnerStatus,proto3" json:"social_community_owner_status,omitempty"`
	InviteMemberDisplay           *InviteMembersDisplay                 `protobuf:"bytes,14,opt,name=invite_member_display,json=inviteMemberDisplay,proto3" json:"invite_member_display,omitempty"`
	RemainingMemberSeats          uint32                                `protobuf:"varint,15,opt,name=remaining_member_seats,json=remainingMemberSeats,proto3" json:"remaining_member_seats,omitempty"`
	SystemMessageCount            uint32                                `protobuf:"varint,16,opt,name=system_message_count,json=systemMessageCount,proto3" json:"system_message_count,omitempty"`
	XXX_NoUnkeyedLiteral          struct{}                              `json:"-"`
	XXX_unrecognized              []byte                                `json:"-"`
	XXX_sizecache                 int32                                 `json:"-"`
}

func (m *GetSocialCommunityProfilePagesResponse) Reset() {
	*m = GetSocialCommunityProfilePagesResponse{}
}
func (m *GetSocialCommunityProfilePagesResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityProfilePagesResponse) ProtoMessage()    {}
func (*GetSocialCommunityProfilePagesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{33}
}
func (m *GetSocialCommunityProfilePagesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityProfilePagesResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityProfilePagesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityProfilePagesResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityProfilePagesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityProfilePagesResponse.Merge(dst, src)
}
func (m *GetSocialCommunityProfilePagesResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityProfilePagesResponse.Size(m)
}
func (m *GetSocialCommunityProfilePagesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityProfilePagesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityProfilePagesResponse proto.InternalMessageInfo

func (m *GetSocialCommunityProfilePagesResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesResponse) GetSecondaryBars() []*MuseSocialCommunityNavSecondaryBar {
	if m != nil {
		return m.SecondaryBars
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesResponse) GetDetailInfo() *SocialCommunityDetail {
	if m != nil {
		return m.DetailInfo
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesResponse) GetMemberList() []*SocialCommunityMemberInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesResponse) GetPhotoList() []*SocialCommunityPhotoAlbumKeyURL {
	if m != nil {
		return m.PhotoList
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesResponse) GetSocialCommunityBackgroundLogo() string {
	if m != nil {
		return m.SocialCommunityBackgroundLogo
	}
	return ""
}

func (m *GetSocialCommunityProfilePagesResponse) GetRobotUrl() []string {
	if m != nil {
		return m.RobotUrl
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesResponse) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetSocialCommunityProfilePagesResponse) GetViewCount() uint32 {
	if m != nil {
		return m.ViewCount
	}
	return 0
}

func (m *GetSocialCommunityProfilePagesResponse) GetUserInfo() *UserInfoInTheCommunity {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesResponse) GetInSharePicUrl() string {
	if m != nil {
		return m.InSharePicUrl
	}
	return ""
}

func (m *GetSocialCommunityProfilePagesResponse) GetHonorInfo() *SocialRankHonorSignInfo {
	if m != nil {
		return m.HonorInfo
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesResponse) GetSocialCommunityOwnerStatus() uint32 {
	if m != nil {
		return m.SocialCommunityOwnerStatus
	}
	return 0
}

func (m *GetSocialCommunityProfilePagesResponse) GetInviteMemberDisplay() *InviteMembersDisplay {
	if m != nil {
		return m.InviteMemberDisplay
	}
	return nil
}

func (m *GetSocialCommunityProfilePagesResponse) GetRemainingMemberSeats() uint32 {
	if m != nil {
		return m.RemainingMemberSeats
	}
	return 0
}

func (m *GetSocialCommunityProfilePagesResponse) GetSystemMessageCount() uint32 {
	if m != nil {
		return m.SystemMessageCount
	}
	return 0
}

// 上榜社群的公演房增加榜单入口
type BatGetRankInChannelRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelIdList        []uint32     `protobuf:"varint,2,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatGetRankInChannelRequest) Reset()         { *m = BatGetRankInChannelRequest{} }
func (m *BatGetRankInChannelRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetRankInChannelRequest) ProtoMessage()    {}
func (*BatGetRankInChannelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{34}
}
func (m *BatGetRankInChannelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetRankInChannelRequest.Unmarshal(m, b)
}
func (m *BatGetRankInChannelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetRankInChannelRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetRankInChannelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetRankInChannelRequest.Merge(dst, src)
}
func (m *BatGetRankInChannelRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetRankInChannelRequest.Size(m)
}
func (m *BatGetRankInChannelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetRankInChannelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetRankInChannelRequest proto.InternalMessageInfo

func (m *BatGetRankInChannelRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatGetRankInChannelRequest) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type RankInChannelInfo struct {
	CategoryId           string   `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	RankType             uint32   `protobuf:"varint,2,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	SocialCommunityId    string   `protobuf:"bytes,4,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	JumpRankUrl          string   `protobuf:"bytes,5,opt,name=jump_rank_url,json=jumpRankUrl,proto3" json:"jump_rank_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RankInChannelInfo) Reset()         { *m = RankInChannelInfo{} }
func (m *RankInChannelInfo) String() string { return proto.CompactTextString(m) }
func (*RankInChannelInfo) ProtoMessage()    {}
func (*RankInChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{35}
}
func (m *RankInChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RankInChannelInfo.Unmarshal(m, b)
}
func (m *RankInChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RankInChannelInfo.Marshal(b, m, deterministic)
}
func (dst *RankInChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RankInChannelInfo.Merge(dst, src)
}
func (m *RankInChannelInfo) XXX_Size() int {
	return xxx_messageInfo_RankInChannelInfo.Size(m)
}
func (m *RankInChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RankInChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RankInChannelInfo proto.InternalMessageInfo

func (m *RankInChannelInfo) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *RankInChannelInfo) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *RankInChannelInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *RankInChannelInfo) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *RankInChannelInfo) GetJumpRankUrl() string {
	if m != nil {
		return m.JumpRankUrl
	}
	return ""
}

type SocialRankHonorSignInfo struct {
	Icon                 string   `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	StyleColorList       []string `protobuf:"bytes,2,rep,name=style_color_list,json=styleColorList,proto3" json:"style_color_list,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	CategoryId           string   `protobuf:"bytes,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	RankType             uint32   `protobuf:"varint,5,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	JumpRankUrl          string   `protobuf:"bytes,6,opt,name=jump_rank_url,json=jumpRankUrl,proto3" json:"jump_rank_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialRankHonorSignInfo) Reset()         { *m = SocialRankHonorSignInfo{} }
func (m *SocialRankHonorSignInfo) String() string { return proto.CompactTextString(m) }
func (*SocialRankHonorSignInfo) ProtoMessage()    {}
func (*SocialRankHonorSignInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{36}
}
func (m *SocialRankHonorSignInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialRankHonorSignInfo.Unmarshal(m, b)
}
func (m *SocialRankHonorSignInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialRankHonorSignInfo.Marshal(b, m, deterministic)
}
func (dst *SocialRankHonorSignInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialRankHonorSignInfo.Merge(dst, src)
}
func (m *SocialRankHonorSignInfo) XXX_Size() int {
	return xxx_messageInfo_SocialRankHonorSignInfo.Size(m)
}
func (m *SocialRankHonorSignInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialRankHonorSignInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SocialRankHonorSignInfo proto.InternalMessageInfo

func (m *SocialRankHonorSignInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *SocialRankHonorSignInfo) GetStyleColorList() []string {
	if m != nil {
		return m.StyleColorList
	}
	return nil
}

func (m *SocialRankHonorSignInfo) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SocialRankHonorSignInfo) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *SocialRankHonorSignInfo) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

func (m *SocialRankHonorSignInfo) GetJumpRankUrl() string {
	if m != nil {
		return m.JumpRankUrl
	}
	return ""
}

type BatGetRankInChannelResponse struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RankInChannelInfoMap map[uint32]*RankInChannelInfo `protobuf:"bytes,2,rep,name=rank_in_channel_info_map,json=rankInChannelInfoMap,proto3" json:"rank_in_channel_info_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatGetRankInChannelResponse) Reset()         { *m = BatGetRankInChannelResponse{} }
func (m *BatGetRankInChannelResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetRankInChannelResponse) ProtoMessage()    {}
func (*BatGetRankInChannelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{37}
}
func (m *BatGetRankInChannelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetRankInChannelResponse.Unmarshal(m, b)
}
func (m *BatGetRankInChannelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetRankInChannelResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetRankInChannelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetRankInChannelResponse.Merge(dst, src)
}
func (m *BatGetRankInChannelResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetRankInChannelResponse.Size(m)
}
func (m *BatGetRankInChannelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetRankInChannelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetRankInChannelResponse proto.InternalMessageInfo

func (m *BatGetRankInChannelResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatGetRankInChannelResponse) GetRankInChannelInfoMap() map[uint32]*RankInChannelInfo {
	if m != nil {
		return m.RankInChannelInfoMap
	}
	return nil
}

// 上榜社群的公演房增加榜单入口 推送
type RankInChannelNotify struct {
	ChannelId            uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RankInfo             *RankInChannelInfo `protobuf:"bytes,2,opt,name=rank_info,json=rankInfo,proto3" json:"rank_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *RankInChannelNotify) Reset()         { *m = RankInChannelNotify{} }
func (m *RankInChannelNotify) String() string { return proto.CompactTextString(m) }
func (*RankInChannelNotify) ProtoMessage()    {}
func (*RankInChannelNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{38}
}
func (m *RankInChannelNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RankInChannelNotify.Unmarshal(m, b)
}
func (m *RankInChannelNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RankInChannelNotify.Marshal(b, m, deterministic)
}
func (dst *RankInChannelNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RankInChannelNotify.Merge(dst, src)
}
func (m *RankInChannelNotify) XXX_Size() int {
	return xxx_messageInfo_RankInChannelNotify.Size(m)
}
func (m *RankInChannelNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_RankInChannelNotify.DiscardUnknown(m)
}

var xxx_messageInfo_RankInChannelNotify proto.InternalMessageInfo

func (m *RankInChannelNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RankInChannelNotify) GetRankInfo() *RankInChannelInfo {
	if m != nil {
		return m.RankInfo
	}
	return nil
}

type RemoveSocialCommunityMemberRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	TargetUid            uint32       `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Reason               string       `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RemoveSocialCommunityMemberRequest) Reset()         { *m = RemoveSocialCommunityMemberRequest{} }
func (m *RemoveSocialCommunityMemberRequest) String() string { return proto.CompactTextString(m) }
func (*RemoveSocialCommunityMemberRequest) ProtoMessage()    {}
func (*RemoveSocialCommunityMemberRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{39}
}
func (m *RemoveSocialCommunityMemberRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveSocialCommunityMemberRequest.Unmarshal(m, b)
}
func (m *RemoveSocialCommunityMemberRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveSocialCommunityMemberRequest.Marshal(b, m, deterministic)
}
func (dst *RemoveSocialCommunityMemberRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveSocialCommunityMemberRequest.Merge(dst, src)
}
func (m *RemoveSocialCommunityMemberRequest) XXX_Size() int {
	return xxx_messageInfo_RemoveSocialCommunityMemberRequest.Size(m)
}
func (m *RemoveSocialCommunityMemberRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveSocialCommunityMemberRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveSocialCommunityMemberRequest proto.InternalMessageInfo

func (m *RemoveSocialCommunityMemberRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RemoveSocialCommunityMemberRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *RemoveSocialCommunityMemberRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *RemoveSocialCommunityMemberRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type RemoveSocialCommunityMemberResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RemoveSocialCommunityMemberResponse) Reset()         { *m = RemoveSocialCommunityMemberResponse{} }
func (m *RemoveSocialCommunityMemberResponse) String() string { return proto.CompactTextString(m) }
func (*RemoveSocialCommunityMemberResponse) ProtoMessage()    {}
func (*RemoveSocialCommunityMemberResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{40}
}
func (m *RemoveSocialCommunityMemberResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveSocialCommunityMemberResponse.Unmarshal(m, b)
}
func (m *RemoveSocialCommunityMemberResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveSocialCommunityMemberResponse.Marshal(b, m, deterministic)
}
func (dst *RemoveSocialCommunityMemberResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveSocialCommunityMemberResponse.Merge(dst, src)
}
func (m *RemoveSocialCommunityMemberResponse) XXX_Size() int {
	return xxx_messageInfo_RemoveSocialCommunityMemberResponse.Size(m)
}
func (m *RemoveSocialCommunityMemberResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveSocialCommunityMemberResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveSocialCommunityMemberResponse proto.InternalMessageInfo

func (m *RemoveSocialCommunityMemberResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ExitSocialCommunityRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	Status               MemberStatus `protobuf:"varint,3,opt,name=status,proto3,enum=ga.muse_social_community_logic.MemberStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ExitSocialCommunityRequest) Reset()         { *m = ExitSocialCommunityRequest{} }
func (m *ExitSocialCommunityRequest) String() string { return proto.CompactTextString(m) }
func (*ExitSocialCommunityRequest) ProtoMessage()    {}
func (*ExitSocialCommunityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{41}
}
func (m *ExitSocialCommunityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitSocialCommunityRequest.Unmarshal(m, b)
}
func (m *ExitSocialCommunityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitSocialCommunityRequest.Marshal(b, m, deterministic)
}
func (dst *ExitSocialCommunityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitSocialCommunityRequest.Merge(dst, src)
}
func (m *ExitSocialCommunityRequest) XXX_Size() int {
	return xxx_messageInfo_ExitSocialCommunityRequest.Size(m)
}
func (m *ExitSocialCommunityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitSocialCommunityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ExitSocialCommunityRequest proto.InternalMessageInfo

func (m *ExitSocialCommunityRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ExitSocialCommunityRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *ExitSocialCommunityRequest) GetStatus() MemberStatus {
	if m != nil {
		return m.Status
	}
	return MemberStatus_MEMBER_STATUS_UNSPECIFIED
}

type ExitSocialCommunityResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ExitSocialCommunityResponse) Reset()         { *m = ExitSocialCommunityResponse{} }
func (m *ExitSocialCommunityResponse) String() string { return proto.CompactTextString(m) }
func (*ExitSocialCommunityResponse) ProtoMessage()    {}
func (*ExitSocialCommunityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{42}
}
func (m *ExitSocialCommunityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitSocialCommunityResponse.Unmarshal(m, b)
}
func (m *ExitSocialCommunityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitSocialCommunityResponse.Marshal(b, m, deterministic)
}
func (dst *ExitSocialCommunityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitSocialCommunityResponse.Merge(dst, src)
}
func (m *ExitSocialCommunityResponse) XXX_Size() int {
	return xxx_messageInfo_ExitSocialCommunityResponse.Size(m)
}
func (m *ExitSocialCommunityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitSocialCommunityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ExitSocialCommunityResponse proto.InternalMessageInfo

func (m *ExitSocialCommunityResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type UpdateMemberRoleRequest struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string            `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	Role                 BrandMemberRoleV2 `protobuf:"varint,3,opt,name=role,proto3,enum=ga.muse_social_community_logic.BrandMemberRoleV2" json:"role,omitempty"`
	TargetUid            uint32            `protobuf:"varint,4,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateMemberRoleRequest) Reset()         { *m = UpdateMemberRoleRequest{} }
func (m *UpdateMemberRoleRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateMemberRoleRequest) ProtoMessage()    {}
func (*UpdateMemberRoleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{43}
}
func (m *UpdateMemberRoleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMemberRoleRequest.Unmarshal(m, b)
}
func (m *UpdateMemberRoleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMemberRoleRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateMemberRoleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMemberRoleRequest.Merge(dst, src)
}
func (m *UpdateMemberRoleRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateMemberRoleRequest.Size(m)
}
func (m *UpdateMemberRoleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMemberRoleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMemberRoleRequest proto.InternalMessageInfo

func (m *UpdateMemberRoleRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdateMemberRoleRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *UpdateMemberRoleRequest) GetRole() BrandMemberRoleV2 {
	if m != nil {
		return m.Role
	}
	return BrandMemberRoleV2_BRAND_MEMBER_ROLE_V2_UNSPECIFIED
}

func (m *UpdateMemberRoleRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type UpdateMemberRoleResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateMemberRoleResponse) Reset()         { *m = UpdateMemberRoleResponse{} }
func (m *UpdateMemberRoleResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateMemberRoleResponse) ProtoMessage()    {}
func (*UpdateMemberRoleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{44}
}
func (m *UpdateMemberRoleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMemberRoleResponse.Unmarshal(m, b)
}
func (m *UpdateMemberRoleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMemberRoleResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateMemberRoleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMemberRoleResponse.Merge(dst, src)
}
func (m *UpdateMemberRoleResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateMemberRoleResponse.Size(m)
}
func (m *UpdateMemberRoleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMemberRoleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMemberRoleResponse proto.InternalMessageInfo

func (m *UpdateMemberRoleResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetSocialCommunityRoleLeftNumbersRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityRoleLeftNumbersRequest) Reset() {
	*m = GetSocialCommunityRoleLeftNumbersRequest{}
}
func (m *GetSocialCommunityRoleLeftNumbersRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityRoleLeftNumbersRequest) ProtoMessage()    {}
func (*GetSocialCommunityRoleLeftNumbersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{45}
}
func (m *GetSocialCommunityRoleLeftNumbersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityRoleLeftNumbersRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityRoleLeftNumbersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityRoleLeftNumbersRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityRoleLeftNumbersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityRoleLeftNumbersRequest.Merge(dst, src)
}
func (m *GetSocialCommunityRoleLeftNumbersRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityRoleLeftNumbersRequest.Size(m)
}
func (m *GetSocialCommunityRoleLeftNumbersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityRoleLeftNumbersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityRoleLeftNumbersRequest proto.InternalMessageInfo

func (m *GetSocialCommunityRoleLeftNumbersRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialCommunityRoleLeftNumbersRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type GetSocialCommunityRoleLeftNumbersResponse struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RoleLeftNumbersMap   map[uint32]int32 `protobuf:"bytes,2,rep,name=role_left_numbers_map,json=roleLeftNumbersMap,proto3" json:"role_left_numbers_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetSocialCommunityRoleLeftNumbersResponse) Reset() {
	*m = GetSocialCommunityRoleLeftNumbersResponse{}
}
func (m *GetSocialCommunityRoleLeftNumbersResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetSocialCommunityRoleLeftNumbersResponse) ProtoMessage() {}
func (*GetSocialCommunityRoleLeftNumbersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{46}
}
func (m *GetSocialCommunityRoleLeftNumbersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityRoleLeftNumbersResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityRoleLeftNumbersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityRoleLeftNumbersResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityRoleLeftNumbersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityRoleLeftNumbersResponse.Merge(dst, src)
}
func (m *GetSocialCommunityRoleLeftNumbersResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityRoleLeftNumbersResponse.Size(m)
}
func (m *GetSocialCommunityRoleLeftNumbersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityRoleLeftNumbersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityRoleLeftNumbersResponse proto.InternalMessageInfo

func (m *GetSocialCommunityRoleLeftNumbersResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityRoleLeftNumbersResponse) GetRoleLeftNumbersMap() map[uint32]int32 {
	if m != nil {
		return m.RoleLeftNumbersMap
	}
	return nil
}

// 获取兴趣讨论区
type GetChannelAssociateSocialCommunityRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelAssociateSocialCommunityRequest) Reset() {
	*m = GetChannelAssociateSocialCommunityRequest{}
}
func (m *GetChannelAssociateSocialCommunityRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetChannelAssociateSocialCommunityRequest) ProtoMessage() {}
func (*GetChannelAssociateSocialCommunityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{47}
}
func (m *GetChannelAssociateSocialCommunityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAssociateSocialCommunityRequest.Unmarshal(m, b)
}
func (m *GetChannelAssociateSocialCommunityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAssociateSocialCommunityRequest.Marshal(b, m, deterministic)
}
func (dst *GetChannelAssociateSocialCommunityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAssociateSocialCommunityRequest.Merge(dst, src)
}
func (m *GetChannelAssociateSocialCommunityRequest) XXX_Size() int {
	return xxx_messageInfo_GetChannelAssociateSocialCommunityRequest.Size(m)
}
func (m *GetChannelAssociateSocialCommunityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAssociateSocialCommunityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAssociateSocialCommunityRequest proto.InternalMessageInfo

func (m *GetChannelAssociateSocialCommunityRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelAssociateSocialCommunityRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelAssociateSocialCommunityRequest) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetChannelAssociateSocialCommunityResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Icon                 string        `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Text                 string        `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	JumlUrl              string        `protobuf:"bytes,4,opt,name=juml_url,json=jumlUrl,proto3" json:"juml_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelAssociateSocialCommunityResponse) Reset() {
	*m = GetChannelAssociateSocialCommunityResponse{}
}
func (m *GetChannelAssociateSocialCommunityResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetChannelAssociateSocialCommunityResponse) ProtoMessage() {}
func (*GetChannelAssociateSocialCommunityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{48}
}
func (m *GetChannelAssociateSocialCommunityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAssociateSocialCommunityResponse.Unmarshal(m, b)
}
func (m *GetChannelAssociateSocialCommunityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAssociateSocialCommunityResponse.Marshal(b, m, deterministic)
}
func (dst *GetChannelAssociateSocialCommunityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAssociateSocialCommunityResponse.Merge(dst, src)
}
func (m *GetChannelAssociateSocialCommunityResponse) XXX_Size() int {
	return xxx_messageInfo_GetChannelAssociateSocialCommunityResponse.Size(m)
}
func (m *GetChannelAssociateSocialCommunityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAssociateSocialCommunityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAssociateSocialCommunityResponse proto.InternalMessageInfo

func (m *GetChannelAssociateSocialCommunityResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelAssociateSocialCommunityResponse) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetChannelAssociateSocialCommunityResponse) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *GetChannelAssociateSocialCommunityResponse) GetJumlUrl() string {
	if m != nil {
		return m.JumlUrl
	}
	return ""
}

type GetMySocialCommunityPageRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OffsetId             string       `protobuf:"bytes,2,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMySocialCommunityPageRequest) Reset()         { *m = GetMySocialCommunityPageRequest{} }
func (m *GetMySocialCommunityPageRequest) String() string { return proto.CompactTextString(m) }
func (*GetMySocialCommunityPageRequest) ProtoMessage()    {}
func (*GetMySocialCommunityPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{49}
}
func (m *GetMySocialCommunityPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMySocialCommunityPageRequest.Unmarshal(m, b)
}
func (m *GetMySocialCommunityPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMySocialCommunityPageRequest.Marshal(b, m, deterministic)
}
func (dst *GetMySocialCommunityPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMySocialCommunityPageRequest.Merge(dst, src)
}
func (m *GetMySocialCommunityPageRequest) XXX_Size() int {
	return xxx_messageInfo_GetMySocialCommunityPageRequest.Size(m)
}
func (m *GetMySocialCommunityPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMySocialCommunityPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMySocialCommunityPageRequest proto.InternalMessageInfo

func (m *GetMySocialCommunityPageRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMySocialCommunityPageRequest) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *GetMySocialCommunityPageRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetMySocialCommunityPageResponse struct {
	BaseResp                   *app.BaseResp                       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SocialCommunityOwnerStatus uint32                              `protobuf:"varint,2,opt,name=social_community_owner_status,json=socialCommunityOwnerStatus,proto3" json:"social_community_owner_status,omitempty"`
	Channels                   []*SocialCommunityView              `protobuf:"bytes,3,rep,name=channels,proto3" json:"channels,omitempty"`
	RcmdChannels               []*music_topic_channel.MusicChannel `protobuf:"bytes,4,rep,name=rcmd_channels,json=rcmdChannels,proto3" json:"rcmd_channels,omitempty"`
	OffsetId                   string                              `protobuf:"bytes,5,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                            `json:"-"`
	XXX_unrecognized           []byte                              `json:"-"`
	XXX_sizecache              int32                               `json:"-"`
}

func (m *GetMySocialCommunityPageResponse) Reset()         { *m = GetMySocialCommunityPageResponse{} }
func (m *GetMySocialCommunityPageResponse) String() string { return proto.CompactTextString(m) }
func (*GetMySocialCommunityPageResponse) ProtoMessage()    {}
func (*GetMySocialCommunityPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{50}
}
func (m *GetMySocialCommunityPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMySocialCommunityPageResponse.Unmarshal(m, b)
}
func (m *GetMySocialCommunityPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMySocialCommunityPageResponse.Marshal(b, m, deterministic)
}
func (dst *GetMySocialCommunityPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMySocialCommunityPageResponse.Merge(dst, src)
}
func (m *GetMySocialCommunityPageResponse) XXX_Size() int {
	return xxx_messageInfo_GetMySocialCommunityPageResponse.Size(m)
}
func (m *GetMySocialCommunityPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMySocialCommunityPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMySocialCommunityPageResponse proto.InternalMessageInfo

func (m *GetMySocialCommunityPageResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMySocialCommunityPageResponse) GetSocialCommunityOwnerStatus() uint32 {
	if m != nil {
		return m.SocialCommunityOwnerStatus
	}
	return 0
}

func (m *GetMySocialCommunityPageResponse) GetChannels() []*SocialCommunityView {
	if m != nil {
		return m.Channels
	}
	return nil
}

func (m *GetMySocialCommunityPageResponse) GetRcmdChannels() []*music_topic_channel.MusicChannel {
	if m != nil {
		return m.RcmdChannels
	}
	return nil
}

func (m *GetMySocialCommunityPageResponse) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

type SocialCommunityView struct {
	Id                   string                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Logo                 string                    `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`
	ChannelId            uint32                    `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OnShowDesc           string                    `protobuf:"bytes,5,opt,name=on_show_desc,json=onShowDesc,proto3" json:"on_show_desc,omitempty"`
	Label                *SocialCommunityViewLabel `protobuf:"bytes,6,opt,name=label,proto3" json:"label,omitempty"`
	BrandProfessionalism uint32                    `protobuf:"varint,7,opt,name=brand_professionalism,json=brandProfessionalism,proto3" json:"brand_professionalism,omitempty"`
	Status               uint32                    `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`
	GroupId              uint32                    `protobuf:"varint,9,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	HasNewMessage        bool                      `protobuf:"varint,10,opt,name=has_new_message,json=hasNewMessage,proto3" json:"has_new_message,omitempty"`
	OnShowText           string                    `protobuf:"bytes,11,opt,name=on_show_text,json=onShowText,proto3" json:"on_show_text,omitempty"`
	BtnType              uint32                    `protobuf:"varint,12,opt,name=btn_type,json=btnType,proto3" json:"btn_type,omitempty"`
	ProfessionalismInfo  *Professionalism          `protobuf:"bytes,13,opt,name=professionalism_info,json=professionalismInfo,proto3" json:"professionalism_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SocialCommunityView) Reset()         { *m = SocialCommunityView{} }
func (m *SocialCommunityView) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityView) ProtoMessage()    {}
func (*SocialCommunityView) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{51}
}
func (m *SocialCommunityView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityView.Unmarshal(m, b)
}
func (m *SocialCommunityView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityView.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityView.Merge(dst, src)
}
func (m *SocialCommunityView) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityView.Size(m)
}
func (m *SocialCommunityView) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityView.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityView proto.InternalMessageInfo

func (m *SocialCommunityView) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SocialCommunityView) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SocialCommunityView) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *SocialCommunityView) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SocialCommunityView) GetOnShowDesc() string {
	if m != nil {
		return m.OnShowDesc
	}
	return ""
}

func (m *SocialCommunityView) GetLabel() *SocialCommunityViewLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *SocialCommunityView) GetBrandProfessionalism() uint32 {
	if m != nil {
		return m.BrandProfessionalism
	}
	return 0
}

func (m *SocialCommunityView) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SocialCommunityView) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SocialCommunityView) GetHasNewMessage() bool {
	if m != nil {
		return m.HasNewMessage
	}
	return false
}

func (m *SocialCommunityView) GetOnShowText() string {
	if m != nil {
		return m.OnShowText
	}
	return ""
}

func (m *SocialCommunityView) GetBtnType() uint32 {
	if m != nil {
		return m.BtnType
	}
	return 0
}

func (m *SocialCommunityView) GetProfessionalismInfo() *Professionalism {
	if m != nil {
		return m.ProfessionalismInfo
	}
	return nil
}

type SocialCommunityViewLabel struct {
	Desc                 string   `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityViewLabel) Reset()         { *m = SocialCommunityViewLabel{} }
func (m *SocialCommunityViewLabel) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityViewLabel) ProtoMessage()    {}
func (*SocialCommunityViewLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{52}
}
func (m *SocialCommunityViewLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityViewLabel.Unmarshal(m, b)
}
func (m *SocialCommunityViewLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityViewLabel.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityViewLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityViewLabel.Merge(dst, src)
}
func (m *SocialCommunityViewLabel) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityViewLabel.Size(m)
}
func (m *SocialCommunityViewLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityViewLabel.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityViewLabel proto.InternalMessageInfo

func (m *SocialCommunityViewLabel) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SocialCommunityViewLabel) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type ListCategoryTypesRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListCategoryTypesRequest) Reset()         { *m = ListCategoryTypesRequest{} }
func (m *ListCategoryTypesRequest) String() string { return proto.CompactTextString(m) }
func (*ListCategoryTypesRequest) ProtoMessage()    {}
func (*ListCategoryTypesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{53}
}
func (m *ListCategoryTypesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListCategoryTypesRequest.Unmarshal(m, b)
}
func (m *ListCategoryTypesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListCategoryTypesRequest.Marshal(b, m, deterministic)
}
func (dst *ListCategoryTypesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListCategoryTypesRequest.Merge(dst, src)
}
func (m *ListCategoryTypesRequest) XXX_Size() int {
	return xxx_messageInfo_ListCategoryTypesRequest.Size(m)
}
func (m *ListCategoryTypesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListCategoryTypesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListCategoryTypesRequest proto.InternalMessageInfo

func (m *ListCategoryTypesRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ListCategoryTypesResponse struct {
	BaseResp                 *app.BaseResp                  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CategoryTypes            []*SocialCommunityCategoryType `protobuf:"bytes,2,rep,name=category_types,json=categoryTypes,proto3" json:"category_types,omitempty"`
	CreateSocialCommunityUrl string                         `protobuf:"bytes,3,opt,name=create_social_community_url,json=createSocialCommunityUrl,proto3" json:"create_social_community_url,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                       `json:"-"`
	XXX_unrecognized         []byte                         `json:"-"`
	XXX_sizecache            int32                          `json:"-"`
}

func (m *ListCategoryTypesResponse) Reset()         { *m = ListCategoryTypesResponse{} }
func (m *ListCategoryTypesResponse) String() string { return proto.CompactTextString(m) }
func (*ListCategoryTypesResponse) ProtoMessage()    {}
func (*ListCategoryTypesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{54}
}
func (m *ListCategoryTypesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListCategoryTypesResponse.Unmarshal(m, b)
}
func (m *ListCategoryTypesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListCategoryTypesResponse.Marshal(b, m, deterministic)
}
func (dst *ListCategoryTypesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListCategoryTypesResponse.Merge(dst, src)
}
func (m *ListCategoryTypesResponse) XXX_Size() int {
	return xxx_messageInfo_ListCategoryTypesResponse.Size(m)
}
func (m *ListCategoryTypesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListCategoryTypesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListCategoryTypesResponse proto.InternalMessageInfo

func (m *ListCategoryTypesResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListCategoryTypesResponse) GetCategoryTypes() []*SocialCommunityCategoryType {
	if m != nil {
		return m.CategoryTypes
	}
	return nil
}

func (m *ListCategoryTypesResponse) GetCreateSocialCommunityUrl() string {
	if m != nil {
		return m.CreateSocialCommunityUrl
	}
	return ""
}

type SocialCommunityCategoryType struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Intro                string   `protobuf:"bytes,3,opt,name=intro,proto3" json:"intro,omitempty"`
	Logo                 string   `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityCategoryType) Reset()         { *m = SocialCommunityCategoryType{} }
func (m *SocialCommunityCategoryType) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityCategoryType) ProtoMessage()    {}
func (*SocialCommunityCategoryType) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{55}
}
func (m *SocialCommunityCategoryType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityCategoryType.Unmarshal(m, b)
}
func (m *SocialCommunityCategoryType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityCategoryType.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityCategoryType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityCategoryType.Merge(dst, src)
}
func (m *SocialCommunityCategoryType) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityCategoryType.Size(m)
}
func (m *SocialCommunityCategoryType) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityCategoryType.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityCategoryType proto.InternalMessageInfo

func (m *SocialCommunityCategoryType) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SocialCommunityCategoryType) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SocialCommunityCategoryType) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *SocialCommunityCategoryType) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

type ListCategoriesRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CategoryTypeId       string       `protobuf:"bytes,2,opt,name=category_type_id,json=categoryTypeId,proto3" json:"category_type_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListCategoriesRequest) Reset()         { *m = ListCategoriesRequest{} }
func (m *ListCategoriesRequest) String() string { return proto.CompactTextString(m) }
func (*ListCategoriesRequest) ProtoMessage()    {}
func (*ListCategoriesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{56}
}
func (m *ListCategoriesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListCategoriesRequest.Unmarshal(m, b)
}
func (m *ListCategoriesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListCategoriesRequest.Marshal(b, m, deterministic)
}
func (dst *ListCategoriesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListCategoriesRequest.Merge(dst, src)
}
func (m *ListCategoriesRequest) XXX_Size() int {
	return xxx_messageInfo_ListCategoriesRequest.Size(m)
}
func (m *ListCategoriesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListCategoriesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListCategoriesRequest proto.InternalMessageInfo

func (m *ListCategoriesRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListCategoriesRequest) GetCategoryTypeId() string {
	if m != nil {
		return m.CategoryTypeId
	}
	return ""
}

type ListCategoriesResponse struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Categories           []*SocialCommunityCategory `protobuf:"bytes,2,rep,name=categories,proto3" json:"categories,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ListCategoriesResponse) Reset()         { *m = ListCategoriesResponse{} }
func (m *ListCategoriesResponse) String() string { return proto.CompactTextString(m) }
func (*ListCategoriesResponse) ProtoMessage()    {}
func (*ListCategoriesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{57}
}
func (m *ListCategoriesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListCategoriesResponse.Unmarshal(m, b)
}
func (m *ListCategoriesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListCategoriesResponse.Marshal(b, m, deterministic)
}
func (dst *ListCategoriesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListCategoriesResponse.Merge(dst, src)
}
func (m *ListCategoriesResponse) XXX_Size() int {
	return xxx_messageInfo_ListCategoriesResponse.Size(m)
}
func (m *ListCategoriesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListCategoriesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListCategoriesResponse proto.InternalMessageInfo

func (m *ListCategoriesResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListCategoriesResponse) GetCategories() []*SocialCommunityCategory {
	if m != nil {
		return m.Categories
	}
	return nil
}

type SocialCommunityCategory struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityCategory) Reset()         { *m = SocialCommunityCategory{} }
func (m *SocialCommunityCategory) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityCategory) ProtoMessage()    {}
func (*SocialCommunityCategory) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{58}
}
func (m *SocialCommunityCategory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityCategory.Unmarshal(m, b)
}
func (m *SocialCommunityCategory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityCategory.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityCategory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityCategory.Merge(dst, src)
}
func (m *SocialCommunityCategory) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityCategory.Size(m)
}
func (m *SocialCommunityCategory) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityCategory.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityCategory proto.InternalMessageInfo

func (m *SocialCommunityCategory) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SocialCommunityCategory) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type ApplyCreateSocialCommunityRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Name                 string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Intro                string       `protobuf:"bytes,3,opt,name=intro,proto3" json:"intro,omitempty"`
	CategoryId           string       `protobuf:"bytes,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Logo                 string       `protobuf:"bytes,5,opt,name=logo,proto3" json:"logo,omitempty"`
	Vision               string       `protobuf:"bytes,6,opt,name=vision,proto3" json:"vision,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ApplyCreateSocialCommunityRequest) Reset()         { *m = ApplyCreateSocialCommunityRequest{} }
func (m *ApplyCreateSocialCommunityRequest) String() string { return proto.CompactTextString(m) }
func (*ApplyCreateSocialCommunityRequest) ProtoMessage()    {}
func (*ApplyCreateSocialCommunityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{59}
}
func (m *ApplyCreateSocialCommunityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCreateSocialCommunityRequest.Unmarshal(m, b)
}
func (m *ApplyCreateSocialCommunityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCreateSocialCommunityRequest.Marshal(b, m, deterministic)
}
func (dst *ApplyCreateSocialCommunityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCreateSocialCommunityRequest.Merge(dst, src)
}
func (m *ApplyCreateSocialCommunityRequest) XXX_Size() int {
	return xxx_messageInfo_ApplyCreateSocialCommunityRequest.Size(m)
}
func (m *ApplyCreateSocialCommunityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCreateSocialCommunityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCreateSocialCommunityRequest proto.InternalMessageInfo

func (m *ApplyCreateSocialCommunityRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ApplyCreateSocialCommunityRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ApplyCreateSocialCommunityRequest) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *ApplyCreateSocialCommunityRequest) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

func (m *ApplyCreateSocialCommunityRequest) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *ApplyCreateSocialCommunityRequest) GetVision() string {
	if m != nil {
		return m.Vision
	}
	return ""
}

type ApplyCreateSocialCommunityResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ApplyCreateSocialCommunityResponse) Reset()         { *m = ApplyCreateSocialCommunityResponse{} }
func (m *ApplyCreateSocialCommunityResponse) String() string { return proto.CompactTextString(m) }
func (*ApplyCreateSocialCommunityResponse) ProtoMessage()    {}
func (*ApplyCreateSocialCommunityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{60}
}
func (m *ApplyCreateSocialCommunityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyCreateSocialCommunityResponse.Unmarshal(m, b)
}
func (m *ApplyCreateSocialCommunityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyCreateSocialCommunityResponse.Marshal(b, m, deterministic)
}
func (dst *ApplyCreateSocialCommunityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyCreateSocialCommunityResponse.Merge(dst, src)
}
func (m *ApplyCreateSocialCommunityResponse) XXX_Size() int {
	return xxx_messageInfo_ApplyCreateSocialCommunityResponse.Size(m)
}
func (m *ApplyCreateSocialCommunityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyCreateSocialCommunityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyCreateSocialCommunityResponse proto.InternalMessageInfo

func (m *ApplyCreateSocialCommunityResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetSocialCommunityFloatRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityFloatRequest) Reset()         { *m = GetSocialCommunityFloatRequest{} }
func (m *GetSocialCommunityFloatRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityFloatRequest) ProtoMessage()    {}
func (*GetSocialCommunityFloatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{61}
}
func (m *GetSocialCommunityFloatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityFloatRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityFloatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityFloatRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityFloatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityFloatRequest.Merge(dst, src)
}
func (m *GetSocialCommunityFloatRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityFloatRequest.Size(m)
}
func (m *GetSocialCommunityFloatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityFloatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityFloatRequest proto.InternalMessageInfo

func (m *GetSocialCommunityFloatRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetSocialCommunityFloatResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Icon                 string        `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Desc                 string        `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	TempDesc             string        `protobuf:"bytes,4,opt,name=temp_desc,json=tempDesc,proto3" json:"temp_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSocialCommunityFloatResponse) Reset()         { *m = GetSocialCommunityFloatResponse{} }
func (m *GetSocialCommunityFloatResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityFloatResponse) ProtoMessage()    {}
func (*GetSocialCommunityFloatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{62}
}
func (m *GetSocialCommunityFloatResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityFloatResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityFloatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityFloatResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityFloatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityFloatResponse.Merge(dst, src)
}
func (m *GetSocialCommunityFloatResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityFloatResponse.Size(m)
}
func (m *GetSocialCommunityFloatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityFloatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityFloatResponse proto.InternalMessageInfo

func (m *GetSocialCommunityFloatResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityFloatResponse) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetSocialCommunityFloatResponse) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GetSocialCommunityFloatResponse) GetTempDesc() string {
	if m != nil {
		return m.TempDesc
	}
	return ""
}

type JoinSocialCommunityRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *JoinSocialCommunityRequest) Reset()         { *m = JoinSocialCommunityRequest{} }
func (m *JoinSocialCommunityRequest) String() string { return proto.CompactTextString(m) }
func (*JoinSocialCommunityRequest) ProtoMessage()    {}
func (*JoinSocialCommunityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{63}
}
func (m *JoinSocialCommunityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinSocialCommunityRequest.Unmarshal(m, b)
}
func (m *JoinSocialCommunityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinSocialCommunityRequest.Marshal(b, m, deterministic)
}
func (dst *JoinSocialCommunityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinSocialCommunityRequest.Merge(dst, src)
}
func (m *JoinSocialCommunityRequest) XXX_Size() int {
	return xxx_messageInfo_JoinSocialCommunityRequest.Size(m)
}
func (m *JoinSocialCommunityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinSocialCommunityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JoinSocialCommunityRequest proto.InternalMessageInfo

func (m *JoinSocialCommunityRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinSocialCommunityRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type JoinSocialCommunityResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SocialCommunityId    string        `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *JoinSocialCommunityResponse) Reset()         { *m = JoinSocialCommunityResponse{} }
func (m *JoinSocialCommunityResponse) String() string { return proto.CompactTextString(m) }
func (*JoinSocialCommunityResponse) ProtoMessage()    {}
func (*JoinSocialCommunityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{64}
}
func (m *JoinSocialCommunityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinSocialCommunityResponse.Unmarshal(m, b)
}
func (m *JoinSocialCommunityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinSocialCommunityResponse.Marshal(b, m, deterministic)
}
func (dst *JoinSocialCommunityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinSocialCommunityResponse.Merge(dst, src)
}
func (m *JoinSocialCommunityResponse) XXX_Size() int {
	return xxx_messageInfo_JoinSocialCommunityResponse.Size(m)
}
func (m *JoinSocialCommunityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinSocialCommunityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JoinSocialCommunityResponse proto.InternalMessageInfo

func (m *JoinSocialCommunityResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *JoinSocialCommunityResponse) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type ValidateUserHasCreateQualificationRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ValidateUserHasCreateQualificationRequest) Reset() {
	*m = ValidateUserHasCreateQualificationRequest{}
}
func (m *ValidateUserHasCreateQualificationRequest) String() string {
	return proto.CompactTextString(m)
}
func (*ValidateUserHasCreateQualificationRequest) ProtoMessage() {}
func (*ValidateUserHasCreateQualificationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{65}
}
func (m *ValidateUserHasCreateQualificationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateUserHasCreateQualificationRequest.Unmarshal(m, b)
}
func (m *ValidateUserHasCreateQualificationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateUserHasCreateQualificationRequest.Marshal(b, m, deterministic)
}
func (dst *ValidateUserHasCreateQualificationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateUserHasCreateQualificationRequest.Merge(dst, src)
}
func (m *ValidateUserHasCreateQualificationRequest) XXX_Size() int {
	return xxx_messageInfo_ValidateUserHasCreateQualificationRequest.Size(m)
}
func (m *ValidateUserHasCreateQualificationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateUserHasCreateQualificationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateUserHasCreateQualificationRequest proto.InternalMessageInfo

func (m *ValidateUserHasCreateQualificationRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ValidateUserHasCreateQualificationResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ValidateUserHasCreateQualificationResponse) Reset() {
	*m = ValidateUserHasCreateQualificationResponse{}
}
func (m *ValidateUserHasCreateQualificationResponse) String() string {
	return proto.CompactTextString(m)
}
func (*ValidateUserHasCreateQualificationResponse) ProtoMessage() {}
func (*ValidateUserHasCreateQualificationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{66}
}
func (m *ValidateUserHasCreateQualificationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateUserHasCreateQualificationResponse.Unmarshal(m, b)
}
func (m *ValidateUserHasCreateQualificationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateUserHasCreateQualificationResponse.Marshal(b, m, deterministic)
}
func (dst *ValidateUserHasCreateQualificationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateUserHasCreateQualificationResponse.Merge(dst, src)
}
func (m *ValidateUserHasCreateQualificationResponse) XXX_Size() int {
	return xxx_messageInfo_ValidateUserHasCreateQualificationResponse.Size(m)
}
func (m *ValidateUserHasCreateQualificationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateUserHasCreateQualificationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateUserHasCreateQualificationResponse proto.InternalMessageInfo

func (m *ValidateUserHasCreateQualificationResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 2.4.1 获取社群基本信息接口
type GetSocialCommunityBaseRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GroupId              uint32       `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityBaseRequest) Reset()         { *m = GetSocialCommunityBaseRequest{} }
func (m *GetSocialCommunityBaseRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityBaseRequest) ProtoMessage()    {}
func (*GetSocialCommunityBaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{67}
}
func (m *GetSocialCommunityBaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityBaseRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityBaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityBaseRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityBaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityBaseRequest.Merge(dst, src)
}
func (m *GetSocialCommunityBaseRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityBaseRequest.Size(m)
}
func (m *GetSocialCommunityBaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityBaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityBaseRequest proto.InternalMessageInfo

func (m *GetSocialCommunityBaseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialCommunityBaseRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetSocialCommunityBaseRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetSocialCommunityBaseResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SocialCommunityId    string        `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	Level                uint32        `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	LevelLogo            string        `protobuf:"bytes,4,opt,name=level_logo,json=levelLogo,proto3" json:"level_logo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSocialCommunityBaseResponse) Reset()         { *m = GetSocialCommunityBaseResponse{} }
func (m *GetSocialCommunityBaseResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityBaseResponse) ProtoMessage()    {}
func (*GetSocialCommunityBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{68}
}
func (m *GetSocialCommunityBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityBaseResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityBaseResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityBaseResponse.Merge(dst, src)
}
func (m *GetSocialCommunityBaseResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityBaseResponse.Size(m)
}
func (m *GetSocialCommunityBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityBaseResponse proto.InternalMessageInfo

func (m *GetSocialCommunityBaseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityBaseResponse) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *GetSocialCommunityBaseResponse) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetSocialCommunityBaseResponse) GetLevelLogo() string {
	if m != nil {
		return m.LevelLogo
	}
	return ""
}

// 2.4.2 获取用户当前社群群聊Id
type GetUserSocialGroupIdsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	GroupId              uint32       `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserSocialGroupIdsRequest) Reset()         { *m = GetUserSocialGroupIdsRequest{} }
func (m *GetUserSocialGroupIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserSocialGroupIdsRequest) ProtoMessage()    {}
func (*GetUserSocialGroupIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{69}
}
func (m *GetUserSocialGroupIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSocialGroupIdsRequest.Unmarshal(m, b)
}
func (m *GetUserSocialGroupIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSocialGroupIdsRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserSocialGroupIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSocialGroupIdsRequest.Merge(dst, src)
}
func (m *GetUserSocialGroupIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserSocialGroupIdsRequest.Size(m)
}
func (m *GetUserSocialGroupIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSocialGroupIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSocialGroupIdsRequest proto.InternalMessageInfo

func (m *GetUserSocialGroupIdsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserSocialGroupIdsRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *GetUserSocialGroupIdsRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetUserSocialGroupIdsResponse struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	JoinGroups           []*SocialSimpleGroup `protobuf:"bytes,2,rep,name=join_groups,json=joinGroups,proto3" json:"join_groups,omitempty"`
	CurrentGroup         *SocialSimpleGroup   `protobuf:"bytes,3,opt,name=current_group,json=currentGroup,proto3" json:"current_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetUserSocialGroupIdsResponse) Reset()         { *m = GetUserSocialGroupIdsResponse{} }
func (m *GetUserSocialGroupIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserSocialGroupIdsResponse) ProtoMessage()    {}
func (*GetUserSocialGroupIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{70}
}
func (m *GetUserSocialGroupIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSocialGroupIdsResponse.Unmarshal(m, b)
}
func (m *GetUserSocialGroupIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSocialGroupIdsResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserSocialGroupIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSocialGroupIdsResponse.Merge(dst, src)
}
func (m *GetUserSocialGroupIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserSocialGroupIdsResponse.Size(m)
}
func (m *GetUserSocialGroupIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSocialGroupIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSocialGroupIdsResponse proto.InternalMessageInfo

func (m *GetUserSocialGroupIdsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserSocialGroupIdsResponse) GetJoinGroups() []*SocialSimpleGroup {
	if m != nil {
		return m.JoinGroups
	}
	return nil
}

func (m *GetUserSocialGroupIdsResponse) GetCurrentGroup() *SocialSimpleGroup {
	if m != nil {
		return m.CurrentGroup
	}
	return nil
}

type SocialSimpleGroup struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupAccount         string   `protobuf:"bytes,2,opt,name=group_account,json=groupAccount,proto3" json:"group_account,omitempty"`
	GroupName            string   `protobuf:"bytes,3,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	GroupType            uint32   `protobuf:"varint,4,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialSimpleGroup) Reset()         { *m = SocialSimpleGroup{} }
func (m *SocialSimpleGroup) String() string { return proto.CompactTextString(m) }
func (*SocialSimpleGroup) ProtoMessage()    {}
func (*SocialSimpleGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{71}
}
func (m *SocialSimpleGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialSimpleGroup.Unmarshal(m, b)
}
func (m *SocialSimpleGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialSimpleGroup.Marshal(b, m, deterministic)
}
func (dst *SocialSimpleGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialSimpleGroup.Merge(dst, src)
}
func (m *SocialSimpleGroup) XXX_Size() int {
	return xxx_messageInfo_SocialSimpleGroup.Size(m)
}
func (m *SocialSimpleGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialSimpleGroup.DiscardUnknown(m)
}

var xxx_messageInfo_SocialSimpleGroup proto.InternalMessageInfo

func (m *SocialSimpleGroup) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *SocialSimpleGroup) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

func (m *SocialSimpleGroup) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *SocialSimpleGroup) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

// 2.4.3 获取群预览信息
type MuseSocialPreviewGroupMessageRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseSocialPreviewGroupMessageRequest) Reset()         { *m = MuseSocialPreviewGroupMessageRequest{} }
func (m *MuseSocialPreviewGroupMessageRequest) String() string { return proto.CompactTextString(m) }
func (*MuseSocialPreviewGroupMessageRequest) ProtoMessage()    {}
func (*MuseSocialPreviewGroupMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{72}
}
func (m *MuseSocialPreviewGroupMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialPreviewGroupMessageRequest.Unmarshal(m, b)
}
func (m *MuseSocialPreviewGroupMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialPreviewGroupMessageRequest.Marshal(b, m, deterministic)
}
func (dst *MuseSocialPreviewGroupMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialPreviewGroupMessageRequest.Merge(dst, src)
}
func (m *MuseSocialPreviewGroupMessageRequest) XXX_Size() int {
	return xxx_messageInfo_MuseSocialPreviewGroupMessageRequest.Size(m)
}
func (m *MuseSocialPreviewGroupMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialPreviewGroupMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialPreviewGroupMessageRequest proto.InternalMessageInfo

func (m *MuseSocialPreviewGroupMessageRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseSocialPreviewGroupMessageRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type MuseSocialPreviewGroupMessageResponse struct {
	BaseResp               *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Message                []*sync.NewMessageSync `protobuf:"bytes,2,rep,name=message,proto3" json:"message,omitempty"`
	GroupId                uint32                 `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	SocialCommunityId      string                 `protobuf:"bytes,4,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	AdminAccounts          []string               `protobuf:"bytes,5,rep,name=admin_accounts,json=adminAccounts,proto3" json:"admin_accounts,omitempty"`
	GroupOwnerAccount      string                 `protobuf:"bytes,6,opt,name=group_owner_account,json=groupOwnerAccount,proto3" json:"group_owner_account,omitempty"`
	Members                []*BrandMember         `protobuf:"bytes,7,rep,name=members,proto3" json:"members,omitempty"`
	CategoryTypeSimpleDesc string                 `protobuf:"bytes,8,opt,name=category_type_simple_desc,json=categoryTypeSimpleDesc,proto3" json:"category_type_simple_desc,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}               `json:"-"`
	XXX_unrecognized       []byte                 `json:"-"`
	XXX_sizecache          int32                  `json:"-"`
}

func (m *MuseSocialPreviewGroupMessageResponse) Reset()         { *m = MuseSocialPreviewGroupMessageResponse{} }
func (m *MuseSocialPreviewGroupMessageResponse) String() string { return proto.CompactTextString(m) }
func (*MuseSocialPreviewGroupMessageResponse) ProtoMessage()    {}
func (*MuseSocialPreviewGroupMessageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{73}
}
func (m *MuseSocialPreviewGroupMessageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialPreviewGroupMessageResponse.Unmarshal(m, b)
}
func (m *MuseSocialPreviewGroupMessageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialPreviewGroupMessageResponse.Marshal(b, m, deterministic)
}
func (dst *MuseSocialPreviewGroupMessageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialPreviewGroupMessageResponse.Merge(dst, src)
}
func (m *MuseSocialPreviewGroupMessageResponse) XXX_Size() int {
	return xxx_messageInfo_MuseSocialPreviewGroupMessageResponse.Size(m)
}
func (m *MuseSocialPreviewGroupMessageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialPreviewGroupMessageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialPreviewGroupMessageResponse proto.InternalMessageInfo

func (m *MuseSocialPreviewGroupMessageResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseSocialPreviewGroupMessageResponse) GetMessage() []*sync.NewMessageSync {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *MuseSocialPreviewGroupMessageResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialPreviewGroupMessageResponse) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *MuseSocialPreviewGroupMessageResponse) GetAdminAccounts() []string {
	if m != nil {
		return m.AdminAccounts
	}
	return nil
}

func (m *MuseSocialPreviewGroupMessageResponse) GetGroupOwnerAccount() string {
	if m != nil {
		return m.GroupOwnerAccount
	}
	return ""
}

func (m *MuseSocialPreviewGroupMessageResponse) GetMembers() []*BrandMember {
	if m != nil {
		return m.Members
	}
	return nil
}

func (m *MuseSocialPreviewGroupMessageResponse) GetCategoryTypeSimpleDesc() string {
	if m != nil {
		return m.CategoryTypeSimpleDesc
	}
	return ""
}

// 移除管理员
type MuseSocialGroupRemoveAdminRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseSocialGroupRemoveAdminRequest) Reset()         { *m = MuseSocialGroupRemoveAdminRequest{} }
func (m *MuseSocialGroupRemoveAdminRequest) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupRemoveAdminRequest) ProtoMessage()    {}
func (*MuseSocialGroupRemoveAdminRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{74}
}
func (m *MuseSocialGroupRemoveAdminRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupRemoveAdminRequest.Unmarshal(m, b)
}
func (m *MuseSocialGroupRemoveAdminRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupRemoveAdminRequest.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupRemoveAdminRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupRemoveAdminRequest.Merge(dst, src)
}
func (m *MuseSocialGroupRemoveAdminRequest) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupRemoveAdminRequest.Size(m)
}
func (m *MuseSocialGroupRemoveAdminRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupRemoveAdminRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupRemoveAdminRequest proto.InternalMessageInfo

func (m *MuseSocialGroupRemoveAdminRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseSocialGroupRemoveAdminRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupRemoveAdminRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MuseSocialGroupRemoveAdminResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GroupId              uint32        `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseSocialGroupRemoveAdminResponse) Reset()         { *m = MuseSocialGroupRemoveAdminResponse{} }
func (m *MuseSocialGroupRemoveAdminResponse) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupRemoveAdminResponse) ProtoMessage()    {}
func (*MuseSocialGroupRemoveAdminResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{75}
}
func (m *MuseSocialGroupRemoveAdminResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupRemoveAdminResponse.Unmarshal(m, b)
}
func (m *MuseSocialGroupRemoveAdminResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupRemoveAdminResponse.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupRemoveAdminResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupRemoveAdminResponse.Merge(dst, src)
}
func (m *MuseSocialGroupRemoveAdminResponse) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupRemoveAdminResponse.Size(m)
}
func (m *MuseSocialGroupRemoveAdminResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupRemoveAdminResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupRemoveAdminResponse proto.InternalMessageInfo

func (m *MuseSocialGroupRemoveAdminResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseSocialGroupRemoveAdminResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

// 全部禁言
type MuseSocialGroupSetAllMuteRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	IsAllMute            bool         `protobuf:"varint,3,opt,name=is_all_mute,json=isAllMute,proto3" json:"is_all_mute,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseSocialGroupSetAllMuteRequest) Reset()         { *m = MuseSocialGroupSetAllMuteRequest{} }
func (m *MuseSocialGroupSetAllMuteRequest) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupSetAllMuteRequest) ProtoMessage()    {}
func (*MuseSocialGroupSetAllMuteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{76}
}
func (m *MuseSocialGroupSetAllMuteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupSetAllMuteRequest.Unmarshal(m, b)
}
func (m *MuseSocialGroupSetAllMuteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupSetAllMuteRequest.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupSetAllMuteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupSetAllMuteRequest.Merge(dst, src)
}
func (m *MuseSocialGroupSetAllMuteRequest) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupSetAllMuteRequest.Size(m)
}
func (m *MuseSocialGroupSetAllMuteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupSetAllMuteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupSetAllMuteRequest proto.InternalMessageInfo

func (m *MuseSocialGroupSetAllMuteRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseSocialGroupSetAllMuteRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupSetAllMuteRequest) GetIsAllMute() bool {
	if m != nil {
		return m.IsAllMute
	}
	return false
}

type MuseSocialGroupSetAllMuteResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GroupId              uint32        `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	IsAllMute            bool          `protobuf:"varint,3,opt,name=is_all_mute,json=isAllMute,proto3" json:"is_all_mute,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseSocialGroupSetAllMuteResponse) Reset()         { *m = MuseSocialGroupSetAllMuteResponse{} }
func (m *MuseSocialGroupSetAllMuteResponse) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupSetAllMuteResponse) ProtoMessage()    {}
func (*MuseSocialGroupSetAllMuteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{77}
}
func (m *MuseSocialGroupSetAllMuteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupSetAllMuteResponse.Unmarshal(m, b)
}
func (m *MuseSocialGroupSetAllMuteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupSetAllMuteResponse.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupSetAllMuteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupSetAllMuteResponse.Merge(dst, src)
}
func (m *MuseSocialGroupSetAllMuteResponse) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupSetAllMuteResponse.Size(m)
}
func (m *MuseSocialGroupSetAllMuteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupSetAllMuteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupSetAllMuteResponse proto.InternalMessageInfo

func (m *MuseSocialGroupSetAllMuteResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseSocialGroupSetAllMuteResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupSetAllMuteResponse) GetIsAllMute() bool {
	if m != nil {
		return m.IsAllMute
	}
	return false
}

type MuseSocialGroupMuteMemberRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uids                 []uint32     `protobuf:"varint,3,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseSocialGroupMuteMemberRequest) Reset()         { *m = MuseSocialGroupMuteMemberRequest{} }
func (m *MuseSocialGroupMuteMemberRequest) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupMuteMemberRequest) ProtoMessage()    {}
func (*MuseSocialGroupMuteMemberRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{78}
}
func (m *MuseSocialGroupMuteMemberRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupMuteMemberRequest.Unmarshal(m, b)
}
func (m *MuseSocialGroupMuteMemberRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupMuteMemberRequest.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupMuteMemberRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupMuteMemberRequest.Merge(dst, src)
}
func (m *MuseSocialGroupMuteMemberRequest) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupMuteMemberRequest.Size(m)
}
func (m *MuseSocialGroupMuteMemberRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupMuteMemberRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupMuteMemberRequest proto.InternalMessageInfo

func (m *MuseSocialGroupMuteMemberRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseSocialGroupMuteMemberRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupMuteMemberRequest) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type MuseSocialGroupMuteMemberResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GroupId              uint32        `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uids                 []uint32      `protobuf:"varint,3,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseSocialGroupMuteMemberResponse) Reset()         { *m = MuseSocialGroupMuteMemberResponse{} }
func (m *MuseSocialGroupMuteMemberResponse) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupMuteMemberResponse) ProtoMessage()    {}
func (*MuseSocialGroupMuteMemberResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{79}
}
func (m *MuseSocialGroupMuteMemberResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupMuteMemberResponse.Unmarshal(m, b)
}
func (m *MuseSocialGroupMuteMemberResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupMuteMemberResponse.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupMuteMemberResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupMuteMemberResponse.Merge(dst, src)
}
func (m *MuseSocialGroupMuteMemberResponse) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupMuteMemberResponse.Size(m)
}
func (m *MuseSocialGroupMuteMemberResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupMuteMemberResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupMuteMemberResponse proto.InternalMessageInfo

func (m *MuseSocialGroupMuteMemberResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseSocialGroupMuteMemberResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupMuteMemberResponse) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type MuseSocialGroupUnmuteMemberRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uids                 []uint32     `protobuf:"varint,3,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseSocialGroupUnmuteMemberRequest) Reset()         { *m = MuseSocialGroupUnmuteMemberRequest{} }
func (m *MuseSocialGroupUnmuteMemberRequest) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupUnmuteMemberRequest) ProtoMessage()    {}
func (*MuseSocialGroupUnmuteMemberRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{80}
}
func (m *MuseSocialGroupUnmuteMemberRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupUnmuteMemberRequest.Unmarshal(m, b)
}
func (m *MuseSocialGroupUnmuteMemberRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupUnmuteMemberRequest.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupUnmuteMemberRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupUnmuteMemberRequest.Merge(dst, src)
}
func (m *MuseSocialGroupUnmuteMemberRequest) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupUnmuteMemberRequest.Size(m)
}
func (m *MuseSocialGroupUnmuteMemberRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupUnmuteMemberRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupUnmuteMemberRequest proto.InternalMessageInfo

func (m *MuseSocialGroupUnmuteMemberRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseSocialGroupUnmuteMemberRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupUnmuteMemberRequest) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type MuseSocialGroupUnmuteMemberResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GroupId              uint32        `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uids                 []uint32      `protobuf:"varint,3,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseSocialGroupUnmuteMemberResponse) Reset()         { *m = MuseSocialGroupUnmuteMemberResponse{} }
func (m *MuseSocialGroupUnmuteMemberResponse) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupUnmuteMemberResponse) ProtoMessage()    {}
func (*MuseSocialGroupUnmuteMemberResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{81}
}
func (m *MuseSocialGroupUnmuteMemberResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupUnmuteMemberResponse.Unmarshal(m, b)
}
func (m *MuseSocialGroupUnmuteMemberResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupUnmuteMemberResponse.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupUnmuteMemberResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupUnmuteMemberResponse.Merge(dst, src)
}
func (m *MuseSocialGroupUnmuteMemberResponse) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupUnmuteMemberResponse.Size(m)
}
func (m *MuseSocialGroupUnmuteMemberResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupUnmuteMemberResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupUnmuteMemberResponse proto.InternalMessageInfo

func (m *MuseSocialGroupUnmuteMemberResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseSocialGroupUnmuteMemberResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupUnmuteMemberResponse) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

// 禁言列表
type MuseSocialGroupGetMuteListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GetCount             bool         `protobuf:"varint,3,opt,name=get_count,json=getCount,proto3" json:"get_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseSocialGroupGetMuteListRequest) Reset()         { *m = MuseSocialGroupGetMuteListRequest{} }
func (m *MuseSocialGroupGetMuteListRequest) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupGetMuteListRequest) ProtoMessage()    {}
func (*MuseSocialGroupGetMuteListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{82}
}
func (m *MuseSocialGroupGetMuteListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupGetMuteListRequest.Unmarshal(m, b)
}
func (m *MuseSocialGroupGetMuteListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupGetMuteListRequest.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupGetMuteListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupGetMuteListRequest.Merge(dst, src)
}
func (m *MuseSocialGroupGetMuteListRequest) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupGetMuteListRequest.Size(m)
}
func (m *MuseSocialGroupGetMuteListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupGetMuteListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupGetMuteListRequest proto.InternalMessageInfo

func (m *MuseSocialGroupGetMuteListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseSocialGroupGetMuteListRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupGetMuteListRequest) GetGetCount() bool {
	if m != nil {
		return m.GetCount
	}
	return false
}

type MuseSocialMuteUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseSocialMuteUserInfo) Reset()         { *m = MuseSocialMuteUserInfo{} }
func (m *MuseSocialMuteUserInfo) String() string { return proto.CompactTextString(m) }
func (*MuseSocialMuteUserInfo) ProtoMessage()    {}
func (*MuseSocialMuteUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{83}
}
func (m *MuseSocialMuteUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialMuteUserInfo.Unmarshal(m, b)
}
func (m *MuseSocialMuteUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialMuteUserInfo.Marshal(b, m, deterministic)
}
func (dst *MuseSocialMuteUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialMuteUserInfo.Merge(dst, src)
}
func (m *MuseSocialMuteUserInfo) XXX_Size() int {
	return xxx_messageInfo_MuseSocialMuteUserInfo.Size(m)
}
func (m *MuseSocialMuteUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialMuteUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialMuteUserInfo proto.InternalMessageInfo

func (m *MuseSocialMuteUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MuseSocialMuteUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MuseSocialMuteUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type MuseSocialGroupGetMuteListResponse struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GroupId              uint32                    `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	MuteList             []*MuseSocialMuteUserInfo `protobuf:"bytes,3,rep,name=mute_list,json=muteList,proto3" json:"mute_list,omitempty"`
	Count                uint32                    `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *MuseSocialGroupGetMuteListResponse) Reset()         { *m = MuseSocialGroupGetMuteListResponse{} }
func (m *MuseSocialGroupGetMuteListResponse) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupGetMuteListResponse) ProtoMessage()    {}
func (*MuseSocialGroupGetMuteListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{84}
}
func (m *MuseSocialGroupGetMuteListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupGetMuteListResponse.Unmarshal(m, b)
}
func (m *MuseSocialGroupGetMuteListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupGetMuteListResponse.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupGetMuteListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupGetMuteListResponse.Merge(dst, src)
}
func (m *MuseSocialGroupGetMuteListResponse) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupGetMuteListResponse.Size(m)
}
func (m *MuseSocialGroupGetMuteListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupGetMuteListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupGetMuteListResponse proto.InternalMessageInfo

func (m *MuseSocialGroupGetMuteListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseSocialGroupGetMuteListResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupGetMuteListResponse) GetMuteList() []*MuseSocialMuteUserInfo {
	if m != nil {
		return m.MuteList
	}
	return nil
}

func (m *MuseSocialGroupGetMuteListResponse) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 成员列表
// 获取群成员列表 BEGIN
type MuseSocialGroupGetMemberListRequest struct {
	BaseReq              *app.BaseReq     `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32           `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	LoadMore             *LoadMoreMembers `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32           `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	AdminOnly            bool             `protobuf:"varint,5,opt,name=admin_only,json=adminOnly,proto3" json:"admin_only,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MuseSocialGroupGetMemberListRequest) Reset()         { *m = MuseSocialGroupGetMemberListRequest{} }
func (m *MuseSocialGroupGetMemberListRequest) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupGetMemberListRequest) ProtoMessage()    {}
func (*MuseSocialGroupGetMemberListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{85}
}
func (m *MuseSocialGroupGetMemberListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupGetMemberListRequest.Unmarshal(m, b)
}
func (m *MuseSocialGroupGetMemberListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupGetMemberListRequest.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupGetMemberListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupGetMemberListRequest.Merge(dst, src)
}
func (m *MuseSocialGroupGetMemberListRequest) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupGetMemberListRequest.Size(m)
}
func (m *MuseSocialGroupGetMemberListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupGetMemberListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupGetMemberListRequest proto.InternalMessageInfo

func (m *MuseSocialGroupGetMemberListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseSocialGroupGetMemberListRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupGetMemberListRequest) GetLoadMore() *LoadMoreMembers {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *MuseSocialGroupGetMemberListRequest) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *MuseSocialGroupGetMemberListRequest) GetAdminOnly() bool {
	if m != nil {
		return m.AdminOnly
	}
	return false
}

type LoadMoreMembers struct {
	NextPosition         int32    `protobuf:"varint,1,opt,name=next_position,json=nextPosition,proto3" json:"next_position,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoadMoreMembers) Reset()         { *m = LoadMoreMembers{} }
func (m *LoadMoreMembers) String() string { return proto.CompactTextString(m) }
func (*LoadMoreMembers) ProtoMessage()    {}
func (*LoadMoreMembers) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{86}
}
func (m *LoadMoreMembers) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoadMoreMembers.Unmarshal(m, b)
}
func (m *LoadMoreMembers) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoadMoreMembers.Marshal(b, m, deterministic)
}
func (dst *LoadMoreMembers) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoadMoreMembers.Merge(dst, src)
}
func (m *LoadMoreMembers) XXX_Size() int {
	return xxx_messageInfo_LoadMoreMembers.Size(m)
}
func (m *LoadMoreMembers) XXX_DiscardUnknown() {
	xxx_messageInfo_LoadMoreMembers.DiscardUnknown(m)
}

var xxx_messageInfo_LoadMoreMembers proto.InternalMessageInfo

func (m *LoadMoreMembers) GetNextPosition() int32 {
	if m != nil {
		return m.NextPosition
	}
	return 0
}

type MuseSocialGroupGetMemberListResponse struct {
	BaseResp             *app.BaseResp                `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GroupAccount         string                       `protobuf:"bytes,2,opt,name=group_account,json=groupAccount,proto3" json:"group_account,omitempty"`
	Members              []*MuseSocialGroupMemberInfo `protobuf:"bytes,3,rep,name=members,proto3" json:"members,omitempty"`
	LoadMore             *LoadMoreMembers             `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *MuseSocialGroupGetMemberListResponse) Reset()         { *m = MuseSocialGroupGetMemberListResponse{} }
func (m *MuseSocialGroupGetMemberListResponse) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupGetMemberListResponse) ProtoMessage()    {}
func (*MuseSocialGroupGetMemberListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{87}
}
func (m *MuseSocialGroupGetMemberListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupGetMemberListResponse.Unmarshal(m, b)
}
func (m *MuseSocialGroupGetMemberListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupGetMemberListResponse.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupGetMemberListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupGetMemberListResponse.Merge(dst, src)
}
func (m *MuseSocialGroupGetMemberListResponse) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupGetMemberListResponse.Size(m)
}
func (m *MuseSocialGroupGetMemberListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupGetMemberListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupGetMemberListResponse proto.InternalMessageInfo

func (m *MuseSocialGroupGetMemberListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseSocialGroupGetMemberListResponse) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

func (m *MuseSocialGroupGetMemberListResponse) GetMembers() []*MuseSocialGroupMemberInfo {
	if m != nil {
		return m.Members
	}
	return nil
}

func (m *MuseSocialGroupGetMemberListResponse) GetLoadMore() *LoadMoreMembers {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type MuseSocialGroupMemberInfo struct {
	Account              string       `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Sex                  uint32       `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	UserNick             string       `protobuf:"bytes,3,opt,name=user_nick,json=userNick,proto3" json:"user_nick,omitempty"`
	Role                 uint32       `protobuf:"varint,4,opt,name=role,proto3" json:"role,omitempty"`
	FaceMd5              string       `protobuf:"bytes,5,opt,name=face_md5,json=faceMd5,proto3" json:"face_md5,omitempty"`
	IsMuted              bool         `protobuf:"varint,6,opt,name=is_muted,json=isMuted,proto3" json:"is_muted,omitempty"`
	OnlineStatus         uint32       `protobuf:"varint,7,opt,name=online_status,json=onlineStatus,proto3" json:"online_status,omitempty"`
	OnlineStatusTs       uint32       `protobuf:"varint,8,opt,name=online_status_ts,json=onlineStatusTs,proto3" json:"online_status_ts,omitempty"`
	BrandMember          *BrandMember `protobuf:"bytes,9,opt,name=brand_member,json=brandMember,proto3" json:"brand_member,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseSocialGroupMemberInfo) Reset()         { *m = MuseSocialGroupMemberInfo{} }
func (m *MuseSocialGroupMemberInfo) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupMemberInfo) ProtoMessage()    {}
func (*MuseSocialGroupMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{88}
}
func (m *MuseSocialGroupMemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupMemberInfo.Unmarshal(m, b)
}
func (m *MuseSocialGroupMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupMemberInfo.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupMemberInfo.Merge(dst, src)
}
func (m *MuseSocialGroupMemberInfo) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupMemberInfo.Size(m)
}
func (m *MuseSocialGroupMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupMemberInfo proto.InternalMessageInfo

func (m *MuseSocialGroupMemberInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MuseSocialGroupMemberInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *MuseSocialGroupMemberInfo) GetUserNick() string {
	if m != nil {
		return m.UserNick
	}
	return ""
}

func (m *MuseSocialGroupMemberInfo) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *MuseSocialGroupMemberInfo) GetFaceMd5() string {
	if m != nil {
		return m.FaceMd5
	}
	return ""
}

func (m *MuseSocialGroupMemberInfo) GetIsMuted() bool {
	if m != nil {
		return m.IsMuted
	}
	return false
}

func (m *MuseSocialGroupMemberInfo) GetOnlineStatus() uint32 {
	if m != nil {
		return m.OnlineStatus
	}
	return 0
}

func (m *MuseSocialGroupMemberInfo) GetOnlineStatusTs() uint32 {
	if m != nil {
		return m.OnlineStatusTs
	}
	return 0
}

func (m *MuseSocialGroupMemberInfo) GetBrandMember() *BrandMember {
	if m != nil {
		return m.BrandMember
	}
	return nil
}

// 查群组详情
type MuseSocialGroupGetDetailInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseSocialGroupGetDetailInfoRequest) Reset()         { *m = MuseSocialGroupGetDetailInfoRequest{} }
func (m *MuseSocialGroupGetDetailInfoRequest) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupGetDetailInfoRequest) ProtoMessage()    {}
func (*MuseSocialGroupGetDetailInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{89}
}
func (m *MuseSocialGroupGetDetailInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupGetDetailInfoRequest.Unmarshal(m, b)
}
func (m *MuseSocialGroupGetDetailInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupGetDetailInfoRequest.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupGetDetailInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupGetDetailInfoRequest.Merge(dst, src)
}
func (m *MuseSocialGroupGetDetailInfoRequest) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupGetDetailInfoRequest.Size(m)
}
func (m *MuseSocialGroupGetDetailInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupGetDetailInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupGetDetailInfoRequest proto.InternalMessageInfo

func (m *MuseSocialGroupGetDetailInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseSocialGroupGetDetailInfoRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type MuseSocialGroupGetDetailInfoResponse struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DetailInfo           *MuseSocialGroupDetailInfo `protobuf:"bytes,2,opt,name=detail_info,json=detailInfo,proto3" json:"detail_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *MuseSocialGroupGetDetailInfoResponse) Reset()         { *m = MuseSocialGroupGetDetailInfoResponse{} }
func (m *MuseSocialGroupGetDetailInfoResponse) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupGetDetailInfoResponse) ProtoMessage()    {}
func (*MuseSocialGroupGetDetailInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{90}
}
func (m *MuseSocialGroupGetDetailInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupGetDetailInfoResponse.Unmarshal(m, b)
}
func (m *MuseSocialGroupGetDetailInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupGetDetailInfoResponse.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupGetDetailInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupGetDetailInfoResponse.Merge(dst, src)
}
func (m *MuseSocialGroupGetDetailInfoResponse) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupGetDetailInfoResponse.Size(m)
}
func (m *MuseSocialGroupGetDetailInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupGetDetailInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupGetDetailInfoResponse proto.InternalMessageInfo

func (m *MuseSocialGroupGetDetailInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseSocialGroupGetDetailInfoResponse) GetDetailInfo() *MuseSocialGroupDetailInfo {
	if m != nil {
		return m.DetailInfo
	}
	return nil
}

type MuseSocialGroupDetailInfo struct {
	GroupId              uint32            `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupName            string            `protobuf:"bytes,3,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	GroupMemCount        uint32            `protobuf:"varint,4,opt,name=group_mem_count,json=groupMemCount,proto3" json:"group_mem_count,omitempty"`
	GroupMemCountLimit   uint32            `protobuf:"varint,5,opt,name=group_mem_count_limit,json=groupMemCountLimit,proto3" json:"group_mem_count_limit,omitempty"`
	PortraitMd5          string            `protobuf:"bytes,6,opt,name=portrait_md5,json=portraitMd5,proto3" json:"portrait_md5,omitempty"`
	CreateTime           uint32            `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	GroupType            uint32            `protobuf:"varint,8,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	GroupLeader          *GroupLeader      `protobuf:"bytes,9,opt,name=group_leader,json=groupLeader,proto3" json:"group_leader,omitempty"`
	Share                *Share            `protobuf:"bytes,10,opt,name=share,proto3" json:"share,omitempty"`
	GroupNumber          uint32            `protobuf:"varint,11,opt,name=group_number,json=groupNumber,proto3" json:"group_number,omitempty"`
	Users                []*SimpleUserInfo `protobuf:"bytes,12,rep,name=users,proto3" json:"users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MuseSocialGroupDetailInfo) Reset()         { *m = MuseSocialGroupDetailInfo{} }
func (m *MuseSocialGroupDetailInfo) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupDetailInfo) ProtoMessage()    {}
func (*MuseSocialGroupDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{91}
}
func (m *MuseSocialGroupDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupDetailInfo.Unmarshal(m, b)
}
func (m *MuseSocialGroupDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupDetailInfo.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupDetailInfo.Merge(dst, src)
}
func (m *MuseSocialGroupDetailInfo) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupDetailInfo.Size(m)
}
func (m *MuseSocialGroupDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupDetailInfo proto.InternalMessageInfo

func (m *MuseSocialGroupDetailInfo) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupDetailInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *MuseSocialGroupDetailInfo) GetGroupMemCount() uint32 {
	if m != nil {
		return m.GroupMemCount
	}
	return 0
}

func (m *MuseSocialGroupDetailInfo) GetGroupMemCountLimit() uint32 {
	if m != nil {
		return m.GroupMemCountLimit
	}
	return 0
}

func (m *MuseSocialGroupDetailInfo) GetPortraitMd5() string {
	if m != nil {
		return m.PortraitMd5
	}
	return ""
}

func (m *MuseSocialGroupDetailInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *MuseSocialGroupDetailInfo) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *MuseSocialGroupDetailInfo) GetGroupLeader() *GroupLeader {
	if m != nil {
		return m.GroupLeader
	}
	return nil
}

func (m *MuseSocialGroupDetailInfo) GetShare() *Share {
	if m != nil {
		return m.Share
	}
	return nil
}

func (m *MuseSocialGroupDetailInfo) GetGroupNumber() uint32 {
	if m != nil {
		return m.GroupNumber
	}
	return 0
}

func (m *MuseSocialGroupDetailInfo) GetUsers() []*SimpleUserInfo {
	if m != nil {
		return m.Users
	}
	return nil
}

type Share struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SocialCommunityName  string   `protobuf:"bytes,2,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	SocialCommunityLogo  string   `protobuf:"bytes,3,opt,name=social_community_logo,json=socialCommunityLogo,proto3" json:"social_community_logo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Share) Reset()         { *m = Share{} }
func (m *Share) String() string { return proto.CompactTextString(m) }
func (*Share) ProtoMessage()    {}
func (*Share) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{92}
}
func (m *Share) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Share.Unmarshal(m, b)
}
func (m *Share) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Share.Marshal(b, m, deterministic)
}
func (dst *Share) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Share.Merge(dst, src)
}
func (m *Share) XXX_Size() int {
	return xxx_messageInfo_Share.Size(m)
}
func (m *Share) XXX_DiscardUnknown() {
	xxx_messageInfo_Share.DiscardUnknown(m)
}

var xxx_messageInfo_Share proto.InternalMessageInfo

func (m *Share) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *Share) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *Share) GetSocialCommunityLogo() string {
	if m != nil {
		return m.SocialCommunityLogo
	}
	return ""
}

type GroupLeader struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	SocialCommunityName  string   `protobuf:"bytes,4,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GroupLeader) Reset()         { *m = GroupLeader{} }
func (m *GroupLeader) String() string { return proto.CompactTextString(m) }
func (*GroupLeader) ProtoMessage()    {}
func (*GroupLeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{93}
}
func (m *GroupLeader) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupLeader.Unmarshal(m, b)
}
func (m *GroupLeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupLeader.Marshal(b, m, deterministic)
}
func (dst *GroupLeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupLeader.Merge(dst, src)
}
func (m *GroupLeader) XXX_Size() int {
	return xxx_messageInfo_GroupLeader.Size(m)
}
func (m *GroupLeader) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupLeader.DiscardUnknown(m)
}

var xxx_messageInfo_GroupLeader proto.InternalMessageInfo

func (m *GroupLeader) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GroupLeader) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GroupLeader) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *GroupLeader) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

type MuseSocialGroupAddAdminRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseSocialGroupAddAdminRequest) Reset()         { *m = MuseSocialGroupAddAdminRequest{} }
func (m *MuseSocialGroupAddAdminRequest) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupAddAdminRequest) ProtoMessage()    {}
func (*MuseSocialGroupAddAdminRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{94}
}
func (m *MuseSocialGroupAddAdminRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupAddAdminRequest.Unmarshal(m, b)
}
func (m *MuseSocialGroupAddAdminRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupAddAdminRequest.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupAddAdminRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupAddAdminRequest.Merge(dst, src)
}
func (m *MuseSocialGroupAddAdminRequest) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupAddAdminRequest.Size(m)
}
func (m *MuseSocialGroupAddAdminRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupAddAdminRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupAddAdminRequest proto.InternalMessageInfo

func (m *MuseSocialGroupAddAdminRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseSocialGroupAddAdminRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseSocialGroupAddAdminRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MuseSocialGroupAddAdminResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GroupId              uint32        `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseSocialGroupAddAdminResponse) Reset()         { *m = MuseSocialGroupAddAdminResponse{} }
func (m *MuseSocialGroupAddAdminResponse) String() string { return proto.CompactTextString(m) }
func (*MuseSocialGroupAddAdminResponse) ProtoMessage()    {}
func (*MuseSocialGroupAddAdminResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{95}
}
func (m *MuseSocialGroupAddAdminResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialGroupAddAdminResponse.Unmarshal(m, b)
}
func (m *MuseSocialGroupAddAdminResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialGroupAddAdminResponse.Marshal(b, m, deterministic)
}
func (dst *MuseSocialGroupAddAdminResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialGroupAddAdminResponse.Merge(dst, src)
}
func (m *MuseSocialGroupAddAdminResponse) XXX_Size() int {
	return xxx_messageInfo_MuseSocialGroupAddAdminResponse.Size(m)
}
func (m *MuseSocialGroupAddAdminResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialGroupAddAdminResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialGroupAddAdminResponse proto.InternalMessageInfo

func (m *MuseSocialGroupAddAdminResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseSocialGroupAddAdminResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

// 获取群聊在线人数
type GetSocialGroupOnlineMembersRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialGroupOnlineMembersRequest) Reset()         { *m = GetSocialGroupOnlineMembersRequest{} }
func (m *GetSocialGroupOnlineMembersRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialGroupOnlineMembersRequest) ProtoMessage()    {}
func (*GetSocialGroupOnlineMembersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{96}
}
func (m *GetSocialGroupOnlineMembersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialGroupOnlineMembersRequest.Unmarshal(m, b)
}
func (m *GetSocialGroupOnlineMembersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialGroupOnlineMembersRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialGroupOnlineMembersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialGroupOnlineMembersRequest.Merge(dst, src)
}
func (m *GetSocialGroupOnlineMembersRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialGroupOnlineMembersRequest.Size(m)
}
func (m *GetSocialGroupOnlineMembersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialGroupOnlineMembersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialGroupOnlineMembersRequest proto.InternalMessageInfo

func (m *GetSocialGroupOnlineMembersRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialGroupOnlineMembersRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetSocialGroupOnlineMembersResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MemberCount          uint32        `protobuf:"varint,2,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	GroupId              uint32        `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Level                uint32        `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	LevelLogo            string        `protobuf:"bytes,5,opt,name=level_logo,json=levelLogo,proto3" json:"level_logo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSocialGroupOnlineMembersResponse) Reset()         { *m = GetSocialGroupOnlineMembersResponse{} }
func (m *GetSocialGroupOnlineMembersResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialGroupOnlineMembersResponse) ProtoMessage()    {}
func (*GetSocialGroupOnlineMembersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{97}
}
func (m *GetSocialGroupOnlineMembersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialGroupOnlineMembersResponse.Unmarshal(m, b)
}
func (m *GetSocialGroupOnlineMembersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialGroupOnlineMembersResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialGroupOnlineMembersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialGroupOnlineMembersResponse.Merge(dst, src)
}
func (m *GetSocialGroupOnlineMembersResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialGroupOnlineMembersResponse.Size(m)
}
func (m *GetSocialGroupOnlineMembersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialGroupOnlineMembersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialGroupOnlineMembersResponse proto.InternalMessageInfo

func (m *GetSocialGroupOnlineMembersResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialGroupOnlineMembersResponse) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *GetSocialGroupOnlineMembersResponse) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetSocialGroupOnlineMembersResponse) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetSocialGroupOnlineMembersResponse) GetLevelLogo() string {
	if m != nil {
		return m.LevelLogo
	}
	return ""
}

type BatGetSocialCommunityKernelMembersRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityIds   []string     `protobuf:"bytes,2,rep,name=social_community_ids,json=socialCommunityIds,proto3" json:"social_community_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatGetSocialCommunityKernelMembersRequest) Reset() {
	*m = BatGetSocialCommunityKernelMembersRequest{}
}
func (m *BatGetSocialCommunityKernelMembersRequest) String() string {
	return proto.CompactTextString(m)
}
func (*BatGetSocialCommunityKernelMembersRequest) ProtoMessage() {}
func (*BatGetSocialCommunityKernelMembersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{98}
}
func (m *BatGetSocialCommunityKernelMembersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetSocialCommunityKernelMembersRequest.Unmarshal(m, b)
}
func (m *BatGetSocialCommunityKernelMembersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetSocialCommunityKernelMembersRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetSocialCommunityKernelMembersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetSocialCommunityKernelMembersRequest.Merge(dst, src)
}
func (m *BatGetSocialCommunityKernelMembersRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetSocialCommunityKernelMembersRequest.Size(m)
}
func (m *BatGetSocialCommunityKernelMembersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetSocialCommunityKernelMembersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetSocialCommunityKernelMembersRequest proto.InternalMessageInfo

func (m *BatGetSocialCommunityKernelMembersRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatGetSocialCommunityKernelMembersRequest) GetSocialCommunityIds() []string {
	if m != nil {
		return m.SocialCommunityIds
	}
	return nil
}

type BatGetSocialCommunityKernelMembersResponse struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	KernelMemberMap      map[string]*BrandMembers `protobuf:"bytes,2,rep,name=kernel_member_map,json=kernelMemberMap,proto3" json:"kernel_member_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatGetSocialCommunityKernelMembersResponse) Reset() {
	*m = BatGetSocialCommunityKernelMembersResponse{}
}
func (m *BatGetSocialCommunityKernelMembersResponse) String() string {
	return proto.CompactTextString(m)
}
func (*BatGetSocialCommunityKernelMembersResponse) ProtoMessage() {}
func (*BatGetSocialCommunityKernelMembersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{99}
}
func (m *BatGetSocialCommunityKernelMembersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetSocialCommunityKernelMembersResponse.Unmarshal(m, b)
}
func (m *BatGetSocialCommunityKernelMembersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetSocialCommunityKernelMembersResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetSocialCommunityKernelMembersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetSocialCommunityKernelMembersResponse.Merge(dst, src)
}
func (m *BatGetSocialCommunityKernelMembersResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetSocialCommunityKernelMembersResponse.Size(m)
}
func (m *BatGetSocialCommunityKernelMembersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetSocialCommunityKernelMembersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetSocialCommunityKernelMembersResponse proto.InternalMessageInfo

func (m *BatGetSocialCommunityKernelMembersResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatGetSocialCommunityKernelMembersResponse) GetKernelMemberMap() map[string]*BrandMembers {
	if m != nil {
		return m.KernelMemberMap
	}
	return nil
}

type BrandMembers struct {
	Members              []*BrandMember `protobuf:"bytes,1,rep,name=members,proto3" json:"members,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BrandMembers) Reset()         { *m = BrandMembers{} }
func (m *BrandMembers) String() string { return proto.CompactTextString(m) }
func (*BrandMembers) ProtoMessage()    {}
func (*BrandMembers) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{100}
}
func (m *BrandMembers) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandMembers.Unmarshal(m, b)
}
func (m *BrandMembers) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandMembers.Marshal(b, m, deterministic)
}
func (dst *BrandMembers) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandMembers.Merge(dst, src)
}
func (m *BrandMembers) XXX_Size() int {
	return xxx_messageInfo_BrandMembers.Size(m)
}
func (m *BrandMembers) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandMembers.DiscardUnknown(m)
}

var xxx_messageInfo_BrandMembers proto.InternalMessageInfo

func (m *BrandMembers) GetMembers() []*BrandMember {
	if m != nil {
		return m.Members
	}
	return nil
}

type GetGroupActiveMembersRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GroupId              uint32       `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGroupActiveMembersRequest) Reset()         { *m = GetGroupActiveMembersRequest{} }
func (m *GetGroupActiveMembersRequest) String() string { return proto.CompactTextString(m) }
func (*GetGroupActiveMembersRequest) ProtoMessage()    {}
func (*GetGroupActiveMembersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{101}
}
func (m *GetGroupActiveMembersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupActiveMembersRequest.Unmarshal(m, b)
}
func (m *GetGroupActiveMembersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupActiveMembersRequest.Marshal(b, m, deterministic)
}
func (dst *GetGroupActiveMembersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupActiveMembersRequest.Merge(dst, src)
}
func (m *GetGroupActiveMembersRequest) XXX_Size() int {
	return xxx_messageInfo_GetGroupActiveMembersRequest.Size(m)
}
func (m *GetGroupActiveMembersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupActiveMembersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupActiveMembersRequest proto.InternalMessageInfo

func (m *GetGroupActiveMembersRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupActiveMembersRequest) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type MemberDetail struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	RoleText             string   `protobuf:"bytes,6,opt,name=role_text,json=roleText,proto3" json:"role_text,omitempty"`
	Role                 uint32   `protobuf:"varint,7,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MemberDetail) Reset()         { *m = MemberDetail{} }
func (m *MemberDetail) String() string { return proto.CompactTextString(m) }
func (*MemberDetail) ProtoMessage()    {}
func (*MemberDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{102}
}
func (m *MemberDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MemberDetail.Unmarshal(m, b)
}
func (m *MemberDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MemberDetail.Marshal(b, m, deterministic)
}
func (dst *MemberDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MemberDetail.Merge(dst, src)
}
func (m *MemberDetail) XXX_Size() int {
	return xxx_messageInfo_MemberDetail.Size(m)
}
func (m *MemberDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_MemberDetail.DiscardUnknown(m)
}

var xxx_messageInfo_MemberDetail proto.InternalMessageInfo

func (m *MemberDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberDetail) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MemberDetail) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *MemberDetail) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *MemberDetail) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MemberDetail) GetRoleText() string {
	if m != nil {
		return m.RoleText
	}
	return ""
}

func (m *MemberDetail) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

type SocialCommunityChannelSimple struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelText          string   `protobuf:"bytes,2,opt,name=channel_text,json=channelText,proto3" json:"channel_text,omitempty"`
	MemberCountText      string   `protobuf:"bytes,3,opt,name=member_count_text,json=memberCountText,proto3" json:"member_count_text,omitempty"`
	ChannelType          uint32   `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityChannelSimple) Reset()         { *m = SocialCommunityChannelSimple{} }
func (m *SocialCommunityChannelSimple) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityChannelSimple) ProtoMessage()    {}
func (*SocialCommunityChannelSimple) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{103}
}
func (m *SocialCommunityChannelSimple) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityChannelSimple.Unmarshal(m, b)
}
func (m *SocialCommunityChannelSimple) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityChannelSimple.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityChannelSimple) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityChannelSimple.Merge(dst, src)
}
func (m *SocialCommunityChannelSimple) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityChannelSimple.Size(m)
}
func (m *SocialCommunityChannelSimple) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityChannelSimple.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityChannelSimple proto.InternalMessageInfo

func (m *SocialCommunityChannelSimple) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SocialCommunityChannelSimple) GetChannelText() string {
	if m != nil {
		return m.ChannelText
	}
	return ""
}

func (m *SocialCommunityChannelSimple) GetMemberCountText() string {
	if m != nil {
		return m.MemberCountText
	}
	return ""
}

func (m *SocialCommunityChannelSimple) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetGroupActiveMembersResponse struct {
	BaseResp             *app.BaseResp                   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MemberList           []*MemberDetail                 `protobuf:"bytes,2,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	Channel              []*SocialCommunityChannelSimple `protobuf:"bytes,3,rep,name=channel,proto3" json:"channel,omitempty"`
	Share                *Share                          `protobuf:"bytes,4,opt,name=share,proto3" json:"share,omitempty"`
	CheckInStatus        uint32                          `protobuf:"varint,5,opt,name=check_in_status,json=checkInStatus,proto3" json:"check_in_status,omitempty"`
	CheckInExp           uint32                          `protobuf:"varint,6,opt,name=check_in_exp,json=checkInExp,proto3" json:"check_in_exp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetGroupActiveMembersResponse) Reset()         { *m = GetGroupActiveMembersResponse{} }
func (m *GetGroupActiveMembersResponse) String() string { return proto.CompactTextString(m) }
func (*GetGroupActiveMembersResponse) ProtoMessage()    {}
func (*GetGroupActiveMembersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{104}
}
func (m *GetGroupActiveMembersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGroupActiveMembersResponse.Unmarshal(m, b)
}
func (m *GetGroupActiveMembersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGroupActiveMembersResponse.Marshal(b, m, deterministic)
}
func (dst *GetGroupActiveMembersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGroupActiveMembersResponse.Merge(dst, src)
}
func (m *GetGroupActiveMembersResponse) XXX_Size() int {
	return xxx_messageInfo_GetGroupActiveMembersResponse.Size(m)
}
func (m *GetGroupActiveMembersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGroupActiveMembersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGroupActiveMembersResponse proto.InternalMessageInfo

func (m *GetGroupActiveMembersResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupActiveMembersResponse) GetMemberList() []*MemberDetail {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *GetGroupActiveMembersResponse) GetChannel() []*SocialCommunityChannelSimple {
	if m != nil {
		return m.Channel
	}
	return nil
}

func (m *GetGroupActiveMembersResponse) GetShare() *Share {
	if m != nil {
		return m.Share
	}
	return nil
}

func (m *GetGroupActiveMembersResponse) GetCheckInStatus() uint32 {
	if m != nil {
		return m.CheckInStatus
	}
	return 0
}

func (m *GetGroupActiveMembersResponse) GetCheckInExp() uint32 {
	if m != nil {
		return m.CheckInExp
	}
	return 0
}

type GetSocialCommunityMemberListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	OffsetId             string       `protobuf:"bytes,3,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Count                uint32       `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityMemberListRequest) Reset()         { *m = GetSocialCommunityMemberListRequest{} }
func (m *GetSocialCommunityMemberListRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityMemberListRequest) ProtoMessage()    {}
func (*GetSocialCommunityMemberListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{105}
}
func (m *GetSocialCommunityMemberListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityMemberListRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityMemberListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityMemberListRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityMemberListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityMemberListRequest.Merge(dst, src)
}
func (m *GetSocialCommunityMemberListRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityMemberListRequest.Size(m)
}
func (m *GetSocialCommunityMemberListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityMemberListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityMemberListRequest proto.InternalMessageInfo

func (m *GetSocialCommunityMemberListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialCommunityMemberListRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *GetSocialCommunityMemberListRequest) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *GetSocialCommunityMemberListRequest) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetSocialCommunityMemberListResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	KernelMemberList     []*MemberDetail `protobuf:"bytes,2,rep,name=kernel_member_list,json=kernelMemberList,proto3" json:"kernel_member_list,omitempty"`
	FansMemberList       []*MemberDetail `protobuf:"bytes,3,rep,name=fans_member_list,json=fansMemberList,proto3" json:"fans_member_list,omitempty"`
	OffsetId             string          `protobuf:"bytes,4,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	UserRole             uint32          `protobuf:"varint,5,opt,name=user_role,json=userRole,proto3" json:"user_role,omitempty"`
	KernelCount          int64           `protobuf:"varint,6,opt,name=kernel_count,json=kernelCount,proto3" json:"kernel_count,omitempty"`
	FansCount            int64           `protobuf:"varint,7,opt,name=fans_count,json=fansCount,proto3" json:"fans_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetSocialCommunityMemberListResponse) Reset()         { *m = GetSocialCommunityMemberListResponse{} }
func (m *GetSocialCommunityMemberListResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityMemberListResponse) ProtoMessage()    {}
func (*GetSocialCommunityMemberListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{106}
}
func (m *GetSocialCommunityMemberListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityMemberListResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityMemberListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityMemberListResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityMemberListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityMemberListResponse.Merge(dst, src)
}
func (m *GetSocialCommunityMemberListResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityMemberListResponse.Size(m)
}
func (m *GetSocialCommunityMemberListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityMemberListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityMemberListResponse proto.InternalMessageInfo

func (m *GetSocialCommunityMemberListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityMemberListResponse) GetKernelMemberList() []*MemberDetail {
	if m != nil {
		return m.KernelMemberList
	}
	return nil
}

func (m *GetSocialCommunityMemberListResponse) GetFansMemberList() []*MemberDetail {
	if m != nil {
		return m.FansMemberList
	}
	return nil
}

func (m *GetSocialCommunityMemberListResponse) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *GetSocialCommunityMemberListResponse) GetUserRole() uint32 {
	if m != nil {
		return m.UserRole
	}
	return 0
}

func (m *GetSocialCommunityMemberListResponse) GetKernelCount() int64 {
	if m != nil {
		return m.KernelCount
	}
	return 0
}

func (m *GetSocialCommunityMemberListResponse) GetFansCount() int64 {
	if m != nil {
		return m.FansCount
	}
	return 0
}

type GetSocialCommunityAnnounceNewsCountRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityAnnounceNewsCountRequest) Reset() {
	*m = GetSocialCommunityAnnounceNewsCountRequest{}
}
func (m *GetSocialCommunityAnnounceNewsCountRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetSocialCommunityAnnounceNewsCountRequest) ProtoMessage() {}
func (*GetSocialCommunityAnnounceNewsCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{107}
}
func (m *GetSocialCommunityAnnounceNewsCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityAnnounceNewsCountRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityAnnounceNewsCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityAnnounceNewsCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityAnnounceNewsCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityAnnounceNewsCountRequest.Merge(dst, src)
}
func (m *GetSocialCommunityAnnounceNewsCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityAnnounceNewsCountRequest.Size(m)
}
func (m *GetSocialCommunityAnnounceNewsCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityAnnounceNewsCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityAnnounceNewsCountRequest proto.InternalMessageInfo

func (m *GetSocialCommunityAnnounceNewsCountRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetSocialCommunityAnnounceNewsCountResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Num                  uint32        `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSocialCommunityAnnounceNewsCountResponse) Reset() {
	*m = GetSocialCommunityAnnounceNewsCountResponse{}
}
func (m *GetSocialCommunityAnnounceNewsCountResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetSocialCommunityAnnounceNewsCountResponse) ProtoMessage() {}
func (*GetSocialCommunityAnnounceNewsCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{108}
}
func (m *GetSocialCommunityAnnounceNewsCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityAnnounceNewsCountResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityAnnounceNewsCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityAnnounceNewsCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityAnnounceNewsCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityAnnounceNewsCountResponse.Merge(dst, src)
}
func (m *GetSocialCommunityAnnounceNewsCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityAnnounceNewsCountResponse.Size(m)
}
func (m *GetSocialCommunityAnnounceNewsCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityAnnounceNewsCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityAnnounceNewsCountResponse proto.InternalMessageInfo

func (m *GetSocialCommunityAnnounceNewsCountResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityAnnounceNewsCountResponse) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type ListMuseSocialAnnouncesRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	OffsetId             string       `protobuf:"bytes,4,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	ListType             uint32       `protobuf:"varint,5,opt,name=list_type,json=listType,proto3" json:"list_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListMuseSocialAnnouncesRequest) Reset()         { *m = ListMuseSocialAnnouncesRequest{} }
func (m *ListMuseSocialAnnouncesRequest) String() string { return proto.CompactTextString(m) }
func (*ListMuseSocialAnnouncesRequest) ProtoMessage()    {}
func (*ListMuseSocialAnnouncesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{109}
}
func (m *ListMuseSocialAnnouncesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialAnnouncesRequest.Unmarshal(m, b)
}
func (m *ListMuseSocialAnnouncesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialAnnouncesRequest.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialAnnouncesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialAnnouncesRequest.Merge(dst, src)
}
func (m *ListMuseSocialAnnouncesRequest) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialAnnouncesRequest.Size(m)
}
func (m *ListMuseSocialAnnouncesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialAnnouncesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialAnnouncesRequest proto.InternalMessageInfo

func (m *ListMuseSocialAnnouncesRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListMuseSocialAnnouncesRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *ListMuseSocialAnnouncesRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ListMuseSocialAnnouncesRequest) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *ListMuseSocialAnnouncesRequest) GetListType() uint32 {
	if m != nil {
		return m.ListType
	}
	return 0
}

type ListMuseSocialAnnouncesResponse struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Title                string                `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Announces            []*MuseSocialAnnounce `protobuf:"bytes,3,rep,name=announces,proto3" json:"announces,omitempty"`
	UnreadedListTypeMap  map[uint32]bool       `protobuf:"bytes,4,rep,name=unreaded_list_type_map,json=unreadedListTypeMap,proto3" json:"unreaded_list_type_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	OffsetId             string                `protobuf:"bytes,5,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ListMuseSocialAnnouncesResponse) Reset()         { *m = ListMuseSocialAnnouncesResponse{} }
func (m *ListMuseSocialAnnouncesResponse) String() string { return proto.CompactTextString(m) }
func (*ListMuseSocialAnnouncesResponse) ProtoMessage()    {}
func (*ListMuseSocialAnnouncesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{110}
}
func (m *ListMuseSocialAnnouncesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialAnnouncesResponse.Unmarshal(m, b)
}
func (m *ListMuseSocialAnnouncesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialAnnouncesResponse.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialAnnouncesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialAnnouncesResponse.Merge(dst, src)
}
func (m *ListMuseSocialAnnouncesResponse) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialAnnouncesResponse.Size(m)
}
func (m *ListMuseSocialAnnouncesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialAnnouncesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialAnnouncesResponse proto.InternalMessageInfo

func (m *ListMuseSocialAnnouncesResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListMuseSocialAnnouncesResponse) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ListMuseSocialAnnouncesResponse) GetAnnounces() []*MuseSocialAnnounce {
	if m != nil {
		return m.Announces
	}
	return nil
}

func (m *ListMuseSocialAnnouncesResponse) GetUnreadedListTypeMap() map[uint32]bool {
	if m != nil {
		return m.UnreadedListTypeMap
	}
	return nil
}

func (m *ListMuseSocialAnnouncesResponse) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

type MuseSocialAnnounce struct {
	Id                string                         `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title             string                         `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Content           string                         `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	StartTime         int64                          `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime           int64                          `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	InterestCount     uint32                         `protobuf:"varint,6,opt,name=interest_count,json=interestCount,proto3" json:"interest_count,omitempty"`
	Interested        bool                           `protobuf:"varint,7,opt,name=interested,proto3" json:"interested,omitempty"`
	IsUnreaded        bool                           `protobuf:"varint,8,opt,name=is_unreaded,json=isUnreaded,proto3" json:"is_unreaded,omitempty"`
	LeftModifyCount   uint32                         `protobuf:"varint,9,opt,name=left_modify_count,json=leftModifyCount,proto3" json:"left_modify_count,omitempty"`
	Images            []string                       `protobuf:"bytes,10,rep,name=images,proto3" json:"images,omitempty"`
	ImageKeys         []string                       `protobuf:"bytes,11,rep,name=image_keys,json=imageKeys,proto3" json:"image_keys,omitempty"`
	CreatorUid        uint32                         `protobuf:"varint,12,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	CreatorAccount    string                         `protobuf:"bytes,13,opt,name=creator_account,json=creatorAccount,proto3" json:"creator_account,omitempty"`
	CreatorNickName   string                         `protobuf:"bytes,14,opt,name=creator_nick_name,json=creatorNickName,proto3" json:"creator_nick_name,omitempty"`
	CreatorSex        uint32                         `protobuf:"varint,15,opt,name=creator_sex,json=creatorSex,proto3" json:"creator_sex,omitempty"`
	CreatorRole       *BrandMember                   `protobuf:"bytes,16,opt,name=creator_role,json=creatorRole,proto3" json:"creator_role,omitempty"`
	DestinationChoose *MuseAnnounceDestinationChoose `protobuf:"bytes,17,opt,name=destination_choose,json=destinationChoose,proto3" json:"destination_choose,omitempty"`
	// Types that are valid to be assigned to MuseAnnounceDestination:
	//	*MuseSocialAnnounce_NoneDestination
	//	*MuseSocialAnnounce_GroupDestination
	//	*MuseSocialAnnounce_ChatChannelDestination
	//	*MuseSocialAnnounce_ShowChannelDestination
	//	*MuseSocialAnnounce_PersonalChannelDestination
	MuseAnnounceDestination isMuseSocialAnnounce_MuseAnnounceDestination `protobuf_oneof:"muse_announce_destination"`
	XXX_NoUnkeyedLiteral    struct{}                                     `json:"-"`
	XXX_unrecognized        []byte                                       `json:"-"`
	XXX_sizecache           int32                                        `json:"-"`
}

func (m *MuseSocialAnnounce) Reset()         { *m = MuseSocialAnnounce{} }
func (m *MuseSocialAnnounce) String() string { return proto.CompactTextString(m) }
func (*MuseSocialAnnounce) ProtoMessage()    {}
func (*MuseSocialAnnounce) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{111}
}
func (m *MuseSocialAnnounce) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseSocialAnnounce.Unmarshal(m, b)
}
func (m *MuseSocialAnnounce) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseSocialAnnounce.Marshal(b, m, deterministic)
}
func (dst *MuseSocialAnnounce) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseSocialAnnounce.Merge(dst, src)
}
func (m *MuseSocialAnnounce) XXX_Size() int {
	return xxx_messageInfo_MuseSocialAnnounce.Size(m)
}
func (m *MuseSocialAnnounce) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseSocialAnnounce.DiscardUnknown(m)
}

var xxx_messageInfo_MuseSocialAnnounce proto.InternalMessageInfo

func (m *MuseSocialAnnounce) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MuseSocialAnnounce) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MuseSocialAnnounce) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *MuseSocialAnnounce) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *MuseSocialAnnounce) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *MuseSocialAnnounce) GetInterestCount() uint32 {
	if m != nil {
		return m.InterestCount
	}
	return 0
}

func (m *MuseSocialAnnounce) GetInterested() bool {
	if m != nil {
		return m.Interested
	}
	return false
}

func (m *MuseSocialAnnounce) GetIsUnreaded() bool {
	if m != nil {
		return m.IsUnreaded
	}
	return false
}

func (m *MuseSocialAnnounce) GetLeftModifyCount() uint32 {
	if m != nil {
		return m.LeftModifyCount
	}
	return 0
}

func (m *MuseSocialAnnounce) GetImages() []string {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *MuseSocialAnnounce) GetImageKeys() []string {
	if m != nil {
		return m.ImageKeys
	}
	return nil
}

func (m *MuseSocialAnnounce) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *MuseSocialAnnounce) GetCreatorAccount() string {
	if m != nil {
		return m.CreatorAccount
	}
	return ""
}

func (m *MuseSocialAnnounce) GetCreatorNickName() string {
	if m != nil {
		return m.CreatorNickName
	}
	return ""
}

func (m *MuseSocialAnnounce) GetCreatorSex() uint32 {
	if m != nil {
		return m.CreatorSex
	}
	return 0
}

func (m *MuseSocialAnnounce) GetCreatorRole() *BrandMember {
	if m != nil {
		return m.CreatorRole
	}
	return nil
}

func (m *MuseSocialAnnounce) GetDestinationChoose() *MuseAnnounceDestinationChoose {
	if m != nil {
		return m.DestinationChoose
	}
	return nil
}

type isMuseSocialAnnounce_MuseAnnounceDestination interface {
	isMuseSocialAnnounce_MuseAnnounceDestination()
}

type MuseSocialAnnounce_NoneDestination struct {
	NoneDestination *MuseAnnounceNoDestination `protobuf:"bytes,18,opt,name=none_destination,json=noneDestination,proto3,oneof"`
}

type MuseSocialAnnounce_GroupDestination struct {
	GroupDestination *MuseAnnounceGroupDestination `protobuf:"bytes,19,opt,name=group_destination,json=groupDestination,proto3,oneof"`
}

type MuseSocialAnnounce_ChatChannelDestination struct {
	ChatChannelDestination *MuseAnnounceChatChanelDestination `protobuf:"bytes,20,opt,name=chat_channel_destination,json=chatChannelDestination,proto3,oneof"`
}

type MuseSocialAnnounce_ShowChannelDestination struct {
	ShowChannelDestination *MuseAnnounceShowChanelDestination `protobuf:"bytes,21,opt,name=show_channel_destination,json=showChannelDestination,proto3,oneof"`
}

type MuseSocialAnnounce_PersonalChannelDestination struct {
	PersonalChannelDestination *MuseAnnouncePersonalChanelDestination `protobuf:"bytes,22,opt,name=personal_channel_destination,json=personalChannelDestination,proto3,oneof"`
}

func (*MuseSocialAnnounce_NoneDestination) isMuseSocialAnnounce_MuseAnnounceDestination() {}

func (*MuseSocialAnnounce_GroupDestination) isMuseSocialAnnounce_MuseAnnounceDestination() {}

func (*MuseSocialAnnounce_ChatChannelDestination) isMuseSocialAnnounce_MuseAnnounceDestination() {}

func (*MuseSocialAnnounce_ShowChannelDestination) isMuseSocialAnnounce_MuseAnnounceDestination() {}

func (*MuseSocialAnnounce_PersonalChannelDestination) isMuseSocialAnnounce_MuseAnnounceDestination() {
}

func (m *MuseSocialAnnounce) GetMuseAnnounceDestination() isMuseSocialAnnounce_MuseAnnounceDestination {
	if m != nil {
		return m.MuseAnnounceDestination
	}
	return nil
}

func (m *MuseSocialAnnounce) GetNoneDestination() *MuseAnnounceNoDestination {
	if x, ok := m.GetMuseAnnounceDestination().(*MuseSocialAnnounce_NoneDestination); ok {
		return x.NoneDestination
	}
	return nil
}

func (m *MuseSocialAnnounce) GetGroupDestination() *MuseAnnounceGroupDestination {
	if x, ok := m.GetMuseAnnounceDestination().(*MuseSocialAnnounce_GroupDestination); ok {
		return x.GroupDestination
	}
	return nil
}

func (m *MuseSocialAnnounce) GetChatChannelDestination() *MuseAnnounceChatChanelDestination {
	if x, ok := m.GetMuseAnnounceDestination().(*MuseSocialAnnounce_ChatChannelDestination); ok {
		return x.ChatChannelDestination
	}
	return nil
}

func (m *MuseSocialAnnounce) GetShowChannelDestination() *MuseAnnounceShowChanelDestination {
	if x, ok := m.GetMuseAnnounceDestination().(*MuseSocialAnnounce_ShowChannelDestination); ok {
		return x.ShowChannelDestination
	}
	return nil
}

func (m *MuseSocialAnnounce) GetPersonalChannelDestination() *MuseAnnouncePersonalChanelDestination {
	if x, ok := m.GetMuseAnnounceDestination().(*MuseSocialAnnounce_PersonalChannelDestination); ok {
		return x.PersonalChannelDestination
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*MuseSocialAnnounce) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _MuseSocialAnnounce_OneofMarshaler, _MuseSocialAnnounce_OneofUnmarshaler, _MuseSocialAnnounce_OneofSizer, []interface{}{
		(*MuseSocialAnnounce_NoneDestination)(nil),
		(*MuseSocialAnnounce_GroupDestination)(nil),
		(*MuseSocialAnnounce_ChatChannelDestination)(nil),
		(*MuseSocialAnnounce_ShowChannelDestination)(nil),
		(*MuseSocialAnnounce_PersonalChannelDestination)(nil),
	}
}

func _MuseSocialAnnounce_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*MuseSocialAnnounce)
	// muse_announce_destination
	switch x := m.MuseAnnounceDestination.(type) {
	case *MuseSocialAnnounce_NoneDestination:
		b.EncodeVarint(18<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.NoneDestination); err != nil {
			return err
		}
	case *MuseSocialAnnounce_GroupDestination:
		b.EncodeVarint(19<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.GroupDestination); err != nil {
			return err
		}
	case *MuseSocialAnnounce_ChatChannelDestination:
		b.EncodeVarint(20<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ChatChannelDestination); err != nil {
			return err
		}
	case *MuseSocialAnnounce_ShowChannelDestination:
		b.EncodeVarint(21<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.ShowChannelDestination); err != nil {
			return err
		}
	case *MuseSocialAnnounce_PersonalChannelDestination:
		b.EncodeVarint(22<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PersonalChannelDestination); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("MuseSocialAnnounce.MuseAnnounceDestination has unexpected type %T", x)
	}
	return nil
}

func _MuseSocialAnnounce_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*MuseSocialAnnounce)
	switch tag {
	case 18: // muse_announce_destination.none_destination
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseAnnounceNoDestination)
		err := b.DecodeMessage(msg)
		m.MuseAnnounceDestination = &MuseSocialAnnounce_NoneDestination{msg}
		return true, err
	case 19: // muse_announce_destination.group_destination
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseAnnounceGroupDestination)
		err := b.DecodeMessage(msg)
		m.MuseAnnounceDestination = &MuseSocialAnnounce_GroupDestination{msg}
		return true, err
	case 20: // muse_announce_destination.chat_channel_destination
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseAnnounceChatChanelDestination)
		err := b.DecodeMessage(msg)
		m.MuseAnnounceDestination = &MuseSocialAnnounce_ChatChannelDestination{msg}
		return true, err
	case 21: // muse_announce_destination.show_channel_destination
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseAnnounceShowChanelDestination)
		err := b.DecodeMessage(msg)
		m.MuseAnnounceDestination = &MuseSocialAnnounce_ShowChannelDestination{msg}
		return true, err
	case 22: // muse_announce_destination.personal_channel_destination
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseAnnouncePersonalChanelDestination)
		err := b.DecodeMessage(msg)
		m.MuseAnnounceDestination = &MuseSocialAnnounce_PersonalChannelDestination{msg}
		return true, err
	default:
		return false, nil
	}
}

func _MuseSocialAnnounce_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*MuseSocialAnnounce)
	// muse_announce_destination
	switch x := m.MuseAnnounceDestination.(type) {
	case *MuseSocialAnnounce_NoneDestination:
		s := proto.Size(x.NoneDestination)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseSocialAnnounce_GroupDestination:
		s := proto.Size(x.GroupDestination)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseSocialAnnounce_ChatChannelDestination:
		s := proto.Size(x.ChatChannelDestination)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseSocialAnnounce_ShowChannelDestination:
		s := proto.Size(x.ShowChannelDestination)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseSocialAnnounce_PersonalChannelDestination:
		s := proto.Size(x.PersonalChannelDestination)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type MuseAnnounceDestinationChoose struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	DestinationType      uint32   `protobuf:"varint,2,opt,name=destination_type,json=destinationType,proto3" json:"destination_type,omitempty"`
	Id                   uint32   `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseAnnounceDestinationChoose) Reset()         { *m = MuseAnnounceDestinationChoose{} }
func (m *MuseAnnounceDestinationChoose) String() string { return proto.CompactTextString(m) }
func (*MuseAnnounceDestinationChoose) ProtoMessage()    {}
func (*MuseAnnounceDestinationChoose) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{112}
}
func (m *MuseAnnounceDestinationChoose) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAnnounceDestinationChoose.Unmarshal(m, b)
}
func (m *MuseAnnounceDestinationChoose) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAnnounceDestinationChoose.Marshal(b, m, deterministic)
}
func (dst *MuseAnnounceDestinationChoose) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAnnounceDestinationChoose.Merge(dst, src)
}
func (m *MuseAnnounceDestinationChoose) XXX_Size() int {
	return xxx_messageInfo_MuseAnnounceDestinationChoose.Size(m)
}
func (m *MuseAnnounceDestinationChoose) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAnnounceDestinationChoose.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAnnounceDestinationChoose proto.InternalMessageInfo

func (m *MuseAnnounceDestinationChoose) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MuseAnnounceDestinationChoose) GetDestinationType() uint32 {
	if m != nil {
		return m.DestinationType
	}
	return 0
}

func (m *MuseAnnounceDestinationChoose) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type MuseAnnounceNoDestination struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseAnnounceNoDestination) Reset()         { *m = MuseAnnounceNoDestination{} }
func (m *MuseAnnounceNoDestination) String() string { return proto.CompactTextString(m) }
func (*MuseAnnounceNoDestination) ProtoMessage()    {}
func (*MuseAnnounceNoDestination) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{113}
}
func (m *MuseAnnounceNoDestination) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAnnounceNoDestination.Unmarshal(m, b)
}
func (m *MuseAnnounceNoDestination) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAnnounceNoDestination.Marshal(b, m, deterministic)
}
func (dst *MuseAnnounceNoDestination) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAnnounceNoDestination.Merge(dst, src)
}
func (m *MuseAnnounceNoDestination) XXX_Size() int {
	return xxx_messageInfo_MuseAnnounceNoDestination.Size(m)
}
func (m *MuseAnnounceNoDestination) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAnnounceNoDestination.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAnnounceNoDestination proto.InternalMessageInfo

type MuseAnnounceShowChanelDestination struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Accounts             []string `protobuf:"bytes,4,rep,name=accounts,proto3" json:"accounts,omitempty"`
	ChannelMd5           string   `protobuf:"bytes,5,opt,name=channel_md5,json=channelMd5,proto3" json:"channel_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseAnnounceShowChanelDestination) Reset()         { *m = MuseAnnounceShowChanelDestination{} }
func (m *MuseAnnounceShowChanelDestination) String() string { return proto.CompactTextString(m) }
func (*MuseAnnounceShowChanelDestination) ProtoMessage()    {}
func (*MuseAnnounceShowChanelDestination) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{114}
}
func (m *MuseAnnounceShowChanelDestination) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAnnounceShowChanelDestination.Unmarshal(m, b)
}
func (m *MuseAnnounceShowChanelDestination) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAnnounceShowChanelDestination.Marshal(b, m, deterministic)
}
func (dst *MuseAnnounceShowChanelDestination) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAnnounceShowChanelDestination.Merge(dst, src)
}
func (m *MuseAnnounceShowChanelDestination) XXX_Size() int {
	return xxx_messageInfo_MuseAnnounceShowChanelDestination.Size(m)
}
func (m *MuseAnnounceShowChanelDestination) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAnnounceShowChanelDestination.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAnnounceShowChanelDestination proto.InternalMessageInfo

func (m *MuseAnnounceShowChanelDestination) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseAnnounceShowChanelDestination) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseAnnounceShowChanelDestination) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *MuseAnnounceShowChanelDestination) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *MuseAnnounceShowChanelDestination) GetChannelMd5() string {
	if m != nil {
		return m.ChannelMd5
	}
	return ""
}

type MuseAnnounceChatChanelDestination struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Accounts             []string `protobuf:"bytes,4,rep,name=accounts,proto3" json:"accounts,omitempty"`
	ChannelMd5           string   `protobuf:"bytes,5,opt,name=channel_md5,json=channelMd5,proto3" json:"channel_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseAnnounceChatChanelDestination) Reset()         { *m = MuseAnnounceChatChanelDestination{} }
func (m *MuseAnnounceChatChanelDestination) String() string { return proto.CompactTextString(m) }
func (*MuseAnnounceChatChanelDestination) ProtoMessage()    {}
func (*MuseAnnounceChatChanelDestination) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{115}
}
func (m *MuseAnnounceChatChanelDestination) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAnnounceChatChanelDestination.Unmarshal(m, b)
}
func (m *MuseAnnounceChatChanelDestination) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAnnounceChatChanelDestination.Marshal(b, m, deterministic)
}
func (dst *MuseAnnounceChatChanelDestination) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAnnounceChatChanelDestination.Merge(dst, src)
}
func (m *MuseAnnounceChatChanelDestination) XXX_Size() int {
	return xxx_messageInfo_MuseAnnounceChatChanelDestination.Size(m)
}
func (m *MuseAnnounceChatChanelDestination) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAnnounceChatChanelDestination.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAnnounceChatChanelDestination proto.InternalMessageInfo

func (m *MuseAnnounceChatChanelDestination) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseAnnounceChatChanelDestination) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseAnnounceChatChanelDestination) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *MuseAnnounceChatChanelDestination) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *MuseAnnounceChatChanelDestination) GetChannelMd5() string {
	if m != nil {
		return m.ChannelMd5
	}
	return ""
}

type MuseAnnouncePersonalChanelDestination struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Accounts             []string `protobuf:"bytes,4,rep,name=accounts,proto3" json:"accounts,omitempty"`
	ChannelMd5           string   `protobuf:"bytes,5,opt,name=channel_md5,json=channelMd5,proto3" json:"channel_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseAnnouncePersonalChanelDestination) Reset()         { *m = MuseAnnouncePersonalChanelDestination{} }
func (m *MuseAnnouncePersonalChanelDestination) String() string { return proto.CompactTextString(m) }
func (*MuseAnnouncePersonalChanelDestination) ProtoMessage()    {}
func (*MuseAnnouncePersonalChanelDestination) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{116}
}
func (m *MuseAnnouncePersonalChanelDestination) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAnnouncePersonalChanelDestination.Unmarshal(m, b)
}
func (m *MuseAnnouncePersonalChanelDestination) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAnnouncePersonalChanelDestination.Marshal(b, m, deterministic)
}
func (dst *MuseAnnouncePersonalChanelDestination) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAnnouncePersonalChanelDestination.Merge(dst, src)
}
func (m *MuseAnnouncePersonalChanelDestination) XXX_Size() int {
	return xxx_messageInfo_MuseAnnouncePersonalChanelDestination.Size(m)
}
func (m *MuseAnnouncePersonalChanelDestination) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAnnouncePersonalChanelDestination.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAnnouncePersonalChanelDestination proto.InternalMessageInfo

func (m *MuseAnnouncePersonalChanelDestination) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseAnnouncePersonalChanelDestination) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseAnnouncePersonalChanelDestination) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *MuseAnnouncePersonalChanelDestination) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *MuseAnnouncePersonalChanelDestination) GetChannelMd5() string {
	if m != nil {
		return m.ChannelMd5
	}
	return ""
}

type MuseAnnounceGroupDestination struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Logo                 string   `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseAnnounceGroupDestination) Reset()         { *m = MuseAnnounceGroupDestination{} }
func (m *MuseAnnounceGroupDestination) String() string { return proto.CompactTextString(m) }
func (*MuseAnnounceGroupDestination) ProtoMessage()    {}
func (*MuseAnnounceGroupDestination) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{117}
}
func (m *MuseAnnounceGroupDestination) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAnnounceGroupDestination.Unmarshal(m, b)
}
func (m *MuseAnnounceGroupDestination) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAnnounceGroupDestination.Marshal(b, m, deterministic)
}
func (dst *MuseAnnounceGroupDestination) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAnnounceGroupDestination.Merge(dst, src)
}
func (m *MuseAnnounceGroupDestination) XXX_Size() int {
	return xxx_messageInfo_MuseAnnounceGroupDestination.Size(m)
}
func (m *MuseAnnounceGroupDestination) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAnnounceGroupDestination.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAnnounceGroupDestination proto.InternalMessageInfo

func (m *MuseAnnounceGroupDestination) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *MuseAnnounceGroupDestination) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MuseAnnounceGroupDestination) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *MuseAnnounceGroupDestination) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

type ListAnnounceDestinationsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListAnnounceDestinationsRequest) Reset()         { *m = ListAnnounceDestinationsRequest{} }
func (m *ListAnnounceDestinationsRequest) String() string { return proto.CompactTextString(m) }
func (*ListAnnounceDestinationsRequest) ProtoMessage()    {}
func (*ListAnnounceDestinationsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{118}
}
func (m *ListAnnounceDestinationsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListAnnounceDestinationsRequest.Unmarshal(m, b)
}
func (m *ListAnnounceDestinationsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListAnnounceDestinationsRequest.Marshal(b, m, deterministic)
}
func (dst *ListAnnounceDestinationsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListAnnounceDestinationsRequest.Merge(dst, src)
}
func (m *ListAnnounceDestinationsRequest) XXX_Size() int {
	return xxx_messageInfo_ListAnnounceDestinationsRequest.Size(m)
}
func (m *ListAnnounceDestinationsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListAnnounceDestinationsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListAnnounceDestinationsRequest proto.InternalMessageInfo

func (m *ListAnnounceDestinationsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListAnnounceDestinationsRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type ListAnnounceDestinationsResponse struct {
	BaseResp             *app.BaseResp                   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DestinationGroups    []*MuseAnnounceDestinationGroup `protobuf:"bytes,2,rep,name=destination_groups,json=destinationGroups,proto3" json:"destination_groups,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *ListAnnounceDestinationsResponse) Reset()         { *m = ListAnnounceDestinationsResponse{} }
func (m *ListAnnounceDestinationsResponse) String() string { return proto.CompactTextString(m) }
func (*ListAnnounceDestinationsResponse) ProtoMessage()    {}
func (*ListAnnounceDestinationsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{119}
}
func (m *ListAnnounceDestinationsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListAnnounceDestinationsResponse.Unmarshal(m, b)
}
func (m *ListAnnounceDestinationsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListAnnounceDestinationsResponse.Marshal(b, m, deterministic)
}
func (dst *ListAnnounceDestinationsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListAnnounceDestinationsResponse.Merge(dst, src)
}
func (m *ListAnnounceDestinationsResponse) XXX_Size() int {
	return xxx_messageInfo_ListAnnounceDestinationsResponse.Size(m)
}
func (m *ListAnnounceDestinationsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListAnnounceDestinationsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListAnnounceDestinationsResponse proto.InternalMessageInfo

func (m *ListAnnounceDestinationsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListAnnounceDestinationsResponse) GetDestinationGroups() []*MuseAnnounceDestinationGroup {
	if m != nil {
		return m.DestinationGroups
	}
	return nil
}

type MuseAnnounceDestinationGroup struct {
	Title                string                           `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Destinations         []*MuseAnnounceDestinationChoose `protobuf:"bytes,2,rep,name=destinations,proto3" json:"destinations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *MuseAnnounceDestinationGroup) Reset()         { *m = MuseAnnounceDestinationGroup{} }
func (m *MuseAnnounceDestinationGroup) String() string { return proto.CompactTextString(m) }
func (*MuseAnnounceDestinationGroup) ProtoMessage()    {}
func (*MuseAnnounceDestinationGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{120}
}
func (m *MuseAnnounceDestinationGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAnnounceDestinationGroup.Unmarshal(m, b)
}
func (m *MuseAnnounceDestinationGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAnnounceDestinationGroup.Marshal(b, m, deterministic)
}
func (dst *MuseAnnounceDestinationGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAnnounceDestinationGroup.Merge(dst, src)
}
func (m *MuseAnnounceDestinationGroup) XXX_Size() int {
	return xxx_messageInfo_MuseAnnounceDestinationGroup.Size(m)
}
func (m *MuseAnnounceDestinationGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAnnounceDestinationGroup.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAnnounceDestinationGroup proto.InternalMessageInfo

func (m *MuseAnnounceDestinationGroup) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MuseAnnounceDestinationGroup) GetDestinations() []*MuseAnnounceDestinationChoose {
	if m != nil {
		return m.Destinations
	}
	return nil
}

type UpsertMuseSocialAnnounceRequest struct {
	BaseReq              *app.BaseReq        `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Announce             *MuseSocialAnnounce `protobuf:"bytes,2,opt,name=announce,proto3" json:"announce,omitempty"`
	SocialCommunityId    string              `protobuf:"bytes,3,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpsertMuseSocialAnnounceRequest) Reset()         { *m = UpsertMuseSocialAnnounceRequest{} }
func (m *UpsertMuseSocialAnnounceRequest) String() string { return proto.CompactTextString(m) }
func (*UpsertMuseSocialAnnounceRequest) ProtoMessage()    {}
func (*UpsertMuseSocialAnnounceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{121}
}
func (m *UpsertMuseSocialAnnounceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertMuseSocialAnnounceRequest.Unmarshal(m, b)
}
func (m *UpsertMuseSocialAnnounceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertMuseSocialAnnounceRequest.Marshal(b, m, deterministic)
}
func (dst *UpsertMuseSocialAnnounceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertMuseSocialAnnounceRequest.Merge(dst, src)
}
func (m *UpsertMuseSocialAnnounceRequest) XXX_Size() int {
	return xxx_messageInfo_UpsertMuseSocialAnnounceRequest.Size(m)
}
func (m *UpsertMuseSocialAnnounceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertMuseSocialAnnounceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertMuseSocialAnnounceRequest proto.InternalMessageInfo

func (m *UpsertMuseSocialAnnounceRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpsertMuseSocialAnnounceRequest) GetAnnounce() *MuseSocialAnnounce {
	if m != nil {
		return m.Announce
	}
	return nil
}

func (m *UpsertMuseSocialAnnounceRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type UpsertMuseSocialAnnounceResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpsertMuseSocialAnnounceResponse) Reset()         { *m = UpsertMuseSocialAnnounceResponse{} }
func (m *UpsertMuseSocialAnnounceResponse) String() string { return proto.CompactTextString(m) }
func (*UpsertMuseSocialAnnounceResponse) ProtoMessage()    {}
func (*UpsertMuseSocialAnnounceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{122}
}
func (m *UpsertMuseSocialAnnounceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertMuseSocialAnnounceResponse.Unmarshal(m, b)
}
func (m *UpsertMuseSocialAnnounceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertMuseSocialAnnounceResponse.Marshal(b, m, deterministic)
}
func (dst *UpsertMuseSocialAnnounceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertMuseSocialAnnounceResponse.Merge(dst, src)
}
func (m *UpsertMuseSocialAnnounceResponse) XXX_Size() int {
	return xxx_messageInfo_UpsertMuseSocialAnnounceResponse.Size(m)
}
func (m *UpsertMuseSocialAnnounceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertMuseSocialAnnounceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertMuseSocialAnnounceResponse proto.InternalMessageInfo

func (m *UpsertMuseSocialAnnounceResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 设置感兴趣
type SetMuseSocialAnnounceInterestRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	MuseAnnounceId       string       `protobuf:"bytes,2,opt,name=muse_announce_id,json=museAnnounceId,proto3" json:"muse_announce_id,omitempty"`
	InterestType         uint32       `protobuf:"varint,3,opt,name=interest_type,json=interestType,proto3" json:"interest_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetMuseSocialAnnounceInterestRequest) Reset()         { *m = SetMuseSocialAnnounceInterestRequest{} }
func (m *SetMuseSocialAnnounceInterestRequest) String() string { return proto.CompactTextString(m) }
func (*SetMuseSocialAnnounceInterestRequest) ProtoMessage()    {}
func (*SetMuseSocialAnnounceInterestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{123}
}
func (m *SetMuseSocialAnnounceInterestRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMuseSocialAnnounceInterestRequest.Unmarshal(m, b)
}
func (m *SetMuseSocialAnnounceInterestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMuseSocialAnnounceInterestRequest.Marshal(b, m, deterministic)
}
func (dst *SetMuseSocialAnnounceInterestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMuseSocialAnnounceInterestRequest.Merge(dst, src)
}
func (m *SetMuseSocialAnnounceInterestRequest) XXX_Size() int {
	return xxx_messageInfo_SetMuseSocialAnnounceInterestRequest.Size(m)
}
func (m *SetMuseSocialAnnounceInterestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMuseSocialAnnounceInterestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetMuseSocialAnnounceInterestRequest proto.InternalMessageInfo

func (m *SetMuseSocialAnnounceInterestRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetMuseSocialAnnounceInterestRequest) GetMuseAnnounceId() string {
	if m != nil {
		return m.MuseAnnounceId
	}
	return ""
}

func (m *SetMuseSocialAnnounceInterestRequest) GetInterestType() uint32 {
	if m != nil {
		return m.InterestType
	}
	return 0
}

type SetMuseSocialAnnounceInterestResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetMuseSocialAnnounceInterestResponse) Reset()         { *m = SetMuseSocialAnnounceInterestResponse{} }
func (m *SetMuseSocialAnnounceInterestResponse) String() string { return proto.CompactTextString(m) }
func (*SetMuseSocialAnnounceInterestResponse) ProtoMessage()    {}
func (*SetMuseSocialAnnounceInterestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{124}
}
func (m *SetMuseSocialAnnounceInterestResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMuseSocialAnnounceInterestResponse.Unmarshal(m, b)
}
func (m *SetMuseSocialAnnounceInterestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMuseSocialAnnounceInterestResponse.Marshal(b, m, deterministic)
}
func (dst *SetMuseSocialAnnounceInterestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMuseSocialAnnounceInterestResponse.Merge(dst, src)
}
func (m *SetMuseSocialAnnounceInterestResponse) XXX_Size() int {
	return xxx_messageInfo_SetMuseSocialAnnounceInterestResponse.Size(m)
}
func (m *SetMuseSocialAnnounceInterestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMuseSocialAnnounceInterestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetMuseSocialAnnounceInterestResponse proto.InternalMessageInfo

func (m *SetMuseSocialAnnounceInterestResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 删除通告牌
type RemoveMuseSocialAnnounceRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	MuseAnnounceId       string       `protobuf:"bytes,2,opt,name=muse_announce_id,json=museAnnounceId,proto3" json:"muse_announce_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RemoveMuseSocialAnnounceRequest) Reset()         { *m = RemoveMuseSocialAnnounceRequest{} }
func (m *RemoveMuseSocialAnnounceRequest) String() string { return proto.CompactTextString(m) }
func (*RemoveMuseSocialAnnounceRequest) ProtoMessage()    {}
func (*RemoveMuseSocialAnnounceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{125}
}
func (m *RemoveMuseSocialAnnounceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveMuseSocialAnnounceRequest.Unmarshal(m, b)
}
func (m *RemoveMuseSocialAnnounceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveMuseSocialAnnounceRequest.Marshal(b, m, deterministic)
}
func (dst *RemoveMuseSocialAnnounceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveMuseSocialAnnounceRequest.Merge(dst, src)
}
func (m *RemoveMuseSocialAnnounceRequest) XXX_Size() int {
	return xxx_messageInfo_RemoveMuseSocialAnnounceRequest.Size(m)
}
func (m *RemoveMuseSocialAnnounceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveMuseSocialAnnounceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveMuseSocialAnnounceRequest proto.InternalMessageInfo

func (m *RemoveMuseSocialAnnounceRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RemoveMuseSocialAnnounceRequest) GetMuseAnnounceId() string {
	if m != nil {
		return m.MuseAnnounceId
	}
	return ""
}

type RemoveMuseSocialAnnounceResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RemoveMuseSocialAnnounceResponse) Reset()         { *m = RemoveMuseSocialAnnounceResponse{} }
func (m *RemoveMuseSocialAnnounceResponse) String() string { return proto.CompactTextString(m) }
func (*RemoveMuseSocialAnnounceResponse) ProtoMessage()    {}
func (*RemoveMuseSocialAnnounceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{126}
}
func (m *RemoveMuseSocialAnnounceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveMuseSocialAnnounceResponse.Unmarshal(m, b)
}
func (m *RemoveMuseSocialAnnounceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveMuseSocialAnnounceResponse.Marshal(b, m, deterministic)
}
func (dst *RemoveMuseSocialAnnounceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveMuseSocialAnnounceResponse.Merge(dst, src)
}
func (m *RemoveMuseSocialAnnounceResponse) XXX_Size() int {
	return xxx_messageInfo_RemoveMuseSocialAnnounceResponse.Size(m)
}
func (m *RemoveMuseSocialAnnounceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveMuseSocialAnnounceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveMuseSocialAnnounceResponse proto.InternalMessageInfo

func (m *RemoveMuseSocialAnnounceResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ListMuseSocialAnnounceInterestUsersRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	MuseAnnounceId       string       `protobuf:"bytes,2,opt,name=muse_announce_id,json=museAnnounceId,proto3" json:"muse_announce_id,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	OffsetId             string       `protobuf:"bytes,4,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListMuseSocialAnnounceInterestUsersRequest) Reset() {
	*m = ListMuseSocialAnnounceInterestUsersRequest{}
}
func (m *ListMuseSocialAnnounceInterestUsersRequest) String() string {
	return proto.CompactTextString(m)
}
func (*ListMuseSocialAnnounceInterestUsersRequest) ProtoMessage() {}
func (*ListMuseSocialAnnounceInterestUsersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{127}
}
func (m *ListMuseSocialAnnounceInterestUsersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialAnnounceInterestUsersRequest.Unmarshal(m, b)
}
func (m *ListMuseSocialAnnounceInterestUsersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialAnnounceInterestUsersRequest.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialAnnounceInterestUsersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialAnnounceInterestUsersRequest.Merge(dst, src)
}
func (m *ListMuseSocialAnnounceInterestUsersRequest) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialAnnounceInterestUsersRequest.Size(m)
}
func (m *ListMuseSocialAnnounceInterestUsersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialAnnounceInterestUsersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialAnnounceInterestUsersRequest proto.InternalMessageInfo

func (m *ListMuseSocialAnnounceInterestUsersRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListMuseSocialAnnounceInterestUsersRequest) GetMuseAnnounceId() string {
	if m != nil {
		return m.MuseAnnounceId
	}
	return ""
}

func (m *ListMuseSocialAnnounceInterestUsersRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ListMuseSocialAnnounceInterestUsersRequest) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

type ListMuseSocialAnnounceInterestUsersResponse struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Users                []*SimpleUserInfo `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	NextOffsetId         string            `protobuf:"bytes,3,opt,name=next_offset_id,json=nextOffsetId,proto3" json:"next_offset_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ListMuseSocialAnnounceInterestUsersResponse) Reset() {
	*m = ListMuseSocialAnnounceInterestUsersResponse{}
}
func (m *ListMuseSocialAnnounceInterestUsersResponse) String() string {
	return proto.CompactTextString(m)
}
func (*ListMuseSocialAnnounceInterestUsersResponse) ProtoMessage() {}
func (*ListMuseSocialAnnounceInterestUsersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{128}
}
func (m *ListMuseSocialAnnounceInterestUsersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListMuseSocialAnnounceInterestUsersResponse.Unmarshal(m, b)
}
func (m *ListMuseSocialAnnounceInterestUsersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListMuseSocialAnnounceInterestUsersResponse.Marshal(b, m, deterministic)
}
func (dst *ListMuseSocialAnnounceInterestUsersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListMuseSocialAnnounceInterestUsersResponse.Merge(dst, src)
}
func (m *ListMuseSocialAnnounceInterestUsersResponse) XXX_Size() int {
	return xxx_messageInfo_ListMuseSocialAnnounceInterestUsersResponse.Size(m)
}
func (m *ListMuseSocialAnnounceInterestUsersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListMuseSocialAnnounceInterestUsersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListMuseSocialAnnounceInterestUsersResponse proto.InternalMessageInfo

func (m *ListMuseSocialAnnounceInterestUsersResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListMuseSocialAnnounceInterestUsersResponse) GetUsers() []*SimpleUserInfo {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *ListMuseSocialAnnounceInterestUsersResponse) GetNextOffsetId() string {
	if m != nil {
		return m.NextOffsetId
	}
	return ""
}

type ValidateUserHasCreateAnnouncePermissionsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ValidateUserHasCreateAnnouncePermissionsRequest) Reset() {
	*m = ValidateUserHasCreateAnnouncePermissionsRequest{}
}
func (m *ValidateUserHasCreateAnnouncePermissionsRequest) String() string {
	return proto.CompactTextString(m)
}
func (*ValidateUserHasCreateAnnouncePermissionsRequest) ProtoMessage() {}
func (*ValidateUserHasCreateAnnouncePermissionsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{129}
}
func (m *ValidateUserHasCreateAnnouncePermissionsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsRequest.Unmarshal(m, b)
}
func (m *ValidateUserHasCreateAnnouncePermissionsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsRequest.Marshal(b, m, deterministic)
}
func (dst *ValidateUserHasCreateAnnouncePermissionsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsRequest.Merge(dst, src)
}
func (m *ValidateUserHasCreateAnnouncePermissionsRequest) XXX_Size() int {
	return xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsRequest.Size(m)
}
func (m *ValidateUserHasCreateAnnouncePermissionsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsRequest proto.InternalMessageInfo

func (m *ValidateUserHasCreateAnnouncePermissionsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ValidateUserHasCreateAnnouncePermissionsRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type ValidateUserHasCreateAnnouncePermissionsResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ValidateUserHasCreateAnnouncePermissionsResponse) Reset() {
	*m = ValidateUserHasCreateAnnouncePermissionsResponse{}
}
func (m *ValidateUserHasCreateAnnouncePermissionsResponse) String() string {
	return proto.CompactTextString(m)
}
func (*ValidateUserHasCreateAnnouncePermissionsResponse) ProtoMessage() {}
func (*ValidateUserHasCreateAnnouncePermissionsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{130}
}
func (m *ValidateUserHasCreateAnnouncePermissionsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsResponse.Unmarshal(m, b)
}
func (m *ValidateUserHasCreateAnnouncePermissionsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsResponse.Marshal(b, m, deterministic)
}
func (dst *ValidateUserHasCreateAnnouncePermissionsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsResponse.Merge(dst, src)
}
func (m *ValidateUserHasCreateAnnouncePermissionsResponse) XXX_Size() int {
	return xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsResponse.Size(m)
}
func (m *ValidateUserHasCreateAnnouncePermissionsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateUserHasCreateAnnouncePermissionsResponse proto.InternalMessageInfo

func (m *ValidateUserHasCreateAnnouncePermissionsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 2.4.1 设置加群模式
type SetCommunityAdditionModeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	AdditionMode         uint32       `protobuf:"varint,3,opt,name=addition_mode,json=additionMode,proto3" json:"addition_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetCommunityAdditionModeRequest) Reset()         { *m = SetCommunityAdditionModeRequest{} }
func (m *SetCommunityAdditionModeRequest) String() string { return proto.CompactTextString(m) }
func (*SetCommunityAdditionModeRequest) ProtoMessage()    {}
func (*SetCommunityAdditionModeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{131}
}
func (m *SetCommunityAdditionModeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCommunityAdditionModeRequest.Unmarshal(m, b)
}
func (m *SetCommunityAdditionModeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCommunityAdditionModeRequest.Marshal(b, m, deterministic)
}
func (dst *SetCommunityAdditionModeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCommunityAdditionModeRequest.Merge(dst, src)
}
func (m *SetCommunityAdditionModeRequest) XXX_Size() int {
	return xxx_messageInfo_SetCommunityAdditionModeRequest.Size(m)
}
func (m *SetCommunityAdditionModeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCommunityAdditionModeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetCommunityAdditionModeRequest proto.InternalMessageInfo

func (m *SetCommunityAdditionModeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetCommunityAdditionModeRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *SetCommunityAdditionModeRequest) GetAdditionMode() uint32 {
	if m != nil {
		return m.AdditionMode
	}
	return 0
}

type SetCommunityAdditionModeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetCommunityAdditionModeResponse) Reset()         { *m = SetCommunityAdditionModeResponse{} }
func (m *SetCommunityAdditionModeResponse) String() string { return proto.CompactTextString(m) }
func (*SetCommunityAdditionModeResponse) ProtoMessage()    {}
func (*SetCommunityAdditionModeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{132}
}
func (m *SetCommunityAdditionModeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCommunityAdditionModeResponse.Unmarshal(m, b)
}
func (m *SetCommunityAdditionModeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCommunityAdditionModeResponse.Marshal(b, m, deterministic)
}
func (dst *SetCommunityAdditionModeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCommunityAdditionModeResponse.Merge(dst, src)
}
func (m *SetCommunityAdditionModeResponse) XXX_Size() int {
	return xxx_messageInfo_SetCommunityAdditionModeResponse.Size(m)
}
func (m *SetCommunityAdditionModeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCommunityAdditionModeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetCommunityAdditionModeResponse proto.InternalMessageInfo

func (m *SetCommunityAdditionModeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 主理人获取加群方式
type GetCommunityAdditionModeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCommunityAdditionModeRequest) Reset()         { *m = GetCommunityAdditionModeRequest{} }
func (m *GetCommunityAdditionModeRequest) String() string { return proto.CompactTextString(m) }
func (*GetCommunityAdditionModeRequest) ProtoMessage()    {}
func (*GetCommunityAdditionModeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{133}
}
func (m *GetCommunityAdditionModeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommunityAdditionModeRequest.Unmarshal(m, b)
}
func (m *GetCommunityAdditionModeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommunityAdditionModeRequest.Marshal(b, m, deterministic)
}
func (dst *GetCommunityAdditionModeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommunityAdditionModeRequest.Merge(dst, src)
}
func (m *GetCommunityAdditionModeRequest) XXX_Size() int {
	return xxx_messageInfo_GetCommunityAdditionModeRequest.Size(m)
}
func (m *GetCommunityAdditionModeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommunityAdditionModeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommunityAdditionModeRequest proto.InternalMessageInfo

func (m *GetCommunityAdditionModeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCommunityAdditionModeRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type AdditionMode struct {
	AdditionMode         uint32   `protobuf:"varint,1,opt,name=addition_mode,json=additionMode,proto3" json:"addition_mode,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdditionMode) Reset()         { *m = AdditionMode{} }
func (m *AdditionMode) String() string { return proto.CompactTextString(m) }
func (*AdditionMode) ProtoMessage()    {}
func (*AdditionMode) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{134}
}
func (m *AdditionMode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdditionMode.Unmarshal(m, b)
}
func (m *AdditionMode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdditionMode.Marshal(b, m, deterministic)
}
func (dst *AdditionMode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdditionMode.Merge(dst, src)
}
func (m *AdditionMode) XXX_Size() int {
	return xxx_messageInfo_AdditionMode.Size(m)
}
func (m *AdditionMode) XXX_DiscardUnknown() {
	xxx_messageInfo_AdditionMode.DiscardUnknown(m)
}

var xxx_messageInfo_AdditionMode proto.InternalMessageInfo

func (m *AdditionMode) GetAdditionMode() uint32 {
	if m != nil {
		return m.AdditionMode
	}
	return 0
}

func (m *AdditionMode) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type GetCommunityAdditionModeResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CurrentAdditionMode  uint32          `protobuf:"varint,2,opt,name=current_addition_mode,json=currentAdditionMode,proto3" json:"current_addition_mode,omitempty"`
	AdditionModes        []*AdditionMode `protobuf:"bytes,3,rep,name=addition_modes,json=additionModes,proto3" json:"addition_modes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCommunityAdditionModeResponse) Reset()         { *m = GetCommunityAdditionModeResponse{} }
func (m *GetCommunityAdditionModeResponse) String() string { return proto.CompactTextString(m) }
func (*GetCommunityAdditionModeResponse) ProtoMessage()    {}
func (*GetCommunityAdditionModeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{135}
}
func (m *GetCommunityAdditionModeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommunityAdditionModeResponse.Unmarshal(m, b)
}
func (m *GetCommunityAdditionModeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommunityAdditionModeResponse.Marshal(b, m, deterministic)
}
func (dst *GetCommunityAdditionModeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommunityAdditionModeResponse.Merge(dst, src)
}
func (m *GetCommunityAdditionModeResponse) XXX_Size() int {
	return xxx_messageInfo_GetCommunityAdditionModeResponse.Size(m)
}
func (m *GetCommunityAdditionModeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommunityAdditionModeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommunityAdditionModeResponse proto.InternalMessageInfo

func (m *GetCommunityAdditionModeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCommunityAdditionModeResponse) GetCurrentAdditionMode() uint32 {
	if m != nil {
		return m.CurrentAdditionMode
	}
	return 0
}

func (m *GetCommunityAdditionModeResponse) GetAdditionModes() []*AdditionMode {
	if m != nil {
		return m.AdditionModes
	}
	return nil
}

// 获取通知列表
type ListSocialCommunitySystemMessageRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	OffsetId             string       `protobuf:"bytes,3,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Limit                uint32       `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListSocialCommunitySystemMessageRequest) Reset() {
	*m = ListSocialCommunitySystemMessageRequest{}
}
func (m *ListSocialCommunitySystemMessageRequest) String() string { return proto.CompactTextString(m) }
func (*ListSocialCommunitySystemMessageRequest) ProtoMessage()    {}
func (*ListSocialCommunitySystemMessageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{136}
}
func (m *ListSocialCommunitySystemMessageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSocialCommunitySystemMessageRequest.Unmarshal(m, b)
}
func (m *ListSocialCommunitySystemMessageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSocialCommunitySystemMessageRequest.Marshal(b, m, deterministic)
}
func (dst *ListSocialCommunitySystemMessageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSocialCommunitySystemMessageRequest.Merge(dst, src)
}
func (m *ListSocialCommunitySystemMessageRequest) XXX_Size() int {
	return xxx_messageInfo_ListSocialCommunitySystemMessageRequest.Size(m)
}
func (m *ListSocialCommunitySystemMessageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSocialCommunitySystemMessageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListSocialCommunitySystemMessageRequest proto.InternalMessageInfo

func (m *ListSocialCommunitySystemMessageRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListSocialCommunitySystemMessageRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *ListSocialCommunitySystemMessageRequest) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *ListSocialCommunitySystemMessageRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type SocialCommunitySystemMessage struct {
	// Types that are valid to be assigned to SocialCommunityMessage:
	//	*SocialCommunitySystemMessage_JoinMessage
	SocialCommunityMessage isSocialCommunitySystemMessage_SocialCommunityMessage `protobuf_oneof:"social_community_message"`
	Type                   uint32                                                `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                                              `json:"-"`
	XXX_unrecognized       []byte                                                `json:"-"`
	XXX_sizecache          int32                                                 `json:"-"`
}

func (m *SocialCommunitySystemMessage) Reset()         { *m = SocialCommunitySystemMessage{} }
func (m *SocialCommunitySystemMessage) String() string { return proto.CompactTextString(m) }
func (*SocialCommunitySystemMessage) ProtoMessage()    {}
func (*SocialCommunitySystemMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{137}
}
func (m *SocialCommunitySystemMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunitySystemMessage.Unmarshal(m, b)
}
func (m *SocialCommunitySystemMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunitySystemMessage.Marshal(b, m, deterministic)
}
func (dst *SocialCommunitySystemMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunitySystemMessage.Merge(dst, src)
}
func (m *SocialCommunitySystemMessage) XXX_Size() int {
	return xxx_messageInfo_SocialCommunitySystemMessage.Size(m)
}
func (m *SocialCommunitySystemMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunitySystemMessage.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunitySystemMessage proto.InternalMessageInfo

type isSocialCommunitySystemMessage_SocialCommunityMessage interface {
	isSocialCommunitySystemMessage_SocialCommunityMessage()
}

type SocialCommunitySystemMessage_JoinMessage struct {
	JoinMessage *JoinSocialCommunityMessage `protobuf:"bytes,1,opt,name=join_message,json=joinMessage,proto3,oneof"`
}

func (*SocialCommunitySystemMessage_JoinMessage) isSocialCommunitySystemMessage_SocialCommunityMessage() {
}

func (m *SocialCommunitySystemMessage) GetSocialCommunityMessage() isSocialCommunitySystemMessage_SocialCommunityMessage {
	if m != nil {
		return m.SocialCommunityMessage
	}
	return nil
}

func (m *SocialCommunitySystemMessage) GetJoinMessage() *JoinSocialCommunityMessage {
	if x, ok := m.GetSocialCommunityMessage().(*SocialCommunitySystemMessage_JoinMessage); ok {
		return x.JoinMessage
	}
	return nil
}

func (m *SocialCommunitySystemMessage) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*SocialCommunitySystemMessage) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _SocialCommunitySystemMessage_OneofMarshaler, _SocialCommunitySystemMessage_OneofUnmarshaler, _SocialCommunitySystemMessage_OneofSizer, []interface{}{
		(*SocialCommunitySystemMessage_JoinMessage)(nil),
	}
}

func _SocialCommunitySystemMessage_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*SocialCommunitySystemMessage)
	// social_community_message
	switch x := m.SocialCommunityMessage.(type) {
	case *SocialCommunitySystemMessage_JoinMessage:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.JoinMessage); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("SocialCommunitySystemMessage.SocialCommunityMessage has unexpected type %T", x)
	}
	return nil
}

func _SocialCommunitySystemMessage_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*SocialCommunitySystemMessage)
	switch tag {
	case 1: // social_community_message.join_message
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(JoinSocialCommunityMessage)
		err := b.DecodeMessage(msg)
		m.SocialCommunityMessage = &SocialCommunitySystemMessage_JoinMessage{msg}
		return true, err
	default:
		return false, nil
	}
}

func _SocialCommunitySystemMessage_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*SocialCommunitySystemMessage)
	// social_community_message
	switch x := m.SocialCommunityMessage.(type) {
	case *SocialCommunitySystemMessage_JoinMessage:
		s := proto.Size(x.JoinMessage)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type JoinSocialCommunityMessage struct {
	Id                   string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	User                 *SimpleUserInfo `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	JoinTime             int64           `protobuf:"varint,3,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	Reason               string          `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	Status               uint32          `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *JoinSocialCommunityMessage) Reset()         { *m = JoinSocialCommunityMessage{} }
func (m *JoinSocialCommunityMessage) String() string { return proto.CompactTextString(m) }
func (*JoinSocialCommunityMessage) ProtoMessage()    {}
func (*JoinSocialCommunityMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{138}
}
func (m *JoinSocialCommunityMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinSocialCommunityMessage.Unmarshal(m, b)
}
func (m *JoinSocialCommunityMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinSocialCommunityMessage.Marshal(b, m, deterministic)
}
func (dst *JoinSocialCommunityMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinSocialCommunityMessage.Merge(dst, src)
}
func (m *JoinSocialCommunityMessage) XXX_Size() int {
	return xxx_messageInfo_JoinSocialCommunityMessage.Size(m)
}
func (m *JoinSocialCommunityMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinSocialCommunityMessage.DiscardUnknown(m)
}

var xxx_messageInfo_JoinSocialCommunityMessage proto.InternalMessageInfo

func (m *JoinSocialCommunityMessage) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *JoinSocialCommunityMessage) GetUser() *SimpleUserInfo {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *JoinSocialCommunityMessage) GetJoinTime() int64 {
	if m != nil {
		return m.JoinTime
	}
	return 0
}

func (m *JoinSocialCommunityMessage) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *JoinSocialCommunityMessage) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type ListSocialCommunitySystemMessageResponse struct {
	BaseResp             *app.BaseResp                   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SystemMessage        []*SocialCommunitySystemMessage `protobuf:"bytes,2,rep,name=system_message,json=systemMessage,proto3" json:"system_message,omitempty"`
	OffsetId             string                          `protobuf:"bytes,3,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *ListSocialCommunitySystemMessageResponse) Reset() {
	*m = ListSocialCommunitySystemMessageResponse{}
}
func (m *ListSocialCommunitySystemMessageResponse) String() string { return proto.CompactTextString(m) }
func (*ListSocialCommunitySystemMessageResponse) ProtoMessage()    {}
func (*ListSocialCommunitySystemMessageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{139}
}
func (m *ListSocialCommunitySystemMessageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSocialCommunitySystemMessageResponse.Unmarshal(m, b)
}
func (m *ListSocialCommunitySystemMessageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSocialCommunitySystemMessageResponse.Marshal(b, m, deterministic)
}
func (dst *ListSocialCommunitySystemMessageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSocialCommunitySystemMessageResponse.Merge(dst, src)
}
func (m *ListSocialCommunitySystemMessageResponse) XXX_Size() int {
	return xxx_messageInfo_ListSocialCommunitySystemMessageResponse.Size(m)
}
func (m *ListSocialCommunitySystemMessageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSocialCommunitySystemMessageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListSocialCommunitySystemMessageResponse proto.InternalMessageInfo

func (m *ListSocialCommunitySystemMessageResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListSocialCommunitySystemMessageResponse) GetSystemMessage() []*SocialCommunitySystemMessage {
	if m != nil {
		return m.SystemMessage
	}
	return nil
}

func (m *ListSocialCommunitySystemMessageResponse) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

type SubmitApplicationToJoinCommunityRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	Reason               string       `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SubmitApplicationToJoinCommunityRequest) Reset() {
	*m = SubmitApplicationToJoinCommunityRequest{}
}
func (m *SubmitApplicationToJoinCommunityRequest) String() string { return proto.CompactTextString(m) }
func (*SubmitApplicationToJoinCommunityRequest) ProtoMessage()    {}
func (*SubmitApplicationToJoinCommunityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{140}
}
func (m *SubmitApplicationToJoinCommunityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitApplicationToJoinCommunityRequest.Unmarshal(m, b)
}
func (m *SubmitApplicationToJoinCommunityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitApplicationToJoinCommunityRequest.Marshal(b, m, deterministic)
}
func (dst *SubmitApplicationToJoinCommunityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitApplicationToJoinCommunityRequest.Merge(dst, src)
}
func (m *SubmitApplicationToJoinCommunityRequest) XXX_Size() int {
	return xxx_messageInfo_SubmitApplicationToJoinCommunityRequest.Size(m)
}
func (m *SubmitApplicationToJoinCommunityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitApplicationToJoinCommunityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitApplicationToJoinCommunityRequest proto.InternalMessageInfo

func (m *SubmitApplicationToJoinCommunityRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SubmitApplicationToJoinCommunityRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *SubmitApplicationToJoinCommunityRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type SubmitApplicationToJoinCommunityResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SubmitApplicationToJoinCommunityResponse) Reset() {
	*m = SubmitApplicationToJoinCommunityResponse{}
}
func (m *SubmitApplicationToJoinCommunityResponse) String() string { return proto.CompactTextString(m) }
func (*SubmitApplicationToJoinCommunityResponse) ProtoMessage()    {}
func (*SubmitApplicationToJoinCommunityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{141}
}
func (m *SubmitApplicationToJoinCommunityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitApplicationToJoinCommunityResponse.Unmarshal(m, b)
}
func (m *SubmitApplicationToJoinCommunityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitApplicationToJoinCommunityResponse.Marshal(b, m, deterministic)
}
func (dst *SubmitApplicationToJoinCommunityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitApplicationToJoinCommunityResponse.Merge(dst, src)
}
func (m *SubmitApplicationToJoinCommunityResponse) XXX_Size() int {
	return xxx_messageInfo_SubmitApplicationToJoinCommunityResponse.Size(m)
}
func (m *SubmitApplicationToJoinCommunityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitApplicationToJoinCommunityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitApplicationToJoinCommunityResponse proto.InternalMessageInfo

func (m *SubmitApplicationToJoinCommunityResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 2.4.6 成员申请加入社群的状态变更
type UpsertJoinSocialCommunityMessageStatusRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Id                   string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Status               uint32       `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpsertJoinSocialCommunityMessageStatusRequest) Reset() {
	*m = UpsertJoinSocialCommunityMessageStatusRequest{}
}
func (m *UpsertJoinSocialCommunityMessageStatusRequest) String() string {
	return proto.CompactTextString(m)
}
func (*UpsertJoinSocialCommunityMessageStatusRequest) ProtoMessage() {}
func (*UpsertJoinSocialCommunityMessageStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{142}
}
func (m *UpsertJoinSocialCommunityMessageStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusRequest.Unmarshal(m, b)
}
func (m *UpsertJoinSocialCommunityMessageStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusRequest.Marshal(b, m, deterministic)
}
func (dst *UpsertJoinSocialCommunityMessageStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusRequest.Merge(dst, src)
}
func (m *UpsertJoinSocialCommunityMessageStatusRequest) XXX_Size() int {
	return xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusRequest.Size(m)
}
func (m *UpsertJoinSocialCommunityMessageStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusRequest proto.InternalMessageInfo

func (m *UpsertJoinSocialCommunityMessageStatusRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpsertJoinSocialCommunityMessageStatusRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpsertJoinSocialCommunityMessageStatusRequest) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type UpsertJoinSocialCommunityMessageStatusResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpsertJoinSocialCommunityMessageStatusResponse) Reset() {
	*m = UpsertJoinSocialCommunityMessageStatusResponse{}
}
func (m *UpsertJoinSocialCommunityMessageStatusResponse) String() string {
	return proto.CompactTextString(m)
}
func (*UpsertJoinSocialCommunityMessageStatusResponse) ProtoMessage() {}
func (*UpsertJoinSocialCommunityMessageStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{143}
}
func (m *UpsertJoinSocialCommunityMessageStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusResponse.Unmarshal(m, b)
}
func (m *UpsertJoinSocialCommunityMessageStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusResponse.Marshal(b, m, deterministic)
}
func (dst *UpsertJoinSocialCommunityMessageStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusResponse.Merge(dst, src)
}
func (m *UpsertJoinSocialCommunityMessageStatusResponse) XXX_Size() int {
	return xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusResponse.Size(m)
}
func (m *UpsertJoinSocialCommunityMessageStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertJoinSocialCommunityMessageStatusResponse proto.InternalMessageInfo

func (m *UpsertJoinSocialCommunityMessageStatusResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取升级提醒
type GetSocialCommunityUpdateLevelTipRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityUpdateLevelTipRequest) Reset() {
	*m = GetSocialCommunityUpdateLevelTipRequest{}
}
func (m *GetSocialCommunityUpdateLevelTipRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityUpdateLevelTipRequest) ProtoMessage()    {}
func (*GetSocialCommunityUpdateLevelTipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{144}
}
func (m *GetSocialCommunityUpdateLevelTipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityUpdateLevelTipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityUpdateLevelTipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.Merge(dst, src)
}
func (m *GetSocialCommunityUpdateLevelTipRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.Size(m)
}
func (m *GetSocialCommunityUpdateLevelTipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest proto.InternalMessageInfo

func (m *GetSocialCommunityUpdateLevelTipRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialCommunityUpdateLevelTipRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type GetSocialCommunityUpdateLevelTipResponse struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SocialCommunityId    string                     `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SocialCommunityName  string                     `protobuf:"bytes,3,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	Logo                 string                     `protobuf:"bytes,4,opt,name=logo,proto3" json:"logo,omitempty"`
	RightGroup           *SocialCommunityRightGroup `protobuf:"bytes,5,opt,name=right_group,json=rightGroup,proto3" json:"right_group,omitempty"`
	CaptainAccount       string                     `protobuf:"bytes,6,opt,name=captain_account,json=captainAccount,proto3" json:"captain_account,omitempty"`
	CaptainNickName      string                     `protobuf:"bytes,7,opt,name=captain_nick_name,json=captainNickName,proto3" json:"captain_nick_name,omitempty"`
	CaptainUid           uint32                     `protobuf:"varint,8,opt,name=captain_uid,json=captainUid,proto3" json:"captain_uid,omitempty"`
	Level                uint32                     `protobuf:"varint,9,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetSocialCommunityUpdateLevelTipResponse) Reset() {
	*m = GetSocialCommunityUpdateLevelTipResponse{}
}
func (m *GetSocialCommunityUpdateLevelTipResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityUpdateLevelTipResponse) ProtoMessage()    {}
func (*GetSocialCommunityUpdateLevelTipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{145}
}
func (m *GetSocialCommunityUpdateLevelTipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityUpdateLevelTipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityUpdateLevelTipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.Merge(dst, src)
}
func (m *GetSocialCommunityUpdateLevelTipResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.Size(m)
}
func (m *GetSocialCommunityUpdateLevelTipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse proto.InternalMessageInfo

func (m *GetSocialCommunityUpdateLevelTipResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetRightGroup() *SocialCommunityRightGroup {
	if m != nil {
		return m.RightGroup
	}
	return nil
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetCaptainAccount() string {
	if m != nil {
		return m.CaptainAccount
	}
	return ""
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetCaptainNickName() string {
	if m != nil {
		return m.CaptainNickName
	}
	return ""
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetCaptainUid() uint32 {
	if m != nil {
		return m.CaptainUid
	}
	return 0
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 获取等级详情
type GetSocialCommunityLevelDetailRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityLevelDetailRequest) Reset()         { *m = GetSocialCommunityLevelDetailRequest{} }
func (m *GetSocialCommunityLevelDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityLevelDetailRequest) ProtoMessage()    {}
func (*GetSocialCommunityLevelDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{146}
}
func (m *GetSocialCommunityLevelDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityLevelDetailRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityLevelDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityLevelDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityLevelDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityLevelDetailRequest.Merge(dst, src)
}
func (m *GetSocialCommunityLevelDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityLevelDetailRequest.Size(m)
}
func (m *GetSocialCommunityLevelDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityLevelDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityLevelDetailRequest proto.InternalMessageInfo

func (m *GetSocialCommunityLevelDetailRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialCommunityLevelDetailRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type GetSocialCommunityLevelDetailResponse struct {
	BaseResp             *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SocialCommunityId    string                      `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SocialCommunityName  string                      `protobuf:"bytes,3,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	SocialCommunityLogo  string                      `protobuf:"bytes,4,opt,name=social_community_logo,json=socialCommunityLogo,proto3" json:"social_community_logo,omitempty"`
	Professionalism      uint32                      `protobuf:"varint,5,opt,name=professionalism,proto3" json:"professionalism,omitempty"`
	Member               *BrandMember                `protobuf:"bytes,6,opt,name=member,proto3" json:"member,omitempty"`
	LevelCard            *SocialCommunityLevelCard   `protobuf:"bytes,7,opt,name=level_card,json=levelCard,proto3" json:"level_card,omitempty"`
	RightGroup           *SocialCommunityRightGroup  `protobuf:"bytes,8,opt,name=right_group,json=rightGroup,proto3" json:"right_group,omitempty"`
	TaskGroups           []*SocialCommunityTaskGroup `protobuf:"bytes,9,rep,name=task_groups,json=taskGroups,proto3" json:"task_groups,omitempty"`
	MemberCount          uint32                      `protobuf:"varint,10,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	ProfessionalismInfo  *Professionalism            `protobuf:"bytes,11,opt,name=professionalism_info,json=professionalismInfo,proto3" json:"professionalism_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetSocialCommunityLevelDetailResponse) Reset()         { *m = GetSocialCommunityLevelDetailResponse{} }
func (m *GetSocialCommunityLevelDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityLevelDetailResponse) ProtoMessage()    {}
func (*GetSocialCommunityLevelDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{147}
}
func (m *GetSocialCommunityLevelDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityLevelDetailResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityLevelDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityLevelDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityLevelDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityLevelDetailResponse.Merge(dst, src)
}
func (m *GetSocialCommunityLevelDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityLevelDetailResponse.Size(m)
}
func (m *GetSocialCommunityLevelDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityLevelDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityLevelDetailResponse proto.InternalMessageInfo

func (m *GetSocialCommunityLevelDetailResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityLevelDetailResponse) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *GetSocialCommunityLevelDetailResponse) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *GetSocialCommunityLevelDetailResponse) GetSocialCommunityLogo() string {
	if m != nil {
		return m.SocialCommunityLogo
	}
	return ""
}

func (m *GetSocialCommunityLevelDetailResponse) GetProfessionalism() uint32 {
	if m != nil {
		return m.Professionalism
	}
	return 0
}

func (m *GetSocialCommunityLevelDetailResponse) GetMember() *BrandMember {
	if m != nil {
		return m.Member
	}
	return nil
}

func (m *GetSocialCommunityLevelDetailResponse) GetLevelCard() *SocialCommunityLevelCard {
	if m != nil {
		return m.LevelCard
	}
	return nil
}

func (m *GetSocialCommunityLevelDetailResponse) GetRightGroup() *SocialCommunityRightGroup {
	if m != nil {
		return m.RightGroup
	}
	return nil
}

func (m *GetSocialCommunityLevelDetailResponse) GetTaskGroups() []*SocialCommunityTaskGroup {
	if m != nil {
		return m.TaskGroups
	}
	return nil
}

func (m *GetSocialCommunityLevelDetailResponse) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *GetSocialCommunityLevelDetailResponse) GetProfessionalismInfo() *Professionalism {
	if m != nil {
		return m.ProfessionalismInfo
	}
	return nil
}

type SocialCommunityRightGroup struct {
	Title                 string                  `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SocialCommunityRights []*SocialCommunityRight `protobuf:"bytes,2,rep,name=social_community_rights,json=socialCommunityRights,proto3" json:"social_community_rights,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                `json:"-"`
	XXX_unrecognized      []byte                  `json:"-"`
	XXX_sizecache         int32                   `json:"-"`
}

func (m *SocialCommunityRightGroup) Reset()         { *m = SocialCommunityRightGroup{} }
func (m *SocialCommunityRightGroup) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityRightGroup) ProtoMessage()    {}
func (*SocialCommunityRightGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{148}
}
func (m *SocialCommunityRightGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityRightGroup.Unmarshal(m, b)
}
func (m *SocialCommunityRightGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityRightGroup.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityRightGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityRightGroup.Merge(dst, src)
}
func (m *SocialCommunityRightGroup) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityRightGroup.Size(m)
}
func (m *SocialCommunityRightGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityRightGroup.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityRightGroup proto.InternalMessageInfo

func (m *SocialCommunityRightGroup) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityRightGroup) GetSocialCommunityRights() []*SocialCommunityRight {
	if m != nil {
		return m.SocialCommunityRights
	}
	return nil
}

type SocialCommunityLevelCard struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	CurExp               int64    `protobuf:"varint,2,opt,name=cur_exp,json=curExp,proto3" json:"cur_exp,omitempty"`
	NextLevelExp         int64    `protobuf:"varint,3,opt,name=next_level_exp,json=nextLevelExp,proto3" json:"next_level_exp,omitempty"`
	Account              string   `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	UserTodayExp         int64    `protobuf:"varint,5,opt,name=user_today_exp,json=userTodayExp,proto3" json:"user_today_exp,omitempty"`
	UserTotalExp         int64    `protobuf:"varint,6,opt,name=user_total_exp,json=userTotalExp,proto3" json:"user_total_exp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityLevelCard) Reset()         { *m = SocialCommunityLevelCard{} }
func (m *SocialCommunityLevelCard) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityLevelCard) ProtoMessage()    {}
func (*SocialCommunityLevelCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{149}
}
func (m *SocialCommunityLevelCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityLevelCard.Unmarshal(m, b)
}
func (m *SocialCommunityLevelCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityLevelCard.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityLevelCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityLevelCard.Merge(dst, src)
}
func (m *SocialCommunityLevelCard) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityLevelCard.Size(m)
}
func (m *SocialCommunityLevelCard) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityLevelCard.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityLevelCard proto.InternalMessageInfo

func (m *SocialCommunityLevelCard) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SocialCommunityLevelCard) GetCurExp() int64 {
	if m != nil {
		return m.CurExp
	}
	return 0
}

func (m *SocialCommunityLevelCard) GetNextLevelExp() int64 {
	if m != nil {
		return m.NextLevelExp
	}
	return 0
}

func (m *SocialCommunityLevelCard) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SocialCommunityLevelCard) GetUserTodayExp() int64 {
	if m != nil {
		return m.UserTodayExp
	}
	return 0
}

func (m *SocialCommunityLevelCard) GetUserTotalExp() int64 {
	if m != nil {
		return m.UserTotalExp
	}
	return 0
}

type SocialCommunityRight struct {
	Logo                 string   `protobuf:"bytes,1,opt,name=logo,proto3" json:"logo,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityRight) Reset()         { *m = SocialCommunityRight{} }
func (m *SocialCommunityRight) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityRight) ProtoMessage()    {}
func (*SocialCommunityRight) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{150}
}
func (m *SocialCommunityRight) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityRight.Unmarshal(m, b)
}
func (m *SocialCommunityRight) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityRight.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityRight) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityRight.Merge(dst, src)
}
func (m *SocialCommunityRight) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityRight.Size(m)
}
func (m *SocialCommunityRight) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityRight.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityRight proto.InternalMessageInfo

func (m *SocialCommunityRight) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *SocialCommunityRight) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityRight) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SocialCommunityRight) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type SocialCommunityTaskGroup struct {
	Title                string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Tasks                []*SocialCommunityTask `protobuf:"bytes,7,rep,name=tasks,proto3" json:"tasks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SocialCommunityTaskGroup) Reset()         { *m = SocialCommunityTaskGroup{} }
func (m *SocialCommunityTaskGroup) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityTaskGroup) ProtoMessage()    {}
func (*SocialCommunityTaskGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{151}
}
func (m *SocialCommunityTaskGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityTaskGroup.Unmarshal(m, b)
}
func (m *SocialCommunityTaskGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityTaskGroup.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityTaskGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityTaskGroup.Merge(dst, src)
}
func (m *SocialCommunityTaskGroup) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityTaskGroup.Size(m)
}
func (m *SocialCommunityTaskGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityTaskGroup.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityTaskGroup proto.InternalMessageInfo

func (m *SocialCommunityTaskGroup) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityTaskGroup) GetTasks() []*SocialCommunityTask {
	if m != nil {
		return m.Tasks
	}
	return nil
}

type SocialCommunityTask struct {
	// Types that are valid to be assigned to SocialCommunityTaskView:
	//	*SocialCommunityTask_NormalTaskView
	//	*SocialCommunityTask_CheckInTaskView
	//	*SocialCommunityTask_StepTaskView
	SocialCommunityTaskView isSocialCommunityTask_SocialCommunityTaskView `protobuf_oneof:"social_community_task_view"`
	Status                  uint32                                        `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	ActionType              uint32                                        `protobuf:"varint,6,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	ActionUrl               string                                        `protobuf:"bytes,7,opt,name=action_url,json=actionUrl,proto3" json:"action_url,omitempty"`
	BgColors                []string                                      `protobuf:"bytes,8,rep,name=bg_colors,json=bgColors,proto3" json:"bg_colors,omitempty"`
	ViewType                uint32                                        `protobuf:"varint,9,opt,name=view_type,json=viewType,proto3" json:"view_type,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                                      `json:"-"`
	XXX_unrecognized        []byte                                        `json:"-"`
	XXX_sizecache           int32                                         `json:"-"`
}

func (m *SocialCommunityTask) Reset()         { *m = SocialCommunityTask{} }
func (m *SocialCommunityTask) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityTask) ProtoMessage()    {}
func (*SocialCommunityTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{152}
}
func (m *SocialCommunityTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityTask.Unmarshal(m, b)
}
func (m *SocialCommunityTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityTask.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityTask.Merge(dst, src)
}
func (m *SocialCommunityTask) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityTask.Size(m)
}
func (m *SocialCommunityTask) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityTask.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityTask proto.InternalMessageInfo

type isSocialCommunityTask_SocialCommunityTaskView interface {
	isSocialCommunityTask_SocialCommunityTaskView()
}

type SocialCommunityTask_NormalTaskView struct {
	NormalTaskView *SocialCommunityNormalTaskView `protobuf:"bytes,1,opt,name=normal_task_view,json=normalTaskView,proto3,oneof"`
}

type SocialCommunityTask_CheckInTaskView struct {
	CheckInTaskView *SocialCommunityCheckInTaskView `protobuf:"bytes,2,opt,name=check_in_task_view,json=checkInTaskView,proto3,oneof"`
}

type SocialCommunityTask_StepTaskView struct {
	StepTaskView *SocialCommunityStepTaskView `protobuf:"bytes,3,opt,name=step_task_view,json=stepTaskView,proto3,oneof"`
}

func (*SocialCommunityTask_NormalTaskView) isSocialCommunityTask_SocialCommunityTaskView() {}

func (*SocialCommunityTask_CheckInTaskView) isSocialCommunityTask_SocialCommunityTaskView() {}

func (*SocialCommunityTask_StepTaskView) isSocialCommunityTask_SocialCommunityTaskView() {}

func (m *SocialCommunityTask) GetSocialCommunityTaskView() isSocialCommunityTask_SocialCommunityTaskView {
	if m != nil {
		return m.SocialCommunityTaskView
	}
	return nil
}

func (m *SocialCommunityTask) GetNormalTaskView() *SocialCommunityNormalTaskView {
	if x, ok := m.GetSocialCommunityTaskView().(*SocialCommunityTask_NormalTaskView); ok {
		return x.NormalTaskView
	}
	return nil
}

func (m *SocialCommunityTask) GetCheckInTaskView() *SocialCommunityCheckInTaskView {
	if x, ok := m.GetSocialCommunityTaskView().(*SocialCommunityTask_CheckInTaskView); ok {
		return x.CheckInTaskView
	}
	return nil
}

func (m *SocialCommunityTask) GetStepTaskView() *SocialCommunityStepTaskView {
	if x, ok := m.GetSocialCommunityTaskView().(*SocialCommunityTask_StepTaskView); ok {
		return x.StepTaskView
	}
	return nil
}

func (m *SocialCommunityTask) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SocialCommunityTask) GetActionType() uint32 {
	if m != nil {
		return m.ActionType
	}
	return 0
}

func (m *SocialCommunityTask) GetActionUrl() string {
	if m != nil {
		return m.ActionUrl
	}
	return ""
}

func (m *SocialCommunityTask) GetBgColors() []string {
	if m != nil {
		return m.BgColors
	}
	return nil
}

func (m *SocialCommunityTask) GetViewType() uint32 {
	if m != nil {
		return m.ViewType
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*SocialCommunityTask) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _SocialCommunityTask_OneofMarshaler, _SocialCommunityTask_OneofUnmarshaler, _SocialCommunityTask_OneofSizer, []interface{}{
		(*SocialCommunityTask_NormalTaskView)(nil),
		(*SocialCommunityTask_CheckInTaskView)(nil),
		(*SocialCommunityTask_StepTaskView)(nil),
	}
}

func _SocialCommunityTask_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*SocialCommunityTask)
	// social_community_task_view
	switch x := m.SocialCommunityTaskView.(type) {
	case *SocialCommunityTask_NormalTaskView:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.NormalTaskView); err != nil {
			return err
		}
	case *SocialCommunityTask_CheckInTaskView:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.CheckInTaskView); err != nil {
			return err
		}
	case *SocialCommunityTask_StepTaskView:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.StepTaskView); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("SocialCommunityTask.SocialCommunityTaskView has unexpected type %T", x)
	}
	return nil
}

func _SocialCommunityTask_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*SocialCommunityTask)
	switch tag {
	case 1: // social_community_task_view.normal_task_view
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityNormalTaskView)
		err := b.DecodeMessage(msg)
		m.SocialCommunityTaskView = &SocialCommunityTask_NormalTaskView{msg}
		return true, err
	case 2: // social_community_task_view.check_in_task_view
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityCheckInTaskView)
		err := b.DecodeMessage(msg)
		m.SocialCommunityTaskView = &SocialCommunityTask_CheckInTaskView{msg}
		return true, err
	case 3: // social_community_task_view.step_task_view
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityStepTaskView)
		err := b.DecodeMessage(msg)
		m.SocialCommunityTaskView = &SocialCommunityTask_StepTaskView{msg}
		return true, err
	default:
		return false, nil
	}
}

func _SocialCommunityTask_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*SocialCommunityTask)
	// social_community_task_view
	switch x := m.SocialCommunityTaskView.(type) {
	case *SocialCommunityTask_NormalTaskView:
		s := proto.Size(x.NormalTaskView)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SocialCommunityTask_CheckInTaskView:
		s := proto.Size(x.CheckInTaskView)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SocialCommunityTask_StepTaskView:
		s := proto.Size(x.StepTaskView)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type SocialCommunityNormalTaskView struct {
	AwardExp             int64    `protobuf:"varint,1,opt,name=award_exp,json=awardExp,proto3" json:"award_exp,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	ActiveDesc           string   `protobuf:"bytes,4,opt,name=active_desc,json=activeDesc,proto3" json:"active_desc,omitempty"`
	ButtonText           string   `protobuf:"bytes,5,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityNormalTaskView) Reset()         { *m = SocialCommunityNormalTaskView{} }
func (m *SocialCommunityNormalTaskView) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityNormalTaskView) ProtoMessage()    {}
func (*SocialCommunityNormalTaskView) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{153}
}
func (m *SocialCommunityNormalTaskView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityNormalTaskView.Unmarshal(m, b)
}
func (m *SocialCommunityNormalTaskView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityNormalTaskView.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityNormalTaskView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityNormalTaskView.Merge(dst, src)
}
func (m *SocialCommunityNormalTaskView) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityNormalTaskView.Size(m)
}
func (m *SocialCommunityNormalTaskView) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityNormalTaskView.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityNormalTaskView proto.InternalMessageInfo

func (m *SocialCommunityNormalTaskView) GetAwardExp() int64 {
	if m != nil {
		return m.AwardExp
	}
	return 0
}

func (m *SocialCommunityNormalTaskView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityNormalTaskView) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SocialCommunityNormalTaskView) GetActiveDesc() string {
	if m != nil {
		return m.ActiveDesc
	}
	return ""
}

func (m *SocialCommunityNormalTaskView) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

type SocialCommunityCheckInTaskView struct {
	AwardExp             int64             `protobuf:"varint,1,opt,name=award_exp,json=awardExp,proto3" json:"award_exp,omitempty"`
	Title                string            `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string            `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	ActiveDesc           string            `protobuf:"bytes,4,opt,name=active_desc,json=activeDesc,proto3" json:"active_desc,omitempty"`
	StatusButtonText     map[uint32]string `protobuf:"bytes,5,rep,name=status_button_text,json=statusButtonText,proto3" json:"status_button_text,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SocialCommunityCheckInTaskView) Reset()         { *m = SocialCommunityCheckInTaskView{} }
func (m *SocialCommunityCheckInTaskView) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityCheckInTaskView) ProtoMessage()    {}
func (*SocialCommunityCheckInTaskView) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{154}
}
func (m *SocialCommunityCheckInTaskView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityCheckInTaskView.Unmarshal(m, b)
}
func (m *SocialCommunityCheckInTaskView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityCheckInTaskView.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityCheckInTaskView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityCheckInTaskView.Merge(dst, src)
}
func (m *SocialCommunityCheckInTaskView) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityCheckInTaskView.Size(m)
}
func (m *SocialCommunityCheckInTaskView) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityCheckInTaskView.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityCheckInTaskView proto.InternalMessageInfo

func (m *SocialCommunityCheckInTaskView) GetAwardExp() int64 {
	if m != nil {
		return m.AwardExp
	}
	return 0
}

func (m *SocialCommunityCheckInTaskView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityCheckInTaskView) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SocialCommunityCheckInTaskView) GetActiveDesc() string {
	if m != nil {
		return m.ActiveDesc
	}
	return ""
}

func (m *SocialCommunityCheckInTaskView) GetStatusButtonText() map[uint32]string {
	if m != nil {
		return m.StatusButtonText
	}
	return nil
}

type SocialCommunityStepTaskView struct {
	Steps                []*SocialCommunityStep `protobuf:"bytes,1,rep,name=steps,proto3" json:"steps,omitempty"`
	Title                string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	ButtonText           string                 `protobuf:"bytes,3,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	Score                int64                  `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	Desc                 string                 `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SocialCommunityStepTaskView) Reset()         { *m = SocialCommunityStepTaskView{} }
func (m *SocialCommunityStepTaskView) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityStepTaskView) ProtoMessage()    {}
func (*SocialCommunityStepTaskView) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{155}
}
func (m *SocialCommunityStepTaskView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityStepTaskView.Unmarshal(m, b)
}
func (m *SocialCommunityStepTaskView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityStepTaskView.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityStepTaskView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityStepTaskView.Merge(dst, src)
}
func (m *SocialCommunityStepTaskView) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityStepTaskView.Size(m)
}
func (m *SocialCommunityStepTaskView) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityStepTaskView.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityStepTaskView proto.InternalMessageInfo

func (m *SocialCommunityStepTaskView) GetSteps() []*SocialCommunityStep {
	if m != nil {
		return m.Steps
	}
	return nil
}

func (m *SocialCommunityStepTaskView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityStepTaskView) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *SocialCommunityStepTaskView) GetScore() int64 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *SocialCommunityStepTaskView) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type SocialCommunityStep struct {
	AwardExp             int64    `protobuf:"varint,1,opt,name=award_exp,json=awardExp,proto3" json:"award_exp,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Score                int64    `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityStep) Reset()         { *m = SocialCommunityStep{} }
func (m *SocialCommunityStep) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityStep) ProtoMessage()    {}
func (*SocialCommunityStep) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{156}
}
func (m *SocialCommunityStep) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityStep.Unmarshal(m, b)
}
func (m *SocialCommunityStep) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityStep.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityStep) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityStep.Merge(dst, src)
}
func (m *SocialCommunityStep) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityStep.Size(m)
}
func (m *SocialCommunityStep) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityStep.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityStep proto.InternalMessageInfo

func (m *SocialCommunityStep) GetAwardExp() int64 {
	if m != nil {
		return m.AwardExp
	}
	return 0
}

func (m *SocialCommunityStep) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SocialCommunityStep) GetScore() int64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type SocialCommunityCheckInRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SocialCommunityId    string       `protobuf:"bytes,2,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SocialCommunityCheckInRequest) Reset()         { *m = SocialCommunityCheckInRequest{} }
func (m *SocialCommunityCheckInRequest) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityCheckInRequest) ProtoMessage()    {}
func (*SocialCommunityCheckInRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{157}
}
func (m *SocialCommunityCheckInRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityCheckInRequest.Unmarshal(m, b)
}
func (m *SocialCommunityCheckInRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityCheckInRequest.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityCheckInRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityCheckInRequest.Merge(dst, src)
}
func (m *SocialCommunityCheckInRequest) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityCheckInRequest.Size(m)
}
func (m *SocialCommunityCheckInRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityCheckInRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityCheckInRequest proto.InternalMessageInfo

func (m *SocialCommunityCheckInRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SocialCommunityCheckInRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type SocialCommunityCheckInResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SocialCommunityCheckInResponse) Reset()         { *m = SocialCommunityCheckInResponse{} }
func (m *SocialCommunityCheckInResponse) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityCheckInResponse) ProtoMessage()    {}
func (*SocialCommunityCheckInResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{158}
}
func (m *SocialCommunityCheckInResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityCheckInResponse.Unmarshal(m, b)
}
func (m *SocialCommunityCheckInResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityCheckInResponse.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityCheckInResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityCheckInResponse.Merge(dst, src)
}
func (m *SocialCommunityCheckInResponse) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityCheckInResponse.Size(m)
}
func (m *SocialCommunityCheckInResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityCheckInResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityCheckInResponse proto.InternalMessageInfo

func (m *SocialCommunityCheckInResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 群聊任务完成公告
type GroupMessageTaskDoneNotify struct {
	Message              *sync.NewMessageSync `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	TaskId               string               `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Desc                 string               `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GroupMessageTaskDoneNotify) Reset()         { *m = GroupMessageTaskDoneNotify{} }
func (m *GroupMessageTaskDoneNotify) String() string { return proto.CompactTextString(m) }
func (*GroupMessageTaskDoneNotify) ProtoMessage()    {}
func (*GroupMessageTaskDoneNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{159}
}
func (m *GroupMessageTaskDoneNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupMessageTaskDoneNotify.Unmarshal(m, b)
}
func (m *GroupMessageTaskDoneNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupMessageTaskDoneNotify.Marshal(b, m, deterministic)
}
func (dst *GroupMessageTaskDoneNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupMessageTaskDoneNotify.Merge(dst, src)
}
func (m *GroupMessageTaskDoneNotify) XXX_Size() int {
	return xxx_messageInfo_GroupMessageTaskDoneNotify.Size(m)
}
func (m *GroupMessageTaskDoneNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupMessageTaskDoneNotify.DiscardUnknown(m)
}

var xxx_messageInfo_GroupMessageTaskDoneNotify proto.InternalMessageInfo

func (m *GroupMessageTaskDoneNotify) GetMessage() *sync.NewMessageSync {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *GroupMessageTaskDoneNotify) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *GroupMessageTaskDoneNotify) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type GetSocialCommunityContentStreamNewsCountRequest struct {
	BaseReq                    *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	NeedDefaultCommunityStream bool         `protobuf:"varint,2,opt,name=need_default_community_stream,json=needDefaultCommunityStream,proto3" json:"need_default_community_stream,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}     `json:"-"`
	XXX_unrecognized           []byte       `json:"-"`
	XXX_sizecache              int32        `json:"-"`
}

func (m *GetSocialCommunityContentStreamNewsCountRequest) Reset() {
	*m = GetSocialCommunityContentStreamNewsCountRequest{}
}
func (m *GetSocialCommunityContentStreamNewsCountRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetSocialCommunityContentStreamNewsCountRequest) ProtoMessage() {}
func (*GetSocialCommunityContentStreamNewsCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{160}
}
func (m *GetSocialCommunityContentStreamNewsCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityContentStreamNewsCountRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityContentStreamNewsCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityContentStreamNewsCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityContentStreamNewsCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityContentStreamNewsCountRequest.Merge(dst, src)
}
func (m *GetSocialCommunityContentStreamNewsCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityContentStreamNewsCountRequest.Size(m)
}
func (m *GetSocialCommunityContentStreamNewsCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityContentStreamNewsCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityContentStreamNewsCountRequest proto.InternalMessageInfo

func (m *GetSocialCommunityContentStreamNewsCountRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialCommunityContentStreamNewsCountRequest) GetNeedDefaultCommunityStream() bool {
	if m != nil {
		return m.NeedDefaultCommunityStream
	}
	return false
}

type GetSocialCommunityContentStreamNewsCountResponse struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Sum                  int64                         `protobuf:"varint,2,opt,name=sum,proto3" json:"sum,omitempty"`
	HasCategoryCircleMsg bool                          `protobuf:"varint,3,opt,name=has_category_circle_msg,json=hasCategoryCircleMsg,proto3" json:"has_category_circle_msg,omitempty"`
	DefaultContentStream *SocialCommunityContentStream `protobuf:"bytes,4,opt,name=default_content_stream,json=defaultContentStream,proto3" json:"default_content_stream,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetSocialCommunityContentStreamNewsCountResponse) Reset() {
	*m = GetSocialCommunityContentStreamNewsCountResponse{}
}
func (m *GetSocialCommunityContentStreamNewsCountResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetSocialCommunityContentStreamNewsCountResponse) ProtoMessage() {}
func (*GetSocialCommunityContentStreamNewsCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{161}
}
func (m *GetSocialCommunityContentStreamNewsCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityContentStreamNewsCountResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityContentStreamNewsCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityContentStreamNewsCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityContentStreamNewsCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityContentStreamNewsCountResponse.Merge(dst, src)
}
func (m *GetSocialCommunityContentStreamNewsCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityContentStreamNewsCountResponse.Size(m)
}
func (m *GetSocialCommunityContentStreamNewsCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityContentStreamNewsCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityContentStreamNewsCountResponse proto.InternalMessageInfo

func (m *GetSocialCommunityContentStreamNewsCountResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityContentStreamNewsCountResponse) GetSum() int64 {
	if m != nil {
		return m.Sum
	}
	return 0
}

func (m *GetSocialCommunityContentStreamNewsCountResponse) GetHasCategoryCircleMsg() bool {
	if m != nil {
		return m.HasCategoryCircleMsg
	}
	return false
}

func (m *GetSocialCommunityContentStreamNewsCountResponse) GetDefaultContentStream() *SocialCommunityContentStream {
	if m != nil {
		return m.DefaultContentStream
	}
	return nil
}

type SocialCommunityContentStream struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SceneId              string   `protobuf:"bytes,2,opt,name=scene_id,json=sceneId,proto3" json:"scene_id,omitempty"`
	StreamId             string   `protobuf:"bytes,3,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	StreamType           uint32   `protobuf:"varint,4,opt,name=stream_type,json=streamType,proto3" json:"stream_type,omitempty"`
	SocialName           string   `protobuf:"bytes,5,opt,name=social_name,json=socialName,proto3" json:"social_name,omitempty"`
	SocialLogo           string   `protobuf:"bytes,6,opt,name=social_logo,json=socialLogo,proto3" json:"social_logo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityContentStream) Reset()         { *m = SocialCommunityContentStream{} }
func (m *SocialCommunityContentStream) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityContentStream) ProtoMessage()    {}
func (*SocialCommunityContentStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{162}
}
func (m *SocialCommunityContentStream) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityContentStream.Unmarshal(m, b)
}
func (m *SocialCommunityContentStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityContentStream.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityContentStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityContentStream.Merge(dst, src)
}
func (m *SocialCommunityContentStream) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityContentStream.Size(m)
}
func (m *SocialCommunityContentStream) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityContentStream.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityContentStream proto.InternalMessageInfo

func (m *SocialCommunityContentStream) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *SocialCommunityContentStream) GetSceneId() string {
	if m != nil {
		return m.SceneId
	}
	return ""
}

func (m *SocialCommunityContentStream) GetStreamId() string {
	if m != nil {
		return m.StreamId
	}
	return ""
}

func (m *SocialCommunityContentStream) GetStreamType() uint32 {
	if m != nil {
		return m.StreamType
	}
	return 0
}

func (m *SocialCommunityContentStream) GetSocialName() string {
	if m != nil {
		return m.SocialName
	}
	return ""
}

func (m *SocialCommunityContentStream) GetSocialLogo() string {
	if m != nil {
		return m.SocialLogo
	}
	return ""
}

type Professionalism struct {
	BrandProfessionalism uint32   `protobuf:"varint,1,opt,name=brand_professionalism,json=brandProfessionalism,proto3" json:"brand_professionalism,omitempty"`
	PictureUrl           string   `protobuf:"bytes,2,opt,name=picture_url,json=pictureUrl,proto3" json:"picture_url,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Professionalism) Reset()         { *m = Professionalism{} }
func (m *Professionalism) String() string { return proto.CompactTextString(m) }
func (*Professionalism) ProtoMessage()    {}
func (*Professionalism) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{163}
}
func (m *Professionalism) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Professionalism.Unmarshal(m, b)
}
func (m *Professionalism) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Professionalism.Marshal(b, m, deterministic)
}
func (dst *Professionalism) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Professionalism.Merge(dst, src)
}
func (m *Professionalism) XXX_Size() int {
	return xxx_messageInfo_Professionalism.Size(m)
}
func (m *Professionalism) XXX_DiscardUnknown() {
	xxx_messageInfo_Professionalism.DiscardUnknown(m)
}

var xxx_messageInfo_Professionalism proto.InternalMessageInfo

func (m *Professionalism) GetBrandProfessionalism() uint32 {
	if m != nil {
		return m.BrandProfessionalism
	}
	return 0
}

func (m *Professionalism) GetPictureUrl() string {
	if m != nil {
		return m.PictureUrl
	}
	return ""
}

func (m *Professionalism) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type GetSocialCommunityContentStreamRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CategoryId           string       `protobuf:"bytes,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSocialCommunityContentStreamRequest) Reset() {
	*m = GetSocialCommunityContentStreamRequest{}
}
func (m *GetSocialCommunityContentStreamRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityContentStreamRequest) ProtoMessage()    {}
func (*GetSocialCommunityContentStreamRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{164}
}
func (m *GetSocialCommunityContentStreamRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityContentStreamRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityContentStreamRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityContentStreamRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityContentStreamRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityContentStreamRequest.Merge(dst, src)
}
func (m *GetSocialCommunityContentStreamRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityContentStreamRequest.Size(m)
}
func (m *GetSocialCommunityContentStreamRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityContentStreamRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityContentStreamRequest proto.InternalMessageInfo

func (m *GetSocialCommunityContentStreamRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSocialCommunityContentStreamRequest) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetSocialCommunityContentStreamRequest) GetCategoryId() string {
	if m != nil {
		return m.CategoryId
	}
	return ""
}

type GetSocialCommunityContentStreamResponse struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Stream               *SocialCommunityContentStream `protobuf:"bytes,2,opt,name=stream,proto3" json:"stream,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetSocialCommunityContentStreamResponse) Reset() {
	*m = GetSocialCommunityContentStreamResponse{}
}
func (m *GetSocialCommunityContentStreamResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityContentStreamResponse) ProtoMessage()    {}
func (*GetSocialCommunityContentStreamResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_logic_6056c560c8e96d59, []int{165}
}
func (m *GetSocialCommunityContentStreamResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityContentStreamResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityContentStreamResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityContentStreamResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityContentStreamResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityContentStreamResponse.Merge(dst, src)
}
func (m *GetSocialCommunityContentStreamResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityContentStreamResponse.Size(m)
}
func (m *GetSocialCommunityContentStreamResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityContentStreamResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityContentStreamResponse proto.InternalMessageInfo

func (m *GetSocialCommunityContentStreamResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSocialCommunityContentStreamResponse) GetStream() *SocialCommunityContentStream {
	if m != nil {
		return m.Stream
	}
	return nil
}

func init() {
	proto.RegisterType((*ListMuseSocialCommunityNavBarsRequest)(nil), "ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsRequest")
	proto.RegisterType((*ListMuseSocialCommunityNavBarsResponse)(nil), "ga.muse_social_community_logic.ListMuseSocialCommunityNavBarsResponse")
	proto.RegisterType((*ListMuseSocialCommunityNavSecondaryBarsRequest)(nil), "ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsRequest")
	proto.RegisterType((*ListMuseSocialCommunityNavSecondaryBarsResponse)(nil), "ga.muse_social_community_logic.ListMuseSocialCommunityNavSecondaryBarsResponse")
	proto.RegisterType((*MuseSocialCommunityNavBar)(nil), "ga.muse_social_community_logic.MuseSocialCommunityNavBar")
	proto.RegisterType((*MuseSocialCommunityNavSecondaryBar)(nil), "ga.muse_social_community_logic.MuseSocialCommunityNavSecondaryBar")
	proto.RegisterType((*MuseSocialCommunityContentStream)(nil), "ga.muse_social_community_logic.MuseSocialCommunityContentStream")
	proto.RegisterType((*MuseSocialCommunityAnnounce)(nil), "ga.muse_social_community_logic.MuseSocialCommunityAnnounce")
	proto.RegisterType((*MuseSocialCommunityChannel)(nil), "ga.muse_social_community_logic.MuseSocialCommunityChannel")
	proto.RegisterType((*MuseSocialCommunityGroup)(nil), "ga.muse_social_community_logic.MuseSocialCommunityGroup")
	proto.RegisterType((*MuseSocialCommunityNavHome)(nil), "ga.muse_social_community_logic.MuseSocialCommunityNavHome")
	proto.RegisterType((*MuseSocialCommunityNavChatChannel)(nil), "ga.muse_social_community_logic.MuseSocialCommunityNavChatChannel")
	proto.RegisterType((*MuseSocialCommunityNavShowChannel)(nil), "ga.muse_social_community_logic.MuseSocialCommunityNavShowChannel")
	proto.RegisterType((*BrandMember)(nil), "ga.muse_social_community_logic.BrandMember")
	proto.RegisterType((*BatGetMuseSocialCommunityUsersRoleRequest)(nil), "ga.muse_social_community_logic.BatGetMuseSocialCommunityUsersRoleRequest")
	proto.RegisterType((*BatGetMuseSocialCommunityUsersRoleResponse)(nil), "ga.muse_social_community_logic.BatGetMuseSocialCommunityUsersRoleResponse")
	proto.RegisterMapType((map[uint32]*BrandMember)(nil), "ga.muse_social_community_logic.BatGetMuseSocialCommunityUsersRoleResponse.UidRoleMapEntry")
	proto.RegisterType((*GetSocialCommunityDetailRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityDetailRequest")
	proto.RegisterType((*GetSocialCommunityDetailResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityDetailResponse")
	proto.RegisterType((*SocialCommunityDetail)(nil), "ga.muse_social_community_logic.SocialCommunityDetail")
	proto.RegisterType((*JoinSocialCommunityFansRequest)(nil), "ga.muse_social_community_logic.JoinSocialCommunityFansRequest")
	proto.RegisterType((*JoinSocialCommunityFansResponse)(nil), "ga.muse_social_community_logic.JoinSocialCommunityFansResponse")
	proto.RegisterType((*SocialCommunityChannelPush)(nil), "ga.muse_social_community_logic.SocialCommunityChannelPush")
	proto.RegisterType((*SimpleUserInfo)(nil), "ga.muse_social_community_logic.SimpleUserInfo")
	proto.RegisterType((*GetMySocialCommunityRequest)(nil), "ga.muse_social_community_logic.GetMySocialCommunityRequest")
	proto.RegisterType((*GetMySocialCommunityResponse)(nil), "ga.muse_social_community_logic.GetMySocialCommunityResponse")
	proto.RegisterType((*SendWelcomePushRequest)(nil), "ga.muse_social_community_logic.SendWelcomePushRequest")
	proto.RegisterType((*SendWelcomePushResponse)(nil), "ga.muse_social_community_logic.SendWelcomePushResponse")
	proto.RegisterType((*JoinFansWelcomePush)(nil), "ga.muse_social_community_logic.JoinFansWelcomePush")
	proto.RegisterType((*GetSocialCommunityProfilePagesRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityProfilePagesRequest")
	proto.RegisterType((*SocialCommunityMemberInfo)(nil), "ga.muse_social_community_logic.SocialCommunityMemberInfo")
	proto.RegisterType((*SocialCommunityPhotoAlbumKeyURL)(nil), "ga.muse_social_community_logic.SocialCommunityPhotoAlbumKeyURL")
	proto.RegisterType((*UserInfoInTheCommunity)(nil), "ga.muse_social_community_logic.UserInfoInTheCommunity")
	proto.RegisterType((*InviteMembersDisplay)(nil), "ga.muse_social_community_logic.InviteMembersDisplay")
	proto.RegisterType((*GetSocialCommunityProfilePagesResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityProfilePagesResponse")
	proto.RegisterType((*BatGetRankInChannelRequest)(nil), "ga.muse_social_community_logic.BatGetRankInChannelRequest")
	proto.RegisterType((*RankInChannelInfo)(nil), "ga.muse_social_community_logic.RankInChannelInfo")
	proto.RegisterType((*SocialRankHonorSignInfo)(nil), "ga.muse_social_community_logic.SocialRankHonorSignInfo")
	proto.RegisterType((*BatGetRankInChannelResponse)(nil), "ga.muse_social_community_logic.BatGetRankInChannelResponse")
	proto.RegisterMapType((map[uint32]*RankInChannelInfo)(nil), "ga.muse_social_community_logic.BatGetRankInChannelResponse.RankInChannelInfoMapEntry")
	proto.RegisterType((*RankInChannelNotify)(nil), "ga.muse_social_community_logic.RankInChannelNotify")
	proto.RegisterType((*RemoveSocialCommunityMemberRequest)(nil), "ga.muse_social_community_logic.RemoveSocialCommunityMemberRequest")
	proto.RegisterType((*RemoveSocialCommunityMemberResponse)(nil), "ga.muse_social_community_logic.RemoveSocialCommunityMemberResponse")
	proto.RegisterType((*ExitSocialCommunityRequest)(nil), "ga.muse_social_community_logic.ExitSocialCommunityRequest")
	proto.RegisterType((*ExitSocialCommunityResponse)(nil), "ga.muse_social_community_logic.ExitSocialCommunityResponse")
	proto.RegisterType((*UpdateMemberRoleRequest)(nil), "ga.muse_social_community_logic.UpdateMemberRoleRequest")
	proto.RegisterType((*UpdateMemberRoleResponse)(nil), "ga.muse_social_community_logic.UpdateMemberRoleResponse")
	proto.RegisterType((*GetSocialCommunityRoleLeftNumbersRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityRoleLeftNumbersRequest")
	proto.RegisterType((*GetSocialCommunityRoleLeftNumbersResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityRoleLeftNumbersResponse")
	proto.RegisterMapType((map[uint32]int32)(nil), "ga.muse_social_community_logic.GetSocialCommunityRoleLeftNumbersResponse.RoleLeftNumbersMapEntry")
	proto.RegisterType((*GetChannelAssociateSocialCommunityRequest)(nil), "ga.muse_social_community_logic.GetChannelAssociateSocialCommunityRequest")
	proto.RegisterType((*GetChannelAssociateSocialCommunityResponse)(nil), "ga.muse_social_community_logic.GetChannelAssociateSocialCommunityResponse")
	proto.RegisterType((*GetMySocialCommunityPageRequest)(nil), "ga.muse_social_community_logic.GetMySocialCommunityPageRequest")
	proto.RegisterType((*GetMySocialCommunityPageResponse)(nil), "ga.muse_social_community_logic.GetMySocialCommunityPageResponse")
	proto.RegisterType((*SocialCommunityView)(nil), "ga.muse_social_community_logic.SocialCommunityView")
	proto.RegisterType((*SocialCommunityViewLabel)(nil), "ga.muse_social_community_logic.SocialCommunityViewLabel")
	proto.RegisterType((*ListCategoryTypesRequest)(nil), "ga.muse_social_community_logic.ListCategoryTypesRequest")
	proto.RegisterType((*ListCategoryTypesResponse)(nil), "ga.muse_social_community_logic.ListCategoryTypesResponse")
	proto.RegisterType((*SocialCommunityCategoryType)(nil), "ga.muse_social_community_logic.SocialCommunityCategoryType")
	proto.RegisterType((*ListCategoriesRequest)(nil), "ga.muse_social_community_logic.ListCategoriesRequest")
	proto.RegisterType((*ListCategoriesResponse)(nil), "ga.muse_social_community_logic.ListCategoriesResponse")
	proto.RegisterType((*SocialCommunityCategory)(nil), "ga.muse_social_community_logic.SocialCommunityCategory")
	proto.RegisterType((*ApplyCreateSocialCommunityRequest)(nil), "ga.muse_social_community_logic.ApplyCreateSocialCommunityRequest")
	proto.RegisterType((*ApplyCreateSocialCommunityResponse)(nil), "ga.muse_social_community_logic.ApplyCreateSocialCommunityResponse")
	proto.RegisterType((*GetSocialCommunityFloatRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityFloatRequest")
	proto.RegisterType((*GetSocialCommunityFloatResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityFloatResponse")
	proto.RegisterType((*JoinSocialCommunityRequest)(nil), "ga.muse_social_community_logic.JoinSocialCommunityRequest")
	proto.RegisterType((*JoinSocialCommunityResponse)(nil), "ga.muse_social_community_logic.JoinSocialCommunityResponse")
	proto.RegisterType((*ValidateUserHasCreateQualificationRequest)(nil), "ga.muse_social_community_logic.ValidateUserHasCreateQualificationRequest")
	proto.RegisterType((*ValidateUserHasCreateQualificationResponse)(nil), "ga.muse_social_community_logic.ValidateUserHasCreateQualificationResponse")
	proto.RegisterType((*GetSocialCommunityBaseRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityBaseRequest")
	proto.RegisterType((*GetSocialCommunityBaseResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityBaseResponse")
	proto.RegisterType((*GetUserSocialGroupIdsRequest)(nil), "ga.muse_social_community_logic.GetUserSocialGroupIdsRequest")
	proto.RegisterType((*GetUserSocialGroupIdsResponse)(nil), "ga.muse_social_community_logic.GetUserSocialGroupIdsResponse")
	proto.RegisterType((*SocialSimpleGroup)(nil), "ga.muse_social_community_logic.SocialSimpleGroup")
	proto.RegisterType((*MuseSocialPreviewGroupMessageRequest)(nil), "ga.muse_social_community_logic.MuseSocialPreviewGroupMessageRequest")
	proto.RegisterType((*MuseSocialPreviewGroupMessageResponse)(nil), "ga.muse_social_community_logic.MuseSocialPreviewGroupMessageResponse")
	proto.RegisterType((*MuseSocialGroupRemoveAdminRequest)(nil), "ga.muse_social_community_logic.MuseSocialGroupRemoveAdminRequest")
	proto.RegisterType((*MuseSocialGroupRemoveAdminResponse)(nil), "ga.muse_social_community_logic.MuseSocialGroupRemoveAdminResponse")
	proto.RegisterType((*MuseSocialGroupSetAllMuteRequest)(nil), "ga.muse_social_community_logic.MuseSocialGroupSetAllMuteRequest")
	proto.RegisterType((*MuseSocialGroupSetAllMuteResponse)(nil), "ga.muse_social_community_logic.MuseSocialGroupSetAllMuteResponse")
	proto.RegisterType((*MuseSocialGroupMuteMemberRequest)(nil), "ga.muse_social_community_logic.MuseSocialGroupMuteMemberRequest")
	proto.RegisterType((*MuseSocialGroupMuteMemberResponse)(nil), "ga.muse_social_community_logic.MuseSocialGroupMuteMemberResponse")
	proto.RegisterType((*MuseSocialGroupUnmuteMemberRequest)(nil), "ga.muse_social_community_logic.MuseSocialGroupUnmuteMemberRequest")
	proto.RegisterType((*MuseSocialGroupUnmuteMemberResponse)(nil), "ga.muse_social_community_logic.MuseSocialGroupUnmuteMemberResponse")
	proto.RegisterType((*MuseSocialGroupGetMuteListRequest)(nil), "ga.muse_social_community_logic.MuseSocialGroupGetMuteListRequest")
	proto.RegisterType((*MuseSocialMuteUserInfo)(nil), "ga.muse_social_community_logic.MuseSocialMuteUserInfo")
	proto.RegisterType((*MuseSocialGroupGetMuteListResponse)(nil), "ga.muse_social_community_logic.MuseSocialGroupGetMuteListResponse")
	proto.RegisterType((*MuseSocialGroupGetMemberListRequest)(nil), "ga.muse_social_community_logic.MuseSocialGroupGetMemberListRequest")
	proto.RegisterType((*LoadMoreMembers)(nil), "ga.muse_social_community_logic.LoadMoreMembers")
	proto.RegisterType((*MuseSocialGroupGetMemberListResponse)(nil), "ga.muse_social_community_logic.MuseSocialGroupGetMemberListResponse")
	proto.RegisterType((*MuseSocialGroupMemberInfo)(nil), "ga.muse_social_community_logic.MuseSocialGroupMemberInfo")
	proto.RegisterType((*MuseSocialGroupGetDetailInfoRequest)(nil), "ga.muse_social_community_logic.MuseSocialGroupGetDetailInfoRequest")
	proto.RegisterType((*MuseSocialGroupGetDetailInfoResponse)(nil), "ga.muse_social_community_logic.MuseSocialGroupGetDetailInfoResponse")
	proto.RegisterType((*MuseSocialGroupDetailInfo)(nil), "ga.muse_social_community_logic.MuseSocialGroupDetailInfo")
	proto.RegisterType((*Share)(nil), "ga.muse_social_community_logic.Share")
	proto.RegisterType((*GroupLeader)(nil), "ga.muse_social_community_logic.GroupLeader")
	proto.RegisterType((*MuseSocialGroupAddAdminRequest)(nil), "ga.muse_social_community_logic.MuseSocialGroupAddAdminRequest")
	proto.RegisterType((*MuseSocialGroupAddAdminResponse)(nil), "ga.muse_social_community_logic.MuseSocialGroupAddAdminResponse")
	proto.RegisterType((*GetSocialGroupOnlineMembersRequest)(nil), "ga.muse_social_community_logic.GetSocialGroupOnlineMembersRequest")
	proto.RegisterType((*GetSocialGroupOnlineMembersResponse)(nil), "ga.muse_social_community_logic.GetSocialGroupOnlineMembersResponse")
	proto.RegisterType((*BatGetSocialCommunityKernelMembersRequest)(nil), "ga.muse_social_community_logic.BatGetSocialCommunityKernelMembersRequest")
	proto.RegisterType((*BatGetSocialCommunityKernelMembersResponse)(nil), "ga.muse_social_community_logic.BatGetSocialCommunityKernelMembersResponse")
	proto.RegisterMapType((map[string]*BrandMembers)(nil), "ga.muse_social_community_logic.BatGetSocialCommunityKernelMembersResponse.KernelMemberMapEntry")
	proto.RegisterType((*BrandMembers)(nil), "ga.muse_social_community_logic.BrandMembers")
	proto.RegisterType((*GetGroupActiveMembersRequest)(nil), "ga.muse_social_community_logic.GetGroupActiveMembersRequest")
	proto.RegisterType((*MemberDetail)(nil), "ga.muse_social_community_logic.MemberDetail")
	proto.RegisterType((*SocialCommunityChannelSimple)(nil), "ga.muse_social_community_logic.SocialCommunityChannelSimple")
	proto.RegisterType((*GetGroupActiveMembersResponse)(nil), "ga.muse_social_community_logic.GetGroupActiveMembersResponse")
	proto.RegisterType((*GetSocialCommunityMemberListRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityMemberListRequest")
	proto.RegisterType((*GetSocialCommunityMemberListResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityMemberListResponse")
	proto.RegisterType((*GetSocialCommunityAnnounceNewsCountRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityAnnounceNewsCountRequest")
	proto.RegisterType((*GetSocialCommunityAnnounceNewsCountResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityAnnounceNewsCountResponse")
	proto.RegisterType((*ListMuseSocialAnnouncesRequest)(nil), "ga.muse_social_community_logic.ListMuseSocialAnnouncesRequest")
	proto.RegisterType((*ListMuseSocialAnnouncesResponse)(nil), "ga.muse_social_community_logic.ListMuseSocialAnnouncesResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "ga.muse_social_community_logic.ListMuseSocialAnnouncesResponse.UnreadedListTypeMapEntry")
	proto.RegisterType((*MuseSocialAnnounce)(nil), "ga.muse_social_community_logic.MuseSocialAnnounce")
	proto.RegisterType((*MuseAnnounceDestinationChoose)(nil), "ga.muse_social_community_logic.MuseAnnounceDestinationChoose")
	proto.RegisterType((*MuseAnnounceNoDestination)(nil), "ga.muse_social_community_logic.MuseAnnounceNoDestination")
	proto.RegisterType((*MuseAnnounceShowChanelDestination)(nil), "ga.muse_social_community_logic.MuseAnnounceShowChanelDestination")
	proto.RegisterType((*MuseAnnounceChatChanelDestination)(nil), "ga.muse_social_community_logic.MuseAnnounceChatChanelDestination")
	proto.RegisterType((*MuseAnnouncePersonalChanelDestination)(nil), "ga.muse_social_community_logic.MuseAnnouncePersonalChanelDestination")
	proto.RegisterType((*MuseAnnounceGroupDestination)(nil), "ga.muse_social_community_logic.MuseAnnounceGroupDestination")
	proto.RegisterType((*ListAnnounceDestinationsRequest)(nil), "ga.muse_social_community_logic.ListAnnounceDestinationsRequest")
	proto.RegisterType((*ListAnnounceDestinationsResponse)(nil), "ga.muse_social_community_logic.ListAnnounceDestinationsResponse")
	proto.RegisterType((*MuseAnnounceDestinationGroup)(nil), "ga.muse_social_community_logic.MuseAnnounceDestinationGroup")
	proto.RegisterType((*UpsertMuseSocialAnnounceRequest)(nil), "ga.muse_social_community_logic.UpsertMuseSocialAnnounceRequest")
	proto.RegisterType((*UpsertMuseSocialAnnounceResponse)(nil), "ga.muse_social_community_logic.UpsertMuseSocialAnnounceResponse")
	proto.RegisterType((*SetMuseSocialAnnounceInterestRequest)(nil), "ga.muse_social_community_logic.SetMuseSocialAnnounceInterestRequest")
	proto.RegisterType((*SetMuseSocialAnnounceInterestResponse)(nil), "ga.muse_social_community_logic.SetMuseSocialAnnounceInterestResponse")
	proto.RegisterType((*RemoveMuseSocialAnnounceRequest)(nil), "ga.muse_social_community_logic.RemoveMuseSocialAnnounceRequest")
	proto.RegisterType((*RemoveMuseSocialAnnounceResponse)(nil), "ga.muse_social_community_logic.RemoveMuseSocialAnnounceResponse")
	proto.RegisterType((*ListMuseSocialAnnounceInterestUsersRequest)(nil), "ga.muse_social_community_logic.ListMuseSocialAnnounceInterestUsersRequest")
	proto.RegisterType((*ListMuseSocialAnnounceInterestUsersResponse)(nil), "ga.muse_social_community_logic.ListMuseSocialAnnounceInterestUsersResponse")
	proto.RegisterType((*ValidateUserHasCreateAnnouncePermissionsRequest)(nil), "ga.muse_social_community_logic.ValidateUserHasCreateAnnouncePermissionsRequest")
	proto.RegisterType((*ValidateUserHasCreateAnnouncePermissionsResponse)(nil), "ga.muse_social_community_logic.ValidateUserHasCreateAnnouncePermissionsResponse")
	proto.RegisterType((*SetCommunityAdditionModeRequest)(nil), "ga.muse_social_community_logic.SetCommunityAdditionModeRequest")
	proto.RegisterType((*SetCommunityAdditionModeResponse)(nil), "ga.muse_social_community_logic.SetCommunityAdditionModeResponse")
	proto.RegisterType((*GetCommunityAdditionModeRequest)(nil), "ga.muse_social_community_logic.GetCommunityAdditionModeRequest")
	proto.RegisterType((*AdditionMode)(nil), "ga.muse_social_community_logic.AdditionMode")
	proto.RegisterType((*GetCommunityAdditionModeResponse)(nil), "ga.muse_social_community_logic.GetCommunityAdditionModeResponse")
	proto.RegisterType((*ListSocialCommunitySystemMessageRequest)(nil), "ga.muse_social_community_logic.ListSocialCommunitySystemMessageRequest")
	proto.RegisterType((*SocialCommunitySystemMessage)(nil), "ga.muse_social_community_logic.SocialCommunitySystemMessage")
	proto.RegisterType((*JoinSocialCommunityMessage)(nil), "ga.muse_social_community_logic.JoinSocialCommunityMessage")
	proto.RegisterType((*ListSocialCommunitySystemMessageResponse)(nil), "ga.muse_social_community_logic.ListSocialCommunitySystemMessageResponse")
	proto.RegisterType((*SubmitApplicationToJoinCommunityRequest)(nil), "ga.muse_social_community_logic.SubmitApplicationToJoinCommunityRequest")
	proto.RegisterType((*SubmitApplicationToJoinCommunityResponse)(nil), "ga.muse_social_community_logic.SubmitApplicationToJoinCommunityResponse")
	proto.RegisterType((*UpsertJoinSocialCommunityMessageStatusRequest)(nil), "ga.muse_social_community_logic.UpsertJoinSocialCommunityMessageStatusRequest")
	proto.RegisterType((*UpsertJoinSocialCommunityMessageStatusResponse)(nil), "ga.muse_social_community_logic.UpsertJoinSocialCommunityMessageStatusResponse")
	proto.RegisterType((*GetSocialCommunityUpdateLevelTipRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityUpdateLevelTipRequest")
	proto.RegisterType((*GetSocialCommunityUpdateLevelTipResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityUpdateLevelTipResponse")
	proto.RegisterType((*GetSocialCommunityLevelDetailRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityLevelDetailRequest")
	proto.RegisterType((*GetSocialCommunityLevelDetailResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityLevelDetailResponse")
	proto.RegisterType((*SocialCommunityRightGroup)(nil), "ga.muse_social_community_logic.SocialCommunityRightGroup")
	proto.RegisterType((*SocialCommunityLevelCard)(nil), "ga.muse_social_community_logic.SocialCommunityLevelCard")
	proto.RegisterType((*SocialCommunityRight)(nil), "ga.muse_social_community_logic.SocialCommunityRight")
	proto.RegisterType((*SocialCommunityTaskGroup)(nil), "ga.muse_social_community_logic.SocialCommunityTaskGroup")
	proto.RegisterType((*SocialCommunityTask)(nil), "ga.muse_social_community_logic.SocialCommunityTask")
	proto.RegisterType((*SocialCommunityNormalTaskView)(nil), "ga.muse_social_community_logic.SocialCommunityNormalTaskView")
	proto.RegisterType((*SocialCommunityCheckInTaskView)(nil), "ga.muse_social_community_logic.SocialCommunityCheckInTaskView")
	proto.RegisterMapType((map[uint32]string)(nil), "ga.muse_social_community_logic.SocialCommunityCheckInTaskView.StatusButtonTextEntry")
	proto.RegisterType((*SocialCommunityStepTaskView)(nil), "ga.muse_social_community_logic.SocialCommunityStepTaskView")
	proto.RegisterType((*SocialCommunityStep)(nil), "ga.muse_social_community_logic.SocialCommunityStep")
	proto.RegisterType((*SocialCommunityCheckInRequest)(nil), "ga.muse_social_community_logic.SocialCommunityCheckInRequest")
	proto.RegisterType((*SocialCommunityCheckInResponse)(nil), "ga.muse_social_community_logic.SocialCommunityCheckInResponse")
	proto.RegisterType((*GroupMessageTaskDoneNotify)(nil), "ga.muse_social_community_logic.GroupMessageTaskDoneNotify")
	proto.RegisterType((*GetSocialCommunityContentStreamNewsCountRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityContentStreamNewsCountRequest")
	proto.RegisterType((*GetSocialCommunityContentStreamNewsCountResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityContentStreamNewsCountResponse")
	proto.RegisterType((*SocialCommunityContentStream)(nil), "ga.muse_social_community_logic.SocialCommunityContentStream")
	proto.RegisterType((*Professionalism)(nil), "ga.muse_social_community_logic.Professionalism")
	proto.RegisterType((*GetSocialCommunityContentStreamRequest)(nil), "ga.muse_social_community_logic.GetSocialCommunityContentStreamRequest")
	proto.RegisterType((*GetSocialCommunityContentStreamResponse)(nil), "ga.muse_social_community_logic.GetSocialCommunityContentStreamResponse")
	proto.RegisterEnum("ga.muse_social_community_logic.MuseSocialCommunityNavBarFlag", MuseSocialCommunityNavBarFlag_name, MuseSocialCommunityNavBarFlag_value)
	proto.RegisterEnum("ga.muse_social_community_logic.BrandChannelType", BrandChannelType_name, BrandChannelType_value)
	proto.RegisterEnum("ga.muse_social_community_logic.BrandMemberRoleV2", BrandMemberRoleV2_name, BrandMemberRoleV2_value)
	proto.RegisterEnum("ga.muse_social_community_logic.SocialCommunityRankType", SocialCommunityRankType_name, SocialCommunityRankType_value)
	proto.RegisterEnum("ga.muse_social_community_logic.MemberStatus", MemberStatus_name, MemberStatus_value)
	proto.RegisterEnum("ga.muse_social_community_logic.SocialCommunityOwnerStatus", SocialCommunityOwnerStatus_name, SocialCommunityOwnerStatus_value)
	proto.RegisterEnum("ga.muse_social_community_logic.BrandProfession", BrandProfession_name, BrandProfession_value)
	proto.RegisterEnum("ga.muse_social_community_logic.SocialCommunityViewStatus", SocialCommunityViewStatus_name, SocialCommunityViewStatus_value)
	proto.RegisterEnum("ga.muse_social_community_logic.SocialCommunityViewType", SocialCommunityViewType_name, SocialCommunityViewType_value)
	proto.RegisterEnum("ga.muse_social_community_logic.BrandProfessionalism", BrandProfessionalism_name, BrandProfessionalism_value)
	proto.RegisterEnum("ga.muse_social_community_logic.ListAnnouncesType", ListAnnouncesType_name, ListAnnouncesType_value)
	proto.RegisterEnum("ga.muse_social_community_logic.MuseAnnounceDestinationType", MuseAnnounceDestinationType_name, MuseAnnounceDestinationType_value)
	proto.RegisterEnum("ga.muse_social_community_logic.SetInterestType", SetInterestType_name, SetInterestType_value)
	proto.RegisterEnum("ga.muse_social_community_logic.SocialCommunitySystemMessageType", SocialCommunitySystemMessageType_name, SocialCommunitySystemMessageType_value)
	proto.RegisterEnum("ga.muse_social_community_logic.AdditionSocialCommunityMode", AdditionSocialCommunityMode_name, AdditionSocialCommunityMode_value)
	proto.RegisterEnum("ga.muse_social_community_logic.JoinSocialCommunityMessageStatus", JoinSocialCommunityMessageStatus_name, JoinSocialCommunityMessageStatus_value)
	proto.RegisterEnum("ga.muse_social_community_logic.TaskViewType", TaskViewType_name, TaskViewType_value)
	proto.RegisterEnum("ga.muse_social_community_logic.MuseSocialGroupMemberInfo_Role", MuseSocialGroupMemberInfo_Role_name, MuseSocialGroupMemberInfo_Role_value)
}

func init() {
	proto.RegisterFile("muse_social_community_logic/muse_social_community_logic.proto", fileDescriptor_muse_social_community_logic_6056c560c8e96d59)
}

var fileDescriptor_muse_social_community_logic_6056c560c8e96d59 = []byte{
	// 7853 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x7d, 0x5b, 0x6c, 0x24, 0xc9,
	0x91, 0xd8, 0x54, 0x77, 0x93, 0xec, 0x8e, 0xe6, 0xa3, 0xa7, 0x86, 0xc3, 0xe1, 0x90, 0xc3, 0x21,
	0xa7, 0xe6, 0xcd, 0xdd, 0xe1, 0xac, 0xb8, 0xbb, 0x7a, 0xaf, 0xed, 0x26, 0xd9, 0x33, 0x6c, 0x0d,
	0xd9, 0xa4, 0xaa, 0x9b, 0x33, 0xda, 0xf5, 0xa3, 0x54, 0xec, 0x4a, 0x36, 0x6b, 0x58, 0x5d, 0xd5,
	0xaa, 0xaa, 0x1e, 0x0e, 0x2d, 0xc9, 0xb2, 0x5e, 0x10, 0x56, 0x86, 0x20, 0x18, 0x92, 0x61, 0x4b,
	0x90, 0xb5, 0x86, 0x21, 0xc3, 0xb6, 0x0c, 0x43, 0xb2, 0x25, 0x7d, 0x58, 0xb0, 0x80, 0x83, 0x74,
	0xc0, 0x9d, 0x3e, 0xee, 0xe7, 0x70, 0x3a, 0xdd, 0x8f, 0x70, 0x5f, 0x77, 0x80, 0x70, 0x3f, 0xf7,
	0x75, 0xb8, 0x9f, 0xc3, 0x1d, 0xf2, 0x51, 0xd5, 0x59, 0x8f, 0x7e, 0x54, 0x0f, 0xb9, 0x5a, 0xdc,
	0x5f, 0x55, 0x64, 0x54, 0x44, 0x64, 0x64, 0x64, 0x64, 0x64, 0x66, 0x64, 0x16, 0xbc, 0xd1, 0x6c,
	0x3b, 0x48, 0x71, 0xac, 0xba, 0xae, 0x1a, 0x4a, 0xdd, 0x6a, 0x36, 0xdb, 0xa6, 0xee, 0x9e, 0x28,
	0x86, 0xd5, 0xd0, 0xeb, 0xf7, 0x7b, 0x94, 0xad, 0xb4, 0x6c, 0xcb, 0xb5, 0xc4, 0xab, 0x0d, 0x75,
	0xa5, 0x07, 0xd6, 0xdc, 0x44, 0x43, 0x55, 0xf6, 0x55, 0x07, 0x51, 0xf4, 0xb9, 0xa5, 0x66, 0xdb,
	0xd1, 0xeb, 0xf7, 0x5c, 0xab, 0xa5, 0xd7, 0xef, 0xd5, 0x0f, 0x55, 0xd3, 0x44, 0xc6, 0x3d, 0x82,
	0xa9, 0x30, 0x0c, 0x70, 0x4e, 0x4c, 0x46, 0x5c, 0xfa, 0x3d, 0x01, 0x6e, 0x6e, 0xe9, 0x8e, 0xbb,
	0xdd, 0x76, 0x50, 0x95, 0xd0, 0x5f, 0xf7, 0xc8, 0x57, 0xd4, 0x67, 0x6b, 0xaa, 0xed, 0xc8, 0xe8,
	0x53, 0x6d, 0xe4, 0xb8, 0xe2, 0x2d, 0xc8, 0x62, 0x2e, 0x8a, 0x8d, 0x3e, 0x35, 0x2b, 0x2c, 0x09,
	0x77, 0xf2, 0xab, 0xf9, 0x95, 0x86, 0xba, 0xb2, 0xa6, 0x3a, 0x48, 0x46, 0x9f, 0x92, 0xc7, 0xf6,
	0xe9, 0x83, 0xb8, 0x00, 0xc0, 0xb8, 0x2a, 0xba, 0x36, 0x9b, 0x5a, 0x12, 0xee, 0x4c, 0xc8, 0x39,
	0x06, 0x29, 0x6b, 0xe2, 0x12, 0x8c, 0xa3, 0xe7, 0xae, 0xad, 0x2a, 0xa6, 0xfa, 0x0c, 0x23, 0xa4,
	0x97, 0x84, 0x3b, 0x39, 0x19, 0x08, 0xac, 0xa2, 0x3e, 0x2b, 0x6b, 0xe2, 0x0a, 0x5c, 0x88, 0xd4,
	0x54, 0xd7, 0x66, 0x33, 0x04, 0xf1, 0xbc, 0x13, 0x14, 0xb2, 0xac, 0x49, 0xbf, 0x11, 0xe0, 0x56,
	0xbf, 0x2a, 0x38, 0x2d, 0xcb, 0x74, 0x90, 0x78, 0x17, 0x72, 0xac, 0x0e, 0x4e, 0x8b, 0x55, 0x62,
	0xbc, 0x53, 0x09, 0xa7, 0x25, 0x67, 0xf7, 0xd9, 0x93, 0xb8, 0x0d, 0x99, 0x7d, 0xd5, 0x76, 0x66,
	0x53, 0x4b, 0xe9, 0x3b, 0xf9, 0xd5, 0x0f, 0xad, 0xf4, 0x6e, 0x84, 0x95, 0xae, 0xcc, 0x65, 0x42,
	0x46, 0xfc, 0x10, 0x5c, 0x6e, 0x9b, 0x36, 0x52, 0x35, 0x45, 0x37, 0x5d, 0x64, 0xab, 0x75, 0x57,
	0x69, 0x3a, 0x0d, 0xa5, 0x6e, 0xb5, 0x4d, 0x97, 0xe8, 0x20, 0x2d, 0xcf, 0x50, 0x84, 0x32, 0x2b,
	0xdf, 0x76, 0x1a, 0xeb, 0xb8, 0x54, 0x3a, 0x84, 0x95, 0xee, 0xd5, 0xab, 0xa2, 0xba, 0x65, 0x6a,
	0xaa, 0x7d, 0x32, 0x4c, 0x53, 0x4d, 0x42, 0x8a, 0x35, 0x51, 0x4e, 0x4e, 0xe9, 0x9a, 0xf4, 0x07,
	0x02, 0xdc, 0x1f, 0x98, 0x55, 0x72, 0x95, 0xea, 0x30, 0xe9, 0x78, 0x34, 0x14, 0x4e, 0xb9, 0x6b,
	0xc3, 0x29, 0x97, 0x97, 0x47, 0x9e, 0x70, 0x78, 0xe9, 0xa4, 0xbf, 0xcb, 0xc0, 0xe5, 0xae, 0x4d,
	0xd2, 0xcd, 0xc2, 0x84, 0x2e, 0x16, 0x26, 0x8a, 0x90, 0x31, 0xd5, 0x26, 0x62, 0x9a, 0x22, 0xcf,
	0x18, 0x66, 0x58, 0x0d, 0x8b, 0xd9, 0x2f, 0x79, 0x16, 0x2f, 0xc1, 0xd8, 0x7e, 0x43, 0x21, 0x60,
	0x6a, 0xad, 0xa3, 0xfb, 0x8d, 0x2d, 0x5c, 0xf0, 0x71, 0xc8, 0x1c, 0x18, 0x6a, 0x63, 0x76, 0x64,
	0x49, 0xb8, 0x33, 0xb9, 0xfa, 0xc6, 0xd0, 0xc6, 0xf4, 0xc0, 0x50, 0x1b, 0x32, 0x21, 0x85, 0x0d,
	0xaa, 0xae, 0xba, 0xa8, 0x61, 0xd9, 0x27, 0x8a, 0x7b, 0xd2, 0x42, 0x8a, 0xa3, 0x37, 0x5b, 0x06,
	0x52, 0x34, 0xe4, 0xd4, 0x67, 0x47, 0x09, 0xf7, 0x19, 0x0f, 0xa1, 0x76, 0xd2, 0x42, 0x55, 0x52,
	0xbc, 0x81, 0x9c, 0x7a, 0x4c, 0x3b, 0x8c, 0x9d, 0x51, 0x3b, 0x88, 0xaf, 0xc2, 0xc5, 0x7d, 0x5b,
	0x35, 0x35, 0xa5, 0x65, 0x5b, 0x07, 0xc8, 0x71, 0x74, 0xcb, 0x54, 0x0d, 0xdd, 0x69, 0xce, 0x66,
	0x89, 0x5f, 0x98, 0x26, 0x85, 0xbb, 0xc1, 0x32, 0x71, 0x1a, 0x46, 0x0c, 0xf4, 0x0c, 0x19, 0xb3,
	0x39, 0x82, 0x44, 0x5f, 0xb0, 0x5f, 0x21, 0x0f, 0x54, 0xbf, 0x40, 0x6a, 0x98, 0x23, 0x10, 0xa2,
	0xe2, 0x3b, 0x50, 0x60, 0x1d, 0xac, 0xd3, 0xaf, 0xf2, 0xa4, 0x5f, 0x4d, 0x52, 0xb8, 0xd7, 0x9f,
	0xc4, 0x7d, 0x98, 0x0e, 0x49, 0xa3, 0xe8, 0xe6, 0x81, 0x35, 0x3b, 0x4e, 0x8c, 0xf7, 0x7e, 0x3f,
	0x25, 0x84, 0xa4, 0x95, 0x2f, 0x84, 0x88, 0x95, 0xcd, 0x03, 0x4b, 0x7a, 0x27, 0x0d, 0x52, 0x7f,
	0x6d, 0x89, 0x8f, 0x61, 0x8c, 0x79, 0x46, 0xd6, 0x75, 0x3e, 0x3c, 0x44, 0x13, 0xac, 0x53, 0x0a,
	0x9b, 0xe7, 0x64, 0x8f, 0x98, 0xb8, 0x0b, 0x23, 0x0d, 0xdb, 0x6a, 0xb7, 0x88, 0xc5, 0xe6, 0x57,
	0x3f, 0x38, 0x04, 0xd5, 0x87, 0xf8, 0xfb, 0xcd, 0x73, 0x32, 0x25, 0x24, 0xbe, 0x09, 0x59, 0xd5,
	0x34, 0xad, 0xb6, 0x59, 0x47, 0xc4, 0xe4, 0xf3, 0xab, 0x1f, 0x19, 0x82, 0x68, 0x91, 0x91, 0xd8,
	0x3c, 0x27, 0xfb, 0xe4, 0xb0, 0x39, 0xd6, 0x2d, 0xd3, 0x45, 0xa6, 0xab, 0x38, 0xae, 0x8d, 0xd4,
	0x26, 0xe9, 0x3c, 0xf9, 0xd5, 0x7f, 0x36, 0x8c, 0x2e, 0x28, 0xa1, 0x2a, 0xa1, 0xb3, 0x79, 0x4e,
	0x9e, 0xa8, 0xf3, 0x80, 0xb5, 0x1c, 0x8c, 0x31, 0x80, 0xf4, 0xa3, 0x14, 0x2c, 0xf5, 0x23, 0x20,
	0x2e, 0xc3, 0xf9, 0xa0, 0x68, 0x1d, 0x37, 0x31, 0x15, 0xa0, 0x5c, 0xd6, 0xc4, 0x45, 0xc8, 0x33,
	0x1c, 0xdc, 0x1d, 0xd9, 0xc0, 0x07, 0x14, 0x84, 0x3b, 0xa0, 0xef, 0x45, 0xd2, 0x31, 0x5e, 0x24,
	0xc3, 0x79, 0x91, 0x38, 0x4b, 0x1e, 0x89, 0xb5, 0xe4, 0x97, 0x41, 0xd4, 0x1d, 0xc5, 0x77, 0x03,
	0x75, 0xdd, 0xae, 0x1b, 0x88, 0x74, 0xfe, 0xac, 0x5c, 0xd0, 0x9d, 0x75, 0x56, 0xb0, 0x4e, 0xe0,
	0xe2, 0x6d, 0x98, 0x6a, 0x3b, 0xc8, 0x56, 0x5a, 0xc8, 0x6e, 0xea, 0xc4, 0x5e, 0x67, 0xc7, 0x88,
	0x90, 0x93, 0x18, 0xbc, 0xeb, 0x43, 0xc5, 0xcb, 0x90, 0x75, 0xea, 0xc8, 0x44, 0xb8, 0xb2, 0x59,
	0x22, 0xd8, 0x18, 0x79, 0x2f, 0x6b, 0xd2, 0xaf, 0x04, 0x98, 0xef, 0xd1, 0xae, 0x7e, 0x1d, 0x85,
	0x60, 0x1d, 0x89, 0x53, 0x62, 0xde, 0x13, 0x3f, 0xc7, 0x7a, 0xcf, 0x19, 0x18, 0x35, 0x2c, 0xd7,
	0xd5, 0x91, 0xe7, 0x3c, 0xe9, 0x1b, 0xe9, 0xf8, 0xe4, 0x49, 0x69, 0x6a, 0xaf, 0x13, 0x4d, 0xe0,
	0x8e, 0x4f, 0x20, 0xdb, 0xda, 0xeb, 0x58, 0xda, 0x7d, 0xac, 0x26, 0xc3, 0xb2, 0x99, 0xdf, 0x1b,
	0xdb, 0x6f, 0xac, 0xe3, 0xd7, 0x58, 0x4d, 0x8e, 0xc5, 0x69, 0x52, 0xfa, 0x6e, 0x0a, 0xe6, 0xba,
	0x77, 0xad, 0x50, 0x4c, 0x23, 0x84, 0x63, 0x9a, 0x2e, 0xe3, 0x03, 0xa9, 0x75, 0x3a, 0xa6, 0xd6,
	0x99, 0xd8, 0x5a, 0x8f, 0xf4, 0xa8, 0xf5, 0x68, 0xb8, 0xd6, 0x55, 0x18, 0xf7, 0x24, 0x22, 0xe6,
	0x36, 0x46, 0x46, 0x96, 0x57, 0xfa, 0x75, 0x99, 0x35, 0xec, 0x6f, 0x59, 0xad, 0xb0, 0x51, 0xca,
	0xf9, 0x7a, 0xe7, 0x25, 0xa0, 0xca, 0x6c, 0x40, 0x95, 0xd2, 0x2f, 0x04, 0x98, 0xed, 0xe6, 0x25,
	0xf0, 0x77, 0xc4, 0x4b, 0x74, 0x94, 0x33, 0x46, 0xde, 0x7f, 0x37, 0xaa, 0xe1, 0x6b, 0x31, 0x16,
	0xac, 0xc5, 0x2b, 0xb1, 0xad, 0x5c, 0x51, 0x9f, 0x6d, 0x5a, 0xcd, 0x58, 0xe3, 0x95, 0x9e, 0xc2,
	0xb5, 0xf8, 0x2f, 0xd6, 0x0f, 0x55, 0xf7, 0x74, 0xcd, 0x43, 0x7a, 0x5b, 0xe8, 0xc6, 0xac, 0x7a,
	0x68, 0x1d, 0xbf, 0x00, 0xb3, 0x05, 0x00, 0xdd, 0x51, 0x9c, 0x43, 0xeb, 0x58, 0x37, 0x1b, 0x84,
	0x65, 0x56, 0xce, 0xe9, 0x4e, 0x95, 0x02, 0x7c, 0x59, 0x32, 0x9c, 0x2c, 0x5f, 0x14, 0x20, 0x4f,
	0x8c, 0x65, 0x1b, 0x35, 0xf7, 0x91, 0x2d, 0x16, 0x20, 0xdd, 0xf6, 0xd9, 0xe1, 0x47, 0x71, 0x1e,
	0x72, 0xb6, 0x65, 0x20, 0xc5, 0x45, 0xcf, 0x5d, 0xc6, 0x2d, 0x8b, 0x01, 0x35, 0xf4, 0xdc, 0x15,
	0x4b, 0x90, 0xc1, 0xcf, 0x84, 0xd7, 0xe4, 0xea, 0xfb, 0x06, 0x32, 0x4b, 0xca, 0x49, 0xb6, 0x0c,
	0xf4, 0x78, 0x55, 0x26, 0x9f, 0x4b, 0x3f, 0x13, 0xe0, 0xee, 0x9a, 0xea, 0x3e, 0x44, 0x71, 0x21,
	0xe9, 0x9e, 0x83, 0x6c, 0x07, 0xa3, 0x9f, 0xf2, 0x0c, 0xe5, 0x32, 0x64, 0xdb, 0xba, 0xa6, 0x18,
	0xba, 0x83, 0x23, 0xf3, 0x34, 0xb6, 0xe6, 0xb6, 0xae, 0xe1, 0xb8, 0x38, 0xf1, 0xd4, 0xe4, 0x9d,
	0x34, 0x2c, 0x0f, 0x22, 0x7f, 0xf2, 0x58, 0xfa, 0x33, 0x30, 0x8e, 0x85, 0x24, 0x2d, 0xd0, 0x54,
	0x5b, 0x2c, 0x92, 0x7e, 0xab, 0xaf, 0xa2, 0x07, 0x16, 0x66, 0x65, 0x4f, 0xd7, 0xf0, 0xfb, 0xb6,
	0xda, 0x2a, 0x99, 0xae, 0x7d, 0x22, 0x43, 0xdb, 0x07, 0xe0, 0x81, 0x27, 0x18, 0x7c, 0x72, 0x03,
	0x5b, 0x81, 0x8f, 0x3a, 0x2b, 0xd8, 0xfc, 0x12, 0x6a, 0x6d, 0xee, 0x29, 0x4c, 0x85, 0x98, 0x63,
	0xf3, 0x3b, 0x42, 0x27, 0x9e, 0xf9, 0x1d, 0xa1, 0x13, 0xb1, 0x08, 0x23, 0xcf, 0x54, 0xa3, 0x8d,
	0x58, 0x88, 0xf3, 0x52, 0x12, 0x13, 0xa3, 0x5f, 0x7e, 0x38, 0xf5, 0x41, 0x41, 0xfa, 0x4f, 0x02,
	0x2c, 0x3e, 0x44, 0x6e, 0x48, 0x1b, 0x1b, 0xc8, 0x55, 0x75, 0xe3, 0x94, 0xed, 0xaa, 0x8b, 0x1a,
	0xd2, 0xdd, 0x8c, 0xe7, 0x07, 0x02, 0x2c, 0x75, 0x17, 0x2d, 0xb9, 0xc9, 0x7c, 0x12, 0x0a, 0x61,
	0xfe, 0x4c, 0x79, 0xaf, 0xf7, 0x53, 0x5e, 0xbc, 0x0c, 0x53, 0x21, 0x99, 0xa5, 0xbf, 0x1f, 0x81,
	0x8b, 0xb1, 0xa8, 0x89, 0x67, 0x5c, 0xab, 0x70, 0x31, 0x4e, 0x14, 0x8b, 0x39, 0x9a, 0x0b, 0xa1,
	0x2f, 0xc8, 0x0c, 0x20, 0xee, 0x1b, 0xce, 0x2e, 0xc3, 0xdf, 0x10, 0xd3, 0xbc, 0x0e, 0x13, 0xbe,
	0x21, 0x13, 0x5c, 0x6a, 0x94, 0xe3, 0x1e, 0x90, 0x20, 0x6d, 0xc2, 0x58, 0x5d, 0x6d, 0xb9, 0xaa,
	0x6e, 0x92, 0x81, 0x28, 0xbf, 0xba, 0xd2, 0x57, 0x5f, 0x64, 0xb2, 0x85, 0xfb, 0x14, 0x9e, 0x0d,
	0xc8, 0xde, 0xe7, 0xe2, 0x6b, 0x30, 0x13, 0x55, 0x83, 0xe9, 0xda, 0x16, 0x1b, 0xc5, 0xa6, 0xc3,
	0x9a, 0xc0, 0x65, 0xe2, 0x35, 0x18, 0x6f, 0x12, 0xc3, 0xe5, 0x42, 0x98, 0x09, 0x39, 0x4f, 0x61,
	0x34, 0x12, 0xbc, 0x09, 0x93, 0xba, 0xe9, 0xa2, 0x86, 0x4d, 0x48, 0x63, 0x24, 0x3c, 0x7e, 0x8f,
	0xc8, 0x13, 0x1e, 0x94, 0xa2, 0x2d, 0x00, 0x38, 0x7a, 0xc3, 0x64, 0x28, 0x39, 0x82, 0x92, 0xc3,
	0x10, 0x5a, 0xdc, 0x73, 0x4e, 0x09, 0x3d, 0xe7, 0x94, 0x5d, 0x27, 0x7a, 0xf9, 0x1e, 0x13, 0xbd,
	0x19, 0x18, 0x7d, 0xa6, 0x93, 0x40, 0x74, 0x9c, 0x0e, 0xf0, 0xf4, 0xad, 0x33, 0x01, 0x9c, 0xe8,
	0x3e, 0x01, 0x9c, 0x0c, 0x4f, 0x00, 0x17, 0x21, 0xef, 0x0b, 0xaf, 0x6b, 0xb3, 0x53, 0x74, 0x5d,
	0xc9, 0x03, 0x95, 0xb5, 0xae, 0xf3, 0xbe, 0xc2, 0x29, 0xce, 0xfb, 0x7e, 0x2e, 0xc0, 0xd5, 0x8f,
	0x59, 0xba, 0x19, 0xea, 0x05, 0x0f, 0x54, 0x33, 0xf1, 0xe2, 0x4c, 0x97, 0x2e, 0x93, 0xea, 0xd6,
	0x65, 0xee, 0xc1, 0x85, 0x56, 0xdb, 0x39, 0xa4, 0xa1, 0x6e, 0xc7, 0x0d, 0xa5, 0x89, 0x0a, 0x0b,
	0xb8, 0x08, 0x47, 0xbb, 0xfc, 0x28, 0xe7, 0xc7, 0x6c, 0x99, 0x40, 0xcc, 0x26, 0xfd, 0x57, 0x01,
	0x16, 0xbb, 0x56, 0x22, 0xb9, 0xdf, 0xa9, 0xc1, 0xb8, 0xaa, 0x69, 0x48, 0x53, 0x08, 0x7d, 0x6f,
	0xd1, 0xe7, 0x7d, 0x83, 0xf9, 0x1c, 0x6a, 0x62, 0x24, 0xcc, 0x94, 0xf3, 0x84, 0x0c, 0x79, 0x76,
	0xa4, 0xaf, 0xa7, 0x60, 0x2e, 0x3e, 0x5a, 0xdf, 0x6d, 0x3b, 0x87, 0xe2, 0x23, 0xc8, 0x91, 0xc9,
	0x0e, 0x69, 0x61, 0x61, 0xa8, 0x5e, 0x9b, 0x6d, 0xb3, 0x27, 0x1c, 0x20, 0x71, 0x51, 0x0e, 0x79,
	0x4e, 0xea, 0xcd, 0xbb, 0x7b, 0xa7, 0x4c, 0x77, 0xef, 0x14, 0x3f, 0xcc, 0x8e, 0xc4, 0x0f, 0xb3,
	0xd2, 0x11, 0x4c, 0x06, 0x6b, 0x10, 0x13, 0xb4, 0xcd, 0xc2, 0x98, 0x5a, 0xa7, 0xbd, 0x9f, 0x56,
	0xc6, 0x7b, 0xc5, 0xe1, 0x9c, 0xa9, 0xd7, 0x8f, 0x78, 0x8f, 0x99, 0xc5, 0x00, 0x22, 0x48, 0x01,
	0xd2, 0x0e, 0x7a, 0xce, 0xec, 0x04, 0x3f, 0x4a, 0x25, 0x98, 0xc7, 0x81, 0xc4, 0x49, 0xa8, 0x09,
	0x12, 0x1a, 0xb9, 0xf4, 0x4e, 0x0a, 0xae, 0xc4, 0xd3, 0x49, 0x6e, 0x67, 0x49, 0x3b, 0xcc, 0x30,
	0xe3, 0x45, 0xd7, 0x71, 0x29, 0xd3, 0x7d, 0x5c, 0x0a, 0x86, 0x05, 0x23, 0xe1, 0xb0, 0x60, 0x01,
	0xc0, 0xf3, 0xee, 0xa6, 0x4b, 0xc6, 0x81, 0x09, 0x39, 0xc7, 0x7c, 0xbb, 0xe9, 0x4a, 0x5f, 0x11,
	0x60, 0xa6, 0x8a, 0x4c, 0xed, 0x09, 0x32, 0xea, 0x56, 0x13, 0x61, 0xe3, 0x4e, 0xea, 0x49, 0x2e,
	0xc2, 0xa8, 0x6b, 0x29, 0x6d, 0x3f, 0x26, 0x19, 0x71, 0xad, 0x3d, 0x3d, 0x79, 0x3c, 0xb2, 0x01,
	0x97, 0x22, 0x82, 0x24, 0x6e, 0x25, 0xe9, 0xff, 0xa7, 0xe0, 0x02, 0x76, 0x2e, 0xd8, 0x9b, 0x70,
	0xa4, 0xc4, 0x1a, 0x4c, 0x1e, 0xd8, 0x56, 0x53, 0x79, 0xd1, 0x5e, 0x3b, 0x8e, 0xa9, 0xf8, 0x3d,
	0x60, 0x17, 0xc6, 0x71, 0xd5, 0x7d, 0x9a, 0xa9, 0xa1, 0x68, 0x82, 0x6b, 0xf9, 0x14, 0x87, 0xb1,
	0x1a, 0x6f, 0xaa, 0xc4, 0xf5, 0x77, 0x32, 0x55, 0x1a, 0xa2, 0x93, 0x7f, 0x0e, 0x6e, 0x46, 0x63,
	0x42, 0x3c, 0x34, 0xe9, 0x06, 0xda, 0x55, 0x1b, 0xe8, 0xac, 0x87, 0x19, 0xe9, 0x2f, 0x05, 0xb8,
	0x1c, 0x62, 0x4f, 0xa3, 0x6a, 0xa2, 0x9d, 0x53, 0x75, 0xbb, 0xd3, 0x30, 0x42, 0x83, 0x23, 0x2a,
	0x0c, 0x7d, 0x39, 0xa5, 0xa9, 0x25, 0x0e, 0x17, 0x58, 0xb7, 0x23, 0xae, 0x9d, 0xb6, 0x0a, 0xeb,
	0x89, 0x78, 0x0a, 0x2b, 0x95, 0x60, 0x31, 0xac, 0xe6, 0x43, 0xcb, 0xb5, 0x8a, 0xc6, 0x7e, 0xbb,
	0xf9, 0x08, 0x9d, 0xec, 0xc9, 0x5b, 0xfc, 0xac, 0x24, 0x47, 0x67, 0x25, 0xd8, 0xe3, 0xda, 0x06,
	0x13, 0x18, 0x3f, 0x4a, 0xff, 0x25, 0x05, 0x33, 0x5e, 0xdd, 0xca, 0x66, 0xed, 0x10, 0xf9, 0xe4,
	0x4e, 0x57, 0x59, 0x77, 0xa1, 0xf0, 0xd4, 0xd2, 0x4d, 0xdd, 0x6c, 0x28, 0x5a, 0xdb, 0x56, 0x5d,
	0x1c, 0x55, 0xd1, 0xee, 0x3e, 0xc5, 0xe0, 0x1b, 0x0c, 0xfc, 0x6e, 0x69, 0x50, 0xbc, 0x05, 0x53,
	0x64, 0x6c, 0x51, 0x34, 0xeb, 0xd8, 0xa4, 0x48, 0xd4, 0xac, 0x27, 0x08, 0x78, 0xc3, 0x3a, 0x36,
	0x89, 0xa6, 0xbf, 0x27, 0xc0, 0x74, 0xd9, 0x7c, 0xa6, 0xbb, 0x88, 0x72, 0x71, 0x36, 0x74, 0xa7,
	0x65, 0xa8, 0x27, 0x38, 0xa8, 0xd4, 0x74, 0xc7, 0xb1, 0x8c, 0x36, 0x96, 0x9b, 0x46, 0xad, 0x98,
	0x16, 0x51, 0x56, 0x5a, 0x9e, 0xe6, 0x0a, 0xd7, 0xbd, 0x32, 0xf1, 0x36, 0x4c, 0xe9, 0x84, 0x98,
	0xa6, 0x50, 0x59, 0x1c, 0xa6, 0x87, 0x49, 0x06, 0x66, 0x4c, 0xb0, 0xe5, 0x53, 0x88, 0xe2, 0x55,
	0xc3, 0x72, 0x55, 0x83, 0x05, 0x4c, 0xe7, 0x75, 0x4e, 0xa0, 0x1a, 0x2e, 0x90, 0x7e, 0x93, 0x85,
	0x5b, 0xfd, 0xfa, 0xde, 0x7b, 0x79, 0x53, 0x4c, 0x7c, 0x0c, 0x79, 0x8d, 0x4c, 0xc7, 0xa8, 0xc5,
	0xa5, 0x5f, 0x64, 0xee, 0x07, 0x94, 0x12, 0x31, 0xbd, 0xb7, 0x7c, 0x43, 0x20, 0x6b, 0x26, 0x99,
	0xc1, 0x76, 0x4c, 0xbb, 0x3a, 0x11, 0xcf, 0x86, 0xc8, 0x8a, 0xcb, 0xbf, 0x02, 0x68, 0xe1, 0x6e,
	0x47, 0x49, 0x8f, 0x10, 0xd2, 0xff, 0x34, 0x21, 0xe9, 0x70, 0xbf, 0x95, 0x73, 0x84, 0x24, 0xa1,
	0xff, 0x10, 0x96, 0x22, 0x44, 0xf6, 0xd5, 0xfa, 0x11, 0x8e, 0x55, 0x4d, 0x8d, 0x8e, 0xed, 0x74,
	0x6e, 0xb6, 0x10, 0xf2, 0x85, 0x6b, 0x3e, 0x16, 0x19, 0xe5, 0x89, 0x8f, 0xdf, 0xb7, 0x5c, 0x05,
	0xf7, 0xff, 0xb1, 0xa5, 0x34, 0xf5, 0xf1, 0xfb, 0x96, 0xbb, 0x67, 0x1b, 0x38, 0xd8, 0x26, 0xfb,
	0xf1, 0xdc, 0x8a, 0x3a, 0x79, 0xa7, 0xc3, 0xff, 0x33, 0x1d, 0x1d, 0x73, 0x53, 0xb2, 0x09, 0x39,
	0x87, 0x21, 0x74, 0x4a, 0x56, 0xe5, 0x7d, 0x04, 0x90, 0x16, 0x7b, 0x7f, 0xbf, 0xea, 0xc7, 0xbb,
	0x1b, 0xce, 0x57, 0xdc, 0x86, 0x82, 0x6e, 0x2a, 0xce, 0xa1, 0x6a, 0x23, 0x05, 0x4b, 0x85, 0x45,
	0xce, 0xd3, 0x9e, 0xa9, 0x9b, 0x55, 0x0c, 0xde, 0xd5, 0xeb, 0x58, 0xee, 0xc7, 0x00, 0x87, 0x96,
	0x69, 0xd9, 0xfc, 0x06, 0xd9, 0x07, 0x06, 0xd3, 0xbe, 0xac, 0x9a, 0x47, 0x9b, 0xf8, 0xdb, 0xaa,
	0xde, 0x30, 0x49, 0xb3, 0xe6, 0x08, 0x29, 0x22, 0x40, 0x11, 0x16, 0x22, 0x1f, 0x5b, 0xc7, 0x26,
	0xb2, 0x15, 0xc7, 0x55, 0xdd, 0xb6, 0xc3, 0x26, 0x7e, 0x73, 0x21, 0x95, 0xef, 0x60, 0x94, 0x2a,
	0xc1, 0x10, 0x0f, 0xe1, 0x62, 0xb0, 0xf7, 0x6a, 0xd4, 0x69, 0x90, 0x89, 0x61, 0x7e, 0xf5, 0xb5,
	0x7e, 0x52, 0xc6, 0x39, 0x1c, 0xf9, 0x02, 0xdf, 0xeb, 0x3d, 0x2f, 0xf4, 0x1a, 0xcc, 0xd8, 0xa8,
	0xa9, 0x52, 0xdf, 0xca, 0x98, 0x39, 0x48, 0x75, 0x1d, 0x32, 0xc7, 0x9c, 0x90, 0xa7, 0xfd, 0x52,
	0xfa, 0x5d, 0x15, 0x97, 0x89, 0xaf, 0xc0, 0xb4, 0x73, 0xe2, 0xb8, 0xa8, 0xa9, 0x34, 0x91, 0xe3,
	0xa8, 0x0d, 0xc4, 0x5a, 0xb8, 0x40, 0xbe, 0x11, 0x69, 0xd9, 0x36, 0x2d, 0xa2, 0x7b, 0x10, 0x06,
	0xcc, 0xd1, 0xe5, 0x39, 0xac, 0xba, 0xb2, 0xc9, 0x66, 0x33, 0x49, 0xc7, 0x73, 0xec, 0x74, 0xfd,
	0x68, 0x93, 0xf6, 0x9a, 0x14, 0x59, 0xc4, 0x9c, 0xf0, 0x43, 0x4e, 0x6c, 0xf8, 0xd2, 0xff, 0x13,
	0xe0, 0x7c, 0x80, 0x11, 0x69, 0x98, 0xd0, 0x24, 0x5a, 0x88, 0x4c, 0xa2, 0xb1, 0x99, 0xab, 0xe6,
	0x11, 0xbf, 0xc7, 0x95, 0xc5, 0x00, 0x6f, 0x87, 0x8b, 0x78, 0xf9, 0x74, 0xff, 0x79, 0x52, 0xb7,
	0xc5, 0x3f, 0x51, 0x82, 0x89, 0xa7, 0xed, 0x66, 0x4b, 0x21, 0x5c, 0xb0, 0x61, 0xd2, 0x21, 0x23,
	0x8f, 0x81, 0x58, 0xde, 0x3d, 0xdb, 0x90, 0xfe, 0x48, 0x80, 0x4b, 0x5d, 0xac, 0x0c, 0xcb, 0xa0,
	0xd7, 0x2d, 0xd3, 0x5b, 0xc4, 0xc7, 0xcf, 0xe2, 0x1d, 0x28, 0x38, 0xee, 0x89, 0x81, 0xe8, 0xa6,
	0x40, 0x47, 0x29, 0x39, 0x79, 0x92, 0xc0, 0xc9, 0xe6, 0x00, 0x71, 0x07, 0x71, 0x35, 0x08, 0xe9,
	0x24, 0xd3, 0x5b, 0x27, 0x23, 0x21, 0x9d, 0x44, 0xea, 0x33, 0x1a, 0xad, 0xcf, 0x9f, 0xa4, 0x60,
	0x3e, 0xb6, 0xe9, 0x93, 0x0f, 0x27, 0x5f, 0x13, 0x60, 0x96, 0xb0, 0xd2, 0xcd, 0xce, 0x2a, 0x80,
	0x79, 0x60, 0x71, 0x8b, 0xc4, 0x7b, 0x83, 0x2d, 0x12, 0xc7, 0x8a, 0xb2, 0x12, 0x31, 0x19, 0x7f,
	0x7d, 0x78, 0xda, 0x8e, 0x29, 0x9a, 0xfb, 0xd7, 0x70, 0xb9, 0xeb, 0x27, 0x31, 0xab, 0xba, 0x0f,
	0x83, 0xab, 0xba, 0x7d, 0x63, 0x93, 0x08, 0x6d, 0x7e, 0x6d, 0xf7, 0x4b, 0x02, 0x5c, 0x08, 0x20,
	0x54, 0x2c, 0x57, 0x3f, 0x38, 0xe9, 0xb7, 0x83, 0x52, 0x61, 0xcd, 0xc9, 0x4d, 0x18, 0x86, 0x90,
	0x23, 0x4b, 0xd5, 0x71, 0x60, 0x49, 0x3f, 0x12, 0x40, 0x92, 0x51, 0xd3, 0x7a, 0x86, 0x62, 0x87,
	0xbc, 0xb3, 0x5e, 0x17, 0x5a, 0x00, 0x70, 0x55, 0xbb, 0x81, 0x5c, 0x32, 0x03, 0xa4, 0xd1, 0x4d,
	0x8e, 0x42, 0xf0, 0x2c, 0x70, 0x06, 0x46, 0x6d, 0xa4, 0x3a, 0x96, 0xe9, 0xed, 0xba, 0xd2, 0x37,
	0x69, 0x17, 0xae, 0xf7, 0x14, 0x3a, 0xf9, 0xcc, 0xef, 0xa7, 0x02, 0xcc, 0x95, 0x9e, 0xeb, 0xee,
	0x8b, 0x2d, 0x19, 0x24, 0xae, 0xff, 0x06, 0x8c, 0xb2, 0x41, 0x85, 0xc6, 0xbb, 0x2f, 0xf7, 0x0d,
	0xac, 0xa8, 0x17, 0x27, 0xdf, 0xc8, 0xec, 0x5b, 0x69, 0x13, 0xe6, 0x63, 0x65, 0x4f, 0xae, 0x86,
	0x5f, 0x0b, 0x70, 0x69, 0xaf, 0xa5, 0xa9, 0xde, 0x30, 0x33, 0xcc, 0x0e, 0x56, 0x52, 0x1d, 0x9c,
	0x52, 0xc4, 0x1f, 0x34, 0xa5, 0x4c, 0xc8, 0x94, 0xa4, 0x12, 0xcc, 0x46, 0x2b, 0x96, 0x5c, 0x41,
	0x5f, 0x10, 0xe0, 0x4e, 0x34, 0xce, 0xc6, 0xd4, 0xb6, 0xd0, 0x81, 0x5b, 0x69, 0x93, 0x11, 0xfb,
	0xac, 0xa7, 0xb9, 0x3f, 0x4e, 0xc1, 0xdd, 0x01, 0x84, 0x48, 0xee, 0xa0, 0xbf, 0x29, 0xc0, 0x45,
	0xb2, 0x18, 0x60, 0xa0, 0x03, 0x57, 0x31, 0x29, 0x21, 0xce, 0x3b, 0xab, 0xfd, 0x1a, 0x67, 0x60,
	0xa9, 0x56, 0x42, 0x70, 0xdf, 0x53, 0x8b, 0x76, 0xa4, 0x60, 0xae, 0x04, 0x97, 0xba, 0xa0, 0xc7,
	0x78, 0xe9, 0x69, 0xde, 0x4b, 0x8f, 0xf0, 0x2e, 0xf7, 0x6d, 0x81, 0xa8, 0x8d, 0x39, 0xc2, 0xa2,
	0x43, 0xea, 0xe0, 0xa2, 0x17, 0xec, 0xf2, 0x7d, 0x36, 0xd6, 0x2e, 0xc2, 0xa8, 0xab, 0xee, 0x77,
	0x16, 0xbb, 0x47, 0x5c, 0x75, 0xbf, 0xac, 0x49, 0xdf, 0x16, 0x60, 0x79, 0x10, 0x59, 0x92, 0xb7,
	0xa1, 0x17, 0x63, 0xa4, 0xb8, 0x18, 0x23, 0x2e, 0x72, 0xb8, 0x0c, 0xd9, 0xa7, 0xed, 0xa6, 0x41,
	0x86, 0x7d, 0xea, 0x5d, 0xc7, 0xf0, 0x3b, 0x1e, 0xf2, 0x3f, 0x43, 0xb6, 0x1d, 0x23, 0xeb, 0x9e,
	0x78, 0x1a, 0x99, 0x54, 0x3b, 0xf3, 0x90, 0xb3, 0x0e, 0x0e, 0x1c, 0xe4, 0x76, 0x0c, 0x3a, 0x4b,
	0x01, 0x65, 0x8d, 0x6c, 0xa5, 0xe8, 0x4d, 0xdd, 0xf5, 0x54, 0x43, 0x5e, 0xa4, 0x9f, 0xa4, 0xc8,
	0xd6, 0x62, 0x17, 0xf6, 0xc9, 0x15, 0xd2, 0x37, 0x9e, 0x4f, 0xf5, 0x8d, 0xe7, 0x77, 0x20, 0xcb,
	0x5a, 0xd4, 0x21, 0xbb, 0xee, 0xf9, 0xd5, 0x57, 0x13, 0x4e, 0xf3, 0x1e, 0xeb, 0xe8, 0x58, 0xf6,
	0x89, 0x88, 0xaf, 0xc3, 0x84, 0x5d, 0x6f, 0x6a, 0x8a, 0x4f, 0x95, 0xce, 0x4b, 0x0b, 0x98, 0xea,
	0x76, 0xdb, 0xd1, 0xeb, 0x5e, 0x68, 0x33, 0x8e, 0xd1, 0xd6, 0xbd, 0xcf, 0x02, 0xda, 0x1c, 0x09,
	0x6a, 0x53, 0xfa, 0x7a, 0x06, 0x2e, 0xc4, 0x70, 0x65, 0x89, 0xb4, 0x82, 0x97, 0x48, 0x3b, 0x70,
	0xc2, 0x68, 0xd0, 0xb0, 0x33, 0x31, 0xb9, 0xd2, 0x96, 0x49, 0xf2, 0x36, 0xe8, 0x16, 0x1c, 0x15,
	0x07, 0x2c, 0xb3, 0x7a, 0x68, 0x1d, 0x93, 0x6d, 0xb7, 0x0a, 0x8c, 0x18, 0xea, 0x3e, 0xa2, 0x51,
	0xe5, 0x00, 0x89, 0x7e, 0x31, 0xc2, 0x6f, 0xe1, 0xef, 0x65, 0x4a, 0xa6, 0xfb, 0x36, 0xde, 0x58,
	0xef, 0x6d, 0x3c, 0xd6, 0xcc, 0x34, 0xab, 0x93, 0xbd, 0x05, 0xb6, 0x98, 0x72, 0xc1, 0xb4, 0xa0,
	0x5b, 0x30, 0x75, 0xa8, 0x3a, 0x8a, 0x89, 0x8e, 0xbd, 0xe9, 0x11, 0x99, 0xdc, 0x66, 0xe5, 0x89,
	0x43, 0xd5, 0xa9, 0xa0, 0x63, 0x36, 0x31, 0xe2, 0x35, 0x40, 0x7a, 0x57, 0x9e, 0xd7, 0x40, 0x8d,
	0xf5, 0xb1, 0x7d, 0xd7, 0xa4, 0xb1, 0xf7, 0x38, 0x65, 0xb2, 0xef, 0x9a, 0x24, 0xf4, 0xee, 0xb6,
	0xe1, 0x37, 0x71, 0x8a, 0x1b, 0x7e, 0x9b, 0x30, 0xdb, 0x4d, 0xa7, 0x7e, 0x5e, 0x8d, 0xc0, 0xe5,
	0x39, 0x75, 0xdd, 0x80, 0x91, 0xd6, 0x60, 0x16, 0x4f, 0x41, 0xd6, 0xb9, 0x15, 0xdf, 0xa4, 0xa3,
	0x9c, 0xf4, 0x5b, 0x01, 0x2e, 0xc7, 0x10, 0x49, 0xde, 0xa1, 0xf7, 0x61, 0x32, 0xb0, 0x28, 0xed,
	0xad, 0x4a, 0x7d, 0x24, 0xa1, 0x81, 0xf1, 0x82, 0xc8, 0x13, 0xfc, 0x6a, 0xb6, 0x23, 0xbe, 0x01,
	0xf3, 0x75, 0x1b, 0xa9, 0x6e, 0x0c, 0x2d, 0xec, 0x30, 0x69, 0x3f, 0x99, 0xa5, 0x28, 0xe1, 0x6c,
	0x15, 0xdb, 0x90, 0x8e, 0x60, 0xbe, 0x07, 0xb3, 0x81, 0xba, 0xa4, 0xbf, 0xc0, 0x9c, 0xe6, 0x17,
	0x98, 0x63, 0x52, 0xd1, 0x24, 0x1d, 0x2e, 0x72, 0x7a, 0xd5, 0x93, 0x2f, 0xb3, 0xdf, 0x81, 0x42,
	0x70, 0x95, 0xdf, 0xf7, 0xd5, 0x93, 0xbc, 0x56, 0xca, 0x9a, 0xf4, 0x1d, 0x01, 0x66, 0xc2, 0xbc,
	0x92, 0x37, 0xe0, 0x13, 0xf0, 0x66, 0xa8, 0xba, 0xdf, 0x78, 0x1f, 0x18, 0xb2, 0xf1, 0x64, 0x8e,
	0x94, 0xf4, 0x86, 0x37, 0xf5, 0x8e, 0xa0, 0x0d, 0xa2, 0x72, 0xe9, 0xe7, 0x02, 0x5c, 0x2b, 0xb6,
	0x5a, 0xc6, 0xc9, 0x7a, 0x5c, 0xbb, 0x26, 0xd5, 0xea, 0xe0, 0x8d, 0xda, 0x77, 0x12, 0xef, 0xb5,
	0xfa, 0x48, 0x30, 0x01, 0x91, 0xe5, 0x27, 0x8c, 0xf2, 0xf9, 0x09, 0xd2, 0x0e, 0x48, 0xbd, 0xea,
	0x90, 0x3c, 0xe4, 0xdd, 0x84, 0xab, 0xd1, 0xb0, 0xee, 0x81, 0x61, 0xa9, 0x6e, 0x52, 0x0f, 0xf0,
	0xef, 0x63, 0xf3, 0x99, 0x18, 0xa9, 0x53, 0x8b, 0x74, 0x22, 0xe9, 0x9b, 0xf3, 0x90, 0x73, 0x51,
	0xb3, 0xa5, 0x70, 0x79, 0x84, 0x59, 0x0c, 0xc0, 0x83, 0x94, 0xe4, 0xc2, 0x5c, 0x4c, 0x3a, 0xc1,
	0x59, 0x47, 0xf0, 0xcf, 0x61, 0x3e, 0x96, 0xeb, 0x99, 0x6f, 0x2c, 0x4b, 0x55, 0xb8, 0xfb, 0x58,
	0x35, 0x74, 0x3c, 0x13, 0xda, 0x73, 0x90, 0xbd, 0xa9, 0x3a, 0xd4, 0x50, 0x3e, 0xde, 0x56, 0x0d,
	0xfd, 0x40, 0xaf, 0x93, 0x5d, 0x98, 0xa4, 0x0d, 0xfb, 0x04, 0x96, 0x07, 0x21, 0x9a, 0xdc, 0xf6,
	0x3e, 0x2f, 0xc0, 0x42, 0xd4, 0x62, 0x18, 0xff, 0x53, 0xce, 0xab, 0xf4, 0xc3, 0x81, 0x74, 0x30,
	0xe3, 0xe4, 0x87, 0x42, 0x5c, 0x07, 0xf0, 0x84, 0x3d, 0xeb, 0x44, 0x00, 0x3f, 0xdd, 0x28, 0xdd,
	0x3d, 0xdd, 0x28, 0x13, 0x4a, 0x37, 0xc2, 0x1d, 0xed, 0xca, 0x43, 0xe4, 0xe2, 0xb6, 0xa0, 0x62,
	0x3f, 0xa4, 0x75, 0x39, 0xf3, 0x3c, 0x9f, 0x1e, 0x6a, 0xfc, 0x1b, 0xda, 0x94, 0x71, 0x32, 0x25,
	0xd7, 0xa2, 0x0c, 0xf9, 0xa7, 0x96, 0x6e, 0xbe, 0x70, 0xd6, 0x0e, 0x60, 0x2a, 0x34, 0x69, 0x47,
	0x7c, 0x0c, 0x13, 0xf5, 0xb6, 0x6d, 0x23, 0xd3, 0xa5, 0x64, 0xd9, 0x1e, 0xd4, 0x10, 0x54, 0xc7,
	0x19, 0x1d, 0xf2, 0x86, 0x1b, 0xe3, 0x7c, 0x04, 0xa7, 0x57, 0x5a, 0xfa, 0x75, 0x98, 0xa0, 0x45,
	0xc1, 0x60, 0x6c, 0x9c, 0x00, 0x8b, 0x2c, 0x25, 0x66, 0x01, 0x80, 0x22, 0x71, 0xfb, 0xfb, 0x39,
	0x02, 0xa9, 0xb0, 0xac, 0x6a, 0x5a, 0x4c, 0x62, 0x4f, 0x16, 0xbc, 0x13, 0x08, 0x1e, 0xc9, 0x25,
	0x1d, 0x6e, 0x74, 0xb6, 0xe8, 0x76, 0x6d, 0xf4, 0x4c, 0x47, 0xc7, 0x44, 0x2e, 0x16, 0xdb, 0x26,
	0xb5, 0x13, 0xbe, 0x36, 0xa9, 0x60, 0xbb, 0x7f, 0x2f, 0x0d, 0x37, 0xfb, 0xf0, 0x4a, 0xde, 0xfe,
	0x2f, 0xc3, 0x98, 0x17, 0x9a, 0xd3, 0xb6, 0x17, 0x31, 0x62, 0x27, 0x36, 0xaf, 0x9e, 0x98, 0x75,
	0xd9, 0x43, 0xe9, 0x61, 0x95, 0x89, 0x77, 0x00, 0x6e, 0xc2, 0xa4, 0xaa, 0x35, 0x75, 0xd3, 0x6b,
	0x1b, 0x87, 0x6c, 0xfb, 0xe5, 0xe4, 0x09, 0x02, 0x65, 0x8d, 0x43, 0xb6, 0x6f, 0x29, 0x47, 0x3a,
	0xd1, 0xf4, 0x1a, 0x92, 0x8e, 0xd4, 0xe7, 0x49, 0x11, 0x99, 0x5f, 0x7a, 0xad, 0x59, 0xc2, 0xf5,
	0xa1, 0xfb, 0xc1, 0xf4, 0xb8, 0x5b, 0xa2, 0x94, 0x61, 0xef, 0xdb, 0xde, 0x39, 0x92, 0xd9, 0x5e,
	0x39, 0x92, 0xd2, 0x73, 0x3e, 0xbd, 0x9f, 0x9a, 0x31, 0x59, 0x61, 0x2d, 0xe2, 0x7a, 0x9d, 0x9e,
	0x39, 0x78, 0x69, 0x5f, 0x69, 0x3f, 0xed, 0x4b, 0x7a, 0xca, 0x9f, 0x46, 0x8b, 0x72, 0x4e, 0x6e,
	0x1c, 0x3d, 0x8c, 0xf1, 0xcb, 0x02, 0x7f, 0xb0, 0x8a, 0x30, 0xab, 0x22, 0xb7, 0x68, 0x18, 0xdb,
	0x6d, 0xf7, 0x14, 0x8d, 0x5e, 0xbc, 0x0a, 0x79, 0xdd, 0x51, 0x54, 0xc3, 0x50, 0x9a, 0x6d, 0x17,
	0x75, 0x4e, 0x35, 0x30, 0x4e, 0xa1, 0xd3, 0x14, 0x11, 0x39, 0x4e, 0xb3, 0xce, 0x7d, 0x65, 0x39,
	0x89, 0xa8, 0x04, 0x83, 0x87, 0x5b, 0xff, 0xef, 0x21, 0x86, 0x08, 0x99, 0xb6, 0xae, 0x39, 0xec,
	0xd4, 0x02, 0x79, 0x96, 0x3e, 0x1b, 0xd1, 0x02, 0xcf, 0xfa, 0x54, 0xb5, 0x10, 0xc7, 0xfe, 0xd3,
	0x11, 0xcb, 0xdb, 0x33, 0x9b, 0xef, 0x56, 0xdd, 0x3f, 0x07, 0xd7, 0x7b, 0x32, 0x3f, 0xf3, 0xda,
	0x7f, 0x31, 0x6a, 0x83, 0xe4, 0xf8, 0x85, 0x8b, 0xf0, 0x0c, 0xef, 0x14, 0x6b, 0x3f, 0x0f, 0xb9,
	0x06, 0x72, 0xb9, 0xe3, 0xe4, 0x59, 0x39, 0xdb, 0x40, 0x2e, 0xdd, 0x58, 0xfe, 0x24, 0xcc, 0x74,
	0x84, 0xc0, 0xcc, 0x87, 0x4a, 0x10, 0x9d, 0x03, 0x92, 0x0f, 0x1a, 0xce, 0x0f, 0x25, 0xb3, 0xba,
	0x5f, 0x09, 0x91, 0x66, 0x0e, 0xd4, 0xf3, 0x54, 0x15, 0x5d, 0x85, 0x1c, 0x6e, 0xc4, 0xce, 0x01,
	0x9d, 0x01, 0x52, 0x22, 0xe2, 0xeb, 0x2f, 0x67, 0x9b, 0x4c, 0x44, 0x1c, 0x03, 0xd2, 0x5a, 0xd3,
	0x71, 0x9c, 0xbe, 0x48, 0x7f, 0x25, 0x44, 0x2c, 0x08, 0xd7, 0xcb, 0x4f, 0x4f, 0x39, 0xc5, 0x16,
	0xdc, 0x82, 0x9c, 0x61, 0xa9, 0x9a, 0xd2, 0xb4, 0x6c, 0xef, 0x84, 0x6d, 0xdf, 0x15, 0xaa, 0x2d,
	0x4b, 0xd5, 0xb6, 0x2d, 0xdb, 0xcb, 0x62, 0x90, 0xb3, 0x06, 0x03, 0xc4, 0x57, 0x07, 0x47, 0x2c,
	0x74, 0x64, 0xb5, 0x4c, 0xe3, 0x84, 0xcc, 0x74, 0xb3, 0x72, 0x8e, 0x40, 0x76, 0x4c, 0xe3, 0x44,
	0x7a, 0x3f, 0x4c, 0x85, 0x28, 0xe2, 0x38, 0xc9, 0x44, 0xcf, 0x5d, 0xa5, 0x65, 0x39, 0x3a, 0x49,
	0x29, 0x13, 0xc8, 0x8a, 0xff, 0x38, 0x06, 0xee, 0x32, 0x98, 0xf4, 0xad, 0x14, 0x1f, 0xea, 0xc4,
	0x69, 0x29, 0x79, 0xfb, 0x0f, 0x14, 0xa0, 0x55, 0x3b, 0x43, 0x7a, 0x3a, 0xe9, 0x35, 0x0d, 0x2c,
	0x3c, 0xf2, 0x93, 0x8e, 0xfc, 0x01, 0x3e, 0xd0, 0x10, 0x99, 0x17, 0x6c, 0x08, 0xe9, 0x3f, 0xa4,
	0xf9, 0x8b, 0x08, 0x42, 0x4c, 0xf9, 0xde, 0x26, 0x04, 0x7b, 0x1b, 0xcb, 0xb8, 0x4e, 0xf9, 0x19,
	0xd7, 0xb8, 0x8b, 0x93, 0x4c, 0x20, 0xdc, 0xe9, 0xbc, 0x0e, 0x88, 0x01, 0x15, 0xbd, 0x7e, 0x84,
	0x9d, 0x0f, 0xd9, 0xe0, 0xa3, 0xcd, 0x4d, 0x77, 0xeb, 0x2e, 0x43, 0xf6, 0x40, 0xad, 0xf3, 0xa7,
	0x66, 0xc7, 0xf0, 0x3b, 0x3b, 0x22, 0xa9, 0x3b, 0x64, 0xac, 0xd2, 0xd8, 0x71, 0xe1, 0x31, 0xdd,
	0xc1, 0x3d, 0x83, 0x44, 0xc6, 0x96, 0x69, 0xe8, 0x26, 0xf2, 0x96, 0xee, 0xe9, 0xca, 0xef, 0x38,
	0x05, 0xb2, 0xc5, 0xfa, 0x3b, 0x50, 0x08, 0x20, 0x29, 0xae, 0xb7, 0xf6, 0x3b, 0xc9, 0xe3, 0xd5,
	0x1c, 0xb1, 0x02, 0xe3, 0x74, 0x41, 0x99, 0xaa, 0x97, 0xac, 0x03, 0x27, 0x0c, 0xbd, 0xf2, 0xfb,
	0x9d, 0x17, 0xe9, 0x01, 0x64, 0x64, 0x5c, 0xb9, 0x09, 0xc8, 0xc9, 0x3b, 0x5b, 0x25, 0xa5, 0xb2,
	0x53, 0x29, 0x15, 0xce, 0x89, 0x93, 0x00, 0xe4, 0x75, 0xe7, 0x49, 0xa5, 0x24, 0x17, 0x04, 0xff,
	0xbd, 0xb8, 0xb1, 0x5d, 0xae, 0x14, 0x52, 0xe2, 0x14, 0xe4, 0xc9, 0xfb, 0xfa, 0xce, 0xf6, 0xf6,
	0x4e, 0xa5, 0x90, 0x96, 0x0e, 0xe3, 0x3a, 0xf6, 0x86, 0x9f, 0xd3, 0x76, 0x8a, 0xc1, 0xf9, 0xff,
	0x16, 0xe2, 0x7a, 0x07, 0xcf, 0x2a, 0x79, 0xef, 0x78, 0x2b, 0x98, 0xc9, 0x47, 0x93, 0x14, 0x92,
	0x1a, 0x3f, 0x27, 0x02, 0x97, 0xcd, 0x27, 0xbd, 0x9d, 0x89, 0x58, 0x6c, 0x07, 0xb3, 0x97, 0x07,
	0xeb, 0x33, 0x5d, 0xba, 0x05, 0x53, 0xb4, 0xb8, 0x89, 0x9a, 0x0a, 0xef, 0x9c, 0x68, 0x47, 0xdf,
	0x46, 0x4d, 0x9a, 0xf1, 0xf6, 0x3e, 0xb8, 0x18, 0xc2, 0x53, 0xe8, 0x0e, 0x16, 0xcd, 0xac, 0x11,
	0x03, 0xd8, 0x5b, 0xb8, 0x44, 0xbc, 0x06, 0xe3, 0x2d, 0xcb, 0x76, 0x6d, 0x55, 0x77, 0xb9, 0x23,
	0xc1, 0x79, 0x0f, 0x86, 0x2d, 0x7e, 0x11, 0xf2, 0x6c, 0xb1, 0xd9, 0xd5, 0x9b, 0x88, 0x19, 0x35,
	0x50, 0x50, 0x4d, 0x8f, 0xcc, 0xe6, 0xb2, 0xa1, 0xd9, 0x1c, 0xb6, 0x63, 0x5a, 0x6c, 0x20, 0x55,
	0x1b, 0xdc, 0x8e, 0x89, 0xfa, 0xb6, 0xc8, 0x27, 0x72, 0xbe, 0xd1, 0x79, 0x11, 0x3f, 0x02, 0x23,
	0x24, 0xff, 0x8e, 0xe5, 0xf4, 0xdd, 0xec, 0x3b, 0x03, 0xc6, 0xc8, 0x32, 0xfd, 0x06, 0xd7, 0x97,
	0x69, 0x9a, 0x6c, 0xd5, 0xb2, 0x33, 0x56, 0x94, 0x3e, 0xdd, 0xbd, 0x15, 0x37, 0x60, 0x04, 0x3b,
	0x07, 0x67, 0x76, 0x9c, 0x38, 0xc6, 0xa4, 0x79, 0xc5, 0xf4, 0x63, 0xe9, 0x1d, 0x01, 0x46, 0x08,
	0xe7, 0x53, 0x39, 0xc0, 0xc7, 0x2d, 0xd5, 0x26, 0x3b, 0x5c, 0x91, 0xee, 0x7a, 0xb8, 0x42, 0xfa,
	0xaa, 0x00, 0x79, 0x4e, 0xc9, 0xa7, 0x77, 0xe4, 0x65, 0x88, 0xf3, 0x3a, 0x52, 0x1b, 0xae, 0x86,
	0x7a, 0x4e, 0x51, 0xd3, 0xce, 0x7e, 0x76, 0xd7, 0x80, 0xc5, 0xae, 0x6c, 0x4f, 0x75, 0x6a, 0xd7,
	0x00, 0xc9, 0x5f, 0xa5, 0x23, 0x7c, 0x76, 0x88, 0xb3, 0xf7, 0x46, 0xbd, 0xd3, 0xf3, 0x99, 0xbf,
	0x10, 0xe0, 0x7a, 0x4f, 0x4e, 0xc9, 0xab, 0x15, 0x3e, 0x44, 0x99, 0x8a, 0x1e, 0xa2, 0xec, 0xb1,
	0x86, 0xe1, 0x2f, 0x11, 0x66, 0xba, 0x2f, 0x11, 0x8e, 0x84, 0x97, 0x08, 0xbf, 0xec, 0x9f, 0x5e,
	0x0f, 0x2d, 0x6c, 0x3e, 0x42, 0xb6, 0x89, 0x8c, 0x21, 0xd5, 0xf6, 0x0a, 0x4c, 0xc7, 0xf4, 0x44,
	0x87, 0x25, 0x34, 0x8a, 0x91, 0xae, 0xe8, 0x48, 0xbf, 0x4e, 0x79, 0xa7, 0xd0, 0x7b, 0xcb, 0x91,
	0x5c, 0xa9, 0xff, 0x4e, 0x80, 0xf3, 0x47, 0x84, 0x88, 0x97, 0x18, 0xdb, 0x49, 0x64, 0x51, 0x06,
	0x4b, 0x33, 0x1c, 0x44, 0xa4, 0x15, 0x1e, 0xea, 0xa7, 0xb1, 0x4c, 0x1d, 0x05, 0xa1, 0x73, 0x2d,
	0x98, 0x8e, 0x43, 0x8c, 0x39, 0xa6, 0xb1, 0x16, 0x4c, 0x33, 0x7c, 0x39, 0x41, 0x38, 0xe2, 0xf0,
	0xe9, 0x2e, 0x7b, 0x30, 0xce, 0x17, 0xf1, 0x6b, 0x4c, 0xc2, 0xf0, 0x6b, 0x4c, 0x92, 0x4a, 0x96,
	0x96, 0x69, 0x4f, 0xae, 0xbb, 0xfa, 0xb3, 0x33, 0xe8, 0x61, 0xff, 0x57, 0x80, 0x71, 0x96, 0xe6,
	0x4c, 0x4f, 0x68, 0x9f, 0xdd, 0x59, 0x41, 0x2e, 0x3f, 0x60, 0x24, 0x90, 0x1f, 0x10, 0xb8, 0x41,
	0x62, 0x34, 0x74, 0x83, 0x84, 0x17, 0xd1, 0x8e, 0x75, 0x22, 0x5a, 0xe9, 0x07, 0x02, 0x5c, 0x89,
	0x3f, 0xf3, 0x49, 0x87, 0xaf, 0x7e, 0x99, 0x9d, 0xd7, 0xb8, 0x4b, 0x53, 0x3a, 0xe7, 0x39, 0xfd,
	0x2b, 0x50, 0x30, 0xdb, 0x65, 0x38, 0xcf, 0xbb, 0x09, 0x85, 0xcb, 0xe9, 0x99, 0xe2, 0x7c, 0x05,
	0xc1, 0xbd, 0x16, 0xba, 0x83, 0x85, 0x56, 0x99, 0xbf, 0x51, 0x45, 0xfa, 0xdb, 0x14, 0x59, 0x91,
	0x8f, 0x6b, 0xca, 0x61, 0xae, 0xa4, 0x0b, 0x9c, 0xb3, 0xa0, 0xdd, 0x6c, 0xc0, 0x74, 0x46, 0xef,
	0xd8, 0x06, 0x77, 0xb4, 0x82, 0xbb, 0x7c, 0x8a, 0xce, 0x9e, 0x3e, 0x9a, 0x74, 0x7f, 0x98, 0xd7,
	0x7d, 0xe7, 0xf2, 0x29, 0x3f, 0xb4, 0xc9, 0x0c, 0x11, 0xda, 0x90, 0xf4, 0x75, 0x54, 0x27, 0xf9,
	0xcb, 0x01, 0xa3, 0x99, 0x20, 0xe0, 0xb2, 0xc9, 0x66, 0x20, 0x4b, 0x58, 0xf7, 0x0c, 0x0f, 0x3d,
	0x6f, 0xb1, 0x73, 0x93, 0xc0, 0x90, 0x4a, 0xcf, 0x5b, 0xd2, 0x0f, 0xf9, 0x31, 0x24, 0x94, 0xbe,
	0x3a, 0xcc, 0xdc, 0x3d, 0xe9, 0x3e, 0x4d, 0x20, 0x91, 0x28, 0x1d, 0x4d, 0xcb, 0x8a, 0x59, 0x6e,
	0xf8, 0x7c, 0x1a, 0x6e, 0xf4, 0x16, 0x79, 0x98, 0xa9, 0x82, 0x18, 0xf4, 0xd0, 0x43, 0xdb, 0x4e,
	0x81, 0xf7, 0xb7, 0xcc, 0x82, 0x0a, 0x07, 0xaa, 0xe9, 0x04, 0x28, 0xa7, 0x87, 0xa0, 0x3c, 0x89,
	0xa9, 0x70, 0x74, 0x03, 0xaa, 0xcb, 0x84, 0x54, 0xe7, 0xcd, 0x83, 0x89, 0x77, 0x60, 0xd9, 0xf6,
	0x18, 0x40, 0xa6, 0x85, 0xd7, 0x60, 0x9c, 0xd5, 0xb6, 0xb3, 0x1b, 0x90, 0x96, 0xf3, 0x14, 0xe6,
	0xdf, 0x81, 0x40, 0x84, 0xe6, 0xaf, 0x83, 0xca, 0x61, 0x08, 0x5d, 0x2c, 0xab, 0x91, 0xa4, 0xc1,
	0x2e, 0xf7, 0x5b, 0x55, 0xd0, 0x31, 0x45, 0x4b, 0xba, 0x7b, 0xfb, 0x14, 0x5e, 0x1a, 0x88, 0x6a,
	0xf2, 0xf6, 0x2d, 0x40, 0xda, 0x6c, 0x37, 0xbd, 0x85, 0x02, 0xb3, 0xdd, 0x94, 0xfe, 0x50, 0x80,
	0xab, 0xc1, 0x5b, 0x1c, 0x3d, 0x46, 0x67, 0xbe, 0x37, 0x19, 0x9b, 0x6d, 0xd8, 0xb7, 0x39, 0xb1,
	0xdd, 0x04, 0x0e, 0x4f, 0x60, 0x00, 0x71, 0x9f, 0xff, 0x33, 0x0d, 0x8b, 0x5d, 0xab, 0x92, 0x5c,
	0x57, 0xd3, 0x30, 0xe2, 0xea, 0xae, 0xe1, 0x4d, 0x42, 0xe8, 0x8b, 0xb8, 0x0b, 0x39, 0xef, 0x2e,
	0x3a, 0x6f, 0x1d, 0x69, 0x75, 0xf0, 0xa9, 0xb4, 0x27, 0x90, 0xdc, 0x21, 0x22, 0x7e, 0x4d, 0x00,
	0x76, 0x99, 0x27, 0xa2, 0x47, 0x70, 0xe8, 0x4e, 0x11, 0x8e, 0x8d, 0x68, 0x12, 0xe2, 0x27, 0xfa,
	0x2e, 0x28, 0xf5, 0xae, 0xf4, 0xca, 0x1e, 0xa3, 0xbe, 0xc5, 0x34, 0xe5, 0x07, 0x45, 0x17, 0xda,
	0xd1, 0x92, 0x9e, 0x39, 0x8d, 0x73, 0x0f, 0x60, 0xb6, 0x1b, 0xb5, 0x7e, 0xa9, 0xbf, 0x59, 0x3e,
	0x16, 0xfa, 0x3e, 0x80, 0x18, 0x15, 0x39, 0x92, 0x14, 0x14, 0xdf, 0x06, 0xb3, 0xfe, 0xc5, 0x7c,
	0xcc, 0x55, 0x7a, 0xaf, 0xe4, 0xca, 0x12, 0x57, 0xb5, 0x5d, 0x3a, 0x6f, 0xcf, 0xd0, 0xee, 0x4a,
	0x20, 0x64, 0xda, 0x7e, 0x19, 0xb2, 0xc8, 0xd4, 0x68, 0x21, 0xbd, 0x24, 0x6f, 0x0c, 0x99, 0x1a,
	0x29, 0x62, 0x77, 0xa2, 0xd8, 0xc8, 0x71, 0x39, 0x6f, 0x30, 0x41, 0xef, 0x44, 0xc1, 0x50, 0xea,
	0x0f, 0xae, 0x02, 0x78, 0x00, 0xa4, 0x11, 0x7f, 0x90, 0x95, 0x39, 0x88, 0xb8, 0x48, 0xf6, 0x76,
	0x3c, 0xb5, 0x92, 0x95, 0x01, 0x8c, 0xe0, 0x78, 0x4a, 0xc3, 0x21, 0x03, 0xc9, 0xe5, 0x6e, 0x5a,
	0x9a, 0x7e, 0x70, 0x12, 0x38, 0xc8, 0x37, 0x85, 0x0b, 0xb6, 0x09, 0x9c, 0x32, 0x9b, 0x81, 0x51,
	0xbd, 0xa9, 0x36, 0x90, 0x33, 0x0b, 0x24, 0x5c, 0x67, 0x6f, 0xe4, 0x86, 0x2e, 0xfc, 0xa4, 0x1c,
	0xa1, 0x13, 0x67, 0x36, 0x4f, 0xca, 0x72, 0x04, 0xf2, 0x08, 0x9d, 0x38, 0xfe, 0xea, 0x85, 0x65,
	0x93, 0xcc, 0xfb, 0x71, 0x6e, 0xf5, 0xc2, 0xb2, 0xf7, 0x74, 0x4d, 0xbc, 0x0d, 0x53, 0x1e, 0x82,
	0x17, 0xb3, 0x4d, 0xb0, 0xec, 0x32, 0x0a, 0xf6, 0x96, 0x4c, 0x97, 0xe1, 0xbc, 0x87, 0xd8, 0x09,
	0xe1, 0x26, 0xd9, 0x8d, 0x86, 0xb4, 0xa0, 0xe2, 0x45, 0x72, 0x1c, 0x57, 0x1c, 0xd1, 0x4d, 0x05,
	0xb8, 0x56, 0xd1, 0x73, 0xb1, 0x02, 0xe3, 0x1e, 0x02, 0xf1, 0xc6, 0x85, 0x21, 0x16, 0xf7, 0x18,
	0x01, 0xe2, 0xbd, 0x0d, 0x10, 0x35, 0xe4, 0xb8, 0xba, 0xa9, 0xd2, 0xf3, 0xbe, 0x87, 0x96, 0xe5,
	0xa0, 0xd9, 0xf3, 0x84, 0xea, 0x40, 0x97, 0xa6, 0x7a, 0x56, 0xb7, 0xd1, 0xa1, 0xb2, 0x4e, 0x88,
	0xc8, 0xe7, 0xb5, 0x30, 0x48, 0x3c, 0x80, 0x82, 0x69, 0x99, 0x64, 0xe7, 0xd6, 0x2b, 0x99, 0x15,
	0x07, 0x5f, 0x49, 0xf3, 0xbd, 0xb7, 0xc5, 0x71, 0xdb, 0x3c, 0x27, 0x4f, 0x61, 0xa2, 0x1c, 0x48,
	0x3c, 0x02, 0xba, 0x1b, 0x1d, 0x60, 0x74, 0x81, 0x30, 0xfa, 0x68, 0x12, 0x46, 0x6c, 0x29, 0x8e,
	0xe7, 0x55, 0x68, 0x84, 0x60, 0xe2, 0x67, 0x61, 0xb6, 0x7e, 0xa8, 0xba, 0xfe, 0xd9, 0x2f, 0x9e,
	0xe7, 0x34, 0xe1, 0x59, 0x4c, 0xc2, 0xd3, 0xbb, 0xe5, 0x0e, 0x19, 0x41, 0xc6, 0x33, 0xf5, 0xce,
	0xf5, 0x77, 0x21, 0xf6, 0x24, 0x59, 0x37, 0x8e, 0xfd, 0xc5, 0xe4, 0xec, 0xbd, 0x7b, 0xef, 0x22,
	0xec, 0x9d, 0xce, 0x85, 0x78, 0x3c, 0xfb, 0xb7, 0x05, 0xb8, 0xd2, 0x42, 0xb6, 0x63, 0x99, 0x98,
	0x70, 0x8c, 0x0c, 0x33, 0x44, 0x86, 0x52, 0x12, 0x19, 0x76, 0x19, 0xbd, 0x38, 0x39, 0xe6, 0x5a,
	0x5c, 0x61, 0xb0, 0x74, 0x6d, 0x1e, 0x2e, 0x13, 0x16, 0xde, 0xb0, 0xc0, 0xcb, 0x21, 0xb5, 0x60,
	0xa1, 0xa7, 0xbd, 0x76, 0xdc, 0xa4, 0xc0, 0xbb, 0xc9, 0xbb, 0x50, 0xe0, 0x3b, 0x08, 0x77, 0x08,
	0x73, 0x8a, 0x83, 0x73, 0xf9, 0xaf, 0x74, 0x1c, 0x4e, 0xe9, 0x9a, 0x34, 0x4f, 0x57, 0x75, 0x63,
	0xad, 0x56, 0xfa, 0xef, 0x6c, 0x9f, 0xb2, 0xa7, 0xde, 0x4f, 0xeb, 0x16, 0xcc, 0x39, 0xc8, 0xfa,
	0x99, 0x1d, 0x19, 0x7a, 0x50, 0xda, 0x7b, 0x27, 0x2e, 0x87, 0xb1, 0xe8, 0x6c, 0x5b, 0x78, 0x5c,
	0xb7, 0xb5, 0xd7, 0x23, 0x92, 0xc6, 0x1a, 0xe8, 0x7b, 0x42, 0xd2, 0xff, 0x25, 0xd0, 0xa4, 0x9c,
	0xbe, 0x76, 0xf4, 0x9e, 0x90, 0xb6, 0x0d, 0x57, 0x7a, 0xf9, 0x9a, 0x33, 0xba, 0xe2, 0x53, 0x3a,
	0xa1, 0xf1, 0x5d, 0x4c, 0x3f, 0x38, 0xf3, 0x13, 0x5e, 0x3f, 0x15, 0x60, 0xa9, 0x3b, 0xef, 0xe4,
	0xc1, 0xe5, 0x51, 0x70, 0xf0, 0x0a, 0xa4, 0xcd, 0x7d, 0x74, 0xc8, 0xc1, 0x8b, 0xa6, 0xea, 0xf0,
	0x63, 0x17, 0xbb, 0xfd, 0xea, 0x3f, 0x0a, 0xc1, 0xf6, 0x0a, 0x7f, 0xd3, 0xc5, 0x7f, 0xa8, 0x30,
	0xce, 0xd1, 0xf2, 0xa4, 0x7b, 0xc1, 0xa1, 0x35, 0x40, 0x52, 0xfa, 0xa5, 0x00, 0x8b, 0x7b, 0x2d,
	0x07, 0xd9, 0x31, 0xf1, 0x6b, 0xd2, 0x26, 0xad, 0x70, 0x97, 0x4e, 0xd3, 0x95, 0xba, 0x61, 0x02,
	0xf3, 0xce, 0x4d, 0xd3, 0x49, 0x6f, 0x3c, 0xda, 0x86, 0xa5, 0xee, 0x55, 0x49, 0x9e, 0x69, 0xfb,
	0x9f, 0x05, 0xb8, 0x51, 0x45, 0x31, 0xc4, 0xca, 0x2c, 0xda, 0x1c, 0xe2, 0x50, 0x41, 0x70, 0x88,
	0xe9, 0x1c, 0x2a, 0x68, 0x72, 0x4d, 0x46, 0xf3, 0x1d, 0xfd, 0x58, 0x98, 0x8c, 0x1a, 0x74, 0x60,
	0x18, 0xf7, 0x80, 0x64, 0xb6, 0x25, 0xc3, 0xcd, 0x3e, 0xe2, 0x25, 0xaf, 0xb3, 0x03, 0x8b, 0x34,
	0xd5, 0xec, 0xc5, 0xad, 0x61, 0xe0, 0xda, 0xe2, 0x76, 0xeb, 0xce, 0x34, 0x79, 0x1d, 0xfe, 0x8f,
	0x00, 0xcb, 0xf1, 0x13, 0x32, 0x4f, 0x33, 0xf4, 0xf6, 0xd4, 0x33, 0x6b, 0xbd, 0xe4, 0xd3, 0x6a,
	0xe9, 0xf7, 0x05, 0x78, 0x69, 0x20, 0x99, 0x93, 0x3b, 0x3a, 0x7f, 0x6b, 0x31, 0xf5, 0x02, 0x5b,
	0x8b, 0xe2, 0x0d, 0x98, 0x24, 0x99, 0x25, 0xe1, 0x35, 0x32, 0x92, 0x5a, 0xb2, 0xe3, 0x55, 0xe3,
	0x6d, 0x01, 0xee, 0xc7, 0xa6, 0xbd, 0x73, 0xa3, 0x2a, 0xbb, 0xb6, 0xfc, 0xcc, 0x07, 0x8c, 0x7f,
	0x09, 0xaf, 0x0c, 0x2e, 0x4a, 0x72, 0x2b, 0xfb, 0xae, 0x00, 0x8b, 0x55, 0xe4, 0x76, 0x56, 0x87,
	0x34, 0x8d, 0xa4, 0xd7, 0x6c, 0x5b, 0xda, 0x99, 0x9f, 0x0f, 0xbf, 0x0e, 0x13, 0x2a, 0x63, 0x87,
	0xa7, 0xb1, 0xbe, 0x7b, 0x50, 0x39, 0x19, 0x70, 0xaf, 0xea, 0x2e, 0x5f, 0xf2, 0xfa, 0x9e, 0x90,
	0x83, 0x2a, 0xbf, 0x8b, 0xea, 0x4a, 0x0f, 0x61, 0x9c, 0x67, 0x17, 0xad, 0xbe, 0x10, 0xad, 0x7e,
	0xdc, 0x25, 0x90, 0xd2, 0x9f, 0xd2, 0x2b, 0x7a, 0x4f, 0x4b, 0x27, 0xe2, 0x2a, 0x5c, 0xf4, 0xf2,
	0xe3, 0x83, 0x02, 0xd1, 0x20, 0xff, 0x02, 0x2b, 0x0c, 0x08, 0x5f, 0x85, 0xc9, 0x00, 0xae, 0x33,
	0xe8, 0x12, 0x6c, 0x40, 0xd8, 0x09, 0xbe, 0xae, 0x8e, 0xf4, 0x63, 0x01, 0x6e, 0x63, 0xf7, 0x11,
	0x5a, 0xb1, 0xac, 0xf2, 0xb7, 0xd6, 0xfc, 0xae, 0x17, 0xd0, 0xa9, 0x4b, 0xcc, 0xf0, 0xe7, 0x9a,
	0x63, 0x36, 0x88, 0x02, 0x22, 0x8b, 0x0a, 0x8c, 0x93, 0x43, 0x0d, 0x5e, 0x66, 0xfb, 0x80, 0x7f,
	0xdd, 0x88, 0x39, 0x48, 0xc4, 0x28, 0x6e, 0x9e, 0x93, 0xc9, 0x31, 0x09, 0x8f, 0x01, 0xb6, 0x92,
	0xce, 0xac, 0x8c, 0x3c, 0xaf, 0xcd, 0xc1, 0x6c, 0x84, 0x2e, 0x13, 0x40, 0xfa, 0x99, 0x10, 0x7b,
	0x38, 0xca, 0x23, 0x17, 0x5e, 0x3d, 0x5b, 0x83, 0x0c, 0x76, 0x9f, 0x43, 0xde, 0x63, 0x48, 0xbe,
	0xc5, 0x7a, 0x25, 0x3a, 0x20, 0x6b, 0x66, 0xf4, 0xd7, 0x43, 0x59, 0x0c, 0x20, 0x8b, 0x66, 0x5d,
	0xae, 0x03, 0xe9, 0xb6, 0x87, 0x27, 0xfd, 0x5a, 0x80, 0x3b, 0xfd, 0x0d, 0x25, 0x79, 0x4f, 0xa8,
	0xc3, 0x64, 0xf0, 0xfa, 0xa4, 0x41, 0x23, 0xe9, 0x9e, 0x82, 0x4c, 0x04, 0xae, 0x5d, 0xea, 0x69,
	0x61, 0xd2, 0xb7, 0x04, 0xb8, 0x5d, 0x6d, 0xef, 0x37, 0x75, 0xb7, 0xd8, 0x6a, 0x19, 0xec, 0x80,
	0x55, 0xcd, 0xc2, 0x8d, 0xf5, 0xae, 0xdd, 0x5d, 0xd2, 0x69, 0x8d, 0x74, 0xe0, 0x72, 0x96, 0x3d,
	0xb8, 0xd3, 0x5f, 0xb4, 0xe4, 0x2e, 0xf9, 0x73, 0x70, 0x8f, 0xc6, 0xbb, 0xdd, 0x2d, 0x92, 0x5d,
	0x8b, 0xf2, 0x62, 0x3f, 0x9a, 0xe2, 0xac, 0x29, 0x1d, 0xb0, 0xa6, 0x7f, 0x0e, 0x2b, 0x83, 0x0a,
	0x30, 0xd4, 0x41, 0xb7, 0xdb, 0xd1, 0x4d, 0x18, 0x7a, 0x63, 0xc9, 0x16, 0x7a, 0x86, 0x8c, 0x9a,
	0xde, 0x3a, 0xeb, 0x91, 0xe7, 0x27, 0xe9, 0xb8, 0xbb, 0x4d, 0xc2, 0x32, 0xbc, 0x37, 0xef, 0xbe,
	0x8d, 0xfb, 0x45, 0xc7, 0x5b, 0x90, 0xb7, 0xf5, 0xc6, 0xa1, 0x77, 0x9c, 0x6b, 0x64, 0xb0, 0xe5,
	0xd3, 0xf0, 0x99, 0x4c, 0x4c, 0x81, 0x1d, 0x16, 0xb3, 0xfd, 0x67, 0xb2, 0xa6, 0x4d, 0xef, 0x4d,
	0x0f, 0x1d, 0xee, 0x99, 0x64, 0x60, 0x7e, 0x4d, 0x9b, 0x21, 0x76, 0xd6, 0xb4, 0xc7, 0xd8, 0x9a,
	0x36, 0x2d, 0x08, 0xac, 0x69, 0x33, 0xdc, 0x36, 0xbb, 0x8c, 0x6f, 0x42, 0x06, 0x06, 0xda, 0xd3,
	0xb5, 0xf8, 0x9f, 0x4f, 0x49, 0xff, 0x26, 0x6e, 0x63, 0x96, 0x34, 0xd8, 0x70, 0xbf, 0x0a, 0x48,
	0x6a, 0x37, 0x7f, 0x3d, 0x12, 0x77, 0xef, 0x6b, 0x40, 0x80, 0x7f, 0x3c, 0x17, 0x26, 0xdf, 0x81,
	0xa9, 0xf0, 0xf5, 0x13, 0x74, 0xd0, 0x09, 0x83, 0xc5, 0x75, 0x18, 0x65, 0x79, 0xc5, 0xa3, 0xc9,
	0xb7, 0x1e, 0xd8, 0xa7, 0xe2, 0x13, 0x2f, 0x8b, 0xab, 0xae, 0xda, 0x74, 0x03, 0x28, 0xf9, 0x45,
	0x1a, 0xa4, 0x25, 0xd6, 0x55, 0x5b, 0x63, 0xf9, 0x5f, 0xf8, 0x31, 0xdc, 0x39, 0xb2, 0xa7, 0xd9,
	0x39, 0xde, 0x84, 0xbc, 0xab, 0x3a, 0x47, 0xde, 0x32, 0x53, 0x8e, 0x0c, 0x8e, 0x49, 0xa5, 0xae,
	0xa9, 0xce, 0x11, 0x23, 0xed, 0x7a, 0x8f, 0x4e, 0x24, 0x53, 0x0e, 0xa2, 0x99, 0x72, 0xdd, 0x6e,
	0xd6, 0xc8, 0x9f, 0xe6, 0x2f, 0xd4, 0xa2, 0x17, 0x0d, 0x77, 0x74, 0xd1, 0x65, 0x7d, 0xcb, 0x80,
	0x4b, 0x11, 0x96, 0x44, 0x69, 0xde, 0x64, 0xf5, 0xb5, 0x61, 0xb4, 0x2f, 0x5f, 0x74, 0x62, 0xa0,
	0x8e, 0xf4, 0x2b, 0x21, 0x72, 0xf9, 0x87, 0x6f, 0x07, 0x1d, 0x3f, 0x22, 0xf0, 0x19, 0x83, 0x97,
	0x60, 0xac, 0xde, 0xb6, 0x49, 0xc6, 0x4a, 0x8a, 0x44, 0x5e, 0xa3, 0xf5, 0xb6, 0x5d, 0x7a, 0xde,
	0xf2, 0xa7, 0xc3, 0xd4, 0x12, 0x71, 0x39, 0x8d, 0xcc, 0xc8, 0x74, 0x98, 0x50, 0xc5, 0x58, 0x5c,
	0x4a, 0x56, 0x26, 0x98, 0x92, 0x75, 0x03, 0xc8, 0x5f, 0xbc, 0x14, 0xd7, 0xd2, 0xd4, 0x13, 0xf2,
	0x3d, 0xdd, 0x0d, 0x1d, 0xc7, 0xd0, 0x1a, 0x06, 0x32, 0x2e, 0x0c, 0xcb, 0x55, 0x0d, 0x3f, 0x6f,
	0xc6, 0xc7, 0x72, 0x55, 0xcc, 0x45, 0x32, 0x60, 0x3a, 0x4e, 0x0d, 0xfe, 0x00, 0x20, 0x70, 0x03,
	0x40, 0xfc, 0x76, 0x6e, 0xdc, 0xf2, 0x6f, 0x67, 0xcc, 0xcf, 0x04, 0xc6, 0xfc, 0x4f, 0x47, 0x94,
	0xe8, 0x9b, 0x65, 0x97, 0x56, 0x2e, 0xc3, 0x08, 0x36, 0x57, 0xef, 0x1c, 0xe7, 0xab, 0x43, 0x58,
	0xbd, 0x4c, 0x29, 0x48, 0x5f, 0x8a, 0x5e, 0xe8, 0x83, 0x8b, 0x45, 0x1d, 0x0a, 0xa6, 0x65, 0x37,
	0x55, 0x43, 0x21, 0xbd, 0xec, 0x99, 0x8e, 0x8e, 0x99, 0x33, 0x7d, 0x23, 0x21, 0xb7, 0x0a, 0x21,
	0x83, 0x89, 0x3e, 0xd6, 0xd1, 0xf1, 0xe6, 0x39, 0x79, 0xd2, 0x0c, 0x40, 0xc4, 0x26, 0x88, 0x7e,
	0x26, 0x53, 0x87, 0x19, 0x0d, 0xf0, 0xff, 0x49, 0xe2, 0x8c, 0x2c, 0x92, 0xfe, 0xc4, 0x71, 0x9b,
	0xaa, 0x07, 0x41, 0x24, 0xb0, 0x76, 0x51, 0x8b, 0x63, 0x35, 0xe0, 0xef, 0xfc, 0xc2, 0x81, 0xb5,
	0x8b, 0x5a, 0x1c, 0x9f, 0x71, 0x87, 0x7b, 0xef, 0x9a, 0xf1, 0xb7, 0x08, 0x79, 0xb5, 0xde, 0xd9,
	0xba, 0x62, 0x49, 0x5b, 0x14, 0x44, 0x76, 0xad, 0x16, 0x80, 0xbd, 0xb1, 0x6b, 0x74, 0x49, 0x46,
	0x2d, 0x85, 0xec, 0xd9, 0x06, 0x0e, 0xd8, 0xbd, 0x5f, 0x7b, 0x39, 0xb3, 0x59, 0xba, 0xc7, 0xc1,
	0xfe, 0xed, 0x45, 0xd2, 0x09, 0xc9, 0x4d, 0xba, 0x84, 0x34, 0x1d, 0xbd, 0xb3, 0x18, 0x80, 0x09,
	0xaf, 0x5d, 0x81, 0xb9, 0x48, 0xbd, 0x7c, 0x15, 0x48, 0xff, 0x43, 0x80, 0x85, 0x9e, 0xed, 0x86,
	0x89, 0xab, 0xc7, 0xaa, 0xad, 0x91, 0x4e, 0x43, 0xaf, 0x9f, 0xce, 0x12, 0x00, 0xee, 0x56, 0x83,
	0x77, 0x02, 0xa6, 0x80, 0x67, 0x88, 0xbf, 0x29, 0x03, 0x28, 0x68, 0x83, 0x21, 0xec, 0xb7, 0x5d,
	0xd7, 0x0a, 0xdc, 0x97, 0x0d, 0x14, 0x44, 0x2e, 0xcb, 0xfe, 0xe3, 0x14, 0x5c, 0xed, 0xdd, 0xea,
	0xef, 0x9a, 0xac, 0x5f, 0x10, 0x40, 0x64, 0xe7, 0x7f, 0x82, 0x32, 0xe3, 0x5e, 0x59, 0x7b, 0x31,
	0xd3, 0x5d, 0xa1, 0x71, 0xfe, 0x9a, 0x5f, 0x71, 0x9a, 0xde, 0x52, 0x70, 0x42, 0xe0, 0xb9, 0x75,
	0xb8, 0x18, 0x8b, 0xda, 0x2f, 0x77, 0x25, 0xc7, 0xe7, 0xae, 0xfc, 0x52, 0x88, 0x5c, 0x26, 0xc4,
	0xdb, 0x37, 0xf6, 0x38, 0xd8, 0xbe, 0xbd, 0xac, 0xde, 0x57, 0x87, 0xe8, 0x2b, 0x32, 0xa5, 0xd0,
	0x45, 0xff, 0xa1, 0x66, 0x4f, 0x87, 0x9b, 0x1d, 0x7f, 0xe6, 0xd4, 0xbd, 0x13, 0x69, 0x69, 0x99,
	0xbe, 0xf8, 0xcd, 0x36, 0xc2, 0xfd, 0xb9, 0xed, 0x5f, 0x44, 0x3c, 0x1a, 0x66, 0xdf, 0xdb, 0x28,
	0xe2, 0x7e, 0xd1, 0xe8, 0x73, 0x4c, 0x73, 0x1c, 0xa5, 0xe3, 0x48, 0x47, 0x61, 0x0d, 0x77, 0xd6,
	0x11, 0xf0, 0xa3, 0x6e, 0x66, 0x3f, 0xcc, 0x54, 0xf0, 0x18, 0xe6, 0xf8, 0xeb, 0x11, 0x70, 0x3b,
	0x6f, 0x58, 0x26, 0x62, 0xf7, 0xc3, 0x72, 0x37, 0x1f, 0x50, 0x32, 0x3d, 0x6f, 0x3e, 0xb8, 0x04,
	0x63, 0xc4, 0x91, 0xf8, 0xc2, 0x8f, 0xe2, 0x57, 0xba, 0x2f, 0x1a, 0xf9, 0xc5, 0xdf, 0x77, 0x04,
	0xb8, 0x1f, 0x8d, 0xe3, 0x03, 0x3f, 0x1d, 0x1d, 0x36, 0xc7, 0x50, 0x2c, 0xc2, 0x82, 0x89, 0x90,
	0xa6, 0x68, 0xe8, 0x40, 0x6d, 0x1b, 0x2e, 0xa7, 0x57, 0xf6, 0x5b, 0x55, 0x9a, 0xb2, 0x35, 0x87,
	0x91, 0x36, 0x28, 0x0e, 0x67, 0x22, 0x18, 0x43, 0xfa, 0x76, 0x0a, 0x5e, 0x19, 0x5c, 0xbc, 0xa1,
	0x92, 0x15, 0x1d, 0x96, 0xac, 0x98, 0x96, 0xf1, 0xa3, 0xf8, 0x3a, 0x5c, 0x3a, 0x54, 0x23, 0xff,
	0x30, 0x55, 0x9a, 0x8e, 0xf7, 0x9f, 0xc2, 0xe9, 0x43, 0x35, 0xf4, 0x23, 0xd3, 0x6d, 0xa7, 0x21,
	0xda, 0x30, 0xd3, 0xa9, 0x66, 0xcc, 0xbf, 0x63, 0x13, 0xa7, 0x32, 0xf3, 0x55, 0x94, 0xa7, 0x35,
	0x4f, 0x3d, 0x1c, 0x54, 0xfa, 0x6d, 0x4c, 0xf6, 0x79, 0xe0, 0x6f, 0xb1, 0x49, 0xcf, 0x48, 0xf1,
	0xff, 0x59, 0x4d, 0x05, 0xfe, 0xb3, 0x8a, 0x7b, 0x6b, 0xe7, 0x87, 0xb3, 0x6c, 0x65, 0xca, 0xe9,
	0xf2, 0xa7, 0xd9, 0x4c, 0xe4, 0x4f, 0xb3, 0x18, 0x81, 0x0a, 0xc2, 0xfd, 0x4b, 0x04, 0x28, 0xc8,
	0x9b, 0x05, 0x33, 0x04, 0xee, 0x82, 0x7b, 0x86, 0x40, 0x8e, 0xae, 0x7c, 0x1a, 0xa6, 0xc2, 0xb7,
	0xfc, 0x75, 0xbd, 0x1a, 0x50, 0xe8, 0x71, 0x35, 0xe0, 0x22, 0xe4, 0x5b, 0x7a, 0xdd, 0x6d, 0xdb,
	0x48, 0xe9, 0xfc, 0x17, 0x03, 0x18, 0x08, 0x8f, 0xe8, 0x31, 0xd7, 0x66, 0x4a, 0x5f, 0x15, 0xe2,
	0x7e, 0xb4, 0x10, 0x6c, 0xa2, 0x84, 0x7d, 0xe3, 0x12, 0x8c, 0xb5, 0x2c, 0x87, 0xbb, 0x21, 0x73,
	0x14, 0xbf, 0x52, 0x5d, 0xf2, 0xf7, 0x82, 0xa5, 0xc3, 0xf7, 0x82, 0x49, 0xdf, 0x8f, 0x5d, 0x35,
	0x0a, 0x09, 0x33, 0xcc, 0x4f, 0xb1, 0x46, 0xb9, 0x5e, 0xf9, 0xa2, 0x06, 0xcb, 0x68, 0x2d, 0xff,
	0xb9, 0x40, 0xf3, 0x8a, 0xba, 0xfe, 0x3c, 0x5c, 0x5c, 0x85, 0x95, 0xed, 0xbd, 0x6a, 0x49, 0xa9,
	0xee, 0xac, 0x97, 0x8b, 0x5b, 0xe4, 0x3c, 0xec, 0x5e, 0xa5, 0x5c, 0x7b, 0x53, 0xa9, 0x14, 0x1f,
	0x2b, 0x6b, 0x45, 0x59, 0x79, 0xb0, 0x55, 0x7c, 0xa8, 0xec, 0x55, 0xaa, 0xbb, 0xa5, 0xf5, 0xf2,
	0x83, 0x72, 0x69, 0xa3, 0x70, 0x4e, 0x7c, 0x09, 0x6e, 0x0f, 0xf0, 0xcd, 0x76, 0xb9, 0x52, 0x2a,
	0x08, 0xe2, 0x3d, 0xb8, 0x3b, 0x00, 0xb2, 0x5c, 0x5a, 0x2f, 0x55, 0x6a, 0x85, 0x94, 0xb8, 0x02,
	0xcb, 0x03, 0xa0, 0xd7, 0x76, 0xf6, 0xe4, 0x72, 0xb5, 0x56, 0x48, 0x2f, 0xb7, 0xa0, 0x10, 0xfe,
	0x87, 0xad, 0x28, 0xc1, 0xd5, 0x35, 0xb9, 0x58, 0xd9, 0x50, 0xd6, 0x37, 0x8b, 0x95, 0x4a, 0x69,
	0x4b, 0xa9, 0xbd, 0xb9, 0x5b, 0x0a, 0xd5, 0x61, 0x1e, 0x2e, 0xc5, 0xe0, 0xac, 0x6f, 0x16, 0x6b,
	0x05, 0xa1, 0x4b, 0x61, 0x75, 0x73, 0xe7, 0x49, 0x21, 0xb5, 0xfc, 0x17, 0x02, 0x9c, 0x8f, 0x5c,
	0x88, 0x2c, 0xde, 0x80, 0x25, 0xfa, 0xc9, 0x76, 0x69, 0x7b, 0xad, 0x24, 0x2b, 0xe4, 0x74, 0xf1,
	0xe3, 0xd5, 0x10, 0xd7, 0x25, 0xb8, 0x12, 0x8b, 0xb5, 0x5e, 0xdc, 0xad, 0x15, 0xcb, 0x95, 0x82,
	0x20, 0x2e, 0xc2, 0x7c, 0x2c, 0xc6, 0xa3, 0x92, 0x5c, 0x29, 0x6d, 0x15, 0x52, 0xe2, 0x4d, 0xb8,
	0x16, 0x8b, 0xf0, 0xb8, 0xbc, 0x5e, 0xf2, 0xe9, 0xa4, 0xc5, 0x6b, 0xb0, 0x10, 0x8b, 0xb6, 0x2b,
	0xef, 0x6c, 0xec, 0xad, 0x97, 0xe4, 0x42, 0x46, 0x5c, 0x80, 0xcb, 0xb1, 0x28, 0x0f, 0x8a, 0x95,
	0x6a, 0x61, 0x64, 0xf9, 0xeb, 0x42, 0xe4, 0x66, 0x3f, 0xd9, 0xbb, 0xc4, 0x7e, 0x19, 0x6e, 0x45,
	0x1a, 0x48, 0x2e, 0x56, 0x1e, 0xc5, 0x69, 0xfa, 0x3a, 0x2c, 0xf6, 0xc0, 0x7d, 0x52, 0x2a, 0x3d,
	0x2a, 0x08, 0x7d, 0x90, 0x36, 0x77, 0xf6, 0xe4, 0x42, 0x6a, 0x59, 0xf1, 0x8e, 0x28, 0xb1, 0x33,
	0x1f, 0x0b, 0x70, 0x99, 0x89, 0x5e, 0xad, 0x15, 0x6b, 0x7b, 0xd5, 0x10, 0xe3, 0x59, 0x98, 0x0e,
	0x16, 0xcb, 0xa5, 0xc7, 0x3b, 0x8f, 0xb0, 0x4d, 0xce, 0x80, 0x18, 0x2c, 0x29, 0x7d, 0xa2, 0x5c,
	0x2b, 0xa4, 0x96, 0xbf, 0x29, 0x44, 0xfe, 0x21, 0xc7, 0x5f, 0x49, 0x7b, 0x0b, 0xa4, 0x88, 0x90,
	0xe4, 0x80, 0xb9, 0x47, 0x86, 0x1d, 0x3e, 0xbf, 0x09, 0xd7, 0x7a, 0xe3, 0x6d, 0x16, 0xab, 0x05,
	0x41, 0xbc, 0x0d, 0xd7, 0x7b, 0xa3, 0x15, 0xf7, 0x36, 0x88, 0x58, 0x4d, 0x98, 0x5a, 0x0b, 0x3a,
	0x53, 0xf1, 0x32, 0x5c, 0xa4, 0x6d, 0xb7, 0x2b, 0xef, 0x3c, 0x28, 0x55, 0xab, 0xe5, 0x9d, 0x8a,
	0xc7, 0xdd, 0x6f, 0x79, 0xae, 0xa8, 0xf3, 0x58, 0xdc, 0x2a, 0x08, 0xe2, 0x15, 0x98, 0x8d, 0xa0,
	0x14, 0xb7, 0x8b, 0xb5, 0x12, 0x51, 0xf3, 0x37, 0xa2, 0x0b, 0x2d, 0x38, 0xe8, 0x65, 0x4a, 0x88,
	0xab, 0xdc, 0xe3, 0x72, 0xe9, 0x49, 0x48, 0x07, 0x71, 0xba, 0xe2, 0xd1, 0x68, 0xdd, 0xe2, 0x95,
	0xc0, 0xe3, 0xc9, 0xa5, 0x8f, 0x95, 0xd6, 0xb1, 0x12, 0xfe, 0x2c, 0x6a, 0x8e, 0x8f, 0xd9, 0x64,
	0x2e, 0xd6, 0x7a, 0x08, 0x11, 0x62, 0x3d, 0x4c, 0xa2, 0x1b, 0xb0, 0xd4, 0x03, 0xa9, 0xaf, 0x3c,
	0x14, 0xab, 0x52, 0xd9, 0xd9, 0xab, 0xac, 0x97, 0x0a, 0xa9, 0x3e, 0x3c, 0xe5, 0x9d, 0x9d, 0xed,
	0x42, 0xba, 0x0f, 0xcf, 0x87, 0xf2, 0xce, 0xde, 0x6e, 0x21, 0x83, 0x15, 0x3e, 0xbd, 0x16, 0x37,
	0x5a, 0xfa, 0x4e, 0x85, 0x6f, 0xbf, 0x72, 0x75, 0x5b, 0xa1, 0x50, 0x56, 0xb1, 0x97, 0xe1, 0x4e,
	0x2f, 0xac, 0x50, 0xdb, 0xdf, 0x86, 0xeb, 0xbd, 0xb0, 0x3b, 0x66, 0x60, 0xc3, 0x79, 0x3e, 0x1d,
	0xce, 0x21, 0x9a, 0x9e, 0x87, 0x4b, 0x5b, 0xe5, 0x6a, 0xcd, 0x57, 0x44, 0x95, 0xe9, 0x65, 0x6b,
	0xab, 0x70, 0x0e, 0x9b, 0x55, 0x5c, 0x21, 0x1b, 0x08, 0x24, 0xb8, 0x1a, 0x57, 0x5a, 0xae, 0xd4,
	0x4a, 0x72, 0xa9, 0x5a, 0x2b, 0x6d, 0x14, 0x52, 0xcb, 0x5f, 0x49, 0xd1, 0xdf, 0xc9, 0xc7, 0x24,
	0x97, 0x11, 0xf6, 0xb7, 0xe1, 0x3a, 0x19, 0x1d, 0x3c, 0x1a, 0xca, 0x46, 0xa9, 0x5a, 0x2b, 0x57,
	0x8a, 0x35, 0x6c, 0xc1, 0x7c, 0x63, 0xdf, 0x81, 0x1b, 0x7d, 0x10, 0xa9, 0xf2, 0x05, 0xf1, 0x3e,
	0xbc, 0xd4, 0x07, 0x13, 0x0f, 0x0a, 0xde, 0x48, 0x50, 0x48, 0x0d, 0xf0, 0x01, 0x1e, 0x28, 0xfc,
	0x0f, 0xd2, 0xe2, 0xab, 0x70, 0xbf, 0xcf, 0x07, 0xbb, 0x25, 0xb9, 0x8a, 0x9b, 0xc1, 0xff, 0x28,
	0xb3, 0xfc, 0x04, 0xa6, 0xaa, 0xc8, 0x2d, 0x73, 0xe9, 0x58, 0x78, 0xf0, 0xa8, 0x96, 0x6a, 0xbe,
	0xc2, 0x22, 0xea, 0x3b, 0x87, 0x55, 0x1c, 0xc5, 0xd8, 0xab, 0x70, 0x38, 0xc2, 0xf2, 0xbf, 0x15,
	0x60, 0xa9, 0xd7, 0x9e, 0x28, 0x61, 0x75, 0x0f, 0xee, 0x46, 0xec, 0xb6, 0xfa, 0x66, 0xb5, 0x56,
	0xda, 0x56, 0xb6, 0x4b, 0xd5, 0x6a, 0xf1, 0x61, 0x29, 0xa0, 0xed, 0x41, 0xd1, 0x3f, 0xb6, 0x83,
	0xc7, 0xb8, 0xe5, 0xff, 0x26, 0xc0, 0xbc, 0x97, 0x6c, 0x10, 0xde, 0xd4, 0xb3, 0x34, 0xd2, 0xca,
	0xc5, 0x8d, 0x8d, 0x32, 0xd1, 0x4f, 0x84, 0xee, 0xf6, 0xce, 0x06, 0xcf, 0xb7, 0x0f, 0x62, 0x71,
	0x77, 0x77, 0xeb, 0x4d, 0xc6, 0x17, 0xc7, 0x16, 0x7d, 0xd0, 0xa9, 0xb7, 0xa1, 0xf8, 0xa9, 0xe5,
	0x6f, 0xa4, 0x60, 0xa9, 0xdf, 0xc6, 0x23, 0x96, 0x01, 0xa3, 0xc7, 0x10, 0x64, 0x35, 0x0f, 0xfa,
	0xc5, 0xfb, 0xf0, 0xd2, 0x40, 0xe8, 0x72, 0x09, 0xfb, 0x09, 0x1a, 0x3f, 0x0d, 0xf4, 0xc1, 0x6e,
	0xb1, 0x5a, 0xa5, 0xd6, 0x39, 0x20, 0x7d, 0xe2, 0x57, 0xd3, 0x03, 0x7f, 0x50, 0xfa, 0xc4, 0x6e,
	0x59, 0x2e, 0x15, 0x32, 0x78, 0x14, 0xf6, 0xd6, 0x41, 0x88, 0xad, 0x5c, 0x86, 0x8b, 0xb5, 0x62,
	0xf5, 0x51, 0xc0, 0xe1, 0xca, 0xdb, 0xc5, 0x2d, 0x1a, 0x64, 0x85, 0x8a, 0xd6, 0x37, 0x4b, 0xeb,
	0x8f, 0x14, 0xd2, 0x1a, 0x97, 0xe0, 0x42, 0xa8, 0xb0, 0x5a, 0x2b, 0xed, 0x16, 0x52, 0x6b, 0x35,
	0x98, 0xad, 0x5b, 0xcd, 0x95, 0x13, 0xfd, 0xc4, 0x6a, 0x93, 0x28, 0xd8, 0xd2, 0x90, 0xb1, 0xd2,
	0xb2, 0x2d, 0xd7, 0x7a, 0xeb, 0x83, 0x0d, 0xcb, 0x50, 0xcd, 0xc6, 0xca, 0xeb, 0xab, 0xae, 0xbb,
	0x52, 0xb7, 0x9a, 0xf7, 0x09, 0xb8, 0x6e, 0x19, 0xf7, 0xd5, 0x56, 0xeb, 0x3e, 0x8e, 0x97, 0xef,
	0xd1, 0x78, 0xf9, 0x9e, 0x1f, 0x2f, 0xdf, 0x23, 0xf1, 0xf2, 0xfe, 0x28, 0xc1, 0x7c, 0xf5, 0x1f,
	0x02, 0x00, 0x00, 0xff, 0xff, 0x76, 0xf5, 0xcb, 0x64, 0x7d, 0x8b, 0x00, 0x00,
}
