// Code generated by protoc-gen-go. DO NOT EDIT.
// source: you-know-who-logic_.proto

package youknowwhologic // import "golang.52tt.com/protocol/app/youknowwhologic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 开通状态(该状态受过期时间影响)
type UKWOpenType int32

const (
	UKWOpenType_UKW_NO_OPEN UKWOpenType = 0
	UKWOpenType_UKW_OPEN    UKWOpenType = 1
	UKWOpenType_UKW_FREEZE  UKWOpenType = 2
	UKWOpenType_UKW_BAN     UKWOpenType = 3
)

var UKWOpenType_name = map[int32]string{
	0: "UKW_NO_OPEN",
	1: "UKW_OPEN",
	2: "UKW_FREEZE",
	3: "UKW_BAN",
}
var UKWOpenType_value = map[string]int32{
	"UKW_NO_OPEN": 0,
	"UKW_OPEN":    1,
	"UKW_FREEZE":  2,
	"UKW_BAN":     3,
}

func (x UKWOpenType) String() string {
	return proto.EnumName(UKWOpenType_name, int32(x))
}
func (UKWOpenType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{0}
}

// 开关状态（该状态受开通状态影响）
type UKWSwitchType int32

const (
	UKWSwitchType_UKW_SWITCH_OFF UKWSwitchType = 0
	UKWSwitchType_UKW_SWITCH_ON  UKWSwitchType = 1
)

var UKWSwitchType_name = map[int32]string{
	0: "UKW_SWITCH_OFF",
	1: "UKW_SWITCH_ON",
}
var UKWSwitchType_value = map[string]int32{
	"UKW_SWITCH_OFF": 0,
	"UKW_SWITCH_ON":  1,
}

func (x UKWSwitchType) String() string {
	return proto.EnumName(UKWSwitchType_name, int32(x))
}
func (UKWSwitchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{1}
}

// 显示状态（该状态受开通状态、开关状态等影响）
type UKWOpenStatus int32

const (
	UKWOpenStatus_UKW_OFF UKWOpenStatus = 0
	UKWOpenStatus_UKW_ON  UKWOpenStatus = 1
)

var UKWOpenStatus_name = map[int32]string{
	0: "UKW_OFF",
	1: "UKW_ON",
}
var UKWOpenStatus_value = map[string]int32{
	"UKW_OFF": 0,
	"UKW_ON":  1,
}

func (x UKWOpenStatus) String() string {
	return proto.EnumName(UKWOpenStatus_name, int32(x))
}
func (UKWOpenStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{2}
}

// 周榜/爱意榜开关（该状态受开关状态影响）
type RankSwitchType int32

const (
	RankSwitchType_UKW_RANK_SWITCH_OFF RankSwitchType = 0
	RankSwitchType_UKW_RANK_SWITCH_ON  RankSwitchType = 1
)

var RankSwitchType_name = map[int32]string{
	0: "UKW_RANK_SWITCH_OFF",
	1: "UKW_RANK_SWITCH_ON",
}
var RankSwitchType_value = map[string]int32{
	"UKW_RANK_SWITCH_OFF": 0,
	"UKW_RANK_SWITCH_ON":  1,
}

func (x RankSwitchType) String() string {
	return proto.EnumName(RankSwitchType_name, int32(x))
}
func (RankSwitchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{3}
}

// 神秘人勋章状态
type MedalType int32

const (
	MedalType_MEDAL_TYPE_NOT_WEAR MedalType = 0
	MedalType_MEDAL_TYPE_WEAR     MedalType = 1
)

var MedalType_name = map[int32]string{
	0: "MEDAL_TYPE_NOT_WEAR",
	1: "MEDAL_TYPE_WEAR",
}
var MedalType_value = map[string]int32{
	"MEDAL_TYPE_NOT_WEAR": 0,
	"MEDAL_TYPE_WEAR":     1,
}

func (x MedalType) String() string {
	return proto.EnumName(MedalType_name, int32(x))
}
func (MedalType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{4}
}

// 神秘人权限信息
type UKWPermissionInfo struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string         `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Status               UKWOpenType    `protobuf:"varint,3,opt,name=status,proto3,enum=ga.youknowwhologic.UKWOpenType" json:"status,omitempty"`
	Switch               UKWSwitchType  `protobuf:"varint,4,opt,name=switch,proto3,enum=ga.youknowwhologic.UKWSwitchType" json:"switch,omitempty"`
	ExpireTime           uint32         `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	ServerTime           uint32         `protobuf:"varint,6,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	Level                uint32         `protobuf:"varint,7,opt,name=level,proto3" json:"level,omitempty"`
	RankSwitch           RankSwitchType `protobuf:"varint,8,opt,name=rank_switch,json=rankSwitch,proto3,enum=ga.youknowwhologic.RankSwitchType" json:"rank_switch,omitempty"`
	FakeUid              uint32         `protobuf:"varint,9,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	IsOpen               UKWOpenStatus  `protobuf:"varint,10,opt,name=is_open,json=isOpen,proto3,enum=ga.youknowwhologic.UKWOpenStatus" json:"is_open,omitempty"`
	EnterNoticeSwitch    UKWSwitchType  `protobuf:"varint,11,opt,name=enter_notice_switch,json=enterNoticeSwitch,proto3,enum=ga.youknowwhologic.UKWSwitchType" json:"enter_notice_switch,omitempty"`
	FakeAccount          string         `protobuf:"bytes,12,opt,name=fake_account,json=fakeAccount,proto3" json:"fake_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UKWPermissionInfo) Reset()         { *m = UKWPermissionInfo{} }
func (m *UKWPermissionInfo) String() string { return proto.CompactTextString(m) }
func (*UKWPermissionInfo) ProtoMessage()    {}
func (*UKWPermissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{0}
}
func (m *UKWPermissionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UKWPermissionInfo.Unmarshal(m, b)
}
func (m *UKWPermissionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UKWPermissionInfo.Marshal(b, m, deterministic)
}
func (dst *UKWPermissionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UKWPermissionInfo.Merge(dst, src)
}
func (m *UKWPermissionInfo) XXX_Size() int {
	return xxx_messageInfo_UKWPermissionInfo.Size(m)
}
func (m *UKWPermissionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UKWPermissionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UKWPermissionInfo proto.InternalMessageInfo

func (m *UKWPermissionInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UKWPermissionInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UKWPermissionInfo) GetStatus() UKWOpenType {
	if m != nil {
		return m.Status
	}
	return UKWOpenType_UKW_NO_OPEN
}

func (m *UKWPermissionInfo) GetSwitch() UKWSwitchType {
	if m != nil {
		return m.Switch
	}
	return UKWSwitchType_UKW_SWITCH_OFF
}

func (m *UKWPermissionInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *UKWPermissionInfo) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *UKWPermissionInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UKWPermissionInfo) GetRankSwitch() RankSwitchType {
	if m != nil {
		return m.RankSwitch
	}
	return RankSwitchType_UKW_RANK_SWITCH_OFF
}

func (m *UKWPermissionInfo) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

func (m *UKWPermissionInfo) GetIsOpen() UKWOpenStatus {
	if m != nil {
		return m.IsOpen
	}
	return UKWOpenStatus_UKW_OFF
}

func (m *UKWPermissionInfo) GetEnterNoticeSwitch() UKWSwitchType {
	if m != nil {
		return m.EnterNoticeSwitch
	}
	return UKWSwitchType_UKW_SWITCH_OFF
}

func (m *UKWPermissionInfo) GetFakeAccount() string {
	if m != nil {
		return m.FakeAccount
	}
	return ""
}

// 神秘人人物信息
type UKWPersonInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Medal                string   `protobuf:"bytes,4,opt,name=medal,proto3" json:"medal,omitempty"`
	Level                uint32   `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
	HeadFrame            string   `protobuf:"bytes,6,opt,name=head_frame,json=headFrame,proto3" json:"head_frame,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UKWPersonInfo) Reset()         { *m = UKWPersonInfo{} }
func (m *UKWPersonInfo) String() string { return proto.CompactTextString(m) }
func (*UKWPersonInfo) ProtoMessage()    {}
func (*UKWPersonInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{1}
}
func (m *UKWPersonInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UKWPersonInfo.Unmarshal(m, b)
}
func (m *UKWPersonInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UKWPersonInfo.Marshal(b, m, deterministic)
}
func (dst *UKWPersonInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UKWPersonInfo.Merge(dst, src)
}
func (m *UKWPersonInfo) XXX_Size() int {
	return xxx_messageInfo_UKWPersonInfo.Size(m)
}
func (m *UKWPersonInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UKWPersonInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UKWPersonInfo proto.InternalMessageInfo

func (m *UKWPersonInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UKWPersonInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UKWPersonInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UKWPersonInfo) GetMedal() string {
	if m != nil {
		return m.Medal
	}
	return ""
}

func (m *UKWPersonInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UKWPersonInfo) GetHeadFrame() string {
	if m != nil {
		return m.HeadFrame
	}
	return ""
}

// 神秘人个人主页/资料卡勋章
type UKWMedal struct {
	Type                 MedalType `protobuf:"varint,1,opt,name=type,proto3,enum=ga.youknowwhologic.MedalType" json:"type,omitempty"`
	MedalUrl             string    `protobuf:"bytes,2,opt,name=medal_url,json=medalUrl,proto3" json:"medal_url,omitempty"`
	JumpUrl              string    `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	NeedNotice           bool      `protobuf:"varint,4,opt,name=need_notice,json=needNotice,proto3" json:"need_notice,omitempty"`
	Notice               string    `protobuf:"bytes,5,opt,name=notice,proto3" json:"notice,omitempty"`
	IsExpired            bool      `protobuf:"varint,6,opt,name=is_expired,json=isExpired,proto3" json:"is_expired,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *UKWMedal) Reset()         { *m = UKWMedal{} }
func (m *UKWMedal) String() string { return proto.CompactTextString(m) }
func (*UKWMedal) ProtoMessage()    {}
func (*UKWMedal) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{2}
}
func (m *UKWMedal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UKWMedal.Unmarshal(m, b)
}
func (m *UKWMedal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UKWMedal.Marshal(b, m, deterministic)
}
func (dst *UKWMedal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UKWMedal.Merge(dst, src)
}
func (m *UKWMedal) XXX_Size() int {
	return xxx_messageInfo_UKWMedal.Size(m)
}
func (m *UKWMedal) XXX_DiscardUnknown() {
	xxx_messageInfo_UKWMedal.DiscardUnknown(m)
}

var xxx_messageInfo_UKWMedal proto.InternalMessageInfo

func (m *UKWMedal) GetType() MedalType {
	if m != nil {
		return m.Type
	}
	return MedalType_MEDAL_TYPE_NOT_WEAR
}

func (m *UKWMedal) GetMedalUrl() string {
	if m != nil {
		return m.MedalUrl
	}
	return ""
}

func (m *UKWMedal) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *UKWMedal) GetNeedNotice() bool {
	if m != nil {
		return m.NeedNotice
	}
	return false
}

func (m *UKWMedal) GetNotice() string {
	if m != nil {
		return m.Notice
	}
	return ""
}

func (m *UKWMedal) GetIsExpired() bool {
	if m != nil {
		return m.IsExpired
	}
	return false
}

// 神秘人信息
type UKWInfo struct {
	Uid                  uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UkwPermissionInfo    *UKWPermissionInfo `protobuf:"bytes,2,opt,name=ukw_permission_info,json=ukwPermissionInfo,proto3" json:"ukw_permission_info,omitempty"`
	UkwPersonInfo        *UKWPersonInfo     `protobuf:"bytes,3,opt,name=ukw_person_info,json=ukwPersonInfo,proto3" json:"ukw_person_info,omitempty"`
	UkwMedal             *UKWMedal          `protobuf:"bytes,4,opt,name=ukw_medal,json=ukwMedal,proto3" json:"ukw_medal,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UKWInfo) Reset()         { *m = UKWInfo{} }
func (m *UKWInfo) String() string { return proto.CompactTextString(m) }
func (*UKWInfo) ProtoMessage()    {}
func (*UKWInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{3}
}
func (m *UKWInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UKWInfo.Unmarshal(m, b)
}
func (m *UKWInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UKWInfo.Marshal(b, m, deterministic)
}
func (dst *UKWInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UKWInfo.Merge(dst, src)
}
func (m *UKWInfo) XXX_Size() int {
	return xxx_messageInfo_UKWInfo.Size(m)
}
func (m *UKWInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UKWInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UKWInfo proto.InternalMessageInfo

func (m *UKWInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UKWInfo) GetUkwPermissionInfo() *UKWPermissionInfo {
	if m != nil {
		return m.UkwPermissionInfo
	}
	return nil
}

func (m *UKWInfo) GetUkwPersonInfo() *UKWPersonInfo {
	if m != nil {
		return m.UkwPersonInfo
	}
	return nil
}

func (m *UKWInfo) GetUkwMedal() *UKWMedal {
	if m != nil {
		return m.UkwMedal
	}
	return nil
}

type OpenUKWReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OpenUKWReq) Reset()         { *m = OpenUKWReq{} }
func (m *OpenUKWReq) String() string { return proto.CompactTextString(m) }
func (*OpenUKWReq) ProtoMessage()    {}
func (*OpenUKWReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{4}
}
func (m *OpenUKWReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenUKWReq.Unmarshal(m, b)
}
func (m *OpenUKWReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenUKWReq.Marshal(b, m, deterministic)
}
func (dst *OpenUKWReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenUKWReq.Merge(dst, src)
}
func (m *OpenUKWReq) XXX_Size() int {
	return xxx_messageInfo_OpenUKWReq.Size(m)
}
func (m *OpenUKWReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenUKWReq.DiscardUnknown(m)
}

var xxx_messageInfo_OpenUKWReq proto.InternalMessageInfo

func (m *OpenUKWReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type OpenUKWResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ServerTime           uint32        `protobuf:"varint,2,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	ExpireTime           uint32        `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OpenUKWResp) Reset()         { *m = OpenUKWResp{} }
func (m *OpenUKWResp) String() string { return proto.CompactTextString(m) }
func (*OpenUKWResp) ProtoMessage()    {}
func (*OpenUKWResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{5}
}
func (m *OpenUKWResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OpenUKWResp.Unmarshal(m, b)
}
func (m *OpenUKWResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OpenUKWResp.Marshal(b, m, deterministic)
}
func (dst *OpenUKWResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenUKWResp.Merge(dst, src)
}
func (m *OpenUKWResp) XXX_Size() int {
	return xxx_messageInfo_OpenUKWResp.Size(m)
}
func (m *OpenUKWResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenUKWResp.DiscardUnknown(m)
}

var xxx_messageInfo_OpenUKWResp proto.InternalMessageInfo

func (m *OpenUKWResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OpenUKWResp) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *OpenUKWResp) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type ChangeUKWSwitchReq struct {
	BaseReq              *app.BaseReq  `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Status               UKWSwitchType `protobuf:"varint,2,opt,name=status,proto3,enum=ga.youknowwhologic.UKWSwitchType" json:"status,omitempty"`
	ChannelId            uint32        `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChangeUKWSwitchReq) Reset()         { *m = ChangeUKWSwitchReq{} }
func (m *ChangeUKWSwitchReq) String() string { return proto.CompactTextString(m) }
func (*ChangeUKWSwitchReq) ProtoMessage()    {}
func (*ChangeUKWSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{6}
}
func (m *ChangeUKWSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeUKWSwitchReq.Unmarshal(m, b)
}
func (m *ChangeUKWSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeUKWSwitchReq.Marshal(b, m, deterministic)
}
func (dst *ChangeUKWSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeUKWSwitchReq.Merge(dst, src)
}
func (m *ChangeUKWSwitchReq) XXX_Size() int {
	return xxx_messageInfo_ChangeUKWSwitchReq.Size(m)
}
func (m *ChangeUKWSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeUKWSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeUKWSwitchReq proto.InternalMessageInfo

func (m *ChangeUKWSwitchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChangeUKWSwitchReq) GetStatus() UKWSwitchType {
	if m != nil {
		return m.Status
	}
	return UKWSwitchType_UKW_SWITCH_OFF
}

func (m *ChangeUKWSwitchReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChangeUKWSwitchResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChangeUKWSwitchResp) Reset()         { *m = ChangeUKWSwitchResp{} }
func (m *ChangeUKWSwitchResp) String() string { return proto.CompactTextString(m) }
func (*ChangeUKWSwitchResp) ProtoMessage()    {}
func (*ChangeUKWSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{7}
}
func (m *ChangeUKWSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeUKWSwitchResp.Unmarshal(m, b)
}
func (m *ChangeUKWSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeUKWSwitchResp.Marshal(b, m, deterministic)
}
func (dst *ChangeUKWSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeUKWSwitchResp.Merge(dst, src)
}
func (m *ChangeUKWSwitchResp) XXX_Size() int {
	return xxx_messageInfo_ChangeUKWSwitchResp.Size(m)
}
func (m *ChangeUKWSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeUKWSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeUKWSwitchResp proto.InternalMessageInfo

func (m *ChangeUKWSwitchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 神秘人信息
type GetUKWInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUKWInfoReq) Reset()         { *m = GetUKWInfoReq{} }
func (m *GetUKWInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUKWInfoReq) ProtoMessage()    {}
func (*GetUKWInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{8}
}
func (m *GetUKWInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUKWInfoReq.Unmarshal(m, b)
}
func (m *GetUKWInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUKWInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUKWInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUKWInfoReq.Merge(dst, src)
}
func (m *GetUKWInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUKWInfoReq.Size(m)
}
func (m *GetUKWInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUKWInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUKWInfoReq proto.InternalMessageInfo

func (m *GetUKWInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUKWInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UkwInfo              *UKWInfo      `protobuf:"bytes,2,opt,name=ukw_info,json=ukwInfo,proto3" json:"ukw_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUKWInfoResp) Reset()         { *m = GetUKWInfoResp{} }
func (m *GetUKWInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUKWInfoResp) ProtoMessage()    {}
func (*GetUKWInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{9}
}
func (m *GetUKWInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUKWInfoResp.Unmarshal(m, b)
}
func (m *GetUKWInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUKWInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUKWInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUKWInfoResp.Merge(dst, src)
}
func (m *GetUKWInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUKWInfoResp.Size(m)
}
func (m *GetUKWInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUKWInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUKWInfoResp proto.InternalMessageInfo

func (m *GetUKWInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUKWInfoResp) GetUkwInfo() *UKWInfo {
	if m != nil {
		return m.UkwInfo
	}
	return nil
}

// 获取神秘人人物信息
type GetUKWUserProfileReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	IsPersonalHomepage   bool         `protobuf:"varint,3,opt,name=is_personal_homepage,json=isPersonalHomepage,proto3" json:"is_personal_homepage,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUKWUserProfileReq) Reset()         { *m = GetUKWUserProfileReq{} }
func (m *GetUKWUserProfileReq) String() string { return proto.CompactTextString(m) }
func (*GetUKWUserProfileReq) ProtoMessage()    {}
func (*GetUKWUserProfileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{10}
}
func (m *GetUKWUserProfileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUKWUserProfileReq.Unmarshal(m, b)
}
func (m *GetUKWUserProfileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUKWUserProfileReq.Marshal(b, m, deterministic)
}
func (dst *GetUKWUserProfileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUKWUserProfileReq.Merge(dst, src)
}
func (m *GetUKWUserProfileReq) XXX_Size() int {
	return xxx_messageInfo_GetUKWUserProfileReq.Size(m)
}
func (m *GetUKWUserProfileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUKWUserProfileReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUKWUserProfileReq proto.InternalMessageInfo

func (m *GetUKWUserProfileReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUKWUserProfileReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUKWUserProfileReq) GetIsPersonalHomepage() bool {
	if m != nil {
		return m.IsPersonalHomepage
	}
	return false
}

type GetUKWUserProfileResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Uid                  uint32           `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	UserProfile          *app.UserProfile `protobuf:"bytes,3,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	UserUkwInfo          *app.UserUKWInfo `protobuf:"bytes,4,opt,name=user_ukw_info,json=userUkwInfo,proto3" json:"user_ukw_info,omitempty"`
	UkwMedal             *UKWMedal        `protobuf:"bytes,5,opt,name=ukw_medal,json=ukwMedal,proto3" json:"ukw_medal,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUKWUserProfileResp) Reset()         { *m = GetUKWUserProfileResp{} }
func (m *GetUKWUserProfileResp) String() string { return proto.CompactTextString(m) }
func (*GetUKWUserProfileResp) ProtoMessage()    {}
func (*GetUKWUserProfileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{11}
}
func (m *GetUKWUserProfileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUKWUserProfileResp.Unmarshal(m, b)
}
func (m *GetUKWUserProfileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUKWUserProfileResp.Marshal(b, m, deterministic)
}
func (dst *GetUKWUserProfileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUKWUserProfileResp.Merge(dst, src)
}
func (m *GetUKWUserProfileResp) XXX_Size() int {
	return xxx_messageInfo_GetUKWUserProfileResp.Size(m)
}
func (m *GetUKWUserProfileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUKWUserProfileResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUKWUserProfileResp proto.InternalMessageInfo

func (m *GetUKWUserProfileResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUKWUserProfileResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUKWUserProfileResp) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *GetUKWUserProfileResp) GetUserUkwInfo() *app.UserUKWInfo {
	if m != nil {
		return m.UserUkwInfo
	}
	return nil
}

func (m *GetUKWUserProfileResp) GetUkwMedal() *UKWMedal {
	if m != nil {
		return m.UkwMedal
	}
	return nil
}

// 批量获取神秘人信息
type BatchGetUKWInfosReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PageSize             uint32       `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageNum              uint32       `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	UidList              []uint32     `protobuf:"varint,4,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetUKWInfosReq) Reset()         { *m = BatchGetUKWInfosReq{} }
func (m *BatchGetUKWInfosReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUKWInfosReq) ProtoMessage()    {}
func (*BatchGetUKWInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{12}
}
func (m *BatchGetUKWInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUKWInfosReq.Unmarshal(m, b)
}
func (m *BatchGetUKWInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUKWInfosReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUKWInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUKWInfosReq.Merge(dst, src)
}
func (m *BatchGetUKWInfosReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUKWInfosReq.Size(m)
}
func (m *BatchGetUKWInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUKWInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUKWInfosReq proto.InternalMessageInfo

func (m *BatchGetUKWInfosReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchGetUKWInfosReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *BatchGetUKWInfosReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *BatchGetUKWInfosReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUKWInfosResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UkwInfos             []*UKWInfo    `protobuf:"bytes,2,rep,name=ukw_infos,json=ukwInfos,proto3" json:"ukw_infos,omitempty"`
	PageSize             uint32        `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageNum              uint32        `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	Count                uint32        `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchGetUKWInfosResp) Reset()         { *m = BatchGetUKWInfosResp{} }
func (m *BatchGetUKWInfosResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUKWInfosResp) ProtoMessage()    {}
func (*BatchGetUKWInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{13}
}
func (m *BatchGetUKWInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUKWInfosResp.Unmarshal(m, b)
}
func (m *BatchGetUKWInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUKWInfosResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUKWInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUKWInfosResp.Merge(dst, src)
}
func (m *BatchGetUKWInfosResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUKWInfosResp.Size(m)
}
func (m *BatchGetUKWInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUKWInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUKWInfosResp proto.InternalMessageInfo

func (m *BatchGetUKWInfosResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchGetUKWInfosResp) GetUkwInfos() []*UKWInfo {
	if m != nil {
		return m.UkwInfos
	}
	return nil
}

func (m *BatchGetUKWInfosResp) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *BatchGetUKWInfosResp) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *BatchGetUKWInfosResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 神秘人周榜/爱意榜开关
type ChangeRankSwitchReq struct {
	BaseReq              *app.BaseReq   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RankSwitch           RankSwitchType `protobuf:"varint,2,opt,name=rank_switch,json=rankSwitch,proto3,enum=ga.youknowwhologic.RankSwitchType" json:"rank_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ChangeRankSwitchReq) Reset()         { *m = ChangeRankSwitchReq{} }
func (m *ChangeRankSwitchReq) String() string { return proto.CompactTextString(m) }
func (*ChangeRankSwitchReq) ProtoMessage()    {}
func (*ChangeRankSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{14}
}
func (m *ChangeRankSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeRankSwitchReq.Unmarshal(m, b)
}
func (m *ChangeRankSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeRankSwitchReq.Marshal(b, m, deterministic)
}
func (dst *ChangeRankSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeRankSwitchReq.Merge(dst, src)
}
func (m *ChangeRankSwitchReq) XXX_Size() int {
	return xxx_messageInfo_ChangeRankSwitchReq.Size(m)
}
func (m *ChangeRankSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeRankSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeRankSwitchReq proto.InternalMessageInfo

func (m *ChangeRankSwitchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChangeRankSwitchReq) GetRankSwitch() RankSwitchType {
	if m != nil {
		return m.RankSwitch
	}
	return RankSwitchType_UKW_RANK_SWITCH_OFF
}

type ChangeRankSwitchResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RankSwitch           RankSwitchType `protobuf:"varint,2,opt,name=rank_switch,json=rankSwitch,proto3,enum=ga.youknowwhologic.RankSwitchType" json:"rank_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ChangeRankSwitchResp) Reset()         { *m = ChangeRankSwitchResp{} }
func (m *ChangeRankSwitchResp) String() string { return proto.CompactTextString(m) }
func (*ChangeRankSwitchResp) ProtoMessage()    {}
func (*ChangeRankSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{15}
}
func (m *ChangeRankSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeRankSwitchResp.Unmarshal(m, b)
}
func (m *ChangeRankSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeRankSwitchResp.Marshal(b, m, deterministic)
}
func (dst *ChangeRankSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeRankSwitchResp.Merge(dst, src)
}
func (m *ChangeRankSwitchResp) XXX_Size() int {
	return xxx_messageInfo_ChangeRankSwitchResp.Size(m)
}
func (m *ChangeRankSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeRankSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeRankSwitchResp proto.InternalMessageInfo

func (m *ChangeRankSwitchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChangeRankSwitchResp) GetRankSwitch() RankSwitchType {
	if m != nil {
		return m.RankSwitch
	}
	return RankSwitchType_UKW_RANK_SWITCH_OFF
}

// 用户切换神秘人是否进房询问身份切换提醒开关
type UserChangeUKWEnterNoticeReq struct {
	BaseReq              *app.BaseReq  `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	EnterNoticeSwitch    UKWSwitchType `protobuf:"varint,2,opt,name=enter_notice_switch,json=enterNoticeSwitch,proto3,enum=ga.youknowwhologic.UKWSwitchType" json:"enter_notice_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserChangeUKWEnterNoticeReq) Reset()         { *m = UserChangeUKWEnterNoticeReq{} }
func (m *UserChangeUKWEnterNoticeReq) String() string { return proto.CompactTextString(m) }
func (*UserChangeUKWEnterNoticeReq) ProtoMessage()    {}
func (*UserChangeUKWEnterNoticeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{16}
}
func (m *UserChangeUKWEnterNoticeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChangeUKWEnterNoticeReq.Unmarshal(m, b)
}
func (m *UserChangeUKWEnterNoticeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChangeUKWEnterNoticeReq.Marshal(b, m, deterministic)
}
func (dst *UserChangeUKWEnterNoticeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChangeUKWEnterNoticeReq.Merge(dst, src)
}
func (m *UserChangeUKWEnterNoticeReq) XXX_Size() int {
	return xxx_messageInfo_UserChangeUKWEnterNoticeReq.Size(m)
}
func (m *UserChangeUKWEnterNoticeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChangeUKWEnterNoticeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserChangeUKWEnterNoticeReq proto.InternalMessageInfo

func (m *UserChangeUKWEnterNoticeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UserChangeUKWEnterNoticeReq) GetEnterNoticeSwitch() UKWSwitchType {
	if m != nil {
		return m.EnterNoticeSwitch
	}
	return UKWSwitchType_UKW_SWITCH_OFF
}

type UserChangeUKWEnterNoticeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	EnterNoticeSwitch    UKWSwitchType `protobuf:"varint,2,opt,name=enter_notice_switch,json=enterNoticeSwitch,proto3,enum=ga.youknowwhologic.UKWSwitchType" json:"enter_notice_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserChangeUKWEnterNoticeResp) Reset()         { *m = UserChangeUKWEnterNoticeResp{} }
func (m *UserChangeUKWEnterNoticeResp) String() string { return proto.CompactTextString(m) }
func (*UserChangeUKWEnterNoticeResp) ProtoMessage()    {}
func (*UserChangeUKWEnterNoticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{17}
}
func (m *UserChangeUKWEnterNoticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChangeUKWEnterNoticeResp.Unmarshal(m, b)
}
func (m *UserChangeUKWEnterNoticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChangeUKWEnterNoticeResp.Marshal(b, m, deterministic)
}
func (dst *UserChangeUKWEnterNoticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChangeUKWEnterNoticeResp.Merge(dst, src)
}
func (m *UserChangeUKWEnterNoticeResp) XXX_Size() int {
	return xxx_messageInfo_UserChangeUKWEnterNoticeResp.Size(m)
}
func (m *UserChangeUKWEnterNoticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChangeUKWEnterNoticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserChangeUKWEnterNoticeResp proto.InternalMessageInfo

func (m *UserChangeUKWEnterNoticeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UserChangeUKWEnterNoticeResp) GetEnterNoticeSwitch() UKWSwitchType {
	if m != nil {
		return m.EnterNoticeSwitch
	}
	return UKWSwitchType_UKW_SWITCH_OFF
}

// UKWChangeChannelMsg 神秘人房间消息通知
type UKWChangeChannelMsg struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               UKWOpenType    `protobuf:"varint,2,opt,name=status,proto3,enum=ga.youknowwhologic.UKWOpenType" json:"status,omitempty"`
	Switch               UKWSwitchType  `protobuf:"varint,3,opt,name=switch,proto3,enum=ga.youknowwhologic.UKWSwitchType" json:"switch,omitempty"`
	Nickname             string         `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string         `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	ServerTime           uint32         `protobuf:"varint,6,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	ExpireTime           uint32         `protobuf:"varint,7,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	RankSwitch           RankSwitchType `protobuf:"varint,8,opt,name=rank_switch,json=rankSwitch,proto3,enum=ga.youknowwhologic.RankSwitchType" json:"rank_switch,omitempty"`
	FakeUid              uint32         `protobuf:"varint,9,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	IsOpen               UKWOpenStatus  `protobuf:"varint,10,opt,name=is_open,json=isOpen,proto3,enum=ga.youknowwhologic.UKWOpenStatus" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UKWChangeChannelMsg) Reset()         { *m = UKWChangeChannelMsg{} }
func (m *UKWChangeChannelMsg) String() string { return proto.CompactTextString(m) }
func (*UKWChangeChannelMsg) ProtoMessage()    {}
func (*UKWChangeChannelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{18}
}
func (m *UKWChangeChannelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UKWChangeChannelMsg.Unmarshal(m, b)
}
func (m *UKWChangeChannelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UKWChangeChannelMsg.Marshal(b, m, deterministic)
}
func (dst *UKWChangeChannelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UKWChangeChannelMsg.Merge(dst, src)
}
func (m *UKWChangeChannelMsg) XXX_Size() int {
	return xxx_messageInfo_UKWChangeChannelMsg.Size(m)
}
func (m *UKWChangeChannelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UKWChangeChannelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UKWChangeChannelMsg proto.InternalMessageInfo

func (m *UKWChangeChannelMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UKWChangeChannelMsg) GetStatus() UKWOpenType {
	if m != nil {
		return m.Status
	}
	return UKWOpenType_UKW_NO_OPEN
}

func (m *UKWChangeChannelMsg) GetSwitch() UKWSwitchType {
	if m != nil {
		return m.Switch
	}
	return UKWSwitchType_UKW_SWITCH_OFF
}

func (m *UKWChangeChannelMsg) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UKWChangeChannelMsg) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UKWChangeChannelMsg) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *UKWChangeChannelMsg) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *UKWChangeChannelMsg) GetRankSwitch() RankSwitchType {
	if m != nil {
		return m.RankSwitch
	}
	return RankSwitchType_UKW_RANK_SWITCH_OFF
}

func (m *UKWChangeChannelMsg) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

func (m *UKWChangeChannelMsg) GetIsOpen() UKWOpenStatus {
	if m != nil {
		return m.IsOpen
	}
	return UKWOpenStatus_UKW_OFF
}

// UKWPermissionChangeMsg 神秘人用户神秘人权益变更消息
type UKWPermissionChangeMsg struct {
	FakeUid              uint32         `protobuf:"varint,1,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	Status               UKWOpenType    `protobuf:"varint,2,opt,name=status,proto3,enum=ga.youknowwhologic.UKWOpenType" json:"status,omitempty"`
	Switch               UKWSwitchType  `protobuf:"varint,3,opt,name=switch,proto3,enum=ga.youknowwhologic.UKWSwitchType" json:"switch,omitempty"`
	Nickname             string         `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string         `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	ServerTime           uint32         `protobuf:"varint,6,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	ExpireTime           uint32         `protobuf:"varint,7,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	RankSwitch           RankSwitchType `protobuf:"varint,8,opt,name=rank_switch,json=rankSwitch,proto3,enum=ga.youknowwhologic.RankSwitchType" json:"rank_switch,omitempty"`
	IsOpen               UKWOpenStatus  `protobuf:"varint,9,opt,name=is_open,json=isOpen,proto3,enum=ga.youknowwhologic.UKWOpenStatus" json:"is_open,omitempty"`
	EnterNoticeSwitch    UKWSwitchType  `protobuf:"varint,10,opt,name=enter_notice_switch,json=enterNoticeSwitch,proto3,enum=ga.youknowwhologic.UKWSwitchType" json:"enter_notice_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UKWPermissionChangeMsg) Reset()         { *m = UKWPermissionChangeMsg{} }
func (m *UKWPermissionChangeMsg) String() string { return proto.CompactTextString(m) }
func (*UKWPermissionChangeMsg) ProtoMessage()    {}
func (*UKWPermissionChangeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{19}
}
func (m *UKWPermissionChangeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UKWPermissionChangeMsg.Unmarshal(m, b)
}
func (m *UKWPermissionChangeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UKWPermissionChangeMsg.Marshal(b, m, deterministic)
}
func (dst *UKWPermissionChangeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UKWPermissionChangeMsg.Merge(dst, src)
}
func (m *UKWPermissionChangeMsg) XXX_Size() int {
	return xxx_messageInfo_UKWPermissionChangeMsg.Size(m)
}
func (m *UKWPermissionChangeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UKWPermissionChangeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UKWPermissionChangeMsg proto.InternalMessageInfo

func (m *UKWPermissionChangeMsg) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

func (m *UKWPermissionChangeMsg) GetStatus() UKWOpenType {
	if m != nil {
		return m.Status
	}
	return UKWOpenType_UKW_NO_OPEN
}

func (m *UKWPermissionChangeMsg) GetSwitch() UKWSwitchType {
	if m != nil {
		return m.Switch
	}
	return UKWSwitchType_UKW_SWITCH_OFF
}

func (m *UKWPermissionChangeMsg) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UKWPermissionChangeMsg) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UKWPermissionChangeMsg) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *UKWPermissionChangeMsg) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *UKWPermissionChangeMsg) GetRankSwitch() RankSwitchType {
	if m != nil {
		return m.RankSwitch
	}
	return RankSwitchType_UKW_RANK_SWITCH_OFF
}

func (m *UKWPermissionChangeMsg) GetIsOpen() UKWOpenStatus {
	if m != nil {
		return m.IsOpen
	}
	return UKWOpenStatus_UKW_OFF
}

func (m *UKWPermissionChangeMsg) GetEnterNoticeSwitch() UKWSwitchType {
	if m != nil {
		return m.EnterNoticeSwitch
	}
	return UKWSwitchType_UKW_SWITCH_OFF
}

// UKWOpenPushInfo 神秘人开通后全服推送结构
type UKWOpenPushInfo struct {
	Nickname             string   `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	UkwOrderTypeLevel    uint32   `protobuf:"varint,3,opt,name=ukw_order_type_level,json=ukwOrderTypeLevel,proto3" json:"ukw_order_type_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UKWOpenPushInfo) Reset()         { *m = UKWOpenPushInfo{} }
func (m *UKWOpenPushInfo) String() string { return proto.CompactTextString(m) }
func (*UKWOpenPushInfo) ProtoMessage()    {}
func (*UKWOpenPushInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{20}
}
func (m *UKWOpenPushInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UKWOpenPushInfo.Unmarshal(m, b)
}
func (m *UKWOpenPushInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UKWOpenPushInfo.Marshal(b, m, deterministic)
}
func (dst *UKWOpenPushInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UKWOpenPushInfo.Merge(dst, src)
}
func (m *UKWOpenPushInfo) XXX_Size() int {
	return xxx_messageInfo_UKWOpenPushInfo.Size(m)
}
func (m *UKWOpenPushInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UKWOpenPushInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UKWOpenPushInfo proto.InternalMessageInfo

func (m *UKWOpenPushInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UKWOpenPushInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UKWOpenPushInfo) GetUkwOrderTypeLevel() uint32 {
	if m != nil {
		return m.UkwOrderTypeLevel
	}
	return 0
}

type UKWExposureChannelMsg struct {
	UkwNickname          string   `protobuf:"bytes,1,opt,name=ukw_nickname,json=ukwNickname,proto3" json:"ukw_nickname,omitempty"`
	UkwAccount           string   `protobuf:"bytes,2,opt,name=ukw_account,json=ukwAccount,proto3" json:"ukw_account,omitempty"`
	HeadFrame            string   `protobuf:"bytes,3,opt,name=head_frame,json=headFrame,proto3" json:"head_frame,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,5,opt,name=account,proto3" json:"account,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	Sex                  uint32   `protobuf:"varint,7,opt,name=sex,proto3" json:"sex,omitempty"`
	OldFakeUid           uint32   `protobuf:"varint,8,opt,name=old_fake_uid,json=oldFakeUid,proto3" json:"old_fake_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UKWExposureChannelMsg) Reset()         { *m = UKWExposureChannelMsg{} }
func (m *UKWExposureChannelMsg) String() string { return proto.CompactTextString(m) }
func (*UKWExposureChannelMsg) ProtoMessage()    {}
func (*UKWExposureChannelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{21}
}
func (m *UKWExposureChannelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UKWExposureChannelMsg.Unmarshal(m, b)
}
func (m *UKWExposureChannelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UKWExposureChannelMsg.Marshal(b, m, deterministic)
}
func (dst *UKWExposureChannelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UKWExposureChannelMsg.Merge(dst, src)
}
func (m *UKWExposureChannelMsg) XXX_Size() int {
	return xxx_messageInfo_UKWExposureChannelMsg.Size(m)
}
func (m *UKWExposureChannelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_UKWExposureChannelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_UKWExposureChannelMsg proto.InternalMessageInfo

func (m *UKWExposureChannelMsg) GetUkwNickname() string {
	if m != nil {
		return m.UkwNickname
	}
	return ""
}

func (m *UKWExposureChannelMsg) GetUkwAccount() string {
	if m != nil {
		return m.UkwAccount
	}
	return ""
}

func (m *UKWExposureChannelMsg) GetHeadFrame() string {
	if m != nil {
		return m.HeadFrame
	}
	return ""
}

func (m *UKWExposureChannelMsg) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UKWExposureChannelMsg) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UKWExposureChannelMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UKWExposureChannelMsg) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UKWExposureChannelMsg) GetOldFakeUid() uint32 {
	if m != nil {
		return m.OldFakeUid
	}
	return 0
}

type ShowUpMsg struct {
	SendUid              uint32   `protobuf:"varint,1,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendNickname         string   `protobuf:"bytes,2,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	SendAccount          string   `protobuf:"bytes,3,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	MsgText              string   `protobuf:"bytes,4,opt,name=msg_text,json=msgText,proto3" json:"msg_text,omitempty"`
	Weight               int64    `protobuf:"varint,5,opt,name=weight,proto3" json:"weight,omitempty"`
	Count                uint32   `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowUpMsg) Reset()         { *m = ShowUpMsg{} }
func (m *ShowUpMsg) String() string { return proto.CompactTextString(m) }
func (*ShowUpMsg) ProtoMessage()    {}
func (*ShowUpMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{22}
}
func (m *ShowUpMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowUpMsg.Unmarshal(m, b)
}
func (m *ShowUpMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowUpMsg.Marshal(b, m, deterministic)
}
func (dst *ShowUpMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowUpMsg.Merge(dst, src)
}
func (m *ShowUpMsg) XXX_Size() int {
	return xxx_messageInfo_ShowUpMsg.Size(m)
}
func (m *ShowUpMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowUpMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ShowUpMsg proto.InternalMessageInfo

func (m *ShowUpMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *ShowUpMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *ShowUpMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *ShowUpMsg) GetMsgText() string {
	if m != nil {
		return m.MsgText
	}
	return ""
}

func (m *ShowUpMsg) GetWeight() int64 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *ShowUpMsg) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 发互动消息
type SendShowUpMsgReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Msg                  *ShowUpMsg   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	ReceiverUid          uint32       `protobuf:"varint,3,opt,name=receiver_uid,json=receiverUid,proto3" json:"receiver_uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendShowUpMsgReq) Reset()         { *m = SendShowUpMsgReq{} }
func (m *SendShowUpMsgReq) String() string { return proto.CompactTextString(m) }
func (*SendShowUpMsgReq) ProtoMessage()    {}
func (*SendShowUpMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{23}
}
func (m *SendShowUpMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendShowUpMsgReq.Unmarshal(m, b)
}
func (m *SendShowUpMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendShowUpMsgReq.Marshal(b, m, deterministic)
}
func (dst *SendShowUpMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendShowUpMsgReq.Merge(dst, src)
}
func (m *SendShowUpMsgReq) XXX_Size() int {
	return xxx_messageInfo_SendShowUpMsgReq.Size(m)
}
func (m *SendShowUpMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendShowUpMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendShowUpMsgReq proto.InternalMessageInfo

func (m *SendShowUpMsgReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendShowUpMsgReq) GetMsg() *ShowUpMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *SendShowUpMsgReq) GetReceiverUid() uint32 {
	if m != nil {
		return m.ReceiverUid
	}
	return 0
}

func (m *SendShowUpMsgReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SendShowUpMsgResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendShowUpMsgResp) Reset()         { *m = SendShowUpMsgResp{} }
func (m *SendShowUpMsgResp) String() string { return proto.CompactTextString(m) }
func (*SendShowUpMsgResp) ProtoMessage()    {}
func (*SendShowUpMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{24}
}
func (m *SendShowUpMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendShowUpMsgResp.Unmarshal(m, b)
}
func (m *SendShowUpMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendShowUpMsgResp.Marshal(b, m, deterministic)
}
func (dst *SendShowUpMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendShowUpMsgResp.Merge(dst, src)
}
func (m *SendShowUpMsgResp) XXX_Size() int {
	return xxx_messageInfo_SendShowUpMsgResp.Size(m)
}
func (m *SendShowUpMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendShowUpMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendShowUpMsgResp proto.InternalMessageInfo

func (m *SendShowUpMsgResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 取收到的消息列表
type GetShowUpMsgListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetShowUpMsgListReq) Reset()         { *m = GetShowUpMsgListReq{} }
func (m *GetShowUpMsgListReq) String() string { return proto.CompactTextString(m) }
func (*GetShowUpMsgListReq) ProtoMessage()    {}
func (*GetShowUpMsgListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{25}
}
func (m *GetShowUpMsgListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowUpMsgListReq.Unmarshal(m, b)
}
func (m *GetShowUpMsgListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowUpMsgListReq.Marshal(b, m, deterministic)
}
func (dst *GetShowUpMsgListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowUpMsgListReq.Merge(dst, src)
}
func (m *GetShowUpMsgListReq) XXX_Size() int {
	return xxx_messageInfo_GetShowUpMsgListReq.Size(m)
}
func (m *GetShowUpMsgListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowUpMsgListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowUpMsgListReq proto.InternalMessageInfo

func (m *GetShowUpMsgListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetShowUpMsgListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetShowUpMsgListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetShowUpMsgListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MsgList              []*ShowUpMsg  `protobuf:"bytes,2,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	SenderCnt            uint32        `protobuf:"varint,3,opt,name=sender_cnt,json=senderCnt,proto3" json:"sender_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetShowUpMsgListResp) Reset()         { *m = GetShowUpMsgListResp{} }
func (m *GetShowUpMsgListResp) String() string { return proto.CompactTextString(m) }
func (*GetShowUpMsgListResp) ProtoMessage()    {}
func (*GetShowUpMsgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{26}
}
func (m *GetShowUpMsgListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowUpMsgListResp.Unmarshal(m, b)
}
func (m *GetShowUpMsgListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowUpMsgListResp.Marshal(b, m, deterministic)
}
func (dst *GetShowUpMsgListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowUpMsgListResp.Merge(dst, src)
}
func (m *GetShowUpMsgListResp) XXX_Size() int {
	return xxx_messageInfo_GetShowUpMsgListResp.Size(m)
}
func (m *GetShowUpMsgListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowUpMsgListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowUpMsgListResp proto.InternalMessageInfo

func (m *GetShowUpMsgListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetShowUpMsgListResp) GetMsgList() []*ShowUpMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

func (m *GetShowUpMsgListResp) GetSenderCnt() uint32 {
	if m != nil {
		return m.SenderCnt
	}
	return 0
}

// 取随机文案列表
type GetShowUpTextListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetShowUpTextListReq) Reset()         { *m = GetShowUpTextListReq{} }
func (m *GetShowUpTextListReq) String() string { return proto.CompactTextString(m) }
func (*GetShowUpTextListReq) ProtoMessage()    {}
func (*GetShowUpTextListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{27}
}
func (m *GetShowUpTextListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowUpTextListReq.Unmarshal(m, b)
}
func (m *GetShowUpTextListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowUpTextListReq.Marshal(b, m, deterministic)
}
func (dst *GetShowUpTextListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowUpTextListReq.Merge(dst, src)
}
func (m *GetShowUpTextListReq) XXX_Size() int {
	return xxx_messageInfo_GetShowUpTextListReq.Size(m)
}
func (m *GetShowUpTextListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowUpTextListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowUpTextListReq proto.InternalMessageInfo

func (m *GetShowUpTextListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetShowUpTextListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TextList             []string      `protobuf:"bytes,2,rep,name=text_list,json=textList,proto3" json:"text_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetShowUpTextListResp) Reset()         { *m = GetShowUpTextListResp{} }
func (m *GetShowUpTextListResp) String() string { return proto.CompactTextString(m) }
func (*GetShowUpTextListResp) ProtoMessage()    {}
func (*GetShowUpTextListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{28}
}
func (m *GetShowUpTextListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShowUpTextListResp.Unmarshal(m, b)
}
func (m *GetShowUpTextListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShowUpTextListResp.Marshal(b, m, deterministic)
}
func (dst *GetShowUpTextListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShowUpTextListResp.Merge(dst, src)
}
func (m *GetShowUpTextListResp) XXX_Size() int {
	return xxx_messageInfo_GetShowUpTextListResp.Size(m)
}
func (m *GetShowUpTextListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShowUpTextListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetShowUpTextListResp proto.InternalMessageInfo

func (m *GetShowUpTextListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetShowUpTextListResp) GetTextList() []string {
	if m != nil {
		return m.TextList
	}
	return nil
}

// 神秘人现身
type ExposureUKWReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ExposureUKWReq) Reset()         { *m = ExposureUKWReq{} }
func (m *ExposureUKWReq) String() string { return proto.CompactTextString(m) }
func (*ExposureUKWReq) ProtoMessage()    {}
func (*ExposureUKWReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{29}
}
func (m *ExposureUKWReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExposureUKWReq.Unmarshal(m, b)
}
func (m *ExposureUKWReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExposureUKWReq.Marshal(b, m, deterministic)
}
func (dst *ExposureUKWReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExposureUKWReq.Merge(dst, src)
}
func (m *ExposureUKWReq) XXX_Size() int {
	return xxx_messageInfo_ExposureUKWReq.Size(m)
}
func (m *ExposureUKWReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExposureUKWReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExposureUKWReq proto.InternalMessageInfo

func (m *ExposureUKWReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ExposureUKWResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ExposureUKWResp) Reset()         { *m = ExposureUKWResp{} }
func (m *ExposureUKWResp) String() string { return proto.CompactTextString(m) }
func (*ExposureUKWResp) ProtoMessage()    {}
func (*ExposureUKWResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_logic__d73ead72765a6a7d, []int{30}
}
func (m *ExposureUKWResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExposureUKWResp.Unmarshal(m, b)
}
func (m *ExposureUKWResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExposureUKWResp.Marshal(b, m, deterministic)
}
func (dst *ExposureUKWResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExposureUKWResp.Merge(dst, src)
}
func (m *ExposureUKWResp) XXX_Size() int {
	return xxx_messageInfo_ExposureUKWResp.Size(m)
}
func (m *ExposureUKWResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExposureUKWResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExposureUKWResp proto.InternalMessageInfo

func (m *ExposureUKWResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*UKWPermissionInfo)(nil), "ga.youknowwhologic.UKWPermissionInfo")
	proto.RegisterType((*UKWPersonInfo)(nil), "ga.youknowwhologic.UKWPersonInfo")
	proto.RegisterType((*UKWMedal)(nil), "ga.youknowwhologic.UKWMedal")
	proto.RegisterType((*UKWInfo)(nil), "ga.youknowwhologic.UKWInfo")
	proto.RegisterType((*OpenUKWReq)(nil), "ga.youknowwhologic.OpenUKWReq")
	proto.RegisterType((*OpenUKWResp)(nil), "ga.youknowwhologic.OpenUKWResp")
	proto.RegisterType((*ChangeUKWSwitchReq)(nil), "ga.youknowwhologic.ChangeUKWSwitchReq")
	proto.RegisterType((*ChangeUKWSwitchResp)(nil), "ga.youknowwhologic.ChangeUKWSwitchResp")
	proto.RegisterType((*GetUKWInfoReq)(nil), "ga.youknowwhologic.GetUKWInfoReq")
	proto.RegisterType((*GetUKWInfoResp)(nil), "ga.youknowwhologic.GetUKWInfoResp")
	proto.RegisterType((*GetUKWUserProfileReq)(nil), "ga.youknowwhologic.GetUKWUserProfileReq")
	proto.RegisterType((*GetUKWUserProfileResp)(nil), "ga.youknowwhologic.GetUKWUserProfileResp")
	proto.RegisterType((*BatchGetUKWInfosReq)(nil), "ga.youknowwhologic.BatchGetUKWInfosReq")
	proto.RegisterType((*BatchGetUKWInfosResp)(nil), "ga.youknowwhologic.BatchGetUKWInfosResp")
	proto.RegisterType((*ChangeRankSwitchReq)(nil), "ga.youknowwhologic.ChangeRankSwitchReq")
	proto.RegisterType((*ChangeRankSwitchResp)(nil), "ga.youknowwhologic.ChangeRankSwitchResp")
	proto.RegisterType((*UserChangeUKWEnterNoticeReq)(nil), "ga.youknowwhologic.UserChangeUKWEnterNoticeReq")
	proto.RegisterType((*UserChangeUKWEnterNoticeResp)(nil), "ga.youknowwhologic.UserChangeUKWEnterNoticeResp")
	proto.RegisterType((*UKWChangeChannelMsg)(nil), "ga.youknowwhologic.UKWChangeChannelMsg")
	proto.RegisterType((*UKWPermissionChangeMsg)(nil), "ga.youknowwhologic.UKWPermissionChangeMsg")
	proto.RegisterType((*UKWOpenPushInfo)(nil), "ga.youknowwhologic.UKWOpenPushInfo")
	proto.RegisterType((*UKWExposureChannelMsg)(nil), "ga.youknowwhologic.UKWExposureChannelMsg")
	proto.RegisterType((*ShowUpMsg)(nil), "ga.youknowwhologic.ShowUpMsg")
	proto.RegisterType((*SendShowUpMsgReq)(nil), "ga.youknowwhologic.SendShowUpMsgReq")
	proto.RegisterType((*SendShowUpMsgResp)(nil), "ga.youknowwhologic.SendShowUpMsgResp")
	proto.RegisterType((*GetShowUpMsgListReq)(nil), "ga.youknowwhologic.GetShowUpMsgListReq")
	proto.RegisterType((*GetShowUpMsgListResp)(nil), "ga.youknowwhologic.GetShowUpMsgListResp")
	proto.RegisterType((*GetShowUpTextListReq)(nil), "ga.youknowwhologic.GetShowUpTextListReq")
	proto.RegisterType((*GetShowUpTextListResp)(nil), "ga.youknowwhologic.GetShowUpTextListResp")
	proto.RegisterType((*ExposureUKWReq)(nil), "ga.youknowwhologic.ExposureUKWReq")
	proto.RegisterType((*ExposureUKWResp)(nil), "ga.youknowwhologic.ExposureUKWResp")
	proto.RegisterEnum("ga.youknowwhologic.UKWOpenType", UKWOpenType_name, UKWOpenType_value)
	proto.RegisterEnum("ga.youknowwhologic.UKWSwitchType", UKWSwitchType_name, UKWSwitchType_value)
	proto.RegisterEnum("ga.youknowwhologic.UKWOpenStatus", UKWOpenStatus_name, UKWOpenStatus_value)
	proto.RegisterEnum("ga.youknowwhologic.RankSwitchType", RankSwitchType_name, RankSwitchType_value)
	proto.RegisterEnum("ga.youknowwhologic.MedalType", MedalType_name, MedalType_value)
}

func init() {
	proto.RegisterFile("you-know-who-logic_.proto", fileDescriptor_you_know_who_logic__d73ead72765a6a7d)
}

var fileDescriptor_you_know_who_logic__d73ead72765a6a7d = []byte{
	// 1675 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0x4f, 0x6f, 0xdb, 0xca,
	0x11, 0x0f, 0x45, 0x59, 0x12, 0x87, 0x92, 0x2d, 0xd3, 0x4e, 0xaa, 0xc4, 0x09, 0x6c, 0xb3, 0x68,
	0xe1, 0x06, 0x8d, 0xdd, 0x3a, 0x6d, 0xfe, 0x14, 0x45, 0x50, 0xdb, 0x95, 0x13, 0xc3, 0x89, 0xec,
	0xd2, 0x16, 0x84, 0xe6, 0xb2, 0xa0, 0xc5, 0xb5, 0xc4, 0x4a, 0x22, 0x19, 0x2e, 0x19, 0xd9, 0x39,
	0xe6, 0xd0, 0x73, 0x4f, 0x45, 0x51, 0x14, 0x28, 0xd0, 0x7b, 0xd1, 0x63, 0x3f, 0x43, 0x0f, 0xfd,
	0x34, 0x05, 0x8a, 0x1e, 0x8b, 0xd9, 0x5d, 0xfd, 0xa3, 0xac, 0x38, 0x74, 0xf2, 0x80, 0x77, 0x78,
	0x37, 0xce, 0xcc, 0xce, 0xec, 0xec, 0x6f, 0x66, 0x76, 0x67, 0x08, 0x77, 0x2f, 0xfd, 0xf8, 0x51,
	0xc7, 0xf3, 0xfb, 0x8f, 0xfa, 0x6d, 0xff, 0x51, 0xd7, 0x6f, 0xb9, 0x4d, 0xb2, 0x19, 0x84, 0x7e,
	0xe4, 0x1b, 0x46, 0xcb, 0xde, 0xbc, 0xf4, 0x63, 0x14, 0xf6, 0xdb, 0x3e, 0x17, 0xdd, 0x2b, 0xb5,
	0x6c, 0x72, 0x66, 0x33, 0x2a, 0x96, 0x98, 0x7f, 0xcd, 0xc2, 0x62, 0xfd, 0xb0, 0x71, 0x4c, 0xc3,
	0x9e, 0xcb, 0x98, 0xeb, 0x7b, 0x07, 0xde, 0xb9, 0x6f, 0x94, 0x41, 0x8d, 0x5d, 0xa7, 0xa2, 0xac,
	0x29, 0x1b, 0x25, 0x0b, 0x3f, 0x8d, 0x7b, 0x50, 0xf0, 0xdc, 0x66, 0xc7, 0xb3, 0x7b, 0xb4, 0x92,
	0x59, 0x53, 0x36, 0x34, 0x6b, 0x48, 0x1b, 0x4f, 0x21, 0xc7, 0x22, 0x3b, 0x8a, 0x59, 0x45, 0x5d,
	0x53, 0x36, 0xe6, 0xb7, 0x57, 0x37, 0xa7, 0xf7, 0xdd, 0xac, 0x1f, 0x36, 0x8e, 0x02, 0xea, 0x9d,
	0x5e, 0x06, 0xd4, 0x92, 0xcb, 0x8d, 0xe7, 0x90, 0x63, 0x7d, 0x37, 0x6a, 0xb6, 0x2b, 0x59, 0xae,
	0xb8, 0x3e, 0x43, 0xf1, 0x84, 0x2f, 0x92, 0xaa, 0xfc, 0xdb, 0x58, 0x05, 0x9d, 0x5e, 0x04, 0x6e,
	0x48, 0x49, 0xe4, 0xf6, 0x68, 0x65, 0x8e, 0x7b, 0x0a, 0x82, 0x75, 0xea, 0xf6, 0x28, 0x2e, 0x60,
	0x34, 0x7c, 0x4f, 0x43, 0xb1, 0x20, 0x27, 0x16, 0x08, 0x16, 0x5f, 0xb0, 0x0c, 0x73, 0x5d, 0xfa,
	0x9e, 0x76, 0x2b, 0x79, 0x2e, 0x12, 0x84, 0xb1, 0x07, 0x7a, 0x68, 0x7b, 0x1d, 0x22, 0xfd, 0x2a,
	0x70, 0xbf, 0xcc, 0xab, 0xfc, 0xb2, 0x6c, 0xaf, 0x33, 0xe6, 0x18, 0x84, 0x43, 0xda, 0xb8, 0x0b,
	0x85, 0x73, 0xbb, 0x43, 0x09, 0x62, 0xa8, 0x71, 0xeb, 0x79, 0xa4, 0xeb, 0xae, 0x63, 0xfc, 0x02,
	0xf2, 0x2e, 0x23, 0x7e, 0x40, 0xbd, 0x0a, 0x7c, 0xf2, 0xcc, 0x08, 0xd6, 0x09, 0x87, 0xc9, 0xca,
	0xb9, 0x0c, 0x29, 0xe3, 0x37, 0xb0, 0x44, 0xbd, 0x88, 0x86, 0xc4, 0xf3, 0x23, 0xb7, 0x49, 0x07,
	0x3e, 0xea, 0x9f, 0x8b, 0xdd, 0x22, 0xd7, 0xae, 0x71, 0x65, 0xe9, 0xe9, 0x3a, 0x14, 0xb9, 0xa7,
	0x76, 0xb3, 0xe9, 0xc7, 0x5e, 0x54, 0x29, 0xf2, 0xd0, 0xea, 0xc8, 0xdb, 0x11, 0x2c, 0xf3, 0x6f,
	0x0a, 0x94, 0x44, 0x86, 0xb0, 0x1b, 0x65, 0x47, 0x05, 0xf2, 0x03, 0xeb, 0x2a, 0x17, 0x0d, 0x48,
	0x8c, 0x40, 0x8f, 0x3a, 0x76, 0x97, 0x47, 0x5f, 0xb3, 0x04, 0x31, 0x8a, 0xcb, 0xdc, 0x78, 0x5c,
	0x1e, 0x00, 0xb4, 0xa9, 0xed, 0x90, 0xf3, 0xd0, 0x96, 0xd1, 0xd4, 0x2c, 0x0d, 0x39, 0xfb, 0xc8,
	0x30, 0xff, 0xad, 0x40, 0xa1, 0x7e, 0xd8, 0x78, 0xc3, 0x2d, 0xfc, 0x14, 0xb2, 0xd1, 0x65, 0x40,
	0xb9, 0x83, 0xf3, 0xdb, 0x0f, 0xae, 0x02, 0x86, 0x2f, 0xe4, 0xa0, 0xf0, 0xa5, 0xc6, 0x0a, 0x68,
	0x7c, 0x77, 0x12, 0x87, 0xdd, 0xc1, 0x09, 0x38, 0xa3, 0x1e, 0x76, 0x31, 0x9c, 0xbf, 0x8b, 0x7b,
	0x01, 0x97, 0xc9, 0x23, 0x20, 0x8d, 0xa2, 0x55, 0xd0, 0x3d, 0x4a, 0x1d, 0x19, 0x11, 0x7e, 0x90,
	0x82, 0x05, 0xc8, 0x12, 0x30, 0x1b, 0x77, 0x20, 0x27, 0x65, 0x73, 0x5c, 0x53, 0x52, 0x78, 0x1e,
	0x97, 0x11, 0x91, 0xaf, 0x0e, 0x3f, 0x4f, 0xc1, 0xd2, 0x5c, 0x56, 0x15, 0x0c, 0xf3, 0x7f, 0x0a,
	0xe4, 0xeb, 0x87, 0x8d, 0x19, 0x70, 0xd7, 0x61, 0x29, 0xee, 0xf4, 0x49, 0x30, 0x2c, 0x5a, 0xe2,
	0x7a, 0xe7, 0x3e, 0xf7, 0x5b, 0xdf, 0xfe, 0xc1, 0x8c, 0x44, 0x98, 0x2c, 0x71, 0x6b, 0x31, 0xee,
	0xf4, 0x13, 0x55, 0x7f, 0x00, 0x0b, 0xd2, 0x2c, 0x1b, 0x98, 0x54, 0xb9, 0xc9, 0xf5, 0xd9, 0x26,
	0x65, 0x4e, 0x58, 0x25, 0x61, 0x6e, 0x90, 0x22, 0xcf, 0x41, 0x43, 0x53, 0xa3, 0xf0, 0xea, 0xdb,
	0xf7, 0x67, 0x18, 0xe1, 0xa1, 0xb0, 0x0a, 0x71, 0xa7, 0xcf, 0xbf, 0xcc, 0x9f, 0x01, 0x60, 0xb6,
	0xd7, 0x0f, 0x1b, 0x16, 0x7d, 0x67, 0xfc, 0x10, 0x0a, 0x78, 0x5b, 0x91, 0x90, 0xbe, 0xe3, 0x08,
	0xe8, 0xdb, 0x3a, 0xda, 0xd9, 0xb5, 0x19, 0xb5, 0xe8, 0x3b, 0x2b, 0x7f, 0x26, 0x3e, 0xcc, 0x0f,
	0xa0, 0x0f, 0xb5, 0x58, 0x60, 0xfc, 0x08, 0x34, 0xa9, 0xc6, 0x02, 0xa9, 0x57, 0x1c, 0xe9, 0xb1,
	0xc0, 0x2a, 0x9c, 0xc9, 0xaf, 0xe4, 0x45, 0x91, 0x99, 0xba, 0x28, 0x12, 0x57, 0x8d, 0x9a, 0xbc,
	0x6a, 0xcc, 0x3f, 0x2a, 0x60, 0xec, 0xb5, 0x6d, 0xaf, 0x45, 0x87, 0xf5, 0x96, 0xc2, 0x75, 0x7e,
	0x0b, 0x8a, 0xeb, 0x33, 0xf3, 0xf9, 0xb7, 0xa0, 0xb8, 0x40, 0x1f, 0x00, 0x34, 0xdb, 0xb6, 0xe7,
	0xd1, 0x2e, 0x71, 0x1d, 0xe9, 0x99, 0x26, 0x39, 0x07, 0x8e, 0xf9, 0x2b, 0x58, 0x9a, 0xf2, 0x2b,
	0x15, 0x38, 0xe6, 0x53, 0x28, 0xbd, 0xa4, 0x91, 0xcc, 0xc4, 0x34, 0xf1, 0x60, 0x30, 0x3f, 0xae,
	0x98, 0x2e, 0x24, 0x4f, 0x00, 0xd3, 0x61, 0x3c, 0xa9, 0x57, 0x66, 0x60, 0xc2, 0xad, 0xe7, 0xe3,
	0x4e, 0x1f, 0x3f, 0xcc, 0x8f, 0x0a, 0x2c, 0x8b, 0x5d, 0xeb, 0x8c, 0x86, 0xc7, 0xa1, 0x7f, 0xee,
	0x76, 0x69, 0x9a, 0x50, 0xc8, 0x52, 0xcb, 0x8c, 0x4a, 0xed, 0x27, 0xb0, 0xec, 0x32, 0x59, 0x12,
	0x76, 0x97, 0xb4, 0xfd, 0x1e, 0x0d, 0xec, 0x96, 0xc8, 0x82, 0x82, 0x65, 0xb8, 0xec, 0x58, 0x8a,
	0x5e, 0x49, 0x09, 0x96, 0xee, 0xed, 0x2b, 0x9c, 0x48, 0x87, 0xc0, 0xb4, 0x23, 0xdb, 0x50, 0x8c,
	0x19, 0x0d, 0x49, 0x20, 0x0c, 0xca, 0xca, 0x5c, 0x40, 0xfd, 0xf1, 0x7d, 0xf4, 0x78, 0x44, 0x18,
	0x8f, 0xa1, 0xc4, 0x75, 0x86, 0x60, 0x66, 0x27, 0x95, 0x06, 0x00, 0x72, 0xa5, 0xba, 0x00, 0x71,
	0xb2, 0x74, 0xe7, 0x52, 0x95, 0xee, 0x1f, 0x14, 0x58, 0xda, 0xb5, 0xa3, 0x66, 0x7b, 0x14, 0x7a,
	0x96, 0x06, 0xfe, 0x15, 0xd0, 0x10, 0x42, 0xc2, 0xdc, 0x0f, 0x83, 0x42, 0x2c, 0x20, 0xe3, 0xc4,
	0xfd, 0x40, 0xf1, 0x16, 0xe6, 0x42, 0x2f, 0xee, 0xc9, 0x4c, 0xcf, 0x23, 0x5d, 0x8b, 0x7b, 0x28,
	0x8a, 0x5d, 0x87, 0x74, 0x5d, 0x16, 0x55, 0xb2, 0x6b, 0x2a, 0x8a, 0x62, 0xd7, 0x79, 0xed, 0xb2,
	0xc8, 0xfc, 0x97, 0x02, 0xcb, 0xd3, 0x2e, 0xa5, 0x0b, 0xc6, 0x33, 0x81, 0x08, 0x22, 0x88, 0x35,
	0xaa, 0x5e, 0x97, 0x8f, 0x05, 0x99, 0x8f, 0x6c, 0xf2, 0x40, 0xea, 0x27, 0x0e, 0x94, 0x9d, 0x3c,
	0xd0, 0x32, 0xcc, 0x89, 0x17, 0x53, 0xbe, 0x81, 0xe2, 0x25, 0xfe, 0xa8, 0x0c, 0xea, 0x79, 0xd4,
	0x7b, 0xa4, 0x81, 0x37, 0xd1, 0xdb, 0x64, 0x6e, 0xd2, 0xdb, 0x98, 0xbf, 0x57, 0x60, 0x79, 0xda,
	0x89, 0x74, 0x80, 0x7e, 0x15, 0x47, 0xfe, 0xa4, 0xc0, 0x0a, 0x26, 0xf1, 0xf0, 0x86, 0xab, 0x8e,
	0xba, 0x9b, 0x34, 0xa8, 0xcc, 0xe8, 0xaa, 0x32, 0x37, 0xef, 0xaa, 0xcc, 0xbf, 0x28, 0x70, 0x7f,
	0xb6, 0x6b, 0xe9, 0xb0, 0xfa, 0x06, 0xdc, 0xfb, 0x87, 0x0a, 0x4b, 0xf5, 0xc3, 0x86, 0xf0, 0x6e,
	0x4f, 0xbc, 0x16, 0x6f, 0x58, 0xeb, 0x8a, 0x46, 0xe3, 0x69, 0xe2, 0x69, 0xba, 0x41, 0x67, 0xaf,
	0xa6, 0xed, 0xec, 0xc7, 0x7b, 0xc9, 0xec, 0xec, 0x5e, 0x72, 0x6e, 0xb2, 0x97, 0xbc, 0xb6, 0xdd,
	0x4f, 0xbc, 0xe2, 0xf9, 0xa9, 0x81, 0xe1, 0x5b, 0xdc, 0xf9, 0x9b, 0xff, 0x51, 0xe1, 0xce, 0x44,
	0x0b, 0x27, 0x62, 0x87, 0x41, 0x1b, 0xdf, 0x51, 0x99, 0xdc, 0xf1, 0xbb, 0xe8, 0xa5, 0x8c, 0xde,
	0x58, 0x88, 0xb4, 0xaf, 0x34, 0x9c, 0xc1, 0x17, 0xd4, 0xe9, 0x05, 0x2c, 0xc8, 0xbd, 0x8e, 0x63,
	0xd6, 0xe6, 0x8f, 0xf3, 0x38, 0xbc, 0xca, 0x6c, 0x78, 0x33, 0x93, 0xf0, 0x6e, 0xc1, 0x32, 0x3e,
	0x60, 0x7e, 0xe8, 0x20, 0xc2, 0x97, 0x01, 0x25, 0x62, 0xc2, 0x12, 0x2f, 0x12, 0x4e, 0x02, 0x47,
	0x28, 0x42, 0x3f, 0x5e, 0xa3, 0xc0, 0xfc, 0xaf, 0x02, 0xb7, 0xf1, 0xda, 0xba, 0x08, 0x7c, 0x16,
	0x87, 0xe3, 0x77, 0xc4, 0x3a, 0x14, 0xd1, 0x54, 0xc2, 0x09, 0x3d, 0xee, 0xf4, 0x6b, 0x03, 0x3f,
	0x56, 0x01, 0x49, 0x32, 0xe9, 0x0b, 0xc4, 0x9d, 0xbe, 0x9c, 0x28, 0x13, 0xb3, 0x9c, 0x9a, 0x98,
	0xe5, 0x6e, 0x98, 0x42, 0xf2, 0xf2, 0xca, 0x8d, 0x2e, 0xaf, 0x32, 0xa8, 0x8c, 0x5e, 0xc8, 0x5c,
	0xc1, 0x4f, 0x63, 0x0d, 0x8a, 0x7e, 0xd7, 0x21, 0xc3, 0x7a, 0x29, 0x88, 0x34, 0xf2, 0xbb, 0xce,
	0xbe, 0x28, 0x19, 0xf3, 0x9f, 0x0a, 0x68, 0x27, 0x6d, 0xbf, 0x5f, 0x0f, 0x64, 0x6d, 0x31, 0xea,
	0x39, 0xe3, 0xb5, 0x85, 0x34, 0xd6, 0xd6, 0xf7, 0xa1, 0xc4, 0x45, 0x89, 0xb1, 0xb7, 0x88, 0xcc,
	0x21, 0x12, 0xeb, 0xc0, 0x69, 0x32, 0x39, 0xff, 0xea, 0xc8, 0x1b, 0x60, 0x71, 0x17, 0x0a, 0x3d,
	0xd6, 0x22, 0x11, 0xbd, 0x88, 0xe4, 0x61, 0xf3, 0x3d, 0xd6, 0x3a, 0xa5, 0x17, 0x11, 0x8e, 0x8e,
	0x7d, 0xea, 0xb6, 0xda, 0xe2, 0xa8, 0xaa, 0x25, 0xa9, 0x51, 0x73, 0x90, 0x1b, 0x6f, 0x0e, 0xfe,
	0xae, 0x40, 0xf9, 0x84, 0x7a, 0xce, 0xd0, 0xfb, 0x34, 0x6f, 0xe0, 0x16, 0xa8, 0x3d, 0xd6, 0x92,
	0xbd, 0xf6, 0x95, 0x03, 0xf3, 0xc8, 0x2c, 0xae, 0xc4, 0x93, 0x85, 0xb4, 0x49, 0x5d, 0x2c, 0xd9,
	0x78, 0x38, 0x7a, 0xe8, 0x03, 0x1e, 0x22, 0x34, 0x39, 0x9b, 0x64, 0x93, 0xb3, 0xc9, 0x0b, 0x58,
	0x4c, 0xb8, 0x9b, 0x6e, 0x32, 0xf1, 0x60, 0xe9, 0x25, 0x8d, 0x86, 0xea, 0xd8, 0xec, 0x7d, 0x59,
	0xa7, 0x7f, 0xcd, 0x2c, 0xf5, 0x67, 0x31, 0x5b, 0x24, 0x36, 0x4c, 0xdb, 0x48, 0xf2, 0x60, 0xf3,
	0x3e, 0x55, 0xf4, 0x91, 0xd7, 0x60, 0x8d, 0xb9, 0x80, 0x1b, 0xa1, 0x73, 0x98, 0x35, 0x34, 0x24,
	0x4d, 0x99, 0x47, 0x25, 0x4b, 0x13, 0x9c, 0x3d, 0x2f, 0x32, 0x5f, 0x8c, 0xf9, 0x86, 0xb9, 0x93,
	0x12, 0x0d, 0x93, 0xf0, 0x91, 0x25, 0xa9, 0x9f, 0xee, 0x70, 0x2b, 0xa0, 0x61, 0x16, 0x8f, 0x4e,
	0xa7, 0x59, 0x85, 0x48, 0xda, 0x32, 0x9f, 0xc1, 0xfc, 0xe0, 0x32, 0x49, 0x39, 0xd8, 0xff, 0x12,
	0x16, 0x26, 0x34, 0x53, 0x39, 0xf5, 0xf0, 0x00, 0xf4, 0xb1, 0x07, 0xce, 0x58, 0xe0, 0x24, 0xa9,
	0x1d, 0x91, 0xa3, 0xe3, 0x6a, 0xad, 0x7c, 0xcb, 0x28, 0xf2, 0xdf, 0x46, 0x82, 0x52, 0x8c, 0x79,
	0x00, 0xa4, 0xf6, 0xad, 0x6a, 0xf5, 0x6d, 0xb5, 0x9c, 0x31, 0x74, 0xfe, 0x13, 0x86, 0xec, 0xee,
	0xd4, 0xca, 0xea, 0xc3, 0x27, 0xfc, 0x37, 0xd8, 0xe8, 0xc6, 0x36, 0x0c, 0x98, 0x47, 0xe9, 0x49,
	0xe3, 0xe0, 0x74, 0xef, 0x15, 0x39, 0xda, 0xdf, 0x2f, 0xdf, 0x32, 0x16, 0xf9, 0xa2, 0x21, 0xaf,
	0x56, 0x56, 0x1e, 0x6e, 0x70, 0xd6, 0xe8, 0xc5, 0x18, 0x58, 0x15, 0x0a, 0x00, 0x39, 0x4e, 0xe0,
	0xca, 0x1d, 0x98, 0x9f, 0x7c, 0x9c, 0x8c, 0xef, 0xf1, 0x46, 0x8d, 0x58, 0x3b, 0xb5, 0xc3, 0xc9,
	0x7d, 0xee, 0x80, 0x31, 0x25, 0x40, 0x13, 0xcf, 0x41, 0x1b, 0xfe, 0xda, 0x42, 0xed, 0x37, 0xd5,
	0x5f, 0xef, 0xbc, 0x26, 0xa7, 0xbf, 0x3d, 0xae, 0x92, 0xda, 0xd1, 0x29, 0x69, 0x54, 0x77, 0xac,
	0xf2, 0x2d, 0x63, 0x09, 0x16, 0xc6, 0x04, 0x9c, 0xa9, 0xec, 0xbe, 0x82, 0x4a, 0xd3, 0xef, 0x6d,
	0x5e, 0xba, 0x97, 0x7e, 0x8c, 0x68, 0xf6, 0x7c, 0x87, 0x76, 0xc5, 0x5f, 0xe2, 0xb7, 0x3f, 0x6e,
	0xf9, 0x5d, 0xdb, 0x6b, 0x6d, 0xfe, 0x7c, 0x3b, 0x8a, 0x36, 0x9b, 0x7e, 0x6f, 0x8b, 0xb3, 0x9b,
	0x7e, 0x77, 0xcb, 0x0e, 0x82, 0xad, 0x44, 0xfe, 0x9e, 0xe5, 0xb8, 0xf4, 0xf1, 0xff, 0x03, 0x00,
	0x00, 0xff, 0xff, 0x47, 0xba, 0x89, 0x4d, 0x9a, 0x16, 0x00, 0x00,
}
