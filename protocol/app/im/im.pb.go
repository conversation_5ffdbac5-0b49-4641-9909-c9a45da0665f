// Code generated by protoc-gen-gogo.
// source: im/im.proto
// DO NOT EDIT!

/*
	Package im is a generated protocol buffer package.

	It is generated from these files:
		im/im.proto

	It has these top-level messages:
		RichTextWords
		RichTextImage
		RichTextLink
		RichTextElement
		RichTextMsg
		PersonalRichTextElement
		PersonalRichTextMsg
		LinkJumpURL
		SessionCreateMsg
		SessionCloseMsg
		CueMsg
		TextMsgExt
		VoiceMsgExt
		SendMsgReq
		SendMsgResp
		UploadAttachmentReq
		UploadAttachmentResp
		DownloadAttachmentReq
		DownloadAttachmentResp
		DeleteMessageReq
		DeleteMessageResp
		MarkMsgReadReq
		MarkMsgReadResp
		UpdateMsgSettingReq
		UpdateMsgSettingResp
		QueryMsgSettingReq
		MsgReceiveSettingItem
		QueryMsgSettingResp
		CheckSendAtEveryoneGroupMsgReq
		CheckSendAtEveryoneGroupMsgResp
		CancelMsgReq
		CancelMsgResp
		MessageReadByPeerMessage
		GetMessagePeerReadStatusReq
		MessagePeerReadStatus
		GetMessagePeerReadStatusResp
		DelMsg
		BatchDelMsgReq
		BatchDelMsgResp
		PopGameJumpMsg
		EmojiMsg
		InteractiveUnreadMsg
		RichTextPresent
		RichTextWithPresentMsg
		MasterApprenticeInviteMsg
		MasterApprenticeEstablishMsg
		MasterApprenticeIntroductionMsg
		ChatCardMsg
		ChannelOpenGameInviteMsg
		FellowInviteMsg
		LevelAwardItem
		FellowNextLevelAwardMsg
		FellowUpgradeMsg
		GainRareUser
		GainRareMsg
		SystemNotifyMsg
		ShareGameMsg
		MiJingUserTag
		UserRecallComeback
		GetImGuideTriggerInfoReq
		GetImGuideTriggerInfoResp
		TriggerItem
		IMCommonCardMsg
		IMCommonXmlMsg
*/
package im

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import ga "golang.52tt.com/protocol/app"
import ga_userpresent "golang.52tt.com/protocol/app/userpresent"

import math5 "math"
import github_com_gogo_protobuf_proto4 "github.com/gogo/protobuf/proto"

import io2 "io"
import math6 "math"
import fmt3 "fmt"
import github_com_gogo_protobuf_proto5 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// IM 消息
type IM_MSG_TYPE int32

const (
	// 1文本 2图片 3语音 4视频 5可扩展的信息(名片、网页、功能跳转链接等)
	IM_MSG_TYPE_TEXT_MSG     IM_MSG_TYPE = 1
	IM_MSG_TYPE_IMG_MSG      IM_MSG_TYPE = 2
	IM_MSG_TYPE_VOICE_MSG    IM_MSG_TYPE = 3
	IM_MSG_TYPE_VIDEO_MSG    IM_MSG_TYPE = 4
	IM_MSG_TYPE_EXTENDED_MSG IM_MSG_TYPE = 5
	// GUILD_APPLY = 6;		// 会长收到入会申请
	IM_MSG_TYPE_SYSTEM_NOTIFY               IM_MSG_TYPE = 7
	IM_MSG_TYPE_GUILD_QUIT                  IM_MSG_TYPE = 8
	IM_MSG_TYPE_EXPRESSION_MSG              IM_MSG_TYPE = 9
	IM_MSG_TYPE_GUILD_BC                    IM_MSG_TYPE = 10
	IM_MSG_TYPE_GUILD_JOIN                  IM_MSG_TYPE = 11
	IM_MSG_TYPE_GUILD_ADMIN                 IM_MSG_TYPE = 12
	IM_MSG_TYPE_GUILD_GROUP_JOIN            IM_MSG_TYPE = 13
	IM_MSG_TYPE_GUILD_GROUP_OWNER           IM_MSG_TYPE = 14
	IM_MSG_TYPE_GUILD_GROUP_ADMIN           IM_MSG_TYPE = 15
	IM_MSG_TYPE_GUILD_ASST_NOMINATE         IM_MSG_TYPE = 16
	IM_MSG_TYPE_GUILD_ASST_APPLY_JOIN_GUILD IM_MSG_TYPE = 17
	IM_MSG_TYPE_GUILD_ASST_APPLY_JOIN_GROUP IM_MSG_TYPE = 18
	IM_MSG_TYPE_GUILD_ASST_GIFT_PKG_RESULT  IM_MSG_TYPE = 19
	IM_MSG_TYPE_GUILD_ASST_QUIT_GUILD       IM_MSG_TYPE = 20
	IM_MSG_TYPE_NEW_EXTENDED                IM_MSG_TYPE = 21
	IM_MSG_TYPE_OFFICAIL_MESSAGE_SINGLE     IM_MSG_TYPE = 22
	IM_MSG_TYPE_OFFICAIL_MESSAGE_BUNCH      IM_MSG_TYPE = 23
	// seesion 临时群实时语音版本 该功能已经下线
	IM_MSG_TYPE_CALL_IN            IM_MSG_TYPE = 24
	IM_MSG_TYPE_SESSION_CREATE_MSG IM_MSG_TYPE = 25
	IM_MSG_TYPE_SESSION_CLOSE_MSG  IM_MSG_TYPE = 26
	IM_MSG_TYPE_ACCEPT_CALL_IN     IM_MSG_TYPE = 27
	IM_MSG_TYPE_CALL_IN_END        IM_MSG_TYPE = 28
	// v1.8.0 ----群组 ---- add by hjinw
	IM_MSG_TYPE_GROUP_ASST_APPLY_JOIN_GROUP                  IM_MSG_TYPE = 29
	IM_MSG_TYPE_GROUP_ASST_QUIT_GROUP                        IM_MSG_TYPE = 30
	IM_MSG_TYPE_TGROUP_JOIN_GROUP                            IM_MSG_TYPE = 31
	IM_MSG_TYPE_AIR_TICKET                                   IM_MSG_TYPE = 32
	IM_MSG_TYPE_GUILD_ASST_NOTIFY                            IM_MSG_TYPE = 33
	IM_MSG_TYPE_AT_SOMEONE_MSG                               IM_MSG_TYPE = 34
	IM_MSG_TYPE_GUILD_MEMBER_TITLE                           IM_MSG_TYPE = 35
	IM_MSG_TYPE_GUILD_ASST_COMMON_URL_NOTIFY                 IM_MSG_TYPE = 36
	IM_MSG_TYPE_TT_COMMON_TEXT_NOTIFY                        IM_MSG_TYPE = 37
	IM_MSG_TYPE_AT_EVERYONE_MSG                              IM_MSG_TYPE = 38
	IM_MSG_TYPE_CUSTOM_EMOTICON_MSG                          IM_MSG_TYPE = 39
	IM_MSG_TYPE_GUILD_CIRCLE_ANNOUNCEMENT                    IM_MSG_TYPE = 40
	IM_MSG_TYPE_CANCEL_MSG                                   IM_MSG_TYPE = 41
	IM_MSG_TYPE_GAME_PREORDER_TEXT_NOTIFY                    IM_MSG_TYPE = 42
	IM_MSG_TYPE_TGROUP_INVITE_MSG                            IM_MSG_TYPE = 43
	IM_MSG_TYPE_FIND_FRIENDS_MATED_UP_MSG                    IM_MSG_TYPE = 44
	IM_MSG_TYPE_FIND_FRIENDS_LIKED_NOTIFY                    IM_MSG_TYPE = 45
	IM_MSG_TYPE_GAME_CENTER_POP_GAME_NOTIFY                  IM_MSG_TYPE = 46
	IM_MSG_TYPE_CHANNEL_DATING_GAME_LIKE_USER_NOTIFY         IM_MSG_TYPE = 47
	IM_MSG_TYPE_FIND_FRIENDS_ICEBREAK_MATCH                  IM_MSG_TYPE = 48
	IM_MSG_TYPE_SENSITIVE_NOTIFY_MSG                         IM_MSG_TYPE = 49
	IM_MSG_TYPE_INTERACTIVE_UNREAD_NOTIFY                    IM_MSG_TYPE = 50
	IM_MSG_TYPE_CHANNEL_PK_RESULT_NOTIFY                     IM_MSG_TYPE = 51
	IM_MSG_TYPE_KNOCK_INROOM_NOTIFY                          IM_MSG_TYPE = 52
	IM_MSG_TYPE_CIVILIZATION_CONVENTION                      IM_MSG_TYPE = 53
	IM_MSG_TYPE_TT_URL_TEXT_NOTIFY                           IM_MSG_TYPE = 54
	IM_MSG_TYPE_STRANGER_INFO_CARD                           IM_MSG_TYPE = 55
	IM_MSG_TYPE_UGC_SHARE_POST                               IM_MSG_TYPE = 56
	IM_MSG_TYPE_SEND_POST_TIPS                               IM_MSG_TYPE = 57
	IM_MSG_TYPE_INVITE_FOLLOW_BACK                           IM_MSG_TYPE = 58
	IM_MSG_TYPE_FOLLOW_TIPS                                  IM_MSG_TYPE = 59
	IM_MSG_TYPE_RICH_TEXT_MSG                                IM_MSG_TYPE = 60
	IM_MSG_TYPE_PERSONAL_RICH_TEXT_MSG                       IM_MSG_TYPE = 61
	IM_MSG_TYPE_CUE_MSG                                      IM_MSG_TYPE = 63
	IM_MSG_TYPE_SEND_PLAY_TOGETHER                           IM_MSG_TYPE = 64
	IM_MSG_TYPE_ACCEPT_PLAY_TOGETHER                         IM_MSG_TYPE = 65
	IM_MSG_TYPE_NEW_POST_TIPS                                IM_MSG_TYPE = 66
	IM_MSG_TYPE_INTERACTION_INTIMACY_GIFT                    IM_MSG_TYPE = 67
	IM_MSG_TYPE_IM_PRESENT                                   IM_MSG_TYPE = 68
	IM_MSG_TYPE_RICH_TEXT_MSG_WITH_NOTIFY                    IM_MSG_TYPE = 69
	IM_MSG_TYPE_PERSONAL_RICH_TEXT_MSG_WITH_NOTIFY           IM_MSG_TYPE = 70
	IM_MSG_TYPE_MASTER_APPRENTICE_INVITE_MSG                 IM_MSG_TYPE = 71
	IM_MSG_TYPE_MASTER_APPRENTICE_ESTABLISH_MSG              IM_MSG_TYPE = 72
	IM_MSG_TYPE_MASTER_APPRENTICE_INTRODUCTION_MSG           IM_MSG_TYPE = 73
	IM_MSG_TYPE_CHANNEL_OPEN_GAME_INVITE_MSG                 IM_MSG_TYPE = 75
	IM_MSG_TYPE_IM_PRESENT_NEW                               IM_MSG_TYPE = 76
	IM_MSG_TYPE_PLAYMATE_CARD                                IM_MSG_TYPE = 77
	IM_MSG_TYPE_DEL_MSG                                      IM_MSG_TYPE = 78
	IM_MSG_TYPE_CHAT_CARD                                    IM_MSG_TYPE = 79
	IM_MSG_TYPE_CHAT_CARD_HI_MSG                             IM_MSG_TYPE = 80
	IM_MSG_TYPE_SUPER_PLAYER_CUSTOM_EMOTICON_MSG             IM_MSG_TYPE = 81
	IM_MSG_TYPE_SLIP_NOTE_MSG                                IM_MSG_TYPE = 82
	IM_MSG_TYPE_CHANNEL_CP_GAME_RESULT_USER_NOTIFY           IM_MSG_TYPE = 83
	IM_MSG_TYPE_FELLOW_INVITE_MSG                            IM_MSG_TYPE = 84
	IM_MSG_TYPE_FELLOW_UPGRADE_MSG                           IM_MSG_TYPE = 85
	IM_MSG_TYPE_CHANNEL_GUIDE_APPOINMENT                     IM_MSG_TYPE = 86
	IM_MSG_TYPE_GAIN_RARE                                    IM_MSG_TYPE = 87
	IM_MSG_TYPE_GROUP_ANNOUNCEMENT                           IM_MSG_TYPE = 88
	IM_MSG_TYPE_GROUP_JOIN_CHECK                             IM_MSG_TYPE = 89
	IM_MSG_TYPE_GROUP_JOIN_ADMIN_ACCEPT                      IM_MSG_TYPE = 90
	IM_MSG_TYPE_GROUP_ANNOUNCEMENT_CHECK_REJECT              IM_MSG_TYPE = 91
	IM_MSG_TYPE_GROUP_JOIN_VERIFY_QUESTION_SET               IM_MSG_TYPE = 92
	IM_MSG_TYPE_BRAND_SHARE                                  IM_MSG_TYPE = 93
	IM_MSG_TYPE_WERWOLF_WIN                                  IM_MSG_TYPE = 94
	IM_MSG_TYPE_SHARE_GAME_MSG                               IM_MSG_TYPE = 95
	IM_MSG_TYPE_SHARE_SCENARIO_MSG                           IM_MSG_TYPE = 96
	IM_MSG_TYPE_PGC_CHANNEL_PK_MVP_MSG                       IM_MSG_TYPE = 97
	IM_MSG_TYPE_MUSE_POST_INTERACTIVE_LIKE_MSG               IM_MSG_TYPE = 98
	IM_MSG_TYPE_MUSE_POST_INTERACTIVE_COMMENT_MSG            IM_MSG_TYPE = 99
	IM_MSG_TYPE_MUSE_POST_INTERACTIVE_COMMENT_AT_MSG         IM_MSG_TYPE = 100
	IM_MSG_TYPE_MUSE_POST_INTERACTIVE_LIKE_AT_MSG            IM_MSG_TYPE = 101
	IM_MSG_TYPE_MIJING_GOOD_COMMENT                          IM_MSG_TYPE = 102
	IM_MSG_TYPE_PERFECT_MATCH_GAME_RESULT_USER_NOTIFY        IM_MSG_TYPE = 103
	IM_MSG_TYPE_MIJING_INVITE_CARD_MSG                       IM_MSG_TYPE = 104
	IM_MSG_TYPE_GLORY_CELEBRITY_SHARE_MSG                    IM_MSG_TYPE = 105
	IM_MSG_TYPE_SCENARIO_SHARE_MSG                           IM_MSG_TYPE = 106
	IM_MSG_TYPE_CHANNEL_LIVE_MULTI_PK_BEST_PARTNER_MSG       IM_MSG_TYPE = 107
	IM_MSG_TYPE_SOCIAL_COMMUNITY_INVITE_KERNEL_MEMBER        IM_MSG_TYPE = 108
	IM_MSG_TYPE_USER_RECALL_NOTIFY_MSG                       IM_MSG_TYPE = 109
	IM_MSG_TYPE_GAME_PAL_CARD_GAME_NICKNAME_MSG              IM_MSG_TYPE = 110
	IM_MSG_TYPE_GAME_PAL_CARD_FIRST_IM_CARD_MSG              IM_MSG_TYPE = 111
	IM_MSG_TYPE_GAME_PAL_CARD_IM_GUIDE_MSG                   IM_MSG_TYPE = 112
	IM_MSG_TYPE_MIJING_PARTNER_CARD_INVITE_MSG               IM_MSG_TYPE = 113
	IM_MSG_TYPE_ESPORT_ORDER_MSG                             IM_MSG_TYPE = 114
	IM_MSG_TYPE_ESPORT_SYS_MSG                               IM_MSG_TYPE = 115
	IM_MSG_TYPE_SCENARIO_AI_PROMPT_AUTO_REPLY_MSG            IM_MSG_TYPE = 116
	IM_MSG_TYPE_SOCIAL_COMMUNITY_POST_SHARE_MSG              IM_MSG_TYPE = 117
	IM_MSG_TYPE_SOCIAL_COMMUNITY_MEMBER_CARD                 IM_MSG_TYPE = 118
	IM_MSG_TYPE_ESPORT_GAME_CARD                             IM_MSG_TYPE = 119
	IM_MSG_TYPE_ESPORT_GAME_SELF_INTRO_TO_VISITOR            IM_MSG_TYPE = 120
	IM_MSG_TYPE_SOCIAL_COMMUNITY_ASSISTANT_POST_PUSH_MESSAGE IM_MSG_TYPE = 121
	IM_MSG_TYPE_GUILD_MANAGE_ROLE_INVITE_MSG                 IM_MSG_TYPE = 122
	IM_MSG_TYPE_GAME_INVITE_ROOM_MSG                         IM_MSG_TYPE = 123
	IM_MSG_TYPE_ESPORT_GAME_SELF_INTRO_TO_VISITOR_V2         IM_MSG_TYPE = 124
	IM_MSG_TYPE_RICHER_BIRTHDAY_NOTICE_MSG                   IM_MSG_TYPE = 125
	IM_MSG_TYPE_VIRTUAL_IMAGW_BIND_INVITE_MSG                IM_MSG_TYPE = 126
	IM_MSG_TYPE_ASSISTANT_PUSH_GAME_PAL                      IM_MSG_TYPE = 127
	IM_MSG_TYPE_ASSISTANT_PUSH_GAME_PAL_ZONE_POPUP           IM_MSG_TYPE = 128
	IM_MSG_TYPE_CommonCardMsg                                IM_MSG_TYPE = 129
	IM_MSG_TYPE_XML_MSG_CENTER                               IM_MSG_TYPE = 130
	IM_MSG_TYPE_AI_PLAY_HUB_IM_Msg                           IM_MSG_TYPE = 131
	IM_MSG_TYPE_XML_MSG_NORMAL                               IM_MSG_TYPE = 132
	IM_MSG_TYPE_XML_MSG_BUBBLE                               IM_MSG_TYPE = 133
	IM_MSG_TYPE_AI_INSPIRATION_IM_MSG                        IM_MSG_TYPE = 134
	IM_MSG_TYPE_WEDDING_PROPOSE_IM_MSG                       IM_MSG_TYPE = 135
	IM_MSG_TYPE_TWIN_MSG                                     IM_MSG_TYPE = 136
	IM_MSG_TYPE_MUSE_ROLE_PLAY_IM_MSG                        IM_MSG_TYPE = 137
	IM_MSG_TYPE_WEDDING_CONSULT_RESERVE_IM_MSG               IM_MSG_TYPE = 138
	IM_MSG_TYPE_WEDDING_ARRANGE_RESERVE_IM_MSG               IM_MSG_TYPE = 139
)

var IM_MSG_TYPE_name = map[int32]string{
	1:   "TEXT_MSG",
	2:   "IMG_MSG",
	3:   "VOICE_MSG",
	4:   "VIDEO_MSG",
	5:   "EXTENDED_MSG",
	7:   "SYSTEM_NOTIFY",
	8:   "GUILD_QUIT",
	9:   "EXPRESSION_MSG",
	10:  "GUILD_BC",
	11:  "GUILD_JOIN",
	12:  "GUILD_ADMIN",
	13:  "GUILD_GROUP_JOIN",
	14:  "GUILD_GROUP_OWNER",
	15:  "GUILD_GROUP_ADMIN",
	16:  "GUILD_ASST_NOMINATE",
	17:  "GUILD_ASST_APPLY_JOIN_GUILD",
	18:  "GUILD_ASST_APPLY_JOIN_GROUP",
	19:  "GUILD_ASST_GIFT_PKG_RESULT",
	20:  "GUILD_ASST_QUIT_GUILD",
	21:  "NEW_EXTENDED",
	22:  "OFFICAIL_MESSAGE_SINGLE",
	23:  "OFFICAIL_MESSAGE_BUNCH",
	24:  "CALL_IN",
	25:  "SESSION_CREATE_MSG",
	26:  "SESSION_CLOSE_MSG",
	27:  "ACCEPT_CALL_IN",
	28:  "CALL_IN_END",
	29:  "GROUP_ASST_APPLY_JOIN_GROUP",
	30:  "GROUP_ASST_QUIT_GROUP",
	31:  "TGROUP_JOIN_GROUP",
	32:  "AIR_TICKET",
	33:  "GUILD_ASST_NOTIFY",
	34:  "AT_SOMEONE_MSG",
	35:  "GUILD_MEMBER_TITLE",
	36:  "GUILD_ASST_COMMON_URL_NOTIFY",
	37:  "TT_COMMON_TEXT_NOTIFY",
	38:  "AT_EVERYONE_MSG",
	39:  "CUSTOM_EMOTICON_MSG",
	40:  "GUILD_CIRCLE_ANNOUNCEMENT",
	41:  "CANCEL_MSG",
	42:  "GAME_PREORDER_TEXT_NOTIFY",
	43:  "TGROUP_INVITE_MSG",
	44:  "FIND_FRIENDS_MATED_UP_MSG",
	45:  "FIND_FRIENDS_LIKED_NOTIFY",
	46:  "GAME_CENTER_POP_GAME_NOTIFY",
	47:  "CHANNEL_DATING_GAME_LIKE_USER_NOTIFY",
	48:  "FIND_FRIENDS_ICEBREAK_MATCH",
	49:  "SENSITIVE_NOTIFY_MSG",
	50:  "INTERACTIVE_UNREAD_NOTIFY",
	51:  "CHANNEL_PK_RESULT_NOTIFY",
	52:  "KNOCK_INROOM_NOTIFY",
	53:  "CIVILIZATION_CONVENTION",
	54:  "TT_URL_TEXT_NOTIFY",
	55:  "STRANGER_INFO_CARD",
	56:  "UGC_SHARE_POST",
	57:  "SEND_POST_TIPS",
	58:  "INVITE_FOLLOW_BACK",
	59:  "FOLLOW_TIPS",
	60:  "RICH_TEXT_MSG",
	61:  "PERSONAL_RICH_TEXT_MSG",
	63:  "CUE_MSG",
	64:  "SEND_PLAY_TOGETHER",
	65:  "ACCEPT_PLAY_TOGETHER",
	66:  "NEW_POST_TIPS",
	67:  "INTERACTION_INTIMACY_GIFT",
	68:  "IM_PRESENT",
	69:  "RICH_TEXT_MSG_WITH_NOTIFY",
	70:  "PERSONAL_RICH_TEXT_MSG_WITH_NOTIFY",
	71:  "MASTER_APPRENTICE_INVITE_MSG",
	72:  "MASTER_APPRENTICE_ESTABLISH_MSG",
	73:  "MASTER_APPRENTICE_INTRODUCTION_MSG",
	75:  "CHANNEL_OPEN_GAME_INVITE_MSG",
	76:  "IM_PRESENT_NEW",
	77:  "PLAYMATE_CARD",
	78:  "DEL_MSG",
	79:  "CHAT_CARD",
	80:  "CHAT_CARD_HI_MSG",
	81:  "SUPER_PLAYER_CUSTOM_EMOTICON_MSG",
	82:  "SLIP_NOTE_MSG",
	83:  "CHANNEL_CP_GAME_RESULT_USER_NOTIFY",
	84:  "FELLOW_INVITE_MSG",
	85:  "FELLOW_UPGRADE_MSG",
	86:  "CHANNEL_GUIDE_APPOINMENT",
	87:  "GAIN_RARE",
	88:  "GROUP_ANNOUNCEMENT",
	89:  "GROUP_JOIN_CHECK",
	90:  "GROUP_JOIN_ADMIN_ACCEPT",
	91:  "GROUP_ANNOUNCEMENT_CHECK_REJECT",
	92:  "GROUP_JOIN_VERIFY_QUESTION_SET",
	93:  "BRAND_SHARE",
	94:  "WERWOLF_WIN",
	95:  "SHARE_GAME_MSG",
	96:  "SHARE_SCENARIO_MSG",
	97:  "PGC_CHANNEL_PK_MVP_MSG",
	98:  "MUSE_POST_INTERACTIVE_LIKE_MSG",
	99:  "MUSE_POST_INTERACTIVE_COMMENT_MSG",
	100: "MUSE_POST_INTERACTIVE_COMMENT_AT_MSG",
	101: "MUSE_POST_INTERACTIVE_LIKE_AT_MSG",
	102: "MIJING_GOOD_COMMENT",
	103: "PERFECT_MATCH_GAME_RESULT_USER_NOTIFY",
	104: "MIJING_INVITE_CARD_MSG",
	105: "GLORY_CELEBRITY_SHARE_MSG",
	106: "SCENARIO_SHARE_MSG",
	107: "CHANNEL_LIVE_MULTI_PK_BEST_PARTNER_MSG",
	108: "SOCIAL_COMMUNITY_INVITE_KERNEL_MEMBER",
	109: "USER_RECALL_NOTIFY_MSG",
	110: "GAME_PAL_CARD_GAME_NICKNAME_MSG",
	111: "GAME_PAL_CARD_FIRST_IM_CARD_MSG",
	112: "GAME_PAL_CARD_IM_GUIDE_MSG",
	113: "MIJING_PARTNER_CARD_INVITE_MSG",
	114: "ESPORT_ORDER_MSG",
	115: "ESPORT_SYS_MSG",
	116: "SCENARIO_AI_PROMPT_AUTO_REPLY_MSG",
	117: "SOCIAL_COMMUNITY_POST_SHARE_MSG",
	118: "SOCIAL_COMMUNITY_MEMBER_CARD",
	119: "ESPORT_GAME_CARD",
	120: "ESPORT_GAME_SELF_INTRO_TO_VISITOR",
	121: "SOCIAL_COMMUNITY_ASSISTANT_POST_PUSH_MESSAGE",
	122: "GUILD_MANAGE_ROLE_INVITE_MSG",
	123: "GAME_INVITE_ROOM_MSG",
	124: "ESPORT_GAME_SELF_INTRO_TO_VISITOR_V2",
	125: "RICHER_BIRTHDAY_NOTICE_MSG",
	126: "VIRTUAL_IMAGW_BIND_INVITE_MSG",
	127: "ASSISTANT_PUSH_GAME_PAL",
	128: "ASSISTANT_PUSH_GAME_PAL_ZONE_POPUP",
	129: "CommonCardMsg",
	130: "XML_MSG_CENTER",
	131: "AI_PLAY_HUB_IM_Msg",
	132: "XML_MSG_NORMAL",
	133: "XML_MSG_BUBBLE",
	134: "AI_INSPIRATION_IM_MSG",
	135: "WEDDING_PROPOSE_IM_MSG",
	136: "TWIN_MSG",
	137: "MUSE_ROLE_PLAY_IM_MSG",
	138: "WEDDING_CONSULT_RESERVE_IM_MSG",
	139: "WEDDING_ARRANGE_RESERVE_IM_MSG",
}
var IM_MSG_TYPE_value = map[string]int32{
	"TEXT_MSG":                                     1,
	"IMG_MSG":                                      2,
	"VOICE_MSG":                                    3,
	"VIDEO_MSG":                                    4,
	"EXTENDED_MSG":                                 5,
	"SYSTEM_NOTIFY":                                7,
	"GUILD_QUIT":                                   8,
	"EXPRESSION_MSG":                               9,
	"GUILD_BC":                                     10,
	"GUILD_JOIN":                                   11,
	"GUILD_ADMIN":                                  12,
	"GUILD_GROUP_JOIN":                             13,
	"GUILD_GROUP_OWNER":                            14,
	"GUILD_GROUP_ADMIN":                            15,
	"GUILD_ASST_NOMINATE":                          16,
	"GUILD_ASST_APPLY_JOIN_GUILD":                  17,
	"GUILD_ASST_APPLY_JOIN_GROUP":                  18,
	"GUILD_ASST_GIFT_PKG_RESULT":                   19,
	"GUILD_ASST_QUIT_GUILD":                        20,
	"NEW_EXTENDED":                                 21,
	"OFFICAIL_MESSAGE_SINGLE":                      22,
	"OFFICAIL_MESSAGE_BUNCH":                       23,
	"CALL_IN":                                      24,
	"SESSION_CREATE_MSG":                           25,
	"SESSION_CLOSE_MSG":                            26,
	"ACCEPT_CALL_IN":                               27,
	"CALL_IN_END":                                  28,
	"GROUP_ASST_APPLY_JOIN_GROUP":                  29,
	"GROUP_ASST_QUIT_GROUP":                        30,
	"TGROUP_JOIN_GROUP":                            31,
	"AIR_TICKET":                                   32,
	"GUILD_ASST_NOTIFY":                            33,
	"AT_SOMEONE_MSG":                               34,
	"GUILD_MEMBER_TITLE":                           35,
	"GUILD_ASST_COMMON_URL_NOTIFY":                 36,
	"TT_COMMON_TEXT_NOTIFY":                        37,
	"AT_EVERYONE_MSG":                              38,
	"CUSTOM_EMOTICON_MSG":                          39,
	"GUILD_CIRCLE_ANNOUNCEMENT":                    40,
	"CANCEL_MSG":                                   41,
	"GAME_PREORDER_TEXT_NOTIFY":                    42,
	"TGROUP_INVITE_MSG":                            43,
	"FIND_FRIENDS_MATED_UP_MSG":                    44,
	"FIND_FRIENDS_LIKED_NOTIFY":                    45,
	"GAME_CENTER_POP_GAME_NOTIFY":                  46,
	"CHANNEL_DATING_GAME_LIKE_USER_NOTIFY":         47,
	"FIND_FRIENDS_ICEBREAK_MATCH":                  48,
	"SENSITIVE_NOTIFY_MSG":                         49,
	"INTERACTIVE_UNREAD_NOTIFY":                    50,
	"CHANNEL_PK_RESULT_NOTIFY":                     51,
	"KNOCK_INROOM_NOTIFY":                          52,
	"CIVILIZATION_CONVENTION":                      53,
	"TT_URL_TEXT_NOTIFY":                           54,
	"STRANGER_INFO_CARD":                           55,
	"UGC_SHARE_POST":                               56,
	"SEND_POST_TIPS":                               57,
	"INVITE_FOLLOW_BACK":                           58,
	"FOLLOW_TIPS":                                  59,
	"RICH_TEXT_MSG":                                60,
	"PERSONAL_RICH_TEXT_MSG":                       61,
	"CUE_MSG":                                      63,
	"SEND_PLAY_TOGETHER":                           64,
	"ACCEPT_PLAY_TOGETHER":                         65,
	"NEW_POST_TIPS":                                66,
	"INTERACTION_INTIMACY_GIFT":                    67,
	"IM_PRESENT":                                   68,
	"RICH_TEXT_MSG_WITH_NOTIFY":                    69,
	"PERSONAL_RICH_TEXT_MSG_WITH_NOTIFY":           70,
	"MASTER_APPRENTICE_INVITE_MSG":                 71,
	"MASTER_APPRENTICE_ESTABLISH_MSG":              72,
	"MASTER_APPRENTICE_INTRODUCTION_MSG":           73,
	"CHANNEL_OPEN_GAME_INVITE_MSG":                 75,
	"IM_PRESENT_NEW":                               76,
	"PLAYMATE_CARD":                                77,
	"DEL_MSG":                                      78,
	"CHAT_CARD":                                    79,
	"CHAT_CARD_HI_MSG":                             80,
	"SUPER_PLAYER_CUSTOM_EMOTICON_MSG":             81,
	"SLIP_NOTE_MSG":                                82,
	"CHANNEL_CP_GAME_RESULT_USER_NOTIFY":           83,
	"FELLOW_INVITE_MSG":                            84,
	"FELLOW_UPGRADE_MSG":                           85,
	"CHANNEL_GUIDE_APPOINMENT":                     86,
	"GAIN_RARE":                                    87,
	"GROUP_ANNOUNCEMENT":                           88,
	"GROUP_JOIN_CHECK":                             89,
	"GROUP_JOIN_ADMIN_ACCEPT":                      90,
	"GROUP_ANNOUNCEMENT_CHECK_REJECT":              91,
	"GROUP_JOIN_VERIFY_QUESTION_SET":               92,
	"BRAND_SHARE":                                  93,
	"WERWOLF_WIN":                                  94,
	"SHARE_GAME_MSG":                               95,
	"SHARE_SCENARIO_MSG":                           96,
	"PGC_CHANNEL_PK_MVP_MSG":                       97,
	"MUSE_POST_INTERACTIVE_LIKE_MSG":               98,
	"MUSE_POST_INTERACTIVE_COMMENT_MSG":            99,
	"MUSE_POST_INTERACTIVE_COMMENT_AT_MSG":         100,
	"MUSE_POST_INTERACTIVE_LIKE_AT_MSG":            101,
	"MIJING_GOOD_COMMENT":                          102,
	"PERFECT_MATCH_GAME_RESULT_USER_NOTIFY":        103,
	"MIJING_INVITE_CARD_MSG":                       104,
	"GLORY_CELEBRITY_SHARE_MSG":                    105,
	"SCENARIO_SHARE_MSG":                           106,
	"CHANNEL_LIVE_MULTI_PK_BEST_PARTNER_MSG":       107,
	"SOCIAL_COMMUNITY_INVITE_KERNEL_MEMBER":        108,
	"USER_RECALL_NOTIFY_MSG":                       109,
	"GAME_PAL_CARD_GAME_NICKNAME_MSG":              110,
	"GAME_PAL_CARD_FIRST_IM_CARD_MSG":              111,
	"GAME_PAL_CARD_IM_GUIDE_MSG":                   112,
	"MIJING_PARTNER_CARD_INVITE_MSG":               113,
	"ESPORT_ORDER_MSG":                             114,
	"ESPORT_SYS_MSG":                               115,
	"SCENARIO_AI_PROMPT_AUTO_REPLY_MSG":            116,
	"SOCIAL_COMMUNITY_POST_SHARE_MSG":              117,
	"SOCIAL_COMMUNITY_MEMBER_CARD":                 118,
	"ESPORT_GAME_CARD":                             119,
	"ESPORT_GAME_SELF_INTRO_TO_VISITOR":            120,
	"SOCIAL_COMMUNITY_ASSISTANT_POST_PUSH_MESSAGE": 121,
	"GUILD_MANAGE_ROLE_INVITE_MSG":                 122,
	"GAME_INVITE_ROOM_MSG":                         123,
	"ESPORT_GAME_SELF_INTRO_TO_VISITOR_V2":         124,
	"RICHER_BIRTHDAY_NOTICE_MSG":                   125,
	"VIRTUAL_IMAGW_BIND_INVITE_MSG":                126,
	"ASSISTANT_PUSH_GAME_PAL":                      127,
	"ASSISTANT_PUSH_GAME_PAL_ZONE_POPUP":           128,
	"CommonCardMsg":                                129,
	"XML_MSG_CENTER":                               130,
	"AI_PLAY_HUB_IM_Msg":                           131,
	"XML_MSG_NORMAL":                               132,
	"XML_MSG_BUBBLE":                               133,
	"AI_INSPIRATION_IM_MSG":                        134,
	"WEDDING_PROPOSE_IM_MSG":                       135,
	"TWIN_MSG":                                     136,
	"MUSE_ROLE_PLAY_IM_MSG":                        137,
	"WEDDING_CONSULT_RESERVE_IM_MSG":               138,
	"WEDDING_ARRANGE_RESERVE_IM_MSG":               139,
}

func (x IM_MSG_TYPE) Enum() *IM_MSG_TYPE {
	p := new(IM_MSG_TYPE)
	*p = x
	return p
}
func (x IM_MSG_TYPE) String() string {
	return proto.EnumName(IM_MSG_TYPE_name, int32(x))
}
func (x *IM_MSG_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(IM_MSG_TYPE_value, data, "IM_MSG_TYPE")
	if err != nil {
		return err
	}
	*x = IM_MSG_TYPE(value)
	return nil
}
func (IM_MSG_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{0} }

type MsgSourceType int32

const (
	MsgSourceType_NORMAL_SOURCE                               MsgSourceType = 0
	MsgSourceType_CHANNEL_ROOM                                MsgSourceType = 1
	MsgSourceType_NEARBY_PEOPLE                               MsgSourceType = 2
	MsgSourceType_MSG_SOURCE_FINDFRIEND_EXAM                  MsgSourceType = 3
	MsgSourceType_MSG_SOURCE_FINDFRIEND_MATCH                 MsgSourceType = 4
	MsgSourceType_MSG_SOURCE_USERTAG_MATCH                    MsgSourceType = 5
	MsgSourceType_MSG_SOURCE_USERTAG_MATCH_NORMAL             MsgSourceType = 6
	MsgSourceType_MSG_SOURCE_DEL_FROM_USER_BLACK_LIST         MsgSourceType = 7
	MsgSourceType_MSG_SOURCE_CHANNEL_USER_CARD                MsgSourceType = 8
	MsgSourceType_MSG_SOURCE_FIND_PLAYER                      MsgSourceType = 9
	MsgSourceType_MSG_SOURCE_SEND_PLAYER                      MsgSourceType = 10
	MsgSourceType_MSG_SOURCE_FROM_UGC_POST                    MsgSourceType = 11
	MsgSourceType_MSG_SOURCE_FROM_IM_PRESENT                  MsgSourceType = 12
	MsgSourceType_MSG_SOURCE_FROM_MASTER_APPRENTICE_INVITE    MsgSourceType = 13
	MsgSourceType_MSG_SOURCE_FROM_GAME_RADAR                  MsgSourceType = 14
	MsgSourceType_MSG_SOURCE_FROM_CHANNEL_DETAIL_GANG_UP_LIST MsgSourceType = 15
	MsgSourceType_MSG_SOURCE_FROM_INVITE_FROM_CHANNEL         MsgSourceType = 16
	MsgSourceType_MSG_SOURCE_FROM_CHAT_CARD                   MsgSourceType = 17
	MsgSourceType_MSG_SOURCE_FROM_SYS_AUTO_HEY                MsgSourceType = 18
	MsgSourceType_MSG_SOURCE_FROM_SLIP_NOTE                   MsgSourceType = 19
	MsgSourceType_MSG_SOURCE_FROM_CHAT_CARD_DIY_MSG           MsgSourceType = 20
	MsgSourceType_MSG_SOURCE_FROM_CHAT_CARD_A_GROUP_LIKE      MsgSourceType = 21
	MsgSourceType_MSG_SOURCE_FROM_FELLOW_INVITE               MsgSourceType = 22
	MsgSourceType_MSG_SOURCE_FROM_FELLOW_UPGRADE              MsgSourceType = 23
	MsgSourceType_MSG_SOURCE_FROM_FELLOW_GAIN_RARE            MsgSourceType = 24
	MsgSourceType_MSG_SOURCE_FROM_CHATGPT_BOT_IM              MsgSourceType = 25
	MsgSourceType_MSG_SOURCE_FROM_MIJING_HOMEPAGE_CHAT_A_CHAT MsgSourceType = 26
	MsgSourceType_MSG_SOURCE_FROM_USER_RECALL_COMEBACK        MsgSourceType = 27
	MsgSourceType_MSG_SOURCE_FROM_GAME_PAL_CARD               MsgSourceType = 28
	MsgSourceType_MSG_SOURCE_FROM_NPC_AUTO_IM                 MsgSourceType = 29
	MsgSourceType_MSG_SOURCE_FROM_ESPORT                      MsgSourceType = 30
	MsgSourceType_MSG_SOURCE_FROM_PAL_CARD_TO_PERSON_CENTER   MsgSourceType = 31
	MsgSourceType_MSG_SOURCE_FROM_AI_PROMPT_AUTO_IM           MsgSourceType = 32
	MsgSourceType_MSG_SOURCE_FROM_GAME_CARD_TO_PERSON_CENTER  MsgSourceType = 33
	MsgSourceType_MSG_SOURCE_FROM_INVITE_ROOM_ENTRANCE        MsgSourceType = 34
	MsgSourceType_MSG_SOURCE_FROM_NON_MIC_RECALL_USER_AUTO_IM MsgSourceType = 35
	MsgSourceType_MSG_SOURCE_FROM_PAL_CARD_INVITE_ROOM        MsgSourceType = 36
	MsgSourceType_MSG_SOURCE_FROM_MIJING_SERVER               MsgSourceType = 100
	MsgSourceType_MSG_SOURCE_FROM_ESPORT_GAME_CARD_2_IM_PAGE  MsgSourceType = 101
	MsgSourceType_MSG_SOURCE_FROM_E_SPORT_ZONE_HALF_SCREEN    MsgSourceType = 102
	MsgSourceType_MSG_SOURCE_FROM_GAME_HALL_TEAM_LIST         MsgSourceType = 103
	MsgSourceType_MSG_SOURCE_FROM_INVITE_FROM_CHANNEL_V2      MsgSourceType = 106
	MsgSourceType_MSG_SOURCE_FROM_GAME_IM_TEAM_GROUP          MsgSourceType = 107
	MsgSourceType_MSG_SOURCE_FROM_GAME_FEED                   MsgSourceType = 108
	MsgSourceType_MSG_SOURCE_FROM_RICHER_BIRTHDAY             MsgSourceType = 109
	MsgSourceType_MSG_SOURCE_FROM_GAME_PAL_ZONE_POPUP         MsgSourceType = 110
	MsgSourceType_MSG_SOURCE_FROM_TODAY_CP                    MsgSourceType = 111
	MsgSourceType_MSG_SOURCE_FROM_ESPORT_BACK_RECALL          MsgSourceType = 112
	MsgSourceType_MSG_SOURCE_FROM_FLASH_CHAT                  MsgSourceType = 113
	MsgSourceType_MSG_SOURCE_FROM_ROLE_PLAY                   MsgSourceType = 114
)

var MsgSourceType_name = map[int32]string{
	0:   "NORMAL_SOURCE",
	1:   "CHANNEL_ROOM",
	2:   "NEARBY_PEOPLE",
	3:   "MSG_SOURCE_FINDFRIEND_EXAM",
	4:   "MSG_SOURCE_FINDFRIEND_MATCH",
	5:   "MSG_SOURCE_USERTAG_MATCH",
	6:   "MSG_SOURCE_USERTAG_MATCH_NORMAL",
	7:   "MSG_SOURCE_DEL_FROM_USER_BLACK_LIST",
	8:   "MSG_SOURCE_CHANNEL_USER_CARD",
	9:   "MSG_SOURCE_FIND_PLAYER",
	10:  "MSG_SOURCE_SEND_PLAYER",
	11:  "MSG_SOURCE_FROM_UGC_POST",
	12:  "MSG_SOURCE_FROM_IM_PRESENT",
	13:  "MSG_SOURCE_FROM_MASTER_APPRENTICE_INVITE",
	14:  "MSG_SOURCE_FROM_GAME_RADAR",
	15:  "MSG_SOURCE_FROM_CHANNEL_DETAIL_GANG_UP_LIST",
	16:  "MSG_SOURCE_FROM_INVITE_FROM_CHANNEL",
	17:  "MSG_SOURCE_FROM_CHAT_CARD",
	18:  "MSG_SOURCE_FROM_SYS_AUTO_HEY",
	19:  "MSG_SOURCE_FROM_SLIP_NOTE",
	20:  "MSG_SOURCE_FROM_CHAT_CARD_DIY_MSG",
	21:  "MSG_SOURCE_FROM_CHAT_CARD_A_GROUP_LIKE",
	22:  "MSG_SOURCE_FROM_FELLOW_INVITE",
	23:  "MSG_SOURCE_FROM_FELLOW_UPGRADE",
	24:  "MSG_SOURCE_FROM_FELLOW_GAIN_RARE",
	25:  "MSG_SOURCE_FROM_CHATGPT_BOT_IM",
	26:  "MSG_SOURCE_FROM_MIJING_HOMEPAGE_CHAT_A_CHAT",
	27:  "MSG_SOURCE_FROM_USER_RECALL_COMEBACK",
	28:  "MSG_SOURCE_FROM_GAME_PAL_CARD",
	29:  "MSG_SOURCE_FROM_NPC_AUTO_IM",
	30:  "MSG_SOURCE_FROM_ESPORT",
	31:  "MSG_SOURCE_FROM_PAL_CARD_TO_PERSON_CENTER",
	32:  "MSG_SOURCE_FROM_AI_PROMPT_AUTO_IM",
	33:  "MSG_SOURCE_FROM_GAME_CARD_TO_PERSON_CENTER",
	34:  "MSG_SOURCE_FROM_INVITE_ROOM_ENTRANCE",
	35:  "MSG_SOURCE_FROM_NON_MIC_RECALL_USER_AUTO_IM",
	36:  "MSG_SOURCE_FROM_PAL_CARD_INVITE_ROOM",
	100: "MSG_SOURCE_FROM_MIJING_SERVER",
	101: "MSG_SOURCE_FROM_ESPORT_GAME_CARD_2_IM_PAGE",
	102: "MSG_SOURCE_FROM_E_SPORT_ZONE_HALF_SCREEN",
	103: "MSG_SOURCE_FROM_GAME_HALL_TEAM_LIST",
	106: "MSG_SOURCE_FROM_INVITE_FROM_CHANNEL_V2",
	107: "MSG_SOURCE_FROM_GAME_IM_TEAM_GROUP",
	108: "MSG_SOURCE_FROM_GAME_FEED",
	109: "MSG_SOURCE_FROM_RICHER_BIRTHDAY",
	110: "MSG_SOURCE_FROM_GAME_PAL_ZONE_POPUP",
	111: "MSG_SOURCE_FROM_TODAY_CP",
	112: "MSG_SOURCE_FROM_ESPORT_BACK_RECALL",
	113: "MSG_SOURCE_FROM_FLASH_CHAT",
	114: "MSG_SOURCE_FROM_ROLE_PLAY",
}
var MsgSourceType_value = map[string]int32{
	"NORMAL_SOURCE":                               0,
	"CHANNEL_ROOM":                                1,
	"NEARBY_PEOPLE":                               2,
	"MSG_SOURCE_FINDFRIEND_EXAM":                  3,
	"MSG_SOURCE_FINDFRIEND_MATCH":                 4,
	"MSG_SOURCE_USERTAG_MATCH":                    5,
	"MSG_SOURCE_USERTAG_MATCH_NORMAL":             6,
	"MSG_SOURCE_DEL_FROM_USER_BLACK_LIST":         7,
	"MSG_SOURCE_CHANNEL_USER_CARD":                8,
	"MSG_SOURCE_FIND_PLAYER":                      9,
	"MSG_SOURCE_SEND_PLAYER":                      10,
	"MSG_SOURCE_FROM_UGC_POST":                    11,
	"MSG_SOURCE_FROM_IM_PRESENT":                  12,
	"MSG_SOURCE_FROM_MASTER_APPRENTICE_INVITE":    13,
	"MSG_SOURCE_FROM_GAME_RADAR":                  14,
	"MSG_SOURCE_FROM_CHANNEL_DETAIL_GANG_UP_LIST": 15,
	"MSG_SOURCE_FROM_INVITE_FROM_CHANNEL":         16,
	"MSG_SOURCE_FROM_CHAT_CARD":                   17,
	"MSG_SOURCE_FROM_SYS_AUTO_HEY":                18,
	"MSG_SOURCE_FROM_SLIP_NOTE":                   19,
	"MSG_SOURCE_FROM_CHAT_CARD_DIY_MSG":           20,
	"MSG_SOURCE_FROM_CHAT_CARD_A_GROUP_LIKE":      21,
	"MSG_SOURCE_FROM_FELLOW_INVITE":               22,
	"MSG_SOURCE_FROM_FELLOW_UPGRADE":              23,
	"MSG_SOURCE_FROM_FELLOW_GAIN_RARE":            24,
	"MSG_SOURCE_FROM_CHATGPT_BOT_IM":              25,
	"MSG_SOURCE_FROM_MIJING_HOMEPAGE_CHAT_A_CHAT": 26,
	"MSG_SOURCE_FROM_USER_RECALL_COMEBACK":        27,
	"MSG_SOURCE_FROM_GAME_PAL_CARD":               28,
	"MSG_SOURCE_FROM_NPC_AUTO_IM":                 29,
	"MSG_SOURCE_FROM_ESPORT":                      30,
	"MSG_SOURCE_FROM_PAL_CARD_TO_PERSON_CENTER":   31,
	"MSG_SOURCE_FROM_AI_PROMPT_AUTO_IM":           32,
	"MSG_SOURCE_FROM_GAME_CARD_TO_PERSON_CENTER":  33,
	"MSG_SOURCE_FROM_INVITE_ROOM_ENTRANCE":        34,
	"MSG_SOURCE_FROM_NON_MIC_RECALL_USER_AUTO_IM": 35,
	"MSG_SOURCE_FROM_PAL_CARD_INVITE_ROOM":        36,
	"MSG_SOURCE_FROM_MIJING_SERVER":               100,
	"MSG_SOURCE_FROM_ESPORT_GAME_CARD_2_IM_PAGE":  101,
	"MSG_SOURCE_FROM_E_SPORT_ZONE_HALF_SCREEN":    102,
	"MSG_SOURCE_FROM_GAME_HALL_TEAM_LIST":         103,
	"MSG_SOURCE_FROM_INVITE_FROM_CHANNEL_V2":      106,
	"MSG_SOURCE_FROM_GAME_IM_TEAM_GROUP":          107,
	"MSG_SOURCE_FROM_GAME_FEED":                   108,
	"MSG_SOURCE_FROM_RICHER_BIRTHDAY":             109,
	"MSG_SOURCE_FROM_GAME_PAL_ZONE_POPUP":         110,
	"MSG_SOURCE_FROM_TODAY_CP":                    111,
	"MSG_SOURCE_FROM_ESPORT_BACK_RECALL":          112,
	"MSG_SOURCE_FROM_FLASH_CHAT":                  113,
	"MSG_SOURCE_FROM_ROLE_PLAY":                   114,
}

func (x MsgSourceType) Enum() *MsgSourceType {
	p := new(MsgSourceType)
	*p = x
	return p
}
func (x MsgSourceType) String() string {
	return proto.EnumName(MsgSourceType_name, int32(x))
}
func (x *MsgSourceType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MsgSourceType_value, data, "MsgSourceType")
	if err != nil {
		return err
	}
	*x = MsgSourceType(value)
	return nil
}
func (MsgSourceType) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{1} }

type MsgSensitiveType int32

const (
	MsgSensitiveType_NORMAL_SENSTIVIVE MsgSensitiveType = 0
	MsgSensitiveType_MONEY_SENSTIVIVE  MsgSensitiveType = 1
)

var MsgSensitiveType_name = map[int32]string{
	0: "NORMAL_SENSTIVIVE",
	1: "MONEY_SENSTIVIVE",
}
var MsgSensitiveType_value = map[string]int32{
	"NORMAL_SENSTIVIVE": 0,
	"MONEY_SENSTIVIVE":  1,
}

func (x MsgSensitiveType) Enum() *MsgSensitiveType {
	p := new(MsgSensitiveType)
	*p = x
	return p
}
func (x MsgSensitiveType) String() string {
	return proto.EnumName(MsgSensitiveType_name, int32(x))
}
func (x *MsgSensitiveType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MsgSensitiveType_value, data, "MsgSensitiveType")
	if err != nil {
		return err
	}
	*x = MsgSensitiveType(value)
	return nil
}
func (MsgSensitiveType) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{2} }

type MsgLabel int32

const (
	MsgLabel_NO_LABEL                MsgLabel = 0
	MsgLabel_NEW_FOLLOWER            MsgLabel = 1
	MsgLabel_MSG_LABEL_SUPER_PUBLISH MsgLabel = 2
	MsgLabel_MSG_LABEL_GAME_PAL_CARD MsgLabel = 3
)

var MsgLabel_name = map[int32]string{
	0: "NO_LABEL",
	1: "NEW_FOLLOWER",
	2: "MSG_LABEL_SUPER_PUBLISH",
	3: "MSG_LABEL_GAME_PAL_CARD",
}
var MsgLabel_value = map[string]int32{
	"NO_LABEL":                0,
	"NEW_FOLLOWER":            1,
	"MSG_LABEL_SUPER_PUBLISH": 2,
	"MSG_LABEL_GAME_PAL_CARD": 3,
}

func (x MsgLabel) Enum() *MsgLabel {
	p := new(MsgLabel)
	*p = x
	return p
}
func (x MsgLabel) String() string {
	return proto.EnumName(MsgLabel_name, int32(x))
}
func (x *MsgLabel) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MsgLabel_value, data, "MsgLabel")
	if err != nil {
		return err
	}
	*x = MsgLabel(value)
	return nil
}
func (MsgLabel) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{3} }

type MsgReceiveSetting int32

const (
	MsgReceiveSetting_MSG_RECEIVE_WITH_NOTIFICATION    MsgReceiveSetting = 0
	MsgReceiveSetting_MSG_RECEIVE_WITHOUT_NOTIFICATION MsgReceiveSetting = 1
	MsgReceiveSetting_MSG_DO_NOT_RECEIVE               MsgReceiveSetting = 2
)

var MsgReceiveSetting_name = map[int32]string{
	0: "MSG_RECEIVE_WITH_NOTIFICATION",
	1: "MSG_RECEIVE_WITHOUT_NOTIFICATION",
	2: "MSG_DO_NOT_RECEIVE",
}
var MsgReceiveSetting_value = map[string]int32{
	"MSG_RECEIVE_WITH_NOTIFICATION":    0,
	"MSG_RECEIVE_WITHOUT_NOTIFICATION": 1,
	"MSG_DO_NOT_RECEIVE":               2,
}

func (x MsgReceiveSetting) Enum() *MsgReceiveSetting {
	p := new(MsgReceiveSetting)
	*p = x
	return p
}
func (x MsgReceiveSetting) String() string {
	return proto.EnumName(MsgReceiveSetting_name, int32(x))
}
func (x *MsgReceiveSetting) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MsgReceiveSetting_value, data, "MsgReceiveSetting")
	if err != nil {
		return err
	}
	*x = MsgReceiveSetting(value)
	return nil
}
func (MsgReceiveSetting) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{4} }

// LevelAwardItemType 废弃
type LevelAwardItemType int32

const (
	LevelAwardItemType_LEVEL_AWARD_ITEM_UNSPECIFIED      LevelAwardItemType = 0
	LevelAwardItemType_LEVEL_AWARD_ITEM_HEAD_WEAR        LevelAwardItemType = 1
	LevelAwardItemType_LEVEL_AWARD_ITEM_HORSE            LevelAwardItemType = 2
	LevelAwardItemType_LEVEL_AWARD_ITEM_MEDAL            LevelAwardItemType = 3
	LevelAwardItemType_LEVEL_AWARD_ITEM_DECORATION       LevelAwardItemType = 4
	LevelAwardItemType_LEVEL_AWARD_ITEM_Nameplate        LevelAwardItemType = 5
	LevelAwardItemType_LEVEL_AWARD_ITEM_MIC_OfficialCert LevelAwardItemType = 6
	LevelAwardItemType_LEVEL_AWARD_ITEM_INFORMATION_CARD LevelAwardItemType = 7
	LevelAwardItemType_LEVEL_AWARD_ITEM_PACKAGE          LevelAwardItemType = 100
)

var LevelAwardItemType_name = map[int32]string{
	0:   "LEVEL_AWARD_ITEM_UNSPECIFIED",
	1:   "LEVEL_AWARD_ITEM_HEAD_WEAR",
	2:   "LEVEL_AWARD_ITEM_HORSE",
	3:   "LEVEL_AWARD_ITEM_MEDAL",
	4:   "LEVEL_AWARD_ITEM_DECORATION",
	5:   "LEVEL_AWARD_ITEM_Nameplate",
	6:   "LEVEL_AWARD_ITEM_MIC_OfficialCert",
	7:   "LEVEL_AWARD_ITEM_INFORMATION_CARD",
	100: "LEVEL_AWARD_ITEM_PACKAGE",
}
var LevelAwardItemType_value = map[string]int32{
	"LEVEL_AWARD_ITEM_UNSPECIFIED":      0,
	"LEVEL_AWARD_ITEM_HEAD_WEAR":        1,
	"LEVEL_AWARD_ITEM_HORSE":            2,
	"LEVEL_AWARD_ITEM_MEDAL":            3,
	"LEVEL_AWARD_ITEM_DECORATION":       4,
	"LEVEL_AWARD_ITEM_Nameplate":        5,
	"LEVEL_AWARD_ITEM_MIC_OfficialCert": 6,
	"LEVEL_AWARD_ITEM_INFORMATION_CARD": 7,
	"LEVEL_AWARD_ITEM_PACKAGE":          100,
}

func (x LevelAwardItemType) Enum() *LevelAwardItemType {
	p := new(LevelAwardItemType)
	*p = x
	return p
}
func (x LevelAwardItemType) String() string {
	return proto.EnumName(LevelAwardItemType_name, int32(x))
}
func (x *LevelAwardItemType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(LevelAwardItemType_value, data, "LevelAwardItemType")
	if err != nil {
		return err
	}
	*x = LevelAwardItemType(value)
	return nil
}
func (LevelAwardItemType) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{5} }

type FellowLevelAwardItemType int32

const (
	FellowLevelAwardItemType_FELLOW_LEVEL_AWARD_ITEM_UNSPECIFIED     FellowLevelAwardItemType = 0
	FellowLevelAwardItemType_FELLOW_LEVEL_AWARD_ITEM_Package         FellowLevelAwardItemType = 1
	FellowLevelAwardItemType_FELLOW_LEVEL_AWARD_ITEM_OfficialCert    FellowLevelAwardItemType = 2
	FellowLevelAwardItemType_FELLOW_LEVEL_AWARD_ITEM_Nameplate       FellowLevelAwardItemType = 3
	FellowLevelAwardItemType_FELLOW_LEVEL_AWARD_ITEM_HeadWear        FellowLevelAwardItemType = 4
	FellowLevelAwardItemType_FELLOW_LEVEL_AWARD_ITEM_Horse           FellowLevelAwardItemType = 5
	FellowLevelAwardItemType_FELLOW_LEVEL_AWARD_ITEM_Float           FellowLevelAwardItemType = 6
	FellowLevelAwardItemType_FELLOW_LEVEL_AWARD_ITEM_ChannelInfoCard FellowLevelAwardItemType = 7
)

var FellowLevelAwardItemType_name = map[int32]string{
	0: "FELLOW_LEVEL_AWARD_ITEM_UNSPECIFIED",
	1: "FELLOW_LEVEL_AWARD_ITEM_Package",
	2: "FELLOW_LEVEL_AWARD_ITEM_OfficialCert",
	3: "FELLOW_LEVEL_AWARD_ITEM_Nameplate",
	4: "FELLOW_LEVEL_AWARD_ITEM_HeadWear",
	5: "FELLOW_LEVEL_AWARD_ITEM_Horse",
	6: "FELLOW_LEVEL_AWARD_ITEM_Float",
	7: "FELLOW_LEVEL_AWARD_ITEM_ChannelInfoCard",
}
var FellowLevelAwardItemType_value = map[string]int32{
	"FELLOW_LEVEL_AWARD_ITEM_UNSPECIFIED":     0,
	"FELLOW_LEVEL_AWARD_ITEM_Package":         1,
	"FELLOW_LEVEL_AWARD_ITEM_OfficialCert":    2,
	"FELLOW_LEVEL_AWARD_ITEM_Nameplate":       3,
	"FELLOW_LEVEL_AWARD_ITEM_HeadWear":        4,
	"FELLOW_LEVEL_AWARD_ITEM_Horse":           5,
	"FELLOW_LEVEL_AWARD_ITEM_Float":           6,
	"FELLOW_LEVEL_AWARD_ITEM_ChannelInfoCard": 7,
}

func (x FellowLevelAwardItemType) Enum() *FellowLevelAwardItemType {
	p := new(FellowLevelAwardItemType)
	*p = x
	return p
}
func (x FellowLevelAwardItemType) String() string {
	return proto.EnumName(FellowLevelAwardItemType_name, int32(x))
}
func (x *FellowLevelAwardItemType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FellowLevelAwardItemType_value, data, "FellowLevelAwardItemType")
	if err != nil {
		return err
	}
	*x = FellowLevelAwardItemType(value)
	return nil
}
func (FellowLevelAwardItemType) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{6} }

type XmlMsgDidsplayType int32

const (
	XmlMsgDidsplayType_UNSPECIFIED XmlMsgDidsplayType = 0
	XmlMsgDidsplayType_CENTER      XmlMsgDidsplayType = 1
	XmlMsgDidsplayType_BUBBLE      XmlMsgDidsplayType = 2
	XmlMsgDidsplayType_NORMAL      XmlMsgDidsplayType = 3
)

var XmlMsgDidsplayType_name = map[int32]string{
	0: "UNSPECIFIED",
	1: "CENTER",
	2: "BUBBLE",
	3: "NORMAL",
}
var XmlMsgDidsplayType_value = map[string]int32{
	"UNSPECIFIED": 0,
	"CENTER":      1,
	"BUBBLE":      2,
	"NORMAL":      3,
}

func (x XmlMsgDidsplayType) Enum() *XmlMsgDidsplayType {
	p := new(XmlMsgDidsplayType)
	*p = x
	return p
}
func (x XmlMsgDidsplayType) String() string {
	return proto.EnumName(XmlMsgDidsplayType_name, int32(x))
}
func (x *XmlMsgDidsplayType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(XmlMsgDidsplayType_value, data, "XmlMsgDidsplayType")
	if err != nil {
		return err
	}
	*x = XmlMsgDidsplayType(value)
	return nil
}
func (XmlMsgDidsplayType) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{7} }

type IMXMLPayLoadType int32

const (
	IMXMLPayLoadType_IM_XML_PAYLOAD_TYPE_UNSPECIFIED              IMXMLPayLoadType = 0
	IMXMLPayLoadType_IM_XML_PAYLOAD_TYPE_WEDDING_GROUP_PHOTO_LIST IMXMLPayLoadType = 1
	IMXMLPayLoadType_IM_XML_PAYLOAD_TYPE_WEDDING_CLIP_LIST        IMXMLPayLoadType = 2
)

var IMXMLPayLoadType_name = map[int32]string{
	0: "IM_XML_PAYLOAD_TYPE_UNSPECIFIED",
	1: "IM_XML_PAYLOAD_TYPE_WEDDING_GROUP_PHOTO_LIST",
	2: "IM_XML_PAYLOAD_TYPE_WEDDING_CLIP_LIST",
}
var IMXMLPayLoadType_value = map[string]int32{
	"IM_XML_PAYLOAD_TYPE_UNSPECIFIED":              0,
	"IM_XML_PAYLOAD_TYPE_WEDDING_GROUP_PHOTO_LIST": 1,
	"IM_XML_PAYLOAD_TYPE_WEDDING_CLIP_LIST":        2,
}

func (x IMXMLPayLoadType) Enum() *IMXMLPayLoadType {
	p := new(IMXMLPayLoadType)
	*p = x
	return p
}
func (x IMXMLPayLoadType) String() string {
	return proto.EnumName(IMXMLPayLoadType_name, int32(x))
}
func (x *IMXMLPayLoadType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(IMXMLPayLoadType_value, data, "IMXMLPayLoadType")
	if err != nil {
		return err
	}
	*x = IMXMLPayLoadType(value)
	return nil
}
func (IMXMLPayLoadType) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{8} }

type RichTextMsg_RichTextMsgLevel int32

const (
	RichTextMsg_TIPS  RichTextMsg_RichTextMsgLevel = 0
	RichTextMsg_GUIDE RichTextMsg_RichTextMsgLevel = 1
)

var RichTextMsg_RichTextMsgLevel_name = map[int32]string{
	0: "TIPS",
	1: "GUIDE",
}
var RichTextMsg_RichTextMsgLevel_value = map[string]int32{
	"TIPS":  0,
	"GUIDE": 1,
}

func (x RichTextMsg_RichTextMsgLevel) Enum() *RichTextMsg_RichTextMsgLevel {
	p := new(RichTextMsg_RichTextMsgLevel)
	*p = x
	return p
}
func (x RichTextMsg_RichTextMsgLevel) String() string {
	return proto.EnumName(RichTextMsg_RichTextMsgLevel_name, int32(x))
}
func (x *RichTextMsg_RichTextMsgLevel) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RichTextMsg_RichTextMsgLevel_value, data, "RichTextMsg_RichTextMsgLevel")
	if err != nil {
		return err
	}
	*x = RichTextMsg_RichTextMsgLevel(value)
	return nil
}
func (RichTextMsg_RichTextMsgLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorIm, []int{4, 0}
}

type RichTextMsg_RichTextMsgType int32

const (
	RichTextMsg_NORMAL               RichTextMsg_RichTextMsgType = 0
	RichTextMsg_INTERACTION_INTIMACY RichTextMsg_RichTextMsgType = 1
)

var RichTextMsg_RichTextMsgType_name = map[int32]string{
	0: "NORMAL",
	1: "INTERACTION_INTIMACY",
}
var RichTextMsg_RichTextMsgType_value = map[string]int32{
	"NORMAL":               0,
	"INTERACTION_INTIMACY": 1,
}

func (x RichTextMsg_RichTextMsgType) Enum() *RichTextMsg_RichTextMsgType {
	p := new(RichTextMsg_RichTextMsgType)
	*p = x
	return p
}
func (x RichTextMsg_RichTextMsgType) String() string {
	return proto.EnumName(RichTextMsg_RichTextMsgType_name, int32(x))
}
func (x *RichTextMsg_RichTextMsgType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RichTextMsg_RichTextMsgType_value, data, "RichTextMsg_RichTextMsgType")
	if err != nil {
		return err
	}
	*x = RichTextMsg_RichTextMsgType(value)
	return nil
}
func (RichTextMsg_RichTextMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorIm, []int{4, 1}
}

type PopGameJumpMsg_JumpType int32

const (
	PopGameJumpMsg_DOWNLOAD_GAME PopGameJumpMsg_JumpType = 1
	PopGameJumpMsg_ORDER_GAME    PopGameJumpMsg_JumpType = 2
)

var PopGameJumpMsg_JumpType_name = map[int32]string{
	1: "DOWNLOAD_GAME",
	2: "ORDER_GAME",
}
var PopGameJumpMsg_JumpType_value = map[string]int32{
	"DOWNLOAD_GAME": 1,
	"ORDER_GAME":    2,
}

func (x PopGameJumpMsg_JumpType) Enum() *PopGameJumpMsg_JumpType {
	p := new(PopGameJumpMsg_JumpType)
	*p = x
	return p
}
func (x PopGameJumpMsg_JumpType) String() string {
	return proto.EnumName(PopGameJumpMsg_JumpType_name, int32(x))
}
func (x *PopGameJumpMsg_JumpType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PopGameJumpMsg_JumpType_value, data, "PopGameJumpMsg_JumpType")
	if err != nil {
		return err
	}
	*x = PopGameJumpMsg_JumpType(value)
	return nil
}
func (PopGameJumpMsg_JumpType) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{39, 0} }

type EmojiMsg_EmojiType int32

const (
	// 默认
	EmojiMsg_EMMOJI_TYPE_UNSPECIFIED EmojiMsg_EmojiType = 0
	// 推荐表情包
	EmojiMsg_EMOJI_TYPE_RECOMMEND EmojiMsg_EmojiType = 1
	// 自动联想表情包
	EmojiMsg_EMOJI_TYPE_KEYWORD EmojiMsg_EmojiType = 2
)

var EmojiMsg_EmojiType_name = map[int32]string{
	0: "EMMOJI_TYPE_UNSPECIFIED",
	1: "EMOJI_TYPE_RECOMMEND",
	2: "EMOJI_TYPE_KEYWORD",
}
var EmojiMsg_EmojiType_value = map[string]int32{
	"EMMOJI_TYPE_UNSPECIFIED": 0,
	"EMOJI_TYPE_RECOMMEND":    1,
	"EMOJI_TYPE_KEYWORD":      2,
}

func (x EmojiMsg_EmojiType) Enum() *EmojiMsg_EmojiType {
	p := new(EmojiMsg_EmojiType)
	*p = x
	return p
}
func (x EmojiMsg_EmojiType) String() string {
	return proto.EnumName(EmojiMsg_EmojiType_name, int32(x))
}
func (x *EmojiMsg_EmojiType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EmojiMsg_EmojiType_value, data, "EmojiMsg_EmojiType")
	if err != nil {
		return err
	}
	*x = EmojiMsg_EmojiType(value)
	return nil
}
func (EmojiMsg_EmojiType) EnumDescriptor() ([]byte, []int) { return fileDescriptorIm, []int{40, 0} }

type InteractiveUnreadMsg_InteractiveType int32

const (
	InteractiveUnreadMsg_NONE         InteractiveUnreadMsg_InteractiveType = 0
	InteractiveUnreadMsg_NEW_ATTITUDE InteractiveUnreadMsg_InteractiveType = 1
	InteractiveUnreadMsg_NEW_COMMENT  InteractiveUnreadMsg_InteractiveType = 2
)

var InteractiveUnreadMsg_InteractiveType_name = map[int32]string{
	0: "NONE",
	1: "NEW_ATTITUDE",
	2: "NEW_COMMENT",
}
var InteractiveUnreadMsg_InteractiveType_value = map[string]int32{
	"NONE":         0,
	"NEW_ATTITUDE": 1,
	"NEW_COMMENT":  2,
}

func (x InteractiveUnreadMsg_InteractiveType) Enum() *InteractiveUnreadMsg_InteractiveType {
	p := new(InteractiveUnreadMsg_InteractiveType)
	*p = x
	return p
}
func (x InteractiveUnreadMsg_InteractiveType) String() string {
	return proto.EnumName(InteractiveUnreadMsg_InteractiveType_name, int32(x))
}
func (x *InteractiveUnreadMsg_InteractiveType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(InteractiveUnreadMsg_InteractiveType_value, data, "InteractiveUnreadMsg_InteractiveType")
	if err != nil {
		return err
	}
	*x = InteractiveUnreadMsg_InteractiveType(value)
	return nil
}
func (InteractiveUnreadMsg_InteractiveType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorIm, []int{41, 0}
}

type RichTextWithPresentMsg_IM_PRESENT_TYPE int32

const (
	RichTextWithPresentMsg_NORMAL RichTextWithPresentMsg_IM_PRESENT_TYPE = 0
	RichTextWithPresentMsg_FELLOW RichTextWithPresentMsg_IM_PRESENT_TYPE = 1
)

var RichTextWithPresentMsg_IM_PRESENT_TYPE_name = map[int32]string{
	0: "NORMAL",
	1: "FELLOW",
}
var RichTextWithPresentMsg_IM_PRESENT_TYPE_value = map[string]int32{
	"NORMAL": 0,
	"FELLOW": 1,
}

func (x RichTextWithPresentMsg_IM_PRESENT_TYPE) Enum() *RichTextWithPresentMsg_IM_PRESENT_TYPE {
	p := new(RichTextWithPresentMsg_IM_PRESENT_TYPE)
	*p = x
	return p
}
func (x RichTextWithPresentMsg_IM_PRESENT_TYPE) String() string {
	return proto.EnumName(RichTextWithPresentMsg_IM_PRESENT_TYPE_name, int32(x))
}
func (x *RichTextWithPresentMsg_IM_PRESENT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RichTextWithPresentMsg_IM_PRESENT_TYPE_value, data, "RichTextWithPresentMsg_IM_PRESENT_TYPE")
	if err != nil {
		return err
	}
	*x = RichTextWithPresentMsg_IM_PRESENT_TYPE(value)
	return nil
}
func (RichTextWithPresentMsg_IM_PRESENT_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorIm, []int{43, 0}
}

type RichTextWords struct {
	Text string `protobuf:"bytes,1,req,name=text" json:"text"`
}

func (m *RichTextWords) Reset()                    { *m = RichTextWords{} }
func (m *RichTextWords) String() string            { return proto.CompactTextString(m) }
func (*RichTextWords) ProtoMessage()               {}
func (*RichTextWords) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{0} }

func (m *RichTextWords) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

type RichTextImage struct {
	Url    string  `protobuf:"bytes,1,req,name=url" json:"url"`
	Width  float32 `protobuf:"fixed32,2,opt,name=width" json:"width"`
	Height float32 `protobuf:"fixed32,3,opt,name=height" json:"height"`
}

func (m *RichTextImage) Reset()                    { *m = RichTextImage{} }
func (m *RichTextImage) String() string            { return proto.CompactTextString(m) }
func (*RichTextImage) ProtoMessage()               {}
func (*RichTextImage) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{1} }

func (m *RichTextImage) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *RichTextImage) GetWidth() float32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *RichTextImage) GetHeight() float32 {
	if m != nil {
		return m.Height
	}
	return 0
}

type RichTextLink struct {
	Text             string `protobuf:"bytes,1,req,name=text" json:"text"`
	TextColor        int32  `protobuf:"varint,2,opt,name=text_color,json=textColor" json:"text_color"`
	JumpUrl          string `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl" json:"jump_url"`
	JumpUrlPbContent []byte `protobuf:"bytes,4,opt,name=jump_url_pb_content,json=jumpUrlPbContent" json:"jump_url_pb_content"`
}

func (m *RichTextLink) Reset()                    { *m = RichTextLink{} }
func (m *RichTextLink) String() string            { return proto.CompactTextString(m) }
func (*RichTextLink) ProtoMessage()               {}
func (*RichTextLink) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{2} }

func (m *RichTextLink) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *RichTextLink) GetTextColor() int32 {
	if m != nil {
		return m.TextColor
	}
	return 0
}

func (m *RichTextLink) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *RichTextLink) GetJumpUrlPbContent() []byte {
	if m != nil {
		return m.JumpUrlPbContent
	}
	return nil
}

type RichTextElement struct {
	// Types that are valid to be assigned to Content:
	//	*RichTextElement_Words
	//	*RichTextElement_Image
	//	*RichTextElement_Link
	Content isRichTextElement_Content `protobuf_oneof:"content"`
}

func (m *RichTextElement) Reset()                    { *m = RichTextElement{} }
func (m *RichTextElement) String() string            { return proto.CompactTextString(m) }
func (*RichTextElement) ProtoMessage()               {}
func (*RichTextElement) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{3} }

type isRichTextElement_Content interface {
	isRichTextElement_Content()
	MarshalTo([]byte) (int, error)
	Size() int
}

type RichTextElement_Words struct {
	Words *RichTextWords `protobuf:"bytes,1,opt,name=words,oneof"`
}
type RichTextElement_Image struct {
	Image *RichTextImage `protobuf:"bytes,2,opt,name=image,oneof"`
}
type RichTextElement_Link struct {
	Link *RichTextLink `protobuf:"bytes,3,opt,name=link,oneof"`
}

func (*RichTextElement_Words) isRichTextElement_Content() {}
func (*RichTextElement_Image) isRichTextElement_Content() {}
func (*RichTextElement_Link) isRichTextElement_Content()  {}

func (m *RichTextElement) GetContent() isRichTextElement_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *RichTextElement) GetWords() *RichTextWords {
	if x, ok := m.GetContent().(*RichTextElement_Words); ok {
		return x.Words
	}
	return nil
}

func (m *RichTextElement) GetImage() *RichTextImage {
	if x, ok := m.GetContent().(*RichTextElement_Image); ok {
		return x.Image
	}
	return nil
}

func (m *RichTextElement) GetLink() *RichTextLink {
	if x, ok := m.GetContent().(*RichTextElement_Link); ok {
		return x.Link
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*RichTextElement) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _RichTextElement_OneofMarshaler, _RichTextElement_OneofUnmarshaler, _RichTextElement_OneofSizer, []interface{}{
		(*RichTextElement_Words)(nil),
		(*RichTextElement_Image)(nil),
		(*RichTextElement_Link)(nil),
	}
}

func _RichTextElement_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*RichTextElement)
	// content
	switch x := m.Content.(type) {
	case *RichTextElement_Words:
		_ = b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Words); err != nil {
			return err
		}
	case *RichTextElement_Image:
		_ = b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Image); err != nil {
			return err
		}
	case *RichTextElement_Link:
		_ = b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Link); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("RichTextElement.Content has unexpected type %T", x)
	}
	return nil
}

func _RichTextElement_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*RichTextElement)
	switch tag {
	case 1: // content.words
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(RichTextWords)
		err := b.DecodeMessage(msg)
		m.Content = &RichTextElement_Words{msg}
		return true, err
	case 2: // content.image
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(RichTextImage)
		err := b.DecodeMessage(msg)
		m.Content = &RichTextElement_Image{msg}
		return true, err
	case 3: // content.link
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(RichTextLink)
		err := b.DecodeMessage(msg)
		m.Content = &RichTextElement_Link{msg}
		return true, err
	default:
		return false, nil
	}
}

func _RichTextElement_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*RichTextElement)
	// content
	switch x := m.Content.(type) {
	case *RichTextElement_Words:
		s := proto.Size(x.Words)
		n += proto.SizeVarint(1<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *RichTextElement_Image:
		s := proto.Size(x.Image)
		n += proto.SizeVarint(2<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *RichTextElement_Link:
		s := proto.Size(x.Link)
		n += proto.SizeVarint(3<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type RichTextMsg struct {
	InValue  []*RichTextElement           `protobuf:"bytes,1,rep,name=in_value,json=inValue" json:"in_value,omitempty"`
	OutValue []*RichTextElement           `protobuf:"bytes,2,rep,name=out_value,json=outValue" json:"out_value,omitempty"`
	Level    RichTextMsg_RichTextMsgLevel `protobuf:"varint,3,opt,name=level,enum=ga.im.RichTextMsg_RichTextMsgLevel" json:"level"`
	Type     RichTextMsg_RichTextMsgType  `protobuf:"varint,4,opt,name=type,enum=ga.im.RichTextMsg_RichTextMsgType" json:"type"`
}

func (m *RichTextMsg) Reset()                    { *m = RichTextMsg{} }
func (m *RichTextMsg) String() string            { return proto.CompactTextString(m) }
func (*RichTextMsg) ProtoMessage()               {}
func (*RichTextMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{4} }

func (m *RichTextMsg) GetInValue() []*RichTextElement {
	if m != nil {
		return m.InValue
	}
	return nil
}

func (m *RichTextMsg) GetOutValue() []*RichTextElement {
	if m != nil {
		return m.OutValue
	}
	return nil
}

func (m *RichTextMsg) GetLevel() RichTextMsg_RichTextMsgLevel {
	if m != nil {
		return m.Level
	}
	return RichTextMsg_TIPS
}

func (m *RichTextMsg) GetType() RichTextMsg_RichTextMsgType {
	if m != nil {
		return m.Type
	}
	return RichTextMsg_NORMAL
}

type PersonalRichTextElement struct {
	Account []string     `protobuf:"bytes,1,rep,name=account" json:"account,omitempty"`
	Msg     *RichTextMsg `protobuf:"bytes,2,req,name=msg" json:"msg,omitempty"`
}

func (m *PersonalRichTextElement) Reset()                    { *m = PersonalRichTextElement{} }
func (m *PersonalRichTextElement) String() string            { return proto.CompactTextString(m) }
func (*PersonalRichTextElement) ProtoMessage()               {}
func (*PersonalRichTextElement) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{5} }

func (m *PersonalRichTextElement) GetAccount() []string {
	if m != nil {
		return m.Account
	}
	return nil
}

func (m *PersonalRichTextElement) GetMsg() *RichTextMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type PersonalRichTextMsg struct {
	Personal []*PersonalRichTextElement `protobuf:"bytes,1,rep,name=personal" json:"personal,omitempty"`
	Default  *RichTextMsg               `protobuf:"bytes,2,opt,name=default" json:"default,omitempty"`
}

func (m *PersonalRichTextMsg) Reset()                    { *m = PersonalRichTextMsg{} }
func (m *PersonalRichTextMsg) String() string            { return proto.CompactTextString(m) }
func (*PersonalRichTextMsg) ProtoMessage()               {}
func (*PersonalRichTextMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{6} }

func (m *PersonalRichTextMsg) GetPersonal() []*PersonalRichTextElement {
	if m != nil {
		return m.Personal
	}
	return nil
}

func (m *PersonalRichTextMsg) GetDefault() *RichTextMsg {
	if m != nil {
		return m.Default
	}
	return nil
}

type LinkJumpURL struct {
	JumpUrlMap map[string]string `protobuf:"bytes,1,rep,name=jump_url_map,json=jumpUrlMap" json:"jump_url_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
}

func (m *LinkJumpURL) Reset()                    { *m = LinkJumpURL{} }
func (m *LinkJumpURL) String() string            { return proto.CompactTextString(m) }
func (*LinkJumpURL) ProtoMessage()               {}
func (*LinkJumpURL) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{7} }

func (m *LinkJumpURL) GetJumpUrlMap() map[string]string {
	if m != nil {
		return m.JumpUrlMap
	}
	return nil
}

// 1v1实时语音--房间创建
type SessionCreateMsg struct {
	SessionId uint32 `protobuf:"varint,1,req,name=session_id,json=sessionId" json:"session_id"`
}

func (m *SessionCreateMsg) Reset()                    { *m = SessionCreateMsg{} }
func (m *SessionCreateMsg) String() string            { return proto.CompactTextString(m) }
func (*SessionCreateMsg) ProtoMessage()               {}
func (*SessionCreateMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{8} }

func (m *SessionCreateMsg) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

// 1V1实时语音--房间关闭
type SessionCloseMsg struct {
	SessionId uint32 `protobuf:"varint,1,req,name=session_id,json=sessionId" json:"session_id"`
}

func (m *SessionCloseMsg) Reset()                    { *m = SessionCloseMsg{} }
func (m *SessionCloseMsg) String() string            { return proto.CompactTextString(m) }
func (*SessionCloseMsg) ProtoMessage()               {}
func (*SessionCloseMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{9} }

func (m *SessionCloseMsg) GetSessionId() uint32 {
	if m != nil {
		return m.SessionId
	}
	return 0
}

// Cue消息客户端透传的消息结构 服务端透明
type CueMsg struct {
	CueId       uint32 `protobuf:"varint,1,req,name=cue_id,json=cueId" json:"cue_id"`
	MsgPicLeft  string `protobuf:"bytes,2,req,name=msg_pic_left,json=msgPicLeft" json:"msg_pic_left"`
	MsgPicRight string `protobuf:"bytes,3,req,name=msg_pic_right,json=msgPicRight" json:"msg_pic_right"`
	PreviewText string `protobuf:"bytes,4,req,name=preview_text,json=previewText" json:"preview_text"`
	LottieUrl   string `protobuf:"bytes,5,req,name=lottie_url,json=lottieUrl" json:"lottie_url"`
}

func (m *CueMsg) Reset()                    { *m = CueMsg{} }
func (m *CueMsg) String() string            { return proto.CompactTextString(m) }
func (*CueMsg) ProtoMessage()               {}
func (*CueMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{10} }

func (m *CueMsg) GetCueId() uint32 {
	if m != nil {
		return m.CueId
	}
	return 0
}

func (m *CueMsg) GetMsgPicLeft() string {
	if m != nil {
		return m.MsgPicLeft
	}
	return ""
}

func (m *CueMsg) GetMsgPicRight() string {
	if m != nil {
		return m.MsgPicRight
	}
	return ""
}

func (m *CueMsg) GetPreviewText() string {
	if m != nil {
		return m.PreviewText
	}
	return ""
}

func (m *CueMsg) GetLottieUrl() string {
	if m != nil {
		return m.LottieUrl
	}
	return ""
}

// 文本消息扩展
type TextMsgExt struct {
	DressId uint32 `protobuf:"varint,1,opt,name=dress_id,json=dressId" json:"dress_id"`
}

func (m *TextMsgExt) Reset()                    { *m = TextMsgExt{} }
func (m *TextMsgExt) String() string            { return proto.CompactTextString(m) }
func (*TextMsgExt) ProtoMessage()               {}
func (*TextMsgExt) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{11} }

func (m *TextMsgExt) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

// 声音消息扩展
type VoiceMsgExt struct {
	DressId uint32 `protobuf:"varint,1,opt,name=dress_id,json=dressId" json:"dress_id"`
}

func (m *VoiceMsgExt) Reset()                    { *m = VoiceMsgExt{} }
func (m *VoiceMsgExt) String() string            { return proto.CompactTextString(m) }
func (*VoiceMsgExt) ProtoMessage()               {}
func (*VoiceMsgExt) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{12} }

func (m *VoiceMsgExt) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

// 发送消息
type SendMsgReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetName     string      `protobuf:"bytes,2,req,name=target_name,json=targetName" json:"target_name"`
	Type           uint32      `protobuf:"varint,3,req,name=type" json:"type"`
	Content        string      `protobuf:"bytes,4,req,name=content" json:"content"`
	ClientMsgId    uint32      `protobuf:"varint,5,req,name=client_msg_id,json=clientMsgId" json:"client_msg_id"`
	ClientMsgTime  uint32      `protobuf:"varint,6,req,name=client_msg_time,json=clientMsgTime" json:"client_msg_time"`
	Thumb          []byte      `protobuf:"bytes,7,opt,name=thumb" json:"thumb"`
	HasAttachment  bool        `protobuf:"varint,8,req,name=has_attachment,json=hasAttachment" json:"has_attachment"`
	MyLoginKey     string      `protobuf:"bytes,9,req,name=my_login_key,json=myLoginKey" json:"my_login_key"`
	Origin         uint32      `protobuf:"varint,10,opt,name=origin" json:"origin"`
	Ext            []byte      `protobuf:"bytes,11,opt,name=ext" json:"ext"`
	MsgSourceType  uint32      `protobuf:"varint,12,opt,name=msg_source_type,json=msgSourceType" json:"msg_source_type"`
	DoNotCheckText bool        `protobuf:"varint,13,opt,name=do_not_check_text,json=doNotCheckText" json:"do_not_check_text"`
	MsgLabel       uint32      `protobuf:"varint,14,opt,name=msg_label,json=msgLabel" json:"msg_label"`
}

func (m *SendMsgReq) Reset()                    { *m = SendMsgReq{} }
func (m *SendMsgReq) String() string            { return proto.CompactTextString(m) }
func (*SendMsgReq) ProtoMessage()               {}
func (*SendMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{13} }

func (m *SendMsgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendMsgReq) GetTargetName() string {
	if m != nil {
		return m.TargetName
	}
	return ""
}

func (m *SendMsgReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SendMsgReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *SendMsgReq) GetClientMsgId() uint32 {
	if m != nil {
		return m.ClientMsgId
	}
	return 0
}

func (m *SendMsgReq) GetClientMsgTime() uint32 {
	if m != nil {
		return m.ClientMsgTime
	}
	return 0
}

func (m *SendMsgReq) GetThumb() []byte {
	if m != nil {
		return m.Thumb
	}
	return nil
}

func (m *SendMsgReq) GetHasAttachment() bool {
	if m != nil {
		return m.HasAttachment
	}
	return false
}

func (m *SendMsgReq) GetMyLoginKey() string {
	if m != nil {
		return m.MyLoginKey
	}
	return ""
}

func (m *SendMsgReq) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *SendMsgReq) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *SendMsgReq) GetMsgSourceType() uint32 {
	if m != nil {
		return m.MsgSourceType
	}
	return 0
}

func (m *SendMsgReq) GetDoNotCheckText() bool {
	if m != nil {
		return m.DoNotCheckText
	}
	return false
}

func (m *SendMsgReq) GetMsgLabel() uint32 {
	if m != nil {
		return m.MsgLabel
	}
	return 0
}

type SendMsgResp struct {
	BaseResp      *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ClientMsgId   uint32       `protobuf:"varint,2,req,name=client_msg_id,json=clientMsgId" json:"client_msg_id"`
	SvrMsgId      uint32       `protobuf:"varint,3,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
	SvrMsgTime    uint32       `protobuf:"varint,4,req,name=svr_msg_time,json=svrMsgTime" json:"svr_msg_time"`
	AttachmentKey string       `protobuf:"bytes,5,opt,name=attachment_key,json=attachmentKey" json:"attachment_key"`
	TargetName    string       `protobuf:"bytes,6,req,name=target_name,json=targetName" json:"target_name"`
	MyLoginKey    string       `protobuf:"bytes,7,req,name=my_login_key,json=myLoginKey" json:"my_login_key"`
	ExceedTime    uint32       `protobuf:"varint,8,opt,name=exceed_time,json=exceedTime" json:"exceed_time"`
	Origin        uint32       `protobuf:"varint,9,opt,name=origin" json:"origin"`
	TargetMsgId   uint32       `protobuf:"varint,10,opt,name=target_msg_id,json=targetMsgId" json:"target_msg_id"`
	TargetUid     uint32       `protobuf:"varint,11,opt,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *SendMsgResp) Reset()                    { *m = SendMsgResp{} }
func (m *SendMsgResp) String() string            { return proto.CompactTextString(m) }
func (*SendMsgResp) ProtoMessage()               {}
func (*SendMsgResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{14} }

func (m *SendMsgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendMsgResp) GetClientMsgId() uint32 {
	if m != nil {
		return m.ClientMsgId
	}
	return 0
}

func (m *SendMsgResp) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *SendMsgResp) GetSvrMsgTime() uint32 {
	if m != nil {
		return m.SvrMsgTime
	}
	return 0
}

func (m *SendMsgResp) GetAttachmentKey() string {
	if m != nil {
		return m.AttachmentKey
	}
	return ""
}

func (m *SendMsgResp) GetTargetName() string {
	if m != nil {
		return m.TargetName
	}
	return ""
}

func (m *SendMsgResp) GetMyLoginKey() string {
	if m != nil {
		return m.MyLoginKey
	}
	return ""
}

func (m *SendMsgResp) GetExceedTime() uint32 {
	if m != nil {
		return m.ExceedTime
	}
	return 0
}

func (m *SendMsgResp) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *SendMsgResp) GetTargetMsgId() uint32 {
	if m != nil {
		return m.TargetMsgId
	}
	return 0
}

func (m *SendMsgResp) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

// 上传附件
type UploadAttachmentReq struct {
	BaseReq            *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	SvrMsgId           uint32      `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
	AttachmentKey      string      `protobuf:"bytes,3,req,name=attachment_key,json=attachmentKey" json:"attachment_key"`
	Attachment         []byte      `protobuf:"bytes,4,req,name=attachment" json:"attachment"`
	AttachmentProperty []byte      `protobuf:"bytes,5,req,name=attachment_property,json=attachmentProperty" json:"attachment_property"`
}

func (m *UploadAttachmentReq) Reset()                    { *m = UploadAttachmentReq{} }
func (m *UploadAttachmentReq) String() string            { return proto.CompactTextString(m) }
func (*UploadAttachmentReq) ProtoMessage()               {}
func (*UploadAttachmentReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{15} }

func (m *UploadAttachmentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UploadAttachmentReq) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *UploadAttachmentReq) GetAttachmentKey() string {
	if m != nil {
		return m.AttachmentKey
	}
	return ""
}

func (m *UploadAttachmentReq) GetAttachment() []byte {
	if m != nil {
		return m.Attachment
	}
	return nil
}

func (m *UploadAttachmentReq) GetAttachmentProperty() []byte {
	if m != nil {
		return m.AttachmentProperty
	}
	return nil
}

type UploadAttachmentResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SvrMsgId uint32       `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
}

func (m *UploadAttachmentResp) Reset()                    { *m = UploadAttachmentResp{} }
func (m *UploadAttachmentResp) String() string            { return proto.CompactTextString(m) }
func (*UploadAttachmentResp) ProtoMessage()               {}
func (*UploadAttachmentResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{16} }

func (m *UploadAttachmentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UploadAttachmentResp) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

type DownloadAttachmentReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	SvrMsgId      uint32      `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
	AttachmentKey string      `protobuf:"bytes,3,req,name=attachment_key,json=attachmentKey" json:"attachment_key"`
}

func (m *DownloadAttachmentReq) Reset()                    { *m = DownloadAttachmentReq{} }
func (m *DownloadAttachmentReq) String() string            { return proto.CompactTextString(m) }
func (*DownloadAttachmentReq) ProtoMessage()               {}
func (*DownloadAttachmentReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{17} }

func (m *DownloadAttachmentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DownloadAttachmentReq) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *DownloadAttachmentReq) GetAttachmentKey() string {
	if m != nil {
		return m.AttachmentKey
	}
	return ""
}

type DownloadAttachmentResp struct {
	BaseResp      *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SvrMsgId      uint32       `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
	AttachmentKey string       `protobuf:"bytes,3,req,name=attachment_key,json=attachmentKey" json:"attachment_key"`
	Attachment    []byte       `protobuf:"bytes,4,req,name=attachment" json:"attachment"`
	TargetAccount string       `protobuf:"bytes,5,req,name=target_account,json=targetAccount" json:"target_account"`
}

func (m *DownloadAttachmentResp) Reset()                    { *m = DownloadAttachmentResp{} }
func (m *DownloadAttachmentResp) String() string            { return proto.CompactTextString(m) }
func (*DownloadAttachmentResp) ProtoMessage()               {}
func (*DownloadAttachmentResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{18} }

func (m *DownloadAttachmentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *DownloadAttachmentResp) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *DownloadAttachmentResp) GetAttachmentKey() string {
	if m != nil {
		return m.AttachmentKey
	}
	return ""
}

func (m *DownloadAttachmentResp) GetAttachment() []byte {
	if m != nil {
		return m.Attachment
	}
	return nil
}

func (m *DownloadAttachmentResp) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

// 删除消息
type DeleteMessageReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	MsgId   uint32      `protobuf:"varint,2,req,name=msg_id,json=msgId" json:"msg_id"`
}

func (m *DeleteMessageReq) Reset()                    { *m = DeleteMessageReq{} }
func (m *DeleteMessageReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteMessageReq) ProtoMessage()               {}
func (*DeleteMessageReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{19} }

func (m *DeleteMessageReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DeleteMessageReq) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

type DeleteMessageResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *DeleteMessageResp) Reset()                    { *m = DeleteMessageResp{} }
func (m *DeleteMessageResp) String() string            { return proto.CompactTextString(m) }
func (*DeleteMessageResp) ProtoMessage()               {}
func (*DeleteMessageResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{20} }

func (m *DeleteMessageResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 批量设置已读
type MarkMsgReadReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetName   string      `protobuf:"bytes,2,req,name=target_name,json=targetName" json:"target_name"`
	SvrMsgId     uint32      `protobuf:"varint,3,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
	PeerSvrMsgId uint32      `protobuf:"varint,4,opt,name=peer_svr_msg_id,json=peerSvrMsgId" json:"peer_svr_msg_id"`
}

func (m *MarkMsgReadReq) Reset()                    { *m = MarkMsgReadReq{} }
func (m *MarkMsgReadReq) String() string            { return proto.CompactTextString(m) }
func (*MarkMsgReadReq) ProtoMessage()               {}
func (*MarkMsgReadReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{21} }

func (m *MarkMsgReadReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MarkMsgReadReq) GetTargetName() string {
	if m != nil {
		return m.TargetName
	}
	return ""
}

func (m *MarkMsgReadReq) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *MarkMsgReadReq) GetPeerSvrMsgId() uint32 {
	if m != nil {
		return m.PeerSvrMsgId
	}
	return 0
}

type MarkMsgReadResp struct {
	BaseResp         *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TargetName       string       `protobuf:"bytes,2,req,name=target_name,json=targetName" json:"target_name"`
	SvrMsgId         uint32       `protobuf:"varint,3,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
	PeerReadSvrMsgId uint32       `protobuf:"varint,4,opt,name=peer_read_svr_msg_id,json=peerReadSvrMsgId" json:"peer_read_svr_msg_id"`
}

func (m *MarkMsgReadResp) Reset()                    { *m = MarkMsgReadResp{} }
func (m *MarkMsgReadResp) String() string            { return proto.CompactTextString(m) }
func (*MarkMsgReadResp) ProtoMessage()               {}
func (*MarkMsgReadResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{22} }

func (m *MarkMsgReadResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MarkMsgReadResp) GetTargetName() string {
	if m != nil {
		return m.TargetName
	}
	return ""
}

func (m *MarkMsgReadResp) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *MarkMsgReadResp) GetPeerReadSvrMsgId() uint32 {
	if m != nil {
		return m.PeerReadSvrMsgId
	}
	return 0
}

type UpdateMsgSettingReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Account        string      `protobuf:"bytes,2,req,name=account" json:"account"`
	ReceiveSetting uint32      `protobuf:"varint,3,req,name=receive_setting,json=receiveSetting" json:"receive_setting"`
}

func (m *UpdateMsgSettingReq) Reset()                    { *m = UpdateMsgSettingReq{} }
func (m *UpdateMsgSettingReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateMsgSettingReq) ProtoMessage()               {}
func (*UpdateMsgSettingReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{23} }

func (m *UpdateMsgSettingReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdateMsgSettingReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UpdateMsgSettingReq) GetReceiveSetting() uint32 {
	if m != nil {
		return m.ReceiveSetting
	}
	return 0
}

type UpdateMsgSettingResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *UpdateMsgSettingResp) Reset()                    { *m = UpdateMsgSettingResp{} }
func (m *UpdateMsgSettingResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateMsgSettingResp) ProtoMessage()               {}
func (*UpdateMsgSettingResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{24} }

func (m *UpdateMsgSettingResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type QueryMsgSettingReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Account string      `protobuf:"bytes,2,opt,name=account" json:"account"`
}

func (m *QueryMsgSettingReq) Reset()                    { *m = QueryMsgSettingReq{} }
func (m *QueryMsgSettingReq) String() string            { return proto.CompactTextString(m) }
func (*QueryMsgSettingReq) ProtoMessage()               {}
func (*QueryMsgSettingReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{25} }

func (m *QueryMsgSettingReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *QueryMsgSettingReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type MsgReceiveSettingItem struct {
	Account        string `protobuf:"bytes,1,req,name=account" json:"account"`
	ReceiveSetting uint32 `protobuf:"varint,2,req,name=receive_setting,json=receiveSetting" json:"receive_setting"`
}

func (m *MsgReceiveSettingItem) Reset()                    { *m = MsgReceiveSettingItem{} }
func (m *MsgReceiveSettingItem) String() string            { return proto.CompactTextString(m) }
func (*MsgReceiveSettingItem) ProtoMessage()               {}
func (*MsgReceiveSettingItem) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{26} }

func (m *MsgReceiveSettingItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MsgReceiveSettingItem) GetReceiveSetting() uint32 {
	if m != nil {
		return m.ReceiveSetting
	}
	return 0
}

type QueryMsgSettingResp struct {
	BaseResp        *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SettingItemList []*MsgReceiveSettingItem `protobuf:"bytes,2,rep,name=setting_item_list,json=settingItemList" json:"setting_item_list,omitempty"`
}

func (m *QueryMsgSettingResp) Reset()                    { *m = QueryMsgSettingResp{} }
func (m *QueryMsgSettingResp) String() string            { return proto.CompactTextString(m) }
func (*QueryMsgSettingResp) ProtoMessage()               {}
func (*QueryMsgSettingResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{27} }

func (m *QueryMsgSettingResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *QueryMsgSettingResp) GetSettingItemList() []*MsgReceiveSettingItem {
	if m != nil {
		return m.SettingItemList
	}
	return nil
}

// 检查用户是否有AT所有人的权限和剩余次数
type CheckSendAtEveryoneGroupMsgReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupId uint32      `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *CheckSendAtEveryoneGroupMsgReq) Reset()         { *m = CheckSendAtEveryoneGroupMsgReq{} }
func (m *CheckSendAtEveryoneGroupMsgReq) String() string { return proto.CompactTextString(m) }
func (*CheckSendAtEveryoneGroupMsgReq) ProtoMessage()    {}
func (*CheckSendAtEveryoneGroupMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorIm, []int{28}
}

func (m *CheckSendAtEveryoneGroupMsgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckSendAtEveryoneGroupMsgReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type CheckSendAtEveryoneGroupMsgResp struct {
	BaseResp          *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RemainCnt         uint32       `protobuf:"varint,2,req,name=remain_cnt,json=remainCnt" json:"remain_cnt"`
	IsAllowAtEveryone bool         `protobuf:"varint,3,req,name=is_allow_at_everyone,json=isAllowAtEveryone" json:"is_allow_at_everyone"`
}

func (m *CheckSendAtEveryoneGroupMsgResp) Reset()         { *m = CheckSendAtEveryoneGroupMsgResp{} }
func (m *CheckSendAtEveryoneGroupMsgResp) String() string { return proto.CompactTextString(m) }
func (*CheckSendAtEveryoneGroupMsgResp) ProtoMessage()    {}
func (*CheckSendAtEveryoneGroupMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorIm, []int{29}
}

func (m *CheckSendAtEveryoneGroupMsgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckSendAtEveryoneGroupMsgResp) GetRemainCnt() uint32 {
	if m != nil {
		return m.RemainCnt
	}
	return 0
}

func (m *CheckSendAtEveryoneGroupMsgResp) GetIsAllowAtEveryone() bool {
	if m != nil {
		return m.IsAllowAtEveryone
	}
	return false
}

type CancelMsgReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetName    string      `protobuf:"bytes,2,req,name=target_name,json=targetName" json:"target_name"`
	SvrMsgId      uint32      `protobuf:"varint,3,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
	TargetMsgId   uint32      `protobuf:"varint,4,req,name=target_msg_id,json=targetMsgId" json:"target_msg_id"`
	ClientMsgId   uint32      `protobuf:"varint,5,req,name=client_msg_id,json=clientMsgId" json:"client_msg_id"`
	ClientMsgTime uint32      `protobuf:"varint,6,req,name=client_msg_time,json=clientMsgTime" json:"client_msg_time"`
	MyLoginKey    string      `protobuf:"bytes,7,req,name=my_login_key,json=myLoginKey" json:"my_login_key"`
}

func (m *CancelMsgReq) Reset()                    { *m = CancelMsgReq{} }
func (m *CancelMsgReq) String() string            { return proto.CompactTextString(m) }
func (*CancelMsgReq) ProtoMessage()               {}
func (*CancelMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{30} }

func (m *CancelMsgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelMsgReq) GetTargetName() string {
	if m != nil {
		return m.TargetName
	}
	return ""
}

func (m *CancelMsgReq) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *CancelMsgReq) GetTargetMsgId() uint32 {
	if m != nil {
		return m.TargetMsgId
	}
	return 0
}

func (m *CancelMsgReq) GetClientMsgId() uint32 {
	if m != nil {
		return m.ClientMsgId
	}
	return 0
}

func (m *CancelMsgReq) GetClientMsgTime() uint32 {
	if m != nil {
		return m.ClientMsgTime
	}
	return 0
}

func (m *CancelMsgReq) GetMyLoginKey() string {
	if m != nil {
		return m.MyLoginKey
	}
	return ""
}

type CancelMsgResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *CancelMsgResp) Reset()                    { *m = CancelMsgResp{} }
func (m *CancelMsgResp) String() string            { return proto.CompactTextString(m) }
func (*CancelMsgResp) ProtoMessage()               {}
func (*CancelMsgResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{31} }

func (m *CancelMsgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 消息已读推送
type MessageReadByPeerMessage struct {
	PeerName string `protobuf:"bytes,1,req,name=peer_name,json=peerName" json:"peer_name"`
	SvrMsgId uint32 `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
}

func (m *MessageReadByPeerMessage) Reset()                    { *m = MessageReadByPeerMessage{} }
func (m *MessageReadByPeerMessage) String() string            { return proto.CompactTextString(m) }
func (*MessageReadByPeerMessage) ProtoMessage()               {}
func (*MessageReadByPeerMessage) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{32} }

func (m *MessageReadByPeerMessage) GetPeerName() string {
	if m != nil {
		return m.PeerName
	}
	return ""
}

func (m *MessageReadByPeerMessage) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

type GetMessagePeerReadStatusReq struct {
	BaseReq     *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	AccountList []string    `protobuf:"bytes,2,rep,name=account_list,json=accountList" json:"account_list,omitempty"`
}

func (m *GetMessagePeerReadStatusReq) Reset()                    { *m = GetMessagePeerReadStatusReq{} }
func (m *GetMessagePeerReadStatusReq) String() string            { return proto.CompactTextString(m) }
func (*GetMessagePeerReadStatusReq) ProtoMessage()               {}
func (*GetMessagePeerReadStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{33} }

func (m *GetMessagePeerReadStatusReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMessagePeerReadStatusReq) GetAccountList() []string {
	if m != nil {
		return m.AccountList
	}
	return nil
}

type MessagePeerReadStatus struct {
	Account  string `protobuf:"bytes,1,req,name=account" json:"account"`
	SvrMsgId uint32 `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
}

func (m *MessagePeerReadStatus) Reset()                    { *m = MessagePeerReadStatus{} }
func (m *MessagePeerReadStatus) String() string            { return proto.CompactTextString(m) }
func (*MessagePeerReadStatus) ProtoMessage()               {}
func (*MessagePeerReadStatus) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{34} }

func (m *MessagePeerReadStatus) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MessagePeerReadStatus) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

type GetMessagePeerReadStatusResp struct {
	BaseResp           *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	PeerReadStatusList []*MessagePeerReadStatus `protobuf:"bytes,2,rep,name=peer_read_status_list,json=peerReadStatusList" json:"peer_read_status_list,omitempty"`
}

func (m *GetMessagePeerReadStatusResp) Reset()                    { *m = GetMessagePeerReadStatusResp{} }
func (m *GetMessagePeerReadStatusResp) String() string            { return proto.CompactTextString(m) }
func (*GetMessagePeerReadStatusResp) ProtoMessage()               {}
func (*GetMessagePeerReadStatusResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{35} }

func (m *GetMessagePeerReadStatusResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMessagePeerReadStatusResp) GetPeerReadStatusList() []*MessagePeerReadStatus {
	if m != nil {
		return m.PeerReadStatusList
	}
	return nil
}

// 删除信息
type DelMsg struct {
	TargetName    string `protobuf:"bytes,1,req,name=target_name,json=targetName" json:"target_name"`
	SvrMsgId      uint32 `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
	TargetMsgId   uint32 `protobuf:"varint,3,req,name=target_msg_id,json=targetMsgId" json:"target_msg_id"`
	ClientMsgId   uint32 `protobuf:"varint,4,req,name=client_msg_id,json=clientMsgId" json:"client_msg_id"`
	ClientMsgTime uint32 `protobuf:"varint,5,req,name=client_msg_time,json=clientMsgTime" json:"client_msg_time"`
	MyLoginKey    string `protobuf:"bytes,6,req,name=my_login_key,json=myLoginKey" json:"my_login_key"`
	FromName      string `protobuf:"bytes,7,req,name=from_name,json=fromName" json:"from_name"`
}

func (m *DelMsg) Reset()                    { *m = DelMsg{} }
func (m *DelMsg) String() string            { return proto.CompactTextString(m) }
func (*DelMsg) ProtoMessage()               {}
func (*DelMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{36} }

func (m *DelMsg) GetTargetName() string {
	if m != nil {
		return m.TargetName
	}
	return ""
}

func (m *DelMsg) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

func (m *DelMsg) GetTargetMsgId() uint32 {
	if m != nil {
		return m.TargetMsgId
	}
	return 0
}

func (m *DelMsg) GetClientMsgId() uint32 {
	if m != nil {
		return m.ClientMsgId
	}
	return 0
}

func (m *DelMsg) GetClientMsgTime() uint32 {
	if m != nil {
		return m.ClientMsgTime
	}
	return 0
}

func (m *DelMsg) GetMyLoginKey() string {
	if m != nil {
		return m.MyLoginKey
	}
	return ""
}

func (m *DelMsg) GetFromName() string {
	if m != nil {
		return m.FromName
	}
	return ""
}

// 批量删除信息
type BatchDelMsgReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	DelMsg  []*DelMsg   `protobuf:"bytes,2,rep,name=del_msg,json=delMsg" json:"del_msg,omitempty"`
}

func (m *BatchDelMsgReq) Reset()                    { *m = BatchDelMsgReq{} }
func (m *BatchDelMsgReq) String() string            { return proto.CompactTextString(m) }
func (*BatchDelMsgReq) ProtoMessage()               {}
func (*BatchDelMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{37} }

func (m *BatchDelMsgReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchDelMsgReq) GetDelMsg() []*DelMsg {
	if m != nil {
		return m.DelMsg
	}
	return nil
}

type BatchDelMsgResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *BatchDelMsgResp) Reset()                    { *m = BatchDelMsgResp{} }
func (m *BatchDelMsgResp) String() string            { return proto.CompactTextString(m) }
func (*BatchDelMsgResp) ProtoMessage()               {}
func (*BatchDelMsgResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{38} }

func (m *BatchDelMsgResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// //游戏中心好友热玩推送
type PopGameJumpMsg struct {
	MsgType     uint32 `protobuf:"varint,1,req,name=msg_type,json=msgType" json:"msg_type"`
	GameId      uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	AreaUrl     string `protobuf:"bytes,3,opt,name=area_url,json=areaUrl" json:"area_url"`
	IconUrl     string `protobuf:"bytes,4,opt,name=icon_url,json=iconUrl" json:"icon_url"`
	DownloadUrl string `protobuf:"bytes,5,opt,name=download_url,json=downloadUrl" json:"download_url"`
	GameSize    string `protobuf:"bytes,6,opt,name=game_size,json=gameSize" json:"game_size"`
	GamePktName string `protobuf:"bytes,7,opt,name=game_pkt_name,json=gamePktName" json:"game_pkt_name"`
	PreorderUrl string `protobuf:"bytes,8,opt,name=preorder_url,json=preorderUrl" json:"preorder_url"`
	GameName    string `protobuf:"bytes,9,opt,name=game_name,json=gameName" json:"game_name"`
}

func (m *PopGameJumpMsg) Reset()                    { *m = PopGameJumpMsg{} }
func (m *PopGameJumpMsg) String() string            { return proto.CompactTextString(m) }
func (*PopGameJumpMsg) ProtoMessage()               {}
func (*PopGameJumpMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{39} }

func (m *PopGameJumpMsg) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *PopGameJumpMsg) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *PopGameJumpMsg) GetAreaUrl() string {
	if m != nil {
		return m.AreaUrl
	}
	return ""
}

func (m *PopGameJumpMsg) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *PopGameJumpMsg) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

func (m *PopGameJumpMsg) GetGameSize() string {
	if m != nil {
		return m.GameSize
	}
	return ""
}

func (m *PopGameJumpMsg) GetGamePktName() string {
	if m != nil {
		return m.GamePktName
	}
	return ""
}

func (m *PopGameJumpMsg) GetPreorderUrl() string {
	if m != nil {
		return m.PreorderUrl
	}
	return ""
}

func (m *PopGameJumpMsg) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

// 表情消息
type EmojiMsg struct {
	Id     string `protobuf:"bytes,1,req,name=id" json:"id"`
	Url    string `protobuf:"bytes,2,req,name=url" json:"url"`
	Height uint32 `protobuf:"varint,3,req,name=height" json:"height"`
	Width  uint32 `protobuf:"varint,4,req,name=width" json:"width"`
	// 表情类型 see EmojiType
	EmojiType uint32 `protobuf:"varint,5,opt,name=emoji_type,json=emojiType" json:"emoji_type"`
	// 自动联想表情包关键词
	Keyword string `protobuf:"bytes,6,opt,name=keyword" json:"keyword"`
	// MD5
	Md5 string `protobuf:"bytes,7,opt,name=md5" json:"md5"`
	// 第三方表情包缩略图高
	ThumbH uint32 `protobuf:"varint,8,opt,name=thumb_h,json=thumbH" json:"thumb_h"`
	// 第三方表情包缩略图宽
	ThumbW uint32 `protobuf:"varint,9,opt,name=thumb_w,json=thumbW" json:"thumb_w"`
	// 是否obs链接
	IsObs bool `protobuf:"varint,10,opt,name=is_obs,json=isObs" json:"is_obs"`
	// obskey
	ObsKey string `protobuf:"bytes,11,opt,name=obs_key,json=obsKey" json:"obs_key"`
}

func (m *EmojiMsg) Reset()                    { *m = EmojiMsg{} }
func (m *EmojiMsg) String() string            { return proto.CompactTextString(m) }
func (*EmojiMsg) ProtoMessage()               {}
func (*EmojiMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{40} }

func (m *EmojiMsg) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *EmojiMsg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *EmojiMsg) GetHeight() uint32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *EmojiMsg) GetWidth() uint32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *EmojiMsg) GetEmojiType() uint32 {
	if m != nil {
		return m.EmojiType
	}
	return 0
}

func (m *EmojiMsg) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *EmojiMsg) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *EmojiMsg) GetThumbH() uint32 {
	if m != nil {
		return m.ThumbH
	}
	return 0
}

func (m *EmojiMsg) GetThumbW() uint32 {
	if m != nil {
		return m.ThumbW
	}
	return 0
}

func (m *EmojiMsg) GetIsObs() bool {
	if m != nil {
		return m.IsObs
	}
	return false
}

func (m *EmojiMsg) GetObsKey() string {
	if m != nil {
		return m.ObsKey
	}
	return ""
}

type InteractiveUnreadMsg struct {
	// 显示最新一条信息用
	Type             InteractiveUnreadMsg_InteractiveType `protobuf:"varint,1,req,name=type,enum=ga.im.InteractiveUnreadMsg_InteractiveType" json:"type"`
	Time             int64                                `protobuf:"varint,2,req,name=time" json:"time"`
	FromUserId       uint32                               `protobuf:"varint,3,req,name=from_user_id,json=fromUserId" json:"from_user_id"`
	FromUserNickname string                               `protobuf:"bytes,4,req,name=from_user_nickname,json=fromUserNickname" json:"from_user_nickname"`
	CommentCount     int32                                `protobuf:"varint,5,req,name=comment_count,json=commentCount" json:"comment_count"`
	AttitudeCount    int32                                `protobuf:"varint,6,req,name=attitude_count,json=attitudeCount" json:"attitude_count"`
}

func (m *InteractiveUnreadMsg) Reset()                    { *m = InteractiveUnreadMsg{} }
func (m *InteractiveUnreadMsg) String() string            { return proto.CompactTextString(m) }
func (*InteractiveUnreadMsg) ProtoMessage()               {}
func (*InteractiveUnreadMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{41} }

func (m *InteractiveUnreadMsg) GetType() InteractiveUnreadMsg_InteractiveType {
	if m != nil {
		return m.Type
	}
	return InteractiveUnreadMsg_NONE
}

func (m *InteractiveUnreadMsg) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *InteractiveUnreadMsg) GetFromUserId() uint32 {
	if m != nil {
		return m.FromUserId
	}
	return 0
}

func (m *InteractiveUnreadMsg) GetFromUserNickname() string {
	if m != nil {
		return m.FromUserNickname
	}
	return ""
}

func (m *InteractiveUnreadMsg) GetCommentCount() int32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *InteractiveUnreadMsg) GetAttitudeCount() int32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

type RichTextPresent struct {
	Id          uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Type        uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	Url         string `protobuf:"bytes,3,req,name=url" json:"url"`
	Head        string `protobuf:"bytes,4,req,name=head" json:"head"`
	Text        string `protobuf:"bytes,5,req,name=text" json:"text"`
	SourceType  uint32 `protobuf:"varint,6,req,name=source_type,json=sourceType" json:"source_type"`
	ShowSendBtn bool   `protobuf:"varint,7,req,name=show_send_btn,json=showSendBtn" json:"show_send_btn"`
	IsDouble    bool   `protobuf:"varint,8,req,name=is_double,json=isDouble" json:"is_double"`
	TargetUid   uint32 `protobuf:"varint,9,req,name=target_uid,json=targetUid" json:"target_uid"`
	Name        string `protobuf:"bytes,10,opt,name=name" json:"name"`
	PriceType   uint32 `protobuf:"varint,11,opt,name=price_type,json=priceType" json:"price_type"`
	Price       uint32 `protobuf:"varint,12,opt,name=price" json:"price"`
}

func (m *RichTextPresent) Reset()                    { *m = RichTextPresent{} }
func (m *RichTextPresent) String() string            { return proto.CompactTextString(m) }
func (*RichTextPresent) ProtoMessage()               {}
func (*RichTextPresent) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{42} }

func (m *RichTextPresent) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RichTextPresent) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *RichTextPresent) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *RichTextPresent) GetHead() string {
	if m != nil {
		return m.Head
	}
	return ""
}

func (m *RichTextPresent) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *RichTextPresent) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *RichTextPresent) GetShowSendBtn() bool {
	if m != nil {
		return m.ShowSendBtn
	}
	return false
}

func (m *RichTextPresent) GetIsDouble() bool {
	if m != nil {
		return m.IsDouble
	}
	return false
}

func (m *RichTextPresent) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *RichTextPresent) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RichTextPresent) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *RichTextPresent) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

// 对应 INTERACTION_INTIMACY_GIFT，IM_PRESENT ,IM_PRESENT_NEW
type RichTextWithPresentMsg struct {
	Msg           *PersonalRichTextMsg                `protobuf:"bytes,1,req,name=msg" json:"msg,omitempty"`
	Present       *RichTextPresent                    `protobuf:"bytes,2,req,name=present" json:"present,omitempty"`
	ItemInfo      *ga_userpresent.PresentSendItemInfo `protobuf:"bytes,3,opt,name=item_info,json=itemInfo" json:"item_info,omitempty"`
	ImPresentType uint32                              `protobuf:"varint,4,opt,name=im_present_type,json=imPresentType" json:"im_present_type"`
}

func (m *RichTextWithPresentMsg) Reset()                    { *m = RichTextWithPresentMsg{} }
func (m *RichTextWithPresentMsg) String() string            { return proto.CompactTextString(m) }
func (*RichTextWithPresentMsg) ProtoMessage()               {}
func (*RichTextWithPresentMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{43} }

func (m *RichTextWithPresentMsg) GetMsg() *PersonalRichTextMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *RichTextWithPresentMsg) GetPresent() *RichTextPresent {
	if m != nil {
		return m.Present
	}
	return nil
}

func (m *RichTextWithPresentMsg) GetItemInfo() *ga_userpresent.PresentSendItemInfo {
	if m != nil {
		return m.ItemInfo
	}
	return nil
}

func (m *RichTextWithPresentMsg) GetImPresentType() uint32 {
	if m != nil {
		return m.ImPresentType
	}
	return 0
}

type MasterApprenticeInviteMsg struct {
	MasterInnerText     string `protobuf:"bytes,1,opt,name=master_inner_text,json=masterInnerText" json:"master_inner_text"`
	MasterOuterText     string `protobuf:"bytes,2,opt,name=master_outer_text,json=masterOuterText" json:"master_outer_text"`
	ApprenticeInnerText string `protobuf:"bytes,3,opt,name=apprentice_inner_text,json=apprenticeInnerText" json:"apprentice_inner_text"`
	ApprenticeOuterText string `protobuf:"bytes,4,opt,name=apprentice_outer_text,json=apprenticeOuterText" json:"apprentice_outer_text"`
}

func (m *MasterApprenticeInviteMsg) Reset()                    { *m = MasterApprenticeInviteMsg{} }
func (m *MasterApprenticeInviteMsg) String() string            { return proto.CompactTextString(m) }
func (*MasterApprenticeInviteMsg) ProtoMessage()               {}
func (*MasterApprenticeInviteMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{44} }

func (m *MasterApprenticeInviteMsg) GetMasterInnerText() string {
	if m != nil {
		return m.MasterInnerText
	}
	return ""
}

func (m *MasterApprenticeInviteMsg) GetMasterOuterText() string {
	if m != nil {
		return m.MasterOuterText
	}
	return ""
}

func (m *MasterApprenticeInviteMsg) GetApprenticeInnerText() string {
	if m != nil {
		return m.ApprenticeInnerText
	}
	return ""
}

func (m *MasterApprenticeInviteMsg) GetApprenticeOuterText() string {
	if m != nil {
		return m.ApprenticeOuterText
	}
	return ""
}

type MasterApprenticeEstablishMsg struct {
	MasterInnerText     string `protobuf:"bytes,1,opt,name=master_inner_text,json=masterInnerText" json:"master_inner_text"`
	MasterOuterText     string `protobuf:"bytes,2,opt,name=master_outer_text,json=masterOuterText" json:"master_outer_text"`
	ApprenticeInnerText string `protobuf:"bytes,3,opt,name=apprentice_inner_text,json=apprenticeInnerText" json:"apprentice_inner_text"`
	ApprenticeOuterText string `protobuf:"bytes,4,opt,name=apprentice_outer_text,json=apprenticeOuterText" json:"apprentice_outer_text"`
}

func (m *MasterApprenticeEstablishMsg) Reset()                    { *m = MasterApprenticeEstablishMsg{} }
func (m *MasterApprenticeEstablishMsg) String() string            { return proto.CompactTextString(m) }
func (*MasterApprenticeEstablishMsg) ProtoMessage()               {}
func (*MasterApprenticeEstablishMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{45} }

func (m *MasterApprenticeEstablishMsg) GetMasterInnerText() string {
	if m != nil {
		return m.MasterInnerText
	}
	return ""
}

func (m *MasterApprenticeEstablishMsg) GetMasterOuterText() string {
	if m != nil {
		return m.MasterOuterText
	}
	return ""
}

func (m *MasterApprenticeEstablishMsg) GetApprenticeInnerText() string {
	if m != nil {
		return m.ApprenticeInnerText
	}
	return ""
}

func (m *MasterApprenticeEstablishMsg) GetApprenticeOuterText() string {
	if m != nil {
		return m.ApprenticeOuterText
	}
	return ""
}

type MasterApprenticeIntroductionMsg struct {
	Text_1 string `protobuf:"bytes,1,req,name=text_1,json=text1" json:"text_1"`
	Text_2 string `protobuf:"bytes,2,req,name=text_2,json=text2" json:"text_2"`
}

func (m *MasterApprenticeIntroductionMsg) Reset()         { *m = MasterApprenticeIntroductionMsg{} }
func (m *MasterApprenticeIntroductionMsg) String() string { return proto.CompactTextString(m) }
func (*MasterApprenticeIntroductionMsg) ProtoMessage()    {}
func (*MasterApprenticeIntroductionMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorIm, []int{46}
}

func (m *MasterApprenticeIntroductionMsg) GetText_1() string {
	if m != nil {
		return m.Text_1
	}
	return ""
}

func (m *MasterApprenticeIntroductionMsg) GetText_2() string {
	if m != nil {
		return m.Text_2
	}
	return ""
}

type ChatCardMsg struct {
	HiText    string `protobuf:"bytes,1,req,name=hi_text,json=hiText" json:"hi_text"`
	TopicText string `protobuf:"bytes,2,req,name=topic_text,json=topicText" json:"topic_text"`
	Card      []byte `protobuf:"bytes,3,req,name=card" json:"card"`
	DressId   uint32 `protobuf:"varint,4,opt,name=dress_id,json=dressId" json:"dress_id"`
}

func (m *ChatCardMsg) Reset()                    { *m = ChatCardMsg{} }
func (m *ChatCardMsg) String() string            { return proto.CompactTextString(m) }
func (*ChatCardMsg) ProtoMessage()               {}
func (*ChatCardMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{47} }

func (m *ChatCardMsg) GetHiText() string {
	if m != nil {
		return m.HiText
	}
	return ""
}

func (m *ChatCardMsg) GetTopicText() string {
	if m != nil {
		return m.TopicText
	}
	return ""
}

func (m *ChatCardMsg) GetCard() []byte {
	if m != nil {
		return m.Card
	}
	return nil
}

func (m *ChatCardMsg) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

type ChannelOpenGameInviteMsg struct {
	IconUrl    string `protobuf:"bytes,1,req,name=icon_url,json=iconUrl" json:"icon_url"`
	Name       string `protobuf:"bytes,2,req,name=name" json:"name"`
	Desc       string `protobuf:"bytes,3,req,name=desc" json:"desc"`
	ImgUrl     string `protobuf:"bytes,4,req,name=img_url,json=imgUrl" json:"img_url"`
	JumpUrl    string `protobuf:"bytes,5,req,name=jump_url,json=jumpUrl" json:"jump_url"`
	FromOutMsg string `protobuf:"bytes,6,opt,name=from_out_msg,json=fromOutMsg" json:"from_out_msg"`
	ToOutMsg   string `protobuf:"bytes,7,opt,name=to_out_msg,json=toOutMsg" json:"to_out_msg"`
}

func (m *ChannelOpenGameInviteMsg) Reset()                    { *m = ChannelOpenGameInviteMsg{} }
func (m *ChannelOpenGameInviteMsg) String() string            { return proto.CompactTextString(m) }
func (*ChannelOpenGameInviteMsg) ProtoMessage()               {}
func (*ChannelOpenGameInviteMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{48} }

func (m *ChannelOpenGameInviteMsg) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *ChannelOpenGameInviteMsg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ChannelOpenGameInviteMsg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ChannelOpenGameInviteMsg) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *ChannelOpenGameInviteMsg) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *ChannelOpenGameInviteMsg) GetFromOutMsg() string {
	if m != nil {
		return m.FromOutMsg
	}
	return ""
}

func (m *ChannelOpenGameInviteMsg) GetToOutMsg() string {
	if m != nil {
		return m.ToOutMsg
	}
	return ""
}

type FellowInviteMsg struct {
	InviteId       string `protobuf:"bytes,1,req,name=invite_id,json=inviteId" json:"invite_id"`
	InviteStatus   uint32 `protobuf:"varint,2,req,name=invite_status,json=inviteStatus" json:"invite_status"`
	PresentId      uint32 `protobuf:"varint,3,req,name=present_id,json=presentId" json:"present_id"`
	IsUnlock       bool   `protobuf:"varint,4,req,name=is_unlock,json=isUnlock" json:"is_unlock"`
	FellowBindType uint32 `protobuf:"varint,5,req,name=fellow_bind_type,json=fellowBindType" json:"fellow_bind_type"`
	FellowType     uint32 `protobuf:"varint,6,req,name=fellow_type,json=fellowType" json:"fellow_type"`
	FellowName     string `protobuf:"bytes,7,req,name=fellow_name,json=fellowName" json:"fellow_name"`
	PresentUrl     string `protobuf:"bytes,8,req,name=present_url,json=presentUrl" json:"present_url"`
	FromBindType   uint32 `protobuf:"varint,9,opt,name=from_bind_type,json=fromBindType" json:"from_bind_type"`
	FromFellowType uint32 `protobuf:"varint,10,opt,name=from_fellow_type,json=fromFellowType" json:"from_fellow_type"`
	FromFellowName string `protobuf:"bytes,11,opt,name=from_fellow_name,json=fromFellowName" json:"from_fellow_name"`
}

func (m *FellowInviteMsg) Reset()                    { *m = FellowInviteMsg{} }
func (m *FellowInviteMsg) String() string            { return proto.CompactTextString(m) }
func (*FellowInviteMsg) ProtoMessage()               {}
func (*FellowInviteMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{49} }

func (m *FellowInviteMsg) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *FellowInviteMsg) GetInviteStatus() uint32 {
	if m != nil {
		return m.InviteStatus
	}
	return 0
}

func (m *FellowInviteMsg) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *FellowInviteMsg) GetIsUnlock() bool {
	if m != nil {
		return m.IsUnlock
	}
	return false
}

func (m *FellowInviteMsg) GetFellowBindType() uint32 {
	if m != nil {
		return m.FellowBindType
	}
	return 0
}

func (m *FellowInviteMsg) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *FellowInviteMsg) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *FellowInviteMsg) GetPresentUrl() string {
	if m != nil {
		return m.PresentUrl
	}
	return ""
}

func (m *FellowInviteMsg) GetFromBindType() uint32 {
	if m != nil {
		return m.FromBindType
	}
	return 0
}

func (m *FellowInviteMsg) GetFromFellowType() uint32 {
	if m != nil {
		return m.FromFellowType
	}
	return 0
}

func (m *FellowInviteMsg) GetFromFellowName() string {
	if m != nil {
		return m.FromFellowName
	}
	return ""
}

type LevelAwardItem struct {
	ItemType      uint32 `protobuf:"varint,1,req,name=item_type,json=itemType" json:"item_type"`
	ItemName      string `protobuf:"bytes,2,req,name=item_name,json=itemName" json:"item_name"`
	ItemId        string `protobuf:"bytes,3,req,name=item_id,json=itemId" json:"item_id"`
	ItemCount     uint32 `protobuf:"varint,4,opt,name=item_count,json=itemCount" json:"item_count"`
	DayCount      uint32 `protobuf:"varint,5,opt,name=day_count,json=dayCount" json:"day_count"`
	ItemIcon      string `protobuf:"bytes,6,req,name=item_icon,json=itemIcon" json:"item_icon"`
	ItemCountInfo string `protobuf:"bytes,7,opt,name=item_count_info,json=itemCountInfo" json:"item_count_info"`
	ItemTypeName  string `protobuf:"bytes,8,opt,name=item_type_name,json=itemTypeName" json:"item_type_name"`
}

func (m *LevelAwardItem) Reset()                    { *m = LevelAwardItem{} }
func (m *LevelAwardItem) String() string            { return proto.CompactTextString(m) }
func (*LevelAwardItem) ProtoMessage()               {}
func (*LevelAwardItem) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{50} }

func (m *LevelAwardItem) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *LevelAwardItem) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *LevelAwardItem) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *LevelAwardItem) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *LevelAwardItem) GetDayCount() uint32 {
	if m != nil {
		return m.DayCount
	}
	return 0
}

func (m *LevelAwardItem) GetItemIcon() string {
	if m != nil {
		return m.ItemIcon
	}
	return ""
}

func (m *LevelAwardItem) GetItemCountInfo() string {
	if m != nil {
		return m.ItemCountInfo
	}
	return ""
}

func (m *LevelAwardItem) GetItemTypeName() string {
	if m != nil {
		return m.ItemTypeName
	}
	return ""
}

// 挚友下一个等级奖励信息
type FellowNextLevelAwardMsg struct {
	NextAwardLevel string            `protobuf:"bytes,1,req,name=next_award_level,json=nextAwardLevel" json:"next_award_level"`
	AwardList      []*LevelAwardItem `protobuf:"bytes,2,rep,name=award_list,json=awardList" json:"award_list,omitempty"`
}

func (m *FellowNextLevelAwardMsg) Reset()                    { *m = FellowNextLevelAwardMsg{} }
func (m *FellowNextLevelAwardMsg) String() string            { return proto.CompactTextString(m) }
func (*FellowNextLevelAwardMsg) ProtoMessage()               {}
func (*FellowNextLevelAwardMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{51} }

func (m *FellowNextLevelAwardMsg) GetNextAwardLevel() string {
	if m != nil {
		return m.NextAwardLevel
	}
	return ""
}

func (m *FellowNextLevelAwardMsg) GetAwardList() []*LevelAwardItem {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type FellowUpgradeMsg struct {
	FellowBindType uint32                   `protobuf:"varint,1,req,name=fellow_bind_type,json=fellowBindType" json:"fellow_bind_type"`
	FellowName     string                   `protobuf:"bytes,2,req,name=fellow_name,json=fellowName" json:"fellow_name"`
	Level          uint32                   `protobuf:"varint,3,req,name=level" json:"level"`
	NextAwardMsg   *FellowNextLevelAwardMsg `protobuf:"bytes,4,opt,name=next_award_msg,json=nextAwardMsg" json:"next_award_msg,omitempty"`
}

func (m *FellowUpgradeMsg) Reset()                    { *m = FellowUpgradeMsg{} }
func (m *FellowUpgradeMsg) String() string            { return proto.CompactTextString(m) }
func (*FellowUpgradeMsg) ProtoMessage()               {}
func (*FellowUpgradeMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{52} }

func (m *FellowUpgradeMsg) GetFellowBindType() uint32 {
	if m != nil {
		return m.FellowBindType
	}
	return 0
}

func (m *FellowUpgradeMsg) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *FellowUpgradeMsg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *FellowUpgradeMsg) GetNextAwardMsg() *FellowNextLevelAwardMsg {
	if m != nil {
		return m.NextAwardMsg
	}
	return nil
}

type GainRareUser struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account  string `protobuf:"bytes,2,req,name=account" json:"account"`
	Nickname string `protobuf:"bytes,3,req,name=nickname" json:"nickname"`
	RareName string `protobuf:"bytes,4,req,name=rare_name,json=rareName" json:"rare_name"`
	IsMvp    bool   `protobuf:"varint,5,req,name=is_mvp,json=isMvp" json:"is_mvp"`
	IsCharm  bool   `protobuf:"varint,6,req,name=is_charm,json=isCharm" json:"is_charm"`
}

func (m *GainRareUser) Reset()                    { *m = GainRareUser{} }
func (m *GainRareUser) String() string            { return proto.CompactTextString(m) }
func (*GainRareUser) ProtoMessage()               {}
func (*GainRareUser) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{53} }

func (m *GainRareUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GainRareUser) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GainRareUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GainRareUser) GetRareName() string {
	if m != nil {
		return m.RareName
	}
	return ""
}

func (m *GainRareUser) GetIsMvp() bool {
	if m != nil {
		return m.IsMvp
	}
	return false
}

func (m *GainRareUser) GetIsCharm() bool {
	if m != nil {
		return m.IsCharm
	}
	return false
}

type GainRareMsg struct {
	Users           []*GainRareUser `protobuf:"bytes,1,rep,name=users" json:"users,omitempty"`
	IsBind          bool            `protobuf:"varint,2,req,name=is_bind,json=isBind" json:"is_bind"`
	ThumbBackground string          `protobuf:"bytes,3,req,name=thumb_background,json=thumbBackground" json:"thumb_background"`
	Background      string          `protobuf:"bytes,4,req,name=background" json:"background"`
	RareDays        string          `protobuf:"bytes,5,req,name=rare_days,json=rareDays" json:"rare_days"`
	RareName        string          `protobuf:"bytes,6,req,name=rare_name,json=rareName" json:"rare_name"`
	Timestamp       uint32          `protobuf:"varint,7,req,name=timestamp" json:"timestamp"`
	ChannelId       uint32          `protobuf:"varint,8,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GainRareMsg) Reset()                    { *m = GainRareMsg{} }
func (m *GainRareMsg) String() string            { return proto.CompactTextString(m) }
func (*GainRareMsg) ProtoMessage()               {}
func (*GainRareMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{54} }

func (m *GainRareMsg) GetUsers() []*GainRareUser {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *GainRareMsg) GetIsBind() bool {
	if m != nil {
		return m.IsBind
	}
	return false
}

func (m *GainRareMsg) GetThumbBackground() string {
	if m != nil {
		return m.ThumbBackground
	}
	return ""
}

func (m *GainRareMsg) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *GainRareMsg) GetRareDays() string {
	if m != nil {
		return m.RareDays
	}
	return ""
}

func (m *GainRareMsg) GetRareName() string {
	if m != nil {
		return m.RareName
	}
	return ""
}

func (m *GainRareMsg) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *GainRareMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SystemNotifyMsg struct {
	HighlightContent string `protobuf:"bytes,1,req,name=highlight_content,json=highlightContent" json:"highlight_content"`
	JumpUrl          string `protobuf:"bytes,2,req,name=jump_url,json=jumpUrl" json:"jump_url"`
}

func (m *SystemNotifyMsg) Reset()                    { *m = SystemNotifyMsg{} }
func (m *SystemNotifyMsg) String() string            { return proto.CompactTextString(m) }
func (*SystemNotifyMsg) ProtoMessage()               {}
func (*SystemNotifyMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{55} }

func (m *SystemNotifyMsg) GetHighlightContent() string {
	if m != nil {
		return m.HighlightContent
	}
	return ""
}

func (m *SystemNotifyMsg) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type ShareGameMsg struct {
	GameName string `protobuf:"bytes,1,req,name=game_name,json=gameName" json:"game_name"`
	ImgUrl   string `protobuf:"bytes,2,req,name=img_url,json=imgUrl" json:"img_url"`
	JumpUrl  string `protobuf:"bytes,3,req,name=jump_url,json=jumpUrl" json:"jump_url"`
}

func (m *ShareGameMsg) Reset()                    { *m = ShareGameMsg{} }
func (m *ShareGameMsg) String() string            { return proto.CompactTextString(m) }
func (*ShareGameMsg) ProtoMessage()               {}
func (*ShareGameMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{56} }

func (m *ShareGameMsg) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *ShareGameMsg) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *ShareGameMsg) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type MiJingUserTag struct {
	MatchingDegree string `protobuf:"bytes,1,req,name=matching_degree,json=matchingDegree" json:"matching_degree"`
}

func (m *MiJingUserTag) Reset()                    { *m = MiJingUserTag{} }
func (m *MiJingUserTag) String() string            { return proto.CompactTextString(m) }
func (*MiJingUserTag) ProtoMessage()               {}
func (*MiJingUserTag) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{57} }

func (m *MiJingUserTag) GetMatchingDegree() string {
	if m != nil {
		return m.MatchingDegree
	}
	return ""
}

type UserRecallComeback struct {
	Content string `protobuf:"bytes,1,req,name=content" json:"content"`
	Hlight  string `protobuf:"bytes,2,req,name=hlight" json:"hlight"`
	Url     string `protobuf:"bytes,3,req,name=url" json:"url"`
}

func (m *UserRecallComeback) Reset()                    { *m = UserRecallComeback{} }
func (m *UserRecallComeback) String() string            { return proto.CompactTextString(m) }
func (*UserRecallComeback) ProtoMessage()               {}
func (*UserRecallComeback) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{58} }

func (m *UserRecallComeback) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *UserRecallComeback) GetHlight() string {
	if m != nil {
		return m.Hlight
	}
	return ""
}

func (m *UserRecallComeback) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type GetImGuideTriggerInfoReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Account       string      `protobuf:"bytes,2,req,name=account" json:"account"`
	TargetAccount string      `protobuf:"bytes,3,req,name=target_account,json=targetAccount" json:"target_account"`
}

func (m *GetImGuideTriggerInfoReq) Reset()                    { *m = GetImGuideTriggerInfoReq{} }
func (m *GetImGuideTriggerInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetImGuideTriggerInfoReq) ProtoMessage()               {}
func (*GetImGuideTriggerInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{59} }

func (m *GetImGuideTriggerInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetImGuideTriggerInfoReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GetImGuideTriggerInfoReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

type GetImGuideTriggerInfoResp struct {
	BaseResp        *ga.BaseResp   `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TriggerItemList []*TriggerItem `protobuf:"bytes,2,rep,name=trigger_item_list,json=triggerItemList" json:"trigger_item_list,omitempty"`
}

func (m *GetImGuideTriggerInfoResp) Reset()                    { *m = GetImGuideTriggerInfoResp{} }
func (m *GetImGuideTriggerInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetImGuideTriggerInfoResp) ProtoMessage()               {}
func (*GetImGuideTriggerInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{60} }

func (m *GetImGuideTriggerInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetImGuideTriggerInfoResp) GetTriggerItemList() []*TriggerItem {
	if m != nil {
		return m.TriggerItemList
	}
	return nil
}

type TriggerItem struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account      string `protobuf:"bytes,2,req,name=account" json:"account"`
	Cid          uint32 `protobuf:"varint,3,req,name=cid" json:"cid"`
	Creator      uint32 `protobuf:"varint,4,req,name=creator" json:"creator"`
	ChannelType  uint32 `protobuf:"varint,5,req,name=channel_type,json=channelType" json:"channel_type"`
	TabId        uint32 `protobuf:"varint,6,opt,name=tab_id,json=tabId" json:"tab_id"`
	GamePlayName string `protobuf:"bytes,7,opt,name=game_play_name,json=gamePlayName" json:"game_play_name"`
	IsOnline     bool   `protobuf:"varint,8,opt,name=is_online,json=isOnline" json:"is_online"`
}

func (m *TriggerItem) Reset()                    { *m = TriggerItem{} }
func (m *TriggerItem) String() string            { return proto.CompactTextString(m) }
func (*TriggerItem) ProtoMessage()               {}
func (*TriggerItem) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{61} }

func (m *TriggerItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TriggerItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *TriggerItem) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *TriggerItem) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *TriggerItem) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *TriggerItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *TriggerItem) GetGamePlayName() string {
	if m != nil {
		return m.GamePlayName
	}
	return ""
}

func (m *TriggerItem) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

// IMCommonCardMsg 通用卡片消息
type IMCommonCardMsg struct {
	Title   string `protobuf:"bytes,1,opt,name=title" json:"title"`
	Content string `protobuf:"bytes,2,opt,name=content" json:"content"`
	JumpUrl string `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl" json:"jump_url"`
	ImgUrl  string `protobuf:"bytes,4,opt,name=img_url,json=imgUrl" json:"img_url"`
}

func (m *IMCommonCardMsg) Reset()                    { *m = IMCommonCardMsg{} }
func (m *IMCommonCardMsg) String() string            { return proto.CompactTextString(m) }
func (*IMCommonCardMsg) ProtoMessage()               {}
func (*IMCommonCardMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{62} }

func (m *IMCommonCardMsg) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *IMCommonCardMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *IMCommonCardMsg) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *IMCommonCardMsg) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

// 通用的Yoga的xml的消息
type IMCommonXmlMsg struct {
	DisplayType uint32           `protobuf:"varint,1,req,name=display_type,json=displayType" json:"display_type"`
	XmlContent  string           `protobuf:"bytes,2,req,name=xml_content,json=xmlContent" json:"xml_content"`
	DressId     uint32           `protobuf:"varint,3,req,name=dress_id,json=dressId" json:"dress_id"`
	Payload     []byte           `protobuf:"bytes,4,opt,name=payload" json:"payload"`
	PayloadType IMXMLPayLoadType `protobuf:"varint,5,opt,name=payload_type,json=payloadType,enum=ga.im.IMXMLPayLoadType" json:"payload_type"`
}

func (m *IMCommonXmlMsg) Reset()                    { *m = IMCommonXmlMsg{} }
func (m *IMCommonXmlMsg) String() string            { return proto.CompactTextString(m) }
func (*IMCommonXmlMsg) ProtoMessage()               {}
func (*IMCommonXmlMsg) Descriptor() ([]byte, []int) { return fileDescriptorIm, []int{63} }

func (m *IMCommonXmlMsg) GetDisplayType() uint32 {
	if m != nil {
		return m.DisplayType
	}
	return 0
}

func (m *IMCommonXmlMsg) GetXmlContent() string {
	if m != nil {
		return m.XmlContent
	}
	return ""
}

func (m *IMCommonXmlMsg) GetDressId() uint32 {
	if m != nil {
		return m.DressId
	}
	return 0
}

func (m *IMCommonXmlMsg) GetPayload() []byte {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (m *IMCommonXmlMsg) GetPayloadType() IMXMLPayLoadType {
	if m != nil {
		return m.PayloadType
	}
	return IMXMLPayLoadType_IM_XML_PAYLOAD_TYPE_UNSPECIFIED
}

func init() {
	proto.RegisterType((*RichTextWords)(nil), "ga.im.RichTextWords")
	proto.RegisterType((*RichTextImage)(nil), "ga.im.RichTextImage")
	proto.RegisterType((*RichTextLink)(nil), "ga.im.RichTextLink")
	proto.RegisterType((*RichTextElement)(nil), "ga.im.RichTextElement")
	proto.RegisterType((*RichTextMsg)(nil), "ga.im.RichTextMsg")
	proto.RegisterType((*PersonalRichTextElement)(nil), "ga.im.PersonalRichTextElement")
	proto.RegisterType((*PersonalRichTextMsg)(nil), "ga.im.PersonalRichTextMsg")
	proto.RegisterType((*LinkJumpURL)(nil), "ga.im.LinkJumpURL")
	proto.RegisterType((*SessionCreateMsg)(nil), "ga.im.SessionCreateMsg")
	proto.RegisterType((*SessionCloseMsg)(nil), "ga.im.SessionCloseMsg")
	proto.RegisterType((*CueMsg)(nil), "ga.im.CueMsg")
	proto.RegisterType((*TextMsgExt)(nil), "ga.im.TextMsgExt")
	proto.RegisterType((*VoiceMsgExt)(nil), "ga.im.VoiceMsgExt")
	proto.RegisterType((*SendMsgReq)(nil), "ga.im.SendMsgReq")
	proto.RegisterType((*SendMsgResp)(nil), "ga.im.SendMsgResp")
	proto.RegisterType((*UploadAttachmentReq)(nil), "ga.im.UploadAttachmentReq")
	proto.RegisterType((*UploadAttachmentResp)(nil), "ga.im.UploadAttachmentResp")
	proto.RegisterType((*DownloadAttachmentReq)(nil), "ga.im.DownloadAttachmentReq")
	proto.RegisterType((*DownloadAttachmentResp)(nil), "ga.im.DownloadAttachmentResp")
	proto.RegisterType((*DeleteMessageReq)(nil), "ga.im.DeleteMessageReq")
	proto.RegisterType((*DeleteMessageResp)(nil), "ga.im.DeleteMessageResp")
	proto.RegisterType((*MarkMsgReadReq)(nil), "ga.im.MarkMsgReadReq")
	proto.RegisterType((*MarkMsgReadResp)(nil), "ga.im.MarkMsgReadResp")
	proto.RegisterType((*UpdateMsgSettingReq)(nil), "ga.im.UpdateMsgSettingReq")
	proto.RegisterType((*UpdateMsgSettingResp)(nil), "ga.im.UpdateMsgSettingResp")
	proto.RegisterType((*QueryMsgSettingReq)(nil), "ga.im.QueryMsgSettingReq")
	proto.RegisterType((*MsgReceiveSettingItem)(nil), "ga.im.MsgReceiveSettingItem")
	proto.RegisterType((*QueryMsgSettingResp)(nil), "ga.im.QueryMsgSettingResp")
	proto.RegisterType((*CheckSendAtEveryoneGroupMsgReq)(nil), "ga.im.CheckSendAtEveryoneGroupMsgReq")
	proto.RegisterType((*CheckSendAtEveryoneGroupMsgResp)(nil), "ga.im.CheckSendAtEveryoneGroupMsgResp")
	proto.RegisterType((*CancelMsgReq)(nil), "ga.im.CancelMsgReq")
	proto.RegisterType((*CancelMsgResp)(nil), "ga.im.CancelMsgResp")
	proto.RegisterType((*MessageReadByPeerMessage)(nil), "ga.im.MessageReadByPeerMessage")
	proto.RegisterType((*GetMessagePeerReadStatusReq)(nil), "ga.im.GetMessagePeerReadStatusReq")
	proto.RegisterType((*MessagePeerReadStatus)(nil), "ga.im.MessagePeerReadStatus")
	proto.RegisterType((*GetMessagePeerReadStatusResp)(nil), "ga.im.GetMessagePeerReadStatusResp")
	proto.RegisterType((*DelMsg)(nil), "ga.im.DelMsg")
	proto.RegisterType((*BatchDelMsgReq)(nil), "ga.im.BatchDelMsgReq")
	proto.RegisterType((*BatchDelMsgResp)(nil), "ga.im.BatchDelMsgResp")
	proto.RegisterType((*PopGameJumpMsg)(nil), "ga.im.PopGameJumpMsg")
	proto.RegisterType((*EmojiMsg)(nil), "ga.im.EmojiMsg")
	proto.RegisterType((*InteractiveUnreadMsg)(nil), "ga.im.InteractiveUnreadMsg")
	proto.RegisterType((*RichTextPresent)(nil), "ga.im.RichTextPresent")
	proto.RegisterType((*RichTextWithPresentMsg)(nil), "ga.im.RichTextWithPresentMsg")
	proto.RegisterType((*MasterApprenticeInviteMsg)(nil), "ga.im.MasterApprenticeInviteMsg")
	proto.RegisterType((*MasterApprenticeEstablishMsg)(nil), "ga.im.MasterApprenticeEstablishMsg")
	proto.RegisterType((*MasterApprenticeIntroductionMsg)(nil), "ga.im.MasterApprenticeIntroductionMsg")
	proto.RegisterType((*ChatCardMsg)(nil), "ga.im.ChatCardMsg")
	proto.RegisterType((*ChannelOpenGameInviteMsg)(nil), "ga.im.ChannelOpenGameInviteMsg")
	proto.RegisterType((*FellowInviteMsg)(nil), "ga.im.FellowInviteMsg")
	proto.RegisterType((*LevelAwardItem)(nil), "ga.im.LevelAwardItem")
	proto.RegisterType((*FellowNextLevelAwardMsg)(nil), "ga.im.FellowNextLevelAwardMsg")
	proto.RegisterType((*FellowUpgradeMsg)(nil), "ga.im.FellowUpgradeMsg")
	proto.RegisterType((*GainRareUser)(nil), "ga.im.GainRareUser")
	proto.RegisterType((*GainRareMsg)(nil), "ga.im.GainRareMsg")
	proto.RegisterType((*SystemNotifyMsg)(nil), "ga.im.SystemNotifyMsg")
	proto.RegisterType((*ShareGameMsg)(nil), "ga.im.ShareGameMsg")
	proto.RegisterType((*MiJingUserTag)(nil), "ga.im.MiJingUserTag")
	proto.RegisterType((*UserRecallComeback)(nil), "ga.im.UserRecallComeback")
	proto.RegisterType((*GetImGuideTriggerInfoReq)(nil), "ga.im.GetImGuideTriggerInfoReq")
	proto.RegisterType((*GetImGuideTriggerInfoResp)(nil), "ga.im.GetImGuideTriggerInfoResp")
	proto.RegisterType((*TriggerItem)(nil), "ga.im.TriggerItem")
	proto.RegisterType((*IMCommonCardMsg)(nil), "ga.im.IMCommonCardMsg")
	proto.RegisterType((*IMCommonXmlMsg)(nil), "ga.im.IMCommonXmlMsg")
	proto.RegisterEnum("ga.im.IM_MSG_TYPE", IM_MSG_TYPE_name, IM_MSG_TYPE_value)
	proto.RegisterEnum("ga.im.MsgSourceType", MsgSourceType_name, MsgSourceType_value)
	proto.RegisterEnum("ga.im.MsgSensitiveType", MsgSensitiveType_name, MsgSensitiveType_value)
	proto.RegisterEnum("ga.im.MsgLabel", MsgLabel_name, MsgLabel_value)
	proto.RegisterEnum("ga.im.MsgReceiveSetting", MsgReceiveSetting_name, MsgReceiveSetting_value)
	proto.RegisterEnum("ga.im.LevelAwardItemType", LevelAwardItemType_name, LevelAwardItemType_value)
	proto.RegisterEnum("ga.im.FellowLevelAwardItemType", FellowLevelAwardItemType_name, FellowLevelAwardItemType_value)
	proto.RegisterEnum("ga.im.XmlMsgDidsplayType", XmlMsgDidsplayType_name, XmlMsgDidsplayType_value)
	proto.RegisterEnum("ga.im.IMXMLPayLoadType", IMXMLPayLoadType_name, IMXMLPayLoadType_value)
	proto.RegisterEnum("ga.im.RichTextMsg_RichTextMsgLevel", RichTextMsg_RichTextMsgLevel_name, RichTextMsg_RichTextMsgLevel_value)
	proto.RegisterEnum("ga.im.RichTextMsg_RichTextMsgType", RichTextMsg_RichTextMsgType_name, RichTextMsg_RichTextMsgType_value)
	proto.RegisterEnum("ga.im.PopGameJumpMsg_JumpType", PopGameJumpMsg_JumpType_name, PopGameJumpMsg_JumpType_value)
	proto.RegisterEnum("ga.im.EmojiMsg_EmojiType", EmojiMsg_EmojiType_name, EmojiMsg_EmojiType_value)
	proto.RegisterEnum("ga.im.InteractiveUnreadMsg_InteractiveType", InteractiveUnreadMsg_InteractiveType_name, InteractiveUnreadMsg_InteractiveType_value)
	proto.RegisterEnum("ga.im.RichTextWithPresentMsg_IM_PRESENT_TYPE", RichTextWithPresentMsg_IM_PRESENT_TYPE_name, RichTextWithPresentMsg_IM_PRESENT_TYPE_value)
}
func (m *RichTextWords) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichTextWords) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	return i, nil
}

func (m *RichTextImage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichTextImage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x15
	i++
	i = encodeFixed32Im(dAtA, i, uint32(math5.Float32bits(float32(m.Width))))
	dAtA[i] = 0x1d
	i++
	i = encodeFixed32Im(dAtA, i, uint32(math5.Float32bits(float32(m.Height))))
	return i, nil
}

func (m *RichTextLink) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichTextLink) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.TextColor))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	if m.JumpUrlPbContent != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintIm(dAtA, i, uint64(len(m.JumpUrlPbContent)))
		i += copy(dAtA[i:], m.JumpUrlPbContent)
	}
	return i, nil
}

func (m *RichTextElement) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichTextElement) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Content != nil {
		nn1, err := m.Content.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += nn1
	}
	return i, nil
}

func (m *RichTextElement_Words) MarshalTo(dAtA []byte) (int, error) {
	i := 0
	if m.Words != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.Words.Size()))
		n2, err := m.Words.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}
func (m *RichTextElement_Image) MarshalTo(dAtA []byte) (int, error) {
	i := 0
	if m.Image != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.Image.Size()))
		n3, err := m.Image.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}
func (m *RichTextElement_Link) MarshalTo(dAtA []byte) (int, error) {
	i := 0
	if m.Link != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.Link.Size()))
		n4, err := m.Link.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}
func (m *RichTextMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichTextMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InValue) > 0 {
		for _, msg := range m.InValue {
			dAtA[i] = 0xa
			i++
			i = encodeVarintIm(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.OutValue) > 0 {
		for _, msg := range m.OutValue {
			dAtA[i] = 0x12
			i++
			i = encodeVarintIm(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *PersonalRichTextElement) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PersonalRichTextElement) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Account) > 0 {
		for _, s := range m.Account {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.Msg.Size()))
		n5, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *PersonalRichTextMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PersonalRichTextMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Personal) > 0 {
		for _, msg := range m.Personal {
			dAtA[i] = 0xa
			i++
			i = encodeVarintIm(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Default != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.Default.Size()))
		n6, err := m.Default.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *LinkJumpURL) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LinkJumpURL) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.JumpUrlMap) > 0 {
		for k, _ := range m.JumpUrlMap {
			dAtA[i] = 0xa
			i++
			v := m.JumpUrlMap[k]
			mapSize := 1 + len(k) + sovIm(uint64(len(k))) + 1 + len(v) + sovIm(uint64(len(v)))
			i = encodeVarintIm(dAtA, i, uint64(mapSize))
			dAtA[i] = 0xa
			i++
			i = encodeVarintIm(dAtA, i, uint64(len(k)))
			i += copy(dAtA[i:], k)
			dAtA[i] = 0x12
			i++
			i = encodeVarintIm(dAtA, i, uint64(len(v)))
			i += copy(dAtA[i:], v)
		}
	}
	return i, nil
}

func (m *SessionCreateMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SessionCreateMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SessionId))
	return i, nil
}

func (m *SessionCloseMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SessionCloseMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SessionId))
	return i, nil
}

func (m *CueMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CueMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.CueId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MsgPicLeft)))
	i += copy(dAtA[i:], m.MsgPicLeft)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MsgPicRight)))
	i += copy(dAtA[i:], m.MsgPicRight)
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.PreviewText)))
	i += copy(dAtA[i:], m.PreviewText)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.LottieUrl)))
	i += copy(dAtA[i:], m.LottieUrl)
	return i, nil
}

func (m *TextMsgExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TextMsgExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.DressId))
	return i, nil
}

func (m *VoiceMsgExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoiceMsgExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.DressId))
	return i, nil
}

func (m *SendMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.TargetName)))
	i += copy(dAtA[i:], m.TargetName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x28
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ClientMsgId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ClientMsgTime))
	if m.Thumb != nil {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintIm(dAtA, i, uint64(len(m.Thumb)))
		i += copy(dAtA[i:], m.Thumb)
	}
	dAtA[i] = 0x40
	i++
	if m.HasAttachment {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x4a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MyLoginKey)))
	i += copy(dAtA[i:], m.MyLoginKey)
	dAtA[i] = 0x50
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Origin))
	if m.Ext != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintIm(dAtA, i, uint64(len(m.Ext)))
		i += copy(dAtA[i:], m.Ext)
	}
	dAtA[i] = 0x60
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.MsgSourceType))
	dAtA[i] = 0x68
	i++
	if m.DoNotCheckText {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x70
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.MsgLabel))
	return i, nil
}

func (m *SendMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ClientMsgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgTime))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.AttachmentKey)))
	i += copy(dAtA[i:], m.AttachmentKey)
	dAtA[i] = 0x32
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.TargetName)))
	i += copy(dAtA[i:], m.TargetName)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MyLoginKey)))
	i += copy(dAtA[i:], m.MyLoginKey)
	dAtA[i] = 0x40
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ExceedTime))
	dAtA[i] = 0x48
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Origin))
	dAtA[i] = 0x50
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.TargetMsgId))
	dAtA[i] = 0x58
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *UploadAttachmentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UploadAttachmentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.AttachmentKey)))
	i += copy(dAtA[i:], m.AttachmentKey)
	if m.Attachment != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintIm(dAtA, i, uint64(len(m.Attachment)))
		i += copy(dAtA[i:], m.Attachment)
	}
	if m.AttachmentProperty != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintIm(dAtA, i, uint64(len(m.AttachmentProperty)))
		i += copy(dAtA[i:], m.AttachmentProperty)
	}
	return i, nil
}

func (m *UploadAttachmentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UploadAttachmentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	return i, nil
}

func (m *DownloadAttachmentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownloadAttachmentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.AttachmentKey)))
	i += copy(dAtA[i:], m.AttachmentKey)
	return i, nil
}

func (m *DownloadAttachmentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownloadAttachmentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.AttachmentKey)))
	i += copy(dAtA[i:], m.AttachmentKey)
	if m.Attachment != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintIm(dAtA, i, uint64(len(m.Attachment)))
		i += copy(dAtA[i:], m.Attachment)
	}
	dAtA[i] = 0x2a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	return i, nil
}

func (m *DeleteMessageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteMessageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n13, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.MsgId))
	return i, nil
}

func (m *DeleteMessageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteMessageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n14, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	return i, nil
}

func (m *MarkMsgReadReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkMsgReadReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n15, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.TargetName)))
	i += copy(dAtA[i:], m.TargetName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.PeerSvrMsgId))
	return i, nil
}

func (m *MarkMsgReadResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkMsgReadResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n16, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.TargetName)))
	i += copy(dAtA[i:], m.TargetName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.PeerReadSvrMsgId))
	return i, nil
}

func (m *UpdateMsgSettingReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateMsgSettingReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n17, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ReceiveSetting))
	return i, nil
}

func (m *UpdateMsgSettingResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateMsgSettingResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n18, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	return i, nil
}

func (m *QueryMsgSettingReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryMsgSettingReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	return i, nil
}

func (m *MsgReceiveSettingItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MsgReceiveSettingItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ReceiveSetting))
	return i, nil
}

func (m *QueryMsgSettingResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryMsgSettingResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n20, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	if len(m.SettingItemList) > 0 {
		for _, msg := range m.SettingItemList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintIm(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CheckSendAtEveryoneGroupMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckSendAtEveryoneGroupMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n21, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *CheckSendAtEveryoneGroupMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckSendAtEveryoneGroupMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n22, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.RemainCnt))
	dAtA[i] = 0x18
	i++
	if m.IsAllowAtEveryone {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CancelMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n23, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.TargetName)))
	i += copy(dAtA[i:], m.TargetName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.TargetMsgId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ClientMsgId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ClientMsgTime))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MyLoginKey)))
	i += copy(dAtA[i:], m.MyLoginKey)
	return i, nil
}

func (m *CancelMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n24, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	return i, nil
}

func (m *MessageReadByPeerMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MessageReadByPeerMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.PeerName)))
	i += copy(dAtA[i:], m.PeerName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	return i, nil
}

func (m *GetMessagePeerReadStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMessagePeerReadStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n25, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	if len(m.AccountList) > 0 {
		for _, s := range m.AccountList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *MessagePeerReadStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MessagePeerReadStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	return i, nil
}

func (m *GetMessagePeerReadStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMessagePeerReadStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n26, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	if len(m.PeerReadStatusList) > 0 {
		for _, msg := range m.PeerReadStatusList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintIm(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DelMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.TargetName)))
	i += copy(dAtA[i:], m.TargetName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SvrMsgId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.TargetMsgId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ClientMsgId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ClientMsgTime))
	dAtA[i] = 0x32
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MyLoginKey)))
	i += copy(dAtA[i:], m.MyLoginKey)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.FromName)))
	i += copy(dAtA[i:], m.FromName)
	return i, nil
}

func (m *BatchDelMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDelMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n27, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	if len(m.DelMsg) > 0 {
		for _, msg := range m.DelMsg {
			dAtA[i] = 0x12
			i++
			i = encodeVarintIm(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchDelMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDelMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n28, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	return i, nil
}

func (m *PopGameJumpMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PopGameJumpMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.MsgType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.AreaUrl)))
	i += copy(dAtA[i:], m.AreaUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.DownloadUrl)))
	i += copy(dAtA[i:], m.DownloadUrl)
	dAtA[i] = 0x32
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.GameSize)))
	i += copy(dAtA[i:], m.GameSize)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.GamePktName)))
	i += copy(dAtA[i:], m.GamePktName)
	dAtA[i] = 0x42
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.PreorderUrl)))
	i += copy(dAtA[i:], m.PreorderUrl)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	return i, nil
}

func (m *EmojiMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EmojiMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Id)))
	i += copy(dAtA[i:], m.Id)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Height))
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Width))
	dAtA[i] = 0x28
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.EmojiType))
	dAtA[i] = 0x32
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Keyword)))
	i += copy(dAtA[i:], m.Keyword)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Md5)))
	i += copy(dAtA[i:], m.Md5)
	dAtA[i] = 0x40
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ThumbH))
	dAtA[i] = 0x48
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ThumbW))
	dAtA[i] = 0x50
	i++
	if m.IsObs {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x5a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ObsKey)))
	i += copy(dAtA[i:], m.ObsKey)
	return i, nil
}

func (m *InteractiveUnreadMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InteractiveUnreadMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Time))
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.FromUserId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.FromUserNickname)))
	i += copy(dAtA[i:], m.FromUserNickname)
	dAtA[i] = 0x28
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.CommentCount))
	dAtA[i] = 0x30
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.AttitudeCount))
	return i, nil
}

func (m *RichTextPresent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichTextPresent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Head)))
	i += copy(dAtA[i:], m.Head)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Text)))
	i += copy(dAtA[i:], m.Text)
	dAtA[i] = 0x30
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x38
	i++
	if m.ShowSendBtn {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	if m.IsDouble {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x52
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x58
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.PriceType))
	dAtA[i] = 0x60
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Price))
	return i, nil
}

func (m *RichTextWithPresentMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RichTextWithPresentMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.Msg.Size()))
		n29, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	if m.Present == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("present")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.Present.Size()))
		n30, err := m.Present.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	if m.ItemInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.ItemInfo.Size()))
		n31, err := m.ItemInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ImPresentType))
	return i, nil
}

func (m *MasterApprenticeInviteMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MasterApprenticeInviteMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MasterInnerText)))
	i += copy(dAtA[i:], m.MasterInnerText)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MasterOuterText)))
	i += copy(dAtA[i:], m.MasterOuterText)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ApprenticeInnerText)))
	i += copy(dAtA[i:], m.ApprenticeInnerText)
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ApprenticeOuterText)))
	i += copy(dAtA[i:], m.ApprenticeOuterText)
	return i, nil
}

func (m *MasterApprenticeEstablishMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MasterApprenticeEstablishMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MasterInnerText)))
	i += copy(dAtA[i:], m.MasterInnerText)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MasterOuterText)))
	i += copy(dAtA[i:], m.MasterOuterText)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ApprenticeInnerText)))
	i += copy(dAtA[i:], m.ApprenticeInnerText)
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ApprenticeOuterText)))
	i += copy(dAtA[i:], m.ApprenticeOuterText)
	return i, nil
}

func (m *MasterApprenticeIntroductionMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MasterApprenticeIntroductionMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Text_1)))
	i += copy(dAtA[i:], m.Text_1)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Text_2)))
	i += copy(dAtA[i:], m.Text_2)
	return i, nil
}

func (m *ChatCardMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChatCardMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.HiText)))
	i += copy(dAtA[i:], m.HiText)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.TopicText)))
	i += copy(dAtA[i:], m.TopicText)
	if m.Card != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintIm(dAtA, i, uint64(len(m.Card)))
		i += copy(dAtA[i:], m.Card)
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.DressId))
	return i, nil
}

func (m *ChannelOpenGameInviteMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelOpenGameInviteMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Desc)))
	i += copy(dAtA[i:], m.Desc)
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ImgUrl)))
	i += copy(dAtA[i:], m.ImgUrl)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x32
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.FromOutMsg)))
	i += copy(dAtA[i:], m.FromOutMsg)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ToOutMsg)))
	i += copy(dAtA[i:], m.ToOutMsg)
	return i, nil
}

func (m *FellowInviteMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FellowInviteMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.InviteId)))
	i += copy(dAtA[i:], m.InviteId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.InviteStatus))
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.PresentId))
	dAtA[i] = 0x20
	i++
	if m.IsUnlock {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.FellowBindType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.FellowType))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.FellowName)))
	i += copy(dAtA[i:], m.FellowName)
	dAtA[i] = 0x42
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.PresentUrl)))
	i += copy(dAtA[i:], m.PresentUrl)
	dAtA[i] = 0x48
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.FromBindType))
	dAtA[i] = 0x50
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.FromFellowType))
	dAtA[i] = 0x5a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.FromFellowName)))
	i += copy(dAtA[i:], m.FromFellowName)
	return i, nil
}

func (m *LevelAwardItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LevelAwardItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ItemName)))
	i += copy(dAtA[i:], m.ItemName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ItemId)))
	i += copy(dAtA[i:], m.ItemId)
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ItemCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.DayCount))
	dAtA[i] = 0x32
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ItemIcon)))
	i += copy(dAtA[i:], m.ItemIcon)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ItemCountInfo)))
	i += copy(dAtA[i:], m.ItemCountInfo)
	dAtA[i] = 0x42
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ItemTypeName)))
	i += copy(dAtA[i:], m.ItemTypeName)
	return i, nil
}

func (m *FellowNextLevelAwardMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FellowNextLevelAwardMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.NextAwardLevel)))
	i += copy(dAtA[i:], m.NextAwardLevel)
	if len(m.AwardList) > 0 {
		for _, msg := range m.AwardList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintIm(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FellowUpgradeMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FellowUpgradeMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.FellowBindType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.FellowName)))
	i += copy(dAtA[i:], m.FellowName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Level))
	if m.NextAwardMsg != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.NextAwardMsg.Size()))
		n32, err := m.NextAwardMsg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n32
	}
	return i, nil
}

func (m *GainRareUser) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GainRareUser) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.RareName)))
	i += copy(dAtA[i:], m.RareName)
	dAtA[i] = 0x28
	i++
	if m.IsMvp {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	if m.IsCharm {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GainRareMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GainRareMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Users) > 0 {
		for _, msg := range m.Users {
			dAtA[i] = 0xa
			i++
			i = encodeVarintIm(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	if m.IsBind {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ThumbBackground)))
	i += copy(dAtA[i:], m.ThumbBackground)
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Background)))
	i += copy(dAtA[i:], m.Background)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.RareDays)))
	i += copy(dAtA[i:], m.RareDays)
	dAtA[i] = 0x32
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.RareName)))
	i += copy(dAtA[i:], m.RareName)
	dAtA[i] = 0x38
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x40
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *SystemNotifyMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SystemNotifyMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.HighlightContent)))
	i += copy(dAtA[i:], m.HighlightContent)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	return i, nil
}

func (m *ShareGameMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShareGameMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ImgUrl)))
	i += copy(dAtA[i:], m.ImgUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	return i, nil
}

func (m *MiJingUserTag) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MiJingUserTag) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.MatchingDegree)))
	i += copy(dAtA[i:], m.MatchingDegree)
	return i, nil
}

func (m *UserRecallComeback) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserRecallComeback) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Hlight)))
	i += copy(dAtA[i:], m.Hlight)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	return i, nil
}

func (m *GetImGuideTriggerInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetImGuideTriggerInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseReq.Size()))
		n33, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n33
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	return i, nil
}

func (m *GetImGuideTriggerInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetImGuideTriggerInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto4.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintIm(dAtA, i, uint64(m.BaseResp.Size()))
		n34, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n34
	}
	if len(m.TriggerItemList) > 0 {
		for _, msg := range m.TriggerItemList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintIm(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *TriggerItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TriggerItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.Creator))
	dAtA[i] = 0x28
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.TabId))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.GamePlayName)))
	i += copy(dAtA[i:], m.GamePlayName)
	dAtA[i] = 0x40
	i++
	if m.IsOnline {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *IMCommonCardMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IMCommonCardMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.ImgUrl)))
	i += copy(dAtA[i:], m.ImgUrl)
	return i, nil
}

func (m *IMCommonXmlMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IMCommonXmlMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.DisplayType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintIm(dAtA, i, uint64(len(m.XmlContent)))
	i += copy(dAtA[i:], m.XmlContent)
	dAtA[i] = 0x18
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.DressId))
	if m.Payload != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintIm(dAtA, i, uint64(len(m.Payload)))
		i += copy(dAtA[i:], m.Payload)
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintIm(dAtA, i, uint64(m.PayloadType))
	return i, nil
}

func encodeFixed64Im(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Im(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintIm(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *RichTextWords) Size() (n int) {
	var l int
	_ = l
	l = len(m.Text)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *RichTextImage) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovIm(uint64(l))
	n += 5
	n += 5
	return n
}

func (m *RichTextLink) Size() (n int) {
	var l int
	_ = l
	l = len(m.Text)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.TextColor))
	l = len(m.JumpUrl)
	n += 1 + l + sovIm(uint64(l))
	if m.JumpUrlPbContent != nil {
		l = len(m.JumpUrlPbContent)
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}

func (m *RichTextElement) Size() (n int) {
	var l int
	_ = l
	if m.Content != nil {
		n += m.Content.Size()
	}
	return n
}

func (m *RichTextElement_Words) Size() (n int) {
	var l int
	_ = l
	if m.Words != nil {
		l = m.Words.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}
func (m *RichTextElement_Image) Size() (n int) {
	var l int
	_ = l
	if m.Image != nil {
		l = m.Image.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}
func (m *RichTextElement_Link) Size() (n int) {
	var l int
	_ = l
	if m.Link != nil {
		l = m.Link.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}
func (m *RichTextMsg) Size() (n int) {
	var l int
	_ = l
	if len(m.InValue) > 0 {
		for _, e := range m.InValue {
			l = e.Size()
			n += 1 + l + sovIm(uint64(l))
		}
	}
	if len(m.OutValue) > 0 {
		for _, e := range m.OutValue {
			l = e.Size()
			n += 1 + l + sovIm(uint64(l))
		}
	}
	n += 1 + sovIm(uint64(m.Level))
	n += 1 + sovIm(uint64(m.Type))
	return n
}

func (m *PersonalRichTextElement) Size() (n int) {
	var l int
	_ = l
	if len(m.Account) > 0 {
		for _, s := range m.Account {
			l = len(s)
			n += 1 + l + sovIm(uint64(l))
		}
	}
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}

func (m *PersonalRichTextMsg) Size() (n int) {
	var l int
	_ = l
	if len(m.Personal) > 0 {
		for _, e := range m.Personal {
			l = e.Size()
			n += 1 + l + sovIm(uint64(l))
		}
	}
	if m.Default != nil {
		l = m.Default.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}

func (m *LinkJumpURL) Size() (n int) {
	var l int
	_ = l
	if len(m.JumpUrlMap) > 0 {
		for k, v := range m.JumpUrlMap {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovIm(uint64(len(k))) + 1 + len(v) + sovIm(uint64(len(v)))
			n += mapEntrySize + 1 + sovIm(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *SessionCreateMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.SessionId))
	return n
}

func (m *SessionCloseMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.SessionId))
	return n
}

func (m *CueMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.CueId))
	l = len(m.MsgPicLeft)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.MsgPicRight)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.PreviewText)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.LottieUrl)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *TextMsgExt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.DressId))
	return n
}

func (m *VoiceMsgExt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.DressId))
	return n
}

func (m *SendMsgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	l = len(m.TargetName)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.Type))
	l = len(m.Content)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.ClientMsgId))
	n += 1 + sovIm(uint64(m.ClientMsgTime))
	if m.Thumb != nil {
		l = len(m.Thumb)
		n += 1 + l + sovIm(uint64(l))
	}
	n += 2
	l = len(m.MyLoginKey)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.Origin))
	if m.Ext != nil {
		l = len(m.Ext)
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.MsgSourceType))
	n += 2
	n += 1 + sovIm(uint64(m.MsgLabel))
	return n
}

func (m *SendMsgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.ClientMsgId))
	n += 1 + sovIm(uint64(m.SvrMsgId))
	n += 1 + sovIm(uint64(m.SvrMsgTime))
	l = len(m.AttachmentKey)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.TargetName)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.MyLoginKey)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.ExceedTime))
	n += 1 + sovIm(uint64(m.Origin))
	n += 1 + sovIm(uint64(m.TargetMsgId))
	n += 1 + sovIm(uint64(m.TargetUid))
	return n
}

func (m *UploadAttachmentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.SvrMsgId))
	l = len(m.AttachmentKey)
	n += 1 + l + sovIm(uint64(l))
	if m.Attachment != nil {
		l = len(m.Attachment)
		n += 1 + l + sovIm(uint64(l))
	}
	if m.AttachmentProperty != nil {
		l = len(m.AttachmentProperty)
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}

func (m *UploadAttachmentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.SvrMsgId))
	return n
}

func (m *DownloadAttachmentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.SvrMsgId))
	l = len(m.AttachmentKey)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *DownloadAttachmentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.SvrMsgId))
	l = len(m.AttachmentKey)
	n += 1 + l + sovIm(uint64(l))
	if m.Attachment != nil {
		l = len(m.Attachment)
		n += 1 + l + sovIm(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *DeleteMessageReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.MsgId))
	return n
}

func (m *DeleteMessageResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}

func (m *MarkMsgReadReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	l = len(m.TargetName)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.SvrMsgId))
	n += 1 + sovIm(uint64(m.PeerSvrMsgId))
	return n
}

func (m *MarkMsgReadResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	l = len(m.TargetName)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.SvrMsgId))
	n += 1 + sovIm(uint64(m.PeerReadSvrMsgId))
	return n
}

func (m *UpdateMsgSettingReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	l = len(m.Account)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.ReceiveSetting))
	return n
}

func (m *UpdateMsgSettingResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}

func (m *QueryMsgSettingReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	l = len(m.Account)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *MsgReceiveSettingItem) Size() (n int) {
	var l int
	_ = l
	l = len(m.Account)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.ReceiveSetting))
	return n
}

func (m *QueryMsgSettingResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	if len(m.SettingItemList) > 0 {
		for _, e := range m.SettingItemList {
			l = e.Size()
			n += 1 + l + sovIm(uint64(l))
		}
	}
	return n
}

func (m *CheckSendAtEveryoneGroupMsgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.GroupId))
	return n
}

func (m *CheckSendAtEveryoneGroupMsgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.RemainCnt))
	n += 2
	return n
}

func (m *CancelMsgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	l = len(m.TargetName)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.SvrMsgId))
	n += 1 + sovIm(uint64(m.TargetMsgId))
	n += 1 + sovIm(uint64(m.ClientMsgId))
	n += 1 + sovIm(uint64(m.ClientMsgTime))
	l = len(m.MyLoginKey)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *CancelMsgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}

func (m *MessageReadByPeerMessage) Size() (n int) {
	var l int
	_ = l
	l = len(m.PeerName)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.SvrMsgId))
	return n
}

func (m *GetMessagePeerReadStatusReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	if len(m.AccountList) > 0 {
		for _, s := range m.AccountList {
			l = len(s)
			n += 1 + l + sovIm(uint64(l))
		}
	}
	return n
}

func (m *MessagePeerReadStatus) Size() (n int) {
	var l int
	_ = l
	l = len(m.Account)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.SvrMsgId))
	return n
}

func (m *GetMessagePeerReadStatusResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	if len(m.PeerReadStatusList) > 0 {
		for _, e := range m.PeerReadStatusList {
			l = e.Size()
			n += 1 + l + sovIm(uint64(l))
		}
	}
	return n
}

func (m *DelMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.TargetName)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.SvrMsgId))
	n += 1 + sovIm(uint64(m.TargetMsgId))
	n += 1 + sovIm(uint64(m.ClientMsgId))
	n += 1 + sovIm(uint64(m.ClientMsgTime))
	l = len(m.MyLoginKey)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.FromName)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *BatchDelMsgReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	if len(m.DelMsg) > 0 {
		for _, e := range m.DelMsg {
			l = e.Size()
			n += 1 + l + sovIm(uint64(l))
		}
	}
	return n
}

func (m *BatchDelMsgResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}

func (m *PopGameJumpMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.MsgType))
	n += 1 + sovIm(uint64(m.GameId))
	l = len(m.AreaUrl)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.IconUrl)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.DownloadUrl)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.GameSize)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.GamePktName)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.PreorderUrl)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.GameName)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *EmojiMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.Id)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.Height))
	n += 1 + sovIm(uint64(m.Width))
	n += 1 + sovIm(uint64(m.EmojiType))
	l = len(m.Keyword)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Md5)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.ThumbH))
	n += 1 + sovIm(uint64(m.ThumbW))
	n += 2
	l = len(m.ObsKey)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *InteractiveUnreadMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.Type))
	n += 1 + sovIm(uint64(m.Time))
	n += 1 + sovIm(uint64(m.FromUserId))
	l = len(m.FromUserNickname)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.CommentCount))
	n += 1 + sovIm(uint64(m.AttitudeCount))
	return n
}

func (m *RichTextPresent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.Id))
	n += 1 + sovIm(uint64(m.Type))
	l = len(m.Url)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Head)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Text)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.SourceType))
	n += 2
	n += 2
	n += 1 + sovIm(uint64(m.TargetUid))
	l = len(m.Name)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.PriceType))
	n += 1 + sovIm(uint64(m.Price))
	return n
}

func (m *RichTextWithPresentMsg) Size() (n int) {
	var l int
	_ = l
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	if m.Present != nil {
		l = m.Present.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	if m.ItemInfo != nil {
		l = m.ItemInfo.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.ImPresentType))
	return n
}

func (m *MasterApprenticeInviteMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.MasterInnerText)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.MasterOuterText)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ApprenticeInnerText)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ApprenticeOuterText)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *MasterApprenticeEstablishMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.MasterInnerText)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.MasterOuterText)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ApprenticeInnerText)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ApprenticeOuterText)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *MasterApprenticeIntroductionMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.Text_1)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Text_2)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *ChatCardMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.HiText)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.TopicText)
	n += 1 + l + sovIm(uint64(l))
	if m.Card != nil {
		l = len(m.Card)
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.DressId))
	return n
}

func (m *ChannelOpenGameInviteMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.IconUrl)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Desc)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ImgUrl)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.FromOutMsg)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ToOutMsg)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *FellowInviteMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.InviteId)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.InviteStatus))
	n += 1 + sovIm(uint64(m.PresentId))
	n += 2
	n += 1 + sovIm(uint64(m.FellowBindType))
	n += 1 + sovIm(uint64(m.FellowType))
	l = len(m.FellowName)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.PresentUrl)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.FromBindType))
	n += 1 + sovIm(uint64(m.FromFellowType))
	l = len(m.FromFellowName)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *LevelAwardItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.ItemType))
	l = len(m.ItemName)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ItemId)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.ItemCount))
	n += 1 + sovIm(uint64(m.DayCount))
	l = len(m.ItemIcon)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ItemCountInfo)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ItemTypeName)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *FellowNextLevelAwardMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.NextAwardLevel)
	n += 1 + l + sovIm(uint64(l))
	if len(m.AwardList) > 0 {
		for _, e := range m.AwardList {
			l = e.Size()
			n += 1 + l + sovIm(uint64(l))
		}
	}
	return n
}

func (m *FellowUpgradeMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.FellowBindType))
	l = len(m.FellowName)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.Level))
	if m.NextAwardMsg != nil {
		l = m.NextAwardMsg.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	return n
}

func (m *GainRareUser) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.RareName)
	n += 1 + l + sovIm(uint64(l))
	n += 2
	n += 2
	return n
}

func (m *GainRareMsg) Size() (n int) {
	var l int
	_ = l
	if len(m.Users) > 0 {
		for _, e := range m.Users {
			l = e.Size()
			n += 1 + l + sovIm(uint64(l))
		}
	}
	n += 2
	l = len(m.ThumbBackground)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Background)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.RareDays)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.RareName)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.Timestamp))
	n += 1 + sovIm(uint64(m.ChannelId))
	return n
}

func (m *SystemNotifyMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.HighlightContent)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *ShareGameMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.GameName)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ImgUrl)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *MiJingUserTag) Size() (n int) {
	var l int
	_ = l
	l = len(m.MatchingDegree)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *UserRecallComeback) Size() (n int) {
	var l int
	_ = l
	l = len(m.Content)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Hlight)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *GetImGuideTriggerInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	l = len(m.Account)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.TargetAccount)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *GetImGuideTriggerInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovIm(uint64(l))
	}
	if len(m.TriggerItemList) > 0 {
		for _, e := range m.TriggerItemList {
			l = e.Size()
			n += 1 + l + sovIm(uint64(l))
		}
	}
	return n
}

func (m *TriggerItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.Cid))
	n += 1 + sovIm(uint64(m.Creator))
	n += 1 + sovIm(uint64(m.ChannelType))
	n += 1 + sovIm(uint64(m.TabId))
	l = len(m.GamePlayName)
	n += 1 + l + sovIm(uint64(l))
	n += 2
	return n
}

func (m *IMCommonCardMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.Title)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.JumpUrl)
	n += 1 + l + sovIm(uint64(l))
	l = len(m.ImgUrl)
	n += 1 + l + sovIm(uint64(l))
	return n
}

func (m *IMCommonXmlMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovIm(uint64(m.DisplayType))
	l = len(m.XmlContent)
	n += 1 + l + sovIm(uint64(l))
	n += 1 + sovIm(uint64(m.DressId))
	if m.Payload != nil {
		l = len(m.Payload)
		n += 1 + l + sovIm(uint64(l))
	}
	n += 1 + sovIm(uint64(m.PayloadType))
	return n
}

func sovIm(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozIm(x uint64) (n int) {
	return sovIm(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RichTextWords) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: RichTextWords: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: RichTextWords: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("text")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichTextImage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: RichTextImage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: RichTextImage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 5 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += 4
			v = uint32(dAtA[iNdEx-4])
			v |= uint32(dAtA[iNdEx-3]) << 8
			v |= uint32(dAtA[iNdEx-2]) << 16
			v |= uint32(dAtA[iNdEx-1]) << 24
			m.Width = float32(math6.Float32frombits(v))
		case 3:
			if wireType != 5 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += 4
			v = uint32(dAtA[iNdEx-4])
			v |= uint32(dAtA[iNdEx-3]) << 8
			v |= uint32(dAtA[iNdEx-2]) << 16
			v |= uint32(dAtA[iNdEx-1]) << 24
			m.Height = float32(math6.Float32frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichTextLink) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: RichTextLink: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: RichTextLink: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TextColor", wireType)
			}
			m.TextColor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TextColor |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field JumpUrlPbContent", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.JumpUrlPbContent = append(m.JumpUrlPbContent[:0], dAtA[iNdEx:postIndex]...)
			if m.JumpUrlPbContent == nil {
				m.JumpUrlPbContent = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("text")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichTextElement) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: RichTextElement: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: RichTextElement: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Words", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			v := &RichTextWords{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Content = &RichTextElement_Words{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Image", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			v := &RichTextImage{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Content = &RichTextElement_Image{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Link", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			v := &RichTextLink{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Content = &RichTextElement_Link{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichTextMsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: RichTextMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: RichTextMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field InValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.InValue = append(m.InValue, &RichTextElement{})
			if err := m.InValue[len(m.InValue)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field OutValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.OutValue = append(m.OutValue, &RichTextElement{})
			if err := m.OutValue[len(m.OutValue)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (RichTextMsg_RichTextMsgLevel(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (RichTextMsg_RichTextMsgType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *PersonalRichTextElement) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: PersonalRichTextElement: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: PersonalRichTextElement: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Account = append(m.Account, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &RichTextMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *PersonalRichTextMsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: PersonalRichTextMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: PersonalRichTextMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Personal", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Personal = append(m.Personal, &PersonalRichTextElement{})
			if err := m.Personal[len(m.Personal)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Default", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.Default == nil {
				m.Default = &RichTextMsg{}
			}
			if err := m.Default.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *LinkJumpURL) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: LinkJumpURL: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: LinkJumpURL: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field JumpUrlMap", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			var keykey uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				keykey |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			var stringLenmapkey uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLenmapkey |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLenmapkey := int(stringLenmapkey)
			if intStringLenmapkey < 0 {
				return ErrInvalidLengthIm
			}
			postStringIndexmapkey := iNdEx + intStringLenmapkey
			if postStringIndexmapkey > l {
				return io2.ErrUnexpectedEOF
			}
			mapkey := string(dAtA[iNdEx:postStringIndexmapkey])
			iNdEx = postStringIndexmapkey
			if m.JumpUrlMap == nil {
				m.JumpUrlMap = make(map[string]string)
			}
			if iNdEx < postIndex {
				var valuekey uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowIm
					}
					if iNdEx >= l {
						return io2.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					valuekey |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				var stringLenmapvalue uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowIm
					}
					if iNdEx >= l {
						return io2.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLenmapvalue |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLenmapvalue := int(stringLenmapvalue)
				if intStringLenmapvalue < 0 {
					return ErrInvalidLengthIm
				}
				postStringIndexmapvalue := iNdEx + intStringLenmapvalue
				if postStringIndexmapvalue > l {
					return io2.ErrUnexpectedEOF
				}
				mapvalue := string(dAtA[iNdEx:postStringIndexmapvalue])
				iNdEx = postStringIndexmapvalue
				m.JumpUrlMap[mapkey] = mapvalue
			} else {
				var mapvalue string
				m.JumpUrlMap[mapkey] = mapvalue
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *SessionCreateMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: SessionCreateMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: SessionCreateMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			m.SessionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SessionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("session_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *SessionCloseMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: SessionCloseMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: SessionCloseMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			m.SessionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SessionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("session_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *CueMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: CueMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: CueMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field CueId", wireType)
			}
			m.CueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MsgPicLeft", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MsgPicLeft = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MsgPicRight", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MsgPicRight = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PreviewText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.PreviewText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field LottieUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.LottieUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("cue_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("msg_pic_left")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("msg_pic_right")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("preview_text")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("lottie_url")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *TextMsgExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: TextMsgExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: TextMsgExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field DressId", wireType)
			}
			m.DressId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DressId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoiceMsgExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: VoiceMsgExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: VoiceMsgExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field DressId", wireType)
			}
			m.DressId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DressId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: SendMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: SendMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TargetName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ClientMsgId", wireType)
			}
			m.ClientMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ClientMsgTime", wireType)
			}
			m.ClientMsgTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Thumb", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Thumb = append(m.Thumb[:0], dAtA[iNdEx:postIndex]...)
			if m.Thumb == nil {
				m.Thumb = []byte{}
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field HasAttachment", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasAttachment = bool(v != 0)
			hasFields[0] |= uint64(0x00000040)
		case 9:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MyLoginKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MyLoginKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Origin", wireType)
			}
			m.Origin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Origin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Ext", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Ext = append(m.Ext[:0], dAtA[iNdEx:postIndex]...)
			if m.Ext == nil {
				m.Ext = []byte{}
			}
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MsgSourceType", wireType)
			}
			m.MsgSourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field DoNotCheckText", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DoNotCheckText = bool(v != 0)
		case 14:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MsgLabel", wireType)
			}
			m.MsgLabel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgLabel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("client_msg_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("client_msg_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("has_attachment")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("my_login_key")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendMsgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: SendMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: SendMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ClientMsgId", wireType)
			}
			m.ClientMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgTime", wireType)
			}
			m.SvrMsgTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field AttachmentKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.AttachmentKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TargetName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MyLoginKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MyLoginKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ExceedTime", wireType)
			}
			m.ExceedTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExceedTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Origin", wireType)
			}
			m.Origin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Origin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetMsgId", wireType)
			}
			m.TargetMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("client_msg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_name")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("my_login_key")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *UploadAttachmentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: UploadAttachmentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: UploadAttachmentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field AttachmentKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.AttachmentKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Attachment", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Attachment = append(m.Attachment[:0], dAtA[iNdEx:postIndex]...)
			if m.Attachment == nil {
				m.Attachment = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field AttachmentProperty", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.AttachmentProperty = append(m.AttachmentProperty[:0], dAtA[iNdEx:postIndex]...)
			if m.AttachmentProperty == nil {
				m.AttachmentProperty = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("attachment_key")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("attachment")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("attachment_property")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *UploadAttachmentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: UploadAttachmentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: UploadAttachmentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownloadAttachmentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: DownloadAttachmentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: DownloadAttachmentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field AttachmentKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.AttachmentKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("attachment_key")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownloadAttachmentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: DownloadAttachmentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: DownloadAttachmentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field AttachmentKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.AttachmentKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Attachment", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Attachment = append(m.Attachment[:0], dAtA[iNdEx:postIndex]...)
			if m.Attachment == nil {
				m.Attachment = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("attachment_key")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("attachment")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteMessageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: DeleteMessageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: DeleteMessageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MsgId", wireType)
			}
			m.MsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("msg_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteMessageResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: DeleteMessageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: DeleteMessageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkMsgReadReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: MarkMsgReadReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: MarkMsgReadReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TargetName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PeerSvrMsgId", wireType)
			}
			m.PeerSvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerSvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkMsgReadResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: MarkMsgReadResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: MarkMsgReadResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TargetName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PeerReadSvrMsgId", wireType)
			}
			m.PeerReadSvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerReadSvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateMsgSettingReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: UpdateMsgSettingReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: UpdateMsgSettingReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ReceiveSetting", wireType)
			}
			m.ReceiveSetting = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiveSetting |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("receive_setting")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateMsgSettingResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: UpdateMsgSettingResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: UpdateMsgSettingResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryMsgSettingReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: QueryMsgSettingReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: QueryMsgSettingReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *MsgReceiveSettingItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: MsgReceiveSettingItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: MsgReceiveSettingItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ReceiveSetting", wireType)
			}
			m.ReceiveSetting = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceiveSetting |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("receive_setting")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryMsgSettingResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: QueryMsgSettingResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: QueryMsgSettingResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SettingItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.SettingItemList = append(m.SettingItemList, &MsgReceiveSettingItem{})
			if err := m.SettingItemList[len(m.SettingItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckSendAtEveryoneGroupMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: CheckSendAtEveryoneGroupMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: CheckSendAtEveryoneGroupMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckSendAtEveryoneGroupMsgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: CheckSendAtEveryoneGroupMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: CheckSendAtEveryoneGroupMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field RemainCnt", wireType)
			}
			m.RemainCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IsAllowAtEveryone", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAllowAtEveryone = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("remain_cnt")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("is_allow_at_everyone")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: CancelMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: CancelMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TargetName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetMsgId", wireType)
			}
			m.TargetMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ClientMsgId", wireType)
			}
			m.ClientMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ClientMsgTime", wireType)
			}
			m.ClientMsgTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MyLoginKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MyLoginKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_msg_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("client_msg_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("client_msg_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("my_login_key")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelMsgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: CancelMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: CancelMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *MessageReadByPeerMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: MessageReadByPeerMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: MessageReadByPeerMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PeerName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.PeerName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("peer_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMessagePeerReadStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: GetMessagePeerReadStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: GetMessagePeerReadStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field AccountList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.AccountList = append(m.AccountList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *MessagePeerReadStatus) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: MessagePeerReadStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: MessagePeerReadStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMessagePeerReadStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: GetMessagePeerReadStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: GetMessagePeerReadStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PeerReadStatusList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.PeerReadStatusList = append(m.PeerReadStatusList, &MessagePeerReadStatus{})
			if err := m.PeerReadStatusList[len(m.PeerReadStatusList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: DelMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: DelMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TargetName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetMsgId", wireType)
			}
			m.TargetMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ClientMsgId", wireType)
			}
			m.ClientMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ClientMsgTime", wireType)
			}
			m.ClientMsgTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MyLoginKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MyLoginKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FromName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.FromName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("svr_msg_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_msg_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("client_msg_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("client_msg_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("my_login_key")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("from_name")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDelMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: BatchDelMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: BatchDelMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field DelMsg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.DelMsg = append(m.DelMsg, &DelMsg{})
			if err := m.DelMsg[len(m.DelMsg)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDelMsgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: BatchDelMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: BatchDelMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *PopGameJumpMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: PopGameJumpMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: PopGameJumpMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MsgType", wireType)
			}
			m.MsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field AreaUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.AreaUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field DownloadUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.DownloadUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field GameSize", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.GameSize = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field GamePktName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.GamePktName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PreorderUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.PreorderUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("msg_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *EmojiMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: EmojiMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: EmojiMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			m.Height = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Height |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Width", wireType)
			}
			m.Width = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Width |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field EmojiType", wireType)
			}
			m.EmojiType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EmojiType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Keyword", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Keyword = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Md5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Md5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ThumbH", wireType)
			}
			m.ThumbH = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThumbH |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ThumbW", wireType)
			}
			m.ThumbW = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThumbW |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IsObs", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsObs = bool(v != 0)
		case 11:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ObsKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ObsKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("height")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("width")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *InteractiveUnreadMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: InteractiveUnreadMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: InteractiveUnreadMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (InteractiveUnreadMsg_InteractiveType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Time", wireType)
			}
			m.Time = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Time |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FromUserId", wireType)
			}
			m.FromUserId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromUserId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FromUserNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.FromUserNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field CommentCount", wireType)
			}
			m.CommentCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentCount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field AttitudeCount", wireType)
			}
			m.AttitudeCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AttitudeCount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("from_user_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("from_user_nickname")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("comment_count")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("attitude_count")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichTextPresent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: RichTextPresent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: RichTextPresent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Head", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Head = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Text", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Text = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ShowSendBtn", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShowSendBtn = bool(v != 0)
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IsDouble", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDouble = bool(v != 0)
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PriceType", wireType)
			}
			m.PriceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PriceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("head")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("text")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("source_type")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("show_send_btn")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("is_double")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *RichTextWithPresentMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: RichTextWithPresentMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: RichTextWithPresentMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &PersonalRichTextMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Present", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.Present == nil {
				m.Present = &RichTextPresent{}
			}
			if err := m.Present.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ItemInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.ItemInfo == nil {
				m.ItemInfo = &ga_userpresent.PresentSendItemInfo{}
			}
			if err := m.ItemInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ImPresentType", wireType)
			}
			m.ImPresentType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ImPresentType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("msg")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("present")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *MasterApprenticeInviteMsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: MasterApprenticeInviteMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: MasterApprenticeInviteMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MasterInnerText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MasterInnerText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MasterOuterText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MasterOuterText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ApprenticeInnerText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ApprenticeInnerText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ApprenticeOuterText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ApprenticeOuterText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *MasterApprenticeEstablishMsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: MasterApprenticeEstablishMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: MasterApprenticeEstablishMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MasterInnerText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MasterInnerText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MasterOuterText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MasterOuterText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ApprenticeInnerText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ApprenticeInnerText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ApprenticeOuterText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ApprenticeOuterText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *MasterApprenticeIntroductionMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: MasterApprenticeIntroductionMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: MasterApprenticeIntroductionMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Text_1", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Text_1 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Text_2", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Text_2 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("text_1")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("text_2")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChatCardMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: ChatCardMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: ChatCardMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field HiText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.HiText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TopicText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TopicText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Card", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Card = append(m.Card[:0], dAtA[iNdEx:postIndex]...)
			if m.Card == nil {
				m.Card = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field DressId", wireType)
			}
			m.DressId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DressId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("hi_text")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("topic_text")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("card")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelOpenGameInviteMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: ChannelOpenGameInviteMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: ChannelOpenGameInviteMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FromOutMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.FromOutMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ToOutMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ToOutMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("icon_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("desc")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("img_url")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("jump_url")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *FellowInviteMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: FellowInviteMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: FellowInviteMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field InviteId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.InviteId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field InviteStatus", wireType)
			}
			m.InviteStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InviteStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PresentId", wireType)
			}
			m.PresentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PresentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IsUnlock", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsUnlock = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FellowBindType", wireType)
			}
			m.FellowBindType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FellowBindType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FellowType", wireType)
			}
			m.FellowType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FellowType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FellowName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.FellowName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PresentUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.PresentUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FromBindType", wireType)
			}
			m.FromBindType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromBindType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FromFellowType", wireType)
			}
			m.FromFellowType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromFellowType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FromFellowName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.FromFellowName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("invite_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("invite_status")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("present_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("is_unlock")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("fellow_bind_type")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("fellow_type")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("fellow_name")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("present_url")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *LevelAwardItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: LevelAwardItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: LevelAwardItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ItemId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field DayCount", wireType)
			}
			m.DayCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ItemIcon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ItemIcon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ItemCountInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ItemCountInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ItemTypeName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ItemTypeName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("item_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("item_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("item_icon")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *FellowNextLevelAwardMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: FellowNextLevelAwardMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: FellowNextLevelAwardMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field NextAwardLevel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.NextAwardLevel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field AwardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.AwardList = append(m.AwardList, &LevelAwardItem{})
			if err := m.AwardList[len(m.AwardList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("next_award_level")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *FellowUpgradeMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: FellowUpgradeMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: FellowUpgradeMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FellowBindType", wireType)
			}
			m.FellowBindType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FellowBindType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field FellowName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.FellowName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field NextAwardMsg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.NextAwardMsg == nil {
				m.NextAwardMsg = &FellowNextLevelAwardMsg{}
			}
			if err := m.NextAwardMsg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("fellow_bind_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("fellow_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("level")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *GainRareUser) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: GainRareUser: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: GainRareUser: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field RareName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.RareName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IsMvp", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMvp = bool(v != 0)
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IsCharm", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCharm = bool(v != 0)
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("rare_name")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("is_mvp")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("is_charm")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *GainRareMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: GainRareMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: GainRareMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Users", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Users = append(m.Users, &GainRareUser{})
			if err := m.Users[len(m.Users)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IsBind", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBind = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ThumbBackground", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ThumbBackground = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Background", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Background = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field RareDays", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.RareDays = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field RareName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.RareName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("is_bind")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("thumb_background")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("background")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("rare_days")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("rare_name")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("timestamp")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *SystemNotifyMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: SystemNotifyMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: SystemNotifyMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field HighlightContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.HighlightContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("highlight_content")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("jump_url")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShareGameMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: ShareGameMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: ShareGameMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("game_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("img_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("jump_url")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *MiJingUserTag) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: MiJingUserTag: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: MiJingUserTag: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field MatchingDegree", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.MatchingDegree = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("matching_degree")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserRecallComeback) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: UserRecallComeback: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: UserRecallComeback: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Hlight", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Hlight = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("hlight")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetImGuideTriggerInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: GetImGuideTriggerInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: GetImGuideTriggerInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetImGuideTriggerInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: GetImGuideTriggerInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: GetImGuideTriggerInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TriggerItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.TriggerItemList = append(m.TriggerItemList, &TriggerItem{})
			if err := m.TriggerItemList[len(m.TriggerItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *TriggerItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: TriggerItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: TriggerItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			m.Creator = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Creator |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field TabId", wireType)
			}
			m.TabId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TabId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field GamePlayName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.GamePlayName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field IsOnline", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOnline = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *IMCommonCardMsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: IMCommonCardMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: IMCommonCardMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field ImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.ImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func (m *IMCommonXmlMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowIm
			}
			if iNdEx >= l {
				return io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt3.Errorf("proto: IMCommonXmlMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt3.Errorf("proto: IMCommonXmlMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field DisplayType", wireType)
			}
			m.DisplayType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DisplayType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field XmlContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.XmlContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field DressId", wireType)
			}
			m.DressId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DressId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt3.Errorf("proto: wrong wireType = %d for field Payload", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthIm
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io2.ErrUnexpectedEOF
			}
			m.Payload = append(m.Payload[:0], dAtA[iNdEx:postIndex]...)
			if m.Payload == nil {
				m.Payload = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt3.Errorf("proto: wrong wireType = %d for field PayloadType", wireType)
			}
			m.PayloadType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowIm
				}
				if iNdEx >= l {
					return io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PayloadType |= (IMXMLPayLoadType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipIm(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthIm
			}
			if (iNdEx + skippy) > l {
				return io2.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("display_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("xml_content")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto5.NewRequiredNotSetError("dress_id")
	}

	if iNdEx > l {
		return io2.ErrUnexpectedEOF
	}
	return nil
}
func skipIm(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowIm
			}
			if iNdEx >= l {
				return 0, io2.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowIm
				}
				if iNdEx >= l {
					return 0, io2.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowIm
				}
				if iNdEx >= l {
					return 0, io2.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthIm
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowIm
					}
					if iNdEx >= l {
						return 0, io2.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipIm(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt3.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthIm = fmt3.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowIm   = fmt3.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("im/im.proto", fileDescriptorIm) }

var fileDescriptorIm = []byte{
	// 6615 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x7c, 0x5b, 0x8c, 0x23, 0x59,
	0x9a, 0x56, 0xdb, 0x79, 0x73, 0xfe, 0xce, 0x4b, 0x54, 0xd4, 0x2d, 0xbb, 0xaa, 0xba, 0x2e, 0xae,
	0xae, 0xae, 0x6b, 0x67, 0xf5, 0xe4, 0xcc, 0xec, 0x0e, 0xc3, 0x30, 0x33, 0x91, 0xe1, 0x93, 0x99,
	0x51, 0x69, 0x47, 0x78, 0xc2, 0xe1, 0xcc, 0xca, 0x9e, 0x85, 0xb3, 0x91, 0x76, 0x94, 0x33, 0xba,
	0x6c, 0x87, 0x3b, 0x22, 0xb2, 0xaa, 0x72, 0xb8, 0x0c, 0xcb, 0xc2, 0x02, 0xcb, 0x22, 0x56, 0x80,
	0x78, 0xe0, 0x01, 0x21, 0x21, 0x24, 0x24, 0x24, 0xc4, 0x33, 0x68, 0x79, 0x43, 0x5a, 0xc4, 0x0b,
	0xda, 0x07, 0xc4, 0x13, 0x42, 0x83, 0x10, 0x0f, 0xf3, 0x88, 0xc4, 0x0b, 0x2f, 0xe8, 0xff, 0xcf,
	0x09, 0xfb, 0x84, 0xed, 0xac, 0xae, 0x44, 0x33, 0x12, 0xda, 0xa7, 0x4e, 0xff, 0xff, 0x77, 0xce,
	0xf9, 0xcf, 0x7f, 0x3f, 0xe7, 0x44, 0x35, 0x94, 0xc3, 0xfe, 0xf3, 0xb0, 0xbf, 0x39, 0x8c, 0xa3,
	0x34, 0xd2, 0x17, 0xba, 0xfe, 0x66, 0xd8, 0xbf, 0xb1, 0xda, 0xf5, 0xf9, 0xb1, 0x9f, 0x04, 0x82,
	0x7a, 0xe3, 0xf6, 0x69, 0x12, 0xc4, 0xc3, 0x38, 0x48, 0x82, 0x41, 0xfa, 0x5c, 0xf9, 0x9b, 0x0b,
	0x7e, 0xe5, 0x31, 0xac, 0xba, 0x61, 0xfb, 0xc4, 0x0b, 0xde, 0xa5, 0x87, 0x51, 0xdc, 0x49, 0xf4,
	0x0d, 0x98, 0x4f, 0x83, 0x77, 0xe9, 0x46, 0xe1, 0x6e, 0xf1, 0xd1, 0xf2, 0xf6, 0xfc, 0x1f, 0xff,
	0xd7, 0x3b, 0x1f, 0xb9, 0x44, 0xa9, 0xf8, 0x63, 0xa8, 0xd5, 0xf7, 0xbb, 0x81, 0x7e, 0x0d, 0xe6,
	0x4e, 0xe3, 0x5e, 0x0e, 0x89, 0x04, 0xfd, 0x06, 0x2c, 0xbc, 0x0d, 0x3b, 0xe9, 0xc9, 0x46, 0xf1,
	0x6e, 0xe1, 0x51, 0x51, 0x72, 0x04, 0x49, 0xbf, 0x05, 0x8b, 0x27, 0x41, 0xd8, 0x3d, 0x49, 0x37,
	0xe6, 0x14, 0xa6, 0xa4, 0x55, 0xfe, 0x59, 0x01, 0x56, 0xb2, 0x35, 0x6a, 0xe1, 0xe0, 0xf5, 0xf9,
	0xd2, 0xe8, 0xf7, 0x01, 0xf0, 0xbf, 0xbc, 0x1d, 0xf5, 0xa2, 0x98, 0x56, 0x5a, 0x90, 0xfc, 0x65,
	0xa4, 0x9b, 0x48, 0xd6, 0xef, 0x40, 0xe9, 0xab, 0xd3, 0xfe, 0x90, 0xa3, 0x98, 0xb8, 0x5e, 0x36,
	0xc5, 0x12, 0x52, 0x5b, 0x71, 0x4f, 0xff, 0x36, 0x5c, 0xce, 0x00, 0x7c, 0x78, 0xcc, 0xdb, 0xd1,
	0x20, 0x0d, 0x06, 0xe9, 0xc6, 0xfc, 0xdd, 0xc2, 0xa3, 0x15, 0x89, 0xd5, 0x24, 0xb6, 0x71, 0x6c,
	0x0a, 0x2e, 0x4a, 0xb9, 0x9e, 0x49, 0xc9, 0x7a, 0x41, 0x3f, 0x18, 0xa4, 0xfa, 0x33, 0x58, 0x78,
	0x8b, 0xfa, 0xdb, 0x28, 0xdc, 0x2d, 0x3c, 0x2a, 0x6f, 0x5d, 0xd9, 0x24, 0x6b, 0x6c, 0xe6, 0x74,
	0xbb, 0x87, 0x5a, 0x20, 0x25, 0x3f, 0x83, 0x85, 0x10, 0x55, 0x48, 0x72, 0x4f, 0xa3, 0x49, 0xbd,
	0x88, 0x26, 0x90, 0xfe, 0x18, 0xe6, 0x7b, 0xe1, 0xe0, 0x35, 0xed, 0xa0, 0xbc, 0x75, 0x79, 0x02,
	0x8c, 0x7a, 0xda, 0xfb, 0xc8, 0x25, 0xc8, 0xf6, 0x32, 0x2c, 0xc9, 0x3d, 0x54, 0xfe, 0x73, 0x11,
	0xca, 0x19, 0xa6, 0x9e, 0x74, 0xf5, 0x6f, 0x41, 0x29, 0x1c, 0xf0, 0x37, 0x7e, 0xef, 0x34, 0xd8,
	0x28, 0xdc, 0x9d, 0x7b, 0x54, 0xde, 0xba, 0x36, 0x31, 0x93, 0xdc, 0x8b, 0xbb, 0x14, 0x0e, 0x0e,
	0x10, 0xa6, 0x7f, 0x1b, 0x96, 0xa3, 0xd3, 0x54, 0x8e, 0x29, 0xbe, 0x77, 0x4c, 0x29, 0x3a, 0x4d,
	0xc5, 0xa0, 0x1f, 0xc1, 0x42, 0x2f, 0x78, 0x13, 0x08, 0x85, 0xaf, 0x6d, 0xdd, 0x9f, 0x18, 0x50,
	0x4f, 0xba, 0xea, 0xdf, 0x35, 0x84, 0x66, 0x2e, 0x42, 0xe3, 0xf4, 0x1f, 0xc0, 0x7c, 0x7a, 0x36,
	0x0c, 0xc8, 0x08, 0x6b, 0x5b, 0x95, 0xf7, 0x8f, 0xf7, 0xce, 0x86, 0xc1, 0xc8, 0x2f, 0xce, 0x86,
	0x41, 0xe5, 0x21, 0x68, 0x93, 0xd3, 0xeb, 0x25, 0x98, 0xf7, 0xac, 0x46, 0x53, 0xfb, 0x48, 0x5f,
	0x86, 0x85, 0xdd, 0x96, 0x55, 0x65, 0x5a, 0xa1, 0xf2, 0x9b, 0x63, 0x23, 0xca, 0x79, 0x74, 0x80,
	0x45, 0xdb, 0x71, 0xeb, 0x46, 0x4d, 0xfb, 0x48, 0xdf, 0x80, 0x2b, 0x96, 0xed, 0x31, 0xd7, 0x30,
	0x3d, 0xcb, 0xb1, 0xb9, 0x65, 0x7b, 0x56, 0xdd, 0x30, 0x8f, 0xb4, 0x42, 0xe5, 0x08, 0xae, 0x37,
	0x82, 0x38, 0x89, 0x06, 0x7e, 0x6f, 0xd2, 0x0b, 0x36, 0x60, 0xc9, 0x6f, 0xb7, 0xa3, 0xd3, 0x41,
	0x4a, 0x2a, 0x5e, 0x76, 0xb3, 0x9f, 0xfa, 0xa7, 0x30, 0xd7, 0x4f, 0xba, 0x1b, 0xc5, 0xbb, 0xc5,
	0x47, 0xe5, 0x2d, 0x7d, 0x7a, 0x4f, 0x2e, 0xb2, 0x2b, 0x3f, 0x87, 0xcb, 0x93, 0x53, 0xa3, 0xe9,
	0xbe, 0x0f, 0xa5, 0xa1, 0x24, 0x4b, 0xd3, 0xdd, 0x96, 0x33, 0x9c, 0x23, 0x88, 0x3b, 0xc2, 0xeb,
	0xcf, 0x60, 0xa9, 0x13, 0xbc, 0xf2, 0x4f, 0x7b, 0xa9, 0x74, 0xb6, 0x59, 0x8b, 0x67, 0x90, 0xca,
	0x3f, 0x29, 0x40, 0x19, 0x1d, 0xea, 0x05, 0xfa, 0xbc, 0x5b, 0xd3, 0xab, 0xb0, 0x32, 0x8a, 0x8f,
	0xbe, 0x3f, 0x94, 0xab, 0x67, 0x36, 0x51, 0x90, 0x9b, 0x2f, 0x44, 0x94, 0xd4, 0xfd, 0x21, 0x1b,
	0xa4, 0xf1, 0x99, 0x0b, 0x5f, 0x8d, 0x08, 0x37, 0x2c, 0x58, 0x9f, 0x60, 0x63, 0xee, 0x78, 0x1d,
	0x9c, 0x51, 0xb4, 0x8c, 0x72, 0xc7, 0xeb, 0xe0, 0x0c, 0x73, 0x47, 0xe6, 0x6e, 0x63, 0x8e, 0x20,
	0x7d, 0xbf, 0xf8, 0x3d, 0xb4, 0x9a, 0xd6, 0x0c, 0x92, 0x24, 0x8c, 0x06, 0x66, 0x1c, 0xf8, 0x69,
	0x80, 0xea, 0xb9, 0x0f, 0x90, 0x08, 0x1a, 0x0f, 0x3b, 0x94, 0x2a, 0x56, 0xb3, 0x54, 0x20, 0xe9,
	0x56, 0xa7, 0xf2, 0x1b, 0xb0, 0x9e, 0x0d, 0xec, 0x45, 0xc9, 0x87, 0x8f, 0xfb, 0xf7, 0x05, 0x58,
	0x34, 0x4f, 0x09, 0x7f, 0x13, 0x16, 0xdb, 0xa7, 0xc1, 0x24, 0x76, 0xa1, 0x7d, 0x1a, 0x58, 0x1d,
	0xfd, 0x33, 0x58, 0xe9, 0x27, 0x5d, 0x3e, 0x0c, 0xdb, 0xbc, 0x17, 0xbc, 0x4a, 0xc9, 0xd2, 0x99,
	0xfc, 0xd0, 0x4f, 0xba, 0x8d, 0xb0, 0x5d, 0x0b, 0x5e, 0xa5, 0xfa, 0x23, 0x58, 0xcd, 0x70, 0xb1,
	0xcc, 0x83, 0x63, 0x60, 0x59, 0x00, 0x5d, 0x64, 0xe8, 0x0f, 0x61, 0x65, 0x18, 0x07, 0x6f, 0xc2,
	0xe0, 0x2d, 0xa7, 0x1c, 0x38, 0xaf, 0x02, 0x25, 0xc7, 0x93, 0xa9, 0xb0, 0x17, 0xa5, 0x69, 0x18,
	0x50, 0x9e, 0x5b, 0x50, 0x60, 0xcb, 0x82, 0xde, 0x8a, 0x7b, 0x95, 0xcf, 0x01, 0xa4, 0xb5, 0xd9,
	0xbb, 0x14, 0x13, 0x63, 0x27, 0x0e, 0x92, 0x44, 0x6c, 0xa6, 0x30, 0xda, 0xcc, 0x12, 0x51, 0xad,
	0x4e, 0x65, 0x13, 0xca, 0x07, 0x51, 0xd8, 0x0e, 0x3e, 0x14, 0xff, 0x8f, 0xe6, 0x01, 0x9a, 0xc1,
	0xa0, 0x83, 0xde, 0x14, 0x7c, 0xad, 0x7f, 0x06, 0x25, 0x2c, 0x42, 0x3c, 0x0e, 0xbe, 0x26, 0x65,
	0x95, 0xb7, 0xca, 0xe8, 0x33, 0xdb, 0x7e, 0x12, 0xb8, 0xc1, 0xd7, 0xee, 0xd2, 0xb1, 0xf8, 0x43,
	0x7f, 0x00, 0xe5, 0xd4, 0x8f, 0xbb, 0x41, 0xca, 0x07, 0x7e, 0x3f, 0xc8, 0x2b, 0x4d, 0x30, 0x6c,
	0xbf, 0x1f, 0x50, 0x19, 0xc0, 0x94, 0x30, 0xa7, 0xe8, 0x9d, 0x28, 0xfa, 0xed, 0x51, 0xc2, 0xcb,
	0xe9, 0x27, 0x23, 0xa2, 0xba, 0xdb, 0xbd, 0x10, 0x0b, 0x1e, 0x6a, 0x3d, 0xec, 0x90, 0x7a, 0xb2,
	0x29, 0xca, 0x82, 0x55, 0x4f, 0xba, 0x56, 0x47, 0x7f, 0x06, 0xeb, 0x0a, 0x32, 0x0d, 0xfb, 0xc1,
	0xc6, 0xa2, 0x82, 0x5d, 0x1d, 0x61, 0xbd, 0xb0, 0x1f, 0xa0, 0x9f, 0xa6, 0x27, 0xa7, 0xfd, 0xe3,
	0x8d, 0x25, 0xa5, 0x54, 0x08, 0x92, 0xfe, 0x14, 0xd6, 0x4e, 0xfc, 0x84, 0xfb, 0x69, 0xea, 0xb7,
	0x4f, 0x30, 0x1c, 0x37, 0x4a, 0x77, 0x8b, 0x8f, 0x4a, 0xd9, 0x44, 0x27, 0x7e, 0x62, 0x8c, 0x58,
	0xe4, 0x37, 0x67, 0xbc, 0x17, 0x75, 0xc3, 0x01, 0xc7, 0x88, 0x58, 0xce, 0xf9, 0xcd, 0x59, 0x0d,
	0x19, 0xfb, 0xc1, 0x19, 0x16, 0xce, 0x28, 0x0e, 0xbb, 0xe1, 0x60, 0x03, 0x14, 0xfd, 0x4b, 0x1a,
	0x86, 0x13, 0xba, 0x48, 0x59, 0x11, 0x06, 0x09, 0xb8, 0x29, 0xdc, 0x4d, 0x12, 0x9d, 0xc6, 0xed,
	0x80, 0x93, 0x0e, 0x57, 0x94, 0xe1, 0xe8, 0x8a, 0x4d, 0xe2, 0x51, 0xfe, 0x7b, 0x0e, 0x97, 0x3a,
	0x11, 0x1f, 0x44, 0x29, 0x6f, 0x9f, 0x04, 0xed, 0xd7, 0xc2, 0xed, 0x56, 0xef, 0x16, 0x46, 0xb2,
	0xaf, 0x75, 0x22, 0x3b, 0x4a, 0x4d, 0x64, 0x92, 0xe7, 0xdd, 0x83, 0x65, 0x9c, 0xbe, 0xe7, 0x1f,
	0x07, 0xbd, 0x8d, 0x35, 0x65, 0xe2, 0x52, 0x3f, 0xe9, 0xd6, 0x90, 0x5a, 0xf9, 0x77, 0x73, 0x50,
	0x1e, 0x39, 0x46, 0x32, 0xd4, 0x1f, 0xc3, 0xb2, 0xf4, 0x8c, 0x64, 0x28, 0x5d, 0x63, 0x65, 0xec,
	0x1a, 0xc9, 0xd0, 0x2d, 0x1d, 0xcb, 0xbf, 0xa6, 0x6d, 0x57, 0x3c, 0xcf, 0x76, 0x15, 0x80, 0xe4,
	0x4d, 0x9c, 0xc1, 0x54, 0x2f, 0x29, 0x25, 0x6f, 0x62, 0x81, 0xf9, 0x0c, 0x56, 0x32, 0x0c, 0x19,
	0x77, 0x5e, 0x41, 0x81, 0x40, 0x91, 0x65, 0x9f, 0xc2, 0xda, 0xd8, 0x72, 0x64, 0x92, 0x05, 0x25,
	0x15, 0xad, 0x8e, 0x79, 0x68, 0x95, 0x09, 0xff, 0x5d, 0x3c, 0xc7, 0x7f, 0x27, 0x8d, 0xbc, 0x74,
	0x8e, 0x91, 0x1f, 0x40, 0x39, 0x78, 0xd7, 0x0e, 0x82, 0x8e, 0x10, 0xb1, 0xa4, 0x68, 0x14, 0x04,
	0x83, 0x44, 0x1c, 0xfb, 0xc2, 0xf2, 0x0c, 0x5f, 0x78, 0x04, 0xab, 0x52, 0x26, 0xa9, 0x0f, 0xd5,
	0x61, 0xa4, 0xb8, 0x42, 0x25, 0xd8, 0x43, 0x09, 0xe4, 0x69, 0xd8, 0x21, 0xe7, 0x19, 0x25, 0x40,
	0x41, 0x6f, 0x85, 0x9d, 0xca, 0x2f, 0x0b, 0x70, 0xb9, 0x35, 0xec, 0x45, 0x7e, 0x67, 0xec, 0xb5,
	0x17, 0x09, 0xf1, 0xbc, 0x6d, 0x8a, 0x33, 0x6d, 0x33, 0xad, 0x73, 0x35, 0x2b, 0x4e, 0xe8, 0xfc,
	0x53, 0x00, 0x25, 0xb4, 0xd0, 0x8c, 0x99, 0xcb, 0x2b, 0x74, 0xfd, 0xbb, 0x70, 0x59, 0x99, 0x72,
	0x18, 0x47, 0xc3, 0x20, 0x4e, 0xcf, 0x28, 0xfc, 0x33, 0xb8, 0x3e, 0x06, 0x34, 0x24, 0xbf, 0x12,
	0xc0, 0x95, 0xe9, 0xcd, 0x5e, 0xcc, 0x6d, 0x3f, 0x60, 0xc3, 0x95, 0x3f, 0x2c, 0xc0, 0xd5, 0x6a,
	0xf4, 0x76, 0xf0, 0xff, 0x8f, 0x5a, 0x2b, 0xff, 0xb3, 0x00, 0xd7, 0x66, 0x89, 0xf4, 0x2b, 0xdf,
	0xfc, 0xaf, 0xc3, 0xda, 0x4f, 0x61, 0x4d, 0x7a, 0x72, 0xd6, 0x7f, 0xa9, 0x65, 0x50, 0xc6, 0x83,
	0x21, 0x58, 0x95, 0x43, 0xd0, 0xaa, 0x41, 0x2f, 0x48, 0x83, 0x7a, 0x90, 0x24, 0x7e, 0x37, 0xb8,
	0x88, 0xda, 0x6f, 0xc2, 0xe2, 0x8c, 0xbd, 0x2d, 0xf4, 0xc9, 0xaa, 0x3f, 0x84, 0x4b, 0x13, 0x13,
	0x5f, 0x48, 0x79, 0x95, 0x7f, 0x5d, 0x80, 0xb5, 0xba, 0x1f, 0xbf, 0xa6, 0x5c, 0xe9, 0x77, 0x7e,
	0x0d, 0x85, 0xf4, 0x43, 0x12, 0xe5, 0x53, 0x58, 0x1f, 0x06, 0x41, 0xcc, 0x15, 0xe0, 0xbc, 0x92,
	0x1a, 0x56, 0x90, 0xd9, 0xcc, 0x1c, 0xf9, 0x8f, 0x0a, 0xb0, 0x9e, 0x13, 0xf9, 0x62, 0xee, 0xf2,
	0x2b, 0x14, 0xfb, 0x3b, 0x70, 0x85, 0xc4, 0x8e, 0x03, 0xbf, 0x73, 0x9e, 0xec, 0x1a, 0x22, 0x50,
	0xce, 0x91, 0xfc, 0x7f, 0x40, 0xd9, 0xad, 0x23, 0x3a, 0xc9, 0x66, 0x90, 0xa6, 0xe1, 0xe0, 0x42,
	0x0d, 0xcc, 0xed, 0x71, 0xc7, 0xaf, 0x0a, 0x3f, 0xea, 0xfb, 0x3f, 0x87, 0xf5, 0x38, 0x68, 0x07,
	0xe1, 0x9b, 0x80, 0x27, 0x62, 0xf6, 0x9c, 0xf8, 0x6b, 0x92, 0x29, 0x57, 0xae, 0x18, 0x98, 0x7e,
	0x26, 0xa5, 0xb9, 0x98, 0x13, 0xfd, 0x16, 0xe8, 0x3f, 0x39, 0x0d, 0xe2, 0xb3, 0x5f, 0xc1, 0x7e,
	0x0a, 0x53, 0xfb, 0xa9, 0xbc, 0x82, 0xab, 0x64, 0x6a, 0x55, 0x6a, 0x2b, 0x0d, 0xfa, 0xea, 0xc0,
	0xc2, 0x07, 0x2a, 0xa2, 0xf8, 0x1e, 0x45, 0xfc, 0x7e, 0x01, 0x2e, 0x4f, 0x6d, 0xe3, 0x62, 0xbe,
	0xb5, 0x07, 0x97, 0xe4, 0x4a, 0x3c, 0x4c, 0x83, 0x3e, 0xef, 0x85, 0x49, 0x2a, 0x4f, 0xb1, 0xb7,
	0xe4, 0x01, 0x66, 0xe6, 0x56, 0xdc, 0xf5, 0x64, 0xfc, 0xa3, 0x16, 0x26, 0x69, 0x25, 0x84, 0xdb,
	0xd4, 0xf3, 0x60, 0x1f, 0x63, 0xa4, 0xec, 0x4d, 0x10, 0x9f, 0x45, 0x83, 0x60, 0x37, 0x8e, 0x4e,
	0x87, 0x17, 0xec, 0x77, 0xef, 0x40, 0xa9, 0x8b, 0xc3, 0x26, 0x13, 0xc8, 0x12, 0x51, 0xad, 0x4e,
	0xe5, 0x5f, 0x14, 0xe0, 0xce, 0x7b, 0xd7, 0xba, 0x98, 0x0e, 0xee, 0x03, 0xc4, 0x41, 0xdf, 0x0f,
	0x07, 0xbc, 0x2d, 0x3d, 0x74, 0x54, 0xe1, 0x05, 0xdd, 0xa4, 0x52, 0x79, 0x25, 0x4c, 0xb8, 0xdf,
	0xeb, 0x45, 0x6f, 0xb9, 0x9f, 0xf2, 0x40, 0xae, 0x49, 0x8e, 0x9a, 0x75, 0x7e, 0x97, 0xc2, 0xc4,
	0x40, 0xc0, 0x58, 0xa4, 0xca, 0xbf, 0x2c, 0xc2, 0x8a, 0xe9, 0x0f, 0xda, 0x41, 0xef, 0xd7, 0xd3,
	0xf4, 0x7f, 0x48, 0xd0, 0x4f, 0xf5, 0x3a, 0x6a, 0x57, 0x97, 0xeb, 0x75, 0x7e, 0x5d, 0x07, 0x81,
	0x0f, 0x6c, 0xed, 0x2a, 0xdf, 0x87, 0x55, 0x45, 0x59, 0x17, 0x0b, 0x69, 0x1f, 0x36, 0x46, 0x15,
	0xc5, 0xef, 0x6c, 0x9f, 0x35, 0x82, 0x20, 0x96, 0x04, 0x6c, 0xc1, 0x29, 0xed, 0x91, 0x2a, 0xd5,
	0xc8, 0x2b, 0x21, 0x79, 0x86, 0x22, 0x67, 0x37, 0x24, 0x27, 0x70, 0x73, 0x37, 0x48, 0xe5, 0xa4,
	0x8d, 0x2c, 0x4b, 0xa6, 0x7e, 0x7a, 0x9a, 0x5c, 0xc4, 0xb4, 0xf7, 0x60, 0x45, 0x06, 0xfc, 0x38,
	0xdc, 0x96, 0xdd, 0xb2, 0xa4, 0x51, 0x30, 0xfd, 0x14, 0xae, 0xce, 0x5c, 0xe6, 0x1b, 0x33, 0xc8,
	0x87, 0x6c, 0xe3, 0x1f, 0x17, 0xe0, 0xd6, 0xf9, 0xfb, 0xb8, 0x58, 0xec, 0x38, 0x70, 0x55, 0x29,
	0x28, 0x34, 0xc5, 0xcc, 0x1c, 0x32, 0x73, 0x2d, 0x7d, 0x98, 0xfb, 0x4d, 0x3b, 0xff, 0xa7, 0x45,
	0x58, 0xac, 0x92, 0x03, 0x4c, 0x86, 0x40, 0xe1, 0x83, 0x42, 0xa0, 0xf8, 0x61, 0x21, 0x30, 0xf7,
	0xc1, 0x21, 0x30, 0x7f, 0x81, 0x10, 0x58, 0xf8, 0xf0, 0x10, 0x58, 0x3c, 0xe7, 0x74, 0x73, 0x0f,
	0x96, 0x5f, 0xc5, 0x51, 0x5f, 0x6c, 0x59, 0x8d, 0x93, 0x12, 0x92, 0x71, 0xc3, 0x95, 0xdf, 0x86,
	0xb5, 0x6d, 0x3f, 0x6d, 0x9f, 0x54, 0x2f, 0x9c, 0x54, 0x3e, 0x83, 0xa5, 0x4e, 0xd0, 0xe3, 0xe2,
	0x92, 0x0d, 0xed, 0xb3, 0x2a, 0xed, 0x23, 0xa7, 0x5a, 0xec, 0xd0, 0x7f, 0x2b, 0x3f, 0x80, 0xf5,
	0xdc, 0x0a, 0x17, 0x8b, 0xc4, 0xdf, 0x9d, 0x83, 0xb5, 0x46, 0x34, 0xdc, 0xf5, 0xfb, 0xc1, 0x8b,
	0xd3, 0x3e, 0x66, 0x64, 0x4c, 0xe9, 0xa4, 0x24, 0x3c, 0x5b, 0xab, 0xf7, 0x42, 0x4b, 0x7d, 0x79,
	0xab, 0xf8, 0x09, 0x2c, 0x75, 0xfd, 0x7e, 0x30, 0x69, 0xc1, 0x45, 0x24, 0x5a, 0x1d, 0x1c, 0xef,
	0xc7, 0x81, 0x3f, 0x7d, 0x47, 0x8d, 0xd4, 0x56, 0xdc, 0x43, 0x40, 0xd8, 0x8e, 0x06, 0x04, 0x98,
	0x57, 0x01, 0x48, 0x45, 0xc0, 0x43, 0x58, 0xe9, 0xc8, 0xc6, 0x5d, 0xde, 0x00, 0x8d, 0x41, 0xe5,
	0x8c, 0x83, 0xc0, 0x7b, 0xb0, 0x4c, 0x92, 0x24, 0xe1, 0xcf, 0x30, 0xa7, 0x8d, 0x51, 0x25, 0x24,
	0x37, 0xc3, 0x9f, 0x05, 0xe8, 0x23, 0x04, 0x19, 0xbe, 0x4e, 0x33, 0x3b, 0x29, 0x93, 0x21, 0xab,
	0xf1, 0x5a, 0xf8, 0xa6, 0xb8, 0x9e, 0x8a, 0xe2, 0x4e, 0x10, 0xd3, 0xaa, 0x25, 0x15, 0x98, 0x71,
	0xd4, 0x55, 0x69, 0xba, 0xe5, 0xc9, 0x55, 0xc9, 0xec, 0x9f, 0x43, 0x09, 0xd5, 0x49, 0xea, 0xba,
	0x04, 0xab, 0x55, 0xe7, 0xd0, 0xae, 0x39, 0x46, 0x95, 0xef, 0x1a, 0x75, 0xa6, 0x15, 0xf4, 0x35,
	0x00, 0xc7, 0xad, 0x32, 0x57, 0xfc, 0x2e, 0x56, 0xfe, 0xf9, 0x1c, 0x94, 0x58, 0x3f, 0xfa, 0x2a,
	0x44, 0xfd, 0x5f, 0x81, 0xa2, 0xbc, 0x91, 0xcb, 0xe6, 0x2d, 0x86, 0x9d, 0xec, 0x6d, 0xa2, 0x38,
	0xf9, 0x36, 0xa1, 0xbe, 0x3f, 0x28, 0xb6, 0x10, 0xb4, 0xf1, 0xcb, 0x85, 0x1a, 0x19, 0xf2, 0xe5,
	0xe2, 0x3e, 0x40, 0x80, 0x6b, 0x0a, 0x4b, 0x2f, 0xa8, 0x87, 0x65, 0xa2, 0x7b, 0xf2, 0x3a, 0xea,
	0x75, 0x70, 0xf6, 0x36, 0x8a, 0x3b, 0x39, 0xfd, 0x66, 0x44, 0x14, 0xab, 0xdf, 0xf9, 0x6e, 0x4e,
	0xa9, 0x48, 0x40, 0x1f, 0xa1, 0xbb, 0x23, 0x7e, 0x92, 0x3b, 0xf4, 0x2f, 0x12, 0x71, 0x6f, 0xcc,
	0x7e, 0x9b, 0x3f, 0xf1, 0x13, 0xf1, 0x10, 0x0f, 0x25, 0x61, 0xc2, 0xa3, 0xe3, 0x84, 0x8e, 0xfa,
	0x59, 0xc9, 0x5e, 0x08, 0x13, 0xe7, 0x38, 0xc1, 0xb1, 0xd1, 0x71, 0x42, 0x81, 0x59, 0x56, 0x96,
	0x5d, 0x8c, 0x8e, 0x13, 0xac, 0x4b, 0x5f, 0xc2, 0x32, 0x1b, 0x89, 0x7f, 0x13, 0xae, 0xb3, 0x7a,
	0xdd, 0x79, 0x61, 0x71, 0xef, 0xa8, 0xc1, 0x78, 0xcb, 0x6e, 0x36, 0x98, 0x69, 0xed, 0x58, 0xac,
	0x2a, 0x6e, 0xc4, 0xd9, 0x98, 0xe7, 0x32, 0xd3, 0xa9, 0xd7, 0x99, 0x5d, 0xd5, 0x0a, 0xfa, 0x35,
	0xd0, 0x15, 0xce, 0x3e, 0x3b, 0x3a, 0x74, 0xdc, 0xaa, 0x56, 0xac, 0xfc, 0xb2, 0x08, 0x57, 0xac,
	0x41, 0x1a, 0xc4, 0x7e, 0x3b, 0x0d, 0xdf, 0x04, 0xad, 0x01, 0xa6, 0x52, 0xb4, 0x19, 0x93, 0xf7,
	0x79, 0x68, 0xb5, 0xb5, 0xad, 0xa7, 0x32, 0x52, 0x67, 0x41, 0x55, 0xe2, 0xe4, 0x5d, 0x3f, 0x5d,
	0x0b, 0x86, 0xb2, 0x83, 0x98, 0x1b, 0x71, 0x64, 0x4a, 0xa2, 0x54, 0x73, 0x9a, 0x04, 0xf1, 0x64,
	0x4e, 0x04, 0xe4, 0xb4, 0x92, 0x20, 0xb6, 0x3a, 0xfa, 0x16, 0xe8, 0x63, 0xdc, 0x20, 0x6c, 0xbf,
	0x26, 0x27, 0x55, 0x6f, 0x12, 0xb5, 0x0c, 0x6d, 0x4b, 0xae, 0xfe, 0x18, 0x56, 0xdb, 0x51, 0x9f,
	0xce, 0xae, 0xe3, 0xa3, 0x66, 0xf6, 0xf8, 0xb4, 0x22, 0x59, 0x26, 0x95, 0x2c, 0x71, 0xd2, 0x0d,
	0xd3, 0xd3, 0x4e, 0x20, 0xb1, 0x8b, 0x0a, 0x76, 0x35, 0xe3, 0x11, 0xb8, 0xf2, 0x43, 0x58, 0x9f,
	0xd8, 0xac, 0x5e, 0x82, 0x79, 0xdb, 0xb1, 0x99, 0xf6, 0x91, 0xae, 0xc1, 0x8a, 0xcd, 0x0e, 0xb9,
	0xe1, 0x79, 0x96, 0xd7, 0xaa, 0x62, 0x50, 0xac, 0x43, 0x19, 0x29, 0xc2, 0x0a, 0x9e, 0x56, 0xac,
	0xfc, 0xce, 0xdc, 0xf8, 0x45, 0xa3, 0x21, 0x5e, 0xf9, 0x94, 0xe0, 0x58, 0x55, 0x82, 0x23, 0xbb,
	0x4e, 0x2d, 0x4e, 0x5d, 0xa7, 0xca, 0xb0, 0x99, 0x9b, 0x0c, 0x9b, 0x0d, 0x98, 0x3f, 0x09, 0xfc,
	0x4e, 0x4e, 0x33, 0x44, 0x19, 0xbd, 0xd0, 0x2d, 0x4c, 0xbd, 0xd0, 0x3d, 0x80, 0xb2, 0x7a, 0xef,
	0xb8, 0x98, 0xbb, 0x6f, 0x1b, 0x5f, 0x3a, 0x3e, 0x82, 0xd5, 0xe4, 0x24, 0x7a, 0xcb, 0x93, 0x60,
	0xd0, 0xe1, 0xc7, 0xe9, 0x80, 0x2a, 0x43, 0xe6, 0xc3, 0x65, 0x64, 0x61, 0x2b, 0xbc, 0x9d, 0x0e,
	0x30, 0x91, 0x84, 0x09, 0xef, 0x44, 0xa7, 0xc7, 0xbd, 0x20, 0x77, 0xa5, 0x5a, 0x0a, 0x93, 0x2a,
	0x51, 0x27, 0x6e, 0xb4, 0x96, 0xd5, 0x7e, 0x77, 0x74, 0xa3, 0x85, 0x22, 0x93, 0x99, 0x41, 0x09,
	0x07, 0xa2, 0xe0, 0xf0, 0x61, 0x1c, 0x66, 0x12, 0xe7, 0x2e, 0xc4, 0x88, 0x4e, 0x02, 0xdf, 0x80,
	0x05, 0xfa, 0x91, 0xbb, 0x49, 0x15, 0xa4, 0xca, 0x3f, 0x28, 0xc2, 0xb5, 0xd1, 0x9b, 0x5f, 0x98,
	0x9e, 0x48, 0x3b, 0xa0, 0xcf, 0x3f, 0x13, 0x2f, 0x40, 0xa2, 0xbe, 0xdc, 0x38, 0xe7, 0xfd, 0x26,
	0x7b, 0x09, 0xd2, 0xbf, 0x80, 0x25, 0xf9, 0x52, 0x2b, 0xdf, 0x8c, 0x26, 0x1f, 0xde, 0xe4, 0xcc,
	0x6e, 0x06, 0xd3, 0x7f, 0x0c, 0xcb, 0x74, 0xcc, 0x09, 0x07, 0xaf, 0x22, 0xf9, 0x54, 0x48, 0x6f,
	0x6f, 0xca, 0xa3, 0xef, 0xa6, 0x1c, 0x84, 0x0a, 0xc5, 0xc3, 0x8d, 0x35, 0x78, 0x15, 0xb9, 0xa5,
	0x50, 0xfe, 0x85, 0x55, 0x3f, 0xec, 0xf3, 0xec, 0x81, 0x78, 0xf4, 0x06, 0x37, 0xaa, 0xfa, 0x61,
	0x5f, 0x8e, 0x47, 0x35, 0x54, 0x1e, 0xc3, 0xba, 0x55, 0xe7, 0x0d, 0x97, 0x35, 0x99, 0xed, 0x51,
	0xe4, 0xe7, 0xde, 0xcf, 0x00, 0x16, 0x77, 0x58, 0xad, 0xe6, 0x1c, 0x6a, 0x85, 0xca, 0xff, 0x2a,
	0xc0, 0xc7, 0x75, 0x3f, 0x49, 0x83, 0xd8, 0x18, 0x0e, 0xe3, 0x60, 0x90, 0x86, 0xed, 0xc0, 0x1a,
	0xbc, 0x09, 0xc5, 0xf3, 0xcd, 0x17, 0x70, 0xa9, 0x4f, 0x4c, 0x1e, 0x0e, 0x06, 0x41, 0xcc, 0xe5,
	0x83, 0xef, 0xd8, 0x36, 0xeb, 0x82, 0x6d, 0x21, 0x97, 0xae, 0x9d, 0xc7, 0x23, 0xa2, 0xd3, 0x34,
	0x1b, 0x51, 0x9c, 0x1e, 0xe1, 0x20, 0x97, 0x46, 0x7c, 0x0f, 0xae, 0xfa, 0xa3, 0xa5, 0xd5, 0x75,
	0xd4, 0x8a, 0x7b, 0xd9, 0x57, 0xa4, 0x1b, 0xcc, 0x1c, 0xa9, 0xac, 0x37, 0x3f, 0x7b, 0xe4, 0x68,
	0xcd, 0xca, 0xff, 0x2e, 0xc0, 0xad, 0xc9, 0x5d, 0xb3, 0x24, 0xf5, 0x8f, 0x7b, 0x61, 0x72, 0xf2,
	0xa7, 0x79, 0xe3, 0x3f, 0x85, 0x3b, 0xd3, 0xd6, 0x4e, 0xe3, 0xa8, 0x73, 0xda, 0x4e, 0xc3, 0x68,
	0x20, 0x9f, 0xd2, 0xe8, 0xf5, 0xfe, 0x5b, 0xb9, 0xc2, 0xbd, 0x80, 0xb4, 0x6f, 0x8d, 0x98, 0x5b,
	0xb9, 0xf2, 0x4d, 0xcc, 0xad, 0xca, 0x1f, 0x14, 0xa0, 0x6c, 0x9e, 0xf8, 0xa9, 0xe9, 0xc7, 0x54,
	0x4a, 0x3e, 0x81, 0xa5, 0x93, 0x90, 0x4f, 0x7d, 0x24, 0xb0, 0x78, 0x12, 0x66, 0x6f, 0x63, 0x69,
	0x34, 0x0c, 0xdb, 0x99, 0xaa, 0x94, 0xb7, 0x31, 0xa2, 0x13, 0x68, 0x03, 0xe6, 0xdb, 0x7e, 0x2c,
	0xaa, 0x44, 0x76, 0xbb, 0x48, 0x94, 0xdc, 0xbb, 0xd7, 0xfc, 0xac, 0x77, 0xaf, 0xff, 0x53, 0x80,
	0x0d, 0xf3, 0xc4, 0x1f, 0x0c, 0x82, 0x9e, 0x33, 0x0c, 0x06, 0xd8, 0x18, 0x8e, 0x3d, 0x5b, 0xed,
	0xdc, 0x72, 0x47, 0x9a, 0xac, 0x73, 0xcb, 0x32, 0x91, 0x2a, 0x97, 0xc8, 0x44, 0x1b, 0x30, 0xdf,
	0x09, 0x92, 0x76, 0x2e, 0x13, 0x13, 0x05, 0x37, 0x1c, 0xf6, 0xbb, 0xb2, 0x1b, 0x54, 0x36, 0x1c,
	0xf6, 0xbb, 0xb2, 0x5b, 0x1c, 0x7d, 0xf2, 0xa0, 0xe6, 0xe4, 0xd1, 0x27, 0x0f, 0x59, 0x69, 0x8c,
	0x4e, 0xa9, 0xbb, 0xcf, 0xf5, 0x29, 0x54, 0x1a, 0x9d, 0x53, 0xca, 0x57, 0x15, 0xd4, 0xdc, 0x08,
	0xa5, 0x76, 0x2c, 0xa5, 0x34, 0x12, 0x98, 0xca, 0x7f, 0x99, 0x83, 0xf5, 0x9d, 0xa0, 0xd7, 0x8b,
	0xde, 0x8e, 0x37, 0x8d, 0x59, 0x9a, 0x7e, 0xf0, 0x89, 0xb6, 0xac, 0x24, 0xc8, 0x56, 0x07, 0x2b,
	0xa8, 0x84, 0x88, 0x63, 0x55, 0xae, 0x10, 0xad, 0x08, 0x96, 0x3c, 0x14, 0x52, 0x46, 0x16, 0x09,
	0x69, 0xa2, 0x8c, 0x2f, 0x4b, 0xba, 0xd5, 0x91, 0x85, 0xe1, 0x74, 0xd0, 0x8b, 0xda, 0xaf, 0x49,
	0x29, 0x4a, 0x61, 0x68, 0x11, 0x55, 0xdf, 0x04, 0xed, 0x15, 0x09, 0xca, 0x8f, 0xc3, 0x41, 0x27,
	0xeb, 0xe1, 0x94, 0xfb, 0x27, 0xc1, 0xdd, 0x0e, 0x07, 0x1d, 0x4a, 0xf2, 0x0f, 0xa0, 0x2c, 0xf1,
	0xd3, 0xc5, 0x4b, 0x30, 0x26, 0x60, 0x53, 0x87, 0x1a, 0x09, 0xa3, 0x5e, 0xf9, 0x01, 0x94, 0xb3,
	0x5d, 0x88, 0x56, 0x59, 0x81, 0x49, 0x06, 0x9a, 0xe6, 0x09, 0xac, 0x91, 0x69, 0xc6, 0x22, 0xaa,
	0xdd, 0x1e, 0x99, 0x6d, 0x24, 0x20, 0x6e, 0x08, 0xb1, 0xaa, 0x94, 0xea, 0x43, 0x0f, 0xcd, 0xb4,
	0x33, 0x96, 0x74, 0x02, 0x4f, 0xe2, 0xaa, 0xfd, 0xa0, 0x82, 0xa7, 0x96, 0xfc, 0xdf, 0x14, 0x61,
	0x8d, 0xbe, 0x9e, 0x30, 0xde, 0xfa, 0x31, 0x55, 0x0b, 0x52, 0x33, 0x56, 0x98, 0xa9, 0xa3, 0x0e,
	0x95, 0x10, 0x5a, 0x25, 0x83, 0x4c, 0x79, 0x35, 0x41, 0x48, 0x17, 0xe8, 0xbf, 0x54, 0xa7, 0x3a,
	0x39, 0xe7, 0x5e, 0xa4, 0x32, 0x44, 0x6f, 0x52, 0xc4, 0x16, 0xed, 0x92, 0x1a, 0x73, 0x34, 0xb3,
	0xe8, 0xab, 0xee, 0xc1, 0x72, 0xc7, 0x3f, 0x1b, 0xb5, 0x5f, 0xca, 0xbb, 0x63, 0xc7, 0x3f, 0x1b,
	0x41, 0xc4, 0x32, 0xed, 0x68, 0x90, 0x3b, 0x91, 0x8a, 0x7a, 0xd7, 0x8e, 0x06, 0x54, 0xef, 0x46,
	0x4b, 0x89, 0xba, 0xa9, 0xba, 0xf9, 0xea, 0x68, 0x3d, 0xaa, 0x8e, 0x4f, 0x60, 0x6d, 0xb4, 0x7b,
	0xb1, 0x3f, 0xf5, 0xc4, 0xb3, 0x92, 0xa9, 0x80, 0x94, 0xf7, 0x73, 0xb8, 0x2e, 0x55, 0x19, 0xbc,
	0x4b, 0xc7, 0x5a, 0xc4, 0xf0, 0xd8, 0x04, 0x6d, 0x80, 0xc9, 0xcd, 0x47, 0x02, 0x17, 0x5f, 0xca,
	0xa8, 0x51, 0xb2, 0x86, 0x5c, 0x42, 0x8b, 0x6f, 0x57, 0xbe, 0x03, 0x20, 0xa1, 0xe3, 0xab, 0x87,
	0xab, 0xd9, 0xf7, 0x17, 0x39, 0xfb, 0xb8, 0xcb, 0x04, 0xa4, 0xab, 0x86, 0xff, 0x58, 0x00, 0x4d,
	0x48, 0xd0, 0x1a, 0x76, 0x63, 0xbf, 0x13, 0xc8, 0xa5, 0xa7, 0x62, 0xa0, 0xf0, 0x41, 0x31, 0x30,
	0x7d, 0x4f, 0xa7, 0x38, 0xf7, 0x8d, 0xf1, 0x07, 0x3f, 0xca, 0xa1, 0x49, 0x7c, 0xcb, 0x53, 0x85,
	0x35, 0x65, 0xb7, 0x98, 0x48, 0xe6, 0xa9, 0x33, 0xc9, 0xbe, 0x5f, 0x39, 0x47, 0x4b, 0xee, 0xca,
	0x48, 0x0b, 0x98, 0x66, 0xfe, 0x43, 0x01, 0x56, 0x76, 0xfd, 0x70, 0xe0, 0xfa, 0x71, 0x80, 0xad,
	0x38, 0xb5, 0xa9, 0x13, 0x7d, 0x2d, 0x12, 0xbe, 0xf1, 0x36, 0xfe, 0x2e, 0x94, 0x46, 0x4d, 0xbe,
	0xea, 0x7c, 0x23, 0x2a, 0xba, 0x4d, 0xec, 0xc7, 0xd2, 0xc0, 0x6a, 0x7e, 0x2d, 0x21, 0x99, 0xf6,
	0x2b, 0x4e, 0x5b, 0xfd, 0x37, 0x43, 0x4a, 0x20, 0xca, 0x69, 0xab, 0xfe, 0x66, 0x48, 0x29, 0x3f,
	0xe1, 0xed, 0x13, 0x3f, 0xee, 0x93, 0xd7, 0x95, 0x46, 0x29, 0x3f, 0x31, 0x91, 0x58, 0xf9, 0xa3,
	0x22, 0x94, 0xb3, 0xbd, 0xa0, 0x51, 0x1e, 0xc3, 0x02, 0x76, 0x68, 0x89, 0xfc, 0xb4, 0x26, 0xfb,
	0xba, 0x4b, 0xdd, 0xae, 0x2b, 0x10, 0x14, 0x39, 0x09, 0xd9, 0x8e, 0x76, 0x57, 0x1a, 0x45, 0x4e,
	0x82, 0x26, 0xd3, 0x9f, 0x83, 0x26, 0x0e, 0x89, 0xc7, 0x7e, 0xfb, 0x75, 0x37, 0x8e, 0x4e, 0x07,
	0xf9, 0x08, 0x5b, 0x27, 0xee, 0xf6, 0x88, 0xa9, 0x7f, 0x0a, 0xa0, 0x40, 0xd5, 0xcd, 0x2a, 0xf4,
	0x91, 0x46, 0x3a, 0xfe, 0x59, 0x92, 0xab, 0x28, 0xa4, 0x91, 0xaa, 0x7f, 0x96, 0xe4, 0x95, 0xb6,
	0x38, 0x53, 0x69, 0x15, 0x58, 0xc6, 0x83, 0x59, 0x92, 0xfa, 0xfd, 0x21, 0xa5, 0xc9, 0x71, 0x5f,
	0x9e, 0x91, 0x31, 0xf4, 0xdb, 0xa2, 0x94, 0x62, 0x72, 0x28, 0xa9, 0x20, 0x49, 0xb7, 0x3a, 0x95,
	0x00, 0xd6, 0x9b, 0x67, 0x09, 0x26, 0x93, 0x28, 0x0d, 0x5f, 0x9d, 0x89, 0x2f, 0xdb, 0x2e, 0x9d,
	0x84, 0xdd, 0x93, 0x1e, 0x1e, 0xe1, 0x47, 0x9f, 0xf0, 0xa9, 0x31, 0xa5, 0x8d, 0xd8, 0xf2, 0x13,
	0xbe, 0x5c, 0x95, 0x2c, 0xce, 0xa8, 0x92, 0x95, 0xaf, 0x61, 0xa5, 0x79, 0xe2, 0xc7, 0x01, 0x16,
	0x74, 0x59, 0xd5, 0xc6, 0x97, 0x18, 0xb9, 0xaa, 0x96, 0x5d, 0x62, 0xa8, 0x85, 0xb9, 0xf8, 0x0d,
	0x85, 0x79, 0x6e, 0xd6, 0x92, 0x3f, 0x84, 0xd5, 0x7a, 0xf8, 0x22, 0x1c, 0x74, 0xd1, 0xe6, 0x9e,
	0xdf, 0xd5, 0x3f, 0x87, 0xf5, 0xbe, 0x9f, 0xb6, 0x4f, 0xc2, 0x41, 0x97, 0x77, 0x82, 0x6e, 0x1c,
	0xe4, 0x57, 0x5e, 0xcb, 0x98, 0x55, 0xe2, 0x55, 0xbe, 0x02, 0x9d, 0xbc, 0x25, 0x68, 0xfb, 0xbd,
	0x9e, 0x19, 0xf5, 0x03, 0x34, 0xa2, 0xfa, 0x81, 0x4c, 0x61, 0xd6, 0x07, 0x32, 0xb7, 0x60, 0x51,
	0xa8, 0x26, 0x2f, 0xb4, 0xa0, 0x9d, 0x77, 0x1e, 0xac, 0xfc, 0xbd, 0x02, 0x6c, 0xec, 0x06, 0xa9,
	0xd5, 0xdf, 0x3d, 0x0d, 0x3b, 0x81, 0x17, 0x87, 0xdd, 0x2e, 0xf6, 0xa9, 0xaf, 0xa2, 0x5f, 0xe5,
	0xdb, 0xd9, 0xf4, 0xa3, 0xee, 0xdc, 0xf9, 0x8f, 0xba, 0xbf, 0x57, 0x80, 0x8f, 0xcf, 0x91, 0xe8,
	0x62, 0xd7, 0xbe, 0x3f, 0x84, 0x4b, 0xa9, 0x18, 0x3d, 0xf5, 0x6c, 0x94, 0x7d, 0x3a, 0x97, 0xcd,
	0x4e, 0x8f, 0x45, 0xe9, 0xf8, 0x07, 0xa5, 0xde, 0xbf, 0x5f, 0x84, 0xb2, 0x02, 0xf8, 0x7f, 0xce,
	0x55, 0xd7, 0x60, 0xae, 0x3d, 0xd1, 0xf2, 0x20, 0x81, 0x0c, 0x1a, 0x07, 0x7e, 0x1a, 0xc5, 0xb9,
	0x5b, 0xaa, 0x8c, 0xa8, 0x3f, 0x84, 0x95, 0x2c, 0x8a, 0xa6, 0xba, 0x9c, 0xb2, 0xe4, 0xc8, 0xcb,
	0x9e, 0xc5, 0xd4, 0x3f, 0xc6, 0x50, 0x5b, 0x54, 0x0f, 0xb2, 0xa9, 0x7f, 0x6c, 0x75, 0xb0, 0xda,
	0x89, 0x7b, 0xc0, 0x9e, 0x7f, 0x36, 0x7d, 0x11, 0xb8, 0x42, 0x17, 0x81, 0x3d, 0xff, 0xcc, 0x96,
	0x39, 0x33, 0x4c, 0x78, 0x34, 0xe8, 0x85, 0x03, 0x51, 0x14, 0x95, 0xf6, 0xcb, 0x21, 0x6a, 0xe5,
	0xef, 0x16, 0xf0, 0xb4, 0x68, 0x46, 0xfd, 0x7e, 0x34, 0xc8, 0x3a, 0xf7, 0x1b, 0xb0, 0x90, 0x86,
	0x69, 0x2f, 0xc8, 0x1d, 0x79, 0x04, 0x49, 0xf5, 0xda, 0xdc, 0x33, 0x64, 0x7b, 0x46, 0xfc, 0xce,
	0xfc, 0xb0, 0x37, 0xd7, 0x25, 0x17, 0x26, 0x83, 0xb1, 0xf2, 0x3f, 0x0a, 0xb0, 0x96, 0xc9, 0xf3,
	0xb2, 0xdf, 0x13, 0x89, 0x78, 0xa5, 0x13, 0x26, 0xb4, 0x5f, 0xa5, 0x32, 0x2e, 0xe2, 0xb0, 0x8d,
	0x82, 0x5b, 0x96, 0xbc, 0xac, 0x30, 0xbe, 0xeb, 0xf7, 0xf8, 0x58, 0x42, 0x25, 0x73, 0xbe, 0xeb,
	0xf7, 0x94, 0x24, 0x33, 0x3a, 0x3c, 0xa8, 0x66, 0xcc, 0x0e, 0x0f, 0xb8, 0xcb, 0xa1, 0x7f, 0xd6,
	0x8b, 0xfc, 0x4e, 0xee, 0x8b, 0xe3, 0x8c, 0xa8, 0xff, 0x18, 0x56, 0xe4, 0x9f, 0xe3, 0x4b, 0xc7,
	0xb5, 0xad, 0xeb, 0xd9, 0x75, 0x59, 0xfd, 0x65, 0xbd, 0xd6, 0xf0, 0xcf, 0x6a, 0x91, 0xdf, 0x51,
	0xae, 0xc6, 0xca, 0x72, 0x08, 0x92, 0x9e, 0xfc, 0xc9, 0x2d, 0x28, 0x5b, 0x75, 0x5e, 0x6f, 0xee,
	0x8a, 0x13, 0xfa, 0x0a, 0x94, 0x3c, 0xf6, 0xd2, 0x43, 0x82, 0x56, 0xd0, 0xcb, 0xb0, 0x64, 0xd5,
	0x77, 0xe9, 0x47, 0x51, 0x5f, 0x85, 0xe5, 0x03, 0xc7, 0x32, 0x19, 0xfd, 0x9c, 0xa3, 0x9f, 0x56,
	0x95, 0x39, 0xf4, 0x73, 0x5e, 0xd7, 0x60, 0x85, 0xbd, 0xf4, 0x98, 0x5d, 0x65, 0x55, 0xa2, 0x2c,
	0xe8, 0x97, 0x60, 0xb5, 0x79, 0xd4, 0xf4, 0x58, 0x9d, 0xdb, 0x8e, 0x67, 0xed, 0x1c, 0x69, 0x4b,
	0xfa, 0x1a, 0xc0, 0x6e, 0xcb, 0xaa, 0x55, 0xf9, 0x4f, 0x5a, 0x96, 0xa7, 0x95, 0x74, 0x1d, 0xd6,
	0xd8, 0xcb, 0x86, 0xcb, 0x9a, 0x4d, 0xcb, 0xb1, 0x69, 0xd8, 0x32, 0x4a, 0x20, 0x30, 0xdb, 0xa6,
	0x06, 0xe3, 0x11, 0x2f, 0x1c, 0xcb, 0xd6, 0xca, 0xfa, 0x3a, 0x94, 0xc5, 0x6f, 0xa3, 0x5a, 0xb7,
	0x6c, 0x6d, 0x45, 0xbf, 0x02, 0x9a, 0x20, 0xec, 0xba, 0x4e, 0xab, 0x21, 0x60, 0xab, 0xfa, 0x55,
	0xb8, 0xa4, 0x52, 0x9d, 0x43, 0x9b, 0xb9, 0xda, 0xda, 0x24, 0x59, 0xcc, 0xb1, 0xae, 0x5f, 0x87,
	0xcb, 0x72, 0xd2, 0x66, 0xd3, 0xe3, 0xb6, 0x53, 0xb7, 0x6c, 0xc3, 0x63, 0x9a, 0xa6, 0xdf, 0x81,
	0x9b, 0x0a, 0xc3, 0x68, 0x34, 0x6a, 0x47, 0xb4, 0x02, 0x27, 0xaa, 0x76, 0xe9, 0x3d, 0x00, 0x5c,
	0x40, 0xd3, 0xf5, 0xdb, 0x70, 0x43, 0x01, 0xec, 0x5a, 0x3b, 0x1e, 0x6f, 0xec, 0xef, 0x72, 0x97,
	0x35, 0x5b, 0x35, 0x4f, 0xbb, 0xac, 0x7f, 0x0c, 0x57, 0x15, 0x3e, 0xaa, 0x45, 0xce, 0x7d, 0x25,
	0xbb, 0xd1, 0xcb, 0xb4, 0xaa, 0x5d, 0xd5, 0x6f, 0xc2, 0x75, 0x67, 0x67, 0xc7, 0x32, 0x0d, 0xab,
	0xc6, 0xeb, 0xac, 0xd9, 0x34, 0x76, 0x19, 0x6f, 0x5a, 0xf6, 0x6e, 0x8d, 0x69, 0xd7, 0xf4, 0x1b,
	0x70, 0x6d, 0x8a, 0xb9, 0xdd, 0xb2, 0xcd, 0x3d, 0xed, 0x3a, 0xda, 0xd1, 0x34, 0x6a, 0x35, 0x6e,
	0xd9, 0xda, 0x86, 0x7e, 0x0d, 0xf4, 0xa6, 0xd4, 0xb8, 0xe9, 0x32, 0xc3, 0x13, 0x06, 0xfd, 0x18,
	0x95, 0x33, 0xa2, 0xd7, 0x9c, 0xa6, 0x20, 0xdf, 0x40, 0x1b, 0x19, 0xa6, 0xc9, 0x1a, 0x1e, 0xcf,
	0xa6, 0xb8, 0x89, 0x56, 0x90, 0x3f, 0x38, 0xb3, 0xab, 0xda, 0x2d, 0xd2, 0x83, 0x50, 0xe9, 0x4c,
	0x3d, 0x7c, 0x42, 0xfb, 0x1c, 0x03, 0xc4, 0x3e, 0x89, 0x75, 0x1b, 0xd7, 0xf5, 0xc6, 0xc6, 0x93,
	0xe4, 0x3b, 0x68, 0x79, 0xc3, 0x72, 0xb9, 0x67, 0x99, 0xfb, 0xcc, 0xd3, 0xee, 0x8e, 0x6d, 0x27,
	0x8d, 0x44, 0x2e, 0x75, 0x8f, 0xc4, 0xf3, 0x78, 0xd3, 0xa9, 0x33, 0xc7, 0x16, 0x22, 0x57, 0x70,
	0x87, 0x02, 0x5a, 0x67, 0xf5, 0x6d, 0x86, 0x73, 0x78, 0x35, 0xa6, 0xdd, 0xd7, 0xef, 0xc2, 0x2d,
	0x65, 0x0a, 0xd3, 0xa9, 0xd7, 0x1d, 0x9b, 0xb7, 0xdc, 0x5a, 0x36, 0xdb, 0xa7, 0x28, 0xa6, 0x37,
	0xe2, 0x50, 0x20, 0x48, 0xd6, 0x03, 0xfd, 0x32, 0xac, 0x1b, 0x1e, 0x67, 0x07, 0xcc, 0x3d, 0xca,
	0x56, 0xfa, 0x0c, 0x3d, 0xc7, 0x6c, 0x35, 0x3d, 0xa7, 0xce, 0x59, 0xdd, 0xf1, 0x2c, 0x53, 0x7a,
	0xf1, 0x43, 0xfd, 0x13, 0xf8, 0x58, 0x2c, 0x65, 0x5a, 0xae, 0x59, 0x63, 0xdc, 0xb0, 0x6d, 0xa7,
	0x65, 0x9b, 0x8c, 0xae, 0x62, 0x1f, 0xe1, 0xe6, 0x4c, 0xc3, 0x36, 0x59, 0x8d, 0xe0, 0x8f, 0x09,
	0x6e, 0xd4, 0x19, 0x6f, 0xb8, 0x4c, 0x3c, 0x64, 0xa8, 0x6b, 0x3f, 0x51, 0x54, 0x64, 0xd9, 0x07,
	0x96, 0xb4, 0xd8, 0x53, 0x1c, 0xb5, 0x63, 0xd9, 0x55, 0xbe, 0xe3, 0x5a, 0xcc, 0xae, 0x36, 0x79,
	0xdd, 0xf0, 0x58, 0x95, 0xb7, 0x1a, 0xc4, 0x7e, 0x36, 0xc5, 0xae, 0x59, 0xfb, 0xac, 0x9a, 0x4d,
	0xfa, 0x39, 0xd9, 0x0c, 0xd7, 0x34, 0x99, 0xed, 0x31, 0x97, 0x37, 0x9c, 0x06, 0x3d, 0x9f, 0x64,
	0x80, 0x4d, 0xfd, 0x11, 0x7c, 0x6a, 0xee, 0x19, 0xb6, 0xcd, 0x6a, 0xbc, 0x6a, 0x78, 0x96, 0xbd,
	0x2b, 0xf8, 0x38, 0x0d, 0x6f, 0x35, 0x99, 0x9b, 0x21, 0x9f, 0xe3, 0x54, 0xb9, 0x95, 0x2c, 0x93,
	0x6d, 0xbb, 0xcc, 0xd8, 0x47, 0x89, 0xcc, 0x3d, 0xed, 0x0b, 0x7d, 0x03, 0xae, 0x34, 0x99, 0xdd,
	0xb4, 0x3c, 0xeb, 0x20, 0x5b, 0x80, 0x84, 0xfc, 0x16, 0x0a, 0x39, 0xfa, 0x8c, 0xfe, 0x80, 0xf1,
	0x96, 0xed, 0x32, 0x63, 0x24, 0xe4, 0x96, 0x7e, 0x0b, 0x36, 0x32, 0x19, 0x1a, 0xfb, 0x32, 0x6c,
	0x32, 0xee, 0xb7, 0x51, 0xfd, 0xfb, 0xb6, 0x63, 0xee, 0x73, 0xcb, 0x76, 0x1d, 0x67, 0x94, 0x68,
	0xbe, 0x83, 0x91, 0x62, 0x5a, 0x07, 0x56, 0xcd, 0xfa, 0xd2, 0xa0, 0xaf, 0xf3, 0x4d, 0xc7, 0x3e,
	0x60, 0x36, 0xfe, 0xa9, 0x7d, 0x17, 0xdd, 0xc3, 0xf3, 0xc8, 0xee, 0xaa, 0x96, 0x7f, 0x83, 0x02,
	0xc3, 0x73, 0x0d, 0x7b, 0x97, 0xb9, 0xdc, 0xb2, 0x77, 0x1c, 0x6e, 0x1a, 0x6e, 0x55, 0xfb, 0x4d,
	0x74, 0xb1, 0xd6, 0xae, 0xc9, 0x9b, 0x7b, 0x86, 0xcb, 0x78, 0xc3, 0x69, 0x7a, 0xda, 0xf7, 0x90,
	0xd6, 0x64, 0x76, 0x95, 0x7e, 0x72, 0xfa, 0xb7, 0x03, 0x7f, 0x06, 0xc7, 0x4b, 0xf3, 0xec, 0x38,
	0xb5, 0x9a, 0x73, 0xc8, 0xb7, 0x0d, 0x73, 0x5f, 0xfb, 0x3e, 0x46, 0x8b, 0x24, 0x10, 0xf0, 0xcf,
	0x62, 0x66, 0x74, 0x2d, 0x73, 0x8f, 0x8f, 0x32, 0xed, 0x0f, 0x30, 0x7a, 0x1b, 0xcc, 0x6d, 0x3a,
	0xb6, 0x51, 0xe3, 0x79, 0xde, 0x9f, 0xa3, 0xe8, 0x6d, 0x09, 0x9b, 0xff, 0x48, 0x44, 0x2f, 0x2e,
	0x5c, 0x33, 0x8e, 0xb8, 0xe7, 0xec, 0x32, 0x6f, 0x8f, 0xb9, 0xda, 0x8f, 0x51, 0xc3, 0x32, 0x4c,
	0xf3, 0x1c, 0x03, 0x57, 0xc3, 0x3c, 0x32, 0x96, 0x74, 0x3b, 0xa7, 0x74, 0xe5, 0xdf, 0x2e, 0x50,
	0x7e, 0xd2, 0x4c, 0xf4, 0xce, 0xf1, 0xcd, 0xad, 0x56, 0x45, 0x78, 0x4e, 0x26, 0x7e, 0x68, 0x79,
	0x7b, 0x99, 0xde, 0x98, 0xfe, 0x19, 0x54, 0x66, 0xcb, 0x9e, 0xc3, 0xed, 0x60, 0xf8, 0xd5, 0x8d,
	0x26, 0xfa, 0x9a, 0xd1, 0x68, 0xb8, 0x68, 0x0e, 0x93, 0xa9, 0x0e, 0xbd, 0xab, 0xdf, 0x87, 0x3b,
	0xd3, 0x08, 0xd6, 0xf4, 0x8c, 0xed, 0x9a, 0xd5, 0xdc, 0x23, 0xd0, 0x1e, 0x2e, 0x37, 0x6b, 0x1a,
	0xcf, 0x75, 0xaa, 0x2d, 0xb1, 0x1f, 0xc4, 0x59, 0xb8, 0x5c, 0xe6, 0x3a, 0x4e, 0x83, 0xd9, 0xc2,
	0x79, 0x95, 0xe5, 0xf6, 0xd1, 0x88, 0xca, 0x0d, 0xb5, 0xcd, 0x0e, 0xb5, 0x1a, 0x6a, 0x0b, 0x15,
	0x88, 0xa1, 0x24, 0xec, 0x5f, 0x47, 0xfd, 0x57, 0x65, 0xa4, 0xda, 0x58, 0xf6, 0xcc, 0x3d, 0xc3,
	0x13, 0x3c, 0x07, 0xcb, 0xcf, 0xe8, 0x27, 0xdf, 0xb3, 0x08, 0xd4, 0xd0, 0x3f, 0x85, 0xbb, 0xcd,
	0x56, 0x03, 0x83, 0xaa, 0x66, 0x1c, 0x31, 0x97, 0xcf, 0xca, 0x11, 0x3f, 0xa1, 0x02, 0x59, 0xb3,
	0x1a, 0xa8, 0x20, 0x21, 0x91, 0x8b, 0x7b, 0xcb, 0x64, 0x36, 0x65, 0x38, 0x4a, 0x9f, 0x57, 0x03,
	0xae, 0x89, 0x09, 0x41, 0x5c, 0x9e, 0xab, 0x1b, 0xf2, 0xd0, 0x39, 0x24, 0xb9, 0xd5, 0xd8, 0x75,
	0x8d, 0xaa, 0xa0, 0xb7, 0xd4, 0x28, 0xa2, 0x7f, 0xdd, 0x82, 0x9a, 0x73, 0x2c, 0x9b, 0x92, 0xd1,
	0x01, 0x6e, 0x69, 0xd7, 0xb0, 0x6c, 0xee, 0x1a, 0x2e, 0xd3, 0x0e, 0x29, 0x7b, 0x8a, 0x54, 0xad,
	0xe6, 0xac, 0x97, 0x54, 0x69, 0xc7, 0x69, 0xda, 0xdc, 0x63, 0xe6, 0xbe, 0x76, 0x84, 0x91, 0xa6,
	0x50, 0xa9, 0xa2, 0x72, 0xe1, 0x88, 0xda, 0x97, 0x68, 0xcf, 0xe9, 0xa9, 0xc4, 0x50, 0xee, 0xb2,
	0x17, 0xcc, 0xf4, 0xb4, 0x9f, 0xea, 0x15, 0xb8, 0xad, 0xcc, 0x70, 0xc0, 0x5c, 0x4c, 0x0e, 0x3f,
	0x69, 0xb1, 0x26, 0xd9, 0xb2, 0xc9, 0x3c, 0xed, 0xb7, 0x30, 0x84, 0xb6, 0x5d, 0xc3, 0xae, 0x8a,
	0x20, 0xd4, 0xfe, 0x3c, 0x12, 0x0e, 0x99, 0x7b, 0xe8, 0xd4, 0x76, 0xf8, 0xa1, 0x65, 0x6b, 0x7f,
	0x81, 0x02, 0x92, 0x02, 0x94, 0x94, 0x86, 0xdb, 0xe6, 0x14, 0x2b, 0x44, 0x6b, 0x9a, 0xcc, 0x36,
	0x5c, 0x4b, 0xf4, 0x2a, 0xbf, 0x4d, 0xc1, 0xb6, 0x6b, 0x72, 0x25, 0xb1, 0xd4, 0x0f, 0x44, 0xd2,
	0xf4, 0x51, 0x9a, 0x7a, 0xab, 0x29, 0xe2, 0x9c, 0xab, 0x99, 0x89, 0xd2, 0x1e, 0x62, 0x8e, 0xf5,
	0x07, 0x70, 0x6f, 0x36, 0x46, 0xbe, 0xb5, 0x11, 0xac, 0x8d, 0xf9, 0xf3, 0xfd, 0x30, 0x43, 0x20,
	0x3b, 0xe7, 0x4f, 0x48, 0x8b, 0x4a, 0x58, 0x80, 0xe9, 0xae, 0x6e, 0xbd, 0xa0, 0x44, 0xec, 0x38,
	0xd5, 0xd1, 0xcb, 0xde, 0x2b, 0xfd, 0x31, 0x3c, 0x68, 0x30, 0x77, 0x87, 0x99, 0x9e, 0xc8, 0xb8,
	0xe7, 0x7a, 0x0e, 0x36, 0xd5, 0xd7, 0xe4, 0x1c, 0xd2, 0x73, 0xc8, 0x73, 0x71, 0xfe, 0x13, 0xaa,
	0x42, 0x35, 0xc7, 0x3d, 0xe2, 0x26, 0xab, 0xb1, 0x6d, 0xd7, 0xf2, 0x8e, 0x64, 0xd2, 0x43, 0x76,
	0x48, 0xea, 0xcc, 0x14, 0x39, 0xa6, 0x7f, 0xa5, 0x3f, 0x81, 0xcf, 0x32, 0x55, 0xd6, 0x50, 0xe8,
	0x7a, 0xab, 0xe6, 0x59, 0xa8, 0xd5, 0x6d, 0xd6, 0xf4, 0x78, 0xc3, 0x70, 0x3d, 0x9b, 0xb9, 0x84,
	0x7d, 0x8d, 0x92, 0x36, 0x1d, 0xd3, 0x32, 0x6a, 0x24, 0x7d, 0xcb, 0xc6, 0x35, 0xa4, 0x20, 0xfb,
	0xcc, 0xc5, 0x29, 0x44, 0xcd, 0xd6, 0x7a, 0x28, 0x29, 0x89, 0xee, 0x32, 0xea, 0x35, 0x94, 0xaa,
	0xd1, 0x27, 0xc7, 0xa2, 0x7a, 0x89, 0x13, 0xe1, 0x06, 0x44, 0xe5, 0xb2, 0xcc, 0x7d, 0x3b, 0x33,
	0xff, 0x60, 0x1a, 0xb4, 0x63, 0xb9, 0xa8, 0xdf, 0xfa, 0x78, 0xcf, 0x11, 0x35, 0x68, 0x39, 0x90,
	0x55, 0x97, 0x31, 0x82, 0xfc, 0x21, 0xf9, 0x83, 0xd0, 0x57, 0xb6, 0x11, 0x81, 0x1a, 0x87, 0xdd,
	0xd7, 0x18, 0x19, 0xac, 0xd9, 0x70, 0x5c, 0x8f, 0x8b, 0xe2, 0x8d, 0xd4, 0x98, 0x9a, 0x5b, 0x41,
	0x6d, 0x1e, 0x35, 0x89, 0x96, 0xa0, 0xa1, 0x47, 0x2a, 0x34, 0x2c, 0xde, 0x70, 0x9d, 0x7a, 0xc3,
	0xe3, 0x46, 0xcb, 0x73, 0xb8, 0xcb, 0xb0, 0x69, 0x42, 0x58, 0x8a, 0x92, 0x4f, 0x69, 0x89, 0x7c,
	0x63, 0xac, 0xf6, 0x53, 0xcc, 0x6f, 0x53, 0x20, 0xd9, 0xf0, 0x50, 0x72, 0x7a, 0xa3, 0xc8, 0x25,
	0x0a, 0x3d, 0x52, 0xdf, 0xa2, 0x0c, 0x2a, 0xb5, 0xc9, 0x6a, 0x3b, 0x22, 0x7d, 0x72, 0xcf, 0xe1,
	0x07, 0x56, 0xd3, 0xf2, 0x1c, 0x57, 0x7b, 0xa7, 0x7f, 0x01, 0xcf, 0xa6, 0xa6, 0x37, 0x9a, 0x4d,
	0xab, 0xe9, 0x19, 0xb6, 0x27, 0xa4, 0x69, 0xb4, 0x30, 0x29, 0x8b, 0x5e, 0x53, 0x3b, 0x1b, 0xb7,
	0x57, 0x75, 0xc3, 0xc6, 0xee, 0xd3, 0x75, 0x6a, 0xb9, 0x84, 0xfb, 0x33, 0x2c, 0x52, 0x6a, 0x16,
	0xa6, 0x9a, 0x8d, 0x9c, 0xbf, 0x88, 0xb1, 0xf2, 0x8d, 0x42, 0xf1, 0x83, 0x2d, 0xed, 0x2f, 0xa1,
	0xc1, 0xb0, 0xc8, 0x30, 0x97, 0x6f, 0x5b, 0xae, 0xb7, 0x57, 0x35, 0x8e, 0xc8, 0x35, 0xe4, 0xb9,
	0xe4, 0x2f, 0xeb, 0xf7, 0xe0, 0x93, 0x03, 0xcb, 0xf5, 0x5a, 0x46, 0x8d, 0x5b, 0x75, 0x63, 0xf7,
	0x90, 0x6f, 0x63, 0x67, 0xa2, 0x88, 0xf1, 0x57, 0x30, 0x67, 0x29, 0x3b, 0xc1, 0x4d, 0x64, 0x2e,
	0xa0, 0xfd, 0x5c, 0x7f, 0x08, 0x95, 0x73, 0x98, 0xfc, 0x4b, 0x6c, 0xfc, 0x1a, 0x4e, 0xa3, 0xd5,
	0xd0, 0xfe, 0x6a, 0x41, 0xd7, 0x61, 0x35, 0x77, 0x5e, 0xd5, 0x7e, 0xa7, 0xa0, 0x5f, 0x86, 0xb5,
	0x97, 0x75, 0x2a, 0x15, 0xb2, 0xad, 0xd2, 0xfe, 0x5a, 0x41, 0xbf, 0x0e, 0x3a, 0xda, 0x1a, 0xcb,
	0xf2, 0x5e, 0x6b, 0x1b, 0x1d, 0x0c, 0xd1, 0xbf, 0x9b, 0x43, 0xcb, 0x67, 0xd1, 0xbf, 0x9e, 0x23,
	0x6e, 0xb7, 0xb6, 0xb7, 0x6b, 0x4c, 0xfb, 0x1b, 0x05, 0xfd, 0x06, 0x5c, 0x35, 0x2c, 0x6e, 0xd9,
	0xcd, 0x86, 0xe5, 0x8a, 0x8e, 0x46, 0x1c, 0xda, 0xb4, 0xdf, 0x2b, 0xe8, 0x37, 0xe1, 0xda, 0x21,
	0xab, 0x56, 0xc9, 0x45, 0x5d, 0xa7, 0x81, 0x9d, 0xbb, 0x64, 0xfe, 0xcd, 0x82, 0xbe, 0x0a, 0x25,
	0xef, 0xd0, 0x12, 0x15, 0xe7, 0x6f, 0xd1, 0x3c, 0x94, 0x68, 0xc8, 0x34, 0x24, 0x91, 0x84, 0xfe,
	0xed, 0x82, 0x7e, 0x1f, 0x6e, 0x67, 0xf3, 0x98, 0x8e, 0x4d, 0xa9, 0x03, 0x2b, 0xa3, 0x7b, 0x30,
	0x9a, 0xef, 0xf7, 0x73, 0x20, 0xc3, 0xa5, 0x5e, 0x69, 0x12, 0xf4, 0x77, 0x0a, 0x4f, 0xfe, 0xd5,
	0x1a, 0xac, 0xd6, 0x73, 0xff, 0x70, 0x08, 0x7b, 0x10, 0xda, 0x21, 0x6f, 0x3a, 0x2d, 0xd7, 0x94,
	0x1f, 0x2c, 0x64, 0x59, 0x03, 0xfd, 0x40, 0x2b, 0x88, 0x46, 0xc5, 0x70, 0xb7, 0x8f, 0x78, 0x83,
	0x39, 0x8d, 0x1a, 0xd3, 0x8a, 0x68, 0x6c, 0x54, 0x84, 0x18, 0xc4, 0xb1, 0xc7, 0x14, 0x2d, 0x26,
	0x67, 0x2f, 0x8d, 0xba, 0x36, 0x87, 0x8d, 0xe7, 0x6c, 0xbe, 0x68, 0x3c, 0xe7, 0xb1, 0xf2, 0x29,
	0x00, 0xcc, 0x27, 0x9e, 0xb1, 0x2b, 0xb9, 0x0b, 0xd4, 0x6f, 0x9c, 0xc3, 0xcd, 0x2c, 0xb2, 0xa8,
	0x3f, 0x84, 0xfb, 0x0a, 0x08, 0x3b, 0x81, 0x1d, 0xd7, 0xa9, 0x8b, 0xb4, 0xba, 0x5d, 0x33, 0xcc,
	0x7d, 0x5e, 0xb3, 0x9a, 0x9e, 0xb6, 0x44, 0xfd, 0xcd, 0x18, 0x98, 0x6d, 0x8e, 0x70, 0x14, 0x7a,
	0x25, 0x4a, 0xbe, 0x79, 0x71, 0x65, 0x87, 0xa0, 0x2d, 0x4f, 0xf0, 0x46, 0x3d, 0x1e, 0x73, 0x35,
	0x98, 0xd8, 0x85, 0x58, 0x7e, 0xd7, 0x14, 0xbd, 0x68, 0x79, 0x52, 0x49, 0xc8, 0x55, 0xda, 0xb7,
	0x15, 0xfd, 0x19, 0x3c, 0x9a, 0xe4, 0x9f, 0xd7, 0x87, 0x69, 0xab, 0xb3, 0x66, 0x13, 0xd5, 0xc4,
	0xa8, 0x1a, 0x78, 0x86, 0x7e, 0x0e, 0x4f, 0x27, 0xf9, 0xa3, 0x53, 0x02, 0xf3, 0xf0, 0xf4, 0xb9,
	0x6b, 0xd8, 0xbb, 0x78, 0x0a, 0x21, 0xb5, 0xac, 0x4f, 0xe8, 0x4f, 0x88, 0x27, 0xdb, 0x64, 0x65,
	0xb0, 0xa6, 0x61, 0xf9, 0x99, 0x31, 0xb3, 0x6c, 0xb5, 0x2e, 0x4d, 0xa8, 0x97, 0xd8, 0x98, 0x58,
	0x29, 0x79, 0xee, 0xb1, 0x23, 0x4d, 0x9f, 0x35, 0xc1, 0xa8, 0xc1, 0xd2, 0x2e, 0x53, 0x95, 0x3d,
	0x6f, 0x7e, 0x5e, 0xb5, 0x44, 0xf2, 0xbd, 0x82, 0xe5, 0xec, 0x7c, 0x98, 0x21, 0xaf, 0x0e, 0xb0,
	0x30, 0x6b, 0x57, 0x31, 0xd9, 0x4c, 0x62, 0x73, 0x7d, 0x99, 0x76, 0x8d, 0x0a, 0xc8, 0x6c, 0x88,
	0xec, 0xd1, 0xb4, 0xeb, 0xd8, 0x2f, 0x9e, 0x83, 0x19, 0x37, 0x66, 0x1b, 0xb3, 0x66, 0x42, 0xc1,
	0x76, 0x1b, 0x1e, 0xdf, 0x76, 0xb0, 0xae, 0x69, 0x1f, 0xcf, 0xb2, 0x8e, 0x2c, 0x5f, 0x7b, 0x4e,
	0x9d, 0x35, 0x30, 0x2d, 0xd3, 0x66, 0x0c, 0xfa, 0x8f, 0x76, 0x83, 0x9a, 0x94, 0x49, 0xd7, 0x52,
	0xaa, 0xae, 0xe9, 0xd4, 0x19, 0x1d, 0x63, 0x6e, 0xce, 0xda, 0x6b, 0xae, 0x72, 0x8a, 0x6b, 0x80,
	0x49, 0x88, 0xdd, 0x30, 0x85, 0x89, 0xac, 0xba, 0xf6, 0xc9, 0x64, 0x00, 0x20, 0x40, 0xa4, 0x7d,
	0xed, 0xb6, 0xfe, 0x39, 0x3c, 0x9e, 0xe4, 0x8d, 0x8a, 0xb2, 0xe7, 0x70, 0x71, 0xc4, 0xc8, 0xd2,
	0xea, 0x9d, 0x59, 0xd6, 0x9c, 0xa8, 0xa8, 0x56, 0x5d, 0xbb, 0xab, 0x6f, 0xc2, 0x93, 0x99, 0x52,
	0xcf, 0x9e, 0xf6, 0xde, 0x2c, 0x7d, 0xa8, 0xd5, 0x8a, 0xd9, 0x78, 0x42, 0x34, 0x99, 0x56, 0x99,
	0xa5, 0x6a, 0x1b, 0x7b, 0x7b, 0xcb, 0xcc, 0x94, 0x47, 0x8a, 0xcc, 0x44, 0xb9, 0x3f, 0x6b, 0xea,
	0x71, 0xd7, 0x31, 0x5e, 0x43, 0xfb, 0x74, 0x96, 0xaa, 0xa5, 0x15, 0x29, 0xd5, 0xba, 0x5a, 0x67,
	0xd6, 0xbe, 0x26, 0x6b, 0x3d, 0xdf, 0xa2, 0x3c, 0x80, 0xc5, 0x39, 0x98, 0x95, 0x04, 0x18, 0x17,
	0x03, 0xa8, 0xac, 0xed, 0x19, 0xb5, 0x1d, 0xde, 0x34, 0x5d, 0xc6, 0x6c, 0xed, 0xd5, 0xac, 0x98,
	0xa5, 0x69, 0xf7, 0x70, 0x5b, 0x1e, 0x33, 0xea, 0x22, 0xb8, 0xbb, 0xb3, 0x82, 0x65, 0x46, 0x70,
	0x63, 0xe5, 0xfe, 0x8a, 0x0e, 0x6e, 0xb3, 0x26, 0xb5, 0xea, 0x62, 0x4a, 0x71, 0xf3, 0xf3, 0x7a,
	0x56, 0x18, 0x13, 0x6e, 0x87, 0xb1, 0xaa, 0xd6, 0x9b, 0x48, 0xda, 0xc4, 0x9e, 0x68, 0x08, 0xb4,
	0xfe, 0xb9, 0x1b, 0x98, 0x28, 0xe3, 0x83, 0x59, 0xa9, 0xd5, 0x73, 0xb0, 0xa9, 0x30, 0x1b, 0x5a,
	0x34, 0x4b, 0x64, 0xa9, 0x65, 0x0c, 0x0a, 0x69, 0x67, 0x6d, 0x38, 0x2b, 0x69, 0xee, 0xd4, 0x8c,
	0xe6, 0x9e, 0x88, 0xb2, 0xaf, 0x67, 0x6d, 0x69, 0x54, 0x82, 0xb5, 0xf8, 0xc9, 0x8f, 0x40, 0xa3,
	0x7f, 0xc6, 0x32, 0x48, 0xc2, 0xd1, 0xa7, 0x7d, 0x57, 0xe1, 0x52, 0x56, 0x32, 0x99, 0xdd, 0xf4,
	0xac, 0x03, 0xeb, 0x00, 0xcb, 0xe6, 0x15, 0xd0, 0xea, 0x8e, 0xcd, 0x8e, 0x54, 0x6a, 0xe1, 0x49,
	0x07, 0x4a, 0x75, 0xf9, 0x0f, 0x6a, 0xf5, 0x15, 0x28, 0xd9, 0x0e, 0xaf, 0x19, 0xdb, 0xac, 0x36,
	0xfe, 0x2e, 0x50, 0x5c, 0x40, 0x30, 0x57, 0xc3, 0x7e, 0xe1, 0x3a, 0xca, 0x42, 0x00, 0x2e, 0x8f,
	0xa9, 0x2d, 0x3a, 0x60, 0x6b, 0xc5, 0x3c, 0x33, 0x1f, 0xde, 0x73, 0x4f, 0x52, 0xb8, 0x34, 0xf5,
	0x0f, 0x62, 0x32, 0x5f, 0x75, 0x99, 0xc9, 0xb0, 0xf9, 0x1f, 0x1f, 0xf9, 0x2d, 0x93, 0x3a, 0x15,
	0xed, 0xa3, 0x2c, 0xbd, 0xa9, 0x10, 0xa7, 0xe5, 0xe5, 0x51, 0xf4, 0x91, 0x28, 0xa2, 0xaa, 0x0e,
	0x32, 0x32, 0xb0, 0x56, 0x7c, 0xf2, 0x6f, 0x8b, 0xa0, 0xe7, 0x1f, 0xb2, 0x49, 0x3f, 0x77, 0xe1,
	0x56, 0x8d, 0x1d, 0xb0, 0x1a, 0x37, 0x0e, 0x29, 0x80, 0x3c, 0x56, 0x9f, 0xf8, 0x1e, 0xf5, 0x36,
	0xdc, 0x98, 0x42, 0xec, 0x31, 0xa3, 0xca, 0x0f, 0x99, 0x81, 0x8a, 0xb8, 0x01, 0xd7, 0xa6, 0xf9,
	0x8e, 0xdb, 0xc4, 0xc6, 0x63, 0x16, 0xaf, 0xce, 0xaa, 0x46, 0x4d, 0x34, 0x1d, 0x53, 0xbc, 0x2a,
	0x33, 0x1d, 0xd1, 0x99, 0x69, 0xf3, 0x33, 0x17, 0xb6, 0xfd, 0x7e, 0x30, 0xec, 0xf9, 0x69, 0xa0,
	0x2d, 0x60, 0xea, 0x9a, 0x9e, 0xdc, 0x32, 0xb9, 0xf3, 0xea, 0x55, 0xd8, 0x0e, 0xfd, 0x9e, 0x19,
	0xc4, 0xa9, 0xb6, 0x38, 0x13, 0x66, 0xd9, 0x3b, 0xe8, 0x14, 0xe2, 0x52, 0x0b, 0xad, 0xb2, 0x84,
	0x1e, 0x3c, 0x05, 0x6b, 0x18, 0xe6, 0x3e, 0xc6, 0x7d, 0xe7, 0xc9, 0x9f, 0x14, 0x61, 0x43, 0x3c,
	0xa2, 0xcf, 0xd0, 0xe1, 0x43, 0xb8, 0x2f, 0xeb, 0xcc, 0x37, 0xa8, 0xf2, 0x3e, 0xdc, 0x39, 0x0f,
	0xd8, 0xf0, 0xdb, 0xaf, 0xfd, 0x6e, 0xa0, 0x15, 0x30, 0xbf, 0x9d, 0x07, 0xca, 0xed, 0xac, 0x88,
	0x3b, 0x3b, 0x0f, 0x39, 0xd6, 0xd3, 0x1c, 0xfa, 0xcd, 0x79, 0xb0, 0xbd, 0xc0, 0xef, 0x1c, 0x06,
	0x7e, 0xac, 0xcd, 0xa3, 0x03, 0x9e, 0x8b, 0x8a, 0xe2, 0x04, 0x15, 0xfe, 0x1e, 0xc8, 0x4e, 0x2f,
	0xf2, 0x51, 0xd9, 0x4f, 0xe1, 0xe1, 0x79, 0x10, 0xf9, 0xf9, 0x96, 0x35, 0x78, 0x15, 0x61, 0xab,
	0xaf, 0x2d, 0x3d, 0xb1, 0x40, 0x17, 0x8f, 0x42, 0xd5, 0xb0, 0x33, 0x7e, 0xf5, 0x59, 0x87, 0x72,
	0x5e, 0x6b, 0x00, 0x8b, 0xb2, 0xae, 0x14, 0xf0, 0x6f, 0xd9, 0xce, 0x17, 0x95, 0xcf, 0x20, 0xe7,
	0x9e, 0xfc, 0xc3, 0x02, 0x68, 0x93, 0x0f, 0x35, 0xa8, 0x6e, 0xab, 0xce, 0xf1, 0x18, 0xd0, 0x30,
	0x8e, 0xe8, 0x43, 0xf7, 0x19, 0x9f, 0x5b, 0x7f, 0x01, 0xcf, 0x66, 0x81, 0xb2, 0xf6, 0x5c, 0xf4,
	0x29, 0x8d, 0x3d, 0xc7, 0x73, 0x44, 0xb2, 0x2e, 0xe0, 0xe1, 0xfb, 0x7d, 0x23, 0x4c, 0xec, 0x95,
	0x08, 0x5a, 0xdc, 0xde, 0xff, 0xe3, 0x5f, 0xdc, 0x2e, 0xfc, 0xa7, 0x5f, 0xdc, 0x2e, 0xfc, 0xb7,
	0x5f, 0xdc, 0x2e, 0xfc, 0xe1, 0x7f, 0xbf, 0xfd, 0x11, 0x6c, 0xb4, 0xa3, 0xfe, 0xe6, 0x59, 0x78,
	0x16, 0x9d, 0x6e, 0x76, 0xfd, 0xcd, 0x7e, 0xd4, 0x09, 0x7a, 0xe2, 0x7f, 0x11, 0xf4, 0xe5, 0x9d,
	0x6e, 0xd4, 0xf3, 0x07, 0xdd, 0xcd, 0xef, 0x6e, 0xa5, 0xe9, 0x66, 0x3b, 0xea, 0x3f, 0x27, 0x72,
	0x3b, 0xea, 0x3d, 0xf7, 0x87, 0xc3, 0xe7, 0x61, 0xff, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0xa2,
	0x9a, 0x35, 0x5d, 0x87, 0x48, 0x00, 0x00,
}
