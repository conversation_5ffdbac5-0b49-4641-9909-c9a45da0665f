// Code generated by protoc-gen-go. DO NOT EDIT.
// source: virtual_avatar_logic/virtual_avatar_logic.proto

package virtual_avatar_logic // import "golang.52tt.com/protocol/app/virtual_avatar_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type VADisplayType int32

const (
	VADisplayType_VA_DISPLAY_TYPE_UNSPECIFIED VADisplayType = 0
	VADisplayType_VA_DISPLAY_TYPE_COMMON      VADisplayType = 1
	VADisplayType_VA_DISPLAY_TYPE_CP          VADisplayType = 2
)

var VADisplayType_name = map[int32]string{
	0: "VA_DISPLAY_TYPE_UNSPECIFIED",
	1: "VA_DISPLAY_TYPE_COMMON",
	2: "VA_DISPLAY_TYPE_CP",
}
var VADisplayType_value = map[string]int32{
	"VA_DISPLAY_TYPE_UNSPECIFIED": 0,
	"VA_DISPLAY_TYPE_COMMON":      1,
	"VA_DISPLAY_TYPE_CP":          2,
}

func (x VADisplayType) String() string {
	return proto.EnumName(VADisplayType_name, int32(x))
}
func (VADisplayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{0}
}

type VAResourceType int32

const (
	VAResourceType_VA_RESOURCE_TYPE_UNSPECIFIED           VAResourceType = 0
	VAResourceType_VA_RESOURCE_TYPE_MIC_SPACE             VAResourceType = 1
	VAResourceType_VA_RESOURCE_TYPE_PERSONAL_CARD_PROFILE VAResourceType = 2
	VAResourceType_VA_RESOURCE_TYPE_ENTER_CHANNEL_EFFECT  VAResourceType = 3
)

var VAResourceType_name = map[int32]string{
	0: "VA_RESOURCE_TYPE_UNSPECIFIED",
	1: "VA_RESOURCE_TYPE_MIC_SPACE",
	2: "VA_RESOURCE_TYPE_PERSONAL_CARD_PROFILE",
	3: "VA_RESOURCE_TYPE_ENTER_CHANNEL_EFFECT",
}
var VAResourceType_value = map[string]int32{
	"VA_RESOURCE_TYPE_UNSPECIFIED":           0,
	"VA_RESOURCE_TYPE_MIC_SPACE":             1,
	"VA_RESOURCE_TYPE_PERSONAL_CARD_PROFILE": 2,
	"VA_RESOURCE_TYPE_ENTER_CHANNEL_EFFECT":  3,
}

func (x VAResourceType) String() string {
	return proto.EnumName(VAResourceType_name, int32(x))
}
func (VAResourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{1}
}

type VAUseScopeType int32

const (
	VAUseScopeType_VA_USE_SCOPE_TYPE_UNSPECIFIED     VAUseScopeType = 0
	VAUseScopeType_VA_USE_SCOPE_TYPE_MIC_SPACE       VAUseScopeType = 1
	VAUseScopeType_VA_USE_SCOPE_TYPE_PERSON_PAGE     VAUseScopeType = 2
	VAUseScopeType_VA_USE_SCOPE_TYPE_CH_PROFILE_CARD VAUseScopeType = 3
)

var VAUseScopeType_name = map[int32]string{
	0: "VA_USE_SCOPE_TYPE_UNSPECIFIED",
	1: "VA_USE_SCOPE_TYPE_MIC_SPACE",
	2: "VA_USE_SCOPE_TYPE_PERSON_PAGE",
	3: "VA_USE_SCOPE_TYPE_CH_PROFILE_CARD",
}
var VAUseScopeType_value = map[string]int32{
	"VA_USE_SCOPE_TYPE_UNSPECIFIED":     0,
	"VA_USE_SCOPE_TYPE_MIC_SPACE":       1,
	"VA_USE_SCOPE_TYPE_PERSON_PAGE":     2,
	"VA_USE_SCOPE_TYPE_CH_PROFILE_CARD": 3,
}

func (x VAUseScopeType) String() string {
	return proto.EnumName(VAUseScopeType_name, int32(x))
}
func (VAUseScopeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{2}
}

// 资源人物类型
type ResCharacterType int32

const (
	ResCharacterType_RES_CHARACTER_TYPE_UNSPECIFIED ResCharacterType = 0
	ResCharacterType_RES_CHARACTER_TYPE_USER_A      ResCharacterType = 1
	ResCharacterType_RES_CHARACTER_TYPE_USER_B      ResCharacterType = 2
)

var ResCharacterType_name = map[int32]string{
	0: "RES_CHARACTER_TYPE_UNSPECIFIED",
	1: "RES_CHARACTER_TYPE_USER_A",
	2: "RES_CHARACTER_TYPE_USER_B",
}
var ResCharacterType_value = map[string]int32{
	"RES_CHARACTER_TYPE_UNSPECIFIED": 0,
	"RES_CHARACTER_TYPE_USER_A":      1,
	"RES_CHARACTER_TYPE_USER_B":      2,
}

func (x ResCharacterType) String() string {
	return proto.EnumName(ResCharacterType_name, int32(x))
}
func (ResCharacterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{3}
}

type UserMicAvatarType int32

const (
	UserMicAvatarType_USER_MIC_AVATAR_TYPE_UNSPECIFIED UserMicAvatarType = 0
	UserMicAvatarType_USER_MIC_AVATAR_TYPE_NORMAL      UserMicAvatarType = 1
	UserMicAvatarType_USER_MIC_AVATAR_TYPE_VIRTUAL     UserMicAvatarType = 2
)

var UserMicAvatarType_name = map[int32]string{
	0: "USER_MIC_AVATAR_TYPE_UNSPECIFIED",
	1: "USER_MIC_AVATAR_TYPE_NORMAL",
	2: "USER_MIC_AVATAR_TYPE_VIRTUAL",
}
var UserMicAvatarType_value = map[string]int32{
	"USER_MIC_AVATAR_TYPE_UNSPECIFIED": 0,
	"USER_MIC_AVATAR_TYPE_NORMAL":      1,
	"USER_MIC_AVATAR_TYPE_VIRTUAL":     2,
}

func (x UserMicAvatarType) String() string {
	return proto.EnumName(UserMicAvatarType_name, int32(x))
}
func (UserMicAvatarType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{4}
}

type SetVirtualAvatarUseScopeRequest_OpType int32

const (
	SetVirtualAvatarUseScopeRequest_OP_TYPE_UNSPECIFIED SetVirtualAvatarUseScopeRequest_OpType = 0
	SetVirtualAvatarUseScopeRequest_OP_TYPE_SET         SetVirtualAvatarUseScopeRequest_OpType = 1
	SetVirtualAvatarUseScopeRequest_OP_TYPE_REMOVE      SetVirtualAvatarUseScopeRequest_OpType = 2
)

var SetVirtualAvatarUseScopeRequest_OpType_name = map[int32]string{
	0: "OP_TYPE_UNSPECIFIED",
	1: "OP_TYPE_SET",
	2: "OP_TYPE_REMOVE",
}
var SetVirtualAvatarUseScopeRequest_OpType_value = map[string]int32{
	"OP_TYPE_UNSPECIFIED": 0,
	"OP_TYPE_SET":         1,
	"OP_TYPE_REMOVE":      2,
}

func (x SetVirtualAvatarUseScopeRequest_OpType) String() string {
	return proto.EnumName(SetVirtualAvatarUseScopeRequest_OpType_name, int32(x))
}
func (SetVirtualAvatarUseScopeRequest_OpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{11, 0}
}

// 获取虚拟形象入口状态
type GetVirtualAvatarEntryRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVirtualAvatarEntryRequest) Reset()         { *m = GetVirtualAvatarEntryRequest{} }
func (m *GetVirtualAvatarEntryRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualAvatarEntryRequest) ProtoMessage()    {}
func (*GetVirtualAvatarEntryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{0}
}
func (m *GetVirtualAvatarEntryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualAvatarEntryRequest.Unmarshal(m, b)
}
func (m *GetVirtualAvatarEntryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualAvatarEntryRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualAvatarEntryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualAvatarEntryRequest.Merge(dst, src)
}
func (m *GetVirtualAvatarEntryRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualAvatarEntryRequest.Size(m)
}
func (m *GetVirtualAvatarEntryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualAvatarEntryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualAvatarEntryRequest proto.InternalMessageInfo

func (m *GetVirtualAvatarEntryRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetVirtualAvatarEntryResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HaveAccess           bool          `protobuf:"varint,2,opt,name=have_access,json=haveAccess,proto3" json:"have_access,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetVirtualAvatarEntryResponse) Reset()         { *m = GetVirtualAvatarEntryResponse{} }
func (m *GetVirtualAvatarEntryResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualAvatarEntryResponse) ProtoMessage()    {}
func (*GetVirtualAvatarEntryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{1}
}
func (m *GetVirtualAvatarEntryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualAvatarEntryResponse.Unmarshal(m, b)
}
func (m *GetVirtualAvatarEntryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualAvatarEntryResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualAvatarEntryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualAvatarEntryResponse.Merge(dst, src)
}
func (m *GetVirtualAvatarEntryResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualAvatarEntryResponse.Size(m)
}
func (m *GetVirtualAvatarEntryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualAvatarEntryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualAvatarEntryResponse proto.InternalMessageInfo

func (m *GetVirtualAvatarEntryResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVirtualAvatarEntryResponse) GetHaveAccess() bool {
	if m != nil {
		return m.HaveAccess
	}
	return false
}

type VAResourceConf struct {
	EffectRes            string   `protobuf:"bytes,1,opt,name=effect_res,json=effectRes,proto3" json:"effect_res,omitempty"`
	EffectResMd5         string   `protobuf:"bytes,2,opt,name=effect_res_md5,json=effectResMd5,proto3" json:"effect_res_md5,omitempty"`
	ResType              uint32   `protobuf:"varint,3,opt,name=res_type,json=resType,proto3" json:"res_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VAResourceConf) Reset()         { *m = VAResourceConf{} }
func (m *VAResourceConf) String() string { return proto.CompactTextString(m) }
func (*VAResourceConf) ProtoMessage()    {}
func (*VAResourceConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{2}
}
func (m *VAResourceConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VAResourceConf.Unmarshal(m, b)
}
func (m *VAResourceConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VAResourceConf.Marshal(b, m, deterministic)
}
func (dst *VAResourceConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VAResourceConf.Merge(dst, src)
}
func (m *VAResourceConf) XXX_Size() int {
	return xxx_messageInfo_VAResourceConf.Size(m)
}
func (m *VAResourceConf) XXX_DiscardUnknown() {
	xxx_messageInfo_VAResourceConf.DiscardUnknown(m)
}

var xxx_messageInfo_VAResourceConf proto.InternalMessageInfo

func (m *VAResourceConf) GetEffectRes() string {
	if m != nil {
		return m.EffectRes
	}
	return ""
}

func (m *VAResourceConf) GetEffectResMd5() string {
	if m != nil {
		return m.EffectResMd5
	}
	return ""
}

func (m *VAResourceConf) GetResType() uint32 {
	if m != nil {
		return m.ResType
	}
	return 0
}

// 一套资源
type VAResourceSet struct {
	UserItemId           uint32            `protobuf:"varint,1,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	DisplayType          uint32            `protobuf:"varint,2,opt,name=display_type,json=displayType,proto3" json:"display_type,omitempty"`
	ResCfg               []*VAResourceConf `protobuf:"bytes,3,rep,name=res_cfg,json=resCfg,proto3" json:"res_cfg,omitempty"`
	InUse                bool              `protobuf:"varint,4,opt,name=in_use,json=inUse,proto3" json:"in_use,omitempty"`
	VaId                 uint32            `protobuf:"varint,5,opt,name=va_id,json=vaId,proto3" json:"va_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *VAResourceSet) Reset()         { *m = VAResourceSet{} }
func (m *VAResourceSet) String() string { return proto.CompactTextString(m) }
func (*VAResourceSet) ProtoMessage()    {}
func (*VAResourceSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{3}
}
func (m *VAResourceSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VAResourceSet.Unmarshal(m, b)
}
func (m *VAResourceSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VAResourceSet.Marshal(b, m, deterministic)
}
func (dst *VAResourceSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VAResourceSet.Merge(dst, src)
}
func (m *VAResourceSet) XXX_Size() int {
	return xxx_messageInfo_VAResourceSet.Size(m)
}
func (m *VAResourceSet) XXX_DiscardUnknown() {
	xxx_messageInfo_VAResourceSet.DiscardUnknown(m)
}

var xxx_messageInfo_VAResourceSet proto.InternalMessageInfo

func (m *VAResourceSet) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *VAResourceSet) GetDisplayType() uint32 {
	if m != nil {
		return m.DisplayType
	}
	return 0
}

func (m *VAResourceSet) GetResCfg() []*VAResourceConf {
	if m != nil {
		return m.ResCfg
	}
	return nil
}

func (m *VAResourceSet) GetInUse() bool {
	if m != nil {
		return m.InUse
	}
	return false
}

func (m *VAResourceSet) GetVaId() uint32 {
	if m != nil {
		return m.VaId
	}
	return 0
}

// 用户虚拟形象信息
type UserVirtualAvatarInfo struct {
	User                 *app.UserProfile `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Name                 string           `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	BasePic              string           `protobuf:"bytes,3,opt,name=base_pic,json=basePic,proto3" json:"base_pic,omitempty"`
	ResSet               *VAResourceSet   `protobuf:"bytes,4,opt,name=res_set,json=resSet,proto3" json:"res_set,omitempty"`
	CpUser               *app.UserProfile `protobuf:"bytes,5,opt,name=cp_user,json=cpUser,proto3" json:"cp_user,omitempty"`
	RelateName           string           `protobuf:"bytes,6,opt,name=relate_name,json=relateName,proto3" json:"relate_name,omitempty"`
	UserChar             uint32           `protobuf:"varint,7,opt,name=user_char,json=userChar,proto3" json:"user_char,omitempty"`
	CpUserChar           uint32           `protobuf:"varint,8,opt,name=cp_user_char,json=cpUserChar,proto3" json:"cp_user_char,omitempty"`
	ExpireTs             int64            `protobuf:"varint,9,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserVirtualAvatarInfo) Reset()         { *m = UserVirtualAvatarInfo{} }
func (m *UserVirtualAvatarInfo) String() string { return proto.CompactTextString(m) }
func (*UserVirtualAvatarInfo) ProtoMessage()    {}
func (*UserVirtualAvatarInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{4}
}
func (m *UserVirtualAvatarInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserVirtualAvatarInfo.Unmarshal(m, b)
}
func (m *UserVirtualAvatarInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserVirtualAvatarInfo.Marshal(b, m, deterministic)
}
func (dst *UserVirtualAvatarInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserVirtualAvatarInfo.Merge(dst, src)
}
func (m *UserVirtualAvatarInfo) XXX_Size() int {
	return xxx_messageInfo_UserVirtualAvatarInfo.Size(m)
}
func (m *UserVirtualAvatarInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserVirtualAvatarInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserVirtualAvatarInfo proto.InternalMessageInfo

func (m *UserVirtualAvatarInfo) GetUser() *app.UserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *UserVirtualAvatarInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserVirtualAvatarInfo) GetBasePic() string {
	if m != nil {
		return m.BasePic
	}
	return ""
}

func (m *UserVirtualAvatarInfo) GetResSet() *VAResourceSet {
	if m != nil {
		return m.ResSet
	}
	return nil
}

func (m *UserVirtualAvatarInfo) GetCpUser() *app.UserProfile {
	if m != nil {
		return m.CpUser
	}
	return nil
}

func (m *UserVirtualAvatarInfo) GetRelateName() string {
	if m != nil {
		return m.RelateName
	}
	return ""
}

func (m *UserVirtualAvatarInfo) GetUserChar() uint32 {
	if m != nil {
		return m.UserChar
	}
	return 0
}

func (m *UserVirtualAvatarInfo) GetCpUserChar() uint32 {
	if m != nil {
		return m.CpUserChar
	}
	return 0
}

func (m *UserVirtualAvatarInfo) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

// 获取用户虚拟形象列表
type GetUserVirtualAvatarListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserVirtualAvatarListRequest) Reset()         { *m = GetUserVirtualAvatarListRequest{} }
func (m *GetUserVirtualAvatarListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualAvatarListRequest) ProtoMessage()    {}
func (*GetUserVirtualAvatarListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{5}
}
func (m *GetUserVirtualAvatarListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualAvatarListRequest.Unmarshal(m, b)
}
func (m *GetUserVirtualAvatarListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualAvatarListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualAvatarListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualAvatarListRequest.Merge(dst, src)
}
func (m *GetUserVirtualAvatarListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualAvatarListRequest.Size(m)
}
func (m *GetUserVirtualAvatarListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualAvatarListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualAvatarListRequest proto.InternalMessageInfo

func (m *GetUserVirtualAvatarListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserVirtualAvatarListResponse struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	VaList               []*UserVirtualAvatarInfo `protobuf:"bytes,2,rep,name=va_list,json=vaList,proto3" json:"va_list,omitempty"`
	AutoPlayDurationSec  uint32                   `protobuf:"varint,3,opt,name=auto_play_duration_sec,json=autoPlayDurationSec,proto3" json:"auto_play_duration_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetUserVirtualAvatarListResponse) Reset()         { *m = GetUserVirtualAvatarListResponse{} }
func (m *GetUserVirtualAvatarListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualAvatarListResponse) ProtoMessage()    {}
func (*GetUserVirtualAvatarListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{6}
}
func (m *GetUserVirtualAvatarListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualAvatarListResponse.Unmarshal(m, b)
}
func (m *GetUserVirtualAvatarListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualAvatarListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualAvatarListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualAvatarListResponse.Merge(dst, src)
}
func (m *GetUserVirtualAvatarListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualAvatarListResponse.Size(m)
}
func (m *GetUserVirtualAvatarListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualAvatarListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualAvatarListResponse proto.InternalMessageInfo

func (m *GetUserVirtualAvatarListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserVirtualAvatarListResponse) GetVaList() []*UserVirtualAvatarInfo {
	if m != nil {
		return m.VaList
	}
	return nil
}

func (m *GetUserVirtualAvatarListResponse) GetAutoPlayDurationSec() uint32 {
	if m != nil {
		return m.AutoPlayDurationSec
	}
	return 0
}

// 用户佩戴虚拟形象
type SetUserVirtualAvatarInUseRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	UserItemId           uint32       `protobuf:"varint,2,opt,name=user_item_id,json=userItemId,proto3" json:"user_item_id,omitempty"`
	DisplayType          uint32       `protobuf:"varint,3,opt,name=display_type,json=displayType,proto3" json:"display_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetUserVirtualAvatarInUseRequest) Reset()         { *m = SetUserVirtualAvatarInUseRequest{} }
func (m *SetUserVirtualAvatarInUseRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserVirtualAvatarInUseRequest) ProtoMessage()    {}
func (*SetUserVirtualAvatarInUseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{7}
}
func (m *SetUserVirtualAvatarInUseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserVirtualAvatarInUseRequest.Unmarshal(m, b)
}
func (m *SetUserVirtualAvatarInUseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserVirtualAvatarInUseRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserVirtualAvatarInUseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserVirtualAvatarInUseRequest.Merge(dst, src)
}
func (m *SetUserVirtualAvatarInUseRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserVirtualAvatarInUseRequest.Size(m)
}
func (m *SetUserVirtualAvatarInUseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserVirtualAvatarInUseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserVirtualAvatarInUseRequest proto.InternalMessageInfo

func (m *SetUserVirtualAvatarInUseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserVirtualAvatarInUseRequest) GetUserItemId() uint32 {
	if m != nil {
		return m.UserItemId
	}
	return 0
}

func (m *SetUserVirtualAvatarInUseRequest) GetDisplayType() uint32 {
	if m != nil {
		return m.DisplayType
	}
	return 0
}

type SetUserVirtualAvatarInUseResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserVirtualAvatarInUseResponse) Reset()         { *m = SetUserVirtualAvatarInUseResponse{} }
func (m *SetUserVirtualAvatarInUseResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserVirtualAvatarInUseResponse) ProtoMessage()    {}
func (*SetUserVirtualAvatarInUseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{8}
}
func (m *SetUserVirtualAvatarInUseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserVirtualAvatarInUseResponse.Unmarshal(m, b)
}
func (m *SetUserVirtualAvatarInUseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserVirtualAvatarInUseResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserVirtualAvatarInUseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserVirtualAvatarInUseResponse.Merge(dst, src)
}
func (m *SetUserVirtualAvatarInUseResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserVirtualAvatarInUseResponse.Size(m)
}
func (m *SetUserVirtualAvatarInUseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserVirtualAvatarInUseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserVirtualAvatarInUseResponse proto.InternalMessageInfo

func (m *SetUserVirtualAvatarInUseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取用户使用中的虚拟形象信息
type GetUserVirtualAvatarInUseRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	SourceType           uint32       `protobuf:"varint,3,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserVirtualAvatarInUseRequest) Reset()         { *m = GetUserVirtualAvatarInUseRequest{} }
func (m *GetUserVirtualAvatarInUseRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualAvatarInUseRequest) ProtoMessage()    {}
func (*GetUserVirtualAvatarInUseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{9}
}
func (m *GetUserVirtualAvatarInUseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualAvatarInUseRequest.Unmarshal(m, b)
}
func (m *GetUserVirtualAvatarInUseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualAvatarInUseRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualAvatarInUseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualAvatarInUseRequest.Merge(dst, src)
}
func (m *GetUserVirtualAvatarInUseRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualAvatarInUseRequest.Size(m)
}
func (m *GetUserVirtualAvatarInUseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualAvatarInUseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualAvatarInUseRequest proto.InternalMessageInfo

func (m *GetUserVirtualAvatarInUseRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserVirtualAvatarInUseRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserVirtualAvatarInUseRequest) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

type GetUserVirtualAvatarInUseResponse struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserVaInfo           *UserVirtualAvatarInfo `protobuf:"bytes,2,opt,name=user_va_info,json=userVaInfo,proto3" json:"user_va_info,omitempty"`
	UseScopeList         []uint32               `protobuf:"varint,10,rep,packed,name=use_scope_list,json=useScopeList,proto3" json:"use_scope_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserVirtualAvatarInUseResponse) Reset()         { *m = GetUserVirtualAvatarInUseResponse{} }
func (m *GetUserVirtualAvatarInUseResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserVirtualAvatarInUseResponse) ProtoMessage()    {}
func (*GetUserVirtualAvatarInUseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{10}
}
func (m *GetUserVirtualAvatarInUseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserVirtualAvatarInUseResponse.Unmarshal(m, b)
}
func (m *GetUserVirtualAvatarInUseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserVirtualAvatarInUseResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserVirtualAvatarInUseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserVirtualAvatarInUseResponse.Merge(dst, src)
}
func (m *GetUserVirtualAvatarInUseResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserVirtualAvatarInUseResponse.Size(m)
}
func (m *GetUserVirtualAvatarInUseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserVirtualAvatarInUseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserVirtualAvatarInUseResponse proto.InternalMessageInfo

func (m *GetUserVirtualAvatarInUseResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserVirtualAvatarInUseResponse) GetUserVaInfo() *UserVirtualAvatarInfo {
	if m != nil {
		return m.UserVaInfo
	}
	return nil
}

func (m *GetUserVirtualAvatarInUseResponse) GetUseScopeList() []uint32 {
	if m != nil {
		return m.UseScopeList
	}
	return nil
}

// 用户设置虚拟形象使用范围
type SetVirtualAvatarUseScopeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	UseScope             uint32       `protobuf:"varint,2,opt,name=use_scope,json=useScope,proto3" json:"use_scope,omitempty"`
	OpType               uint32       `protobuf:"varint,3,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetVirtualAvatarUseScopeRequest) Reset()         { *m = SetVirtualAvatarUseScopeRequest{} }
func (m *SetVirtualAvatarUseScopeRequest) String() string { return proto.CompactTextString(m) }
func (*SetVirtualAvatarUseScopeRequest) ProtoMessage()    {}
func (*SetVirtualAvatarUseScopeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{11}
}
func (m *SetVirtualAvatarUseScopeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualAvatarUseScopeRequest.Unmarshal(m, b)
}
func (m *SetVirtualAvatarUseScopeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualAvatarUseScopeRequest.Marshal(b, m, deterministic)
}
func (dst *SetVirtualAvatarUseScopeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualAvatarUseScopeRequest.Merge(dst, src)
}
func (m *SetVirtualAvatarUseScopeRequest) XXX_Size() int {
	return xxx_messageInfo_SetVirtualAvatarUseScopeRequest.Size(m)
}
func (m *SetVirtualAvatarUseScopeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualAvatarUseScopeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualAvatarUseScopeRequest proto.InternalMessageInfo

func (m *SetVirtualAvatarUseScopeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetVirtualAvatarUseScopeRequest) GetUseScope() uint32 {
	if m != nil {
		return m.UseScope
	}
	return 0
}

func (m *SetVirtualAvatarUseScopeRequest) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

type SetVirtualAvatarUseScopeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetVirtualAvatarUseScopeResponse) Reset()         { *m = SetVirtualAvatarUseScopeResponse{} }
func (m *SetVirtualAvatarUseScopeResponse) String() string { return proto.CompactTextString(m) }
func (*SetVirtualAvatarUseScopeResponse) ProtoMessage()    {}
func (*SetVirtualAvatarUseScopeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{12}
}
func (m *SetVirtualAvatarUseScopeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVirtualAvatarUseScopeResponse.Unmarshal(m, b)
}
func (m *SetVirtualAvatarUseScopeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVirtualAvatarUseScopeResponse.Marshal(b, m, deterministic)
}
func (dst *SetVirtualAvatarUseScopeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVirtualAvatarUseScopeResponse.Merge(dst, src)
}
func (m *SetVirtualAvatarUseScopeResponse) XXX_Size() int {
	return xxx_messageInfo_SetVirtualAvatarUseScopeResponse.Size(m)
}
func (m *SetVirtualAvatarUseScopeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVirtualAvatarUseScopeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetVirtualAvatarUseScopeResponse proto.InternalMessageInfo

func (m *SetVirtualAvatarUseScopeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取用户虚拟形象使用范围
type GetVirtualAvatarUseScopeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVirtualAvatarUseScopeRequest) Reset()         { *m = GetVirtualAvatarUseScopeRequest{} }
func (m *GetVirtualAvatarUseScopeRequest) String() string { return proto.CompactTextString(m) }
func (*GetVirtualAvatarUseScopeRequest) ProtoMessage()    {}
func (*GetVirtualAvatarUseScopeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{13}
}
func (m *GetVirtualAvatarUseScopeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualAvatarUseScopeRequest.Unmarshal(m, b)
}
func (m *GetVirtualAvatarUseScopeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualAvatarUseScopeRequest.Marshal(b, m, deterministic)
}
func (dst *GetVirtualAvatarUseScopeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualAvatarUseScopeRequest.Merge(dst, src)
}
func (m *GetVirtualAvatarUseScopeRequest) XXX_Size() int {
	return xxx_messageInfo_GetVirtualAvatarUseScopeRequest.Size(m)
}
func (m *GetVirtualAvatarUseScopeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualAvatarUseScopeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualAvatarUseScopeRequest proto.InternalMessageInfo

func (m *GetVirtualAvatarUseScopeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetVirtualAvatarUseScopeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UseScope             []uint32      `protobuf:"varint,2,rep,packed,name=use_scope,json=useScope,proto3" json:"use_scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetVirtualAvatarUseScopeResponse) Reset()         { *m = GetVirtualAvatarUseScopeResponse{} }
func (m *GetVirtualAvatarUseScopeResponse) String() string { return proto.CompactTextString(m) }
func (*GetVirtualAvatarUseScopeResponse) ProtoMessage()    {}
func (*GetVirtualAvatarUseScopeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{14}
}
func (m *GetVirtualAvatarUseScopeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVirtualAvatarUseScopeResponse.Unmarshal(m, b)
}
func (m *GetVirtualAvatarUseScopeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVirtualAvatarUseScopeResponse.Marshal(b, m, deterministic)
}
func (dst *GetVirtualAvatarUseScopeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVirtualAvatarUseScopeResponse.Merge(dst, src)
}
func (m *GetVirtualAvatarUseScopeResponse) XXX_Size() int {
	return xxx_messageInfo_GetVirtualAvatarUseScopeResponse.Size(m)
}
func (m *GetVirtualAvatarUseScopeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVirtualAvatarUseScopeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVirtualAvatarUseScopeResponse proto.InternalMessageInfo

func (m *GetVirtualAvatarUseScopeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVirtualAvatarUseScopeResponse) GetUseScope() []uint32 {
	if m != nil {
		return m.UseScope
	}
	return nil
}

// 用户获取自己当前麦位形象状态
type GetUserCurrMicAvatarTypeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserCurrMicAvatarTypeRequest) Reset()         { *m = GetUserCurrMicAvatarTypeRequest{} }
func (m *GetUserCurrMicAvatarTypeRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrMicAvatarTypeRequest) ProtoMessage()    {}
func (*GetUserCurrMicAvatarTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{15}
}
func (m *GetUserCurrMicAvatarTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrMicAvatarTypeRequest.Unmarshal(m, b)
}
func (m *GetUserCurrMicAvatarTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrMicAvatarTypeRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrMicAvatarTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrMicAvatarTypeRequest.Merge(dst, src)
}
func (m *GetUserCurrMicAvatarTypeRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrMicAvatarTypeRequest.Size(m)
}
func (m *GetUserCurrMicAvatarTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrMicAvatarTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrMicAvatarTypeRequest proto.InternalMessageInfo

func (m *GetUserCurrMicAvatarTypeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserCurrMicAvatarTypeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MicAvatarType        uint32        `protobuf:"varint,2,opt,name=mic_avatar_type,json=micAvatarType,proto3" json:"mic_avatar_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserCurrMicAvatarTypeResponse) Reset()         { *m = GetUserCurrMicAvatarTypeResponse{} }
func (m *GetUserCurrMicAvatarTypeResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserCurrMicAvatarTypeResponse) ProtoMessage()    {}
func (*GetUserCurrMicAvatarTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{16}
}
func (m *GetUserCurrMicAvatarTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCurrMicAvatarTypeResponse.Unmarshal(m, b)
}
func (m *GetUserCurrMicAvatarTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCurrMicAvatarTypeResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserCurrMicAvatarTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCurrMicAvatarTypeResponse.Merge(dst, src)
}
func (m *GetUserCurrMicAvatarTypeResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserCurrMicAvatarTypeResponse.Size(m)
}
func (m *GetUserCurrMicAvatarTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCurrMicAvatarTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCurrMicAvatarTypeResponse proto.InternalMessageInfo

func (m *GetUserCurrMicAvatarTypeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserCurrMicAvatarTypeResponse) GetMicAvatarType() uint32 {
	if m != nil {
		return m.MicAvatarType
	}
	return 0
}

type SetUserCurrMicAvatarTypeRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicAvatarType        uint32       `protobuf:"varint,3,opt,name=mic_avatar_type,json=micAvatarType,proto3" json:"mic_avatar_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetUserCurrMicAvatarTypeRequest) Reset()         { *m = SetUserCurrMicAvatarTypeRequest{} }
func (m *SetUserCurrMicAvatarTypeRequest) String() string { return proto.CompactTextString(m) }
func (*SetUserCurrMicAvatarTypeRequest) ProtoMessage()    {}
func (*SetUserCurrMicAvatarTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{17}
}
func (m *SetUserCurrMicAvatarTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserCurrMicAvatarTypeRequest.Unmarshal(m, b)
}
func (m *SetUserCurrMicAvatarTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserCurrMicAvatarTypeRequest.Marshal(b, m, deterministic)
}
func (dst *SetUserCurrMicAvatarTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserCurrMicAvatarTypeRequest.Merge(dst, src)
}
func (m *SetUserCurrMicAvatarTypeRequest) XXX_Size() int {
	return xxx_messageInfo_SetUserCurrMicAvatarTypeRequest.Size(m)
}
func (m *SetUserCurrMicAvatarTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserCurrMicAvatarTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserCurrMicAvatarTypeRequest proto.InternalMessageInfo

func (m *SetUserCurrMicAvatarTypeRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserCurrMicAvatarTypeRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetUserCurrMicAvatarTypeRequest) GetMicAvatarType() uint32 {
	if m != nil {
		return m.MicAvatarType
	}
	return 0
}

type SetUserCurrMicAvatarTypeResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserCurrMicAvatarTypeResponse) Reset()         { *m = SetUserCurrMicAvatarTypeResponse{} }
func (m *SetUserCurrMicAvatarTypeResponse) String() string { return proto.CompactTextString(m) }
func (*SetUserCurrMicAvatarTypeResponse) ProtoMessage()    {}
func (*SetUserCurrMicAvatarTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{18}
}
func (m *SetUserCurrMicAvatarTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserCurrMicAvatarTypeResponse.Unmarshal(m, b)
}
func (m *SetUserCurrMicAvatarTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserCurrMicAvatarTypeResponse.Marshal(b, m, deterministic)
}
func (dst *SetUserCurrMicAvatarTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserCurrMicAvatarTypeResponse.Merge(dst, src)
}
func (m *SetUserCurrMicAvatarTypeResponse) XXX_Size() int {
	return xxx_messageInfo_SetUserCurrMicAvatarTypeResponse.Size(m)
}
func (m *SetUserCurrMicAvatarTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserCurrMicAvatarTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserCurrMicAvatarTypeResponse proto.InternalMessageInfo

func (m *SetUserCurrMicAvatarTypeResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type MicVirtualAvatarInfoChanegeOpt struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AvatarType           uint32          `protobuf:"varint,2,opt,name=avatar_type,json=avatarType,proto3" json:"avatar_type,omitempty"`
	MicSpaceRes          *VAResourceConf `protobuf:"bytes,3,opt,name=mic_space_res,json=micSpaceRes,proto3" json:"mic_space_res,omitempty"`
	VaId                 uint32          `protobuf:"varint,4,opt,name=va_id,json=vaId,proto3" json:"va_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MicVirtualAvatarInfoChanegeOpt) Reset()         { *m = MicVirtualAvatarInfoChanegeOpt{} }
func (m *MicVirtualAvatarInfoChanegeOpt) String() string { return proto.CompactTextString(m) }
func (*MicVirtualAvatarInfoChanegeOpt) ProtoMessage()    {}
func (*MicVirtualAvatarInfoChanegeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{19}
}
func (m *MicVirtualAvatarInfoChanegeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicVirtualAvatarInfoChanegeOpt.Unmarshal(m, b)
}
func (m *MicVirtualAvatarInfoChanegeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicVirtualAvatarInfoChanegeOpt.Marshal(b, m, deterministic)
}
func (dst *MicVirtualAvatarInfoChanegeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicVirtualAvatarInfoChanegeOpt.Merge(dst, src)
}
func (m *MicVirtualAvatarInfoChanegeOpt) XXX_Size() int {
	return xxx_messageInfo_MicVirtualAvatarInfoChanegeOpt.Size(m)
}
func (m *MicVirtualAvatarInfoChanegeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_MicVirtualAvatarInfoChanegeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_MicVirtualAvatarInfoChanegeOpt proto.InternalMessageInfo

func (m *MicVirtualAvatarInfoChanegeOpt) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicVirtualAvatarInfoChanegeOpt) GetAvatarType() uint32 {
	if m != nil {
		return m.AvatarType
	}
	return 0
}

func (m *MicVirtualAvatarInfoChanegeOpt) GetMicSpaceRes() *VAResourceConf {
	if m != nil {
		return m.MicSpaceRes
	}
	return nil
}

func (m *MicVirtualAvatarInfoChanegeOpt) GetVaId() uint32 {
	if m != nil {
		return m.VaId
	}
	return 0
}

type MicVirtualAvatarInfoOpt struct {
	AvatarType           uint32          `protobuf:"varint,1,opt,name=avatar_type,json=avatarType,proto3" json:"avatar_type,omitempty"`
	MicSpaceRes          *VAResourceConf `protobuf:"bytes,2,opt,name=mic_space_res,json=micSpaceRes,proto3" json:"mic_space_res,omitempty"`
	VaId                 uint32          `protobuf:"varint,3,opt,name=va_id,json=vaId,proto3" json:"va_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MicVirtualAvatarInfoOpt) Reset()         { *m = MicVirtualAvatarInfoOpt{} }
func (m *MicVirtualAvatarInfoOpt) String() string { return proto.CompactTextString(m) }
func (*MicVirtualAvatarInfoOpt) ProtoMessage()    {}
func (*MicVirtualAvatarInfoOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{20}
}
func (m *MicVirtualAvatarInfoOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicVirtualAvatarInfoOpt.Unmarshal(m, b)
}
func (m *MicVirtualAvatarInfoOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicVirtualAvatarInfoOpt.Marshal(b, m, deterministic)
}
func (dst *MicVirtualAvatarInfoOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicVirtualAvatarInfoOpt.Merge(dst, src)
}
func (m *MicVirtualAvatarInfoOpt) XXX_Size() int {
	return xxx_messageInfo_MicVirtualAvatarInfoOpt.Size(m)
}
func (m *MicVirtualAvatarInfoOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_MicVirtualAvatarInfoOpt.DiscardUnknown(m)
}

var xxx_messageInfo_MicVirtualAvatarInfoOpt proto.InternalMessageInfo

func (m *MicVirtualAvatarInfoOpt) GetAvatarType() uint32 {
	if m != nil {
		return m.AvatarType
	}
	return 0
}

func (m *MicVirtualAvatarInfoOpt) GetMicSpaceRes() *VAResourceConf {
	if m != nil {
		return m.MicSpaceRes
	}
	return nil
}

func (m *MicVirtualAvatarInfoOpt) GetVaId() uint32 {
	if m != nil {
		return m.VaId
	}
	return 0
}

type VirtualAvatarGainNewNotify struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	VaInfo               *UserVirtualAvatarInfo `protobuf:"bytes,2,opt,name=va_info,json=vaInfo,proto3" json:"va_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *VirtualAvatarGainNewNotify) Reset()         { *m = VirtualAvatarGainNewNotify{} }
func (m *VirtualAvatarGainNewNotify) String() string { return proto.CompactTextString(m) }
func (*VirtualAvatarGainNewNotify) ProtoMessage()    {}
func (*VirtualAvatarGainNewNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_avatar_logic_22f6067dadba1eed, []int{21}
}
func (m *VirtualAvatarGainNewNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VirtualAvatarGainNewNotify.Unmarshal(m, b)
}
func (m *VirtualAvatarGainNewNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VirtualAvatarGainNewNotify.Marshal(b, m, deterministic)
}
func (dst *VirtualAvatarGainNewNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VirtualAvatarGainNewNotify.Merge(dst, src)
}
func (m *VirtualAvatarGainNewNotify) XXX_Size() int {
	return xxx_messageInfo_VirtualAvatarGainNewNotify.Size(m)
}
func (m *VirtualAvatarGainNewNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_VirtualAvatarGainNewNotify.DiscardUnknown(m)
}

var xxx_messageInfo_VirtualAvatarGainNewNotify proto.InternalMessageInfo

func (m *VirtualAvatarGainNewNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VirtualAvatarGainNewNotify) GetVaInfo() *UserVirtualAvatarInfo {
	if m != nil {
		return m.VaInfo
	}
	return nil
}

func init() {
	proto.RegisterType((*GetVirtualAvatarEntryRequest)(nil), "ga.virtual_avatar_logic.GetVirtualAvatarEntryRequest")
	proto.RegisterType((*GetVirtualAvatarEntryResponse)(nil), "ga.virtual_avatar_logic.GetVirtualAvatarEntryResponse")
	proto.RegisterType((*VAResourceConf)(nil), "ga.virtual_avatar_logic.VAResourceConf")
	proto.RegisterType((*VAResourceSet)(nil), "ga.virtual_avatar_logic.VAResourceSet")
	proto.RegisterType((*UserVirtualAvatarInfo)(nil), "ga.virtual_avatar_logic.UserVirtualAvatarInfo")
	proto.RegisterType((*GetUserVirtualAvatarListRequest)(nil), "ga.virtual_avatar_logic.GetUserVirtualAvatarListRequest")
	proto.RegisterType((*GetUserVirtualAvatarListResponse)(nil), "ga.virtual_avatar_logic.GetUserVirtualAvatarListResponse")
	proto.RegisterType((*SetUserVirtualAvatarInUseRequest)(nil), "ga.virtual_avatar_logic.SetUserVirtualAvatarInUseRequest")
	proto.RegisterType((*SetUserVirtualAvatarInUseResponse)(nil), "ga.virtual_avatar_logic.SetUserVirtualAvatarInUseResponse")
	proto.RegisterType((*GetUserVirtualAvatarInUseRequest)(nil), "ga.virtual_avatar_logic.GetUserVirtualAvatarInUseRequest")
	proto.RegisterType((*GetUserVirtualAvatarInUseResponse)(nil), "ga.virtual_avatar_logic.GetUserVirtualAvatarInUseResponse")
	proto.RegisterType((*SetVirtualAvatarUseScopeRequest)(nil), "ga.virtual_avatar_logic.SetVirtualAvatarUseScopeRequest")
	proto.RegisterType((*SetVirtualAvatarUseScopeResponse)(nil), "ga.virtual_avatar_logic.SetVirtualAvatarUseScopeResponse")
	proto.RegisterType((*GetVirtualAvatarUseScopeRequest)(nil), "ga.virtual_avatar_logic.GetVirtualAvatarUseScopeRequest")
	proto.RegisterType((*GetVirtualAvatarUseScopeResponse)(nil), "ga.virtual_avatar_logic.GetVirtualAvatarUseScopeResponse")
	proto.RegisterType((*GetUserCurrMicAvatarTypeRequest)(nil), "ga.virtual_avatar_logic.GetUserCurrMicAvatarTypeRequest")
	proto.RegisterType((*GetUserCurrMicAvatarTypeResponse)(nil), "ga.virtual_avatar_logic.GetUserCurrMicAvatarTypeResponse")
	proto.RegisterType((*SetUserCurrMicAvatarTypeRequest)(nil), "ga.virtual_avatar_logic.SetUserCurrMicAvatarTypeRequest")
	proto.RegisterType((*SetUserCurrMicAvatarTypeResponse)(nil), "ga.virtual_avatar_logic.SetUserCurrMicAvatarTypeResponse")
	proto.RegisterType((*MicVirtualAvatarInfoChanegeOpt)(nil), "ga.virtual_avatar_logic.MicVirtualAvatarInfoChanegeOpt")
	proto.RegisterType((*MicVirtualAvatarInfoOpt)(nil), "ga.virtual_avatar_logic.MicVirtualAvatarInfoOpt")
	proto.RegisterType((*VirtualAvatarGainNewNotify)(nil), "ga.virtual_avatar_logic.VirtualAvatarGainNewNotify")
	proto.RegisterEnum("ga.virtual_avatar_logic.VADisplayType", VADisplayType_name, VADisplayType_value)
	proto.RegisterEnum("ga.virtual_avatar_logic.VAResourceType", VAResourceType_name, VAResourceType_value)
	proto.RegisterEnum("ga.virtual_avatar_logic.VAUseScopeType", VAUseScopeType_name, VAUseScopeType_value)
	proto.RegisterEnum("ga.virtual_avatar_logic.ResCharacterType", ResCharacterType_name, ResCharacterType_value)
	proto.RegisterEnum("ga.virtual_avatar_logic.UserMicAvatarType", UserMicAvatarType_name, UserMicAvatarType_value)
	proto.RegisterEnum("ga.virtual_avatar_logic.SetVirtualAvatarUseScopeRequest_OpType", SetVirtualAvatarUseScopeRequest_OpType_name, SetVirtualAvatarUseScopeRequest_OpType_value)
}

func init() {
	proto.RegisterFile("virtual_avatar_logic/virtual_avatar_logic.proto", fileDescriptor_virtual_avatar_logic_22f6067dadba1eed)
}

var fileDescriptor_virtual_avatar_logic_22f6067dadba1eed = []byte{
	// 1338 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0xcd, 0x72, 0xdb, 0x46,
	0x12, 0x5e, 0x90, 0x12, 0x45, 0x36, 0x25, 0x19, 0x3b, 0x2e, 0x5b, 0xb4, 0x6c, 0x59, 0x14, 0xd6,
	0xd6, 0xca, 0x3a, 0xd0, 0xb5, 0x72, 0xf9, 0xbc, 0x81, 0x21, 0x48, 0x66, 0x85, 0x3f, 0xa8, 0x01,
	0xc9, 0x2a, 0xe7, 0x32, 0x35, 0x02, 0x87, 0x14, 0x12, 0x12, 0x80, 0x31, 0x43, 0x3a, 0xac, 0xca,
	0x13, 0xe4, 0x92, 0x3c, 0x40, 0x2a, 0xf7, 0x3c, 0x40, 0x5e, 0x20, 0x87, 0xe4, 0x90, 0x63, 0x5e,
	0x28, 0x35, 0x03, 0x8a, 0xa2, 0x44, 0xd0, 0x0e, 0x13, 0xdd, 0x46, 0xdd, 0x9f, 0xba, 0xbf, 0xfe,
	0xa6, 0xbb, 0x31, 0x84, 0x97, 0x63, 0x3f, 0x16, 0x23, 0x3a, 0x20, 0x74, 0x4c, 0x05, 0x8d, 0xc9,
	0x20, 0xec, 0xfb, 0x5e, 0xaa, 0xb1, 0x12, 0xc5, 0xa1, 0x08, 0xd1, 0x4e, 0x9f, 0x56, 0xd2, 0xdc,
	0xbb, 0x5b, 0x7d, 0x4a, 0x2e, 0x28, 0x67, 0x09, 0xce, 0x38, 0x83, 0x27, 0xe7, 0x4c, 0x74, 0x12,
	0xa4, 0xa9, 0x80, 0x76, 0x20, 0xe2, 0x09, 0x66, 0xef, 0x47, 0x8c, 0x0b, 0x74, 0x08, 0x79, 0x89,
	0x26, 0x31, 0x7b, 0x5f, 0xd2, 0xca, 0xda, 0x51, 0xf1, 0xa4, 0x58, 0xe9, 0xd3, 0xca, 0x1b, 0xca,
	0x19, 0x66, 0xef, 0xf1, 0xc6, 0x45, 0x72, 0x30, 0xbe, 0x82, 0xbd, 0x25, 0x71, 0x78, 0x14, 0x06,
	0x9c, 0xa1, 0x17, 0x50, 0x98, 0x06, 0xe2, 0xd1, 0x34, 0xd2, 0xe6, 0x75, 0x24, 0x1e, 0xe1, 0xfc,
	0xc5, 0xf4, 0x84, 0xf6, 0xa1, 0x78, 0x49, 0xc7, 0x8c, 0x50, 0xcf, 0x63, 0x9c, 0x97, 0x32, 0x65,
	0xed, 0x28, 0x8f, 0x41, 0x9a, 0x4c, 0x65, 0x31, 0x22, 0xd8, 0xee, 0x98, 0x98, 0xf1, 0x70, 0x14,
	0x7b, 0xcc, 0x0a, 0x83, 0x1e, 0xda, 0x03, 0x60, 0xbd, 0x1e, 0xf3, 0x84, 0x8c, 0xaf, 0xc2, 0x17,
	0x70, 0x21, 0xb1, 0x60, 0xc6, 0xd1, 0x33, 0xd8, 0xbe, 0x76, 0x93, 0x61, 0xf7, 0xb5, 0x0a, 0x5a,
	0xc0, 0x9b, 0x33, 0x48, 0xbd, 0xfb, 0x1a, 0x3d, 0x82, 0xbc, 0x74, 0x8b, 0x49, 0xc4, 0x4a, 0xd9,
	0xb2, 0x76, 0xb4, 0x85, 0x37, 0x62, 0xc6, 0x5b, 0x93, 0x88, 0x19, 0xbf, 0x68, 0xb0, 0x75, 0x9d,
	0xd2, 0x65, 0x02, 0x95, 0x61, 0x73, 0xc4, 0x59, 0x4c, 0x7c, 0xc1, 0x86, 0xc4, 0xef, 0xaa, 0x9c,
	0x5b, 0x18, 0xa4, 0xad, 0x2a, 0xd8, 0xb0, 0xda, 0x45, 0x07, 0xb0, 0xd9, 0xf5, 0x79, 0x34, 0xa0,
	0x93, 0x24, 0x64, 0x46, 0x21, 0x8a, 0x53, 0x9b, 0x0c, 0x8b, 0x3e, 0x03, 0x99, 0x81, 0x78, 0xbd,
	0x7e, 0x29, 0x5b, 0xce, 0x1e, 0x15, 0x4f, 0xfe, 0x5b, 0x59, 0x72, 0x6f, 0x95, 0x9b, 0x05, 0xe3,
	0x5c, 0xcc, 0xb8, 0xd5, 0xeb, 0xa3, 0x07, 0x90, 0xf3, 0x03, 0x32, 0xe2, 0xac, 0xb4, 0xa6, 0x64,
	0x5a, 0xf7, 0x83, 0x36, 0x67, 0xe8, 0x3e, 0xac, 0x8f, 0xa9, 0xa4, 0xb5, 0xae, 0x92, 0xae, 0x8d,
	0x69, 0xb5, 0x6b, 0xfc, 0x91, 0x81, 0x07, 0x6d, 0xce, 0xe2, 0x1b, 0xb7, 0x54, 0x0d, 0x7a, 0x21,
	0xfa, 0x0f, 0xac, 0x49, 0xe2, 0xd3, 0x7b, 0xb9, 0x27, 0x49, 0x48, 0xa0, 0x13, 0x87, 0x3d, 0x7f,
	0xc0, 0xb0, 0x72, 0x22, 0x04, 0x6b, 0x01, 0x1d, 0xb2, 0xa9, 0x74, 0xea, 0x2c, 0x25, 0x53, 0xb7,
	0x1a, 0xf9, 0x9e, 0x92, 0xac, 0x90, 0x74, 0x84, 0xe3, 0x7b, 0xe8, 0xff, 0x49, 0x6d, 0x9c, 0x09,
	0x45, 0xad, 0x78, 0x72, 0xf8, 0x17, 0x6a, 0x73, 0x99, 0x50, 0xa5, 0x49, 0x85, 0x8f, 0x60, 0xc3,
	0x8b, 0x88, 0xe2, 0xb5, 0x9e, 0xce, 0x2b, 0xe7, 0x45, 0xf2, 0x4f, 0xd9, 0x30, 0x31, 0x1b, 0x50,
	0xc1, 0x88, 0x22, 0x98, 0x53, 0x44, 0x20, 0x31, 0x35, 0x24, 0xcd, 0xc7, 0x50, 0x50, 0x97, 0xe5,
	0x5d, 0xd2, 0xb8, 0xb4, 0xa1, 0x24, 0xc9, 0x4b, 0x83, 0x75, 0x49, 0x63, 0x79, 0x93, 0xd3, 0x3c,
	0x89, 0x3f, 0x9f, 0xdc, 0x64, 0x12, 0x5b, 0x21, 0x1e, 0x43, 0x81, 0x7d, 0x1d, 0xf9, 0x31, 0x23,
	0x82, 0x97, 0x0a, 0x65, 0xed, 0x28, 0x8b, 0xf3, 0x89, 0xa1, 0xc5, 0x8d, 0x2a, 0xec, 0x9f, 0x33,
	0xb1, 0xa0, 0x6b, 0xcd, 0xe7, 0x62, 0xd5, 0x21, 0xfa, 0x5d, 0x83, 0xf2, 0xf2, 0x58, 0xab, 0x0f,
	0xd2, 0x39, 0x6c, 0x8c, 0x29, 0x19, 0xf8, 0x5c, 0x94, 0x32, 0xaa, 0xbd, 0x2a, 0x4b, 0xaf, 0x20,
	0xb5, 0x2f, 0x70, 0x6e, 0x4c, 0x65, 0x6e, 0xf4, 0x0a, 0x1e, 0xd2, 0x91, 0x08, 0x89, 0x6a, 0xe6,
	0xee, 0x28, 0xa6, 0xc2, 0x0f, 0x03, 0xc2, 0x99, 0x37, 0x9d, 0x93, 0xfb, 0xd2, 0xeb, 0x0c, 0xe8,
	0xe4, 0x74, 0xea, 0x73, 0x99, 0x67, 0x7c, 0xa7, 0x41, 0xd9, 0x4d, 0xa9, 0xa6, 0x2a, 0x3b, 0x74,
	0x45, 0x69, 0x16, 0xc6, 0x2d, 0xf3, 0xc9, 0x71, 0xcb, 0x2e, 0x8c, 0x9b, 0xd1, 0x80, 0x83, 0x8f,
	0x10, 0x5a, 0x59, 0x5f, 0xe3, 0xdb, 0x25, 0xf7, 0xf5, 0xb7, 0x2a, 0xdc, 0x03, 0x10, 0x34, 0xee,
	0x33, 0x41, 0x46, 0xb3, 0xfa, 0x0a, 0x89, 0xa5, 0xed, 0x77, 0x65, 0x8f, 0x27, 0x23, 0x32, 0x5f,
	0x1d, 0x24, 0x26, 0x55, 0xdc, 0xaf, 0x1a, 0x1c, 0x9c, 0xdf, 0x61, 0x75, 0xc8, 0x99, 0x4a, 0x2e,
	0x17, 0x49, 0xd0, 0x0b, 0x15, 0xa5, 0xd5, 0x5b, 0x48, 0x5d, 0x51, 0x87, 0xaa, 0x35, 0xf3, 0x0c,
	0xb6, 0x47, 0x9c, 0x11, 0xee, 0x85, 0x11, 0x4b, 0xda, 0x12, 0xca, 0xd9, 0xa3, 0x2d, 0x2c, 0xf3,
	0xb8, 0xd2, 0x28, 0x9b, 0xcd, 0xf8, 0x4d, 0x83, 0x7d, 0xf7, 0xd6, 0xb7, 0xa4, 0x3d, 0x05, 0xac,
	0x2a, 0x6a, 0x32, 0xf8, 0x49, 0xc6, 0xa9, 0xa6, 0xf9, 0xab, 0x64, 0x68, 0x07, 0x36, 0xc2, 0x68,
	0x5e, 0xce, 0x5c, 0x18, 0x29, 0x29, 0xcf, 0x20, 0xd7, 0x54, 0x27, 0xb4, 0x03, 0xf7, 0x9b, 0x0e,
	0x69, 0xbd, 0x73, 0x6c, 0xd2, 0x6e, 0xb8, 0x8e, 0x6d, 0x55, 0xcf, 0xaa, 0xf6, 0xa9, 0xfe, 0x2f,
	0x74, 0x0f, 0x8a, 0x57, 0x0e, 0xd7, 0x6e, 0xe9, 0x1a, 0x42, 0xb0, 0x7d, 0x65, 0xc0, 0x76, 0xbd,
	0xd9, 0xb1, 0xf5, 0x8c, 0x51, 0x57, 0x03, 0xb0, 0xa4, 0x90, 0xd5, 0xdb, 0x2d, 0xd9, 0x34, 0x77,
	0xa1, 0x8b, 0xf1, 0xa5, 0x6a, 0xdc, 0xbb, 0x62, 0x76, 0x5b, 0xe6, 0xec, 0xbc, 0xcc, 0x73, 0x0b,
	0xd2, 0x1a, 0xc5, 0x71, 0xdd, 0xf7, 0x92, 0x7c, 0x52, 0xdf, 0x55, 0x69, 0x8f, 0x66, 0xf3, 0x96,
	0x12, 0x6a, 0x75, 0xda, 0x87, 0x70, 0x6f, 0xe8, 0x7b, 0x57, 0x5d, 0x3c, 0xf7, 0x91, 0xde, 0x1a,
	0xce, 0x87, 0x36, 0xbe, 0x4f, 0x3a, 0xf2, 0x2e, 0x4a, 0x90, 0x63, 0xee, 0x5d, 0xd2, 0x20, 0x60,
	0x83, 0xeb, 0x35, 0x56, 0x98, 0x5a, 0xaa, 0xdd, 0x34, 0x4a, 0xd9, 0x34, 0x4a, 0xf5, 0xd9, 0x6e,
	0xbd, 0x0b, 0x25, 0x8c, 0x9f, 0x35, 0x78, 0x5a, 0xf7, 0xbd, 0x85, 0xf1, 0xb5, 0x2e, 0x69, 0xc0,
	0xfa, 0xac, 0x19, 0x09, 0xa4, 0x43, 0x76, 0x34, 0x7b, 0xe7, 0xc8, 0xa3, 0x5c, 0x49, 0x8b, 0xd2,
	0x01, 0x9d, 0x11, 0x41, 0x9f, 0x83, 0x64, 0x4d, 0x78, 0x44, 0x3d, 0xc5, 0x42, 0x95, 0xb2, 0xc2,
	0x23, 0xa7, 0x38, 0xf4, 0x3d, 0x57, 0xfe, 0xb3, 0x7c, 0xc3, 0xcd, 0x9e, 0x34, 0x6b, 0x73, 0x4f,
	0x9a, 0x1f, 0x34, 0xd8, 0x49, 0xe3, 0x2d, 0x09, 0xdf, 0xa2, 0xa7, 0x7d, 0x9a, 0x5e, 0xe6, 0x2e,
	0xe8, 0x65, 0xe7, 0xe8, 0x7d, 0x80, 0xdd, 0x1b, 0xd4, 0xce, 0xa9, 0x1f, 0x34, 0xd8, 0x87, 0x46,
	0x28, 0xfc, 0xde, 0x24, 0x45, 0xd1, 0xe4, 0x83, 0xfd, 0x0f, 0xb6, 0x6d, 0x6e, 0xac, 0x36, 0xed,
	0x71, 0x57, 0x3e, 0x57, 0x4f, 0xe7, 0x5e, 0x9a, 0xfb, 0xf0, 0xb8, 0x63, 0x92, 0xd3, 0xaa, 0xeb,
	0xd4, 0xcc, 0x77, 0x69, 0x0b, 0x6d, 0x17, 0x1e, 0xde, 0x06, 0x58, 0xcd, 0x7a, 0xbd, 0xd9, 0xd0,
	0x35, 0xf4, 0x10, 0xd0, 0x82, 0xcf, 0xd1, 0x33, 0xc7, 0x3f, 0x69, 0xf3, 0x0f, 0x71, 0x95, 0xa7,
	0x0c, 0x4f, 0x3a, 0x26, 0xc1, 0xb6, 0xdb, 0x6c, 0x63, 0xcb, 0x4e, 0x4b, 0xf4, 0x14, 0x76, 0x17,
	0x10, 0xf5, 0xaa, 0x45, 0x5c, 0xc7, 0xb4, 0x6c, 0x5d, 0x43, 0xc7, 0x70, 0xb8, 0xe0, 0x77, 0x6c,
	0xec, 0x36, 0x1b, 0x66, 0x8d, 0x58, 0x26, 0x3e, 0x25, 0x0e, 0x6e, 0x9e, 0x55, 0x6b, 0xb6, 0x9e,
	0x41, 0x2f, 0xe0, 0xf9, 0x02, 0xd6, 0x6e, 0xb4, 0x6c, 0x4c, 0xac, 0xb7, 0x66, 0xa3, 0x61, 0xd7,
	0x88, 0x7d, 0x76, 0x66, 0x5b, 0x2d, 0x3d, 0x7b, 0xfc, 0xa3, 0xe2, 0x7a, 0xb5, 0xe4, 0x14, 0xd7,
	0x03, 0xd8, 0xeb, 0x98, 0xa4, 0xed, 0xda, 0xc4, 0xb5, 0x9a, 0x4e, 0x2a, 0xd9, 0x44, 0xb6, 0x5b,
	0x90, 0x79, 0xb6, 0xa9, 0x31, 0x12, 0xba, 0xc4, 0x31, 0xcf, 0x25, 0xc9, 0xe7, 0x70, 0xb0, 0x08,
	0xb1, 0xde, 0x5e, 0x95, 0xa1, 0x6a, 0xd2, 0xb3, 0xc7, 0x02, 0x74, 0xcc, 0xb8, 0x7c, 0x6f, 0x52,
	0x4f, 0xb0, 0xa4, 0x43, 0x0d, 0x78, 0x8a, 0x6d, 0x57, 0x16, 0x83, 0x4d, 0x4b, 0x96, 0x95, 0x42,
	0x71, 0x0f, 0x1e, 0xa5, 0x61, 0x5c, 0x1b, 0x13, 0x53, 0xd7, 0x3e, 0xe6, 0x7e, 0xa3, 0x67, 0x8e,
	0xbf, 0x81, 0x7f, 0xcb, 0x4e, 0xba, 0xb1, 0x40, 0xd0, 0x33, 0x28, 0x2b, 0x80, 0x2c, 0xd4, 0xec,
	0x98, 0x2d, 0x13, 0x2f, 0xd1, 0x26, 0x15, 0xd5, 0x68, 0xe2, 0xba, 0x59, 0xd3, 0x35, 0xd9, 0x0b,
	0xa9, 0x80, 0x4e, 0x15, 0xb7, 0xda, 0x66, 0x4d, 0xcf, 0xbc, 0xa9, 0x43, 0xc9, 0x0b, 0x87, 0x95,
	0x89, 0x3f, 0x09, 0x47, 0xb2, 0xd3, 0x87, 0x61, 0x97, 0x0d, 0x92, 0x5f, 0xa6, 0x5f, 0xfc, 0xaf,
	0x1f, 0x0e, 0x68, 0xd0, 0xaf, 0xbc, 0x3e, 0x11, 0xa2, 0xe2, 0x85, 0xc3, 0x97, 0xca, 0xec, 0x85,
	0x83, 0x97, 0x34, 0x8a, 0x52, 0x7f, 0xfa, 0x5e, 0xe4, 0x14, 0xe4, 0xd5, 0x9f, 0x01, 0x00, 0x00,
	0xff, 0xff, 0xcc, 0x75, 0xcb, 0xbc, 0x2e, 0x0f, 0x00, 0x00,
}
