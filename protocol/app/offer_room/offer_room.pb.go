// Code generated by protoc-gen-go. DO NOT EDIT.
// source: offer_room/offer_room.proto

package offer_room // import "golang.52tt.com/protocol/app/offer_room"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 游戏阶段
type OfferRoomGamePhase int32

const (
	OfferRoomGamePhase_OFFER_ROOM_GAME_PHASE_UNSPECIFIED    OfferRoomGamePhase = 0
	OfferRoomGamePhase_OFFER_ROOM_GAME_PHASE_IN_PREPARATION OfferRoomGamePhase = 1
	OfferRoomGamePhase_OFFER_ROOM_GAME_PHASE_OFFERING       OfferRoomGamePhase = 2
	OfferRoomGamePhase_OFFER_ROOM_GAME_PHASE_SETTLE         OfferRoomGamePhase = 3
)

var OfferRoomGamePhase_name = map[int32]string{
	0: "OFFER_ROOM_GAME_PHASE_UNSPECIFIED",
	1: "OFFER_ROOM_GAME_PHASE_IN_PREPARATION",
	2: "OFFER_ROOM_GAME_PHASE_OFFERING",
	3: "OFFER_ROOM_GAME_PHASE_SETTLE",
}
var OfferRoomGamePhase_value = map[string]int32{
	"OFFER_ROOM_GAME_PHASE_UNSPECIFIED":    0,
	"OFFER_ROOM_GAME_PHASE_IN_PREPARATION": 1,
	"OFFER_ROOM_GAME_PHASE_OFFERING":       2,
	"OFFER_ROOM_GAME_PHASE_SETTLE":         3,
}

func (x OfferRoomGamePhase) String() string {
	return proto.EnumName(OfferRoomGamePhase_name, int32(x))
}
func (OfferRoomGamePhase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{0}
}

type OfferRoomNamePriceOperationType int32

const (
	OfferRoomNamePriceOperationType_OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_UNSPECIFIED      OfferRoomNamePriceOperationType = 0
	OfferRoomNamePriceOperationType_OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_NAME_PRICE_ONCE  OfferRoomNamePriceOperationType = 1
	OfferRoomNamePriceOperationType_OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_NAME_PRICE_MAX   OfferRoomNamePriceOperationType = 2
	OfferRoomNamePriceOperationType_OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_NAME_PRICE_MERGE OfferRoomNamePriceOperationType = 3
)

var OfferRoomNamePriceOperationType_name = map[int32]string{
	0: "OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_UNSPECIFIED",
	1: "OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_NAME_PRICE_ONCE",
	2: "OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_NAME_PRICE_MAX",
	3: "OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_NAME_PRICE_MERGE",
}
var OfferRoomNamePriceOperationType_value = map[string]int32{
	"OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_UNSPECIFIED":      0,
	"OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_NAME_PRICE_ONCE":  1,
	"OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_NAME_PRICE_MAX":   2,
	"OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_NAME_PRICE_MERGE": 3,
}

func (x OfferRoomNamePriceOperationType) String() string {
	return proto.EnumName(OfferRoomNamePriceOperationType_name, int32(x))
}
func (OfferRoomNamePriceOperationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{1}
}

type OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType int32

const (
	OfferRoomUiResource_OfferRoomDownloadSourceInfo_OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_UNSPECIFIED OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType = 0
	OfferRoomUiResource_OfferRoomDownloadSourceInfo_OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_MP4         OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType = 1
	OfferRoomUiResource_OfferRoomDownloadSourceInfo_OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_VAP         OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType = 2
	OfferRoomUiResource_OfferRoomDownloadSourceInfo_OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_PNG         OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType = 3
	OfferRoomUiResource_OfferRoomDownloadSourceInfo_OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_LOTTIE      OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType = 4
)

var OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType_name = map[int32]string{
	0: "OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_UNSPECIFIED",
	1: "OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_MP4",
	2: "OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_VAP",
	3: "OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_PNG",
	4: "OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_LOTTIE",
}
var OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType_value = map[string]int32{
	"OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_UNSPECIFIED": 0,
	"OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_MP4":         1,
	"OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_VAP":         2,
	"OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_PNG":         3,
	"OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_LOTTIE":      4,
}

func (x OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType) String() string {
	return proto.EnumName(OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType_name, int32(x))
}
func (OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{10, 0, 0}
}

// 嘉宾报名列表以及推送
type OfferRoomApplyList struct {
	List                 []*app.UserProfile `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Version              int64              `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *OfferRoomApplyList) Reset()         { *m = OfferRoomApplyList{} }
func (m *OfferRoomApplyList) String() string { return proto.CompactTextString(m) }
func (*OfferRoomApplyList) ProtoMessage()    {}
func (*OfferRoomApplyList) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{0}
}
func (m *OfferRoomApplyList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomApplyList.Unmarshal(m, b)
}
func (m *OfferRoomApplyList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomApplyList.Marshal(b, m, deterministic)
}
func (dst *OfferRoomApplyList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomApplyList.Merge(dst, src)
}
func (m *OfferRoomApplyList) XXX_Size() int {
	return xxx_messageInfo_OfferRoomApplyList.Size(m)
}
func (m *OfferRoomApplyList) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomApplyList.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomApplyList proto.InternalMessageInfo

func (m *OfferRoomApplyList) GetList() []*app.UserProfile {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *OfferRoomApplyList) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type OfferRoomGetApplyListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomGetApplyListRequest) Reset()         { *m = OfferRoomGetApplyListRequest{} }
func (m *OfferRoomGetApplyListRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGetApplyListRequest) ProtoMessage()    {}
func (*OfferRoomGetApplyListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{1}
}
func (m *OfferRoomGetApplyListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGetApplyListRequest.Unmarshal(m, b)
}
func (m *OfferRoomGetApplyListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGetApplyListRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGetApplyListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGetApplyListRequest.Merge(dst, src)
}
func (m *OfferRoomGetApplyListRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGetApplyListRequest.Size(m)
}
func (m *OfferRoomGetApplyListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGetApplyListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGetApplyListRequest proto.InternalMessageInfo

func (m *OfferRoomGetApplyListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomGetApplyListRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type OfferRoomGetApplyListResponse struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ApplyList            *OfferRoomApplyList `protobuf:"bytes,2,opt,name=apply_list,json=applyList,proto3" json:"apply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *OfferRoomGetApplyListResponse) Reset()         { *m = OfferRoomGetApplyListResponse{} }
func (m *OfferRoomGetApplyListResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGetApplyListResponse) ProtoMessage()    {}
func (*OfferRoomGetApplyListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{2}
}
func (m *OfferRoomGetApplyListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGetApplyListResponse.Unmarshal(m, b)
}
func (m *OfferRoomGetApplyListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGetApplyListResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGetApplyListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGetApplyListResponse.Merge(dst, src)
}
func (m *OfferRoomGetApplyListResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGetApplyListResponse.Size(m)
}
func (m *OfferRoomGetApplyListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGetApplyListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGetApplyListResponse proto.InternalMessageInfo

func (m *OfferRoomGetApplyListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfferRoomGetApplyListResponse) GetApplyList() *OfferRoomApplyList {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

type OfferRoomUserApplyRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomUserApplyRequest) Reset()         { *m = OfferRoomUserApplyRequest{} }
func (m *OfferRoomUserApplyRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomUserApplyRequest) ProtoMessage()    {}
func (*OfferRoomUserApplyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{3}
}
func (m *OfferRoomUserApplyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomUserApplyRequest.Unmarshal(m, b)
}
func (m *OfferRoomUserApplyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomUserApplyRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomUserApplyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomUserApplyRequest.Merge(dst, src)
}
func (m *OfferRoomUserApplyRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomUserApplyRequest.Size(m)
}
func (m *OfferRoomUserApplyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomUserApplyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomUserApplyRequest proto.InternalMessageInfo

func (m *OfferRoomUserApplyRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomUserApplyRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type OfferRoomUserApplyResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomUserApplyResponse) Reset()         { *m = OfferRoomUserApplyResponse{} }
func (m *OfferRoomUserApplyResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomUserApplyResponse) ProtoMessage()    {}
func (*OfferRoomUserApplyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{4}
}
func (m *OfferRoomUserApplyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomUserApplyResponse.Unmarshal(m, b)
}
func (m *OfferRoomUserApplyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomUserApplyResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomUserApplyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomUserApplyResponse.Merge(dst, src)
}
func (m *OfferRoomUserApplyResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomUserApplyResponse.Size(m)
}
func (m *OfferRoomUserApplyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomUserApplyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomUserApplyResponse proto.InternalMessageInfo

func (m *OfferRoomUserApplyResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type OfferRoomUserCancelApplyRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomUserCancelApplyRequest) Reset()         { *m = OfferRoomUserCancelApplyRequest{} }
func (m *OfferRoomUserCancelApplyRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomUserCancelApplyRequest) ProtoMessage()    {}
func (*OfferRoomUserCancelApplyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{5}
}
func (m *OfferRoomUserCancelApplyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomUserCancelApplyRequest.Unmarshal(m, b)
}
func (m *OfferRoomUserCancelApplyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomUserCancelApplyRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomUserCancelApplyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomUserCancelApplyRequest.Merge(dst, src)
}
func (m *OfferRoomUserCancelApplyRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomUserCancelApplyRequest.Size(m)
}
func (m *OfferRoomUserCancelApplyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomUserCancelApplyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomUserCancelApplyRequest proto.InternalMessageInfo

func (m *OfferRoomUserCancelApplyRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomUserCancelApplyRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type OfferRoomUserCancelApplyResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomUserCancelApplyResponse) Reset()         { *m = OfferRoomUserCancelApplyResponse{} }
func (m *OfferRoomUserCancelApplyResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomUserCancelApplyResponse) ProtoMessage()    {}
func (*OfferRoomUserCancelApplyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{6}
}
func (m *OfferRoomUserCancelApplyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomUserCancelApplyResponse.Unmarshal(m, b)
}
func (m *OfferRoomUserCancelApplyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomUserCancelApplyResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomUserCancelApplyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomUserCancelApplyResponse.Merge(dst, src)
}
func (m *OfferRoomUserCancelApplyResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomUserCancelApplyResponse.Size(m)
}
func (m *OfferRoomUserCancelApplyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomUserCancelApplyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomUserCancelApplyResponse proto.InternalMessageInfo

func (m *OfferRoomUserCancelApplyResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 榜单列表
type OfferRoomTopList struct {
	List                 []*OfferRoomTopList_TopListItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *OfferRoomTopList) Reset()         { *m = OfferRoomTopList{} }
func (m *OfferRoomTopList) String() string { return proto.CompactTextString(m) }
func (*OfferRoomTopList) ProtoMessage()    {}
func (*OfferRoomTopList) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{7}
}
func (m *OfferRoomTopList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomTopList.Unmarshal(m, b)
}
func (m *OfferRoomTopList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomTopList.Marshal(b, m, deterministic)
}
func (dst *OfferRoomTopList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomTopList.Merge(dst, src)
}
func (m *OfferRoomTopList) XXX_Size() int {
	return xxx_messageInfo_OfferRoomTopList.Size(m)
}
func (m *OfferRoomTopList) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomTopList.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomTopList proto.InternalMessageInfo

func (m *OfferRoomTopList) GetList() []*OfferRoomTopList_TopListItem {
	if m != nil {
		return m.List
	}
	return nil
}

type OfferRoomTopList_TopListItem struct {
	User                 *app.UserProfile `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Price                uint32           `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	UniId                string           `protobuf:"bytes,3,opt,name=uni_id,json=uniId,proto3" json:"uni_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *OfferRoomTopList_TopListItem) Reset()         { *m = OfferRoomTopList_TopListItem{} }
func (m *OfferRoomTopList_TopListItem) String() string { return proto.CompactTextString(m) }
func (*OfferRoomTopList_TopListItem) ProtoMessage()    {}
func (*OfferRoomTopList_TopListItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{7, 0}
}
func (m *OfferRoomTopList_TopListItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomTopList_TopListItem.Unmarshal(m, b)
}
func (m *OfferRoomTopList_TopListItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomTopList_TopListItem.Marshal(b, m, deterministic)
}
func (dst *OfferRoomTopList_TopListItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomTopList_TopListItem.Merge(dst, src)
}
func (m *OfferRoomTopList_TopListItem) XXX_Size() int {
	return xxx_messageInfo_OfferRoomTopList_TopListItem.Size(m)
}
func (m *OfferRoomTopList_TopListItem) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomTopList_TopListItem.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomTopList_TopListItem proto.InternalMessageInfo

func (m *OfferRoomTopList_TopListItem) GetUser() *app.UserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *OfferRoomTopList_TopListItem) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *OfferRoomTopList_TopListItem) GetUniId() string {
	if m != nil {
		return m.UniId
	}
	return ""
}

// 游戏设置
type OfferRoomGameSetting struct {
	RelationshipName     string             `protobuf:"bytes,1,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	Gift                 *OfferRoomGiftInfo `protobuf:"bytes,2,opt,name=gift,proto3" json:"gift,omitempty"`
	MaxOfferingPrice     uint32             `protobuf:"varint,3,opt,name=max_offering_price,json=maxOfferingPrice,proto3" json:"max_offering_price,omitempty"`
	ReturnGift           *OfferRoomGiftInfo `protobuf:"bytes,4,opt,name=return_gift,json=returnGift,proto3" json:"return_gift,omitempty"`
	RelationshipDayMap   map[uint32]uint32  `protobuf:"bytes,5,rep,name=relationship_day_map,json=relationshipDayMap,proto3" json:"relationship_day_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ReturnGiftNum        uint32             `protobuf:"varint,6,opt,name=return_gift_num,json=returnGiftNum,proto3" json:"return_gift_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *OfferRoomGameSetting) Reset()         { *m = OfferRoomGameSetting{} }
func (m *OfferRoomGameSetting) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGameSetting) ProtoMessage()    {}
func (*OfferRoomGameSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{8}
}
func (m *OfferRoomGameSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGameSetting.Unmarshal(m, b)
}
func (m *OfferRoomGameSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGameSetting.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGameSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGameSetting.Merge(dst, src)
}
func (m *OfferRoomGameSetting) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGameSetting.Size(m)
}
func (m *OfferRoomGameSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGameSetting.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGameSetting proto.InternalMessageInfo

func (m *OfferRoomGameSetting) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *OfferRoomGameSetting) GetGift() *OfferRoomGiftInfo {
	if m != nil {
		return m.Gift
	}
	return nil
}

func (m *OfferRoomGameSetting) GetMaxOfferingPrice() uint32 {
	if m != nil {
		return m.MaxOfferingPrice
	}
	return 0
}

func (m *OfferRoomGameSetting) GetReturnGift() *OfferRoomGiftInfo {
	if m != nil {
		return m.ReturnGift
	}
	return nil
}

func (m *OfferRoomGameSetting) GetRelationshipDayMap() map[uint32]uint32 {
	if m != nil {
		return m.RelationshipDayMap
	}
	return nil
}

func (m *OfferRoomGameSetting) GetReturnGiftNum() uint32 {
	if m != nil {
		return m.ReturnGiftNum
	}
	return 0
}

type OfferRoomSettleRelationship struct {
	OfferUser            *app.UserProfile `protobuf:"bytes,1,opt,name=offer_user,json=offerUser,proto3" json:"offer_user,omitempty"`
	SaleUser             *app.UserProfile `protobuf:"bytes,2,opt,name=sale_user,json=saleUser,proto3" json:"sale_user,omitempty"`
	Duration             uint32           `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	GiftNum              uint32           `protobuf:"varint,4,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *OfferRoomSettleRelationship) Reset()         { *m = OfferRoomSettleRelationship{} }
func (m *OfferRoomSettleRelationship) String() string { return proto.CompactTextString(m) }
func (*OfferRoomSettleRelationship) ProtoMessage()    {}
func (*OfferRoomSettleRelationship) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{9}
}
func (m *OfferRoomSettleRelationship) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomSettleRelationship.Unmarshal(m, b)
}
func (m *OfferRoomSettleRelationship) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomSettleRelationship.Marshal(b, m, deterministic)
}
func (dst *OfferRoomSettleRelationship) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomSettleRelationship.Merge(dst, src)
}
func (m *OfferRoomSettleRelationship) XXX_Size() int {
	return xxx_messageInfo_OfferRoomSettleRelationship.Size(m)
}
func (m *OfferRoomSettleRelationship) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomSettleRelationship.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomSettleRelationship proto.InternalMessageInfo

func (m *OfferRoomSettleRelationship) GetOfferUser() *app.UserProfile {
	if m != nil {
		return m.OfferUser
	}
	return nil
}

func (m *OfferRoomSettleRelationship) GetSaleUser() *app.UserProfile {
	if m != nil {
		return m.SaleUser
	}
	return nil
}

func (m *OfferRoomSettleRelationship) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *OfferRoomSettleRelationship) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

// ui资源（部分）
type OfferRoomUiResource struct {
	// DOWNLOAD_SOURCE_TYPE
	BgSource              *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,1,opt,name=bg_source,json=bgSource,proto3" json:"bg_source,omitempty"`
	BgSourcePng           string                                           `protobuf:"bytes,2,opt,name=bg_source_png,json=bgSourcePng,proto3" json:"bg_source_png,omitempty"`
	Seat1Source           *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,3,opt,name=seat1_source,json=seat1Source,proto3" json:"seat1_source,omitempty"`
	Seat2Source           *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,4,opt,name=seat2_source,json=seat2Source,proto3" json:"seat2_source,omitempty"`
	Seat3Source           *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,5,opt,name=seat3_source,json=seat3Source,proto3" json:"seat3_source,omitempty"`
	SeatWingSource        *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,6,opt,name=seat_wing_source,json=seatWingSource,proto3" json:"seat_wing_source,omitempty"`
	LiftLowSource         *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,7,opt,name=lift_low_source,json=liftLowSource,proto3" json:"lift_low_source,omitempty"`
	LiftHighSource        *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,8,opt,name=lift_high_source,json=liftHighSource,proto3" json:"lift_high_source,omitempty"`
	LockSuccessSource     *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,9,opt,name=lock_success_source,json=lockSuccessSource,proto3" json:"lock_success_source,omitempty"`
	RelationShowSource    *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,10,opt,name=relation_show_source,json=relationShowSource,proto3" json:"relation_show_source,omitempty"`
	GiftOpenSource        *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,11,opt,name=gift_open_source,json=giftOpenSource,proto3" json:"gift_open_source,omitempty"`
	LittleGiftSource      *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,12,opt,name=little_gift_source,json=littleGiftSource,proto3" json:"little_gift_source,omitempty"`
	RelationShowPicSource *OfferRoomUiResource_OfferRoomDownloadSourceInfo `protobuf:"bytes,13,opt,name=relation_show_pic_source,json=relationShowPicSource,proto3" json:"relation_show_pic_source,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                                         `json:"-"`
	XXX_unrecognized      []byte                                           `json:"-"`
	XXX_sizecache         int32                                            `json:"-"`
}

func (m *OfferRoomUiResource) Reset()         { *m = OfferRoomUiResource{} }
func (m *OfferRoomUiResource) String() string { return proto.CompactTextString(m) }
func (*OfferRoomUiResource) ProtoMessage()    {}
func (*OfferRoomUiResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{10}
}
func (m *OfferRoomUiResource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomUiResource.Unmarshal(m, b)
}
func (m *OfferRoomUiResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomUiResource.Marshal(b, m, deterministic)
}
func (dst *OfferRoomUiResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomUiResource.Merge(dst, src)
}
func (m *OfferRoomUiResource) XXX_Size() int {
	return xxx_messageInfo_OfferRoomUiResource.Size(m)
}
func (m *OfferRoomUiResource) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomUiResource.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomUiResource proto.InternalMessageInfo

func (m *OfferRoomUiResource) GetBgSource() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.BgSource
	}
	return nil
}

func (m *OfferRoomUiResource) GetBgSourcePng() string {
	if m != nil {
		return m.BgSourcePng
	}
	return ""
}

func (m *OfferRoomUiResource) GetSeat1Source() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.Seat1Source
	}
	return nil
}

func (m *OfferRoomUiResource) GetSeat2Source() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.Seat2Source
	}
	return nil
}

func (m *OfferRoomUiResource) GetSeat3Source() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.Seat3Source
	}
	return nil
}

func (m *OfferRoomUiResource) GetSeatWingSource() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.SeatWingSource
	}
	return nil
}

func (m *OfferRoomUiResource) GetLiftLowSource() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.LiftLowSource
	}
	return nil
}

func (m *OfferRoomUiResource) GetLiftHighSource() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.LiftHighSource
	}
	return nil
}

func (m *OfferRoomUiResource) GetLockSuccessSource() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.LockSuccessSource
	}
	return nil
}

func (m *OfferRoomUiResource) GetRelationShowSource() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.RelationShowSource
	}
	return nil
}

func (m *OfferRoomUiResource) GetGiftOpenSource() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.GiftOpenSource
	}
	return nil
}

func (m *OfferRoomUiResource) GetLittleGiftSource() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.LittleGiftSource
	}
	return nil
}

func (m *OfferRoomUiResource) GetRelationShowPicSource() *OfferRoomUiResource_OfferRoomDownloadSourceInfo {
	if m != nil {
		return m.RelationShowPicSource
	}
	return nil
}

// 资源结构
type OfferRoomUiResource_OfferRoomDownloadSourceInfo struct {
	SourceType           OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3,enum=ga.offer_room.OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType" json:"source_type,omitempty"`
	Url                  string                                                                      `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string                                                                      `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                                                    `json:"-"`
	XXX_unrecognized     []byte                                                                      `json:"-"`
	XXX_sizecache        int32                                                                       `json:"-"`
}

func (m *OfferRoomUiResource_OfferRoomDownloadSourceInfo) Reset() {
	*m = OfferRoomUiResource_OfferRoomDownloadSourceInfo{}
}
func (m *OfferRoomUiResource_OfferRoomDownloadSourceInfo) String() string {
	return proto.CompactTextString(m)
}
func (*OfferRoomUiResource_OfferRoomDownloadSourceInfo) ProtoMessage() {}
func (*OfferRoomUiResource_OfferRoomDownloadSourceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{10, 0}
}
func (m *OfferRoomUiResource_OfferRoomDownloadSourceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomUiResource_OfferRoomDownloadSourceInfo.Unmarshal(m, b)
}
func (m *OfferRoomUiResource_OfferRoomDownloadSourceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomUiResource_OfferRoomDownloadSourceInfo.Marshal(b, m, deterministic)
}
func (dst *OfferRoomUiResource_OfferRoomDownloadSourceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomUiResource_OfferRoomDownloadSourceInfo.Merge(dst, src)
}
func (m *OfferRoomUiResource_OfferRoomDownloadSourceInfo) XXX_Size() int {
	return xxx_messageInfo_OfferRoomUiResource_OfferRoomDownloadSourceInfo.Size(m)
}
func (m *OfferRoomUiResource_OfferRoomDownloadSourceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomUiResource_OfferRoomDownloadSourceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomUiResource_OfferRoomDownloadSourceInfo proto.InternalMessageInfo

func (m *OfferRoomUiResource_OfferRoomDownloadSourceInfo) GetSourceType() OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType {
	if m != nil {
		return m.SourceType
	}
	return OfferRoomUiResource_OfferRoomDownloadSourceInfo_OFFER_ROOM_DOWNLOAD_SOURCE_TYPE_UNSPECIFIED
}

func (m *OfferRoomUiResource_OfferRoomDownloadSourceInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *OfferRoomUiResource_OfferRoomDownloadSourceInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

// 出价/一键定拍推送
type OfferRoomNamePricePushMsg struct {
	User                 *app.UserProfile                `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Num                  uint32                          `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	OperationType        OfferRoomNamePriceOperationType `protobuf:"varint,3,opt,name=operation_type,json=operationType,proto3,enum=ga.offer_room.OfferRoomNamePriceOperationType" json:"operation_type,omitempty"`
	TopList              *OfferRoomTopList               `protobuf:"bytes,4,opt,name=top_list,json=topList,proto3" json:"top_list,omitempty"`
	IsLocked             bool                            `protobuf:"varint,5,opt,name=is_locked,json=isLocked,proto3" json:"is_locked,omitempty"`
	LockedUid            uint32                          `protobuf:"varint,6,opt,name=locked_uid,json=lockedUid,proto3" json:"locked_uid,omitempty"`
	Version              int64                           `protobuf:"varint,7,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *OfferRoomNamePricePushMsg) Reset()         { *m = OfferRoomNamePricePushMsg{} }
func (m *OfferRoomNamePricePushMsg) String() string { return proto.CompactTextString(m) }
func (*OfferRoomNamePricePushMsg) ProtoMessage()    {}
func (*OfferRoomNamePricePushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{11}
}
func (m *OfferRoomNamePricePushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomNamePricePushMsg.Unmarshal(m, b)
}
func (m *OfferRoomNamePricePushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomNamePricePushMsg.Marshal(b, m, deterministic)
}
func (dst *OfferRoomNamePricePushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomNamePricePushMsg.Merge(dst, src)
}
func (m *OfferRoomNamePricePushMsg) XXX_Size() int {
	return xxx_messageInfo_OfferRoomNamePricePushMsg.Size(m)
}
func (m *OfferRoomNamePricePushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomNamePricePushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomNamePricePushMsg proto.InternalMessageInfo

func (m *OfferRoomNamePricePushMsg) GetUser() *app.UserProfile {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *OfferRoomNamePricePushMsg) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *OfferRoomNamePricePushMsg) GetOperationType() OfferRoomNamePriceOperationType {
	if m != nil {
		return m.OperationType
	}
	return OfferRoomNamePriceOperationType_OFFER_ROOM_NAME_PRICE_OPERATION_TYPE_UNSPECIFIED
}

func (m *OfferRoomNamePricePushMsg) GetTopList() *OfferRoomTopList {
	if m != nil {
		return m.TopList
	}
	return nil
}

func (m *OfferRoomNamePricePushMsg) GetIsLocked() bool {
	if m != nil {
		return m.IsLocked
	}
	return false
}

func (m *OfferRoomNamePricePushMsg) GetLockedUid() uint32 {
	if m != nil {
		return m.LockedUid
	}
	return 0
}

func (m *OfferRoomNamePricePushMsg) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

// 当前的游戏信息，会用于推送
type OfferRoomCurOfferingGameInfo struct {
	ChannelId            uint32                       `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64                        `protobuf:"varint,2,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	Version              int64                        `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	TopList              *OfferRoomTopList            `protobuf:"bytes,4,opt,name=top_list,json=topList,proto3" json:"top_list,omitempty"`
	Phase                OfferRoomGamePhase           `protobuf:"varint,5,opt,name=phase,proto3,enum=ga.offer_room.OfferRoomGamePhase" json:"phase,omitempty"`
	IsLocked             bool                         `protobuf:"varint,6,opt,name=is_locked,json=isLocked,proto3" json:"is_locked,omitempty"`
	LockedUid            uint32                       `protobuf:"varint,7,opt,name=locked_uid,json=lockedUid,proto3" json:"locked_uid,omitempty"`
	GameSetting          *OfferRoomGameSetting        `protobuf:"bytes,8,opt,name=game_setting,json=gameSetting,proto3" json:"game_setting,omitempty"`
	Relationship         *OfferRoomSettleRelationship `protobuf:"bytes,9,opt,name=relationship,proto3" json:"relationship,omitempty"`
	HonoredGuest         *app.UserProfile             `protobuf:"bytes,10,opt,name=honored_guest,json=honoredGuest,proto3" json:"honored_guest,omitempty"`
	ErrMsg               string                       `protobuf:"bytes,11,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	LevelUpMap           map[uint32]uint32            `protobuf:"bytes,12,rep,name=level_up_map,json=levelUpMap,proto3" json:"level_up_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *OfferRoomCurOfferingGameInfo) Reset()         { *m = OfferRoomCurOfferingGameInfo{} }
func (m *OfferRoomCurOfferingGameInfo) String() string { return proto.CompactTextString(m) }
func (*OfferRoomCurOfferingGameInfo) ProtoMessage()    {}
func (*OfferRoomCurOfferingGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{12}
}
func (m *OfferRoomCurOfferingGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomCurOfferingGameInfo.Unmarshal(m, b)
}
func (m *OfferRoomCurOfferingGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomCurOfferingGameInfo.Marshal(b, m, deterministic)
}
func (dst *OfferRoomCurOfferingGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomCurOfferingGameInfo.Merge(dst, src)
}
func (m *OfferRoomCurOfferingGameInfo) XXX_Size() int {
	return xxx_messageInfo_OfferRoomCurOfferingGameInfo.Size(m)
}
func (m *OfferRoomCurOfferingGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomCurOfferingGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomCurOfferingGameInfo proto.InternalMessageInfo

func (m *OfferRoomCurOfferingGameInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfferRoomCurOfferingGameInfo) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

func (m *OfferRoomCurOfferingGameInfo) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *OfferRoomCurOfferingGameInfo) GetTopList() *OfferRoomTopList {
	if m != nil {
		return m.TopList
	}
	return nil
}

func (m *OfferRoomCurOfferingGameInfo) GetPhase() OfferRoomGamePhase {
	if m != nil {
		return m.Phase
	}
	return OfferRoomGamePhase_OFFER_ROOM_GAME_PHASE_UNSPECIFIED
}

func (m *OfferRoomCurOfferingGameInfo) GetIsLocked() bool {
	if m != nil {
		return m.IsLocked
	}
	return false
}

func (m *OfferRoomCurOfferingGameInfo) GetLockedUid() uint32 {
	if m != nil {
		return m.LockedUid
	}
	return 0
}

func (m *OfferRoomCurOfferingGameInfo) GetGameSetting() *OfferRoomGameSetting {
	if m != nil {
		return m.GameSetting
	}
	return nil
}

func (m *OfferRoomCurOfferingGameInfo) GetRelationship() *OfferRoomSettleRelationship {
	if m != nil {
		return m.Relationship
	}
	return nil
}

func (m *OfferRoomCurOfferingGameInfo) GetHonoredGuest() *app.UserProfile {
	if m != nil {
		return m.HonoredGuest
	}
	return nil
}

func (m *OfferRoomCurOfferingGameInfo) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *OfferRoomCurOfferingGameInfo) GetLevelUpMap() map[uint32]uint32 {
	if m != nil {
		return m.LevelUpMap
	}
	return nil
}

// 获取当前房间的信息
type OfferRoomGetCurOfferingGameInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomGetCurOfferingGameInfoRequest) Reset() {
	*m = OfferRoomGetCurOfferingGameInfoRequest{}
}
func (m *OfferRoomGetCurOfferingGameInfoRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGetCurOfferingGameInfoRequest) ProtoMessage()    {}
func (*OfferRoomGetCurOfferingGameInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{13}
}
func (m *OfferRoomGetCurOfferingGameInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGetCurOfferingGameInfoRequest.Unmarshal(m, b)
}
func (m *OfferRoomGetCurOfferingGameInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGetCurOfferingGameInfoRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGetCurOfferingGameInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGetCurOfferingGameInfoRequest.Merge(dst, src)
}
func (m *OfferRoomGetCurOfferingGameInfoRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGetCurOfferingGameInfoRequest.Size(m)
}
func (m *OfferRoomGetCurOfferingGameInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGetCurOfferingGameInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGetCurOfferingGameInfoRequest proto.InternalMessageInfo

func (m *OfferRoomGetCurOfferingGameInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomGetCurOfferingGameInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type OfferRoomGetCurOfferingGameInfoResponse struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CurGameInfo          *OfferRoomCurOfferingGameInfo `protobuf:"bytes,2,opt,name=cur_game_info,json=curGameInfo,proto3" json:"cur_game_info,omitempty"`
	MyOfferingPrice      uint32                        `protobuf:"varint,3,opt,name=my_offering_price,json=myOfferingPrice,proto3" json:"my_offering_price,omitempty"`
	UiResource           *OfferRoomUiResource          `protobuf:"bytes,4,opt,name=ui_resource,json=uiResource,proto3" json:"ui_resource,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *OfferRoomGetCurOfferingGameInfoResponse) Reset() {
	*m = OfferRoomGetCurOfferingGameInfoResponse{}
}
func (m *OfferRoomGetCurOfferingGameInfoResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGetCurOfferingGameInfoResponse) ProtoMessage()    {}
func (*OfferRoomGetCurOfferingGameInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{14}
}
func (m *OfferRoomGetCurOfferingGameInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGetCurOfferingGameInfoResponse.Unmarshal(m, b)
}
func (m *OfferRoomGetCurOfferingGameInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGetCurOfferingGameInfoResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGetCurOfferingGameInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGetCurOfferingGameInfoResponse.Merge(dst, src)
}
func (m *OfferRoomGetCurOfferingGameInfoResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGetCurOfferingGameInfoResponse.Size(m)
}
func (m *OfferRoomGetCurOfferingGameInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGetCurOfferingGameInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGetCurOfferingGameInfoResponse proto.InternalMessageInfo

func (m *OfferRoomGetCurOfferingGameInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfferRoomGetCurOfferingGameInfoResponse) GetCurGameInfo() *OfferRoomCurOfferingGameInfo {
	if m != nil {
		return m.CurGameInfo
	}
	return nil
}

func (m *OfferRoomGetCurOfferingGameInfoResponse) GetMyOfferingPrice() uint32 {
	if m != nil {
		return m.MyOfferingPrice
	}
	return 0
}

func (m *OfferRoomGetCurOfferingGameInfoResponse) GetUiResource() *OfferRoomUiResource {
	if m != nil {
		return m.UiResource
	}
	return nil
}

type OfferRoomGiftInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftPrice            int64    `protobuf:"varint,2,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftIcon             string   `protobuf:"bytes,4,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfferRoomGiftInfo) Reset()         { *m = OfferRoomGiftInfo{} }
func (m *OfferRoomGiftInfo) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGiftInfo) ProtoMessage()    {}
func (*OfferRoomGiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{15}
}
func (m *OfferRoomGiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGiftInfo.Unmarshal(m, b)
}
func (m *OfferRoomGiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGiftInfo.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGiftInfo.Merge(dst, src)
}
func (m *OfferRoomGiftInfo) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGiftInfo.Size(m)
}
func (m *OfferRoomGiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGiftInfo proto.InternalMessageInfo

func (m *OfferRoomGiftInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *OfferRoomGiftInfo) GetGiftPrice() int64 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *OfferRoomGiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *OfferRoomGiftInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

// 拍卖配置
type OfferRoomOfferingConfig struct {
	Id                   int64                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RelationshipNameList []string             `protobuf:"bytes,2,rep,name=relationship_name_list,json=relationshipNameList,proto3" json:"relationship_name_list,omitempty"`
	GiftInfoList         []*OfferRoomGiftInfo `protobuf:"bytes,3,rep,name=gift_info_list,json=giftInfoList,proto3" json:"gift_info_list,omitempty"`
	ReturnGiftInfo       *OfferRoomGiftInfo   `protobuf:"bytes,4,opt,name=return_gift_info,json=returnGiftInfo,proto3" json:"return_gift_info,omitempty"`
	MaxReturnGiftCnt     uint32               `protobuf:"varint,5,opt,name=max_return_gift_cnt,json=maxReturnGiftCnt,proto3" json:"max_return_gift_cnt,omitempty"`
	CntToDays            map[uint32]uint32    `protobuf:"bytes,6,rep,name=cnt_to_days,json=cntToDays,proto3" json:"cnt_to_days,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ReturnGiftInfoList   []*OfferRoomGiftInfo `protobuf:"bytes,7,rep,name=return_gift_info_list,json=returnGiftInfoList,proto3" json:"return_gift_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *OfferRoomOfferingConfig) Reset()         { *m = OfferRoomOfferingConfig{} }
func (m *OfferRoomOfferingConfig) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingConfig) ProtoMessage()    {}
func (*OfferRoomOfferingConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{16}
}
func (m *OfferRoomOfferingConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingConfig.Unmarshal(m, b)
}
func (m *OfferRoomOfferingConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingConfig.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingConfig.Merge(dst, src)
}
func (m *OfferRoomOfferingConfig) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingConfig.Size(m)
}
func (m *OfferRoomOfferingConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingConfig.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingConfig proto.InternalMessageInfo

func (m *OfferRoomOfferingConfig) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OfferRoomOfferingConfig) GetRelationshipNameList() []string {
	if m != nil {
		return m.RelationshipNameList
	}
	return nil
}

func (m *OfferRoomOfferingConfig) GetGiftInfoList() []*OfferRoomGiftInfo {
	if m != nil {
		return m.GiftInfoList
	}
	return nil
}

func (m *OfferRoomOfferingConfig) GetReturnGiftInfo() *OfferRoomGiftInfo {
	if m != nil {
		return m.ReturnGiftInfo
	}
	return nil
}

func (m *OfferRoomOfferingConfig) GetMaxReturnGiftCnt() uint32 {
	if m != nil {
		return m.MaxReturnGiftCnt
	}
	return 0
}

func (m *OfferRoomOfferingConfig) GetCntToDays() map[uint32]uint32 {
	if m != nil {
		return m.CntToDays
	}
	return nil
}

func (m *OfferRoomOfferingConfig) GetReturnGiftInfoList() []*OfferRoomGiftInfo {
	if m != nil {
		return m.ReturnGiftInfoList
	}
	return nil
}

// 获取拍卖配置
type OfferRoomGetOfferingConfigRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomGetOfferingConfigRequest) Reset()         { *m = OfferRoomGetOfferingConfigRequest{} }
func (m *OfferRoomGetOfferingConfigRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGetOfferingConfigRequest) ProtoMessage()    {}
func (*OfferRoomGetOfferingConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{17}
}
func (m *OfferRoomGetOfferingConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGetOfferingConfigRequest.Unmarshal(m, b)
}
func (m *OfferRoomGetOfferingConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGetOfferingConfigRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGetOfferingConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGetOfferingConfigRequest.Merge(dst, src)
}
func (m *OfferRoomGetOfferingConfigRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGetOfferingConfigRequest.Size(m)
}
func (m *OfferRoomGetOfferingConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGetOfferingConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGetOfferingConfigRequest proto.InternalMessageInfo

func (m *OfferRoomGetOfferingConfigRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type OfferRoomGetOfferingConfigResponse struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OfferingConfig       *OfferRoomOfferingConfig `protobuf:"bytes,2,opt,name=offering_config,json=offeringConfig,proto3" json:"offering_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *OfferRoomGetOfferingConfigResponse) Reset()         { *m = OfferRoomGetOfferingConfigResponse{} }
func (m *OfferRoomGetOfferingConfigResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGetOfferingConfigResponse) ProtoMessage()    {}
func (*OfferRoomGetOfferingConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{18}
}
func (m *OfferRoomGetOfferingConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGetOfferingConfigResponse.Unmarshal(m, b)
}
func (m *OfferRoomGetOfferingConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGetOfferingConfigResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGetOfferingConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGetOfferingConfigResponse.Merge(dst, src)
}
func (m *OfferRoomGetOfferingConfigResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGetOfferingConfigResponse.Size(m)
}
func (m *OfferRoomGetOfferingConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGetOfferingConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGetOfferingConfigResponse proto.InternalMessageInfo

func (m *OfferRoomGetOfferingConfigResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfferRoomGetOfferingConfigResponse) GetOfferingConfig() *OfferRoomOfferingConfig {
	if m != nil {
		return m.OfferingConfig
	}
	return nil
}

// 提交拍卖设置
type OfferRoomSubmitOfferingSettingRequest struct {
	BaseReq                  *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId                uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId              int64        `protobuf:"varint,3,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	ConfigId                 int64        `protobuf:"varint,4,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	GiftIndex                uint32       `protobuf:"varint,5,opt,name=gift_index,json=giftIndex,proto3" json:"gift_index,omitempty"`
	ReturnGiftNum            uint32       `protobuf:"varint,6,opt,name=return_gift_num,json=returnGiftNum,proto3" json:"return_gift_num,omitempty"`
	RelationshipNameIndex    uint32       `protobuf:"varint,7,opt,name=relationship_name_index,json=relationshipNameIndex,proto3" json:"relationship_name_index,omitempty"`
	CustomerRelationshipName string       `protobuf:"bytes,8,opt,name=customer_relationship_name,json=customerRelationshipName,proto3" json:"customer_relationship_name,omitempty"`
	ReturnGiftIndex          uint32       `protobuf:"varint,9,opt,name=return_gift_index,json=returnGiftIndex,proto3" json:"return_gift_index,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}     `json:"-"`
	XXX_unrecognized         []byte       `json:"-"`
	XXX_sizecache            int32        `json:"-"`
}

func (m *OfferRoomSubmitOfferingSettingRequest) Reset()         { *m = OfferRoomSubmitOfferingSettingRequest{} }
func (m *OfferRoomSubmitOfferingSettingRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomSubmitOfferingSettingRequest) ProtoMessage()    {}
func (*OfferRoomSubmitOfferingSettingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{19}
}
func (m *OfferRoomSubmitOfferingSettingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomSubmitOfferingSettingRequest.Unmarshal(m, b)
}
func (m *OfferRoomSubmitOfferingSettingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomSubmitOfferingSettingRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomSubmitOfferingSettingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomSubmitOfferingSettingRequest.Merge(dst, src)
}
func (m *OfferRoomSubmitOfferingSettingRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomSubmitOfferingSettingRequest.Size(m)
}
func (m *OfferRoomSubmitOfferingSettingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomSubmitOfferingSettingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomSubmitOfferingSettingRequest proto.InternalMessageInfo

func (m *OfferRoomSubmitOfferingSettingRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomSubmitOfferingSettingRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfferRoomSubmitOfferingSettingRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

func (m *OfferRoomSubmitOfferingSettingRequest) GetConfigId() int64 {
	if m != nil {
		return m.ConfigId
	}
	return 0
}

func (m *OfferRoomSubmitOfferingSettingRequest) GetGiftIndex() uint32 {
	if m != nil {
		return m.GiftIndex
	}
	return 0
}

func (m *OfferRoomSubmitOfferingSettingRequest) GetReturnGiftNum() uint32 {
	if m != nil {
		return m.ReturnGiftNum
	}
	return 0
}

func (m *OfferRoomSubmitOfferingSettingRequest) GetRelationshipNameIndex() uint32 {
	if m != nil {
		return m.RelationshipNameIndex
	}
	return 0
}

func (m *OfferRoomSubmitOfferingSettingRequest) GetCustomerRelationshipName() string {
	if m != nil {
		return m.CustomerRelationshipName
	}
	return ""
}

func (m *OfferRoomSubmitOfferingSettingRequest) GetReturnGiftIndex() uint32 {
	if m != nil {
		return m.ReturnGiftIndex
	}
	return 0
}

type OfferRoomSubmitOfferingSettingResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomSubmitOfferingSettingResponse) Reset() {
	*m = OfferRoomSubmitOfferingSettingResponse{}
}
func (m *OfferRoomSubmitOfferingSettingResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomSubmitOfferingSettingResponse) ProtoMessage()    {}
func (*OfferRoomSubmitOfferingSettingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{20}
}
func (m *OfferRoomSubmitOfferingSettingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomSubmitOfferingSettingResponse.Unmarshal(m, b)
}
func (m *OfferRoomSubmitOfferingSettingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomSubmitOfferingSettingResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomSubmitOfferingSettingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomSubmitOfferingSettingResponse.Merge(dst, src)
}
func (m *OfferRoomSubmitOfferingSettingResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomSubmitOfferingSettingResponse.Size(m)
}
func (m *OfferRoomSubmitOfferingSettingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomSubmitOfferingSettingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomSubmitOfferingSettingResponse proto.InternalMessageInfo

func (m *OfferRoomSubmitOfferingSettingResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type OfferRoomNamePriceOnceRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64        `protobuf:"varint,3,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomNamePriceOnceRequest) Reset()         { *m = OfferRoomNamePriceOnceRequest{} }
func (m *OfferRoomNamePriceOnceRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomNamePriceOnceRequest) ProtoMessage()    {}
func (*OfferRoomNamePriceOnceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{21}
}
func (m *OfferRoomNamePriceOnceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomNamePriceOnceRequest.Unmarshal(m, b)
}
func (m *OfferRoomNamePriceOnceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomNamePriceOnceRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomNamePriceOnceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomNamePriceOnceRequest.Merge(dst, src)
}
func (m *OfferRoomNamePriceOnceRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomNamePriceOnceRequest.Size(m)
}
func (m *OfferRoomNamePriceOnceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomNamePriceOnceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomNamePriceOnceRequest proto.InternalMessageInfo

func (m *OfferRoomNamePriceOnceRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomNamePriceOnceRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfferRoomNamePriceOnceRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

type OfferRoomNamePriceOnceResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MyOfferingPrice      uint32        `protobuf:"varint,2,opt,name=my_offering_price,json=myOfferingPrice,proto3" json:"my_offering_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomNamePriceOnceResponse) Reset()         { *m = OfferRoomNamePriceOnceResponse{} }
func (m *OfferRoomNamePriceOnceResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomNamePriceOnceResponse) ProtoMessage()    {}
func (*OfferRoomNamePriceOnceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{22}
}
func (m *OfferRoomNamePriceOnceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomNamePriceOnceResponse.Unmarshal(m, b)
}
func (m *OfferRoomNamePriceOnceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomNamePriceOnceResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomNamePriceOnceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomNamePriceOnceResponse.Merge(dst, src)
}
func (m *OfferRoomNamePriceOnceResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomNamePriceOnceResponse.Size(m)
}
func (m *OfferRoomNamePriceOnceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomNamePriceOnceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomNamePriceOnceResponse proto.InternalMessageInfo

func (m *OfferRoomNamePriceOnceResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfferRoomNamePriceOnceResponse) GetMyOfferingPrice() uint32 {
	if m != nil {
		return m.MyOfferingPrice
	}
	return 0
}

type OfferRoomNamePriceMaxRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64        `protobuf:"varint,3,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomNamePriceMaxRequest) Reset()         { *m = OfferRoomNamePriceMaxRequest{} }
func (m *OfferRoomNamePriceMaxRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomNamePriceMaxRequest) ProtoMessage()    {}
func (*OfferRoomNamePriceMaxRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{23}
}
func (m *OfferRoomNamePriceMaxRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomNamePriceMaxRequest.Unmarshal(m, b)
}
func (m *OfferRoomNamePriceMaxRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomNamePriceMaxRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomNamePriceMaxRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomNamePriceMaxRequest.Merge(dst, src)
}
func (m *OfferRoomNamePriceMaxRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomNamePriceMaxRequest.Size(m)
}
func (m *OfferRoomNamePriceMaxRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomNamePriceMaxRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomNamePriceMaxRequest proto.InternalMessageInfo

func (m *OfferRoomNamePriceMaxRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomNamePriceMaxRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfferRoomNamePriceMaxRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

type OfferRoomNamePriceMaxResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MyOfferingPrice      uint32        `protobuf:"varint,2,opt,name=my_offering_price,json=myOfferingPrice,proto3" json:"my_offering_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomNamePriceMaxResponse) Reset()         { *m = OfferRoomNamePriceMaxResponse{} }
func (m *OfferRoomNamePriceMaxResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomNamePriceMaxResponse) ProtoMessage()    {}
func (*OfferRoomNamePriceMaxResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{24}
}
func (m *OfferRoomNamePriceMaxResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomNamePriceMaxResponse.Unmarshal(m, b)
}
func (m *OfferRoomNamePriceMaxResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomNamePriceMaxResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomNamePriceMaxResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomNamePriceMaxResponse.Merge(dst, src)
}
func (m *OfferRoomNamePriceMaxResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomNamePriceMaxResponse.Size(m)
}
func (m *OfferRoomNamePriceMaxResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomNamePriceMaxResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomNamePriceMaxResponse proto.InternalMessageInfo

func (m *OfferRoomNamePriceMaxResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfferRoomNamePriceMaxResponse) GetMyOfferingPrice() uint32 {
	if m != nil {
		return m.MyOfferingPrice
	}
	return 0
}

type OfferRoomOfferingSetRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64        `protobuf:"varint,3,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	TopUniId             string       `protobuf:"bytes,4,opt,name=top_uni_id,json=topUniId,proto3" json:"top_uni_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomOfferingSetRequest) Reset()         { *m = OfferRoomOfferingSetRequest{} }
func (m *OfferRoomOfferingSetRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingSetRequest) ProtoMessage()    {}
func (*OfferRoomOfferingSetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{25}
}
func (m *OfferRoomOfferingSetRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingSetRequest.Unmarshal(m, b)
}
func (m *OfferRoomOfferingSetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingSetRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingSetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingSetRequest.Merge(dst, src)
}
func (m *OfferRoomOfferingSetRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingSetRequest.Size(m)
}
func (m *OfferRoomOfferingSetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingSetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingSetRequest proto.InternalMessageInfo

func (m *OfferRoomOfferingSetRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomOfferingSetRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfferRoomOfferingSetRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

func (m *OfferRoomOfferingSetRequest) GetTopUniId() string {
	if m != nil {
		return m.TopUniId
	}
	return ""
}

type OfferRoomOfferingSetResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomOfferingSetResponse) Reset()         { *m = OfferRoomOfferingSetResponse{} }
func (m *OfferRoomOfferingSetResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingSetResponse) ProtoMessage()    {}
func (*OfferRoomOfferingSetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{26}
}
func (m *OfferRoomOfferingSetResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingSetResponse.Unmarshal(m, b)
}
func (m *OfferRoomOfferingSetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingSetResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingSetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingSetResponse.Merge(dst, src)
}
func (m *OfferRoomOfferingSetResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingSetResponse.Size(m)
}
func (m *OfferRoomOfferingSetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingSetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingSetResponse proto.InternalMessageInfo

func (m *OfferRoomOfferingSetResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type OfferRoomOfferingPassRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64        `protobuf:"varint,3,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomOfferingPassRequest) Reset()         { *m = OfferRoomOfferingPassRequest{} }
func (m *OfferRoomOfferingPassRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingPassRequest) ProtoMessage()    {}
func (*OfferRoomOfferingPassRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{27}
}
func (m *OfferRoomOfferingPassRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingPassRequest.Unmarshal(m, b)
}
func (m *OfferRoomOfferingPassRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingPassRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingPassRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingPassRequest.Merge(dst, src)
}
func (m *OfferRoomOfferingPassRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingPassRequest.Size(m)
}
func (m *OfferRoomOfferingPassRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingPassRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingPassRequest proto.InternalMessageInfo

func (m *OfferRoomOfferingPassRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomOfferingPassRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfferRoomOfferingPassRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

type OfferRoomOfferingPassResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomOfferingPassResponse) Reset()         { *m = OfferRoomOfferingPassResponse{} }
func (m *OfferRoomOfferingPassResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingPassResponse) ProtoMessage()    {}
func (*OfferRoomOfferingPassResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{28}
}
func (m *OfferRoomOfferingPassResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingPassResponse.Unmarshal(m, b)
}
func (m *OfferRoomOfferingPassResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingPassResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingPassResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingPassResponse.Merge(dst, src)
}
func (m *OfferRoomOfferingPassResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingPassResponse.Size(m)
}
func (m *OfferRoomOfferingPassResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingPassResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingPassResponse proto.InternalMessageInfo

func (m *OfferRoomOfferingPassResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type OfferRoomOfferingEndRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64        `protobuf:"varint,3,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomOfferingEndRequest) Reset()         { *m = OfferRoomOfferingEndRequest{} }
func (m *OfferRoomOfferingEndRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingEndRequest) ProtoMessage()    {}
func (*OfferRoomOfferingEndRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{29}
}
func (m *OfferRoomOfferingEndRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingEndRequest.Unmarshal(m, b)
}
func (m *OfferRoomOfferingEndRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingEndRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingEndRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingEndRequest.Merge(dst, src)
}
func (m *OfferRoomOfferingEndRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingEndRequest.Size(m)
}
func (m *OfferRoomOfferingEndRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingEndRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingEndRequest proto.InternalMessageInfo

func (m *OfferRoomOfferingEndRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomOfferingEndRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfferRoomOfferingEndRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

type OfferRoomOfferingEndResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomOfferingEndResponse) Reset()         { *m = OfferRoomOfferingEndResponse{} }
func (m *OfferRoomOfferingEndResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingEndResponse) ProtoMessage()    {}
func (*OfferRoomOfferingEndResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{30}
}
func (m *OfferRoomOfferingEndResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingEndResponse.Unmarshal(m, b)
}
func (m *OfferRoomOfferingEndResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingEndResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingEndResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingEndResponse.Merge(dst, src)
}
func (m *OfferRoomOfferingEndResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingEndResponse.Size(m)
}
func (m *OfferRoomOfferingEndResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingEndResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingEndResponse proto.InternalMessageInfo

func (m *OfferRoomOfferingEndResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取用户关系列表
type OfferRoomOfferingRelationshipsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	PageToken            string       `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32       `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomOfferingRelationshipsRequest) Reset()         { *m = OfferRoomOfferingRelationshipsRequest{} }
func (m *OfferRoomOfferingRelationshipsRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingRelationshipsRequest) ProtoMessage()    {}
func (*OfferRoomOfferingRelationshipsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{31}
}
func (m *OfferRoomOfferingRelationshipsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingRelationshipsRequest.Unmarshal(m, b)
}
func (m *OfferRoomOfferingRelationshipsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingRelationshipsRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingRelationshipsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingRelationshipsRequest.Merge(dst, src)
}
func (m *OfferRoomOfferingRelationshipsRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingRelationshipsRequest.Size(m)
}
func (m *OfferRoomOfferingRelationshipsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingRelationshipsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingRelationshipsRequest proto.InternalMessageInfo

func (m *OfferRoomOfferingRelationshipsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomOfferingRelationshipsRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *OfferRoomOfferingRelationshipsRequest) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *OfferRoomOfferingRelationshipsRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 关系信息
type OfferRoomOfferingRelationshipInfo struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	PartnerUid           uint32   `protobuf:"varint,2,opt,name=partner_uid,json=partnerUid,proto3" json:"partner_uid,omitempty"`
	PartnerAccount       string   `protobuf:"bytes,3,opt,name=partner_account,json=partnerAccount,proto3" json:"partner_account,omitempty"`
	PartnerNickName      string   `protobuf:"bytes,4,opt,name=partner_nick_name,json=partnerNickName,proto3" json:"partner_nick_name,omitempty"`
	RelationshipName     string   `protobuf:"bytes,5,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	ExpireTs             int64    `protobuf:"varint,6,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	GiftId               uint32   `protobuf:"varint,7,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftCnt              uint32   `protobuf:"varint,8,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt,omitempty"`
	GiftIcon             string   `protobuf:"bytes,9,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	IsBuyer              bool     `protobuf:"varint,10,opt,name=is_buyer,json=isBuyer,proto3" json:"is_buyer,omitempty"`
	IsPriceMax           bool     `protobuf:"varint,11,opt,name=is_price_max,json=isPriceMax,proto3" json:"is_price_max,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfferRoomOfferingRelationshipInfo) Reset()         { *m = OfferRoomOfferingRelationshipInfo{} }
func (m *OfferRoomOfferingRelationshipInfo) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingRelationshipInfo) ProtoMessage()    {}
func (*OfferRoomOfferingRelationshipInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{32}
}
func (m *OfferRoomOfferingRelationshipInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingRelationshipInfo.Unmarshal(m, b)
}
func (m *OfferRoomOfferingRelationshipInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingRelationshipInfo.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingRelationshipInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingRelationshipInfo.Merge(dst, src)
}
func (m *OfferRoomOfferingRelationshipInfo) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingRelationshipInfo.Size(m)
}
func (m *OfferRoomOfferingRelationshipInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingRelationshipInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingRelationshipInfo proto.InternalMessageInfo

func (m *OfferRoomOfferingRelationshipInfo) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *OfferRoomOfferingRelationshipInfo) GetPartnerUid() uint32 {
	if m != nil {
		return m.PartnerUid
	}
	return 0
}

func (m *OfferRoomOfferingRelationshipInfo) GetPartnerAccount() string {
	if m != nil {
		return m.PartnerAccount
	}
	return ""
}

func (m *OfferRoomOfferingRelationshipInfo) GetPartnerNickName() string {
	if m != nil {
		return m.PartnerNickName
	}
	return ""
}

func (m *OfferRoomOfferingRelationshipInfo) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *OfferRoomOfferingRelationshipInfo) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *OfferRoomOfferingRelationshipInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *OfferRoomOfferingRelationshipInfo) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

func (m *OfferRoomOfferingRelationshipInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *OfferRoomOfferingRelationshipInfo) GetIsBuyer() bool {
	if m != nil {
		return m.IsBuyer
	}
	return false
}

func (m *OfferRoomOfferingRelationshipInfo) GetIsPriceMax() bool {
	if m != nil {
		return m.IsPriceMax
	}
	return false
}

type OfferRoomOfferingRelationshipsResponse struct {
	BaseResp             *app.BaseResp                        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RelationshipName     string                               `protobuf:"bytes,2,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	RelationshipList     []*OfferRoomOfferingRelationshipInfo `protobuf:"bytes,3,rep,name=relationship_list,json=relationshipList,proto3" json:"relationship_list,omitempty"`
	NextPageToken        string                               `protobuf:"bytes,4,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	RelationshipCnt      uint32                               `protobuf:"varint,5,opt,name=relationship_cnt,json=relationshipCnt,proto3" json:"relationship_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *OfferRoomOfferingRelationshipsResponse) Reset() {
	*m = OfferRoomOfferingRelationshipsResponse{}
}
func (m *OfferRoomOfferingRelationshipsResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingRelationshipsResponse) ProtoMessage()    {}
func (*OfferRoomOfferingRelationshipsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{33}
}
func (m *OfferRoomOfferingRelationshipsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingRelationshipsResponse.Unmarshal(m, b)
}
func (m *OfferRoomOfferingRelationshipsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingRelationshipsResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingRelationshipsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingRelationshipsResponse.Merge(dst, src)
}
func (m *OfferRoomOfferingRelationshipsResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingRelationshipsResponse.Size(m)
}
func (m *OfferRoomOfferingRelationshipsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingRelationshipsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingRelationshipsResponse proto.InternalMessageInfo

func (m *OfferRoomOfferingRelationshipsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfferRoomOfferingRelationshipsResponse) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *OfferRoomOfferingRelationshipsResponse) GetRelationshipList() []*OfferRoomOfferingRelationshipInfo {
	if m != nil {
		return m.RelationshipList
	}
	return nil
}

func (m *OfferRoomOfferingRelationshipsResponse) GetNextPageToken() string {
	if m != nil {
		return m.NextPageToken
	}
	return ""
}

func (m *OfferRoomOfferingRelationshipsResponse) GetRelationshipCnt() uint32 {
	if m != nil {
		return m.RelationshipCnt
	}
	return 0
}

// 个人资料卡 与 个人主页 共用
type OfferRoomCardInfoRequest struct {
	BaseReq   *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	// 个人主页需要前三条数据
	NeedTop3Relationships bool     `protobuf:"varint,3,opt,name=need_top3_relationships,json=needTop3Relationships,proto3" json:"need_top3_relationships,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *OfferRoomCardInfoRequest) Reset()         { *m = OfferRoomCardInfoRequest{} }
func (m *OfferRoomCardInfoRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomCardInfoRequest) ProtoMessage()    {}
func (*OfferRoomCardInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{34}
}
func (m *OfferRoomCardInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomCardInfoRequest.Unmarshal(m, b)
}
func (m *OfferRoomCardInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomCardInfoRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomCardInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomCardInfoRequest.Merge(dst, src)
}
func (m *OfferRoomCardInfoRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomCardInfoRequest.Size(m)
}
func (m *OfferRoomCardInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomCardInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomCardInfoRequest proto.InternalMessageInfo

func (m *OfferRoomCardInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomCardInfoRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *OfferRoomCardInfoRequest) GetNeedTop3Relationships() bool {
	if m != nil {
		return m.NeedTop3Relationships
	}
	return false
}

// 个人资料卡 与 个人主页 共用
type OfferRoomCardInfoResponse struct {
	BaseResp         *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TotalCnt         uint32        `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	RelationshipName string        `protobuf:"bytes,3,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	// =============== 个人资料卡 铭牌 =============
	Rank    uint32 `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	StarCnt uint32 `protobuf:"varint,5,opt,name=star_cnt,json=starCnt,proto3" json:"star_cnt,omitempty"`
	// 个人主页需要前三条关系
	RelationshipList []*OfferRoomOfferingRelationshipInfo `protobuf:"bytes,6,rep,name=relationship_list,json=relationshipList,proto3" json:"relationship_list,omitempty"`
	// 资源配置版本号
	NameplateCfgVersion  uint32   `protobuf:"varint,7,opt,name=nameplate_cfg_version,json=nameplateCfgVersion,proto3" json:"nameplate_cfg_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfferRoomCardInfoResponse) Reset()         { *m = OfferRoomCardInfoResponse{} }
func (m *OfferRoomCardInfoResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomCardInfoResponse) ProtoMessage()    {}
func (*OfferRoomCardInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{35}
}
func (m *OfferRoomCardInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomCardInfoResponse.Unmarshal(m, b)
}
func (m *OfferRoomCardInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomCardInfoResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomCardInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomCardInfoResponse.Merge(dst, src)
}
func (m *OfferRoomCardInfoResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomCardInfoResponse.Size(m)
}
func (m *OfferRoomCardInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomCardInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomCardInfoResponse proto.InternalMessageInfo

func (m *OfferRoomCardInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfferRoomCardInfoResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *OfferRoomCardInfoResponse) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *OfferRoomCardInfoResponse) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *OfferRoomCardInfoResponse) GetStarCnt() uint32 {
	if m != nil {
		return m.StarCnt
	}
	return 0
}

func (m *OfferRoomCardInfoResponse) GetRelationshipList() []*OfferRoomOfferingRelationshipInfo {
	if m != nil {
		return m.RelationshipList
	}
	return nil
}

func (m *OfferRoomCardInfoResponse) GetNameplateCfgVersion() uint32 {
	if m != nil {
		return m.NameplateCfgVersion
	}
	return 0
}

// 删除关系
type OfferRoomDeleteRelationshipRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	RecordId             uint32       `protobuf:"varint,2,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomDeleteRelationshipRequest) Reset()         { *m = OfferRoomDeleteRelationshipRequest{} }
func (m *OfferRoomDeleteRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomDeleteRelationshipRequest) ProtoMessage()    {}
func (*OfferRoomDeleteRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{36}
}
func (m *OfferRoomDeleteRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomDeleteRelationshipRequest.Unmarshal(m, b)
}
func (m *OfferRoomDeleteRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomDeleteRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomDeleteRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomDeleteRelationshipRequest.Merge(dst, src)
}
func (m *OfferRoomDeleteRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomDeleteRelationshipRequest.Size(m)
}
func (m *OfferRoomDeleteRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomDeleteRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomDeleteRelationshipRequest proto.InternalMessageInfo

func (m *OfferRoomDeleteRelationshipRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomDeleteRelationshipRequest) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

type OfferRoomDeleteRelationshipResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomDeleteRelationshipResponse) Reset()         { *m = OfferRoomDeleteRelationshipResponse{} }
func (m *OfferRoomDeleteRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomDeleteRelationshipResponse) ProtoMessage()    {}
func (*OfferRoomDeleteRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{37}
}
func (m *OfferRoomDeleteRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomDeleteRelationshipResponse.Unmarshal(m, b)
}
func (m *OfferRoomDeleteRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomDeleteRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomDeleteRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomDeleteRelationshipResponse.Merge(dst, src)
}
func (m *OfferRoomDeleteRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomDeleteRelationshipResponse.Size(m)
}
func (m *OfferRoomDeleteRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomDeleteRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomDeleteRelationshipResponse proto.InternalMessageInfo

func (m *OfferRoomDeleteRelationshipResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type OfferRoomOfferingInitRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	HonorGuestUid        uint32       `protobuf:"varint,3,opt,name=honor_guest_uid,json=honorGuestUid,proto3" json:"honor_guest_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomOfferingInitRequest) Reset()         { *m = OfferRoomOfferingInitRequest{} }
func (m *OfferRoomOfferingInitRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingInitRequest) ProtoMessage()    {}
func (*OfferRoomOfferingInitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{38}
}
func (m *OfferRoomOfferingInitRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingInitRequest.Unmarshal(m, b)
}
func (m *OfferRoomOfferingInitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingInitRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingInitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingInitRequest.Merge(dst, src)
}
func (m *OfferRoomOfferingInitRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingInitRequest.Size(m)
}
func (m *OfferRoomOfferingInitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingInitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingInitRequest proto.InternalMessageInfo

func (m *OfferRoomOfferingInitRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomOfferingInitRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfferRoomOfferingInitRequest) GetHonorGuestUid() uint32 {
	if m != nil {
		return m.HonorGuestUid
	}
	return 0
}

type OfferRoomOfferingInitResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomOfferingInitResponse) Reset()         { *m = OfferRoomOfferingInitResponse{} }
func (m *OfferRoomOfferingInitResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomOfferingInitResponse) ProtoMessage()    {}
func (*OfferRoomOfferingInitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{39}
}
func (m *OfferRoomOfferingInitResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomOfferingInitResponse.Unmarshal(m, b)
}
func (m *OfferRoomOfferingInitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomOfferingInitResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomOfferingInitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomOfferingInitResponse.Merge(dst, src)
}
func (m *OfferRoomOfferingInitResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomOfferingInitResponse.Size(m)
}
func (m *OfferRoomOfferingInitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomOfferingInitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomOfferingInitResponse proto.InternalMessageInfo

func (m *OfferRoomOfferingInitResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type OfferRoomEmptyResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomEmptyResponse) Reset()         { *m = OfferRoomEmptyResponse{} }
func (m *OfferRoomEmptyResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomEmptyResponse) ProtoMessage()    {}
func (*OfferRoomEmptyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{40}
}
func (m *OfferRoomEmptyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomEmptyResponse.Unmarshal(m, b)
}
func (m *OfferRoomEmptyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomEmptyResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomEmptyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomEmptyResponse.Merge(dst, src)
}
func (m *OfferRoomEmptyResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomEmptyResponse.Size(m)
}
func (m *OfferRoomEmptyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomEmptyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomEmptyResponse proto.InternalMessageInfo

func (m *OfferRoomEmptyResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取用户拍卖铭牌信息
type OfferRoomGetUserNameplateInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	LastCnt              uint32       `protobuf:"varint,3,opt,name=last_cnt,json=lastCnt,proto3" json:"last_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomGetUserNameplateInfoReq) Reset()         { *m = OfferRoomGetUserNameplateInfoReq{} }
func (m *OfferRoomGetUserNameplateInfoReq) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGetUserNameplateInfoReq) ProtoMessage()    {}
func (*OfferRoomGetUserNameplateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{41}
}
func (m *OfferRoomGetUserNameplateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGetUserNameplateInfoReq.Unmarshal(m, b)
}
func (m *OfferRoomGetUserNameplateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGetUserNameplateInfoReq.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGetUserNameplateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGetUserNameplateInfoReq.Merge(dst, src)
}
func (m *OfferRoomGetUserNameplateInfoReq) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGetUserNameplateInfoReq.Size(m)
}
func (m *OfferRoomGetUserNameplateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGetUserNameplateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGetUserNameplateInfoReq proto.InternalMessageInfo

func (m *OfferRoomGetUserNameplateInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfferRoomGetUserNameplateInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OfferRoomGetUserNameplateInfoReq) GetLastCnt() uint32 {
	if m != nil {
		return m.LastCnt
	}
	return 0
}

type OfferRoomGetUserNameplateInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Rank                 uint32        `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`
	StarCnt              uint32        `protobuf:"varint,3,opt,name=star_cnt,json=starCnt,proto3" json:"star_cnt,omitempty"`
	StarChangeCnt        int32         `protobuf:"varint,4,opt,name=star_change_cnt,json=starChangeCnt,proto3" json:"star_change_cnt,omitempty"`
	TotalStarCnt         uint32        `protobuf:"varint,5,opt,name=total_star_cnt,json=totalStarCnt,proto3" json:"total_star_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *OfferRoomGetUserNameplateInfoResp) Reset()         { *m = OfferRoomGetUserNameplateInfoResp{} }
func (m *OfferRoomGetUserNameplateInfoResp) String() string { return proto.CompactTextString(m) }
func (*OfferRoomGetUserNameplateInfoResp) ProtoMessage()    {}
func (*OfferRoomGetUserNameplateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{42}
}
func (m *OfferRoomGetUserNameplateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomGetUserNameplateInfoResp.Unmarshal(m, b)
}
func (m *OfferRoomGetUserNameplateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomGetUserNameplateInfoResp.Marshal(b, m, deterministic)
}
func (dst *OfferRoomGetUserNameplateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomGetUserNameplateInfoResp.Merge(dst, src)
}
func (m *OfferRoomGetUserNameplateInfoResp) XXX_Size() int {
	return xxx_messageInfo_OfferRoomGetUserNameplateInfoResp.Size(m)
}
func (m *OfferRoomGetUserNameplateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomGetUserNameplateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomGetUserNameplateInfoResp proto.InternalMessageInfo

func (m *OfferRoomGetUserNameplateInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfferRoomGetUserNameplateInfoResp) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *OfferRoomGetUserNameplateInfoResp) GetStarCnt() uint32 {
	if m != nil {
		return m.StarCnt
	}
	return 0
}

func (m *OfferRoomGetUserNameplateInfoResp) GetStarChangeCnt() int32 {
	if m != nil {
		return m.StarChangeCnt
	}
	return 0
}

func (m *OfferRoomGetUserNameplateInfoResp) GetTotalStarCnt() uint32 {
	if m != nil {
		return m.TotalStarCnt
	}
	return 0
}

// 获取铭牌配置
type OfferRoomNameplateConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OfferRoomNameplateConfigReq) Reset()         { *m = OfferRoomNameplateConfigReq{} }
func (m *OfferRoomNameplateConfigReq) String() string { return proto.CompactTextString(m) }
func (*OfferRoomNameplateConfigReq) ProtoMessage()    {}
func (*OfferRoomNameplateConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{43}
}
func (m *OfferRoomNameplateConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomNameplateConfigReq.Unmarshal(m, b)
}
func (m *OfferRoomNameplateConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomNameplateConfigReq.Marshal(b, m, deterministic)
}
func (dst *OfferRoomNameplateConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomNameplateConfigReq.Merge(dst, src)
}
func (m *OfferRoomNameplateConfigReq) XXX_Size() int {
	return xxx_messageInfo_OfferRoomNameplateConfigReq.Size(m)
}
func (m *OfferRoomNameplateConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomNameplateConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomNameplateConfigReq proto.InternalMessageInfo

func (m *OfferRoomNameplateConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type OfferRoomNameplateConfigResp struct {
	BaseResp             *app.BaseResp                             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NameplateList        []*OfferRoomNameplateConfigResp_Nameplate `protobuf:"bytes,2,rep,name=nameplate_list,json=nameplateList,proto3" json:"nameplate_list,omitempty"`
	NameplateCfgVersion  uint32                                    `protobuf:"varint,3,opt,name=nameplate_cfg_version,json=nameplateCfgVersion,proto3" json:"nameplate_cfg_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *OfferRoomNameplateConfigResp) Reset()         { *m = OfferRoomNameplateConfigResp{} }
func (m *OfferRoomNameplateConfigResp) String() string { return proto.CompactTextString(m) }
func (*OfferRoomNameplateConfigResp) ProtoMessage()    {}
func (*OfferRoomNameplateConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{44}
}
func (m *OfferRoomNameplateConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomNameplateConfigResp.Unmarshal(m, b)
}
func (m *OfferRoomNameplateConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomNameplateConfigResp.Marshal(b, m, deterministic)
}
func (dst *OfferRoomNameplateConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomNameplateConfigResp.Merge(dst, src)
}
func (m *OfferRoomNameplateConfigResp) XXX_Size() int {
	return xxx_messageInfo_OfferRoomNameplateConfigResp.Size(m)
}
func (m *OfferRoomNameplateConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomNameplateConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomNameplateConfigResp proto.InternalMessageInfo

func (m *OfferRoomNameplateConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfferRoomNameplateConfigResp) GetNameplateList() []*OfferRoomNameplateConfigResp_Nameplate {
	if m != nil {
		return m.NameplateList
	}
	return nil
}

func (m *OfferRoomNameplateConfigResp) GetNameplateCfgVersion() uint32 {
	if m != nil {
		return m.NameplateCfgVersion
	}
	return 0
}

type OfferRoomNameplateConfigResp_Nameplate struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	LevelThreshold       uint32   `protobuf:"varint,5,opt,name=level_threshold,json=levelThreshold,proto3" json:"level_threshold,omitempty"`
	LevelUpCnt           uint32   `protobuf:"varint,6,opt,name=level_up_cnt,json=levelUpCnt,proto3" json:"level_up_cnt,omitempty"`
	LevelName            string   `protobuf:"bytes,7,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfferRoomNameplateConfigResp_Nameplate) Reset() {
	*m = OfferRoomNameplateConfigResp_Nameplate{}
}
func (m *OfferRoomNameplateConfigResp_Nameplate) String() string { return proto.CompactTextString(m) }
func (*OfferRoomNameplateConfigResp_Nameplate) ProtoMessage()    {}
func (*OfferRoomNameplateConfigResp_Nameplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_582b5f44d401e029, []int{44, 0}
}
func (m *OfferRoomNameplateConfigResp_Nameplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomNameplateConfigResp_Nameplate.Unmarshal(m, b)
}
func (m *OfferRoomNameplateConfigResp_Nameplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomNameplateConfigResp_Nameplate.Marshal(b, m, deterministic)
}
func (dst *OfferRoomNameplateConfigResp_Nameplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomNameplateConfigResp_Nameplate.Merge(dst, src)
}
func (m *OfferRoomNameplateConfigResp_Nameplate) XXX_Size() int {
	return xxx_messageInfo_OfferRoomNameplateConfigResp_Nameplate.Size(m)
}
func (m *OfferRoomNameplateConfigResp_Nameplate) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomNameplateConfigResp_Nameplate.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomNameplateConfigResp_Nameplate proto.InternalMessageInfo

func (m *OfferRoomNameplateConfigResp_Nameplate) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *OfferRoomNameplateConfigResp_Nameplate) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *OfferRoomNameplateConfigResp_Nameplate) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *OfferRoomNameplateConfigResp_Nameplate) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *OfferRoomNameplateConfigResp_Nameplate) GetLevelThreshold() uint32 {
	if m != nil {
		return m.LevelThreshold
	}
	return 0
}

func (m *OfferRoomNameplateConfigResp_Nameplate) GetLevelUpCnt() uint32 {
	if m != nil {
		return m.LevelUpCnt
	}
	return 0
}

func (m *OfferRoomNameplateConfigResp_Nameplate) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func init() {
	proto.RegisterType((*OfferRoomApplyList)(nil), "ga.offer_room.OfferRoomApplyList")
	proto.RegisterType((*OfferRoomGetApplyListRequest)(nil), "ga.offer_room.OfferRoomGetApplyListRequest")
	proto.RegisterType((*OfferRoomGetApplyListResponse)(nil), "ga.offer_room.OfferRoomGetApplyListResponse")
	proto.RegisterType((*OfferRoomUserApplyRequest)(nil), "ga.offer_room.OfferRoomUserApplyRequest")
	proto.RegisterType((*OfferRoomUserApplyResponse)(nil), "ga.offer_room.OfferRoomUserApplyResponse")
	proto.RegisterType((*OfferRoomUserCancelApplyRequest)(nil), "ga.offer_room.OfferRoomUserCancelApplyRequest")
	proto.RegisterType((*OfferRoomUserCancelApplyResponse)(nil), "ga.offer_room.OfferRoomUserCancelApplyResponse")
	proto.RegisterType((*OfferRoomTopList)(nil), "ga.offer_room.OfferRoomTopList")
	proto.RegisterType((*OfferRoomTopList_TopListItem)(nil), "ga.offer_room.OfferRoomTopList.TopListItem")
	proto.RegisterType((*OfferRoomGameSetting)(nil), "ga.offer_room.OfferRoomGameSetting")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.offer_room.OfferRoomGameSetting.RelationshipDayMapEntry")
	proto.RegisterType((*OfferRoomSettleRelationship)(nil), "ga.offer_room.OfferRoomSettleRelationship")
	proto.RegisterType((*OfferRoomUiResource)(nil), "ga.offer_room.OfferRoomUiResource")
	proto.RegisterType((*OfferRoomUiResource_OfferRoomDownloadSourceInfo)(nil), "ga.offer_room.OfferRoomUiResource.OfferRoomDownloadSourceInfo")
	proto.RegisterType((*OfferRoomNamePricePushMsg)(nil), "ga.offer_room.OfferRoomNamePricePushMsg")
	proto.RegisterType((*OfferRoomCurOfferingGameInfo)(nil), "ga.offer_room.OfferRoomCurOfferingGameInfo")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.offer_room.OfferRoomCurOfferingGameInfo.LevelUpMapEntry")
	proto.RegisterType((*OfferRoomGetCurOfferingGameInfoRequest)(nil), "ga.offer_room.OfferRoomGetCurOfferingGameInfoRequest")
	proto.RegisterType((*OfferRoomGetCurOfferingGameInfoResponse)(nil), "ga.offer_room.OfferRoomGetCurOfferingGameInfoResponse")
	proto.RegisterType((*OfferRoomGiftInfo)(nil), "ga.offer_room.OfferRoomGiftInfo")
	proto.RegisterType((*OfferRoomOfferingConfig)(nil), "ga.offer_room.OfferRoomOfferingConfig")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.offer_room.OfferRoomOfferingConfig.CntToDaysEntry")
	proto.RegisterType((*OfferRoomGetOfferingConfigRequest)(nil), "ga.offer_room.OfferRoomGetOfferingConfigRequest")
	proto.RegisterType((*OfferRoomGetOfferingConfigResponse)(nil), "ga.offer_room.OfferRoomGetOfferingConfigResponse")
	proto.RegisterType((*OfferRoomSubmitOfferingSettingRequest)(nil), "ga.offer_room.OfferRoomSubmitOfferingSettingRequest")
	proto.RegisterType((*OfferRoomSubmitOfferingSettingResponse)(nil), "ga.offer_room.OfferRoomSubmitOfferingSettingResponse")
	proto.RegisterType((*OfferRoomNamePriceOnceRequest)(nil), "ga.offer_room.OfferRoomNamePriceOnceRequest")
	proto.RegisterType((*OfferRoomNamePriceOnceResponse)(nil), "ga.offer_room.OfferRoomNamePriceOnceResponse")
	proto.RegisterType((*OfferRoomNamePriceMaxRequest)(nil), "ga.offer_room.OfferRoomNamePriceMaxRequest")
	proto.RegisterType((*OfferRoomNamePriceMaxResponse)(nil), "ga.offer_room.OfferRoomNamePriceMaxResponse")
	proto.RegisterType((*OfferRoomOfferingSetRequest)(nil), "ga.offer_room.OfferRoomOfferingSetRequest")
	proto.RegisterType((*OfferRoomOfferingSetResponse)(nil), "ga.offer_room.OfferRoomOfferingSetResponse")
	proto.RegisterType((*OfferRoomOfferingPassRequest)(nil), "ga.offer_room.OfferRoomOfferingPassRequest")
	proto.RegisterType((*OfferRoomOfferingPassResponse)(nil), "ga.offer_room.OfferRoomOfferingPassResponse")
	proto.RegisterType((*OfferRoomOfferingEndRequest)(nil), "ga.offer_room.OfferRoomOfferingEndRequest")
	proto.RegisterType((*OfferRoomOfferingEndResponse)(nil), "ga.offer_room.OfferRoomOfferingEndResponse")
	proto.RegisterType((*OfferRoomOfferingRelationshipsRequest)(nil), "ga.offer_room.OfferRoomOfferingRelationshipsRequest")
	proto.RegisterType((*OfferRoomOfferingRelationshipInfo)(nil), "ga.offer_room.OfferRoomOfferingRelationshipInfo")
	proto.RegisterType((*OfferRoomOfferingRelationshipsResponse)(nil), "ga.offer_room.OfferRoomOfferingRelationshipsResponse")
	proto.RegisterType((*OfferRoomCardInfoRequest)(nil), "ga.offer_room.OfferRoomCardInfoRequest")
	proto.RegisterType((*OfferRoomCardInfoResponse)(nil), "ga.offer_room.OfferRoomCardInfoResponse")
	proto.RegisterType((*OfferRoomDeleteRelationshipRequest)(nil), "ga.offer_room.OfferRoomDeleteRelationshipRequest")
	proto.RegisterType((*OfferRoomDeleteRelationshipResponse)(nil), "ga.offer_room.OfferRoomDeleteRelationshipResponse")
	proto.RegisterType((*OfferRoomOfferingInitRequest)(nil), "ga.offer_room.OfferRoomOfferingInitRequest")
	proto.RegisterType((*OfferRoomOfferingInitResponse)(nil), "ga.offer_room.OfferRoomOfferingInitResponse")
	proto.RegisterType((*OfferRoomEmptyResponse)(nil), "ga.offer_room.OfferRoomEmptyResponse")
	proto.RegisterType((*OfferRoomGetUserNameplateInfoReq)(nil), "ga.offer_room.OfferRoomGetUserNameplateInfoReq")
	proto.RegisterType((*OfferRoomGetUserNameplateInfoResp)(nil), "ga.offer_room.OfferRoomGetUserNameplateInfoResp")
	proto.RegisterType((*OfferRoomNameplateConfigReq)(nil), "ga.offer_room.OfferRoomNameplateConfigReq")
	proto.RegisterType((*OfferRoomNameplateConfigResp)(nil), "ga.offer_room.OfferRoomNameplateConfigResp")
	proto.RegisterType((*OfferRoomNameplateConfigResp_Nameplate)(nil), "ga.offer_room.OfferRoomNameplateConfigResp.Nameplate")
	proto.RegisterEnum("ga.offer_room.OfferRoomGamePhase", OfferRoomGamePhase_name, OfferRoomGamePhase_value)
	proto.RegisterEnum("ga.offer_room.OfferRoomNamePriceOperationType", OfferRoomNamePriceOperationType_name, OfferRoomNamePriceOperationType_value)
	proto.RegisterEnum("ga.offer_room.OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType", OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType_name, OfferRoomUiResource_OfferRoomDownloadSourceInfo_OfferRoomDownloadSourceType_value)
}

func init() {
	proto.RegisterFile("offer_room/offer_room.proto", fileDescriptor_offer_room_582b5f44d401e029)
}

var fileDescriptor_offer_room_582b5f44d401e029 = []byte{
	// 2786 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x1a, 0x4d, 0x73, 0xdb, 0xd6,
	0xb1, 0x24, 0x24, 0x91, 0x58, 0x8a, 0x14, 0x0d, 0xdb, 0xb1, 0x22, 0xc5, 0x89, 0x02, 0x27, 0x8a,
	0x63, 0xa7, 0x74, 0x2a, 0xdb, 0x49, 0x9a, 0xa4, 0x1f, 0x0a, 0x45, 0x2b, 0x4c, 0x25, 0x92, 0x03,
	0x52, 0x49, 0x9b, 0x36, 0x83, 0x81, 0xc0, 0x27, 0x10, 0x23, 0x10, 0x0f, 0xc1, 0x87, 0x25, 0xa6,
	0xe7, 0xcc, 0xb4, 0x9d, 0xb4, 0xc7, 0x4e, 0x7b, 0x6b, 0x2f, 0x3d, 0xb4, 0xa7, 0x66, 0x3a, 0xbd,
	0xf4, 0xd8, 0x4b, 0x67, 0x7a, 0xeb, 0x9f, 0xe8, 0x2f, 0xe8, 0xbd, 0xf3, 0x3e, 0x00, 0x3e, 0x48,
	0xa0, 0x44, 0x3a, 0x4c, 0x7c, 0x22, 0xde, 0xbe, 0xc5, 0xee, 0xbe, 0xdd, 0x7d, 0xfb, 0x05, 0xc2,
	0x3a, 0x3e, 0x3a, 0x42, 0xbe, 0xee, 0x63, 0x3c, 0xbc, 0x37, 0x7e, 0xac, 0x79, 0x3e, 0x0e, 0xb1,
	0x52, 0xb6, 0x8c, 0xda, 0x18, 0xb8, 0x56, 0xb6, 0x0c, 0xfd, 0xd0, 0x08, 0x10, 0xdb, 0x55, 0xbb,
	0xa0, 0xb4, 0xc9, 0xa6, 0x86, 0xf1, 0x70, 0xdb, 0xf3, 0x9c, 0xd1, 0x9e, 0x1d, 0x84, 0xca, 0x2d,
	0x58, 0x70, 0xec, 0x20, 0x5c, 0xcd, 0x6d, 0x48, 0xb7, 0x4b, 0x5b, 0x2b, 0x35, 0xcb, 0xa8, 0x1d,
	0x04, 0xc8, 0xef, 0xf8, 0xf8, 0xc8, 0x76, 0x90, 0x46, 0x37, 0x95, 0x55, 0x28, 0x3c, 0x46, 0x7e,
	0x60, 0x63, 0x77, 0x35, 0xbf, 0x91, 0xbb, 0x2d, 0x69, 0xf1, 0x52, 0x45, 0xf0, 0x5c, 0x42, 0x74,
	0x17, 0x85, 0x09, 0x5d, 0x0d, 0x7d, 0x1a, 0xa1, 0x20, 0x54, 0x36, 0xa1, 0x48, 0x44, 0xd0, 0x7d,
	0xf4, 0xe9, 0x6a, 0x6e, 0x23, 0x77, 0xbb, 0xb4, 0x55, 0x22, 0x2c, 0xde, 0x33, 0x02, 0xa4, 0xa1,
	0x4f, 0xb5, 0xc2, 0x21, 0x7b, 0x50, 0x6e, 0x02, 0x98, 0x03, 0xc3, 0x75, 0x91, 0xa3, 0xdb, 0x7d,
	0xca, 0xa4, 0xac, 0xc9, 0x1c, 0xd2, 0xec, 0xab, 0x5f, 0xe4, 0xe0, 0xe6, 0x04, 0x3e, 0x81, 0x87,
	0xdd, 0x00, 0x29, 0xaf, 0x82, 0xcc, 0x19, 0x05, 0x1e, 0xe7, 0xb4, 0x3c, 0xe6, 0x14, 0x78, 0x5a,
	0xf1, 0x90, 0x3f, 0x29, 0x3f, 0x04, 0x30, 0xc8, 0xfb, 0x3a, 0x3d, 0x78, 0x9e, 0xe2, 0xbe, 0x58,
	0x4b, 0xe9, 0xae, 0x76, 0x5e, 0x53, 0x9a, 0x6c, 0xc4, 0x8f, 0xea, 0x21, 0x3c, 0x9b, 0x20, 0x10,
	0x6d, 0x51, 0xa4, 0x39, 0x1f, 0x79, 0x17, 0xd6, 0xb2, 0x78, 0xcc, 0x7c, 0x5c, 0x75, 0x00, 0x2f,
	0xa4, 0x08, 0xd5, 0x0d, 0xd7, 0x44, 0xce, 0xd7, 0x21, 0xf2, 0x3e, 0x6c, 0x4c, 0xe6, 0x34, 0xbb,
	0xe0, 0x7f, 0xcb, 0x41, 0x35, 0xa1, 0xd7, 0xc3, 0x1e, 0xf5, 0xd7, 0x1f, 0xa4, 0xfc, 0xf5, 0xee,
	0x24, 0xb3, 0x71, 0xf4, 0x1a, 0xff, 0x6d, 0x86, 0x68, 0xc8, 0x7c, 0x79, 0x4d, 0x87, 0x92, 0x00,
	0x24, 0xfe, 0x1f, 0x05, 0xc8, 0xe7, 0xa2, 0x9c, 0xf7, 0x7f, 0xb2, 0xa9, 0x5c, 0x83, 0x45, 0xcf,
	0xb7, 0x4d, 0xc4, 0x8f, 0xcc, 0x16, 0xca, 0x75, 0x58, 0x8a, 0x5c, 0x9b, 0x68, 0x42, 0xda, 0xc8,
	0xdd, 0x96, 0xb5, 0xc5, 0xc8, 0xb5, 0x9b, 0x7d, 0xf5, 0x5f, 0x12, 0x5c, 0x1b, 0xfb, 0xaa, 0x31,
	0x44, 0x5d, 0x14, 0x86, 0xb6, 0x6b, 0x29, 0x77, 0xe1, 0x8a, 0x8f, 0x1c, 0x23, 0xb4, 0xb1, 0x1b,
	0x0c, 0x6c, 0x4f, 0x77, 0x8d, 0x21, 0xa2, 0x7c, 0x65, 0xad, 0x2a, 0x6e, 0xb4, 0x8c, 0x21, 0x52,
	0x1e, 0xc0, 0x82, 0x65, 0x1f, 0xc5, 0xee, 0xb9, 0x31, 0xe9, 0x9c, 0xbb, 0xf6, 0x51, 0xd8, 0x74,
	0x8f, 0xb0, 0x46, 0xb1, 0x95, 0xd7, 0x40, 0x19, 0x1a, 0xa7, 0x3a, 0xc5, 0xb4, 0x5d, 0x4b, 0x67,
	0x52, 0x4b, 0x54, 0xea, 0xea, 0xd0, 0x38, 0x6d, 0xf3, 0x8d, 0x0e, 0x3d, 0xc0, 0x36, 0x94, 0x7c,
	0x14, 0x46, 0xbe, 0xab, 0x53, 0x56, 0x0b, 0x53, 0xb2, 0x02, 0xf6, 0x12, 0x59, 0x2b, 0x43, 0xb8,
	0x96, 0x3a, 0x53, 0xdf, 0x18, 0xe9, 0x43, 0xc3, 0x5b, 0x5d, 0xa4, 0xe6, 0x79, 0x67, 0x22, 0xad,
	0xb1, 0x5a, 0x6a, 0x9a, 0xf0, 0xfe, 0x8e, 0x31, 0xda, 0x37, 0xbc, 0x86, 0x1b, 0xfa, 0x23, 0x4d,
	0xf1, 0xcf, 0x6d, 0x28, 0x9b, 0xb0, 0x22, 0x48, 0xac, 0xbb, 0xd1, 0x70, 0x75, 0x89, 0x1e, 0xae,
	0x3c, 0x96, 0xa9, 0x15, 0x0d, 0xd7, 0x1a, 0x70, 0x63, 0x02, 0x59, 0xa5, 0x0a, 0xd2, 0x31, 0x1a,
	0x51, 0xbd, 0x97, 0x35, 0xf2, 0x48, 0xac, 0xfb, 0xd8, 0x70, 0xa2, 0xc4, 0xba, 0x74, 0xf1, 0x76,
	0xfe, 0xad, 0x9c, 0xfa, 0x65, 0x0e, 0xd6, 0x13, 0x99, 0x89, 0xbc, 0x0e, 0x12, 0xc9, 0x2a, 0x35,
	0x00, 0x76, 0xba, 0x8b, 0x5c, 0x48, 0xa6, 0x28, 0x04, 0xa2, 0xbc, 0x06, 0x72, 0x60, 0x38, 0x88,
	0xa1, 0xe7, 0xb3, 0xd1, 0x8b, 0x04, 0x83, 0x62, 0xaf, 0x41, 0xb1, 0x1f, 0xf9, 0x94, 0x1b, 0x37,
	0x61, 0xb2, 0x56, 0x9e, 0x85, 0x62, 0xa2, 0x81, 0x05, 0xba, 0x57, 0xb0, 0xd8, 0xd9, 0xd5, 0xff,
	0x96, 0xe1, 0xea, 0xf8, 0x1a, 0xda, 0x1a, 0x0a, 0x70, 0xe4, 0x9b, 0x48, 0xf9, 0x29, 0xc8, 0x87,
	0x96, 0xce, 0x16, 0x5c, 0xd6, 0xef, 0x4f, 0xb2, 0xcf, 0xf8, 0xb5, 0x31, 0x6c, 0x07, 0x9f, 0xb8,
	0x0e, 0x36, 0xfa, 0x5d, 0x0a, 0xa7, 0x9e, 0x50, 0x3c, 0xb4, 0xd8, 0x4a, 0x51, 0xa1, 0x9c, 0x10,
	0xd7, 0x3d, 0xd7, 0xa2, 0xa7, 0x93, 0xb5, 0x52, 0x8c, 0xd0, 0x71, 0x2d, 0xc5, 0x80, 0xe5, 0x00,
	0x19, 0xe1, 0x77, 0x62, 0x19, 0xa4, 0xb9, 0xc8, 0x50, 0xa2, 0x34, 0xb9, 0x18, 0x9c, 0xc5, 0x56,
	0xcc, 0x62, 0x61, 0x7e, 0x2c, 0xb6, 0xd2, 0x2c, 0xee, 0xc7, 0x2c, 0x16, 0xe7, 0xc7, 0xe2, 0x3e,
	0x67, 0x31, 0x80, 0x2a, 0x59, 0xea, 0x27, 0xe4, 0x0a, 0x73, 0x36, 0x4b, 0x73, 0x61, 0x53, 0x21,
	0x74, 0x3f, 0xb2, 0xdd, 0xd8, 0x6c, 0x47, 0xb0, 0xe2, 0x10, 0x37, 0x72, 0xf0, 0x49, 0xcc, 0xa8,
	0x30, 0x17, 0x46, 0x65, 0x42, 0x76, 0x0f, 0x9f, 0x8c, 0x4f, 0x44, 0xf9, 0x0c, 0x6c, 0x6b, 0x10,
	0x33, 0x2a, 0xce, 0xe7, 0x44, 0x84, 0xee, 0xfb, 0xb6, 0x35, 0xe0, 0x9c, 0x5c, 0xb8, 0xea, 0x60,
	0xf3, 0x58, 0x0f, 0x22, 0xd3, 0x44, 0x41, 0x10, 0x33, 0x93, 0xe7, 0xc2, 0xec, 0x0a, 0x21, 0xdd,
	0x65, 0x94, 0x39, 0x3f, 0x6f, 0x1c, 0x00, 0xf5, 0x60, 0x30, 0x56, 0x23, 0xcc, 0x85, 0x61, 0x12,
	0x03, 0xbb, 0x03, 0x51, 0x97, 0xf4, 0xea, 0x63, 0x0f, 0xb9, 0x31, 0xb7, 0xd2, 0x7c, 0x74, 0x49,
	0xe8, 0xb6, 0x3d, 0xe4, 0x72, 0x4e, 0x0e, 0x28, 0x8e, 0x4d, 0x82, 0x1e, 0x8b, 0xb6, 0x9c, 0xd7,
	0xf2, 0x5c, 0x78, 0x55, 0x19, 0x65, 0x12, 0xb0, 0x39, 0xb7, 0x13, 0x58, 0x4d, 0x6b, 0xd2, 0xb3,
	0xcd, 0x98, 0x67, 0x79, 0x2e, 0x3c, 0xaf, 0x8b, 0xda, 0xec, 0xd8, 0x26, 0xdb, 0x5a, 0xfb, 0xbb,
	0x24, 0x44, 0xf9, 0xf3, 0xaf, 0x29, 0x3f, 0x87, 0x12, 0x0f, 0x6c, 0xe1, 0xc8, 0x63, 0xa1, 0xb3,
	0xb2, 0xf5, 0xf1, 0x57, 0x93, 0x65, 0xd2, 0x5e, 0x6f, 0xe4, 0x21, 0x0d, 0x82, 0xe4, 0x99, 0xa4,
	0xab, 0xc8, 0x77, 0x78, 0x38, 0x25, 0x8f, 0x04, 0x32, 0xec, 0x3f, 0xe4, 0x35, 0x07, 0x79, 0x54,
	0xff, 0x97, 0x9b, 0x78, 0x00, 0x4a, 0xe3, 0x1e, 0xdc, 0x6d, 0x3f, 0x7a, 0xd4, 0xd0, 0x74, 0xad,
	0xdd, 0xde, 0xd7, 0x77, 0xda, 0x1f, 0xb5, 0xf6, 0xda, 0xdb, 0x3b, 0x7a, 0xb7, 0x7d, 0xa0, 0xd5,
	0x1b, 0x7a, 0xef, 0x27, 0x9d, 0x86, 0x7e, 0xd0, 0xea, 0x76, 0x1a, 0xf5, 0xe6, 0xa3, 0x66, 0x63,
	0xa7, 0xfa, 0x2d, 0xe5, 0x15, 0xb8, 0x75, 0xd9, 0x0b, 0xfb, 0x9d, 0x07, 0xd5, 0xdc, 0x34, 0x88,
	0x1f, 0x6e, 0x77, 0xaa, 0xf9, 0x69, 0x10, 0x3b, 0xad, 0xdd, 0xaa, 0xa4, 0xdc, 0x81, 0xcd, 0xcb,
	0x10, 0xf7, 0xda, 0xbd, 0x5e, 0xb3, 0x51, 0x5d, 0x50, 0xff, 0x91, 0x17, 0xea, 0x70, 0x52, 0x35,
	0xd1, 0xb2, 0xa6, 0x13, 0x05, 0x83, 0xfd, 0xc0, 0x9a, 0xae, 0xb2, 0xab, 0x82, 0x44, 0x52, 0x28,
	0xcb, 0xfc, 0xe4, 0x51, 0x39, 0x80, 0x0a, 0xf6, 0x10, 0x4b, 0xb3, 0xcc, 0xe0, 0x12, 0x35, 0x78,
	0x6d, 0x92, 0xc1, 0x13, 0xc6, 0xed, 0xf8, 0x35, 0x6a, 0xc4, 0x32, 0x16, 0x97, 0xca, 0xdb, 0x50,
	0x0c, 0xb1, 0xc7, 0x5a, 0x0e, 0x96, 0x95, 0x5e, 0xb8, 0xa4, 0x76, 0xd5, 0x0a, 0x21, 0xaf, 0x79,
	0xd7, 0x41, 0xb6, 0x03, 0x9d, 0xc4, 0x1e, 0xd4, 0xa7, 0xf9, 0xa6, 0xa8, 0x15, 0xed, 0x60, 0x8f,
	0xae, 0x49, 0x4d, 0xce, 0x76, 0xf4, 0xc8, 0xee, 0xf3, 0x6a, 0x48, 0x66, 0x90, 0x03, 0xbb, 0x2f,
	0xb6, 0x6e, 0x85, 0x74, 0xeb, 0xf6, 0xd7, 0x45, 0xa1, 0x77, 0xab, 0x47, 0x7e, 0x5c, 0x1b, 0x92,
	0xda, 0x8c, 0xfa, 0x7d, 0xba, 0xda, 0xcf, 0x9d, 0xa9, 0xf6, 0x49, 0xca, 0xb7, 0x8c, 0x21, 0xd2,
	0x7d, 0x1c, 0xb9, 0xfd, 0xb8, 0x1f, 0x90, 0xb4, 0x12, 0x01, 0x6a, 0x04, 0xd6, 0x4c, 0x71, 0x97,
	0x52, 0xdc, 0xbf, 0x92, 0x3e, 0xde, 0x84, 0x45, 0x6f, 0x60, 0x04, 0x2c, 0xf7, 0x56, 0x26, 0xf7,
	0x6e, 0xe4, 0x24, 0x1d, 0x82, 0xa8, 0x31, 0xfc, 0xb4, 0x22, 0x97, 0x2e, 0x54, 0x64, 0xe1, 0xac,
	0x22, 0x1f, 0xc1, 0x32, 0x3d, 0x6e, 0xc0, 0xca, 0x56, 0x9e, 0xbe, 0x6e, 0x4d, 0x51, 0xe1, 0x32,
	0x95, 0xc4, 0x5d, 0x40, 0x0b, 0x96, 0xc5, 0xc2, 0x96, 0x67, 0xa6, 0x3b, 0x93, 0xe8, 0x9c, 0xaf,
	0x3a, 0xb5, 0xd4, 0xfb, 0xca, 0x03, 0x28, 0x0f, 0xb0, 0x8b, 0x7d, 0xd4, 0xd7, 0x2d, 0xd2, 0xcc,
	0xf1, 0xcc, 0x73, 0xce, 0xdf, 0x97, 0x39, 0xd6, 0x2e, 0xed, 0xf8, 0x6e, 0x40, 0x01, 0xf9, 0xbe,
	0x3e, 0x0c, 0x2c, 0x9a, 0x3b, 0x64, 0x6d, 0x09, 0xf9, 0x3e, 0xb9, 0x35, 0x9f, 0xc0, 0xb2, 0x83,
	0x1e, 0x23, 0x47, 0x8f, 0x3c, 0x5a, 0xc8, 0x2f, 0x5f, 0x5c, 0xc8, 0x67, 0xf8, 0x4d, 0x6d, 0x8f,
	0xbc, 0x7f, 0xe0, 0x25, 0x85, 0x3c, 0x38, 0x09, 0x60, 0xed, 0x7b, 0xb0, 0x72, 0x66, 0x7b, 0xa6,
	0x82, 0x1c, 0xc3, 0xa6, 0x38, 0x06, 0xc8, 0xe0, 0x3e, 0xe7, 0x96, 0xf6, 0xb7, 0x79, 0x78, 0xe5,
	0x52, 0x8e, 0xb3, 0x8f, 0x20, 0xda, 0x50, 0x36, 0x23, 0x5f, 0xa7, 0x0e, 0x65, 0xbb, 0x47, 0x98,
	0x37, 0x03, 0x77, 0x67, 0x50, 0xb3, 0x56, 0x32, 0x23, 0x3f, 0xb9, 0xab, 0x77, 0xe0, 0xca, 0x70,
	0x94, 0xdd, 0xf7, 0xad, 0x0c, 0x47, 0xe9, 0xb6, 0xaf, 0x0e, 0xa5, 0xc8, 0x26, 0x52, 0x8a, 0x35,
	0xb2, 0x7a, 0x79, 0x3e, 0xd3, 0x20, 0x4a, 0x9e, 0xd5, 0xcf, 0x73, 0x70, 0xe5, 0x5c, 0x6b, 0x48,
	0xdc, 0x8a, 0x96, 0x0a, 0x49, 0xbc, 0x58, 0x22, 0xcb, 0x26, 0xbd, 0x5c, 0x74, 0x63, 0xdc, 0x46,
	0x4b, 0x9a, 0x4c, 0x20, 0x4c, 0xa4, 0x75, 0x90, 0x59, 0x3b, 0x43, 0x5a, 0x62, 0x96, 0xd9, 0x68,
	0x7f, 0x43, 0x5b, 0xe1, 0x78, 0xd3, 0x36, 0xb1, 0x4b, 0xa5, 0xe5, 0x9b, 0x4d, 0x13, 0xbb, 0xea,
	0x17, 0x0b, 0x70, 0x23, 0x91, 0x23, 0x3e, 0x67, 0x1d, 0xbb, 0x47, 0xb6, 0xa5, 0x54, 0x20, 0xcf,
	0x05, 0x91, 0xb4, 0xbc, 0xdd, 0x57, 0x1e, 0xc0, 0x33, 0xe7, 0x1a, 0xf0, 0x78, 0x08, 0x24, 0xdd,
	0x96, 0xb5, 0x6b, 0x67, 0xbb, 0x70, 0x1a, 0x6d, 0x1e, 0x41, 0x85, 0xb1, 0x77, 0x8f, 0x30, 0xc3,
	0x96, 0xe8, 0x9d, 0xb8, 0xbc, 0x51, 0x5e, 0xb6, 0xf8, 0x13, 0xa5, 0xf3, 0x01, 0x54, 0xc5, 0xde,
	0x95, 0x9a, 0x7d, 0xda, 0x96, 0xbb, 0x32, 0x6e, 0x6f, 0xa9, 0x9e, 0xbf, 0x0d, 0x57, 0x49, 0x9f,
	0x2f, 0xd2, 0x33, 0xdd, 0x90, 0xc6, 0x43, 0xd6, 0xe8, 0x6b, 0x09, 0x7e, 0xdd, 0x0d, 0x95, 0x03,
	0x28, 0x99, 0x6e, 0xa8, 0x87, 0x98, 0xf4, 0xe7, 0xc1, 0xea, 0x12, 0x95, 0xff, 0xe1, 0x24, 0xae,
	0x69, 0x2d, 0xd6, 0xea, 0x6e, 0xd8, 0xc3, 0x3b, 0xc6, 0x28, 0x60, 0xb7, 0x59, 0x36, 0xe3, 0xb5,
	0xd2, 0x85, 0xeb, 0x67, 0x4f, 0xc4, 0x14, 0x54, 0x98, 0x52, 0x41, 0x4a, 0xfa, 0x58, 0x44, 0x4d,
	0x6b, 0xef, 0x42, 0x25, 0xcd, 0x71, 0xa6, 0x00, 0xf1, 0x23, 0x78, 0x51, 0xbc, 0xae, 0xe9, 0xa3,
	0xcc, 0x18, 0x1b, 0xd4, 0x3f, 0xe4, 0x40, 0xbd, 0x88, 0xda, 0x93, 0xdc, 0xfb, 0x95, 0xe4, 0x8e,
	0x9a, 0x94, 0x0a, 0xbf, 0xf9, 0x9b, 0xd3, 0x19, 0x43, 0xab, 0xe0, 0xd4, 0x5a, 0xfd, 0x9d, 0x04,
	0x2f, 0x8f, 0x73, 0x45, 0x74, 0x38, 0xb4, 0x13, 0x29, 0xe3, 0xec, 0x33, 0xd7, 0x80, 0x78, 0x3e,
	0xeb, 0x4b, 0xe7, 0xb3, 0xfe, 0x3a, 0xc8, 0xec, 0x70, 0x64, 0x7f, 0x81, 0xee, 0x17, 0x19, 0x40,
	0x88, 0x04, 0xb6, 0xdb, 0x47, 0xa7, 0xdc, 0x63, 0x65, 0x76, 0x51, 0xfa, 0xe8, 0x74, 0xda, 0x09,
	0x8f, 0xf2, 0x06, 0xdc, 0x38, 0x7f, 0x97, 0x19, 0x4d, 0x96, 0xba, 0xaf, 0x9f, 0xbd, 0xcc, 0x8c,
	0xfe, 0xbb, 0xb0, 0x66, 0x46, 0x41, 0x88, 0x87, 0x44, 0xd3, 0xe7, 0xa6, 0x71, 0x45, 0x1a, 0x5d,
	0x56, 0x63, 0x0c, 0xed, 0xec, 0x54, 0xee, 0x0e, 0x5c, 0x49, 0x7b, 0x3c, 0xe1, 0x27, 0xb3, 0x30,
	0x2b, 0xfa, 0x72, 0x1f, 0x9d, 0xaa, 0x5d, 0x21, 0x57, 0x4d, 0xb0, 0xcc, 0xec, 0x33, 0xd1, 0x5f,
	0x89, 0x83, 0xf0, 0x71, 0xe5, 0xe9, 0x9a, 0xe8, 0x9b, 0xb7, 0xb3, 0x7a, 0x02, 0xcf, 0x4f, 0x92,
	0x65, 0xf6, 0xab, 0x91, 0x99, 0xc1, 0xf2, 0x99, 0x19, 0x4c, 0xfd, 0x65, 0x4e, 0x28, 0x5d, 0x13,
	0xce, 0xfb, 0x24, 0xec, 0x7d, 0xe3, 0x4a, 0x78, 0x9c, 0x65, 0x10, 0x2a, 0xca, 0xd7, 0xab, 0x83,
	0x3f, 0x89, 0x4d, 0x9f, 0xe0, 0x59, 0x4f, 0xe1, 0xbe, 0x3f, 0x07, 0x40, 0x6a, 0x79, 0x3e, 0x0c,
	0xe7, 0x19, 0x3a, 0xc4, 0xde, 0x01, 0x9d, 0x87, 0x37, 0x05, 0x5b, 0xa5, 0xe4, 0x9c, 0xdd, 0xfb,
	0x53, 0x76, 0x4f, 0xd4, 0x61, 0x04, 0xc1, 0x53, 0xb0, 0xfb, 0x07, 0x82, 0xdd, 0xd3, 0xa2, 0xcc,
	0x7e, 0xae, 0x5f, 0x64, 0xd9, 0xb2, 0xe1, 0xf6, 0x9f, 0xc2, 0xb1, 0xb2, 0xac, 0x45, 0x25, 0x99,
	0xfd, 0x54, 0x7f, 0xce, 0x09, 0xb9, 0x29, 0xa6, 0x25, 0x86, 0xd4, 0x27, 0x31, 0x5b, 0x68, 0xf8,
	0x16, 0x0a, 0x69, 0x8b, 0xc6, 0xcf, 0xc7, 0x20, 0xa4, 0x45, 0xbb, 0x09, 0xe0, 0x19, 0x16, 0xd2,
	0x43, 0x7c, 0x8c, 0x5c, 0x5e, 0x46, 0xca, 0x04, 0xd2, 0x23, 0x00, 0x92, 0x96, 0xe8, 0x76, 0x60,
	0x7f, 0x86, 0xf8, 0xd0, 0xbc, 0x48, 0x00, 0x5d, 0xfb, 0x33, 0xa4, 0xfe, 0x46, 0x12, 0x2a, 0x87,
	0x2c, 0x61, 0x69, 0xdd, 0xb5, 0x0e, 0xb2, 0x8f, 0x4c, 0xec, 0xf7, 0xc7, 0x15, 0x6e, 0x91, 0x01,
	0x9a, 0x7d, 0xe5, 0x05, 0x28, 0x79, 0x86, 0x1f, 0xba, 0xc8, 0x17, 0xc4, 0x03, 0x0e, 0x22, 0xf2,
	0xbd, 0x02, 0x2b, 0x31, 0x82, 0x61, 0x9a, 0x38, 0x72, 0x43, 0x2e, 0x64, 0x85, 0x83, 0xb7, 0x19,
	0x94, 0xc4, 0x81, 0x18, 0xd1, 0xb5, 0xcd, 0x63, 0x96, 0x9b, 0xd8, 0xbd, 0x8a, 0x29, 0xb4, 0x6c,
	0xf3, 0x98, 0xa6, 0xa4, 0xcc, 0xaf, 0x4a, 0x8b, 0x13, 0xbe, 0x2a, 0xad, 0x83, 0x8c, 0x4e, 0x3d,
	0xdb, 0x47, 0x7a, 0x18, 0xd0, 0xbc, 0x2a, 0x69, 0x45, 0x06, 0xe8, 0x05, 0x62, 0xf1, 0x5e, 0x48,
	0x15, 0xef, 0xf1, 0xc7, 0x06, 0x52, 0x62, 0x16, 0xc7, 0x1f, 0x1b, 0x48, 0x65, 0x99, 0xaa, 0xcd,
	0xe5, 0x74, 0x6d, 0x4e, 0xde, 0xb3, 0x03, 0xfd, 0x30, 0x1a, 0x21, 0x9f, 0x76, 0xa5, 0x45, 0xad,
	0x60, 0x07, 0xef, 0x91, 0xa5, 0xb2, 0x01, 0xcb, 0x76, 0xc0, 0x02, 0x9c, 0x3e, 0x34, 0x4e, 0x69,
	0x13, 0x5a, 0xd4, 0xc0, 0x0e, 0xe2, 0xf0, 0xa9, 0x7e, 0x99, 0x17, 0xf2, 0xe7, 0x04, 0xef, 0x99,
	0x3d, 0xc2, 0x66, 0x6a, 0x2b, 0x3f, 0x41, 0x5b, 0x9f, 0x9c, 0x41, 0x16, 0x8a, 0xff, 0xd7, 0x2f,
	0xab, 0xd7, 0xce, 0xba, 0x4e, 0x9a, 0x3c, 0x6d, 0x08, 0x36, 0x61, 0xc5, 0x45, 0xa7, 0xa1, 0x2e,
	0xf8, 0x2c, 0xb3, 0x71, 0x99, 0x80, 0x3b, 0x89, 0xdf, 0xbe, 0x0a, 0xa9, 0x77, 0x85, 0x4a, 0x7f,
	0x45, 0x84, 0xd7, 0xdd, 0x50, 0xfd, 0x7d, 0x0e, 0x56, 0xc7, 0x4d, 0xa3, 0xe1, 0xf7, 0x9f, 0xb0,
	0x25, 0xbe, 0xe8, 0x96, 0xbd, 0x01, 0x37, 0x5c, 0x84, 0xfa, 0x7a, 0x88, 0xbd, 0xfb, 0xa9, 0x12,
	0x2a, 0xa0, 0xde, 0x5c, 0xd4, 0xae, 0x93, 0xed, 0x1e, 0xf6, 0xee, 0xa7, 0xac, 0xa5, 0xfe, 0x47,
	0x9c, 0xd6, 0x8d, 0x65, 0x9b, 0xdd, 0x86, 0xeb, 0x20, 0x87, 0x38, 0x34, 0x1c, 0xaa, 0x08, 0x26,
	0x5e, 0x91, 0x02, 0x88, 0x43, 0x66, 0x1a, 0x58, 0x9a, 0x60, 0x60, 0x05, 0x16, 0x7c, 0xc3, 0x3d,
	0xe6, 0xc1, 0x80, 0x3e, 0x13, 0xa7, 0x0d, 0x42, 0xc3, 0x17, 0xb4, 0x5c, 0x20, 0x6b, 0x42, 0x3b,
	0xd3, 0x1f, 0x96, 0xe6, 0xe6, 0x0f, 0x5b, 0x70, 0x9d, 0x48, 0xeb, 0x39, 0x46, 0x88, 0x74, 0xf3,
	0xc8, 0xd2, 0xc5, 0xc1, 0x5d, 0x59, 0xbb, 0x9a, 0x6c, 0xd6, 0x8f, 0xac, 0x0f, 0xf9, 0x10, 0xcf,
	0x16, 0x3a, 0x94, 0x1d, 0xe4, 0xa0, 0x30, 0x3d, 0x2a, 0x9a, 0xd1, 0xf2, 0xa9, 0xf0, 0x96, 0x4f,
	0x87, 0x37, 0xb5, 0x03, 0xb7, 0x2e, 0x64, 0x35, 0x7b, 0x82, 0xf8, 0x75, 0x56, 0x3a, 0x6f, 0xba,
	0xf6, 0xbc, 0x6b, 0x98, 0x4d, 0x58, 0xa1, 0xc3, 0x2f, 0x36, 0x20, 0xa3, 0x5e, 0xcd, 0x46, 0x23,
	0x6c, 0x72, 0x46, 0x27, 0x62, 0x07, 0x76, 0x76, 0x4a, 0x67, 0xe2, 0xcc, 0x7e, 0xb6, 0x3a, 0x3c,
	0x93, 0xd0, 0x6a, 0x0c, 0xbd, 0xf0, 0x89, 0xfe, 0x01, 0x71, 0x22, 0xfc, 0xa1, 0x62, 0x17, 0x85,
	0x07, 0x01, 0xf2, 0x5b, 0xb1, 0x13, 0xf0, 0x9b, 0x3d, 0xb5, 0x8e, 0xaa, 0x20, 0x8d, 0xaf, 0x33,
	0x79, 0x24, 0x9e, 0xee, 0x18, 0x01, 0x0b, 0xeb, 0x4c, 0x1f, 0x05, 0xb2, 0x26, 0x71, 0xe4, 0xdf,
	0xb9, 0x74, 0x1f, 0x9d, 0xc1, 0x39, 0xf0, 0x66, 0xb9, 0xb3, 0xf1, 0x4d, 0xcb, 0x4f, 0xb8, 0x69,
	0x52, 0xfa, 0xa6, 0x6d, 0xc2, 0x0a, 0xdb, 0x1a, 0x18, 0xae, 0x85, 0x28, 0x06, 0xb9, 0xa3, 0x8b,
	0x5a, 0x99, 0x62, 0x50, 0x28, 0xc1, 0x7b, 0x09, 0x2a, 0x2c, 0x14, 0x9c, 0xb9, 0xb2, 0xcb, 0x14,
	0xda, 0x65, 0xd4, 0xd4, 0x86, 0x50, 0x5d, 0x25, 0xa7, 0x48, 0x46, 0x02, 0x53, 0x8f, 0x03, 0xfe,
	0x28, 0x9d, 0xe9, 0x3a, 0x52, 0x74, 0x66, 0xd3, 0xc7, 0xcf, 0xa0, 0x32, 0xbe, 0xeb, 0xc9, 0x08,
	0xea, 0x82, 0xa1, 0x4c, 0x06, 0xbf, 0x5a, 0x02, 0xd3, 0xca, 0x09, 0xb1, 0x8b, 0x23, 0x89, 0x34,
	0x31, 0x92, 0xac, 0xfd, 0x33, 0x07, 0x72, 0x42, 0x90, 0xd8, 0x4b, 0xf8, 0x7b, 0x0a, 0x7d, 0x9e,
	0xe6, 0x53, 0x94, 0x72, 0x0d, 0x16, 0xe9, 0xb4, 0x97, 0x87, 0x54, 0xb6, 0x20, 0x85, 0x0f, 0x1b,
	0x2a, 0x87, 0x03, 0x1f, 0x05, 0x03, 0xec, 0xf4, 0xb9, 0x9d, 0x2a, 0x14, 0xdc, 0x8b, 0xa1, 0xa4,
	0x2c, 0x48, 0xa6, 0xcf, 0xc4, 0x9a, 0xac, 0xf5, 0x8f, 0x07, 0xc8, 0xc4, 0xe2, 0x37, 0x81, 0xad,
	0x58, 0x60, 0x2f, 0xb0, 0x1a, 0x8f, 0x42, 0x88, 0xf0, 0x77, 0xfe, 0x92, 0x13, 0xfe, 0xe5, 0x96,
	0xcc, 0xff, 0x95, 0x97, 0xe1, 0x45, 0xe1, 0xab, 0xd2, 0xee, 0xf6, 0x7e, 0x43, 0xef, 0xbc, 0xbf,
	0xdd, 0x3d, 0xfb, 0xdd, 0xeb, 0x36, 0xbc, 0x94, 0x8d, 0xd6, 0x6c, 0xe9, 0x1d, 0xad, 0xd1, 0xd9,
	0xd6, 0xb6, 0x7b, 0xcd, 0x76, 0xab, 0x9a, 0x53, 0x54, 0x78, 0x3e, 0x1b, 0x93, 0x42, 0x9b, 0xad,
	0xdd, 0x6a, 0x5e, 0xd9, 0x80, 0xe7, 0xb2, 0x71, 0xba, 0x8d, 0x5e, 0x6f, 0xaf, 0x51, 0x95, 0xee,
	0x7c, 0x9e, 0x17, 0xfe, 0x9b, 0x95, 0xfd, 0x1d, 0x49, 0x79, 0x00, 0xaf, 0x0b, 0x54, 0x5a, 0x94,
	0x8a, 0xd6, 0xac, 0x37, 0xf4, 0x76, 0xa7, 0xc1, 0xc4, 0xc9, 0xfa, 0x82, 0xf7, 0x16, 0x3c, 0x98,
	0xea, 0x2d, 0x71, 0xa7, 0x55, 0x6f, 0x54, 0x73, 0xca, 0x9b, 0x70, 0x7f, 0xd6, 0x37, 0xf7, 0xb7,
	0x7f, 0x5c, 0xcd, 0x2b, 0xdf, 0x85, 0x87, 0x33, 0xbf, 0xd8, 0xd0, 0x76, 0x1b, 0x55, 0xe9, 0xbd,
	0x16, 0xac, 0x9a, 0x78, 0x58, 0x1b, 0xd9, 0x23, 0x1c, 0x91, 0x0b, 0x30, 0xc4, 0x7d, 0xe4, 0xb0,
	0xbf, 0x2d, 0x7e, 0xbc, 0x65, 0x61, 0xc7, 0x70, 0xad, 0xda, 0xc3, 0xad, 0x30, 0xac, 0x99, 0x78,
	0x78, 0x8f, 0x82, 0x4d, 0xec, 0xdc, 0x33, 0x3c, 0x4f, 0xf8, 0x0f, 0xe4, 0x3b, 0xe3, 0xc7, 0xc3,
	0x25, 0x8a, 0x73, 0xff, 0xff, 0x01, 0x00, 0x00, 0xff, 0xff, 0xe6, 0xdc, 0x1d, 0x45, 0x2e, 0x29,
	0x00, 0x00,
}
