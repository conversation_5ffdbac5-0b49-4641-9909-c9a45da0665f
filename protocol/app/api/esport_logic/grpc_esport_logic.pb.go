// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/esport_logic/grpc_esport_logic.proto

package esport_logic // import "golang.52tt.com/protocol/app/api/esport_logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import esport_logic "golang.52tt.com/protocol/app/esport_logic"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EsportLogicClient is the client API for EsportLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EsportLogicClient interface {
	// 获取游戏列表
	GetGameList(ctx context.Context, in *esport_logic.GetGameListRequest, opts ...grpc.CallOption) (*esport_logic.GetGameListResponse, error)
	// 获取开关状态
	GetSwitch(ctx context.Context, in *esport_logic.GetSwitchRequest, opts ...grpc.CallOption) (*esport_logic.GetSwitchResponse, error)
	// 获取用户电竞指导身份信息（个人主页大神页入口、成为电竞指导入口(非工会入口)）
	GetESportApplyAccess(ctx context.Context, in *esport_logic.GetESportApplyAccessRequest, opts ...grpc.CallOption) (*esport_logic.GetESportApplyAccessResponse, error)
	// 找人优化 获取顶部游戏列表
	EsportGetTopGameList(ctx context.Context, in *esport_logic.GetTopGameListRequest, opts ...grpc.CallOption) (*esport_logic.GetTopGameListResponse, error)
	// 获取游戏属性(筛选项)
	GetInviteOrderRecommend(ctx context.Context, in *esport_logic.GetInviteOrderRecommendRequest, opts ...grpc.CallOption) (*esport_logic.GetInviteOrderRecommendResponse, error)
	// 获取游戏属性(筛选项)
	GetGamePropertyList(ctx context.Context, in *esport_logic.GetGamePropertyListRequest, opts ...grpc.CallOption) (*esport_logic.GetGamePropertyListResponse, error)
	// 获取电竞专区电竞教练列表
	GetEsportAreaCoachList(ctx context.Context, in *esport_logic.GetEsportAreaCoachListRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportAreaCoachListResponse, error)
	// 个人主页技能商品列表
	GetHomePageSkillProductList(ctx context.Context, in *esport_logic.GetHomePageSkillProductListRequest, opts ...grpc.CallOption) (*esport_logic.GetHomepageSkillProductListResponse, error)
	// im浮窗商品列表/订单状态
	GetIMFloatWindowInfo(ctx context.Context, in *esport_logic.GetIMFloatWindowInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetIMFloatWindowInfoResponse, error)
	// 邀请下单
	InviteOrder(ctx context.Context, in *esport_logic.InviteOrderRequest, opts ...grpc.CallOption) (*esport_logic.InviteOrderResponse, error)
	// 玩家下单
	PlayerPayOrder(ctx context.Context, in *esport_logic.PlayerPayOrderRequest, opts ...grpc.CallOption) (*esport_logic.PlayerPayOrderResponse, error)
	// 玩家取消订单
	PlayerCancelOrder(ctx context.Context, in *esport_logic.PlayerCancelOrderRequest, opts ...grpc.CallOption) (*esport_logic.PlayerCancelOrderResponse, error)
	// 玩家确认完成订单
	PlayerFinishOrder(ctx context.Context, in *esport_logic.PlayerFinishOrderRequest, opts ...grpc.CallOption) (*esport_logic.PlayerFinishOrderResponse, error)
	// 电竞指导接单
	CoachReceiveOrder(ctx context.Context, in *esport_logic.CoachReceiveOrderRequest, opts ...grpc.CallOption) (*esport_logic.CoachReceiveOrderResponse, error)
	// 电竞指导拒绝接单
	CoachRefuseOrder(ctx context.Context, in *esport_logic.CoachRefuseOrderRequest, opts ...grpc.CallOption) (*esport_logic.CoachRefuseOrderResponse, error)
	// 获取订单详情
	GetOrderDetail(ctx context.Context, in *esport_logic.GetOrderDetailRequest, opts ...grpc.CallOption) (*esport_logic.GetOrderDetailResponse, error)
	// 获取订单列表
	GetOrderList(ctx context.Context, in *esport_logic.GetOrderListRequest, opts ...grpc.CallOption) (*esport_logic.GetOrderListResponse, error)
	// 删除订单
	DelOrderRecord(ctx context.Context, in *esport_logic.DelOrderRecordRequest, opts ...grpc.CallOption) (*esport_logic.DelOrderRecordResponse, error)
	// 获取原因文案列表
	GetReasonTextList(ctx context.Context, in *esport_logic.GetReasonTextListRequest, opts ...grpc.CallOption) (*esport_logic.GetReasonTextListResponse, error)
	// 电竞指导提醒玩家去完成订单
	CoachNotifyFinishOrder(ctx context.Context, in *esport_logic.CoachNotifyFinishOrderRequest, opts ...grpc.CallOption) (*esport_logic.CoachNotifyFinishOrderResponse, error)
	// 接受退款
	AcceptRefund(ctx context.Context, in *esport_logic.AcceptRefundRequest, opts ...grpc.CallOption) (*esport_logic.AcceptRefundResponse, error)
	// 处理下单邀请
	HandleInviteOrder(ctx context.Context, in *esport_logic.HandleInviteOrderRequest, opts ...grpc.CallOption) (*esport_logic.HandleInviteOrderResponse, error)
	// 获取评价快捷词列表
	GetEvaluateWordList(ctx context.Context, in *esport_logic.GetEvaluateWordListRequest, opts ...grpc.CallOption) (*esport_logic.GetEvaluateWordListResponse, error)
	// 评价
	Evaluate(ctx context.Context, in *esport_logic.EvaluateRequest, opts ...grpc.CallOption) (*esport_logic.EvaluateResponse, error)
	// 新建游戏名片时获取配置
	GetEsportGameCardConfig(ctx context.Context, in *esport_logic.GetEsportGameCardConfigRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportGameCardConfigResponse, error)
	// 获取游戏名片信息
	GetEsportGameCardInfo(ctx context.Context, in *esport_logic.GetEsportGameCardInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportGameCardInfoResponse, error)
	// 更新创建游戏名片
	UpsertEsportGameCardInfo(ctx context.Context, in *esport_logic.UpsertEsportGameCardInfoRequest, opts ...grpc.CallOption) (*esport_logic.UpsertEsportGameCardInfoResponse, error)
	// 获取游戏名片列表
	GetEsportGameCardList(ctx context.Context, in *esport_logic.GetEsportGameCardListRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportGameCardListResponse, error)
	// 获取游戏名片列表
	SendEsportGameCard(ctx context.Context, in *esport_logic.SendEsportGameCardRequest, opts ...grpc.CallOption) (*esport_logic.SendEsportGameCardResponse, error)
	// 删除游戏名片
	DeleteEsportGameCard(ctx context.Context, in *esport_logic.DeleteEsportGameCardRequest, opts ...grpc.CallOption) (*esport_logic.DeleteEsportGameCardResponse, error)
	// 获取配置了游戏名片的游戏
	GetGameCardGame(ctx context.Context, in *esport_logic.GetGameCardGameRequest, opts ...grpc.CallOption) (*esport_logic.GetGameCardGameResponse, error)
	// 上报用户从大神游戏详情卡片进入大神IM页
	EnterEsportIMPageReport(ctx context.Context, in *esport_logic.EnterEsportIMPageReportRequest, opts ...grpc.CallOption) (*esport_logic.EnterEsportIMPageReportResponse, error)
	// 获取IM页电竞tag
	GetChatListEsportTags(ctx context.Context, in *esport_logic.GetChatListEsportTagsRequest, opts ...grpc.CallOption) (*esport_logic.GetChatListEsportTagsResponse, error)
	// 获取快捷回复列表
	GetEsportQuickReplyList(ctx context.Context, in *esport_logic.GetEsportQuickReplyListRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportQuickReplyListResponse, error)
	// 上报快捷回复
	ReportQuickReply(ctx context.Context, in *esport_logic.ReportQuickReplyRequest, opts ...grpc.CallOption) (*esport_logic.ReportQuickReplyResponse, error)
	// 上报已曝光大神
	ReportExposeCoach(ctx context.Context, in *esport_logic.ReportExposeCoachRequest, opts ...grpc.CallOption) (*esport_logic.ReportExposeCoachResponse, error)
	// 玩家下单前置检查
	PlayerPayOrderPreCheck(ctx context.Context, in *esport_logic.PlayerPayOrderRequest, opts ...grpc.CallOption) (*esport_logic.PlayerPayOrderResponse, error)
	// 估算订单总价
	EstimateOrderTotalPrice(ctx context.Context, in *esport_logic.EstimateOrderTotalPriceRequest, opts ...grpc.CallOption) (*esport_logic.EstimateOrderTotalPriceResponse, error)
	// 联系客服
	ContactCustomerService(ctx context.Context, in *esport_logic.ContactCustomerServiceRequest, opts ...grpc.CallOption) (*esport_logic.ContactCustomerServiceResponse, error)
	// 判断指定用户是否是客服
	IsUserACustomer(ctx context.Context, in *esport_logic.IsUserACustomerRequest, opts ...grpc.CallOption) (*esport_logic.IsUserACustomerResponse, error)
	// 获取ugc房推荐列表的入口
	GetUGCReListEnt(ctx context.Context, in *esport_logic.GetUGCReListEntRequest, opts ...grpc.CallOption) (*esport_logic.GetUGCReListEntResponse, error)
	// 获取ugc房推荐大神卡信息接口
	GetUGCReCoachCardInfo(ctx context.Context, in *esport_logic.GetUGCReCoachCardInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetUGCReCoachCardInfoResponse, error)
	// 更新不再推荐状态接口
	NoMoreReOnUGC(ctx context.Context, in *esport_logic.NoMoreReOnUGCRequest, opts ...grpc.CallOption) (*esport_logic.NoMoreReOnUGCResponse, error)
	// 获取推荐大神列表接口
	GetRecommendedGodList(ctx context.Context, in *esport_logic.GetRecommendedGodListRequest, opts ...grpc.CallOption) (*esport_logic.GetRecommendedGodListResponse, error)
	// 登录获取优惠券弹窗
	LoginAppShowCouponRemain(ctx context.Context, in *esport_logic.LoginAppShowCouponRemainRequest, opts ...grpc.CallOption) (*esport_logic.LoginAppShowCouponRemainResponse, error)
	// 显示手动发放的优惠券
	ShowManualGrantCoupon(ctx context.Context, in *esport_logic.ShowManualGrantCouponRequest, opts ...grpc.CallOption) (*esport_logic.ShowManualGrantCouponResponse, error)
	// 标记手动发放的优惠券已读
	MarkManualGrantCouponRead(ctx context.Context, in *esport_logic.MarkManualGrantCouponReadRequest, opts ...grpc.CallOption) (*esport_logic.MarkManualGrantCouponReadResponse, error)
	// 获取优惠券入口信息
	GetCouponEntranceInfo(ctx context.Context, in *esport_logic.GetCouponEntranceInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetCouponEntranceInfoResponse, error)
	// 获取首页优惠券入口信息
	GetHomeCouponEntranceInfo(ctx context.Context, in *esport_logic.GetHomeCouponEntranceInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetHomeCouponEntranceInfoResponse, error)
	// 电竞专区顶部金刚位
	GetEsportAreaTopBannerList(ctx context.Context, in *esport_logic.GetEsportAreaTopBannerListRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportAreaTopBannerListResponse, error)
	// 获取电竞大神任务信息
	GetEsportCoachMissionInfo(ctx context.Context, in *esport_logic.GetEsportCoachMissionInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportCoachMissionInfoResponse, error)
	// 获取新用户退出挽留展示的推荐大神（复用专区列表的结构）
	GetBackRecallReCoach(ctx context.Context, in *esport_logic.GetBackRecallReCoachRequest, opts ...grpc.CallOption) (*esport_logic.GetBackRecallReCoachResponse, error)
	// 获取专区新用户承接弹出页的数据
	GetNewCustomerTabSetting(ctx context.Context, in *esport_logic.GetNewCustomerTabSettingRequest, opts ...grpc.CallOption) (*esport_logic.GetNewCustomerTabSettingResponse, error)
	// 提交专区新用户承接弹出页的数据
	PostNewCustomerTabSetting(ctx context.Context, in *esport_logic.PostNewCustomerTabSettingRequest, opts ...grpc.CallOption) (*esport_logic.PostNewCustomerTabSettingResponse, error)
	// GetOneKeyFindCoachEntry 获取一键找人入口
	GetOneKeyFindCoachEntry(ctx context.Context, in *esport_logic.GetOneKeyFindCoachEntryRequest, opts ...grpc.CallOption) (*esport_logic.GetOneKeyFindCoachEntryResponse, error)
	// GetOneKeyPublishCfg 获取一键找人发布配置
	GetOneKeyPublishCfg(ctx context.Context, in *esport_logic.GetOneKeyPublishCfgRequest, opts ...grpc.CallOption) (*esport_logic.GetOneKeyPublishCfgResponse, error)
	// PublishOneKeyFindCoach 发布一键找人
	PublishOneKeyFindCoach(ctx context.Context, in *esport_logic.PublishOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.PublishOneKeyFindCoachResponse, error)
	// CancelOneKeyFindCoach 取消一键找人
	CancelOneKeyFindCoach(ctx context.Context, in *esport_logic.CancelOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.CancelOneKeyFindCoachResponse, error)
	// StickOneKeyFindCoach 置顶一键找人
	StickOneKeyFindCoach(ctx context.Context, in *esport_logic.StickOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.StickOneKeyFindCoachResponse, error)
	// GetGoingOneKeyFindCoach 获取进行中的一键找人
	GetGoingOneKeyFindCoach(ctx context.Context, in *esport_logic.GetGoingOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.GetGoingOneKeyFindCoachResponse, error)
	// EsportReportClickIm 电竞IM页点击上报
	EsportReportClickIm(ctx context.Context, in *esport_logic.EsportReportClickImRequest, opts ...grpc.CallOption) (*esport_logic.EsportReportClickImResponse, error)
	// EsportRegionHeartbeat 电竞区域心跳
	EsportRegionHeartbeat(ctx context.Context, in *esport_logic.EsportRegionHeartbeatRequest, opts ...grpc.CallOption) (*esport_logic.EsportRegionHeartbeatResponse, error)
	// CheckIfCanPublishOneKeyFindCoach 检查是否可以发布一键找人
	CheckIfCanPublishOneKeyFindCoach(ctx context.Context, in *esport_logic.CheckIfCanPublishOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.CheckIfCanPublishOneKeyFindCoachResponse, error)
	// GetGlobalOneKeyFindCfg 获取全局一键找人配置
	GetGlobalOneKeyFindCfg(ctx context.Context, in *esport_logic.GetGlobalOneKeyFindCfgRequest, opts ...grpc.CallOption) (*esport_logic.GetGlobalOneKeyFindCfgResponse, error)
}

type esportLogicClient struct {
	cc *grpc.ClientConn
}

func NewEsportLogicClient(cc *grpc.ClientConn) EsportLogicClient {
	return &esportLogicClient{cc}
}

func (c *esportLogicClient) GetGameList(ctx context.Context, in *esport_logic.GetGameListRequest, opts ...grpc.CallOption) (*esport_logic.GetGameListResponse, error) {
	out := new(esport_logic.GetGameListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetSwitch(ctx context.Context, in *esport_logic.GetSwitchRequest, opts ...grpc.CallOption) (*esport_logic.GetSwitchResponse, error) {
	out := new(esport_logic.GetSwitchResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetESportApplyAccess(ctx context.Context, in *esport_logic.GetESportApplyAccessRequest, opts ...grpc.CallOption) (*esport_logic.GetESportApplyAccessResponse, error) {
	out := new(esport_logic.GetESportApplyAccessResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetESportApplyAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) EsportGetTopGameList(ctx context.Context, in *esport_logic.GetTopGameListRequest, opts ...grpc.CallOption) (*esport_logic.GetTopGameListResponse, error) {
	out := new(esport_logic.GetTopGameListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/EsportGetTopGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetInviteOrderRecommend(ctx context.Context, in *esport_logic.GetInviteOrderRecommendRequest, opts ...grpc.CallOption) (*esport_logic.GetInviteOrderRecommendResponse, error) {
	out := new(esport_logic.GetInviteOrderRecommendResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetInviteOrderRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetGamePropertyList(ctx context.Context, in *esport_logic.GetGamePropertyListRequest, opts ...grpc.CallOption) (*esport_logic.GetGamePropertyListResponse, error) {
	out := new(esport_logic.GetGamePropertyListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetGamePropertyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetEsportAreaCoachList(ctx context.Context, in *esport_logic.GetEsportAreaCoachListRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportAreaCoachListResponse, error) {
	out := new(esport_logic.GetEsportAreaCoachListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetEsportAreaCoachList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetHomePageSkillProductList(ctx context.Context, in *esport_logic.GetHomePageSkillProductListRequest, opts ...grpc.CallOption) (*esport_logic.GetHomepageSkillProductListResponse, error) {
	out := new(esport_logic.GetHomepageSkillProductListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetHomePageSkillProductList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetIMFloatWindowInfo(ctx context.Context, in *esport_logic.GetIMFloatWindowInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetIMFloatWindowInfoResponse, error) {
	out := new(esport_logic.GetIMFloatWindowInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetIMFloatWindowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) InviteOrder(ctx context.Context, in *esport_logic.InviteOrderRequest, opts ...grpc.CallOption) (*esport_logic.InviteOrderResponse, error) {
	out := new(esport_logic.InviteOrderResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/InviteOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) PlayerPayOrder(ctx context.Context, in *esport_logic.PlayerPayOrderRequest, opts ...grpc.CallOption) (*esport_logic.PlayerPayOrderResponse, error) {
	out := new(esport_logic.PlayerPayOrderResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/PlayerPayOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) PlayerCancelOrder(ctx context.Context, in *esport_logic.PlayerCancelOrderRequest, opts ...grpc.CallOption) (*esport_logic.PlayerCancelOrderResponse, error) {
	out := new(esport_logic.PlayerCancelOrderResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/PlayerCancelOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) PlayerFinishOrder(ctx context.Context, in *esport_logic.PlayerFinishOrderRequest, opts ...grpc.CallOption) (*esport_logic.PlayerFinishOrderResponse, error) {
	out := new(esport_logic.PlayerFinishOrderResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/PlayerFinishOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) CoachReceiveOrder(ctx context.Context, in *esport_logic.CoachReceiveOrderRequest, opts ...grpc.CallOption) (*esport_logic.CoachReceiveOrderResponse, error) {
	out := new(esport_logic.CoachReceiveOrderResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/CoachReceiveOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) CoachRefuseOrder(ctx context.Context, in *esport_logic.CoachRefuseOrderRequest, opts ...grpc.CallOption) (*esport_logic.CoachRefuseOrderResponse, error) {
	out := new(esport_logic.CoachRefuseOrderResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/CoachRefuseOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetOrderDetail(ctx context.Context, in *esport_logic.GetOrderDetailRequest, opts ...grpc.CallOption) (*esport_logic.GetOrderDetailResponse, error) {
	out := new(esport_logic.GetOrderDetailResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetOrderList(ctx context.Context, in *esport_logic.GetOrderListRequest, opts ...grpc.CallOption) (*esport_logic.GetOrderListResponse, error) {
	out := new(esport_logic.GetOrderListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) DelOrderRecord(ctx context.Context, in *esport_logic.DelOrderRecordRequest, opts ...grpc.CallOption) (*esport_logic.DelOrderRecordResponse, error) {
	out := new(esport_logic.DelOrderRecordResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/DelOrderRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetReasonTextList(ctx context.Context, in *esport_logic.GetReasonTextListRequest, opts ...grpc.CallOption) (*esport_logic.GetReasonTextListResponse, error) {
	out := new(esport_logic.GetReasonTextListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetReasonTextList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) CoachNotifyFinishOrder(ctx context.Context, in *esport_logic.CoachNotifyFinishOrderRequest, opts ...grpc.CallOption) (*esport_logic.CoachNotifyFinishOrderResponse, error) {
	out := new(esport_logic.CoachNotifyFinishOrderResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/CoachNotifyFinishOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) AcceptRefund(ctx context.Context, in *esport_logic.AcceptRefundRequest, opts ...grpc.CallOption) (*esport_logic.AcceptRefundResponse, error) {
	out := new(esport_logic.AcceptRefundResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/AcceptRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) HandleInviteOrder(ctx context.Context, in *esport_logic.HandleInviteOrderRequest, opts ...grpc.CallOption) (*esport_logic.HandleInviteOrderResponse, error) {
	out := new(esport_logic.HandleInviteOrderResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/HandleInviteOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetEvaluateWordList(ctx context.Context, in *esport_logic.GetEvaluateWordListRequest, opts ...grpc.CallOption) (*esport_logic.GetEvaluateWordListResponse, error) {
	out := new(esport_logic.GetEvaluateWordListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetEvaluateWordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) Evaluate(ctx context.Context, in *esport_logic.EvaluateRequest, opts ...grpc.CallOption) (*esport_logic.EvaluateResponse, error) {
	out := new(esport_logic.EvaluateResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/Evaluate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetEsportGameCardConfig(ctx context.Context, in *esport_logic.GetEsportGameCardConfigRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportGameCardConfigResponse, error) {
	out := new(esport_logic.GetEsportGameCardConfigResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetEsportGameCardConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetEsportGameCardInfo(ctx context.Context, in *esport_logic.GetEsportGameCardInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportGameCardInfoResponse, error) {
	out := new(esport_logic.GetEsportGameCardInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetEsportGameCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) UpsertEsportGameCardInfo(ctx context.Context, in *esport_logic.UpsertEsportGameCardInfoRequest, opts ...grpc.CallOption) (*esport_logic.UpsertEsportGameCardInfoResponse, error) {
	out := new(esport_logic.UpsertEsportGameCardInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/UpsertEsportGameCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetEsportGameCardList(ctx context.Context, in *esport_logic.GetEsportGameCardListRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportGameCardListResponse, error) {
	out := new(esport_logic.GetEsportGameCardListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetEsportGameCardList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) SendEsportGameCard(ctx context.Context, in *esport_logic.SendEsportGameCardRequest, opts ...grpc.CallOption) (*esport_logic.SendEsportGameCardResponse, error) {
	out := new(esport_logic.SendEsportGameCardResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/SendEsportGameCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) DeleteEsportGameCard(ctx context.Context, in *esport_logic.DeleteEsportGameCardRequest, opts ...grpc.CallOption) (*esport_logic.DeleteEsportGameCardResponse, error) {
	out := new(esport_logic.DeleteEsportGameCardResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/DeleteEsportGameCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetGameCardGame(ctx context.Context, in *esport_logic.GetGameCardGameRequest, opts ...grpc.CallOption) (*esport_logic.GetGameCardGameResponse, error) {
	out := new(esport_logic.GetGameCardGameResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetGameCardGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) EnterEsportIMPageReport(ctx context.Context, in *esport_logic.EnterEsportIMPageReportRequest, opts ...grpc.CallOption) (*esport_logic.EnterEsportIMPageReportResponse, error) {
	out := new(esport_logic.EnterEsportIMPageReportResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/EnterEsportIMPageReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetChatListEsportTags(ctx context.Context, in *esport_logic.GetChatListEsportTagsRequest, opts ...grpc.CallOption) (*esport_logic.GetChatListEsportTagsResponse, error) {
	out := new(esport_logic.GetChatListEsportTagsResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetChatListEsportTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetEsportQuickReplyList(ctx context.Context, in *esport_logic.GetEsportQuickReplyListRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportQuickReplyListResponse, error) {
	out := new(esport_logic.GetEsportQuickReplyListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetEsportQuickReplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) ReportQuickReply(ctx context.Context, in *esport_logic.ReportQuickReplyRequest, opts ...grpc.CallOption) (*esport_logic.ReportQuickReplyResponse, error) {
	out := new(esport_logic.ReportQuickReplyResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/ReportQuickReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) ReportExposeCoach(ctx context.Context, in *esport_logic.ReportExposeCoachRequest, opts ...grpc.CallOption) (*esport_logic.ReportExposeCoachResponse, error) {
	out := new(esport_logic.ReportExposeCoachResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/ReportExposeCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) PlayerPayOrderPreCheck(ctx context.Context, in *esport_logic.PlayerPayOrderRequest, opts ...grpc.CallOption) (*esport_logic.PlayerPayOrderResponse, error) {
	out := new(esport_logic.PlayerPayOrderResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/PlayerPayOrderPreCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) EstimateOrderTotalPrice(ctx context.Context, in *esport_logic.EstimateOrderTotalPriceRequest, opts ...grpc.CallOption) (*esport_logic.EstimateOrderTotalPriceResponse, error) {
	out := new(esport_logic.EstimateOrderTotalPriceResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/EstimateOrderTotalPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) ContactCustomerService(ctx context.Context, in *esport_logic.ContactCustomerServiceRequest, opts ...grpc.CallOption) (*esport_logic.ContactCustomerServiceResponse, error) {
	out := new(esport_logic.ContactCustomerServiceResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/ContactCustomerService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) IsUserACustomer(ctx context.Context, in *esport_logic.IsUserACustomerRequest, opts ...grpc.CallOption) (*esport_logic.IsUserACustomerResponse, error) {
	out := new(esport_logic.IsUserACustomerResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/IsUserACustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetUGCReListEnt(ctx context.Context, in *esport_logic.GetUGCReListEntRequest, opts ...grpc.CallOption) (*esport_logic.GetUGCReListEntResponse, error) {
	out := new(esport_logic.GetUGCReListEntResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetUGCReListEnt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetUGCReCoachCardInfo(ctx context.Context, in *esport_logic.GetUGCReCoachCardInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetUGCReCoachCardInfoResponse, error) {
	out := new(esport_logic.GetUGCReCoachCardInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetUGCReCoachCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) NoMoreReOnUGC(ctx context.Context, in *esport_logic.NoMoreReOnUGCRequest, opts ...grpc.CallOption) (*esport_logic.NoMoreReOnUGCResponse, error) {
	out := new(esport_logic.NoMoreReOnUGCResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/NoMoreReOnUGC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetRecommendedGodList(ctx context.Context, in *esport_logic.GetRecommendedGodListRequest, opts ...grpc.CallOption) (*esport_logic.GetRecommendedGodListResponse, error) {
	out := new(esport_logic.GetRecommendedGodListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetRecommendedGodList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) LoginAppShowCouponRemain(ctx context.Context, in *esport_logic.LoginAppShowCouponRemainRequest, opts ...grpc.CallOption) (*esport_logic.LoginAppShowCouponRemainResponse, error) {
	out := new(esport_logic.LoginAppShowCouponRemainResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/LoginAppShowCouponRemain", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) ShowManualGrantCoupon(ctx context.Context, in *esport_logic.ShowManualGrantCouponRequest, opts ...grpc.CallOption) (*esport_logic.ShowManualGrantCouponResponse, error) {
	out := new(esport_logic.ShowManualGrantCouponResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/ShowManualGrantCoupon", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) MarkManualGrantCouponRead(ctx context.Context, in *esport_logic.MarkManualGrantCouponReadRequest, opts ...grpc.CallOption) (*esport_logic.MarkManualGrantCouponReadResponse, error) {
	out := new(esport_logic.MarkManualGrantCouponReadResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/MarkManualGrantCouponRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetCouponEntranceInfo(ctx context.Context, in *esport_logic.GetCouponEntranceInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetCouponEntranceInfoResponse, error) {
	out := new(esport_logic.GetCouponEntranceInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetCouponEntranceInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetHomeCouponEntranceInfo(ctx context.Context, in *esport_logic.GetHomeCouponEntranceInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetHomeCouponEntranceInfoResponse, error) {
	out := new(esport_logic.GetHomeCouponEntranceInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetHomeCouponEntranceInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetEsportAreaTopBannerList(ctx context.Context, in *esport_logic.GetEsportAreaTopBannerListRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportAreaTopBannerListResponse, error) {
	out := new(esport_logic.GetEsportAreaTopBannerListResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetEsportAreaTopBannerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetEsportCoachMissionInfo(ctx context.Context, in *esport_logic.GetEsportCoachMissionInfoRequest, opts ...grpc.CallOption) (*esport_logic.GetEsportCoachMissionInfoResponse, error) {
	out := new(esport_logic.GetEsportCoachMissionInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetEsportCoachMissionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetBackRecallReCoach(ctx context.Context, in *esport_logic.GetBackRecallReCoachRequest, opts ...grpc.CallOption) (*esport_logic.GetBackRecallReCoachResponse, error) {
	out := new(esport_logic.GetBackRecallReCoachResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetBackRecallReCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetNewCustomerTabSetting(ctx context.Context, in *esport_logic.GetNewCustomerTabSettingRequest, opts ...grpc.CallOption) (*esport_logic.GetNewCustomerTabSettingResponse, error) {
	out := new(esport_logic.GetNewCustomerTabSettingResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetNewCustomerTabSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) PostNewCustomerTabSetting(ctx context.Context, in *esport_logic.PostNewCustomerTabSettingRequest, opts ...grpc.CallOption) (*esport_logic.PostNewCustomerTabSettingResponse, error) {
	out := new(esport_logic.PostNewCustomerTabSettingResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/PostNewCustomerTabSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetOneKeyFindCoachEntry(ctx context.Context, in *esport_logic.GetOneKeyFindCoachEntryRequest, opts ...grpc.CallOption) (*esport_logic.GetOneKeyFindCoachEntryResponse, error) {
	out := new(esport_logic.GetOneKeyFindCoachEntryResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetOneKeyFindCoachEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetOneKeyPublishCfg(ctx context.Context, in *esport_logic.GetOneKeyPublishCfgRequest, opts ...grpc.CallOption) (*esport_logic.GetOneKeyPublishCfgResponse, error) {
	out := new(esport_logic.GetOneKeyPublishCfgResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetOneKeyPublishCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) PublishOneKeyFindCoach(ctx context.Context, in *esport_logic.PublishOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.PublishOneKeyFindCoachResponse, error) {
	out := new(esport_logic.PublishOneKeyFindCoachResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/PublishOneKeyFindCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) CancelOneKeyFindCoach(ctx context.Context, in *esport_logic.CancelOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.CancelOneKeyFindCoachResponse, error) {
	out := new(esport_logic.CancelOneKeyFindCoachResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/CancelOneKeyFindCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) StickOneKeyFindCoach(ctx context.Context, in *esport_logic.StickOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.StickOneKeyFindCoachResponse, error) {
	out := new(esport_logic.StickOneKeyFindCoachResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/StickOneKeyFindCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetGoingOneKeyFindCoach(ctx context.Context, in *esport_logic.GetGoingOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.GetGoingOneKeyFindCoachResponse, error) {
	out := new(esport_logic.GetGoingOneKeyFindCoachResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetGoingOneKeyFindCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) EsportReportClickIm(ctx context.Context, in *esport_logic.EsportReportClickImRequest, opts ...grpc.CallOption) (*esport_logic.EsportReportClickImResponse, error) {
	out := new(esport_logic.EsportReportClickImResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/EsportReportClickIm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) EsportRegionHeartbeat(ctx context.Context, in *esport_logic.EsportRegionHeartbeatRequest, opts ...grpc.CallOption) (*esport_logic.EsportRegionHeartbeatResponse, error) {
	out := new(esport_logic.EsportRegionHeartbeatResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/EsportRegionHeartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) CheckIfCanPublishOneKeyFindCoach(ctx context.Context, in *esport_logic.CheckIfCanPublishOneKeyFindCoachRequest, opts ...grpc.CallOption) (*esport_logic.CheckIfCanPublishOneKeyFindCoachResponse, error) {
	out := new(esport_logic.CheckIfCanPublishOneKeyFindCoachResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/CheckIfCanPublishOneKeyFindCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportLogicClient) GetGlobalOneKeyFindCfg(ctx context.Context, in *esport_logic.GetGlobalOneKeyFindCfgRequest, opts ...grpc.CallOption) (*esport_logic.GetGlobalOneKeyFindCfgResponse, error) {
	out := new(esport_logic.GetGlobalOneKeyFindCfgResponse)
	err := c.cc.Invoke(ctx, "/ga.api.esport_logic.EsportLogic/GetGlobalOneKeyFindCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EsportLogicServer is the server API for EsportLogic service.
type EsportLogicServer interface {
	// 获取游戏列表
	GetGameList(context.Context, *esport_logic.GetGameListRequest) (*esport_logic.GetGameListResponse, error)
	// 获取开关状态
	GetSwitch(context.Context, *esport_logic.GetSwitchRequest) (*esport_logic.GetSwitchResponse, error)
	// 获取用户电竞指导身份信息（个人主页大神页入口、成为电竞指导入口(非工会入口)）
	GetESportApplyAccess(context.Context, *esport_logic.GetESportApplyAccessRequest) (*esport_logic.GetESportApplyAccessResponse, error)
	// 找人优化 获取顶部游戏列表
	EsportGetTopGameList(context.Context, *esport_logic.GetTopGameListRequest) (*esport_logic.GetTopGameListResponse, error)
	// 获取游戏属性(筛选项)
	GetInviteOrderRecommend(context.Context, *esport_logic.GetInviteOrderRecommendRequest) (*esport_logic.GetInviteOrderRecommendResponse, error)
	// 获取游戏属性(筛选项)
	GetGamePropertyList(context.Context, *esport_logic.GetGamePropertyListRequest) (*esport_logic.GetGamePropertyListResponse, error)
	// 获取电竞专区电竞教练列表
	GetEsportAreaCoachList(context.Context, *esport_logic.GetEsportAreaCoachListRequest) (*esport_logic.GetEsportAreaCoachListResponse, error)
	// 个人主页技能商品列表
	GetHomePageSkillProductList(context.Context, *esport_logic.GetHomePageSkillProductListRequest) (*esport_logic.GetHomepageSkillProductListResponse, error)
	// im浮窗商品列表/订单状态
	GetIMFloatWindowInfo(context.Context, *esport_logic.GetIMFloatWindowInfoRequest) (*esport_logic.GetIMFloatWindowInfoResponse, error)
	// 邀请下单
	InviteOrder(context.Context, *esport_logic.InviteOrderRequest) (*esport_logic.InviteOrderResponse, error)
	// 玩家下单
	PlayerPayOrder(context.Context, *esport_logic.PlayerPayOrderRequest) (*esport_logic.PlayerPayOrderResponse, error)
	// 玩家取消订单
	PlayerCancelOrder(context.Context, *esport_logic.PlayerCancelOrderRequest) (*esport_logic.PlayerCancelOrderResponse, error)
	// 玩家确认完成订单
	PlayerFinishOrder(context.Context, *esport_logic.PlayerFinishOrderRequest) (*esport_logic.PlayerFinishOrderResponse, error)
	// 电竞指导接单
	CoachReceiveOrder(context.Context, *esport_logic.CoachReceiveOrderRequest) (*esport_logic.CoachReceiveOrderResponse, error)
	// 电竞指导拒绝接单
	CoachRefuseOrder(context.Context, *esport_logic.CoachRefuseOrderRequest) (*esport_logic.CoachRefuseOrderResponse, error)
	// 获取订单详情
	GetOrderDetail(context.Context, *esport_logic.GetOrderDetailRequest) (*esport_logic.GetOrderDetailResponse, error)
	// 获取订单列表
	GetOrderList(context.Context, *esport_logic.GetOrderListRequest) (*esport_logic.GetOrderListResponse, error)
	// 删除订单
	DelOrderRecord(context.Context, *esport_logic.DelOrderRecordRequest) (*esport_logic.DelOrderRecordResponse, error)
	// 获取原因文案列表
	GetReasonTextList(context.Context, *esport_logic.GetReasonTextListRequest) (*esport_logic.GetReasonTextListResponse, error)
	// 电竞指导提醒玩家去完成订单
	CoachNotifyFinishOrder(context.Context, *esport_logic.CoachNotifyFinishOrderRequest) (*esport_logic.CoachNotifyFinishOrderResponse, error)
	// 接受退款
	AcceptRefund(context.Context, *esport_logic.AcceptRefundRequest) (*esport_logic.AcceptRefundResponse, error)
	// 处理下单邀请
	HandleInviteOrder(context.Context, *esport_logic.HandleInviteOrderRequest) (*esport_logic.HandleInviteOrderResponse, error)
	// 获取评价快捷词列表
	GetEvaluateWordList(context.Context, *esport_logic.GetEvaluateWordListRequest) (*esport_logic.GetEvaluateWordListResponse, error)
	// 评价
	Evaluate(context.Context, *esport_logic.EvaluateRequest) (*esport_logic.EvaluateResponse, error)
	// 新建游戏名片时获取配置
	GetEsportGameCardConfig(context.Context, *esport_logic.GetEsportGameCardConfigRequest) (*esport_logic.GetEsportGameCardConfigResponse, error)
	// 获取游戏名片信息
	GetEsportGameCardInfo(context.Context, *esport_logic.GetEsportGameCardInfoRequest) (*esport_logic.GetEsportGameCardInfoResponse, error)
	// 更新创建游戏名片
	UpsertEsportGameCardInfo(context.Context, *esport_logic.UpsertEsportGameCardInfoRequest) (*esport_logic.UpsertEsportGameCardInfoResponse, error)
	// 获取游戏名片列表
	GetEsportGameCardList(context.Context, *esport_logic.GetEsportGameCardListRequest) (*esport_logic.GetEsportGameCardListResponse, error)
	// 获取游戏名片列表
	SendEsportGameCard(context.Context, *esport_logic.SendEsportGameCardRequest) (*esport_logic.SendEsportGameCardResponse, error)
	// 删除游戏名片
	DeleteEsportGameCard(context.Context, *esport_logic.DeleteEsportGameCardRequest) (*esport_logic.DeleteEsportGameCardResponse, error)
	// 获取配置了游戏名片的游戏
	GetGameCardGame(context.Context, *esport_logic.GetGameCardGameRequest) (*esport_logic.GetGameCardGameResponse, error)
	// 上报用户从大神游戏详情卡片进入大神IM页
	EnterEsportIMPageReport(context.Context, *esport_logic.EnterEsportIMPageReportRequest) (*esport_logic.EnterEsportIMPageReportResponse, error)
	// 获取IM页电竞tag
	GetChatListEsportTags(context.Context, *esport_logic.GetChatListEsportTagsRequest) (*esport_logic.GetChatListEsportTagsResponse, error)
	// 获取快捷回复列表
	GetEsportQuickReplyList(context.Context, *esport_logic.GetEsportQuickReplyListRequest) (*esport_logic.GetEsportQuickReplyListResponse, error)
	// 上报快捷回复
	ReportQuickReply(context.Context, *esport_logic.ReportQuickReplyRequest) (*esport_logic.ReportQuickReplyResponse, error)
	// 上报已曝光大神
	ReportExposeCoach(context.Context, *esport_logic.ReportExposeCoachRequest) (*esport_logic.ReportExposeCoachResponse, error)
	// 玩家下单前置检查
	PlayerPayOrderPreCheck(context.Context, *esport_logic.PlayerPayOrderRequest) (*esport_logic.PlayerPayOrderResponse, error)
	// 估算订单总价
	EstimateOrderTotalPrice(context.Context, *esport_logic.EstimateOrderTotalPriceRequest) (*esport_logic.EstimateOrderTotalPriceResponse, error)
	// 联系客服
	ContactCustomerService(context.Context, *esport_logic.ContactCustomerServiceRequest) (*esport_logic.ContactCustomerServiceResponse, error)
	// 判断指定用户是否是客服
	IsUserACustomer(context.Context, *esport_logic.IsUserACustomerRequest) (*esport_logic.IsUserACustomerResponse, error)
	// 获取ugc房推荐列表的入口
	GetUGCReListEnt(context.Context, *esport_logic.GetUGCReListEntRequest) (*esport_logic.GetUGCReListEntResponse, error)
	// 获取ugc房推荐大神卡信息接口
	GetUGCReCoachCardInfo(context.Context, *esport_logic.GetUGCReCoachCardInfoRequest) (*esport_logic.GetUGCReCoachCardInfoResponse, error)
	// 更新不再推荐状态接口
	NoMoreReOnUGC(context.Context, *esport_logic.NoMoreReOnUGCRequest) (*esport_logic.NoMoreReOnUGCResponse, error)
	// 获取推荐大神列表接口
	GetRecommendedGodList(context.Context, *esport_logic.GetRecommendedGodListRequest) (*esport_logic.GetRecommendedGodListResponse, error)
	// 登录获取优惠券弹窗
	LoginAppShowCouponRemain(context.Context, *esport_logic.LoginAppShowCouponRemainRequest) (*esport_logic.LoginAppShowCouponRemainResponse, error)
	// 显示手动发放的优惠券
	ShowManualGrantCoupon(context.Context, *esport_logic.ShowManualGrantCouponRequest) (*esport_logic.ShowManualGrantCouponResponse, error)
	// 标记手动发放的优惠券已读
	MarkManualGrantCouponRead(context.Context, *esport_logic.MarkManualGrantCouponReadRequest) (*esport_logic.MarkManualGrantCouponReadResponse, error)
	// 获取优惠券入口信息
	GetCouponEntranceInfo(context.Context, *esport_logic.GetCouponEntranceInfoRequest) (*esport_logic.GetCouponEntranceInfoResponse, error)
	// 获取首页优惠券入口信息
	GetHomeCouponEntranceInfo(context.Context, *esport_logic.GetHomeCouponEntranceInfoRequest) (*esport_logic.GetHomeCouponEntranceInfoResponse, error)
	// 电竞专区顶部金刚位
	GetEsportAreaTopBannerList(context.Context, *esport_logic.GetEsportAreaTopBannerListRequest) (*esport_logic.GetEsportAreaTopBannerListResponse, error)
	// 获取电竞大神任务信息
	GetEsportCoachMissionInfo(context.Context, *esport_logic.GetEsportCoachMissionInfoRequest) (*esport_logic.GetEsportCoachMissionInfoResponse, error)
	// 获取新用户退出挽留展示的推荐大神（复用专区列表的结构）
	GetBackRecallReCoach(context.Context, *esport_logic.GetBackRecallReCoachRequest) (*esport_logic.GetBackRecallReCoachResponse, error)
	// 获取专区新用户承接弹出页的数据
	GetNewCustomerTabSetting(context.Context, *esport_logic.GetNewCustomerTabSettingRequest) (*esport_logic.GetNewCustomerTabSettingResponse, error)
	// 提交专区新用户承接弹出页的数据
	PostNewCustomerTabSetting(context.Context, *esport_logic.PostNewCustomerTabSettingRequest) (*esport_logic.PostNewCustomerTabSettingResponse, error)
	// GetOneKeyFindCoachEntry 获取一键找人入口
	GetOneKeyFindCoachEntry(context.Context, *esport_logic.GetOneKeyFindCoachEntryRequest) (*esport_logic.GetOneKeyFindCoachEntryResponse, error)
	// GetOneKeyPublishCfg 获取一键找人发布配置
	GetOneKeyPublishCfg(context.Context, *esport_logic.GetOneKeyPublishCfgRequest) (*esport_logic.GetOneKeyPublishCfgResponse, error)
	// PublishOneKeyFindCoach 发布一键找人
	PublishOneKeyFindCoach(context.Context, *esport_logic.PublishOneKeyFindCoachRequest) (*esport_logic.PublishOneKeyFindCoachResponse, error)
	// CancelOneKeyFindCoach 取消一键找人
	CancelOneKeyFindCoach(context.Context, *esport_logic.CancelOneKeyFindCoachRequest) (*esport_logic.CancelOneKeyFindCoachResponse, error)
	// StickOneKeyFindCoach 置顶一键找人
	StickOneKeyFindCoach(context.Context, *esport_logic.StickOneKeyFindCoachRequest) (*esport_logic.StickOneKeyFindCoachResponse, error)
	// GetGoingOneKeyFindCoach 获取进行中的一键找人
	GetGoingOneKeyFindCoach(context.Context, *esport_logic.GetGoingOneKeyFindCoachRequest) (*esport_logic.GetGoingOneKeyFindCoachResponse, error)
	// EsportReportClickIm 电竞IM页点击上报
	EsportReportClickIm(context.Context, *esport_logic.EsportReportClickImRequest) (*esport_logic.EsportReportClickImResponse, error)
	// EsportRegionHeartbeat 电竞区域心跳
	EsportRegionHeartbeat(context.Context, *esport_logic.EsportRegionHeartbeatRequest) (*esport_logic.EsportRegionHeartbeatResponse, error)
	// CheckIfCanPublishOneKeyFindCoach 检查是否可以发布一键找人
	CheckIfCanPublishOneKeyFindCoach(context.Context, *esport_logic.CheckIfCanPublishOneKeyFindCoachRequest) (*esport_logic.CheckIfCanPublishOneKeyFindCoachResponse, error)
	// GetGlobalOneKeyFindCfg 获取全局一键找人配置
	GetGlobalOneKeyFindCfg(context.Context, *esport_logic.GetGlobalOneKeyFindCfgRequest) (*esport_logic.GetGlobalOneKeyFindCfgResponse, error)
}

func RegisterEsportLogicServer(s *grpc.Server, srv EsportLogicServer) {
	s.RegisterService(&_EsportLogic_serviceDesc, srv)
}

func _EsportLogic_GetGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetGameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetGameList(ctx, req.(*esport_logic.GetGameListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetSwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetSwitch(ctx, req.(*esport_logic.GetSwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetESportApplyAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetESportApplyAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetESportApplyAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetESportApplyAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetESportApplyAccess(ctx, req.(*esport_logic.GetESportApplyAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_EsportGetTopGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetTopGameListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).EsportGetTopGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/EsportGetTopGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).EsportGetTopGameList(ctx, req.(*esport_logic.GetTopGameListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetInviteOrderRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetInviteOrderRecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetInviteOrderRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetInviteOrderRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetInviteOrderRecommend(ctx, req.(*esport_logic.GetInviteOrderRecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetGamePropertyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetGamePropertyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetGamePropertyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetGamePropertyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetGamePropertyList(ctx, req.(*esport_logic.GetGamePropertyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetEsportAreaCoachList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetEsportAreaCoachListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetEsportAreaCoachList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetEsportAreaCoachList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetEsportAreaCoachList(ctx, req.(*esport_logic.GetEsportAreaCoachListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetHomePageSkillProductList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetHomePageSkillProductListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetHomePageSkillProductList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetHomePageSkillProductList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetHomePageSkillProductList(ctx, req.(*esport_logic.GetHomePageSkillProductListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetIMFloatWindowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetIMFloatWindowInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetIMFloatWindowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetIMFloatWindowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetIMFloatWindowInfo(ctx, req.(*esport_logic.GetIMFloatWindowInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_InviteOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.InviteOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).InviteOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/InviteOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).InviteOrder(ctx, req.(*esport_logic.InviteOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_PlayerPayOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.PlayerPayOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).PlayerPayOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/PlayerPayOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).PlayerPayOrder(ctx, req.(*esport_logic.PlayerPayOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_PlayerCancelOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.PlayerCancelOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).PlayerCancelOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/PlayerCancelOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).PlayerCancelOrder(ctx, req.(*esport_logic.PlayerCancelOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_PlayerFinishOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.PlayerFinishOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).PlayerFinishOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/PlayerFinishOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).PlayerFinishOrder(ctx, req.(*esport_logic.PlayerFinishOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_CoachReceiveOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.CoachReceiveOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).CoachReceiveOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/CoachReceiveOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).CoachReceiveOrder(ctx, req.(*esport_logic.CoachReceiveOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_CoachRefuseOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.CoachRefuseOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).CoachRefuseOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/CoachRefuseOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).CoachRefuseOrder(ctx, req.(*esport_logic.CoachRefuseOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetOrderDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetOrderDetail(ctx, req.(*esport_logic.GetOrderDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetOrderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetOrderList(ctx, req.(*esport_logic.GetOrderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_DelOrderRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.DelOrderRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).DelOrderRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/DelOrderRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).DelOrderRecord(ctx, req.(*esport_logic.DelOrderRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetReasonTextList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetReasonTextListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetReasonTextList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetReasonTextList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetReasonTextList(ctx, req.(*esport_logic.GetReasonTextListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_CoachNotifyFinishOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.CoachNotifyFinishOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).CoachNotifyFinishOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/CoachNotifyFinishOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).CoachNotifyFinishOrder(ctx, req.(*esport_logic.CoachNotifyFinishOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_AcceptRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.AcceptRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).AcceptRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/AcceptRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).AcceptRefund(ctx, req.(*esport_logic.AcceptRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_HandleInviteOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.HandleInviteOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).HandleInviteOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/HandleInviteOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).HandleInviteOrder(ctx, req.(*esport_logic.HandleInviteOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetEvaluateWordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetEvaluateWordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetEvaluateWordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetEvaluateWordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetEvaluateWordList(ctx, req.(*esport_logic.GetEvaluateWordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_Evaluate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.EvaluateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).Evaluate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/Evaluate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).Evaluate(ctx, req.(*esport_logic.EvaluateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetEsportGameCardConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetEsportGameCardConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetEsportGameCardConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetEsportGameCardConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetEsportGameCardConfig(ctx, req.(*esport_logic.GetEsportGameCardConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetEsportGameCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetEsportGameCardInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetEsportGameCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetEsportGameCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetEsportGameCardInfo(ctx, req.(*esport_logic.GetEsportGameCardInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_UpsertEsportGameCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.UpsertEsportGameCardInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).UpsertEsportGameCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/UpsertEsportGameCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).UpsertEsportGameCardInfo(ctx, req.(*esport_logic.UpsertEsportGameCardInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetEsportGameCardList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetEsportGameCardListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetEsportGameCardList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetEsportGameCardList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetEsportGameCardList(ctx, req.(*esport_logic.GetEsportGameCardListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_SendEsportGameCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.SendEsportGameCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).SendEsportGameCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/SendEsportGameCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).SendEsportGameCard(ctx, req.(*esport_logic.SendEsportGameCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_DeleteEsportGameCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.DeleteEsportGameCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).DeleteEsportGameCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/DeleteEsportGameCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).DeleteEsportGameCard(ctx, req.(*esport_logic.DeleteEsportGameCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetGameCardGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetGameCardGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetGameCardGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetGameCardGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetGameCardGame(ctx, req.(*esport_logic.GetGameCardGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_EnterEsportIMPageReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.EnterEsportIMPageReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).EnterEsportIMPageReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/EnterEsportIMPageReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).EnterEsportIMPageReport(ctx, req.(*esport_logic.EnterEsportIMPageReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetChatListEsportTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetChatListEsportTagsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetChatListEsportTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetChatListEsportTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetChatListEsportTags(ctx, req.(*esport_logic.GetChatListEsportTagsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetEsportQuickReplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetEsportQuickReplyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetEsportQuickReplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetEsportQuickReplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetEsportQuickReplyList(ctx, req.(*esport_logic.GetEsportQuickReplyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_ReportQuickReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.ReportQuickReplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).ReportQuickReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/ReportQuickReply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).ReportQuickReply(ctx, req.(*esport_logic.ReportQuickReplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_ReportExposeCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.ReportExposeCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).ReportExposeCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/ReportExposeCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).ReportExposeCoach(ctx, req.(*esport_logic.ReportExposeCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_PlayerPayOrderPreCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.PlayerPayOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).PlayerPayOrderPreCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/PlayerPayOrderPreCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).PlayerPayOrderPreCheck(ctx, req.(*esport_logic.PlayerPayOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_EstimateOrderTotalPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.EstimateOrderTotalPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).EstimateOrderTotalPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/EstimateOrderTotalPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).EstimateOrderTotalPrice(ctx, req.(*esport_logic.EstimateOrderTotalPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_ContactCustomerService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.ContactCustomerServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).ContactCustomerService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/ContactCustomerService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).ContactCustomerService(ctx, req.(*esport_logic.ContactCustomerServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_IsUserACustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.IsUserACustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).IsUserACustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/IsUserACustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).IsUserACustomer(ctx, req.(*esport_logic.IsUserACustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetUGCReListEnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetUGCReListEntRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetUGCReListEnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetUGCReListEnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetUGCReListEnt(ctx, req.(*esport_logic.GetUGCReListEntRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetUGCReCoachCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetUGCReCoachCardInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetUGCReCoachCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetUGCReCoachCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetUGCReCoachCardInfo(ctx, req.(*esport_logic.GetUGCReCoachCardInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_NoMoreReOnUGC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.NoMoreReOnUGCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).NoMoreReOnUGC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/NoMoreReOnUGC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).NoMoreReOnUGC(ctx, req.(*esport_logic.NoMoreReOnUGCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetRecommendedGodList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetRecommendedGodListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetRecommendedGodList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetRecommendedGodList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetRecommendedGodList(ctx, req.(*esport_logic.GetRecommendedGodListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_LoginAppShowCouponRemain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.LoginAppShowCouponRemainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).LoginAppShowCouponRemain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/LoginAppShowCouponRemain",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).LoginAppShowCouponRemain(ctx, req.(*esport_logic.LoginAppShowCouponRemainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_ShowManualGrantCoupon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.ShowManualGrantCouponRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).ShowManualGrantCoupon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/ShowManualGrantCoupon",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).ShowManualGrantCoupon(ctx, req.(*esport_logic.ShowManualGrantCouponRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_MarkManualGrantCouponRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.MarkManualGrantCouponReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).MarkManualGrantCouponRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/MarkManualGrantCouponRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).MarkManualGrantCouponRead(ctx, req.(*esport_logic.MarkManualGrantCouponReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetCouponEntranceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetCouponEntranceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetCouponEntranceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetCouponEntranceInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetCouponEntranceInfo(ctx, req.(*esport_logic.GetCouponEntranceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetHomeCouponEntranceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetHomeCouponEntranceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetHomeCouponEntranceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetHomeCouponEntranceInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetHomeCouponEntranceInfo(ctx, req.(*esport_logic.GetHomeCouponEntranceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetEsportAreaTopBannerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetEsportAreaTopBannerListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetEsportAreaTopBannerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetEsportAreaTopBannerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetEsportAreaTopBannerList(ctx, req.(*esport_logic.GetEsportAreaTopBannerListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetEsportCoachMissionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetEsportCoachMissionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetEsportCoachMissionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetEsportCoachMissionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetEsportCoachMissionInfo(ctx, req.(*esport_logic.GetEsportCoachMissionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetBackRecallReCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetBackRecallReCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetBackRecallReCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetBackRecallReCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetBackRecallReCoach(ctx, req.(*esport_logic.GetBackRecallReCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetNewCustomerTabSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetNewCustomerTabSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetNewCustomerTabSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetNewCustomerTabSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetNewCustomerTabSetting(ctx, req.(*esport_logic.GetNewCustomerTabSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_PostNewCustomerTabSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.PostNewCustomerTabSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).PostNewCustomerTabSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/PostNewCustomerTabSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).PostNewCustomerTabSetting(ctx, req.(*esport_logic.PostNewCustomerTabSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetOneKeyFindCoachEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetOneKeyFindCoachEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetOneKeyFindCoachEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetOneKeyFindCoachEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetOneKeyFindCoachEntry(ctx, req.(*esport_logic.GetOneKeyFindCoachEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetOneKeyPublishCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetOneKeyPublishCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetOneKeyPublishCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetOneKeyPublishCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetOneKeyPublishCfg(ctx, req.(*esport_logic.GetOneKeyPublishCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_PublishOneKeyFindCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.PublishOneKeyFindCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).PublishOneKeyFindCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/PublishOneKeyFindCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).PublishOneKeyFindCoach(ctx, req.(*esport_logic.PublishOneKeyFindCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_CancelOneKeyFindCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.CancelOneKeyFindCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).CancelOneKeyFindCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/CancelOneKeyFindCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).CancelOneKeyFindCoach(ctx, req.(*esport_logic.CancelOneKeyFindCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_StickOneKeyFindCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.StickOneKeyFindCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).StickOneKeyFindCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/StickOneKeyFindCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).StickOneKeyFindCoach(ctx, req.(*esport_logic.StickOneKeyFindCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetGoingOneKeyFindCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetGoingOneKeyFindCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetGoingOneKeyFindCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetGoingOneKeyFindCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetGoingOneKeyFindCoach(ctx, req.(*esport_logic.GetGoingOneKeyFindCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_EsportReportClickIm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.EsportReportClickImRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).EsportReportClickIm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/EsportReportClickIm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).EsportReportClickIm(ctx, req.(*esport_logic.EsportReportClickImRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_EsportRegionHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.EsportRegionHeartbeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).EsportRegionHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/EsportRegionHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).EsportRegionHeartbeat(ctx, req.(*esport_logic.EsportRegionHeartbeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_CheckIfCanPublishOneKeyFindCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.CheckIfCanPublishOneKeyFindCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).CheckIfCanPublishOneKeyFindCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/CheckIfCanPublishOneKeyFindCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).CheckIfCanPublishOneKeyFindCoach(ctx, req.(*esport_logic.CheckIfCanPublishOneKeyFindCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportLogic_GetGlobalOneKeyFindCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(esport_logic.GetGlobalOneKeyFindCfgRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportLogicServer).GetGlobalOneKeyFindCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.esport_logic.EsportLogic/GetGlobalOneKeyFindCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportLogicServer).GetGlobalOneKeyFindCfg(ctx, req.(*esport_logic.GetGlobalOneKeyFindCfgRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EsportLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.esport_logic.EsportLogic",
	HandlerType: (*EsportLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGameList",
			Handler:    _EsportLogic_GetGameList_Handler,
		},
		{
			MethodName: "GetSwitch",
			Handler:    _EsportLogic_GetSwitch_Handler,
		},
		{
			MethodName: "GetESportApplyAccess",
			Handler:    _EsportLogic_GetESportApplyAccess_Handler,
		},
		{
			MethodName: "EsportGetTopGameList",
			Handler:    _EsportLogic_EsportGetTopGameList_Handler,
		},
		{
			MethodName: "GetInviteOrderRecommend",
			Handler:    _EsportLogic_GetInviteOrderRecommend_Handler,
		},
		{
			MethodName: "GetGamePropertyList",
			Handler:    _EsportLogic_GetGamePropertyList_Handler,
		},
		{
			MethodName: "GetEsportAreaCoachList",
			Handler:    _EsportLogic_GetEsportAreaCoachList_Handler,
		},
		{
			MethodName: "GetHomePageSkillProductList",
			Handler:    _EsportLogic_GetHomePageSkillProductList_Handler,
		},
		{
			MethodName: "GetIMFloatWindowInfo",
			Handler:    _EsportLogic_GetIMFloatWindowInfo_Handler,
		},
		{
			MethodName: "InviteOrder",
			Handler:    _EsportLogic_InviteOrder_Handler,
		},
		{
			MethodName: "PlayerPayOrder",
			Handler:    _EsportLogic_PlayerPayOrder_Handler,
		},
		{
			MethodName: "PlayerCancelOrder",
			Handler:    _EsportLogic_PlayerCancelOrder_Handler,
		},
		{
			MethodName: "PlayerFinishOrder",
			Handler:    _EsportLogic_PlayerFinishOrder_Handler,
		},
		{
			MethodName: "CoachReceiveOrder",
			Handler:    _EsportLogic_CoachReceiveOrder_Handler,
		},
		{
			MethodName: "CoachRefuseOrder",
			Handler:    _EsportLogic_CoachRefuseOrder_Handler,
		},
		{
			MethodName: "GetOrderDetail",
			Handler:    _EsportLogic_GetOrderDetail_Handler,
		},
		{
			MethodName: "GetOrderList",
			Handler:    _EsportLogic_GetOrderList_Handler,
		},
		{
			MethodName: "DelOrderRecord",
			Handler:    _EsportLogic_DelOrderRecord_Handler,
		},
		{
			MethodName: "GetReasonTextList",
			Handler:    _EsportLogic_GetReasonTextList_Handler,
		},
		{
			MethodName: "CoachNotifyFinishOrder",
			Handler:    _EsportLogic_CoachNotifyFinishOrder_Handler,
		},
		{
			MethodName: "AcceptRefund",
			Handler:    _EsportLogic_AcceptRefund_Handler,
		},
		{
			MethodName: "HandleInviteOrder",
			Handler:    _EsportLogic_HandleInviteOrder_Handler,
		},
		{
			MethodName: "GetEvaluateWordList",
			Handler:    _EsportLogic_GetEvaluateWordList_Handler,
		},
		{
			MethodName: "Evaluate",
			Handler:    _EsportLogic_Evaluate_Handler,
		},
		{
			MethodName: "GetEsportGameCardConfig",
			Handler:    _EsportLogic_GetEsportGameCardConfig_Handler,
		},
		{
			MethodName: "GetEsportGameCardInfo",
			Handler:    _EsportLogic_GetEsportGameCardInfo_Handler,
		},
		{
			MethodName: "UpsertEsportGameCardInfo",
			Handler:    _EsportLogic_UpsertEsportGameCardInfo_Handler,
		},
		{
			MethodName: "GetEsportGameCardList",
			Handler:    _EsportLogic_GetEsportGameCardList_Handler,
		},
		{
			MethodName: "SendEsportGameCard",
			Handler:    _EsportLogic_SendEsportGameCard_Handler,
		},
		{
			MethodName: "DeleteEsportGameCard",
			Handler:    _EsportLogic_DeleteEsportGameCard_Handler,
		},
		{
			MethodName: "GetGameCardGame",
			Handler:    _EsportLogic_GetGameCardGame_Handler,
		},
		{
			MethodName: "EnterEsportIMPageReport",
			Handler:    _EsportLogic_EnterEsportIMPageReport_Handler,
		},
		{
			MethodName: "GetChatListEsportTags",
			Handler:    _EsportLogic_GetChatListEsportTags_Handler,
		},
		{
			MethodName: "GetEsportQuickReplyList",
			Handler:    _EsportLogic_GetEsportQuickReplyList_Handler,
		},
		{
			MethodName: "ReportQuickReply",
			Handler:    _EsportLogic_ReportQuickReply_Handler,
		},
		{
			MethodName: "ReportExposeCoach",
			Handler:    _EsportLogic_ReportExposeCoach_Handler,
		},
		{
			MethodName: "PlayerPayOrderPreCheck",
			Handler:    _EsportLogic_PlayerPayOrderPreCheck_Handler,
		},
		{
			MethodName: "EstimateOrderTotalPrice",
			Handler:    _EsportLogic_EstimateOrderTotalPrice_Handler,
		},
		{
			MethodName: "ContactCustomerService",
			Handler:    _EsportLogic_ContactCustomerService_Handler,
		},
		{
			MethodName: "IsUserACustomer",
			Handler:    _EsportLogic_IsUserACustomer_Handler,
		},
		{
			MethodName: "GetUGCReListEnt",
			Handler:    _EsportLogic_GetUGCReListEnt_Handler,
		},
		{
			MethodName: "GetUGCReCoachCardInfo",
			Handler:    _EsportLogic_GetUGCReCoachCardInfo_Handler,
		},
		{
			MethodName: "NoMoreReOnUGC",
			Handler:    _EsportLogic_NoMoreReOnUGC_Handler,
		},
		{
			MethodName: "GetRecommendedGodList",
			Handler:    _EsportLogic_GetRecommendedGodList_Handler,
		},
		{
			MethodName: "LoginAppShowCouponRemain",
			Handler:    _EsportLogic_LoginAppShowCouponRemain_Handler,
		},
		{
			MethodName: "ShowManualGrantCoupon",
			Handler:    _EsportLogic_ShowManualGrantCoupon_Handler,
		},
		{
			MethodName: "MarkManualGrantCouponRead",
			Handler:    _EsportLogic_MarkManualGrantCouponRead_Handler,
		},
		{
			MethodName: "GetCouponEntranceInfo",
			Handler:    _EsportLogic_GetCouponEntranceInfo_Handler,
		},
		{
			MethodName: "GetHomeCouponEntranceInfo",
			Handler:    _EsportLogic_GetHomeCouponEntranceInfo_Handler,
		},
		{
			MethodName: "GetEsportAreaTopBannerList",
			Handler:    _EsportLogic_GetEsportAreaTopBannerList_Handler,
		},
		{
			MethodName: "GetEsportCoachMissionInfo",
			Handler:    _EsportLogic_GetEsportCoachMissionInfo_Handler,
		},
		{
			MethodName: "GetBackRecallReCoach",
			Handler:    _EsportLogic_GetBackRecallReCoach_Handler,
		},
		{
			MethodName: "GetNewCustomerTabSetting",
			Handler:    _EsportLogic_GetNewCustomerTabSetting_Handler,
		},
		{
			MethodName: "PostNewCustomerTabSetting",
			Handler:    _EsportLogic_PostNewCustomerTabSetting_Handler,
		},
		{
			MethodName: "GetOneKeyFindCoachEntry",
			Handler:    _EsportLogic_GetOneKeyFindCoachEntry_Handler,
		},
		{
			MethodName: "GetOneKeyPublishCfg",
			Handler:    _EsportLogic_GetOneKeyPublishCfg_Handler,
		},
		{
			MethodName: "PublishOneKeyFindCoach",
			Handler:    _EsportLogic_PublishOneKeyFindCoach_Handler,
		},
		{
			MethodName: "CancelOneKeyFindCoach",
			Handler:    _EsportLogic_CancelOneKeyFindCoach_Handler,
		},
		{
			MethodName: "StickOneKeyFindCoach",
			Handler:    _EsportLogic_StickOneKeyFindCoach_Handler,
		},
		{
			MethodName: "GetGoingOneKeyFindCoach",
			Handler:    _EsportLogic_GetGoingOneKeyFindCoach_Handler,
		},
		{
			MethodName: "EsportReportClickIm",
			Handler:    _EsportLogic_EsportReportClickIm_Handler,
		},
		{
			MethodName: "EsportRegionHeartbeat",
			Handler:    _EsportLogic_EsportRegionHeartbeat_Handler,
		},
		{
			MethodName: "CheckIfCanPublishOneKeyFindCoach",
			Handler:    _EsportLogic_CheckIfCanPublishOneKeyFindCoach_Handler,
		},
		{
			MethodName: "GetGlobalOneKeyFindCfg",
			Handler:    _EsportLogic_GetGlobalOneKeyFindCfg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/esport_logic/grpc_esport_logic.proto",
}

func init() {
	proto.RegisterFile("api/esport_logic/grpc_esport_logic.proto", fileDescriptor_grpc_esport_logic_a95e9dc0025464f0)
}

var fileDescriptor_grpc_esport_logic_a95e9dc0025464f0 = []byte{
	// 1809 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x9a, 0xf9, 0x6f, 0xdc, 0x44,
	0x1b, 0xc7, 0xb5, 0x7e, 0x5f, 0xbd, 0xea, 0xeb, 0x96, 0x6b, 0x5a, 0x02, 0x04, 0x21, 0xd2, 0x52,
	0x7a, 0xd0, 0x76, 0x43, 0x53, 0x90, 0x38, 0x7e, 0x40, 0xe9, 0x36, 0x75, 0x23, 0x9a, 0x76, 0xd9,
	0x4d, 0x54, 0x51, 0x21, 0x55, 0x13, 0xef, 0xc4, 0x6b, 0xc5, 0x3b, 0xe3, 0xda, 0xb3, 0x49, 0x83,
	0x84, 0x14, 0x15, 0x09, 0x09, 0x84, 0x40, 0xe2, 0x3f, 0x80, 0xbf, 0xc2, 0xfc, 0xc6, 0x7d, 0xdf,
	0x97, 0xb8, 0xef, 0xfb, 0xbe, 0xef, 0x4b, 0xeb, 0xb1, 0x77, 0xfd, 0xac, 0x9f, 0x71, 0x36, 0x48,
	0xfc, 0x44, 0x58, 0x7f, 0x9e, 0xf9, 0x7e, 0x3d, 0xf3, 0xcc, 0x33, 0xcf, 0xce, 0xd6, 0xdc, 0x46,
	0x7d, 0x77, 0x94, 0x85, 0xbe, 0x08, 0xe4, 0x31, 0x4f, 0x38, 0xae, 0x3d, 0xea, 0x04, 0xbe, 0x7d,
	0x2c, 0xfb, 0x49, 0xd9, 0x0f, 0x84, 0x14, 0x64, 0xbd, 0x43, 0xcb, 0xd4, 0x77, 0xcb, 0xd9, 0x47,
	0xc3, 0xe7, 0xc5, 0xe1, 0x27, 0x24, 0xe3, 0xa1, 0x2b, 0x78, 0xef, 0x2f, 0x15, 0x33, 0x7c, 0x3e,
	0x18, 0x39, 0x3f, 0xe8, 0xd8, 0x6d, 0x57, 0x99, 0x6b, 0x27, 0xe2, 0x8f, 0x0f, 0x76, 0x3e, 0x25,
	0xb3, 0xe6, 0x5a, 0x8b, 0x49, 0x8b, 0xb6, 0xd8, 0x41, 0x37, 0x94, 0xe4, 0x82, 0xb2, 0x43, 0x81,
	0x60, 0x39, 0xf3, 0xb4, 0xc6, 0x8e, 0xb7, 0x59, 0x28, 0x87, 0x37, 0x17, 0x43, 0xa1, 0x2f, 0x78,
	0xc8, 0x36, 0xad, 0x39, 0xb9, 0x3c, 0xf2, 0xdf, 0x35, 0xf7, 0x44, 0x06, 0xb9, 0xce, 0xfc, 0xbf,
	0xc5, 0x64, 0x7d, 0xd1, 0x95, 0x76, 0x93, 0x6c, 0xc4, 0x82, 0xd5, 0xb3, 0x74, 0xfc, 0x4d, 0x45,
	0x08, 0x18, 0xfd, 0xde, 0xc8, 0x20, 0x37, 0x98, 0x1b, 0x2c, 0x26, 0x27, 0xea, 0x1d, 0x7c, 0xdc,
	0xf7, 0xbd, 0xa5, 0x71, 0xdb, 0x66, 0x61, 0x48, 0x76, 0x62, 0xa3, 0xe4, 0xb0, 0x54, 0x73, 0xd7,
	0x80, 0x34, 0x90, 0xbf, 0x2f, 0x32, 0xc8, 0x71, 0x73, 0x83, 0x9a, 0x4f, 0x8b, 0xc9, 0x69, 0xe1,
	0x77, 0x67, 0x72, 0x0b, 0x36, 0x60, 0x06, 0x48, 0x85, 0xb7, 0xae, 0xc8, 0x01, 0xc9, 0xfb, 0x23,
	0x83, 0xdc, 0x54, 0x32, 0xcf, 0xb2, 0x98, 0x9c, 0xe4, 0x0b, 0xae, 0x64, 0x87, 0x83, 0x06, 0x0b,
	0x6a, 0xcc, 0x16, 0xad, 0x16, 0xe3, 0x0d, 0x32, 0x8a, 0x0d, 0x87, 0x91, 0xa9, 0xfe, 0xc5, 0x83,
	0x07, 0x00, 0x23, 0x0f, 0x44, 0x06, 0xb9, 0xde, 0x5c, 0x9f, 0xac, 0x7c, 0x35, 0x10, 0x3e, 0x0b,
	0xe4, 0x52, 0xfc, 0xea, 0x3b, 0x74, 0xf9, 0x91, 0xa5, 0x52, 0xfd, 0x9d, 0x83, 0xc1, 0x40, 0xfb,
	0xc1, 0xc8, 0x20, 0x37, 0x96, 0xcc, 0xa1, 0xce, 0x12, 0xc5, 0x91, 0xe3, 0x01, 0xa3, 0x15, 0x41,
	0xed, 0x66, 0xac, 0x5f, 0x46, 0xd7, 0x32, 0x0f, 0xa6, 0x16, 0x46, 0x07, 0xe6, 0x81, 0x8b, 0x87,
	0x22, 0x83, 0xdc, 0x51, 0x32, 0xcf, 0xb5, 0x98, 0x3c, 0x20, 0x5a, 0xac, 0x4a, 0x1d, 0x56, 0x9f,
	0x77, 0x3d, 0xaf, 0x1a, 0x88, 0x46, 0xdb, 0x96, 0xb1, 0x95, 0x3d, 0xd8, 0xd0, 0x3a, 0x3a, 0xf5,
	0x73, 0x89, 0x2e, 0xc8, 0x47, 0x83, 0x80, 0xa9, 0x87, 0xbb, 0x3b, 0x62, 0x72, 0x6a, 0xbf, 0x27,
	0xa8, 0x3c, 0xe2, 0xf2, 0x86, 0x58, 0x9c, 0xe4, 0x73, 0x02, 0xdf, 0x11, 0x39, 0xac, 0x70, 0x47,
	0x20, 0x34, 0x90, 0x7f, 0x24, 0x32, 0x3a, 0x25, 0x25, 0x93, 0x3f, 0x48, 0x49, 0x01, 0xd9, 0xa5,
	0x2b, 0x29, 0x00, 0x02, 0x1a, 0x8f, 0x46, 0x06, 0x99, 0x37, 0x4f, 0xad, 0x7a, 0x74, 0x89, 0x05,
	0x55, 0xba, 0xa4, 0x64, 0xf2, 0xfb, 0x0d, 0x02, 0xfa, 0xfd, 0xd6, 0xcf, 0x01, 0xb1, 0xc7, 0x22,
	0x83, 0x48, 0xf3, 0x0c, 0xc5, 0x54, 0x28, 0xb7, 0x99, 0xa7, 0xf4, 0xb6, 0x6b, 0xc6, 0xc9, 0x30,
	0xa9, 0xe4, 0x45, 0x83, 0xa0, 0x40, 0xf5, 0xf1, 0xac, 0xea, 0x7e, 0x97, 0xbb, 0x61, 0xb3, 0x58,
	0x35, 0xc3, 0xac, 0xa4, 0x0a, 0x50, 0xa0, 0xfa, 0x84, 0x52, 0x8d, 0xf3, 0xbd, 0xc6, 0x6c, 0xe6,
	0x2e, 0x30, 0x9d, 0x6a, 0x8e, 0xd1, 0xab, 0x22, 0x28, 0x50, 0x7d, 0x32, 0x2e, 0xa2, 0xa7, 0x27,
	0xd8, 0x5c, 0x3b, 0x4c, 0x44, 0xb7, 0xe9, 0x46, 0xea, 0x22, 0xa9, 0xe6, 0xf6, 0x01, 0x48, 0x20,
	0xf9, 0x94, 0xca, 0x20, 0x8b, 0xc9, 0xf8, 0xe9, 0x3e, 0x26, 0xa9, 0xeb, 0xe1, 0x15, 0x3b, 0x03,
	0x14, 0x56, 0x6c, 0xc0, 0x01, 0xb1, 0xa7, 0x23, 0x83, 0x30, 0x73, 0x5d, 0xca, 0xc4, 0x65, 0x61,
	0xb3, 0x76, 0x88, 0x6c, 0x1d, 0xb8, 0x70, 0x05, 0x0a, 0xc8, 0x3c, 0xa3, 0xde, 0x69, 0x5f, 0x37,
	0xa1, 0x6c, 0x11, 0x34, 0x90, 0x77, 0x82, 0x80, 0xfe, 0x9d, 0xfa, 0x39, 0x20, 0xf6, 0xac, 0xca,
	0x14, 0x8b, 0xc9, 0x1a, 0xa3, 0xa1, 0xe0, 0xd3, 0xec, 0x84, 0xaa, 0x77, 0xdb, 0x31, 0xcb, 0x90,
	0xd1, 0x67, 0x0a, 0x82, 0x02, 0xd5, 0xe7, 0x92, 0xb2, 0x1f, 0xaf, 0xee, 0x21, 0x21, 0xdd, 0xb9,
	0xa5, 0xec, 0xde, 0x28, 0xe3, 0x69, 0x90, 0x03, 0xf5, 0x65, 0x5f, 0xc7, 0x03, 0x17, 0xcf, 0xab,
	0xf5, 0xec, 0x34, 0x04, 0xbe, 0xec, 0xe4, 0x18, 0x6f, 0x20, 0xeb, 0x99, 0x7d, 0xac, 0x5f, 0x4f,
	0x48, 0x01, 0x99, 0x17, 0xd4, 0x14, 0x1f, 0xa0, 0xbc, 0xe1, 0xb1, 0x6c, 0x3d, 0xcd, 0x4f, 0x71,
	0x8e, 0xd1, 0x4f, 0x31, 0x82, 0x02, 0xd5, 0x17, 0xbb, 0xa7, 0xfa, 0xc4, 0x02, 0xf5, 0xda, 0x54,
	0xb2, 0x23, 0x22, 0x68, 0xe8, 0x4f, 0xf5, 0x7e, 0xaa, 0xf0, 0x54, 0xcf, 0xc3, 0x40, 0xfb, 0xa5,
	0xc8, 0x20, 0xd7, 0x9a, 0x6b, 0x52, 0x8a, 0x8c, 0xe4, 0xc6, 0x48, 0x1f, 0xa5, 0x2a, 0x1b, 0x0b,
	0x08, 0x30, 0xf4, 0xcb, 0xbd, 0xae, 0x29, 0x69, 0xd6, 0x68, 0x8b, 0x55, 0x68, 0xd0, 0xa8, 0x08,
	0x3e, 0xe7, 0x3a, 0xa4, 0xa0, 0x03, 0x80, 0x64, 0x61, 0xd7, 0x84, 0x07, 0x00, 0x23, 0xaf, 0x44,
	0x06, 0x59, 0x2e, 0x99, 0x67, 0xe6, 0xe8, 0xf8, 0x80, 0xde, 0xb5, 0xf2, 0xa8, 0xd9, 0x13, 0xba,
	0x3c, 0x28, 0x0e, 0x2c, 0xbc, 0x1a, 0x19, 0xe4, 0xe6, 0x92, 0x79, 0xf6, 0x8c, 0x1f, 0xb2, 0x00,
	0x73, 0x91, 0x7f, 0x37, 0x1d, 0x9a, 0x1a, 0xd9, 0xbd, 0x8a, 0x08, 0xe0, 0xe5, 0x35, 0xdd, 0x74,
	0xc4, 0x19, 0x37, 0xc0, 0x74, 0x64, 0x73, 0xae, 0x3c, 0x28, 0x0e, 0x2c, 0xbc, 0x1e, 0x19, 0x64,
	0xd1, 0x24, 0x75, 0xc6, 0x1b, 0x90, 0x25, 0xf9, 0xdd, 0x93, 0x87, 0x52, 0xed, 0x1d, 0x03, 0xb1,
	0x40, 0xf8, 0x0d, 0xd5, 0xa9, 0xed, 0x63, 0x1e, 0x93, 0xac, 0x4f, 0x7a, 0x27, 0x56, 0x8e, 0x73,
	0x98, 0xbe, 0x53, 0xc3, 0x69, 0x20, 0xff, 0x66, 0x64, 0x10, 0x6e, 0x9e, 0x96, 0x34, 0xdb, 0x1d,
	0xa0, 0xf3, 0x5f, 0xb2, 0x55, 0xd7, 0x8e, 0xa7, 0x44, 0x2a, 0xba, 0x6d, 0x65, 0x10, 0xe8, 0xbd,
	0x95, 0x6c, 0xc1, 0x09, 0x2e, 0x59, 0xa0, 0x9c, 0x4d, 0x4e, 0x75, 0xba, 0xe0, 0x1a, 0xeb, 0xfc,
	0x8d, 0x6c, 0x41, 0x0d, 0xa9, 0xdf, 0x82, 0xda, 0x00, 0x60, 0xe4, 0xed, 0x5e, 0xce, 0x55, 0x9a,
	0x34, 0x3e, 0x67, 0x54, 0xcc, 0x34, 0x75, 0x42, 0x3c, 0xe7, 0xf2, 0x5c, 0x61, 0xce, 0x61, 0x38,
	0xb0, 0xf0, 0x4e, 0x7f, 0x39, 0xba, 0xa6, 0xed, 0xda, 0xf3, 0x35, 0xe6, 0x7b, 0xea, 0x0b, 0x54,
	0x41, 0x39, 0x82, 0xe4, 0x00, 0xe5, 0xa8, 0x3f, 0x00, 0x18, 0x79, 0x57, 0xf5, 0x5e, 0x6a, 0x9e,
	0x7a, 0x24, 0xd2, 0x7b, 0xf5, 0x23, 0xfa, 0xde, 0x2b, 0x4f, 0x02, 0xc9, 0xf7, 0xd4, 0xb9, 0xa6,
	0xa8, 0x89, 0x13, 0xbe, 0x08, 0x59, 0x7c, 0xec, 0x12, 0xdd, 0x48, 0x19, 0x46, 0x7f, 0xae, 0x21,
	0x28, 0x50, 0x7d, 0x3f, 0x32, 0x48, 0x68, 0x0e, 0xc1, 0x56, 0xbf, 0x1a, 0xb0, 0x4a, 0x93, 0xd9,
	0xf3, 0xff, 0xd6, 0x77, 0x87, 0x0f, 0xd2, 0x94, 0x0f, 0xa5, 0xdb, 0xa2, 0xc9, 0x81, 0x3b, 0x2d,
	0x24, 0xf5, 0xaa, 0x81, 0x6b, 0x33, 0x2c, 0xe5, 0x71, 0xb2, 0x20, 0xe5, 0x75, 0x01, 0xc0, 0xc8,
	0x87, 0xdd, 0xc6, 0x89, 0x4b, 0x6a, 0xcb, 0x4a, 0x3b, 0x94, 0xa2, 0xc5, 0x82, 0x3a, 0x0b, 0x16,
	0x3a, 0x3e, 0xb0, 0xc6, 0x09, 0x03, 0x8b, 0x1a, 0x27, 0x9c, 0x07, 0x2e, 0x3e, 0x52, 0x15, 0x67,
	0x32, 0x9c, 0x09, 0x59, 0x30, 0x9e, 0xb2, 0x48, 0xc5, 0xe9, 0x23, 0xf4, 0x15, 0x27, 0x07, 0x02,
	0xbd, 0x8f, 0xbb, 0x15, 0x6e, 0xc6, 0xaa, 0xd4, 0xe2, 0xcb, 0x94, 0x09, 0x2e, 0xf1, 0x0a, 0x97,
	0x25, 0x0a, 0x2b, 0x1c, 0x04, 0x81, 0xde, 0x27, 0xbd, 0xc2, 0x12, 0x53, 0x71, 0x1e, 0x16, 0x9f,
	0xed, 0x79, 0xae, 0xb0, 0xb0, 0x60, 0x38, 0xb0, 0xf0, 0x69, 0x64, 0x90, 0xa6, 0x79, 0xca, 0x21,
	0x31, 0x25, 0x02, 0x56, 0x63, 0x87, 0xf9, 0x8c, 0x55, 0x21, 0xf9, 0xb6, 0x13, 0x3c, 0x4f, 0x15,
	0xb7, 0xac, 0x84, 0x01, 0xa5, 0xcf, 0x7a, 0x2f, 0xdb, 0xbd, 0x21, 0x62, 0x0d, 0x4b, 0x14, 0x9c,
	0xdc, 0x79, 0xae, 0xf0, 0x65, 0x31, 0x1c, 0x58, 0xf8, 0x3c, 0x69, 0x64, 0x0e, 0x0a, 0xc7, 0xe5,
	0xe3, 0xbe, 0x5f, 0x6f, 0x8a, 0xc5, 0x8a, 0x68, 0xfb, 0x82, 0xd7, 0x58, 0x8b, 0xba, 0x1c, 0x69,
	0x64, 0x74, 0xa8, 0xbe, 0x91, 0xd1, 0x47, 0x00, 0x2f, 0x5f, 0x24, 0xd3, 0xd1, 0xc1, 0xa6, 0x28,
	0x6f, 0x53, 0xcf, 0x0a, 0x28, 0x97, 0x2a, 0x02, 0x99, 0x0e, 0x94, 0xd3, 0x4f, 0x87, 0x06, 0x07,
	0x16, 0xbe, 0x8c, 0x0c, 0x72, 0x6b, 0xc9, 0x3c, 0x67, 0x8a, 0x06, 0xf3, 0x08, 0x4b, 0x1b, 0x24,
	0xff, 0x76, 0x5a, 0x36, 0xb5, 0x32, 0xb6, 0x9a, 0x10, 0x60, 0xe7, 0xab, 0xcc, 0x31, 0x1b, 0x33,
	0x13, 0x5c, 0x06, 0x94, 0xdb, 0x4c, 0xbf, 0x1b, 0xf2, 0x5c, 0xf1, 0x31, 0x8b, 0xe0, 0xc0, 0xc2,
	0xd7, 0xc9, 0x8c, 0x24, 0xb7, 0x67, 0x88, 0x8d, 0xdd, 0xba, 0x9b, 0x36, 0xbd, 0x95, 0xb1, 0xd5,
	0x84, 0x00, 0x3b, 0xdf, 0x44, 0x06, 0xb9, 0xbd, 0x64, 0x0e, 0x83, 0xcb, 0xc5, 0x69, 0xe1, 0xef,
	0xa5, 0x9c, 0x27, 0xf7, 0x02, 0x63, 0xc5, 0x37, 0x91, 0x00, 0x4e, 0x0d, 0xed, 0x59, 0x55, 0x0c,
	0x70, 0xf4, 0x6d, 0x6f, 0x82, 0x54, 0x40, 0x5c, 0x5b, 0xa6, 0xdc, 0x30, 0x74, 0x05, 0xd7, 0x4f,
	0x10, 0xce, 0x16, 0x4e, 0x90, 0x2e, 0x04, 0xd8, 0xf9, 0xae, 0x7b, 0x77, 0xb9, 0x97, 0x76, 0x9a,
	0x07, 0x9b, 0x7a, 0x5e, 0x52, 0xef, 0xf0, 0xbb, 0xcb, 0x1c, 0x56, 0x78, 0x77, 0x89, 0xd0, 0x40,
	0xfe, 0xfb, 0xa4, 0x9e, 0x58, 0x4c, 0x1e, 0x62, 0x8b, 0xe9, 0xa1, 0x32, 0x4d, 0x67, 0xeb, 0x4c,
	0x4a, 0x97, 0x3b, 0x04, 0xed, 0xb2, 0x50, 0x54, 0x5f, 0x4f, 0xf4, 0x11, 0xc0, 0xcb, 0x0f, 0xc9,
	0xca, 0x54, 0x45, 0xa8, 0x31, 0x93, 0x1f, 0x5a, 0xcb, 0xea, 0x57, 0xa6, 0x20, 0x04, 0xd8, 0xf9,
	0xb1, 0xd7, 0xb0, 0x1e, 0xe6, 0xec, 0x6a, 0xb6, 0xb4, 0xdf, 0xe5, 0x8d, 0x78, 0x0e, 0x3b, 0xf9,
	0xbe, 0x84, 0x37, 0xac, 0x18, 0x59, 0xd8, 0xb0, 0xe2, 0x01, 0xc0, 0xc8, 0x4f, 0xdd, 0xfb, 0x09,
	0x05, 0x57, 0xdb, 0xb3, 0x9e, 0x1b, 0x36, 0x2b, 0x73, 0x0e, 0x7e, 0x3f, 0xd1, 0x4f, 0x15, 0xde,
	0x4f, 0xe4, 0x61, 0xa0, 0xfd, 0x73, 0xd2, 0x45, 0x25, 0x40, 0x9f, 0x5b, 0xa4, 0x8b, 0xc2, 0x41,
	0x7d, 0x17, 0xa5, 0xe3, 0x81, 0x8b, 0x5f, 0x92, 0xba, 0x9a, 0x5c, 0x1e, 0xf7, 0x99, 0xc8, 0x27,
	0x3e, 0xca, 0xe9, 0xeb, 0xaa, 0x06, 0x07, 0x16, 0x7e, 0x55, 0xfb, 0xb4, 0x2e, 0x5d, 0x7b, 0xbe,
	0xdf, 0x40, 0x7e, 0x62, 0x31, 0x4c, 0xbf, 0x4f, 0x71, 0x1a, 0xc8, 0xff, 0xd6, 0x4b, 0x46, 0x4b,
	0xb8, 0xdc, 0xe9, 0xb7, 0x80, 0x26, 0x23, 0x46, 0x16, 0x26, 0x23, 0x1e, 0x00, 0x8c, 0xfc, 0xae,
	0x92, 0x51, 0xd5, 0x36, 0xf5, 0x0d, 0xa4, 0xe2, 0xb9, 0xf6, 0xfc, 0x64, 0x0b, 0x49, 0x46, 0x84,
	0xd2, 0x27, 0x23, 0x0a, 0x03, 0xed, 0x3f, 0x92, 0x34, 0x48, 0x49, 0xc7, 0x15, 0xfc, 0x00, 0xa3,
	0x81, 0x9c, 0x65, 0x14, 0xeb, 0xbf, 0x50, 0x4e, 0x9f, 0x06, 0x1a, 0x1c, 0x58, 0xf8, 0x33, 0x32,
	0xc8, 0x9d, 0x25, 0x73, 0x24, 0xfe, 0x0e, 0x35, 0x39, 0x57, 0xa1, 0x5c, 0xb3, 0x33, 0x2e, 0xcb,
	0x67, 0xd9, 0x0a, 0x21, 0xa9, 0xb1, 0xcb, 0xff, 0x41, 0x24, 0xf0, 0xf8, 0x57, 0xef, 0x97, 0x42,
	0xcb, 0x13, 0xb3, 0x34, 0x9b, 0xd9, 0x73, 0x0e, 0xfe, 0x4b, 0x21, 0x02, 0x16, 0xfe, 0x52, 0x88,
	0xf2, 0xc0, 0xc5, 0xf2, 0xdd, 0xc6, 0xf0, 0xd0, 0xc9, 0xe5, 0x91, 0x75, 0x2a, 0x74, 0x57, 0x1c,
	0x7a, 0xcb, 0xf2, 0x88, 0xe1, 0x88, 0xbd, 0x47, 0xcd, 0x21, 0x5b, 0xb4, 0xca, 0xc7, 0xdb, 0x8b,
	0x94, 0x97, 0xa5, 0x54, 0x3f, 0xd3, 0x97, 0xa9, 0xef, 0x1e, 0xbd, 0xc2, 0x11, 0x1e, 0xe5, 0x4e,
	0xf9, 0xd2, 0x31, 0x29, 0xcb, 0xb6, 0x68, 0x8d, 0xc6, 0x8f, 0x6c, 0xe1, 0x8d, 0x52, 0xdf, 0x1f,
	0xed, 0xff, 0x67, 0x04, 0x57, 0x66, 0xff, 0xe7, 0x2e, 0xe3, 0x3f, 0xb5, 0x6a, 0x65, 0xf6, 0x7f,
	0x71, 0xc4, 0x9e, 0xbf, 0x03, 0x00, 0x00, 0xff, 0xff, 0x38, 0xb8, 0x52, 0x11, 0x74, 0x20, 0x00,
	0x00,
}
