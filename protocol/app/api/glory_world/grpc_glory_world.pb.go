// Code generated by protoc-gen-go. DO NOT EDIT.
// source: api/glory_world/grpc_glory_world.proto

package glory_world // import "golang.52tt.com/protocol/app/api/glory_world"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/app/api/extension"
import glory_world "golang.52tt.com/protocol/app/glory_world"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GloryWorldLogicClient is the client API for GloryWorldLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GloryWorldLogicClient interface {
	GetGloryEnterInfo(ctx context.Context, in *glory_world.GetGloryEnterInfoRequest, opts ...grpc.CallOption) (*glory_world.GetGloryEnterInfoResponse, error)
	ReceiveReward(ctx context.Context, in *glory_world.ReceiveRewardRequest, opts ...grpc.CallOption) (*glory_world.ReceiveRewardResponse, error)
	GetFloatingLayerInfo(ctx context.Context, in *glory_world.GetFloatingLayerInfoRequest, opts ...grpc.CallOption) (*glory_world.GetFloatingLayerInfoResponse, error)
}

type gloryWorldLogicClient struct {
	cc *grpc.ClientConn
}

func NewGloryWorldLogicClient(cc *grpc.ClientConn) GloryWorldLogicClient {
	return &gloryWorldLogicClient{cc}
}

func (c *gloryWorldLogicClient) GetGloryEnterInfo(ctx context.Context, in *glory_world.GetGloryEnterInfoRequest, opts ...grpc.CallOption) (*glory_world.GetGloryEnterInfoResponse, error) {
	out := new(glory_world.GetGloryEnterInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.glory_world.GloryWorldLogic/GetGloryEnterInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldLogicClient) ReceiveReward(ctx context.Context, in *glory_world.ReceiveRewardRequest, opts ...grpc.CallOption) (*glory_world.ReceiveRewardResponse, error) {
	out := new(glory_world.ReceiveRewardResponse)
	err := c.cc.Invoke(ctx, "/ga.api.glory_world.GloryWorldLogic/ReceiveReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gloryWorldLogicClient) GetFloatingLayerInfo(ctx context.Context, in *glory_world.GetFloatingLayerInfoRequest, opts ...grpc.CallOption) (*glory_world.GetFloatingLayerInfoResponse, error) {
	out := new(glory_world.GetFloatingLayerInfoResponse)
	err := c.cc.Invoke(ctx, "/ga.api.glory_world.GloryWorldLogic/GetFloatingLayerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GloryWorldLogicServer is the server API for GloryWorldLogic service.
type GloryWorldLogicServer interface {
	GetGloryEnterInfo(context.Context, *glory_world.GetGloryEnterInfoRequest) (*glory_world.GetGloryEnterInfoResponse, error)
	ReceiveReward(context.Context, *glory_world.ReceiveRewardRequest) (*glory_world.ReceiveRewardResponse, error)
	GetFloatingLayerInfo(context.Context, *glory_world.GetFloatingLayerInfoRequest) (*glory_world.GetFloatingLayerInfoResponse, error)
}

func RegisterGloryWorldLogicServer(s *grpc.Server, srv GloryWorldLogicServer) {
	s.RegisterService(&_GloryWorldLogic_serviceDesc, srv)
}

func _GloryWorldLogic_GetGloryEnterInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(glory_world.GetGloryEnterInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldLogicServer).GetGloryEnterInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.glory_world.GloryWorldLogic/GetGloryEnterInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldLogicServer).GetGloryEnterInfo(ctx, req.(*glory_world.GetGloryEnterInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldLogic_ReceiveReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(glory_world.ReceiveRewardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldLogicServer).ReceiveReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.glory_world.GloryWorldLogic/ReceiveReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldLogicServer).ReceiveReward(ctx, req.(*glory_world.ReceiveRewardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GloryWorldLogic_GetFloatingLayerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(glory_world.GetFloatingLayerInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GloryWorldLogicServer).GetFloatingLayerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ga.api.glory_world.GloryWorldLogic/GetFloatingLayerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GloryWorldLogicServer).GetFloatingLayerInfo(ctx, req.(*glory_world.GetFloatingLayerInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _GloryWorldLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ga.api.glory_world.GloryWorldLogic",
	HandlerType: (*GloryWorldLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGloryEnterInfo",
			Handler:    _GloryWorldLogic_GetGloryEnterInfo_Handler,
		},
		{
			MethodName: "ReceiveReward",
			Handler:    _GloryWorldLogic_ReceiveReward_Handler,
		},
		{
			MethodName: "GetFloatingLayerInfo",
			Handler:    _GloryWorldLogic_GetFloatingLayerInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/glory_world/grpc_glory_world.proto",
}

func init() {
	proto.RegisterFile("api/glory_world/grpc_glory_world.proto", fileDescriptor_grpc_glory_world_f056eaed12b388df)
}

var fileDescriptor_grpc_glory_world_f056eaed12b388df = []byte{
	// 318 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x92, 0x41, 0x4b, 0x3b, 0x31,
	0x10, 0xc5, 0x61, 0xfb, 0xe7, 0x4f, 0x09, 0x88, 0x34, 0x88, 0x87, 0x95, 0x42, 0x11, 0x15, 0x45,
	0x9b, 0x85, 0x8a, 0x20, 0x78, 0x53, 0xb4, 0x08, 0x3d, 0xc8, 0x5e, 0x14, 0x2f, 0x25, 0xa6, 0xd3,
	0x10, 0x48, 0x33, 0x69, 0x36, 0xb5, 0x16, 0x2f, 0xa5, 0x2a, 0x82, 0x1f, 0xa2, 0x07, 0x3f, 0xa9,
	0xec, 0xd6, 0xe2, 0x6e, 0x2b, 0xd8, 0xdb, 0x30, 0xef, 0xfd, 0xe6, 0x25, 0x99, 0x90, 0x3d, 0x6e,
	0x55, 0x24, 0x35, 0xba, 0x51, 0x7b, 0x88, 0x4e, 0x77, 0x22, 0xe9, 0xac, 0x68, 0xe7, 0x1a, 0xcc,
	0x3a, 0xf4, 0x48, 0xa9, 0xe4, 0x8c, 0x5b, 0xc5, 0x72, 0x4a, 0x58, 0x2d, 0x70, 0x8b, 0x48, 0x58,
	0x4d, 0x47, 0xc3, 0x93, 0x07, 0x93, 0x28, 0x34, 0x3f, 0xd5, 0x4c, 0x6e, 0xbc, 0x97, 0xc8, 0x7a,
	0x33, 0x85, 0x6e, 0x53, 0xa6, 0x85, 0x52, 0x09, 0xea, 0x48, 0xa5, 0x09, 0x3e, 0xeb, 0x5e, 0x1a,
	0x0f, 0xee, 0xda, 0x74, 0x91, 0xee, 0x33, 0xc9, 0xf3, 0xb9, 0x6c, 0xc9, 0x12, 0x43, 0x7f, 0x00,
	0x89, 0x0f, 0x0f, 0x56, 0x70, 0x26, 0x16, 0x4d, 0x02, 0xdb, 0xe5, 0xc9, 0xb8, 0xf6, 0xaf, 0xfc,
	0x32, 0x0d, 0x68, 0x97, 0xac, 0xc5, 0x20, 0x40, 0x3d, 0x42, 0x0c, 0x43, 0xee, 0x3a, 0x74, 0x67,
	0x71, 0x4a, 0x41, 0x9e, 0x67, 0xed, 0xfe, 0xe1, 0x2a, 0xe4, 0xbc, 0x4e, 0x03, 0xfa, 0x4c, 0x36,
	0x9a, 0xe0, 0xaf, 0x34, 0x72, 0xaf, 0x8c, 0x6c, 0xf1, 0xd1, 0xf7, 0xf5, 0x0e, 0x7f, 0x39, 0xf4,
	0x92, 0x6b, 0x9e, 0x7a, 0xb4, 0x9a, 0xb9, 0x10, 0xfe, 0x36, 0x0d, 0xc2, 0xad, 0xc9, 0xb8, 0x56,
	0xc9, 0xb8, 0x7a, 0xc6, 0xd5, 0x75, 0xfa, 0xde, 0x1f, 0xe3, 0x5a, 0x20, 0xf1, 0xfc, 0x8e, 0x6c,
	0x0a, 0xec, 0xb1, 0xfe, 0x60, 0xc8, 0x0d, 0xf3, 0x7e, 0xb6, 0x9f, 0x74, 0xdb, 0xf7, 0xa7, 0x12,
	0x35, 0x37, 0x92, 0x9d, 0x34, 0xbc, 0x67, 0x02, 0x7b, 0x51, 0x26, 0x09, 0xd4, 0x11, 0xb7, 0x36,
	0x5a, 0xf8, 0x3a, 0x67, 0xb9, 0xfa, 0x33, 0x28, 0xc5, 0x37, 0x17, 0x0f, 0xff, 0x33, 0xff, 0xf1,
	0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0xb3, 0x81, 0x22, 0xe2, 0x66, 0x02, 0x00, 0x00,
}
