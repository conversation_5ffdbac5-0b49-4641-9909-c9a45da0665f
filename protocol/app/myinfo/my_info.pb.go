// Code generated by protoc-gen-gogo.
// source: my_info.proto
// DO NOT EDIT!

/*
	Package myinfo is a generated protocol buffer package.

	It is generated from these files:
		my_info.proto

	It has these top-level messages:
		ModifyUserNameReq
		ModifyUserNameResp
		ModifyNicknameReq
		ModifyNicknameResp
		ModifySignatureReq
		ModifySignatureResp
		BatGetUserGrowInfoReq
		BatGetUserGrowInfoResp
		SetMyMedalTaillightReq
		SetMyMedalTaillightResp
		UpdatePhotoAlbumReq
		UpdatePhotoAlbumResp
		GetPhotoAlbumReq
		PhotoAlbumNewURLInfo
		GetPhotoAlbumResp
		SetUserHeadwearReq
		SetUserHeadwearResp
		GetUserHeadwearReq
		GetUserHeadwearResp
		RemoveUserHeadwearReq
		RemoveUserHeadwearResp
		GetUserCertificationReq
		GetUserCertificationResp
		UserCertificationInfo
		GetUserCertifyListReq
		GetUserCertifyListResp
		SetUserWearCertificationReq
		SetUserWearCertificationResp
		UserContractInfo
		GetUserContractInfoReq
		GetUserContractInfoResp
		AgreeUserContractReq
		AgreeUserContractResp
		BatchGetNobilityInfosReq
		BatchGetNobilityInfosResp
		NobilityCycleInfo
		GetNobilityInfoReq
		GetNobilityInfoResp
		GetNobilityDetailInfoReq
		GetNobilityDetailInfoResp
*/
package myinfo

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type LocationType int32

const (
	LocationType_Personal_Page    LocationType = 0
	LocationType_Channel          LocationType = 1
	LocationType_Anchor_Card      LocationType = 2
	LocationType_PersonalityDress LocationType = 3
)

var LocationType_name = map[int32]string{
	0: "Personal_Page",
	1: "Channel",
	2: "Anchor_Card",
	3: "PersonalityDress",
}
var LocationType_value = map[string]int32{
	"Personal_Page":    0,
	"Channel":          1,
	"Anchor_Card":      2,
	"PersonalityDress": 3,
}

func (x LocationType) Enum() *LocationType {
	p := new(LocationType)
	*p = x
	return p
}
func (x LocationType) String() string {
	return proto.EnumName(LocationType_name, int32(x))
}
func (x *LocationType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(LocationType_value, data, "LocationType")
	if err != nil {
		return err
	}
	*x = LocationType(value)
	return nil
}
func (LocationType) EnumDescriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{0} }

type UserContractInfo_UserContractType int32

const (
	UserContractInfo_INVALID_CONTRACT_TYPE UserContractInfo_UserContractType = 0
	UserContractInfo_TT_SERVICE_CONTRACT   UserContractInfo_UserContractType = 1
)

var UserContractInfo_UserContractType_name = map[int32]string{
	0: "INVALID_CONTRACT_TYPE",
	1: "TT_SERVICE_CONTRACT",
}
var UserContractInfo_UserContractType_value = map[string]int32{
	"INVALID_CONTRACT_TYPE": 0,
	"TT_SERVICE_CONTRACT":   1,
}

func (x UserContractInfo_UserContractType) Enum() *UserContractInfo_UserContractType {
	p := new(UserContractInfo_UserContractType)
	*p = x
	return p
}
func (x UserContractInfo_UserContractType) String() string {
	return proto.EnumName(UserContractInfo_UserContractType_name, int32(x))
}
func (x *UserContractInfo_UserContractType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(UserContractInfo_UserContractType_value, data, "UserContractInfo_UserContractType")
	if err != nil {
		return err
	}
	*x = UserContractInfo_UserContractType(value)
	return nil
}
func (UserContractInfo_UserContractType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMyInfo, []int{28, 0}
}

// 修改手游宝账号
type ModifyUserNameReq struct {
	BaseReq     *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	NewUserName string      `protobuf:"bytes,2,req,name=new_user_name,json=newUserName" json:"new_user_name"`
}

func (m *ModifyUserNameReq) Reset()                    { *m = ModifyUserNameReq{} }
func (m *ModifyUserNameReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyUserNameReq) ProtoMessage()               {}
func (*ModifyUserNameReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{0} }

func (m *ModifyUserNameReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ModifyUserNameReq) GetNewUserName() string {
	if m != nil {
		return m.NewUserName
	}
	return ""
}

type ModifyUserNameResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ModifyUserNameResp) Reset()                    { *m = ModifyUserNameResp{} }
func (m *ModifyUserNameResp) String() string            { return proto.CompactTextString(m) }
func (*ModifyUserNameResp) ProtoMessage()               {}
func (*ModifyUserNameResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{1} }

func (m *ModifyUserNameResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 修改昵称
type ModifyNicknameReq struct {
	BaseReq     *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	NewNickname string      `protobuf:"bytes,2,req,name=new_nickname,json=newNickname" json:"new_nickname"`
	PrefixValid bool        `protobuf:"varint,3,opt,name=prefix_valid,json=prefixValid" json:"prefix_valid"`
}

func (m *ModifyNicknameReq) Reset()                    { *m = ModifyNicknameReq{} }
func (m *ModifyNicknameReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyNicknameReq) ProtoMessage()               {}
func (*ModifyNicknameReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{2} }

func (m *ModifyNicknameReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ModifyNicknameReq) GetNewNickname() string {
	if m != nil {
		return m.NewNickname
	}
	return ""
}

func (m *ModifyNicknameReq) GetPrefixValid() bool {
	if m != nil {
		return m.PrefixValid
	}
	return false
}

type ModifyNicknameResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ModifyNicknameResp) Reset()                    { *m = ModifyNicknameResp{} }
func (m *ModifyNicknameResp) String() string            { return proto.CompactTextString(m) }
func (*ModifyNicknameResp) ProtoMessage()               {}
func (*ModifyNicknameResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{3} }

func (m *ModifyNicknameResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 修改签名
type ModifySignatureReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	NewSignature string      `protobuf:"bytes,2,req,name=new_signature,json=newSignature" json:"new_signature"`
}

func (m *ModifySignatureReq) Reset()                    { *m = ModifySignatureReq{} }
func (m *ModifySignatureReq) String() string            { return proto.CompactTextString(m) }
func (*ModifySignatureReq) ProtoMessage()               {}
func (*ModifySignatureReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{4} }

func (m *ModifySignatureReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ModifySignatureReq) GetNewSignature() string {
	if m != nil {
		return m.NewSignature
	}
	return ""
}

type ModifySignatureResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ModifySignatureResp) Reset()                    { *m = ModifySignatureResp{} }
func (m *ModifySignatureResp) String() string            { return proto.CompactTextString(m) }
func (*ModifySignatureResp) ProtoMessage()               {}
func (*ModifySignatureResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{5} }

func (m *ModifySignatureResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 批量获取用户grow info
type BatGetUserGrowInfoReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	UidList []uint32    `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatGetUserGrowInfoReq) Reset()                    { *m = BatGetUserGrowInfoReq{} }
func (m *BatGetUserGrowInfoReq) String() string            { return proto.CompactTextString(m) }
func (*BatGetUserGrowInfoReq) ProtoMessage()               {}
func (*BatGetUserGrowInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{6} }

func (m *BatGetUserGrowInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatGetUserGrowInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatGetUserGrowInfoResp struct {
	BaseResp     *ga.BaseResp   `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GrowInfoList []*ga.GrowInfo `protobuf:"bytes,2,rep,name=grow_info_list,json=growInfoList" json:"grow_info_list,omitempty"`
}

func (m *BatGetUserGrowInfoResp) Reset()                    { *m = BatGetUserGrowInfoResp{} }
func (m *BatGetUserGrowInfoResp) String() string            { return proto.CompactTextString(m) }
func (*BatGetUserGrowInfoResp) ProtoMessage()               {}
func (*BatGetUserGrowInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{7} }

func (m *BatGetUserGrowInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatGetUserGrowInfoResp) GetGrowInfoList() []*ga.GrowInfo {
	if m != nil {
		return m.GrowInfoList
	}
	return nil
}

// 设置勋章尾灯
type SetMyMedalTaillightReq struct {
	BaseReq            *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	MedalTaillightList []uint32    `protobuf:"varint,2,rep,name=medal_taillight_list,json=medalTaillightList" json:"medal_taillight_list,omitempty"`
}

func (m *SetMyMedalTaillightReq) Reset()                    { *m = SetMyMedalTaillightReq{} }
func (m *SetMyMedalTaillightReq) String() string            { return proto.CompactTextString(m) }
func (*SetMyMedalTaillightReq) ProtoMessage()               {}
func (*SetMyMedalTaillightReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{8} }

func (m *SetMyMedalTaillightReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetMyMedalTaillightReq) GetMedalTaillightList() []uint32 {
	if m != nil {
		return m.MedalTaillightList
	}
	return nil
}

type SetMyMedalTaillightResp struct {
	BaseResp           *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	MedalTaillightList []uint32     `protobuf:"varint,2,rep,name=medal_taillight_list,json=medalTaillightList" json:"medal_taillight_list,omitempty"`
}

func (m *SetMyMedalTaillightResp) Reset()                    { *m = SetMyMedalTaillightResp{} }
func (m *SetMyMedalTaillightResp) String() string            { return proto.CompactTextString(m) }
func (*SetMyMedalTaillightResp) ProtoMessage()               {}
func (*SetMyMedalTaillightResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{9} }

func (m *SetMyMedalTaillightResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SetMyMedalTaillightResp) GetMedalTaillightList() []uint32 {
	if m != nil {
		return m.MedalTaillightList
	}
	return nil
}

type UpdatePhotoAlbumReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid           uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
	ImgKeyList    []string    `protobuf:"bytes,3,rep,name=img_key_list,json=imgKeyList" json:"img_key_list,omitempty"`
	NewImgKeyList []string    `protobuf:"bytes,4,rep,name=new_img_key_list,json=newImgKeyList" json:"new_img_key_list,omitempty"`
}

func (m *UpdatePhotoAlbumReq) Reset()                    { *m = UpdatePhotoAlbumReq{} }
func (m *UpdatePhotoAlbumReq) String() string            { return proto.CompactTextString(m) }
func (*UpdatePhotoAlbumReq) ProtoMessage()               {}
func (*UpdatePhotoAlbumReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{10} }

func (m *UpdatePhotoAlbumReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdatePhotoAlbumReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdatePhotoAlbumReq) GetImgKeyList() []string {
	if m != nil {
		return m.ImgKeyList
	}
	return nil
}

func (m *UpdatePhotoAlbumReq) GetNewImgKeyList() []string {
	if m != nil {
		return m.NewImgKeyList
	}
	return nil
}

type UpdatePhotoAlbumResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *UpdatePhotoAlbumResp) Reset()                    { *m = UpdatePhotoAlbumResp{} }
func (m *UpdatePhotoAlbumResp) String() string            { return proto.CompactTextString(m) }
func (*UpdatePhotoAlbumResp) ProtoMessage()               {}
func (*UpdatePhotoAlbumResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{11} }

func (m *UpdatePhotoAlbumResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetPhotoAlbumReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid     uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetPhotoAlbumReq) Reset()                    { *m = GetPhotoAlbumReq{} }
func (m *GetPhotoAlbumReq) String() string            { return proto.CompactTextString(m) }
func (*GetPhotoAlbumReq) ProtoMessage()               {}
func (*GetPhotoAlbumReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{12} }

func (m *GetPhotoAlbumReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPhotoAlbumReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type PhotoAlbumNewURLInfo struct {
	Key string `protobuf:"bytes,1,req,name=key" json:"key"`
	Url string `protobuf:"bytes,2,req,name=url" json:"url"`
}

func (m *PhotoAlbumNewURLInfo) Reset()                    { *m = PhotoAlbumNewURLInfo{} }
func (m *PhotoAlbumNewURLInfo) String() string            { return proto.CompactTextString(m) }
func (*PhotoAlbumNewURLInfo) ProtoMessage()               {}
func (*PhotoAlbumNewURLInfo) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{13} }

func (m *PhotoAlbumNewURLInfo) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *PhotoAlbumNewURLInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type GetPhotoAlbumResp struct {
	BaseResp     *ga.BaseResp            `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ImgKeyList   []string                `protobuf:"bytes,2,rep,name=img_key_list,json=imgKeyList" json:"img_key_list,omitempty"`
	ImgUrlList   []string                `protobuf:"bytes,3,rep,name=img_url_list,json=imgUrlList" json:"img_url_list,omitempty"`
	AllPhotoList []*PhotoAlbumNewURLInfo `protobuf:"bytes,4,rep,name=all_photo_list,json=allPhotoList" json:"all_photo_list,omitempty"`
}

func (m *GetPhotoAlbumResp) Reset()                    { *m = GetPhotoAlbumResp{} }
func (m *GetPhotoAlbumResp) String() string            { return proto.CompactTextString(m) }
func (*GetPhotoAlbumResp) ProtoMessage()               {}
func (*GetPhotoAlbumResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{14} }

func (m *GetPhotoAlbumResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPhotoAlbumResp) GetImgKeyList() []string {
	if m != nil {
		return m.ImgKeyList
	}
	return nil
}

func (m *GetPhotoAlbumResp) GetImgUrlList() []string {
	if m != nil {
		return m.ImgUrlList
	}
	return nil
}

func (m *GetPhotoAlbumResp) GetAllPhotoList() []*PhotoAlbumNewURLInfo {
	if m != nil {
		return m.AllPhotoList
	}
	return nil
}

type SetUserHeadwearReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	HeadwearId uint32      `protobuf:"varint,2,req,name=headwear_id,json=headwearId" json:"headwear_id"`
	CpUid      uint32      `protobuf:"varint,3,opt,name=cp_uid,json=cpUid" json:"cp_uid"`
}

func (m *SetUserHeadwearReq) Reset()                    { *m = SetUserHeadwearReq{} }
func (m *SetUserHeadwearReq) String() string            { return proto.CompactTextString(m) }
func (*SetUserHeadwearReq) ProtoMessage()               {}
func (*SetUserHeadwearReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{15} }

func (m *SetUserHeadwearReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserHeadwearReq) GetHeadwearId() uint32 {
	if m != nil {
		return m.HeadwearId
	}
	return 0
}

func (m *SetUserHeadwearReq) GetCpUid() uint32 {
	if m != nil {
		return m.CpUid
	}
	return 0
}

type SetUserHeadwearResp struct {
	BaseResp     *ga.BaseResp         `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	HeadwearInfo *ga.UserHeadwearInfo `protobuf:"bytes,2,req,name=headwear_info,json=headwearInfo" json:"headwear_info,omitempty"`
}

func (m *SetUserHeadwearResp) Reset()                    { *m = SetUserHeadwearResp{} }
func (m *SetUserHeadwearResp) String() string            { return proto.CompactTextString(m) }
func (*SetUserHeadwearResp) ProtoMessage()               {}
func (*SetUserHeadwearResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{16} }

func (m *SetUserHeadwearResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SetUserHeadwearResp) GetHeadwearInfo() *ga.UserHeadwearInfo {
	if m != nil {
		return m.HeadwearInfo
	}
	return nil
}

type GetUserHeadwearReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetUserHeadwearReq) Reset()                    { *m = GetUserHeadwearReq{} }
func (m *GetUserHeadwearReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserHeadwearReq) ProtoMessage()               {}
func (*GetUserHeadwearReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{17} }

func (m *GetUserHeadwearReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserHeadwearResp struct {
	BaseResp        *ga.BaseResp           `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	HeadwearList    []*ga.UserHeadwearInfo `protobuf:"bytes,2,rep,name=headwear_list,json=headwearList" json:"headwear_list,omitempty"`
	HeadwearIdInuse uint32                 `protobuf:"varint,3,req,name=headwear_id_inuse,json=headwearIdInuse" json:"headwear_id_inuse"`
	CpUidInuse      uint32                 `protobuf:"varint,4,opt,name=cp_uid_inuse,json=cpUidInuse" json:"cp_uid_inuse"`
}

func (m *GetUserHeadwearResp) Reset()                    { *m = GetUserHeadwearResp{} }
func (m *GetUserHeadwearResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserHeadwearResp) ProtoMessage()               {}
func (*GetUserHeadwearResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{18} }

func (m *GetUserHeadwearResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserHeadwearResp) GetHeadwearList() []*ga.UserHeadwearInfo {
	if m != nil {
		return m.HeadwearList
	}
	return nil
}

func (m *GetUserHeadwearResp) GetHeadwearIdInuse() uint32 {
	if m != nil {
		return m.HeadwearIdInuse
	}
	return 0
}

func (m *GetUserHeadwearResp) GetCpUidInuse() uint32 {
	if m != nil {
		return m.CpUidInuse
	}
	return 0
}

type RemoveUserHeadwearReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	HeadwearId uint32      `protobuf:"varint,2,req,name=headwear_id,json=headwearId" json:"headwear_id"`
}

func (m *RemoveUserHeadwearReq) Reset()                    { *m = RemoveUserHeadwearReq{} }
func (m *RemoveUserHeadwearReq) String() string            { return proto.CompactTextString(m) }
func (*RemoveUserHeadwearReq) ProtoMessage()               {}
func (*RemoveUserHeadwearReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{19} }

func (m *RemoveUserHeadwearReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RemoveUserHeadwearReq) GetHeadwearId() uint32 {
	if m != nil {
		return m.HeadwearId
	}
	return 0
}

type RemoveUserHeadwearResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *RemoveUserHeadwearResp) Reset()                    { *m = RemoveUserHeadwearResp{} }
func (m *RemoveUserHeadwearResp) String() string            { return proto.CompactTextString(m) }
func (*RemoveUserHeadwearResp) ProtoMessage()               {}
func (*RemoveUserHeadwearResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{20} }

func (m *RemoveUserHeadwearResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 用户官方认证 cmd 231
type GetUserCertificationReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetAccount string      `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
	RequestType   uint32      `protobuf:"varint,3,opt,name=request_type,json=requestType" json:"request_type"`
}

func (m *GetUserCertificationReq) Reset()                    { *m = GetUserCertificationReq{} }
func (m *GetUserCertificationReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserCertificationReq) ProtoMessage()               {}
func (*GetUserCertificationReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{21} }

func (m *GetUserCertificationReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserCertificationReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *GetUserCertificationReq) GetRequestType() uint32 {
	if m != nil {
		return m.RequestType
	}
	return 0
}

type GetUserCertificationResp struct {
	BaseResp                  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Title                     string       `protobuf:"bytes,2,req,name=title" json:"title"`
	Intro                     string       `protobuf:"bytes,3,req,name=intro" json:"intro"`
	Style                     string       `protobuf:"bytes,4,opt,name=style" json:"style"`
	CertifySpecialEffectIcon  string       `protobuf:"bytes,5,opt,name=certify_special_effect_icon,json=certifySpecialEffectIcon" json:"certify_special_effect_icon"`
	CertifySpecialEffectTitle string       `protobuf:"bytes,6,opt,name=certify_special_effect_title,json=certifySpecialEffectTitle" json:"certify_special_effect_title"`
}

func (m *GetUserCertificationResp) Reset()                    { *m = GetUserCertificationResp{} }
func (m *GetUserCertificationResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserCertificationResp) ProtoMessage()               {}
func (*GetUserCertificationResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{22} }

func (m *GetUserCertificationResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserCertificationResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetUserCertificationResp) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *GetUserCertificationResp) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *GetUserCertificationResp) GetCertifySpecialEffectIcon() string {
	if m != nil {
		return m.CertifySpecialEffectIcon
	}
	return ""
}

func (m *GetUserCertificationResp) GetCertifySpecialEffectTitle() string {
	if m != nil {
		return m.CertifySpecialEffectTitle
	}
	return ""
}

type UserCertificationInfo struct {
	Id                        uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Title                     string `protobuf:"bytes,2,req,name=title" json:"title"`
	Intro                     string `protobuf:"bytes,3,req,name=intro" json:"intro"`
	Style                     string `protobuf:"bytes,4,req,name=style" json:"style"`
	BeginTs                   uint64 `protobuf:"varint,5,req,name=begin_ts,json=beginTs" json:"begin_ts"`
	EndTs                     uint64 `protobuf:"varint,6,req,name=end_ts,json=endTs" json:"end_ts"`
	IsUse                     bool   `protobuf:"varint,7,req,name=is_use,json=isUse" json:"is_use"`
	CertifySpecialEffectIcon  string `protobuf:"bytes,8,opt,name=certify_special_effect_icon,json=certifySpecialEffectIcon" json:"certify_special_effect_icon"`
	CertifySpecialEffectTitle string `protobuf:"bytes,9,opt,name=certify_special_effect_title,json=certifySpecialEffectTitle" json:"certify_special_effect_title"`
}

func (m *UserCertificationInfo) Reset()                    { *m = UserCertificationInfo{} }
func (m *UserCertificationInfo) String() string            { return proto.CompactTextString(m) }
func (*UserCertificationInfo) ProtoMessage()               {}
func (*UserCertificationInfo) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{23} }

func (m *UserCertificationInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserCertificationInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *UserCertificationInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *UserCertificationInfo) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

func (m *UserCertificationInfo) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *UserCertificationInfo) GetEndTs() uint64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *UserCertificationInfo) GetIsUse() bool {
	if m != nil {
		return m.IsUse
	}
	return false
}

func (m *UserCertificationInfo) GetCertifySpecialEffectIcon() string {
	if m != nil {
		return m.CertifySpecialEffectIcon
	}
	return ""
}

func (m *UserCertificationInfo) GetCertifySpecialEffectTitle() string {
	if m != nil {
		return m.CertifySpecialEffectTitle
	}
	return ""
}

// 获取用户的认证列表 cmd 233
type GetUserCertifyListReq struct {
	BaseReq     *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetUid   uint32      `protobuf:"varint,2,opt,name=target_uid,json=targetUid" json:"target_uid"`
	RequestType uint32      `protobuf:"varint,3,opt,name=request_type,json=requestType" json:"request_type"`
}

func (m *GetUserCertifyListReq) Reset()                    { *m = GetUserCertifyListReq{} }
func (m *GetUserCertifyListReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserCertifyListReq) ProtoMessage()               {}
func (*GetUserCertifyListReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{24} }

func (m *GetUserCertifyListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserCertifyListReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserCertifyListReq) GetRequestType() uint32 {
	if m != nil {
		return m.RequestType
	}
	return 0
}

type GetUserCertifyListResp struct {
	BaseResp    *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CertifyList []*UserCertificationInfo `protobuf:"bytes,2,rep,name=certify_list,json=certifyList" json:"certify_list,omitempty"`
}

func (m *GetUserCertifyListResp) Reset()                    { *m = GetUserCertifyListResp{} }
func (m *GetUserCertifyListResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserCertifyListResp) ProtoMessage()               {}
func (*GetUserCertifyListResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{25} }

func (m *GetUserCertifyListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserCertifyListResp) GetCertifyList() []*UserCertificationInfo {
	if m != nil {
		return m.CertifyList
	}
	return nil
}

// 设置用户佩戴的认证样式
type SetUserWearCertificationReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Id      uint32      `protobuf:"varint,2,req,name=id" json:"id"`
}

func (m *SetUserWearCertificationReq) Reset()         { *m = SetUserWearCertificationReq{} }
func (m *SetUserWearCertificationReq) String() string { return proto.CompactTextString(m) }
func (*SetUserWearCertificationReq) ProtoMessage()    {}
func (*SetUserWearCertificationReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMyInfo, []int{26}
}

func (m *SetUserWearCertificationReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserWearCertificationReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type SetUserWearCertificationResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *SetUserWearCertificationResp) Reset()         { *m = SetUserWearCertificationResp{} }
func (m *SetUserWearCertificationResp) String() string { return proto.CompactTextString(m) }
func (*SetUserWearCertificationResp) ProtoMessage()    {}
func (*SetUserWearCertificationResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMyInfo, []int{27}
}

func (m *SetUserWearCertificationResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 用户服务协议
type UserContractInfo struct {
	ContractType    uint32 `protobuf:"varint,1,req,name=contract_type,json=contractType" json:"contract_type"`
	ContractUrl     string `protobuf:"bytes,2,req,name=contract_url,json=contractUrl" json:"contract_url"`
	ContractVersion string `protobuf:"bytes,3,req,name=contract_version,json=contractVersion" json:"contract_version"`
	IsAgree         bool   `protobuf:"varint,4,opt,name=is_agree,json=isAgree" json:"is_agree"`
}

func (m *UserContractInfo) Reset()                    { *m = UserContractInfo{} }
func (m *UserContractInfo) String() string            { return proto.CompactTextString(m) }
func (*UserContractInfo) ProtoMessage()               {}
func (*UserContractInfo) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{28} }

func (m *UserContractInfo) GetContractType() uint32 {
	if m != nil {
		return m.ContractType
	}
	return 0
}

func (m *UserContractInfo) GetContractUrl() string {
	if m != nil {
		return m.ContractUrl
	}
	return ""
}

func (m *UserContractInfo) GetContractVersion() string {
	if m != nil {
		return m.ContractVersion
	}
	return ""
}

func (m *UserContractInfo) GetIsAgree() bool {
	if m != nil {
		return m.IsAgree
	}
	return false
}

type GetUserContractInfoReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ContractType uint32      `protobuf:"varint,2,req,name=contract_type,json=contractType" json:"contract_type"`
}

func (m *GetUserContractInfoReq) Reset()                    { *m = GetUserContractInfoReq{} }
func (m *GetUserContractInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserContractInfoReq) ProtoMessage()               {}
func (*GetUserContractInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{29} }

func (m *GetUserContractInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserContractInfoReq) GetContractType() uint32 {
	if m != nil {
		return m.ContractType
	}
	return 0
}

type GetUserContractInfoResp struct {
	BaseResp     *ga.BaseResp      `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ContractInfo *UserContractInfo `protobuf:"bytes,2,opt,name=contract_info,json=contractInfo" json:"contract_info,omitempty"`
}

func (m *GetUserContractInfoResp) Reset()                    { *m = GetUserContractInfoResp{} }
func (m *GetUserContractInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserContractInfoResp) ProtoMessage()               {}
func (*GetUserContractInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{30} }

func (m *GetUserContractInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserContractInfoResp) GetContractInfo() *UserContractInfo {
	if m != nil {
		return m.ContractInfo
	}
	return nil
}

type AgreeUserContractReq struct {
	BaseReq         *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ContractType    uint32      `protobuf:"varint,2,req,name=contract_type,json=contractType" json:"contract_type"`
	ContractVersion string      `protobuf:"bytes,3,req,name=contract_version,json=contractVersion" json:"contract_version"`
}

func (m *AgreeUserContractReq) Reset()                    { *m = AgreeUserContractReq{} }
func (m *AgreeUserContractReq) String() string            { return proto.CompactTextString(m) }
func (*AgreeUserContractReq) ProtoMessage()               {}
func (*AgreeUserContractReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{31} }

func (m *AgreeUserContractReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AgreeUserContractReq) GetContractType() uint32 {
	if m != nil {
		return m.ContractType
	}
	return 0
}

func (m *AgreeUserContractReq) GetContractVersion() string {
	if m != nil {
		return m.ContractVersion
	}
	return ""
}

type AgreeUserContractResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *AgreeUserContractResp) Reset()                    { *m = AgreeUserContractResp{} }
func (m *AgreeUserContractResp) String() string            { return proto.CompactTextString(m) }
func (*AgreeUserContractResp) ProtoMessage()               {}
func (*AgreeUserContractResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{32} }

func (m *AgreeUserContractResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type BatchGetNobilityInfosReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uids     []uint32    `protobuf:"varint,2,rep,name=uids" json:"uids,omitempty"`
	Accounts []string    `protobuf:"bytes,3,rep,name=accounts" json:"accounts,omitempty"`
}

func (m *BatchGetNobilityInfosReq) Reset()                    { *m = BatchGetNobilityInfosReq{} }
func (m *BatchGetNobilityInfosReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetNobilityInfosReq) ProtoMessage()               {}
func (*BatchGetNobilityInfosReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{33} }

func (m *BatchGetNobilityInfosReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchGetNobilityInfosReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *BatchGetNobilityInfosReq) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

type BatchGetNobilityInfosResp struct {
	BaseResp             *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	NobilityInfos        []*ga.NobilityInfo `protobuf:"bytes,2,rep,name=nobility_infos,json=nobilityInfos" json:"nobility_infos,omitempty"`
	AccountNobilityInfos []*ga.NobilityInfo `protobuf:"bytes,3,rep,name=account_nobility_infos,json=accountNobilityInfos" json:"account_nobility_infos,omitempty"`
}

func (m *BatchGetNobilityInfosResp) Reset()                    { *m = BatchGetNobilityInfosResp{} }
func (m *BatchGetNobilityInfosResp) String() string            { return proto.CompactTextString(m) }
func (*BatchGetNobilityInfosResp) ProtoMessage()               {}
func (*BatchGetNobilityInfosResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{34} }

func (m *BatchGetNobilityInfosResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchGetNobilityInfosResp) GetNobilityInfos() []*ga.NobilityInfo {
	if m != nil {
		return m.NobilityInfos
	}
	return nil
}

func (m *BatchGetNobilityInfosResp) GetAccountNobilityInfos() []*ga.NobilityInfo {
	if m != nil {
		return m.AccountNobilityInfos
	}
	return nil
}

// 贵族周期信息
type NobilityCycleInfo struct {
	RemainDays     uint32 `protobuf:"varint,1,req,name=remain_days,json=remainDays" json:"remain_days"`
	FloatCall      string `protobuf:"bytes,2,req,name=float_call,json=floatCall" json:"float_call"`
	FloatPrivilege string `protobuf:"bytes,3,req,name=float_privilege,json=floatPrivilege" json:"float_privilege"`
	NowTs          uint32 `protobuf:"varint,4,req,name=now_ts,json=nowTs" json:"now_ts"`
}

func (m *NobilityCycleInfo) Reset()                    { *m = NobilityCycleInfo{} }
func (m *NobilityCycleInfo) String() string            { return proto.CompactTextString(m) }
func (*NobilityCycleInfo) ProtoMessage()               {}
func (*NobilityCycleInfo) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{35} }

func (m *NobilityCycleInfo) GetRemainDays() uint32 {
	if m != nil {
		return m.RemainDays
	}
	return 0
}

func (m *NobilityCycleInfo) GetFloatCall() string {
	if m != nil {
		return m.FloatCall
	}
	return ""
}

func (m *NobilityCycleInfo) GetFloatPrivilege() string {
	if m != nil {
		return m.FloatPrivilege
	}
	return ""
}

func (m *NobilityCycleInfo) GetNowTs() uint32 {
	if m != nil {
		return m.NowTs
	}
	return 0
}

// 获取贵族相关信息，本来想搞成获取贵族信息的通用接口, 由于实现逻辑问题，这个接口只用来获取贵族保级提醒信息
type GetNobilityInfoReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid     uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetNobilityInfoReq) Reset()                    { *m = GetNobilityInfoReq{} }
func (m *GetNobilityInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetNobilityInfoReq) ProtoMessage()               {}
func (*GetNobilityInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{36} }

func (m *GetNobilityInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNobilityInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetNobilityInfoResp struct {
	BaseResp  *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CycleInfo *NobilityCycleInfo `protobuf:"bytes,2,opt,name=cycle_info,json=cycleInfo" json:"cycle_info,omitempty"`
	IsKeep    bool               `protobuf:"varint,3,opt,name=is_keep,json=isKeep" json:"is_keep"`
}

func (m *GetNobilityInfoResp) Reset()                    { *m = GetNobilityInfoResp{} }
func (m *GetNobilityInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetNobilityInfoResp) ProtoMessage()               {}
func (*GetNobilityInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{37} }

func (m *GetNobilityInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNobilityInfoResp) GetCycleInfo() *NobilityCycleInfo {
	if m != nil {
		return m.CycleInfo
	}
	return nil
}

func (m *GetNobilityInfoResp) GetIsKeep() bool {
	if m != nil {
		return m.IsKeep
	}
	return false
}

// 获取用户贵族详细信息
type GetNobilityDetailInfoReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid     uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetNobilityDetailInfoReq) Reset()                    { *m = GetNobilityDetailInfoReq{} }
func (m *GetNobilityDetailInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetNobilityDetailInfoReq) ProtoMessage()               {}
func (*GetNobilityDetailInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{38} }

func (m *GetNobilityDetailInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNobilityDetailInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetNobilityDetailInfoResp struct {
	BaseResp *ga.BaseResp     `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Info     *ga.NobilityInfo `protobuf:"bytes,2,opt,name=info" json:"info,omitempty"`
}

func (m *GetNobilityDetailInfoResp) Reset()                    { *m = GetNobilityDetailInfoResp{} }
func (m *GetNobilityDetailInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetNobilityDetailInfoResp) ProtoMessage()               {}
func (*GetNobilityDetailInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorMyInfo, []int{39} }

func (m *GetNobilityDetailInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNobilityDetailInfoResp) GetInfo() *ga.NobilityInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func init() {
	proto.RegisterType((*ModifyUserNameReq)(nil), "ga.ModifyUserNameReq")
	proto.RegisterType((*ModifyUserNameResp)(nil), "ga.ModifyUserNameResp")
	proto.RegisterType((*ModifyNicknameReq)(nil), "ga.ModifyNicknameReq")
	proto.RegisterType((*ModifyNicknameResp)(nil), "ga.ModifyNicknameResp")
	proto.RegisterType((*ModifySignatureReq)(nil), "ga.ModifySignatureReq")
	proto.RegisterType((*ModifySignatureResp)(nil), "ga.ModifySignatureResp")
	proto.RegisterType((*BatGetUserGrowInfoReq)(nil), "ga.BatGetUserGrowInfoReq")
	proto.RegisterType((*BatGetUserGrowInfoResp)(nil), "ga.BatGetUserGrowInfoResp")
	proto.RegisterType((*SetMyMedalTaillightReq)(nil), "ga.SetMyMedalTaillightReq")
	proto.RegisterType((*SetMyMedalTaillightResp)(nil), "ga.SetMyMedalTaillightResp")
	proto.RegisterType((*UpdatePhotoAlbumReq)(nil), "ga.UpdatePhotoAlbumReq")
	proto.RegisterType((*UpdatePhotoAlbumResp)(nil), "ga.UpdatePhotoAlbumResp")
	proto.RegisterType((*GetPhotoAlbumReq)(nil), "ga.GetPhotoAlbumReq")
	proto.RegisterType((*PhotoAlbumNewURLInfo)(nil), "ga.PhotoAlbumNewURLInfo")
	proto.RegisterType((*GetPhotoAlbumResp)(nil), "ga.GetPhotoAlbumResp")
	proto.RegisterType((*SetUserHeadwearReq)(nil), "ga.SetUserHeadwearReq")
	proto.RegisterType((*SetUserHeadwearResp)(nil), "ga.SetUserHeadwearResp")
	proto.RegisterType((*GetUserHeadwearReq)(nil), "ga.GetUserHeadwearReq")
	proto.RegisterType((*GetUserHeadwearResp)(nil), "ga.GetUserHeadwearResp")
	proto.RegisterType((*RemoveUserHeadwearReq)(nil), "ga.RemoveUserHeadwearReq")
	proto.RegisterType((*RemoveUserHeadwearResp)(nil), "ga.RemoveUserHeadwearResp")
	proto.RegisterType((*GetUserCertificationReq)(nil), "ga.GetUserCertificationReq")
	proto.RegisterType((*GetUserCertificationResp)(nil), "ga.GetUserCertificationResp")
	proto.RegisterType((*UserCertificationInfo)(nil), "ga.UserCertificationInfo")
	proto.RegisterType((*GetUserCertifyListReq)(nil), "ga.GetUserCertifyListReq")
	proto.RegisterType((*GetUserCertifyListResp)(nil), "ga.GetUserCertifyListResp")
	proto.RegisterType((*SetUserWearCertificationReq)(nil), "ga.SetUserWearCertificationReq")
	proto.RegisterType((*SetUserWearCertificationResp)(nil), "ga.SetUserWearCertificationResp")
	proto.RegisterType((*UserContractInfo)(nil), "ga.UserContractInfo")
	proto.RegisterType((*GetUserContractInfoReq)(nil), "ga.GetUserContractInfoReq")
	proto.RegisterType((*GetUserContractInfoResp)(nil), "ga.GetUserContractInfoResp")
	proto.RegisterType((*AgreeUserContractReq)(nil), "ga.AgreeUserContractReq")
	proto.RegisterType((*AgreeUserContractResp)(nil), "ga.AgreeUserContractResp")
	proto.RegisterType((*BatchGetNobilityInfosReq)(nil), "ga.BatchGetNobilityInfosReq")
	proto.RegisterType((*BatchGetNobilityInfosResp)(nil), "ga.BatchGetNobilityInfosResp")
	proto.RegisterType((*NobilityCycleInfo)(nil), "ga.NobilityCycleInfo")
	proto.RegisterType((*GetNobilityInfoReq)(nil), "ga.GetNobilityInfoReq")
	proto.RegisterType((*GetNobilityInfoResp)(nil), "ga.GetNobilityInfoResp")
	proto.RegisterType((*GetNobilityDetailInfoReq)(nil), "ga.GetNobilityDetailInfoReq")
	proto.RegisterType((*GetNobilityDetailInfoResp)(nil), "ga.GetNobilityDetailInfoResp")
	proto.RegisterEnum("ga.LocationType", LocationType_name, LocationType_value)
	proto.RegisterEnum("ga.UserContractInfo_UserContractType", UserContractInfo_UserContractType_name, UserContractInfo_UserContractType_value)
}
func (m *ModifyUserNameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyUserNameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.NewUserName)))
	i += copy(dAtA[i:], m.NewUserName)
	return i, nil
}

func (m *ModifyUserNameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyUserNameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *ModifyNicknameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyNicknameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.NewNickname)))
	i += copy(dAtA[i:], m.NewNickname)
	dAtA[i] = 0x18
	i++
	if m.PrefixValid {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ModifyNicknameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyNicknameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *ModifySignatureReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifySignatureReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.NewSignature)))
	i += copy(dAtA[i:], m.NewSignature)
	return i, nil
}

func (m *ModifySignatureResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifySignatureResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *BatGetUserGrowInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserGrowInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatGetUserGrowInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetUserGrowInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if len(m.GrowInfoList) > 0 {
		for _, msg := range m.GrowInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetMyMedalTaillightReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetMyMedalTaillightReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if len(m.MedalTaillightList) > 0 {
		for _, num := range m.MedalTaillightList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *SetMyMedalTaillightResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetMyMedalTaillightResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if len(m.MedalTaillightList) > 0 {
		for _, num := range m.MedalTaillightList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *UpdatePhotoAlbumReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePhotoAlbumReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.Uid))
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.NewImgKeyList) > 0 {
		for _, s := range m.NewImgKeyList {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *UpdatePhotoAlbumResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePhotoAlbumResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *GetPhotoAlbumReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPhotoAlbumReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n13, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *PhotoAlbumNewURLInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PhotoAlbumNewURLInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x12
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	return i, nil
}

func (m *GetPhotoAlbumResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPhotoAlbumResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n14, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.ImgUrlList) > 0 {
		for _, s := range m.ImgUrlList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.AllPhotoList) > 0 {
		for _, msg := range m.AllPhotoList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetUserHeadwearReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserHeadwearReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n15, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.HeadwearId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.CpUid))
	return i, nil
}

func (m *SetUserHeadwearResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserHeadwearResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n16, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	if m.HeadwearInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("headwear_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.HeadwearInfo.Size()))
		n17, err := m.HeadwearInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	return i, nil
}

func (m *GetUserHeadwearReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserHeadwearReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n18, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	return i, nil
}

func (m *GetUserHeadwearResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserHeadwearResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n19, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	if len(m.HeadwearList) > 0 {
		for _, msg := range m.HeadwearList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.HeadwearIdInuse))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.CpUidInuse))
	return i, nil
}

func (m *RemoveUserHeadwearReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveUserHeadwearReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n20, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.HeadwearId))
	return i, nil
}

func (m *RemoveUserHeadwearResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveUserHeadwearResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n21, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	return i, nil
}

func (m *GetUserCertificationReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCertificationReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n22, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.RequestType))
	return i, nil
}

func (m *GetUserCertificationResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCertificationResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n23, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.Intro)))
	i += copy(dAtA[i:], m.Intro)
	dAtA[i] = 0x22
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.Style)))
	i += copy(dAtA[i:], m.Style)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.CertifySpecialEffectIcon)))
	i += copy(dAtA[i:], m.CertifySpecialEffectIcon)
	dAtA[i] = 0x32
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.CertifySpecialEffectTitle)))
	i += copy(dAtA[i:], m.CertifySpecialEffectTitle)
	return i, nil
}

func (m *UserCertificationInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserCertificationInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.Intro)))
	i += copy(dAtA[i:], m.Intro)
	dAtA[i] = 0x22
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.Style)))
	i += copy(dAtA[i:], m.Style)
	dAtA[i] = 0x28
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x30
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.EndTs))
	dAtA[i] = 0x38
	i++
	if m.IsUse {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x42
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.CertifySpecialEffectIcon)))
	i += copy(dAtA[i:], m.CertifySpecialEffectIcon)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.CertifySpecialEffectTitle)))
	i += copy(dAtA[i:], m.CertifySpecialEffectTitle)
	return i, nil
}

func (m *GetUserCertifyListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCertifyListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n24, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.RequestType))
	return i, nil
}

func (m *GetUserCertifyListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCertifyListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n25, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	if len(m.CertifyList) > 0 {
		for _, msg := range m.CertifyList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetUserWearCertificationReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserWearCertificationReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n26, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *SetUserWearCertificationResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserWearCertificationResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n27, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	return i, nil
}

func (m *UserContractInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserContractInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.ContractType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.ContractUrl)))
	i += copy(dAtA[i:], m.ContractUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.ContractVersion)))
	i += copy(dAtA[i:], m.ContractVersion)
	dAtA[i] = 0x20
	i++
	if m.IsAgree {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserContractInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserContractInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n28, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.ContractType))
	return i, nil
}

func (m *GetUserContractInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserContractInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n29, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	if m.ContractInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.ContractInfo.Size()))
		n30, err := m.ContractInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	return i, nil
}

func (m *AgreeUserContractReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AgreeUserContractReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n31, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.ContractType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.ContractVersion)))
	i += copy(dAtA[i:], m.ContractVersion)
	return i, nil
}

func (m *AgreeUserContractResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AgreeUserContractResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n32, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n32
	}
	return i, nil
}

func (m *BatchGetNobilityInfosReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetNobilityInfosReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n33, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n33
	}
	if len(m.Uids) > 0 {
		for _, num := range m.Uids {
			dAtA[i] = 0x10
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(num))
		}
	}
	if len(m.Accounts) > 0 {
		for _, s := range m.Accounts {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *BatchGetNobilityInfosResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetNobilityInfosResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n34, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n34
	}
	if len(m.NobilityInfos) > 0 {
		for _, msg := range m.NobilityInfos {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.AccountNobilityInfos) > 0 {
		for _, msg := range m.AccountNobilityInfos {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintMyInfo(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *NobilityCycleInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NobilityCycleInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.RemainDays))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.FloatCall)))
	i += copy(dAtA[i:], m.FloatCall)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(len(m.FloatPrivilege)))
	i += copy(dAtA[i:], m.FloatPrivilege)
	dAtA[i] = 0x20
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.NowTs))
	return i, nil
}

func (m *GetNobilityInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNobilityInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n35, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n35
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetNobilityInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNobilityInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n36, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n36
	}
	if m.CycleInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.CycleInfo.Size()))
		n37, err := m.CycleInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n37
	}
	dAtA[i] = 0x18
	i++
	if m.IsKeep {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetNobilityDetailInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNobilityDetailInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseReq.Size()))
		n38, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n38
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMyInfo(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetNobilityDetailInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNobilityDetailInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.BaseResp.Size()))
		n39, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n39
	}
	if m.Info != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMyInfo(dAtA, i, uint64(m.Info.Size()))
		n40, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n40
	}
	return i, nil
}

func encodeFixed64MyInfo(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32MyInfo(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintMyInfo(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ModifyUserNameReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	l = len(m.NewUserName)
	n += 1 + l + sovMyInfo(uint64(l))
	return n
}

func (m *ModifyUserNameResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *ModifyNicknameReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	l = len(m.NewNickname)
	n += 1 + l + sovMyInfo(uint64(l))
	n += 2
	return n
}

func (m *ModifyNicknameResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *ModifySignatureReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	l = len(m.NewSignature)
	n += 1 + l + sovMyInfo(uint64(l))
	return n
}

func (m *ModifySignatureResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *BatGetUserGrowInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovMyInfo(uint64(e))
		}
	}
	return n
}

func (m *BatGetUserGrowInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if len(m.GrowInfoList) > 0 {
		for _, e := range m.GrowInfoList {
			l = e.Size()
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	return n
}

func (m *SetMyMedalTaillightReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if len(m.MedalTaillightList) > 0 {
		for _, e := range m.MedalTaillightList {
			n += 1 + sovMyInfo(uint64(e))
		}
	}
	return n
}

func (m *SetMyMedalTaillightResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if len(m.MedalTaillightList) > 0 {
		for _, e := range m.MedalTaillightList {
			n += 1 + sovMyInfo(uint64(e))
		}
	}
	return n
}

func (m *UpdatePhotoAlbumReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.Uid))
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			l = len(s)
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	if len(m.NewImgKeyList) > 0 {
		for _, s := range m.NewImgKeyList {
			l = len(s)
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	return n
}

func (m *UpdatePhotoAlbumResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *GetPhotoAlbumReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.Uid))
	return n
}

func (m *PhotoAlbumNewURLInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovMyInfo(uint64(l))
	return n
}

func (m *GetPhotoAlbumResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			l = len(s)
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	if len(m.ImgUrlList) > 0 {
		for _, s := range m.ImgUrlList {
			l = len(s)
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	if len(m.AllPhotoList) > 0 {
		for _, e := range m.AllPhotoList {
			l = e.Size()
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	return n
}

func (m *SetUserHeadwearReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.HeadwearId))
	n += 1 + sovMyInfo(uint64(m.CpUid))
	return n
}

func (m *SetUserHeadwearResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if m.HeadwearInfo != nil {
		l = m.HeadwearInfo.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *GetUserHeadwearReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *GetUserHeadwearResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if len(m.HeadwearList) > 0 {
		for _, e := range m.HeadwearList {
			l = e.Size()
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	n += 1 + sovMyInfo(uint64(m.HeadwearIdInuse))
	n += 1 + sovMyInfo(uint64(m.CpUidInuse))
	return n
}

func (m *RemoveUserHeadwearReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.HeadwearId))
	return n
}

func (m *RemoveUserHeadwearResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *GetUserCertificationReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovMyInfo(uint64(l))
	n += 1 + sovMyInfo(uint64(m.RequestType))
	return n
}

func (m *GetUserCertificationResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	l = len(m.Title)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.Intro)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.Style)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.CertifySpecialEffectIcon)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.CertifySpecialEffectTitle)
	n += 1 + l + sovMyInfo(uint64(l))
	return n
}

func (m *UserCertificationInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMyInfo(uint64(m.Id))
	l = len(m.Title)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.Intro)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.Style)
	n += 1 + l + sovMyInfo(uint64(l))
	n += 1 + sovMyInfo(uint64(m.BeginTs))
	n += 1 + sovMyInfo(uint64(m.EndTs))
	n += 2
	l = len(m.CertifySpecialEffectIcon)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.CertifySpecialEffectTitle)
	n += 1 + l + sovMyInfo(uint64(l))
	return n
}

func (m *GetUserCertifyListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.TargetUid))
	n += 1 + sovMyInfo(uint64(m.RequestType))
	return n
}

func (m *GetUserCertifyListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if len(m.CertifyList) > 0 {
		for _, e := range m.CertifyList {
			l = e.Size()
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	return n
}

func (m *SetUserWearCertificationReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.Id))
	return n
}

func (m *SetUserWearCertificationResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *UserContractInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMyInfo(uint64(m.ContractType))
	l = len(m.ContractUrl)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.ContractVersion)
	n += 1 + l + sovMyInfo(uint64(l))
	n += 2
	return n
}

func (m *GetUserContractInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.ContractType))
	return n
}

func (m *GetUserContractInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if m.ContractInfo != nil {
		l = m.ContractInfo.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *AgreeUserContractReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.ContractType))
	l = len(m.ContractVersion)
	n += 1 + l + sovMyInfo(uint64(l))
	return n
}

func (m *AgreeUserContractResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func (m *BatchGetNobilityInfosReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if len(m.Uids) > 0 {
		for _, e := range m.Uids {
			n += 1 + sovMyInfo(uint64(e))
		}
	}
	if len(m.Accounts) > 0 {
		for _, s := range m.Accounts {
			l = len(s)
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	return n
}

func (m *BatchGetNobilityInfosResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if len(m.NobilityInfos) > 0 {
		for _, e := range m.NobilityInfos {
			l = e.Size()
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	if len(m.AccountNobilityInfos) > 0 {
		for _, e := range m.AccountNobilityInfos {
			l = e.Size()
			n += 1 + l + sovMyInfo(uint64(l))
		}
	}
	return n
}

func (m *NobilityCycleInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMyInfo(uint64(m.RemainDays))
	l = len(m.FloatCall)
	n += 1 + l + sovMyInfo(uint64(l))
	l = len(m.FloatPrivilege)
	n += 1 + l + sovMyInfo(uint64(l))
	n += 1 + sovMyInfo(uint64(m.NowTs))
	return n
}

func (m *GetNobilityInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.Uid))
	return n
}

func (m *GetNobilityInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if m.CycleInfo != nil {
		l = m.CycleInfo.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 2
	return n
}

func (m *GetNobilityDetailInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	n += 1 + sovMyInfo(uint64(m.Uid))
	return n
}

func (m *GetNobilityDetailInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovMyInfo(uint64(l))
	}
	return n
}

func sovMyInfo(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozMyInfo(x uint64) (n int) {
	return sovMyInfo(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ModifyUserNameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyUserNameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyUserNameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewUserName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NewUserName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("new_user_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyUserNameResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyUserNameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyUserNameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyNicknameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyNicknameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyNicknameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NewNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PrefixValid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PrefixValid = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("new_nickname")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyNicknameResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyNicknameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyNicknameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifySignatureReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifySignatureReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifySignatureReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewSignature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NewSignature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("new_signature")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifySignatureResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifySignatureResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifySignatureResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserGrowInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserGrowInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserGrowInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMyInfo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMyInfo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMyInfo
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMyInfo
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetUserGrowInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetUserGrowInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetUserGrowInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GrowInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GrowInfoList = append(m.GrowInfoList, &ga.GrowInfo{})
			if err := m.GrowInfoList[len(m.GrowInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetMyMedalTaillightReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetMyMedalTaillightReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetMyMedalTaillightReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMyInfo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MedalTaillightList = append(m.MedalTaillightList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMyInfo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMyInfo
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMyInfo
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MedalTaillightList = append(m.MedalTaillightList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalTaillightList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetMyMedalTaillightResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetMyMedalTaillightResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetMyMedalTaillightResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMyInfo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MedalTaillightList = append(m.MedalTaillightList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMyInfo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMyInfo
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMyInfo
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MedalTaillightList = append(m.MedalTaillightList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalTaillightList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePhotoAlbumReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePhotoAlbumReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePhotoAlbumReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgKeyList = append(m.ImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NewImgKeyList = append(m.NewImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePhotoAlbumResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePhotoAlbumResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePhotoAlbumResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPhotoAlbumReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPhotoAlbumReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPhotoAlbumReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PhotoAlbumNewURLInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PhotoAlbumNewURLInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PhotoAlbumNewURLInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPhotoAlbumResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPhotoAlbumResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPhotoAlbumResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgKeyList = append(m.ImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgUrlList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgUrlList = append(m.ImgUrlList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllPhotoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AllPhotoList = append(m.AllPhotoList, &PhotoAlbumNewURLInfo{})
			if err := m.AllPhotoList[len(m.AllPhotoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserHeadwearReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserHeadwearReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserHeadwearReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearId", wireType)
			}
			m.HeadwearId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadwearId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpUid", wireType)
			}
			m.CpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("headwear_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserHeadwearResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserHeadwearResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserHeadwearResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.HeadwearInfo == nil {
				m.HeadwearInfo = &ga.UserHeadwearInfo{}
			}
			if err := m.HeadwearInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("headwear_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserHeadwearReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserHeadwearReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserHeadwearReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserHeadwearResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserHeadwearResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserHeadwearResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadwearList = append(m.HeadwearList, &ga.UserHeadwearInfo{})
			if err := m.HeadwearList[len(m.HeadwearList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearIdInuse", wireType)
			}
			m.HeadwearIdInuse = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadwearIdInuse |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpUidInuse", wireType)
			}
			m.CpUidInuse = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpUidInuse |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("headwear_id_inuse")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveUserHeadwearReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveUserHeadwearReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveUserHeadwearReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearId", wireType)
			}
			m.HeadwearId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HeadwearId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("headwear_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveUserHeadwearResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveUserHeadwearResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveUserHeadwearResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCertificationReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCertificationReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCertificationReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestType", wireType)
			}
			m.RequestType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCertificationResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCertificationResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCertificationResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Intro", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Intro = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Style", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Style = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifySpecialEffectIcon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifySpecialEffectIcon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifySpecialEffectTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifySpecialEffectTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("intro")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserCertificationInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserCertificationInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserCertificationInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Intro", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Intro = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Style", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Style = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsUse", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsUse = bool(v != 0)
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifySpecialEffectIcon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifySpecialEffectIcon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifySpecialEffectTitle", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifySpecialEffectTitle = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("intro")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("style")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("begin_ts")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("end_ts")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_use")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCertifyListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCertifyListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCertifyListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequestType", wireType)
			}
			m.RequestType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCertifyListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCertifyListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCertifyListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CertifyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CertifyList = append(m.CertifyList, &UserCertificationInfo{})
			if err := m.CertifyList[len(m.CertifyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserWearCertificationReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserWearCertificationReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserWearCertificationReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserWearCertificationResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserWearCertificationResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserWearCertificationResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserContractInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserContractInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserContractInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContractType", wireType)
			}
			m.ContractType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContractType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContractUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ContractUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContractVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ContractVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAgree", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAgree = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("contract_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("contract_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("contract_version")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserContractInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserContractInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserContractInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContractType", wireType)
			}
			m.ContractType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContractType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("contract_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserContractInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserContractInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserContractInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContractInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ContractInfo == nil {
				m.ContractInfo = &UserContractInfo{}
			}
			if err := m.ContractInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AgreeUserContractReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AgreeUserContractReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AgreeUserContractReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContractType", wireType)
			}
			m.ContractType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContractType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContractVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ContractVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("contract_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("contract_version")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AgreeUserContractResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AgreeUserContractResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AgreeUserContractResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetNobilityInfosReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetNobilityInfosReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetNobilityInfosReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMyInfo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Uids = append(m.Uids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMyInfo
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMyInfo
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMyInfo
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Uids = append(m.Uids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uids", wireType)
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Accounts", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Accounts = append(m.Accounts, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetNobilityInfosResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetNobilityInfosResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetNobilityInfosResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NobilityInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NobilityInfos = append(m.NobilityInfos, &ga.NobilityInfo{})
			if err := m.NobilityInfos[len(m.NobilityInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccountNobilityInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccountNobilityInfos = append(m.AccountNobilityInfos, &ga.NobilityInfo{})
			if err := m.AccountNobilityInfos[len(m.AccountNobilityInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NobilityCycleInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NobilityCycleInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NobilityCycleInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainDays", wireType)
			}
			m.RemainDays = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainDays |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FloatCall", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FloatCall = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FloatPrivilege", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FloatPrivilege = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NowTs", wireType)
			}
			m.NowTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NowTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("remain_days")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("float_call")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("float_privilege")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("now_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNobilityInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNobilityInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNobilityInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNobilityInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNobilityInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNobilityInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CycleInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CycleInfo == nil {
				m.CycleInfo = &NobilityCycleInfo{}
			}
			if err := m.CycleInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsKeep", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsKeep = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNobilityDetailInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNobilityDetailInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNobilityDetailInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNobilityDetailInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNobilityDetailInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNobilityDetailInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMyInfo
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &ga.NobilityInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMyInfo(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMyInfo
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipMyInfo(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMyInfo
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMyInfo
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthMyInfo
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowMyInfo
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipMyInfo(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthMyInfo = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMyInfo   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("my_info.proto", fileDescriptorMyInfo) }

var fileDescriptorMyInfo = []byte{
	// 1587 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xcd, 0x6e, 0xdb, 0xc6,
	0x16, 0x36, 0x29, 0xff, 0x1e, 0x49, 0xb6, 0x4c, 0xcb, 0x8e, 0x9c, 0xe4, 0x3a, 0x02, 0x73, 0x93,
	0x38, 0xf7, 0xa2, 0x76, 0x60, 0xb4, 0x28, 0x0a, 0x04, 0x6d, 0x6d, 0xc5, 0x71, 0x85, 0x38, 0x8e,
	0x21, 0x4b, 0x2e, 0x9a, 0x2e, 0x88, 0x31, 0x39, 0xa2, 0x07, 0xa6, 0x38, 0x0c, 0x67, 0x64, 0x95,
	0xe8, 0xa2, 0xe9, 0x03, 0x04, 0xe8, 0xa6, 0x28, 0xd0, 0x55, 0x37, 0x5d, 0xf5, 0x2d, 0xba, 0x69,
	0x96, 0xdd, 0x74, 0x5b, 0x14, 0xe9, 0xa6, 0x8f, 0x51, 0xcc, 0x90, 0x94, 0x48, 0x49, 0x46, 0xc2,
	0xc0, 0xd9, 0x49, 0xe7, 0x3b, 0x73, 0xce, 0x77, 0x7e, 0x67, 0x24, 0x28, 0x76, 0x02, 0x83, 0xb8,
	0x6d, 0xba, 0xe1, 0xf9, 0x94, 0x53, 0x4d, 0xb5, 0xd1, 0xd5, 0xa2, 0x8d, 0x8c, 0x13, 0xc4, 0x70,
	0x28, 0xd2, 0x31, 0x2c, 0x3e, 0xa6, 0x16, 0x69, 0x07, 0x2d, 0x86, 0xfd, 0x03, 0xd4, 0xc1, 0x0d,
	0xfc, 0x4c, 0xbb, 0x0d, 0xb3, 0x42, 0xc5, 0xf0, 0xf1, 0xb3, 0x8a, 0x52, 0x55, 0xd7, 0xf3, 0x5b,
	0xf9, 0x0d, 0x1b, 0x6d, 0xec, 0x20, 0x26, 0xe0, 0xc6, 0xcc, 0x49, 0xf8, 0x41, 0x5b, 0x87, 0xa2,
	0x8b, 0x7b, 0x46, 0x97, 0x61, 0xdf, 0x70, 0x51, 0x07, 0x57, 0xd4, 0xaa, 0xba, 0x3e, 0xb7, 0x33,
	0xf9, 0xf2, 0xcf, 0x1b, 0x13, 0x8d, 0xbc, 0x8b, 0x7b, 0xb1, 0x51, 0xfd, 0x13, 0xd0, 0x86, 0xdd,
	0x30, 0x4f, 0xbb, 0x0b, 0x73, 0x91, 0x1f, 0xe6, 0x45, 0x8e, 0x0a, 0x03, 0x47, 0xcc, 0x6b, 0xcc,
	0x9e, 0x44, 0x9f, 0xf4, 0x17, 0x4a, 0x4c, 0xf4, 0x80, 0x98, 0x67, 0x6e, 0x46, 0xa2, 0x77, 0xa0,
	0x20, 0x88, 0xba, 0xd1, 0xd1, 0x11, 0x9e, 0xb1, 0x4d, 0xa1, 0xe8, 0xf9, 0xb8, 0x4d, 0xbe, 0x32,
	0xce, 0x91, 0x43, 0xac, 0x4a, 0xae, 0xaa, 0xac, 0xcf, 0xc6, 0x8a, 0x21, 0x72, 0x2c, 0x80, 0x41,
	0x40, 0x03, 0x3a, 0xd9, 0x02, 0xb2, 0x63, 0x03, 0x47, 0xc4, 0x76, 0x11, 0xef, 0xfa, 0x99, 0x02,
	0xba, 0x1b, 0x66, 0x9e, 0xc5, 0x67, 0x53, 0x11, 0x89, 0x58, 0xfb, 0x56, 0xf5, 0x4f, 0x61, 0x69,
	0xc4, 0x51, 0x36, 0xaa, 0x4f, 0x61, 0x79, 0x07, 0xf1, 0x3d, 0xcc, 0x45, 0xf1, 0xf6, 0x7c, 0xda,
	0xab, 0xbb, 0x6d, 0x9a, 0x85, 0xed, 0x2a, 0xcc, 0x76, 0x89, 0x65, 0x38, 0x84, 0xf1, 0x8a, 0x5a,
	0xcd, 0xad, 0x17, 0x1b, 0x33, 0x5d, 0x62, 0xed, 0x13, 0xc6, 0xf5, 0x1e, 0xac, 0x8c, 0xb3, 0x9d,
	0x89, 0xa0, 0xb6, 0x05, 0xf3, 0xb6, 0x4f, 0x7b, 0xb2, 0xd5, 0x07, 0x5e, 0x22, 0xfd, 0xbe, 0xd1,
	0x82, 0x1d, 0x7d, 0x92, 0x8e, 0x7d, 0x58, 0x39, 0xc2, 0xfc, 0x71, 0xf0, 0x18, 0x5b, 0xc8, 0x69,
	0x22, 0xe2, 0x38, 0xc4, 0x3e, 0xe5, 0x59, 0xa2, 0xba, 0x07, 0xe5, 0x8e, 0x38, 0x6c, 0xf0, 0xf8,
	0x74, 0x32, 0x42, 0xad, 0x93, 0x32, 0x2c, 0x7d, 0x9e, 0xc3, 0x95, 0xb1, 0x3e, 0xb3, 0x45, 0x9b,
	0xdd, 0xef, 0x4f, 0x0a, 0x2c, 0xb5, 0x3c, 0x0b, 0x71, 0x7c, 0x78, 0x4a, 0x39, 0xdd, 0x76, 0x4e,
	0xba, 0x9d, 0x2c, 0x91, 0xae, 0x40, 0xae, 0x4b, 0x2c, 0xd9, 0x63, 0xc5, 0xa8, 0xc7, 0x84, 0x40,
	0xab, 0x42, 0x81, 0x74, 0x6c, 0xe3, 0x0c, 0x07, 0x21, 0x83, 0x5c, 0x35, 0xb7, 0x3e, 0xd7, 0x00,
	0xd2, 0xb1, 0x1f, 0xe1, 0x40, 0x78, 0xd6, 0xee, 0x40, 0x49, 0xf4, 0x69, 0x4a, 0x6b, 0x52, 0x6a,
	0x89, 0xfe, 0xad, 0xf7, 0x15, 0xf5, 0x6d, 0x28, 0x8f, 0x32, 0xcc, 0xd6, 0xa6, 0x0d, 0x28, 0xed,
	0x61, 0x7e, 0xa9, 0x11, 0xea, 0x0f, 0xa1, 0x3c, 0x30, 0x78, 0x80, 0x7b, 0xad, 0xc6, 0xbe, 0xe8,
	0x20, 0xa1, 0x7f, 0x86, 0x03, 0x69, 0x32, 0x9e, 0x3a, 0x21, 0x90, 0x76, 0x7c, 0x27, 0x35, 0x8d,
	0x42, 0xa0, 0xff, 0xaa, 0xc0, 0xe2, 0x10, 0xb9, 0x6c, 0x45, 0x1f, 0x4e, 0xb5, 0x3a, 0x92, 0xea,
	0x48, 0xa3, 0xeb, 0x3b, 0xc3, 0xc5, 0x68, 0xf9, 0x8e, 0xd4, 0xf8, 0x18, 0xe6, 0x91, 0xe3, 0x18,
	0x9e, 0x20, 0x31, 0x28, 0x45, 0x7e, 0xab, 0x22, 0x7c, 0x8e, 0x0b, 0xb3, 0x51, 0x40, 0x8e, 0x23,
	0x01, 0x59, 0xa3, 0xe7, 0x0a, 0x68, 0x47, 0xe1, 0xa4, 0x7e, 0x86, 0x91, 0xd5, 0xc3, 0xc8, 0xcf,
	0x92, 0xe3, 0x5b, 0x90, 0x3f, 0x8d, 0x8e, 0x19, 0x43, 0xb9, 0x86, 0x18, 0xa8, 0x5b, 0xda, 0x35,
	0x98, 0x36, 0x3d, 0xa3, 0x1b, 0x2d, 0xdf, 0x58, 0x63, 0xca, 0xf4, 0x5a, 0xc4, 0xd2, 0xbf, 0x86,
	0xa5, 0x11, 0x06, 0xd9, 0x12, 0xf9, 0x11, 0x14, 0x07, 0x2c, 0xdc, 0x36, 0x95, 0x3c, 0xf2, 0x5b,
	0x65, 0xa1, 0x9e, 0xb4, 0x1b, 0xc6, 0x7f, 0x9a, 0xf8, 0xa6, 0xdf, 0x07, 0x6d, 0xef, 0xad, 0xc3,
	0xd7, 0xff, 0x50, 0x60, 0x69, 0xef, 0xf2, 0xb8, 0x27, 0xd6, 0xdc, 0x6b, 0xb8, 0xcb, 0xda, 0xdf,
	0x83, 0xc5, 0x44, 0xf2, 0x0d, 0xe2, 0x76, 0x19, 0xae, 0xe4, 0x12, 0x25, 0x58, 0x18, 0x94, 0xa0,
	0x2e, 0x40, 0xed, 0x36, 0x14, 0xc2, 0x3a, 0x44, 0xca, 0x93, 0x89, 0x6a, 0x80, 0xac, 0x86, 0xd4,
	0xd3, 0xdb, 0xb0, 0xdc, 0xc0, 0x1d, 0x7a, 0x8e, 0xdf, 0x6d, 0x5f, 0xe8, 0x35, 0x58, 0x19, 0xe7,
	0x27, 0xdb, 0x8e, 0xf8, 0x41, 0x81, 0x2b, 0x51, 0x11, 0x6a, 0xd8, 0xe7, 0xa4, 0x4d, 0x4c, 0xc4,
	0x09, 0x75, 0xb3, 0xf0, 0xfd, 0x3f, 0xcc, 0x73, 0xe4, 0xdb, 0x98, 0x1b, 0xc8, 0x34, 0x69, 0xd7,
	0xe5, 0xa9, 0x71, 0x2f, 0x86, 0xd8, 0x76, 0x08, 0x89, 0x07, 0x85, 0x8f, 0x9f, 0x75, 0x31, 0xe3,
	0x06, 0x0f, 0x3c, 0x9c, 0xea, 0xe9, 0x7c, 0x84, 0x34, 0x03, 0x0f, 0xeb, 0x3f, 0xab, 0x50, 0x19,
	0xcf, 0x2c, 0x5b, 0x8f, 0x5c, 0x85, 0x29, 0x4e, 0xb8, 0x93, 0x7e, 0x11, 0x84, 0x22, 0x81, 0x11,
	0x97, 0xfb, 0x54, 0x16, 0xbe, 0x8f, 0x49, 0x91, 0xc0, 0x18, 0x0f, 0x9c, 0xb0, 0xce, 0x7d, 0x4c,
	0x8a, 0xb4, 0x1a, 0x5c, 0x33, 0x25, 0xa7, 0xc0, 0x60, 0x1e, 0x36, 0x09, 0x72, 0x0c, 0xdc, 0x6e,
	0x63, 0x93, 0x1b, 0xc4, 0xa4, 0x6e, 0x65, 0x2a, 0x71, 0xa2, 0x12, 0x29, 0x1e, 0x85, 0x7a, 0xbb,
	0x52, 0xad, 0x6e, 0x52, 0x57, 0xdb, 0x85, 0xeb, 0x17, 0x18, 0x09, 0xf9, 0x4e, 0x27, 0xac, 0xac,
	0x8e, 0xb3, 0xd2, 0x14, 0x6a, 0xfa, 0x3f, 0x2a, 0x2c, 0x8f, 0x24, 0x49, 0xee, 0xe4, 0x32, 0xa8,
	0xc4, 0x92, 0xd9, 0x89, 0x13, 0xac, 0x12, 0xeb, 0x32, 0xf2, 0xa1, 0x0e, 0xe7, 0xe3, 0x06, 0xcc,
	0x9e, 0x60, 0x9b, 0xb8, 0x06, 0x67, 0x95, 0xa9, 0xaa, 0xba, 0x3e, 0x19, 0xc1, 0x33, 0x52, 0xda,
	0x64, 0x62, 0x87, 0x61, 0xd7, 0x12, 0xf0, 0x74, 0x02, 0x9e, 0xc2, 0xae, 0x15, 0x82, 0x84, 0x89,
	0x47, 0x73, 0x65, 0xa6, 0xaa, 0xf6, 0x5f, 0x97, 0x53, 0x84, 0xb5, 0xd8, 0x6b, 0x53, 0x3d, 0x7b,
	0x29, 0xa9, 0x9e, 0x7b, 0xb3, 0x54, 0xbf, 0x50, 0x60, 0x39, 0xd5, 0x92, 0xf2, 0xa2, 0xc9, 0x32,
	0x2a, 0x37, 0x01, 0xa2, 0x51, 0x09, 0x6f, 0xd7, 0x41, 0xef, 0xcf, 0x85, 0xf2, 0x16, 0xb1, 0xde,
	0x7c, 0x44, 0xbe, 0x55, 0x60, 0x65, 0x1c, 0x9f, 0x6c, 0x03, 0x72, 0x1f, 0x0a, 0x71, 0x72, 0x12,
	0x3b, 0x74, 0x35, 0xde, 0xa1, 0x23, 0x7d, 0xd5, 0xc8, 0x9b, 0x03, 0x67, 0xfa, 0x97, 0x70, 0x2d,
	0xba, 0x80, 0x3e, 0xc7, 0xe8, 0xed, 0x77, 0x48, 0xd8, 0xab, 0x6a, 0xba, 0x57, 0xf5, 0x3a, 0x5c,
	0xbf, 0xd8, 0x78, 0xb6, 0x45, 0xf7, 0x5c, 0x85, 0x92, 0x0c, 0x87, 0xba, 0xdc, 0x47, 0x26, 0x97,
	0x13, 0x72, 0x17, 0x8a, 0x66, 0xf4, 0x3d, 0x4c, 0x75, 0x72, 0x58, 0x0a, 0x31, 0x24, 0x72, 0x2d,
	0x8a, 0xd2, 0x57, 0x1d, 0x7e, 0xd1, 0xe4, 0x63, 0xa4, 0xe5, 0x3b, 0xda, 0x26, 0x94, 0xfa, 0x8a,
	0xe7, 0xd8, 0x67, 0x84, 0xba, 0xa9, 0x71, 0x5a, 0x88, 0xd1, 0xe3, 0x10, 0x14, 0xc3, 0x43, 0x98,
	0x81, 0x6c, 0x1f, 0x87, 0xbb, 0x26, 0x1e, 0x80, 0x19, 0xc2, 0xb6, 0x85, 0x50, 0x7f, 0x98, 0x66,
	0x2e, 0xe9, 0xac, 0xc2, 0x72, 0xfd, 0xe0, 0x78, 0x7b, 0xbf, 0xfe, 0xc0, 0xa8, 0x3d, 0x39, 0x68,
	0x36, 0xb6, 0x6b, 0x4d, 0xa3, 0xf9, 0xc5, 0xe1, 0x6e, 0x69, 0x42, 0xbb, 0x02, 0x4b, 0xcd, 0xa6,
	0x71, 0xb4, 0xdb, 0x38, 0xae, 0xd7, 0x76, 0xfb, 0x68, 0x49, 0xd1, 0xcf, 0x06, 0xdd, 0x92, 0x48,
	0x42, 0xc6, 0x5f, 0x59, 0xe9, 0x7c, 0xa9, 0x17, 0xe5, 0x4b, 0xff, 0x66, 0x70, 0xaf, 0xa4, 0x9c,
	0x65, 0xbe, 0xe0, 0xfb, 0x0e, 0xa3, 0xc7, 0x89, 0x92, 0xbc, 0xe0, 0x53, 0xb6, 0xfb, 0x04, 0xe4,
	0xe3, 0xe4, 0x47, 0x05, 0xca, 0x32, 0x7f, 0x49, 0xbd, 0x77, 0x13, 0x6c, 0xe6, 0x9a, 0xeb, 0x3b,
	0xb0, 0x3c, 0x86, 0x5b, 0xb6, 0x8e, 0xf6, 0xa1, 0xb2, 0x83, 0xb8, 0x79, 0xba, 0x87, 0xf9, 0x01,
	0x3d, 0x21, 0x0e, 0xe1, 0x81, 0x08, 0x9c, 0x65, 0x89, 0x51, 0x83, 0xc9, 0x2e, 0xb1, 0x58, 0xf4,
	0x53, 0x49, 0x7e, 0xd6, 0xae, 0xc2, 0x6c, 0x74, 0x8f, 0xb3, 0xe8, 0xcd, 0xdc, 0xff, 0xae, 0xff,
	0xa6, 0xc0, 0xea, 0x05, 0x4e, 0xb3, 0x15, 0xf6, 0x43, 0x98, 0x77, 0xa3, 0xf3, 0xb2, 0xb0, 0x2c,
	0x5a, 0x3b, 0x25, 0xa1, 0x9f, 0xb4, 0xdc, 0x28, 0xba, 0x49, 0x3f, 0xda, 0x43, 0x58, 0x89, 0xd8,
	0x18, 0x43, 0x06, 0x72, 0x17, 0x18, 0x28, 0x47, 0xfa, 0x29, 0xbe, 0xfa, 0x2f, 0x0a, 0x2c, 0xc6,
	0x92, 0x5a, 0x60, 0x3a, 0x58, 0x2e, 0x84, 0x5b, 0x90, 0xf7, 0x71, 0x07, 0x11, 0xd7, 0xb0, 0x50,
	0xc0, 0x52, 0xeb, 0x00, 0x42, 0xe0, 0x01, 0x0a, 0x98, 0x58, 0xe3, 0x6d, 0x87, 0x22, 0x6e, 0x98,
	0xc8, 0x49, 0xaf, 0x82, 0x39, 0x29, 0xaf, 0x21, 0xc7, 0xd1, 0xde, 0x83, 0x85, 0x50, 0xc9, 0xf3,
	0xc9, 0x39, 0x71, 0xb0, 0x8d, 0x53, 0x3d, 0x31, 0x2f, 0xc1, 0xc3, 0x18, 0x13, 0xb7, 0xa0, 0x4b,
	0x7b, 0xe2, 0x8a, 0x9c, 0x4c, 0x78, 0x9d, 0x72, 0x69, 0xaf, 0xc9, 0xf4, 0xa6, 0x7c, 0x69, 0xa7,
	0xc2, 0xba, 0x84, 0x1f, 0x73, 0xdf, 0x87, 0x2f, 0xf0, 0xb4, 0xd9, 0x6c, 0x75, 0x7c, 0x1f, 0xc0,
	0x14, 0xd9, 0x4b, 0x4e, 0xe7, 0x72, 0xb2, 0x04, 0xfd, 0xdc, 0x36, 0xe6, 0xcc, 0x7e, 0x9a, 0xff,
	0x03, 0x33, 0x84, 0x19, 0x67, 0x18, 0x7b, 0xa9, 0x3f, 0x94, 0xa6, 0x09, 0x7b, 0x84, 0xb1, 0xa7,
	0x3f, 0x95, 0x2f, 0xbf, 0xd8, 0xc2, 0x03, 0x2c, 0x7e, 0xd7, 0x5f, 0x56, 0xcc, 0x0e, 0xac, 0x5e,
	0x60, 0x3b, 0x5b, 0xe0, 0xff, 0x85, 0xc9, 0x44, 0xc8, 0xa3, 0x5d, 0x27, 0xd1, 0xff, 0xb5, 0xa0,
	0xb0, 0x4f, 0xc3, 0x0b, 0x4b, 0x2e, 0x8a, 0x45, 0x28, 0x1e, 0x62, 0x9f, 0x51, 0x17, 0x39, 0xc6,
	0x21, 0xb2, 0x71, 0x69, 0x42, 0xcb, 0xc3, 0x4c, 0xed, 0x14, 0xb9, 0x2e, 0x76, 0x4a, 0x8a, 0xb6,
	0x00, 0xf9, 0x6d, 0xd7, 0x3c, 0xa5, 0xbe, 0x51, 0x43, 0xbe, 0x55, 0x52, 0xb5, 0x32, 0x94, 0xe2,
	0x03, 0x82, 0xae, 0x8f, 0x19, 0x2b, 0xe5, 0x76, 0x9e, 0xbc, 0x7c, 0xb5, 0xa6, 0xfc, 0xfe, 0x6a,
	0x4d, 0xf9, 0xeb, 0xd5, 0x9a, 0xf2, 0xdd, 0xdf, 0x6b, 0x13, 0x50, 0x31, 0x69, 0x67, 0x23, 0x20,
	0x01, 0xed, 0x0a, 0x26, 0x1d, 0x6a, 0x61, 0x27, 0xfc, 0x43, 0xf3, 0xe9, 0x4d, 0x9b, 0x3a, 0xc8,
	0xb5, 0x37, 0x3e, 0xd8, 0xe2, 0x7c, 0xc3, 0xa4, 0x9d, 0x4d, 0x29, 0x36, 0xa9, 0xb3, 0x89, 0x3c,
	0x6f, 0xb3, 0x13, 0x08, 0x9e, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0x8f, 0x5b, 0xed, 0xd4, 0x18,
	0x15, 0x00, 0x00,
}
