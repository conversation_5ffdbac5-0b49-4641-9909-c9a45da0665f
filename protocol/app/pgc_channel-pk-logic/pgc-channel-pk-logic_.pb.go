// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pgc-channel-pk-logic_.proto

package pgc_channel_pk_logic // import "golang.52tt.com/protocol/app/pgc_channel-pk-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// PK中，对面房间语音状态
type PgcChannelPKOpponentMicFlag int32

const (
	PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN  PgcChannelPKOpponentMicFlag = 0
	PgcChannelPKOpponentMicFlag_PgcPKMic_CLOSE PgcChannelPKOpponentMicFlag = 1
)

var PgcChannelPKOpponentMicFlag_name = map[int32]string{
	0: "PgcPKMic_OPEN",
	1: "PgcPKMic_CLOSE",
}
var PgcChannelPKOpponentMicFlag_value = map[string]int32{
	"PgcPKMic_OPEN":  0,
	"PgcPKMic_CLOSE": 1,
}

func (x PgcChannelPKOpponentMicFlag) String() string {
	return proto.EnumName(PgcChannelPKOpponentMicFlag_name, int32(x))
}
func (PgcChannelPKOpponentMicFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{0}
}

type PgcChannelPKSwitchEnum int32

const (
	PgcChannelPKSwitchEnum_ON  PgcChannelPKSwitchEnum = 0
	PgcChannelPKSwitchEnum_OFF PgcChannelPKSwitchEnum = 1
)

var PgcChannelPKSwitchEnum_name = map[int32]string{
	0: "ON",
	1: "OFF",
}
var PgcChannelPKSwitchEnum_value = map[string]int32{
	"ON":  0,
	"OFF": 1,
}

func (x PgcChannelPKSwitchEnum) String() string {
	return proto.EnumName(PgcChannelPKSwitchEnum_name, int32(x))
}
func (PgcChannelPKSwitchEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{1}
}

type PgcChannelPKQuickKillChangeOpt_EventType int32

const (
	PgcChannelPKQuickKillChangeOpt_OnlySyncInfo     PgcChannelPKQuickKillChangeOpt_EventType = 0
	PgcChannelPKQuickKillChangeOpt_TriggerQuickKill PgcChannelPKQuickKillChangeOpt_EventType = 1
	PgcChannelPKQuickKillChangeOpt_StopQuickKill    PgcChannelPKQuickKillChangeOpt_EventType = 2
)

var PgcChannelPKQuickKillChangeOpt_EventType_name = map[int32]string{
	0: "OnlySyncInfo",
	1: "TriggerQuickKill",
	2: "StopQuickKill",
}
var PgcChannelPKQuickKillChangeOpt_EventType_value = map[string]int32{
	"OnlySyncInfo":     0,
	"TriggerQuickKill": 1,
	"StopQuickKill":    2,
}

func (x PgcChannelPKQuickKillChangeOpt_EventType) String() string {
	return proto.EnumName(PgcChannelPKQuickKillChangeOpt_EventType_name, int32(x))
}
func (PgcChannelPKQuickKillChangeOpt_EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{5, 0}
}

type PgcChannelPKBattle_Status int32

const (
	PgcChannelPKBattle_NotPK    PgcChannelPKBattle_Status = 0
	PgcChannelPKBattle_InvitePK PgcChannelPKBattle_Status = 1
	PgcChannelPKBattle_InPKing  PgcChannelPKBattle_Status = 2
	// QuickKill = 3;  // 斩杀
	// PeakPk = 4;     // 巅峰对决
	PgcChannelPKBattle_ChoseInteraction PgcChannelPKBattle_Status = 5
	PgcChannelPKBattle_Interaction      PgcChannelPKBattle_Status = 6
)

var PgcChannelPKBattle_Status_name = map[int32]string{
	0: "NotPK",
	1: "InvitePK",
	2: "InPKing",
	5: "ChoseInteraction",
	6: "Interaction",
}
var PgcChannelPKBattle_Status_value = map[string]int32{
	"NotPK":            0,
	"InvitePK":         1,
	"InPKing":          2,
	"ChoseInteraction": 5,
	"Interaction":      6,
}

func (x PgcChannelPKBattle_Status) String() string {
	return proto.EnumName(PgcChannelPKBattle_Status_name, int32(x))
}
func (PgcChannelPKBattle_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{7, 0}
}

type PgcChannelPKMemResult_ResultType int32

const (
	PgcChannelPKMemResult_Unknown PgcChannelPKMemResult_ResultType = 0
	PgcChannelPKMemResult_Win     PgcChannelPKMemResult_ResultType = 1
	PgcChannelPKMemResult_Draw    PgcChannelPKMemResult_ResultType = 2
	PgcChannelPKMemResult_Loss    PgcChannelPKMemResult_ResultType = 3
)

var PgcChannelPKMemResult_ResultType_name = map[int32]string{
	0: "Unknown",
	1: "Win",
	2: "Draw",
	3: "Loss",
}
var PgcChannelPKMemResult_ResultType_value = map[string]int32{
	"Unknown": 0,
	"Win":     1,
	"Draw":    2,
	"Loss":    3,
}

func (x PgcChannelPKMemResult_ResultType) String() string {
	return proto.EnumName(PgcChannelPKMemResult_ResultType_name, int32(x))
}
func (PgcChannelPKMemResult_ResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{8, 0}
}

type PgcChannelPKResultOpt_EndType int32

const (
	PgcChannelPKResultOpt_Common    PgcChannelPKResultOpt_EndType = 0
	PgcChannelPKResultOpt_QuickKill PgcChannelPKResultOpt_EndType = 1
)

var PgcChannelPKResultOpt_EndType_name = map[int32]string{
	0: "Common",
	1: "QuickKill",
}
var PgcChannelPKResultOpt_EndType_value = map[string]int32{
	"Common":    0,
	"QuickKill": 1,
}

func (x PgcChannelPKResultOpt_EndType) String() string {
	return proto.EnumName(PgcChannelPKResultOpt_EndType_name, int32(x))
}
func (PgcChannelPKResultOpt_EndType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{9, 0}
}

type PgcChannelPKChannelInfo_Status int32

const (
	PgcChannelPKChannelInfo_NotPK PgcChannelPKChannelInfo_Status = 0
	PgcChannelPKChannelInfo_InPK  PgcChannelPKChannelInfo_Status = 1
)

var PgcChannelPKChannelInfo_Status_name = map[int32]string{
	0: "NotPK",
	1: "InPK",
}
var PgcChannelPKChannelInfo_Status_value = map[string]int32{
	"NotPK": 0,
	"InPK":  1,
}

func (x PgcChannelPKChannelInfo_Status) String() string {
	return proto.EnumName(PgcChannelPKChannelInfo_Status_name, int32(x))
}
func (PgcChannelPKChannelInfo_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{13, 0}
}

// PgcChannelPK用户简略信息
type PgcChannelPKAnchor struct {
	UserProfile          *app.UserProfile `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PgcChannelPKAnchor) Reset()         { *m = PgcChannelPKAnchor{} }
func (m *PgcChannelPKAnchor) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKAnchor) ProtoMessage()    {}
func (*PgcChannelPKAnchor) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{0}
}
func (m *PgcChannelPKAnchor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKAnchor.Unmarshal(m, b)
}
func (m *PgcChannelPKAnchor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKAnchor.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKAnchor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKAnchor.Merge(dst, src)
}
func (m *PgcChannelPKAnchor) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKAnchor.Size(m)
}
func (m *PgcChannelPKAnchor) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKAnchor.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKAnchor proto.InternalMessageInfo

func (m *PgcChannelPKAnchor) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

// 麦上收礼1-3名跟送礼者用户连线
type PgcChannelPKMicRankInfo struct {
	MicUser              *PgcChannelPKAnchor `protobuf:"bytes,1,opt,name=mic_user,json=micUser,proto3" json:"mic_user,omitempty"`
	FromUser             *PgcChannelPKAnchor `protobuf:"bytes,2,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	Score                uint32              `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	Rank                 uint32              `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PgcChannelPKMicRankInfo) Reset()         { *m = PgcChannelPKMicRankInfo{} }
func (m *PgcChannelPKMicRankInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKMicRankInfo) ProtoMessage()    {}
func (*PgcChannelPKMicRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{1}
}
func (m *PgcChannelPKMicRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKMicRankInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKMicRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKMicRankInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKMicRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKMicRankInfo.Merge(dst, src)
}
func (m *PgcChannelPKMicRankInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKMicRankInfo.Size(m)
}
func (m *PgcChannelPKMicRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKMicRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKMicRankInfo proto.InternalMessageInfo

func (m *PgcChannelPKMicRankInfo) GetMicUser() *PgcChannelPKAnchor {
	if m != nil {
		return m.MicUser
	}
	return nil
}

func (m *PgcChannelPKMicRankInfo) GetFromUser() *PgcChannelPKAnchor {
	if m != nil {
		return m.FromUser
	}
	return nil
}

func (m *PgcChannelPKMicRankInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PgcChannelPKMicRankInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

// PK信息
type PgcChannelPKInfo struct {
	ChannelInfo          *PgcChannelPKChannelInfo   `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Score                uint32                     `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	TopAnchorList        []*PgcChannelPKAnchor      `protobuf:"bytes,3,rep,name=top_anchor_list,json=topAnchorList,proto3" json:"top_anchor_list,omitempty"`
	MvpInfo              []*PgcChannelPKMicRankInfo `protobuf:"bytes,4,rep,name=mvp_info,json=mvpInfo,proto3" json:"mvp_info,omitempty"`
	ChannelClientId      string                     `protobuf:"bytes,5,opt,name=channel_client_id,json=channelClientId,proto3" json:"channel_client_id,omitempty"`
	MinScoreText         string                     `protobuf:"bytes,6,opt,name=min_score_text,json=minScoreText,proto3" json:"min_score_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PgcChannelPKInfo) Reset()         { *m = PgcChannelPKInfo{} }
func (m *PgcChannelPKInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKInfo) ProtoMessage()    {}
func (*PgcChannelPKInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{2}
}
func (m *PgcChannelPKInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKInfo.Merge(dst, src)
}
func (m *PgcChannelPKInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKInfo.Size(m)
}
func (m *PgcChannelPKInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKInfo proto.InternalMessageInfo

func (m *PgcChannelPKInfo) GetChannelInfo() *PgcChannelPKChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *PgcChannelPKInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PgcChannelPKInfo) GetTopAnchorList() []*PgcChannelPKAnchor {
	if m != nil {
		return m.TopAnchorList
	}
	return nil
}

func (m *PgcChannelPKInfo) GetMvpInfo() []*PgcChannelPKMicRankInfo {
	if m != nil {
		return m.MvpInfo
	}
	return nil
}

func (m *PgcChannelPKInfo) GetChannelClientId() string {
	if m != nil {
		return m.ChannelClientId
	}
	return ""
}

func (m *PgcChannelPKInfo) GetMinScoreText() string {
	if m != nil {
		return m.MinScoreText
	}
	return ""
}

// 斩杀信息
type PgcChannelPKQuickKillInfo struct {
	QuickKillDescPrefix  string   `protobuf:"bytes,1,opt,name=quick_kill_desc_prefix,json=quickKillDescPrefix,proto3" json:"quick_kill_desc_prefix,omitempty"`
	QuickKillDesc        string   `protobuf:"bytes,2,opt,name=quick_kill_desc,json=quickKillDesc,proto3" json:"quick_kill_desc,omitempty"`
	QuickKillEndTs       uint32   `protobuf:"varint,3,opt,name=quick_kill_end_ts,json=quickKillEndTs,proto3" json:"quick_kill_end_ts,omitempty"`
	EnableMinPkSec       uint32   `protobuf:"varint,4,opt,name=enable_min_pk_sec,json=enableMinPkSec,proto3" json:"enable_min_pk_sec,omitempty"`
	EnableMaxPkSec       uint32   `protobuf:"varint,5,opt,name=enable_max_pk_sec,json=enableMaxPkSec,proto3" json:"enable_max_pk_sec,omitempty"`
	ConditionValue       uint32   `protobuf:"varint,6,opt,name=condition_value,json=conditionValue,proto3" json:"condition_value,omitempty"`
	InQuickKill          bool     `protobuf:"varint,7,opt,name=in_quick_kill,json=inQuickKill,proto3" json:"in_quick_kill,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PgcChannelPKQuickKillInfo) Reset()         { *m = PgcChannelPKQuickKillInfo{} }
func (m *PgcChannelPKQuickKillInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKQuickKillInfo) ProtoMessage()    {}
func (*PgcChannelPKQuickKillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{3}
}
func (m *PgcChannelPKQuickKillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKQuickKillInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKQuickKillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKQuickKillInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKQuickKillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKQuickKillInfo.Merge(dst, src)
}
func (m *PgcChannelPKQuickKillInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKQuickKillInfo.Size(m)
}
func (m *PgcChannelPKQuickKillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKQuickKillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKQuickKillInfo proto.InternalMessageInfo

func (m *PgcChannelPKQuickKillInfo) GetQuickKillDescPrefix() string {
	if m != nil {
		return m.QuickKillDescPrefix
	}
	return ""
}

func (m *PgcChannelPKQuickKillInfo) GetQuickKillDesc() string {
	if m != nil {
		return m.QuickKillDesc
	}
	return ""
}

func (m *PgcChannelPKQuickKillInfo) GetQuickKillEndTs() uint32 {
	if m != nil {
		return m.QuickKillEndTs
	}
	return 0
}

func (m *PgcChannelPKQuickKillInfo) GetEnableMinPkSec() uint32 {
	if m != nil {
		return m.EnableMinPkSec
	}
	return 0
}

func (m *PgcChannelPKQuickKillInfo) GetEnableMaxPkSec() uint32 {
	if m != nil {
		return m.EnableMaxPkSec
	}
	return 0
}

func (m *PgcChannelPKQuickKillInfo) GetConditionValue() uint32 {
	if m != nil {
		return m.ConditionValue
	}
	return 0
}

func (m *PgcChannelPKQuickKillInfo) GetInQuickKill() bool {
	if m != nil {
		return m.InQuickKill
	}
	return false
}

// 巅峰对决信息
type PgcChannelPKPeakInfo struct {
	PeakDesc             string   `protobuf:"bytes,1,opt,name=peak_desc,json=peakDesc,proto3" json:"peak_desc,omitempty"`
	InPeakPk             bool     `protobuf:"varint,2,opt,name=in_peak_pk,json=inPeakPk,proto3" json:"in_peak_pk,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PgcChannelPKPeakInfo) Reset()         { *m = PgcChannelPKPeakInfo{} }
func (m *PgcChannelPKPeakInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKPeakInfo) ProtoMessage()    {}
func (*PgcChannelPKPeakInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{4}
}
func (m *PgcChannelPKPeakInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKPeakInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKPeakInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKPeakInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKPeakInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKPeakInfo.Merge(dst, src)
}
func (m *PgcChannelPKPeakInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKPeakInfo.Size(m)
}
func (m *PgcChannelPKPeakInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKPeakInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKPeakInfo proto.InternalMessageInfo

func (m *PgcChannelPKPeakInfo) GetPeakDesc() string {
	if m != nil {
		return m.PeakDesc
	}
	return ""
}

func (m *PgcChannelPKPeakInfo) GetInPeakPk() bool {
	if m != nil {
		return m.InPeakPk
	}
	return false
}

// 斩杀变更推送opt
type PgcChannelPKQuickKillChangeOpt struct {
	EventType            uint32                     `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	TriggerChannelId     uint32                     `protobuf:"varint,2,opt,name=trigger_channel_id,json=triggerChannelId,proto3" json:"trigger_channel_id,omitempty"`
	KillChannelId        uint32                     `protobuf:"varint,3,opt,name=kill_channel_id,json=killChannelId,proto3" json:"kill_channel_id,omitempty"`
	QuickKillInfo        *PgcChannelPKQuickKillInfo `protobuf:"bytes,4,opt,name=quick_kill_info,json=quickKillInfo,proto3" json:"quick_kill_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PgcChannelPKQuickKillChangeOpt) Reset()         { *m = PgcChannelPKQuickKillChangeOpt{} }
func (m *PgcChannelPKQuickKillChangeOpt) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKQuickKillChangeOpt) ProtoMessage()    {}
func (*PgcChannelPKQuickKillChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{5}
}
func (m *PgcChannelPKQuickKillChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKQuickKillChangeOpt.Unmarshal(m, b)
}
func (m *PgcChannelPKQuickKillChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKQuickKillChangeOpt.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKQuickKillChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKQuickKillChangeOpt.Merge(dst, src)
}
func (m *PgcChannelPKQuickKillChangeOpt) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKQuickKillChangeOpt.Size(m)
}
func (m *PgcChannelPKQuickKillChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKQuickKillChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKQuickKillChangeOpt proto.InternalMessageInfo

func (m *PgcChannelPKQuickKillChangeOpt) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *PgcChannelPKQuickKillChangeOpt) GetTriggerChannelId() uint32 {
	if m != nil {
		return m.TriggerChannelId
	}
	return 0
}

func (m *PgcChannelPKQuickKillChangeOpt) GetKillChannelId() uint32 {
	if m != nil {
		return m.KillChannelId
	}
	return 0
}

func (m *PgcChannelPKQuickKillChangeOpt) GetQuickKillInfo() *PgcChannelPKQuickKillInfo {
	if m != nil {
		return m.QuickKillInfo
	}
	return nil
}

// 巅峰对决变更推送opt
type PgcChannelPKPeakChangeOpt struct {
	AwardChannelId       uint32                `protobuf:"varint,1,opt,name=award_channel_id,json=awardChannelId,proto3" json:"award_channel_id,omitempty"`
	AwardPkValue         uint32                `protobuf:"varint,2,opt,name=award_pk_value,json=awardPkValue,proto3" json:"award_pk_value,omitempty"`
	DurationDesc         string                `protobuf:"bytes,3,opt,name=duration_desc,json=durationDesc,proto3" json:"duration_desc,omitempty"`
	PeakInfo             *PgcChannelPKPeakInfo `protobuf:"bytes,4,opt,name=peak_info,json=peakInfo,proto3" json:"peak_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *PgcChannelPKPeakChangeOpt) Reset()         { *m = PgcChannelPKPeakChangeOpt{} }
func (m *PgcChannelPKPeakChangeOpt) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKPeakChangeOpt) ProtoMessage()    {}
func (*PgcChannelPKPeakChangeOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{6}
}
func (m *PgcChannelPKPeakChangeOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKPeakChangeOpt.Unmarshal(m, b)
}
func (m *PgcChannelPKPeakChangeOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKPeakChangeOpt.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKPeakChangeOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKPeakChangeOpt.Merge(dst, src)
}
func (m *PgcChannelPKPeakChangeOpt) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKPeakChangeOpt.Size(m)
}
func (m *PgcChannelPKPeakChangeOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKPeakChangeOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKPeakChangeOpt proto.InternalMessageInfo

func (m *PgcChannelPKPeakChangeOpt) GetAwardChannelId() uint32 {
	if m != nil {
		return m.AwardChannelId
	}
	return 0
}

func (m *PgcChannelPKPeakChangeOpt) GetAwardPkValue() uint32 {
	if m != nil {
		return m.AwardPkValue
	}
	return 0
}

func (m *PgcChannelPKPeakChangeOpt) GetDurationDesc() string {
	if m != nil {
		return m.DurationDesc
	}
	return ""
}

func (m *PgcChannelPKPeakChangeOpt) GetPeakInfo() *PgcChannelPKPeakInfo {
	if m != nil {
		return m.PeakInfo
	}
	return nil
}

// PK实时战况
type PgcChannelPKBattle struct {
	PkList               []*PgcChannelPKInfo `protobuf:"bytes,1,rep,name=pk_list,json=pkList,proto3" json:"pk_list,omitempty"`
	PkEndTs              uint32              `protobuf:"varint,2,opt,name=pk_end_ts,json=pkEndTs,proto3" json:"pk_end_ts,omitempty"`
	ServerNs             int64               `protobuf:"varint,3,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	ValidPkDesc          string              `protobuf:"bytes,4,opt,name=valid_pk_desc,json=validPkDesc,proto3" json:"valid_pk_desc,omitempty"`
	ValidPk              bool                `protobuf:"varint,5,opt,name=valid_pk,json=validPk,proto3" json:"valid_pk,omitempty"`
	Phase                uint32              `protobuf:"varint,6,opt,name=phase,proto3" json:"phase,omitempty"`
	PkId                 uint32              `protobuf:"varint,7,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	StatusEndTs          uint32              `protobuf:"varint,8,opt,name=status_end_ts,json=statusEndTs,proto3" json:"status_end_ts,omitempty"`
	StatusDesc           string              `protobuf:"bytes,9,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PgcChannelPKBattle) Reset()         { *m = PgcChannelPKBattle{} }
func (m *PgcChannelPKBattle) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKBattle) ProtoMessage()    {}
func (*PgcChannelPKBattle) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{7}
}
func (m *PgcChannelPKBattle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKBattle.Unmarshal(m, b)
}
func (m *PgcChannelPKBattle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKBattle.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKBattle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKBattle.Merge(dst, src)
}
func (m *PgcChannelPKBattle) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKBattle.Size(m)
}
func (m *PgcChannelPKBattle) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKBattle.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKBattle proto.InternalMessageInfo

func (m *PgcChannelPKBattle) GetPkList() []*PgcChannelPKInfo {
	if m != nil {
		return m.PkList
	}
	return nil
}

func (m *PgcChannelPKBattle) GetPkEndTs() uint32 {
	if m != nil {
		return m.PkEndTs
	}
	return 0
}

func (m *PgcChannelPKBattle) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

func (m *PgcChannelPKBattle) GetValidPkDesc() string {
	if m != nil {
		return m.ValidPkDesc
	}
	return ""
}

func (m *PgcChannelPKBattle) GetValidPk() bool {
	if m != nil {
		return m.ValidPk
	}
	return false
}

func (m *PgcChannelPKBattle) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *PgcChannelPKBattle) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *PgcChannelPKBattle) GetStatusEndTs() uint32 {
	if m != nil {
		return m.StatusEndTs
	}
	return 0
}

func (m *PgcChannelPKBattle) GetStatusDesc() string {
	if m != nil {
		return m.StatusDesc
	}
	return ""
}

// PK单方结果
type PgcChannelPKMemResult struct {
	PkInfo               *PgcChannelPKInfo          `protobuf:"bytes,1,opt,name=pk_info,json=pkInfo,proto3" json:"pk_info,omitempty"`
	ResultType           uint32                     `protobuf:"varint,2,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	ServerNs             int64                      `protobuf:"varint,3,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	MvpExpired           uint32                     `protobuf:"varint,4,opt,name=mvp_expired,json=mvpExpired,proto3" json:"mvp_expired,omitempty"`
	EndInfo              []*PgcChannelPKMicRankInfo `protobuf:"bytes,5,rep,name=end_info,json=endInfo,proto3" json:"end_info,omitempty"`
	EndMvp               []*PgcChannelPKMicRankInfo `protobuf:"bytes,6,rep,name=end_mvp,json=endMvp,proto3" json:"end_mvp,omitempty"`
	LastMvp              []*PgcChannelPKMicRankInfo `protobuf:"bytes,7,rep,name=last_mvp,json=lastMvp,proto3" json:"last_mvp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PgcChannelPKMemResult) Reset()         { *m = PgcChannelPKMemResult{} }
func (m *PgcChannelPKMemResult) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKMemResult) ProtoMessage()    {}
func (*PgcChannelPKMemResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{8}
}
func (m *PgcChannelPKMemResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKMemResult.Unmarshal(m, b)
}
func (m *PgcChannelPKMemResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKMemResult.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKMemResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKMemResult.Merge(dst, src)
}
func (m *PgcChannelPKMemResult) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKMemResult.Size(m)
}
func (m *PgcChannelPKMemResult) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKMemResult.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKMemResult proto.InternalMessageInfo

func (m *PgcChannelPKMemResult) GetPkInfo() *PgcChannelPKInfo {
	if m != nil {
		return m.PkInfo
	}
	return nil
}

func (m *PgcChannelPKMemResult) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

func (m *PgcChannelPKMemResult) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

func (m *PgcChannelPKMemResult) GetMvpExpired() uint32 {
	if m != nil {
		return m.MvpExpired
	}
	return 0
}

func (m *PgcChannelPKMemResult) GetEndInfo() []*PgcChannelPKMicRankInfo {
	if m != nil {
		return m.EndInfo
	}
	return nil
}

func (m *PgcChannelPKMemResult) GetEndMvp() []*PgcChannelPKMicRankInfo {
	if m != nil {
		return m.EndMvp
	}
	return nil
}

func (m *PgcChannelPKMemResult) GetLastMvp() []*PgcChannelPKMicRankInfo {
	if m != nil {
		return m.LastMvp
	}
	return nil
}

// PK结果页push
type PgcChannelPKResultOpt struct {
	MemList              []*PgcChannelPKMemResult `protobuf:"bytes,1,rep,name=mem_list,json=memList,proto3" json:"mem_list,omitempty"`
	Invalid              bool                     `protobuf:"varint,2,opt,name=invalid,proto3" json:"invalid,omitempty"`
	EndType              uint32                   `protobuf:"varint,3,opt,name=end_type,json=endType,proto3" json:"end_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PgcChannelPKResultOpt) Reset()         { *m = PgcChannelPKResultOpt{} }
func (m *PgcChannelPKResultOpt) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKResultOpt) ProtoMessage()    {}
func (*PgcChannelPKResultOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{9}
}
func (m *PgcChannelPKResultOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKResultOpt.Unmarshal(m, b)
}
func (m *PgcChannelPKResultOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKResultOpt.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKResultOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKResultOpt.Merge(dst, src)
}
func (m *PgcChannelPKResultOpt) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKResultOpt.Size(m)
}
func (m *PgcChannelPKResultOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKResultOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKResultOpt proto.InternalMessageInfo

func (m *PgcChannelPKResultOpt) GetMemList() []*PgcChannelPKMemResult {
	if m != nil {
		return m.MemList
	}
	return nil
}

func (m *PgcChannelPKResultOpt) GetInvalid() bool {
	if m != nil {
		return m.Invalid
	}
	return false
}

func (m *PgcChannelPKResultOpt) GetEndType() uint32 {
	if m != nil {
		return m.EndType
	}
	return 0
}

// 获取PK入口
type GetPgcChannelPKEntryReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPgcChannelPKEntryReq) Reset()         { *m = GetPgcChannelPKEntryReq{} }
func (m *GetPgcChannelPKEntryReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKEntryReq) ProtoMessage()    {}
func (*GetPgcChannelPKEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{10}
}
func (m *GetPgcChannelPKEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKEntryReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKEntryReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKEntryReq.Merge(dst, src)
}
func (m *GetPgcChannelPKEntryReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKEntryReq.Size(m)
}
func (m *GetPgcChannelPKEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKEntryReq proto.InternalMessageInfo

func (m *GetPgcChannelPKEntryReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPgcChannelPKEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 获取PK入口响应
type GetPgcChannelPKEntryResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	HasEntry             bool          `protobuf:"varint,3,opt,name=has_entry,json=hasEntry,proto3" json:"has_entry,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPgcChannelPKEntryResp) Reset()         { *m = GetPgcChannelPKEntryResp{} }
func (m *GetPgcChannelPKEntryResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKEntryResp) ProtoMessage()    {}
func (*GetPgcChannelPKEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{11}
}
func (m *GetPgcChannelPKEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKEntryResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKEntryResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKEntryResp.Merge(dst, src)
}
func (m *GetPgcChannelPKEntryResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKEntryResp.Size(m)
}
func (m *GetPgcChannelPKEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKEntryResp proto.InternalMessageInfo

func (m *GetPgcChannelPKEntryResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPgcChannelPKEntryResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKEntryResp) GetHasEntry() bool {
	if m != nil {
		return m.HasEntry
	}
	return false
}

// 获取PK房间列表
type GetPgcChannelPKChannelListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GuildId              uint32       `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPgcChannelPKChannelListReq) Reset()         { *m = GetPgcChannelPKChannelListReq{} }
func (m *GetPgcChannelPKChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKChannelListReq) ProtoMessage()    {}
func (*GetPgcChannelPKChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{12}
}
func (m *GetPgcChannelPKChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKChannelListReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKChannelListReq.Merge(dst, src)
}
func (m *GetPgcChannelPKChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKChannelListReq.Size(m)
}
func (m *GetPgcChannelPKChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKChannelListReq proto.InternalMessageInfo

func (m *GetPgcChannelPKChannelListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPgcChannelPKChannelListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKChannelListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type PgcChannelPKChannelInfo struct {
	ChannelId            uint32                      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32                      `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelIcon          string                      `protobuf:"bytes,3,opt,name=channel_icon,json=channelIcon,proto3" json:"channel_icon,omitempty"`
	ChannelName          string                      `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	BindGuildId          uint32                      `protobuf:"varint,5,opt,name=bind_guild_id,json=bindGuildId,proto3" json:"bind_guild_id,omitempty"`
	CreatorUid           uint32                      `protobuf:"varint,6,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	AnchorUserProfile    *app.UserProfile            `protobuf:"bytes,7,opt,name=anchor_user_profile,json=anchorUserProfile,proto3" json:"anchor_user_profile,omitempty"`
	TabName              string                      `protobuf:"bytes,8,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Hot                  uint32                      `protobuf:"varint,9,opt,name=hot,proto3" json:"hot,omitempty"`
	PkStatus             uint32                      `protobuf:"varint,10,opt,name=pk_status,json=pkStatus,proto3" json:"pk_status,omitempty"`
	MicFlag              PgcChannelPKOpponentMicFlag `protobuf:"varint,11,opt,name=mic_flag,json=micFlag,proto3,enum=ga.pgc_channel_pk_logic.PgcChannelPKOpponentMicFlag" json:"mic_flag,omitempty"`
	TabColor             []string                    `protobuf:"bytes,12,rep,name=tab_color,json=tabColor,proto3" json:"tab_color,omitempty"`
	InteractionMicFlag   PgcChannelPKOpponentMicFlag `protobuf:"varint,13,opt,name=interaction_mic_flag,json=interactionMicFlag,proto3,enum=ga.pgc_channel_pk_logic.PgcChannelPKOpponentMicFlag" json:"interaction_mic_flag,omitempty"`
	InteractionUser      *PgcChannelPKMicSpace       `protobuf:"bytes,14,opt,name=interaction_user,json=interactionUser,proto3" json:"interaction_user,omitempty"`
	ResultType           uint32                      `protobuf:"varint,15,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *PgcChannelPKChannelInfo) Reset()         { *m = PgcChannelPKChannelInfo{} }
func (m *PgcChannelPKChannelInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKChannelInfo) ProtoMessage()    {}
func (*PgcChannelPKChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{13}
}
func (m *PgcChannelPKChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKChannelInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKChannelInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKChannelInfo.Merge(dst, src)
}
func (m *PgcChannelPKChannelInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKChannelInfo.Size(m)
}
func (m *PgcChannelPKChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKChannelInfo proto.InternalMessageInfo

func (m *PgcChannelPKChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetChannelIcon() string {
	if m != nil {
		return m.ChannelIcon
	}
	return ""
}

func (m *PgcChannelPKChannelInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PgcChannelPKChannelInfo) GetBindGuildId() uint32 {
	if m != nil {
		return m.BindGuildId
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetAnchorUserProfile() *app.UserProfile {
	if m != nil {
		return m.AnchorUserProfile
	}
	return nil
}

func (m *PgcChannelPKChannelInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *PgcChannelPKChannelInfo) GetHot() uint32 {
	if m != nil {
		return m.Hot
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetPkStatus() uint32 {
	if m != nil {
		return m.PkStatus
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetMicFlag() PgcChannelPKOpponentMicFlag {
	if m != nil {
		return m.MicFlag
	}
	return PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN
}

func (m *PgcChannelPKChannelInfo) GetTabColor() []string {
	if m != nil {
		return m.TabColor
	}
	return nil
}

func (m *PgcChannelPKChannelInfo) GetInteractionMicFlag() PgcChannelPKOpponentMicFlag {
	if m != nil {
		return m.InteractionMicFlag
	}
	return PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN
}

func (m *PgcChannelPKChannelInfo) GetInteractionUser() *PgcChannelPKMicSpace {
	if m != nil {
		return m.InteractionUser
	}
	return nil
}

func (m *PgcChannelPKChannelInfo) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

type GetPgcChannelPKChannelListResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32                     `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GuildId              uint32                     `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelList          []*PgcChannelPKChannelInfo `protobuf:"bytes,4,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	PkSwitch             uint32                     `protobuf:"varint,5,opt,name=pk_switch,json=pkSwitch,proto3" json:"pk_switch,omitempty"`
	PkLimitText          string                     `protobuf:"bytes,6,opt,name=pk_limit_text,json=pkLimitText,proto3" json:"pk_limit_text,omitempty"`
	LimitPk              bool                       `protobuf:"varint,7,opt,name=limit_pk,json=limitPk,proto3" json:"limit_pk,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetPgcChannelPKChannelListResp) Reset()         { *m = GetPgcChannelPKChannelListResp{} }
func (m *GetPgcChannelPKChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKChannelListResp) ProtoMessage()    {}
func (*GetPgcChannelPKChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{14}
}
func (m *GetPgcChannelPKChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKChannelListResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKChannelListResp.Merge(dst, src)
}
func (m *GetPgcChannelPKChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKChannelListResp.Size(m)
}
func (m *GetPgcChannelPKChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKChannelListResp proto.InternalMessageInfo

func (m *GetPgcChannelPKChannelListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPgcChannelPKChannelListResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKChannelListResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetPgcChannelPKChannelListResp) GetChannelList() []*PgcChannelPKChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetPgcChannelPKChannelListResp) GetPkSwitch() uint32 {
	if m != nil {
		return m.PkSwitch
	}
	return 0
}

func (m *GetPgcChannelPKChannelListResp) GetPkLimitText() string {
	if m != nil {
		return m.PkLimitText
	}
	return ""
}

func (m *GetPgcChannelPKChannelListResp) GetLimitPk() bool {
	if m != nil {
		return m.LimitPk
	}
	return false
}

// 切换PK状态
type SetPgcChannelPKSwitchReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkSwitch             uint32       `protobuf:"varint,3,opt,name=pk_switch,json=pkSwitch,proto3" json:"pk_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetPgcChannelPKSwitchReq) Reset()         { *m = SetPgcChannelPKSwitchReq{} }
func (m *SetPgcChannelPKSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKSwitchReq) ProtoMessage()    {}
func (*SetPgcChannelPKSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{15}
}
func (m *SetPgcChannelPKSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKSwitchReq.Unmarshal(m, b)
}
func (m *SetPgcChannelPKSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKSwitchReq.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKSwitchReq.Merge(dst, src)
}
func (m *SetPgcChannelPKSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKSwitchReq.Size(m)
}
func (m *SetPgcChannelPKSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKSwitchReq proto.InternalMessageInfo

func (m *SetPgcChannelPKSwitchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetPgcChannelPKSwitchReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPgcChannelPKSwitchReq) GetPkSwitch() uint32 {
	if m != nil {
		return m.PkSwitch
	}
	return 0
}

type SetPgcChannelPKSwitchResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkSwitch             uint32        `protobuf:"varint,3,opt,name=pk_switch,json=pkSwitch,proto3" json:"pk_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetPgcChannelPKSwitchResp) Reset()         { *m = SetPgcChannelPKSwitchResp{} }
func (m *SetPgcChannelPKSwitchResp) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKSwitchResp) ProtoMessage()    {}
func (*SetPgcChannelPKSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{16}
}
func (m *SetPgcChannelPKSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKSwitchResp.Unmarshal(m, b)
}
func (m *SetPgcChannelPKSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKSwitchResp.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKSwitchResp.Merge(dst, src)
}
func (m *SetPgcChannelPKSwitchResp) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKSwitchResp.Size(m)
}
func (m *SetPgcChannelPKSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKSwitchResp proto.InternalMessageInfo

func (m *SetPgcChannelPKSwitchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SetPgcChannelPKSwitchResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPgcChannelPKSwitchResp) GetPkSwitch() uint32 {
	if m != nil {
		return m.PkSwitch
	}
	return 0
}

// 发起PK
type StartPgcChannelPKReq struct {
	BaseReq              *app.BaseReq             `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FromChannelId        uint32                   `protobuf:"varint,2,opt,name=from_channel_id,json=fromChannelId,proto3" json:"from_channel_id,omitempty"`
	ToChannelInfo        *PgcChannelPKChannelInfo `protobuf:"bytes,3,opt,name=to_channel_info,json=toChannelInfo,proto3" json:"to_channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *StartPgcChannelPKReq) Reset()         { *m = StartPgcChannelPKReq{} }
func (m *StartPgcChannelPKReq) String() string { return proto.CompactTextString(m) }
func (*StartPgcChannelPKReq) ProtoMessage()    {}
func (*StartPgcChannelPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{17}
}
func (m *StartPgcChannelPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartPgcChannelPKReq.Unmarshal(m, b)
}
func (m *StartPgcChannelPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartPgcChannelPKReq.Marshal(b, m, deterministic)
}
func (dst *StartPgcChannelPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartPgcChannelPKReq.Merge(dst, src)
}
func (m *StartPgcChannelPKReq) XXX_Size() int {
	return xxx_messageInfo_StartPgcChannelPKReq.Size(m)
}
func (m *StartPgcChannelPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartPgcChannelPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartPgcChannelPKReq proto.InternalMessageInfo

func (m *StartPgcChannelPKReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartPgcChannelPKReq) GetFromChannelId() uint32 {
	if m != nil {
		return m.FromChannelId
	}
	return 0
}

func (m *StartPgcChannelPKReq) GetToChannelInfo() *PgcChannelPKChannelInfo {
	if m != nil {
		return m.ToChannelInfo
	}
	return nil
}

type StartPgcChannelPKResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartPgcChannelPKResp) Reset()         { *m = StartPgcChannelPKResp{} }
func (m *StartPgcChannelPKResp) String() string { return proto.CompactTextString(m) }
func (*StartPgcChannelPKResp) ProtoMessage()    {}
func (*StartPgcChannelPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{18}
}
func (m *StartPgcChannelPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartPgcChannelPKResp.Unmarshal(m, b)
}
func (m *StartPgcChannelPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartPgcChannelPKResp.Marshal(b, m, deterministic)
}
func (dst *StartPgcChannelPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartPgcChannelPKResp.Merge(dst, src)
}
func (m *StartPgcChannelPKResp) XXX_Size() int {
	return xxx_messageInfo_StartPgcChannelPKResp.Size(m)
}
func (m *StartPgcChannelPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartPgcChannelPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartPgcChannelPKResp proto.InternalMessageInfo

func (m *StartPgcChannelPKResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 接受方收到PK邀请push
type PgcChannelPKInviteOpt struct {
	FromChannel          *PgcChannelPKChannelInfo `protobuf:"bytes,1,opt,name=from_channel,json=fromChannel,proto3" json:"from_channel,omitempty"`
	ExpiredSec           uint32                   `protobuf:"varint,2,opt,name=expired_sec,json=expiredSec,proto3" json:"expired_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PgcChannelPKInviteOpt) Reset()         { *m = PgcChannelPKInviteOpt{} }
func (m *PgcChannelPKInviteOpt) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKInviteOpt) ProtoMessage()    {}
func (*PgcChannelPKInviteOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{19}
}
func (m *PgcChannelPKInviteOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKInviteOpt.Unmarshal(m, b)
}
func (m *PgcChannelPKInviteOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKInviteOpt.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKInviteOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKInviteOpt.Merge(dst, src)
}
func (m *PgcChannelPKInviteOpt) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKInviteOpt.Size(m)
}
func (m *PgcChannelPKInviteOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKInviteOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKInviteOpt proto.InternalMessageInfo

func (m *PgcChannelPKInviteOpt) GetFromChannel() *PgcChannelPKChannelInfo {
	if m != nil {
		return m.FromChannel
	}
	return nil
}

func (m *PgcChannelPKInviteOpt) GetExpiredSec() uint32 {
	if m != nil {
		return m.ExpiredSec
	}
	return 0
}

// 接受方处理收到PK邀请
type AcceptPgcChannelPKReq struct {
	BaseReq              *app.BaseReq             `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	MyChannelId          uint32                   `protobuf:"varint,2,opt,name=my_channel_id,json=myChannelId,proto3" json:"my_channel_id,omitempty"`
	FromChannelId        *PgcChannelPKChannelInfo `protobuf:"bytes,3,opt,name=from_channel_id,json=fromChannelId,proto3" json:"from_channel_id,omitempty"`
	Accept               bool                     `protobuf:"varint,4,opt,name=accept,proto3" json:"accept,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AcceptPgcChannelPKReq) Reset()         { *m = AcceptPgcChannelPKReq{} }
func (m *AcceptPgcChannelPKReq) String() string { return proto.CompactTextString(m) }
func (*AcceptPgcChannelPKReq) ProtoMessage()    {}
func (*AcceptPgcChannelPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{20}
}
func (m *AcceptPgcChannelPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptPgcChannelPKReq.Unmarshal(m, b)
}
func (m *AcceptPgcChannelPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptPgcChannelPKReq.Marshal(b, m, deterministic)
}
func (dst *AcceptPgcChannelPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptPgcChannelPKReq.Merge(dst, src)
}
func (m *AcceptPgcChannelPKReq) XXX_Size() int {
	return xxx_messageInfo_AcceptPgcChannelPKReq.Size(m)
}
func (m *AcceptPgcChannelPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptPgcChannelPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptPgcChannelPKReq proto.InternalMessageInfo

func (m *AcceptPgcChannelPKReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AcceptPgcChannelPKReq) GetMyChannelId() uint32 {
	if m != nil {
		return m.MyChannelId
	}
	return 0
}

func (m *AcceptPgcChannelPKReq) GetFromChannelId() *PgcChannelPKChannelInfo {
	if m != nil {
		return m.FromChannelId
	}
	return nil
}

func (m *AcceptPgcChannelPKReq) GetAccept() bool {
	if m != nil {
		return m.Accept
	}
	return false
}

type AcceptPgcChannelPKResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AcceptPgcChannelPKResp) Reset()         { *m = AcceptPgcChannelPKResp{} }
func (m *AcceptPgcChannelPKResp) String() string { return proto.CompactTextString(m) }
func (*AcceptPgcChannelPKResp) ProtoMessage()    {}
func (*AcceptPgcChannelPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{21}
}
func (m *AcceptPgcChannelPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptPgcChannelPKResp.Unmarshal(m, b)
}
func (m *AcceptPgcChannelPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptPgcChannelPKResp.Marshal(b, m, deterministic)
}
func (dst *AcceptPgcChannelPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptPgcChannelPKResp.Merge(dst, src)
}
func (m *AcceptPgcChannelPKResp) XXX_Size() int {
	return xxx_messageInfo_AcceptPgcChannelPKResp.Size(m)
}
func (m *AcceptPgcChannelPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptPgcChannelPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptPgcChannelPKResp proto.InternalMessageInfo

func (m *AcceptPgcChannelPKResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 拒绝邀请单播给发起方, 接受邀请房间广播
type PgcChannelPKInviteRespOpt struct {
	FromChannel          *PgcChannelPKChannelInfo `protobuf:"bytes,1,opt,name=from_channel,json=fromChannel,proto3" json:"from_channel,omitempty"`
	ToChannel            *PgcChannelPKChannelInfo `protobuf:"bytes,2,opt,name=to_channel,json=toChannel,proto3" json:"to_channel,omitempty"`
	Accept               bool                     `protobuf:"varint,3,opt,name=accept,proto3" json:"accept,omitempty"`
	RejectedText         string                   `protobuf:"bytes,4,opt,name=rejected_text,json=rejectedText,proto3" json:"rejected_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *PgcChannelPKInviteRespOpt) Reset()         { *m = PgcChannelPKInviteRespOpt{} }
func (m *PgcChannelPKInviteRespOpt) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKInviteRespOpt) ProtoMessage()    {}
func (*PgcChannelPKInviteRespOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{22}
}
func (m *PgcChannelPKInviteRespOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKInviteRespOpt.Unmarshal(m, b)
}
func (m *PgcChannelPKInviteRespOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKInviteRespOpt.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKInviteRespOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKInviteRespOpt.Merge(dst, src)
}
func (m *PgcChannelPKInviteRespOpt) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKInviteRespOpt.Size(m)
}
func (m *PgcChannelPKInviteRespOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKInviteRespOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKInviteRespOpt proto.InternalMessageInfo

func (m *PgcChannelPKInviteRespOpt) GetFromChannel() *PgcChannelPKChannelInfo {
	if m != nil {
		return m.FromChannel
	}
	return nil
}

func (m *PgcChannelPKInviteRespOpt) GetToChannel() *PgcChannelPKChannelInfo {
	if m != nil {
		return m.ToChannel
	}
	return nil
}

func (m *PgcChannelPKInviteRespOpt) GetAccept() bool {
	if m != nil {
		return m.Accept
	}
	return false
}

func (m *PgcChannelPKInviteRespOpt) GetRejectedText() string {
	if m != nil {
		return m.RejectedText
	}
	return ""
}

// 获取房间PK信息
type GetPgcChannelPKInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPgcChannelPKInfoReq) Reset()         { *m = GetPgcChannelPKInfoReq{} }
func (m *GetPgcChannelPKInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKInfoReq) ProtoMessage()    {}
func (*GetPgcChannelPKInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{23}
}
func (m *GetPgcChannelPKInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKInfoReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKInfoReq.Merge(dst, src)
}
func (m *GetPgcChannelPKInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKInfoReq.Size(m)
}
func (m *GetPgcChannelPKInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKInfoReq proto.InternalMessageInfo

func (m *GetPgcChannelPKInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPgcChannelPKInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DownloadSourceConf struct {
	MinScore             uint32                  `protobuf:"varint,1,opt,name=min_score,json=minScore,proto3" json:"min_score,omitempty"`
	MaxScore             uint32                  `protobuf:"varint,2,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	Source               *app.DownloadSourceInfo `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	BackgroundPic        string                  `protobuf:"bytes,4,opt,name=background_pic,json=backgroundPic,proto3" json:"background_pic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *DownloadSourceConf) Reset()         { *m = DownloadSourceConf{} }
func (m *DownloadSourceConf) String() string { return proto.CompactTextString(m) }
func (*DownloadSourceConf) ProtoMessage()    {}
func (*DownloadSourceConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{24}
}
func (m *DownloadSourceConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownloadSourceConf.Unmarshal(m, b)
}
func (m *DownloadSourceConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownloadSourceConf.Marshal(b, m, deterministic)
}
func (dst *DownloadSourceConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownloadSourceConf.Merge(dst, src)
}
func (m *DownloadSourceConf) XXX_Size() int {
	return xxx_messageInfo_DownloadSourceConf.Size(m)
}
func (m *DownloadSourceConf) XXX_DiscardUnknown() {
	xxx_messageInfo_DownloadSourceConf.DiscardUnknown(m)
}

var xxx_messageInfo_DownloadSourceConf proto.InternalMessageInfo

func (m *DownloadSourceConf) GetMinScore() uint32 {
	if m != nil {
		return m.MinScore
	}
	return 0
}

func (m *DownloadSourceConf) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

func (m *DownloadSourceConf) GetSource() *app.DownloadSourceInfo {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *DownloadSourceConf) GetBackgroundPic() string {
	if m != nil {
		return m.BackgroundPic
	}
	return ""
}

type ResultSourceConf struct {
	MinScore             uint32                  `protobuf:"varint,1,opt,name=min_score,json=minScore,proto3" json:"min_score,omitempty"`
	MaxScore             uint32                  `protobuf:"varint,2,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	SourceWithMvp        *app.DownloadSourceInfo `protobuf:"bytes,3,opt,name=source_with_mvp,json=sourceWithMvp,proto3" json:"source_with_mvp,omitempty"`
	SourceWithoutMvp     *app.DownloadSourceInfo `protobuf:"bytes,4,opt,name=source_without_mvp,json=sourceWithoutMvp,proto3" json:"source_without_mvp,omitempty"`
	Rank                 uint32                  `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	TitleSource          *app.DownloadSourceInfo `protobuf:"bytes,6,opt,name=title_source,json=titleSource,proto3" json:"title_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ResultSourceConf) Reset()         { *m = ResultSourceConf{} }
func (m *ResultSourceConf) String() string { return proto.CompactTextString(m) }
func (*ResultSourceConf) ProtoMessage()    {}
func (*ResultSourceConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{25}
}
func (m *ResultSourceConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResultSourceConf.Unmarshal(m, b)
}
func (m *ResultSourceConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResultSourceConf.Marshal(b, m, deterministic)
}
func (dst *ResultSourceConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResultSourceConf.Merge(dst, src)
}
func (m *ResultSourceConf) XXX_Size() int {
	return xxx_messageInfo_ResultSourceConf.Size(m)
}
func (m *ResultSourceConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ResultSourceConf.DiscardUnknown(m)
}

var xxx_messageInfo_ResultSourceConf proto.InternalMessageInfo

func (m *ResultSourceConf) GetMinScore() uint32 {
	if m != nil {
		return m.MinScore
	}
	return 0
}

func (m *ResultSourceConf) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

func (m *ResultSourceConf) GetSourceWithMvp() *app.DownloadSourceInfo {
	if m != nil {
		return m.SourceWithMvp
	}
	return nil
}

func (m *ResultSourceConf) GetSourceWithoutMvp() *app.DownloadSourceInfo {
	if m != nil {
		return m.SourceWithoutMvp
	}
	return nil
}

func (m *ResultSourceConf) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *ResultSourceConf) GetTitleSource() *app.DownloadSourceInfo {
	if m != nil {
		return m.TitleSource
	}
	return nil
}

type HeadIconSourceConf struct {
	MinScore             uint32   `protobuf:"varint,1,opt,name=min_score,json=minScore,proto3" json:"min_score,omitempty"`
	MaxScore             uint32   `protobuf:"varint,2,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	PicList              []string `protobuf:"bytes,3,rep,name=pic_list,json=picList,proto3" json:"pic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HeadIconSourceConf) Reset()         { *m = HeadIconSourceConf{} }
func (m *HeadIconSourceConf) String() string { return proto.CompactTextString(m) }
func (*HeadIconSourceConf) ProtoMessage()    {}
func (*HeadIconSourceConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{26}
}
func (m *HeadIconSourceConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HeadIconSourceConf.Unmarshal(m, b)
}
func (m *HeadIconSourceConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HeadIconSourceConf.Marshal(b, m, deterministic)
}
func (dst *HeadIconSourceConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HeadIconSourceConf.Merge(dst, src)
}
func (m *HeadIconSourceConf) XXX_Size() int {
	return xxx_messageInfo_HeadIconSourceConf.Size(m)
}
func (m *HeadIconSourceConf) XXX_DiscardUnknown() {
	xxx_messageInfo_HeadIconSourceConf.DiscardUnknown(m)
}

var xxx_messageInfo_HeadIconSourceConf proto.InternalMessageInfo

func (m *HeadIconSourceConf) GetMinScore() uint32 {
	if m != nil {
		return m.MinScore
	}
	return 0
}

func (m *HeadIconSourceConf) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

func (m *HeadIconSourceConf) GetPicList() []string {
	if m != nil {
		return m.PicList
	}
	return nil
}

type GetPgcChannelPKInfoResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PkBattleInfo         *PgcChannelPKBattle        `protobuf:"bytes,2,opt,name=pk_battle_info,json=pkBattleInfo,proto3" json:"pk_battle_info,omitempty"`
	QuickKillInfo        *PgcChannelPKQuickKillInfo `protobuf:"bytes,3,opt,name=quick_kill_info,json=quickKillInfo,proto3" json:"quick_kill_info,omitempty"`
	PeakInfo             *PgcChannelPKPeakInfo      `protobuf:"bytes,4,opt,name=peak_info,json=peakInfo,proto3" json:"peak_info,omitempty"`
	LastMvpInfo          []*PgcChannelPKMicRankInfo `protobuf:"bytes,5,rep,name=last_mvp_info,json=lastMvpInfo,proto3" json:"last_mvp_info,omitempty"`
	LastMvpExpired       uint32                     `protobuf:"varint,6,opt,name=last_mvp_expired,json=lastMvpExpired,proto3" json:"last_mvp_expired,omitempty"`
	ResultConf           []*ResultSourceConf        `protobuf:"bytes,7,rep,name=result_conf,json=resultConf,proto3" json:"result_conf,omitempty"`
	MvpConf              []*DownloadSourceConf      `protobuf:"bytes,8,rep,name=mvp_conf,json=mvpConf,proto3" json:"mvp_conf,omitempty"`
	IconConf             []*HeadIconSourceConf      `protobuf:"bytes,9,rep,name=icon_conf,json=iconConf,proto3" json:"icon_conf,omitempty"`
	MvpIcon              []*app.DownloadSourceInfo  `protobuf:"bytes,10,rep,name=mvp_icon,json=mvpIcon,proto3" json:"mvp_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetPgcChannelPKInfoResp) Reset()         { *m = GetPgcChannelPKInfoResp{} }
func (m *GetPgcChannelPKInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKInfoResp) ProtoMessage()    {}
func (*GetPgcChannelPKInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{27}
}
func (m *GetPgcChannelPKInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKInfoResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKInfoResp.Merge(dst, src)
}
func (m *GetPgcChannelPKInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKInfoResp.Size(m)
}
func (m *GetPgcChannelPKInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKInfoResp proto.InternalMessageInfo

func (m *GetPgcChannelPKInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetPkBattleInfo() *PgcChannelPKBattle {
	if m != nil {
		return m.PkBattleInfo
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetQuickKillInfo() *PgcChannelPKQuickKillInfo {
	if m != nil {
		return m.QuickKillInfo
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetPeakInfo() *PgcChannelPKPeakInfo {
	if m != nil {
		return m.PeakInfo
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetLastMvpInfo() []*PgcChannelPKMicRankInfo {
	if m != nil {
		return m.LastMvpInfo
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetLastMvpExpired() uint32 {
	if m != nil {
		return m.LastMvpExpired
	}
	return 0
}

func (m *GetPgcChannelPKInfoResp) GetResultConf() []*ResultSourceConf {
	if m != nil {
		return m.ResultConf
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetMvpConf() []*DownloadSourceConf {
	if m != nil {
		return m.MvpConf
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetIconConf() []*HeadIconSourceConf {
	if m != nil {
		return m.IconConf
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetMvpIcon() []*app.DownloadSourceInfo {
	if m != nil {
		return m.MvpIcon
	}
	return nil
}

// 主持麦位语音流变化上报，由主持人上下麦和pk开始时上报
type PgcChannelPKReportClientIDChangeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicId                uint32       `protobuf:"varint,3,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	PkId                 uint32       `protobuf:"varint,4,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	ClientId             string       `protobuf:"bytes,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PgcChannelPKReportClientIDChangeReq) Reset()         { *m = PgcChannelPKReportClientIDChangeReq{} }
func (m *PgcChannelPKReportClientIDChangeReq) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKReportClientIDChangeReq) ProtoMessage()    {}
func (*PgcChannelPKReportClientIDChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{28}
}
func (m *PgcChannelPKReportClientIDChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.Unmarshal(m, b)
}
func (m *PgcChannelPKReportClientIDChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKReportClientIDChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.Merge(dst, src)
}
func (m *PgcChannelPKReportClientIDChangeReq) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.Size(m)
}
func (m *PgcChannelPKReportClientIDChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKReportClientIDChangeReq proto.InternalMessageInfo

func (m *PgcChannelPKReportClientIDChangeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PgcChannelPKReportClientIDChangeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcChannelPKReportClientIDChangeReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *PgcChannelPKReportClientIDChangeReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *PgcChannelPKReportClientIDChangeReq) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

type PgcChannelPKReportClientIDChangeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PgcChannelPKReportClientIDChangeResp) Reset()         { *m = PgcChannelPKReportClientIDChangeResp{} }
func (m *PgcChannelPKReportClientIDChangeResp) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKReportClientIDChangeResp) ProtoMessage()    {}
func (*PgcChannelPKReportClientIDChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{29}
}
func (m *PgcChannelPKReportClientIDChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.Unmarshal(m, b)
}
func (m *PgcChannelPKReportClientIDChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKReportClientIDChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.Merge(dst, src)
}
func (m *PgcChannelPKReportClientIDChangeResp) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.Size(m)
}
func (m *PgcChannelPKReportClientIDChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKReportClientIDChangeResp proto.InternalMessageInfo

func (m *PgcChannelPKReportClientIDChangeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// PK麦位音频流变化推送
type PgcChannelPKMicRadioInfoPushOpt struct {
	ChannelId            uint32                  `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelClientId      string                  `protobuf:"bytes,2,opt,name=channel_client_id,json=channelClientId,proto3" json:"channel_client_id,omitempty"`
	ClientList           []*PgcChannelPKMicSpace `protobuf:"bytes,3,rep,name=client_list,json=clientList,proto3" json:"client_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *PgcChannelPKMicRadioInfoPushOpt) Reset()         { *m = PgcChannelPKMicRadioInfoPushOpt{} }
func (m *PgcChannelPKMicRadioInfoPushOpt) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKMicRadioInfoPushOpt) ProtoMessage()    {}
func (*PgcChannelPKMicRadioInfoPushOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{30}
}
func (m *PgcChannelPKMicRadioInfoPushOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKMicRadioInfoPushOpt.Unmarshal(m, b)
}
func (m *PgcChannelPKMicRadioInfoPushOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKMicRadioInfoPushOpt.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKMicRadioInfoPushOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKMicRadioInfoPushOpt.Merge(dst, src)
}
func (m *PgcChannelPKMicRadioInfoPushOpt) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKMicRadioInfoPushOpt.Size(m)
}
func (m *PgcChannelPKMicRadioInfoPushOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKMicRadioInfoPushOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKMicRadioInfoPushOpt proto.InternalMessageInfo

func (m *PgcChannelPKMicRadioInfoPushOpt) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcChannelPKMicRadioInfoPushOpt) GetChannelClientId() string {
	if m != nil {
		return m.ChannelClientId
	}
	return ""
}

func (m *PgcChannelPKMicRadioInfoPushOpt) GetClientList() []*PgcChannelPKMicSpace {
	if m != nil {
		return m.ClientList
	}
	return nil
}

// 主播设置PK中是否能听到对面语音
type SetPgcChannelPKOpponentMicFlagReq struct {
	BaseReq              *app.BaseReq                `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OptMicFlag           PgcChannelPKOpponentMicFlag `protobuf:"varint,3,opt,name=opt_mic_flag,json=optMicFlag,proto3,enum=ga.pgc_channel_pk_logic.PgcChannelPKOpponentMicFlag" json:"opt_mic_flag,omitempty"`
	PkId                 uint32                      `protobuf:"varint,4,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	MicId                uint32                      `protobuf:"varint,5,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *SetPgcChannelPKOpponentMicFlagReq) Reset()         { *m = SetPgcChannelPKOpponentMicFlagReq{} }
func (m *SetPgcChannelPKOpponentMicFlagReq) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKOpponentMicFlagReq) ProtoMessage()    {}
func (*SetPgcChannelPKOpponentMicFlagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{31}
}
func (m *SetPgcChannelPKOpponentMicFlagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.Unmarshal(m, b)
}
func (m *SetPgcChannelPKOpponentMicFlagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKOpponentMicFlagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.Merge(dst, src)
}
func (m *SetPgcChannelPKOpponentMicFlagReq) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.Size(m)
}
func (m *SetPgcChannelPKOpponentMicFlagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq proto.InternalMessageInfo

func (m *SetPgcChannelPKOpponentMicFlagReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetPgcChannelPKOpponentMicFlagReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPgcChannelPKOpponentMicFlagReq) GetOptMicFlag() PgcChannelPKOpponentMicFlag {
	if m != nil {
		return m.OptMicFlag
	}
	return PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN
}

func (m *SetPgcChannelPKOpponentMicFlagReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *SetPgcChannelPKOpponentMicFlagReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type SetPgcChannelPKOpponentMicFlagResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetPgcChannelPKOpponentMicFlagResp) Reset()         { *m = SetPgcChannelPKOpponentMicFlagResp{} }
func (m *SetPgcChannelPKOpponentMicFlagResp) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKOpponentMicFlagResp) ProtoMessage()    {}
func (*SetPgcChannelPKOpponentMicFlagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{32}
}
func (m *SetPgcChannelPKOpponentMicFlagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.Unmarshal(m, b)
}
func (m *SetPgcChannelPKOpponentMicFlagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKOpponentMicFlagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.Merge(dst, src)
}
func (m *SetPgcChannelPKOpponentMicFlagResp) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.Size(m)
}
func (m *SetPgcChannelPKOpponentMicFlagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp proto.InternalMessageInfo

func (m *SetPgcChannelPKOpponentMicFlagResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 主播设置PK中能否听到对方房间语音房间推送，所有人根据这个推送确定能否听到对面房间声音。包括主播自己
type PgcChannelPKOpponentMicFlagPushMsg struct {
	OptMicFlag           PgcChannelPKOpponentMicFlag `protobuf:"varint,1,opt,name=opt_mic_flag,json=optMicFlag,proto3,enum=ga.pgc_channel_pk_logic.PgcChannelPKOpponentMicFlag" json:"opt_mic_flag,omitempty"`
	ChannelId            uint32                      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeBanMicChannelId    uint32                      `protobuf:"varint,3,opt,name=be_ban_mic_channel_id,json=beBanMicChannelId,proto3" json:"be_ban_mic_channel_id,omitempty"`
	MicId                uint32                      `protobuf:"varint,4,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *PgcChannelPKOpponentMicFlagPushMsg) Reset()         { *m = PgcChannelPKOpponentMicFlagPushMsg{} }
func (m *PgcChannelPKOpponentMicFlagPushMsg) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKOpponentMicFlagPushMsg) ProtoMessage()    {}
func (*PgcChannelPKOpponentMicFlagPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{33}
}
func (m *PgcChannelPKOpponentMicFlagPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.Unmarshal(m, b)
}
func (m *PgcChannelPKOpponentMicFlagPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKOpponentMicFlagPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.Merge(dst, src)
}
func (m *PgcChannelPKOpponentMicFlagPushMsg) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.Size(m)
}
func (m *PgcChannelPKOpponentMicFlagPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg proto.InternalMessageInfo

func (m *PgcChannelPKOpponentMicFlagPushMsg) GetOptMicFlag() PgcChannelPKOpponentMicFlag {
	if m != nil {
		return m.OptMicFlag
	}
	return PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN
}

func (m *PgcChannelPKOpponentMicFlagPushMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcChannelPKOpponentMicFlagPushMsg) GetBeBanMicChannelId() uint32 {
	if m != nil {
		return m.BeBanMicChannelId
	}
	return 0
}

func (m *PgcChannelPKOpponentMicFlagPushMsg) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

// 获取自己在当前PK中送给to_uid的送礼值
type GetPgcChannelPKSendGiftScoreReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32       `protobuf:"varint,3,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	MyUid                uint32       `protobuf:"varint,4,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	ToUid                uint32       `protobuf:"varint,5,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPgcChannelPKSendGiftScoreReq) Reset()         { *m = GetPgcChannelPKSendGiftScoreReq{} }
func (m *GetPgcChannelPKSendGiftScoreReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKSendGiftScoreReq) ProtoMessage()    {}
func (*GetPgcChannelPKSendGiftScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{34}
}
func (m *GetPgcChannelPKSendGiftScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKSendGiftScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKSendGiftScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.Merge(dst, src)
}
func (m *GetPgcChannelPKSendGiftScoreReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.Size(m)
}
func (m *GetPgcChannelPKSendGiftScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq proto.InternalMessageInfo

func (m *GetPgcChannelPKSendGiftScoreReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPgcChannelPKSendGiftScoreReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type GetPgcChannelPKSendGiftScoreResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32        `protobuf:"varint,3,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	MyUid                uint32        `protobuf:"varint,4,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	ToUid                uint32        `protobuf:"varint,5,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Score                uint32        `protobuf:"varint,6,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetPgcChannelPKSendGiftScoreResp) Reset()         { *m = GetPgcChannelPKSendGiftScoreResp{} }
func (m *GetPgcChannelPKSendGiftScoreResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKSendGiftScoreResp) ProtoMessage()    {}
func (*GetPgcChannelPKSendGiftScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{35}
}
func (m *GetPgcChannelPKSendGiftScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKSendGiftScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKSendGiftScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.Merge(dst, src)
}
func (m *GetPgcChannelPKSendGiftScoreResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.Size(m)
}
func (m *GetPgcChannelPKSendGiftScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp proto.InternalMessageInfo

func (m *GetPgcChannelPKSendGiftScoreResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPgcChannelPKSendGiftScoreResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreResp) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreResp) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreResp) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// 获取PK观众火力榜
type GetPgcChannelPKAudienceRankReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32       `protobuf:"varint,3,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	Begin                uint32       `protobuf:"varint,4,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32       `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPgcChannelPKAudienceRankReq) Reset()         { *m = GetPgcChannelPKAudienceRankReq{} }
func (m *GetPgcChannelPKAudienceRankReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKAudienceRankReq) ProtoMessage()    {}
func (*GetPgcChannelPKAudienceRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{36}
}
func (m *GetPgcChannelPKAudienceRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKAudienceRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKAudienceRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKAudienceRankReq.Merge(dst, src)
}
func (m *GetPgcChannelPKAudienceRankReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankReq.Size(m)
}
func (m *GetPgcChannelPKAudienceRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKAudienceRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKAudienceRankReq proto.InternalMessageInfo

func (m *GetPgcChannelPKAudienceRankReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPgcChannelPKAudienceRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKAudienceRankReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *GetPgcChannelPKAudienceRankReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetPgcChannelPKAudienceRankReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type PgcChannelPKAudienceInfo struct {
	UserProfile          *app.UserProfile   `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	Score                uint32             `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Rich                 uint32             `protobuf:"varint,3,opt,name=rich,proto3" json:"rich,omitempty"`
	Charm                uint32             `protobuf:"varint,4,opt,name=charm,proto3" json:"charm,omitempty"`
	NobilityLevel        uint32             `protobuf:"varint,5,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	GroupFansLevel       uint32             `protobuf:"varint,6,opt,name=group_fans_level,json=groupFansLevel,proto3" json:"group_fans_level,omitempty"`
	ChannelMemLevel      uint32             `protobuf:"varint,7,opt,name=channel_mem_level,json=channelMemLevel,proto3" json:"channel_mem_level,omitempty"`
	GroupName            string             `protobuf:"bytes,8,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	PlateInfo            *app.FansPlateInfo `protobuf:"bytes,9,opt,name=plate_info,json=plateInfo,proto3" json:"plate_info,omitempty"`
	NobilityInfo         *app.NobilityInfo  `protobuf:"bytes,10,opt,name=nobility_info,json=nobilityInfo,proto3" json:"nobility_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PgcChannelPKAudienceInfo) Reset()         { *m = PgcChannelPKAudienceInfo{} }
func (m *PgcChannelPKAudienceInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKAudienceInfo) ProtoMessage()    {}
func (*PgcChannelPKAudienceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{37}
}
func (m *PgcChannelPKAudienceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKAudienceInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKAudienceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKAudienceInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKAudienceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKAudienceInfo.Merge(dst, src)
}
func (m *PgcChannelPKAudienceInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKAudienceInfo.Size(m)
}
func (m *PgcChannelPKAudienceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKAudienceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKAudienceInfo proto.InternalMessageInfo

func (m *PgcChannelPKAudienceInfo) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *PgcChannelPKAudienceInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetRich() uint32 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetGroupFansLevel() uint32 {
	if m != nil {
		return m.GroupFansLevel
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetChannelMemLevel() uint32 {
	if m != nil {
		return m.ChannelMemLevel
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *PgcChannelPKAudienceInfo) GetPlateInfo() *app.FansPlateInfo {
	if m != nil {
		return m.PlateInfo
	}
	return nil
}

func (m *PgcChannelPKAudienceInfo) GetNobilityInfo() *app.NobilityInfo {
	if m != nil {
		return m.NobilityInfo
	}
	return nil
}

type GetPgcChannelPKAudienceRankResp struct {
	BaseResp             *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RankList             []*PgcChannelPKAudienceInfo `protobuf:"bytes,2,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetPgcChannelPKAudienceRankResp) Reset()         { *m = GetPgcChannelPKAudienceRankResp{} }
func (m *GetPgcChannelPKAudienceRankResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKAudienceRankResp) ProtoMessage()    {}
func (*GetPgcChannelPKAudienceRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{38}
}
func (m *GetPgcChannelPKAudienceRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKAudienceRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKAudienceRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKAudienceRankResp.Merge(dst, src)
}
func (m *GetPgcChannelPKAudienceRankResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankResp.Size(m)
}
func (m *GetPgcChannelPKAudienceRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKAudienceRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKAudienceRankResp proto.InternalMessageInfo

func (m *GetPgcChannelPKAudienceRankResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPgcChannelPKAudienceRankResp) GetRankList() []*PgcChannelPKAudienceInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

type MVPImMember struct {
	UserProfile          *app.UserProfile `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	HeadwearUrl          string           `protobuf:"bytes,2,opt,name=headwear_url,json=headwearUrl,proto3" json:"headwear_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MVPImMember) Reset()         { *m = MVPImMember{} }
func (m *MVPImMember) String() string { return proto.CompactTextString(m) }
func (*MVPImMember) ProtoMessage()    {}
func (*MVPImMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{39}
}
func (m *MVPImMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MVPImMember.Unmarshal(m, b)
}
func (m *MVPImMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MVPImMember.Marshal(b, m, deterministic)
}
func (dst *MVPImMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MVPImMember.Merge(dst, src)
}
func (m *MVPImMember) XXX_Size() int {
	return xxx_messageInfo_MVPImMember.Size(m)
}
func (m *MVPImMember) XXX_DiscardUnknown() {
	xxx_messageInfo_MVPImMember.DiscardUnknown(m)
}

var xxx_messageInfo_MVPImMember proto.InternalMessageInfo

func (m *MVPImMember) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *MVPImMember) GetHeadwearUrl() string {
	if m != nil {
		return m.HeadwearUrl
	}
	return ""
}

// mvp im 消息
type PkMVPImOpt struct {
	MemList              []*MVPImMember `protobuf:"bytes,1,rep,name=mem_list,json=memList,proto3" json:"mem_list,omitempty"`
	SmallCardUrl         string         `protobuf:"bytes,2,opt,name=small_card_url,json=smallCardUrl,proto3" json:"small_card_url,omitempty"`
	CardUrl              string         `protobuf:"bytes,3,opt,name=card_url,json=cardUrl,proto3" json:"card_url,omitempty"`
	Title                string         `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Content              string         `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	BindId               uint32         `protobuf:"varint,6,opt,name=bind_id,json=bindId,proto3" json:"bind_id,omitempty"`
	ServerTs             int64          `protobuf:"varint,7,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PkMVPImOpt) Reset()         { *m = PkMVPImOpt{} }
func (m *PkMVPImOpt) String() string { return proto.CompactTextString(m) }
func (*PkMVPImOpt) ProtoMessage()    {}
func (*PkMVPImOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{40}
}
func (m *PkMVPImOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PkMVPImOpt.Unmarshal(m, b)
}
func (m *PkMVPImOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PkMVPImOpt.Marshal(b, m, deterministic)
}
func (dst *PkMVPImOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PkMVPImOpt.Merge(dst, src)
}
func (m *PkMVPImOpt) XXX_Size() int {
	return xxx_messageInfo_PkMVPImOpt.Size(m)
}
func (m *PkMVPImOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PkMVPImOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PkMVPImOpt proto.InternalMessageInfo

func (m *PkMVPImOpt) GetMemList() []*MVPImMember {
	if m != nil {
		return m.MemList
	}
	return nil
}

func (m *PkMVPImOpt) GetSmallCardUrl() string {
	if m != nil {
		return m.SmallCardUrl
	}
	return ""
}

func (m *PkMVPImOpt) GetCardUrl() string {
	if m != nil {
		return m.CardUrl
	}
	return ""
}

func (m *PkMVPImOpt) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PkMVPImOpt) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *PkMVPImOpt) GetBindId() uint32 {
	if m != nil {
		return m.BindId
	}
	return 0
}

func (m *PkMVPImOpt) GetServerTs() int64 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

type PgcChannelPKMicSpace struct {
	MicId                uint32           `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	UserProfile          *app.UserProfile `protobuf:"bytes,2,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	ChannelClientId      string           `protobuf:"bytes,3,opt,name=channel_client_id,json=channelClientId,proto3" json:"channel_client_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *PgcChannelPKMicSpace) Reset()         { *m = PgcChannelPKMicSpace{} }
func (m *PgcChannelPKMicSpace) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKMicSpace) ProtoMessage()    {}
func (*PgcChannelPKMicSpace) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{41}
}
func (m *PgcChannelPKMicSpace) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKMicSpace.Unmarshal(m, b)
}
func (m *PgcChannelPKMicSpace) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKMicSpace.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKMicSpace) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKMicSpace.Merge(dst, src)
}
func (m *PgcChannelPKMicSpace) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKMicSpace.Size(m)
}
func (m *PgcChannelPKMicSpace) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKMicSpace.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKMicSpace proto.InternalMessageInfo

func (m *PgcChannelPKMicSpace) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *PgcChannelPKMicSpace) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *PgcChannelPKMicSpace) GetChannelClientId() string {
	if m != nil {
		return m.ChannelClientId
	}
	return ""
}

// 选择互动玩家
type ChoseInteractionReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32       `protobuf:"varint,3,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	MicId                uint32       `protobuf:"varint,4,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChoseInteractionReq) Reset()         { *m = ChoseInteractionReq{} }
func (m *ChoseInteractionReq) String() string { return proto.CompactTextString(m) }
func (*ChoseInteractionReq) ProtoMessage()    {}
func (*ChoseInteractionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{42}
}
func (m *ChoseInteractionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChoseInteractionReq.Unmarshal(m, b)
}
func (m *ChoseInteractionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChoseInteractionReq.Marshal(b, m, deterministic)
}
func (dst *ChoseInteractionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChoseInteractionReq.Merge(dst, src)
}
func (m *ChoseInteractionReq) XXX_Size() int {
	return xxx_messageInfo_ChoseInteractionReq.Size(m)
}
func (m *ChoseInteractionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChoseInteractionReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChoseInteractionReq proto.InternalMessageInfo

func (m *ChoseInteractionReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChoseInteractionReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChoseInteractionReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *ChoseInteractionReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type ChoseInteractionResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Toast                string        `protobuf:"bytes,2,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChoseInteractionResp) Reset()         { *m = ChoseInteractionResp{} }
func (m *ChoseInteractionResp) String() string { return proto.CompactTextString(m) }
func (*ChoseInteractionResp) ProtoMessage()    {}
func (*ChoseInteractionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{43}
}
func (m *ChoseInteractionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChoseInteractionResp.Unmarshal(m, b)
}
func (m *ChoseInteractionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChoseInteractionResp.Marshal(b, m, deterministic)
}
func (dst *ChoseInteractionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChoseInteractionResp.Merge(dst, src)
}
func (m *ChoseInteractionResp) XXX_Size() int {
	return xxx_messageInfo_ChoseInteractionResp.Size(m)
}
func (m *ChoseInteractionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChoseInteractionResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChoseInteractionResp proto.InternalMessageInfo

func (m *ChoseInteractionResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChoseInteractionResp) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

// 选择互动玩家后的toast提示
type PgcChannelPKChoseInteractionOpt struct {
	Toast                string   `protobuf:"bytes,1,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PgcChannelPKChoseInteractionOpt) Reset()         { *m = PgcChannelPKChoseInteractionOpt{} }
func (m *PgcChannelPKChoseInteractionOpt) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKChoseInteractionOpt) ProtoMessage()    {}
func (*PgcChannelPKChoseInteractionOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{44}
}
func (m *PgcChannelPKChoseInteractionOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKChoseInteractionOpt.Unmarshal(m, b)
}
func (m *PgcChannelPKChoseInteractionOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKChoseInteractionOpt.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKChoseInteractionOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKChoseInteractionOpt.Merge(dst, src)
}
func (m *PgcChannelPKChoseInteractionOpt) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKChoseInteractionOpt.Size(m)
}
func (m *PgcChannelPKChoseInteractionOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKChoseInteractionOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKChoseInteractionOpt proto.InternalMessageInfo

func (m *PgcChannelPKChoseInteractionOpt) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

// 互动结束
type PgcChannelPKInteractionEndOpt struct {
	MvpExpired           uint32                     `protobuf:"varint,1,opt,name=mvp_expired,json=mvpExpired,proto3" json:"mvp_expired,omitempty"`
	LastMvp              []*PgcChannelPKMicRankInfo `protobuf:"bytes,2,rep,name=last_mvp,json=lastMvp,proto3" json:"last_mvp,omitempty"`
	Toast                string                     `protobuf:"bytes,3,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PgcChannelPKInteractionEndOpt) Reset()         { *m = PgcChannelPKInteractionEndOpt{} }
func (m *PgcChannelPKInteractionEndOpt) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKInteractionEndOpt) ProtoMessage()    {}
func (*PgcChannelPKInteractionEndOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{45}
}
func (m *PgcChannelPKInteractionEndOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKInteractionEndOpt.Unmarshal(m, b)
}
func (m *PgcChannelPKInteractionEndOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKInteractionEndOpt.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKInteractionEndOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKInteractionEndOpt.Merge(dst, src)
}
func (m *PgcChannelPKInteractionEndOpt) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKInteractionEndOpt.Size(m)
}
func (m *PgcChannelPKInteractionEndOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKInteractionEndOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKInteractionEndOpt proto.InternalMessageInfo

func (m *PgcChannelPKInteractionEndOpt) GetMvpExpired() uint32 {
	if m != nil {
		return m.MvpExpired
	}
	return 0
}

func (m *PgcChannelPKInteractionEndOpt) GetLastMvp() []*PgcChannelPKMicRankInfo {
	if m != nil {
		return m.LastMvp
	}
	return nil
}

func (m *PgcChannelPKInteractionEndOpt) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

// 互动公屏消息
type PgcChannelPKChoseInteractionRoomMsgOpt struct {
	RoomMsg              string   `protobuf:"bytes,1,opt,name=room_msg,json=roomMsg,proto3" json:"room_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PgcChannelPKChoseInteractionRoomMsgOpt) Reset() {
	*m = PgcChannelPKChoseInteractionRoomMsgOpt{}
}
func (m *PgcChannelPKChoseInteractionRoomMsgOpt) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKChoseInteractionRoomMsgOpt) ProtoMessage()    {}
func (*PgcChannelPKChoseInteractionRoomMsgOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{46}
}
func (m *PgcChannelPKChoseInteractionRoomMsgOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKChoseInteractionRoomMsgOpt.Unmarshal(m, b)
}
func (m *PgcChannelPKChoseInteractionRoomMsgOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKChoseInteractionRoomMsgOpt.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKChoseInteractionRoomMsgOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKChoseInteractionRoomMsgOpt.Merge(dst, src)
}
func (m *PgcChannelPKChoseInteractionRoomMsgOpt) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKChoseInteractionRoomMsgOpt.Size(m)
}
func (m *PgcChannelPKChoseInteractionRoomMsgOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKChoseInteractionRoomMsgOpt.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKChoseInteractionRoomMsgOpt proto.InternalMessageInfo

func (m *PgcChannelPKChoseInteractionRoomMsgOpt) GetRoomMsg() string {
	if m != nil {
		return m.RoomMsg
	}
	return ""
}

type SetPgcChannelPKEndReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32       `protobuf:"varint,3,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetPgcChannelPKEndReq) Reset()         { *m = SetPgcChannelPKEndReq{} }
func (m *SetPgcChannelPKEndReq) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKEndReq) ProtoMessage()    {}
func (*SetPgcChannelPKEndReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{47}
}
func (m *SetPgcChannelPKEndReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKEndReq.Unmarshal(m, b)
}
func (m *SetPgcChannelPKEndReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKEndReq.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKEndReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKEndReq.Merge(dst, src)
}
func (m *SetPgcChannelPKEndReq) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKEndReq.Size(m)
}
func (m *SetPgcChannelPKEndReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKEndReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKEndReq proto.InternalMessageInfo

func (m *SetPgcChannelPKEndReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetPgcChannelPKEndReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPgcChannelPKEndReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

type SetPgcChannelPKEndResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetPgcChannelPKEndResp) Reset()         { *m = SetPgcChannelPKEndResp{} }
func (m *SetPgcChannelPKEndResp) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKEndResp) ProtoMessage()    {}
func (*SetPgcChannelPKEndResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab, []int{48}
}
func (m *SetPgcChannelPKEndResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKEndResp.Unmarshal(m, b)
}
func (m *SetPgcChannelPKEndResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKEndResp.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKEndResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKEndResp.Merge(dst, src)
}
func (m *SetPgcChannelPKEndResp) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKEndResp.Size(m)
}
func (m *SetPgcChannelPKEndResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKEndResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKEndResp proto.InternalMessageInfo

func (m *SetPgcChannelPKEndResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*PgcChannelPKAnchor)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKAnchor")
	proto.RegisterType((*PgcChannelPKMicRankInfo)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKMicRankInfo")
	proto.RegisterType((*PgcChannelPKInfo)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKInfo")
	proto.RegisterType((*PgcChannelPKQuickKillInfo)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKQuickKillInfo")
	proto.RegisterType((*PgcChannelPKPeakInfo)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKPeakInfo")
	proto.RegisterType((*PgcChannelPKQuickKillChangeOpt)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKQuickKillChangeOpt")
	proto.RegisterType((*PgcChannelPKPeakChangeOpt)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKPeakChangeOpt")
	proto.RegisterType((*PgcChannelPKBattle)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKBattle")
	proto.RegisterType((*PgcChannelPKMemResult)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKMemResult")
	proto.RegisterType((*PgcChannelPKResultOpt)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKResultOpt")
	proto.RegisterType((*GetPgcChannelPKEntryReq)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKEntryReq")
	proto.RegisterType((*GetPgcChannelPKEntryResp)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKEntryResp")
	proto.RegisterType((*GetPgcChannelPKChannelListReq)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKChannelListReq")
	proto.RegisterType((*PgcChannelPKChannelInfo)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKChannelInfo")
	proto.RegisterType((*GetPgcChannelPKChannelListResp)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKChannelListResp")
	proto.RegisterType((*SetPgcChannelPKSwitchReq)(nil), "ga.pgc_channel_pk_logic.SetPgcChannelPKSwitchReq")
	proto.RegisterType((*SetPgcChannelPKSwitchResp)(nil), "ga.pgc_channel_pk_logic.SetPgcChannelPKSwitchResp")
	proto.RegisterType((*StartPgcChannelPKReq)(nil), "ga.pgc_channel_pk_logic.StartPgcChannelPKReq")
	proto.RegisterType((*StartPgcChannelPKResp)(nil), "ga.pgc_channel_pk_logic.StartPgcChannelPKResp")
	proto.RegisterType((*PgcChannelPKInviteOpt)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKInviteOpt")
	proto.RegisterType((*AcceptPgcChannelPKReq)(nil), "ga.pgc_channel_pk_logic.AcceptPgcChannelPKReq")
	proto.RegisterType((*AcceptPgcChannelPKResp)(nil), "ga.pgc_channel_pk_logic.AcceptPgcChannelPKResp")
	proto.RegisterType((*PgcChannelPKInviteRespOpt)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKInviteRespOpt")
	proto.RegisterType((*GetPgcChannelPKInfoReq)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKInfoReq")
	proto.RegisterType((*DownloadSourceConf)(nil), "ga.pgc_channel_pk_logic.DownloadSourceConf")
	proto.RegisterType((*ResultSourceConf)(nil), "ga.pgc_channel_pk_logic.ResultSourceConf")
	proto.RegisterType((*HeadIconSourceConf)(nil), "ga.pgc_channel_pk_logic.HeadIconSourceConf")
	proto.RegisterType((*GetPgcChannelPKInfoResp)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKInfoResp")
	proto.RegisterType((*PgcChannelPKReportClientIDChangeReq)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKReportClientIDChangeReq")
	proto.RegisterType((*PgcChannelPKReportClientIDChangeResp)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKReportClientIDChangeResp")
	proto.RegisterType((*PgcChannelPKMicRadioInfoPushOpt)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKMicRadioInfoPushOpt")
	proto.RegisterType((*SetPgcChannelPKOpponentMicFlagReq)(nil), "ga.pgc_channel_pk_logic.SetPgcChannelPKOpponentMicFlagReq")
	proto.RegisterType((*SetPgcChannelPKOpponentMicFlagResp)(nil), "ga.pgc_channel_pk_logic.SetPgcChannelPKOpponentMicFlagResp")
	proto.RegisterType((*PgcChannelPKOpponentMicFlagPushMsg)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKOpponentMicFlagPushMsg")
	proto.RegisterType((*GetPgcChannelPKSendGiftScoreReq)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKSendGiftScoreReq")
	proto.RegisterType((*GetPgcChannelPKSendGiftScoreResp)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKSendGiftScoreResp")
	proto.RegisterType((*GetPgcChannelPKAudienceRankReq)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKAudienceRankReq")
	proto.RegisterType((*PgcChannelPKAudienceInfo)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKAudienceInfo")
	proto.RegisterType((*GetPgcChannelPKAudienceRankResp)(nil), "ga.pgc_channel_pk_logic.GetPgcChannelPKAudienceRankResp")
	proto.RegisterType((*MVPImMember)(nil), "ga.pgc_channel_pk_logic.MVPImMember")
	proto.RegisterType((*PkMVPImOpt)(nil), "ga.pgc_channel_pk_logic.PkMVPImOpt")
	proto.RegisterType((*PgcChannelPKMicSpace)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKMicSpace")
	proto.RegisterType((*ChoseInteractionReq)(nil), "ga.pgc_channel_pk_logic.ChoseInteractionReq")
	proto.RegisterType((*ChoseInteractionResp)(nil), "ga.pgc_channel_pk_logic.ChoseInteractionResp")
	proto.RegisterType((*PgcChannelPKChoseInteractionOpt)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKChoseInteractionOpt")
	proto.RegisterType((*PgcChannelPKInteractionEndOpt)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKInteractionEndOpt")
	proto.RegisterType((*PgcChannelPKChoseInteractionRoomMsgOpt)(nil), "ga.pgc_channel_pk_logic.PgcChannelPKChoseInteractionRoomMsgOpt")
	proto.RegisterType((*SetPgcChannelPKEndReq)(nil), "ga.pgc_channel_pk_logic.SetPgcChannelPKEndReq")
	proto.RegisterType((*SetPgcChannelPKEndResp)(nil), "ga.pgc_channel_pk_logic.SetPgcChannelPKEndResp")
	proto.RegisterEnum("ga.pgc_channel_pk_logic.PgcChannelPKOpponentMicFlag", PgcChannelPKOpponentMicFlag_name, PgcChannelPKOpponentMicFlag_value)
	proto.RegisterEnum("ga.pgc_channel_pk_logic.PgcChannelPKSwitchEnum", PgcChannelPKSwitchEnum_name, PgcChannelPKSwitchEnum_value)
	proto.RegisterEnum("ga.pgc_channel_pk_logic.PgcChannelPKQuickKillChangeOpt_EventType", PgcChannelPKQuickKillChangeOpt_EventType_name, PgcChannelPKQuickKillChangeOpt_EventType_value)
	proto.RegisterEnum("ga.pgc_channel_pk_logic.PgcChannelPKBattle_Status", PgcChannelPKBattle_Status_name, PgcChannelPKBattle_Status_value)
	proto.RegisterEnum("ga.pgc_channel_pk_logic.PgcChannelPKMemResult_ResultType", PgcChannelPKMemResult_ResultType_name, PgcChannelPKMemResult_ResultType_value)
	proto.RegisterEnum("ga.pgc_channel_pk_logic.PgcChannelPKResultOpt_EndType", PgcChannelPKResultOpt_EndType_name, PgcChannelPKResultOpt_EndType_value)
	proto.RegisterEnum("ga.pgc_channel_pk_logic.PgcChannelPKChannelInfo_Status", PgcChannelPKChannelInfo_Status_name, PgcChannelPKChannelInfo_Status_value)
}

func init() {
	proto.RegisterFile("pgc-channel-pk-logic_.proto", fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab)
}

var fileDescriptor_pgc_channel_pk_logic__0c0a8d1198a0d4ab = []byte{
	// 3015 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x3a, 0x4d, 0x6c, 0x1b, 0xc7,
	0xd5, 0x5e, 0x52, 0x22, 0x97, 0x8f, 0xa4, 0x44, 0xaf, 0x65, 0x9b, 0x8e, 0x3f, 0xc7, 0xca, 0xc6,
	0x9f, 0x6b, 0xa7, 0xb5, 0x92, 0x38, 0x4d, 0x8b, 0x5e, 0x1a, 0xc4, 0xb2, 0x1c, 0x2b, 0xb6, 0x24,
	0x7a, 0x69, 0x25, 0x41, 0x2e, 0xdb, 0xe1, 0xee, 0x88, 0x9a, 0x72, 0x77, 0x76, 0xbd, 0x3b, 0xd4,
	0xcf, 0xa5, 0x40, 0x7b, 0xea, 0x25, 0xa7, 0x1e, 0x7a, 0xe9, 0xa5, 0x28, 0x0a, 0x14, 0xe8, 0xb9,
	0x05, 0x0a, 0xb4, 0x40, 0x2f, 0xed, 0xb9, 0x97, 0xa0, 0x97, 0x5e, 0x0a, 0xf4, 0x5c, 0xa0, 0x40,
	0xcf, 0x45, 0x31, 0x6f, 0x66, 0xc9, 0xe5, 0x8f, 0x64, 0x51, 0x66, 0x7a, 0x9b, 0x79, 0xf3, 0xf6,
	0xcd, 0x9b, 0xf7, 0x33, 0xef, 0x67, 0x16, 0xae, 0xc7, 0x5d, 0xef, 0x9e, 0xb7, 0x4f, 0x38, 0xa7,
	0xc1, 0xbd, 0xb8, 0x77, 0x2f, 0x88, 0xba, 0xcc, 0x73, 0xd7, 0xe2, 0x24, 0x12, 0x91, 0x75, 0xb5,
	0x4b, 0xd6, 0xe2, 0xae, 0xe7, 0xea, 0x75, 0x37, 0xee, 0xb9, 0xb8, 0xfe, 0x5a, 0xbd, 0x4b, 0xdc,
	0x0e, 0x49, 0xa9, 0xc2, 0xb3, 0x1f, 0x83, 0xd5, 0xea, 0x7a, 0xeb, 0x0a, 0xab, 0xf5, 0xe4, 0x43,
	0xee, 0xed, 0x47, 0x89, 0x75, 0x1f, 0x6a, 0xfd, 0x94, 0x26, 0x6e, 0x9c, 0x44, 0x7b, 0x2c, 0xa0,
	0x4d, 0x63, 0xd5, 0xb8, 0x53, 0xbd, 0xbf, 0xbc, 0xd6, 0x25, 0x6b, 0xbb, 0x29, 0x4d, 0x5a, 0x0a,
	0xec, 0x54, 0xfb, 0xc3, 0x89, 0xfd, 0xa5, 0x01, 0x57, 0xf3, 0xa4, 0xb6, 0x98, 0xe7, 0x10, 0xde,
	0xdb, 0xe4, 0x7b, 0x91, 0xf5, 0x08, 0xcc, 0x90, 0x79, 0xae, 0x44, 0xd7, 0xb4, 0xbe, 0xbe, 0x76,
	0x02, 0x83, 0x6b, 0x93, 0xec, 0x38, 0xe5, 0x90, 0x79, 0x72, 0x5f, 0xeb, 0x31, 0x54, 0xf6, 0x92,
	0x28, 0x54, 0x84, 0x0a, 0xb3, 0x13, 0x32, 0xe5, 0xd7, 0x48, 0x69, 0x05, 0x16, 0x53, 0x2f, 0x4a,
	0x68, 0xb3, 0xb8, 0x6a, 0xdc, 0xa9, 0x3b, 0x6a, 0x62, 0x59, 0xb0, 0x90, 0x10, 0xde, 0x6b, 0x2e,
	0x20, 0x10, 0xc7, 0xf6, 0xbf, 0x0a, 0xd0, 0xc8, 0x93, 0xc2, 0x03, 0xb5, 0xa1, 0x96, 0xed, 0xc7,
	0xf8, 0x5e, 0xa4, 0x0f, 0xf5, 0xce, 0x99, 0x78, 0xd1, 0x03, 0x49, 0xc7, 0xa9, 0x7a, 0xc3, 0xc9,
	0x90, 0xa7, 0x42, 0x9e, 0xa7, 0x36, 0x2c, 0x8b, 0x28, 0x76, 0x09, 0x9e, 0xc0, 0x0d, 0x58, 0x2a,
	0x9a, 0xc5, 0xd5, 0xe2, 0xac, 0x27, 0xaf, 0x8b, 0x28, 0x56, 0xc3, 0xa7, 0x2c, 0x15, 0xd6, 0x13,
	0x30, 0xc3, 0x83, 0x58, 0xf1, 0xbe, 0x80, 0xd4, 0xce, 0xc6, 0x7b, 0x4e, 0xa9, 0x4e, 0x39, 0x3c,
	0x88, 0x91, 0xef, 0xb7, 0xe0, 0x62, 0xf6, 0x91, 0x17, 0x30, 0xca, 0x85, 0xcb, 0xfc, 0xe6, 0xe2,
	0xaa, 0x71, 0xa7, 0xe2, 0x2c, 0xeb, 0x85, 0x75, 0x84, 0x6f, 0xfa, 0xd6, 0x2d, 0x58, 0x0a, 0x19,
	0x77, 0xf1, 0x68, 0xae, 0xa0, 0x47, 0xa2, 0x59, 0x42, 0xc4, 0x5a, 0xc8, 0x78, 0x5b, 0x02, 0x9f,
	0xd3, 0x23, 0x61, 0xff, 0xb9, 0x00, 0xd7, 0xf2, 0xdb, 0x3e, 0xeb, 0x33, 0xaf, 0xf7, 0x84, 0x05,
	0x4a, 0x4e, 0xef, 0xc1, 0x95, 0x17, 0x12, 0xe0, 0xf6, 0x58, 0x10, 0xb8, 0x3e, 0x4d, 0x3d, 0x37,
	0x4e, 0xe8, 0x1e, 0x3b, 0x42, 0x35, 0x54, 0x9c, 0x4b, 0x2f, 0x32, 0xf4, 0x87, 0x34, 0xf5, 0x5a,
	0xb8, 0x64, 0xdd, 0x86, 0xe5, 0xb1, 0x8f, 0x50, 0xcc, 0x15, 0xa7, 0x3e, 0x82, 0x6d, 0xdd, 0x85,
	0x8b, 0x39, 0x3c, 0xca, 0x7d, 0x57, 0xa4, 0xda, 0x48, 0x96, 0x06, 0x98, 0x1b, 0xdc, 0x7f, 0x9e,
	0x4a, 0x54, 0xca, 0x49, 0x27, 0xa0, 0xae, 0x3c, 0x52, 0xdc, 0x73, 0x53, 0xea, 0x69, 0xd3, 0x59,
	0x52, 0x0b, 0x5b, 0x8c, 0xb7, 0x7a, 0x6d, 0xea, 0xe5, 0x51, 0xc9, 0x51, 0x86, 0xba, 0x38, 0x82,
	0x4a, 0x8e, 0x14, 0xea, 0xd7, 0x60, 0xd9, 0x8b, 0xb8, 0xcf, 0x04, 0x8b, 0xb8, 0x7b, 0x40, 0x82,
	0x3e, 0x45, 0x11, 0xd5, 0x9d, 0xa5, 0x01, 0xf8, 0x13, 0x09, 0xb5, 0x6c, 0xa8, 0x33, 0xee, 0x0e,
	0x99, 0x6d, 0x96, 0x57, 0x8d, 0x3b, 0xa6, 0x53, 0x65, 0x7c, 0x20, 0x2e, 0xfb, 0x19, 0xac, 0xe4,
	0xe5, 0xd8, 0xa2, 0x44, 0x39, 0xe4, 0x75, 0xa8, 0xc4, 0x94, 0xf4, 0x94, 0x1c, 0x94, 0xd4, 0x4c,
	0x09, 0x40, 0x11, 0xfc, 0x1f, 0x80, 0x3c, 0x8f, 0x5c, 0x8f, 0x7b, 0x28, 0x25, 0xd3, 0x31, 0x19,
	0x97, 0x1f, 0xb7, 0x7a, 0xf6, 0xef, 0x0a, 0xf0, 0xfa, 0x54, 0xdd, 0x48, 0x48, 0x97, 0xee, 0xc4,
	0xc2, 0xba, 0x01, 0x40, 0x0f, 0xa4, 0x1d, 0x88, 0xe3, 0x58, 0x5d, 0x1e, 0x75, 0xa7, 0x82, 0x90,
	0xe7, 0xc7, 0x31, 0xb5, 0xbe, 0x01, 0x96, 0x48, 0x58, 0xb7, 0x4b, 0x93, 0x81, 0xb1, 0x31, 0x5f,
	0x1b, 0x7d, 0x43, 0xaf, 0x64, 0x4e, 0xe2, 0x4b, 0xc5, 0xa1, 0x2a, 0x72, 0xa8, 0x4a, 0x1d, 0xf5,
	0x9e, 0xde, 0x54, 0xe1, 0x7d, 0x3e, 0xa2, 0x60, 0x6d, 0xd9, 0xd2, 0x2b, 0xef, 0x9f, 0xc9, 0xb2,
	0x47, 0x4c, 0x2c, 0x67, 0x14, 0x72, 0x6a, 0x3f, 0x82, 0xca, 0xc6, 0x80, 0xfd, 0x06, 0xd4, 0x76,
	0x78, 0x70, 0xdc, 0x3e, 0xe6, 0x9e, 0x5c, 0x6c, 0x5c, 0xb0, 0x56, 0xa0, 0xf1, 0x5c, 0xb1, 0x3d,
	0xa0, 0xd2, 0x30, 0xac, 0x8b, 0x50, 0x6f, 0x8b, 0x28, 0x1e, 0x82, 0x0a, 0xf6, 0xdf, 0x8c, 0x51,
	0xbb, 0x96, 0x22, 0x1d, 0x8a, 0xed, 0x0e, 0x34, 0xc8, 0x21, 0x49, 0xfc, 0xfc, 0x51, 0x95, 0xf0,
	0x96, 0x10, 0x3e, 0x3c, 0xeb, 0x2d, 0x50, 0x10, 0x79, 0x14, 0x65, 0x22, 0x4a, 0x7a, 0x35, 0x84,
	0xb6, 0x7a, 0xca, 0x40, 0xde, 0x84, 0xba, 0xdf, 0x4f, 0x08, 0x1a, 0x12, 0x2a, 0xba, 0xa8, 0x5c,
	0x2d, 0x03, 0xa2, 0xb2, 0x3f, 0xd6, 0x96, 0x90, 0x13, 0xd8, 0xbd, 0x33, 0x09, 0x2c, 0xb3, 0x25,
	0x65, 0x38, 0x28, 0xa6, 0x9f, 0x16, 0x47, 0xa3, 0xc9, 0x03, 0x22, 0x44, 0x40, 0xad, 0x07, 0x50,
	0x96, 0x14, 0xe4, 0xcd, 0x65, 0xe0, 0x5d, 0x73, 0xf7, 0x4c, 0x1b, 0x20, 0xf1, 0x52, 0xdc, 0xc3,
	0x0b, 0xeb, 0x35, 0xa8, 0xc4, 0xbd, 0xcc, 0x1d, 0xd5, 0x61, 0xcb, 0x71, 0x4f, 0xf9, 0xe1, 0x75,
	0xa8, 0xa4, 0x34, 0x39, 0xa0, 0x89, 0xcb, 0x95, 0xab, 0x16, 0x1d, 0x53, 0x01, 0xb6, 0x53, 0xe9,
	0x25, 0x07, 0x24, 0x60, 0x28, 0x2a, 0x14, 0xc2, 0x02, 0x0a, 0xa1, 0x8a, 0xc0, 0x96, 0x32, 0xf8,
	0x6b, 0x60, 0x66, 0x38, 0xe8, 0x94, 0xa6, 0x53, 0xd6, 0xcb, 0xf2, 0x4e, 0x8e, 0xf7, 0x49, 0x9a,
	0xf9, 0xa0, 0x9a, 0x58, 0x97, 0x60, 0x31, 0xee, 0x49, 0xf5, 0x94, 0x55, 0xa0, 0x88, 0x7b, 0x9b,
	0xbe, 0xdc, 0x29, 0x15, 0x44, 0xf4, 0xd3, 0x8c, 0x4d, 0x13, 0x17, 0xab, 0x0a, 0xa8, 0x58, 0xbd,
	0x09, 0x7a, 0xaa, 0x78, 0xa9, 0x20, 0x2f, 0xa0, 0x40, 0x92, 0x15, 0x7b, 0x17, 0x4a, 0x6d, 0x9c,
	0x59, 0x15, 0x58, 0xdc, 0x8e, 0x44, 0xeb, 0x49, 0xe3, 0x82, 0x55, 0x03, 0x73, 0x93, 0x1f, 0x30,
	0x41, 0x5b, 0x4f, 0x1a, 0x86, 0x55, 0x85, 0xf2, 0x26, 0x6f, 0x3d, 0x61, 0xbc, 0xdb, 0x28, 0x48,
	0xd3, 0x5b, 0xdf, 0x8f, 0x52, 0xba, 0xc9, 0x05, 0x4d, 0x88, 0x27, 0xd5, 0xda, 0x58, 0xb4, 0x96,
	0xa1, 0x9a, 0x07, 0x94, 0xec, 0xbf, 0x16, 0xe1, 0xf2, 0xc8, 0x3d, 0x4e, 0x43, 0x87, 0xa6, 0xfd,
	0x40, 0x68, 0xe5, 0xe4, 0x82, 0xd8, 0x6c, 0xca, 0xc1, 0xdb, 0xe4, 0x26, 0x54, 0x13, 0xa4, 0xa6,
	0x1c, 0x5e, 0xa9, 0x07, 0x14, 0x08, 0x5d, 0xe6, 0x54, 0x0d, 0xdd, 0x84, 0xaa, 0x8c, 0x45, 0xf4,
	0x28, 0x66, 0x09, 0xf5, 0xf5, 0x05, 0x0a, 0xe1, 0x41, 0xbc, 0xa1, 0x20, 0x32, 0x58, 0x49, 0x89,
	0x22, 0x8f, 0x8b, 0xe7, 0x0d, 0x56, 0x94, 0xfb, 0xc8, 0xeb, 0x26, 0xc8, 0xa1, 0x1b, 0x1e, 0xc4,
	0xcd, 0xd2, 0x39, 0x69, 0x95, 0x28, 0xf7, 0xb7, 0x0e, 0x62, 0xc9, 0x57, 0x40, 0x52, 0x81, 0xb4,
	0xca, 0xe7, 0xe5, 0x4b, 0x52, 0xd8, 0x3a, 0x88, 0xed, 0x6f, 0x01, 0x38, 0x43, 0x81, 0x55, 0xa1,
	0xbc, 0xcb, 0x7b, 0x3c, 0x3a, 0xe4, 0x8d, 0x0b, 0x56, 0x19, 0x8a, 0x9f, 0x32, 0xde, 0x30, 0x2c,
	0x13, 0x16, 0x1e, 0x26, 0xe4, 0xb0, 0x51, 0x90, 0xa3, 0xa7, 0x51, 0x9a, 0x36, 0x8a, 0xf6, 0x1f,
	0x8c, 0x51, 0xcd, 0x2a, 0x22, 0xf2, 0x3a, 0xd9, 0x04, 0x33, 0xa4, 0x61, 0xde, 0xef, 0xd6, 0xce,
	0xc6, 0x5e, 0x66, 0x1b, 0x4e, 0x39, 0xa4, 0x21, 0x7a, 0x5f, 0x13, 0xca, 0x8c, 0xa3, 0x4b, 0xe8,
	0x70, 0x90, 0x4d, 0xa5, 0xeb, 0xa0, 0xb5, 0x4b, 0xbd, 0xab, 0x6b, 0x59, 0x8a, 0x57, 0x9e, 0xc1,
	0xbe, 0x05, 0xe5, 0x0d, 0x35, 0xb4, 0x00, 0x4a, 0xeb, 0x51, 0x18, 0x46, 0xf2, 0x34, 0x75, 0xa8,
	0xe4, 0x6e, 0x49, 0xfb, 0x7b, 0x70, 0xf5, 0x23, 0x2a, 0xf2, 0xfb, 0x6f, 0x70, 0x91, 0x1c, 0x3b,
	0xf4, 0x85, 0x75, 0x1b, 0x4c, 0x99, 0xa9, 0xba, 0x09, 0x7d, 0xa1, 0x6d, 0xb3, 0x2a, 0x0f, 0xf0,
	0x80, 0xa4, 0xd4, 0xa1, 0x2f, 0x9c, 0x72, 0x47, 0x0d, 0x64, 0xb8, 0x99, 0x88, 0x23, 0x95, 0x2c,
	0xb1, 0xf2, 0xed, 0x1f, 0x1a, 0xd0, 0x9c, 0xbe, 0x45, 0x1a, 0x5b, 0x77, 0xa1, 0xa2, 0xf7, 0x48,
	0x63, 0xbd, 0x49, 0x6d, 0xb8, 0x49, 0x1a, 0x3b, 0x66, 0x47, 0x8f, 0x5e, 0xb2, 0x8d, 0xb4, 0xf1,
	0x7d, 0x22, 0x7d, 0x5f, 0x24, 0xc7, 0x28, 0x0a, 0xd3, 0x31, 0xf7, 0x49, 0x8a, 0x5b, 0x49, 0x1e,
	0x6e, 0x8c, 0xf1, 0xa0, 0x07, 0x52, 0xbe, 0xf3, 0x3b, 0xac, 0xd4, 0x47, 0xb7, 0xcf, 0x02, 0x7f,
	0x18, 0x26, 0xcb, 0x38, 0xdf, 0xf4, 0xed, 0xbf, 0x2f, 0x8e, 0x26, 0xe8, 0xb9, 0x3c, 0x74, 0x8c,
	0xaa, 0x31, 0x4e, 0xf5, 0x8d, 0x61, 0xba, 0x9b, 0xf3, 0xf0, 0x2c, 0x79, 0x45, 0x15, 0xe7, 0x50,
	0x98, 0x17, 0x71, 0x1d, 0x6b, 0x06, 0xf9, 0xad, 0x17, 0xf1, 0x3c, 0x0a, 0x27, 0x21, 0xcd, 0x6e,
	0x62, 0x0d, 0xdb, 0x26, 0x21, 0xe6, 0x34, 0x1d, 0xc6, 0x7d, 0x77, 0x70, 0x06, 0x95, 0x23, 0x55,
	0x25, 0xf0, 0x23, 0x75, 0x0e, 0x79, 0x5f, 0x78, 0x09, 0x25, 0x22, 0x4a, 0xdc, 0x3e, 0xf3, 0xf5,
	0xc5, 0x0c, 0x1a, 0xb4, 0xcb, 0x7c, 0xeb, 0x03, 0xb8, 0xa4, 0xb3, 0xe5, 0x91, 0x22, 0xa6, 0x3c,
	0xbd, 0x88, 0xb9, 0xa8, 0x70, 0x73, 0x20, 0x29, 0x44, 0x41, 0x3a, 0x8a, 0x49, 0x13, 0x99, 0x2c,
	0x0b, 0xd2, 0x41, 0x06, 0x1b, 0x50, 0xdc, 0x8f, 0x04, 0x5e, 0xdc, 0x75, 0x47, 0x0e, 0x31, 0x95,
	0xea, 0xb9, 0xea, 0x0a, 0x6f, 0x02, 0xc2, 0xcd, 0xb8, 0xa7, 0x2f, 0xf1, 0x1d, 0x55, 0xf8, 0xec,
	0x05, 0xa4, 0xdb, 0xac, 0xae, 0x1a, 0x77, 0x96, 0xee, 0x7f, 0xf3, 0x4c, 0x3e, 0xb8, 0x13, 0xc7,
	0x11, 0xa7, 0x5c, 0x6c, 0x31, 0xef, 0x51, 0x40, 0xba, 0x58, 0x01, 0xc9, 0x81, 0xdc, 0x4d, 0xb2,
	0xe6, 0x45, 0x41, 0x94, 0x34, 0x6b, 0xab, 0x45, 0x99, 0xb8, 0x09, 0xd2, 0x59, 0x97, 0x73, 0x6b,
	0x0f, 0x56, 0xd8, 0xf0, 0xda, 0x77, 0x07, 0x3b, 0xd7, 0x5f, 0x61, 0x67, 0x2b, 0x47, 0x51, 0xc3,
	0xac, 0xcf, 0xa0, 0x91, 0xdf, 0x07, 0xab, 0xb1, 0xa5, 0x19, 0x52, 0x87, 0x2d, 0xe6, 0xb5, 0x63,
	0xe2, 0x51, 0x67, 0x39, 0x47, 0x06, 0xcb, 0xb2, 0xb1, 0x48, 0xb2, 0x3c, 0x1e, 0x49, 0xec, 0x1b,
	0xd3, 0xe2, 0xa3, 0x09, 0x0b, 0x32, 0x22, 0x36, 0x0c, 0xfb, 0xb7, 0x05, 0x78, 0xfd, 0x34, 0x3f,
	0x9b, 0xab, 0xc7, 0x9f, 0xec, 0x6b, 0xf9, 0xfa, 0x10, 0xef, 0xdf, 0x59, 0x6a, 0xac, 0x69, 0xf5,
	0x21, 0xde, 0xc2, 0xda, 0xd2, 0x0e, 0x99, 0xf0, 0xf6, 0xb5, 0x63, 0x48, 0x4b, 0xc3, 0xb9, 0xf4,
	0x1c, 0x4c, 0xb2, 0x42, 0x26, 0xf2, 0x75, 0x55, 0x55, 0xe6, 0x4f, 0x21, 0x13, 0xb2, 0xac, 0x92,
	0x0c, 0x2b, 0x84, 0xb8, 0xa7, 0x8b, 0x85, 0x32, 0xce, 0x5b, 0x3d, 0xfb, 0x07, 0xd0, 0x6c, 0x8f,
	0xca, 0x4d, 0xd1, 0x9d, 0xe3, 0xd5, 0x34, 0xc2, 0x7e, 0x71, 0x94, 0x7d, 0xfb, 0x47, 0x06, 0x5c,
	0x3b, 0x81, 0x81, 0x79, 0xdf, 0xd2, 0x27, 0x33, 0xf1, 0x47, 0x03, 0x56, 0xda, 0x82, 0x24, 0x62,
	0x34, 0xa0, 0x9e, 0x5d, 0x02, 0xb7, 0x61, 0x19, 0xfb, 0x13, 0x13, 0x1c, 0xd4, 0x25, 0x78, 0x98,
	0xbf, 0x7f, 0x26, 0x6b, 0x7a, 0x77, 0xa4, 0x83, 0x50, 0x3c, 0x67, 0x07, 0xa1, 0x2e, 0xa2, 0xdc,
	0xd4, 0x7e, 0x00, 0x97, 0xa7, 0x9c, 0x60, 0x26, 0x11, 0xda, 0x5f, 0x8c, 0xa5, 0x14, 0x2a, 0xf7,
	0x94, 0x29, 0x45, 0x1b, 0x6a, 0xf9, 0xf3, 0x9d, 0xbf, 0xed, 0x91, 0x13, 0x87, 0xf4, 0x79, 0x9d,
	0xfb, 0x61, 0x55, 0xac, 0xb3, 0x47, 0x0d, 0x6a, 0x53, 0xcf, 0xfe, 0xd2, 0x80, 0xcb, 0x1f, 0x7a,
	0x1e, 0x8d, 0xcf, 0xad, 0x17, 0x1b, 0xea, 0xe1, 0xf1, 0xa4, 0x56, 0xaa, 0xe1, 0xf1, 0x88, 0x4e,
	0xc6, 0x75, 0x77, 0x6e, 0x9d, 0x8c, 0x6a, 0xfb, 0x0a, 0x94, 0x08, 0xb2, 0x8f, 0x11, 0xcf, 0x74,
	0xf4, 0xcc, 0x5e, 0x87, 0x2b, 0xd3, 0x8e, 0x35, 0x9b, 0xb2, 0xfe, 0x33, 0x56, 0x52, 0x2a, 0x65,
	0xc9, 0xa5, 0xaf, 0x4c, 0x61, 0x3b, 0x00, 0x43, 0xeb, 0xd5, 0x6d, 0xb8, 0xd9, 0x49, 0x56, 0x06,
	0x86, 0x9b, 0x13, 0x50, 0x31, 0x2f, 0x20, 0x59, 0xc0, 0x26, 0xf4, 0xfb, 0xd4, 0x13, 0xd4, 0x57,
	0x77, 0x9a, 0xca, 0x18, 0x6a, 0x19, 0x10, 0x7b, 0x45, 0x2e, 0x5c, 0x19, 0xbb, 0xf1, 0x91, 0xfc,
	0xfc, 0xf2, 0xc7, 0x5f, 0x1a, 0x60, 0x3d, 0x8c, 0x0e, 0x79, 0x10, 0x11, 0xbf, 0x1d, 0xf5, 0x13,
	0x8f, 0xae, 0x47, 0x7c, 0x4f, 0xde, 0x24, 0x83, 0x4e, 0x96, 0xce, 0x98, 0xcc, 0xac, 0x89, 0x85,
	0x8b, 0xe4, 0xc8, 0xcd, 0xb7, 0xf3, 0xcc, 0x90, 0x1c, 0xa9, 0xc5, 0x35, 0x28, 0xa5, 0x48, 0x47,
	0x1b, 0xd8, 0x15, 0xc9, 0xd5, 0xe8, 0x0e, 0xaa, 0xce, 0x50, 0x58, 0xd6, 0xff, 0xc3, 0x52, 0x87,
	0x78, 0xbd, 0x6e, 0x12, 0xf5, 0xb9, 0xef, 0xc6, 0x2c, 0xab, 0x61, 0xeb, 0x43, 0x68, 0x8b, 0x79,
	0xf6, 0xcf, 0x0b, 0xd0, 0x50, 0x89, 0xfb, 0x5c, 0xb8, 0xfc, 0x2e, 0x2c, 0xab, 0xfd, 0xdd, 0x43,
	0x26, 0xf6, 0xb1, 0xc8, 0x39, 0x9d, 0xdd, 0xba, 0x42, 0xff, 0x94, 0x89, 0x7d, 0x59, 0x1d, 0x3d,
	0x04, 0x2b, 0xf7, 0x7d, 0xd4, 0x57, 0x75, 0xd2, 0xc2, 0xa9, 0x24, 0x1a, 0x43, 0x12, 0x51, 0x5f,
	0x96, 0x45, 0x83, 0x8e, 0xec, 0xe2, 0xb0, 0x23, 0x6b, 0x7d, 0x07, 0x6a, 0x82, 0x89, 0x80, 0xba,
	0x5a, 0x8a, 0xa5, 0x53, 0x69, 0x56, 0x11, 0x57, 0x01, 0x6c, 0x06, 0xd6, 0x63, 0x4a, 0x7c, 0x99,
	0x8e, 0xce, 0x45, 0x48, 0xd7, 0xc0, 0x8c, 0x99, 0x37, 0xec, 0xca, 0x56, 0x9c, 0x72, 0xcc, 0x3c,
	0x19, 0xad, 0xed, 0xbf, 0x2c, 0x4e, 0x54, 0x36, 0xca, 0x30, 0x67, 0x8b, 0x67, 0xcf, 0x60, 0x29,
	0xee, 0xb9, 0x1d, 0xec, 0xa4, 0xa8, 0x48, 0x31, 0x4b, 0xdf, 0x5b, 0x75, 0x60, 0x9c, 0x5a, 0xdc,
	0x53, 0x23, 0x4c, 0xf6, 0xa7, 0x74, 0xca, 0x8a, 0x73, 0xea, 0x94, 0xcd, 0xb3, 0x9d, 0x64, 0x3d,
	0x87, 0x7a, 0x56, 0x5f, 0xbf, 0x5a, 0xf1, 0x5f, 0xd5, 0x45, 0x36, 0x52, 0xbd, 0x03, 0x8d, 0x01,
	0xd5, 0xac, 0xe7, 0xa0, 0x1b, 0xac, 0x1a, 0x2d, 0xeb, 0x3b, 0x7c, 0x3c, 0x48, 0x46, 0xbd, 0x88,
	0xef, 0xe9, 0x12, 0xff, 0xe4, 0xf6, 0xc8, 0xb8, 0xef, 0x65, 0x79, 0x2b, 0x9a, 0xd8, 0x23, 0xd5,
	0x70, 0x47, 0x42, 0xe6, 0x4b, 0xda, 0xf7, 0x93, 0x97, 0x0d, 0xf6, 0xda, 0x91, 0xce, 0x63, 0xa8,
	0xc8, 0xf2, 0x4a, 0x11, 0xaa, 0xbc, 0x84, 0xd0, 0xa4, 0xa9, 0x3b, 0xa6, 0xfc, 0x1a, 0x29, 0xbd,
	0xab, 0x9f, 0x00, 0x64, 0xb1, 0x06, 0x48, 0xe8, 0x24, 0x0f, 0xc2, 0x46, 0xbf, 0x17, 0x71, 0xfb,
	0x37, 0x06, 0xbc, 0x39, 0x1a, 0xab, 0xe2, 0x28, 0x11, 0xba, 0xb7, 0xff, 0x50, 0x35, 0x32, 0xe7,
	0x98, 0x30, 0x5e, 0x86, 0x92, 0x2c, 0x61, 0x06, 0xd9, 0xf5, 0x62, 0xc8, 0xbc, 0x4d, 0x7f, 0xd8,
	0x7c, 0x5b, 0xc8, 0x35, 0xdf, 0xae, 0x43, 0x65, 0xfc, 0xed, 0xc1, 0xf4, 0xf4, 0xa3, 0x83, 0xfd,
	0x0c, 0x6e, 0xbd, 0x9c, 0xed, 0xd9, 0xc2, 0xee, 0xef, 0x0d, 0xb8, 0x39, 0x61, 0x6e, 0x3e, 0x8b,
	0xa4, 0xc0, 0x5a, 0xfd, 0x74, 0x5f, 0xb7, 0xc1, 0x4f, 0x2b, 0xaa, 0xa7, 0x3e, 0x9b, 0x14, 0xa6,
	0x3f, 0x9b, 0x6c, 0x43, 0x55, 0xe3, 0xe4, 0x1e, 0x80, 0x66, 0x2c, 0xb6, 0x40, 0x51, 0xc0, 0xcb,
	0xe9, 0x9f, 0x06, 0xbc, 0x31, 0x96, 0x6e, 0x8f, 0x17, 0x7e, 0xf3, 0xd3, 0xe3, 0x27, 0x50, 0x8b,
	0x62, 0x31, 0x2c, 0x47, 0x8b, 0xaf, 0x50, 0x8e, 0x42, 0x14, 0x67, 0xe3, 0xe9, 0x86, 0x30, 0x34,
	0x9a, 0xc5, 0x9c, 0xd1, 0xd8, 0x3b, 0x60, 0xbf, 0xec, 0xbc, 0xb3, 0x19, 0xc0, 0x3f, 0x0c, 0xb0,
	0x4f, 0x21, 0x27, 0x6d, 0x60, 0x2b, 0xed, 0x4e, 0x9c, 0xdd, 0x98, 0xd3, 0xd9, 0x5f, 0x22, 0xf2,
	0x77, 0xe0, 0x72, 0x87, 0xba, 0x1d, 0xa2, 0x9a, 0x00, 0x13, 0x4f, 0x27, 0x17, 0x3b, 0xf4, 0x01,
	0x91, 0xe5, 0xfc, 0xfa, 0x14, 0x67, 0x5b, 0xc8, 0xcb, 0xed, 0xd7, 0x06, 0xdc, 0x1c, 0x8b, 0x62,
	0x6d, 0xca, 0xfd, 0x8f, 0xd8, 0x9e, 0xc0, 0x08, 0x38, 0x47, 0x33, 0x19, 0xa8, 0xb3, 0x38, 0xa6,
	0xce, 0x63, 0xec, 0xf3, 0x64, 0x6c, 0x1d, 0xef, 0x32, 0x04, 0x8b, 0x08, 0xc1, 0x5a, 0xcb, 0x22,
	0xda, 0x65, 0xbe, 0xfd, 0x27, 0x03, 0x56, 0x4f, 0xe7, 0x76, 0xae, 0xc5, 0xe4, 0xab, 0x73, 0x3c,
	0x7c, 0xf3, 0x2d, 0xe5, 0xde, 0x7c, 0xed, 0x5f, 0x19, 0x13, 0x6d, 0x8c, 0x0f, 0xfb, 0x3e, 0xa3,
	0xdc, 0xa3, 0x32, 0xa8, 0x7d, 0xd5, 0x42, 0x5f, 0x81, 0xc5, 0x0e, 0xed, 0x32, 0x9e, 0x9d, 0x00,
	0x27, 0x12, 0x8a, 0xdd, 0x82, 0xec, 0x00, 0x38, 0xb1, 0x7f, 0x52, 0x84, 0xe6, 0x34, 0x3e, 0x31,
	0xd6, 0x9e, 0xe3, 0x3f, 0x82, 0x13, 0x5e, 0xc1, 0x65, 0x1e, 0xc8, 0x06, 0x25, 0x3b, 0x8e, 0x25,
	0xa6, 0xb7, 0x4f, 0x92, 0x30, 0x63, 0x13, 0x27, 0x32, 0x5b, 0xe6, 0x51, 0x87, 0x05, 0x4c, 0x1c,
	0xbb, 0x01, 0x3d, 0xa0, 0x81, 0xe6, 0xb7, 0x9e, 0x41, 0x9f, 0x4a, 0xa0, 0x4c, 0x03, 0x64, 0xea,
	0x1c, 0xbb, 0x7b, 0x84, 0xa7, 0x1a, 0x51, 0xa7, 0x01, 0x08, 0x7f, 0x44, 0x78, 0xaa, 0x30, 0x73,
	0xf7, 0x34, 0xf6, 0xd3, 0x11, 0x55, 0x3d, 0xfc, 0x64, 0xf7, 0xf4, 0x16, 0x0d, 0x15, 0xee, 0x0d,
	0x00, 0x45, 0x35, 0xd7, 0x3b, 0xac, 0x20, 0x04, 0xbb, 0x87, 0xef, 0x00, 0xc4, 0x01, 0x11, 0x3a,
	0x91, 0xab, 0xa0, 0x34, 0x2e, 0x4a, 0x69, 0xc8, 0xdd, 0x5a, 0x72, 0x45, 0x95, 0x46, 0x71, 0x36,
	0xb4, 0xde, 0x87, 0x01, 0xdf, 0xea, 0x23, 0xc0, 0x8f, 0x1a, 0xf2, 0xa3, 0x6d, 0xbd, 0x80, 0xdf,
	0xd4, 0x78, 0x6e, 0x66, 0xff, 0x6c, 0xd2, 0x6d, 0x47, 0x0d, 0x68, 0x36, 0x3f, 0xd8, 0x86, 0x8a,
	0xcc, 0xbc, 0x55, 0xf0, 0x29, 0x60, 0xf0, 0x79, 0xf7, 0x6c, 0x7f, 0x1f, 0xe4, 0xac, 0xc1, 0x31,
	0x25, 0x0d, 0x0c, 0x3f, 0x3e, 0x54, 0xb7, 0x3e, 0x69, 0x6d, 0x86, 0x5b, 0x34, 0xec, 0xd0, 0x73,
	0xfd, 0x6e, 0x62, 0xbd, 0x01, 0xb5, 0x7d, 0x4a, 0xfc, 0x43, 0x4a, 0x12, 0xb7, 0x9f, 0x04, 0x3a,
	0x70, 0x56, 0x33, 0xd8, 0x6e, 0x12, 0xd8, 0xff, 0x36, 0x00, 0x5a, 0x3d, 0xdc, 0x48, 0x86, 0xe3,
	0x0f, 0x26, 0xde, 0x43, 0x6e, 0x9d, 0x78, 0x86, 0x1c, 0x77, 0xc3, 0x57, 0x90, 0x5b, 0xb0, 0x94,
	0x86, 0x24, 0x08, 0x5c, 0x8f, 0x24, 0x7e, 0x6e, 0xd3, 0x1a, 0x42, 0xd7, 0x49, 0xe2, 0xef, 0x26,
	0x81, 0x2c, 0x09, 0x06, 0xeb, 0xaa, 0x09, 0x5e, 0xf6, 0xf4, 0xd2, 0x0a, 0x2c, 0x62, 0x31, 0xa2,
	0xeb, 0x37, 0x35, 0xb1, 0x9a, 0x50, 0xf6, 0x22, 0x2e, 0x28, 0x17, 0x3a, 0x71, 0xc9, 0xa6, 0xd6,
	0x55, 0x28, 0x63, 0x37, 0x7c, 0xd0, 0xe5, 0x2e, 0xc9, 0xa9, 0xca, 0x76, 0xf4, 0x7b, 0x9a, 0x48,
	0xd1, 0x14, 0x07, 0xef, 0x69, 0xcf, 0x53, 0xfb, 0x0b, 0x63, 0xf4, 0xd1, 0x3f, 0x4b, 0x00, 0x72,
	0x57, 0xbc, 0x91, 0xcf, 0xa7, 0xc6, 0xa5, 0x5f, 0x38, 0x83, 0xf4, 0xa7, 0xe6, 0x2e, 0xc5, 0xa9,
	0xb9, 0x8b, 0xfd, 0x63, 0x03, 0x2e, 0x8d, 0xbf, 0x51, 0xfe, 0x2f, 0xc2, 0xc6, 0x94, 0x68, 0xf6,
	0x29, 0xac, 0x4c, 0x72, 0x32, 0x9b, 0x2b, 0x48, 0x1d, 0x46, 0x04, 0xdd, 0x40, 0xe9, 0x50, 0x4e,
	0xec, 0x6f, 0x8f, 0x66, 0x83, 0xe3, 0x9b, 0x48, 0xf3, 0x1b, 0x7c, 0x68, 0xe4, 0x3f, 0xfc, 0x85,
	0x01, 0x37, 0x46, 0x4b, 0xc4, 0xc1, 0x47, 0x1b, 0xdc, 0x97, 0xdf, 0x8d, 0x3d, 0x8f, 0x1a, 0xd3,
	0x9e, 0x47, 0x07, 0xcf, 0x90, 0x85, 0x57, 0x7c, 0x86, 0x1c, 0x72, 0x59, 0xcc, 0x73, 0xb9, 0x0e,
	0xb7, 0x4f, 0x3b, 0x9e, 0x13, 0x45, 0xe1, 0x56, 0xda, 0x95, 0xdc, 0x5e, 0x03, 0x33, 0x89, 0xa2,
	0xd0, 0x0d, 0xd3, 0xae, 0x3e, 0x68, 0x39, 0x51, 0xab, 0x76, 0x0a, 0x97, 0xdb, 0xe3, 0xcf, 0x70,
	0xfe, 0x57, 0x6c, 0x08, 0xf6, 0x3a, 0x5c, 0x99, 0xb6, 0xe9, 0x4c, 0x3a, 0x7f, 0xeb, 0x21, 0x5c,
	0x3f, 0x25, 0x2f, 0xb3, 0x2e, 0x42, 0xbd, 0xd5, 0xf5, 0x50, 0xa0, 0xee, 0x4e, 0x6b, 0x63, 0xbb,
	0x71, 0xc1, 0xb2, 0x60, 0x69, 0x00, 0x5a, 0x7f, 0xba, 0xd3, 0xde, 0x68, 0x18, 0x6f, 0xdd, 0x85,
	0x2b, 0x93, 0xed, 0xed, 0x0d, 0xde, 0x0f, 0xad, 0x12, 0x14, 0x76, 0xb6, 0xd5, 0x43, 0xef, 0xce,
	0xa3, 0x47, 0x0d, 0xe3, 0xc1, 0x16, 0x34, 0xbd, 0x28, 0x5c, 0x3b, 0x66, 0xc7, 0x51, 0x5f, 0xf2,
	0x14, 0x46, 0x3e, 0x0d, 0xd4, 0x1f, 0x7b, 0x9f, 0xbf, 0xdb, 0x8d, 0x02, 0xc2, 0xbb, 0x6b, 0xef,
	0xdf, 0x17, 0x62, 0xcd, 0x8b, 0xc2, 0xb7, 0x11, 0xec, 0x45, 0xc1, 0xdb, 0x24, 0x8e, 0xdf, 0xce,
	0x69, 0x7d, 0xf0, 0x4f, 0x60, 0xa7, 0x84, 0x28, 0xef, 0xfd, 0x37, 0x00, 0x00, 0xff, 0xff, 0x00,
	0x36, 0xa0, 0x21, 0x32, 0x28, 0x00, 0x00,
}
