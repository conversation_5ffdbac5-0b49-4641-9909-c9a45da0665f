// Code generated by protoc-gen-gogo.
// source: guildcircle_.proto
// DO NOT EDIT!

/*
	Package guild is a generated protocol buffer package.

	It is generated from these files:
		guildcircle_.proto

	It has these top-level messages:
		GuildCircleTopicImage
		GuildCircleTopic
		GuildCircleCommentImage
		GuildCircleTopicComment
		GuildCircleDynamicData
		GuildCirclePostTopicReq
		GuildCirclePostTopicResp
		GuildCircleGetTopicListReq
		GuildCircleGetTopicListResp
		GuildCircleGetTopicReq
		GuildCircleGetTopicResp
		GuildCirclePostCommentReq
		GuildCirclePostCommentResp
		GuildCircleLikeTopicReq
		GuildCircleLikeTopicResp
		GuildCircleReportTopicReq
		GuildCircleReportTopicResp
		GuildCircleDeleteTopicReq
		GuildCircleDeleteTopicResp
		GuildCircleDeleteCommentReq
		GuildCircleDeleteCommentResp
		GuildCircleGetCircleDetailReq
		GuildCircleGetCircleDetailResp
		GuildCircleGetUserTopicReq
		GuildCircleGetUserTopicResp
		GuildCircleGetLikeUserListReq
		GuildCircleGetLikeUserListResp
		GuildCircleMarkReadedReq
		GuildCircleMarkReadedResp
		GuildCircleCancelHighlightTopicReq
		GuildCircleCancelHighlightTopicResp
		GuildCommentReplayTargetBase
		GuildCommentBase
		GuildCircleTopicNomalComment
		GuildCircleGetNomalCommentListReq
		GuildCircleGetNomalCommentListResp
		GuildCircleGetCommentReplyListReq
		GuildCircleGetCommentReplyListResp
		GuildCircleAddTopicTagReq
		GuildCircleAddTopicTagResp
*/
package guild

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type GuildCircleTopicCommentStatus int32

const (
	GuildCircleTopicCommentStatus_GUILD_COMMENT_STATUS_NORMAL  GuildCircleTopicCommentStatus = 0
	GuildCircleTopicCommentStatus_GUILD_COMMENT_STATUS_DELETED GuildCircleTopicCommentStatus = 1
	GuildCircleTopicCommentStatus_GUILD_COMMENT_STATUS_SHIELD  GuildCircleTopicCommentStatus = 2
)

var GuildCircleTopicCommentStatus_name = map[int32]string{
	0: "GUILD_COMMENT_STATUS_NORMAL",
	1: "GUILD_COMMENT_STATUS_DELETED",
	2: "GUILD_COMMENT_STATUS_SHIELD",
}
var GuildCircleTopicCommentStatus_value = map[string]int32{
	"GUILD_COMMENT_STATUS_NORMAL":  0,
	"GUILD_COMMENT_STATUS_DELETED": 1,
	"GUILD_COMMENT_STATUS_SHIELD":  2,
}

func (x GuildCircleTopicCommentStatus) Enum() *GuildCircleTopicCommentStatus {
	p := new(GuildCircleTopicCommentStatus)
	*p = x
	return p
}
func (x GuildCircleTopicCommentStatus) String() string {
	return proto.EnumName(GuildCircleTopicCommentStatus_name, int32(x))
}
func (x *GuildCircleTopicCommentStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GuildCircleTopicCommentStatus_value, data, "GuildCircleTopicCommentStatus")
	if err != nil {
		return err
	}
	*x = GuildCircleTopicCommentStatus(value)
	return nil
}
func (GuildCircleTopicCommentStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{0}
}

type GuildCircleTopicCommentType int32

const (
	GuildCircleTopicCommentType_GUILD_COMMENT_TYPE_NORMAL      GuildCircleTopicCommentType = 0
	GuildCircleTopicCommentType_GUILD_COMMENT_TYPE_REPLY       GuildCircleTopicCommentType = 1
	GuildCircleTopicCommentType_GUILD_COMMENT_TYPE_REPLY_REPLY GuildCircleTopicCommentType = 2
	GuildCircleTopicCommentType_GUILD_COMMENT_TYPE_HAVE_REPLY  GuildCircleTopicCommentType = 4
)

var GuildCircleTopicCommentType_name = map[int32]string{
	0: "GUILD_COMMENT_TYPE_NORMAL",
	1: "GUILD_COMMENT_TYPE_REPLY",
	2: "GUILD_COMMENT_TYPE_REPLY_REPLY",
	4: "GUILD_COMMENT_TYPE_HAVE_REPLY",
}
var GuildCircleTopicCommentType_value = map[string]int32{
	"GUILD_COMMENT_TYPE_NORMAL":      0,
	"GUILD_COMMENT_TYPE_REPLY":       1,
	"GUILD_COMMENT_TYPE_REPLY_REPLY": 2,
	"GUILD_COMMENT_TYPE_HAVE_REPLY":  4,
}

func (x GuildCircleTopicCommentType) Enum() *GuildCircleTopicCommentType {
	p := new(GuildCircleTopicCommentType)
	*p = x
	return p
}
func (x GuildCircleTopicCommentType) String() string {
	return proto.EnumName(GuildCircleTopicCommentType_name, int32(x))
}
func (x *GuildCircleTopicCommentType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GuildCircleTopicCommentType_value, data, "GuildCircleTopicCommentType")
	if err != nil {
		return err
	}
	*x = GuildCircleTopicCommentType(value)
	return nil
}
func (GuildCircleTopicCommentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{1}
}

type GuildCircleTopic_TOPIC_STATE int32

const (
	GuildCircleTopic_STATE_HIGHT_LIGHT GuildCircleTopic_TOPIC_STATE = 1
)

var GuildCircleTopic_TOPIC_STATE_name = map[int32]string{
	1: "STATE_HIGHT_LIGHT",
}
var GuildCircleTopic_TOPIC_STATE_value = map[string]int32{
	"STATE_HIGHT_LIGHT": 1,
}

func (x GuildCircleTopic_TOPIC_STATE) Enum() *GuildCircleTopic_TOPIC_STATE {
	p := new(GuildCircleTopic_TOPIC_STATE)
	*p = x
	return p
}
func (x GuildCircleTopic_TOPIC_STATE) String() string {
	return proto.EnumName(GuildCircleTopic_TOPIC_STATE_name, int32(x))
}
func (x *GuildCircleTopic_TOPIC_STATE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GuildCircleTopic_TOPIC_STATE_value, data, "GuildCircleTopic_TOPIC_STATE")
	if err != nil {
		return err
	}
	*x = GuildCircleTopic_TOPIC_STATE(value)
	return nil
}
func (GuildCircleTopic_TOPIC_STATE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{1, 0}
}

type GuildCircleTopicComment_TopicCommentStatus int32

const (
	GuildCircleTopicComment_NORMAL  GuildCircleTopicComment_TopicCommentStatus = 0
	GuildCircleTopicComment_DELETED GuildCircleTopicComment_TopicCommentStatus = 1
	GuildCircleTopicComment_SHIELD  GuildCircleTopicComment_TopicCommentStatus = 2
)

var GuildCircleTopicComment_TopicCommentStatus_name = map[int32]string{
	0: "NORMAL",
	1: "DELETED",
	2: "SHIELD",
}
var GuildCircleTopicComment_TopicCommentStatus_value = map[string]int32{
	"NORMAL":  0,
	"DELETED": 1,
	"SHIELD":  2,
}

func (x GuildCircleTopicComment_TopicCommentStatus) Enum() *GuildCircleTopicComment_TopicCommentStatus {
	p := new(GuildCircleTopicComment_TopicCommentStatus)
	*p = x
	return p
}
func (x GuildCircleTopicComment_TopicCommentStatus) String() string {
	return proto.EnumName(GuildCircleTopicComment_TopicCommentStatus_name, int32(x))
}
func (x *GuildCircleTopicComment_TopicCommentStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GuildCircleTopicComment_TopicCommentStatus_value, data, "GuildCircleTopicComment_TopicCommentStatus")
	if err != nil {
		return err
	}
	*x = GuildCircleTopicComment_TopicCommentStatus(value)
	return nil
}
func (GuildCircleTopicComment_TopicCommentStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{3, 0}
}

// 游戏圈主题配图
type GuildCircleTopicImage struct {
	ThumbUrl    string `protobuf:"bytes,1,req,name=thumb_url,json=thumbUrl" json:"thumb_url"`
	ImageUrl    string `protobuf:"bytes,2,req,name=image_url,json=imageUrl" json:"image_url"`
	ImageWidth  uint32 `protobuf:"varint,3,opt,name=image_width,json=imageWidth" json:"image_width"`
	ImageHeight uint32 `protobuf:"varint,4,opt,name=image_height,json=imageHeight" json:"image_height"`
}

func (m *GuildCircleTopicImage) Reset()         { *m = GuildCircleTopicImage{} }
func (m *GuildCircleTopicImage) String() string { return proto.CompactTextString(m) }
func (*GuildCircleTopicImage) ProtoMessage()    {}
func (*GuildCircleTopicImage) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{0}
}

func (m *GuildCircleTopicImage) GetThumbUrl() string {
	if m != nil {
		return m.ThumbUrl
	}
	return ""
}

func (m *GuildCircleTopicImage) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *GuildCircleTopicImage) GetImageWidth() uint32 {
	if m != nil {
		return m.ImageWidth
	}
	return 0
}

func (m *GuildCircleTopicImage) GetImageHeight() uint32 {
	if m != nil {
		return m.ImageHeight
	}
	return 0
}

// 游戏圈主题
type GuildCircleTopic struct {
	GuildId             uint32                   `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId             uint32                   `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Title               string                   `protobuf:"bytes,4,req,name=title" json:"title"`
	Content             string                   `protobuf:"bytes,5,req,name=content" json:"content"`
	CreateTime          uint32                   `protobuf:"varint,6,req,name=create_time,json=createTime" json:"create_time"`
	ImageList           []*GuildCircleTopicImage `protobuf:"bytes,7,rep,name=image_list,json=imageList" json:"image_list,omitempty"`
	LikeCount           uint32                   `protobuf:"varint,8,req,name=like_count,json=likeCount" json:"like_count"`
	CommentCount        uint32                   `protobuf:"varint,9,req,name=comment_count,json=commentCount" json:"comment_count"`
	IsLiked             uint32                   `protobuf:"varint,10,req,name=is_liked,json=isLiked" json:"is_liked"`
	TopicState          uint32                   `protobuf:"varint,11,req,name=topic_state,json=topicState" json:"topic_state"`
	LastCommentTime     uint32                   `protobuf:"varint,12,req,name=last_comment_time,json=lastCommentTime" json:"last_comment_time"`
	Creator             *ga.CircleUser           `protobuf:"bytes,13,opt,name=creator" json:"creator,omitempty"`
	CreateTimeDesc      string                   `protobuf:"bytes,14,req,name=create_time_desc,json=createTimeDesc" json:"create_time_desc"`
	LastCommentTimeDesc string                   `protobuf:"bytes,15,req,name=last_comment_time_desc,json=lastCommentTimeDesc" json:"last_comment_time_desc"`
}

func (m *GuildCircleTopic) Reset()                    { *m = GuildCircleTopic{} }
func (m *GuildCircleTopic) String() string            { return proto.CompactTextString(m) }
func (*GuildCircleTopic) ProtoMessage()               {}
func (*GuildCircleTopic) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle_, []int{1} }

func (m *GuildCircleTopic) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleTopic) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleTopic) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GuildCircleTopic) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GuildCircleTopic) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GuildCircleTopic) GetImageList() []*GuildCircleTopicImage {
	if m != nil {
		return m.ImageList
	}
	return nil
}

func (m *GuildCircleTopic) GetLikeCount() uint32 {
	if m != nil {
		return m.LikeCount
	}
	return 0
}

func (m *GuildCircleTopic) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *GuildCircleTopic) GetIsLiked() uint32 {
	if m != nil {
		return m.IsLiked
	}
	return 0
}

func (m *GuildCircleTopic) GetTopicState() uint32 {
	if m != nil {
		return m.TopicState
	}
	return 0
}

func (m *GuildCircleTopic) GetLastCommentTime() uint32 {
	if m != nil {
		return m.LastCommentTime
	}
	return 0
}

func (m *GuildCircleTopic) GetCreator() *ga.CircleUser {
	if m != nil {
		return m.Creator
	}
	return nil
}

func (m *GuildCircleTopic) GetCreateTimeDesc() string {
	if m != nil {
		return m.CreateTimeDesc
	}
	return ""
}

func (m *GuildCircleTopic) GetLastCommentTimeDesc() string {
	if m != nil {
		return m.LastCommentTimeDesc
	}
	return ""
}

// 游戏圈评论配图
type GuildCircleCommentImage struct {
	ThumbUrl string `protobuf:"bytes,1,req,name=thumb_url,json=thumbUrl" json:"thumb_url"`
	ImageUrl string `protobuf:"bytes,2,req,name=image_url,json=imageUrl" json:"image_url"`
}

func (m *GuildCircleCommentImage) Reset()         { *m = GuildCircleCommentImage{} }
func (m *GuildCircleCommentImage) String() string { return proto.CompactTextString(m) }
func (*GuildCircleCommentImage) ProtoMessage()    {}
func (*GuildCircleCommentImage) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{2}
}

func (m *GuildCircleCommentImage) GetThumbUrl() string {
	if m != nil {
		return m.ThumbUrl
	}
	return ""
}

func (m *GuildCircleCommentImage) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

// 游戏圈评论
type GuildCircleTopicComment struct {
	CommentId      uint32                     `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	CircleId       uint32                     `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId        uint32                     `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Content        string                     `protobuf:"bytes,4,req,name=content" json:"content"`
	Creator        *ga.CircleUser             `protobuf:"bytes,5,req,name=creator" json:"creator,omitempty"`
	CreateTime     uint32                     `protobuf:"varint,6,req,name=create_time,json=createTime" json:"create_time"`
	Status         uint32                     `protobuf:"varint,7,req,name=status" json:"status"`
	RepliedComment *GuildCircleTopicComment   `protobuf:"bytes,8,opt,name=replied_comment,json=repliedComment" json:"replied_comment,omitempty"`
	CreateTimeDesc string                     `protobuf:"bytes,9,req,name=create_time_desc,json=createTimeDesc" json:"create_time_desc"`
	ImageList      []*GuildCircleCommentImage `protobuf:"bytes,10,rep,name=image_list,json=imageList" json:"image_list,omitempty"`
	Floor          int32                      `protobuf:"varint,11,opt,name=floor" json:"floor"`
}

func (m *GuildCircleTopicComment) Reset()         { *m = GuildCircleTopicComment{} }
func (m *GuildCircleTopicComment) String() string { return proto.CompactTextString(m) }
func (*GuildCircleTopicComment) ProtoMessage()    {}
func (*GuildCircleTopicComment) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{3}
}

func (m *GuildCircleTopicComment) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *GuildCircleTopicComment) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *GuildCircleTopicComment) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleTopicComment) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GuildCircleTopicComment) GetCreator() *ga.CircleUser {
	if m != nil {
		return m.Creator
	}
	return nil
}

func (m *GuildCircleTopicComment) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GuildCircleTopicComment) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GuildCircleTopicComment) GetRepliedComment() *GuildCircleTopicComment {
	if m != nil {
		return m.RepliedComment
	}
	return nil
}

func (m *GuildCircleTopicComment) GetCreateTimeDesc() string {
	if m != nil {
		return m.CreateTimeDesc
	}
	return ""
}

func (m *GuildCircleTopicComment) GetImageList() []*GuildCircleCommentImage {
	if m != nil {
		return m.ImageList
	}
	return nil
}

func (m *GuildCircleTopicComment) GetFloor() int32 {
	if m != nil {
		return m.Floor
	}
	return 0
}

// 圈子概要数据
type GuildCircleDynamicData struct {
	GuildId         uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicCount      uint32 `protobuf:"varint,2,opt,name=topic_count,json=topicCount" json:"topic_count"`
	TodayTopicCount uint32 `protobuf:"varint,3,opt,name=today_topic_count,json=todayTopicCount" json:"today_topic_count"`
}

func (m *GuildCircleDynamicData) Reset()         { *m = GuildCircleDynamicData{} }
func (m *GuildCircleDynamicData) String() string { return proto.CompactTextString(m) }
func (*GuildCircleDynamicData) ProtoMessage()    {}
func (*GuildCircleDynamicData) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{4}
}

func (m *GuildCircleDynamicData) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleDynamicData) GetTopicCount() uint32 {
	if m != nil {
		return m.TopicCount
	}
	return 0
}

func (m *GuildCircleDynamicData) GetTodayTopicCount() uint32 {
	if m != nil {
		return m.TodayTopicCount
	}
	return 0
}

// 发表主题
type GuildCirclePostTopicReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId    uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ClientId   string      `protobuf:"bytes,3,req,name=client_id,json=clientId" json:"client_id"`
	Title      string      `protobuf:"bytes,4,opt,name=title" json:"title"`
	Content    string      `protobuf:"bytes,5,req,name=content" json:"content"`
	ImgKeyList []string    `protobuf:"bytes,6,rep,name=img_key_list,json=imgKeyList" json:"img_key_list,omitempty"`
	Highlight  bool        `protobuf:"varint,7,opt,name=highlight" json:"highlight"`
}

func (m *GuildCirclePostTopicReq) Reset()         { *m = GuildCirclePostTopicReq{} }
func (m *GuildCirclePostTopicReq) String() string { return proto.CompactTextString(m) }
func (*GuildCirclePostTopicReq) ProtoMessage()    {}
func (*GuildCirclePostTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{5}
}

func (m *GuildCirclePostTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCirclePostTopicReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCirclePostTopicReq) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *GuildCirclePostTopicReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GuildCirclePostTopicReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GuildCirclePostTopicReq) GetImgKeyList() []string {
	if m != nil {
		return m.ImgKeyList
	}
	return nil
}

func (m *GuildCirclePostTopicReq) GetHighlight() bool {
	if m != nil {
		return m.Highlight
	}
	return false
}

type GuildCirclePostTopicResp struct {
	BaseResp *ga.BaseResp      `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ClientId string            `protobuf:"bytes,2,req,name=client_id,json=clientId" json:"client_id"`
	Topic    *GuildCircleTopic `protobuf:"bytes,3,req,name=topic" json:"topic,omitempty"`
}

func (m *GuildCirclePostTopicResp) Reset()         { *m = GuildCirclePostTopicResp{} }
func (m *GuildCirclePostTopicResp) String() string { return proto.CompactTextString(m) }
func (*GuildCirclePostTopicResp) ProtoMessage()    {}
func (*GuildCirclePostTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{6}
}

func (m *GuildCirclePostTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCirclePostTopicResp) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *GuildCirclePostTopicResp) GetTopic() *GuildCircleTopic {
	if m != nil {
		return m.Topic
	}
	return nil
}

// 获取topic列表
type GuildCircleGetTopicListReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId      uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	PageCount    uint32      `protobuf:"varint,3,req,name=page_count,json=pageCount" json:"page_count"`
	PagePosition uint32      `protobuf:"varint,4,req,name=page_position,json=pagePosition" json:"page_position"`
	Highlight    bool        `protobuf:"varint,5,req,name=highlight" json:"highlight"`
	RequireCount bool        `protobuf:"varint,6,opt,name=require_count,json=requireCount" json:"require_count"`
}

func (m *GuildCircleGetTopicListReq) Reset()         { *m = GuildCircleGetTopicListReq{} }
func (m *GuildCircleGetTopicListReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetTopicListReq) ProtoMessage()    {}
func (*GuildCircleGetTopicListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{7}
}

func (m *GuildCircleGetTopicListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleGetTopicListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetTopicListReq) GetPageCount() uint32 {
	if m != nil {
		return m.PageCount
	}
	return 0
}

func (m *GuildCircleGetTopicListReq) GetPagePosition() uint32 {
	if m != nil {
		return m.PagePosition
	}
	return 0
}

func (m *GuildCircleGetTopicListReq) GetHighlight() bool {
	if m != nil {
		return m.Highlight
	}
	return false
}

func (m *GuildCircleGetTopicListReq) GetRequireCount() bool {
	if m != nil {
		return m.RequireCount
	}
	return false
}

type GuildCircleGetTopicListResp struct {
	BaseResp        *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId         uint32              `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	PageCount       uint32              `protobuf:"varint,3,req,name=page_count,json=pageCount" json:"page_count"`
	PagePosition    uint32              `protobuf:"varint,4,req,name=page_position,json=pagePosition" json:"page_position"`
	TopicList       []*GuildCircleTopic `protobuf:"bytes,5,rep,name=topic_list,json=topicList" json:"topic_list,omitempty"`
	NewestTopicId   uint32              `protobuf:"varint,6,req,name=newest_topic_id,json=newestTopicId" json:"newest_topic_id"`
	TotalTopicCount uint32              `protobuf:"varint,7,opt,name=total_topic_count,json=totalTopicCount" json:"total_topic_count"`
}

func (m *GuildCircleGetTopicListResp) Reset()         { *m = GuildCircleGetTopicListResp{} }
func (m *GuildCircleGetTopicListResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetTopicListResp) ProtoMessage()    {}
func (*GuildCircleGetTopicListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{8}
}

func (m *GuildCircleGetTopicListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleGetTopicListResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetTopicListResp) GetPageCount() uint32 {
	if m != nil {
		return m.PageCount
	}
	return 0
}

func (m *GuildCircleGetTopicListResp) GetPagePosition() uint32 {
	if m != nil {
		return m.PagePosition
	}
	return 0
}

func (m *GuildCircleGetTopicListResp) GetTopicList() []*GuildCircleTopic {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *GuildCircleGetTopicListResp) GetNewestTopicId() uint32 {
	if m != nil {
		return m.NewestTopicId
	}
	return 0
}

func (m *GuildCircleGetTopicListResp) GetTotalTopicCount() uint32 {
	if m != nil {
		return m.TotalTopicCount
	}
	return 0
}

// 获取某条topic
type GuildCircleGetTopicReq struct {
	BaseReq         *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId         uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId         uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	LikeUserCount   uint32      `protobuf:"varint,4,opt,name=like_user_count,json=likeUserCount" json:"like_user_count"`
	TopCommentCount uint32      `protobuf:"varint,5,opt,name=top_comment_count,json=topCommentCount" json:"top_comment_count"`
}

func (m *GuildCircleGetTopicReq) Reset()         { *m = GuildCircleGetTopicReq{} }
func (m *GuildCircleGetTopicReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetTopicReq) ProtoMessage()    {}
func (*GuildCircleGetTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{9}
}

func (m *GuildCircleGetTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleGetTopicReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleGetTopicReq) GetLikeUserCount() uint32 {
	if m != nil {
		return m.LikeUserCount
	}
	return 0
}

func (m *GuildCircleGetTopicReq) GetTopCommentCount() uint32 {
	if m != nil {
		return m.TopCommentCount
	}
	return 0
}

type GuildCircleGetTopicResp struct {
	BaseResp       *ga.BaseResp               `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId        uint32                     `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId        uint32                     `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Topic          *GuildCircleTopic          `protobuf:"bytes,4,req,name=topic" json:"topic,omitempty"`
	LikeUserList   []string                   `protobuf:"bytes,5,rep,name=like_user_list,json=likeUserList" json:"like_user_list,omitempty"`
	TopCommentList []*GuildCircleTopicComment `protobuf:"bytes,6,rep,name=top_comment_list,json=topCommentList" json:"top_comment_list,omitempty"`
}

func (m *GuildCircleGetTopicResp) Reset()         { *m = GuildCircleGetTopicResp{} }
func (m *GuildCircleGetTopicResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetTopicResp) ProtoMessage()    {}
func (*GuildCircleGetTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{10}
}

func (m *GuildCircleGetTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleGetTopicResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleGetTopicResp) GetTopic() *GuildCircleTopic {
	if m != nil {
		return m.Topic
	}
	return nil
}

func (m *GuildCircleGetTopicResp) GetLikeUserList() []string {
	if m != nil {
		return m.LikeUserList
	}
	return nil
}

func (m *GuildCircleGetTopicResp) GetTopCommentList() []*GuildCircleTopicComment {
	if m != nil {
		return m.TopCommentList
	}
	return nil
}

// 发表评论
type GuildCirclePostCommentReq struct {
	BaseReq          *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId          uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId          uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	RepliedCommentId uint32      `protobuf:"varint,4,req,name=replied_comment_id,json=repliedCommentId" json:"replied_comment_id"`
	Content          string      `protobuf:"bytes,5,req,name=content" json:"content"`
	ImgKeyList       []string    `protobuf:"bytes,6,rep,name=img_key_list,json=imgKeyList" json:"img_key_list,omitempty"`
}

func (m *GuildCirclePostCommentReq) Reset()         { *m = GuildCirclePostCommentReq{} }
func (m *GuildCirclePostCommentReq) String() string { return proto.CompactTextString(m) }
func (*GuildCirclePostCommentReq) ProtoMessage()    {}
func (*GuildCirclePostCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{11}
}

func (m *GuildCirclePostCommentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCirclePostCommentReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCirclePostCommentReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCirclePostCommentReq) GetRepliedCommentId() uint32 {
	if m != nil {
		return m.RepliedCommentId
	}
	return 0
}

func (m *GuildCirclePostCommentReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GuildCirclePostCommentReq) GetImgKeyList() []string {
	if m != nil {
		return m.ImgKeyList
	}
	return nil
}

type GuildCirclePostCommentResp struct {
	BaseResp         *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId          uint32                   `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId          uint32                   `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	RepliedCommentId uint32                   `protobuf:"varint,4,req,name=replied_comment_id,json=repliedCommentId" json:"replied_comment_id"`
	Comment          *GuildCircleTopicComment `protobuf:"bytes,5,req,name=comment" json:"comment,omitempty"`
}

func (m *GuildCirclePostCommentResp) Reset()         { *m = GuildCirclePostCommentResp{} }
func (m *GuildCirclePostCommentResp) String() string { return proto.CompactTextString(m) }
func (*GuildCirclePostCommentResp) ProtoMessage()    {}
func (*GuildCirclePostCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{12}
}

func (m *GuildCirclePostCommentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCirclePostCommentResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCirclePostCommentResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCirclePostCommentResp) GetRepliedCommentId() uint32 {
	if m != nil {
		return m.RepliedCommentId
	}
	return 0
}

func (m *GuildCirclePostCommentResp) GetComment() *GuildCircleTopicComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

// 赞/取消赞 消息  同旧版
type GuildCircleLikeTopicReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	IsLike  bool        `protobuf:"varint,4,req,name=is_like,json=isLike" json:"is_like"`
}

func (m *GuildCircleLikeTopicReq) Reset()         { *m = GuildCircleLikeTopicReq{} }
func (m *GuildCircleLikeTopicReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleLikeTopicReq) ProtoMessage()    {}
func (*GuildCircleLikeTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{13}
}

func (m *GuildCircleLikeTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleLikeTopicReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleLikeTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleLikeTopicReq) GetIsLike() bool {
	if m != nil {
		return m.IsLike
	}
	return false
}

type GuildCircleLikeTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId  uint32       `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	IsLike   bool         `protobuf:"varint,4,req,name=is_like,json=isLike" json:"is_like"`
}

func (m *GuildCircleLikeTopicResp) Reset()         { *m = GuildCircleLikeTopicResp{} }
func (m *GuildCircleLikeTopicResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleLikeTopicResp) ProtoMessage()    {}
func (*GuildCircleLikeTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{14}
}

func (m *GuildCircleLikeTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleLikeTopicResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleLikeTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleLikeTopicResp) GetIsLike() bool {
	if m != nil {
		return m.IsLike
	}
	return false
}

// 举报主题 同旧版
type GuildCircleReportTopicReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *GuildCircleReportTopicReq) Reset()         { *m = GuildCircleReportTopicReq{} }
func (m *GuildCircleReportTopicReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleReportTopicReq) ProtoMessage()    {}
func (*GuildCircleReportTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{15}
}

func (m *GuildCircleReportTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleReportTopicReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *GuildCircleReportTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type GuildCircleReportTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *GuildCircleReportTopicResp) Reset()         { *m = GuildCircleReportTopicResp{} }
func (m *GuildCircleReportTopicResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleReportTopicResp) ProtoMessage()    {}
func (*GuildCircleReportTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{16}
}

func (m *GuildCircleReportTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleReportTopicResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *GuildCircleReportTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

// 删除我发的主题 同旧版
type GuildCircleDeleteTopicReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *GuildCircleDeleteTopicReq) Reset()         { *m = GuildCircleDeleteTopicReq{} }
func (m *GuildCircleDeleteTopicReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleDeleteTopicReq) ProtoMessage()    {}
func (*GuildCircleDeleteTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{17}
}

func (m *GuildCircleDeleteTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleDeleteTopicReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleDeleteTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type GuildCircleDeleteTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId  uint32       `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *GuildCircleDeleteTopicResp) Reset()         { *m = GuildCircleDeleteTopicResp{} }
func (m *GuildCircleDeleteTopicResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleDeleteTopicResp) ProtoMessage()    {}
func (*GuildCircleDeleteTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{18}
}

func (m *GuildCircleDeleteTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleDeleteTopicResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleDeleteTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

// 删除我发的评论 同旧版
type GuildCircleDeleteCommentReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId   uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId   uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32      `protobuf:"varint,4,req,name=comment_id,json=commentId" json:"comment_id"`
}

func (m *GuildCircleDeleteCommentReq) Reset()         { *m = GuildCircleDeleteCommentReq{} }
func (m *GuildCircleDeleteCommentReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleDeleteCommentReq) ProtoMessage()    {}
func (*GuildCircleDeleteCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{19}
}

func (m *GuildCircleDeleteCommentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleDeleteCommentReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleDeleteCommentReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleDeleteCommentReq) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

type GuildCircleDeleteCommentResp struct {
	BaseResp  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId   uint32       `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId   uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32       `protobuf:"varint,4,req,name=comment_id,json=commentId" json:"comment_id"`
}

func (m *GuildCircleDeleteCommentResp) Reset()         { *m = GuildCircleDeleteCommentResp{} }
func (m *GuildCircleDeleteCommentResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleDeleteCommentResp) ProtoMessage()    {}
func (*GuildCircleDeleteCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{20}
}

func (m *GuildCircleDeleteCommentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleDeleteCommentResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleDeleteCommentResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleDeleteCommentResp) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

// 单查游戏圈
type GuildCircleGetCircleDetailReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GuildCircleGetCircleDetailReq) Reset()         { *m = GuildCircleGetCircleDetailReq{} }
func (m *GuildCircleGetCircleDetailReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetCircleDetailReq) ProtoMessage()    {}
func (*GuildCircleGetCircleDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{21}
}

func (m *GuildCircleGetCircleDetailReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleGetCircleDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GuildCircleGetCircleDetailResp struct {
	BaseResp           *ga.BaseResp            `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId            uint32                  `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	GuildCircleDynamic *GuildCircleDynamicData `protobuf:"bytes,3,req,name=guild_circle_dynamic,json=guildCircleDynamic" json:"guild_circle_dynamic,omitempty"`
}

func (m *GuildCircleGetCircleDetailResp) Reset()         { *m = GuildCircleGetCircleDetailResp{} }
func (m *GuildCircleGetCircleDetailResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetCircleDetailResp) ProtoMessage()    {}
func (*GuildCircleGetCircleDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{22}
}

func (m *GuildCircleGetCircleDetailResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleGetCircleDetailResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetCircleDetailResp) GetGuildCircleDynamic() *GuildCircleDynamicData {
	if m != nil {
		return m.GuildCircleDynamic
	}
	return nil
}

// 查我发表过的主题
type GuildCircleGetUserTopicReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	StartTopicId uint32      `protobuf:"varint,2,req,name=start_topic_id,json=startTopicId" json:"start_topic_id"`
	TopicCount   uint32      `protobuf:"varint,3,req,name=topic_count,json=topicCount" json:"topic_count"`
	Uid          uint32      `protobuf:"varint,4,req,name=uid" json:"uid"`
}

func (m *GuildCircleGetUserTopicReq) Reset()         { *m = GuildCircleGetUserTopicReq{} }
func (m *GuildCircleGetUserTopicReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetUserTopicReq) ProtoMessage()    {}
func (*GuildCircleGetUserTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{23}
}

func (m *GuildCircleGetUserTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleGetUserTopicReq) GetStartTopicId() uint32 {
	if m != nil {
		return m.StartTopicId
	}
	return 0
}

func (m *GuildCircleGetUserTopicReq) GetTopicCount() uint32 {
	if m != nil {
		return m.TopicCount
	}
	return 0
}

func (m *GuildCircleGetUserTopicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GuildCircleGetUserTopicResp struct {
	BaseResp     *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	StartTopicId uint32              `protobuf:"varint,3,req,name=start_topic_id,json=startTopicId" json:"start_topic_id"`
	TopicCount   uint32              `protobuf:"varint,4,req,name=topic_count,json=topicCount" json:"topic_count"`
	TopicList    []*GuildCircleTopic `protobuf:"bytes,5,rep,name=topic_list,json=topicList" json:"topic_list,omitempty"`
	Uid          uint32              `protobuf:"varint,6,req,name=uid" json:"uid"`
}

func (m *GuildCircleGetUserTopicResp) Reset()         { *m = GuildCircleGetUserTopicResp{} }
func (m *GuildCircleGetUserTopicResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetUserTopicResp) ProtoMessage()    {}
func (*GuildCircleGetUserTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{24}
}

func (m *GuildCircleGetUserTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleGetUserTopicResp) GetStartTopicId() uint32 {
	if m != nil {
		return m.StartTopicId
	}
	return 0
}

func (m *GuildCircleGetUserTopicResp) GetTopicCount() uint32 {
	if m != nil {
		return m.TopicCount
	}
	return 0
}

func (m *GuildCircleGetUserTopicResp) GetTopicList() []*GuildCircleTopic {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *GuildCircleGetUserTopicResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 查点赞列表
type GuildCircleGetLikeUserListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Offset  uint32      `protobuf:"varint,4,req,name=offset" json:"offset"`
	Limit   uint32      `protobuf:"varint,5,req,name=limit" json:"limit"`
}

func (m *GuildCircleGetLikeUserListReq) Reset()         { *m = GuildCircleGetLikeUserListReq{} }
func (m *GuildCircleGetLikeUserListReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetLikeUserListReq) ProtoMessage()    {}
func (*GuildCircleGetLikeUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{25}
}

func (m *GuildCircleGetLikeUserListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleGetLikeUserListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetLikeUserListReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleGetLikeUserListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GuildCircleGetLikeUserListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GuildCircleGetLikeUserListResp struct {
	BaseResp     *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId      uint32       `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId      uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Offset       uint32       `protobuf:"varint,4,req,name=offset" json:"offset"`
	Limit        uint32       `protobuf:"varint,5,req,name=limit" json:"limit"`
	UserNickList []string     `protobuf:"bytes,6,rep,name=user_nick_list,json=userNickList" json:"user_nick_list,omitempty"`
}

func (m *GuildCircleGetLikeUserListResp) Reset()         { *m = GuildCircleGetLikeUserListResp{} }
func (m *GuildCircleGetLikeUserListResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetLikeUserListResp) ProtoMessage()    {}
func (*GuildCircleGetLikeUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{26}
}

func (m *GuildCircleGetLikeUserListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleGetLikeUserListResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetLikeUserListResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleGetLikeUserListResp) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GuildCircleGetLikeUserListResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GuildCircleGetLikeUserListResp) GetUserNickList() []string {
	if m != nil {
		return m.UserNickList
	}
	return nil
}

// /------ 以下不变
// 批量设置已读
type GuildCircleMarkReadedReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	SvrMsgId uint32      `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
}

func (m *GuildCircleMarkReadedReq) Reset()         { *m = GuildCircleMarkReadedReq{} }
func (m *GuildCircleMarkReadedReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleMarkReadedReq) ProtoMessage()    {}
func (*GuildCircleMarkReadedReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{27}
}

func (m *GuildCircleMarkReadedReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleMarkReadedReq) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

type GuildCircleMarkReadedResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SvrMsgId uint32       `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
}

func (m *GuildCircleMarkReadedResp) Reset()         { *m = GuildCircleMarkReadedResp{} }
func (m *GuildCircleMarkReadedResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleMarkReadedResp) ProtoMessage()    {}
func (*GuildCircleMarkReadedResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{28}
}

func (m *GuildCircleMarkReadedResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleMarkReadedResp) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

// 取消加精 -- v1.5
type GuildCircleCancelHighlightTopicReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *GuildCircleCancelHighlightTopicReq) Reset()         { *m = GuildCircleCancelHighlightTopicReq{} }
func (m *GuildCircleCancelHighlightTopicReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleCancelHighlightTopicReq) ProtoMessage()    {}
func (*GuildCircleCancelHighlightTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{29}
}

func (m *GuildCircleCancelHighlightTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleCancelHighlightTopicReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleCancelHighlightTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type GuildCircleCancelHighlightTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId  uint32       `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *GuildCircleCancelHighlightTopicResp) Reset()         { *m = GuildCircleCancelHighlightTopicResp{} }
func (m *GuildCircleCancelHighlightTopicResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleCancelHighlightTopicResp) ProtoMessage()    {}
func (*GuildCircleCancelHighlightTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{30}
}

func (m *GuildCircleCancelHighlightTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleCancelHighlightTopicResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleCancelHighlightTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

// 评论回复的目标
type GuildCommentReplayTargetBase struct {
	CommentId      uint32         `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	Creator        *ga.CircleUser `protobuf:"bytes,2,req,name=creator" json:"creator,omitempty"`
	CreateTime     uint32         `protobuf:"varint,3,req,name=create_time,json=createTime" json:"create_time"`
	Status         uint32         `protobuf:"varint,4,req,name=status" json:"status"`
	CreateTimeDesc string         `protobuf:"bytes,5,req,name=create_time_desc,json=createTimeDesc" json:"create_time_desc"`
}

func (m *GuildCommentReplayTargetBase) Reset()         { *m = GuildCommentReplayTargetBase{} }
func (m *GuildCommentReplayTargetBase) String() string { return proto.CompactTextString(m) }
func (*GuildCommentReplayTargetBase) ProtoMessage()    {}
func (*GuildCommentReplayTargetBase) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{31}
}

func (m *GuildCommentReplayTargetBase) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *GuildCommentReplayTargetBase) GetCreator() *ga.CircleUser {
	if m != nil {
		return m.Creator
	}
	return nil
}

func (m *GuildCommentReplayTargetBase) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GuildCommentReplayTargetBase) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GuildCommentReplayTargetBase) GetCreateTimeDesc() string {
	if m != nil {
		return m.CreateTimeDesc
	}
	return ""
}

// 一条评论的基本数据
type GuildCommentBase struct {
	CommentId      uint32                        `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	Content        string                        `protobuf:"bytes,2,req,name=content" json:"content"`
	Creator        *ga.CircleUser                `protobuf:"bytes,3,req,name=creator" json:"creator,omitempty"`
	CreateTime     uint32                        `protobuf:"varint,4,req,name=create_time,json=createTime" json:"create_time"`
	Status         uint32                        `protobuf:"varint,5,req,name=status" json:"status"`
	Type           uint32                        `protobuf:"varint,6,req,name=type" json:"type"`
	CreateTimeDesc string                        `protobuf:"bytes,7,req,name=create_time_desc,json=createTimeDesc" json:"create_time_desc"`
	ReplyTarget    *GuildCommentReplayTargetBase `protobuf:"bytes,8,opt,name=reply_target,json=replyTarget" json:"reply_target,omitempty"`
}

func (m *GuildCommentBase) Reset()                    { *m = GuildCommentBase{} }
func (m *GuildCommentBase) String() string            { return proto.CompactTextString(m) }
func (*GuildCommentBase) ProtoMessage()               {}
func (*GuildCommentBase) Descriptor() ([]byte, []int) { return fileDescriptorGuildcircle_, []int{32} }

func (m *GuildCommentBase) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *GuildCommentBase) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GuildCommentBase) GetCreator() *ga.CircleUser {
	if m != nil {
		return m.Creator
	}
	return nil
}

func (m *GuildCommentBase) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GuildCommentBase) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GuildCommentBase) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GuildCommentBase) GetCreateTimeDesc() string {
	if m != nil {
		return m.CreateTimeDesc
	}
	return ""
}

func (m *GuildCommentBase) GetReplyTarget() *GuildCommentReplayTargetBase {
	if m != nil {
		return m.ReplyTarget
	}
	return nil
}

// 普通评论
type GuildCircleTopicNomalComment struct {
	CommentBase      *GuildCommentBase          `protobuf:"bytes,1,req,name=comment_base,json=commentBase" json:"comment_base,omitempty"`
	Floor            int32                      `protobuf:"varint,2,req,name=floor" json:"floor"`
	ImageList        []*GuildCircleCommentImage `protobuf:"bytes,3,rep,name=image_list,json=imageList" json:"image_list,omitempty"`
	ReplyCommentList []*GuildCommentBase        `protobuf:"bytes,4,rep,name=reply_comment_list,json=replyCommentList" json:"reply_comment_list,omitempty"`
	ReplyTotalCnt    uint32                     `protobuf:"varint,5,opt,name=reply_total_cnt,json=replyTotalCnt" json:"reply_total_cnt"`
}

func (m *GuildCircleTopicNomalComment) Reset()         { *m = GuildCircleTopicNomalComment{} }
func (m *GuildCircleTopicNomalComment) String() string { return proto.CompactTextString(m) }
func (*GuildCircleTopicNomalComment) ProtoMessage()    {}
func (*GuildCircleTopicNomalComment) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{33}
}

func (m *GuildCircleTopicNomalComment) GetCommentBase() *GuildCommentBase {
	if m != nil {
		return m.CommentBase
	}
	return nil
}

func (m *GuildCircleTopicNomalComment) GetFloor() int32 {
	if m != nil {
		return m.Floor
	}
	return 0
}

func (m *GuildCircleTopicNomalComment) GetImageList() []*GuildCircleCommentImage {
	if m != nil {
		return m.ImageList
	}
	return nil
}

func (m *GuildCircleTopicNomalComment) GetReplyCommentList() []*GuildCommentBase {
	if m != nil {
		return m.ReplyCommentList
	}
	return nil
}

func (m *GuildCircleTopicNomalComment) GetReplyTotalCnt() uint32 {
	if m != nil {
		return m.ReplyTotalCnt
	}
	return 0
}

// 获取普通评论列表
type GuildCircleGetNomalCommentListReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId        uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId        uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	StartCommentId uint32      `protobuf:"varint,4,req,name=start_comment_id,json=startCommentId" json:"start_comment_id"`
	Count          uint32      `protobuf:"varint,5,req,name=count" json:"count"`
	IncludeStartId bool        `protobuf:"varint,6,opt,name=include_start_id,json=includeStartId" json:"include_start_id"`
	IsDesc         bool        `protobuf:"varint,7,opt,name=is_desc,json=isDesc" json:"is_desc"`
}

func (m *GuildCircleGetNomalCommentListReq) Reset()         { *m = GuildCircleGetNomalCommentListReq{} }
func (m *GuildCircleGetNomalCommentListReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetNomalCommentListReq) ProtoMessage()    {}
func (*GuildCircleGetNomalCommentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{34}
}

func (m *GuildCircleGetNomalCommentListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleGetNomalCommentListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetNomalCommentListReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleGetNomalCommentListReq) GetStartCommentId() uint32 {
	if m != nil {
		return m.StartCommentId
	}
	return 0
}

func (m *GuildCircleGetNomalCommentListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GuildCircleGetNomalCommentListReq) GetIncludeStartId() bool {
	if m != nil {
		return m.IncludeStartId
	}
	return false
}

func (m *GuildCircleGetNomalCommentListReq) GetIsDesc() bool {
	if m != nil {
		return m.IsDesc
	}
	return false
}

type GuildCircleGetNomalCommentListResp struct {
	BaseResp       *ga.BaseResp                    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId        uint32                          `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId        uint32                          `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	StartCommentId uint32                          `protobuf:"varint,4,req,name=start_comment_id,json=startCommentId" json:"start_comment_id"`
	CommentList    []*GuildCircleTopicNomalComment `protobuf:"bytes,5,rep,name=comment_list,json=commentList" json:"comment_list,omitempty"`
	NomalLeftCnt   uint32                          `protobuf:"varint,6,opt,name=nomal_left_cnt,json=nomalLeftCnt" json:"nomal_left_cnt"`
}

func (m *GuildCircleGetNomalCommentListResp) Reset()         { *m = GuildCircleGetNomalCommentListResp{} }
func (m *GuildCircleGetNomalCommentListResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetNomalCommentListResp) ProtoMessage()    {}
func (*GuildCircleGetNomalCommentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{35}
}

func (m *GuildCircleGetNomalCommentListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleGetNomalCommentListResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetNomalCommentListResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleGetNomalCommentListResp) GetStartCommentId() uint32 {
	if m != nil {
		return m.StartCommentId
	}
	return 0
}

func (m *GuildCircleGetNomalCommentListResp) GetCommentList() []*GuildCircleTopicNomalComment {
	if m != nil {
		return m.CommentList
	}
	return nil
}

func (m *GuildCircleGetNomalCommentListResp) GetNomalLeftCnt() uint32 {
	if m != nil {
		return m.NomalLeftCnt
	}
	return 0
}

// 获取指定评论的回复列表
type GuildCircleGetCommentReplyListReq struct {
	BaseReq             *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId             uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId             uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	ParentCommentId     uint32      `protobuf:"varint,4,req,name=parent_comment_id,json=parentCommentId" json:"parent_comment_id"`
	StartReplyCommentId uint32      `protobuf:"varint,5,req,name=start_reply_comment_id,json=startReplyCommentId" json:"start_reply_comment_id"`
	Count               uint32      `protobuf:"varint,6,req,name=count" json:"count"`
}

func (m *GuildCircleGetCommentReplyListReq) Reset()         { *m = GuildCircleGetCommentReplyListReq{} }
func (m *GuildCircleGetCommentReplyListReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetCommentReplyListReq) ProtoMessage()    {}
func (*GuildCircleGetCommentReplyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{36}
}

func (m *GuildCircleGetCommentReplyListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleGetCommentReplyListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetCommentReplyListReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleGetCommentReplyListReq) GetParentCommentId() uint32 {
	if m != nil {
		return m.ParentCommentId
	}
	return 0
}

func (m *GuildCircleGetCommentReplyListReq) GetStartReplyCommentId() uint32 {
	if m != nil {
		return m.StartReplyCommentId
	}
	return 0
}

func (m *GuildCircleGetCommentReplyListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GuildCircleGetCommentReplyListResp struct {
	BaseResp            *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId             uint32              `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId             uint32              `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	ParentCommentId     uint32              `protobuf:"varint,4,req,name=parent_comment_id,json=parentCommentId" json:"parent_comment_id"`
	StartReplyCommentId uint32              `protobuf:"varint,5,req,name=start_reply_comment_id,json=startReplyCommentId" json:"start_reply_comment_id"`
	ReplyList           []*GuildCommentBase `protobuf:"bytes,6,rep,name=reply_list,json=replyList" json:"reply_list,omitempty"`
	ReplyTotalCnt       uint32              `protobuf:"varint,7,opt,name=reply_total_cnt,json=replyTotalCnt" json:"reply_total_cnt"`
	ReplyLeftCnt        uint32              `protobuf:"varint,8,opt,name=reply_left_cnt,json=replyLeftCnt" json:"reply_left_cnt"`
}

func (m *GuildCircleGetCommentReplyListResp) Reset()         { *m = GuildCircleGetCommentReplyListResp{} }
func (m *GuildCircleGetCommentReplyListResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleGetCommentReplyListResp) ProtoMessage()    {}
func (*GuildCircleGetCommentReplyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{37}
}

func (m *GuildCircleGetCommentReplyListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleGetCommentReplyListResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleGetCommentReplyListResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleGetCommentReplyListResp) GetParentCommentId() uint32 {
	if m != nil {
		return m.ParentCommentId
	}
	return 0
}

func (m *GuildCircleGetCommentReplyListResp) GetStartReplyCommentId() uint32 {
	if m != nil {
		return m.StartReplyCommentId
	}
	return 0
}

func (m *GuildCircleGetCommentReplyListResp) GetReplyList() []*GuildCommentBase {
	if m != nil {
		return m.ReplyList
	}
	return nil
}

func (m *GuildCircleGetCommentReplyListResp) GetReplyTotalCnt() uint32 {
	if m != nil {
		return m.ReplyTotalCnt
	}
	return 0
}

func (m *GuildCircleGetCommentReplyListResp) GetReplyLeftCnt() uint32 {
	if m != nil {
		return m.ReplyLeftCnt
	}
	return 0
}

type GuildCircleAddTopicTagReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Tag     uint32      `protobuf:"varint,4,req,name=tag" json:"tag"`
	Add     bool        `protobuf:"varint,5,req,name=add" json:"add"`
}

func (m *GuildCircleAddTopicTagReq) Reset()         { *m = GuildCircleAddTopicTagReq{} }
func (m *GuildCircleAddTopicTagReq) String() string { return proto.CompactTextString(m) }
func (*GuildCircleAddTopicTagReq) ProtoMessage()    {}
func (*GuildCircleAddTopicTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{38}
}

func (m *GuildCircleAddTopicTagReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GuildCircleAddTopicTagReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleAddTopicTagReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *GuildCircleAddTopicTagReq) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *GuildCircleAddTopicTagReq) GetAdd() bool {
	if m != nil {
		return m.Add
	}
	return false
}

type GuildCircleAddTopicTagResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GuildId  uint32       `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *GuildCircleAddTopicTagResp) Reset()         { *m = GuildCircleAddTopicTagResp{} }
func (m *GuildCircleAddTopicTagResp) String() string { return proto.CompactTextString(m) }
func (*GuildCircleAddTopicTagResp) ProtoMessage()    {}
func (*GuildCircleAddTopicTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildcircle_, []int{39}
}

func (m *GuildCircleAddTopicTagResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GuildCircleAddTopicTagResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCircleAddTopicTagResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func init() {
	proto.RegisterType((*GuildCircleTopicImage)(nil), "ga.GuildCircleTopicImage")
	proto.RegisterType((*GuildCircleTopic)(nil), "ga.GuildCircleTopic")
	proto.RegisterType((*GuildCircleCommentImage)(nil), "ga.GuildCircleCommentImage")
	proto.RegisterType((*GuildCircleTopicComment)(nil), "ga.GuildCircleTopicComment")
	proto.RegisterType((*GuildCircleDynamicData)(nil), "ga.GuildCircleDynamicData")
	proto.RegisterType((*GuildCirclePostTopicReq)(nil), "ga.GuildCirclePostTopicReq")
	proto.RegisterType((*GuildCirclePostTopicResp)(nil), "ga.GuildCirclePostTopicResp")
	proto.RegisterType((*GuildCircleGetTopicListReq)(nil), "ga.GuildCircleGetTopicListReq")
	proto.RegisterType((*GuildCircleGetTopicListResp)(nil), "ga.GuildCircleGetTopicListResp")
	proto.RegisterType((*GuildCircleGetTopicReq)(nil), "ga.GuildCircleGetTopicReq")
	proto.RegisterType((*GuildCircleGetTopicResp)(nil), "ga.GuildCircleGetTopicResp")
	proto.RegisterType((*GuildCirclePostCommentReq)(nil), "ga.GuildCirclePostCommentReq")
	proto.RegisterType((*GuildCirclePostCommentResp)(nil), "ga.GuildCirclePostCommentResp")
	proto.RegisterType((*GuildCircleLikeTopicReq)(nil), "ga.GuildCircleLikeTopicReq")
	proto.RegisterType((*GuildCircleLikeTopicResp)(nil), "ga.GuildCircleLikeTopicResp")
	proto.RegisterType((*GuildCircleReportTopicReq)(nil), "ga.GuildCircleReportTopicReq")
	proto.RegisterType((*GuildCircleReportTopicResp)(nil), "ga.GuildCircleReportTopicResp")
	proto.RegisterType((*GuildCircleDeleteTopicReq)(nil), "ga.GuildCircleDeleteTopicReq")
	proto.RegisterType((*GuildCircleDeleteTopicResp)(nil), "ga.GuildCircleDeleteTopicResp")
	proto.RegisterType((*GuildCircleDeleteCommentReq)(nil), "ga.GuildCircleDeleteCommentReq")
	proto.RegisterType((*GuildCircleDeleteCommentResp)(nil), "ga.GuildCircleDeleteCommentResp")
	proto.RegisterType((*GuildCircleGetCircleDetailReq)(nil), "ga.GuildCircleGetCircleDetailReq")
	proto.RegisterType((*GuildCircleGetCircleDetailResp)(nil), "ga.GuildCircleGetCircleDetailResp")
	proto.RegisterType((*GuildCircleGetUserTopicReq)(nil), "ga.GuildCircleGetUserTopicReq")
	proto.RegisterType((*GuildCircleGetUserTopicResp)(nil), "ga.GuildCircleGetUserTopicResp")
	proto.RegisterType((*GuildCircleGetLikeUserListReq)(nil), "ga.GuildCircleGetLikeUserListReq")
	proto.RegisterType((*GuildCircleGetLikeUserListResp)(nil), "ga.GuildCircleGetLikeUserListResp")
	proto.RegisterType((*GuildCircleMarkReadedReq)(nil), "ga.GuildCircleMarkReadedReq")
	proto.RegisterType((*GuildCircleMarkReadedResp)(nil), "ga.GuildCircleMarkReadedResp")
	proto.RegisterType((*GuildCircleCancelHighlightTopicReq)(nil), "ga.GuildCircleCancelHighlightTopicReq")
	proto.RegisterType((*GuildCircleCancelHighlightTopicResp)(nil), "ga.GuildCircleCancelHighlightTopicResp")
	proto.RegisterType((*GuildCommentReplayTargetBase)(nil), "ga.GuildCommentReplayTargetBase")
	proto.RegisterType((*GuildCommentBase)(nil), "ga.GuildCommentBase")
	proto.RegisterType((*GuildCircleTopicNomalComment)(nil), "ga.GuildCircleTopicNomalComment")
	proto.RegisterType((*GuildCircleGetNomalCommentListReq)(nil), "ga.GuildCircleGetNomalCommentListReq")
	proto.RegisterType((*GuildCircleGetNomalCommentListResp)(nil), "ga.GuildCircleGetNomalCommentListResp")
	proto.RegisterType((*GuildCircleGetCommentReplyListReq)(nil), "ga.GuildCircleGetCommentReplyListReq")
	proto.RegisterType((*GuildCircleGetCommentReplyListResp)(nil), "ga.GuildCircleGetCommentReplyListResp")
	proto.RegisterType((*GuildCircleAddTopicTagReq)(nil), "ga.GuildCircleAddTopicTagReq")
	proto.RegisterType((*GuildCircleAddTopicTagResp)(nil), "ga.GuildCircleAddTopicTagResp")
	proto.RegisterEnum("ga.GuildCircleTopicCommentStatus", GuildCircleTopicCommentStatus_name, GuildCircleTopicCommentStatus_value)
	proto.RegisterEnum("ga.GuildCircleTopicCommentType", GuildCircleTopicCommentType_name, GuildCircleTopicCommentType_value)
	proto.RegisterEnum("ga.GuildCircleTopic_TOPIC_STATE", GuildCircleTopic_TOPIC_STATE_name, GuildCircleTopic_TOPIC_STATE_value)
	proto.RegisterEnum("ga.GuildCircleTopicComment_TopicCommentStatus", GuildCircleTopicComment_TopicCommentStatus_name, GuildCircleTopicComment_TopicCommentStatus_value)
}
func (m *GuildCircleTopicImage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleTopicImage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.ThumbUrl)))
	i += copy(dAtA[i:], m.ThumbUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.ImageUrl)))
	i += copy(dAtA[i:], m.ImageUrl)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.ImageWidth))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.ImageHeight))
	return i, nil
}

func (m *GuildCircleTopic) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleTopic) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CreateTime))
	if len(m.ImageList) > 0 {
		for _, msg := range m.ImageList {
			dAtA[i] = 0x3a
			i++
			i = encodeVarintGuildcircle_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.LikeCount))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CommentCount))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.IsLiked))
	dAtA[i] = 0x58
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicState))
	dAtA[i] = 0x60
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.LastCommentTime))
	if m.Creator != nil {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Creator.Size()))
		n1, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x72
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.CreateTimeDesc)))
	i += copy(dAtA[i:], m.CreateTimeDesc)
	dAtA[i] = 0x7a
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.LastCommentTimeDesc)))
	i += copy(dAtA[i:], m.LastCommentTimeDesc)
	return i, nil
}

func (m *GuildCircleCommentImage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleCommentImage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.ThumbUrl)))
	i += copy(dAtA[i:], m.ThumbUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.ImageUrl)))
	i += copy(dAtA[i:], m.ImageUrl)
	return i, nil
}

func (m *GuildCircleTopicComment) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleTopicComment) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CommentId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if m.Creator == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	} else {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Creator.Size()))
		n2, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Status))
	if m.RepliedComment != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.RepliedComment.Size()))
		n3, err := m.RepliedComment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.CreateTimeDesc)))
	i += copy(dAtA[i:], m.CreateTimeDesc)
	if len(m.ImageList) > 0 {
		for _, msg := range m.ImageList {
			dAtA[i] = 0x52
			i++
			i = encodeVarintGuildcircle_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x58
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Floor))
	return i, nil
}

func (m *GuildCircleDynamicData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleDynamicData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TodayTopicCount))
	return i, nil
}

func (m *GuildCirclePostTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCirclePostTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n4, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.ClientId)))
	i += copy(dAtA[i:], m.ClientId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			dAtA[i] = 0x32
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x38
	i++
	if m.Highlight {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GuildCirclePostTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCirclePostTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n5, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.ClientId)))
	i += copy(dAtA[i:], m.ClientId)
	if m.Topic == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Topic.Size()))
		n6, err := m.Topic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *GuildCircleGetTopicListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetTopicListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.PageCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.PagePosition))
	dAtA[i] = 0x28
	i++
	if m.Highlight {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	if m.RequireCount {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GuildCircleGetTopicListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetTopicListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.PageCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.PagePosition))
	if len(m.TopicList) > 0 {
		for _, msg := range m.TopicList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintGuildcircle_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.NewestTopicId))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TotalTopicCount))
	return i, nil
}

func (m *GuildCircleGetTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.LikeUserCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopCommentCount))
	return i, nil
}

func (m *GuildCircleGetTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	if m.Topic == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic")
	} else {
		dAtA[i] = 0x22
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Topic.Size()))
		n11, err := m.Topic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if len(m.LikeUserList) > 0 {
		for _, s := range m.LikeUserList {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.TopCommentList) > 0 {
		for _, msg := range m.TopCommentList {
			dAtA[i] = 0x32
			i++
			i = encodeVarintGuildcircle_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GuildCirclePostCommentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCirclePostCommentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n12, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.RepliedCommentId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			dAtA[i] = 0x32
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GuildCirclePostCommentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCirclePostCommentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n13, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.RepliedCommentId))
	if m.Comment == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment")
	} else {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Comment.Size()))
		n14, err := m.Comment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	return i, nil
}

func (m *GuildCircleLikeTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleLikeTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n15, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	if m.IsLike {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GuildCircleLikeTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleLikeTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n16, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	if m.IsLike {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GuildCircleReportTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleReportTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n17, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *GuildCircleReportTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleReportTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n18, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *GuildCircleDeleteTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleDeleteTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *GuildCircleDeleteTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleDeleteTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n20, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *GuildCircleDeleteCommentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleDeleteCommentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n21, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CommentId))
	return i, nil
}

func (m *GuildCircleDeleteCommentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleDeleteCommentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n22, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CommentId))
	return i, nil
}

func (m *GuildCircleGetCircleDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetCircleDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n23, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GuildCircleGetCircleDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetCircleDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n24, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	if m.GuildCircleDynamic == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_circle_dynamic")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildCircleDynamic.Size()))
		n25, err := m.GuildCircleDynamic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	return i, nil
}

func (m *GuildCircleGetUserTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetUserTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n26, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.StartTopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GuildCircleGetUserTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetUserTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n27, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.StartTopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicCount))
	if len(m.TopicList) > 0 {
		for _, msg := range m.TopicList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintGuildcircle_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GuildCircleGetLikeUserListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetLikeUserListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n28, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GuildCircleGetLikeUserListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetLikeUserListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n29, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Limit))
	if len(m.UserNickList) > 0 {
		for _, s := range m.UserNickList {
			dAtA[i] = 0x32
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GuildCircleMarkReadedReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleMarkReadedReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n30, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.SvrMsgId))
	return i, nil
}

func (m *GuildCircleMarkReadedResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleMarkReadedResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n31, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.SvrMsgId))
	return i, nil
}

func (m *GuildCircleCancelHighlightTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleCancelHighlightTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n32, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n32
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *GuildCircleCancelHighlightTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleCancelHighlightTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n33, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n33
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *GuildCommentReplayTargetBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCommentReplayTargetBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CommentId))
	if m.Creator == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Creator.Size()))
		n34, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n34
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.CreateTimeDesc)))
	i += copy(dAtA[i:], m.CreateTimeDesc)
	return i, nil
}

func (m *GuildCommentBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCommentBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CommentId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if m.Creator == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Creator.Size()))
		n35, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n35
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(len(m.CreateTimeDesc)))
	i += copy(dAtA[i:], m.CreateTimeDesc)
	if m.ReplyTarget != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.ReplyTarget.Size()))
		n36, err := m.ReplyTarget.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n36
	}
	return i, nil
}

func (m *GuildCircleTopicNomalComment) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleTopicNomalComment) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CommentBase == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_base")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.CommentBase.Size()))
		n37, err := m.CommentBase.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n37
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Floor))
	if len(m.ImageList) > 0 {
		for _, msg := range m.ImageList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGuildcircle_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ReplyCommentList) > 0 {
		for _, msg := range m.ReplyCommentList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintGuildcircle_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.ReplyTotalCnt))
	return i, nil
}

func (m *GuildCircleGetNomalCommentListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetNomalCommentListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n38, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n38
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.StartCommentId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x30
	i++
	if m.IncludeStartId {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	if m.IsDesc {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GuildCircleGetNomalCommentListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetNomalCommentListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n39, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n39
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.StartCommentId))
	if len(m.CommentList) > 0 {
		for _, msg := range m.CommentList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintGuildcircle_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.NomalLeftCnt))
	return i, nil
}

func (m *GuildCircleGetCommentReplyListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetCommentReplyListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n40, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n40
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.ParentCommentId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.StartReplyCommentId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GuildCircleGetCommentReplyListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleGetCommentReplyListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n41, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n41
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.ParentCommentId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.StartReplyCommentId))
	if len(m.ReplyList) > 0 {
		for _, msg := range m.ReplyList {
			dAtA[i] = 0x32
			i++
			i = encodeVarintGuildcircle_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.ReplyTotalCnt))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.ReplyLeftCnt))
	return i, nil
}

func (m *GuildCircleAddTopicTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleAddTopicTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseReq.Size()))
		n42, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n42
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.Tag))
	dAtA[i] = 0x28
	i++
	if m.Add {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GuildCircleAddTopicTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildCircleAddTopicTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildcircle_(dAtA, i, uint64(m.BaseResp.Size()))
		n43, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n43
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildcircle_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func encodeFixed64Guildcircle_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Guildcircle_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGuildcircle_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GuildCircleTopicImage) Size() (n int) {
	var l int
	_ = l
	l = len(m.ThumbUrl)
	n += 1 + l + sovGuildcircle_(uint64(l))
	l = len(m.ImageUrl)
	n += 1 + l + sovGuildcircle_(uint64(l))
	n += 1 + sovGuildcircle_(uint64(m.ImageWidth))
	n += 1 + sovGuildcircle_(uint64(m.ImageHeight))
	return n
}

func (m *GuildCircleTopic) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	l = len(m.Title)
	n += 1 + l + sovGuildcircle_(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovGuildcircle_(uint64(l))
	n += 1 + sovGuildcircle_(uint64(m.CreateTime))
	if len(m.ImageList) > 0 {
		for _, e := range m.ImageList {
			l = e.Size()
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	n += 1 + sovGuildcircle_(uint64(m.LikeCount))
	n += 1 + sovGuildcircle_(uint64(m.CommentCount))
	n += 1 + sovGuildcircle_(uint64(m.IsLiked))
	n += 1 + sovGuildcircle_(uint64(m.TopicState))
	n += 1 + sovGuildcircle_(uint64(m.LastCommentTime))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	l = len(m.CreateTimeDesc)
	n += 1 + l + sovGuildcircle_(uint64(l))
	l = len(m.LastCommentTimeDesc)
	n += 1 + l + sovGuildcircle_(uint64(l))
	return n
}

func (m *GuildCircleCommentImage) Size() (n int) {
	var l int
	_ = l
	l = len(m.ThumbUrl)
	n += 1 + l + sovGuildcircle_(uint64(l))
	l = len(m.ImageUrl)
	n += 1 + l + sovGuildcircle_(uint64(l))
	return n
}

func (m *GuildCircleTopicComment) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle_(uint64(m.CommentId))
	n += 1 + sovGuildcircle_(uint64(m.CircleId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	l = len(m.Content)
	n += 1 + l + sovGuildcircle_(uint64(l))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.CreateTime))
	n += 1 + sovGuildcircle_(uint64(m.Status))
	if m.RepliedComment != nil {
		l = m.RepliedComment.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	l = len(m.CreateTimeDesc)
	n += 1 + l + sovGuildcircle_(uint64(l))
	if len(m.ImageList) > 0 {
		for _, e := range m.ImageList {
			l = e.Size()
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	n += 1 + sovGuildcircle_(uint64(m.Floor))
	return n
}

func (m *GuildCircleDynamicData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicCount))
	n += 1 + sovGuildcircle_(uint64(m.TodayTopicCount))
	return n
}

func (m *GuildCirclePostTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	l = len(m.ClientId)
	n += 1 + l + sovGuildcircle_(uint64(l))
	l = len(m.Title)
	n += 1 + l + sovGuildcircle_(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovGuildcircle_(uint64(l))
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			l = len(s)
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *GuildCirclePostTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	l = len(m.ClientId)
	n += 1 + l + sovGuildcircle_(uint64(l))
	if m.Topic != nil {
		l = m.Topic.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	return n
}

func (m *GuildCircleGetTopicListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.PageCount))
	n += 1 + sovGuildcircle_(uint64(m.PagePosition))
	n += 2
	n += 2
	return n
}

func (m *GuildCircleGetTopicListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.PageCount))
	n += 1 + sovGuildcircle_(uint64(m.PagePosition))
	if len(m.TopicList) > 0 {
		for _, e := range m.TopicList {
			l = e.Size()
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	n += 1 + sovGuildcircle_(uint64(m.NewestTopicId))
	n += 1 + sovGuildcircle_(uint64(m.TotalTopicCount))
	return n
}

func (m *GuildCircleGetTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.LikeUserCount))
	n += 1 + sovGuildcircle_(uint64(m.TopCommentCount))
	return n
}

func (m *GuildCircleGetTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	if m.Topic != nil {
		l = m.Topic.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	if len(m.LikeUserList) > 0 {
		for _, s := range m.LikeUserList {
			l = len(s)
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	if len(m.TopCommentList) > 0 {
		for _, e := range m.TopCommentList {
			l = e.Size()
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	return n
}

func (m *GuildCirclePostCommentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.RepliedCommentId))
	l = len(m.Content)
	n += 1 + l + sovGuildcircle_(uint64(l))
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			l = len(s)
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	return n
}

func (m *GuildCirclePostCommentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.RepliedCommentId))
	if m.Comment != nil {
		l = m.Comment.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	return n
}

func (m *GuildCircleLikeTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 2
	return n
}

func (m *GuildCircleLikeTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 2
	return n
}

func (m *GuildCircleReportTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.CircleId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	return n
}

func (m *GuildCircleReportTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.CircleId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	return n
}

func (m *GuildCircleDeleteTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	return n
}

func (m *GuildCircleDeleteTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	return n
}

func (m *GuildCircleDeleteCommentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.CommentId))
	return n
}

func (m *GuildCircleDeleteCommentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.CommentId))
	return n
}

func (m *GuildCircleGetCircleDetailReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	return n
}

func (m *GuildCircleGetCircleDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	if m.GuildCircleDynamic != nil {
		l = m.GuildCircleDynamic.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	return n
}

func (m *GuildCircleGetUserTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.StartTopicId))
	n += 1 + sovGuildcircle_(uint64(m.TopicCount))
	n += 1 + sovGuildcircle_(uint64(m.Uid))
	return n
}

func (m *GuildCircleGetUserTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.StartTopicId))
	n += 1 + sovGuildcircle_(uint64(m.TopicCount))
	if len(m.TopicList) > 0 {
		for _, e := range m.TopicList {
			l = e.Size()
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	n += 1 + sovGuildcircle_(uint64(m.Uid))
	return n
}

func (m *GuildCircleGetLikeUserListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.Offset))
	n += 1 + sovGuildcircle_(uint64(m.Limit))
	return n
}

func (m *GuildCircleGetLikeUserListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.Offset))
	n += 1 + sovGuildcircle_(uint64(m.Limit))
	if len(m.UserNickList) > 0 {
		for _, s := range m.UserNickList {
			l = len(s)
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	return n
}

func (m *GuildCircleMarkReadedReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.SvrMsgId))
	return n
}

func (m *GuildCircleMarkReadedResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.SvrMsgId))
	return n
}

func (m *GuildCircleCancelHighlightTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	return n
}

func (m *GuildCircleCancelHighlightTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	return n
}

func (m *GuildCommentReplayTargetBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle_(uint64(m.CommentId))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.CreateTime))
	n += 1 + sovGuildcircle_(uint64(m.Status))
	l = len(m.CreateTimeDesc)
	n += 1 + l + sovGuildcircle_(uint64(l))
	return n
}

func (m *GuildCommentBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildcircle_(uint64(m.CommentId))
	l = len(m.Content)
	n += 1 + l + sovGuildcircle_(uint64(l))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.CreateTime))
	n += 1 + sovGuildcircle_(uint64(m.Status))
	n += 1 + sovGuildcircle_(uint64(m.Type))
	l = len(m.CreateTimeDesc)
	n += 1 + l + sovGuildcircle_(uint64(l))
	if m.ReplyTarget != nil {
		l = m.ReplyTarget.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	return n
}

func (m *GuildCircleTopicNomalComment) Size() (n int) {
	var l int
	_ = l
	if m.CommentBase != nil {
		l = m.CommentBase.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.Floor))
	if len(m.ImageList) > 0 {
		for _, e := range m.ImageList {
			l = e.Size()
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	if len(m.ReplyCommentList) > 0 {
		for _, e := range m.ReplyCommentList {
			l = e.Size()
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	n += 1 + sovGuildcircle_(uint64(m.ReplyTotalCnt))
	return n
}

func (m *GuildCircleGetNomalCommentListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.StartCommentId))
	n += 1 + sovGuildcircle_(uint64(m.Count))
	n += 2
	n += 2
	return n
}

func (m *GuildCircleGetNomalCommentListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.StartCommentId))
	if len(m.CommentList) > 0 {
		for _, e := range m.CommentList {
			l = e.Size()
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	n += 1 + sovGuildcircle_(uint64(m.NomalLeftCnt))
	return n
}

func (m *GuildCircleGetCommentReplyListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.ParentCommentId))
	n += 1 + sovGuildcircle_(uint64(m.StartReplyCommentId))
	n += 1 + sovGuildcircle_(uint64(m.Count))
	return n
}

func (m *GuildCircleGetCommentReplyListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.ParentCommentId))
	n += 1 + sovGuildcircle_(uint64(m.StartReplyCommentId))
	if len(m.ReplyList) > 0 {
		for _, e := range m.ReplyList {
			l = e.Size()
			n += 1 + l + sovGuildcircle_(uint64(l))
		}
	}
	n += 1 + sovGuildcircle_(uint64(m.ReplyTotalCnt))
	n += 1 + sovGuildcircle_(uint64(m.ReplyLeftCnt))
	return n
}

func (m *GuildCircleAddTopicTagReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	n += 1 + sovGuildcircle_(uint64(m.Tag))
	n += 2
	return n
}

func (m *GuildCircleAddTopicTagResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGuildcircle_(uint64(l))
	}
	n += 1 + sovGuildcircle_(uint64(m.GuildId))
	n += 1 + sovGuildcircle_(uint64(m.TopicId))
	return n
}

func sovGuildcircle_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGuildcircle_(x uint64) (n int) {
	return sovGuildcircle_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GuildCircleTopicImage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleTopicImage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleTopicImage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageWidth", wireType)
			}
			m.ImageWidth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ImageWidth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageHeight", wireType)
			}
			m.ImageHeight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ImageHeight |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("thumb_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("image_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleTopic) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleTopic: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleTopic: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageList = append(m.ImageList, &GuildCircleTopicImage{})
			if err := m.ImageList[len(m.ImageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeCount", wireType)
			}
			m.LikeCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentCount", wireType)
			}
			m.CommentCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLiked", wireType)
			}
			m.IsLiked = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsLiked |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicState", wireType)
			}
			m.TopicState = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicState |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastCommentTime", wireType)
			}
			m.LastCommentTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastCommentTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &ga.CircleUser{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000400)
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastCommentTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LastCommentTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000800)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("like_count")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_count")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_liked")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_state")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_comment_time")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time_desc")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_comment_time_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleCommentImage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleCommentImage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleCommentImage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("thumb_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("image_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleTopicComment) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleTopicComment: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleTopicComment: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &ga.CircleUser{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedComment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RepliedComment == nil {
				m.RepliedComment = &GuildCircleTopicComment{}
			}
			if err := m.RepliedComment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageList = append(m.ImageList, &GuildCircleCommentImage{})
			if err := m.ImageList[len(m.ImageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Floor", wireType)
			}
			m.Floor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Floor |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleDynamicData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleDynamicData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleDynamicData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicCount", wireType)
			}
			m.TopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TodayTopicCount", wireType)
			}
			m.TodayTopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TodayTopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCirclePostTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCirclePostTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCirclePostTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgKeyList = append(m.ImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Highlight", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Highlight = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("client_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCirclePostTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCirclePostTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCirclePostTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Topic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Topic == nil {
				m.Topic = &GuildCircleTopic{}
			}
			if err := m.Topic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("client_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetTopicListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetTopicListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetTopicListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageCount", wireType)
			}
			m.PageCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PagePosition", wireType)
			}
			m.PagePosition = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PagePosition |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Highlight", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Highlight = bool(v != 0)
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RequireCount", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RequireCount = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("page_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("page_position")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("highlight")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetTopicListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetTopicListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetTopicListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageCount", wireType)
			}
			m.PageCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PagePosition", wireType)
			}
			m.PagePosition = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PagePosition |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicList = append(m.TopicList, &GuildCircleTopic{})
			if err := m.TopicList[len(m.TopicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewestTopicId", wireType)
			}
			m.NewestTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewestTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalTopicCount", wireType)
			}
			m.TotalTopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalTopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("page_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("page_position")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("newest_topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUserCount", wireType)
			}
			m.LikeUserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeUserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopCommentCount", wireType)
			}
			m.TopCommentCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopCommentCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Topic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Topic == nil {
				m.Topic = &GuildCircleTopic{}
			}
			if err := m.Topic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUserList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LikeUserList = append(m.LikeUserList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopCommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopCommentList = append(m.TopCommentList, &GuildCircleTopicComment{})
			if err := m.TopCommentList[len(m.TopCommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCirclePostCommentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCirclePostCommentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCirclePostCommentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedCommentId", wireType)
			}
			m.RepliedCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RepliedCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgKeyList = append(m.ImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("replied_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCirclePostCommentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCirclePostCommentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCirclePostCommentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedCommentId", wireType)
			}
			m.RepliedCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RepliedCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Comment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Comment == nil {
				m.Comment = &GuildCircleTopicComment{}
			}
			if err := m.Comment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("replied_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleLikeTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleLikeTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleLikeTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLike", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLike = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_like")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleLikeTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleLikeTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleLikeTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLike", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLike = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_like")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleReportTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleReportTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleReportTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleReportTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleReportTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleReportTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleDeleteTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleDeleteTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleDeleteTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleDeleteTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleDeleteTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleDeleteTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleDeleteCommentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleDeleteCommentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleDeleteCommentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleDeleteCommentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleDeleteCommentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleDeleteCommentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetCircleDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetCircleDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetCircleDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetCircleDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetCircleDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetCircleDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildCircleDynamic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GuildCircleDynamic == nil {
				m.GuildCircleDynamic = &GuildCircleDynamicData{}
			}
			if err := m.GuildCircleDynamic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_circle_dynamic")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetUserTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetUserTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetUserTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTopicId", wireType)
			}
			m.StartTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicCount", wireType)
			}
			m.TopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetUserTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetUserTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetUserTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTopicId", wireType)
			}
			m.StartTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicCount", wireType)
			}
			m.TopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicList = append(m.TopicList, &GuildCircleTopic{})
			if err := m.TopicList[len(m.TopicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetLikeUserListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetLikeUserListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetLikeUserListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetLikeUserListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetLikeUserListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetLikeUserListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserNickList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserNickList = append(m.UserNickList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleMarkReadedReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleMarkReadedReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleMarkReadedReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleMarkReadedResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleMarkReadedResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleMarkReadedResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleCancelHighlightTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleCancelHighlightTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleCancelHighlightTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleCancelHighlightTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleCancelHighlightTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleCancelHighlightTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCommentReplayTargetBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCommentReplayTargetBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCommentReplayTargetBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &ga.CircleUser{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCommentBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCommentBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCommentBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &ga.CircleUser{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyTarget", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ReplyTarget == nil {
				m.ReplyTarget = &GuildCommentReplayTargetBase{}
			}
			if err := m.ReplyTarget.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleTopicNomalComment) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleTopicNomalComment: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleTopicNomalComment: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentBase", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CommentBase == nil {
				m.CommentBase = &GuildCommentBase{}
			}
			if err := m.CommentBase.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Floor", wireType)
			}
			m.Floor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Floor |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageList = append(m.ImageList, &GuildCircleCommentImage{})
			if err := m.ImageList[len(m.ImageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyCommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReplyCommentList = append(m.ReplyCommentList, &GuildCommentBase{})
			if err := m.ReplyCommentList[len(m.ReplyCommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyTotalCnt", wireType)
			}
			m.ReplyTotalCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplyTotalCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_base")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("floor")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetNomalCommentListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetNomalCommentListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetNomalCommentListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCommentId", wireType)
			}
			m.StartCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncludeStartId", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IncludeStartId = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDesc", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDesc = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetNomalCommentListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetNomalCommentListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetNomalCommentListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCommentId", wireType)
			}
			m.StartCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommentList = append(m.CommentList, &GuildCircleTopicNomalComment{})
			if err := m.CommentList[len(m.CommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NomalLeftCnt", wireType)
			}
			m.NomalLeftCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NomalLeftCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetCommentReplyListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetCommentReplyListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetCommentReplyListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentCommentId", wireType)
			}
			m.ParentCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartReplyCommentId", wireType)
			}
			m.StartReplyCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartReplyCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("parent_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_reply_comment_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleGetCommentReplyListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleGetCommentReplyListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleGetCommentReplyListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentCommentId", wireType)
			}
			m.ParentCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartReplyCommentId", wireType)
			}
			m.StartReplyCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartReplyCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReplyList = append(m.ReplyList, &GuildCommentBase{})
			if err := m.ReplyList[len(m.ReplyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyTotalCnt", wireType)
			}
			m.ReplyTotalCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplyTotalCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyLeftCnt", wireType)
			}
			m.ReplyLeftCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplyLeftCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("parent_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_reply_comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleAddTopicTagReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleAddTopicTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleAddTopicTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			m.Tag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Add", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Add = bool(v != 0)
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("tag")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("add")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildCircleAddTopicTagResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildCircleAddTopicTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildCircleAddTopicTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildcircle_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildcircle_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGuildcircle_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGuildcircle_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildcircle_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGuildcircle_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGuildcircle_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGuildcircle_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGuildcircle_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGuildcircle_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("guildcircle_.proto", fileDescriptorGuildcircle_) }

var fileDescriptorGuildcircle_ = []byte{
	// 2105 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x5a, 0x4d, 0x6c, 0x23, 0x49,
	0x15, 0x9e, 0xf6, 0xbf, 0x9f, 0x9d, 0xc4, 0xd3, 0x3b, 0x1b, 0x7a, 0x32, 0x99, 0x8c, 0xa7, 0x66,
	0x81, 0x6c, 0x34, 0xca, 0x8c, 0xb2, 0x5a, 0xc1, 0x70, 0x9b, 0xb1, 0xad, 0x89, 0x85, 0x93, 0x89,
	0x3a, 0x1e, 0xd0, 0x72, 0x69, 0xf5, 0x74, 0x57, 0x3a, 0x4d, 0xda, 0x6e, 0x4f, 0x77, 0x79, 0x57,
	0x3e, 0x03, 0x5a, 0x21, 0x04, 0x42, 0x2b, 0xc4, 0x81, 0x0b, 0x5a, 0x0e, 0x88, 0x03, 0x08, 0x84,
	0xc4, 0x85, 0x2b, 0x97, 0x39, 0x21, 0x2e, 0x5c, 0x11, 0x0a, 0x17, 0x2e, 0xdc, 0x10, 0x17, 0x2e,
	0xa8, 0xaa, 0xba, 0xed, 0xaa, 0x76, 0x3b, 0x8e, 0x87, 0xc8, 0xb9, 0x44, 0xd6, 0x7b, 0xaf, 0xbb,
	0xde, 0xfb, 0xea, 0x7b, 0xaf, 0x5e, 0xbd, 0x0e, 0xa8, 0xce, 0xd0, 0xf5, 0x6c, 0xcb, 0x0d, 0x2c,
	0x0f, 0x1b, 0xbb, 0x83, 0xc0, 0x27, 0xbe, 0x9a, 0x71, 0xcc, 0x8d, 0x15, 0xc7, 0x34, 0x5e, 0x99,
	0x21, 0xe6, 0x22, 0xf4, 0x1b, 0x05, 0xde, 0x7d, 0x4e, 0x2d, 0x1b, 0xcc, 0xb2, 0xeb, 0x0f, 0x5c,
	0xab, 0xdd, 0x33, 0x1d, 0xac, 0xde, 0x87, 0x32, 0x39, 0x1d, 0xf6, 0x5e, 0x19, 0xc3, 0xc0, 0xd3,
	0x94, 0x7a, 0x66, 0xbb, 0xfc, 0x2c, 0xf7, 0xe6, 0x6f, 0xf7, 0x6e, 0xe8, 0x25, 0x26, 0x7e, 0x19,
	0x78, 0xd4, 0xc4, 0xa5, 0xb6, 0xcc, 0x24, 0x23, 0x9a, 0x30, 0x31, 0x35, 0xf9, 0x22, 0x54, 0xb8,
	0xc9, 0x27, 0xae, 0x4d, 0x4e, 0xb5, 0x6c, 0x5d, 0xd9, 0x5e, 0x89, 0x8c, 0x80, 0x29, 0xbe, 0x49,
	0xe5, 0xea, 0x97, 0xa1, 0xca, 0xcd, 0x4e, 0xb1, 0xeb, 0x9c, 0x12, 0x2d, 0x27, 0xd8, 0xf1, 0x17,
	0xec, 0x33, 0x05, 0xfa, 0x4f, 0x0e, 0x6a, 0x49, 0x7f, 0xd5, 0x7b, 0x50, 0x62, 0xd1, 0x1a, 0xae,
	0xcd, 0xdc, 0x88, 0x9f, 0x2c, 0x32, 0x69, 0xdb, 0xa6, 0x06, 0x84, 0x5a, 0x52, 0x83, 0xac, 0x68,
	0xc0, 0xa4, 0x6d, 0x5b, 0xdd, 0x80, 0x3c, 0x71, 0x89, 0x87, 0xb5, 0x9c, 0x10, 0x05, 0x17, 0xa9,
	0x5b, 0x50, 0xb4, 0xfc, 0x3e, 0xc1, 0x7d, 0xa2, 0xe5, 0x05, 0x6d, 0x2c, 0xa4, 0x21, 0x5a, 0x01,
	0x36, 0x09, 0x36, 0x88, 0xdb, 0xc3, 0x5a, 0x41, 0x78, 0x3f, 0x70, 0x45, 0xd7, 0xed, 0x61, 0xf5,
	0xab, 0xc0, 0x03, 0x36, 0x3c, 0x37, 0x24, 0x5a, 0xb1, 0x9e, 0xdd, 0xae, 0xec, 0xdd, 0xde, 0x75,
	0xcc, 0xdd, 0x54, 0xf8, 0x75, 0x8e, 0x6c, 0xc7, 0x0d, 0x89, 0xfa, 0x00, 0xc0, 0x73, 0xcf, 0xb0,
	0x61, 0xf9, 0xc3, 0x3e, 0xd1, 0x4a, 0xc2, 0xfb, 0xcb, 0x54, 0xde, 0xa0, 0x62, 0xf5, 0x7d, 0x58,
	0xb1, 0xfc, 0x5e, 0x0f, 0xf7, 0x49, 0x64, 0x57, 0x16, 0xec, 0xaa, 0x91, 0x8a, 0x9b, 0xde, 0x83,
	0x92, 0x1b, 0x1a, 0xf4, 0x51, 0x5b, 0x03, 0x11, 0x0d, 0x37, 0xec, 0x50, 0x21, 0x8d, 0x88, 0xc3,
	0x15, 0x12, 0x93, 0x60, 0xad, 0x22, 0x46, 0xc4, 0x14, 0xc7, 0x54, 0xae, 0x3e, 0x86, 0x9b, 0x9e,
	0x19, 0xd2, 0xf5, 0xf8, 0xba, 0x2c, 0xfc, 0xaa, 0x60, 0xbc, 0x46, 0xd5, 0x0d, 0xae, 0x65, 0x18,
	0x6c, 0x43, 0x91, 0x21, 0xe2, 0x07, 0xda, 0x4a, 0x5d, 0xd9, 0xae, 0xec, 0xad, 0x52, 0x00, 0x78,
	0xec, 0x2f, 0x43, 0x1c, 0xe8, 0xb1, 0x5a, 0xdd, 0x85, 0x9a, 0x00, 0xaa, 0x61, 0xe3, 0xd0, 0xd2,
	0x56, 0x05, 0xf4, 0x57, 0x27, 0xc8, 0x36, 0x71, 0x68, 0xa9, 0x4f, 0x60, 0x7d, 0xca, 0x17, 0xfe,
	0xd4, 0x9a, 0xf0, 0xd4, 0x3b, 0x09, 0x87, 0xe8, 0xa3, 0xe8, 0x3d, 0xa8, 0x74, 0x5f, 0x1c, 0xb5,
	0x1b, 0xc6, 0x71, 0xf7, 0x69, 0xb7, 0xa5, 0xbe, 0x0b, 0x37, 0xd9, 0x0f, 0x63, 0xbf, 0xfd, 0x7c,
	0xbf, 0x6b, 0x74, 0xe8, 0xdf, 0x9a, 0x82, 0x0c, 0xf8, 0x82, 0xb0, 0x51, 0xd1, 0x3b, 0xae, 0x30,
	0x53, 0xd0, 0xcf, 0x72, 0xd2, 0x0a, 0x8c, 0x0a, 0xd1, 0x32, 0x94, 0x01, 0x71, 0x60, 0xae, 0xcd,
	0x96, 0x18, 0x33, 0x20, 0x92, 0xb7, 0x6d, 0xba, 0x46, 0x94, 0xee, 0x89, 0x34, 0x28, 0x71, 0xf1,
	0x65, 0xf2, 0x40, 0xe0, 0x7a, 0x2e, 0x8d, 0xeb, 0xc2, 0x06, 0xd2, 0x5c, 0xb8, 0x60, 0x03, 0x2f,
	0x99, 0x15, 0x9b, 0x50, 0xa0, 0x24, 0x1b, 0x86, 0x5a, 0x51, 0xb0, 0x88, 0x64, 0x6a, 0x13, 0xd6,
	0x02, 0x3c, 0xf0, 0x5c, 0x6c, 0xc7, 0x1b, 0xab, 0x95, 0x18, 0x6f, 0xee, 0xa4, 0x25, 0x4e, 0x84,
	0x96, 0xbe, 0x1a, 0x3d, 0x13, 0xa3, 0x97, 0xc6, 0xa5, 0xf2, 0x05, 0x5c, 0xfa, 0x9a, 0x94, 0xa9,
	0xc0, 0x32, 0x35, 0xb9, 0xa0, 0x48, 0x00, 0x31, 0x57, 0x37, 0x20, 0x7f, 0xe2, 0xf9, 0x7e, 0xa0,
	0x55, 0xea, 0xca, 0x76, 0x3e, 0x2e, 0x24, 0x4c, 0x84, 0x9e, 0x80, 0x2a, 0xfa, 0x79, 0xcc, 0x63,
	0x04, 0x28, 0x1c, 0xbe, 0xd0, 0x0f, 0x9e, 0x76, 0x6a, 0x37, 0xd4, 0x0a, 0x14, 0x9b, 0xad, 0x4e,
	0xab, 0xdb, 0x6a, 0xd6, 0x14, 0xaa, 0x38, 0xde, 0x6f, 0xb7, 0x3a, 0xcd, 0x5a, 0x06, 0x7d, 0xa6,
	0xc0, 0xba, 0xb0, 0x7a, 0x73, 0xd4, 0x37, 0x7b, 0xae, 0xd5, 0x34, 0x89, 0x29, 0x15, 0x3f, 0x25,
	0xad, 0xf8, 0x8d, 0xb3, 0x99, 0xd7, 0x85, 0x8c, 0x58, 0x82, 0x09, 0xf7, 0x87, 0x56, 0x85, 0xc7,
	0x70, 0x93, 0xf8, 0xb6, 0x39, 0x32, 0x44, 0x63, 0xb1, 0x5e, 0xaf, 0x31, 0x75, 0x77, 0xfc, 0x04,
	0xfa, 0x34, 0x23, 0x31, 0xf6, 0xc8, 0x0f, 0x09, 0xd3, 0xea, 0xf8, 0xb5, 0xfa, 0x25, 0x28, 0xd1,
	0x53, 0xc6, 0x08, 0xf0, 0x6b, 0xe6, 0x55, 0x65, 0xaf, 0x42, 0x11, 0x7c, 0x66, 0x86, 0x58, 0xc7,
	0xaf, 0xf5, 0xe2, 0x2b, 0xfe, 0x63, 0x7e, 0xe9, 0xa6, 0xac, 0xf6, 0xdc, 0x88, 0xf9, 0x59, 0x31,
	0x73, 0xb8, 0x58, 0x2e, 0xde, 0xca, 0xa2, 0xc5, 0xbb, 0x4e, 0x0f, 0x1e, 0xc7, 0x38, 0xc3, 0x23,
	0xbe, 0xdb, 0x85, 0x7a, 0x76, 0xbb, 0x4c, 0x8f, 0x26, 0xe7, 0xeb, 0x78, 0xc4, 0x76, 0x14, 0x41,
	0xf9, 0xd4, 0x75, 0x4e, 0x3d, 0x76, 0x2e, 0x15, 0xeb, 0xca, 0x76, 0x29, 0x4e, 0xbd, 0xb1, 0x18,
	0xfd, 0x44, 0x01, 0x2d, 0x1d, 0x89, 0x70, 0xa0, 0xbe, 0x0f, 0xe5, 0x08, 0x8a, 0x70, 0x10, 0x61,
	0x51, 0x9d, 0x60, 0x11, 0x0e, 0xf4, 0xd2, 0xab, 0xe8, 0x97, 0x1c, 0x6c, 0x26, 0x35, 0xd8, 0x1d,
	0xc8, 0xb3, 0x0d, 0x62, 0x58, 0x54, 0xf6, 0x6e, 0xa5, 0x25, 0x82, 0xce, 0x4d, 0xd0, 0x77, 0x33,
	0xb0, 0x21, 0xe8, 0x9e, 0x63, 0xee, 0x15, 0x0d, 0xeb, 0x4a, 0xf7, 0xe8, 0x01, 0xc0, 0x80, 0xe6,
	0x4b, 0xcc, 0x19, 0xa1, 0x3c, 0x51, 0xf9, 0xf8, 0x80, 0x62, 0x46, 0x03, 0x3f, 0x74, 0x89, 0xeb,
	0xf7, 0x59, 0x81, 0x19, 0x1f, 0x50, 0x54, 0x75, 0x14, 0x69, 0x64, 0xc8, 0xe9, 0xb6, 0x4d, 0x43,
	0x4e, 0x5f, 0x17, 0xe0, 0xd7, 0x43, 0x37, 0x88, 0x97, 0x2d, 0x08, 0x5b, 0x53, 0x8d, 0x54, 0x9c,
	0xa7, 0x7f, 0xce, 0xc0, 0x9d, 0x99, 0x30, 0x2c, 0xb6, 0x41, 0x4b, 0x87, 0xe2, 0x03, 0xe0, 0x39,
	0xca, 0xd9, 0x99, 0x67, 0xb5, 0x28, 0x7d, 0xcf, 0xcb, 0x24, 0x0e, 0x4a, 0x7d, 0x08, 0x6b, 0x7d,
	0xfc, 0x09, 0x0e, 0x89, 0x31, 0xae, 0xf6, 0x62, 0xfd, 0x5d, 0xe1, 0xca, 0x6e, 0x54, 0xf3, 0x59,
	0xe2, 0x13, 0xd3, 0x93, 0x12, 0xbf, 0x28, 0x27, 0x3e, 0x31, 0x3d, 0x21, 0xf1, 0xcf, 0xe5, 0x6a,
	0x14, 0x03, 0x7a, 0xa5, 0x9c, 0x9a, 0x7b, 0x54, 0x3d, 0x84, 0x35, 0xd6, 0x15, 0x0d, 0x43, 0x1c,
	0x44, 0x4e, 0x8b, 0x5d, 0xe3, 0x0a, 0x55, 0xd2, 0x23, 0x49, 0xa8, 0x6e, 0x03, 0x43, 0x6e, 0x91,
	0xf2, 0x72, 0x90, 0x83, 0x86, 0xd0, 0x25, 0xa1, 0x9f, 0xcb, 0xd5, 0x6d, 0x12, 0xe4, 0x15, 0x33,
	0x66, 0x6e, 0xa0, 0xe3, 0x8c, 0xcf, 0xcd, 0xcd, 0x78, 0xf5, 0x3d, 0x58, 0x9d, 0x80, 0x32, 0xa6,
	0x4c, 0x59, 0xaf, 0xc6, 0x68, 0x30, 0x7e, 0xb4, 0xa0, 0x26, 0x82, 0x31, 0x2e, 0x7c, 0xf3, 0xce,
	0xd5, 0x09, 0x44, 0xf4, 0x35, 0xe8, 0xbf, 0x0a, 0xdc, 0x4e, 0x54, 0xbd, 0xd8, 0x74, 0xa9, 0x4c,
	0xd8, 0x03, 0x35, 0xd1, 0x25, 0x50, 0x53, 0x31, 0xa7, 0x6a, 0x72, 0x47, 0x20, 0x37, 0x3a, 0x6f,
	0x77, 0x2e, 0xa0, 0x7f, 0x2b, 0x52, 0x71, 0x95, 0xa2, 0x5f, 0x36, 0x45, 0xde, 0x06, 0x81, 0x0f,
	0x29, 0x02, 0xbc, 0xa7, 0xe2, 0xad, 0xdc, 0x85, 0x7b, 0x1f, 0xdb, 0xa2, 0x5f, 0x28, 0x52, 0x5a,
	0xd0, 0x0b, 0xc3, 0x35, 0x24, 0xff, 0x5d, 0x28, 0x46, 0x57, 0x18, 0x16, 0x65, 0x5c, 0xf7, 0x0b,
	0xfc, 0x06, 0x83, 0x7e, 0x29, 0x9f, 0xc7, 0x82, 0x93, 0xcb, 0xde, 0x99, 0x39, 0x8e, 0x7e, 0x2a,
	0xa7, 0x90, 0x8e, 0x07, 0x7e, 0xb0, 0x78, 0x31, 0xbd, 0x82, 0xce, 0x1f, 0xfd, 0x40, 0xa6, 0xb3,
	0xe4, 0xc9, 0xe2, 0x4d, 0xcc, 0xff, 0xeb, 0xcd, 0xf7, 0x64, 0x5c, 0x9a, 0xd8, 0xc3, 0xe4, 0x1a,
	0x78, 0x86, 0xbe, 0x2f, 0xa3, 0x22, 0xf9, 0xb1, 0x64, 0x2a, 0xa1, 0x5f, 0x2b, 0x52, 0x1b, 0xc3,
	0x7d, 0xb9, 0x96, 0x82, 0x2b, 0x5f, 0x47, 0x73, 0xa9, 0xd7, 0x51, 0xf4, 0x3b, 0x05, 0x36, 0x67,
	0xbb, 0xbb, 0xec, 0x3c, 0xbc, 0x94, 0xcb, 0xa7, 0x70, 0x57, 0x3e, 0xf1, 0x63, 0xd7, 0x89, 0xe9,
	0x7a, 0x57, 0x09, 0x31, 0xfa, 0xa3, 0x02, 0x5b, 0x17, 0x2d, 0x75, 0xc5, 0xf0, 0x74, 0xe0, 0x16,
	0x37, 0x88, 0x12, 0xd3, 0xe6, 0xf7, 0xc7, 0xe8, 0x0e, 0xb1, 0x91, 0x28, 0xfc, 0xc2, 0xed, 0x52,
	0xe7, 0x63, 0x44, 0x49, 0x8e, 0x7e, 0xab, 0x24, 0xaf, 0x15, 0xb4, 0xb3, 0x58, 0x38, 0x3b, 0x77,
	0x60, 0x35, 0x24, 0x66, 0x20, 0x34, 0xa9, 0xa2, 0xef, 0x55, 0xa6, 0x8b, 0x7b, 0xd4, 0xc4, 0x1d,
	0x36, 0x3b, 0x35, 0x91, 0xe2, 0x5d, 0xde, 0x3a, 0x64, 0x87, 0x89, 0xed, 0xa5, 0x02, 0xf4, 0x4f,
	0x25, 0x79, 0x03, 0x10, 0x3c, 0x5e, 0x0c, 0xeb, 0x69, 0xaf, 0xb3, 0x97, 0xf5, 0x3a, 0x37, 0xc3,
	0xeb, 0xb7, 0xea, 0xf1, 0xa3, 0x50, 0x0b, 0xc9, 0x50, 0xff, 0xa4, 0x24, 0x49, 0xdc, 0x11, 0x5a,
	0xbf, 0xe5, 0xd6, 0x89, 0x4d, 0x28, 0xf8, 0x27, 0x27, 0x21, 0x96, 0x43, 0x8f, 0x64, 0xf4, 0xda,
	0xee, 0xb9, 0x3d, 0x97, 0xb7, 0x1f, 0xb1, 0x92, 0x8b, 0xd0, 0xbf, 0xa6, 0xf2, 0x43, 0x8e, 0x62,
	0xd9, 0xe5, 0xe3, 0xad, 0x23, 0xa1, 0x1d, 0x39, 0x6b, 0xc6, 0xfb, 0xae, 0x75, 0x26, 0xb6, 0x92,
	0x55, 0x2a, 0x3d, 0x74, 0xad, 0x33, 0xd6, 0x4c, 0x9e, 0x48, 0xfd, 0xca, 0x81, 0x19, 0x9c, 0xe9,
	0xd8, 0xb4, 0xb1, 0xbd, 0xc8, 0x7e, 0x21, 0x80, 0xf0, 0xe3, 0xc0, 0xe8, 0x85, 0xce, 0xd4, 0xc1,
	0x1b, 0x7e, 0x1c, 0x1c, 0x84, 0x4e, 0xdb, 0x46, 0xdf, 0x96, 0x8e, 0x55, 0x71, 0x9d, 0xc5, 0x10,
	0xbd, 0xcc, 0x5a, 0x3f, 0x52, 0x00, 0x89, 0x13, 0x33, 0xb3, 0x6f, 0x61, 0x6f, 0x3f, 0xbe, 0xc0,
	0x5f, 0xc3, 0x61, 0xfe, 0x99, 0x02, 0x0f, 0xe6, 0x3a, 0xb4, 0xec, 0x53, 0xfd, 0x7c, 0x7c, 0x4c,
	0xc6, 0x47, 0xe3, 0xc0, 0x33, 0x47, 0x5d, 0x33, 0x70, 0x30, 0xa1, 0x0b, 0x5e, 0x6e, 0xf6, 0x2b,
	0xcc, 0x65, 0x33, 0x0b, 0xcd, 0x65, 0xb3, 0x73, 0xe7, 0xb2, 0xb9, 0x94, 0xb9, 0x6c, 0xda, 0x44,
	0x35, 0x3f, 0x7b, 0xa2, 0x8a, 0xfe, 0x9a, 0x89, 0xbf, 0xda, 0x70, 0x8f, 0x2f, 0x1f, 0x98, 0x70,
	0x4f, 0xcb, 0xcc, 0x19, 0x48, 0x67, 0x17, 0x0a, 0x3c, 0x37, 0x37, 0xf0, 0x7c, 0x4a, 0xe0, 0x1a,
	0xe4, 0xc8, 0x68, 0x20, 0x8f, 0xb3, 0x99, 0x24, 0x15, 0x92, 0xe2, 0x05, 0x43, 0xe6, 0x06, 0x54,
	0xe9, 0x95, 0x6c, 0x64, 0x10, 0xb6, 0xd5, 0xd1, 0x5c, 0xbb, 0x3e, 0x29, 0xfb, 0xe9, 0x74, 0xd0,
	0x2b, 0xec, 0x29, 0x2e, 0x40, 0x9f, 0x67, 0xa4, 0x1e, 0x8b, 0x51, 0xf8, 0xd0, 0xef, 0x99, 0x5e,
	0x3c, 0xfa, 0xfe, 0x0a, 0xc4, 0x9f, 0x7e, 0xd8, 0x47, 0xbf, 0x88, 0xcd, 0xb7, 0x92, 0xab, 0xf0,
	0x37, 0x5b, 0xc2, 0xe6, 0x8c, 0xe7, 0xd8, 0x14, 0x75, 0x79, 0x8e, 0x9d, 0x98, 0x8f, 0x67, 0x17,
	0x9a, 0x8f, 0x3f, 0xe3, 0x37, 0xd5, 0x91, 0x3c, 0x7c, 0xc8, 0x25, 0xcf, 0x3c, 0xc1, 0x2d, 0x76,
	0x73, 0x1d, 0x09, 0x73, 0x07, 0xf5, 0x21, 0xff, 0x2a, 0x30, 0x32, 0xf8, 0xd8, 0xca, 0x4a, 0x4c,
	0x72, 0x56, 0x38, 0x42, 0x54, 0xd7, 0xe8, 0x13, 0xf4, 0xab, 0x0c, 0xdc, 0x97, 0x8f, 0x12, 0x11,
	0xa1, 0xe5, 0x1f, 0x8a, 0xbb, 0x50, 0xe3, 0x0d, 0xc4, 0x8c, 0x7e, 0x94, 0xb7, 0x17, 0x93, 0x7b,
	0xfa, 0x06, 0xe4, 0xe3, 0x69, 0x95, 0x70, 0xb8, 0x30, 0x11, 0x7d, 0x97, 0xdb, 0xb7, 0xbc, 0xa1,
	0x8d, 0x0d, 0xfe, 0x4e, 0xd6, 0x11, 0x4c, 0xe6, 0xa0, 0xab, 0x91, 0xf6, 0x98, 0x2a, 0xc7, 0xb7,
	0xd1, 0x88, 0x9b, 0x8a, 0x78, 0x1b, 0x65, 0x69, 0xfa, 0xfb, 0x8c, 0x54, 0xb1, 0x53, 0xa1, 0x5a,
	0xf6, 0xc9, 0xbb, 0x28, 0x5c, 0x8d, 0x09, 0xe3, 0x85, 0x76, 0xaa, 0x9e, 0xd6, 0x4e, 0x89, 0xc1,
	0x8d, 0xd9, 0xcf, 0x18, 0xb6, 0x03, 0xab, 0x7d, 0xaa, 0x34, 0x3c, 0x7c, 0x42, 0x18, 0xc1, 0x0a,
	0x02, 0xc1, 0xaa, 0x4c, 0xd7, 0xc1, 0x27, 0x84, 0xf2, 0xeb, 0xa7, 0x53, 0xfc, 0x12, 0x52, 0x77,
	0xb4, 0x7c, 0x7e, 0x3d, 0x86, 0x9b, 0x03, 0x33, 0xe0, 0x43, 0xce, 0x54, 0xc4, 0xd6, 0xb8, 0x7a,
	0x02, 0xd9, 0x13, 0x58, 0xe7, 0x10, 0xcb, 0x99, 0xe9, 0xda, 0x12, 0xe5, 0xde, 0x61, 0x36, 0xba,
	0x90, 0x8c, 0x22, 0x39, 0x0b, 0x53, 0xe4, 0x44, 0x3f, 0xcc, 0x26, 0xd9, 0x34, 0x0d, 0xcc, 0xb2,
	0xd9, 0xb4, 0x54, 0x70, 0x3e, 0x00, 0xe0, 0x0f, 0x09, 0x03, 0xd6, 0xf4, 0x1a, 0x57, 0x0e, 0x62,
	0x48, 0xd2, 0x8a, 0x5b, 0x71, 0x66, 0x71, 0xa3, 0x44, 0x8d, 0x96, 0x88, 0x89, 0x5a, 0x12, 0x89,
	0xca, 0x5f, 0x1b, 0x11, 0xf5, 0x0f, 0xf2, 0x4c, 0xe5, 0xa9, 0x6d, 0xb3, 0x2c, 0xe8, 0x9a, 0xce,
	0x72, 0x09, 0xba, 0x0e, 0x59, 0x62, 0x3a, 0xf2, 0x25, 0x8d, 0x98, 0x0e, 0x95, 0x9b, 0xb6, 0x2d,
	0x7d, 0xef, 0xa1, 0x82, 0xe4, 0x0c, 0x46, 0xf2, 0x7b, 0xc9, 0xfc, 0xd9, 0xf9, 0x8e, 0x7c, 0xbb,
	0x4a, 0xf9, 0x9c, 0x7b, 0x0f, 0xee, 0x3c, 0x7f, 0xd9, 0xee, 0x34, 0x8d, 0xc6, 0x8b, 0x83, 0x83,
	0xd6, 0x61, 0x97, 0xfd, 0x57, 0xc1, 0xcb, 0x63, 0x63, 0xfc, 0x8d, 0xb7, 0x0e, 0x9b, 0xa9, 0x06,
	0x93, 0x0f, 0xbf, 0xb3, 0x5e, 0x11, 0x7f, 0x0d, 0xde, 0xf9, 0x5c, 0xbe, 0xce, 0x8a, 0x5e, 0x74,
	0x69, 0x2f, 0x72, 0x17, 0x6e, 0xcb, 0x2f, 0xe8, 0x7e, 0x74, 0xd4, 0x9a, 0x78, 0xb0, 0x09, 0x5a,
	0x8a, 0x5a, 0x6f, 0x1d, 0x75, 0x3e, 0xaa, 0x29, 0x2a, 0x82, 0xad, 0x59, 0xda, 0xc8, 0x26, 0xa3,
	0xde, 0x87, 0xbb, 0x29, 0x36, 0xfb, 0x4f, 0xbf, 0x11, 0xbf, 0x26, 0xf7, 0xec, 0xf0, 0xcd, 0xf9,
	0x96, 0xf2, 0x97, 0xf3, 0x2d, 0xe5, 0xef, 0xe7, 0x5b, 0xca, 0x8f, 0xff, 0xb1, 0x75, 0x03, 0x34,
	0xcb, 0xef, 0xed, 0x8e, 0xdc, 0x91, 0x3f, 0xa4, 0xdb, 0xd3, 0xf3, 0x6d, 0xec, 0xf1, 0x7f, 0x42,
	0xfa, 0x16, 0x72, 0x7c, 0xcf, 0xec, 0x3b, 0xbb, 0x1f, 0xee, 0x11, 0xb2, 0x6b, 0xf9, 0xbd, 0x47,
	0x4c, 0x6c, 0xf9, 0xde, 0x23, 0x73, 0x30, 0x78, 0xc4, 0x36, 0xe7, 0x7f, 0x01, 0x00, 0x00, 0xff,
	0xff, 0x32, 0x9a, 0xf8, 0xa5, 0xd0, 0x24, 0x00, 0x00,
}
