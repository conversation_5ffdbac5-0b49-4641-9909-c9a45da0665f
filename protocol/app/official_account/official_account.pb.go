// Code generated by protoc-gen-gogo.
// source: official_account.proto
// DO NOT EDIT!

/*
	Package official_account is a generated protocol buffer package.

	It is generated from these files:
		official_account.proto

	It has these top-level messages:
		InAppNavigationCommand
		SystemBrowserCommand
		GameDownloadCommand
		OfficialAccountSettings
		OfficialAccountUpdateSettingsReq
		OfficialAccountUpdateSettingsResp
		OfficialAccountGetNewestMessageReq
		OfficialAccountGetNewestMessageResp
		UnsubscribeOfficialAccountReq
		UnsubscribeOfficialAccountResp
		GetOfficialAccountDetailReq
		GetOfficialAccountDetailResp
*/
package official_account

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type OfficialAccountCmd int32

const (
	OfficialAccountCmd_IN_APP_NAVIGATION OfficialAccountCmd = 1
	OfficialAccountCmd_SYSTEM_BROWSER    OfficialAccountCmd = 2
	// /////////////////////
	OfficialAccountCmd_SPECIAL_COMMAND_BEGIN OfficialAccountCmd = 10000
	OfficialAccountCmd_GAME_DOWNLOAD         OfficialAccountCmd = 10001
	OfficialAccountCmd_GET_LATEST_MESSAGE    OfficialAccountCmd = 10002
)

var OfficialAccountCmd_name = map[int32]string{
	1:     "IN_APP_NAVIGATION",
	2:     "SYSTEM_BROWSER",
	10000: "SPECIAL_COMMAND_BEGIN",
	10001: "GAME_DOWNLOAD",
	10002: "GET_LATEST_MESSAGE",
}
var OfficialAccountCmd_value = map[string]int32{
	"IN_APP_NAVIGATION":     1,
	"SYSTEM_BROWSER":        2,
	"SPECIAL_COMMAND_BEGIN": 10000,
	"GAME_DOWNLOAD":         10001,
	"GET_LATEST_MESSAGE":    10002,
}

func (x OfficialAccountCmd) Enum() *OfficialAccountCmd {
	p := new(OfficialAccountCmd)
	*p = x
	return p
}
func (x OfficialAccountCmd) String() string {
	return proto.EnumName(OfficialAccountCmd_name, int32(x))
}
func (x *OfficialAccountCmd) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OfficialAccountCmd_value, data, "OfficialAccountCmd")
	if err != nil {
		return err
	}
	*x = OfficialAccountCmd(value)
	return nil
}
func (OfficialAccountCmd) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{0}
}

type OfficialAccountSettings_SettingConstants int32

const (
	OfficialAccountSettings_ENABLE  OfficialAccountSettings_SettingConstants = 1
	OfficialAccountSettings_DISABLE OfficialAccountSettings_SettingConstants = 2
)

var OfficialAccountSettings_SettingConstants_name = map[int32]string{
	1: "ENABLE",
	2: "DISABLE",
}
var OfficialAccountSettings_SettingConstants_value = map[string]int32{
	"ENABLE":  1,
	"DISABLE": 2,
}

func (x OfficialAccountSettings_SettingConstants) Enum() *OfficialAccountSettings_SettingConstants {
	p := new(OfficialAccountSettings_SettingConstants)
	*p = x
	return p
}
func (x OfficialAccountSettings_SettingConstants) String() string {
	return proto.EnumName(OfficialAccountSettings_SettingConstants_name, int32(x))
}
func (x *OfficialAccountSettings_SettingConstants) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OfficialAccountSettings_SettingConstants_value, data, "OfficialAccountSettings_SettingConstants")
	if err != nil {
		return err
	}
	*x = OfficialAccountSettings_SettingConstants(value)
	return nil
}
func (OfficialAccountSettings_SettingConstants) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{3, 0}
}

type InAppNavigationCommand struct {
	Uri string `protobuf:"bytes,1,req,name=uri" json:"uri"`
}

func (m *InAppNavigationCommand) Reset()         { *m = InAppNavigationCommand{} }
func (m *InAppNavigationCommand) String() string { return proto.CompactTextString(m) }
func (*InAppNavigationCommand) ProtoMessage()    {}
func (*InAppNavigationCommand) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{0}
}

func (m *InAppNavigationCommand) GetUri() string {
	if m != nil {
		return m.Uri
	}
	return ""
}

type SystemBrowserCommand struct {
	Url string `protobuf:"bytes,1,req,name=url" json:"url"`
}

func (m *SystemBrowserCommand) Reset()         { *m = SystemBrowserCommand{} }
func (m *SystemBrowserCommand) String() string { return proto.CompactTextString(m) }
func (*SystemBrowserCommand) ProtoMessage()    {}
func (*SystemBrowserCommand) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{1}
}

func (m *SystemBrowserCommand) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type GameDownloadCommand struct {
	GameUrl  string `protobuf:"bytes,1,req,name=game_url,json=gameUrl" json:"game_url"`
	GameId   uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	GameName string `protobuf:"bytes,3,req,name=game_name,json=gameName" json:"game_name"`
	GamePkg  string `protobuf:"bytes,4,opt,name=game_pkg,json=gamePkg" json:"game_pkg"`
}

func (m *GameDownloadCommand) Reset()         { *m = GameDownloadCommand{} }
func (m *GameDownloadCommand) String() string { return proto.CompactTextString(m) }
func (*GameDownloadCommand) ProtoMessage()    {}
func (*GameDownloadCommand) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{2}
}

func (m *GameDownloadCommand) GetGameUrl() string {
	if m != nil {
		return m.GameUrl
	}
	return ""
}

func (m *GameDownloadCommand) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameDownloadCommand) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameDownloadCommand) GetGamePkg() string {
	if m != nil {
		return m.GamePkg
	}
	return ""
}

type OfficialAccountSettings struct {
	MessageNotification uint32 `protobuf:"varint,1,opt,name=message_notification,json=messageNotification" json:"message_notification"`
}

func (m *OfficialAccountSettings) Reset()         { *m = OfficialAccountSettings{} }
func (m *OfficialAccountSettings) String() string { return proto.CompactTextString(m) }
func (*OfficialAccountSettings) ProtoMessage()    {}
func (*OfficialAccountSettings) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{3}
}

func (m *OfficialAccountSettings) GetMessageNotification() uint32 {
	if m != nil {
		return m.MessageNotification
	}
	return 0
}

type OfficialAccountUpdateSettingsReq struct {
	BaseReq           *ga.BaseReq              `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	OfficialAccountId uint32                   `protobuf:"varint,2,req,name=official_account_id,json=officialAccountId" json:"official_account_id"`
	Settings          *OfficialAccountSettings `protobuf:"bytes,3,req,name=settings" json:"settings,omitempty"`
}

func (m *OfficialAccountUpdateSettingsReq) Reset()         { *m = OfficialAccountUpdateSettingsReq{} }
func (m *OfficialAccountUpdateSettingsReq) String() string { return proto.CompactTextString(m) }
func (*OfficialAccountUpdateSettingsReq) ProtoMessage()    {}
func (*OfficialAccountUpdateSettingsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{4}
}

func (m *OfficialAccountUpdateSettingsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfficialAccountUpdateSettingsReq) GetOfficialAccountId() uint32 {
	if m != nil {
		return m.OfficialAccountId
	}
	return 0
}

func (m *OfficialAccountUpdateSettingsReq) GetSettings() *OfficialAccountSettings {
	if m != nil {
		return m.Settings
	}
	return nil
}

type OfficialAccountUpdateSettingsResp struct {
	BaseResp          *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	OfficialAccountId uint32                   `protobuf:"varint,2,req,name=official_account_id,json=officialAccountId" json:"official_account_id"`
	UpdatedSettings   *OfficialAccountSettings `protobuf:"bytes,3,req,name=updated_settings,json=updatedSettings" json:"updated_settings,omitempty"`
}

func (m *OfficialAccountUpdateSettingsResp) Reset()         { *m = OfficialAccountUpdateSettingsResp{} }
func (m *OfficialAccountUpdateSettingsResp) String() string { return proto.CompactTextString(m) }
func (*OfficialAccountUpdateSettingsResp) ProtoMessage()    {}
func (*OfficialAccountUpdateSettingsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{5}
}

func (m *OfficialAccountUpdateSettingsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfficialAccountUpdateSettingsResp) GetOfficialAccountId() uint32 {
	if m != nil {
		return m.OfficialAccountId
	}
	return 0
}

func (m *OfficialAccountUpdateSettingsResp) GetUpdatedSettings() *OfficialAccountSettings {
	if m != nil {
		return m.UpdatedSettings
	}
	return nil
}

// 拉取最新消息 -- v1.5
type OfficialAccountGetNewestMessageReq struct {
	BaseReq           *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	OfficialAccountId uint32      `protobuf:"varint,2,req,name=official_account_id,json=officialAccountId" json:"official_account_id"`
}

func (m *OfficialAccountGetNewestMessageReq) Reset()         { *m = OfficialAccountGetNewestMessageReq{} }
func (m *OfficialAccountGetNewestMessageReq) String() string { return proto.CompactTextString(m) }
func (*OfficialAccountGetNewestMessageReq) ProtoMessage()    {}
func (*OfficialAccountGetNewestMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{6}
}

func (m *OfficialAccountGetNewestMessageReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OfficialAccountGetNewestMessageReq) GetOfficialAccountId() uint32 {
	if m != nil {
		return m.OfficialAccountId
	}
	return 0
}

type OfficialAccountGetNewestMessageResp struct {
	BaseResp          *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	OfficialAccountId uint32       `protobuf:"varint,2,req,name=official_account_id,json=officialAccountId" json:"official_account_id"`
	HasMessage        bool         `protobuf:"varint,3,req,name=has_message,json=hasMessage" json:"has_message"`
}

func (m *OfficialAccountGetNewestMessageResp) Reset()         { *m = OfficialAccountGetNewestMessageResp{} }
func (m *OfficialAccountGetNewestMessageResp) String() string { return proto.CompactTextString(m) }
func (*OfficialAccountGetNewestMessageResp) ProtoMessage()    {}
func (*OfficialAccountGetNewestMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{7}
}

func (m *OfficialAccountGetNewestMessageResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *OfficialAccountGetNewestMessageResp) GetOfficialAccountId() uint32 {
	if m != nil {
		return m.OfficialAccountId
	}
	return 0
}

func (m *OfficialAccountGetNewestMessageResp) GetHasMessage() bool {
	if m != nil {
		return m.HasMessage
	}
	return false
}

// 取消关注公众号
type UnsubscribeOfficialAccountReq struct {
	BaseReq           *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	OfficialAccountId uint32      `protobuf:"varint,2,req,name=official_account_id,json=officialAccountId" json:"official_account_id"`
}

func (m *UnsubscribeOfficialAccountReq) Reset()         { *m = UnsubscribeOfficialAccountReq{} }
func (m *UnsubscribeOfficialAccountReq) String() string { return proto.CompactTextString(m) }
func (*UnsubscribeOfficialAccountReq) ProtoMessage()    {}
func (*UnsubscribeOfficialAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{8}
}

func (m *UnsubscribeOfficialAccountReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UnsubscribeOfficialAccountReq) GetOfficialAccountId() uint32 {
	if m != nil {
		return m.OfficialAccountId
	}
	return 0
}

type UnsubscribeOfficialAccountResp struct {
	BaseResp          *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	OfficialAccountId uint32       `protobuf:"varint,2,req,name=official_account_id,json=officialAccountId" json:"official_account_id"`
}

func (m *UnsubscribeOfficialAccountResp) Reset()         { *m = UnsubscribeOfficialAccountResp{} }
func (m *UnsubscribeOfficialAccountResp) String() string { return proto.CompactTextString(m) }
func (*UnsubscribeOfficialAccountResp) ProtoMessage()    {}
func (*UnsubscribeOfficialAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{9}
}

func (m *UnsubscribeOfficialAccountResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UnsubscribeOfficialAccountResp) GetOfficialAccountId() uint32 {
	if m != nil {
		return m.OfficialAccountId
	}
	return 0
}

type GetOfficialAccountDetailReq struct {
	BaseReq           *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	OfficialAccountId uint32      `protobuf:"varint,2,req,name=official_account_id,json=officialAccountId" json:"official_account_id"`
}

func (m *GetOfficialAccountDetailReq) Reset()         { *m = GetOfficialAccountDetailReq{} }
func (m *GetOfficialAccountDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetOfficialAccountDetailReq) ProtoMessage()    {}
func (*GetOfficialAccountDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{10}
}

func (m *GetOfficialAccountDetailReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetOfficialAccountDetailReq) GetOfficialAccountId() uint32 {
	if m != nil {
		return m.OfficialAccountId
	}
	return 0
}

type GetOfficialAccountDetailResp struct {
	BaseResp *ga.BaseResp            `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Detail   *ga.OfficialAccountInfo `protobuf:"bytes,2,req,name=detail" json:"detail,omitempty"`
}

func (m *GetOfficialAccountDetailResp) Reset()         { *m = GetOfficialAccountDetailResp{} }
func (m *GetOfficialAccountDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetOfficialAccountDetailResp) ProtoMessage()    {}
func (*GetOfficialAccountDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorOfficialAccount, []int{11}
}

func (m *GetOfficialAccountDetailResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetOfficialAccountDetailResp) GetDetail() *ga.OfficialAccountInfo {
	if m != nil {
		return m.Detail
	}
	return nil
}

func init() {
	proto.RegisterType((*InAppNavigationCommand)(nil), "ga.InAppNavigationCommand")
	proto.RegisterType((*SystemBrowserCommand)(nil), "ga.SystemBrowserCommand")
	proto.RegisterType((*GameDownloadCommand)(nil), "ga.GameDownloadCommand")
	proto.RegisterType((*OfficialAccountSettings)(nil), "ga.OfficialAccountSettings")
	proto.RegisterType((*OfficialAccountUpdateSettingsReq)(nil), "ga.OfficialAccountUpdateSettingsReq")
	proto.RegisterType((*OfficialAccountUpdateSettingsResp)(nil), "ga.OfficialAccountUpdateSettingsResp")
	proto.RegisterType((*OfficialAccountGetNewestMessageReq)(nil), "ga.OfficialAccountGetNewestMessageReq")
	proto.RegisterType((*OfficialAccountGetNewestMessageResp)(nil), "ga.OfficialAccountGetNewestMessageResp")
	proto.RegisterType((*UnsubscribeOfficialAccountReq)(nil), "ga.unsubscribeOfficialAccountReq")
	proto.RegisterType((*UnsubscribeOfficialAccountResp)(nil), "ga.unsubscribeOfficialAccountResp")
	proto.RegisterType((*GetOfficialAccountDetailReq)(nil), "ga.GetOfficialAccountDetailReq")
	proto.RegisterType((*GetOfficialAccountDetailResp)(nil), "ga.GetOfficialAccountDetailResp")
	proto.RegisterEnum("ga.OfficialAccountCmd", OfficialAccountCmd_name, OfficialAccountCmd_value)
	proto.RegisterEnum("ga.OfficialAccountSettings_SettingConstants", OfficialAccountSettings_SettingConstants_name, OfficialAccountSettings_SettingConstants_value)
}
func (m *InAppNavigationCommand) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InAppNavigationCommand) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(len(m.Uri)))
	i += copy(dAtA[i:], m.Uri)
	return i, nil
}

func (m *SystemBrowserCommand) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SystemBrowserCommand) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	return i, nil
}

func (m *GameDownloadCommand) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameDownloadCommand) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(len(m.GameUrl)))
	i += copy(dAtA[i:], m.GameUrl)
	dAtA[i] = 0x10
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(len(m.GamePkg)))
	i += copy(dAtA[i:], m.GamePkg)
	return i, nil
}

func (m *OfficialAccountSettings) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfficialAccountSettings) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(m.MessageNotification))
	return i, nil
}

func (m *OfficialAccountUpdateSettingsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfficialAccountUpdateSettingsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(m.OfficialAccountId))
	if m.Settings == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("settings")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.Settings.Size()))
		n2, err := m.Settings.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *OfficialAccountUpdateSettingsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfficialAccountUpdateSettingsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.BaseResp.Size()))
		n3, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(m.OfficialAccountId))
	if m.UpdatedSettings == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("updated_settings")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.UpdatedSettings.Size()))
		n4, err := m.UpdatedSettings.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *OfficialAccountGetNewestMessageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfficialAccountGetNewestMessageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(m.OfficialAccountId))
	return i, nil
}

func (m *OfficialAccountGetNewestMessageResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfficialAccountGetNewestMessageResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(m.OfficialAccountId))
	dAtA[i] = 0x18
	i++
	if m.HasMessage {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *UnsubscribeOfficialAccountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnsubscribeOfficialAccountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(m.OfficialAccountId))
	return i, nil
}

func (m *UnsubscribeOfficialAccountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnsubscribeOfficialAccountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(m.OfficialAccountId))
	return i, nil
}

func (m *GetOfficialAccountDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfficialAccountDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintOfficialAccount(dAtA, i, uint64(m.OfficialAccountId))
	return i, nil
}

func (m *GetOfficialAccountDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOfficialAccountDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if m.Detail == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("detail")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintOfficialAccount(dAtA, i, uint64(m.Detail.Size()))
		n11, err := m.Detail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func encodeFixed64OfficialAccount(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32OfficialAccount(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintOfficialAccount(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *InAppNavigationCommand) Size() (n int) {
	var l int
	_ = l
	l = len(m.Uri)
	n += 1 + l + sovOfficialAccount(uint64(l))
	return n
}

func (m *SystemBrowserCommand) Size() (n int) {
	var l int
	_ = l
	l = len(m.Url)
	n += 1 + l + sovOfficialAccount(uint64(l))
	return n
}

func (m *GameDownloadCommand) Size() (n int) {
	var l int
	_ = l
	l = len(m.GameUrl)
	n += 1 + l + sovOfficialAccount(uint64(l))
	n += 1 + sovOfficialAccount(uint64(m.GameId))
	l = len(m.GameName)
	n += 1 + l + sovOfficialAccount(uint64(l))
	l = len(m.GamePkg)
	n += 1 + l + sovOfficialAccount(uint64(l))
	return n
}

func (m *OfficialAccountSettings) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovOfficialAccount(uint64(m.MessageNotification))
	return n
}

func (m *OfficialAccountUpdateSettingsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	n += 1 + sovOfficialAccount(uint64(m.OfficialAccountId))
	if m.Settings != nil {
		l = m.Settings.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	return n
}

func (m *OfficialAccountUpdateSettingsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	n += 1 + sovOfficialAccount(uint64(m.OfficialAccountId))
	if m.UpdatedSettings != nil {
		l = m.UpdatedSettings.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	return n
}

func (m *OfficialAccountGetNewestMessageReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	n += 1 + sovOfficialAccount(uint64(m.OfficialAccountId))
	return n
}

func (m *OfficialAccountGetNewestMessageResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	n += 1 + sovOfficialAccount(uint64(m.OfficialAccountId))
	n += 2
	return n
}

func (m *UnsubscribeOfficialAccountReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	n += 1 + sovOfficialAccount(uint64(m.OfficialAccountId))
	return n
}

func (m *UnsubscribeOfficialAccountResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	n += 1 + sovOfficialAccount(uint64(m.OfficialAccountId))
	return n
}

func (m *GetOfficialAccountDetailReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	n += 1 + sovOfficialAccount(uint64(m.OfficialAccountId))
	return n
}

func (m *GetOfficialAccountDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	if m.Detail != nil {
		l = m.Detail.Size()
		n += 1 + l + sovOfficialAccount(uint64(l))
	}
	return n
}

func sovOfficialAccount(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozOfficialAccount(x uint64) (n int) {
	return sovOfficialAccount(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *InAppNavigationCommand) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InAppNavigationCommand: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InAppNavigationCommand: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uri", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Uri = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uri")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SystemBrowserCommand) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SystemBrowserCommand: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SystemBrowserCommand: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameDownloadCommand) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameDownloadCommand: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameDownloadCommand: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GamePkg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GamePkg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfficialAccountSettings) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfficialAccountSettings: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfficialAccountSettings: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MessageNotification", wireType)
			}
			m.MessageNotification = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MessageNotification |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfficialAccountUpdateSettingsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfficialAccountUpdateSettingsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfficialAccountUpdateSettingsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficialAccountId", wireType)
			}
			m.OfficialAccountId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficialAccountId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Settings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Settings == nil {
				m.Settings = &OfficialAccountSettings{}
			}
			if err := m.Settings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("official_account_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("settings")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfficialAccountUpdateSettingsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfficialAccountUpdateSettingsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfficialAccountUpdateSettingsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficialAccountId", wireType)
			}
			m.OfficialAccountId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficialAccountId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdatedSettings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UpdatedSettings == nil {
				m.UpdatedSettings = &OfficialAccountSettings{}
			}
			if err := m.UpdatedSettings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("official_account_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("updated_settings")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfficialAccountGetNewestMessageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfficialAccountGetNewestMessageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfficialAccountGetNewestMessageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficialAccountId", wireType)
			}
			m.OfficialAccountId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficialAccountId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("official_account_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfficialAccountGetNewestMessageResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OfficialAccountGetNewestMessageResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OfficialAccountGetNewestMessageResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficialAccountId", wireType)
			}
			m.OfficialAccountId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficialAccountId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HasMessage", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasMessage = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("official_account_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("has_message")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnsubscribeOfficialAccountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: unsubscribeOfficialAccountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: unsubscribeOfficialAccountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficialAccountId", wireType)
			}
			m.OfficialAccountId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficialAccountId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("official_account_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnsubscribeOfficialAccountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: unsubscribeOfficialAccountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: unsubscribeOfficialAccountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficialAccountId", wireType)
			}
			m.OfficialAccountId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficialAccountId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("official_account_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfficialAccountDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfficialAccountDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfficialAccountDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfficialAccountId", wireType)
			}
			m.OfficialAccountId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfficialAccountId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("official_account_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOfficialAccountDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOfficialAccountDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOfficialAccountDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Detail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Detail == nil {
				m.Detail = &ga.OfficialAccountInfo{}
			}
			if err := m.Detail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipOfficialAccount(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOfficialAccount
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipOfficialAccount(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowOfficialAccount
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowOfficialAccount
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthOfficialAccount
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowOfficialAccount
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipOfficialAccount(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthOfficialAccount = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowOfficialAccount   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("official_account.proto", fileDescriptorOfficialAccount) }

var fileDescriptorOfficialAccount = []byte{
	// 721 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x95, 0x5f, 0x4f, 0xc3, 0x54,
	0x18, 0xc6, 0x39, 0x83, 0xc0, 0x78, 0x27, 0x5a, 0x0e, 0xff, 0x16, 0x90, 0x31, 0x6a, 0x34, 0xa8,
	0x71, 0x33, 0x8b, 0x86, 0xeb, 0x6e, 0xab, 0x4d, 0x93, 0xad, 0x5b, 0xda, 0x21, 0xc1, 0x9b, 0x93,
	0xb3, 0xf6, 0xac, 0x34, 0xb4, 0x3d, 0x65, 0xe7, 0x4c, 0x82, 0xc6, 0x18, 0xbd, 0xf2, 0x12, 0xbd,
	0xf3, 0x43, 0xf8, 0x01, 0xfc, 0x06, 0x5c, 0x78, 0xe1, 0x27, 0x30, 0x06, 0xbf, 0x88, 0x59, 0x57,
	0x10, 0x1a, 0x91, 0x60, 0x02, 0x77, 0xa7, 0xef, 0x79, 0xce, 0xfb, 0x3c, 0xe7, 0xf7, 0xb6, 0x29,
	0x6c, 0xf2, 0xd1, 0x28, 0x70, 0x03, 0x1a, 0x12, 0xea, 0xba, 0x7c, 0x12, 0xcb, 0x5a, 0x32, 0xe6,
	0x92, 0xe3, 0x82, 0x4f, 0xb7, 0x57, 0x7c, 0x4a, 0x86, 0x54, 0xb0, 0x59, 0x49, 0xfd, 0x18, 0x36,
	0xcd, 0x58, 0x4b, 0x12, 0x8b, 0x7e, 0x19, 0xf8, 0x54, 0x06, 0x3c, 0x6e, 0xf1, 0x28, 0xa2, 0xb1,
	0x87, 0x37, 0x61, 0x7e, 0x32, 0x0e, 0xca, 0xa8, 0x5a, 0x38, 0x58, 0x6e, 0x2e, 0x5c, 0xff, 0xb1,
	0x37, 0x67, 0x4f, 0x0b, 0x6a, 0x0d, 0xd6, 0x9d, 0x4b, 0x21, 0x59, 0xd4, 0x1c, 0xf3, 0x0b, 0xc1,
	0xc6, 0x0f, 0xf4, 0x61, 0x5e, 0x1f, 0xaa, 0x3f, 0x23, 0x58, 0x33, 0x68, 0xc4, 0xda, 0xfc, 0x22,
	0x0e, 0x39, 0xf5, 0x6e, 0xf5, 0x7b, 0x50, 0xf4, 0x69, 0xc4, 0x48, 0xfe, 0xd0, 0xd2, 0xb4, 0x7a,
	0x34, 0x0e, 0xf1, 0x2e, 0xa4, 0x4b, 0x12, 0x78, 0xe5, 0x42, 0xb5, 0x70, 0xb0, 0x92, 0xed, 0x2f,
	0x4e, 0x8b, 0xa6, 0x87, 0xf7, 0x61, 0x39, 0xdd, 0x8e, 0x69, 0xc4, 0xca, 0xf3, 0xf7, 0x1a, 0xa4,
	0x6d, 0x2d, 0x1a, 0xb1, 0x3b, 0x8b, 0xe4, 0xcc, 0x2f, 0x2f, 0x54, 0xd1, 0x43, 0x8b, 0xfe, 0x99,
	0xaf, 0x7e, 0x0b, 0x5b, 0xbd, 0x0c, 0x95, 0x36, 0x23, 0xe5, 0x30, 0x29, 0x83, 0xd8, 0x17, 0xf8,
	0x10, 0xd6, 0x23, 0x26, 0x04, 0xf5, 0x19, 0x89, 0xb9, 0x0c, 0x46, 0x81, 0x9b, 0xd2, 0x29, 0xa3,
	0x2a, 0xba, 0x8b, 0xb2, 0x96, 0x29, 0xac, 0x7b, 0x02, 0xf5, 0x43, 0x50, 0xb2, 0x26, 0x2d, 0x1e,
	0x0b, 0x49, 0x63, 0x29, 0x30, 0xc0, 0xa2, 0x6e, 0x69, 0xcd, 0x8e, 0xae, 0x20, 0x5c, 0x82, 0xa5,
	0xb6, 0xe9, 0xa4, 0x0f, 0x05, 0xf5, 0x57, 0x04, 0xd5, 0x5c, 0x82, 0xa3, 0xc4, 0xa3, 0x92, 0xdd,
	0xe6, 0xb0, 0xd9, 0x39, 0x7e, 0x0f, 0x8a, 0xd3, 0x89, 0x91, 0x31, 0x3b, 0x4f, 0x49, 0x95, 0x1a,
	0xa5, 0x9a, 0x4f, 0x6b, 0x4d, 0x2a, 0x98, 0xcd, 0xce, 0xed, 0xa5, 0xe1, 0x6c, 0x81, 0x3f, 0x81,
	0xb5, 0xfc, 0xe0, 0xf3, 0xf0, 0x56, 0xf9, 0x43, 0x33, 0xd3, 0xc3, 0x87, 0x50, 0x14, 0x99, 0x59,
	0x8a, 0xb1, 0xd4, 0xd8, 0x99, 0x76, 0x7f, 0x84, 0x8b, 0x7d, 0x27, 0x56, 0x7f, 0x43, 0xb0, 0xff,
	0x44, 0x76, 0x91, 0xe0, 0xf7, 0x61, 0x39, 0x0b, 0x2f, 0x92, 0x2c, 0xfd, 0x1b, 0xff, 0xa4, 0x17,
	0x89, 0x5d, 0x1c, 0x66, 0xab, 0xff, 0x99, 0xff, 0x33, 0x50, 0x26, 0xa9, 0xad, 0x47, 0x9e, 0x73,
	0x8f, 0xb7, 0xb2, 0x43, 0xb7, 0x05, 0xf5, 0x7b, 0x04, 0x6a, 0x4e, 0x6c, 0x30, 0x69, 0xb1, 0x0b,
	0x26, 0x64, 0x77, 0x36, 0xe7, 0x17, 0x1f, 0x86, 0xfa, 0x0b, 0x82, 0x77, 0x9e, 0x0c, 0xf1, 0x1a,
	0x54, 0xdf, 0x85, 0xd2, 0x29, 0x15, 0x24, 0x7b, 0xc1, 0x53, 0xa0, 0xc5, 0x4c, 0x0d, 0xa7, 0x54,
	0x64, 0x59, 0xd4, 0x6f, 0x60, 0x77, 0x12, 0x8b, 0xc9, 0x50, 0xb8, 0xe3, 0x60, 0xc8, 0x72, 0xc9,
	0x5f, 0x1e, 0xd7, 0x77, 0x08, 0x2a, 0xff, 0xe5, 0xff, 0x0a, 0xa4, 0xd4, 0xaf, 0x61, 0xc7, 0x60,
	0x32, 0x67, 0xdd, 0x66, 0x92, 0x06, 0xe1, 0xcb, 0x03, 0xf8, 0x0a, 0xde, 0x7e, 0xdc, 0xfc, 0x79,
	0xb7, 0xaf, 0xc3, 0xa2, 0x97, 0x1e, 0x4c, 0x3d, 0x4b, 0x8d, 0xad, 0x7f, 0xf9, 0x7a, 0xcc, 0x78,
	0xc4, 0xed, 0x4c, 0xf6, 0xc1, 0x0f, 0x08, 0x70, 0x6e, 0xbf, 0x15, 0x79, 0x78, 0x03, 0x56, 0x4d,
	0x8b, 0x68, 0xfd, 0x3e, 0xb1, 0xb4, 0xcf, 0x4d, 0x43, 0x1b, 0x98, 0x3d, 0x4b, 0x41, 0x18, 0xc3,
	0x9b, 0xce, 0x89, 0x33, 0xd0, 0xbb, 0xa4, 0x69, 0xf7, 0x8e, 0x1d, 0xdd, 0x56, 0x0a, 0x78, 0x1b,
	0x36, 0x9c, 0xbe, 0xde, 0x32, 0xb5, 0x0e, 0x69, 0xf5, 0xba, 0x5d, 0xcd, 0x6a, 0x93, 0xa6, 0x6e,
	0x98, 0x96, 0x72, 0x65, 0x61, 0x0c, 0x2b, 0x86, 0xd6, 0xd5, 0x49, 0xbb, 0x77, 0x6c, 0x75, 0x7a,
	0x5a, 0x5b, 0xf9, 0xd1, 0xc2, 0x5b, 0x80, 0x0d, 0x7d, 0x40, 0x3a, 0xda, 0x40, 0x77, 0x06, 0xa4,
	0xab, 0x3b, 0x8e, 0x66, 0xe8, 0xca, 0x4f, 0x56, 0xf3, 0xe4, 0xfa, 0xa6, 0x82, 0x7e, 0xbf, 0xa9,
	0xa0, 0x3f, 0x6f, 0x2a, 0xe8, 0xea, 0xaf, 0xca, 0x1c, 0x94, 0x5d, 0x1e, 0xd5, 0x2e, 0x83, 0x4b,
	0x3e, 0x99, 0x5e, 0x23, 0xe2, 0x1e, 0x0b, 0x67, 0x7f, 0xbc, 0x2f, 0x3e, 0xf2, 0x79, 0x48, 0x63,
	0xbf, 0xf6, 0x69, 0x43, 0xca, 0x9a, 0xcb, 0xa3, 0x7a, 0x5a, 0x76, 0x79, 0x58, 0xa7, 0x49, 0x52,
	0xcf, 0xcf, 0xe0, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0x36, 0x47, 0xa7, 0xf5, 0x4c, 0x07, 0x00,
	0x00,
}
