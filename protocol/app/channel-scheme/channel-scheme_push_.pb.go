// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-scheme_push_.proto

package channel_scheme // import "golang.52tt.com/protocol/app/channel-scheme"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 切换玩法相关推送
type ChannelSchemeSwitchEvent struct {
	Cid                    uint32             `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	SchemeInfo             *ChannelSchemeInfo `protobuf:"bytes,2,opt,name=scheme_info,json=schemeInfo,proto3" json:"scheme_info,omitempty"`
	NeedExitChannel        bool               `protobuf:"varint,3,opt,name=need_exit_channel,json=needExitChannel,proto3" json:"need_exit_channel,omitempty"`
	ExitChannelText        string             `protobuf:"bytes,4,opt,name=exit_channel_text,json=exitChannelText,proto3" json:"exit_channel_text,omitempty"`
	NotNeedExitList        []uint32           `protobuf:"varint,5,rep,packed,name=not_need_exit_list,json=notNeedExitList,proto3" json:"not_need_exit_list,omitempty"`
	NeedRecheckExitChannel bool               `protobuf:"varint,6,opt,name=need_recheck_exit_channel,json=needRecheckExitChannel,proto3" json:"need_recheck_exit_channel,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}           `json:"-"`
	XXX_unrecognized       []byte             `json:"-"`
	XXX_sizecache          int32              `json:"-"`
}

func (m *ChannelSchemeSwitchEvent) Reset()         { *m = ChannelSchemeSwitchEvent{} }
func (m *ChannelSchemeSwitchEvent) String() string { return proto.CompactTextString(m) }
func (*ChannelSchemeSwitchEvent) ProtoMessage()    {}
func (*ChannelSchemeSwitchEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_push__31331956ea70ca28, []int{0}
}
func (m *ChannelSchemeSwitchEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSchemeSwitchEvent.Unmarshal(m, b)
}
func (m *ChannelSchemeSwitchEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSchemeSwitchEvent.Marshal(b, m, deterministic)
}
func (dst *ChannelSchemeSwitchEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSchemeSwitchEvent.Merge(dst, src)
}
func (m *ChannelSchemeSwitchEvent) XXX_Size() int {
	return xxx_messageInfo_ChannelSchemeSwitchEvent.Size(m)
}
func (m *ChannelSchemeSwitchEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSchemeSwitchEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSchemeSwitchEvent proto.InternalMessageInfo

func (m *ChannelSchemeSwitchEvent) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChannelSchemeSwitchEvent) GetSchemeInfo() *ChannelSchemeInfo {
	if m != nil {
		return m.SchemeInfo
	}
	return nil
}

func (m *ChannelSchemeSwitchEvent) GetNeedExitChannel() bool {
	if m != nil {
		return m.NeedExitChannel
	}
	return false
}

func (m *ChannelSchemeSwitchEvent) GetExitChannelText() string {
	if m != nil {
		return m.ExitChannelText
	}
	return ""
}

func (m *ChannelSchemeSwitchEvent) GetNotNeedExitList() []uint32 {
	if m != nil {
		return m.NotNeedExitList
	}
	return nil
}

func (m *ChannelSchemeSwitchEvent) GetNeedRecheckExitChannel() bool {
	if m != nil {
		return m.NeedRecheckExitChannel
	}
	return false
}

func init() {
	proto.RegisterType((*ChannelSchemeSwitchEvent)(nil), "ga.ChannelSchemeSwitchEvent")
}

func init() {
	proto.RegisterFile("channel-scheme_push_.proto", fileDescriptor_channel_scheme_push__31331956ea70ca28)
}

var fileDescriptor_channel_scheme_push__31331956ea70ca28 = []byte{
	// 292 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x5c, 0x90, 0x4f, 0x4b, 0xf3, 0x40,
	0x10, 0xc6, 0x49, 0xfb, 0xbe, 0x45, 0xb7, 0x94, 0xea, 0x42, 0x65, 0xed, 0x29, 0x78, 0x0a, 0x16,
	0xb7, 0x50, 0x51, 0xf0, 0xaa, 0x14, 0x11, 0xc4, 0xc3, 0xd6, 0x93, 0x97, 0x25, 0x6e, 0xa6, 0xc9,
	0x62, 0xb2, 0x13, 0x9a, 0xa9, 0xa6, 0xdf, 0xc7, 0x0f, 0x2a, 0xf9, 0x83, 0x6d, 0xbc, 0x0d, 0xfb,
	0xfb, 0xed, 0xc3, 0x33, 0xc3, 0xa6, 0x26, 0x09, 0x9d, 0x83, 0xf4, 0xaa, 0x30, 0x09, 0x64, 0xa0,
	0xf3, 0x6d, 0x91, 0x68, 0x99, 0x6f, 0x90, 0x90, 0xf7, 0xe2, 0x70, 0x3a, 0xf9, 0xc3, 0x1b, 0x74,
	0xf1, 0xdd, 0x63, 0xe2, 0xa1, 0x21, 0xab, 0x1a, 0xac, 0xbe, 0x2c, 0x99, 0x64, 0xf9, 0x09, 0x8e,
	0xf8, 0x09, 0xeb, 0x1b, 0x1b, 0x09, 0xcf, 0xf7, 0x82, 0x91, 0xaa, 0x46, 0x7e, 0xcb, 0x86, 0xed,
	0x7f, 0xeb, 0xd6, 0x28, 0x7a, 0xbe, 0x17, 0x0c, 0x17, 0x13, 0x19, 0x87, 0xb2, 0x13, 0xf2, 0xe4,
	0xd6, 0xa8, 0x58, 0xf1, 0x3b, 0xf3, 0x4b, 0x76, 0xea, 0x00, 0x22, 0x0d, 0xa5, 0x25, 0xdd, 0x36,
	0x11, 0x7d, 0xdf, 0x0b, 0x8e, 0xd4, 0xb8, 0x02, 0xcb, 0xd2, 0x52, 0x9b, 0x50, 0xb9, 0x87, 0x9a,
	0x26, 0x28, 0x49, 0xfc, 0xf3, 0xbd, 0xe0, 0x58, 0x8d, 0x61, 0xef, 0xbd, 0x42, 0x49, 0x7c, 0xc6,
	0xb8, 0x43, 0xd2, 0xfb, 0xec, 0xd4, 0x16, 0x24, 0xfe, 0xfb, 0xfd, 0x60, 0xa4, 0xc6, 0x0e, 0xe9,
	0xa5, 0xcd, 0x7e, 0xb6, 0x05, 0xf1, 0x3b, 0x76, 0x5e, 0x8b, 0x1b, 0x30, 0x09, 0x98, 0x8f, 0x6e,
	0x99, 0x41, 0x5d, 0xe6, 0xac, 0x12, 0x54, 0xc3, 0x0f, 0x3a, 0xdd, 0x3f, 0x32, 0x61, 0x30, 0x93,
	0x3b, 0xbb, 0xc3, 0x6d, 0xb5, 0x6d, 0x86, 0x11, 0xa4, 0xcd, 0x09, 0xdf, 0x66, 0x31, 0xa6, 0xa1,
	0x8b, 0xe5, 0xcd, 0x82, 0x48, 0x1a, 0xcc, 0xe6, 0xf5, 0xb3, 0xc1, 0x74, 0x1e, 0xe6, 0xf9, 0xbc,
	0x7b, 0xf6, 0xf7, 0x41, 0x0d, 0xaf, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x78, 0x0d, 0xd2, 0xd3,
	0xaf, 0x01, 0x00, 0x00,
}
