// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel_game_player_logic_.proto

package channel_game_player // import "golang.52tt.com/protocol/app/channel-game-player"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 游戏加载完，登录获取openid和code
type GetOpenidGamePlayerReq struct {
	BaseReq              *app.BaseResp `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32        `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetOpenidGamePlayerReq) Reset()         { *m = GetOpenidGamePlayerReq{} }
func (m *GetOpenidGamePlayerReq) String() string { return proto.CompactTextString(m) }
func (*GetOpenidGamePlayerReq) ProtoMessage()    {}
func (*GetOpenidGamePlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{0}
}
func (m *GetOpenidGamePlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenidGamePlayerReq.Unmarshal(m, b)
}
func (m *GetOpenidGamePlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenidGamePlayerReq.Marshal(b, m, deterministic)
}
func (dst *GetOpenidGamePlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenidGamePlayerReq.Merge(dst, src)
}
func (m *GetOpenidGamePlayerReq) XXX_Size() int {
	return xxx_messageInfo_GetOpenidGamePlayerReq.Size(m)
}
func (m *GetOpenidGamePlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenidGamePlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenidGamePlayerReq proto.InternalMessageInfo

func (m *GetOpenidGamePlayerReq) GetBaseReq() *app.BaseResp {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetOpenidGamePlayerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetOpenidGamePlayerReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetOpenidGamePlayerResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OpenId               string        `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	Code                 int32         `protobuf:"varint,3,opt,name=code,proto3" json:"code,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetOpenidGamePlayerResp) Reset()         { *m = GetOpenidGamePlayerResp{} }
func (m *GetOpenidGamePlayerResp) String() string { return proto.CompactTextString(m) }
func (*GetOpenidGamePlayerResp) ProtoMessage()    {}
func (*GetOpenidGamePlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{1}
}
func (m *GetOpenidGamePlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOpenidGamePlayerResp.Unmarshal(m, b)
}
func (m *GetOpenidGamePlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOpenidGamePlayerResp.Marshal(b, m, deterministic)
}
func (dst *GetOpenidGamePlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOpenidGamePlayerResp.Merge(dst, src)
}
func (m *GetOpenidGamePlayerResp) XXX_Size() int {
	return xxx_messageInfo_GetOpenidGamePlayerResp.Size(m)
}
func (m *GetOpenidGamePlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOpenidGamePlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOpenidGamePlayerResp proto.InternalMessageInfo

func (m *GetOpenidGamePlayerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetOpenidGamePlayerResp) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GetOpenidGamePlayerResp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

// 加入游戏
type JoinChannelGamePlayerReq struct {
	BaseReq              *app.BaseResp `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32        `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	PermissionsCode      uint32        `protobuf:"varint,4,opt,name=permissions_code,json=permissionsCode,proto3" json:"permissions_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *JoinChannelGamePlayerReq) Reset()         { *m = JoinChannelGamePlayerReq{} }
func (m *JoinChannelGamePlayerReq) String() string { return proto.CompactTextString(m) }
func (*JoinChannelGamePlayerReq) ProtoMessage()    {}
func (*JoinChannelGamePlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{2}
}
func (m *JoinChannelGamePlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelGamePlayerReq.Unmarshal(m, b)
}
func (m *JoinChannelGamePlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelGamePlayerReq.Marshal(b, m, deterministic)
}
func (dst *JoinChannelGamePlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelGamePlayerReq.Merge(dst, src)
}
func (m *JoinChannelGamePlayerReq) XXX_Size() int {
	return xxx_messageInfo_JoinChannelGamePlayerReq.Size(m)
}
func (m *JoinChannelGamePlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelGamePlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelGamePlayerReq proto.InternalMessageInfo

func (m *JoinChannelGamePlayerReq) GetBaseReq() *app.BaseResp {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinChannelGamePlayerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinChannelGamePlayerReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *JoinChannelGamePlayerReq) GetPermissionsCode() uint32 {
	if m != nil {
		return m.PermissionsCode
	}
	return 0
}

type JoinChannelGamePlayerResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *JoinChannelGamePlayerResp) Reset()         { *m = JoinChannelGamePlayerResp{} }
func (m *JoinChannelGamePlayerResp) String() string { return proto.CompactTextString(m) }
func (*JoinChannelGamePlayerResp) ProtoMessage()    {}
func (*JoinChannelGamePlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{3}
}
func (m *JoinChannelGamePlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelGamePlayerResp.Unmarshal(m, b)
}
func (m *JoinChannelGamePlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelGamePlayerResp.Marshal(b, m, deterministic)
}
func (dst *JoinChannelGamePlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelGamePlayerResp.Merge(dst, src)
}
func (m *JoinChannelGamePlayerResp) XXX_Size() int {
	return xxx_messageInfo_JoinChannelGamePlayerResp.Size(m)
}
func (m *JoinChannelGamePlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelGamePlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelGamePlayerResp proto.InternalMessageInfo

func (m *JoinChannelGamePlayerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 取消加入
type QuitChannelGamePlayerReq struct {
	BaseReq              *app.BaseResp `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32        `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QuitChannelGamePlayerReq) Reset()         { *m = QuitChannelGamePlayerReq{} }
func (m *QuitChannelGamePlayerReq) String() string { return proto.CompactTextString(m) }
func (*QuitChannelGamePlayerReq) ProtoMessage()    {}
func (*QuitChannelGamePlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{4}
}
func (m *QuitChannelGamePlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelGamePlayerReq.Unmarshal(m, b)
}
func (m *QuitChannelGamePlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelGamePlayerReq.Marshal(b, m, deterministic)
}
func (dst *QuitChannelGamePlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelGamePlayerReq.Merge(dst, src)
}
func (m *QuitChannelGamePlayerReq) XXX_Size() int {
	return xxx_messageInfo_QuitChannelGamePlayerReq.Size(m)
}
func (m *QuitChannelGamePlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelGamePlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelGamePlayerReq proto.InternalMessageInfo

func (m *QuitChannelGamePlayerReq) GetBaseReq() *app.BaseResp {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *QuitChannelGamePlayerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QuitChannelGamePlayerReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type QuitChannelGamePlayerResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QuitChannelGamePlayerResp) Reset()         { *m = QuitChannelGamePlayerResp{} }
func (m *QuitChannelGamePlayerResp) String() string { return proto.CompactTextString(m) }
func (*QuitChannelGamePlayerResp) ProtoMessage()    {}
func (*QuitChannelGamePlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{5}
}
func (m *QuitChannelGamePlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelGamePlayerResp.Unmarshal(m, b)
}
func (m *QuitChannelGamePlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelGamePlayerResp.Marshal(b, m, deterministic)
}
func (dst *QuitChannelGamePlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelGamePlayerResp.Merge(dst, src)
}
func (m *QuitChannelGamePlayerResp) XXX_Size() int {
	return xxx_messageInfo_QuitChannelGamePlayerResp.Size(m)
}
func (m *QuitChannelGamePlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelGamePlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelGamePlayerResp proto.InternalMessageInfo

func (m *QuitChannelGamePlayerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 准备游戏
type ReadyChannelGamePlayerReq struct {
	BaseReq              *app.BaseResp `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32        `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReadyChannelGamePlayerReq) Reset()         { *m = ReadyChannelGamePlayerReq{} }
func (m *ReadyChannelGamePlayerReq) String() string { return proto.CompactTextString(m) }
func (*ReadyChannelGamePlayerReq) ProtoMessage()    {}
func (*ReadyChannelGamePlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{6}
}
func (m *ReadyChannelGamePlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyChannelGamePlayerReq.Unmarshal(m, b)
}
func (m *ReadyChannelGamePlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyChannelGamePlayerReq.Marshal(b, m, deterministic)
}
func (dst *ReadyChannelGamePlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyChannelGamePlayerReq.Merge(dst, src)
}
func (m *ReadyChannelGamePlayerReq) XXX_Size() int {
	return xxx_messageInfo_ReadyChannelGamePlayerReq.Size(m)
}
func (m *ReadyChannelGamePlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyChannelGamePlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyChannelGamePlayerReq proto.InternalMessageInfo

func (m *ReadyChannelGamePlayerReq) GetBaseReq() *app.BaseResp {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReadyChannelGamePlayerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReadyChannelGamePlayerReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ReadyChannelGamePlayerResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReadyChannelGamePlayerResp) Reset()         { *m = ReadyChannelGamePlayerResp{} }
func (m *ReadyChannelGamePlayerResp) String() string { return proto.CompactTextString(m) }
func (*ReadyChannelGamePlayerResp) ProtoMessage()    {}
func (*ReadyChannelGamePlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{7}
}
func (m *ReadyChannelGamePlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyChannelGamePlayerResp.Unmarshal(m, b)
}
func (m *ReadyChannelGamePlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyChannelGamePlayerResp.Marshal(b, m, deterministic)
}
func (dst *ReadyChannelGamePlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyChannelGamePlayerResp.Merge(dst, src)
}
func (m *ReadyChannelGamePlayerResp) XXX_Size() int {
	return xxx_messageInfo_ReadyChannelGamePlayerResp.Size(m)
}
func (m *ReadyChannelGamePlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyChannelGamePlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyChannelGamePlayerResp proto.InternalMessageInfo

func (m *ReadyChannelGamePlayerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 取消准备
type UnReadyChannelGamePlayerReq struct {
	BaseReq              *app.BaseResp `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32        `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UnReadyChannelGamePlayerReq) Reset()         { *m = UnReadyChannelGamePlayerReq{} }
func (m *UnReadyChannelGamePlayerReq) String() string { return proto.CompactTextString(m) }
func (*UnReadyChannelGamePlayerReq) ProtoMessage()    {}
func (*UnReadyChannelGamePlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{8}
}
func (m *UnReadyChannelGamePlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnReadyChannelGamePlayerReq.Unmarshal(m, b)
}
func (m *UnReadyChannelGamePlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnReadyChannelGamePlayerReq.Marshal(b, m, deterministic)
}
func (dst *UnReadyChannelGamePlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnReadyChannelGamePlayerReq.Merge(dst, src)
}
func (m *UnReadyChannelGamePlayerReq) XXX_Size() int {
	return xxx_messageInfo_UnReadyChannelGamePlayerReq.Size(m)
}
func (m *UnReadyChannelGamePlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnReadyChannelGamePlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnReadyChannelGamePlayerReq proto.InternalMessageInfo

func (m *UnReadyChannelGamePlayerReq) GetBaseReq() *app.BaseResp {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UnReadyChannelGamePlayerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UnReadyChannelGamePlayerReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type UnReadyChannelGamePlayerResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UnReadyChannelGamePlayerResp) Reset()         { *m = UnReadyChannelGamePlayerResp{} }
func (m *UnReadyChannelGamePlayerResp) String() string { return proto.CompactTextString(m) }
func (*UnReadyChannelGamePlayerResp) ProtoMessage()    {}
func (*UnReadyChannelGamePlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{9}
}
func (m *UnReadyChannelGamePlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnReadyChannelGamePlayerResp.Unmarshal(m, b)
}
func (m *UnReadyChannelGamePlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnReadyChannelGamePlayerResp.Marshal(b, m, deterministic)
}
func (dst *UnReadyChannelGamePlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnReadyChannelGamePlayerResp.Merge(dst, src)
}
func (m *UnReadyChannelGamePlayerResp) XXX_Size() int {
	return xxx_messageInfo_UnReadyChannelGamePlayerResp.Size(m)
}
func (m *UnReadyChannelGamePlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnReadyChannelGamePlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnReadyChannelGamePlayerResp proto.InternalMessageInfo

func (m *UnReadyChannelGamePlayerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 更改权限值
type SetChannelGamePlayerPermissionCodeReq struct {
	BaseReq              *app.BaseResp `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32        `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	PermissionUid        uint32        `protobuf:"varint,4,opt,name=permission_uid,json=permissionUid,proto3" json:"permission_uid,omitempty"`
	PermissionCode       uint32        `protobuf:"varint,5,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChannelGamePlayerPermissionCodeReq) Reset()         { *m = SetChannelGamePlayerPermissionCodeReq{} }
func (m *SetChannelGamePlayerPermissionCodeReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGamePlayerPermissionCodeReq) ProtoMessage()    {}
func (*SetChannelGamePlayerPermissionCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{10}
}
func (m *SetChannelGamePlayerPermissionCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.Unmarshal(m, b)
}
func (m *SetChannelGamePlayerPermissionCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGamePlayerPermissionCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.Merge(dst, src)
}
func (m *SetChannelGamePlayerPermissionCodeReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.Size(m)
}
func (m *SetChannelGamePlayerPermissionCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGamePlayerPermissionCodeReq proto.InternalMessageInfo

func (m *SetChannelGamePlayerPermissionCodeReq) GetBaseReq() *app.BaseResp {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetChannelGamePlayerPermissionCodeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGamePlayerPermissionCodeReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGamePlayerPermissionCodeReq) GetPermissionUid() uint32 {
	if m != nil {
		return m.PermissionUid
	}
	return 0
}

func (m *SetChannelGamePlayerPermissionCodeReq) GetPermissionCode() uint32 {
	if m != nil {
		return m.PermissionCode
	}
	return 0
}

type SetChannelGamePlayerPermissionCodeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChannelGamePlayerPermissionCodeResp) Reset() {
	*m = SetChannelGamePlayerPermissionCodeResp{}
}
func (m *SetChannelGamePlayerPermissionCodeResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGamePlayerPermissionCodeResp) ProtoMessage()    {}
func (*SetChannelGamePlayerPermissionCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{11}
}
func (m *SetChannelGamePlayerPermissionCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.Unmarshal(m, b)
}
func (m *SetChannelGamePlayerPermissionCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGamePlayerPermissionCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.Merge(dst, src)
}
func (m *SetChannelGamePlayerPermissionCodeResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.Size(m)
}
func (m *SetChannelGamePlayerPermissionCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGamePlayerPermissionCodeResp proto.InternalMessageInfo

func (m *SetChannelGamePlayerPermissionCodeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 开始游戏
type StartChannelGamePlayerReq struct {
	BaseReq              *app.BaseResp `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32        `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartChannelGamePlayerReq) Reset()         { *m = StartChannelGamePlayerReq{} }
func (m *StartChannelGamePlayerReq) String() string { return proto.CompactTextString(m) }
func (*StartChannelGamePlayerReq) ProtoMessage()    {}
func (*StartChannelGamePlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{12}
}
func (m *StartChannelGamePlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChannelGamePlayerReq.Unmarshal(m, b)
}
func (m *StartChannelGamePlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChannelGamePlayerReq.Marshal(b, m, deterministic)
}
func (dst *StartChannelGamePlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChannelGamePlayerReq.Merge(dst, src)
}
func (m *StartChannelGamePlayerReq) XXX_Size() int {
	return xxx_messageInfo_StartChannelGamePlayerReq.Size(m)
}
func (m *StartChannelGamePlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChannelGamePlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartChannelGamePlayerReq proto.InternalMessageInfo

func (m *StartChannelGamePlayerReq) GetBaseReq() *app.BaseResp {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartChannelGamePlayerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartChannelGamePlayerReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type StartChannelGamePlayerResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartChannelGamePlayerResp) Reset()         { *m = StartChannelGamePlayerResp{} }
func (m *StartChannelGamePlayerResp) String() string { return proto.CompactTextString(m) }
func (*StartChannelGamePlayerResp) ProtoMessage()    {}
func (*StartChannelGamePlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{13}
}
func (m *StartChannelGamePlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChannelGamePlayerResp.Unmarshal(m, b)
}
func (m *StartChannelGamePlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChannelGamePlayerResp.Marshal(b, m, deterministic)
}
func (dst *StartChannelGamePlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChannelGamePlayerResp.Merge(dst, src)
}
func (m *StartChannelGamePlayerResp) XXX_Size() int {
	return xxx_messageInfo_StartChannelGamePlayerResp.Size(m)
}
func (m *StartChannelGamePlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChannelGamePlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartChannelGamePlayerResp proto.InternalMessageInfo

func (m *StartChannelGamePlayerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 退出游戏
type ExitChannelGamePlayerReq struct {
	BaseReq              *app.BaseResp `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32        `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ExitChannelGamePlayerReq) Reset()         { *m = ExitChannelGamePlayerReq{} }
func (m *ExitChannelGamePlayerReq) String() string { return proto.CompactTextString(m) }
func (*ExitChannelGamePlayerReq) ProtoMessage()    {}
func (*ExitChannelGamePlayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{14}
}
func (m *ExitChannelGamePlayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelGamePlayerReq.Unmarshal(m, b)
}
func (m *ExitChannelGamePlayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelGamePlayerReq.Marshal(b, m, deterministic)
}
func (dst *ExitChannelGamePlayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelGamePlayerReq.Merge(dst, src)
}
func (m *ExitChannelGamePlayerReq) XXX_Size() int {
	return xxx_messageInfo_ExitChannelGamePlayerReq.Size(m)
}
func (m *ExitChannelGamePlayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelGamePlayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelGamePlayerReq proto.InternalMessageInfo

func (m *ExitChannelGamePlayerReq) GetBaseReq() *app.BaseResp {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ExitChannelGamePlayerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ExitChannelGamePlayerReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ExitChannelGamePlayerResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ExitChannelGamePlayerResp) Reset()         { *m = ExitChannelGamePlayerResp{} }
func (m *ExitChannelGamePlayerResp) String() string { return proto.CompactTextString(m) }
func (*ExitChannelGamePlayerResp) ProtoMessage()    {}
func (*ExitChannelGamePlayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{15}
}
func (m *ExitChannelGamePlayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelGamePlayerResp.Unmarshal(m, b)
}
func (m *ExitChannelGamePlayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelGamePlayerResp.Marshal(b, m, deterministic)
}
func (dst *ExitChannelGamePlayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelGamePlayerResp.Merge(dst, src)
}
func (m *ExitChannelGamePlayerResp) XXX_Size() int {
	return xxx_messageInfo_ExitChannelGamePlayerResp.Size(m)
}
func (m *ExitChannelGamePlayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelGamePlayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelGamePlayerResp proto.InternalMessageInfo

func (m *ExitChannelGamePlayerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// ---------------------------------------
// push结构 game change
// 游戏用户信息
type ChannelGamePlayerInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Username             string   `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Openid               string   `protobuf:"bytes,6,opt,name=openid,proto3" json:"openid,omitempty"`
	Seq                  uint32   `protobuf:"varint,7,opt,name=seq,proto3" json:"seq,omitempty"`
	PermissionCode       uint32   `protobuf:"varint,8,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	Face                 string   `protobuf:"bytes,9,opt,name=face,proto3" json:"face,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGamePlayerInfo) Reset()         { *m = ChannelGamePlayerInfo{} }
func (m *ChannelGamePlayerInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelGamePlayerInfo) ProtoMessage()    {}
func (*ChannelGamePlayerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{16}
}
func (m *ChannelGamePlayerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGamePlayerInfo.Unmarshal(m, b)
}
func (m *ChannelGamePlayerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGamePlayerInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelGamePlayerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGamePlayerInfo.Merge(dst, src)
}
func (m *ChannelGamePlayerInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelGamePlayerInfo.Size(m)
}
func (m *ChannelGamePlayerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGamePlayerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGamePlayerInfo proto.InternalMessageInfo

func (m *ChannelGamePlayerInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *ChannelGamePlayerInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelGamePlayerInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *ChannelGamePlayerInfo) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetPermissionCode() uint32 {
	if m != nil {
		return m.PermissionCode
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetFace() string {
	if m != nil {
		return m.Face
	}
	return ""
}

// 游戏模式信息
type ChannelGameModeInfo struct {
	ModeKey              string   `protobuf:"bytes,1,opt,name=mode_key,json=modeKey,proto3" json:"mode_key,omitempty"`
	GameParam            string   `protobuf:"bytes,2,opt,name=game_param,json=gameParam,proto3" json:"game_param,omitempty"`
	PlayerLimit          []uint32 `protobuf:"varint,3,rep,packed,name=player_limit,json=playerLimit,proto3" json:"player_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGameModeInfo) Reset()         { *m = ChannelGameModeInfo{} }
func (m *ChannelGameModeInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelGameModeInfo) ProtoMessage()    {}
func (*ChannelGameModeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{17}
}
func (m *ChannelGameModeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGameModeInfo.Unmarshal(m, b)
}
func (m *ChannelGameModeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGameModeInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelGameModeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGameModeInfo.Merge(dst, src)
}
func (m *ChannelGameModeInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelGameModeInfo.Size(m)
}
func (m *ChannelGameModeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGameModeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGameModeInfo proto.InternalMessageInfo

func (m *ChannelGameModeInfo) GetModeKey() string {
	if m != nil {
		return m.ModeKey
	}
	return ""
}

func (m *ChannelGameModeInfo) GetGameParam() string {
	if m != nil {
		return m.GameParam
	}
	return ""
}

func (m *ChannelGameModeInfo) GetPlayerLimit() []uint32 {
	if m != nil {
		return m.PlayerLimit
	}
	return nil
}

type ChannelGameInfoChange struct {
	GameId               uint32                   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameVersion          string                   `protobuf:"bytes,2,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	CpId                 uint32                   `protobuf:"varint,3,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	GameMemberCntLimit   uint32                   `protobuf:"varint,4,opt,name=game_member_cnt_limit,json=gameMemberCntLimit,proto3" json:"game_member_cnt_limit,omitempty"`
	GameMaster           uint32                   `protobuf:"varint,5,opt,name=game_master,json=gameMaster,proto3" json:"game_master,omitempty"`
	GameStatus           uint32                   `protobuf:"varint,6,opt,name=game_status,json=gameStatus,proto3" json:"game_status,omitempty"`
	GameMode             *ChannelGameModeInfo     `protobuf:"bytes,7,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	GamePlayers          []*ChannelGamePlayerInfo `protobuf:"bytes,8,rep,name=game_players,json=gamePlayers,proto3" json:"game_players,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ChannelGameInfoChange) Reset()         { *m = ChannelGameInfoChange{} }
func (m *ChannelGameInfoChange) String() string { return proto.CompactTextString(m) }
func (*ChannelGameInfoChange) ProtoMessage()    {}
func (*ChannelGameInfoChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_game_player_logic__18a1051d2c20da00, []int{18}
}
func (m *ChannelGameInfoChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGameInfoChange.Unmarshal(m, b)
}
func (m *ChannelGameInfoChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGameInfoChange.Marshal(b, m, deterministic)
}
func (dst *ChannelGameInfoChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGameInfoChange.Merge(dst, src)
}
func (m *ChannelGameInfoChange) XXX_Size() int {
	return xxx_messageInfo_ChannelGameInfoChange.Size(m)
}
func (m *ChannelGameInfoChange) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGameInfoChange.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGameInfoChange proto.InternalMessageInfo

func (m *ChannelGameInfoChange) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ChannelGameInfoChange) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

func (m *ChannelGameInfoChange) GetCpId() uint32 {
	if m != nil {
		return m.CpId
	}
	return 0
}

func (m *ChannelGameInfoChange) GetGameMemberCntLimit() uint32 {
	if m != nil {
		return m.GameMemberCntLimit
	}
	return 0
}

func (m *ChannelGameInfoChange) GetGameMaster() uint32 {
	if m != nil {
		return m.GameMaster
	}
	return 0
}

func (m *ChannelGameInfoChange) GetGameStatus() uint32 {
	if m != nil {
		return m.GameStatus
	}
	return 0
}

func (m *ChannelGameInfoChange) GetGameMode() *ChannelGameModeInfo {
	if m != nil {
		return m.GameMode
	}
	return nil
}

func (m *ChannelGameInfoChange) GetGamePlayers() []*ChannelGamePlayerInfo {
	if m != nil {
		return m.GamePlayers
	}
	return nil
}

func init() {
	proto.RegisterType((*GetOpenidGamePlayerReq)(nil), "ga.channel_game_player.GetOpenidGamePlayerReq")
	proto.RegisterType((*GetOpenidGamePlayerResp)(nil), "ga.channel_game_player.GetOpenidGamePlayerResp")
	proto.RegisterType((*JoinChannelGamePlayerReq)(nil), "ga.channel_game_player.JoinChannelGamePlayerReq")
	proto.RegisterType((*JoinChannelGamePlayerResp)(nil), "ga.channel_game_player.JoinChannelGamePlayerResp")
	proto.RegisterType((*QuitChannelGamePlayerReq)(nil), "ga.channel_game_player.QuitChannelGamePlayerReq")
	proto.RegisterType((*QuitChannelGamePlayerResp)(nil), "ga.channel_game_player.QuitChannelGamePlayerResp")
	proto.RegisterType((*ReadyChannelGamePlayerReq)(nil), "ga.channel_game_player.ReadyChannelGamePlayerReq")
	proto.RegisterType((*ReadyChannelGamePlayerResp)(nil), "ga.channel_game_player.ReadyChannelGamePlayerResp")
	proto.RegisterType((*UnReadyChannelGamePlayerReq)(nil), "ga.channel_game_player.UnReadyChannelGamePlayerReq")
	proto.RegisterType((*UnReadyChannelGamePlayerResp)(nil), "ga.channel_game_player.UnReadyChannelGamePlayerResp")
	proto.RegisterType((*SetChannelGamePlayerPermissionCodeReq)(nil), "ga.channel_game_player.SetChannelGamePlayerPermissionCodeReq")
	proto.RegisterType((*SetChannelGamePlayerPermissionCodeResp)(nil), "ga.channel_game_player.SetChannelGamePlayerPermissionCodeResp")
	proto.RegisterType((*StartChannelGamePlayerReq)(nil), "ga.channel_game_player.StartChannelGamePlayerReq")
	proto.RegisterType((*StartChannelGamePlayerResp)(nil), "ga.channel_game_player.StartChannelGamePlayerResp")
	proto.RegisterType((*ExitChannelGamePlayerReq)(nil), "ga.channel_game_player.ExitChannelGamePlayerReq")
	proto.RegisterType((*ExitChannelGamePlayerResp)(nil), "ga.channel_game_player.ExitChannelGamePlayerResp")
	proto.RegisterType((*ChannelGamePlayerInfo)(nil), "ga.channel_game_player.ChannelGamePlayerInfo")
	proto.RegisterType((*ChannelGameModeInfo)(nil), "ga.channel_game_player.ChannelGameModeInfo")
	proto.RegisterType((*ChannelGameInfoChange)(nil), "ga.channel_game_player.ChannelGameInfoChange")
}

func init() {
	proto.RegisterFile("channel_game_player_logic_.proto", fileDescriptor_channel_game_player_logic__18a1051d2c20da00)
}

var fileDescriptor_channel_game_player_logic__18a1051d2c20da00 = []byte{
	// 749 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x56, 0xdf, 0x4e, 0xdb, 0x3e,
	0x14, 0x56, 0x68, 0x69, 0x9b, 0x53, 0xfa, 0xfb, 0x21, 0x23, 0x20, 0xb0, 0x4d, 0x2b, 0x95, 0x18,
	0x45, 0x13, 0x61, 0x63, 0xda, 0x0b, 0x80, 0x36, 0xd6, 0x0d, 0xb4, 0x2e, 0x15, 0xbb, 0xd8, 0x4d,
	0xe4, 0x26, 0x26, 0xb3, 0x68, 0xe2, 0x34, 0x4e, 0x27, 0xaa, 0x4d, 0x7b, 0x9c, 0x3d, 0xd3, 0x9e,
	0x63, 0x77, 0xbb, 0x9b, 0x8e, 0xed, 0xd2, 0x22, 0x5a, 0x89, 0xde, 0xd0, 0x3b, 0x9f, 0x3f, 0x39,
	0xdf, 0xf9, 0xec, 0xef, 0xc4, 0x86, 0x7a, 0xf0, 0x95, 0x26, 0x09, 0xeb, 0xf9, 0x11, 0x8d, 0x99,
	0x9f, 0xf6, 0xe8, 0x90, 0x65, 0x7e, 0x4f, 0x44, 0x3c, 0xf0, 0xdd, 0x34, 0x13, 0xb9, 0x20, 0x1b,
	0x11, 0x75, 0xa7, 0x24, 0x6d, 0xd7, 0x22, 0xea, 0x77, 0xa9, 0x64, 0x3a, 0xad, 0x31, 0x84, 0x8d,
	0x53, 0x96, 0x7f, 0x4c, 0x59, 0xc2, 0xc3, 0x53, 0x1a, 0xb3, 0xb6, 0xca, 0xf2, 0x58, 0x9f, 0xec,
	0x41, 0x05, 0xf3, 0xfc, 0x8c, 0xf5, 0x1d, 0xab, 0x6e, 0x35, 0xab, 0x47, 0x2b, 0x6e, 0x44, 0xdd,
	0x63, 0x2a, 0x99, 0xc7, 0x64, 0xea, 0x95, 0xbb, 0x6a, 0xd5, 0x27, 0x4f, 0x00, 0x46, 0x40, 0x3c,
	0x74, 0x96, 0xea, 0x56, 0xb3, 0xe6, 0xd9, 0xc6, 0xd3, 0x0a, 0xc9, 0x26, 0x94, 0x15, 0x3e, 0x0f,
	0x9d, 0x82, 0x8a, 0x95, 0xd0, 0x6c, 0x85, 0x8d, 0x3e, 0x6c, 0x4e, 0x85, 0x96, 0x29, 0xd9, 0x07,
	0xdb, 0x60, 0xcb, 0x74, 0x2a, 0x78, 0xa5, 0x6b, 0x56, 0x58, 0x5e, 0xa4, 0x2c, 0x19, 0x41, 0xdb,
	0x5e, 0x09, 0xcd, 0x56, 0x48, 0x08, 0x14, 0x03, 0x11, 0x32, 0x05, 0xba, 0xec, 0xa9, 0x75, 0xe3,
	0x97, 0x05, 0xce, 0x7b, 0xc1, 0x93, 0x13, 0xdd, 0xdd, 0xc3, 0x12, 0x26, 0xfb, 0xb0, 0x9a, 0xb2,
	0x2c, 0xe6, 0x52, 0x72, 0x91, 0x48, 0x5f, 0x75, 0x57, 0x54, 0x19, 0xff, 0x4f, 0xf8, 0x4f, 0xb0,
	0xd1, 0xb7, 0xb0, 0x35, 0xa3, 0xcf, 0xb9, 0x76, 0xa7, 0xf1, 0x1d, 0x9c, 0x4f, 0x03, 0x9e, 0x2f,
	0x84, 0x2f, 0x92, 0x98, 0x01, 0x3e, 0x1f, 0x89, 0x1f, 0xb0, 0xe5, 0x31, 0x1a, 0x0e, 0x17, 0xc3,
	0xe2, 0x14, 0xb6, 0x67, 0xa1, 0xcf, 0x47, 0xe3, 0x27, 0x3c, 0xba, 0x48, 0x16, 0x48, 0xa4, 0x05,
	0x8f, 0x67, 0xe3, 0xcf, 0x47, 0xe5, 0xb7, 0x05, 0xbb, 0x1d, 0x76, 0xf7, 0x64, 0xdb, 0x37, 0x32,
	0x46, 0x15, 0x3f, 0xc8, 0x50, 0xed, 0xc2, 0x7f, 0xe3, 0xe1, 0xf1, 0x07, 0x3c, 0x34, 0x23, 0x55,
	0x1b, 0x7b, 0x2f, 0x78, 0x48, 0xf6, 0x60, 0x62, 0xc6, 0xf4, 0xe8, 0x2d, 0xab, 0xbc, 0x89, 0xaf,
	0xd5, 0xe4, 0x75, 0xe0, 0xd9, 0x7d, 0x98, 0xcd, 0xad, 0xe0, 0x4e, 0x4e, 0xb3, 0x7c, 0x61, 0x0a,
	0x9e, 0x85, 0x3e, 0xf7, 0xdf, 0xe4, 0xcd, 0xf5, 0x02, 0xff, 0x26, 0x33, 0xc0, 0xe7, 0x23, 0xf1,
	0xc7, 0x82, 0xf5, 0x3b, 0x45, 0x5a, 0xc9, 0xa5, 0x20, 0xab, 0x50, 0x40, 0xfd, 0x58, 0x0a, 0x16,
	0x97, 0x64, 0x03, 0x4a, 0x32, 0xa7, 0xf9, 0x40, 0x9a, 0x3e, 0x8d, 0x45, 0xb6, 0xa1, 0x32, 0x90,
	0x2c, 0x4b, 0x68, 0xac, 0xef, 0x17, 0xdb, 0xbb, 0xb1, 0x31, 0x96, 0xf0, 0xe0, 0x4a, 0xc5, 0x8a,
	0x3a, 0x36, 0xb2, 0x11, 0x41, 0xb2, 0x6b, 0xa3, 0x3c, 0x5c, 0x22, 0x82, 0x50, 0x37, 0xa0, 0x53,
	0x1a, 0xdf, 0x5e, 0x3c, 0xd4, 0x99, 0x7d, 0xa7, 0x3c, 0xca, 0xec, 0x4f, 0x53, 0x70, 0x65, 0x9a,
	0x82, 0xf1, 0xe2, 0xbb, 0xa4, 0x01, 0x73, 0x6c, 0x55, 0x50, 0xad, 0x1b, 0x19, 0xac, 0x4d, 0x70,
	0x3e, 0x17, 0x21, 0x53, 0x8c, 0xb7, 0xa0, 0x12, 0x8b, 0x90, 0xf9, 0x57, 0x6c, 0xa8, 0x68, 0xdb,
	0x5e, 0x19, 0xed, 0x0f, 0x6c, 0x88, 0xc7, 0xa4, 0x9f, 0x0d, 0x34, 0xa3, 0xb1, 0xb9, 0x5a, 0x6d,
	0xf4, 0xb4, 0xd1, 0x41, 0x76, 0x60, 0x65, 0xf4, 0xea, 0xe0, 0x31, 0xcf, 0x9d, 0x42, 0xbd, 0xd0,
	0xac, 0x79, 0x55, 0xed, 0x3b, 0x43, 0x57, 0xe3, 0xef, 0xd2, 0xad, 0x8d, 0x46, 0x40, 0x34, 0x23,
	0x36, 0x79, 0xc6, 0xd6, 0xad, 0x61, 0xde, 0x81, 0x15, 0x15, 0xf8, 0xc6, 0x32, 0xa4, 0x63, 0x60,
	0xab, 0xe8, 0xfb, 0xac, 0x5d, 0x64, 0x0d, 0x96, 0x83, 0x74, 0xac, 0x8e, 0x62, 0x90, 0xb6, 0x42,
	0xf2, 0x12, 0xd6, 0xd5, 0x77, 0x31, 0x8b, 0xbb, 0x2c, 0xf3, 0x83, 0x24, 0x37, 0x6d, 0xe9, 0x7f,
	0x01, 0xc1, 0xe0, 0xb9, 0x8a, 0x9d, 0x24, 0xb9, 0xea, 0x8e, 0x3c, 0x85, 0xaa, 0xfe, 0x84, 0xca,
	0x9c, 0x65, 0xe6, 0x48, 0x14, 0xe5, 0x73, 0xe5, 0xb9, 0x49, 0x30, 0x02, 0x28, 0x8d, 0x13, 0x3a,
	0x5a, 0x04, 0xef, 0xc0, 0xd6, 0x15, 0xf0, 0x28, 0xca, 0x4a, 0x73, 0xcf, 0xdd, 0xe9, 0xaf, 0x2e,
	0x77, 0xca, 0xe6, 0x7b, 0x95, 0xc8, 0x58, 0xa4, 0x6d, 0x68, 0xeb, 0x64, 0xe9, 0x54, 0xea, 0x85,
	0x66, 0xf5, 0xe8, 0xe0, 0x1e, 0xc5, 0xc6, 0xea, 0xd5, 0xbb, 0xa4, 0x6d, 0x79, 0x7c, 0x06, 0x4e,
	0x20, 0x62, 0x77, 0xc8, 0x87, 0x62, 0x80, 0x65, 0xb0, 0xc7, 0x9e, 0x7e, 0xf2, 0x7d, 0x79, 0x11,
	0x89, 0x1e, 0x4d, 0x22, 0xf7, 0xf5, 0x51, 0x9e, 0xbb, 0x81, 0x88, 0x0f, 0x95, 0x3b, 0x10, 0xbd,
	0x43, 0x9a, 0xa6, 0x87, 0x06, 0xf0, 0x00, 0xab, 0x1d, 0x68, 0xc0, 0x6e, 0x49, 0x65, 0xbc, 0xfa,
	0x17, 0x00, 0x00, 0xff, 0xff, 0x58, 0xa8, 0x46, 0x7a, 0x76, 0x0a, 0x00, 0x00,
}
