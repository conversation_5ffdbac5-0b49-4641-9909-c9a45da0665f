// Code generated by protoc-gen-go. DO NOT EDIT.
// source: fellow_logic/fellow-logic_.proto

package fellow_logic // import "golang.52tt.com/protocol/app/fellow-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FellowType int32

const (
	FellowType_ENUM_FELLOW_TYPE_UNKNOWN  FellowType = 0
	FellowType_ENUM_FELLOW_TYPE_BRO      FellowType = 1
	FellowType_ENUM_FELLOW_TYPE_LADYBRO  FellowType = 2
	FellowType_ENUM_FELLOW_TYPE_INTIMATE FellowType = 3
	FellowType_ENUM_FELLOW_TYPE_PARTNER  FellowType = 4
)

var FellowType_name = map[int32]string{
	0: "ENUM_FELLOW_TYPE_UNKNOWN",
	1: "ENUM_FELLOW_TYPE_BRO",
	2: "ENUM_FELLOW_TYPE_LADYBRO",
	3: "ENUM_FELLOW_TYPE_INTIMATE",
	4: "ENUM_FELLOW_TYPE_PARTNER",
}
var FellowType_value = map[string]int32{
	"ENUM_FELLOW_TYPE_UNKNOWN":  0,
	"ENUM_FELLOW_TYPE_BRO":      1,
	"ENUM_FELLOW_TYPE_LADYBRO":  2,
	"ENUM_FELLOW_TYPE_INTIMATE": 3,
	"ENUM_FELLOW_TYPE_PARTNER":  4,
}

func (x FellowType) String() string {
	return proto.EnumName(FellowType_name, int32(x))
}
func (FellowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{0}
}

type FellowBindType int32

const (
	FellowBindType_ENUM_FELLOW_BIND_TYPE_UNKNOWN FellowBindType = 0
	FellowBindType_ENUM_FELLOW_BIND_TYPE_UNIQUE  FellowBindType = 1
	FellowBindType_ENUM_FELLOW_BIND_TYPE_MULTI   FellowBindType = 2
)

var FellowBindType_name = map[int32]string{
	0: "ENUM_FELLOW_BIND_TYPE_UNKNOWN",
	1: "ENUM_FELLOW_BIND_TYPE_UNIQUE",
	2: "ENUM_FELLOW_BIND_TYPE_MULTI",
}
var FellowBindType_value = map[string]int32{
	"ENUM_FELLOW_BIND_TYPE_UNKNOWN": 0,
	"ENUM_FELLOW_BIND_TYPE_UNIQUE":  1,
	"ENUM_FELLOW_BIND_TYPE_MULTI":   2,
}

func (x FellowBindType) String() string {
	return proto.EnumName(FellowBindType_name, int32(x))
}
func (FellowBindType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{1}
}

type FellowBindStatus int32

const (
	FellowBindStatus_ENUM_FELLOW_BIND_STATUS_NORMAL FellowBindStatus = 0
	FellowBindStatus_ENUM_FELLOW_BIND_STATUS_APART  FellowBindStatus = 1
)

var FellowBindStatus_name = map[int32]string{
	0: "ENUM_FELLOW_BIND_STATUS_NORMAL",
	1: "ENUM_FELLOW_BIND_STATUS_APART",
}
var FellowBindStatus_value = map[string]int32{
	"ENUM_FELLOW_BIND_STATUS_NORMAL": 0,
	"ENUM_FELLOW_BIND_STATUS_APART":  1,
}

func (x FellowBindStatus) String() string {
	return proto.EnumName(FellowBindStatus_name, int32(x))
}
func (FellowBindStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{2}
}

type BanType int32

const (
	BanType_BAN_TYPE_NORMAL    BanType = 0
	BanType_BAN_TYPE_TMP       BanType = 1
	BanType_BAN_TYPE_PERMANENT BanType = 2
)

var BanType_name = map[int32]string{
	0: "BAN_TYPE_NORMAL",
	1: "BAN_TYPE_TMP",
	2: "BAN_TYPE_PERMANENT",
}
var BanType_value = map[string]int32{
	"BAN_TYPE_NORMAL":    0,
	"BAN_TYPE_TMP":       1,
	"BAN_TYPE_PERMANENT": 2,
}

func (x BanType) String() string {
	return proto.EnumName(BanType_name, int32(x))
}
func (BanType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{3}
}

type SwitchEnum int32

const (
	SwitchEnum_SWITCH_OFF SwitchEnum = 0
	SwitchEnum_SWITCH_ON  SwitchEnum = 1
)

var SwitchEnum_name = map[int32]string{
	0: "SWITCH_OFF",
	1: "SWITCH_ON",
}
var SwitchEnum_value = map[string]int32{
	"SWITCH_OFF": 0,
	"SWITCH_ON":  1,
}

func (x SwitchEnum) String() string {
	return proto.EnumName(SwitchEnum_name, int32(x))
}
func (SwitchEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{4}
}

type InviteStatus int32

const (
	InviteStatus_ENUM_INVITE_STATUS_NO_INVITE InviteStatus = 0
	InviteStatus_ENUM_INVITE_STATUS_INVITED   InviteStatus = 1
	InviteStatus_ENUM_INVITE_STATUS_SUCCESS   InviteStatus = 2
	InviteStatus_ENUM_INVITE_STATUS_FAILED    InviteStatus = 3
	InviteStatus_ENUM_INVITE_STATUS_CANCELED  InviteStatus = 4
	InviteStatus_ENUM_INVITE_STATUS_TIMEOUT   InviteStatus = 5
)

var InviteStatus_name = map[int32]string{
	0: "ENUM_INVITE_STATUS_NO_INVITE",
	1: "ENUM_INVITE_STATUS_INVITED",
	2: "ENUM_INVITE_STATUS_SUCCESS",
	3: "ENUM_INVITE_STATUS_FAILED",
	4: "ENUM_INVITE_STATUS_CANCELED",
	5: "ENUM_INVITE_STATUS_TIMEOUT",
}
var InviteStatus_value = map[string]int32{
	"ENUM_INVITE_STATUS_NO_INVITE": 0,
	"ENUM_INVITE_STATUS_INVITED":   1,
	"ENUM_INVITE_STATUS_SUCCESS":   2,
	"ENUM_INVITE_STATUS_FAILED":    3,
	"ENUM_INVITE_STATUS_CANCELED":  4,
	"ENUM_INVITE_STATUS_TIMEOUT":   5,
}

func (x InviteStatus) String() string {
	return proto.EnumName(InviteStatus_name, int32(x))
}
func (InviteStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{5}
}

type FellowSourceType int32

const (
	FellowSourceType_ENUM_FELLOW_SOURCE_TYPE_UNKNOWN FellowSourceType = 0
	FellowSourceType_ENUM_FELLOW_SOURCE_TYPE_PIC     FellowSourceType = 1
	FellowSourceType_ENUM_FELLOW_SOURCE_TYPE_VAP     FellowSourceType = 2
)

var FellowSourceType_name = map[int32]string{
	0: "ENUM_FELLOW_SOURCE_TYPE_UNKNOWN",
	1: "ENUM_FELLOW_SOURCE_TYPE_PIC",
	2: "ENUM_FELLOW_SOURCE_TYPE_VAP",
}
var FellowSourceType_value = map[string]int32{
	"ENUM_FELLOW_SOURCE_TYPE_UNKNOWN": 0,
	"ENUM_FELLOW_SOURCE_TYPE_PIC":     1,
	"ENUM_FELLOW_SOURCE_TYPE_VAP":     2,
}

func (x FellowSourceType) String() string {
	return proto.EnumName(FellowSourceType_name, int32(x))
}
func (FellowSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{6}
}

type FellowHouseRes_ResType int32

const (
	FellowHouseRes_ENUM_RES_TYPE_UNKNOWN FellowHouseRes_ResType = 0
	FellowHouseRes_ENUM_RES_TYPE_VAP     FellowHouseRes_ResType = 1
	FellowHouseRes_ENUM_RES_TYPE_LOTTIE  FellowHouseRes_ResType = 2
)

var FellowHouseRes_ResType_name = map[int32]string{
	0: "ENUM_RES_TYPE_UNKNOWN",
	1: "ENUM_RES_TYPE_VAP",
	2: "ENUM_RES_TYPE_LOTTIE",
}
var FellowHouseRes_ResType_value = map[string]int32{
	"ENUM_RES_TYPE_UNKNOWN": 0,
	"ENUM_RES_TYPE_VAP":     1,
	"ENUM_RES_TYPE_LOTTIE":  2,
}

func (x FellowHouseRes_ResType) String() string {
	return proto.EnumName(FellowHouseRes_ResType_name, int32(x))
}
func (FellowHouseRes_ResType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{81, 0}
}

// 铭牌信息
type PlateUrl struct {
	CpUnique             string   `protobuf:"bytes,1,opt,name=cp_unique,json=cpUnique,proto3" json:"cp_unique,omitempty"`
	DateUnique           string   `protobuf:"bytes,2,opt,name=date_unique,json=dateUnique,proto3" json:"date_unique,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlateUrl) Reset()         { *m = PlateUrl{} }
func (m *PlateUrl) String() string { return proto.CompactTextString(m) }
func (*PlateUrl) ProtoMessage()    {}
func (*PlateUrl) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{0}
}
func (m *PlateUrl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlateUrl.Unmarshal(m, b)
}
func (m *PlateUrl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlateUrl.Marshal(b, m, deterministic)
}
func (dst *PlateUrl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlateUrl.Merge(dst, src)
}
func (m *PlateUrl) XXX_Size() int {
	return xxx_messageInfo_PlateUrl.Size(m)
}
func (m *PlateUrl) XXX_DiscardUnknown() {
	xxx_messageInfo_PlateUrl.DiscardUnknown(m)
}

var xxx_messageInfo_PlateUrl proto.InternalMessageInfo

func (m *PlateUrl) GetCpUnique() string {
	if m != nil {
		return m.CpUnique
	}
	return ""
}

func (m *PlateUrl) GetDateUnique() string {
	if m != nil {
		return m.DateUnique
	}
	return ""
}

type FellowBackground struct {
	BackgroundUrl        string   `protobuf:"bytes,1,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	SourceType           uint32   `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	Md5                  string   `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	BackgroundImg        string   `protobuf:"bytes,4,opt,name=background_img,json=backgroundImg,proto3" json:"background_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowBackground) Reset()         { *m = FellowBackground{} }
func (m *FellowBackground) String() string { return proto.CompactTextString(m) }
func (*FellowBackground) ProtoMessage()    {}
func (*FellowBackground) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{1}
}
func (m *FellowBackground) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowBackground.Unmarshal(m, b)
}
func (m *FellowBackground) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowBackground.Marshal(b, m, deterministic)
}
func (dst *FellowBackground) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowBackground.Merge(dst, src)
}
func (m *FellowBackground) XXX_Size() int {
	return xxx_messageInfo_FellowBackground.Size(m)
}
func (m *FellowBackground) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowBackground.DiscardUnknown(m)
}

var xxx_messageInfo_FellowBackground proto.InternalMessageInfo

func (m *FellowBackground) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *FellowBackground) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *FellowBackground) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *FellowBackground) GetBackgroundImg() string {
	if m != nil {
		return m.BackgroundImg
	}
	return ""
}

type RareInfo struct {
	RareId               uint32   `protobuf:"varint,1,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	SubRareId            uint32   `protobuf:"varint,2,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	DayCount             uint32   `protobuf:"varint,3,opt,name=day_count,json=dayCount,proto3" json:"day_count,omitempty"`
	RemainCount          uint32   `protobuf:"varint,4,opt,name=remain_count,json=remainCount,proto3" json:"remain_count,omitempty"`
	BindStatus           bool     `protobuf:"varint,5,opt,name=bind_status,json=bindStatus,proto3" json:"bind_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RareInfo) Reset()         { *m = RareInfo{} }
func (m *RareInfo) String() string { return proto.CompactTextString(m) }
func (*RareInfo) ProtoMessage()    {}
func (*RareInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{2}
}
func (m *RareInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RareInfo.Unmarshal(m, b)
}
func (m *RareInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RareInfo.Marshal(b, m, deterministic)
}
func (dst *RareInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RareInfo.Merge(dst, src)
}
func (m *RareInfo) XXX_Size() int {
	return xxx_messageInfo_RareInfo.Size(m)
}
func (m *RareInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RareInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RareInfo proto.InternalMessageInfo

func (m *RareInfo) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *RareInfo) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

func (m *RareInfo) GetDayCount() uint32 {
	if m != nil {
		return m.DayCount
	}
	return 0
}

func (m *RareInfo) GetRemainCount() uint32 {
	if m != nil {
		return m.RemainCount
	}
	return 0
}

func (m *RareInfo) GetBindStatus() bool {
	if m != nil {
		return m.BindStatus
	}
	return false
}

// 稀缺信息
type RareTabInfo struct {
	ToUid                uint32            `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToAccount            string            `protobuf:"bytes,2,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ToNickName           string            `protobuf:"bytes,3,opt,name=to_nick_name,json=toNickName,proto3" json:"to_nick_name,omitempty"`
	BindType             uint32            `protobuf:"varint,4,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32            `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowName           string            `protobuf:"bytes,6,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	OutRare              *RareInfo         `protobuf:"bytes,7,opt,name=out_rare,json=outRare,proto3" json:"out_rare,omitempty"`
	PresentBg            *FellowBackground `protobuf:"bytes,8,opt,name=present_bg,json=presentBg,proto3" json:"present_bg,omitempty"`
	RareCount            uint32            `protobuf:"varint,9,opt,name=rare_count,json=rareCount,proto3" json:"rare_count,omitempty"`
	UserProfile          *app.UserProfile  `protobuf:"bytes,11,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	ToSex                uint32            `protobuf:"varint,12,opt,name=to_sex,json=toSex,proto3" json:"to_sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RareTabInfo) Reset()         { *m = RareTabInfo{} }
func (m *RareTabInfo) String() string { return proto.CompactTextString(m) }
func (*RareTabInfo) ProtoMessage()    {}
func (*RareTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{3}
}
func (m *RareTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RareTabInfo.Unmarshal(m, b)
}
func (m *RareTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RareTabInfo.Marshal(b, m, deterministic)
}
func (dst *RareTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RareTabInfo.Merge(dst, src)
}
func (m *RareTabInfo) XXX_Size() int {
	return xxx_messageInfo_RareTabInfo.Size(m)
}
func (m *RareTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RareTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RareTabInfo proto.InternalMessageInfo

func (m *RareTabInfo) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *RareTabInfo) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *RareTabInfo) GetToNickName() string {
	if m != nil {
		return m.ToNickName
	}
	return ""
}

func (m *RareTabInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *RareTabInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *RareTabInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *RareTabInfo) GetOutRare() *RareInfo {
	if m != nil {
		return m.OutRare
	}
	return nil
}

func (m *RareTabInfo) GetPresentBg() *FellowBackground {
	if m != nil {
		return m.PresentBg
	}
	return nil
}

func (m *RareTabInfo) GetRareCount() uint32 {
	if m != nil {
		return m.RareCount
	}
	return 0
}

func (m *RareTabInfo) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *RareTabInfo) GetToSex() uint32 {
	if m != nil {
		return m.ToSex
	}
	return 0
}

// 段位信息 （一个等级范围属于一个段位）
type GradingInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	GradingName          string   `protobuf:"bytes,2,opt,name=grading_name,json=gradingName,proto3" json:"grading_name,omitempty"`
	GradingIcon          string   `protobuf:"bytes,3,opt,name=grading_icon,json=gradingIcon,proto3" json:"grading_icon,omitempty"`
	GradingColor         string   `protobuf:"bytes,4,opt,name=grading_color,json=gradingColor,proto3" json:"grading_color,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GradingInfo) Reset()         { *m = GradingInfo{} }
func (m *GradingInfo) String() string { return proto.CompactTextString(m) }
func (*GradingInfo) ProtoMessage()    {}
func (*GradingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{4}
}
func (m *GradingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GradingInfo.Unmarshal(m, b)
}
func (m *GradingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GradingInfo.Marshal(b, m, deterministic)
}
func (dst *GradingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GradingInfo.Merge(dst, src)
}
func (m *GradingInfo) XXX_Size() int {
	return xxx_messageInfo_GradingInfo.Size(m)
}
func (m *GradingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GradingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GradingInfo proto.InternalMessageInfo

func (m *GradingInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GradingInfo) GetGradingName() string {
	if m != nil {
		return m.GradingName
	}
	return ""
}

func (m *GradingInfo) GetGradingIcon() string {
	if m != nil {
		return m.GradingIcon
	}
	return ""
}

func (m *GradingInfo) GetGradingColor() string {
	if m != nil {
		return m.GradingColor
	}
	return ""
}

// 挚友信息
type FellowInfo struct {
	Uid                  int64             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string            `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string            `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	FellowLevel          uint32            `protobuf:"varint,4,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level,omitempty"`
	FellowType           uint32            `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	DayCnt               uint32            `protobuf:"varint,6,opt,name=day_cnt,json=dayCnt,proto3" json:"day_cnt,omitempty"`
	FellowName           string            `protobuf:"bytes,7,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	BindType             uint32            `protobuf:"varint,8,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowPoint          uint32            `protobuf:"varint,9,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	Background           *FellowBackground `protobuf:"bytes,10,opt,name=background,proto3" json:"background,omitempty"`
	CpPresentIcon        string            `protobuf:"bytes,17,opt,name=cp_present_icon,json=cpPresentIcon,proto3" json:"cp_present_icon,omitempty"`
	PlateUrl             *PlateUrl         `protobuf:"bytes,18,opt,name=plate_url,json=plateUrl,proto3" json:"plate_url,omitempty"`
	BindStatus           uint32            `protobuf:"varint,19,opt,name=bind_status,json=bindStatus,proto3" json:"bind_status,omitempty"`
	BanType              BanType           `protobuf:"varint,20,opt,name=ban_type,json=banType,proto3,enum=ga.fellow_logic.BanType" json:"ban_type,omitempty"`
	BindRare             *RareInfo         `protobuf:"bytes,21,opt,name=bind_rare,json=bindRare,proto3" json:"bind_rare,omitempty"`
	UserProfile          *app.UserProfile  `protobuf:"bytes,22,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	FellowIcon           string            `protobuf:"bytes,23,opt,name=fellow_icon,json=fellowIcon,proto3" json:"fellow_icon,omitempty"`
	CardColour           string            `protobuf:"bytes,24,opt,name=card_colour,json=cardColour,proto3" json:"card_colour,omitempty"`
	RoomMsgColour        []string          `protobuf:"bytes,25,rep,name=room_msg_colour,json=roomMsgColour,proto3" json:"room_msg_colour,omitempty"`
	LigatureUrl          string            `protobuf:"bytes,26,opt,name=ligature_url,json=ligatureUrl,proto3" json:"ligature_url,omitempty"`
	GradingInfo          *GradingInfo      `protobuf:"bytes,27,opt,name=grading_info,json=gradingInfo,proto3" json:"grading_info,omitempty"`
	Cfg                  *FellowHouseCfg   `protobuf:"bytes,28,opt,name=cfg,proto3" json:"cfg,omitempty"`
	Sex                  uint32            `protobuf:"varint,29,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *FellowInfo) Reset()         { *m = FellowInfo{} }
func (m *FellowInfo) String() string { return proto.CompactTextString(m) }
func (*FellowInfo) ProtoMessage()    {}
func (*FellowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{5}
}
func (m *FellowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowInfo.Unmarshal(m, b)
}
func (m *FellowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowInfo.Marshal(b, m, deterministic)
}
func (dst *FellowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowInfo.Merge(dst, src)
}
func (m *FellowInfo) XXX_Size() int {
	return xxx_messageInfo_FellowInfo.Size(m)
}
func (m *FellowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowInfo proto.InternalMessageInfo

func (m *FellowInfo) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FellowInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FellowInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *FellowInfo) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *FellowInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *FellowInfo) GetDayCnt() uint32 {
	if m != nil {
		return m.DayCnt
	}
	return 0
}

func (m *FellowInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *FellowInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *FellowInfo) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *FellowInfo) GetBackground() *FellowBackground {
	if m != nil {
		return m.Background
	}
	return nil
}

func (m *FellowInfo) GetCpPresentIcon() string {
	if m != nil {
		return m.CpPresentIcon
	}
	return ""
}

func (m *FellowInfo) GetPlateUrl() *PlateUrl {
	if m != nil {
		return m.PlateUrl
	}
	return nil
}

func (m *FellowInfo) GetBindStatus() uint32 {
	if m != nil {
		return m.BindStatus
	}
	return 0
}

func (m *FellowInfo) GetBanType() BanType {
	if m != nil {
		return m.BanType
	}
	return BanType_BAN_TYPE_NORMAL
}

func (m *FellowInfo) GetBindRare() *RareInfo {
	if m != nil {
		return m.BindRare
	}
	return nil
}

func (m *FellowInfo) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *FellowInfo) GetFellowIcon() string {
	if m != nil {
		return m.FellowIcon
	}
	return ""
}

func (m *FellowInfo) GetCardColour() string {
	if m != nil {
		return m.CardColour
	}
	return ""
}

func (m *FellowInfo) GetRoomMsgColour() []string {
	if m != nil {
		return m.RoomMsgColour
	}
	return nil
}

func (m *FellowInfo) GetLigatureUrl() string {
	if m != nil {
		return m.LigatureUrl
	}
	return ""
}

func (m *FellowInfo) GetGradingInfo() *GradingInfo {
	if m != nil {
		return m.GradingInfo
	}
	return nil
}

func (m *FellowInfo) GetCfg() *FellowHouseCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

func (m *FellowInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 申请挚友页面提供的用户信息
type FellowInviteUser struct {
	Uid                  int64    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	FellowPoint          uint32   `protobuf:"varint,4,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	InviteStatus         uint32   `protobuf:"varint,5,opt,name=invite_status,json=inviteStatus,proto3" json:"invite_status,omitempty"`
	Sex                  uint32   `protobuf:"varint,6,opt,name=sex,proto3" json:"sex,omitempty"`
	InviteId             string   `protobuf:"bytes,7,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowInviteUser) Reset()         { *m = FellowInviteUser{} }
func (m *FellowInviteUser) String() string { return proto.CompactTextString(m) }
func (*FellowInviteUser) ProtoMessage()    {}
func (*FellowInviteUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{6}
}
func (m *FellowInviteUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowInviteUser.Unmarshal(m, b)
}
func (m *FellowInviteUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowInviteUser.Marshal(b, m, deterministic)
}
func (dst *FellowInviteUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowInviteUser.Merge(dst, src)
}
func (m *FellowInviteUser) XXX_Size() int {
	return xxx_messageInfo_FellowInviteUser.Size(m)
}
func (m *FellowInviteUser) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowInviteUser.DiscardUnknown(m)
}

var xxx_messageInfo_FellowInviteUser proto.InternalMessageInfo

func (m *FellowInviteUser) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FellowInviteUser) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FellowInviteUser) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *FellowInviteUser) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *FellowInviteUser) GetInviteStatus() uint32 {
	if m != nil {
		return m.InviteStatus
	}
	return 0
}

func (m *FellowInviteUser) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *FellowInviteUser) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

// 信物信息
type FellowPresentInfo struct {
	ItemId               uint32            `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Name                 string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Value                uint32            `protobuf:"varint,3,opt,name=value,proto3" json:"value,omitempty"`
	Icon                 string            `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	PriceType            uint32            `protobuf:"varint,5,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	UniqueBackground     *FellowBackground `protobuf:"bytes,6,opt,name=unique_background,json=uniqueBackground,proto3" json:"unique_background,omitempty"`
	MultiBackground      *FellowBackground `protobuf:"bytes,7,opt,name=multi_background,json=multiBackground,proto3" json:"multi_background,omitempty"`
	AlreadyOwn           uint32            `protobuf:"varint,8,opt,name=already_own,json=alreadyOwn,proto3" json:"already_own,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *FellowPresentInfo) Reset()         { *m = FellowPresentInfo{} }
func (m *FellowPresentInfo) String() string { return proto.CompactTextString(m) }
func (*FellowPresentInfo) ProtoMessage()    {}
func (*FellowPresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{7}
}
func (m *FellowPresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowPresentInfo.Unmarshal(m, b)
}
func (m *FellowPresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowPresentInfo.Marshal(b, m, deterministic)
}
func (dst *FellowPresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowPresentInfo.Merge(dst, src)
}
func (m *FellowPresentInfo) XXX_Size() int {
	return xxx_messageInfo_FellowPresentInfo.Size(m)
}
func (m *FellowPresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowPresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowPresentInfo proto.InternalMessageInfo

func (m *FellowPresentInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *FellowPresentInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FellowPresentInfo) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *FellowPresentInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FellowPresentInfo) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *FellowPresentInfo) GetUniqueBackground() *FellowBackground {
	if m != nil {
		return m.UniqueBackground
	}
	return nil
}

func (m *FellowPresentInfo) GetMultiBackground() *FellowBackground {
	if m != nil {
		return m.MultiBackground
	}
	return nil
}

func (m *FellowPresentInfo) GetAlreadyOwn() uint32 {
	if m != nil {
		return m.AlreadyOwn
	}
	return 0
}

// 邀请函信息
type FellowInviteInfo struct {
	InviteId             string             `protobuf:"bytes,1,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	FromUid              int64              `protobuf:"varint,2,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromAccount          string             `protobuf:"bytes,3,opt,name=from_account,json=fromAccount,proto3" json:"from_account,omitempty"`
	FromNickname         string             `protobuf:"bytes,4,opt,name=from_nickname,json=fromNickname,proto3" json:"from_nickname,omitempty"`
	BindType             uint32             `protobuf:"varint,5,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32             `protobuf:"varint,6,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowPoint          uint32             `protobuf:"varint,7,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	CreateTime           uint32             `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	FellowName           string             `protobuf:"bytes,9,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	ToUid                int64              `protobuf:"varint,10,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToAccount            string             `protobuf:"bytes,11,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ToNickname           string             `protobuf:"bytes,12,opt,name=to_nickname,json=toNickname,proto3" json:"to_nickname,omitempty"`
	Status               uint32             `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"`
	WithUnlock           bool               `protobuf:"varint,14,opt,name=with_unlock,json=withUnlock,proto3" json:"with_unlock,omitempty"`
	FromSex              uint32             `protobuf:"varint,15,opt,name=from_sex,json=fromSex,proto3" json:"from_sex,omitempty"`
	ToSex                uint32             `protobuf:"varint,16,opt,name=to_sex,json=toSex,proto3" json:"to_sex,omitempty"`
	PresentInfo          *FellowPresentInfo `protobuf:"bytes,17,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	UnlockPrice          uint32             `protobuf:"varint,18,opt,name=unlock_price,json=unlockPrice,proto3" json:"unlock_price,omitempty"`
	ChannelId            uint32             `protobuf:"varint,19,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	EndTime              uint32             `protobuf:"varint,20,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	FromFellowName       string             `protobuf:"bytes,21,opt,name=from_fellow_name,json=fromFellowName,proto3" json:"from_fellow_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *FellowInviteInfo) Reset()         { *m = FellowInviteInfo{} }
func (m *FellowInviteInfo) String() string { return proto.CompactTextString(m) }
func (*FellowInviteInfo) ProtoMessage()    {}
func (*FellowInviteInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{8}
}
func (m *FellowInviteInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowInviteInfo.Unmarshal(m, b)
}
func (m *FellowInviteInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowInviteInfo.Marshal(b, m, deterministic)
}
func (dst *FellowInviteInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowInviteInfo.Merge(dst, src)
}
func (m *FellowInviteInfo) XXX_Size() int {
	return xxx_messageInfo_FellowInviteInfo.Size(m)
}
func (m *FellowInviteInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowInviteInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowInviteInfo proto.InternalMessageInfo

func (m *FellowInviteInfo) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *FellowInviteInfo) GetFromUid() int64 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *FellowInviteInfo) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *FellowInviteInfo) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *FellowInviteInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *FellowInviteInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *FellowInviteInfo) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *FellowInviteInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *FellowInviteInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *FellowInviteInfo) GetToUid() int64 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *FellowInviteInfo) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *FellowInviteInfo) GetToNickname() string {
	if m != nil {
		return m.ToNickname
	}
	return ""
}

func (m *FellowInviteInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *FellowInviteInfo) GetWithUnlock() bool {
	if m != nil {
		return m.WithUnlock
	}
	return false
}

func (m *FellowInviteInfo) GetFromSex() uint32 {
	if m != nil {
		return m.FromSex
	}
	return 0
}

func (m *FellowInviteInfo) GetToSex() uint32 {
	if m != nil {
		return m.ToSex
	}
	return 0
}

func (m *FellowInviteInfo) GetPresentInfo() *FellowPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *FellowInviteInfo) GetUnlockPrice() uint32 {
	if m != nil {
		return m.UnlockPrice
	}
	return 0
}

func (m *FellowInviteInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FellowInviteInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *FellowInviteInfo) GetFromFellowName() string {
	if m != nil {
		return m.FromFellowName
	}
	return ""
}

// 邀请函信息
type FellowTypeInfo struct {
	MultiFellowType      uint32   `protobuf:"varint,1,opt,name=multi_fellow_type,json=multiFellowType,proto3" json:"multi_fellow_type,omitempty"`
	MultiFellowName      string   `protobuf:"bytes,2,opt,name=multi_fellow_name,json=multiFellowName,proto3" json:"multi_fellow_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowTypeInfo) Reset()         { *m = FellowTypeInfo{} }
func (m *FellowTypeInfo) String() string { return proto.CompactTextString(m) }
func (*FellowTypeInfo) ProtoMessage()    {}
func (*FellowTypeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{9}
}
func (m *FellowTypeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowTypeInfo.Unmarshal(m, b)
}
func (m *FellowTypeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowTypeInfo.Marshal(b, m, deterministic)
}
func (dst *FellowTypeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowTypeInfo.Merge(dst, src)
}
func (m *FellowTypeInfo) XXX_Size() int {
	return xxx_messageInfo_FellowTypeInfo.Size(m)
}
func (m *FellowTypeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowTypeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowTypeInfo proto.InternalMessageInfo

func (m *FellowTypeInfo) GetMultiFellowType() uint32 {
	if m != nil {
		return m.MultiFellowType
	}
	return 0
}

func (m *FellowTypeInfo) GetMultiFellowName() string {
	if m != nil {
		return m.MultiFellowName
	}
	return ""
}

type GetFellowListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  int64        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowListReq) Reset()         { *m = GetFellowListReq{} }
func (m *GetFellowListReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowListReq) ProtoMessage()    {}
func (*GetFellowListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{10}
}
func (m *GetFellowListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowListReq.Unmarshal(m, b)
}
func (m *GetFellowListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowListReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowListReq.Merge(dst, src)
}
func (m *GetFellowListReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowListReq.Size(m)
}
func (m *GetFellowListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowListReq proto.InternalMessageInfo

func (m *GetFellowListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowListReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFellowListResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Uid                  int64          `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	UniqueFellow         *FellowInfo    `protobuf:"bytes,3,opt,name=unique_fellow,json=uniqueFellow,proto3" json:"unique_fellow,omitempty"`
	MultiFellow          []*FellowInfo  `protobuf:"bytes,4,rep,name=multi_fellow,json=multiFellow,proto3" json:"multi_fellow,omitempty"`
	FellowPositionCnt    uint32         `protobuf:"varint,5,opt,name=fellow_position_cnt,json=fellowPositionCnt,proto3" json:"fellow_position_cnt,omitempty"`
	PendingInviteCnt     uint32         `protobuf:"varint,6,opt,name=pending_invite_cnt,json=pendingInviteCnt,proto3" json:"pending_invite_cnt,omitempty"`
	UnlockInfo           *UnlockInfo    `protobuf:"bytes,7,opt,name=unlock_info,json=unlockInfo,proto3" json:"unlock_info,omitempty"`
	SendInviteCount      uint32         `protobuf:"varint,8,opt,name=send_invite_count,json=sendInviteCount,proto3" json:"send_invite_count,omitempty"`
	RareTabList          []*RareTabInfo `protobuf:"bytes,9,rep,name=rare_tab_list,json=rareTabList,proto3" json:"rare_tab_list,omitempty"`
	SvipVisible          bool           `protobuf:"varint,10,opt,name=svip_visible,json=svipVisible,proto3" json:"svip_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetFellowListResp) Reset()         { *m = GetFellowListResp{} }
func (m *GetFellowListResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowListResp) ProtoMessage()    {}
func (*GetFellowListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{11}
}
func (m *GetFellowListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowListResp.Unmarshal(m, b)
}
func (m *GetFellowListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowListResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowListResp.Merge(dst, src)
}
func (m *GetFellowListResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowListResp.Size(m)
}
func (m *GetFellowListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowListResp proto.InternalMessageInfo

func (m *GetFellowListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowListResp) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetFellowListResp) GetUniqueFellow() *FellowInfo {
	if m != nil {
		return m.UniqueFellow
	}
	return nil
}

func (m *GetFellowListResp) GetMultiFellow() []*FellowInfo {
	if m != nil {
		return m.MultiFellow
	}
	return nil
}

func (m *GetFellowListResp) GetFellowPositionCnt() uint32 {
	if m != nil {
		return m.FellowPositionCnt
	}
	return 0
}

func (m *GetFellowListResp) GetPendingInviteCnt() uint32 {
	if m != nil {
		return m.PendingInviteCnt
	}
	return 0
}

func (m *GetFellowListResp) GetUnlockInfo() *UnlockInfo {
	if m != nil {
		return m.UnlockInfo
	}
	return nil
}

func (m *GetFellowListResp) GetSendInviteCount() uint32 {
	if m != nil {
		return m.SendInviteCount
	}
	return 0
}

func (m *GetFellowListResp) GetRareTabList() []*RareTabInfo {
	if m != nil {
		return m.RareTabList
	}
	return nil
}

func (m *GetFellowListResp) GetSvipVisible() bool {
	if m != nil {
		return m.SvipVisible
	}
	return false
}

type UnlockInfo struct {
	UnlockPrice          uint32   `protobuf:"varint,1,opt,name=unlock_price,json=unlockPrice,proto3" json:"unlock_price,omitempty"`
	UnlockLevel          uint32   `protobuf:"varint,2,opt,name=unlock_level,json=unlockLevel,proto3" json:"unlock_level,omitempty"`
	UnlockSite           uint32   `protobuf:"varint,3,opt,name=unlock_site,json=unlockSite,proto3" json:"unlock_site,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnlockInfo) Reset()         { *m = UnlockInfo{} }
func (m *UnlockInfo) String() string { return proto.CompactTextString(m) }
func (*UnlockInfo) ProtoMessage()    {}
func (*UnlockInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{12}
}
func (m *UnlockInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlockInfo.Unmarshal(m, b)
}
func (m *UnlockInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlockInfo.Marshal(b, m, deterministic)
}
func (dst *UnlockInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlockInfo.Merge(dst, src)
}
func (m *UnlockInfo) XXX_Size() int {
	return xxx_messageInfo_UnlockInfo.Size(m)
}
func (m *UnlockInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlockInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UnlockInfo proto.InternalMessageInfo

func (m *UnlockInfo) GetUnlockPrice() uint32 {
	if m != nil {
		return m.UnlockPrice
	}
	return 0
}

func (m *UnlockInfo) GetUnlockLevel() uint32 {
	if m != nil {
		return m.UnlockLevel
	}
	return 0
}

func (m *UnlockInfo) GetUnlockSite() uint32 {
	if m != nil {
		return m.UnlockSite
	}
	return 0
}

type GetFellowCandidateListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	BindType             uint32       `protobuf:"varint,2,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	Page                 uint32       `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32       `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowCandidateListReq) Reset()         { *m = GetFellowCandidateListReq{} }
func (m *GetFellowCandidateListReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowCandidateListReq) ProtoMessage()    {}
func (*GetFellowCandidateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{13}
}
func (m *GetFellowCandidateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCandidateListReq.Unmarshal(m, b)
}
func (m *GetFellowCandidateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCandidateListReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowCandidateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCandidateListReq.Merge(dst, src)
}
func (m *GetFellowCandidateListReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowCandidateListReq.Size(m)
}
func (m *GetFellowCandidateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCandidateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCandidateListReq proto.InternalMessageInfo

func (m *GetFellowCandidateListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowCandidateListReq) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *GetFellowCandidateListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetFellowCandidateListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetFellowCandidateListResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FellowList           []*FellowInviteUser `protobuf:"bytes,2,rep,name=fellow_list,json=fellowList,proto3" json:"fellow_list,omitempty"`
	IsReachEnd           bool                `protobuf:"varint,3,opt,name=is_reach_end,json=isReachEnd,proto3" json:"is_reach_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetFellowCandidateListResp) Reset()         { *m = GetFellowCandidateListResp{} }
func (m *GetFellowCandidateListResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowCandidateListResp) ProtoMessage()    {}
func (*GetFellowCandidateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{14}
}
func (m *GetFellowCandidateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCandidateListResp.Unmarshal(m, b)
}
func (m *GetFellowCandidateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCandidateListResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowCandidateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCandidateListResp.Merge(dst, src)
}
func (m *GetFellowCandidateListResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowCandidateListResp.Size(m)
}
func (m *GetFellowCandidateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCandidateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCandidateListResp proto.InternalMessageInfo

func (m *GetFellowCandidateListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowCandidateListResp) GetFellowList() []*FellowInviteUser {
	if m != nil {
		return m.FellowList
	}
	return nil
}

func (m *GetFellowCandidateListResp) GetIsReachEnd() bool {
	if m != nil {
		return m.IsReachEnd
	}
	return false
}

type GetFellowCandidateInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChangeFellowType     bool         `protobuf:"varint,3,opt,name=change_fellow_type,json=changeFellowType,proto3" json:"change_fellow_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowCandidateInfoReq) Reset()         { *m = GetFellowCandidateInfoReq{} }
func (m *GetFellowCandidateInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowCandidateInfoReq) ProtoMessage()    {}
func (*GetFellowCandidateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{15}
}
func (m *GetFellowCandidateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCandidateInfoReq.Unmarshal(m, b)
}
func (m *GetFellowCandidateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCandidateInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowCandidateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCandidateInfoReq.Merge(dst, src)
}
func (m *GetFellowCandidateInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowCandidateInfoReq.Size(m)
}
func (m *GetFellowCandidateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCandidateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCandidateInfoReq proto.InternalMessageInfo

func (m *GetFellowCandidateInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowCandidateInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetFellowCandidateInfoReq) GetChangeFellowType() bool {
	if m != nil {
		return m.ChangeFellowType
	}
	return false
}

type GetFellowCandidateInfoResp struct {
	BaseResp              *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PresentInfo           []*FellowPresentInfo `protobuf:"bytes,2,rep,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	UserInfo              *FellowInviteUser    `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	MultiFellowOptionList []*FellowTypeInfo    `protobuf:"bytes,4,rep,name=multi_fellow_option_list,json=multiFellowOptionList,proto3" json:"multi_fellow_option_list,omitempty"`
	HasMultiFellowField   bool                 `protobuf:"varint,5,opt,name=has_multi_fellow_field,json=hasMultiFellowField,proto3" json:"has_multi_fellow_field,omitempty"`
	UnlockPrice           uint32               `protobuf:"varint,6,opt,name=unlock_price,json=unlockPrice,proto3" json:"unlock_price,omitempty"`
	HasCpField            bool                 `protobuf:"varint,7,opt,name=has_cp_field,json=hasCpField,proto3" json:"has_cp_field,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *GetFellowCandidateInfoResp) Reset()         { *m = GetFellowCandidateInfoResp{} }
func (m *GetFellowCandidateInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowCandidateInfoResp) ProtoMessage()    {}
func (*GetFellowCandidateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{16}
}
func (m *GetFellowCandidateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCandidateInfoResp.Unmarshal(m, b)
}
func (m *GetFellowCandidateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCandidateInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowCandidateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCandidateInfoResp.Merge(dst, src)
}
func (m *GetFellowCandidateInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowCandidateInfoResp.Size(m)
}
func (m *GetFellowCandidateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCandidateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCandidateInfoResp proto.InternalMessageInfo

func (m *GetFellowCandidateInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowCandidateInfoResp) GetPresentInfo() []*FellowPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *GetFellowCandidateInfoResp) GetUserInfo() *FellowInviteUser {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GetFellowCandidateInfoResp) GetMultiFellowOptionList() []*FellowTypeInfo {
	if m != nil {
		return m.MultiFellowOptionList
	}
	return nil
}

func (m *GetFellowCandidateInfoResp) GetHasMultiFellowField() bool {
	if m != nil {
		return m.HasMultiFellowField
	}
	return false
}

func (m *GetFellowCandidateInfoResp) GetUnlockPrice() uint32 {
	if m != nil {
		return m.UnlockPrice
	}
	return 0
}

func (m *GetFellowCandidateInfoResp) GetHasCpField() bool {
	if m != nil {
		return m.HasCpField
	}
	return false
}

type SendFellowInviteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	BindType             uint32       `protobuf:"varint,3,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	PresentId            uint32       `protobuf:"varint,4,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	FellowType           uint32       `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	WithUnlock           bool         `protobuf:"varint,6,opt,name=with_unlock,json=withUnlock,proto3" json:"with_unlock,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendFellowInviteReq) Reset()         { *m = SendFellowInviteReq{} }
func (m *SendFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*SendFellowInviteReq) ProtoMessage()    {}
func (*SendFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{17}
}
func (m *SendFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendFellowInviteReq.Unmarshal(m, b)
}
func (m *SendFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *SendFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendFellowInviteReq.Merge(dst, src)
}
func (m *SendFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_SendFellowInviteReq.Size(m)
}
func (m *SendFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendFellowInviteReq proto.InternalMessageInfo

func (m *SendFellowInviteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendFellowInviteReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendFellowInviteReq) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *SendFellowInviteReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *SendFellowInviteReq) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *SendFellowInviteReq) GetWithUnlock() bool {
	if m != nil {
		return m.WithUnlock
	}
	return false
}

type SendFellowInviteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainCurrency       int64         `protobuf:"varint,2,opt,name=remain_currency,json=remainCurrency,proto3" json:"remain_currency,omitempty"`
	RemainTbean          int64         `protobuf:"varint,3,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendFellowInviteResp) Reset()         { *m = SendFellowInviteResp{} }
func (m *SendFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*SendFellowInviteResp) ProtoMessage()    {}
func (*SendFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{18}
}
func (m *SendFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendFellowInviteResp.Unmarshal(m, b)
}
func (m *SendFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *SendFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendFellowInviteResp.Merge(dst, src)
}
func (m *SendFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_SendFellowInviteResp.Size(m)
}
func (m *SendFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendFellowInviteResp proto.InternalMessageInfo

func (m *SendFellowInviteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendFellowInviteResp) GetRemainCurrency() int64 {
	if m != nil {
		return m.RemainCurrency
	}
	return 0
}

func (m *SendFellowInviteResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

type GetFellowInviteListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowInviteListReq) Reset()         { *m = GetFellowInviteListReq{} }
func (m *GetFellowInviteListReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowInviteListReq) ProtoMessage()    {}
func (*GetFellowInviteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{19}
}
func (m *GetFellowInviteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInviteListReq.Unmarshal(m, b)
}
func (m *GetFellowInviteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInviteListReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowInviteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInviteListReq.Merge(dst, src)
}
func (m *GetFellowInviteListReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowInviteListReq.Size(m)
}
func (m *GetFellowInviteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInviteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInviteListReq proto.InternalMessageInfo

func (m *GetFellowInviteListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetFellowInviteListResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InviteList           []*FellowInviteInfo `protobuf:"bytes,2,rep,name=invite_list,json=inviteList,proto3" json:"invite_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetFellowInviteListResp) Reset()         { *m = GetFellowInviteListResp{} }
func (m *GetFellowInviteListResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowInviteListResp) ProtoMessage()    {}
func (*GetFellowInviteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{20}
}
func (m *GetFellowInviteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInviteListResp.Unmarshal(m, b)
}
func (m *GetFellowInviteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInviteListResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowInviteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInviteListResp.Merge(dst, src)
}
func (m *GetFellowInviteListResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowInviteListResp.Size(m)
}
func (m *GetFellowInviteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInviteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInviteListResp proto.InternalMessageInfo

func (m *GetFellowInviteListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowInviteListResp) GetInviteList() []*FellowInviteInfo {
	if m != nil {
		return m.InviteList
	}
	return nil
}

type GetFellowInviteInfoByIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	InviteId             string       `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowInviteInfoByIdReq) Reset()         { *m = GetFellowInviteInfoByIdReq{} }
func (m *GetFellowInviteInfoByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowInviteInfoByIdReq) ProtoMessage()    {}
func (*GetFellowInviteInfoByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{21}
}
func (m *GetFellowInviteInfoByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInviteInfoByIdReq.Unmarshal(m, b)
}
func (m *GetFellowInviteInfoByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInviteInfoByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowInviteInfoByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInviteInfoByIdReq.Merge(dst, src)
}
func (m *GetFellowInviteInfoByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowInviteInfoByIdReq.Size(m)
}
func (m *GetFellowInviteInfoByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInviteInfoByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInviteInfoByIdReq proto.InternalMessageInfo

func (m *GetFellowInviteInfoByIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowInviteInfoByIdReq) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

type GetFellowInviteInfoByIdResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InviteInfo           *FellowInviteInfo `protobuf:"bytes,2,opt,name=invite_info,json=inviteInfo,proto3" json:"invite_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetFellowInviteInfoByIdResp) Reset()         { *m = GetFellowInviteInfoByIdResp{} }
func (m *GetFellowInviteInfoByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowInviteInfoByIdResp) ProtoMessage()    {}
func (*GetFellowInviteInfoByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{22}
}
func (m *GetFellowInviteInfoByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInviteInfoByIdResp.Unmarshal(m, b)
}
func (m *GetFellowInviteInfoByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInviteInfoByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowInviteInfoByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInviteInfoByIdResp.Merge(dst, src)
}
func (m *GetFellowInviteInfoByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowInviteInfoByIdResp.Size(m)
}
func (m *GetFellowInviteInfoByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInviteInfoByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInviteInfoByIdResp proto.InternalMessageInfo

func (m *GetFellowInviteInfoByIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowInviteInfoByIdResp) GetInviteInfo() *FellowInviteInfo {
	if m != nil {
		return m.InviteInfo
	}
	return nil
}

type HandleFellowInviteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	InviteId             string       `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	IsAcceptInvite       bool         `protobuf:"varint,3,opt,name=is_accept_invite,json=isAcceptInvite,proto3" json:"is_accept_invite,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HandleFellowInviteReq) Reset()         { *m = HandleFellowInviteReq{} }
func (m *HandleFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*HandleFellowInviteReq) ProtoMessage()    {}
func (*HandleFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{23}
}
func (m *HandleFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleFellowInviteReq.Unmarshal(m, b)
}
func (m *HandleFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *HandleFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleFellowInviteReq.Merge(dst, src)
}
func (m *HandleFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_HandleFellowInviteReq.Size(m)
}
func (m *HandleFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleFellowInviteReq proto.InternalMessageInfo

func (m *HandleFellowInviteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HandleFellowInviteReq) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *HandleFellowInviteReq) GetIsAcceptInvite() bool {
	if m != nil {
		return m.IsAcceptInvite
	}
	return false
}

type HandleFellowInviteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HandleFellowInviteResp) Reset()         { *m = HandleFellowInviteResp{} }
func (m *HandleFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*HandleFellowInviteResp) ProtoMessage()    {}
func (*HandleFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{24}
}
func (m *HandleFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleFellowInviteResp.Unmarshal(m, b)
}
func (m *HandleFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *HandleFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleFellowInviteResp.Merge(dst, src)
}
func (m *HandleFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_HandleFellowInviteResp.Size(m)
}
func (m *HandleFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleFellowInviteResp proto.InternalMessageInfo

func (m *HandleFellowInviteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type CheckFellowInviteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	BindType             uint32       `protobuf:"varint,2,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckFellowInviteReq) Reset()         { *m = CheckFellowInviteReq{} }
func (m *CheckFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*CheckFellowInviteReq) ProtoMessage()    {}
func (*CheckFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{25}
}
func (m *CheckFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFellowInviteReq.Unmarshal(m, b)
}
func (m *CheckFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *CheckFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFellowInviteReq.Merge(dst, src)
}
func (m *CheckFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_CheckFellowInviteReq.Size(m)
}
func (m *CheckFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFellowInviteReq proto.InternalMessageInfo

func (m *CheckFellowInviteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckFellowInviteReq) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

type CheckFellowInviteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ReachLimit           bool          `protobuf:"varint,2,opt,name=reach_limit,json=reachLimit,proto3" json:"reach_limit,omitempty"`
	CpName               string        `protobuf:"bytes,3,opt,name=cp_name,json=cpName,proto3" json:"cp_name,omitempty"`
	CpSex                uint32        `protobuf:"varint,4,opt,name=cp_sex,json=cpSex,proto3" json:"cp_sex,omitempty"`
	UnlockInfo           *UnlockInfo   `protobuf:"bytes,5,opt,name=unlock_info,json=unlockInfo,proto3" json:"unlock_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckFellowInviteResp) Reset()         { *m = CheckFellowInviteResp{} }
func (m *CheckFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*CheckFellowInviteResp) ProtoMessage()    {}
func (*CheckFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{26}
}
func (m *CheckFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFellowInviteResp.Unmarshal(m, b)
}
func (m *CheckFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *CheckFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFellowInviteResp.Merge(dst, src)
}
func (m *CheckFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_CheckFellowInviteResp.Size(m)
}
func (m *CheckFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFellowInviteResp proto.InternalMessageInfo

func (m *CheckFellowInviteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckFellowInviteResp) GetReachLimit() bool {
	if m != nil {
		return m.ReachLimit
	}
	return false
}

func (m *CheckFellowInviteResp) GetCpName() string {
	if m != nil {
		return m.CpName
	}
	return ""
}

func (m *CheckFellowInviteResp) GetCpSex() uint32 {
	if m != nil {
		return m.CpSex
	}
	return 0
}

func (m *CheckFellowInviteResp) GetUnlockInfo() *UnlockInfo {
	if m != nil {
		return m.UnlockInfo
	}
	return nil
}

type UnlockFellowPositionReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UnlockFellowPositionReq) Reset()         { *m = UnlockFellowPositionReq{} }
func (m *UnlockFellowPositionReq) String() string { return proto.CompactTextString(m) }
func (*UnlockFellowPositionReq) ProtoMessage()    {}
func (*UnlockFellowPositionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{27}
}
func (m *UnlockFellowPositionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlockFellowPositionReq.Unmarshal(m, b)
}
func (m *UnlockFellowPositionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlockFellowPositionReq.Marshal(b, m, deterministic)
}
func (dst *UnlockFellowPositionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlockFellowPositionReq.Merge(dst, src)
}
func (m *UnlockFellowPositionReq) XXX_Size() int {
	return xxx_messageInfo_UnlockFellowPositionReq.Size(m)
}
func (m *UnlockFellowPositionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlockFellowPositionReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnlockFellowPositionReq proto.InternalMessageInfo

func (m *UnlockFellowPositionReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type UnlockFellowPositionResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainTbean          int64         `protobuf:"varint,2,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UnlockFellowPositionResp) Reset()         { *m = UnlockFellowPositionResp{} }
func (m *UnlockFellowPositionResp) String() string { return proto.CompactTextString(m) }
func (*UnlockFellowPositionResp) ProtoMessage()    {}
func (*UnlockFellowPositionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{28}
}
func (m *UnlockFellowPositionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnlockFellowPositionResp.Unmarshal(m, b)
}
func (m *UnlockFellowPositionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnlockFellowPositionResp.Marshal(b, m, deterministic)
}
func (dst *UnlockFellowPositionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnlockFellowPositionResp.Merge(dst, src)
}
func (m *UnlockFellowPositionResp) XXX_Size() int {
	return xxx_messageInfo_UnlockFellowPositionResp.Size(m)
}
func (m *UnlockFellowPositionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnlockFellowPositionResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnlockFellowPositionResp proto.InternalMessageInfo

func (m *UnlockFellowPositionResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UnlockFellowPositionResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

type UnboundFellowReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UnboundFellowReq) Reset()         { *m = UnboundFellowReq{} }
func (m *UnboundFellowReq) String() string { return proto.CompactTextString(m) }
func (*UnboundFellowReq) ProtoMessage()    {}
func (*UnboundFellowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{29}
}
func (m *UnboundFellowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnboundFellowReq.Unmarshal(m, b)
}
func (m *UnboundFellowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnboundFellowReq.Marshal(b, m, deterministic)
}
func (dst *UnboundFellowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnboundFellowReq.Merge(dst, src)
}
func (m *UnboundFellowReq) XXX_Size() int {
	return xxx_messageInfo_UnboundFellowReq.Size(m)
}
func (m *UnboundFellowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnboundFellowReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnboundFellowReq proto.InternalMessageInfo

func (m *UnboundFellowReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UnboundFellowReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type UnboundFellowResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UnboundFellowResp) Reset()         { *m = UnboundFellowResp{} }
func (m *UnboundFellowResp) String() string { return proto.CompactTextString(m) }
func (*UnboundFellowResp) ProtoMessage()    {}
func (*UnboundFellowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{30}
}
func (m *UnboundFellowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnboundFellowResp.Unmarshal(m, b)
}
func (m *UnboundFellowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnboundFellowResp.Marshal(b, m, deterministic)
}
func (dst *UnboundFellowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnboundFellowResp.Merge(dst, src)
}
func (m *UnboundFellowResp) XXX_Size() int {
	return xxx_messageInfo_UnboundFellowResp.Size(m)
}
func (m *UnboundFellowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnboundFellowResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnboundFellowResp proto.InternalMessageInfo

func (m *UnboundFellowResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type CancelUnboundFellowReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelUnboundFellowReq) Reset()         { *m = CancelUnboundFellowReq{} }
func (m *CancelUnboundFellowReq) String() string { return proto.CompactTextString(m) }
func (*CancelUnboundFellowReq) ProtoMessage()    {}
func (*CancelUnboundFellowReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{31}
}
func (m *CancelUnboundFellowReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelUnboundFellowReq.Unmarshal(m, b)
}
func (m *CancelUnboundFellowReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelUnboundFellowReq.Marshal(b, m, deterministic)
}
func (dst *CancelUnboundFellowReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelUnboundFellowReq.Merge(dst, src)
}
func (m *CancelUnboundFellowReq) XXX_Size() int {
	return xxx_messageInfo_CancelUnboundFellowReq.Size(m)
}
func (m *CancelUnboundFellowReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelUnboundFellowReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelUnboundFellowReq proto.InternalMessageInfo

func (m *CancelUnboundFellowReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelUnboundFellowReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type CancelUnboundFellowResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelUnboundFellowResp) Reset()         { *m = CancelUnboundFellowResp{} }
func (m *CancelUnboundFellowResp) String() string { return proto.CompactTextString(m) }
func (*CancelUnboundFellowResp) ProtoMessage()    {}
func (*CancelUnboundFellowResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{32}
}
func (m *CancelUnboundFellowResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelUnboundFellowResp.Unmarshal(m, b)
}
func (m *CancelUnboundFellowResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelUnboundFellowResp.Marshal(b, m, deterministic)
}
func (dst *CancelUnboundFellowResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelUnboundFellowResp.Merge(dst, src)
}
func (m *CancelUnboundFellowResp) XXX_Size() int {
	return xxx_messageInfo_CancelUnboundFellowResp.Size(m)
}
func (m *CancelUnboundFellowResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelUnboundFellowResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelUnboundFellowResp proto.InternalMessageInfo

func (m *CancelUnboundFellowResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type CancelFellowInviteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	InviteId             string       `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelFellowInviteReq) Reset()         { *m = CancelFellowInviteReq{} }
func (m *CancelFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*CancelFellowInviteReq) ProtoMessage()    {}
func (*CancelFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{33}
}
func (m *CancelFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelFellowInviteReq.Unmarshal(m, b)
}
func (m *CancelFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *CancelFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelFellowInviteReq.Merge(dst, src)
}
func (m *CancelFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_CancelFellowInviteReq.Size(m)
}
func (m *CancelFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelFellowInviteReq proto.InternalMessageInfo

func (m *CancelFellowInviteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelFellowInviteReq) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

type CancelFellowInviteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainCurrency       int64         `protobuf:"varint,2,opt,name=remain_currency,json=remainCurrency,proto3" json:"remain_currency,omitempty"`
	RemainTbean          int64         `protobuf:"varint,3,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelFellowInviteResp) Reset()         { *m = CancelFellowInviteResp{} }
func (m *CancelFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*CancelFellowInviteResp) ProtoMessage()    {}
func (*CancelFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{34}
}
func (m *CancelFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelFellowInviteResp.Unmarshal(m, b)
}
func (m *CancelFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *CancelFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelFellowInviteResp.Merge(dst, src)
}
func (m *CancelFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_CancelFellowInviteResp.Size(m)
}
func (m *CancelFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelFellowInviteResp proto.InternalMessageInfo

func (m *CancelFellowInviteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CancelFellowInviteResp) GetRemainCurrency() int64 {
	if m != nil {
		return m.RemainCurrency
	}
	return 0
}

func (m *CancelFellowInviteResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

type GetFellowPointReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FellowAccount        string       `protobuf:"bytes,2,opt,name=fellow_account,json=fellowAccount,proto3" json:"fellow_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowPointReq) Reset()         { *m = GetFellowPointReq{} }
func (m *GetFellowPointReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowPointReq) ProtoMessage()    {}
func (*GetFellowPointReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{35}
}
func (m *GetFellowPointReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPointReq.Unmarshal(m, b)
}
func (m *GetFellowPointReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPointReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowPointReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPointReq.Merge(dst, src)
}
func (m *GetFellowPointReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowPointReq.Size(m)
}
func (m *GetFellowPointReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPointReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPointReq proto.InternalMessageInfo

func (m *GetFellowPointReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowPointReq) GetFellowAccount() string {
	if m != nil {
		return m.FellowAccount
	}
	return ""
}

type GetFellowPointResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FellowUid            uint32        `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	FellowPoint          uint32        `protobuf:"varint,3,opt,name=fellow_point,json=fellowPoint,proto3" json:"fellow_point,omitempty"`
	FellowLevel          uint32        `protobuf:"varint,4,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level,omitempty"`
	CurrentLevelPoint    uint32        `protobuf:"varint,5,opt,name=current_level_point,json=currentLevelPoint,proto3" json:"current_level_point,omitempty"`
	NextLevelPoint       uint32        `protobuf:"varint,6,opt,name=next_level_point,json=nextLevelPoint,proto3" json:"next_level_point,omitempty"`
	NextAwardLevel       string        `protobuf:"bytes,7,opt,name=next_award_level,json=nextAwardLevel,proto3" json:"next_award_level,omitempty"`
	AvailableAward       string        `protobuf:"bytes,8,opt,name=available_award,json=availableAward,proto3" json:"available_award,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetFellowPointResp) Reset()         { *m = GetFellowPointResp{} }
func (m *GetFellowPointResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowPointResp) ProtoMessage()    {}
func (*GetFellowPointResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{36}
}
func (m *GetFellowPointResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPointResp.Unmarshal(m, b)
}
func (m *GetFellowPointResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPointResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowPointResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPointResp.Merge(dst, src)
}
func (m *GetFellowPointResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowPointResp.Size(m)
}
func (m *GetFellowPointResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPointResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPointResp proto.InternalMessageInfo

func (m *GetFellowPointResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowPointResp) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *GetFellowPointResp) GetFellowPoint() uint32 {
	if m != nil {
		return m.FellowPoint
	}
	return 0
}

func (m *GetFellowPointResp) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *GetFellowPointResp) GetCurrentLevelPoint() uint32 {
	if m != nil {
		return m.CurrentLevelPoint
	}
	return 0
}

func (m *GetFellowPointResp) GetNextLevelPoint() uint32 {
	if m != nil {
		return m.NextLevelPoint
	}
	return 0
}

func (m *GetFellowPointResp) GetNextAwardLevel() string {
	if m != nil {
		return m.NextAwardLevel
	}
	return ""
}

func (m *GetFellowPointResp) GetAvailableAward() string {
	if m != nil {
		return m.AvailableAward
	}
	return ""
}

type GetFellowPresentDetailReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowPresentDetailReq) Reset()         { *m = GetFellowPresentDetailReq{} }
func (m *GetFellowPresentDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowPresentDetailReq) ProtoMessage()    {}
func (*GetFellowPresentDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{37}
}
func (m *GetFellowPresentDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPresentDetailReq.Unmarshal(m, b)
}
func (m *GetFellowPresentDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPresentDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowPresentDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPresentDetailReq.Merge(dst, src)
}
func (m *GetFellowPresentDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowPresentDetailReq.Size(m)
}
func (m *GetFellowPresentDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPresentDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPresentDetailReq proto.InternalMessageInfo

func (m *GetFellowPresentDetailReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowPresentDetailReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetFellowPresentDetailResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PresentList          []*FellowPresentInfo `protobuf:"bytes,2,rep,name=present_list,json=presentList,proto3" json:"present_list,omitempty"`
	FellowPresent        *FellowPresentInfo   `protobuf:"bytes,3,opt,name=fellow_present,json=fellowPresent,proto3" json:"fellow_present,omitempty"`
	FellowLevel          uint32               `protobuf:"varint,4,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level,omitempty"`
	FellowName           string               `protobuf:"bytes,5,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	DayCnt               uint32               `protobuf:"varint,6,opt,name=day_cnt,json=dayCnt,proto3" json:"day_cnt,omitempty"`
	BindType             uint32               `protobuf:"varint,7,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetFellowPresentDetailResp) Reset()         { *m = GetFellowPresentDetailResp{} }
func (m *GetFellowPresentDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowPresentDetailResp) ProtoMessage()    {}
func (*GetFellowPresentDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{38}
}
func (m *GetFellowPresentDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowPresentDetailResp.Unmarshal(m, b)
}
func (m *GetFellowPresentDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowPresentDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowPresentDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowPresentDetailResp.Merge(dst, src)
}
func (m *GetFellowPresentDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowPresentDetailResp.Size(m)
}
func (m *GetFellowPresentDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowPresentDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowPresentDetailResp proto.InternalMessageInfo

func (m *GetFellowPresentDetailResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowPresentDetailResp) GetPresentList() []*FellowPresentInfo {
	if m != nil {
		return m.PresentList
	}
	return nil
}

func (m *GetFellowPresentDetailResp) GetFellowPresent() *FellowPresentInfo {
	if m != nil {
		return m.FellowPresent
	}
	return nil
}

func (m *GetFellowPresentDetailResp) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *GetFellowPresentDetailResp) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *GetFellowPresentDetailResp) GetDayCnt() uint32 {
	if m != nil {
		return m.DayCnt
	}
	return 0
}

func (m *GetFellowPresentDetailResp) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

type SendFellowPresentReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	PresentId            uint32       `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendFellowPresentReq) Reset()         { *m = SendFellowPresentReq{} }
func (m *SendFellowPresentReq) String() string { return proto.CompactTextString(m) }
func (*SendFellowPresentReq) ProtoMessage()    {}
func (*SendFellowPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{39}
}
func (m *SendFellowPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendFellowPresentReq.Unmarshal(m, b)
}
func (m *SendFellowPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendFellowPresentReq.Marshal(b, m, deterministic)
}
func (dst *SendFellowPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendFellowPresentReq.Merge(dst, src)
}
func (m *SendFellowPresentReq) XXX_Size() int {
	return xxx_messageInfo_SendFellowPresentReq.Size(m)
}
func (m *SendFellowPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendFellowPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendFellowPresentReq proto.InternalMessageInfo

func (m *SendFellowPresentReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendFellowPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendFellowPresentReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

type SendFellowPresentResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainCurrency       int64                      `protobuf:"varint,2,opt,name=remain_currency,json=remainCurrency,proto3" json:"remain_currency,omitempty"`
	RemainTbean          int64                      `protobuf:"varint,3,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	PresentInfo          *PresentSendItemSimpleInfo `protobuf:"bytes,4,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *SendFellowPresentResp) Reset()         { *m = SendFellowPresentResp{} }
func (m *SendFellowPresentResp) String() string { return proto.CompactTextString(m) }
func (*SendFellowPresentResp) ProtoMessage()    {}
func (*SendFellowPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{40}
}
func (m *SendFellowPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendFellowPresentResp.Unmarshal(m, b)
}
func (m *SendFellowPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendFellowPresentResp.Marshal(b, m, deterministic)
}
func (dst *SendFellowPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendFellowPresentResp.Merge(dst, src)
}
func (m *SendFellowPresentResp) XXX_Size() int {
	return xxx_messageInfo_SendFellowPresentResp.Size(m)
}
func (m *SendFellowPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendFellowPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendFellowPresentResp proto.InternalMessageInfo

func (m *SendFellowPresentResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendFellowPresentResp) GetRemainCurrency() int64 {
	if m != nil {
		return m.RemainCurrency
	}
	return 0
}

func (m *SendFellowPresentResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

func (m *SendFellowPresentResp) GetPresentInfo() *PresentSendItemSimpleInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

type PresentSendItemSimpleInfo struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	ShowEffect           uint32   `protobuf:"varint,3,opt,name=show_effect,json=showEffect,proto3" json:"show_effect,omitempty"`
	ShowEffectV2         uint32   `protobuf:"varint,4,opt,name=show_effect_v2,json=showEffectV2,proto3" json:"show_effect_v2,omitempty"`
	PresentIcon          string   `protobuf:"bytes,5,opt,name=present_icon,json=presentIcon,proto3" json:"present_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentSendItemSimpleInfo) Reset()         { *m = PresentSendItemSimpleInfo{} }
func (m *PresentSendItemSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*PresentSendItemSimpleInfo) ProtoMessage()    {}
func (*PresentSendItemSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{41}
}
func (m *PresentSendItemSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentSendItemSimpleInfo.Unmarshal(m, b)
}
func (m *PresentSendItemSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentSendItemSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *PresentSendItemSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentSendItemSimpleInfo.Merge(dst, src)
}
func (m *PresentSendItemSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_PresentSendItemSimpleInfo.Size(m)
}
func (m *PresentSendItemSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentSendItemSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentSendItemSimpleInfo proto.InternalMessageInfo

func (m *PresentSendItemSimpleInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *PresentSendItemSimpleInfo) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *PresentSendItemSimpleInfo) GetShowEffect() uint32 {
	if m != nil {
		return m.ShowEffect
	}
	return 0
}

func (m *PresentSendItemSimpleInfo) GetShowEffectV2() uint32 {
	if m != nil {
		return m.ShowEffectV2
	}
	return 0
}

func (m *PresentSendItemSimpleInfo) GetPresentIcon() string {
	if m != nil {
		return m.PresentIcon
	}
	return ""
}

type GetFellowInfoByUidReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowInfoByUidReq) Reset()         { *m = GetFellowInfoByUidReq{} }
func (m *GetFellowInfoByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetFellowInfoByUidReq) ProtoMessage()    {}
func (*GetFellowInfoByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{42}
}
func (m *GetFellowInfoByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInfoByUidReq.Unmarshal(m, b)
}
func (m *GetFellowInfoByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInfoByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetFellowInfoByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInfoByUidReq.Merge(dst, src)
}
func (m *GetFellowInfoByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetFellowInfoByUidReq.Size(m)
}
func (m *GetFellowInfoByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInfoByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInfoByUidReq proto.InternalMessageInfo

func (m *GetFellowInfoByUidReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowInfoByUidReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetFellowInfoByUidResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FellowInfo           *FellowInfo   `protobuf:"bytes,2,opt,name=fellow_info,json=fellowInfo,proto3" json:"fellow_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetFellowInfoByUidResp) Reset()         { *m = GetFellowInfoByUidResp{} }
func (m *GetFellowInfoByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetFellowInfoByUidResp) ProtoMessage()    {}
func (*GetFellowInfoByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{43}
}
func (m *GetFellowInfoByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowInfoByUidResp.Unmarshal(m, b)
}
func (m *GetFellowInfoByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowInfoByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetFellowInfoByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowInfoByUidResp.Merge(dst, src)
}
func (m *GetFellowInfoByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetFellowInfoByUidResp.Size(m)
}
func (m *GetFellowInfoByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowInfoByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowInfoByUidResp proto.InternalMessageInfo

func (m *GetFellowInfoByUidResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowInfoByUidResp) GetFellowInfo() *FellowInfo {
	if m != nil {
		return m.FellowInfo
	}
	return nil
}

type ChannelSendFellowPresentReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	PresentId            uint32       `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelSendFellowPresentReq) Reset()         { *m = ChannelSendFellowPresentReq{} }
func (m *ChannelSendFellowPresentReq) String() string { return proto.CompactTextString(m) }
func (*ChannelSendFellowPresentReq) ProtoMessage()    {}
func (*ChannelSendFellowPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{44}
}
func (m *ChannelSendFellowPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSendFellowPresentReq.Unmarshal(m, b)
}
func (m *ChannelSendFellowPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSendFellowPresentReq.Marshal(b, m, deterministic)
}
func (dst *ChannelSendFellowPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSendFellowPresentReq.Merge(dst, src)
}
func (m *ChannelSendFellowPresentReq) XXX_Size() int {
	return xxx_messageInfo_ChannelSendFellowPresentReq.Size(m)
}
func (m *ChannelSendFellowPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSendFellowPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSendFellowPresentReq proto.InternalMessageInfo

func (m *ChannelSendFellowPresentReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelSendFellowPresentReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ChannelSendFellowPresentReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *ChannelSendFellowPresentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelSendFellowPresentResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainCurrency       int64         `protobuf:"varint,2,opt,name=remain_currency,json=remainCurrency,proto3" json:"remain_currency,omitempty"`
	RemainTbean          int64         `protobuf:"varint,3,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelSendFellowPresentResp) Reset()         { *m = ChannelSendFellowPresentResp{} }
func (m *ChannelSendFellowPresentResp) String() string { return proto.CompactTextString(m) }
func (*ChannelSendFellowPresentResp) ProtoMessage()    {}
func (*ChannelSendFellowPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{45}
}
func (m *ChannelSendFellowPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSendFellowPresentResp.Unmarshal(m, b)
}
func (m *ChannelSendFellowPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSendFellowPresentResp.Marshal(b, m, deterministic)
}
func (dst *ChannelSendFellowPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSendFellowPresentResp.Merge(dst, src)
}
func (m *ChannelSendFellowPresentResp) XXX_Size() int {
	return xxx_messageInfo_ChannelSendFellowPresentResp.Size(m)
}
func (m *ChannelSendFellowPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSendFellowPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSendFellowPresentResp proto.InternalMessageInfo

func (m *ChannelSendFellowPresentResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelSendFellowPresentResp) GetRemainCurrency() int64 {
	if m != nil {
		return m.RemainCurrency
	}
	return 0
}

func (m *ChannelSendFellowPresentResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

type SendChannelFellowInviteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	BindType             uint32       `protobuf:"varint,3,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	PresentId            uint32       `protobuf:"varint,4,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	FellowType           uint32       `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	WithUnlock           bool         `protobuf:"varint,6,opt,name=with_unlock,json=withUnlock,proto3" json:"with_unlock,omitempty"`
	ChannelId            uint32       `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendChannelFellowInviteReq) Reset()         { *m = SendChannelFellowInviteReq{} }
func (m *SendChannelFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*SendChannelFellowInviteReq) ProtoMessage()    {}
func (*SendChannelFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{46}
}
func (m *SendChannelFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelFellowInviteReq.Unmarshal(m, b)
}
func (m *SendChannelFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *SendChannelFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelFellowInviteReq.Merge(dst, src)
}
func (m *SendChannelFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_SendChannelFellowInviteReq.Size(m)
}
func (m *SendChannelFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelFellowInviteReq proto.InternalMessageInfo

func (m *SendChannelFellowInviteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendChannelFellowInviteReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendChannelFellowInviteReq) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *SendChannelFellowInviteReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *SendChannelFellowInviteReq) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *SendChannelFellowInviteReq) GetWithUnlock() bool {
	if m != nil {
		return m.WithUnlock
	}
	return false
}

func (m *SendChannelFellowInviteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SendChannelFellowInviteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainCurrency       int64         `protobuf:"varint,2,opt,name=remain_currency,json=remainCurrency,proto3" json:"remain_currency,omitempty"`
	RemainTbean          int64         `protobuf:"varint,3,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendChannelFellowInviteResp) Reset()         { *m = SendChannelFellowInviteResp{} }
func (m *SendChannelFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*SendChannelFellowInviteResp) ProtoMessage()    {}
func (*SendChannelFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{47}
}
func (m *SendChannelFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendChannelFellowInviteResp.Unmarshal(m, b)
}
func (m *SendChannelFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendChannelFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *SendChannelFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendChannelFellowInviteResp.Merge(dst, src)
}
func (m *SendChannelFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_SendChannelFellowInviteResp.Size(m)
}
func (m *SendChannelFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendChannelFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendChannelFellowInviteResp proto.InternalMessageInfo

func (m *SendChannelFellowInviteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendChannelFellowInviteResp) GetRemainCurrency() int64 {
	if m != nil {
		return m.RemainCurrency
	}
	return 0
}

func (m *SendChannelFellowInviteResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

type HandleChannelFellowInviteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	InviteId             string       `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	IsAcceptInvite       bool         `protobuf:"varint,3,opt,name=is_accept_invite,json=isAcceptInvite,proto3" json:"is_accept_invite,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HandleChannelFellowInviteReq) Reset()         { *m = HandleChannelFellowInviteReq{} }
func (m *HandleChannelFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*HandleChannelFellowInviteReq) ProtoMessage()    {}
func (*HandleChannelFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{48}
}
func (m *HandleChannelFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleChannelFellowInviteReq.Unmarshal(m, b)
}
func (m *HandleChannelFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleChannelFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *HandleChannelFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleChannelFellowInviteReq.Merge(dst, src)
}
func (m *HandleChannelFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_HandleChannelFellowInviteReq.Size(m)
}
func (m *HandleChannelFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleChannelFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_HandleChannelFellowInviteReq proto.InternalMessageInfo

func (m *HandleChannelFellowInviteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HandleChannelFellowInviteReq) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

func (m *HandleChannelFellowInviteReq) GetIsAcceptInvite() bool {
	if m != nil {
		return m.IsAcceptInvite
	}
	return false
}

func (m *HandleChannelFellowInviteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type HandleChannelFellowInviteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HandleChannelFellowInviteResp) Reset()         { *m = HandleChannelFellowInviteResp{} }
func (m *HandleChannelFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*HandleChannelFellowInviteResp) ProtoMessage()    {}
func (*HandleChannelFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{49}
}
func (m *HandleChannelFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleChannelFellowInviteResp.Unmarshal(m, b)
}
func (m *HandleChannelFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleChannelFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *HandleChannelFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleChannelFellowInviteResp.Merge(dst, src)
}
func (m *HandleChannelFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_HandleChannelFellowInviteResp.Size(m)
}
func (m *HandleChannelFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleChannelFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_HandleChannelFellowInviteResp proto.InternalMessageInfo

func (m *HandleChannelFellowInviteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 邀请函信息
type ChannelFellowMsg struct {
	FromUid              int64              `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromAccount          string             `protobuf:"bytes,2,opt,name=from_account,json=fromAccount,proto3" json:"from_account,omitempty"`
	FromNickname         string             `protobuf:"bytes,3,opt,name=from_nickname,json=fromNickname,proto3" json:"from_nickname,omitempty"`
	BindType             uint32             `protobuf:"varint,4,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32             `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowName           string             `protobuf:"bytes,6,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	ToUid                int64              `protobuf:"varint,7,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToAccount            string             `protobuf:"bytes,8,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ToNickname           string             `protobuf:"bytes,9,opt,name=to_nickname,json=toNickname,proto3" json:"to_nickname,omitempty"`
	FromSex              uint32             `protobuf:"varint,10,opt,name=from_sex,json=fromSex,proto3" json:"from_sex,omitempty"`
	ToSex                uint32             `protobuf:"varint,11,opt,name=to_sex,json=toSex,proto3" json:"to_sex,omitempty"`
	PresentInfo          *FellowPresentInfo `protobuf:"bytes,12,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	Status               uint32             `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ChannelFellowMsg) Reset()         { *m = ChannelFellowMsg{} }
func (m *ChannelFellowMsg) String() string { return proto.CompactTextString(m) }
func (*ChannelFellowMsg) ProtoMessage()    {}
func (*ChannelFellowMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{50}
}
func (m *ChannelFellowMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelFellowMsg.Unmarshal(m, b)
}
func (m *ChannelFellowMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelFellowMsg.Marshal(b, m, deterministic)
}
func (dst *ChannelFellowMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelFellowMsg.Merge(dst, src)
}
func (m *ChannelFellowMsg) XXX_Size() int {
	return xxx_messageInfo_ChannelFellowMsg.Size(m)
}
func (m *ChannelFellowMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelFellowMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelFellowMsg proto.InternalMessageInfo

func (m *ChannelFellowMsg) GetFromUid() int64 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *ChannelFellowMsg) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *ChannelFellowMsg) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *ChannelFellowMsg) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *ChannelFellowMsg) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *ChannelFellowMsg) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *ChannelFellowMsg) GetToUid() int64 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *ChannelFellowMsg) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *ChannelFellowMsg) GetToNickname() string {
	if m != nil {
		return m.ToNickname
	}
	return ""
}

func (m *ChannelFellowMsg) GetFromSex() uint32 {
	if m != nil {
		return m.FromSex
	}
	return 0
}

func (m *ChannelFellowMsg) GetToSex() uint32 {
	if m != nil {
		return m.ToSex
	}
	return 0
}

func (m *ChannelFellowMsg) GetPresentInfo() *FellowPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *ChannelFellowMsg) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetRoomFellowListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	NextUid              []uint32     `protobuf:"varint,3,rep,packed,name=next_uid,json=nextUid,proto3" json:"next_uid,omitempty"`
	OnMicUid             []uint32     `protobuf:"varint,4,rep,packed,name=on_mic_uid,json=onMicUid,proto3" json:"on_mic_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRoomFellowListReq) Reset()         { *m = GetRoomFellowListReq{} }
func (m *GetRoomFellowListReq) String() string { return proto.CompactTextString(m) }
func (*GetRoomFellowListReq) ProtoMessage()    {}
func (*GetRoomFellowListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{51}
}
func (m *GetRoomFellowListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomFellowListReq.Unmarshal(m, b)
}
func (m *GetRoomFellowListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomFellowListReq.Marshal(b, m, deterministic)
}
func (dst *GetRoomFellowListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomFellowListReq.Merge(dst, src)
}
func (m *GetRoomFellowListReq) XXX_Size() int {
	return xxx_messageInfo_GetRoomFellowListReq.Size(m)
}
func (m *GetRoomFellowListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomFellowListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomFellowListReq proto.InternalMessageInfo

func (m *GetRoomFellowListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRoomFellowListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRoomFellowListReq) GetNextUid() []uint32 {
	if m != nil {
		return m.NextUid
	}
	return nil
}

func (m *GetRoomFellowListReq) GetOnMicUid() []uint32 {
	if m != nil {
		return m.OnMicUid
	}
	return nil
}

type GetRoomFellowListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	FellowList           []*FellowInfo `protobuf:"bytes,3,rep,name=fellow_list,json=fellowList,proto3" json:"fellow_list,omitempty"`
	SvipVisible          bool          `protobuf:"varint,4,opt,name=svip_visible,json=svipVisible,proto3" json:"svip_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRoomFellowListResp) Reset()         { *m = GetRoomFellowListResp{} }
func (m *GetRoomFellowListResp) String() string { return proto.CompactTextString(m) }
func (*GetRoomFellowListResp) ProtoMessage()    {}
func (*GetRoomFellowListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{52}
}
func (m *GetRoomFellowListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomFellowListResp.Unmarshal(m, b)
}
func (m *GetRoomFellowListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomFellowListResp.Marshal(b, m, deterministic)
}
func (dst *GetRoomFellowListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomFellowListResp.Merge(dst, src)
}
func (m *GetRoomFellowListResp) XXX_Size() int {
	return xxx_messageInfo_GetRoomFellowListResp.Size(m)
}
func (m *GetRoomFellowListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomFellowListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomFellowListResp proto.InternalMessageInfo

func (m *GetRoomFellowListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRoomFellowListResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRoomFellowListResp) GetFellowList() []*FellowInfo {
	if m != nil {
		return m.FellowList
	}
	return nil
}

func (m *GetRoomFellowListResp) GetSvipVisible() bool {
	if m != nil {
		return m.SvipVisible
	}
	return false
}

type GetAllChannelFellowInviteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllChannelFellowInviteReq) Reset()         { *m = GetAllChannelFellowInviteReq{} }
func (m *GetAllChannelFellowInviteReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelFellowInviteReq) ProtoMessage()    {}
func (*GetAllChannelFellowInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{53}
}
func (m *GetAllChannelFellowInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelFellowInviteReq.Unmarshal(m, b)
}
func (m *GetAllChannelFellowInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelFellowInviteReq.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelFellowInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelFellowInviteReq.Merge(dst, src)
}
func (m *GetAllChannelFellowInviteReq) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelFellowInviteReq.Size(m)
}
func (m *GetAllChannelFellowInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelFellowInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelFellowInviteReq proto.InternalMessageInfo

func (m *GetAllChannelFellowInviteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAllChannelFellowInviteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetAllChannelFellowInviteResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SendInviteList       []*FellowInviteInfo `protobuf:"bytes,2,rep,name=send_invite_list,json=sendInviteList,proto3" json:"send_invite_list,omitempty"`
	ReceivedInviteList   []*FellowInviteInfo `protobuf:"bytes,3,rep,name=received_invite_list,json=receivedInviteList,proto3" json:"received_invite_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllChannelFellowInviteResp) Reset()         { *m = GetAllChannelFellowInviteResp{} }
func (m *GetAllChannelFellowInviteResp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelFellowInviteResp) ProtoMessage()    {}
func (*GetAllChannelFellowInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{54}
}
func (m *GetAllChannelFellowInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelFellowInviteResp.Unmarshal(m, b)
}
func (m *GetAllChannelFellowInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelFellowInviteResp.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelFellowInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelFellowInviteResp.Merge(dst, src)
}
func (m *GetAllChannelFellowInviteResp) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelFellowInviteResp.Size(m)
}
func (m *GetAllChannelFellowInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelFellowInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelFellowInviteResp proto.InternalMessageInfo

func (m *GetAllChannelFellowInviteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllChannelFellowInviteResp) GetSendInviteList() []*FellowInviteInfo {
	if m != nil {
		return m.SendInviteList
	}
	return nil
}

func (m *GetAllChannelFellowInviteResp) GetReceivedInviteList() []*FellowInviteInfo {
	if m != nil {
		return m.ReceivedInviteList
	}
	return nil
}

type GetChannelFellowCandidateInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ItemId               uint32       `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelFellowCandidateInfoReq) Reset()         { *m = GetChannelFellowCandidateInfoReq{} }
func (m *GetChannelFellowCandidateInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelFellowCandidateInfoReq) ProtoMessage()    {}
func (*GetChannelFellowCandidateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{55}
}
func (m *GetChannelFellowCandidateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFellowCandidateInfoReq.Unmarshal(m, b)
}
func (m *GetChannelFellowCandidateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFellowCandidateInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelFellowCandidateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFellowCandidateInfoReq.Merge(dst, src)
}
func (m *GetChannelFellowCandidateInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelFellowCandidateInfoReq.Size(m)
}
func (m *GetChannelFellowCandidateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFellowCandidateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFellowCandidateInfoReq proto.InternalMessageInfo

func (m *GetChannelFellowCandidateInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelFellowCandidateInfoReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetChannelFellowCandidateInfoReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

// 可选的关系信息
type FellowOptionInfo struct {
	BindType             uint32   `protobuf:"varint,1,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32   `protobuf:"varint,2,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowName           string   `protobuf:"bytes,3,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowOptionInfo) Reset()         { *m = FellowOptionInfo{} }
func (m *FellowOptionInfo) String() string { return proto.CompactTextString(m) }
func (*FellowOptionInfo) ProtoMessage()    {}
func (*FellowOptionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{56}
}
func (m *FellowOptionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowOptionInfo.Unmarshal(m, b)
}
func (m *FellowOptionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowOptionInfo.Marshal(b, m, deterministic)
}
func (dst *FellowOptionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowOptionInfo.Merge(dst, src)
}
func (m *FellowOptionInfo) XXX_Size() int {
	return xxx_messageInfo_FellowOptionInfo.Size(m)
}
func (m *FellowOptionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowOptionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowOptionInfo proto.InternalMessageInfo

func (m *FellowOptionInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *FellowOptionInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *FellowOptionInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

type GetChannelFellowCandidateInfoResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PresentInfo          *FellowPresentInfo  `protobuf:"bytes,2,opt,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	UserInfo             *FellowInviteUser   `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	FellowOptionList     []*FellowOptionInfo `protobuf:"bytes,4,rep,name=fellow_option_list,json=fellowOptionList,proto3" json:"fellow_option_list,omitempty"`
	HasMultiFellowField  bool                `protobuf:"varint,5,opt,name=has_multi_fellow_field,json=hasMultiFellowField,proto3" json:"has_multi_fellow_field,omitempty"`
	UnlockPrice          uint32              `protobuf:"varint,6,opt,name=unlock_price,json=unlockPrice,proto3" json:"unlock_price,omitempty"`
	HasCpField           bool                `protobuf:"varint,7,opt,name=has_cp_field,json=hasCpField,proto3" json:"has_cp_field,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetChannelFellowCandidateInfoResp) Reset()         { *m = GetChannelFellowCandidateInfoResp{} }
func (m *GetChannelFellowCandidateInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelFellowCandidateInfoResp) ProtoMessage()    {}
func (*GetChannelFellowCandidateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{57}
}
func (m *GetChannelFellowCandidateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelFellowCandidateInfoResp.Unmarshal(m, b)
}
func (m *GetChannelFellowCandidateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelFellowCandidateInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelFellowCandidateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelFellowCandidateInfoResp.Merge(dst, src)
}
func (m *GetChannelFellowCandidateInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelFellowCandidateInfoResp.Size(m)
}
func (m *GetChannelFellowCandidateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelFellowCandidateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelFellowCandidateInfoResp proto.InternalMessageInfo

func (m *GetChannelFellowCandidateInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelFellowCandidateInfoResp) GetPresentInfo() *FellowPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *GetChannelFellowCandidateInfoResp) GetUserInfo() *FellowInviteUser {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *GetChannelFellowCandidateInfoResp) GetFellowOptionList() []*FellowOptionInfo {
	if m != nil {
		return m.FellowOptionList
	}
	return nil
}

func (m *GetChannelFellowCandidateInfoResp) GetHasMultiFellowField() bool {
	if m != nil {
		return m.HasMultiFellowField
	}
	return false
}

func (m *GetChannelFellowCandidateInfoResp) GetUnlockPrice() uint32 {
	if m != nil {
		return m.UnlockPrice
	}
	return 0
}

func (m *GetChannelFellowCandidateInfoResp) GetHasCpField() bool {
	if m != nil {
		return m.HasCpField
	}
	return false
}

type GetOnMicFellowListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOnMicFellowListReq) Reset()         { *m = GetOnMicFellowListReq{} }
func (m *GetOnMicFellowListReq) String() string { return proto.CompactTextString(m) }
func (*GetOnMicFellowListReq) ProtoMessage()    {}
func (*GetOnMicFellowListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{58}
}
func (m *GetOnMicFellowListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnMicFellowListReq.Unmarshal(m, b)
}
func (m *GetOnMicFellowListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnMicFellowListReq.Marshal(b, m, deterministic)
}
func (dst *GetOnMicFellowListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnMicFellowListReq.Merge(dst, src)
}
func (m *GetOnMicFellowListReq) XXX_Size() int {
	return xxx_messageInfo_GetOnMicFellowListReq.Size(m)
}
func (m *GetOnMicFellowListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnMicFellowListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnMicFellowListReq proto.InternalMessageInfo

func (m *GetOnMicFellowListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetOnMicFellowListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 麦位挚友信息
type MicFellowInfo struct {
	Uid                  uint32    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FellowUid            uint32    `protobuf:"varint,2,opt,name=fellow_uid,json=fellowUid,proto3" json:"fellow_uid,omitempty"`
	FellowLevel          uint32    `protobuf:"varint,3,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level,omitempty"`
	FellowType           uint32    `protobuf:"varint,4,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	BindType             uint32    `protobuf:"varint,5,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	CurrentRare          *RareInfo `protobuf:"bytes,6,opt,name=current_rare,json=currentRare,proto3" json:"current_rare,omitempty"`
	LigatureUrl          string    `protobuf:"bytes,7,opt,name=ligature_url,json=ligatureUrl,proto3" json:"ligature_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *MicFellowInfo) Reset()         { *m = MicFellowInfo{} }
func (m *MicFellowInfo) String() string { return proto.CompactTextString(m) }
func (*MicFellowInfo) ProtoMessage()    {}
func (*MicFellowInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{59}
}
func (m *MicFellowInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicFellowInfo.Unmarshal(m, b)
}
func (m *MicFellowInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicFellowInfo.Marshal(b, m, deterministic)
}
func (dst *MicFellowInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicFellowInfo.Merge(dst, src)
}
func (m *MicFellowInfo) XXX_Size() int {
	return xxx_messageInfo_MicFellowInfo.Size(m)
}
func (m *MicFellowInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicFellowInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicFellowInfo proto.InternalMessageInfo

func (m *MicFellowInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicFellowInfo) GetFellowUid() uint32 {
	if m != nil {
		return m.FellowUid
	}
	return 0
}

func (m *MicFellowInfo) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *MicFellowInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *MicFellowInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *MicFellowInfo) GetCurrentRare() *RareInfo {
	if m != nil {
		return m.CurrentRare
	}
	return nil
}

func (m *MicFellowInfo) GetLigatureUrl() string {
	if m != nil {
		return m.LigatureUrl
	}
	return ""
}

type MicFellowInfoChangeInfo struct {
	MicFellow            []*MicFellowInfo `protobuf:"bytes,1,rep,name=mic_fellow,json=micFellow,proto3" json:"mic_fellow,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MicFellowInfoChangeInfo) Reset()         { *m = MicFellowInfoChangeInfo{} }
func (m *MicFellowInfoChangeInfo) String() string { return proto.CompactTextString(m) }
func (*MicFellowInfoChangeInfo) ProtoMessage()    {}
func (*MicFellowInfoChangeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{60}
}
func (m *MicFellowInfoChangeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicFellowInfoChangeInfo.Unmarshal(m, b)
}
func (m *MicFellowInfoChangeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicFellowInfoChangeInfo.Marshal(b, m, deterministic)
}
func (dst *MicFellowInfoChangeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicFellowInfoChangeInfo.Merge(dst, src)
}
func (m *MicFellowInfoChangeInfo) XXX_Size() int {
	return xxx_messageInfo_MicFellowInfoChangeInfo.Size(m)
}
func (m *MicFellowInfoChangeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicFellowInfoChangeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicFellowInfoChangeInfo proto.InternalMessageInfo

func (m *MicFellowInfoChangeInfo) GetMicFellow() []*MicFellowInfo {
	if m != nil {
		return m.MicFellow
	}
	return nil
}

type GetOnMicFellowListResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MicFellow            []*MicFellowInfo `protobuf:"bytes,2,rep,name=mic_fellow,json=micFellow,proto3" json:"mic_fellow,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetOnMicFellowListResp) Reset()         { *m = GetOnMicFellowListResp{} }
func (m *GetOnMicFellowListResp) String() string { return proto.CompactTextString(m) }
func (*GetOnMicFellowListResp) ProtoMessage()    {}
func (*GetOnMicFellowListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{61}
}
func (m *GetOnMicFellowListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnMicFellowListResp.Unmarshal(m, b)
}
func (m *GetOnMicFellowListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnMicFellowListResp.Marshal(b, m, deterministic)
}
func (dst *GetOnMicFellowListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnMicFellowListResp.Merge(dst, src)
}
func (m *GetOnMicFellowListResp) XXX_Size() int {
	return xxx_messageInfo_GetOnMicFellowListResp.Size(m)
}
func (m *GetOnMicFellowListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnMicFellowListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnMicFellowListResp proto.InternalMessageInfo

func (m *GetOnMicFellowListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetOnMicFellowListResp) GetMicFellow() []*MicFellowInfo {
	if m != nil {
		return m.MicFellow
	}
	return nil
}

// 我发出的挚友邀请列表
type GetSendInviteListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSendInviteListReq) Reset()         { *m = GetSendInviteListReq{} }
func (m *GetSendInviteListReq) String() string { return proto.CompactTextString(m) }
func (*GetSendInviteListReq) ProtoMessage()    {}
func (*GetSendInviteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{62}
}
func (m *GetSendInviteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSendInviteListReq.Unmarshal(m, b)
}
func (m *GetSendInviteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSendInviteListReq.Marshal(b, m, deterministic)
}
func (dst *GetSendInviteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSendInviteListReq.Merge(dst, src)
}
func (m *GetSendInviteListReq) XXX_Size() int {
	return xxx_messageInfo_GetSendInviteListReq.Size(m)
}
func (m *GetSendInviteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSendInviteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSendInviteListReq proto.InternalMessageInfo

func (m *GetSendInviteListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetSendInviteListResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InviteList           []*FellowInviteInfo `protobuf:"bytes,2,rep,name=invite_list,json=inviteList,proto3" json:"invite_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetSendInviteListResp) Reset()         { *m = GetSendInviteListResp{} }
func (m *GetSendInviteListResp) String() string { return proto.CompactTextString(m) }
func (*GetSendInviteListResp) ProtoMessage()    {}
func (*GetSendInviteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{63}
}
func (m *GetSendInviteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSendInviteListResp.Unmarshal(m, b)
}
func (m *GetSendInviteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSendInviteListResp.Marshal(b, m, deterministic)
}
func (dst *GetSendInviteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSendInviteListResp.Merge(dst, src)
}
func (m *GetSendInviteListResp) XXX_Size() int {
	return xxx_messageInfo_GetSendInviteListResp.Size(m)
}
func (m *GetSendInviteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSendInviteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSendInviteListResp proto.InternalMessageInfo

func (m *GetSendInviteListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSendInviteListResp) GetInviteList() []*FellowInviteInfo {
	if m != nil {
		return m.InviteList
	}
	return nil
}

// 换绑关系
type ChangeFellowBindTypeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OpUid                uint32       `protobuf:"varint,2,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	TargetUid            uint32       `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	FromBindType         uint32       `protobuf:"varint,4,opt,name=from_bind_type,json=fromBindType,proto3" json:"from_bind_type,omitempty"`
	FromFellowType       uint32       `protobuf:"varint,5,opt,name=from_fellow_type,json=fromFellowType,proto3" json:"from_fellow_type,omitempty"`
	ToBindType           uint32       `protobuf:"varint,6,opt,name=to_bind_type,json=toBindType,proto3" json:"to_bind_type,omitempty"`
	ToFellowType         uint32       `protobuf:"varint,7,opt,name=to_fellow_type,json=toFellowType,proto3" json:"to_fellow_type,omitempty"`
	PresentId            uint32       `protobuf:"varint,8,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	WithUnlock           bool         `protobuf:"varint,9,opt,name=with_unlock,json=withUnlock,proto3" json:"with_unlock,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChangeFellowBindTypeReq) Reset()         { *m = ChangeFellowBindTypeReq{} }
func (m *ChangeFellowBindTypeReq) String() string { return proto.CompactTextString(m) }
func (*ChangeFellowBindTypeReq) ProtoMessage()    {}
func (*ChangeFellowBindTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{64}
}
func (m *ChangeFellowBindTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeFellowBindTypeReq.Unmarshal(m, b)
}
func (m *ChangeFellowBindTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeFellowBindTypeReq.Marshal(b, m, deterministic)
}
func (dst *ChangeFellowBindTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeFellowBindTypeReq.Merge(dst, src)
}
func (m *ChangeFellowBindTypeReq) XXX_Size() int {
	return xxx_messageInfo_ChangeFellowBindTypeReq.Size(m)
}
func (m *ChangeFellowBindTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeFellowBindTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeFellowBindTypeReq proto.InternalMessageInfo

func (m *ChangeFellowBindTypeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChangeFellowBindTypeReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetFromBindType() uint32 {
	if m != nil {
		return m.FromBindType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetFromFellowType() uint32 {
	if m != nil {
		return m.FromFellowType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetToBindType() uint32 {
	if m != nil {
		return m.ToBindType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetToFellowType() uint32 {
	if m != nil {
		return m.ToFellowType
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *ChangeFellowBindTypeReq) GetWithUnlock() bool {
	if m != nil {
		return m.WithUnlock
	}
	return false
}

type ChangeFellowBindTypeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainTbean          int64         `protobuf:"varint,2,opt,name=remain_tbean,json=remainTbean,proto3" json:"remain_tbean,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChangeFellowBindTypeResp) Reset()         { *m = ChangeFellowBindTypeResp{} }
func (m *ChangeFellowBindTypeResp) String() string { return proto.CompactTextString(m) }
func (*ChangeFellowBindTypeResp) ProtoMessage()    {}
func (*ChangeFellowBindTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{65}
}
func (m *ChangeFellowBindTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeFellowBindTypeResp.Unmarshal(m, b)
}
func (m *ChangeFellowBindTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeFellowBindTypeResp.Marshal(b, m, deterministic)
}
func (dst *ChangeFellowBindTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeFellowBindTypeResp.Merge(dst, src)
}
func (m *ChangeFellowBindTypeResp) XXX_Size() int {
	return xxx_messageInfo_ChangeFellowBindTypeResp.Size(m)
}
func (m *ChangeFellowBindTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeFellowBindTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeFellowBindTypeResp proto.InternalMessageInfo

func (m *ChangeFellowBindTypeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChangeFellowBindTypeResp) GetRemainTbean() int64 {
	if m != nil {
		return m.RemainTbean
	}
	return 0
}

// 获取所有稀缺关系配置
type GetRareConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRareConfigReq) Reset()         { *m = GetRareConfigReq{} }
func (m *GetRareConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetRareConfigReq) ProtoMessage()    {}
func (*GetRareConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{66}
}
func (m *GetRareConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareConfigReq.Unmarshal(m, b)
}
func (m *GetRareConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetRareConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareConfigReq.Merge(dst, src)
}
func (m *GetRareConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetRareConfigReq.Size(m)
}
func (m *GetRareConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareConfigReq proto.InternalMessageInfo

func (m *GetRareConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type AnimationConfig struct {
	ResourceUrl          string   `protobuf:"bytes,1,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnimationConfig) Reset()         { *m = AnimationConfig{} }
func (m *AnimationConfig) String() string { return proto.CompactTextString(m) }
func (*AnimationConfig) ProtoMessage()    {}
func (*AnimationConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{67}
}
func (m *AnimationConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnimationConfig.Unmarshal(m, b)
}
func (m *AnimationConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnimationConfig.Marshal(b, m, deterministic)
}
func (dst *AnimationConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnimationConfig.Merge(dst, src)
}
func (m *AnimationConfig) XXX_Size() int {
	return xxx_messageInfo_AnimationConfig.Size(m)
}
func (m *AnimationConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_AnimationConfig.DiscardUnknown(m)
}

var xxx_messageInfo_AnimationConfig proto.InternalMessageInfo

func (m *AnimationConfig) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *AnimationConfig) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type SubRareConfig struct {
	SubRareId            uint32   `protobuf:"varint,1,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	RareFlag             string   `protobuf:"bytes,3,opt,name=rare_flag,json=rareFlag,proto3" json:"rare_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubRareConfig) Reset()         { *m = SubRareConfig{} }
func (m *SubRareConfig) String() string { return proto.CompactTextString(m) }
func (*SubRareConfig) ProtoMessage()    {}
func (*SubRareConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{68}
}
func (m *SubRareConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubRareConfig.Unmarshal(m, b)
}
func (m *SubRareConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubRareConfig.Marshal(b, m, deterministic)
}
func (dst *SubRareConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubRareConfig.Merge(dst, src)
}
func (m *SubRareConfig) XXX_Size() int {
	return xxx_messageInfo_SubRareConfig.Size(m)
}
func (m *SubRareConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_SubRareConfig.DiscardUnknown(m)
}

var xxx_messageInfo_SubRareConfig proto.InternalMessageInfo

func (m *SubRareConfig) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

func (m *SubRareConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SubRareConfig) GetRareFlag() string {
	if m != nil {
		return m.RareFlag
	}
	return ""
}

type ConnectedForMic struct {
	Left                 string   `protobuf:"bytes,1,opt,name=left,proto3" json:"left,omitempty"`
	Right                string   `protobuf:"bytes,2,opt,name=right,proto3" json:"right,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConnectedForMic) Reset()         { *m = ConnectedForMic{} }
func (m *ConnectedForMic) String() string { return proto.CompactTextString(m) }
func (*ConnectedForMic) ProtoMessage()    {}
func (*ConnectedForMic) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{69}
}
func (m *ConnectedForMic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConnectedForMic.Unmarshal(m, b)
}
func (m *ConnectedForMic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConnectedForMic.Marshal(b, m, deterministic)
}
func (dst *ConnectedForMic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConnectedForMic.Merge(dst, src)
}
func (m *ConnectedForMic) XXX_Size() int {
	return xxx_messageInfo_ConnectedForMic.Size(m)
}
func (m *ConnectedForMic) XXX_DiscardUnknown() {
	xxx_messageInfo_ConnectedForMic.DiscardUnknown(m)
}

var xxx_messageInfo_ConnectedForMic proto.InternalMessageInfo

func (m *ConnectedForMic) GetLeft() string {
	if m != nil {
		return m.Left
	}
	return ""
}

func (m *ConnectedForMic) GetRight() string {
	if m != nil {
		return m.Right
	}
	return ""
}

// MsgNotifyPictures 消息通知图片配置
type MsgNotifyPictures struct {
	Origin               string   `protobuf:"bytes,1,opt,name=origin,proto3" json:"origin,omitempty"`
	Thumbnail            string   `protobuf:"bytes,2,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MsgNotifyPictures) Reset()         { *m = MsgNotifyPictures{} }
func (m *MsgNotifyPictures) String() string { return proto.CompactTextString(m) }
func (*MsgNotifyPictures) ProtoMessage()    {}
func (*MsgNotifyPictures) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{70}
}
func (m *MsgNotifyPictures) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MsgNotifyPictures.Unmarshal(m, b)
}
func (m *MsgNotifyPictures) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MsgNotifyPictures.Marshal(b, m, deterministic)
}
func (dst *MsgNotifyPictures) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MsgNotifyPictures.Merge(dst, src)
}
func (m *MsgNotifyPictures) XXX_Size() int {
	return xxx_messageInfo_MsgNotifyPictures.Size(m)
}
func (m *MsgNotifyPictures) XXX_DiscardUnknown() {
	xxx_messageInfo_MsgNotifyPictures.DiscardUnknown(m)
}

var xxx_messageInfo_MsgNotifyPictures proto.InternalMessageInfo

func (m *MsgNotifyPictures) GetOrigin() string {
	if m != nil {
		return m.Origin
	}
	return ""
}

func (m *MsgNotifyPictures) GetThumbnail() string {
	if m != nil {
		return m.Thumbnail
	}
	return ""
}

type RareConfig struct {
	RareId                uint32             `protobuf:"varint,1,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	Name                  string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	SubRareCfg            []*SubRareConfig   `protobuf:"bytes,3,rep,name=sub_rare_cfg,json=subRareCfg,proto3" json:"sub_rare_cfg,omitempty"`
	CpAnimation           *AnimationConfig   `protobuf:"bytes,4,opt,name=cp_animation,json=cpAnimation,proto3" json:"cp_animation,omitempty"`
	MicConnected          *ConnectedForMic   `protobuf:"bytes,5,opt,name=mic_connected,json=micConnected,proto3" json:"mic_connected,omitempty"`
	RareFlag              string             `protobuf:"bytes,6,opt,name=rare_flag,json=rareFlag,proto3" json:"rare_flag,omitempty"`
	CardColor             string             `protobuf:"bytes,7,opt,name=card_color,json=cardColor,proto3" json:"card_color,omitempty"`
	CpBg                  *FellowBackground  `protobuf:"bytes,8,opt,name=cp_bg,json=cpBg,proto3" json:"cp_bg,omitempty"`
	MidBg                 *FellowBackground  `protobuf:"bytes,9,opt,name=mid_bg,json=midBg,proto3" json:"mid_bg,omitempty"`
	MsgPictrures          *MsgNotifyPictures `protobuf:"bytes,10,opt,name=msg_pictrures,json=msgPictrures,proto3" json:"msg_pictrures,omitempty"`
	FriendSpaceBackground string             `protobuf:"bytes,11,opt,name=friend_space_background,json=friendSpaceBackground,proto3" json:"friend_space_background,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}           `json:"-"`
	XXX_unrecognized      []byte             `json:"-"`
	XXX_sizecache         int32              `json:"-"`
}

func (m *RareConfig) Reset()         { *m = RareConfig{} }
func (m *RareConfig) String() string { return proto.CompactTextString(m) }
func (*RareConfig) ProtoMessage()    {}
func (*RareConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{71}
}
func (m *RareConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RareConfig.Unmarshal(m, b)
}
func (m *RareConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RareConfig.Marshal(b, m, deterministic)
}
func (dst *RareConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RareConfig.Merge(dst, src)
}
func (m *RareConfig) XXX_Size() int {
	return xxx_messageInfo_RareConfig.Size(m)
}
func (m *RareConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RareConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RareConfig proto.InternalMessageInfo

func (m *RareConfig) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *RareConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RareConfig) GetSubRareCfg() []*SubRareConfig {
	if m != nil {
		return m.SubRareCfg
	}
	return nil
}

func (m *RareConfig) GetCpAnimation() *AnimationConfig {
	if m != nil {
		return m.CpAnimation
	}
	return nil
}

func (m *RareConfig) GetMicConnected() *ConnectedForMic {
	if m != nil {
		return m.MicConnected
	}
	return nil
}

func (m *RareConfig) GetRareFlag() string {
	if m != nil {
		return m.RareFlag
	}
	return ""
}

func (m *RareConfig) GetCardColor() string {
	if m != nil {
		return m.CardColor
	}
	return ""
}

func (m *RareConfig) GetCpBg() *FellowBackground {
	if m != nil {
		return m.CpBg
	}
	return nil
}

func (m *RareConfig) GetMidBg() *FellowBackground {
	if m != nil {
		return m.MidBg
	}
	return nil
}

func (m *RareConfig) GetMsgPictrures() *MsgNotifyPictures {
	if m != nil {
		return m.MsgPictrures
	}
	return nil
}

func (m *RareConfig) GetFriendSpaceBackground() string {
	if m != nil {
		return m.FriendSpaceBackground
	}
	return ""
}

type GetRareConfigResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RareList             []*RareConfig `protobuf:"bytes,2,rep,name=rare_list,json=rareList,proto3" json:"rare_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRareConfigResp) Reset()         { *m = GetRareConfigResp{} }
func (m *GetRareConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetRareConfigResp) ProtoMessage()    {}
func (*GetRareConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{72}
}
func (m *GetRareConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareConfigResp.Unmarshal(m, b)
}
func (m *GetRareConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetRareConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareConfigResp.Merge(dst, src)
}
func (m *GetRareConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetRareConfigResp.Size(m)
}
func (m *GetRareConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareConfigResp proto.InternalMessageInfo

func (m *GetRareConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRareConfigResp) GetRareList() []*RareConfig {
	if m != nil {
		return m.RareList
	}
	return nil
}

// 切换绑定的稀缺关系
type SetBindRelationReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32       `protobuf:"varint,3,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	RareId               uint32       `protobuf:"varint,4,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	SubRareId            uint32       `protobuf:"varint,5,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	Bind                 bool         `protobuf:"varint,6,opt,name=bind,proto3" json:"bind,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetBindRelationReq) Reset()         { *m = SetBindRelationReq{} }
func (m *SetBindRelationReq) String() string { return proto.CompactTextString(m) }
func (*SetBindRelationReq) ProtoMessage()    {}
func (*SetBindRelationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{73}
}
func (m *SetBindRelationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBindRelationReq.Unmarshal(m, b)
}
func (m *SetBindRelationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBindRelationReq.Marshal(b, m, deterministic)
}
func (dst *SetBindRelationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBindRelationReq.Merge(dst, src)
}
func (m *SetBindRelationReq) XXX_Size() int {
	return xxx_messageInfo_SetBindRelationReq.Size(m)
}
func (m *SetBindRelationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBindRelationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBindRelationReq proto.InternalMessageInfo

func (m *SetBindRelationReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetBindRelationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetBindRelationReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *SetBindRelationReq) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *SetBindRelationReq) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

func (m *SetBindRelationReq) GetBind() bool {
	if m != nil {
		return m.Bind
	}
	return false
}

// 切换绑定的稀缺关系
type SetBindRelationResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Uid                  uint32        `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32        `protobuf:"varint,3,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	RareId               uint32        `protobuf:"varint,4,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	SubRareId            uint32        `protobuf:"varint,5,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	Bind                 bool          `protobuf:"varint,6,opt,name=bind,proto3" json:"bind,omitempty"`
	BindType             uint32        `protobuf:"varint,7,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetBindRelationResp) Reset()         { *m = SetBindRelationResp{} }
func (m *SetBindRelationResp) String() string { return proto.CompactTextString(m) }
func (*SetBindRelationResp) ProtoMessage()    {}
func (*SetBindRelationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{74}
}
func (m *SetBindRelationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBindRelationResp.Unmarshal(m, b)
}
func (m *SetBindRelationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBindRelationResp.Marshal(b, m, deterministic)
}
func (dst *SetBindRelationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBindRelationResp.Merge(dst, src)
}
func (m *SetBindRelationResp) XXX_Size() int {
	return xxx_messageInfo_SetBindRelationResp.Size(m)
}
func (m *SetBindRelationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBindRelationResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBindRelationResp proto.InternalMessageInfo

func (m *SetBindRelationResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SetBindRelationResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetBindRelationResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *SetBindRelationResp) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *SetBindRelationResp) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

func (m *SetBindRelationResp) GetBind() bool {
	if m != nil {
		return m.Bind
	}
	return false
}

func (m *SetBindRelationResp) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

// 获取房间可用稀缺关系配置
type GetChannelRareConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelRareConfigReq) Reset()         { *m = GetChannelRareConfigReq{} }
func (m *GetChannelRareConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRareConfigReq) ProtoMessage()    {}
func (*GetChannelRareConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{75}
}
func (m *GetChannelRareConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRareConfigReq.Unmarshal(m, b)
}
func (m *GetChannelRareConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRareConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelRareConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRareConfigReq.Merge(dst, src)
}
func (m *GetChannelRareConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelRareConfigReq.Size(m)
}
func (m *GetChannelRareConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRareConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRareConfigReq proto.InternalMessageInfo

func (m *GetChannelRareConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelRareConfigReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelRareConfigResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	IntroUrl             string        `protobuf:"bytes,3,opt,name=intro_url,json=introUrl,proto3" json:"intro_url,omitempty"`
	RareList             []*RareConfig `protobuf:"bytes,4,rep,name=rare_list,json=rareList,proto3" json:"rare_list,omitempty"`
	EntranceUrl          string        `protobuf:"bytes,5,opt,name=entrance_url,json=entranceUrl,proto3" json:"entrance_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelRareConfigResp) Reset()         { *m = GetChannelRareConfigResp{} }
func (m *GetChannelRareConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRareConfigResp) ProtoMessage()    {}
func (*GetChannelRareConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{76}
}
func (m *GetChannelRareConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRareConfigResp.Unmarshal(m, b)
}
func (m *GetChannelRareConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRareConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelRareConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRareConfigResp.Merge(dst, src)
}
func (m *GetChannelRareConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelRareConfigResp.Size(m)
}
func (m *GetChannelRareConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRareConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRareConfigResp proto.InternalMessageInfo

func (m *GetChannelRareConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelRareConfigResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelRareConfigResp) GetIntroUrl() string {
	if m != nil {
		return m.IntroUrl
	}
	return ""
}

func (m *GetChannelRareConfigResp) GetRareList() []*RareConfig {
	if m != nil {
		return m.RareList
	}
	return nil
}

func (m *GetChannelRareConfigResp) GetEntranceUrl() string {
	if m != nil {
		return m.EntranceUrl
	}
	return ""
}

// 获取两个UID的所有稀缺关系列表
type GetRareListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32       `protobuf:"varint,3,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRareListReq) Reset()         { *m = GetRareListReq{} }
func (m *GetRareListReq) String() string { return proto.CompactTextString(m) }
func (*GetRareListReq) ProtoMessage()    {}
func (*GetRareListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{77}
}
func (m *GetRareListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareListReq.Unmarshal(m, b)
}
func (m *GetRareListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareListReq.Marshal(b, m, deterministic)
}
func (dst *GetRareListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareListReq.Merge(dst, src)
}
func (m *GetRareListReq) XXX_Size() int {
	return xxx_messageInfo_GetRareListReq.Size(m)
}
func (m *GetRareListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareListReq proto.InternalMessageInfo

func (m *GetRareListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRareListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRareListReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type GetRareListResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32            `protobuf:"varint,3,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToAccount            string            `protobuf:"bytes,4,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ToNickName           string            `protobuf:"bytes,5,opt,name=to_nick_name,json=toNickName,proto3" json:"to_nick_name,omitempty"`
	BindType             uint32            `protobuf:"varint,6,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	FellowType           uint32            `protobuf:"varint,7,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	FellowName           string            `protobuf:"bytes,8,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	RareList             []*RareInfo       `protobuf:"bytes,9,rep,name=rare_list,json=rareList,proto3" json:"rare_list,omitempty"`
	PresentBg            *FellowBackground `protobuf:"bytes,10,opt,name=present_bg,json=presentBg,proto3" json:"present_bg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetRareListResp) Reset()         { *m = GetRareListResp{} }
func (m *GetRareListResp) String() string { return proto.CompactTextString(m) }
func (*GetRareListResp) ProtoMessage()    {}
func (*GetRareListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{78}
}
func (m *GetRareListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRareListResp.Unmarshal(m, b)
}
func (m *GetRareListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRareListResp.Marshal(b, m, deterministic)
}
func (dst *GetRareListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRareListResp.Merge(dst, src)
}
func (m *GetRareListResp) XXX_Size() int {
	return xxx_messageInfo_GetRareListResp.Size(m)
}
func (m *GetRareListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRareListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRareListResp proto.InternalMessageInfo

func (m *GetRareListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRareListResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRareListResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetRareListResp) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *GetRareListResp) GetToNickName() string {
	if m != nil {
		return m.ToNickName
	}
	return ""
}

func (m *GetRareListResp) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *GetRareListResp) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *GetRareListResp) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *GetRareListResp) GetRareList() []*RareInfo {
	if m != nil {
		return m.RareList
	}
	return nil
}

func (m *GetRareListResp) GetPresentBg() *FellowBackground {
	if m != nil {
		return m.PresentBg
	}
	return nil
}

type DelRareReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ToUid                uint32       `protobuf:"varint,3,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	RareId               uint32       `protobuf:"varint,4,opt,name=rare_id,json=rareId,proto3" json:"rare_id,omitempty"`
	SubRareId            uint32       `protobuf:"varint,5,opt,name=sub_rare_id,json=subRareId,proto3" json:"sub_rare_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DelRareReq) Reset()         { *m = DelRareReq{} }
func (m *DelRareReq) String() string { return proto.CompactTextString(m) }
func (*DelRareReq) ProtoMessage()    {}
func (*DelRareReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{79}
}
func (m *DelRareReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelRareReq.Unmarshal(m, b)
}
func (m *DelRareReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelRareReq.Marshal(b, m, deterministic)
}
func (dst *DelRareReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelRareReq.Merge(dst, src)
}
func (m *DelRareReq) XXX_Size() int {
	return xxx_messageInfo_DelRareReq.Size(m)
}
func (m *DelRareReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelRareReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelRareReq proto.InternalMessageInfo

func (m *DelRareReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DelRareReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelRareReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *DelRareReq) GetRareId() uint32 {
	if m != nil {
		return m.RareId
	}
	return 0
}

func (m *DelRareReq) GetSubRareId() uint32 {
	if m != nil {
		return m.SubRareId
	}
	return 0
}

type DelRareResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DelRareResp) Reset()         { *m = DelRareResp{} }
func (m *DelRareResp) String() string { return proto.CompactTextString(m) }
func (*DelRareResp) ProtoMessage()    {}
func (*DelRareResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{80}
}
func (m *DelRareResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelRareResp.Unmarshal(m, b)
}
func (m *DelRareResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelRareResp.Marshal(b, m, deterministic)
}
func (dst *DelRareResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelRareResp.Merge(dst, src)
}
func (m *DelRareResp) XXX_Size() int {
	return xxx_messageInfo_DelRareResp.Size(m)
}
func (m *DelRareResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelRareResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelRareResp proto.InternalMessageInfo

func (m *DelRareResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type FellowHouseRes struct {
	ResType              uint32   `protobuf:"varint,1,opt,name=res_type,json=resType,proto3" json:"res_type,omitempty"`
	HouseRes             string   `protobuf:"bytes,2,opt,name=house_res,json=houseRes,proto3" json:"house_res,omitempty"`
	HouseResMd5          string   `protobuf:"bytes,3,opt,name=house_res_md5,json=houseResMd5,proto3" json:"house_res_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowHouseRes) Reset()         { *m = FellowHouseRes{} }
func (m *FellowHouseRes) String() string { return proto.CompactTextString(m) }
func (*FellowHouseRes) ProtoMessage()    {}
func (*FellowHouseRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{81}
}
func (m *FellowHouseRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowHouseRes.Unmarshal(m, b)
}
func (m *FellowHouseRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowHouseRes.Marshal(b, m, deterministic)
}
func (dst *FellowHouseRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowHouseRes.Merge(dst, src)
}
func (m *FellowHouseRes) XXX_Size() int {
	return xxx_messageInfo_FellowHouseRes.Size(m)
}
func (m *FellowHouseRes) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowHouseRes.DiscardUnknown(m)
}

var xxx_messageInfo_FellowHouseRes proto.InternalMessageInfo

func (m *FellowHouseRes) GetResType() uint32 {
	if m != nil {
		return m.ResType
	}
	return 0
}

func (m *FellowHouseRes) GetHouseRes() string {
	if m != nil {
		return m.HouseRes
	}
	return ""
}

func (m *FellowHouseRes) GetHouseResMd5() string {
	if m != nil {
		return m.HouseResMd5
	}
	return ""
}

type FellowHouseCfg struct {
	HouseId              uint32          `protobuf:"varint,1,opt,name=house_id,json=houseId,proto3" json:"house_id,omitempty"`
	Name                 string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string          `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	HouseRes             *FellowHouseRes `protobuf:"bytes,4,opt,name=house_res,json=houseRes,proto3" json:"house_res,omitempty"`
	BonusRatio           uint32          `protobuf:"varint,5,opt,name=bonus_ratio,json=bonusRatio,proto3" json:"bonus_ratio,omitempty"`
	DiscountPrice        uint32          `protobuf:"varint,6,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *FellowHouseCfg) Reset()         { *m = FellowHouseCfg{} }
func (m *FellowHouseCfg) String() string { return proto.CompactTextString(m) }
func (*FellowHouseCfg) ProtoMessage()    {}
func (*FellowHouseCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{82}
}
func (m *FellowHouseCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowHouseCfg.Unmarshal(m, b)
}
func (m *FellowHouseCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowHouseCfg.Marshal(b, m, deterministic)
}
func (dst *FellowHouseCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowHouseCfg.Merge(dst, src)
}
func (m *FellowHouseCfg) XXX_Size() int {
	return xxx_messageInfo_FellowHouseCfg.Size(m)
}
func (m *FellowHouseCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowHouseCfg.DiscardUnknown(m)
}

var xxx_messageInfo_FellowHouseCfg proto.InternalMessageInfo

func (m *FellowHouseCfg) GetHouseId() uint32 {
	if m != nil {
		return m.HouseId
	}
	return 0
}

func (m *FellowHouseCfg) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FellowHouseCfg) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *FellowHouseCfg) GetHouseRes() *FellowHouseRes {
	if m != nil {
		return m.HouseRes
	}
	return nil
}

func (m *FellowHouseCfg) GetBonusRatio() uint32 {
	if m != nil {
		return m.BonusRatio
	}
	return 0
}

func (m *FellowHouseCfg) GetDiscountPrice() uint32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

type FellowHouseInfo struct {
	Cfg                  *FellowHouseCfg  `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	CpUser               *app.UserProfile `protobuf:"bytes,2,opt,name=cp_user,json=cpUser,proto3" json:"cp_user,omitempty"`
	ExpireTs             int64            `protobuf:"varint,3,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	FellowLevel          uint32           `protobuf:"varint,4,opt,name=fellow_level,json=fellowLevel,proto3" json:"fellow_level,omitempty"`
	FellowType           uint32           `protobuf:"varint,5,opt,name=fellow_type,json=fellowType,proto3" json:"fellow_type,omitempty"`
	DayCnt               uint32           `protobuf:"varint,6,opt,name=day_cnt,json=dayCnt,proto3" json:"day_cnt,omitempty"`
	FellowName           string           `protobuf:"bytes,7,opt,name=fellow_name,json=fellowName,proto3" json:"fellow_name,omitempty"`
	BindType             uint32           `protobuf:"varint,8,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	GradingInfo          *GradingInfo     `protobuf:"bytes,9,opt,name=grading_info,json=gradingInfo,proto3" json:"grading_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *FellowHouseInfo) Reset()         { *m = FellowHouseInfo{} }
func (m *FellowHouseInfo) String() string { return proto.CompactTextString(m) }
func (*FellowHouseInfo) ProtoMessage()    {}
func (*FellowHouseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{83}
}
func (m *FellowHouseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowHouseInfo.Unmarshal(m, b)
}
func (m *FellowHouseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowHouseInfo.Marshal(b, m, deterministic)
}
func (dst *FellowHouseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowHouseInfo.Merge(dst, src)
}
func (m *FellowHouseInfo) XXX_Size() int {
	return xxx_messageInfo_FellowHouseInfo.Size(m)
}
func (m *FellowHouseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowHouseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FellowHouseInfo proto.InternalMessageInfo

func (m *FellowHouseInfo) GetCfg() *FellowHouseCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

func (m *FellowHouseInfo) GetCpUser() *app.UserProfile {
	if m != nil {
		return m.CpUser
	}
	return nil
}

func (m *FellowHouseInfo) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *FellowHouseInfo) GetFellowLevel() uint32 {
	if m != nil {
		return m.FellowLevel
	}
	return 0
}

func (m *FellowHouseInfo) GetFellowType() uint32 {
	if m != nil {
		return m.FellowType
	}
	return 0
}

func (m *FellowHouseInfo) GetDayCnt() uint32 {
	if m != nil {
		return m.DayCnt
	}
	return 0
}

func (m *FellowHouseInfo) GetFellowName() string {
	if m != nil {
		return m.FellowName
	}
	return ""
}

func (m *FellowHouseInfo) GetBindType() uint32 {
	if m != nil {
		return m.BindType
	}
	return 0
}

func (m *FellowHouseInfo) GetGradingInfo() *GradingInfo {
	if m != nil {
		return m.GradingInfo
	}
	return nil
}

// 获取用户正在使用挚友小屋列表
type GetFellowHouseInuseListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowHouseInuseListRequest) Reset()         { *m = GetFellowHouseInuseListRequest{} }
func (m *GetFellowHouseInuseListRequest) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseInuseListRequest) ProtoMessage()    {}
func (*GetFellowHouseInuseListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{84}
}
func (m *GetFellowHouseInuseListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseInuseListRequest.Unmarshal(m, b)
}
func (m *GetFellowHouseInuseListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseInuseListRequest.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseInuseListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseInuseListRequest.Merge(dst, src)
}
func (m *GetFellowHouseInuseListRequest) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseInuseListRequest.Size(m)
}
func (m *GetFellowHouseInuseListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseInuseListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseInuseListRequest proto.InternalMessageInfo

func (m *GetFellowHouseInuseListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowHouseInuseListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFellowHouseInuseListResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*FellowHouseInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	CpHouseCnt           uint32             `protobuf:"varint,3,opt,name=cp_house_cnt,json=cpHouseCnt,proto3" json:"cp_house_cnt,omitempty"`
	SvipVisible          bool               `protobuf:"varint,4,opt,name=svip_visible,json=svipVisible,proto3" json:"svip_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetFellowHouseInuseListResponse) Reset()         { *m = GetFellowHouseInuseListResponse{} }
func (m *GetFellowHouseInuseListResponse) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseInuseListResponse) ProtoMessage()    {}
func (*GetFellowHouseInuseListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{85}
}
func (m *GetFellowHouseInuseListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseInuseListResponse.Unmarshal(m, b)
}
func (m *GetFellowHouseInuseListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseInuseListResponse.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseInuseListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseInuseListResponse.Merge(dst, src)
}
func (m *GetFellowHouseInuseListResponse) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseInuseListResponse.Size(m)
}
func (m *GetFellowHouseInuseListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseInuseListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseInuseListResponse proto.InternalMessageInfo

func (m *GetFellowHouseInuseListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowHouseInuseListResponse) GetList() []*FellowHouseInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetFellowHouseInuseListResponse) GetCpHouseCnt() uint32 {
	if m != nil {
		return m.CpHouseCnt
	}
	return 0
}

func (m *GetFellowHouseInuseListResponse) GetSvipVisible() bool {
	if m != nil {
		return m.SvipVisible
	}
	return false
}

// 获取入口信息
type GetFellowHouseEntryInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowHouseEntryInfoRequest) Reset()         { *m = GetFellowHouseEntryInfoRequest{} }
func (m *GetFellowHouseEntryInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseEntryInfoRequest) ProtoMessage()    {}
func (*GetFellowHouseEntryInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{86}
}
func (m *GetFellowHouseEntryInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseEntryInfoRequest.Unmarshal(m, b)
}
func (m *GetFellowHouseEntryInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseEntryInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseEntryInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseEntryInfoRequest.Merge(dst, src)
}
func (m *GetFellowHouseEntryInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseEntryInfoRequest.Size(m)
}
func (m *GetFellowHouseEntryInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseEntryInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseEntryInfoRequest proto.InternalMessageInfo

func (m *GetFellowHouseEntryInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowHouseEntryInfoRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetFellowHouseEntryInfoResponse struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HasEntry             bool             `protobuf:"varint,2,opt,name=has_entry,json=hasEntry,proto3" json:"has_entry,omitempty"`
	Info                 *FellowHouseInfo `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetFellowHouseEntryInfoResponse) Reset()         { *m = GetFellowHouseEntryInfoResponse{} }
func (m *GetFellowHouseEntryInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetFellowHouseEntryInfoResponse) ProtoMessage()    {}
func (*GetFellowHouseEntryInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{87}
}
func (m *GetFellowHouseEntryInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowHouseEntryInfoResponse.Unmarshal(m, b)
}
func (m *GetFellowHouseEntryInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowHouseEntryInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetFellowHouseEntryInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowHouseEntryInfoResponse.Merge(dst, src)
}
func (m *GetFellowHouseEntryInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetFellowHouseEntryInfoResponse.Size(m)
}
func (m *GetFellowHouseEntryInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowHouseEntryInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowHouseEntryInfoResponse proto.InternalMessageInfo

func (m *GetFellowHouseEntryInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowHouseEntryInfoResponse) GetHasEntry() bool {
	if m != nil {
		return m.HasEntry
	}
	return false
}

func (m *GetFellowHouseEntryInfoResponse) GetInfo() *FellowHouseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 个人资料卡 挚友小屋信息 【废弃】
type GetFellowCardInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TargetUid            uint32       `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetFellowCardInfoRequest) Reset()         { *m = GetFellowCardInfoRequest{} }
func (m *GetFellowCardInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetFellowCardInfoRequest) ProtoMessage()    {}
func (*GetFellowCardInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{88}
}
func (m *GetFellowCardInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCardInfoRequest.Unmarshal(m, b)
}
func (m *GetFellowCardInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCardInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetFellowCardInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCardInfoRequest.Merge(dst, src)
}
func (m *GetFellowCardInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetFellowCardInfoRequest.Size(m)
}
func (m *GetFellowCardInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCardInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCardInfoRequest proto.InternalMessageInfo

func (m *GetFellowCardInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFellowCardInfoRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type SimpleHouseInfo struct {
	Cfg                  *FellowHouseCfg  `protobuf:"bytes,1,opt,name=cfg,proto3" json:"cfg,omitempty"`
	CpUser               *app.UserProfile `protobuf:"bytes,2,opt,name=cp_user,json=cpUser,proto3" json:"cp_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SimpleHouseInfo) Reset()         { *m = SimpleHouseInfo{} }
func (m *SimpleHouseInfo) String() string { return proto.CompactTextString(m) }
func (*SimpleHouseInfo) ProtoMessage()    {}
func (*SimpleHouseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{89}
}
func (m *SimpleHouseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleHouseInfo.Unmarshal(m, b)
}
func (m *SimpleHouseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleHouseInfo.Marshal(b, m, deterministic)
}
func (dst *SimpleHouseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleHouseInfo.Merge(dst, src)
}
func (m *SimpleHouseInfo) XXX_Size() int {
	return xxx_messageInfo_SimpleHouseInfo.Size(m)
}
func (m *SimpleHouseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleHouseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleHouseInfo proto.InternalMessageInfo

func (m *SimpleHouseInfo) GetCfg() *FellowHouseCfg {
	if m != nil {
		return m.Cfg
	}
	return nil
}

func (m *SimpleHouseInfo) GetCpUser() *app.UserProfile {
	if m != nil {
		return m.CpUser
	}
	return nil
}

// 个人资料卡 挚友小屋信息 【废弃】
type GetFellowCardInfoResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*SimpleHouseInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	SvipVisible          bool               `protobuf:"varint,3,opt,name=svip_visible,json=svipVisible,proto3" json:"svip_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetFellowCardInfoResponse) Reset()         { *m = GetFellowCardInfoResponse{} }
func (m *GetFellowCardInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetFellowCardInfoResponse) ProtoMessage()    {}
func (*GetFellowCardInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{90}
}
func (m *GetFellowCardInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFellowCardInfoResponse.Unmarshal(m, b)
}
func (m *GetFellowCardInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFellowCardInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetFellowCardInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFellowCardInfoResponse.Merge(dst, src)
}
func (m *GetFellowCardInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetFellowCardInfoResponse.Size(m)
}
func (m *GetFellowCardInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFellowCardInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetFellowCardInfoResponse proto.InternalMessageInfo

func (m *GetFellowCardInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFellowCardInfoResponse) GetList() []*SimpleHouseInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetFellowCardInfoResponse) GetSvipVisible() bool {
	if m != nil {
		return m.SvipVisible
	}
	return false
}

type FellowUpgradeAnimationPushMsg struct {
	AnimationUrl         string   `protobuf:"bytes,1,opt,name=animation_url,json=animationUrl,proto3" json:"animation_url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	ExtendJson           string   `protobuf:"bytes,3,opt,name=extend_json,json=extendJson,proto3" json:"extend_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FellowUpgradeAnimationPushMsg) Reset()         { *m = FellowUpgradeAnimationPushMsg{} }
func (m *FellowUpgradeAnimationPushMsg) String() string { return proto.CompactTextString(m) }
func (*FellowUpgradeAnimationPushMsg) ProtoMessage()    {}
func (*FellowUpgradeAnimationPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{91}
}
func (m *FellowUpgradeAnimationPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FellowUpgradeAnimationPushMsg.Unmarshal(m, b)
}
func (m *FellowUpgradeAnimationPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FellowUpgradeAnimationPushMsg.Marshal(b, m, deterministic)
}
func (dst *FellowUpgradeAnimationPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FellowUpgradeAnimationPushMsg.Merge(dst, src)
}
func (m *FellowUpgradeAnimationPushMsg) XXX_Size() int {
	return xxx_messageInfo_FellowUpgradeAnimationPushMsg.Size(m)
}
func (m *FellowUpgradeAnimationPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_FellowUpgradeAnimationPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_FellowUpgradeAnimationPushMsg proto.InternalMessageInfo

func (m *FellowUpgradeAnimationPushMsg) GetAnimationUrl() string {
	if m != nil {
		return m.AnimationUrl
	}
	return ""
}

func (m *FellowUpgradeAnimationPushMsg) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *FellowUpgradeAnimationPushMsg) GetExtendJson() string {
	if m != nil {
		return m.ExtendJson
	}
	return ""
}

type ExpiringHouseInfo struct {
	HouseId              uint32           `protobuf:"varint,1,opt,name=house_id,json=houseId,proto3" json:"house_id,omitempty"`
	CpUser               *app.UserProfile `protobuf:"bytes,2,opt,name=cp_user,json=cpUser,proto3" json:"cp_user,omitempty"`
	HouseIcon            string           `protobuf:"bytes,3,opt,name=house_icon,json=houseIcon,proto3" json:"house_icon,omitempty"`
	HouseName            string           `protobuf:"bytes,4,opt,name=house_name,json=houseName,proto3" json:"house_name,omitempty"`
	ExpTs                int64            `protobuf:"varint,5,opt,name=exp_ts,json=expTs,proto3" json:"exp_ts,omitempty"`
	PopupId              string           `protobuf:"bytes,6,opt,name=popup_id,json=popupId,proto3" json:"popup_id,omitempty"`
	HighlightText        string           `protobuf:"bytes,7,opt,name=highlight_text,json=highlightText,proto3" json:"highlight_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ExpiringHouseInfo) Reset()         { *m = ExpiringHouseInfo{} }
func (m *ExpiringHouseInfo) String() string { return proto.CompactTextString(m) }
func (*ExpiringHouseInfo) ProtoMessage()    {}
func (*ExpiringHouseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{92}
}
func (m *ExpiringHouseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpiringHouseInfo.Unmarshal(m, b)
}
func (m *ExpiringHouseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpiringHouseInfo.Marshal(b, m, deterministic)
}
func (dst *ExpiringHouseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpiringHouseInfo.Merge(dst, src)
}
func (m *ExpiringHouseInfo) XXX_Size() int {
	return xxx_messageInfo_ExpiringHouseInfo.Size(m)
}
func (m *ExpiringHouseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpiringHouseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExpiringHouseInfo proto.InternalMessageInfo

func (m *ExpiringHouseInfo) GetHouseId() uint32 {
	if m != nil {
		return m.HouseId
	}
	return 0
}

func (m *ExpiringHouseInfo) GetCpUser() *app.UserProfile {
	if m != nil {
		return m.CpUser
	}
	return nil
}

func (m *ExpiringHouseInfo) GetHouseIcon() string {
	if m != nil {
		return m.HouseIcon
	}
	return ""
}

func (m *ExpiringHouseInfo) GetHouseName() string {
	if m != nil {
		return m.HouseName
	}
	return ""
}

func (m *ExpiringHouseInfo) GetExpTs() int64 {
	if m != nil {
		return m.ExpTs
	}
	return 0
}

func (m *ExpiringHouseInfo) GetPopupId() string {
	if m != nil {
		return m.PopupId
	}
	return ""
}

func (m *ExpiringHouseInfo) GetHighlightText() string {
	if m != nil {
		return m.HighlightText
	}
	return ""
}

// 获取用户即将过期小屋列表
type GetExpiringFellowHouseListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetExpiringFellowHouseListRequest) Reset()         { *m = GetExpiringFellowHouseListRequest{} }
func (m *GetExpiringFellowHouseListRequest) String() string { return proto.CompactTextString(m) }
func (*GetExpiringFellowHouseListRequest) ProtoMessage()    {}
func (*GetExpiringFellowHouseListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{93}
}
func (m *GetExpiringFellowHouseListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExpiringFellowHouseListRequest.Unmarshal(m, b)
}
func (m *GetExpiringFellowHouseListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExpiringFellowHouseListRequest.Marshal(b, m, deterministic)
}
func (dst *GetExpiringFellowHouseListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExpiringFellowHouseListRequest.Merge(dst, src)
}
func (m *GetExpiringFellowHouseListRequest) XXX_Size() int {
	return xxx_messageInfo_GetExpiringFellowHouseListRequest.Size(m)
}
func (m *GetExpiringFellowHouseListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExpiringFellowHouseListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetExpiringFellowHouseListRequest proto.InternalMessageInfo

func (m *GetExpiringFellowHouseListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetExpiringFellowHouseListResponse struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HouseList            []*ExpiringHouseInfo `protobuf:"bytes,2,rep,name=house_list,json=houseList,proto3" json:"house_list,omitempty"`
	PopupTitle           string               `protobuf:"bytes,3,opt,name=popup_title,json=popupTitle,proto3" json:"popup_title,omitempty"`
	PopupFormatText      string               `protobuf:"bytes,4,opt,name=popup_format_text,json=popupFormatText,proto3" json:"popup_format_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetExpiringFellowHouseListResponse) Reset()         { *m = GetExpiringFellowHouseListResponse{} }
func (m *GetExpiringFellowHouseListResponse) String() string { return proto.CompactTextString(m) }
func (*GetExpiringFellowHouseListResponse) ProtoMessage()    {}
func (*GetExpiringFellowHouseListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_fellow_logic__69cfc50eb589d9b9, []int{94}
}
func (m *GetExpiringFellowHouseListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExpiringFellowHouseListResponse.Unmarshal(m, b)
}
func (m *GetExpiringFellowHouseListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExpiringFellowHouseListResponse.Marshal(b, m, deterministic)
}
func (dst *GetExpiringFellowHouseListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExpiringFellowHouseListResponse.Merge(dst, src)
}
func (m *GetExpiringFellowHouseListResponse) XXX_Size() int {
	return xxx_messageInfo_GetExpiringFellowHouseListResponse.Size(m)
}
func (m *GetExpiringFellowHouseListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExpiringFellowHouseListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetExpiringFellowHouseListResponse proto.InternalMessageInfo

func (m *GetExpiringFellowHouseListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetExpiringFellowHouseListResponse) GetHouseList() []*ExpiringHouseInfo {
	if m != nil {
		return m.HouseList
	}
	return nil
}

func (m *GetExpiringFellowHouseListResponse) GetPopupTitle() string {
	if m != nil {
		return m.PopupTitle
	}
	return ""
}

func (m *GetExpiringFellowHouseListResponse) GetPopupFormatText() string {
	if m != nil {
		return m.PopupFormatText
	}
	return ""
}

func init() {
	proto.RegisterType((*PlateUrl)(nil), "ga.fellow_logic.PlateUrl")
	proto.RegisterType((*FellowBackground)(nil), "ga.fellow_logic.FellowBackground")
	proto.RegisterType((*RareInfo)(nil), "ga.fellow_logic.RareInfo")
	proto.RegisterType((*RareTabInfo)(nil), "ga.fellow_logic.RareTabInfo")
	proto.RegisterType((*GradingInfo)(nil), "ga.fellow_logic.GradingInfo")
	proto.RegisterType((*FellowInfo)(nil), "ga.fellow_logic.FellowInfo")
	proto.RegisterType((*FellowInviteUser)(nil), "ga.fellow_logic.FellowInviteUser")
	proto.RegisterType((*FellowPresentInfo)(nil), "ga.fellow_logic.FellowPresentInfo")
	proto.RegisterType((*FellowInviteInfo)(nil), "ga.fellow_logic.FellowInviteInfo")
	proto.RegisterType((*FellowTypeInfo)(nil), "ga.fellow_logic.FellowTypeInfo")
	proto.RegisterType((*GetFellowListReq)(nil), "ga.fellow_logic.GetFellowListReq")
	proto.RegisterType((*GetFellowListResp)(nil), "ga.fellow_logic.GetFellowListResp")
	proto.RegisterType((*UnlockInfo)(nil), "ga.fellow_logic.UnlockInfo")
	proto.RegisterType((*GetFellowCandidateListReq)(nil), "ga.fellow_logic.GetFellowCandidateListReq")
	proto.RegisterType((*GetFellowCandidateListResp)(nil), "ga.fellow_logic.GetFellowCandidateListResp")
	proto.RegisterType((*GetFellowCandidateInfoReq)(nil), "ga.fellow_logic.GetFellowCandidateInfoReq")
	proto.RegisterType((*GetFellowCandidateInfoResp)(nil), "ga.fellow_logic.GetFellowCandidateInfoResp")
	proto.RegisterType((*SendFellowInviteReq)(nil), "ga.fellow_logic.SendFellowInviteReq")
	proto.RegisterType((*SendFellowInviteResp)(nil), "ga.fellow_logic.SendFellowInviteResp")
	proto.RegisterType((*GetFellowInviteListReq)(nil), "ga.fellow_logic.GetFellowInviteListReq")
	proto.RegisterType((*GetFellowInviteListResp)(nil), "ga.fellow_logic.GetFellowInviteListResp")
	proto.RegisterType((*GetFellowInviteInfoByIdReq)(nil), "ga.fellow_logic.GetFellowInviteInfoByIdReq")
	proto.RegisterType((*GetFellowInviteInfoByIdResp)(nil), "ga.fellow_logic.GetFellowInviteInfoByIdResp")
	proto.RegisterType((*HandleFellowInviteReq)(nil), "ga.fellow_logic.HandleFellowInviteReq")
	proto.RegisterType((*HandleFellowInviteResp)(nil), "ga.fellow_logic.HandleFellowInviteResp")
	proto.RegisterType((*CheckFellowInviteReq)(nil), "ga.fellow_logic.CheckFellowInviteReq")
	proto.RegisterType((*CheckFellowInviteResp)(nil), "ga.fellow_logic.CheckFellowInviteResp")
	proto.RegisterType((*UnlockFellowPositionReq)(nil), "ga.fellow_logic.UnlockFellowPositionReq")
	proto.RegisterType((*UnlockFellowPositionResp)(nil), "ga.fellow_logic.UnlockFellowPositionResp")
	proto.RegisterType((*UnboundFellowReq)(nil), "ga.fellow_logic.UnboundFellowReq")
	proto.RegisterType((*UnboundFellowResp)(nil), "ga.fellow_logic.UnboundFellowResp")
	proto.RegisterType((*CancelUnboundFellowReq)(nil), "ga.fellow_logic.CancelUnboundFellowReq")
	proto.RegisterType((*CancelUnboundFellowResp)(nil), "ga.fellow_logic.CancelUnboundFellowResp")
	proto.RegisterType((*CancelFellowInviteReq)(nil), "ga.fellow_logic.CancelFellowInviteReq")
	proto.RegisterType((*CancelFellowInviteResp)(nil), "ga.fellow_logic.CancelFellowInviteResp")
	proto.RegisterType((*GetFellowPointReq)(nil), "ga.fellow_logic.GetFellowPointReq")
	proto.RegisterType((*GetFellowPointResp)(nil), "ga.fellow_logic.GetFellowPointResp")
	proto.RegisterType((*GetFellowPresentDetailReq)(nil), "ga.fellow_logic.GetFellowPresentDetailReq")
	proto.RegisterType((*GetFellowPresentDetailResp)(nil), "ga.fellow_logic.GetFellowPresentDetailResp")
	proto.RegisterType((*SendFellowPresentReq)(nil), "ga.fellow_logic.SendFellowPresentReq")
	proto.RegisterType((*SendFellowPresentResp)(nil), "ga.fellow_logic.SendFellowPresentResp")
	proto.RegisterType((*PresentSendItemSimpleInfo)(nil), "ga.fellow_logic.PresentSendItemSimpleInfo")
	proto.RegisterType((*GetFellowInfoByUidReq)(nil), "ga.fellow_logic.GetFellowInfoByUidReq")
	proto.RegisterType((*GetFellowInfoByUidResp)(nil), "ga.fellow_logic.GetFellowInfoByUidResp")
	proto.RegisterType((*ChannelSendFellowPresentReq)(nil), "ga.fellow_logic.ChannelSendFellowPresentReq")
	proto.RegisterType((*ChannelSendFellowPresentResp)(nil), "ga.fellow_logic.ChannelSendFellowPresentResp")
	proto.RegisterType((*SendChannelFellowInviteReq)(nil), "ga.fellow_logic.SendChannelFellowInviteReq")
	proto.RegisterType((*SendChannelFellowInviteResp)(nil), "ga.fellow_logic.SendChannelFellowInviteResp")
	proto.RegisterType((*HandleChannelFellowInviteReq)(nil), "ga.fellow_logic.HandleChannelFellowInviteReq")
	proto.RegisterType((*HandleChannelFellowInviteResp)(nil), "ga.fellow_logic.HandleChannelFellowInviteResp")
	proto.RegisterType((*ChannelFellowMsg)(nil), "ga.fellow_logic.ChannelFellowMsg")
	proto.RegisterType((*GetRoomFellowListReq)(nil), "ga.fellow_logic.GetRoomFellowListReq")
	proto.RegisterType((*GetRoomFellowListResp)(nil), "ga.fellow_logic.GetRoomFellowListResp")
	proto.RegisterType((*GetAllChannelFellowInviteReq)(nil), "ga.fellow_logic.GetAllChannelFellowInviteReq")
	proto.RegisterType((*GetAllChannelFellowInviteResp)(nil), "ga.fellow_logic.GetAllChannelFellowInviteResp")
	proto.RegisterType((*GetChannelFellowCandidateInfoReq)(nil), "ga.fellow_logic.GetChannelFellowCandidateInfoReq")
	proto.RegisterType((*FellowOptionInfo)(nil), "ga.fellow_logic.FellowOptionInfo")
	proto.RegisterType((*GetChannelFellowCandidateInfoResp)(nil), "ga.fellow_logic.GetChannelFellowCandidateInfoResp")
	proto.RegisterType((*GetOnMicFellowListReq)(nil), "ga.fellow_logic.GetOnMicFellowListReq")
	proto.RegisterType((*MicFellowInfo)(nil), "ga.fellow_logic.MicFellowInfo")
	proto.RegisterType((*MicFellowInfoChangeInfo)(nil), "ga.fellow_logic.MicFellowInfoChangeInfo")
	proto.RegisterType((*GetOnMicFellowListResp)(nil), "ga.fellow_logic.GetOnMicFellowListResp")
	proto.RegisterType((*GetSendInviteListReq)(nil), "ga.fellow_logic.GetSendInviteListReq")
	proto.RegisterType((*GetSendInviteListResp)(nil), "ga.fellow_logic.GetSendInviteListResp")
	proto.RegisterType((*ChangeFellowBindTypeReq)(nil), "ga.fellow_logic.ChangeFellowBindTypeReq")
	proto.RegisterType((*ChangeFellowBindTypeResp)(nil), "ga.fellow_logic.ChangeFellowBindTypeResp")
	proto.RegisterType((*GetRareConfigReq)(nil), "ga.fellow_logic.GetRareConfigReq")
	proto.RegisterType((*AnimationConfig)(nil), "ga.fellow_logic.AnimationConfig")
	proto.RegisterType((*SubRareConfig)(nil), "ga.fellow_logic.SubRareConfig")
	proto.RegisterType((*ConnectedForMic)(nil), "ga.fellow_logic.ConnectedForMic")
	proto.RegisterType((*MsgNotifyPictures)(nil), "ga.fellow_logic.MsgNotifyPictures")
	proto.RegisterType((*RareConfig)(nil), "ga.fellow_logic.RareConfig")
	proto.RegisterType((*GetRareConfigResp)(nil), "ga.fellow_logic.GetRareConfigResp")
	proto.RegisterType((*SetBindRelationReq)(nil), "ga.fellow_logic.SetBindRelationReq")
	proto.RegisterType((*SetBindRelationResp)(nil), "ga.fellow_logic.SetBindRelationResp")
	proto.RegisterType((*GetChannelRareConfigReq)(nil), "ga.fellow_logic.GetChannelRareConfigReq")
	proto.RegisterType((*GetChannelRareConfigResp)(nil), "ga.fellow_logic.GetChannelRareConfigResp")
	proto.RegisterType((*GetRareListReq)(nil), "ga.fellow_logic.GetRareListReq")
	proto.RegisterType((*GetRareListResp)(nil), "ga.fellow_logic.GetRareListResp")
	proto.RegisterType((*DelRareReq)(nil), "ga.fellow_logic.DelRareReq")
	proto.RegisterType((*DelRareResp)(nil), "ga.fellow_logic.DelRareResp")
	proto.RegisterType((*FellowHouseRes)(nil), "ga.fellow_logic.FellowHouseRes")
	proto.RegisterType((*FellowHouseCfg)(nil), "ga.fellow_logic.FellowHouseCfg")
	proto.RegisterType((*FellowHouseInfo)(nil), "ga.fellow_logic.FellowHouseInfo")
	proto.RegisterType((*GetFellowHouseInuseListRequest)(nil), "ga.fellow_logic.GetFellowHouseInuseListRequest")
	proto.RegisterType((*GetFellowHouseInuseListResponse)(nil), "ga.fellow_logic.GetFellowHouseInuseListResponse")
	proto.RegisterType((*GetFellowHouseEntryInfoRequest)(nil), "ga.fellow_logic.GetFellowHouseEntryInfoRequest")
	proto.RegisterType((*GetFellowHouseEntryInfoResponse)(nil), "ga.fellow_logic.GetFellowHouseEntryInfoResponse")
	proto.RegisterType((*GetFellowCardInfoRequest)(nil), "ga.fellow_logic.GetFellowCardInfoRequest")
	proto.RegisterType((*SimpleHouseInfo)(nil), "ga.fellow_logic.SimpleHouseInfo")
	proto.RegisterType((*GetFellowCardInfoResponse)(nil), "ga.fellow_logic.GetFellowCardInfoResponse")
	proto.RegisterType((*FellowUpgradeAnimationPushMsg)(nil), "ga.fellow_logic.FellowUpgradeAnimationPushMsg")
	proto.RegisterType((*ExpiringHouseInfo)(nil), "ga.fellow_logic.ExpiringHouseInfo")
	proto.RegisterType((*GetExpiringFellowHouseListRequest)(nil), "ga.fellow_logic.GetExpiringFellowHouseListRequest")
	proto.RegisterType((*GetExpiringFellowHouseListResponse)(nil), "ga.fellow_logic.GetExpiringFellowHouseListResponse")
	proto.RegisterEnum("ga.fellow_logic.FellowType", FellowType_name, FellowType_value)
	proto.RegisterEnum("ga.fellow_logic.FellowBindType", FellowBindType_name, FellowBindType_value)
	proto.RegisterEnum("ga.fellow_logic.FellowBindStatus", FellowBindStatus_name, FellowBindStatus_value)
	proto.RegisterEnum("ga.fellow_logic.BanType", BanType_name, BanType_value)
	proto.RegisterEnum("ga.fellow_logic.SwitchEnum", SwitchEnum_name, SwitchEnum_value)
	proto.RegisterEnum("ga.fellow_logic.InviteStatus", InviteStatus_name, InviteStatus_value)
	proto.RegisterEnum("ga.fellow_logic.FellowSourceType", FellowSourceType_name, FellowSourceType_value)
	proto.RegisterEnum("ga.fellow_logic.FellowHouseRes_ResType", FellowHouseRes_ResType_name, FellowHouseRes_ResType_value)
}

func init() {
	proto.RegisterFile("fellow_logic/fellow-logic_.proto", fileDescriptor_fellow_logic__69cfc50eb589d9b9)
}

var fileDescriptor_fellow_logic__69cfc50eb589d9b9 = []byte{
	// 4602 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3c, 0x4d, 0x73, 0x23, 0x49,
	0x56, 0x53, 0x92, 0x6c, 0x49, 0x4f, 0x92, 0x2d, 0x57, 0xb7, 0xbb, 0xe5, 0x71, 0x7b, 0xec, 0xae,
	0x99, 0xd9, 0xed, 0x35, 0x4b, 0x4f, 0xd0, 0xb3, 0x33, 0x31, 0x01, 0xcd, 0xd0, 0xb6, 0x5a, 0xee,
	0xd6, 0x8e, 0xbf, 0x28, 0xc9, 0x33, 0x3b, 0x0b, 0x41, 0x6d, 0xa9, 0x94, 0x96, 0x8a, 0x2e, 0x55,
	0x55, 0x57, 0x95, 0xba, 0xed, 0x08, 0x0e, 0xb0, 0x87, 0x0d, 0xbe, 0x21, 0x82, 0x8f, 0xeb, 0x12,
	0x1b, 0x04, 0x27, 0xb8, 0x10, 0xc0, 0x89, 0x1b, 0x41, 0xc0, 0x0d, 0x96, 0x3d, 0x41, 0x04, 0x70,
	0xe4, 0x42, 0x04, 0x3f, 0x61, 0x89, 0x7c, 0x99, 0x55, 0xca, 0xaa, 0x92, 0x64, 0x97, 0xbb, 0x7b,
	0x7a, 0x62, 0x6f, 0xaa, 0x97, 0x99, 0x2f, 0x33, 0xdf, 0x77, 0xbe, 0x97, 0x29, 0xd8, 0x3a, 0x25,
	0x96, 0xe5, 0x3c, 0xd7, 0x2c, 0x67, 0x60, 0x1a, 0xef, 0xb1, 0x8f, 0x9f, 0xc6, 0x0f, 0xed, 0xae,
	0xeb, 0x39, 0x81, 0x23, 0x2f, 0x0f, 0xf4, 0xbb, 0x62, 0xa7, 0x37, 0x6b, 0x03, 0x5d, 0xeb, 0xe9,
	0x3e, 0x61, 0xed, 0xca, 0x63, 0x28, 0x1d, 0x5b, 0x7a, 0x40, 0x4e, 0x3c, 0x4b, 0x5e, 0x87, 0xb2,
	0xe1, 0x6a, 0x63, 0xdb, 0x7c, 0x3a, 0x26, 0x0d, 0x69, 0x4b, 0xba, 0x53, 0x56, 0x4b, 0x86, 0x7b,
	0x82, 0xdf, 0xf2, 0x26, 0x54, 0xfa, 0x7a, 0x40, 0xc2, 0xe6, 0x1c, 0x36, 0x03, 0x05, 0xb1, 0x0e,
	0xca, 0x1f, 0x49, 0x50, 0xdf, 0xc3, 0x99, 0x76, 0x75, 0xe3, 0xc9, 0xc0, 0x73, 0xc6, 0x76, 0x5f,
	0x7e, 0x17, 0x96, 0x7a, 0xd1, 0x97, 0x36, 0xf6, 0x2c, 0x8e, 0xb7, 0x36, 0x81, 0xd2, 0x99, 0x37,
	0xa1, 0xe2, 0x3b, 0x63, 0xcf, 0x20, 0x5a, 0x70, 0xee, 0x32, 0xe4, 0x35, 0x15, 0x18, 0xa8, 0x7b,
	0xee, 0x12, 0xb9, 0x0e, 0xf9, 0x51, 0xff, 0x83, 0x46, 0x1e, 0x07, 0xd3, 0x9f, 0x09, 0xcc, 0xe6,
	0x68, 0xd0, 0x28, 0x24, 0x31, 0xb7, 0x47, 0x03, 0xe5, 0xcf, 0x25, 0x28, 0xa9, 0xba, 0x47, 0xda,
	0xf6, 0xa9, 0x23, 0xdf, 0x84, 0xa2, 0xa7, 0x7b, 0x44, 0x33, 0xfb, 0xb8, 0x8c, 0x9a, 0xba, 0x48,
	0x3f, 0xdb, 0x7d, 0xf9, 0x2d, 0xa8, 0xf8, 0xe3, 0x9e, 0x16, 0x36, 0xb2, 0xf9, 0xcb, 0xfe, 0xb8,
	0xa7, 0xb2, 0xf6, 0x75, 0x28, 0xf7, 0xf5, 0x73, 0xcd, 0x70, 0xc6, 0x76, 0x80, 0x8b, 0xa8, 0xa9,
	0xa5, 0xbe, 0x7e, 0xde, 0xa4, 0xdf, 0xf2, 0x6d, 0xa8, 0x7a, 0x64, 0xa4, 0x9b, 0x36, 0x6f, 0x2f,
	0x60, 0x7b, 0x85, 0xc1, 0x58, 0x97, 0x4d, 0xa8, 0xf4, 0x4c, 0xbb, 0xaf, 0xf9, 0x81, 0x1e, 0x8c,
	0xfd, 0xc6, 0xc2, 0x96, 0x74, 0xa7, 0xa4, 0x02, 0x05, 0x75, 0x10, 0xa2, 0xfc, 0x45, 0x1e, 0x2a,
	0x74, 0xae, 0xae, 0xde, 0xc3, 0x95, 0xae, 0xc2, 0x62, 0xe0, 0x68, 0xe3, 0x68, 0xa1, 0x0b, 0x81,
	0x73, 0x62, 0xf6, 0xe5, 0x0d, 0x80, 0xc0, 0xd1, 0x74, 0x83, 0x4d, 0xc4, 0x78, 0x50, 0x0e, 0x9c,
	0x1d, 0x06, 0x90, 0xb7, 0xa0, 0x1a, 0x38, 0x9a, 0x6d, 0x1a, 0x4f, 0x34, 0x5b, 0x1f, 0x11, 0x4e,
	0x2e, 0x08, 0x9c, 0x43, 0xd3, 0x78, 0x72, 0xa8, 0x8f, 0x08, 0xdd, 0x08, 0x2e, 0x04, 0xc9, 0xcc,
	0x16, 0x5a, 0xa2, 0x00, 0x24, 0xf2, 0x26, 0x54, 0xb8, 0xa8, 0x60, 0xf3, 0x02, 0xe3, 0x02, 0x03,
	0x25, 0x3a, 0x20, 0xfa, 0x45, 0x86, 0x9e, 0x81, 0x10, 0xfd, 0x37, 0xa0, 0xe4, 0x8c, 0x03, 0xa4,
	0x63, 0xa3, 0xb8, 0x25, 0xdd, 0xa9, 0xdc, 0x5b, 0xbb, 0x9b, 0x10, 0xc0, 0xbb, 0x21, 0x37, 0xd4,
	0xa2, 0x33, 0x0e, 0xe8, 0x87, 0xfc, 0x00, 0xc0, 0xf5, 0x88, 0x4f, 0xec, 0x40, 0xeb, 0x0d, 0x1a,
	0x25, 0x1c, 0x77, 0x3b, 0x35, 0x2e, 0x29, 0x5b, 0x6a, 0x99, 0x0f, 0xda, 0x1d, 0x50, 0xba, 0x20,
	0xef, 0x18, 0x5d, 0xca, 0x8c, 0x7d, 0x14, 0xc2, 0xc8, 0x7f, 0x0f, 0xaa, 0x63, 0x9f, 0x78, 0x9a,
	0xeb, 0x39, 0xa7, 0xa6, 0x45, 0x1a, 0x15, 0x9c, 0x62, 0x99, 0x4e, 0x71, 0xe2, 0x13, 0xef, 0x98,
	0x81, 0xd5, 0xca, 0x78, 0xf2, 0xc1, 0x39, 0xe0, 0x93, 0xb3, 0x46, 0x35, 0xe4, 0x40, 0x87, 0x9c,
	0x29, 0xbf, 0x2f, 0x41, 0xe5, 0x91, 0xa7, 0xf7, 0x4d, 0x7b, 0x80, 0x8c, 0xba, 0x0e, 0x0b, 0x16,
	0x79, 0x46, 0xac, 0x90, 0x4f, 0xf8, 0x41, 0x45, 0x62, 0xc0, 0x3a, 0x31, 0x4a, 0x31, 0x4e, 0x55,
	0x38, 0x0c, 0x49, 0x25, 0x74, 0x31, 0x0d, 0xc7, 0xe6, 0xbc, 0x0a, 0xbb, 0xb4, 0x0d, 0xc7, 0x96,
	0xdf, 0x86, 0x5a, 0xd8, 0xc5, 0x70, 0x2c, 0xc7, 0xe3, 0x12, 0x1e, 0x8e, 0x6b, 0x52, 0x98, 0xf2,
	0x3b, 0x45, 0x00, 0x46, 0x1a, 0x5c, 0x4f, 0x1d, 0xf2, 0xa1, 0xd4, 0xe4, 0x55, 0xfa, 0x53, 0x6e,
	0x40, 0x31, 0x2e, 0x30, 0xe1, 0x27, 0x15, 0x86, 0xa4, 0xac, 0x94, 0xec, 0x50, 0x52, 0x6e, 0x43,
	0x35, 0x24, 0x3f, 0xee, 0x8f, 0x4b, 0x35, 0x83, 0xed, 0xe3, 0x2e, 0x2f, 0x94, 0x97, 0x9b, 0x50,
	0x44, 0xb5, 0xb1, 0x03, 0x94, 0x95, 0x9a, 0xba, 0x48, 0x95, 0x86, 0xe9, 0x83, 0x28, 0x48, 0xc5,
	0x94, 0x20, 0xc5, 0xe4, 0xb4, 0x94, 0x90, 0xd3, 0xc9, 0xd2, 0x5c, 0xc7, 0x8c, 0xf8, 0xcd, 0x31,
	0x1e, 0x53, 0x90, 0xbc, 0x03, 0x30, 0xb1, 0x03, 0x0d, 0xb8, 0xac, 0x48, 0x09, 0x83, 0xe4, 0xaf,
	0xc0, 0xb2, 0xe1, 0x6a, 0xa1, 0x60, 0x22, 0x8f, 0x56, 0x98, 0x85, 0x31, 0xdc, 0x63, 0x06, 0x45,
	0x2e, 0x7d, 0x08, 0x65, 0xd7, 0x42, 0xcb, 0xe8, 0x59, 0x0d, 0x79, 0x86, 0xd0, 0x87, 0x36, 0x56,
	0x2d, 0xb9, 0xa1, 0xb5, 0x4d, 0xd8, 0x84, 0x6b, 0x8c, 0x7a, 0x13, 0x9b, 0x20, 0xbf, 0x0f, 0xa5,
	0x9e, 0x6e, 0x33, 0x12, 0x5c, 0xdf, 0x92, 0xee, 0x2c, 0xdd, 0x6b, 0xa4, 0xf0, 0xee, 0xea, 0x36,
	0x25, 0x89, 0x5a, 0xec, 0xb1, 0x1f, 0x74, 0x35, 0x88, 0x15, 0x55, 0x70, 0xf5, 0x22, 0x15, 0x44,
	0x9a, 0xa2, 0x0e, 0x26, 0x55, 0xe4, 0xc6, 0x25, 0x54, 0x64, 0xc2, 0x45, 0xa4, 0xce, 0x4d, 0x91,
	0x8b, 0x48, 0x9a, 0x4d, 0xa8, 0x18, 0xba, 0xd7, 0x47, 0xe9, 0x1d, 0x7b, 0x8d, 0x06, 0xeb, 0x40,
	0x41, 0x4d, 0x84, 0x50, 0x1a, 0x7b, 0x8e, 0x33, 0xd2, 0x46, 0xfe, 0x20, 0xec, 0xb4, 0xb6, 0x95,
	0xa7, 0x34, 0xa6, 0xe0, 0x03, 0x7f, 0xc0, 0xfb, 0xdd, 0x86, 0xaa, 0x65, 0x0e, 0xf4, 0x60, 0xec,
	0x31, 0x32, 0xbf, 0xc9, 0x94, 0x25, 0x84, 0x51, 0x72, 0xfe, 0x82, 0xa0, 0x4f, 0xf6, 0xa9, 0xd3,
	0x58, 0xc7, 0x0d, 0xdc, 0x4a, 0xed, 0x5d, 0x50, 0xde, 0x89, 0xb6, 0x51, 0xcd, 0xf9, 0x19, 0xc8,
	0x1b, 0xa7, 0x83, 0xc6, 0x2d, 0x1c, 0xb7, 0x39, 0x43, 0x56, 0x1e, 0x3b, 0x63, 0x9f, 0x34, 0x4f,
	0x07, 0x2a, 0xed, 0x4b, 0x95, 0x8d, 0x1a, 0x88, 0x0d, 0x64, 0x1d, 0xfd, 0xa9, 0xfc, 0x5b, 0xe4,
	0x04, 0xdb, 0xf6, 0x33, 0x33, 0x20, 0x94, 0x76, 0xaf, 0x42, 0x27, 0x99, 0xe0, 0x17, 0xd2, 0x82,
	0xff, 0x36, 0xd4, 0x4c, 0x9c, 0x59, 0xf4, 0x35, 0x35, 0xb5, 0xca, 0x80, 0x5c, 0xb2, 0xf8, 0xba,
	0x17, 0xa3, 0x75, 0xd3, 0x69, 0xf9, 0x30, 0xb3, 0xcf, 0xd5, 0xb1, 0xc4, 0x00, 0xed, 0xbe, 0xf2,
	0xcf, 0x39, 0x58, 0x61, 0x9b, 0x0a, 0xe5, 0x9e, 0x3b, 0x53, 0x33, 0x20, 0x23, 0xc1, 0x99, 0xd2,
	0xcf, 0x76, 0x5f, 0x96, 0xa1, 0x20, 0x18, 0x3d, 0xfc, 0x4d, 0xcd, 0xe4, 0x33, 0xdd, 0x1a, 0x13,
	0xee, 0x3c, 0xd9, 0x07, 0xed, 0x89, 0x92, 0xc3, 0xec, 0x1a, 0xfe, 0xa6, 0xa6, 0xdc, 0xf5, 0xcc,
	0x30, 0x12, 0x60, 0xab, 0x2f, 0x23, 0x04, 0xe5, 0xfb, 0x10, 0x56, 0x58, 0x04, 0xa2, 0x09, 0xfa,
	0xbd, 0x78, 0x59, 0xfd, 0xae, 0xb3, 0xb1, 0x42, 0x80, 0xb2, 0x0f, 0xf5, 0xd1, 0xd8, 0x0a, 0x4c,
	0x11, 0x5d, 0xf1, 0xb2, 0xe8, 0x96, 0x71, 0xa8, 0x80, 0x6d, 0x13, 0x2a, 0xba, 0xe5, 0x11, 0xbd,
	0x7f, 0xae, 0x39, 0xcf, 0x6d, 0x6e, 0xb8, 0x80, 0x83, 0x8e, 0x9e, 0xdb, 0xca, 0x3f, 0x2e, 0xc4,
	0xe5, 0x03, 0x29, 0x19, 0x23, 0xbe, 0x14, 0x27, 0xbe, 0xbc, 0x06, 0xa5, 0x53, 0xcf, 0x19, 0x61,
	0x2c, 0x90, 0x43, 0x09, 0x2a, 0xd2, 0x6f, 0x1a, 0x0d, 0x50, 0x71, 0xa0, 0x4d, 0xa1, 0x28, 0x71,
	0x17, 0x42, 0x61, 0x61, 0x44, 0xf0, 0x36, 0xd4, 0xb0, 0x0b, 0x15, 0x21, 0x64, 0x0a, 0x77, 0x21,
	0x14, 0x78, 0xc8, 0x61, 0x71, 0x63, 0xbb, 0x30, 0x3f, 0x28, 0x58, 0x4c, 0x19, 0xf9, 0xa4, 0x50,
	0x16, 0xd3, 0x42, 0x49, 0xed, 0x80, 0x47, 0xa8, 0x8d, 0x0c, 0xcc, 0x51, 0x68, 0xcf, 0x81, 0x81,
	0xba, 0xe6, 0x28, 0x15, 0x58, 0x94, 0x53, 0xfe, 0x60, 0x12, 0x0f, 0x01, 0xd2, 0x60, 0x6a, 0x3c,
	0x54, 0x49, 0xc6, 0x43, 0x9b, 0x50, 0xe1, 0xf1, 0x10, 0xa2, 0xad, 0x8a, 0xe1, 0x10, 0xee, 0xfc,
	0x06, 0x2c, 0x72, 0x35, 0xa9, 0x31, 0x11, 0x66, 0x5f, 0x74, 0xe0, 0x73, 0x33, 0x18, 0x6a, 0x63,
	0xdb, 0x72, 0x8c, 0x27, 0x8d, 0x25, 0x16, 0xaf, 0x51, 0xd0, 0x09, 0x42, 0x22, 0xae, 0x50, 0x35,
	0x5a, 0xc6, 0xa1, 0xc8, 0x95, 0x0e, 0x39, 0x13, 0x02, 0x87, 0xba, 0x10, 0x38, 0xc8, 0x2d, 0xa8,
	0x46, 0xbe, 0x84, 0xda, 0xa7, 0x15, 0x14, 0x32, 0x65, 0x86, 0x90, 0x09, 0x8a, 0xa6, 0x56, 0x5c,
	0x41, 0xeb, 0x6e, 0x43, 0x95, 0x2d, 0x4a, 0x43, 0x9d, 0x40, 0x87, 0x53, 0x53, 0x2b, 0x0c, 0x76,
	0x4c, 0x41, 0x94, 0x28, 0xc6, 0x50, 0xb7, 0x6d, 0x62, 0x51, 0x79, 0x62, 0x7e, 0xa5, 0xcc, 0x21,
	0x4c, 0xa0, 0x08, 0x65, 0x36, 0xe5, 0xc4, 0x75, 0xb6, 0x74, 0x62, 0xf7, 0x91, 0x0d, 0x77, 0xa0,
	0x8e, 0xbb, 0x12, 0x79, 0xb1, 0x8a, 0x44, 0x5b, 0xa2, 0xf0, 0xbd, 0x88, 0x1f, 0xca, 0x10, 0x96,
	0xf6, 0x22, 0x11, 0xc0, 0x85, 0x6d, 0xc3, 0x0a, 0x53, 0x24, 0x51, 0x5a, 0x98, 0x61, 0x60, 0x6a,
	0x32, 0xe9, 0x9f, 0xea, 0x2b, 0x98, 0x0b, 0xb1, 0x2f, 0xce, 0xb4, 0x0f, 0xf5, 0x47, 0x24, 0x60,
	0x80, 0x7d, 0xd3, 0x0f, 0x54, 0xf2, 0x54, 0xfe, 0x0a, 0xf5, 0x8c, 0x3e, 0xd1, 0x3c, 0xf2, 0x14,
	0xa7, 0xa8, 0xdc, 0xab, 0x50, 0x3a, 0xee, 0xea, 0x3e, 0x51, 0xc9, 0x53, 0xea, 0x0c, 0xf1, 0x47,
	0x68, 0x78, 0x73, 0x91, 0xe1, 0x55, 0x7e, 0x9c, 0x87, 0x95, 0x04, 0x3a, 0xdf, 0x95, 0xbf, 0x06,
	0x65, 0x8e, 0xcf, 0x77, 0x39, 0xc2, 0xea, 0x04, 0xa1, 0xef, 0xaa, 0xa5, 0x1e, 0xff, 0x95, 0x46,
	0x29, 0x3f, 0x80, 0x1a, 0xb7, 0x48, 0x6c, 0x37, 0xa8, 0x86, 0x95, 0x7b, 0xeb, 0x33, 0x38, 0x8b,
	0x2c, 0xad, 0xb2, 0x11, 0x0c, 0x22, 0x7f, 0x0c, 0x55, 0x91, 0x1c, 0x8d, 0xc2, 0x56, 0xfe, 0x22,
	0x04, 0x15, 0x81, 0x4c, 0xf2, 0x5d, 0xb8, 0x16, 0x69, 0xa0, 0x6f, 0x06, 0xa6, 0x63, 0x63, 0xc8,
	0xc5, 0x34, 0x79, 0x25, 0x54, 0x44, 0xd6, 0x42, 0xa3, 0xaf, 0xaf, 0x83, 0xec, 0x12, 0x9b, 0xbb,
	0x4a, 0xb4, 0x3b, 0x93, 0x08, 0xad, 0xce, 0x5b, 0x98, 0x79, 0xa2, 0xbd, 0xef, 0x03, 0x97, 0x2e,
	0x26, 0xb7, 0xc5, 0x19, 0xbb, 0x63, 0x8a, 0x81, 0x8b, 0x83, 0x71, 0xf4, 0x9b, 0xb2, 0xda, 0xa7,
	0xe2, 0x16, 0x4e, 0x84, 0x8a, 0xca, 0x0c, 0xc0, 0x32, 0x6d, 0xe0, 0xf3, 0xa0, 0xba, 0x3e, 0x80,
	0x1a, 0x46, 0xf1, 0x81, 0xde, 0xd3, 0x2c, 0xd3, 0xa7, 0x81, 0x5d, 0x7e, 0xaa, 0x0f, 0x17, 0x4e,
	0x4a, 0x6a, 0xc5, 0x63, 0x1f, 0x94, 0x99, 0x54, 0x3b, 0xfc, 0x67, 0xa6, 0xab, 0x3d, 0x33, 0x7d,
	0xb3, 0x67, 0x11, 0x34, 0x16, 0x25, 0xb5, 0x42, 0x61, 0x9f, 0x32, 0x90, 0xe2, 0x03, 0x4c, 0x96,
	0x9a, 0x52, 0x27, 0x29, 0xad, 0x4e, 0x93, 0x2e, 0x2c, 0x10, 0xce, 0x89, 0x5d, 0xa2, 0x40, 0x98,
	0x77, 0xf1, 0xcd, 0x20, 0xf4, 0x71, 0x9c, 0x0a, 0x1d, 0x33, 0x20, 0xca, 0x6f, 0x4b, 0xb0, 0x16,
	0x89, 0x5d, 0x53, 0xb7, 0xfb, 0x26, 0x3d, 0x38, 0x67, 0x15, 0xe7, 0x98, 0x9d, 0xce, 0x25, 0xec,
	0xb4, 0x0c, 0x05, 0x57, 0x1f, 0x84, 0x93, 0xe3, 0x6f, 0xea, 0x75, 0xc5, 0x23, 0x29, 0xfb, 0x50,
	0xfe, 0x4a, 0x82, 0x37, 0x67, 0x2d, 0x26, 0x9b, 0x32, 0xec, 0x46, 0x66, 0x1b, 0xd9, 0x95, 0x43,
	0x76, 0xdd, 0x9e, 0x29, 0xb7, 0x61, 0x40, 0x14, 0x5a, 0x76, 0x64, 0xd9, 0x16, 0x54, 0x4d, 0x5f,
	0xf3, 0x88, 0x6e, 0x0c, 0x35, 0x62, 0xf7, 0x71, 0xfd, 0x25, 0x15, 0x4c, 0x5f, 0xa5, 0xa0, 0x96,
	0xdd, 0x57, 0xfe, 0x70, 0x2a, 0xf1, 0x90, 0xf9, 0x19, 0x88, 0x47, 0x5d, 0x85, 0xee, 0x0d, 0x48,
	0x10, 0x79, 0xd2, 0x9a, 0x5a, 0x66, 0x10, 0xea, 0x49, 0xbe, 0x0e, 0x32, 0x35, 0x91, 0x03, 0x12,
	0xb3, 0x5f, 0x6c, 0x31, 0x75, 0xd6, 0x32, 0x31, 0x60, 0xca, 0x0f, 0xf2, 0xd3, 0x48, 0xc8, 0x96,
	0x94, 0x8d, 0x84, 0x49, 0xb7, 0xc0, 0x68, 0x98, 0xd9, 0x2d, 0x7c, 0x0c, 0x65, 0x0c, 0xdf, 0x11,
	0x47, 0x7e, 0x6e, 0xfc, 0x22, 0xf0, 0xa1, 0x44, 0xc7, 0xe0, 0xf8, 0x6f, 0x41, 0x23, 0x66, 0x91,
	0x1d, 0x17, 0xcd, 0x08, 0xb2, 0x95, 0x99, 0xa3, 0x59, 0x11, 0x71, 0xe8, 0x00, 0xd4, 0x55, 0xc1,
	0x24, 0x1d, 0xe1, 0x70, 0xe4, 0xef, 0xfb, 0x70, 0x63, 0xa8, 0xfb, 0x5a, 0x0c, 0xfb, 0xa9, 0x49,
	0xac, 0x3e, 0xcf, 0x82, 0x5c, 0x1b, 0xea, 0xfe, 0xc1, 0x64, 0xe4, 0x1e, 0x6d, 0x4a, 0xa9, 0xe5,
	0x62, 0x5a, 0x2d, 0xb7, 0xa0, 0x4a, 0xf1, 0x1a, 0x2e, 0xc7, 0x56, 0x64, 0x72, 0x33, 0xd4, 0xfd,
	0xa6, 0x8b, 0x48, 0x94, 0xff, 0x90, 0xe0, 0x5a, 0x87, 0xd8, 0x7d, 0x71, 0xdb, 0x2f, 0x51, 0x62,
	0x62, 0xda, 0x98, 0x4f, 0x68, 0xe3, 0xc6, 0x24, 0xa5, 0x61, 0xf6, 0xb9, 0xfa, 0x85, 0xf9, 0x8a,
	0x76, 0xff, 0x52, 0x99, 0x16, 0x31, 0x00, 0x59, 0x4c, 0x06, 0x20, 0xca, 0xef, 0x49, 0x70, 0x3d,
	0xbd, 0xb9, 0x6c, 0xb2, 0xf7, 0x55, 0x58, 0x0e, 0x13, 0x57, 0x63, 0xcf, 0x23, 0xb6, 0x71, 0xce,
	0xfd, 0xda, 0x12, 0xcf, 0x5d, 0x71, 0xa8, 0x90, 0xe1, 0x0a, 0x7a, 0x44, 0x67, 0xb9, 0x8a, 0x7c,
	0x98, 0xe1, 0xea, 0x52, 0x90, 0xf2, 0x00, 0x6e, 0x44, 0x0a, 0xc1, 0x56, 0x93, 0xd1, 0xba, 0x29,
	0xbf, 0x29, 0xc1, 0xcd, 0xa9, 0x28, 0x32, 0xdb, 0x24, 0xee, 0x6b, 0x2e, 0x6d, 0x93, 0x98, 0xd3,
	0x32, 0xa3, 0x29, 0x15, 0x5d, 0xd0, 0xee, 0x49, 0x97, 0xdd, 0xf3, 0x76, 0x3f, 0xa3, 0xb9, 0x9e,
	0x84, 0xf5, 0xb9, 0xc4, 0x99, 0xea, 0x77, 0x25, 0x58, 0x9f, 0x39, 0xc7, 0x55, 0x77, 0xcc, 0x2d,
	0x88, 0x94, 0x69, 0xc7, 0xf4, 0xb7, 0xf2, 0x3d, 0x09, 0x56, 0x1f, 0xeb, 0x76, 0xdf, 0x22, 0x57,
	0xd5, 0x96, 0x79, 0xbb, 0xa5, 0x81, 0xa5, 0xe9, 0xd3, 0x38, 0x9d, 0xb8, 0x01, 0x0f, 0x05, 0xb8,
	0x6d, 0x5d, 0x32, 0xfd, 0x1d, 0x04, 0xb3, 0x19, 0x95, 0x26, 0xdc, 0x98, 0xb6, 0x8e, 0x4c, 0x14,
	0x51, 0x7e, 0x09, 0xae, 0x37, 0x87, 0xc4, 0x78, 0xf2, 0x02, 0x7b, 0x99, 0xe9, 0x68, 0x95, 0x1f,
	0x4a, 0xb0, 0x3a, 0x05, 0x7b, 0x36, 0x9e, 0x6d, 0x42, 0x85, 0xb9, 0x3c, 0xcb, 0x1c, 0x99, 0x2c,
	0x09, 0x50, 0x52, 0x01, 0x41, 0xfb, 0x14, 0x42, 0x4f, 0xd7, 0x86, 0x2b, 0x66, 0x01, 0x16, 0x0d,
	0x37, 0x3c, 0x09, 0x19, 0x2e, 0x1e, 0x2f, 0x42, 0xa7, 0xee, 0xd2, 0xe3, 0x45, 0x22, 0x4a, 0x5b,
	0xc8, 0x14, 0xa5, 0x29, 0x3b, 0x70, 0x93, 0xb5, 0xec, 0xc5, 0x82, 0xc5, 0x2c, 0xea, 0x3b, 0x84,
	0xc6, 0x74, 0x14, 0xd9, 0x08, 0x93, 0x34, 0x35, 0xb9, 0xb4, 0xa9, 0xf9, 0x1c, 0xea, 0x27, 0x76,
	0x8f, 0x9e, 0xb7, 0xd9, 0x54, 0x2f, 0xcf, 0xa6, 0x2b, 0x1f, 0xc3, 0x4a, 0x02, 0x75, 0x36, 0xc1,
	0xd3, 0xe0, 0x46, 0x53, 0xb7, 0x0d, 0x62, 0xbd, 0xaa, 0x05, 0x3e, 0x84, 0x9b, 0x53, 0x27, 0xc8,
	0xb6, 0xcc, 0x5f, 0x86, 0x55, 0x86, 0xe5, 0x55, 0x28, 0xbb, 0xf2, 0x07, 0x52, 0x48, 0x85, 0x2f,
	0x8b, 0x73, 0xea, 0x09, 0x87, 0x3e, 0xcc, 0x48, 0x64, 0xd9, 0xeb, 0xbb, 0xb0, 0xc4, 0x55, 0x28,
	0x9e, 0xb2, 0xab, 0x31, 0x28, 0xcf, 0x35, 0x28, 0x3f, 0xcc, 0x81, 0x9c, 0x9c, 0x24, 0xdb, 0x8e,
	0x37, 0x80, 0x47, 0x00, 0x22, 0xeb, 0x19, 0x24, 0xcc, 0xf6, 0x88, 0x79, 0x96, 0x7c, 0x3a, 0xcf,
	0x72, 0x89, 0x9c, 0xfd, 0x5d, 0xb8, 0xc6, 0xe8, 0x19, 0xb0, 0x3e, 0x1c, 0x19, 0x3f, 0x2b, 0xf2,
	0x26, 0xec, 0xca, 0x50, 0xde, 0x81, 0xba, 0x4d, 0xce, 0xe2, 0x9d, 0x59, 0x34, 0xb6, 0x44, 0xe1,
	0x53, 0x7a, 0xea, 0xcf, 0x75, 0xaf, 0xcf, 0x17, 0xc0, 0x32, 0x89, 0xd8, 0x73, 0x87, 0x82, 0xd9,
	0x1a, 0xbe, 0x0a, 0xcb, 0xfa, 0x33, 0xdd, 0xb4, 0xf4, 0x9e, 0x45, 0x58, 0x77, 0x3c, 0x11, 0x96,
	0xd5, 0xa5, 0x08, 0x8c, 0xbd, 0x95, 0x9e, 0x10, 0xf8, 0xf3, 0xd0, 0xf7, 0x21, 0x09, 0x74, 0xd3,
	0x7a, 0x89, 0x1a, 0xf5, 0xa3, 0x9c, 0xe0, 0xec, 0x13, 0x93, 0x5c, 0x39, 0x94, 0x17, 0x42, 0x8f,
	0x2c, 0xa1, 0x3c, 0x06, 0xcc, 0xed, 0x48, 0xde, 0x38, 0x94, 0xc7, 0xf3, 0x97, 0x41, 0xc4, 0x65,
	0x92, 0x83, 0xb2, 0xd5, 0x70, 0xd0, 0xd7, 0x2c, 0xa4, 0x32, 0x6f, 0x33, 0x6b, 0x38, 0x31, 0x27,
	0x59, 0x4c, 0x38, 0xc9, 0x5f, 0x13, 0xa3, 0x53, 0xbe, 0x9c, 0x97, 0x18, 0x7b, 0xc7, 0xc3, 0xeb,
	0x7c, 0x22, 0xbc, 0x56, 0xfe, 0x53, 0x82, 0xd5, 0x29, 0xd3, 0xbf, 0x36, 0x03, 0x24, 0x1f, 0x24,
	0x4e, 0x79, 0x05, 0x9c, 0x79, 0x3b, 0x5d, 0x26, 0x62, 0x9d, 0xe8, 0xda, 0xdb, 0x01, 0x19, 0x75,
	0xcc, 0x91, 0x6b, 0x91, 0xd4, 0x69, 0x4f, 0xf9, 0x1b, 0x09, 0xd6, 0x66, 0x76, 0x9d, 0x9d, 0x98,
	0x8f, 0xd2, 0x01, 0x39, 0x21, 0x1d, 0x80, 0xb5, 0xf7, 0xa1, 0xf3, 0x5c, 0x23, 0xa7, 0xa7, 0xc4,
	0x08, 0xcd, 0x0a, 0x50, 0x50, 0x0b, 0x21, 0xf2, 0x3b, 0xb0, 0x24, 0x74, 0xd0, 0x9e, 0xdd, 0xe3,
	0x72, 0x54, 0x9d, 0xf4, 0xf9, 0xf4, 0x1e, 0xa5, 0x42, 0xac, 0x56, 0xc6, 0x24, 0x29, 0x5a, 0xb6,
	0xe1, 0xd8, 0xca, 0xaf, 0xc0, 0xaa, 0x10, 0xf2, 0xd2, 0x60, 0xf7, 0xc4, 0xec, 0xbf, 0x44, 0x55,
	0xfe, 0x0d, 0x29, 0x76, 0x08, 0x89, 0x26, 0xc8, 0xc6, 0xf7, 0xfb, 0x93, 0xaa, 0xd6, 0x24, 0x9c,
	0x9e, 0x9b, 0x8c, 0x0b, 0x4b, 0x5e, 0x94, 0x35, 0x3f, 0x90, 0x60, 0xbd, 0xc9, 0x72, 0xad, 0xaf,
	0x4f, 0x01, 0x12, 0x29, 0xe0, 0x42, 0x22, 0x05, 0xac, 0xfc, 0x89, 0x04, 0xb7, 0x66, 0x2f, 0xf2,
	0xf5, 0xf9, 0xe9, 0x5f, 0xcf, 0xc1, 0x9b, 0x74, 0x41, 0x7c, 0x6d, 0x3f, 0x89, 0x07, 0xf7, 0x04,
	0x6b, 0x8a, 0x49, 0xd6, 0xfc, 0xb1, 0x04, 0xeb, 0x33, 0x49, 0xf0, 0xfa, 0x38, 0xf3, 0x97, 0x12,
	0xdc, 0x62, 0xe7, 0xb2, 0x17, 0xe4, 0xcd, 0xcb, 0x39, 0x26, 0x5e, 0x24, 0xe0, 0xdf, 0x84, 0x8d,
	0x39, 0xab, 0xcd, 0x16, 0x2c, 0xff, 0x7d, 0x1e, 0xea, 0x31, 0x34, 0x07, 0xfe, 0x20, 0x56, 0x95,
	0x93, 0xe6, 0x57, 0xe5, 0x72, 0x97, 0xa8, 0xca, 0xe5, 0x2f, 0xaa, 0xca, 0xbd, 0xfc, 0xab, 0x3a,
	0x93, 0x8a, 0x5a, 0x71, 0x76, 0x45, 0xad, 0x74, 0x41, 0x45, 0xad, 0x9c, 0xaa, 0xa8, 0x89, 0x85,
	0x31, 0x98, 0x55, 0x18, 0xab, 0xcc, 0x2b, 0x8c, 0x55, 0xaf, 0x56, 0x18, 0x9b, 0x51, 0xca, 0x53,
	0x7e, 0x4b, 0x82, 0xeb, 0x8f, 0x48, 0xa0, 0x3a, 0x61, 0xfd, 0xea, 0x05, 0x8a, 0x48, 0x35, 0x56,
	0xf1, 0x59, 0x83, 0x12, 0x46, 0xba, 0x63, 0x34, 0xbd, 0x79, 0xba, 0x47, 0xfa, 0x4d, 0xc9, 0x77,
	0x0b, 0xc0, 0xb1, 0xb5, 0x91, 0x69, 0x60, 0x63, 0x01, 0x1b, 0x4b, 0x8e, 0x7d, 0x60, 0x1a, 0xd4,
	0x41, 0xfd, 0xad, 0x84, 0x1e, 0x30, 0xb9, 0x96, 0x2b, 0x57, 0xa0, 0xf8, 0x7a, 0xee, 0xc7, 0xd3,
	0xf0, 0xf9, 0x8b, 0xcb, 0x47, 0x62, 0x02, 0x3e, 0x59, 0x33, 0x29, 0xa4, 0x6b, 0x26, 0x04, 0x6e,
	0x3d, 0x22, 0xc1, 0x8e, 0x65, 0xbd, 0xb8, 0x61, 0x16, 0xb4, 0x36, 0x97, 0xd4, 0xda, 0xff, 0x95,
	0x60, 0x63, 0xce, 0x3c, 0xd9, 0xc8, 0xf4, 0x09, 0xd4, 0xc5, 0xc2, 0x53, 0xb6, 0x64, 0xe0, 0xd2,
	0xa4, 0x34, 0x85, 0x34, 0xea, 0xc0, 0x75, 0x8f, 0x18, 0xc4, 0x7c, 0x46, 0xe2, 0x08, 0xf3, 0x97,
	0x45, 0x28, 0x87, 0xc3, 0x27, 0x48, 0x95, 0xef, 0x4a, 0xb0, 0xf5, 0x88, 0x04, 0xb1, 0xbd, 0xbe,
	0xaa, 0xf2, 0x86, 0x10, 0x13, 0xe6, 0xc5, 0x98, 0x50, 0x79, 0x1a, 0xde, 0x47, 0x60, 0x29, 0xfb,
	0xf0, 0x3e, 0xc2, 0xc4, 0xf2, 0x48, 0xf3, 0x2d, 0x4f, 0xee, 0x22, 0xcb, 0x93, 0x4f, 0x5a, 0x1e,
	0xe5, 0xcf, 0xf2, 0x70, 0xfb, 0x82, 0x7d, 0xbf, 0x68, 0x0d, 0x45, 0x7a, 0x1d, 0x35, 0x94, 0x23,
	0x90, 0x67, 0x56, 0x4f, 0x66, 0x21, 0x9a, 0x50, 0x5d, 0xad, 0x9f, 0x7e, 0x79, 0x4a, 0x27, 0x2c,
	0x52, 0x3f, 0xa2, 0x76, 0xeb, 0x6a, 0x46, 0xf3, 0x02, 0x4d, 0xff, 0x6e, 0x0e, 0x6a, 0x11, 0xee,
	0xe4, 0xbd, 0x45, 0x6e, 0xd5, 0x2e, 0x9d, 0x0e, 0x61, 0x67, 0xdb, 0xfc, 0x85, 0xf7, 0x13, 0x0b,
	0x29, 0x51, 0x9d, 0x7b, 0xf1, 0xe5, 0x3e, 0x54, 0xc3, 0x4c, 0x09, 0x5e, 0xa6, 0x5b, 0xbc, 0xe8,
	0x32, 0x5d, 0x85, 0x77, 0xc7, 0xfb, 0x74, 0xc9, 0x1b, 0x6b, 0xc5, 0xd4, 0x8d, 0x35, 0xe5, 0x5b,
	0x70, 0x33, 0x46, 0x83, 0x26, 0x56, 0x19, 0x91, 0x1a, 0x3f, 0x0f, 0x40, 0x7d, 0x08, 0xbf, 0x0f,
	0x20, 0xa1, 0x08, 0xbd, 0x95, 0x9a, 0x39, 0x36, 0x5a, 0x2d, 0x8f, 0xc2, 0x4f, 0x6a, 0x59, 0x6e,
	0x4c, 0xe3, 0x5f, 0x36, 0xb5, 0x8a, 0x2f, 0x22, 0x97, 0x75, 0x11, 0x1f, 0xa3, 0xdf, 0xed, 0xc4,
	0x0c, 0x69, 0x96, 0x84, 0xf2, 0xf7, 0x98, 0xb3, 0x4c, 0x22, 0xf8, 0xe2, 0xab, 0x41, 0xff, 0x92,
	0x83, 0x9b, 0x4d, 0xa1, 0x02, 0xbc, 0xcb, 0x25, 0x24, 0x8b, 0x3e, 0xac, 0xc2, 0xa2, 0xe3, 0x0a,
	0x82, 0xbc, 0xe0, 0xb8, 0x61, 0xb4, 0x35, 0xb1, 0xda, 0xf9, 0xa4, 0xd5, 0x7e, 0x07, 0xf0, 0xde,
	0x8d, 0x96, 0x8c, 0x03, 0x31, 0x50, 0x0c, 0x97, 0x91, 0xbc, 0xb5, 0x23, 0x08, 0xb3, 0x70, 0x6b,
	0x07, 0x7b, 0xb2, 0xfb, 0xe1, 0x13, 0x6c, 0xfc, 0x32, 0x57, 0xe0, 0x44, 0xb8, 0xde, 0x81, 0xa5,
	0xc0, 0x89, 0x61, 0x62, 0x27, 0x94, 0x6a, 0xe0, 0x08, 0x78, 0xe2, 0x87, 0xa4, 0xd2, 0x94, 0x43,
	0x92, 0x78, 0x06, 0x2a, 0xa7, 0x8a, 0x97, 0x43, 0x68, 0x4c, 0x27, 0xe8, 0x4b, 0xaf, 0x15, 0xfc,
	0x2c, 0xde, 0x1e, 0x52, 0xf1, 0x26, 0xb8, 0x7d, 0x6a, 0x0e, 0xb2, 0x08, 0xe0, 0x1e, 0x2c, 0xef,
	0xd8, 0xe6, 0x48, 0xc7, 0x6b, 0x33, 0x38, 0x9a, 0xcd, 0xc8, 0x5f, 0x2a, 0x4c, 0x1e, 0x33, 0x54,
	0x42, 0xd8, 0x89, 0x67, 0x85, 0x2f, 0x15, 0x72, 0xd1, 0x4b, 0x05, 0xe5, 0x3b, 0x50, 0xeb, 0xb0,
	0x97, 0x04, 0x1c, 0x4b, 0xe2, 0xb5, 0x81, 0x94, 0x7c, 0x6d, 0x30, 0xed, 0x02, 0xe5, 0x3a, 0xe0,
	0x7d, 0x76, 0xed, 0xd4, 0xd2, 0x07, 0xe1, 0xbd, 0x50, 0x0a, 0xd8, 0xb3, 0xf4, 0x81, 0xf2, 0x73,
	0xb0, 0xdc, 0x74, 0x6c, 0x9b, 0x18, 0x01, 0xe9, 0xef, 0x39, 0xde, 0x81, 0x69, 0x50, 0x1c, 0x16,
	0x39, 0x0d, 0xf8, 0x0a, 0xf1, 0xb7, 0x7c, 0x1d, 0x16, 0x3c, 0x73, 0x30, 0x0c, 0x8f, 0x24, 0xec,
	0x43, 0x69, 0xc3, 0xca, 0x81, 0x3f, 0x38, 0x74, 0x02, 0xf3, 0xf4, 0xfc, 0xd8, 0x34, 0xa8, 0x75,
	0xf2, 0x69, 0x34, 0xed, 0x78, 0xe6, 0xc0, 0xb4, 0x39, 0x02, 0xfe, 0x25, 0xdf, 0x82, 0x72, 0x30,
	0x1c, 0x8f, 0x7a, 0xb6, 0x6e, 0x5a, 0xd1, 0xfb, 0x83, 0x10, 0xa0, 0xfc, 0x5d, 0x01, 0x40, 0xd8,
	0xe7, 0xcc, 0xe7, 0x16, 0xd3, 0x36, 0xf8, 0x00, 0xaa, 0x11, 0x51, 0x8c, 0xd3, 0x01, 0x0f, 0xad,
	0xd2, 0xf6, 0x26, 0x46, 0x4a, 0x15, 0x38, 0xd5, 0x9a, 0xa7, 0x03, 0xb9, 0x09, 0x55, 0xc3, 0xd5,
	0xf4, 0x90, 0x65, 0x3c, 0xc9, 0xb6, 0x95, 0xc2, 0x90, 0x60, 0xaa, 0x5a, 0x31, 0xdc, 0x08, 0x24,
	0xb7, 0xa0, 0x46, 0x8d, 0x9e, 0x11, 0x92, 0x93, 0x57, 0xd2, 0xd2, 0x58, 0x12, 0x04, 0x57, 0xab,
	0x23, 0xd3, 0x88, 0x60, 0x71, 0x76, 0x2d, 0xc6, 0xd9, 0x85, 0xce, 0x31, 0xbc, 0x16, 0xed, 0x71,
	0xcf, 0x50, 0x0e, 0x6f, 0x45, 0x7b, 0xf2, 0x87, 0xb0, 0x60, 0xb8, 0x99, 0x5e, 0x42, 0x14, 0x0c,
	0x77, 0x77, 0x20, 0x7f, 0x04, 0x8b, 0x23, 0xb3, 0x4f, 0x07, 0x96, 0x2f, 0x3b, 0x70, 0x61, 0x64,
	0xf6, 0x77, 0x07, 0xf2, 0x23, 0xa8, 0x8d, 0xfc, 0x81, 0xe6, 0x9a, 0x46, 0xe0, 0x51, 0xf6, 0xf3,
	0x0b, 0xf3, 0xe9, 0x08, 0x2a, 0x25, 0x28, 0x6a, 0x75, 0xe4, 0x0f, 0x8e, 0xc3, 0x71, 0xf2, 0x87,
	0x70, 0xf3, 0xd4, 0x33, 0x69, 0xd8, 0xed, 0xbb, 0xba, 0x11, 0xbb, 0xa3, 0xcb, 0x2e, 0x67, 0xae,
	0xb2, 0xe6, 0x0e, 0x6d, 0x9d, 0xac, 0x43, 0x39, 0xc3, 0x02, 0x8d, 0xa8, 0xa6, 0xd9, 0x2c, 0xc1,
	0x47, 0x9c, 0xdc, 0x82, 0x91, 0x5f, 0x9f, 0xea, 0xa8, 0x39, 0x7a, 0xe4, 0x05, 0x1a, 0xf7, 0xbf,
	0x96, 0x40, 0xee, 0x90, 0x80, 0x9a, 0x20, 0x95, 0x58, 0x7a, 0xc6, 0xaa, 0xe7, 0x94, 0xc3, 0xd8,
	0xe4, 0x5c, 0x9d, 0x17, 0x5f, 0xee, 0x08, 0xba, 0x50, 0x98, 0xf7, 0xf4, 0x68, 0x61, 0x8a, 0x31,
	0xa0, 0x06, 0x9b, 0x67, 0x92, 0xf0, 0xb7, 0xf2, 0x23, 0xbc, 0xd9, 0x92, 0x58, 0xf4, 0x8b, 0x9e,
	0x22, 0xbf, 0x80, 0x85, 0xcf, 0xaf, 0x19, 0x7c, 0x07, 0xef, 0x7f, 0xf0, 0x63, 0xc1, 0x95, 0x4c,
	0xf6, 0x45, 0x61, 0xe7, 0x7f, 0x49, 0xd0, 0x98, 0x3e, 0x45, 0xe6, 0x4a, 0xdd, 0x9c, 0x69, 0x58,
	0x8e, 0x2b, 0xf0, 0x1c, 0x74, 0x11, 0xf9, 0x30, 0xc7, 0x15, 0x78, 0x0e, 0xf5, 0x0f, 0x31, 0x51,
	0x2d, 0x64, 0x10, 0x55, 0xea, 0x7c, 0x88, 0x1d, 0x78, 0xba, 0xcd, 0x9d, 0x0f, 0xcf, 0xb0, 0x87,
	0x30, 0x1a, 0x52, 0xea, 0xb0, 0xc4, 0xf5, 0xe8, 0xc5, 0xb3, 0x1c, 0xd3, 0xe5, 0x41, 0xf9, 0x71,
	0x0e, 0x96, 0x63, 0x73, 0xbc, 0x22, 0xb9, 0x8b, 0x27, 0xa2, 0x0a, 0x17, 0x3d, 0x75, 0x5b, 0x98,
	0xff, 0xd4, 0x6d, 0x71, 0xfe, 0x29, 0xb6, 0x78, 0xd1, 0x29, 0xb6, 0x94, 0xca, 0x9f, 0x7d, 0x28,
	0xf2, 0x91, 0x5d, 0x54, 0x9d, 0xf7, 0xd0, 0x26, 0xe2, 0x62, 0xfc, 0xb1, 0x1b, 0x64, 0x7f, 0xec,
	0xa6, 0xfc, 0xa9, 0x04, 0xf0, 0x90, 0x89, 0xef, 0x97, 0xca, 0x54, 0x29, 0x1f, 0x41, 0x25, 0x5a,
	0x57, 0xb6, 0x1c, 0xeb, 0x3f, 0x48, 0xe1, 0x7d, 0x72, 0x7c, 0x60, 0xa3, 0x12, 0x5f, 0x5e, 0x83,
	0x92, 0x47, 0x7c, 0x31, 0x07, 0x51, 0xf4, 0x88, 0x1f, 0x1e, 0xdb, 0x86, 0xb4, 0x1b, 0xc5, 0x1c,
	0xe6, 0x90, 0x87, 0xe1, 0x38, 0x05, 0x6a, 0x51, 0xa3, 0x36, 0x79, 0x33, 0x5a, 0x09, 0x3b, 0x1c,
	0xf4, 0x3f, 0x50, 0x4e, 0xa0, 0xa8, 0x72, 0x5c, 0x6b, 0xb0, 0xda, 0x3a, 0x3c, 0x39, 0xd0, 0xd4,
	0x56, 0x47, 0xeb, 0x7e, 0x7e, 0xdc, 0xd2, 0x4e, 0x0e, 0x3f, 0x39, 0x3c, 0xfa, 0xec, 0xb0, 0xfe,
	0x86, 0xbc, 0x0a, 0x2b, 0xf1, 0xa6, 0x4f, 0x77, 0x8e, 0xeb, 0x92, 0xdc, 0x80, 0xeb, 0x71, 0xf0,
	0xfe, 0x51, 0xb7, 0xdb, 0x6e, 0xd5, 0x73, 0xca, 0xbf, 0xc7, 0x77, 0x41, 0x63, 0x92, 0x35, 0x60,
	0x2b, 0x9b, 0xc4, 0x40, 0x45, 0xfc, 0x9e, 0x11, 0x04, 0x85, 0x0f, 0x62, 0xf2, 0xc2, 0x83, 0x98,
	0xfb, 0xe2, 0x6e, 0x0b, 0x17, 0xbf, 0x4e, 0x52, 0x89, 0x2f, 0x90, 0x63, 0x13, 0x2a, 0x3d, 0xc7,
	0x1e, 0xfb, 0x9a, 0x47, 0xdd, 0x44, 0x98, 0x28, 0x46, 0x90, 0x4a, 0x21, 0xf2, 0xbb, 0xb0, 0xd4,
	0x37, 0x7d, 0x54, 0xaa, 0x58, 0xc6, 0xa0, 0x16, 0x42, 0x31, 0x67, 0xa0, 0xfc, 0x4f, 0x0e, 0x96,
	0x85, 0x49, 0xc4, 0x17, 0x53, 0x52, 0x86, 0x17, 0x53, 0x77, 0xf0, 0x5a, 0xd3, 0xd8, 0x27, 0x1e,
	0xcf, 0xd2, 0xa4, 0x5e, 0x98, 0x2d, 0x1a, 0x2e, 0x3e, 0x9a, 0x5a, 0x87, 0x32, 0x39, 0x73, 0x4d,
	0x8f, 0x68, 0x81, 0xcf, 0x2b, 0x12, 0x25, 0x06, 0xe8, 0xfa, 0x5f, 0xe6, 0xc7, 0x89, 0xc9, 0x77,
	0x68, 0xe5, 0x8c, 0xef, 0xd0, 0x94, 0x6f, 0xc3, 0x5b, 0x51, 0x11, 0x93, 0xd3, 0x7a, 0xec, 0x87,
	0xf6, 0x7c, 0x4c, 0xfc, 0xe0, 0xea, 0x1a, 0xaf, 0xfc, 0x93, 0x04, 0x9b, 0x33, 0x91, 0xfb, 0xae,
	0x63, 0xfb, 0x24, 0x8b, 0x31, 0xff, 0x06, 0x14, 0x84, 0x88, 0x6b, 0x6b, 0x9e, 0x04, 0xe0, 0x3e,
	0xb1, 0x37, 0x35, 0xdd, 0x86, 0xab, 0x31, 0x99, 0x36, 0xa2, 0x8b, 0x2c, 0x60, 0xb8, 0x4c, 0x54,
	0xec, 0x4b, 0xa5, 0xa4, 0x53, 0x54, 0x6a, 0xd9, 0x81, 0x77, 0xce, 0xb3, 0xa6, 0x2f, 0x48, 0xa5,
	0xef, 0xa7, 0xa8, 0x24, 0x20, 0xcf, 0x4e, 0x25, 0x6a, 0xae, 0x74, 0x5f, 0xa3, 0x7e, 0xfa, 0x9c,
	0xdf, 0xf4, 0x2b, 0x0d, 0x75, 0x1f, 0x71, 0x52, 0x12, 0x0a, 0xf9, 0xc6, 0x4b, 0x90, 0x90, 0xf6,
	0x56, 0x74, 0x8c, 0x63, 0xc2, 0xd4, 0xa9, 0xd7, 0xbf, 0xca, 0xbe, 0x2f, 0x28, 0xa6, 0xdb, 0xb0,
	0xcc, 0xee, 0x14, 0x7c, 0x31, 0xfa, 0x4e, 0x89, 0xbe, 0x36, 0x65, 0x4f, 0x2f, 0x5f, 0x28, 0x13,
	0xbb, 0xe2, 0x42, 0x99, 0x14, 0xb9, 0x7c, 0x5a, 0xe4, 0x9e, 0xc3, 0x06, 0x5b, 0xdd, 0x89, 0x4b,
	0xf5, 0x95, 0x44, 0x87, 0xc6, 0xe3, 0xb1, 0x3f, 0x3c, 0xf0, 0x07, 0xf2, 0xdb, 0x50, 0x8b, 0x4e,
	0x9f, 0x42, 0x7a, 0xa0, 0x1a, 0x01, 0xa7, 0xe6, 0x07, 0xa8, 0xbd, 0x21, 0x67, 0x01, 0x3d, 0x34,
	0xfd, 0xaa, 0x1f, 0xd9, 0x7e, 0x60, 0xa0, 0x6f, 0xfa, 0x8e, 0xad, 0xfc, 0x9f, 0x04, 0x2b, 0x2d,
	0x6a, 0xfa, 0x4c, 0x7b, 0x30, 0xe1, 0xc6, 0x1c, 0xd7, 0x72, 0x79, 0x2b, 0xbb, 0x01, 0xc0, 0x91,
	0x4c, 0xdc, 0x0e, 0x73, 0x37, 0x6d, 0xfe, 0x18, 0x93, 0x35, 0x0b, 0x6f, 0x07, 0x59, 0x73, 0x58,
	0x43, 0x24, 0x67, 0x2e, 0x35, 0xd0, 0x0b, 0xac, 0x86, 0x48, 0xce, 0xdc, 0x2e, 0xba, 0x6e, 0xd7,
	0x71, 0xc7, 0x2e, 0x5d, 0x19, 0x3b, 0xfb, 0x16, 0xf1, 0xbb, 0x8d, 0xff, 0x07, 0x31, 0x34, 0x07,
	0x43, 0xcb, 0x1c, 0x0c, 0x03, 0x2d, 0x20, 0x67, 0x01, 0x37, 0xaf, 0xb5, 0x08, 0xda, 0x25, 0x67,
	0x81, 0xf2, 0x09, 0x56, 0x08, 0xc2, 0x3d, 0x0b, 0x82, 0x75, 0x05, 0x33, 0xa8, 0xfc, 0xb7, 0x04,
	0xca, 0x3c, 0x6c, 0xd9, 0x45, 0x6c, 0x27, 0x24, 0xcb, 0xdc, 0x7b, 0x5e, 0x29, 0x96, 0x71, 0xd2,
	0x61, 0x18, 0xb8, 0x09, 0x15, 0x46, 0xa3, 0xc0, 0x0c, 0xac, 0xa8, 0x4a, 0x82, 0xa0, 0x2e, 0x85,
	0xc8, 0xdb, 0xb0, 0xc2, 0x3a, 0x9c, 0x3a, 0xde, 0x48, 0xe7, 0xc4, 0x62, 0x1c, 0x58, 0xc6, 0x86,
	0x3d, 0x84, 0x53, 0x72, 0x6d, 0x7f, 0x5f, 0x0a, 0xff, 0x03, 0x00, 0x5d, 0xd0, 0x2d, 0x68, 0x60,
	0x84, 0xb2, 0xd7, 0xda, 0xdf, 0x3f, 0xfa, 0x2c, 0x19, 0xd6, 0x84, 0xf1, 0x8b, 0xd8, 0xba, 0xab,
	0x1e, 0xd5, 0xa5, 0xa9, 0xe3, 0xf6, 0x77, 0x1e, 0x7e, 0x4e, 0x5b, 0x73, 0xf2, 0x06, 0xac, 0xa5,
	0x5a, 0xdb, 0x87, 0xdd, 0xf6, 0xc1, 0x4e, 0xb7, 0x55, 0xcf, 0x4f, 0x1d, 0x7c, 0xbc, 0xa3, 0x76,
	0x0f, 0x5b, 0x6a, 0xbd, 0xb0, 0xfd, 0x2c, 0x8c, 0x8c, 0x76, 0x27, 0x8f, 0xf8, 0x37, 0xc4, 0xfe,
	0xbb, 0xed, 0xc3, 0x87, 0xc9, 0x95, 0x6e, 0xc1, 0xad, 0x59, 0x5d, 0xda, 0xbf, 0x78, 0xd2, 0xaa,
	0x4b, 0xf2, 0x26, 0xac, 0x4f, 0xef, 0x71, 0x70, 0xb2, 0xdf, 0x6d, 0xd7, 0x73, 0xdb, 0x9f, 0x47,
	0xff, 0x49, 0x32, 0x79, 0x57, 0xaf, 0xc0, 0x5b, 0xa9, 0x41, 0x9d, 0xee, 0x4e, 0xf7, 0xa4, 0xa3,
	0x1d, 0x1e, 0xa9, 0x07, 0x3b, 0xfb, 0xf5, 0x37, 0xa6, 0xae, 0x8e, 0xf7, 0xd9, 0xa1, 0xbb, 0xaa,
	0x4b, 0xdb, 0x8f, 0xa1, 0xc8, 0x5f, 0xdf, 0xcb, 0xd7, 0x60, 0x79, 0x77, 0xe7, 0x90, 0xcd, 0x1c,
	0xa1, 0xa8, 0x43, 0x35, 0x02, 0x76, 0x0f, 0x68, 0xe4, 0x78, 0x03, 0xe4, 0x08, 0x72, 0xdc, 0x52,
	0x0f, 0x76, 0x0e, 0x5b, 0x87, 0xdd, 0x7a, 0x6e, 0xfb, 0xa7, 0x00, 0x3a, 0xcf, 0xcd, 0xc0, 0x18,
	0xb6, 0xec, 0xf1, 0x48, 0x5e, 0x02, 0xe8, 0x7c, 0xd6, 0xee, 0x36, 0x1f, 0x6b, 0x47, 0x7b, 0x7b,
	0xf5, 0x37, 0xe4, 0x1a, 0x94, 0xc3, 0xef, 0xc3, 0xba, 0xb4, 0xfd, 0xaf, 0x12, 0x54, 0xdb, 0xe2,
	0x63, 0xee, 0x90, 0x4a, 0xed, 0xc3, 0x4f, 0xdb, 0xdd, 0xd6, 0x64, 0x27, 0x1c, 0x50, 0x7f, 0x43,
	0x7e, 0x0b, 0xde, 0x9c, 0xd2, 0x83, 0x7d, 0x3d, 0xac, 0x4b, 0x33, 0xda, 0x3b, 0x27, 0xcd, 0x66,
	0xab, 0xd3, 0x11, 0x38, 0x1f, 0x6f, 0xdf, 0xdb, 0x69, 0xef, 0xb7, 0x1e, 0xd6, 0xf3, 0x11, 0x13,
	0xe2, 0xcd, 0xcd, 0x9d, 0xc3, 0x66, 0x8b, 0x76, 0x28, 0xcc, 0xc0, 0xdf, 0x6d, 0x1f, 0xb4, 0x8e,
	0x4e, 0xba, 0xf5, 0x85, 0xed, 0xf3, 0x90, 0x49, 0x9d, 0xc9, 0x1f, 0xbe, 0xbc, 0x0d, 0x9b, 0x22,
	0x03, 0x3a, 0x47, 0x27, 0x6a, 0xb3, 0x95, 0x14, 0x90, 0x04, 0xfb, 0xc5, 0x4e, 0xc7, 0xed, 0x66,
	0x5a, 0x3e, 0xc4, 0x0e, 0x34, 0x98, 0xcf, 0xed, 0xb6, 0xa0, 0x61, 0x38, 0xa3, 0xbb, 0xe7, 0xe6,
	0xb9, 0x33, 0xa6, 0x0a, 0x3c, 0x72, 0xfa, 0xc4, 0x62, 0x7f, 0x8d, 0xf3, 0xed, 0xaf, 0x0d, 0x1c,
	0x4b, 0xb7, 0x07, 0x77, 0x3f, 0xb8, 0x17, 0x04, 0x77, 0x0d, 0x67, 0xf4, 0x1e, 0x82, 0x0d, 0xc7,
	0x7a, 0x4f, 0x77, 0xdd, 0xd8, 0x9f, 0xed, 0xf4, 0x16, 0xb1, 0xe9, 0xfd, 0xff, 0x0f, 0x00, 0x00,
	0xff, 0xff, 0x90, 0x13, 0x34, 0x14, 0x90, 0x47, 0x00, 0x00,
}
