// Code generated by protoc-gen-gogo.
// source: contact.proto
// DO NOT EDIT!

/*
	Package contact is a generated protocol buffer package.

	It is generated from these files:
		contact.proto

	It has these top-level messages:
		AddFriendReq
		AddFriendResp
		VerifyFriendReq
		VerifyFriendResp
		DeleteFriendReq
		DeleteFriendResp
		RemarkFriendReq
		RemarkFriendResp
		BanFriendReq
		BanFriendResp
		MarkFriendWithStarReq
		MarkFriendWithStarResp
		GetUserDetailReq
		UsersTGroup
		GetUserDetailResp
		AliasExtendInfo
		GetCoverReq
		GetCoverResp
		GetUserStatusReq
		GetUserStatusResp
		CheckUserGrantReq
		FindFriendGrant
		CheckUserGrantResp
		SetFindFriendSwitchReq
		SetFindFriendSwitchResp
		GetUserOnlineTerminalListReq
		UserOnlineTerminal
		GetUserOnlineTerminalListResp
		UserKickTerminalReq
		UserKickTerminalResp
		UserTerminalOnlineStatus
		CheckRelatedLoginAccountReq
		CheckRelatedLoginAccountResp
		GetRelatedLoginAccountReq
		RelatedLoginAccount
		GetRelatedLoginAccountResp
*/
package contact

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 好友来源
type FRIEND_SRC_TYPE int32

const (
	FRIEND_SRC_TYPE_FRIEND_SRC_DEFAULT                  FRIEND_SRC_TYPE = 0
	FRIEND_SRC_TYPE_FRIEND_SRC_GUILD_AUTO_ADD           FRIEND_SRC_TYPE = 1
	FRIEND_SRC_TYPE_FRIEND_SRC_INVITE_CODE              FRIEND_SRC_TYPE = 2
	FRIEND_SRC_TYPE_FRIEND_SRC_CONTACT                  FRIEND_SRC_TYPE = 3
	FRIEND_SRC_TYPE_FRIEND_SRC_GAME_INTERNAL            FRIEND_SRC_TYPE = 4
	FRIEND_SRC_TYPE_FRIEND_SRC_WEIXIN_SHARE             FRIEND_SRC_TYPE = 5
	FRIEND_SRC_TYPE_FRIEND_SRC_QQ_SHARE                 FRIEND_SRC_TYPE = 6
	FRIEND_SRC_TYPE_FRIEND_SRC_USER_RECOMMEND           FRIEND_SRC_TYPE = 7
	FRIEND_SRC_TYPE_FRIEND_SRC_FIND_FRIEND              FRIEND_SRC_TYPE = 8
	FRIEND_SRC_TYPE_FRIEND_SRC_FAST_MATHING             FRIEND_SRC_TYPE = 9
	FRIEND_SRC_TYPE_FRIEND_SRC_GROUP_INTERNAL           FRIEND_SRC_TYPE = 10
	FRIEND_SRC_TYPE_FRIEND_SRC_GUILD_MEMBER_LIST        FRIEND_SRC_TYPE = 11
	FRIEND_SRC_TYPE_FRIEND_SRC_CIRCLE_CREATE_TOPIC_USER FRIEND_SRC_TYPE = 12
	// channel
	FRIEND_SRC_TYPE_FRIEND_SRC_USER_KH_CHANNEL_TYPE FRIEND_SRC_TYPE = 13
	FRIEND_SRC_TYPE_FRIEND_SRC_HIGH_KH_CHANNEL_TYPE FRIEND_SRC_TYPE = 14
	FRIEND_SRC_TYPE_FRIEND_SRC_FUN_CHANNEL_TYPE     FRIEND_SRC_TYPE = 15
	// rank
	FRIEND_SRC_TYPE_FRIEND_SRC_RANK_LIST       FRIEND_SRC_TYPE = 16
	FRIEND_SRC_TYPE_FRIEND_SRC_ICEBREAK_FRIEND FRIEND_SRC_TYPE = 17
	FRIEND_SRC_TYPE_FRIEND_SRC_BIFOLLOW        FRIEND_SRC_TYPE = 18
)

var FRIEND_SRC_TYPE_name = map[int32]string{
	0:  "FRIEND_SRC_DEFAULT",
	1:  "FRIEND_SRC_GUILD_AUTO_ADD",
	2:  "FRIEND_SRC_INVITE_CODE",
	3:  "FRIEND_SRC_CONTACT",
	4:  "FRIEND_SRC_GAME_INTERNAL",
	5:  "FRIEND_SRC_WEIXIN_SHARE",
	6:  "FRIEND_SRC_QQ_SHARE",
	7:  "FRIEND_SRC_USER_RECOMMEND",
	8:  "FRIEND_SRC_FIND_FRIEND",
	9:  "FRIEND_SRC_FAST_MATHING",
	10: "FRIEND_SRC_GROUP_INTERNAL",
	11: "FRIEND_SRC_GUILD_MEMBER_LIST",
	12: "FRIEND_SRC_CIRCLE_CREATE_TOPIC_USER",
	13: "FRIEND_SRC_USER_KH_CHANNEL_TYPE",
	14: "FRIEND_SRC_HIGH_KH_CHANNEL_TYPE",
	15: "FRIEND_SRC_FUN_CHANNEL_TYPE",
	16: "FRIEND_SRC_RANK_LIST",
	17: "FRIEND_SRC_ICEBREAK_FRIEND",
	18: "FRIEND_SRC_BIFOLLOW",
}
var FRIEND_SRC_TYPE_value = map[string]int32{
	"FRIEND_SRC_DEFAULT":                  0,
	"FRIEND_SRC_GUILD_AUTO_ADD":           1,
	"FRIEND_SRC_INVITE_CODE":              2,
	"FRIEND_SRC_CONTACT":                  3,
	"FRIEND_SRC_GAME_INTERNAL":            4,
	"FRIEND_SRC_WEIXIN_SHARE":             5,
	"FRIEND_SRC_QQ_SHARE":                 6,
	"FRIEND_SRC_USER_RECOMMEND":           7,
	"FRIEND_SRC_FIND_FRIEND":              8,
	"FRIEND_SRC_FAST_MATHING":             9,
	"FRIEND_SRC_GROUP_INTERNAL":           10,
	"FRIEND_SRC_GUILD_MEMBER_LIST":        11,
	"FRIEND_SRC_CIRCLE_CREATE_TOPIC_USER": 12,
	"FRIEND_SRC_USER_KH_CHANNEL_TYPE":     13,
	"FRIEND_SRC_HIGH_KH_CHANNEL_TYPE":     14,
	"FRIEND_SRC_FUN_CHANNEL_TYPE":         15,
	"FRIEND_SRC_RANK_LIST":                16,
	"FRIEND_SRC_ICEBREAK_FRIEND":          17,
	"FRIEND_SRC_BIFOLLOW":                 18,
}

func (x FRIEND_SRC_TYPE) Enum() *FRIEND_SRC_TYPE {
	p := new(FRIEND_SRC_TYPE)
	*p = x
	return p
}
func (x FRIEND_SRC_TYPE) String() string {
	return proto.EnumName(FRIEND_SRC_TYPE_name, int32(x))
}
func (x *FRIEND_SRC_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FRIEND_SRC_TYPE_value, data, "FRIEND_SRC_TYPE")
	if err != nil {
		return err
	}
	*x = FRIEND_SRC_TYPE(value)
	return nil
}
func (FRIEND_SRC_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorContact, []int{0} }

type AliasSignType int32

const (
	AliasSignType_ENUM_ALIAS_TYPE_UNKNOW_TYPE     AliasSignType = 0
	AliasSignType_ENUM_ALIAS_TYPE_DEFAULT_SIGN    AliasSignType = 1
	AliasSignType_ENUM_ALIAS_TYPE_DEFAULT_NO_SIGN AliasSignType = 2
)

var AliasSignType_name = map[int32]string{
	0: "ENUM_ALIAS_TYPE_UNKNOW_TYPE",
	1: "ENUM_ALIAS_TYPE_DEFAULT_SIGN",
	2: "ENUM_ALIAS_TYPE_DEFAULT_NO_SIGN",
}
var AliasSignType_value = map[string]int32{
	"ENUM_ALIAS_TYPE_UNKNOW_TYPE":     0,
	"ENUM_ALIAS_TYPE_DEFAULT_SIGN":    1,
	"ENUM_ALIAS_TYPE_DEFAULT_NO_SIGN": 2,
}

func (x AliasSignType) Enum() *AliasSignType {
	p := new(AliasSignType)
	*p = x
	return p
}
func (x AliasSignType) String() string {
	return proto.EnumName(AliasSignType_name, int32(x))
}
func (x *AliasSignType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(AliasSignType_value, data, "AliasSignType")
	if err != nil {
		return err
	}
	*x = AliasSignType(value)
	return nil
}
func (AliasSignType) EnumDescriptor() ([]byte, []int) { return fileDescriptorContact, []int{1} }

type EOSType int32

const (
	EOSType_ENUM_OS_UNKNOWN  EOSType = 0
	EOSType_ENUM_OS_ANDROID  EOSType = 1
	EOSType_ENUM_OS_IOS      EOSType = 2
	EOSType_ENUM_OS_WINPHONE EOSType = 3
	EOSType_ENUM_OS_MACOSX   EOSType = 4
	EOSType_ENUM_OS_WINDOWS  EOSType = 5
	EOSType_ENUM_OS_LINUX    EOSType = 6
	EOSType_ENUM_OS_MAX      EOSType = 15
)

var EOSType_name = map[int32]string{
	0:  "ENUM_OS_UNKNOWN",
	1:  "ENUM_OS_ANDROID",
	2:  "ENUM_OS_IOS",
	3:  "ENUM_OS_WINPHONE",
	4:  "ENUM_OS_MACOSX",
	5:  "ENUM_OS_WINDOWS",
	6:  "ENUM_OS_LINUX",
	15: "ENUM_OS_MAX",
}
var EOSType_value = map[string]int32{
	"ENUM_OS_UNKNOWN":  0,
	"ENUM_OS_ANDROID":  1,
	"ENUM_OS_IOS":      2,
	"ENUM_OS_WINPHONE": 3,
	"ENUM_OS_MACOSX":   4,
	"ENUM_OS_WINDOWS":  5,
	"ENUM_OS_LINUX":    6,
	"ENUM_OS_MAX":      15,
}

func (x EOSType) Enum() *EOSType {
	p := new(EOSType)
	*p = x
	return p
}
func (x EOSType) String() string {
	return proto.EnumName(EOSType_name, int32(x))
}
func (x *EOSType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EOSType_value, data, "EOSType")
	if err != nil {
		return err
	}
	*x = EOSType(value)
	return nil
}
func (EOSType) EnumDescriptor() ([]byte, []int) { return fileDescriptorContact, []int{2} }

type EPlatformType int32

const (
	EPlatformType_ENUM_PLATFORM_UNKNOWN EPlatformType = 0
	EPlatformType_ENUM_PLATFORM_MOBILE  EPlatformType = 1
	EPlatformType_ENUM_PLATFORM_WEB     EPlatformType = 2
	EPlatformType_ENUM_PLATFORM_PC      EPlatformType = 3
	EPlatformType_ENUM_PLATFORM_PAD     EPlatformType = 4
	// 2020.06.11
	EPlatformType_ENUM_PLATFORM_QQ_EMBEDED     EPlatformType = 5
	EPlatformType_ENUM_PLATFORM_WECHAT_EMBEDED EPlatformType = 6
	EPlatformType_ENUM_PLATFORM_MAX            EPlatformType = 15
)

var EPlatformType_name = map[int32]string{
	0:  "ENUM_PLATFORM_UNKNOWN",
	1:  "ENUM_PLATFORM_MOBILE",
	2:  "ENUM_PLATFORM_WEB",
	3:  "ENUM_PLATFORM_PC",
	4:  "ENUM_PLATFORM_PAD",
	5:  "ENUM_PLATFORM_QQ_EMBEDED",
	6:  "ENUM_PLATFORM_WECHAT_EMBEDED",
	15: "ENUM_PLATFORM_MAX",
}
var EPlatformType_value = map[string]int32{
	"ENUM_PLATFORM_UNKNOWN":        0,
	"ENUM_PLATFORM_MOBILE":         1,
	"ENUM_PLATFORM_WEB":            2,
	"ENUM_PLATFORM_PC":             3,
	"ENUM_PLATFORM_PAD":            4,
	"ENUM_PLATFORM_QQ_EMBEDED":     5,
	"ENUM_PLATFORM_WECHAT_EMBEDED": 6,
	"ENUM_PLATFORM_MAX":            15,
}

func (x EPlatformType) Enum() *EPlatformType {
	p := new(EPlatformType)
	*p = x
	return p
}
func (x EPlatformType) String() string {
	return proto.EnumName(EPlatformType_name, int32(x))
}
func (x *EPlatformType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EPlatformType_value, data, "EPlatformType")
	if err != nil {
		return err
	}
	*x = EPlatformType(value)
	return nil
}
func (EPlatformType) EnumDescriptor() ([]byte, []int) { return fileDescriptorContact, []int{3} }

type EAppID int32

const (
	EAppID_ENUM_APPID_TT_NOMAL   EAppID = 0
	EAppID_ENUM_APPID_TT_ANDROID EAppID = 1
	EAppID_ENUM_APPID_TT_IPHONE  EAppID = 2
	// TT插件应用的APPID从11开始
	EAppID_ENUM_APPID_HAPPYCITY        EAppID = 11
	EAppID_ENUM_APPID_TT_GAME_SDK      EAppID = 12
	EAppID_ENUM_APPID_TT_WIN_ASSISTANT EAppID = 13
	EAppID_ENUM_APPID_H5_WEBVIEW       EAppID = 14
	// 2020.06.11
	EAppID_ENUM_APPID_TT_PC             EAppID = 15
	EAppID_ENUM_APPID_TT_QQ_EMBEDED     EAppID = 16
	EAppID_ENUM_APPID_TT_WECHAT_EMBEDED EAppID = 17
	// 兼容马甲包同时在线
	EAppID_ENUM_APPID_HUANYOU   EAppID = 22
	EAppID_ENUM_APPID_ZAIYA     EAppID = 23
	EAppID_ENUM_APPID_TOP_SPEED EAppID = 24
	EAppID_ENUM_APPID_MAIKE     EAppID = 25
	EAppID_ENUM_APPID_MIJING    EAppID = 26
	EAppID_ENUM_APPID_ALL       EAppID = 65534
	EAppID_ENUM_APPID_MAX       EAppID = 65535
)

var EAppID_name = map[int32]string{
	0:     "ENUM_APPID_TT_NOMAL",
	1:     "ENUM_APPID_TT_ANDROID",
	2:     "ENUM_APPID_TT_IPHONE",
	11:    "ENUM_APPID_HAPPYCITY",
	12:    "ENUM_APPID_TT_GAME_SDK",
	13:    "ENUM_APPID_TT_WIN_ASSISTANT",
	14:    "ENUM_APPID_H5_WEBVIEW",
	15:    "ENUM_APPID_TT_PC",
	16:    "ENUM_APPID_TT_QQ_EMBEDED",
	17:    "ENUM_APPID_TT_WECHAT_EMBEDED",
	22:    "ENUM_APPID_HUANYOU",
	23:    "ENUM_APPID_ZAIYA",
	24:    "ENUM_APPID_TOP_SPEED",
	25:    "ENUM_APPID_MAIKE",
	26:    "ENUM_APPID_MIJING",
	65534: "ENUM_APPID_ALL",
	65535: "ENUM_APPID_MAX",
}
var EAppID_value = map[string]int32{
	"ENUM_APPID_TT_NOMAL":          0,
	"ENUM_APPID_TT_ANDROID":        1,
	"ENUM_APPID_TT_IPHONE":         2,
	"ENUM_APPID_HAPPYCITY":         11,
	"ENUM_APPID_TT_GAME_SDK":       12,
	"ENUM_APPID_TT_WIN_ASSISTANT":  13,
	"ENUM_APPID_H5_WEBVIEW":        14,
	"ENUM_APPID_TT_PC":             15,
	"ENUM_APPID_TT_QQ_EMBEDED":     16,
	"ENUM_APPID_TT_WECHAT_EMBEDED": 17,
	"ENUM_APPID_HUANYOU":           22,
	"ENUM_APPID_ZAIYA":             23,
	"ENUM_APPID_TOP_SPEED":         24,
	"ENUM_APPID_MAIKE":             25,
	"ENUM_APPID_MIJING":            26,
	"ENUM_APPID_ALL":               65534,
	"ENUM_APPID_MAX":               65535,
}

func (x EAppID) Enum() *EAppID {
	p := new(EAppID)
	*p = x
	return p
}
func (x EAppID) String() string {
	return proto.EnumName(EAppID_name, int32(x))
}
func (x *EAppID) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(EAppID_value, data, "EAppID")
	if err != nil {
		return err
	}
	*x = EAppID(value)
	return nil
}
func (EAppID) EnumDescriptor() ([]byte, []int) { return fileDescriptorContact, []int{4} }

// 添加好友
type AddFriendReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetAccount string      `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
	Verify        string      `protobuf:"bytes,3,req,name=verify" json:"verify"`
	FriendSrcType uint32      `protobuf:"varint,4,opt,name=friend_src_type,json=friendSrcType" json:"friend_src_type"`
}

func (m *AddFriendReq) Reset()                    { *m = AddFriendReq{} }
func (m *AddFriendReq) String() string            { return proto.CompactTextString(m) }
func (*AddFriendReq) ProtoMessage()               {}
func (*AddFriendReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{0} }

func (m *AddFriendReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AddFriendReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *AddFriendReq) GetVerify() string {
	if m != nil {
		return m.Verify
	}
	return ""
}

func (m *AddFriendReq) GetFriendSrcType() uint32 {
	if m != nil {
		return m.FriendSrcType
	}
	return 0
}

type AddFriendResp struct {
	BaseResp      *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TargetAccount string       `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
}

func (m *AddFriendResp) Reset()                    { *m = AddFriendResp{} }
func (m *AddFriendResp) String() string            { return proto.CompactTextString(m) }
func (*AddFriendResp) ProtoMessage()               {}
func (*AddFriendResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{1} }

func (m *AddFriendResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *AddFriendResp) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

// 用户验证是否通过
type VerifyFriendReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Account  string      `protobuf:"bytes,2,req,name=account" json:"account"`
	IsVerify bool        `protobuf:"varint,3,req,name=is_verify,json=isVerify" json:"is_verify"`
	Reason   string      `protobuf:"bytes,4,req,name=reason" json:"reason"`
}

func (m *VerifyFriendReq) Reset()                    { *m = VerifyFriendReq{} }
func (m *VerifyFriendReq) String() string            { return proto.CompactTextString(m) }
func (*VerifyFriendReq) ProtoMessage()               {}
func (*VerifyFriendReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{2} }

func (m *VerifyFriendReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *VerifyFriendReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *VerifyFriendReq) GetIsVerify() bool {
	if m != nil {
		return m.IsVerify
	}
	return false
}

func (m *VerifyFriendReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type VerifyFriendResp struct {
	BaseResp      *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TargetAccount string       `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
}

func (m *VerifyFriendResp) Reset()                    { *m = VerifyFriendResp{} }
func (m *VerifyFriendResp) String() string            { return proto.CompactTextString(m) }
func (*VerifyFriendResp) ProtoMessage()               {}
func (*VerifyFriendResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{3} }

func (m *VerifyFriendResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *VerifyFriendResp) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

// 删除好友
type DeleteFriendReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetAccount string      `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
}

func (m *DeleteFriendReq) Reset()                    { *m = DeleteFriendReq{} }
func (m *DeleteFriendReq) String() string            { return proto.CompactTextString(m) }
func (*DeleteFriendReq) ProtoMessage()               {}
func (*DeleteFriendReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{4} }

func (m *DeleteFriendReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DeleteFriendReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

// 删除好友响应
type DeleteFriendResp struct {
	BaseResp      *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TargetAccount string       `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
}

func (m *DeleteFriendResp) Reset()                    { *m = DeleteFriendResp{} }
func (m *DeleteFriendResp) String() string            { return proto.CompactTextString(m) }
func (*DeleteFriendResp) ProtoMessage()               {}
func (*DeleteFriendResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{5} }

func (m *DeleteFriendResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *DeleteFriendResp) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

// 备注好友
type RemarkFriendReq struct {
	BaseReq     *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	UserAccount string      `protobuf:"bytes,2,req,name=user_account,json=userAccount" json:"user_account"`
	Remark      string      `protobuf:"bytes,3,req,name=remark" json:"remark"`
	IsClean     bool        `protobuf:"varint,4,opt,name=is_clean,json=isClean" json:"is_clean"`
}

func (m *RemarkFriendReq) Reset()                    { *m = RemarkFriendReq{} }
func (m *RemarkFriendReq) String() string            { return proto.CompactTextString(m) }
func (*RemarkFriendReq) ProtoMessage()               {}
func (*RemarkFriendReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{6} }

func (m *RemarkFriendReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RemarkFriendReq) GetUserAccount() string {
	if m != nil {
		return m.UserAccount
	}
	return ""
}

func (m *RemarkFriendReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *RemarkFriendReq) GetIsClean() bool {
	if m != nil {
		return m.IsClean
	}
	return false
}

type RemarkFriendResp struct {
	BaseResp    *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	UserAccount string       `protobuf:"bytes,2,req,name=user_account,json=userAccount" json:"user_account"`
}

func (m *RemarkFriendResp) Reset()                    { *m = RemarkFriendResp{} }
func (m *RemarkFriendResp) String() string            { return proto.CompactTextString(m) }
func (*RemarkFriendResp) ProtoMessage()               {}
func (*RemarkFriendResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{7} }

func (m *RemarkFriendResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *RemarkFriendResp) GetUserAccount() string {
	if m != nil {
		return m.UserAccount
	}
	return ""
}

// 拉黑
type BanFriendReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetAccount string      `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
	IsBaned       bool        `protobuf:"varint,3,req,name=is_baned,json=isBaned" json:"is_baned"`
}

func (m *BanFriendReq) Reset()                    { *m = BanFriendReq{} }
func (m *BanFriendReq) String() string            { return proto.CompactTextString(m) }
func (*BanFriendReq) ProtoMessage()               {}
func (*BanFriendReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{8} }

func (m *BanFriendReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BanFriendReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *BanFriendReq) GetIsBaned() bool {
	if m != nil {
		return m.IsBaned
	}
	return false
}

type BanFriendResp struct {
	BaseResp      *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TargetAccount string       `protobuf:"bytes,2,opt,name=target_account,json=targetAccount" json:"target_account"`
	IsBaned       bool         `protobuf:"varint,3,opt,name=is_baned,json=isBaned" json:"is_baned"`
}

func (m *BanFriendResp) Reset()                    { *m = BanFriendResp{} }
func (m *BanFriendResp) String() string            { return proto.CompactTextString(m) }
func (*BanFriendResp) ProtoMessage()               {}
func (*BanFriendResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{9} }

func (m *BanFriendResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BanFriendResp) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *BanFriendResp) GetIsBaned() bool {
	if m != nil {
		return m.IsBaned
	}
	return false
}

// 星标好友
type MarkFriendWithStarReq struct {
	BaseReq     *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	UserAccount string      `protobuf:"bytes,2,req,name=user_account,json=userAccount" json:"user_account"`
	IsStar      bool        `protobuf:"varint,3,req,name=is_star,json=isStar" json:"is_star"`
}

func (m *MarkFriendWithStarReq) Reset()                    { *m = MarkFriendWithStarReq{} }
func (m *MarkFriendWithStarReq) String() string            { return proto.CompactTextString(m) }
func (*MarkFriendWithStarReq) ProtoMessage()               {}
func (*MarkFriendWithStarReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{10} }

func (m *MarkFriendWithStarReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MarkFriendWithStarReq) GetUserAccount() string {
	if m != nil {
		return m.UserAccount
	}
	return ""
}

func (m *MarkFriendWithStarReq) GetIsStar() bool {
	if m != nil {
		return m.IsStar
	}
	return false
}

type MarkFriendWithStarResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *MarkFriendWithStarResp) Reset()                    { *m = MarkFriendWithStarResp{} }
func (m *MarkFriendWithStarResp) String() string            { return proto.CompactTextString(m) }
func (*MarkFriendWithStarResp) ProtoMessage()               {}
func (*MarkFriendWithStarResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{11} }

func (m *MarkFriendWithStarResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetUserDetailReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetAccount string      `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
	Cid           uint32      `protobuf:"varint,3,opt,name=cid" json:"cid"`
}

func (m *GetUserDetailReq) Reset()                    { *m = GetUserDetailReq{} }
func (m *GetUserDetailReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserDetailReq) ProtoMessage()               {}
func (*GetUserDetailReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{12} }

func (m *GetUserDetailReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserDetailReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *GetUserDetailReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

// 用户兴趣群组
type UsersTGroup struct {
	GroupId     uint32 `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	GroupName   string `protobuf:"bytes,2,req,name=group_name,json=groupName" json:"group_name"`
	PortraitMd5 string `protobuf:"bytes,3,req,name=portrait_md5,json=portraitMd5" json:"portrait_md5"`
	GameName    string `protobuf:"bytes,4,opt,name=game_name,json=gameName" json:"game_name"`
}

func (m *UsersTGroup) Reset()                    { *m = UsersTGroup{} }
func (m *UsersTGroup) String() string            { return proto.CompactTextString(m) }
func (*UsersTGroup) ProtoMessage()               {}
func (*UsersTGroup) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{13} }

func (m *UsersTGroup) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UsersTGroup) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *UsersTGroup) GetPortraitMd5() string {
	if m != nil {
		return m.PortraitMd5
	}
	return ""
}

func (m *UsersTGroup) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GetUserDetailResp struct {
	BaseResp           *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Contact            *ga.Contact        `protobuf:"bytes,2,req,name=contact" json:"contact,omitempty"`
	UserTgroupList     []*UsersTGroup     `protobuf:"bytes,3,rep,name=user_tgroup_list,json=userTgroupList" json:"user_tgroup_list,omitempty"`
	HeadwearImg        string             `protobuf:"bytes,4,opt,name=headwear_img,json=headwearImg" json:"headwear_img"`
	ModifiedSex        bool               `protobuf:"varint,5,opt,name=modified_sex,json=modifiedSex" json:"modified_sex"`
	HeadwearUrl        string             `protobuf:"bytes,6,opt,name=headwear_url,json=headwearUrl" json:"headwear_url"`
	HeadwearExtendJson string             `protobuf:"bytes,7,opt,name=headwear_extend_json,json=headwearExtendJson" json:"headwear_extend_json"`
	HeadwearCustomText string             `protobuf:"bytes,8,opt,name=headwear_custom_text,json=headwearCustomText" json:"headwear_custom_text"`
	HeadwearExtend     *ga.HeadwearExtend `protobuf:"bytes,9,opt,name=headwear_extend,json=headwearExtend" json:"headwear_extend,omitempty"`
	AliasSignType      AliasSignType      `protobuf:"varint,10,opt,name=alias_sign_type,json=aliasSignType,enum=ga.AliasSignType" json:"alias_sign_type"`
	AliasExtendInfo    []byte             `protobuf:"bytes,11,opt,name=alias_extend_info,json=aliasExtendInfo" json:"alias_extend_info"`
}

func (m *GetUserDetailResp) Reset()                    { *m = GetUserDetailResp{} }
func (m *GetUserDetailResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserDetailResp) ProtoMessage()               {}
func (*GetUserDetailResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{14} }

func (m *GetUserDetailResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserDetailResp) GetContact() *ga.Contact {
	if m != nil {
		return m.Contact
	}
	return nil
}

func (m *GetUserDetailResp) GetUserTgroupList() []*UsersTGroup {
	if m != nil {
		return m.UserTgroupList
	}
	return nil
}

func (m *GetUserDetailResp) GetHeadwearImg() string {
	if m != nil {
		return m.HeadwearImg
	}
	return ""
}

func (m *GetUserDetailResp) GetModifiedSex() bool {
	if m != nil {
		return m.ModifiedSex
	}
	return false
}

func (m *GetUserDetailResp) GetHeadwearUrl() string {
	if m != nil {
		return m.HeadwearUrl
	}
	return ""
}

func (m *GetUserDetailResp) GetHeadwearExtendJson() string {
	if m != nil {
		return m.HeadwearExtendJson
	}
	return ""
}

func (m *GetUserDetailResp) GetHeadwearCustomText() string {
	if m != nil {
		return m.HeadwearCustomText
	}
	return ""
}

func (m *GetUserDetailResp) GetHeadwearExtend() *ga.HeadwearExtend {
	if m != nil {
		return m.HeadwearExtend
	}
	return nil
}

func (m *GetUserDetailResp) GetAliasSignType() AliasSignType {
	if m != nil {
		return m.AliasSignType
	}
	return AliasSignType_ENUM_ALIAS_TYPE_UNKNOW_TYPE
}

func (m *GetUserDetailResp) GetAliasExtendInfo() []byte {
	if m != nil {
		return m.AliasExtendInfo
	}
	return nil
}

type AliasExtendInfo struct {
	LevelName        string `protobuf:"bytes,1,req,name=level_name,json=levelName" json:"level_name"`
	BackgroundImgUrl string `protobuf:"bytes,2,opt,name=background_img_url,json=backgroundImgUrl" json:"background_img_url"`
	EffectUrl        string `protobuf:"bytes,3,opt,name=effect_url,json=effectUrl" json:"effect_url"`
	EffectMd5        string `protobuf:"bytes,4,opt,name=effect_md5,json=effectMd5" json:"effect_md5"`
	TextColor        string `protobuf:"bytes,5,opt,name=text_color,json=textColor" json:"text_color"`
	LightImgUrl      string `protobuf:"bytes,6,opt,name=light_img_url,json=lightImgUrl" json:"light_img_url"`
	CrownImgUrl      string `protobuf:"bytes,7,opt,name=crown_img_url,json=crownImgUrl" json:"crown_img_url"`
}

func (m *AliasExtendInfo) Reset()                    { *m = AliasExtendInfo{} }
func (m *AliasExtendInfo) String() string            { return proto.CompactTextString(m) }
func (*AliasExtendInfo) ProtoMessage()               {}
func (*AliasExtendInfo) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{15} }

func (m *AliasExtendInfo) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *AliasExtendInfo) GetBackgroundImgUrl() string {
	if m != nil {
		return m.BackgroundImgUrl
	}
	return ""
}

func (m *AliasExtendInfo) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

func (m *AliasExtendInfo) GetEffectMd5() string {
	if m != nil {
		return m.EffectMd5
	}
	return ""
}

func (m *AliasExtendInfo) GetTextColor() string {
	if m != nil {
		return m.TextColor
	}
	return ""
}

func (m *AliasExtendInfo) GetLightImgUrl() string {
	if m != nil {
		return m.LightImgUrl
	}
	return ""
}

func (m *AliasExtendInfo) GetCrownImgUrl() string {
	if m != nil {
		return m.CrownImgUrl
	}
	return ""
}

// 他人详情背景封面图片
type GetCoverReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetAccount string      `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
}

func (m *GetCoverReq) Reset()                    { *m = GetCoverReq{} }
func (m *GetCoverReq) String() string            { return proto.CompactTextString(m) }
func (*GetCoverReq) ProtoMessage()               {}
func (*GetCoverReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{16} }

func (m *GetCoverReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCoverReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

type GetCoverResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Cover    []byte       `protobuf:"bytes,2,req,name=cover" json:"cover"`
}

func (m *GetCoverResp) Reset()                    { *m = GetCoverResp{} }
func (m *GetCoverResp) String() string            { return proto.CompactTextString(m) }
func (*GetCoverResp) ProtoMessage()               {}
func (*GetCoverResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{17} }

func (m *GetCoverResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCoverResp) GetCover() []byte {
	if m != nil {
		return m.Cover
	}
	return nil
}

type GetUserStatusReq struct {
	BaseReq                *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetUid              uint32      `protobuf:"varint,2,opt,name=target_uid,json=targetUid" json:"target_uid"`
	TargetAccount          string      `protobuf:"bytes,3,opt,name=target_account,json=targetAccount" json:"target_account"`
	GetBannedForeverStatus bool        `protobuf:"varint,4,opt,name=get_banned_forever_status,json=getBannedForeverStatus" json:"get_banned_forever_status"`
}

func (m *GetUserStatusReq) Reset()                    { *m = GetUserStatusReq{} }
func (m *GetUserStatusReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserStatusReq) ProtoMessage()               {}
func (*GetUserStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{18} }

func (m *GetUserStatusReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserStatusReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserStatusReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *GetUserStatusReq) GetGetBannedForeverStatus() bool {
	if m != nil {
		return m.GetBannedForeverStatus
	}
	return false
}

type GetUserStatusResp struct {
	BaseResp        *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	IsBannedForever bool         `protobuf:"varint,2,opt,name=is_banned_forever,json=isBannedForever" json:"is_banned_forever"`
}

func (m *GetUserStatusResp) Reset()                    { *m = GetUserStatusResp{} }
func (m *GetUserStatusResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserStatusResp) ProtoMessage()               {}
func (*GetUserStatusResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{19} }

func (m *GetUserStatusResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserStatusResp) GetIsBannedForever() bool {
	if m != nil {
		return m.IsBannedForever
	}
	return false
}

// 检查用户权限
type CheckUserGrantReq struct {
	BaseReq             *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TargetUid           uint32      `protobuf:"varint,2,opt,name=target_uid,json=targetUid" json:"target_uid"`
	TargetAccount       string      `protobuf:"bytes,3,opt,name=target_account,json=targetAccount" json:"target_account"`
	CheckListUserNear   bool        `protobuf:"varint,4,opt,name=check_list_user_near,json=checkListUserNear" json:"check_list_user_near"`
	CheckListFindFriend bool        `protobuf:"varint,5,opt,name=check_list_find_friend,json=checkListFindFriend" json:"check_list_find_friend"`
}

func (m *CheckUserGrantReq) Reset()                    { *m = CheckUserGrantReq{} }
func (m *CheckUserGrantReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUserGrantReq) ProtoMessage()               {}
func (*CheckUserGrantReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{20} }

func (m *CheckUserGrantReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckUserGrantReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *CheckUserGrantReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *CheckUserGrantReq) GetCheckListUserNear() bool {
	if m != nil {
		return m.CheckListUserNear
	}
	return false
}

func (m *CheckUserGrantReq) GetCheckListFindFriend() bool {
	if m != nil {
		return m.CheckListFindFriend
	}
	return false
}

type FindFriendGrant struct {
	CanListFindFriend   bool `protobuf:"varint,1,opt,name=can_list_find_friend,json=canListFindFriend" json:"can_list_find_friend"`
	CanSwitchFindFriend bool `protobuf:"varint,2,opt,name=can_switch_find_friend,json=canSwitchFindFriend" json:"can_switch_find_friend"`
	IsInternalTest      bool `protobuf:"varint,3,opt,name=is_internal_test,json=isInternalTest" json:"is_internal_test"`
}

func (m *FindFriendGrant) Reset()                    { *m = FindFriendGrant{} }
func (m *FindFriendGrant) String() string            { return proto.CompactTextString(m) }
func (*FindFriendGrant) ProtoMessage()               {}
func (*FindFriendGrant) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{21} }

func (m *FindFriendGrant) GetCanListFindFriend() bool {
	if m != nil {
		return m.CanListFindFriend
	}
	return false
}

func (m *FindFriendGrant) GetCanSwitchFindFriend() bool {
	if m != nil {
		return m.CanSwitchFindFriend
	}
	return false
}

func (m *FindFriendGrant) GetIsInternalTest() bool {
	if m != nil {
		return m.IsInternalTest
	}
	return false
}

type CheckUserGrantResp struct {
	BaseResp        *ga.BaseResp     `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CanListUserNear bool             `protobuf:"varint,2,opt,name=can_list_user_near,json=canListUserNear" json:"can_list_user_near"`
	FindFriendGrant *FindFriendGrant `protobuf:"bytes,3,opt,name=find_friend_grant,json=findFriendGrant" json:"find_friend_grant,omitempty"`
}

func (m *CheckUserGrantResp) Reset()                    { *m = CheckUserGrantResp{} }
func (m *CheckUserGrantResp) String() string            { return proto.CompactTextString(m) }
func (*CheckUserGrantResp) ProtoMessage()               {}
func (*CheckUserGrantResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{22} }

func (m *CheckUserGrantResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckUserGrantResp) GetCanListUserNear() bool {
	if m != nil {
		return m.CanListUserNear
	}
	return false
}

func (m *CheckUserGrantResp) GetFindFriendGrant() *FindFriendGrant {
	if m != nil {
		return m.FindFriendGrant
	}
	return nil
}

type SetFindFriendSwitchReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	SwitchOn bool        `protobuf:"varint,2,opt,name=switch_on,json=switchOn" json:"switch_on"`
}

func (m *SetFindFriendSwitchReq) Reset()                    { *m = SetFindFriendSwitchReq{} }
func (m *SetFindFriendSwitchReq) String() string            { return proto.CompactTextString(m) }
func (*SetFindFriendSwitchReq) ProtoMessage()               {}
func (*SetFindFriendSwitchReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{23} }

func (m *SetFindFriendSwitchReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetFindFriendSwitchReq) GetSwitchOn() bool {
	if m != nil {
		return m.SwitchOn
	}
	return false
}

type SetFindFriendSwitchResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SwitchOn bool         `protobuf:"varint,2,opt,name=switch_on,json=switchOn" json:"switch_on"`
}

func (m *SetFindFriendSwitchResp) Reset()                    { *m = SetFindFriendSwitchResp{} }
func (m *SetFindFriendSwitchResp) String() string            { return proto.CompactTextString(m) }
func (*SetFindFriendSwitchResp) ProtoMessage()               {}
func (*SetFindFriendSwitchResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{24} }

func (m *SetFindFriendSwitchResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SetFindFriendSwitchResp) GetSwitchOn() bool {
	if m != nil {
		return m.SwitchOn
	}
	return false
}

type GetUserOnlineTerminalListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetUserOnlineTerminalListReq) Reset()         { *m = GetUserOnlineTerminalListReq{} }
func (m *GetUserOnlineTerminalListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserOnlineTerminalListReq) ProtoMessage()    {}
func (*GetUserOnlineTerminalListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorContact, []int{25}
}

func (m *GetUserOnlineTerminalListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type UserOnlineTerminal struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	DeviceId     []byte `protobuf:"bytes,2,req,name=device_id,json=deviceId" json:"device_id"`
	DeviceName   string `protobuf:"bytes,3,req,name=device_name,json=deviceName" json:"device_name"`
	ClientId     uint32 `protobuf:"varint,4,req,name=client_id,json=clientId" json:"client_id"`
	ClientIp     string `protobuf:"bytes,5,req,name=client_ip,json=clientIp" json:"client_ip"`
	OnlineTime   uint32 `protobuf:"varint,6,req,name=online_time,json=onlineTime" json:"online_time"`
	TerminalType uint32 `protobuf:"varint,7,opt,name=terminal_type,json=terminalType" json:"terminal_type"`
	OsType       uint32 `protobuf:"varint,8,req,name=os_type,json=osType" json:"os_type"`
	PlatformType uint32 `protobuf:"varint,9,req,name=platform_type,json=platformType" json:"platform_type"`
	AppId        uint32 `protobuf:"varint,10,req,name=app_id,json=appId" json:"app_id"`
}

func (m *UserOnlineTerminal) Reset()                    { *m = UserOnlineTerminal{} }
func (m *UserOnlineTerminal) String() string            { return proto.CompactTextString(m) }
func (*UserOnlineTerminal) ProtoMessage()               {}
func (*UserOnlineTerminal) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{26} }

func (m *UserOnlineTerminal) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserOnlineTerminal) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *UserOnlineTerminal) GetDeviceName() string {
	if m != nil {
		return m.DeviceName
	}
	return ""
}

func (m *UserOnlineTerminal) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *UserOnlineTerminal) GetClientIp() string {
	if m != nil {
		return m.ClientIp
	}
	return ""
}

func (m *UserOnlineTerminal) GetOnlineTime() uint32 {
	if m != nil {
		return m.OnlineTime
	}
	return 0
}

func (m *UserOnlineTerminal) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *UserOnlineTerminal) GetOsType() uint32 {
	if m != nil {
		return m.OsType
	}
	return 0
}

func (m *UserOnlineTerminal) GetPlatformType() uint32 {
	if m != nil {
		return m.PlatformType
	}
	return 0
}

func (m *UserOnlineTerminal) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

type GetUserOnlineTerminalListResp struct {
	BaseResp *ga.BaseResp          `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	List     []*UserOnlineTerminal `protobuf:"bytes,2,rep,name=list" json:"list,omitempty"`
}

func (m *GetUserOnlineTerminalListResp) Reset()         { *m = GetUserOnlineTerminalListResp{} }
func (m *GetUserOnlineTerminalListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserOnlineTerminalListResp) ProtoMessage()    {}
func (*GetUserOnlineTerminalListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorContact, []int{27}
}

func (m *GetUserOnlineTerminalListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserOnlineTerminalListResp) GetList() []*UserOnlineTerminal {
	if m != nil {
		return m.List
	}
	return nil
}

type UserKickTerminalReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TerminalType uint32      `protobuf:"varint,2,req,name=terminal_type,json=terminalType" json:"terminal_type"`
}

func (m *UserKickTerminalReq) Reset()                    { *m = UserKickTerminalReq{} }
func (m *UserKickTerminalReq) String() string            { return proto.CompactTextString(m) }
func (*UserKickTerminalReq) ProtoMessage()               {}
func (*UserKickTerminalReq) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{28} }

func (m *UserKickTerminalReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UserKickTerminalReq) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

type UserKickTerminalResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *UserKickTerminalResp) Reset()                    { *m = UserKickTerminalResp{} }
func (m *UserKickTerminalResp) String() string            { return proto.CompactTextString(m) }
func (*UserKickTerminalResp) ProtoMessage()               {}
func (*UserKickTerminalResp) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{29} }

func (m *UserKickTerminalResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type UserTerminalOnlineStatus struct {
	PcOnlineSize uint32 `protobuf:"varint,1,req,name=pc_online_size,json=pcOnlineSize" json:"pc_online_size"`
}

func (m *UserTerminalOnlineStatus) Reset()                    { *m = UserTerminalOnlineStatus{} }
func (m *UserTerminalOnlineStatus) String() string            { return proto.CompactTextString(m) }
func (*UserTerminalOnlineStatus) ProtoMessage()               {}
func (*UserTerminalOnlineStatus) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{30} }

func (m *UserTerminalOnlineStatus) GetPcOnlineSize() uint32 {
	if m != nil {
		return m.PcOnlineSize
	}
	return 0
}

type CheckRelatedLoginAccountReq struct {
	BaseReq              *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	IgnoreRelatedUidList []uint32    `protobuf:"varint,2,rep,name=ignore_related_uid_list,json=ignoreRelatedUidList" json:"ignore_related_uid_list,omitempty"`
}

func (m *CheckRelatedLoginAccountReq) Reset()         { *m = CheckRelatedLoginAccountReq{} }
func (m *CheckRelatedLoginAccountReq) String() string { return proto.CompactTextString(m) }
func (*CheckRelatedLoginAccountReq) ProtoMessage()    {}
func (*CheckRelatedLoginAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorContact, []int{31}
}

func (m *CheckRelatedLoginAccountReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckRelatedLoginAccountReq) GetIgnoreRelatedUidList() []uint32 {
	if m != nil {
		return m.IgnoreRelatedUidList
	}
	return nil
}

type CheckRelatedLoginAccountResp struct {
	BaseResp           *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	HasRelatedAccounts bool         `protobuf:"varint,2,opt,name=has_related_accounts,json=hasRelatedAccounts" json:"has_related_accounts"`
}

func (m *CheckRelatedLoginAccountResp) Reset()         { *m = CheckRelatedLoginAccountResp{} }
func (m *CheckRelatedLoginAccountResp) String() string { return proto.CompactTextString(m) }
func (*CheckRelatedLoginAccountResp) ProtoMessage()    {}
func (*CheckRelatedLoginAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorContact, []int{32}
}

func (m *CheckRelatedLoginAccountResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckRelatedLoginAccountResp) GetHasRelatedAccounts() bool {
	if m != nil {
		return m.HasRelatedAccounts
	}
	return false
}

type GetRelatedLoginAccountReq struct {
	BaseReq              *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	IgnoreRelatedUidList []uint32    `protobuf:"varint,2,rep,name=ignore_related_uid_list,json=ignoreRelatedUidList" json:"ignore_related_uid_list,omitempty"`
}

func (m *GetRelatedLoginAccountReq) Reset()         { *m = GetRelatedLoginAccountReq{} }
func (m *GetRelatedLoginAccountReq) String() string { return proto.CompactTextString(m) }
func (*GetRelatedLoginAccountReq) ProtoMessage()    {}
func (*GetRelatedLoginAccountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorContact, []int{33}
}

func (m *GetRelatedLoginAccountReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRelatedLoginAccountReq) GetIgnoreRelatedUidList() []uint32 {
	if m != nil {
		return m.IgnoreRelatedUidList
	}
	return nil
}

type RelatedLoginAccount struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid" json:"uid"`
	Alias       string `protobuf:"bytes,2,opt,name=alias" json:"alias"`
	Username    string `protobuf:"bytes,3,opt,name=username" json:"username"`
	Nickname    string `protobuf:"bytes,4,opt,name=nickname" json:"nickname"`
	LastLoginAt uint32 `protobuf:"varint,5,opt,name=last_login_at,json=lastLoginAt" json:"last_login_at"`
}

func (m *RelatedLoginAccount) Reset()                    { *m = RelatedLoginAccount{} }
func (m *RelatedLoginAccount) String() string            { return proto.CompactTextString(m) }
func (*RelatedLoginAccount) ProtoMessage()               {}
func (*RelatedLoginAccount) Descriptor() ([]byte, []int) { return fileDescriptorContact, []int{34} }

func (m *RelatedLoginAccount) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RelatedLoginAccount) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *RelatedLoginAccount) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *RelatedLoginAccount) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *RelatedLoginAccount) GetLastLoginAt() uint32 {
	if m != nil {
		return m.LastLoginAt
	}
	return 0
}

type GetRelatedLoginAccountResp struct {
	BaseResp           *ga.BaseResp           `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RelatedAccountList []*RelatedLoginAccount `protobuf:"bytes,2,rep,name=related_account_list,json=relatedAccountList" json:"related_account_list,omitempty"`
}

func (m *GetRelatedLoginAccountResp) Reset()         { *m = GetRelatedLoginAccountResp{} }
func (m *GetRelatedLoginAccountResp) String() string { return proto.CompactTextString(m) }
func (*GetRelatedLoginAccountResp) ProtoMessage()    {}
func (*GetRelatedLoginAccountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorContact, []int{35}
}

func (m *GetRelatedLoginAccountResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRelatedLoginAccountResp) GetRelatedAccountList() []*RelatedLoginAccount {
	if m != nil {
		return m.RelatedAccountList
	}
	return nil
}

func init() {
	proto.RegisterType((*AddFriendReq)(nil), "ga.AddFriendReq")
	proto.RegisterType((*AddFriendResp)(nil), "ga.AddFriendResp")
	proto.RegisterType((*VerifyFriendReq)(nil), "ga.VerifyFriendReq")
	proto.RegisterType((*VerifyFriendResp)(nil), "ga.VerifyFriendResp")
	proto.RegisterType((*DeleteFriendReq)(nil), "ga.DeleteFriendReq")
	proto.RegisterType((*DeleteFriendResp)(nil), "ga.DeleteFriendResp")
	proto.RegisterType((*RemarkFriendReq)(nil), "ga.RemarkFriendReq")
	proto.RegisterType((*RemarkFriendResp)(nil), "ga.RemarkFriendResp")
	proto.RegisterType((*BanFriendReq)(nil), "ga.BanFriendReq")
	proto.RegisterType((*BanFriendResp)(nil), "ga.BanFriendResp")
	proto.RegisterType((*MarkFriendWithStarReq)(nil), "ga.MarkFriendWithStarReq")
	proto.RegisterType((*MarkFriendWithStarResp)(nil), "ga.MarkFriendWithStarResp")
	proto.RegisterType((*GetUserDetailReq)(nil), "ga.GetUserDetailReq")
	proto.RegisterType((*UsersTGroup)(nil), "ga.UsersTGroup")
	proto.RegisterType((*GetUserDetailResp)(nil), "ga.GetUserDetailResp")
	proto.RegisterType((*AliasExtendInfo)(nil), "ga.AliasExtendInfo")
	proto.RegisterType((*GetCoverReq)(nil), "ga.GetCoverReq")
	proto.RegisterType((*GetCoverResp)(nil), "ga.GetCoverResp")
	proto.RegisterType((*GetUserStatusReq)(nil), "ga.GetUserStatusReq")
	proto.RegisterType((*GetUserStatusResp)(nil), "ga.GetUserStatusResp")
	proto.RegisterType((*CheckUserGrantReq)(nil), "ga.CheckUserGrantReq")
	proto.RegisterType((*FindFriendGrant)(nil), "ga.FindFriendGrant")
	proto.RegisterType((*CheckUserGrantResp)(nil), "ga.CheckUserGrantResp")
	proto.RegisterType((*SetFindFriendSwitchReq)(nil), "ga.SetFindFriendSwitchReq")
	proto.RegisterType((*SetFindFriendSwitchResp)(nil), "ga.SetFindFriendSwitchResp")
	proto.RegisterType((*GetUserOnlineTerminalListReq)(nil), "ga.GetUserOnlineTerminalListReq")
	proto.RegisterType((*UserOnlineTerminal)(nil), "ga.UserOnlineTerminal")
	proto.RegisterType((*GetUserOnlineTerminalListResp)(nil), "ga.GetUserOnlineTerminalListResp")
	proto.RegisterType((*UserKickTerminalReq)(nil), "ga.UserKickTerminalReq")
	proto.RegisterType((*UserKickTerminalResp)(nil), "ga.UserKickTerminalResp")
	proto.RegisterType((*UserTerminalOnlineStatus)(nil), "ga.UserTerminalOnlineStatus")
	proto.RegisterType((*CheckRelatedLoginAccountReq)(nil), "ga.CheckRelatedLoginAccountReq")
	proto.RegisterType((*CheckRelatedLoginAccountResp)(nil), "ga.CheckRelatedLoginAccountResp")
	proto.RegisterType((*GetRelatedLoginAccountReq)(nil), "ga.GetRelatedLoginAccountReq")
	proto.RegisterType((*RelatedLoginAccount)(nil), "ga.RelatedLoginAccount")
	proto.RegisterType((*GetRelatedLoginAccountResp)(nil), "ga.GetRelatedLoginAccountResp")
	proto.RegisterEnum("ga.FRIEND_SRC_TYPE", FRIEND_SRC_TYPE_name, FRIEND_SRC_TYPE_value)
	proto.RegisterEnum("ga.AliasSignType", AliasSignType_name, AliasSignType_value)
	proto.RegisterEnum("ga.EOSType", EOSType_name, EOSType_value)
	proto.RegisterEnum("ga.EPlatformType", EPlatformType_name, EPlatformType_value)
	proto.RegisterEnum("ga.EAppID", EAppID_name, EAppID_value)
}
func (m *AddFriendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFriendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.Verify)))
	i += copy(dAtA[i:], m.Verify)
	dAtA[i] = 0x20
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.FriendSrcType))
	return i, nil
}

func (m *AddFriendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFriendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	return i, nil
}

func (m *VerifyFriendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyFriendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x18
	i++
	if m.IsVerify {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x22
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	return i, nil
}

func (m *VerifyFriendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyFriendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	return i, nil
}

func (m *DeleteFriendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteFriendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	return i, nil
}

func (m *DeleteFriendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteFriendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	return i, nil
}

func (m *RemarkFriendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemarkFriendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.UserAccount)))
	i += copy(dAtA[i:], m.UserAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.Remark)))
	i += copy(dAtA[i:], m.Remark)
	dAtA[i] = 0x20
	i++
	if m.IsClean {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RemarkFriendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemarkFriendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.UserAccount)))
	i += copy(dAtA[i:], m.UserAccount)
	return i, nil
}

func (m *BanFriendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BanFriendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x18
	i++
	if m.IsBaned {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BanFriendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BanFriendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x18
	i++
	if m.IsBaned {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *MarkFriendWithStarReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkFriendWithStarReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.UserAccount)))
	i += copy(dAtA[i:], m.UserAccount)
	dAtA[i] = 0x18
	i++
	if m.IsStar {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *MarkFriendWithStarResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkFriendWithStarResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *GetUserDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n13, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x18
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.Cid))
	return i, nil
}

func (m *UsersTGroup) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UsersTGroup) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.GroupName)))
	i += copy(dAtA[i:], m.GroupName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.PortraitMd5)))
	i += copy(dAtA[i:], m.PortraitMd5)
	dAtA[i] = 0x22
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	return i, nil
}

func (m *GetUserDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n14, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	if m.Contact == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("contact")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.Contact.Size()))
		n15, err := m.Contact.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	if len(m.UserTgroupList) > 0 {
		for _, msg := range m.UserTgroupList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintContact(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x22
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.HeadwearImg)))
	i += copy(dAtA[i:], m.HeadwearImg)
	dAtA[i] = 0x28
	i++
	if m.ModifiedSex {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x32
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.HeadwearUrl)))
	i += copy(dAtA[i:], m.HeadwearUrl)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.HeadwearExtendJson)))
	i += copy(dAtA[i:], m.HeadwearExtendJson)
	dAtA[i] = 0x42
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.HeadwearCustomText)))
	i += copy(dAtA[i:], m.HeadwearCustomText)
	if m.HeadwearExtend != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.HeadwearExtend.Size()))
		n16, err := m.HeadwearExtend.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x50
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.AliasSignType))
	if m.AliasExtendInfo != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintContact(dAtA, i, uint64(len(m.AliasExtendInfo)))
		i += copy(dAtA[i:], m.AliasExtendInfo)
	}
	return i, nil
}

func (m *AliasExtendInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AliasExtendInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.LevelName)))
	i += copy(dAtA[i:], m.LevelName)
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.BackgroundImgUrl)))
	i += copy(dAtA[i:], m.BackgroundImgUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.EffectUrl)))
	i += copy(dAtA[i:], m.EffectUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.EffectMd5)))
	i += copy(dAtA[i:], m.EffectMd5)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TextColor)))
	i += copy(dAtA[i:], m.TextColor)
	dAtA[i] = 0x32
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.LightImgUrl)))
	i += copy(dAtA[i:], m.LightImgUrl)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.CrownImgUrl)))
	i += copy(dAtA[i:], m.CrownImgUrl)
	return i, nil
}

func (m *GetCoverReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCoverReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n17, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	return i, nil
}

func (m *GetCoverResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCoverResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n18, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	if m.Cover != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintContact(dAtA, i, uint64(len(m.Cover)))
		i += copy(dAtA[i:], m.Cover)
	}
	return i, nil
}

func (m *GetUserStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x20
	i++
	if m.GetBannedForeverStatus {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n20, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	dAtA[i] = 0x10
	i++
	if m.IsBannedForever {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CheckUserGrantReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserGrantReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n21, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x20
	i++
	if m.CheckListUserNear {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	if m.CheckListFindFriend {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *FindFriendGrant) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendGrant) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.CanListFindFriend {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x10
	i++
	if m.CanSwitchFindFriend {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.IsInternalTest {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CheckUserGrantResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserGrantResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n22, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x10
	i++
	if m.CanListUserNear {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.FindFriendGrant != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.FindFriendGrant.Size()))
		n23, err := m.FindFriendGrant.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	return i, nil
}

func (m *SetFindFriendSwitchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetFindFriendSwitchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n24, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	dAtA[i] = 0x10
	i++
	if m.SwitchOn {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetFindFriendSwitchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetFindFriendSwitchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n25, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	dAtA[i] = 0x10
	i++
	if m.SwitchOn {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserOnlineTerminalListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserOnlineTerminalListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n26, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	return i, nil
}

func (m *UserOnlineTerminal) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserOnlineTerminal) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.Uid))
	if m.DeviceId != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintContact(dAtA, i, uint64(len(m.DeviceId)))
		i += copy(dAtA[i:], m.DeviceId)
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.DeviceName)))
	i += copy(dAtA[i:], m.DeviceName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.ClientId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.ClientIp)))
	i += copy(dAtA[i:], m.ClientIp)
	dAtA[i] = 0x30
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.OnlineTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.TerminalType))
	dAtA[i] = 0x40
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.OsType))
	dAtA[i] = 0x48
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.PlatformType))
	dAtA[i] = 0x50
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.AppId))
	return i, nil
}

func (m *GetUserOnlineTerminalListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserOnlineTerminalListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n27, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	if len(m.List) > 0 {
		for _, msg := range m.List {
			dAtA[i] = 0x12
			i++
			i = encodeVarintContact(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserKickTerminalReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserKickTerminalReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n28, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.TerminalType))
	return i, nil
}

func (m *UserKickTerminalResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserKickTerminalResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n29, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	return i, nil
}

func (m *UserTerminalOnlineStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserTerminalOnlineStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.PcOnlineSize))
	return i, nil
}

func (m *CheckRelatedLoginAccountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckRelatedLoginAccountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n30, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	if len(m.IgnoreRelatedUidList) > 0 {
		for _, num := range m.IgnoreRelatedUidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintContact(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CheckRelatedLoginAccountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckRelatedLoginAccountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n31, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	dAtA[i] = 0x10
	i++
	if m.HasRelatedAccounts {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetRelatedLoginAccountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRelatedLoginAccountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseReq.Size()))
		n32, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n32
	}
	if len(m.IgnoreRelatedUidList) > 0 {
		for _, num := range m.IgnoreRelatedUidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintContact(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *RelatedLoginAccount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RelatedLoginAccount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.Username)))
	i += copy(dAtA[i:], m.Username)
	dAtA[i] = 0x22
	i++
	i = encodeVarintContact(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x28
	i++
	i = encodeVarintContact(dAtA, i, uint64(m.LastLoginAt))
	return i, nil
}

func (m *GetRelatedLoginAccountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRelatedLoginAccountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintContact(dAtA, i, uint64(m.BaseResp.Size()))
		n33, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n33
	}
	if len(m.RelatedAccountList) > 0 {
		for _, msg := range m.RelatedAccountList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintContact(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Contact(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Contact(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintContact(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AddFriendReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.Verify)
	n += 1 + l + sovContact(uint64(l))
	n += 1 + sovContact(uint64(m.FriendSrcType))
	return n
}

func (m *AddFriendResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	return n
}

func (m *VerifyFriendReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.Account)
	n += 1 + l + sovContact(uint64(l))
	n += 2
	l = len(m.Reason)
	n += 1 + l + sovContact(uint64(l))
	return n
}

func (m *VerifyFriendResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	return n
}

func (m *DeleteFriendReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	return n
}

func (m *DeleteFriendResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	return n
}

func (m *RemarkFriendReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.UserAccount)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.Remark)
	n += 1 + l + sovContact(uint64(l))
	n += 2
	return n
}

func (m *RemarkFriendResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.UserAccount)
	n += 1 + l + sovContact(uint64(l))
	return n
}

func (m *BanFriendReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	n += 2
	return n
}

func (m *BanFriendResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	n += 2
	return n
}

func (m *MarkFriendWithStarReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.UserAccount)
	n += 1 + l + sovContact(uint64(l))
	n += 2
	return n
}

func (m *MarkFriendWithStarResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	return n
}

func (m *GetUserDetailReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	n += 1 + sovContact(uint64(m.Cid))
	return n
}

func (m *UsersTGroup) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovContact(uint64(m.GroupId))
	l = len(m.GroupName)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.PortraitMd5)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.GameName)
	n += 1 + l + sovContact(uint64(l))
	return n
}

func (m *GetUserDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	if m.Contact != nil {
		l = m.Contact.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	if len(m.UserTgroupList) > 0 {
		for _, e := range m.UserTgroupList {
			l = e.Size()
			n += 1 + l + sovContact(uint64(l))
		}
	}
	l = len(m.HeadwearImg)
	n += 1 + l + sovContact(uint64(l))
	n += 2
	l = len(m.HeadwearUrl)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.HeadwearExtendJson)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.HeadwearCustomText)
	n += 1 + l + sovContact(uint64(l))
	if m.HeadwearExtend != nil {
		l = m.HeadwearExtend.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	n += 1 + sovContact(uint64(m.AliasSignType))
	if m.AliasExtendInfo != nil {
		l = len(m.AliasExtendInfo)
		n += 1 + l + sovContact(uint64(l))
	}
	return n
}

func (m *AliasExtendInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.LevelName)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.BackgroundImgUrl)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.EffectUrl)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.EffectMd5)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.TextColor)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.LightImgUrl)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.CrownImgUrl)
	n += 1 + l + sovContact(uint64(l))
	return n
}

func (m *GetCoverReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	return n
}

func (m *GetCoverResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	if m.Cover != nil {
		l = len(m.Cover)
		n += 1 + l + sovContact(uint64(l))
	}
	return n
}

func (m *GetUserStatusReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	n += 1 + sovContact(uint64(m.TargetUid))
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	n += 2
	return n
}

func (m *GetUserStatusResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	n += 2
	return n
}

func (m *CheckUserGrantReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	n += 1 + sovContact(uint64(m.TargetUid))
	l = len(m.TargetAccount)
	n += 1 + l + sovContact(uint64(l))
	n += 2
	n += 2
	return n
}

func (m *FindFriendGrant) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 2
	n += 2
	return n
}

func (m *CheckUserGrantResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	n += 2
	if m.FindFriendGrant != nil {
		l = m.FindFriendGrant.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	return n
}

func (m *SetFindFriendSwitchReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	n += 2
	return n
}

func (m *SetFindFriendSwitchResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	n += 2
	return n
}

func (m *GetUserOnlineTerminalListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	return n
}

func (m *UserOnlineTerminal) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovContact(uint64(m.Uid))
	if m.DeviceId != nil {
		l = len(m.DeviceId)
		n += 1 + l + sovContact(uint64(l))
	}
	l = len(m.DeviceName)
	n += 1 + l + sovContact(uint64(l))
	n += 1 + sovContact(uint64(m.ClientId))
	l = len(m.ClientIp)
	n += 1 + l + sovContact(uint64(l))
	n += 1 + sovContact(uint64(m.OnlineTime))
	n += 1 + sovContact(uint64(m.TerminalType))
	n += 1 + sovContact(uint64(m.OsType))
	n += 1 + sovContact(uint64(m.PlatformType))
	n += 1 + sovContact(uint64(m.AppId))
	return n
}

func (m *GetUserOnlineTerminalListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	if len(m.List) > 0 {
		for _, e := range m.List {
			l = e.Size()
			n += 1 + l + sovContact(uint64(l))
		}
	}
	return n
}

func (m *UserKickTerminalReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	n += 1 + sovContact(uint64(m.TerminalType))
	return n
}

func (m *UserKickTerminalResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	return n
}

func (m *UserTerminalOnlineStatus) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovContact(uint64(m.PcOnlineSize))
	return n
}

func (m *CheckRelatedLoginAccountReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	if len(m.IgnoreRelatedUidList) > 0 {
		for _, e := range m.IgnoreRelatedUidList {
			n += 1 + sovContact(uint64(e))
		}
	}
	return n
}

func (m *CheckRelatedLoginAccountResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	n += 2
	return n
}

func (m *GetRelatedLoginAccountReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	if len(m.IgnoreRelatedUidList) > 0 {
		for _, e := range m.IgnoreRelatedUidList {
			n += 1 + sovContact(uint64(e))
		}
	}
	return n
}

func (m *RelatedLoginAccount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovContact(uint64(m.Uid))
	l = len(m.Alias)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.Username)
	n += 1 + l + sovContact(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovContact(uint64(l))
	n += 1 + sovContact(uint64(m.LastLoginAt))
	return n
}

func (m *GetRelatedLoginAccountResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovContact(uint64(l))
	}
	if len(m.RelatedAccountList) > 0 {
		for _, e := range m.RelatedAccountList {
			l = e.Size()
			n += 1 + l + sovContact(uint64(l))
		}
	}
	return n
}

func sovContact(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozContact(x uint64) (n int) {
	return sovContact(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *AddFriendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFriendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFriendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Verify", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Verify = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendSrcType", wireType)
			}
			m.FriendSrcType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendSrcType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("verify")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFriendResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFriendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFriendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyFriendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyFriendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyFriendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsVerify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsVerify = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_verify")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("reason")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyFriendResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyFriendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyFriendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteFriendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteFriendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteFriendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteFriendResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteFriendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteFriendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemarkFriendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemarkFriendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemarkFriendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Remark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Remark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsClean", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsClean = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("user_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("remark")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemarkFriendResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemarkFriendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemarkFriendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("user_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BanFriendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BanFriendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BanFriendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsBaned", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBaned = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_baned")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BanFriendResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BanFriendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BanFriendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsBaned", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBaned = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkFriendWithStarReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MarkFriendWithStarReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MarkFriendWithStarReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsStar", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsStar = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("user_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_star")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkFriendWithStarResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MarkFriendWithStarResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MarkFriendWithStarResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UsersTGroup) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UsersTGroup: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UsersTGroup: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PortraitMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PortraitMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("portrait_md5")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Contact", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Contact == nil {
				m.Contact = &ga.Contact{}
			}
			if err := m.Contact.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserTgroupList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserTgroupList = append(m.UserTgroupList, &UsersTGroup{})
			if err := m.UserTgroupList[len(m.UserTgroupList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadwearImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ModifiedSex", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ModifiedSex = bool(v != 0)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadwearUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearExtendJson", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadwearExtendJson = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearCustomText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HeadwearCustomText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HeadwearExtend", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.HeadwearExtend == nil {
				m.HeadwearExtend = &ga.HeadwearExtend{}
			}
			if err := m.HeadwearExtend.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AliasSignType", wireType)
			}
			m.AliasSignType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AliasSignType |= (AliasSignType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AliasExtendInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AliasExtendInfo = append(m.AliasExtendInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.AliasExtendInfo == nil {
				m.AliasExtendInfo = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("contact")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AliasExtendInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AliasExtendInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AliasExtendInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LevelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LevelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BackgroundImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BackgroundImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EffectUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EffectUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EffectMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EffectMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TextColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LightImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LightImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CrownImgUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CrownImgUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("level_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCoverReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCoverReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCoverReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCoverResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCoverResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCoverResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cover", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Cover = append(m.Cover[:0], dAtA[iNdEx:postIndex]...)
			if m.Cover == nil {
				m.Cover = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("cover")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetBannedForeverStatus", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GetBannedForeverStatus = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsBannedForever", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBannedForever = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserGrantReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserGrantReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserGrantReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckListUserNear", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CheckListUserNear = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckListFindFriend", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CheckListFindFriend = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendGrant) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendGrant: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendGrant: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CanListFindFriend", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanListFindFriend = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CanSwitchFindFriend", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanSwitchFindFriend = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsInternalTest", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsInternalTest = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserGrantResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserGrantResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserGrantResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CanListUserNear", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanListUserNear = bool(v != 0)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindFriendGrant", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FindFriendGrant == nil {
				m.FindFriendGrant = &FindFriendGrant{}
			}
			if err := m.FindFriendGrant.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetFindFriendSwitchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetFindFriendSwitchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetFindFriendSwitchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SwitchOn", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SwitchOn = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetFindFriendSwitchResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetFindFriendSwitchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetFindFriendSwitchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SwitchOn", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SwitchOn = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserOnlineTerminalListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserOnlineTerminalListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserOnlineTerminalListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserOnlineTerminal) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserOnlineTerminal: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserOnlineTerminal: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = append(m.DeviceId[:0], dAtA[iNdEx:postIndex]...)
			if m.DeviceId == nil {
				m.DeviceId = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			m.ClientId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientIp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineTime", wireType)
			}
			m.OnlineTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TerminalType", wireType)
			}
			m.TerminalType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TerminalType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			m.OsType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OsType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlatformType", wireType)
			}
			m.PlatformType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PlatformType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("device_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("client_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("client_ip")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("online_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("os_type")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("platform_type")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("app_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserOnlineTerminalListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserOnlineTerminalListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserOnlineTerminalListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field List", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.List = append(m.List, &UserOnlineTerminal{})
			if err := m.List[len(m.List)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserKickTerminalReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserKickTerminalReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserKickTerminalReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TerminalType", wireType)
			}
			m.TerminalType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TerminalType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("terminal_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserKickTerminalResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserKickTerminalResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserKickTerminalResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserTerminalOnlineStatus) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserTerminalOnlineStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserTerminalOnlineStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PcOnlineSize", wireType)
			}
			m.PcOnlineSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PcOnlineSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("pc_online_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckRelatedLoginAccountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckRelatedLoginAccountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckRelatedLoginAccountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowContact
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.IgnoreRelatedUidList = append(m.IgnoreRelatedUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowContact
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthContact
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowContact
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.IgnoreRelatedUidList = append(m.IgnoreRelatedUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field IgnoreRelatedUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckRelatedLoginAccountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckRelatedLoginAccountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckRelatedLoginAccountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HasRelatedAccounts", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasRelatedAccounts = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRelatedLoginAccountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRelatedLoginAccountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRelatedLoginAccountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowContact
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.IgnoreRelatedUidList = append(m.IgnoreRelatedUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowContact
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthContact
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowContact
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.IgnoreRelatedUidList = append(m.IgnoreRelatedUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field IgnoreRelatedUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RelatedLoginAccount) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RelatedLoginAccount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RelatedLoginAccount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Username", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Username = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastLoginAt", wireType)
			}
			m.LastLoginAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastLoginAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRelatedLoginAccountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContact
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRelatedLoginAccountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRelatedLoginAccountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RelatedAccountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContact
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContact
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RelatedAccountList = append(m.RelatedAccountList, &RelatedLoginAccount{})
			if err := m.RelatedAccountList[len(m.RelatedAccountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipContact(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContact
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipContact(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowContact
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowContact
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowContact
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthContact
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowContact
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipContact(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthContact = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowContact   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("contact.proto", fileDescriptorContact) }

var fileDescriptorContact = []byte{
	// 2418 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x59, 0xcf, 0x6f, 0xdb, 0xc8,
	0xf5, 0x0f, 0xe5, 0x5f, 0xf2, 0x93, 0x65, 0xd1, 0x8c, 0xd7, 0x51, 0x9c, 0x5f, 0x5e, 0xe6, 0xbb,
	0xdf, 0xf5, 0xa6, 0x85, 0x77, 0x6b, 0x20, 0x05, 0x16, 0x3d, 0x2c, 0x68, 0x89, 0xb6, 0xb9, 0x96,
	0x28, 0x85, 0xa4, 0xd6, 0xc9, 0x5e, 0x06, 0x63, 0x6a, 0x24, 0x73, 0x23, 0x91, 0x5a, 0x72, 0xec,
	0x24, 0x8b, 0x02, 0x45, 0x81, 0x02, 0xed, 0xb1, 0xe8, 0xb1, 0x97, 0xa2, 0xa7, 0x9e, 0x8a, 0x1e,
	0x7a, 0xe8, 0xa1, 0x87, 0x5e, 0xf7, 0x52, 0x60, 0xff, 0x82, 0xb6, 0xd8, 0xfe, 0x0d, 0xbd, 0x36,
	0xc5, 0xcc, 0x90, 0xf4, 0x50, 0x76, 0x8a, 0x08, 0x85, 0xb1, 0x37, 0xf9, 0xbd, 0xcf, 0xbc, 0xcf,
	0x7b, 0x9f, 0xf7, 0xe6, 0x07, 0x13, 0xa8, 0xfa, 0x51, 0x48, 0xb1, 0x4f, 0x77, 0x26, 0x71, 0x44,
	0x23, 0xad, 0x34, 0xc4, 0x9b, 0xd5, 0x21, 0x46, 0x27, 0x38, 0x21, 0xc2, 0xa4, 0xff, 0x41, 0x81,
	0x15, 0xa3, 0xdf, 0xdf, 0x8f, 0x03, 0x12, 0xf6, 0x1d, 0xf2, 0xa5, 0xf6, 0xff, 0x50, 0x66, 0x6e,
	0x14, 0x93, 0x2f, 0xeb, 0xca, 0x56, 0x69, 0xbb, 0xb2, 0x5b, 0xd9, 0x19, 0xe2, 0x9d, 0x3d, 0x9c,
	0x10, 0x87, 0x7c, 0xe9, 0x2c, 0x9d, 0x88, 0x1f, 0xda, 0xf7, 0x60, 0x95, 0xe2, 0x78, 0x48, 0x28,
	0xc2, 0xbe, 0x1f, 0x9d, 0x85, 0xb4, 0x5e, 0xda, 0x2a, 0x6d, 0x2f, 0xef, 0xcd, 0x7f, 0xfd, 0xb7,
	0x07, 0x37, 0x9c, 0xaa, 0xf0, 0x19, 0xc2, 0xa5, 0xdd, 0x85, 0xc5, 0x73, 0x12, 0x07, 0x83, 0x57,
	0xf5, 0x39, 0x09, 0x94, 0xda, 0xb4, 0xef, 0x43, 0x6d, 0xc0, 0xf9, 0x51, 0x12, 0xfb, 0x88, 0xbe,
	0x9a, 0x90, 0xfa, 0xfc, 0x96, 0xb2, 0x5d, 0xcd, 0x62, 0x09, 0xa7, 0x1b, 0xfb, 0xde, 0xab, 0x09,
	0xd1, 0x87, 0x50, 0x95, 0x12, 0x4e, 0x26, 0xda, 0x07, 0xb0, 0x9c, 0x66, 0x9c, 0x4c, 0xd2, 0x94,
	0x57, 0x2e, 0x52, 0x4e, 0x26, 0x4e, 0xf9, 0x24, 0xfd, 0x35, 0x53, 0xd2, 0xfa, 0xaf, 0x15, 0xa8,
	0x7d, 0xc6, 0x33, 0x9c, 0x5d, 0x9d, 0xfb, 0xb0, 0x74, 0x15, 0x43, 0x66, 0xd4, 0xde, 0x85, 0xe5,
	0x20, 0x41, 0x92, 0x26, 0xe5, 0x14, 0x51, 0x0e, 0x12, 0xc1, 0xc9, 0x34, 0x8b, 0x09, 0x4e, 0xa2,
	0xb0, 0x3e, 0x2f, 0x6b, 0x26, 0x6c, 0xfa, 0x17, 0xa0, 0x16, 0x73, 0xbb, 0x46, 0x21, 0x06, 0x50,
	0x6b, 0x92, 0x11, 0xa1, 0xe4, 0x7a, 0xa7, 0x84, 0xd5, 0x54, 0xe4, 0xb9, 0xc6, 0x9a, 0x7e, 0xab,
	0x40, 0xcd, 0x21, 0x63, 0x1c, 0x3f, 0x9f, 0xbd, 0xa8, 0xf7, 0x61, 0xe5, 0x2c, 0x21, 0xf1, 0x95,
	0x34, 0x15, 0xe6, 0x91, 0xc6, 0x3e, 0xe6, 0x1c, 0xc5, 0xb1, 0x17, 0x36, 0xed, 0x01, 0x94, 0x83,
	0x04, 0xf9, 0x23, 0x82, 0x43, 0x3e, 0xef, 0xd9, 0x08, 0x2c, 0x05, 0x49, 0x83, 0x19, 0xf5, 0x01,
	0xa8, 0xc5, 0x14, 0x67, 0xd3, 0xe3, 0x6d, 0xd3, 0xd4, 0x7f, 0xa6, 0xc0, 0xca, 0x1e, 0x0e, 0xaf,
	0xf9, 0x0c, 0x10, 0xe5, 0x9e, 0xe0, 0x90, 0xf4, 0x0b, 0x13, 0xbf, 0x14, 0x24, 0x7b, 0xcc, 0xa8,
	0xff, 0x42, 0x81, 0xaa, 0x94, 0xc6, 0xff, 0xde, 0x7c, 0xe5, 0xed, 0x52, 0x51, 0x2e, 0xa7, 0xf2,
	0x73, 0x05, 0xde, 0x69, 0xe7, 0xc2, 0x1f, 0x07, 0xf4, 0xd4, 0xa5, 0x38, 0xbe, 0x96, 0x19, 0xb9,
	0x07, 0x4b, 0x41, 0x82, 0x12, 0x8a, 0xe3, 0x82, 0x2a, 0x8b, 0x41, 0xc2, 0x28, 0xf5, 0x06, 0x6c,
	0x5c, 0x95, 0xc8, 0x4c, 0xe2, 0xe8, 0x3f, 0x01, 0xf5, 0x80, 0xd0, 0x5e, 0x42, 0xe2, 0x26, 0xa1,
	0x38, 0x18, 0x5d, 0x5b, 0x8f, 0x37, 0x60, 0xce, 0x0f, 0x84, 0xa6, 0xd9, 0xe9, 0xcd, 0x0c, 0xfa,
	0x6f, 0x14, 0xa8, 0x30, 0xfa, 0xc4, 0x3b, 0x88, 0xa3, 0xb3, 0x09, 0x6b, 0xc0, 0x90, 0xfd, 0x40,
	0x41, 0x9f, 0x93, 0x67, 0xe0, 0x25, 0x6e, 0xb5, 0xfa, 0xda, 0x43, 0x00, 0x01, 0x08, 0xf1, 0x98,
	0x14, 0x18, 0x97, 0xb9, 0xdd, 0xc6, 0x63, 0xc2, 0x34, 0x9e, 0x44, 0x31, 0x8d, 0x71, 0x40, 0xd1,
	0xb8, 0xff, 0xb8, 0xb0, 0xc9, 0x2a, 0x99, 0xa7, 0xdd, 0x7f, 0xcc, 0x4e, 0xdb, 0x21, 0x1e, 0x13,
	0x11, 0x6c, 0x5e, 0x9a, 0x8b, 0x32, 0x33, 0xb3, 0x58, 0xfa, 0x9f, 0xe7, 0x61, 0x6d, 0x4a, 0xa3,
	0xd9, 0x06, 0xf0, 0x3d, 0x58, 0x4a, 0x2f, 0x5b, 0x9e, 0x6e, 0x2a, 0x67, 0x43, 0x98, 0x9c, 0xcc,
	0xa7, 0x7d, 0x0c, 0x2a, 0x9f, 0x0b, 0x2a, 0xca, 0x1b, 0x05, 0x09, 0xad, 0xcf, 0x6d, 0xcd, 0x6d,
	0x57, 0x76, 0x6b, 0x0c, 0x2f, 0x89, 0xe4, 0xac, 0x32, 0xa0, 0xc7, 0x71, 0xad, 0x20, 0xa1, 0xac,
	0xdc, 0x53, 0x82, 0xfb, 0x2f, 0x08, 0x8e, 0x51, 0x30, 0x1e, 0x16, 0x0a, 0xa9, 0x64, 0x1e, 0x6b,
	0x3c, 0x64, 0xc0, 0x71, 0xd4, 0x0f, 0x06, 0x01, 0xe9, 0xa3, 0x84, 0xbc, 0xac, 0x2f, 0x48, 0x23,
	0x5e, 0xc9, 0x3c, 0x2e, 0x79, 0x59, 0x88, 0x78, 0x16, 0x8f, 0xea, 0x8b, 0x57, 0x45, 0xec, 0xc5,
	0x23, 0xed, 0x87, 0xb0, 0x9e, 0x03, 0xc9, 0x4b, 0xca, 0xae, 0xea, 0x2f, 0xd8, 0xcd, 0xb4, 0x24,
	0x2d, 0xd0, 0x32, 0x84, 0xc9, 0x01, 0x9f, 0x26, 0x51, 0x58, 0x58, 0xe7, 0x9f, 0x25, 0x34, 0x1a,
	0x23, 0x4a, 0x5e, 0xd2, 0x7a, 0xf9, 0xaa, 0x75, 0x0d, 0x0e, 0xf0, 0xc8, 0x4b, 0xaa, 0xfd, 0x08,
	0x6a, 0x53, 0x7c, 0xf5, 0xe5, 0x2d, 0x65, 0xbb, 0xb2, 0xab, 0x31, 0x91, 0x0e, 0x0b, 0x44, 0xce,
	0x6a, 0x91, 0x58, 0xfb, 0x04, 0x6a, 0x78, 0x14, 0xe0, 0x04, 0x25, 0xc1, 0x30, 0x14, 0xcf, 0x09,
	0xd8, 0x52, 0xb6, 0x57, 0x77, 0xd7, 0xd8, 0x62, 0x83, 0xb9, 0xdc, 0x60, 0x18, 0xb2, 0xc7, 0x44,
	0x36, 0xc5, 0x58, 0x36, 0x6a, 0x1f, 0xc1, 0x9a, 0x08, 0x90, 0x96, 0x1a, 0x84, 0x83, 0xa8, 0x5e,
	0xd9, 0x52, 0xb6, 0x57, 0x52, 0xbc, 0x88, 0x2f, 0xe8, 0xac, 0x70, 0x10, 0xe9, 0xbf, 0x2f, 0x41,
	0xcd, 0x28, 0xda, 0xd8, 0x08, 0x8f, 0xc8, 0x39, 0x19, 0x89, 0xa9, 0x53, 0xe4, 0x11, 0xe6, 0x76,
	0x3e, 0xc2, 0xbb, 0xa0, 0x9d, 0x60, 0xff, 0x39, 0x6b, 0x32, 0x23, 0x1a, 0x0f, 0x79, 0x1f, 0xe4,
	0xa3, 0x4b, 0xbd, 0xf0, 0x5b, 0xe3, 0x21, 0x6b, 0xc6, 0x43, 0x00, 0x32, 0x18, 0x10, 0x9f, 0x72,
	0xec, 0x9c, 0x84, 0x5d, 0x16, 0xf6, 0x22, 0x88, 0xed, 0x8c, 0xf9, 0xcb, 0x20, 0xb6, 0x2f, 0x1e,
	0x02, 0xb0, 0x76, 0x20, 0x3f, 0x1a, 0x45, 0x31, 0x1f, 0x93, 0x1c, 0xc4, 0xec, 0x0d, 0x66, 0xd6,
	0xb6, 0xa1, 0x3a, 0x0a, 0x86, 0xa7, 0x34, 0xcf, 0xae, 0x30, 0x25, 0xdc, 0x95, 0x26, 0xb6, 0x0d,
	0x55, 0x3f, 0x8e, 0x5e, 0x84, 0x39, 0x52, 0x1e, 0x8f, 0x0a, 0x77, 0x09, 0xa4, 0x7e, 0x02, 0x95,
	0x03, 0x42, 0x1b, 0xd1, 0x39, 0x89, 0xaf, 0xed, 0x35, 0xd1, 0x83, 0x95, 0x0b, 0x8e, 0xd9, 0xf6,
	0xf2, 0x26, 0x2c, 0xf8, 0x6c, 0x1d, 0x0f, 0x9f, 0x35, 0x5d, 0x98, 0xf4, 0xbf, 0x2a, 0xf9, 0x61,
	0xea, 0x52, 0x4c, 0xcf, 0x92, 0x59, 0x0a, 0x60, 0x82, 0x8b, 0x02, 0xce, 0x82, 0x3e, 0x6f, 0x73,
	0x35, 0x17, 0x9c, 0xdb, 0x7b, 0x41, 0xff, 0x8a, 0x2a, 0xe7, 0xde, 0x7c, 0x95, 0x7d, 0x02, 0xb7,
	0x19, 0xf2, 0x04, 0x87, 0x21, 0xe9, 0xa3, 0x41, 0x14, 0x93, 0x73, 0x12, 0xb3, 0xeb, 0x84, 0x9e,
	0x25, 0x85, 0x57, 0xc5, 0xc6, 0x90, 0xd0, 0x3d, 0x8e, 0xda, 0x17, 0x20, 0x91, 0xbd, 0x3e, 0xc9,
	0xcf, 0xbd, 0xac, 0x9c, 0xd9, 0xb4, 0xfa, 0x08, 0xd6, 0xc4, 0x5d, 0x2a, 0xf1, 0xf3, 0xca, 0x32,
	0xe2, 0x1a, 0xbf, 0x54, 0x2f, 0x78, 0xf5, 0xd7, 0x0a, 0xac, 0x35, 0x4e, 0x89, 0xff, 0x9c, 0x91,
	0x1e, 0xc4, 0x38, 0xa4, 0xdf, 0xad, 0x84, 0x8f, 0x61, 0xdd, 0x67, 0xe9, 0xf0, 0xc3, 0x18, 0xf1,
	0xd3, 0x39, 0x24, 0x38, 0x2e, 0xa8, 0xb7, 0xc6, 0x11, 0xec, 0x18, 0x66, 0x49, 0xdb, 0x04, 0xc7,
	0xda, 0xc7, 0xb0, 0x21, 0x2d, 0x1b, 0x04, 0x61, 0x1f, 0x89, 0x0f, 0x95, 0xc2, 0x79, 0x7b, 0x33,
	0x5f, 0xb8, 0x1f, 0x84, 0xe9, 0x57, 0x8b, 0xfe, 0x47, 0x05, 0x6a, 0x17, 0x7f, 0x72, 0x09, 0x78,
	0x16, 0x38, 0xbc, 0x1c, 0x4c, 0x29, 0x64, 0x81, 0xc3, 0x62, 0x28, 0x9e, 0x05, 0x0e, 0x51, 0xf2,
	0x22, 0xa0, 0xfe, 0x69, 0x61, 0x61, 0xa9, 0x90, 0x05, 0x0e, 0x5d, 0x0e, 0x91, 0x96, 0xee, 0x80,
	0x1a, 0x24, 0x28, 0x08, 0x29, 0x89, 0x43, 0x3c, 0x42, 0x94, 0x24, 0xb4, 0xf0, 0x1a, 0x5a, 0x0d,
	0x12, 0x2b, 0x75, 0x7a, 0x24, 0xa1, 0x2c, 0x6b, 0x6d, 0xba, 0x6f, 0xb3, 0xcd, 0xca, 0x0f, 0x40,
	0xcb, 0x6b, 0xbc, 0xd0, 0xb9, 0x30, 0x2c, 0x69, 0x85, 0xb9, 0xca, 0x9f, 0xc0, 0x9a, 0x54, 0x14,
	0x1a, 0x32, 0x5a, 0x9e, 0x65, 0x65, 0xf7, 0x26, 0x63, 0x99, 0x92, 0xd1, 0xa9, 0x0d, 0x8a, 0x06,
	0xdd, 0x87, 0x0d, 0x97, 0x48, 0x8a, 0x09, 0x19, 0x66, 0x99, 0xb8, 0x77, 0x61, 0x39, 0x95, 0x37,
	0x0a, 0x0b, 0xc9, 0x96, 0x85, 0xb9, 0x13, 0xea, 0x43, 0xb8, 0x75, 0x25, 0xc9, 0x6c, 0xf2, 0xbc,
	0x05, 0xd1, 0x3e, 0xdc, 0x4d, 0x77, 0x6b, 0x27, 0x1c, 0x05, 0x21, 0xf1, 0x48, 0x3c, 0x0e, 0x42,
	0x3c, 0x62, 0x9a, 0xcd, 0x50, 0x93, 0xfe, 0xaf, 0x12, 0x68, 0x97, 0xa3, 0xb0, 0xf7, 0xdb, 0xd9,
	0xd4, 0x93, 0x8c, 0x19, 0x58, 0x66, 0x7d, 0x72, 0x1e, 0xf8, 0x04, 0xf1, 0x3d, 0x77, 0x71, 0x28,
	0x96, 0x85, 0xd9, 0xea, 0x6b, 0xef, 0x41, 0x25, 0x85, 0xf0, 0xfb, 0x4e, 0x7e, 0x8b, 0x81, 0x70,
	0xf0, 0x0b, 0xef, 0x5d, 0x58, 0xf6, 0x47, 0x01, 0x09, 0x29, 0x8b, 0x34, 0x2f, 0xf1, 0x94, 0x85,
	0xd9, 0xea, 0xcb, 0x90, 0x49, 0x7d, 0x41, 0x8a, 0x93, 0x41, 0xd8, 0x63, 0xab, 0x12, 0xf1, 0xcc,
	0x11, 0x0d, 0xc6, 0xa4, 0xbe, 0x28, 0xc5, 0x01, 0xe1, 0xf0, 0x82, 0x31, 0xd1, 0x3e, 0x80, 0x2a,
	0x4d, 0x4b, 0x13, 0xef, 0x80, 0x25, 0xe9, 0xb8, 0x58, 0xc9, 0x5c, 0xfc, 0xce, 0xbf, 0x07, 0x4b,
	0x51, 0x22, 0x40, 0x65, 0x29, 0xda, 0x62, 0x94, 0x70, 0xf7, 0x07, 0x50, 0x9d, 0x8c, 0x30, 0x1d,
	0x44, 0xf1, 0x58, 0x80, 0x96, 0x25, 0xd0, 0x4a, 0xe6, 0xe2, 0xd0, 0x3b, 0xb0, 0x88, 0x27, 0xfc,
	0x65, 0x0b, 0x12, 0x66, 0x01, 0x4f, 0x26, 0x56, 0x5f, 0x3f, 0x87, 0x7b, 0xff, 0xa5, 0x7f, 0xb3,
	0x8d, 0xcb, 0x23, 0x98, 0xe7, 0xcf, 0xc7, 0x12, 0x7f, 0x3e, 0x6e, 0x64, 0xcf, 0xc7, 0x62, 0x60,
	0x87, 0x63, 0xf4, 0x53, 0xb8, 0xc9, 0x7c, 0x47, 0x81, 0xff, 0x3c, 0xf7, 0xcc, 0xb0, 0x05, 0x2e,
	0x09, 0x59, 0x92, 0xcb, 0x97, 0x85, 0xd4, 0x0d, 0x58, 0xbf, 0xcc, 0x34, 0xdb, 0xe7, 0xca, 0x3e,
	0xd4, 0x59, 0x88, 0x6c, 0xb9, 0x28, 0x48, 0xdc, 0x4e, 0xda, 0x23, 0x58, 0x9d, 0xf8, 0x28, 0x6d,
	0x7e, 0x12, 0x7c, 0x45, 0x0a, 0xc3, 0xba, 0x32, 0xf1, 0x53, 0x74, 0xf0, 0x15, 0xd1, 0x7f, 0x0c,
	0x77, 0xf8, 0x79, 0xe5, 0x90, 0x11, 0xa6, 0xa4, 0xdf, 0x8a, 0x86, 0x41, 0x98, 0x1e, 0xfa, 0xb3,
	0x14, 0xff, 0x18, 0x6e, 0x05, 0xc3, 0x30, 0x8a, 0x19, 0x92, 0x07, 0x62, 0x37, 0x0f, 0xca, 0xa5,
	0xaf, 0x3a, 0xeb, 0xc2, 0x9d, 0xd2, 0xf4, 0x82, 0x3e, 0xeb, 0xa6, 0xfe, 0x53, 0x05, 0xee, 0xbe,
	0x99, 0x7e, 0xb6, 0x56, 0xb3, 0x77, 0x34, 0x4e, 0x72, 0xfe, 0xf4, 0x52, 0x4b, 0x0a, 0x87, 0x84,
	0x76, 0x8a, 0x93, 0x94, 0x2a, 0x65, 0x49, 0xf4, 0xaf, 0xe0, 0xf6, 0x01, 0xa1, 0xdf, 0x4d, 0xfd,
	0x7f, 0x52, 0xe0, 0xe6, 0x15, 0xcc, 0x17, 0x67, 0x8c, 0x52, 0x3c, 0x63, 0x36, 0x61, 0x81, 0x3f,
	0xab, 0x0b, 0xaf, 0x5f, 0x61, 0xd2, 0xb6, 0xa0, 0xcc, 0xee, 0x8b, 0xf4, 0x64, 0x91, 0xbe, 0xdf,
	0x32, 0x2b, 0x43, 0x84, 0x81, 0xff, 0xfc, 0xf2, 0x17, 0x5e, 0x66, 0xe5, 0xef, 0x58, 0x9c, 0x50,
	0x34, 0x62, 0xc9, 0x20, 0x4c, 0xf9, 0x35, 0x5d, 0xcd, 0xdf, 0xb1, 0x38, 0xa1, 0x22, 0x4d, 0xaa,
	0xff, 0x4a, 0x81, 0xcd, 0x37, 0xc9, 0x36, 0x5b, 0xdf, 0x2c, 0x58, 0x9f, 0xea, 0x19, 0x92, 0xb6,
	0xec, 0x2d, 0xb6, 0xea, 0x2a, 0x16, 0x2d, 0x2e, 0xf4, 0x91, 0xc9, 0xf9, 0xe8, 0x9b, 0x79, 0xa8,
	0xed, 0x3b, 0x96, 0x69, 0x37, 0x91, 0xeb, 0x34, 0x90, 0xf7, 0xac, 0x6b, 0x6a, 0x1b, 0xa0, 0x49,
	0xa6, 0xa6, 0xb9, 0x6f, 0xf4, 0x5a, 0x9e, 0x7a, 0x43, 0xbb, 0x07, 0xb7, 0x25, 0xfb, 0x41, 0xcf,
	0x6a, 0x35, 0x91, 0xd1, 0xf3, 0x3a, 0xc8, 0x68, 0x36, 0x55, 0x45, 0xdb, 0x84, 0x0d, 0xc9, 0x6d,
	0xd9, 0x9f, 0x59, 0x9e, 0x89, 0x1a, 0x9d, 0xa6, 0xa9, 0x96, 0xa6, 0x42, 0x36, 0x3a, 0xb6, 0x67,
	0x34, 0x3c, 0x75, 0x4e, 0xbb, 0x0b, 0x75, 0x39, 0xa4, 0xd1, 0x36, 0x91, 0x65, 0x7b, 0xa6, 0x63,
	0x1b, 0x2d, 0x75, 0x5e, 0xbb, 0x03, 0xb7, 0x24, 0xef, 0xb1, 0x69, 0x3d, 0xb5, 0x6c, 0xe4, 0x1e,
	0x1a, 0x8e, 0xa9, 0x2e, 0x68, 0xb7, 0xe0, 0xa6, 0xe4, 0x7c, 0xf2, 0x24, 0x75, 0x2c, 0x4e, 0xa5,
	0xd9, 0x73, 0x4d, 0x07, 0x39, 0x66, 0xa3, 0xd3, 0x6e, 0x9b, 0x76, 0x53, 0x5d, 0x9a, 0x4a, 0x73,
	0xdf, 0xb2, 0x9b, 0x48, 0xfc, 0xad, 0x96, 0xa7, 0x08, 0xf7, 0x0d, 0xd7, 0x43, 0x6d, 0xc3, 0x3b,
	0xb4, 0xec, 0x03, 0x75, 0x79, 0xba, 0x7c, 0xa7, 0xd3, 0xeb, 0x5e, 0x24, 0x0b, 0xda, 0x16, 0xdc,
	0xbd, 0xa4, 0x4e, 0xdb, 0x6c, 0xef, 0x99, 0x0e, 0x6a, 0x59, 0xae, 0xa7, 0x56, 0xb4, 0xf7, 0xe1,
	0xa1, 0x2c, 0x82, 0xe5, 0x34, 0x5a, 0x26, 0x6a, 0x38, 0xa6, 0xe1, 0x99, 0xc8, 0xeb, 0x74, 0x2d,
	0x91, 0xac, 0xba, 0xa2, 0x3d, 0x84, 0x07, 0xd3, 0x15, 0x1c, 0x1d, 0xa2, 0xc6, 0xa1, 0x61, 0xdb,
	0x66, 0x8b, 0xf7, 0x48, 0xad, 0x4e, 0x81, 0x0e, 0xad, 0x83, 0xc3, 0x4b, 0xa0, 0x55, 0xed, 0x01,
	0xdc, 0x91, 0x0b, 0xea, 0xd9, 0x45, 0x40, 0x4d, 0xab, 0xc3, 0xba, 0x04, 0x70, 0x0c, 0xfb, 0x48,
	0x64, 0xab, 0x6a, 0xf7, 0x61, 0x53, 0x6e, 0x67, 0xc3, 0xdc, 0x73, 0x4c, 0xe3, 0x28, 0xd3, 0x6a,
	0x6d, 0x4a, 0xff, 0x3d, 0x6b, 0xbf, 0xd3, 0x6a, 0x75, 0x8e, 0x55, 0xed, 0xd1, 0x0b, 0xa8, 0x16,
	0xbe, 0x86, 0x59, 0x12, 0xa6, 0xdd, 0x6b, 0x23, 0xa3, 0x65, 0x19, 0x2e, 0x27, 0x46, 0x3d, 0xfb,
	0xc8, 0xee, 0x1c, 0x8b, 0x24, 0x6e, 0x30, 0xe9, 0xa6, 0x01, 0xe9, 0xd4, 0x21, 0xd7, 0x3a, 0xb0,
	0x55, 0x85, 0x15, 0xfb, 0x26, 0x84, 0xdd, 0x11, 0xa0, 0xd2, 0xa3, 0xdf, 0x29, 0xb0, 0x64, 0x76,
	0x5c, 0xce, 0x79, 0x13, 0x6a, 0x7c, 0x41, 0xc7, 0x4d, 0xb9, 0x6c, 0xf5, 0x86, 0x6c, 0x34, 0xec,
	0xa6, 0xd3, 0xb1, 0xd8, 0xd8, 0xd6, 0xa0, 0x92, 0x19, 0xad, 0x8e, 0xab, 0x96, 0xb4, 0x75, 0x50,
	0x33, 0xc3, 0xb1, 0x65, 0x77, 0x0f, 0x3b, 0xb6, 0xa9, 0xce, 0x69, 0x1a, 0xac, 0x66, 0xd6, 0xb6,
	0xd1, 0xe8, 0xb8, 0x4f, 0xd5, 0x79, 0x39, 0xde, 0xb1, 0x65, 0x37, 0x3b, 0xc7, 0xae, 0xba, 0xa0,
	0xad, 0x41, 0x35, 0x33, 0xb6, 0x2c, 0xbb, 0xf7, 0x54, 0x5d, 0x94, 0x29, 0xda, 0xc6, 0x53, 0xb5,
	0xf6, 0xe8, 0xef, 0x0a, 0x54, 0xcd, 0xae, 0x7c, 0xbd, 0xdf, 0x86, 0x77, 0x38, 0xa4, 0xdb, 0x32,
	0xbc, 0xfd, 0x8e, 0xd3, 0x96, 0xb2, 0xae, 0xc3, 0x7a, 0xd1, 0xd5, 0xee, 0xec, 0x59, 0x2d, 0x53,
	0x55, 0xb4, 0x77, 0x60, 0xad, 0xe8, 0x39, 0x36, 0xf7, 0xa4, 0x02, 0x72, 0x73, 0xb7, 0xa1, 0xce,
	0x5d, 0x06, 0x77, 0x8d, 0xa6, 0x3a, 0xcf, 0x76, 0x60, 0xd1, 0xfc, 0xe4, 0x09, 0x62, 0x53, 0xdb,
	0x34, 0x9b, 0xea, 0x42, 0xde, 0x19, 0x89, 0xa1, 0x71, 0x68, 0x78, 0x39, 0x62, 0xf1, 0x72, 0x58,
	0x51, 0xe1, 0x5f, 0xe6, 0x60, 0xd1, 0x34, 0x26, 0x13, 0xab, 0xc9, 0x06, 0x45, 0xf4, 0xae, 0xdb,
	0xb5, 0x9a, 0xc8, 0x63, 0x1d, 0x6b, 0x1b, 0x2d, 0xf5, 0x46, 0x5e, 0x73, 0xee, 0xb8, 0x68, 0x4a,
	0x56, 0x73, 0xee, 0xb2, 0x44, 0x1f, 0x4a, 0x53, 0x9e, 0x43, 0xa3, 0xdb, 0x7d, 0xd6, 0xb0, 0xbc,
	0x67, 0x6a, 0x85, 0x6d, 0xec, 0xe2, 0x1a, 0x7e, 0x9c, 0xb8, 0xcd, 0x23, 0x75, 0xe5, 0x62, 0x04,
	0x33, 0xdf, 0xb1, 0x65, 0x23, 0xc3, 0x75, 0x2d, 0xd7, 0x33, 0x6c, 0x4f, 0xad, 0x4e, 0xe5, 0x72,
	0xf8, 0x98, 0x49, 0xf9, 0x99, 0x65, 0x1e, 0xab, 0xab, 0xb9, 0x9c, 0xf9, 0xda, 0x6e, 0x43, 0xad,
	0xe5, 0xba, 0xe5, 0x56, 0x49, 0x37, 0xf5, 0x62, 0xa2, 0x73, 0xbe, 0xa2, 0x6e, 0x6b, 0xec, 0x44,
	0x94, 0x09, 0x7b, 0x86, 0xfd, 0xac, 0xd3, 0x53, 0x37, 0xa6, 0xd8, 0x3e, 0x37, 0xac, 0x67, 0x86,
	0x7a, 0x6b, 0x5a, 0x8f, 0x4e, 0x17, 0xb9, 0x5d, 0xd3, 0x6c, 0xaa, 0xf5, 0x29, 0x7c, 0xdb, 0xb0,
	0x8e, 0x4c, 0xf5, 0x76, 0xde, 0x95, 0xd4, 0x6a, 0x7d, 0xca, 0x8e, 0xb0, 0x4d, 0x6d, 0x3d, 0x1d,
	0x62, 0x61, 0x36, 0x5a, 0x2d, 0xf5, 0xdf, 0xaf, 0xe7, 0xa6, 0xac, 0xac, 0x7f, 0xaf, 0x5f, 0xcf,
	0xed, 0x75, 0xbf, 0xfe, 0xf6, 0xbe, 0xf2, 0xcd, 0xb7, 0xf7, 0x95, 0x7f, 0x7c, 0x7b, 0x5f, 0xf9,
	0xe5, 0x3f, 0xef, 0xdf, 0x80, 0xba, 0x1f, 0x8d, 0x77, 0x5e, 0x05, 0xaf, 0xa2, 0x33, 0x76, 0xc3,
	0x8c, 0xa3, 0x3e, 0x19, 0x89, 0xff, 0xee, 0xfb, 0xfc, 0xff, 0x86, 0xd1, 0x08, 0x87, 0xc3, 0x9d,
	0xc7, 0xbb, 0x94, 0xee, 0xf8, 0xd1, 0xf8, 0x43, 0x6e, 0xf6, 0xa3, 0xd1, 0x87, 0x78, 0x32, 0xf9,
	0x30, 0xfd, 0x47, 0xca, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0x28, 0xee, 0x57, 0xba, 0x37, 0x1c,
	0x00, 0x00,
}
