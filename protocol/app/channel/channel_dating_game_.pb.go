// Code generated by protoc-gen-gogo.
// source: channel_dating_game_.proto
// DO NOT EDIT!

/*
	Package channel is a generated protocol buffer package.

	It is generated from these files:
		channel_dating_game_.proto

	It has these top-level messages:
		CheckChannelDatingGameEntryReq
		CheckChannelDatingGameEntryResp
		SetDatingGamePhaseReq
		SetDatingGamePhaseResp
		DatingGamePhasePushNotifyOpt
		DatingGameVipUser
		DatingGameHatUser
		RuleEntryStyle
		GetDatingGameInitInfoReq
		GetDatingGameInitInfoResp
		UserLikeBeatInfo
		AlreadyOpenLikeUserInfo
		DatingGameHoldVipMicReq
		DatingGameHoldVipMicResp
		DatingGameVipMsg
		SelectLikeDatingUserReq
		SelectLikeDatingUserResp
		OpenLikeDatingUserReq
		OpenLikeDatingUserResp
		GetAlreadySelectLikeDatingUserReq
		GetAlreadySelectLikeDatingUserResp
		LikeBeatValChangeMsg
		DatingSelectStatusMsg
		SelectStatusInfo
		OpenDatingLikeUserMsg
		DatingUserInfo
		ApplyDatingMicReq
		ApplyDatingMicResp
		ApplyDatingMicUserInfo
		GetApplyDatingMicUserListReq
		GetApplyDatingMicUserListResp
		ApplyDatingMicMsg
		ShowoffUserInfo
		DatingGameShowoff
		GamePushLikeUserRecord
*/
package channel

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type DatingGamePhaseType int32

const (
	DatingGamePhaseType_DATING_GAME_PHASE_INVALID    DatingGamePhaseType = 0
	DatingGamePhaseType_DATING_GAME_PHASE_FIN        DatingGamePhaseType = 1
	DatingGamePhaseType_DATING_GAME_PHASE_DISCUSSION DatingGamePhaseType = 2
	DatingGamePhaseType_DATING_GAME_PHASE_CHOICE     DatingGamePhaseType = 3
	DatingGamePhaseType_DATING_GAME_PHASE_PUBLISH    DatingGamePhaseType = 4
	DatingGamePhaseType_DATING_GAME_PHASE_CLOSE      DatingGamePhaseType = 5
)

var DatingGamePhaseType_name = map[int32]string{
	0: "DATING_GAME_PHASE_INVALID",
	1: "DATING_GAME_PHASE_FIN",
	2: "DATING_GAME_PHASE_DISCUSSION",
	3: "DATING_GAME_PHASE_CHOICE",
	4: "DATING_GAME_PHASE_PUBLISH",
	5: "DATING_GAME_PHASE_CLOSE",
}
var DatingGamePhaseType_value = map[string]int32{
	"DATING_GAME_PHASE_INVALID":    0,
	"DATING_GAME_PHASE_FIN":        1,
	"DATING_GAME_PHASE_DISCUSSION": 2,
	"DATING_GAME_PHASE_CHOICE":     3,
	"DATING_GAME_PHASE_PUBLISH":    4,
	"DATING_GAME_PHASE_CLOSE":      5,
}

func (x DatingGamePhaseType) Enum() *DatingGamePhaseType {
	p := new(DatingGamePhaseType)
	*p = x
	return p
}
func (x DatingGamePhaseType) String() string {
	return proto.EnumName(DatingGamePhaseType_name, int32(x))
}
func (x *DatingGamePhaseType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(DatingGamePhaseType_value, data, "DatingGamePhaseType")
	if err != nil {
		return err
	}
	*x = DatingGamePhaseType(value)
	return nil
}
func (DatingGamePhaseType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{0}
}

type DatingGameLevel int32

const (
	DatingGameLevel_DATING_GAME_LEVEL_FREE       DatingGameLevel = 0
	DatingGameLevel_DATING_GAME_LEVEL_SENIOR     DatingGameLevel = 1
	DatingGameLevel_DATING_GAME_LEVEL_MEDIATE    DatingGameLevel = 2
	DatingGameLevel_DATING_GAME_LEVEL_PRIMARY    DatingGameLevel = 3
	DatingGameLevel_DATING_GAME_LEVEL_PREFECT_CP DatingGameLevel = 4
	DatingGameLevel_DATING_GAME_LEVEL_TOPEST     DatingGameLevel = 5
)

var DatingGameLevel_name = map[int32]string{
	0: "DATING_GAME_LEVEL_FREE",
	1: "DATING_GAME_LEVEL_SENIOR",
	2: "DATING_GAME_LEVEL_MEDIATE",
	3: "DATING_GAME_LEVEL_PRIMARY",
	4: "DATING_GAME_LEVEL_PREFECT_CP",
	5: "DATING_GAME_LEVEL_TOPEST",
}
var DatingGameLevel_value = map[string]int32{
	"DATING_GAME_LEVEL_FREE":       0,
	"DATING_GAME_LEVEL_SENIOR":     1,
	"DATING_GAME_LEVEL_MEDIATE":    2,
	"DATING_GAME_LEVEL_PRIMARY":    3,
	"DATING_GAME_LEVEL_PREFECT_CP": 4,
	"DATING_GAME_LEVEL_TOPEST":     5,
}

func (x DatingGameLevel) Enum() *DatingGameLevel {
	p := new(DatingGameLevel)
	*p = x
	return p
}
func (x DatingGameLevel) String() string {
	return proto.EnumName(DatingGameLevel_name, int32(x))
}
func (x *DatingGameLevel) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(DatingGameLevel_value, data, "DatingGameLevel")
	if err != nil {
		return err
	}
	*x = DatingGameLevel(value)
	return nil
}
func (DatingGameLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{1}
}

// 检查是否有相亲游戏 入口权限
type CheckChannelDatingGameEntryReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *CheckChannelDatingGameEntryReq) Reset()         { *m = CheckChannelDatingGameEntryReq{} }
func (m *CheckChannelDatingGameEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckChannelDatingGameEntryReq) ProtoMessage()    {}
func (*CheckChannelDatingGameEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{0}
}

func (m *CheckChannelDatingGameEntryReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckChannelDatingGameEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckChannelDatingGameEntryResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	IsOpen   bool         `protobuf:"varint,2,req,name=is_open,json=isOpen" json:"is_open"`
	Level    uint32       `protobuf:"varint,3,opt,name=level" json:"level"`
}

func (m *CheckChannelDatingGameEntryResp) Reset()         { *m = CheckChannelDatingGameEntryResp{} }
func (m *CheckChannelDatingGameEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckChannelDatingGameEntryResp) ProtoMessage()    {}
func (*CheckChannelDatingGameEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{1}
}

func (m *CheckChannelDatingGameEntryResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckChannelDatingGameEntryResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *CheckChannelDatingGameEntryResp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 设置相亲游戏的阶段
type SetDatingGamePhaseReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Phase     uint32      `protobuf:"varint,3,req,name=phase" json:"phase"`
}

func (m *SetDatingGamePhaseReq) Reset()         { *m = SetDatingGamePhaseReq{} }
func (m *SetDatingGamePhaseReq) String() string { return proto.CompactTextString(m) }
func (*SetDatingGamePhaseReq) ProtoMessage()    {}
func (*SetDatingGamePhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{2}
}

func (m *SetDatingGamePhaseReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetDatingGamePhaseReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetDatingGamePhaseReq) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

type SetDatingGamePhaseResp struct {
	BaseResp    *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ChannelId   uint32       `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	FromPhase   uint32       `protobuf:"varint,3,req,name=from_phase,json=fromPhase" json:"from_phase"`
	TargetPhase uint32       `protobuf:"varint,4,req,name=target_phase,json=targetPhase" json:"target_phase"`
}

func (m *SetDatingGamePhaseResp) Reset()         { *m = SetDatingGamePhaseResp{} }
func (m *SetDatingGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*SetDatingGamePhaseResp) ProtoMessage()    {}
func (*SetDatingGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{3}
}

func (m *SetDatingGamePhaseResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SetDatingGamePhaseResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetDatingGamePhaseResp) GetFromPhase() uint32 {
	if m != nil {
		return m.FromPhase
	}
	return 0
}

func (m *SetDatingGamePhaseResp) GetTargetPhase() uint32 {
	if m != nil {
		return m.TargetPhase
	}
	return 0
}

// 阶段变化 的 push notify
type DatingGamePhasePushNotifyOpt struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	FromPhase uint32 `protobuf:"varint,2,req,name=from_phase,json=fromPhase" json:"from_phase"`
	ToPhase   uint32 `protobuf:"varint,3,req,name=to_phase,json=toPhase" json:"to_phase"`
}

func (m *DatingGamePhasePushNotifyOpt) Reset()         { *m = DatingGamePhasePushNotifyOpt{} }
func (m *DatingGamePhasePushNotifyOpt) String() string { return proto.CompactTextString(m) }
func (*DatingGamePhasePushNotifyOpt) ProtoMessage()    {}
func (*DatingGamePhasePushNotifyOpt) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{4}
}

func (m *DatingGamePhasePushNotifyOpt) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DatingGamePhasePushNotifyOpt) GetFromPhase() uint32 {
	if m != nil {
		return m.FromPhase
	}
	return 0
}

func (m *DatingGamePhasePushNotifyOpt) GetToPhase() uint32 {
	if m != nil {
		return m.ToPhase
	}
	return 0
}

// vip麦的用户信息
type DatingGameVipUser struct {
	Uid         uint32          `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account     string          `protobuf:"bytes,2,req,name=account" json:"account"`
	Nickname    string          `protobuf:"bytes,3,req,name=nickname" json:"nickname"`
	UserProfile *ga.UserProfile `protobuf:"bytes,4,opt,name=user_profile,json=userProfile" json:"user_profile,omitempty"`
}

func (m *DatingGameVipUser) Reset()         { *m = DatingGameVipUser{} }
func (m *DatingGameVipUser) String() string { return proto.CompactTextString(m) }
func (*DatingGameVipUser) ProtoMessage()    {}
func (*DatingGameVipUser) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{5}
}

func (m *DatingGameVipUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DatingGameVipUser) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *DatingGameVipUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *DatingGameVipUser) GetUserProfile() *ga.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

// 帽子用户信息
type DatingGameHatUser struct {
	Uid         uint32                 `protobuf:"varint,1,req,name=uid" json:"uid"`
	UrlSource   *ga.DownloadSourceInfo `protobuf:"bytes,2,opt,name=url_source,json=urlSource" json:"url_source,omitempty"`
	IsMale      bool                   `protobuf:"varint,3,opt,name=is_male,json=isMale" json:"is_male"`
	Account     string                 `protobuf:"bytes,4,req,name=account" json:"account"`
	Nickname    string                 `protobuf:"bytes,5,req,name=nickname" json:"nickname"`
	UserProfile *ga.UserProfile        `protobuf:"bytes,6,opt,name=user_profile,json=userProfile" json:"user_profile,omitempty"`
}

func (m *DatingGameHatUser) Reset()         { *m = DatingGameHatUser{} }
func (m *DatingGameHatUser) String() string { return proto.CompactTextString(m) }
func (*DatingGameHatUser) ProtoMessage()    {}
func (*DatingGameHatUser) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{6}
}

func (m *DatingGameHatUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DatingGameHatUser) GetUrlSource() *ga.DownloadSourceInfo {
	if m != nil {
		return m.UrlSource
	}
	return nil
}

func (m *DatingGameHatUser) GetIsMale() bool {
	if m != nil {
		return m.IsMale
	}
	return false
}

func (m *DatingGameHatUser) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *DatingGameHatUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *DatingGameHatUser) GetUserProfile() *ga.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

type RuleEntryStyle struct {
	GameLevel     uint32 `protobuf:"varint,1,req,name=game_level,json=gameLevel" json:"game_level"`
	RulesEntryUrl string `protobuf:"bytes,2,req,name=rules_entry_url,json=rulesEntryUrl" json:"rules_entry_url"`
	StyleColor    string `protobuf:"bytes,3,req,name=style_color,json=styleColor" json:"style_color"`
}

func (m *RuleEntryStyle) Reset()                    { *m = RuleEntryStyle{} }
func (m *RuleEntryStyle) String() string            { return proto.CompactTextString(m) }
func (*RuleEntryStyle) ProtoMessage()               {}
func (*RuleEntryStyle) Descriptor() ([]byte, []int) { return fileDescriptorChannelDatingGame_, []int{7} }

func (m *RuleEntryStyle) GetGameLevel() uint32 {
	if m != nil {
		return m.GameLevel
	}
	return 0
}

func (m *RuleEntryStyle) GetRulesEntryUrl() string {
	if m != nil {
		return m.RulesEntryUrl
	}
	return ""
}

func (m *RuleEntryStyle) GetStyleColor() string {
	if m != nil {
		return m.StyleColor
	}
	return ""
}

// 获取相亲房信息
// 用于进房的时候拉取初始信息（比如麦上用户的心动值 / 当前帽子用户 / 当前土豪位用户 / 当前阶段 ）
type GetDatingGameInitInfoReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetDatingGameInitInfoReq) Reset()         { *m = GetDatingGameInitInfoReq{} }
func (m *GetDatingGameInitInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetDatingGameInitInfoReq) ProtoMessage()    {}
func (*GetDatingGameInitInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{8}
}

func (m *GetDatingGameInitInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetDatingGameInitInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetDatingGameInitInfoResp struct {
	BaseResp         *ga.BaseResp               `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Phase            uint32                     `protobuf:"varint,2,req,name=phase" json:"phase"`
	VipUser          *DatingGameVipUser         `protobuf:"bytes,3,opt,name=vip_user,json=vipUser" json:"vip_user,omitempty"`
	HatUserList      []*DatingGameHatUser       `protobuf:"bytes,4,rep,name=hat_user_list,json=hatUserList" json:"hat_user_list,omitempty"`
	LikeBeatList     []*UserLikeBeatInfo        `protobuf:"bytes,5,rep,name=like_beat_list,json=likeBeatList" json:"like_beat_list,omitempty"`
	OpenLikeUserList []*AlreadyOpenLikeUserInfo `protobuf:"bytes,6,rep,name=open_like_user_list,json=openLikeUserList" json:"open_like_user_list,omitempty"`
	ApplyMicLen      uint32                     `protobuf:"varint,7,opt,name=apply_mic_len,json=applyMicLen" json:"apply_mic_len"`
	StyleList        []*RuleEntryStyle          `protobuf:"bytes,8,rep,name=style_list,json=styleList" json:"style_list,omitempty"`
}

func (m *GetDatingGameInitInfoResp) Reset()         { *m = GetDatingGameInitInfoResp{} }
func (m *GetDatingGameInitInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetDatingGameInitInfoResp) ProtoMessage()    {}
func (*GetDatingGameInitInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{9}
}

func (m *GetDatingGameInitInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetDatingGameInitInfoResp) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *GetDatingGameInitInfoResp) GetVipUser() *DatingGameVipUser {
	if m != nil {
		return m.VipUser
	}
	return nil
}

func (m *GetDatingGameInitInfoResp) GetHatUserList() []*DatingGameHatUser {
	if m != nil {
		return m.HatUserList
	}
	return nil
}

func (m *GetDatingGameInitInfoResp) GetLikeBeatList() []*UserLikeBeatInfo {
	if m != nil {
		return m.LikeBeatList
	}
	return nil
}

func (m *GetDatingGameInitInfoResp) GetOpenLikeUserList() []*AlreadyOpenLikeUserInfo {
	if m != nil {
		return m.OpenLikeUserList
	}
	return nil
}

func (m *GetDatingGameInitInfoResp) GetApplyMicLen() uint32 {
	if m != nil {
		return m.ApplyMicLen
	}
	return 0
}

func (m *GetDatingGameInitInfoResp) GetStyleList() []*RuleEntryStyle {
	if m != nil {
		return m.StyleList
	}
	return nil
}

type UserLikeBeatInfo struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LikeBeatVal  uint32 `protobuf:"varint,2,req,name=like_beat_val,json=likeBeatVal" json:"like_beat_val"`
	SelectStatus bool   `protobuf:"varint,3,opt,name=select_status,json=selectStatus" json:"select_status"`
}

func (m *UserLikeBeatInfo) Reset()         { *m = UserLikeBeatInfo{} }
func (m *UserLikeBeatInfo) String() string { return proto.CompactTextString(m) }
func (*UserLikeBeatInfo) ProtoMessage()    {}
func (*UserLikeBeatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{10}
}

func (m *UserLikeBeatInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLikeBeatInfo) GetLikeBeatVal() uint32 {
	if m != nil {
		return m.LikeBeatVal
	}
	return 0
}

func (m *UserLikeBeatInfo) GetSelectStatus() bool {
	if m != nil {
		return m.SelectStatus
	}
	return false
}

// 已公布用户
type AlreadyOpenLikeUserInfo struct {
	OpenUid uint32 `protobuf:"varint,1,req,name=open_uid,json=openUid" json:"open_uid"`
	LikeUid uint32 `protobuf:"varint,2,req,name=like_uid,json=likeUid" json:"like_uid"`
}

func (m *AlreadyOpenLikeUserInfo) Reset()         { *m = AlreadyOpenLikeUserInfo{} }
func (m *AlreadyOpenLikeUserInfo) String() string { return proto.CompactTextString(m) }
func (*AlreadyOpenLikeUserInfo) ProtoMessage()    {}
func (*AlreadyOpenLikeUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{11}
}

func (m *AlreadyOpenLikeUserInfo) GetOpenUid() uint32 {
	if m != nil {
		return m.OpenUid
	}
	return 0
}

func (m *AlreadyOpenLikeUserInfo) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

// 上vip麦
type DatingGameHoldVipMicReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	IsManual  bool        `protobuf:"varint,3,req,name=is_manual,json=isManual" json:"is_manual"`
}

func (m *DatingGameHoldVipMicReq) Reset()         { *m = DatingGameHoldVipMicReq{} }
func (m *DatingGameHoldVipMicReq) String() string { return proto.CompactTextString(m) }
func (*DatingGameHoldVipMicReq) ProtoMessage()    {}
func (*DatingGameHoldVipMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{12}
}

func (m *DatingGameHoldVipMicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DatingGameHoldVipMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DatingGameHoldVipMicReq) GetIsManual() bool {
	if m != nil {
		return m.IsManual
	}
	return false
}

type DatingGameHoldVipMicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *DatingGameHoldVipMicResp) Reset()         { *m = DatingGameHoldVipMicResp{} }
func (m *DatingGameHoldVipMicResp) String() string { return proto.CompactTextString(m) }
func (*DatingGameHoldVipMicResp) ProtoMessage()    {}
func (*DatingGameHoldVipMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{13}
}

func (m *DatingGameHoldVipMicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 相亲房的可上vip麦位的通知(单推)
type DatingGameVipMsg struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *DatingGameVipMsg) Reset()         { *m = DatingGameVipMsg{} }
func (m *DatingGameVipMsg) String() string { return proto.CompactTextString(m) }
func (*DatingGameVipMsg) ProtoMessage()    {}
func (*DatingGameVipMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{14}
}

func (m *DatingGameVipMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DatingGameVipMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 选择心动对象
type SelectLikeDatingUserReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	TargetUid uint32      `protobuf:"varint,3,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *SelectLikeDatingUserReq) Reset()         { *m = SelectLikeDatingUserReq{} }
func (m *SelectLikeDatingUserReq) String() string { return proto.CompactTextString(m) }
func (*SelectLikeDatingUserReq) ProtoMessage()    {}
func (*SelectLikeDatingUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{15}
}

func (m *SelectLikeDatingUserReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SelectLikeDatingUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SelectLikeDatingUserReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type SelectLikeDatingUserResp struct {
	BaseResp  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TargetUid uint32       `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *SelectLikeDatingUserResp) Reset()         { *m = SelectLikeDatingUserResp{} }
func (m *SelectLikeDatingUserResp) String() string { return proto.CompactTextString(m) }
func (*SelectLikeDatingUserResp) ProtoMessage()    {}
func (*SelectLikeDatingUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{16}
}

func (m *SelectLikeDatingUserResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SelectLikeDatingUserResp) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

// 公布心动对象
type OpenLikeDatingUserReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	OpenUid   uint32      `protobuf:"varint,3,req,name=open_uid,json=openUid" json:"open_uid"`
}

func (m *OpenLikeDatingUserReq) Reset()         { *m = OpenLikeDatingUserReq{} }
func (m *OpenLikeDatingUserReq) String() string { return proto.CompactTextString(m) }
func (*OpenLikeDatingUserReq) ProtoMessage()    {}
func (*OpenLikeDatingUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{17}
}

func (m *OpenLikeDatingUserReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *OpenLikeDatingUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenLikeDatingUserReq) GetOpenUid() uint32 {
	if m != nil {
		return m.OpenUid
	}
	return 0
}

type OpenLikeDatingUserResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *OpenLikeDatingUserResp) Reset()         { *m = OpenLikeDatingUserResp{} }
func (m *OpenLikeDatingUserResp) String() string { return proto.CompactTextString(m) }
func (*OpenLikeDatingUserResp) ProtoMessage()    {}
func (*OpenLikeDatingUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{18}
}

func (m *OpenLikeDatingUserResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取已经选择心动的用户
type GetAlreadySelectLikeDatingUserReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetAlreadySelectLikeDatingUserReq) Reset()         { *m = GetAlreadySelectLikeDatingUserReq{} }
func (m *GetAlreadySelectLikeDatingUserReq) String() string { return proto.CompactTextString(m) }
func (*GetAlreadySelectLikeDatingUserReq) ProtoMessage()    {}
func (*GetAlreadySelectLikeDatingUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{19}
}

func (m *GetAlreadySelectLikeDatingUserReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAlreadySelectLikeDatingUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetAlreadySelectLikeDatingUserResp struct {
	BaseResp      *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SelectUidList []uint32     `protobuf:"varint,2,rep,name=select_uid_list,json=selectUidList" json:"select_uid_list,omitempty"`
}

func (m *GetAlreadySelectLikeDatingUserResp) Reset()         { *m = GetAlreadySelectLikeDatingUserResp{} }
func (m *GetAlreadySelectLikeDatingUserResp) String() string { return proto.CompactTextString(m) }
func (*GetAlreadySelectLikeDatingUserResp) ProtoMessage()    {}
func (*GetAlreadySelectLikeDatingUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{20}
}

func (m *GetAlreadySelectLikeDatingUserResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAlreadySelectLikeDatingUserResp) GetSelectUidList() []uint32 {
	if m != nil {
		return m.SelectUidList
	}
	return nil
}

// 心动值变化push msg
type LikeBeatValChangeMsg struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid         uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	LikeBeatVal uint32 `protobuf:"varint,3,req,name=like_beat_val,json=likeBeatVal" json:"like_beat_val"`
}

func (m *LikeBeatValChangeMsg) Reset()         { *m = LikeBeatValChangeMsg{} }
func (m *LikeBeatValChangeMsg) String() string { return proto.CompactTextString(m) }
func (*LikeBeatValChangeMsg) ProtoMessage()    {}
func (*LikeBeatValChangeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{21}
}

func (m *LikeBeatValChangeMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LikeBeatValChangeMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LikeBeatValChangeMsg) GetLikeBeatVal() uint32 {
	if m != nil {
		return m.LikeBeatVal
	}
	return 0
}

// 心动状态push msg
type DatingSelectStatusMsg struct {
	ChannelId      uint32              `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	StatusInfoList []*SelectStatusInfo `protobuf:"bytes,2,rep,name=status_info_list,json=statusInfoList" json:"status_info_list,omitempty"`
}

func (m *DatingSelectStatusMsg) Reset()         { *m = DatingSelectStatusMsg{} }
func (m *DatingSelectStatusMsg) String() string { return proto.CompactTextString(m) }
func (*DatingSelectStatusMsg) ProtoMessage()    {}
func (*DatingSelectStatusMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{22}
}

func (m *DatingSelectStatusMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DatingSelectStatusMsg) GetStatusInfoList() []*SelectStatusInfo {
	if m != nil {
		return m.StatusInfoList
	}
	return nil
}

type SelectStatusInfo struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	SelectStatus bool   `protobuf:"varint,2,req,name=select_status,json=selectStatus" json:"select_status"`
}

func (m *SelectStatusInfo) Reset()         { *m = SelectStatusInfo{} }
func (m *SelectStatusInfo) String() string { return proto.CompactTextString(m) }
func (*SelectStatusInfo) ProtoMessage()    {}
func (*SelectStatusInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{23}
}

func (m *SelectStatusInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SelectStatusInfo) GetSelectStatus() bool {
	if m != nil {
		return m.SelectStatus
	}
	return false
}

// 公布心动对象push msg
type OpenDatingLikeUserMsg struct {
	ChannelId uint32          `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	OpenUid   uint32          `protobuf:"varint,2,req,name=open_uid,json=openUid" json:"open_uid"`
	LikeUser  *DatingUserInfo `protobuf:"bytes,3,req,name=like_user,json=likeUser" json:"like_user,omitempty"`
}

func (m *OpenDatingLikeUserMsg) Reset()         { *m = OpenDatingLikeUserMsg{} }
func (m *OpenDatingLikeUserMsg) String() string { return proto.CompactTextString(m) }
func (*OpenDatingLikeUserMsg) ProtoMessage()    {}
func (*OpenDatingLikeUserMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{24}
}

func (m *OpenDatingLikeUserMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenDatingLikeUserMsg) GetOpenUid() uint32 {
	if m != nil {
		return m.OpenUid
	}
	return 0
}

func (m *OpenDatingLikeUserMsg) GetLikeUser() *DatingUserInfo {
	if m != nil {
		return m.LikeUser
	}
	return nil
}

type DatingUserInfo struct {
	Uid         uint32          `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account     string          `protobuf:"bytes,2,req,name=account" json:"account"`
	Nickname    string          `protobuf:"bytes,3,req,name=nickname" json:"nickname"`
	Sex         uint32          `protobuf:"varint,4,req,name=sex" json:"sex"`
	UserProfile *ga.UserProfile `protobuf:"bytes,5,opt,name=user_profile,json=userProfile" json:"user_profile,omitempty"`
}

func (m *DatingUserInfo) Reset()         { *m = DatingUserInfo{} }
func (m *DatingUserInfo) String() string { return proto.CompactTextString(m) }
func (*DatingUserInfo) ProtoMessage()    {}
func (*DatingUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{25}
}

func (m *DatingUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DatingUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *DatingUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *DatingUserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *DatingUserInfo) GetUserProfile() *ga.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

// 排麦
type ApplyDatingMicReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	IsCancel  bool        `protobuf:"varint,3,req,name=is_cancel,json=isCancel" json:"is_cancel"`
}

func (m *ApplyDatingMicReq) Reset()         { *m = ApplyDatingMicReq{} }
func (m *ApplyDatingMicReq) String() string { return proto.CompactTextString(m) }
func (*ApplyDatingMicReq) ProtoMessage()    {}
func (*ApplyDatingMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{26}
}

func (m *ApplyDatingMicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ApplyDatingMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ApplyDatingMicReq) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

type ApplyDatingMicResp struct {
	BaseResp      *ga.BaseResp              `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	IsCancel      bool                      `protobuf:"varint,2,req,name=is_cancel,json=isCancel" json:"is_cancel"`
	ApplyUserList []*ApplyDatingMicUserInfo `protobuf:"bytes,3,rep,name=apply_user_list,json=applyUserList" json:"apply_user_list,omitempty"`
}

func (m *ApplyDatingMicResp) Reset()         { *m = ApplyDatingMicResp{} }
func (m *ApplyDatingMicResp) String() string { return proto.CompactTextString(m) }
func (*ApplyDatingMicResp) ProtoMessage()    {}
func (*ApplyDatingMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{27}
}

func (m *ApplyDatingMicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ApplyDatingMicResp) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

func (m *ApplyDatingMicResp) GetApplyUserList() []*ApplyDatingMicUserInfo {
	if m != nil {
		return m.ApplyUserList
	}
	return nil
}

// 排麦列表
type ApplyDatingMicUserInfo struct {
	Uid         uint32          `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account     string          `protobuf:"bytes,2,req,name=account" json:"account"`
	Sex         uint32          `protobuf:"varint,3,req,name=sex" json:"sex"`
	NickName    string          `protobuf:"bytes,4,req,name=nick_name,json=nickName" json:"nick_name"`
	UserProfile *ga.UserProfile `protobuf:"bytes,5,opt,name=user_profile,json=userProfile" json:"user_profile,omitempty"`
}

func (m *ApplyDatingMicUserInfo) Reset()         { *m = ApplyDatingMicUserInfo{} }
func (m *ApplyDatingMicUserInfo) String() string { return proto.CompactTextString(m) }
func (*ApplyDatingMicUserInfo) ProtoMessage()    {}
func (*ApplyDatingMicUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{28}
}

func (m *ApplyDatingMicUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyDatingMicUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ApplyDatingMicUserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ApplyDatingMicUserInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ApplyDatingMicUserInfo) GetUserProfile() *ga.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

type GetApplyDatingMicUserListReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ChannelId uint32      `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetApplyDatingMicUserListReq) Reset()         { *m = GetApplyDatingMicUserListReq{} }
func (m *GetApplyDatingMicUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetApplyDatingMicUserListReq) ProtoMessage()    {}
func (*GetApplyDatingMicUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{29}
}

func (m *GetApplyDatingMicUserListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetApplyDatingMicUserListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetApplyDatingMicUserListResp struct {
	BaseResp      *ga.BaseResp              `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ApplyUserList []*ApplyDatingMicUserInfo `protobuf:"bytes,2,rep,name=apply_user_list,json=applyUserList" json:"apply_user_list,omitempty"`
}

func (m *GetApplyDatingMicUserListResp) Reset()         { *m = GetApplyDatingMicUserListResp{} }
func (m *GetApplyDatingMicUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetApplyDatingMicUserListResp) ProtoMessage()    {}
func (*GetApplyDatingMicUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{30}
}

func (m *GetApplyDatingMicUserListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetApplyDatingMicUserListResp) GetApplyUserList() []*ApplyDatingMicUserInfo {
	if m != nil {
		return m.ApplyUserList
	}
	return nil
}

// 排麦push msg
type ApplyDatingMicMsg struct {
	ChannelId           uint32          `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	IsCancel            bool            `protobuf:"varint,2,req,name=is_cancel,json=isCancel" json:"is_cancel"`
	Account             string          `protobuf:"bytes,3,req,name=account" json:"account"`
	ApplyUserCount      uint32          `protobuf:"varint,4,req,name=apply_user_count,json=applyUserCount" json:"apply_user_count"`
	EntranceAccount     string          `protobuf:"bytes,5,opt,name=entrance_account,json=entranceAccount" json:"entrance_account"`
	ApplyUserProfile    *ga.UserProfile `protobuf:"bytes,6,opt,name=apply_user_profile,json=applyUserProfile" json:"apply_user_profile,omitempty"`
	EntranceUserProfile *ga.UserProfile `protobuf:"bytes,7,opt,name=entrance_user_profile,json=entranceUserProfile" json:"entrance_user_profile,omitempty"`
}

func (m *ApplyDatingMicMsg) Reset()         { *m = ApplyDatingMicMsg{} }
func (m *ApplyDatingMicMsg) String() string { return proto.CompactTextString(m) }
func (*ApplyDatingMicMsg) ProtoMessage()    {}
func (*ApplyDatingMicMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{31}
}

func (m *ApplyDatingMicMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ApplyDatingMicMsg) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

func (m *ApplyDatingMicMsg) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ApplyDatingMicMsg) GetApplyUserCount() uint32 {
	if m != nil {
		return m.ApplyUserCount
	}
	return 0
}

func (m *ApplyDatingMicMsg) GetEntranceAccount() string {
	if m != nil {
		return m.EntranceAccount
	}
	return ""
}

func (m *ApplyDatingMicMsg) GetApplyUserProfile() *ga.UserProfile {
	if m != nil {
		return m.ApplyUserProfile
	}
	return nil
}

func (m *ApplyDatingMicMsg) GetEntranceUserProfile() *ga.UserProfile {
	if m != nil {
		return m.EntranceUserProfile
	}
	return nil
}

type ShowoffUserInfo struct {
	Uid         uint32          `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account     string          `protobuf:"bytes,2,req,name=account" json:"account"`
	Nickname    string          `protobuf:"bytes,3,req,name=nickname" json:"nickname"`
	Sex         uint32          `protobuf:"varint,4,req,name=sex" json:"sex"`
	UserProfile *ga.UserProfile `protobuf:"bytes,5,opt,name=user_profile,json=userProfile" json:"user_profile,omitempty"`
}

func (m *ShowoffUserInfo) Reset()         { *m = ShowoffUserInfo{} }
func (m *ShowoffUserInfo) String() string { return proto.CompactTextString(m) }
func (*ShowoffUserInfo) ProtoMessage()    {}
func (*ShowoffUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{32}
}

func (m *ShowoffUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ShowoffUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ShowoffUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ShowoffUserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ShowoffUserInfo) GetUserProfile() *ga.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

type DatingGameShowoff struct {
	DsInfo          *ga.DownloadSourceInfo `protobuf:"bytes,1,req,name=ds_info,json=dsInfo" json:"ds_info,omitempty"`
	ShowoffImageUrl string                 `protobuf:"bytes,2,req,name=showoff_image_url,json=showoffImageUrl" json:"showoff_image_url"`
	ShowoffUinfos   []*ShowoffUserInfo     `protobuf:"bytes,3,rep,name=showoff_uinfos,json=showoffUinfos" json:"showoff_uinfos,omitempty"`
	ServerTime      uint32                 `protobuf:"varint,4,req,name=server_time,json=serverTime" json:"server_time"`
}

func (m *DatingGameShowoff) Reset()         { *m = DatingGameShowoff{} }
func (m *DatingGameShowoff) String() string { return proto.CompactTextString(m) }
func (*DatingGameShowoff) ProtoMessage()    {}
func (*DatingGameShowoff) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{33}
}

func (m *DatingGameShowoff) GetDsInfo() *ga.DownloadSourceInfo {
	if m != nil {
		return m.DsInfo
	}
	return nil
}

func (m *DatingGameShowoff) GetShowoffImageUrl() string {
	if m != nil {
		return m.ShowoffImageUrl
	}
	return ""
}

func (m *DatingGameShowoff) GetShowoffUinfos() []*ShowoffUserInfo {
	if m != nil {
		return m.ShowoffUinfos
	}
	return nil
}

func (m *DatingGameShowoff) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

type GamePushLikeUserRecord struct {
	ShowoffImageUrl     string `protobuf:"bytes,1,req,name=showoff_image_url,json=showoffImageUrl" json:"showoff_image_url"`
	DescFirst           string `protobuf:"bytes,2,req,name=desc_first,json=descFirst" json:"desc_first"`
	DescLast            string `protobuf:"bytes,3,req,name=desc_last,json=descLast" json:"desc_last"`
	ShowoffFullImageUrl string `protobuf:"bytes,4,req,name=showoff_full_image_url,json=showoffFullImageUrl" json:"showoff_full_image_url"`
}

func (m *GamePushLikeUserRecord) Reset()         { *m = GamePushLikeUserRecord{} }
func (m *GamePushLikeUserRecord) String() string { return proto.CompactTextString(m) }
func (*GamePushLikeUserRecord) ProtoMessage()    {}
func (*GamePushLikeUserRecord) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelDatingGame_, []int{34}
}

func (m *GamePushLikeUserRecord) GetShowoffImageUrl() string {
	if m != nil {
		return m.ShowoffImageUrl
	}
	return ""
}

func (m *GamePushLikeUserRecord) GetDescFirst() string {
	if m != nil {
		return m.DescFirst
	}
	return ""
}

func (m *GamePushLikeUserRecord) GetDescLast() string {
	if m != nil {
		return m.DescLast
	}
	return ""
}

func (m *GamePushLikeUserRecord) GetShowoffFullImageUrl() string {
	if m != nil {
		return m.ShowoffFullImageUrl
	}
	return ""
}

func init() {
	proto.RegisterType((*CheckChannelDatingGameEntryReq)(nil), "ga.CheckChannelDatingGameEntryReq")
	proto.RegisterType((*CheckChannelDatingGameEntryResp)(nil), "ga.CheckChannelDatingGameEntryResp")
	proto.RegisterType((*SetDatingGamePhaseReq)(nil), "ga.SetDatingGamePhaseReq")
	proto.RegisterType((*SetDatingGamePhaseResp)(nil), "ga.SetDatingGamePhaseResp")
	proto.RegisterType((*DatingGamePhasePushNotifyOpt)(nil), "ga.DatingGamePhasePushNotifyOpt")
	proto.RegisterType((*DatingGameVipUser)(nil), "ga.DatingGameVipUser")
	proto.RegisterType((*DatingGameHatUser)(nil), "ga.DatingGameHatUser")
	proto.RegisterType((*RuleEntryStyle)(nil), "ga.RuleEntryStyle")
	proto.RegisterType((*GetDatingGameInitInfoReq)(nil), "ga.GetDatingGameInitInfoReq")
	proto.RegisterType((*GetDatingGameInitInfoResp)(nil), "ga.GetDatingGameInitInfoResp")
	proto.RegisterType((*UserLikeBeatInfo)(nil), "ga.UserLikeBeatInfo")
	proto.RegisterType((*AlreadyOpenLikeUserInfo)(nil), "ga.AlreadyOpenLikeUserInfo")
	proto.RegisterType((*DatingGameHoldVipMicReq)(nil), "ga.DatingGameHoldVipMicReq")
	proto.RegisterType((*DatingGameHoldVipMicResp)(nil), "ga.DatingGameHoldVipMicResp")
	proto.RegisterType((*DatingGameVipMsg)(nil), "ga.DatingGameVipMsg")
	proto.RegisterType((*SelectLikeDatingUserReq)(nil), "ga.SelectLikeDatingUserReq")
	proto.RegisterType((*SelectLikeDatingUserResp)(nil), "ga.SelectLikeDatingUserResp")
	proto.RegisterType((*OpenLikeDatingUserReq)(nil), "ga.OpenLikeDatingUserReq")
	proto.RegisterType((*OpenLikeDatingUserResp)(nil), "ga.OpenLikeDatingUserResp")
	proto.RegisterType((*GetAlreadySelectLikeDatingUserReq)(nil), "ga.GetAlreadySelectLikeDatingUserReq")
	proto.RegisterType((*GetAlreadySelectLikeDatingUserResp)(nil), "ga.GetAlreadySelectLikeDatingUserResp")
	proto.RegisterType((*LikeBeatValChangeMsg)(nil), "ga.LikeBeatValChangeMsg")
	proto.RegisterType((*DatingSelectStatusMsg)(nil), "ga.DatingSelectStatusMsg")
	proto.RegisterType((*SelectStatusInfo)(nil), "ga.SelectStatusInfo")
	proto.RegisterType((*OpenDatingLikeUserMsg)(nil), "ga.OpenDatingLikeUserMsg")
	proto.RegisterType((*DatingUserInfo)(nil), "ga.DatingUserInfo")
	proto.RegisterType((*ApplyDatingMicReq)(nil), "ga.ApplyDatingMicReq")
	proto.RegisterType((*ApplyDatingMicResp)(nil), "ga.ApplyDatingMicResp")
	proto.RegisterType((*ApplyDatingMicUserInfo)(nil), "ga.ApplyDatingMicUserInfo")
	proto.RegisterType((*GetApplyDatingMicUserListReq)(nil), "ga.GetApplyDatingMicUserListReq")
	proto.RegisterType((*GetApplyDatingMicUserListResp)(nil), "ga.GetApplyDatingMicUserListResp")
	proto.RegisterType((*ApplyDatingMicMsg)(nil), "ga.ApplyDatingMicMsg")
	proto.RegisterType((*ShowoffUserInfo)(nil), "ga.ShowoffUserInfo")
	proto.RegisterType((*DatingGameShowoff)(nil), "ga.DatingGameShowoff")
	proto.RegisterType((*GamePushLikeUserRecord)(nil), "ga.GamePushLikeUserRecord")
	proto.RegisterEnum("ga.DatingGamePhaseType", DatingGamePhaseType_name, DatingGamePhaseType_value)
	proto.RegisterEnum("ga.DatingGameLevel", DatingGameLevel_name, DatingGameLevel_value)
}
func (m *CheckChannelDatingGameEntryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckChannelDatingGameEntryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *CheckChannelDatingGameEntryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckChannelDatingGameEntryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	if m.IsOpen {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *SetDatingGamePhaseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetDatingGamePhaseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Phase))
	return i, nil
}

func (m *SetDatingGamePhaseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetDatingGamePhaseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.FromPhase))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.TargetPhase))
	return i, nil
}

func (m *DatingGamePhasePushNotifyOpt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingGamePhasePushNotifyOpt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.FromPhase))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ToPhase))
	return i, nil
}

func (m *DatingGameVipUser) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingGameVipUser) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	if m.UserProfile != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.UserProfile.Size()))
		n5, err := m.UserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *DatingGameHatUser) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingGameHatUser) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Uid))
	if m.UrlSource != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.UrlSource.Size()))
		n6, err := m.UrlSource.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x18
	i++
	if m.IsMale {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	if m.UserProfile != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.UserProfile.Size()))
		n7, err := m.UserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *RuleEntryStyle) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RuleEntryStyle) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.GameLevel))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.RulesEntryUrl)))
	i += copy(dAtA[i:], m.RulesEntryUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.StyleColor)))
	i += copy(dAtA[i:], m.StyleColor)
	return i, nil
}

func (m *GetDatingGameInitInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDatingGameInitInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n8, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetDatingGameInitInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDatingGameInitInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n9, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Phase))
	if m.VipUser != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.VipUser.Size()))
		n10, err := m.VipUser.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if len(m.HatUserList) > 0 {
		for _, msg := range m.HatUserList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintChannelDatingGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.LikeBeatList) > 0 {
		for _, msg := range m.LikeBeatList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintChannelDatingGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.OpenLikeUserList) > 0 {
		for _, msg := range m.OpenLikeUserList {
			dAtA[i] = 0x32
			i++
			i = encodeVarintChannelDatingGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ApplyMicLen))
	if len(m.StyleList) > 0 {
		for _, msg := range m.StyleList {
			dAtA[i] = 0x42
			i++
			i = encodeVarintChannelDatingGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserLikeBeatInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLikeBeatInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.LikeBeatVal))
	dAtA[i] = 0x18
	i++
	if m.SelectStatus {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AlreadyOpenLikeUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AlreadyOpenLikeUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.OpenUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.LikeUid))
	return i, nil
}

func (m *DatingGameHoldVipMicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingGameHoldVipMicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	if m.IsManual {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *DatingGameHoldVipMicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingGameHoldVipMicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *DatingGameVipMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingGameVipMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *SelectLikeDatingUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SelectLikeDatingUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n13, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *SelectLikeDatingUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SelectLikeDatingUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n14, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *OpenLikeDatingUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenLikeDatingUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n15, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.OpenUid))
	return i, nil
}

func (m *OpenLikeDatingUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenLikeDatingUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n16, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	return i, nil
}

func (m *GetAlreadySelectLikeDatingUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAlreadySelectLikeDatingUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n17, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetAlreadySelectLikeDatingUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAlreadySelectLikeDatingUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n18, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	if len(m.SelectUidList) > 0 {
		for _, num := range m.SelectUidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintChannelDatingGame_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *LikeBeatValChangeMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LikeBeatValChangeMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.LikeBeatVal))
	return i, nil
}

func (m *DatingSelectStatusMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingSelectStatusMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	if len(m.StatusInfoList) > 0 {
		for _, msg := range m.StatusInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelDatingGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SelectStatusInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SelectStatusInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.SelectStatus {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *OpenDatingLikeUserMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenDatingLikeUserMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.OpenUid))
	if m.LikeUser == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("like_user")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.LikeUser.Size()))
		n19, err := m.LikeUser.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	return i, nil
}

func (m *DatingUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Sex))
	if m.UserProfile != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.UserProfile.Size()))
		n20, err := m.UserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	return i, nil
}

func (m *ApplyDatingMicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyDatingMicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n21, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	if m.IsCancel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ApplyDatingMicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyDatingMicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n22, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x10
	i++
	if m.IsCancel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if len(m.ApplyUserList) > 0 {
		for _, msg := range m.ApplyUserList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelDatingGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ApplyDatingMicUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyDatingMicUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.NickName)))
	i += copy(dAtA[i:], m.NickName)
	if m.UserProfile != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.UserProfile.Size()))
		n23, err := m.UserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	return i, nil
}

func (m *GetApplyDatingMicUserListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyDatingMicUserListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseReq.Size()))
		n24, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetApplyDatingMicUserListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyDatingMicUserListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.BaseResp.Size()))
		n25, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	if len(m.ApplyUserList) > 0 {
		for _, msg := range m.ApplyUserList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelDatingGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ApplyDatingMicMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyDatingMicMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	if m.IsCancel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ApplyUserCount))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.EntranceAccount)))
	i += copy(dAtA[i:], m.EntranceAccount)
	if m.ApplyUserProfile != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ApplyUserProfile.Size()))
		n26, err := m.ApplyUserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	if m.EntranceUserProfile != nil {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.EntranceUserProfile.Size()))
		n27, err := m.EntranceUserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	return i, nil
}

func (m *ShowoffUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShowoffUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.Sex))
	if m.UserProfile != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.UserProfile.Size()))
		n28, err := m.UserProfile.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	return i, nil
}

func (m *DatingGameShowoff) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingGameShowoff) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.DsInfo == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ds_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.DsInfo.Size()))
		n29, err := m.DsInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.ShowoffImageUrl)))
	i += copy(dAtA[i:], m.ShowoffImageUrl)
	if len(m.ShowoffUinfos) > 0 {
		for _, msg := range m.ShowoffUinfos {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelDatingGame_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(m.ServerTime))
	return i, nil
}

func (m *GamePushLikeUserRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GamePushLikeUserRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.ShowoffImageUrl)))
	i += copy(dAtA[i:], m.ShowoffImageUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.DescFirst)))
	i += copy(dAtA[i:], m.DescFirst)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.DescLast)))
	i += copy(dAtA[i:], m.DescLast)
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelDatingGame_(dAtA, i, uint64(len(m.ShowoffFullImageUrl)))
	i += copy(dAtA[i:], m.ShowoffFullImageUrl)
	return i, nil
}

func encodeFixed64ChannelDatingGame_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32ChannelDatingGame_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelDatingGame_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CheckChannelDatingGameEntryReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	return n
}

func (m *CheckChannelDatingGameEntryResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 2
	n += 1 + sovChannelDatingGame_(uint64(m.Level))
	return n
}

func (m *SetDatingGamePhaseReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGame_(uint64(m.Phase))
	return n
}

func (m *SetDatingGamePhaseResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGame_(uint64(m.FromPhase))
	n += 1 + sovChannelDatingGame_(uint64(m.TargetPhase))
	return n
}

func (m *DatingGamePhasePushNotifyOpt) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGame_(uint64(m.FromPhase))
	n += 1 + sovChannelDatingGame_(uint64(m.ToPhase))
	return n
}

func (m *DatingGameVipUser) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	if m.UserProfile != nil {
		l = m.UserProfile.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	return n
}

func (m *DatingGameHatUser) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.Uid))
	if m.UrlSource != nil {
		l = m.UrlSource.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 2
	l = len(m.Account)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	if m.UserProfile != nil {
		l = m.UserProfile.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	return n
}

func (m *RuleEntryStyle) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.GameLevel))
	l = len(m.RulesEntryUrl)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	l = len(m.StyleColor)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	return n
}

func (m *GetDatingGameInitInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	return n
}

func (m *GetDatingGameInitInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.Phase))
	if m.VipUser != nil {
		l = m.VipUser.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	if len(m.HatUserList) > 0 {
		for _, e := range m.HatUserList {
			l = e.Size()
			n += 1 + l + sovChannelDatingGame_(uint64(l))
		}
	}
	if len(m.LikeBeatList) > 0 {
		for _, e := range m.LikeBeatList {
			l = e.Size()
			n += 1 + l + sovChannelDatingGame_(uint64(l))
		}
	}
	if len(m.OpenLikeUserList) > 0 {
		for _, e := range m.OpenLikeUserList {
			l = e.Size()
			n += 1 + l + sovChannelDatingGame_(uint64(l))
		}
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ApplyMicLen))
	if len(m.StyleList) > 0 {
		for _, e := range m.StyleList {
			l = e.Size()
			n += 1 + l + sovChannelDatingGame_(uint64(l))
		}
	}
	return n
}

func (m *UserLikeBeatInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.Uid))
	n += 1 + sovChannelDatingGame_(uint64(m.LikeBeatVal))
	n += 2
	return n
}

func (m *AlreadyOpenLikeUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.OpenUid))
	n += 1 + sovChannelDatingGame_(uint64(m.LikeUid))
	return n
}

func (m *DatingGameHoldVipMicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 2
	return n
}

func (m *DatingGameHoldVipMicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	return n
}

func (m *DatingGameVipMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGame_(uint64(m.Uid))
	return n
}

func (m *SelectLikeDatingUserReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGame_(uint64(m.TargetUid))
	return n
}

func (m *SelectLikeDatingUserResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.TargetUid))
	return n
}

func (m *OpenLikeDatingUserReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGame_(uint64(m.OpenUid))
	return n
}

func (m *OpenLikeDatingUserResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	return n
}

func (m *GetAlreadySelectLikeDatingUserReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	return n
}

func (m *GetAlreadySelectLikeDatingUserResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	if len(m.SelectUidList) > 0 {
		for _, e := range m.SelectUidList {
			n += 1 + sovChannelDatingGame_(uint64(e))
		}
	}
	return n
}

func (m *LikeBeatValChangeMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGame_(uint64(m.Uid))
	n += 1 + sovChannelDatingGame_(uint64(m.LikeBeatVal))
	return n
}

func (m *DatingSelectStatusMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	if len(m.StatusInfoList) > 0 {
		for _, e := range m.StatusInfoList {
			l = e.Size()
			n += 1 + l + sovChannelDatingGame_(uint64(l))
		}
	}
	return n
}

func (m *SelectStatusInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.Uid))
	n += 2
	return n
}

func (m *OpenDatingLikeUserMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 1 + sovChannelDatingGame_(uint64(m.OpenUid))
	if m.LikeUser != nil {
		l = m.LikeUser.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	return n
}

func (m *DatingUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	n += 1 + sovChannelDatingGame_(uint64(m.Sex))
	if m.UserProfile != nil {
		l = m.UserProfile.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	return n
}

func (m *ApplyDatingMicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 2
	return n
}

func (m *ApplyDatingMicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 2
	if len(m.ApplyUserList) > 0 {
		for _, e := range m.ApplyUserList {
			l = e.Size()
			n += 1 + l + sovChannelDatingGame_(uint64(l))
		}
	}
	return n
}

func (m *ApplyDatingMicUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	n += 1 + sovChannelDatingGame_(uint64(m.Sex))
	l = len(m.NickName)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	if m.UserProfile != nil {
		l = m.UserProfile.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	return n
}

func (m *GetApplyDatingMicUserListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	return n
}

func (m *GetApplyDatingMicUserListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	if len(m.ApplyUserList) > 0 {
		for _, e := range m.ApplyUserList {
			l = e.Size()
			n += 1 + l + sovChannelDatingGame_(uint64(l))
		}
	}
	return n
}

func (m *ApplyDatingMicMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.ChannelId))
	n += 2
	l = len(m.Account)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	n += 1 + sovChannelDatingGame_(uint64(m.ApplyUserCount))
	l = len(m.EntranceAccount)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	if m.ApplyUserProfile != nil {
		l = m.ApplyUserProfile.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	if m.EntranceUserProfile != nil {
		l = m.EntranceUserProfile.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	return n
}

func (m *ShowoffUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelDatingGame_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	n += 1 + sovChannelDatingGame_(uint64(m.Sex))
	if m.UserProfile != nil {
		l = m.UserProfile.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	return n
}

func (m *DatingGameShowoff) Size() (n int) {
	var l int
	_ = l
	if m.DsInfo != nil {
		l = m.DsInfo.Size()
		n += 1 + l + sovChannelDatingGame_(uint64(l))
	}
	l = len(m.ShowoffImageUrl)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	if len(m.ShowoffUinfos) > 0 {
		for _, e := range m.ShowoffUinfos {
			l = e.Size()
			n += 1 + l + sovChannelDatingGame_(uint64(l))
		}
	}
	n += 1 + sovChannelDatingGame_(uint64(m.ServerTime))
	return n
}

func (m *GamePushLikeUserRecord) Size() (n int) {
	var l int
	_ = l
	l = len(m.ShowoffImageUrl)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	l = len(m.DescFirst)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	l = len(m.DescLast)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	l = len(m.ShowoffFullImageUrl)
	n += 1 + l + sovChannelDatingGame_(uint64(l))
	return n
}

func sovChannelDatingGame_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelDatingGame_(x uint64) (n int) {
	return sovChannelDatingGame_(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *CheckChannelDatingGameEntryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckChannelDatingGameEntryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckChannelDatingGameEntryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckChannelDatingGameEntryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckChannelDatingGameEntryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckChannelDatingGameEntryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOpen", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOpen = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_open")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetDatingGamePhaseReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetDatingGamePhaseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetDatingGamePhaseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phase", wireType)
			}
			m.Phase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Phase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("phase")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetDatingGamePhaseResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetDatingGamePhaseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetDatingGamePhaseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromPhase", wireType)
			}
			m.FromPhase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromPhase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetPhase", wireType)
			}
			m.TargetPhase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetPhase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("from_phase")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_phase")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingGamePhasePushNotifyOpt) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingGamePhasePushNotifyOpt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingGamePhasePushNotifyOpt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromPhase", wireType)
			}
			m.FromPhase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromPhase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToPhase", wireType)
			}
			m.ToPhase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToPhase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("from_phase")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("to_phase")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingGameVipUser) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingGameVipUser: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingGameVipUser: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserProfile == nil {
				m.UserProfile = &ga.UserProfile{}
			}
			if err := m.UserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingGameHatUser) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingGameHatUser: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingGameHatUser: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UrlSource", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UrlSource == nil {
				m.UrlSource = &ga.DownloadSourceInfo{}
			}
			if err := m.UrlSource.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMale", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMale = bool(v != 0)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserProfile == nil {
				m.UserProfile = &ga.UserProfile{}
			}
			if err := m.UserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RuleEntryStyle) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RuleEntryStyle: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RuleEntryStyle: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameLevel", wireType)
			}
			m.GameLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RulesEntryUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RulesEntryUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StyleColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StyleColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_level")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("rules_entry_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("style_color")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDatingGameInitInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDatingGameInitInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDatingGameInitInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDatingGameInitInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDatingGameInitInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDatingGameInitInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phase", wireType)
			}
			m.Phase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Phase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VipUser", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VipUser == nil {
				m.VipUser = &DatingGameVipUser{}
			}
			if err := m.VipUser.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HatUserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HatUserList = append(m.HatUserList, &DatingGameHatUser{})
			if err := m.HatUserList[len(m.HatUserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeBeatList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LikeBeatList = append(m.LikeBeatList, &UserLikeBeatInfo{})
			if err := m.LikeBeatList[len(m.LikeBeatList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenLikeUserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpenLikeUserList = append(m.OpenLikeUserList, &AlreadyOpenLikeUserInfo{})
			if err := m.OpenLikeUserList[len(m.OpenLikeUserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyMicLen", wireType)
			}
			m.ApplyMicLen = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyMicLen |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StyleList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StyleList = append(m.StyleList, &RuleEntryStyle{})
			if err := m.StyleList[len(m.StyleList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("phase")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLikeBeatInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLikeBeatInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLikeBeatInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeBeatVal", wireType)
			}
			m.LikeBeatVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeBeatVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SelectStatus", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SelectStatus = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("like_beat_val")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AlreadyOpenLikeUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AlreadyOpenLikeUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AlreadyOpenLikeUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenUid", wireType)
			}
			m.OpenUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpenUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUid", wireType)
			}
			m.LikeUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("open_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("like_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingGameHoldVipMicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingGameHoldVipMicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingGameHoldVipMicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsManual", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsManual = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_manual")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingGameHoldVipMicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingGameHoldVipMicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingGameHoldVipMicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingGameVipMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingGameVipMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingGameVipMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SelectLikeDatingUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SelectLikeDatingUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SelectLikeDatingUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SelectLikeDatingUserResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SelectLikeDatingUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SelectLikeDatingUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenLikeDatingUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OpenLikeDatingUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OpenLikeDatingUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenUid", wireType)
			}
			m.OpenUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpenUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("open_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenLikeDatingUserResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OpenLikeDatingUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OpenLikeDatingUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAlreadySelectLikeDatingUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAlreadySelectLikeDatingUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAlreadySelectLikeDatingUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAlreadySelectLikeDatingUserResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAlreadySelectLikeDatingUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAlreadySelectLikeDatingUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelDatingGame_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SelectUidList = append(m.SelectUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelDatingGame_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelDatingGame_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelDatingGame_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SelectUidList = append(m.SelectUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SelectUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LikeBeatValChangeMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LikeBeatValChangeMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LikeBeatValChangeMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeBeatVal", wireType)
			}
			m.LikeBeatVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeBeatVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("like_beat_val")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingSelectStatusMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingSelectStatusMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingSelectStatusMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatusInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StatusInfoList = append(m.StatusInfoList, &SelectStatusInfo{})
			if err := m.StatusInfoList[len(m.StatusInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SelectStatusInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SelectStatusInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SelectStatusInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SelectStatus", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SelectStatus = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("select_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenDatingLikeUserMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OpenDatingLikeUserMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OpenDatingLikeUserMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenUid", wireType)
			}
			m.OpenUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpenUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUser", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.LikeUser == nil {
				m.LikeUser = &DatingUserInfo{}
			}
			if err := m.LikeUser.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("open_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("like_user")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserProfile == nil {
				m.UserProfile = &ga.UserProfile{}
			}
			if err := m.UserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("sex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyDatingMicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ApplyDatingMicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ApplyDatingMicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCancel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCancel = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_cancel")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyDatingMicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ApplyDatingMicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ApplyDatingMicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCancel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCancel = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyUserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyUserList = append(m.ApplyUserList, &ApplyDatingMicUserInfo{})
			if err := m.ApplyUserList[len(m.ApplyUserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_cancel")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyDatingMicUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ApplyDatingMicUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ApplyDatingMicUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NickName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NickName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserProfile == nil {
				m.UserProfile = &ga.UserProfile{}
			}
			if err := m.UserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nick_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyDatingMicUserListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyDatingMicUserListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyDatingMicUserListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyDatingMicUserListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyDatingMicUserListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyDatingMicUserListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyUserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ApplyUserList = append(m.ApplyUserList, &ApplyDatingMicUserInfo{})
			if err := m.ApplyUserList[len(m.ApplyUserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyDatingMicMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ApplyDatingMicMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ApplyDatingMicMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCancel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCancel = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyUserCount", wireType)
			}
			m.ApplyUserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyUserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntranceAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EntranceAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyUserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ApplyUserProfile == nil {
				m.ApplyUserProfile = &ga.UserProfile{}
			}
			if err := m.ApplyUserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EntranceUserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.EntranceUserProfile == nil {
				m.EntranceUserProfile = &ga.UserProfile{}
			}
			if err := m.EntranceUserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_cancel")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("apply_user_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShowoffUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ShowoffUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ShowoffUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserProfile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserProfile == nil {
				m.UserProfile = &ga.UserProfile{}
			}
			if err := m.UserProfile.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("sex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingGameShowoff) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingGameShowoff: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingGameShowoff: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DsInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.DsInfo == nil {
				m.DsInfo = &ga.DownloadSourceInfo{}
			}
			if err := m.DsInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowoffImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ShowoffImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowoffUinfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ShowoffUinfos = append(m.ShowoffUinfos, &ShowoffUserInfo{})
			if err := m.ShowoffUinfos[len(m.ShowoffUinfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerTime", wireType)
			}
			m.ServerTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("ds_info")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("showoff_image_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("server_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GamePushLikeUserRecord) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GamePushLikeUserRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GamePushLikeUserRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowoffImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ShowoffImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DescFirst", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DescFirst = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DescLast", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DescLast = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowoffFullImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ShowoffFullImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelDatingGame_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelDatingGame_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("showoff_image_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("desc_first")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("desc_last")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("showoff_full_image_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelDatingGame_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelDatingGame_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelDatingGame_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelDatingGame_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelDatingGame_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelDatingGame_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelDatingGame_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelDatingGame_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("channel_dating_game_.proto", fileDescriptorChannelDatingGame_) }

var fileDescriptorChannelDatingGame_ = []byte{
	// 1734 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x58, 0x4f, 0x6f, 0xdb, 0xca,
	0x11, 0x37, 0x25, 0xdb, 0x92, 0x46, 0x96, 0xc5, 0xd0, 0xb1, 0xac, 0x38, 0x8e, 0xe3, 0xd0, 0x6d,
	0xea, 0x04, 0x85, 0x9d, 0x1a, 0xc8, 0x21, 0x01, 0x5a, 0x40, 0x96, 0x69, 0x5b, 0x85, 0x6c, 0x09,
	0x94, 0x65, 0xa0, 0xed, 0x81, 0xa0, 0xa9, 0x95, 0xc4, 0x7a, 0x45, 0x32, 0xfc, 0xe3, 0x54, 0x40,
	0x8b, 0x16, 0x28, 0x8a, 0xa2, 0x2d, 0x10, 0xf4, 0x2b, 0xf4, 0xd2, 0x4f, 0x50, 0xb4, 0xc7, 0x5e,
	0x53, 0x14, 0x05, 0x7a, 0xed, 0xa5, 0x08, 0xd2, 0x0f, 0xd1, 0xeb, 0xc3, 0xee, 0x92, 0x12, 0x49,
	0xd1, 0x8e, 0xf4, 0x9e, 0xf3, 0x80, 0x77, 0x93, 0x66, 0x66, 0xe7, 0x37, 0xb3, 0x33, 0xf3, 0xdb,
	0x5d, 0xc2, 0xba, 0xd6, 0x57, 0x0d, 0x03, 0x61, 0xa5, 0xa3, 0xba, 0xba, 0xd1, 0x53, 0x7a, 0xea,
	0x00, 0x29, 0xbb, 0x96, 0x6d, 0xba, 0xa6, 0x90, 0xea, 0xa9, 0xeb, 0x85, 0x9e, 0xaa, 0x5c, 0xaa,
	0x0e, 0x62, 0x22, 0x71, 0x00, 0x9b, 0xd5, 0x3e, 0xd2, 0xae, 0xaa, 0x6c, 0xd5, 0x21, 0x5d, 0x74,
	0xac, 0x0e, 0x90, 0x64, 0xb8, 0xf6, 0x50, 0x46, 0x6f, 0x84, 0xa7, 0x90, 0x25, 0xf6, 0x8a, 0x8d,
	0xde, 0x94, 0xb9, 0xad, 0xd4, 0x4e, 0x7e, 0x3f, 0xbf, 0xdb, 0x53, 0x77, 0x0f, 0x54, 0x07, 0xc9,
	0xe8, 0x8d, 0x9c, 0xb9, 0x64, 0x3f, 0x84, 0x6d, 0x80, 0x00, 0x5a, 0xef, 0x94, 0x53, 0x5b, 0xa9,
	0x9d, 0xc2, 0xc1, 0xfc, 0xfb, 0xff, 0x3e, 0x9e, 0x93, 0x73, 0xbe, 0xbc, 0xd6, 0x11, 0x7f, 0xcb,
	0xc1, 0xe3, 0x5b, 0xf1, 0x1c, 0x4b, 0x78, 0x06, 0x39, 0x1f, 0xd0, 0xb1, 0x7c, 0xc4, 0xa5, 0x31,
	0xa2, 0x63, 0xc9, 0xd9, 0x4b, 0xff, 0x97, 0xf0, 0x08, 0x32, 0xba, 0xa3, 0x98, 0x16, 0x32, 0x28,
	0x60, 0xd6, 0x07, 0x5c, 0xd4, 0x9d, 0x86, 0x85, 0x0c, 0x61, 0x1d, 0x16, 0x30, 0xba, 0x46, 0xb8,
	0x9c, 0xde, 0xe2, 0x46, 0xd1, 0x30, 0x91, 0xf8, 0x2b, 0x0e, 0x56, 0x5b, 0xc8, 0x1d, 0x07, 0xd0,
	0xec, 0xfb, 0x89, 0xdc, 0x65, 0xc2, 0x24, 0x04, 0x8b, 0x38, 0x2e, 0xa7, 0x43, 0x7a, 0x26, 0x12,
	0xff, 0xca, 0x41, 0x29, 0x29, 0x84, 0xd9, 0xf6, 0x60, 0xaa, 0x30, 0xb6, 0x01, 0xba, 0xb6, 0x39,
	0x50, 0x26, 0x63, 0xc9, 0x11, 0x39, 0x05, 0x16, 0xbe, 0x03, 0x4b, 0xae, 0x6a, 0xf7, 0x90, 0xeb,
	0x9b, 0xcd, 0x87, 0xcc, 0xf2, 0x4c, 0x43, 0x0d, 0xc5, 0xdf, 0x71, 0xb0, 0x11, 0x8b, 0xba, 0xe9,
	0x39, 0xfd, 0x33, 0xd3, 0xd5, 0xbb, 0xc3, 0x86, 0xe5, 0xc6, 0x62, 0xe2, 0xa6, 0x89, 0x29, 0x95,
	0x1c, 0xd3, 0x63, 0xc8, 0xba, 0x66, 0x42, 0xd8, 0x19, 0xd7, 0x64, 0xb1, 0xfc, 0x89, 0x83, 0x7b,
	0xe3, 0x58, 0x2e, 0x74, 0xab, 0xed, 0x20, 0x5b, 0x28, 0x41, 0xda, 0x8b, 0x21, 0x13, 0x81, 0xb0,
	0x09, 0x19, 0x55, 0xd3, 0x4c, 0xcf, 0x70, 0x29, 0x60, 0x2e, 0xf0, 0xe6, 0x0b, 0x85, 0x2d, 0xc8,
	0x1a, 0xba, 0x76, 0x65, 0xa8, 0x03, 0x06, 0x17, 0x18, 0x8c, 0xa4, 0xc2, 0x3e, 0x2c, 0x79, 0x0e,
	0xb2, 0x15, 0xcb, 0x36, 0xbb, 0x3a, 0x26, 0x9b, 0xc4, 0xed, 0xe4, 0xf7, 0x8b, 0xa4, 0x38, 0x04,
	0xb9, 0xc9, 0xc4, 0x72, 0xde, 0x1b, 0xff, 0x11, 0xff, 0x1f, 0x89, 0xf1, 0x44, 0x75, 0x6f, 0x8d,
	0xf1, 0x25, 0x80, 0x67, 0x63, 0xc5, 0x31, 0x3d, 0x5b, 0x23, 0xfb, 0x42, 0xfc, 0x97, 0x88, 0xff,
	0x43, 0xf3, 0xad, 0x81, 0x4d, 0xb5, 0xd3, 0xa2, 0x9a, 0x9a, 0xd1, 0x35, 0xe5, 0x9c, 0x67, 0x63,
	0xf6, 0xd7, 0x9f, 0x85, 0x81, 0x8a, 0x11, 0x6d, 0xf7, 0xd0, 0x2c, 0x9c, 0xaa, 0x18, 0x85, 0x33,
	0x9f, 0xff, 0x54, 0xe6, 0x0b, 0x53, 0x65, 0xbe, 0x38, 0x45, 0xe6, 0xbf, 0xe7, 0x60, 0x59, 0xf6,
	0x30, 0x9b, 0xee, 0x96, 0x3b, 0xc4, 0x88, 0x94, 0x9d, 0x72, 0x12, 0x9b, 0xcc, 0x48, 0x6f, 0x10,
	0x79, 0x9d, 0x88, 0x85, 0xef, 0x42, 0xd1, 0xf6, 0x30, 0x72, 0x14, 0x44, 0x16, 0x2a, 0x9e, 0x8d,
	0x23, 0xf5, 0x2a, 0x50, 0x25, 0x75, 0xda, 0xb6, 0xb1, 0xf0, 0x6d, 0xc8, 0x3b, 0xc4, 0xb7, 0xa2,
	0x99, 0xd8, 0xb4, 0x23, 0x85, 0x03, 0xaa, 0xa8, 0x12, 0xb9, 0xd8, 0x83, 0xf2, 0x71, 0x78, 0xdc,
	0x6a, 0x86, 0xee, 0xd2, 0x5d, 0xbc, 0x6b, 0x96, 0xfb, 0x4b, 0x1a, 0x1e, 0xdc, 0x80, 0x34, 0xdb,
	0x6c, 0x8f, 0xd8, 0x23, 0x35, 0xc1, 0x1e, 0xc2, 0x0b, 0xc8, 0x5e, 0xeb, 0x96, 0x42, 0x76, 0x9b,
	0x16, 0x3c, 0xbf, 0xbf, 0x4a, 0x9b, 0x24, 0x3e, 0x0b, 0x72, 0xe6, 0xda, 0x1f, 0x8a, 0x57, 0x50,
	0xe8, 0xab, 0x2e, 0x5d, 0xa1, 0x60, 0xdd, 0x21, 0x8d, 0x90, 0x9e, 0x5c, 0xe6, 0xb7, 0xa7, 0x9c,
	0xef, 0xb3, 0x1f, 0x75, 0xdd, 0x71, 0x85, 0xd7, 0xb0, 0x8c, 0xf5, 0x2b, 0xa4, 0x5c, 0x22, 0xd5,
	0x65, 0x6b, 0x17, 0xe8, 0xda, 0xfb, 0x41, 0xf5, 0xeb, 0xfa, 0x15, 0x3a, 0x40, 0x2a, 0xcb, 0x72,
	0x09, 0xfb, 0xff, 0xe8, 0xda, 0x1f, 0xc2, 0x0a, 0x61, 0x68, 0x85, 0x3a, 0x18, 0x83, 0x2f, 0x52,
	0x07, 0x0f, 0x89, 0x83, 0x0a, 0xb6, 0x91, 0xda, 0x19, 0x12, 0xce, 0x26, 0x7e, 0x88, 0x3f, 0xea,
	0x87, 0x37, 0x43, 0x12, 0xea, 0x6b, 0x07, 0x0a, 0xaa, 0x65, 0xe1, 0xa1, 0x32, 0xd0, 0x35, 0x05,
	0x23, 0xa3, 0x9c, 0x09, 0x31, 0x7b, 0x9e, 0xaa, 0x4e, 0x75, 0xad, 0x8e, 0x0c, 0xe1, 0x7b, 0xc0,
	0x4a, 0xcf, 0xc0, 0xb2, 0x14, 0x4c, 0x20, 0x60, 0xd1, 0x76, 0x94, 0x73, 0xd4, 0x8a, 0x38, 0x17,
	0x7f, 0x09, 0x7c, 0x3c, 0x95, 0x1b, 0x87, 0x74, 0x07, 0x0a, 0xe3, 0x0d, 0xb9, 0x56, 0x71, 0xa4,
	0x42, 0xf9, 0x20, 0xff, 0x0b, 0x15, 0x0b, 0xcf, 0xa0, 0xe0, 0x20, 0x8c, 0x34, 0x57, 0x71, 0x5c,
	0xd5, 0xf5, 0x9c, 0xc8, 0x74, 0x2e, 0x31, 0x55, 0x8b, 0x6a, 0xc4, 0x9f, 0xc0, 0xda, 0x0d, 0x5b,
	0x41, 0x78, 0x90, 0x6e, 0x62, 0x3c, 0x98, 0x0c, 0x91, 0xb6, 0xf5, 0x0e, 0x31, 0x60, 0x1b, 0x1c,
	0x6b, 0xcb, 0x0c, 0x91, 0xb6, 0xf5, 0x0e, 0x21, 0xed, 0xb5, 0x50, 0x95, 0x4d, 0xdc, 0xb9, 0xd0,
	0xad, 0x53, 0x5d, 0xbb, 0xf3, 0x23, 0xef, 0x09, 0xe4, 0x28, 0x11, 0x19, 0x9e, 0x8a, 0xe9, 0x2c,
	0x06, 0xc9, 0x66, 0x09, 0x15, 0x11, 0xa9, 0x28, 0x41, 0x39, 0x39, 0x94, 0x99, 0xc6, 0x43, 0x6c,
	0x00, 0x1f, 0x69, 0xf7, 0x53, 0xa7, 0x37, 0xdd, 0xd1, 0xe3, 0x57, 0x35, 0x15, 0xab, 0xaa, 0xf8,
	0x07, 0x0e, 0xd6, 0x5a, 0xb4, 0x22, 0x64, 0xf3, 0x99, 0x6f, 0x3a, 0x0c, 0x77, 0xbd, 0x47, 0xdb,
	0x00, 0xfe, 0x51, 0x4b, 0xe2, 0x88, 0x9c, 0xc7, 0x4c, 0x4e, 0x2a, 0xf6, 0x53, 0x28, 0x27, 0x07,
	0x33, 0xf3, 0x05, 0x21, 0x84, 0x95, 0x4a, 0xc6, 0xfa, 0x0d, 0x07, 0xab, 0x41, 0xd3, 0x7d, 0xc6,
	0xbc, 0xc3, 0x6d, 0x9c, 0x4e, 0x68, 0x63, 0xb1, 0x0a, 0xa5, 0xa4, 0x30, 0x66, 0xeb, 0x0b, 0x0b,
	0x9e, 0x1c, 0x23, 0xd7, 0x1f, 0xa5, 0xaf, 0xa3, 0x9e, 0xe2, 0x5b, 0x10, 0x3f, 0x85, 0x38, 0x5b,
	0xd1, 0x9e, 0x42, 0xd1, 0x67, 0x0d, 0x4f, 0xef, 0x30, 0x0e, 0x4b, 0x6d, 0xa5, 0x77, 0x0a, 0xb2,
	0x4f, 0x26, 0x6d, 0xbd, 0x43, 0x39, 0xeb, 0x17, 0x70, 0xbf, 0x3e, 0x26, 0x1b, 0x72, 0xab, 0xee,
	0xa1, 0xaf, 0x3a, 0x06, 0x93, 0xe4, 0x96, 0xbe, 0x81, 0xdc, 0xc4, 0x9f, 0xc3, 0x2a, 0xcb, 0xb1,
	0x15, 0xe2, 0xb1, 0xa9, 0xf1, 0x7f, 0x00, 0x3c, 0xe3, 0x44, 0x45, 0x37, 0xba, 0xe6, 0x38, 0x4b,
	0xff, 0x5c, 0x09, 0xfb, 0xa4, 0xe7, 0xc1, 0xb2, 0x33, 0xfa, 0x4d, 0x93, 0x6f, 0x03, 0x1f, 0xb7,
	0xb9, 0x91, 0xb0, 0x27, 0x68, 0x38, 0xfc, 0x60, 0x88, 0xd2, 0xf0, 0x3b, 0x7f, 0x16, 0x58, 0x66,
	0x01, 0x0d, 0x4f, 0x9d, 0x55, 0xb8, 0xc7, 0x53, 0x49, 0x54, 0xbd, 0x07, 0xb9, 0xd1, 0x59, 0x48,
	0xb7, 0xd6, 0x3f, 0x99, 0xc6, 0xdd, 0x42, 0xb3, 0xa5, 0x7c, 0x4e, 0xfe, 0x91, 0x87, 0xc2, 0x72,
	0x54, 0xf9, 0x19, 0x2f, 0xb8, 0x25, 0x48, 0x3b, 0xe8, 0x67, 0x91, 0xcb, 0x3f, 0x11, 0x4c, 0x5c,
	0xff, 0x16, 0xa6, 0xb8, 0xfe, 0xfd, 0x9a, 0x83, 0x7b, 0x15, 0x72, 0x28, 0xb3, 0xe8, 0x3f, 0xdf,
	0x69, 0xa3, 0xa9, 0x86, 0x86, 0x26, 0x4e, 0x9b, 0x2a, 0x95, 0x8a, 0x7f, 0xe6, 0x40, 0x88, 0x47,
	0x31, 0xdb, 0x34, 0x46, 0x40, 0x52, 0x49, 0x20, 0xc2, 0x01, 0x14, 0xd9, 0xcd, 0x64, 0x7c, 0xc3,
	0x49, 0xd3, 0x56, 0x5e, 0xa7, 0x37, 0x9c, 0x08, 0xfc, 0xa8, 0xc4, 0xec, 0x32, 0x13, 0xdc, 0x6e,
	0xc4, 0xbf, 0x73, 0x50, 0x4a, 0xb6, 0xfc, 0xd2, 0xf5, 0xf6, 0xab, 0x99, 0x8e, 0x57, 0xf3, 0x09,
	0xe4, 0x48, 0xc5, 0x15, 0xda, 0x08, 0xf3, 0xf1, 0x46, 0x38, 0x4b, 0xba, 0xef, 0x4f, 0x53, 0xf0,
	0x2b, 0xd8, 0x20, 0x3c, 0x38, 0x91, 0x03, 0x49, 0xef, 0xce, 0x49, 0xf7, 0x1d, 0x07, 0x8f, 0x6e,
	0x41, 0x9b, 0xad, 0xc4, 0x09, 0xf5, 0x4b, 0xcd, 0x5a, 0xbf, 0x0f, 0xa9, 0x78, 0xbb, 0x4f, 0x4d,
	0x1a, 0x53, 0x74, 0x58, 0xa8, 0xd4, 0xe9, 0xa4, 0x52, 0xef, 0x02, 0x1f, 0xca, 0x60, 0xfc, 0xd4,
	0x0b, 0xd0, 0x96, 0x47, 0xc1, 0x56, 0xa9, 0xfd, 0x1e, 0xf0, 0xe4, 0x75, 0x45, 0x9c, 0x2b, 0x81,
	0x63, 0x52, 0xe3, 0xc0, 0x71, 0x31, 0xd0, 0x56, 0x7c, 0x80, 0xef, 0x83, 0x10, 0x02, 0xf8, 0xc4,
	0x33, 0x90, 0x1f, 0xa1, 0xf9, 0x12, 0xa1, 0x0a, 0xab, 0x23, 0xbc, 0x88, 0x87, 0x4c, 0xb2, 0x87,
	0x95, 0xc0, 0x3a, 0x24, 0x14, 0xff, 0xc6, 0x41, 0xb1, 0xd5, 0x37, 0xdf, 0x9a, 0xdd, 0xee, 0x37,
	0x8c, 0x0b, 0xff, 0x13, 0xf9, 0x08, 0xe0, 0xe7, 0x20, 0xec, 0x41, 0xa6, 0xc3, 0x8e, 0x3f, 0xbf,
	0x3f, 0x6f, 0x7a, 0xe9, 0x2f, 0x76, 0xd8, 0xf9, 0xf6, 0x02, 0xee, 0x39, 0x6c, 0xad, 0xa2, 0x0f,
	0xd4, 0x1e, 0x9a, 0x78, 0x1b, 0x17, 0x7d, 0x75, 0x8d, 0x68, 0xc9, 0xeb, 0xf8, 0x35, 0x2c, 0x07,
	0x2b, 0x3c, 0x02, 0xe4, 0xf8, 0xc4, 0xb4, 0x42, 0xcf, 0xd8, 0xe8, 0x5e, 0xca, 0x05, 0xdf, 0xb4,
	0x4d, 0x2d, 0xe9, 0xcb, 0x1a, 0xd9, 0xd7, 0xc8, 0x56, 0x5c, 0x7d, 0x10, 0xfd, 0x22, 0x04, 0x4c,
	0x71, 0xae, 0x0f, 0x90, 0xf8, 0x2f, 0x0e, 0x4a, 0xf4, 0x53, 0x90, 0xe7, 0xf4, 0x83, 0xf3, 0x52,
	0x46, 0x9a, 0x69, 0x77, 0x92, 0xe3, 0xe5, 0x6e, 0x8b, 0x77, 0x1b, 0xa0, 0x83, 0x1c, 0x4d, 0xe9,
	0xea, 0xb6, 0x13, 0xad, 0x5c, 0x8e, 0xc8, 0x8f, 0x88, 0x98, 0xcc, 0x0b, 0x35, 0xc2, 0xaa, 0x13,
	0x1d, 0x87, 0x2c, 0x11, 0xd7, 0x55, 0xc7, 0x15, 0x5e, 0x41, 0x29, 0x40, 0xee, 0x7a, 0x18, 0x87,
	0xe0, 0xc3, 0x7c, 0xb7, 0xe2, 0xdb, 0x1c, 0x79, 0x18, 0x07, 0x21, 0x3c, 0xff, 0x27, 0x07, 0x2b,
	0xb1, 0x0f, 0x5c, 0xe7, 0x43, 0x0b, 0x09, 0x8f, 0xe0, 0xc1, 0x61, 0xe5, 0xbc, 0x76, 0x76, 0xac,
	0x1c, 0x57, 0x4e, 0x25, 0xa5, 0x79, 0x52, 0x69, 0x49, 0x4a, 0xed, 0xec, 0xa2, 0x52, 0xaf, 0x1d,
	0xf2, 0x73, 0xc2, 0x03, 0x58, 0x9d, 0x54, 0x1f, 0xd5, 0xce, 0x78, 0x4e, 0xd8, 0x82, 0x8d, 0x49,
	0xd5, 0x61, 0xad, 0x55, 0x6d, 0xb7, 0x5a, 0xb5, 0xc6, 0x19, 0x9f, 0x12, 0x36, 0xa0, 0x3c, 0x69,
	0x51, 0x3d, 0x69, 0xd4, 0xaa, 0x12, 0x9f, 0x4e, 0x46, 0x6e, 0xb6, 0x0f, 0xea, 0xb5, 0xd6, 0x09,
	0x3f, 0x2f, 0x3c, 0x84, 0xb5, 0x84, 0xc5, 0xf5, 0x46, 0x4b, 0xe2, 0x17, 0x9e, 0xff, 0x83, 0x83,
	0xe2, 0x38, 0x1b, 0xf6, 0x81, 0x65, 0x1d, 0x4a, 0xe1, 0x05, 0x75, 0xe9, 0x42, 0xaa, 0x2b, 0x47,
	0xb2, 0x24, 0xf1, 0x73, 0xf1, 0x48, 0x98, 0xae, 0x25, 0x9d, 0xd5, 0x1a, 0x32, 0xcf, 0xc5, 0x23,
	0x61, 0xda, 0x53, 0xe9, 0xb0, 0x56, 0x39, 0x97, 0xf8, 0x54, 0xb2, 0xba, 0x29, 0xd7, 0x4e, 0x2b,
	0xf2, 0x8f, 0xf8, 0x74, 0x7c, 0x1f, 0x02, 0xb5, 0x74, 0x24, 0x55, 0xcf, 0x95, 0x6a, 0x93, 0x9f,
	0x4f, 0x46, 0x3f, 0x6f, 0x34, 0xa5, 0xd6, 0x39, 0xbf, 0x70, 0xd0, 0x7c, 0xff, 0x71, 0x93, 0xfb,
	0xf7, 0xc7, 0x4d, 0xee, 0xc3, 0xc7, 0x4d, 0xee, 0x8f, 0xff, 0xdb, 0x9c, 0x83, 0xb2, 0x66, 0x0e,
	0x76, 0x87, 0xfa, 0xd0, 0xf4, 0x48, 0x3f, 0x0f, 0xcc, 0x0e, 0xc2, 0xec, 0xdb, 0xf6, 0x8f, 0xbf,
	0xd5, 0x33, 0xb1, 0x6a, 0xf4, 0x76, 0x5f, 0xee, 0xbb, 0xee, 0xae, 0x66, 0x0e, 0xf6, 0xa8, 0x58,
	0x33, 0xf1, 0x9e, 0x6a, 0x59, 0x7b, 0x3e, 0xf7, 0x7e, 0x11, 0x00, 0x00, 0xff, 0xff, 0xa7, 0x67,
	0x64, 0x5f, 0x31, 0x17, 0x00, 0x00,
}
