// Code generated by protoc-gen-gogo.
// source: channel_personalization_.proto
// DO NOT EDIT!

/*
	Package channel is a generated protocol buffer package.

	It is generated from these files:
		channel_personalization_.proto

	It has these top-level messages:
		GetUserChannelDecorationListReq
		GetUserChannelDecorationListResp
		ActivateUserChannelDecorationReq
		ActivateUserChannelDecorationResp
*/
package channel

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 获取用户的进房特效列表
type GetUserChannelDecorationListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Type    uint32      `protobuf:"varint,2,req,name=type" json:"type"`
}

func (m *GetUserChannelDecorationListReq) Reset()         { *m = GetUserChannelDecorationListReq{} }
func (m *GetUserChannelDecorationListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelDecorationListReq) ProtoMessage()    {}
func (*GetUserChannelDecorationListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelPersonalization_, []int{0}
}

func (m *GetUserChannelDecorationListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserChannelDecorationListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetUserChannelDecorationListResp struct {
	BaseResp       *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	DecorationList []*ga.UserDecorationInfo `protobuf:"bytes,2,rep,name=decoration_list,json=decorationList" json:"decoration_list,omitempty"`
}

func (m *GetUserChannelDecorationListResp) Reset()         { *m = GetUserChannelDecorationListResp{} }
func (m *GetUserChannelDecorationListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserChannelDecorationListResp) ProtoMessage()    {}
func (*GetUserChannelDecorationListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelPersonalization_, []int{1}
}

func (m *GetUserChannelDecorationListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserChannelDecorationListResp) GetDecorationList() []*ga.UserDecorationInfo {
	if m != nil {
		return m.DecorationList
	}
	return nil
}

// 激活/取消用户的进房特效
type ActivateUserChannelDecorationReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Type         uint32      `protobuf:"varint,2,req,name=type" json:"type"`
	DecorationId string      `protobuf:"bytes,3,req,name=decoration_id,json=decorationId" json:"decoration_id"`
}

func (m *ActivateUserChannelDecorationReq) Reset()         { *m = ActivateUserChannelDecorationReq{} }
func (m *ActivateUserChannelDecorationReq) String() string { return proto.CompactTextString(m) }
func (*ActivateUserChannelDecorationReq) ProtoMessage()    {}
func (*ActivateUserChannelDecorationReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelPersonalization_, []int{2}
}

func (m *ActivateUserChannelDecorationReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ActivateUserChannelDecorationReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ActivateUserChannelDecorationReq) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

type ActivateUserChannelDecorationResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ActivateUserChannelDecorationResp) Reset()         { *m = ActivateUserChannelDecorationResp{} }
func (m *ActivateUserChannelDecorationResp) String() string { return proto.CompactTextString(m) }
func (*ActivateUserChannelDecorationResp) ProtoMessage()    {}
func (*ActivateUserChannelDecorationResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelPersonalization_, []int{3}
}

func (m *ActivateUserChannelDecorationResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*GetUserChannelDecorationListReq)(nil), "ga.GetUserChannelDecorationListReq")
	proto.RegisterType((*GetUserChannelDecorationListResp)(nil), "ga.GetUserChannelDecorationListResp")
	proto.RegisterType((*ActivateUserChannelDecorationReq)(nil), "ga.ActivateUserChannelDecorationReq")
	proto.RegisterType((*ActivateUserChannelDecorationResp)(nil), "ga.ActivateUserChannelDecorationResp")
}
func (m *GetUserChannelDecorationListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserChannelDecorationListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelPersonalization_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelPersonalization_(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *GetUserChannelDecorationListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserChannelDecorationListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelPersonalization_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.DecorationList) > 0 {
		for _, msg := range m.DecorationList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelPersonalization_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ActivateUserChannelDecorationReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivateUserChannelDecorationReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelPersonalization_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelPersonalization_(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelPersonalization_(dAtA, i, uint64(len(m.DecorationId)))
	i += copy(dAtA[i:], m.DecorationId)
	return i, nil
}

func (m *ActivateUserChannelDecorationResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActivateUserChannelDecorationResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelPersonalization_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func encodeFixed64ChannelPersonalization_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32ChannelPersonalization_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelPersonalization_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetUserChannelDecorationListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelPersonalization_(uint64(l))
	}
	n += 1 + sovChannelPersonalization_(uint64(m.Type))
	return n
}

func (m *GetUserChannelDecorationListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelPersonalization_(uint64(l))
	}
	if len(m.DecorationList) > 0 {
		for _, e := range m.DecorationList {
			l = e.Size()
			n += 1 + l + sovChannelPersonalization_(uint64(l))
		}
	}
	return n
}

func (m *ActivateUserChannelDecorationReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelPersonalization_(uint64(l))
	}
	n += 1 + sovChannelPersonalization_(uint64(m.Type))
	l = len(m.DecorationId)
	n += 1 + l + sovChannelPersonalization_(uint64(l))
	return n
}

func (m *ActivateUserChannelDecorationResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelPersonalization_(uint64(l))
	}
	return n
}

func sovChannelPersonalization_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelPersonalization_(x uint64) (n int) {
	return sovChannelPersonalization_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GetUserChannelDecorationListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelPersonalization_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserChannelDecorationListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserChannelDecorationListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelPersonalization_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserChannelDecorationListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelPersonalization_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserChannelDecorationListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserChannelDecorationListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DecorationList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DecorationList = append(m.DecorationList, &ga.UserDecorationInfo{})
			if err := m.DecorationList[len(m.DecorationList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelPersonalization_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivateUserChannelDecorationReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelPersonalization_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivateUserChannelDecorationReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivateUserChannelDecorationReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DecorationId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DecorationId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelPersonalization_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("decoration_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActivateUserChannelDecorationResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelPersonalization_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActivateUserChannelDecorationResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActivateUserChannelDecorationResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelPersonalization_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelPersonalization_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelPersonalization_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelPersonalization_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelPersonalization_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelPersonalization_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelPersonalization_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelPersonalization_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelPersonalization_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelPersonalization_   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("channel_personalization_.proto", fileDescriptorChannelPersonalization_)
}

var fileDescriptorChannelPersonalization_ = []byte{
	// 334 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x92, 0x41, 0x4b, 0xc3, 0x30,
	0x14, 0xc7, 0xd7, 0x6e, 0xe0, 0x96, 0x6d, 0x0a, 0x3d, 0x48, 0xf1, 0xd0, 0xd5, 0x22, 0xd2, 0x5d,
	0x3a, 0x18, 0x78, 0x16, 0xa7, 0x20, 0x03, 0x11, 0x29, 0x78, 0xf1, 0x52, 0xb2, 0xf4, 0x59, 0x03,
	0x59, 0x5e, 0xd6, 0x44, 0x61, 0x7e, 0x08, 0xf5, 0x63, 0xed, 0xe8, 0x27, 0x10, 0x99, 0x5f, 0x44,
	0xba, 0x16, 0xa7, 0x20, 0x8a, 0xe0, 0xed, 0xf1, 0x92, 0xdf, 0xfb, 0xfd, 0x13, 0x1e, 0xf1, 0xd8,
	0x0d, 0x95, 0x12, 0x44, 0xa2, 0x20, 0xd7, 0x28, 0xa9, 0xe0, 0xf7, 0xd4, 0x70, 0x94, 0x49, 0xa4,
	0x72, 0x34, 0xe8, 0xd8, 0x19, 0xdd, 0xe9, 0x66, 0x34, 0x99, 0x50, 0x0d, 0x65, 0x2b, 0x60, 0xa4,
	0x77, 0x0a, 0xe6, 0x52, 0x43, 0x7e, 0x5c, 0xb2, 0x27, 0xc0, 0x30, 0x5f, 0x51, 0x67, 0x5c, 0x9b,
	0x18, 0x66, 0xce, 0x3e, 0x69, 0x16, 0x40, 0x92, 0xc3, 0xcc, 0xb5, 0x7c, 0x3b, 0x6c, 0x0f, 0xdb,
	0x51, 0x46, 0xa3, 0x11, 0xd5, 0x10, 0xc3, 0x2c, 0xde, 0x98, 0x94, 0x85, 0xe3, 0x92, 0x86, 0x99,
	0x2b, 0x70, 0x6d, 0xdf, 0x0e, 0xbb, 0xa3, 0xc6, 0xe2, 0xa5, 0x57, 0x8b, 0x57, 0x9d, 0xe0, 0xc1,
	0x22, 0xfe, 0xcf, 0x16, 0xad, 0x9c, 0x3e, 0x69, 0x55, 0x1a, 0xad, 0x2a, 0x4f, 0x67, 0xed, 0xd1,
	0x2a, 0x6e, 0x4e, 0xaa, 0xca, 0x39, 0x24, 0x5b, 0xe9, 0xc7, 0x80, 0x44, 0x70, 0x6d, 0x5c, 0xdb,
	0xaf, 0x87, 0xed, 0xe1, 0x76, 0x01, 0x14, 0x9a, 0xf5, 0xfc, 0xb1, 0xbc, 0xc6, 0x78, 0x33, 0xfd,
	0xe2, 0x0b, 0x1e, 0x2d, 0xe2, 0x1f, 0x31, 0xc3, 0xef, 0xa8, 0x81, 0x6f, 0x53, 0xfd, 0xcb, 0xbb,
	0x9d, 0x3e, 0xe9, 0x7e, 0xca, 0xc9, 0x53, 0xb7, 0xee, 0xdb, 0x61, 0xab, 0xba, 0xd2, 0x59, 0x1f,
	0x8d, 0xd3, 0xe0, 0x9c, 0xec, 0xfe, 0x12, 0xe8, 0x4f, 0x5f, 0x34, 0xba, 0x58, 0x2c, 0x3d, 0xeb,
	0x79, 0xe9, 0x59, 0xaf, 0x4b, 0xcf, 0x7a, 0x7a, 0xf3, 0x6a, 0xc4, 0x65, 0x38, 0x8d, 0xe6, 0x7c,
	0x8e, 0xb7, 0x05, 0x32, 0xc5, 0x14, 0x44, 0xb9, 0x03, 0x57, 0x7b, 0x19, 0x0a, 0x2a, 0xb3, 0xe8,
	0x60, 0x68, 0x4c, 0xc4, 0x70, 0x3a, 0x58, 0xb5, 0x19, 0x8a, 0x01, 0x55, 0x6a, 0x50, 0xed, 0xd4,
	0x7b, 0x00, 0x00, 0x00, 0xff, 0xff, 0xc0, 0x19, 0xb4, 0x55, 0x5d, 0x02, 0x00, 0x00,
}
