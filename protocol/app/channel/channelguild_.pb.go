// Code generated by protoc-gen-gogo.
// source: channelguild_.proto
// DO NOT EDIT!

/*
	Package channel is a generated protocol buffer package.

	It is generated from these files:
		channelguild_.proto

	It has these top-level messages:
		ChannelGuildListEntry
		ChannelGuildGetListSummaryReq
		ChannelGuildGetListSummaryResp
		ChannelGuildGetListByTypeReq
		ChannelGuildGetListByTypeResp
		ChannelGuildGetTotalMemberCountReq
		ChannelGuildGetTotalMemberCountResp
*/
package channel

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ChannelGuildListType int32

const (
	ChannelGuildListType_CHANNELGUILD_LIST_NONE   ChannelGuildListType = 0
	ChannelGuildListType_CHANNELGUILD_LIST_HOME   ChannelGuildListType = 1
	ChannelGuildListType_CHANNELGUILD_LIST_PUBLIC ChannelGuildListType = 2
	ChannelGuildListType_CHANNELGUILD_LIST_INNER  ChannelGuildListType = 3
	ChannelGuildListType_CHANNELGUILD_LIST_MEMBER ChannelGuildListType = 4
)

var ChannelGuildListType_name = map[int32]string{
	0: "CHANNELGUILD_LIST_NONE",
	1: "CHANNELGUILD_LIST_HOME",
	2: "CHANNELGUILD_LIST_PUBLIC",
	3: "CHANNELGUILD_LIST_INNER",
	4: "CHANNELGUILD_LIST_MEMBER",
}
var ChannelGuildListType_value = map[string]int32{
	"CHANNELGUILD_LIST_NONE":   0,
	"CHANNELGUILD_LIST_HOME":   1,
	"CHANNELGUILD_LIST_PUBLIC": 2,
	"CHANNELGUILD_LIST_INNER":  3,
	"CHANNELGUILD_LIST_MEMBER": 4,
}

func (x ChannelGuildListType) Enum() *ChannelGuildListType {
	p := new(ChannelGuildListType)
	*p = x
	return p
}
func (x ChannelGuildListType) String() string {
	return proto.EnumName(ChannelGuildListType_name, int32(x))
}
func (x *ChannelGuildListType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelGuildListType_value, data, "ChannelGuildListType")
	if err != nil {
		return err
	}
	*x = ChannelGuildListType(value)
	return nil
}
func (ChannelGuildListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelguild_, []int{0}
}

type ChannelGuildListEntry struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType uint32 `protobuf:"varint,2,req,name=channel_type,json=channelType" json:"channel_type"`
	MemberCount uint32 `protobuf:"varint,3,opt,name=member_count,json=memberCount" json:"member_count"`
	ChannelName string `protobuf:"bytes,4,opt,name=channel_name,json=channelName" json:"channel_name"`
	TagName     string `protobuf:"bytes,5,opt,name=tag_name,json=tagName" json:"tag_name"`
	BgColor     string `protobuf:"bytes,6,opt,name=bg_color,json=bgColor" json:"bg_color"`
	Account     string `protobuf:"bytes,7,opt,name=account" json:"account"`
	HasPwd      bool   `protobuf:"varint,8,opt,name=has_pwd,json=hasPwd" json:"has_pwd"`
	IconMd5     string `protobuf:"bytes,9,opt,name=icon_md5,json=iconMd5" json:"icon_md5"`
}

func (m *ChannelGuildListEntry) Reset()         { *m = ChannelGuildListEntry{} }
func (m *ChannelGuildListEntry) String() string { return proto.CompactTextString(m) }
func (*ChannelGuildListEntry) ProtoMessage()    {}
func (*ChannelGuildListEntry) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelguild_, []int{0}
}

func (m *ChannelGuildListEntry) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelGuildListEntry) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *ChannelGuildListEntry) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *ChannelGuildListEntry) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelGuildListEntry) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *ChannelGuildListEntry) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

func (m *ChannelGuildListEntry) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelGuildListEntry) GetHasPwd() bool {
	if m != nil {
		return m.HasPwd
	}
	return false
}

func (m *ChannelGuildListEntry) GetIconMd5() string {
	if m != nil {
		return m.IconMd5
	}
	return ""
}

// 首页列表接口
type ChannelGuildGetListSummaryReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *ChannelGuildGetListSummaryReq) Reset()         { *m = ChannelGuildGetListSummaryReq{} }
func (m *ChannelGuildGetListSummaryReq) String() string { return proto.CompactTextString(m) }
func (*ChannelGuildGetListSummaryReq) ProtoMessage()    {}
func (*ChannelGuildGetListSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelguild_, []int{1}
}

func (m *ChannelGuildGetListSummaryReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelGuildGetListSummaryReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type ChannelGuildGetListSummaryResp struct {
	BaseResp    *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	PublicList  []*ChannelGuildListEntry `protobuf:"bytes,2,rep,name=public_list,json=publicList" json:"public_list,omitempty"`
	InnerList   []*ChannelGuildListEntry `protobuf:"bytes,3,rep,name=inner_list,json=innerList" json:"inner_list,omitempty"`
	MemberList  []*ChannelGuildListEntry `protobuf:"bytes,4,rep,name=member_list,json=memberList" json:"member_list,omitempty"`
	HomeChannel []*ChannelGuildListEntry `protobuf:"bytes,5,rep,name=home_channel,json=homeChannel" json:"home_channel,omitempty"`
}

func (m *ChannelGuildGetListSummaryResp) Reset()         { *m = ChannelGuildGetListSummaryResp{} }
func (m *ChannelGuildGetListSummaryResp) String() string { return proto.CompactTextString(m) }
func (*ChannelGuildGetListSummaryResp) ProtoMessage()    {}
func (*ChannelGuildGetListSummaryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelguild_, []int{2}
}

func (m *ChannelGuildGetListSummaryResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelGuildGetListSummaryResp) GetPublicList() []*ChannelGuildListEntry {
	if m != nil {
		return m.PublicList
	}
	return nil
}

func (m *ChannelGuildGetListSummaryResp) GetInnerList() []*ChannelGuildListEntry {
	if m != nil {
		return m.InnerList
	}
	return nil
}

func (m *ChannelGuildGetListSummaryResp) GetMemberList() []*ChannelGuildListEntry {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *ChannelGuildGetListSummaryResp) GetHomeChannel() []*ChannelGuildListEntry {
	if m != nil {
		return m.HomeChannel
	}
	return nil
}

// 分页更多接口
type ChannelGuildGetListByTypeReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	Type    uint32      `protobuf:"varint,3,req,name=type" json:"type"`
	Index   uint32      `protobuf:"varint,4,req,name=index" json:"index"`
}

func (m *ChannelGuildGetListByTypeReq) Reset()         { *m = ChannelGuildGetListByTypeReq{} }
func (m *ChannelGuildGetListByTypeReq) String() string { return proto.CompactTextString(m) }
func (*ChannelGuildGetListByTypeReq) ProtoMessage()    {}
func (*ChannelGuildGetListByTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelguild_, []int{3}
}

func (m *ChannelGuildGetListByTypeReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelGuildGetListByTypeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ChannelGuildGetListByTypeReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ChannelGuildGetListByTypeReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

type ChannelGuildGetListByTypeResp struct {
	BaseResp    *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ChannelList []*ChannelGuildListEntry `protobuf:"bytes,2,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
	Total       uint32                   `protobuf:"varint,3,req,name=total" json:"total"`
}

func (m *ChannelGuildGetListByTypeResp) Reset()         { *m = ChannelGuildGetListByTypeResp{} }
func (m *ChannelGuildGetListByTypeResp) String() string { return proto.CompactTextString(m) }
func (*ChannelGuildGetListByTypeResp) ProtoMessage()    {}
func (*ChannelGuildGetListByTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelguild_, []int{4}
}

func (m *ChannelGuildGetListByTypeResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelGuildGetListByTypeResp) GetChannelList() []*ChannelGuildListEntry {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *ChannelGuildGetListByTypeResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 公会房间总人数
type ChannelGuildGetTotalMemberCountReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GuildId uint32      `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *ChannelGuildGetTotalMemberCountReq) Reset()         { *m = ChannelGuildGetTotalMemberCountReq{} }
func (m *ChannelGuildGetTotalMemberCountReq) String() string { return proto.CompactTextString(m) }
func (*ChannelGuildGetTotalMemberCountReq) ProtoMessage()    {}
func (*ChannelGuildGetTotalMemberCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelguild_, []int{5}
}

func (m *ChannelGuildGetTotalMemberCountReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelGuildGetTotalMemberCountReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type ChannelGuildGetTotalMemberCountResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Count    uint32       `protobuf:"varint,2,req,name=count" json:"count"`
}

func (m *ChannelGuildGetTotalMemberCountResp) Reset()         { *m = ChannelGuildGetTotalMemberCountResp{} }
func (m *ChannelGuildGetTotalMemberCountResp) String() string { return proto.CompactTextString(m) }
func (*ChannelGuildGetTotalMemberCountResp) ProtoMessage()    {}
func (*ChannelGuildGetTotalMemberCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelguild_, []int{6}
}

func (m *ChannelGuildGetTotalMemberCountResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelGuildGetTotalMemberCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func init() {
	proto.RegisterType((*ChannelGuildListEntry)(nil), "ga.ChannelGuildListEntry")
	proto.RegisterType((*ChannelGuildGetListSummaryReq)(nil), "ga.ChannelGuildGetListSummaryReq")
	proto.RegisterType((*ChannelGuildGetListSummaryResp)(nil), "ga.ChannelGuildGetListSummaryResp")
	proto.RegisterType((*ChannelGuildGetListByTypeReq)(nil), "ga.ChannelGuildGetListByTypeReq")
	proto.RegisterType((*ChannelGuildGetListByTypeResp)(nil), "ga.ChannelGuildGetListByTypeResp")
	proto.RegisterType((*ChannelGuildGetTotalMemberCountReq)(nil), "ga.ChannelGuildGetTotalMemberCountReq")
	proto.RegisterType((*ChannelGuildGetTotalMemberCountResp)(nil), "ga.ChannelGuildGetTotalMemberCountResp")
	proto.RegisterEnum("ga.ChannelGuildListType", ChannelGuildListType_name, ChannelGuildListType_value)
}
func (m *ChannelGuildListEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelGuildListEntry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.MemberCount))
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(len(m.ChannelName)))
	i += copy(dAtA[i:], m.ChannelName)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(len(m.TagName)))
	i += copy(dAtA[i:], m.TagName)
	dAtA[i] = 0x32
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(len(m.BgColor)))
	i += copy(dAtA[i:], m.BgColor)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x40
	i++
	if m.HasPwd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x4a
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(len(m.IconMd5)))
	i += copy(dAtA[i:], m.IconMd5)
	return i, nil
}

func (m *ChannelGuildGetListSummaryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelGuildGetListSummaryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelguild_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *ChannelGuildGetListSummaryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelGuildGetListSummaryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelguild_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if len(m.PublicList) > 0 {
		for _, msg := range m.PublicList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelguild_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.InnerList) > 0 {
		for _, msg := range m.InnerList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelguild_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.MemberList) > 0 {
		for _, msg := range m.MemberList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintChannelguild_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.HomeChannel) > 0 {
		for _, msg := range m.HomeChannel {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintChannelguild_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ChannelGuildGetListByTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelGuildGetListByTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelguild_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.Index))
	return i, nil
}

func (m *ChannelGuildGetListByTypeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelGuildGetListByTypeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelguild_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if len(m.ChannelList) > 0 {
		for _, msg := range m.ChannelList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelguild_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *ChannelGuildGetTotalMemberCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelGuildGetTotalMemberCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelguild_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *ChannelGuildGetTotalMemberCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelGuildGetTotalMemberCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelguild_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelguild_(dAtA, i, uint64(m.Count))
	return i, nil
}

func encodeFixed64Channelguild_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelguild_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelguild_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ChannelGuildListEntry) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelguild_(uint64(m.ChannelId))
	n += 1 + sovChannelguild_(uint64(m.ChannelType))
	n += 1 + sovChannelguild_(uint64(m.MemberCount))
	l = len(m.ChannelName)
	n += 1 + l + sovChannelguild_(uint64(l))
	l = len(m.TagName)
	n += 1 + l + sovChannelguild_(uint64(l))
	l = len(m.BgColor)
	n += 1 + l + sovChannelguild_(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovChannelguild_(uint64(l))
	n += 2
	l = len(m.IconMd5)
	n += 1 + l + sovChannelguild_(uint64(l))
	return n
}

func (m *ChannelGuildGetListSummaryReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelguild_(uint64(l))
	}
	n += 1 + sovChannelguild_(uint64(m.GuildId))
	return n
}

func (m *ChannelGuildGetListSummaryResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelguild_(uint64(l))
	}
	if len(m.PublicList) > 0 {
		for _, e := range m.PublicList {
			l = e.Size()
			n += 1 + l + sovChannelguild_(uint64(l))
		}
	}
	if len(m.InnerList) > 0 {
		for _, e := range m.InnerList {
			l = e.Size()
			n += 1 + l + sovChannelguild_(uint64(l))
		}
	}
	if len(m.MemberList) > 0 {
		for _, e := range m.MemberList {
			l = e.Size()
			n += 1 + l + sovChannelguild_(uint64(l))
		}
	}
	if len(m.HomeChannel) > 0 {
		for _, e := range m.HomeChannel {
			l = e.Size()
			n += 1 + l + sovChannelguild_(uint64(l))
		}
	}
	return n
}

func (m *ChannelGuildGetListByTypeReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelguild_(uint64(l))
	}
	n += 1 + sovChannelguild_(uint64(m.GuildId))
	n += 1 + sovChannelguild_(uint64(m.Type))
	n += 1 + sovChannelguild_(uint64(m.Index))
	return n
}

func (m *ChannelGuildGetListByTypeResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelguild_(uint64(l))
	}
	if len(m.ChannelList) > 0 {
		for _, e := range m.ChannelList {
			l = e.Size()
			n += 1 + l + sovChannelguild_(uint64(l))
		}
	}
	n += 1 + sovChannelguild_(uint64(m.Total))
	return n
}

func (m *ChannelGuildGetTotalMemberCountReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovChannelguild_(uint64(l))
	}
	n += 1 + sovChannelguild_(uint64(m.GuildId))
	return n
}

func (m *ChannelGuildGetTotalMemberCountResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovChannelguild_(uint64(l))
	}
	n += 1 + sovChannelguild_(uint64(m.Count))
	return n
}

func sovChannelguild_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelguild_(x uint64) (n int) {
	return sovChannelguild_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ChannelGuildListEntry) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelguild_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelGuildListEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelGuildListEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberCount", wireType)
			}
			m.MemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BgColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HasPwd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasPwd = bool(v != 0)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelguild_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelguild_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelGuildGetListSummaryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelguild_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelGuildGetListSummaryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelGuildGetListSummaryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelguild_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelguild_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelGuildGetListSummaryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelguild_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelGuildGetListSummaryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelGuildGetListSummaryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PublicList = append(m.PublicList, &ChannelGuildListEntry{})
			if err := m.PublicList[len(m.PublicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InnerList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InnerList = append(m.InnerList, &ChannelGuildListEntry{})
			if err := m.InnerList[len(m.InnerList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberList = append(m.MemberList, &ChannelGuildListEntry{})
			if err := m.MemberList[len(m.MemberList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HomeChannel", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HomeChannel = append(m.HomeChannel, &ChannelGuildListEntry{})
			if err := m.HomeChannel[len(m.HomeChannel)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelguild_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelguild_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelGuildGetListByTypeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelguild_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelGuildGetListByTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelGuildGetListByTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelguild_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelguild_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("index")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelGuildGetListByTypeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelguild_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelGuildGetListByTypeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelGuildGetListByTypeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelList = append(m.ChannelList, &ChannelGuildListEntry{})
			if err := m.ChannelList[len(m.ChannelList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelguild_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelguild_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelGuildGetTotalMemberCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelguild_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelGuildGetTotalMemberCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelGuildGetTotalMemberCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelguild_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelguild_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelGuildGetTotalMemberCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelguild_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelGuildGetTotalMemberCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelGuildGetTotalMemberCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelguild_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelguild_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelguild_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelguild_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelguild_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelguild_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelguild_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelguild_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelguild_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelguild_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelguild_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("channelguild_.proto", fileDescriptorChannelguild_) }

var fileDescriptorChannelguild_ = []byte{
	// 665 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x54, 0xcd, 0x6a, 0xdb, 0x4c,
	0x14, 0x8d, 0x64, 0x3b, 0xb6, 0xaf, 0x13, 0x30, 0xf3, 0xfd, 0x54, 0x75, 0x13, 0xc7, 0x28, 0xa5,
	0x75, 0xbb, 0x70, 0x20, 0x10, 0x28, 0x21, 0x9b, 0xda, 0x15, 0x89, 0xc1, 0x56, 0x82, 0xe2, 0x6c,
	0xba, 0x11, 0x23, 0x69, 0x90, 0x05, 0x92, 0x46, 0x91, 0xc6, 0xa4, 0x7a, 0x8b, 0xbe, 0x40, 0x77,
	0x85, 0x3e, 0x44, 0x5f, 0x20, 0xcb, 0x6e, 0xbb, 0x29, 0x25, 0x7d, 0x91, 0x32, 0x1a, 0x19, 0x2b,
	0x89, 0xa9, 0x1b, 0xc8, 0x4e, 0x9c, 0x73, 0xee, 0x9c, 0xd1, 0xb9, 0xf7, 0x0e, 0xfc, 0x63, 0x4f,
	0x71, 0x18, 0x12, 0xdf, 0x9d, 0x79, 0xbe, 0x63, 0xf6, 0xa2, 0x98, 0x32, 0x8a, 0x64, 0x17, 0xb7,
	0x36, 0x5d, 0x6c, 0x5a, 0x38, 0x21, 0x02, 0x52, 0xbf, 0xcb, 0xf0, 0xdf, 0x40, 0x48, 0x8f, 0xb9,
	0x74, 0xe4, 0x25, 0x4c, 0x0b, 0x59, 0x9c, 0xa2, 0x5d, 0x80, 0xfc, 0x0c, 0xd3, 0x73, 0x14, 0xa9,
	0x23, 0x77, 0x37, 0xfb, 0xe5, 0xeb, 0x1f, 0x3b, 0x6b, 0x46, 0x3d, 0xc7, 0x87, 0x0e, 0x7a, 0x09,
	0x1b, 0x73, 0x11, 0x4b, 0x23, 0xa2, 0xc8, 0x05, 0x59, 0x23, 0x67, 0x26, 0x69, 0x44, 0xb8, 0x30,
	0x20, 0x81, 0x45, 0x62, 0xd3, 0xa6, 0xb3, 0x90, 0x29, 0xa5, 0x8e, 0xb4, 0x10, 0x0a, 0x66, 0xc0,
	0x89, 0xe2, 0x89, 0x21, 0x0e, 0x88, 0x52, 0xee, 0x48, 0xdd, 0xfa, 0x9d, 0x13, 0x75, 0x1c, 0x10,
	0xb4, 0x03, 0x35, 0x86, 0x5d, 0x21, 0xaa, 0x14, 0x44, 0x55, 0x86, 0xdd, 0xb9, 0xc0, 0x72, 0x4d,
	0x9b, 0xfa, 0x34, 0x56, 0xd6, 0x8b, 0x02, 0xcb, 0x1d, 0x70, 0x10, 0xb5, 0xa1, 0x8a, 0x6d, 0x71,
	0x9d, 0x6a, 0x91, 0xcf, 0x41, 0xb4, 0x0d, 0xd5, 0x29, 0x4e, 0xcc, 0xe8, 0xca, 0x51, 0x6a, 0x1d,
	0xa9, 0x5b, 0xcb, 0xf9, 0xf5, 0x29, 0x4e, 0xce, 0xae, 0x1c, 0x7e, 0xbe, 0x67, 0xd3, 0xd0, 0x0c,
	0x9c, 0x03, 0xa5, 0x5e, 0xac, 0xe7, 0xe8, 0xd8, 0x39, 0x50, 0xa7, 0xb0, 0x5d, 0x8c, 0xf6, 0x98,
	0x30, 0x9e, 0xee, 0xf9, 0x2c, 0x08, 0x70, 0x9c, 0x1a, 0xe4, 0x12, 0xbd, 0x80, 0x1a, 0x6f, 0x85,
	0x19, 0x93, 0xcb, 0x2c, 0xe0, 0xc6, 0x7e, 0xa3, 0xe7, 0xe2, 0x5e, 0x1f, 0x27, 0xc4, 0x20, 0x97,
	0x46, 0xd5, 0x12, 0x1f, 0xdc, 0x49, 0xf4, 0xd1, 0x73, 0x6e, 0x25, 0x5c, 0xcd, 0xd0, 0xa1, 0xa3,
	0x7e, 0x95, 0xa1, 0xfd, 0x27, 0xab, 0x24, 0x42, 0xaf, 0xa0, 0x9e, 0x7b, 0x25, 0x51, 0x6e, 0xb6,
	0xb1, 0x30, 0x4b, 0x22, 0xa3, 0x66, 0xe5, 0x5f, 0xe8, 0x10, 0x1a, 0xd1, 0xcc, 0xf2, 0x3d, 0xdb,
	0xf4, 0xbd, 0x84, 0x29, 0x72, 0xa7, 0xd4, 0x6d, 0xec, 0x3f, 0xe5, 0xe2, 0xa5, 0x93, 0x62, 0x80,
	0x50, 0x73, 0x00, 0xbd, 0x01, 0xf0, 0xc2, 0x90, 0xc4, 0xa2, 0xb4, 0xb4, 0xaa, 0xb4, 0x9e, 0x89,
	0xb3, 0xca, 0x43, 0xc8, 0xe7, 0x40, 0x94, 0x96, 0x57, 0xba, 0x0a, 0x75, 0x56, 0x7b, 0x04, 0x1b,
	0x53, 0x1a, 0x10, 0x33, 0x9f, 0x0f, 0xa5, 0xb2, 0xaa, 0xb8, 0xc1, 0xe5, 0x39, 0xa5, 0x7e, 0x92,
	0x60, 0x6b, 0x49, 0x7a, 0xfd, 0x94, 0x4f, 0xee, 0x63, 0xf6, 0x09, 0x29, 0x50, 0xce, 0xd6, 0xa4,
	0x54, 0x20, 0x33, 0x04, 0xb5, 0xa0, 0xe2, 0x85, 0x0e, 0xf9, 0xa0, 0x94, 0x0b, 0x94, 0x80, 0xd4,
	0xcf, 0xd2, 0xd2, 0x41, 0x9a, 0xdf, 0xef, 0x61, 0xcd, 0x3d, 0x5a, 0xec, 0xd7, 0xdf, 0x75, 0x77,
	0xbe, 0x74, 0x59, 0xd0, 0x2d, 0xa8, 0x30, 0xca, 0xb0, 0x7f, 0xeb, 0x0f, 0x04, 0xa4, 0x06, 0xa0,
	0xde, 0xb9, 0xe5, 0x84, 0xe3, 0xe3, 0xc5, 0x72, 0x3f, 0xea, 0xcc, 0xfb, 0xb0, 0xbb, 0xd2, 0xee,
	0x61, 0xd1, 0xb4, 0xa0, 0x22, 0x5e, 0x83, 0xa2, 0x9f, 0x80, 0x5e, 0x7f, 0x91, 0xe0, 0xdf, 0xbb,
	0xf9, 0x4c, 0x44, 0xe3, 0xfe, 0x1f, 0x9c, 0xbc, 0xd5, 0x75, 0x6d, 0x74, 0x7c, 0x31, 0x1c, 0xbd,
	0x33, 0x47, 0xc3, 0xf3, 0x89, 0xa9, 0x9f, 0xea, 0x5a, 0x73, 0x6d, 0x39, 0x77, 0x72, 0x3a, 0xd6,
	0x9a, 0x12, 0xda, 0x02, 0xe5, 0x3e, 0x77, 0x76, 0xd1, 0x1f, 0x0d, 0x07, 0x4d, 0x19, 0x3d, 0x83,
	0x27, 0xf7, 0xd9, 0xa1, 0xae, 0x6b, 0x46, 0xb3, 0xb4, 0xbc, 0x74, 0xac, 0x8d, 0xfb, 0x9a, 0xd1,
	0x2c, 0xf7, 0xcf, 0xae, 0x6f, 0xda, 0xd2, 0xb7, 0x9b, 0xb6, 0xf4, 0xf3, 0xa6, 0x2d, 0x7d, 0xfc,
	0xd5, 0x5e, 0x03, 0xc5, 0xa6, 0x41, 0x2f, 0xf5, 0x52, 0x3a, 0xe3, 0x3f, 0x1e, 0x50, 0x87, 0xf8,
	0xe2, 0xf5, 0x7f, 0xff, 0xdc, 0xa5, 0x3e, 0x0e, 0xdd, 0xde, 0xc1, 0x3e, 0x63, 0x3d, 0x9b, 0x06,
	0x7b, 0x19, 0x6c, 0x53, 0x7f, 0x0f, 0x47, 0xd1, 0x5e, 0xde, 0xf8, 0xdf, 0x01, 0x00, 0x00, 0xff,
	0xff, 0xa8, 0x84, 0x1a, 0xff, 0x4c, 0x06, 0x00, 0x00,
}
