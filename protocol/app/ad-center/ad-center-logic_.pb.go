// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ad-center-logic_.proto

package ad_center // import "golang.52tt.com/protocol/app/ad-center"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Area_Type int32

const (
	Area_Type_Music    Area_Type = 0
	Area_Type_Activity Area_Type = 1
)

var Area_Type_name = map[int32]string{
	0: "Music",
	1: "Activity",
}
var Area_Type_value = map[string]int32{
	"Music":    0,
	"Activity": 1,
}

func (x Area_Type) String() string {
	return proto.EnumName(Area_Type_name, int32(x))
}
func (Area_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{0}
}

type CampaignCreativeType int32

const (
	// 图文
	CampaignCreativeType_TELETEXT CampaignCreativeType = 0
	// 轮播图
	CampaignCreativeType_SLIDESHOW CampaignCreativeType = 1
	// 信息流
	CampaignCreativeType_FLOW CampaignCreativeType = 3
	// 视频
	CampaignCreativeType_VIDEO CampaignCreativeType = 4
)

var CampaignCreativeType_name = map[int32]string{
	0: "TELETEXT",
	1: "SLIDESHOW",
	3: "FLOW",
	4: "VIDEO",
}
var CampaignCreativeType_value = map[string]int32{
	"TELETEXT":  0,
	"SLIDESHOW": 1,
	"FLOW":      3,
	"VIDEO":     4,
}

func (x CampaignCreativeType) String() string {
	return proto.EnumName(CampaignCreativeType_name, int32(x))
}
func (CampaignCreativeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{1}
}

type BatchGetAdReq_Ad_Mic_Mode_Type int32

const (
	// 非法
	BatchGetAdReq_Ad_Mic_Mode_Type_INVALID BatchGetAdReq_Ad_Mic_Mode_Type = 0
	// 娱乐模式 派对，点唱厅，扩列，踢保，小故事
	BatchGetAdReq_PGC_Fun BatchGetAdReq_Ad_Mic_Mode_Type = 1
	// CP战
	BatchGetAdReq_PGC_CP BatchGetAdReq_Ad_Mic_Mode_Type = 2
	// 狼人杀
	BatchGetAdReq_PGC_WarewolvesGame BatchGetAdReq_Ad_Mic_Mode_Type = 3
	// pia戏
	BatchGetAdReq_PGC_PIA BatchGetAdReq_Ad_Mic_Mode_Type = 4
	// 相亲交友
	BatchGetAdReq_PGC_Dating BatchGetAdReq_Ad_Mic_Mode_Type = 5
)

var BatchGetAdReq_Ad_Mic_Mode_Type_name = map[int32]string{
	0: "Ad_Mic_Mode_Type_INVALID",
	1: "PGC_Fun",
	2: "PGC_CP",
	3: "PGC_WarewolvesGame",
	4: "PGC_PIA",
	5: "PGC_Dating",
}
var BatchGetAdReq_Ad_Mic_Mode_Type_value = map[string]int32{
	"Ad_Mic_Mode_Type_INVALID": 0,
	"PGC_Fun":                  1,
	"PGC_CP":                   2,
	"PGC_WarewolvesGame":       3,
	"PGC_PIA":                  4,
	"PGC_Dating":               5,
}

func (x BatchGetAdReq_Ad_Mic_Mode_Type) String() string {
	return proto.EnumName(BatchGetAdReq_Ad_Mic_Mode_Type_name, int32(x))
}
func (BatchGetAdReq_Ad_Mic_Mode_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{0, 0}
}

type BatchGetAdReq_Ad_Config_Extra_Type int32

const (
	// 非法
	BatchGetAdReq_Ad_Config_Extra_Type_INVALID BatchGetAdReq_Ad_Config_Extra_Type = 0
	// 活动中心
	BatchGetAdReq_Ad_Config_Extra_Type_ActivityArea BatchGetAdReq_Ad_Config_Extra_Type = 1
)

var BatchGetAdReq_Ad_Config_Extra_Type_name = map[int32]string{
	0: "Ad_Config_Extra_Type_INVALID",
	1: "Ad_Config_Extra_Type_ActivityArea",
}
var BatchGetAdReq_Ad_Config_Extra_Type_value = map[string]int32{
	"Ad_Config_Extra_Type_INVALID":      0,
	"Ad_Config_Extra_Type_ActivityArea": 1,
}

func (x BatchGetAdReq_Ad_Config_Extra_Type) String() string {
	return proto.EnumName(BatchGetAdReq_Ad_Config_Extra_Type_name, int32(x))
}
func (BatchGetAdReq_Ad_Config_Extra_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{0, 1}
}

type BatchGetAdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	AdIdList             []uint32     `protobuf:"varint,2,rep,packed,name=ad_id_list,json=adIdList,proto3" json:"ad_id_list,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32       `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelTabId         uint32       `protobuf:"varint,5,opt,name=channel_tab_id,json=channelTabId,proto3" json:"channel_tab_id,omitempty"`
	AreaId               uint32       `protobuf:"varint,6,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	MicMode              uint32       `protobuf:"varint,7,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	MicCount             uint32       `protobuf:"varint,8,opt,name=mic_count,json=micCount,proto3" json:"mic_count,omitempty"`
	MarketChannelId      string       `protobuf:"bytes,9,opt,name=market_channel_id,json=marketChannelId,proto3" json:"market_channel_id,omitempty"`
	GetConfigExtraType   uint32       `protobuf:"varint,10,opt,name=get_config_extra_type,json=getConfigExtraType,proto3" json:"get_config_extra_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetAdReq) Reset()         { *m = BatchGetAdReq{} }
func (m *BatchGetAdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAdReq) ProtoMessage()    {}
func (*BatchGetAdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{0}
}
func (m *BatchGetAdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAdReq.Unmarshal(m, b)
}
func (m *BatchGetAdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAdReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAdReq.Merge(dst, src)
}
func (m *BatchGetAdReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAdReq.Size(m)
}
func (m *BatchGetAdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAdReq proto.InternalMessageInfo

func (m *BatchGetAdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchGetAdReq) GetAdIdList() []uint32 {
	if m != nil {
		return m.AdIdList
	}
	return nil
}

func (m *BatchGetAdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchGetAdReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *BatchGetAdReq) GetChannelTabId() uint32 {
	if m != nil {
		return m.ChannelTabId
	}
	return 0
}

func (m *BatchGetAdReq) GetAreaId() uint32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *BatchGetAdReq) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *BatchGetAdReq) GetMicCount() uint32 {
	if m != nil {
		return m.MicCount
	}
	return 0
}

func (m *BatchGetAdReq) GetMarketChannelId() string {
	if m != nil {
		return m.MarketChannelId
	}
	return ""
}

func (m *BatchGetAdReq) GetGetConfigExtraType() uint32 {
	if m != nil {
		return m.GetConfigExtraType
	}
	return 0
}

type AreaInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	AdIds                []uint32 `protobuf:"varint,3,rep,packed,name=ad_ids,json=adIds,proto3" json:"ad_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AreaInfo) Reset()         { *m = AreaInfo{} }
func (m *AreaInfo) String() string { return proto.CompactTextString(m) }
func (*AreaInfo) ProtoMessage()    {}
func (*AreaInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{1}
}
func (m *AreaInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AreaInfo.Unmarshal(m, b)
}
func (m *AreaInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AreaInfo.Marshal(b, m, deterministic)
}
func (dst *AreaInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AreaInfo.Merge(dst, src)
}
func (m *AreaInfo) XXX_Size() int {
	return xxx_messageInfo_AreaInfo.Size(m)
}
func (m *AreaInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AreaInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AreaInfo proto.InternalMessageInfo

func (m *AreaInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AreaInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AreaInfo) GetAdIds() []uint32 {
	if m != nil {
		return m.AdIds
	}
	return nil
}

type ConfigExtra struct {
	AreaInfos            []*AreaInfo `protobuf:"bytes,1,rep,name=area_infos,json=areaInfos,proto3" json:"area_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ConfigExtra) Reset()         { *m = ConfigExtra{} }
func (m *ConfigExtra) String() string { return proto.CompactTextString(m) }
func (*ConfigExtra) ProtoMessage()    {}
func (*ConfigExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{2}
}
func (m *ConfigExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfigExtra.Unmarshal(m, b)
}
func (m *ConfigExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfigExtra.Marshal(b, m, deterministic)
}
func (dst *ConfigExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfigExtra.Merge(dst, src)
}
func (m *ConfigExtra) XXX_Size() int {
	return xxx_messageInfo_ConfigExtra.Size(m)
}
func (m *ConfigExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfigExtra.DiscardUnknown(m)
}

var xxx_messageInfo_ConfigExtra proto.InternalMessageInfo

func (m *ConfigExtra) GetAreaInfos() []*AreaInfo {
	if m != nil {
		return m.AreaInfos
	}
	return nil
}

type BatchGetAdResp struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	AdCampaignMap        map[uint32]*CampaignList `protobuf:"bytes,2,rep,name=ad_campaign_map,json=adCampaignMap,proto3" json:"ad_campaign_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	AreaInfo             *AreaInfo                `protobuf:"bytes,3,opt,name=area_info,json=areaInfo,proto3" json:"area_info,omitempty"`
	Extra                *ConfigExtra             `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchGetAdResp) Reset()         { *m = BatchGetAdResp{} }
func (m *BatchGetAdResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAdResp) ProtoMessage()    {}
func (*BatchGetAdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{3}
}
func (m *BatchGetAdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAdResp.Unmarshal(m, b)
}
func (m *BatchGetAdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAdResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAdResp.Merge(dst, src)
}
func (m *BatchGetAdResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAdResp.Size(m)
}
func (m *BatchGetAdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAdResp proto.InternalMessageInfo

func (m *BatchGetAdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchGetAdResp) GetAdCampaignMap() map[uint32]*CampaignList {
	if m != nil {
		return m.AdCampaignMap
	}
	return nil
}

func (m *BatchGetAdResp) GetAreaInfo() *AreaInfo {
	if m != nil {
		return m.AreaInfo
	}
	return nil
}

func (m *BatchGetAdResp) GetExtra() *ConfigExtra {
	if m != nil {
		return m.Extra
	}
	return nil
}

type CampaignList struct {
	CampaignList         []*Campaign `protobuf:"bytes,1,rep,name=campaign_list,json=campaignList,proto3" json:"campaign_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CampaignList) Reset()         { *m = CampaignList{} }
func (m *CampaignList) String() string { return proto.CompactTextString(m) }
func (*CampaignList) ProtoMessage()    {}
func (*CampaignList) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{4}
}
func (m *CampaignList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CampaignList.Unmarshal(m, b)
}
func (m *CampaignList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CampaignList.Marshal(b, m, deterministic)
}
func (dst *CampaignList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CampaignList.Merge(dst, src)
}
func (m *CampaignList) XXX_Size() int {
	return xxx_messageInfo_CampaignList.Size(m)
}
func (m *CampaignList) XXX_DiscardUnknown() {
	xxx_messageInfo_CampaignList.DiscardUnknown(m)
}

var xxx_messageInfo_CampaignList proto.InternalMessageInfo

func (m *CampaignList) GetCampaignList() []*Campaign {
	if m != nil {
		return m.CampaignList
	}
	return nil
}

// 活动
type Campaign struct {
	// 广告id
	Id   uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 图片列表
	ImgUrlList []string `protobuf:"bytes,3,rep,name=img_url_list,json=imgUrlList,proto3" json:"img_url_list,omitempty"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,4,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	// 内容
	Text      string `protobuf:"bytes,5,opt,name=text,proto3" json:"text,omitempty"`
	TextColor string `protobuf:"bytes,6,opt,name=text_color,json=textColor,proto3" json:"text_color,omitempty"`
	// 跳转链接
	JumpUrl string `protobuf:"bytes,7,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	// CampaignCreativeType
	Type uint32 `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`
	// 开屏广告持续时间
	Duration  uint32 `protobuf:"varint,9,opt,name=duration,proto3" json:"duration,omitempty"`
	BeginTime int64  `protobuf:"varint,10,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime   int64  `protobuf:"varint,11,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 额外信息参数，json
	Extra    string    `protobuf:"bytes,12,opt,name=extra,proto3" json:"extra,omitempty"`
	Material *Material `protobuf:"bytes,13,opt,name=material,proto3" json:"material,omitempty"`
	// 活动id
	PolicyId             uint32   `protobuf:"varint,14,opt,name=policy_id,json=policyId,proto3" json:"policy_id,omitempty"`
	PolicyType           uint32   `protobuf:"varint,15,opt,name=policy_type,json=policyType,proto3" json:"policy_type,omitempty"`
	TablistInsertN       uint32   `protobuf:"varint,16,opt,name=tablist_insert_n,json=tablistInsertN,proto3" json:"tablist_insert_n,omitempty"`
	SkipWhenN            uint32   `protobuf:"varint,17,opt,name=skip_when_n,json=skipWhenN,proto3" json:"skip_when_n,omitempty"`
	ContentUrl           string   `protobuf:"bytes,18,opt,name=content_url,json=contentUrl,proto3" json:"content_url,omitempty"`
	LastPushTime         int64    `protobuf:"varint,19,opt,name=last_push_time,json=lastPushTime,proto3" json:"last_push_time,omitempty"`
	AreaId               uint32   `protobuf:"varint,20,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	MysteryplaceId       uint32   `protobuf:"varint,21,opt,name=mysteryplace_id,json=mysteryplaceId,proto3" json:"mysteryplace_id,omitempty"`
	TabAliasName         string   `protobuf:"bytes,22,opt,name=tab_alias_name,json=tabAliasName,proto3" json:"tab_alias_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Campaign) Reset()         { *m = Campaign{} }
func (m *Campaign) String() string { return proto.CompactTextString(m) }
func (*Campaign) ProtoMessage()    {}
func (*Campaign) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{5}
}
func (m *Campaign) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Campaign.Unmarshal(m, b)
}
func (m *Campaign) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Campaign.Marshal(b, m, deterministic)
}
func (dst *Campaign) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Campaign.Merge(dst, src)
}
func (m *Campaign) XXX_Size() int {
	return xxx_messageInfo_Campaign.Size(m)
}
func (m *Campaign) XXX_DiscardUnknown() {
	xxx_messageInfo_Campaign.DiscardUnknown(m)
}

var xxx_messageInfo_Campaign proto.InternalMessageInfo

func (m *Campaign) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Campaign) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Campaign) GetImgUrlList() []string {
	if m != nil {
		return m.ImgUrlList
	}
	return nil
}

func (m *Campaign) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *Campaign) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *Campaign) GetTextColor() string {
	if m != nil {
		return m.TextColor
	}
	return ""
}

func (m *Campaign) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *Campaign) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *Campaign) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *Campaign) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *Campaign) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *Campaign) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

func (m *Campaign) GetMaterial() *Material {
	if m != nil {
		return m.Material
	}
	return nil
}

func (m *Campaign) GetPolicyId() uint32 {
	if m != nil {
		return m.PolicyId
	}
	return 0
}

func (m *Campaign) GetPolicyType() uint32 {
	if m != nil {
		return m.PolicyType
	}
	return 0
}

func (m *Campaign) GetTablistInsertN() uint32 {
	if m != nil {
		return m.TablistInsertN
	}
	return 0
}

func (m *Campaign) GetSkipWhenN() uint32 {
	if m != nil {
		return m.SkipWhenN
	}
	return 0
}

func (m *Campaign) GetContentUrl() string {
	if m != nil {
		return m.ContentUrl
	}
	return ""
}

func (m *Campaign) GetLastPushTime() int64 {
	if m != nil {
		return m.LastPushTime
	}
	return 0
}

func (m *Campaign) GetAreaId() uint32 {
	if m != nil {
		return m.AreaId
	}
	return 0
}

func (m *Campaign) GetMysteryplaceId() uint32 {
	if m != nil {
		return m.MysteryplaceId
	}
	return 0
}

func (m *Campaign) GetTabAliasName() string {
	if m != nil {
		return m.TabAliasName
	}
	return ""
}

type Material struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 图片列表
	ImgUrlList []string `protobuf:"bytes,2,rep,name=img_url_list,json=imgUrlList,proto3" json:"img_url_list,omitempty"`
	// 视频地址
	VideoUrl string `protobuf:"bytes,3,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	// 内容
	TextList             []string `protobuf:"bytes,4,rep,name=text_list,json=textList,proto3" json:"text_list,omitempty"`
	TextColorList        []string `protobuf:"bytes,5,rep,name=text_color_list,json=textColorList,proto3" json:"text_color_list,omitempty"`
	SkipWhenN            uint32   `protobuf:"varint,6,opt,name=skip_when_n,json=skipWhenN,proto3" json:"skip_when_n,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Material) Reset()         { *m = Material{} }
func (m *Material) String() string { return proto.CompactTextString(m) }
func (*Material) ProtoMessage()    {}
func (*Material) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{6}
}
func (m *Material) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Material.Unmarshal(m, b)
}
func (m *Material) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Material.Marshal(b, m, deterministic)
}
func (dst *Material) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Material.Merge(dst, src)
}
func (m *Material) XXX_Size() int {
	return xxx_messageInfo_Material.Size(m)
}
func (m *Material) XXX_DiscardUnknown() {
	xxx_messageInfo_Material.DiscardUnknown(m)
}

var xxx_messageInfo_Material proto.InternalMessageInfo

func (m *Material) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Material) GetImgUrlList() []string {
	if m != nil {
		return m.ImgUrlList
	}
	return nil
}

func (m *Material) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *Material) GetTextList() []string {
	if m != nil {
		return m.TextList
	}
	return nil
}

func (m *Material) GetTextColorList() []string {
	if m != nil {
		return m.TextColorList
	}
	return nil
}

func (m *Material) GetSkipWhenN() uint32 {
	if m != nil {
		return m.SkipWhenN
	}
	return 0
}

type CheckTagIdMateReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TagId                uint32       `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckTagIdMateReq) Reset()         { *m = CheckTagIdMateReq{} }
func (m *CheckTagIdMateReq) String() string { return proto.CompactTextString(m) }
func (*CheckTagIdMateReq) ProtoMessage()    {}
func (*CheckTagIdMateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{7}
}
func (m *CheckTagIdMateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTagIdMateReq.Unmarshal(m, b)
}
func (m *CheckTagIdMateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTagIdMateReq.Marshal(b, m, deterministic)
}
func (dst *CheckTagIdMateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTagIdMateReq.Merge(dst, src)
}
func (m *CheckTagIdMateReq) XXX_Size() int {
	return xxx_messageInfo_CheckTagIdMateReq.Size(m)
}
func (m *CheckTagIdMateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTagIdMateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTagIdMateReq proto.InternalMessageInfo

func (m *CheckTagIdMateReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckTagIdMateReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type CheckTagIdMateResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Flag                 bool          `protobuf:"varint,2,opt,name=flag,proto3" json:"flag,omitempty"`
	TagId                uint32        `protobuf:"varint,3,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckTagIdMateResp) Reset()         { *m = CheckTagIdMateResp{} }
func (m *CheckTagIdMateResp) String() string { return proto.CompactTextString(m) }
func (*CheckTagIdMateResp) ProtoMessage()    {}
func (*CheckTagIdMateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{8}
}
func (m *CheckTagIdMateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTagIdMateResp.Unmarshal(m, b)
}
func (m *CheckTagIdMateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTagIdMateResp.Marshal(b, m, deterministic)
}
func (dst *CheckTagIdMateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTagIdMateResp.Merge(dst, src)
}
func (m *CheckTagIdMateResp) XXX_Size() int {
	return xxx_messageInfo_CheckTagIdMateResp.Size(m)
}
func (m *CheckTagIdMateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTagIdMateResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTagIdMateResp proto.InternalMessageInfo

func (m *CheckTagIdMateResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckTagIdMateResp) GetFlag() bool {
	if m != nil {
		return m.Flag
	}
	return false
}

func (m *CheckTagIdMateResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type CommitAdExposureReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	AdId                 uint32       `protobuf:"varint,2,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32       `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	CampaignId           uint32       `protobuf:"varint,5,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	MaterialId           uint32       `protobuf:"varint,6,opt,name=material_id,json=materialId,proto3" json:"material_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CommitAdExposureReq) Reset()         { *m = CommitAdExposureReq{} }
func (m *CommitAdExposureReq) String() string { return proto.CompactTextString(m) }
func (*CommitAdExposureReq) ProtoMessage()    {}
func (*CommitAdExposureReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{9}
}
func (m *CommitAdExposureReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitAdExposureReq.Unmarshal(m, b)
}
func (m *CommitAdExposureReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitAdExposureReq.Marshal(b, m, deterministic)
}
func (dst *CommitAdExposureReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitAdExposureReq.Merge(dst, src)
}
func (m *CommitAdExposureReq) XXX_Size() int {
	return xxx_messageInfo_CommitAdExposureReq.Size(m)
}
func (m *CommitAdExposureReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitAdExposureReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommitAdExposureReq proto.InternalMessageInfo

func (m *CommitAdExposureReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommitAdExposureReq) GetAdId() uint32 {
	if m != nil {
		return m.AdId
	}
	return 0
}

func (m *CommitAdExposureReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CommitAdExposureReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *CommitAdExposureReq) GetCampaignId() uint32 {
	if m != nil {
		return m.CampaignId
	}
	return 0
}

func (m *CommitAdExposureReq) GetMaterialId() uint32 {
	if m != nil {
		return m.MaterialId
	}
	return 0
}

type CommitAdExposureResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CommitAdExposureResp) Reset()         { *m = CommitAdExposureResp{} }
func (m *CommitAdExposureResp) String() string { return proto.CompactTextString(m) }
func (*CommitAdExposureResp) ProtoMessage()    {}
func (*CommitAdExposureResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{10}
}
func (m *CommitAdExposureResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitAdExposureResp.Unmarshal(m, b)
}
func (m *CommitAdExposureResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitAdExposureResp.Marshal(b, m, deterministic)
}
func (dst *CommitAdExposureResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitAdExposureResp.Merge(dst, src)
}
func (m *CommitAdExposureResp) XXX_Size() int {
	return xxx_messageInfo_CommitAdExposureResp.Size(m)
}
func (m *CommitAdExposureResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitAdExposureResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommitAdExposureResp proto.InternalMessageInfo

func (m *CommitAdExposureResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type CommitAdClickReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	AdId                 uint32       `protobuf:"varint,2,opt,name=ad_id,json=adId,proto3" json:"ad_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32       `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	CampaignId           uint32       `protobuf:"varint,5,opt,name=campaign_id,json=campaignId,proto3" json:"campaign_id,omitempty"`
	MaterialId           uint32       `protobuf:"varint,6,opt,name=material_id,json=materialId,proto3" json:"material_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CommitAdClickReq) Reset()         { *m = CommitAdClickReq{} }
func (m *CommitAdClickReq) String() string { return proto.CompactTextString(m) }
func (*CommitAdClickReq) ProtoMessage()    {}
func (*CommitAdClickReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{11}
}
func (m *CommitAdClickReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitAdClickReq.Unmarshal(m, b)
}
func (m *CommitAdClickReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitAdClickReq.Marshal(b, m, deterministic)
}
func (dst *CommitAdClickReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitAdClickReq.Merge(dst, src)
}
func (m *CommitAdClickReq) XXX_Size() int {
	return xxx_messageInfo_CommitAdClickReq.Size(m)
}
func (m *CommitAdClickReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitAdClickReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommitAdClickReq proto.InternalMessageInfo

func (m *CommitAdClickReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommitAdClickReq) GetAdId() uint32 {
	if m != nil {
		return m.AdId
	}
	return 0
}

func (m *CommitAdClickReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CommitAdClickReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *CommitAdClickReq) GetCampaignId() uint32 {
	if m != nil {
		return m.CampaignId
	}
	return 0
}

func (m *CommitAdClickReq) GetMaterialId() uint32 {
	if m != nil {
		return m.MaterialId
	}
	return 0
}

type CommitAdClickResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CommitAdClickResp) Reset()         { *m = CommitAdClickResp{} }
func (m *CommitAdClickResp) String() string { return proto.CompactTextString(m) }
func (*CommitAdClickResp) ProtoMessage()    {}
func (*CommitAdClickResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ad_center_logic__714e6153797b9c4b, []int{12}
}
func (m *CommitAdClickResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommitAdClickResp.Unmarshal(m, b)
}
func (m *CommitAdClickResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommitAdClickResp.Marshal(b, m, deterministic)
}
func (dst *CommitAdClickResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitAdClickResp.Merge(dst, src)
}
func (m *CommitAdClickResp) XXX_Size() int {
	return xxx_messageInfo_CommitAdClickResp.Size(m)
}
func (m *CommitAdClickResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitAdClickResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommitAdClickResp proto.InternalMessageInfo

func (m *CommitAdClickResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*BatchGetAdReq)(nil), "ga.ad_center.BatchGetAdReq")
	proto.RegisterType((*AreaInfo)(nil), "ga.ad_center.AreaInfo")
	proto.RegisterType((*ConfigExtra)(nil), "ga.ad_center.ConfigExtra")
	proto.RegisterType((*BatchGetAdResp)(nil), "ga.ad_center.BatchGetAdResp")
	proto.RegisterMapType((map[uint32]*CampaignList)(nil), "ga.ad_center.BatchGetAdResp.AdCampaignMapEntry")
	proto.RegisterType((*CampaignList)(nil), "ga.ad_center.CampaignList")
	proto.RegisterType((*Campaign)(nil), "ga.ad_center.Campaign")
	proto.RegisterType((*Material)(nil), "ga.ad_center.Material")
	proto.RegisterType((*CheckTagIdMateReq)(nil), "ga.ad_center.CheckTagIdMateReq")
	proto.RegisterType((*CheckTagIdMateResp)(nil), "ga.ad_center.CheckTagIdMateResp")
	proto.RegisterType((*CommitAdExposureReq)(nil), "ga.ad_center.CommitAdExposureReq")
	proto.RegisterType((*CommitAdExposureResp)(nil), "ga.ad_center.CommitAdExposureResp")
	proto.RegisterType((*CommitAdClickReq)(nil), "ga.ad_center.CommitAdClickReq")
	proto.RegisterType((*CommitAdClickResp)(nil), "ga.ad_center.CommitAdClickResp")
	proto.RegisterEnum("ga.ad_center.Area_Type", Area_Type_name, Area_Type_value)
	proto.RegisterEnum("ga.ad_center.CampaignCreativeType", CampaignCreativeType_name, CampaignCreativeType_value)
	proto.RegisterEnum("ga.ad_center.BatchGetAdReq_Ad_Mic_Mode_Type", BatchGetAdReq_Ad_Mic_Mode_Type_name, BatchGetAdReq_Ad_Mic_Mode_Type_value)
	proto.RegisterEnum("ga.ad_center.BatchGetAdReq_Ad_Config_Extra_Type", BatchGetAdReq_Ad_Config_Extra_Type_name, BatchGetAdReq_Ad_Config_Extra_Type_value)
}

func init() {
	proto.RegisterFile("ad-center-logic_.proto", fileDescriptor_ad_center_logic__714e6153797b9c4b)
}

var fileDescriptor_ad_center_logic__714e6153797b9c4b = []byte{
	// 1312 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x56, 0x6d, 0x6f, 0xdb, 0x36,
	0x10, 0xae, 0xdf, 0x62, 0xe9, 0xfc, 0x12, 0x85, 0x4d, 0x32, 0x35, 0xed, 0x36, 0xd7, 0xe8, 0xba,
	0xac, 0x40, 0x9d, 0xcd, 0x45, 0x81, 0x61, 0x03, 0x06, 0x38, 0x8e, 0xdb, 0x0a, 0x4b, 0xd2, 0x40,
	0x75, 0x9b, 0x61, 0x18, 0x40, 0xd0, 0x12, 0x23, 0xb3, 0xd1, 0x5b, 0x25, 0x3a, 0xad, 0xbf, 0xec,
	0xe7, 0xec, 0x67, 0xec, 0x27, 0xec, 0xcb, 0x80, 0x61, 0x3f, 0x67, 0xe0, 0x49, 0x72, 0x1d, 0x37,
	0x1b, 0x16, 0x60, 0x9f, 0xf6, 0x49, 0xe4, 0xf3, 0x1c, 0x8f, 0x77, 0xa7, 0x87, 0x47, 0xc2, 0x36,
	0x73, 0x1f, 0x3a, 0x3c, 0x94, 0x3c, 0x79, 0xe8, 0x47, 0x9e, 0x70, 0x68, 0x2f, 0x4e, 0x22, 0x19,
	0x91, 0xa6, 0xc7, 0x7a, 0xcc, 0xa5, 0x19, 0xb5, 0xd3, 0xf2, 0x18, 0x9d, 0xb0, 0x94, 0x67, 0x64,
	0xf7, 0xb7, 0x2a, 0xb4, 0xf6, 0x99, 0x74, 0xa6, 0x4f, 0xb9, 0x1c, 0xb8, 0x36, 0x7f, 0x43, 0xee,
	0x83, 0xa6, 0x78, 0x9a, 0xf0, 0x37, 0x66, 0xa9, 0x53, 0xda, 0x6d, 0xf4, 0x1b, 0x3d, 0x8f, 0xf5,
	0xf6, 0x59, 0xca, 0x6d, 0xfe, 0xc6, 0xae, 0x4f, 0xb2, 0x01, 0xb9, 0x03, 0xc0, 0x5c, 0x2a, 0x5c,
	0xea, 0x8b, 0x54, 0x9a, 0xe5, 0x4e, 0x65, 0xb7, 0x65, 0x6b, 0xcc, 0xb5, 0xdc, 0x43, 0x91, 0x4a,
	0xf2, 0x31, 0x80, 0x33, 0x65, 0x61, 0xc8, 0x7d, 0x2a, 0x5c, 0xb3, 0xd2, 0x29, 0xed, 0xb6, 0x6c,
	0x3d, 0x47, 0x2c, 0x97, 0xdc, 0x85, 0x66, 0x41, 0xcb, 0x79, 0xcc, 0xcd, 0x2a, 0x1a, 0x34, 0x72,
	0x6c, 0x3c, 0x8f, 0x39, 0xb9, 0x07, 0xed, 0x85, 0x09, 0x9b, 0x28, 0x2f, 0x35, 0x34, 0x2a, 0x16,
	0x8e, 0xd9, 0xc4, 0x72, 0xc9, 0x47, 0x50, 0x67, 0x09, 0x67, 0x8a, 0x5e, 0x43, 0x7a, 0x4d, 0x4d,
	0x2d, 0x97, 0xdc, 0x02, 0x2d, 0x10, 0x0e, 0x0d, 0x22, 0x97, 0x9b, 0x75, 0x64, 0xea, 0x81, 0x70,
	0x8e, 0x22, 0x97, 0x93, 0xdb, 0xa0, 0x2b, 0xca, 0x89, 0x66, 0xa1, 0x34, 0x35, 0xe4, 0x94, 0xed,
	0x50, 0xcd, 0xc9, 0x03, 0xd8, 0x08, 0x58, 0x72, 0xce, 0x25, 0x5d, 0x8a, 0x5f, 0xef, 0x94, 0x76,
	0x75, 0x7b, 0x3d, 0x23, 0x86, 0x8b, 0x2c, 0xbe, 0x82, 0x2d, 0x4f, 0x19, 0x46, 0xe1, 0x99, 0xf0,
	0x28, 0x7f, 0x27, 0x13, 0x96, 0xa5, 0x03, 0xe8, 0x94, 0x78, 0x5c, 0x0e, 0x91, 0x1b, 0x29, 0x4a,
	0x65, 0xd5, 0xfd, 0x19, 0x8c, 0x81, 0x4b, 0x8f, 0x84, 0x43, 0x55, 0x28, 0x14, 0x33, 0xbd, 0x03,
	0xe6, 0x2a, 0x46, 0xad, 0xe3, 0x57, 0x83, 0x43, 0xeb, 0xc0, 0xb8, 0x41, 0x1a, 0x50, 0x3f, 0x79,
	0x3a, 0xa4, 0x4f, 0x66, 0xa1, 0x51, 0x22, 0x00, 0x6b, 0x6a, 0x32, 0x3c, 0x31, 0xca, 0x64, 0x1b,
	0x88, 0x1a, 0x9f, 0xb2, 0x84, 0xbf, 0x8d, 0xfc, 0x0b, 0x9e, 0x3e, 0x65, 0x01, 0x37, 0x2a, 0xc5,
	0x82, 0x13, 0x6b, 0x60, 0x54, 0x49, 0x1b, 0x40, 0x4d, 0x0e, 0x98, 0x14, 0xa1, 0x67, 0xd4, 0xba,
	0x14, 0x36, 0x07, 0x2e, 0xcd, 0xa2, 0xa2, 0x18, 0x56, 0x16, 0x43, 0x07, 0xee, 0x5c, 0x85, 0x2f,
	0xc5, 0xf1, 0x19, 0xdc, 0xbd, 0xd2, 0x62, 0xe0, 0x48, 0x71, 0x21, 0xe4, 0x7c, 0x90, 0x70, 0x66,
	0x94, 0xba, 0x23, 0xd0, 0xd4, 0xc8, 0x0a, 0xcf, 0x22, 0xd2, 0x86, 0xb2, 0x70, 0x51, 0x44, 0x2d,
	0xbb, 0x2c, 0x5c, 0x42, 0xa0, 0x1a, 0xb2, 0x80, 0x9b, 0x65, 0x2c, 0x27, 0x8e, 0xc9, 0x16, 0xac,
	0xa1, 0x8c, 0x52, 0xb3, 0x82, 0x12, 0xaa, 0x29, 0x09, 0xa5, 0xdd, 0x03, 0x68, 0x2c, 0x95, 0x8e,
	0x3c, 0x06, 0xc8, 0x7e, 0x73, 0x78, 0x16, 0xa5, 0x66, 0xa9, 0x53, 0xd9, 0x6d, 0xf4, 0xb7, 0x7b,
	0xcb, 0xc2, 0xee, 0x15, 0xbb, 0xda, 0x3a, 0xcb, 0x47, 0x69, 0xf7, 0xcf, 0x32, 0xb4, 0x97, 0xd5,
	0x9d, 0xc6, 0xe4, 0x0b, 0xd0, 0x73, 0x79, 0xa7, 0x71, 0xae, 0xef, 0xe6, 0x7b, 0x7d, 0xa7, 0xb1,
	0xad, 0x4d, 0xf2, 0x11, 0x39, 0x85, 0x75, 0xe5, 0x9e, 0x05, 0x31, 0x13, 0x5e, 0x48, 0x03, 0x16,
	0xa3, 0xcc, 0x1b, 0xfd, 0xbd, 0xcb, 0x3b, 0x5f, 0xde, 0xa1, 0x37, 0x70, 0x87, 0xf9, 0x92, 0x23,
	0x16, 0x8f, 0x42, 0x99, 0xcc, 0xed, 0x16, 0x5b, 0xc6, 0xc8, 0x23, 0xd0, 0x17, 0xd9, 0xe0, 0xd9,
	0xf8, 0xfb, 0x64, 0xb4, 0x22, 0x19, 0xb2, 0x07, 0x35, 0x54, 0x18, 0x9e, 0x95, 0x46, 0xff, 0xd6,
	0xe5, 0x05, 0x4b, 0xc5, 0xb2, 0x33, 0xbb, 0x9d, 0x9f, 0x80, 0x7c, 0x18, 0x0a, 0x31, 0xa0, 0x72,
	0xce, 0xe7, 0xf9, 0x4f, 0x51, 0x43, 0xf2, 0x25, 0xd4, 0x2e, 0x98, 0x3f, 0xcb, 0x7e, 0x4b, 0xa3,
	0xbf, 0xb3, 0xe2, 0x38, 0x77, 0xa0, 0x4e, 0xb5, 0x9d, 0x19, 0x7e, 0x53, 0xfe, 0xba, 0xd4, 0xfd,
	0x1e, 0x9a, 0xcb, 0x14, 0xf9, 0x16, 0x5a, 0x8b, 0x4a, 0x61, 0x47, 0xb8, 0xf2, 0x27, 0x15, 0x4b,
	0xec, 0xa6, 0xb3, 0xb4, 0xb8, 0xfb, 0x4b, 0x0d, 0xb4, 0x82, 0xfa, 0x57, 0xaa, 0xe9, 0x40, 0x53,
	0x04, 0x1e, 0x9d, 0x25, 0x7e, 0xb6, 0x99, 0xd2, 0x8e, 0x6e, 0x83, 0x08, 0xbc, 0x97, 0x89, 0x8f,
	0xf1, 0xdc, 0x06, 0xfd, 0x42, 0xb8, 0x3c, 0x52, 0x36, 0x58, 0x32, 0xdd, 0xd6, 0x10, 0x78, 0x99,
	0xf8, 0xca, 0xa5, 0xe4, 0xef, 0x24, 0x76, 0x14, 0xdd, 0xc6, 0xb1, 0xea, 0x58, 0xea, 0x4b, 0x9d,
	0xc8, 0x8f, 0x12, 0x6c, 0x26, 0xba, 0xad, 0x2b, 0x64, 0xa8, 0x00, 0xd5, 0x4f, 0x5e, 0xcf, 0x82,
	0x18, 0xdd, 0xd5, 0x91, 0xac, 0xab, 0x79, 0xe1, 0x4d, 0x9d, 0xfa, 0xac, 0x95, 0xe0, 0x98, 0xec,
	0x80, 0xe6, 0xce, 0x12, 0x26, 0x45, 0x14, 0x62, 0xf7, 0x68, 0xd9, 0x8b, 0xb9, 0xda, 0x69, 0xc2,
	0x3d, 0x11, 0x52, 0x29, 0x82, 0xac, 0x57, 0x54, 0x6c, 0x1d, 0x91, 0xb1, 0x08, 0xb8, 0xda, 0x89,
	0x87, 0x6e, 0x46, 0x36, 0x90, 0xac, 0xf3, 0xd0, 0x45, 0x6a, 0xb3, 0xd0, 0x40, 0x13, 0x23, 0xc8,
	0x26, 0xa4, 0x0f, 0x5a, 0xc0, 0x24, 0x4f, 0x04, 0xf3, 0xcd, 0xd6, 0x55, 0x6a, 0x3a, 0xca, 0x59,
	0x7b, 0x61, 0xa7, 0xca, 0x13, 0x47, 0xbe, 0x70, 0xe6, 0xaa, 0xbd, 0xb5, 0xb3, 0x00, 0x33, 0xc0,
	0x72, 0xc9, 0xa7, 0xd0, 0xc8, 0x49, 0xcc, 0x6b, 0x1d, 0x69, 0xc8, 0x20, 0xec, 0x16, 0xbb, 0x60,
	0x48, 0x36, 0x51, 0x95, 0xa7, 0x22, 0x4c, 0x79, 0x22, 0x69, 0x68, 0x1a, 0x68, 0xd5, 0xce, 0x71,
	0x0b, 0xe1, 0x63, 0xf2, 0x09, 0x34, 0xd2, 0x73, 0x11, 0xd3, 0xb7, 0x53, 0x1e, 0xd2, 0xd0, 0xdc,
	0xc8, 0x2e, 0x02, 0x05, 0x9d, 0x4e, 0x79, 0x78, 0xac, 0xb6, 0x72, 0xa2, 0x50, 0xf2, 0x50, 0x62,
	0x65, 0x09, 0xe6, 0x05, 0x39, 0xa4, 0x8a, 0x7b, 0x0f, 0xda, 0x3e, 0x4b, 0x25, 0x8d, 0x67, 0xe9,
	0x34, 0xab, 0xc9, 0x4d, 0xac, 0x49, 0x53, 0xa1, 0x27, 0xb3, 0x74, 0x8a, 0x85, 0x59, 0xba, 0x06,
	0x36, 0x2f, 0x5d, 0x03, 0x9f, 0xc3, 0x7a, 0x30, 0x4f, 0x25, 0x4f, 0xe6, 0xb1, 0xcf, 0x1c, 0xae,
	0x0c, 0xb6, 0xb2, 0x40, 0x97, 0x61, 0xcb, 0x55, 0xfb, 0xa8, 0x6b, 0x86, 0xf9, 0x82, 0xa5, 0x14,
	0xf5, 0xb6, 0x8d, 0xb1, 0x34, 0x25, 0x9b, 0x0c, 0x14, 0x78, 0xcc, 0x02, 0xde, 0xfd, 0xb5, 0x04,
	0x5a, 0x51, 0xcd, 0x0f, 0x84, 0xba, 0x2a, 0xca, 0xf2, 0x3f, 0x8b, 0xb2, 0xb2, 0x22, 0xca, 0xdb,
	0x80, 0x72, 0xcb, 0xd6, 0x56, 0x71, 0xad, 0xa6, 0x00, 0x5c, 0x79, 0x1f, 0xd6, 0xdf, 0xab, 0x33,
	0x33, 0xa9, 0xa1, 0x49, 0x6b, 0x21, 0x51, 0xb4, 0x5b, 0xa9, 0xf7, 0xda, 0x4a, 0xbd, 0xbb, 0x36,
	0x6c, 0x0c, 0xa7, 0xdc, 0x39, 0x1f, 0x33, 0xcf, 0x72, 0x55, 0x26, 0xd7, 0xb9, 0xf2, 0xb7, 0x60,
	0x4d, 0x32, 0x4f, 0xd5, 0xb0, 0x8c, 0x7e, 0x6b, 0x52, 0x79, 0xe9, 0xbe, 0x06, 0xb2, 0xea, 0xf3,
	0x7a, 0x8d, 0x96, 0x40, 0xf5, 0xcc, 0x67, 0x1e, 0x7a, 0xd5, 0x6c, 0x1c, 0x2f, 0xed, 0x55, 0x59,
	0xde, 0xeb, 0x8f, 0x12, 0xdc, 0x1c, 0x46, 0x41, 0x20, 0xe4, 0xc0, 0x1d, 0xbd, 0x8b, 0xa3, 0x74,
	0x96, 0x5c, 0x2b, 0x85, 0x9b, 0x50, 0xc3, 0xeb, 0x26, 0xcf, 0xa0, 0xaa, 0x6e, 0x9b, 0xff, 0xe0,
	0xb1, 0xa2, 0x64, 0x5c, 0x74, 0xbf, 0xc5, 0x4b, 0x05, 0x0a, 0x28, 0x3b, 0x52, 0xc5, 0xd9, 0x7b,
	0xff, 0x56, 0x81, 0x02, 0xb2, 0xdc, 0xee, 0x00, 0x36, 0x3f, 0xcc, 0xeb, 0x5a, 0x65, 0xec, 0xfe,
	0x5e, 0x02, 0xa3, 0xf0, 0x31, 0xf4, 0x85, 0x73, 0xfe, 0xbf, 0x28, 0xcc, 0x77, 0xb0, 0xb1, 0x92,
	0xd4, 0xb5, 0xaa, 0xf2, 0xe0, 0x1e, 0xe8, 0xea, 0x36, 0xcd, 0x9e, 0x39, 0x3a, 0xd4, 0x8e, 0x66,
	0xa9, 0x70, 0x8c, 0x1b, 0xa4, 0x09, 0x5a, 0xf1, 0x74, 0x31, 0x4a, 0x0f, 0x9e, 0xc1, 0x66, 0x71,
	0x01, 0x0d, 0x13, 0xce, 0xa4, 0xb8, 0xe0, 0xb8, 0xa0, 0x09, 0xda, 0x78, 0x74, 0x38, 0x1a, 0x8f,
	0x7e, 0x18, 0x1b, 0x37, 0x48, 0x0b, 0xf4, 0x17, 0x87, 0xd6, 0xc1, 0xe8, 0xc5, 0xb3, 0xe7, 0xa7,
	0x46, 0x89, 0x68, 0x50, 0x7d, 0x72, 0xf8, 0xfc, 0xd4, 0xa8, 0x28, 0xbf, 0xaf, 0xac, 0x83, 0xd1,
	0x73, 0xa3, 0xba, 0xbf, 0x0f, 0xa6, 0x13, 0x05, 0xbd, 0xb9, 0x98, 0x47, 0x33, 0x15, 0x92, 0x7a,
	0x81, 0xfa, 0xd9, 0x6b, 0xfb, 0xc7, 0xfb, 0x5e, 0xe4, 0xb3, 0xd0, 0xeb, 0x3d, 0xee, 0x4b, 0xd9,
	0x73, 0xa2, 0x60, 0x0f, 0x61, 0x27, 0xf2, 0xf7, 0x58, 0x1c, 0xef, 0x2d, 0xde, 0xef, 0x93, 0x35,
	0xc4, 0x1f, 0xfd, 0x15, 0x00, 0x00, 0xff, 0xff, 0x82, 0x72, 0x91, 0xbe, 0xd3, 0x0b, 0x00, 0x00,
}
