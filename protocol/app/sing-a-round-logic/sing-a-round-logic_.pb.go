// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sing-a-round-logic_.proto

package sing_a_round_logic // import "golang.52tt.com/protocol/app/sing-a-round-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SingingGameType int32

const (
	SingingGameType_SingingGameTypeUndefined    SingingGameType = 0
	SingingGameType_SingingGameTypeExpert       SingingGameType = 1
	SingingGameType_SingingGameTypeRookie       SingingGameType = 2
	SingingGameType_SingingGameTypePassThrough  SingingGameType = 999
	SingingGameType_SingingGameTypePassThrough2 SingingGameType = 1000
)

var SingingGameType_name = map[int32]string{
	0:    "SingingGameTypeUndefined",
	1:    "SingingGameTypeExpert",
	2:    "SingingGameTypeRookie",
	999:  "SingingGameTypePassThrough",
	1000: "SingingGameTypePassThrough2",
}
var SingingGameType_value = map[string]int32{
	"SingingGameTypeUndefined":    0,
	"SingingGameTypeExpert":       1,
	"SingingGameTypeRookie":       2,
	"SingingGameTypePassThrough":  999,
	"SingingGameTypePassThrough2": 1000,
}

func (x SingingGameType) String() string {
	return proto.EnumName(SingingGameType_name, int32(x))
}
func (SingingGameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{0}
}

type SingingGameRoundStage int32

const (
	SingingGameRoundStage_SingingGameRoundStageUndefined       SingingGameRoundStage = 0
	SingingGameRoundStage_SingingGameRoundStageGrabMic         SingingGameRoundStage = 1
	SingingGameRoundStage_SingingGameRoundStageSinging         SingingGameRoundStage = 2
	SingingGameRoundStage_SingingGameRoundStageReqHelp1        SingingGameRoundStage = 3
	SingingGameRoundStage_SingingGameRoundStageReqHelpFailed1  SingingGameRoundStage = 4
	SingingGameRoundStage_SingingGameRoundStageComparing       SingingGameRoundStage = 5
	SingingGameRoundStage_SingingGameRoundStageResult          SingingGameRoundStage = 6
	SingingGameRoundStage_SingingGameRoundStageReqHelp2        SingingGameRoundStage = 7
	SingingGameRoundStage_SingingGameRoundStageReqHelpFailed2  SingingGameRoundStage = 8
	SingingGameRoundStage_SingingGameRoundStageHelperSinging   SingingGameRoundStage = 9
	SingingGameRoundStage_SingingGameRoundStageHelperComparing SingingGameRoundStage = 10
	SingingGameRoundStage_SingingGameRoundStageHelperResult    SingingGameRoundStage = 11
	SingingGameRoundStage_SingingGameRoundStagePartialResult   SingingGameRoundStage = 12
	SingingGameRoundStage_SingingGameRoundStageVote            SingingGameRoundStage = 13
	SingingGameRoundStage_SingingGameRoundStagePreload         SingingGameRoundStage = 14
)

var SingingGameRoundStage_name = map[int32]string{
	0:  "SingingGameRoundStageUndefined",
	1:  "SingingGameRoundStageGrabMic",
	2:  "SingingGameRoundStageSinging",
	3:  "SingingGameRoundStageReqHelp1",
	4:  "SingingGameRoundStageReqHelpFailed1",
	5:  "SingingGameRoundStageComparing",
	6:  "SingingGameRoundStageResult",
	7:  "SingingGameRoundStageReqHelp2",
	8:  "SingingGameRoundStageReqHelpFailed2",
	9:  "SingingGameRoundStageHelperSinging",
	10: "SingingGameRoundStageHelperComparing",
	11: "SingingGameRoundStageHelperResult",
	12: "SingingGameRoundStagePartialResult",
	13: "SingingGameRoundStageVote",
	14: "SingingGameRoundStagePreload",
}
var SingingGameRoundStage_value = map[string]int32{
	"SingingGameRoundStageUndefined":       0,
	"SingingGameRoundStageGrabMic":         1,
	"SingingGameRoundStageSinging":         2,
	"SingingGameRoundStageReqHelp1":        3,
	"SingingGameRoundStageReqHelpFailed1":  4,
	"SingingGameRoundStageComparing":       5,
	"SingingGameRoundStageResult":          6,
	"SingingGameRoundStageReqHelp2":        7,
	"SingingGameRoundStageReqHelpFailed2":  8,
	"SingingGameRoundStageHelperSinging":   9,
	"SingingGameRoundStageHelperComparing": 10,
	"SingingGameRoundStageHelperResult":    11,
	"SingingGameRoundStagePartialResult":   12,
	"SingingGameRoundStageVote":            13,
	"SingingGameRoundStagePreload":         14,
}

func (x SingingGameRoundStage) String() string {
	return proto.EnumName(SingingGameRoundStage_name, int32(x))
}
func (SingingGameRoundStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{1}
}

type SingingGameUserType int32

const (
	SingingGameUserType_SingingGameUserTypeUndefined SingingGameUserType = 0
	SingingGameUserType_SingingGameUserTypeSinger    SingingGameUserType = 1
	SingingGameUserType_SingingGameUserTypeHelper    SingingGameUserType = 2
)

var SingingGameUserType_name = map[int32]string{
	0: "SingingGameUserTypeUndefined",
	1: "SingingGameUserTypeSinger",
	2: "SingingGameUserTypeHelper",
}
var SingingGameUserType_value = map[string]int32{
	"SingingGameUserTypeUndefined": 0,
	"SingingGameUserTypeSinger":    1,
	"SingingGameUserTypeHelper":    2,
}

func (x SingingGameUserType) String() string {
	return proto.EnumName(SingingGameUserType_name, int32(x))
}
func (SingingGameUserType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{2}
}

type RankListType int32

const (
	RankListType_RankListTypeUndefined RankListType = 0
	RankListType_RankListTypeFriend    RankListType = 1
	RankListType_RankListTypeAll       RankListType = 2
	RankListType_RankListTypeDay       RankListType = 3
)

var RankListType_name = map[int32]string{
	0: "RankListTypeUndefined",
	1: "RankListTypeFriend",
	2: "RankListTypeAll",
	3: "RankListTypeDay",
}
var RankListType_value = map[string]int32{
	"RankListTypeUndefined": 0,
	"RankListTypeFriend":    1,
	"RankListTypeAll":       2,
	"RankListTypeDay":       3,
}

func (x RankListType) String() string {
	return proto.EnumName(RankListType_name, int32(x))
}
func (RankListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{3}
}

type SingingGameResultType int32

const (
	SingingGameResultType_SingingGameResultTypeUndefined   SingingGameResultType = 0
	SingingGameResultType_SingingGameResultTypeFailed      SingingGameResultType = 1
	SingingGameResultType_SingingGameResultTypeNormal      SingingGameResultType = 2
	SingingGameResultType_SingingGameResultTypeCooperative SingingGameResultType = 3
	SingingGameResultType_SingingGameResultTypePerfect     SingingGameResultType = 4
)

var SingingGameResultType_name = map[int32]string{
	0: "SingingGameResultTypeUndefined",
	1: "SingingGameResultTypeFailed",
	2: "SingingGameResultTypeNormal",
	3: "SingingGameResultTypeCooperative",
	4: "SingingGameResultTypePerfect",
}
var SingingGameResultType_value = map[string]int32{
	"SingingGameResultTypeUndefined":   0,
	"SingingGameResultTypeFailed":      1,
	"SingingGameResultTypeNormal":      2,
	"SingingGameResultTypeCooperative": 3,
	"SingingGameResultTypePerfect":     4,
}

func (x SingingGameResultType) String() string {
	return proto.EnumName(SingingGameResultType_name, int32(x))
}
func (SingingGameResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{4}
}

type SingingGameLikeType int32

const (
	SingingGameLikeType_SingingGameLikeTypeUndefined SingingGameLikeType = 0
	SingingGameLikeType_SingingGameLikeTypeOne       SingingGameLikeType = 1
	SingingGameLikeType_SingingGameLikeTypeMany      SingingGameLikeType = 2
)

var SingingGameLikeType_name = map[int32]string{
	0: "SingingGameLikeTypeUndefined",
	1: "SingingGameLikeTypeOne",
	2: "SingingGameLikeTypeMany",
}
var SingingGameLikeType_value = map[string]int32{
	"SingingGameLikeTypeUndefined": 0,
	"SingingGameLikeTypeOne":       1,
	"SingingGameLikeTypeMany":      2,
}

func (x SingingGameLikeType) String() string {
	return proto.EnumName(SingingGameLikeType_name, int32(x))
}
func (SingingGameLikeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{5}
}

// 形象信息
type UserImageType int32

const (
	UserImageType_NormalImage  UserImageType = 0
	UserImageType_DefaultImage UserImageType = 1
	UserImageType_LockImage    UserImageType = 2
)

var UserImageType_name = map[int32]string{
	0: "NormalImage",
	1: "DefaultImage",
	2: "LockImage",
}
var UserImageType_value = map[string]int32{
	"NormalImage":  0,
	"DefaultImage": 1,
	"LockImage":    2,
}

func (x UserImageType) String() string {
	return proto.EnumName(UserImageType_name, int32(x))
}
func (UserImageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{6}
}

type AppReportAudioNotify_ReportType int32

const (
	AppReportAudioNotify_NotApprove AppReportAudioNotify_ReportType = 0
)

var AppReportAudioNotify_ReportType_name = map[int32]string{
	0: "NotApprove",
}
var AppReportAudioNotify_ReportType_value = map[string]int32{
	"NotApprove": 0,
}

func (x AppReportAudioNotify_ReportType) String() string {
	return proto.EnumName(AppReportAudioNotify_ReportType_name, int32(x))
}
func (AppReportAudioNotify_ReportType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{81, 0}
}

// 开始匹配
type StartSingingGameMatchingReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SingingGameType      SingingGameType `protobuf:"varint,2,opt,name=singing_game_type,json=singingGameType,proto3,enum=ga.sing_a_round_logic.SingingGameType" json:"singing_game_type,omitempty"`
	NewSingingGameType   uint32          `protobuf:"varint,3,opt,name=new_singing_game_type,json=newSingingGameType,proto3" json:"new_singing_game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StartSingingGameMatchingReq) Reset()         { *m = StartSingingGameMatchingReq{} }
func (m *StartSingingGameMatchingReq) String() string { return proto.CompactTextString(m) }
func (*StartSingingGameMatchingReq) ProtoMessage()    {}
func (*StartSingingGameMatchingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{0}
}
func (m *StartSingingGameMatchingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartSingingGameMatchingReq.Unmarshal(m, b)
}
func (m *StartSingingGameMatchingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartSingingGameMatchingReq.Marshal(b, m, deterministic)
}
func (dst *StartSingingGameMatchingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartSingingGameMatchingReq.Merge(dst, src)
}
func (m *StartSingingGameMatchingReq) XXX_Size() int {
	return xxx_messageInfo_StartSingingGameMatchingReq.Size(m)
}
func (m *StartSingingGameMatchingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartSingingGameMatchingReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartSingingGameMatchingReq proto.InternalMessageInfo

func (m *StartSingingGameMatchingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartSingingGameMatchingReq) GetSingingGameType() SingingGameType {
	if m != nil {
		return m.SingingGameType
	}
	return SingingGameType_SingingGameTypeUndefined
}

func (m *StartSingingGameMatchingReq) GetNewSingingGameType() uint32 {
	if m != nil {
		return m.NewSingingGameType
	}
	return 0
}

type StartSingingGameMatchingResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32        `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartSingingGameMatchingResp) Reset()         { *m = StartSingingGameMatchingResp{} }
func (m *StartSingingGameMatchingResp) String() string { return proto.CompactTextString(m) }
func (*StartSingingGameMatchingResp) ProtoMessage()    {}
func (*StartSingingGameMatchingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{1}
}
func (m *StartSingingGameMatchingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartSingingGameMatchingResp.Unmarshal(m, b)
}
func (m *StartSingingGameMatchingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartSingingGameMatchingResp.Marshal(b, m, deterministic)
}
func (dst *StartSingingGameMatchingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartSingingGameMatchingResp.Merge(dst, src)
}
func (m *StartSingingGameMatchingResp) XXX_Size() int {
	return xxx_messageInfo_StartSingingGameMatchingResp.Size(m)
}
func (m *StartSingingGameMatchingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartSingingGameMatchingResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartSingingGameMatchingResp proto.InternalMessageInfo

func (m *StartSingingGameMatchingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *StartSingingGameMatchingResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 加入游戏（观众加入游戏时使用）
type JoinSingingGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *JoinSingingGameReq) Reset()         { *m = JoinSingingGameReq{} }
func (m *JoinSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*JoinSingingGameReq) ProtoMessage()    {}
func (*JoinSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{2}
}
func (m *JoinSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinSingingGameReq.Unmarshal(m, b)
}
func (m *JoinSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *JoinSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinSingingGameReq.Merge(dst, src)
}
func (m *JoinSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_JoinSingingGameReq.Size(m)
}
func (m *JoinSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinSingingGameReq proto.InternalMessageInfo

func (m *JoinSingingGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinSingingGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type JoinSingingGameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsSuccess            bool          `protobuf:"varint,2,opt,name=is_success,json=isSuccess,proto3" json:"is_success,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *JoinSingingGameResp) Reset()         { *m = JoinSingingGameResp{} }
func (m *JoinSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*JoinSingingGameResp) ProtoMessage()    {}
func (*JoinSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{3}
}
func (m *JoinSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinSingingGameResp.Unmarshal(m, b)
}
func (m *JoinSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *JoinSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinSingingGameResp.Merge(dst, src)
}
func (m *JoinSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_JoinSingingGameResp.Size(m)
}
func (m *JoinSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinSingingGameResp proto.InternalMessageInfo

func (m *JoinSingingGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *JoinSingingGameResp) GetIsSuccess() bool {
	if m != nil {
		return m.IsSuccess
	}
	return false
}

type SingingGameSong struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SongName             string   `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	Singer               string   `protobuf:"bytes,3,opt,name=singer,proto3" json:"singer,omitempty"`
	Lyricist             string   `protobuf:"bytes,4,opt,name=lyricist,proto3" json:"lyricist,omitempty"`
	Composer             string   `protobuf:"bytes,5,opt,name=composer,proto3" json:"composer,omitempty"`
	OriginSinger         string   `protobuf:"bytes,6,opt,name=origin_singer,json=originSinger,proto3" json:"origin_singer,omitempty"`
	UploadUser           string   `protobuf:"bytes,7,opt,name=upload_user,json=uploadUser,proto3" json:"upload_user,omitempty"`
	PrecentorAccount     string   `protobuf:"bytes,10,opt,name=precentor_account,json=precentorAccount,proto3" json:"precentor_account,omitempty"`
	PrecentorUid         uint32   `protobuf:"varint,11,opt,name=precentor_uid,json=precentorUid,proto3" json:"precentor_uid,omitempty"`
	FileUrl              string   `protobuf:"bytes,8,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	Md5                  string   `protobuf:"bytes,9,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameSong) Reset()         { *m = SingingGameSong{} }
func (m *SingingGameSong) String() string { return proto.CompactTextString(m) }
func (*SingingGameSong) ProtoMessage()    {}
func (*SingingGameSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{4}
}
func (m *SingingGameSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameSong.Unmarshal(m, b)
}
func (m *SingingGameSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameSong.Marshal(b, m, deterministic)
}
func (dst *SingingGameSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameSong.Merge(dst, src)
}
func (m *SingingGameSong) XXX_Size() int {
	return xxx_messageInfo_SingingGameSong.Size(m)
}
func (m *SingingGameSong) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameSong.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameSong proto.InternalMessageInfo

func (m *SingingGameSong) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SingingGameSong) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *SingingGameSong) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *SingingGameSong) GetLyricist() string {
	if m != nil {
		return m.Lyricist
	}
	return ""
}

func (m *SingingGameSong) GetComposer() string {
	if m != nil {
		return m.Composer
	}
	return ""
}

func (m *SingingGameSong) GetOriginSinger() string {
	if m != nil {
		return m.OriginSinger
	}
	return ""
}

func (m *SingingGameSong) GetUploadUser() string {
	if m != nil {
		return m.UploadUser
	}
	return ""
}

func (m *SingingGameSong) GetPrecentorAccount() string {
	if m != nil {
		return m.PrecentorAccount
	}
	return ""
}

func (m *SingingGameSong) GetPrecentorUid() uint32 {
	if m != nil {
		return m.PrecentorUid
	}
	return 0
}

func (m *SingingGameSong) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *SingingGameSong) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

// 房间状态信息修改的通知（修改形象、用户加入游戏、用户下麦、用户退房、SingingGameRoundStage变化）
type SingingGameChannelInfoNotify struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingingGameChannelInfoNotify) Reset()         { *m = SingingGameChannelInfoNotify{} }
func (m *SingingGameChannelInfoNotify) String() string { return proto.CompactTextString(m) }
func (*SingingGameChannelInfoNotify) ProtoMessage()    {}
func (*SingingGameChannelInfoNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{5}
}
func (m *SingingGameChannelInfoNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameChannelInfoNotify.Unmarshal(m, b)
}
func (m *SingingGameChannelInfoNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameChannelInfoNotify.Marshal(b, m, deterministic)
}
func (dst *SingingGameChannelInfoNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameChannelInfoNotify.Merge(dst, src)
}
func (m *SingingGameChannelInfoNotify) XXX_Size() int {
	return xxx_messageInfo_SingingGameChannelInfoNotify.Size(m)
}
func (m *SingingGameChannelInfoNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameChannelInfoNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameChannelInfoNotify proto.InternalMessageInfo

func (m *SingingGameChannelInfoNotify) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 房间全量状态信息
type SingingGameChannelInfo struct {
	ChannelId          uint32                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MemberLimit        uint32                 `protobuf:"varint,2,opt,name=member_limit,json=memberLimit,proto3" json:"member_limit,omitempty"`
	SingingGameType    SingingGameType        `protobuf:"varint,3,opt,name=singing_game_type,json=singingGameType,proto3,enum=ga.sing_a_round_logic.SingingGameType" json:"singing_game_type,omitempty"`
	NewSingingGameType uint32                 `protobuf:"varint,16,opt,name=new_singing_game_type,json=newSingingGameType,proto3" json:"new_singing_game_type,omitempty"`
	SongCountdown      uint32                 `protobuf:"varint,4,opt,name=song_countdown,json=songCountdown,proto3" json:"song_countdown,omitempty"`
	MaxLifeCount       uint32                 `protobuf:"varint,5,opt,name=max_life_count,json=maxLifeCount,proto3" json:"max_life_count,omitempty"`
	CurRoundId         uint32                 `protobuf:"varint,6,opt,name=cur_round_id,json=curRoundId,proto3" json:"cur_round_id,omitempty"`
	IsPlaying          bool                   `protobuf:"varint,7,opt,name=is_playing,json=isPlaying,proto3" json:"is_playing,omitempty"`
	Stage              SingingGameRoundStage  `protobuf:"varint,8,opt,name=stage,proto3,enum=ga.sing_a_round_logic.SingingGameRoundStage" json:"stage,omitempty"`
	StageUpdatedAt     uint32                 `protobuf:"varint,9,opt,name=stage_updated_at,json=stageUpdatedAt,proto3" json:"stage_updated_at,omitempty"`
	CurSongId          string                 `protobuf:"bytes,10,opt,name=cur_song_id,json=curSongId,proto3" json:"cur_song_id,omitempty"`
	CurSongIndex       uint32                 `protobuf:"varint,11,opt,name=cur_song_index,json=curSongIndex,proto3" json:"cur_song_index,omitempty"`
	Positions          []*SingingGamePosition `protobuf:"bytes,12,rep,name=positions,proto3" json:"positions,omitempty"`
	Version            uint32                 `protobuf:"varint,13,opt,name=version,proto3" json:"version,omitempty"`
	MinimumPlayerNum   uint32                 `protobuf:"varint,14,opt,name=minimum_player_num,json=minimumPlayerNum,proto3" json:"minimum_player_num,omitempty"`
	ControllerId       uint32                 `protobuf:"varint,15,opt,name=controller_id,json=controllerId,proto3" json:"controller_id,omitempty"`
	OfficialGroupInfo  string                 `protobuf:"bytes,17,opt,name=official_group_info,json=officialGroupInfo,proto3" json:"official_group_info,omitempty"`
	// Types that are valid to be assigned to Extra:
	//	*SingingGameChannelInfo_PassThroughExtra
	Extra                isSingingGameChannelInfo_Extra `protobuf_oneof:"extra"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *SingingGameChannelInfo) Reset()         { *m = SingingGameChannelInfo{} }
func (m *SingingGameChannelInfo) String() string { return proto.CompactTextString(m) }
func (*SingingGameChannelInfo) ProtoMessage()    {}
func (*SingingGameChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{6}
}
func (m *SingingGameChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameChannelInfo.Unmarshal(m, b)
}
func (m *SingingGameChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameChannelInfo.Marshal(b, m, deterministic)
}
func (dst *SingingGameChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameChannelInfo.Merge(dst, src)
}
func (m *SingingGameChannelInfo) XXX_Size() int {
	return xxx_messageInfo_SingingGameChannelInfo.Size(m)
}
func (m *SingingGameChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameChannelInfo proto.InternalMessageInfo

func (m *SingingGameChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameChannelInfo) GetMemberLimit() uint32 {
	if m != nil {
		return m.MemberLimit
	}
	return 0
}

func (m *SingingGameChannelInfo) GetSingingGameType() SingingGameType {
	if m != nil {
		return m.SingingGameType
	}
	return SingingGameType_SingingGameTypeUndefined
}

func (m *SingingGameChannelInfo) GetNewSingingGameType() uint32 {
	if m != nil {
		return m.NewSingingGameType
	}
	return 0
}

func (m *SingingGameChannelInfo) GetSongCountdown() uint32 {
	if m != nil {
		return m.SongCountdown
	}
	return 0
}

func (m *SingingGameChannelInfo) GetMaxLifeCount() uint32 {
	if m != nil {
		return m.MaxLifeCount
	}
	return 0
}

func (m *SingingGameChannelInfo) GetCurRoundId() uint32 {
	if m != nil {
		return m.CurRoundId
	}
	return 0
}

func (m *SingingGameChannelInfo) GetIsPlaying() bool {
	if m != nil {
		return m.IsPlaying
	}
	return false
}

func (m *SingingGameChannelInfo) GetStage() SingingGameRoundStage {
	if m != nil {
		return m.Stage
	}
	return SingingGameRoundStage_SingingGameRoundStageUndefined
}

func (m *SingingGameChannelInfo) GetStageUpdatedAt() uint32 {
	if m != nil {
		return m.StageUpdatedAt
	}
	return 0
}

func (m *SingingGameChannelInfo) GetCurSongId() string {
	if m != nil {
		return m.CurSongId
	}
	return ""
}

func (m *SingingGameChannelInfo) GetCurSongIndex() uint32 {
	if m != nil {
		return m.CurSongIndex
	}
	return 0
}

func (m *SingingGameChannelInfo) GetPositions() []*SingingGamePosition {
	if m != nil {
		return m.Positions
	}
	return nil
}

func (m *SingingGameChannelInfo) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *SingingGameChannelInfo) GetMinimumPlayerNum() uint32 {
	if m != nil {
		return m.MinimumPlayerNum
	}
	return 0
}

func (m *SingingGameChannelInfo) GetControllerId() uint32 {
	if m != nil {
		return m.ControllerId
	}
	return 0
}

func (m *SingingGameChannelInfo) GetOfficialGroupInfo() string {
	if m != nil {
		return m.OfficialGroupInfo
	}
	return ""
}

type isSingingGameChannelInfo_Extra interface {
	isSingingGameChannelInfo_Extra()
}

type SingingGameChannelInfo_PassThroughExtra struct {
	PassThroughExtra *PassThroughExtra `protobuf:"bytes,18,opt,name=pass_through_extra,json=passThroughExtra,proto3,oneof"`
}

func (*SingingGameChannelInfo_PassThroughExtra) isSingingGameChannelInfo_Extra() {}

func (m *SingingGameChannelInfo) GetExtra() isSingingGameChannelInfo_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (m *SingingGameChannelInfo) GetPassThroughExtra() *PassThroughExtra {
	if x, ok := m.GetExtra().(*SingingGameChannelInfo_PassThroughExtra); ok {
		return x.PassThroughExtra
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*SingingGameChannelInfo) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _SingingGameChannelInfo_OneofMarshaler, _SingingGameChannelInfo_OneofUnmarshaler, _SingingGameChannelInfo_OneofSizer, []interface{}{
		(*SingingGameChannelInfo_PassThroughExtra)(nil),
	}
}

func _SingingGameChannelInfo_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*SingingGameChannelInfo)
	// extra
	switch x := m.Extra.(type) {
	case *SingingGameChannelInfo_PassThroughExtra:
		b.EncodeVarint(18<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PassThroughExtra); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("SingingGameChannelInfo.Extra has unexpected type %T", x)
	}
	return nil
}

func _SingingGameChannelInfo_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*SingingGameChannelInfo)
	switch tag {
	case 18: // extra.pass_through_extra
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PassThroughExtra)
		err := b.DecodeMessage(msg)
		m.Extra = &SingingGameChannelInfo_PassThroughExtra{msg}
		return true, err
	default:
		return false, nil
	}
}

func _SingingGameChannelInfo_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*SingingGameChannelInfo)
	// extra
	switch x := m.Extra.(type) {
	case *SingingGameChannelInfo_PassThroughExtra:
		s := proto.Size(x.PassThroughExtra)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type PassThroughExtra struct {
	CurSkipCount         uint32   `protobuf:"varint,1,opt,name=cur_skip_count,json=curSkipCount,proto3" json:"cur_skip_count,omitempty"`
	MaxSkipCount         uint32   `protobuf:"varint,2,opt,name=max_skip_count,json=maxSkipCount,proto3" json:"max_skip_count,omitempty"`
	CurSongIndex         uint32   `protobuf:"varint,3,opt,name=cur_song_index,json=curSongIndex,proto3" json:"cur_song_index,omitempty"`
	PassThroughCount     uint32   `protobuf:"varint,4,opt,name=pass_through_count,json=passThroughCount,proto3" json:"pass_through_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PassThroughExtra) Reset()         { *m = PassThroughExtra{} }
func (m *PassThroughExtra) String() string { return proto.CompactTextString(m) }
func (*PassThroughExtra) ProtoMessage()    {}
func (*PassThroughExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{7}
}
func (m *PassThroughExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PassThroughExtra.Unmarshal(m, b)
}
func (m *PassThroughExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PassThroughExtra.Marshal(b, m, deterministic)
}
func (dst *PassThroughExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PassThroughExtra.Merge(dst, src)
}
func (m *PassThroughExtra) XXX_Size() int {
	return xxx_messageInfo_PassThroughExtra.Size(m)
}
func (m *PassThroughExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_PassThroughExtra.DiscardUnknown(m)
}

var xxx_messageInfo_PassThroughExtra proto.InternalMessageInfo

func (m *PassThroughExtra) GetCurSkipCount() uint32 {
	if m != nil {
		return m.CurSkipCount
	}
	return 0
}

func (m *PassThroughExtra) GetMaxSkipCount() uint32 {
	if m != nil {
		return m.MaxSkipCount
	}
	return 0
}

func (m *PassThroughExtra) GetCurSongIndex() uint32 {
	if m != nil {
		return m.CurSongIndex
	}
	return 0
}

func (m *PassThroughExtra) GetPassThroughCount() uint32 {
	if m != nil {
		return m.PassThroughCount
	}
	return 0
}

type SingingGamePosition struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string              `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string              `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Index                uint32              `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	Prepared             bool                `protobuf:"varint,5,opt,name=prepared,proto3" json:"prepared,omitempty"`
	LeftLifeCount        uint32              `protobuf:"varint,6,opt,name=left_life_count,json=leftLifeCount,proto3" json:"left_life_count,omitempty"`
	UserType             SingingGameUserType `protobuf:"varint,7,opt,name=user_type,json=userType,proto3,enum=ga.sing_a_round_logic.SingingGameUserType" json:"user_type,omitempty"`
	Score                uint32              `protobuf:"varint,8,opt,name=score,proto3" json:"score,omitempty"`
	UserSetImageId       string              `protobuf:"bytes,9,opt,name=user_set_image_id,json=userSetImageId,proto3" json:"user_set_image_id,omitempty"`
	DecorationId         string              `protobuf:"bytes,10,opt,name=decoration_id,json=decorationId,proto3" json:"decoration_id,omitempty"`
	Sex                  uint32              `protobuf:"varint,11,opt,name=sex,proto3" json:"sex,omitempty"`
	HaveRunAway          bool                `protobuf:"varint,12,opt,name=have_run_away,json=haveRunAway,proto3" json:"have_run_away,omitempty"`
	PrecentorTag         string              `protobuf:"bytes,13,opt,name=precentor_tag,json=precentorTag,proto3" json:"precentor_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SingingGamePosition) Reset()         { *m = SingingGamePosition{} }
func (m *SingingGamePosition) String() string { return proto.CompactTextString(m) }
func (*SingingGamePosition) ProtoMessage()    {}
func (*SingingGamePosition) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{8}
}
func (m *SingingGamePosition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGamePosition.Unmarshal(m, b)
}
func (m *SingingGamePosition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGamePosition.Marshal(b, m, deterministic)
}
func (dst *SingingGamePosition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGamePosition.Merge(dst, src)
}
func (m *SingingGamePosition) XXX_Size() int {
	return xxx_messageInfo_SingingGamePosition.Size(m)
}
func (m *SingingGamePosition) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGamePosition.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGamePosition proto.InternalMessageInfo

func (m *SingingGamePosition) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingingGamePosition) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *SingingGamePosition) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SingingGamePosition) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *SingingGamePosition) GetPrepared() bool {
	if m != nil {
		return m.Prepared
	}
	return false
}

func (m *SingingGamePosition) GetLeftLifeCount() uint32 {
	if m != nil {
		return m.LeftLifeCount
	}
	return 0
}

func (m *SingingGamePosition) GetUserType() SingingGameUserType {
	if m != nil {
		return m.UserType
	}
	return SingingGameUserType_SingingGameUserTypeUndefined
}

func (m *SingingGamePosition) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *SingingGamePosition) GetUserSetImageId() string {
	if m != nil {
		return m.UserSetImageId
	}
	return ""
}

func (m *SingingGamePosition) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

func (m *SingingGamePosition) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *SingingGamePosition) GetHaveRunAway() bool {
	if m != nil {
		return m.HaveRunAway
	}
	return false
}

func (m *SingingGamePosition) GetPrecentorTag() string {
	if m != nil {
		return m.PrecentorTag
	}
	return ""
}

// 获取开始倒计时
type GetSingingGameCountdownReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSingingGameCountdownReq) Reset()         { *m = GetSingingGameCountdownReq{} }
func (m *GetSingingGameCountdownReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameCountdownReq) ProtoMessage()    {}
func (*GetSingingGameCountdownReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{9}
}
func (m *GetSingingGameCountdownReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameCountdownReq.Unmarshal(m, b)
}
func (m *GetSingingGameCountdownReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameCountdownReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameCountdownReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameCountdownReq.Merge(dst, src)
}
func (m *GetSingingGameCountdownReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameCountdownReq.Size(m)
}
func (m *GetSingingGameCountdownReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameCountdownReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameCountdownReq proto.InternalMessageInfo

func (m *GetSingingGameCountdownReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSingingGameCountdownReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetSingingGameCountdownReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type GetSingingGameCountdownResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Countdown            uint32        `protobuf:"varint,2,opt,name=countdown,proto3" json:"countdown,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSingingGameCountdownResp) Reset()         { *m = GetSingingGameCountdownResp{} }
func (m *GetSingingGameCountdownResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameCountdownResp) ProtoMessage()    {}
func (*GetSingingGameCountdownResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{10}
}
func (m *GetSingingGameCountdownResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameCountdownResp.Unmarshal(m, b)
}
func (m *GetSingingGameCountdownResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameCountdownResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameCountdownResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameCountdownResp.Merge(dst, src)
}
func (m *GetSingingGameCountdownResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameCountdownResp.Size(m)
}
func (m *GetSingingGameCountdownResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameCountdownResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameCountdownResp proto.InternalMessageInfo

func (m *GetSingingGameCountdownResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSingingGameCountdownResp) GetCountdown() uint32 {
	if m != nil {
		return m.Countdown
	}
	return 0
}

// 准备（开始游戏）
type StartSingingGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartSingingGameReq) Reset()         { *m = StartSingingGameReq{} }
func (m *StartSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*StartSingingGameReq) ProtoMessage()    {}
func (*StartSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{11}
}
func (m *StartSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartSingingGameReq.Unmarshal(m, b)
}
func (m *StartSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *StartSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartSingingGameReq.Merge(dst, src)
}
func (m *StartSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_StartSingingGameReq.Size(m)
}
func (m *StartSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartSingingGameReq proto.InternalMessageInfo

func (m *StartSingingGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartSingingGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartSingingGameReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type StartSingingGameResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *StartSingingGameResp) Reset()         { *m = StartSingingGameResp{} }
func (m *StartSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*StartSingingGameResp) ProtoMessage()    {}
func (*StartSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{12}
}
func (m *StartSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartSingingGameResp.Unmarshal(m, b)
}
func (m *StartSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *StartSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartSingingGameResp.Merge(dst, src)
}
func (m *StartSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_StartSingingGameResp.Size(m)
}
func (m *StartSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartSingingGameResp proto.InternalMessageInfo

func (m *StartSingingGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *StartSingingGameResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 取消准备（UGC房）
type CancelSingingGamePreparationReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelSingingGamePreparationReq) Reset()         { *m = CancelSingingGamePreparationReq{} }
func (m *CancelSingingGamePreparationReq) String() string { return proto.CompactTextString(m) }
func (*CancelSingingGamePreparationReq) ProtoMessage()    {}
func (*CancelSingingGamePreparationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{13}
}
func (m *CancelSingingGamePreparationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelSingingGamePreparationReq.Unmarshal(m, b)
}
func (m *CancelSingingGamePreparationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelSingingGamePreparationReq.Marshal(b, m, deterministic)
}
func (dst *CancelSingingGamePreparationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelSingingGamePreparationReq.Merge(dst, src)
}
func (m *CancelSingingGamePreparationReq) XXX_Size() int {
	return xxx_messageInfo_CancelSingingGamePreparationReq.Size(m)
}
func (m *CancelSingingGamePreparationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelSingingGamePreparationReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelSingingGamePreparationReq proto.InternalMessageInfo

func (m *CancelSingingGamePreparationReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelSingingGamePreparationReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CancelSingingGamePreparationReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type CancelSingingGamePreparationResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelSingingGamePreparationResp) Reset()         { *m = CancelSingingGamePreparationResp{} }
func (m *CancelSingingGamePreparationResp) String() string { return proto.CompactTextString(m) }
func (*CancelSingingGamePreparationResp) ProtoMessage()    {}
func (*CancelSingingGamePreparationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{14}
}
func (m *CancelSingingGamePreparationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelSingingGamePreparationResp.Unmarshal(m, b)
}
func (m *CancelSingingGamePreparationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelSingingGamePreparationResp.Marshal(b, m, deterministic)
}
func (dst *CancelSingingGamePreparationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelSingingGamePreparationResp.Merge(dst, src)
}
func (m *CancelSingingGamePreparationResp) XXX_Size() int {
	return xxx_messageInfo_CancelSingingGamePreparationResp.Size(m)
}
func (m *CancelSingingGamePreparationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelSingingGamePreparationResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelSingingGamePreparationResp proto.InternalMessageInfo

func (m *CancelSingingGamePreparationResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 开始游戏（UGC房）
type StartUgcChannelSingingGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartUgcChannelSingingGameReq) Reset()         { *m = StartUgcChannelSingingGameReq{} }
func (m *StartUgcChannelSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*StartUgcChannelSingingGameReq) ProtoMessage()    {}
func (*StartUgcChannelSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{15}
}
func (m *StartUgcChannelSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartUgcChannelSingingGameReq.Unmarshal(m, b)
}
func (m *StartUgcChannelSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartUgcChannelSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *StartUgcChannelSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartUgcChannelSingingGameReq.Merge(dst, src)
}
func (m *StartUgcChannelSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_StartUgcChannelSingingGameReq.Size(m)
}
func (m *StartUgcChannelSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartUgcChannelSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartUgcChannelSingingGameReq proto.InternalMessageInfo

func (m *StartUgcChannelSingingGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartUgcChannelSingingGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartUgcChannelSingingGameReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type StartUgcChannelSingingGameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartUgcChannelSingingGameResp) Reset()         { *m = StartUgcChannelSingingGameResp{} }
func (m *StartUgcChannelSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*StartUgcChannelSingingGameResp) ProtoMessage()    {}
func (*StartUgcChannelSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{16}
}
func (m *StartUgcChannelSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartUgcChannelSingingGameResp.Unmarshal(m, b)
}
func (m *StartUgcChannelSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartUgcChannelSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *StartUgcChannelSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartUgcChannelSingingGameResp.Merge(dst, src)
}
func (m *StartUgcChannelSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_StartUgcChannelSingingGameResp.Size(m)
}
func (m *StartUgcChannelSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartUgcChannelSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartUgcChannelSingingGameResp proto.InternalMessageInfo

func (m *StartUgcChannelSingingGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 投票push
type SingingGameVoteNotify struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Styles               []*SingingGameSongStyle `protobuf:"bytes,2,rep,name=styles,proto3" json:"styles,omitempty"`
	VoteCountdown        uint32                  `protobuf:"varint,3,opt,name=vote_countdown,json=voteCountdown,proto3" json:"vote_countdown,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingingGameVoteNotify) Reset()         { *m = SingingGameVoteNotify{} }
func (m *SingingGameVoteNotify) String() string { return proto.CompactTextString(m) }
func (*SingingGameVoteNotify) ProtoMessage()    {}
func (*SingingGameVoteNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{17}
}
func (m *SingingGameVoteNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameVoteNotify.Unmarshal(m, b)
}
func (m *SingingGameVoteNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameVoteNotify.Marshal(b, m, deterministic)
}
func (dst *SingingGameVoteNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameVoteNotify.Merge(dst, src)
}
func (m *SingingGameVoteNotify) XXX_Size() int {
	return xxx_messageInfo_SingingGameVoteNotify.Size(m)
}
func (m *SingingGameVoteNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameVoteNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameVoteNotify proto.InternalMessageInfo

func (m *SingingGameVoteNotify) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SingingGameVoteNotify) GetStyles() []*SingingGameSongStyle {
	if m != nil {
		return m.Styles
	}
	return nil
}

func (m *SingingGameVoteNotify) GetVoteCountdown() uint32 {
	if m != nil {
		return m.VoteCountdown
	}
	return 0
}

type SingingGameSongStyle struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	SupportedNum         uint32   `protobuf:"varint,3,opt,name=supported_num,json=supportedNum,proto3" json:"supported_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameSongStyle) Reset()         { *m = SingingGameSongStyle{} }
func (m *SingingGameSongStyle) String() string { return proto.CompactTextString(m) }
func (*SingingGameSongStyle) ProtoMessage()    {}
func (*SingingGameSongStyle) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{18}
}
func (m *SingingGameSongStyle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameSongStyle.Unmarshal(m, b)
}
func (m *SingingGameSongStyle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameSongStyle.Marshal(b, m, deterministic)
}
func (dst *SingingGameSongStyle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameSongStyle.Merge(dst, src)
}
func (m *SingingGameSongStyle) XXX_Size() int {
	return xxx_messageInfo_SingingGameSongStyle.Size(m)
}
func (m *SingingGameSongStyle) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameSongStyle.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameSongStyle proto.InternalMessageInfo

func (m *SingingGameSongStyle) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SingingGameSongStyle) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SingingGameSongStyle) GetSupportedNum() uint32 {
	if m != nil {
		return m.SupportedNum
	}
	return 0
}

// 预加载push
type SingingGamePreloadNotify struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Style                *SingingGameSongStyle   `protobuf:"bytes,2,opt,name=style,proto3" json:"style,omitempty"`
	Songs                []*SingingGameSong      `protobuf:"bytes,3,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingingGamePreloadNotify) Reset()         { *m = SingingGamePreloadNotify{} }
func (m *SingingGamePreloadNotify) String() string { return proto.CompactTextString(m) }
func (*SingingGamePreloadNotify) ProtoMessage()    {}
func (*SingingGamePreloadNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{19}
}
func (m *SingingGamePreloadNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGamePreloadNotify.Unmarshal(m, b)
}
func (m *SingingGamePreloadNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGamePreloadNotify.Marshal(b, m, deterministic)
}
func (dst *SingingGamePreloadNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGamePreloadNotify.Merge(dst, src)
}
func (m *SingingGamePreloadNotify) XXX_Size() int {
	return xxx_messageInfo_SingingGamePreloadNotify.Size(m)
}
func (m *SingingGamePreloadNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGamePreloadNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGamePreloadNotify proto.InternalMessageInfo

func (m *SingingGamePreloadNotify) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SingingGamePreloadNotify) GetStyle() *SingingGameSongStyle {
	if m != nil {
		return m.Style
	}
	return nil
}

func (m *SingingGamePreloadNotify) GetSongs() []*SingingGameSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

// 投票
type SingingGameVoteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	StyleId              string       `protobuf:"bytes,4,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SingingGameVoteReq) Reset()         { *m = SingingGameVoteReq{} }
func (m *SingingGameVoteReq) String() string { return proto.CompactTextString(m) }
func (*SingingGameVoteReq) ProtoMessage()    {}
func (*SingingGameVoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{20}
}
func (m *SingingGameVoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameVoteReq.Unmarshal(m, b)
}
func (m *SingingGameVoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameVoteReq.Marshal(b, m, deterministic)
}
func (dst *SingingGameVoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameVoteReq.Merge(dst, src)
}
func (m *SingingGameVoteReq) XXX_Size() int {
	return xxx_messageInfo_SingingGameVoteReq.Size(m)
}
func (m *SingingGameVoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameVoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameVoteReq proto.InternalMessageInfo

func (m *SingingGameVoteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SingingGameVoteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameVoteReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *SingingGameVoteReq) GetStyleId() string {
	if m != nil {
		return m.StyleId
	}
	return ""
}

type SingingGameVoteResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Styles               []*SingingGameSongStyle `protobuf:"bytes,3,rep,name=styles,proto3" json:"styles,omitempty"`
	Style                *SingingGameSongStyle   `protobuf:"bytes,4,opt,name=style,proto3" json:"style,omitempty"`
	Songs                []*SingingGameSong      `protobuf:"bytes,5,rep,name=songs,proto3" json:"songs,omitempty"`
	VoteCountdown        uint32                  `protobuf:"varint,6,opt,name=vote_countdown,json=voteCountdown,proto3" json:"vote_countdown,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingingGameVoteResp) Reset()         { *m = SingingGameVoteResp{} }
func (m *SingingGameVoteResp) String() string { return proto.CompactTextString(m) }
func (*SingingGameVoteResp) ProtoMessage()    {}
func (*SingingGameVoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{21}
}
func (m *SingingGameVoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameVoteResp.Unmarshal(m, b)
}
func (m *SingingGameVoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameVoteResp.Marshal(b, m, deterministic)
}
func (dst *SingingGameVoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameVoteResp.Merge(dst, src)
}
func (m *SingingGameVoteResp) XXX_Size() int {
	return xxx_messageInfo_SingingGameVoteResp.Size(m)
}
func (m *SingingGameVoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameVoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameVoteResp proto.InternalMessageInfo

func (m *SingingGameVoteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SingingGameVoteResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SingingGameVoteResp) GetStyles() []*SingingGameSongStyle {
	if m != nil {
		return m.Styles
	}
	return nil
}

func (m *SingingGameVoteResp) GetStyle() *SingingGameSongStyle {
	if m != nil {
		return m.Style
	}
	return nil
}

func (m *SingingGameVoteResp) GetSongs() []*SingingGameSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *SingingGameVoteResp) GetVoteCountdown() uint32 {
	if m != nil {
		return m.VoteCountdown
	}
	return 0
}

// 完成资源加载
type AccomplishLoadingResReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AccomplishLoadingResReq) Reset()         { *m = AccomplishLoadingResReq{} }
func (m *AccomplishLoadingResReq) String() string { return proto.CompactTextString(m) }
func (*AccomplishLoadingResReq) ProtoMessage()    {}
func (*AccomplishLoadingResReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{22}
}
func (m *AccomplishLoadingResReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccomplishLoadingResReq.Unmarshal(m, b)
}
func (m *AccomplishLoadingResReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccomplishLoadingResReq.Marshal(b, m, deterministic)
}
func (dst *AccomplishLoadingResReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccomplishLoadingResReq.Merge(dst, src)
}
func (m *AccomplishLoadingResReq) XXX_Size() int {
	return xxx_messageInfo_AccomplishLoadingResReq.Size(m)
}
func (m *AccomplishLoadingResReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AccomplishLoadingResReq.DiscardUnknown(m)
}

var xxx_messageInfo_AccomplishLoadingResReq proto.InternalMessageInfo

func (m *AccomplishLoadingResReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AccomplishLoadingResReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AccomplishLoadingResReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type AccomplishLoadingResResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AccomplishLoadingResResp) Reset()         { *m = AccomplishLoadingResResp{} }
func (m *AccomplishLoadingResResp) String() string { return proto.CompactTextString(m) }
func (*AccomplishLoadingResResp) ProtoMessage()    {}
func (*AccomplishLoadingResResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{23}
}
func (m *AccomplishLoadingResResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccomplishLoadingResResp.Unmarshal(m, b)
}
func (m *AccomplishLoadingResResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccomplishLoadingResResp.Marshal(b, m, deterministic)
}
func (dst *AccomplishLoadingResResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccomplishLoadingResResp.Merge(dst, src)
}
func (m *AccomplishLoadingResResp) XXX_Size() int {
	return xxx_messageInfo_AccomplishLoadingResResp.Size(m)
}
func (m *AccomplishLoadingResResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AccomplishLoadingResResp.DiscardUnknown(m)
}

var xxx_messageInfo_AccomplishLoadingResResp proto.InternalMessageInfo

func (m *AccomplishLoadingResResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *AccomplishLoadingResResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 排行榜
type GetSingingGameRankListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Type                 RankListType `protobuf:"varint,2,opt,name=type,proto3,enum=ga.sing_a_round_logic.RankListType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSingingGameRankListReq) Reset()         { *m = GetSingingGameRankListReq{} }
func (m *GetSingingGameRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRankListReq) ProtoMessage()    {}
func (*GetSingingGameRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{24}
}
func (m *GetSingingGameRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRankListReq.Unmarshal(m, b)
}
func (m *GetSingingGameRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRankListReq.Merge(dst, src)
}
func (m *GetSingingGameRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRankListReq.Size(m)
}
func (m *GetSingingGameRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRankListReq proto.InternalMessageInfo

func (m *GetSingingGameRankListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSingingGameRankListReq) GetType() RankListType {
	if m != nil {
		return m.Type
	}
	return RankListType_RankListTypeUndefined
}

type GetSingingGameRankListResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*SingingGameRankListItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	PersonalRank         *SingingGameRankListItem   `protobuf:"bytes,3,opt,name=personal_rank,json=personalRank,proto3" json:"personal_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetSingingGameRankListResp) Reset()         { *m = GetSingingGameRankListResp{} }
func (m *GetSingingGameRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRankListResp) ProtoMessage()    {}
func (*GetSingingGameRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{25}
}
func (m *GetSingingGameRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRankListResp.Unmarshal(m, b)
}
func (m *GetSingingGameRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRankListResp.Merge(dst, src)
}
func (m *GetSingingGameRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRankListResp.Size(m)
}
func (m *GetSingingGameRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRankListResp proto.InternalMessageInfo

func (m *GetSingingGameRankListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSingingGameRankListResp) GetList() []*SingingGameRankListItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetSingingGameRankListResp) GetPersonalRank() *SingingGameRankListItem {
	if m != nil {
		return m.PersonalRank
	}
	return nil
}

type SingingGameRankListItem struct {
	Rank                 uint32                `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`
	Accounts             []string              `protobuf:"bytes,2,rep,name=accounts,proto3" json:"accounts,omitempty"`
	PassThroughCount     uint32                `protobuf:"varint,3,opt,name=pass_through_count,json=passThroughCount,proto3" json:"pass_through_count,omitempty"`
	UpdatedAt            uint32                `protobuf:"varint,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	ResultType           SingingGameResultType `protobuf:"varint,5,opt,name=result_type,json=resultType,proto3,enum=ga.sing_a_round_logic.SingingGameResultType" json:"result_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SingingGameRankListItem) Reset()         { *m = SingingGameRankListItem{} }
func (m *SingingGameRankListItem) String() string { return proto.CompactTextString(m) }
func (*SingingGameRankListItem) ProtoMessage()    {}
func (*SingingGameRankListItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{26}
}
func (m *SingingGameRankListItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameRankListItem.Unmarshal(m, b)
}
func (m *SingingGameRankListItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameRankListItem.Marshal(b, m, deterministic)
}
func (dst *SingingGameRankListItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameRankListItem.Merge(dst, src)
}
func (m *SingingGameRankListItem) XXX_Size() int {
	return xxx_messageInfo_SingingGameRankListItem.Size(m)
}
func (m *SingingGameRankListItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameRankListItem.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameRankListItem proto.InternalMessageInfo

func (m *SingingGameRankListItem) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SingingGameRankListItem) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *SingingGameRankListItem) GetPassThroughCount() uint32 {
	if m != nil {
		return m.PassThroughCount
	}
	return 0
}

func (m *SingingGameRankListItem) GetUpdatedAt() uint32 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *SingingGameRankListItem) GetResultType() SingingGameResultType {
	if m != nil {
		return m.ResultType
	}
	return SingingGameResultType_SingingGameResultTypeUndefined
}

// 通关模式游戏记录
type GetSingingGameRecordListReq struct {
	BaseReq              *app.BaseReq         `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Count                uint32               `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *SingingGameLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetSingingGameRecordListReq) Reset()         { *m = GetSingingGameRecordListReq{} }
func (m *GetSingingGameRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRecordListReq) ProtoMessage()    {}
func (*GetSingingGameRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{27}
}
func (m *GetSingingGameRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRecordListReq.Unmarshal(m, b)
}
func (m *GetSingingGameRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRecordListReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRecordListReq.Merge(dst, src)
}
func (m *GetSingingGameRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRecordListReq.Size(m)
}
func (m *GetSingingGameRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRecordListReq proto.InternalMessageInfo

func (m *GetSingingGameRecordListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSingingGameRecordListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetSingingGameRecordListReq) GetLoadMore() *SingingGameLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetSingingGameRecordListResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*SingingGameRankListItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	LoadMore             *SingingGameLoadMore       `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetSingingGameRecordListResp) Reset()         { *m = GetSingingGameRecordListResp{} }
func (m *GetSingingGameRecordListResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRecordListResp) ProtoMessage()    {}
func (*GetSingingGameRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{28}
}
func (m *GetSingingGameRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRecordListResp.Unmarshal(m, b)
}
func (m *GetSingingGameRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRecordListResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRecordListResp.Merge(dst, src)
}
func (m *GetSingingGameRecordListResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRecordListResp.Size(m)
}
func (m *GetSingingGameRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRecordListResp proto.InternalMessageInfo

func (m *GetSingingGameRecordListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSingingGameRecordListResp) GetList() []*SingingGameRankListItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetSingingGameRecordListResp) GetLoadMore() *SingingGameLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type SingingGameLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameLoadMore) Reset()         { *m = SingingGameLoadMore{} }
func (m *SingingGameLoadMore) String() string { return proto.CompactTextString(m) }
func (*SingingGameLoadMore) ProtoMessage()    {}
func (*SingingGameLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{29}
}
func (m *SingingGameLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameLoadMore.Unmarshal(m, b)
}
func (m *SingingGameLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameLoadMore.Marshal(b, m, deterministic)
}
func (dst *SingingGameLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameLoadMore.Merge(dst, src)
}
func (m *SingingGameLoadMore) XXX_Size() int {
	return xxx_messageInfo_SingingGameLoadMore.Size(m)
}
func (m *SingingGameLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameLoadMore proto.InternalMessageInfo

func (m *SingingGameLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *SingingGameLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

// 开始抢唱push
type GrabSingingGameMicNotify struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	BtnShowedAt          uint32                  `protobuf:"varint,2,opt,name=btn_showed_at,json=btnShowedAt,proto3" json:"btn_showed_at,omitempty"`
	BtnHiddenAt          uint32                  `protobuf:"varint,3,opt,name=btn_hidden_at,json=btnHiddenAt,proto3" json:"btn_hidden_at,omitempty"`
	BtnDisplayTime       uint32                  `protobuf:"varint,4,opt,name=btn_display_time,json=btnDisplayTime,proto3" json:"btn_display_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GrabSingingGameMicNotify) Reset()         { *m = GrabSingingGameMicNotify{} }
func (m *GrabSingingGameMicNotify) String() string { return proto.CompactTextString(m) }
func (*GrabSingingGameMicNotify) ProtoMessage()    {}
func (*GrabSingingGameMicNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{30}
}
func (m *GrabSingingGameMicNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabSingingGameMicNotify.Unmarshal(m, b)
}
func (m *GrabSingingGameMicNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabSingingGameMicNotify.Marshal(b, m, deterministic)
}
func (dst *GrabSingingGameMicNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabSingingGameMicNotify.Merge(dst, src)
}
func (m *GrabSingingGameMicNotify) XXX_Size() int {
	return xxx_messageInfo_GrabSingingGameMicNotify.Size(m)
}
func (m *GrabSingingGameMicNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabSingingGameMicNotify.DiscardUnknown(m)
}

var xxx_messageInfo_GrabSingingGameMicNotify proto.InternalMessageInfo

func (m *GrabSingingGameMicNotify) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *GrabSingingGameMicNotify) GetBtnShowedAt() uint32 {
	if m != nil {
		return m.BtnShowedAt
	}
	return 0
}

func (m *GrabSingingGameMicNotify) GetBtnHiddenAt() uint32 {
	if m != nil {
		return m.BtnHiddenAt
	}
	return 0
}

func (m *GrabSingingGameMicNotify) GetBtnDisplayTime() uint32 {
	if m != nil {
		return m.BtnDisplayTime
	}
	return 0
}

// 抢唱
type GrabSingingGameMicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string       `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GrabSingingGameMicReq) Reset()         { *m = GrabSingingGameMicReq{} }
func (m *GrabSingingGameMicReq) String() string { return proto.CompactTextString(m) }
func (*GrabSingingGameMicReq) ProtoMessage()    {}
func (*GrabSingingGameMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{31}
}
func (m *GrabSingingGameMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabSingingGameMicReq.Unmarshal(m, b)
}
func (m *GrabSingingGameMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabSingingGameMicReq.Marshal(b, m, deterministic)
}
func (dst *GrabSingingGameMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabSingingGameMicReq.Merge(dst, src)
}
func (m *GrabSingingGameMicReq) XXX_Size() int {
	return xxx_messageInfo_GrabSingingGameMicReq.Size(m)
}
func (m *GrabSingingGameMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabSingingGameMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_GrabSingingGameMicReq proto.InternalMessageInfo

func (m *GrabSingingGameMicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GrabSingingGameMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GrabSingingGameMicReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *GrabSingingGameMicReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type GrabSingingGameMicResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	IsSuccess            bool                    `protobuf:"varint,3,opt,name=is_success,json=isSuccess,proto3" json:"is_success,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GrabSingingGameMicResp) Reset()         { *m = GrabSingingGameMicResp{} }
func (m *GrabSingingGameMicResp) String() string { return proto.CompactTextString(m) }
func (*GrabSingingGameMicResp) ProtoMessage()    {}
func (*GrabSingingGameMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{32}
}
func (m *GrabSingingGameMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabSingingGameMicResp.Unmarshal(m, b)
}
func (m *GrabSingingGameMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabSingingGameMicResp.Marshal(b, m, deterministic)
}
func (dst *GrabSingingGameMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabSingingGameMicResp.Merge(dst, src)
}
func (m *GrabSingingGameMicResp) XXX_Size() int {
	return xxx_messageInfo_GrabSingingGameMicResp.Size(m)
}
func (m *GrabSingingGameMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabSingingGameMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_GrabSingingGameMicResp proto.InternalMessageInfo

func (m *GrabSingingGameMicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GrabSingingGameMicResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *GrabSingingGameMicResp) GetIsSuccess() bool {
	if m != nil {
		return m.IsSuccess
	}
	return false
}

// 请求帮唱
type AskForSingingHelpReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string       `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	AutoReq              bool         `protobuf:"varint,5,opt,name=auto_req,json=autoReq,proto3" json:"auto_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AskForSingingHelpReq) Reset()         { *m = AskForSingingHelpReq{} }
func (m *AskForSingingHelpReq) String() string { return proto.CompactTextString(m) }
func (*AskForSingingHelpReq) ProtoMessage()    {}
func (*AskForSingingHelpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{33}
}
func (m *AskForSingingHelpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AskForSingingHelpReq.Unmarshal(m, b)
}
func (m *AskForSingingHelpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AskForSingingHelpReq.Marshal(b, m, deterministic)
}
func (dst *AskForSingingHelpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AskForSingingHelpReq.Merge(dst, src)
}
func (m *AskForSingingHelpReq) XXX_Size() int {
	return xxx_messageInfo_AskForSingingHelpReq.Size(m)
}
func (m *AskForSingingHelpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AskForSingingHelpReq.DiscardUnknown(m)
}

var xxx_messageInfo_AskForSingingHelpReq proto.InternalMessageInfo

func (m *AskForSingingHelpReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AskForSingingHelpReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AskForSingingHelpReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *AskForSingingHelpReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *AskForSingingHelpReq) GetAutoReq() bool {
	if m != nil {
		return m.AutoReq
	}
	return false
}

type AskForSingingHelpResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AskForSingingHelpResp) Reset()         { *m = AskForSingingHelpResp{} }
func (m *AskForSingingHelpResp) String() string { return proto.CompactTextString(m) }
func (*AskForSingingHelpResp) ProtoMessage()    {}
func (*AskForSingingHelpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{34}
}
func (m *AskForSingingHelpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AskForSingingHelpResp.Unmarshal(m, b)
}
func (m *AskForSingingHelpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AskForSingingHelpResp.Marshal(b, m, deterministic)
}
func (dst *AskForSingingHelpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AskForSingingHelpResp.Merge(dst, src)
}
func (m *AskForSingingHelpResp) XXX_Size() int {
	return xxx_messageInfo_AskForSingingHelpResp.Size(m)
}
func (m *AskForSingingHelpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AskForSingingHelpResp.DiscardUnknown(m)
}

var xxx_messageInfo_AskForSingingHelpResp proto.InternalMessageInfo

func (m *AskForSingingHelpResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *AskForSingingHelpResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 开始帮唱push
type SingingHelpNotify struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	BtnShowedAt          uint32                  `protobuf:"varint,2,opt,name=btn_showed_at,json=btnShowedAt,proto3" json:"btn_showed_at,omitempty"`
	BtnHiddenAt          uint32                  `protobuf:"varint,3,opt,name=btn_hidden_at,json=btnHiddenAt,proto3" json:"btn_hidden_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingingHelpNotify) Reset()         { *m = SingingHelpNotify{} }
func (m *SingingHelpNotify) String() string { return proto.CompactTextString(m) }
func (*SingingHelpNotify) ProtoMessage()    {}
func (*SingingHelpNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{35}
}
func (m *SingingHelpNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingHelpNotify.Unmarshal(m, b)
}
func (m *SingingHelpNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingHelpNotify.Marshal(b, m, deterministic)
}
func (dst *SingingHelpNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingHelpNotify.Merge(dst, src)
}
func (m *SingingHelpNotify) XXX_Size() int {
	return xxx_messageInfo_SingingHelpNotify.Size(m)
}
func (m *SingingHelpNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingHelpNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingHelpNotify proto.InternalMessageInfo

func (m *SingingHelpNotify) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SingingHelpNotify) GetBtnShowedAt() uint32 {
	if m != nil {
		return m.BtnShowedAt
	}
	return 0
}

func (m *SingingHelpNotify) GetBtnHiddenAt() uint32 {
	if m != nil {
		return m.BtnHiddenAt
	}
	return 0
}

// 帮唱
type AnswerSingingHelpReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string       `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AnswerSingingHelpReq) Reset()         { *m = AnswerSingingHelpReq{} }
func (m *AnswerSingingHelpReq) String() string { return proto.CompactTextString(m) }
func (*AnswerSingingHelpReq) ProtoMessage()    {}
func (*AnswerSingingHelpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{36}
}
func (m *AnswerSingingHelpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnswerSingingHelpReq.Unmarshal(m, b)
}
func (m *AnswerSingingHelpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnswerSingingHelpReq.Marshal(b, m, deterministic)
}
func (dst *AnswerSingingHelpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnswerSingingHelpReq.Merge(dst, src)
}
func (m *AnswerSingingHelpReq) XXX_Size() int {
	return xxx_messageInfo_AnswerSingingHelpReq.Size(m)
}
func (m *AnswerSingingHelpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AnswerSingingHelpReq.DiscardUnknown(m)
}

var xxx_messageInfo_AnswerSingingHelpReq proto.InternalMessageInfo

func (m *AnswerSingingHelpReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AnswerSingingHelpReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AnswerSingingHelpReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *AnswerSingingHelpReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type AnswerSingingHelpResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	IsSuccess            bool                    `protobuf:"varint,3,opt,name=is_success,json=isSuccess,proto3" json:"is_success,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AnswerSingingHelpResp) Reset()         { *m = AnswerSingingHelpResp{} }
func (m *AnswerSingingHelpResp) String() string { return proto.CompactTextString(m) }
func (*AnswerSingingHelpResp) ProtoMessage()    {}
func (*AnswerSingingHelpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{37}
}
func (m *AnswerSingingHelpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnswerSingingHelpResp.Unmarshal(m, b)
}
func (m *AnswerSingingHelpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnswerSingingHelpResp.Marshal(b, m, deterministic)
}
func (dst *AnswerSingingHelpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnswerSingingHelpResp.Merge(dst, src)
}
func (m *AnswerSingingHelpResp) XXX_Size() int {
	return xxx_messageInfo_AnswerSingingHelpResp.Size(m)
}
func (m *AnswerSingingHelpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AnswerSingingHelpResp.DiscardUnknown(m)
}

var xxx_messageInfo_AnswerSingingHelpResp proto.InternalMessageInfo

func (m *AnswerSingingHelpResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *AnswerSingingHelpResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *AnswerSingingHelpResp) GetIsSuccess() bool {
	if m != nil {
		return m.IsSuccess
	}
	return false
}

// 完成演唱
type AccomplishSingingGameSongReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string       `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AccomplishSingingGameSongReq) Reset()         { *m = AccomplishSingingGameSongReq{} }
func (m *AccomplishSingingGameSongReq) String() string { return proto.CompactTextString(m) }
func (*AccomplishSingingGameSongReq) ProtoMessage()    {}
func (*AccomplishSingingGameSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{38}
}
func (m *AccomplishSingingGameSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccomplishSingingGameSongReq.Unmarshal(m, b)
}
func (m *AccomplishSingingGameSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccomplishSingingGameSongReq.Marshal(b, m, deterministic)
}
func (dst *AccomplishSingingGameSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccomplishSingingGameSongReq.Merge(dst, src)
}
func (m *AccomplishSingingGameSongReq) XXX_Size() int {
	return xxx_messageInfo_AccomplishSingingGameSongReq.Size(m)
}
func (m *AccomplishSingingGameSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AccomplishSingingGameSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_AccomplishSingingGameSongReq proto.InternalMessageInfo

func (m *AccomplishSingingGameSongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AccomplishSingingGameSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AccomplishSingingGameSongReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *AccomplishSingingGameSongReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type AccomplishSingingGameSongResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AccomplishSingingGameSongResp) Reset()         { *m = AccomplishSingingGameSongResp{} }
func (m *AccomplishSingingGameSongResp) String() string { return proto.CompactTextString(m) }
func (*AccomplishSingingGameSongResp) ProtoMessage()    {}
func (*AccomplishSingingGameSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{39}
}
func (m *AccomplishSingingGameSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccomplishSingingGameSongResp.Unmarshal(m, b)
}
func (m *AccomplishSingingGameSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccomplishSingingGameSongResp.Marshal(b, m, deterministic)
}
func (dst *AccomplishSingingGameSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccomplishSingingGameSongResp.Merge(dst, src)
}
func (m *AccomplishSingingGameSongResp) XXX_Size() int {
	return xxx_messageInfo_AccomplishSingingGameSongResp.Size(m)
}
func (m *AccomplishSingingGameSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AccomplishSingingGameSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_AccomplishSingingGameSongResp proto.InternalMessageInfo

func (m *AccomplishSingingGameSongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *AccomplishSingingGameSongResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 上报积分
type ReportSingingGameSongScoreReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string       `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Score                uint32       `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReportSingingGameSongScoreReq) Reset()         { *m = ReportSingingGameSongScoreReq{} }
func (m *ReportSingingGameSongScoreReq) String() string { return proto.CompactTextString(m) }
func (*ReportSingingGameSongScoreReq) ProtoMessage()    {}
func (*ReportSingingGameSongScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{40}
}
func (m *ReportSingingGameSongScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportSingingGameSongScoreReq.Unmarshal(m, b)
}
func (m *ReportSingingGameSongScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportSingingGameSongScoreReq.Marshal(b, m, deterministic)
}
func (dst *ReportSingingGameSongScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportSingingGameSongScoreReq.Merge(dst, src)
}
func (m *ReportSingingGameSongScoreReq) XXX_Size() int {
	return xxx_messageInfo_ReportSingingGameSongScoreReq.Size(m)
}
func (m *ReportSingingGameSongScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportSingingGameSongScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportSingingGameSongScoreReq proto.InternalMessageInfo

func (m *ReportSingingGameSongScoreReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportSingingGameSongScoreReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportSingingGameSongScoreReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *ReportSingingGameSongScoreReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ReportSingingGameSongScoreReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type ReportSingingGameSongScoreResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SongResult           *SingingGameSongResult `protobuf:"bytes,2,opt,name=song_result,json=songResult,proto3" json:"song_result,omitempty"`
	IsReportAudioInfo    bool                   `protobuf:"varint,3,opt,name=is_report_audio_info,json=isReportAudioInfo,proto3" json:"is_report_audio_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ReportSingingGameSongScoreResp) Reset()         { *m = ReportSingingGameSongScoreResp{} }
func (m *ReportSingingGameSongScoreResp) String() string { return proto.CompactTextString(m) }
func (*ReportSingingGameSongScoreResp) ProtoMessage()    {}
func (*ReportSingingGameSongScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{41}
}
func (m *ReportSingingGameSongScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportSingingGameSongScoreResp.Unmarshal(m, b)
}
func (m *ReportSingingGameSongScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportSingingGameSongScoreResp.Marshal(b, m, deterministic)
}
func (dst *ReportSingingGameSongScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportSingingGameSongScoreResp.Merge(dst, src)
}
func (m *ReportSingingGameSongScoreResp) XXX_Size() int {
	return xxx_messageInfo_ReportSingingGameSongScoreResp.Size(m)
}
func (m *ReportSingingGameSongScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportSingingGameSongScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportSingingGameSongScoreResp proto.InternalMessageInfo

func (m *ReportSingingGameSongScoreResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ReportSingingGameSongScoreResp) GetSongResult() *SingingGameSongResult {
	if m != nil {
		return m.SongResult
	}
	return nil
}

func (m *ReportSingingGameSongScoreResp) GetIsReportAudioInfo() bool {
	if m != nil {
		return m.IsReportAudioInfo
	}
	return false
}

type SwitchSingingGameTypeReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32          `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SingingGameType      SingingGameType `protobuf:"varint,3,opt,name=singing_game_type,json=singingGameType,proto3,enum=ga.sing_a_round_logic.SingingGameType" json:"singing_game_type,omitempty"`
	NewSingingGameType   uint32          `protobuf:"varint,4,opt,name=new_singing_game_type,json=newSingingGameType,proto3" json:"new_singing_game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SwitchSingingGameTypeReq) Reset()         { *m = SwitchSingingGameTypeReq{} }
func (m *SwitchSingingGameTypeReq) String() string { return proto.CompactTextString(m) }
func (*SwitchSingingGameTypeReq) ProtoMessage()    {}
func (*SwitchSingingGameTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{42}
}
func (m *SwitchSingingGameTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSingingGameTypeReq.Unmarshal(m, b)
}
func (m *SwitchSingingGameTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSingingGameTypeReq.Marshal(b, m, deterministic)
}
func (dst *SwitchSingingGameTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSingingGameTypeReq.Merge(dst, src)
}
func (m *SwitchSingingGameTypeReq) XXX_Size() int {
	return xxx_messageInfo_SwitchSingingGameTypeReq.Size(m)
}
func (m *SwitchSingingGameTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSingingGameTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSingingGameTypeReq proto.InternalMessageInfo

func (m *SwitchSingingGameTypeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SwitchSingingGameTypeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchSingingGameTypeReq) GetSingingGameType() SingingGameType {
	if m != nil {
		return m.SingingGameType
	}
	return SingingGameType_SingingGameTypeUndefined
}

func (m *SwitchSingingGameTypeReq) GetNewSingingGameType() uint32 {
	if m != nil {
		return m.NewSingingGameType
	}
	return 0
}

type SwitchSingingGameTypeResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Songs                []*SingingGameSong      `protobuf:"bytes,3,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SwitchSingingGameTypeResp) Reset()         { *m = SwitchSingingGameTypeResp{} }
func (m *SwitchSingingGameTypeResp) String() string { return proto.CompactTextString(m) }
func (*SwitchSingingGameTypeResp) ProtoMessage()    {}
func (*SwitchSingingGameTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{43}
}
func (m *SwitchSingingGameTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSingingGameTypeResp.Unmarshal(m, b)
}
func (m *SwitchSingingGameTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSingingGameTypeResp.Marshal(b, m, deterministic)
}
func (dst *SwitchSingingGameTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSingingGameTypeResp.Merge(dst, src)
}
func (m *SwitchSingingGameTypeResp) XXX_Size() int {
	return xxx_messageInfo_SwitchSingingGameTypeResp.Size(m)
}
func (m *SwitchSingingGameTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSingingGameTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSingingGameTypeResp proto.InternalMessageInfo

func (m *SwitchSingingGameTypeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SwitchSingingGameTypeResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SwitchSingingGameTypeResp) GetSongs() []*SingingGameSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

// 用户房间状态（客户端重连时使用）
type GetSingingGameChannelInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSingingGameChannelInfoReq) Reset()         { *m = GetSingingGameChannelInfoReq{} }
func (m *GetSingingGameChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameChannelInfoReq) ProtoMessage()    {}
func (*GetSingingGameChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{44}
}
func (m *GetSingingGameChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameChannelInfoReq.Unmarshal(m, b)
}
func (m *GetSingingGameChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameChannelInfoReq.Merge(dst, src)
}
func (m *GetSingingGameChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameChannelInfoReq.Size(m)
}
func (m *GetSingingGameChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameChannelInfoReq proto.InternalMessageInfo

func (m *GetSingingGameChannelInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSingingGameChannelInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetSingingGameChannelInfoResp struct {
	BaseResp              *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelInfo           *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Songs                 []*SingingGameSong      `protobuf:"bytes,3,rep,name=songs,proto3" json:"songs,omitempty"`
	GrabMicBtnHiddenAt    uint32                  `protobuf:"varint,4,opt,name=grab_mic_btn_hidden_at,json=grabMicBtnHiddenAt,proto3" json:"grab_mic_btn_hidden_at,omitempty"`
	GrabMicBtnDisplayTime uint32                  `protobuf:"varint,5,opt,name=grab_mic_btn_display_time,json=grabMicBtnDisplayTime,proto3" json:"grab_mic_btn_display_time,omitempty"`
	HelpBtnHiddenAt       uint32                  `protobuf:"varint,6,opt,name=help_btn_hidden_at,json=helpBtnHiddenAt,proto3" json:"help_btn_hidden_at,omitempty"`
	HelpBtnDisplayTime    uint32                  `protobuf:"varint,7,opt,name=help_btn_display_time,json=helpBtnDisplayTime,proto3" json:"help_btn_display_time,omitempty"`
	Styles                []*SingingGameSongStyle `protobuf:"bytes,8,rep,name=styles,proto3" json:"styles,omitempty"`
	SelectedStyle         *SingingGameSongStyle   `protobuf:"bytes,9,opt,name=selected_style,json=selectedStyle,proto3" json:"selected_style,omitempty"`
	VoteCountdown         uint32                  `protobuf:"varint,10,opt,name=vote_countdown,json=voteCountdown,proto3" json:"vote_countdown,omitempty"`
	NoPrepareBanDuration  uint32                  `protobuf:"varint,11,opt,name=no_prepare_ban_duration,json=noPrepareBanDuration,proto3" json:"no_prepare_ban_duration,omitempty"`
	ExitGameBanDuration   uint32                  `protobuf:"varint,12,opt,name=exit_game_ban_duration,json=exitGameBanDuration,proto3" json:"exit_game_ban_duration,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                `json:"-"`
	XXX_unrecognized      []byte                  `json:"-"`
	XXX_sizecache         int32                   `json:"-"`
}

func (m *GetSingingGameChannelInfoResp) Reset()         { *m = GetSingingGameChannelInfoResp{} }
func (m *GetSingingGameChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameChannelInfoResp) ProtoMessage()    {}
func (*GetSingingGameChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{45}
}
func (m *GetSingingGameChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameChannelInfoResp.Unmarshal(m, b)
}
func (m *GetSingingGameChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameChannelInfoResp.Merge(dst, src)
}
func (m *GetSingingGameChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameChannelInfoResp.Size(m)
}
func (m *GetSingingGameChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameChannelInfoResp proto.InternalMessageInfo

func (m *GetSingingGameChannelInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSingingGameChannelInfoResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *GetSingingGameChannelInfoResp) GetSongs() []*SingingGameSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *GetSingingGameChannelInfoResp) GetGrabMicBtnHiddenAt() uint32 {
	if m != nil {
		return m.GrabMicBtnHiddenAt
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetGrabMicBtnDisplayTime() uint32 {
	if m != nil {
		return m.GrabMicBtnDisplayTime
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetHelpBtnHiddenAt() uint32 {
	if m != nil {
		return m.HelpBtnHiddenAt
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetHelpBtnDisplayTime() uint32 {
	if m != nil {
		return m.HelpBtnDisplayTime
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetStyles() []*SingingGameSongStyle {
	if m != nil {
		return m.Styles
	}
	return nil
}

func (m *GetSingingGameChannelInfoResp) GetSelectedStyle() *SingingGameSongStyle {
	if m != nil {
		return m.SelectedStyle
	}
	return nil
}

func (m *GetSingingGameChannelInfoResp) GetVoteCountdown() uint32 {
	if m != nil {
		return m.VoteCountdown
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetNoPrepareBanDuration() uint32 {
	if m != nil {
		return m.NoPrepareBanDuration
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetExitGameBanDuration() uint32 {
	if m != nil {
		return m.ExitGameBanDuration
	}
	return 0
}

// 歌曲成绩push
type SingingGameSongResultNotify struct {
	Result               *SingingGameSongResult `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SingingGameSongResultNotify) Reset()         { *m = SingingGameSongResultNotify{} }
func (m *SingingGameSongResultNotify) String() string { return proto.CompactTextString(m) }
func (*SingingGameSongResultNotify) ProtoMessage()    {}
func (*SingingGameSongResultNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{46}
}
func (m *SingingGameSongResultNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameSongResultNotify.Unmarshal(m, b)
}
func (m *SingingGameSongResultNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameSongResultNotify.Marshal(b, m, deterministic)
}
func (dst *SingingGameSongResultNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameSongResultNotify.Merge(dst, src)
}
func (m *SingingGameSongResultNotify) XXX_Size() int {
	return xxx_messageInfo_SingingGameSongResultNotify.Size(m)
}
func (m *SingingGameSongResultNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameSongResultNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameSongResultNotify proto.InternalMessageInfo

func (m *SingingGameSongResultNotify) GetResult() *SingingGameSongResult {
	if m != nil {
		return m.Result
	}
	return nil
}

// 游戏成绩push
type SingingGameResultNotify struct {
	Result               *SingingGameResult      `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,2,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Songs                []*SingingGameSong      `protobuf:"bytes,3,rep,name=songs,proto3" json:"songs,omitempty"`
	Countdown            uint32                  `protobuf:"varint,4,opt,name=countdown,proto3" json:"countdown,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingingGameResultNotify) Reset()         { *m = SingingGameResultNotify{} }
func (m *SingingGameResultNotify) String() string { return proto.CompactTextString(m) }
func (*SingingGameResultNotify) ProtoMessage()    {}
func (*SingingGameResultNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{47}
}
func (m *SingingGameResultNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameResultNotify.Unmarshal(m, b)
}
func (m *SingingGameResultNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameResultNotify.Marshal(b, m, deterministic)
}
func (dst *SingingGameResultNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameResultNotify.Merge(dst, src)
}
func (m *SingingGameResultNotify) XXX_Size() int {
	return xxx_messageInfo_SingingGameResultNotify.Size(m)
}
func (m *SingingGameResultNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameResultNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameResultNotify proto.InternalMessageInfo

func (m *SingingGameResultNotify) GetResult() *SingingGameResult {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *SingingGameResultNotify) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SingingGameResultNotify) GetSongs() []*SingingGameSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *SingingGameResultNotify) GetCountdown() uint32 {
	if m != nil {
		return m.Countdown
	}
	return 0
}

// 切换游戏类型push
type SwitchSingingGameTypeNotify struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Songs                []*SingingGameSong      `protobuf:"bytes,2,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SwitchSingingGameTypeNotify) Reset()         { *m = SwitchSingingGameTypeNotify{} }
func (m *SwitchSingingGameTypeNotify) String() string { return proto.CompactTextString(m) }
func (*SwitchSingingGameTypeNotify) ProtoMessage()    {}
func (*SwitchSingingGameTypeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{48}
}
func (m *SwitchSingingGameTypeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSingingGameTypeNotify.Unmarshal(m, b)
}
func (m *SwitchSingingGameTypeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSingingGameTypeNotify.Marshal(b, m, deterministic)
}
func (dst *SwitchSingingGameTypeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSingingGameTypeNotify.Merge(dst, src)
}
func (m *SwitchSingingGameTypeNotify) XXX_Size() int {
	return xxx_messageInfo_SwitchSingingGameTypeNotify.Size(m)
}
func (m *SwitchSingingGameTypeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSingingGameTypeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSingingGameTypeNotify proto.InternalMessageInfo

func (m *SwitchSingingGameTypeNotify) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SwitchSingingGameTypeNotify) GetSongs() []*SingingGameSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

type SingingGameSongResult struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	ScoreDetail          *SingingGameScoreDetail `protobuf:"bytes,2,opt,name=score_detail,json=scoreDetail,proto3" json:"score_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingingGameSongResult) Reset()         { *m = SingingGameSongResult{} }
func (m *SingingGameSongResult) String() string { return proto.CompactTextString(m) }
func (*SingingGameSongResult) ProtoMessage()    {}
func (*SingingGameSongResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{49}
}
func (m *SingingGameSongResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameSongResult.Unmarshal(m, b)
}
func (m *SingingGameSongResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameSongResult.Marshal(b, m, deterministic)
}
func (dst *SingingGameSongResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameSongResult.Merge(dst, src)
}
func (m *SingingGameSongResult) XXX_Size() int {
	return xxx_messageInfo_SingingGameSongResult.Size(m)
}
func (m *SingingGameSongResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameSongResult.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameSongResult proto.InternalMessageInfo

func (m *SingingGameSongResult) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SingingGameSongResult) GetScoreDetail() *SingingGameScoreDetail {
	if m != nil {
		return m.ScoreDetail
	}
	return nil
}

type SingingGameScoreDetail struct {
	Success              bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	HelperScore          uint32   `protobuf:"varint,2,opt,name=helper_score,json=helperScore,proto3" json:"helper_score,omitempty"`
	Scores               []string `protobuf:"bytes,3,rep,name=scores,proto3" json:"scores,omitempty"`
	Account              string   `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameScoreDetail) Reset()         { *m = SingingGameScoreDetail{} }
func (m *SingingGameScoreDetail) String() string { return proto.CompactTextString(m) }
func (*SingingGameScoreDetail) ProtoMessage()    {}
func (*SingingGameScoreDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{50}
}
func (m *SingingGameScoreDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameScoreDetail.Unmarshal(m, b)
}
func (m *SingingGameScoreDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameScoreDetail.Marshal(b, m, deterministic)
}
func (dst *SingingGameScoreDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameScoreDetail.Merge(dst, src)
}
func (m *SingingGameScoreDetail) XXX_Size() int {
	return xxx_messageInfo_SingingGameScoreDetail.Size(m)
}
func (m *SingingGameScoreDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameScoreDetail.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameScoreDetail proto.InternalMessageInfo

func (m *SingingGameScoreDetail) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *SingingGameScoreDetail) GetHelperScore() uint32 {
	if m != nil {
		return m.HelperScore
	}
	return 0
}

func (m *SingingGameScoreDetail) GetScores() []string {
	if m != nil {
		return m.Scores
	}
	return nil
}

func (m *SingingGameScoreDetail) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type CoupleUserDayScoreInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoupleUserDayScoreInfo) Reset()         { *m = CoupleUserDayScoreInfo{} }
func (m *CoupleUserDayScoreInfo) String() string { return proto.CompactTextString(m) }
func (*CoupleUserDayScoreInfo) ProtoMessage()    {}
func (*CoupleUserDayScoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{51}
}
func (m *CoupleUserDayScoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoupleUserDayScoreInfo.Unmarshal(m, b)
}
func (m *CoupleUserDayScoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoupleUserDayScoreInfo.Marshal(b, m, deterministic)
}
func (dst *CoupleUserDayScoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoupleUserDayScoreInfo.Merge(dst, src)
}
func (m *CoupleUserDayScoreInfo) XXX_Size() int {
	return xxx_messageInfo_CoupleUserDayScoreInfo.Size(m)
}
func (m *CoupleUserDayScoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CoupleUserDayScoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CoupleUserDayScoreInfo proto.InternalMessageInfo

func (m *CoupleUserDayScoreInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CoupleUserDayScoreInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type SingingGameResult struct {
	Scores               []*SingingGameScore       `protobuf:"bytes,1,rep,name=scores,proto3" json:"scores,omitempty"`
	Helps                []*SingingGameHelp        `protobuf:"bytes,2,rep,name=helps,proto3" json:"helps,omitempty"`
	Tip                  string                    `protobuf:"bytes,3,opt,name=tip,proto3" json:"tip,omitempty"`
	PassThroughCount     uint32                    `protobuf:"varint,4,opt,name=pass_through_count,json=passThroughCount,proto3" json:"pass_through_count,omitempty"`
	ResultType           SingingGameResultType     `protobuf:"varint,5,opt,name=result_type,json=resultType,proto3,enum=ga.sing_a_round_logic.SingingGameResultType" json:"result_type,omitempty"`
	CoupleUserScoreList  []*CoupleUserDayScoreInfo `protobuf:"bytes,6,rep,name=couple_user_score_list,json=coupleUserScoreList,proto3" json:"couple_user_score_list,omitempty"`
	IsUseCoupleScore     bool                      `protobuf:"varint,7,opt,name=is_use_couple_score,json=isUseCoupleScore,proto3" json:"is_use_couple_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SingingGameResult) Reset()         { *m = SingingGameResult{} }
func (m *SingingGameResult) String() string { return proto.CompactTextString(m) }
func (*SingingGameResult) ProtoMessage()    {}
func (*SingingGameResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{52}
}
func (m *SingingGameResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameResult.Unmarshal(m, b)
}
func (m *SingingGameResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameResult.Marshal(b, m, deterministic)
}
func (dst *SingingGameResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameResult.Merge(dst, src)
}
func (m *SingingGameResult) XXX_Size() int {
	return xxx_messageInfo_SingingGameResult.Size(m)
}
func (m *SingingGameResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameResult.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameResult proto.InternalMessageInfo

func (m *SingingGameResult) GetScores() []*SingingGameScore {
	if m != nil {
		return m.Scores
	}
	return nil
}

func (m *SingingGameResult) GetHelps() []*SingingGameHelp {
	if m != nil {
		return m.Helps
	}
	return nil
}

func (m *SingingGameResult) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

func (m *SingingGameResult) GetPassThroughCount() uint32 {
	if m != nil {
		return m.PassThroughCount
	}
	return 0
}

func (m *SingingGameResult) GetResultType() SingingGameResultType {
	if m != nil {
		return m.ResultType
	}
	return SingingGameResultType_SingingGameResultTypeUndefined
}

func (m *SingingGameResult) GetCoupleUserScoreList() []*CoupleUserDayScoreInfo {
	if m != nil {
		return m.CoupleUserScoreList
	}
	return nil
}

func (m *SingingGameResult) GetIsUseCoupleScore() bool {
	if m != nil {
		return m.IsUseCoupleScore
	}
	return false
}

type SingingGameScore struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	LeftLifeCount        uint32   `protobuf:"varint,4,opt,name=left_life_count,json=leftLifeCount,proto3" json:"left_life_count,omitempty"`
	Score                uint32   `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	Count                uint32   `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`
	HaveRunAway          bool     `protobuf:"varint,7,opt,name=have_run_away,json=haveRunAway,proto3" json:"have_run_away,omitempty"`
	GrabMicTime          uint32   `protobuf:"varint,8,opt,name=grab_mic_time,json=grabMicTime,proto3" json:"grab_mic_time,omitempty"`
	GrabMicSuccessRate   float32  `protobuf:"fixed32,9,opt,name=grab_mic_success_rate,json=grabMicSuccessRate,proto3" json:"grab_mic_success_rate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameScore) Reset()         { *m = SingingGameScore{} }
func (m *SingingGameScore) String() string { return proto.CompactTextString(m) }
func (*SingingGameScore) ProtoMessage()    {}
func (*SingingGameScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{53}
}
func (m *SingingGameScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameScore.Unmarshal(m, b)
}
func (m *SingingGameScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameScore.Marshal(b, m, deterministic)
}
func (dst *SingingGameScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameScore.Merge(dst, src)
}
func (m *SingingGameScore) XXX_Size() int {
	return xxx_messageInfo_SingingGameScore.Size(m)
}
func (m *SingingGameScore) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameScore.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameScore proto.InternalMessageInfo

func (m *SingingGameScore) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingingGameScore) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *SingingGameScore) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SingingGameScore) GetLeftLifeCount() uint32 {
	if m != nil {
		return m.LeftLifeCount
	}
	return 0
}

func (m *SingingGameScore) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *SingingGameScore) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SingingGameScore) GetHaveRunAway() bool {
	if m != nil {
		return m.HaveRunAway
	}
	return false
}

func (m *SingingGameScore) GetGrabMicTime() uint32 {
	if m != nil {
		return m.GrabMicTime
	}
	return 0
}

func (m *SingingGameScore) GetGrabMicSuccessRate() float32 {
	if m != nil {
		return m.GrabMicSuccessRate
	}
	return 0
}

type SingingGameHelp struct {
	SingerId             uint32   `protobuf:"varint,1,opt,name=singer_id,json=singerId,proto3" json:"singer_id,omitempty"`
	HelperId             uint32   `protobuf:"varint,2,opt,name=helper_id,json=helperId,proto3" json:"helper_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameHelp) Reset()         { *m = SingingGameHelp{} }
func (m *SingingGameHelp) String() string { return proto.CompactTextString(m) }
func (*SingingGameHelp) ProtoMessage()    {}
func (*SingingGameHelp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{54}
}
func (m *SingingGameHelp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameHelp.Unmarshal(m, b)
}
func (m *SingingGameHelp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameHelp.Marshal(b, m, deterministic)
}
func (dst *SingingGameHelp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameHelp.Merge(dst, src)
}
func (m *SingingGameHelp) XXX_Size() int {
	return xxx_messageInfo_SingingGameHelp.Size(m)
}
func (m *SingingGameHelp) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameHelp.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameHelp proto.InternalMessageInfo

func (m *SingingGameHelp) GetSingerId() uint32 {
	if m != nil {
		return m.SingerId
	}
	return 0
}

func (m *SingingGameHelp) GetHelperId() uint32 {
	if m != nil {
		return m.HelperId
	}
	return 0
}

// 牛啊
type ExpressSingingGameLikeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32       `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string       `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Count                uint32       `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ExpressSingingGameLikeReq) Reset()         { *m = ExpressSingingGameLikeReq{} }
func (m *ExpressSingingGameLikeReq) String() string { return proto.CompactTextString(m) }
func (*ExpressSingingGameLikeReq) ProtoMessage()    {}
func (*ExpressSingingGameLikeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{55}
}
func (m *ExpressSingingGameLikeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressSingingGameLikeReq.Unmarshal(m, b)
}
func (m *ExpressSingingGameLikeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressSingingGameLikeReq.Marshal(b, m, deterministic)
}
func (dst *ExpressSingingGameLikeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressSingingGameLikeReq.Merge(dst, src)
}
func (m *ExpressSingingGameLikeReq) XXX_Size() int {
	return xxx_messageInfo_ExpressSingingGameLikeReq.Size(m)
}
func (m *ExpressSingingGameLikeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressSingingGameLikeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressSingingGameLikeReq proto.InternalMessageInfo

func (m *ExpressSingingGameLikeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ExpressSingingGameLikeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ExpressSingingGameLikeReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *ExpressSingingGameLikeReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ExpressSingingGameLikeReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ExpressSingingGameLikeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ExpressSingingGameLikeResp) Reset()         { *m = ExpressSingingGameLikeResp{} }
func (m *ExpressSingingGameLikeResp) String() string { return proto.CompactTextString(m) }
func (*ExpressSingingGameLikeResp) ProtoMessage()    {}
func (*ExpressSingingGameLikeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{56}
}
func (m *ExpressSingingGameLikeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressSingingGameLikeResp.Unmarshal(m, b)
}
func (m *ExpressSingingGameLikeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressSingingGameLikeResp.Marshal(b, m, deterministic)
}
func (dst *ExpressSingingGameLikeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressSingingGameLikeResp.Merge(dst, src)
}
func (m *ExpressSingingGameLikeResp) XXX_Size() int {
	return xxx_messageInfo_ExpressSingingGameLikeResp.Size(m)
}
func (m *ExpressSingingGameLikeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressSingingGameLikeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressSingingGameLikeResp proto.InternalMessageInfo

func (m *ExpressSingingGameLikeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// “牛啊”公屏消息
type SingingGameLikeMsg struct {
	ChannelId            uint32              `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FromUser             *SingingGameUser    `protobuf:"bytes,2,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	ToUser               *SingingGameUser    `protobuf:"bytes,3,opt,name=to_user,json=toUser,proto3" json:"to_user,omitempty"`
	LikeType             SingingGameLikeType `protobuf:"varint,4,opt,name=like_type,json=likeType,proto3,enum=ga.sing_a_round_logic.SingingGameLikeType" json:"like_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SingingGameLikeMsg) Reset()         { *m = SingingGameLikeMsg{} }
func (m *SingingGameLikeMsg) String() string { return proto.CompactTextString(m) }
func (*SingingGameLikeMsg) ProtoMessage()    {}
func (*SingingGameLikeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{57}
}
func (m *SingingGameLikeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameLikeMsg.Unmarshal(m, b)
}
func (m *SingingGameLikeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameLikeMsg.Marshal(b, m, deterministic)
}
func (dst *SingingGameLikeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameLikeMsg.Merge(dst, src)
}
func (m *SingingGameLikeMsg) XXX_Size() int {
	return xxx_messageInfo_SingingGameLikeMsg.Size(m)
}
func (m *SingingGameLikeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameLikeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameLikeMsg proto.InternalMessageInfo

func (m *SingingGameLikeMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameLikeMsg) GetFromUser() *SingingGameUser {
	if m != nil {
		return m.FromUser
	}
	return nil
}

func (m *SingingGameLikeMsg) GetToUser() *SingingGameUser {
	if m != nil {
		return m.ToUser
	}
	return nil
}

func (m *SingingGameLikeMsg) GetLikeType() SingingGameLikeType {
	if m != nil {
		return m.LikeType
	}
	return SingingGameLikeType_SingingGameLikeTypeUndefined
}

// 牛啊通知
type SingingGameLikeNotify struct {
	ChannelId            uint32              `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FromUser             *SingingGameUser    `protobuf:"bytes,2,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	ToUser               *SingingGameUser    `protobuf:"bytes,3,opt,name=to_user,json=toUser,proto3" json:"to_user,omitempty"`
	LikeType             SingingGameLikeType `protobuf:"varint,4,opt,name=like_type,json=likeType,proto3,enum=ga.sing_a_round_logic.SingingGameLikeType" json:"like_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SingingGameLikeNotify) Reset()         { *m = SingingGameLikeNotify{} }
func (m *SingingGameLikeNotify) String() string { return proto.CompactTextString(m) }
func (*SingingGameLikeNotify) ProtoMessage()    {}
func (*SingingGameLikeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{58}
}
func (m *SingingGameLikeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameLikeNotify.Unmarshal(m, b)
}
func (m *SingingGameLikeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameLikeNotify.Marshal(b, m, deterministic)
}
func (dst *SingingGameLikeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameLikeNotify.Merge(dst, src)
}
func (m *SingingGameLikeNotify) XXX_Size() int {
	return xxx_messageInfo_SingingGameLikeNotify.Size(m)
}
func (m *SingingGameLikeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameLikeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameLikeNotify proto.InternalMessageInfo

func (m *SingingGameLikeNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameLikeNotify) GetFromUser() *SingingGameUser {
	if m != nil {
		return m.FromUser
	}
	return nil
}

func (m *SingingGameLikeNotify) GetToUser() *SingingGameUser {
	if m != nil {
		return m.ToUser
	}
	return nil
}

func (m *SingingGameLikeNotify) GetLikeType() SingingGameLikeType {
	if m != nil {
		return m.LikeType
	}
	return SingingGameLikeType_SingingGameLikeTypeUndefined
}

type SingingGameUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameUser) Reset()         { *m = SingingGameUser{} }
func (m *SingingGameUser) String() string { return proto.CompactTextString(m) }
func (*SingingGameUser) ProtoMessage()    {}
func (*SingingGameUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{59}
}
func (m *SingingGameUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameUser.Unmarshal(m, b)
}
func (m *SingingGameUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameUser.Marshal(b, m, deterministic)
}
func (dst *SingingGameUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameUser.Merge(dst, src)
}
func (m *SingingGameUser) XXX_Size() int {
	return xxx_messageInfo_SingingGameUser.Size(m)
}
func (m *SingingGameUser) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameUser.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameUser proto.InternalMessageInfo

func (m *SingingGameUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingingGameUser) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SingingGameUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *SingingGameUser) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

// 牛啊通知（用于展示“一键碰拳”）
type SingingGameLikesNotify struct {
	ChannelId            uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FromUsers            []*SingingGameUser `protobuf:"bytes,2,rep,name=from_users,json=fromUsers,proto3" json:"from_users,omitempty"`
	ToUser               *SingingGameUser   `protobuf:"bytes,3,opt,name=to_user,json=toUser,proto3" json:"to_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SingingGameLikesNotify) Reset()         { *m = SingingGameLikesNotify{} }
func (m *SingingGameLikesNotify) String() string { return proto.CompactTextString(m) }
func (*SingingGameLikesNotify) ProtoMessage()    {}
func (*SingingGameLikesNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{60}
}
func (m *SingingGameLikesNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameLikesNotify.Unmarshal(m, b)
}
func (m *SingingGameLikesNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameLikesNotify.Marshal(b, m, deterministic)
}
func (dst *SingingGameLikesNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameLikesNotify.Merge(dst, src)
}
func (m *SingingGameLikesNotify) XXX_Size() int {
	return xxx_messageInfo_SingingGameLikesNotify.Size(m)
}
func (m *SingingGameLikesNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameLikesNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameLikesNotify proto.InternalMessageInfo

func (m *SingingGameLikesNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameLikesNotify) GetFromUsers() []*SingingGameUser {
	if m != nil {
		return m.FromUsers
	}
	return nil
}

func (m *SingingGameLikesNotify) GetToUser() *SingingGameUser {
	if m != nil {
		return m.ToUser
	}
	return nil
}

// 碰拳
type SingingGameFistBumpReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uids                 []uint32     `protobuf:"varint,3,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SingingGameFistBumpReq) Reset()         { *m = SingingGameFistBumpReq{} }
func (m *SingingGameFistBumpReq) String() string { return proto.CompactTextString(m) }
func (*SingingGameFistBumpReq) ProtoMessage()    {}
func (*SingingGameFistBumpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{61}
}
func (m *SingingGameFistBumpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameFistBumpReq.Unmarshal(m, b)
}
func (m *SingingGameFistBumpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameFistBumpReq.Marshal(b, m, deterministic)
}
func (dst *SingingGameFistBumpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameFistBumpReq.Merge(dst, src)
}
func (m *SingingGameFistBumpReq) XXX_Size() int {
	return xxx_messageInfo_SingingGameFistBumpReq.Size(m)
}
func (m *SingingGameFistBumpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameFistBumpReq.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameFistBumpReq proto.InternalMessageInfo

func (m *SingingGameFistBumpReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SingingGameFistBumpReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameFistBumpReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type SingingGameFistBumpResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SingingGameFistBumpResp) Reset()         { *m = SingingGameFistBumpResp{} }
func (m *SingingGameFistBumpResp) String() string { return proto.CompactTextString(m) }
func (*SingingGameFistBumpResp) ProtoMessage()    {}
func (*SingingGameFistBumpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{62}
}
func (m *SingingGameFistBumpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameFistBumpResp.Unmarshal(m, b)
}
func (m *SingingGameFistBumpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameFistBumpResp.Marshal(b, m, deterministic)
}
func (dst *SingingGameFistBumpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameFistBumpResp.Merge(dst, src)
}
func (m *SingingGameFistBumpResp) XXX_Size() int {
	return xxx_messageInfo_SingingGameFistBumpResp.Size(m)
}
func (m *SingingGameFistBumpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameFistBumpResp.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameFistBumpResp proto.InternalMessageInfo

func (m *SingingGameFistBumpResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 碰拳通知
type SingingGameFistBumpNotify struct {
	ChannelId            uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FromUser             *SingingGameUser   `protobuf:"bytes,2,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	ToUsers              []*SingingGameUser `protobuf:"bytes,3,rep,name=to_users,json=toUsers,proto3" json:"to_users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SingingGameFistBumpNotify) Reset()         { *m = SingingGameFistBumpNotify{} }
func (m *SingingGameFistBumpNotify) String() string { return proto.CompactTextString(m) }
func (*SingingGameFistBumpNotify) ProtoMessage()    {}
func (*SingingGameFistBumpNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{63}
}
func (m *SingingGameFistBumpNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameFistBumpNotify.Unmarshal(m, b)
}
func (m *SingingGameFistBumpNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameFistBumpNotify.Marshal(b, m, deterministic)
}
func (dst *SingingGameFistBumpNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameFistBumpNotify.Merge(dst, src)
}
func (m *SingingGameFistBumpNotify) XXX_Size() int {
	return xxx_messageInfo_SingingGameFistBumpNotify.Size(m)
}
func (m *SingingGameFistBumpNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameFistBumpNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameFistBumpNotify proto.InternalMessageInfo

func (m *SingingGameFistBumpNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameFistBumpNotify) GetFromUser() *SingingGameUser {
	if m != nil {
		return m.FromUser
	}
	return nil
}

func (m *SingingGameFistBumpNotify) GetToUsers() []*SingingGameUser {
	if m != nil {
		return m.ToUsers
	}
	return nil
}

type UserImageConf struct {
	ImageId              string   `protobuf:"bytes,1,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	ImageName            string   `protobuf:"bytes,2,opt,name=image_name,json=imageName,proto3" json:"image_name,omitempty"`
	ImageUrl             string   `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Md5                  string   `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	ImageType            uint32   `protobuf:"varint,5,opt,name=image_type,json=imageType,proto3" json:"image_type,omitempty"`
	Index                uint32   `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty"`
	DefaultIconUrl       string   `protobuf:"bytes,7,opt,name=default_icon_url,json=defaultIconUrl,proto3" json:"default_icon_url,omitempty"`
	DefaultSingAvatarUrl string   `protobuf:"bytes,8,opt,name=default_sing_avatar_url,json=defaultSingAvatarUrl,proto3" json:"default_sing_avatar_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserImageConf) Reset()         { *m = UserImageConf{} }
func (m *UserImageConf) String() string { return proto.CompactTextString(m) }
func (*UserImageConf) ProtoMessage()    {}
func (*UserImageConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{64}
}
func (m *UserImageConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserImageConf.Unmarshal(m, b)
}
func (m *UserImageConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserImageConf.Marshal(b, m, deterministic)
}
func (dst *UserImageConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserImageConf.Merge(dst, src)
}
func (m *UserImageConf) XXX_Size() int {
	return xxx_messageInfo_UserImageConf.Size(m)
}
func (m *UserImageConf) XXX_DiscardUnknown() {
	xxx_messageInfo_UserImageConf.DiscardUnknown(m)
}

var xxx_messageInfo_UserImageConf proto.InternalMessageInfo

func (m *UserImageConf) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

func (m *UserImageConf) GetImageName() string {
	if m != nil {
		return m.ImageName
	}
	return ""
}

func (m *UserImageConf) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *UserImageConf) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *UserImageConf) GetImageType() uint32 {
	if m != nil {
		return m.ImageType
	}
	return 0
}

func (m *UserImageConf) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *UserImageConf) GetDefaultIconUrl() string {
	if m != nil {
		return m.DefaultIconUrl
	}
	return ""
}

func (m *UserImageConf) GetDefaultSingAvatarUrl() string {
	if m != nil {
		return m.DefaultSingAvatarUrl
	}
	return ""
}

// 唱歌时装饰
type DecorationInfo struct {
	DecorationId         string   `protobuf:"bytes,1,opt,name=decoration_id,json=decorationId,proto3" json:"decoration_id,omitempty"`
	DecorationName       string   `protobuf:"bytes,2,opt,name=decoration_name,json=decorationName,proto3" json:"decoration_name,omitempty"`
	DecorationUrl        string   `protobuf:"bytes,3,opt,name=decoration_url,json=decorationUrl,proto3" json:"decoration_url,omitempty"`
	Md5                  string   `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecorationInfo) Reset()         { *m = DecorationInfo{} }
func (m *DecorationInfo) String() string { return proto.CompactTextString(m) }
func (*DecorationInfo) ProtoMessage()    {}
func (*DecorationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{65}
}
func (m *DecorationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecorationInfo.Unmarshal(m, b)
}
func (m *DecorationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecorationInfo.Marshal(b, m, deterministic)
}
func (dst *DecorationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecorationInfo.Merge(dst, src)
}
func (m *DecorationInfo) XXX_Size() int {
	return xxx_messageInfo_DecorationInfo.Size(m)
}
func (m *DecorationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DecorationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DecorationInfo proto.InternalMessageInfo

func (m *DecorationInfo) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

func (m *DecorationInfo) GetDecorationName() string {
	if m != nil {
		return m.DecorationName
	}
	return ""
}

func (m *DecorationInfo) GetDecorationUrl() string {
	if m != nil {
		return m.DecorationUrl
	}
	return ""
}

func (m *DecorationInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type GetSingAllConfReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Version              uint32       `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSingAllConfReq) Reset()         { *m = GetSingAllConfReq{} }
func (m *GetSingAllConfReq) String() string { return proto.CompactTextString(m) }
func (*GetSingAllConfReq) ProtoMessage()    {}
func (*GetSingAllConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{66}
}
func (m *GetSingAllConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingAllConfReq.Unmarshal(m, b)
}
func (m *GetSingAllConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingAllConfReq.Marshal(b, m, deterministic)
}
func (dst *GetSingAllConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingAllConfReq.Merge(dst, src)
}
func (m *GetSingAllConfReq) XXX_Size() int {
	return xxx_messageInfo_GetSingAllConfReq.Size(m)
}
func (m *GetSingAllConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingAllConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingAllConfReq proto.InternalMessageInfo

func (m *GetSingAllConfReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSingAllConfReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

// 新解锁形象推送
type UserNewImageUnlockNotify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	NewImageId           string   `protobuf:"bytes,2,opt,name=new_image_id,json=newImageId,proto3" json:"new_image_id,omitempty"`
	NewImageIds          []string `protobuf:"bytes,3,rep,name=new_image_ids,json=newImageIds,proto3" json:"new_image_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserNewImageUnlockNotify) Reset()         { *m = UserNewImageUnlockNotify{} }
func (m *UserNewImageUnlockNotify) String() string { return proto.CompactTextString(m) }
func (*UserNewImageUnlockNotify) ProtoMessage()    {}
func (*UserNewImageUnlockNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{67}
}
func (m *UserNewImageUnlockNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserNewImageUnlockNotify.Unmarshal(m, b)
}
func (m *UserNewImageUnlockNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserNewImageUnlockNotify.Marshal(b, m, deterministic)
}
func (dst *UserNewImageUnlockNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserNewImageUnlockNotify.Merge(dst, src)
}
func (m *UserNewImageUnlockNotify) XXX_Size() int {
	return xxx_messageInfo_UserNewImageUnlockNotify.Size(m)
}
func (m *UserNewImageUnlockNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_UserNewImageUnlockNotify.DiscardUnknown(m)
}

var xxx_messageInfo_UserNewImageUnlockNotify proto.InternalMessageInfo

func (m *UserNewImageUnlockNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserNewImageUnlockNotify) GetNewImageId() string {
	if m != nil {
		return m.NewImageId
	}
	return ""
}

func (m *UserNewImageUnlockNotify) GetNewImageIds() []string {
	if m != nil {
		return m.NewImageIds
	}
	return nil
}

type UserLife struct {
	Tag                  uint32   `protobuf:"varint,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLife) Reset()         { *m = UserLife{} }
func (m *UserLife) String() string { return proto.CompactTextString(m) }
func (*UserLife) ProtoMessage()    {}
func (*UserLife) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{68}
}
func (m *UserLife) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLife.Unmarshal(m, b)
}
func (m *UserLife) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLife.Marshal(b, m, deterministic)
}
func (dst *UserLife) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLife.Merge(dst, src)
}
func (m *UserLife) XXX_Size() int {
	return xxx_messageInfo_UserLife.Size(m)
}
func (m *UserLife) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLife.DiscardUnknown(m)
}

var xxx_messageInfo_UserLife proto.InternalMessageInfo

func (m *UserLife) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *UserLife) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type VoiceWaveText struct {
	Tag                  uint32   `protobuf:"varint,1,opt,name=tag,proto3" json:"tag,omitempty"`
	MidTextList          []string `protobuf:"bytes,2,rep,name=mid_text_list,json=midTextList,proto3" json:"mid_text_list,omitempty"`
	EncourageTextList    []string `protobuf:"bytes,3,rep,name=encourage_text_list,json=encourageTextList,proto3" json:"encourage_text_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoiceWaveText) Reset()         { *m = VoiceWaveText{} }
func (m *VoiceWaveText) String() string { return proto.CompactTextString(m) }
func (*VoiceWaveText) ProtoMessage()    {}
func (*VoiceWaveText) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{69}
}
func (m *VoiceWaveText) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoiceWaveText.Unmarshal(m, b)
}
func (m *VoiceWaveText) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoiceWaveText.Marshal(b, m, deterministic)
}
func (dst *VoiceWaveText) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoiceWaveText.Merge(dst, src)
}
func (m *VoiceWaveText) XXX_Size() int {
	return xxx_messageInfo_VoiceWaveText.Size(m)
}
func (m *VoiceWaveText) XXX_DiscardUnknown() {
	xxx_messageInfo_VoiceWaveText.DiscardUnknown(m)
}

var xxx_messageInfo_VoiceWaveText proto.InternalMessageInfo

func (m *VoiceWaveText) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *VoiceWaveText) GetMidTextList() []string {
	if m != nil {
		return m.MidTextList
	}
	return nil
}

func (m *VoiceWaveText) GetEncourageTextList() []string {
	if m != nil {
		return m.EncourageTextList
	}
	return nil
}

type GetSingAllConfResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserImageConfList    []*UserImageConf  `protobuf:"bytes,2,rep,name=user_image_conf_list,json=userImageConfList,proto3" json:"user_image_conf_list,omitempty"`
	DecorationList       []*DecorationInfo `protobuf:"bytes,3,rep,name=decoration_list,json=decorationList,proto3" json:"decoration_list,omitempty"`
	VoiceWaveText        []*VoiceWaveText  `protobuf:"bytes,4,rep,name=voice_wave_text,json=voiceWaveText,proto3" json:"voice_wave_text,omitempty"`
	Version              uint32            `protobuf:"varint,5,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetSingAllConfResp) Reset()         { *m = GetSingAllConfResp{} }
func (m *GetSingAllConfResp) String() string { return proto.CompactTextString(m) }
func (*GetSingAllConfResp) ProtoMessage()    {}
func (*GetSingAllConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{70}
}
func (m *GetSingAllConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingAllConfResp.Unmarshal(m, b)
}
func (m *GetSingAllConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingAllConfResp.Marshal(b, m, deterministic)
}
func (dst *GetSingAllConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingAllConfResp.Merge(dst, src)
}
func (m *GetSingAllConfResp) XXX_Size() int {
	return xxx_messageInfo_GetSingAllConfResp.Size(m)
}
func (m *GetSingAllConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingAllConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingAllConfResp proto.InternalMessageInfo

func (m *GetSingAllConfResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSingAllConfResp) GetUserImageConfList() []*UserImageConf {
	if m != nil {
		return m.UserImageConfList
	}
	return nil
}

func (m *GetSingAllConfResp) GetDecorationList() []*DecorationInfo {
	if m != nil {
		return m.DecorationList
	}
	return nil
}

func (m *GetSingAllConfResp) GetVoiceWaveText() []*VoiceWaveText {
	if m != nil {
		return m.VoiceWaveText
	}
	return nil
}

func (m *GetSingAllConfResp) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetUserSingImageReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserSingImageReq) Reset()         { *m = GetUserSingImageReq{} }
func (m *GetUserSingImageReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSingImageReq) ProtoMessage()    {}
func (*GetUserSingImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{71}
}
func (m *GetUserSingImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingImageReq.Unmarshal(m, b)
}
func (m *GetUserSingImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingImageReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSingImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingImageReq.Merge(dst, src)
}
func (m *GetUserSingImageReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSingImageReq.Size(m)
}
func (m *GetUserSingImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingImageReq proto.InternalMessageInfo

func (m *GetUserSingImageReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserSingImageResp struct {
	BaseResp             *app.BaseResp                     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ImageIdList          []string                          `protobuf:"bytes,2,rep,name=image_id_list,json=imageIdList,proto3" json:"image_id_list,omitempty"`
	DecorationId         string                            `protobuf:"bytes,3,opt,name=decoration_id,json=decorationId,proto3" json:"decoration_id,omitempty"`
	DecorationEndTs      uint32                            `protobuf:"varint,4,opt,name=decoration_end_ts,json=decorationEndTs,proto3" json:"decoration_end_ts,omitempty"`
	UserSetImageId       string                            `protobuf:"bytes,5,opt,name=user_set_image_id,json=userSetImageId,proto3" json:"user_set_image_id,omitempty"`
	LockImages           []*GetUserSingImageResp_LockImage `protobuf:"bytes,6,rep,name=lock_images,json=lockImages,proto3" json:"lock_images,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetUserSingImageResp) Reset()         { *m = GetUserSingImageResp{} }
func (m *GetUserSingImageResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSingImageResp) ProtoMessage()    {}
func (*GetUserSingImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{72}
}
func (m *GetUserSingImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingImageResp.Unmarshal(m, b)
}
func (m *GetUserSingImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingImageResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSingImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingImageResp.Merge(dst, src)
}
func (m *GetUserSingImageResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSingImageResp.Size(m)
}
func (m *GetUserSingImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingImageResp proto.InternalMessageInfo

func (m *GetUserSingImageResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserSingImageResp) GetImageIdList() []string {
	if m != nil {
		return m.ImageIdList
	}
	return nil
}

func (m *GetUserSingImageResp) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

func (m *GetUserSingImageResp) GetDecorationEndTs() uint32 {
	if m != nil {
		return m.DecorationEndTs
	}
	return 0
}

func (m *GetUserSingImageResp) GetUserSetImageId() string {
	if m != nil {
		return m.UserSetImageId
	}
	return ""
}

func (m *GetUserSingImageResp) GetLockImages() []*GetUserSingImageResp_LockImage {
	if m != nil {
		return m.LockImages
	}
	return nil
}

type GetUserSingImageResp_LockImage struct {
	ImageId              string   `protobuf:"bytes,1,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	Tip                  string   `protobuf:"bytes,2,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSingImageResp_LockImage) Reset()         { *m = GetUserSingImageResp_LockImage{} }
func (m *GetUserSingImageResp_LockImage) String() string { return proto.CompactTextString(m) }
func (*GetUserSingImageResp_LockImage) ProtoMessage()    {}
func (*GetUserSingImageResp_LockImage) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{72, 0}
}
func (m *GetUserSingImageResp_LockImage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingImageResp_LockImage.Unmarshal(m, b)
}
func (m *GetUserSingImageResp_LockImage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingImageResp_LockImage.Marshal(b, m, deterministic)
}
func (dst *GetUserSingImageResp_LockImage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingImageResp_LockImage.Merge(dst, src)
}
func (m *GetUserSingImageResp_LockImage) XXX_Size() int {
	return xxx_messageInfo_GetUserSingImageResp_LockImage.Size(m)
}
func (m *GetUserSingImageResp_LockImage) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingImageResp_LockImage.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingImageResp_LockImage proto.InternalMessageInfo

func (m *GetUserSingImageResp_LockImage) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

func (m *GetUserSingImageResp_LockImage) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

type SetUserSingImageReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ImageId              string       `protobuf:"bytes,2,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetUserSingImageReq) Reset()         { *m = SetUserSingImageReq{} }
func (m *SetUserSingImageReq) String() string { return proto.CompactTextString(m) }
func (*SetUserSingImageReq) ProtoMessage()    {}
func (*SetUserSingImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{73}
}
func (m *SetUserSingImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSingImageReq.Unmarshal(m, b)
}
func (m *SetUserSingImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSingImageReq.Marshal(b, m, deterministic)
}
func (dst *SetUserSingImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSingImageReq.Merge(dst, src)
}
func (m *SetUserSingImageReq) XXX_Size() int {
	return xxx_messageInfo_SetUserSingImageReq.Size(m)
}
func (m *SetUserSingImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSingImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSingImageReq proto.InternalMessageInfo

func (m *SetUserSingImageReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUserSingImageReq) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

func (m *SetUserSingImageReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SetUserSingImageResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUserSingImageResp) Reset()         { *m = SetUserSingImageResp{} }
func (m *SetUserSingImageResp) String() string { return proto.CompactTextString(m) }
func (*SetUserSingImageResp) ProtoMessage()    {}
func (*SetUserSingImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{74}
}
func (m *SetUserSingImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSingImageResp.Unmarshal(m, b)
}
func (m *SetUserSingImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSingImageResp.Marshal(b, m, deterministic)
}
func (dst *SetUserSingImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSingImageResp.Merge(dst, src)
}
func (m *SetUserSingImageResp) XXX_Size() int {
	return xxx_messageInfo_SetUserSingImageResp.Size(m)
}
func (m *SetUserSingImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSingImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSingImageResp proto.InternalMessageInfo

func (m *SetUserSingImageResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetSingingGameBgReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Version              uint32       `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSingingGameBgReq) Reset()         { *m = GetSingingGameBgReq{} }
func (m *GetSingingGameBgReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameBgReq) ProtoMessage()    {}
func (*GetSingingGameBgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{75}
}
func (m *GetSingingGameBgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameBgReq.Unmarshal(m, b)
}
func (m *GetSingingGameBgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameBgReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameBgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameBgReq.Merge(dst, src)
}
func (m *GetSingingGameBgReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameBgReq.Size(m)
}
func (m *GetSingingGameBgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameBgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameBgReq proto.InternalMessageInfo

func (m *GetSingingGameBgReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSingingGameBgReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetSingingGameBgResp struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ExpertRoomBg         *GetSingingGameBgResp_Bg `protobuf:"bytes,2,opt,name=expert_room_bg,json=expertRoomBg,proto3" json:"expert_room_bg,omitempty"`
	RookieRoomBg         *GetSingingGameBgResp_Bg `protobuf:"bytes,3,opt,name=rookie_room_bg,json=rookieRoomBg,proto3" json:"rookie_room_bg,omitempty"`
	Version              uint32                   `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetSingingGameBgResp) Reset()         { *m = GetSingingGameBgResp{} }
func (m *GetSingingGameBgResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameBgResp) ProtoMessage()    {}
func (*GetSingingGameBgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{76}
}
func (m *GetSingingGameBgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameBgResp.Unmarshal(m, b)
}
func (m *GetSingingGameBgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameBgResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameBgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameBgResp.Merge(dst, src)
}
func (m *GetSingingGameBgResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameBgResp.Size(m)
}
func (m *GetSingingGameBgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameBgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameBgResp proto.InternalMessageInfo

func (m *GetSingingGameBgResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSingingGameBgResp) GetExpertRoomBg() *GetSingingGameBgResp_Bg {
	if m != nil {
		return m.ExpertRoomBg
	}
	return nil
}

func (m *GetSingingGameBgResp) GetRookieRoomBg() *GetSingingGameBgResp_Bg {
	if m != nil {
		return m.RookieRoomBg
	}
	return nil
}

func (m *GetSingingGameBgResp) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetSingingGameBgResp_Bg struct {
	PreparingStageUrl    string   `protobuf:"bytes,1,opt,name=preparing_stage_url,json=preparingStageUrl,proto3" json:"preparing_stage_url,omitempty"`
	PlayingStageUrl      string   `protobuf:"bytes,2,opt,name=playing_stage_url,json=playingStageUrl,proto3" json:"playing_stage_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingingGameBgResp_Bg) Reset()         { *m = GetSingingGameBgResp_Bg{} }
func (m *GetSingingGameBgResp_Bg) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameBgResp_Bg) ProtoMessage()    {}
func (*GetSingingGameBgResp_Bg) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{76, 0}
}
func (m *GetSingingGameBgResp_Bg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameBgResp_Bg.Unmarshal(m, b)
}
func (m *GetSingingGameBgResp_Bg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameBgResp_Bg.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameBgResp_Bg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameBgResp_Bg.Merge(dst, src)
}
func (m *GetSingingGameBgResp_Bg) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameBgResp_Bg.Size(m)
}
func (m *GetSingingGameBgResp_Bg) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameBgResp_Bg.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameBgResp_Bg proto.InternalMessageInfo

func (m *GetSingingGameBgResp_Bg) GetPreparingStageUrl() string {
	if m != nil {
		return m.PreparingStageUrl
	}
	return ""
}

func (m *GetSingingGameBgResp_Bg) GetPlayingStageUrl() string {
	if m != nil {
		return m.PlayingStageUrl
	}
	return ""
}

type SpecialGameZoneInfo struct {
	Style                uint32   `protobuf:"varint,1,opt,name=style,proto3" json:"style,omitempty"`
	IsUserChannel        bool     `protobuf:"varint,2,opt,name=is_user_channel,json=isUserChannel,proto3" json:"is_user_channel,omitempty"`
	IsNeedShare          bool     `protobuf:"varint,3,opt,name=is_need_share,json=isNeedShare,proto3" json:"is_need_share,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,5,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SpecialGameZoneInfo) Reset()         { *m = SpecialGameZoneInfo{} }
func (m *SpecialGameZoneInfo) String() string { return proto.CompactTextString(m) }
func (*SpecialGameZoneInfo) ProtoMessage()    {}
func (*SpecialGameZoneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{77}
}
func (m *SpecialGameZoneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SpecialGameZoneInfo.Unmarshal(m, b)
}
func (m *SpecialGameZoneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SpecialGameZoneInfo.Marshal(b, m, deterministic)
}
func (dst *SpecialGameZoneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpecialGameZoneInfo.Merge(dst, src)
}
func (m *SpecialGameZoneInfo) XXX_Size() int {
	return xxx_messageInfo_SpecialGameZoneInfo.Size(m)
}
func (m *SpecialGameZoneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SpecialGameZoneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SpecialGameZoneInfo proto.InternalMessageInfo

func (m *SpecialGameZoneInfo) GetStyle() uint32 {
	if m != nil {
		return m.Style
	}
	return 0
}

func (m *SpecialGameZoneInfo) GetIsUserChannel() bool {
	if m != nil {
		return m.IsUserChannel
	}
	return false
}

func (m *SpecialGameZoneInfo) GetIsNeedShare() bool {
	if m != nil {
		return m.IsNeedShare
	}
	return false
}

func (m *SpecialGameZoneInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SpecialGameZoneInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type SingingGameInfo struct {
	Type                 uint32               `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Descr                string               `protobuf:"bytes,3,opt,name=descr,proto3" json:"descr,omitempty"`
	InletIcon            string               `protobuf:"bytes,4,opt,name=inlet_icon,json=inletIcon,proto3" json:"inlet_icon,omitempty"`
	InletColor           string               `protobuf:"bytes,5,opt,name=inlet_color,json=inletColor,proto3" json:"inlet_color,omitempty"`
	ReadyCover           string               `protobuf:"bytes,6,opt,name=ready_cover,json=readyCover,proto3" json:"ready_cover,omitempty"`
	ZoneButton           string               `protobuf:"bytes,7,opt,name=zone_button,json=zoneButton,proto3" json:"zone_button,omitempty"`
	ZoneReadyBg          string               `protobuf:"bytes,8,opt,name=zone_ready_bg,json=zoneReadyBg,proto3" json:"zone_ready_bg,omitempty"`
	ZoneBg               string               `protobuf:"bytes,9,opt,name=zone_bg,json=zoneBg,proto3" json:"zone_bg,omitempty"`
	TimeLimitIcon        string               `protobuf:"bytes,10,opt,name=time_limit_icon,json=timeLimitIcon,proto3" json:"time_limit_icon,omitempty"`
	SpecialGameInfo      *SpecialGameZoneInfo `protobuf:"bytes,11,opt,name=special_game_info,json=specialGameInfo,proto3" json:"special_game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SingingGameInfo) Reset()         { *m = SingingGameInfo{} }
func (m *SingingGameInfo) String() string { return proto.CompactTextString(m) }
func (*SingingGameInfo) ProtoMessage()    {}
func (*SingingGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{78}
}
func (m *SingingGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameInfo.Unmarshal(m, b)
}
func (m *SingingGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameInfo.Marshal(b, m, deterministic)
}
func (dst *SingingGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameInfo.Merge(dst, src)
}
func (m *SingingGameInfo) XXX_Size() int {
	return xxx_messageInfo_SingingGameInfo.Size(m)
}
func (m *SingingGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameInfo proto.InternalMessageInfo

func (m *SingingGameInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SingingGameInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SingingGameInfo) GetDescr() string {
	if m != nil {
		return m.Descr
	}
	return ""
}

func (m *SingingGameInfo) GetInletIcon() string {
	if m != nil {
		return m.InletIcon
	}
	return ""
}

func (m *SingingGameInfo) GetInletColor() string {
	if m != nil {
		return m.InletColor
	}
	return ""
}

func (m *SingingGameInfo) GetReadyCover() string {
	if m != nil {
		return m.ReadyCover
	}
	return ""
}

func (m *SingingGameInfo) GetZoneButton() string {
	if m != nil {
		return m.ZoneButton
	}
	return ""
}

func (m *SingingGameInfo) GetZoneReadyBg() string {
	if m != nil {
		return m.ZoneReadyBg
	}
	return ""
}

func (m *SingingGameInfo) GetZoneBg() string {
	if m != nil {
		return m.ZoneBg
	}
	return ""
}

func (m *SingingGameInfo) GetTimeLimitIcon() string {
	if m != nil {
		return m.TimeLimitIcon
	}
	return ""
}

func (m *SingingGameInfo) GetSpecialGameInfo() *SpecialGameZoneInfo {
	if m != nil {
		return m.SpecialGameInfo
	}
	return nil
}

type GetSingingGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	CurrentGameType      uint32       `protobuf:"varint,2,opt,name=current_game_type,json=currentGameType,proto3" json:"current_game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSingingGameReq) Reset()         { *m = GetSingingGameReq{} }
func (m *GetSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameReq) ProtoMessage()    {}
func (*GetSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{79}
}
func (m *GetSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameReq.Unmarshal(m, b)
}
func (m *GetSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameReq.Merge(dst, src)
}
func (m *GetSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameReq.Size(m)
}
func (m *GetSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameReq proto.InternalMessageInfo

func (m *GetSingingGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSingingGameReq) GetCurrentGameType() uint32 {
	if m != nil {
		return m.CurrentGameType
	}
	return 0
}

type GetSingingGameResp struct {
	BaseResp             *app.BaseResp               `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SingingGameInfos     []*SingingGameInfo          `protobuf:"bytes,2,rep,name=singing_game_infos,json=singingGameInfos,proto3" json:"singing_game_infos,omitempty"`
	ExtraInfo            map[uint32]*SingingGameInfo `protobuf:"bytes,3,rep,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetSingingGameResp) Reset()         { *m = GetSingingGameResp{} }
func (m *GetSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameResp) ProtoMessage()    {}
func (*GetSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{80}
}
func (m *GetSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameResp.Unmarshal(m, b)
}
func (m *GetSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameResp.Merge(dst, src)
}
func (m *GetSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameResp.Size(m)
}
func (m *GetSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameResp proto.InternalMessageInfo

func (m *GetSingingGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSingingGameResp) GetSingingGameInfos() []*SingingGameInfo {
	if m != nil {
		return m.SingingGameInfos
	}
	return nil
}

func (m *GetSingingGameResp) GetExtraInfo() map[uint32]*SingingGameInfo {
	if m != nil {
		return m.ExtraInfo
	}
	return nil
}

// 客户端上报音频文件
type AppReportAudioNotify struct {
	ReportType           uint32                    `protobuf:"varint,1,opt,name=report_type,json=reportType,proto3" json:"report_type,omitempty"`
	ResultInfo           *NotApproveSingResultInfo `protobuf:"bytes,2,opt,name=result_info,json=resultInfo,proto3" json:"result_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *AppReportAudioNotify) Reset()         { *m = AppReportAudioNotify{} }
func (m *AppReportAudioNotify) String() string { return proto.CompactTextString(m) }
func (*AppReportAudioNotify) ProtoMessage()    {}
func (*AppReportAudioNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{81}
}
func (m *AppReportAudioNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppReportAudioNotify.Unmarshal(m, b)
}
func (m *AppReportAudioNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppReportAudioNotify.Marshal(b, m, deterministic)
}
func (dst *AppReportAudioNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppReportAudioNotify.Merge(dst, src)
}
func (m *AppReportAudioNotify) XXX_Size() int {
	return xxx_messageInfo_AppReportAudioNotify.Size(m)
}
func (m *AppReportAudioNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_AppReportAudioNotify.DiscardUnknown(m)
}

var xxx_messageInfo_AppReportAudioNotify proto.InternalMessageInfo

func (m *AppReportAudioNotify) GetReportType() uint32 {
	if m != nil {
		return m.ReportType
	}
	return 0
}

func (m *AppReportAudioNotify) GetResultInfo() *NotApproveSingResultInfo {
	if m != nil {
		return m.ResultInfo
	}
	return nil
}

type NotApproveSingResultInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LibType              uint32   `protobuf:"varint,4,opt,name=lib_type,json=libType,proto3" json:"lib_type,omitempty"`
	RoundId              uint32   `protobuf:"varint,5,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string   `protobuf:"bytes,6,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotApproveSingResultInfo) Reset()         { *m = NotApproveSingResultInfo{} }
func (m *NotApproveSingResultInfo) String() string { return proto.CompactTextString(m) }
func (*NotApproveSingResultInfo) ProtoMessage()    {}
func (*NotApproveSingResultInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{82}
}
func (m *NotApproveSingResultInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotApproveSingResultInfo.Unmarshal(m, b)
}
func (m *NotApproveSingResultInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotApproveSingResultInfo.Marshal(b, m, deterministic)
}
func (dst *NotApproveSingResultInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotApproveSingResultInfo.Merge(dst, src)
}
func (m *NotApproveSingResultInfo) XXX_Size() int {
	return xxx_messageInfo_NotApproveSingResultInfo.Size(m)
}
func (m *NotApproveSingResultInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NotApproveSingResultInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NotApproveSingResultInfo proto.InternalMessageInfo

func (m *NotApproveSingResultInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotApproveSingResultInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *NotApproveSingResultInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NotApproveSingResultInfo) GetLibType() uint32 {
	if m != nil {
		return m.LibType
	}
	return 0
}

func (m *NotApproveSingResultInfo) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *NotApproveSingResultInfo) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

// 客户端不认可结果
type NotApproveSingResultReq struct {
	BaseReq              *app.BaseReq              `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ResultInfo           *NotApproveSingResultInfo `protobuf:"bytes,2,opt,name=result_info,json=resultInfo,proto3" json:"result_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *NotApproveSingResultReq) Reset()         { *m = NotApproveSingResultReq{} }
func (m *NotApproveSingResultReq) String() string { return proto.CompactTextString(m) }
func (*NotApproveSingResultReq) ProtoMessage()    {}
func (*NotApproveSingResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{83}
}
func (m *NotApproveSingResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotApproveSingResultReq.Unmarshal(m, b)
}
func (m *NotApproveSingResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotApproveSingResultReq.Marshal(b, m, deterministic)
}
func (dst *NotApproveSingResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotApproveSingResultReq.Merge(dst, src)
}
func (m *NotApproveSingResultReq) XXX_Size() int {
	return xxx_messageInfo_NotApproveSingResultReq.Size(m)
}
func (m *NotApproveSingResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotApproveSingResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotApproveSingResultReq proto.InternalMessageInfo

func (m *NotApproveSingResultReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *NotApproveSingResultReq) GetResultInfo() *NotApproveSingResultInfo {
	if m != nil {
		return m.ResultInfo
	}
	return nil
}

type NotApproveSingResultResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *NotApproveSingResultResp) Reset()         { *m = NotApproveSingResultResp{} }
func (m *NotApproveSingResultResp) String() string { return proto.CompactTextString(m) }
func (*NotApproveSingResultResp) ProtoMessage()    {}
func (*NotApproveSingResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{84}
}
func (m *NotApproveSingResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotApproveSingResultResp.Unmarshal(m, b)
}
func (m *NotApproveSingResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotApproveSingResultResp.Marshal(b, m, deterministic)
}
func (dst *NotApproveSingResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotApproveSingResultResp.Merge(dst, src)
}
func (m *NotApproveSingResultResp) XXX_Size() int {
	return xxx_messageInfo_NotApproveSingResultResp.Size(m)
}
func (m *NotApproveSingResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotApproveSingResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotApproveSingResultResp proto.InternalMessageInfo

func (m *NotApproveSingResultResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetSingingGameUserInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSingingGameUserInfoReq) Reset()         { *m = GetSingingGameUserInfoReq{} }
func (m *GetSingingGameUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameUserInfoReq) ProtoMessage()    {}
func (*GetSingingGameUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{85}
}
func (m *GetSingingGameUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameUserInfoReq.Unmarshal(m, b)
}
func (m *GetSingingGameUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameUserInfoReq.Merge(dst, src)
}
func (m *GetSingingGameUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameUserInfoReq.Size(m)
}
func (m *GetSingingGameUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameUserInfoReq proto.InternalMessageInfo

func (m *GetSingingGameUserInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSingingGameUserInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSingingGameUserInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	GrabMicTime          uint32        `protobuf:"varint,2,opt,name=grab_mic_time,json=grabMicTime,proto3" json:"grab_mic_time,omitempty"`
	GrabMicSuccessRate   float32       `protobuf:"fixed32,3,opt,name=grab_mic_success_rate,json=grabMicSuccessRate,proto3" json:"grab_mic_success_rate,omitempty"`
	LikeCount            uint32        `protobuf:"varint,4,opt,name=like_count,json=likeCount,proto3" json:"like_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSingingGameUserInfoResp) Reset()         { *m = GetSingingGameUserInfoResp{} }
func (m *GetSingingGameUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameUserInfoResp) ProtoMessage()    {}
func (*GetSingingGameUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c, []int{86}
}
func (m *GetSingingGameUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameUserInfoResp.Unmarshal(m, b)
}
func (m *GetSingingGameUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameUserInfoResp.Merge(dst, src)
}
func (m *GetSingingGameUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameUserInfoResp.Size(m)
}
func (m *GetSingingGameUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameUserInfoResp proto.InternalMessageInfo

func (m *GetSingingGameUserInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSingingGameUserInfoResp) GetGrabMicTime() uint32 {
	if m != nil {
		return m.GrabMicTime
	}
	return 0
}

func (m *GetSingingGameUserInfoResp) GetGrabMicSuccessRate() float32 {
	if m != nil {
		return m.GrabMicSuccessRate
	}
	return 0
}

func (m *GetSingingGameUserInfoResp) GetLikeCount() uint32 {
	if m != nil {
		return m.LikeCount
	}
	return 0
}

func init() {
	proto.RegisterType((*StartSingingGameMatchingReq)(nil), "ga.sing_a_round_logic.StartSingingGameMatchingReq")
	proto.RegisterType((*StartSingingGameMatchingResp)(nil), "ga.sing_a_round_logic.StartSingingGameMatchingResp")
	proto.RegisterType((*JoinSingingGameReq)(nil), "ga.sing_a_round_logic.JoinSingingGameReq")
	proto.RegisterType((*JoinSingingGameResp)(nil), "ga.sing_a_round_logic.JoinSingingGameResp")
	proto.RegisterType((*SingingGameSong)(nil), "ga.sing_a_round_logic.SingingGameSong")
	proto.RegisterType((*SingingGameChannelInfoNotify)(nil), "ga.sing_a_round_logic.SingingGameChannelInfoNotify")
	proto.RegisterType((*SingingGameChannelInfo)(nil), "ga.sing_a_round_logic.SingingGameChannelInfo")
	proto.RegisterType((*PassThroughExtra)(nil), "ga.sing_a_round_logic.PassThroughExtra")
	proto.RegisterType((*SingingGamePosition)(nil), "ga.sing_a_round_logic.SingingGamePosition")
	proto.RegisterType((*GetSingingGameCountdownReq)(nil), "ga.sing_a_round_logic.GetSingingGameCountdownReq")
	proto.RegisterType((*GetSingingGameCountdownResp)(nil), "ga.sing_a_round_logic.GetSingingGameCountdownResp")
	proto.RegisterType((*StartSingingGameReq)(nil), "ga.sing_a_round_logic.StartSingingGameReq")
	proto.RegisterType((*StartSingingGameResp)(nil), "ga.sing_a_round_logic.StartSingingGameResp")
	proto.RegisterType((*CancelSingingGamePreparationReq)(nil), "ga.sing_a_round_logic.CancelSingingGamePreparationReq")
	proto.RegisterType((*CancelSingingGamePreparationResp)(nil), "ga.sing_a_round_logic.CancelSingingGamePreparationResp")
	proto.RegisterType((*StartUgcChannelSingingGameReq)(nil), "ga.sing_a_round_logic.StartUgcChannelSingingGameReq")
	proto.RegisterType((*StartUgcChannelSingingGameResp)(nil), "ga.sing_a_round_logic.StartUgcChannelSingingGameResp")
	proto.RegisterType((*SingingGameVoteNotify)(nil), "ga.sing_a_round_logic.SingingGameVoteNotify")
	proto.RegisterType((*SingingGameSongStyle)(nil), "ga.sing_a_round_logic.SingingGameSongStyle")
	proto.RegisterType((*SingingGamePreloadNotify)(nil), "ga.sing_a_round_logic.SingingGamePreloadNotify")
	proto.RegisterType((*SingingGameVoteReq)(nil), "ga.sing_a_round_logic.SingingGameVoteReq")
	proto.RegisterType((*SingingGameVoteResp)(nil), "ga.sing_a_round_logic.SingingGameVoteResp")
	proto.RegisterType((*AccomplishLoadingResReq)(nil), "ga.sing_a_round_logic.AccomplishLoadingResReq")
	proto.RegisterType((*AccomplishLoadingResResp)(nil), "ga.sing_a_round_logic.AccomplishLoadingResResp")
	proto.RegisterType((*GetSingingGameRankListReq)(nil), "ga.sing_a_round_logic.GetSingingGameRankListReq")
	proto.RegisterType((*GetSingingGameRankListResp)(nil), "ga.sing_a_round_logic.GetSingingGameRankListResp")
	proto.RegisterType((*SingingGameRankListItem)(nil), "ga.sing_a_round_logic.SingingGameRankListItem")
	proto.RegisterType((*GetSingingGameRecordListReq)(nil), "ga.sing_a_round_logic.GetSingingGameRecordListReq")
	proto.RegisterType((*GetSingingGameRecordListResp)(nil), "ga.sing_a_round_logic.GetSingingGameRecordListResp")
	proto.RegisterType((*SingingGameLoadMore)(nil), "ga.sing_a_round_logic.SingingGameLoadMore")
	proto.RegisterType((*GrabSingingGameMicNotify)(nil), "ga.sing_a_round_logic.GrabSingingGameMicNotify")
	proto.RegisterType((*GrabSingingGameMicReq)(nil), "ga.sing_a_round_logic.GrabSingingGameMicReq")
	proto.RegisterType((*GrabSingingGameMicResp)(nil), "ga.sing_a_round_logic.GrabSingingGameMicResp")
	proto.RegisterType((*AskForSingingHelpReq)(nil), "ga.sing_a_round_logic.AskForSingingHelpReq")
	proto.RegisterType((*AskForSingingHelpResp)(nil), "ga.sing_a_round_logic.AskForSingingHelpResp")
	proto.RegisterType((*SingingHelpNotify)(nil), "ga.sing_a_round_logic.SingingHelpNotify")
	proto.RegisterType((*AnswerSingingHelpReq)(nil), "ga.sing_a_round_logic.AnswerSingingHelpReq")
	proto.RegisterType((*AnswerSingingHelpResp)(nil), "ga.sing_a_round_logic.AnswerSingingHelpResp")
	proto.RegisterType((*AccomplishSingingGameSongReq)(nil), "ga.sing_a_round_logic.AccomplishSingingGameSongReq")
	proto.RegisterType((*AccomplishSingingGameSongResp)(nil), "ga.sing_a_round_logic.AccomplishSingingGameSongResp")
	proto.RegisterType((*ReportSingingGameSongScoreReq)(nil), "ga.sing_a_round_logic.ReportSingingGameSongScoreReq")
	proto.RegisterType((*ReportSingingGameSongScoreResp)(nil), "ga.sing_a_round_logic.ReportSingingGameSongScoreResp")
	proto.RegisterType((*SwitchSingingGameTypeReq)(nil), "ga.sing_a_round_logic.SwitchSingingGameTypeReq")
	proto.RegisterType((*SwitchSingingGameTypeResp)(nil), "ga.sing_a_round_logic.SwitchSingingGameTypeResp")
	proto.RegisterType((*GetSingingGameChannelInfoReq)(nil), "ga.sing_a_round_logic.GetSingingGameChannelInfoReq")
	proto.RegisterType((*GetSingingGameChannelInfoResp)(nil), "ga.sing_a_round_logic.GetSingingGameChannelInfoResp")
	proto.RegisterType((*SingingGameSongResultNotify)(nil), "ga.sing_a_round_logic.SingingGameSongResultNotify")
	proto.RegisterType((*SingingGameResultNotify)(nil), "ga.sing_a_round_logic.SingingGameResultNotify")
	proto.RegisterType((*SwitchSingingGameTypeNotify)(nil), "ga.sing_a_round_logic.SwitchSingingGameTypeNotify")
	proto.RegisterType((*SingingGameSongResult)(nil), "ga.sing_a_round_logic.SingingGameSongResult")
	proto.RegisterType((*SingingGameScoreDetail)(nil), "ga.sing_a_round_logic.SingingGameScoreDetail")
	proto.RegisterType((*CoupleUserDayScoreInfo)(nil), "ga.sing_a_round_logic.CoupleUserDayScoreInfo")
	proto.RegisterType((*SingingGameResult)(nil), "ga.sing_a_round_logic.SingingGameResult")
	proto.RegisterType((*SingingGameScore)(nil), "ga.sing_a_round_logic.SingingGameScore")
	proto.RegisterType((*SingingGameHelp)(nil), "ga.sing_a_round_logic.SingingGameHelp")
	proto.RegisterType((*ExpressSingingGameLikeReq)(nil), "ga.sing_a_round_logic.ExpressSingingGameLikeReq")
	proto.RegisterType((*ExpressSingingGameLikeResp)(nil), "ga.sing_a_round_logic.ExpressSingingGameLikeResp")
	proto.RegisterType((*SingingGameLikeMsg)(nil), "ga.sing_a_round_logic.SingingGameLikeMsg")
	proto.RegisterType((*SingingGameLikeNotify)(nil), "ga.sing_a_round_logic.SingingGameLikeNotify")
	proto.RegisterType((*SingingGameUser)(nil), "ga.sing_a_round_logic.SingingGameUser")
	proto.RegisterType((*SingingGameLikesNotify)(nil), "ga.sing_a_round_logic.SingingGameLikesNotify")
	proto.RegisterType((*SingingGameFistBumpReq)(nil), "ga.sing_a_round_logic.SingingGameFistBumpReq")
	proto.RegisterType((*SingingGameFistBumpResp)(nil), "ga.sing_a_round_logic.SingingGameFistBumpResp")
	proto.RegisterType((*SingingGameFistBumpNotify)(nil), "ga.sing_a_round_logic.SingingGameFistBumpNotify")
	proto.RegisterType((*UserImageConf)(nil), "ga.sing_a_round_logic.UserImageConf")
	proto.RegisterType((*DecorationInfo)(nil), "ga.sing_a_round_logic.DecorationInfo")
	proto.RegisterType((*GetSingAllConfReq)(nil), "ga.sing_a_round_logic.GetSingAllConfReq")
	proto.RegisterType((*UserNewImageUnlockNotify)(nil), "ga.sing_a_round_logic.UserNewImageUnlockNotify")
	proto.RegisterType((*UserLife)(nil), "ga.sing_a_round_logic.UserLife")
	proto.RegisterType((*VoiceWaveText)(nil), "ga.sing_a_round_logic.VoiceWaveText")
	proto.RegisterType((*GetSingAllConfResp)(nil), "ga.sing_a_round_logic.GetSingAllConfResp")
	proto.RegisterType((*GetUserSingImageReq)(nil), "ga.sing_a_round_logic.GetUserSingImageReq")
	proto.RegisterType((*GetUserSingImageResp)(nil), "ga.sing_a_round_logic.GetUserSingImageResp")
	proto.RegisterType((*GetUserSingImageResp_LockImage)(nil), "ga.sing_a_round_logic.GetUserSingImageResp.LockImage")
	proto.RegisterType((*SetUserSingImageReq)(nil), "ga.sing_a_round_logic.SetUserSingImageReq")
	proto.RegisterType((*SetUserSingImageResp)(nil), "ga.sing_a_round_logic.SetUserSingImageResp")
	proto.RegisterType((*GetSingingGameBgReq)(nil), "ga.sing_a_round_logic.GetSingingGameBgReq")
	proto.RegisterType((*GetSingingGameBgResp)(nil), "ga.sing_a_round_logic.GetSingingGameBgResp")
	proto.RegisterType((*GetSingingGameBgResp_Bg)(nil), "ga.sing_a_round_logic.GetSingingGameBgResp.Bg")
	proto.RegisterType((*SpecialGameZoneInfo)(nil), "ga.sing_a_round_logic.SpecialGameZoneInfo")
	proto.RegisterType((*SingingGameInfo)(nil), "ga.sing_a_round_logic.SingingGameInfo")
	proto.RegisterType((*GetSingingGameReq)(nil), "ga.sing_a_round_logic.GetSingingGameReq")
	proto.RegisterType((*GetSingingGameResp)(nil), "ga.sing_a_round_logic.GetSingingGameResp")
	proto.RegisterMapType((map[uint32]*SingingGameInfo)(nil), "ga.sing_a_round_logic.GetSingingGameResp.ExtraInfoEntry")
	proto.RegisterType((*AppReportAudioNotify)(nil), "ga.sing_a_round_logic.AppReportAudioNotify")
	proto.RegisterType((*NotApproveSingResultInfo)(nil), "ga.sing_a_round_logic.NotApproveSingResultInfo")
	proto.RegisterType((*NotApproveSingResultReq)(nil), "ga.sing_a_round_logic.NotApproveSingResultReq")
	proto.RegisterType((*NotApproveSingResultResp)(nil), "ga.sing_a_round_logic.NotApproveSingResultResp")
	proto.RegisterType((*GetSingingGameUserInfoReq)(nil), "ga.sing_a_round_logic.GetSingingGameUserInfoReq")
	proto.RegisterType((*GetSingingGameUserInfoResp)(nil), "ga.sing_a_round_logic.GetSingingGameUserInfoResp")
	proto.RegisterEnum("ga.sing_a_round_logic.SingingGameType", SingingGameType_name, SingingGameType_value)
	proto.RegisterEnum("ga.sing_a_round_logic.SingingGameRoundStage", SingingGameRoundStage_name, SingingGameRoundStage_value)
	proto.RegisterEnum("ga.sing_a_round_logic.SingingGameUserType", SingingGameUserType_name, SingingGameUserType_value)
	proto.RegisterEnum("ga.sing_a_round_logic.RankListType", RankListType_name, RankListType_value)
	proto.RegisterEnum("ga.sing_a_round_logic.SingingGameResultType", SingingGameResultType_name, SingingGameResultType_value)
	proto.RegisterEnum("ga.sing_a_round_logic.SingingGameLikeType", SingingGameLikeType_name, SingingGameLikeType_value)
	proto.RegisterEnum("ga.sing_a_round_logic.UserImageType", UserImageType_name, UserImageType_value)
	proto.RegisterEnum("ga.sing_a_round_logic.AppReportAudioNotify_ReportType", AppReportAudioNotify_ReportType_name, AppReportAudioNotify_ReportType_value)
}

func init() {
	proto.RegisterFile("sing-a-round-logic_.proto", fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c)
}

var fileDescriptor_sing_a_round_logic__9b1cdd543c8ebb1c = []byte{
	// 4397 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x3c, 0x5d, 0x6f, 0x24, 0x49,
	0x52, 0x5b, 0xdd, 0xb6, 0xdb, 0x1d, 0xdd, 0x6d, 0x97, 0xd3, 0x9e, 0x99, 0x9e, 0xaf, 0x5d, 0x6f,
	0xed, 0xee, 0xec, 0x9c, 0x77, 0xd7, 0xa3, 0xf5, 0x69, 0xc4, 0x0a, 0x2d, 0xba, 0xb3, 0x3d, 0xb3,
	0x33, 0x66, 0x67, 0x06, 0x53, 0x1e, 0xcf, 0x4a, 0xc7, 0x43, 0x51, 0x5d, 0x95, 0x2e, 0xa7, 0x5c,
	0x5d, 0x59, 0x53, 0x55, 0x6d, 0x4f, 0x03, 0x42, 0x02, 0x84, 0x10, 0x2f, 0x27, 0xc1, 0x21, 0x9d,
	0x90, 0x78, 0x44, 0x82, 0x93, 0xee, 0x24, 0x04, 0xf7, 0x82, 0x10, 0x20, 0x90, 0x78, 0x02, 0xf1,
	0xc2, 0x71, 0x8f, 0x88, 0x17, 0xc4, 0x21, 0xc1, 0x13, 0x3f, 0x00, 0x94, 0x91, 0x59, 0xd5, 0x59,
	0xfd, 0xe1, 0x71, 0xcf, 0x37, 0xe2, 0xad, 0x32, 0x32, 0x32, 0x32, 0x33, 0x32, 0x22, 0x32, 0x32,
	0x32, 0xb2, 0xe0, 0x62, 0xca, 0xa2, 0xe0, 0x13, 0xf7, 0x93, 0x84, 0xf7, 0x22, 0xff, 0x93, 0x90,
	0x07, 0xcc, 0x73, 0xd6, 0xe3, 0x84, 0x67, 0x9c, 0x9c, 0x0b, 0xdc, 0x75, 0x51, 0xeb, 0xb8, 0x0e,
	0xd6, 0x3a, 0x58, 0x7b, 0xa9, 0x15, 0xb8, 0x4e, 0xc7, 0x4d, 0xa9, 0xc4, 0xb2, 0xfe, 0xc1, 0x80,
	0xcb, 0x7b, 0x99, 0x9b, 0x64, 0x7b, 0x2c, 0x0a, 0x58, 0x14, 0xdc, 0x71, 0xbb, 0xf4, 0xbe, 0x9b,
	0x79, 0x87, 0x2c, 0x0a, 0x6c, 0xfa, 0x98, 0x5c, 0x83, 0x79, 0x81, 0xed, 0x24, 0xf4, 0x71, 0xdb,
	0x58, 0x35, 0xae, 0x37, 0x36, 0x1a, 0xeb, 0x81, 0xbb, 0xbe, 0xe5, 0xa6, 0xd4, 0xa6, 0x8f, 0xed,
	0x5a, 0x47, 0x7e, 0x10, 0x1b, 0x96, 0x52, 0x49, 0xc1, 0x09, 0xdc, 0x2e, 0x75, 0xb2, 0x7e, 0x4c,
	0xdb, 0x95, 0x55, 0xe3, 0xfa, 0xc2, 0xc6, 0xb5, 0xf5, 0xb1, 0x23, 0x59, 0xd7, 0x7a, 0x7c, 0xd8,
	0x8f, 0xa9, 0xbd, 0x98, 0x96, 0x01, 0xe4, 0x53, 0x38, 0x17, 0xd1, 0x13, 0x67, 0x94, 0x6e, 0x75,
	0xd5, 0xb8, 0xde, 0xb2, 0x49, 0x44, 0x4f, 0x86, 0x68, 0x58, 0x87, 0x70, 0x65, 0xf2, 0x6c, 0xd2,
	0x98, 0x7c, 0x0d, 0xea, 0x6a, 0x3a, 0x69, 0xac, 0xe6, 0xd3, 0x1c, 0xcc, 0x27, 0x8d, 0xed, 0xf9,
	0x8e, 0xfa, 0x22, 0x57, 0x01, 0xbc, 0x43, 0x37, 0x8a, 0x68, 0xe8, 0x30, 0x1f, 0xa7, 0xd2, 0xb2,
	0xeb, 0x0a, 0xb2, 0xe3, 0x5b, 0xbf, 0x00, 0xe4, 0x67, 0x39, 0x8b, 0xb4, 0x8e, 0xa6, 0x61, 0xd7,
	0x53, 0x88, 0x3b, 0xb0, 0x3c, 0x42, 0x7c, 0xea, 0xd1, 0xb3, 0xd4, 0x49, 0x7b, 0x9e, 0x47, 0xd3,
	0x14, 0x3b, 0x98, 0xb7, 0xeb, 0x2c, 0xdd, 0x93, 0x00, 0xeb, 0x9f, 0x2a, 0xb0, 0xa8, 0x51, 0xdf,
	0xe3, 0x51, 0x40, 0x16, 0xa0, 0xc2, 0x7c, 0x24, 0x5b, 0xb7, 0x2b, 0xcc, 0x27, 0x97, 0xa1, 0x9e,
	0xf2, 0x28, 0x70, 0x22, 0xb7, 0x2b, 0x97, 0xb2, 0x6e, 0xcf, 0x0b, 0xc0, 0x03, 0xb7, 0x4b, 0xc9,
	0x79, 0x98, 0x13, 0xeb, 0x42, 0x13, 0x5c, 0x8c, 0xba, 0xad, 0x4a, 0xe4, 0x12, 0xcc, 0x87, 0xfd,
	0x84, 0x79, 0x2c, 0xcd, 0xda, 0x33, 0xb2, 0x4d, 0x5e, 0x16, 0x75, 0x1e, 0xef, 0xc6, 0x3c, 0xa5,
	0x49, 0x7b, 0x56, 0xd6, 0xe5, 0x65, 0xf2, 0x1e, 0xb4, 0x78, 0xc2, 0x02, 0x16, 0x39, 0x8a, 0xec,
	0x1c, 0x22, 0x34, 0x25, 0x70, 0x4f, 0x12, 0x7f, 0x07, 0x1a, 0xbd, 0x38, 0xe4, 0xae, 0xef, 0xf4,
	0x04, 0x8d, 0x1a, 0xa2, 0x80, 0x04, 0xed, 0x0b, 0x2a, 0x1f, 0xc1, 0x52, 0x9c, 0x50, 0x8f, 0x46,
	0x19, 0x4f, 0x1c, 0xd7, 0xf3, 0x78, 0x2f, 0xca, 0xda, 0x80, 0x68, 0x66, 0x51, 0xb1, 0x29, 0xe1,
	0xa2, 0xcb, 0x01, 0x72, 0x8f, 0xf9, 0xed, 0x06, 0x2e, 0x43, 0xb3, 0x00, 0xee, 0x33, 0x9f, 0x5c,
	0x84, 0xf9, 0x03, 0x16, 0x52, 0xa7, 0x97, 0x84, 0xed, 0x79, 0x24, 0x54, 0x13, 0xe5, 0xfd, 0x24,
	0x24, 0x26, 0x54, 0xbb, 0xfe, 0xcd, 0x76, 0x1d, 0xa1, 0xe2, 0xd3, 0x8a, 0xe1, 0x8a, 0xc6, 0xd4,
	0x6d, 0xb5, 0x9c, 0xd1, 0x01, 0x7f, 0xc0, 0x33, 0x76, 0xd0, 0x27, 0xbb, 0xd0, 0x2c, 0x56, 0x3d,
	0x3a, 0xe0, 0x6a, 0x09, 0x3f, 0x79, 0xba, 0x7e, 0x68, 0xa4, 0xec, 0x86, 0x37, 0x28, 0x58, 0xff,
	0x39, 0x07, 0xe7, 0xc7, 0xe3, 0x0d, 0x89, 0x98, 0x31, 0x24, 0x62, 0xe4, 0x5d, 0x68, 0x76, 0x69,
	0xb7, 0x43, 0x13, 0x27, 0x64, 0x5d, 0x96, 0x29, 0x19, 0x6c, 0x48, 0xd8, 0x3d, 0x01, 0x1a, 0xaf,
	0xd3, 0xd5, 0x97, 0xa4, 0xd3, 0xe6, 0x24, 0x9d, 0x26, 0x1f, 0xc0, 0x02, 0xca, 0x21, 0xae, 0x9a,
	0xcf, 0x4f, 0x22, 0x14, 0xac, 0x96, 0xdd, 0x12, 0xd0, 0xed, 0x1c, 0x48, 0xde, 0x87, 0x85, 0xae,
	0xfb, 0xc4, 0x09, 0xd9, 0x01, 0x95, 0xa8, 0x28, 0x63, 0x2d, 0xbb, 0xd9, 0x75, 0x9f, 0xdc, 0x63,
	0x07, 0x14, 0x31, 0xc9, 0x2a, 0x34, 0xbd, 0x5e, 0xa2, 0xc6, 0xcc, 0x7c, 0x14, 0xb3, 0x96, 0x0d,
	0x5e, 0x2f, 0xb1, 0x05, 0x68, 0xc7, 0x57, 0x9a, 0x13, 0x87, 0x6e, 0x9f, 0x45, 0x01, 0xca, 0x18,
	0x6a, 0xce, 0xae, 0x04, 0x90, 0x2d, 0x98, 0x4d, 0x33, 0x37, 0xa0, 0x28, 0x0d, 0x0b, 0x1b, 0x1f,
	0x3f, 0x9d, 0x11, 0x48, 0x78, 0x4f, 0xb4, 0xb1, 0x65, 0x53, 0x72, 0x1d, 0x4c, 0xfc, 0x70, 0x7a,
	0xb1, 0xef, 0x66, 0xd4, 0x77, 0xdc, 0x0c, 0xc5, 0xa8, 0x65, 0x2f, 0x20, 0x7c, 0x5f, 0x82, 0x37,
	0x33, 0xf2, 0x36, 0x34, 0xc4, 0x70, 0x71, 0xfe, 0xcc, 0x57, 0xa2, 0x5c, 0xf7, 0x7a, 0x89, 0xd0,
	0xd8, 0x1d, 0x5f, 0x4c, 0x7a, 0x50, 0x1f, 0xf9, 0xf4, 0x49, 0x2e, 0xc4, 0x39, 0x8a, 0x80, 0x91,
	0xbb, 0x50, 0x8f, 0x79, 0xca, 0x32, 0xc6, 0xa3, 0xb4, 0xdd, 0x5c, 0xad, 0x5e, 0x6f, 0x6c, 0xac,
	0x3d, 0x7d, 0xdc, 0xbb, 0xaa, 0x89, 0x3d, 0x68, 0x4c, 0xda, 0x50, 0x3b, 0xa6, 0x49, 0xca, 0x78,
	0xd4, 0x6e, 0x61, 0x47, 0x79, 0x91, 0x7c, 0x0c, 0xa4, 0xcb, 0x22, 0xd6, 0xed, 0x75, 0x91, 0x77,
	0x34, 0x71, 0xa2, 0x5e, 0xb7, 0xbd, 0x80, 0x48, 0xa6, 0xaa, 0xd9, 0xc5, 0x8a, 0x07, 0xbd, 0xae,
	0xd0, 0x3d, 0x8f, 0x47, 0x59, 0xc2, 0xc3, 0x90, 0x26, 0x62, 0x66, 0x8b, 0x6a, 0xd8, 0x05, 0x70,
	0xc7, 0x27, 0xeb, 0xb0, 0xcc, 0x0f, 0x0e, 0x98, 0xc7, 0xdc, 0xd0, 0x09, 0x12, 0xde, 0x8b, 0xa5,
	0xd6, 0x2c, 0x21, 0x13, 0x96, 0xf2, 0xaa, 0x3b, 0xa2, 0x06, 0x25, 0xfe, 0x2b, 0x20, 0xb1, 0x9b,
	0xa6, 0x4e, 0x76, 0x98, 0xf0, 0x5e, 0x70, 0xe8, 0xd0, 0x27, 0x59, 0xe2, 0xb6, 0x09, 0x2a, 0xd9,
	0x87, 0x13, 0xe6, 0xbb, 0xeb, 0xa6, 0xe9, 0x43, 0x89, 0x7f, 0x5b, 0xa0, 0xdf, 0x7d, 0xcb, 0x36,
	0xe3, 0x21, 0xd8, 0x56, 0x0d, 0x66, 0x91, 0x96, 0xf5, 0xa7, 0x06, 0x98, 0xc3, 0x2d, 0x8a, 0x35,
	0x38, 0x62, 0xb1, 0x12, 0x3c, 0x63, 0xb0, 0x06, 0x47, 0x2c, 0x96, 0x82, 0xa7, 0xc4, 0x53, 0xc3,
	0xaa, 0x14, 0xe2, 0x59, 0xc2, 0x1a, 0x5a, 0xcf, 0xea, 0x98, 0xf5, 0xfc, 0x78, 0x68, 0xa2, 0x92,
	0x9e, 0xd4, 0x0a, 0x7d, 0xf4, 0x48, 0xd3, 0xfa, 0x9b, 0x2a, 0x2c, 0x8f, 0x59, 0x56, 0x61, 0xbf,
	0x7a, 0x85, 0x65, 0x10, 0x9f, 0xc2, 0x40, 0x47, 0xcc, 0x3b, 0xd2, 0x0d, 0x7e, 0x5e, 0x16, 0x2b,
	0x9f, 0x1b, 0x54, 0x69, 0xf1, 0xf3, 0x22, 0x59, 0x81, 0x59, 0x39, 0x54, 0x39, 0x00, 0x59, 0x10,
	0xb4, 0xe2, 0x84, 0xc6, 0x6e, 0x42, 0x7d, 0x54, 0xc4, 0x79, 0xbb, 0x28, 0x93, 0x6b, 0xb0, 0x18,
	0xd2, 0x83, 0x4c, 0xd7, 0x55, 0xa9, 0x87, 0x2d, 0x01, 0x1e, 0x28, 0xeb, 0x1d, 0xa8, 0x0b, 0x43,
	0x2f, 0x0d, 0x44, 0x0d, 0xf5, 0xed, 0x0c, 0x72, 0x2b, 0x76, 0x02, 0x34, 0x3e, 0xf3, 0x3d, 0xf5,
	0x25, 0x86, 0x98, 0x7a, 0x3c, 0x91, 0x4a, 0xdb, 0xb2, 0x65, 0x81, 0x7c, 0x0d, 0x96, 0x90, 0x7c,
	0x4a, 0x33, 0x87, 0x75, 0x85, 0x3e, 0x32, 0x5f, 0x99, 0xf3, 0x05, 0x51, 0xb1, 0x47, 0xb3, 0x1d,
	0x01, 0xde, 0xf1, 0x85, 0xbc, 0xfa, 0xd4, 0xe3, 0x89, 0x2b, 0x38, 0x37, 0xd0, 0xc4, 0xe6, 0x00,
	0xb8, 0xe3, 0x0b, 0x86, 0xa6, 0x85, 0x06, 0x8a, 0x4f, 0x62, 0x41, 0xeb, 0xd0, 0x3d, 0xa6, 0x4e,
	0xd2, 0x8b, 0x1c, 0xf7, 0xc4, 0xed, 0xb7, 0x9b, 0xc8, 0x89, 0x86, 0x00, 0xda, 0xbd, 0x68, 0xf3,
	0xc4, 0xed, 0x97, 0xb7, 0xa1, 0xcc, 0x0d, 0x50, 0xb1, 0xea, 0xda, 0x36, 0xf4, 0xd0, 0x0d, 0xac,
	0x5f, 0x85, 0x4b, 0x77, 0xa8, 0xee, 0xd5, 0x14, 0x76, 0xef, 0xc5, 0x79, 0x1d, 0x62, 0xaf, 0x2b,
	0xec, 0xa2, 0x14, 0xbb, 0x5a, 0x22, 0x8d, 0xa2, 0x75, 0x00, 0x97, 0x27, 0xf6, 0x3f, 0x9d, 0x63,
	0x72, 0x05, 0xea, 0x03, 0x43, 0x9e, 0x0f, 0x21, 0x07, 0x58, 0x27, 0xb0, 0x3c, 0xec, 0xbf, 0xbd,
	0x9a, 0x09, 0x7e, 0xc7, 0x80, 0x95, 0xd1, 0x9e, 0xa7, 0x9b, 0xda, 0xf0, 0xf6, 0x5e, 0x79, 0xee,
	0xed, 0xfd, 0x37, 0x0c, 0x78, 0x67, 0xdb, 0x8d, 0x3c, 0x1a, 0xea, 0x0a, 0x8c, 0x6a, 0x84, 0x42,
	0xf7, 0x6a, 0x78, 0x73, 0x1f, 0x56, 0x4f, 0x1f, 0xc4, 0x54, 0x6c, 0xb2, 0x7e, 0xcd, 0x80, 0xab,
	0xc8, 0xea, 0xfd, 0xc0, 0x53, 0x33, 0x7f, 0xe5, 0xcb, 0xfd, 0x25, 0xbc, 0x7d, 0xda, 0x10, 0xa6,
	0x9b, 0xd0, 0x3f, 0x1b, 0x70, 0x4e, 0x6b, 0xfe, 0x88, 0x67, 0xf4, 0x65, 0x39, 0x7c, 0x64, 0x1b,
	0xe6, 0xd2, 0xac, 0x1f, 0x52, 0xe1, 0xd3, 0x8b, 0x7d, 0xfc, 0xa3, 0xa7, 0xd3, 0x12, 0xfb, 0xc6,
	0x9e, 0x68, 0x63, 0xab, 0xa6, 0xc2, 0xa3, 0x3a, 0xe6, 0x19, 0xd5, 0x3c, 0x2a, 0xc9, 0x9e, 0x96,
	0x80, 0x16, 0x9a, 0x6d, 0x39, 0xb0, 0x32, 0x8e, 0xcc, 0xc8, 0x41, 0x81, 0xc0, 0x8c, 0x4f, 0x53,
	0x4f, 0x6d, 0x19, 0xf8, 0x2d, 0xac, 0x5a, 0xda, 0x8b, 0x63, 0x9e, 0x08, 0xf7, 0x46, 0x78, 0x02,
	0x6a, 0x1f, 0x2b, 0x80, 0x0f, 0x7a, 0x5d, 0xeb, 0xbf, 0x0c, 0x68, 0x97, 0x65, 0x4a, 0x78, 0xf2,
	0x2f, 0x8d, 0x77, 0x9b, 0xc2, 0x75, 0xeb, 0x87, 0x54, 0x29, 0xe6, 0x54, 0xac, 0x93, 0x2d, 0xc9,
	0xe7, 0x30, 0x2b, 0xf6, 0xe6, 0xb4, 0x5d, 0x45, 0xee, 0x5f, 0x3b, 0x1b, 0x09, 0x5b, 0x36, 0xb2,
	0x7e, 0xc7, 0x00, 0x32, 0x24, 0x28, 0xaf, 0x44, 0xdc, 0x45, 0x15, 0x8e, 0x5f, 0x54, 0xc9, 0x53,
	0x59, 0x0d, 0xcb, 0x3b, 0xbe, 0xf5, 0x3f, 0x95, 0x92, 0x77, 0x20, 0xc7, 0xf4, 0x9a, 0xed, 0x9e,
	0x26, 0xe5, 0xd5, 0x67, 0x97, 0xf2, 0x62, 0xb9, 0x67, 0x9e, 0x7f, 0xb9, 0x67, 0x9f, 0x61, 0xb9,
	0xc7, 0xa8, 0xd9, 0xdc, 0x38, 0x35, 0xfb, 0x65, 0xb8, 0x20, 0x8e, 0xa4, 0xdd, 0x38, 0x64, 0xe9,
	0xe1, 0x3d, 0xee, 0xfa, 0x32, 0x58, 0xf1, 0x6a, 0x0c, 0xe1, 0x77, 0x0d, 0x68, 0x8f, 0xef, 0xfd,
	0x75, 0xef, 0x7d, 0xbf, 0x02, 0x17, 0xcb, 0x2e, 0x87, 0xed, 0x46, 0x47, 0xf7, 0x58, 0x9a, 0x4d,
	0xc3, 0x98, 0x9f, 0x82, 0x19, 0x2d, 0x12, 0xf5, 0xde, 0x84, 0xe1, 0xe4, 0x94, 0xd1, 0x6b, 0xc4,
	0x06, 0xd6, 0xbf, 0x1a, 0xc3, 0x1e, 0xd7, 0xa0, 0xfb, 0xe9, 0x38, 0xb3, 0x05, 0x33, 0x21, 0x4b,
	0x33, 0x65, 0xaf, 0xd7, 0xcf, 0x70, 0x5e, 0x54, 0x1d, 0xed, 0x64, 0xb4, 0x6b, 0x63, 0x5b, 0xb2,
	0x07, 0xad, 0x98, 0x26, 0x29, 0x8f, 0xdc, 0xd0, 0x49, 0xdc, 0xe8, 0x08, 0x57, 0x71, 0x7a, 0x62,
	0xcd, 0x9c, 0x88, 0x80, 0x5a, 0xff, 0x66, 0xc0, 0x85, 0x09, 0x98, 0xc2, 0xa4, 0x63, 0x3f, 0xf2,
	0x70, 0x80, 0xdf, 0xc2, 0xa3, 0x57, 0x2e, 0xbf, 0xdc, 0x7c, 0xea, 0x76, 0x51, 0x9e, 0x70, 0x22,
	0xa9, 0x8e, 0x3f, 0x91, 0x08, 0x71, 0xd5, 0x4e, 0xbe, 0xf2, 0xd8, 0x50, 0xef, 0x15, 0x87, 0xde,
	0xfb, 0xd0, 0x48, 0x68, 0xda, 0x0b, 0x33, 0xe9, 0xf8, 0xcf, 0x9e, 0xf9, 0xa0, 0x8d, 0x8d, 0x70,
	0x11, 0x21, 0x29, 0xbe, 0xad, 0x3f, 0x34, 0x86, 0x9d, 0x57, 0x5b, 0x78, 0xed, 0xfe, 0xb4, 0xb2,
	0xb4, 0x02, 0xb3, 0xfa, 0xc1, 0x4d, 0x16, 0xc4, 0x19, 0x05, 0x23, 0x52, 0x5d, 0x71, 0xbc, 0x90,
	0xcb, 0x72, 0x86, 0x33, 0x8a, 0x50, 0xb4, 0xfb, 0x3c, 0xa1, 0xf6, 0x7c, 0xa8, 0xbe, 0xac, 0x1f,
	0x1b, 0x70, 0x65, 0xf2, 0x30, 0x5f, 0xbd, 0xcc, 0xbd, 0xb0, 0x89, 0xfd, 0x7c, 0x69, 0x83, 0xc9,
	0x11, 0xc8, 0x65, 0xa8, 0x87, 0x6e, 0x9a, 0x39, 0xb1, 0x1b, 0x50, 0x25, 0x67, 0xf3, 0x02, 0xb0,
	0xeb, 0x06, 0x54, 0x48, 0x08, 0x56, 0xea, 0x0c, 0x47, 0x74, 0x79, 0xa4, 0xfd, 0x17, 0x03, 0xda,
	0x77, 0x12, 0xb7, 0xa3, 0x87, 0x79, 0x99, 0xf7, 0xd2, 0x1c, 0x07, 0x0b, 0x5a, 0x9d, 0x2c, 0x72,
	0xd2, 0x43, 0x7e, 0x22, 0x45, 0x56, 0x05, 0xcb, 0x3a, 0x59, 0xb4, 0x87, 0xb0, 0xcd, 0x2c, 0xc7,
	0x39, 0x64, 0xbe, 0x4f, 0x23, 0x81, 0x53, 0x2d, 0x70, 0xee, 0x22, 0x6c, 0x33, 0x23, 0xd7, 0xc1,
	0x14, 0x38, 0x3e, 0x4b, 0xe3, 0xd0, 0xed, 0x3b, 0x19, 0xeb, 0x52, 0x25, 0xfd, 0x0b, 0x9d, 0x2c,
	0xba, 0x25, 0xc1, 0x0f, 0x59, 0x97, 0x5a, 0xbf, 0x6b, 0xc0, 0xb9, 0xd1, 0x09, 0xbe, 0x1a, 0x67,
	0xe1, 0x02, 0xd4, 0xf2, 0x78, 0xd3, 0x8c, 0x8a, 0xed, 0x62, 0xb0, 0xc9, 0xfa, 0xa1, 0x01, 0xe7,
	0xc7, 0x0d, 0xea, 0x75, 0x7b, 0x0b, 0xe5, 0x58, 0x77, 0x75, 0x38, 0xd6, 0xfd, 0x7d, 0x03, 0x56,
	0x36, 0xd3, 0xa3, 0x2f, 0x78, 0xa2, 0x88, 0xdd, 0xa5, 0x61, 0xfc, 0x7a, 0x59, 0x29, 0xda, 0xb8,
	0xbd, 0x8c, 0x63, 0xd7, 0x32, 0x3a, 0x52, 0x13, 0x65, 0x9b, 0x3e, 0xb6, 0x7e, 0xcf, 0x80, 0x73,
	0x63, 0x86, 0xfb, 0xba, 0xb7, 0xe3, 0x1f, 0x18, 0xb0, 0xa4, 0x0d, 0xe8, 0x4d, 0xd7, 0x35, 0xe1,
	0x6b, 0xaf, 0x6c, 0x46, 0xe9, 0x09, 0x7d, 0x73, 0x56, 0xdd, 0xfa, 0x33, 0xb1, 0xb4, 0xa3, 0x63,
	0x7a, 0xc3, 0xf5, 0xe7, 0xf7, 0x0d, 0xb8, 0x32, 0x70, 0x11, 0x87, 0x7d, 0xdd, 0xd7, 0xcb, 0xd1,
	0x3f, 0x30, 0xe0, 0xea, 0x29, 0x63, 0x7b, 0xdd, 0x4a, 0xf3, 0x27, 0x06, 0x5c, 0xb5, 0xa9, 0x38,
	0xf0, 0x0e, 0x9f, 0x32, 0x3c, 0xb1, 0x47, 0xbe, 0x5e, 0x1b, 0x54, 0x04, 0x45, 0x67, 0xb5, 0xa0,
	0xa8, 0xf5, 0xf7, 0x06, 0xbc, 0x7d, 0xda, 0x90, 0xa7, 0x63, 0xe9, 0x7d, 0x68, 0x60, 0xe7, 0xd2,
	0x1d, 0x53, 0x1c, 0xfd, 0xf8, 0x8c, 0xc7, 0x28, 0x6c, 0x63, 0x43, 0x5a, 0x7c, 0x93, 0x1b, 0xb0,
	0xc2, 0x52, 0x27, 0xc1, 0xe1, 0x39, 0x6e, 0xcf, 0x67, 0x5c, 0xae, 0x94, 0x94, 0xd9, 0x25, 0x96,
	0xca, 0x91, 0x6f, 0x8a, 0x1a, 0x5c, 0x80, 0x9f, 0x18, 0xd0, 0xde, 0x3b, 0x61, 0x99, 0x77, 0x38,
	0x7c, 0x33, 0xf5, 0xe2, 0x78, 0xff, 0x4a, 0xaf, 0xc9, 0x66, 0x26, 0x5e, 0x7d, 0xff, 0xd8, 0x80,
	0x8b, 0x13, 0xa6, 0xfa, 0xba, 0x0d, 0xcc, 0xf3, 0x45, 0x4d, 0xe8, 0xb0, 0x5f, 0xac, 0xf7, 0xf3,
	0xe2, 0xee, 0xdc, 0x7f, 0x34, 0x0b, 0x57, 0x4f, 0xe9, 0xe7, 0xff, 0x34, 0x0f, 0xc9, 0x06, 0x9c,
	0x0f, 0x12, 0xb7, 0xe3, 0x74, 0x99, 0xe7, 0x94, 0xb7, 0x4e, 0x25, 0x50, 0xa2, 0xf6, 0x3e, 0xf3,
	0xb6, 0x34, 0x6f, 0xf5, 0x33, 0xb8, 0x58, 0x6a, 0x53, 0x72, 0x5b, 0xa5, 0xcd, 0x38, 0x37, 0x68,
	0xa6, 0x79, 0xaf, 0xe4, 0x23, 0x20, 0x87, 0x34, 0x8c, 0x87, 0x7a, 0x92, 0xc1, 0x8f, 0x45, 0x51,
	0xa3, 0x77, 0xf3, 0x29, 0x9c, 0x2b, 0x90, 0x4b, 0x5d, 0xd4, 0xe4, 0xc8, 0x14, 0xbe, 0x4e, 0x7f,
	0x10, 0x1e, 0x9a, 0x7f, 0xf6, 0xf0, 0x90, 0x0d, 0x0b, 0x29, 0x0d, 0xa9, 0x27, 0x4e, 0xa1, 0x32,
	0x4e, 0x54, 0x9f, 0x3e, 0x4e, 0xd4, 0xca, 0x49, 0xc8, 0xc8, 0xe8, 0x68, 0xc4, 0x07, 0xc6, 0x44,
	0x7c, 0xc8, 0x4d, 0xb8, 0x10, 0x71, 0x47, 0x5d, 0x87, 0x39, 0x1d, 0x37, 0x72, 0xfc, 0x9e, 0x8c,
	0xa5, 0xab, 0xcb, 0xa3, 0x95, 0x88, 0xcb, 0x00, 0x3b, 0xdd, 0x72, 0xa3, 0x5b, 0xaa, 0x8e, 0x7c,
	0x1d, 0xce, 0xd3, 0x27, 0x2c, 0x93, 0xd6, 0xa0, 0xd4, 0xaa, 0x89, 0xad, 0x96, 0x45, 0xad, 0x18,
	0x9f, 0xd6, 0xc8, 0xf2, 0xe0, 0xf2, 0x58, 0xbb, 0xaa, 0x1c, 0xb8, 0x5b, 0x30, 0xa7, 0x6c, 0xb3,
	0xf1, 0x0c, 0xb6, 0x59, 0xb5, 0xb5, 0x7e, 0xbb, 0x52, 0x0e, 0x25, 0xe8, 0x3d, 0x7c, 0x73, 0xa8,
	0x87, 0xeb, 0x67, 0x3d, 0xc8, 0xe7, 0xd4, 0xdf, 0x38, 0x65, 0x2a, 0x5d, 0x61, 0xcd, 0x0c, 0x5f,
	0x61, 0xfd, 0xc0, 0x80, 0xcb, 0x63, 0xed, 0xf0, 0x4b, 0x73, 0x99, 0x8b, 0xd9, 0x54, 0x9e, 0xc5,
	0xbc, 0xfe, 0x45, 0xf9, 0xf6, 0x62, 0xb0, 0xba, 0x2f, 0x61, 0xa4, 0xbb, 0xd0, 0x44, 0x2f, 0xc3,
	0xf1, 0x69, 0xe6, 0xb2, 0xf0, 0xec, 0x2b, 0x89, 0xce, 0xc7, 0x2d, 0x6c, 0x64, 0x37, 0xd2, 0x41,
	0xc1, 0xfa, 0x2d, 0xa3, 0x94, 0x00, 0xa3, 0xe1, 0x91, 0x36, 0xd4, 0x72, 0x9f, 0xd6, 0x90, 0x47,
	0x2c, 0x55, 0x24, 0xef, 0x42, 0x53, 0x58, 0x15, 0x9a, 0x38, 0xd2, 0x01, 0x52, 0x47, 0x0c, 0x09,
	0x43, 0x12, 0x98, 0xdf, 0x24, 0x3e, 0xa4, 0x88, 0x08, 0xa7, 0x09, 0x4b, 0xfa, 0x35, 0xf8, 0x4c,
	0xe9, 0x1a, 0xdc, 0xfa, 0x26, 0x9c, 0xdf, 0xe6, 0xbd, 0x38, 0xc4, 0xfb, 0xe7, 0x5b, 0x6e, 0x1f,
	0xe9, 0xe0, 0xac, 0x47, 0x2f, 0xda, 0x0b, 0xd7, 0xab, 0xa2, 0xbb, 0x5e, 0x3f, 0xac, 0x16, 0x47,
	0xac, 0x81, 0x16, 0x90, 0x6f, 0x14, 0x23, 0x31, 0x70, 0x79, 0x3f, 0x3c, 0x23, 0xb7, 0x8a, 0x21,
	0x7f, 0x0e, 0xb3, 0x62, 0x66, 0x53, 0x88, 0x07, 0x1e, 0x4b, 0x64, 0x23, 0x31, 0xf8, 0x8c, 0xc5,
	0xea, 0xce, 0x5f, 0x7c, 0x4e, 0x97, 0x7d, 0xf0, 0x82, 0x83, 0x79, 0xa4, 0x03, 0xe7, 0x3d, 0xe4,
	0xb2, 0x23, 0xaf, 0xee, 0x51, 0x9a, 0x30, 0xd6, 0x35, 0x87, 0xb3, 0x9b, 0x24, 0x4b, 0xe3, 0x97,
	0xc6, 0x5e, 0xf6, 0x0a, 0x38, 0x02, 0xef, 0xb1, 0x34, 0x23, 0x9f, 0xc0, 0x32, 0x4b, 0x05, 0x7d,
	0x47, 0x75, 0x25, 0xd7, 0x4a, 0xa6, 0x02, 0x99, 0x2c, 0xdd, 0x4f, 0xa9, 0x24, 0x87, 0x4d, 0xac,
	0xef, 0x55, 0xc0, 0x1c, 0x66, 0xfe, 0x0b, 0x4b, 0xae, 0x18, 0x93, 0x2a, 0x31, 0x33, 0x2e, 0x55,
	0x62, 0xac, 0x33, 0x3f, 0x08, 0x59, 0xce, 0xe9, 0x21, 0xcb, 0x91, 0xac, 0x84, 0xda, 0x68, 0x56,
	0x82, 0x05, 0xad, 0x62, 0xf3, 0xc7, 0xdd, 0x58, 0x66, 0x4e, 0x34, 0xd4, 0x86, 0x8f, 0xdb, 0xf0,
	0xa7, 0x70, 0xae, 0xc0, 0x51, 0xaa, 0xe5, 0x24, 0x6e, 0x26, 0x37, 0xd2, 0x4a, 0xe1, 0x53, 0xa8,
	0x73, 0xa4, 0xed, 0x66, 0xd4, 0xfa, 0xb2, 0x94, 0x76, 0x28, 0xe4, 0x0c, 0xd3, 0x0c, 0x31, 0xbd,
	0x6f, 0x90, 0xa6, 0x36, 0x2f, 0x01, 0x3b, 0x98, 0x83, 0xa8, 0x34, 0xb5, 0x70, 0xd9, 0xe6, 0x25,
	0x60, 0xc7, 0xb7, 0xbe, 0x6f, 0xc0, 0xc5, 0xdb, 0x4f, 0xe2, 0x84, 0xa6, 0xa9, 0x1e, 0x60, 0x64,
	0x47, 0x6f, 0xc0, 0xc9, 0x4a, 0xcf, 0x40, 0x93, 0x05, 0xeb, 0x0e, 0x5c, 0x9a, 0x34, 0xda, 0xe9,
	0xee, 0x9b, 0x7f, 0xbd, 0x52, 0xba, 0x46, 0x14, 0x24, 0xee, 0xa7, 0xc1, 0xd3, 0x12, 0xfe, 0xb6,
	0xa1, 0x7e, 0x90, 0xf0, 0xae, 0x4c, 0x9d, 0x94, 0x86, 0xf7, 0xda, 0xd9, 0x92, 0x69, 0xec, 0x79,
	0xd1, 0x10, 0x13, 0x2c, 0xbf, 0x01, 0xb5, 0x8c, 0x4b, 0x12, 0xd5, 0xa9, 0x48, 0xcc, 0x65, 0x1c,
	0x09, 0xdc, 0x81, 0x7a, 0xc8, 0x8e, 0xb4, 0xc3, 0xcc, 0x99, 0x52, 0x7a, 0xc4, 0x14, 0x65, 0x4a,
	0x4f, 0xa8, 0xbe, 0xac, 0xdf, 0xac, 0x94, 0xb6, 0x2d, 0x81, 0xa1, 0x36, 0xd8, 0xff, 0x5f, 0x7c,
	0x38, 0x2a, 0x69, 0x14, 0xd2, 0x1e, 0xb5, 0x3d, 0x9a, 0x7d, 0xa9, 0x94, 0xed, 0x8b, 0x6e, 0x95,
	0xaa, 0x43, 0x56, 0x49, 0xe5, 0x33, 0xcd, 0x14, 0xf9, 0x4c, 0xd6, 0x5f, 0x97, 0x77, 0x5b, 0x31,
	0x9c, 0xf4, 0x6c, 0x5c, 0xbf, 0x0d, 0x50, 0x70, 0x7d, 0x8a, 0x9d, 0x08, 0x79, 0x56, 0xcf, 0xd9,
	0x9e, 0x3e, 0x37, 0xdf, 0xad, 0xb4, 0x34, 0x81, 0x2f, 0x58, 0x9a, 0x6d, 0xf5, 0xba, 0x2f, 0x32,
	0x2e, 0x48, 0x60, 0xa6, 0xc7, 0x7c, 0xe9, 0x36, 0xb4, 0x6c, 0xfc, 0xb6, 0x6e, 0x95, 0xbc, 0xe3,
	0x41, 0xa7, 0xd3, 0xa9, 0xfd, 0xdf, 0x8a, 0x03, 0xfe, 0x28, 0x99, 0x57, 0x28, 0xf5, 0x9b, 0x30,
	0xaf, 0xb8, 0x3f, 0x85, 0xe7, 0x8c, 0x34, 0x6a, 0x92, 0xfd, 0xa9, 0xf5, 0xed, 0x0a, 0xb4, 0xc4,
	0x17, 0x26, 0xd6, 0x6d, 0xf3, 0xe8, 0x40, 0x18, 0xd8, 0x22, 0xf9, 0x4e, 0xe6, 0x94, 0xd4, 0x98,
	0xca, 0xba, 0xbb, 0x0a, 0x20, 0xab, 0xb4, 0x4d, 0xb3, 0x8e, 0x10, 0xcc, 0x41, 0xbf, 0x0c, 0xb2,
	0x80, 0xc9, 0xd9, 0x4a, 0x78, 0x11, 0xa0, 0x65, 0x67, 0xcf, 0x14, 0xd9, 0xd9, 0x03, 0x6a, 0x85,
	0x23, 0xd2, 0x52, 0xd4, 0xf2, 0x1c, 0x41, 0x99, 0xc6, 0x38, 0xa7, 0xa7, 0x31, 0x5e, 0x07, 0xd3,
	0xa7, 0x07, 0xae, 0xf0, 0x5f, 0x98, 0xc7, 0x23, 0xec, 0x4a, 0xe6, 0x9d, 0x2f, 0x28, 0xf8, 0x8e,
	0xc7, 0x23, 0xd1, 0xe1, 0x4d, 0xb8, 0x90, 0x63, 0x4a, 0x86, 0x1c, 0xbb, 0x99, 0x9b, 0x68, 0x89,
	0xe3, 0x2b, 0xaa, 0x5a, 0xf0, 0x66, 0x13, 0x2b, 0xf7, 0x93, 0xd0, 0xfa, 0xae, 0x01, 0x0b, 0xb7,
	0x06, 0x59, 0x84, 0xc2, 0x5f, 0x1c, 0x49, 0x36, 0x34, 0xc6, 0x24, 0x1b, 0x7e, 0x08, 0x8b, 0x1a,
	0x92, 0xc6, 0xa0, 0x85, 0x01, 0x18, 0xb9, 0xf4, 0x01, 0x68, 0x10, 0x8d, 0x55, 0x5a, 0x1f, 0x63,
	0xf9, 0x65, 0xed, 0xc3, 0x92, 0x8a, 0x87, 0x6c, 0x86, 0xa1, 0x58, 0xaa, 0x69, 0xb4, 0x44, 0x4b,
	0x14, 0xae, 0x94, 0x12, 0x85, 0xad, 0x04, 0xda, 0x42, 0x00, 0x1e, 0xd0, 0x13, 0x94, 0x81, 0xfd,
	0x28, 0xe4, 0xde, 0x91, 0x12, 0xe2, 0x51, 0xcb, 0xb5, 0x0a, 0xcd, 0x88, 0x9e, 0x0c, 0xd2, 0x33,
	0xe5, 0x1c, 0x21, 0x52, 0xad, 0x77, 0x7c, 0xe1, 0xa9, 0xe8, 0x18, 0xb9, 0xc3, 0xde, 0x18, 0xa0,
	0xa4, 0xd6, 0x3a, 0xcc, 0x8b, 0x3e, 0x85, 0xbb, 0x84, 0x0e, 0xad, 0x1b, 0xe4, 0x7d, 0x64, 0x6e,
	0x20, 0x20, 0x5e, 0x71, 0xcb, 0x28, 0x3e, 0xad, 0x1e, 0xb4, 0x1e, 0x71, 0xe6, 0xd1, 0xaf, 0xdc,
	0x63, 0xfa, 0x90, 0x3e, 0xc9, 0xc6, 0x34, 0xb2, 0xa0, 0xd5, 0x65, 0xbe, 0x93, 0xd1, 0x27, 0x99,
	0x53, 0xdc, 0xb5, 0xd6, 0xed, 0x46, 0x97, 0xf9, 0xa2, 0x05, 0x3a, 0x92, 0xeb, 0xb0, 0x4c, 0x23,
	0x8f, 0xf7, 0x12, 0x94, 0xba, 0x02, 0x53, 0x0e, 0x70, 0xa9, 0xa8, 0xca, 0xf1, 0xad, 0x7f, 0xac,
	0x00, 0x19, 0x66, 0xf9, 0x74, 0x71, 0xa7, 0x7d, 0x58, 0x41, 0xbf, 0x58, 0x72, 0xc3, 0xe3, 0xd1,
	0x81, 0xa3, 0x5d, 0x04, 0xbf, 0x3f, 0x41, 0x5b, 0x4b, 0x0a, 0x69, 0x63, 0x52, 0x6c, 0x51, 0xc4,
	0x89, 0x3c, 0x28, 0x09, 0x5b, 0x31, 0x89, 0xc6, 0xc6, 0x07, 0x13, 0x28, 0x96, 0x25, 0x5a, 0x97,
	0x49, 0xa4, 0x77, 0x0f, 0x16, 0x8f, 0x05, 0x7f, 0x9d, 0x13, 0xe1, 0x87, 0x0a, 0xce, 0xb4, 0x67,
	0x4e, 0x1d, 0x61, 0x69, 0x35, 0xec, 0xd6, 0x71, 0x69, 0x71, 0x34, 0x59, 0x9b, 0x2d, 0xcb, 0xda,
	0xcf, 0xc0, 0xf2, 0x1d, 0x9a, 0xa1, 0x77, 0xcf, 0xa2, 0x00, 0xe7, 0x34, 0x85, 0x10, 0x5b, 0xff,
	0x5d, 0x81, 0x95, 0xd1, 0xf6, 0xd3, 0xad, 0x88, 0x05, 0xad, 0x5c, 0x34, 0x4b, 0x72, 0xa2, 0x6c,
	0x1c, 0xb2, 0x63, 0x44, 0xe1, 0xab, 0x63, 0x14, 0x7e, 0x0d, 0x96, 0x34, 0x24, 0x1a, 0xf9, 0x4e,
	0x96, 0xaa, 0xbd, 0x59, 0x5b, 0x9c, 0xdb, 0x91, 0xff, 0x30, 0x1d, 0x9f, 0xd9, 0x3c, 0x3b, 0x36,
	0xb3, 0xf9, 0x11, 0x34, 0x84, 0x02, 0x4a, 0xb4, 0x54, 0x9d, 0xa2, 0x6e, 0x4e, 0x58, 0x86, 0x71,
	0xcc, 0x58, 0xbf, 0xc7, 0xbd, 0x23, 0x59, 0x82, 0x30, 0xff, 0x4c, 0x2f, 0x7d, 0x06, 0xf5, 0xa2,
	0xe2, 0x34, 0x1b, 0xaf, 0xce, 0x97, 0x95, 0xe2, 0x7c, 0x89, 0x39, 0xc0, 0xcf, 0xbe, 0x68, 0xa5,
	0xbe, 0x2a, 0x23, 0xfb, 0x89, 0xb6, 0x47, 0x56, 0x87, 0x23, 0xc0, 0x9b, 0xb0, 0xb2, 0xf7, 0x7c,
	0xab, 0x6d, 0x7d, 0x85, 0x02, 0xa7, 0xed, 0x7e, 0x5b, 0xc1, 0x8b, 0xb1, 0x9a, 0xff, 0x21, 0x45,
	0x71, 0x88, 0xf2, 0x74, 0xa2, 0xf8, 0x10, 0x16, 0xe8, 0x93, 0x98, 0x26, 0x99, 0x93, 0x70, 0xde,
	0x75, 0x3a, 0x81, 0x72, 0x04, 0xd6, 0x27, 0xaf, 0xf6, 0x48, 0x7f, 0xeb, 0x5b, 0x81, 0xdd, 0x94,
	0x54, 0x6c, 0xce, 0xbb, 0x5b, 0x81, 0xa0, 0x9a, 0x70, 0x7e, 0xc4, 0x68, 0x41, 0xb5, 0xfa, 0x6c,
	0x54, 0x25, 0x15, 0x45, 0x55, 0xe3, 0xc4, 0x4c, 0x89, 0x13, 0x97, 0x7e, 0x11, 0x2a, 0x5b, 0x81,
	0x30, 0xad, 0x32, 0x7e, 0x2a, 0x3a, 0x50, 0x8f, 0x69, 0x92, 0x50, 0x09, 0xd7, 0x52, 0x51, 0x85,
	0xcf, 0x6e, 0xc4, 0xf6, 0xb6, 0x06, 0x4b, 0xea, 0x49, 0x8f, 0x86, 0x2d, 0xc5, 0x63, 0x51, 0x55,
	0xe4, 0xb8, 0xd6, 0xf7, 0x0c, 0x58, 0xde, 0x8b, 0x29, 0x3e, 0x2e, 0x71, 0xbb, 0xf4, 0x5b, 0x3c,
	0x92, 0x71, 0x9c, 0x95, 0x3c, 0xa1, 0xd0, 0x50, 0x67, 0x6c, 0x8c, 0xf9, 0x5e, 0x83, 0x45, 0x19,
	0x2d, 0x48, 0x1c, 0x25, 0x4a, 0xea, 0xb9, 0x5d, 0x0b, 0x23, 0x05, 0x89, 0x0a, 0x86, 0xa1, 0x21,
	0x48, 0x9d, 0x88, 0x52, 0xdf, 0x49, 0x0f, 0x5d, 0x95, 0x53, 0x33, 0x6f, 0x37, 0x58, 0xfa, 0x80,
	0x52, 0x7f, 0x4f, 0x80, 0xc8, 0x39, 0x98, 0xcb, 0xdc, 0x4e, 0x7e, 0xa0, 0x6c, 0xd9, 0xb3, 0x99,
	0xdb, 0x91, 0x67, 0x50, 0x01, 0xc6, 0x4d, 0x5e, 0x6a, 0x71, 0x2d, 0x73, 0x3b, 0x62, 0x77, 0xb7,
	0xbe, 0x5d, 0x2d, 0xf9, 0xff, 0x38, 0x4e, 0xa2, 0x92, 0xde, 0x54, 0xf2, 0x96, 0xf8, 0x16, 0x30,
	0xcd, 0x47, 0xc0, 0x6f, 0x31, 0x1f, 0x9f, 0xa6, 0x5e, 0xfe, 0x84, 0x4f, 0x16, 0xd0, 0x4d, 0x8a,
	0x42, 0x2a, 0xfd, 0x1d, 0xe5, 0x0f, 0xd4, 0x11, 0x22, 0x3c, 0x1d, 0xf2, 0x0e, 0x34, 0x64, 0xb5,
	0xc7, 0x43, 0x9e, 0xbf, 0xe3, 0x93, 0x2d, 0xb6, 0x05, 0x44, 0x20, 0x24, 0xd4, 0xf5, 0xfb, 0x8e,
	0xc7, 0x8f, 0x8b, 0x77, 0x7c, 0x80, 0xa0, 0x6d, 0x01, 0x11, 0x08, 0xbf, 0xc4, 0x23, 0xea, 0x74,
	0x7a, 0x59, 0xc6, 0xa3, 0xfc, 0x15, 0x9f, 0x00, 0x6d, 0x21, 0x44, 0x70, 0x0a, 0x11, 0x24, 0x99,
	0x4e, 0xa0, 0xfc, 0x27, 0x6c, 0x65, 0x0b, 0xd8, 0x56, 0x20, 0xce, 0xde, 0x92, 0x48, 0xa0, 0x5e,
	0x6c, 0xcc, 0x21, 0x81, 0x40, 0x2c, 0x47, 0xc6, 0xba, 0x54, 0xbe, 0x6a, 0x93, 0x73, 0x90, 0x6f,
	0x35, 0x5a, 0x02, 0x8c, 0x0f, 0xdb, 0x70, 0x1e, 0x8f, 0x60, 0x29, 0x95, 0x6b, 0x2c, 0xe3, 0xe9,
	0x18, 0xe1, 0x6c, 0x9c, 0x9e, 0xe6, 0x34, 0x2a, 0x13, 0xf6, 0x62, 0x3a, 0x00, 0xe2, 0x8d, 0x63,
	0x50, 0x78, 0x4d, 0xcf, 0x90, 0xd0, 0xbe, 0x06, 0x4b, 0x5e, 0x2f, 0x49, 0x68, 0x94, 0x0d, 0xbd,
	0xa2, 0x6d, 0xd9, 0x8b, 0xaa, 0xa2, 0xb8, 0xef, 0xfb, 0xd1, 0xc0, 0x59, 0x78, 0xc6, 0xf7, 0x0a,
	0x0f, 0x81, 0x94, 0x2e, 0x18, 0x05, 0x0b, 0xa6, 0x38, 0x9b, 0xe1, 0xfc, 0xcd, 0xb4, 0x0c, 0x48,
	0xc9, 0x57, 0x00, 0xf8, 0x58, 0x2a, 0xbf, 0x99, 0x15, 0xd4, 0x3e, 0x3b, 0x93, 0x2d, 0x40, 0x4b,
	0x80, 0x6f, 0xaa, 0x04, 0xa5, 0xdb, 0x51, 0x96, 0xf4, 0xed, 0x3a, 0xcd, 0xcb, 0x97, 0x7c, 0x58,
	0x28, 0x57, 0x8a, 0xbd, 0xe3, 0x88, 0xf6, 0x73, 0xaf, 0xec, 0x88, 0xf6, 0xc9, 0xe7, 0x30, 0x7b,
	0xec, 0x86, 0x3d, 0x7a, 0xf6, 0x23, 0x0e, 0xce, 0x42, 0x36, 0xfa, 0xe9, 0xca, 0x67, 0x86, 0xf5,
	0x47, 0x06, 0xac, 0x6c, 0xc6, 0xb1, 0x76, 0x91, 0xac, 0x7c, 0x53, 0x94, 0x6b, 0xbc, 0x78, 0xd6,
	0x94, 0x0b, 0x24, 0x08, 0x0f, 0x10, 0xbb, 0x45, 0xa4, 0x53, 0xbb, 0xa5, 0xb8, 0x31, 0x61, 0x04,
	0x0f, 0x78, 0xb6, 0x19, 0xc7, 0x09, 0x3f, 0xa6, 0x7b, 0x2c, 0x0f, 0xb8, 0xe3, 0x50, 0x54, 0xb0,
	0x13, 0x65, 0xe9, 0x0a, 0x80, 0x3d, 0xa0, 0xbf, 0x00, 0x30, 0x68, 0x65, 0xbe, 0x65, 0xfd, 0xb9,
	0x01, 0xed, 0x49, 0x64, 0xc6, 0x78, 0xd2, 0x57, 0x01, 0x32, 0x37, 0x09, 0x68, 0x86, 0x6f, 0x5d,
	0xd5, 0xb9, 0x55, 0x42, 0xf6, 0xd9, 0xd3, 0xf6, 0x46, 0x61, 0x82, 0x42, 0xd6, 0xd1, 0xef, 0xa0,
	0x6b, 0x21, 0xeb, 0xe0, 0xb8, 0xf4, 0x08, 0xd9, 0xec, 0xc4, 0x08, 0xd9, 0x5c, 0x29, 0x6f, 0xe3,
	0x3b, 0x06, 0x5c, 0x18, 0x37, 0xf6, 0x69, 0x94, 0xe5, 0xc5, 0xf3, 0xfb, 0xf6, 0x78, 0x86, 0x4e,
	0xeb, 0x04, 0xec, 0x0f, 0x67, 0x2e, 0xa3, 0x7f, 0x3d, 0xe5, 0x6d, 0xb5, 0x5a, 0xc0, 0x4a, 0xb1,
	0x80, 0xd6, 0x5f, 0x8e, 0xa4, 0x24, 0x0f, 0xe8, 0x4e, 0xed, 0x93, 0x96, 0x83, 0xbb, 0x95, 0x29,
	0x82, 0xbb, 0xd5, 0x49, 0xc1, 0x5d, 0x4c, 0xda, 0x64, 0x47, 0xe5, 0x30, 0x35, 0x46, 0xb9, 0x30,
	0x44, 0xbd, 0xf6, 0xc7, 0x46, 0x69, 0xab, 0x42, 0xd9, 0xb9, 0x52, 0x7a, 0x00, 0x22, 0x40, 0xfb,
	0x91, 0x4f, 0x0f, 0x58, 0x44, 0x7d, 0xf3, 0x2d, 0x72, 0xb1, 0x14, 0xe2, 0x13, 0xb5, 0xb7, 0xd1,
	0xf3, 0x30, 0x8d, 0x31, 0x55, 0x36, 0xba, 0x0f, 0x66, 0x85, 0xbc, 0x03, 0x97, 0x86, 0xaa, 0xb4,
	0x27, 0x9b, 0xe6, 0xbf, 0xd7, 0xc8, 0x6a, 0xe9, 0x4a, 0x74, 0x08, 0x61, 0xc3, 0xfc, 0x49, 0x6d,
	0xed, 0xaf, 0x66, 0x4a, 0xe4, 0x07, 0x2f, 0x78, 0x89, 0x05, 0x6f, 0x8f, 0xad, 0xd0, 0x87, 0xbd,
	0x5a, 0x7a, 0x06, 0x3e, 0xc0, 0xb9, 0x23, 0x59, 0x66, 0x1a, 0x13, 0x31, 0x14, 0xd0, 0xac, 0x90,
	0x77, 0xe1, 0xea, 0xf8, 0x27, 0xc4, 0xf4, 0xf1, 0x5d, 0x1a, 0xc6, 0x9f, 0x9a, 0x55, 0xf2, 0x21,
	0xbc, 0x77, 0x1a, 0xca, 0x17, 0x2e, 0x0b, 0xa9, 0xff, 0xa9, 0x39, 0x33, 0x71, 0xcc, 0xdb, 0xbc,
	0x2b, 0xbd, 0x24, 0x73, 0x96, 0xbc, 0x53, 0xe2, 0x89, 0x4e, 0x4c, 0x68, 0x80, 0x39, 0xf7, 0xb4,
	0x01, 0x6d, 0x98, 0xb5, 0xb3, 0x0d, 0x68, 0xc3, 0x9c, 0x27, 0xd7, 0xc0, 0x1a, 0x8b, 0x78, 0x57,
	0x5e, 0xc0, 0x29, 0x26, 0xd4, 0xc9, 0x75, 0x78, 0xff, 0x14, 0xbc, 0xc1, 0xf0, 0x81, 0x7c, 0x00,
	0xef, 0x9e, 0x82, 0xa9, 0x26, 0xd1, 0x98, 0xd8, 0xf1, 0xae, 0x9b, 0x64, 0xcc, 0x0d, 0x15, 0x5e,
	0x93, 0x5c, 0x2d, 0x45, 0xda, 0x06, 0x78, 0x8f, 0x78, 0x46, 0xcd, 0xd6, 0xc4, 0xe5, 0x53, 0x0f,
	0x98, 0xcc, 0x85, 0xb5, 0x5e, 0x29, 0xe7, 0x39, 0x7f, 0x91, 0x3a, 0xd4, 0x30, 0x07, 0xeb, 0xb2,
	0x53, 0xee, 0x39, 0xc7, 0x90, 0xff, 0x3f, 0x30, 0x8d, 0x09, 0xd5, 0x72, 0x96, 0x66, 0x65, 0xed,
	0x08, 0x9a, 0xfa, 0x5b, 0x06, 0xa1, 0x25, 0x7a, 0x59, 0xef, 0xe8, 0x3c, 0x10, 0xbd, 0xea, 0x8b,
	0x84, 0xd1, 0xc8, 0x37, 0x0d, 0xb2, 0x0c, 0x8b, 0x3a, 0x7c, 0x33, 0x0c, 0xcd, 0xca, 0x30, 0xf0,
	0x96, 0xdb, 0x37, 0xab, 0x6b, 0x7f, 0x57, 0xbe, 0x38, 0x1e, 0x5c, 0xd8, 0x0d, 0x0b, 0x5c, 0x51,
	0xa1, 0xf7, 0x3f, 0x24, 0x70, 0x05, 0x8e, 0x94, 0x12, 0xd3, 0x98, 0x88, 0xf0, 0x80, 0x27, 0x5d,
	0x57, 0x0c, 0xea, 0x7d, 0x58, 0x1d, 0x8b, 0xb0, 0xcd, 0x79, 0x4c, 0xc5, 0x69, 0xf8, 0x98, 0x9a,
	0xd5, 0xe1, 0xb5, 0x2a, 0xb0, 0x76, 0x69, 0x72, 0x40, 0xbd, 0xcc, 0x9c, 0x59, 0x8b, 0xcb, 0xf9,
	0xe9, 0x2a, 0xb0, 0x3e, 0xd4, 0x30, 0x07, 0xeb, 0x53, 0xb8, 0x34, 0x12, 0x0c, 0x17, 0x18, 0x3f,
	0x17, 0x51, 0xd3, 0x20, 0x97, 0x4b, 0x21, 0xdf, 0xbc, 0xee, 0xbe, 0x1b, 0xf5, 0xcd, 0xca, 0xda,
	0xa6, 0x16, 0x03, 0xc5, 0xbe, 0x16, 0xa1, 0x21, 0xa7, 0x85, 0x20, 0xf3, 0x2d, 0x62, 0x42, 0xf3,
	0x96, 0x0a, 0x2f, 0x22, 0xc4, 0x20, 0x2d, 0xed, 0x3c, 0x6d, 0x56, 0xb6, 0xbe, 0x84, 0xb6, 0xc7,
	0xbb, 0xeb, 0x7d, 0xd6, 0xe7, 0x3d, 0x61, 0xe5, 0xbb, 0xdc, 0xa7, 0xa1, 0xfc, 0xa7, 0xcb, 0xb7,
	0x6e, 0x04, 0x3c, 0x74, 0xa3, 0x60, 0xfd, 0xe6, 0x46, 0x96, 0xad, 0x7b, 0xbc, 0x7b, 0x03, 0xc1,
	0x1e, 0x0f, 0x6f, 0xb8, 0x71, 0x7c, 0x63, 0xf4, 0x8f, 0x31, 0x9d, 0x39, 0x44, 0xf8, 0xfa, 0xff,
	0x06, 0x00, 0x00, 0xff, 0xff, 0x2c, 0x44, 0xa4, 0xe9, 0x4e, 0x46, 0x00, 0x00,
}
