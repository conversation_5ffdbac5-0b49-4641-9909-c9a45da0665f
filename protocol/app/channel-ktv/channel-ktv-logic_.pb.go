// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-ktv-logic_.proto

package channel_ktv // import "golang.52tt.com/protocol/app/channel-ktv"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type HandClapUserMicStatus int32

const (
	HandClapUserMicStatus_HandClapUserMicStatus_UNKNOW HandClapUserMicStatus = 0
	HandClapUserMicStatus_HandClapUserMicStatus_ON_MIC HandClapUserMicStatus = 1
	HandClapUserMicStatus_HandClapUserMicStatus_MISS   HandClapUserMicStatus = 2
)

var HandClapUserMicStatus_name = map[int32]string{
	0: "HandClapUserMicStatus_UNKNOW",
	1: "HandClapUserMicStatus_ON_MIC",
	2: "HandClapUserMicStatus_MISS",
}
var HandClapUserMicStatus_value = map[string]int32{
	"HandClapUserMicStatus_UNKNOW": 0,
	"HandClapUserMicStatus_ON_MIC": 1,
	"HandClapUserMicStatus_MISS":   2,
}

func (x HandClapUserMicStatus) String() string {
	return proto.EnumName(HandClapUserMicStatus_name, int32(x))
}
func (HandClapUserMicStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{0}
}

type ChannelKTVUpdate_ChannelKTVUpdateStatus int32

const (
	ChannelKTVUpdate_NOORDER  ChannelKTVUpdate_ChannelKTVUpdateStatus = 0
	ChannelKTVUpdate_DOWNLOAD ChannelKTVUpdate_ChannelKTVUpdateStatus = 1
	ChannelKTVUpdate_PLAYING  ChannelKTVUpdate_ChannelKTVUpdateStatus = 2
	ChannelKTVUpdate_END      ChannelKTVUpdate_ChannelKTVUpdateStatus = 3
	ChannelKTVUpdate_RESULT   ChannelKTVUpdate_ChannelKTVUpdateStatus = 4
)

var ChannelKTVUpdate_ChannelKTVUpdateStatus_name = map[int32]string{
	0: "NOORDER",
	1: "DOWNLOAD",
	2: "PLAYING",
	3: "END",
	4: "RESULT",
}
var ChannelKTVUpdate_ChannelKTVUpdateStatus_value = map[string]int32{
	"NOORDER":  0,
	"DOWNLOAD": 1,
	"PLAYING":  2,
	"END":      3,
	"RESULT":   4,
}

func (x ChannelKTVUpdate_ChannelKTVUpdateStatus) String() string {
	return proto.EnumName(ChannelKTVUpdate_ChannelKTVUpdateStatus_name, int32(x))
}
func (ChannelKTVUpdate_ChannelKTVUpdateStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{49, 0}
}

type ChannelKTVUpdate_PlayingUpdateType int32

const (
	ChannelKTVUpdate_NORMAL           ChannelKTVUpdate_PlayingUpdateType = 0
	ChannelKTVUpdate_JOIN             ChannelKTVUpdate_PlayingUpdateType = 1
	ChannelKTVUpdate_QUIT             ChannelKTVUpdate_PlayingUpdateType = 2
	ChannelKTVUpdate_LOST             ChannelKTVUpdate_PlayingUpdateType = 3
	ChannelKTVUpdate_CHANGE_MAIN      ChannelKTVUpdate_PlayingUpdateType = 4
	ChannelKTVUpdate_CHANGE_SONG      ChannelKTVUpdate_PlayingUpdateType = 5
	ChannelKTVUpdate_CHANGE_RANK      ChannelKTVUpdate_PlayingUpdateType = 6
	ChannelKTVUpdate_PERFECT_ENDING   ChannelKTVUpdate_PlayingUpdateType = 7
	ChannelKTVUpdate_FIRST_PERFECT    ChannelKTVUpdate_PlayingUpdateType = 8
	ChannelKTVUpdate_PERFECT_CHORUS   ChannelKTVUpdate_PlayingUpdateType = 9
	ChannelKTVUpdate_COOL_FIT         ChannelKTVUpdate_PlayingUpdateType = 10
	ChannelKTVUpdate_FLY_THE_AUDIENCE ChannelKTVUpdate_PlayingUpdateType = 11
	ChannelKTVUpdate_ACE              ChannelKTVUpdate_PlayingUpdateType = 12
)

var ChannelKTVUpdate_PlayingUpdateType_name = map[int32]string{
	0:  "NORMAL",
	1:  "JOIN",
	2:  "QUIT",
	3:  "LOST",
	4:  "CHANGE_MAIN",
	5:  "CHANGE_SONG",
	6:  "CHANGE_RANK",
	7:  "PERFECT_ENDING",
	8:  "FIRST_PERFECT",
	9:  "PERFECT_CHORUS",
	10: "COOL_FIT",
	11: "FLY_THE_AUDIENCE",
	12: "ACE",
}
var ChannelKTVUpdate_PlayingUpdateType_value = map[string]int32{
	"NORMAL":           0,
	"JOIN":             1,
	"QUIT":             2,
	"LOST":             3,
	"CHANGE_MAIN":      4,
	"CHANGE_SONG":      5,
	"CHANGE_RANK":      6,
	"PERFECT_ENDING":   7,
	"FIRST_PERFECT":    8,
	"PERFECT_CHORUS":   9,
	"COOL_FIT":         10,
	"FLY_THE_AUDIENCE": 11,
	"ACE":              12,
}

func (x ChannelKTVUpdate_PlayingUpdateType) String() string {
	return proto.EnumName(ChannelKTVUpdate_PlayingUpdateType_name, int32(x))
}
func (ChannelKTVUpdate_PlayingUpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{49, 1}
}

type PlayListUpdate_UpdateType int32

const (
	PlayListUpdate_ADD PlayListUpdate_UpdateType = 0
	PlayListUpdate_UP  PlayListUpdate_UpdateType = 1
	PlayListUpdate_DEL PlayListUpdate_UpdateType = 2
)

var PlayListUpdate_UpdateType_name = map[int32]string{
	0: "ADD",
	1: "UP",
	2: "DEL",
}
var PlayListUpdate_UpdateType_value = map[string]int32{
	"ADD": 0,
	"UP":  1,
	"DEL": 2,
}

func (x PlayListUpdate_UpdateType) String() string {
	return proto.EnumName(PlayListUpdate_UpdateType_name, int32(x))
}
func (PlayListUpdate_UpdateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{51, 0}
}

type KTVCopyright struct {
	SongLyric            int32    `protobuf:"varint,1,opt,name=song_lyric,json=songLyric,proto3" json:"song_lyric,omitempty"`
	Recordingval         int32    `protobuf:"varint,2,opt,name=recordingval,proto3" json:"recordingval,omitempty"`
	Channel              int32    `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVCopyright) Reset()         { *m = KTVCopyright{} }
func (m *KTVCopyright) String() string { return proto.CompactTextString(m) }
func (*KTVCopyright) ProtoMessage()    {}
func (*KTVCopyright) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{0}
}
func (m *KTVCopyright) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVCopyright.Unmarshal(m, b)
}
func (m *KTVCopyright) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVCopyright.Marshal(b, m, deterministic)
}
func (dst *KTVCopyright) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVCopyright.Merge(dst, src)
}
func (m *KTVCopyright) XXX_Size() int {
	return xxx_messageInfo_KTVCopyright.Size(m)
}
func (m *KTVCopyright) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVCopyright.DiscardUnknown(m)
}

var xxx_messageInfo_KTVCopyright proto.InternalMessageInfo

func (m *KTVCopyright) GetSongLyric() int32 {
	if m != nil {
		return m.SongLyric
	}
	return 0
}

func (m *KTVCopyright) GetRecordingval() int32 {
	if m != nil {
		return m.Recordingval
	}
	return 0
}

func (m *KTVCopyright) GetChannel() int32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

type KTVSong struct {
	SongId               string        `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName             string        `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SingerName           string        `protobuf:"bytes,3,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	AlbumName            string        `protobuf:"bytes,4,opt,name=album_name,json=albumName,proto3" json:"album_name,omitempty"`
	AlbumImg             string        `protobuf:"bytes,5,opt,name=album_img,json=albumImg,proto3" json:"album_img,omitempty"`
	Duration             int32         `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	IsAdd                bool          `protobuf:"varint,7,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty"`
	Copyright            *KTVCopyright `protobuf:"bytes,8,opt,name=copyright,proto3" json:"copyright,omitempty"`
	SegmentBegin         uint32        `protobuf:"varint,9,opt,name=segment_begin,json=segmentBegin,proto3" json:"segment_begin,omitempty"`
	SegmentEnd           uint32        `protobuf:"varint,10,opt,name=segment_end,json=segmentEnd,proto3" json:"segment_end,omitempty"`
	IsClimax             bool          `protobuf:"varint,11,opt,name=is_climax,json=isClimax,proto3" json:"is_climax,omitempty"`
	VendorId             int32         `protobuf:"varint,12,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	PitchAbility         int32         `protobuf:"varint,13,opt,name=pitch_ability,json=pitchAbility,proto3" json:"pitch_ability,omitempty"`
	MetaId               string        `protobuf:"bytes,99,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *KTVSong) Reset()         { *m = KTVSong{} }
func (m *KTVSong) String() string { return proto.CompactTextString(m) }
func (*KTVSong) ProtoMessage()    {}
func (*KTVSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{1}
}
func (m *KTVSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVSong.Unmarshal(m, b)
}
func (m *KTVSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVSong.Marshal(b, m, deterministic)
}
func (dst *KTVSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVSong.Merge(dst, src)
}
func (m *KTVSong) XXX_Size() int {
	return xxx_messageInfo_KTVSong.Size(m)
}
func (m *KTVSong) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVSong.DiscardUnknown(m)
}

var xxx_messageInfo_KTVSong proto.InternalMessageInfo

func (m *KTVSong) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *KTVSong) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *KTVSong) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *KTVSong) GetAlbumName() string {
	if m != nil {
		return m.AlbumName
	}
	return ""
}

func (m *KTVSong) GetAlbumImg() string {
	if m != nil {
		return m.AlbumImg
	}
	return ""
}

func (m *KTVSong) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *KTVSong) GetIsAdd() bool {
	if m != nil {
		return m.IsAdd
	}
	return false
}

func (m *KTVSong) GetCopyright() *KTVCopyright {
	if m != nil {
		return m.Copyright
	}
	return nil
}

func (m *KTVSong) GetSegmentBegin() uint32 {
	if m != nil {
		return m.SegmentBegin
	}
	return 0
}

func (m *KTVSong) GetSegmentEnd() uint32 {
	if m != nil {
		return m.SegmentEnd
	}
	return 0
}

func (m *KTVSong) GetIsClimax() bool {
	if m != nil {
		return m.IsClimax
	}
	return false
}

func (m *KTVSong) GetVendorId() int32 {
	if m != nil {
		return m.VendorId
	}
	return 0
}

func (m *KTVSong) GetPitchAbility() int32 {
	if m != nil {
		return m.PitchAbility
	}
	return 0
}

func (m *KTVSong) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

type KTVUser struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Username             string   `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  int32    `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVUser) Reset()         { *m = KTVUser{} }
func (m *KTVUser) String() string { return proto.CompactTextString(m) }
func (*KTVUser) ProtoMessage()    {}
func (*KTVUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{2}
}
func (m *KTVUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVUser.Unmarshal(m, b)
}
func (m *KTVUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVUser.Marshal(b, m, deterministic)
}
func (dst *KTVUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVUser.Merge(dst, src)
}
func (m *KTVUser) XXX_Size() int {
	return xxx_messageInfo_KTVUser.Size(m)
}
func (m *KTVUser) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVUser.DiscardUnknown(m)
}

var xxx_messageInfo_KTVUser proto.InternalMessageInfo

func (m *KTVUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KTVUser) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *KTVUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *KTVUser) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type KTVRecordSinger struct {
	User                 *KTVUser `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	ScoreTutti           uint32   `protobuf:"varint,2,opt,name=score_tutti,json=scoreTutti,proto3" json:"score_tutti,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVRecordSinger) Reset()         { *m = KTVRecordSinger{} }
func (m *KTVRecordSinger) String() string { return proto.CompactTextString(m) }
func (*KTVRecordSinger) ProtoMessage()    {}
func (*KTVRecordSinger) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{3}
}
func (m *KTVRecordSinger) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVRecordSinger.Unmarshal(m, b)
}
func (m *KTVRecordSinger) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVRecordSinger.Marshal(b, m, deterministic)
}
func (dst *KTVRecordSinger) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVRecordSinger.Merge(dst, src)
}
func (m *KTVRecordSinger) XXX_Size() int {
	return xxx_messageInfo_KTVRecordSinger.Size(m)
}
func (m *KTVRecordSinger) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVRecordSinger.DiscardUnknown(m)
}

var xxx_messageInfo_KTVRecordSinger proto.InternalMessageInfo

func (m *KTVRecordSinger) GetUser() *KTVUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *KTVRecordSinger) GetScoreTutti() uint32 {
	if m != nil {
		return m.ScoreTutti
	}
	return 0
}

type KTVRecord struct {
	Song                 *KTVSong           `protobuf:"bytes,1,opt,name=song,proto3" json:"song,omitempty"`
	ScoreSolo            uint32             `protobuf:"varint,2,opt,name=score_solo,json=scoreSolo,proto3" json:"score_solo,omitempty"`
	ScoreTotalTutti      uint32             `protobuf:"varint,4,opt,name=score_total_tutti,json=scoreTotalTutti,proto3" json:"score_total_tutti,omitempty"`
	SingerList           []*KTVRecordSinger `protobuf:"bytes,3,rep,name=singer_list,json=singerList,proto3" json:"singer_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *KTVRecord) Reset()         { *m = KTVRecord{} }
func (m *KTVRecord) String() string { return proto.CompactTextString(m) }
func (*KTVRecord) ProtoMessage()    {}
func (*KTVRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{4}
}
func (m *KTVRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVRecord.Unmarshal(m, b)
}
func (m *KTVRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVRecord.Marshal(b, m, deterministic)
}
func (dst *KTVRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVRecord.Merge(dst, src)
}
func (m *KTVRecord) XXX_Size() int {
	return xxx_messageInfo_KTVRecord.Size(m)
}
func (m *KTVRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVRecord.DiscardUnknown(m)
}

var xxx_messageInfo_KTVRecord proto.InternalMessageInfo

func (m *KTVRecord) GetSong() *KTVSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *KTVRecord) GetScoreSolo() uint32 {
	if m != nil {
		return m.ScoreSolo
	}
	return 0
}

func (m *KTVRecord) GetScoreTotalTutti() uint32 {
	if m != nil {
		return m.ScoreTotalTutti
	}
	return 0
}

func (m *KTVRecord) GetSingerList() []*KTVRecordSinger {
	if m != nil {
		return m.SingerList
	}
	return nil
}

type KTVOrder struct {
	IndexId              int64     `protobuf:"varint,1,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	Song                 *KTVSong  `protobuf:"bytes,2,opt,name=song,proto3" json:"song,omitempty"`
	User                 *KTVUser  `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Glory                *KTVGlory `protobuf:"bytes,4,opt,name=glory,proto3" json:"glory,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *KTVOrder) Reset()         { *m = KTVOrder{} }
func (m *KTVOrder) String() string { return proto.CompactTextString(m) }
func (*KTVOrder) ProtoMessage()    {}
func (*KTVOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{5}
}
func (m *KTVOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVOrder.Unmarshal(m, b)
}
func (m *KTVOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVOrder.Marshal(b, m, deterministic)
}
func (dst *KTVOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVOrder.Merge(dst, src)
}
func (m *KTVOrder) XXX_Size() int {
	return xxx_messageInfo_KTVOrder.Size(m)
}
func (m *KTVOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVOrder.DiscardUnknown(m)
}

var xxx_messageInfo_KTVOrder proto.InternalMessageInfo

func (m *KTVOrder) GetIndexId() int64 {
	if m != nil {
		return m.IndexId
	}
	return 0
}

func (m *KTVOrder) GetSong() *KTVSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *KTVOrder) GetUser() *KTVUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *KTVOrder) GetGlory() *KTVGlory {
	if m != nil {
		return m.Glory
	}
	return nil
}

type KTVGlory struct {
	GloryName            string   `protobuf:"bytes,1,opt,name=glory_name,json=gloryName,proto3" json:"glory_name,omitempty"`
	GloryImg             string   `protobuf:"bytes,2,opt,name=glory_img,json=gloryImg,proto3" json:"glory_img,omitempty"`
	GloryColor           string   `protobuf:"bytes,3,opt,name=glory_color,json=gloryColor,proto3" json:"glory_color,omitempty"`
	GloryRank            uint32   `protobuf:"varint,4,opt,name=glory_rank,json=gloryRank,proto3" json:"glory_rank,omitempty"`
	PlayColor            string   `protobuf:"bytes,5,opt,name=play_color,json=playColor,proto3" json:"play_color,omitempty"`
	BannerImg            string   `protobuf:"bytes,6,opt,name=banner_img,json=bannerImg,proto3" json:"banner_img,omitempty"`
	GloryBgImg           string   `protobuf:"bytes,7,opt,name=glory_bg_img,json=gloryBgImg,proto3" json:"glory_bg_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVGlory) Reset()         { *m = KTVGlory{} }
func (m *KTVGlory) String() string { return proto.CompactTextString(m) }
func (*KTVGlory) ProtoMessage()    {}
func (*KTVGlory) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{6}
}
func (m *KTVGlory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVGlory.Unmarshal(m, b)
}
func (m *KTVGlory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVGlory.Marshal(b, m, deterministic)
}
func (dst *KTVGlory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVGlory.Merge(dst, src)
}
func (m *KTVGlory) XXX_Size() int {
	return xxx_messageInfo_KTVGlory.Size(m)
}
func (m *KTVGlory) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVGlory.DiscardUnknown(m)
}

var xxx_messageInfo_KTVGlory proto.InternalMessageInfo

func (m *KTVGlory) GetGloryName() string {
	if m != nil {
		return m.GloryName
	}
	return ""
}

func (m *KTVGlory) GetGloryImg() string {
	if m != nil {
		return m.GloryImg
	}
	return ""
}

func (m *KTVGlory) GetGloryColor() string {
	if m != nil {
		return m.GloryColor
	}
	return ""
}

func (m *KTVGlory) GetGloryRank() uint32 {
	if m != nil {
		return m.GloryRank
	}
	return 0
}

func (m *KTVGlory) GetPlayColor() string {
	if m != nil {
		return m.PlayColor
	}
	return ""
}

func (m *KTVGlory) GetBannerImg() string {
	if m != nil {
		return m.BannerImg
	}
	return ""
}

func (m *KTVGlory) GetGloryBgImg() string {
	if m != nil {
		return m.GloryBgImg
	}
	return ""
}

// 歌单类型
type KTVSongListType struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	CoverUrl             string   `protobuf:"bytes,4,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	Position             uint32   `protobuf:"varint,5,opt,name=position,proto3" json:"position,omitempty"`
	Description          string   `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVSongListType) Reset()         { *m = KTVSongListType{} }
func (m *KTVSongListType) String() string { return proto.CompactTextString(m) }
func (*KTVSongListType) ProtoMessage()    {}
func (*KTVSongListType) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{7}
}
func (m *KTVSongListType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVSongListType.Unmarshal(m, b)
}
func (m *KTVSongListType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVSongListType.Marshal(b, m, deterministic)
}
func (dst *KTVSongListType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVSongListType.Merge(dst, src)
}
func (m *KTVSongListType) XXX_Size() int {
	return xxx_messageInfo_KTVSongListType.Size(m)
}
func (m *KTVSongListType) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVSongListType.DiscardUnknown(m)
}

var xxx_messageInfo_KTVSongListType proto.InternalMessageInfo

func (m *KTVSongListType) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *KTVSongListType) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *KTVSongListType) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *KTVSongListType) GetCoverUrl() string {
	if m != nil {
		return m.CoverUrl
	}
	return ""
}

func (m *KTVSongListType) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

func (m *KTVSongListType) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

type GetChannelKTVSongListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Page                 uint32       `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32       `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelKTVSongListReq) Reset()         { *m = GetChannelKTVSongListReq{} }
func (m *GetChannelKTVSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVSongListReq) ProtoMessage()    {}
func (*GetChannelKTVSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{8}
}
func (m *GetChannelKTVSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVSongListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVSongListReq.Merge(dst, src)
}
func (m *GetChannelKTVSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVSongListReq.Size(m)
}
func (m *GetChannelKTVSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVSongListReq proto.InternalMessageInfo

func (m *GetChannelKTVSongListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelKTVSongListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelKTVSongListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetChannelKTVSongListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelKTVSongListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SongList             []*KTVSong    `protobuf:"bytes,2,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelKTVSongListResp) Reset()         { *m = GetChannelKTVSongListResp{} }
func (m *GetChannelKTVSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVSongListResp) ProtoMessage()    {}
func (*GetChannelKTVSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{9}
}
func (m *GetChannelKTVSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVSongListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVSongListResp.Merge(dst, src)
}
func (m *GetChannelKTVSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVSongListResp.Size(m)
}
func (m *GetChannelKTVSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVSongListResp proto.InternalMessageInfo

func (m *GetChannelKTVSongListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelKTVSongListResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

type GetChannelKTVHistoryListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Page                 uint32       `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32       `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelKTVHistoryListReq) Reset()         { *m = GetChannelKTVHistoryListReq{} }
func (m *GetChannelKTVHistoryListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVHistoryListReq) ProtoMessage()    {}
func (*GetChannelKTVHistoryListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{10}
}
func (m *GetChannelKTVHistoryListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVHistoryListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVHistoryListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVHistoryListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVHistoryListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVHistoryListReq.Merge(dst, src)
}
func (m *GetChannelKTVHistoryListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVHistoryListReq.Size(m)
}
func (m *GetChannelKTVHistoryListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVHistoryListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVHistoryListReq proto.InternalMessageInfo

func (m *GetChannelKTVHistoryListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelKTVHistoryListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelKTVHistoryListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetChannelKTVHistoryListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelKTVHistoryListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RecordList           []*KTVRecord  `protobuf:"bytes,2,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelKTVHistoryListResp) Reset()         { *m = GetChannelKTVHistoryListResp{} }
func (m *GetChannelKTVHistoryListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVHistoryListResp) ProtoMessage()    {}
func (*GetChannelKTVHistoryListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{11}
}
func (m *GetChannelKTVHistoryListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVHistoryListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVHistoryListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVHistoryListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVHistoryListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVHistoryListResp.Merge(dst, src)
}
func (m *GetChannelKTVHistoryListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVHistoryListResp.Size(m)
}
func (m *GetChannelKTVHistoryListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVHistoryListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVHistoryListResp proto.InternalMessageInfo

func (m *GetChannelKTVHistoryListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelKTVHistoryListResp) GetRecordList() []*KTVRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

type GetChannelKTVRecommendListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Page                 uint32       `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32       `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongListTypeId       int64        `protobuf:"varint,5,opt,name=song_list_type_id,json=songListTypeId,proto3" json:"song_list_type_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelKTVRecommendListReq) Reset()         { *m = GetChannelKTVRecommendListReq{} }
func (m *GetChannelKTVRecommendListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVRecommendListReq) ProtoMessage()    {}
func (*GetChannelKTVRecommendListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{12}
}
func (m *GetChannelKTVRecommendListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVRecommendListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVRecommendListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVRecommendListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVRecommendListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVRecommendListReq.Merge(dst, src)
}
func (m *GetChannelKTVRecommendListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVRecommendListReq.Size(m)
}
func (m *GetChannelKTVRecommendListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVRecommendListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVRecommendListReq proto.InternalMessageInfo

func (m *GetChannelKTVRecommendListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelKTVRecommendListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelKTVRecommendListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetChannelKTVRecommendListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelKTVRecommendListReq) GetSongListTypeId() int64 {
	if m != nil {
		return m.SongListTypeId
	}
	return 0
}

type GetChannelKTVRecommendListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SongList             []*KTVSong    `protobuf:"bytes,2,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelKTVRecommendListResp) Reset()         { *m = GetChannelKTVRecommendListResp{} }
func (m *GetChannelKTVRecommendListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVRecommendListResp) ProtoMessage()    {}
func (*GetChannelKTVRecommendListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{13}
}
func (m *GetChannelKTVRecommendListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVRecommendListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVRecommendListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVRecommendListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVRecommendListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVRecommendListResp.Merge(dst, src)
}
func (m *GetChannelKTVRecommendListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVRecommendListResp.Size(m)
}
func (m *GetChannelKTVRecommendListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVRecommendListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVRecommendListResp proto.InternalMessageInfo

func (m *GetChannelKTVRecommendListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelKTVRecommendListResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

type GetChannelKTVPlayListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Page                 uint32       `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32       `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelKTVPlayListReq) Reset()         { *m = GetChannelKTVPlayListReq{} }
func (m *GetChannelKTVPlayListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVPlayListReq) ProtoMessage()    {}
func (*GetChannelKTVPlayListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{14}
}
func (m *GetChannelKTVPlayListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVPlayListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVPlayListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVPlayListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVPlayListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVPlayListReq.Merge(dst, src)
}
func (m *GetChannelKTVPlayListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVPlayListReq.Size(m)
}
func (m *GetChannelKTVPlayListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVPlayListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVPlayListReq proto.InternalMessageInfo

func (m *GetChannelKTVPlayListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelKTVPlayListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetChannelKTVPlayListReq) GetSize() uint32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetChannelKTVPlayListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelKTVPlayListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OrderList            []*KTVOrder   `protobuf:"bytes,2,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelKTVPlayListResp) Reset()         { *m = GetChannelKTVPlayListResp{} }
func (m *GetChannelKTVPlayListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVPlayListResp) ProtoMessage()    {}
func (*GetChannelKTVPlayListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{15}
}
func (m *GetChannelKTVPlayListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVPlayListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVPlayListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVPlayListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVPlayListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVPlayListResp.Merge(dst, src)
}
func (m *GetChannelKTVPlayListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVPlayListResp.Size(m)
}
func (m *GetChannelKTVPlayListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVPlayListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVPlayListResp proto.InternalMessageInfo

func (m *GetChannelKTVPlayListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelKTVPlayListResp) GetOrderList() []*KTVOrder {
	if m != nil {
		return m.OrderList
	}
	return nil
}

type GetChannelKTVGuessLikeSongListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelKTVGuessLikeSongListReq) Reset()         { *m = GetChannelKTVGuessLikeSongListReq{} }
func (m *GetChannelKTVGuessLikeSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVGuessLikeSongListReq) ProtoMessage()    {}
func (*GetChannelKTVGuessLikeSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{16}
}
func (m *GetChannelKTVGuessLikeSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.Unmarshal(m, b)
}
func (m *GetChannelKTVGuessLikeSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVGuessLikeSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.Merge(dst, src)
}
func (m *GetChannelKTVGuessLikeSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.Size(m)
}
func (m *GetChannelKTVGuessLikeSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVGuessLikeSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVGuessLikeSongListReq proto.InternalMessageInfo

func (m *GetChannelKTVGuessLikeSongListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelKTVGuessLikeSongListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelKTVGuessLikeSongListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SongList             []*KTVSong    `protobuf:"bytes,2,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelKTVGuessLikeSongListResp) Reset()         { *m = GetChannelKTVGuessLikeSongListResp{} }
func (m *GetChannelKTVGuessLikeSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVGuessLikeSongListResp) ProtoMessage()    {}
func (*GetChannelKTVGuessLikeSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{17}
}
func (m *GetChannelKTVGuessLikeSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.Unmarshal(m, b)
}
func (m *GetChannelKTVGuessLikeSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVGuessLikeSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.Merge(dst, src)
}
func (m *GetChannelKTVGuessLikeSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.Size(m)
}
func (m *GetChannelKTVGuessLikeSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVGuessLikeSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVGuessLikeSongListResp proto.InternalMessageInfo

func (m *GetChannelKTVGuessLikeSongListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelKTVGuessLikeSongListResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

type AddChannelKTVSongToPlayListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Song                 *KTVSong     `protobuf:"bytes,2,opt,name=song,proto3" json:"song,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ShareToken           string       `protobuf:"bytes,4,opt,name=share_token,json=shareToken,proto3" json:"share_token,omitempty"`
	SongListId           uint32       `protobuf:"varint,5,opt,name=song_list_id,json=songListId,proto3" json:"song_list_id,omitempty"`
	ShareTokenList       []string     `protobuf:"bytes,6,rep,name=share_token_list,json=shareTokenList,proto3" json:"share_token_list,omitempty"`
	IsClimax             bool         `protobuf:"varint,7,opt,name=is_climax,json=isClimax,proto3" json:"is_climax,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddChannelKTVSongToPlayListReq) Reset()         { *m = AddChannelKTVSongToPlayListReq{} }
func (m *AddChannelKTVSongToPlayListReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelKTVSongToPlayListReq) ProtoMessage()    {}
func (*AddChannelKTVSongToPlayListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{18}
}
func (m *AddChannelKTVSongToPlayListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelKTVSongToPlayListReq.Unmarshal(m, b)
}
func (m *AddChannelKTVSongToPlayListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelKTVSongToPlayListReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelKTVSongToPlayListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelKTVSongToPlayListReq.Merge(dst, src)
}
func (m *AddChannelKTVSongToPlayListReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelKTVSongToPlayListReq.Size(m)
}
func (m *AddChannelKTVSongToPlayListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelKTVSongToPlayListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelKTVSongToPlayListReq proto.InternalMessageInfo

func (m *AddChannelKTVSongToPlayListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AddChannelKTVSongToPlayListReq) GetSong() *KTVSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *AddChannelKTVSongToPlayListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddChannelKTVSongToPlayListReq) GetShareToken() string {
	if m != nil {
		return m.ShareToken
	}
	return ""
}

func (m *AddChannelKTVSongToPlayListReq) GetSongListId() uint32 {
	if m != nil {
		return m.SongListId
	}
	return 0
}

func (m *AddChannelKTVSongToPlayListReq) GetShareTokenList() []string {
	if m != nil {
		return m.ShareTokenList
	}
	return nil
}

func (m *AddChannelKTVSongToPlayListReq) GetIsClimax() bool {
	if m != nil {
		return m.IsClimax
	}
	return false
}

type AddChannelKTVSongToPlayListResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	Song                 *KTVOrder         `protobuf:"bytes,3,opt,name=song,proto3" json:"song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddChannelKTVSongToPlayListResp) Reset()         { *m = AddChannelKTVSongToPlayListResp{} }
func (m *AddChannelKTVSongToPlayListResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelKTVSongToPlayListResp) ProtoMessage()    {}
func (*AddChannelKTVSongToPlayListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{19}
}
func (m *AddChannelKTVSongToPlayListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelKTVSongToPlayListResp.Unmarshal(m, b)
}
func (m *AddChannelKTVSongToPlayListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelKTVSongToPlayListResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelKTVSongToPlayListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelKTVSongToPlayListResp.Merge(dst, src)
}
func (m *AddChannelKTVSongToPlayListResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelKTVSongToPlayListResp.Size(m)
}
func (m *AddChannelKTVSongToPlayListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelKTVSongToPlayListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelKTVSongToPlayListResp proto.InternalMessageInfo

func (m *AddChannelKTVSongToPlayListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *AddChannelKTVSongToPlayListResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *AddChannelKTVSongToPlayListResp) GetSong() *KTVOrder {
	if m != nil {
		return m.Song
	}
	return nil
}

type MoveUpChannelKTVSongReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	IndexId              int64        `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MoveUpChannelKTVSongReq) Reset()         { *m = MoveUpChannelKTVSongReq{} }
func (m *MoveUpChannelKTVSongReq) String() string { return proto.CompactTextString(m) }
func (*MoveUpChannelKTVSongReq) ProtoMessage()    {}
func (*MoveUpChannelKTVSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{20}
}
func (m *MoveUpChannelKTVSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveUpChannelKTVSongReq.Unmarshal(m, b)
}
func (m *MoveUpChannelKTVSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveUpChannelKTVSongReq.Marshal(b, m, deterministic)
}
func (dst *MoveUpChannelKTVSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveUpChannelKTVSongReq.Merge(dst, src)
}
func (m *MoveUpChannelKTVSongReq) XXX_Size() int {
	return xxx_messageInfo_MoveUpChannelKTVSongReq.Size(m)
}
func (m *MoveUpChannelKTVSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveUpChannelKTVSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_MoveUpChannelKTVSongReq proto.InternalMessageInfo

func (m *MoveUpChannelKTVSongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MoveUpChannelKTVSongReq) GetIndexId() int64 {
	if m != nil {
		return m.IndexId
	}
	return 0
}

func (m *MoveUpChannelKTVSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type MoveUpChannelKTVSongResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MoveUpChannelKTVSongResp) Reset()         { *m = MoveUpChannelKTVSongResp{} }
func (m *MoveUpChannelKTVSongResp) String() string { return proto.CompactTextString(m) }
func (*MoveUpChannelKTVSongResp) ProtoMessage()    {}
func (*MoveUpChannelKTVSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{21}
}
func (m *MoveUpChannelKTVSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MoveUpChannelKTVSongResp.Unmarshal(m, b)
}
func (m *MoveUpChannelKTVSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MoveUpChannelKTVSongResp.Marshal(b, m, deterministic)
}
func (dst *MoveUpChannelKTVSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MoveUpChannelKTVSongResp.Merge(dst, src)
}
func (m *MoveUpChannelKTVSongResp) XXX_Size() int {
	return xxx_messageInfo_MoveUpChannelKTVSongResp.Size(m)
}
func (m *MoveUpChannelKTVSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MoveUpChannelKTVSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_MoveUpChannelKTVSongResp proto.InternalMessageInfo

func (m *MoveUpChannelKTVSongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MoveUpChannelKTVSongResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type RemoveChannelKTVSongReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	IndexId              int64        `protobuf:"varint,2,opt,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RemoveChannelKTVSongReq) Reset()         { *m = RemoveChannelKTVSongReq{} }
func (m *RemoveChannelKTVSongReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelKTVSongReq) ProtoMessage()    {}
func (*RemoveChannelKTVSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{22}
}
func (m *RemoveChannelKTVSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveChannelKTVSongReq.Unmarshal(m, b)
}
func (m *RemoveChannelKTVSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveChannelKTVSongReq.Marshal(b, m, deterministic)
}
func (dst *RemoveChannelKTVSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveChannelKTVSongReq.Merge(dst, src)
}
func (m *RemoveChannelKTVSongReq) XXX_Size() int {
	return xxx_messageInfo_RemoveChannelKTVSongReq.Size(m)
}
func (m *RemoveChannelKTVSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveChannelKTVSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveChannelKTVSongReq proto.InternalMessageInfo

func (m *RemoveChannelKTVSongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RemoveChannelKTVSongReq) GetIndexId() int64 {
	if m != nil {
		return m.IndexId
	}
	return 0
}

func (m *RemoveChannelKTVSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type RemoveChannelKTVSongResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RemoveChannelKTVSongResp) Reset()         { *m = RemoveChannelKTVSongResp{} }
func (m *RemoveChannelKTVSongResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelKTVSongResp) ProtoMessage()    {}
func (*RemoveChannelKTVSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{23}
}
func (m *RemoveChannelKTVSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveChannelKTVSongResp.Unmarshal(m, b)
}
func (m *RemoveChannelKTVSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveChannelKTVSongResp.Marshal(b, m, deterministic)
}
func (dst *RemoveChannelKTVSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveChannelKTVSongResp.Merge(dst, src)
}
func (m *RemoveChannelKTVSongResp) XXX_Size() int {
	return xxx_messageInfo_RemoveChannelKTVSongResp.Size(m)
}
func (m *RemoveChannelKTVSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveChannelKTVSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveChannelKTVSongResp proto.InternalMessageInfo

func (m *RemoveChannelKTVSongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *RemoveChannelKTVSongResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type BeginChannelKTVSingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SongId               string       `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BeginChannelKTVSingReq) Reset()         { *m = BeginChannelKTVSingReq{} }
func (m *BeginChannelKTVSingReq) String() string { return proto.CompactTextString(m) }
func (*BeginChannelKTVSingReq) ProtoMessage()    {}
func (*BeginChannelKTVSingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{24}
}
func (m *BeginChannelKTVSingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeginChannelKTVSingReq.Unmarshal(m, b)
}
func (m *BeginChannelKTVSingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeginChannelKTVSingReq.Marshal(b, m, deterministic)
}
func (dst *BeginChannelKTVSingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeginChannelKTVSingReq.Merge(dst, src)
}
func (m *BeginChannelKTVSingReq) XXX_Size() int {
	return xxx_messageInfo_BeginChannelKTVSingReq.Size(m)
}
func (m *BeginChannelKTVSingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BeginChannelKTVSingReq.DiscardUnknown(m)
}

var xxx_messageInfo_BeginChannelKTVSingReq proto.InternalMessageInfo

func (m *BeginChannelKTVSingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BeginChannelKTVSingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *BeginChannelKTVSingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type BeginChannelKTVSingResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BeginChannelKTVSingResp) Reset()         { *m = BeginChannelKTVSingResp{} }
func (m *BeginChannelKTVSingResp) String() string { return proto.CompactTextString(m) }
func (*BeginChannelKTVSingResp) ProtoMessage()    {}
func (*BeginChannelKTVSingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{25}
}
func (m *BeginChannelKTVSingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeginChannelKTVSingResp.Unmarshal(m, b)
}
func (m *BeginChannelKTVSingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeginChannelKTVSingResp.Marshal(b, m, deterministic)
}
func (dst *BeginChannelKTVSingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeginChannelKTVSingResp.Merge(dst, src)
}
func (m *BeginChannelKTVSingResp) XXX_Size() int {
	return xxx_messageInfo_BeginChannelKTVSingResp.Size(m)
}
func (m *BeginChannelKTVSingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BeginChannelKTVSingResp.DiscardUnknown(m)
}

var xxx_messageInfo_BeginChannelKTVSingResp proto.InternalMessageInfo

func (m *BeginChannelKTVSingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BeginChannelKTVSingResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetChannelKTVInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelKTVInfoReq) Reset()         { *m = GetChannelKTVInfoReq{} }
func (m *GetChannelKTVInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVInfoReq) ProtoMessage()    {}
func (*GetChannelKTVInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{26}
}
func (m *GetChannelKTVInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVInfoReq.Unmarshal(m, b)
}
func (m *GetChannelKTVInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVInfoReq.Merge(dst, src)
}
func (m *GetChannelKTVInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVInfoReq.Size(m)
}
func (m *GetChannelKTVInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVInfoReq proto.InternalMessageInfo

func (m *GetChannelKTVInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelKTVInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelKTVInfoResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	InteractionShowAll   bool              `protobuf:"varint,3,opt,name=interaction_show_all,json=interactionShowAll,proto3" json:"interaction_show_all,omitempty"`
	UserRemainLightNum   uint32            `protobuf:"varint,4,opt,name=user_remain_light_num,json=userRemainLightNum,proto3" json:"user_remain_light_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChannelKTVInfoResp) Reset()         { *m = GetChannelKTVInfoResp{} }
func (m *GetChannelKTVInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVInfoResp) ProtoMessage()    {}
func (*GetChannelKTVInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{27}
}
func (m *GetChannelKTVInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVInfoResp.Unmarshal(m, b)
}
func (m *GetChannelKTVInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVInfoResp.Merge(dst, src)
}
func (m *GetChannelKTVInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVInfoResp.Size(m)
}
func (m *GetChannelKTVInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVInfoResp proto.InternalMessageInfo

func (m *GetChannelKTVInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelKTVInfoResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetChannelKTVInfoResp) GetInteractionShowAll() bool {
	if m != nil {
		return m.InteractionShowAll
	}
	return false
}

func (m *GetChannelKTVInfoResp) GetUserRemainLightNum() uint32 {
	if m != nil {
		return m.UserRemainLightNum
	}
	return 0
}

type JoinChannelKTVSingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	IsSilently           bool         `protobuf:"varint,4,opt,name=is_silently,json=isSilently,proto3" json:"is_silently,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *JoinChannelKTVSingReq) Reset()         { *m = JoinChannelKTVSingReq{} }
func (m *JoinChannelKTVSingReq) String() string { return proto.CompactTextString(m) }
func (*JoinChannelKTVSingReq) ProtoMessage()    {}
func (*JoinChannelKTVSingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{28}
}
func (m *JoinChannelKTVSingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelKTVSingReq.Unmarshal(m, b)
}
func (m *JoinChannelKTVSingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelKTVSingReq.Marshal(b, m, deterministic)
}
func (dst *JoinChannelKTVSingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelKTVSingReq.Merge(dst, src)
}
func (m *JoinChannelKTVSingReq) XXX_Size() int {
	return xxx_messageInfo_JoinChannelKTVSingReq.Size(m)
}
func (m *JoinChannelKTVSingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelKTVSingReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelKTVSingReq proto.InternalMessageInfo

func (m *JoinChannelKTVSingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinChannelKTVSingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinChannelKTVSingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *JoinChannelKTVSingReq) GetIsSilently() bool {
	if m != nil {
		return m.IsSilently
	}
	return false
}

type JoinChannelKTVSingResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *JoinChannelKTVSingResp) Reset()         { *m = JoinChannelKTVSingResp{} }
func (m *JoinChannelKTVSingResp) String() string { return proto.CompactTextString(m) }
func (*JoinChannelKTVSingResp) ProtoMessage()    {}
func (*JoinChannelKTVSingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{29}
}
func (m *JoinChannelKTVSingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelKTVSingResp.Unmarshal(m, b)
}
func (m *JoinChannelKTVSingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelKTVSingResp.Marshal(b, m, deterministic)
}
func (dst *JoinChannelKTVSingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelKTVSingResp.Merge(dst, src)
}
func (m *JoinChannelKTVSingResp) XXX_Size() int {
	return xxx_messageInfo_JoinChannelKTVSingResp.Size(m)
}
func (m *JoinChannelKTVSingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelKTVSingResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelKTVSingResp proto.InternalMessageInfo

func (m *JoinChannelKTVSingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *JoinChannelKTVSingResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type QuitChannelKTVSingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QuitChannelKTVSingReq) Reset()         { *m = QuitChannelKTVSingReq{} }
func (m *QuitChannelKTVSingReq) String() string { return proto.CompactTextString(m) }
func (*QuitChannelKTVSingReq) ProtoMessage()    {}
func (*QuitChannelKTVSingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{30}
}
func (m *QuitChannelKTVSingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelKTVSingReq.Unmarshal(m, b)
}
func (m *QuitChannelKTVSingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelKTVSingReq.Marshal(b, m, deterministic)
}
func (dst *QuitChannelKTVSingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelKTVSingReq.Merge(dst, src)
}
func (m *QuitChannelKTVSingReq) XXX_Size() int {
	return xxx_messageInfo_QuitChannelKTVSingReq.Size(m)
}
func (m *QuitChannelKTVSingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelKTVSingReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelKTVSingReq proto.InternalMessageInfo

func (m *QuitChannelKTVSingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *QuitChannelKTVSingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QuitChannelKTVSingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type QuitChannelKTVSingResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *QuitChannelKTVSingResp) Reset()         { *m = QuitChannelKTVSingResp{} }
func (m *QuitChannelKTVSingResp) String() string { return proto.CompactTextString(m) }
func (*QuitChannelKTVSingResp) ProtoMessage()    {}
func (*QuitChannelKTVSingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{31}
}
func (m *QuitChannelKTVSingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelKTVSingResp.Unmarshal(m, b)
}
func (m *QuitChannelKTVSingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelKTVSingResp.Marshal(b, m, deterministic)
}
func (dst *QuitChannelKTVSingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelKTVSingResp.Merge(dst, src)
}
func (m *QuitChannelKTVSingResp) XXX_Size() int {
	return xxx_messageInfo_QuitChannelKTVSingResp.Size(m)
}
func (m *QuitChannelKTVSingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelKTVSingResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelKTVSingResp proto.InternalMessageInfo

func (m *QuitChannelKTVSingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *QuitChannelKTVSingResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateChannelKTVScoreReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Score                uint32       `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	SongId               string       `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Prefect              uint32       `protobuf:"varint,5,opt,name=prefect,proto3" json:"prefect,omitempty"`
	LastLrc              uint32       `protobuf:"varint,6,opt,name=last_lrc,json=lastLrc,proto3" json:"last_lrc,omitempty"`
	IsOriginalSong       bool         `protobuf:"varint,7,opt,name=is_original_song,json=isOriginalSong,proto3" json:"is_original_song,omitempty"`
	MaxScore             uint32       `protobuf:"varint,8,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateChannelKTVScoreReq) Reset()         { *m = UpdateChannelKTVScoreReq{} }
func (m *UpdateChannelKTVScoreReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelKTVScoreReq) ProtoMessage()    {}
func (*UpdateChannelKTVScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{32}
}
func (m *UpdateChannelKTVScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelKTVScoreReq.Unmarshal(m, b)
}
func (m *UpdateChannelKTVScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelKTVScoreReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelKTVScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelKTVScoreReq.Merge(dst, src)
}
func (m *UpdateChannelKTVScoreReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelKTVScoreReq.Size(m)
}
func (m *UpdateChannelKTVScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelKTVScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelKTVScoreReq proto.InternalMessageInfo

func (m *UpdateChannelKTVScoreReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdateChannelKTVScoreReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateChannelKTVScoreReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *UpdateChannelKTVScoreReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *UpdateChannelKTVScoreReq) GetPrefect() uint32 {
	if m != nil {
		return m.Prefect
	}
	return 0
}

func (m *UpdateChannelKTVScoreReq) GetLastLrc() uint32 {
	if m != nil {
		return m.LastLrc
	}
	return 0
}

func (m *UpdateChannelKTVScoreReq) GetIsOriginalSong() bool {
	if m != nil {
		return m.IsOriginalSong
	}
	return false
}

func (m *UpdateChannelKTVScoreReq) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

type UpdateChannelKTVScoreResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Interval             uint32        `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateChannelKTVScoreResp) Reset()         { *m = UpdateChannelKTVScoreResp{} }
func (m *UpdateChannelKTVScoreResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelKTVScoreResp) ProtoMessage()    {}
func (*UpdateChannelKTVScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{33}
}
func (m *UpdateChannelKTVScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelKTVScoreResp.Unmarshal(m, b)
}
func (m *UpdateChannelKTVScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelKTVScoreResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelKTVScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelKTVScoreResp.Merge(dst, src)
}
func (m *UpdateChannelKTVScoreResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelKTVScoreResp.Size(m)
}
func (m *UpdateChannelKTVScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelKTVScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelKTVScoreResp proto.InternalMessageInfo

func (m *UpdateChannelKTVScoreResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UpdateChannelKTVScoreResp) GetInterval() uint32 {
	if m != nil {
		return m.Interval
	}
	return 0
}

type SwitchChannelKTVBGReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Open                 bool         `protobuf:"varint,3,opt,name=open,proto3" json:"open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SwitchChannelKTVBGReq) Reset()         { *m = SwitchChannelKTVBGReq{} }
func (m *SwitchChannelKTVBGReq) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelKTVBGReq) ProtoMessage()    {}
func (*SwitchChannelKTVBGReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{34}
}
func (m *SwitchChannelKTVBGReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelKTVBGReq.Unmarshal(m, b)
}
func (m *SwitchChannelKTVBGReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelKTVBGReq.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelKTVBGReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelKTVBGReq.Merge(dst, src)
}
func (m *SwitchChannelKTVBGReq) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelKTVBGReq.Size(m)
}
func (m *SwitchChannelKTVBGReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelKTVBGReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelKTVBGReq proto.InternalMessageInfo

func (m *SwitchChannelKTVBGReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SwitchChannelKTVBGReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchChannelKTVBGReq) GetOpen() bool {
	if m != nil {
		return m.Open
	}
	return false
}

type SwitchChannelKTVBGResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SwitchChannelKTVBGResp) Reset()         { *m = SwitchChannelKTVBGResp{} }
func (m *SwitchChannelKTVBGResp) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelKTVBGResp) ProtoMessage()    {}
func (*SwitchChannelKTVBGResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{35}
}
func (m *SwitchChannelKTVBGResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelKTVBGResp.Unmarshal(m, b)
}
func (m *SwitchChannelKTVBGResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelKTVBGResp.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelKTVBGResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelKTVBGResp.Merge(dst, src)
}
func (m *SwitchChannelKTVBGResp) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelKTVBGResp.Size(m)
}
func (m *SwitchChannelKTVBGResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelKTVBGResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelKTVBGResp proto.InternalMessageInfo

func (m *SwitchChannelKTVBGResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetChannelKTVBGReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelKTVBGReq) Reset()         { *m = GetChannelKTVBGReq{} }
func (m *GetChannelKTVBGReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVBGReq) ProtoMessage()    {}
func (*GetChannelKTVBGReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{36}
}
func (m *GetChannelKTVBGReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVBGReq.Unmarshal(m, b)
}
func (m *GetChannelKTVBGReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVBGReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVBGReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVBGReq.Merge(dst, src)
}
func (m *GetChannelKTVBGReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVBGReq.Size(m)
}
func (m *GetChannelKTVBGReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVBGReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVBGReq proto.InternalMessageInfo

func (m *GetChannelKTVBGReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetChannelKTVBGResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Open                 bool          `protobuf:"varint,2,opt,name=open,proto3" json:"open,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelKTVBGResp) Reset()         { *m = GetChannelKTVBGResp{} }
func (m *GetChannelKTVBGResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVBGResp) ProtoMessage()    {}
func (*GetChannelKTVBGResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{37}
}
func (m *GetChannelKTVBGResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVBGResp.Unmarshal(m, b)
}
func (m *GetChannelKTVBGResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVBGResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVBGResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVBGResp.Merge(dst, src)
}
func (m *GetChannelKTVBGResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVBGResp.Size(m)
}
func (m *GetChannelKTVBGResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVBGResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVBGResp proto.InternalMessageInfo

func (m *GetChannelKTVBGResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelKTVBGResp) GetOpen() bool {
	if m != nil {
		return m.Open
	}
	return false
}

type ChannelKTVHandClapReq struct {
	BaseReq              *app.BaseReq          `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FromUserMicStatus    HandClapUserMicStatus `protobuf:"varint,3,opt,name=from_user_mic_status,json=fromUserMicStatus,proto3,enum=ga.channel_ktv.HandClapUserMicStatus" json:"from_user_mic_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ChannelKTVHandClapReq) Reset()         { *m = ChannelKTVHandClapReq{} }
func (m *ChannelKTVHandClapReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVHandClapReq) ProtoMessage()    {}
func (*ChannelKTVHandClapReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{38}
}
func (m *ChannelKTVHandClapReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVHandClapReq.Unmarshal(m, b)
}
func (m *ChannelKTVHandClapReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVHandClapReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVHandClapReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVHandClapReq.Merge(dst, src)
}
func (m *ChannelKTVHandClapReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVHandClapReq.Size(m)
}
func (m *ChannelKTVHandClapReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVHandClapReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVHandClapReq proto.InternalMessageInfo

func (m *ChannelKTVHandClapReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelKTVHandClapReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVHandClapReq) GetFromUserMicStatus() HandClapUserMicStatus {
	if m != nil {
		return m.FromUserMicStatus
	}
	return HandClapUserMicStatus_HandClapUserMicStatus_UNKNOW
}

type ChannelKTVHandClapResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelKTVHandClapResp) Reset()         { *m = ChannelKTVHandClapResp{} }
func (m *ChannelKTVHandClapResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVHandClapResp) ProtoMessage()    {}
func (*ChannelKTVHandClapResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{39}
}
func (m *ChannelKTVHandClapResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVHandClapResp.Unmarshal(m, b)
}
func (m *ChannelKTVHandClapResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVHandClapResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVHandClapResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVHandClapResp.Merge(dst, src)
}
func (m *ChannelKTVHandClapResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVHandClapResp.Size(m)
}
func (m *ChannelKTVHandClapResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVHandClapResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVHandClapResp proto.InternalMessageInfo

func (m *ChannelKTVHandClapResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type EndChannelKTVSingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *EndChannelKTVSingReq) Reset()         { *m = EndChannelKTVSingReq{} }
func (m *EndChannelKTVSingReq) String() string { return proto.CompactTextString(m) }
func (*EndChannelKTVSingReq) ProtoMessage()    {}
func (*EndChannelKTVSingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{40}
}
func (m *EndChannelKTVSingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndChannelKTVSingReq.Unmarshal(m, b)
}
func (m *EndChannelKTVSingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndChannelKTVSingReq.Marshal(b, m, deterministic)
}
func (dst *EndChannelKTVSingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndChannelKTVSingReq.Merge(dst, src)
}
func (m *EndChannelKTVSingReq) XXX_Size() int {
	return xxx_messageInfo_EndChannelKTVSingReq.Size(m)
}
func (m *EndChannelKTVSingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EndChannelKTVSingReq.DiscardUnknown(m)
}

var xxx_messageInfo_EndChannelKTVSingReq proto.InternalMessageInfo

func (m *EndChannelKTVSingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *EndChannelKTVSingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *EndChannelKTVSingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type EndChannelKTVSingResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *EndChannelKTVSingResp) Reset()         { *m = EndChannelKTVSingResp{} }
func (m *EndChannelKTVSingResp) String() string { return proto.CompactTextString(m) }
func (*EndChannelKTVSingResp) ProtoMessage()    {}
func (*EndChannelKTVSingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{41}
}
func (m *EndChannelKTVSingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndChannelKTVSingResp.Unmarshal(m, b)
}
func (m *EndChannelKTVSingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndChannelKTVSingResp.Marshal(b, m, deterministic)
}
func (dst *EndChannelKTVSingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndChannelKTVSingResp.Merge(dst, src)
}
func (m *EndChannelKTVSingResp) XXX_Size() int {
	return xxx_messageInfo_EndChannelKTVSingResp.Size(m)
}
func (m *EndChannelKTVSingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EndChannelKTVSingResp.DiscardUnknown(m)
}

var xxx_messageInfo_EndChannelKTVSingResp proto.InternalMessageInfo

func (m *EndChannelKTVSingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *EndChannelKTVSingResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type KickOutKTVMemberReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	VictimId             uint32       `protobuf:"varint,3,opt,name=victim_id,json=victimId,proto3" json:"victim_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *KickOutKTVMemberReq) Reset()         { *m = KickOutKTVMemberReq{} }
func (m *KickOutKTVMemberReq) String() string { return proto.CompactTextString(m) }
func (*KickOutKTVMemberReq) ProtoMessage()    {}
func (*KickOutKTVMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{42}
}
func (m *KickOutKTVMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickOutKTVMemberReq.Unmarshal(m, b)
}
func (m *KickOutKTVMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickOutKTVMemberReq.Marshal(b, m, deterministic)
}
func (dst *KickOutKTVMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickOutKTVMemberReq.Merge(dst, src)
}
func (m *KickOutKTVMemberReq) XXX_Size() int {
	return xxx_messageInfo_KickOutKTVMemberReq.Size(m)
}
func (m *KickOutKTVMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_KickOutKTVMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_KickOutKTVMemberReq proto.InternalMessageInfo

func (m *KickOutKTVMemberReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *KickOutKTVMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *KickOutKTVMemberReq) GetVictimId() uint32 {
	if m != nil {
		return m.VictimId
	}
	return 0
}

type KickOutKTVMemberResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *KickOutKTVMemberResp) Reset()         { *m = KickOutKTVMemberResp{} }
func (m *KickOutKTVMemberResp) String() string { return proto.CompactTextString(m) }
func (*KickOutKTVMemberResp) ProtoMessage()    {}
func (*KickOutKTVMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{43}
}
func (m *KickOutKTVMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KickOutKTVMemberResp.Unmarshal(m, b)
}
func (m *KickOutKTVMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KickOutKTVMemberResp.Marshal(b, m, deterministic)
}
func (dst *KickOutKTVMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KickOutKTVMemberResp.Merge(dst, src)
}
func (m *KickOutKTVMemberResp) XXX_Size() int {
	return xxx_messageInfo_KickOutKTVMemberResp.Size(m)
}
func (m *KickOutKTVMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_KickOutKTVMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_KickOutKTVMemberResp proto.InternalMessageInfo

func (m *KickOutKTVMemberResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *KickOutKTVMemberResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type CutChannelKTVSongReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CutChannelKTVSongReq) Reset()         { *m = CutChannelKTVSongReq{} }
func (m *CutChannelKTVSongReq) String() string { return proto.CompactTextString(m) }
func (*CutChannelKTVSongReq) ProtoMessage()    {}
func (*CutChannelKTVSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{44}
}
func (m *CutChannelKTVSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutChannelKTVSongReq.Unmarshal(m, b)
}
func (m *CutChannelKTVSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutChannelKTVSongReq.Marshal(b, m, deterministic)
}
func (dst *CutChannelKTVSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutChannelKTVSongReq.Merge(dst, src)
}
func (m *CutChannelKTVSongReq) XXX_Size() int {
	return xxx_messageInfo_CutChannelKTVSongReq.Size(m)
}
func (m *CutChannelKTVSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CutChannelKTVSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_CutChannelKTVSongReq proto.InternalMessageInfo

func (m *CutChannelKTVSongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CutChannelKTVSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CutChannelKTVSongReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type CutChannelKTVSongResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ChannelKTVUpdate `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CutChannelKTVSongResp) Reset()         { *m = CutChannelKTVSongResp{} }
func (m *CutChannelKTVSongResp) String() string { return proto.CompactTextString(m) }
func (*CutChannelKTVSongResp) ProtoMessage()    {}
func (*CutChannelKTVSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{45}
}
func (m *CutChannelKTVSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CutChannelKTVSongResp.Unmarshal(m, b)
}
func (m *CutChannelKTVSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CutChannelKTVSongResp.Marshal(b, m, deterministic)
}
func (dst *CutChannelKTVSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CutChannelKTVSongResp.Merge(dst, src)
}
func (m *CutChannelKTVSongResp) XXX_Size() int {
	return xxx_messageInfo_CutChannelKTVSongResp.Size(m)
}
func (m *CutChannelKTVSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CutChannelKTVSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_CutChannelKTVSongResp proto.InternalMessageInfo

func (m *CutChannelKTVSongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CutChannelKTVSongResp) GetInfo() *ChannelKTVUpdate {
	if m != nil {
		return m.Info
	}
	return nil
}

type ListChannelKTVSongListTypeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TypeId               int64        `protobuf:"varint,2,opt,name=type_id,json=typeId,proto3" json:"type_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListChannelKTVSongListTypeReq) Reset()         { *m = ListChannelKTVSongListTypeReq{} }
func (m *ListChannelKTVSongListTypeReq) String() string { return proto.CompactTextString(m) }
func (*ListChannelKTVSongListTypeReq) ProtoMessage()    {}
func (*ListChannelKTVSongListTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{46}
}
func (m *ListChannelKTVSongListTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelKTVSongListTypeReq.Unmarshal(m, b)
}
func (m *ListChannelKTVSongListTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelKTVSongListTypeReq.Marshal(b, m, deterministic)
}
func (dst *ListChannelKTVSongListTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelKTVSongListTypeReq.Merge(dst, src)
}
func (m *ListChannelKTVSongListTypeReq) XXX_Size() int {
	return xxx_messageInfo_ListChannelKTVSongListTypeReq.Size(m)
}
func (m *ListChannelKTVSongListTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelKTVSongListTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelKTVSongListTypeReq proto.InternalMessageInfo

func (m *ListChannelKTVSongListTypeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListChannelKTVSongListTypeReq) GetTypeId() int64 {
	if m != nil {
		return m.TypeId
	}
	return 0
}

type ListChannelKTVSongListTypeResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SongListTypeList     []*KTVSongListType `protobuf:"bytes,2,rep,name=song_list_type_list,json=songListTypeList,proto3" json:"song_list_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ListChannelKTVSongListTypeResp) Reset()         { *m = ListChannelKTVSongListTypeResp{} }
func (m *ListChannelKTVSongListTypeResp) String() string { return proto.CompactTextString(m) }
func (*ListChannelKTVSongListTypeResp) ProtoMessage()    {}
func (*ListChannelKTVSongListTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{47}
}
func (m *ListChannelKTVSongListTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelKTVSongListTypeResp.Unmarshal(m, b)
}
func (m *ListChannelKTVSongListTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelKTVSongListTypeResp.Marshal(b, m, deterministic)
}
func (dst *ListChannelKTVSongListTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelKTVSongListTypeResp.Merge(dst, src)
}
func (m *ListChannelKTVSongListTypeResp) XXX_Size() int {
	return xxx_messageInfo_ListChannelKTVSongListTypeResp.Size(m)
}
func (m *ListChannelKTVSongListTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelKTVSongListTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelKTVSongListTypeResp proto.InternalMessageInfo

func (m *ListChannelKTVSongListTypeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListChannelKTVSongListTypeResp) GetSongListTypeList() []*KTVSongListType {
	if m != nil {
		return m.SongListTypeList
	}
	return nil
}

type KTVSinger struct {
	User                 *KTVUser `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Main                 bool     `protobuf:"varint,3,opt,name=main,proto3" json:"main,omitempty"`
	Playing              bool     `protobuf:"varint,4,opt,name=playing,proto3" json:"playing,omitempty"`
	BestUid              uint32   `protobuf:"varint,5,opt,name=best_uid,json=bestUid,proto3" json:"best_uid,omitempty"`
	SpecialEventFlag     bool     `protobuf:"varint,6,opt,name=special_event_flag,json=specialEventFlag,proto3" json:"special_event_flag,omitempty"`
	IsEvenOriginalSong   bool     `protobuf:"varint,7,opt,name=is_even_original_song,json=isEvenOriginalSong,proto3" json:"is_even_original_song,omitempty"`
	IsSilently           bool     `protobuf:"varint,8,opt,name=is_silently,json=isSilently,proto3" json:"is_silently,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KTVSinger) Reset()         { *m = KTVSinger{} }
func (m *KTVSinger) String() string { return proto.CompactTextString(m) }
func (*KTVSinger) ProtoMessage()    {}
func (*KTVSinger) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{48}
}
func (m *KTVSinger) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KTVSinger.Unmarshal(m, b)
}
func (m *KTVSinger) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KTVSinger.Marshal(b, m, deterministic)
}
func (dst *KTVSinger) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KTVSinger.Merge(dst, src)
}
func (m *KTVSinger) XXX_Size() int {
	return xxx_messageInfo_KTVSinger.Size(m)
}
func (m *KTVSinger) XXX_DiscardUnknown() {
	xxx_messageInfo_KTVSinger.DiscardUnknown(m)
}

var xxx_messageInfo_KTVSinger proto.InternalMessageInfo

func (m *KTVSinger) GetUser() *KTVUser {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *KTVSinger) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *KTVSinger) GetMain() bool {
	if m != nil {
		return m.Main
	}
	return false
}

func (m *KTVSinger) GetPlaying() bool {
	if m != nil {
		return m.Playing
	}
	return false
}

func (m *KTVSinger) GetBestUid() uint32 {
	if m != nil {
		return m.BestUid
	}
	return 0
}

func (m *KTVSinger) GetSpecialEventFlag() bool {
	if m != nil {
		return m.SpecialEventFlag
	}
	return false
}

func (m *KTVSinger) GetIsEvenOriginalSong() bool {
	if m != nil {
		return m.IsEvenOriginalSong
	}
	return false
}

func (m *KTVSinger) GetIsSilently() bool {
	if m != nil {
		return m.IsSilently
	}
	return false
}

type ChannelKTVUpdate struct {
	ChannelId                  uint32                                  `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Status                     ChannelKTVUpdate_ChannelKTVUpdateStatus `protobuf:"varint,2,opt,name=status,proto3,enum=ga.channel_ktv.ChannelKTVUpdate_ChannelKTVUpdateStatus" json:"status,omitempty"`
	SingId                     string                                  `protobuf:"bytes,3,opt,name=sing_id,json=singId,proto3" json:"sing_id,omitempty"`
	SingName                   string                                  `protobuf:"bytes,4,opt,name=sing_name,json=singName,proto3" json:"sing_name,omitempty"`
	SingUid                    uint32                                  `protobuf:"varint,5,opt,name=sing_uid,json=singUid,proto3" json:"sing_uid,omitempty"`
	SingerName                 string                                  `protobuf:"bytes,6,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	ShareToken                 string                                  `protobuf:"bytes,7,opt,name=share_token,json=shareToken,proto3" json:"share_token,omitempty"`
	Duration                   uint32                                  `protobuf:"varint,8,opt,name=duration,proto3" json:"duration,omitempty"`
	SingerList                 []*KTVSinger                            `protobuf:"bytes,9,rep,name=singer_list,json=singerList,proto3" json:"singer_list,omitempty"`
	PlayListCount              uint32                                  `protobuf:"varint,10,opt,name=play_list_count,json=playListCount,proto3" json:"play_list_count,omitempty"`
	NextSingId                 string                                  `protobuf:"bytes,11,opt,name=next_sing_id,json=nextSingId,proto3" json:"next_sing_id,omitempty"`
	NextSingName               string                                  `protobuf:"bytes,12,opt,name=next_sing_name,json=nextSingName,proto3" json:"next_sing_name,omitempty"`
	NextSingUid                uint32                                  `protobuf:"varint,13,opt,name=next_sing_uid,json=nextSingUid,proto3" json:"next_sing_uid,omitempty"`
	NextSingerName             string                                  `protobuf:"bytes,14,opt,name=next_singer_name,json=nextSingerName,proto3" json:"next_singer_name,omitempty"`
	Type                       ChannelKTVUpdate_PlayingUpdateType      `protobuf:"varint,15,opt,name=type,proto3,enum=ga.channel_ktv.ChannelKTVUpdate_PlayingUpdateType" json:"type,omitempty"`
	BgOpen                     bool                                    `protobuf:"varint,16,opt,name=bg_open,json=bgOpen,proto3" json:"bg_open,omitempty"`
	UpdateTime                 int64                                   `protobuf:"varint,17,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	StartTime                  int64                                   `protobuf:"varint,18,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	SpecialEventMap            map[uint32]uint32                       `protobuf:"bytes,19,rep,name=special_event_map,json=specialEventMap,proto3" json:"special_event_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	SpecialEventAppendScoreMap map[uint32]uint32                       `protobuf:"bytes,22,rep,name=special_event_append_score_map,json=specialEventAppendScoreMap,proto3" json:"special_event_append_score_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MaxScore                   uint32                                  `protobuf:"varint,21,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	IsOriginalSong             bool                                    `protobuf:"varint,23,opt,name=is_original_song,json=isOriginalSong,proto3" json:"is_original_song,omitempty"`
	Glory                      *KTVGlory                               `protobuf:"bytes,24,opt,name=glory,proto3" json:"glory,omitempty"`
	IsPowerSong                bool                                    `protobuf:"varint,25,opt,name=is_power_song,json=isPowerSong,proto3" json:"is_power_song,omitempty"`
	BurstLightUserList         []uint32                                `protobuf:"varint,26,rep,packed,name=burst_light_user_list,json=burstLightUserList,proto3" json:"burst_light_user_list,omitempty"`
	RatingName                 string                                  `protobuf:"bytes,27,opt,name=RatingName,proto3" json:"RatingName,omitempty"`
	IsClimax                   bool                                    `protobuf:"varint,28,opt,name=is_climax,json=isClimax,proto3" json:"is_climax,omitempty"`
	SegmentBegin               uint32                                  `protobuf:"varint,29,opt,name=segment_begin,json=segmentBegin,proto3" json:"segment_begin,omitempty"`
	SegmentEnd                 uint32                                  `protobuf:"varint,30,opt,name=segment_end,json=segmentEnd,proto3" json:"segment_end,omitempty"`
	HandClapList               []uint32                                `protobuf:"varint,31,rep,packed,name=hand_clap_list,json=handClapList,proto3" json:"hand_clap_list,omitempty"`
	HandClapUser               []*KTVUser                              `protobuf:"bytes,32,rep,name=hand_clap_user,json=handClapUser,proto3" json:"hand_clap_user,omitempty"`
	VendorId                   int32                                   `protobuf:"varint,33,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	PitchAbility               int32                                   `protobuf:"varint,34,opt,name=pitch_ability,json=pitchAbility,proto3" json:"pitch_ability,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}                                `json:"-"`
	XXX_unrecognized           []byte                                  `json:"-"`
	XXX_sizecache              int32                                   `json:"-"`
}

func (m *ChannelKTVUpdate) Reset()         { *m = ChannelKTVUpdate{} }
func (m *ChannelKTVUpdate) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVUpdate) ProtoMessage()    {}
func (*ChannelKTVUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{49}
}
func (m *ChannelKTVUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVUpdate.Unmarshal(m, b)
}
func (m *ChannelKTVUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVUpdate.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVUpdate.Merge(dst, src)
}
func (m *ChannelKTVUpdate) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVUpdate.Size(m)
}
func (m *ChannelKTVUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVUpdate proto.InternalMessageInfo

func (m *ChannelKTVUpdate) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVUpdate) GetStatus() ChannelKTVUpdate_ChannelKTVUpdateStatus {
	if m != nil {
		return m.Status
	}
	return ChannelKTVUpdate_NOORDER
}

func (m *ChannelKTVUpdate) GetSingId() string {
	if m != nil {
		return m.SingId
	}
	return ""
}

func (m *ChannelKTVUpdate) GetSingName() string {
	if m != nil {
		return m.SingName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetSingUid() uint32 {
	if m != nil {
		return m.SingUid
	}
	return 0
}

func (m *ChannelKTVUpdate) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetShareToken() string {
	if m != nil {
		return m.ShareToken
	}
	return ""
}

func (m *ChannelKTVUpdate) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *ChannelKTVUpdate) GetSingerList() []*KTVSinger {
	if m != nil {
		return m.SingerList
	}
	return nil
}

func (m *ChannelKTVUpdate) GetPlayListCount() uint32 {
	if m != nil {
		return m.PlayListCount
	}
	return 0
}

func (m *ChannelKTVUpdate) GetNextSingId() string {
	if m != nil {
		return m.NextSingId
	}
	return ""
}

func (m *ChannelKTVUpdate) GetNextSingName() string {
	if m != nil {
		return m.NextSingName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetNextSingUid() uint32 {
	if m != nil {
		return m.NextSingUid
	}
	return 0
}

func (m *ChannelKTVUpdate) GetNextSingerName() string {
	if m != nil {
		return m.NextSingerName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetType() ChannelKTVUpdate_PlayingUpdateType {
	if m != nil {
		return m.Type
	}
	return ChannelKTVUpdate_NORMAL
}

func (m *ChannelKTVUpdate) GetBgOpen() bool {
	if m != nil {
		return m.BgOpen
	}
	return false
}

func (m *ChannelKTVUpdate) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ChannelKTVUpdate) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ChannelKTVUpdate) GetSpecialEventMap() map[uint32]uint32 {
	if m != nil {
		return m.SpecialEventMap
	}
	return nil
}

func (m *ChannelKTVUpdate) GetSpecialEventAppendScoreMap() map[uint32]uint32 {
	if m != nil {
		return m.SpecialEventAppendScoreMap
	}
	return nil
}

func (m *ChannelKTVUpdate) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

func (m *ChannelKTVUpdate) GetIsOriginalSong() bool {
	if m != nil {
		return m.IsOriginalSong
	}
	return false
}

func (m *ChannelKTVUpdate) GetGlory() *KTVGlory {
	if m != nil {
		return m.Glory
	}
	return nil
}

func (m *ChannelKTVUpdate) GetIsPowerSong() bool {
	if m != nil {
		return m.IsPowerSong
	}
	return false
}

func (m *ChannelKTVUpdate) GetBurstLightUserList() []uint32 {
	if m != nil {
		return m.BurstLightUserList
	}
	return nil
}

func (m *ChannelKTVUpdate) GetRatingName() string {
	if m != nil {
		return m.RatingName
	}
	return ""
}

func (m *ChannelKTVUpdate) GetIsClimax() bool {
	if m != nil {
		return m.IsClimax
	}
	return false
}

func (m *ChannelKTVUpdate) GetSegmentBegin() uint32 {
	if m != nil {
		return m.SegmentBegin
	}
	return 0
}

func (m *ChannelKTVUpdate) GetSegmentEnd() uint32 {
	if m != nil {
		return m.SegmentEnd
	}
	return 0
}

func (m *ChannelKTVUpdate) GetHandClapList() []uint32 {
	if m != nil {
		return m.HandClapList
	}
	return nil
}

func (m *ChannelKTVUpdate) GetHandClapUser() []*KTVUser {
	if m != nil {
		return m.HandClapUser
	}
	return nil
}

func (m *ChannelKTVUpdate) GetVendorId() int32 {
	if m != nil {
		return m.VendorId
	}
	return 0
}

func (m *ChannelKTVUpdate) GetPitchAbility() int32 {
	if m != nil {
		return m.PitchAbility
	}
	return 0
}

type ChannelKTVHandClap struct {
	FromUser             *KTVUser `protobuf:"bytes,1,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	ToUser               *KTVUser `protobuf:"bytes,2,opt,name=to_user,json=toUser,proto3" json:"to_user,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVHandClap) Reset()         { *m = ChannelKTVHandClap{} }
func (m *ChannelKTVHandClap) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVHandClap) ProtoMessage()    {}
func (*ChannelKTVHandClap) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{50}
}
func (m *ChannelKTVHandClap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVHandClap.Unmarshal(m, b)
}
func (m *ChannelKTVHandClap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVHandClap.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVHandClap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVHandClap.Merge(dst, src)
}
func (m *ChannelKTVHandClap) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVHandClap.Size(m)
}
func (m *ChannelKTVHandClap) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVHandClap.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVHandClap proto.InternalMessageInfo

func (m *ChannelKTVHandClap) GetFromUser() *KTVUser {
	if m != nil {
		return m.FromUser
	}
	return nil
}

func (m *ChannelKTVHandClap) GetToUser() *KTVUser {
	if m != nil {
		return m.ToUser
	}
	return nil
}

func (m *ChannelKTVHandClap) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type PlayListUpdate struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Song                 []*KTVOrder               `protobuf:"bytes,2,rep,name=song,proto3" json:"song,omitempty"`
	IndexId              []int64                   `protobuf:"varint,3,rep,packed,name=index_id,json=indexId,proto3" json:"index_id,omitempty"`
	UpdateType           PlayListUpdate_UpdateType `protobuf:"varint,4,opt,name=update_type,json=updateType,proto3,enum=ga.channel_ktv.PlayListUpdate_UpdateType" json:"update_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *PlayListUpdate) Reset()         { *m = PlayListUpdate{} }
func (m *PlayListUpdate) String() string { return proto.CompactTextString(m) }
func (*PlayListUpdate) ProtoMessage()    {}
func (*PlayListUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{51}
}
func (m *PlayListUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayListUpdate.Unmarshal(m, b)
}
func (m *PlayListUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayListUpdate.Marshal(b, m, deterministic)
}
func (dst *PlayListUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayListUpdate.Merge(dst, src)
}
func (m *PlayListUpdate) XXX_Size() int {
	return xxx_messageInfo_PlayListUpdate.Size(m)
}
func (m *PlayListUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayListUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_PlayListUpdate proto.InternalMessageInfo

func (m *PlayListUpdate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PlayListUpdate) GetSong() []*KTVOrder {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *PlayListUpdate) GetIndexId() []int64 {
	if m != nil {
		return m.IndexId
	}
	return nil
}

func (m *PlayListUpdate) GetUpdateType() PlayListUpdate_UpdateType {
	if m != nil {
		return m.UpdateType
	}
	return PlayListUpdate_ADD
}

// 获取特殊事件的文案
type GetSpecialEventCopyWritingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSpecialEventCopyWritingReq) Reset()         { *m = GetSpecialEventCopyWritingReq{} }
func (m *GetSpecialEventCopyWritingReq) String() string { return proto.CompactTextString(m) }
func (*GetSpecialEventCopyWritingReq) ProtoMessage()    {}
func (*GetSpecialEventCopyWritingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{52}
}
func (m *GetSpecialEventCopyWritingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecialEventCopyWritingReq.Unmarshal(m, b)
}
func (m *GetSpecialEventCopyWritingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecialEventCopyWritingReq.Marshal(b, m, deterministic)
}
func (dst *GetSpecialEventCopyWritingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecialEventCopyWritingReq.Merge(dst, src)
}
func (m *GetSpecialEventCopyWritingReq) XXX_Size() int {
	return xxx_messageInfo_GetSpecialEventCopyWritingReq.Size(m)
}
func (m *GetSpecialEventCopyWritingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecialEventCopyWritingReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecialEventCopyWritingReq proto.InternalMessageInfo

func (m *GetSpecialEventCopyWritingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetSpecialEventCopyWritingResp struct {
	BaseResp                   *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SpecialEventCopyWritingMap map[uint32]string `protobuf:"bytes,2,rep,name=special_event_copy_writing_map,json=specialEventCopyWritingMap,proto3" json:"special_event_copy_writing_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral       struct{}          `json:"-"`
	XXX_unrecognized           []byte            `json:"-"`
	XXX_sizecache              int32             `json:"-"`
}

func (m *GetSpecialEventCopyWritingResp) Reset()         { *m = GetSpecialEventCopyWritingResp{} }
func (m *GetSpecialEventCopyWritingResp) String() string { return proto.CompactTextString(m) }
func (*GetSpecialEventCopyWritingResp) ProtoMessage()    {}
func (*GetSpecialEventCopyWritingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{53}
}
func (m *GetSpecialEventCopyWritingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSpecialEventCopyWritingResp.Unmarshal(m, b)
}
func (m *GetSpecialEventCopyWritingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSpecialEventCopyWritingResp.Marshal(b, m, deterministic)
}
func (dst *GetSpecialEventCopyWritingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSpecialEventCopyWritingResp.Merge(dst, src)
}
func (m *GetSpecialEventCopyWritingResp) XXX_Size() int {
	return xxx_messageInfo_GetSpecialEventCopyWritingResp.Size(m)
}
func (m *GetSpecialEventCopyWritingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSpecialEventCopyWritingResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSpecialEventCopyWritingResp proto.InternalMessageInfo

func (m *GetSpecialEventCopyWritingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSpecialEventCopyWritingResp) GetSpecialEventCopyWritingMap() map[uint32]string {
	if m != nil {
		return m.SpecialEventCopyWritingMap
	}
	return nil
}

// 根据歌单获取歌曲信息
type GetChannelKTVSongListByIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SongListId           uint32       `protobuf:"varint,2,opt,name=song_list_id,json=songListId,proto3" json:"song_list_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelKTVSongListByIdReq) Reset()         { *m = GetChannelKTVSongListByIdReq{} }
func (m *GetChannelKTVSongListByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVSongListByIdReq) ProtoMessage()    {}
func (*GetChannelKTVSongListByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{54}
}
func (m *GetChannelKTVSongListByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVSongListByIdReq.Unmarshal(m, b)
}
func (m *GetChannelKTVSongListByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVSongListByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVSongListByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVSongListByIdReq.Merge(dst, src)
}
func (m *GetChannelKTVSongListByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVSongListByIdReq.Size(m)
}
func (m *GetChannelKTVSongListByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVSongListByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVSongListByIdReq proto.InternalMessageInfo

func (m *GetChannelKTVSongListByIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelKTVSongListByIdReq) GetSongListId() uint32 {
	if m != nil {
		return m.SongListId
	}
	return 0
}

type GetChannelKTVSongListByIdResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SongList             []*KTVSong    `protobuf:"bytes,2,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelKTVSongListByIdResp) Reset()         { *m = GetChannelKTVSongListByIdResp{} }
func (m *GetChannelKTVSongListByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelKTVSongListByIdResp) ProtoMessage()    {}
func (*GetChannelKTVSongListByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{55}
}
func (m *GetChannelKTVSongListByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelKTVSongListByIdResp.Unmarshal(m, b)
}
func (m *GetChannelKTVSongListByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelKTVSongListByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelKTVSongListByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelKTVSongListByIdResp.Merge(dst, src)
}
func (m *GetChannelKTVSongListByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelKTVSongListByIdResp.Size(m)
}
func (m *GetChannelKTVSongListByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelKTVSongListByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelKTVSongListByIdResp proto.InternalMessageInfo

func (m *GetChannelKTVSongListByIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelKTVSongListByIdResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

// 爆灯
type ChannelKTVBurstLightReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelKTVBurstLightReq) Reset()         { *m = ChannelKTVBurstLightReq{} }
func (m *ChannelKTVBurstLightReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVBurstLightReq) ProtoMessage()    {}
func (*ChannelKTVBurstLightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{56}
}
func (m *ChannelKTVBurstLightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBurstLightReq.Unmarshal(m, b)
}
func (m *ChannelKTVBurstLightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBurstLightReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBurstLightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBurstLightReq.Merge(dst, src)
}
func (m *ChannelKTVBurstLightReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBurstLightReq.Size(m)
}
func (m *ChannelKTVBurstLightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBurstLightReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBurstLightReq proto.InternalMessageInfo

func (m *ChannelKTVBurstLightReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelKTVBurstLightReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelKTVBurstLightResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelKTVBurstLightResp) Reset()         { *m = ChannelKTVBurstLightResp{} }
func (m *ChannelKTVBurstLightResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVBurstLightResp) ProtoMessage()    {}
func (*ChannelKTVBurstLightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{57}
}
func (m *ChannelKTVBurstLightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBurstLightResp.Unmarshal(m, b)
}
func (m *ChannelKTVBurstLightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBurstLightResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBurstLightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBurstLightResp.Merge(dst, src)
}
func (m *ChannelKTVBurstLightResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBurstLightResp.Size(m)
}
func (m *ChannelKTVBurstLightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBurstLightResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBurstLightResp proto.InternalMessageInfo

func (m *ChannelKTVBurstLightResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 一键击掌
type ChannelKTVHighFiveReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32     `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelKTVHighFiveReq) Reset()         { *m = ChannelKTVHighFiveReq{} }
func (m *ChannelKTVHighFiveReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVHighFiveReq) ProtoMessage()    {}
func (*ChannelKTVHighFiveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{58}
}
func (m *ChannelKTVHighFiveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVHighFiveReq.Unmarshal(m, b)
}
func (m *ChannelKTVHighFiveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVHighFiveReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVHighFiveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVHighFiveReq.Merge(dst, src)
}
func (m *ChannelKTVHighFiveReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVHighFiveReq.Size(m)
}
func (m *ChannelKTVHighFiveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVHighFiveReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVHighFiveReq proto.InternalMessageInfo

func (m *ChannelKTVHighFiveReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelKTVHighFiveReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVHighFiveReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type ChannelKTVHighFiveResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelKTVHighFiveResp) Reset()         { *m = ChannelKTVHighFiveResp{} }
func (m *ChannelKTVHighFiveResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVHighFiveResp) ProtoMessage()    {}
func (*ChannelKTVHighFiveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{59}
}
func (m *ChannelKTVHighFiveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVHighFiveResp.Unmarshal(m, b)
}
func (m *ChannelKTVHighFiveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVHighFiveResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVHighFiveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVHighFiveResp.Merge(dst, src)
}
func (m *ChannelKTVHighFiveResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVHighFiveResp.Size(m)
}
func (m *ChannelKTVHighFiveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVHighFiveResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVHighFiveResp proto.InternalMessageInfo

func (m *ChannelKTVHighFiveResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取引导高潮歌单列表
type ChannelKTVGuideClimaxSongListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelKTVGuideClimaxSongListReq) Reset()         { *m = ChannelKTVGuideClimaxSongListReq{} }
func (m *ChannelKTVGuideClimaxSongListReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVGuideClimaxSongListReq) ProtoMessage()    {}
func (*ChannelKTVGuideClimaxSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{60}
}
func (m *ChannelKTVGuideClimaxSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.Unmarshal(m, b)
}
func (m *ChannelKTVGuideClimaxSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVGuideClimaxSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.Merge(dst, src)
}
func (m *ChannelKTVGuideClimaxSongListReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.Size(m)
}
func (m *ChannelKTVGuideClimaxSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVGuideClimaxSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVGuideClimaxSongListReq proto.InternalMessageInfo

func (m *ChannelKTVGuideClimaxSongListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ChannelKTVGuideClimaxSongListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SongList             []*KTVSong    `protobuf:"bytes,2,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelKTVGuideClimaxSongListResp) Reset()         { *m = ChannelKTVGuideClimaxSongListResp{} }
func (m *ChannelKTVGuideClimaxSongListResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVGuideClimaxSongListResp) ProtoMessage()    {}
func (*ChannelKTVGuideClimaxSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{61}
}
func (m *ChannelKTVGuideClimaxSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.Unmarshal(m, b)
}
func (m *ChannelKTVGuideClimaxSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVGuideClimaxSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.Merge(dst, src)
}
func (m *ChannelKTVGuideClimaxSongListResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.Size(m)
}
func (m *ChannelKTVGuideClimaxSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVGuideClimaxSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVGuideClimaxSongListResp proto.InternalMessageInfo

func (m *ChannelKTVGuideClimaxSongListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelKTVGuideClimaxSongListResp) GetSongList() []*KTVSong {
	if m != nil {
		return m.SongList
	}
	return nil
}

// 批量获取歌曲高潮片段
type ChannelKTVBatchSongClimaxInfosReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SongIds              []string     `protobuf:"bytes,2,rep,name=song_ids,json=songIds,proto3" json:"song_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelKTVBatchSongClimaxInfosReq) Reset()         { *m = ChannelKTVBatchSongClimaxInfosReq{} }
func (m *ChannelKTVBatchSongClimaxInfosReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVBatchSongClimaxInfosReq) ProtoMessage()    {}
func (*ChannelKTVBatchSongClimaxInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{62}
}
func (m *ChannelKTVBatchSongClimaxInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.Unmarshal(m, b)
}
func (m *ChannelKTVBatchSongClimaxInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBatchSongClimaxInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.Merge(dst, src)
}
func (m *ChannelKTVBatchSongClimaxInfosReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.Size(m)
}
func (m *ChannelKTVBatchSongClimaxInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBatchSongClimaxInfosReq proto.InternalMessageInfo

func (m *ChannelKTVBatchSongClimaxInfosReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelKTVBatchSongClimaxInfosReq) GetSongIds() []string {
	if m != nil {
		return m.SongIds
	}
	return nil
}

type ChannelKTVBatchSongClimaxInfosResp struct {
	BaseResp             *app.BaseResp                                             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ClimaxInfoMap        map[string]*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo `protobuf:"bytes,2,rep,name=climax_info_map,json=climaxInfoMap,proto3" json:"climax_info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                  `json:"-"`
	XXX_unrecognized     []byte                                                    `json:"-"`
	XXX_sizecache        int32                                                     `json:"-"`
}

func (m *ChannelKTVBatchSongClimaxInfosResp) Reset()         { *m = ChannelKTVBatchSongClimaxInfosResp{} }
func (m *ChannelKTVBatchSongClimaxInfosResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVBatchSongClimaxInfosResp) ProtoMessage()    {}
func (*ChannelKTVBatchSongClimaxInfosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{63}
}
func (m *ChannelKTVBatchSongClimaxInfosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.Unmarshal(m, b)
}
func (m *ChannelKTVBatchSongClimaxInfosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBatchSongClimaxInfosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.Merge(dst, src)
}
func (m *ChannelKTVBatchSongClimaxInfosResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.Size(m)
}
func (m *ChannelKTVBatchSongClimaxInfosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp proto.InternalMessageInfo

func (m *ChannelKTVBatchSongClimaxInfosResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelKTVBatchSongClimaxInfosResp) GetClimaxInfoMap() map[string]*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo {
	if m != nil {
		return m.ClimaxInfoMap
	}
	return nil
}

type ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo struct {
	SegmentBegin         uint32   `protobuf:"varint,1,opt,name=segment_begin,json=segmentBegin,proto3" json:"segment_begin,omitempty"`
	SegmentEnd           uint32   `protobuf:"varint,2,opt,name=segment_end,json=segmentEnd,proto3" json:"segment_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) Reset() {
	*m = ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo{}
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) String() string {
	return proto.CompactTextString(m)
}
func (*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) ProtoMessage() {}
func (*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{63, 1}
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.Unmarshal(m, b)
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.Merge(dst, src)
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.Size(m)
}
func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo proto.InternalMessageInfo

func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) GetSegmentBegin() uint32 {
	if m != nil {
		return m.SegmentBegin
	}
	return 0
}

func (m *ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo) GetSegmentEnd() uint32 {
	if m != nil {
		return m.SegmentEnd
	}
	return 0
}

// 跳过前奏
type ChannelKTVSkipTheIntroReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongId               string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SkipTime             uint32       `protobuf:"varint,4,opt,name=skip_time,json=skipTime,proto3" json:"skip_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelKTVSkipTheIntroReq) Reset()         { *m = ChannelKTVSkipTheIntroReq{} }
func (m *ChannelKTVSkipTheIntroReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVSkipTheIntroReq) ProtoMessage()    {}
func (*ChannelKTVSkipTheIntroReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{64}
}
func (m *ChannelKTVSkipTheIntroReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVSkipTheIntroReq.Unmarshal(m, b)
}
func (m *ChannelKTVSkipTheIntroReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVSkipTheIntroReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVSkipTheIntroReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVSkipTheIntroReq.Merge(dst, src)
}
func (m *ChannelKTVSkipTheIntroReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVSkipTheIntroReq.Size(m)
}
func (m *ChannelKTVSkipTheIntroReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVSkipTheIntroReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVSkipTheIntroReq proto.InternalMessageInfo

func (m *ChannelKTVSkipTheIntroReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelKTVSkipTheIntroReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVSkipTheIntroReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ChannelKTVSkipTheIntroReq) GetSkipTime() uint32 {
	if m != nil {
		return m.SkipTime
	}
	return 0
}

type ChannelKTVSkipTheIntroResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelKTVSkipTheIntroResp) Reset()         { *m = ChannelKTVSkipTheIntroResp{} }
func (m *ChannelKTVSkipTheIntroResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVSkipTheIntroResp) ProtoMessage()    {}
func (*ChannelKTVSkipTheIntroResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{65}
}
func (m *ChannelKTVSkipTheIntroResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVSkipTheIntroResp.Unmarshal(m, b)
}
func (m *ChannelKTVSkipTheIntroResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVSkipTheIntroResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVSkipTheIntroResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVSkipTheIntroResp.Merge(dst, src)
}
func (m *ChannelKTVSkipTheIntroResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVSkipTheIntroResp.Size(m)
}
func (m *ChannelKTVSkipTheIntroResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVSkipTheIntroResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVSkipTheIntroResp proto.InternalMessageInfo

func (m *ChannelKTVSkipTheIntroResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 公屏引导回关
type ChannelKTVFollowTriggerReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UidList              []uint32     `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelKTVFollowTriggerReq) Reset()         { *m = ChannelKTVFollowTriggerReq{} }
func (m *ChannelKTVFollowTriggerReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVFollowTriggerReq) ProtoMessage()    {}
func (*ChannelKTVFollowTriggerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{66}
}
func (m *ChannelKTVFollowTriggerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVFollowTriggerReq.Unmarshal(m, b)
}
func (m *ChannelKTVFollowTriggerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVFollowTriggerReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVFollowTriggerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVFollowTriggerReq.Merge(dst, src)
}
func (m *ChannelKTVFollowTriggerReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVFollowTriggerReq.Size(m)
}
func (m *ChannelKTVFollowTriggerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVFollowTriggerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVFollowTriggerReq proto.InternalMessageInfo

func (m *ChannelKTVFollowTriggerReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelKTVFollowTriggerReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVFollowTriggerReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type ChannelKTVFollowTriggerResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelKTVFollowTriggerResp) Reset()         { *m = ChannelKTVFollowTriggerResp{} }
func (m *ChannelKTVFollowTriggerResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVFollowTriggerResp) ProtoMessage()    {}
func (*ChannelKTVFollowTriggerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{67}
}
func (m *ChannelKTVFollowTriggerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVFollowTriggerResp.Unmarshal(m, b)
}
func (m *ChannelKTVFollowTriggerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVFollowTriggerResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVFollowTriggerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVFollowTriggerResp.Merge(dst, src)
}
func (m *ChannelKTVFollowTriggerResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVFollowTriggerResp.Size(m)
}
func (m *ChannelKTVFollowTriggerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVFollowTriggerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVFollowTriggerResp proto.InternalMessageInfo

func (m *ChannelKTVFollowTriggerResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChannelKTVBootBackOffPushMsg struct {
	FollowUser           *KTVUser `protobuf:"bytes,1,opt,name=follow_user,json=followUser,proto3" json:"follow_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVBootBackOffPushMsg) Reset()         { *m = ChannelKTVBootBackOffPushMsg{} }
func (m *ChannelKTVBootBackOffPushMsg) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVBootBackOffPushMsg) ProtoMessage()    {}
func (*ChannelKTVBootBackOffPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{68}
}
func (m *ChannelKTVBootBackOffPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVBootBackOffPushMsg.Unmarshal(m, b)
}
func (m *ChannelKTVBootBackOffPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVBootBackOffPushMsg.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVBootBackOffPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVBootBackOffPushMsg.Merge(dst, src)
}
func (m *ChannelKTVBootBackOffPushMsg) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVBootBackOffPushMsg.Size(m)
}
func (m *ChannelKTVBootBackOffPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVBootBackOffPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVBootBackOffPushMsg proto.InternalMessageInfo

func (m *ChannelKTVBootBackOffPushMsg) GetFollowUser() *KTVUser {
	if m != nil {
		return m.FollowUser
	}
	return nil
}

// 搜索歌曲
type ChannelKTVQuerySongReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Keyword              string       `protobuf:"bytes,3,opt,name=keyword,proto3" json:"keyword,omitempty"`
	Page                 uint32       `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelKTVQuerySongReq) Reset()         { *m = ChannelKTVQuerySongReq{} }
func (m *ChannelKTVQuerySongReq) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVQuerySongReq) ProtoMessage()    {}
func (*ChannelKTVQuerySongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{69}
}
func (m *ChannelKTVQuerySongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVQuerySongReq.Unmarshal(m, b)
}
func (m *ChannelKTVQuerySongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVQuerySongReq.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVQuerySongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVQuerySongReq.Merge(dst, src)
}
func (m *ChannelKTVQuerySongReq) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVQuerySongReq.Size(m)
}
func (m *ChannelKTVQuerySongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVQuerySongReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVQuerySongReq proto.InternalMessageInfo

func (m *ChannelKTVQuerySongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChannelKTVQuerySongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelKTVQuerySongReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

func (m *ChannelKTVQuerySongReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

type ChannelKTVQuerySongResp struct {
	BaseResp             *app.BaseResp                   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Code                 uint32                          `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	Message              string                          `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Songs                []*ChannelKTVQuerySongResp_Song `protobuf:"bytes,4,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *ChannelKTVQuerySongResp) Reset()         { *m = ChannelKTVQuerySongResp{} }
func (m *ChannelKTVQuerySongResp) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVQuerySongResp) ProtoMessage()    {}
func (*ChannelKTVQuerySongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{70}
}
func (m *ChannelKTVQuerySongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVQuerySongResp.Unmarshal(m, b)
}
func (m *ChannelKTVQuerySongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVQuerySongResp.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVQuerySongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVQuerySongResp.Merge(dst, src)
}
func (m *ChannelKTVQuerySongResp) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVQuerySongResp.Size(m)
}
func (m *ChannelKTVQuerySongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVQuerySongResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVQuerySongResp proto.InternalMessageInfo

func (m *ChannelKTVQuerySongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChannelKTVQuerySongResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ChannelKTVQuerySongResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *ChannelKTVQuerySongResp) GetSongs() []*ChannelKTVQuerySongResp_Song {
	if m != nil {
		return m.Songs
	}
	return nil
}

type ChannelKTVQuerySongResp_Song struct {
	SongId               string                              `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName             string                              `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	SingerId             string                              `protobuf:"bytes,3,opt,name=singer_id,json=singerId,proto3" json:"singer_id,omitempty"`
	SingerName           string                              `protobuf:"bytes,4,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	SingerImg            string                              `protobuf:"bytes,5,opt,name=singer_img,json=singerImg,proto3" json:"singer_img,omitempty"`
	AlbumId              string                              `protobuf:"bytes,6,opt,name=album_id,json=albumId,proto3" json:"album_id,omitempty"`
	AlbumName            string                              `protobuf:"bytes,7,opt,name=album_name,json=albumName,proto3" json:"album_name,omitempty"`
	AlbumImg             string                              `protobuf:"bytes,8,opt,name=album_img,json=albumImg,proto3" json:"album_img,omitempty"`
	AlbumImgMini         string                              `protobuf:"bytes,9,opt,name=album_img_mini,json=albumImgMini,proto3" json:"album_img_mini,omitempty"`
	AlbumImgSmall        string                              `protobuf:"bytes,10,opt,name=album_img_small,json=albumImgSmall,proto3" json:"album_img_small,omitempty"`
	AlbumImgMedium       string                              `protobuf:"bytes,11,opt,name=album_img_medium,json=albumImgMedium,proto3" json:"album_img_medium,omitempty"`
	Copyright            *ChannelKTVQuerySongResp_Copyright  `protobuf:"bytes,12,opt,name=copyright,proto3" json:"copyright,omitempty"`
	VendorId             int32                               `protobuf:"varint,13,opt,name=vendor_id,json=vendorId,proto3" json:"vendor_id,omitempty"`
	PitchAbility         int32                               `protobuf:"varint,14,opt,name=pitch_ability,json=pitchAbility,proto3" json:"pitch_ability,omitempty"`
	HasShortSegment      int32                               `protobuf:"varint,15,opt,name=has_short_segment,json=hasShortSegment,proto3" json:"has_short_segment,omitempty"`
	ClimaxInfo           *ChannelKTVQuerySongResp_ClimaxInfo `protobuf:"bytes,16,opt,name=climax_info,json=climaxInfo,proto3" json:"climax_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *ChannelKTVQuerySongResp_Song) Reset()         { *m = ChannelKTVQuerySongResp_Song{} }
func (m *ChannelKTVQuerySongResp_Song) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVQuerySongResp_Song) ProtoMessage()    {}
func (*ChannelKTVQuerySongResp_Song) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{70, 0}
}
func (m *ChannelKTVQuerySongResp_Song) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVQuerySongResp_Song.Unmarshal(m, b)
}
func (m *ChannelKTVQuerySongResp_Song) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVQuerySongResp_Song.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVQuerySongResp_Song) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVQuerySongResp_Song.Merge(dst, src)
}
func (m *ChannelKTVQuerySongResp_Song) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVQuerySongResp_Song.Size(m)
}
func (m *ChannelKTVQuerySongResp_Song) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVQuerySongResp_Song.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVQuerySongResp_Song proto.InternalMessageInfo

func (m *ChannelKTVQuerySongResp_Song) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetSingerId() string {
	if m != nil {
		return m.SingerId
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetSingerImg() string {
	if m != nil {
		return m.SingerImg
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetAlbumId() string {
	if m != nil {
		return m.AlbumId
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetAlbumName() string {
	if m != nil {
		return m.AlbumName
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetAlbumImg() string {
	if m != nil {
		return m.AlbumImg
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetAlbumImgMini() string {
	if m != nil {
		return m.AlbumImgMini
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetAlbumImgSmall() string {
	if m != nil {
		return m.AlbumImgSmall
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetAlbumImgMedium() string {
	if m != nil {
		return m.AlbumImgMedium
	}
	return ""
}

func (m *ChannelKTVQuerySongResp_Song) GetCopyright() *ChannelKTVQuerySongResp_Copyright {
	if m != nil {
		return m.Copyright
	}
	return nil
}

func (m *ChannelKTVQuerySongResp_Song) GetVendorId() int32 {
	if m != nil {
		return m.VendorId
	}
	return 0
}

func (m *ChannelKTVQuerySongResp_Song) GetPitchAbility() int32 {
	if m != nil {
		return m.PitchAbility
	}
	return 0
}

func (m *ChannelKTVQuerySongResp_Song) GetHasShortSegment() int32 {
	if m != nil {
		return m.HasShortSegment
	}
	return 0
}

func (m *ChannelKTVQuerySongResp_Song) GetClimaxInfo() *ChannelKTVQuerySongResp_ClimaxInfo {
	if m != nil {
		return m.ClimaxInfo
	}
	return nil
}

type ChannelKTVQuerySongResp_Copyright struct {
	SongLyric            uint32   `protobuf:"varint,1,opt,name=song_lyric,json=songLyric,proto3" json:"song_lyric,omitempty"`
	Recording            uint32   `protobuf:"varint,2,opt,name=recording,proto3" json:"recording,omitempty"`
	Channel              uint32   `protobuf:"varint,3,opt,name=channel,proto3" json:"channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVQuerySongResp_Copyright) Reset()         { *m = ChannelKTVQuerySongResp_Copyright{} }
func (m *ChannelKTVQuerySongResp_Copyright) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVQuerySongResp_Copyright) ProtoMessage()    {}
func (*ChannelKTVQuerySongResp_Copyright) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{70, 1}
}
func (m *ChannelKTVQuerySongResp_Copyright) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVQuerySongResp_Copyright.Unmarshal(m, b)
}
func (m *ChannelKTVQuerySongResp_Copyright) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVQuerySongResp_Copyright.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVQuerySongResp_Copyright) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVQuerySongResp_Copyright.Merge(dst, src)
}
func (m *ChannelKTVQuerySongResp_Copyright) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVQuerySongResp_Copyright.Size(m)
}
func (m *ChannelKTVQuerySongResp_Copyright) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVQuerySongResp_Copyright.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVQuerySongResp_Copyright proto.InternalMessageInfo

func (m *ChannelKTVQuerySongResp_Copyright) GetSongLyric() uint32 {
	if m != nil {
		return m.SongLyric
	}
	return 0
}

func (m *ChannelKTVQuerySongResp_Copyright) GetRecording() uint32 {
	if m != nil {
		return m.Recording
	}
	return 0
}

func (m *ChannelKTVQuerySongResp_Copyright) GetChannel() uint32 {
	if m != nil {
		return m.Channel
	}
	return 0
}

type ChannelKTVQuerySongResp_ClimaxInfo struct {
	SegmentBegin         uint32   `protobuf:"varint,1,opt,name=segment_begin,json=segmentBegin,proto3" json:"segment_begin,omitempty"`
	SegmentEnd           uint32   `protobuf:"varint,2,opt,name=segment_end,json=segmentEnd,proto3" json:"segment_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelKTVQuerySongResp_ClimaxInfo) Reset()         { *m = ChannelKTVQuerySongResp_ClimaxInfo{} }
func (m *ChannelKTVQuerySongResp_ClimaxInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelKTVQuerySongResp_ClimaxInfo) ProtoMessage()    {}
func (*ChannelKTVQuerySongResp_ClimaxInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9, []int{70, 2}
}
func (m *ChannelKTVQuerySongResp_ClimaxInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelKTVQuerySongResp_ClimaxInfo.Unmarshal(m, b)
}
func (m *ChannelKTVQuerySongResp_ClimaxInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelKTVQuerySongResp_ClimaxInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelKTVQuerySongResp_ClimaxInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelKTVQuerySongResp_ClimaxInfo.Merge(dst, src)
}
func (m *ChannelKTVQuerySongResp_ClimaxInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelKTVQuerySongResp_ClimaxInfo.Size(m)
}
func (m *ChannelKTVQuerySongResp_ClimaxInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelKTVQuerySongResp_ClimaxInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelKTVQuerySongResp_ClimaxInfo proto.InternalMessageInfo

func (m *ChannelKTVQuerySongResp_ClimaxInfo) GetSegmentBegin() uint32 {
	if m != nil {
		return m.SegmentBegin
	}
	return 0
}

func (m *ChannelKTVQuerySongResp_ClimaxInfo) GetSegmentEnd() uint32 {
	if m != nil {
		return m.SegmentEnd
	}
	return 0
}

func init() {
	proto.RegisterType((*KTVCopyright)(nil), "ga.channel_ktv.KTVCopyright")
	proto.RegisterType((*KTVSong)(nil), "ga.channel_ktv.KTVSong")
	proto.RegisterType((*KTVUser)(nil), "ga.channel_ktv.KTVUser")
	proto.RegisterType((*KTVRecordSinger)(nil), "ga.channel_ktv.KTVRecordSinger")
	proto.RegisterType((*KTVRecord)(nil), "ga.channel_ktv.KTVRecord")
	proto.RegisterType((*KTVOrder)(nil), "ga.channel_ktv.KTVOrder")
	proto.RegisterType((*KTVGlory)(nil), "ga.channel_ktv.KTVGlory")
	proto.RegisterType((*KTVSongListType)(nil), "ga.channel_ktv.KTVSongListType")
	proto.RegisterType((*GetChannelKTVSongListReq)(nil), "ga.channel_ktv.GetChannelKTVSongListReq")
	proto.RegisterType((*GetChannelKTVSongListResp)(nil), "ga.channel_ktv.GetChannelKTVSongListResp")
	proto.RegisterType((*GetChannelKTVHistoryListReq)(nil), "ga.channel_ktv.GetChannelKTVHistoryListReq")
	proto.RegisterType((*GetChannelKTVHistoryListResp)(nil), "ga.channel_ktv.GetChannelKTVHistoryListResp")
	proto.RegisterType((*GetChannelKTVRecommendListReq)(nil), "ga.channel_ktv.GetChannelKTVRecommendListReq")
	proto.RegisterType((*GetChannelKTVRecommendListResp)(nil), "ga.channel_ktv.GetChannelKTVRecommendListResp")
	proto.RegisterType((*GetChannelKTVPlayListReq)(nil), "ga.channel_ktv.GetChannelKTVPlayListReq")
	proto.RegisterType((*GetChannelKTVPlayListResp)(nil), "ga.channel_ktv.GetChannelKTVPlayListResp")
	proto.RegisterType((*GetChannelKTVGuessLikeSongListReq)(nil), "ga.channel_ktv.GetChannelKTVGuessLikeSongListReq")
	proto.RegisterType((*GetChannelKTVGuessLikeSongListResp)(nil), "ga.channel_ktv.GetChannelKTVGuessLikeSongListResp")
	proto.RegisterType((*AddChannelKTVSongToPlayListReq)(nil), "ga.channel_ktv.AddChannelKTVSongToPlayListReq")
	proto.RegisterType((*AddChannelKTVSongToPlayListResp)(nil), "ga.channel_ktv.AddChannelKTVSongToPlayListResp")
	proto.RegisterType((*MoveUpChannelKTVSongReq)(nil), "ga.channel_ktv.MoveUpChannelKTVSongReq")
	proto.RegisterType((*MoveUpChannelKTVSongResp)(nil), "ga.channel_ktv.MoveUpChannelKTVSongResp")
	proto.RegisterType((*RemoveChannelKTVSongReq)(nil), "ga.channel_ktv.RemoveChannelKTVSongReq")
	proto.RegisterType((*RemoveChannelKTVSongResp)(nil), "ga.channel_ktv.RemoveChannelKTVSongResp")
	proto.RegisterType((*BeginChannelKTVSingReq)(nil), "ga.channel_ktv.BeginChannelKTVSingReq")
	proto.RegisterType((*BeginChannelKTVSingResp)(nil), "ga.channel_ktv.BeginChannelKTVSingResp")
	proto.RegisterType((*GetChannelKTVInfoReq)(nil), "ga.channel_ktv.GetChannelKTVInfoReq")
	proto.RegisterType((*GetChannelKTVInfoResp)(nil), "ga.channel_ktv.GetChannelKTVInfoResp")
	proto.RegisterType((*JoinChannelKTVSingReq)(nil), "ga.channel_ktv.JoinChannelKTVSingReq")
	proto.RegisterType((*JoinChannelKTVSingResp)(nil), "ga.channel_ktv.JoinChannelKTVSingResp")
	proto.RegisterType((*QuitChannelKTVSingReq)(nil), "ga.channel_ktv.QuitChannelKTVSingReq")
	proto.RegisterType((*QuitChannelKTVSingResp)(nil), "ga.channel_ktv.QuitChannelKTVSingResp")
	proto.RegisterType((*UpdateChannelKTVScoreReq)(nil), "ga.channel_ktv.UpdateChannelKTVScoreReq")
	proto.RegisterType((*UpdateChannelKTVScoreResp)(nil), "ga.channel_ktv.UpdateChannelKTVScoreResp")
	proto.RegisterType((*SwitchChannelKTVBGReq)(nil), "ga.channel_ktv.SwitchChannelKTVBGReq")
	proto.RegisterType((*SwitchChannelKTVBGResp)(nil), "ga.channel_ktv.SwitchChannelKTVBGResp")
	proto.RegisterType((*GetChannelKTVBGReq)(nil), "ga.channel_ktv.GetChannelKTVBGReq")
	proto.RegisterType((*GetChannelKTVBGResp)(nil), "ga.channel_ktv.GetChannelKTVBGResp")
	proto.RegisterType((*ChannelKTVHandClapReq)(nil), "ga.channel_ktv.ChannelKTVHandClapReq")
	proto.RegisterType((*ChannelKTVHandClapResp)(nil), "ga.channel_ktv.ChannelKTVHandClapResp")
	proto.RegisterType((*EndChannelKTVSingReq)(nil), "ga.channel_ktv.EndChannelKTVSingReq")
	proto.RegisterType((*EndChannelKTVSingResp)(nil), "ga.channel_ktv.EndChannelKTVSingResp")
	proto.RegisterType((*KickOutKTVMemberReq)(nil), "ga.channel_ktv.KickOutKTVMemberReq")
	proto.RegisterType((*KickOutKTVMemberResp)(nil), "ga.channel_ktv.KickOutKTVMemberResp")
	proto.RegisterType((*CutChannelKTVSongReq)(nil), "ga.channel_ktv.CutChannelKTVSongReq")
	proto.RegisterType((*CutChannelKTVSongResp)(nil), "ga.channel_ktv.CutChannelKTVSongResp")
	proto.RegisterType((*ListChannelKTVSongListTypeReq)(nil), "ga.channel_ktv.ListChannelKTVSongListTypeReq")
	proto.RegisterType((*ListChannelKTVSongListTypeResp)(nil), "ga.channel_ktv.ListChannelKTVSongListTypeResp")
	proto.RegisterType((*KTVSinger)(nil), "ga.channel_ktv.KTVSinger")
	proto.RegisterType((*ChannelKTVUpdate)(nil), "ga.channel_ktv.ChannelKTVUpdate")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.channel_ktv.ChannelKTVUpdate.SpecialEventAppendScoreMapEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.channel_ktv.ChannelKTVUpdate.SpecialEventMapEntry")
	proto.RegisterType((*ChannelKTVHandClap)(nil), "ga.channel_ktv.ChannelKTVHandClap")
	proto.RegisterType((*PlayListUpdate)(nil), "ga.channel_ktv.PlayListUpdate")
	proto.RegisterType((*GetSpecialEventCopyWritingReq)(nil), "ga.channel_ktv.GetSpecialEventCopyWritingReq")
	proto.RegisterType((*GetSpecialEventCopyWritingResp)(nil), "ga.channel_ktv.GetSpecialEventCopyWritingResp")
	proto.RegisterMapType((map[uint32]string)(nil), "ga.channel_ktv.GetSpecialEventCopyWritingResp.SpecialEventCopyWritingMapEntry")
	proto.RegisterType((*GetChannelKTVSongListByIdReq)(nil), "ga.channel_ktv.GetChannelKTVSongListByIdReq")
	proto.RegisterType((*GetChannelKTVSongListByIdResp)(nil), "ga.channel_ktv.GetChannelKTVSongListByIdResp")
	proto.RegisterType((*ChannelKTVBurstLightReq)(nil), "ga.channel_ktv.ChannelKTVBurstLightReq")
	proto.RegisterType((*ChannelKTVBurstLightResp)(nil), "ga.channel_ktv.ChannelKTVBurstLightResp")
	proto.RegisterType((*ChannelKTVHighFiveReq)(nil), "ga.channel_ktv.ChannelKTVHighFiveReq")
	proto.RegisterType((*ChannelKTVHighFiveResp)(nil), "ga.channel_ktv.ChannelKTVHighFiveResp")
	proto.RegisterType((*ChannelKTVGuideClimaxSongListReq)(nil), "ga.channel_ktv.ChannelKTVGuideClimaxSongListReq")
	proto.RegisterType((*ChannelKTVGuideClimaxSongListResp)(nil), "ga.channel_ktv.ChannelKTVGuideClimaxSongListResp")
	proto.RegisterType((*ChannelKTVBatchSongClimaxInfosReq)(nil), "ga.channel_ktv.ChannelKTVBatchSongClimaxInfosReq")
	proto.RegisterType((*ChannelKTVBatchSongClimaxInfosResp)(nil), "ga.channel_ktv.ChannelKTVBatchSongClimaxInfosResp")
	proto.RegisterMapType((map[string]*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo)(nil), "ga.channel_ktv.ChannelKTVBatchSongClimaxInfosResp.ClimaxInfoMapEntry")
	proto.RegisterType((*ChannelKTVBatchSongClimaxInfosResp_ClimaxInfo)(nil), "ga.channel_ktv.ChannelKTVBatchSongClimaxInfosResp.ClimaxInfo")
	proto.RegisterType((*ChannelKTVSkipTheIntroReq)(nil), "ga.channel_ktv.ChannelKTVSkipTheIntroReq")
	proto.RegisterType((*ChannelKTVSkipTheIntroResp)(nil), "ga.channel_ktv.ChannelKTVSkipTheIntroResp")
	proto.RegisterType((*ChannelKTVFollowTriggerReq)(nil), "ga.channel_ktv.ChannelKTVFollowTriggerReq")
	proto.RegisterType((*ChannelKTVFollowTriggerResp)(nil), "ga.channel_ktv.ChannelKTVFollowTriggerResp")
	proto.RegisterType((*ChannelKTVBootBackOffPushMsg)(nil), "ga.channel_ktv.ChannelKTVBootBackOffPushMsg")
	proto.RegisterType((*ChannelKTVQuerySongReq)(nil), "ga.channel_ktv.ChannelKTVQuerySongReq")
	proto.RegisterType((*ChannelKTVQuerySongResp)(nil), "ga.channel_ktv.ChannelKTVQuerySongResp")
	proto.RegisterType((*ChannelKTVQuerySongResp_Song)(nil), "ga.channel_ktv.ChannelKTVQuerySongResp.Song")
	proto.RegisterType((*ChannelKTVQuerySongResp_Copyright)(nil), "ga.channel_ktv.ChannelKTVQuerySongResp.Copyright")
	proto.RegisterType((*ChannelKTVQuerySongResp_ClimaxInfo)(nil), "ga.channel_ktv.ChannelKTVQuerySongResp.ClimaxInfo")
	proto.RegisterEnum("ga.channel_ktv.HandClapUserMicStatus", HandClapUserMicStatus_name, HandClapUserMicStatus_value)
	proto.RegisterEnum("ga.channel_ktv.ChannelKTVUpdate_ChannelKTVUpdateStatus", ChannelKTVUpdate_ChannelKTVUpdateStatus_name, ChannelKTVUpdate_ChannelKTVUpdateStatus_value)
	proto.RegisterEnum("ga.channel_ktv.ChannelKTVUpdate_PlayingUpdateType", ChannelKTVUpdate_PlayingUpdateType_name, ChannelKTVUpdate_PlayingUpdateType_value)
	proto.RegisterEnum("ga.channel_ktv.PlayListUpdate_UpdateType", PlayListUpdate_UpdateType_name, PlayListUpdate_UpdateType_value)
}

func init() {
	proto.RegisterFile("channel-ktv-logic_.proto", fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9)
}

var fileDescriptor_channel_ktv_logic__b18afdf0ac6da3a9 = []byte{
	// 3442 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x3b, 0x4d, 0x6f, 0xe3, 0x48,
	0x76, 0xa3, 0x0f, 0x5b, 0xd2, 0x93, 0x25, 0xb3, 0x6b, 0xec, 0x69, 0xb5, 0xfb, 0xcb, 0xc3, 0x4c,
	0x06, 0x9e, 0xc9, 0xac, 0x67, 0xa7, 0xb3, 0x8b, 0x5d, 0x0c, 0xb2, 0xc0, 0xda, 0xb2, 0xec, 0xd6,
	0xd8, 0x96, 0x7a, 0x28, 0xb9, 0x27, 0x1b, 0x20, 0xe0, 0x52, 0x64, 0x59, 0xaa, 0x98, 0x5f, 0x43,
	0x52, 0xb6, 0xb5, 0x13, 0x64, 0x13, 0x60, 0x73, 0x08, 0xb0, 0xb9, 0x24, 0x97, 0x0d, 0x02, 0xe4,
	0x98, 0xeb, 0xe6, 0x14, 0x20, 0x3f, 0x20, 0x87, 0xfc, 0x83, 0x5c, 0x82, 0xdc, 0x72, 0xcb, 0x25,
	0xe7, 0x1c, 0x82, 0x7a, 0x45, 0x52, 0xa4, 0x3e, 0xac, 0x56, 0x8f, 0xa3, 0xb9, 0xb1, 0x5e, 0x3d,
	0xd6, 0x7b, 0xf5, 0xea, 0x7d, 0xd5, 0x7b, 0x24, 0xd4, 0xf4, 0x81, 0x66, 0xdb, 0xd4, 0xfc, 0xde,
	0x55, 0x70, 0xfd, 0x3d, 0xd3, 0xe9, 0x33, 0x5d, 0xdd, 0x77, 0x3d, 0x27, 0x70, 0x48, 0xb5, 0xaf,
	0xed, 0x87, 0x93, 0xea, 0x55, 0x70, 0xbd, 0x53, 0xe9, 0x6b, 0x6a, 0x4f, 0xf3, 0xa9, 0x98, 0x96,
	0xaf, 0x60, 0xe3, 0xb4, 0xfb, 0xba, 0xee, 0xb8, 0x23, 0x8f, 0xf5, 0x07, 0x01, 0x79, 0x0a, 0xe0,
	0x3b, 0x76, 0x5f, 0x35, 0x47, 0x1e, 0xd3, 0x6b, 0x99, 0xdd, 0xcc, 0xde, 0x9a, 0x52, 0xe2, 0x90,
	0x33, 0x0e, 0x20, 0x32, 0x6c, 0x78, 0x54, 0x77, 0x3c, 0x83, 0xd9, 0xfd, 0x6b, 0xcd, 0xac, 0x65,
	0x11, 0x21, 0x05, 0x23, 0x35, 0x28, 0x84, 0x04, 0x6b, 0x39, 0x9c, 0x8e, 0x86, 0xf2, 0xbf, 0xe5,
	0xa0, 0x70, 0xda, 0x7d, 0xdd, 0x71, 0xec, 0x3e, 0x79, 0x08, 0x05, 0x24, 0xc4, 0x0c, 0xa4, 0x52,
	0x52, 0xd6, 0xf9, 0xb0, 0x69, 0x90, 0xc7, 0x80, 0xf4, 0x54, 0x5b, 0xb3, 0x28, 0xae, 0x5f, 0x52,
	0x8a, 0x1c, 0xd0, 0xd2, 0x2c, 0x4a, 0x9e, 0x43, 0xd9, 0x67, 0x76, 0x9f, 0x7a, 0x62, 0x3a, 0x87,
	0xd3, 0x20, 0x40, 0x88, 0xf0, 0x14, 0x40, 0x33, 0x7b, 0x43, 0x4b, 0xcc, 0xe7, 0x71, 0xbe, 0x84,
	0x10, 0x9c, 0x7e, 0x0c, 0x62, 0xa0, 0x32, 0xab, 0x5f, 0x5b, 0x13, 0x8b, 0x23, 0xa0, 0x69, 0xf5,
	0xc9, 0x0e, 0x14, 0x8d, 0xa1, 0xa7, 0x05, 0xcc, 0xb1, 0x6b, 0xeb, 0xc8, 0x79, 0x3c, 0x26, 0xdb,
	0xb0, 0xce, 0x7c, 0x55, 0x33, 0x8c, 0x5a, 0x61, 0x37, 0xb3, 0x57, 0x54, 0xd6, 0x98, 0x7f, 0x60,
	0x18, 0xe4, 0x73, 0x28, 0xe9, 0x91, 0xec, 0x6a, 0xc5, 0xdd, 0xcc, 0x5e, 0xf9, 0xc5, 0x93, 0xfd,
	0xb4, 0xc4, 0xf7, 0x93, 0xf2, 0x55, 0xc6, 0xe8, 0xe4, 0x77, 0xa0, 0xe2, 0xd3, 0xbe, 0x45, 0xed,
	0x40, 0xed, 0xd1, 0x3e, 0xb3, 0x6b, 0xa5, 0xdd, 0xcc, 0x5e, 0x45, 0xd9, 0x08, 0x81, 0x87, 0x1c,
	0x86, 0x1b, 0x0e, 0x91, 0xa8, 0x6d, 0xd4, 0x00, 0x51, 0x20, 0x04, 0x35, 0x6c, 0x14, 0x17, 0xf3,
	0x55, 0xdd, 0x64, 0x96, 0x76, 0x5b, 0x2b, 0x23, 0x6f, 0x45, 0xe6, 0xd7, 0x71, 0xcc, 0x27, 0xaf,
	0xa9, 0x6d, 0x38, 0x1e, 0x17, 0xf3, 0x86, 0xd8, 0x92, 0x00, 0x34, 0x0d, 0x4e, 0xdf, 0x65, 0x81,
	0x3e, 0x50, 0xb5, 0x1e, 0x33, 0x59, 0x30, 0xaa, 0x55, 0xc4, 0x61, 0x22, 0xf0, 0x40, 0xc0, 0xf8,
	0x31, 0x59, 0x34, 0xd0, 0xf8, 0xfb, 0xba, 0x38, 0x26, 0x3e, 0x6c, 0x1a, 0x32, 0xc5, 0xa3, 0xbc,
	0xf0, 0xa9, 0x47, 0x24, 0xc8, 0x0d, 0xc3, 0x63, 0xac, 0x28, 0xfc, 0x91, 0x4b, 0x72, 0xe8, 0x53,
	0x2f, 0x79, 0x84, 0xd1, 0x98, 0xcf, 0xd9, 0x4c, 0xbf, 0x4a, 0x9c, 0x5f, 0x3c, 0xe6, 0x2b, 0xf9,
	0xf4, 0x16, 0x8f, 0x6d, 0x4d, 0xe1, 0x8f, 0xb2, 0x0a, 0x9b, 0xa7, 0xdd, 0xd7, 0x0a, 0xea, 0x57,
	0x07, 0x8f, 0x99, 0xfc, 0x1e, 0xe4, 0xf9, 0x62, 0x48, 0xaf, 0xfc, 0xe2, 0xe1, 0x0c, 0x71, 0x73,
	0xae, 0x14, 0x44, 0x42, 0xf9, 0xe9, 0x8e, 0x47, 0xd5, 0x60, 0x18, 0x04, 0x0c, 0x99, 0xe1, 0xf2,
	0xe3, 0xa0, 0x2e, 0x87, 0xc8, 0xff, 0x9a, 0x81, 0x52, 0x4c, 0x81, 0xaf, 0xcd, 0x75, 0xed, 0x8e,
	0xb5, 0xb9, 0xf2, 0x2a, 0x88, 0x84, 0xb6, 0x82, 0x6b, 0xfb, 0x8e, 0xe9, 0x84, 0x4b, 0x97, 0x10,
	0xd2, 0x71, 0x4c, 0x87, 0x7c, 0x0c, 0x0f, 0x42, 0xd2, 0x4e, 0xa0, 0x99, 0x21, 0x03, 0x79, 0xc4,
	0xda, 0x14, 0x0c, 0x70, 0x38, 0x72, 0x41, 0x7e, 0x1a, 0xeb, 0xb5, 0xc9, 0xfc, 0xa0, 0x96, 0xdb,
	0xcd, 0xed, 0x95, 0x5f, 0x3c, 0x9f, 0x41, 0x3e, 0x29, 0x89, 0x48, 0xf1, 0xcf, 0x98, 0x1f, 0xc8,
	0xbf, 0xcd, 0x40, 0xf1, 0xb4, 0xfb, 0xba, 0xed, 0x19, 0xd4, 0x23, 0x8f, 0xa0, 0xc8, 0x6c, 0x83,
	0xde, 0x46, 0xd6, 0x95, 0x53, 0x0a, 0x38, 0x6e, 0x8e, 0x77, 0x98, 0x7d, 0x93, 0x1d, 0x46, 0xa2,
	0xce, 0xbd, 0x89, 0xa8, 0xf7, 0x61, 0xad, 0x6f, 0x3a, 0xde, 0x08, 0xf7, 0x58, 0x7e, 0x51, 0x9b,
	0x81, 0x7d, 0xc2, 0xe7, 0x15, 0x81, 0x26, 0xff, 0x97, 0xe0, 0x18, 0x61, 0x5c, 0x96, 0x08, 0x15,
	0x76, 0x2b, 0x3c, 0x42, 0x09, 0x21, 0x91, 0xdd, 0x8a, 0x69, 0x6e, 0xb7, 0xa1, 0x46, 0x21, 0x80,
	0xdb, 0xed, 0x73, 0x28, 0x8b, 0x49, 0xdd, 0x31, 0x1d, 0x2f, 0x72, 0x0a, 0x08, 0xaa, 0x73, 0xc8,
	0x78, 0x71, 0x4f, 0xb3, 0xaf, 0xc2, 0x23, 0x10, 0xeb, 0x29, 0x9a, 0x7d, 0xc5, 0xa7, 0x5d, 0x53,
	0x8b, 0x5e, 0x17, 0x5e, 0xa1, 0xc4, 0x21, 0xf1, 0xdb, 0x3d, 0xbe, 0x0b, 0x0f, 0x89, 0xaf, 0x8b,
	0x69, 0x01, 0xe1, 0xd4, 0x77, 0x61, 0x43, 0x2c, 0xde, 0xeb, 0x23, 0x42, 0x21, 0x41, 0xfe, 0xb0,
	0xdf, 0xb4, 0xfa, 0xf2, 0x3f, 0x66, 0x50, 0x89, 0xb9, 0x5c, 0xf9, 0x51, 0x75, 0x47, 0x2e, 0x25,
	0x55, 0xc8, 0xc6, 0x67, 0x93, 0x65, 0x06, 0x21, 0x90, 0x4f, 0x58, 0x0b, 0x3e, 0x93, 0x2d, 0x58,
	0x0b, 0x58, 0x60, 0x46, 0x66, 0x22, 0x06, 0x5c, 0x14, 0xba, 0x73, 0x4d, 0x3d, 0x75, 0xe8, 0x99,
	0xa1, 0x83, 0x2b, 0x22, 0xe0, 0xc2, 0x33, 0xb9, 0x71, 0xb9, 0x8e, 0xcf, 0xd0, 0x85, 0xad, 0xe1,
	0x3e, 0xe3, 0x31, 0xd9, 0x85, 0xb2, 0x41, 0x7d, 0xdd, 0x63, 0x2e, 0x4e, 0x0b, 0x3e, 0x93, 0x20,
	0xf9, 0xaf, 0x32, 0x50, 0x3b, 0xa1, 0x41, 0x5d, 0x1c, 0x5a, 0x82, 0x65, 0x85, 0x7e, 0x4d, 0x3e,
	0x84, 0x22, 0x8f, 0x1b, 0xaa, 0x47, 0xbf, 0x0e, 0xcd, 0xa3, 0xcc, 0x4f, 0xf8, 0x50, 0xf3, 0xa9,
	0x42, 0xbf, 0x56, 0x0a, 0x3d, 0xf1, 0xc0, 0x77, 0xe2, 0x6a, 0x7d, 0x1a, 0xda, 0x03, 0x3e, 0x73,
	0x98, 0xcf, 0x7e, 0x21, 0x36, 0x52, 0x51, 0xf0, 0x99, 0x8b, 0x35, 0xd2, 0x0e, 0x66, 0x44, 0x87,
	0x12, 0x42, 0x9a, 0x86, 0xfc, 0xa7, 0xf0, 0x68, 0x0e, 0x2b, 0xbe, 0x4b, 0x3e, 0x82, 0x52, 0xc8,
	0x8b, 0xef, 0x86, 0xcc, 0x6c, 0x8c, 0x99, 0xf1, 0x5d, 0xa5, 0xd8, 0x0b, 0x9f, 0xc8, 0x0f, 0xc2,
	0x70, 0x82, 0x76, 0x95, 0x45, 0xbb, 0x9a, 0xab, 0xf4, 0x18, 0x67, 0xd0, 0x9a, 0x7e, 0x9d, 0x81,
	0xc7, 0x29, 0xf2, 0x2f, 0x99, 0x1f, 0x38, 0xde, 0xe8, 0x3b, 0x12, 0xc6, 0x5f, 0x66, 0xe0, 0xc9,
	0x7c, 0x76, 0x96, 0x13, 0xc8, 0xe7, 0x50, 0x16, 0xe1, 0x3a, 0x29, 0x92, 0x47, 0x73, 0x5d, 0x8d,
	0x02, 0x02, 0x1b, 0xc5, 0xf2, 0x2f, 0x19, 0x78, 0x9a, 0xe2, 0x83, 0xe3, 0x58, 0x16, 0xb5, 0x8d,
	0xef, 0x46, 0x30, 0xe4, 0x23, 0x78, 0x10, 0x9f, 0xae, 0x1a, 0x8c, 0x5c, 0xca, 0xb1, 0xd6, 0xd0,
	0xaa, 0xaa, 0x7e, 0xc2, 0xde, 0x9a, 0x86, 0xfc, 0x17, 0x19, 0x78, 0x76, 0x17, 0xef, 0xab, 0x50,
	0xab, 0x29, 0x03, 0x7b, 0x65, 0x6a, 0xdf, 0x95, 0x4e, 0xfd, 0x72, 0xc2, 0xc0, 0xc6, 0xac, 0x2c,
	0x27, 0x89, 0x1f, 0x01, 0x38, 0x3c, 0xe8, 0x24, 0x45, 0x31, 0xcb, 0xf7, 0x63, 0x64, 0x52, 0x4a,
	0x88, 0x8b, 0xc2, 0xf8, 0x13, 0x78, 0x3f, 0xc5, 0xc0, 0xc9, 0x90, 0xfa, 0xfe, 0x19, 0xbb, 0xa2,
	0x6f, 0xe3, 0x75, 0x16, 0x1b, 0x90, 0xbc, 0x88, 0xd8, 0x2a, 0x14, 0xe0, 0x1f, 0xb2, 0xf0, 0xec,
	0xc0, 0x30, 0xd2, 0x6e, 0xad, 0xeb, 0xbc, 0x8d, 0x1a, 0x2c, 0x15, 0xc8, 0xd3, 0xe2, 0xc9, 0x4d,
	0x9a, 0x11, 0xcf, 0x92, 0x06, 0x1a, 0xa6, 0x2a, 0x57, 0xd4, 0x0e, 0xa3, 0x0a, 0x20, 0xa8, 0xcb,
	0x21, 0x3c, 0xc8, 0x8d, 0xed, 0x2c, 0x34, 0x31, 0x9e, 0x47, 0x85, 0xfb, 0x6a, 0x1a, 0x64, 0x0f,
	0xa4, 0xc4, 0x12, 0x42, 0x2c, 0xeb, 0xbb, 0xb9, 0xbd, 0x92, 0x52, 0x1d, 0xaf, 0xc3, 0x71, 0xd3,
	0x19, 0x6b, 0x21, 0x9d, 0xb1, 0xca, 0xff, 0x94, 0x81, 0xe7, 0x77, 0x0a, 0x68, 0xd9, 0x53, 0xca,
	0x33, 0xfb, 0xd2, 0x09, 0x85, 0xb4, 0x3b, 0x29, 0xa4, 0x31, 0x99, 0x0b, 0xd7, 0xd0, 0x02, 0xaa,
	0x20, 0x36, 0xf9, 0x24, 0x14, 0x6d, 0x6e, 0x6e, 0x22, 0x23, 0x94, 0x19, 0xb1, 0xe4, 0x6f, 0xe0,
	0xe1, 0xb9, 0x73, 0x4d, 0x2f, 0xdc, 0x34, 0xd3, 0xcb, 0x9c, 0x65, 0x32, 0x5f, 0xcb, 0xa6, 0xf3,
	0xb5, 0xbb, 0x4f, 0x4e, 0xfe, 0x06, 0x6a, 0xb3, 0x89, 0xaf, 0x40, 0x4e, 0x7c, 0xe7, 0x0a, 0xb5,
	0x9c, 0x6b, 0xfa, 0x1d, 0xed, 0x7c, 0x36, 0xf1, 0x55, 0xec, 0xfc, 0x16, 0xde, 0xc3, 0xfb, 0x59,
	0x82, 0x36, 0x5b, 0x6e, 0xe3, 0x89, 0xfb, 0x6f, 0x36, 0x75, 0xff, 0x5d, 0xb0, 0xed, 0x5f, 0xc0,
	0xc3, 0x99, 0x94, 0x57, 0xb1, 0xeb, 0x3f, 0x86, 0xad, 0x94, 0x13, 0x6d, 0xda, 0x97, 0xce, 0xdb,
	0x3b, 0xe9, 0xec, 0xe4, 0xd6, 0xfe, 0x23, 0x03, 0xdb, 0x33, 0xd6, 0x5f, 0x85, 0xc5, 0x7f, 0x1f,
	0xb6, 0x98, 0x1d, 0x50, 0x4f, 0xd3, 0x79, 0x22, 0xac, 0xfa, 0x03, 0xe7, 0x46, 0xd5, 0x4c, 0x51,
	0xc0, 0x28, 0x2a, 0x24, 0x31, 0xd7, 0x19, 0x38, 0x37, 0x07, 0xa6, 0x49, 0x3e, 0x83, 0x6d, 0x7e,
	0xeb, 0x51, 0x3d, 0x6a, 0x69, 0x8c, 0xfb, 0xbb, 0xfe, 0x20, 0x50, 0xed, 0xa1, 0x15, 0xc6, 0x1e,
	0xc2, 0x27, 0x15, 0x9c, 0x3b, 0xe3, 0x53, 0xad, 0xa1, 0x25, 0xff, 0x26, 0x03, 0xdb, 0x5f, 0x38,
	0xdf, 0x46, 0x69, 0xee, 0x16, 0x60, 0x52, 0xa7, 0x72, 0x29, 0x9d, 0x7a, 0x0e, 0x65, 0xe6, 0xab,
	0x3e, 0x33, 0xa9, 0x1d, 0x98, 0xe2, 0x82, 0x56, 0x54, 0x80, 0xf9, 0x9d, 0x10, 0x22, 0x8f, 0xe0,
	0xbd, 0x59, 0x9c, 0xad, 0x42, 0xa9, 0x6e, 0x60, 0xfb, 0xcb, 0x21, 0x0b, 0x56, 0x2e, 0x14, 0xbe,
	0xe7, 0x59, 0x84, 0x57, 0xb1, 0xe7, 0x5f, 0x67, 0xa1, 0x26, 0x00, 0x09, 0xea, 0xba, 0xe3, 0xd1,
	0x7b, 0xdc, 0xf7, 0x16, 0xac, 0x61, 0x95, 0x21, 0x74, 0x21, 0x62, 0x90, 0x94, 0x46, 0x3e, 0xa5,
	0x22, 0x35, 0x28, 0xb8, 0x1e, 0xbd, 0xa4, 0x7a, 0x10, 0x06, 0xf7, 0x68, 0xc8, 0x5d, 0xb4, 0xa9,
	0xf9, 0x81, 0x6a, 0x7a, 0x3a, 0xde, 0x7e, 0x2b, 0x4a, 0x81, 0x8f, 0xcf, 0x3c, 0x9d, 0x07, 0x7d,
	0xe6, 0xab, 0x8e, 0xc7, 0xfa, 0xcc, 0xd6, 0x4c, 0x15, 0x83, 0xa6, 0x88, 0xe8, 0x55, 0xe6, 0xb7,
	0x43, 0x30, 0x96, 0xfb, 0x1e, 0x43, 0xc9, 0xd2, 0x6e, 0x55, 0xc1, 0x51, 0x51, 0xdc, 0x4c, 0x2d,
	0xed, 0x16, 0x37, 0x2d, 0xf7, 0xe0, 0xd1, 0x1c, 0x69, 0x2c, 0x77, 0x18, 0x3b, 0x3c, 0x98, 0x04,
	0xd4, 0x8b, 0x2a, 0x93, 0x15, 0x25, 0x1e, 0xcb, 0x1e, 0x6c, 0x77, 0x6e, 0x58, 0xa0, 0x0f, 0xc6,
	0x34, 0x0e, 0x4f, 0xee, 0x51, 0xdc, 0x04, 0xf2, 0x8e, 0x4b, 0xed, 0xd0, 0x63, 0xe0, 0xb3, 0x5c,
	0x87, 0xf7, 0x66, 0xd1, 0x5c, 0x6a, 0x53, 0xf2, 0x1f, 0x00, 0x49, 0x39, 0xc5, 0xa5, 0xb8, 0x96,
	0xbb, 0xf0, 0xee, 0xd4, 0xdb, 0xcb, 0x09, 0x35, 0xda, 0x58, 0x36, 0xb1, 0xb1, 0x7f, 0xce, 0xc0,
	0x76, 0xe2, 0x32, 0xaa, 0xd9, 0x46, 0xdd, 0xd4, 0xdc, 0x7b, 0x94, 0xe6, 0x6b, 0xd8, 0xba, 0xf4,
	0x1c, 0x4b, 0x45, 0x17, 0x6b, 0x31, 0x5d, 0xf5, 0x03, 0x2d, 0x18, 0xfa, 0x28, 0xdd, 0xea, 0x8b,
	0xdf, 0x9d, 0x34, 0xb3, 0x88, 0x83, 0x0b, 0x9f, 0x7a, 0xe7, 0x4c, 0xef, 0x20, 0xb2, 0xf2, 0x80,
	0x2f, 0x91, 0x02, 0xf1, 0x13, 0x99, 0xc5, 0xf7, 0x72, 0x27, 0x72, 0x0d, 0x5b, 0x0d, 0xdb, 0x58,
	0xbd, 0xc3, 0xba, 0x85, 0xed, 0x19, 0x74, 0x57, 0xe1, 0xaf, 0x46, 0xf0, 0xee, 0x29, 0xd3, 0xaf,
	0xda, 0xc3, 0xe0, 0xb4, 0xfb, 0xfa, 0x9c, 0x5a, 0x3d, 0x1e, 0xdb, 0xee, 0x6d, 0xc3, 0x8f, 0xa1,
	0x74, 0xcd, 0xf4, 0x80, 0x59, 0xe3, 0x84, 0xa7, 0x28, 0x00, 0x4d, 0x43, 0xbe, 0x81, 0xad, 0x69,
	0xd2, 0xab, 0xd8, 0xf3, 0x35, 0x6c, 0xd5, 0x87, 0xc1, 0xdb, 0x67, 0xb6, 0xdf, 0xe2, 0x94, 0x67,
	0xd0, 0x5d, 0xc5, 0x8e, 0x7f, 0x0e, 0x4f, 0xf9, 0x1d, 0x6b, 0xba, 0xe6, 0xd6, 0x1d, 0xb9, 0x74,
	0xc9, 0xdc, 0x36, 0xaa, 0xc5, 0x88, 0x9c, 0x7e, 0x3d, 0x10, 0x35, 0x98, 0xbf, 0xcf, 0xc0, 0xb3,
	0xbb, 0x48, 0x2c, 0xb7, 0xcb, 0x16, 0xbc, 0x3b, 0x51, 0xfc, 0x49, 0x5c, 0xc6, 0x9f, 0xcf, 0xb9,
	0x10, 0xc7, 0xc4, 0xa4, 0x64, 0x7d, 0x08, 0x2f, 0xe7, 0x7f, 0x97, 0xc5, 0x56, 0xc0, 0xdb, 0xb4,
	0x19, 0xe2, 0x60, 0x9b, 0x4d, 0x06, 0x5b, 0x02, 0x79, 0x9e, 0x00, 0x46, 0x31, 0x81, 0x3f, 0x63,
	0x9c, 0x35, 0xb5, 0x11, 0xb3, 0xfb, 0x61, 0x1a, 0x16, 0x0d, 0x79, 0x9c, 0xed, 0x51, 0x3f, 0x50,
	0x87, 0xf1, 0xfd, 0xba, 0xc0, 0xc7, 0x17, 0xcc, 0x20, 0x9f, 0x00, 0xf1, 0x5d, 0xaa, 0x33, 0xcd,
	0x54, 0xe9, 0x35, 0xb5, 0x03, 0xf5, 0xd2, 0xd4, 0x44, 0x29, 0xba, 0xa8, 0x48, 0xe1, 0x4c, 0x83,
	0x4f, 0x1c, 0x9b, 0x5a, 0x9f, 0xa7, 0xa6, 0xcc, 0x47, 0xc4, 0x99, 0xa1, 0x99, 0x30, 0x9f, 0xe3,
	0xa6, 0xc2, 0xf3, 0x44, 0x82, 0x58, 0x9c, 0x4a, 0x10, 0xff, 0xa7, 0x0a, 0xd2, 0xa4, 0xda, 0x4c,
	0xa8, 0x78, 0x66, 0x52, 0xc5, 0xdb, 0xb0, 0x1e, 0xba, 0xed, 0x2c, 0xba, 0xed, 0x1f, 0x2d, 0xd2,
	0xc3, 0x29, 0x40, 0xe8, 0xc8, 0xc3, 0x65, 0xd0, 0x66, 0x58, 0xda, 0x66, 0x58, 0xdc, 0x33, 0x64,
	0x51, 0xcf, 0x30, 0xac, 0x89, 0x73, 0x00, 0xf6, 0x0e, 0x1e, 0x01, 0x3e, 0x27, 0xe5, 0xca, 0xc7,
	0x5c, 0xae, 0x13, 0xed, 0xc4, 0xf5, 0xa9, 0x76, 0xe2, 0x44, 0x61, 0xa4, 0x30, 0x55, 0x18, 0x49,
	0xf6, 0x0c, 0xc3, 0xb4, 0x26, 0xee, 0x19, 0x7e, 0x9e, 0x6e, 0xea, 0x94, 0xe6, 0x56, 0x5a, 0xa7,
	0xdb, 0x39, 0xe4, 0x43, 0xd8, 0xc4, 0x9e, 0x04, 0xea, 0xb6, 0xee, 0x0c, 0xed, 0x20, 0xec, 0xfd,
	0x55, 0xdc, 0xb0, 0x14, 0x52, 0xe7, 0x40, 0xb2, 0x0b, 0x1b, 0x36, 0xbd, 0x0d, 0xd4, 0x48, 0x2e,
	0x65, 0xc1, 0x21, 0x87, 0x75, 0x84, 0x6c, 0x3e, 0x80, 0xea, 0x18, 0x03, 0xb7, 0xb9, 0x81, 0x38,
	0x1b, 0x11, 0x0e, 0x6e, 0x54, 0x86, 0xca, 0x18, 0x8b, 0x4b, 0xaa, 0x82, 0xd4, 0xca, 0x11, 0x12,
	0x97, 0xd6, 0x1e, 0x48, 0x31, 0x4e, 0x24, 0xb2, 0x2a, 0xae, 0x55, 0x8d, 0xd0, 0x42, 0xb1, 0x1d,
	0x43, 0x9e, 0xdb, 0x63, 0x6d, 0x13, 0xcf, 0xfd, 0xc5, 0xc2, 0x73, 0x7f, 0x25, 0x4c, 0x40, 0x8c,
	0xd0, 0x3a, 0xf1, 0x7d, 0x7e, 0xe0, 0xbd, 0xbe, 0x8a, 0xe9, 0x87, 0x84, 0x2a, 0xb9, 0xde, 0xeb,
	0xb7, 0x5d, 0x8a, 0x6d, 0xd1, 0x21, 0x22, 0xab, 0x01, 0xb3, 0x68, 0xed, 0x01, 0x7a, 0x19, 0x10,
	0xa0, 0x2e, 0xb3, 0x28, 0x79, 0x02, 0xa5, 0x4e, 0xa0, 0x79, 0x01, 0x1f, 0xd4, 0x08, 0x4e, 0x8f,
	0x01, 0x44, 0x83, 0x07, 0x69, 0x7b, 0xb2, 0x34, 0xb7, 0xf6, 0x2e, 0x9e, 0xcf, 0x0f, 0x17, 0x32,
	0xdb, 0x49, 0xd8, 0xdb, 0xb9, 0xe6, 0x36, 0xec, 0xc0, 0x1b, 0x29, 0x9b, 0x7e, 0x1a, 0x4a, 0x7e,
	0x95, 0x81, 0x67, 0x69, 0x1a, 0x9a, 0xeb, 0x52, 0xdb, 0x10, 0x29, 0x30, 0x12, 0x7c, 0x0f, 0x09,
	0xfe, 0x74, 0x29, 0x82, 0x07, 0xb8, 0x08, 0x66, 0xc7, 0x31, 0xed, 0x1d, 0x7f, 0x2e, 0x42, 0x3a,
	0xef, 0xde, 0x4e, 0xe7, 0xdd, 0x33, 0xd3, 0xf7, 0x87, 0x33, 0xd3, 0xf7, 0xb8, 0xb7, 0x57, 0x7b,
	0xa3, 0xde, 0x1e, 0x57, 0x27, 0xe6, 0xab, 0xae, 0x73, 0x43, 0x3d, 0xb1, 0xec, 0x23, 0x5c, 0xb6,
	0xcc, 0xfc, 0x57, 0x1c, 0x86, 0x6b, 0x7e, 0x06, 0xdb, 0xbd, 0xa1, 0xc7, 0x2f, 0x16, 0x78, 0x77,
	0xc6, 0x54, 0x0f, 0x0d, 0x65, 0x67, 0x37, 0xc7, 0x6f, 0xd0, 0x38, 0x89, 0x97, 0x67, 0xee, 0x68,
	0xd1, 0x2a, 0x9e, 0x01, 0x28, 0x5a, 0x10, 0xea, 0x6c, 0xed, 0xb1, 0xd0, 0xf5, 0x31, 0x24, 0x5d,
	0x5a, 0x7c, 0x32, 0xd1, 0x0c, 0x9f, 0xea, 0xb7, 0x3f, 0x5d, 0xdc, 0x6f, 0x7f, 0x36, 0xd5, 0x6f,
	0xff, 0x00, 0xaa, 0x03, 0xcd, 0x36, 0x54, 0xdd, 0xd4, 0x5c, 0xc1, 0xee, 0x73, 0x64, 0x77, 0x63,
	0x10, 0x66, 0x93, 0xc8, 0xe8, 0x4f, 0x92, 0x58, 0x18, 0x46, 0x76, 0xe7, 0x96, 0x88, 0x31, 0x8c,
	0xc4, 0xaf, 0x63, 0x47, 0x3d, 0xd5, 0xb7, 0x7f, 0x7f, 0x51, 0xdf, 0x5e, 0x9e, 0xee, 0xdb, 0xef,
	0x1c, 0xc2, 0xd6, 0x2c, 0x3d, 0x25, 0x12, 0xe4, 0xae, 0xe8, 0x28, 0xea, 0xd5, 0x5f, 0xd1, 0x11,
	0x0f, 0x5d, 0xd7, 0x9a, 0x39, 0x8c, 0x43, 0x17, 0x0e, 0x3e, 0xcf, 0xfe, 0x38, 0xb3, 0x73, 0x0e,
	0xcf, 0x17, 0xa8, 0xde, 0x32, 0xcb, 0xc9, 0x17, 0xc9, 0xdc, 0x3b, 0xe9, 0xdf, 0x49, 0x19, 0x0a,
	0xad, 0x76, 0x5b, 0x39, 0x6a, 0x28, 0xd2, 0x3b, 0x64, 0x03, 0x8a, 0x47, 0xed, 0xaf, 0x5a, 0x67,
	0xed, 0x83, 0x23, 0x29, 0xc3, 0xa7, 0x5e, 0x9d, 0x1d, 0xfc, 0xac, 0xd9, 0x3a, 0x91, 0xb2, 0xa4,
	0x00, 0xb9, 0x46, 0xeb, 0x48, 0xca, 0x11, 0x80, 0x75, 0xa5, 0xd1, 0xb9, 0x38, 0xeb, 0x4a, 0x79,
	0xf9, 0xdf, 0x33, 0xf0, 0x60, 0xca, 0x7f, 0x70, 0x8c, 0x56, 0x5b, 0x39, 0x3f, 0x38, 0x93, 0xde,
	0x21, 0x45, 0xc8, 0x7f, 0xd1, 0x6e, 0xb6, 0xa4, 0x0c, 0x7f, 0xfa, 0xf2, 0xa2, 0xd9, 0x95, 0xb2,
	0xfc, 0xe9, 0xac, 0xdd, 0xe9, 0x4a, 0x39, 0xb2, 0x09, 0xe5, 0xfa, 0xcb, 0x83, 0xd6, 0x49, 0x43,
	0x3d, 0x3f, 0x68, 0xb6, 0xa4, 0x7c, 0x02, 0xd0, 0x69, 0xb7, 0x4e, 0xa4, 0xb5, 0x04, 0x40, 0x39,
	0x68, 0x9d, 0x4a, 0xeb, 0x84, 0x40, 0xf5, 0x55, 0x43, 0x39, 0x6e, 0xd4, 0xbb, 0x6a, 0xa3, 0x75,
	0xc4, 0x79, 0x2b, 0x90, 0x07, 0x50, 0x39, 0x6e, 0x2a, 0x9d, 0xae, 0x1a, 0xce, 0x48, 0xc5, 0x24,
	0x5a, 0xfd, 0x65, 0x5b, 0xb9, 0xe8, 0x48, 0x25, 0xbe, 0xbb, 0x7a, 0xbb, 0x7d, 0xa6, 0x1e, 0x37,
	0xbb, 0x12, 0x90, 0x2d, 0x90, 0x8e, 0xcf, 0x7e, 0xa6, 0x76, 0x5f, 0x36, 0xd4, 0x83, 0x8b, 0xa3,
	0x66, 0xa3, 0x55, 0x6f, 0x48, 0x65, 0xbe, 0xcd, 0x83, 0x7a, 0x43, 0xda, 0xe0, 0xe9, 0x12, 0x99,
	0xbe, 0xae, 0x90, 0x1f, 0x40, 0x29, 0xbe, 0x1c, 0x2d, 0x4a, 0x4f, 0x8a, 0xd1, 0x1d, 0x88, 0x7c,
	0x1f, 0x0a, 0x81, 0x23, 0xde, 0xc9, 0xde, 0xfd, 0xce, 0x7a, 0xe0, 0xe0, 0x1b, 0x0b, 0x2a, 0x91,
	0xff, 0x99, 0x81, 0x6a, 0x54, 0x97, 0x0f, 0x13, 0x82, 0xe9, 0x2f, 0x41, 0x3e, 0x89, 0xbb, 0x14,
	0xb9, 0xc5, 0xa5, 0xf4, 0x54, 0x35, 0x38, 0xb7, 0x9b, 0x4b, 0x56, 0x83, 0xbf, 0x18, 0x7b, 0x7c,
	0x1e, 0x59, 0xf2, 0x18, 0x59, 0x3e, 0x9a, 0x5c, 0x2f, 0xcd, 0xcf, 0x7e, 0x22, 0xa0, 0x44, 0xc1,
	0x61, 0xe4, 0x52, 0xf9, 0x43, 0x80, 0x84, 0xaa, 0x70, 0x71, 0x1f, 0x1d, 0x49, 0xef, 0x90, 0x75,
	0xc8, 0x5e, 0xbc, 0x92, 0x32, 0x1c, 0x70, 0xd4, 0x38, 0x93, 0xb2, 0xf2, 0x09, 0x76, 0x3b, 0x93,
	0x36, 0x50, 0x77, 0xdc, 0xd1, 0x57, 0x1e, 0x0b, 0x96, 0xbb, 0xf1, 0xc9, 0xbf, 0xcd, 0x62, 0xef,
	0x71, 0xee, 0x4a, 0xcb, 0xe5, 0xbd, 0x7f, 0x33, 0x15, 0x5a, 0x74, 0xc7, 0x1d, 0xa9, 0x37, 0x62,
	0x35, 0x0c, 0x2d, 0x42, 0xdc, 0xad, 0x49, 0xf1, 0xdc, 0xcd, 0xc3, 0xfe, 0x9c, 0xb9, 0xd9, 0x81,
	0x26, 0x8d, 0x30, 0xe9, 0x2c, 0x66, 0xbc, 0xbe, 0xc8, 0x59, 0x94, 0x92, 0xce, 0x62, 0x30, 0xd1,
	0xf0, 0x8e, 0x52, 0xf7, 0xc3, 0x51, 0xd3, 0x58, 0xe6, 0x2a, 0x32, 0xd9, 0xb8, 0xca, 0x4e, 0x36,
	0xae, 0xe4, 0x3f, 0x9f, 0xec, 0x69, 0xa7, 0x49, 0xad, 0xa2, 0x2b, 0xf8, 0x73, 0x78, 0x98, 0xa8,
	0xd0, 0xc4, 0x61, 0xef, 0x1e, 0x4b, 0xeb, 0x0d, 0xa8, 0xcd, 0xa6, 0xb0, 0x5c, 0xe5, 0x63, 0x94,
	0x2a, 0xfb, 0xb0, 0xfe, 0xe0, 0x98, 0x5d, 0xdf, 0x67, 0xcd, 0xf2, 0x11, 0x14, 0x87, 0xcc, 0x18,
	0x7f, 0x03, 0x55, 0x51, 0x0a, 0x43, 0x26, 0x3e, 0x3d, 0x48, 0x57, 0x6e, 0x62, 0xd2, 0xcb, 0xf1,
	0xff, 0x05, 0xec, 0x26, 0x5b, 0xc0, 0xcc, 0xa0, 0x22, 0x37, 0x78, 0x8b, 0x8e, 0xb3, 0xfc, 0xab,
	0x0c, 0xbc, 0xbf, 0x60, 0xb1, 0x55, 0xe8, 0xce, 0x65, 0x92, 0x8b, 0x43, 0x2d, 0xd0, 0x07, 0x1c,
	0x41, 0x70, 0xd2, 0xb4, 0x2f, 0x1d, 0x7f, 0xc9, 0x6e, 0x5c, 0x58, 0x94, 0xf0, 0x91, 0x83, 0x92,
	0x52, 0x10, 0x55, 0x09, 0x5f, 0xfe, 0xdb, 0x1c, 0xc8, 0x8b, 0x08, 0x2d, 0xb7, 0x5f, 0x0b, 0x36,
	0x45, 0xa6, 0xa6, 0x32, 0xfb, 0xd2, 0x49, 0xb8, 0xad, 0xc6, 0xfc, 0x8c, 0x78, 0x1e, 0xdd, 0xfd,
	0xf1, 0x38, 0xf6, 0x56, 0x15, 0x3d, 0x09, 0xdb, 0xf9, 0x25, 0x90, 0x69, 0xa4, 0xa4, 0x4f, 0x2a,
	0x09, 0x9f, 0xd4, 0x49, 0xfa, 0xa4, 0xf2, 0x8b, 0x9f, 0x7c, 0x2b, 0x66, 0x92, 0xe9, 0x94, 0x02,
	0x30, 0x9e, 0x98, 0xce, 0x46, 0x33, 0x8b, 0xb3, 0xd1, 0xec, 0x64, 0x36, 0x2a, 0xff, 0x26, 0x03,
	0x8f, 0x12, 0x9e, 0xeb, 0x8a, 0xb9, 0xdd, 0x01, 0x6d, 0xda, 0x81, 0xe7, 0xac, 0xa2, 0xad, 0xc4,
	0xaf, 0xdd, 0x57, 0xcc, 0x15, 0x77, 0x30, 0xd1, 0xf7, 0x2a, 0x72, 0x00, 0xbf, 0x63, 0xc9, 0x27,
	0xb0, 0x33, 0x8f, 0xb3, 0xe5, 0x8c, 0xf6, 0xcf, 0x92, 0x0b, 0x1d, 0x3b, 0xa6, 0xe9, 0xdc, 0x74,
	0x3d, 0xd6, 0xef, 0xdf, 0x6b, 0x0d, 0xf2, 0x0e, 0xcf, 0xf3, 0x12, 0x1e, 0xcf, 0xa5, 0xbf, 0xdc,
	0x4e, 0xfe, 0x10, 0x9e, 0x24, 0xb4, 0xc7, 0x71, 0x82, 0x43, 0x4d, 0xbf, 0x6a, 0x5f, 0x5e, 0xbe,
	0x1a, 0xfa, 0x83, 0x73, 0xbf, 0x4f, 0x7e, 0x0c, 0xe5, 0x4b, 0x5c, 0xff, 0x8d, 0x52, 0x3b, 0x10,
	0xb8, 0xfc, 0x59, 0xfe, 0xeb, 0x4c, 0xd2, 0x3d, 0x7e, 0x39, 0xa4, 0xde, 0xe8, 0x9e, 0xeb, 0x95,
	0x35, 0x28, 0x5c, 0xd1, 0xd1, 0x8d, 0xe3, 0x45, 0x4a, 0x10, 0x0d, 0xe3, 0xef, 0x91, 0xf2, 0xe3,
	0xef, 0x91, 0xe4, 0xff, 0x2e, 0x24, 0x43, 0x5a, 0x82, 0x9f, 0xa5, 0x7b, 0x0f, 0xba, 0x63, 0xc4,
	0x9f, 0x3a, 0xf1, 0x67, 0xce, 0x88, 0x45, 0x7d, 0x9f, 0x53, 0x0c, 0x19, 0x09, 0x87, 0xe4, 0x10,
	0xd6, 0xb8, 0x62, 0xfa, 0xb5, 0x3c, 0xba, 0x91, 0x4f, 0xe6, 0x5b, 0x6e, 0x8a, 0xa1, 0x7d, 0x7c,
	0x10, 0xaf, 0xee, 0xfc, 0x6f, 0x1e, 0xf2, 0xdf, 0xe2, 0xfb, 0xf4, 0xb0, 0x10, 0x45, 0xbd, 0xb1,
	0xb1, 0x14, 0x05, 0xa0, 0x39, 0x55, 0x6d, 0xca, 0xcf, 0xfa, 0x78, 0x3d, 0x7a, 0x3b, 0xfe, 0x3c,
	0x3d, 0x5c, 0xaf, 0x69, 0x61, 0x76, 0x1c, 0x7e, 0xbc, 0x6e, 0x84, 0xa5, 0xaa, 0x82, 0xf8, 0x76,
	0xdd, 0x98, 0xf8, 0xec, 0xbd, 0x70, 0xe7, 0x67, 0xef, 0xc5, 0x89, 0xcf, 0xde, 0x3f, 0x80, 0x6a,
	0x3c, 0xa9, 0x5a, 0xcc, 0x66, 0xf8, 0x21, 0x7a, 0x49, 0xd9, 0x88, 0x30, 0xce, 0x99, 0xcd, 0xc8,
	0x87, 0xb0, 0x39, 0xc6, 0xf2, 0x2d, 0xcd, 0x34, 0xb1, 0x20, 0x55, 0x52, 0x2a, 0x11, 0x5a, 0x87,
	0x03, 0xc9, 0x1e, 0x48, 0x89, 0xd5, 0xa8, 0xc1, 0x86, 0x56, 0x58, 0x94, 0xaa, 0xc6, 0xeb, 0x21,
	0x94, 0xb4, 0x93, 0xdf, 0xce, 0x6f, 0xa0, 0x1e, 0x7c, 0xf6, 0xa6, 0x47, 0x36, 0xf3, 0x83, 0xfa,
	0xd4, 0xad, 0xb9, 0xb2, 0xe8, 0xd6, 0x5c, 0x9d, 0xf1, 0xb5, 0xfb, 0xc7, 0xf0, 0x60, 0xa0, 0xf9,
	0xaa, 0x3f, 0x70, 0xbc, 0x40, 0x0d, 0xdd, 0x2c, 0x16, 0xb1, 0xd6, 0x94, 0xcd, 0x81, 0xe6, 0x77,
	0x38, 0xbc, 0x23, 0xc0, 0xa4, 0x03, 0xe5, 0x44, 0xf8, 0xc2, 0xfa, 0x54, 0xf9, 0xae, 0x52, 0xd7,
	0xc4, 0x06, 0xc6, 0x21, 0x02, 0xc6, 0x71, 0x6a, 0xa7, 0x07, 0xa5, 0xbb, 0xfe, 0xc5, 0xa8, 0x24,
	0xff, 0xc5, 0x78, 0x02, 0xa5, 0xf8, 0xbf, 0x8b, 0xc8, 0x5e, 0x63, 0xc0, 0xe4, 0x5f, 0x18, 0x95,
	0xf8, 0x2f, 0x8c, 0xff, 0x8f, 0x38, 0xf4, 0xf1, 0x37, 0xb0, 0x3d, 0xb3, 0x07, 0x47, 0x76, 0xe1,
	0xc9, 0xcc, 0x09, 0xf5, 0xa2, 0x75, 0xda, 0x6a, 0x7f, 0x25, 0xbd, 0x33, 0x1f, 0xa3, 0xdd, 0x52,
	0xcf, 0x9b, 0x75, 0x29, 0x43, 0x9e, 0xc1, 0xce, 0x6c, 0x8c, 0xf3, 0x66, 0xa7, 0x23, 0x65, 0x0f,
	0x8f, 0xa0, 0xa6, 0x3b, 0xd6, 0xfe, 0x88, 0x8d, 0x9c, 0x21, 0x97, 0xbf, 0xe5, 0x18, 0xd4, 0x14,
	0xff, 0xb7, 0xfc, 0xd1, 0x5e, 0xdf, 0x31, 0x35, 0xbb, 0xbf, 0xff, 0xc3, 0x17, 0x41, 0xb0, 0xaf,
	0x3b, 0xd6, 0xa7, 0x08, 0xd6, 0x1d, 0xf3, 0x53, 0xcd, 0x75, 0x3f, 0x4d, 0xfc, 0x35, 0xd3, 0x5b,
	0xc7, 0x99, 0xdf, 0xff, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xc0, 0x5f, 0x8a, 0x3e, 0x4b, 0x33,
	0x00, 0x00,
}
