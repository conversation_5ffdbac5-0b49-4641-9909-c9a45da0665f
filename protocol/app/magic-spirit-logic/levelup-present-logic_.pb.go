// Code generated by protoc-gen-go. DO NOT EDIT.
// source: levelup-present-logic_.proto

package magic_spirit_logic // import "golang.52tt.com/protocol/app/magic-spirit-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type LevelupPresentBatchData struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	BatchCount           uint32   `protobuf:"varint,2,opt,name=batch_count,json=batchCount,proto3" json:"batch_count,omitempty"`
	EffectUrl            string   `protobuf:"bytes,3,opt,name=effect_url,json=effectUrl,proto3" json:"effect_url,omitempty"`
	EffectDesc           string   `protobuf:"bytes,4,opt,name=effect_desc,json=effectDesc,proto3" json:"effect_desc,omitempty"`
	EffectDoc            string   `protobuf:"bytes,5,opt,name=effect_doc,json=effectDoc,proto3" json:"effect_doc,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelupPresentBatchData) Reset()         { *m = LevelupPresentBatchData{} }
func (m *LevelupPresentBatchData) String() string { return proto.CompactTextString(m) }
func (*LevelupPresentBatchData) ProtoMessage()    {}
func (*LevelupPresentBatchData) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_logic__4b736df73634ddfb, []int{0}
}
func (m *LevelupPresentBatchData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupPresentBatchData.Unmarshal(m, b)
}
func (m *LevelupPresentBatchData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupPresentBatchData.Marshal(b, m, deterministic)
}
func (dst *LevelupPresentBatchData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupPresentBatchData.Merge(dst, src)
}
func (m *LevelupPresentBatchData) XXX_Size() int {
	return xxx_messageInfo_LevelupPresentBatchData.Size(m)
}
func (m *LevelupPresentBatchData) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupPresentBatchData.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupPresentBatchData proto.InternalMessageInfo

func (m *LevelupPresentBatchData) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LevelupPresentBatchData) GetBatchCount() uint32 {
	if m != nil {
		return m.BatchCount
	}
	return 0
}

func (m *LevelupPresentBatchData) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

func (m *LevelupPresentBatchData) GetEffectDesc() string {
	if m != nil {
		return m.EffectDesc
	}
	return ""
}

func (m *LevelupPresentBatchData) GetEffectDoc() string {
	if m != nil {
		return m.EffectDoc
	}
	return ""
}

func (m *LevelupPresentBatchData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *LevelupPresentBatchData) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type LevelupPresentData struct {
	ItemId               uint32                     `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Level                uint32                     `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Exp                  uint32                     `protobuf:"varint,3,opt,name=exp,proto3" json:"exp,omitempty"`
	BatchList            []*LevelupPresentBatchData `protobuf:"bytes,4,rep,name=batch_list,json=batchList,proto3" json:"batch_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *LevelupPresentData) Reset()         { *m = LevelupPresentData{} }
func (m *LevelupPresentData) String() string { return proto.CompactTextString(m) }
func (*LevelupPresentData) ProtoMessage()    {}
func (*LevelupPresentData) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_logic__4b736df73634ddfb, []int{1}
}
func (m *LevelupPresentData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupPresentData.Unmarshal(m, b)
}
func (m *LevelupPresentData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupPresentData.Marshal(b, m, deterministic)
}
func (dst *LevelupPresentData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupPresentData.Merge(dst, src)
}
func (m *LevelupPresentData) XXX_Size() int {
	return xxx_messageInfo_LevelupPresentData.Size(m)
}
func (m *LevelupPresentData) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupPresentData.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupPresentData proto.InternalMessageInfo

func (m *LevelupPresentData) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LevelupPresentData) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelupPresentData) GetExp() uint32 {
	if m != nil {
		return m.Exp
	}
	return 0
}

func (m *LevelupPresentData) GetBatchList() []*LevelupPresentBatchData {
	if m != nil {
		return m.BatchList
	}
	return nil
}

type LevelupPresentParentData struct {
	ParentItemId         uint32                `protobuf:"varint,1,opt,name=parent_item_id,json=parentItemId,proto3" json:"parent_item_id,omitempty"`
	PresentType          uint32                `protobuf:"varint,2,opt,name=present_type,json=presentType,proto3" json:"present_type,omitempty"`
	CurrentVersion       uint32                `protobuf:"varint,3,opt,name=current_version,json=currentVersion,proto3" json:"current_version,omitempty"`
	TipsTemplate         string                `protobuf:"bytes,4,opt,name=tips_template,json=tipsTemplate,proto3" json:"tips_template,omitempty"`
	CreateTime           uint32                `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32                `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	LevelList            []*LevelupPresentData `protobuf:"bytes,7,rep,name=level_list,json=levelList,proto3" json:"level_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *LevelupPresentParentData) Reset()         { *m = LevelupPresentParentData{} }
func (m *LevelupPresentParentData) String() string { return proto.CompactTextString(m) }
func (*LevelupPresentParentData) ProtoMessage()    {}
func (*LevelupPresentParentData) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_logic__4b736df73634ddfb, []int{2}
}
func (m *LevelupPresentParentData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupPresentParentData.Unmarshal(m, b)
}
func (m *LevelupPresentParentData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupPresentParentData.Marshal(b, m, deterministic)
}
func (dst *LevelupPresentParentData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupPresentParentData.Merge(dst, src)
}
func (m *LevelupPresentParentData) XXX_Size() int {
	return xxx_messageInfo_LevelupPresentParentData.Size(m)
}
func (m *LevelupPresentParentData) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupPresentParentData.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupPresentParentData proto.InternalMessageInfo

func (m *LevelupPresentParentData) GetParentItemId() uint32 {
	if m != nil {
		return m.ParentItemId
	}
	return 0
}

func (m *LevelupPresentParentData) GetPresentType() uint32 {
	if m != nil {
		return m.PresentType
	}
	return 0
}

func (m *LevelupPresentParentData) GetCurrentVersion() uint32 {
	if m != nil {
		return m.CurrentVersion
	}
	return 0
}

func (m *LevelupPresentParentData) GetTipsTemplate() string {
	if m != nil {
		return m.TipsTemplate
	}
	return ""
}

func (m *LevelupPresentParentData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *LevelupPresentParentData) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *LevelupPresentParentData) GetLevelList() []*LevelupPresentData {
	if m != nil {
		return m.LevelList
	}
	return nil
}

type LevelupPresentParentMap struct {
	BaseReq              *app.BaseReq                         `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Map                  map[uint32]*LevelupPresentParentData `protobuf:"bytes,2,rep,name=map,proto3" json:"map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *LevelupPresentParentMap) Reset()         { *m = LevelupPresentParentMap{} }
func (m *LevelupPresentParentMap) String() string { return proto.CompactTextString(m) }
func (*LevelupPresentParentMap) ProtoMessage()    {}
func (*LevelupPresentParentMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_logic__4b736df73634ddfb, []int{3}
}
func (m *LevelupPresentParentMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelupPresentParentMap.Unmarshal(m, b)
}
func (m *LevelupPresentParentMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelupPresentParentMap.Marshal(b, m, deterministic)
}
func (dst *LevelupPresentParentMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelupPresentParentMap.Merge(dst, src)
}
func (m *LevelupPresentParentMap) XXX_Size() int {
	return xxx_messageInfo_LevelupPresentParentMap.Size(m)
}
func (m *LevelupPresentParentMap) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelupPresentParentMap.DiscardUnknown(m)
}

var xxx_messageInfo_LevelupPresentParentMap proto.InternalMessageInfo

func (m *LevelupPresentParentMap) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *LevelupPresentParentMap) GetMap() map[uint32]*LevelupPresentParentData {
	if m != nil {
		return m.Map
	}
	return nil
}

type EmptyMsg struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *EmptyMsg) Reset()         { *m = EmptyMsg{} }
func (m *EmptyMsg) String() string { return proto.CompactTextString(m) }
func (*EmptyMsg) ProtoMessage()    {}
func (*EmptyMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_logic__4b736df73634ddfb, []int{4}
}
func (m *EmptyMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyMsg.Unmarshal(m, b)
}
func (m *EmptyMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyMsg.Marshal(b, m, deterministic)
}
func (dst *EmptyMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyMsg.Merge(dst, src)
}
func (m *EmptyMsg) XXX_Size() int {
	return xxx_messageInfo_EmptyMsg.Size(m)
}
func (m *EmptyMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyMsg.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyMsg proto.InternalMessageInfo

func (m *EmptyMsg) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type UserLevelExp struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Exp                  uint32   `protobuf:"varint,2,opt,name=exp,proto3" json:"exp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserLevelExp) Reset()         { *m = UserLevelExp{} }
func (m *UserLevelExp) String() string { return proto.CompactTextString(m) }
func (*UserLevelExp) ProtoMessage()    {}
func (*UserLevelExp) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_logic__4b736df73634ddfb, []int{5}
}
func (m *UserLevelExp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelExp.Unmarshal(m, b)
}
func (m *UserLevelExp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelExp.Marshal(b, m, deterministic)
}
func (dst *UserLevelExp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelExp.Merge(dst, src)
}
func (m *UserLevelExp) XXX_Size() int {
	return xxx_messageInfo_UserLevelExp.Size(m)
}
func (m *UserLevelExp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelExp.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelExp proto.InternalMessageInfo

func (m *UserLevelExp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UserLevelExp) GetExp() uint32 {
	if m != nil {
		return m.Exp
	}
	return 0
}

type UserLevelupPresentStatusMap struct {
	BaseReq              *app.BaseReq             `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Map                  map[uint32]*UserLevelExp `protobuf:"bytes,2,rep,name=map,proto3" json:"map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UserLevelupPresentStatusMap) Reset()         { *m = UserLevelupPresentStatusMap{} }
func (m *UserLevelupPresentStatusMap) String() string { return proto.CompactTextString(m) }
func (*UserLevelupPresentStatusMap) ProtoMessage()    {}
func (*UserLevelupPresentStatusMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_logic__4b736df73634ddfb, []int{6}
}
func (m *UserLevelupPresentStatusMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelupPresentStatusMap.Unmarshal(m, b)
}
func (m *UserLevelupPresentStatusMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelupPresentStatusMap.Marshal(b, m, deterministic)
}
func (dst *UserLevelupPresentStatusMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelupPresentStatusMap.Merge(dst, src)
}
func (m *UserLevelupPresentStatusMap) XXX_Size() int {
	return xxx_messageInfo_UserLevelupPresentStatusMap.Size(m)
}
func (m *UserLevelupPresentStatusMap) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelupPresentStatusMap.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelupPresentStatusMap proto.InternalMessageInfo

func (m *UserLevelupPresentStatusMap) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UserLevelupPresentStatusMap) GetMap() map[uint32]*UserLevelExp {
	if m != nil {
		return m.Map
	}
	return nil
}

type SendLevelupPresentReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SendType             uint32       `protobuf:"varint,2,opt,name=send_type,json=sendType,proto3" json:"send_type,omitempty"`
	ItemId               uint32       `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Version              uint32       `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	ItemCount            uint32       `protobuf:"varint,5,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendLevelupPresentReq) Reset()         { *m = SendLevelupPresentReq{} }
func (m *SendLevelupPresentReq) String() string { return proto.CompactTextString(m) }
func (*SendLevelupPresentReq) ProtoMessage()    {}
func (*SendLevelupPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_logic__4b736df73634ddfb, []int{7}
}
func (m *SendLevelupPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendLevelupPresentReq.Unmarshal(m, b)
}
func (m *SendLevelupPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendLevelupPresentReq.Marshal(b, m, deterministic)
}
func (dst *SendLevelupPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendLevelupPresentReq.Merge(dst, src)
}
func (m *SendLevelupPresentReq) XXX_Size() int {
	return xxx_messageInfo_SendLevelupPresentReq.Size(m)
}
func (m *SendLevelupPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendLevelupPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendLevelupPresentReq proto.InternalMessageInfo

func (m *SendLevelupPresentReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendLevelupPresentReq) GetSendType() uint32 {
	if m != nil {
		return m.SendType
	}
	return 0
}

func (m *SendLevelupPresentReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SendLevelupPresentReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *SendLevelupPresentReq) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

type SendLevelupPresentRes struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Level                uint32       `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Exp                  uint32       `protobuf:"varint,3,opt,name=exp,proto3" json:"exp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SendLevelupPresentRes) Reset()         { *m = SendLevelupPresentRes{} }
func (m *SendLevelupPresentRes) String() string { return proto.CompactTextString(m) }
func (*SendLevelupPresentRes) ProtoMessage()    {}
func (*SendLevelupPresentRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_levelup_present_logic__4b736df73634ddfb, []int{8}
}
func (m *SendLevelupPresentRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendLevelupPresentRes.Unmarshal(m, b)
}
func (m *SendLevelupPresentRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendLevelupPresentRes.Marshal(b, m, deterministic)
}
func (dst *SendLevelupPresentRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendLevelupPresentRes.Merge(dst, src)
}
func (m *SendLevelupPresentRes) XXX_Size() int {
	return xxx_messageInfo_SendLevelupPresentRes.Size(m)
}
func (m *SendLevelupPresentRes) XXX_DiscardUnknown() {
	xxx_messageInfo_SendLevelupPresentRes.DiscardUnknown(m)
}

var xxx_messageInfo_SendLevelupPresentRes proto.InternalMessageInfo

func (m *SendLevelupPresentRes) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendLevelupPresentRes) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SendLevelupPresentRes) GetExp() uint32 {
	if m != nil {
		return m.Exp
	}
	return 0
}

func init() {
	proto.RegisterType((*LevelupPresentBatchData)(nil), "ga.levelup_present_logic.LevelupPresentBatchData")
	proto.RegisterType((*LevelupPresentData)(nil), "ga.levelup_present_logic.LevelupPresentData")
	proto.RegisterType((*LevelupPresentParentData)(nil), "ga.levelup_present_logic.LevelupPresentParentData")
	proto.RegisterType((*LevelupPresentParentMap)(nil), "ga.levelup_present_logic.LevelupPresentParentMap")
	proto.RegisterMapType((map[uint32]*LevelupPresentParentData)(nil), "ga.levelup_present_logic.LevelupPresentParentMap.MapEntry")
	proto.RegisterType((*EmptyMsg)(nil), "ga.levelup_present_logic.EmptyMsg")
	proto.RegisterType((*UserLevelExp)(nil), "ga.levelup_present_logic.UserLevelExp")
	proto.RegisterType((*UserLevelupPresentStatusMap)(nil), "ga.levelup_present_logic.UserLevelupPresentStatusMap")
	proto.RegisterMapType((map[uint32]*UserLevelExp)(nil), "ga.levelup_present_logic.UserLevelupPresentStatusMap.MapEntry")
	proto.RegisterType((*SendLevelupPresentReq)(nil), "ga.levelup_present_logic.SendLevelupPresentReq")
	proto.RegisterType((*SendLevelupPresentRes)(nil), "ga.levelup_present_logic.SendLevelupPresentRes")
}

func init() {
	proto.RegisterFile("levelup-present-logic_.proto", fileDescriptor_levelup_present_logic__4b736df73634ddfb)
}

var fileDescriptor_levelup_present_logic__4b736df73634ddfb = []byte{
	// 700 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x55, 0x4f, 0x6f, 0xd3, 0x4e,
	0x10, 0x95, 0x93, 0xe6, 0xdf, 0x24, 0xe9, 0xef, 0x27, 0x0b, 0x54, 0xab, 0x05, 0x51, 0x0c, 0x2a,
	0x3d, 0x50, 0x47, 0x04, 0x81, 0x50, 0x85, 0x38, 0x94, 0x46, 0xa2, 0x6a, 0x2b, 0x45, 0x6e, 0xcb,
	0x81, 0x03, 0xd6, 0x66, 0x3d, 0x35, 0x06, 0xff, 0xd9, 0x7a, 0xd7, 0x55, 0xfd, 0x89, 0xb8, 0xf1,
	0x19, 0xf8, 0x50, 0x48, 0x1c, 0xb8, 0xa0, 0xdd, 0x75, 0x42, 0x52, 0xda, 0xe2, 0xde, 0xbc, 0x6f,
	0xdf, 0xec, 0xcc, 0x7b, 0xb3, 0x3b, 0x86, 0x7b, 0x11, 0x9e, 0x63, 0x94, 0xb3, 0x2d, 0x96, 0x21,
	0xc7, 0x44, 0x6c, 0x45, 0x69, 0x10, 0x52, 0xcf, 0x61, 0x59, 0x2a, 0x52, 0xd3, 0x0a, 0x88, 0x53,
	0x12, 0xbc, 0x92, 0xe0, 0x29, 0xc2, 0x6a, 0x3f, 0x20, 0xde, 0x84, 0x70, 0xd4, 0x44, 0xfb, 0x87,
	0x01, 0x2b, 0x07, 0x9a, 0x38, 0xd6, 0xbc, 0x1d, 0x22, 0xe8, 0xa7, 0x5d, 0x22, 0x88, 0xb9, 0x02,
	0xad, 0x50, 0x60, 0xec, 0x85, 0xbe, 0x65, 0xac, 0x1b, 0x9b, 0x7d, 0xb7, 0x29, 0x97, 0x7b, 0xbe,
	0xf9, 0x00, 0xba, 0x13, 0xc9, 0xf2, 0x68, 0x9a, 0x27, 0xc2, 0xaa, 0xa9, 0x4d, 0x50, 0xd0, 0x5b,
	0x89, 0x98, 0xf7, 0x01, 0xf0, 0xf4, 0x14, 0xa9, 0xf0, 0xf2, 0x2c, 0xb2, 0xea, 0xeb, 0xc6, 0x66,
	0xc7, 0xed, 0x68, 0xe4, 0x24, 0x8b, 0x64, 0x7c, 0xb9, 0xed, 0x23, 0xa7, 0xd6, 0x92, 0xda, 0x2f,
	0x23, 0x76, 0x91, 0xd3, 0xb9, 0x78, 0x3f, 0xa5, 0x56, 0x63, 0x3e, 0x7e, 0x37, 0xa5, 0x32, 0x9e,
	0x66, 0x48, 0x04, 0x7a, 0x22, 0x8c, 0xd1, 0x6a, 0xea, 0xfc, 0x1a, 0x3a, 0x0e, 0x63, 0x94, 0x84,
	0x9c, 0xf9, 0x33, 0x42, 0x4b, 0x13, 0x34, 0x24, 0x09, 0xf6, 0x57, 0x03, 0xcc, 0x45, 0xd9, 0x37,
	0x2b, 0xbe, 0x03, 0x0d, 0x65, 0x67, 0xa9, 0x55, 0x2f, 0xcc, 0xff, 0xa1, 0x8e, 0x17, 0x4c, 0xe9,
	0xeb, 0xbb, 0xf2, 0xd3, 0x1c, 0x83, 0xb6, 0xc1, 0x8b, 0x42, 0x2e, 0xac, 0xa5, 0xf5, 0xfa, 0x66,
	0x77, 0xf8, 0xcc, 0xb9, 0xae, 0x19, 0xce, 0x35, 0xce, 0xbb, 0x1d, 0x75, 0xc8, 0x41, 0xc8, 0x85,
	0xfd, 0xbd, 0x06, 0xd6, 0x22, 0x6d, 0x4c, 0xb2, 0x69, 0xbd, 0x8f, 0x61, 0x99, 0xa9, 0x95, 0xb7,
	0x58, 0x76, 0x4f, 0xa3, 0x7b, 0xba, 0xf8, 0x87, 0xd0, 0x9b, 0xa6, 0x15, 0x05, 0xc3, 0x52, 0x43,
	0xb7, 0xc4, 0x8e, 0x0b, 0x86, 0xe6, 0x13, 0xf8, 0x8f, 0xe6, 0x99, 0x3a, 0xe9, 0x1c, 0x33, 0x1e,
	0xa6, 0x49, 0xa9, 0x6a, 0xb9, 0x84, 0xdf, 0x6b, 0xd4, 0x7c, 0x04, 0x7d, 0x11, 0x32, 0xee, 0x09,
	0x8c, 0x59, 0x44, 0x04, 0x96, 0xcd, 0xeb, 0x49, 0xf0, 0xb8, 0xc4, 0x2e, 0xf7, 0xa7, 0xf1, 0xaf,
	0xfe, 0x34, 0x2f, 0xf7, 0xc7, 0xdc, 0x07, 0x50, 0x8e, 0x69, 0x1f, 0x5b, 0xca, 0xc7, 0xa7, 0x55,
	0x7d, 0xd4, 0x16, 0x2a, 0xa6, 0xb2, 0xf0, 0xd7, 0x5f, 0x77, 0x5c, 0x5b, 0x78, 0x48, 0x98, 0xb9,
	0x01, 0x6d, 0xf9, 0x1a, 0xbc, 0x0c, 0xcf, 0x94, 0x77, 0xdd, 0x61, 0x57, 0xa6, 0xd9, 0x21, 0x1c,
	0x5d, 0x3c, 0x73, 0x5b, 0x13, 0xfd, 0x61, 0x1e, 0x40, 0x3d, 0x26, 0xcc, 0xaa, 0xa9, 0x4a, 0xb6,
	0xab, 0x56, 0x32, 0xcb, 0xe3, 0x1c, 0x12, 0x36, 0x4a, 0x44, 0x56, 0xb8, 0xf2, 0x98, 0xd5, 0xcf,
	0xd0, 0x9e, 0x02, 0xf2, 0x12, 0x7d, 0xc1, 0xa2, 0x6c, 0x9c, 0xfc, 0x34, 0xdf, 0x41, 0xe3, 0x9c,
	0x44, 0xb9, 0x6e, 0x54, 0x77, 0x38, 0xbc, 0x5d, 0x36, 0xa5, 0x5e, 0x1f, 0xb0, 0x5d, 0x7b, 0x65,
	0xd8, 0x43, 0x68, 0x8f, 0x62, 0x26, 0x8a, 0x43, 0x1e, 0x54, 0x55, 0x6b, 0xbf, 0x84, 0xde, 0x09,
	0xc7, 0x4c, 0x1d, 0x3f, 0xba, 0x60, 0x7f, 0xae, 0xbf, 0x71, 0xc5, 0xf5, 0xaf, 0xcd, 0xae, 0xbf,
	0xfd, 0xd3, 0x80, 0xb5, 0x59, 0xe0, 0xac, 0xae, 0x23, 0x41, 0x44, 0xce, 0x6f, 0xe3, 0xf6, 0x78,
	0xde, 0xed, 0x37, 0xd7, 0xeb, 0xbf, 0x21, 0xd7, 0x25, 0xc7, 0x3f, 0xde, 0xe8, 0xf8, 0xeb, 0x45,
	0xc7, 0x37, 0x2a, 0x64, 0x1c, 0x5d, 0xb0, 0x79, 0x97, 0xbf, 0x19, 0x70, 0xf7, 0x08, 0x13, 0x7f,
	0xb1, 0x1a, 0xa9, 0xa5, 0xaa, 0xe6, 0x35, 0xe8, 0x70, 0x4c, 0xfc, 0xf9, 0x27, 0xda, 0x96, 0x80,
	0x7a, 0x9f, 0x73, 0x83, 0xa9, 0xbe, 0x30, 0x98, 0x2c, 0x68, 0x4d, 0x1f, 0xec, 0x92, 0xda, 0x98,
	0x2e, 0xe5, 0x0c, 0x55, 0x21, 0x7a, 0x46, 0xeb, 0x37, 0xd8, 0x91, 0x88, 0x1a, 0xd1, 0x76, 0x70,
	0x75, 0xbd, 0xbc, 0x72, 0xbd, 0x15, 0x47, 0xe2, 0xce, 0x3e, 0x58, 0x34, 0x8d, 0x9d, 0x22, 0x2c,
	0xd2, 0x5c, 0x1e, 0x14, 0xa7, 0x3e, 0x46, 0xfa, 0xef, 0xf3, 0x61, 0x10, 0xa4, 0x11, 0x49, 0x02,
	0xe7, 0xc5, 0x50, 0x08, 0x87, 0xa6, 0xf1, 0x40, 0xc1, 0x34, 0x8d, 0x06, 0x84, 0xb1, 0x41, 0x4c,
	0x82, 0x90, 0x6e, 0x71, 0x16, 0x66, 0x61, 0xf9, 0x7b, 0x9b, 0x34, 0x15, 0xe1, 0xf9, 0xef, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x77, 0xd4, 0xd9, 0x90, 0xfe, 0x06, 0x00, 0x00,
}
