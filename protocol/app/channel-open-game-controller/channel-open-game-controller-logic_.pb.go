// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-open-game-controller-logic_.proto

package channel_open_game_controller // import "golang.52tt.com/protocol/app/channel-open-game-controller"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 游戏加载完，登录获取openid和code
type GetChannelGamePlayerOpenidReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64        `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelGamePlayerOpenidReq) Reset()         { *m = GetChannelGamePlayerOpenidReq{} }
func (m *GetChannelGamePlayerOpenidReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelGamePlayerOpenidReq) ProtoMessage()    {}
func (*GetChannelGamePlayerOpenidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{0}
}
func (m *GetChannelGamePlayerOpenidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGamePlayerOpenidReq.Unmarshal(m, b)
}
func (m *GetChannelGamePlayerOpenidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGamePlayerOpenidReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelGamePlayerOpenidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGamePlayerOpenidReq.Merge(dst, src)
}
func (m *GetChannelGamePlayerOpenidReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelGamePlayerOpenidReq.Size(m)
}
func (m *GetChannelGamePlayerOpenidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGamePlayerOpenidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGamePlayerOpenidReq proto.InternalMessageInfo

func (m *GetChannelGamePlayerOpenidReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelGamePlayerOpenidReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelGamePlayerOpenidReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type GetChannelGamePlayerOpenidResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	OpenId               string        `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	Code                 string        `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelGamePlayerOpenidResp) Reset()         { *m = GetChannelGamePlayerOpenidResp{} }
func (m *GetChannelGamePlayerOpenidResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelGamePlayerOpenidResp) ProtoMessage()    {}
func (*GetChannelGamePlayerOpenidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{1}
}
func (m *GetChannelGamePlayerOpenidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGamePlayerOpenidResp.Unmarshal(m, b)
}
func (m *GetChannelGamePlayerOpenidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGamePlayerOpenidResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelGamePlayerOpenidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGamePlayerOpenidResp.Merge(dst, src)
}
func (m *GetChannelGamePlayerOpenidResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelGamePlayerOpenidResp.Size(m)
}
func (m *GetChannelGamePlayerOpenidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGamePlayerOpenidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGamePlayerOpenidResp proto.InternalMessageInfo

func (m *GetChannelGamePlayerOpenidResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelGamePlayerOpenidResp) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GetChannelGamePlayerOpenidResp) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

// 修改游戏附加信息
type SetChannelGameModeInfoReq struct {
	BaseReq              *app.BaseReq         `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32               `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64                `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	GameMode             *ChannelGameModeInfo `protobuf:"bytes,4,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetChannelGameModeInfoReq) Reset()         { *m = SetChannelGameModeInfoReq{} }
func (m *SetChannelGameModeInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameModeInfoReq) ProtoMessage()    {}
func (*SetChannelGameModeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{2}
}
func (m *SetChannelGameModeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameModeInfoReq.Unmarshal(m, b)
}
func (m *SetChannelGameModeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameModeInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameModeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameModeInfoReq.Merge(dst, src)
}
func (m *SetChannelGameModeInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameModeInfoReq.Size(m)
}
func (m *SetChannelGameModeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameModeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameModeInfoReq proto.InternalMessageInfo

func (m *SetChannelGameModeInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetChannelGameModeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameModeInfoReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

func (m *SetChannelGameModeInfoReq) GetGameMode() *ChannelGameModeInfo {
	if m != nil {
		return m.GameMode
	}
	return nil
}

type SetChannelGameModeInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChannelGameModeInfoResp) Reset()         { *m = SetChannelGameModeInfoResp{} }
func (m *SetChannelGameModeInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameModeInfoResp) ProtoMessage()    {}
func (*SetChannelGameModeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{3}
}
func (m *SetChannelGameModeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameModeInfoResp.Unmarshal(m, b)
}
func (m *SetChannelGameModeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameModeInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameModeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameModeInfoResp.Merge(dst, src)
}
func (m *SetChannelGameModeInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameModeInfoResp.Size(m)
}
func (m *SetChannelGameModeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameModeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameModeInfoResp proto.InternalMessageInfo

func (m *SetChannelGameModeInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 加入游戏
type JoinChannelGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64        `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *JoinChannelGameReq) Reset()         { *m = JoinChannelGameReq{} }
func (m *JoinChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*JoinChannelGameReq) ProtoMessage()    {}
func (*JoinChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{4}
}
func (m *JoinChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelGameReq.Unmarshal(m, b)
}
func (m *JoinChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *JoinChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelGameReq.Merge(dst, src)
}
func (m *JoinChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_JoinChannelGameReq.Size(m)
}
func (m *JoinChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelGameReq proto.InternalMessageInfo

func (m *JoinChannelGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *JoinChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinChannelGameReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type JoinChannelGameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MatchSeq             uint32        `protobuf:"varint,2,opt,name=match_seq,json=matchSeq,proto3" json:"match_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *JoinChannelGameResp) Reset()         { *m = JoinChannelGameResp{} }
func (m *JoinChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*JoinChannelGameResp) ProtoMessage()    {}
func (*JoinChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{5}
}
func (m *JoinChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinChannelGameResp.Unmarshal(m, b)
}
func (m *JoinChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *JoinChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinChannelGameResp.Merge(dst, src)
}
func (m *JoinChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_JoinChannelGameResp.Size(m)
}
func (m *JoinChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinChannelGameResp proto.InternalMessageInfo

func (m *JoinChannelGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *JoinChannelGameResp) GetMatchSeq() uint32 {
	if m != nil {
		return m.MatchSeq
	}
	return 0
}

// 取消加入
type QuitChannelGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64        `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *QuitChannelGameReq) Reset()         { *m = QuitChannelGameReq{} }
func (m *QuitChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*QuitChannelGameReq) ProtoMessage()    {}
func (*QuitChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{6}
}
func (m *QuitChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelGameReq.Unmarshal(m, b)
}
func (m *QuitChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *QuitChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelGameReq.Merge(dst, src)
}
func (m *QuitChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_QuitChannelGameReq.Size(m)
}
func (m *QuitChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelGameReq proto.InternalMessageInfo

func (m *QuitChannelGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *QuitChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *QuitChannelGameReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type QuitChannelGameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *QuitChannelGameResp) Reset()         { *m = QuitChannelGameResp{} }
func (m *QuitChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*QuitChannelGameResp) ProtoMessage()    {}
func (*QuitChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{7}
}
func (m *QuitChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuitChannelGameResp.Unmarshal(m, b)
}
func (m *QuitChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuitChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *QuitChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuitChannelGameResp.Merge(dst, src)
}
func (m *QuitChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_QuitChannelGameResp.Size(m)
}
func (m *QuitChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuitChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuitChannelGameResp proto.InternalMessageInfo

func (m *QuitChannelGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 准备游戏
type ReadyChannelGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64        `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReadyChannelGameReq) Reset()         { *m = ReadyChannelGameReq{} }
func (m *ReadyChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*ReadyChannelGameReq) ProtoMessage()    {}
func (*ReadyChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{8}
}
func (m *ReadyChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyChannelGameReq.Unmarshal(m, b)
}
func (m *ReadyChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *ReadyChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyChannelGameReq.Merge(dst, src)
}
func (m *ReadyChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_ReadyChannelGameReq.Size(m)
}
func (m *ReadyChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyChannelGameReq proto.InternalMessageInfo

func (m *ReadyChannelGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReadyChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReadyChannelGameReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type ReadyChannelGameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReadyChannelGameResp) Reset()         { *m = ReadyChannelGameResp{} }
func (m *ReadyChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*ReadyChannelGameResp) ProtoMessage()    {}
func (*ReadyChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{9}
}
func (m *ReadyChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReadyChannelGameResp.Unmarshal(m, b)
}
func (m *ReadyChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReadyChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *ReadyChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadyChannelGameResp.Merge(dst, src)
}
func (m *ReadyChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_ReadyChannelGameResp.Size(m)
}
func (m *ReadyChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadyChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReadyChannelGameResp proto.InternalMessageInfo

func (m *ReadyChannelGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 取消准备
type UnReadyChannelGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64        `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UnReadyChannelGameReq) Reset()         { *m = UnReadyChannelGameReq{} }
func (m *UnReadyChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*UnReadyChannelGameReq) ProtoMessage()    {}
func (*UnReadyChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{10}
}
func (m *UnReadyChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnReadyChannelGameReq.Unmarshal(m, b)
}
func (m *UnReadyChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnReadyChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *UnReadyChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnReadyChannelGameReq.Merge(dst, src)
}
func (m *UnReadyChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_UnReadyChannelGameReq.Size(m)
}
func (m *UnReadyChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnReadyChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnReadyChannelGameReq proto.InternalMessageInfo

func (m *UnReadyChannelGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UnReadyChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UnReadyChannelGameReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type UnReadyChannelGameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UnReadyChannelGameResp) Reset()         { *m = UnReadyChannelGameResp{} }
func (m *UnReadyChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*UnReadyChannelGameResp) ProtoMessage()    {}
func (*UnReadyChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{11}
}
func (m *UnReadyChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnReadyChannelGameResp.Unmarshal(m, b)
}
func (m *UnReadyChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnReadyChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *UnReadyChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnReadyChannelGameResp.Merge(dst, src)
}
func (m *UnReadyChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_UnReadyChannelGameResp.Size(m)
}
func (m *UnReadyChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnReadyChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnReadyChannelGameResp proto.InternalMessageInfo

func (m *UnReadyChannelGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取游戏数据
type GetChannelGameStatusInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64        `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelGameStatusInfoReq) Reset()         { *m = GetChannelGameStatusInfoReq{} }
func (m *GetChannelGameStatusInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelGameStatusInfoReq) ProtoMessage()    {}
func (*GetChannelGameStatusInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{12}
}
func (m *GetChannelGameStatusInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGameStatusInfoReq.Unmarshal(m, b)
}
func (m *GetChannelGameStatusInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGameStatusInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelGameStatusInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGameStatusInfoReq.Merge(dst, src)
}
func (m *GetChannelGameStatusInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelGameStatusInfoReq.Size(m)
}
func (m *GetChannelGameStatusInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGameStatusInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGameStatusInfoReq proto.InternalMessageInfo

func (m *GetChannelGameStatusInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelGameStatusInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelGameStatusInfoReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type GetChannelGameStatusInfoResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Status               *ChannelGameInfoStatus `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetChannelGameStatusInfoResp) Reset()         { *m = GetChannelGameStatusInfoResp{} }
func (m *GetChannelGameStatusInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelGameStatusInfoResp) ProtoMessage()    {}
func (*GetChannelGameStatusInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{13}
}
func (m *GetChannelGameStatusInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGameStatusInfoResp.Unmarshal(m, b)
}
func (m *GetChannelGameStatusInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGameStatusInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelGameStatusInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGameStatusInfoResp.Merge(dst, src)
}
func (m *GetChannelGameStatusInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelGameStatusInfoResp.Size(m)
}
func (m *GetChannelGameStatusInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGameStatusInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGameStatusInfoResp proto.InternalMessageInfo

func (m *GetChannelGameStatusInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelGameStatusInfoResp) GetStatus() *ChannelGameInfoStatus {
	if m != nil {
		return m.Status
	}
	return nil
}

// 开始游戏
type StartChannelGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64        `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StartChannelGameReq) Reset()         { *m = StartChannelGameReq{} }
func (m *StartChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*StartChannelGameReq) ProtoMessage()    {}
func (*StartChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{14}
}
func (m *StartChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChannelGameReq.Unmarshal(m, b)
}
func (m *StartChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *StartChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChannelGameReq.Merge(dst, src)
}
func (m *StartChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_StartChannelGameReq.Size(m)
}
func (m *StartChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartChannelGameReq proto.InternalMessageInfo

func (m *StartChannelGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartChannelGameReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type StartChannelGameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StartChannelGameResp) Reset()         { *m = StartChannelGameResp{} }
func (m *StartChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*StartChannelGameResp) ProtoMessage()    {}
func (*StartChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{15}
}
func (m *StartChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartChannelGameResp.Unmarshal(m, b)
}
func (m *StartChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *StartChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartChannelGameResp.Merge(dst, src)
}
func (m *StartChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_StartChannelGameResp.Size(m)
}
func (m *StartChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartChannelGameResp proto.InternalMessageInfo

func (m *StartChannelGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 退出游戏
type ExitChannelGameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64        `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	Reason               uint32       `protobuf:"varint,4,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ExitChannelGameReq) Reset()         { *m = ExitChannelGameReq{} }
func (m *ExitChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*ExitChannelGameReq) ProtoMessage()    {}
func (*ExitChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{16}
}
func (m *ExitChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelGameReq.Unmarshal(m, b)
}
func (m *ExitChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *ExitChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelGameReq.Merge(dst, src)
}
func (m *ExitChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_ExitChannelGameReq.Size(m)
}
func (m *ExitChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelGameReq proto.InternalMessageInfo

func (m *ExitChannelGameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ExitChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ExitChannelGameReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

func (m *ExitChannelGameReq) GetReason() uint32 {
	if m != nil {
		return m.Reason
	}
	return 0
}

type ExitChannelGameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ExitChannelGameResp) Reset()         { *m = ExitChannelGameResp{} }
func (m *ExitChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*ExitChannelGameResp) ProtoMessage()    {}
func (*ExitChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{17}
}
func (m *ExitChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExitChannelGameResp.Unmarshal(m, b)
}
func (m *ExitChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExitChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *ExitChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExitChannelGameResp.Merge(dst, src)
}
func (m *ExitChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_ExitChannelGameResp.Size(m)
}
func (m *ExitChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExitChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExitChannelGameResp proto.InternalMessageInfo

func (m *ExitChannelGameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 设置房间内用户加载游戏状态
type SetChannelGamePlayerLoadingReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadSeq              int64        `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	IsLoadingCompleted   bool         `protobuf:"varint,4,opt,name=is_loading_completed,json=isLoadingCompleted,proto3" json:"is_loading_completed,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetChannelGamePlayerLoadingReq) Reset()         { *m = SetChannelGamePlayerLoadingReq{} }
func (m *SetChannelGamePlayerLoadingReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGamePlayerLoadingReq) ProtoMessage()    {}
func (*SetChannelGamePlayerLoadingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{18}
}
func (m *SetChannelGamePlayerLoadingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGamePlayerLoadingReq.Unmarshal(m, b)
}
func (m *SetChannelGamePlayerLoadingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGamePlayerLoadingReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGamePlayerLoadingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGamePlayerLoadingReq.Merge(dst, src)
}
func (m *SetChannelGamePlayerLoadingReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGamePlayerLoadingReq.Size(m)
}
func (m *SetChannelGamePlayerLoadingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGamePlayerLoadingReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGamePlayerLoadingReq proto.InternalMessageInfo

func (m *SetChannelGamePlayerLoadingReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetChannelGamePlayerLoadingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGamePlayerLoadingReq) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

func (m *SetChannelGamePlayerLoadingReq) GetIsLoadingCompleted() bool {
	if m != nil {
		return m.IsLoadingCompleted
	}
	return false
}

type SetChannelGamePlayerLoadingResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetChannelGamePlayerLoadingResp) Reset()         { *m = SetChannelGamePlayerLoadingResp{} }
func (m *SetChannelGamePlayerLoadingResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGamePlayerLoadingResp) ProtoMessage()    {}
func (*SetChannelGamePlayerLoadingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{19}
}
func (m *SetChannelGamePlayerLoadingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGamePlayerLoadingResp.Unmarshal(m, b)
}
func (m *SetChannelGamePlayerLoadingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGamePlayerLoadingResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGamePlayerLoadingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGamePlayerLoadingResp.Merge(dst, src)
}
func (m *SetChannelGamePlayerLoadingResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGamePlayerLoadingResp.Size(m)
}
func (m *SetChannelGamePlayerLoadingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGamePlayerLoadingResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGamePlayerLoadingResp proto.InternalMessageInfo

func (m *SetChannelGamePlayerLoadingResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type BatchGetUidByOpenidReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GameId               uint32       `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	OpenidList           []string     `protobuf:"bytes,3,rep,name=openid_list,json=openidList,proto3" json:"openid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetUidByOpenidReq) Reset()         { *m = BatchGetUidByOpenidReq{} }
func (m *BatchGetUidByOpenidReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUidByOpenidReq) ProtoMessage()    {}
func (*BatchGetUidByOpenidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{20}
}
func (m *BatchGetUidByOpenidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUidByOpenidReq.Unmarshal(m, b)
}
func (m *BatchGetUidByOpenidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUidByOpenidReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUidByOpenidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUidByOpenidReq.Merge(dst, src)
}
func (m *BatchGetUidByOpenidReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUidByOpenidReq.Size(m)
}
func (m *BatchGetUidByOpenidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUidByOpenidReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUidByOpenidReq proto.InternalMessageInfo

func (m *BatchGetUidByOpenidReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BatchGetUidByOpenidReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *BatchGetUidByOpenidReq) GetOpenidList() []string {
	if m != nil {
		return m.OpenidList
	}
	return nil
}

type BatchGetUidByOpenidResp struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	AccountMap           map[string]*AccountInfo `protobuf:"bytes,2,rep,name=account_map,json=accountMap,proto3" json:"account_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BatchGetUidByOpenidResp) Reset()         { *m = BatchGetUidByOpenidResp{} }
func (m *BatchGetUidByOpenidResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUidByOpenidResp) ProtoMessage()    {}
func (*BatchGetUidByOpenidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{21}
}
func (m *BatchGetUidByOpenidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUidByOpenidResp.Unmarshal(m, b)
}
func (m *BatchGetUidByOpenidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUidByOpenidResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUidByOpenidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUidByOpenidResp.Merge(dst, src)
}
func (m *BatchGetUidByOpenidResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUidByOpenidResp.Size(m)
}
func (m *BatchGetUidByOpenidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUidByOpenidResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUidByOpenidResp proto.InternalMessageInfo

func (m *BatchGetUidByOpenidResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BatchGetUidByOpenidResp) GetAccountMap() map[string]*AccountInfo {
	if m != nil {
		return m.AccountMap
	}
	return nil
}

type AccountInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AccountInfo) Reset()         { *m = AccountInfo{} }
func (m *AccountInfo) String() string { return proto.CompactTextString(m) }
func (*AccountInfo) ProtoMessage()    {}
func (*AccountInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{22}
}
func (m *AccountInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccountInfo.Unmarshal(m, b)
}
func (m *AccountInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccountInfo.Marshal(b, m, deterministic)
}
func (dst *AccountInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccountInfo.Merge(dst, src)
}
func (m *AccountInfo) XXX_Size() int {
	return xxx_messageInfo_AccountInfo.Size(m)
}
func (m *AccountInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AccountInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AccountInfo proto.InternalMessageInfo

func (m *AccountInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AccountInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

// CheckUserStartGamePermission 检查用户是否能开始游戏
// 特殊游戏\用户的游戏流程，这类游戏不通过tt服务端(channel-open-game-controller)控制游戏流程，
// 仅通过该接口做必要的监管风控校验(如绑定手机号，实名认证)，以判定用户是否能开始游戏。
// 目前此类游戏仅限于端到端单人\单机游戏，在房用户只能感知到房间加载了游戏，不同用户在房内有各自的游戏局进度。
// 入参is_pop_permission:
//  在权限不足(如未绑定手机号，未实名认证)时，
//  1.若填true表示需要弹窗验证，接口返回相应监管\风控错误码；(一般由用户点开始主动触发)
//  2.否则不需要弹窗验证，接口返回成功，在响应体里面填充错误信息。(用于游戏发起验证后定时轮询)
type CheckUserStartGamePermissionRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GameId               uint32       `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	IsPopPermission      bool         `protobuf:"varint,3,opt,name=is_pop_permission,json=isPopPermission,proto3" json:"is_pop_permission,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckUserStartGamePermissionRequest) Reset()         { *m = CheckUserStartGamePermissionRequest{} }
func (m *CheckUserStartGamePermissionRequest) String() string { return proto.CompactTextString(m) }
func (*CheckUserStartGamePermissionRequest) ProtoMessage()    {}
func (*CheckUserStartGamePermissionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{23}
}
func (m *CheckUserStartGamePermissionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserStartGamePermissionRequest.Unmarshal(m, b)
}
func (m *CheckUserStartGamePermissionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserStartGamePermissionRequest.Marshal(b, m, deterministic)
}
func (dst *CheckUserStartGamePermissionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserStartGamePermissionRequest.Merge(dst, src)
}
func (m *CheckUserStartGamePermissionRequest) XXX_Size() int {
	return xxx_messageInfo_CheckUserStartGamePermissionRequest.Size(m)
}
func (m *CheckUserStartGamePermissionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserStartGamePermissionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserStartGamePermissionRequest proto.InternalMessageInfo

func (m *CheckUserStartGamePermissionRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckUserStartGamePermissionRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CheckUserStartGamePermissionRequest) GetIsPopPermission() bool {
	if m != nil {
		return m.IsPopPermission
	}
	return false
}

type CheckUserStartGamePermissionResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ErrCode              int32         `protobuf:"varint,2,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string        `protobuf:"bytes,3,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckUserStartGamePermissionResponse) Reset()         { *m = CheckUserStartGamePermissionResponse{} }
func (m *CheckUserStartGamePermissionResponse) String() string { return proto.CompactTextString(m) }
func (*CheckUserStartGamePermissionResponse) ProtoMessage()    {}
func (*CheckUserStartGamePermissionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{24}
}
func (m *CheckUserStartGamePermissionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserStartGamePermissionResponse.Unmarshal(m, b)
}
func (m *CheckUserStartGamePermissionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserStartGamePermissionResponse.Marshal(b, m, deterministic)
}
func (dst *CheckUserStartGamePermissionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserStartGamePermissionResponse.Merge(dst, src)
}
func (m *CheckUserStartGamePermissionResponse) XXX_Size() int {
	return xxx_messageInfo_CheckUserStartGamePermissionResponse.Size(m)
}
func (m *CheckUserStartGamePermissionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserStartGamePermissionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserStartGamePermissionResponse proto.InternalMessageInfo

func (m *CheckUserStartGamePermissionResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckUserStartGamePermissionResponse) GetErrCode() int32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *CheckUserStartGamePermissionResponse) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

// ---------------------------------------
// push结构 game change
// 游戏用户信息
type ChannelGamePlayerInfo struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               uint32           `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Username             string           `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string           `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32           `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Openid               string           `protobuf:"bytes,6,opt,name=openid,proto3" json:"openid,omitempty"`
	Seq                  uint32           `protobuf:"varint,7,opt,name=seq,proto3" json:"seq,omitempty"`
	IsLoadingCompleted   bool             `protobuf:"varint,8,opt,name=is_loading_completed,json=isLoadingCompleted,proto3" json:"is_loading_completed,omitempty"`
	UserProfile          *app.UserProfile `protobuf:"bytes,9,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ChannelGamePlayerInfo) Reset()         { *m = ChannelGamePlayerInfo{} }
func (m *ChannelGamePlayerInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelGamePlayerInfo) ProtoMessage()    {}
func (*ChannelGamePlayerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{25}
}
func (m *ChannelGamePlayerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGamePlayerInfo.Unmarshal(m, b)
}
func (m *ChannelGamePlayerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGamePlayerInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelGamePlayerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGamePlayerInfo.Merge(dst, src)
}
func (m *ChannelGamePlayerInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelGamePlayerInfo.Size(m)
}
func (m *ChannelGamePlayerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGamePlayerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGamePlayerInfo proto.InternalMessageInfo

func (m *ChannelGamePlayerInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *ChannelGamePlayerInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelGamePlayerInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *ChannelGamePlayerInfo) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func (m *ChannelGamePlayerInfo) GetIsLoadingCompleted() bool {
	if m != nil {
		return m.IsLoadingCompleted
	}
	return false
}

func (m *ChannelGamePlayerInfo) GetUserProfile() *app.UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

// 游戏模式信息
type ChannelGameModeInfo struct {
	ModeKey              string   `protobuf:"bytes,1,opt,name=mode_key,json=modeKey,proto3" json:"mode_key,omitempty"`
	GameParam            string   `protobuf:"bytes,2,opt,name=game_param,json=gameParam,proto3" json:"game_param,omitempty"`
	PlayerLimitList      []uint32 `protobuf:"varint,3,rep,packed,name=player_limit_list,json=playerLimitList,proto3" json:"player_limit_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGameModeInfo) Reset()         { *m = ChannelGameModeInfo{} }
func (m *ChannelGameModeInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelGameModeInfo) ProtoMessage()    {}
func (*ChannelGameModeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{26}
}
func (m *ChannelGameModeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGameModeInfo.Unmarshal(m, b)
}
func (m *ChannelGameModeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGameModeInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelGameModeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGameModeInfo.Merge(dst, src)
}
func (m *ChannelGameModeInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelGameModeInfo.Size(m)
}
func (m *ChannelGameModeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGameModeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGameModeInfo proto.InternalMessageInfo

func (m *ChannelGameModeInfo) GetModeKey() string {
	if m != nil {
		return m.ModeKey
	}
	return ""
}

func (m *ChannelGameModeInfo) GetGameParam() string {
	if m != nil {
		return m.GameParam
	}
	return ""
}

func (m *ChannelGameModeInfo) GetPlayerLimitList() []uint32 {
	if m != nil {
		return m.PlayerLimitList
	}
	return nil
}

type ChannelGameInfoStatus struct {
	GameMaster           uint32                   `protobuf:"varint,1,opt,name=game_master,json=gameMaster,proto3" json:"game_master,omitempty"`
	GameStatus           uint32                   `protobuf:"varint,2,opt,name=game_status,json=gameStatus,proto3" json:"game_status,omitempty"`
	GameMode             *ChannelGameModeInfo     `protobuf:"bytes,3,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	GamePlayers          []*ChannelGamePlayerInfo `protobuf:"bytes,4,rep,name=game_players,json=gamePlayers,proto3" json:"game_players,omitempty"`
	LoadSeq              int64                    `protobuf:"varint,5,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	ChangeSeq            int64                    `protobuf:"varint,6,opt,name=change_seq,json=changeSeq,proto3" json:"change_seq,omitempty"`
	Desc                 string                   `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	OtherPlayers         []*ChannelGamePlayerInfo `protobuf:"bytes,8,rep,name=other_players,json=otherPlayers,proto3" json:"other_players,omitempty"`
	SetId                string                   `protobuf:"bytes,9,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ChannelGameInfoStatus) Reset()         { *m = ChannelGameInfoStatus{} }
func (m *ChannelGameInfoStatus) String() string { return proto.CompactTextString(m) }
func (*ChannelGameInfoStatus) ProtoMessage()    {}
func (*ChannelGameInfoStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043, []int{27}
}
func (m *ChannelGameInfoStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGameInfoStatus.Unmarshal(m, b)
}
func (m *ChannelGameInfoStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGameInfoStatus.Marshal(b, m, deterministic)
}
func (dst *ChannelGameInfoStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGameInfoStatus.Merge(dst, src)
}
func (m *ChannelGameInfoStatus) XXX_Size() int {
	return xxx_messageInfo_ChannelGameInfoStatus.Size(m)
}
func (m *ChannelGameInfoStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGameInfoStatus.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGameInfoStatus proto.InternalMessageInfo

func (m *ChannelGameInfoStatus) GetGameMaster() uint32 {
	if m != nil {
		return m.GameMaster
	}
	return 0
}

func (m *ChannelGameInfoStatus) GetGameStatus() uint32 {
	if m != nil {
		return m.GameStatus
	}
	return 0
}

func (m *ChannelGameInfoStatus) GetGameMode() *ChannelGameModeInfo {
	if m != nil {
		return m.GameMode
	}
	return nil
}

func (m *ChannelGameInfoStatus) GetGamePlayers() []*ChannelGamePlayerInfo {
	if m != nil {
		return m.GamePlayers
	}
	return nil
}

func (m *ChannelGameInfoStatus) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

func (m *ChannelGameInfoStatus) GetChangeSeq() int64 {
	if m != nil {
		return m.ChangeSeq
	}
	return 0
}

func (m *ChannelGameInfoStatus) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ChannelGameInfoStatus) GetOtherPlayers() []*ChannelGamePlayerInfo {
	if m != nil {
		return m.OtherPlayers
	}
	return nil
}

func (m *ChannelGameInfoStatus) GetSetId() string {
	if m != nil {
		return m.SetId
	}
	return ""
}

func init() {
	proto.RegisterType((*GetChannelGamePlayerOpenidReq)(nil), "ga.channel_open_game_controller.GetChannelGamePlayerOpenidReq")
	proto.RegisterType((*GetChannelGamePlayerOpenidResp)(nil), "ga.channel_open_game_controller.GetChannelGamePlayerOpenidResp")
	proto.RegisterType((*SetChannelGameModeInfoReq)(nil), "ga.channel_open_game_controller.SetChannelGameModeInfoReq")
	proto.RegisterType((*SetChannelGameModeInfoResp)(nil), "ga.channel_open_game_controller.SetChannelGameModeInfoResp")
	proto.RegisterType((*JoinChannelGameReq)(nil), "ga.channel_open_game_controller.JoinChannelGameReq")
	proto.RegisterType((*JoinChannelGameResp)(nil), "ga.channel_open_game_controller.JoinChannelGameResp")
	proto.RegisterType((*QuitChannelGameReq)(nil), "ga.channel_open_game_controller.QuitChannelGameReq")
	proto.RegisterType((*QuitChannelGameResp)(nil), "ga.channel_open_game_controller.QuitChannelGameResp")
	proto.RegisterType((*ReadyChannelGameReq)(nil), "ga.channel_open_game_controller.ReadyChannelGameReq")
	proto.RegisterType((*ReadyChannelGameResp)(nil), "ga.channel_open_game_controller.ReadyChannelGameResp")
	proto.RegisterType((*UnReadyChannelGameReq)(nil), "ga.channel_open_game_controller.UnReadyChannelGameReq")
	proto.RegisterType((*UnReadyChannelGameResp)(nil), "ga.channel_open_game_controller.UnReadyChannelGameResp")
	proto.RegisterType((*GetChannelGameStatusInfoReq)(nil), "ga.channel_open_game_controller.GetChannelGameStatusInfoReq")
	proto.RegisterType((*GetChannelGameStatusInfoResp)(nil), "ga.channel_open_game_controller.GetChannelGameStatusInfoResp")
	proto.RegisterType((*StartChannelGameReq)(nil), "ga.channel_open_game_controller.StartChannelGameReq")
	proto.RegisterType((*StartChannelGameResp)(nil), "ga.channel_open_game_controller.StartChannelGameResp")
	proto.RegisterType((*ExitChannelGameReq)(nil), "ga.channel_open_game_controller.ExitChannelGameReq")
	proto.RegisterType((*ExitChannelGameResp)(nil), "ga.channel_open_game_controller.ExitChannelGameResp")
	proto.RegisterType((*SetChannelGamePlayerLoadingReq)(nil), "ga.channel_open_game_controller.SetChannelGamePlayerLoadingReq")
	proto.RegisterType((*SetChannelGamePlayerLoadingResp)(nil), "ga.channel_open_game_controller.SetChannelGamePlayerLoadingResp")
	proto.RegisterType((*BatchGetUidByOpenidReq)(nil), "ga.channel_open_game_controller.BatchGetUidByOpenidReq")
	proto.RegisterType((*BatchGetUidByOpenidResp)(nil), "ga.channel_open_game_controller.BatchGetUidByOpenidResp")
	proto.RegisterMapType((map[string]*AccountInfo)(nil), "ga.channel_open_game_controller.BatchGetUidByOpenidResp.AccountMapEntry")
	proto.RegisterType((*AccountInfo)(nil), "ga.channel_open_game_controller.AccountInfo")
	proto.RegisterType((*CheckUserStartGamePermissionRequest)(nil), "ga.channel_open_game_controller.CheckUserStartGamePermissionRequest")
	proto.RegisterType((*CheckUserStartGamePermissionResponse)(nil), "ga.channel_open_game_controller.CheckUserStartGamePermissionResponse")
	proto.RegisterType((*ChannelGamePlayerInfo)(nil), "ga.channel_open_game_controller.ChannelGamePlayerInfo")
	proto.RegisterType((*ChannelGameModeInfo)(nil), "ga.channel_open_game_controller.ChannelGameModeInfo")
	proto.RegisterType((*ChannelGameInfoStatus)(nil), "ga.channel_open_game_controller.ChannelGameInfoStatus")
}

func init() {
	proto.RegisterFile("channel-open-game-controller-logic_.proto", fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043)
}

var fileDescriptor_channel_open_game_controller_logic__e70bd2bc802c0043 = []byte{
	// 1109 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x57, 0x5f, 0x4f, 0x24, 0x45,
	0x10, 0xcf, 0xb0, 0xb0, 0x3b, 0x53, 0xcb, 0x06, 0x6d, 0x0e, 0x18, 0x38, 0xef, 0x20, 0xa3, 0x31,
	0x9c, 0x91, 0xc1, 0xe0, 0x9f, 0x78, 0x3e, 0x79, 0x90, 0x0b, 0xa2, 0xa0, 0x5c, 0x13, 0x1e, 0xd4,
	0x98, 0x49, 0x33, 0x53, 0x37, 0x74, 0x98, 0x99, 0x9e, 0x9d, 0xee, 0x45, 0x56, 0x1f, 0x8c, 0x0f,
	0xbe, 0xfa, 0xe0, 0x9b, 0xd1, 0x8f, 0xe1, 0x97, 0xf0, 0xcd, 0x8f, 0x64, 0xba, 0x67, 0x96, 0xbd,
	0x75, 0x59, 0xc8, 0xe4, 0xcc, 0xde, 0x5b, 0x57, 0x57, 0x4d, 0xfd, 0x7e, 0xd5, 0x55, 0x5d, 0x5d,
	0x03, 0x8f, 0xc2, 0x73, 0x96, 0x65, 0x98, 0x6c, 0x89, 0x1c, 0xb3, 0xad, 0x98, 0xa5, 0xb8, 0x15,
	0x8a, 0x4c, 0x15, 0x22, 0x49, 0xb0, 0xd8, 0x4a, 0x44, 0xcc, 0xc3, 0xc0, 0xcf, 0x0b, 0xa1, 0x04,
	0x59, 0x8f, 0x99, 0x5f, 0x59, 0x07, 0xda, 0x3a, 0xd0, 0xd6, 0xc1, 0xd0, 0x7a, 0xad, 0x13, 0xb3,
	0xe0, 0x8c, 0x49, 0x2c, 0xed, 0xbd, 0x9f, 0x2d, 0x78, 0xb0, 0x8f, 0x6a, 0xaf, 0xfc, 0x64, 0x9f,
	0xa5, 0x78, 0x9c, 0xb0, 0x3e, 0x16, 0x5f, 0xe5, 0x98, 0xf1, 0x88, 0x62, 0x97, 0xbc, 0x0d, 0xb6,
	0xb6, 0x0f, 0x0a, 0xec, 0xba, 0xd6, 0x86, 0xb5, 0xd9, 0xde, 0x69, 0xfb, 0x31, 0xf3, 0x77, 0x99,
	0x44, 0x8a, 0x5d, 0xda, 0x3a, 0x2b, 0x17, 0xe4, 0x01, 0xc0, 0x00, 0x98, 0x47, 0xee, 0xcc, 0x86,
	0xb5, 0xd9, 0xa1, 0x4e, 0xb5, 0x73, 0x10, 0x91, 0x55, 0xb0, 0x13, 0xc1, 0xa2, 0x40, 0x62, 0xd7,
	0x6d, 0x6c, 0x58, 0x9b, 0x0d, 0xda, 0xd2, 0xf2, 0x09, 0x76, 0xbd, 0x2b, 0x78, 0x78, 0x1b, 0x05,
	0x99, 0x93, 0x47, 0xe0, 0x54, 0x1c, 0x64, 0x5e, 0x91, 0x98, 0x1f, 0x92, 0x90, 0x39, 0xb5, 0xcf,
	0xaa, 0x15, 0x59, 0x81, 0x96, 0x89, 0xbb, 0xe2, 0xe0, 0xd0, 0xa6, 0x16, 0x0f, 0x22, 0x42, 0x60,
	0x36, 0x14, 0x11, 0x1a, 0x70, 0x87, 0x9a, 0xb5, 0xf7, 0x8f, 0x05, 0xab, 0x27, 0x23, 0xd0, 0x47,
	0x22, 0xc2, 0x83, 0xec, 0xb9, 0x98, 0x4a, 0xe4, 0xe4, 0x19, 0x38, 0x26, 0x3f, 0xa9, 0x26, 0x36,
	0x6b, 0x20, 0x3e, 0xf0, 0xef, 0xc8, 0xa0, 0x7f, 0x13, 0x5b, 0x3b, 0xae, 0x24, 0x6f, 0x1f, 0xd6,
	0x26, 0x45, 0x54, 0xeb, 0x20, 0xbd, 0x4b, 0x20, 0x9f, 0x0b, 0x9e, 0xbd, 0xe0, 0x69, 0x3a, 0xd5,
	0xf0, 0x1d, 0x2c, 0x8e, 0xe1, 0xd6, 0x2b, 0x81, 0xfb, 0xe0, 0xa4, 0x4c, 0x85, 0xe7, 0xc6, 0x7b,
	0x09, 0x6d, 0x9b, 0x0d, 0xed, 0xfe, 0x12, 0xc8, 0xb3, 0x1e, 0x57, 0x53, 0x0f, 0xeb, 0x53, 0x58,
	0x1c, 0xc3, 0xad, 0x97, 0x90, 0xef, 0x61, 0x91, 0x22, 0x8b, 0xfa, 0x53, 0xa7, 0xfe, 0x04, 0xee,
	0x8d, 0x03, 0xd7, 0xe3, 0xde, 0x87, 0xa5, 0xd3, 0xec, 0xd5, 0xb0, 0xdf, 0x83, 0xe5, 0x9b, 0xa0,
	0xeb, 0xf1, 0xff, 0x09, 0xee, 0x8f, 0xb6, 0xa8, 0x13, 0xc5, 0x54, 0x4f, 0x4e, 0xad, 0x53, 0x78,
	0xbf, 0x5b, 0xf0, 0xc6, 0x64, 0x06, 0xf5, 0xee, 0xc7, 0x97, 0xd0, 0x94, 0xe6, 0x63, 0xc3, 0xa0,
	0xbd, 0xf3, 0x51, 0x9d, 0x96, 0xa3, 0x01, 0x4b, 0x68, 0x5a, 0x79, 0xd1, 0x85, 0x79, 0xa2, 0x58,
	0xa1, 0x5e, 0x45, 0x61, 0x8e, 0x03, 0xd7, 0x4b, 0xec, 0xaf, 0x16, 0x90, 0xa7, 0x57, 0xd3, 0xef,
	0x07, 0x64, 0x19, 0x9a, 0x05, 0x32, 0x29, 0x32, 0xd3, 0xf7, 0x3b, 0xb4, 0x92, 0x74, 0x9f, 0x18,
	0xe3, 0x53, 0x2f, 0xa4, 0xbf, 0x2c, 0x78, 0x78, 0x72, 0xc3, 0x7b, 0x7a, 0x28, 0x58, 0xc4, 0xb3,
	0x78, 0x3a, 0xe1, 0xbd, 0x07, 0xf7, 0xb8, 0x0c, 0x92, 0x12, 0x32, 0x08, 0x45, 0x9a, 0x27, 0xa8,
	0x30, 0x32, 0xc1, 0xda, 0x94, 0x70, 0x59, 0xb1, 0xd9, 0x1b, 0x68, 0xbc, 0x43, 0x58, 0xbf, 0x95,
	0x75, 0xbd, 0x43, 0xf8, 0x01, 0x96, 0x77, 0x75, 0xcb, 0xdf, 0x47, 0x75, 0xca, 0xa3, 0xdd, 0x7e,
	0xfd, 0x79, 0x66, 0x05, 0x5a, 0xe6, 0x1a, 0x5c, 0x07, 0xde, 0xd4, 0xe2, 0x41, 0x44, 0xd6, 0xa1,
	0x2d, 0x8c, 0xb7, 0x20, 0xe1, 0x52, 0xb9, 0x8d, 0x8d, 0xc6, 0xa6, 0x43, 0xa1, 0xdc, 0x3a, 0xe4,
	0x52, 0x79, 0x7f, 0xce, 0xc0, 0xca, 0x8d, 0xe0, 0xf5, 0xae, 0x29, 0x87, 0x36, 0x0b, 0x43, 0xd1,
	0xcb, 0x54, 0x90, 0xb2, 0xdc, 0x9d, 0xd9, 0x68, 0x6c, 0xb6, 0x77, 0x3e, 0xbb, 0xf3, 0xae, 0x4e,
	0x40, 0xf6, 0x9f, 0x94, 0xbe, 0x8e, 0x58, 0xfe, 0x34, 0x53, 0x45, 0x9f, 0x02, 0xbb, 0xde, 0x58,
	0xbb, 0x80, 0x85, 0xff, 0xa8, 0xc9, 0x6b, 0xd0, 0xb8, 0xc0, 0xbe, 0xa1, 0xe8, 0x50, 0xbd, 0x24,
	0xbb, 0x30, 0x77, 0xc9, 0x92, 0x1e, 0x56, 0x5d, 0xe3, 0xdd, 0x3b, 0x99, 0x54, 0x2e, 0x4d, 0x8b,
	0x2a, 0x3f, 0xfd, 0x64, 0xe6, 0x63, 0xcb, 0x7b, 0x0c, 0xed, 0x17, 0x34, 0x1a, 0xa8, 0xc7, 0x23,
	0x03, 0xd4, 0xa1, 0x7a, 0x49, 0x5c, 0x68, 0x55, 0xdc, 0xaa, 0x11, 0x6e, 0x20, 0x7a, 0xbf, 0x59,
	0xf0, 0xe6, 0xde, 0x39, 0x86, 0x17, 0xa7, 0x12, 0x0b, 0x73, 0xf5, 0x4d, 0xa1, 0x60, 0x91, 0x72,
	0x29, 0xb9, 0xc8, 0x28, 0x76, 0x7b, 0x28, 0xd5, 0xcb, 0xe7, 0xf8, 0x1d, 0x78, 0x9d, 0xcb, 0x20,
	0x17, 0x79, 0x90, 0x5f, 0x3b, 0x37, 0x25, 0x6e, 0xd3, 0x05, 0x2e, 0x8f, 0x45, 0x3e, 0xc4, 0xf4,
	0x7e, 0xb1, 0xe0, 0xad, 0xdb, 0x49, 0xc9, 0x5c, 0x64, 0x12, 0xeb, 0xe4, 0x7e, 0x15, 0x6c, 0x2c,
	0x8a, 0xc0, 0x0c, 0xac, 0x9a, 0xd9, 0x1c, 0x6d, 0x61, 0x51, 0xec, 0x89, 0x08, 0x35, 0x67, 0xad,
	0x4a, 0x65, 0x5c, 0x8d, 0xb2, 0x4d, 0x2c, 0x8a, 0x23, 0x19, 0x7b, 0x7f, 0xcc, 0xc0, 0xd2, 0xd8,
	0xf5, 0x99, 0x70, 0xc4, 0xcb, 0x23, 0x4f, 0x40, 0x67, 0xd0, 0xca, 0xc9, 0x1a, 0xd8, 0x3d, 0x89,
	0x45, 0xc6, 0xd2, 0xc1, 0xa0, 0x7c, 0x2d, 0x6b, 0x5d, 0xc6, 0xc3, 0x0b, 0xa3, 0x9b, 0x2d, 0x75,
	0x03, 0x59, 0x23, 0x48, 0xbc, 0x72, 0xe7, 0x4a, 0x04, 0x89, 0x57, 0x1a, 0xa1, 0xbc, 0x12, 0x6e,
	0x73, 0x38, 0x86, 0xf3, 0xa8, 0xb4, 0xec, 0xba, 0xad, 0x81, 0xe5, 0xe4, 0x56, 0x61, 0x4f, 0x6a,
	0x15, 0x64, 0x07, 0xe6, 0x35, 0xab, 0x20, 0x2f, 0xc4, 0x73, 0x9e, 0xa0, 0xeb, 0x98, 0xb3, 0x5c,
	0xd0, 0x67, 0xa9, 0x73, 0x70, 0x5c, 0x6e, 0xd3, 0x76, 0x6f, 0x28, 0x78, 0x3f, 0xc2, 0xe2, 0x0d,
	0x43, 0xb1, 0x3e, 0x68, 0x3d, 0x7c, 0x07, 0xc3, 0x5a, 0x6f, 0x69, 0xf9, 0x0b, 0xec, 0xeb, 0xe6,
	0x67, 0x8a, 0x23, 0x67, 0x05, 0x4b, 0xab, 0x4a, 0x34, 0xe3, 0xfa, 0xb1, 0xde, 0xd0, 0x25, 0x92,
	0x9b, 0x23, 0x0e, 0x12, 0x9e, 0x72, 0x35, 0x6c, 0x06, 0x1d, 0xba, 0x50, 0x2a, 0x0e, 0xf5, 0xbe,
	0xe9, 0x08, 0x7f, 0x37, 0x46, 0x52, 0x33, 0x7c, 0x43, 0x75, 0x33, 0x29, 0xff, 0x00, 0x98, 0x54,
	0x58, 0x54, 0x29, 0x32, 0xb8, 0x47, 0x66, 0xe7, 0xda, 0x60, 0x24, 0x5d, 0xc6, 0xa0, 0xf2, 0x30,
	0xf2, 0x0f, 0xd1, 0xf8, 0x3f, 0xfe, 0x21, 0xc8, 0xd7, 0x30, 0x5f, 0x46, 0x6e, 0xc2, 0x90, 0xee,
	0xac, 0x69, 0x3d, 0xb5, 0xc6, 0x84, 0x61, 0xf5, 0x51, 0xc3, 0xbf, 0x94, 0xe5, 0xc8, 0x93, 0x31,
	0x37, 0xfa, 0x64, 0x54, 0x8f, 0x4d, 0x8c, 0x46, 0xd9, 0x34, 0x4a, 0xa7, 0xdc, 0xd1, 0x6a, 0x02,
	0xb3, 0x11, 0xca, 0xd0, 0x54, 0x8e, 0x43, 0xcd, 0x9a, 0x7c, 0x0b, 0x1d, 0xa1, 0xce, 0x75, 0x25,
	0x54, 0x4c, 0xed, 0x97, 0x62, 0x3a, 0x6f, 0x9c, 0x0d, 0xa8, 0x2e, 0x41, 0x53, 0xa2, 0xd2, 0xbd,
	0xc1, 0x31, 0x90, 0x73, 0x12, 0xd5, 0x41, 0xb4, 0x7b, 0x0a, 0x6e, 0x28, 0x52, 0xbf, 0xcf, 0xfb,
	0xa2, 0xa7, 0x71, 0xf4, 0xb9, 0x27, 0xe5, 0xdf, 0xf4, 0x37, 0x8f, 0x63, 0x91, 0xb0, 0x2c, 0xf6,
	0x3f, 0xdc, 0x51, 0xca, 0x0f, 0x45, 0xba, 0x6d, 0xb6, 0x43, 0x91, 0x6c, 0xb3, 0x3c, 0xdf, 0xbe,
	0xed, 0x2f, 0xfe, 0xac, 0x69, 0x4c, 0xdf, 0xff, 0x37, 0x00, 0x00, 0xff, 0xff, 0xfc, 0x68, 0x03,
	0xb2, 0xec, 0x0f, 0x00, 0x00,
}
