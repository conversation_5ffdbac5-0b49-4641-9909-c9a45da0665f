// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse-post-logic_.proto

package muse_post_logic // import "golang.52tt.com/protocol/app/muse-post-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MuseContentType int32

const (
	MuseContentType_MuseContentType_UNDEFINED MuseContentType = 0
	MuseContentType_MuseContentType_PLAINTEXT MuseContentType = 1
)

var MuseContentType_name = map[int32]string{
	0: "MuseContentType_UNDEFINED",
	1: "MuseContentType_PLAINTEXT",
}
var MuseContentType_value = map[string]int32{
	"MuseContentType_UNDEFINED": 0,
	"MuseContentType_PLAINTEXT": 1,
}

func (x MuseContentType) String() string {
	return proto.EnumName(MuseContentType_name, int32(x))
}
func (MuseContentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{0}
}

type ContentStatus int32

const (
	ContentStatus_ContentStatus_UNDEFINED ContentStatus = 0
	ContentStatus_ContentStatus_EXAMINING ContentStatus = 1
	ContentStatus_ContentStatus_ILLEGAL   ContentStatus = 2
	ContentStatus_ContentStatus_NORMAL    ContentStatus = 3
	ContentStatus_ContentStatus_DELETED   ContentStatus = 4
)

var ContentStatus_name = map[int32]string{
	0: "ContentStatus_UNDEFINED",
	1: "ContentStatus_EXAMINING",
	2: "ContentStatus_ILLEGAL",
	3: "ContentStatus_NORMAL",
	4: "ContentStatus_DELETED",
}
var ContentStatus_value = map[string]int32{
	"ContentStatus_UNDEFINED": 0,
	"ContentStatus_EXAMINING": 1,
	"ContentStatus_ILLEGAL":   2,
	"ContentStatus_NORMAL":    3,
	"ContentStatus_DELETED":   4,
}

func (x ContentStatus) String() string {
	return proto.EnumName(ContentStatus_name, int32(x))
}
func (ContentStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{1}
}

type MuseInteractiveMsgType int32

const (
	MuseInteractiveMsgType_MuseInteractiveMsgType_UNDEFINED MuseInteractiveMsgType = 0
	MuseInteractiveMsgType_MuseInteractiveMsgType_COMMENT   MuseInteractiveMsgType = 1
	MuseInteractiveMsgType_MuseInteractiveMsgType_LIKE      MuseInteractiveMsgType = 2
)

var MuseInteractiveMsgType_name = map[int32]string{
	0: "MuseInteractiveMsgType_UNDEFINED",
	1: "MuseInteractiveMsgType_COMMENT",
	2: "MuseInteractiveMsgType_LIKE",
}
var MuseInteractiveMsgType_value = map[string]int32{
	"MuseInteractiveMsgType_UNDEFINED": 0,
	"MuseInteractiveMsgType_COMMENT":   1,
	"MuseInteractiveMsgType_LIKE":      2,
}

func (x MuseInteractiveMsgType) String() string {
	return proto.EnumName(MuseInteractiveMsgType_name, int32(x))
}
func (MuseInteractiveMsgType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{2}
}

// 获取留言列表
type GetMusePostListReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32             `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32             `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMusePostListReq) Reset()         { *m = GetMusePostListReq{} }
func (m *GetMusePostListReq) String() string { return proto.CompactTextString(m) }
func (*GetMusePostListReq) ProtoMessage()    {}
func (*GetMusePostListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{0}
}
func (m *GetMusePostListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusePostListReq.Unmarshal(m, b)
}
func (m *GetMusePostListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusePostListReq.Marshal(b, m, deterministic)
}
func (dst *GetMusePostListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusePostListReq.Merge(dst, src)
}
func (m *GetMusePostListReq) XXX_Size() int {
	return xxx_messageInfo_GetMusePostListReq.Size(m)
}
func (m *GetMusePostListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusePostListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusePostListReq proto.InternalMessageInfo

func (m *GetMusePostListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusePostListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMusePostListReq) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetMusePostListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetMusePostListResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Posts                []*MusePost        `protobuf:"bytes,2,rep,name=posts,proto3" json:"posts,omitempty"`
	UnreadMsgCount       uint32             `protobuf:"varint,3,opt,name=unread_msg_count,json=unreadMsgCount,proto3" json:"unread_msg_count,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMusePostListResp) Reset()         { *m = GetMusePostListResp{} }
func (m *GetMusePostListResp) String() string { return proto.CompactTextString(m) }
func (*GetMusePostListResp) ProtoMessage()    {}
func (*GetMusePostListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{1}
}
func (m *GetMusePostListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusePostListResp.Unmarshal(m, b)
}
func (m *GetMusePostListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusePostListResp.Marshal(b, m, deterministic)
}
func (dst *GetMusePostListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusePostListResp.Merge(dst, src)
}
func (m *GetMusePostListResp) XXX_Size() int {
	return xxx_messageInfo_GetMusePostListResp.Size(m)
}
func (m *GetMusePostListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusePostListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusePostListResp proto.InternalMessageInfo

func (m *GetMusePostListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusePostListResp) GetPosts() []*MusePost {
	if m != nil {
		return m.Posts
	}
	return nil
}

func (m *GetMusePostListResp) GetUnreadMsgCount() uint32 {
	if m != nil {
		return m.UnreadMsgCount
	}
	return 0
}

func (m *GetMusePostListResp) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

// 获取个人留言列表
type GetMyMusePostListReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32             `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32             `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMyMusePostListReq) Reset()         { *m = GetMyMusePostListReq{} }
func (m *GetMyMusePostListReq) String() string { return proto.CompactTextString(m) }
func (*GetMyMusePostListReq) ProtoMessage()    {}
func (*GetMyMusePostListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{2}
}
func (m *GetMyMusePostListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyMusePostListReq.Unmarshal(m, b)
}
func (m *GetMyMusePostListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyMusePostListReq.Marshal(b, m, deterministic)
}
func (dst *GetMyMusePostListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyMusePostListReq.Merge(dst, src)
}
func (m *GetMyMusePostListReq) XXX_Size() int {
	return xxx_messageInfo_GetMyMusePostListReq.Size(m)
}
func (m *GetMyMusePostListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyMusePostListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyMusePostListReq proto.InternalMessageInfo

func (m *GetMyMusePostListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMyMusePostListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMyMusePostListReq) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetMyMusePostListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetMyMusePostListResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Posts                []*MusePost        `protobuf:"bytes,2,rep,name=posts,proto3" json:"posts,omitempty"`
	UnreadMsgCount       uint32             `protobuf:"varint,3,opt,name=unread_msg_count,json=unreadMsgCount,proto3" json:"unread_msg_count,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMyMusePostListResp) Reset()         { *m = GetMyMusePostListResp{} }
func (m *GetMyMusePostListResp) String() string { return proto.CompactTextString(m) }
func (*GetMyMusePostListResp) ProtoMessage()    {}
func (*GetMyMusePostListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{3}
}
func (m *GetMyMusePostListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMyMusePostListResp.Unmarshal(m, b)
}
func (m *GetMyMusePostListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMyMusePostListResp.Marshal(b, m, deterministic)
}
func (dst *GetMyMusePostListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMyMusePostListResp.Merge(dst, src)
}
func (m *GetMyMusePostListResp) XXX_Size() int {
	return xxx_messageInfo_GetMyMusePostListResp.Size(m)
}
func (m *GetMyMusePostListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMyMusePostListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMyMusePostListResp proto.InternalMessageInfo

func (m *GetMyMusePostListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMyMusePostListResp) GetPosts() []*MusePost {
	if m != nil {
		return m.Posts
	}
	return nil
}

func (m *GetMyMusePostListResp) GetUnreadMsgCount() uint32 {
	if m != nil {
		return m.UnreadMsgCount
	}
	return 0
}

func (m *GetMyMusePostListResp) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type MusePost struct {
	Id                   string                `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ContentType          MuseContentType       `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3,enum=ga.muse_post_logic.MuseContentType" json:"content_type,omitempty"`
	Text                 string                `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	UserInfo             *MuseUserInfo         `protobuf:"bytes,4,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	Status               ContentStatus         `protobuf:"varint,5,opt,name=status,proto3,enum=ga.muse_post_logic.ContentStatus" json:"status,omitempty"`
	CommentCount         uint32                `protobuf:"varint,6,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	LikeCount            uint32                `protobuf:"varint,7,opt,name=like_count,json=likeCount,proto3" json:"like_count,omitempty"`
	CreatedAt            uint32                `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	HaveLike             bool                  `protobuf:"varint,9,opt,name=have_like,json=haveLike,proto3" json:"have_like,omitempty"`
	MoodId               string                `protobuf:"bytes,10,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	HaveAt               bool                  `protobuf:"varint,11,opt,name=have_at,json=haveAt,proto3" json:"have_at,omitempty"`
	MoodInfo             *MusePostUserMoodInfo `protobuf:"bytes,12,opt,name=mood_info,json=moodInfo,proto3" json:"mood_info,omitempty"`
	Source               string                `protobuf:"bytes,13,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *MusePost) Reset()         { *m = MusePost{} }
func (m *MusePost) String() string { return proto.CompactTextString(m) }
func (*MusePost) ProtoMessage()    {}
func (*MusePost) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{4}
}
func (m *MusePost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusePost.Unmarshal(m, b)
}
func (m *MusePost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusePost.Marshal(b, m, deterministic)
}
func (dst *MusePost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusePost.Merge(dst, src)
}
func (m *MusePost) XXX_Size() int {
	return xxx_messageInfo_MusePost.Size(m)
}
func (m *MusePost) XXX_DiscardUnknown() {
	xxx_messageInfo_MusePost.DiscardUnknown(m)
}

var xxx_messageInfo_MusePost proto.InternalMessageInfo

func (m *MusePost) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MusePost) GetContentType() MuseContentType {
	if m != nil {
		return m.ContentType
	}
	return MuseContentType_MuseContentType_UNDEFINED
}

func (m *MusePost) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *MusePost) GetUserInfo() *MuseUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *MusePost) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_ContentStatus_UNDEFINED
}

func (m *MusePost) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *MusePost) GetLikeCount() uint32 {
	if m != nil {
		return m.LikeCount
	}
	return 0
}

func (m *MusePost) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *MusePost) GetHaveLike() bool {
	if m != nil {
		return m.HaveLike
	}
	return false
}

func (m *MusePost) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *MusePost) GetHaveAt() bool {
	if m != nil {
		return m.HaveAt
	}
	return false
}

func (m *MusePost) GetMoodInfo() *MusePostUserMoodInfo {
	if m != nil {
		return m.MoodInfo
	}
	return nil
}

func (m *MusePost) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type MusePostUserMoodInfo struct {
	MoodId               string   `protobuf:"bytes,1,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	MoodUrl              string   `protobuf:"bytes,2,opt,name=mood_url,json=moodUrl,proto3" json:"mood_url,omitempty"`
	MoodMd5              string   `protobuf:"bytes,3,opt,name=mood_md5,json=moodMd5,proto3" json:"mood_md5,omitempty"`
	MoodText             string   `protobuf:"bytes,4,opt,name=mood_text,json=moodText,proto3" json:"mood_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusePostUserMoodInfo) Reset()         { *m = MusePostUserMoodInfo{} }
func (m *MusePostUserMoodInfo) String() string { return proto.CompactTextString(m) }
func (*MusePostUserMoodInfo) ProtoMessage()    {}
func (*MusePostUserMoodInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{5}
}
func (m *MusePostUserMoodInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusePostUserMoodInfo.Unmarshal(m, b)
}
func (m *MusePostUserMoodInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusePostUserMoodInfo.Marshal(b, m, deterministic)
}
func (dst *MusePostUserMoodInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusePostUserMoodInfo.Merge(dst, src)
}
func (m *MusePostUserMoodInfo) XXX_Size() int {
	return xxx_messageInfo_MusePostUserMoodInfo.Size(m)
}
func (m *MusePostUserMoodInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusePostUserMoodInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusePostUserMoodInfo proto.InternalMessageInfo

func (m *MusePostUserMoodInfo) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

func (m *MusePostUserMoodInfo) GetMoodUrl() string {
	if m != nil {
		return m.MoodUrl
	}
	return ""
}

func (m *MusePostUserMoodInfo) GetMoodMd5() string {
	if m != nil {
		return m.MoodMd5
	}
	return ""
}

func (m *MusePostUserMoodInfo) GetMoodText() string {
	if m != nil {
		return m.MoodText
	}
	return ""
}

// 获取一级评论列表
type GetMuseParentCommentListReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId               string             `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32             `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	ExtCommentId         string             `protobuf:"bytes,5,opt,name=ext_comment_id,json=extCommentId,proto3" json:"ext_comment_id,omitempty"`
	ChannelId            uint32             `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMuseParentCommentListReq) Reset()         { *m = GetMuseParentCommentListReq{} }
func (m *GetMuseParentCommentListReq) String() string { return proto.CompactTextString(m) }
func (*GetMuseParentCommentListReq) ProtoMessage()    {}
func (*GetMuseParentCommentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{6}
}
func (m *GetMuseParentCommentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseParentCommentListReq.Unmarshal(m, b)
}
func (m *GetMuseParentCommentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseParentCommentListReq.Marshal(b, m, deterministic)
}
func (dst *GetMuseParentCommentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseParentCommentListReq.Merge(dst, src)
}
func (m *GetMuseParentCommentListReq) XXX_Size() int {
	return xxx_messageInfo_GetMuseParentCommentListReq.Size(m)
}
func (m *GetMuseParentCommentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseParentCommentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseParentCommentListReq proto.InternalMessageInfo

func (m *GetMuseParentCommentListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMuseParentCommentListReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetMuseParentCommentListReq) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetMuseParentCommentListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetMuseParentCommentListReq) GetExtCommentId() string {
	if m != nil {
		return m.ExtCommentId
	}
	return ""
}

func (m *GetMuseParentCommentListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMuseParentCommentListResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Comments             []*MuseParentComment `protobuf:"bytes,2,rep,name=comments,proto3" json:"comments,omitempty"`
	LoadMore             *MuseFeedsLoadMore   `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMuseParentCommentListResp) Reset()         { *m = GetMuseParentCommentListResp{} }
func (m *GetMuseParentCommentListResp) String() string { return proto.CompactTextString(m) }
func (*GetMuseParentCommentListResp) ProtoMessage()    {}
func (*GetMuseParentCommentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{7}
}
func (m *GetMuseParentCommentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseParentCommentListResp.Unmarshal(m, b)
}
func (m *GetMuseParentCommentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseParentCommentListResp.Marshal(b, m, deterministic)
}
func (dst *GetMuseParentCommentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseParentCommentListResp.Merge(dst, src)
}
func (m *GetMuseParentCommentListResp) XXX_Size() int {
	return xxx_messageInfo_GetMuseParentCommentListResp.Size(m)
}
func (m *GetMuseParentCommentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseParentCommentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseParentCommentListResp proto.InternalMessageInfo

func (m *GetMuseParentCommentListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMuseParentCommentListResp) GetComments() []*MuseParentComment {
	if m != nil {
		return m.Comments
	}
	return nil
}

func (m *GetMuseParentCommentListResp) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type MuseParentComment struct {
	Comment              *MuseComment       `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
	SubComments          []*MuseComment     `protobuf:"bytes,2,rep,name=sub_comments,json=subComments,proto3" json:"sub_comments,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	SubCommentCount      uint32             `protobuf:"varint,4,opt,name=sub_comment_count,json=subCommentCount,proto3" json:"sub_comment_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MuseParentComment) Reset()         { *m = MuseParentComment{} }
func (m *MuseParentComment) String() string { return proto.CompactTextString(m) }
func (*MuseParentComment) ProtoMessage()    {}
func (*MuseParentComment) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{8}
}
func (m *MuseParentComment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseParentComment.Unmarshal(m, b)
}
func (m *MuseParentComment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseParentComment.Marshal(b, m, deterministic)
}
func (dst *MuseParentComment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseParentComment.Merge(dst, src)
}
func (m *MuseParentComment) XXX_Size() int {
	return xxx_messageInfo_MuseParentComment.Size(m)
}
func (m *MuseParentComment) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseParentComment.DiscardUnknown(m)
}

var xxx_messageInfo_MuseParentComment proto.InternalMessageInfo

func (m *MuseParentComment) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *MuseParentComment) GetSubComments() []*MuseComment {
	if m != nil {
		return m.SubComments
	}
	return nil
}

func (m *MuseParentComment) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *MuseParentComment) GetSubCommentCount() uint32 {
	if m != nil {
		return m.SubCommentCount
	}
	return 0
}

// 获取二级评论列表
type GetMuseSubCommentListReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ParentCommentId      string             `protobuf:"bytes,2,opt,name=parent_comment_id,json=parentCommentId,proto3" json:"parent_comment_id,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32             `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMuseSubCommentListReq) Reset()         { *m = GetMuseSubCommentListReq{} }
func (m *GetMuseSubCommentListReq) String() string { return proto.CompactTextString(m) }
func (*GetMuseSubCommentListReq) ProtoMessage()    {}
func (*GetMuseSubCommentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{9}
}
func (m *GetMuseSubCommentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseSubCommentListReq.Unmarshal(m, b)
}
func (m *GetMuseSubCommentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseSubCommentListReq.Marshal(b, m, deterministic)
}
func (dst *GetMuseSubCommentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseSubCommentListReq.Merge(dst, src)
}
func (m *GetMuseSubCommentListReq) XXX_Size() int {
	return xxx_messageInfo_GetMuseSubCommentListReq.Size(m)
}
func (m *GetMuseSubCommentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseSubCommentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseSubCommentListReq proto.InternalMessageInfo

func (m *GetMuseSubCommentListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMuseSubCommentListReq) GetParentCommentId() string {
	if m != nil {
		return m.ParentCommentId
	}
	return ""
}

func (m *GetMuseSubCommentListReq) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetMuseSubCommentListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetMuseSubCommentListResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Comments             []*MuseComment     `protobuf:"bytes,2,rep,name=comments,proto3" json:"comments,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMuseSubCommentListResp) Reset()         { *m = GetMuseSubCommentListResp{} }
func (m *GetMuseSubCommentListResp) String() string { return proto.CompactTextString(m) }
func (*GetMuseSubCommentListResp) ProtoMessage()    {}
func (*GetMuseSubCommentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{10}
}
func (m *GetMuseSubCommentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseSubCommentListResp.Unmarshal(m, b)
}
func (m *GetMuseSubCommentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseSubCommentListResp.Marshal(b, m, deterministic)
}
func (dst *GetMuseSubCommentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseSubCommentListResp.Merge(dst, src)
}
func (m *GetMuseSubCommentListResp) XXX_Size() int {
	return xxx_messageInfo_GetMuseSubCommentListResp.Size(m)
}
func (m *GetMuseSubCommentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseSubCommentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseSubCommentListResp proto.InternalMessageInfo

func (m *GetMuseSubCommentListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMuseSubCommentListResp) GetComments() []*MuseComment {
	if m != nil {
		return m.Comments
	}
	return nil
}

func (m *GetMuseSubCommentListResp) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type MuseComment struct {
	Id                   string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ContentType          MuseContentType `protobuf:"varint,2,opt,name=content_type,json=contentType,proto3,enum=ga.muse_post_logic.MuseContentType" json:"content_type,omitempty"`
	Text                 string          `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	UserInfo             *MuseUserInfo   `protobuf:"bytes,4,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	Status               ContentStatus   `protobuf:"varint,5,opt,name=status,proto3,enum=ga.muse_post_logic.ContentStatus" json:"status,omitempty"`
	CreatedAt            uint32          `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ReplyToUserInfo      *MuseUserInfo   `protobuf:"bytes,7,opt,name=reply_to_user_info,json=replyToUserInfo,proto3" json:"reply_to_user_info,omitempty"`
	HaveAt               bool            `protobuf:"varint,8,opt,name=have_at,json=haveAt,proto3" json:"have_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MuseComment) Reset()         { *m = MuseComment{} }
func (m *MuseComment) String() string { return proto.CompactTextString(m) }
func (*MuseComment) ProtoMessage()    {}
func (*MuseComment) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{11}
}
func (m *MuseComment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseComment.Unmarshal(m, b)
}
func (m *MuseComment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseComment.Marshal(b, m, deterministic)
}
func (dst *MuseComment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseComment.Merge(dst, src)
}
func (m *MuseComment) XXX_Size() int {
	return xxx_messageInfo_MuseComment.Size(m)
}
func (m *MuseComment) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseComment.DiscardUnknown(m)
}

var xxx_messageInfo_MuseComment proto.InternalMessageInfo

func (m *MuseComment) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MuseComment) GetContentType() MuseContentType {
	if m != nil {
		return m.ContentType
	}
	return MuseContentType_MuseContentType_UNDEFINED
}

func (m *MuseComment) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *MuseComment) GetUserInfo() *MuseUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *MuseComment) GetStatus() ContentStatus {
	if m != nil {
		return m.Status
	}
	return ContentStatus_ContentStatus_UNDEFINED
}

func (m *MuseComment) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *MuseComment) GetReplyToUserInfo() *MuseUserInfo {
	if m != nil {
		return m.ReplyToUserInfo
	}
	return nil
}

func (m *MuseComment) GetHaveAt() bool {
	if m != nil {
		return m.HaveAt
	}
	return false
}

// 获取点赞列表
type GetMuseContentLikeListReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32             `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PostId               string             `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string             `protobuf:"bytes,4,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,5,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32             `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMuseContentLikeListReq) Reset()         { *m = GetMuseContentLikeListReq{} }
func (m *GetMuseContentLikeListReq) String() string { return proto.CompactTextString(m) }
func (*GetMuseContentLikeListReq) ProtoMessage()    {}
func (*GetMuseContentLikeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{12}
}
func (m *GetMuseContentLikeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseContentLikeListReq.Unmarshal(m, b)
}
func (m *GetMuseContentLikeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseContentLikeListReq.Marshal(b, m, deterministic)
}
func (dst *GetMuseContentLikeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseContentLikeListReq.Merge(dst, src)
}
func (m *GetMuseContentLikeListReq) XXX_Size() int {
	return xxx_messageInfo_GetMuseContentLikeListReq.Size(m)
}
func (m *GetMuseContentLikeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseContentLikeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseContentLikeListReq proto.InternalMessageInfo

func (m *GetMuseContentLikeListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMuseContentLikeListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMuseContentLikeListReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetMuseContentLikeListReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *GetMuseContentLikeListReq) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetMuseContentLikeListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetMuseContentLikeListResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Likes                []*MuseLike        `protobuf:"bytes,2,rep,name=likes,proto3" json:"likes,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMuseContentLikeListResp) Reset()         { *m = GetMuseContentLikeListResp{} }
func (m *GetMuseContentLikeListResp) String() string { return proto.CompactTextString(m) }
func (*GetMuseContentLikeListResp) ProtoMessage()    {}
func (*GetMuseContentLikeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{13}
}
func (m *GetMuseContentLikeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseContentLikeListResp.Unmarshal(m, b)
}
func (m *GetMuseContentLikeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseContentLikeListResp.Marshal(b, m, deterministic)
}
func (dst *GetMuseContentLikeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseContentLikeListResp.Merge(dst, src)
}
func (m *GetMuseContentLikeListResp) XXX_Size() int {
	return xxx_messageInfo_GetMuseContentLikeListResp.Size(m)
}
func (m *GetMuseContentLikeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseContentLikeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseContentLikeListResp proto.InternalMessageInfo

func (m *GetMuseContentLikeListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMuseContentLikeListResp) GetLikes() []*MuseLike {
	if m != nil {
		return m.Likes
	}
	return nil
}

func (m *GetMuseContentLikeListResp) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type MuseLike struct {
	UserInfo             *MuseUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	CreatedAt            uint32        `protobuf:"varint,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseLike) Reset()         { *m = MuseLike{} }
func (m *MuseLike) String() string { return proto.CompactTextString(m) }
func (*MuseLike) ProtoMessage()    {}
func (*MuseLike) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{14}
}
func (m *MuseLike) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseLike.Unmarshal(m, b)
}
func (m *MuseLike) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseLike.Marshal(b, m, deterministic)
}
func (dst *MuseLike) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseLike.Merge(dst, src)
}
func (m *MuseLike) XXX_Size() int {
	return xxx_messageInfo_MuseLike.Size(m)
}
func (m *MuseLike) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseLike.DiscardUnknown(m)
}

var xxx_messageInfo_MuseLike proto.InternalMessageInfo

func (m *MuseLike) GetUserInfo() *MuseUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *MuseLike) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

type MuseUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	CheckInTitle         string   `protobuf:"bytes,4,opt,name=check_in_title,json=checkInTitle,proto3" json:"check_in_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseUserInfo) Reset()         { *m = MuseUserInfo{} }
func (m *MuseUserInfo) String() string { return proto.CompactTextString(m) }
func (*MuseUserInfo) ProtoMessage()    {}
func (*MuseUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{15}
}
func (m *MuseUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseUserInfo.Unmarshal(m, b)
}
func (m *MuseUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseUserInfo.Marshal(b, m, deterministic)
}
func (dst *MuseUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseUserInfo.Merge(dst, src)
}
func (m *MuseUserInfo) XXX_Size() int {
	return xxx_messageInfo_MuseUserInfo.Size(m)
}
func (m *MuseUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseUserInfo proto.InternalMessageInfo

func (m *MuseUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MuseUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MuseUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MuseUserInfo) GetCheckInTitle() string {
	if m != nil {
		return m.CheckInTitle
	}
	return ""
}

type MuseLastFeedInfo struct {
	Time                 uint64   `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Score                float64  `protobuf:"fixed64,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseLastFeedInfo) Reset()         { *m = MuseLastFeedInfo{} }
func (m *MuseLastFeedInfo) String() string { return proto.CompactTextString(m) }
func (*MuseLastFeedInfo) ProtoMessage()    {}
func (*MuseLastFeedInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{16}
}
func (m *MuseLastFeedInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseLastFeedInfo.Unmarshal(m, b)
}
func (m *MuseLastFeedInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseLastFeedInfo.Marshal(b, m, deterministic)
}
func (dst *MuseLastFeedInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseLastFeedInfo.Merge(dst, src)
}
func (m *MuseLastFeedInfo) XXX_Size() int {
	return xxx_messageInfo_MuseLastFeedInfo.Size(m)
}
func (m *MuseLastFeedInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseLastFeedInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MuseLastFeedInfo proto.InternalMessageInfo

func (m *MuseLastFeedInfo) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *MuseLastFeedInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MuseLastFeedInfo) GetScore() float64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type MuseFeedsLoadMore struct {
	LastPage             uint32            `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32            `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	LastFeedInfo         *MuseLastFeedInfo `protobuf:"bytes,3,opt,name=last_feed_info,json=lastFeedInfo,proto3" json:"last_feed_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MuseFeedsLoadMore) Reset()         { *m = MuseFeedsLoadMore{} }
func (m *MuseFeedsLoadMore) String() string { return proto.CompactTextString(m) }
func (*MuseFeedsLoadMore) ProtoMessage()    {}
func (*MuseFeedsLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{17}
}
func (m *MuseFeedsLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseFeedsLoadMore.Unmarshal(m, b)
}
func (m *MuseFeedsLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseFeedsLoadMore.Marshal(b, m, deterministic)
}
func (dst *MuseFeedsLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseFeedsLoadMore.Merge(dst, src)
}
func (m *MuseFeedsLoadMore) XXX_Size() int {
	return xxx_messageInfo_MuseFeedsLoadMore.Size(m)
}
func (m *MuseFeedsLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseFeedsLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_MuseFeedsLoadMore proto.InternalMessageInfo

func (m *MuseFeedsLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *MuseFeedsLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

func (m *MuseFeedsLoadMore) GetLastFeedInfo() *MuseLastFeedInfo {
	if m != nil {
		return m.LastFeedInfo
	}
	return nil
}

// 获取留言详情
type GetMusePostDetailReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusePostDetailReq) Reset()         { *m = GetMusePostDetailReq{} }
func (m *GetMusePostDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetMusePostDetailReq) ProtoMessage()    {}
func (*GetMusePostDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{18}
}
func (m *GetMusePostDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusePostDetailReq.Unmarshal(m, b)
}
func (m *GetMusePostDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusePostDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetMusePostDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusePostDetailReq.Merge(dst, src)
}
func (m *GetMusePostDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetMusePostDetailReq.Size(m)
}
func (m *GetMusePostDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusePostDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusePostDetailReq proto.InternalMessageInfo

func (m *GetMusePostDetailReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusePostDetailReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type GetMusePostDetailResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Post                 *MusePost     `protobuf:"bytes,2,opt,name=post,proto3" json:"post,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetMusePostDetailResp) Reset()         { *m = GetMusePostDetailResp{} }
func (m *GetMusePostDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetMusePostDetailResp) ProtoMessage()    {}
func (*GetMusePostDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{19}
}
func (m *GetMusePostDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusePostDetailResp.Unmarshal(m, b)
}
func (m *GetMusePostDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusePostDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetMusePostDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusePostDetailResp.Merge(dst, src)
}
func (m *GetMusePostDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetMusePostDetailResp.Size(m)
}
func (m *GetMusePostDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusePostDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusePostDetailResp proto.InternalMessageInfo

func (m *GetMusePostDetailResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusePostDetailResp) GetPost() *MusePost {
	if m != nil {
		return m.Post
	}
	return nil
}

// 点赞
type MuseAddLikeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string       `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Source               string       `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseAddLikeReq) Reset()         { *m = MuseAddLikeReq{} }
func (m *MuseAddLikeReq) String() string { return proto.CompactTextString(m) }
func (*MuseAddLikeReq) ProtoMessage()    {}
func (*MuseAddLikeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{20}
}
func (m *MuseAddLikeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAddLikeReq.Unmarshal(m, b)
}
func (m *MuseAddLikeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAddLikeReq.Marshal(b, m, deterministic)
}
func (dst *MuseAddLikeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAddLikeReq.Merge(dst, src)
}
func (m *MuseAddLikeReq) XXX_Size() int {
	return xxx_messageInfo_MuseAddLikeReq.Size(m)
}
func (m *MuseAddLikeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAddLikeReq.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAddLikeReq proto.InternalMessageInfo

func (m *MuseAddLikeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseAddLikeReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *MuseAddLikeReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *MuseAddLikeReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type MuseAddLikeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseAddLikeResp) Reset()         { *m = MuseAddLikeResp{} }
func (m *MuseAddLikeResp) String() string { return proto.CompactTextString(m) }
func (*MuseAddLikeResp) ProtoMessage()    {}
func (*MuseAddLikeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{21}
}
func (m *MuseAddLikeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAddLikeResp.Unmarshal(m, b)
}
func (m *MuseAddLikeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAddLikeResp.Marshal(b, m, deterministic)
}
func (dst *MuseAddLikeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAddLikeResp.Merge(dst, src)
}
func (m *MuseAddLikeResp) XXX_Size() int {
	return xxx_messageInfo_MuseAddLikeResp.Size(m)
}
func (m *MuseAddLikeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAddLikeResp.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAddLikeResp proto.InternalMessageInfo

func (m *MuseAddLikeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 取消点赞
type MuseResetLikeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string       `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseResetLikeReq) Reset()         { *m = MuseResetLikeReq{} }
func (m *MuseResetLikeReq) String() string { return proto.CompactTextString(m) }
func (*MuseResetLikeReq) ProtoMessage()    {}
func (*MuseResetLikeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{22}
}
func (m *MuseResetLikeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseResetLikeReq.Unmarshal(m, b)
}
func (m *MuseResetLikeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseResetLikeReq.Marshal(b, m, deterministic)
}
func (dst *MuseResetLikeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseResetLikeReq.Merge(dst, src)
}
func (m *MuseResetLikeReq) XXX_Size() int {
	return xxx_messageInfo_MuseResetLikeReq.Size(m)
}
func (m *MuseResetLikeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseResetLikeReq.DiscardUnknown(m)
}

var xxx_messageInfo_MuseResetLikeReq proto.InternalMessageInfo

func (m *MuseResetLikeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseResetLikeReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *MuseResetLikeReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type MuseResetLikeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseResetLikeResp) Reset()         { *m = MuseResetLikeResp{} }
func (m *MuseResetLikeResp) String() string { return proto.CompactTextString(m) }
func (*MuseResetLikeResp) ProtoMessage()    {}
func (*MuseResetLikeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{23}
}
func (m *MuseResetLikeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseResetLikeResp.Unmarshal(m, b)
}
func (m *MuseResetLikeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseResetLikeResp.Marshal(b, m, deterministic)
}
func (dst *MuseResetLikeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseResetLikeResp.Merge(dst, src)
}
func (m *MuseResetLikeResp) XXX_Size() int {
	return xxx_messageInfo_MuseResetLikeResp.Size(m)
}
func (m *MuseResetLikeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseResetLikeResp.DiscardUnknown(m)
}

var xxx_messageInfo_MuseResetLikeResp proto.InternalMessageInfo

func (m *MuseResetLikeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 发布留言
type MusePublishPostReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Post                 *MusePost    `protobuf:"bytes,3,opt,name=post,proto3" json:"post,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MusePublishPostReq) Reset()         { *m = MusePublishPostReq{} }
func (m *MusePublishPostReq) String() string { return proto.CompactTextString(m) }
func (*MusePublishPostReq) ProtoMessage()    {}
func (*MusePublishPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{24}
}
func (m *MusePublishPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusePublishPostReq.Unmarshal(m, b)
}
func (m *MusePublishPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusePublishPostReq.Marshal(b, m, deterministic)
}
func (dst *MusePublishPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusePublishPostReq.Merge(dst, src)
}
func (m *MusePublishPostReq) XXX_Size() int {
	return xxx_messageInfo_MusePublishPostReq.Size(m)
}
func (m *MusePublishPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MusePublishPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_MusePublishPostReq proto.InternalMessageInfo

func (m *MusePublishPostReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MusePublishPostReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MusePublishPostReq) GetPost() *MusePost {
	if m != nil {
		return m.Post
	}
	return nil
}

type MusePublishPostResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Post                 *MusePost     `protobuf:"bytes,2,opt,name=post,proto3" json:"post,omitempty"`
	Tip                  string        `protobuf:"bytes,3,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MusePublishPostResp) Reset()         { *m = MusePublishPostResp{} }
func (m *MusePublishPostResp) String() string { return proto.CompactTextString(m) }
func (*MusePublishPostResp) ProtoMessage()    {}
func (*MusePublishPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{25}
}
func (m *MusePublishPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusePublishPostResp.Unmarshal(m, b)
}
func (m *MusePublishPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusePublishPostResp.Marshal(b, m, deterministic)
}
func (dst *MusePublishPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusePublishPostResp.Merge(dst, src)
}
func (m *MusePublishPostResp) XXX_Size() int {
	return xxx_messageInfo_MusePublishPostResp.Size(m)
}
func (m *MusePublishPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MusePublishPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_MusePublishPostResp proto.InternalMessageInfo

func (m *MusePublishPostResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MusePublishPostResp) GetPost() *MusePost {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *MusePublishPostResp) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

// 发布评论
type MusePublishCommentReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Comment              *MuseComment `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
	ParentCommentId      string       `protobuf:"bytes,4,opt,name=parent_comment_id,json=parentCommentId,proto3" json:"parent_comment_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MusePublishCommentReq) Reset()         { *m = MusePublishCommentReq{} }
func (m *MusePublishCommentReq) String() string { return proto.CompactTextString(m) }
func (*MusePublishCommentReq) ProtoMessage()    {}
func (*MusePublishCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{26}
}
func (m *MusePublishCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusePublishCommentReq.Unmarshal(m, b)
}
func (m *MusePublishCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusePublishCommentReq.Marshal(b, m, deterministic)
}
func (dst *MusePublishCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusePublishCommentReq.Merge(dst, src)
}
func (m *MusePublishCommentReq) XXX_Size() int {
	return xxx_messageInfo_MusePublishCommentReq.Size(m)
}
func (m *MusePublishCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MusePublishCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_MusePublishCommentReq proto.InternalMessageInfo

func (m *MusePublishCommentReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MusePublishCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *MusePublishCommentReq) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *MusePublishCommentReq) GetParentCommentId() string {
	if m != nil {
		return m.ParentCommentId
	}
	return ""
}

func (m *MusePublishCommentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type MusePublishCommentResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Comment              *MuseComment  `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	Tip                  string        `protobuf:"bytes,3,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MusePublishCommentResp) Reset()         { *m = MusePublishCommentResp{} }
func (m *MusePublishCommentResp) String() string { return proto.CompactTextString(m) }
func (*MusePublishCommentResp) ProtoMessage()    {}
func (*MusePublishCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{27}
}
func (m *MusePublishCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusePublishCommentResp.Unmarshal(m, b)
}
func (m *MusePublishCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusePublishCommentResp.Marshal(b, m, deterministic)
}
func (dst *MusePublishCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusePublishCommentResp.Merge(dst, src)
}
func (m *MusePublishCommentResp) XXX_Size() int {
	return xxx_messageInfo_MusePublishCommentResp.Size(m)
}
func (m *MusePublishCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MusePublishCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_MusePublishCommentResp proto.InternalMessageInfo

func (m *MusePublishCommentResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MusePublishCommentResp) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *MusePublishCommentResp) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

// 是否有新留言或互动消息（小红点）
type MuseHaveNewPostReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseHaveNewPostReq) Reset()         { *m = MuseHaveNewPostReq{} }
func (m *MuseHaveNewPostReq) String() string { return proto.CompactTextString(m) }
func (*MuseHaveNewPostReq) ProtoMessage()    {}
func (*MuseHaveNewPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{28}
}
func (m *MuseHaveNewPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseHaveNewPostReq.Unmarshal(m, b)
}
func (m *MuseHaveNewPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseHaveNewPostReq.Marshal(b, m, deterministic)
}
func (dst *MuseHaveNewPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseHaveNewPostReq.Merge(dst, src)
}
func (m *MuseHaveNewPostReq) XXX_Size() int {
	return xxx_messageInfo_MuseHaveNewPostReq.Size(m)
}
func (m *MuseHaveNewPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseHaveNewPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_MuseHaveNewPostReq proto.InternalMessageInfo

func (m *MuseHaveNewPostReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MuseHaveNewPostReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type MuseHaveNewPostResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HaveNew              bool          `protobuf:"varint,2,opt,name=have_new,json=haveNew,proto3" json:"have_new,omitempty"`
	UnreadLikeCount      uint32        `protobuf:"varint,3,opt,name=unread_like_count,json=unreadLikeCount,proto3" json:"unread_like_count,omitempty"`
	UnreadCommentCount   uint32        `protobuf:"varint,4,opt,name=unread_comment_count,json=unreadCommentCount,proto3" json:"unread_comment_count,omitempty"`
	UnreadAtCount        uint32        `protobuf:"varint,5,opt,name=unread_at_count,json=unreadAtCount,proto3" json:"unread_at_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MuseHaveNewPostResp) Reset()         { *m = MuseHaveNewPostResp{} }
func (m *MuseHaveNewPostResp) String() string { return proto.CompactTextString(m) }
func (*MuseHaveNewPostResp) ProtoMessage()    {}
func (*MuseHaveNewPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{29}
}
func (m *MuseHaveNewPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseHaveNewPostResp.Unmarshal(m, b)
}
func (m *MuseHaveNewPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseHaveNewPostResp.Marshal(b, m, deterministic)
}
func (dst *MuseHaveNewPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseHaveNewPostResp.Merge(dst, src)
}
func (m *MuseHaveNewPostResp) XXX_Size() int {
	return xxx_messageInfo_MuseHaveNewPostResp.Size(m)
}
func (m *MuseHaveNewPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseHaveNewPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_MuseHaveNewPostResp proto.InternalMessageInfo

func (m *MuseHaveNewPostResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MuseHaveNewPostResp) GetHaveNew() bool {
	if m != nil {
		return m.HaveNew
	}
	return false
}

func (m *MuseHaveNewPostResp) GetUnreadLikeCount() uint32 {
	if m != nil {
		return m.UnreadLikeCount
	}
	return 0
}

func (m *MuseHaveNewPostResp) GetUnreadCommentCount() uint32 {
	if m != nil {
		return m.UnreadCommentCount
	}
	return 0
}

func (m *MuseHaveNewPostResp) GetUnreadAtCount() uint32 {
	if m != nil {
		return m.UnreadAtCount
	}
	return 0
}

type NewInteractiveMsgNotify struct {
	ChannelId             uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Tip                   string   `protobuf:"bytes,2,opt,name=tip,proto3" json:"tip,omitempty"`
	UnreadLikeMsgCount    uint32   `protobuf:"varint,3,opt,name=unread_like_msg_count,json=unreadLikeMsgCount,proto3" json:"unread_like_msg_count,omitempty"`
	UnreadCommentMsgCount uint32   `protobuf:"varint,4,opt,name=unread_comment_msg_count,json=unreadCommentMsgCount,proto3" json:"unread_comment_msg_count,omitempty"`
	UnreadAtMsgCount      uint32   `protobuf:"varint,5,opt,name=unread_at_msg_count,json=unreadAtMsgCount,proto3" json:"unread_at_msg_count,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *NewInteractiveMsgNotify) Reset()         { *m = NewInteractiveMsgNotify{} }
func (m *NewInteractiveMsgNotify) String() string { return proto.CompactTextString(m) }
func (*NewInteractiveMsgNotify) ProtoMessage()    {}
func (*NewInteractiveMsgNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{30}
}
func (m *NewInteractiveMsgNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewInteractiveMsgNotify.Unmarshal(m, b)
}
func (m *NewInteractiveMsgNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewInteractiveMsgNotify.Marshal(b, m, deterministic)
}
func (dst *NewInteractiveMsgNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewInteractiveMsgNotify.Merge(dst, src)
}
func (m *NewInteractiveMsgNotify) XXX_Size() int {
	return xxx_messageInfo_NewInteractiveMsgNotify.Size(m)
}
func (m *NewInteractiveMsgNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_NewInteractiveMsgNotify.DiscardUnknown(m)
}

var xxx_messageInfo_NewInteractiveMsgNotify proto.InternalMessageInfo

func (m *NewInteractiveMsgNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NewInteractiveMsgNotify) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

func (m *NewInteractiveMsgNotify) GetUnreadLikeMsgCount() uint32 {
	if m != nil {
		return m.UnreadLikeMsgCount
	}
	return 0
}

func (m *NewInteractiveMsgNotify) GetUnreadCommentMsgCount() uint32 {
	if m != nil {
		return m.UnreadCommentMsgCount
	}
	return 0
}

func (m *NewInteractiveMsgNotify) GetUnreadAtMsgCount() uint32 {
	if m != nil {
		return m.UnreadAtMsgCount
	}
	return 0
}

type NewPostNotify struct {
	ChannelId            uint32        `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UserInfo             *MuseUserInfo `protobuf:"bytes,2,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *NewPostNotify) Reset()         { *m = NewPostNotify{} }
func (m *NewPostNotify) String() string { return proto.CompactTextString(m) }
func (*NewPostNotify) ProtoMessage()    {}
func (*NewPostNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{31}
}
func (m *NewPostNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewPostNotify.Unmarshal(m, b)
}
func (m *NewPostNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewPostNotify.Marshal(b, m, deterministic)
}
func (dst *NewPostNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewPostNotify.Merge(dst, src)
}
func (m *NewPostNotify) XXX_Size() int {
	return xxx_messageInfo_NewPostNotify.Size(m)
}
func (m *NewPostNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_NewPostNotify.DiscardUnknown(m)
}

var xxx_messageInfo_NewPostNotify proto.InternalMessageInfo

func (m *NewPostNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NewPostNotify) GetUserInfo() *MuseUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

// 标记已读
type MarkMuseInteractiveMsgReadReq struct {
	BaseReq              *app.BaseReq           `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MsgId                string                 `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	MsgType              MuseInteractiveMsgType `protobuf:"varint,4,opt,name=msg_type,json=msgType,proto3,enum=ga.muse_post_logic.MuseInteractiveMsgType" json:"msg_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *MarkMuseInteractiveMsgReadReq) Reset()         { *m = MarkMuseInteractiveMsgReadReq{} }
func (m *MarkMuseInteractiveMsgReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkMuseInteractiveMsgReadReq) ProtoMessage()    {}
func (*MarkMuseInteractiveMsgReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{32}
}
func (m *MarkMuseInteractiveMsgReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkMuseInteractiveMsgReadReq.Unmarshal(m, b)
}
func (m *MarkMuseInteractiveMsgReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkMuseInteractiveMsgReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkMuseInteractiveMsgReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkMuseInteractiveMsgReadReq.Merge(dst, src)
}
func (m *MarkMuseInteractiveMsgReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkMuseInteractiveMsgReadReq.Size(m)
}
func (m *MarkMuseInteractiveMsgReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkMuseInteractiveMsgReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkMuseInteractiveMsgReadReq proto.InternalMessageInfo

func (m *MarkMuseInteractiveMsgReadReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MarkMuseInteractiveMsgReadReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MarkMuseInteractiveMsgReadReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *MarkMuseInteractiveMsgReadReq) GetMsgType() MuseInteractiveMsgType {
	if m != nil {
		return m.MsgType
	}
	return MuseInteractiveMsgType_MuseInteractiveMsgType_UNDEFINED
}

type MarkMuseInteractiveMsgReadResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MarkMuseInteractiveMsgReadResp) Reset()         { *m = MarkMuseInteractiveMsgReadResp{} }
func (m *MarkMuseInteractiveMsgReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkMuseInteractiveMsgReadResp) ProtoMessage()    {}
func (*MarkMuseInteractiveMsgReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{33}
}
func (m *MarkMuseInteractiveMsgReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkMuseInteractiveMsgReadResp.Unmarshal(m, b)
}
func (m *MarkMuseInteractiveMsgReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkMuseInteractiveMsgReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkMuseInteractiveMsgReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkMuseInteractiveMsgReadResp.Merge(dst, src)
}
func (m *MarkMuseInteractiveMsgReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkMuseInteractiveMsgReadResp.Size(m)
}
func (m *MarkMuseInteractiveMsgReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkMuseInteractiveMsgReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkMuseInteractiveMsgReadResp proto.InternalMessageInfo

func (m *MarkMuseInteractiveMsgReadResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取互动消息
type GetMuseInteractiveMsgReq struct {
	BaseReq              *app.BaseReq           `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MsgType              MuseInteractiveMsgType `protobuf:"varint,3,opt,name=msg_type,json=msgType,proto3,enum=ga.muse_post_logic.MuseInteractiveMsgType" json:"msg_type,omitempty"`
	LoadMore             *MuseFeedsLoadMore     `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32                 `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetMuseInteractiveMsgReq) Reset()         { *m = GetMuseInteractiveMsgReq{} }
func (m *GetMuseInteractiveMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetMuseInteractiveMsgReq) ProtoMessage()    {}
func (*GetMuseInteractiveMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{34}
}
func (m *GetMuseInteractiveMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseInteractiveMsgReq.Unmarshal(m, b)
}
func (m *GetMuseInteractiveMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseInteractiveMsgReq.Marshal(b, m, deterministic)
}
func (dst *GetMuseInteractiveMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseInteractiveMsgReq.Merge(dst, src)
}
func (m *GetMuseInteractiveMsgReq) XXX_Size() int {
	return xxx_messageInfo_GetMuseInteractiveMsgReq.Size(m)
}
func (m *GetMuseInteractiveMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseInteractiveMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseInteractiveMsgReq proto.InternalMessageInfo

func (m *GetMuseInteractiveMsgReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMuseInteractiveMsgReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMuseInteractiveMsgReq) GetMsgType() MuseInteractiveMsgType {
	if m != nil {
		return m.MsgType
	}
	return MuseInteractiveMsgType_MuseInteractiveMsgType_UNDEFINED
}

func (m *GetMuseInteractiveMsgReq) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetMuseInteractiveMsgReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetMuseInteractiveMsgResp struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Msgs                 []*MuseInteractiveMsg    `protobuf:"bytes,2,rep,name=msgs,proto3" json:"msgs,omitempty"`
	Count                *MuseInteractiveMsgCount `protobuf:"bytes,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *MuseFeedsLoadMore       `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetMuseInteractiveMsgResp) Reset()         { *m = GetMuseInteractiveMsgResp{} }
func (m *GetMuseInteractiveMsgResp) String() string { return proto.CompactTextString(m) }
func (*GetMuseInteractiveMsgResp) ProtoMessage()    {}
func (*GetMuseInteractiveMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{35}
}
func (m *GetMuseInteractiveMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseInteractiveMsgResp.Unmarshal(m, b)
}
func (m *GetMuseInteractiveMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseInteractiveMsgResp.Marshal(b, m, deterministic)
}
func (dst *GetMuseInteractiveMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseInteractiveMsgResp.Merge(dst, src)
}
func (m *GetMuseInteractiveMsgResp) XXX_Size() int {
	return xxx_messageInfo_GetMuseInteractiveMsgResp.Size(m)
}
func (m *GetMuseInteractiveMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseInteractiveMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseInteractiveMsgResp proto.InternalMessageInfo

func (m *GetMuseInteractiveMsgResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMuseInteractiveMsgResp) GetMsgs() []*MuseInteractiveMsg {
	if m != nil {
		return m.Msgs
	}
	return nil
}

func (m *GetMuseInteractiveMsgResp) GetCount() *MuseInteractiveMsgCount {
	if m != nil {
		return m.Count
	}
	return nil
}

func (m *GetMuseInteractiveMsgResp) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type MuseInteractiveMsgCount struct {
	UnreadLikeMsgCount    uint32   `protobuf:"varint,1,opt,name=unread_like_msg_count,json=unreadLikeMsgCount,proto3" json:"unread_like_msg_count,omitempty"`
	UnreadCommentMsgCount uint32   `protobuf:"varint,2,opt,name=unread_comment_msg_count,json=unreadCommentMsgCount,proto3" json:"unread_comment_msg_count,omitempty"`
	UnreadAtMsgCount      uint32   `protobuf:"varint,3,opt,name=unread_at_msg_count,json=unreadAtMsgCount,proto3" json:"unread_at_msg_count,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *MuseInteractiveMsgCount) Reset()         { *m = MuseInteractiveMsgCount{} }
func (m *MuseInteractiveMsgCount) String() string { return proto.CompactTextString(m) }
func (*MuseInteractiveMsgCount) ProtoMessage()    {}
func (*MuseInteractiveMsgCount) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{36}
}
func (m *MuseInteractiveMsgCount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseInteractiveMsgCount.Unmarshal(m, b)
}
func (m *MuseInteractiveMsgCount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseInteractiveMsgCount.Marshal(b, m, deterministic)
}
func (dst *MuseInteractiveMsgCount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseInteractiveMsgCount.Merge(dst, src)
}
func (m *MuseInteractiveMsgCount) XXX_Size() int {
	return xxx_messageInfo_MuseInteractiveMsgCount.Size(m)
}
func (m *MuseInteractiveMsgCount) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseInteractiveMsgCount.DiscardUnknown(m)
}

var xxx_messageInfo_MuseInteractiveMsgCount proto.InternalMessageInfo

func (m *MuseInteractiveMsgCount) GetUnreadLikeMsgCount() uint32 {
	if m != nil {
		return m.UnreadLikeMsgCount
	}
	return 0
}

func (m *MuseInteractiveMsgCount) GetUnreadCommentMsgCount() uint32 {
	if m != nil {
		return m.UnreadCommentMsgCount
	}
	return 0
}

func (m *MuseInteractiveMsgCount) GetUnreadAtMsgCount() uint32 {
	if m != nil {
		return m.UnreadAtMsgCount
	}
	return 0
}

type MuseInteractiveMsg struct {
	Id       string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FromUser *MuseUserInfo `protobuf:"bytes,2,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	// Types that are valid to be assigned to Msg:
	//	*MuseInteractiveMsg_LikeMsg
	//	*MuseInteractiveMsg_CommentMsg
	//	*MuseInteractiveMsg_AtMsg
	Msg                  isMuseInteractiveMsg_Msg `protobuf_oneof:"msg"`
	Desc                 string                   `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *MuseInteractiveMsg) Reset()         { *m = MuseInteractiveMsg{} }
func (m *MuseInteractiveMsg) String() string { return proto.CompactTextString(m) }
func (*MuseInteractiveMsg) ProtoMessage()    {}
func (*MuseInteractiveMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{37}
}
func (m *MuseInteractiveMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseInteractiveMsg.Unmarshal(m, b)
}
func (m *MuseInteractiveMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseInteractiveMsg.Marshal(b, m, deterministic)
}
func (dst *MuseInteractiveMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseInteractiveMsg.Merge(dst, src)
}
func (m *MuseInteractiveMsg) XXX_Size() int {
	return xxx_messageInfo_MuseInteractiveMsg.Size(m)
}
func (m *MuseInteractiveMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseInteractiveMsg.DiscardUnknown(m)
}

var xxx_messageInfo_MuseInteractiveMsg proto.InternalMessageInfo

func (m *MuseInteractiveMsg) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MuseInteractiveMsg) GetFromUser() *MuseUserInfo {
	if m != nil {
		return m.FromUser
	}
	return nil
}

type isMuseInteractiveMsg_Msg interface {
	isMuseInteractiveMsg_Msg()
}

type MuseInteractiveMsg_LikeMsg struct {
	LikeMsg *MuseLikeMsg `protobuf:"bytes,3,opt,name=like_msg,json=likeMsg,proto3,oneof"`
}

type MuseInteractiveMsg_CommentMsg struct {
	CommentMsg *MuseCommentMsg `protobuf:"bytes,4,opt,name=comment_msg,json=commentMsg,proto3,oneof"`
}

type MuseInteractiveMsg_AtMsg struct {
	AtMsg *MuseAtMsg `protobuf:"bytes,6,opt,name=at_msg,json=atMsg,proto3,oneof"`
}

func (*MuseInteractiveMsg_LikeMsg) isMuseInteractiveMsg_Msg() {}

func (*MuseInteractiveMsg_CommentMsg) isMuseInteractiveMsg_Msg() {}

func (*MuseInteractiveMsg_AtMsg) isMuseInteractiveMsg_Msg() {}

func (m *MuseInteractiveMsg) GetMsg() isMuseInteractiveMsg_Msg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *MuseInteractiveMsg) GetLikeMsg() *MuseLikeMsg {
	if x, ok := m.GetMsg().(*MuseInteractiveMsg_LikeMsg); ok {
		return x.LikeMsg
	}
	return nil
}

func (m *MuseInteractiveMsg) GetCommentMsg() *MuseCommentMsg {
	if x, ok := m.GetMsg().(*MuseInteractiveMsg_CommentMsg); ok {
		return x.CommentMsg
	}
	return nil
}

func (m *MuseInteractiveMsg) GetAtMsg() *MuseAtMsg {
	if x, ok := m.GetMsg().(*MuseInteractiveMsg_AtMsg); ok {
		return x.AtMsg
	}
	return nil
}

func (m *MuseInteractiveMsg) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*MuseInteractiveMsg) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _MuseInteractiveMsg_OneofMarshaler, _MuseInteractiveMsg_OneofUnmarshaler, _MuseInteractiveMsg_OneofSizer, []interface{}{
		(*MuseInteractiveMsg_LikeMsg)(nil),
		(*MuseInteractiveMsg_CommentMsg)(nil),
		(*MuseInteractiveMsg_AtMsg)(nil),
	}
}

func _MuseInteractiveMsg_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*MuseInteractiveMsg)
	// msg
	switch x := m.Msg.(type) {
	case *MuseInteractiveMsg_LikeMsg:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.LikeMsg); err != nil {
			return err
		}
	case *MuseInteractiveMsg_CommentMsg:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.CommentMsg); err != nil {
			return err
		}
	case *MuseInteractiveMsg_AtMsg:
		b.EncodeVarint(6<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.AtMsg); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("MuseInteractiveMsg.Msg has unexpected type %T", x)
	}
	return nil
}

func _MuseInteractiveMsg_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*MuseInteractiveMsg)
	switch tag {
	case 3: // msg.like_msg
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseLikeMsg)
		err := b.DecodeMessage(msg)
		m.Msg = &MuseInteractiveMsg_LikeMsg{msg}
		return true, err
	case 4: // msg.comment_msg
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseCommentMsg)
		err := b.DecodeMessage(msg)
		m.Msg = &MuseInteractiveMsg_CommentMsg{msg}
		return true, err
	case 6: // msg.at_msg
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MuseAtMsg)
		err := b.DecodeMessage(msg)
		m.Msg = &MuseInteractiveMsg_AtMsg{msg}
		return true, err
	default:
		return false, nil
	}
}

func _MuseInteractiveMsg_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*MuseInteractiveMsg)
	// msg
	switch x := m.Msg.(type) {
	case *MuseInteractiveMsg_LikeMsg:
		s := proto.Size(x.LikeMsg)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseInteractiveMsg_CommentMsg:
		s := proto.Size(x.CommentMsg)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MuseInteractiveMsg_AtMsg:
		s := proto.Size(x.AtMsg)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type MuseLikeMsg struct {
	Post                 *MusePost    `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	Comment              *MuseComment `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	CreatedAt            uint32       `protobuf:"varint,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseLikeMsg) Reset()         { *m = MuseLikeMsg{} }
func (m *MuseLikeMsg) String() string { return proto.CompactTextString(m) }
func (*MuseLikeMsg) ProtoMessage()    {}
func (*MuseLikeMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{38}
}
func (m *MuseLikeMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseLikeMsg.Unmarshal(m, b)
}
func (m *MuseLikeMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseLikeMsg.Marshal(b, m, deterministic)
}
func (dst *MuseLikeMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseLikeMsg.Merge(dst, src)
}
func (m *MuseLikeMsg) XXX_Size() int {
	return xxx_messageInfo_MuseLikeMsg.Size(m)
}
func (m *MuseLikeMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseLikeMsg.DiscardUnknown(m)
}

var xxx_messageInfo_MuseLikeMsg proto.InternalMessageInfo

func (m *MuseLikeMsg) GetPost() *MusePost {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *MuseLikeMsg) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *MuseLikeMsg) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

type MuseCommentMsg struct {
	Post                 *MusePost    `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	Comment              *MuseComment `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	CreatedAt            uint32       `protobuf:"varint,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseCommentMsg) Reset()         { *m = MuseCommentMsg{} }
func (m *MuseCommentMsg) String() string { return proto.CompactTextString(m) }
func (*MuseCommentMsg) ProtoMessage()    {}
func (*MuseCommentMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{39}
}
func (m *MuseCommentMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseCommentMsg.Unmarshal(m, b)
}
func (m *MuseCommentMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseCommentMsg.Marshal(b, m, deterministic)
}
func (dst *MuseCommentMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseCommentMsg.Merge(dst, src)
}
func (m *MuseCommentMsg) XXX_Size() int {
	return xxx_messageInfo_MuseCommentMsg.Size(m)
}
func (m *MuseCommentMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseCommentMsg.DiscardUnknown(m)
}

var xxx_messageInfo_MuseCommentMsg proto.InternalMessageInfo

func (m *MuseCommentMsg) GetPost() *MusePost {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *MuseCommentMsg) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *MuseCommentMsg) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

type MuseAtMsg struct {
	Post                 *MusePost    `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	Comment              *MuseComment `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	CreatedAt            uint32       `protobuf:"varint,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MuseAtMsg) Reset()         { *m = MuseAtMsg{} }
func (m *MuseAtMsg) String() string { return proto.CompactTextString(m) }
func (*MuseAtMsg) ProtoMessage()    {}
func (*MuseAtMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{40}
}
func (m *MuseAtMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAtMsg.Unmarshal(m, b)
}
func (m *MuseAtMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAtMsg.Marshal(b, m, deterministic)
}
func (dst *MuseAtMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAtMsg.Merge(dst, src)
}
func (m *MuseAtMsg) XXX_Size() int {
	return xxx_messageInfo_MuseAtMsg.Size(m)
}
func (m *MuseAtMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAtMsg.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAtMsg proto.InternalMessageInfo

func (m *MuseAtMsg) GetPost() *MusePost {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *MuseAtMsg) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *MuseAtMsg) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

// 删除留言或评论
type DeleteMuseContentReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId            string       `protobuf:"bytes,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DeleteMuseContentReq) Reset()         { *m = DeleteMuseContentReq{} }
func (m *DeleteMuseContentReq) String() string { return proto.CompactTextString(m) }
func (*DeleteMuseContentReq) ProtoMessage()    {}
func (*DeleteMuseContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{41}
}
func (m *DeleteMuseContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteMuseContentReq.Unmarshal(m, b)
}
func (m *DeleteMuseContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteMuseContentReq.Marshal(b, m, deterministic)
}
func (dst *DeleteMuseContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteMuseContentReq.Merge(dst, src)
}
func (m *DeleteMuseContentReq) XXX_Size() int {
	return xxx_messageInfo_DeleteMuseContentReq.Size(m)
}
func (m *DeleteMuseContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteMuseContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteMuseContentReq proto.InternalMessageInfo

func (m *DeleteMuseContentReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DeleteMuseContentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *DeleteMuseContentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type DeleteMuseContentResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Comment              *MuseComment  `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DeleteMuseContentResp) Reset()         { *m = DeleteMuseContentResp{} }
func (m *DeleteMuseContentResp) String() string { return proto.CompactTextString(m) }
func (*DeleteMuseContentResp) ProtoMessage()    {}
func (*DeleteMuseContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{42}
}
func (m *DeleteMuseContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteMuseContentResp.Unmarshal(m, b)
}
func (m *DeleteMuseContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteMuseContentResp.Marshal(b, m, deterministic)
}
func (dst *DeleteMuseContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteMuseContentResp.Merge(dst, src)
}
func (m *DeleteMuseContentResp) XXX_Size() int {
	return xxx_messageInfo_DeleteMuseContentResp.Size(m)
}
func (m *DeleteMuseContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteMuseContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteMuseContentResp proto.InternalMessageInfo

func (m *DeleteMuseContentResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *DeleteMuseContentResp) GetComment() *MuseComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

type CheckMusePublishRightReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckMusePublishRightReq) Reset()         { *m = CheckMusePublishRightReq{} }
func (m *CheckMusePublishRightReq) String() string { return proto.CompactTextString(m) }
func (*CheckMusePublishRightReq) ProtoMessage()    {}
func (*CheckMusePublishRightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{43}
}
func (m *CheckMusePublishRightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMusePublishRightReq.Unmarshal(m, b)
}
func (m *CheckMusePublishRightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMusePublishRightReq.Marshal(b, m, deterministic)
}
func (dst *CheckMusePublishRightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMusePublishRightReq.Merge(dst, src)
}
func (m *CheckMusePublishRightReq) XXX_Size() int {
	return xxx_messageInfo_CheckMusePublishRightReq.Size(m)
}
func (m *CheckMusePublishRightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMusePublishRightReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMusePublishRightReq proto.InternalMessageInfo

func (m *CheckMusePublishRightReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckMusePublishRightReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckMusePublishRightResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CanPublish           bool          `protobuf:"varint,2,opt,name=can_publish,json=canPublish,proto3" json:"can_publish,omitempty"`
	Reason               string        `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	CannotCommentReason  string        `protobuf:"bytes,4,opt,name=cannot_comment_reason,json=cannotCommentReason,proto3" json:"cannot_comment_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckMusePublishRightResp) Reset()         { *m = CheckMusePublishRightResp{} }
func (m *CheckMusePublishRightResp) String() string { return proto.CompactTextString(m) }
func (*CheckMusePublishRightResp) ProtoMessage()    {}
func (*CheckMusePublishRightResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{44}
}
func (m *CheckMusePublishRightResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckMusePublishRightResp.Unmarshal(m, b)
}
func (m *CheckMusePublishRightResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckMusePublishRightResp.Marshal(b, m, deterministic)
}
func (dst *CheckMusePublishRightResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckMusePublishRightResp.Merge(dst, src)
}
func (m *CheckMusePublishRightResp) XXX_Size() int {
	return xxx_messageInfo_CheckMusePublishRightResp.Size(m)
}
func (m *CheckMusePublishRightResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckMusePublishRightResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckMusePublishRightResp proto.InternalMessageInfo

func (m *CheckMusePublishRightResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckMusePublishRightResp) GetCanPublish() bool {
	if m != nil {
		return m.CanPublish
	}
	return false
}

func (m *CheckMusePublishRightResp) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *CheckMusePublishRightResp) GetCannotCommentReason() string {
	if m != nil {
		return m.CannotCommentReason
	}
	return ""
}

// IM消息类型
type MuseInteractiveImMsg struct {
	Msg         *MuseInteractiveMsg `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	ChannelId   uint32              `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName string              `protobuf:"bytes,3,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	// 新旧版本兼容。因为不能加新的推送类型，客户端会蹦，所以加一个新的字段给客户端识别
	MsgV2                *MuseInteractiveMsg `protobuf:"bytes,4,opt,name=msg_v2,json=msgV2,proto3" json:"msg_v2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *MuseInteractiveImMsg) Reset()         { *m = MuseInteractiveImMsg{} }
func (m *MuseInteractiveImMsg) String() string { return proto.CompactTextString(m) }
func (*MuseInteractiveImMsg) ProtoMessage()    {}
func (*MuseInteractiveImMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{45}
}
func (m *MuseInteractiveImMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseInteractiveImMsg.Unmarshal(m, b)
}
func (m *MuseInteractiveImMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseInteractiveImMsg.Marshal(b, m, deterministic)
}
func (dst *MuseInteractiveImMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseInteractiveImMsg.Merge(dst, src)
}
func (m *MuseInteractiveImMsg) XXX_Size() int {
	return xxx_messageInfo_MuseInteractiveImMsg.Size(m)
}
func (m *MuseInteractiveImMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseInteractiveImMsg.DiscardUnknown(m)
}

var xxx_messageInfo_MuseInteractiveImMsg proto.InternalMessageInfo

func (m *MuseInteractiveImMsg) GetMsg() *MuseInteractiveMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *MuseInteractiveImMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuseInteractiveImMsg) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *MuseInteractiveImMsg) GetMsgV2() *MuseInteractiveMsg {
	if m != nil {
		return m.MsgV2
	}
	return nil
}

// 获取个人发布记录
type GetMuseUserRecordRequest struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32             `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32             `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMuseUserRecordRequest) Reset()         { *m = GetMuseUserRecordRequest{} }
func (m *GetMuseUserRecordRequest) String() string { return proto.CompactTextString(m) }
func (*GetMuseUserRecordRequest) ProtoMessage()    {}
func (*GetMuseUserRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{46}
}
func (m *GetMuseUserRecordRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseUserRecordRequest.Unmarshal(m, b)
}
func (m *GetMuseUserRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseUserRecordRequest.Marshal(b, m, deterministic)
}
func (dst *GetMuseUserRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseUserRecordRequest.Merge(dst, src)
}
func (m *GetMuseUserRecordRequest) XXX_Size() int {
	return xxx_messageInfo_GetMuseUserRecordRequest.Size(m)
}
func (m *GetMuseUserRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseUserRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseUserRecordRequest proto.InternalMessageInfo

func (m *GetMuseUserRecordRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMuseUserRecordRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMuseUserRecordRequest) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetMuseUserRecordRequest) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetMuseUserRecordResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Posts                []*MusePost        `protobuf:"bytes,2,rep,name=posts,proto3" json:"posts,omitempty"`
	LoadMore             *MuseFeedsLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMuseUserRecordResponse) Reset()         { *m = GetMuseUserRecordResponse{} }
func (m *GetMuseUserRecordResponse) String() string { return proto.CompactTextString(m) }
func (*GetMuseUserRecordResponse) ProtoMessage()    {}
func (*GetMuseUserRecordResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{47}
}
func (m *GetMuseUserRecordResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseUserRecordResponse.Unmarshal(m, b)
}
func (m *GetMuseUserRecordResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseUserRecordResponse.Marshal(b, m, deterministic)
}
func (dst *GetMuseUserRecordResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseUserRecordResponse.Merge(dst, src)
}
func (m *GetMuseUserRecordResponse) XXX_Size() int {
	return xxx_messageInfo_GetMuseUserRecordResponse.Size(m)
}
func (m *GetMuseUserRecordResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseUserRecordResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseUserRecordResponse proto.InternalMessageInfo

func (m *GetMuseUserRecordResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMuseUserRecordResponse) GetPosts() []*MusePost {
	if m != nil {
		return m.Posts
	}
	return nil
}

func (m *GetMuseUserRecordResponse) GetLoadMore() *MuseFeedsLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

// 获取能够艾特的用户
type GetMusePostAtFriendsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusePostAtFriendsRequest) Reset()         { *m = GetMusePostAtFriendsRequest{} }
func (m *GetMusePostAtFriendsRequest) String() string { return proto.CompactTextString(m) }
func (*GetMusePostAtFriendsRequest) ProtoMessage()    {}
func (*GetMusePostAtFriendsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{48}
}
func (m *GetMusePostAtFriendsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusePostAtFriendsRequest.Unmarshal(m, b)
}
func (m *GetMusePostAtFriendsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusePostAtFriendsRequest.Marshal(b, m, deterministic)
}
func (dst *GetMusePostAtFriendsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusePostAtFriendsRequest.Merge(dst, src)
}
func (m *GetMusePostAtFriendsRequest) XXX_Size() int {
	return xxx_messageInfo_GetMusePostAtFriendsRequest.Size(m)
}
func (m *GetMusePostAtFriendsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusePostAtFriendsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusePostAtFriendsRequest proto.InternalMessageInfo

func (m *GetMusePostAtFriendsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusePostAtFriendsRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMusePostAtFriendsResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserList             []*MuseUserInfo `protobuf:"bytes,2,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetMusePostAtFriendsResponse) Reset()         { *m = GetMusePostAtFriendsResponse{} }
func (m *GetMusePostAtFriendsResponse) String() string { return proto.CompactTextString(m) }
func (*GetMusePostAtFriendsResponse) ProtoMessage()    {}
func (*GetMusePostAtFriendsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{49}
}
func (m *GetMusePostAtFriendsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusePostAtFriendsResponse.Unmarshal(m, b)
}
func (m *GetMusePostAtFriendsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusePostAtFriendsResponse.Marshal(b, m, deterministic)
}
func (dst *GetMusePostAtFriendsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusePostAtFriendsResponse.Merge(dst, src)
}
func (m *GetMusePostAtFriendsResponse) XXX_Size() int {
	return xxx_messageInfo_GetMusePostAtFriendsResponse.Size(m)
}
func (m *GetMusePostAtFriendsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusePostAtFriendsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusePostAtFriendsResponse proto.InternalMessageInfo

func (m *GetMusePostAtFriendsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusePostAtFriendsResponse) GetUserList() []*MuseUserInfo {
	if m != nil {
		return m.UserList
	}
	return nil
}

type MuseAtData struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MuseAtData) Reset()         { *m = MuseAtData{} }
func (m *MuseAtData) String() string { return proto.CompactTextString(m) }
func (*MuseAtData) ProtoMessage()    {}
func (*MuseAtData) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_post_logic__c3815f98af3fd481, []int{50}
}
func (m *MuseAtData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MuseAtData.Unmarshal(m, b)
}
func (m *MuseAtData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MuseAtData.Marshal(b, m, deterministic)
}
func (dst *MuseAtData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MuseAtData.Merge(dst, src)
}
func (m *MuseAtData) XXX_Size() int {
	return xxx_messageInfo_MuseAtData.Size(m)
}
func (m *MuseAtData) XXX_DiscardUnknown() {
	xxx_messageInfo_MuseAtData.DiscardUnknown(m)
}

var xxx_messageInfo_MuseAtData proto.InternalMessageInfo

func (m *MuseAtData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MuseAtData) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MuseAtData) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func init() {
	proto.RegisterType((*GetMusePostListReq)(nil), "ga.muse_post_logic.GetMusePostListReq")
	proto.RegisterType((*GetMusePostListResp)(nil), "ga.muse_post_logic.GetMusePostListResp")
	proto.RegisterType((*GetMyMusePostListReq)(nil), "ga.muse_post_logic.GetMyMusePostListReq")
	proto.RegisterType((*GetMyMusePostListResp)(nil), "ga.muse_post_logic.GetMyMusePostListResp")
	proto.RegisterType((*MusePost)(nil), "ga.muse_post_logic.MusePost")
	proto.RegisterType((*MusePostUserMoodInfo)(nil), "ga.muse_post_logic.MusePostUserMoodInfo")
	proto.RegisterType((*GetMuseParentCommentListReq)(nil), "ga.muse_post_logic.GetMuseParentCommentListReq")
	proto.RegisterType((*GetMuseParentCommentListResp)(nil), "ga.muse_post_logic.GetMuseParentCommentListResp")
	proto.RegisterType((*MuseParentComment)(nil), "ga.muse_post_logic.MuseParentComment")
	proto.RegisterType((*GetMuseSubCommentListReq)(nil), "ga.muse_post_logic.GetMuseSubCommentListReq")
	proto.RegisterType((*GetMuseSubCommentListResp)(nil), "ga.muse_post_logic.GetMuseSubCommentListResp")
	proto.RegisterType((*MuseComment)(nil), "ga.muse_post_logic.MuseComment")
	proto.RegisterType((*GetMuseContentLikeListReq)(nil), "ga.muse_post_logic.GetMuseContentLikeListReq")
	proto.RegisterType((*GetMuseContentLikeListResp)(nil), "ga.muse_post_logic.GetMuseContentLikeListResp")
	proto.RegisterType((*MuseLike)(nil), "ga.muse_post_logic.MuseLike")
	proto.RegisterType((*MuseUserInfo)(nil), "ga.muse_post_logic.MuseUserInfo")
	proto.RegisterType((*MuseLastFeedInfo)(nil), "ga.muse_post_logic.MuseLastFeedInfo")
	proto.RegisterType((*MuseFeedsLoadMore)(nil), "ga.muse_post_logic.MuseFeedsLoadMore")
	proto.RegisterType((*GetMusePostDetailReq)(nil), "ga.muse_post_logic.GetMusePostDetailReq")
	proto.RegisterType((*GetMusePostDetailResp)(nil), "ga.muse_post_logic.GetMusePostDetailResp")
	proto.RegisterType((*MuseAddLikeReq)(nil), "ga.muse_post_logic.MuseAddLikeReq")
	proto.RegisterType((*MuseAddLikeResp)(nil), "ga.muse_post_logic.MuseAddLikeResp")
	proto.RegisterType((*MuseResetLikeReq)(nil), "ga.muse_post_logic.MuseResetLikeReq")
	proto.RegisterType((*MuseResetLikeResp)(nil), "ga.muse_post_logic.MuseResetLikeResp")
	proto.RegisterType((*MusePublishPostReq)(nil), "ga.muse_post_logic.MusePublishPostReq")
	proto.RegisterType((*MusePublishPostResp)(nil), "ga.muse_post_logic.MusePublishPostResp")
	proto.RegisterType((*MusePublishCommentReq)(nil), "ga.muse_post_logic.MusePublishCommentReq")
	proto.RegisterType((*MusePublishCommentResp)(nil), "ga.muse_post_logic.MusePublishCommentResp")
	proto.RegisterType((*MuseHaveNewPostReq)(nil), "ga.muse_post_logic.MuseHaveNewPostReq")
	proto.RegisterType((*MuseHaveNewPostResp)(nil), "ga.muse_post_logic.MuseHaveNewPostResp")
	proto.RegisterType((*NewInteractiveMsgNotify)(nil), "ga.muse_post_logic.NewInteractiveMsgNotify")
	proto.RegisterType((*NewPostNotify)(nil), "ga.muse_post_logic.NewPostNotify")
	proto.RegisterType((*MarkMuseInteractiveMsgReadReq)(nil), "ga.muse_post_logic.MarkMuseInteractiveMsgReadReq")
	proto.RegisterType((*MarkMuseInteractiveMsgReadResp)(nil), "ga.muse_post_logic.MarkMuseInteractiveMsgReadResp")
	proto.RegisterType((*GetMuseInteractiveMsgReq)(nil), "ga.muse_post_logic.GetMuseInteractiveMsgReq")
	proto.RegisterType((*GetMuseInteractiveMsgResp)(nil), "ga.muse_post_logic.GetMuseInteractiveMsgResp")
	proto.RegisterType((*MuseInteractiveMsgCount)(nil), "ga.muse_post_logic.MuseInteractiveMsgCount")
	proto.RegisterType((*MuseInteractiveMsg)(nil), "ga.muse_post_logic.MuseInteractiveMsg")
	proto.RegisterType((*MuseLikeMsg)(nil), "ga.muse_post_logic.MuseLikeMsg")
	proto.RegisterType((*MuseCommentMsg)(nil), "ga.muse_post_logic.MuseCommentMsg")
	proto.RegisterType((*MuseAtMsg)(nil), "ga.muse_post_logic.MuseAtMsg")
	proto.RegisterType((*DeleteMuseContentReq)(nil), "ga.muse_post_logic.DeleteMuseContentReq")
	proto.RegisterType((*DeleteMuseContentResp)(nil), "ga.muse_post_logic.DeleteMuseContentResp")
	proto.RegisterType((*CheckMusePublishRightReq)(nil), "ga.muse_post_logic.CheckMusePublishRightReq")
	proto.RegisterType((*CheckMusePublishRightResp)(nil), "ga.muse_post_logic.CheckMusePublishRightResp")
	proto.RegisterType((*MuseInteractiveImMsg)(nil), "ga.muse_post_logic.MuseInteractiveImMsg")
	proto.RegisterType((*GetMuseUserRecordRequest)(nil), "ga.muse_post_logic.GetMuseUserRecordRequest")
	proto.RegisterType((*GetMuseUserRecordResponse)(nil), "ga.muse_post_logic.GetMuseUserRecordResponse")
	proto.RegisterType((*GetMusePostAtFriendsRequest)(nil), "ga.muse_post_logic.GetMusePostAtFriendsRequest")
	proto.RegisterType((*GetMusePostAtFriendsResponse)(nil), "ga.muse_post_logic.GetMusePostAtFriendsResponse")
	proto.RegisterType((*MuseAtData)(nil), "ga.muse_post_logic.MuseAtData")
	proto.RegisterEnum("ga.muse_post_logic.MuseContentType", MuseContentType_name, MuseContentType_value)
	proto.RegisterEnum("ga.muse_post_logic.ContentStatus", ContentStatus_name, ContentStatus_value)
	proto.RegisterEnum("ga.muse_post_logic.MuseInteractiveMsgType", MuseInteractiveMsgType_name, MuseInteractiveMsgType_value)
}

func init() {
	proto.RegisterFile("muse-post-logic_.proto", fileDescriptor_muse_post_logic__c3815f98af3fd481)
}

var fileDescriptor_muse_post_logic__c3815f98af3fd481 = []byte{
	// 2141 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x5a, 0xdd, 0x6f, 0x1c, 0x49,
	0x11, 0xbf, 0xd9, 0x2f, 0xef, 0xd6, 0xae, 0x9d, 0x4d, 0xc7, 0x8e, 0xd7, 0xc9, 0xf9, 0xe2, 0x9b,
	0x0b, 0x91, 0x31, 0xc4, 0x39, 0x8c, 0x02, 0x1c, 0x5c, 0x90, 0xd6, 0xf6, 0x26, 0x59, 0x6e, 0x77,
	0x13, 0xcd, 0x39, 0x70, 0x82, 0x87, 0x51, 0x7b, 0xa6, 0xbd, 0x1e, 0x79, 0x3e, 0x36, 0xd3, 0xbd,
	0x4e, 0x2c, 0xc1, 0x03, 0x81, 0x87, 0xe3, 0x01, 0x89, 0x37, 0x74, 0x82, 0xff, 0x02, 0x10, 0x0f,
	0x27, 0xc1, 0x0b, 0x12, 0x3c, 0xc2, 0x3f, 0x80, 0x04, 0x82, 0xff, 0x80, 0x27, 0x24, 0x24, 0xd4,
	0x1f, 0xb3, 0x3b, 0xb3, 0x1f, 0xe7, 0xdd, 0xdc, 0x24, 0x0a, 0xdc, 0x5b, 0x77, 0x57, 0x57, 0x75,
	0xfd, 0xaa, 0x6b, 0xaa, 0xab, 0x6a, 0x17, 0x2e, 0x7b, 0x7d, 0x4a, 0x6e, 0xf6, 0x02, 0xca, 0x6e,
	0xba, 0x41, 0xd7, 0xb1, 0xcc, 0xed, 0x5e, 0x18, 0xb0, 0x00, 0xa1, 0x2e, 0xde, 0xe6, 0x24, 0x93,
	0x93, 0x4c, 0x41, 0xba, 0xb2, 0xd8, 0xc5, 0xe6, 0x21, 0xa6, 0x44, 0x6e, 0xd1, 0x7f, 0xa3, 0x01,
	0xba, 0x47, 0x58, 0xbb, 0x4f, 0xc9, 0xc3, 0x80, 0xb2, 0x96, 0x43, 0x99, 0x41, 0x1e, 0xa3, 0x1b,
	0x50, 0xe4, 0x9b, 0xcc, 0x90, 0x3c, 0xae, 0x69, 0x1b, 0xda, 0x66, 0x79, 0xa7, 0xbc, 0xdd, 0xc5,
	0xdb, 0xbb, 0x98, 0x12, 0x83, 0x3c, 0x36, 0x16, 0x0e, 0xe5, 0x00, 0xad, 0x03, 0x58, 0xc7, 0xd8,
	0xf7, 0x89, 0x6b, 0x3a, 0x76, 0x2d, 0xb3, 0xa1, 0x6d, 0x2e, 0x1a, 0x25, 0xb5, 0xd2, 0xb4, 0xd1,
	0x2e, 0x94, 0xdc, 0x00, 0xdb, 0xa6, 0x17, 0x84, 0xa4, 0x96, 0x15, 0x72, 0x3e, 0xb7, 0x3d, 0xae,
	0xd4, 0x36, 0x3f, 0xfe, 0x2e, 0x21, 0x36, 0x6d, 0x05, 0xd8, 0x6e, 0x07, 0x21, 0x31, 0x8a, 0xae,
	0x1a, 0xa1, 0x65, 0xc8, 0x5b, 0x41, 0xdf, 0x67, 0xb5, 0x9c, 0x90, 0x2e, 0x27, 0xfa, 0xdf, 0x35,
	0xb8, 0x34, 0xa6, 0x37, 0xed, 0xa1, 0xcf, 0x43, 0x49, 0x29, 0x4e, 0x7b, 0x4a, 0xf3, 0xca, 0x50,
	0x73, 0xda, 0x33, 0x8a, 0x87, 0x6a, 0x84, 0x76, 0x20, 0xcf, 0x55, 0xa0, 0xb5, 0xcc, 0x46, 0x76,
	0xb3, 0xbc, 0xf3, 0xfa, 0x34, 0xc5, 0xb8, 0x7c, 0x43, 0x6e, 0x45, 0x9b, 0x50, 0xed, 0xfb, 0x21,
	0xe1, 0x90, 0x68, 0xd7, 0x94, 0x7a, 0x65, 0x85, 0x5e, 0x4b, 0x72, 0xbd, 0x4d, 0xbb, 0x7b, 0x7c,
	0x35, 0x09, 0x3d, 0xf7, 0x5c, 0xd0, 0xf5, 0xdf, 0x6a, 0xb0, 0xcc, 0x41, 0x9e, 0xfd, 0xcf, 0x5d,
	0xcf, 0x3f, 0x34, 0x58, 0x99, 0xa0, 0xf9, 0xff, 0xdb, 0x05, 0xfd, 0x30, 0x07, 0xc5, 0x48, 0x03,
	0xb4, 0x04, 0x19, 0xc7, 0x16, 0x90, 0x4a, 0x46, 0xc6, 0xb1, 0xd1, 0x5d, 0xa8, 0x58, 0x81, 0xcf,
	0x88, 0xcf, 0x4c, 0x76, 0xd6, 0x23, 0xc2, 0xfc, 0x4b, 0x3b, 0x6f, 0x4d, 0x3b, 0x63, 0x4f, 0xee,
	0x3d, 0x38, 0xeb, 0x11, 0xa3, 0x6c, 0x0d, 0x27, 0x08, 0x41, 0x8e, 0x91, 0xa7, 0x12, 0x46, 0xc9,
	0x10, 0x63, 0x74, 0x07, 0x4a, 0x7d, 0x4a, 0x42, 0xd3, 0xf1, 0x8f, 0x02, 0xa5, 0xfc, 0xc6, 0x34,
	0xc1, 0x8f, 0x28, 0x09, 0x9b, 0xfe, 0x51, 0x60, 0x14, 0xfb, 0x6a, 0x84, 0xde, 0x81, 0x02, 0x65,
	0x98, 0xf5, 0x69, 0x2d, 0x2f, 0x94, 0x7a, 0x73, 0x12, 0xaf, 0x52, 0xe8, 0x7d, 0xb1, 0xd1, 0x50,
	0x0c, 0xe8, 0x2d, 0x58, 0xb4, 0x02, 0xcf, 0xe3, 0xa8, 0xa4, 0x75, 0x0b, 0xc2, 0xba, 0x15, 0xb5,
	0x28, 0x6d, 0xbb, 0x0e, 0xe0, 0x3a, 0x27, 0x44, 0xed, 0x58, 0x90, 0x7e, 0xc7, 0x57, 0x06, 0x64,
	0x2b, 0x24, 0x98, 0x11, 0xdb, 0xc4, 0xac, 0x56, 0x54, 0x6e, 0x29, 0x57, 0xea, 0x0c, 0x5d, 0x85,
	0xd2, 0x31, 0x3e, 0x25, 0x26, 0x67, 0xa8, 0x95, 0x36, 0xb4, 0xcd, 0xa2, 0x51, 0xe4, 0x0b, 0x2d,
	0xe7, 0x84, 0xa0, 0x55, 0x58, 0xf0, 0x82, 0xc0, 0xe6, 0xfe, 0x0c, 0xc2, 0x20, 0x05, 0x3e, 0x6d,
	0xda, 0x9c, 0x20, 0xb8, 0x30, 0xab, 0x95, 0x05, 0x4f, 0x81, 0x4f, 0xeb, 0x0c, 0x35, 0xa0, 0x24,
	0x39, 0xb8, 0xad, 0x2a, 0xc2, 0x56, 0x9b, 0x9f, 0xe4, 0x4a, 0xdc, 0x5e, 0x6d, 0x2e, 0x53, 0xd8,
	0xcc, 0x53, 0x23, 0x74, 0x19, 0x0a, 0x34, 0xe8, 0x87, 0x16, 0xa9, 0x2d, 0xca, 0x73, 0xe5, 0x4c,
	0x7f, 0xa6, 0xc1, 0xf2, 0x24, 0xd6, 0xb8, 0xa6, 0x5a, 0x42, 0xd3, 0x35, 0x10, 0x52, 0xcd, 0x7e,
	0xe8, 0x0a, 0xa7, 0x28, 0x19, 0x62, 0xe3, 0xa3, 0xd0, 0x1d, 0x90, 0x3c, 0xfb, 0xb6, 0xba, 0x6f,
	0x41, 0x6a, 0xdb, 0xb7, 0xb9, 0x55, 0x04, 0x49, 0xf8, 0x42, 0x4e, 0xd0, 0xc4, 0xde, 0x03, 0xf2,
	0x94, 0xe9, 0xff, 0xd1, 0xe0, 0x6a, 0x14, 0x0e, 0x71, 0x28, 0xee, 0x41, 0x5c, 0xc7, 0xbc, 0x01,
	0x63, 0x15, 0x16, 0x84, 0x45, 0x54, 0xb4, 0x28, 0x19, 0x05, 0x3e, 0x7d, 0x91, 0xa1, 0x02, 0x5d,
	0x87, 0x25, 0xf2, 0x94, 0x3b, 0x93, 0x74, 0x2a, 0xc7, 0x16, 0x3e, 0x59, 0x32, 0x2a, 0xe4, 0x69,
	0x84, 0xa2, 0x69, 0x8f, 0x44, 0xb2, 0xc2, 0x48, 0x24, 0xd3, 0xff, 0xac, 0xc1, 0xeb, 0xd3, 0xf1,
	0xcf, 0x17, 0x76, 0xea, 0x50, 0x54, 0xca, 0x44, 0x91, 0x67, 0x2a, 0xd2, 0xc4, 0x59, 0xc6, 0x80,
	0x2d, 0x0d, 0x6b, 0xe9, 0xcf, 0x32, 0x70, 0x71, 0xec, 0x0c, 0xf4, 0x0e, 0x2c, 0xa8, 0x53, 0x14,
	0x8a, 0x6b, 0xd3, 0xe3, 0x89, 0xd4, 0x2a, 0xda, 0x8f, 0x76, 0xa1, 0x42, 0xfb, 0x87, 0xe6, 0x08,
	0xb6, 0x73, 0xf9, 0xcb, 0xb4, 0x7f, 0xb8, 0x97, 0x22, 0x30, 0xb4, 0x05, 0x17, 0x63, 0x7a, 0x98,
	0x71, 0x97, 0xb8, 0x30, 0x3c, 0x4b, 0x44, 0x0a, 0xfd, 0x4f, 0x1a, 0xd4, 0xd4, 0xbd, 0xbe, 0x3f,
	0x20, 0xcd, 0xeb, 0xd4, 0x5b, 0x70, 0xb1, 0x27, 0x8c, 0x18, 0x77, 0x32, 0xe9, 0xde, 0x17, 0x7a,
	0x71, 0xeb, 0xbe, 0xd0, 0x27, 0xf1, 0x8f, 0x1a, 0xac, 0x4d, 0x81, 0x32, 0x9f, 0x7f, 0x7e, 0x63,
	0xcc, 0x3f, 0xcf, 0xbd, 0xc3, 0x74, 0x3d, 0xf3, 0xdf, 0x19, 0x28, 0xc7, 0xa4, 0x7f, 0x46, 0x1e,
	0xbe, 0xe4, 0xa3, 0x55, 0x18, 0x7d, 0xb4, 0xda, 0x80, 0x42, 0xd2, 0x73, 0xcf, 0x4c, 0x16, 0x98,
	0x43, 0x0d, 0x17, 0x66, 0xd4, 0xf0, 0x82, 0xe0, 0x3d, 0x08, 0xa2, 0x85, 0xf8, 0x6b, 0x56, 0x8c,
	0xbf, 0x66, 0xfa, 0xbf, 0x86, 0x6e, 0xa4, 0xf4, 0xe4, 0xcf, 0x62, 0xca, 0x89, 0x61, 0xec, 0x19,
	0xc8, 0x26, 0x9e, 0x01, 0xce, 0x37, 0xfc, 0x86, 0xe4, 0x2b, 0x54, 0xb2, 0x26, 0x7f, 0x3d, 0xf9,
	0x4f, 0xf9, 0xf5, 0x14, 0xe2, 0x5f, 0xcf, 0xef, 0x35, 0xb8, 0x32, 0x0d, 0xf6, 0xdc, 0x59, 0x25,
	0x4f, 0x2c, 0xce, 0xcd, 0x2a, 0xb9, 0x7c, 0x43, 0x6e, 0x4d, 0xe5, 0xab, 0x39, 0x96, 0xa9, 0xa2,
	0x48, 0x62, 0x12, 0x5e, 0xac, 0xcd, 0xed, 0xc5, 0x49, 0x57, 0xcc, 0x8c, 0xb8, 0xa2, 0xfe, 0x7d,
	0xa8, 0xc4, 0x19, 0x51, 0x15, 0xb2, 0x7d, 0xf5, 0x81, 0x2e, 0x1a, 0x7c, 0x88, 0xae, 0x40, 0xd1,
	0x77, 0xac, 0x13, 0x1f, 0x7b, 0x44, 0x05, 0xc2, 0xc1, 0x1c, 0xd5, 0x60, 0x01, 0x5b, 0xc3, 0xc4,
	0xb9, 0x64, 0x44, 0x53, 0xfe, 0x52, 0x5b, 0xc7, 0xc4, 0x3a, 0x31, 0x1d, 0xdf, 0x64, 0x0e, 0x73,
	0x89, 0x72, 0x80, 0x8a, 0x58, 0x6d, 0xfa, 0x07, 0x7c, 0x4d, 0x6f, 0x41, 0x55, 0xe0, 0xc4, 0x94,
	0x71, 0x53, 0x08, 0x0d, 0xf8, 0x97, 0xec, 0x78, 0x44, 0xa8, 0x90, 0x33, 0xc4, 0x58, 0x45, 0x8d,
	0xcc, 0x20, 0x6a, 0x2c, 0x43, 0x9e, 0x5a, 0x91, 0x7d, 0x35, 0x43, 0x4e, 0xf4, 0x5f, 0x68, 0xf2,
	0x15, 0x4c, 0x58, 0x95, 0xe7, 0x42, 0x2e, 0xa6, 0xcc, 0xec, 0xe1, 0x2e, 0x51, 0xb8, 0x8a, 0x7c,
	0xe1, 0x21, 0xee, 0x12, 0x91, 0x7c, 0x72, 0xa2, 0xc4, 0xa0, 0xac, 0xc3, 0x57, 0x64, 0xf2, 0xf9,
	0x2d, 0x58, 0x12, 0xe4, 0x23, 0x42, 0x54, 0x4e, 0x28, 0x2f, 0xf4, 0xfa, 0x54, 0x47, 0x88, 0x21,
	0x31, 0x2a, 0x6e, 0x6c, 0xa6, 0x7f, 0x47, 0xd6, 0x67, 0x2a, 0xfb, 0xdb, 0x27, 0x0c, 0x3b, 0x6e,
	0x1a, 0xe9, 0x96, 0xce, 0x64, 0xf9, 0x34, 0x22, 0x78, 0x3e, 0x47, 0x7f, 0x1b, 0x72, 0x5c, 0x9a,
	0x90, 0x7c, 0x5e, 0xf5, 0x24, 0x76, 0xea, 0x1f, 0x6a, 0xb0, 0xc4, 0x97, 0xea, 0xb6, 0x2d, 0xbc,
	0x3f, 0x8d, 0xc4, 0x31, 0x19, 0x31, 0xb2, 0xa3, 0x11, 0x63, 0x98, 0x55, 0xe7, 0x12, 0x59, 0xf5,
	0xbb, 0x70, 0x21, 0xa1, 0xc9, 0x5c, 0xd0, 0xf5, 0x50, 0xfa, 0xa0, 0x41, 0x28, 0x61, 0x2f, 0x09,
	0x89, 0xfe, 0x4d, 0xe9, 0xa8, 0xb1, 0x33, 0xe7, 0xd3, 0xf9, 0xa7, 0x1a, 0x20, 0x71, 0x1f, 0xfd,
	0x43, 0xd7, 0xa1, 0xc7, 0xe2, 0x5a, 0xd2, 0x8b, 0xe8, 0x91, 0x33, 0x64, 0x67, 0x76, 0x86, 0x1f,
	0x6b, 0x70, 0x69, 0x4c, 0x9f, 0x17, 0xec, 0x81, 0x3c, 0x54, 0x31, 0xa7, 0xa7, 0x8c, 0xcb, 0x87,
	0xfa, 0x5f, 0x35, 0x58, 0x89, 0xa9, 0x11, 0x65, 0x34, 0x69, 0x5c, 0x68, 0x2c, 0x97, 0xce, 0xce,
	0x99, 0x4b, 0x4f, 0x4c, 0x29, 0x73, 0x93, 0x53, 0xca, 0xe4, 0xcd, 0xe4, 0x47, 0x4b, 0x97, 0x9f,
	0x69, 0x70, 0x79, 0x12, 0xc0, 0xf9, 0x4c, 0x1d, 0xc3, 0x92, 0x99, 0x13, 0xcb, 0xb8, 0xcd, 0xbf,
	0x27, 0x3d, 0xf1, 0x3e, 0x3e, 0x25, 0x1d, 0xf2, 0x24, 0x5d, 0x4f, 0xd4, 0xff, 0xa6, 0xfc, 0x2a,
	0x21, 0x7d, 0x3e, 0xb0, 0x6b, 0x20, 0xfa, 0x01, 0xa6, 0x4f, 0x9e, 0x08, 0xf9, 0x45, 0x43, 0x24,
	0x4b, 0x1d, 0xf2, 0x84, 0x5f, 0x8c, 0xea, 0xff, 0xc4, 0x1a, 0x10, 0xb2, 0x01, 0x74, 0x41, 0x12,
	0x5a, 0x83, 0x36, 0xc4, 0xdb, 0xb0, 0xac, 0xf6, 0x4e, 0xaa, 0x45, 0x90, 0xa4, 0xc5, 0xcb, 0x11,
	0x74, 0x03, 0x94, 0x10, 0x13, 0x47, 0x9b, 0xe5, 0x7d, 0x2e, 0xca, 0xe5, 0xba, 0x2a, 0x5b, 0xfe,
	0xa9, 0xc1, 0x6a, 0x87, 0x3c, 0x69, 0xfa, 0x8c, 0x84, 0xd8, 0x62, 0xce, 0x29, 0x69, 0xd3, 0x6e,
	0x27, 0x60, 0xce, 0xd1, 0xd9, 0x88, 0x79, 0xb4, 0xd1, 0x0f, 0x55, 0xdd, 0x46, 0x66, 0x70, 0x1b,
	0xe8, 0x4b, 0xb0, 0x12, 0x87, 0x34, 0xda, 0xd7, 0x42, 0x43, 0x58, 0x83, 0xde, 0xd6, 0x57, 0xa1,
	0x36, 0x82, 0x6c, 0xc8, 0x25, 0xd1, 0xad, 0x24, 0xd0, 0x0d, 0x18, 0x6f, 0xc2, 0xa5, 0x21, 0xc0,
	0x21, 0x8f, 0x04, 0x59, 0x8d, 0x40, 0x46, 0xdb, 0x75, 0x0f, 0x16, 0xd5, 0x15, 0xce, 0x06, 0x2e,
	0x91, 0xf7, 0x64, 0xe6, 0xcd, 0x7b, 0xf4, 0x3f, 0x68, 0xb0, 0xde, 0xc6, 0xe1, 0x09, 0x27, 0x27,
	0x6d, 0x6b, 0x10, 0x6c, 0xa7, 0x18, 0x2d, 0x57, 0xa0, 0xc0, 0xc1, 0x0f, 0xc2, 0x7c, 0xde, 0xa3,
	0xdd, 0xa6, 0x8d, 0x1a, 0x50, 0xe4, 0xcb, 0xa2, 0xa8, 0xc9, 0x89, 0xfa, 0x61, 0x6b, 0x9a, 0xf6,
	0x49, 0xf5, 0x44, 0x6d, 0xb3, 0xe0, 0xc9, 0x81, 0xfe, 0x1e, 0xbc, 0xf1, 0x49, 0x28, 0xe6, 0x7b,
	0x36, 0x9e, 0x65, 0x06, 0x15, 0xf2, 0xa8, 0xb0, 0xd4, 0xcc, 0x11, 0xc7, 0x9d, 0x7d, 0x6e, 0xdc,
	0x69, 0x74, 0x5c, 0x87, 0xd5, 0x41, 0x3e, 0x5e, 0x1d, 0xfc, 0x24, 0x33, 0x28, 0x8a, 0x46, 0x8d,
	0x30, 0x5f, 0x64, 0xf9, 0x3a, 0xe4, 0x3c, 0xda, 0x8d, 0x6a, 0x83, 0x1b, 0xb3, 0xa1, 0x34, 0x04,
	0x0f, 0xaa, 0x47, 0xaa, 0xc9, 0xc7, 0xe4, 0x0b, 0xb3, 0x31, 0x8b, 0x0f, 0x29, 0xea, 0x85, 0xa5,
	0xd1, 0x93, 0xfe, 0xb5, 0x06, 0xab, 0x53, 0x8e, 0x99, 0x1e, 0x4a, 0xb4, 0xe7, 0x0a, 0x25, 0x99,
	0xe7, 0x08, 0x25, 0xd9, 0x29, 0xa1, 0xe4, 0xe3, 0x8c, 0x7c, 0x74, 0x92, 0x6a, 0x8f, 0xf5, 0x16,
	0xee, 0x40, 0xe9, 0x28, 0x0c, 0x3c, 0x51, 0x62, 0xcf, 0x1e, 0x41, 0x38, 0x0b, 0x9f, 0xa1, 0x77,
	0xa1, 0x18, 0x21, 0x3f, 0xef, 0xcd, 0x57, 0x56, 0xb8, 0xff, 0x9a, 0xb1, 0xe0, 0xca, 0x21, 0x6a,
	0x40, 0x39, 0x66, 0x04, 0x75, 0x41, 0xfa, 0x39, 0x0f, 0xad, 0x94, 0x11, 0xa5, 0x8e, 0x5c, 0xcc,
	0x57, 0xa0, 0x20, 0x4d, 0x22, 0x4a, 0xdc, 0xf2, 0xce, 0xfa, 0x34, 0x09, 0x75, 0xc5, 0x9c, 0xc7,
	0x82, 0x0f, 0x41, 0xce, 0x26, 0xd4, 0x52, 0xfd, 0x51, 0x31, 0xde, 0xcd, 0x43, 0xd6, 0xa3, 0x5d,
	0xfd, 0x23, 0x4d, 0xb6, 0x64, 0x94, 0xd2, 0x83, 0xcc, 0x4b, 0x9b, 0x39, 0xf3, 0xfa, 0x14, 0x09,
	0x44, 0xb2, 0x1c, 0xcd, 0x8e, 0x96, 0xa3, 0xbf, 0x54, 0x55, 0xc5, 0xd0, 0x1e, 0xaf, 0x94, 0x7a,
	0x3f, 0xd7, 0xa0, 0x34, 0x30, 0xf6, 0x2b, 0xa5, 0xd9, 0x29, 0x2c, 0xef, 0x13, 0x97, 0x30, 0x12,
	0xeb, 0x7a, 0xbc, 0x8c, 0x4a, 0xe6, 0x07, 0xb0, 0x32, 0xe1, 0xdc, 0x97, 0x95, 0x8f, 0xea, 0x18,
	0x6a, 0x7b, 0xc7, 0xc4, 0x3a, 0x89, 0x25, 0xc5, 0x86, 0xd3, 0x3d, 0x4e, 0x33, 0x07, 0xfd, 0x95,
	0x06, 0x6b, 0x53, 0xce, 0x98, 0x0f, 0xe6, 0x35, 0x28, 0x5b, 0xd8, 0x37, 0x7b, 0x52, 0x84, 0x4a,
	0x46, 0xc1, 0xc2, 0xbe, 0x12, 0xca, 0xeb, 0xdb, 0x90, 0x60, 0x1a, 0xf8, 0x51, 0x23, 0x4d, 0xce,
	0xd0, 0x0e, 0xac, 0x58, 0xd8, 0xf7, 0x83, 0x61, 0x01, 0xa1, 0xb6, 0xc9, 0x22, 0xe2, 0x92, 0x24,
	0x0e, 0x8a, 0x01, 0x4e, 0xd2, 0xff, 0xa2, 0x7e, 0x69, 0x8a, 0x85, 0xc8, 0xa6, 0xc7, 0x9d, 0xf6,
	0x6b, 0x22, 0x08, 0x28, 0x55, 0x67, 0x7d, 0xb4, 0x38, 0xcb, 0x79, 0x0f, 0xff, 0x9b, 0x50, 0x89,
	0xc8, 0xa2, 0x57, 0x24, 0x31, 0x94, 0xd5, 0x5a, 0x07, 0x7b, 0x04, 0xdd, 0x91, 0xa9, 0xd2, 0xe9,
	0x8e, 0x0a, 0x87, 0xb3, 0x1e, 0xcf, 0x53, 0xaa, 0x6f, 0xef, 0xe8, 0x1f, 0x0f, 0x1b, 0xfc, 0x3c,
	0x40, 0x1b, 0xc4, 0x0a, 0x42, 0x9e, 0xc9, 0xf5, 0x09, 0x65, 0xaf, 0xfe, 0xcf, 0xdc, 0xbf, 0x1b,
	0x36, 0x63, 0xe3, 0xda, 0xd3, 0x5e, 0xe0, 0x53, 0xf2, 0xa2, 0x7f, 0xea, 0x4e, 0xa3, 0x29, 0x69,
	0x0f, 0x7f, 0x36, 0x0c, 0x28, 0xab, 0xb3, 0xbb, 0xa1, 0x43, 0x7c, 0x9b, 0xa6, 0x7b, 0x01, 0xfa,
	0x87, 0xb1, 0x5f, 0xe7, 0x92, 0xc7, 0xcc, 0x6f, 0xa9, 0xa8, 0x84, 0x70, 0x1d, 0xd1, 0x58, 0xc8,
	0xce, 0x5e, 0x42, 0xb4, 0x1c, 0xca, 0xf4, 0x03, 0x00, 0x19, 0xec, 0xf7, 0x31, 0xc3, 0x69, 0x75,
	0x46, 0xb7, 0x1e, 0xc8, 0x6e, 0x55, 0xec, 0x97, 0x0c, 0xb4, 0x0e, 0x6b, 0x23, 0x4b, 0xe6, 0xa3,
	0xce, 0x7e, 0xe3, 0x6e, 0xb3, 0xd3, 0xd8, 0xaf, 0xbe, 0x36, 0x89, 0xfc, 0xb0, 0x55, 0x6f, 0x76,
	0x0e, 0x1a, 0x1f, 0x1c, 0x54, 0xb5, 0xad, 0x8f, 0x34, 0x58, 0x4c, 0xfc, 0x0c, 0x81, 0xae, 0xc2,
	0x6a, 0x62, 0x21, 0x21, 0x6d, 0x8c, 0xd8, 0xf8, 0xa0, 0xde, 0x6e, 0x76, 0x9a, 0x9d, 0x7b, 0x55,
	0x0d, 0xad, 0xc1, 0x4a, 0x92, 0xd8, 0x6c, 0xb5, 0x1a, 0xf7, 0xea, 0xad, 0x6a, 0x06, 0xd5, 0x60,
	0x39, 0x49, 0xea, 0x3c, 0x30, 0xda, 0xf5, 0x56, 0x35, 0x3b, 0xce, 0xb4, 0xdf, 0x68, 0x35, 0x0e,
	0x1a, 0xfb, 0xd5, 0xdc, 0xd6, 0x8f, 0x54, 0xc3, 0x62, 0x3c, 0xd5, 0x47, 0xd7, 0x61, 0x63, 0x32,
	0x25, 0xa1, 0xad, 0x0e, 0x6f, 0x4c, 0xd9, 0xb5, 0xf7, 0xa0, 0xdd, 0x6e, 0x74, 0x0e, 0xaa, 0x1a,
	0xba, 0x06, 0x57, 0xa7, 0xec, 0x69, 0x35, 0xdf, 0x6b, 0x54, 0x33, 0xbb, 0xf7, 0xa1, 0x66, 0x05,
	0xde, 0xf6, 0x99, 0x73, 0x16, 0xf4, 0xc5, 0xfd, 0x07, 0x36, 0x71, 0xe5, 0x9f, 0x9a, 0xbe, 0xfb,
	0xc5, 0x6e, 0xe0, 0x62, 0xbf, 0xbb, 0x7d, 0x7b, 0x87, 0xb1, 0x6d, 0x2b, 0xf0, 0x6e, 0x89, 0x65,
	0x2b, 0x70, 0x6f, 0xe1, 0x5e, 0xef, 0xd6, 0xc8, 0x9f, 0xa5, 0x0e, 0x0b, 0x82, 0xfa, 0xe5, 0xff,
	0x06, 0x00, 0x00, 0xff, 0xff, 0x06, 0x95, 0x09, 0xed, 0x46, 0x25, 0x00, 0x00,
}
