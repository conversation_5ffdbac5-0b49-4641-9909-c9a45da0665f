// Code generated by protoc-gen-go. DO NOT EDIT.
// source: unified-interface_.proto

package unified_interface // import "golang.52tt.com/protocol/app/unified-interface"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UnifiedChannelShowType int32

const (
	UnifiedChannelShowType_UNIFIED_CHANNEL_SHOW_TYPE_UNKNOWN UnifiedChannelShowType = 0
	UnifiedChannelShowType_UNIFIED_CHANNEL_SHOW_TYPE_LOTTERY UnifiedChannelShowType = 1
)

var UnifiedChannelShowType_name = map[int32]string{
	0: "UNIFIED_CHANNEL_SHOW_TYPE_UNKNOWN",
	1: "UNIFIED_CHANNEL_SHOW_TYPE_LOTTERY",
}
var UnifiedChannelShowType_value = map[string]int32{
	"UNIFIED_CHANNEL_SHOW_TYPE_UNKNOWN": 0,
	"UNIFIED_CHANNEL_SHOW_TYPE_LOTTERY": 1,
}

func (x UnifiedChannelShowType) String() string {
	return proto.EnumName(UnifiedChannelShowType_name, int32(x))
}
func (UnifiedChannelShowType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_interface__2d565030c1a6ac78, []int{0}
}

type UnifiedChannelShowStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          int32        `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelRole          int32        `protobuf:"varint,4,opt,name=channel_role,json=channelRole,proto3" json:"channel_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UnifiedChannelShowStatusReq) Reset()         { *m = UnifiedChannelShowStatusReq{} }
func (m *UnifiedChannelShowStatusReq) String() string { return proto.CompactTextString(m) }
func (*UnifiedChannelShowStatusReq) ProtoMessage()    {}
func (*UnifiedChannelShowStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_interface__2d565030c1a6ac78, []int{0}
}
func (m *UnifiedChannelShowStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedChannelShowStatusReq.Unmarshal(m, b)
}
func (m *UnifiedChannelShowStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedChannelShowStatusReq.Marshal(b, m, deterministic)
}
func (dst *UnifiedChannelShowStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedChannelShowStatusReq.Merge(dst, src)
}
func (m *UnifiedChannelShowStatusReq) XXX_Size() int {
	return xxx_messageInfo_UnifiedChannelShowStatusReq.Size(m)
}
func (m *UnifiedChannelShowStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedChannelShowStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedChannelShowStatusReq proto.InternalMessageInfo

func (m *UnifiedChannelShowStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UnifiedChannelShowStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UnifiedChannelShowStatusReq) GetChannelType() int32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *UnifiedChannelShowStatusReq) GetChannelRole() int32 {
	if m != nil {
		return m.ChannelRole
	}
	return 0
}

type UnifiedChannelShowStatusResp struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ShowList             []UnifiedChannelShowType `protobuf:"varint,2,rep,packed,name=show_list,json=showList,proto3,enum=ga.UnifiedChannelShowType" json:"show_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UnifiedChannelShowStatusResp) Reset()         { *m = UnifiedChannelShowStatusResp{} }
func (m *UnifiedChannelShowStatusResp) String() string { return proto.CompactTextString(m) }
func (*UnifiedChannelShowStatusResp) ProtoMessage()    {}
func (*UnifiedChannelShowStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_interface__2d565030c1a6ac78, []int{1}
}
func (m *UnifiedChannelShowStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnifiedChannelShowStatusResp.Unmarshal(m, b)
}
func (m *UnifiedChannelShowStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnifiedChannelShowStatusResp.Marshal(b, m, deterministic)
}
func (dst *UnifiedChannelShowStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnifiedChannelShowStatusResp.Merge(dst, src)
}
func (m *UnifiedChannelShowStatusResp) XXX_Size() int {
	return xxx_messageInfo_UnifiedChannelShowStatusResp.Size(m)
}
func (m *UnifiedChannelShowStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnifiedChannelShowStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnifiedChannelShowStatusResp proto.InternalMessageInfo

func (m *UnifiedChannelShowStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UnifiedChannelShowStatusResp) GetShowList() []UnifiedChannelShowType {
	if m != nil {
		return m.ShowList
	}
	return nil
}

func init() {
	proto.RegisterType((*UnifiedChannelShowStatusReq)(nil), "ga.UnifiedChannelShowStatusReq")
	proto.RegisterType((*UnifiedChannelShowStatusResp)(nil), "ga.UnifiedChannelShowStatusResp")
	proto.RegisterEnum("ga.UnifiedChannelShowType", UnifiedChannelShowType_name, UnifiedChannelShowType_value)
}

func init() {
	proto.RegisterFile("unified-interface_.proto", fileDescriptor_unified_interface__2d565030c1a6ac78)
}

var fileDescriptor_unified_interface__2d565030c1a6ac78 = []byte{
	// 351 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x91, 0xe1, 0x4b, 0xfa, 0x40,
	0x18, 0x80, 0x7f, 0xd3, 0x5f, 0xa5, 0xa7, 0x86, 0xec, 0x43, 0x0c, 0x2b, 0x98, 0x42, 0xb1, 0x82,
	0x26, 0x18, 0xd1, 0xe7, 0xb4, 0x85, 0x96, 0xcc, 0x38, 0x27, 0x62, 0x5f, 0x8e, 0xdb, 0x76, 0xce,
	0xc1, 0xb9, 0x3b, 0x77, 0x27, 0xb2, 0xaf, 0xfd, 0x39, 0xfd, 0x95, 0x31, 0xb7, 0xa8, 0xb0, 0xfa,
	0xf6, 0xf2, 0xbc, 0xcf, 0x1d, 0x0f, 0xbc, 0x40, 0x5b, 0x47, 0xe1, 0x3c, 0x24, 0xfe, 0x55, 0x18,
	0x49, 0x12, 0xcf, 0xb1, 0x47, 0x90, 0xc9, 0x63, 0x26, 0x99, 0x5a, 0x08, 0x70, 0xa3, 0x16, 0x60,
	0xe4, 0x62, 0x41, 0x32, 0xd4, 0x7a, 0x53, 0xc0, 0xf1, 0x24, 0xf3, 0x7b, 0x0b, 0x1c, 0x45, 0x84,
	0x8e, 0x17, 0x6c, 0x33, 0x96, 0x58, 0xae, 0x05, 0x24, 0x2b, 0xf5, 0x1c, 0x94, 0x52, 0x1b, 0xc5,
	0x64, 0xa5, 0x29, 0xba, 0x62, 0x54, 0x3a, 0x15, 0x33, 0xc0, 0x66, 0x17, 0x0b, 0x02, 0xc9, 0x0a,
	0x1e, 0xb8, 0xd9, 0xa0, 0x9e, 0x02, 0xe0, 0x65, 0xef, 0x51, 0xe8, 0x6b, 0x05, 0x5d, 0x31, 0x6a,
	0xb0, 0x9c, 0x93, 0x81, 0xaf, 0x36, 0x41, 0xf5, 0x63, 0x2d, 0x13, 0x4e, 0xb4, 0xa2, 0xae, 0x18,
	0x7b, 0xb0, 0x92, 0x33, 0x27, 0xe1, 0xe4, 0xab, 0x12, 0x33, 0x4a, 0xb4, 0xff, 0xdf, 0x14, 0xc8,
	0x28, 0x69, 0xbd, 0x2a, 0xe0, 0xe4, 0xf7, 0x58, 0xc1, 0xd5, 0x0b, 0x50, 0xce, 0x6b, 0x05, 0xcf,
	0x73, 0xab, 0x9f, 0xb9, 0x82, 0xc3, 0x92, 0x9b, 0x4f, 0xea, 0x2d, 0x28, 0x8b, 0x05, 0xdb, 0x20,
	0x1a, 0x0a, 0xa9, 0x15, 0xf4, 0xa2, 0x71, 0xd8, 0x69, 0xa4, 0xea, 0xee, 0xff, 0x69, 0x1d, 0x2c,
	0xa5, 0xf2, 0x30, 0x14, 0xf2, 0x72, 0x0e, 0x8e, 0x7e, 0x76, 0xd4, 0x33, 0xd0, 0x9c, 0xd8, 0x83,
	0x87, 0x81, 0x75, 0x8f, 0x7a, 0xfd, 0x3b, 0xdb, 0xb6, 0x86, 0x68, 0xdc, 0x1f, 0x4d, 0x91, 0x33,
	0x7b, 0xb6, 0xd0, 0xc4, 0x7e, 0xb2, 0x47, 0x53, 0xbb, 0xfe, 0xef, 0x6f, 0x6d, 0x38, 0x72, 0x1c,
	0x0b, 0xce, 0xea, 0x4a, 0xf7, 0x11, 0x68, 0x1e, 0x5b, 0x9a, 0x49, 0x98, 0xb0, 0x75, 0x1a, 0xb6,
	0x64, 0x3e, 0xa1, 0xd9, 0xd5, 0x5e, 0xcc, 0x80, 0x51, 0x1c, 0x05, 0xe6, 0x4d, 0x47, 0x4a, 0xd3,
	0x63, 0xcb, 0xf6, 0x16, 0x7b, 0x8c, 0xb6, 0x31, 0xe7, 0xed, 0x9d, 0xfb, 0xbb, 0xfb, 0xdb, 0xfd,
	0xf5, 0x7b, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf2, 0xa5, 0xd7, 0xf4, 0x1b, 0x02, 0x00, 0x00,
}
