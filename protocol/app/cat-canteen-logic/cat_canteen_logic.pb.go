// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cat_canteen_logic/cat_canteen_logic.proto

package cat_canteen_logic // import "golang.52tt.com/protocol/app/cat-canteen-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type NotifyPlaceType int32

const (
	NotifyPlaceType_NOTIFY_PLACE_TYPE_UNSPECIFIED NotifyPlaceType = 0
	NotifyPlaceType_NOTIFY_PLACE_TYPE_CHANNEL     NotifyPlaceType = 1
	NotifyPlaceType_NOTIFY_PLACE_TYPE_GAME        NotifyPlaceType = 2
	NotifyPlaceType_NOTIFY_PLACE_TYPE_PUBLIC      NotifyPlaceType = 3
)

var NotifyPlaceType_name = map[int32]string{
	0: "NOTIFY_PLACE_TYPE_UNSPECIFIED",
	1: "NOTIFY_PLACE_TYPE_CHANNEL",
	2: "NOTIFY_PLACE_TYPE_GAME",
	3: "NOTIFY_PLACE_TYPE_PUBLIC",
}
var NotifyPlaceType_value = map[string]int32{
	"NOTIFY_PLACE_TYPE_UNSPECIFIED": 0,
	"NOTIFY_PLACE_TYPE_CHANNEL":     1,
	"NOTIFY_PLACE_TYPE_GAME":        2,
	"NOTIFY_PLACE_TYPE_PUBLIC":      3,
}

func (x NotifyPlaceType) String() string {
	return proto.EnumName(NotifyPlaceType_name, int32(x))
}
func (NotifyPlaceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{0}
}

type ResultType int32

const (
	ResultType_RESULT_TYPE_UNSPECIFIED         ResultType = 0
	ResultType_RESULT_TYPE_UPGRADE_FAIL        ResultType = 1
	ResultType_RESULT_TYPE_UPGRADE_SUCCESS     ResultType = 2
	ResultType_RESULT_TYPE_CROSS_LEVEL_UPGRADE ResultType = 3
)

var ResultType_name = map[int32]string{
	0: "RESULT_TYPE_UNSPECIFIED",
	1: "RESULT_TYPE_UPGRADE_FAIL",
	2: "RESULT_TYPE_UPGRADE_SUCCESS",
	3: "RESULT_TYPE_CROSS_LEVEL_UPGRADE",
}
var ResultType_value = map[string]int32{
	"RESULT_TYPE_UNSPECIFIED":         0,
	"RESULT_TYPE_UPGRADE_FAIL":        1,
	"RESULT_TYPE_UPGRADE_SUCCESS":     2,
	"RESULT_TYPE_CROSS_LEVEL_UPGRADE": 3,
}

func (x ResultType) String() string {
	return proto.EnumName(ResultType_name, int32(x))
}
func (ResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{1}
}

type GameNotifyInfo_NotifyType int32

const (
	GameNotifyInfo_NOTIFY_TYPE_UNSPECIFIED GameNotifyInfo_NotifyType = 0
	GameNotifyInfo_NOTIFY_TYPE_COMMON      GameNotifyInfo_NotifyType = 1
)

var GameNotifyInfo_NotifyType_name = map[int32]string{
	0: "NOTIFY_TYPE_UNSPECIFIED",
	1: "NOTIFY_TYPE_COMMON",
}
var GameNotifyInfo_NotifyType_value = map[string]int32{
	"NOTIFY_TYPE_UNSPECIFIED": 0,
	"NOTIFY_TYPE_COMMON":      1,
}

func (x GameNotifyInfo_NotifyType) String() string {
	return proto.EnumName(GameNotifyInfo_NotifyType_name, int32(x))
}
func (GameNotifyInfo_NotifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{1, 0}
}

type GameAccessNotifyInfo_ChanceGameType int32

const (
	GameAccessNotifyInfo_CHANCE_GAME_TYPE_UNSPECIFIED GameAccessNotifyInfo_ChanceGameType = 0
	GameAccessNotifyInfo_CHANCE_GAME_TYPE_CAT_CANTEEN GameAccessNotifyInfo_ChanceGameType = 1
)

var GameAccessNotifyInfo_ChanceGameType_name = map[int32]string{
	0: "CHANCE_GAME_TYPE_UNSPECIFIED",
	1: "CHANCE_GAME_TYPE_CAT_CANTEEN",
}
var GameAccessNotifyInfo_ChanceGameType_value = map[string]int32{
	"CHANCE_GAME_TYPE_UNSPECIFIED": 0,
	"CHANCE_GAME_TYPE_CAT_CANTEEN": 1,
}

func (x GameAccessNotifyInfo_ChanceGameType) String() string {
	return proto.EnumName(GameAccessNotifyInfo_ChanceGameType_name, int32(x))
}
func (GameAccessNotifyInfo_ChanceGameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{2, 0}
}

type UserLevelStatus_Status int32

const (
	UserLevelStatus_STATUS_UNSPECIFIED        UserLevelStatus_Status = 0
	UserLevelStatus_STATUS_TO_BE_OPEN         UserLevelStatus_Status = 1
	UserLevelStatus_STATUS_IN_OPENING         UserLevelStatus_Status = 2
	UserLevelStatus_STATUS_TEMPORARILY_CLOSED UserLevelStatus_Status = 3
	UserLevelStatus_STATUS_CLOSED             UserLevelStatus_Status = 4
)

var UserLevelStatus_Status_name = map[int32]string{
	0: "STATUS_UNSPECIFIED",
	1: "STATUS_TO_BE_OPEN",
	2: "STATUS_IN_OPENING",
	3: "STATUS_TEMPORARILY_CLOSED",
	4: "STATUS_CLOSED",
}
var UserLevelStatus_Status_value = map[string]int32{
	"STATUS_UNSPECIFIED":        0,
	"STATUS_TO_BE_OPEN":         1,
	"STATUS_IN_OPENING":         2,
	"STATUS_TEMPORARILY_CLOSED": 3,
	"STATUS_CLOSED":             4,
}

func (x UserLevelStatus_Status) String() string {
	return proto.EnumName(UserLevelStatus_Status_name, int32(x))
}
func (UserLevelStatus_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{11, 0}
}

type GameWinningRecord_RecordType int32

const (
	GameWinningRecord_RECORD_TYPE_UNSPECIFIED        GameWinningRecord_RecordType = 0
	GameWinningRecord_RECORD_TYPE_AWARD_POSITIVE_VAL GameWinningRecord_RecordType = 1
	GameWinningRecord_RECORD_TYPE_AWARD_GIFT         GameWinningRecord_RecordType = 2
	GameWinningRecord_RECORD_TYPE_AWARD_UPGRADE      GameWinningRecord_RecordType = 3
	GameWinningRecord_RECORD_TYPE_AWARD_CROSS_LEVEL  GameWinningRecord_RecordType = 4
)

var GameWinningRecord_RecordType_name = map[int32]string{
	0: "RECORD_TYPE_UNSPECIFIED",
	1: "RECORD_TYPE_AWARD_POSITIVE_VAL",
	2: "RECORD_TYPE_AWARD_GIFT",
	3: "RECORD_TYPE_AWARD_UPGRADE",
	4: "RECORD_TYPE_AWARD_CROSS_LEVEL",
}
var GameWinningRecord_RecordType_value = map[string]int32{
	"RECORD_TYPE_UNSPECIFIED":        0,
	"RECORD_TYPE_AWARD_POSITIVE_VAL": 1,
	"RECORD_TYPE_AWARD_GIFT":         2,
	"RECORD_TYPE_AWARD_UPGRADE":      3,
	"RECORD_TYPE_AWARD_CROSS_LEVEL":  4,
}

func (x GameWinningRecord_RecordType) String() string {
	return proto.EnumName(GameWinningRecord_RecordType_name, int32(x))
}
func (GameWinningRecord_RecordType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{26, 0}
}

type GetWinningRecordsRequest_QueryType int32

const (
	GetWinningRecordsRequest_QUERY_TYPE_UNSPECIFIED GetWinningRecordsRequest_QueryType = 0
	GetWinningRecordsRequest_QUERY_TYPE_ALL         GetWinningRecordsRequest_QueryType = 1
	GetWinningRecordsRequest_QUERY_TYPE_BONUS       GetWinningRecordsRequest_QueryType = 2
)

var GetWinningRecordsRequest_QueryType_name = map[int32]string{
	0: "QUERY_TYPE_UNSPECIFIED",
	1: "QUERY_TYPE_ALL",
	2: "QUERY_TYPE_BONUS",
}
var GetWinningRecordsRequest_QueryType_value = map[string]int32{
	"QUERY_TYPE_UNSPECIFIED": 0,
	"QUERY_TYPE_ALL":         1,
	"QUERY_TYPE_BONUS":       2,
}

func (x GetWinningRecordsRequest_QueryType) String() string {
	return proto.EnumName(GetWinningRecordsRequest_QueryType_name, int32(x))
}
func (GetWinningRecordsRequest_QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{27, 0}
}

type GetUserCatPropListRequest_PropType int32

const (
	GetUserCatPropListRequest_PROP_TYPE_UNSPECIFIED GetUserCatPropListRequest_PropType = 0
	GetUserCatPropListRequest_PROP_TYPE_FISH        GetUserCatPropListRequest_PropType = 1
	GetUserCatPropListRequest_PROP_TYPE_VOUCHER     GetUserCatPropListRequest_PropType = 2
)

var GetUserCatPropListRequest_PropType_name = map[int32]string{
	0: "PROP_TYPE_UNSPECIFIED",
	1: "PROP_TYPE_FISH",
	2: "PROP_TYPE_VOUCHER",
}
var GetUserCatPropListRequest_PropType_value = map[string]int32{
	"PROP_TYPE_UNSPECIFIED": 0,
	"PROP_TYPE_FISH":        1,
	"PROP_TYPE_VOUCHER":     2,
}

func (x GetUserCatPropListRequest_PropType) String() string {
	return proto.EnumName(GetUserCatPropListRequest_PropType_name, int32(x))
}
func (GetUserCatPropListRequest_PropType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{33, 0}
}

type NotifyItem struct {
	IsNeed               bool            `protobuf:"varint,1,opt,name=is_need,json=isNeed,proto3" json:"is_need,omitempty"`
	Text                 string          `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Color                string          `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
	Duration             uint32          `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	Place                NotifyPlaceType `protobuf:"varint,5,opt,name=place,proto3,enum=ga.cat_canteen_logic.NotifyPlaceType" json:"place,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *NotifyItem) Reset()         { *m = NotifyItem{} }
func (m *NotifyItem) String() string { return proto.CompactTextString(m) }
func (*NotifyItem) ProtoMessage()    {}
func (*NotifyItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{0}
}
func (m *NotifyItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyItem.Unmarshal(m, b)
}
func (m *NotifyItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyItem.Marshal(b, m, deterministic)
}
func (dst *NotifyItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyItem.Merge(dst, src)
}
func (m *NotifyItem) XXX_Size() int {
	return xxx_messageInfo_NotifyItem.Size(m)
}
func (m *NotifyItem) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyItem.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyItem proto.InternalMessageInfo

func (m *NotifyItem) GetIsNeed() bool {
	if m != nil {
		return m.IsNeed
	}
	return false
}

func (m *NotifyItem) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *NotifyItem) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

func (m *NotifyItem) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *NotifyItem) GetPlace() NotifyPlaceType {
	if m != nil {
		return m.Place
	}
	return NotifyPlaceType_NOTIFY_PLACE_TYPE_UNSPECIFIED
}

type GameNotifyInfo struct {
	NotifyType           GameNotifyInfo_NotifyType `protobuf:"varint,1,opt,name=notify_type,json=notifyType,proto3,enum=ga.cat_canteen_logic.GameNotifyInfo_NotifyType" json:"notify_type,omitempty"`
	BeginTime            uint32                    `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32                    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	NotifyList           []*NotifyItem             `protobuf:"bytes,4,rep,name=notify_list,json=notifyList,proto3" json:"notify_list,omitempty"`
	HasRed               bool                      `protobuf:"varint,5,opt,name=has_red,json=hasRed,proto3" json:"has_red,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GameNotifyInfo) Reset()         { *m = GameNotifyInfo{} }
func (m *GameNotifyInfo) String() string { return proto.CompactTextString(m) }
func (*GameNotifyInfo) ProtoMessage()    {}
func (*GameNotifyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{1}
}
func (m *GameNotifyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameNotifyInfo.Unmarshal(m, b)
}
func (m *GameNotifyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameNotifyInfo.Marshal(b, m, deterministic)
}
func (dst *GameNotifyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameNotifyInfo.Merge(dst, src)
}
func (m *GameNotifyInfo) XXX_Size() int {
	return xxx_messageInfo_GameNotifyInfo.Size(m)
}
func (m *GameNotifyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameNotifyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameNotifyInfo proto.InternalMessageInfo

func (m *GameNotifyInfo) GetNotifyType() GameNotifyInfo_NotifyType {
	if m != nil {
		return m.NotifyType
	}
	return GameNotifyInfo_NOTIFY_TYPE_UNSPECIFIED
}

func (m *GameNotifyInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GameNotifyInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GameNotifyInfo) GetNotifyList() []*NotifyItem {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

func (m *GameNotifyInfo) GetHasRed() bool {
	if m != nil {
		return m.HasRed
	}
	return false
}

type GameAccessNotifyInfo struct {
	GameType             GameAccessNotifyInfo_ChanceGameType `protobuf:"varint,1,opt,name=game_type,json=gameType,proto3,enum=ga.cat_canteen_logic.GameAccessNotifyInfo_ChanceGameType" json:"game_type,omitempty"`
	HaveAccess           bool                                `protobuf:"varint,3,opt,name=have_access,json=haveAccess,proto3" json:"have_access,omitempty"`
	NotifyList           []*GameNotifyInfo                   `protobuf:"bytes,4,rep,name=notify_list,json=notifyList,proto3" json:"notify_list,omitempty"`
	NotifyVersion        uint64                              `protobuf:"varint,5,opt,name=notify_version,json=notifyVersion,proto3" json:"notify_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GameAccessNotifyInfo) Reset()         { *m = GameAccessNotifyInfo{} }
func (m *GameAccessNotifyInfo) String() string { return proto.CompactTextString(m) }
func (*GameAccessNotifyInfo) ProtoMessage()    {}
func (*GameAccessNotifyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{2}
}
func (m *GameAccessNotifyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameAccessNotifyInfo.Unmarshal(m, b)
}
func (m *GameAccessNotifyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameAccessNotifyInfo.Marshal(b, m, deterministic)
}
func (dst *GameAccessNotifyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameAccessNotifyInfo.Merge(dst, src)
}
func (m *GameAccessNotifyInfo) XXX_Size() int {
	return xxx_messageInfo_GameAccessNotifyInfo.Size(m)
}
func (m *GameAccessNotifyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameAccessNotifyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameAccessNotifyInfo proto.InternalMessageInfo

func (m *GameAccessNotifyInfo) GetGameType() GameAccessNotifyInfo_ChanceGameType {
	if m != nil {
		return m.GameType
	}
	return GameAccessNotifyInfo_CHANCE_GAME_TYPE_UNSPECIFIED
}

func (m *GameAccessNotifyInfo) GetHaveAccess() bool {
	if m != nil {
		return m.HaveAccess
	}
	return false
}

func (m *GameAccessNotifyInfo) GetNotifyList() []*GameNotifyInfo {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

func (m *GameAccessNotifyInfo) GetNotifyVersion() uint64 {
	if m != nil {
		return m.NotifyVersion
	}
	return 0
}

// 获取玩法权限、提醒信息
type GetChanceGameAccessNotifyInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GameType             []uint32     `protobuf:"varint,2,rep,packed,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChanceGameAccessNotifyInfoRequest) Reset()         { *m = GetChanceGameAccessNotifyInfoRequest{} }
func (m *GetChanceGameAccessNotifyInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetChanceGameAccessNotifyInfoRequest) ProtoMessage()    {}
func (*GetChanceGameAccessNotifyInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{3}
}
func (m *GetChanceGameAccessNotifyInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChanceGameAccessNotifyInfoRequest.Unmarshal(m, b)
}
func (m *GetChanceGameAccessNotifyInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChanceGameAccessNotifyInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetChanceGameAccessNotifyInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChanceGameAccessNotifyInfoRequest.Merge(dst, src)
}
func (m *GetChanceGameAccessNotifyInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetChanceGameAccessNotifyInfoRequest.Size(m)
}
func (m *GetChanceGameAccessNotifyInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChanceGameAccessNotifyInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChanceGameAccessNotifyInfoRequest proto.InternalMessageInfo

func (m *GetChanceGameAccessNotifyInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChanceGameAccessNotifyInfoRequest) GetGameType() []uint32 {
	if m != nil {
		return m.GameType
	}
	return nil
}

func (m *GetChanceGameAccessNotifyInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChanceGameAccessNotifyInfoResponse struct {
	BaseResp             *app.BaseResp           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	AccessList           []*GameAccessNotifyInfo `protobuf:"bytes,2,rep,name=access_list,json=accessList,proto3" json:"access_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetChanceGameAccessNotifyInfoResponse) Reset()         { *m = GetChanceGameAccessNotifyInfoResponse{} }
func (m *GetChanceGameAccessNotifyInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetChanceGameAccessNotifyInfoResponse) ProtoMessage()    {}
func (*GetChanceGameAccessNotifyInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{4}
}
func (m *GetChanceGameAccessNotifyInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChanceGameAccessNotifyInfoResponse.Unmarshal(m, b)
}
func (m *GetChanceGameAccessNotifyInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChanceGameAccessNotifyInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetChanceGameAccessNotifyInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChanceGameAccessNotifyInfoResponse.Merge(dst, src)
}
func (m *GetChanceGameAccessNotifyInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetChanceGameAccessNotifyInfoResponse.Size(m)
}
func (m *GetChanceGameAccessNotifyInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChanceGameAccessNotifyInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChanceGameAccessNotifyInfoResponse proto.InternalMessageInfo

func (m *GetChanceGameAccessNotifyInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChanceGameAccessNotifyInfoResponse) GetAccessList() []*GameAccessNotifyInfo {
	if m != nil {
		return m.AccessList
	}
	return nil
}

type AwardEffect struct {
	EffectId             uint32   `protobuf:"varint,1,opt,name=effect_id,json=effectId,proto3" json:"effect_id,omitempty"`
	EffectNotifyText     string   `protobuf:"bytes,2,opt,name=effect_notify_text,json=effectNotifyText,proto3" json:"effect_notify_text,omitempty"`
	EffectNotifyFormat   string   `protobuf:"bytes,3,opt,name=effect_notify_format,json=effectNotifyFormat,proto3" json:"effect_notify_format,omitempty"`
	EffectUrl            string   `protobuf:"bytes,4,opt,name=effect_url,json=effectUrl,proto3" json:"effect_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardEffect) Reset()         { *m = AwardEffect{} }
func (m *AwardEffect) String() string { return proto.CompactTextString(m) }
func (*AwardEffect) ProtoMessage()    {}
func (*AwardEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{5}
}
func (m *AwardEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardEffect.Unmarshal(m, b)
}
func (m *AwardEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardEffect.Marshal(b, m, deterministic)
}
func (dst *AwardEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardEffect.Merge(dst, src)
}
func (m *AwardEffect) XXX_Size() int {
	return xxx_messageInfo_AwardEffect.Size(m)
}
func (m *AwardEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardEffect.DiscardUnknown(m)
}

var xxx_messageInfo_AwardEffect proto.InternalMessageInfo

func (m *AwardEffect) GetEffectId() uint32 {
	if m != nil {
		return m.EffectId
	}
	return 0
}

func (m *AwardEffect) GetEffectNotifyText() string {
	if m != nil {
		return m.EffectNotifyText
	}
	return ""
}

func (m *AwardEffect) GetEffectNotifyFormat() string {
	if m != nil {
		return m.EffectNotifyFormat
	}
	return ""
}

func (m *AwardEffect) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

type LevelGift struct {
	PackId               uint32       `protobuf:"varint,1,opt,name=pack_id,json=packId,proto3" json:"pack_id,omitempty"`
	PackName             string       `protobuf:"bytes,2,opt,name=pack_name,json=packName,proto3" json:"pack_name,omitempty"`
	PackPic              string       `protobuf:"bytes,3,opt,name=pack_pic,json=packPic,proto3" json:"pack_pic,omitempty"`
	PackPrice            uint32       `protobuf:"varint,4,opt,name=pack_price,json=packPrice,proto3" json:"pack_price,omitempty"`
	Amount               uint32       `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	Effect               *AwardEffect `protobuf:"bytes,6,opt,name=effect,proto3" json:"effect,omitempty"`
	LightEffect          *LightEffect `protobuf:"bytes,7,opt,name=light_effect,json=lightEffect,proto3" json:"light_effect,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LevelGift) Reset()         { *m = LevelGift{} }
func (m *LevelGift) String() string { return proto.CompactTextString(m) }
func (*LevelGift) ProtoMessage()    {}
func (*LevelGift) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{6}
}
func (m *LevelGift) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelGift.Unmarshal(m, b)
}
func (m *LevelGift) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelGift.Marshal(b, m, deterministic)
}
func (dst *LevelGift) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelGift.Merge(dst, src)
}
func (m *LevelGift) XXX_Size() int {
	return xxx_messageInfo_LevelGift.Size(m)
}
func (m *LevelGift) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelGift.DiscardUnknown(m)
}

var xxx_messageInfo_LevelGift proto.InternalMessageInfo

func (m *LevelGift) GetPackId() uint32 {
	if m != nil {
		return m.PackId
	}
	return 0
}

func (m *LevelGift) GetPackName() string {
	if m != nil {
		return m.PackName
	}
	return ""
}

func (m *LevelGift) GetPackPic() string {
	if m != nil {
		return m.PackPic
	}
	return ""
}

func (m *LevelGift) GetPackPrice() uint32 {
	if m != nil {
		return m.PackPrice
	}
	return 0
}

func (m *LevelGift) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *LevelGift) GetEffect() *AwardEffect {
	if m != nil {
		return m.Effect
	}
	return nil
}

func (m *LevelGift) GetLightEffect() *LightEffect {
	if m != nil {
		return m.LightEffect
	}
	return nil
}

type LevelMount struct {
	MountId              string   `protobuf:"bytes,1,opt,name=mount_id,json=mountId,proto3" json:"mount_id,omitempty"`
	MountName            string   `protobuf:"bytes,2,opt,name=mount_name,json=mountName,proto3" json:"mount_name,omitempty"`
	MountPic             string   `protobuf:"bytes,3,opt,name=mount_pic,json=mountPic,proto3" json:"mount_pic,omitempty"`
	AwardDays            uint32   `protobuf:"varint,4,opt,name=award_days,json=awardDays,proto3" json:"award_days,omitempty"`
	Amount               uint32   `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelMount) Reset()         { *m = LevelMount{} }
func (m *LevelMount) String() string { return proto.CompactTextString(m) }
func (*LevelMount) ProtoMessage()    {}
func (*LevelMount) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{7}
}
func (m *LevelMount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelMount.Unmarshal(m, b)
}
func (m *LevelMount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelMount.Marshal(b, m, deterministic)
}
func (dst *LevelMount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelMount.Merge(dst, src)
}
func (m *LevelMount) XXX_Size() int {
	return xxx_messageInfo_LevelMount.Size(m)
}
func (m *LevelMount) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelMount.DiscardUnknown(m)
}

var xxx_messageInfo_LevelMount proto.InternalMessageInfo

func (m *LevelMount) GetMountId() string {
	if m != nil {
		return m.MountId
	}
	return ""
}

func (m *LevelMount) GetMountName() string {
	if m != nil {
		return m.MountName
	}
	return ""
}

func (m *LevelMount) GetMountPic() string {
	if m != nil {
		return m.MountPic
	}
	return ""
}

func (m *LevelMount) GetAwardDays() uint32 {
	if m != nil {
		return m.AwardDays
	}
	return 0
}

func (m *LevelMount) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

// 关卡升级的奖励
type UpgradeAward struct {
	Present              *LevelGift  `protobuf:"bytes,1,opt,name=present,proto3" json:"present,omitempty"`
	Mount                *LevelMount `protobuf:"bytes,2,opt,name=mount,proto3" json:"mount,omitempty"`
	PropInfo             *PropInfo   `protobuf:"bytes,3,opt,name=prop_info,json=propInfo,proto3" json:"prop_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpgradeAward) Reset()         { *m = UpgradeAward{} }
func (m *UpgradeAward) String() string { return proto.CompactTextString(m) }
func (*UpgradeAward) ProtoMessage()    {}
func (*UpgradeAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{8}
}
func (m *UpgradeAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpgradeAward.Unmarshal(m, b)
}
func (m *UpgradeAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpgradeAward.Marshal(b, m, deterministic)
}
func (dst *UpgradeAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpgradeAward.Merge(dst, src)
}
func (m *UpgradeAward) XXX_Size() int {
	return xxx_messageInfo_UpgradeAward.Size(m)
}
func (m *UpgradeAward) XXX_DiscardUnknown() {
	xxx_messageInfo_UpgradeAward.DiscardUnknown(m)
}

var xxx_messageInfo_UpgradeAward proto.InternalMessageInfo

func (m *UpgradeAward) GetPresent() *LevelGift {
	if m != nil {
		return m.Present
	}
	return nil
}

func (m *UpgradeAward) GetMount() *LevelMount {
	if m != nil {
		return m.Mount
	}
	return nil
}

func (m *UpgradeAward) GetPropInfo() *PropInfo {
	if m != nil {
		return m.PropInfo
	}
	return nil
}

type ResourcesCfg struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResourcesCfg) Reset()         { *m = ResourcesCfg{} }
func (m *ResourcesCfg) String() string { return proto.CompactTextString(m) }
func (*ResourcesCfg) ProtoMessage()    {}
func (*ResourcesCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{9}
}
func (m *ResourcesCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResourcesCfg.Unmarshal(m, b)
}
func (m *ResourcesCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResourcesCfg.Marshal(b, m, deterministic)
}
func (dst *ResourcesCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResourcesCfg.Merge(dst, src)
}
func (m *ResourcesCfg) XXX_Size() int {
	return xxx_messageInfo_ResourcesCfg.Size(m)
}
func (m *ResourcesCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_ResourcesCfg.DiscardUnknown(m)
}

var xxx_messageInfo_ResourcesCfg proto.InternalMessageInfo

func (m *ResourcesCfg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ResourcesCfg) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

// 关卡配置
type LevelCfg struct {
	LevelId                uint32        `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	LevelName              string        `protobuf:"bytes,2,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	ResourceCfg            *ResourcesCfg `protobuf:"bytes,3,opt,name=resource_cfg,json=resourceCfg,proto3" json:"resource_cfg,omitempty"`
	EntryGift              *LevelGift    `protobuf:"bytes,4,opt,name=entry_gift,json=entryGift,proto3" json:"entry_gift,omitempty"`
	UpgradeAward           *UpgradeAward `protobuf:"bytes,5,opt,name=upgrade_award,json=upgradeAward,proto3" json:"upgrade_award,omitempty"`
	CostChancePer          uint32        `protobuf:"varint,6,opt,name=cost_chance_per,json=costChancePer,proto3" json:"cost_chance_per,omitempty"`
	BuyChanceAmountOption  []uint32      `protobuf:"varint,7,rep,packed,name=buy_chance_amount_option,json=buyChanceAmountOption,proto3" json:"buy_chance_amount_option,omitempty"`
	CostChanceAmountOption []uint32      `protobuf:"varint,8,rep,packed,name=cost_chance_amount_option,json=costChanceAmountOption,proto3" json:"cost_chance_amount_option,omitempty"`
	NotifyContinueFailCnt  uint32        `protobuf:"varint,9,opt,name=notify_continue_fail_cnt,json=notifyContinueFailCnt,proto3" json:"notify_continue_fail_cnt,omitempty"`
	// bool rate_of_progress_enable = 10;             // 关卡好评值（保底值）进度开关，为false时不展示进度条
	UnlockNotify         string    `protobuf:"bytes,10,opt,name=unlock_notify,json=unlockNotify,proto3" json:"unlock_notify,omitempty"`
	MaxN                 uint32    `protobuf:"varint,11,opt,name=max_n,json=maxN,proto3" json:"max_n,omitempty"`
	EntryPropInfo        *PropInfo `protobuf:"bytes,12,opt,name=entry_prop_info,json=entryPropInfo,proto3" json:"entry_prop_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *LevelCfg) Reset()         { *m = LevelCfg{} }
func (m *LevelCfg) String() string { return proto.CompactTextString(m) }
func (*LevelCfg) ProtoMessage()    {}
func (*LevelCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{10}
}
func (m *LevelCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelCfg.Unmarshal(m, b)
}
func (m *LevelCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelCfg.Marshal(b, m, deterministic)
}
func (dst *LevelCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelCfg.Merge(dst, src)
}
func (m *LevelCfg) XXX_Size() int {
	return xxx_messageInfo_LevelCfg.Size(m)
}
func (m *LevelCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelCfg.DiscardUnknown(m)
}

var xxx_messageInfo_LevelCfg proto.InternalMessageInfo

func (m *LevelCfg) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *LevelCfg) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelCfg) GetResourceCfg() *ResourcesCfg {
	if m != nil {
		return m.ResourceCfg
	}
	return nil
}

func (m *LevelCfg) GetEntryGift() *LevelGift {
	if m != nil {
		return m.EntryGift
	}
	return nil
}

func (m *LevelCfg) GetUpgradeAward() *UpgradeAward {
	if m != nil {
		return m.UpgradeAward
	}
	return nil
}

func (m *LevelCfg) GetCostChancePer() uint32 {
	if m != nil {
		return m.CostChancePer
	}
	return 0
}

func (m *LevelCfg) GetBuyChanceAmountOption() []uint32 {
	if m != nil {
		return m.BuyChanceAmountOption
	}
	return nil
}

func (m *LevelCfg) GetCostChanceAmountOption() []uint32 {
	if m != nil {
		return m.CostChanceAmountOption
	}
	return nil
}

func (m *LevelCfg) GetNotifyContinueFailCnt() uint32 {
	if m != nil {
		return m.NotifyContinueFailCnt
	}
	return 0
}

func (m *LevelCfg) GetUnlockNotify() string {
	if m != nil {
		return m.UnlockNotify
	}
	return ""
}

func (m *LevelCfg) GetMaxN() uint32 {
	if m != nil {
		return m.MaxN
	}
	return 0
}

func (m *LevelCfg) GetEntryPropInfo() *PropInfo {
	if m != nil {
		return m.EntryPropInfo
	}
	return nil
}

type UserLevelStatus struct {
	Status               UserLevelStatus_Status `protobuf:"varint,1,opt,name=status,proto3,enum=ga.cat_canteen_logic.UserLevelStatus_Status" json:"status,omitempty"`
	Days                 uint32                 `protobuf:"varint,2,opt,name=days,proto3" json:"days,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *UserLevelStatus) Reset()         { *m = UserLevelStatus{} }
func (m *UserLevelStatus) String() string { return proto.CompactTextString(m) }
func (*UserLevelStatus) ProtoMessage()    {}
func (*UserLevelStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{11}
}
func (m *UserLevelStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevelStatus.Unmarshal(m, b)
}
func (m *UserLevelStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevelStatus.Marshal(b, m, deterministic)
}
func (dst *UserLevelStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevelStatus.Merge(dst, src)
}
func (m *UserLevelStatus) XXX_Size() int {
	return xxx_messageInfo_UserLevelStatus.Size(m)
}
func (m *UserLevelStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevelStatus.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevelStatus proto.InternalMessageInfo

func (m *UserLevelStatus) GetStatus() UserLevelStatus_Status {
	if m != nil {
		return m.Status
	}
	return UserLevelStatus_STATUS_UNSPECIFIED
}

func (m *UserLevelStatus) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

type PropInfo struct {
	PropId               uint32   `protobuf:"varint,1,opt,name=prop_id,json=propId,proto3" json:"prop_id,omitempty"`
	PropIcon             string   `protobuf:"bytes,2,opt,name=prop_icon,json=propIcon,proto3" json:"prop_icon,omitempty"`
	PropName             string   `protobuf:"bytes,3,opt,name=prop_name,json=propName,proto3" json:"prop_name,omitempty"`
	Amount               uint32   `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`
	DurationDay          uint32   `protobuf:"varint,5,opt,name=duration_day,json=durationDay,proto3" json:"duration_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PropInfo) Reset()         { *m = PropInfo{} }
func (m *PropInfo) String() string { return proto.CompactTextString(m) }
func (*PropInfo) ProtoMessage()    {}
func (*PropInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{12}
}
func (m *PropInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PropInfo.Unmarshal(m, b)
}
func (m *PropInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PropInfo.Marshal(b, m, deterministic)
}
func (dst *PropInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PropInfo.Merge(dst, src)
}
func (m *PropInfo) XXX_Size() int {
	return xxx_messageInfo_PropInfo.Size(m)
}
func (m *PropInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PropInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PropInfo proto.InternalMessageInfo

func (m *PropInfo) GetPropId() uint32 {
	if m != nil {
		return m.PropId
	}
	return 0
}

func (m *PropInfo) GetPropIcon() string {
	if m != nil {
		return m.PropIcon
	}
	return ""
}

func (m *PropInfo) GetPropName() string {
	if m != nil {
		return m.PropName
	}
	return ""
}

func (m *PropInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *PropInfo) GetDurationDay() uint32 {
	if m != nil {
		return m.DurationDay
	}
	return 0
}

// 用户的关卡存档
type UserPlayFile struct {
	LevelId              uint32           `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	EntryGiftCnt         uint32           `protobuf:"varint,2,opt,name=entry_gift_cnt,json=entryGiftCnt,proto3" json:"entry_gift_cnt,omitempty"`
	RateOfProgress       float32          `protobuf:"fixed32,3,opt,name=rate_of_progress,json=rateOfProgress,proto3" json:"rate_of_progress,omitempty"`
	CanLv                uint32           `protobuf:"varint,4,opt,name=can_lv,json=canLv,proto3" json:"can_lv,omitempty"`
	UserLevelStatus      *UserLevelStatus `protobuf:"bytes,5,opt,name=user_level_status,json=userLevelStatus,proto3" json:"user_level_status,omitempty"`
	PropNum              uint32           `protobuf:"varint,6,opt,name=prop_num,json=propNum,proto3" json:"prop_num,omitempty"`
	UserN                uint32           `protobuf:"varint,7,opt,name=user_n,json=userN,proto3" json:"user_n,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserPlayFile) Reset()         { *m = UserPlayFile{} }
func (m *UserPlayFile) String() string { return proto.CompactTextString(m) }
func (*UserPlayFile) ProtoMessage()    {}
func (*UserPlayFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{13}
}
func (m *UserPlayFile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPlayFile.Unmarshal(m, b)
}
func (m *UserPlayFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPlayFile.Marshal(b, m, deterministic)
}
func (dst *UserPlayFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPlayFile.Merge(dst, src)
}
func (m *UserPlayFile) XXX_Size() int {
	return xxx_messageInfo_UserPlayFile.Size(m)
}
func (m *UserPlayFile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPlayFile.DiscardUnknown(m)
}

var xxx_messageInfo_UserPlayFile proto.InternalMessageInfo

func (m *UserPlayFile) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *UserPlayFile) GetEntryGiftCnt() uint32 {
	if m != nil {
		return m.EntryGiftCnt
	}
	return 0
}

func (m *UserPlayFile) GetRateOfProgress() float32 {
	if m != nil {
		return m.RateOfProgress
	}
	return 0
}

func (m *UserPlayFile) GetCanLv() uint32 {
	if m != nil {
		return m.CanLv
	}
	return 0
}

func (m *UserPlayFile) GetUserLevelStatus() *UserLevelStatus {
	if m != nil {
		return m.UserLevelStatus
	}
	return nil
}

func (m *UserPlayFile) GetPropNum() uint32 {
	if m != nil {
		return m.PropNum
	}
	return 0
}

func (m *UserPlayFile) GetUserN() uint32 {
	if m != nil {
		return m.UserN
	}
	return 0
}

type LevelInfo struct {
	LevelCfg             *LevelCfg     `protobuf:"bytes,1,opt,name=level_cfg,json=levelCfg,proto3" json:"level_cfg,omitempty"`
	UserPlayInfo         *UserPlayFile `protobuf:"bytes,2,opt,name=user_play_info,json=userPlayInfo,proto3" json:"user_play_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *LevelInfo) Reset()         { *m = LevelInfo{} }
func (m *LevelInfo) String() string { return proto.CompactTextString(m) }
func (*LevelInfo) ProtoMessage()    {}
func (*LevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{14}
}
func (m *LevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelInfo.Unmarshal(m, b)
}
func (m *LevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelInfo.Marshal(b, m, deterministic)
}
func (dst *LevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelInfo.Merge(dst, src)
}
func (m *LevelInfo) XXX_Size() int {
	return xxx_messageInfo_LevelInfo.Size(m)
}
func (m *LevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LevelInfo proto.InternalMessageInfo

func (m *LevelInfo) GetLevelCfg() *LevelCfg {
	if m != nil {
		return m.LevelCfg
	}
	return nil
}

func (m *LevelInfo) GetUserPlayInfo() *UserPlayFile {
	if m != nil {
		return m.UserPlayInfo
	}
	return nil
}

// 获取游戏信息
type GetGameInfoRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameInfoRequest) Reset()         { *m = GetGameInfoRequest{} }
func (m *GetGameInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoRequest) ProtoMessage()    {}
func (*GetGameInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{15}
}
func (m *GetGameInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoRequest.Unmarshal(m, b)
}
func (m *GetGameInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoRequest.Merge(dst, src)
}
func (m *GetGameInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoRequest.Size(m)
}
func (m *GetGameInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoRequest proto.InternalMessageInfo

func (m *GetGameInfoRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetGameInfoResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserChanceCnt        uint32        `protobuf:"varint,2,opt,name=user_chance_cnt,json=userChanceCnt,proto3" json:"user_chance_cnt,omitempty"`
	LevelInfoList        []*LevelInfo  `protobuf:"bytes,3,rep,name=level_info_list,json=levelInfoList,proto3" json:"level_info_list,omitempty"`
	BuyChanceAward       *LevelGift    `protobuf:"bytes,4,opt,name=buy_chance_award,json=buyChanceAward,proto3" json:"buy_chance_award,omitempty"`
	CanResourcesCfg      *ResourcesCfg `protobuf:"bytes,5,opt,name=can_resources_cfg,json=canResourcesCfg,proto3" json:"can_resources_cfg,omitempty"`
	BuyChanceExpireDesc  string        `protobuf:"bytes,6,opt,name=buy_chance_expire_desc,json=buyChanceExpireDesc,proto3" json:"buy_chance_expire_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetGameInfoResponse) Reset()         { *m = GetGameInfoResponse{} }
func (m *GetGameInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetGameInfoResponse) ProtoMessage()    {}
func (*GetGameInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{16}
}
func (m *GetGameInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameInfoResponse.Unmarshal(m, b)
}
func (m *GetGameInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetGameInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameInfoResponse.Merge(dst, src)
}
func (m *GetGameInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetGameInfoResponse.Size(m)
}
func (m *GetGameInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameInfoResponse proto.InternalMessageInfo

func (m *GetGameInfoResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameInfoResponse) GetUserChanceCnt() uint32 {
	if m != nil {
		return m.UserChanceCnt
	}
	return 0
}

func (m *GetGameInfoResponse) GetLevelInfoList() []*LevelInfo {
	if m != nil {
		return m.LevelInfoList
	}
	return nil
}

func (m *GetGameInfoResponse) GetBuyChanceAward() *LevelGift {
	if m != nil {
		return m.BuyChanceAward
	}
	return nil
}

func (m *GetGameInfoResponse) GetCanResourcesCfg() *ResourcesCfg {
	if m != nil {
		return m.CanResourcesCfg
	}
	return nil
}

func (m *GetGameInfoResponse) GetBuyChanceExpireDesc() string {
	if m != nil {
		return m.BuyChanceExpireDesc
	}
	return ""
}

// 购买抽奖机会
type BuyChanceRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChanceAmount         uint32       `protobuf:"varint,3,opt,name=chance_amount,json=chanceAmount,proto3" json:"chance_amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BuyChanceRequest) Reset()         { *m = BuyChanceRequest{} }
func (m *BuyChanceRequest) String() string { return proto.CompactTextString(m) }
func (*BuyChanceRequest) ProtoMessage()    {}
func (*BuyChanceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{17}
}
func (m *BuyChanceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyChanceRequest.Unmarshal(m, b)
}
func (m *BuyChanceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyChanceRequest.Marshal(b, m, deterministic)
}
func (dst *BuyChanceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyChanceRequest.Merge(dst, src)
}
func (m *BuyChanceRequest) XXX_Size() int {
	return xxx_messageInfo_BuyChanceRequest.Size(m)
}
func (m *BuyChanceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyChanceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BuyChanceRequest proto.InternalMessageInfo

func (m *BuyChanceRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *BuyChanceRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BuyChanceRequest) GetChanceAmount() uint32 {
	if m != nil {
		return m.ChanceAmount
	}
	return 0
}

type BuyChanceResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FinalChanceAmount    uint32        `protobuf:"varint,2,opt,name=final_chance_amount,json=finalChanceAmount,proto3" json:"final_chance_amount,omitempty"`
	Balance              uint64        `protobuf:"varint,3,opt,name=balance,proto3" json:"balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BuyChanceResponse) Reset()         { *m = BuyChanceResponse{} }
func (m *BuyChanceResponse) String() string { return proto.CompactTextString(m) }
func (*BuyChanceResponse) ProtoMessage()    {}
func (*BuyChanceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{18}
}
func (m *BuyChanceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyChanceResponse.Unmarshal(m, b)
}
func (m *BuyChanceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyChanceResponse.Marshal(b, m, deterministic)
}
func (dst *BuyChanceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyChanceResponse.Merge(dst, src)
}
func (m *BuyChanceResponse) XXX_Size() int {
	return xxx_messageInfo_BuyChanceResponse.Size(m)
}
func (m *BuyChanceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyChanceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BuyChanceResponse proto.InternalMessageInfo

func (m *BuyChanceResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *BuyChanceResponse) GetFinalChanceAmount() uint32 {
	if m != nil {
		return m.FinalChanceAmount
	}
	return 0
}

func (m *BuyChanceResponse) GetBalance() uint64 {
	if m != nil {
		return m.Balance
	}
	return 0
}

type LightEffect struct {
	EffectUrl            string   `protobuf:"bytes,1,opt,name=effect_url,json=effectUrl,proto3" json:"effect_url,omitempty"`
	EffectMd5            string   `protobuf:"bytes,2,opt,name=effect_md5,json=effectMd5,proto3" json:"effect_md5,omitempty"`
	EffectJson           string   `protobuf:"bytes,3,opt,name=effect_json,json=effectJson,proto3" json:"effect_json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LightEffect) Reset()         { *m = LightEffect{} }
func (m *LightEffect) String() string { return proto.CompactTextString(m) }
func (*LightEffect) ProtoMessage()    {}
func (*LightEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{19}
}
func (m *LightEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LightEffect.Unmarshal(m, b)
}
func (m *LightEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LightEffect.Marshal(b, m, deterministic)
}
func (dst *LightEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LightEffect.Merge(dst, src)
}
func (m *LightEffect) XXX_Size() int {
	return xxx_messageInfo_LightEffect.Size(m)
}
func (m *LightEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_LightEffect.DiscardUnknown(m)
}

var xxx_messageInfo_LightEffect proto.InternalMessageInfo

func (m *LightEffect) GetEffectUrl() string {
	if m != nil {
		return m.EffectUrl
	}
	return ""
}

func (m *LightEffect) GetEffectMd5() string {
	if m != nil {
		return m.EffectMd5
	}
	return ""
}

func (m *LightEffect) GetEffectJson() string {
	if m != nil {
		return m.EffectJson
	}
	return ""
}

// 奖励
type LotteryDrawAward struct {
	ResultType           ResultType  `protobuf:"varint,1,opt,name=result_type,json=resultType,proto3,enum=ga.cat_canteen_logic.ResultType" json:"result_type,omitempty"`
	Present              *LevelGift  `protobuf:"bytes,2,opt,name=present,proto3" json:"present,omitempty"`
	Mount                *LevelMount `protobuf:"bytes,3,opt,name=mount,proto3" json:"mount,omitempty"`
	PropInfo             *PropInfo   `protobuf:"bytes,4,opt,name=prop_info,json=propInfo,proto3" json:"prop_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *LotteryDrawAward) Reset()         { *m = LotteryDrawAward{} }
func (m *LotteryDrawAward) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawAward) ProtoMessage()    {}
func (*LotteryDrawAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{20}
}
func (m *LotteryDrawAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawAward.Unmarshal(m, b)
}
func (m *LotteryDrawAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawAward.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawAward.Merge(dst, src)
}
func (m *LotteryDrawAward) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawAward.Size(m)
}
func (m *LotteryDrawAward) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawAward.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawAward proto.InternalMessageInfo

func (m *LotteryDrawAward) GetResultType() ResultType {
	if m != nil {
		return m.ResultType
	}
	return ResultType_RESULT_TYPE_UNSPECIFIED
}

func (m *LotteryDrawAward) GetPresent() *LevelGift {
	if m != nil {
		return m.Present
	}
	return nil
}

func (m *LotteryDrawAward) GetMount() *LevelMount {
	if m != nil {
		return m.Mount
	}
	return nil
}

func (m *LotteryDrawAward) GetPropInfo() *PropInfo {
	if m != nil {
		return m.PropInfo
	}
	return nil
}

type LotteryDrawResult struct {
	LevelId              uint32              `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	ResultType           ResultType          `protobuf:"varint,2,opt,name=result_type,json=resultType,proto3,enum=ga.cat_canteen_logic.ResultType" json:"result_type,omitempty"`
	AwardList            []*LotteryDrawAward `protobuf:"bytes,3,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	NextLevelId          uint32              `protobuf:"varint,4,opt,name=next_level_id,json=nextLevelId,proto3" json:"next_level_id,omitempty"`
	IncrProgressVal      uint32              `protobuf:"varint,5,opt,name=incr_progress_val,json=incrProgressVal,proto3" json:"incr_progress_val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *LotteryDrawResult) Reset()         { *m = LotteryDrawResult{} }
func (m *LotteryDrawResult) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawResult) ProtoMessage()    {}
func (*LotteryDrawResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{21}
}
func (m *LotteryDrawResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawResult.Unmarshal(m, b)
}
func (m *LotteryDrawResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawResult.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawResult.Merge(dst, src)
}
func (m *LotteryDrawResult) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawResult.Size(m)
}
func (m *LotteryDrawResult) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawResult.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawResult proto.InternalMessageInfo

func (m *LotteryDrawResult) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *LotteryDrawResult) GetResultType() ResultType {
	if m != nil {
		return m.ResultType
	}
	return ResultType_RESULT_TYPE_UNSPECIFIED
}

func (m *LotteryDrawResult) GetAwardList() []*LotteryDrawAward {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *LotteryDrawResult) GetNextLevelId() uint32 {
	if m != nil {
		return m.NextLevelId
	}
	return 0
}

func (m *LotteryDrawResult) GetIncrProgressVal() uint32 {
	if m != nil {
		return m.IncrProgressVal
	}
	return 0
}

// 抽奖（经营）
type LotteryDrawRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Amount               uint32       `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	LevelId              uint32       `protobuf:"varint,4,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *LotteryDrawRequest) Reset()         { *m = LotteryDrawRequest{} }
func (m *LotteryDrawRequest) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawRequest) ProtoMessage()    {}
func (*LotteryDrawRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{22}
}
func (m *LotteryDrawRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawRequest.Unmarshal(m, b)
}
func (m *LotteryDrawRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawRequest.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawRequest.Merge(dst, src)
}
func (m *LotteryDrawRequest) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawRequest.Size(m)
}
func (m *LotteryDrawRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawRequest proto.InternalMessageInfo

func (m *LotteryDrawRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *LotteryDrawRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LotteryDrawRequest) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *LotteryDrawRequest) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

type LotteryDrawResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	RemainChance         uint32             `protobuf:"varint,2,opt,name=remain_chance,json=remainChance,proto3" json:"remain_chance,omitempty"`
	PlayFileList         []*UserPlayFile    `protobuf:"bytes,3,rep,name=play_file_list,json=playFileList,proto3" json:"play_file_list,omitempty"`
	Result               *LotteryDrawResult `protobuf:"bytes,4,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *LotteryDrawResponse) Reset()         { *m = LotteryDrawResponse{} }
func (m *LotteryDrawResponse) String() string { return proto.CompactTextString(m) }
func (*LotteryDrawResponse) ProtoMessage()    {}
func (*LotteryDrawResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{23}
}
func (m *LotteryDrawResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LotteryDrawResponse.Unmarshal(m, b)
}
func (m *LotteryDrawResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LotteryDrawResponse.Marshal(b, m, deterministic)
}
func (dst *LotteryDrawResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LotteryDrawResponse.Merge(dst, src)
}
func (m *LotteryDrawResponse) XXX_Size() int {
	return xxx_messageInfo_LotteryDrawResponse.Size(m)
}
func (m *LotteryDrawResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LotteryDrawResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LotteryDrawResponse proto.InternalMessageInfo

func (m *LotteryDrawResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *LotteryDrawResponse) GetRemainChance() uint32 {
	if m != nil {
		return m.RemainChance
	}
	return 0
}

func (m *LotteryDrawResponse) GetPlayFileList() []*UserPlayFile {
	if m != nil {
		return m.PlayFileList
	}
	return nil
}

func (m *LotteryDrawResponse) GetResult() *LotteryDrawResult {
	if m != nil {
		return m.Result
	}
	return nil
}

type GetUserPlayFileRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserPlayFileRequest) Reset()         { *m = GetUserPlayFileRequest{} }
func (m *GetUserPlayFileRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserPlayFileRequest) ProtoMessage()    {}
func (*GetUserPlayFileRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{24}
}
func (m *GetUserPlayFileRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPlayFileRequest.Unmarshal(m, b)
}
func (m *GetUserPlayFileRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPlayFileRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserPlayFileRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPlayFileRequest.Merge(dst, src)
}
func (m *GetUserPlayFileRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserPlayFileRequest.Size(m)
}
func (m *GetUserPlayFileRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPlayFileRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPlayFileRequest proto.InternalMessageInfo

func (m *GetUserPlayFileRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserPlayFileRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserPlayFileResponse struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PlayFileList         []*UserPlayFile `protobuf:"bytes,2,rep,name=play_file_list,json=playFileList,proto3" json:"play_file_list,omitempty"`
	UserChanceCnt        uint32          `protobuf:"varint,3,opt,name=user_chance_cnt,json=userChanceCnt,proto3" json:"user_chance_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserPlayFileResponse) Reset()         { *m = GetUserPlayFileResponse{} }
func (m *GetUserPlayFileResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserPlayFileResponse) ProtoMessage()    {}
func (*GetUserPlayFileResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{25}
}
func (m *GetUserPlayFileResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserPlayFileResponse.Unmarshal(m, b)
}
func (m *GetUserPlayFileResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserPlayFileResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserPlayFileResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserPlayFileResponse.Merge(dst, src)
}
func (m *GetUserPlayFileResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserPlayFileResponse.Size(m)
}
func (m *GetUserPlayFileResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserPlayFileResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserPlayFileResponse proto.InternalMessageInfo

func (m *GetUserPlayFileResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserPlayFileResponse) GetPlayFileList() []*UserPlayFile {
	if m != nil {
		return m.PlayFileList
	}
	return nil
}

func (m *GetUserPlayFileResponse) GetUserChanceCnt() uint32 {
	if m != nil {
		return m.UserChanceCnt
	}
	return 0
}

// 中奖记录
type GameWinningRecord struct {
	Id                   string                       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	OrderId              string                       `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	RecordType           GameWinningRecord_RecordType `protobuf:"varint,3,opt,name=record_type,json=recordType,proto3,enum=ga.cat_canteen_logic.GameWinningRecord_RecordType" json:"record_type,omitempty"`
	IncrProgressVal      uint32                       `protobuf:"varint,4,opt,name=incr_progress_val,json=incrProgressVal,proto3" json:"incr_progress_val,omitempty"`
	AwardInfo            *LevelGift                   `protobuf:"bytes,5,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	AwardMount           *LevelMount                  `protobuf:"bytes,6,opt,name=award_mount,json=awardMount,proto3" json:"award_mount,omitempty"`
	EntryGift            *LevelGift                   `protobuf:"bytes,7,opt,name=entry_gift,json=entryGift,proto3" json:"entry_gift,omitempty"`
	CreateTime           uint32                       `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	AwardProp            *PropInfo                    `protobuf:"bytes,9,opt,name=award_prop,json=awardProp,proto3" json:"award_prop,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GameWinningRecord) Reset()         { *m = GameWinningRecord{} }
func (m *GameWinningRecord) String() string { return proto.CompactTextString(m) }
func (*GameWinningRecord) ProtoMessage()    {}
func (*GameWinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{26}
}
func (m *GameWinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameWinningRecord.Unmarshal(m, b)
}
func (m *GameWinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameWinningRecord.Marshal(b, m, deterministic)
}
func (dst *GameWinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameWinningRecord.Merge(dst, src)
}
func (m *GameWinningRecord) XXX_Size() int {
	return xxx_messageInfo_GameWinningRecord.Size(m)
}
func (m *GameWinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GameWinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GameWinningRecord proto.InternalMessageInfo

func (m *GameWinningRecord) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GameWinningRecord) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *GameWinningRecord) GetRecordType() GameWinningRecord_RecordType {
	if m != nil {
		return m.RecordType
	}
	return GameWinningRecord_RECORD_TYPE_UNSPECIFIED
}

func (m *GameWinningRecord) GetIncrProgressVal() uint32 {
	if m != nil {
		return m.IncrProgressVal
	}
	return 0
}

func (m *GameWinningRecord) GetAwardInfo() *LevelGift {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *GameWinningRecord) GetAwardMount() *LevelMount {
	if m != nil {
		return m.AwardMount
	}
	return nil
}

func (m *GameWinningRecord) GetEntryGift() *LevelGift {
	if m != nil {
		return m.EntryGift
	}
	return nil
}

func (m *GameWinningRecord) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameWinningRecord) GetAwardProp() *PropInfo {
	if m != nil {
		return m.AwardProp
	}
	return nil
}

// 获取用户中奖纪录
type GetWinningRecordsRequest struct {
	BaseReq              *app.BaseReq                       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	LevelId              uint32                             `protobuf:"varint,2,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	OffsetId             string                             `protobuf:"bytes,3,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	Limit                uint32                             `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	QueryType            GetWinningRecordsRequest_QueryType `protobuf:"varint,5,opt,name=query_type,json=queryType,proto3,enum=ga.cat_canteen_logic.GetWinningRecordsRequest_QueryType" json:"query_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *GetWinningRecordsRequest) Reset()         { *m = GetWinningRecordsRequest{} }
func (m *GetWinningRecordsRequest) String() string { return proto.CompactTextString(m) }
func (*GetWinningRecordsRequest) ProtoMessage()    {}
func (*GetWinningRecordsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{27}
}
func (m *GetWinningRecordsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWinningRecordsRequest.Unmarshal(m, b)
}
func (m *GetWinningRecordsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWinningRecordsRequest.Marshal(b, m, deterministic)
}
func (dst *GetWinningRecordsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWinningRecordsRequest.Merge(dst, src)
}
func (m *GetWinningRecordsRequest) XXX_Size() int {
	return xxx_messageInfo_GetWinningRecordsRequest.Size(m)
}
func (m *GetWinningRecordsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWinningRecordsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetWinningRecordsRequest proto.InternalMessageInfo

func (m *GetWinningRecordsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetWinningRecordsRequest) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *GetWinningRecordsRequest) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

func (m *GetWinningRecordsRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetWinningRecordsRequest) GetQueryType() GetWinningRecordsRequest_QueryType {
	if m != nil {
		return m.QueryType
	}
	return GetWinningRecordsRequest_QUERY_TYPE_UNSPECIFIED
}

type GetWinningRecordsResponse struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Records              []*GameWinningRecord `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetWinningRecordsResponse) Reset()         { *m = GetWinningRecordsResponse{} }
func (m *GetWinningRecordsResponse) String() string { return proto.CompactTextString(m) }
func (*GetWinningRecordsResponse) ProtoMessage()    {}
func (*GetWinningRecordsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{28}
}
func (m *GetWinningRecordsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWinningRecordsResponse.Unmarshal(m, b)
}
func (m *GetWinningRecordsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWinningRecordsResponse.Marshal(b, m, deterministic)
}
func (dst *GetWinningRecordsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWinningRecordsResponse.Merge(dst, src)
}
func (m *GetWinningRecordsResponse) XXX_Size() int {
	return xxx_messageInfo_GetWinningRecordsResponse.Size(m)
}
func (m *GetWinningRecordsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWinningRecordsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetWinningRecordsResponse proto.InternalMessageInfo

func (m *GetWinningRecordsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetWinningRecordsResponse) GetRecords() []*GameWinningRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

// 平台中奖记录  轮播用
type SimpleGameWinningRecord struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string     `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Gift                 *LevelGift `protobuf:"bytes,3,opt,name=gift,proto3" json:"gift,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SimpleGameWinningRecord) Reset()         { *m = SimpleGameWinningRecord{} }
func (m *SimpleGameWinningRecord) String() string { return proto.CompactTextString(m) }
func (*SimpleGameWinningRecord) ProtoMessage()    {}
func (*SimpleGameWinningRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{29}
}
func (m *SimpleGameWinningRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SimpleGameWinningRecord.Unmarshal(m, b)
}
func (m *SimpleGameWinningRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SimpleGameWinningRecord.Marshal(b, m, deterministic)
}
func (dst *SimpleGameWinningRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SimpleGameWinningRecord.Merge(dst, src)
}
func (m *SimpleGameWinningRecord) XXX_Size() int {
	return xxx_messageInfo_SimpleGameWinningRecord.Size(m)
}
func (m *SimpleGameWinningRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_SimpleGameWinningRecord.DiscardUnknown(m)
}

var xxx_messageInfo_SimpleGameWinningRecord proto.InternalMessageInfo

func (m *SimpleGameWinningRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SimpleGameWinningRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *SimpleGameWinningRecord) GetGift() *LevelGift {
	if m != nil {
		return m.Gift
	}
	return nil
}

// 获取最新的中奖纪录
type GetRecentWinningRecordsRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Limit                uint32       `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRecentWinningRecordsRequest) Reset()         { *m = GetRecentWinningRecordsRequest{} }
func (m *GetRecentWinningRecordsRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecentWinningRecordsRequest) ProtoMessage()    {}
func (*GetRecentWinningRecordsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{30}
}
func (m *GetRecentWinningRecordsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentWinningRecordsRequest.Unmarshal(m, b)
}
func (m *GetRecentWinningRecordsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentWinningRecordsRequest.Marshal(b, m, deterministic)
}
func (dst *GetRecentWinningRecordsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentWinningRecordsRequest.Merge(dst, src)
}
func (m *GetRecentWinningRecordsRequest) XXX_Size() int {
	return xxx_messageInfo_GetRecentWinningRecordsRequest.Size(m)
}
func (m *GetRecentWinningRecordsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentWinningRecordsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentWinningRecordsRequest proto.InternalMessageInfo

func (m *GetRecentWinningRecordsRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRecentWinningRecordsRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRecentWinningRecordsResponse struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Records              []*SimpleGameWinningRecord `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetRecentWinningRecordsResponse) Reset()         { *m = GetRecentWinningRecordsResponse{} }
func (m *GetRecentWinningRecordsResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecentWinningRecordsResponse) ProtoMessage()    {}
func (*GetRecentWinningRecordsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{31}
}
func (m *GetRecentWinningRecordsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentWinningRecordsResponse.Unmarshal(m, b)
}
func (m *GetRecentWinningRecordsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentWinningRecordsResponse.Marshal(b, m, deterministic)
}
func (dst *GetRecentWinningRecordsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentWinningRecordsResponse.Merge(dst, src)
}
func (m *GetRecentWinningRecordsResponse) XXX_Size() int {
	return xxx_messageInfo_GetRecentWinningRecordsResponse.Size(m)
}
func (m *GetRecentWinningRecordsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentWinningRecordsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentWinningRecordsResponse proto.InternalMessageInfo

func (m *GetRecentWinningRecordsResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecentWinningRecordsResponse) GetRecords() []*SimpleGameWinningRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

// 道具时效
type CatPropTimeliness struct {
	Num                  uint32   `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
	FinTimestamp         int64    `protobuf:"varint,2,opt,name=fin_timestamp,json=finTimestamp,proto3" json:"fin_timestamp,omitempty"`
	LevelName            string   `protobuf:"bytes,3,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	PropId               uint32   `protobuf:"varint,4,opt,name=prop_id,json=propId,proto3" json:"prop_id,omitempty"`
	PropName             string   `protobuf:"bytes,5,opt,name=prop_name,json=propName,proto3" json:"prop_name,omitempty"`
	PropIcon             string   `protobuf:"bytes,6,opt,name=prop_icon,json=propIcon,proto3" json:"prop_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CatPropTimeliness) Reset()         { *m = CatPropTimeliness{} }
func (m *CatPropTimeliness) String() string { return proto.CompactTextString(m) }
func (*CatPropTimeliness) ProtoMessage()    {}
func (*CatPropTimeliness) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{32}
}
func (m *CatPropTimeliness) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatPropTimeliness.Unmarshal(m, b)
}
func (m *CatPropTimeliness) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatPropTimeliness.Marshal(b, m, deterministic)
}
func (dst *CatPropTimeliness) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatPropTimeliness.Merge(dst, src)
}
func (m *CatPropTimeliness) XXX_Size() int {
	return xxx_messageInfo_CatPropTimeliness.Size(m)
}
func (m *CatPropTimeliness) XXX_DiscardUnknown() {
	xxx_messageInfo_CatPropTimeliness.DiscardUnknown(m)
}

var xxx_messageInfo_CatPropTimeliness proto.InternalMessageInfo

func (m *CatPropTimeliness) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *CatPropTimeliness) GetFinTimestamp() int64 {
	if m != nil {
		return m.FinTimestamp
	}
	return 0
}

func (m *CatPropTimeliness) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *CatPropTimeliness) GetPropId() uint32 {
	if m != nil {
		return m.PropId
	}
	return 0
}

func (m *CatPropTimeliness) GetPropName() string {
	if m != nil {
		return m.PropName
	}
	return ""
}

func (m *CatPropTimeliness) GetPropIcon() string {
	if m != nil {
		return m.PropIcon
	}
	return ""
}

type GetUserCatPropListRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SortAsc              bool         `protobuf:"varint,2,opt,name=sort_asc,json=sortAsc,proto3" json:"sort_asc,omitempty"`
	PropType             uint32       `protobuf:"varint,3,opt,name=prop_type,json=propType,proto3" json:"prop_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserCatPropListRequest) Reset()         { *m = GetUserCatPropListRequest{} }
func (m *GetUserCatPropListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserCatPropListRequest) ProtoMessage()    {}
func (*GetUserCatPropListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{33}
}
func (m *GetUserCatPropListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCatPropListRequest.Unmarshal(m, b)
}
func (m *GetUserCatPropListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCatPropListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserCatPropListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCatPropListRequest.Merge(dst, src)
}
func (m *GetUserCatPropListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserCatPropListRequest.Size(m)
}
func (m *GetUserCatPropListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCatPropListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCatPropListRequest proto.InternalMessageInfo

func (m *GetUserCatPropListRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserCatPropListRequest) GetSortAsc() bool {
	if m != nil {
		return m.SortAsc
	}
	return false
}

func (m *GetUserCatPropListRequest) GetPropType() uint32 {
	if m != nil {
		return m.PropType
	}
	return 0
}

type GetUserCatPropListResponse struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	PropList             []*CatPropTimeliness `protobuf:"bytes,2,rep,name=prop_list,json=propList,proto3" json:"prop_list,omitempty"`
	Desc                 string               `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetUserCatPropListResponse) Reset()         { *m = GetUserCatPropListResponse{} }
func (m *GetUserCatPropListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserCatPropListResponse) ProtoMessage()    {}
func (*GetUserCatPropListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{34}
}
func (m *GetUserCatPropListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCatPropListResponse.Unmarshal(m, b)
}
func (m *GetUserCatPropListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCatPropListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserCatPropListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCatPropListResponse.Merge(dst, src)
}
func (m *GetUserCatPropListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserCatPropListResponse.Size(m)
}
func (m *GetUserCatPropListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCatPropListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCatPropListResponse proto.InternalMessageInfo

func (m *GetUserCatPropListResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserCatPropListResponse) GetPropList() []*CatPropTimeliness {
	if m != nil {
		return m.PropList
	}
	return nil
}

func (m *GetUserCatPropListResponse) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type CatPropNotify struct {
	PropId               uint32   `protobuf:"varint,1,opt,name=prop_id,json=propId,proto3" json:"prop_id,omitempty"`
	NotifyContent        string   `protobuf:"bytes,2,opt,name=notify_content,json=notifyContent,proto3" json:"notify_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CatPropNotify) Reset()         { *m = CatPropNotify{} }
func (m *CatPropNotify) String() string { return proto.CompactTextString(m) }
func (*CatPropNotify) ProtoMessage()    {}
func (*CatPropNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{35}
}
func (m *CatPropNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatPropNotify.Unmarshal(m, b)
}
func (m *CatPropNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatPropNotify.Marshal(b, m, deterministic)
}
func (dst *CatPropNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatPropNotify.Merge(dst, src)
}
func (m *CatPropNotify) XXX_Size() int {
	return xxx_messageInfo_CatPropNotify.Size(m)
}
func (m *CatPropNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_CatPropNotify.DiscardUnknown(m)
}

var xxx_messageInfo_CatPropNotify proto.InternalMessageInfo

func (m *CatPropNotify) GetPropId() uint32 {
	if m != nil {
		return m.PropId
	}
	return 0
}

func (m *CatPropNotify) GetNotifyContent() string {
	if m != nil {
		return m.NotifyContent
	}
	return ""
}

type GetUserExpireCatPropNotifyRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserExpireCatPropNotifyRequest) Reset()         { *m = GetUserExpireCatPropNotifyRequest{} }
func (m *GetUserExpireCatPropNotifyRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserExpireCatPropNotifyRequest) ProtoMessage()    {}
func (*GetUserExpireCatPropNotifyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{36}
}
func (m *GetUserExpireCatPropNotifyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExpireCatPropNotifyRequest.Unmarshal(m, b)
}
func (m *GetUserExpireCatPropNotifyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExpireCatPropNotifyRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserExpireCatPropNotifyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExpireCatPropNotifyRequest.Merge(dst, src)
}
func (m *GetUserExpireCatPropNotifyRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserExpireCatPropNotifyRequest.Size(m)
}
func (m *GetUserExpireCatPropNotifyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExpireCatPropNotifyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExpireCatPropNotifyRequest proto.InternalMessageInfo

func (m *GetUserExpireCatPropNotifyRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserExpireCatPropNotifyResponse struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	NeedNotify           bool             `protobuf:"varint,2,opt,name=need_notify,json=needNotify,proto3" json:"need_notify,omitempty"`
	NotifyList           []*CatPropNotify `protobuf:"bytes,3,rep,name=notify_list,json=notifyList,proto3" json:"notify_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserExpireCatPropNotifyResponse) Reset()         { *m = GetUserExpireCatPropNotifyResponse{} }
func (m *GetUserExpireCatPropNotifyResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserExpireCatPropNotifyResponse) ProtoMessage()    {}
func (*GetUserExpireCatPropNotifyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{37}
}
func (m *GetUserExpireCatPropNotifyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserExpireCatPropNotifyResponse.Unmarshal(m, b)
}
func (m *GetUserExpireCatPropNotifyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserExpireCatPropNotifyResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserExpireCatPropNotifyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserExpireCatPropNotifyResponse.Merge(dst, src)
}
func (m *GetUserExpireCatPropNotifyResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserExpireCatPropNotifyResponse.Size(m)
}
func (m *GetUserExpireCatPropNotifyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserExpireCatPropNotifyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserExpireCatPropNotifyResponse proto.InternalMessageInfo

func (m *GetUserExpireCatPropNotifyResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserExpireCatPropNotifyResponse) GetNeedNotify() bool {
	if m != nil {
		return m.NeedNotify
	}
	return false
}

func (m *GetUserExpireCatPropNotifyResponse) GetNotifyList() []*CatPropNotify {
	if m != nil {
		return m.NotifyList
	}
	return nil
}

type GetCatCanteenResourceRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCatCanteenResourceRequest) Reset()         { *m = GetCatCanteenResourceRequest{} }
func (m *GetCatCanteenResourceRequest) String() string { return proto.CompactTextString(m) }
func (*GetCatCanteenResourceRequest) ProtoMessage()    {}
func (*GetCatCanteenResourceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{38}
}
func (m *GetCatCanteenResourceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatCanteenResourceRequest.Unmarshal(m, b)
}
func (m *GetCatCanteenResourceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatCanteenResourceRequest.Marshal(b, m, deterministic)
}
func (dst *GetCatCanteenResourceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatCanteenResourceRequest.Merge(dst, src)
}
func (m *GetCatCanteenResourceRequest) XXX_Size() int {
	return xxx_messageInfo_GetCatCanteenResourceRequest.Size(m)
}
func (m *GetCatCanteenResourceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatCanteenResourceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatCanteenResourceRequest proto.InternalMessageInfo

func (m *GetCatCanteenResourceRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetCatCanteenResourceResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	CanResourcesCfg      *ResourcesCfg `protobuf:"bytes,2,opt,name=can_resources_cfg,json=canResourcesCfg,proto3" json:"can_resources_cfg,omitempty"`
	LevelCfg             []*LevelCfg   `protobuf:"bytes,3,rep,name=level_cfg,json=levelCfg,proto3" json:"level_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCatCanteenResourceResponse) Reset()         { *m = GetCatCanteenResourceResponse{} }
func (m *GetCatCanteenResourceResponse) String() string { return proto.CompactTextString(m) }
func (*GetCatCanteenResourceResponse) ProtoMessage()    {}
func (*GetCatCanteenResourceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e, []int{39}
}
func (m *GetCatCanteenResourceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatCanteenResourceResponse.Unmarshal(m, b)
}
func (m *GetCatCanteenResourceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatCanteenResourceResponse.Marshal(b, m, deterministic)
}
func (dst *GetCatCanteenResourceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatCanteenResourceResponse.Merge(dst, src)
}
func (m *GetCatCanteenResourceResponse) XXX_Size() int {
	return xxx_messageInfo_GetCatCanteenResourceResponse.Size(m)
}
func (m *GetCatCanteenResourceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatCanteenResourceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatCanteenResourceResponse proto.InternalMessageInfo

func (m *GetCatCanteenResourceResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCatCanteenResourceResponse) GetCanResourcesCfg() *ResourcesCfg {
	if m != nil {
		return m.CanResourcesCfg
	}
	return nil
}

func (m *GetCatCanteenResourceResponse) GetLevelCfg() []*LevelCfg {
	if m != nil {
		return m.LevelCfg
	}
	return nil
}

func init() {
	proto.RegisterType((*NotifyItem)(nil), "ga.cat_canteen_logic.NotifyItem")
	proto.RegisterType((*GameNotifyInfo)(nil), "ga.cat_canteen_logic.GameNotifyInfo")
	proto.RegisterType((*GameAccessNotifyInfo)(nil), "ga.cat_canteen_logic.GameAccessNotifyInfo")
	proto.RegisterType((*GetChanceGameAccessNotifyInfoRequest)(nil), "ga.cat_canteen_logic.GetChanceGameAccessNotifyInfoRequest")
	proto.RegisterType((*GetChanceGameAccessNotifyInfoResponse)(nil), "ga.cat_canteen_logic.GetChanceGameAccessNotifyInfoResponse")
	proto.RegisterType((*AwardEffect)(nil), "ga.cat_canteen_logic.AwardEffect")
	proto.RegisterType((*LevelGift)(nil), "ga.cat_canteen_logic.LevelGift")
	proto.RegisterType((*LevelMount)(nil), "ga.cat_canteen_logic.LevelMount")
	proto.RegisterType((*UpgradeAward)(nil), "ga.cat_canteen_logic.UpgradeAward")
	proto.RegisterType((*ResourcesCfg)(nil), "ga.cat_canteen_logic.ResourcesCfg")
	proto.RegisterType((*LevelCfg)(nil), "ga.cat_canteen_logic.LevelCfg")
	proto.RegisterType((*UserLevelStatus)(nil), "ga.cat_canteen_logic.UserLevelStatus")
	proto.RegisterType((*PropInfo)(nil), "ga.cat_canteen_logic.PropInfo")
	proto.RegisterType((*UserPlayFile)(nil), "ga.cat_canteen_logic.UserPlayFile")
	proto.RegisterType((*LevelInfo)(nil), "ga.cat_canteen_logic.LevelInfo")
	proto.RegisterType((*GetGameInfoRequest)(nil), "ga.cat_canteen_logic.GetGameInfoRequest")
	proto.RegisterType((*GetGameInfoResponse)(nil), "ga.cat_canteen_logic.GetGameInfoResponse")
	proto.RegisterType((*BuyChanceRequest)(nil), "ga.cat_canteen_logic.BuyChanceRequest")
	proto.RegisterType((*BuyChanceResponse)(nil), "ga.cat_canteen_logic.BuyChanceResponse")
	proto.RegisterType((*LightEffect)(nil), "ga.cat_canteen_logic.LightEffect")
	proto.RegisterType((*LotteryDrawAward)(nil), "ga.cat_canteen_logic.LotteryDrawAward")
	proto.RegisterType((*LotteryDrawResult)(nil), "ga.cat_canteen_logic.LotteryDrawResult")
	proto.RegisterType((*LotteryDrawRequest)(nil), "ga.cat_canteen_logic.LotteryDrawRequest")
	proto.RegisterType((*LotteryDrawResponse)(nil), "ga.cat_canteen_logic.LotteryDrawResponse")
	proto.RegisterType((*GetUserPlayFileRequest)(nil), "ga.cat_canteen_logic.GetUserPlayFileRequest")
	proto.RegisterType((*GetUserPlayFileResponse)(nil), "ga.cat_canteen_logic.GetUserPlayFileResponse")
	proto.RegisterType((*GameWinningRecord)(nil), "ga.cat_canteen_logic.GameWinningRecord")
	proto.RegisterType((*GetWinningRecordsRequest)(nil), "ga.cat_canteen_logic.GetWinningRecordsRequest")
	proto.RegisterType((*GetWinningRecordsResponse)(nil), "ga.cat_canteen_logic.GetWinningRecordsResponse")
	proto.RegisterType((*SimpleGameWinningRecord)(nil), "ga.cat_canteen_logic.SimpleGameWinningRecord")
	proto.RegisterType((*GetRecentWinningRecordsRequest)(nil), "ga.cat_canteen_logic.GetRecentWinningRecordsRequest")
	proto.RegisterType((*GetRecentWinningRecordsResponse)(nil), "ga.cat_canteen_logic.GetRecentWinningRecordsResponse")
	proto.RegisterType((*CatPropTimeliness)(nil), "ga.cat_canteen_logic.CatPropTimeliness")
	proto.RegisterType((*GetUserCatPropListRequest)(nil), "ga.cat_canteen_logic.GetUserCatPropListRequest")
	proto.RegisterType((*GetUserCatPropListResponse)(nil), "ga.cat_canteen_logic.GetUserCatPropListResponse")
	proto.RegisterType((*CatPropNotify)(nil), "ga.cat_canteen_logic.CatPropNotify")
	proto.RegisterType((*GetUserExpireCatPropNotifyRequest)(nil), "ga.cat_canteen_logic.GetUserExpireCatPropNotifyRequest")
	proto.RegisterType((*GetUserExpireCatPropNotifyResponse)(nil), "ga.cat_canteen_logic.GetUserExpireCatPropNotifyResponse")
	proto.RegisterType((*GetCatCanteenResourceRequest)(nil), "ga.cat_canteen_logic.GetCatCanteenResourceRequest")
	proto.RegisterType((*GetCatCanteenResourceResponse)(nil), "ga.cat_canteen_logic.GetCatCanteenResourceResponse")
	proto.RegisterEnum("ga.cat_canteen_logic.NotifyPlaceType", NotifyPlaceType_name, NotifyPlaceType_value)
	proto.RegisterEnum("ga.cat_canteen_logic.ResultType", ResultType_name, ResultType_value)
	proto.RegisterEnum("ga.cat_canteen_logic.GameNotifyInfo_NotifyType", GameNotifyInfo_NotifyType_name, GameNotifyInfo_NotifyType_value)
	proto.RegisterEnum("ga.cat_canteen_logic.GameAccessNotifyInfo_ChanceGameType", GameAccessNotifyInfo_ChanceGameType_name, GameAccessNotifyInfo_ChanceGameType_value)
	proto.RegisterEnum("ga.cat_canteen_logic.UserLevelStatus_Status", UserLevelStatus_Status_name, UserLevelStatus_Status_value)
	proto.RegisterEnum("ga.cat_canteen_logic.GameWinningRecord_RecordType", GameWinningRecord_RecordType_name, GameWinningRecord_RecordType_value)
	proto.RegisterEnum("ga.cat_canteen_logic.GetWinningRecordsRequest_QueryType", GetWinningRecordsRequest_QueryType_name, GetWinningRecordsRequest_QueryType_value)
	proto.RegisterEnum("ga.cat_canteen_logic.GetUserCatPropListRequest_PropType", GetUserCatPropListRequest_PropType_name, GetUserCatPropListRequest_PropType_value)
}

func init() {
	proto.RegisterFile("cat_canteen_logic/cat_canteen_logic.proto", fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e)
}

var fileDescriptor_cat_canteen_logic_df2dfed5ade6de2e = []byte{
	// 2854 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x5a, 0x5f, 0x6f, 0x1b, 0xc7,
	0xb5, 0xbf, 0xbb, 0xa4, 0x24, 0xf2, 0x90, 0x94, 0xa8, 0xb1, 0x2d, 0x53, 0xfe, 0x13, 0xc9, 0xeb,
	0xd8, 0x71, 0x82, 0x84, 0xbe, 0x50, 0x90, 0x9b, 0x6b, 0x04, 0x6d, 0x41, 0x93, 0x14, 0xcd, 0x84,
	0x22, 0xe9, 0x25, 0xa5, 0x20, 0x2d, 0xd0, 0xc5, 0x68, 0x39, 0xa4, 0xb7, 0x59, 0xee, 0xd2, 0xbb,
	0x4b, 0xc7, 0x04, 0x8a, 0xa2, 0x7d, 0x28, 0x90, 0x16, 0x68, 0x51, 0x14, 0x0d, 0x50, 0xf4, 0xa5,
	0xe8, 0x5b, 0x8b, 0xbe, 0xf7, 0xa5, 0x41, 0xbf, 0x41, 0x9f, 0xfa, 0xd0, 0x0f, 0x51, 0xa0, 0xe8,
	0x47, 0x28, 0xe6, 0xcc, 0x2c, 0xb9, 0x4b, 0x91, 0x0a, 0xe9, 0xe4, 0x49, 0x33, 0xe7, 0xec, 0x99,
	0x39, 0xf3, 0x3b, 0x7f, 0x67, 0x28, 0x78, 0xd3, 0xa4, 0x81, 0x61, 0x52, 0x27, 0x60, 0xcc, 0x31,
	0x6c, 0x77, 0x60, 0x99, 0x0f, 0x2f, 0x50, 0x8a, 0x23, 0xcf, 0x0d, 0x5c, 0x72, 0x75, 0x40, 0x8b,
	0x17, 0x78, 0x37, 0x72, 0x03, 0x6a, 0x9c, 0x53, 0x9f, 0x89, 0x8f, 0xb4, 0x3f, 0x2a, 0x00, 0x4d,
	0x37, 0xb0, 0xfa, 0x93, 0x7a, 0xc0, 0x86, 0xe4, 0x3a, 0x6c, 0x59, 0xbe, 0xe1, 0x30, 0xd6, 0x2b,
	0x28, 0x87, 0xca, 0x83, 0x94, 0xbe, 0x69, 0xf9, 0x4d, 0xc6, 0x7a, 0x84, 0x40, 0x32, 0x60, 0x2f,
	0x83, 0x82, 0x7a, 0xa8, 0x3c, 0x48, 0xeb, 0x38, 0x26, 0x57, 0x61, 0xc3, 0x74, 0x6d, 0xd7, 0x2b,
	0x24, 0x90, 0x28, 0x26, 0xe4, 0x06, 0xa4, 0x7a, 0x63, 0x8f, 0x06, 0x96, 0xeb, 0x14, 0x92, 0x87,
	0xca, 0x83, 0x9c, 0x3e, 0x9d, 0x93, 0x0f, 0x60, 0x63, 0x64, 0x53, 0x93, 0x15, 0x36, 0x0e, 0x95,
	0x07, 0xdb, 0x47, 0xf7, 0x8a, 0x8b, 0x54, 0x2c, 0x0a, 0x7d, 0xda, 0xfc, 0xc3, 0xee, 0x64, 0xc4,
	0x74, 0x21, 0xa3, 0xfd, 0x55, 0x85, 0xed, 0x1a, 0x1d, 0x32, 0xa9, 0xae, 0xd3, 0x77, 0x49, 0x1b,
	0x32, 0x0e, 0xce, 0x8c, 0x60, 0x32, 0x62, 0xa8, 0xf2, 0xf6, 0xd1, 0xc3, 0xc5, 0xab, 0xc6, 0x45,
	0xe5, 0x26, 0xb8, 0x3e, 0x38, 0xd3, 0x31, 0xb9, 0x0d, 0x70, 0xce, 0x06, 0x96, 0x63, 0x04, 0xd6,
	0x90, 0xe1, 0x69, 0x73, 0x7a, 0x1a, 0x29, 0x5d, 0x6b, 0xc8, 0xc8, 0x3e, 0xa4, 0x98, 0xd3, 0x13,
	0xcc, 0x04, 0x32, 0xb7, 0x98, 0xd3, 0x43, 0x56, 0x69, 0xaa, 0x8b, 0x6d, 0xf9, 0x41, 0x21, 0x79,
	0x98, 0x78, 0x90, 0x39, 0x3a, 0xbc, 0xec, 0x84, 0x1c, 0xf1, 0x70, 0xf3, 0x86, 0xe5, 0x07, 0x1c,
	0xfd, 0x67, 0xd4, 0x37, 0x3c, 0xd6, 0x43, 0x80, 0x52, 0xfa, 0xe6, 0x33, 0xea, 0xeb, 0xac, 0xa7,
	0x95, 0x42, 0x23, 0xa1, 0x8e, 0x37, 0xe1, 0x7a, 0xb3, 0xd5, 0xad, 0x1f, 0x7f, 0x62, 0x74, 0x3f,
	0x69, 0x57, 0x8d, 0xd3, 0x66, 0xa7, 0x5d, 0x2d, 0xd7, 0x8f, 0xeb, 0xd5, 0x4a, 0xfe, 0x7f, 0xc8,
	0x1e, 0x90, 0x28, 0xb3, 0xdc, 0x3a, 0x39, 0x69, 0x35, 0xf3, 0x8a, 0xf6, 0x0f, 0x15, 0xae, 0x72,
	0x08, 0x4a, 0xa6, 0xc9, 0x7c, 0x3f, 0x82, 0xe1, 0x19, 0xa4, 0x07, 0x74, 0xc8, 0xa2, 0x08, 0x3e,
	0x5a, 0x8e, 0xe0, 0xbc, 0x78, 0xb1, 0xfc, 0x8c, 0x3a, 0x26, 0xe3, 0x2c, 0xc4, 0x32, 0x35, 0x90,
	0x23, 0x72, 0x00, 0x99, 0x67, 0xf4, 0x05, 0x33, 0x28, 0x4a, 0x20, 0x5a, 0x29, 0x1d, 0x38, 0x49,
	0xac, 0x41, 0xaa, 0x8b, 0x00, 0x7b, 0x7d, 0x15, 0xe3, 0xc5, 0x40, 0xbb, 0x07, 0xdb, 0x72, 0x99,
	0x17, 0xcc, 0xf3, 0xb9, 0xd7, 0x71, 0xec, 0x92, 0x7a, 0x4e, 0x50, 0xcf, 0x04, 0x51, 0xeb, 0xc2,
	0x76, 0x5c, 0x55, 0x72, 0x08, 0xb7, 0xca, 0x4f, 0x4a, 0xcd, 0x72, 0xd5, 0xa8, 0x95, 0x4e, 0xaa,
	0x8b, 0xb0, 0x5c, 0xf4, 0x45, 0xb9, 0xd4, 0x35, 0xca, 0xa5, 0x66, 0xb7, 0x5a, 0xe5, 0xa8, 0xfe,
	0x5c, 0x81, 0xd7, 0x6b, 0x2c, 0x98, 0xad, 0x3c, 0x8f, 0x8f, 0xce, 0x9e, 0x8f, 0x99, 0x1f, 0x90,
	0xfb, 0x90, 0xe2, 0x51, 0x67, 0x78, 0xec, 0x39, 0x82, 0x9c, 0x39, 0xca, 0xf0, 0x93, 0x3e, 0xa6,
	0x3e, 0xd3, 0xd9, 0x73, 0x7d, 0xeb, 0x5c, 0x0c, 0xc8, 0xcd, 0xa8, 0x35, 0xd4, 0xc3, 0x04, 0x0f,
	0x9f, 0x29, 0xa4, 0xb7, 0x01, 0xcc, 0x67, 0xd4, 0x71, 0x98, 0x6d, 0x58, 0x3d, 0xe9, 0x7f, 0x69,
	0x49, 0xa9, 0xf7, 0xb4, 0xdf, 0x2b, 0x70, 0xef, 0x2b, 0x94, 0xf1, 0x47, 0xae, 0xe3, 0x33, 0xf2,
	0x26, 0xa4, 0xa5, 0x36, 0xfe, 0x48, 0xaa, 0x93, 0x9d, 0xa9, 0xe3, 0x8f, 0xf4, 0xd4, 0xb9, 0x1c,
	0x91, 0x8f, 0x20, 0x23, 0x2c, 0x28, 0xac, 0xa4, 0xa2, 0x95, 0xde, 0x5a, 0xdd, 0x41, 0x74, 0x10,
	0xe2, 0xdc, 0x56, 0xda, 0x9f, 0x14, 0xc8, 0x94, 0x3e, 0xa3, 0x5e, 0xaf, 0xda, 0xef, 0x33, 0x33,
	0xe0, 0xa7, 0x65, 0x38, 0xe2, 0xe7, 0x51, 0x44, 0xb2, 0x10, 0x84, 0x7a, 0x8f, 0xbc, 0x0d, 0x44,
	0x32, 0xc3, 0x18, 0x9f, 0x25, 0xa0, 0xbc, 0xe0, 0xc8, 0xa0, 0xe0, 0xc9, 0xe8, 0x7f, 0xe1, 0x6a,
	0xfc, 0xeb, 0xbe, 0xeb, 0x0d, 0x69, 0x20, 0x73, 0x13, 0x89, 0x7e, 0x7f, 0x8c, 0x1c, 0x8e, 0xa6,
	0x94, 0x18, 0x7b, 0x36, 0xa6, 0xaa, 0xb4, 0x2e, 0xd5, 0x39, 0xf5, 0x6c, 0xed, 0x37, 0x2a, 0xa4,
	0x1b, 0xec, 0x05, 0xb3, 0x6b, 0x56, 0x1f, 0x43, 0x73, 0x44, 0xcd, 0x4f, 0x67, 0x7a, 0x6e, 0xf2,
	0x69, 0xbd, 0xc7, 0x8f, 0x80, 0x0c, 0x87, 0xca, 0x7c, 0x91, 0xd6, 0x53, 0x9c, 0xd0, 0xa4, 0x22,
	0x5d, 0x20, 0x73, 0x64, 0x99, 0x52, 0x11, 0x5c, 0xa5, 0x6d, 0x99, 0x7c, 0x77, 0xc1, 0xf2, 0x2c,
	0x93, 0xc9, 0x44, 0x89, 0x2b, 0xb5, 0x39, 0x81, 0xec, 0xc1, 0x26, 0x1d, 0xba, 0x63, 0x27, 0x40,
	0x6f, 0xce, 0xe9, 0x72, 0x46, 0x1e, 0xc1, 0xa6, 0x50, 0xb1, 0xb0, 0x89, 0x66, 0xbb, 0xb3, 0xd8,
	0x12, 0x11, 0x90, 0x75, 0x29, 0x40, 0x2a, 0x90, 0xb5, 0xad, 0xc1, 0xb3, 0xc0, 0x90, 0x0b, 0x6c,
	0x5d, 0xb6, 0x40, 0x83, 0x7f, 0x29, 0x17, 0xc8, 0xd8, 0xb3, 0x89, 0xf6, 0x3b, 0x05, 0x00, 0x61,
	0x39, 0x41, 0x7d, 0xf6, 0x21, 0x85, 0x8a, 0x85, 0xc0, 0xa4, 0xf5, 0x2d, 0x9c, 0xd7, 0x7b, 0xfc,
	0x84, 0x82, 0x15, 0x81, 0x26, 0x8d, 0x14, 0xc4, 0xe6, 0x26, 0x88, 0x49, 0x04, 0x1c, 0xb1, 0x94,
	0x44, 0x87, 0xf2, 0x23, 0x18, 0x3d, 0x3a, 0xf1, 0x43, 0x74, 0x90, 0x52, 0xa1, 0x13, 0x7f, 0x19,
	0x3a, 0xda, 0xdf, 0x14, 0xc8, 0x9e, 0x8e, 0x06, 0x1e, 0xed, 0x31, 0x44, 0x80, 0x3c, 0x82, 0xad,
	0x91, 0xc7, 0x7c, 0xe6, 0x04, 0xd2, 0xcd, 0x0f, 0x96, 0x1c, 0x37, 0x34, 0xb4, 0x1e, 0x7e, 0x4f,
	0xfe, 0x0f, 0x36, 0xc4, 0x16, 0x2a, 0x0a, 0x1e, 0x5e, 0x22, 0x88, 0x50, 0xe8, 0xe2, 0x73, 0xf2,
	0x01, 0xa4, 0x47, 0x9e, 0x3b, 0x32, 0x2c, 0xa7, 0xef, 0xe2, 0xb9, 0x32, 0x47, 0xaf, 0x2d, 0x96,
	0x6d, 0x7b, 0xee, 0x08, 0x43, 0x24, 0x35, 0x92, 0x23, 0xed, 0x08, 0xb2, 0x3a, 0xf3, 0xdd, 0xb1,
	0x67, 0x32, 0xbf, 0xdc, 0x1f, 0x90, 0x3c, 0x24, 0xb8, 0x73, 0x0a, 0x64, 0xf9, 0x90, 0x53, 0x86,
	0xbd, 0xf7, 0x24, 0x9c, 0x7c, 0xa8, 0xfd, 0x2b, 0x09, 0x29, 0x54, 0x83, 0x0b, 0xec, 0x43, 0xca,
	0xe6, 0xe3, 0x99, 0xa3, 0x6e, 0xe1, 0x5c, 0xd8, 0x43, 0xb0, 0xa2, 0xf6, 0x40, 0x0a, 0xda, 0xa3,
	0x0a, 0x59, 0x4f, 0x6e, 0x6d, 0x98, 0xfd, 0x81, 0x54, 0x5d, 0x5b, 0xac, 0x7a, 0x54, 0x49, 0x3d,
	0x13, 0xca, 0x71, 0x05, 0xbe, 0x0d, 0xc0, 0x9c, 0xc0, 0x9b, 0x18, 0x03, 0xab, 0x1f, 0xa0, 0xe5,
	0x56, 0x00, 0x3d, 0x8d, 0x22, 0x18, 0x68, 0x35, 0xc8, 0x8d, 0x85, 0x05, 0x0d, 0xb4, 0x37, 0x5a,
	0x78, 0xa9, 0x1e, 0x51, 0x63, 0xeb, 0xd9, 0x71, 0xd4, 0xf4, 0xf7, 0x61, 0xc7, 0x74, 0xfd, 0xc0,
	0x30, 0x31, 0x1d, 0x1a, 0x23, 0xe6, 0x61, 0xc8, 0xe4, 0xf4, 0x1c, 0x27, 0x8b, 0x24, 0xd9, 0x66,
	0x1e, 0x79, 0x1f, 0x0a, 0xe7, 0xe3, 0x49, 0xf8, 0x99, 0x70, 0x24, 0xc3, 0x1d, 0x61, 0xff, 0xb2,
	0x85, 0x09, 0xf8, 0xda, 0xf9, 0x78, 0x22, 0xbe, 0x2f, 0x21, 0xb7, 0x85, 0x4c, 0xf2, 0x08, 0xf6,
	0xa3, 0x1b, 0xc4, 0x25, 0x53, 0x28, 0xb9, 0x37, 0xdb, 0x2a, 0x26, 0xfa, 0x3e, 0x14, 0x64, 0x96,
	0x32, 0x5d, 0x27, 0xb0, 0x9c, 0x31, 0x33, 0xfa, 0xd4, 0xb2, 0x0d, 0xd3, 0x09, 0x0a, 0x69, 0x54,
	0xf2, 0x9a, 0xe0, 0x97, 0x25, 0xfb, 0x98, 0x5a, 0x76, 0xd9, 0x09, 0xc8, 0x5d, 0xc8, 0x8d, 0x1d,
	0xdb, 0xe5, 0xf9, 0x06, 0xf9, 0x05, 0x40, 0x33, 0x66, 0x05, 0x51, 0xa4, 0x37, 0x72, 0x05, 0x36,
	0x86, 0xf4, 0xa5, 0xe1, 0x14, 0x32, 0xb8, 0x54, 0x72, 0x48, 0x5f, 0x36, 0xc9, 0x31, 0xec, 0x08,
	0xbb, 0xcc, 0x9c, 0x33, 0xbb, 0x92, 0x73, 0xe6, 0x50, 0x2c, 0x9c, 0x6a, 0xff, 0x56, 0x60, 0xe7,
	0xd4, 0x67, 0x1e, 0x1a, 0xaf, 0x13, 0xd0, 0x60, 0xec, 0x93, 0x0a, 0x6c, 0xfa, 0x38, 0x92, 0xfd,
	0xc3, 0xdb, 0x4b, 0x8c, 0x15, 0x17, 0x2b, 0x8a, 0x3f, 0xba, 0x94, 0xe5, 0x2d, 0x26, 0x46, 0xbb,
	0x68, 0xba, 0x70, 0xac, 0xfd, 0x58, 0x81, 0x4d, 0xb9, 0xc9, 0x1e, 0x90, 0x4e, 0xb7, 0xd4, 0x3d,
	0xed, 0xcc, 0x15, 0xe9, 0x6b, 0xb0, 0x2b, 0xe9, 0xdd, 0x96, 0xf1, 0xb8, 0x6a, 0xb4, 0xda, 0xbc,
	0x32, 0x47, 0xc8, 0xf5, 0x26, 0xd2, 0xea, 0xcd, 0x5a, 0x5e, 0x25, 0xb7, 0x61, 0x3f, 0xfc, 0xba,
	0x7a, 0xd2, 0x6e, 0xe9, 0x25, 0xbd, 0xde, 0xf8, 0xc4, 0x28, 0x37, 0x5a, 0x9d, 0x6a, 0x25, 0x9f,
	0x20, 0xbb, 0x90, 0x93, 0x6c, 0x49, 0x4a, 0x6a, 0xbf, 0x55, 0x20, 0x15, 0x9e, 0x1e, 0xcb, 0x00,
	0xe2, 0x37, 0x2b, 0x03, 0x9c, 0x25, 0xca, 0x00, 0x32, 0x4c, 0xd7, 0x99, 0x96, 0x01, 0xce, 0x32,
	0x5d, 0x67, 0xca, 0xc4, 0xc0, 0x4b, 0xcc, 0x98, 0x18, 0x77, 0xb3, 0x5c, 0x96, 0x8c, 0x65, 0xfa,
	0x3b, 0x90, 0x0d, 0xfb, 0x66, 0x9e, 0x05, 0x65, 0xa6, 0xcb, 0x84, 0xb4, 0x0a, 0x9d, 0x68, 0x5f,
	0xa8, 0x90, 0xe5, 0xa0, 0xb6, 0x6d, 0x3a, 0x39, 0xb6, 0x6c, 0x76, 0x59, 0xf4, 0xbf, 0x0e, 0xdb,
	0xb3, 0xb8, 0x44, 0x47, 0x13, 0x38, 0x67, 0xa7, 0xa1, 0xc7, 0xfd, 0xeb, 0x01, 0xe4, 0x3d, 0x1a,
	0x30, 0xc3, 0xed, 0x73, 0x3f, 0x19, 0x78, 0x61, 0xe7, 0xa6, 0xea, 0xdb, 0x9c, 0xde, 0xea, 0xb7,
	0x25, 0x95, 0x5c, 0x83, 0x4d, 0x93, 0x3a, 0x86, 0xfd, 0x42, 0xaa, 0xbd, 0x61, 0x52, 0xa7, 0xf1,
	0x82, 0x3c, 0x85, 0xdd, 0xb1, 0xcf, 0x3c, 0x43, 0xa8, 0x21, 0xbd, 0x42, 0x84, 0xf0, 0xbd, 0x95,
	0xbc, 0x42, 0xdf, 0x19, 0xcf, 0x79, 0x17, 0x2f, 0xa2, 0x88, 0xde, 0x78, 0x28, 0x23, 0x18, 0x6d,
	0xd0, 0x1c, 0x0f, 0xb9, 0x12, 0xb8, 0x9b, 0x83, 0xc5, 0x2c, 0xa7, 0x6f, 0xf0, 0x59, 0x53, 0xfb,
	0xb5, 0x22, 0x4b, 0x37, 0xda, 0xec, 0x03, 0x10, 0x59, 0x0e, 0xb3, 0x9a, 0x72, 0x99, 0xcf, 0x87,
	0x59, 0x54, 0x17, 0x28, 0xf2, 0x74, 0xf6, 0x04, 0xb6, 0x71, 0x87, 0x91, 0x4d, 0x27, 0x22, 0x6a,
	0xd4, 0x4b, 0xf3, 0x51, 0xc4, 0x1a, 0x7a, 0x76, 0x2c, 0x67, 0x18, 0x38, 0xdf, 0x03, 0x52, 0x63,
	0x01, 0x6f, 0x91, 0x5e, 0xa5, 0x2f, 0x8c, 0xb7, 0x7e, 0xea, 0x7c, 0xeb, 0xf7, 0x93, 0x04, 0x5c,
	0x89, 0xad, 0xbe, 0x7e, 0xa3, 0x77, 0x1f, 0x10, 0xf9, 0x30, 0x9d, 0xcd, 0x3c, 0x24, 0xc7, 0xc9,
	0x22, 0x89, 0x71, 0x17, 0xa9, 0xc1, 0x8e, 0xf4, 0x31, 0xa7, 0xef, 0x8a, 0xa6, 0x30, 0x81, 0x4d,
	0xe1, 0x65, 0x59, 0x5e, 0x64, 0x12, 0x3b, 0x1c, 0x62, 0xe3, 0x5e, 0x87, 0x7c, 0x34, 0xf1, 0x62,
	0xb2, 0x5f, 0xb1, 0x5e, 0x6c, 0xcf, 0x32, 0x32, 0xe6, 0xfa, 0x26, 0xec, 0x72, 0x67, 0x0c, 0xeb,
	0x90, 0x8f, 0xa6, 0xde, 0x58, 0xb9, 0x80, 0xed, 0x98, 0xd4, 0x89, 0x95, 0xdd, 0x77, 0x61, 0x2f,
	0xa2, 0x1a, 0x7b, 0x39, 0xb2, 0x3c, 0x66, 0xf4, 0x98, 0x6f, 0xa2, 0x03, 0xa6, 0xf5, 0x2b, 0xd3,
	0xfd, 0xab, 0xc8, 0xab, 0x30, 0xdf, 0xd4, 0x7e, 0x04, 0xf9, 0xc7, 0x21, 0xf9, 0x9b, 0x35, 0x2f,
	0x4f, 0xfb, 0xb1, 0x2a, 0x23, 0x7b, 0xff, 0xac, 0x19, 0x29, 0x2d, 0xda, 0xe7, 0x0a, 0xec, 0x46,
	0x14, 0x58, 0xdf, 0x03, 0x8a, 0x70, 0xa5, 0x6f, 0x39, 0xd4, 0x8e, 0x57, 0x34, 0xa9, 0xcd, 0x2e,
	0xb2, 0xa2, 0xb5, 0x8c, 0x14, 0x60, 0xeb, 0x9c, 0xda, 0x9c, 0x80, 0xfa, 0x24, 0xf5, 0x70, 0xaa,
	0xd9, 0x90, 0x89, 0x34, 0x90, 0x73, 0x9d, 0xb6, 0x32, 0xd7, 0x69, 0x47, 0xd8, 0xb3, 0xce, 0x46,
	0xb2, 0x4f, 0x7a, 0xef, 0xf1, 0x8b, 0xa4, 0x64, 0xff, 0xc0, 0x77, 0x1d, 0x99, 0x3f, 0xa5, 0xc4,
	0x87, 0xbe, 0xeb, 0x68, 0x3f, 0x55, 0x21, 0xdf, 0x70, 0x83, 0x80, 0x79, 0x93, 0x8a, 0x47, 0x3f,
	0x13, 0x2e, 0x51, 0x02, 0xde, 0x96, 0x8c, 0xed, 0x20, 0x7a, 0xb1, 0x3d, 0x5c, 0xea, 0x0c, 0x63,
	0x3b, 0x10, 0x6f, 0x01, 0xde, 0x74, 0x1c, 0x6d, 0x1e, 0xd5, 0x57, 0x6d, 0x1e, 0x13, 0x5f, 0xa3,
	0x79, 0x4c, 0xae, 0xd9, 0x3c, 0x7e, 0xae, 0xc2, 0x6e, 0x04, 0x07, 0x71, 0xaa, 0xcb, 0x6a, 0xc2,
	0x1c, 0x46, 0xea, 0x2b, 0x60, 0x54, 0x0d, 0x1b, 0xf5, 0x48, 0x22, 0xb8, 0xbf, 0xe4, 0xb4, 0x73,
	0x26, 0x92, 0x0d, 0x3d, 0xe6, 0x02, 0x0d, 0x72, 0x0e, 0x7b, 0x19, 0x18, 0x53, 0x4d, 0x45, 0x51,
	0xc9, 0x70, 0x62, 0x43, 0x6a, 0xfb, 0x16, 0xec, 0x5a, 0x8e, 0xe9, 0x4d, 0x0b, 0x93, 0xf1, 0x82,
	0xda, 0xb2, 0x2a, 0xee, 0x70, 0x46, 0x58, 0x9a, 0xce, 0xa8, 0xad, 0xfd, 0x52, 0x01, 0x12, 0x83,
	0xe2, 0x1b, 0x0d, 0xc7, 0x59, 0xc9, 0x4e, 0xc4, 0x4a, 0x76, 0x14, 0xea, 0x64, 0x0c, 0x6a, 0xed,
	0x3f, 0x0a, 0x5c, 0x89, 0xdb, 0x66, 0xed, 0xf0, 0xbc, 0x0b, 0x39, 0x8f, 0x0d, 0xa9, 0xe5, 0xc8,
	0xf8, 0x0c, 0x0b, 0xb8, 0x20, 0x8a, 0xc8, 0xe4, 0xf5, 0x0a, 0x4b, 0x55, 0xdf, 0xb2, 0x59, 0xd4,
	0x26, 0x2b, 0xd5, 0xab, 0x91, 0x1c, 0xa1, 0x49, 0xbe, 0x03, 0x9b, 0xc2, 0xce, 0xd2, 0x0f, 0xdf,
	0xf8, 0x4a, 0xab, 0x0a, 0x17, 0xd1, 0xa5, 0x98, 0x66, 0xc0, 0x5e, 0x8d, 0x05, 0xb1, 0x1d, 0xbe,
	0xd9, 0xa2, 0xf7, 0x17, 0x05, 0xae, 0x5f, 0xd8, 0x61, 0x7d, 0x5c, 0x2f, 0x42, 0xa6, 0xbe, 0x22,
	0x64, 0x0b, 0x4a, 0x68, 0x62, 0x41, 0x09, 0xd5, 0x7e, 0xb5, 0x01, 0xbb, 0xbc, 0x54, 0x7f, 0x6c,
	0x39, 0x8e, 0xe5, 0x0c, 0x74, 0x66, 0xba, 0x5e, 0x8f, 0x6c, 0x83, 0x3a, 0xbd, 0x44, 0xab, 0x56,
	0x8f, 0x7b, 0x93, 0xeb, 0xf5, 0x98, 0x17, 0x9e, 0x3d, 0xad, 0x6f, 0xe1, 0xbc, 0xde, 0x23, 0x1d,
	0x1e, 0xb8, 0x5c, 0x48, 0x04, 0x6e, 0x02, 0x03, 0xf7, 0x68, 0xf9, 0xa3, 0x4c, 0x6c, 0xa3, 0xa2,
	0xf8, 0x13, 0x86, 0x72, 0x38, 0x5e, 0x1c, 0x5f, 0xc9, 0x85, 0xf1, 0xc5, 0x6f, 0x79, 0x22, 0xec,
	0x31, 0x51, 0x6d, 0xac, 0x78, 0xcb, 0x43, 0x11, 0xec, 0xc9, 0x4a, 0x90, 0x11, 0xf2, 0x22, 0x8c,
	0x36, 0x57, 0xcc, 0x92, 0x62, 0x53, 0xf1, 0xf2, 0x10, 0xbf, 0x68, 0x6e, 0xad, 0x7d, 0xd1, 0x3c,
	0x80, 0x8c, 0xe9, 0x31, 0xde, 0xec, 0xe2, 0x6b, 0x6e, 0x0a, 0x0f, 0x0a, 0x82, 0x84, 0x0f, 0xba,
	0xdf, 0x0a, 0xcf, 0xc8, 0x13, 0x2c, 0x5e, 0xcb, 0xbe, 0x3a, 0x19, 0x8b, 0x23, 0xf2, 0xa9, 0xf6,
	0x67, 0x05, 0x60, 0x86, 0x34, 0xb9, 0x09, 0xd7, 0xf5, 0x6a, 0xb9, 0xa5, 0x57, 0x16, 0x3d, 0x34,
	0x6a, 0xf0, 0x5a, 0x94, 0x59, 0xfa, 0xb8, 0xa4, 0x57, 0x8c, 0x76, 0xab, 0x53, 0xef, 0xd6, 0xcf,
	0xaa, 0xc6, 0x59, 0xa9, 0x91, 0x57, 0xc8, 0x0d, 0xd8, 0xbb, 0xf8, 0x4d, 0xad, 0x7e, 0xdc, 0x15,
	0xb7, 0x9a, 0x8b, 0xbc, 0xd3, 0x76, 0x4d, 0x2f, 0x55, 0xaa, 0xf9, 0x04, 0xb9, 0x03, 0xb7, 0x2f,
	0xb2, 0xcb, 0x7a, 0xab, 0xd3, 0x31, 0x1a, 0xd5, 0xb3, 0x6a, 0x23, 0x9f, 0xd4, 0xbe, 0x54, 0xa1,
	0x50, 0x63, 0x41, 0xcc, 0x51, 0xfc, 0x75, 0xe3, 0x35, 0x9a, 0xff, 0xd4, 0x78, 0xa9, 0xb9, 0x09,
	0x69, 0xb7, 0xdf, 0xf7, 0x59, 0x10, 0xbe, 0x5c, 0xa6, 0xf5, 0x94, 0x20, 0xd4, 0x7b, 0xe4, 0x2a,
	0x6c, 0xd8, 0xd6, 0xd0, 0x0a, 0x6f, 0x40, 0x62, 0x42, 0x3e, 0x06, 0x78, 0x3e, 0x66, 0x9e, 0x7c,
	0xdb, 0x17, 0xbf, 0x18, 0xfc, 0xff, 0x12, 0x1f, 0x5f, 0xa2, 0x79, 0xf1, 0x29, 0x5f, 0x00, 0x3d,
	0x3d, 0xfd, 0x3c, 0x1c, 0x6a, 0x4f, 0x21, 0x3d, 0xa5, 0x73, 0x58, 0x9f, 0x9e, 0x56, 0xf5, 0x85,
	0x6f, 0xe9, 0x04, 0xb6, 0x23, 0xbc, 0x52, 0x83, 0x9b, 0xe1, 0x2a, 0xe4, 0x23, 0xb4, 0xc7, 0xad,
	0xe6, 0x69, 0x27, 0xaf, 0x6a, 0x3f, 0x53, 0x60, 0x7f, 0x81, 0x12, 0xeb, 0x27, 0xa3, 0x12, 0x6c,
	0x89, 0x90, 0xf4, 0x65, 0x16, 0x7a, 0x63, 0xc5, 0xa8, 0xd6, 0x43, 0x39, 0xed, 0x87, 0x70, 0xbd,
	0x63, 0x0d, 0x47, 0x36, 0xbb, 0x98, 0x62, 0xf2, 0x90, 0x18, 0x4f, 0xdb, 0x00, 0x3e, 0x24, 0x37,
	0x20, 0xe5, 0x58, 0xe6, 0xa7, 0xd1, 0xd7, 0xcb, 0x70, 0x4e, 0xde, 0x85, 0x24, 0xc6, 0x56, 0x62,
	0xb5, 0xd8, 0xc2, 0x8f, 0xb5, 0xef, 0xc3, 0x6b, 0x35, 0x16, 0xe8, 0xcc, 0x64, 0xce, 0xd7, 0xf4,
	0xa6, 0xa9, 0x57, 0xa8, 0x11, 0xaf, 0xd0, 0xbe, 0x50, 0xe0, 0x60, 0xe9, 0x06, 0xeb, 0xe3, 0x5d,
	0x9b, 0xc7, 0xfb, 0x9d, 0xc5, 0xc7, 0x5c, 0x82, 0xe8, 0x0c, 0xf5, 0x2f, 0x15, 0xd8, 0x2d, 0xd3,
	0x80, 0x87, 0x3e, 0xcf, 0x1e, 0xb6, 0xe5, 0xf0, 0x5b, 0x72, 0x1e, 0x12, 0xfc, 0xda, 0x2a, 0x01,
	0x77, 0xc6, 0x43, 0x5e, 0xc5, 0xfb, 0xf2, 0xe7, 0x25, 0x3f, 0xa0, 0xc3, 0x11, 0x9e, 0x2e, 0xa1,
	0x67, 0xfb, 0xe2, 0x17, 0x26, 0xa4, 0xcd, 0x3d, 0xd5, 0x25, 0xe6, 0x9f, 0xea, 0x22, 0xaf, 0x10,
	0xc9, 0x85, 0xaf, 0x10, 0x28, 0xb6, 0x31, 0xf7, 0xd0, 0x10, 0x7b, 0xa2, 0xd8, 0x8c, 0x3f, 0x51,
	0x68, 0x7f, 0x17, 0x0e, 0xcc, 0x8b, 0x9b, 0x3c, 0x05, 0xaf, 0x68, 0xaf, 0x90, 0x00, 0x7c, 0xd7,
	0x0b, 0x0c, 0xea, 0x9b, 0x78, 0xae, 0x94, 0xbe, 0xc5, 0xe7, 0x25, 0xdf, 0x9c, 0xee, 0x3e, 0x2d,
	0x58, 0x39, 0xb1, 0x3b, 0x46, 0x64, 0x5b, 0x3c, 0xb1, 0x60, 0x40, 0xee, 0xc3, 0xb5, 0xb6, 0xde,
	0x6a, 0x2f, 0x89, 0xc7, 0x19, 0xeb, 0xb8, 0xde, 0x79, 0x22, 0xde, 0x79, 0x66, 0xb4, 0xb3, 0xd6,
	0x69, 0xf9, 0x49, 0x55, 0xcf, 0xab, 0xda, 0x1f, 0x14, 0xb8, 0xb1, 0xe8, 0x3c, 0xeb, 0x7b, 0x48,
	0x45, 0x2a, 0x1e, 0xe9, 0x0c, 0x96, 0xc4, 0xe4, 0x05, 0xf3, 0x8b, 0x13, 0x62, 0x6b, 0x40, 0x20,
	0x89, 0xf7, 0x47, 0x61, 0x4b, 0x1c, 0x6b, 0x2d, 0xc8, 0x49, 0x11, 0xf9, 0x70, 0xb7, 0xf4, 0x75,
	0x69, 0xf6, 0x1b, 0x97, 0xe9, 0x3a, 0x41, 0x78, 0x21, 0x49, 0x87, 0xbf, 0x71, 0x95, 0x05, 0x51,
	0xfb, 0x08, 0xee, 0xc8, 0x33, 0x8b, 0x6b, 0x69, 0x6c, 0xf5, 0x35, 0x6d, 0xc9, 0xbb, 0x2b, 0xed,
	0xb2, 0xd5, 0xd6, 0x47, 0xf2, 0x00, 0x32, 0x0e, 0x63, 0xbd, 0xf0, 0xe9, 0x52, 0x38, 0x08, 0x70,
	0x92, 0x3c, 0x7f, 0x25, 0xfe, 0x8b, 0xa0, 0xe8, 0x5c, 0xef, 0x5e, 0x0a, 0xb6, 0xd4, 0x26, 0xf2,
	0x83, 0xa0, 0x76, 0x0c, 0xb7, 0x6a, 0x2c, 0x28, 0xd3, 0xa0, 0x2c, 0x04, 0xc2, 0x9b, 0xfd, 0xba,
	0x00, 0xfc, 0x53, 0x81, 0xdb, 0x4b, 0x16, 0x5a, 0xff, 0xec, 0x0b, 0x5f, 0x28, 0xd4, 0x57, 0x7f,
	0xa1, 0x88, 0x3d, 0x6a, 0x09, 0xa0, 0x56, 0x7e, 0xd4, 0x7a, 0xeb, 0x17, 0x0a, 0xec, 0xcc, 0xfd,
	0xc8, 0xce, 0x7b, 0x04, 0xf9, 0xbb, 0x71, 0xbb, 0x51, 0x2a, 0x2f, 0xfc, 0x39, 0xf4, 0x36, 0xec,
	0x5f, 0xfc, 0xa4, 0xfc, 0xa4, 0xd4, 0x6c, 0x56, 0x65, 0x83, 0x72, 0x91, 0x5d, 0x2b, 0x9d, 0x54,
	0xf3, 0x2a, 0xb9, 0x05, 0x85, 0x8b, 0xbc, 0xf6, 0xe9, 0xe3, 0x46, 0xbd, 0x9c, 0x4f, 0x70, 0x7d,
	0x60, 0x76, 0xbf, 0x14, 0xad, 0x52, 0xe7, 0xb4, 0xd1, 0x5d, 0xa4, 0xc4, 0x2d, 0x28, 0xc4, 0x98,
	0xa2, 0xc9, 0x31, 0x8e, 0x4b, 0x75, 0xae, 0xc3, 0x01, 0xdc, 0x5c, 0xc4, 0xed, 0x9c, 0x96, 0xcb,
	0xd5, 0x4e, 0x27, 0xaf, 0x92, 0xbb, 0x70, 0x10, 0xfd, 0x20, 0xd2, 0x04, 0xcd, 0xfa, 0xa5, 0xc7,
	0x1f, 0x42, 0xc1, 0x74, 0x87, 0xc5, 0x89, 0x35, 0x71, 0xc7, 0x1c, 0xd4, 0xa1, 0xdb, 0x63, 0xb6,
	0xf8, 0x87, 0x89, 0xef, 0x16, 0x07, 0xae, 0x4d, 0x9d, 0x41, 0xf1, 0xbd, 0xa3, 0x20, 0x28, 0x9a,
	0xee, 0xf0, 0x21, 0x92, 0x4d, 0xd7, 0x7e, 0x48, 0x47, 0xa3, 0x87, 0x26, 0x0d, 0xde, 0x91, 0xf0,
	0xbf, 0x83, 0xf0, 0x9f, 0x6f, 0x22, 0xff, 0xdd, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0xf8, 0xb1,
	0xba, 0xf8, 0xb9, 0x21, 0x00, 0x00,
}
