// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-listening-logic_.proto

package channellisteninglogic // import "golang.52tt.com/protocol/app/channellisteninglogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 歌单类型
type SongType int32

const (
	SongType_OldSongSheetType SongType = 0
	SongType_NewSongSheetType SongType = 1
)

var SongType_name = map[int32]string{
	0: "OldSongSheetType",
	1: "NewSongSheetType",
}
var SongType_value = map[string]int32{
	"OldSongSheetType": 0,
	"NewSongSheetType": 1,
}

func (x SongType) String() string {
	return proto.EnumName(SongType_name, int32(x))
}
func (SongType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{0}
}

type ChannelListeningCheckInType int32

const (
	ChannelListeningCheckInType_ChannelListeningCheckInType_UNDEFINED   ChannelListeningCheckInType = 0
	ChannelListeningCheckInType_ChannelListeningCheckInType_SUCCESS     ChannelListeningCheckInType = 1
	ChannelListeningCheckInType_ChannelListeningCheckInType_GET_TITLE   ChannelListeningCheckInType = 2
	ChannelListeningCheckInType_ChannelListeningCheckInType_RENEW_TITLE ChannelListeningCheckInType = 3
)

var ChannelListeningCheckInType_name = map[int32]string{
	0: "ChannelListeningCheckInType_UNDEFINED",
	1: "ChannelListeningCheckInType_SUCCESS",
	2: "ChannelListeningCheckInType_GET_TITLE",
	3: "ChannelListeningCheckInType_RENEW_TITLE",
}
var ChannelListeningCheckInType_value = map[string]int32{
	"ChannelListeningCheckInType_UNDEFINED":   0,
	"ChannelListeningCheckInType_SUCCESS":     1,
	"ChannelListeningCheckInType_GET_TITLE":   2,
	"ChannelListeningCheckInType_RENEW_TITLE": 3,
}

func (x ChannelListeningCheckInType) String() string {
	return proto.EnumName(ChannelListeningCheckInType_name, int32(x))
}
func (ChannelListeningCheckInType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{1}
}

// 挂房听歌信息
type GetChannelListeningSimpleInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelListeningSimpleInfoReq) Reset()         { *m = GetChannelListeningSimpleInfoReq{} }
func (m *GetChannelListeningSimpleInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelListeningSimpleInfoReq) ProtoMessage()    {}
func (*GetChannelListeningSimpleInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{0}
}
func (m *GetChannelListeningSimpleInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListeningSimpleInfoReq.Unmarshal(m, b)
}
func (m *GetChannelListeningSimpleInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListeningSimpleInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelListeningSimpleInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListeningSimpleInfoReq.Merge(dst, src)
}
func (m *GetChannelListeningSimpleInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelListeningSimpleInfoReq.Size(m)
}
func (m *GetChannelListeningSimpleInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListeningSimpleInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListeningSimpleInfoReq proto.InternalMessageInfo

func (m *GetChannelListeningSimpleInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelListeningSimpleInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelListeningSimpleInfoResp struct {
	BaseResp             *app.BaseResp                `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LikeUserAccounts     []string                     `protobuf:"bytes,2,rep,name=like_user_accounts,json=likeUserAccounts,proto3" json:"like_user_accounts,omitempty"`
	LikeUserCount        uint32                       `protobuf:"varint,3,opt,name=like_user_count,json=likeUserCount,proto3" json:"like_user_count,omitempty"`
	TodayFlowerCount     uint32                       `protobuf:"varint,4,opt,name=today_flower_count,json=todayFlowerCount,proto3" json:"today_flower_count,omitempty"`
	EnteredAt            uint32                       `protobuf:"varint,5,opt,name=entered_at,json=enteredAt,proto3" json:"entered_at,omitempty"`
	Theme                *ChannelListenTheme          `protobuf:"bytes,6,opt,name=theme,proto3" json:"theme,omitempty"`
	RuleUrl              string                       `protobuf:"bytes,7,opt,name=rule_url,json=ruleUrl,proto3" json:"rule_url,omitempty"`
	CheckInTopic         string                       `protobuf:"bytes,8,opt,name=check_in_topic,json=checkInTopic,proto3" json:"check_in_topic,omitempty"`
	CheckInInfo          *ChannelListeningCheckInInfo `protobuf:"bytes,9,opt,name=check_in_info,json=checkInInfo,proto3" json:"check_in_info,omitempty"`
	HasUserSetMood       bool                         `protobuf:"varint,10,opt,name=has_user_set_mood,json=hasUserSetMood,proto3" json:"has_user_set_mood,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetChannelListeningSimpleInfoResp) Reset()         { *m = GetChannelListeningSimpleInfoResp{} }
func (m *GetChannelListeningSimpleInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelListeningSimpleInfoResp) ProtoMessage()    {}
func (*GetChannelListeningSimpleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{1}
}
func (m *GetChannelListeningSimpleInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListeningSimpleInfoResp.Unmarshal(m, b)
}
func (m *GetChannelListeningSimpleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListeningSimpleInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelListeningSimpleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListeningSimpleInfoResp.Merge(dst, src)
}
func (m *GetChannelListeningSimpleInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelListeningSimpleInfoResp.Size(m)
}
func (m *GetChannelListeningSimpleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListeningSimpleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListeningSimpleInfoResp proto.InternalMessageInfo

func (m *GetChannelListeningSimpleInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelListeningSimpleInfoResp) GetLikeUserAccounts() []string {
	if m != nil {
		return m.LikeUserAccounts
	}
	return nil
}

func (m *GetChannelListeningSimpleInfoResp) GetLikeUserCount() uint32 {
	if m != nil {
		return m.LikeUserCount
	}
	return 0
}

func (m *GetChannelListeningSimpleInfoResp) GetTodayFlowerCount() uint32 {
	if m != nil {
		return m.TodayFlowerCount
	}
	return 0
}

func (m *GetChannelListeningSimpleInfoResp) GetEnteredAt() uint32 {
	if m != nil {
		return m.EnteredAt
	}
	return 0
}

func (m *GetChannelListeningSimpleInfoResp) GetTheme() *ChannelListenTheme {
	if m != nil {
		return m.Theme
	}
	return nil
}

func (m *GetChannelListeningSimpleInfoResp) GetRuleUrl() string {
	if m != nil {
		return m.RuleUrl
	}
	return ""
}

func (m *GetChannelListeningSimpleInfoResp) GetCheckInTopic() string {
	if m != nil {
		return m.CheckInTopic
	}
	return ""
}

func (m *GetChannelListeningSimpleInfoResp) GetCheckInInfo() *ChannelListeningCheckInInfo {
	if m != nil {
		return m.CheckInInfo
	}
	return nil
}

func (m *GetChannelListeningSimpleInfoResp) GetHasUserSetMood() bool {
	if m != nil {
		return m.HasUserSetMood
	}
	return false
}

type ChannelListeningCheckInInfo struct {
	HaveCheckIn          bool     `protobuf:"varint,1,opt,name=have_check_in,json=haveCheckIn,proto3" json:"have_check_in,omitempty"`
	Day                  uint32   `protobuf:"varint,2,opt,name=day,proto3" json:"day,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	ExpireAt             uint32   `protobuf:"varint,4,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListeningCheckInInfo) Reset()         { *m = ChannelListeningCheckInInfo{} }
func (m *ChannelListeningCheckInInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningCheckInInfo) ProtoMessage()    {}
func (*ChannelListeningCheckInInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{2}
}
func (m *ChannelListeningCheckInInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningCheckInInfo.Unmarshal(m, b)
}
func (m *ChannelListeningCheckInInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningCheckInInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningCheckInInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningCheckInInfo.Merge(dst, src)
}
func (m *ChannelListeningCheckInInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningCheckInInfo.Size(m)
}
func (m *ChannelListeningCheckInInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningCheckInInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningCheckInInfo proto.InternalMessageInfo

func (m *ChannelListeningCheckInInfo) GetHaveCheckIn() bool {
	if m != nil {
		return m.HaveCheckIn
	}
	return false
}

func (m *ChannelListeningCheckInInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *ChannelListeningCheckInInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ChannelListeningCheckInInfo) GetExpireAt() uint32 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

// 喜欢某首歌
type LikeSongReq struct {
	BaseReq              *app.BaseReq          `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Song                 *ChannelListeningSong `protobuf:"bytes,3,opt,name=song,proto3" json:"song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *LikeSongReq) Reset()         { *m = LikeSongReq{} }
func (m *LikeSongReq) String() string { return proto.CompactTextString(m) }
func (*LikeSongReq) ProtoMessage()    {}
func (*LikeSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{3}
}
func (m *LikeSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeSongReq.Unmarshal(m, b)
}
func (m *LikeSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeSongReq.Marshal(b, m, deterministic)
}
func (dst *LikeSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeSongReq.Merge(dst, src)
}
func (m *LikeSongReq) XXX_Size() int {
	return xxx_messageInfo_LikeSongReq.Size(m)
}
func (m *LikeSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_LikeSongReq proto.InternalMessageInfo

func (m *LikeSongReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *LikeSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LikeSongReq) GetSong() *ChannelListeningSong {
	if m != nil {
		return m.Song
	}
	return nil
}

type LikeSongResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *LikeSongResp) Reset()         { *m = LikeSongResp{} }
func (m *LikeSongResp) String() string { return proto.CompactTextString(m) }
func (*LikeSongResp) ProtoMessage()    {}
func (*LikeSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{4}
}
func (m *LikeSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeSongResp.Unmarshal(m, b)
}
func (m *LikeSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeSongResp.Marshal(b, m, deterministic)
}
func (dst *LikeSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeSongResp.Merge(dst, src)
}
func (m *LikeSongResp) XXX_Size() int {
	return xxx_messageInfo_LikeSongResp.Size(m)
}
func (m *LikeSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_LikeSongResp proto.InternalMessageInfo

func (m *LikeSongResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChannelListeningSong struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListeningSong) Reset()         { *m = ChannelListeningSong{} }
func (m *ChannelListeningSong) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningSong) ProtoMessage()    {}
func (*ChannelListeningSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{5}
}
func (m *ChannelListeningSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningSong.Unmarshal(m, b)
}
func (m *ChannelListeningSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningSong.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningSong.Merge(dst, src)
}
func (m *ChannelListeningSong) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningSong.Size(m)
}
func (m *ChannelListeningSong) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningSong.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningSong proto.InternalMessageInfo

func (m *ChannelListeningSong) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ChannelListeningSong) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 喜欢歌曲（挂房听歌）
type LikeSongNotify struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromAccount          string   `protobuf:"bytes,2,opt,name=from_account,json=fromAccount,proto3" json:"from_account,omitempty"`
	FromNickname         string   `protobuf:"bytes,3,opt,name=from_nickname,json=fromNickname,proto3" json:"from_nickname,omitempty"`
	SongName             string   `protobuf:"bytes,4,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	TotalCount           uint32   `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	ChannelId            uint32   `protobuf:"varint,6,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LikeSongNotify) Reset()         { *m = LikeSongNotify{} }
func (m *LikeSongNotify) String() string { return proto.CompactTextString(m) }
func (*LikeSongNotify) ProtoMessage()    {}
func (*LikeSongNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{6}
}
func (m *LikeSongNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LikeSongNotify.Unmarshal(m, b)
}
func (m *LikeSongNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LikeSongNotify.Marshal(b, m, deterministic)
}
func (dst *LikeSongNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LikeSongNotify.Merge(dst, src)
}
func (m *LikeSongNotify) XXX_Size() int {
	return xxx_messageInfo_LikeSongNotify.Size(m)
}
func (m *LikeSongNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_LikeSongNotify.DiscardUnknown(m)
}

var xxx_messageInfo_LikeSongNotify proto.InternalMessageInfo

func (m *LikeSongNotify) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *LikeSongNotify) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *LikeSongNotify) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *LikeSongNotify) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *LikeSongNotify) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *LikeSongNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 被喜欢歌曲列表
type GetBeLikedSongListReq struct {
	BaseReq              *app.BaseReq              `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32                    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetBeLikedSongListReq) Reset()         { *m = GetBeLikedSongListReq{} }
func (m *GetBeLikedSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetBeLikedSongListReq) ProtoMessage()    {}
func (*GetBeLikedSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{7}
}
func (m *GetBeLikedSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeLikedSongListReq.Unmarshal(m, b)
}
func (m *GetBeLikedSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeLikedSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetBeLikedSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeLikedSongListReq.Merge(dst, src)
}
func (m *GetBeLikedSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetBeLikedSongListReq.Size(m)
}
func (m *GetBeLikedSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeLikedSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeLikedSongListReq proto.InternalMessageInfo

func (m *GetBeLikedSongListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBeLikedSongListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetBeLikedSongListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetBeLikedSongListReq) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetBeLikedSongListResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Songs                []*BeLikedSong            `protobuf:"bytes,2,rep,name=songs,proto3" json:"songs,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetBeLikedSongListResp) Reset()         { *m = GetBeLikedSongListResp{} }
func (m *GetBeLikedSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetBeLikedSongListResp) ProtoMessage()    {}
func (*GetBeLikedSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{8}
}
func (m *GetBeLikedSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeLikedSongListResp.Unmarshal(m, b)
}
func (m *GetBeLikedSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeLikedSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetBeLikedSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeLikedSongListResp.Merge(dst, src)
}
func (m *GetBeLikedSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetBeLikedSongListResp.Size(m)
}
func (m *GetBeLikedSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeLikedSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeLikedSongListResp proto.InternalMessageInfo

func (m *GetBeLikedSongListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBeLikedSongListResp) GetSongs() []*BeLikedSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *GetBeLikedSongListResp) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type BeLikedSong struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	SongName             string   `protobuf:"bytes,4,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BeLikedSong) Reset()         { *m = BeLikedSong{} }
func (m *BeLikedSong) String() string { return proto.CompactTextString(m) }
func (*BeLikedSong) ProtoMessage()    {}
func (*BeLikedSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{9}
}
func (m *BeLikedSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BeLikedSong.Unmarshal(m, b)
}
func (m *BeLikedSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BeLikedSong.Marshal(b, m, deterministic)
}
func (dst *BeLikedSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BeLikedSong.Merge(dst, src)
}
func (m *BeLikedSong) XXX_Size() int {
	return xxx_messageInfo_BeLikedSong.Size(m)
}
func (m *BeLikedSong) XXX_DiscardUnknown() {
	xxx_messageInfo_BeLikedSong.DiscardUnknown(m)
}

var xxx_messageInfo_BeLikedSong proto.InternalMessageInfo

func (m *BeLikedSong) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *BeLikedSong) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *BeLikedSong) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *BeLikedSong) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

// 喜欢的歌曲列表
type GetLikedSongListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetLikedSongListReq) Reset()         { *m = GetLikedSongListReq{} }
func (m *GetLikedSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetLikedSongListReq) ProtoMessage()    {}
func (*GetLikedSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{10}
}
func (m *GetLikedSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLikedSongListReq.Unmarshal(m, b)
}
func (m *GetLikedSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLikedSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetLikedSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLikedSongListReq.Merge(dst, src)
}
func (m *GetLikedSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetLikedSongListReq.Size(m)
}
func (m *GetLikedSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLikedSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLikedSongListReq proto.InternalMessageInfo

func (m *GetLikedSongListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetLikedSongListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLikedSongListResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SongList             []string      `protobuf:"bytes,2,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetLikedSongListResp) Reset()         { *m = GetLikedSongListResp{} }
func (m *GetLikedSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetLikedSongListResp) ProtoMessage()    {}
func (*GetLikedSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{11}
}
func (m *GetLikedSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLikedSongListResp.Unmarshal(m, b)
}
func (m *GetLikedSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLikedSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetLikedSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLikedSongListResp.Merge(dst, src)
}
func (m *GetLikedSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetLikedSongListResp.Size(m)
}
func (m *GetLikedSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLikedSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLikedSongListResp proto.InternalMessageInfo

func (m *GetLikedSongListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetLikedSongListResp) GetSongList() []string {
	if m != nil {
		return m.SongList
	}
	return nil
}

// 获取花花列表
type GetUserFlowerListReq struct {
	BaseReq              *app.BaseReq              `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32                    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetUserFlowerListReq) Reset()         { *m = GetUserFlowerListReq{} }
func (m *GetUserFlowerListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFlowerListReq) ProtoMessage()    {}
func (*GetUserFlowerListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{12}
}
func (m *GetUserFlowerListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFlowerListReq.Unmarshal(m, b)
}
func (m *GetUserFlowerListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFlowerListReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFlowerListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFlowerListReq.Merge(dst, src)
}
func (m *GetUserFlowerListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFlowerListReq.Size(m)
}
func (m *GetUserFlowerListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFlowerListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFlowerListReq proto.InternalMessageInfo

func (m *GetUserFlowerListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserFlowerListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserFlowerListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetUserFlowerListReq) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetUserFlowerListResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Flowers              []*UserFlower             `protobuf:"bytes,2,rep,name=flowers,proto3" json:"flowers,omitempty"`
	Flower               *UserFlower               `protobuf:"bytes,3,opt,name=flower,proto3" json:"flower,omitempty"`
	TodayFlowerCount     uint32                    `protobuf:"varint,4,opt,name=today_flower_count,json=todayFlowerCount,proto3" json:"today_flower_count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,5,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetUserFlowerListResp) Reset()         { *m = GetUserFlowerListResp{} }
func (m *GetUserFlowerListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFlowerListResp) ProtoMessage()    {}
func (*GetUserFlowerListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{13}
}
func (m *GetUserFlowerListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFlowerListResp.Unmarshal(m, b)
}
func (m *GetUserFlowerListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFlowerListResp.Marshal(b, m, deterministic)
}
func (dst *GetUserFlowerListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFlowerListResp.Merge(dst, src)
}
func (m *GetUserFlowerListResp) XXX_Size() int {
	return xxx_messageInfo_GetUserFlowerListResp.Size(m)
}
func (m *GetUserFlowerListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFlowerListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFlowerListResp proto.InternalMessageInfo

func (m *GetUserFlowerListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserFlowerListResp) GetFlowers() []*UserFlower {
	if m != nil {
		return m.Flowers
	}
	return nil
}

func (m *GetUserFlowerListResp) GetFlower() *UserFlower {
	if m != nil {
		return m.Flower
	}
	return nil
}

func (m *GetUserFlowerListResp) GetTodayFlowerCount() uint32 {
	if m != nil {
		return m.TodayFlowerCount
	}
	return 0
}

func (m *GetUserFlowerListResp) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type UserFlower struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Rank                 uint32   `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	Num                  uint32   `protobuf:"varint,5,opt,name=num,proto3" json:"num,omitempty"`
	CheckInTitle         string   `protobuf:"bytes,6,opt,name=check_in_title,json=checkInTitle,proto3" json:"check_in_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserFlower) Reset()         { *m = UserFlower{} }
func (m *UserFlower) String() string { return proto.CompactTextString(m) }
func (*UserFlower) ProtoMessage()    {}
func (*UserFlower) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{14}
}
func (m *UserFlower) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserFlower.Unmarshal(m, b)
}
func (m *UserFlower) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserFlower.Marshal(b, m, deterministic)
}
func (dst *UserFlower) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserFlower.Merge(dst, src)
}
func (m *UserFlower) XXX_Size() int {
	return xxx_messageInfo_UserFlower.Size(m)
}
func (m *UserFlower) XXX_DiscardUnknown() {
	xxx_messageInfo_UserFlower.DiscardUnknown(m)
}

var xxx_messageInfo_UserFlower proto.InternalMessageInfo

func (m *UserFlower) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserFlower) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserFlower) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserFlower) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *UserFlower) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *UserFlower) GetCheckInTitle() string {
	if m != nil {
		return m.CheckInTitle
	}
	return ""
}

// 获取花花历史详情
type GetFlowerDetailReq struct {
	BaseReq              *app.BaseReq              `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32                    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetFlowerDetailReq) Reset()         { *m = GetFlowerDetailReq{} }
func (m *GetFlowerDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetFlowerDetailReq) ProtoMessage()    {}
func (*GetFlowerDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{15}
}
func (m *GetFlowerDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowerDetailReq.Unmarshal(m, b)
}
func (m *GetFlowerDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowerDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetFlowerDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowerDetailReq.Merge(dst, src)
}
func (m *GetFlowerDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetFlowerDetailReq.Size(m)
}
func (m *GetFlowerDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowerDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowerDetailReq proto.InternalMessageInfo

func (m *GetFlowerDetailReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFlowerDetailReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetFlowerDetailReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetFlowerDetailReq) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetFlowerDetailResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Details              []*FlowerDetail           `protobuf:"bytes,2,rep,name=details,proto3" json:"details,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetFlowerDetailResp) Reset()         { *m = GetFlowerDetailResp{} }
func (m *GetFlowerDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetFlowerDetailResp) ProtoMessage()    {}
func (*GetFlowerDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{16}
}
func (m *GetFlowerDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlowerDetailResp.Unmarshal(m, b)
}
func (m *GetFlowerDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlowerDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetFlowerDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlowerDetailResp.Merge(dst, src)
}
func (m *GetFlowerDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetFlowerDetailResp.Size(m)
}
func (m *GetFlowerDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlowerDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlowerDetailResp proto.InternalMessageInfo

func (m *GetFlowerDetailResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFlowerDetailResp) GetDetails() []*FlowerDetail {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *GetFlowerDetailResp) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type FlowerDetail struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	FlowerCount          uint32   `protobuf:"varint,3,opt,name=flower_count,json=flowerCount,proto3" json:"flower_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlowerDetail) Reset()         { *m = FlowerDetail{} }
func (m *FlowerDetail) String() string { return proto.CompactTextString(m) }
func (*FlowerDetail) ProtoMessage()    {}
func (*FlowerDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{17}
}
func (m *FlowerDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlowerDetail.Unmarshal(m, b)
}
func (m *FlowerDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlowerDetail.Marshal(b, m, deterministic)
}
func (dst *FlowerDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlowerDetail.Merge(dst, src)
}
func (m *FlowerDetail) XXX_Size() int {
	return xxx_messageInfo_FlowerDetail.Size(m)
}
func (m *FlowerDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_FlowerDetail.DiscardUnknown(m)
}

var xxx_messageInfo_FlowerDetail proto.InternalMessageInfo

func (m *FlowerDetail) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *FlowerDetail) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *FlowerDetail) GetFlowerCount() uint32 {
	if m != nil {
		return m.FlowerCount
	}
	return 0
}

type ChannelListeningLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListeningLoadMore) Reset()         { *m = ChannelListeningLoadMore{} }
func (m *ChannelListeningLoadMore) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningLoadMore) ProtoMessage()    {}
func (*ChannelListeningLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{18}
}
func (m *ChannelListeningLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningLoadMore.Unmarshal(m, b)
}
func (m *ChannelListeningLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningLoadMore.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningLoadMore.Merge(dst, src)
}
func (m *ChannelListeningLoadMore) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningLoadMore.Size(m)
}
func (m *ChannelListeningLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningLoadMore proto.InternalMessageInfo

func (m *ChannelListeningLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *ChannelListeningLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

// 听歌花花奖励消息
type FlowerAwardMsg struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	ChannelId            uint32   `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	NewContent           string   `protobuf:"bytes,6,opt,name=new_content,json=newContent,proto3" json:"new_content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlowerAwardMsg) Reset()         { *m = FlowerAwardMsg{} }
func (m *FlowerAwardMsg) String() string { return proto.CompactTextString(m) }
func (*FlowerAwardMsg) ProtoMessage()    {}
func (*FlowerAwardMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{19}
}
func (m *FlowerAwardMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlowerAwardMsg.Unmarshal(m, b)
}
func (m *FlowerAwardMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlowerAwardMsg.Marshal(b, m, deterministic)
}
func (dst *FlowerAwardMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlowerAwardMsg.Merge(dst, src)
}
func (m *FlowerAwardMsg) XXX_Size() int {
	return xxx_messageInfo_FlowerAwardMsg.Size(m)
}
func (m *FlowerAwardMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_FlowerAwardMsg.DiscardUnknown(m)
}

var xxx_messageInfo_FlowerAwardMsg proto.InternalMessageInfo

func (m *FlowerAwardMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FlowerAwardMsg) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *FlowerAwardMsg) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *FlowerAwardMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *FlowerAwardMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *FlowerAwardMsg) GetNewContent() string {
	if m != nil {
		return m.NewContent
	}
	return ""
}

// 花花数量变化
type FlowerNotify struct {
	FlowerCount          uint32   `protobuf:"varint,1,opt,name=flower_count,json=flowerCount,proto3" json:"flower_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlowerNotify) Reset()         { *m = FlowerNotify{} }
func (m *FlowerNotify) String() string { return proto.CompactTextString(m) }
func (*FlowerNotify) ProtoMessage()    {}
func (*FlowerNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{20}
}
func (m *FlowerNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlowerNotify.Unmarshal(m, b)
}
func (m *FlowerNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlowerNotify.Marshal(b, m, deterministic)
}
func (dst *FlowerNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlowerNotify.Merge(dst, src)
}
func (m *FlowerNotify) XXX_Size() int {
	return xxx_messageInfo_FlowerNotify.Size(m)
}
func (m *FlowerNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_FlowerNotify.DiscardUnknown(m)
}

var xxx_messageInfo_FlowerNotify proto.InternalMessageInfo

func (m *FlowerNotify) GetFlowerCount() uint32 {
	if m != nil {
		return m.FlowerCount
	}
	return 0
}

// 花花通知手账本
type FlowerToPostNotify struct {
	FlowerDesc_1         string   `protobuf:"bytes,1,opt,name=flower_desc_1,json=flowerDesc1,proto3" json:"flower_desc_1,omitempty"`
	FlowerDesc_2         string   `protobuf:"bytes,2,opt,name=flower_desc_2,json=flowerDesc2,proto3" json:"flower_desc_2,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlowerToPostNotify) Reset()         { *m = FlowerToPostNotify{} }
func (m *FlowerToPostNotify) String() string { return proto.CompactTextString(m) }
func (*FlowerToPostNotify) ProtoMessage()    {}
func (*FlowerToPostNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{21}
}
func (m *FlowerToPostNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlowerToPostNotify.Unmarshal(m, b)
}
func (m *FlowerToPostNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlowerToPostNotify.Marshal(b, m, deterministic)
}
func (dst *FlowerToPostNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlowerToPostNotify.Merge(dst, src)
}
func (m *FlowerToPostNotify) XXX_Size() int {
	return xxx_messageInfo_FlowerToPostNotify.Size(m)
}
func (m *FlowerToPostNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_FlowerToPostNotify.DiscardUnknown(m)
}

var xxx_messageInfo_FlowerToPostNotify proto.InternalMessageInfo

func (m *FlowerToPostNotify) GetFlowerDesc_1() string {
	if m != nil {
		return m.FlowerDesc_1
	}
	return ""
}

func (m *FlowerToPostNotify) GetFlowerDesc_2() string {
	if m != nil {
		return m.FlowerDesc_2
	}
	return ""
}

type ChannelListenTheme struct {
	Bg                   string   `protobuf:"bytes,1,opt,name=bg,proto3" json:"bg,omitempty"`
	PlayerBg             string   `protobuf:"bytes,2,opt,name=player_bg,json=playerBg,proto3" json:"player_bg,omitempty"`
	ClockBg              string   `protobuf:"bytes,3,opt,name=clock_bg,json=clockBg,proto3" json:"clock_bg,omitempty"`
	HalfFlowerUrl        string   `protobuf:"bytes,4,opt,name=half_flower_url,json=halfFlowerUrl,proto3" json:"half_flower_url,omitempty"`
	EntireFlowerUrl      string   `protobuf:"bytes,5,opt,name=entire_flower_url,json=entireFlowerUrl,proto3" json:"entire_flower_url,omitempty"`
	Unit                 string   `protobuf:"bytes,6,opt,name=unit,proto3" json:"unit,omitempty"`
	HoldMicBgs           []string `protobuf:"bytes,7,rep,name=hold_mic_bgs,json=holdMicBgs,proto3" json:"hold_mic_bgs,omitempty"`
	LeaveMicBg           string   `protobuf:"bytes,8,opt,name=leave_mic_bg,json=leaveMicBg,proto3" json:"leave_mic_bg,omitempty"`
	MicSeat              string   `protobuf:"bytes,9,opt,name=mic_seat,json=micSeat,proto3" json:"mic_seat,omitempty"`
	NormalSeat           string   `protobuf:"bytes,10,opt,name=normal_seat,json=normalSeat,proto3" json:"normal_seat,omitempty"`
	ClosedSeat           string   `protobuf:"bytes,11,opt,name=closed_seat,json=closedSeat,proto3" json:"closed_seat,omitempty"`
	ShareEntranceIcon    string   `protobuf:"bytes,12,opt,name=share_entrance_icon,json=shareEntranceIcon,proto3" json:"share_entrance_icon,omitempty"`
	NewHoldMicBgs        []string `protobuf:"bytes,13,rep,name=new_hold_mic_bgs,json=newHoldMicBgs,proto3" json:"new_hold_mic_bgs,omitempty"`
	NewLeaveMicBg        string   `protobuf:"bytes,14,opt,name=new_leave_mic_bg,json=newLeaveMicBg,proto3" json:"new_leave_mic_bg,omitempty"`
	ResourceUrl          string   `protobuf:"bytes,15,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	ResourceMd5          string   `protobuf:"bytes,16,opt,name=resource_md5,json=resourceMd5,proto3" json:"resource_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListenTheme) Reset()         { *m = ChannelListenTheme{} }
func (m *ChannelListenTheme) String() string { return proto.CompactTextString(m) }
func (*ChannelListenTheme) ProtoMessage()    {}
func (*ChannelListenTheme) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{22}
}
func (m *ChannelListenTheme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListenTheme.Unmarshal(m, b)
}
func (m *ChannelListenTheme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListenTheme.Marshal(b, m, deterministic)
}
func (dst *ChannelListenTheme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListenTheme.Merge(dst, src)
}
func (m *ChannelListenTheme) XXX_Size() int {
	return xxx_messageInfo_ChannelListenTheme.Size(m)
}
func (m *ChannelListenTheme) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListenTheme.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListenTheme proto.InternalMessageInfo

func (m *ChannelListenTheme) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ChannelListenTheme) GetPlayerBg() string {
	if m != nil {
		return m.PlayerBg
	}
	return ""
}

func (m *ChannelListenTheme) GetClockBg() string {
	if m != nil {
		return m.ClockBg
	}
	return ""
}

func (m *ChannelListenTheme) GetHalfFlowerUrl() string {
	if m != nil {
		return m.HalfFlowerUrl
	}
	return ""
}

func (m *ChannelListenTheme) GetEntireFlowerUrl() string {
	if m != nil {
		return m.EntireFlowerUrl
	}
	return ""
}

func (m *ChannelListenTheme) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *ChannelListenTheme) GetHoldMicBgs() []string {
	if m != nil {
		return m.HoldMicBgs
	}
	return nil
}

func (m *ChannelListenTheme) GetLeaveMicBg() string {
	if m != nil {
		return m.LeaveMicBg
	}
	return ""
}

func (m *ChannelListenTheme) GetMicSeat() string {
	if m != nil {
		return m.MicSeat
	}
	return ""
}

func (m *ChannelListenTheme) GetNormalSeat() string {
	if m != nil {
		return m.NormalSeat
	}
	return ""
}

func (m *ChannelListenTheme) GetClosedSeat() string {
	if m != nil {
		return m.ClosedSeat
	}
	return ""
}

func (m *ChannelListenTheme) GetShareEntranceIcon() string {
	if m != nil {
		return m.ShareEntranceIcon
	}
	return ""
}

func (m *ChannelListenTheme) GetNewHoldMicBgs() []string {
	if m != nil {
		return m.NewHoldMicBgs
	}
	return nil
}

func (m *ChannelListenTheme) GetNewLeaveMicBg() string {
	if m != nil {
		return m.NewLeaveMicBg
	}
	return ""
}

func (m *ChannelListenTheme) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

func (m *ChannelListenTheme) GetResourceMd5() string {
	if m != nil {
		return m.ResourceMd5
	}
	return ""
}

type ChannelListenThemeNotify struct {
	Theme                *ChannelListenTheme `protobuf:"bytes,1,opt,name=theme,proto3" json:"theme,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ChannelListenThemeNotify) Reset()         { *m = ChannelListenThemeNotify{} }
func (m *ChannelListenThemeNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelListenThemeNotify) ProtoMessage()    {}
func (*ChannelListenThemeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{23}
}
func (m *ChannelListenThemeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListenThemeNotify.Unmarshal(m, b)
}
func (m *ChannelListenThemeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListenThemeNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelListenThemeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListenThemeNotify.Merge(dst, src)
}
func (m *ChannelListenThemeNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelListenThemeNotify.Size(m)
}
func (m *ChannelListenThemeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListenThemeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListenThemeNotify proto.InternalMessageInfo

func (m *ChannelListenThemeNotify) GetTheme() *ChannelListenTheme {
	if m != nil {
		return m.Theme
	}
	return nil
}

type SetListeningCheckInTopicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Topic                string       `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetListeningCheckInTopicReq) Reset()         { *m = SetListeningCheckInTopicReq{} }
func (m *SetListeningCheckInTopicReq) String() string { return proto.CompactTextString(m) }
func (*SetListeningCheckInTopicReq) ProtoMessage()    {}
func (*SetListeningCheckInTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{24}
}
func (m *SetListeningCheckInTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetListeningCheckInTopicReq.Unmarshal(m, b)
}
func (m *SetListeningCheckInTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetListeningCheckInTopicReq.Marshal(b, m, deterministic)
}
func (dst *SetListeningCheckInTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetListeningCheckInTopicReq.Merge(dst, src)
}
func (m *SetListeningCheckInTopicReq) XXX_Size() int {
	return xxx_messageInfo_SetListeningCheckInTopicReq.Size(m)
}
func (m *SetListeningCheckInTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetListeningCheckInTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetListeningCheckInTopicReq proto.InternalMessageInfo

func (m *SetListeningCheckInTopicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetListeningCheckInTopicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetListeningCheckInTopicReq) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

type SetListeningCheckInTopicResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetListeningCheckInTopicResp) Reset()         { *m = SetListeningCheckInTopicResp{} }
func (m *SetListeningCheckInTopicResp) String() string { return proto.CompactTextString(m) }
func (*SetListeningCheckInTopicResp) ProtoMessage()    {}
func (*SetListeningCheckInTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{25}
}
func (m *SetListeningCheckInTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetListeningCheckInTopicResp.Unmarshal(m, b)
}
func (m *SetListeningCheckInTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetListeningCheckInTopicResp.Marshal(b, m, deterministic)
}
func (dst *SetListeningCheckInTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetListeningCheckInTopicResp.Merge(dst, src)
}
func (m *SetListeningCheckInTopicResp) XXX_Size() int {
	return xxx_messageInfo_SetListeningCheckInTopicResp.Size(m)
}
func (m *SetListeningCheckInTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetListeningCheckInTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetListeningCheckInTopicResp proto.InternalMessageInfo

func (m *SetListeningCheckInTopicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 打卡话题变更通知
type ChannelListeningTopicChangeNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Topic                string   `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListeningTopicChangeNotify) Reset()         { *m = ChannelListeningTopicChangeNotify{} }
func (m *ChannelListeningTopicChangeNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningTopicChangeNotify) ProtoMessage()    {}
func (*ChannelListeningTopicChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{26}
}
func (m *ChannelListeningTopicChangeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningTopicChangeNotify.Unmarshal(m, b)
}
func (m *ChannelListeningTopicChangeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningTopicChangeNotify.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningTopicChangeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningTopicChangeNotify.Merge(dst, src)
}
func (m *ChannelListeningTopicChangeNotify) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningTopicChangeNotify.Size(m)
}
func (m *ChannelListeningTopicChangeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningTopicChangeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningTopicChangeNotify proto.InternalMessageInfo

func (m *ChannelListeningTopicChangeNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelListeningTopicChangeNotify) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

type ListeningCheckInReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningCheckInReq) Reset()         { *m = ListeningCheckInReq{} }
func (m *ListeningCheckInReq) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInReq) ProtoMessage()    {}
func (*ListeningCheckInReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{27}
}
func (m *ListeningCheckInReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInReq.Unmarshal(m, b)
}
func (m *ListeningCheckInReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInReq.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInReq.Merge(dst, src)
}
func (m *ListeningCheckInReq) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInReq.Size(m)
}
func (m *ListeningCheckInReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInReq proto.InternalMessageInfo

func (m *ListeningCheckInReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningCheckInReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningCheckInResp struct {
	BaseResp             *app.BaseResp                     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Type                 ChannelListeningCheckInType       `protobuf:"varint,2,opt,name=type,proto3,enum=ga.channellisteninglogic.ChannelListeningCheckInType" json:"type,omitempty"`
	CheckInInfo          *ChannelListeningCheckInInfo      `protobuf:"bytes,3,opt,name=check_in_info,json=checkInInfo,proto3" json:"check_in_info,omitempty"`
	ShareCard            *ChannelListeningCheckInShareCard `protobuf:"bytes,4,opt,name=share_card,json=shareCard,proto3" json:"share_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ListeningCheckInResp) Reset()         { *m = ListeningCheckInResp{} }
func (m *ListeningCheckInResp) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInResp) ProtoMessage()    {}
func (*ListeningCheckInResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{28}
}
func (m *ListeningCheckInResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInResp.Unmarshal(m, b)
}
func (m *ListeningCheckInResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInResp.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInResp.Merge(dst, src)
}
func (m *ListeningCheckInResp) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInResp.Size(m)
}
func (m *ListeningCheckInResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInResp proto.InternalMessageInfo

func (m *ListeningCheckInResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningCheckInResp) GetType() ChannelListeningCheckInType {
	if m != nil {
		return m.Type
	}
	return ChannelListeningCheckInType_ChannelListeningCheckInType_UNDEFINED
}

func (m *ListeningCheckInResp) GetCheckInInfo() *ChannelListeningCheckInInfo {
	if m != nil {
		return m.CheckInInfo
	}
	return nil
}

func (m *ListeningCheckInResp) GetShareCard() *ChannelListeningCheckInShareCard {
	if m != nil {
		return m.ShareCard
	}
	return nil
}

// 获取打卡榜
type GetListeningUserCheckInInfoListReq struct {
	BaseReq              *app.BaseReq              `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                    `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Count                uint32                    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetListeningUserCheckInInfoListReq) Reset()         { *m = GetListeningUserCheckInInfoListReq{} }
func (m *GetListeningUserCheckInInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetListeningUserCheckInInfoListReq) ProtoMessage()    {}
func (*GetListeningUserCheckInInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{29}
}
func (m *GetListeningUserCheckInInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListeningUserCheckInInfoListReq.Unmarshal(m, b)
}
func (m *GetListeningUserCheckInInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListeningUserCheckInInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetListeningUserCheckInInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListeningUserCheckInInfoListReq.Merge(dst, src)
}
func (m *GetListeningUserCheckInInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetListeningUserCheckInInfoListReq.Size(m)
}
func (m *GetListeningUserCheckInInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListeningUserCheckInInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetListeningUserCheckInInfoListReq proto.InternalMessageInfo

func (m *GetListeningUserCheckInInfoListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetListeningUserCheckInInfoListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetListeningUserCheckInInfoListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetListeningUserCheckInInfoListReq) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetListeningUserCheckInInfoListResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *UserCheckInInfo          `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	List                 []*UserCheckInInfo        `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	LoadMore             *ChannelListeningLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetListeningUserCheckInInfoListResp) Reset()         { *m = GetListeningUserCheckInInfoListResp{} }
func (m *GetListeningUserCheckInInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetListeningUserCheckInInfoListResp) ProtoMessage()    {}
func (*GetListeningUserCheckInInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{30}
}
func (m *GetListeningUserCheckInInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListeningUserCheckInInfoListResp.Unmarshal(m, b)
}
func (m *GetListeningUserCheckInInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListeningUserCheckInInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetListeningUserCheckInInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListeningUserCheckInInfoListResp.Merge(dst, src)
}
func (m *GetListeningUserCheckInInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetListeningUserCheckInInfoListResp.Size(m)
}
func (m *GetListeningUserCheckInInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListeningUserCheckInInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetListeningUserCheckInInfoListResp proto.InternalMessageInfo

func (m *GetListeningUserCheckInInfoListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetListeningUserCheckInInfoListResp) GetInfo() *UserCheckInInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetListeningUserCheckInInfoListResp) GetList() []*UserCheckInInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetListeningUserCheckInInfoListResp) GetLoadMore() *ChannelListeningLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type ChannelListeningCheckInShareCard struct {
	CheckInTitleDesc     string                            `protobuf:"bytes,1,opt,name=check_in_title_desc,json=checkInTitleDesc,proto3" json:"check_in_title_desc,omitempty"`
	ShowTime             int64                             `protobuf:"varint,2,opt,name=show_time,json=showTime,proto3" json:"show_time,omitempty"`
	Account              string                            `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string                            `protobuf:"bytes,4,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	LittleTitle          string                            `protobuf:"bytes,5,opt,name=little_title,json=littleTitle,proto3" json:"little_title,omitempty"`
	Title                string                            `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	ChannelId            uint32                            `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	CardImg              string                            `protobuf:"bytes,8,opt,name=card_img,json=cardImg,proto3" json:"card_img,omitempty"`
	WebBg                string                            `protobuf:"bytes,9,opt,name=web_bg,json=webBg,proto3" json:"web_bg,omitempty"`
	BtDesc               string                            `protobuf:"bytes,10,opt,name=bt_desc,json=btDesc,proto3" json:"bt_desc,omitempty"`
	WebTitle             string                            `protobuf:"bytes,11,opt,name=web_title,json=webTitle,proto3" json:"web_title,omitempty"`
	ShareInfo            *ChannelListeningCheckInShareInfo `protobuf:"bytes,12,opt,name=share_info,json=shareInfo,proto3" json:"share_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ChannelListeningCheckInShareCard) Reset()         { *m = ChannelListeningCheckInShareCard{} }
func (m *ChannelListeningCheckInShareCard) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningCheckInShareCard) ProtoMessage()    {}
func (*ChannelListeningCheckInShareCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{31}
}
func (m *ChannelListeningCheckInShareCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningCheckInShareCard.Unmarshal(m, b)
}
func (m *ChannelListeningCheckInShareCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningCheckInShareCard.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningCheckInShareCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningCheckInShareCard.Merge(dst, src)
}
func (m *ChannelListeningCheckInShareCard) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningCheckInShareCard.Size(m)
}
func (m *ChannelListeningCheckInShareCard) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningCheckInShareCard.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningCheckInShareCard proto.InternalMessageInfo

func (m *ChannelListeningCheckInShareCard) GetCheckInTitleDesc() string {
	if m != nil {
		return m.CheckInTitleDesc
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetShowTime() int64 {
	if m != nil {
		return m.ShowTime
	}
	return 0
}

func (m *ChannelListeningCheckInShareCard) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetLittleTitle() string {
	if m != nil {
		return m.LittleTitle
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelListeningCheckInShareCard) GetCardImg() string {
	if m != nil {
		return m.CardImg
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetWebBg() string {
	if m != nil {
		return m.WebBg
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetBtDesc() string {
	if m != nil {
		return m.BtDesc
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetWebTitle() string {
	if m != nil {
		return m.WebTitle
	}
	return ""
}

func (m *ChannelListeningCheckInShareCard) GetShareInfo() *ChannelListeningCheckInShareInfo {
	if m != nil {
		return m.ShareInfo
	}
	return nil
}

type ChannelListeningCheckInShareInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Img                  string   `protobuf:"bytes,4,opt,name=img,proto3" json:"img,omitempty"`
	NewSubTitle          string   `protobuf:"bytes,5,opt,name=new_sub_title,json=newSubTitle,proto3" json:"new_sub_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelListeningCheckInShareInfo) Reset()         { *m = ChannelListeningCheckInShareInfo{} }
func (m *ChannelListeningCheckInShareInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningCheckInShareInfo) ProtoMessage()    {}
func (*ChannelListeningCheckInShareInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{32}
}
func (m *ChannelListeningCheckInShareInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningCheckInShareInfo.Unmarshal(m, b)
}
func (m *ChannelListeningCheckInShareInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningCheckInShareInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningCheckInShareInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningCheckInShareInfo.Merge(dst, src)
}
func (m *ChannelListeningCheckInShareInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningCheckInShareInfo.Size(m)
}
func (m *ChannelListeningCheckInShareInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningCheckInShareInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningCheckInShareInfo proto.InternalMessageInfo

func (m *ChannelListeningCheckInShareInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ChannelListeningCheckInShareInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ChannelListeningCheckInShareInfo) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *ChannelListeningCheckInShareInfo) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *ChannelListeningCheckInShareInfo) GetNewSubTitle() string {
	if m != nil {
		return m.NewSubTitle
	}
	return ""
}

type UserCheckInInfo struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Rank                 uint32   `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	Day                  uint32   `protobuf:"varint,5,opt,name=day,proto3" json:"day,omitempty"`
	CheckInTitle         string   `protobuf:"bytes,6,opt,name=check_in_title,json=checkInTitle,proto3" json:"check_in_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserCheckInInfo) Reset()         { *m = UserCheckInInfo{} }
func (m *UserCheckInInfo) String() string { return proto.CompactTextString(m) }
func (*UserCheckInInfo) ProtoMessage()    {}
func (*UserCheckInInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{33}
}
func (m *UserCheckInInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCheckInInfo.Unmarshal(m, b)
}
func (m *UserCheckInInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCheckInInfo.Marshal(b, m, deterministic)
}
func (dst *UserCheckInInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCheckInInfo.Merge(dst, src)
}
func (m *UserCheckInInfo) XXX_Size() int {
	return xxx_messageInfo_UserCheckInInfo.Size(m)
}
func (m *UserCheckInInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCheckInInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserCheckInInfo proto.InternalMessageInfo

func (m *UserCheckInInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserCheckInInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserCheckInInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserCheckInInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *UserCheckInInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *UserCheckInInfo) GetCheckInTitle() string {
	if m != nil {
		return m.CheckInTitle
	}
	return ""
}

// 分享打卡
type ListeningCheckInSharedReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ShareId              string       `protobuf:"bytes,2,opt,name=share_id,json=shareId,proto3" json:"share_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListeningCheckInSharedReq) Reset()         { *m = ListeningCheckInSharedReq{} }
func (m *ListeningCheckInSharedReq) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInSharedReq) ProtoMessage()    {}
func (*ListeningCheckInSharedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{34}
}
func (m *ListeningCheckInSharedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInSharedReq.Unmarshal(m, b)
}
func (m *ListeningCheckInSharedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInSharedReq.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInSharedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInSharedReq.Merge(dst, src)
}
func (m *ListeningCheckInSharedReq) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInSharedReq.Size(m)
}
func (m *ListeningCheckInSharedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInSharedReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInSharedReq proto.InternalMessageInfo

func (m *ListeningCheckInSharedReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningCheckInSharedReq) GetShareId() string {
	if m != nil {
		return m.ShareId
	}
	return ""
}

type ListeningCheckInSharedResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ListeningCheckInSharedResp) Reset()         { *m = ListeningCheckInSharedResp{} }
func (m *ListeningCheckInSharedResp) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInSharedResp) ProtoMessage()    {}
func (*ListeningCheckInSharedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{35}
}
func (m *ListeningCheckInSharedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInSharedResp.Unmarshal(m, b)
}
func (m *ListeningCheckInSharedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInSharedResp.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInSharedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInSharedResp.Merge(dst, src)
}
func (m *ListeningCheckInSharedResp) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInSharedResp.Size(m)
}
func (m *ListeningCheckInSharedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInSharedResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInSharedResp proto.InternalMessageInfo

func (m *ListeningCheckInSharedResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// LISTENING_CHECK_IN = 263; // 挂房听歌打卡
type CheckInNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Day                  uint32   `protobuf:"varint,4,opt,name=day,proto3" json:"day,omitempty"`
	Topic                string   `protobuf:"bytes,5,opt,name=topic,proto3" json:"topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInNotify) Reset()         { *m = CheckInNotify{} }
func (m *CheckInNotify) String() string { return proto.CompactTextString(m) }
func (*CheckInNotify) ProtoMessage()    {}
func (*CheckInNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{36}
}
func (m *CheckInNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInNotify.Unmarshal(m, b)
}
func (m *CheckInNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInNotify.Marshal(b, m, deterministic)
}
func (dst *CheckInNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInNotify.Merge(dst, src)
}
func (m *CheckInNotify) XXX_Size() int {
	return xxx_messageInfo_CheckInNotify.Size(m)
}
func (m *CheckInNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInNotify.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInNotify proto.InternalMessageInfo

func (m *CheckInNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckInNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckInNotify) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CheckInNotify) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *CheckInNotify) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

type GetRcmdSongMenuReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SongSheetType        uint32       `protobuf:"varint,2,opt,name=song_sheet_type,json=songSheetType,proto3" json:"song_sheet_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRcmdSongMenuReq) Reset()         { *m = GetRcmdSongMenuReq{} }
func (m *GetRcmdSongMenuReq) String() string { return proto.CompactTextString(m) }
func (*GetRcmdSongMenuReq) ProtoMessage()    {}
func (*GetRcmdSongMenuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{37}
}
func (m *GetRcmdSongMenuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdSongMenuReq.Unmarshal(m, b)
}
func (m *GetRcmdSongMenuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdSongMenuReq.Marshal(b, m, deterministic)
}
func (dst *GetRcmdSongMenuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdSongMenuReq.Merge(dst, src)
}
func (m *GetRcmdSongMenuReq) XXX_Size() int {
	return xxx_messageInfo_GetRcmdSongMenuReq.Size(m)
}
func (m *GetRcmdSongMenuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdSongMenuReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdSongMenuReq proto.InternalMessageInfo

func (m *GetRcmdSongMenuReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRcmdSongMenuReq) GetSongSheetType() uint32 {
	if m != nil {
		return m.SongSheetType
	}
	return 0
}

type GetRcmdSongMenuResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Menus                []*ListeningSongMenu `protobuf:"bytes,2,rep,name=menus,proto3" json:"menus,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetRcmdSongMenuResp) Reset()         { *m = GetRcmdSongMenuResp{} }
func (m *GetRcmdSongMenuResp) String() string { return proto.CompactTextString(m) }
func (*GetRcmdSongMenuResp) ProtoMessage()    {}
func (*GetRcmdSongMenuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{38}
}
func (m *GetRcmdSongMenuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdSongMenuResp.Unmarshal(m, b)
}
func (m *GetRcmdSongMenuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdSongMenuResp.Marshal(b, m, deterministic)
}
func (dst *GetRcmdSongMenuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdSongMenuResp.Merge(dst, src)
}
func (m *GetRcmdSongMenuResp) XXX_Size() int {
	return xxx_messageInfo_GetRcmdSongMenuResp.Size(m)
}
func (m *GetRcmdSongMenuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdSongMenuResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdSongMenuResp proto.InternalMessageInfo

func (m *GetRcmdSongMenuResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRcmdSongMenuResp) GetMenus() []*ListeningSongMenu {
	if m != nil {
		return m.Menus
	}
	return nil
}

type ListeningSongMenu struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Bg                   string   `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningSongMenu) Reset()         { *m = ListeningSongMenu{} }
func (m *ListeningSongMenu) String() string { return proto.CompactTextString(m) }
func (*ListeningSongMenu) ProtoMessage()    {}
func (*ListeningSongMenu) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{39}
}
func (m *ListeningSongMenu) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningSongMenu.Unmarshal(m, b)
}
func (m *ListeningSongMenu) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningSongMenu.Marshal(b, m, deterministic)
}
func (dst *ListeningSongMenu) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningSongMenu.Merge(dst, src)
}
func (m *ListeningSongMenu) XXX_Size() int {
	return xxx_messageInfo_ListeningSongMenu.Size(m)
}
func (m *ListeningSongMenu) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningSongMenu.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningSongMenu proto.InternalMessageInfo

func (m *ListeningSongMenu) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListeningSongMenu) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListeningSongMenu) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

type GetSongByMenuIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SongMenuId           string       `protobuf:"bytes,2,opt,name=song_menu_id,json=songMenuId,proto3" json:"song_menu_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSongByMenuIdReq) Reset()         { *m = GetSongByMenuIdReq{} }
func (m *GetSongByMenuIdReq) String() string { return proto.CompactTextString(m) }
func (*GetSongByMenuIdReq) ProtoMessage()    {}
func (*GetSongByMenuIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{40}
}
func (m *GetSongByMenuIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongByMenuIdReq.Unmarshal(m, b)
}
func (m *GetSongByMenuIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongByMenuIdReq.Marshal(b, m, deterministic)
}
func (dst *GetSongByMenuIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongByMenuIdReq.Merge(dst, src)
}
func (m *GetSongByMenuIdReq) XXX_Size() int {
	return xxx_messageInfo_GetSongByMenuIdReq.Size(m)
}
func (m *GetSongByMenuIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongByMenuIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongByMenuIdReq proto.InternalMessageInfo

func (m *GetSongByMenuIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetSongByMenuIdReq) GetSongMenuId() string {
	if m != nil {
		return m.SongMenuId
	}
	return ""
}

type GetSongByMenuIdResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Songs                []*ListeningSong `protobuf:"bytes,2,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetSongByMenuIdResp) Reset()         { *m = GetSongByMenuIdResp{} }
func (m *GetSongByMenuIdResp) String() string { return proto.CompactTextString(m) }
func (*GetSongByMenuIdResp) ProtoMessage()    {}
func (*GetSongByMenuIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{41}
}
func (m *GetSongByMenuIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongByMenuIdResp.Unmarshal(m, b)
}
func (m *GetSongByMenuIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongByMenuIdResp.Marshal(b, m, deterministic)
}
func (dst *GetSongByMenuIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongByMenuIdResp.Merge(dst, src)
}
func (m *GetSongByMenuIdResp) XXX_Size() int {
	return xxx_messageInfo_GetSongByMenuIdResp.Size(m)
}
func (m *GetSongByMenuIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongByMenuIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongByMenuIdResp proto.InternalMessageInfo

func (m *GetSongByMenuIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetSongByMenuIdResp) GetSongs() []*ListeningSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

type ListeningSong struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Singer               string   `protobuf:"bytes,3,opt,name=singer,proto3" json:"singer,omitempty"`
	MusicType            uint32   `protobuf:"varint,4,opt,name=music_type,json=musicType,proto3" json:"music_type,omitempty"`
	File                 string   `protobuf:"bytes,5,opt,name=file,proto3" json:"file,omitempty"`
	FileSize             string   `protobuf:"bytes,6,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileType             string   `protobuf:"bytes,7,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileHash             string   `protobuf:"bytes,8,opt,name=file_hash,json=fileHash,proto3" json:"file_hash,omitempty"`
	Uid                  uint32   `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,10,opt,name=nickname,proto3" json:"nickname,omitempty"`
	CreatedAt            uint64   `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Status               uint32   `protobuf:"varint,12,opt,name=status,proto3" json:"status,omitempty"`
	Account              string   `protobuf:"bytes,13,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningSong) Reset()         { *m = ListeningSong{} }
func (m *ListeningSong) String() string { return proto.CompactTextString(m) }
func (*ListeningSong) ProtoMessage()    {}
func (*ListeningSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{42}
}
func (m *ListeningSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningSong.Unmarshal(m, b)
}
func (m *ListeningSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningSong.Marshal(b, m, deterministic)
}
func (dst *ListeningSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningSong.Merge(dst, src)
}
func (m *ListeningSong) XXX_Size() int {
	return xxx_messageInfo_ListeningSong.Size(m)
}
func (m *ListeningSong) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningSong.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningSong proto.InternalMessageInfo

func (m *ListeningSong) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListeningSong) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListeningSong) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *ListeningSong) GetMusicType() uint32 {
	if m != nil {
		return m.MusicType
	}
	return 0
}

func (m *ListeningSong) GetFile() string {
	if m != nil {
		return m.File
	}
	return ""
}

func (m *ListeningSong) GetFileSize() string {
	if m != nil {
		return m.FileSize
	}
	return ""
}

func (m *ListeningSong) GetFileType() string {
	if m != nil {
		return m.FileType
	}
	return ""
}

func (m *ListeningSong) GetFileHash() string {
	if m != nil {
		return m.FileHash
	}
	return ""
}

func (m *ListeningSong) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListeningSong) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ListeningSong) GetCreatedAt() uint64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *ListeningSong) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ListeningSong) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type SharedPageNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Bg                   string   `protobuf:"bytes,2,opt,name=bg,proto3" json:"bg,omitempty"`
	Text                 string   `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	Date                 uint32   `protobuf:"varint,4,opt,name=date,proto3" json:"date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SharedPageNotify) Reset()         { *m = SharedPageNotify{} }
func (m *SharedPageNotify) String() string { return proto.CompactTextString(m) }
func (*SharedPageNotify) ProtoMessage()    {}
func (*SharedPageNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{43}
}
func (m *SharedPageNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SharedPageNotify.Unmarshal(m, b)
}
func (m *SharedPageNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SharedPageNotify.Marshal(b, m, deterministic)
}
func (dst *SharedPageNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SharedPageNotify.Merge(dst, src)
}
func (m *SharedPageNotify) XXX_Size() int {
	return xxx_messageInfo_SharedPageNotify.Size(m)
}
func (m *SharedPageNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SharedPageNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SharedPageNotify proto.InternalMessageInfo

func (m *SharedPageNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SharedPageNotify) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *SharedPageNotify) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *SharedPageNotify) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

type ListeningMood struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Text                 string   `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	Lottie1Url           string   `protobuf:"bytes,4,opt,name=lottie1_url,json=lottie1Url,proto3" json:"lottie1_url,omitempty"`
	Lottie1Md5           string   `protobuf:"bytes,5,opt,name=lottie1_md5,json=lottie1Md5,proto3" json:"lottie1_md5,omitempty"`
	Lottie2Url           string   `protobuf:"bytes,6,opt,name=lottie2_url,json=lottie2Url,proto3" json:"lottie2_url,omitempty"`
	Lottie2Md5           string   `protobuf:"bytes,7,opt,name=lottie2_md5,json=lottie2Md5,proto3" json:"lottie2_md5,omitempty"`
	LottieCompleteUrl    string   `protobuf:"bytes,8,opt,name=lottie_complete_url,json=lottieCompleteUrl,proto3" json:"lottie_complete_url,omitempty"`
	LottieCompleteMd5    string   `protobuf:"bytes,9,opt,name=lottie_complete_md5,json=lottieCompleteMd5,proto3" json:"lottie_complete_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListeningMood) Reset()         { *m = ListeningMood{} }
func (m *ListeningMood) String() string { return proto.CompactTextString(m) }
func (*ListeningMood) ProtoMessage()    {}
func (*ListeningMood) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{44}
}
func (m *ListeningMood) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningMood.Unmarshal(m, b)
}
func (m *ListeningMood) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningMood.Marshal(b, m, deterministic)
}
func (dst *ListeningMood) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningMood.Merge(dst, src)
}
func (m *ListeningMood) XXX_Size() int {
	return xxx_messageInfo_ListeningMood.Size(m)
}
func (m *ListeningMood) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningMood.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningMood proto.InternalMessageInfo

func (m *ListeningMood) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListeningMood) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *ListeningMood) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *ListeningMood) GetLottie1Url() string {
	if m != nil {
		return m.Lottie1Url
	}
	return ""
}

func (m *ListeningMood) GetLottie1Md5() string {
	if m != nil {
		return m.Lottie1Md5
	}
	return ""
}

func (m *ListeningMood) GetLottie2Url() string {
	if m != nil {
		return m.Lottie2Url
	}
	return ""
}

func (m *ListeningMood) GetLottie2Md5() string {
	if m != nil {
		return m.Lottie2Md5
	}
	return ""
}

func (m *ListeningMood) GetLottieCompleteUrl() string {
	if m != nil {
		return m.LottieCompleteUrl
	}
	return ""
}

func (m *ListeningMood) GetLottieCompleteMd5() string {
	if m != nil {
		return m.LottieCompleteMd5
	}
	return ""
}

type GetAllMoodCfgReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllMoodCfgReq) Reset()         { *m = GetAllMoodCfgReq{} }
func (m *GetAllMoodCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetAllMoodCfgReq) ProtoMessage()    {}
func (*GetAllMoodCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{45}
}
func (m *GetAllMoodCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMoodCfgReq.Unmarshal(m, b)
}
func (m *GetAllMoodCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMoodCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetAllMoodCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMoodCfgReq.Merge(dst, src)
}
func (m *GetAllMoodCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetAllMoodCfgReq.Size(m)
}
func (m *GetAllMoodCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMoodCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMoodCfgReq proto.InternalMessageInfo

func (m *GetAllMoodCfgReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetAllMoodCfgResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Moods                []*ListeningMood `protobuf:"bytes,2,rep,name=moods,proto3" json:"moods,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAllMoodCfgResp) Reset()         { *m = GetAllMoodCfgResp{} }
func (m *GetAllMoodCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetAllMoodCfgResp) ProtoMessage()    {}
func (*GetAllMoodCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{46}
}
func (m *GetAllMoodCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllMoodCfgResp.Unmarshal(m, b)
}
func (m *GetAllMoodCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllMoodCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetAllMoodCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllMoodCfgResp.Merge(dst, src)
}
func (m *GetAllMoodCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetAllMoodCfgResp.Size(m)
}
func (m *GetAllMoodCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllMoodCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllMoodCfgResp proto.InternalMessageInfo

func (m *GetAllMoodCfgResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllMoodCfgResp) GetMoods() []*ListeningMood {
	if m != nil {
		return m.Moods
	}
	return nil
}

type SetListeningUserMoodReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MoodId               string       `protobuf:"bytes,3,opt,name=mood_id,json=moodId,proto3" json:"mood_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetListeningUserMoodReq) Reset()         { *m = SetListeningUserMoodReq{} }
func (m *SetListeningUserMoodReq) String() string { return proto.CompactTextString(m) }
func (*SetListeningUserMoodReq) ProtoMessage()    {}
func (*SetListeningUserMoodReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{47}
}
func (m *SetListeningUserMoodReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetListeningUserMoodReq.Unmarshal(m, b)
}
func (m *SetListeningUserMoodReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetListeningUserMoodReq.Marshal(b, m, deterministic)
}
func (dst *SetListeningUserMoodReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetListeningUserMoodReq.Merge(dst, src)
}
func (m *SetListeningUserMoodReq) XXX_Size() int {
	return xxx_messageInfo_SetListeningUserMoodReq.Size(m)
}
func (m *SetListeningUserMoodReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetListeningUserMoodReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetListeningUserMoodReq proto.InternalMessageInfo

func (m *SetListeningUserMoodReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetListeningUserMoodReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetListeningUserMoodReq) GetMoodId() string {
	if m != nil {
		return m.MoodId
	}
	return ""
}

type SetListeningUserMoodResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetListeningUserMoodResp) Reset()         { *m = SetListeningUserMoodResp{} }
func (m *SetListeningUserMoodResp) String() string { return proto.CompactTextString(m) }
func (*SetListeningUserMoodResp) ProtoMessage()    {}
func (*SetListeningUserMoodResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{48}
}
func (m *SetListeningUserMoodResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetListeningUserMoodResp.Unmarshal(m, b)
}
func (m *SetListeningUserMoodResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetListeningUserMoodResp.Marshal(b, m, deterministic)
}
func (dst *SetListeningUserMoodResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetListeningUserMoodResp.Merge(dst, src)
}
func (m *SetListeningUserMoodResp) XXX_Size() int {
	return xxx_messageInfo_SetListeningUserMoodResp.Size(m)
}
func (m *SetListeningUserMoodResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetListeningUserMoodResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetListeningUserMoodResp proto.InternalMessageInfo

func (m *SetListeningUserMoodResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetAllListeningOnMicUserMoodReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllListeningOnMicUserMoodReq) Reset()         { *m = GetAllListeningOnMicUserMoodReq{} }
func (m *GetAllListeningOnMicUserMoodReq) String() string { return proto.CompactTextString(m) }
func (*GetAllListeningOnMicUserMoodReq) ProtoMessage()    {}
func (*GetAllListeningOnMicUserMoodReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{49}
}
func (m *GetAllListeningOnMicUserMoodReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodReq.Unmarshal(m, b)
}
func (m *GetAllListeningOnMicUserMoodReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodReq.Marshal(b, m, deterministic)
}
func (dst *GetAllListeningOnMicUserMoodReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllListeningOnMicUserMoodReq.Merge(dst, src)
}
func (m *GetAllListeningOnMicUserMoodReq) XXX_Size() int {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodReq.Size(m)
}
func (m *GetAllListeningOnMicUserMoodReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllListeningOnMicUserMoodReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllListeningOnMicUserMoodReq proto.InternalMessageInfo

func (m *GetAllListeningOnMicUserMoodReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAllListeningOnMicUserMoodReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetAllListeningOnMicUserMoodResp struct {
	BaseResp             *app.BaseResp             `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserMood             map[uint32]*ListeningMood `protobuf:"bytes,2,rep,name=user_mood,json=userMood,proto3" json:"user_mood,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetAllListeningOnMicUserMoodResp) Reset()         { *m = GetAllListeningOnMicUserMoodResp{} }
func (m *GetAllListeningOnMicUserMoodResp) String() string { return proto.CompactTextString(m) }
func (*GetAllListeningOnMicUserMoodResp) ProtoMessage()    {}
func (*GetAllListeningOnMicUserMoodResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{50}
}
func (m *GetAllListeningOnMicUserMoodResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodResp.Unmarshal(m, b)
}
func (m *GetAllListeningOnMicUserMoodResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodResp.Marshal(b, m, deterministic)
}
func (dst *GetAllListeningOnMicUserMoodResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllListeningOnMicUserMoodResp.Merge(dst, src)
}
func (m *GetAllListeningOnMicUserMoodResp) XXX_Size() int {
	return xxx_messageInfo_GetAllListeningOnMicUserMoodResp.Size(m)
}
func (m *GetAllListeningOnMicUserMoodResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllListeningOnMicUserMoodResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllListeningOnMicUserMoodResp proto.InternalMessageInfo

func (m *GetAllListeningOnMicUserMoodResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAllListeningOnMicUserMoodResp) GetUserMood() map[uint32]*ListeningMood {
	if m != nil {
		return m.UserMood
	}
	return nil
}

// 用户上麦/更新状态时推送
type ListeningMoodNotify struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Mood                 *ListeningMood `protobuf:"bytes,2,opt,name=mood,proto3" json:"mood,omitempty"`
	ChannelId            uint32         `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ListeningMoodNotify) Reset()         { *m = ListeningMoodNotify{} }
func (m *ListeningMoodNotify) String() string { return proto.CompactTextString(m) }
func (*ListeningMoodNotify) ProtoMessage()    {}
func (*ListeningMoodNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{51}
}
func (m *ListeningMoodNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningMoodNotify.Unmarshal(m, b)
}
func (m *ListeningMoodNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningMoodNotify.Marshal(b, m, deterministic)
}
func (dst *ListeningMoodNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningMoodNotify.Merge(dst, src)
}
func (m *ListeningMoodNotify) XXX_Size() int {
	return xxx_messageInfo_ListeningMoodNotify.Size(m)
}
func (m *ListeningMoodNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningMoodNotify.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningMoodNotify proto.InternalMessageInfo

func (m *ListeningMoodNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListeningMoodNotify) GetMood() *ListeningMood {
	if m != nil {
		return m.Mood
	}
	return nil
}

func (m *ListeningMoodNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ListeningCheckInV3Req struct {
	BaseReq              *app.BaseReq   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32         `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ConstellationChoose  *Constellation `protobuf:"bytes,3,opt,name=constellation_choose,json=constellationChoose,proto3" json:"constellation_choose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ListeningCheckInV3Req) Reset()         { *m = ListeningCheckInV3Req{} }
func (m *ListeningCheckInV3Req) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInV3Req) ProtoMessage()    {}
func (*ListeningCheckInV3Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{52}
}
func (m *ListeningCheckInV3Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInV3Req.Unmarshal(m, b)
}
func (m *ListeningCheckInV3Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInV3Req.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInV3Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInV3Req.Merge(dst, src)
}
func (m *ListeningCheckInV3Req) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInV3Req.Size(m)
}
func (m *ListeningCheckInV3Req) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInV3Req.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInV3Req proto.InternalMessageInfo

func (m *ListeningCheckInV3Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningCheckInV3Req) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningCheckInV3Req) GetConstellationChoose() *Constellation {
	if m != nil {
		return m.ConstellationChoose
	}
	return nil
}

type Constellation struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Constellation) Reset()         { *m = Constellation{} }
func (m *Constellation) String() string { return proto.CompactTextString(m) }
func (*Constellation) ProtoMessage()    {}
func (*Constellation) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{53}
}
func (m *Constellation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Constellation.Unmarshal(m, b)
}
func (m *Constellation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Constellation.Marshal(b, m, deterministic)
}
func (dst *Constellation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Constellation.Merge(dst, src)
}
func (m *Constellation) XXX_Size() int {
	return xxx_messageInfo_Constellation.Size(m)
}
func (m *Constellation) XXX_DiscardUnknown() {
	xxx_messageInfo_Constellation.DiscardUnknown(m)
}

var xxx_messageInfo_Constellation proto.InternalMessageInfo

func (m *Constellation) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Constellation) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type ListeningCheckInV3Resp struct {
	BaseResp             *app.BaseResp                `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Type                 ChannelListeningCheckInType  `protobuf:"varint,2,opt,name=type,proto3,enum=ga.channellisteninglogic.ChannelListeningCheckInType" json:"type,omitempty"`
	CheckInInfo          *ChannelListeningCheckInInfo `protobuf:"bytes,3,opt,name=check_in_info,json=checkInInfo,proto3" json:"check_in_info,omitempty"`
	ShareCard            *ChannelListeningShareCardV3 `protobuf:"bytes,4,opt,name=share_card,json=shareCard,proto3" json:"share_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ListeningCheckInV3Resp) Reset()         { *m = ListeningCheckInV3Resp{} }
func (m *ListeningCheckInV3Resp) String() string { return proto.CompactTextString(m) }
func (*ListeningCheckInV3Resp) ProtoMessage()    {}
func (*ListeningCheckInV3Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{54}
}
func (m *ListeningCheckInV3Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningCheckInV3Resp.Unmarshal(m, b)
}
func (m *ListeningCheckInV3Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningCheckInV3Resp.Marshal(b, m, deterministic)
}
func (dst *ListeningCheckInV3Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningCheckInV3Resp.Merge(dst, src)
}
func (m *ListeningCheckInV3Resp) XXX_Size() int {
	return xxx_messageInfo_ListeningCheckInV3Resp.Size(m)
}
func (m *ListeningCheckInV3Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningCheckInV3Resp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningCheckInV3Resp proto.InternalMessageInfo

func (m *ListeningCheckInV3Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningCheckInV3Resp) GetType() ChannelListeningCheckInType {
	if m != nil {
		return m.Type
	}
	return ChannelListeningCheckInType_ChannelListeningCheckInType_UNDEFINED
}

func (m *ListeningCheckInV3Resp) GetCheckInInfo() *ChannelListeningCheckInInfo {
	if m != nil {
		return m.CheckInInfo
	}
	return nil
}

func (m *ListeningCheckInV3Resp) GetShareCard() *ChannelListeningShareCardV3 {
	if m != nil {
		return m.ShareCard
	}
	return nil
}

type ListeningDropCardV3Req struct {
	BaseReq              *app.BaseReq   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32         `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ConstellationChoose  *Constellation `protobuf:"bytes,3,opt,name=constellation_choose,json=constellationChoose,proto3" json:"constellation_choose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ListeningDropCardV3Req) Reset()         { *m = ListeningDropCardV3Req{} }
func (m *ListeningDropCardV3Req) String() string { return proto.CompactTextString(m) }
func (*ListeningDropCardV3Req) ProtoMessage()    {}
func (*ListeningDropCardV3Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{55}
}
func (m *ListeningDropCardV3Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningDropCardV3Req.Unmarshal(m, b)
}
func (m *ListeningDropCardV3Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningDropCardV3Req.Marshal(b, m, deterministic)
}
func (dst *ListeningDropCardV3Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningDropCardV3Req.Merge(dst, src)
}
func (m *ListeningDropCardV3Req) XXX_Size() int {
	return xxx_messageInfo_ListeningDropCardV3Req.Size(m)
}
func (m *ListeningDropCardV3Req) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningDropCardV3Req.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningDropCardV3Req proto.InternalMessageInfo

func (m *ListeningDropCardV3Req) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListeningDropCardV3Req) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ListeningDropCardV3Req) GetConstellationChoose() *Constellation {
	if m != nil {
		return m.ConstellationChoose
	}
	return nil
}

type ListeningDropCardV3Resp struct {
	BaseResp             *app.BaseResp                `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ShareCard            *ChannelListeningShareCardV3 `protobuf:"bytes,2,opt,name=share_card,json=shareCard,proto3" json:"share_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ListeningDropCardV3Resp) Reset()         { *m = ListeningDropCardV3Resp{} }
func (m *ListeningDropCardV3Resp) String() string { return proto.CompactTextString(m) }
func (*ListeningDropCardV3Resp) ProtoMessage()    {}
func (*ListeningDropCardV3Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{56}
}
func (m *ListeningDropCardV3Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListeningDropCardV3Resp.Unmarshal(m, b)
}
func (m *ListeningDropCardV3Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListeningDropCardV3Resp.Marshal(b, m, deterministic)
}
func (dst *ListeningDropCardV3Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListeningDropCardV3Resp.Merge(dst, src)
}
func (m *ListeningDropCardV3Resp) XXX_Size() int {
	return xxx_messageInfo_ListeningDropCardV3Resp.Size(m)
}
func (m *ListeningDropCardV3Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListeningDropCardV3Resp.DiscardUnknown(m)
}

var xxx_messageInfo_ListeningDropCardV3Resp proto.InternalMessageInfo

func (m *ListeningDropCardV3Resp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListeningDropCardV3Resp) GetShareCard() *ChannelListeningShareCardV3 {
	if m != nil {
		return m.ShareCard
	}
	return nil
}

type ChannelListeningShareCardV3 struct {
	Account              string                            `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string                            `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	ShowTime             int64                             `protobuf:"varint,3,opt,name=show_time,json=showTime,proto3" json:"show_time,omitempty"`
	DisplayId            uint32                            `protobuf:"varint,4,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelId            uint32                            `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string                            `protobuf:"bytes,6,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	CardImg              string                            `protobuf:"bytes,7,opt,name=card_img,json=cardImg,proto3" json:"card_img,omitempty"`
	BgImg                string                            `protobuf:"bytes,8,opt,name=bg_img,json=bgImg,proto3" json:"bg_img,omitempty"`
	BtDesc               string                            `protobuf:"bytes,9,opt,name=bt_desc,json=btDesc,proto3" json:"bt_desc,omitempty"`
	WebTitle             string                            `protobuf:"bytes,10,opt,name=web_title,json=webTitle,proto3" json:"web_title,omitempty"`
	Fortune              string                            `protobuf:"bytes,11,opt,name=fortune,proto3" json:"fortune,omitempty"`
	MatchConstellation   *Constellation                    `protobuf:"bytes,12,opt,name=match_constellation,json=matchConstellation,proto3" json:"match_constellation,omitempty"`
	ChooseConstellation  *Constellation                    `protobuf:"bytes,13,opt,name=choose_constellation,json=chooseConstellation,proto3" json:"choose_constellation,omitempty"`
	ShareInfo            *ChannelListeningCheckInShareInfo `protobuf:"bytes,14,opt,name=share_info,json=shareInfo,proto3" json:"share_info,omitempty"`
	ChannelViewId        string                            `protobuf:"bytes,15,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ChannelListeningShareCardV3) Reset()         { *m = ChannelListeningShareCardV3{} }
func (m *ChannelListeningShareCardV3) String() string { return proto.CompactTextString(m) }
func (*ChannelListeningShareCardV3) ProtoMessage()    {}
func (*ChannelListeningShareCardV3) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{57}
}
func (m *ChannelListeningShareCardV3) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelListeningShareCardV3.Unmarshal(m, b)
}
func (m *ChannelListeningShareCardV3) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelListeningShareCardV3.Marshal(b, m, deterministic)
}
func (dst *ChannelListeningShareCardV3) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelListeningShareCardV3.Merge(dst, src)
}
func (m *ChannelListeningShareCardV3) XXX_Size() int {
	return xxx_messageInfo_ChannelListeningShareCardV3.Size(m)
}
func (m *ChannelListeningShareCardV3) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelListeningShareCardV3.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelListeningShareCardV3 proto.InternalMessageInfo

func (m *ChannelListeningShareCardV3) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetShowTime() int64 {
	if m != nil {
		return m.ShowTime
	}
	return 0
}

func (m *ChannelListeningShareCardV3) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *ChannelListeningShareCardV3) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelListeningShareCardV3) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetCardImg() string {
	if m != nil {
		return m.CardImg
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetBgImg() string {
	if m != nil {
		return m.BgImg
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetBtDesc() string {
	if m != nil {
		return m.BtDesc
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetWebTitle() string {
	if m != nil {
		return m.WebTitle
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetFortune() string {
	if m != nil {
		return m.Fortune
	}
	return ""
}

func (m *ChannelListeningShareCardV3) GetMatchConstellation() *Constellation {
	if m != nil {
		return m.MatchConstellation
	}
	return nil
}

func (m *ChannelListeningShareCardV3) GetChooseConstellation() *Constellation {
	if m != nil {
		return m.ChooseConstellation
	}
	return nil
}

func (m *ChannelListeningShareCardV3) GetShareInfo() *ChannelListeningCheckInShareInfo {
	if m != nil {
		return m.ShareInfo
	}
	return nil
}

func (m *ChannelListeningShareCardV3) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type SetConstellationReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ConstellationChoose  int32        `protobuf:"varint,2,opt,name=constellation_choose,json=constellationChoose,proto3" json:"constellation_choose,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetConstellationReq) Reset()         { *m = SetConstellationReq{} }
func (m *SetConstellationReq) String() string { return proto.CompactTextString(m) }
func (*SetConstellationReq) ProtoMessage()    {}
func (*SetConstellationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{58}
}
func (m *SetConstellationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetConstellationReq.Unmarshal(m, b)
}
func (m *SetConstellationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetConstellationReq.Marshal(b, m, deterministic)
}
func (dst *SetConstellationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetConstellationReq.Merge(dst, src)
}
func (m *SetConstellationReq) XXX_Size() int {
	return xxx_messageInfo_SetConstellationReq.Size(m)
}
func (m *SetConstellationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetConstellationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetConstellationReq proto.InternalMessageInfo

func (m *SetConstellationReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetConstellationReq) GetConstellationChoose() int32 {
	if m != nil {
		return m.ConstellationChoose
	}
	return 0
}

type SetConstellationResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetConstellationResp) Reset()         { *m = SetConstellationResp{} }
func (m *SetConstellationResp) String() string { return proto.CompactTextString(m) }
func (*SetConstellationResp) ProtoMessage()    {}
func (*SetConstellationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{59}
}
func (m *SetConstellationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetConstellationResp.Unmarshal(m, b)
}
func (m *SetConstellationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetConstellationResp.Marshal(b, m, deterministic)
}
func (dst *SetConstellationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetConstellationResp.Merge(dst, src)
}
func (m *SetConstellationResp) XXX_Size() int {
	return xxx_messageInfo_SetConstellationResp.Size(m)
}
func (m *SetConstellationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetConstellationResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetConstellationResp proto.InternalMessageInfo

func (m *SetConstellationResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ConstellationReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ConstellationReq) Reset()         { *m = ConstellationReq{} }
func (m *ConstellationReq) String() string { return proto.CompactTextString(m) }
func (*ConstellationReq) ProtoMessage()    {}
func (*ConstellationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{60}
}
func (m *ConstellationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConstellationReq.Unmarshal(m, b)
}
func (m *ConstellationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConstellationReq.Marshal(b, m, deterministic)
}
func (dst *ConstellationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConstellationReq.Merge(dst, src)
}
func (m *ConstellationReq) XXX_Size() int {
	return xxx_messageInfo_ConstellationReq.Size(m)
}
func (m *ConstellationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConstellationReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConstellationReq proto.InternalMessageInfo

func (m *ConstellationReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ConstellationResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ConstellationChoose  *Constellation   `protobuf:"bytes,2,opt,name=constellation_choose,json=constellationChoose,proto3" json:"constellation_choose,omitempty"`
	Constellations       []*Constellation `protobuf:"bytes,3,rep,name=constellations,proto3" json:"constellations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ConstellationResp) Reset()         { *m = ConstellationResp{} }
func (m *ConstellationResp) String() string { return proto.CompactTextString(m) }
func (*ConstellationResp) ProtoMessage()    {}
func (*ConstellationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{61}
}
func (m *ConstellationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConstellationResp.Unmarshal(m, b)
}
func (m *ConstellationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConstellationResp.Marshal(b, m, deterministic)
}
func (dst *ConstellationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConstellationResp.Merge(dst, src)
}
func (m *ConstellationResp) XXX_Size() int {
	return xxx_messageInfo_ConstellationResp.Size(m)
}
func (m *ConstellationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConstellationResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConstellationResp proto.InternalMessageInfo

func (m *ConstellationResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ConstellationResp) GetConstellationChoose() *Constellation {
	if m != nil {
		return m.ConstellationChoose
	}
	return nil
}

func (m *ConstellationResp) GetConstellations() []*Constellation {
	if m != nil {
		return m.Constellations
	}
	return nil
}

type GetListeningCheckInShareCardV3Req struct {
	ShareId              string   `protobuf:"bytes,1,opt,name=share_id,json=shareId,proto3" json:"share_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetListeningCheckInShareCardV3Req) Reset()         { *m = GetListeningCheckInShareCardV3Req{} }
func (m *GetListeningCheckInShareCardV3Req) String() string { return proto.CompactTextString(m) }
func (*GetListeningCheckInShareCardV3Req) ProtoMessage()    {}
func (*GetListeningCheckInShareCardV3Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{62}
}
func (m *GetListeningCheckInShareCardV3Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Req.Unmarshal(m, b)
}
func (m *GetListeningCheckInShareCardV3Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Req.Marshal(b, m, deterministic)
}
func (dst *GetListeningCheckInShareCardV3Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListeningCheckInShareCardV3Req.Merge(dst, src)
}
func (m *GetListeningCheckInShareCardV3Req) XXX_Size() int {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Req.Size(m)
}
func (m *GetListeningCheckInShareCardV3Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListeningCheckInShareCardV3Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetListeningCheckInShareCardV3Req proto.InternalMessageInfo

func (m *GetListeningCheckInShareCardV3Req) GetShareId() string {
	if m != nil {
		return m.ShareId
	}
	return ""
}

type GetListeningCheckInShareCardV3Resp struct {
	ShareCard            *ChannelListeningShareCardV3 `protobuf:"bytes,1,opt,name=share_card,json=shareCard,proto3" json:"share_card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetListeningCheckInShareCardV3Resp) Reset()         { *m = GetListeningCheckInShareCardV3Resp{} }
func (m *GetListeningCheckInShareCardV3Resp) String() string { return proto.CompactTextString(m) }
func (*GetListeningCheckInShareCardV3Resp) ProtoMessage()    {}
func (*GetListeningCheckInShareCardV3Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{63}
}
func (m *GetListeningCheckInShareCardV3Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Resp.Unmarshal(m, b)
}
func (m *GetListeningCheckInShareCardV3Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Resp.Marshal(b, m, deterministic)
}
func (dst *GetListeningCheckInShareCardV3Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetListeningCheckInShareCardV3Resp.Merge(dst, src)
}
func (m *GetListeningCheckInShareCardV3Resp) XXX_Size() int {
	return xxx_messageInfo_GetListeningCheckInShareCardV3Resp.Size(m)
}
func (m *GetListeningCheckInShareCardV3Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetListeningCheckInShareCardV3Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetListeningCheckInShareCardV3Resp proto.InternalMessageInfo

func (m *GetListeningCheckInShareCardV3Resp) GetShareCard() *ChannelListeningShareCardV3 {
	if m != nil {
		return m.ShareCard
	}
	return nil
}

// 检查房间玩法是否是挂房听歌
type CheckListeningChannelTabReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckListeningChannelTabReq) Reset()         { *m = CheckListeningChannelTabReq{} }
func (m *CheckListeningChannelTabReq) String() string { return proto.CompactTextString(m) }
func (*CheckListeningChannelTabReq) ProtoMessage()    {}
func (*CheckListeningChannelTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{64}
}
func (m *CheckListeningChannelTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckListeningChannelTabReq.Unmarshal(m, b)
}
func (m *CheckListeningChannelTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckListeningChannelTabReq.Marshal(b, m, deterministic)
}
func (dst *CheckListeningChannelTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckListeningChannelTabReq.Merge(dst, src)
}
func (m *CheckListeningChannelTabReq) XXX_Size() int {
	return xxx_messageInfo_CheckListeningChannelTabReq.Size(m)
}
func (m *CheckListeningChannelTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckListeningChannelTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckListeningChannelTabReq proto.InternalMessageInfo

func (m *CheckListeningChannelTabReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckListeningChannelTabReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckListeningChannelTabResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsListeningChannel   bool          `protobuf:"varint,2,opt,name=is_listening_channel,json=isListeningChannel,proto3" json:"is_listening_channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckListeningChannelTabResp) Reset()         { *m = CheckListeningChannelTabResp{} }
func (m *CheckListeningChannelTabResp) String() string { return proto.CompactTextString(m) }
func (*CheckListeningChannelTabResp) ProtoMessage()    {}
func (*CheckListeningChannelTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1, []int{65}
}
func (m *CheckListeningChannelTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckListeningChannelTabResp.Unmarshal(m, b)
}
func (m *CheckListeningChannelTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckListeningChannelTabResp.Marshal(b, m, deterministic)
}
func (dst *CheckListeningChannelTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckListeningChannelTabResp.Merge(dst, src)
}
func (m *CheckListeningChannelTabResp) XXX_Size() int {
	return xxx_messageInfo_CheckListeningChannelTabResp.Size(m)
}
func (m *CheckListeningChannelTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckListeningChannelTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckListeningChannelTabResp proto.InternalMessageInfo

func (m *CheckListeningChannelTabResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckListeningChannelTabResp) GetIsListeningChannel() bool {
	if m != nil {
		return m.IsListeningChannel
	}
	return false
}

func init() {
	proto.RegisterType((*GetChannelListeningSimpleInfoReq)(nil), "ga.channellisteninglogic.GetChannelListeningSimpleInfoReq")
	proto.RegisterType((*GetChannelListeningSimpleInfoResp)(nil), "ga.channellisteninglogic.GetChannelListeningSimpleInfoResp")
	proto.RegisterType((*ChannelListeningCheckInInfo)(nil), "ga.channellisteninglogic.ChannelListeningCheckInInfo")
	proto.RegisterType((*LikeSongReq)(nil), "ga.channellisteninglogic.LikeSongReq")
	proto.RegisterType((*LikeSongResp)(nil), "ga.channellisteninglogic.LikeSongResp")
	proto.RegisterType((*ChannelListeningSong)(nil), "ga.channellisteninglogic.ChannelListeningSong")
	proto.RegisterType((*LikeSongNotify)(nil), "ga.channellisteninglogic.LikeSongNotify")
	proto.RegisterType((*GetBeLikedSongListReq)(nil), "ga.channellisteninglogic.GetBeLikedSongListReq")
	proto.RegisterType((*GetBeLikedSongListResp)(nil), "ga.channellisteninglogic.GetBeLikedSongListResp")
	proto.RegisterType((*BeLikedSong)(nil), "ga.channellisteninglogic.BeLikedSong")
	proto.RegisterType((*GetLikedSongListReq)(nil), "ga.channellisteninglogic.GetLikedSongListReq")
	proto.RegisterType((*GetLikedSongListResp)(nil), "ga.channellisteninglogic.GetLikedSongListResp")
	proto.RegisterType((*GetUserFlowerListReq)(nil), "ga.channellisteninglogic.GetUserFlowerListReq")
	proto.RegisterType((*GetUserFlowerListResp)(nil), "ga.channellisteninglogic.GetUserFlowerListResp")
	proto.RegisterType((*UserFlower)(nil), "ga.channellisteninglogic.UserFlower")
	proto.RegisterType((*GetFlowerDetailReq)(nil), "ga.channellisteninglogic.GetFlowerDetailReq")
	proto.RegisterType((*GetFlowerDetailResp)(nil), "ga.channellisteninglogic.GetFlowerDetailResp")
	proto.RegisterType((*FlowerDetail)(nil), "ga.channellisteninglogic.FlowerDetail")
	proto.RegisterType((*ChannelListeningLoadMore)(nil), "ga.channellisteninglogic.ChannelListeningLoadMore")
	proto.RegisterType((*FlowerAwardMsg)(nil), "ga.channellisteninglogic.FlowerAwardMsg")
	proto.RegisterType((*FlowerNotify)(nil), "ga.channellisteninglogic.FlowerNotify")
	proto.RegisterType((*FlowerToPostNotify)(nil), "ga.channellisteninglogic.FlowerToPostNotify")
	proto.RegisterType((*ChannelListenTheme)(nil), "ga.channellisteninglogic.ChannelListenTheme")
	proto.RegisterType((*ChannelListenThemeNotify)(nil), "ga.channellisteninglogic.ChannelListenThemeNotify")
	proto.RegisterType((*SetListeningCheckInTopicReq)(nil), "ga.channellisteninglogic.SetListeningCheckInTopicReq")
	proto.RegisterType((*SetListeningCheckInTopicResp)(nil), "ga.channellisteninglogic.SetListeningCheckInTopicResp")
	proto.RegisterType((*ChannelListeningTopicChangeNotify)(nil), "ga.channellisteninglogic.ChannelListeningTopicChangeNotify")
	proto.RegisterType((*ListeningCheckInReq)(nil), "ga.channellisteninglogic.ListeningCheckInReq")
	proto.RegisterType((*ListeningCheckInResp)(nil), "ga.channellisteninglogic.ListeningCheckInResp")
	proto.RegisterType((*GetListeningUserCheckInInfoListReq)(nil), "ga.channellisteninglogic.GetListeningUserCheckInInfoListReq")
	proto.RegisterType((*GetListeningUserCheckInInfoListResp)(nil), "ga.channellisteninglogic.GetListeningUserCheckInInfoListResp")
	proto.RegisterType((*ChannelListeningCheckInShareCard)(nil), "ga.channellisteninglogic.ChannelListeningCheckInShareCard")
	proto.RegisterType((*ChannelListeningCheckInShareInfo)(nil), "ga.channellisteninglogic.ChannelListeningCheckInShareInfo")
	proto.RegisterType((*UserCheckInInfo)(nil), "ga.channellisteninglogic.UserCheckInInfo")
	proto.RegisterType((*ListeningCheckInSharedReq)(nil), "ga.channellisteninglogic.ListeningCheckInSharedReq")
	proto.RegisterType((*ListeningCheckInSharedResp)(nil), "ga.channellisteninglogic.ListeningCheckInSharedResp")
	proto.RegisterType((*CheckInNotify)(nil), "ga.channellisteninglogic.CheckInNotify")
	proto.RegisterType((*GetRcmdSongMenuReq)(nil), "ga.channellisteninglogic.GetRcmdSongMenuReq")
	proto.RegisterType((*GetRcmdSongMenuResp)(nil), "ga.channellisteninglogic.GetRcmdSongMenuResp")
	proto.RegisterType((*ListeningSongMenu)(nil), "ga.channellisteninglogic.ListeningSongMenu")
	proto.RegisterType((*GetSongByMenuIdReq)(nil), "ga.channellisteninglogic.GetSongByMenuIdReq")
	proto.RegisterType((*GetSongByMenuIdResp)(nil), "ga.channellisteninglogic.GetSongByMenuIdResp")
	proto.RegisterType((*ListeningSong)(nil), "ga.channellisteninglogic.ListeningSong")
	proto.RegisterType((*SharedPageNotify)(nil), "ga.channellisteninglogic.SharedPageNotify")
	proto.RegisterType((*ListeningMood)(nil), "ga.channellisteninglogic.ListeningMood")
	proto.RegisterType((*GetAllMoodCfgReq)(nil), "ga.channellisteninglogic.GetAllMoodCfgReq")
	proto.RegisterType((*GetAllMoodCfgResp)(nil), "ga.channellisteninglogic.GetAllMoodCfgResp")
	proto.RegisterType((*SetListeningUserMoodReq)(nil), "ga.channellisteninglogic.SetListeningUserMoodReq")
	proto.RegisterType((*SetListeningUserMoodResp)(nil), "ga.channellisteninglogic.SetListeningUserMoodResp")
	proto.RegisterType((*GetAllListeningOnMicUserMoodReq)(nil), "ga.channellisteninglogic.GetAllListeningOnMicUserMoodReq")
	proto.RegisterType((*GetAllListeningOnMicUserMoodResp)(nil), "ga.channellisteninglogic.GetAllListeningOnMicUserMoodResp")
	proto.RegisterMapType((map[uint32]*ListeningMood)(nil), "ga.channellisteninglogic.GetAllListeningOnMicUserMoodResp.UserMoodEntry")
	proto.RegisterType((*ListeningMoodNotify)(nil), "ga.channellisteninglogic.ListeningMoodNotify")
	proto.RegisterType((*ListeningCheckInV3Req)(nil), "ga.channellisteninglogic.ListeningCheckInV3Req")
	proto.RegisterType((*Constellation)(nil), "ga.channellisteninglogic.Constellation")
	proto.RegisterType((*ListeningCheckInV3Resp)(nil), "ga.channellisteninglogic.ListeningCheckInV3Resp")
	proto.RegisterType((*ListeningDropCardV3Req)(nil), "ga.channellisteninglogic.ListeningDropCardV3Req")
	proto.RegisterType((*ListeningDropCardV3Resp)(nil), "ga.channellisteninglogic.ListeningDropCardV3Resp")
	proto.RegisterType((*ChannelListeningShareCardV3)(nil), "ga.channellisteninglogic.ChannelListeningShareCardV3")
	proto.RegisterType((*SetConstellationReq)(nil), "ga.channellisteninglogic.SetConstellationReq")
	proto.RegisterType((*SetConstellationResp)(nil), "ga.channellisteninglogic.SetConstellationResp")
	proto.RegisterType((*ConstellationReq)(nil), "ga.channellisteninglogic.ConstellationReq")
	proto.RegisterType((*ConstellationResp)(nil), "ga.channellisteninglogic.ConstellationResp")
	proto.RegisterType((*GetListeningCheckInShareCardV3Req)(nil), "ga.channellisteninglogic.GetListeningCheckInShareCardV3Req")
	proto.RegisterType((*GetListeningCheckInShareCardV3Resp)(nil), "ga.channellisteninglogic.GetListeningCheckInShareCardV3Resp")
	proto.RegisterType((*CheckListeningChannelTabReq)(nil), "ga.channellisteninglogic.CheckListeningChannelTabReq")
	proto.RegisterType((*CheckListeningChannelTabResp)(nil), "ga.channellisteninglogic.CheckListeningChannelTabResp")
	proto.RegisterEnum("ga.channellisteninglogic.SongType", SongType_name, SongType_value)
	proto.RegisterEnum("ga.channellisteninglogic.ChannelListeningCheckInType", ChannelListeningCheckInType_name, ChannelListeningCheckInType_value)
}

func init() {
	proto.RegisterFile("channel-listening-logic_.proto", fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1)
}

var fileDescriptor_channel_listening_logic__2c5d43aaa0c737b1 = []byte{
	// 2905 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3a, 0x4d, 0x73, 0xdb, 0xd6,
	0xb5, 0x01, 0x29, 0x8a, 0xe4, 0xa1, 0x28, 0x53, 0x90, 0x12, 0x33, 0x76, 0xf2, 0x2c, 0x23, 0x89,
	0x3f, 0xf2, 0xc1, 0x3c, 0xcb, 0xe3, 0x37, 0xef, 0x39, 0xaf, 0x99, 0x4a, 0xb2, 0xa2, 0x70, 0xc6,
	0x92, 0x33, 0xa0, 0xec, 0x26, 0x99, 0x8c, 0x31, 0x20, 0x70, 0x05, 0x62, 0x04, 0x02, 0x0c, 0x70,
	0x19, 0x86, 0xee, 0x74, 0xba, 0x68, 0xa7, 0xbb, 0xee, 0x3b, 0xe9, 0xa2, 0xdd, 0x74, 0xd1, 0x45,
	0x37, 0x5d, 0xb6, 0x8b, 0x74, 0x32, 0xed, 0xa2, 0xbb, 0x7e, 0xfd, 0x8b, 0xfe, 0x85, 0x2e, 0x3a,
	0xe7, 0xdc, 0x0b, 0x12, 0x80, 0x48, 0x59, 0x48, 0x98, 0x85, 0xbb, 0xd2, 0xc5, 0xb9, 0xe7, 0xe3,
	0xde, 0x73, 0xce, 0x3d, 0x5f, 0x14, 0xfc, 0x97, 0xd5, 0x33, 0x7d, 0x9f, 0x79, 0x6f, 0x79, 0x6e,
	0xc4, 0x99, 0xef, 0xfa, 0xce, 0x5b, 0x5e, 0xe0, 0xb8, 0x96, 0xd1, 0x1a, 0x84, 0x01, 0x0f, 0xd4,
	0xa6, 0x63, 0xb6, 0x24, 0xca, 0x04, 0x83, 0x10, 0x2e, 0xd5, 0x1d, 0xd3, 0xe8, 0x9a, 0x11, 0x13,
	0x88, 0x9a, 0x0b, 0x9b, 0xfb, 0x8c, 0xef, 0x0a, 0xd4, 0xfb, 0x31, 0x6a, 0xc7, 0xed, 0x0f, 0x3c,
	0xd6, 0xf6, 0x8f, 0x03, 0x9d, 0x7d, 0xaa, 0x5e, 0x83, 0x0a, 0x52, 0x18, 0x21, 0xfb, 0xb4, 0xa9,
	0x6c, 0x2a, 0x37, 0x6a, 0x5b, 0xb5, 0x96, 0x63, 0xb6, 0x76, 0xcc, 0x88, 0xe9, 0xec, 0x53, 0xbd,
	0xdc, 0x15, 0x0b, 0xf5, 0x65, 0x00, 0x29, 0xd3, 0x70, 0xed, 0x66, 0x61, 0x53, 0xb9, 0x51, 0xd7,
	0xab, 0x12, 0xd2, 0xb6, 0xb5, 0x7f, 0x15, 0xe1, 0xea, 0x53, 0x64, 0x45, 0x03, 0xf5, 0x26, 0x54,
	0xa5, 0xb0, 0x68, 0x20, 0xa5, 0xad, 0x4c, 0xa5, 0x45, 0x03, 0xbd, 0xd2, 0x95, 0x2b, 0xf5, 0x4d,
	0x50, 0x3d, 0xf7, 0x84, 0x19, 0xc3, 0x88, 0x85, 0x86, 0x69, 0x59, 0xc1, 0xd0, 0xe7, 0x51, 0xb3,
	0xb0, 0x59, 0xbc, 0x51, 0xd5, 0x1b, 0xb8, 0xf3, 0x30, 0x62, 0xe1, 0xb6, 0x84, 0xab, 0xd7, 0xe0,
	0xc2, 0x14, 0x9b, 0x60, 0xcd, 0x22, 0x1d, 0xb1, 0x1e, 0xa3, 0xee, 0x22, 0x10, 0xb9, 0xf2, 0xc0,
	0x36, 0xc7, 0xc6, 0xb1, 0x17, 0x8c, 0x26, 0xa8, 0x4b, 0x84, 0xda, 0xa0, 0x9d, 0xf7, 0x68, 0x43,
	0x60, 0xbf, 0x0c, 0xc0, 0x7c, 0xce, 0x42, 0x66, 0x1b, 0x26, 0x6f, 0x96, 0xc4, 0x9d, 0x25, 0x64,
	0x9b, 0xab, 0x3b, 0x50, 0xe2, 0x3d, 0xd6, 0x67, 0xcd, 0x65, 0xba, 0xc9, 0x9b, 0xad, 0x79, 0x76,
	0x69, 0xa5, 0xd4, 0x72, 0x84, 0x34, 0xba, 0x20, 0x55, 0x5f, 0x84, 0x4a, 0x38, 0xf4, 0x98, 0x31,
	0x0c, 0xbd, 0x66, 0x79, 0x53, 0xb9, 0x51, 0xd5, 0xcb, 0xf8, 0xfd, 0x30, 0xf4, 0xd4, 0x57, 0x61,
	0xd5, 0xea, 0x31, 0xeb, 0xc4, 0x70, 0x7d, 0x83, 0x07, 0x03, 0xd7, 0x6a, 0x56, 0x08, 0x61, 0x85,
	0xa0, 0x6d, 0xff, 0x08, 0x61, 0xea, 0x47, 0x50, 0x9f, 0x60, 0xb9, 0xfe, 0x71, 0xd0, 0xac, 0xd2,
	0x61, 0xee, 0x9c, 0xf3, 0x30, 0xae, 0xef, 0xec, 0x0a, 0x76, 0x64, 0xa4, 0x9a, 0x35, 0xfd, 0x50,
	0x6f, 0xc2, 0x5a, 0xcf, 0x8c, 0x84, 0x4e, 0x23, 0xc6, 0x8d, 0x7e, 0x10, 0xd8, 0x4d, 0xd8, 0x54,
	0x6e, 0x54, 0xf4, 0xd5, 0x9e, 0x19, 0xa1, 0x56, 0x3b, 0x8c, 0x1f, 0x04, 0x81, 0xad, 0xfd, 0x44,
	0x81, 0xcb, 0x67, 0xf0, 0x55, 0x35, 0xa8, 0xf7, 0xcc, 0xcf, 0x98, 0x11, 0x1f, 0x95, 0x8c, 0x5f,
	0xd1, 0x6b, 0x08, 0x94, 0x78, 0x6a, 0x03, 0x8a, 0xb6, 0x39, 0x96, 0xae, 0x85, 0x4b, 0x75, 0x03,
	0x4a, 0xdc, 0xe5, 0x1e, 0x23, 0x5b, 0x56, 0x75, 0xf1, 0xa1, 0x5e, 0x86, 0x2a, 0xfb, 0x7c, 0xe0,
	0x86, 0x0c, 0x8d, 0x22, 0x4c, 0x57, 0x11, 0x80, 0x6d, 0xae, 0xfd, 0x4c, 0x81, 0xda, 0x7d, 0xf7,
	0x84, 0x75, 0x02, 0xdf, 0x59, 0x9c, 0x7b, 0xab, 0x3b, 0xb0, 0x14, 0x05, 0xbe, 0x43, 0x07, 0xa9,
	0x6d, 0xb5, 0xce, 0xaf, 0x5c, 0x3a, 0x07, 0xd1, 0x6a, 0xff, 0x07, 0x2b, 0xd3, 0x93, 0xe5, 0x7a,
	0x0c, 0xda, 0x5d, 0xd8, 0x98, 0xc5, 0x58, 0x5d, 0x85, 0x82, 0x6b, 0x13, 0x6d, 0x55, 0x2f, 0xb8,
	0xb6, 0xaa, 0xc2, 0x92, 0x6f, 0xf6, 0x19, 0x9d, 0xbf, 0xaa, 0xd3, 0x5a, 0xfb, 0x8b, 0x02, 0xab,
	0xb1, 0xdc, 0xc3, 0x80, 0xbb, 0xc7, 0x63, 0x74, 0xba, 0xe3, 0x30, 0xe8, 0x1b, 0x43, 0x49, 0x5c,
	0xd7, 0xcb, 0xf8, 0xfd, 0xd0, 0xb5, 0xd5, 0xab, 0xb0, 0x42, 0x5b, 0xf2, 0xc5, 0x49, 0x4e, 0x35,
	0x84, 0xc9, 0xc7, 0xa6, 0xbe, 0x02, 0x75, 0x42, 0xf1, 0x5d, 0xeb, 0x84, 0xa4, 0x09, 0xeb, 0x10,
	0xdd, 0xa1, 0x84, 0xa1, 0x91, 0xf0, 0xd2, 0x06, 0x21, 0x2c, 0x11, 0x42, 0x05, 0x01, 0x87, 0xb8,
	0x79, 0x05, 0x6a, 0x3c, 0xe0, 0xa6, 0x27, 0x9f, 0x9f, 0x78, 0x58, 0x40, 0xa0, 0xc9, 0xc3, 0x4b,
	0x58, 0x63, 0x39, 0x1b, 0x6c, 0xfe, 0xa4, 0xc0, 0xf3, 0xfb, 0x8c, 0xef, 0x30, 0xbc, 0x97, 0x8d,
	0x17, 0x43, 0xb5, 0x2c, 0xd0, 0xdc, 0x1b, 0x50, 0x4a, 0x06, 0x11, 0xf1, 0xa1, 0x3e, 0x80, 0xaa,
	0x17, 0x98, 0xb6, 0xd1, 0x0f, 0x42, 0x71, 0xa7, 0xda, 0xd6, 0xd6, 0xf9, 0x3d, 0xe1, 0x7e, 0x60,
	0xda, 0x07, 0x41, 0xc8, 0xf4, 0x8a, 0x27, 0x57, 0xda, 0x5f, 0x15, 0x78, 0x61, 0xd6, 0x3d, 0xf2,
	0x45, 0xca, 0x77, 0xa0, 0x84, 0x9a, 0x15, 0xc1, 0xb1, 0xb6, 0xf5, 0xda, 0xfc, 0x23, 0x25, 0x04,
	0xe9, 0x82, 0x26, 0x7d, 0xa7, 0xe2, 0x02, 0xee, 0x14, 0x42, 0x2d, 0x21, 0x46, 0x6d, 0x42, 0x39,
	0x76, 0x25, 0xe1, 0xa6, 0xf1, 0xa7, 0x7a, 0x09, 0x2a, 0x13, 0x0f, 0x12, 0x5e, 0x36, 0xf9, 0xc6,
	0x50, 0x10, 0xb1, 0xcf, 0xa5, 0xf6, 0x71, 0x79, 0xa6, 0x3f, 0x69, 0x9f, 0xc0, 0xfa, 0x3e, 0xe3,
	0xdf, 0x92, 0x33, 0x68, 0x8f, 0x61, 0xe3, 0x34, 0xf7, 0x7c, 0x26, 0x8a, 0x4f, 0x8f, 0xea, 0x94,
	0x39, 0x8c, 0x4e, 0x8f, 0xbc, 0xb4, 0x3f, 0x2a, 0x24, 0x00, 0xc3, 0xa9, 0x48, 0x3e, 0xcf, 0xa6,
	0x33, 0x7f, 0x59, 0xa0, 0x47, 0x99, 0xbd, 0x46, 0x3e, 0x45, 0xbd, 0x0b, 0x65, 0x91, 0x99, 0x63,
	0x6f, 0x7e, 0x75, 0xfe, 0x99, 0xa6, 0x92, 0xf4, 0x98, 0x48, 0xfd, 0x7f, 0x58, 0x16, 0x4b, 0xe9,
	0xcb, 0xe7, 0x23, 0x97, 0x34, 0x39, 0xab, 0x83, 0x94, 0x06, 0x4b, 0x0b, 0xd0, 0xe0, 0x2f, 0x15,
	0x80, 0xe9, 0xa9, 0x16, 0xf6, 0x74, 0x54, 0x58, 0x0a, 0x4d, 0xff, 0x44, 0xde, 0x83, 0xd6, 0x88,
	0xe5, 0x0f, 0xfb, 0x32, 0xf2, 0xe2, 0x32, 0x5d, 0x6d, 0x50, 0xd2, 0x5d, 0x4e, 0x57, 0x1b, 0x08,
	0xd3, 0xbe, 0x52, 0x40, 0xdd, 0x67, 0x5c, 0x9c, 0xf0, 0x1e, 0xe3, 0xa6, 0xeb, 0x3d, 0x7b, 0x9e,
	0xfa, 0x77, 0x85, 0xe2, 0x45, 0xfa, 0x12, 0xf9, 0xfc, 0xf4, 0xbb, 0x50, 0xb6, 0x89, 0x30, 0xf6,
	0xd3, 0x6b, 0xf3, 0x4f, 0x94, 0x92, 0x13, 0x93, 0x2d, 0x3e, 0xf0, 0x7e, 0x04, 0x2b, 0x49, 0x49,
	0x68, 0x76, 0xdb, 0xe4, 0x4c, 0xfa, 0x0e, 0xad, 0x09, 0xc6, 0x22, 0x2b, 0xae, 0x0f, 0x70, 0x4d,
	0x19, 0x3f, 0xe9, 0xee, 0x42, 0xf7, 0xb5, 0xe3, 0xa9, 0xa7, 0x6b, 0x8f, 0xa0, 0x39, 0xef, 0x00,
	0x18, 0xda, 0x3c, 0x33, 0xe2, 0xc6, 0xc0, 0x74, 0x98, 0x2c, 0x26, 0x2a, 0x08, 0xf8, 0xc0, 0x74,
	0x18, 0xda, 0x9b, 0x36, 0xa7, 0xb5, 0x44, 0x5d, 0x27, 0x74, 0xc1, 0xf7, 0xb7, 0x0a, 0xac, 0x8a,
	0x33, 0x6f, 0x8f, 0xcc, 0xd0, 0x3e, 0x88, 0x1c, 0x74, 0xcc, 0x69, 0x55, 0x82, 0xcb, 0xe4, 0x33,
	0x28, 0xcc, 0x7f, 0x06, 0xc5, 0xcc, 0x33, 0x68, 0x42, 0xd9, 0x0a, 0x7c, 0xce, 0xe4, 0xfb, 0xad,
	0xea, 0xf1, 0x67, 0xc6, 0x07, 0x4b, 0x59, 0x1f, 0xbc, 0x02, 0x35, 0x9f, 0x8d, 0x8c, 0x98, 0x58,
	0x3c, 0x02, 0xf0, 0xd9, 0x68, 0x57, 0x40, 0xb4, 0x5b, 0xb1, 0x9e, 0x65, 0x31, 0x95, 0xd5, 0x9f,
	0x72, 0x5a, 0x7f, 0x9f, 0x80, 0x2a, 0x48, 0x8e, 0x82, 0x0f, 0x82, 0x88, 0x4b, 0x42, 0x0d, 0xea,
	0x92, 0x10, 0xed, 0x60, 0xdc, 0x92, 0x96, 0x92, 0x94, 0xf7, 0x58, 0x64, 0xdd, 0xca, 0xe2, 0x6c,
	0x4d, 0xea, 0xb1, 0x09, 0xce, 0x96, 0xf6, 0x9b, 0x25, 0x50, 0x4f, 0x37, 0x18, 0x58, 0x1b, 0x76,
	0x9d, 0xb8, 0x36, 0xec, 0x3a, 0x68, 0xa8, 0x81, 0x67, 0x8e, 0x59, 0x68, 0x74, 0x9d, 0x38, 0x6a,
	0x08, 0xc0, 0x8e, 0x83, 0x15, 0xa1, 0xe5, 0x05, 0xd6, 0x09, 0xee, 0x15, 0xa5, 0xbe, 0xf0, 0x7b,
	0xc7, 0xc1, 0xd6, 0xaa, 0x67, 0x7a, 0xc7, 0x71, 0x4c, 0xc4, 0x46, 0x45, 0x68, 0xb4, 0x8e, 0x60,
	0x71, 0x2f, 0x6c, 0x57, 0x5e, 0x87, 0x35, 0xe6, 0x73, 0x2c, 0xcb, 0x13, 0x98, 0x25, 0xc2, 0xbc,
	0x20, 0x36, 0xa6, 0xb8, 0x2a, 0x2c, 0x0d, 0x7d, 0x37, 0xd6, 0x2e, 0xad, 0xd5, 0x4d, 0x58, 0xe9,
	0x05, 0x9e, 0x6d, 0xf4, 0x5d, 0xcb, 0xe8, 0x3a, 0x51, 0xb3, 0x4c, 0x69, 0x12, 0x10, 0x76, 0xe0,
	0x5a, 0x3b, 0x4e, 0x84, 0x18, 0x1e, 0xc3, 0x2e, 0x42, 0xa0, 0xc8, 0x76, 0x08, 0x08, 0x46, 0x28,
	0x78, 0x0d, 0xdc, 0x8b, 0x98, 0xc9, 0xa9, 0x0f, 0xaa, 0xea, 0xe5, 0xbe, 0x6b, 0x75, 0x98, 0xc9,
	0xc9, 0xae, 0x41, 0xd8, 0x37, 0x3d, 0xb1, 0x0b, 0xd2, 0xae, 0x04, 0x8a, 0x11, 0x2c, 0x2f, 0x88,
	0x98, 0x2d, 0x10, 0x6a, 0x02, 0x41, 0x80, 0x08, 0xa1, 0x05, 0xeb, 0x51, 0xcf, 0x0c, 0x99, 0xc1,
	0x7c, 0x1e, 0x9a, 0xbe, 0xc5, 0x0c, 0xd7, 0x0a, 0xfc, 0xe6, 0x0a, 0x21, 0xae, 0xd1, 0xd6, 0x9e,
	0xdc, 0x69, 0x5b, 0x81, 0xaf, 0x5e, 0x87, 0x06, 0x7a, 0x52, 0xea, 0x52, 0x75, 0xba, 0x54, 0xdd,
	0x67, 0xa3, 0xf7, 0xa7, 0xf7, 0x92, 0x88, 0xa9, 0xbb, 0xad, 0x0a, 0x15, 0xfb, 0x6c, 0x74, 0x7f,
	0x7a, 0xbd, 0xab, 0xb0, 0x12, 0xb2, 0x28, 0x18, 0x86, 0x96, 0x68, 0x18, 0x2f, 0x08, 0x67, 0x88,
	0x61, 0xa8, 0xd9, 0x24, 0x4a, 0xdf, 0xbe, 0xd3, 0x6c, 0xa4, 0x51, 0x0e, 0xec, 0x3b, 0xda, 0xe3,
	0xcc, 0x6b, 0x26, 0x77, 0x91, 0x3e, 0x39, 0x69, 0x69, 0x95, 0xaf, 0xdd, 0xd2, 0x6a, 0x4f, 0xe0,
	0x72, 0x07, 0xeb, 0xa5, 0x74, 0x1b, 0x48, 0xdd, 0xea, 0x62, 0x73, 0x85, 0x68, 0x8a, 0xe3, 0xde,
	0x10, 0x3f, 0xb4, 0x36, 0xbc, 0x34, 0x5f, 0x76, 0xbe, 0x9e, 0xeb, 0x43, 0xb8, 0x9a, 0x0d, 0x7a,
	0xc4, 0x07, 0x81, 0x4e, 0xac, 0xaf, 0xf4, 0x21, 0x95, 0xb9, 0x87, 0x2c, 0x24, 0x0f, 0xf9, 0x09,
	0xac, 0x67, 0x4f, 0xb8, 0xc0, 0x72, 0xf5, 0xab, 0x02, 0x6c, 0x9c, 0x66, 0x9f, 0x2f, 0xbd, 0xb5,
	0x61, 0x89, 0x8f, 0x07, 0xa2, 0xb8, 0x58, 0xfd, 0x1a, 0xb3, 0x84, 0xa3, 0xf1, 0x80, 0xe9, 0xc4,
	0xe2, 0xf4, 0x7c, 0xa2, 0xb8, 0xb0, 0xf9, 0xc4, 0x47, 0x00, 0xe2, 0x41, 0x5a, 0x66, 0x68, 0xcb,
	0xca, 0xe0, 0x6e, 0x6e, 0xbe, 0x1d, 0x64, 0xb1, 0x6b, 0x86, 0xb6, 0x5e, 0x8d, 0xe2, 0xa5, 0xf6,
	0x37, 0x05, 0xb4, 0xfd, 0x84, 0x23, 0xd1, 0x04, 0x69, 0x2a, 0xfa, 0xd9, 0xac, 0xd0, 0x7f, 0x51,
	0x80, 0x57, 0x9e, 0x7a, 0xa9, 0x7c, 0x8e, 0xf2, 0x1d, 0x58, 0x22, 0xa3, 0x16, 0x08, 0xeb, 0xe6,
	0xd9, 0xd5, 0x76, 0xd2, 0x90, 0x44, 0x86, 0xe4, 0xd4, 0x12, 0x15, 0xa9, 0x86, 0xca, 0x43, 0x8e,
	0xfb, 0x8b, 0xd7, 0xd0, 0x1f, 0x8a, 0xb0, 0xf9, 0x34, 0x37, 0x51, 0xdf, 0x82, 0xf5, 0x74, 0xa5,
	0x4c, 0xb9, 0x59, 0x66, 0xda, 0x46, 0xb2, 0x5c, 0xc6, 0xfc, 0x4c, 0xbd, 0x5f, 0x2f, 0x18, 0x19,
	0xdc, 0x95, 0xd5, 0x7a, 0x51, 0xaf, 0x20, 0xe0, 0xc8, 0x15, 0x65, 0x4a, 0x5c, 0xdc, 0x14, 0xd3,
	0xc5, 0xcd, 0x65, 0xa8, 0x62, 0x31, 0x93, 0x6a, 0x78, 0x11, 0x40, 0x03, 0x94, 0xab, 0xb0, 0xe2,
	0xb9, 0x1c, 0x45, 0x8b, 0x52, 0x5d, 0xa4, 0xd9, 0x9a, 0x80, 0x91, 0xe8, 0xe9, 0xec, 0x6c, 0x39,
	0x39, 0x3b, 0x4b, 0x3b, 0x62, 0x39, 0xeb, 0x88, 0x58, 0x06, 0x98, 0xa1, 0x6d, 0xb8, 0xfd, 0x38,
	0xbb, 0x96, 0xf1, 0xbb, 0xdd, 0x77, 0xd4, 0xe7, 0x61, 0x79, 0xc4, 0xba, 0x98, 0x9a, 0x44, 0x62,
	0x2d, 0x8d, 0x58, 0x77, 0xc7, 0x51, 0x2f, 0x42, 0xb9, 0xcb, 0x85, 0x02, 0x44, 0x4a, 0x5d, 0xee,
	0xf2, 0xf8, 0xda, 0x88, 0x2f, 0xce, 0x20, 0x92, 0x69, 0x65, 0xc4, 0xba, 0xe2, 0x70, 0x93, 0x97,
	0x4b, 0xce, 0xb3, 0xf2, 0x4d, 0x5e, 0x2e, 0xb9, 0x83, 0x78, 0xb9, 0xb8, 0xd4, 0x7e, 0xae, 0x9c,
	0x6d, 0x42, 0x8a, 0x1c, 0xd9, 0xb9, 0xd9, 0x44, 0x59, 0x85, 0xcc, 0xa0, 0x31, 0x1a, 0xc6, 0x57,
	0x90, 0x05, 0x66, 0x34, 0x94, 0x57, 0x68, 0x40, 0x11, 0xb5, 0x24, 0x2c, 0x83, 0x4b, 0xac, 0xd5,
	0x30, 0x8d, 0x4f, 0x49, 0xa4, 0x55, 0x7c, 0x36, 0xea, 0x48, 0x2a, 0xed, 0x57, 0x0a, 0x5c, 0xc8,
	0xf8, 0xf2, 0xb7, 0xdd, 0xe7, 0xd9, 0xe6, 0x38, 0xee, 0xf3, 0x6c, 0x73, 0x7c, 0xce, 0x3e, 0xef,
	0x31, 0xbc, 0x38, 0x53, 0x7b, 0x76, 0x9e, 0xa8, 0xf7, 0x22, 0x54, 0xa4, 0x95, 0xed, 0xb8, 0x74,
	0x17, 0x76, 0xb2, 0xb5, 0x7d, 0xb8, 0x34, 0x8f, 0x7f, 0xbe, 0x2c, 0xfd, 0x63, 0x05, 0xea, 0x92,
	0xc1, 0xf9, 0x52, 0xb2, 0x6c, 0x30, 0x0a, 0xd3, 0x06, 0xe3, 0xac, 0x36, 0x42, 0xea, 0x6f, 0x29,
	0x3d, 0x93, 0xa6, 0x94, 0x5e, 0x4a, 0xa6, 0x74, 0x9b, 0xda, 0x62, 0xdd, 0xea, 0xd3, 0x88, 0xe8,
	0x80, 0xf9, 0xc3, 0x3c, 0x8a, 0xba, 0x06, 0x17, 0x68, 0x3c, 0x14, 0xf5, 0x18, 0xe3, 0xc6, 0x24,
	0xf3, 0xd6, 0xf5, 0x3a, 0x82, 0x3b, 0x08, 0xc5, 0x8c, 0xaa, 0xfd, 0x48, 0x34, 0xae, 0x69, 0x31,
	0xf9, 0x02, 0xf6, 0x36, 0x94, 0xfa, 0xcc, 0x1f, 0xc6, 0x6d, 0xeb, 0x1b, 0xf3, 0x1f, 0x5d, 0x6a,
	0xd2, 0x4c, 0xa2, 0x04, 0xa5, 0xb6, 0x0f, 0x6b, 0xa7, 0xf6, 0xce, 0x33, 0x89, 0x96, 0x1d, 0x49,
	0x31, 0xee, 0x48, 0xb4, 0xc7, 0xa4, 0x34, 0x64, 0xb1, 0x33, 0x46, 0x26, 0xed, 0x5c, 0xde, 0xb5,
	0x09, 0x2b, 0xa4, 0x34, 0x3c, 0xd4, 0xd4, 0xc3, 0x20, 0x92, 0x27, 0x6a, 0xdb, 0xda, 0x0f, 0x49,
	0x5b, 0x69, 0xfe, 0x79, 0xd3, 0x5b, 0x6a, 0xb4, 0x7a, 0xfd, 0x9c, 0xda, 0x92, 0xc3, 0x55, 0xed,
	0x1f, 0x05, 0xa8, 0xe7, 0x1e, 0xd8, 0xab, 0x2f, 0xc0, 0x72, 0xe4, 0xfa, 0x8e, 0x9c, 0x61, 0x55,
	0x75, 0xf9, 0x85, 0x8e, 0xdd, 0x1f, 0x46, 0xae, 0x25, 0x1c, 0x44, 0xb8, 0x64, 0x95, 0x20, 0xe8,
	0x1c, 0xc8, 0xea, 0xd8, 0x9d, 0x44, 0x1d, 0x5a, 0x63, 0x04, 0xc3, 0xbf, 0x46, 0xe4, 0x3e, 0x89,
	0xdf, 0x79, 0x05, 0x01, 0x1d, 0xf7, 0xc9, 0x74, 0x93, 0xd8, 0x95, 0xa7, 0x9b, 0xc4, 0x2d, 0xde,
	0xec, 0x99, 0x51, 0x4f, 0xa6, 0x02, 0xda, 0x7c, 0xdf, 0x8c, 0x7a, 0xf1, 0x1b, 0xaa, 0xce, 0x7e,
	0x43, 0x90, 0x79, 0x43, 0xf8, 0x20, 0x43, 0x66, 0x72, 0xf1, 0x2b, 0x1a, 0xa6, 0x82, 0x25, 0xbd,
	0x2a, 0x21, 0xdb, 0x9c, 0xae, 0xcb, 0x4d, 0x3e, 0x8c, 0x28, 0x0f, 0xd4, 0x75, 0xf9, 0x95, 0x0c,
	0x8b, 0xf5, 0x54, 0x58, 0xd4, 0x5c, 0x68, 0x88, 0x60, 0xf1, 0x81, 0x79, 0xde, 0x42, 0x5c, 0xb8,
	0x5e, 0x61, 0xd2, 0x0c, 0xab, 0xb0, 0xc4, 0xd9, 0xe7, 0x71, 0xd2, 0xa5, 0xf5, 0x64, 0x60, 0x22,
	0xe3, 0x27, 0xae, 0xb5, 0x5f, 0x27, 0x2d, 0x78, 0x10, 0x04, 0xf6, 0x2c, 0x0b, 0x12, 0xa7, 0x42,
	0x82, 0x93, 0xcc, 0x0d, 0xc5, 0x69, 0x6e, 0xb8, 0x02, 0x35, 0x2f, 0xe0, 0xdc, 0x65, 0xb7, 0x12,
	0x0d, 0x34, 0x48, 0x10, 0xf6, 0x6d, 0x09, 0x04, 0x6c, 0xdb, 0x4a, 0x29, 0x84, 0x03, 0xfb, 0xce,
	0x14, 0x61, 0x8b, 0x38, 0x2c, 0x27, 0x11, 0xb6, 0x52, 0x1c, 0xb6, 0x88, 0x43, 0x39, 0x85, 0x80,
	0x1c, 0x5a, 0xb0, 0x2e, 0xbe, 0x0c, 0x2b, 0xe8, 0x0f, 0x3c, 0xc6, 0x45, 0x13, 0x29, 0x8c, 0xbb,
	0x26, 0xb6, 0x76, 0xe5, 0x0e, 0x32, 0x9c, 0x81, 0x8f, 0x8c, 0xab, 0xb3, 0xf0, 0xb1, 0xaf, 0xbc,
	0x0b, 0x8d, 0x7d, 0xc6, 0xb7, 0x3d, 0x0f, 0xf5, 0xb4, 0x7b, 0x9c, 0xe7, 0xe7, 0x37, 0xed, 0x07,
	0xb0, 0x96, 0xa1, 0xcd, 0xfd, 0x50, 0xfb, 0x41, 0x60, 0xe7, 0x79, 0xa8, 0x28, 0x49, 0x17, 0x54,
	0xda, 0x18, 0x2e, 0x76, 0x32, 0x85, 0x31, 0x6d, 0x2f, 0xae, 0xc4, 0xbf, 0x08, 0x65, 0x14, 0x85,
	0x7b, 0xf2, 0x55, 0xe3, 0x67, 0xdb, 0xd6, 0xf6, 0xa0, 0x39, 0x5b, 0x74, 0xbe, 0x3c, 0xd8, 0x83,
	0x2b, 0x42, 0x81, 0x13, 0x4e, 0x0f, 0xfc, 0x03, 0xd7, 0x5a, 0xfc, 0x4d, 0xb4, 0x2f, 0x0a, 0xf4,
	0x5f, 0x05, 0x67, 0x88, 0xca, 0x67, 0x3a, 0x06, 0x55, 0xfa, 0x85, 0x99, 0x7e, 0x5d, 0x16, 0xe6,
	0x7b, 0x7f, 0xbe, 0xf9, 0x9e, 0x26, 0xb9, 0x15, 0x7f, 0xec, 0xf9, 0x3c, 0x1c, 0xeb, 0x95, 0xa1,
	0xfc, 0xbc, 0x64, 0x43, 0x3d, 0xb5, 0x85, 0x8f, 0xf4, 0x84, 0x8d, 0xe3, 0x49, 0xe3, 0x09, 0x1b,
	0xa3, 0x13, 0x7d, 0x66, 0x7a, 0x43, 0x26, 0xbb, 0x99, 0xf3, 0x3b, 0x11, 0x51, 0xdd, 0x2d, 0xfc,
	0xaf, 0x42, 0x19, 0x3a, 0xb5, 0x29, 0xc3, 0xd3, 0xe9, 0xb1, 0xe6, 0x3b, 0xb0, 0x24, 0x6f, 0x9c,
	0x4b, 0x16, 0x11, 0x65, 0x4c, 0x54, 0xcc, 0x9a, 0xe8, 0x77, 0x0a, 0x3c, 0x9f, 0x2d, 0xaf, 0x1e,
	0xdd, 0x5e, 0xa0, 0x37, 0x7f, 0x0c, 0x1b, 0x56, 0xe0, 0x47, 0x9c, 0x79, 0x9e, 0xc9, 0xdd, 0xc0,
	0x37, 0xac, 0x5e, 0x10, 0x44, 0xf1, 0x1c, 0xfb, 0x8c, 0xcb, 0xec, 0x26, 0xa9, 0xf4, 0xf5, 0x14,
	0x93, 0x5d, 0xe2, 0xa1, 0xdd, 0x86, 0x7a, 0x0a, 0x2b, 0x11, 0x71, 0x4b, 0x73, 0x7f, 0xe4, 0xfe,
	0xb2, 0x00, 0x2f, 0xcc, 0xba, 0xf1, 0x7f, 0xe2, 0xd8, 0xe3, 0x68, 0xc6, 0xd8, 0x23, 0x07, 0xdf,
	0x49, 0x23, 0xfb, 0xe8, 0x76, 0x72, 0xe2, 0xf1, 0x7b, 0x25, 0xa1, 0xc1, 0x7b, 0x61, 0x30, 0x90,
	0x28, 0xcf, 0x86, 0xd3, 0x7c, 0xa1, 0xc0, 0xc5, 0x99, 0xa7, 0xcf, 0xe7, 0x00, 0x69, 0xd5, 0x16,
	0x16, 0xa4, 0xda, 0x9f, 0x96, 0x4e, 0xff, 0x73, 0x4c, 0x02, 0xf5, 0x8c, 0x06, 0x30, 0x35, 0x04,
	0x28, 0x64, 0x86, 0x00, 0xa9, 0xc1, 0x42, 0x31, 0x33, 0x58, 0x78, 0x19, 0xc0, 0x76, 0xa3, 0x81,
	0x67, 0x8e, 0xd1, 0x16, 0xb2, 0x58, 0x94, 0x90, 0xb6, 0xfd, 0xb4, 0x1f, 0x41, 0xae, 0xc2, 0x4a,
	0xbc, 0x4d, 0xa2, 0x45, 0xb5, 0x51, 0x93, 0x30, 0x92, 0x9e, 0x1c, 0x15, 0x94, 0x4f, 0x8d, 0x0a,
	0xba, 0x4e, 0x62, 0x86, 0x50, 0xea, 0x3a, 0x08, 0x4e, 0x8c, 0x0a, 0xaa, 0xf3, 0x47, 0x05, 0x90,
	0x19, 0x15, 0x34, 0xa1, 0x7c, 0x1c, 0x84, 0x7c, 0xe8, 0xc7, 0x53, 0x84, 0xf8, 0x53, 0xfd, 0x10,
	0xd6, 0xfb, 0x26, 0xb7, 0x7a, 0x46, 0xca, 0x21, 0xe4, 0x34, 0xe1, 0xdc, 0xee, 0xa4, 0x12, 0x8f,
	0x74, 0xc4, 0x41, 0x4f, 0x25, 0xbf, 0xca, 0xb0, 0xae, 0xe7, 0xf5, 0x54, 0x62, 0x92, 0xe6, 0x9d,
	0x1e, 0x7d, 0xac, 0x2e, 0x70, 0xf4, 0x81, 0x6d, 0x64, 0x6c, 0xb5, 0xcf, 0x5c, 0x36, 0x42, 0xcb,
	0x8a, 0x5f, 0x08, 0xea, 0x12, 0xfc, 0xc8, 0x65, 0xa3, 0xb6, 0xad, 0x0d, 0x60, 0xbd, 0xc3, 0x78,
	0xfa, 0xac, 0x39, 0x9e, 0xf9, 0xad, 0x39, 0xef, 0xb8, 0x40, 0x11, 0x7a, 0xe6, 0xf3, 0xdc, 0x86,
	0x8d, 0xd3, 0x12, 0xf3, 0xfe, 0x0b, 0x54, 0xe3, 0xeb, 0x9e, 0x58, 0xfb, 0xa7, 0x02, 0x6b, 0xdf,
	0x44, 0xf8, 0xdc, 0xd0, 0x55, 0xf8, 0xe6, 0xa1, 0x4b, 0x7d, 0x00, 0xab, 0x29, 0x70, 0x24, 0xa7,
	0xa1, 0xe7, 0xe6, 0x9a, 0x21, 0xd7, 0xde, 0xa5, 0xff, 0xc4, 0x9c, 0x3b, 0xbf, 0x14, 0x31, 0x3d,
	0x39, 0x9b, 0x51, 0xd2, 0xb3, 0x99, 0x27, 0xe9, 0xd1, 0xf7, 0x2c, 0xfa, 0x53, 0xa1, 0x52, 0x59,
	0x50, 0xa8, 0xb4, 0x31, 0x52, 0x32, 0xeb, 0x24, 0x21, 0x9d, 0xe8, 0x8e, 0xcc, 0xee, 0x02, 0x4b,
	0xd8, 0xef, 0xc3, 0x4b, 0xf3, 0xa5, 0xe4, 0xf3, 0x8c, 0xff, 0x86, 0x0d, 0x37, 0x32, 0x26, 0x97,
	0x35, 0xa4, 0x10, 0x92, 0x59, 0xd1, 0x55, 0x37, 0xca, 0xca, 0x78, 0xfd, 0x7f, 0xa0, 0xd2, 0x09,
	0x7c, 0x87, 0xba, 0xec, 0x0d, 0x68, 0x3c, 0xf0, 0x68, 0x96, 0x33, 0x19, 0xf2, 0x34, 0x9e, 0x43,
	0xe8, 0x21, 0x1b, 0xa5, 0xa1, 0xca, 0xeb, 0x7f, 0x9e, 0xff, 0x2f, 0x96, 0xc4, 0xeb, 0x26, 0xbc,
	0x76, 0xc6, 0xb6, 0xf1, 0xf0, 0xf0, 0xde, 0xde, 0x7b, 0xed, 0xc3, 0xbd, 0x7b, 0x8d, 0xe7, 0xd4,
	0xeb, 0xf0, 0xca, 0x59, 0xa8, 0x9d, 0x87, 0xbb, 0xbb, 0x7b, 0x9d, 0x4e, 0x43, 0x79, 0x1a, 0xcf,
	0xfd, 0xbd, 0x23, 0xe3, 0xa8, 0x7d, 0x74, 0x7f, 0xaf, 0x51, 0x50, 0xdf, 0x80, 0xeb, 0x67, 0xa1,
	0xea, 0x7b, 0x87, 0x7b, 0xdf, 0x93, 0xc8, 0xc5, 0x9d, 0x43, 0x68, 0x5a, 0x41, 0xbf, 0x35, 0x76,
	0xc7, 0xc1, 0x10, 0x15, 0xdb, 0x0f, 0x6c, 0xe6, 0x89, 0x7f, 0x5a, 0xfe, 0x78, 0xcb, 0x09, 0x3c,
	0xd3, 0x77, 0x5a, 0x77, 0xb6, 0x38, 0x6f, 0x59, 0x41, 0xff, 0x6d, 0x02, 0x5b, 0x81, 0xf7, 0xb6,
	0x39, 0x18, 0xbc, 0x3d, 0xd3, 0xbb, 0xba, 0xcb, 0x84, 0x73, 0xfb, 0xdf, 0x01, 0x00, 0x00, 0xff,
	0xff, 0xa7, 0x26, 0x54, 0x7a, 0x3a, 0x2d, 0x00, 0x00,
}
