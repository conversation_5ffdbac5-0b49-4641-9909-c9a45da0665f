// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rhythm_.proto

package rhythm // import "golang.52tt.com/protocol/app/rhythm"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RhythmSwitchType int32

const (
	RhythmSwitchType_UnknownSwitchType RhythmSwitchType = 0
	RhythmSwitchType_VoteAndFollow     RhythmSwitchType = 1
	RhythmSwitchType_UserDefinedVotePk RhythmSwitchType = 2
)

var RhythmSwitchType_name = map[int32]string{
	0: "UnknownSwitchType",
	1: "VoteAndFollow",
	2: "UserDefinedVotePk",
}
var RhythmSwitchType_value = map[string]int32{
	"UnknownSwitchType": 0,
	"VoteAndFollow":     1,
	"UserDefinedVotePk": 2,
}

func (x RhythmSwitchType) String() string {
	return proto.EnumName(RhythmSwitchType_name, int32(x))
}
func (RhythmSwitchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{0}
}

type RhythmSwitchStatus int32

const (
	RhythmSwitchStatus_UnknownSwitchStatus RhythmSwitchStatus = 0
	RhythmSwitchStatus_Open                RhythmSwitchStatus = 1
	RhythmSwitchStatus_Close               RhythmSwitchStatus = 2
)

var RhythmSwitchStatus_name = map[int32]string{
	0: "UnknownSwitchStatus",
	1: "Open",
	2: "Close",
}
var RhythmSwitchStatus_value = map[string]int32{
	"UnknownSwitchStatus": 0,
	"Open":                1,
	"Close":               2,
}

func (x RhythmSwitchStatus) String() string {
	return proto.EnumName(RhythmSwitchStatus_name, int32(x))
}
func (RhythmSwitchStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{1}
}

type BrandMemberRole int32

const (
	BrandMemberRole_UnknownRole BrandMemberRole = 0
	BrandMemberRole_TeamLeader  BrandMemberRole = 1
	BrandMemberRole_Normal      BrandMemberRole = 2
	BrandMemberRole_ViceCaption BrandMemberRole = 3
	BrandMemberRole_Producer    BrandMemberRole = 4
)

var BrandMemberRole_name = map[int32]string{
	0: "UnknownRole",
	1: "TeamLeader",
	2: "Normal",
	3: "ViceCaption",
	4: "Producer",
}
var BrandMemberRole_value = map[string]int32{
	"UnknownRole": 0,
	"TeamLeader":  1,
	"Normal":      2,
	"ViceCaption": 3,
	"Producer":    4,
}

func (x BrandMemberRole) String() string {
	return proto.EnumName(BrandMemberRole_name, int32(x))
}
func (BrandMemberRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{2}
}

// ========= 相册 =========
type PhotoAlbumType int32

const (
	PhotoAlbumType_UnknownAlbum PhotoAlbumType = 0
	PhotoAlbumType_BrandAlbum   PhotoAlbumType = 1
)

var PhotoAlbumType_name = map[int32]string{
	0: "UnknownAlbum",
	1: "BrandAlbum",
}
var PhotoAlbumType_value = map[string]int32{
	"UnknownAlbum": 0,
	"BrandAlbum":   1,
}

func (x PhotoAlbumType) String() string {
	return proto.EnumName(PhotoAlbumType_name, int32(x))
}
func (PhotoAlbumType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{3}
}

type ColorConfiguration int32

const (
	ColorConfiguration_ColorWhite ColorConfiguration = 0
	ColorConfiguration_ColorBlack ColorConfiguration = 1
)

var ColorConfiguration_name = map[int32]string{
	0: "ColorWhite",
	1: "ColorBlack",
}
var ColorConfiguration_value = map[string]int32{
	"ColorWhite": 0,
	"ColorBlack": 1,
}

func (x ColorConfiguration) String() string {
	return proto.EnumName(ColorConfiguration_name, int32(x))
}
func (ColorConfiguration) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{4}
}

type UserDefinedVotePKType int32

const (
	UserDefinedVotePKType_UnknownType UserDefinedVotePKType = 0
	// 和 channelpk 不重复
	UserDefinedVotePKType_StringOptionType UserDefinedVotePKType = 100
)

var UserDefinedVotePKType_name = map[int32]string{
	0:   "UnknownType",
	100: "StringOptionType",
}
var UserDefinedVotePKType_value = map[string]int32{
	"UnknownType":      0,
	"StringOptionType": 100,
}

func (x UserDefinedVotePKType) String() string {
	return proto.EnumName(UserDefinedVotePKType_name, int32(x))
}
func (UserDefinedVotePKType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{5}
}

// AI 外语
type AIType int32

const (
	AIType_AiRapperType          AIType = 0
	AIType_AiForeignLanguageType AIType = 1
)

var AIType_name = map[int32]string{
	0: "AiRapperType",
	1: "AiForeignLanguageType",
}
var AIType_value = map[string]int32{
	"AiRapperType":          0,
	"AiForeignLanguageType": 1,
}

func (x AIType) String() string {
	return proto.EnumName(AIType_name, int32(x))
}
func (AIType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{6}
}

// 上报类型枚举
type AggregationChoose int32

const (
	AggregationChoose_EXposure AggregationChoose = 0
	AggregationChoose_Compose  AggregationChoose = 1
	AggregationChoose_Share    AggregationChoose = 2
)

var AggregationChoose_name = map[int32]string{
	0: "EXposure",
	1: "Compose",
	2: "Share",
}
var AggregationChoose_value = map[string]int32{
	"EXposure": 0,
	"Compose":  1,
	"Share":    2,
}

func (x AggregationChoose) String() string {
	return proto.EnumName(AggregationChoose_name, int32(x))
}
func (AggregationChoose) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{7}
}

type StayAddTicketStatus int32

const (
	StayAddTicketStatus_UnknownStatus StayAddTicketStatus = 0
	StayAddTicketStatus_OnStatus      StayAddTicketStatus = 1
	StayAddTicketStatus_OffStatus     StayAddTicketStatus = 2
)

var StayAddTicketStatus_name = map[int32]string{
	0: "UnknownStatus",
	1: "OnStatus",
	2: "OffStatus",
}
var StayAddTicketStatus_value = map[string]int32{
	"UnknownStatus": 0,
	"OnStatus":      1,
	"OffStatus":     2,
}

func (x StayAddTicketStatus) String() string {
	return proto.EnumName(StayAddTicketStatus_name, int32(x))
}
func (StayAddTicketStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{8}
}

// battle 状态
type BattleStatusEnum int32

const (
	BattleStatusEnum_UnknownBattleStatus BattleStatusEnum = 0
	BattleStatusEnum_StartBattle         BattleStatusEnum = 1
	BattleStatusEnum_CancelBattle        BattleStatusEnum = 2
)

var BattleStatusEnum_name = map[int32]string{
	0: "UnknownBattleStatus",
	1: "StartBattle",
	2: "CancelBattle",
}
var BattleStatusEnum_value = map[string]int32{
	"UnknownBattleStatus": 0,
	"StartBattle":         1,
	"CancelBattle":        2,
}

func (x BattleStatusEnum) String() string {
	return proto.EnumName(BattleStatusEnum_name, int32(x))
}
func (BattleStatusEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{9}
}

type ActionMode int32

const (
	ActionMode_UnknownActionMode     ActionMode = 0
	ActionMode_SwitchActionMode      ActionMode = 1
	ActionMode_ExamineModeActionMode ActionMode = 2
)

var ActionMode_name = map[int32]string{
	0: "UnknownActionMode",
	1: "SwitchActionMode",
	2: "ExamineModeActionMode",
}
var ActionMode_value = map[string]int32{
	"UnknownActionMode":     0,
	"SwitchActionMode":      1,
	"ExamineModeActionMode": 2,
}

func (x ActionMode) String() string {
	return proto.EnumName(ActionMode_name, int32(x))
}
func (ActionMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{10}
}

type UserDefinedVotePKNotify_UDVotePKStatus int32

const (
	UserDefinedVotePKNotify_UserDefinedStatusUnknown UserDefinedVotePKNotify_UDVotePKStatus = 0
	UserDefinedVotePKNotify_UserDefinedStatusStart   UserDefinedVotePKNotify_UDVotePKStatus = 1
	UserDefinedVotePKNotify_UserDefinedStatusInPK    UserDefinedVotePKNotify_UDVotePKStatus = 2
	UserDefinedVotePKNotify_UserDefinedStatusTimeout UserDefinedVotePKNotify_UDVotePKStatus = 3
	UserDefinedVotePKNotify_UserDefinedStatusCancel  UserDefinedVotePKNotify_UDVotePKStatus = 4
)

var UserDefinedVotePKNotify_UDVotePKStatus_name = map[int32]string{
	0: "UserDefinedStatusUnknown",
	1: "UserDefinedStatusStart",
	2: "UserDefinedStatusInPK",
	3: "UserDefinedStatusTimeout",
	4: "UserDefinedStatusCancel",
}
var UserDefinedVotePKNotify_UDVotePKStatus_value = map[string]int32{
	"UserDefinedStatusUnknown": 0,
	"UserDefinedStatusStart":   1,
	"UserDefinedStatusInPK":    2,
	"UserDefinedStatusTimeout": 3,
	"UserDefinedStatusCancel":  4,
}

func (x UserDefinedVotePKNotify_UDVotePKStatus) String() string {
	return proto.EnumName(UserDefinedVotePKNotify_UDVotePKStatus_name, int32(x))
}
func (UserDefinedVotePKNotify_UDVotePKStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{40, 0}
}

type GetMusicBlockFilterResp_FilterItemType int32

const (
	GetMusicBlockFilterResp_HOME_FILTER_ITEM  GetMusicBlockFilterResp_FilterItemType = 0
	GetMusicBlockFilterResp_PAGE_FILTER_ITERM GetMusicBlockFilterResp_FilterItemType = 1
	GetMusicBlockFilterResp_PAGE_POST         GetMusicBlockFilterResp_FilterItemType = 2
)

var GetMusicBlockFilterResp_FilterItemType_name = map[int32]string{
	0: "HOME_FILTER_ITEM",
	1: "PAGE_FILTER_ITERM",
	2: "PAGE_POST",
}
var GetMusicBlockFilterResp_FilterItemType_value = map[string]int32{
	"HOME_FILTER_ITEM":  0,
	"PAGE_FILTER_ITERM": 1,
	"PAGE_POST":         2,
}

func (x GetMusicBlockFilterResp_FilterItemType) String() string {
	return proto.EnumName(GetMusicBlockFilterResp_FilterItemType_name, int32(x))
}
func (GetMusicBlockFilterResp_FilterItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{79, 0}
}

// 律动现场 开关 管理
type GetRhythmSwitchReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	SwitchType           uint32       `protobuf:"varint,3,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRhythmSwitchReq) Reset()         { *m = GetRhythmSwitchReq{} }
func (m *GetRhythmSwitchReq) String() string { return proto.CompactTextString(m) }
func (*GetRhythmSwitchReq) ProtoMessage()    {}
func (*GetRhythmSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{0}
}
func (m *GetRhythmSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRhythmSwitchReq.Unmarshal(m, b)
}
func (m *GetRhythmSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRhythmSwitchReq.Marshal(b, m, deterministic)
}
func (dst *GetRhythmSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRhythmSwitchReq.Merge(dst, src)
}
func (m *GetRhythmSwitchReq) XXX_Size() int {
	return xxx_messageInfo_GetRhythmSwitchReq.Size(m)
}
func (m *GetRhythmSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRhythmSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRhythmSwitchReq proto.InternalMessageInfo

func (m *GetRhythmSwitchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRhythmSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRhythmSwitchReq) GetSwitchType() uint32 {
	if m != nil {
		return m.SwitchType
	}
	return 0
}

func (m *GetRhythmSwitchReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetRhythmSwitchResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SwitchStatus         uint32        `protobuf:"varint,2,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	IsVisible            bool          `protobuf:"varint,3,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRhythmSwitchResp) Reset()         { *m = GetRhythmSwitchResp{} }
func (m *GetRhythmSwitchResp) String() string { return proto.CompactTextString(m) }
func (*GetRhythmSwitchResp) ProtoMessage()    {}
func (*GetRhythmSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{1}
}
func (m *GetRhythmSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRhythmSwitchResp.Unmarshal(m, b)
}
func (m *GetRhythmSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRhythmSwitchResp.Marshal(b, m, deterministic)
}
func (dst *GetRhythmSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRhythmSwitchResp.Merge(dst, src)
}
func (m *GetRhythmSwitchResp) XXX_Size() int {
	return xxx_messageInfo_GetRhythmSwitchResp.Size(m)
}
func (m *GetRhythmSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRhythmSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRhythmSwitchResp proto.InternalMessageInfo

func (m *GetRhythmSwitchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRhythmSwitchResp) GetSwitchStatus() uint32 {
	if m != nil {
		return m.SwitchStatus
	}
	return 0
}

func (m *GetRhythmSwitchResp) GetIsVisible() bool {
	if m != nil {
		return m.IsVisible
	}
	return false
}

type SetRhythmSwitchReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	SwitchType           uint32       `protobuf:"varint,3,opt,name=switch_type,json=switchType,proto3" json:"switch_type,omitempty"`
	SwitchStatus         uint32       `protobuf:"varint,4,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetRhythmSwitchReq) Reset()         { *m = SetRhythmSwitchReq{} }
func (m *SetRhythmSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetRhythmSwitchReq) ProtoMessage()    {}
func (*SetRhythmSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{2}
}
func (m *SetRhythmSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRhythmSwitchReq.Unmarshal(m, b)
}
func (m *SetRhythmSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRhythmSwitchReq.Marshal(b, m, deterministic)
}
func (dst *SetRhythmSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRhythmSwitchReq.Merge(dst, src)
}
func (m *SetRhythmSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetRhythmSwitchReq.Size(m)
}
func (m *SetRhythmSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRhythmSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetRhythmSwitchReq proto.InternalMessageInfo

func (m *SetRhythmSwitchReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetRhythmSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetRhythmSwitchReq) GetSwitchType() uint32 {
	if m != nil {
		return m.SwitchType
	}
	return 0
}

func (m *SetRhythmSwitchReq) GetSwitchStatus() uint32 {
	if m != nil {
		return m.SwitchStatus
	}
	return 0
}

type SetRhythmSwitchResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetRhythmSwitchResp) Reset()         { *m = SetRhythmSwitchResp{} }
func (m *SetRhythmSwitchResp) String() string { return proto.CompactTextString(m) }
func (*SetRhythmSwitchResp) ProtoMessage()    {}
func (*SetRhythmSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{3}
}
func (m *SetRhythmSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRhythmSwitchResp.Unmarshal(m, b)
}
func (m *SetRhythmSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRhythmSwitchResp.Marshal(b, m, deterministic)
}
func (dst *SetRhythmSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRhythmSwitchResp.Merge(dst, src)
}
func (m *SetRhythmSwitchResp) XXX_Size() int {
	return xxx_messageInfo_SetRhythmSwitchResp.Size(m)
}
func (m *SetRhythmSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRhythmSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetRhythmSwitchResp proto.InternalMessageInfo

func (m *SetRhythmSwitchResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 厂牌
type GetBrandListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBrandListReq) Reset()         { *m = GetBrandListReq{} }
func (m *GetBrandListReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandListReq) ProtoMessage()    {}
func (*GetBrandListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{4}
}
func (m *GetBrandListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandListReq.Unmarshal(m, b)
}
func (m *GetBrandListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandListReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandListReq.Merge(dst, src)
}
func (m *GetBrandListReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandListReq.Size(m)
}
func (m *GetBrandListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandListReq proto.InternalMessageInfo

func (m *GetBrandListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type BrandListInfo struct {
	BrandId              string   `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandName            string   `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	FollowerNum          uint32   `protobuf:"varint,4,opt,name=follower_num,json=followerNum,proto3" json:"follower_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandListInfo) Reset()         { *m = BrandListInfo{} }
func (m *BrandListInfo) String() string { return proto.CompactTextString(m) }
func (*BrandListInfo) ProtoMessage()    {}
func (*BrandListInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{5}
}
func (m *BrandListInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandListInfo.Unmarshal(m, b)
}
func (m *BrandListInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandListInfo.Marshal(b, m, deterministic)
}
func (dst *BrandListInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandListInfo.Merge(dst, src)
}
func (m *BrandListInfo) XXX_Size() int {
	return xxx_messageInfo_BrandListInfo.Size(m)
}
func (m *BrandListInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandListInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandListInfo proto.InternalMessageInfo

func (m *BrandListInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BrandListInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *BrandListInfo) GetFollowerNum() uint32 {
	if m != nil {
		return m.FollowerNum
	}
	return 0
}

type BrandCategoryListInfo struct {
	Info                 []*BrandListInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Character            string           `protobuf:"bytes,2,opt,name=character,proto3" json:"character,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BrandCategoryListInfo) Reset()         { *m = BrandCategoryListInfo{} }
func (m *BrandCategoryListInfo) String() string { return proto.CompactTextString(m) }
func (*BrandCategoryListInfo) ProtoMessage()    {}
func (*BrandCategoryListInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{6}
}
func (m *BrandCategoryListInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandCategoryListInfo.Unmarshal(m, b)
}
func (m *BrandCategoryListInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandCategoryListInfo.Marshal(b, m, deterministic)
}
func (dst *BrandCategoryListInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandCategoryListInfo.Merge(dst, src)
}
func (m *BrandCategoryListInfo) XXX_Size() int {
	return xxx_messageInfo_BrandCategoryListInfo.Size(m)
}
func (m *BrandCategoryListInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandCategoryListInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandCategoryListInfo proto.InternalMessageInfo

func (m *BrandCategoryListInfo) GetInfo() []*BrandListInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *BrandCategoryListInfo) GetCharacter() string {
	if m != nil {
		return m.Character
	}
	return ""
}

type GetBrandListResp struct {
	BaseResp             *app.BaseResp            `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TopBrandList         []*BrandListInfo         `protobuf:"bytes,2,rep,name=top_brand_list,json=topBrandList,proto3" json:"top_brand_list,omitempty"`
	CategoryBrandList    []*BrandCategoryListInfo `protobuf:"bytes,3,rep,name=category_brand_list,json=categoryBrandList,proto3" json:"category_brand_list,omitempty"`
	Url                  string                   `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	IntroText            string                   `protobuf:"bytes,5,opt,name=IntroText,proto3" json:"IntroText,omitempty"`
	OutSharePicUrl       string                   `protobuf:"bytes,6,opt,name=out_share_pic_url,json=outSharePicUrl,proto3" json:"out_share_pic_url,omitempty"`
	MyBrand              string                   `protobuf:"bytes,7,opt,name=my_brand,json=myBrand,proto3" json:"my_brand,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetBrandListResp) Reset()         { *m = GetBrandListResp{} }
func (m *GetBrandListResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandListResp) ProtoMessage()    {}
func (*GetBrandListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{7}
}
func (m *GetBrandListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandListResp.Unmarshal(m, b)
}
func (m *GetBrandListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandListResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandListResp.Merge(dst, src)
}
func (m *GetBrandListResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandListResp.Size(m)
}
func (m *GetBrandListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandListResp proto.InternalMessageInfo

func (m *GetBrandListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBrandListResp) GetTopBrandList() []*BrandListInfo {
	if m != nil {
		return m.TopBrandList
	}
	return nil
}

func (m *GetBrandListResp) GetCategoryBrandList() []*BrandCategoryListInfo {
	if m != nil {
		return m.CategoryBrandList
	}
	return nil
}

func (m *GetBrandListResp) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GetBrandListResp) GetIntroText() string {
	if m != nil {
		return m.IntroText
	}
	return ""
}

func (m *GetBrandListResp) GetOutSharePicUrl() string {
	if m != nil {
		return m.OutSharePicUrl
	}
	return ""
}

func (m *GetBrandListResp) GetMyBrand() string {
	if m != nil {
		return m.MyBrand
	}
	return ""
}

type GetBrandInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	BrandId              string       `protobuf:"bytes,2,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBrandInfoReq) Reset()         { *m = GetBrandInfoReq{} }
func (m *GetBrandInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandInfoReq) ProtoMessage()    {}
func (*GetBrandInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{8}
}
func (m *GetBrandInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandInfoReq.Unmarshal(m, b)
}
func (m *GetBrandInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandInfoReq.Merge(dst, src)
}
func (m *GetBrandInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandInfoReq.Size(m)
}
func (m *GetBrandInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandInfoReq proto.InternalMessageInfo

func (m *GetBrandInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBrandInfoReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

type BrandMemberInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	FollowerNum          uint32   `protobuf:"varint,4,opt,name=follower_num,json=followerNum,proto3" json:"follower_num,omitempty"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Intro                string   `protobuf:"bytes,6,opt,name=intro,proto3" json:"intro,omitempty"`
	Role                 uint32   `protobuf:"varint,7,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandMemberInfo) Reset()         { *m = BrandMemberInfo{} }
func (m *BrandMemberInfo) String() string { return proto.CompactTextString(m) }
func (*BrandMemberInfo) ProtoMessage()    {}
func (*BrandMemberInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{9}
}
func (m *BrandMemberInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandMemberInfo.Unmarshal(m, b)
}
func (m *BrandMemberInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandMemberInfo.Marshal(b, m, deterministic)
}
func (dst *BrandMemberInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandMemberInfo.Merge(dst, src)
}
func (m *BrandMemberInfo) XXX_Size() int {
	return xxx_messageInfo_BrandMemberInfo.Size(m)
}
func (m *BrandMemberInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandMemberInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandMemberInfo proto.InternalMessageInfo

func (m *BrandMemberInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BrandMemberInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *BrandMemberInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *BrandMemberInfo) GetFollowerNum() uint32 {
	if m != nil {
		return m.FollowerNum
	}
	return 0
}

func (m *BrandMemberInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *BrandMemberInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *BrandMemberInfo) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

type BrandSimpleInfo struct {
	BrandId              string              `protobuf:"bytes,1,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	BrandName            string              `protobuf:"bytes,2,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	Intro                string              `protobuf:"bytes,3,opt,name=intro,proto3" json:"intro,omitempty"`
	FollowerNum          uint32              `protobuf:"varint,4,opt,name=follower_num,json=followerNum,proto3" json:"follower_num,omitempty"`
	MemberList           []*BrandMemberInfo  `protobuf:"bytes,5,rep,name=member_list,json=memberList,proto3" json:"member_list,omitempty"`
	PhotoList            []*PhotoAlbumKeyURL `protobuf:"bytes,6,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BrandSimpleInfo) Reset()         { *m = BrandSimpleInfo{} }
func (m *BrandSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*BrandSimpleInfo) ProtoMessage()    {}
func (*BrandSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{10}
}
func (m *BrandSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandSimpleInfo.Unmarshal(m, b)
}
func (m *BrandSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *BrandSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandSimpleInfo.Merge(dst, src)
}
func (m *BrandSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_BrandSimpleInfo.Size(m)
}
func (m *BrandSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BrandSimpleInfo proto.InternalMessageInfo

func (m *BrandSimpleInfo) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *BrandSimpleInfo) GetBrandName() string {
	if m != nil {
		return m.BrandName
	}
	return ""
}

func (m *BrandSimpleInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *BrandSimpleInfo) GetFollowerNum() uint32 {
	if m != nil {
		return m.FollowerNum
	}
	return 0
}

func (m *BrandSimpleInfo) GetMemberList() []*BrandMemberInfo {
	if m != nil {
		return m.MemberList
	}
	return nil
}

func (m *BrandSimpleInfo) GetPhotoList() []*PhotoAlbumKeyURL {
	if m != nil {
		return m.PhotoList
	}
	return nil
}

type GetBrandInfoResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	BrandInfo            *BrandSimpleInfo `protobuf:"bytes,2,opt,name=brand_info,json=brandInfo,proto3" json:"brand_info,omitempty"`
	Role                 uint32           `protobuf:"varint,3,opt,name=role,proto3" json:"role,omitempty"`
	InSharePicUrl        string           `protobuf:"bytes,4,opt,name=in_share_pic_url,json=inSharePicUrl,proto3" json:"in_share_pic_url,omitempty"`
	RobotUrl             []string         `protobuf:"bytes,5,rep,name=robot_url,json=robotUrl,proto3" json:"robot_url,omitempty"`
	TopicId              string           `protobuf:"bytes,6,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ViewCount            uint32           `protobuf:"varint,7,opt,name=view_count,json=viewCount,proto3" json:"view_count,omitempty"`
	BrandIntegral        int32            `protobuf:"varint,8,opt,name=brand_integral,json=brandIntegral,proto3" json:"brand_integral,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetBrandInfoResp) Reset()         { *m = GetBrandInfoResp{} }
func (m *GetBrandInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandInfoResp) ProtoMessage()    {}
func (*GetBrandInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{11}
}
func (m *GetBrandInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandInfoResp.Unmarshal(m, b)
}
func (m *GetBrandInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandInfoResp.Merge(dst, src)
}
func (m *GetBrandInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandInfoResp.Size(m)
}
func (m *GetBrandInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandInfoResp proto.InternalMessageInfo

func (m *GetBrandInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBrandInfoResp) GetBrandInfo() *BrandSimpleInfo {
	if m != nil {
		return m.BrandInfo
	}
	return nil
}

func (m *GetBrandInfoResp) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *GetBrandInfoResp) GetInSharePicUrl() string {
	if m != nil {
		return m.InSharePicUrl
	}
	return ""
}

func (m *GetBrandInfoResp) GetRobotUrl() []string {
	if m != nil {
		return m.RobotUrl
	}
	return nil
}

func (m *GetBrandInfoResp) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetBrandInfoResp) GetViewCount() uint32 {
	if m != nil {
		return m.ViewCount
	}
	return 0
}

func (m *GetBrandInfoResp) GetBrandIntegral() int32 {
	if m != nil {
		return m.BrandIntegral
	}
	return 0
}

// 当要修改厂牌成员介绍时，uid!=0，且要带上brand_id
type UpdateBrandInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	BrandId              string       `protobuf:"bytes,2,opt,name=brand_id,json=brandId,proto3" json:"brand_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Intro                string       `protobuf:"bytes,4,opt,name=intro,proto3" json:"intro,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateBrandInfoReq) Reset()         { *m = UpdateBrandInfoReq{} }
func (m *UpdateBrandInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBrandInfoReq) ProtoMessage()    {}
func (*UpdateBrandInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{12}
}
func (m *UpdateBrandInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBrandInfoReq.Unmarshal(m, b)
}
func (m *UpdateBrandInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBrandInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBrandInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBrandInfoReq.Merge(dst, src)
}
func (m *UpdateBrandInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBrandInfoReq.Size(m)
}
func (m *UpdateBrandInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBrandInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBrandInfoReq proto.InternalMessageInfo

func (m *UpdateBrandInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdateBrandInfoReq) GetBrandId() string {
	if m != nil {
		return m.BrandId
	}
	return ""
}

func (m *UpdateBrandInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateBrandInfoReq) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

type UpdateBrandInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateBrandInfoResp) Reset()         { *m = UpdateBrandInfoResp{} }
func (m *UpdateBrandInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBrandInfoResp) ProtoMessage()    {}
func (*UpdateBrandInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{13}
}
func (m *UpdateBrandInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBrandInfoResp.Unmarshal(m, b)
}
func (m *UpdateBrandInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBrandInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBrandInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBrandInfoResp.Merge(dst, src)
}
func (m *UpdateBrandInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBrandInfoResp.Size(m)
}
func (m *UpdateBrandInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBrandInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBrandInfoResp proto.InternalMessageInfo

func (m *UpdateBrandInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 用户厂牌
type UserBrandInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type                 string   `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Attr                 string   `protobuf:"bytes,4,opt,name=attr,proto3" json:"attr,omitempty"`
	AttrInfo             string   `protobuf:"bytes,5,opt,name=attr_info,json=attrInfo,proto3" json:"attr_info,omitempty"`
	Role                 string   `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBrandInfo) Reset()         { *m = UserBrandInfo{} }
func (m *UserBrandInfo) String() string { return proto.CompactTextString(m) }
func (*UserBrandInfo) ProtoMessage()    {}
func (*UserBrandInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{14}
}
func (m *UserBrandInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBrandInfo.Unmarshal(m, b)
}
func (m *UserBrandInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBrandInfo.Marshal(b, m, deterministic)
}
func (dst *UserBrandInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBrandInfo.Merge(dst, src)
}
func (m *UserBrandInfo) XXX_Size() int {
	return xxx_messageInfo_UserBrandInfo.Size(m)
}
func (m *UserBrandInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBrandInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserBrandInfo proto.InternalMessageInfo

func (m *UserBrandInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UserBrandInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UserBrandInfo) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *UserBrandInfo) GetAttr() string {
	if m != nil {
		return m.Attr
	}
	return ""
}

func (m *UserBrandInfo) GetAttrInfo() string {
	if m != nil {
		return m.AttrInfo
	}
	return ""
}

func (m *UserBrandInfo) GetRole() string {
	if m != nil {
		return m.Role
	}
	return ""
}

type GetUserBrandInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserBrandInfoReq) Reset()         { *m = GetUserBrandInfoReq{} }
func (m *GetUserBrandInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBrandInfoReq) ProtoMessage()    {}
func (*GetUserBrandInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{15}
}
func (m *GetUserBrandInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBrandInfoReq.Unmarshal(m, b)
}
func (m *GetUserBrandInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBrandInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBrandInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBrandInfoReq.Merge(dst, src)
}
func (m *GetUserBrandInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBrandInfoReq.Size(m)
}
func (m *GetUserBrandInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBrandInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBrandInfoReq proto.InternalMessageInfo

func (m *GetUserBrandInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserBrandInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserBrandInfoResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *UserBrandInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserBrandInfoResp) Reset()         { *m = GetUserBrandInfoResp{} }
func (m *GetUserBrandInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBrandInfoResp) ProtoMessage()    {}
func (*GetUserBrandInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{16}
}
func (m *GetUserBrandInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBrandInfoResp.Unmarshal(m, b)
}
func (m *GetUserBrandInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBrandInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBrandInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBrandInfoResp.Merge(dst, src)
}
func (m *GetUserBrandInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBrandInfoResp.Size(m)
}
func (m *GetUserBrandInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBrandInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBrandInfoResp proto.InternalMessageInfo

func (m *GetUserBrandInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserBrandInfoResp) GetInfo() *UserBrandInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// message GetCommonPhotoAlbumReq{
//    ga.BaseReq base_req = 1;
//    string id = 2;
//    uint32 album_type = 3; /* PhotoAlbumType */
// }
type PhotoAlbumKeyURL struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PhotoAlbumKeyURL) Reset()         { *m = PhotoAlbumKeyURL{} }
func (m *PhotoAlbumKeyURL) String() string { return proto.CompactTextString(m) }
func (*PhotoAlbumKeyURL) ProtoMessage()    {}
func (*PhotoAlbumKeyURL) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{17}
}
func (m *PhotoAlbumKeyURL) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PhotoAlbumKeyURL.Unmarshal(m, b)
}
func (m *PhotoAlbumKeyURL) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PhotoAlbumKeyURL.Marshal(b, m, deterministic)
}
func (dst *PhotoAlbumKeyURL) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PhotoAlbumKeyURL.Merge(dst, src)
}
func (m *PhotoAlbumKeyURL) XXX_Size() int {
	return xxx_messageInfo_PhotoAlbumKeyURL.Size(m)
}
func (m *PhotoAlbumKeyURL) XXX_DiscardUnknown() {
	xxx_messageInfo_PhotoAlbumKeyURL.DiscardUnknown(m)
}

var xxx_messageInfo_PhotoAlbumKeyURL proto.InternalMessageInfo

func (m *PhotoAlbumKeyURL) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *PhotoAlbumKeyURL) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type UpdateCommonPhotoAlbumReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Id                   string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	ImgKeyList           []string     `protobuf:"bytes,4,rep,name=img_key_list,json=imgKeyList,proto3" json:"img_key_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateCommonPhotoAlbumReq) Reset()         { *m = UpdateCommonPhotoAlbumReq{} }
func (m *UpdateCommonPhotoAlbumReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCommonPhotoAlbumReq) ProtoMessage()    {}
func (*UpdateCommonPhotoAlbumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{18}
}
func (m *UpdateCommonPhotoAlbumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommonPhotoAlbumReq.Unmarshal(m, b)
}
func (m *UpdateCommonPhotoAlbumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommonPhotoAlbumReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCommonPhotoAlbumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommonPhotoAlbumReq.Merge(dst, src)
}
func (m *UpdateCommonPhotoAlbumReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCommonPhotoAlbumReq.Size(m)
}
func (m *UpdateCommonPhotoAlbumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommonPhotoAlbumReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommonPhotoAlbumReq proto.InternalMessageInfo

func (m *UpdateCommonPhotoAlbumReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UpdateCommonPhotoAlbumReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateCommonPhotoAlbumReq) GetImgKeyList() []string {
	if m != nil {
		return m.ImgKeyList
	}
	return nil
}

type UpdateCommonPhotoAlbumResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateCommonPhotoAlbumResp) Reset()         { *m = UpdateCommonPhotoAlbumResp{} }
func (m *UpdateCommonPhotoAlbumResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCommonPhotoAlbumResp) ProtoMessage()    {}
func (*UpdateCommonPhotoAlbumResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{19}
}
func (m *UpdateCommonPhotoAlbumResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommonPhotoAlbumResp.Unmarshal(m, b)
}
func (m *UpdateCommonPhotoAlbumResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommonPhotoAlbumResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCommonPhotoAlbumResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommonPhotoAlbumResp.Merge(dst, src)
}
func (m *UpdateCommonPhotoAlbumResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCommonPhotoAlbumResp.Size(m)
}
func (m *UpdateCommonPhotoAlbumResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommonPhotoAlbumResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommonPhotoAlbumResp proto.InternalMessageInfo

func (m *UpdateCommonPhotoAlbumResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 音乐专区
type GetMusicZoneTemplateReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Id                   string       `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicZoneTemplateReq) Reset()         { *m = GetMusicZoneTemplateReq{} }
func (m *GetMusicZoneTemplateReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicZoneTemplateReq) ProtoMessage()    {}
func (*GetMusicZoneTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{20}
}
func (m *GetMusicZoneTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicZoneTemplateReq.Unmarshal(m, b)
}
func (m *GetMusicZoneTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicZoneTemplateReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicZoneTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicZoneTemplateReq.Merge(dst, src)
}
func (m *GetMusicZoneTemplateReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicZoneTemplateReq.Size(m)
}
func (m *GetMusicZoneTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicZoneTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicZoneTemplateReq proto.InternalMessageInfo

func (m *GetMusicZoneTemplateReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicZoneTemplateReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type GetMusicZoneTemplateResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ZoneName             string             `protobuf:"bytes,2,opt,name=zone_name,json=zoneName,proto3" json:"zone_name,omitempty"`
	BannerId             uint32             `protobuf:"varint,3,opt,name=banner_id,json=bannerId,proto3" json:"banner_id,omitempty"`
	TabReqStr            string             `protobuf:"bytes,4,opt,name=tab_req_str,json=tabReqStr,proto3" json:"tab_req_str,omitempty"`
	RoomUrl              string             `protobuf:"bytes,5,opt,name=room_url,json=roomUrl,proto3" json:"room_url,omitempty"`
	RoomTabName          string             `protobuf:"bytes,6,opt,name=room_tab_name,json=roomTabName,proto3" json:"room_tab_name,omitempty"`
	FindRoomName         string             `protobuf:"bytes,7,opt,name=find_room_name,json=findRoomName,proto3" json:"find_room_name,omitempty"`
	FindRoomId           string             `protobuf:"bytes,8,opt,name=find_room_id,json=findRoomId,proto3" json:"find_room_id,omitempty"`
	StarFlag             bool               `protobuf:"varint,9,opt,name=star_flag,json=starFlag,proto3" json:"star_flag,omitempty"`
	BackgroundImageUrl   string             `protobuf:"bytes,10,opt,name=background_image_url,json=backgroundImageUrl,proto3" json:"background_image_url,omitempty"`
	ColorConfiguration   ColorConfiguration `protobuf:"varint,11,opt,name=color_configuration,json=colorConfiguration,proto3,enum=ga.rhythm.ColorConfiguration" json:"color_configuration,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetMusicZoneTemplateResp) Reset()         { *m = GetMusicZoneTemplateResp{} }
func (m *GetMusicZoneTemplateResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicZoneTemplateResp) ProtoMessage()    {}
func (*GetMusicZoneTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{21}
}
func (m *GetMusicZoneTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicZoneTemplateResp.Unmarshal(m, b)
}
func (m *GetMusicZoneTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicZoneTemplateResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicZoneTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicZoneTemplateResp.Merge(dst, src)
}
func (m *GetMusicZoneTemplateResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicZoneTemplateResp.Size(m)
}
func (m *GetMusicZoneTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicZoneTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicZoneTemplateResp proto.InternalMessageInfo

func (m *GetMusicZoneTemplateResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicZoneTemplateResp) GetZoneName() string {
	if m != nil {
		return m.ZoneName
	}
	return ""
}

func (m *GetMusicZoneTemplateResp) GetBannerId() uint32 {
	if m != nil {
		return m.BannerId
	}
	return 0
}

func (m *GetMusicZoneTemplateResp) GetTabReqStr() string {
	if m != nil {
		return m.TabReqStr
	}
	return ""
}

func (m *GetMusicZoneTemplateResp) GetRoomUrl() string {
	if m != nil {
		return m.RoomUrl
	}
	return ""
}

func (m *GetMusicZoneTemplateResp) GetRoomTabName() string {
	if m != nil {
		return m.RoomTabName
	}
	return ""
}

func (m *GetMusicZoneTemplateResp) GetFindRoomName() string {
	if m != nil {
		return m.FindRoomName
	}
	return ""
}

func (m *GetMusicZoneTemplateResp) GetFindRoomId() string {
	if m != nil {
		return m.FindRoomId
	}
	return ""
}

func (m *GetMusicZoneTemplateResp) GetStarFlag() bool {
	if m != nil {
		return m.StarFlag
	}
	return false
}

func (m *GetMusicZoneTemplateResp) GetBackgroundImageUrl() string {
	if m != nil {
		return m.BackgroundImageUrl
	}
	return ""
}

func (m *GetMusicZoneTemplateResp) GetColorConfiguration() ColorConfiguration {
	if m != nil {
		return m.ColorConfiguration
	}
	return ColorConfiguration_ColorWhite
}

// 可乐玩法
// 用户新增可乐
type AddCokeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ActId                uint32       `protobuf:"varint,3,opt,name=act_id,json=actId,proto3" json:"act_id,omitempty"`
	CokeNum              uint32       `protobuf:"varint,4,opt,name=coke_num,json=cokeNum,proto3" json:"coke_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddCokeReq) Reset()         { *m = AddCokeReq{} }
func (m *AddCokeReq) String() string { return proto.CompactTextString(m) }
func (*AddCokeReq) ProtoMessage()    {}
func (*AddCokeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{22}
}
func (m *AddCokeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCokeReq.Unmarshal(m, b)
}
func (m *AddCokeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCokeReq.Marshal(b, m, deterministic)
}
func (dst *AddCokeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCokeReq.Merge(dst, src)
}
func (m *AddCokeReq) XXX_Size() int {
	return xxx_messageInfo_AddCokeReq.Size(m)
}
func (m *AddCokeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCokeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCokeReq proto.InternalMessageInfo

func (m *AddCokeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AddCokeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddCokeReq) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *AddCokeReq) GetCokeNum() uint32 {
	if m != nil {
		return m.CokeNum
	}
	return 0
}

type AddCokeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LeftCoke             uint32        `protobuf:"varint,2,opt,name=left_coke,json=leftCoke,proto3" json:"left_coke,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddCokeResp) Reset()         { *m = AddCokeResp{} }
func (m *AddCokeResp) String() string { return proto.CompactTextString(m) }
func (*AddCokeResp) ProtoMessage()    {}
func (*AddCokeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{23}
}
func (m *AddCokeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCokeResp.Unmarshal(m, b)
}
func (m *AddCokeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCokeResp.Marshal(b, m, deterministic)
}
func (dst *AddCokeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCokeResp.Merge(dst, src)
}
func (m *AddCokeResp) XXX_Size() int {
	return xxx_messageInfo_AddCokeResp.Size(m)
}
func (m *AddCokeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCokeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCokeResp proto.InternalMessageInfo

func (m *AddCokeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *AddCokeResp) GetLeftCoke() uint32 {
	if m != nil {
		return m.LeftCoke
	}
	return 0
}

// 用户赠送可乐到麦上用户
type SendCokeReq struct {
	BaseReq              *app.BaseReq      `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32            `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ActId                uint32            `protobuf:"varint,3,opt,name=act_id,json=actId,proto3" json:"act_id,omitempty"`
	MapUidGainCoke       map[uint32]uint32 `protobuf:"bytes,4,rep,name=map_uid_gain_coke,json=mapUidGainCoke,proto3" json:"map_uid_gain_coke,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SendCokeReq) Reset()         { *m = SendCokeReq{} }
func (m *SendCokeReq) String() string { return proto.CompactTextString(m) }
func (*SendCokeReq) ProtoMessage()    {}
func (*SendCokeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{24}
}
func (m *SendCokeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCokeReq.Unmarshal(m, b)
}
func (m *SendCokeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCokeReq.Marshal(b, m, deterministic)
}
func (dst *SendCokeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCokeReq.Merge(dst, src)
}
func (m *SendCokeReq) XXX_Size() int {
	return xxx_messageInfo_SendCokeReq.Size(m)
}
func (m *SendCokeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCokeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendCokeReq proto.InternalMessageInfo

func (m *SendCokeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendCokeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendCokeReq) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *SendCokeReq) GetMapUidGainCoke() map[uint32]uint32 {
	if m != nil {
		return m.MapUidGainCoke
	}
	return nil
}

type SendCokeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LeftCoke             uint32        `protobuf:"varint,2,opt,name=left_coke,json=leftCoke,proto3" json:"left_coke,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SendCokeResp) Reset()         { *m = SendCokeResp{} }
func (m *SendCokeResp) String() string { return proto.CompactTextString(m) }
func (*SendCokeResp) ProtoMessage()    {}
func (*SendCokeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{25}
}
func (m *SendCokeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCokeResp.Unmarshal(m, b)
}
func (m *SendCokeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCokeResp.Marshal(b, m, deterministic)
}
func (dst *SendCokeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCokeResp.Merge(dst, src)
}
func (m *SendCokeResp) XXX_Size() int {
	return xxx_messageInfo_SendCokeResp.Size(m)
}
func (m *SendCokeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCokeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendCokeResp proto.InternalMessageInfo

func (m *SendCokeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendCokeResp) GetLeftCoke() uint32 {
	if m != nil {
		return m.LeftCoke
	}
	return 0
}

type SendCokeToUserNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FromUid              uint32   `protobuf:"varint,2,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromNickname         string   `protobuf:"bytes,3,opt,name=from_nickname,json=fromNickname,proto3" json:"from_nickname,omitempty"`
	FromAccount          string   `protobuf:"bytes,4,opt,name=from_account,json=fromAccount,proto3" json:"from_account,omitempty"`
	ToUid                uint32   `protobuf:"varint,5,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToNickname           string   `protobuf:"bytes,6,opt,name=to_nickname,json=toNickname,proto3" json:"to_nickname,omitempty"`
	ToAccount            string   `protobuf:"bytes,7,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	CokeCnt              uint32   `protobuf:"varint,8,opt,name=coke_cnt,json=cokeCnt,proto3" json:"coke_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendCokeToUserNotify) Reset()         { *m = SendCokeToUserNotify{} }
func (m *SendCokeToUserNotify) String() string { return proto.CompactTextString(m) }
func (*SendCokeToUserNotify) ProtoMessage()    {}
func (*SendCokeToUserNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{26}
}
func (m *SendCokeToUserNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendCokeToUserNotify.Unmarshal(m, b)
}
func (m *SendCokeToUserNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendCokeToUserNotify.Marshal(b, m, deterministic)
}
func (dst *SendCokeToUserNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendCokeToUserNotify.Merge(dst, src)
}
func (m *SendCokeToUserNotify) XXX_Size() int {
	return xxx_messageInfo_SendCokeToUserNotify.Size(m)
}
func (m *SendCokeToUserNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_SendCokeToUserNotify.DiscardUnknown(m)
}

var xxx_messageInfo_SendCokeToUserNotify proto.InternalMessageInfo

func (m *SendCokeToUserNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SendCokeToUserNotify) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *SendCokeToUserNotify) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *SendCokeToUserNotify) GetFromAccount() string {
	if m != nil {
		return m.FromAccount
	}
	return ""
}

func (m *SendCokeToUserNotify) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *SendCokeToUserNotify) GetToNickname() string {
	if m != nil {
		return m.ToNickname
	}
	return ""
}

func (m *SendCokeToUserNotify) GetToAccount() string {
	if m != nil {
		return m.ToAccount
	}
	return ""
}

func (m *SendCokeToUserNotify) GetCokeCnt() uint32 {
	if m != nil {
		return m.CokeCnt
	}
	return 0
}

// ------------------ 自定义投票 ------------------
type UserDefinedVotePkOptionInfo struct {
	OptionName           string   `protobuf:"bytes,1,opt,name=option_name,json=optionName,proto3" json:"option_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDefinedVotePkOptionInfo) Reset()         { *m = UserDefinedVotePkOptionInfo{} }
func (m *UserDefinedVotePkOptionInfo) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePkOptionInfo) ProtoMessage()    {}
func (*UserDefinedVotePkOptionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{27}
}
func (m *UserDefinedVotePkOptionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePkOptionInfo.Unmarshal(m, b)
}
func (m *UserDefinedVotePkOptionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePkOptionInfo.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePkOptionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePkOptionInfo.Merge(dst, src)
}
func (m *UserDefinedVotePkOptionInfo) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePkOptionInfo.Size(m)
}
func (m *UserDefinedVotePkOptionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePkOptionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePkOptionInfo proto.InternalMessageInfo

func (m *UserDefinedVotePkOptionInfo) GetOptionName() string {
	if m != nil {
		return m.OptionName
	}
	return ""
}

type UserDefinedVotePKStartReq struct {
	BaseReq              *app.BaseReq                   `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32                         `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DurationMin          uint32                         `protobuf:"varint,3,opt,name=duration_min,json=durationMin,proto3" json:"duration_min,omitempty"`
	PkType               uint32                         `protobuf:"varint,4,opt,name=pk_type,json=pkType,proto3" json:"pk_type,omitempty"`
	OptionList           []*UserDefinedVotePkOptionInfo `protobuf:"bytes,5,rep,name=option_list,json=optionList,proto3" json:"option_list,omitempty"`
	VoteCnt              uint32                         `protobuf:"varint,6,opt,name=vote_cnt,json=voteCnt,proto3" json:"vote_cnt,omitempty"`
	PkName               string                         `protobuf:"bytes,7,opt,name=pk_name,json=pkName,proto3" json:"pk_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *UserDefinedVotePKStartReq) Reset()         { *m = UserDefinedVotePKStartReq{} }
func (m *UserDefinedVotePKStartReq) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKStartReq) ProtoMessage()    {}
func (*UserDefinedVotePKStartReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{28}
}
func (m *UserDefinedVotePKStartReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKStartReq.Unmarshal(m, b)
}
func (m *UserDefinedVotePKStartReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKStartReq.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKStartReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKStartReq.Merge(dst, src)
}
func (m *UserDefinedVotePKStartReq) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKStartReq.Size(m)
}
func (m *UserDefinedVotePKStartReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKStartReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKStartReq proto.InternalMessageInfo

func (m *UserDefinedVotePKStartReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UserDefinedVotePKStartReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVotePKStartReq) GetDurationMin() uint32 {
	if m != nil {
		return m.DurationMin
	}
	return 0
}

func (m *UserDefinedVotePKStartReq) GetPkType() uint32 {
	if m != nil {
		return m.PkType
	}
	return 0
}

func (m *UserDefinedVotePKStartReq) GetOptionList() []*UserDefinedVotePkOptionInfo {
	if m != nil {
		return m.OptionList
	}
	return nil
}

func (m *UserDefinedVotePKStartReq) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

func (m *UserDefinedVotePKStartReq) GetPkName() string {
	if m != nil {
		return m.PkName
	}
	return ""
}

type UserDefinedVotePKStartResp struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChannelId            uint32                `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTimestamp       uint32                `protobuf:"varint,3,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	OptionList           []*UserDefinedOptInfo `protobuf:"bytes,4,rep,name=option_list,json=optionList,proto3" json:"option_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *UserDefinedVotePKStartResp) Reset()         { *m = UserDefinedVotePKStartResp{} }
func (m *UserDefinedVotePKStartResp) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKStartResp) ProtoMessage()    {}
func (*UserDefinedVotePKStartResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{29}
}
func (m *UserDefinedVotePKStartResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKStartResp.Unmarshal(m, b)
}
func (m *UserDefinedVotePKStartResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKStartResp.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKStartResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKStartResp.Merge(dst, src)
}
func (m *UserDefinedVotePKStartResp) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKStartResp.Size(m)
}
func (m *UserDefinedVotePKStartResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKStartResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKStartResp proto.InternalMessageInfo

func (m *UserDefinedVotePKStartResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UserDefinedVotePKStartResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVotePKStartResp) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *UserDefinedVotePKStartResp) GetOptionList() []*UserDefinedOptInfo {
	if m != nil {
		return m.OptionList
	}
	return nil
}

type UserDefinedCompetitor struct {
	OptInfo              *UserDefinedOptInfo `protobuf:"bytes,1,opt,name=opt_info,json=optInfo,proto3" json:"opt_info,omitempty"`
	Vote                 uint32              `protobuf:"varint,2,opt,name=vote,proto3" json:"vote,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserDefinedCompetitor) Reset()         { *m = UserDefinedCompetitor{} }
func (m *UserDefinedCompetitor) String() string { return proto.CompactTextString(m) }
func (*UserDefinedCompetitor) ProtoMessage()    {}
func (*UserDefinedCompetitor) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{30}
}
func (m *UserDefinedCompetitor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedCompetitor.Unmarshal(m, b)
}
func (m *UserDefinedCompetitor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedCompetitor.Marshal(b, m, deterministic)
}
func (dst *UserDefinedCompetitor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedCompetitor.Merge(dst, src)
}
func (m *UserDefinedCompetitor) XXX_Size() int {
	return xxx_messageInfo_UserDefinedCompetitor.Size(m)
}
func (m *UserDefinedCompetitor) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedCompetitor.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedCompetitor proto.InternalMessageInfo

func (m *UserDefinedCompetitor) GetOptInfo() *UserDefinedOptInfo {
	if m != nil {
		return m.OptInfo
	}
	return nil
}

func (m *UserDefinedCompetitor) GetVote() uint32 {
	if m != nil {
		return m.Vote
	}
	return 0
}

type UserDefinedOptInfo struct {
	OptId                uint32                       `protobuf:"varint,1,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	OptInfo              *UserDefinedVotePkOptionInfo `protobuf:"bytes,2,opt,name=opt_info,json=optInfo,proto3" json:"opt_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UserDefinedOptInfo) Reset()         { *m = UserDefinedOptInfo{} }
func (m *UserDefinedOptInfo) String() string { return proto.CompactTextString(m) }
func (*UserDefinedOptInfo) ProtoMessage()    {}
func (*UserDefinedOptInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{31}
}
func (m *UserDefinedOptInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedOptInfo.Unmarshal(m, b)
}
func (m *UserDefinedOptInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedOptInfo.Marshal(b, m, deterministic)
}
func (dst *UserDefinedOptInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedOptInfo.Merge(dst, src)
}
func (m *UserDefinedOptInfo) XXX_Size() int {
	return xxx_messageInfo_UserDefinedOptInfo.Size(m)
}
func (m *UserDefinedOptInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedOptInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedOptInfo proto.InternalMessageInfo

func (m *UserDefinedOptInfo) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *UserDefinedOptInfo) GetOptInfo() *UserDefinedVotePkOptionInfo {
	if m != nil {
		return m.OptInfo
	}
	return nil
}

type UserDefinedVotePKInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DurationMin          uint32   `protobuf:"varint,3,opt,name=duration_min,json=durationMin,proto3" json:"duration_min,omitempty"`
	StartTimestamp       uint32   `protobuf:"varint,4,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	VoteCnt              uint32   `protobuf:"varint,5,opt,name=vote_cnt,json=voteCnt,proto3" json:"vote_cnt,omitempty"`
	PkName               string   `protobuf:"bytes,6,opt,name=pk_name,json=pkName,proto3" json:"pk_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDefinedVotePKInfo) Reset()         { *m = UserDefinedVotePKInfo{} }
func (m *UserDefinedVotePKInfo) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKInfo) ProtoMessage()    {}
func (*UserDefinedVotePKInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{32}
}
func (m *UserDefinedVotePKInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKInfo.Unmarshal(m, b)
}
func (m *UserDefinedVotePKInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKInfo.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKInfo.Merge(dst, src)
}
func (m *UserDefinedVotePKInfo) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKInfo.Size(m)
}
func (m *UserDefinedVotePKInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKInfo proto.InternalMessageInfo

func (m *UserDefinedVotePKInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetDurationMin() uint32 {
	if m != nil {
		return m.DurationMin
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

func (m *UserDefinedVotePKInfo) GetPkName() string {
	if m != nil {
		return m.PkName
	}
	return ""
}

// 全量信息
type UserDefinedPKRankInfo struct {
	Info                 *UserDefinedVotePKInfo   `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	CompetitorList       []*UserDefinedCompetitor `protobuf:"bytes,2,rep,name=competitor_list,json=competitorList,proto3" json:"competitor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UserDefinedPKRankInfo) Reset()         { *m = UserDefinedPKRankInfo{} }
func (m *UserDefinedPKRankInfo) String() string { return proto.CompactTextString(m) }
func (*UserDefinedPKRankInfo) ProtoMessage()    {}
func (*UserDefinedPKRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{33}
}
func (m *UserDefinedPKRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedPKRankInfo.Unmarshal(m, b)
}
func (m *UserDefinedPKRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedPKRankInfo.Marshal(b, m, deterministic)
}
func (dst *UserDefinedPKRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedPKRankInfo.Merge(dst, src)
}
func (m *UserDefinedPKRankInfo) XXX_Size() int {
	return xxx_messageInfo_UserDefinedPKRankInfo.Size(m)
}
func (m *UserDefinedPKRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedPKRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedPKRankInfo proto.InternalMessageInfo

func (m *UserDefinedPKRankInfo) GetInfo() *UserDefinedVotePKInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *UserDefinedPKRankInfo) GetCompetitorList() []*UserDefinedCompetitor {
	if m != nil {
		return m.CompetitorList
	}
	return nil
}

type GetUserDefinedVotePKReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserDefinedVotePKReq) Reset()         { *m = GetUserDefinedVotePKReq{} }
func (m *GetUserDefinedVotePKReq) String() string { return proto.CompactTextString(m) }
func (*GetUserDefinedVotePKReq) ProtoMessage()    {}
func (*GetUserDefinedVotePKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{34}
}
func (m *GetUserDefinedVotePKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDefinedVotePKReq.Unmarshal(m, b)
}
func (m *GetUserDefinedVotePKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDefinedVotePKReq.Marshal(b, m, deterministic)
}
func (dst *GetUserDefinedVotePKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDefinedVotePKReq.Merge(dst, src)
}
func (m *GetUserDefinedVotePKReq) XXX_Size() int {
	return xxx_messageInfo_GetUserDefinedVotePKReq.Size(m)
}
func (m *GetUserDefinedVotePKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDefinedVotePKReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDefinedVotePKReq proto.InternalMessageInfo

func (m *GetUserDefinedVotePKReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserDefinedVotePKReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserDefinedVotePKResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LeftVote             uint32                 `protobuf:"varint,2,opt,name=left_vote,json=leftVote,proto3" json:"left_vote,omitempty"`
	PkInfo               *UserDefinedPKRankInfo `protobuf:"bytes,3,opt,name=pk_info,json=pkInfo,proto3" json:"pk_info,omitempty"`
	AddTicketInfo        *StayAddTicketInfo     `protobuf:"bytes,4,opt,name=add_ticket_info,json=addTicketInfo,proto3" json:"add_ticket_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetUserDefinedVotePKResp) Reset()         { *m = GetUserDefinedVotePKResp{} }
func (m *GetUserDefinedVotePKResp) String() string { return proto.CompactTextString(m) }
func (*GetUserDefinedVotePKResp) ProtoMessage()    {}
func (*GetUserDefinedVotePKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{35}
}
func (m *GetUserDefinedVotePKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserDefinedVotePKResp.Unmarshal(m, b)
}
func (m *GetUserDefinedVotePKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserDefinedVotePKResp.Marshal(b, m, deterministic)
}
func (dst *GetUserDefinedVotePKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserDefinedVotePKResp.Merge(dst, src)
}
func (m *GetUserDefinedVotePKResp) XXX_Size() int {
	return xxx_messageInfo_GetUserDefinedVotePKResp.Size(m)
}
func (m *GetUserDefinedVotePKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserDefinedVotePKResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserDefinedVotePKResp proto.InternalMessageInfo

func (m *GetUserDefinedVotePKResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserDefinedVotePKResp) GetLeftVote() uint32 {
	if m != nil {
		return m.LeftVote
	}
	return 0
}

func (m *GetUserDefinedVotePKResp) GetPkInfo() *UserDefinedPKRankInfo {
	if m != nil {
		return m.PkInfo
	}
	return nil
}

func (m *GetUserDefinedVotePKResp) GetAddTicketInfo() *StayAddTicketInfo {
	if m != nil {
		return m.AddTicketInfo
	}
	return nil
}

// 投票
type UserDefinedVoteReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	OptId                uint32       `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	VoteCnt              uint32       `protobuf:"varint,3,opt,name=vote_cnt,json=voteCnt,proto3" json:"vote_cnt,omitempty"`
	ChannelId            uint32       `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTimestamp       uint32       `protobuf:"varint,5,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UserDefinedVoteReq) Reset()         { *m = UserDefinedVoteReq{} }
func (m *UserDefinedVoteReq) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVoteReq) ProtoMessage()    {}
func (*UserDefinedVoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{36}
}
func (m *UserDefinedVoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVoteReq.Unmarshal(m, b)
}
func (m *UserDefinedVoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVoteReq.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVoteReq.Merge(dst, src)
}
func (m *UserDefinedVoteReq) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVoteReq.Size(m)
}
func (m *UserDefinedVoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVoteReq proto.InternalMessageInfo

func (m *UserDefinedVoteReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UserDefinedVoteReq) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *UserDefinedVoteReq) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

func (m *UserDefinedVoteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVoteReq) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

type UserDefinedVoteResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LeftVote             uint32        `protobuf:"varint,2,opt,name=left_vote,json=leftVote,proto3" json:"left_vote,omitempty"`
	RemainExtraVote      uint32        `protobuf:"varint,3,opt,name=remain_extra_vote,json=remainExtraVote,proto3" json:"remain_extra_vote,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserDefinedVoteResp) Reset()         { *m = UserDefinedVoteResp{} }
func (m *UserDefinedVoteResp) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVoteResp) ProtoMessage()    {}
func (*UserDefinedVoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{37}
}
func (m *UserDefinedVoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVoteResp.Unmarshal(m, b)
}
func (m *UserDefinedVoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVoteResp.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVoteResp.Merge(dst, src)
}
func (m *UserDefinedVoteResp) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVoteResp.Size(m)
}
func (m *UserDefinedVoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVoteResp proto.InternalMessageInfo

func (m *UserDefinedVoteResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *UserDefinedVoteResp) GetLeftVote() uint32 {
	if m != nil {
		return m.LeftVote
	}
	return 0
}

func (m *UserDefinedVoteResp) GetRemainExtraVote() uint32 {
	if m != nil {
		return m.RemainExtraVote
	}
	return 0
}

type UserDefinedVotePKCancelReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTimestamp       uint32       `protobuf:"varint,3,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UserDefinedVotePKCancelReq) Reset()         { *m = UserDefinedVotePKCancelReq{} }
func (m *UserDefinedVotePKCancelReq) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKCancelReq) ProtoMessage()    {}
func (*UserDefinedVotePKCancelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{38}
}
func (m *UserDefinedVotePKCancelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKCancelReq.Unmarshal(m, b)
}
func (m *UserDefinedVotePKCancelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKCancelReq.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKCancelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKCancelReq.Merge(dst, src)
}
func (m *UserDefinedVotePKCancelReq) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKCancelReq.Size(m)
}
func (m *UserDefinedVotePKCancelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKCancelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKCancelReq proto.InternalMessageInfo

func (m *UserDefinedVotePKCancelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *UserDefinedVotePKCancelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserDefinedVotePKCancelReq) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

type UserDefinedVotePKCancelResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UserDefinedVotePKCancelResp) Reset()         { *m = UserDefinedVotePKCancelResp{} }
func (m *UserDefinedVotePKCancelResp) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKCancelResp) ProtoMessage()    {}
func (*UserDefinedVotePKCancelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{39}
}
func (m *UserDefinedVotePKCancelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKCancelResp.Unmarshal(m, b)
}
func (m *UserDefinedVotePKCancelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKCancelResp.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKCancelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKCancelResp.Merge(dst, src)
}
func (m *UserDefinedVotePKCancelResp) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKCancelResp.Size(m)
}
func (m *UserDefinedVotePKCancelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKCancelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKCancelResp proto.InternalMessageInfo

func (m *UserDefinedVotePKCancelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 自定义PK全量PUSH信息
type UserDefinedVotePKNotify struct {
	PkInfo       *UserDefinedPKRankInfo `protobuf:"bytes,1,opt,name=pk_info,json=pkInfo,proto3" json:"pk_info,omitempty"`
	NotifyStatus uint32                 `protobuf:"varint,2,opt,name=notify_status,json=notifyStatus,proto3" json:"notify_status,omitempty"`
	// 操作者数据 仅在PK 开始和被取消时 需要填写
	OpUid                uint32   `protobuf:"varint,3,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	OpNickname           string   `protobuf:"bytes,4,opt,name=op_nickname,json=opNickname,proto3" json:"op_nickname,omitempty"`
	OpAccount            string   `protobuf:"bytes,5,opt,name=op_account,json=opAccount,proto3" json:"op_account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDefinedVotePKNotify) Reset()         { *m = UserDefinedVotePKNotify{} }
func (m *UserDefinedVotePKNotify) String() string { return proto.CompactTextString(m) }
func (*UserDefinedVotePKNotify) ProtoMessage()    {}
func (*UserDefinedVotePKNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{40}
}
func (m *UserDefinedVotePKNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDefinedVotePKNotify.Unmarshal(m, b)
}
func (m *UserDefinedVotePKNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDefinedVotePKNotify.Marshal(b, m, deterministic)
}
func (dst *UserDefinedVotePKNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDefinedVotePKNotify.Merge(dst, src)
}
func (m *UserDefinedVotePKNotify) XXX_Size() int {
	return xxx_messageInfo_UserDefinedVotePKNotify.Size(m)
}
func (m *UserDefinedVotePKNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDefinedVotePKNotify.DiscardUnknown(m)
}

var xxx_messageInfo_UserDefinedVotePKNotify proto.InternalMessageInfo

func (m *UserDefinedVotePKNotify) GetPkInfo() *UserDefinedPKRankInfo {
	if m != nil {
		return m.PkInfo
	}
	return nil
}

func (m *UserDefinedVotePKNotify) GetNotifyStatus() uint32 {
	if m != nil {
		return m.NotifyStatus
	}
	return 0
}

func (m *UserDefinedVotePKNotify) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *UserDefinedVotePKNotify) GetOpNickname() string {
	if m != nil {
		return m.OpNickname
	}
	return ""
}

func (m *UserDefinedVotePKNotify) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

// 加票推送
type AddVotePkUserTicketNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	StartTimestamp       uint32   `protobuf:"varint,2,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	TicketCnt            uint32   `protobuf:"varint,3,opt,name=ticket_cnt,json=ticketCnt,proto3" json:"ticket_cnt,omitempty"`
	CurrentTotalTicket   uint32   `protobuf:"varint,4,opt,name=current_total_ticket,json=currentTotalTicket,proto3" json:"current_total_ticket,omitempty"`
	PushVersion          uint32   `protobuf:"varint,5,opt,name=push_version,json=pushVersion,proto3" json:"push_version,omitempty"`
	UserAddTicketCnt     uint32   `protobuf:"varint,6,opt,name=user_add_ticket_cnt,json=userAddTicketCnt,proto3" json:"user_add_ticket_cnt,omitempty"`
	UserRemainTicketCnt  uint32   `protobuf:"varint,7,opt,name=user_remain_ticket_cnt,json=userRemainTicketCnt,proto3" json:"user_remain_ticket_cnt,omitempty"`
	IsEndAct             bool     `protobuf:"varint,8,opt,name=is_end_act,json=isEndAct,proto3" json:"is_end_act,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddVotePkUserTicketNotify) Reset()         { *m = AddVotePkUserTicketNotify{} }
func (m *AddVotePkUserTicketNotify) String() string { return proto.CompactTextString(m) }
func (*AddVotePkUserTicketNotify) ProtoMessage()    {}
func (*AddVotePkUserTicketNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{41}
}
func (m *AddVotePkUserTicketNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVotePkUserTicketNotify.Unmarshal(m, b)
}
func (m *AddVotePkUserTicketNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVotePkUserTicketNotify.Marshal(b, m, deterministic)
}
func (dst *AddVotePkUserTicketNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVotePkUserTicketNotify.Merge(dst, src)
}
func (m *AddVotePkUserTicketNotify) XXX_Size() int {
	return xxx_messageInfo_AddVotePkUserTicketNotify.Size(m)
}
func (m *AddVotePkUserTicketNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVotePkUserTicketNotify.DiscardUnknown(m)
}

var xxx_messageInfo_AddVotePkUserTicketNotify proto.InternalMessageInfo

func (m *AddVotePkUserTicketNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddVotePkUserTicketNotify) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *AddVotePkUserTicketNotify) GetTicketCnt() uint32 {
	if m != nil {
		return m.TicketCnt
	}
	return 0
}

func (m *AddVotePkUserTicketNotify) GetCurrentTotalTicket() uint32 {
	if m != nil {
		return m.CurrentTotalTicket
	}
	return 0
}

func (m *AddVotePkUserTicketNotify) GetPushVersion() uint32 {
	if m != nil {
		return m.PushVersion
	}
	return 0
}

func (m *AddVotePkUserTicketNotify) GetUserAddTicketCnt() uint32 {
	if m != nil {
		return m.UserAddTicketCnt
	}
	return 0
}

func (m *AddVotePkUserTicketNotify) GetUserRemainTicketCnt() uint32 {
	if m != nil {
		return m.UserRemainTicketCnt
	}
	return 0
}

func (m *AddVotePkUserTicketNotify) GetIsEndAct() bool {
	if m != nil {
		return m.IsEndAct
	}
	return false
}

// ============ AI 说唱 ============
type VoiceWatermarkResultNotify struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	StageName            string   `protobuf:"bytes,2,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	VoiceUrl             string   `protobuf:"bytes,3,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`
	IsValid              bool     `protobuf:"varint,4,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoiceWatermarkResultNotify) Reset()         { *m = VoiceWatermarkResultNotify{} }
func (m *VoiceWatermarkResultNotify) String() string { return proto.CompactTextString(m) }
func (*VoiceWatermarkResultNotify) ProtoMessage()    {}
func (*VoiceWatermarkResultNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{42}
}
func (m *VoiceWatermarkResultNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoiceWatermarkResultNotify.Unmarshal(m, b)
}
func (m *VoiceWatermarkResultNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoiceWatermarkResultNotify.Marshal(b, m, deterministic)
}
func (dst *VoiceWatermarkResultNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoiceWatermarkResultNotify.Merge(dst, src)
}
func (m *VoiceWatermarkResultNotify) XXX_Size() int {
	return xxx_messageInfo_VoiceWatermarkResultNotify.Size(m)
}
func (m *VoiceWatermarkResultNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_VoiceWatermarkResultNotify.DiscardUnknown(m)
}

var xxx_messageInfo_VoiceWatermarkResultNotify proto.InternalMessageInfo

func (m *VoiceWatermarkResultNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VoiceWatermarkResultNotify) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *VoiceWatermarkResultNotify) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

func (m *VoiceWatermarkResultNotify) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

// 中台返回的声音质量分数&干音音频
type VoicePartialQualityNotify struct {
	Uid                   uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Timestamp             uint32   `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	VoiceStartSecond      float64  `protobuf:"fixed64,3,opt,name=voice_start_second,json=voiceStartSecond,proto3" json:"voice_start_second,omitempty"`
	VoiceEndSecond        float64  `protobuf:"fixed64,4,opt,name=voice_end_second,json=voiceEndSecond,proto3" json:"voice_end_second,omitempty"`
	Score                 uint32   `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	Pass                  bool     `protobuf:"varint,6,opt,name=pass,proto3" json:"pass,omitempty"`
	Text                  []string `protobuf:"bytes,7,rep,name=text,proto3" json:"text,omitempty"`
	ClientReportTimestamp uint32   `protobuf:"varint,8,opt,name=client_report_timestamp,json=clientReportTimestamp,proto3" json:"client_report_timestamp,omitempty"`
	AiType                uint32   `protobuf:"varint,9,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *VoicePartialQualityNotify) Reset()         { *m = VoicePartialQualityNotify{} }
func (m *VoicePartialQualityNotify) String() string { return proto.CompactTextString(m) }
func (*VoicePartialQualityNotify) ProtoMessage()    {}
func (*VoicePartialQualityNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{43}
}
func (m *VoicePartialQualityNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoicePartialQualityNotify.Unmarshal(m, b)
}
func (m *VoicePartialQualityNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoicePartialQualityNotify.Marshal(b, m, deterministic)
}
func (dst *VoicePartialQualityNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoicePartialQualityNotify.Merge(dst, src)
}
func (m *VoicePartialQualityNotify) XXX_Size() int {
	return xxx_messageInfo_VoicePartialQualityNotify.Size(m)
}
func (m *VoicePartialQualityNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_VoicePartialQualityNotify.DiscardUnknown(m)
}

var xxx_messageInfo_VoicePartialQualityNotify proto.InternalMessageInfo

func (m *VoicePartialQualityNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VoicePartialQualityNotify) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *VoicePartialQualityNotify) GetVoiceStartSecond() float64 {
	if m != nil {
		return m.VoiceStartSecond
	}
	return 0
}

func (m *VoicePartialQualityNotify) GetVoiceEndSecond() float64 {
	if m != nil {
		return m.VoiceEndSecond
	}
	return 0
}

func (m *VoicePartialQualityNotify) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *VoicePartialQualityNotify) GetPass() bool {
	if m != nil {
		return m.Pass
	}
	return false
}

func (m *VoicePartialQualityNotify) GetText() []string {
	if m != nil {
		return m.Text
	}
	return nil
}

func (m *VoicePartialQualityNotify) GetClientReportTimestamp() uint32 {
	if m != nil {
		return m.ClientReportTimestamp
	}
	return 0
}

func (m *VoicePartialQualityNotify) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type RapperPostTopicInfo struct {
	TopicName            string   `protobuf:"bytes,1,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	TopicId              string   `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RapperPostTopicInfo) Reset()         { *m = RapperPostTopicInfo{} }
func (m *RapperPostTopicInfo) String() string { return proto.CompactTextString(m) }
func (*RapperPostTopicInfo) ProtoMessage()    {}
func (*RapperPostTopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{44}
}
func (m *RapperPostTopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RapperPostTopicInfo.Unmarshal(m, b)
}
func (m *RapperPostTopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RapperPostTopicInfo.Marshal(b, m, deterministic)
}
func (dst *RapperPostTopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RapperPostTopicInfo.Merge(dst, src)
}
func (m *RapperPostTopicInfo) XXX_Size() int {
	return xxx_messageInfo_RapperPostTopicInfo.Size(m)
}
func (m *RapperPostTopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RapperPostTopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RapperPostTopicInfo proto.InternalMessageInfo

func (m *RapperPostTopicInfo) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *RapperPostTopicInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

// 中台返回的说唱音频
type RapperVoiceNotify struct {
	Uid                   uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Timestamp             uint32                 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	VoiceUrl              string                 `protobuf:"bytes,3,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`
	NicknameList          []string               `protobuf:"bytes,4,rep,name=nickname_list,json=nicknameList,proto3" json:"nickname_list,omitempty"`
	BgVideoUrl            string                 `protobuf:"bytes,5,opt,name=bg_video_url,json=bgVideoUrl,proto3" json:"bg_video_url,omitempty"`
	BgVideoMd5            string                 `protobuf:"bytes,6,opt,name=bg_video_md5,json=bgVideoMd5,proto3" json:"bg_video_md5,omitempty"`
	EndVideoUrl           string                 `protobuf:"bytes,7,opt,name=end_video_url,json=endVideoUrl,proto3" json:"end_video_url,omitempty"`
	EndVideoMd5           string                 `protobuf:"bytes,8,opt,name=end_video_md5,json=endVideoMd5,proto3" json:"end_video_md5,omitempty"`
	TopicList             []*RapperPostTopicInfo `protobuf:"bytes,9,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	StageName             string                 `protobuf:"bytes,10,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	MusicProducer         string                 `protobuf:"bytes,11,opt,name=music_producer,json=musicProducer,proto3" json:"music_producer,omitempty"`
	MusicDirector         string                 `protobuf:"bytes,12,opt,name=music_director,json=musicDirector,proto3" json:"music_director,omitempty"`
	ClientReportTimestamp uint32                 `protobuf:"varint,13,opt,name=client_report_timestamp,json=clientReportTimestamp,proto3" json:"client_report_timestamp,omitempty"`
	MarketUrl             string                 `protobuf:"bytes,14,opt,name=market_url,json=marketUrl,proto3" json:"market_url,omitempty"`
	BgOriginVedioUrl      string                 `protobuf:"bytes,15,opt,name=bg_origin_vedio_url,json=bgOriginVedioUrl,proto3" json:"bg_origin_vedio_url,omitempty"`
	BgVideoFirstFrameUrl  string                 `protobuf:"bytes,16,opt,name=bg_video_first_frame_url,json=bgVideoFirstFrameUrl,proto3" json:"bg_video_first_frame_url,omitempty"`
	AiType                uint32                 `protobuf:"varint,17,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}               `json:"-"`
	XXX_unrecognized      []byte                 `json:"-"`
	XXX_sizecache         int32                  `json:"-"`
}

func (m *RapperVoiceNotify) Reset()         { *m = RapperVoiceNotify{} }
func (m *RapperVoiceNotify) String() string { return proto.CompactTextString(m) }
func (*RapperVoiceNotify) ProtoMessage()    {}
func (*RapperVoiceNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{45}
}
func (m *RapperVoiceNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RapperVoiceNotify.Unmarshal(m, b)
}
func (m *RapperVoiceNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RapperVoiceNotify.Marshal(b, m, deterministic)
}
func (dst *RapperVoiceNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RapperVoiceNotify.Merge(dst, src)
}
func (m *RapperVoiceNotify) XXX_Size() int {
	return xxx_messageInfo_RapperVoiceNotify.Size(m)
}
func (m *RapperVoiceNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_RapperVoiceNotify.DiscardUnknown(m)
}

var xxx_messageInfo_RapperVoiceNotify proto.InternalMessageInfo

func (m *RapperVoiceNotify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RapperVoiceNotify) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *RapperVoiceNotify) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

func (m *RapperVoiceNotify) GetNicknameList() []string {
	if m != nil {
		return m.NicknameList
	}
	return nil
}

func (m *RapperVoiceNotify) GetBgVideoUrl() string {
	if m != nil {
		return m.BgVideoUrl
	}
	return ""
}

func (m *RapperVoiceNotify) GetBgVideoMd5() string {
	if m != nil {
		return m.BgVideoMd5
	}
	return ""
}

func (m *RapperVoiceNotify) GetEndVideoUrl() string {
	if m != nil {
		return m.EndVideoUrl
	}
	return ""
}

func (m *RapperVoiceNotify) GetEndVideoMd5() string {
	if m != nil {
		return m.EndVideoMd5
	}
	return ""
}

func (m *RapperVoiceNotify) GetTopicList() []*RapperPostTopicInfo {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *RapperVoiceNotify) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *RapperVoiceNotify) GetMusicProducer() string {
	if m != nil {
		return m.MusicProducer
	}
	return ""
}

func (m *RapperVoiceNotify) GetMusicDirector() string {
	if m != nil {
		return m.MusicDirector
	}
	return ""
}

func (m *RapperVoiceNotify) GetClientReportTimestamp() uint32 {
	if m != nil {
		return m.ClientReportTimestamp
	}
	return 0
}

func (m *RapperVoiceNotify) GetMarketUrl() string {
	if m != nil {
		return m.MarketUrl
	}
	return ""
}

func (m *RapperVoiceNotify) GetBgOriginVedioUrl() string {
	if m != nil {
		return m.BgOriginVedioUrl
	}
	return ""
}

func (m *RapperVoiceNotify) GetBgVideoFirstFrameUrl() string {
	if m != nil {
		return m.BgVideoFirstFrameUrl
	}
	return ""
}

func (m *RapperVoiceNotify) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

// 房间推送 是否开启厂牌专属房间背景及话题入口 也用于请求的返回
type BrandChannelBGTopicNotify struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SwitchChannelBg      bool     `protobuf:"varint,2,opt,name=switch_channel_bg,json=switchChannelBg,proto3" json:"switch_channel_bg,omitempty"`
	UnderPicUrl          string   `protobuf:"bytes,3,opt,name=under_pic_url,json=underPicUrl,proto3" json:"under_pic_url,omitempty"`
	UnderPicMd5          string   `protobuf:"bytes,4,opt,name=under_pic_md5,json=underPicMd5,proto3" json:"under_pic_md5,omitempty"`
	StagePicUrl          string   `protobuf:"bytes,5,opt,name=stage_pic_url,json=stagePicUrl,proto3" json:"stage_pic_url,omitempty"`
	StagePicMd5          string   `protobuf:"bytes,6,opt,name=stage_pic_md5,json=stagePicMd5,proto3" json:"stage_pic_md5,omitempty"`
	PicValidBeginTs      uint32   `protobuf:"varint,7,opt,name=pic_valid_begin_ts,json=picValidBeginTs,proto3" json:"pic_valid_begin_ts,omitempty"`
	PicValidEndTs        uint32   `protobuf:"varint,8,opt,name=pic_valid_end_ts,json=picValidEndTs,proto3" json:"pic_valid_end_ts,omitempty"`
	PcUnderPicUrl        string   `protobuf:"bytes,9,opt,name=pc_under_pic_url,json=pcUnderPicUrl,proto3" json:"pc_under_pic_url,omitempty"`
	PcUnderPicMd5        string   `protobuf:"bytes,10,opt,name=pc_under_pic_md5,json=pcUnderPicMd5,proto3" json:"pc_under_pic_md5,omitempty"`
	PcStagePicUrl        string   `protobuf:"bytes,11,opt,name=pc_stage_pic_url,json=pcStagePicUrl,proto3" json:"pc_stage_pic_url,omitempty"`
	PcStagePicMd5        string   `protobuf:"bytes,12,opt,name=pc_stage_pic_md5,json=pcStagePicMd5,proto3" json:"pc_stage_pic_md5,omitempty"`
	TopicId              string   `protobuf:"bytes,13,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName            string   `protobuf:"bytes,14,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BrandChannelBGTopicNotify) Reset()         { *m = BrandChannelBGTopicNotify{} }
func (m *BrandChannelBGTopicNotify) String() string { return proto.CompactTextString(m) }
func (*BrandChannelBGTopicNotify) ProtoMessage()    {}
func (*BrandChannelBGTopicNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{46}
}
func (m *BrandChannelBGTopicNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandChannelBGTopicNotify.Unmarshal(m, b)
}
func (m *BrandChannelBGTopicNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandChannelBGTopicNotify.Marshal(b, m, deterministic)
}
func (dst *BrandChannelBGTopicNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandChannelBGTopicNotify.Merge(dst, src)
}
func (m *BrandChannelBGTopicNotify) XXX_Size() int {
	return xxx_messageInfo_BrandChannelBGTopicNotify.Size(m)
}
func (m *BrandChannelBGTopicNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandChannelBGTopicNotify.DiscardUnknown(m)
}

var xxx_messageInfo_BrandChannelBGTopicNotify proto.InternalMessageInfo

func (m *BrandChannelBGTopicNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BrandChannelBGTopicNotify) GetSwitchChannelBg() bool {
	if m != nil {
		return m.SwitchChannelBg
	}
	return false
}

func (m *BrandChannelBGTopicNotify) GetUnderPicUrl() string {
	if m != nil {
		return m.UnderPicUrl
	}
	return ""
}

func (m *BrandChannelBGTopicNotify) GetUnderPicMd5() string {
	if m != nil {
		return m.UnderPicMd5
	}
	return ""
}

func (m *BrandChannelBGTopicNotify) GetStagePicUrl() string {
	if m != nil {
		return m.StagePicUrl
	}
	return ""
}

func (m *BrandChannelBGTopicNotify) GetStagePicMd5() string {
	if m != nil {
		return m.StagePicMd5
	}
	return ""
}

func (m *BrandChannelBGTopicNotify) GetPicValidBeginTs() uint32 {
	if m != nil {
		return m.PicValidBeginTs
	}
	return 0
}

func (m *BrandChannelBGTopicNotify) GetPicValidEndTs() uint32 {
	if m != nil {
		return m.PicValidEndTs
	}
	return 0
}

func (m *BrandChannelBGTopicNotify) GetPcUnderPicUrl() string {
	if m != nil {
		return m.PcUnderPicUrl
	}
	return ""
}

func (m *BrandChannelBGTopicNotify) GetPcUnderPicMd5() string {
	if m != nil {
		return m.PcUnderPicMd5
	}
	return ""
}

func (m *BrandChannelBGTopicNotify) GetPcStagePicUrl() string {
	if m != nil {
		return m.PcStagePicUrl
	}
	return ""
}

func (m *BrandChannelBGTopicNotify) GetPcStagePicMd5() string {
	if m != nil {
		return m.PcStagePicMd5
	}
	return ""
}

func (m *BrandChannelBGTopicNotify) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *BrandChannelBGTopicNotify) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

// 设置声音水印
type SetVoiceWatermarkReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	StageName            string       `protobuf:"bytes,3,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	VoiceComposedKey     string       `protobuf:"bytes,4,opt,name=voice_composed_key,json=voiceComposedKey,proto3" json:"voice_composed_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetVoiceWatermarkReq) Reset()         { *m = SetVoiceWatermarkReq{} }
func (m *SetVoiceWatermarkReq) String() string { return proto.CompactTextString(m) }
func (*SetVoiceWatermarkReq) ProtoMessage()    {}
func (*SetVoiceWatermarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{47}
}
func (m *SetVoiceWatermarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVoiceWatermarkReq.Unmarshal(m, b)
}
func (m *SetVoiceWatermarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVoiceWatermarkReq.Marshal(b, m, deterministic)
}
func (dst *SetVoiceWatermarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVoiceWatermarkReq.Merge(dst, src)
}
func (m *SetVoiceWatermarkReq) XXX_Size() int {
	return xxx_messageInfo_SetVoiceWatermarkReq.Size(m)
}
func (m *SetVoiceWatermarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVoiceWatermarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetVoiceWatermarkReq proto.InternalMessageInfo

func (m *SetVoiceWatermarkReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetVoiceWatermarkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetVoiceWatermarkReq) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *SetVoiceWatermarkReq) GetVoiceComposedKey() string {
	if m != nil {
		return m.VoiceComposedKey
	}
	return ""
}

type SetVoiceWatermarkResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetVoiceWatermarkResp) Reset()         { *m = SetVoiceWatermarkResp{} }
func (m *SetVoiceWatermarkResp) String() string { return proto.CompactTextString(m) }
func (*SetVoiceWatermarkResp) ProtoMessage()    {}
func (*SetVoiceWatermarkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{48}
}
func (m *SetVoiceWatermarkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVoiceWatermarkResp.Unmarshal(m, b)
}
func (m *SetVoiceWatermarkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVoiceWatermarkResp.Marshal(b, m, deterministic)
}
func (dst *SetVoiceWatermarkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVoiceWatermarkResp.Merge(dst, src)
}
func (m *SetVoiceWatermarkResp) XXX_Size() int {
	return xxx_messageInfo_SetVoiceWatermarkResp.Size(m)
}
func (m *SetVoiceWatermarkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVoiceWatermarkResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetVoiceWatermarkResp proto.InternalMessageInfo

func (m *SetVoiceWatermarkResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetVoiceWatermarkReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetVoiceWatermarkReq) Reset()         { *m = GetVoiceWatermarkReq{} }
func (m *GetVoiceWatermarkReq) String() string { return proto.CompactTextString(m) }
func (*GetVoiceWatermarkReq) ProtoMessage()    {}
func (*GetVoiceWatermarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{49}
}
func (m *GetVoiceWatermarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoiceWatermarkReq.Unmarshal(m, b)
}
func (m *GetVoiceWatermarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoiceWatermarkReq.Marshal(b, m, deterministic)
}
func (dst *GetVoiceWatermarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoiceWatermarkReq.Merge(dst, src)
}
func (m *GetVoiceWatermarkReq) XXX_Size() int {
	return xxx_messageInfo_GetVoiceWatermarkReq.Size(m)
}
func (m *GetVoiceWatermarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoiceWatermarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoiceWatermarkReq proto.InternalMessageInfo

func (m *GetVoiceWatermarkReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetVoiceWatermarkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetVoiceWatermarkResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	StageName            string        `protobuf:"bytes,2,opt,name=stage_name,json=stageName,proto3" json:"stage_name,omitempty"`
	VoiceUrl             string        `protobuf:"bytes,3,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetVoiceWatermarkResp) Reset()         { *m = GetVoiceWatermarkResp{} }
func (m *GetVoiceWatermarkResp) String() string { return proto.CompactTextString(m) }
func (*GetVoiceWatermarkResp) ProtoMessage()    {}
func (*GetVoiceWatermarkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{50}
}
func (m *GetVoiceWatermarkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoiceWatermarkResp.Unmarshal(m, b)
}
func (m *GetVoiceWatermarkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoiceWatermarkResp.Marshal(b, m, deterministic)
}
func (dst *GetVoiceWatermarkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoiceWatermarkResp.Merge(dst, src)
}
func (m *GetVoiceWatermarkResp) XXX_Size() int {
	return xxx_messageInfo_GetVoiceWatermarkResp.Size(m)
}
func (m *GetVoiceWatermarkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoiceWatermarkResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoiceWatermarkResp proto.InternalMessageInfo

func (m *GetVoiceWatermarkResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetVoiceWatermarkResp) GetStageName() string {
	if m != nil {
		return m.StageName
	}
	return ""
}

func (m *GetVoiceWatermarkResp) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

type DelVoiceWatermarkReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DelVoiceWatermarkReq) Reset()         { *m = DelVoiceWatermarkReq{} }
func (m *DelVoiceWatermarkReq) String() string { return proto.CompactTextString(m) }
func (*DelVoiceWatermarkReq) ProtoMessage()    {}
func (*DelVoiceWatermarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{51}
}
func (m *DelVoiceWatermarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelVoiceWatermarkReq.Unmarshal(m, b)
}
func (m *DelVoiceWatermarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelVoiceWatermarkReq.Marshal(b, m, deterministic)
}
func (dst *DelVoiceWatermarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelVoiceWatermarkReq.Merge(dst, src)
}
func (m *DelVoiceWatermarkReq) XXX_Size() int {
	return xxx_messageInfo_DelVoiceWatermarkReq.Size(m)
}
func (m *DelVoiceWatermarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelVoiceWatermarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelVoiceWatermarkReq proto.InternalMessageInfo

func (m *DelVoiceWatermarkReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DelVoiceWatermarkReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DelVoiceWatermarkResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DelVoiceWatermarkResp) Reset()         { *m = DelVoiceWatermarkResp{} }
func (m *DelVoiceWatermarkResp) String() string { return proto.CompactTextString(m) }
func (*DelVoiceWatermarkResp) ProtoMessage()    {}
func (*DelVoiceWatermarkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{52}
}
func (m *DelVoiceWatermarkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelVoiceWatermarkResp.Unmarshal(m, b)
}
func (m *DelVoiceWatermarkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelVoiceWatermarkResp.Marshal(b, m, deterministic)
}
func (dst *DelVoiceWatermarkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelVoiceWatermarkResp.Merge(dst, src)
}
func (m *DelVoiceWatermarkResp) XXX_Size() int {
	return xxx_messageInfo_DelVoiceWatermarkResp.Size(m)
}
func (m *DelVoiceWatermarkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelVoiceWatermarkResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelVoiceWatermarkResp proto.InternalMessageInfo

func (m *DelVoiceWatermarkResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 用户念的音频
type SendReadVoiceReq struct {
	BaseReq               *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                   uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	SongId                string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	VoiceKey              string       `protobuf:"bytes,4,opt,name=voice_key,json=voiceKey,proto3" json:"voice_key,omitempty"`
	ClientReportTimestamp uint32       `protobuf:"varint,5,opt,name=client_report_timestamp,json=clientReportTimestamp,proto3" json:"client_report_timestamp,omitempty"`
	AiType                uint32       `protobuf:"varint,6,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}     `json:"-"`
	XXX_unrecognized      []byte       `json:"-"`
	XXX_sizecache         int32        `json:"-"`
}

func (m *SendReadVoiceReq) Reset()         { *m = SendReadVoiceReq{} }
func (m *SendReadVoiceReq) String() string { return proto.CompactTextString(m) }
func (*SendReadVoiceReq) ProtoMessage()    {}
func (*SendReadVoiceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{53}
}
func (m *SendReadVoiceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendReadVoiceReq.Unmarshal(m, b)
}
func (m *SendReadVoiceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendReadVoiceReq.Marshal(b, m, deterministic)
}
func (dst *SendReadVoiceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendReadVoiceReq.Merge(dst, src)
}
func (m *SendReadVoiceReq) XXX_Size() int {
	return xxx_messageInfo_SendReadVoiceReq.Size(m)
}
func (m *SendReadVoiceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendReadVoiceReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendReadVoiceReq proto.InternalMessageInfo

func (m *SendReadVoiceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SendReadVoiceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SendReadVoiceReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *SendReadVoiceReq) GetVoiceKey() string {
	if m != nil {
		return m.VoiceKey
	}
	return ""
}

func (m *SendReadVoiceReq) GetClientReportTimestamp() uint32 {
	if m != nil {
		return m.ClientReportTimestamp
	}
	return 0
}

func (m *SendReadVoiceReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type SendReadVoiceResp struct {
	BaseResp              *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Timestamp             uint32        `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ClientReportTimestamp uint32        `protobuf:"varint,3,opt,name=client_report_timestamp,json=clientReportTimestamp,proto3" json:"client_report_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}      `json:"-"`
	XXX_unrecognized      []byte        `json:"-"`
	XXX_sizecache         int32         `json:"-"`
}

func (m *SendReadVoiceResp) Reset()         { *m = SendReadVoiceResp{} }
func (m *SendReadVoiceResp) String() string { return proto.CompactTextString(m) }
func (*SendReadVoiceResp) ProtoMessage()    {}
func (*SendReadVoiceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{54}
}
func (m *SendReadVoiceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendReadVoiceResp.Unmarshal(m, b)
}
func (m *SendReadVoiceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendReadVoiceResp.Marshal(b, m, deterministic)
}
func (dst *SendReadVoiceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendReadVoiceResp.Merge(dst, src)
}
func (m *SendReadVoiceResp) XXX_Size() int {
	return xxx_messageInfo_SendReadVoiceResp.Size(m)
}
func (m *SendReadVoiceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendReadVoiceResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendReadVoiceResp proto.InternalMessageInfo

func (m *SendReadVoiceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SendReadVoiceResp) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *SendReadVoiceResp) GetClientReportTimestamp() uint32 {
	if m != nil {
		return m.ClientReportTimestamp
	}
	return 0
}

// 等级
type LevelConfig struct {
	LevelName            string   `protobuf:"bytes,1,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	MinNum               uint32   `protobuf:"varint,2,opt,name=min_num,json=minNum,proto3" json:"min_num,omitempty"`
	MaxNum               uint32   `protobuf:"varint,3,opt,name=max_num,json=maxNum,proto3" json:"max_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelConfig) Reset()         { *m = LevelConfig{} }
func (m *LevelConfig) String() string { return proto.CompactTextString(m) }
func (*LevelConfig) ProtoMessage()    {}
func (*LevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{55}
}
func (m *LevelConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConfig.Unmarshal(m, b)
}
func (m *LevelConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConfig.Marshal(b, m, deterministic)
}
func (dst *LevelConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConfig.Merge(dst, src)
}
func (m *LevelConfig) XXX_Size() int {
	return xxx_messageInfo_LevelConfig.Size(m)
}
func (m *LevelConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConfig proto.InternalMessageInfo

func (m *LevelConfig) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LevelConfig) GetMinNum() uint32 {
	if m != nil {
		return m.MinNum
	}
	return 0
}

func (m *LevelConfig) GetMaxNum() uint32 {
	if m != nil {
		return m.MaxNum
	}
	return 0
}

type UserLevel struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string       `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Level                *LevelConfig `protobuf:"bytes,3,opt,name=level,proto3" json:"level,omitempty"`
	Point                uint32       `protobuf:"varint,4,opt,name=point,proto3" json:"point,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UserLevel) Reset()         { *m = UserLevel{} }
func (m *UserLevel) String() string { return proto.CompactTextString(m) }
func (*UserLevel) ProtoMessage()    {}
func (*UserLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{56}
}
func (m *UserLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserLevel.Unmarshal(m, b)
}
func (m *UserLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserLevel.Marshal(b, m, deterministic)
}
func (dst *UserLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserLevel.Merge(dst, src)
}
func (m *UserLevel) XXX_Size() int {
	return xxx_messageInfo_UserLevel.Size(m)
}
func (m *UserLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_UserLevel.DiscardUnknown(m)
}

var xxx_messageInfo_UserLevel proto.InternalMessageInfo

func (m *UserLevel) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLevel) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserLevel) GetLevel() *LevelConfig {
	if m != nil {
		return m.Level
	}
	return nil
}

func (m *UserLevel) GetPoint() uint32 {
	if m != nil {
		return m.Point
	}
	return 0
}

type GetUserAIRapperLevelReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserAIRapperLevelReq) Reset()         { *m = GetUserAIRapperLevelReq{} }
func (m *GetUserAIRapperLevelReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRapperLevelReq) ProtoMessage()    {}
func (*GetUserAIRapperLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{57}
}
func (m *GetUserAIRapperLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRapperLevelReq.Unmarshal(m, b)
}
func (m *GetUserAIRapperLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRapperLevelReq.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRapperLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRapperLevelReq.Merge(dst, src)
}
func (m *GetUserAIRapperLevelReq) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRapperLevelReq.Size(m)
}
func (m *GetUserAIRapperLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRapperLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRapperLevelReq proto.InternalMessageInfo

func (m *GetUserAIRapperLevelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserAIRapperLevelResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	UserLevel            *UserLevel    `protobuf:"bytes,2,opt,name=user_level,json=userLevel,proto3" json:"user_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserAIRapperLevelResp) Reset()         { *m = GetUserAIRapperLevelResp{} }
func (m *GetUserAIRapperLevelResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAIRapperLevelResp) ProtoMessage()    {}
func (*GetUserAIRapperLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{58}
}
func (m *GetUserAIRapperLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserAIRapperLevelResp.Unmarshal(m, b)
}
func (m *GetUserAIRapperLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserAIRapperLevelResp.Marshal(b, m, deterministic)
}
func (dst *GetUserAIRapperLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserAIRapperLevelResp.Merge(dst, src)
}
func (m *GetUserAIRapperLevelResp) XXX_Size() int {
	return xxx_messageInfo_GetUserAIRapperLevelResp.Size(m)
}
func (m *GetUserAIRapperLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserAIRapperLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserAIRapperLevelResp proto.InternalMessageInfo

func (m *GetUserAIRapperLevelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserAIRapperLevelResp) GetUserLevel() *UserLevel {
	if m != nil {
		return m.UserLevel
	}
	return nil
}

// 用户发完帖子后将帖子id发给服务端
type PostAIRapperPostReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string       `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AiType               uint32       `protobuf:"varint,4,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PostAIRapperPostReq) Reset()         { *m = PostAIRapperPostReq{} }
func (m *PostAIRapperPostReq) String() string { return proto.CompactTextString(m) }
func (*PostAIRapperPostReq) ProtoMessage()    {}
func (*PostAIRapperPostReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{59}
}
func (m *PostAIRapperPostReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostAIRapperPostReq.Unmarshal(m, b)
}
func (m *PostAIRapperPostReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostAIRapperPostReq.Marshal(b, m, deterministic)
}
func (dst *PostAIRapperPostReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostAIRapperPostReq.Merge(dst, src)
}
func (m *PostAIRapperPostReq) XXX_Size() int {
	return xxx_messageInfo_PostAIRapperPostReq.Size(m)
}
func (m *PostAIRapperPostReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PostAIRapperPostReq.DiscardUnknown(m)
}

var xxx_messageInfo_PostAIRapperPostReq proto.InternalMessageInfo

func (m *PostAIRapperPostReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PostAIRapperPostReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PostAIRapperPostReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostAIRapperPostReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type PostAIRapperPostResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PostAIRapperPostResp) Reset()         { *m = PostAIRapperPostResp{} }
func (m *PostAIRapperPostResp) String() string { return proto.CompactTextString(m) }
func (*PostAIRapperPostResp) ProtoMessage()    {}
func (*PostAIRapperPostResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{60}
}
func (m *PostAIRapperPostResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostAIRapperPostResp.Unmarshal(m, b)
}
func (m *PostAIRapperPostResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostAIRapperPostResp.Marshal(b, m, deterministic)
}
func (dst *PostAIRapperPostResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostAIRapperPostResp.Merge(dst, src)
}
func (m *PostAIRapperPostResp) XXX_Size() int {
	return xxx_messageInfo_PostAIRapperPostResp.Size(m)
}
func (m *PostAIRapperPostResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PostAIRapperPostResp.DiscardUnknown(m)
}

var xxx_messageInfo_PostAIRapperPostResp proto.InternalMessageInfo

func (m *PostAIRapperPostResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取tab
type GetTabInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	AiType               uint32       `protobuf:"varint,2,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetTabInfoReq) Reset()         { *m = GetTabInfoReq{} }
func (m *GetTabInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetTabInfoReq) ProtoMessage()    {}
func (*GetTabInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{61}
}
func (m *GetTabInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabInfoReq.Unmarshal(m, b)
}
func (m *GetTabInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetTabInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabInfoReq.Merge(dst, src)
}
func (m *GetTabInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetTabInfoReq.Size(m)
}
func (m *GetTabInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabInfoReq proto.InternalMessageInfo

func (m *GetTabInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetTabInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type TabAttribute struct {
	TabId                string   `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TabAttribute) Reset()         { *m = TabAttribute{} }
func (m *TabAttribute) String() string { return proto.CompactTextString(m) }
func (*TabAttribute) ProtoMessage()    {}
func (*TabAttribute) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{62}
}
func (m *TabAttribute) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabAttribute.Unmarshal(m, b)
}
func (m *TabAttribute) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabAttribute.Marshal(b, m, deterministic)
}
func (dst *TabAttribute) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabAttribute.Merge(dst, src)
}
func (m *TabAttribute) XXX_Size() int {
	return xxx_messageInfo_TabAttribute.Size(m)
}
func (m *TabAttribute) XXX_DiscardUnknown() {
	xxx_messageInfo_TabAttribute.DiscardUnknown(m)
}

var xxx_messageInfo_TabAttribute proto.InternalMessageInfo

func (m *TabAttribute) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *TabAttribute) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type GetTabInfoResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TabInfo              []*TabAttribute `protobuf:"bytes,2,rep,name=tab_info,json=tabInfo,proto3" json:"tab_info,omitempty"`
	NewbieSong           *LyricInfo      `protobuf:"bytes,3,opt,name=newbie_song,json=newbieSong,proto3" json:"newbie_song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetTabInfoResp) Reset()         { *m = GetTabInfoResp{} }
func (m *GetTabInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetTabInfoResp) ProtoMessage()    {}
func (*GetTabInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{63}
}
func (m *GetTabInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabInfoResp.Unmarshal(m, b)
}
func (m *GetTabInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetTabInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabInfoResp.Merge(dst, src)
}
func (m *GetTabInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetTabInfoResp.Size(m)
}
func (m *GetTabInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabInfoResp proto.InternalMessageInfo

func (m *GetTabInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetTabInfoResp) GetTabInfo() []*TabAttribute {
	if m != nil {
		return m.TabInfo
	}
	return nil
}

func (m *GetTabInfoResp) GetNewbieSong() *LyricInfo {
	if m != nil {
		return m.NewbieSong
	}
	return nil
}

// rapper首页歌词列表
type GetLyricInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                string       `protobuf:"bytes,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32       `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	SongId               string       `protobuf:"bytes,5,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	AiType               uint32       `protobuf:"varint,6,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetLyricInfoReq) Reset()         { *m = GetLyricInfoReq{} }
func (m *GetLyricInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetLyricInfoReq) ProtoMessage()    {}
func (*GetLyricInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{64}
}
func (m *GetLyricInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLyricInfoReq.Unmarshal(m, b)
}
func (m *GetLyricInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLyricInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetLyricInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLyricInfoReq.Merge(dst, src)
}
func (m *GetLyricInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetLyricInfoReq.Size(m)
}
func (m *GetLyricInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLyricInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLyricInfoReq proto.InternalMessageInfo

func (m *GetLyricInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetLyricInfoReq) GetTabId() string {
	if m != nil {
		return m.TabId
	}
	return ""
}

func (m *GetLyricInfoReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetLyricInfoReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetLyricInfoReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *GetLyricInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

// 赛博世界首页
type GetCyberWorldHomeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCyberWorldHomeReq) Reset()         { *m = GetCyberWorldHomeReq{} }
func (m *GetCyberWorldHomeReq) String() string { return proto.CompactTextString(m) }
func (*GetCyberWorldHomeReq) ProtoMessage()    {}
func (*GetCyberWorldHomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{65}
}
func (m *GetCyberWorldHomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCyberWorldHomeReq.Unmarshal(m, b)
}
func (m *GetCyberWorldHomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCyberWorldHomeReq.Marshal(b, m, deterministic)
}
func (dst *GetCyberWorldHomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCyberWorldHomeReq.Merge(dst, src)
}
func (m *GetCyberWorldHomeReq) XXX_Size() int {
	return xxx_messageInfo_GetCyberWorldHomeReq.Size(m)
}
func (m *GetCyberWorldHomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCyberWorldHomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCyberWorldHomeReq proto.InternalMessageInfo

func (m *GetCyberWorldHomeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type CyberWorldModel struct {
	PicUrl               string   `protobuf:"bytes,1,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle             string   `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	JumpUrl              string   `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	AiType               uint32   `protobuf:"varint,5,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CyberWorldModel) Reset()         { *m = CyberWorldModel{} }
func (m *CyberWorldModel) String() string { return proto.CompactTextString(m) }
func (*CyberWorldModel) ProtoMessage()    {}
func (*CyberWorldModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{66}
}
func (m *CyberWorldModel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CyberWorldModel.Unmarshal(m, b)
}
func (m *CyberWorldModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CyberWorldModel.Marshal(b, m, deterministic)
}
func (dst *CyberWorldModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CyberWorldModel.Merge(dst, src)
}
func (m *CyberWorldModel) XXX_Size() int {
	return xxx_messageInfo_CyberWorldModel.Size(m)
}
func (m *CyberWorldModel) XXX_DiscardUnknown() {
	xxx_messageInfo_CyberWorldModel.DiscardUnknown(m)
}

var xxx_messageInfo_CyberWorldModel proto.InternalMessageInfo

func (m *CyberWorldModel) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *CyberWorldModel) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CyberWorldModel) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *CyberWorldModel) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *CyberWorldModel) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type GetCyberWorldHomeResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ModelList            []*CyberWorldModel `protobuf:"bytes,2,rep,name=model_list,json=modelList,proto3" json:"model_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetCyberWorldHomeResp) Reset()         { *m = GetCyberWorldHomeResp{} }
func (m *GetCyberWorldHomeResp) String() string { return proto.CompactTextString(m) }
func (*GetCyberWorldHomeResp) ProtoMessage()    {}
func (*GetCyberWorldHomeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{67}
}
func (m *GetCyberWorldHomeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCyberWorldHomeResp.Unmarshal(m, b)
}
func (m *GetCyberWorldHomeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCyberWorldHomeResp.Marshal(b, m, deterministic)
}
func (dst *GetCyberWorldHomeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCyberWorldHomeResp.Merge(dst, src)
}
func (m *GetCyberWorldHomeResp) XXX_Size() int {
	return xxx_messageInfo_GetCyberWorldHomeResp.Size(m)
}
func (m *GetCyberWorldHomeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCyberWorldHomeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCyberWorldHomeResp proto.InternalMessageInfo

func (m *GetCyberWorldHomeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCyberWorldHomeResp) GetModelList() []*CyberWorldModel {
	if m != nil {
		return m.ModelList
	}
	return nil
}

// ai外语首页歌曲卡片的视频+按钮链接
type CardBGVideo struct {
	HomePicUrl           string   `protobuf:"bytes,1,opt,name=home_pic_url,json=homePicUrl,proto3" json:"home_pic_url,omitempty"`
	ButtonUrl            string   `protobuf:"bytes,2,opt,name=button_url,json=buttonUrl,proto3" json:"button_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CardBGVideo) Reset()         { *m = CardBGVideo{} }
func (m *CardBGVideo) String() string { return proto.CompactTextString(m) }
func (*CardBGVideo) ProtoMessage()    {}
func (*CardBGVideo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{68}
}
func (m *CardBGVideo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardBGVideo.Unmarshal(m, b)
}
func (m *CardBGVideo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardBGVideo.Marshal(b, m, deterministic)
}
func (dst *CardBGVideo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardBGVideo.Merge(dst, src)
}
func (m *CardBGVideo) XXX_Size() int {
	return xxx_messageInfo_CardBGVideo.Size(m)
}
func (m *CardBGVideo) XXX_DiscardUnknown() {
	xxx_messageInfo_CardBGVideo.DiscardUnknown(m)
}

var xxx_messageInfo_CardBGVideo proto.InternalMessageInfo

func (m *CardBGVideo) GetHomePicUrl() string {
	if m != nil {
		return m.HomePicUrl
	}
	return ""
}

func (m *CardBGVideo) GetButtonUrl() string {
	if m != nil {
		return m.ButtonUrl
	}
	return ""
}

type LyricInfo struct {
	SongId                    string       `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName                  string       `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	RapName                   string       `protobuf:"bytes,3,opt,name=rap_name,json=rapName,proto3" json:"rap_name,omitempty"`
	LyricContent              []string     `protobuf:"bytes,4,rep,name=lyric_content,json=lyricContent,proto3" json:"lyric_content,omitempty"`
	LyricUrl                  string       `protobuf:"bytes,5,opt,name=lyric_url,json=lyricUrl,proto3" json:"lyric_url,omitempty"`
	FileMd5                   string       `protobuf:"bytes,6,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	LevelName                 string       `protobuf:"bytes,7,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	LevelScore                uint32       `protobuf:"varint,8,opt,name=level_score,json=levelScore,proto3" json:"level_score,omitempty"`
	IsLocked                  bool         `protobuf:"varint,9,opt,name=is_locked,json=isLocked,proto3" json:"is_locked,omitempty"`
	ExcerptContentUrl         string       `protobuf:"bytes,10,opt,name=excerpt_content_url,json=excerptContentUrl,proto3" json:"excerpt_content_url,omitempty"`
	ExcerptContentMd5         string       `protobuf:"bytes,11,opt,name=excerpt_content_md5,json=excerptContentMd5,proto3" json:"excerpt_content_md5,omitempty"`
	FriendCircleHeat          uint64       `protobuf:"varint,12,opt,name=friend_circle_heat,json=friendCircleHeat,proto3" json:"friend_circle_heat,omitempty"`
	ProducerNickName          string       `protobuf:"bytes,13,opt,name=producer_nick_name,json=producerNickName,proto3" json:"producer_nick_name,omitempty"`
	NewLyricUrl               string       `protobuf:"bytes,14,opt,name=new_lyric_url,json=newLyricUrl,proto3" json:"new_lyric_url,omitempty"`
	NewLyricMd5               string       `protobuf:"bytes,15,opt,name=new_lyric_md5,json=newLyricMd5,proto3" json:"new_lyric_md5,omitempty"`
	BgVideoUrl                string       `protobuf:"bytes,16,opt,name=bg_video_url,json=bgVideoUrl,proto3" json:"bg_video_url,omitempty"`
	BgVideoMd5                string       `protobuf:"bytes,17,opt,name=bg_video_md5,json=bgVideoMd5,proto3" json:"bg_video_md5,omitempty"`
	FriendCircleHeatDatumLine uint64       `protobuf:"varint,18,opt,name=friend_circle_heat_datum_line,json=friendCircleHeatDatumLine,proto3" json:"friend_circle_heat_datum_line,omitempty"`
	AiType                    uint32       `protobuf:"varint,19,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	CardBgVideoUrl            *CardBGVideo `protobuf:"bytes,20,opt,name=card_bg_video_url,json=cardBgVideoUrl,proto3" json:"card_bg_video_url,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}     `json:"-"`
	XXX_unrecognized          []byte       `json:"-"`
	XXX_sizecache             int32        `json:"-"`
}

func (m *LyricInfo) Reset()         { *m = LyricInfo{} }
func (m *LyricInfo) String() string { return proto.CompactTextString(m) }
func (*LyricInfo) ProtoMessage()    {}
func (*LyricInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{69}
}
func (m *LyricInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LyricInfo.Unmarshal(m, b)
}
func (m *LyricInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LyricInfo.Marshal(b, m, deterministic)
}
func (dst *LyricInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LyricInfo.Merge(dst, src)
}
func (m *LyricInfo) XXX_Size() int {
	return xxx_messageInfo_LyricInfo.Size(m)
}
func (m *LyricInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LyricInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LyricInfo proto.InternalMessageInfo

func (m *LyricInfo) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *LyricInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *LyricInfo) GetRapName() string {
	if m != nil {
		return m.RapName
	}
	return ""
}

func (m *LyricInfo) GetLyricContent() []string {
	if m != nil {
		return m.LyricContent
	}
	return nil
}

func (m *LyricInfo) GetLyricUrl() string {
	if m != nil {
		return m.LyricUrl
	}
	return ""
}

func (m *LyricInfo) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *LyricInfo) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

func (m *LyricInfo) GetLevelScore() uint32 {
	if m != nil {
		return m.LevelScore
	}
	return 0
}

func (m *LyricInfo) GetIsLocked() bool {
	if m != nil {
		return m.IsLocked
	}
	return false
}

func (m *LyricInfo) GetExcerptContentUrl() string {
	if m != nil {
		return m.ExcerptContentUrl
	}
	return ""
}

func (m *LyricInfo) GetExcerptContentMd5() string {
	if m != nil {
		return m.ExcerptContentMd5
	}
	return ""
}

func (m *LyricInfo) GetFriendCircleHeat() uint64 {
	if m != nil {
		return m.FriendCircleHeat
	}
	return 0
}

func (m *LyricInfo) GetProducerNickName() string {
	if m != nil {
		return m.ProducerNickName
	}
	return ""
}

func (m *LyricInfo) GetNewLyricUrl() string {
	if m != nil {
		return m.NewLyricUrl
	}
	return ""
}

func (m *LyricInfo) GetNewLyricMd5() string {
	if m != nil {
		return m.NewLyricMd5
	}
	return ""
}

func (m *LyricInfo) GetBgVideoUrl() string {
	if m != nil {
		return m.BgVideoUrl
	}
	return ""
}

func (m *LyricInfo) GetBgVideoMd5() string {
	if m != nil {
		return m.BgVideoMd5
	}
	return ""
}

func (m *LyricInfo) GetFriendCircleHeatDatumLine() uint64 {
	if m != nil {
		return m.FriendCircleHeatDatumLine
	}
	return 0
}

func (m *LyricInfo) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

func (m *LyricInfo) GetCardBgVideoUrl() *CardBGVideo {
	if m != nil {
		return m.CardBgVideoUrl
	}
	return nil
}

type GetLyricInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LyricInfo            []*LyricInfo  `protobuf:"bytes,2,rep,name=lyric_info,json=lyricInfo,proto3" json:"lyric_info,omitempty"`
	HasMore              bool          `protobuf:"varint,3,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	IsOpenOriginalSound  bool          `protobuf:"varint,4,opt,name=is_open_original_sound,json=isOpenOriginalSound,proto3" json:"is_open_original_sound,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetLyricInfoResp) Reset()         { *m = GetLyricInfoResp{} }
func (m *GetLyricInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetLyricInfoResp) ProtoMessage()    {}
func (*GetLyricInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{70}
}
func (m *GetLyricInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLyricInfoResp.Unmarshal(m, b)
}
func (m *GetLyricInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLyricInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetLyricInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLyricInfoResp.Merge(dst, src)
}
func (m *GetLyricInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetLyricInfoResp.Size(m)
}
func (m *GetLyricInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLyricInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLyricInfoResp proto.InternalMessageInfo

func (m *GetLyricInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetLyricInfoResp) GetLyricInfo() []*LyricInfo {
	if m != nil {
		return m.LyricInfo
	}
	return nil
}

func (m *GetLyricInfoResp) GetHasMore() bool {
	if m != nil {
		return m.HasMore
	}
	return false
}

func (m *GetLyricInfoResp) GetIsOpenOriginalSound() bool {
	if m != nil {
		return m.IsOpenOriginalSound
	}
	return false
}

type AggregationInfo struct {
	AggregationChoose    AggregationChoose `protobuf:"varint,1,opt,name=aggregation_choose,json=aggregationChoose,proto3,enum=ga.rhythm.AggregationChoose" json:"aggregation_choose,omitempty"`
	SongId               string            `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Number               uint32            `protobuf:"varint,3,opt,name=number,proto3" json:"number,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AggregationInfo) Reset()         { *m = AggregationInfo{} }
func (m *AggregationInfo) String() string { return proto.CompactTextString(m) }
func (*AggregationInfo) ProtoMessage()    {}
func (*AggregationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{71}
}
func (m *AggregationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AggregationInfo.Unmarshal(m, b)
}
func (m *AggregationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AggregationInfo.Marshal(b, m, deterministic)
}
func (dst *AggregationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AggregationInfo.Merge(dst, src)
}
func (m *AggregationInfo) XXX_Size() int {
	return xxx_messageInfo_AggregationInfo.Size(m)
}
func (m *AggregationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AggregationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AggregationInfo proto.InternalMessageInfo

func (m *AggregationInfo) GetAggregationChoose() AggregationChoose {
	if m != nil {
		return m.AggregationChoose
	}
	return AggregationChoose_EXposure
}

func (m *AggregationInfo) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *AggregationInfo) GetNumber() uint32 {
	if m != nil {
		return m.Number
	}
	return 0
}

type AggregationInfoReq struct {
	BaseReq              *app.BaseReq       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	AggregationInfo      []*AggregationInfo `protobuf:"bytes,2,rep,name=aggregation_info,json=aggregationInfo,proto3" json:"aggregation_info,omitempty"`
	AiType               uint32             `protobuf:"varint,3,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AggregationInfoReq) Reset()         { *m = AggregationInfoReq{} }
func (m *AggregationInfoReq) String() string { return proto.CompactTextString(m) }
func (*AggregationInfoReq) ProtoMessage()    {}
func (*AggregationInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{72}
}
func (m *AggregationInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AggregationInfoReq.Unmarshal(m, b)
}
func (m *AggregationInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AggregationInfoReq.Marshal(b, m, deterministic)
}
func (dst *AggregationInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AggregationInfoReq.Merge(dst, src)
}
func (m *AggregationInfoReq) XXX_Size() int {
	return xxx_messageInfo_AggregationInfoReq.Size(m)
}
func (m *AggregationInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AggregationInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_AggregationInfoReq proto.InternalMessageInfo

func (m *AggregationInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AggregationInfoReq) GetAggregationInfo() []*AggregationInfo {
	if m != nil {
		return m.AggregationInfo
	}
	return nil
}

func (m *AggregationInfoReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type AggregationInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AggregationInfoResp) Reset()         { *m = AggregationInfoResp{} }
func (m *AggregationInfoResp) String() string { return proto.CompactTextString(m) }
func (*AggregationInfoResp) ProtoMessage()    {}
func (*AggregationInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{73}
}
func (m *AggregationInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AggregationInfoResp.Unmarshal(m, b)
}
func (m *AggregationInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AggregationInfoResp.Marshal(b, m, deterministic)
}
func (dst *AggregationInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AggregationInfoResp.Merge(dst, src)
}
func (m *AggregationInfoResp) XXX_Size() int {
	return xxx_messageInfo_AggregationInfoResp.Size(m)
}
func (m *AggregationInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AggregationInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_AggregationInfoResp proto.InternalMessageInfo

func (m *AggregationInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取用户体验小问卷
type GetQuestionnaireReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SongId               string       `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	AiType               uint32       `protobuf:"varint,3,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetQuestionnaireReq) Reset()         { *m = GetQuestionnaireReq{} }
func (m *GetQuestionnaireReq) String() string { return proto.CompactTextString(m) }
func (*GetQuestionnaireReq) ProtoMessage()    {}
func (*GetQuestionnaireReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{74}
}
func (m *GetQuestionnaireReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuestionnaireReq.Unmarshal(m, b)
}
func (m *GetQuestionnaireReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuestionnaireReq.Marshal(b, m, deterministic)
}
func (dst *GetQuestionnaireReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuestionnaireReq.Merge(dst, src)
}
func (m *GetQuestionnaireReq) XXX_Size() int {
	return xxx_messageInfo_GetQuestionnaireReq.Size(m)
}
func (m *GetQuestionnaireReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuestionnaireReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuestionnaireReq proto.InternalMessageInfo

func (m *GetQuestionnaireReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetQuestionnaireReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *GetQuestionnaireReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type GetQuestionnaireResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	DisplayFlag          bool          `protobuf:"varint,2,opt,name=display_flag,json=displayFlag,proto3" json:"display_flag,omitempty"`
	Topic                string        `protobuf:"bytes,3,opt,name=topic,proto3" json:"topic,omitempty"`
	Options              []string      `protobuf:"bytes,4,rep,name=options,proto3" json:"options,omitempty"`
	FeedbackText         string        `protobuf:"bytes,5,opt,name=feedback_text,json=feedbackText,proto3" json:"feedback_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetQuestionnaireResp) Reset()         { *m = GetQuestionnaireResp{} }
func (m *GetQuestionnaireResp) String() string { return proto.CompactTextString(m) }
func (*GetQuestionnaireResp) ProtoMessage()    {}
func (*GetQuestionnaireResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{75}
}
func (m *GetQuestionnaireResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuestionnaireResp.Unmarshal(m, b)
}
func (m *GetQuestionnaireResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuestionnaireResp.Marshal(b, m, deterministic)
}
func (dst *GetQuestionnaireResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuestionnaireResp.Merge(dst, src)
}
func (m *GetQuestionnaireResp) XXX_Size() int {
	return xxx_messageInfo_GetQuestionnaireResp.Size(m)
}
func (m *GetQuestionnaireResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuestionnaireResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuestionnaireResp proto.InternalMessageInfo

func (m *GetQuestionnaireResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetQuestionnaireResp) GetDisplayFlag() bool {
	if m != nil {
		return m.DisplayFlag
	}
	return false
}

func (m *GetQuestionnaireResp) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *GetQuestionnaireResp) GetOptions() []string {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *GetQuestionnaireResp) GetFeedbackText() string {
	if m != nil {
		return m.FeedbackText
	}
	return ""
}

// ai 说唱三期 分享朋友圈
type GetH5UrlWithAICtxReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	BackgroundVideoUrl   string       `protobuf:"bytes,2,opt,name=background_video_url,json=backgroundVideoUrl,proto3" json:"background_video_url,omitempty"`
	VoiceUrl             string       `protobuf:"bytes,3,opt,name=voice_url,json=voiceUrl,proto3" json:"voice_url,omitempty"`
	SongName             string       `protobuf:"bytes,4,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	Lyrics               []string     `protobuf:"bytes,5,rep,name=lyrics,proto3" json:"lyrics,omitempty"`
	SongId               string       `protobuf:"bytes,6,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	AiType               uint32       `protobuf:"varint,7,opt,name=ai_type,json=aiType,proto3" json:"ai_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetH5UrlWithAICtxReq) Reset()         { *m = GetH5UrlWithAICtxReq{} }
func (m *GetH5UrlWithAICtxReq) String() string { return proto.CompactTextString(m) }
func (*GetH5UrlWithAICtxReq) ProtoMessage()    {}
func (*GetH5UrlWithAICtxReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{76}
}
func (m *GetH5UrlWithAICtxReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetH5UrlWithAICtxReq.Unmarshal(m, b)
}
func (m *GetH5UrlWithAICtxReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetH5UrlWithAICtxReq.Marshal(b, m, deterministic)
}
func (dst *GetH5UrlWithAICtxReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetH5UrlWithAICtxReq.Merge(dst, src)
}
func (m *GetH5UrlWithAICtxReq) XXX_Size() int {
	return xxx_messageInfo_GetH5UrlWithAICtxReq.Size(m)
}
func (m *GetH5UrlWithAICtxReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetH5UrlWithAICtxReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetH5UrlWithAICtxReq proto.InternalMessageInfo

func (m *GetH5UrlWithAICtxReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetH5UrlWithAICtxReq) GetBackgroundVideoUrl() string {
	if m != nil {
		return m.BackgroundVideoUrl
	}
	return ""
}

func (m *GetH5UrlWithAICtxReq) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

func (m *GetH5UrlWithAICtxReq) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *GetH5UrlWithAICtxReq) GetLyrics() []string {
	if m != nil {
		return m.Lyrics
	}
	return nil
}

func (m *GetH5UrlWithAICtxReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *GetH5UrlWithAICtxReq) GetAiType() uint32 {
	if m != nil {
		return m.AiType
	}
	return 0
}

type GetH5UrlWithAICtxResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	H5Url                string        `protobuf:"bytes,2,opt,name=h5_url,json=h5Url,proto3" json:"h5_url,omitempty"`
	MainTitle            string        `protobuf:"bytes,3,opt,name=main_title,json=mainTitle,proto3" json:"main_title,omitempty"`
	SubTitle             string        `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	MainText             string        `protobuf:"bytes,5,opt,name=main_text,json=mainText,proto3" json:"main_text,omitempty"`
	Topic                []string      `protobuf:"bytes,6,rep,name=topic,proto3" json:"topic,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetH5UrlWithAICtxResp) Reset()         { *m = GetH5UrlWithAICtxResp{} }
func (m *GetH5UrlWithAICtxResp) String() string { return proto.CompactTextString(m) }
func (*GetH5UrlWithAICtxResp) ProtoMessage()    {}
func (*GetH5UrlWithAICtxResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{77}
}
func (m *GetH5UrlWithAICtxResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetH5UrlWithAICtxResp.Unmarshal(m, b)
}
func (m *GetH5UrlWithAICtxResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetH5UrlWithAICtxResp.Marshal(b, m, deterministic)
}
func (dst *GetH5UrlWithAICtxResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetH5UrlWithAICtxResp.Merge(dst, src)
}
func (m *GetH5UrlWithAICtxResp) XXX_Size() int {
	return xxx_messageInfo_GetH5UrlWithAICtxResp.Size(m)
}
func (m *GetH5UrlWithAICtxResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetH5UrlWithAICtxResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetH5UrlWithAICtxResp proto.InternalMessageInfo

func (m *GetH5UrlWithAICtxResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetH5UrlWithAICtxResp) GetH5Url() string {
	if m != nil {
		return m.H5Url
	}
	return ""
}

func (m *GetH5UrlWithAICtxResp) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *GetH5UrlWithAICtxResp) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *GetH5UrlWithAICtxResp) GetMainText() string {
	if m != nil {
		return m.MainText
	}
	return ""
}

func (m *GetH5UrlWithAICtxResp) GetTopic() []string {
	if m != nil {
		return m.Topic
	}
	return nil
}

// 获取筛选器
type GetMusicBlockFilterReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	FilterType           string       `protobuf:"bytes,2,opt,name=filter_type,json=filterType,proto3" json:"filter_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMusicBlockFilterReq) Reset()         { *m = GetMusicBlockFilterReq{} }
func (m *GetMusicBlockFilterReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicBlockFilterReq) ProtoMessage()    {}
func (*GetMusicBlockFilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{78}
}
func (m *GetMusicBlockFilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicBlockFilterReq.Unmarshal(m, b)
}
func (m *GetMusicBlockFilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicBlockFilterReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicBlockFilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicBlockFilterReq.Merge(dst, src)
}
func (m *GetMusicBlockFilterReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicBlockFilterReq.Size(m)
}
func (m *GetMusicBlockFilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicBlockFilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicBlockFilterReq proto.InternalMessageInfo

func (m *GetMusicBlockFilterReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMusicBlockFilterReq) GetFilterType() string {
	if m != nil {
		return m.FilterType
	}
	return ""
}

type GetMusicBlockFilterResp struct {
	BaseResp             *app.BaseResp                         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	FilterItems          []*GetMusicBlockFilterResp_FilterItem `protobuf:"bytes,2,rep,name=filter_items,json=filterItems,proto3" json:"filter_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *GetMusicBlockFilterResp) Reset()         { *m = GetMusicBlockFilterResp{} }
func (m *GetMusicBlockFilterResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicBlockFilterResp) ProtoMessage()    {}
func (*GetMusicBlockFilterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{79}
}
func (m *GetMusicBlockFilterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicBlockFilterResp.Unmarshal(m, b)
}
func (m *GetMusicBlockFilterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicBlockFilterResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicBlockFilterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicBlockFilterResp.Merge(dst, src)
}
func (m *GetMusicBlockFilterResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicBlockFilterResp.Size(m)
}
func (m *GetMusicBlockFilterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicBlockFilterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicBlockFilterResp proto.InternalMessageInfo

func (m *GetMusicBlockFilterResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMusicBlockFilterResp) GetFilterItems() []*GetMusicBlockFilterResp_FilterItem {
	if m != nil {
		return m.FilterItems
	}
	return nil
}

type GetMusicBlockFilterResp_FilterItem struct {
	Title                string                                   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FilterItemType       string                                   `protobuf:"bytes,2,opt,name=filter_item_type,json=filterItemType,proto3" json:"filter_item_type,omitempty"`
	FilterId             string                                   `protobuf:"bytes,3,opt,name=filter_id,json=filterId,proto3" json:"filter_id,omitempty"`
	FilterSubItems       []*GetMusicBlockFilterResp_FilterSubItem `protobuf:"bytes,4,rep,name=filter_sub_items,json=filterSubItems,proto3" json:"filter_sub_items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *GetMusicBlockFilterResp_FilterItem) Reset()         { *m = GetMusicBlockFilterResp_FilterItem{} }
func (m *GetMusicBlockFilterResp_FilterItem) String() string { return proto.CompactTextString(m) }
func (*GetMusicBlockFilterResp_FilterItem) ProtoMessage()    {}
func (*GetMusicBlockFilterResp_FilterItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{79, 0}
}
func (m *GetMusicBlockFilterResp_FilterItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicBlockFilterResp_FilterItem.Unmarshal(m, b)
}
func (m *GetMusicBlockFilterResp_FilterItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicBlockFilterResp_FilterItem.Marshal(b, m, deterministic)
}
func (dst *GetMusicBlockFilterResp_FilterItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicBlockFilterResp_FilterItem.Merge(dst, src)
}
func (m *GetMusicBlockFilterResp_FilterItem) XXX_Size() int {
	return xxx_messageInfo_GetMusicBlockFilterResp_FilterItem.Size(m)
}
func (m *GetMusicBlockFilterResp_FilterItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicBlockFilterResp_FilterItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicBlockFilterResp_FilterItem proto.InternalMessageInfo

func (m *GetMusicBlockFilterResp_FilterItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicBlockFilterResp_FilterItem) GetFilterItemType() string {
	if m != nil {
		return m.FilterItemType
	}
	return ""
}

func (m *GetMusicBlockFilterResp_FilterItem) GetFilterId() string {
	if m != nil {
		return m.FilterId
	}
	return ""
}

func (m *GetMusicBlockFilterResp_FilterItem) GetFilterSubItems() []*GetMusicBlockFilterResp_FilterSubItem {
	if m != nil {
		return m.FilterSubItems
	}
	return nil
}

type GetMusicBlockFilterResp_FilterSubItem struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	FilterSubId          string   `protobuf:"bytes,2,opt,name=filter_sub_id,json=filterSubId,proto3" json:"filter_sub_id,omitempty"`
	FilterSubItemType    string   `protobuf:"bytes,3,opt,name=filter_sub_item_type,json=filterSubItemType,proto3" json:"filter_sub_item_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicBlockFilterResp_FilterSubItem) Reset()         { *m = GetMusicBlockFilterResp_FilterSubItem{} }
func (m *GetMusicBlockFilterResp_FilterSubItem) String() string { return proto.CompactTextString(m) }
func (*GetMusicBlockFilterResp_FilterSubItem) ProtoMessage()    {}
func (*GetMusicBlockFilterResp_FilterSubItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{79, 1}
}
func (m *GetMusicBlockFilterResp_FilterSubItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicBlockFilterResp_FilterSubItem.Unmarshal(m, b)
}
func (m *GetMusicBlockFilterResp_FilterSubItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicBlockFilterResp_FilterSubItem.Marshal(b, m, deterministic)
}
func (dst *GetMusicBlockFilterResp_FilterSubItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicBlockFilterResp_FilterSubItem.Merge(dst, src)
}
func (m *GetMusicBlockFilterResp_FilterSubItem) XXX_Size() int {
	return xxx_messageInfo_GetMusicBlockFilterResp_FilterSubItem.Size(m)
}
func (m *GetMusicBlockFilterResp_FilterSubItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicBlockFilterResp_FilterSubItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicBlockFilterResp_FilterSubItem proto.InternalMessageInfo

func (m *GetMusicBlockFilterResp_FilterSubItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMusicBlockFilterResp_FilterSubItem) GetFilterSubId() string {
	if m != nil {
		return m.FilterSubId
	}
	return ""
}

func (m *GetMusicBlockFilterResp_FilterSubItem) GetFilterSubItemType() string {
	if m != nil {
		return m.FilterSubItemType
	}
	return ""
}

type GetBrandChannelBGTopicInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBrandChannelBGTopicInfoReq) Reset()         { *m = GetBrandChannelBGTopicInfoReq{} }
func (m *GetBrandChannelBGTopicInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBrandChannelBGTopicInfoReq) ProtoMessage()    {}
func (*GetBrandChannelBGTopicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{80}
}
func (m *GetBrandChannelBGTopicInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandChannelBGTopicInfoReq.Unmarshal(m, b)
}
func (m *GetBrandChannelBGTopicInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandChannelBGTopicInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBrandChannelBGTopicInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandChannelBGTopicInfoReq.Merge(dst, src)
}
func (m *GetBrandChannelBGTopicInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBrandChannelBGTopicInfoReq.Size(m)
}
func (m *GetBrandChannelBGTopicInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandChannelBGTopicInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandChannelBGTopicInfoReq proto.InternalMessageInfo

func (m *GetBrandChannelBGTopicInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBrandChannelBGTopicInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetBrandChannelBGTopicInfoResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *BrandChannelBGTopicNotify `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetBrandChannelBGTopicInfoResp) Reset()         { *m = GetBrandChannelBGTopicInfoResp{} }
func (m *GetBrandChannelBGTopicInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBrandChannelBGTopicInfoResp) ProtoMessage()    {}
func (*GetBrandChannelBGTopicInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{81}
}
func (m *GetBrandChannelBGTopicInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBrandChannelBGTopicInfoResp.Unmarshal(m, b)
}
func (m *GetBrandChannelBGTopicInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBrandChannelBGTopicInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBrandChannelBGTopicInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBrandChannelBGTopicInfoResp.Merge(dst, src)
}
func (m *GetBrandChannelBGTopicInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBrandChannelBGTopicInfoResp.Size(m)
}
func (m *GetBrandChannelBGTopicInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBrandChannelBGTopicInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBrandChannelBGTopicInfoResp proto.InternalMessageInfo

func (m *GetBrandChannelBGTopicInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBrandChannelBGTopicInfoResp) GetInfo() *BrandChannelBGTopicNotify {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpgradeUserAIRapperLevelReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpgradeUserAIRapperLevelReq) Reset()         { *m = UpgradeUserAIRapperLevelReq{} }
func (m *UpgradeUserAIRapperLevelReq) String() string { return proto.CompactTextString(m) }
func (*UpgradeUserAIRapperLevelReq) ProtoMessage()    {}
func (*UpgradeUserAIRapperLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{82}
}
func (m *UpgradeUserAIRapperLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpgradeUserAIRapperLevelReq.Unmarshal(m, b)
}
func (m *UpgradeUserAIRapperLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpgradeUserAIRapperLevelReq.Marshal(b, m, deterministic)
}
func (dst *UpgradeUserAIRapperLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpgradeUserAIRapperLevelReq.Merge(dst, src)
}
func (m *UpgradeUserAIRapperLevelReq) XXX_Size() int {
	return xxx_messageInfo_UpgradeUserAIRapperLevelReq.Size(m)
}
func (m *UpgradeUserAIRapperLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpgradeUserAIRapperLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpgradeUserAIRapperLevelReq proto.InternalMessageInfo

func (m *UpgradeUserAIRapperLevelReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type UpgradeUserAIRapperLevelResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpgradeUserAIRapperLevelResp) Reset()         { *m = UpgradeUserAIRapperLevelResp{} }
func (m *UpgradeUserAIRapperLevelResp) String() string { return proto.CompactTextString(m) }
func (*UpgradeUserAIRapperLevelResp) ProtoMessage()    {}
func (*UpgradeUserAIRapperLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{83}
}
func (m *UpgradeUserAIRapperLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpgradeUserAIRapperLevelResp.Unmarshal(m, b)
}
func (m *UpgradeUserAIRapperLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpgradeUserAIRapperLevelResp.Marshal(b, m, deterministic)
}
func (dst *UpgradeUserAIRapperLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpgradeUserAIRapperLevelResp.Merge(dst, src)
}
func (m *UpgradeUserAIRapperLevelResp) XXX_Size() int {
	return xxx_messageInfo_UpgradeUserAIRapperLevelResp.Size(m)
}
func (m *UpgradeUserAIRapperLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpgradeUserAIRapperLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpgradeUserAIRapperLevelResp proto.InternalMessageInfo

func (m *UpgradeUserAIRapperLevelResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type RhythmRecommendRoomItem struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelOwnerAccount  string   `protobuf:"bytes,3,opt,name=channel_owner_account,json=channelOwnerAccount,proto3" json:"channel_owner_account,omitempty"`
	ChannelOwnerSex      int32    `protobuf:"varint,4,opt,name=channel_owner_sex,json=channelOwnerSex,proto3" json:"channel_owner_sex,omitempty"`
	ChannelMemberCount   uint32   `protobuf:"varint,5,opt,name=channel_member_count,json=channelMemberCount,proto3" json:"channel_member_count,omitempty"`
	PbViewData           []byte   `protobuf:"bytes,6,opt,name=pb_view_data,json=pbViewData,proto3" json:"pb_view_data,omitempty"`
	PublishDesc          string   `protobuf:"bytes,7,opt,name=publish_desc,json=publishDesc,proto3" json:"publish_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RhythmRecommendRoomItem) Reset()         { *m = RhythmRecommendRoomItem{} }
func (m *RhythmRecommendRoomItem) String() string { return proto.CompactTextString(m) }
func (*RhythmRecommendRoomItem) ProtoMessage()    {}
func (*RhythmRecommendRoomItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{84}
}
func (m *RhythmRecommendRoomItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RhythmRecommendRoomItem.Unmarshal(m, b)
}
func (m *RhythmRecommendRoomItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RhythmRecommendRoomItem.Marshal(b, m, deterministic)
}
func (dst *RhythmRecommendRoomItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RhythmRecommendRoomItem.Merge(dst, src)
}
func (m *RhythmRecommendRoomItem) XXX_Size() int {
	return xxx_messageInfo_RhythmRecommendRoomItem.Size(m)
}
func (m *RhythmRecommendRoomItem) XXX_DiscardUnknown() {
	xxx_messageInfo_RhythmRecommendRoomItem.DiscardUnknown(m)
}

var xxx_messageInfo_RhythmRecommendRoomItem proto.InternalMessageInfo

func (m *RhythmRecommendRoomItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RhythmRecommendRoomItem) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *RhythmRecommendRoomItem) GetChannelOwnerAccount() string {
	if m != nil {
		return m.ChannelOwnerAccount
	}
	return ""
}

func (m *RhythmRecommendRoomItem) GetChannelOwnerSex() int32 {
	if m != nil {
		return m.ChannelOwnerSex
	}
	return 0
}

func (m *RhythmRecommendRoomItem) GetChannelMemberCount() uint32 {
	if m != nil {
		return m.ChannelMemberCount
	}
	return 0
}

func (m *RhythmRecommendRoomItem) GetPbViewData() []byte {
	if m != nil {
		return m.PbViewData
	}
	return nil
}

func (m *RhythmRecommendRoomItem) GetPublishDesc() string {
	if m != nil {
		return m.PublishDesc
	}
	return ""
}

type RhythmGetRecommendRoomReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RhythmGetRecommendRoomReq) Reset()         { *m = RhythmGetRecommendRoomReq{} }
func (m *RhythmGetRecommendRoomReq) String() string { return proto.CompactTextString(m) }
func (*RhythmGetRecommendRoomReq) ProtoMessage()    {}
func (*RhythmGetRecommendRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{85}
}
func (m *RhythmGetRecommendRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RhythmGetRecommendRoomReq.Unmarshal(m, b)
}
func (m *RhythmGetRecommendRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RhythmGetRecommendRoomReq.Marshal(b, m, deterministic)
}
func (dst *RhythmGetRecommendRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RhythmGetRecommendRoomReq.Merge(dst, src)
}
func (m *RhythmGetRecommendRoomReq) XXX_Size() int {
	return xxx_messageInfo_RhythmGetRecommendRoomReq.Size(m)
}
func (m *RhythmGetRecommendRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RhythmGetRecommendRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_RhythmGetRecommendRoomReq proto.InternalMessageInfo

func (m *RhythmGetRecommendRoomReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type RhythmGetRecommendRoomResp struct {
	BaseResp             *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Item                 []*RhythmRecommendRoomItem `protobuf:"bytes,2,rep,name=item,proto3" json:"item,omitempty"`
	DynamicText          string                     `protobuf:"bytes,3,opt,name=dynamic_text,json=dynamicText,proto3" json:"dynamic_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *RhythmGetRecommendRoomResp) Reset()         { *m = RhythmGetRecommendRoomResp{} }
func (m *RhythmGetRecommendRoomResp) String() string { return proto.CompactTextString(m) }
func (*RhythmGetRecommendRoomResp) ProtoMessage()    {}
func (*RhythmGetRecommendRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{86}
}
func (m *RhythmGetRecommendRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RhythmGetRecommendRoomResp.Unmarshal(m, b)
}
func (m *RhythmGetRecommendRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RhythmGetRecommendRoomResp.Marshal(b, m, deterministic)
}
func (dst *RhythmGetRecommendRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RhythmGetRecommendRoomResp.Merge(dst, src)
}
func (m *RhythmGetRecommendRoomResp) XXX_Size() int {
	return xxx_messageInfo_RhythmGetRecommendRoomResp.Size(m)
}
func (m *RhythmGetRecommendRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RhythmGetRecommendRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_RhythmGetRecommendRoomResp proto.InternalMessageInfo

func (m *RhythmGetRecommendRoomResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *RhythmGetRecommendRoomResp) GetItem() []*RhythmRecommendRoomItem {
	if m != nil {
		return m.Item
	}
	return nil
}

func (m *RhythmGetRecommendRoomResp) GetDynamicText() string {
	if m != nil {
		return m.DynamicText
	}
	return ""
}

type RhythmGetPostsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RhythmGetPostsReq) Reset()         { *m = RhythmGetPostsReq{} }
func (m *RhythmGetPostsReq) String() string { return proto.CompactTextString(m) }
func (*RhythmGetPostsReq) ProtoMessage()    {}
func (*RhythmGetPostsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{87}
}
func (m *RhythmGetPostsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RhythmGetPostsReq.Unmarshal(m, b)
}
func (m *RhythmGetPostsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RhythmGetPostsReq.Marshal(b, m, deterministic)
}
func (dst *RhythmGetPostsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RhythmGetPostsReq.Merge(dst, src)
}
func (m *RhythmGetPostsReq) XXX_Size() int {
	return xxx_messageInfo_RhythmGetPostsReq.Size(m)
}
func (m *RhythmGetPostsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RhythmGetPostsReq.DiscardUnknown(m)
}

var xxx_messageInfo_RhythmGetPostsReq proto.InternalMessageInfo

func (m *RhythmGetPostsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type RhythmGetPostsResp struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Item                 []*RhythmGetPostsItem `protobuf:"bytes,2,rep,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *RhythmGetPostsResp) Reset()         { *m = RhythmGetPostsResp{} }
func (m *RhythmGetPostsResp) String() string { return proto.CompactTextString(m) }
func (*RhythmGetPostsResp) ProtoMessage()    {}
func (*RhythmGetPostsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{88}
}
func (m *RhythmGetPostsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RhythmGetPostsResp.Unmarshal(m, b)
}
func (m *RhythmGetPostsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RhythmGetPostsResp.Marshal(b, m, deterministic)
}
func (dst *RhythmGetPostsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RhythmGetPostsResp.Merge(dst, src)
}
func (m *RhythmGetPostsResp) XXX_Size() int {
	return xxx_messageInfo_RhythmGetPostsResp.Size(m)
}
func (m *RhythmGetPostsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RhythmGetPostsResp.DiscardUnknown(m)
}

var xxx_messageInfo_RhythmGetPostsResp proto.InternalMessageInfo

func (m *RhythmGetPostsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *RhythmGetPostsResp) GetItem() []*RhythmGetPostsItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type RhythmGetPostsItem struct {
	MainTitle            string   `protobuf:"bytes,1,opt,name=main_title,json=mainTitle,proto3" json:"main_title,omitempty"`
	SubTitle             string   `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	UrlLink              string   `protobuf:"bytes,3,opt,name=url_link,json=urlLink,proto3" json:"url_link,omitempty"`
	UserImage            string   `protobuf:"bytes,4,opt,name=user_image,json=userImage,proto3" json:"user_image,omitempty"`
	TopicId              string   `protobuf:"bytes,5,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RhythmGetPostsItem) Reset()         { *m = RhythmGetPostsItem{} }
func (m *RhythmGetPostsItem) String() string { return proto.CompactTextString(m) }
func (*RhythmGetPostsItem) ProtoMessage()    {}
func (*RhythmGetPostsItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{89}
}
func (m *RhythmGetPostsItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RhythmGetPostsItem.Unmarshal(m, b)
}
func (m *RhythmGetPostsItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RhythmGetPostsItem.Marshal(b, m, deterministic)
}
func (dst *RhythmGetPostsItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RhythmGetPostsItem.Merge(dst, src)
}
func (m *RhythmGetPostsItem) XXX_Size() int {
	return xxx_messageInfo_RhythmGetPostsItem.Size(m)
}
func (m *RhythmGetPostsItem) XXX_DiscardUnknown() {
	xxx_messageInfo_RhythmGetPostsItem.DiscardUnknown(m)
}

var xxx_messageInfo_RhythmGetPostsItem proto.InternalMessageInfo

func (m *RhythmGetPostsItem) GetMainTitle() string {
	if m != nil {
		return m.MainTitle
	}
	return ""
}

func (m *RhythmGetPostsItem) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *RhythmGetPostsItem) GetUrlLink() string {
	if m != nil {
		return m.UrlLink
	}
	return ""
}

func (m *RhythmGetPostsItem) GetUserImage() string {
	if m != nil {
		return m.UserImage
	}
	return ""
}

func (m *RhythmGetPostsItem) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

// 停留加票 6.13.0
type ReportStayAddTicketReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ActId                uint32       `protobuf:"varint,4,opt,name=act_id,json=actId,proto3" json:"act_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReportStayAddTicketReq) Reset()         { *m = ReportStayAddTicketReq{} }
func (m *ReportStayAddTicketReq) String() string { return proto.CompactTextString(m) }
func (*ReportStayAddTicketReq) ProtoMessage()    {}
func (*ReportStayAddTicketReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{90}
}
func (m *ReportStayAddTicketReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportStayAddTicketReq.Unmarshal(m, b)
}
func (m *ReportStayAddTicketReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportStayAddTicketReq.Marshal(b, m, deterministic)
}
func (dst *ReportStayAddTicketReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportStayAddTicketReq.Merge(dst, src)
}
func (m *ReportStayAddTicketReq) XXX_Size() int {
	return xxx_messageInfo_ReportStayAddTicketReq.Size(m)
}
func (m *ReportStayAddTicketReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportStayAddTicketReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportStayAddTicketReq proto.InternalMessageInfo

func (m *ReportStayAddTicketReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportStayAddTicketReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportStayAddTicketReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportStayAddTicketReq) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

type StayAddTicketInfo struct {
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	StayInterval         uint32   `protobuf:"varint,2,opt,name=stay_interval,json=stayInterval,proto3" json:"stay_interval,omitempty"`
	ActId                uint32   `protobuf:"varint,3,opt,name=act_id,json=actId,proto3" json:"act_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TicketCnt            uint32   `protobuf:"varint,5,opt,name=ticket_cnt,json=ticketCnt,proto3" json:"ticket_cnt,omitempty"`
	TotalAddTicketCnt    uint32   `protobuf:"varint,6,opt,name=total_add_ticket_cnt,json=totalAddTicketCnt,proto3" json:"total_add_ticket_cnt,omitempty"`
	UserAddTicketCnt     uint32   `protobuf:"varint,7,opt,name=user_add_ticket_cnt,json=userAddTicketCnt,proto3" json:"user_add_ticket_cnt,omitempty"`
	UserRemainTicketCnt  uint32   `protobuf:"varint,8,opt,name=user_remain_ticket_cnt,json=userRemainTicketCnt,proto3" json:"user_remain_ticket_cnt,omitempty"`
	IsEndAct             bool     `protobuf:"varint,9,opt,name=is_end_act,json=isEndAct,proto3" json:"is_end_act,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StayAddTicketInfo) Reset()         { *m = StayAddTicketInfo{} }
func (m *StayAddTicketInfo) String() string { return proto.CompactTextString(m) }
func (*StayAddTicketInfo) ProtoMessage()    {}
func (*StayAddTicketInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{91}
}
func (m *StayAddTicketInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StayAddTicketInfo.Unmarshal(m, b)
}
func (m *StayAddTicketInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StayAddTicketInfo.Marshal(b, m, deterministic)
}
func (dst *StayAddTicketInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StayAddTicketInfo.Merge(dst, src)
}
func (m *StayAddTicketInfo) XXX_Size() int {
	return xxx_messageInfo_StayAddTicketInfo.Size(m)
}
func (m *StayAddTicketInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StayAddTicketInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StayAddTicketInfo proto.InternalMessageInfo

func (m *StayAddTicketInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StayAddTicketInfo) GetStayInterval() uint32 {
	if m != nil {
		return m.StayInterval
	}
	return 0
}

func (m *StayAddTicketInfo) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *StayAddTicketInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StayAddTicketInfo) GetTicketCnt() uint32 {
	if m != nil {
		return m.TicketCnt
	}
	return 0
}

func (m *StayAddTicketInfo) GetTotalAddTicketCnt() uint32 {
	if m != nil {
		return m.TotalAddTicketCnt
	}
	return 0
}

func (m *StayAddTicketInfo) GetUserAddTicketCnt() uint32 {
	if m != nil {
		return m.UserAddTicketCnt
	}
	return 0
}

func (m *StayAddTicketInfo) GetUserRemainTicketCnt() uint32 {
	if m != nil {
		return m.UserRemainTicketCnt
	}
	return 0
}

func (m *StayAddTicketInfo) GetIsEndAct() bool {
	if m != nil {
		return m.IsEndAct
	}
	return false
}

type ReportStayAddTicketResp struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *StayAddTicketInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ReportStayAddTicketResp) Reset()         { *m = ReportStayAddTicketResp{} }
func (m *ReportStayAddTicketResp) String() string { return proto.CompactTextString(m) }
func (*ReportStayAddTicketResp) ProtoMessage()    {}
func (*ReportStayAddTicketResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{92}
}
func (m *ReportStayAddTicketResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportStayAddTicketResp.Unmarshal(m, b)
}
func (m *ReportStayAddTicketResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportStayAddTicketResp.Marshal(b, m, deterministic)
}
func (dst *ReportStayAddTicketResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportStayAddTicketResp.Merge(dst, src)
}
func (m *ReportStayAddTicketResp) XXX_Size() int {
	return xxx_messageInfo_ReportStayAddTicketResp.Size(m)
}
func (m *ReportStayAddTicketResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportStayAddTicketResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportStayAddTicketResp proto.InternalMessageInfo

func (m *ReportStayAddTicketResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ReportStayAddTicketResp) GetInfo() *StayAddTicketInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type StayAddTicketNotify struct {
	Info                 *StayAddTicketInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *StayAddTicketNotify) Reset()         { *m = StayAddTicketNotify{} }
func (m *StayAddTicketNotify) String() string { return proto.CompactTextString(m) }
func (*StayAddTicketNotify) ProtoMessage()    {}
func (*StayAddTicketNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{93}
}
func (m *StayAddTicketNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StayAddTicketNotify.Unmarshal(m, b)
}
func (m *StayAddTicketNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StayAddTicketNotify.Marshal(b, m, deterministic)
}
func (dst *StayAddTicketNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StayAddTicketNotify.Merge(dst, src)
}
func (m *StayAddTicketNotify) XXX_Size() int {
	return xxx_messageInfo_StayAddTicketNotify.Size(m)
}
func (m *StayAddTicketNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_StayAddTicketNotify.DiscardUnknown(m)
}

var xxx_messageInfo_StayAddTicketNotify proto.InternalMessageInfo

func (m *StayAddTicketNotify) GetInfo() *StayAddTicketInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetMicSortReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Info                 *MicSortInfo `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetMicSortReq) Reset()         { *m = SetMicSortReq{} }
func (m *SetMicSortReq) String() string { return proto.CompactTextString(m) }
func (*SetMicSortReq) ProtoMessage()    {}
func (*SetMicSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{94}
}
func (m *SetMicSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicSortReq.Unmarshal(m, b)
}
func (m *SetMicSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicSortReq.Marshal(b, m, deterministic)
}
func (dst *SetMicSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicSortReq.Merge(dst, src)
}
func (m *SetMicSortReq) XXX_Size() int {
	return xxx_messageInfo_SetMicSortReq.Size(m)
}
func (m *SetMicSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicSortReq proto.InternalMessageInfo

func (m *SetMicSortReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetMicSortReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMicSortReq) GetInfo() *MicSortInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetMicSortResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetMicSortResp) Reset()         { *m = SetMicSortResp{} }
func (m *SetMicSortResp) String() string { return proto.CompactTextString(m) }
func (*SetMicSortResp) ProtoMessage()    {}
func (*SetMicSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{95}
}
func (m *SetMicSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicSortResp.Unmarshal(m, b)
}
func (m *SetMicSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicSortResp.Marshal(b, m, deterministic)
}
func (dst *SetMicSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicSortResp.Merge(dst, src)
}
func (m *SetMicSortResp) XXX_Size() int {
	return xxx_messageInfo_SetMicSortResp.Size(m)
}
func (m *SetMicSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicSortResp proto.InternalMessageInfo

func (m *SetMicSortResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type MicUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicUserInfo) Reset()         { *m = MicUserInfo{} }
func (m *MicUserInfo) String() string { return proto.CompactTextString(m) }
func (*MicUserInfo) ProtoMessage()    {}
func (*MicUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{96}
}
func (m *MicUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicUserInfo.Unmarshal(m, b)
}
func (m *MicUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicUserInfo.Marshal(b, m, deterministic)
}
func (dst *MicUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicUserInfo.Merge(dst, src)
}
func (m *MicUserInfo) XXX_Size() int {
	return xxx_messageInfo_MicUserInfo.Size(m)
}
func (m *MicUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicUserInfo proto.InternalMessageInfo

func (m *MicUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MicUserInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MicUserInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type MicSortInfo struct {
	MicFlag              bool           `protobuf:"varint,1,opt,name=mic_flag,json=micFlag,proto3" json:"mic_flag,omitempty"`
	UserInfo             []*MicUserInfo `protobuf:"bytes,2,rep,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *MicSortInfo) Reset()         { *m = MicSortInfo{} }
func (m *MicSortInfo) String() string { return proto.CompactTextString(m) }
func (*MicSortInfo) ProtoMessage()    {}
func (*MicSortInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{97}
}
func (m *MicSortInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicSortInfo.Unmarshal(m, b)
}
func (m *MicSortInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicSortInfo.Marshal(b, m, deterministic)
}
func (dst *MicSortInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicSortInfo.Merge(dst, src)
}
func (m *MicSortInfo) XXX_Size() int {
	return xxx_messageInfo_MicSortInfo.Size(m)
}
func (m *MicSortInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicSortInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicSortInfo proto.InternalMessageInfo

func (m *MicSortInfo) GetMicFlag() bool {
	if m != nil {
		return m.MicFlag
	}
	return false
}

func (m *MicSortInfo) GetUserInfo() []*MicUserInfo {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

type GetMicSortReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMicSortReq) Reset()         { *m = GetMicSortReq{} }
func (m *GetMicSortReq) String() string { return proto.CompactTextString(m) }
func (*GetMicSortReq) ProtoMessage()    {}
func (*GetMicSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{98}
}
func (m *GetMicSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicSortReq.Unmarshal(m, b)
}
func (m *GetMicSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicSortReq.Marshal(b, m, deterministic)
}
func (dst *GetMicSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicSortReq.Merge(dst, src)
}
func (m *GetMicSortReq) XXX_Size() int {
	return xxx_messageInfo_GetMicSortReq.Size(m)
}
func (m *GetMicSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicSortReq proto.InternalMessageInfo

func (m *GetMicSortReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMicSortReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMicSortResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *MicSortInfo  `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetMicSortResp) Reset()         { *m = GetMicSortResp{} }
func (m *GetMicSortResp) String() string { return proto.CompactTextString(m) }
func (*GetMicSortResp) ProtoMessage()    {}
func (*GetMicSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{99}
}
func (m *GetMicSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicSortResp.Unmarshal(m, b)
}
func (m *GetMicSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicSortResp.Marshal(b, m, deterministic)
}
func (dst *GetMicSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicSortResp.Merge(dst, src)
}
func (m *GetMicSortResp) XXX_Size() int {
	return xxx_messageInfo_GetMicSortResp.Size(m)
}
func (m *GetMicSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicSortResp proto.InternalMessageInfo

func (m *GetMicSortResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMicSortResp) GetInfo() *MicSortInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SwitchMicSortReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	SwitchFlag           bool         `protobuf:"varint,2,opt,name=switch_flag,json=switchFlag,proto3" json:"switch_flag,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SwitchMicSortReq) Reset()         { *m = SwitchMicSortReq{} }
func (m *SwitchMicSortReq) String() string { return proto.CompactTextString(m) }
func (*SwitchMicSortReq) ProtoMessage()    {}
func (*SwitchMicSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{100}
}
func (m *SwitchMicSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchMicSortReq.Unmarshal(m, b)
}
func (m *SwitchMicSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchMicSortReq.Marshal(b, m, deterministic)
}
func (dst *SwitchMicSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchMicSortReq.Merge(dst, src)
}
func (m *SwitchMicSortReq) XXX_Size() int {
	return xxx_messageInfo_SwitchMicSortReq.Size(m)
}
func (m *SwitchMicSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchMicSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchMicSortReq proto.InternalMessageInfo

func (m *SwitchMicSortReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SwitchMicSortReq) GetSwitchFlag() bool {
	if m != nil {
		return m.SwitchFlag
	}
	return false
}

func (m *SwitchMicSortReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SwitchMicSortResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SwitchMicSortResp) Reset()         { *m = SwitchMicSortResp{} }
func (m *SwitchMicSortResp) String() string { return proto.CompactTextString(m) }
func (*SwitchMicSortResp) ProtoMessage()    {}
func (*SwitchMicSortResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{101}
}
func (m *SwitchMicSortResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchMicSortResp.Unmarshal(m, b)
}
func (m *SwitchMicSortResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchMicSortResp.Marshal(b, m, deterministic)
}
func (dst *SwitchMicSortResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchMicSortResp.Merge(dst, src)
}
func (m *SwitchMicSortResp) XXX_Size() int {
	return xxx_messageInfo_SwitchMicSortResp.Size(m)
}
func (m *SwitchMicSortResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchMicSortResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchMicSortResp proto.InternalMessageInfo

func (m *SwitchMicSortResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 讨论话题 强插
type GetForceTopicReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TabName              string       `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	TabType              uint32       `protobuf:"varint,4,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetForceTopicReq) Reset()         { *m = GetForceTopicReq{} }
func (m *GetForceTopicReq) String() string { return proto.CompactTextString(m) }
func (*GetForceTopicReq) ProtoMessage()    {}
func (*GetForceTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{102}
}
func (m *GetForceTopicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetForceTopicReq.Unmarshal(m, b)
}
func (m *GetForceTopicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetForceTopicReq.Marshal(b, m, deterministic)
}
func (dst *GetForceTopicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetForceTopicReq.Merge(dst, src)
}
func (m *GetForceTopicReq) XXX_Size() int {
	return xxx_messageInfo_GetForceTopicReq.Size(m)
}
func (m *GetForceTopicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetForceTopicReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetForceTopicReq proto.InternalMessageInfo

func (m *GetForceTopicReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetForceTopicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetForceTopicReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *GetForceTopicReq) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

type PostContentProfileInfo struct {
	Username             string   `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	PostContent          string   `protobuf:"bytes,2,opt,name=post_content,json=postContent,proto3" json:"post_content,omitempty"`
	AttitudeCount        uint32   `protobuf:"varint,3,opt,name=attitude_count,json=attitudeCount,proto3" json:"attitude_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostContentProfileInfo) Reset()         { *m = PostContentProfileInfo{} }
func (m *PostContentProfileInfo) String() string { return proto.CompactTextString(m) }
func (*PostContentProfileInfo) ProtoMessage()    {}
func (*PostContentProfileInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{103}
}
func (m *PostContentProfileInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostContentProfileInfo.Unmarshal(m, b)
}
func (m *PostContentProfileInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostContentProfileInfo.Marshal(b, m, deterministic)
}
func (dst *PostContentProfileInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostContentProfileInfo.Merge(dst, src)
}
func (m *PostContentProfileInfo) XXX_Size() int {
	return xxx_messageInfo_PostContentProfileInfo.Size(m)
}
func (m *PostContentProfileInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostContentProfileInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostContentProfileInfo proto.InternalMessageInfo

func (m *PostContentProfileInfo) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *PostContentProfileInfo) GetPostContent() string {
	if m != nil {
		return m.PostContent
	}
	return ""
}

func (m *PostContentProfileInfo) GetAttitudeCount() uint32 {
	if m != nil {
		return m.AttitudeCount
	}
	return 0
}

type ForceTopicInfo struct {
	TopicId              string                    `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	TopicName            string                    `protobuf:"bytes,2,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	TopicType            uint32                    `protobuf:"varint,3,opt,name=topic_type,json=topicType,proto3" json:"topic_type,omitempty"`
	MemberCnt            uint32                    `protobuf:"varint,4,opt,name=member_cnt,json=memberCnt,proto3" json:"member_cnt,omitempty"`
	PostList             []*PostContentProfileInfo `protobuf:"bytes,5,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	Username             []string                  `protobuf:"bytes,6,rep,name=username,proto3" json:"username,omitempty"`
	Position             uint32                    `protobuf:"varint,7,opt,name=position,proto3" json:"position,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ForceTopicInfo) Reset()         { *m = ForceTopicInfo{} }
func (m *ForceTopicInfo) String() string { return proto.CompactTextString(m) }
func (*ForceTopicInfo) ProtoMessage()    {}
func (*ForceTopicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{104}
}
func (m *ForceTopicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ForceTopicInfo.Unmarshal(m, b)
}
func (m *ForceTopicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ForceTopicInfo.Marshal(b, m, deterministic)
}
func (dst *ForceTopicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ForceTopicInfo.Merge(dst, src)
}
func (m *ForceTopicInfo) XXX_Size() int {
	return xxx_messageInfo_ForceTopicInfo.Size(m)
}
func (m *ForceTopicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ForceTopicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ForceTopicInfo proto.InternalMessageInfo

func (m *ForceTopicInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *ForceTopicInfo) GetTopicName() string {
	if m != nil {
		return m.TopicName
	}
	return ""
}

func (m *ForceTopicInfo) GetTopicType() uint32 {
	if m != nil {
		return m.TopicType
	}
	return 0
}

func (m *ForceTopicInfo) GetMemberCnt() uint32 {
	if m != nil {
		return m.MemberCnt
	}
	return 0
}

func (m *ForceTopicInfo) GetPostList() []*PostContentProfileInfo {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *ForceTopicInfo) GetUsername() []string {
	if m != nil {
		return m.Username
	}
	return nil
}

func (m *ForceTopicInfo) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

type GetForceTopicResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TopicList            []*ForceTopicInfo `protobuf:"bytes,2,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetForceTopicResp) Reset()         { *m = GetForceTopicResp{} }
func (m *GetForceTopicResp) String() string { return proto.CompactTextString(m) }
func (*GetForceTopicResp) ProtoMessage()    {}
func (*GetForceTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{105}
}
func (m *GetForceTopicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetForceTopicResp.Unmarshal(m, b)
}
func (m *GetForceTopicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetForceTopicResp.Marshal(b, m, deterministic)
}
func (dst *GetForceTopicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetForceTopicResp.Merge(dst, src)
}
func (m *GetForceTopicResp) XXX_Size() int {
	return xxx_messageInfo_GetForceTopicResp.Size(m)
}
func (m *GetForceTopicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetForceTopicResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetForceTopicResp proto.InternalMessageInfo

func (m *GetForceTopicResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetForceTopicResp) GetTopicList() []*ForceTopicInfo {
	if m != nil {
		return m.TopicList
	}
	return nil
}

type GetBackgroundVideoUrlReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	VideoMd5             string       `protobuf:"bytes,2,opt,name=video_md5,json=videoMd5,proto3" json:"video_md5,omitempty"`
	SongId               string       `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBackgroundVideoUrlReq) Reset()         { *m = GetBackgroundVideoUrlReq{} }
func (m *GetBackgroundVideoUrlReq) String() string { return proto.CompactTextString(m) }
func (*GetBackgroundVideoUrlReq) ProtoMessage()    {}
func (*GetBackgroundVideoUrlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{106}
}
func (m *GetBackgroundVideoUrlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBackgroundVideoUrlReq.Unmarshal(m, b)
}
func (m *GetBackgroundVideoUrlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBackgroundVideoUrlReq.Marshal(b, m, deterministic)
}
func (dst *GetBackgroundVideoUrlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBackgroundVideoUrlReq.Merge(dst, src)
}
func (m *GetBackgroundVideoUrlReq) XXX_Size() int {
	return xxx_messageInfo_GetBackgroundVideoUrlReq.Size(m)
}
func (m *GetBackgroundVideoUrlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBackgroundVideoUrlReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBackgroundVideoUrlReq proto.InternalMessageInfo

func (m *GetBackgroundVideoUrlReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBackgroundVideoUrlReq) GetVideoMd5() string {
	if m != nil {
		return m.VideoMd5
	}
	return ""
}

func (m *GetBackgroundVideoUrlReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type GetBackgroundVideoUrlResp struct {
	BaseResp             *app.BaseResp          `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 []*BackgroundVideoInfo `protobuf:"bytes,2,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetBackgroundVideoUrlResp) Reset()         { *m = GetBackgroundVideoUrlResp{} }
func (m *GetBackgroundVideoUrlResp) String() string { return proto.CompactTextString(m) }
func (*GetBackgroundVideoUrlResp) ProtoMessage()    {}
func (*GetBackgroundVideoUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{107}
}
func (m *GetBackgroundVideoUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBackgroundVideoUrlResp.Unmarshal(m, b)
}
func (m *GetBackgroundVideoUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBackgroundVideoUrlResp.Marshal(b, m, deterministic)
}
func (dst *GetBackgroundVideoUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBackgroundVideoUrlResp.Merge(dst, src)
}
func (m *GetBackgroundVideoUrlResp) XXX_Size() int {
	return xxx_messageInfo_GetBackgroundVideoUrlResp.Size(m)
}
func (m *GetBackgroundVideoUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBackgroundVideoUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBackgroundVideoUrlResp proto.InternalMessageInfo

func (m *GetBackgroundVideoUrlResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBackgroundVideoUrlResp) GetInfo() []*BackgroundVideoInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BackgroundVideoInfo struct {
	VideoUrl             string   `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	VideoMd5             string   `protobuf:"bytes,2,opt,name=video_md5,json=videoMd5,proto3" json:"video_md5,omitempty"`
	ShareCoverPicUrl     string   `protobuf:"bytes,3,opt,name=share_cover_pic_url,json=shareCoverPicUrl,proto3" json:"share_cover_pic_url,omitempty"`
	OriginVideoUrl       string   `protobuf:"bytes,4,opt,name=origin_video_url,json=originVideoUrl,proto3" json:"origin_video_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BackgroundVideoInfo) Reset()         { *m = BackgroundVideoInfo{} }
func (m *BackgroundVideoInfo) String() string { return proto.CompactTextString(m) }
func (*BackgroundVideoInfo) ProtoMessage()    {}
func (*BackgroundVideoInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{108}
}
func (m *BackgroundVideoInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BackgroundVideoInfo.Unmarshal(m, b)
}
func (m *BackgroundVideoInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BackgroundVideoInfo.Marshal(b, m, deterministic)
}
func (dst *BackgroundVideoInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackgroundVideoInfo.Merge(dst, src)
}
func (m *BackgroundVideoInfo) XXX_Size() int {
	return xxx_messageInfo_BackgroundVideoInfo.Size(m)
}
func (m *BackgroundVideoInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BackgroundVideoInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BackgroundVideoInfo proto.InternalMessageInfo

func (m *BackgroundVideoInfo) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *BackgroundVideoInfo) GetVideoMd5() string {
	if m != nil {
		return m.VideoMd5
	}
	return ""
}

func (m *BackgroundVideoInfo) GetShareCoverPicUrl() string {
	if m != nil {
		return m.ShareCoverPicUrl
	}
	return ""
}

func (m *BackgroundVideoInfo) GetOriginVideoUrl() string {
	if m != nil {
		return m.OriginVideoUrl
	}
	return ""
}

// 开启battle
type SetBattleStartRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DurationMin          uint32       `protobuf:"varint,3,opt,name=duration_min,json=durationMin,proto3" json:"duration_min,omitempty"`
	Type                 uint32       `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	UidList              []uint32     `protobuf:"varint,5,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	VoteCnt              uint32       `protobuf:"varint,6,opt,name=vote_cnt,json=voteCnt,proto3" json:"vote_cnt,omitempty"`
	PkName               string       `protobuf:"bytes,7,opt,name=pk_name,json=pkName,proto3" json:"pk_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetBattleStartRequest) Reset()         { *m = SetBattleStartRequest{} }
func (m *SetBattleStartRequest) String() string { return proto.CompactTextString(m) }
func (*SetBattleStartRequest) ProtoMessage()    {}
func (*SetBattleStartRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{109}
}
func (m *SetBattleStartRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBattleStartRequest.Unmarshal(m, b)
}
func (m *SetBattleStartRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBattleStartRequest.Marshal(b, m, deterministic)
}
func (dst *SetBattleStartRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBattleStartRequest.Merge(dst, src)
}
func (m *SetBattleStartRequest) XXX_Size() int {
	return xxx_messageInfo_SetBattleStartRequest.Size(m)
}
func (m *SetBattleStartRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBattleStartRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetBattleStartRequest proto.InternalMessageInfo

func (m *SetBattleStartRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetBattleStartRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetBattleStartRequest) GetDurationMin() uint32 {
	if m != nil {
		return m.DurationMin
	}
	return 0
}

func (m *SetBattleStartRequest) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SetBattleStartRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *SetBattleStartRequest) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

func (m *SetBattleStartRequest) GetPkName() string {
	if m != nil {
		return m.PkName
	}
	return ""
}

type SetBattleStartResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetBattleStartResponse) Reset()         { *m = SetBattleStartResponse{} }
func (m *SetBattleStartResponse) String() string { return proto.CompactTextString(m) }
func (*SetBattleStartResponse) ProtoMessage()    {}
func (*SetBattleStartResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{110}
}
func (m *SetBattleStartResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBattleStartResponse.Unmarshal(m, b)
}
func (m *SetBattleStartResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBattleStartResponse.Marshal(b, m, deterministic)
}
func (dst *SetBattleStartResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBattleStartResponse.Merge(dst, src)
}
func (m *SetBattleStartResponse) XXX_Size() int {
	return xxx_messageInfo_SetBattleStartResponse.Size(m)
}
func (m *SetBattleStartResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBattleStartResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetBattleStartResponse proto.InternalMessageInfo

func (m *SetBattleStartResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 关闭battle
type CancelBattleStartRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CancelBattleStartRequest) Reset()         { *m = CancelBattleStartRequest{} }
func (m *CancelBattleStartRequest) String() string { return proto.CompactTextString(m) }
func (*CancelBattleStartRequest) ProtoMessage()    {}
func (*CancelBattleStartRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{111}
}
func (m *CancelBattleStartRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelBattleStartRequest.Unmarshal(m, b)
}
func (m *CancelBattleStartRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelBattleStartRequest.Marshal(b, m, deterministic)
}
func (dst *CancelBattleStartRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelBattleStartRequest.Merge(dst, src)
}
func (m *CancelBattleStartRequest) XXX_Size() int {
	return xxx_messageInfo_CancelBattleStartRequest.Size(m)
}
func (m *CancelBattleStartRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelBattleStartRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelBattleStartRequest proto.InternalMessageInfo

func (m *CancelBattleStartRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelBattleStartRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CancelBattleStartResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CancelBattleStartResponse) Reset()         { *m = CancelBattleStartResponse{} }
func (m *CancelBattleStartResponse) String() string { return proto.CompactTextString(m) }
func (*CancelBattleStartResponse) ProtoMessage()    {}
func (*CancelBattleStartResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{112}
}
func (m *CancelBattleStartResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelBattleStartResponse.Unmarshal(m, b)
}
func (m *CancelBattleStartResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelBattleStartResponse.Marshal(b, m, deterministic)
}
func (dst *CancelBattleStartResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelBattleStartResponse.Merge(dst, src)
}
func (m *CancelBattleStartResponse) XXX_Size() int {
	return xxx_messageInfo_CancelBattleStartResponse.Size(m)
}
func (m *CancelBattleStartResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelBattleStartResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelBattleStartResponse proto.InternalMessageInfo

func (m *CancelBattleStartResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取battle
type GetBattleStartRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBattleStartRequest) Reset()         { *m = GetBattleStartRequest{} }
func (m *GetBattleStartRequest) String() string { return proto.CompactTextString(m) }
func (*GetBattleStartRequest) ProtoMessage()    {}
func (*GetBattleStartRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{113}
}
func (m *GetBattleStartRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBattleStartRequest.Unmarshal(m, b)
}
func (m *GetBattleStartRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBattleStartRequest.Marshal(b, m, deterministic)
}
func (dst *GetBattleStartRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBattleStartRequest.Merge(dst, src)
}
func (m *GetBattleStartRequest) XXX_Size() int {
	return xxx_messageInfo_GetBattleStartRequest.Size(m)
}
func (m *GetBattleStartRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBattleStartRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBattleStartRequest proto.InternalMessageInfo

func (m *GetBattleStartRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetBattleStartRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetBattleStartResponse struct {
	BaseResp             *app.BaseResp      `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *BattleStartNotify `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetBattleStartResponse) Reset()         { *m = GetBattleStartResponse{} }
func (m *GetBattleStartResponse) String() string { return proto.CompactTextString(m) }
func (*GetBattleStartResponse) ProtoMessage()    {}
func (*GetBattleStartResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{114}
}
func (m *GetBattleStartResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBattleStartResponse.Unmarshal(m, b)
}
func (m *GetBattleStartResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBattleStartResponse.Marshal(b, m, deterministic)
}
func (dst *GetBattleStartResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBattleStartResponse.Merge(dst, src)
}
func (m *GetBattleStartResponse) XXX_Size() int {
	return xxx_messageInfo_GetBattleStartResponse.Size(m)
}
func (m *GetBattleStartResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBattleStartResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBattleStartResponse proto.InternalMessageInfo

func (m *GetBattleStartResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBattleStartResponse) GetInfo() *BattleStartNotify {
	if m != nil {
		return m.Info
	}
	return nil
}

// 开启battle推送
type BattleStartNotify struct {
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	DurationMin          uint32   `protobuf:"varint,3,opt,name=duration_min,json=durationMin,proto3" json:"duration_min,omitempty"`
	ChannelVotePkType    uint32   `protobuf:"varint,4,opt,name=channel_vote_pk_type,json=channelVotePkType,proto3" json:"channel_vote_pk_type,omitempty"`
	UidList              []uint32 `protobuf:"varint,5,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	VoteCnt              uint32   `protobuf:"varint,6,opt,name=vote_cnt,json=voteCnt,proto3" json:"vote_cnt,omitempty"`
	PkName               string   `protobuf:"bytes,7,opt,name=pk_name,json=pkName,proto3" json:"pk_name,omitempty"`
	BattleStatus         uint32   `protobuf:"varint,8,opt,name=battle_status,json=battleStatus,proto3" json:"battle_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BattleStartNotify) Reset()         { *m = BattleStartNotify{} }
func (m *BattleStartNotify) String() string { return proto.CompactTextString(m) }
func (*BattleStartNotify) ProtoMessage()    {}
func (*BattleStartNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{115}
}
func (m *BattleStartNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BattleStartNotify.Unmarshal(m, b)
}
func (m *BattleStartNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BattleStartNotify.Marshal(b, m, deterministic)
}
func (dst *BattleStartNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BattleStartNotify.Merge(dst, src)
}
func (m *BattleStartNotify) XXX_Size() int {
	return xxx_messageInfo_BattleStartNotify.Size(m)
}
func (m *BattleStartNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_BattleStartNotify.DiscardUnknown(m)
}

var xxx_messageInfo_BattleStartNotify proto.InternalMessageInfo

func (m *BattleStartNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BattleStartNotify) GetDurationMin() uint32 {
	if m != nil {
		return m.DurationMin
	}
	return 0
}

func (m *BattleStartNotify) GetChannelVotePkType() uint32 {
	if m != nil {
		return m.ChannelVotePkType
	}
	return 0
}

func (m *BattleStartNotify) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BattleStartNotify) GetVoteCnt() uint32 {
	if m != nil {
		return m.VoteCnt
	}
	return 0
}

func (m *BattleStartNotify) GetPkName() string {
	if m != nil {
		return m.PkName
	}
	return ""
}

func (m *BattleStartNotify) GetBattleStatus() uint32 {
	if m != nil {
		return m.BattleStatus
	}
	return 0
}

// 麦位名称---------------------------
type SwitchMicNameFuncReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FuncSwitch           bool         `protobuf:"varint,3,opt,name=func_switch,json=funcSwitch,proto3" json:"func_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SwitchMicNameFuncReq) Reset()         { *m = SwitchMicNameFuncReq{} }
func (m *SwitchMicNameFuncReq) String() string { return proto.CompactTextString(m) }
func (*SwitchMicNameFuncReq) ProtoMessage()    {}
func (*SwitchMicNameFuncReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{116}
}
func (m *SwitchMicNameFuncReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchMicNameFuncReq.Unmarshal(m, b)
}
func (m *SwitchMicNameFuncReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchMicNameFuncReq.Marshal(b, m, deterministic)
}
func (dst *SwitchMicNameFuncReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchMicNameFuncReq.Merge(dst, src)
}
func (m *SwitchMicNameFuncReq) XXX_Size() int {
	return xxx_messageInfo_SwitchMicNameFuncReq.Size(m)
}
func (m *SwitchMicNameFuncReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchMicNameFuncReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchMicNameFuncReq proto.InternalMessageInfo

func (m *SwitchMicNameFuncReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SwitchMicNameFuncReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchMicNameFuncReq) GetFuncSwitch() bool {
	if m != nil {
		return m.FuncSwitch
	}
	return false
}

type SwitchMicNameFuncResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SwitchMicNameFuncResp) Reset()         { *m = SwitchMicNameFuncResp{} }
func (m *SwitchMicNameFuncResp) String() string { return proto.CompactTextString(m) }
func (*SwitchMicNameFuncResp) ProtoMessage()    {}
func (*SwitchMicNameFuncResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{117}
}
func (m *SwitchMicNameFuncResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchMicNameFuncResp.Unmarshal(m, b)
}
func (m *SwitchMicNameFuncResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchMicNameFuncResp.Marshal(b, m, deterministic)
}
func (dst *SwitchMicNameFuncResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchMicNameFuncResp.Merge(dst, src)
}
func (m *SwitchMicNameFuncResp) XXX_Size() int {
	return xxx_messageInfo_SwitchMicNameFuncResp.Size(m)
}
func (m *SwitchMicNameFuncResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchMicNameFuncResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchMicNameFuncResp proto.InternalMessageInfo

func (m *SwitchMicNameFuncResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetMicNameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMicNameReq) Reset()         { *m = GetMicNameReq{} }
func (m *GetMicNameReq) String() string { return proto.CompactTextString(m) }
func (*GetMicNameReq) ProtoMessage()    {}
func (*GetMicNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{118}
}
func (m *GetMicNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicNameReq.Unmarshal(m, b)
}
func (m *GetMicNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicNameReq.Marshal(b, m, deterministic)
}
func (dst *GetMicNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicNameReq.Merge(dst, src)
}
func (m *GetMicNameReq) XXX_Size() int {
	return xxx_messageInfo_GetMicNameReq.Size(m)
}
func (m *GetMicNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicNameReq proto.InternalMessageInfo

func (m *GetMicNameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMicNameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMicNameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MicInfo              *MicNameInfo  `protobuf:"bytes,2,opt,name=mic_info,json=micInfo,proto3" json:"mic_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetMicNameResp) Reset()         { *m = GetMicNameResp{} }
func (m *GetMicNameResp) String() string { return proto.CompactTextString(m) }
func (*GetMicNameResp) ProtoMessage()    {}
func (*GetMicNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{119}
}
func (m *GetMicNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMicNameResp.Unmarshal(m, b)
}
func (m *GetMicNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMicNameResp.Marshal(b, m, deterministic)
}
func (dst *GetMicNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMicNameResp.Merge(dst, src)
}
func (m *GetMicNameResp) XXX_Size() int {
	return xxx_messageInfo_GetMicNameResp.Size(m)
}
func (m *GetMicNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMicNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMicNameResp proto.InternalMessageInfo

func (m *GetMicNameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMicNameResp) GetMicInfo() *MicNameInfo {
	if m != nil {
		return m.MicInfo
	}
	return nil
}

type SetMicNameReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicInfo              *MicNameInfo `protobuf:"bytes,3,opt,name=mic_info,json=micInfo,proto3" json:"mic_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetMicNameReq) Reset()         { *m = SetMicNameReq{} }
func (m *SetMicNameReq) String() string { return proto.CompactTextString(m) }
func (*SetMicNameReq) ProtoMessage()    {}
func (*SetMicNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{120}
}
func (m *SetMicNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicNameReq.Unmarshal(m, b)
}
func (m *SetMicNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicNameReq.Marshal(b, m, deterministic)
}
func (dst *SetMicNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicNameReq.Merge(dst, src)
}
func (m *SetMicNameReq) XXX_Size() int {
	return xxx_messageInfo_SetMicNameReq.Size(m)
}
func (m *SetMicNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicNameReq proto.InternalMessageInfo

func (m *SetMicNameReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetMicNameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMicNameReq) GetMicInfo() *MicNameInfo {
	if m != nil {
		return m.MicInfo
	}
	return nil
}

type SetMicNameResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetMicNameResp) Reset()         { *m = SetMicNameResp{} }
func (m *SetMicNameResp) String() string { return proto.CompactTextString(m) }
func (*SetMicNameResp) ProtoMessage()    {}
func (*SetMicNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{121}
}
func (m *SetMicNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMicNameResp.Unmarshal(m, b)
}
func (m *SetMicNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMicNameResp.Marshal(b, m, deterministic)
}
func (dst *SetMicNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMicNameResp.Merge(dst, src)
}
func (m *SetMicNameResp) XXX_Size() int {
	return xxx_messageInfo_SetMicNameResp.Size(m)
}
func (m *SetMicNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMicNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMicNameResp proto.InternalMessageInfo

func (m *SetMicNameResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 麦位名称推送
type MicNameInfo struct {
	StatusAudit          bool            `protobuf:"varint,1,opt,name=status_audit,json=statusAudit,proto3" json:"status_audit,omitempty"`
	FuncSwitch           bool            `protobuf:"varint,2,opt,name=func_switch,json=funcSwitch,proto3" json:"func_switch,omitempty"`
	MicNameArray         []*MicNameArray `protobuf:"bytes,3,rep,name=mic_name_array,json=micNameArray,proto3" json:"mic_name_array,omitempty"`
	Uid                  uint32          `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	ActionMode           ActionMode      `protobuf:"varint,5,opt,name=action_mode,json=actionMode,proto3,enum=ga.rhythm.ActionMode" json:"action_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MicNameInfo) Reset()         { *m = MicNameInfo{} }
func (m *MicNameInfo) String() string { return proto.CompactTextString(m) }
func (*MicNameInfo) ProtoMessage()    {}
func (*MicNameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{122}
}
func (m *MicNameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicNameInfo.Unmarshal(m, b)
}
func (m *MicNameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicNameInfo.Marshal(b, m, deterministic)
}
func (dst *MicNameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicNameInfo.Merge(dst, src)
}
func (m *MicNameInfo) XXX_Size() int {
	return xxx_messageInfo_MicNameInfo.Size(m)
}
func (m *MicNameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MicNameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MicNameInfo proto.InternalMessageInfo

func (m *MicNameInfo) GetStatusAudit() bool {
	if m != nil {
		return m.StatusAudit
	}
	return false
}

func (m *MicNameInfo) GetFuncSwitch() bool {
	if m != nil {
		return m.FuncSwitch
	}
	return false
}

func (m *MicNameInfo) GetMicNameArray() []*MicNameArray {
	if m != nil {
		return m.MicNameArray
	}
	return nil
}

func (m *MicNameInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MicNameInfo) GetActionMode() ActionMode {
	if m != nil {
		return m.ActionMode
	}
	return ActionMode_UnknownActionMode
}

type MicNameArray struct {
	MicId                uint32   `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	MicName              string   `protobuf:"bytes,2,opt,name=mic_name,json=micName,proto3" json:"mic_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicNameArray) Reset()         { *m = MicNameArray{} }
func (m *MicNameArray) String() string { return proto.CompactTextString(m) }
func (*MicNameArray) ProtoMessage()    {}
func (*MicNameArray) Descriptor() ([]byte, []int) {
	return fileDescriptor_rhythm__2c496ab71e09c878, []int{123}
}
func (m *MicNameArray) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicNameArray.Unmarshal(m, b)
}
func (m *MicNameArray) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicNameArray.Marshal(b, m, deterministic)
}
func (dst *MicNameArray) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicNameArray.Merge(dst, src)
}
func (m *MicNameArray) XXX_Size() int {
	return xxx_messageInfo_MicNameArray.Size(m)
}
func (m *MicNameArray) XXX_DiscardUnknown() {
	xxx_messageInfo_MicNameArray.DiscardUnknown(m)
}

var xxx_messageInfo_MicNameArray proto.InternalMessageInfo

func (m *MicNameArray) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *MicNameArray) GetMicName() string {
	if m != nil {
		return m.MicName
	}
	return ""
}

func init() {
	proto.RegisterType((*GetRhythmSwitchReq)(nil), "ga.rhythm.GetRhythmSwitchReq")
	proto.RegisterType((*GetRhythmSwitchResp)(nil), "ga.rhythm.GetRhythmSwitchResp")
	proto.RegisterType((*SetRhythmSwitchReq)(nil), "ga.rhythm.SetRhythmSwitchReq")
	proto.RegisterType((*SetRhythmSwitchResp)(nil), "ga.rhythm.SetRhythmSwitchResp")
	proto.RegisterType((*GetBrandListReq)(nil), "ga.rhythm.GetBrandListReq")
	proto.RegisterType((*BrandListInfo)(nil), "ga.rhythm.BrandListInfo")
	proto.RegisterType((*BrandCategoryListInfo)(nil), "ga.rhythm.BrandCategoryListInfo")
	proto.RegisterType((*GetBrandListResp)(nil), "ga.rhythm.GetBrandListResp")
	proto.RegisterType((*GetBrandInfoReq)(nil), "ga.rhythm.GetBrandInfoReq")
	proto.RegisterType((*BrandMemberInfo)(nil), "ga.rhythm.BrandMemberInfo")
	proto.RegisterType((*BrandSimpleInfo)(nil), "ga.rhythm.BrandSimpleInfo")
	proto.RegisterType((*GetBrandInfoResp)(nil), "ga.rhythm.GetBrandInfoResp")
	proto.RegisterType((*UpdateBrandInfoReq)(nil), "ga.rhythm.UpdateBrandInfoReq")
	proto.RegisterType((*UpdateBrandInfoResp)(nil), "ga.rhythm.UpdateBrandInfoResp")
	proto.RegisterType((*UserBrandInfo)(nil), "ga.rhythm.UserBrandInfo")
	proto.RegisterType((*GetUserBrandInfoReq)(nil), "ga.rhythm.GetUserBrandInfoReq")
	proto.RegisterType((*GetUserBrandInfoResp)(nil), "ga.rhythm.GetUserBrandInfoResp")
	proto.RegisterType((*PhotoAlbumKeyURL)(nil), "ga.rhythm.PhotoAlbumKeyURL")
	proto.RegisterType((*UpdateCommonPhotoAlbumReq)(nil), "ga.rhythm.UpdateCommonPhotoAlbumReq")
	proto.RegisterType((*UpdateCommonPhotoAlbumResp)(nil), "ga.rhythm.UpdateCommonPhotoAlbumResp")
	proto.RegisterType((*GetMusicZoneTemplateReq)(nil), "ga.rhythm.GetMusicZoneTemplateReq")
	proto.RegisterType((*GetMusicZoneTemplateResp)(nil), "ga.rhythm.GetMusicZoneTemplateResp")
	proto.RegisterType((*AddCokeReq)(nil), "ga.rhythm.AddCokeReq")
	proto.RegisterType((*AddCokeResp)(nil), "ga.rhythm.AddCokeResp")
	proto.RegisterType((*SendCokeReq)(nil), "ga.rhythm.SendCokeReq")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ga.rhythm.SendCokeReq.MapUidGainCokeEntry")
	proto.RegisterType((*SendCokeResp)(nil), "ga.rhythm.SendCokeResp")
	proto.RegisterType((*SendCokeToUserNotify)(nil), "ga.rhythm.SendCokeToUserNotify")
	proto.RegisterType((*UserDefinedVotePkOptionInfo)(nil), "ga.rhythm.UserDefinedVotePkOptionInfo")
	proto.RegisterType((*UserDefinedVotePKStartReq)(nil), "ga.rhythm.UserDefinedVotePKStartReq")
	proto.RegisterType((*UserDefinedVotePKStartResp)(nil), "ga.rhythm.UserDefinedVotePKStartResp")
	proto.RegisterType((*UserDefinedCompetitor)(nil), "ga.rhythm.UserDefinedCompetitor")
	proto.RegisterType((*UserDefinedOptInfo)(nil), "ga.rhythm.UserDefinedOptInfo")
	proto.RegisterType((*UserDefinedVotePKInfo)(nil), "ga.rhythm.UserDefinedVotePKInfo")
	proto.RegisterType((*UserDefinedPKRankInfo)(nil), "ga.rhythm.UserDefinedPKRankInfo")
	proto.RegisterType((*GetUserDefinedVotePKReq)(nil), "ga.rhythm.GetUserDefinedVotePKReq")
	proto.RegisterType((*GetUserDefinedVotePKResp)(nil), "ga.rhythm.GetUserDefinedVotePKResp")
	proto.RegisterType((*UserDefinedVoteReq)(nil), "ga.rhythm.UserDefinedVoteReq")
	proto.RegisterType((*UserDefinedVoteResp)(nil), "ga.rhythm.UserDefinedVoteResp")
	proto.RegisterType((*UserDefinedVotePKCancelReq)(nil), "ga.rhythm.UserDefinedVotePKCancelReq")
	proto.RegisterType((*UserDefinedVotePKCancelResp)(nil), "ga.rhythm.UserDefinedVotePKCancelResp")
	proto.RegisterType((*UserDefinedVotePKNotify)(nil), "ga.rhythm.UserDefinedVotePKNotify")
	proto.RegisterType((*AddVotePkUserTicketNotify)(nil), "ga.rhythm.AddVotePkUserTicketNotify")
	proto.RegisterType((*VoiceWatermarkResultNotify)(nil), "ga.rhythm.VoiceWatermarkResultNotify")
	proto.RegisterType((*VoicePartialQualityNotify)(nil), "ga.rhythm.VoicePartialQualityNotify")
	proto.RegisterType((*RapperPostTopicInfo)(nil), "ga.rhythm.RapperPostTopicInfo")
	proto.RegisterType((*RapperVoiceNotify)(nil), "ga.rhythm.RapperVoiceNotify")
	proto.RegisterType((*BrandChannelBGTopicNotify)(nil), "ga.rhythm.BrandChannelBGTopicNotify")
	proto.RegisterType((*SetVoiceWatermarkReq)(nil), "ga.rhythm.SetVoiceWatermarkReq")
	proto.RegisterType((*SetVoiceWatermarkResp)(nil), "ga.rhythm.SetVoiceWatermarkResp")
	proto.RegisterType((*GetVoiceWatermarkReq)(nil), "ga.rhythm.GetVoiceWatermarkReq")
	proto.RegisterType((*GetVoiceWatermarkResp)(nil), "ga.rhythm.GetVoiceWatermarkResp")
	proto.RegisterType((*DelVoiceWatermarkReq)(nil), "ga.rhythm.DelVoiceWatermarkReq")
	proto.RegisterType((*DelVoiceWatermarkResp)(nil), "ga.rhythm.DelVoiceWatermarkResp")
	proto.RegisterType((*SendReadVoiceReq)(nil), "ga.rhythm.SendReadVoiceReq")
	proto.RegisterType((*SendReadVoiceResp)(nil), "ga.rhythm.SendReadVoiceResp")
	proto.RegisterType((*LevelConfig)(nil), "ga.rhythm.LevelConfig")
	proto.RegisterType((*UserLevel)(nil), "ga.rhythm.UserLevel")
	proto.RegisterType((*GetUserAIRapperLevelReq)(nil), "ga.rhythm.GetUserAIRapperLevelReq")
	proto.RegisterType((*GetUserAIRapperLevelResp)(nil), "ga.rhythm.GetUserAIRapperLevelResp")
	proto.RegisterType((*PostAIRapperPostReq)(nil), "ga.rhythm.PostAIRapperPostReq")
	proto.RegisterType((*PostAIRapperPostResp)(nil), "ga.rhythm.PostAIRapperPostResp")
	proto.RegisterType((*GetTabInfoReq)(nil), "ga.rhythm.GetTabInfoReq")
	proto.RegisterType((*TabAttribute)(nil), "ga.rhythm.TabAttribute")
	proto.RegisterType((*GetTabInfoResp)(nil), "ga.rhythm.GetTabInfoResp")
	proto.RegisterType((*GetLyricInfoReq)(nil), "ga.rhythm.GetLyricInfoReq")
	proto.RegisterType((*GetCyberWorldHomeReq)(nil), "ga.rhythm.GetCyberWorldHomeReq")
	proto.RegisterType((*CyberWorldModel)(nil), "ga.rhythm.CyberWorldModel")
	proto.RegisterType((*GetCyberWorldHomeResp)(nil), "ga.rhythm.GetCyberWorldHomeResp")
	proto.RegisterType((*CardBGVideo)(nil), "ga.rhythm.CardBGVideo")
	proto.RegisterType((*LyricInfo)(nil), "ga.rhythm.LyricInfo")
	proto.RegisterType((*GetLyricInfoResp)(nil), "ga.rhythm.GetLyricInfoResp")
	proto.RegisterType((*AggregationInfo)(nil), "ga.rhythm.AggregationInfo")
	proto.RegisterType((*AggregationInfoReq)(nil), "ga.rhythm.AggregationInfoReq")
	proto.RegisterType((*AggregationInfoResp)(nil), "ga.rhythm.AggregationInfoResp")
	proto.RegisterType((*GetQuestionnaireReq)(nil), "ga.rhythm.GetQuestionnaireReq")
	proto.RegisterType((*GetQuestionnaireResp)(nil), "ga.rhythm.GetQuestionnaireResp")
	proto.RegisterType((*GetH5UrlWithAICtxReq)(nil), "ga.rhythm.GetH5UrlWithAICtxReq")
	proto.RegisterType((*GetH5UrlWithAICtxResp)(nil), "ga.rhythm.GetH5UrlWithAICtxResp")
	proto.RegisterType((*GetMusicBlockFilterReq)(nil), "ga.rhythm.GetMusicBlockFilterReq")
	proto.RegisterType((*GetMusicBlockFilterResp)(nil), "ga.rhythm.GetMusicBlockFilterResp")
	proto.RegisterType((*GetMusicBlockFilterResp_FilterItem)(nil), "ga.rhythm.GetMusicBlockFilterResp.FilterItem")
	proto.RegisterType((*GetMusicBlockFilterResp_FilterSubItem)(nil), "ga.rhythm.GetMusicBlockFilterResp.FilterSubItem")
	proto.RegisterType((*GetBrandChannelBGTopicInfoReq)(nil), "ga.rhythm.GetBrandChannelBGTopicInfoReq")
	proto.RegisterType((*GetBrandChannelBGTopicInfoResp)(nil), "ga.rhythm.GetBrandChannelBGTopicInfoResp")
	proto.RegisterType((*UpgradeUserAIRapperLevelReq)(nil), "ga.rhythm.UpgradeUserAIRapperLevelReq")
	proto.RegisterType((*UpgradeUserAIRapperLevelResp)(nil), "ga.rhythm.UpgradeUserAIRapperLevelResp")
	proto.RegisterType((*RhythmRecommendRoomItem)(nil), "ga.rhythm.RhythmRecommendRoomItem")
	proto.RegisterType((*RhythmGetRecommendRoomReq)(nil), "ga.rhythm.RhythmGetRecommendRoomReq")
	proto.RegisterType((*RhythmGetRecommendRoomResp)(nil), "ga.rhythm.RhythmGetRecommendRoomResp")
	proto.RegisterType((*RhythmGetPostsReq)(nil), "ga.rhythm.RhythmGetPostsReq")
	proto.RegisterType((*RhythmGetPostsResp)(nil), "ga.rhythm.RhythmGetPostsResp")
	proto.RegisterType((*RhythmGetPostsItem)(nil), "ga.rhythm.RhythmGetPostsItem")
	proto.RegisterType((*ReportStayAddTicketReq)(nil), "ga.rhythm.ReportStayAddTicketReq")
	proto.RegisterType((*StayAddTicketInfo)(nil), "ga.rhythm.StayAddTicketInfo")
	proto.RegisterType((*ReportStayAddTicketResp)(nil), "ga.rhythm.ReportStayAddTicketResp")
	proto.RegisterType((*StayAddTicketNotify)(nil), "ga.rhythm.StayAddTicketNotify")
	proto.RegisterType((*SetMicSortReq)(nil), "ga.rhythm.SetMicSortReq")
	proto.RegisterType((*SetMicSortResp)(nil), "ga.rhythm.SetMicSortResp")
	proto.RegisterType((*MicUserInfo)(nil), "ga.rhythm.MicUserInfo")
	proto.RegisterType((*MicSortInfo)(nil), "ga.rhythm.MicSortInfo")
	proto.RegisterType((*GetMicSortReq)(nil), "ga.rhythm.GetMicSortReq")
	proto.RegisterType((*GetMicSortResp)(nil), "ga.rhythm.GetMicSortResp")
	proto.RegisterType((*SwitchMicSortReq)(nil), "ga.rhythm.SwitchMicSortReq")
	proto.RegisterType((*SwitchMicSortResp)(nil), "ga.rhythm.SwitchMicSortResp")
	proto.RegisterType((*GetForceTopicReq)(nil), "ga.rhythm.GetForceTopicReq")
	proto.RegisterType((*PostContentProfileInfo)(nil), "ga.rhythm.PostContentProfileInfo")
	proto.RegisterType((*ForceTopicInfo)(nil), "ga.rhythm.ForceTopicInfo")
	proto.RegisterType((*GetForceTopicResp)(nil), "ga.rhythm.GetForceTopicResp")
	proto.RegisterType((*GetBackgroundVideoUrlReq)(nil), "ga.rhythm.GetBackgroundVideoUrlReq")
	proto.RegisterType((*GetBackgroundVideoUrlResp)(nil), "ga.rhythm.GetBackgroundVideoUrlResp")
	proto.RegisterType((*BackgroundVideoInfo)(nil), "ga.rhythm.BackgroundVideoInfo")
	proto.RegisterType((*SetBattleStartRequest)(nil), "ga.rhythm.SetBattleStartRequest")
	proto.RegisterType((*SetBattleStartResponse)(nil), "ga.rhythm.SetBattleStartResponse")
	proto.RegisterType((*CancelBattleStartRequest)(nil), "ga.rhythm.CancelBattleStartRequest")
	proto.RegisterType((*CancelBattleStartResponse)(nil), "ga.rhythm.CancelBattleStartResponse")
	proto.RegisterType((*GetBattleStartRequest)(nil), "ga.rhythm.GetBattleStartRequest")
	proto.RegisterType((*GetBattleStartResponse)(nil), "ga.rhythm.GetBattleStartResponse")
	proto.RegisterType((*BattleStartNotify)(nil), "ga.rhythm.BattleStartNotify")
	proto.RegisterType((*SwitchMicNameFuncReq)(nil), "ga.rhythm.SwitchMicNameFuncReq")
	proto.RegisterType((*SwitchMicNameFuncResp)(nil), "ga.rhythm.SwitchMicNameFuncResp")
	proto.RegisterType((*GetMicNameReq)(nil), "ga.rhythm.GetMicNameReq")
	proto.RegisterType((*GetMicNameResp)(nil), "ga.rhythm.GetMicNameResp")
	proto.RegisterType((*SetMicNameReq)(nil), "ga.rhythm.SetMicNameReq")
	proto.RegisterType((*SetMicNameResp)(nil), "ga.rhythm.SetMicNameResp")
	proto.RegisterType((*MicNameInfo)(nil), "ga.rhythm.MicNameInfo")
	proto.RegisterType((*MicNameArray)(nil), "ga.rhythm.MicNameArray")
	proto.RegisterEnum("ga.rhythm.RhythmSwitchType", RhythmSwitchType_name, RhythmSwitchType_value)
	proto.RegisterEnum("ga.rhythm.RhythmSwitchStatus", RhythmSwitchStatus_name, RhythmSwitchStatus_value)
	proto.RegisterEnum("ga.rhythm.BrandMemberRole", BrandMemberRole_name, BrandMemberRole_value)
	proto.RegisterEnum("ga.rhythm.PhotoAlbumType", PhotoAlbumType_name, PhotoAlbumType_value)
	proto.RegisterEnum("ga.rhythm.ColorConfiguration", ColorConfiguration_name, ColorConfiguration_value)
	proto.RegisterEnum("ga.rhythm.UserDefinedVotePKType", UserDefinedVotePKType_name, UserDefinedVotePKType_value)
	proto.RegisterEnum("ga.rhythm.AIType", AIType_name, AIType_value)
	proto.RegisterEnum("ga.rhythm.AggregationChoose", AggregationChoose_name, AggregationChoose_value)
	proto.RegisterEnum("ga.rhythm.StayAddTicketStatus", StayAddTicketStatus_name, StayAddTicketStatus_value)
	proto.RegisterEnum("ga.rhythm.BattleStatusEnum", BattleStatusEnum_name, BattleStatusEnum_value)
	proto.RegisterEnum("ga.rhythm.ActionMode", ActionMode_name, ActionMode_value)
	proto.RegisterEnum("ga.rhythm.UserDefinedVotePKNotify_UDVotePKStatus", UserDefinedVotePKNotify_UDVotePKStatus_name, UserDefinedVotePKNotify_UDVotePKStatus_value)
	proto.RegisterEnum("ga.rhythm.GetMusicBlockFilterResp_FilterItemType", GetMusicBlockFilterResp_FilterItemType_name, GetMusicBlockFilterResp_FilterItemType_value)
}

func init() { proto.RegisterFile("rhythm_.proto", fileDescriptor_rhythm__2c496ab71e09c878) }

var fileDescriptor_rhythm__2c496ab71e09c878 = []byte{
	// 5823 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x7c, 0xdf, 0x6f, 0xe4, 0xc8,
	0x71, 0xf0, 0x71, 0x34, 0x23, 0xcd, 0xd4, 0xcc, 0x48, 0x23, 0x4a, 0xda, 0x95, 0xb4, 0xb7, 0xbe,
	0x3d, 0x9e, 0x7f, 0xac, 0xf5, 0xdd, 0xed, 0xdd, 0xb7, 0xe7, 0x3d, 0xf8, 0x6c, 0x78, 0xbf, 0x93,
	0xb4, 0xda, 0x3d, 0x61, 0x7f, 0x48, 0xa6, 0xa4, 0xbd, 0x0f, 0x06, 0x6c, 0xa6, 0x87, 0x6c, 0x8d,
	0x18, 0x0d, 0xd9, 0x3c, 0xb2, 0x47, 0x2b, 0x5d, 0x10, 0x27, 0x31, 0x62, 0x20, 0x0e, 0x82, 0x04,
	0x01, 0xfc, 0x10, 0x20, 0x09, 0x10, 0x04, 0x48, 0x60, 0xe4, 0x29, 0x70, 0x80, 0x00, 0x7e, 0x0d,
	0x60, 0xc0, 0x6f, 0x79, 0xc8, 0x4b, 0x92, 0xd7, 0xe4, 0x3f, 0x48, 0x9e, 0x82, 0x04, 0x08, 0xba,
	0xba, 0x49, 0x36, 0x39, 0x23, 0xed, 0xf2, 0x24, 0xfb, 0x49, 0xd3, 0xd5, 0xc5, 0xea, 0xea, 0xea,
	0xea, 0xaa, 0xea, 0xea, 0x6a, 0x41, 0x37, 0x3e, 0x3a, 0xe3, 0x47, 0x81, 0x73, 0x27, 0x8a, 0x19,
	0x67, 0x66, 0x6b, 0x40, 0xee, 0x48, 0xc8, 0x6a, 0x77, 0x40, 0x9c, 0x3e, 0x49, 0xa8, 0xec, 0xb1,
	0xfe, 0xd0, 0x00, 0xf3, 0x11, 0xe5, 0x36, 0x76, 0xee, 0xbd, 0xf0, 0xb9, 0x7b, 0x64, 0xd3, 0x4f,
	0xcd, 0x2f, 0x43, 0x53, 0x20, 0x39, 0x31, 0xfd, 0x74, 0xd9, 0xb8, 0x65, 0xdc, 0x6e, 0xdf, 0x6d,
	0xdf, 0x19, 0x90, 0x3b, 0x1b, 0x24, 0xa1, 0x36, 0xfd, 0xd4, 0x9e, 0xe9, 0xcb, 0x1f, 0x66, 0x0f,
	0xa6, 0x46, 0xbe, 0xb7, 0x5c, 0xbb, 0x65, 0xdc, 0xee, 0xda, 0xe2, 0xa7, 0xf9, 0x06, 0xb4, 0x13,
	0x24, 0xe3, 0xf0, 0xb3, 0x88, 0x2e, 0x4f, 0x61, 0x0f, 0x48, 0xd0, 0xfe, 0x59, 0x44, 0xcd, 0x9b,
	0x00, 0xee, 0x11, 0x09, 0x43, 0x3a, 0x74, 0x7c, 0x6f, 0xb9, 0x8e, 0xfd, 0x2d, 0x05, 0xd9, 0xf6,
	0xac, 0xdf, 0x35, 0x60, 0x61, 0x8c, 0xa1, 0x24, 0x32, 0xbf, 0x0a, 0x2d, 0xc5, 0x51, 0x12, 0x29,
	0x96, 0x3a, 0x39, 0x4b, 0x49, 0x64, 0x37, 0xfb, 0xea, 0x97, 0xf9, 0x16, 0x74, 0x15, 0x0b, 0x09,
	0x27, 0x7c, 0x94, 0x28, 0xf6, 0x3a, 0x12, 0xb8, 0x87, 0x30, 0xc1, 0x86, 0x9f, 0x38, 0x27, 0x7e,
	0xe2, 0xf7, 0x87, 0x92, 0xcd, 0xa6, 0xdd, 0xf2, 0x93, 0xe7, 0x12, 0x60, 0xfd, 0xd8, 0x00, 0x73,
	0xef, 0x57, 0x2a, 0x97, 0x31, 0xae, 0xeb, 0xe3, 0x5c, 0x5b, 0x1f, 0xc1, 0xc2, 0xde, 0xa5, 0x84,
	0x63, 0x7d, 0x08, 0x73, 0x8f, 0x28, 0xdf, 0x88, 0x49, 0xe8, 0x3d, 0xf1, 0x13, 0x5e, 0x61, 0x52,
	0xd6, 0x10, 0xba, 0xd9, 0x77, 0xdb, 0xe1, 0x21, 0x33, 0x57, 0xa0, 0xd9, 0x17, 0x00, 0xb1, 0x90,
	0xe2, 0xc3, 0x96, 0x3d, 0x83, 0xed, 0x6d, 0x4f, 0x88, 0x57, 0x76, 0x85, 0x24, 0xa0, 0x28, 0x87,
	0x96, 0xdd, 0x42, 0xc8, 0x33, 0x12, 0x50, 0xf3, 0x4d, 0xe8, 0x1c, 0xb2, 0xe1, 0x90, 0xbd, 0xa0,
	0xb1, 0x13, 0x8e, 0x02, 0x35, 0xd7, 0x76, 0x0a, 0x7b, 0x36, 0x0a, 0x2c, 0x17, 0x96, 0x70, 0xb4,
	0x4d, 0xc2, 0xe9, 0x80, 0xc5, 0x67, 0xd9, 0xa8, 0x6f, 0x43, 0xdd, 0x0f, 0x0f, 0xd9, 0xb2, 0x71,
	0x6b, 0xea, 0x76, 0xfb, 0xee, 0xf2, 0x9d, 0x4c, 0xb7, 0xef, 0x14, 0xb8, 0xb3, 0x11, 0xcb, 0x7c,
	0x1d, 0x84, 0x72, 0xc5, 0xc4, 0xe5, 0x34, 0x4e, 0xf9, 0xc8, 0x00, 0xd6, 0xcf, 0x6b, 0xd0, 0x2b,
	0x8a, 0xa3, 0x9a, 0xaa, 0xdd, 0x87, 0x59, 0xce, 0x22, 0x47, 0x4e, 0x75, 0xe8, 0x27, 0x7c, 0xb9,
	0xf6, 0x12, 0xae, 0x3a, 0x9c, 0x45, 0x19, 0xc4, 0xdc, 0x85, 0x05, 0x57, 0xcd, 0x4f, 0x27, 0x32,
	0x85, 0x44, 0x6e, 0x95, 0x89, 0x94, 0x45, 0x61, 0xcf, 0xa7, 0x1f, 0xe7, 0x14, 0x85, 0xe6, 0xc5,
	0x43, 0x14, 0x68, 0xcb, 0x16, 0x3f, 0x85, 0x04, 0xb6, 0x43, 0x1e, 0xb3, 0x7d, 0x7a, 0xca, 0x97,
	0x1b, 0x52, 0x02, 0x19, 0xc0, 0xfc, 0x2a, 0xcc, 0xb3, 0x11, 0x77, 0x92, 0x23, 0x12, 0x53, 0x27,
	0xf2, 0x5d, 0x47, 0x7c, 0x3d, 0x8d, 0x58, 0xb3, 0x6c, 0xc4, 0xf7, 0x04, 0x7c, 0xd7, 0x77, 0x0f,
	0xe2, 0xa1, 0x58, 0xee, 0x40, 0xb1, 0xb9, 0x3c, 0x23, 0x97, 0x3b, 0x90, 0x23, 0x5b, 0xfb, 0xb9,
	0x56, 0x21, 0x63, 0x15, 0xb6, 0x8a, 0xae, 0x44, 0xb5, 0x82, 0x12, 0x59, 0x3f, 0x33, 0x60, 0x0e,
	0x69, 0x3e, 0xa5, 0x41, 0x9f, 0xc6, 0xb8, 0xfa, 0x6a, 0x67, 0x19, 0xf9, 0xce, 0x5a, 0x86, 0x19,
	0xe2, 0xba, 0x6c, 0x14, 0xf2, 0xf4, 0x7b, 0xd5, 0x34, 0x6f, 0x40, 0x2b, 0xf4, 0xdd, 0x63, 0xa9,
	0x83, 0x53, 0xd8, 0xd7, 0x14, 0x80, 0x57, 0x54, 0x41, 0x31, 0x56, 0x42, 0x4f, 0x51, 0x66, 0x5d,
	0x5b, 0xfc, 0x34, 0x17, 0xa1, 0xe1, 0x0b, 0xd1, 0x29, 0x09, 0xc9, 0x86, 0x69, 0x42, 0x3d, 0x66,
	0x43, 0x8a, 0x42, 0xe9, 0xda, 0xf8, 0xdb, 0xfa, 0x9f, 0x94, 0xf7, 0x3d, 0x3f, 0x88, 0x86, 0xf4,
	0x92, 0xfb, 0x25, 0x1b, 0x77, 0x4a, 0x1f, 0xf7, 0x15, 0xa6, 0xf0, 0x4d, 0x68, 0x07, 0x28, 0x3c,
	0xa9, 0x58, 0x0d, 0x54, 0xac, 0xd5, 0xb2, 0x62, 0xe5, 0xf2, 0xb5, 0x41, 0xa2, 0xa3, 0x2e, 0x7d,
	0x03, 0x20, 0x3a, 0x62, 0x9c, 0xc9, 0x6f, 0xa7, 0xf1, 0xdb, 0x1b, 0xda, 0xb7, 0xbb, 0xa2, 0x73,
	0x7d, 0xd8, 0x1f, 0x05, 0x8f, 0xe9, 0xd9, 0x81, 0xfd, 0xc4, 0x6e, 0x21, 0xba, 0xf8, 0xd6, 0xfa,
	0x3b, 0x6d, 0x67, 0x49, 0x95, 0xa8, 0xb6, 0xb3, 0x3e, 0x4c, 0x05, 0x82, 0x7b, 0xbd, 0x86, 0xb8,
	0x63, 0x7c, 0xe7, 0xb2, 0x55, 0xc2, 0x42, 0x31, 0xa7, 0xcb, 0x31, 0x95, 0x2f, 0x87, 0xf9, 0x15,
	0xe8, 0xf9, 0x61, 0x49, 0xcb, 0xe5, 0x1e, 0xe9, 0xfa, 0xa1, 0xae, 0xe4, 0x37, 0xa0, 0x15, 0xb3,
	0x3e, 0xe3, 0x88, 0x21, 0xc4, 0xd5, 0xb2, 0x9b, 0x08, 0x50, 0x3b, 0x80, 0x33, 0xf1, 0xb9, 0xef,
	0x29, 0x0d, 0x98, 0xc1, 0xb6, 0x5c, 0xc0, 0x13, 0x9f, 0xbe, 0x70, 0xa4, 0x22, 0x4a, 0x4d, 0x68,
	0x09, 0xc8, 0x26, 0xaa, 0xe2, 0x97, 0x60, 0x36, 0x9d, 0x0e, 0xa7, 0x83, 0x98, 0x0c, 0x97, 0x9b,
	0xb7, 0x8c, 0xdb, 0x0d, 0xbb, 0xab, 0xd8, 0x96, 0x40, 0xeb, 0xb7, 0xc0, 0x3c, 0x88, 0x3c, 0xc2,
	0xe9, 0x15, 0x6f, 0xa5, 0x74, 0xdb, 0x4c, 0xe5, 0xdb, 0x26, 0x53, 0xa9, 0xba, 0xa6, 0x52, 0xc2,
	0xc1, 0x8c, 0x31, 0x50, 0xcd, 0xc1, 0xfc, 0x81, 0x01, 0xdd, 0x83, 0x84, 0xc6, 0x19, 0x01, 0x73,
	0x16, 0x6a, 0x99, 0xc2, 0xd7, 0x7c, 0x4f, 0xac, 0x8f, 0xa6, 0xe5, 0xf8, 0x5b, 0xc0, 0x32, 0xbf,
	0xd8, 0xb2, 0xf1, 0xb7, 0x80, 0x11, 0xce, 0x63, 0xc5, 0x20, 0xfe, 0x16, 0xcb, 0x23, 0xfe, 0x4a,
	0xad, 0x90, 0xc6, 0xac, 0x29, 0x00, 0x85, 0x85, 0x97, 0x4b, 0x23, 0xf7, 0xe1, 0x0e, 0x86, 0x13,
	0x05, 0x86, 0x2e, 0xe5, 0xc8, 0x2d, 0x06, 0x8b, 0xe3, 0x04, 0xab, 0xe9, 0x76, 0xea, 0xc1, 0xa4,
	0x56, 0xeb, 0xbe, 0xa2, 0x48, 0x16, 0xb1, 0xac, 0x0f, 0xa0, 0x57, 0xde, 0x68, 0x82, 0xad, 0x63,
	0x7a, 0xa6, 0x64, 0x2a, 0x7e, 0xa6, 0x76, 0xbf, 0x96, 0xd9, 0x7d, 0x6b, 0x04, 0x2b, 0x72, 0x29,
	0x37, 0x59, 0x10, 0xb0, 0x30, 0xa7, 0x51, 0x65, 0xfe, 0x72, 0xed, 0x6a, 0xd9, 0xda, 0xdd, 0x82,
	0x8e, 0x1f, 0x0c, 0x9c, 0x63, 0x7a, 0x26, 0x8d, 0x42, 0x1d, 0x77, 0x08, 0xf8, 0xc1, 0xe0, 0x31,
	0x45, 0xbf, 0x64, 0x3d, 0x82, 0xd5, 0xf3, 0x86, 0xad, 0xa6, 0x48, 0xdf, 0x86, 0xeb, 0x8f, 0x28,
	0x7f, 0x3a, 0x4a, 0x7c, 0xf7, 0x3b, 0x2c, 0xa4, 0xfb, 0x34, 0x88, 0x86, 0x84, 0xd3, 0x4b, 0x70,
	0x6f, 0xfd, 0x7c, 0x0a, 0x96, 0x27, 0xd3, 0xac, 0xb6, 0x80, 0x37, 0xa0, 0xf5, 0x19, 0x0b, 0xa9,
	0x6e, 0xac, 0x9b, 0x02, 0x80, 0xb6, 0xfa, 0x86, 0xa0, 0x13, 0x86, 0x34, 0x76, 0xb2, 0x0d, 0xd7,
	0x94, 0x80, 0x6d, 0xcf, 0xfc, 0x02, 0xb4, 0x39, 0xe9, 0x0b, 0xc6, 0x9d, 0x24, 0x53, 0xed, 0x16,
	0x27, 0x7d, 0x9b, 0x7e, 0xba, 0xc7, 0x63, 0xb1, 0x85, 0x63, 0xc6, 0x02, 0x65, 0x7d, 0x70, 0x0b,
	0x8b, 0xb6, 0x30, 0x3e, 0x16, 0x74, 0xb1, 0x4b, 0x7c, 0x8f, 0x03, 0x4b, 0x35, 0x6f, 0x0b, 0xe0,
	0x3e, 0xe9, 0xe3, 0xd8, 0x5f, 0x84, 0xd9, 0x43, 0x3f, 0xf4, 0x1c, 0x44, 0x44, 0x24, 0xe9, 0xa8,
	0x3b, 0x02, 0x6a, 0x33, 0x16, 0x20, 0xd6, 0x2d, 0xe8, 0xe4, 0x58, 0xbe, 0x87, 0xa6, 0xa8, 0x65,
	0x43, 0x8a, 0xb3, 0xed, 0x89, 0x39, 0x24, 0x9c, 0xc4, 0xce, 0xe1, 0x90, 0x0c, 0x96, 0x5b, 0x18,
	0x1c, 0x37, 0x05, 0xe0, 0xe1, 0x90, 0x0c, 0xcc, 0xf7, 0x60, 0xb1, 0x4f, 0xdc, 0xe3, 0x41, 0xcc,
	0x46, 0xc2, 0xd6, 0x04, 0x64, 0x40, 0x91, 0x5f, 0x40, 0x32, 0x66, 0xde, 0xb7, 0x2d, 0xba, 0x04,
	0xeb, 0xcf, 0x60, 0xc1, 0x65, 0x43, 0x16, 0x3b, 0x2e, 0x0b, 0x0f, 0xfd, 0xc1, 0x28, 0x26, 0xdc,
	0x67, 0xe1, 0x72, 0xfb, 0x96, 0x71, 0x7b, 0xf6, 0xee, 0x4d, 0x4d, 0xff, 0x37, 0x05, 0xd6, 0xa6,
	0x8e, 0x64, 0x9b, 0xee, 0x18, 0xcc, 0xfa, 0xa1, 0x01, 0xb0, 0xee, 0x79, 0x9b, 0xec, 0xb8, 0x92,
	0x3a, 0x14, 0x8f, 0x1e, 0xb5, 0xd2, 0xd1, 0xc3, 0x5c, 0x82, 0x69, 0xe2, 0xf2, 0x7c, 0xd5, 0x1a,
	0xc4, 0xe5, 0xdb, 0x9e, 0x58, 0x12, 0x97, 0x1d, 0x53, 0xcd, 0xc3, 0xce, 0x88, 0xb6, 0x88, 0x51,
	0x0f, 0xa0, 0x9d, 0xb1, 0x51, 0x59, 0x83, 0x86, 0xf4, 0x90, 0x3b, 0x82, 0x92, 0xe2, 0xa4, 0x29,
	0x00, 0x82, 0x96, 0xf5, 0xc3, 0x1a, 0xb4, 0xf7, 0x68, 0xf8, 0x2b, 0x9a, 0xdf, 0x01, 0xcc, 0x07,
	0x24, 0x72, 0x46, 0xbe, 0xe7, 0x0c, 0x88, 0x1f, 0x4a, 0x96, 0xea, 0xe8, 0xec, 0xd7, 0xb4, 0xa5,
	0xd1, 0x18, 0xba, 0xf3, 0x94, 0x44, 0x07, 0xbe, 0xf7, 0x88, 0xf8, 0xa1, 0x80, 0x6c, 0x85, 0x3c,
	0x3e, 0xb3, 0x67, 0x83, 0x02, 0x70, 0x75, 0x1d, 0x16, 0x26, 0xa0, 0xe9, 0x96, 0xab, 0x2b, 0x2d,
	0xd7, 0x22, 0x34, 0x4e, 0xc8, 0x70, 0x94, 0x8a, 0x41, 0x36, 0xbe, 0x51, 0xfb, 0xba, 0x61, 0x3d,
	0x87, 0x4e, 0x3e, 0xea, 0x15, 0xca, 0xf7, 0xf7, 0x6b, 0xb0, 0x98, 0x12, 0xde, 0x67, 0xc2, 0xe6,
	0x3e, 0x63, 0xdc, 0x3f, 0x3c, 0x2b, 0x09, 0xd0, 0x28, 0x0b, 0x70, 0x05, 0x9a, 0x87, 0xb1, 0xd8,
	0x9c, 0x99, 0x74, 0x67, 0x44, 0xfb, 0xc0, 0xf7, 0xc4, 0xe9, 0x0d, 0xbb, 0x44, 0x78, 0xa9, 0x85,
	0x9b, 0x1d, 0x01, 0x7c, 0xa6, 0x60, 0x18, 0xaf, 0x09, 0xa4, 0x34, 0x5c, 0x95, 0xbb, 0xbf, 0x2d,
	0x60, 0xeb, 0x2a, 0x64, 0x5d, 0x82, 0x69, 0xce, 0x70, 0x00, 0x19, 0x75, 0x36, 0x38, 0x3b, 0x90,
	0xa7, 0x47, 0xce, 0x72, 0xe2, 0x72, 0xe7, 0x03, 0x67, 0x19, 0xe9, 0x9b, 0x00, 0x9c, 0x65, 0x84,
	0x67, 0x94, 0x59, 0x61, 0x29, 0xd9, 0x54, 0x87, 0xdd, 0x90, 0xe3, 0x6e, 0x57, 0x3a, 0xbc, 0x19,
	0x72, 0xeb, 0x3e, 0xdc, 0x10, 0x12, 0x78, 0x40, 0x0f, 0xfd, 0x90, 0x7a, 0xcf, 0x19, 0xa7, 0xbb,
	0xc7, 0x3b, 0x91, 0xd8, 0x66, 0xe8, 0x53, 0xdf, 0x80, 0x36, 0xc3, 0x96, 0x34, 0x27, 0xd2, 0xe3,
	0x80, 0x04, 0x09, 0x63, 0x62, 0xfd, 0x69, 0x0d, 0x56, 0xca, 0x04, 0x1e, 0xef, 0x71, 0x12, 0xf3,
	0x2b, 0x54, 0xdd, 0x37, 0xa1, 0xe3, 0xa9, 0xcd, 0xef, 0x04, 0x7e, 0xa8, 0x14, 0xb8, 0x9d, 0xc2,
	0x9e, 0xfa, 0xa1, 0x79, 0x1d, 0x66, 0xa2, 0x63, 0x79, 0xb8, 0x96, 0xbb, 0x74, 0x3a, 0x3a, 0xc6,
	0x83, 0xf5, 0xa3, 0x6c, 0x06, 0x5a, 0x08, 0xfc, 0xe5, 0x92, 0xd3, 0x3d, 0x67, 0xfa, 0xe9, 0x4c,
	0x31, 0x1c, 0x5e, 0x81, 0xe6, 0x09, 0xe3, 0x52, 0x88, 0xd3, 0x52, 0x88, 0xa2, 0xbd, 0x19, 0x72,
	0x35, 0xb8, 0x66, 0x70, 0xa7, 0x23, 0x3c, 0x65, 0x58, 0xff, 0x68, 0xc0, 0xea, 0x79, 0xd2, 0xa9,
	0xa6, 0xd1, 0x2f, 0x91, 0xd0, 0x57, 0x60, 0x4e, 0x18, 0x68, 0xee, 0x70, 0x3f, 0xa0, 0x09, 0x27,
	0x41, 0xa4, 0x84, 0x34, 0x8b, 0xe0, 0xfd, 0x14, 0x6a, 0xde, 0x2f, 0x8a, 0x43, 0x6e, 0xf4, 0x9b,
	0x93, 0xc5, 0xb1, 0x13, 0xf1, 0xb2, 0x14, 0x2c, 0x0a, 0x4b, 0x1a, 0xc6, 0x26, 0x0b, 0x22, 0xca,
	0x7d, 0xce, 0x62, 0xf3, 0xeb, 0xd0, 0x64, 0x11, 0x77, 0xd4, 0xd9, 0xdc, 0x78, 0x39, 0xd5, 0x19,
	0x26, 0x7f, 0x88, 0xb8, 0x4d, 0x08, 0x52, 0x4d, 0x0a, 0x7f, 0x5b, 0x21, 0x98, 0xe3, 0x9f, 0x88,
	0xed, 0x81, 0x63, 0xa4, 0x9b, 0xb3, 0x21, 0x48, 0x78, 0xe6, 0xba, 0x36, 0xb4, 0x0c, 0xaa, 0x5e,
	0x75, 0x7d, 0x53, 0x1e, 0xac, 0x5f, 0x18, 0x85, 0x79, 0xc9, 0x85, 0x3a, 0xe7, 0xc4, 0x79, 0x79,
	0x65, 0x9d, 0xb0, 0x5a, 0xf5, 0x89, 0xab, 0xa5, 0xeb, 0x5c, 0xe3, 0x5c, 0x9d, 0x9b, 0x2e, 0xe8,
	0xdc, 0x9f, 0x14, 0xa7, 0xb2, 0xfb, 0xd8, 0x26, 0xe1, 0x31, 0x4e, 0xe5, 0x6b, 0x59, 0xea, 0xc4,
	0x28, 0xe5, 0x17, 0x26, 0x4e, 0x5d, 0xa5, 0x50, 0xb6, 0x61, 0xce, 0xcd, 0x96, 0x59, 0xcf, 0x72,
	0x9c, 0x43, 0x20, 0xd7, 0x09, 0x7b, 0x36, 0xff, 0x10, 0x95, 0xe7, 0xd7, 0x30, 0xa6, 0x1b, 0x1b,
	0xec, 0xea, 0x2c, 0x85, 0xf5, 0x6f, 0x06, 0x86, 0x78, 0x13, 0x86, 0xf8, 0x7c, 0x0e, 0x44, 0x53,
	0x4c, 0x74, 0x20, 0x82, 0x9a, 0xf9, 0x21, 0x8a, 0x1e, 0x45, 0x39, 0x75, 0x91, 0x28, 0x73, 0xd1,
	0x8b, 0xc5, 0xc1, 0x25, 0x78, 0x00, 0x73, 0xc4, 0xf3, 0x1c, 0xee, 0xbb, 0xc7, 0x54, 0x69, 0x6c,
	0x1d, 0x49, 0xbc, 0xae, 0xfb, 0x5a, 0x4e, 0xce, 0xd6, 0x3d, 0x6f, 0x1f, 0x91, 0xf0, 0xf3, 0x2e,
	0xd1, 0x9b, 0xd6, 0xdf, 0x1b, 0x85, 0xed, 0x21, 0x98, 0xaa, 0x22, 0xc3, 0x7c, 0x1b, 0xd5, 0xf4,
	0x6d, 0xa4, 0x2b, 0xdb, 0x54, 0x51, 0xd9, 0x2e, 0xce, 0xda, 0x4e, 0xd2, 0xe7, 0xc6, 0x24, 0x7d,
	0xb6, 0x7e, 0x64, 0xc0, 0xc2, 0x18, 0xe3, 0x57, 0xb8, 0x32, 0x6b, 0x30, 0x1f, 0xd3, 0x40, 0x84,
	0x31, 0xf4, 0x94, 0xc7, 0x44, 0x22, 0xc9, 0xb9, 0xcc, 0xc9, 0x8e, 0x2d, 0x01, 0x17, 0xb8, 0xe2,
	0xa4, 0x3a, 0x6e, 0x9b, 0x37, 0x49, 0xe8, 0xd2, 0xe1, 0x15, 0xba, 0xae, 0x57, 0x35, 0xcc, 0xd6,
	0xc7, 0xe3, 0x8e, 0x38, 0xe3, 0xa6, 0xda, 0xc9, 0xe9, 0x3f, 0x6a, 0x70, 0x7d, 0x8c, 0x94, 0x0a,
	0x71, 0x34, 0xd5, 0x35, 0x2a, 0xaa, 0xee, 0x5b, 0xd0, 0x0d, 0x91, 0x48, 0x29, 0xaf, 0x2e, 0x81,
	0x2a, 0xaf, 0x8e, 0xaa, 0xe5, 0xe4, 0xb9, 0x86, 0x06, 0x8b, 0x54, 0x00, 0xc3, 0xa2, 0x3c, 0x80,
	0xa9, 0xa7, 0x61, 0x84, 0x1e, 0xc0, 0xb0, 0x28, 0x0b, 0x60, 0x54, 0x9a, 0x92, 0x45, 0x2a, 0x80,
	0xb1, 0xfe, 0xc2, 0x80, 0xd9, 0x83, 0x07, 0x99, 0xfb, 0x14, 0x23, 0xbd, 0x0e, 0xcb, 0x1a, 0xbf,
	0x12, 0x78, 0x10, 0x1e, 0x87, 0xec, 0x45, 0xd8, 0x7b, 0xcd, 0x5c, 0x85, 0x6b, 0x63, 0xbd, 0xe8,
	0x77, 0x7b, 0x86, 0xb9, 0x52, 0xb0, 0x8f, 0xb2, 0x6f, 0x3b, 0xdc, 0x7d, 0xdc, 0xab, 0x4d, 0x24,
	0x2a, 0x96, 0x88, 0x8d, 0x78, 0x6f, 0xca, 0xbc, 0x51, 0x90, 0xab, 0xec, 0x95, 0x4b, 0xd4, 0xab,
	0x5b, 0xff, 0x52, 0x83, 0x95, 0x75, 0x4f, 0xb9, 0x18, 0x81, 0x26, 0xf7, 0xeb, 0xab, 0x85, 0x96,
	0x13, 0xb4, 0xa4, 0x36, 0xd1, 0x21, 0x88, 0x40, 0x4f, 0xda, 0x8e, 0x7c, 0x97, 0xb6, 0x24, 0x44,
	0xec, 0xd3, 0xf7, 0x60, 0xd1, 0x1d, 0xc5, 0x31, 0x0d, 0xb9, 0xc3, 0x19, 0x27, 0x43, 0x65, 0x68,
	0xd4, 0x8e, 0x35, 0x55, 0xdf, 0xbe, 0xe8, 0x92, 0xec, 0x09, 0x6f, 0x15, 0x8d, 0x92, 0x23, 0xe7,
	0x84, 0xc6, 0x89, 0x38, 0x94, 0xc9, 0x7d, 0xdb, 0x16, 0xb0, 0xe7, 0x12, 0x64, 0xbe, 0x03, 0x0b,
	0xa3, 0x84, 0xc6, 0x8e, 0x66, 0xb8, 0xf2, 0x18, 0xa8, 0x27, 0xba, 0x32, 0x63, 0x25, 0x78, 0x78,
	0x1f, 0xae, 0x21, 0xba, 0xda, 0x88, 0xda, 0x17, 0x32, 0x2d, 0x86, 0xc4, 0x6c, 0xec, 0xcc, 0x3f,
	0x7a, 0x1d, 0xef, 0x63, 0x68, 0xe8, 0x39, 0xc4, 0x95, 0x31, 0x6a, 0xd3, 0x6e, 0xfa, 0xc9, 0x56,
	0xe8, 0xad, 0xbb, 0x5c, 0x1c, 0xf8, 0x56, 0x9f, 0x33, 0xdf, 0xa5, 0x9f, 0x10, 0x4e, 0xe3, 0x80,
	0xc4, 0xc7, 0x36, 0x4d, 0x46, 0xc3, 0x54, 0xb8, 0x13, 0x5d, 0x74, 0xc2, 0xc5, 0xc1, 0x54, 0xcf,
	0xa7, 0x22, 0x24, 0x3d, 0xa3, 0x9f, 0x08, 0x72, 0x78, 0x6e, 0x55, 0x99, 0x61, 0x04, 0xa8, 0x2c,
	0x9f, 0x9f, 0x38, 0x27, 0x64, 0xa8, 0x2c, 0x5d, 0xd3, 0x9e, 0xf1, 0x93, 0xe7, 0xa2, 0x69, 0xfd,
	0xb4, 0x06, 0x2b, 0xc8, 0xc7, 0x2e, 0x89, 0xb9, 0x4f, 0x86, 0xdf, 0x1e, 0x91, 0xa1, 0xcf, 0xcf,
	0xce, 0x65, 0xe3, 0x75, 0x68, 0x95, 0x17, 0x34, 0x07, 0x98, 0x6f, 0x83, 0x29, 0xb9, 0x90, 0x4b,
	0x9f, 0x50, 0x97, 0x85, 0x72, 0xdf, 0x18, 0x76, 0x0f, 0x7b, 0x50, 0x63, 0xf7, 0x10, 0x6e, 0xde,
	0x06, 0x09, 0x43, 0x21, 0x29, 0xdc, 0x3a, 0xe2, 0xce, 0x22, 0x7c, 0x2b, 0xf4, 0x14, 0xe6, 0x22,
	0x34, 0x12, 0x97, 0xc5, 0x34, 0x3d, 0x43, 0x60, 0x43, 0x44, 0x59, 0x11, 0x49, 0x12, 0x5c, 0xb6,
	0xa6, 0x8d, 0xbf, 0x31, 0xed, 0x46, 0x4f, 0xc5, 0xc2, 0x4c, 0x61, 0xda, 0x8d, 0x9e, 0x72, 0xf3,
	0x03, 0xb8, 0xee, 0x0e, 0x7d, 0xa1, 0x41, 0x31, 0x8d, 0x58, 0x41, 0x25, 0xe5, 0xd1, 0x61, 0x49,
	0x76, 0xdb, 0xd8, 0x9b, 0x6b, 0xe6, 0x75, 0x98, 0x21, 0xbe, 0x0c, 0xc0, 0x5b, 0x32, 0x00, 0x27,
	0xbe, 0x08, 0xc0, 0xad, 0x1d, 0x58, 0xb0, 0x49, 0x14, 0xd1, 0x78, 0x97, 0x25, 0x7c, 0x1f, 0xf3,
	0xa5, 0xc2, 0x9c, 0xe0, 0x91, 0x25, 0xf2, 0x5d, 0xfd, 0x60, 0xd1, 0x42, 0x08, 0x2e, 0x91, 0x9e,
	0x6b, 0xad, 0x15, 0x72, 0xad, 0xd6, 0x8f, 0x1b, 0x30, 0x2f, 0x29, 0xe2, 0x5a, 0x7c, 0x4e, 0xe9,
	0x5f, 0xa8, 0x03, 0xc2, 0xd6, 0x29, 0xd3, 0xa4, 0x27, 0xba, 0x3a, 0x29, 0x10, 0x0f, 0x04, 0xb7,
	0xa0, 0xd3, 0x1f, 0x38, 0x27, 0xbe, 0x47, 0x99, 0x96, 0xb0, 0x81, 0xfe, 0xe0, 0xb9, 0x00, 0x09,
	0x32, 0x3a, 0x46, 0xe0, 0xdd, 0x4b, 0x0f, 0x6e, 0x0a, 0xe3, 0xa9, 0x77, 0xcf, 0xb4, 0xa0, 0x2b,
	0xd6, 0x33, 0x27, 0x22, 0xcf, 0x0f, 0x6d, 0x1a, 0x7a, 0x19, 0x95, 0x02, 0x8e, 0x20, 0xd3, 0x2c,
	0xe2, 0x08, 0x3a, 0xdf, 0x4a, 0xa5, 0x89, 0xdc, 0xb6, 0x30, 0x3e, 0xfb, 0x82, 0x66, 0xda, 0x27,
	0xac, 0x80, 0x92, 0x36, 0x4e, 0xa5, 0xb8, 0x5f, 0xa0, 0xbc, 0x5f, 0xbe, 0x04, 0xb3, 0xc1, 0x28,
	0xf1, 0x5d, 0x27, 0x8a, 0x99, 0x37, 0x72, 0x69, 0x8c, 0xb9, 0x9b, 0x96, 0xdd, 0x45, 0xe8, 0xae,
	0x02, 0xe6, 0x68, 0x9e, 0x1f, 0x53, 0x97, 0xb3, 0x78, 0xb9, 0xa3, 0xa1, 0x3d, 0x50, 0xc0, 0x8b,
	0x34, 0xac, 0x7b, 0x91, 0x86, 0xdd, 0x04, 0x10, 0x5b, 0x9f, 0xca, 0xe4, 0xfc, 0xac, 0x64, 0x52,
	0x42, 0x84, 0x98, 0xde, 0x81, 0x85, 0xfe, 0xc0, 0x61, 0xb1, 0x3f, 0xf0, 0x43, 0xe7, 0x84, 0x7a,
	0xbe, 0x14, 0xe8, 0x1c, 0xe2, 0xf5, 0xfa, 0x83, 0x1d, 0xec, 0x79, 0x2e, 0x3a, 0x04, 0xfa, 0x07,
	0xb0, 0x9c, 0xad, 0xcd, 0xa1, 0x1f, 0x27, 0xdc, 0x39, 0x8c, 0xc5, 0x72, 0x8b, 0x6f, 0x7a, 0xf8,
	0xcd, 0xa2, 0x5a, 0xa7, 0x87, 0xa2, 0xf7, 0xa1, 0xe8, 0x14, 0xdf, 0x69, 0x7a, 0x3e, 0x5f, 0xd0,
	0xf3, 0xbf, 0xa9, 0xc3, 0x8a, 0xbc, 0xa7, 0x93, 0x66, 0x7d, 0xe3, 0x11, 0x8a, 0xfa, 0xd5, 0x1c,
	0xc0, 0x1a, 0xcc, 0xab, 0xeb, 0xdf, 0x14, 0xab, 0x3f, 0x40, 0x9d, 0x6d, 0xda, 0x73, 0xb2, 0x23,
	0xa5, 0x3a, 0x10, 0xfa, 0x30, 0x0a, 0x3d, 0x1a, 0x67, 0x37, 0x19, 0x52, 0x7b, 0xdb, 0x08, 0x54,
	0xf7, 0x18, 0x05, 0x1c, 0xa1, 0x33, 0xf5, 0x22, 0x8e, 0xd2, 0x3d, 0xb9, 0xe8, 0x29, 0x1d, 0xa9,
	0xc0, 0x6d, 0x04, 0xe6, 0x74, 0x72, 0x9c, 0x5c, 0x85, 0x33, 0x1c, 0x41, 0xe7, 0xff, 0x80, 0x29,
	0x7a, 0xd1, 0x62, 0x3a, 0x7d, 0x2a, 0xc4, 0xcf, 0x13, 0x65, 0xec, 0xe7, 0x22, 0xdf, 0x45, 0xdb,
	0xb9, 0x21, 0xe0, 0xfb, 0x89, 0xf9, 0x15, 0xe8, 0xe5, 0xc8, 0x42, 0xad, 0x79, 0xa2, 0xec, 0x4a,
	0x37, 0x45, 0xdd, 0x0a, 0x3d, 0x85, 0xe8, 0x3a, 0xc5, 0x89, 0xb6, 0xa4, 0x3a, 0x45, 0xee, 0x81,
	0x36, 0xd5, 0x32, 0xa2, 0xe0, 0x12, 0xca, 0x88, 0x82, 0x4f, 0x89, 0x58, 0x9c, 0x72, 0x3b, 0x45,
	0xdc, 0xd3, 0x26, 0x5d, 0x46, 0x14, 0x14, 0x3b, 0x65, 0x44, 0x41, 0x51, 0x37, 0x52, 0xdd, 0xb1,
	0x0b, 0x21, 0xcd, 0xbc, 0xcd, 0x96, 0xcc, 0x9b, 0xf5, 0xe7, 0x06, 0x2c, 0xee, 0x51, 0x5e, 0x76,
	0x6a, 0x97, 0x29, 0x31, 0x28, 0xee, 0xe1, 0xa9, 0xf2, 0x1e, 0xce, 0xbc, 0x8d, 0x38, 0x93, 0xb1,
	0x84, 0x7a, 0xce, 0x31, 0x3d, 0x53, 0x6a, 0x21, 0x3d, 0xcb, 0xa6, 0xea, 0x78, 0x4c, 0xcf, 0xac,
	0x0d, 0x58, 0x9a, 0xc0, 0x5e, 0xb5, 0x38, 0x74, 0x17, 0xaf, 0x4a, 0xae, 0x70, 0x8a, 0xd6, 0xf7,
	0x61, 0xe9, 0xd1, 0x25, 0xb9, 0xba, 0x4c, 0x68, 0x20, 0x66, 0xf4, 0x80, 0x0e, 0xaf, 0x72, 0x46,
	0x1b, 0xb0, 0x34, 0x81, 0x62, 0x35, 0x39, 0xff, 0x93, 0x01, 0xbd, 0x3d, 0x1a, 0x7a, 0x36, 0x25,
	0x1e, 0x52, 0xba, 0x9c, 0x1e, 0x5d, 0x87, 0x99, 0x84, 0x85, 0x83, 0x34, 0x51, 0xdc, 0xb2, 0xa7,
	0x45, 0x53, 0xde, 0x0a, 0x48, 0xd1, 0xe4, 0x8a, 0x23, 0x45, 0xf3, 0x98, 0x9e, 0x5d, 0x64, 0xd4,
	0x1b, 0xaf, 0x18, 0x36, 0x4c, 0x17, 0xcc, 0xe9, 0x8f, 0x0d, 0x98, 0x2f, 0xcd, 0xaa, 0xda, 0x42,
	0x5f, 0xec, 0xfe, 0x2f, 0xe0, 0x77, 0xea, 0x02, 0x7e, 0xad, 0xef, 0x41, 0xfb, 0x09, 0x3d, 0xa1,
	0x43, 0x79, 0x23, 0x21, 0xb4, 0x69, 0x28, 0x9a, 0x85, 0x28, 0x06, 0x21, 0xa8, 0x4d, 0xd7, 0x61,
	0x26, 0xf0, 0x43, 0xbc, 0x3b, 0x90, 0x1c, 0x4c, 0x07, 0x7e, 0xf8, 0x6c, 0x14, 0x60, 0x07, 0x39,
	0xc5, 0x8e, 0x29, 0xd5, 0x41, 0x4e, 0x9f, 0x8d, 0x02, 0xeb, 0x37, 0xa0, 0x25, 0x0e, 0x0f, 0x38,
	0x46, 0xa5, 0x6a, 0x87, 0xb7, 0xa1, 0x81, 0xe3, 0xaa, 0x94, 0xc4, 0x35, 0xcd, 0xf9, 0x6b, 0x0c,
	0xdb, 0x12, 0x49, 0xc4, 0x88, 0x11, 0xf3, 0xc3, 0xf4, 0x64, 0x20, 0x1b, 0xd6, 0x7a, 0x96, 0x9f,
	0x59, 0xdf, 0x96, 0x11, 0x03, 0x7e, 0x5a, 0xa5, 0x4a, 0xe8, 0xb3, 0x2c, 0xff, 0x52, 0x22, 0x51,
	0x6d, 0xf1, 0xde, 0x07, 0xc0, 0x43, 0x84, 0x9c, 0x92, 0x4c, 0xea, 0x2d, 0x96, 0x8e, 0xaa, 0x92,
	0x70, 0x6b, 0x94, 0xfe, 0xb4, 0x7e, 0xc7, 0x80, 0x05, 0x11, 0xe2, 0xa4, 0x23, 0x8b, 0xdf, 0x97,
	0xde, 0x0b, 0x11, 0x4b, 0xb8, 0xb6, 0x17, 0x44, 0x73, 0xdb, 0xd3, 0xd5, 0xb6, 0x5e, 0x50, 0xdb,
	0x75, 0x58, 0x1c, 0x67, 0xa1, 0xaa, 0xdd, 0xec, 0x3e, 0xa2, 0x7c, 0x9f, 0xf4, 0xab, 0xde, 0x56,
	0x6b, 0x4c, 0xd5, 0x0a, 0x4c, 0x7d, 0x04, 0x9d, 0x7d, 0xd2, 0x5f, 0xe7, 0x3c, 0xf6, 0xfb, 0x23,
	0x4e, 0xf1, 0x9a, 0x81, 0xf4, 0xf3, 0x3a, 0x94, 0x06, 0x27, 0x7d, 0x99, 0x00, 0xca, 0x6e, 0x17,
	0xd3, 0x98, 0x5b, 0xde, 0x2c, 0x5a, 0x7f, 0x6d, 0xc0, 0xac, 0xce, 0x54, 0xb5, 0xd5, 0xbc, 0x2b,
	0x09, 0xab, 0x04, 0xad, 0x88, 0x4d, 0xaf, 0x6b, 0x6b, 0xa9, 0xb3, 0x86, 0x23, 0xe2, 0xf9, 0xe0,
	0x1e, 0xb4, 0x43, 0xfa, 0xa2, 0xef, 0x53, 0x47, 0x98, 0x1f, 0xa5, 0xd5, 0xba, 0x0a, 0x3c, 0x39,
	0x8b, 0x55, 0x20, 0x0b, 0x12, 0x71, 0x8f, 0x85, 0x03, 0xeb, 0x6f, 0x0d, 0xac, 0x45, 0xca, 0x3b,
	0xab, 0xe5, 0xc5, 0x94, 0x58, 0x6a, 0xba, 0x58, 0x16, 0xa1, 0x31, 0xf4, 0x03, 0x3f, 0x3d, 0x6e,
	0xcb, 0x86, 0x79, 0x0d, 0xa6, 0xd9, 0xe1, 0x61, 0x92, 0x1d, 0xae, 0x55, 0x4b, 0x37, 0x9f, 0x8d,
	0x82, 0xf9, 0x3c, 0xd7, 0xd2, 0xdd, 0x47, 0x3f, 0xb9, 0x79, 0xd6, 0xa7, 0xf1, 0x27, 0x2c, 0x1e,
	0x7a, 0x1f, 0xb3, 0xa0, 0x8a, 0x09, 0xb7, 0xfe, 0xd8, 0x80, 0xb9, 0xfc, 0xeb, 0xa7, 0xcc, 0xa3,
	0x18, 0xa5, 0xa6, 0x21, 0x8e, 0xa1, 0x14, 0x57, 0xc6, 0x36, 0x8b, 0xd0, 0xe0, 0x3e, 0x1f, 0xd2,
	0x6c, 0x8a, 0xa2, 0x81, 0x17, 0xbe, 0xa3, 0xbe, 0x23, 0x7b, 0x94, 0xd7, 0x4b, 0x46, 0xfd, 0x7d,
	0xec, 0x5c, 0x81, 0xe6, 0xaf, 0x8f, 0x82, 0x48, 0x2b, 0x9a, 0x99, 0x11, 0xed, 0x52, 0x30, 0xdc,
	0x28, 0xcc, 0xe9, 0x37, 0xd1, 0x53, 0x97, 0xe7, 0x54, 0xb9, 0x06, 0x28, 0x10, 0x93, 0xd1, 0x73,
	0xce, 0x7a, 0x0d, 0x50, 0x69, 0xce, 0x76, 0x0b, 0xb1, 0x31, 0xd1, 0xfc, 0x0c, 0xda, 0x9b, 0x24,
	0xf6, 0x36, 0x1e, 0x61, 0x00, 0x2f, 0xce, 0x61, 0x47, 0x2c, 0xc8, 0xa3, 0x3e, 0x75, 0x8d, 0x25,
	0x60, 0x2a, 0xe4, 0xbb, 0x09, 0xd0, 0x1f, 0x71, 0xce, 0x42, 0x27, 0x2f, 0xa3, 0x68, 0x49, 0x88,
	0x70, 0xfc, 0xff, 0xdd, 0x80, 0x56, 0xa6, 0x52, 0xfa, 0x12, 0x1b, 0x65, 0x0f, 0x89, 0x1d, 0x7a,
	0x61, 0x80, 0x00, 0xa4, 0x27, 0xda, 0x98, 0x44, 0x7a, 0x74, 0x36, 0x13, 0x93, 0x08, 0xbb, 0xde,
	0x82, 0xee, 0x50, 0x50, 0x77, 0x5c, 0x16, 0x72, 0x1a, 0x66, 0xc7, 0x4d, 0x04, 0x6e, 0x4a, 0x18,
	0x26, 0x3e, 0x11, 0x29, 0x0f, 0xd5, 0x9b, 0x08, 0x50, 0x49, 0x8b, 0x43, 0x7f, 0x48, 0xb5, 0x10,
	0x7d, 0x46, 0xb4, 0x45, 0x90, 0x5a, 0x74, 0x51, 0x33, 0x65, 0x17, 0xf5, 0x06, 0xb4, 0x65, 0xb7,
	0xcc, 0x19, 0xc8, 0x58, 0x5c, 0x7e, 0xb1, 0x87, 0x89, 0x83, 0x1b, 0xd0, 0xf2, 0x13, 0x67, 0xc8,
	0xdc, 0x63, 0xea, 0xa5, 0xc5, 0x00, 0x7e, 0xf2, 0x04, 0xdb, 0xe6, 0x1d, 0x58, 0xa0, 0xa7, 0x2e,
	0x8d, 0x23, 0x9e, 0xf2, 0xae, 0xd5, 0x02, 0xcc, 0xab, 0x2e, 0x35, 0x03, 0xc1, 0xe7, 0x04, 0x7c,
	0xc1, 0x72, 0x7b, 0x12, 0xbe, 0x60, 0xfe, 0x6d, 0x30, 0x0f, 0x63, 0x5f, 0x9c, 0x13, 0x5c, 0x3f,
	0x76, 0x87, 0xd4, 0x39, 0xa2, 0x84, 0x63, 0x30, 0x5e, 0xb7, 0x7b, 0xb2, 0x67, 0x13, 0x3b, 0x3e,
	0xa6, 0x44, 0xf8, 0x40, 0x33, 0x3d, 0xa1, 0x3a, 0x79, 0xe9, 0x9f, 0x8c, 0xcc, 0x7b, 0x69, 0xcf,
	0xb3, 0xb4, 0x04, 0xd0, 0x82, 0x6e, 0x48, 0x5f, 0x38, 0xb9, 0x50, 0x65, 0x94, 0x2e, 0xcc, 0xce,
	0x93, 0x54, 0xae, 0x05, 0x1c, 0xc1, 0xe9, 0x5c, 0x11, 0x47, 0xf0, 0x58, 0xce, 0x03, 0xf4, 0x5e,
	0x9a, 0x07, 0x98, 0x1f, 0xcb, 0x03, 0x7c, 0x04, 0x37, 0xc7, 0xe7, 0xe9, 0x78, 0x84, 0x8f, 0x02,
	0x67, 0xe8, 0x87, 0x74, 0xd9, 0xc4, 0x29, 0xaf, 0x94, 0xa7, 0xfc, 0x40, 0x60, 0x3c, 0xf1, 0x43,
	0xaa, 0x6f, 0xc5, 0x05, 0x7d, 0x2b, 0x9a, 0xeb, 0x30, 0xef, 0x92, 0xd8, 0x73, 0x0a, 0x3c, 0x2e,
	0x8e, 0x05, 0x09, 0xda, 0x7e, 0xb1, 0x67, 0xc5, 0x07, 0x1b, 0x19, 0xff, 0xd6, 0x3f, 0x18, 0x58,
	0xcd, 0xa7, 0x19, 0xd5, 0xca, 0xde, 0x5c, 0x4a, 0x50, 0xf3, 0x00, 0x93, 0x4d, 0xb9, 0x54, 0xf1,
	0xb4, 0x5c, 0xf2, 0x88, 0x24, 0x4e, 0x20, 0xb4, 0x52, 0x16, 0x68, 0xcf, 0x1c, 0x91, 0xe4, 0xa9,
	0x50, 0xc9, 0xf7, 0xe1, 0x9a, 0x9f, 0x38, 0x2c, 0xa2, 0xa1, 0x3a, 0xef, 0x93, 0xa1, 0x93, 0xb0,
	0x51, 0x98, 0x26, 0xec, 0x16, 0xfc, 0x64, 0x27, 0xa2, 0xe1, 0x8e, 0xea, 0xdb, 0x13, 0x5d, 0xd6,
	0x1f, 0x19, 0x30, 0xb7, 0x3e, 0x18, 0xc4, 0x74, 0x40, 0xb2, 0xeb, 0xed, 0xc7, 0x60, 0x92, 0x1c,
	0xe4, 0xb8, 0x47, 0x8c, 0x25, 0x32, 0x8c, 0x9b, 0x2d, 0xdc, 0xc8, 0x68, 0xdf, 0x6d, 0x22, 0x8e,
	0x3d, 0x4f, 0xca, 0x20, 0xdd, 0x2c, 0xd4, 0x0a, 0x66, 0xe1, 0x1a, 0x4c, 0x87, 0xa3, 0xa0, 0x4f,
	0xe3, 0x34, 0xd6, 0x93, 0x2d, 0xeb, 0xcf, 0x0c, 0x30, 0x4b, 0x1c, 0x55, 0x71, 0x57, 0x5b, 0xd0,
	0xd3, 0x99, 0xd7, 0x64, 0xbb, 0x3a, 0x99, 0x75, 0x1c, 0x60, 0x8e, 0x94, 0x64, 0xa0, 0x29, 0xce,
	0x54, 0x29, 0x6a, 0x58, 0x18, 0xe3, 0xae, 0x5a, 0x24, 0xc3, 0xb0, 0xfa, 0xee, 0xdb, 0x23, 0x9a,
	0x08, 0x0a, 0x21, 0xf1, 0x63, 0x5a, 0x31, 0x9e, 0x99, 0x2c, 0xd0, 0x73, 0x59, 0xfe, 0x99, 0x81,
	0xbe, 0xb4, 0x34, 0x62, 0x35, 0x65, 0x7d, 0x13, 0x3a, 0x9e, 0x9f, 0x44, 0x43, 0x72, 0x26, 0xeb,
	0x9f, 0x64, 0x16, 0xa6, 0xad, 0x60, 0x58, 0x02, 0x25, 0x9c, 0xa8, 0x38, 0xca, 0xa7, 0xf5, 0xb8,
	0xd8, 0x10, 0xb1, 0xb9, 0xbc, 0x28, 0x4f, 0x94, 0xfd, 0x4e, 0x9b, 0x58, 0x1e, 0x42, 0xa9, 0xd7,
	0x27, 0xee, 0xb1, 0xc3, 0xf3, 0x3a, 0xec, 0x4e, 0x0a, 0xdc, 0xa7, 0xa7, 0xdc, 0xfa, 0x4f, 0xc9,
	0xfb, 0xc7, 0xf7, 0x0e, 0xe2, 0xe1, 0x27, 0x3e, 0x3f, 0x5a, 0xdf, 0xde, 0xe4, 0xa7, 0x55, 0xc4,
	0x55, 0x2c, 0xcc, 0xca, 0xf7, 0x7a, 0xad, 0x5c, 0x98, 0x95, 0xd9, 0xa5, 0x0b, 0x73, 0xa0, 0x05,
	0x67, 0x56, 0x2f, 0x39, 0xb3, 0x6b, 0x30, 0x8d, 0x3b, 0x35, 0x51, 0x45, 0xb2, 0xaa, 0xa5, 0x2f,
	0xd9, 0xf4, 0x79, 0x4b, 0x36, 0x53, 0x58, 0xb2, 0x5f, 0x18, 0x18, 0x2a, 0x94, 0xa7, 0x5d, 0x6d,
	0xcd, 0x96, 0x60, 0xfa, 0xe8, 0x9e, 0x36, 0xd9, 0xc6, 0xd1, 0x3d, 0xe5, 0xd5, 0xd5, 0x1d, 0x44,
	0x1e, 0xd7, 0xb4, 0xe4, 0xc5, 0xc3, 0x58, 0xd4, 0x53, 0x2f, 0x45, 0x3d, 0x37, 0xa0, 0x25, 0xbf,
	0xcd, 0xd7, 0xab, 0x89, 0x9f, 0xd2, 0x53, 0x9e, 0x2b, 0xc0, 0x34, 0xce, 0x5e, 0x36, 0x2c, 0x02,
	0xd7, 0xd2, 0xf2, 0xc2, 0x0d, 0xe1, 0x2f, 0x1f, 0xfa, 0x43, 0x4e, 0xe3, 0x2a, 0x4b, 0xf8, 0x06,
	0xb4, 0x0f, 0xf1, 0xa3, 0x3c, 0x8a, 0xc7, 0xca, 0x3c, 0x01, 0x42, 0x69, 0xfd, 0xa4, 0x9e, 0x97,
	0x45, 0x16, 0xc6, 0xa8, 0x26, 0xaf, 0x5d, 0xe8, 0xa8, 0x71, 0x7c, 0x4e, 0x83, 0x44, 0x99, 0x8d,
	0x77, 0x34, 0xb3, 0x71, 0xce, 0x20, 0x77, 0xe4, 0xcf, 0x6d, 0x4e, 0x03, 0x5b, 0xb1, 0x2a, 0x7e,
	0x27, 0xab, 0x3f, 0x37, 0x00, 0xf2, 0xbe, 0x3c, 0xcc, 0x34, 0xf4, 0x30, 0xf3, 0x36, 0xf4, 0xb4,
	0x61, 0xf5, 0x39, 0xce, 0xe6, 0xb4, 0xd0, 0x69, 0xdd, 0x80, 0x56, 0x8a, 0x99, 0x1e, 0xbd, 0x9a,
	0x0a, 0xc5, 0x33, 0xbf, 0x93, 0x91, 0x11, 0xcb, 0x27, 0x67, 0x20, 0x0b, 0x59, 0xde, 0x7b, 0xe5,
	0x19, 0xec, 0x8d, 0xfa, 0x38, 0x09, 0x35, 0xb0, 0x6a, 0x26, 0xab, 0x9f, 0x41, 0xb7, 0x80, 0x70,
	0xce, 0x4c, 0x2c, 0xe8, 0xea, 0x2c, 0xa4, 0x06, 0xaa, 0x9d, 0x53, 0xf3, 0xcc, 0x77, 0x61, 0xb1,
	0xc4, 0xa6, 0xa3, 0x15, 0x39, 0xcf, 0x17, 0x06, 0xc6, 0xc5, 0x7d, 0x02, 0xb3, 0x0f, 0x8b, 0x62,
	0x58, 0x84, 0xde, 0xc7, 0x3b, 0x4f, 0xb7, 0x9c, 0x87, 0xdb, 0x4f, 0xf6, 0xb7, 0x6c, 0x67, 0x7b,
	0x7f, 0xeb, 0x69, 0xef, 0x35, 0x73, 0x09, 0xe6, 0x77, 0xd7, 0x1f, 0xe9, 0x50, 0xfb, 0x69, 0xcf,
	0x30, 0xbb, 0xd0, 0x42, 0xf0, 0xee, 0xce, 0xde, 0x7e, 0xaf, 0x66, 0x1d, 0xc2, 0xcd, 0xb4, 0x02,
	0xbf, 0x98, 0x91, 0xae, 0xea, 0x67, 0x5e, 0x52, 0x72, 0xf1, 0x43, 0x03, 0xbe, 0x70, 0xd1, 0x40,
	0xd5, 0x34, 0xf3, 0xeb, 0x85, 0xe2, 0xe8, 0x2f, 0x8e, 0xbd, 0x81, 0x99, 0x90, 0x5b, 0x57, 0x85,
	0xd2, 0x5b, 0x70, 0xe3, 0x20, 0x1a, 0xc4, 0xc4, 0xa3, 0x97, 0x4a, 0x60, 0x6c, 0xc3, 0xeb, 0xe7,
	0x93, 0xa9, 0xe6, 0xfe, 0x7e, 0x5a, 0x83, 0xeb, 0xf2, 0xb1, 0x96, 0x4d, 0x5d, 0x16, 0x04, 0x54,
	0x15, 0xd8, 0x0a, 0xb5, 0x7a, 0xc9, 0x7d, 0xc0, 0x9b, 0xd0, 0x49, 0xbb, 0xb5, 0xc3, 0x44, 0x5b,
	0xc1, 0xd0, 0x04, 0xdf, 0x85, 0xa5, 0x14, 0x85, 0xbd, 0x08, 0x69, 0x9c, 0xdd, 0x9e, 0x4b, 0xfd,
	0x5a, 0x50, 0x9d, 0x3b, 0xa2, 0x2f, 0x2d, 0x04, 0x5c, 0x83, 0xf9, 0xe2, 0x37, 0x09, 0x3d, 0x45,
	0xcb, 0xd7, 0xb0, 0xe7, 0x74, 0xfc, 0x3d, 0x7a, 0x8a, 0x77, 0xc9, 0x0a, 0x57, 0xbd, 0x21, 0xc9,
	0x2f, 0xe7, 0xbb, 0xb6, 0xa9, 0xfa, 0xe4, 0xdb, 0x11, 0xf9, 0xca, 0xe1, 0x16, 0x74, 0xa2, 0xbe,
	0x83, 0xef, 0x20, 0x3c, 0xc2, 0x09, 0x7a, 0x80, 0x8e, 0x0d, 0x51, 0xff, 0xb9, 0x4f, 0x5f, 0x3c,
	0x20, 0x9c, 0xc8, 0xdb, 0xe6, 0xfe, 0xd0, 0x4f, 0x8e, 0x1c, 0x8f, 0x26, 0x6e, 0x7a, 0xdb, 0xa5,
	0x60, 0x0f, 0x68, 0xe2, 0x5a, 0x9b, 0xb0, 0x22, 0x65, 0xf6, 0x88, 0xf2, 0x82, 0xd8, 0xaa, 0x2c,
	0xe2, 0x5f, 0x1a, 0xb0, 0x7a, 0x1e, 0x95, 0x6a, 0xfa, 0xf8, 0x01, 0xd4, 0xc5, 0xce, 0x55, 0x16,
	0xd2, 0xd2, 0xaf, 0xd4, 0x26, 0xaf, 0xac, 0x8d, 0xf8, 0x18, 0x45, 0x9c, 0x85, 0x24, 0xf0, 0x5d,
	0xe9, 0x41, 0xd4, 0x1d, 0x8d, 0x82, 0xa1, 0xc3, 0xff, 0x26, 0xcc, 0x67, 0x3c, 0xee, 0xb2, 0x84,
	0x27, 0x55, 0x66, 0x18, 0x83, 0x59, 0xfe, 0xb8, 0xda, 0xc4, 0xfe, 0x6f, 0x61, 0x62, 0x37, 0xc7,
	0x26, 0x96, 0xd2, 0xcd, 0xe7, 0x64, 0xfd, 0x95, 0x51, 0x1e, 0x34, 0x55, 0x65, 0xcd, 0xcb, 0x1a,
	0x17, 0x7a, 0xd9, 0xda, 0x78, 0x6e, 0x61, 0x14, 0x8b, 0x13, 0x7e, 0x78, 0x9c, 0x1e, 0x8a, 0x47,
	0xf1, 0xf0, 0x89, 0x1f, 0x1e, 0x0b, 0xb2, 0x98, 0x02, 0xc4, 0x0a, 0xf3, 0xb4, 0x54, 0x5e, 0x40,
	0xb0, 0xae, 0xbc, 0x70, 0xf7, 0xd2, 0x28, 0x5e, 0x10, 0xff, 0x9e, 0x01, 0xd7, 0x64, 0xde, 0xb6,
	0x50, 0x49, 0x75, 0xe9, 0xeb, 0x15, 0x6d, 0xc3, 0x4e, 0x9d, 0x5f, 0x5d, 0x5d, 0xd7, 0xaa, 0xab,
	0xad, 0x7f, 0xae, 0xc1, 0xfc, 0x58, 0x39, 0x97, 0x88, 0x9e, 0x54, 0x0d, 0x8d, 0xdc, 0xf8, 0xaa,
	0x85, 0x8f, 0x40, 0x39, 0x39, 0xc3, 0x57, 0x42, 0xf1, 0x09, 0x19, 0x66, 0x4f, 0x57, 0x39, 0x39,
	0xdb, 0x56, 0xb0, 0xf3, 0xea, 0xb8, 0x5f, 0x52, 0xa2, 0x55, 0x2c, 0x1c, 0x69, 0x94, 0x0b, 0x47,
	0xde, 0x85, 0x45, 0x59, 0x30, 0x32, 0xb1, 0xc8, 0x63, 0x1e, 0xfb, 0x0a, 0x55, 0x1e, 0xe7, 0x14,
	0x85, 0xcc, 0x54, 0x2e, 0x0a, 0x69, 0xbe, 0x6a, 0x51, 0x48, 0xab, 0x54, 0x14, 0x72, 0x02, 0xd7,
	0x27, 0x2e, 0x72, 0xb5, 0x6d, 0xf0, 0x5e, 0xc1, 0xdf, 0x5c, 0x5c, 0x85, 0x27, 0xfd, 0xcc, 0x23,
	0x58, 0x28, 0x74, 0xa9, 0x0b, 0xde, 0xea, 0x84, 0x7e, 0x60, 0x40, 0x77, 0x8f, 0xf2, 0xa7, 0xbe,
	0xbb, 0xc7, 0xae, 0xb4, 0x5c, 0x7a, 0x4d, 0xb1, 0x32, 0x7e, 0x13, 0xa0, 0xc6, 0xd2, 0x98, 0xf8,
	0x26, 0xcc, 0xea, 0x3c, 0x54, 0x73, 0x70, 0x03, 0x68, 0x3f, 0xf5, 0x5d, 0xe1, 0x27, 0xcf, 0x29,
	0x95, 0x5d, 0x85, 0x66, 0x56, 0xf4, 0x55, 0xcb, 0x5f, 0x60, 0x62, 0xc9, 0x97, 0x76, 0x95, 0x31,
	0x55, 0xbc, 0xca, 0x50, 0x0f, 0x2f, 0xeb, 0xd9, 0xc3, 0x4b, 0xeb, 0xbb, 0x38, 0x50, 0xca, 0x3a,
	0x3e, 0x45, 0xf5, 0x5d, 0x79, 0x3c, 0x33, 0x64, 0x6a, 0x20, 0xf0, 0x5d, 0x3c, 0x9a, 0xbd, 0x0f,
	0x2d, 0x69, 0x35, 0xf2, 0xd3, 0x70, 0x49, 0x00, 0x29, 0xbb, 0x76, 0x73, 0xa4, 0x7e, 0x59, 0xcf,
	0x31, 0xe3, 0x7e, 0xe5, 0x0b, 0x61, 0x0d, 0x30, 0x69, 0xfe, 0xf9, 0x84, 0x9b, 0xad, 0x62, 0xed,
	0x15, 0x56, 0xf1, 0x33, 0xe8, 0xc9, 0xf7, 0xe0, 0x9f, 0x63, 0x0e, 0xf9, 0xd3, 0x74, 0xed, 0xb8,
	0xab, 0x9e, 0xa6, 0xa3, 0x48, 0x2f, 0xb6, 0x7c, 0xd6, 0x7d, 0x98, 0x2f, 0x8d, 0x5d, 0x4d, 0x89,
	0x7e, 0x20, 0x93, 0x4b, 0x0f, 0x59, 0xec, 0x52, 0x8c, 0xea, 0x2e, 0x67, 0xa7, 0xf5, 0x4b, 0x8c,
	0xa9, 0xc2, 0x25, 0x46, 0xda, 0xa5, 0xdd, 0xda, 0x88, 0x2e, 0x0c, 0xbd, 0xbf, 0x0f, 0xd7, 0x84,
	0x43, 0x53, 0x99, 0xc7, 0xdd, 0x98, 0x1d, 0xfa, 0xea, 0xd5, 0xee, 0x2a, 0xa0, 0x9e, 0x68, 0xf7,
	0x7b, 0x59, 0x1b, 0xc3, 0x19, 0x96, 0x64, 0xa9, 0xcc, 0x34, 0x4a, 0x8b, 0x72, 0x4a, 0xe6, 0x97,
	0x60, 0x96, 0x70, 0xee, 0xf3, 0x91, 0x47, 0x9d, 0x5c, 0xd9, 0xbb, 0x76, 0x37, 0x85, 0x62, 0xe8,
	0x64, 0xfd, 0x76, 0x0d, 0x66, 0x73, 0x09, 0xa4, 0x4a, 0x9e, 0x39, 0x38, 0xe3, 0xa2, 0xe2, 0x82,
	0x5a, 0xb9, 0x76, 0x2a, 0xeb, 0xd6, 0x32, 0x24, 0xb2, 0x3b, 0xfd, 0x17, 0x0c, 0x69, 0x40, 0x97,
	0x5d, 0x00, 0xb6, 0x24, 0x44, 0x58, 0xdd, 0xfb, 0xd0, 0xc2, 0x49, 0x69, 0xcf, 0x25, 0xde, 0xd4,
	0x5f, 0xfd, 0x4e, 0x14, 0x93, 0xdd, 0x14, 0xdf, 0x60, 0x2d, 0x91, 0x2e, 0x30, 0x79, 0x3c, 0xce,
	0x05, 0xb6, 0x0a, 0x02, 0xcf, 0xc7, 0xe7, 0x5f, 0xd2, 0x55, 0x64, 0x6d, 0xeb, 0x14, 0xe6, 0x4b,
	0x6a, 0x50, 0xf5, 0xe4, 0xa0, 0x97, 0x40, 0xc9, 0xad, 0xbf, 0xa2, 0x31, 0x5e, 0x14, 0xaf, 0x56,
	0xfd, 0x64, 0x9d, 0xe2, 0x9d, 0xe5, 0xc6, 0x58, 0x7e, 0xa4, 0x8a, 0x22, 0xde, 0x80, 0x56, 0x9e,
	0xdf, 0x55, 0xa6, 0xee, 0x24, 0xcd, 0xee, 0x9e, 0x77, 0xa5, 0x6e, 0x7d, 0x06, 0x2b, 0xe7, 0x8c,
	0x5c, 0xf5, 0x82, 0xad, 0xae, 0x19, 0x3c, 0xbd, 0xf0, 0xab, 0x44, 0x5b, 0xb3, 0x19, 0x3f, 0x31,
	0x60, 0x61, 0x42, 0x6f, 0x3e, 0x93, 0xfc, 0xa6, 0x44, 0xce, 0x24, 0xcd, 0x18, 0x9d, 0x3b, 0xcd,
	0x77, 0x60, 0x41, 0x3e, 0xb1, 0x76, 0xd9, 0xc9, 0x58, 0x79, 0x52, 0x0f, 0xbb, 0x36, 0x45, 0x8f,
	0xba, 0x73, 0xb9, 0x0d, 0xbd, 0xb4, 0x5a, 0x2b, 0x1b, 0xaf, 0xae, 0xfe, 0xf5, 0x80, 0xac, 0xd5,
	0x4a, 0xf3, 0xcf, 0xff, 0x6e, 0x60, 0x39, 0xca, 0x06, 0xe1, 0x7c, 0x48, 0xd3, 0xc7, 0x45, 0x23,
	0x9a, 0xf0, 0x5f, 0xe1, 0x03, 0xa3, 0xf4, 0x89, 0xb2, 0xdc, 0x2f, 0xf2, 0x89, 0xb2, 0x88, 0x5e,
	0x7d, 0x2f, 0xdf, 0x29, 0x5d, 0x7b, 0x66, 0xe4, 0x7b, 0x9f, 0xfb, 0xb5, 0xd0, 0x26, 0x5c, 0x2b,
	0xcf, 0x32, 0x89, 0x58, 0x98, 0xd0, 0x2a, 0xe6, 0x94, 0xc0, 0xb2, 0xac, 0x49, 0xfe, 0xa5, 0x49,
	0xcb, 0x7a, 0x08, 0x2b, 0x13, 0x86, 0xa8, 0xce, 0xea, 0xf7, 0x30, 0xf3, 0xf7, 0xcb, 0xe3, 0x73,
	0x84, 0xf9, 0xb8, 0xcb, 0x31, 0x79, 0x41, 0x5c, 0xa7, 0x11, 0x2e, 0x24, 0x22, 0xfe, 0xcb, 0x80,
	0xf9, 0xb1, 0xbe, 0x2b, 0xd0, 0xc0, 0x77, 0xf3, 0x03, 0x39, 0xaa, 0x56, 0xf1, 0xbd, 0x5b, 0x7a,
	0xb0, 0x97, 0x05, 0xe8, 0xfb, 0x57, 0xaf, 0x9e, 0xe2, 0x74, 0xd2, 0xc7, 0x69, 0xa5, 0x0f, 0x00,
	0x64, 0xe8, 0xde, 0xe9, 0xa7, 0x73, 0xe5, 0xa3, 0xc4, 0xfa, 0x3e, 0x2c, 0x66, 0xd1, 0x80, 0xf8,
	0xea, 0xe1, 0x28, 0x74, 0xaf, 0x30, 0xb4, 0x7d, 0x03, 0xda, 0x87, 0xa3, 0xd0, 0x75, 0x64, 0x78,
	0xa2, 0xee, 0x85, 0x40, 0x80, 0xe4, 0xa8, 0x58, 0xb8, 0x36, 0x3e, 0x7e, 0xb5, 0x88, 0x24, 0x0b,
	0x07, 0x05, 0x81, 0x2b, 0x0c, 0x07, 0xc3, 0x34, 0x1c, 0x94, 0x74, 0xab, 0x9e, 0xd7, 0x31, 0xe6,
	0x3d, 0x3f, 0x24, 0x14, 0x44, 0xe5, 0xa3, 0xb6, 0x40, 0xba, 0x38, 0xeb, 0x47, 0xd9, 0x01, 0xe3,
	0x6a, 0x27, 0x52, 0xe0, 0x65, 0xea, 0xd5, 0x78, 0xc9, 0xce, 0x19, 0x9f, 0x63, 0xee, 0xd6, 0xbf,
	0x1a, 0x18, 0xff, 0xa7, 0x54, 0xc5, 0x66, 0x91, 0x2a, 0xe8, 0x90, 0x91, 0xe7, 0x73, 0x75, 0x06,
	0x68, 0x4b, 0xd8, 0xba, 0x00, 0x95, 0x15, 0xa5, 0x56, 0x56, 0x14, 0xf3, 0x5b, 0x30, 0x1b, 0xa8,
	0x08, 0xca, 0x21, 0x71, 0x4c, 0xce, 0xd4, 0xbf, 0xdd, 0xb9, 0x3e, 0x3e, 0x93, 0x75, 0xd1, 0x6d,
	0x77, 0x02, 0xad, 0x95, 0x06, 0x9e, 0xf5, 0x3c, 0xf0, 0xfc, 0x00, 0xda, 0xc4, 0x95, 0xfb, 0x97,
	0x79, 0xb2, 0x1e, 0x62, 0xf6, 0xee, 0x92, 0x7e, 0x13, 0x87, 0xbd, 0x4f, 0x99, 0x47, 0x6d, 0x20,
	0xd9, 0x6f, 0xeb, 0x23, 0xe8, 0xe8, 0xe3, 0x88, 0xf3, 0x7d, 0x90, 0x47, 0x7d, 0x5d, 0xbb, 0x11,
	0x60, 0xcc, 0xa7, 0xce, 0x3c, 0x7a, 0x71, 0x8e, 0x62, 0x68, 0x6d, 0x0f, 0x7a, 0xfa, 0xff, 0x84,
	0xc2, 0xbd, 0xbf, 0x04, 0xf3, 0xea, 0x35, 0x4c, 0x0e, 0xec, 0xbd, 0x66, 0xce, 0x43, 0x57, 0x18,
	0x88, 0xf5, 0xd0, 0x7b, 0x88, 0xff, 0x26, 0xa6, 0x67, 0x20, 0x66, 0xf9, 0x89, 0x64, 0xaf, 0xb6,
	0xb6, 0x91, 0xe6, 0x7a, 0xf6, 0xf4, 0xff, 0x9b, 0x75, 0x1d, 0x16, 0x0a, 0x64, 0x25, 0xb8, 0xf7,
	0x9a, 0xd9, 0x84, 0xfa, 0x4e, 0x44, 0xc3, 0x9e, 0x61, 0xb6, 0xa0, 0xb1, 0x39, 0x64, 0x09, 0xed,
	0xd5, 0xd6, 0xbe, 0x5b, 0xf8, 0x07, 0x3e, 0x36, 0x1b, 0x52, 0x73, 0x0e, 0xda, 0x8a, 0x80, 0x68,
	0xf6, 0x5e, 0x33, 0x67, 0x01, 0xf6, 0x29, 0x09, 0x9e, 0x50, 0xe2, 0xd1, 0xb8, 0x67, 0x98, 0x00,
	0xd3, 0xcf, 0x58, 0x1c, 0x90, 0x61, 0xaf, 0x26, 0x90, 0x9f, 0xfb, 0x2e, 0xdd, 0x24, 0x78, 0x8f,
	0xd6, 0x9b, 0x32, 0x3b, 0xd0, 0x4c, 0xab, 0xcd, 0x7b, 0xf5, 0xb5, 0xbb, 0x30, 0x9b, 0xff, 0x7f,
	0x09, 0x9c, 0x75, 0x0f, 0x3a, 0x8a, 0x3a, 0xc2, 0x24, 0x79, 0x64, 0x41, 0xb6, 0x8d, 0xb5, 0xaf,
	0x81, 0x39, 0xfe, 0x5f, 0x06, 0x04, 0x16, 0x42, 0x3f, 0x39, 0xf2, 0xb9, 0x62, 0x0a, 0xdb, 0x1b,
	0x43, 0xe2, 0x1e, 0xf7, 0x8c, 0xb5, 0xfb, 0x13, 0x5e, 0x87, 0xe2, 0x80, 0xf9, 0x74, 0x94, 0x80,
	0x17, 0xa1, 0xb7, 0xc7, 0x63, 0x3f, 0x1c, 0xc8, 0x57, 0xa6, 0x08, 0xf5, 0xd6, 0xee, 0xc1, 0xf4,
	0xfa, 0x76, 0xca, 0xe1, 0xba, 0x2f, 0x73, 0xca, 0xea, 0x8b, 0x15, 0x58, 0x5a, 0xf7, 0x1f, 0xb2,
	0x98, 0xfa, 0x83, 0xf0, 0x09, 0x09, 0x07, 0x23, 0x32, 0xa0, 0xd8, 0x65, 0xac, 0x7d, 0x08, 0xf3,
	0x63, 0x37, 0xcf, 0x42, 0x06, 0x5b, 0xff, 0x3f, 0x62, 0xc9, 0x28, 0x16, 0x5f, 0xb7, 0x61, 0x46,
	0xd5, 0xed, 0x4a, 0xd1, 0xe3, 0x3f, 0xb3, 0xe9, 0xd5, 0xd6, 0xb6, 0x4a, 0x59, 0x0a, 0xb5, 0x7e,
	0xf3, 0xd0, 0x4d, 0xd7, 0x2f, 0x5d, 0xb9, 0x0e, 0x34, 0x77, 0xd2, 0x16, 0x5e, 0x22, 0xec, 0x1c,
	0x1e, 0xaa, 0x66, 0x6d, 0xed, 0x19, 0xf4, 0x36, 0x34, 0xf3, 0xbe, 0x15, 0x62, 0xed, 0x62, 0xaa,
	0x03, 0x7a, 0x57, 0xef, 0x35, 0x21, 0x0c, 0xf4, 0x78, 0x12, 0xdc, 0x33, 0xc4, 0x64, 0xf5, 0x40,
	0xa1, 0x57, 0x5b, 0xdb, 0x07, 0xc8, 0xb7, 0x81, 0xa6, 0xa4, 0x39, 0x50, 0xc9, 0x10, 0xb5, 0x4b,
	0x83, 0xe2, 0xb3, 0xad, 0xad, 0x53, 0x12, 0xf8, 0x21, 0x15, 0x00, 0xad, 0xab, 0xb6, 0xf1, 0xff,
	0x60, 0xd9, 0x65, 0xc1, 0x9d, 0x33, 0xff, 0x8c, 0x8d, 0xc4, 0x86, 0xc3, 0x42, 0x20, 0xf9, 0x2f,
	0xee, 0xbe, 0xf3, 0xd6, 0x80, 0x0d, 0x49, 0x38, 0xb8, 0x73, 0xef, 0x2e, 0xe7, 0x77, 0x5c, 0x16,
	0xbc, 0x8b, 0x60, 0x97, 0x0d, 0xdf, 0x25, 0x51, 0xf4, 0xae, 0xdc, 0x9a, 0xfd, 0x69, 0x04, 0xbe,
	0xff, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x99, 0x4d, 0xa8, 0xac, 0x39, 0x4f, 0x00, 0x00,
}
