// Code generated by protoc-gen-gogo.
// source: lbs_.proto
// DO NOT EDIT!

/*
	Package lbs is a generated protocol buffer package.

	It is generated from these files:
		lbs_.proto

	It has these top-level messages:
		ReportUserGeoReq
		ReportUserGeoResp
		LbsNearUser
		GetNearUserListReq
		GetNearUserListResp
		ModifyUserGeoSwitchReq
		ModifyUserGeoSwitchResp
		GetUserGeoSwitchReq
		GetUserGeoSwitchResp
		LocationCity
		UserLocation
		ReportUserLocationReq
		ReportUserLocationResp
		CityUserNearChannelInfo
		CityUserNear
		ListCityUserFilter
		ListCityUserNearReq
		ListCityUserNearResp
		FindFriendFilter
		GetFindFriendListReq
		GetFindFriendListResp
*/
package lbs

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import math3 "math"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type FindFriendFilter_SexType int32

const (
	FindFriendFilter_FIND_FRIEND_FEMALE FindFriendFilter_SexType = 0
	FindFriendFilter_FIND_FRIEND_MALE   FindFriendFilter_SexType = 1
	FindFriendFilter_FIND_FRIEND_ALL    FindFriendFilter_SexType = 2
)

var FindFriendFilter_SexType_name = map[int32]string{
	0: "FIND_FRIEND_FEMALE",
	1: "FIND_FRIEND_MALE",
	2: "FIND_FRIEND_ALL",
}
var FindFriendFilter_SexType_value = map[string]int32{
	"FIND_FRIEND_FEMALE": 0,
	"FIND_FRIEND_MALE":   1,
	"FIND_FRIEND_ALL":    2,
}

func (x FindFriendFilter_SexType) Enum() *FindFriendFilter_SexType {
	p := new(FindFriendFilter_SexType)
	*p = x
	return p
}
func (x FindFriendFilter_SexType) String() string {
	return proto.EnumName(FindFriendFilter_SexType_name, int32(x))
}
func (x *FindFriendFilter_SexType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FindFriendFilter_SexType_value, data, "FindFriendFilter_SexType")
	if err != nil {
		return err
	}
	*x = FindFriendFilter_SexType(value)
	return nil
}
func (FindFriendFilter_SexType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorLbs_, []int{18, 0}
}

// 上报用户经纬度, deprecated
type ReportUserGeoReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Longitude float64     `protobuf:"fixed64,2,opt,name=longitude" json:"longitude"`
	Latitude  float64     `protobuf:"fixed64,3,opt,name=latitude" json:"latitude"`
}

func (m *ReportUserGeoReq) Reset()                    { *m = ReportUserGeoReq{} }
func (m *ReportUserGeoReq) String() string            { return proto.CompactTextString(m) }
func (*ReportUserGeoReq) ProtoMessage()               {}
func (*ReportUserGeoReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{0} }

func (m *ReportUserGeoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportUserGeoReq) GetLongitude() float64 {
	if m != nil {
		return m.Longitude
	}
	return 0
}

func (m *ReportUserGeoReq) GetLatitude() float64 {
	if m != nil {
		return m.Latitude
	}
	return 0
}

// deprecated
type ReportUserGeoResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ReportUserGeoResp) Reset()                    { *m = ReportUserGeoResp{} }
func (m *ReportUserGeoResp) String() string            { return proto.CompactTextString(m) }
func (*ReportUserGeoResp) ProtoMessage()               {}
func (*ReportUserGeoResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{1} }

func (m *ReportUserGeoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取附近的人
// deprecated
type LbsNearUser struct {
	Uid          uint32  `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account      string  `protobuf:"bytes,2,req,name=account" json:"account"`
	Ttid         string  `protobuf:"bytes,3,req,name=ttid" json:"ttid"`
	Nickname     string  `protobuf:"bytes,4,req,name=nickname" json:"nickname"`
	Sex          int32   `protobuf:"varint,5,req,name=sex" json:"sex"`
	Signature    string  `protobuf:"bytes,6,req,name=signature" json:"signature"`
	Distance     float64 `protobuf:"fixed64,7,req,name=distance" json:"distance"`
	LastTime     uint32  `protobuf:"varint,8,req,name=last_time,json=lastTime" json:"last_time"`
	ChannelId    uint32  `protobuf:"varint,9,opt,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType  uint32  `protobuf:"varint,10,opt,name=channel_type,json=channelType" json:"channel_type"`
	ChannelIsPwd bool    `protobuf:"varint,11,opt,name=channel_is_pwd,json=channelIsPwd" json:"channel_is_pwd"`
}

func (m *LbsNearUser) Reset()                    { *m = LbsNearUser{} }
func (m *LbsNearUser) String() string            { return proto.CompactTextString(m) }
func (*LbsNearUser) ProtoMessage()               {}
func (*LbsNearUser) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{2} }

func (m *LbsNearUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LbsNearUser) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *LbsNearUser) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *LbsNearUser) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *LbsNearUser) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *LbsNearUser) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *LbsNearUser) GetDistance() float64 {
	if m != nil {
		return m.Distance
	}
	return 0
}

func (m *LbsNearUser) GetLastTime() uint32 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

func (m *LbsNearUser) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LbsNearUser) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *LbsNearUser) GetChannelIsPwd() bool {
	if m != nil {
		return m.ChannelIsPwd
	}
	return false
}

type GetNearUserListReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Longitude float64     `protobuf:"fixed64,2,opt,name=longitude" json:"longitude"`
	Latitude  float64     `protobuf:"fixed64,3,opt,name=latitude" json:"latitude"`
	// min_distance + begin_uid + count 作为分页选项 存在
	MinDistance float64 `protobuf:"fixed64,4,req,name=min_distance,json=minDistance" json:"min_distance"`
	Count       uint32  `protobuf:"varint,5,req,name=count" json:"count"`
	BeginUid    uint32  `protobuf:"varint,6,req,name=begin_uid,json=beginUid" json:"begin_uid"`
	FilterSex   int32   `protobuf:"varint,7,req,name=filter_sex,json=filterSex" json:"filter_sex"`
}

func (m *GetNearUserListReq) Reset()                    { *m = GetNearUserListReq{} }
func (m *GetNearUserListReq) String() string            { return proto.CompactTextString(m) }
func (*GetNearUserListReq) ProtoMessage()               {}
func (*GetNearUserListReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{3} }

func (m *GetNearUserListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetNearUserListReq) GetLongitude() float64 {
	if m != nil {
		return m.Longitude
	}
	return 0
}

func (m *GetNearUserListReq) GetLatitude() float64 {
	if m != nil {
		return m.Latitude
	}
	return 0
}

func (m *GetNearUserListReq) GetMinDistance() float64 {
	if m != nil {
		return m.MinDistance
	}
	return 0
}

func (m *GetNearUserListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetNearUserListReq) GetBeginUid() uint32 {
	if m != nil {
		return m.BeginUid
	}
	return 0
}

func (m *GetNearUserListReq) GetFilterSex() int32 {
	if m != nil {
		return m.FilterSex
	}
	return 0
}

type GetNearUserListResp struct {
	BaseResp *ga.BaseResp   `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	UserList []*LbsNearUser `protobuf:"bytes,2,rep,name=user_list,json=userList" json:"user_list,omitempty"`
}

func (m *GetNearUserListResp) Reset()                    { *m = GetNearUserListResp{} }
func (m *GetNearUserListResp) String() string            { return proto.CompactTextString(m) }
func (*GetNearUserListResp) ProtoMessage()               {}
func (*GetNearUserListResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{4} }

func (m *GetNearUserListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNearUserListResp) GetUserList() []*LbsNearUser {
	if m != nil {
		return m.UserList
	}
	return nil
}

// 附近的人开关
type ModifyUserGeoSwitchReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	DisableGeo bool        `protobuf:"varint,2,req,name=disable_geo,json=disableGeo" json:"disable_geo"`
}

func (m *ModifyUserGeoSwitchReq) Reset()                    { *m = ModifyUserGeoSwitchReq{} }
func (m *ModifyUserGeoSwitchReq) String() string            { return proto.CompactTextString(m) }
func (*ModifyUserGeoSwitchReq) ProtoMessage()               {}
func (*ModifyUserGeoSwitchReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{5} }

func (m *ModifyUserGeoSwitchReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ModifyUserGeoSwitchReq) GetDisableGeo() bool {
	if m != nil {
		return m.DisableGeo
	}
	return false
}

type ModifyUserGeoSwitchResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	DisableGeo bool         `protobuf:"varint,2,req,name=disable_geo,json=disableGeo" json:"disable_geo"`
}

func (m *ModifyUserGeoSwitchResp) Reset()                    { *m = ModifyUserGeoSwitchResp{} }
func (m *ModifyUserGeoSwitchResp) String() string            { return proto.CompactTextString(m) }
func (*ModifyUserGeoSwitchResp) ProtoMessage()               {}
func (*ModifyUserGeoSwitchResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{6} }

func (m *ModifyUserGeoSwitchResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ModifyUserGeoSwitchResp) GetDisableGeo() bool {
	if m != nil {
		return m.DisableGeo
	}
	return false
}

type GetUserGeoSwitchReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetUserGeoSwitchReq) Reset()                    { *m = GetUserGeoSwitchReq{} }
func (m *GetUserGeoSwitchReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserGeoSwitchReq) ProtoMessage()               {}
func (*GetUserGeoSwitchReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{7} }

func (m *GetUserGeoSwitchReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserGeoSwitchResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	DisableGeo bool         `protobuf:"varint,2,req,name=disable_geo,json=disableGeo" json:"disable_geo"`
}

func (m *GetUserGeoSwitchResp) Reset()                    { *m = GetUserGeoSwitchResp{} }
func (m *GetUserGeoSwitchResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserGeoSwitchResp) ProtoMessage()               {}
func (*GetUserGeoSwitchResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{8} }

func (m *GetUserGeoSwitchResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserGeoSwitchResp) GetDisableGeo() bool {
	if m != nil {
		return m.DisableGeo
	}
	return false
}

type LocationCity struct {
	Country  string `protobuf:"bytes,1,opt,name=country" json:"country"`
	Province string `protobuf:"bytes,2,opt,name=province" json:"province"`
	City     string `protobuf:"bytes,3,opt,name=city" json:"city"`
}

func (m *LocationCity) Reset()                    { *m = LocationCity{} }
func (m *LocationCity) String() string            { return proto.CompactTextString(m) }
func (*LocationCity) ProtoMessage()               {}
func (*LocationCity) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{9} }

func (m *LocationCity) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *LocationCity) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *LocationCity) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

type UserLocation struct {
	Longitude float64       `protobuf:"fixed64,1,opt,name=longitude" json:"longitude"`
	Latitude  float64       `protobuf:"fixed64,2,opt,name=latitude" json:"latitude"`
	City      *LocationCity `protobuf:"bytes,3,opt,name=city" json:"city,omitempty"`
}

func (m *UserLocation) Reset()                    { *m = UserLocation{} }
func (m *UserLocation) String() string            { return proto.CompactTextString(m) }
func (*UserLocation) ProtoMessage()               {}
func (*UserLocation) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{10} }

func (m *UserLocation) GetLongitude() float64 {
	if m != nil {
		return m.Longitude
	}
	return 0
}

func (m *UserLocation) GetLatitude() float64 {
	if m != nil {
		return m.Latitude
	}
	return 0
}

func (m *UserLocation) GetCity() *LocationCity {
	if m != nil {
		return m.City
	}
	return nil
}

// 上报位置
type ReportUserLocationReq struct {
	BaseReq           *ga.BaseReq   `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CanAccessLocation bool          `protobuf:"varint,2,req,name=can_access_location,json=canAccessLocation" json:"can_access_location"`
	ValidLocation     bool          `protobuf:"varint,3,req,name=valid_location,json=validLocation" json:"valid_location"`
	UserLocation      *UserLocation `protobuf:"bytes,4,opt,name=user_location,json=userLocation" json:"user_location,omitempty"`
}

func (m *ReportUserLocationReq) Reset()                    { *m = ReportUserLocationReq{} }
func (m *ReportUserLocationReq) String() string            { return proto.CompactTextString(m) }
func (*ReportUserLocationReq) ProtoMessage()               {}
func (*ReportUserLocationReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{11} }

func (m *ReportUserLocationReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ReportUserLocationReq) GetCanAccessLocation() bool {
	if m != nil {
		return m.CanAccessLocation
	}
	return false
}

func (m *ReportUserLocationReq) GetValidLocation() bool {
	if m != nil {
		return m.ValidLocation
	}
	return false
}

func (m *ReportUserLocationReq) GetUserLocation() *UserLocation {
	if m != nil {
		return m.UserLocation
	}
	return nil
}

type ReportUserLocationResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *ReportUserLocationResp) Reset()                    { *m = ReportUserLocationResp{} }
func (m *ReportUserLocationResp) String() string            { return proto.CompactTextString(m) }
func (*ReportUserLocationResp) ProtoMessage()               {}
func (*ReportUserLocationResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{12} }

func (m *ReportUserLocationResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

//
type CityUserNearChannelInfo struct {
	ChannelId       uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType     uint32 `protobuf:"varint,2,opt,name=channel_type,json=channelType" json:"channel_type"`
	FindPlayingText string `protobuf:"bytes,3,opt,name=find_playing_text,json=findPlayingText" json:"find_playing_text"`
	FindPlayingImg  string `protobuf:"bytes,4,opt,name=find_playing_img,json=findPlayingImg" json:"find_playing_img"`
}

func (m *CityUserNearChannelInfo) Reset()                    { *m = CityUserNearChannelInfo{} }
func (m *CityUserNearChannelInfo) String() string            { return proto.CompactTextString(m) }
func (*CityUserNearChannelInfo) ProtoMessage()               {}
func (*CityUserNearChannelInfo) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{13} }

func (m *CityUserNearChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CityUserNearChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *CityUserNearChannelInfo) GetFindPlayingText() string {
	if m != nil {
		return m.FindPlayingText
	}
	return ""
}

func (m *CityUserNearChannelInfo) GetFindPlayingImg() string {
	if m != nil {
		return m.FindPlayingImg
	}
	return ""
}

type CityUserNear struct {
	// account info
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account  string `protobuf:"bytes,2,req,name=account" json:"account"`
	Alias    string `protobuf:"bytes,3,req,name=alias" json:"alias"`
	Nickname string `protobuf:"bytes,4,req,name=nickname" json:"nickname"`
	Sex      int32  `protobuf:"varint,5,req,name=sex" json:"sex"`
	FaceMd5  string `protobuf:"bytes,6,req,name=face_md5,json=faceMd5" json:"face_md5"`
	// location
	Distance float64       `protobuf:"fixed64,7,opt,name=distance" json:"distance"`
	City     *LocationCity `protobuf:"bytes,8,opt,name=city" json:"city,omitempty"`
	LastTime uint32        `protobuf:"varint,9,opt,name=last_time,json=lastTime" json:"last_time"`
	// user tag
	GameTagGameList []string `protobuf:"bytes,10,rep,name=game_tag_game_list,json=gameTagGameList" json:"game_tag_game_list,omitempty"`
	// in-channel
	InChannel *CityUserNearChannelInfo `protobuf:"bytes,11,opt,name=in_channel,json=inChannel" json:"in_channel,omitempty"`
	// online
	OnlineAt      uint32 `protobuf:"varint,12,opt,name=online_at,json=onlineAt" json:"online_at"`
	OfflineAt     uint32 `protobuf:"varint,13,opt,name=offline_at,json=offlineAt" json:"offline_at"`
	RecommandNote string `protobuf:"bytes,14,opt,name=recommand_note,json=recommandNote" json:"recommand_note"`
}

func (m *CityUserNear) Reset()                    { *m = CityUserNear{} }
func (m *CityUserNear) String() string            { return proto.CompactTextString(m) }
func (*CityUserNear) ProtoMessage()               {}
func (*CityUserNear) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{14} }

func (m *CityUserNear) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CityUserNear) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CityUserNear) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *CityUserNear) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CityUserNear) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *CityUserNear) GetFaceMd5() string {
	if m != nil {
		return m.FaceMd5
	}
	return ""
}

func (m *CityUserNear) GetDistance() float64 {
	if m != nil {
		return m.Distance
	}
	return 0
}

func (m *CityUserNear) GetCity() *LocationCity {
	if m != nil {
		return m.City
	}
	return nil
}

func (m *CityUserNear) GetLastTime() uint32 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

func (m *CityUserNear) GetGameTagGameList() []string {
	if m != nil {
		return m.GameTagGameList
	}
	return nil
}

func (m *CityUserNear) GetInChannel() *CityUserNearChannelInfo {
	if m != nil {
		return m.InChannel
	}
	return nil
}

func (m *CityUserNear) GetOnlineAt() uint32 {
	if m != nil {
		return m.OnlineAt
	}
	return 0
}

func (m *CityUserNear) GetOfflineAt() uint32 {
	if m != nil {
		return m.OfflineAt
	}
	return 0
}

func (m *CityUserNear) GetRecommandNote() string {
	if m != nil {
		return m.RecommandNote
	}
	return ""
}

type ListCityUserFilter struct {
	Sex       int32 `protobuf:"varint,1,opt,name=sex" json:"sex"`
	Online    bool  `protobuf:"varint,2,opt,name=online" json:"online"`
	InChannel bool  `protobuf:"varint,3,opt,name=in_channel,json=inChannel" json:"in_channel"`
}

func (m *ListCityUserFilter) Reset()                    { *m = ListCityUserFilter{} }
func (m *ListCityUserFilter) String() string            { return proto.CompactTextString(m) }
func (*ListCityUserFilter) ProtoMessage()               {}
func (*ListCityUserFilter) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{15} }

func (m *ListCityUserFilter) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ListCityUserFilter) GetOnline() bool {
	if m != nil {
		return m.Online
	}
	return false
}

func (m *ListCityUserFilter) GetInChannel() bool {
	if m != nil {
		return m.InChannel
	}
	return false
}

type ListCityUserNearReq struct {
	BaseReq       *ga.BaseReq         `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ValidLocation bool                `protobuf:"varint,2,req,name=valid_location,json=validLocation" json:"valid_location"`
	UserLocation  *UserLocation       `protobuf:"bytes,3,opt,name=user_location,json=userLocation" json:"user_location,omitempty"`
	Filter        *ListCityUserFilter `protobuf:"bytes,4,opt,name=filter" json:"filter,omitempty"`
	Offset        uint32              `protobuf:"varint,5,opt,name=offset" json:"offset"`
}

func (m *ListCityUserNearReq) Reset()                    { *m = ListCityUserNearReq{} }
func (m *ListCityUserNearReq) String() string            { return proto.CompactTextString(m) }
func (*ListCityUserNearReq) ProtoMessage()               {}
func (*ListCityUserNearReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{16} }

func (m *ListCityUserNearReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListCityUserNearReq) GetValidLocation() bool {
	if m != nil {
		return m.ValidLocation
	}
	return false
}

func (m *ListCityUserNearReq) GetUserLocation() *UserLocation {
	if m != nil {
		return m.UserLocation
	}
	return nil
}

func (m *ListCityUserNearReq) GetFilter() *ListCityUserFilter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (m *ListCityUserNearReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type ListCityUserNearResp struct {
	BaseResp   *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	UserList   []*CityUserNear `protobuf:"bytes,2,rep,name=user_list,json=userList" json:"user_list,omitempty"`
	NextOffset uint32          `protobuf:"varint,3,opt,name=next_offset,json=nextOffset" json:"next_offset"`
}

func (m *ListCityUserNearResp) Reset()                    { *m = ListCityUserNearResp{} }
func (m *ListCityUserNearResp) String() string            { return proto.CompactTextString(m) }
func (*ListCityUserNearResp) ProtoMessage()               {}
func (*ListCityUserNearResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{17} }

func (m *ListCityUserNearResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListCityUserNearResp) GetUserList() []*CityUserNear {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *ListCityUserNearResp) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

type FindFriendFilter struct {
	Sex         int32 `protobuf:"varint,1,opt,name=sex" json:"sex"`
	IsOnline    bool  `protobuf:"varint,2,opt,name=is_online,json=isOnline" json:"is_online"`
	IsInChannel bool  `protobuf:"varint,3,opt,name=is_in_channel,json=isInChannel" json:"is_in_channel"`
}

func (m *FindFriendFilter) Reset()                    { *m = FindFriendFilter{} }
func (m *FindFriendFilter) String() string            { return proto.CompactTextString(m) }
func (*FindFriendFilter) ProtoMessage()               {}
func (*FindFriendFilter) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{18} }

func (m *FindFriendFilter) GetSex() int32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *FindFriendFilter) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

func (m *FindFriendFilter) GetIsInChannel() bool {
	if m != nil {
		return m.IsInChannel
	}
	return false
}

type GetFindFriendListReq struct {
	BaseReq *ga.BaseReq       `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Filter  *FindFriendFilter `protobuf:"bytes,2,opt,name=filter" json:"filter,omitempty"`
	Offset  uint32            `protobuf:"varint,3,opt,name=offset" json:"offset"`
}

func (m *GetFindFriendListReq) Reset()                    { *m = GetFindFriendListReq{} }
func (m *GetFindFriendListReq) String() string            { return proto.CompactTextString(m) }
func (*GetFindFriendListReq) ProtoMessage()               {}
func (*GetFindFriendListReq) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{19} }

func (m *GetFindFriendListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetFindFriendListReq) GetFilter() *FindFriendFilter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (m *GetFindFriendListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetFindFriendListResp struct {
	BaseResp   *ga.BaseResp    `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	UserList   []*CityUserNear `protobuf:"bytes,2,rep,name=user_list,json=userList" json:"user_list,omitempty"`
	NextOffset uint32          `protobuf:"varint,3,opt,name=next_offset,json=nextOffset" json:"next_offset"`
}

func (m *GetFindFriendListResp) Reset()                    { *m = GetFindFriendListResp{} }
func (m *GetFindFriendListResp) String() string            { return proto.CompactTextString(m) }
func (*GetFindFriendListResp) ProtoMessage()               {}
func (*GetFindFriendListResp) Descriptor() ([]byte, []int) { return fileDescriptorLbs_, []int{20} }

func (m *GetFindFriendListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetFindFriendListResp) GetUserList() []*CityUserNear {
	if m != nil {
		return m.UserList
	}
	return nil
}

func (m *GetFindFriendListResp) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

func init() {
	proto.RegisterType((*ReportUserGeoReq)(nil), "ga.ReportUserGeoReq")
	proto.RegisterType((*ReportUserGeoResp)(nil), "ga.ReportUserGeoResp")
	proto.RegisterType((*LbsNearUser)(nil), "ga.LbsNearUser")
	proto.RegisterType((*GetNearUserListReq)(nil), "ga.GetNearUserListReq")
	proto.RegisterType((*GetNearUserListResp)(nil), "ga.GetNearUserListResp")
	proto.RegisterType((*ModifyUserGeoSwitchReq)(nil), "ga.ModifyUserGeoSwitchReq")
	proto.RegisterType((*ModifyUserGeoSwitchResp)(nil), "ga.ModifyUserGeoSwitchResp")
	proto.RegisterType((*GetUserGeoSwitchReq)(nil), "ga.GetUserGeoSwitchReq")
	proto.RegisterType((*GetUserGeoSwitchResp)(nil), "ga.GetUserGeoSwitchResp")
	proto.RegisterType((*LocationCity)(nil), "ga.LocationCity")
	proto.RegisterType((*UserLocation)(nil), "ga.UserLocation")
	proto.RegisterType((*ReportUserLocationReq)(nil), "ga.ReportUserLocationReq")
	proto.RegisterType((*ReportUserLocationResp)(nil), "ga.ReportUserLocationResp")
	proto.RegisterType((*CityUserNearChannelInfo)(nil), "ga.CityUserNearChannelInfo")
	proto.RegisterType((*CityUserNear)(nil), "ga.CityUserNear")
	proto.RegisterType((*ListCityUserFilter)(nil), "ga.ListCityUserFilter")
	proto.RegisterType((*ListCityUserNearReq)(nil), "ga.ListCityUserNearReq")
	proto.RegisterType((*ListCityUserNearResp)(nil), "ga.ListCityUserNearResp")
	proto.RegisterType((*FindFriendFilter)(nil), "ga.FindFriendFilter")
	proto.RegisterType((*GetFindFriendListReq)(nil), "ga.GetFindFriendListReq")
	proto.RegisterType((*GetFindFriendListResp)(nil), "ga.GetFindFriendListResp")
	proto.RegisterEnum("ga.FindFriendFilter_SexType", FindFriendFilter_SexType_name, FindFriendFilter_SexType_value)
}
func (m *ReportUserGeoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportUserGeoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x11
	i++
	i = encodeFixed64Lbs_(dAtA, i, uint64(math3.Float64bits(float64(m.Longitude))))
	dAtA[i] = 0x19
	i++
	i = encodeFixed64Lbs_(dAtA, i, uint64(math3.Float64bits(float64(m.Latitude))))
	return i, nil
}

func (m *ReportUserGeoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportUserGeoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *LbsNearUser) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LbsNearUser) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.Ttid)))
	i += copy(dAtA[i:], m.Ttid)
	dAtA[i] = 0x22
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x28
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x32
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.Signature)))
	i += copy(dAtA[i:], m.Signature)
	dAtA[i] = 0x39
	i++
	i = encodeFixed64Lbs_(dAtA, i, uint64(math3.Float64bits(float64(m.Distance))))
	dAtA[i] = 0x40
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.LastTime))
	dAtA[i] = 0x48
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x50
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x58
	i++
	if m.ChannelIsPwd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetNearUserListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNearUserListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x11
	i++
	i = encodeFixed64Lbs_(dAtA, i, uint64(math3.Float64bits(float64(m.Longitude))))
	dAtA[i] = 0x19
	i++
	i = encodeFixed64Lbs_(dAtA, i, uint64(math3.Float64bits(float64(m.Latitude))))
	dAtA[i] = 0x21
	i++
	i = encodeFixed64Lbs_(dAtA, i, uint64(math3.Float64bits(float64(m.MinDistance))))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x30
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.BeginUid))
	dAtA[i] = 0x38
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.FilterSex))
	return i, nil
}

func (m *GetNearUserListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetNearUserListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if len(m.UserList) > 0 {
		for _, msg := range m.UserList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintLbs_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ModifyUserGeoSwitchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyUserGeoSwitchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	if m.DisableGeo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ModifyUserGeoSwitchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyUserGeoSwitchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	if m.DisableGeo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserGeoSwitchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserGeoSwitchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *GetUserGeoSwitchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserGeoSwitchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	if m.DisableGeo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *LocationCity) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LocationCity) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.Country)))
	i += copy(dAtA[i:], m.Country)
	dAtA[i] = 0x12
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.Province)))
	i += copy(dAtA[i:], m.Province)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.City)))
	i += copy(dAtA[i:], m.City)
	return i, nil
}

func (m *UserLocation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLocation) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x9
	i++
	i = encodeFixed64Lbs_(dAtA, i, uint64(math3.Float64bits(float64(m.Longitude))))
	dAtA[i] = 0x11
	i++
	i = encodeFixed64Lbs_(dAtA, i, uint64(math3.Float64bits(float64(m.Latitude))))
	if m.City != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.City.Size()))
		n9, err := m.City.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *ReportUserLocationReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportUserLocationReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseReq.Size()))
		n10, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	if m.CanAccessLocation {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.ValidLocation {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.UserLocation != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.UserLocation.Size()))
		n11, err := m.UserLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *ReportUserLocationResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportUserLocationResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	return i, nil
}

func (m *CityUserNearChannelInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CityUserNearChannelInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.ChannelType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.FindPlayingText)))
	i += copy(dAtA[i:], m.FindPlayingText)
	dAtA[i] = 0x22
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.FindPlayingImg)))
	i += copy(dAtA[i:], m.FindPlayingImg)
	return i, nil
}

func (m *CityUserNear) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CityUserNear) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	dAtA[i] = 0x22
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x28
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x32
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.FaceMd5)))
	i += copy(dAtA[i:], m.FaceMd5)
	dAtA[i] = 0x39
	i++
	i = encodeFixed64Lbs_(dAtA, i, uint64(math3.Float64bits(float64(m.Distance))))
	if m.City != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.City.Size()))
		n13, err := m.City.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x48
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.LastTime))
	if len(m.GameTagGameList) > 0 {
		for _, s := range m.GameTagGameList {
			dAtA[i] = 0x52
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.InChannel != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.InChannel.Size()))
		n14, err := m.InChannel.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x60
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.OnlineAt))
	dAtA[i] = 0x68
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.OfflineAt))
	dAtA[i] = 0x72
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(len(m.RecommandNote)))
	i += copy(dAtA[i:], m.RecommandNote)
	return i, nil
}

func (m *ListCityUserFilter) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListCityUserFilter) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x10
	i++
	if m.Online {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.InChannel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ListCityUserNearReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListCityUserNearReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseReq.Size()))
		n15, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x10
	i++
	if m.ValidLocation {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.UserLocation != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.UserLocation.Size()))
		n16, err := m.UserLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	if m.Filter != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.Filter.Size()))
		n17, err := m.Filter.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.Offset))
	return i, nil
}

func (m *ListCityUserNearResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListCityUserNearResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseResp.Size()))
		n18, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	if len(m.UserList) > 0 {
		for _, msg := range m.UserList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintLbs_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.NextOffset))
	return i, nil
}

func (m *FindFriendFilter) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendFilter) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x10
	i++
	if m.IsOnline {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.IsInChannel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetFindFriendListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindFriendListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	if m.Filter != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.Filter.Size()))
		n20, err := m.Filter.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.Offset))
	return i, nil
}

func (m *GetFindFriendListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFindFriendListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLbs_(dAtA, i, uint64(m.BaseResp.Size()))
		n21, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	if len(m.UserList) > 0 {
		for _, msg := range m.UserList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintLbs_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintLbs_(dAtA, i, uint64(m.NextOffset))
	return i, nil
}

func encodeFixed64Lbs_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Lbs_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintLbs_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ReportUserGeoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 9
	n += 9
	return n
}

func (m *ReportUserGeoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	return n
}

func (m *LbsNearUser) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLbs_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovLbs_(uint64(l))
	l = len(m.Ttid)
	n += 1 + l + sovLbs_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovLbs_(uint64(l))
	n += 1 + sovLbs_(uint64(m.Sex))
	l = len(m.Signature)
	n += 1 + l + sovLbs_(uint64(l))
	n += 9
	n += 1 + sovLbs_(uint64(m.LastTime))
	n += 1 + sovLbs_(uint64(m.ChannelId))
	n += 1 + sovLbs_(uint64(m.ChannelType))
	n += 2
	return n
}

func (m *GetNearUserListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 9
	n += 9
	n += 9
	n += 1 + sovLbs_(uint64(m.Count))
	n += 1 + sovLbs_(uint64(m.BeginUid))
	n += 1 + sovLbs_(uint64(m.FilterSex))
	return n
}

func (m *GetNearUserListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	if len(m.UserList) > 0 {
		for _, e := range m.UserList {
			l = e.Size()
			n += 1 + l + sovLbs_(uint64(l))
		}
	}
	return n
}

func (m *ModifyUserGeoSwitchReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 2
	return n
}

func (m *ModifyUserGeoSwitchResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 2
	return n
}

func (m *GetUserGeoSwitchReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	return n
}

func (m *GetUserGeoSwitchResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 2
	return n
}

func (m *LocationCity) Size() (n int) {
	var l int
	_ = l
	l = len(m.Country)
	n += 1 + l + sovLbs_(uint64(l))
	l = len(m.Province)
	n += 1 + l + sovLbs_(uint64(l))
	l = len(m.City)
	n += 1 + l + sovLbs_(uint64(l))
	return n
}

func (m *UserLocation) Size() (n int) {
	var l int
	_ = l
	n += 9
	n += 9
	if m.City != nil {
		l = m.City.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	return n
}

func (m *ReportUserLocationReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 2
	n += 2
	if m.UserLocation != nil {
		l = m.UserLocation.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	return n
}

func (m *ReportUserLocationResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	return n
}

func (m *CityUserNearChannelInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLbs_(uint64(m.ChannelId))
	n += 1 + sovLbs_(uint64(m.ChannelType))
	l = len(m.FindPlayingText)
	n += 1 + l + sovLbs_(uint64(l))
	l = len(m.FindPlayingImg)
	n += 1 + l + sovLbs_(uint64(l))
	return n
}

func (m *CityUserNear) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLbs_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovLbs_(uint64(l))
	l = len(m.Alias)
	n += 1 + l + sovLbs_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovLbs_(uint64(l))
	n += 1 + sovLbs_(uint64(m.Sex))
	l = len(m.FaceMd5)
	n += 1 + l + sovLbs_(uint64(l))
	n += 9
	if m.City != nil {
		l = m.City.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 1 + sovLbs_(uint64(m.LastTime))
	if len(m.GameTagGameList) > 0 {
		for _, s := range m.GameTagGameList {
			l = len(s)
			n += 1 + l + sovLbs_(uint64(l))
		}
	}
	if m.InChannel != nil {
		l = m.InChannel.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 1 + sovLbs_(uint64(m.OnlineAt))
	n += 1 + sovLbs_(uint64(m.OfflineAt))
	l = len(m.RecommandNote)
	n += 1 + l + sovLbs_(uint64(l))
	return n
}

func (m *ListCityUserFilter) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLbs_(uint64(m.Sex))
	n += 2
	n += 2
	return n
}

func (m *ListCityUserNearReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 2
	if m.UserLocation != nil {
		l = m.UserLocation.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	if m.Filter != nil {
		l = m.Filter.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 1 + sovLbs_(uint64(m.Offset))
	return n
}

func (m *ListCityUserNearResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	if len(m.UserList) > 0 {
		for _, e := range m.UserList {
			l = e.Size()
			n += 1 + l + sovLbs_(uint64(l))
		}
	}
	n += 1 + sovLbs_(uint64(m.NextOffset))
	return n
}

func (m *FindFriendFilter) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLbs_(uint64(m.Sex))
	n += 2
	n += 2
	return n
}

func (m *GetFindFriendListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	if m.Filter != nil {
		l = m.Filter.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	n += 1 + sovLbs_(uint64(m.Offset))
	return n
}

func (m *GetFindFriendListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovLbs_(uint64(l))
	}
	if len(m.UserList) > 0 {
		for _, e := range m.UserList {
			l = e.Size()
			n += 1 + l + sovLbs_(uint64(l))
		}
	}
	n += 1 + sovLbs_(uint64(m.NextOffset))
	return n
}

func sovLbs_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozLbs_(x uint64) (n int) {
	return sovLbs_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ReportUserGeoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportUserGeoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportUserGeoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Longitude", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Longitude = float64(math4.Float64frombits(v))
		case 3:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Latitude", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Latitude = float64(math4.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportUserGeoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportUserGeoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportUserGeoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LbsNearUser) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LbsNearUser: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LbsNearUser: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ttid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Signature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Signature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Distance", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Distance = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastTime", wireType)
			}
			m.LastTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIsPwd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ChannelIsPwd = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("ttid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("signature")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("distance")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNearUserListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNearUserListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNearUserListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Longitude", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Longitude = float64(math4.Float64frombits(v))
		case 3:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Latitude", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Latitude = float64(math4.Float64frombits(v))
		case 4:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinDistance", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.MinDistance = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000002)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginUid", wireType)
			}
			m.BeginUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FilterSex", wireType)
			}
			m.FilterSex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FilterSex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("min_distance")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("begin_uid")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("filter_sex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetNearUserListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetNearUserListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetNearUserListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserList = append(m.UserList, &LbsNearUser{})
			if err := m.UserList[len(m.UserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyUserGeoSwitchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyUserGeoSwitchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyUserGeoSwitchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisableGeo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DisableGeo = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("disable_geo")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyUserGeoSwitchResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ModifyUserGeoSwitchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ModifyUserGeoSwitchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisableGeo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DisableGeo = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("disable_geo")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserGeoSwitchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserGeoSwitchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserGeoSwitchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserGeoSwitchResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserGeoSwitchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserGeoSwitchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisableGeo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DisableGeo = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("disable_geo")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LocationCity) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LocationCity: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LocationCity: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Country", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Country = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Province", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Province = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.City = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLocation) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLocation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLocation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Longitude", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Longitude = float64(math4.Float64frombits(v))
		case 2:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Latitude", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Latitude = float64(math4.Float64frombits(v))
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.City == nil {
				m.City = &LocationCity{}
			}
			if err := m.City.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportUserLocationReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportUserLocationReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportUserLocationReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CanAccessLocation", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanAccessLocation = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidLocation", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ValidLocation = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserLocation == nil {
				m.UserLocation = &UserLocation{}
			}
			if err := m.UserLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("can_access_location")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("valid_location")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportUserLocationResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportUserLocationResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportUserLocationResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CityUserNearChannelInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CityUserNearChannelInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CityUserNearChannelInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindPlayingText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FindPlayingText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FindPlayingImg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FindPlayingImg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CityUserNear) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CityUserNear: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CityUserNear: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Distance", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.Distance = float64(math4.Float64frombits(v))
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.City == nil {
				m.City = &LocationCity{}
			}
			if err := m.City.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastTime", wireType)
			}
			m.LastTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameTagGameList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameTagGameList = append(m.GameTagGameList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InChannel", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.InChannel == nil {
				m.InChannel = &CityUserNearChannelInfo{}
			}
			if err := m.InChannel.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineAt", wireType)
			}
			m.OnlineAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfflineAt", wireType)
			}
			m.OfflineAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfflineAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommandNote", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommandNote = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("alias")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("face_md5")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListCityUserFilter) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ListCityUserFilter: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ListCityUserFilter: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Online", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Online = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InChannel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.InChannel = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListCityUserNearReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ListCityUserNearReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ListCityUserNearReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidLocation", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ValidLocation = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserLocation == nil {
				m.UserLocation = &UserLocation{}
			}
			if err := m.UserLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Filter", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Filter == nil {
				m.Filter = &ListCityUserFilter{}
			}
			if err := m.Filter.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("valid_location")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListCityUserNearResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ListCityUserNearResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ListCityUserNearResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserList = append(m.UserList, &CityUserNear{})
			if err := m.UserList[len(m.UserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NextOffset", wireType)
			}
			m.NextOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NextOffset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendFilter) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendFilter: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendFilter: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOnline", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOnline = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsInChannel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsInChannel = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindFriendListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindFriendListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindFriendListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Filter", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Filter == nil {
				m.Filter = &FindFriendFilter{}
			}
			if err := m.Filter.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFindFriendListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFindFriendListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFindFriendListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLbs_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserList = append(m.UserList, &CityUserNear{})
			if err := m.UserList[len(m.UserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NextOffset", wireType)
			}
			m.NextOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NextOffset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLbs_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLbs_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipLbs_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowLbs_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLbs_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthLbs_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowLbs_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipLbs_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthLbs_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowLbs_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("lbs_.proto", fileDescriptorLbs_) }

var fileDescriptorLbs_ = []byte{
	// 1251 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0xcd, 0x6f, 0x1b, 0x45,
	0x14, 0xef, 0xae, 0xf3, 0x61, 0x3f, 0xdb, 0x89, 0xbb, 0x49, 0xd3, 0x55, 0x41, 0xa9, 0xbb, 0x7c,
	0x19, 0x5a, 0x5c, 0x14, 0x91, 0x0b, 0x12, 0x48, 0xe9, 0x47, 0xa2, 0x48, 0xee, 0x87, 0xdc, 0xf4,
	0xc2, 0x65, 0x34, 0xde, 0x1d, 0x6f, 0x87, 0xee, 0xce, 0xb8, 0x9e, 0x71, 0x6b, 0x73, 0xe2, 0xc4,
	0x81, 0x0b, 0xdc, 0xe0, 0x1f, 0x42, 0x2a, 0x37, 0xae, 0x5c, 0x10, 0x2a, 0x7f, 0x00, 0xff, 0x00,
	0x07, 0x34, 0xb3, 0xb3, 0xde, 0x71, 0xec, 0xd2, 0xb8, 0x08, 0xc4, 0x29, 0xeb, 0xdf, 0x7b, 0x33,
	0xf3, 0xde, 0xef, 0xfd, 0xe6, 0xbd, 0x09, 0x40, 0xd2, 0x13, 0xa8, 0x3d, 0x18, 0x72, 0xc9, 0x3d,
	0x37, 0xc6, 0x97, 0xea, 0x31, 0x46, 0x3d, 0x2c, 0x48, 0x06, 0x05, 0x5f, 0x39, 0xd0, 0xe8, 0x92,
	0x01, 0x1f, 0xca, 0x87, 0x82, 0x0c, 0x8f, 0x08, 0xef, 0x92, 0x27, 0xde, 0xbb, 0x50, 0x56, 0x2e,
	0x68, 0x48, 0x9e, 0xf8, 0x4e, 0xd3, 0x6d, 0x55, 0xf7, 0xaa, 0xed, 0x18, 0xb7, 0x6f, 0x60, 0x41,
	0xba, 0xe4, 0x49, 0x77, 0xbd, 0x97, 0x7d, 0x78, 0x01, 0x54, 0x12, 0xce, 0x62, 0x2a, 0x47, 0x11,
	0xf1, 0xdd, 0xa6, 0xd3, 0x72, 0x6e, 0xac, 0x3c, 0xff, 0xf5, 0xf2, 0xb9, 0x6e, 0x01, 0x7b, 0x4d,
	0x28, 0x27, 0x58, 0x66, 0x2e, 0x25, 0xcb, 0x65, 0x8a, 0x06, 0x9f, 0xc1, 0xf9, 0x53, 0x11, 0x88,
	0x81, 0xf7, 0x3e, 0x54, 0x4c, 0x08, 0x62, 0x60, 0x62, 0xa8, 0x15, 0x31, 0x88, 0x41, 0xb7, 0xdc,
	0x33, 0x5f, 0xc1, 0x9f, 0x2e, 0x54, 0x3b, 0x3d, 0x71, 0x97, 0xe0, 0xa1, 0xda, 0xc1, 0xdb, 0x81,
	0xd2, 0x88, 0x46, 0x7a, 0x51, 0xdd, 0x1c, 0xa6, 0x00, 0x6f, 0x17, 0xd6, 0x71, 0x18, 0xf2, 0x11,
	0x93, 0xbe, 0xdb, 0x74, 0x5b, 0x15, 0x63, 0xcb, 0x41, 0xcf, 0x87, 0x15, 0x29, 0x69, 0xe4, 0x97,
	0x2c, 0xa3, 0x46, 0x54, 0x0e, 0x8c, 0x86, 0x8f, 0x19, 0x4e, 0x89, 0xbf, 0x62, 0x59, 0xa7, 0xa8,
	0x3a, 0x53, 0x90, 0xb1, 0xbf, 0xda, 0x74, 0x5b, 0xab, 0xf9, 0x99, 0x82, 0x8c, 0x15, 0x43, 0x82,
	0xc6, 0x0c, 0xcb, 0xd1, 0x90, 0xf8, 0x6b, 0xd6, 0xd2, 0x02, 0x56, 0xbb, 0x47, 0x54, 0x48, 0xcc,
	0x42, 0xe2, 0xaf, 0x37, 0xdd, 0x82, 0xa1, 0x1c, 0xf5, 0xae, 0x40, 0x25, 0xc1, 0x42, 0x22, 0x49,
	0x53, 0xe2, 0x97, 0xad, 0xbc, 0xca, 0x0a, 0x3e, 0xa1, 0x29, 0xf1, 0xde, 0x02, 0x08, 0x1f, 0x61,
	0xc6, 0x48, 0x82, 0x68, 0xe4, 0x57, 0x9a, 0xce, 0xd4, 0xa7, 0x62, 0xf0, 0xe3, 0xc8, 0x7b, 0x0f,
	0x6a, 0xb9, 0x93, 0x9c, 0x0c, 0x88, 0x0f, 0x96, 0x5b, 0xd5, 0x58, 0x4e, 0x26, 0x03, 0xe2, 0x7d,
	0x00, 0x1b, 0xd3, 0xdd, 0x04, 0x1a, 0x3c, 0x8b, 0xfc, 0x6a, 0xd3, 0x69, 0x95, 0x8d, 0x6b, 0xbe,
	0xc9, 0xb1, 0xb8, 0xff, 0x2c, 0x0a, 0xbe, 0x75, 0xc1, 0x3b, 0x22, 0x32, 0xa7, 0xbf, 0x43, 0x85,
	0xfc, 0xcf, 0x35, 0xa4, 0x32, 0x4b, 0x29, 0x43, 0x53, 0x1e, 0x57, 0x2c, 0x1e, 0xab, 0x29, 0x65,
	0xb7, 0x72, 0x2a, 0x2f, 0xc1, 0x6a, 0x26, 0x81, 0x55, 0x8b, 0xc6, 0x0c, 0x52, 0x34, 0xf7, 0x48,
	0x4c, 0x19, 0x52, 0xf2, 0x59, 0xb3, 0x69, 0xd6, 0xf0, 0x43, 0x1a, 0x29, 0x9a, 0xfb, 0x34, 0x91,
	0x64, 0x88, 0x54, 0xb9, 0xd7, 0xad, 0x72, 0x57, 0x32, 0xfc, 0x01, 0x19, 0x07, 0x0c, 0xb6, 0xe6,
	0x08, 0x59, 0x4a, 0xd2, 0xde, 0x35, 0xa8, 0x8c, 0x04, 0x19, 0xa2, 0x84, 0x0a, 0x25, 0xd6, 0x52,
	0xab, 0xba, 0xb7, 0xa9, 0x5c, 0x2d, 0x99, 0x77, 0xcb, 0x23, 0xb3, 0x79, 0x10, 0xc3, 0xce, 0x1d,
	0x1e, 0xd1, 0xfe, 0xc4, 0x5c, 0xa0, 0x07, 0xcf, 0xa8, 0x0c, 0x1f, 0x2d, 0x53, 0x84, 0x77, 0xa0,
	0x1a, 0x51, 0x81, 0x7b, 0x09, 0x41, 0x31, 0xe1, 0xfa, 0x7a, 0xe4, 0xc5, 0x06, 0x63, 0x38, 0x22,
	0x3c, 0x78, 0x0c, 0x17, 0x17, 0x1e, 0xb4, 0x5c, 0x72, 0x67, 0x3c, 0xec, 0x53, 0xcd, 0xe2, 0xeb,
	0xa6, 0x14, 0x3c, 0x82, 0xed, 0xf9, 0xe5, 0xff, 0x4a, 0xa0, 0x5f, 0x40, 0xad, 0xc3, 0x43, 0x2c,
	0x29, 0x67, 0x37, 0xa9, 0x9c, 0xa8, 0x3e, 0xa3, 0xf5, 0x34, 0x9c, 0xf8, 0x4e, 0xd3, 0x29, 0xfa,
	0x8c, 0x01, 0x95, 0x9a, 0x07, 0x43, 0xfe, 0x94, 0x2a, 0x9d, 0xba, 0x96, 0xc3, 0x14, 0x55, 0x9d,
	0x28, 0xa4, 0x72, 0xa2, 0xb5, 0x3e, 0xed, 0x44, 0x0a, 0x09, 0xbe, 0x84, 0x9a, 0xd6, 0x94, 0x39,
	0x6f, 0xf6, 0xf6, 0x38, 0xaf, 0xbe, 0x3d, 0xee, 0xc2, 0xdb, 0xf3, 0xb6, 0x75, 0x5e, 0x75, 0xaf,
	0xa1, 0x95, 0x66, 0x65, 0x64, 0xce, 0xfe, 0xc5, 0x81, 0x0b, 0x45, 0xa3, 0xce, 0x1d, 0x96, 0x91,
	0xd9, 0xc7, 0xb0, 0x15, 0x62, 0x86, 0x70, 0x18, 0x12, 0x21, 0x50, 0x62, 0x76, 0x98, 0x21, 0xf6,
	0x7c, 0x88, 0xd9, 0x81, 0xb6, 0x4f, 0x73, 0xbc, 0x0a, 0x1b, 0x4f, 0x71, 0x42, 0xa3, 0x62, 0x41,
	0xc9, 0x5a, 0x50, 0xd7, 0xb6, 0xa9, 0xf3, 0x3e, 0xd4, 0xb3, 0x9b, 0x93, 0xfb, 0xae, 0x14, 0x39,
	0xcd, 0x84, 0x5d, 0x1b, 0x59, 0xbf, 0x82, 0x9b, 0xb0, 0xb3, 0x28, 0xb5, 0xe5, 0x06, 0xd1, 0x4f,
	0x0e, 0x5c, 0x54, 0x7c, 0xa9, 0x3d, 0xd4, 0x35, 0xbd, 0x69, 0xda, 0x24, 0xeb, 0xf3, 0x53, 0xfd,
	0xd9, 0x39, 0x5b, 0x7f, 0x76, 0x5f, 0xd6, 0x9f, 0x3f, 0x82, 0xf3, 0x7d, 0xca, 0x22, 0x34, 0x48,
	0xf0, 0x84, 0xb2, 0x18, 0x49, 0x32, 0x96, 0x33, 0x6a, 0xd9, 0x54, 0xe6, 0xfb, 0x99, 0xf5, 0x84,
	0x8c, 0xa5, 0xd7, 0x86, 0xc6, 0xcc, 0x0a, 0x9a, 0xc6, 0x9a, 0x9a, 0x7c, 0xc1, 0x86, 0xb5, 0xe0,
	0x38, 0x8d, 0x83, 0xaf, 0x57, 0xa0, 0x66, 0xe7, 0xf2, 0xda, 0x53, 0xf5, 0x12, 0xac, 0xe2, 0x84,
	0x62, 0x31, 0x33, 0x56, 0x33, 0xe8, 0x1f, 0xcc, 0xd5, 0xcb, 0x50, 0xee, 0xe3, 0x90, 0xa0, 0x34,
	0xda, 0x9f, 0x19, 0xab, 0xeb, 0x0a, 0xbd, 0x13, 0xed, 0x9f, 0x1a, 0xaa, 0xce, 0x82, 0xa1, 0x9a,
	0x8b, 0xbe, 0xfc, 0x77, 0xa2, 0x9f, 0x1d, 0xbd, 0xf6, 0x58, 0x2d, 0x46, 0xef, 0x55, 0xf0, 0x62,
	0x9c, 0x12, 0x24, 0x71, 0x8c, 0xf4, 0x87, 0xee, 0xda, 0xd0, 0x2c, 0xb5, 0x2a, 0xdd, 0x4d, 0x05,
	0x9c, 0xe0, 0xf8, 0x08, 0xa7, 0x44, 0xf5, 0x6a, 0xef, 0x13, 0x00, 0xca, 0x90, 0xa9, 0xa5, 0x9e,
	0xaa, 0xd5, 0xbd, 0x37, 0xd4, 0xd9, 0x2f, 0x11, 0x4e, 0xb7, 0x42, 0x99, 0xf9, 0xa9, 0x62, 0xe1,
	0x2c, 0xa1, 0x8c, 0x20, 0x2c, 0xfd, 0x9a, 0x1d, 0x4b, 0x06, 0x1f, 0x48, 0x25, 0x33, 0xde, 0xef,
	0xe7, 0x3e, 0x75, 0x5b, 0x66, 0x06, 0x3f, 0x90, 0xea, 0x42, 0x0d, 0x49, 0xc8, 0xd3, 0x14, 0xb3,
	0x08, 0x31, 0x2e, 0x89, 0xbf, 0x61, 0x29, 0xa1, 0x3e, 0xb5, 0xdd, 0xe5, 0x92, 0x04, 0x1c, 0x3c,
	0x15, 0x78, 0x1e, 0xde, 0xa1, 0x9e, 0x72, 0x79, 0x5d, 0x94, 0x8e, 0x67, 0xea, 0xf2, 0x26, 0xac,
	0x65, 0xb1, 0x68, 0xed, 0xe6, 0x77, 0xd4, 0x60, 0x2a, 0x3a, 0x2b, 0xf9, 0x92, 0xe5, 0x51, 0x64,
	0x19, 0xfc, 0xe1, 0xc0, 0x96, 0x7d, 0xa2, 0x22, 0x64, 0x99, 0x26, 0x33, 0xdf, 0x2e, 0xdc, 0x25,
	0xda, 0x45, 0xe9, 0x2c, 0xed, 0xc2, 0x6b, 0xc3, 0x5a, 0x36, 0xee, 0x4d, 0x7b, 0xd9, 0xd1, 0xea,
	0x99, 0xa3, 0xa9, 0x6b, 0xbc, 0x34, 0x2d, 0xfd, 0xbe, 0x20, 0xea, 0xd9, 0x51, 0x94, 0xc4, 0x60,
	0xc1, 0xf7, 0x0e, 0x6c, 0xcf, 0x67, 0xbc, 0xdc, 0xac, 0xfa, 0x70, 0xfe, 0xc5, 0xd0, 0x38, 0x2d,
	0xab, 0xe2, 0xc9, 0xa0, 0x46, 0x1b, 0x23, 0x63, 0x89, 0x4c, 0x54, 0x25, 0x2b, 0x2a, 0x50, 0x86,
	0x7b, 0x59, 0x64, 0x3f, 0x3a, 0xd0, 0x38, 0xa4, 0x2c, 0x3a, 0x1c, 0x52, 0xc2, 0xa2, 0x57, 0xd4,
	0xfe, 0x0a, 0x54, 0xa8, 0x40, 0x0b, 0xca, 0x5f, 0xa6, 0xe2, 0x5e, 0x26, 0x80, 0x16, 0xd4, 0xa9,
	0x40, 0x2f, 0xd1, 0x40, 0x95, 0x8a, 0xe3, 0xa9, 0x0a, 0x3a, 0xb0, 0xfe, 0x80, 0x8c, 0x75, 0xb3,
	0xdb, 0x01, 0xef, 0xf0, 0xf8, 0xee, 0x2d, 0x74, 0xd8, 0x3d, 0xbe, 0xad, 0xfe, 0xdc, 0xbe, 0x73,
	0xd0, 0xb9, 0xdd, 0x38, 0xe7, 0x6d, 0x43, 0xc3, 0xc6, 0x35, 0xea, 0x78, 0x5b, 0xb0, 0x69, 0xa3,
	0x07, 0x9d, 0x4e, 0xc3, 0x0d, 0xbe, 0x71, 0xf4, 0x6b, 0xa0, 0x48, 0x65, 0xd9, 0x57, 0xea, 0xb5,
	0x69, 0xc1, 0x5d, 0x5d, 0xf0, 0x6d, 0xe5, 0x75, 0x9a, 0x99, 0x05, 0xe5, 0x2e, 0x2d, 0x28, 0xf7,
	0x0f, 0x0e, 0x5c, 0x58, 0x10, 0xcc, 0xff, 0xa0, 0xde, 0x37, 0x3a, 0xcf, 0x5f, 0xec, 0x3a, 0x3f,
	0xbf, 0xd8, 0x75, 0x7e, 0x7b, 0xb1, 0xeb, 0x7c, 0xf7, 0xfb, 0xee, 0x39, 0xf0, 0x43, 0x9e, 0xb6,
	0x27, 0x74, 0xc2, 0x47, 0x6a, 0xf7, 0x94, 0x47, 0x24, 0xc9, 0xfe, 0x73, 0xfc, 0xbc, 0x19, 0xf3,
	0x04, 0xb3, 0xb8, 0xbd, 0xbf, 0x27, 0x65, 0x3b, 0xe4, 0xe9, 0x75, 0x0d, 0x87, 0x3c, 0xb9, 0x8e,
	0x07, 0x83, 0xeb, 0x49, 0x4f, 0xfc, 0x15, 0x00, 0x00, 0xff, 0xff, 0x1e, 0xdb, 0x63, 0xc7, 0x7b,
	0x0e, 0x00, 0x00,
}
