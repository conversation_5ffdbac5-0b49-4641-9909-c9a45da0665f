// Code generated by protoc-gen-gogo.
// source: src/proto/pbfile/circle2_.proto
// DO NOT EDIT!

/*
	Package circle is a generated protocol buffer package.

	It is generated from these files:
		src/proto/pbfile/circle2_.proto

	It has these top-level messages:
		Circle<PERSON>eeper
		CircleTopicImage
		CircleTopic
		CircleCommentImage
		CircleTopicComment
		CircleDynamicData
		CircleGetListReq
		CircleGetListResp
		CircleJoinReq
		CircleJoinResp
		CircleQuitReq
		CircleQuitResp
		CircleGetTopicListReq
		CircleGetTopicListResp
		CircleGetTopicReq
		CircleGetTopicResp
		CirclePostTopicReq
		CirclePostTopicResp
		CircleGetCommentListReq
		CircleGetCommentListResp
		CirclePostCommentReq
		CirclePostCommentResp
		CircleLikeTopicReq
		CircleLikeTopicResp
		CircleReportTopicReq
		CircleReportTopicResp
		CircleDeleteTopicReq
		CircleDeleteTopicResp
		CircleDeleteCommentReq
		CircleDeleteCommentResp
		CircleGetCircleDetailReq
		CircleGetCircleDetailResp
		CircleGetUserTopicReq
		CircleGetUserTopicResp
		CircleGetLikeUserListReq
		CircleGetLikeUserListResp
		CircleMarkReadedReq
		CircleMarkReadedResp
		CircleMuteUserReq
		CircleMuteUserResp
		CircleUnmuteUserReq
		CircleUnmuteUserResp
		MyCircleOrderReq
		MyCircleOrderResp
		CircleHighlightTopicReq
		CircleHighlightTopicResp
		CircleCancelHighlightTopicReq
		CircleCancelHighlightTopicResp
		CircleManagerDeleteCommentReq
		CircleManagerDeleteCommentResp
		CircleCheckUpdateReq
		CircleCheckUpdateResp
		CircleActivityDetail
		CircleGetActivityListReq
		CircleGetActivityListResp
		CircleGetHotReq
		CircleGetHotResp
		StCommentReplayTargetBase
		StCommentBase
		CircleTopicNomalComment
		CircleGetNomalCommentListReq
		CircleGetNomalCommentListResp
		CircleGetCommentReplyListReq
		CircleGetCommentReplyListResp
*/
package circle

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type CircleListRequestType int32

const (
	CircleListRequestType_REQ_TYPE_RECOMMEND         CircleListRequestType = 1
	CircleListRequestType_REQ_TYPE_MY_CIRCLES        CircleListRequestType = 2
	CircleListRequestType_REQ_TYPE_NEW_GAME          CircleListRequestType = 3
	CircleListRequestType_REQ_TYPE_MY_VISIT_RECENTLY CircleListRequestType = 4
)

var CircleListRequestType_name = map[int32]string{
	1: "REQ_TYPE_RECOMMEND",
	2: "REQ_TYPE_MY_CIRCLES",
	3: "REQ_TYPE_NEW_GAME",
	4: "REQ_TYPE_MY_VISIT_RECENTLY",
}
var CircleListRequestType_value = map[string]int32{
	"REQ_TYPE_RECOMMEND":         1,
	"REQ_TYPE_MY_CIRCLES":        2,
	"REQ_TYPE_NEW_GAME":          3,
	"REQ_TYPE_MY_VISIT_RECENTLY": 4,
}

func (x CircleListRequestType) Enum() *CircleListRequestType {
	p := new(CircleListRequestType)
	*p = x
	return p
}
func (x CircleListRequestType) String() string {
	return proto.EnumName(CircleListRequestType_name, int32(x))
}
func (x *CircleListRequestType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CircleListRequestType_value, data, "CircleListRequestType")
	if err != nil {
		return err
	}
	*x = CircleListRequestType(value)
	return nil
}
func (CircleListRequestType) EnumDescriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{0} }

type CircleTopicListReqType int32

const (
	CircleTopicListReqType_REQ_TYPE_HOT_TOPICS         CircleTopicListReqType = 1
	CircleTopicListReqType_REQ_TYPE_NEW_TOPICS         CircleTopicListReqType = 2
	CircleTopicListReqType_REQ_TYPE_CONTACT_TOPICS     CircleTopicListReqType = 3
	CircleTopicListReqType_REQ_TYPE_LAST_UPDATE_TOPICS CircleTopicListReqType = 4
	CircleTopicListReqType_REQ_TYPE_HIGHLIGHT_TOPICS   CircleTopicListReqType = 5
)

var CircleTopicListReqType_name = map[int32]string{
	1: "REQ_TYPE_HOT_TOPICS",
	2: "REQ_TYPE_NEW_TOPICS",
	3: "REQ_TYPE_CONTACT_TOPICS",
	4: "REQ_TYPE_LAST_UPDATE_TOPICS",
	5: "REQ_TYPE_HIGHLIGHT_TOPICS",
}
var CircleTopicListReqType_value = map[string]int32{
	"REQ_TYPE_HOT_TOPICS":         1,
	"REQ_TYPE_NEW_TOPICS":         2,
	"REQ_TYPE_CONTACT_TOPICS":     3,
	"REQ_TYPE_LAST_UPDATE_TOPICS": 4,
	"REQ_TYPE_HIGHLIGHT_TOPICS":   5,
}

func (x CircleTopicListReqType) Enum() *CircleTopicListReqType {
	p := new(CircleTopicListReqType)
	*p = x
	return p
}
func (x CircleTopicListReqType) String() string {
	return proto.EnumName(CircleTopicListReqType_name, int32(x))
}
func (x *CircleTopicListReqType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CircleTopicListReqType_value, data, "CircleTopicListReqType")
	if err != nil {
		return err
	}
	*x = CircleTopicListReqType(value)
	return nil
}
func (CircleTopicListReqType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{1}
}

type CircleTopicCommentStatus int32

const (
	CircleTopicCommentStatus_COMMENT_STATUS_NORMAL  CircleTopicCommentStatus = 0
	CircleTopicCommentStatus_COMMENT_STATUS_DELETED CircleTopicCommentStatus = 1
	CircleTopicCommentStatus_COMMENT_STATUS_SHIELD  CircleTopicCommentStatus = 2
)

var CircleTopicCommentStatus_name = map[int32]string{
	0: "COMMENT_STATUS_NORMAL",
	1: "COMMENT_STATUS_DELETED",
	2: "COMMENT_STATUS_SHIELD",
}
var CircleTopicCommentStatus_value = map[string]int32{
	"COMMENT_STATUS_NORMAL":  0,
	"COMMENT_STATUS_DELETED": 1,
	"COMMENT_STATUS_SHIELD":  2,
}

func (x CircleTopicCommentStatus) Enum() *CircleTopicCommentStatus {
	p := new(CircleTopicCommentStatus)
	*p = x
	return p
}
func (x CircleTopicCommentStatus) String() string {
	return proto.EnumName(CircleTopicCommentStatus_name, int32(x))
}
func (x *CircleTopicCommentStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CircleTopicCommentStatus_value, data, "CircleTopicCommentStatus")
	if err != nil {
		return err
	}
	*x = CircleTopicCommentStatus(value)
	return nil
}
func (CircleTopicCommentStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{2}
}

type CircleTopicCommentType int32

const (
	CircleTopicCommentType_COMMENT_TYPE_NORMAL      CircleTopicCommentType = 0
	CircleTopicCommentType_COMMENT_TYPE_REPLY       CircleTopicCommentType = 1
	CircleTopicCommentType_COMMENT_TYPE_REPLY_REPLY CircleTopicCommentType = 2
	CircleTopicCommentType_COMMENT_TYPE_HAVE_REPLY  CircleTopicCommentType = 4
)

var CircleTopicCommentType_name = map[int32]string{
	0: "COMMENT_TYPE_NORMAL",
	1: "COMMENT_TYPE_REPLY",
	2: "COMMENT_TYPE_REPLY_REPLY",
	4: "COMMENT_TYPE_HAVE_REPLY",
}
var CircleTopicCommentType_value = map[string]int32{
	"COMMENT_TYPE_NORMAL":      0,
	"COMMENT_TYPE_REPLY":       1,
	"COMMENT_TYPE_REPLY_REPLY": 2,
	"COMMENT_TYPE_HAVE_REPLY":  4,
}

func (x CircleTopicCommentType) Enum() *CircleTopicCommentType {
	p := new(CircleTopicCommentType)
	*p = x
	return p
}
func (x CircleTopicCommentType) String() string {
	return proto.EnumName(CircleTopicCommentType_name, int32(x))
}
func (x *CircleTopicCommentType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CircleTopicCommentType_value, data, "CircleTopicCommentType")
	if err != nil {
		return err
	}
	*x = CircleTopicCommentType(value)
	return nil
}
func (CircleTopicCommentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{3}
}

type CircleKeeper_KEEPR_TYPE int32

const (
	CircleKeeper_MASTER CircleKeeper_KEEPR_TYPE = 1
	CircleKeeper_VICE   CircleKeeper_KEEPR_TYPE = 2
)

var CircleKeeper_KEEPR_TYPE_name = map[int32]string{
	1: "MASTER",
	2: "VICE",
}
var CircleKeeper_KEEPR_TYPE_value = map[string]int32{
	"MASTER": 1,
	"VICE":   2,
}

func (x CircleKeeper_KEEPR_TYPE) Enum() *CircleKeeper_KEEPR_TYPE {
	p := new(CircleKeeper_KEEPR_TYPE)
	*p = x
	return p
}
func (x CircleKeeper_KEEPR_TYPE) String() string {
	return proto.EnumName(CircleKeeper_KEEPR_TYPE_name, int32(x))
}
func (x *CircleKeeper_KEEPR_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CircleKeeper_KEEPR_TYPE_value, data, "CircleKeeper_KEEPR_TYPE")
	if err != nil {
		return err
	}
	*x = CircleKeeper_KEEPR_TYPE(value)
	return nil
}
func (CircleKeeper_KEEPR_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{0, 0}
}

type CircleTopic_TOPIC_TYPE int32

const (
	CircleTopic_NORMAL    CircleTopic_TOPIC_TYPE = 0
	CircleTopic_CUNZAIGAN CircleTopic_TOPIC_TYPE = 1
	CircleTopic_OFFICIAL  CircleTopic_TOPIC_TYPE = 2
)

var CircleTopic_TOPIC_TYPE_name = map[int32]string{
	0: "NORMAL",
	1: "CUNZAIGAN",
	2: "OFFICIAL",
}
var CircleTopic_TOPIC_TYPE_value = map[string]int32{
	"NORMAL":    0,
	"CUNZAIGAN": 1,
	"OFFICIAL":  2,
}

func (x CircleTopic_TOPIC_TYPE) Enum() *CircleTopic_TOPIC_TYPE {
	p := new(CircleTopic_TOPIC_TYPE)
	*p = x
	return p
}
func (x CircleTopic_TOPIC_TYPE) String() string {
	return proto.EnumName(CircleTopic_TOPIC_TYPE_name, int32(x))
}
func (x *CircleTopic_TOPIC_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CircleTopic_TOPIC_TYPE_value, data, "CircleTopic_TOPIC_TYPE")
	if err != nil {
		return err
	}
	*x = CircleTopic_TOPIC_TYPE(value)
	return nil
}
func (CircleTopic_TOPIC_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{2, 0}
}

type CircleTopic_TOPIC_STATE int32

const (
	CircleTopic_STATE_HIGHT_LIGHT   CircleTopic_TOPIC_STATE = 1
	CircleTopic_STATE_STICK         CircleTopic_TOPIC_STATE = 2
	CircleTopic_STATE_ACTIVITY      CircleTopic_TOPIC_STATE = 4
	CircleTopic_STATE_GAME_DOWNLOAD CircleTopic_TOPIC_STATE = 8
)

var CircleTopic_TOPIC_STATE_name = map[int32]string{
	1: "STATE_HIGHT_LIGHT",
	2: "STATE_STICK",
	4: "STATE_ACTIVITY",
	8: "STATE_GAME_DOWNLOAD",
}
var CircleTopic_TOPIC_STATE_value = map[string]int32{
	"STATE_HIGHT_LIGHT":   1,
	"STATE_STICK":         2,
	"STATE_ACTIVITY":      4,
	"STATE_GAME_DOWNLOAD": 8,
}

func (x CircleTopic_TOPIC_STATE) Enum() *CircleTopic_TOPIC_STATE {
	p := new(CircleTopic_TOPIC_STATE)
	*p = x
	return p
}
func (x CircleTopic_TOPIC_STATE) String() string {
	return proto.EnumName(CircleTopic_TOPIC_STATE_name, int32(x))
}
func (x *CircleTopic_TOPIC_STATE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CircleTopic_TOPIC_STATE_value, data, "CircleTopic_TOPIC_STATE")
	if err != nil {
		return err
	}
	*x = CircleTopic_TOPIC_STATE(value)
	return nil
}
func (CircleTopic_TOPIC_STATE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{2, 1}
}

type CircleTopicComment_TopicCommentStatus int32

const (
	CircleTopicComment_NORMAL  CircleTopicComment_TopicCommentStatus = 0
	CircleTopicComment_DELETED CircleTopicComment_TopicCommentStatus = 1
	CircleTopicComment_SHIELD  CircleTopicComment_TopicCommentStatus = 2
)

var CircleTopicComment_TopicCommentStatus_name = map[int32]string{
	0: "NORMAL",
	1: "DELETED",
	2: "SHIELD",
}
var CircleTopicComment_TopicCommentStatus_value = map[string]int32{
	"NORMAL":  0,
	"DELETED": 1,
	"SHIELD":  2,
}

func (x CircleTopicComment_TopicCommentStatus) Enum() *CircleTopicComment_TopicCommentStatus {
	p := new(CircleTopicComment_TopicCommentStatus)
	*p = x
	return p
}
func (x CircleTopicComment_TopicCommentStatus) String() string {
	return proto.EnumName(CircleTopicComment_TopicCommentStatus_name, int32(x))
}
func (x *CircleTopicComment_TopicCommentStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CircleTopicComment_TopicCommentStatus_value, data, "CircleTopicComment_TopicCommentStatus")
	if err != nil {
		return err
	}
	*x = CircleTopicComment_TopicCommentStatus(value)
	return nil
}
func (CircleTopicComment_TopicCommentStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{4, 0}
}

// 圈主
type CircleKeeper struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Account    string `protobuf:"bytes,2,req,name=account" json:"account"`
	NickName   string `protobuf:"bytes,3,req,name=nick_name,json=nickName" json:"nick_name"`
	Sex        uint32 `protobuf:"varint,4,req,name=sex" json:"sex"`
	KeeperType uint32 `protobuf:"varint,5,req,name=keeper_type,json=keeperType" json:"keeper_type"`
}

func (m *CircleKeeper) Reset()                    { *m = CircleKeeper{} }
func (m *CircleKeeper) String() string            { return proto.CompactTextString(m) }
func (*CircleKeeper) ProtoMessage()               {}
func (*CircleKeeper) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{0} }

func (m *CircleKeeper) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CircleKeeper) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CircleKeeper) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *CircleKeeper) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *CircleKeeper) GetKeeperType() uint32 {
	if m != nil {
		return m.KeeperType
	}
	return 0
}

// 游戏圈主题配图
type CircleTopicImage struct {
	ThumbUrl    string `protobuf:"bytes,1,req,name=thumb_url,json=thumbUrl" json:"thumb_url"`
	ImageUrl    string `protobuf:"bytes,2,req,name=image_url,json=imageUrl" json:"image_url"`
	ImageWidth  uint32 `protobuf:"varint,3,opt,name=image_width,json=imageWidth" json:"image_width"`
	ImageHeight uint32 `protobuf:"varint,4,opt,name=image_height,json=imageHeight" json:"image_height"`
}

func (m *CircleTopicImage) Reset()                    { *m = CircleTopicImage{} }
func (m *CircleTopicImage) String() string            { return proto.CompactTextString(m) }
func (*CircleTopicImage) ProtoMessage()               {}
func (*CircleTopicImage) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{1} }

func (m *CircleTopicImage) GetThumbUrl() string {
	if m != nil {
		return m.ThumbUrl
	}
	return ""
}

func (m *CircleTopicImage) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *CircleTopicImage) GetImageWidth() uint32 {
	if m != nil {
		return m.ImageWidth
	}
	return 0
}

func (m *CircleTopicImage) GetImageHeight() uint32 {
	if m != nil {
		return m.ImageHeight
	}
	return 0
}

// 游戏圈主题
type CircleTopic struct {
	Type                uint32                          `protobuf:"varint,1,req,name=type" json:"type"`
	CircleId            uint32                          `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId             uint32                          `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Title               string                          `protobuf:"bytes,4,req,name=title" json:"title"`
	Content             string                          `protobuf:"bytes,5,req,name=content" json:"content"`
	CreateTime          uint32                          `protobuf:"varint,6,req,name=create_time,json=createTime" json:"create_time"`
	ImageList           []*CircleTopicImage             `protobuf:"bytes,7,rep,name=image_list,json=imageList" json:"image_list,omitempty"`
	LikeCount           uint32                          `protobuf:"varint,8,req,name=like_count,json=likeCount" json:"like_count"`
	CommentCount        uint32                          `protobuf:"varint,9,req,name=comment_count,json=commentCount" json:"comment_count"`
	IsLiked             uint32                          `protobuf:"varint,10,req,name=is_liked,json=isLiked" json:"is_liked"`
	TopicState          uint32                          `protobuf:"varint,11,req,name=topic_state,json=topicState" json:"topic_state"`
	LastCommentTime     uint32                          `protobuf:"varint,12,req,name=last_comment_time,json=lastCommentTime" json:"last_comment_time"`
	Creator             *ga.CircleUser                  `protobuf:"bytes,13,opt,name=creator" json:"creator,omitempty"`
	CreateTimeDesc      string                          `protobuf:"bytes,14,req,name=create_time_desc,json=createTimeDesc" json:"create_time_desc"`
	LastCommentTimeDesc string                          `protobuf:"bytes,15,req,name=last_comment_time_desc,json=lastCommentTimeDesc" json:"last_comment_time_desc"`
	DownloadInfo        *ga.CircleTopicGameDownloadInfo `protobuf:"bytes,16,opt,name=download_info,json=downloadInfo" json:"download_info,omitempty"`
}

func (m *CircleTopic) Reset()                    { *m = CircleTopic{} }
func (m *CircleTopic) String() string            { return proto.CompactTextString(m) }
func (*CircleTopic) ProtoMessage()               {}
func (*CircleTopic) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{2} }

func (m *CircleTopic) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CircleTopic) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleTopic) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleTopic) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CircleTopic) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CircleTopic) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *CircleTopic) GetImageList() []*CircleTopicImage {
	if m != nil {
		return m.ImageList
	}
	return nil
}

func (m *CircleTopic) GetLikeCount() uint32 {
	if m != nil {
		return m.LikeCount
	}
	return 0
}

func (m *CircleTopic) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *CircleTopic) GetIsLiked() uint32 {
	if m != nil {
		return m.IsLiked
	}
	return 0
}

func (m *CircleTopic) GetTopicState() uint32 {
	if m != nil {
		return m.TopicState
	}
	return 0
}

func (m *CircleTopic) GetLastCommentTime() uint32 {
	if m != nil {
		return m.LastCommentTime
	}
	return 0
}

func (m *CircleTopic) GetCreator() *ga.CircleUser {
	if m != nil {
		return m.Creator
	}
	return nil
}

func (m *CircleTopic) GetCreateTimeDesc() string {
	if m != nil {
		return m.CreateTimeDesc
	}
	return ""
}

func (m *CircleTopic) GetLastCommentTimeDesc() string {
	if m != nil {
		return m.LastCommentTimeDesc
	}
	return ""
}

func (m *CircleTopic) GetDownloadInfo() *ga.CircleTopicGameDownloadInfo {
	if m != nil {
		return m.DownloadInfo
	}
	return nil
}

// 游戏圈评论配图
type CircleCommentImage struct {
	ThumbUrl string `protobuf:"bytes,1,req,name=thumb_url,json=thumbUrl" json:"thumb_url"`
	ImageUrl string `protobuf:"bytes,2,req,name=image_url,json=imageUrl" json:"image_url"`
}

func (m *CircleCommentImage) Reset()                    { *m = CircleCommentImage{} }
func (m *CircleCommentImage) String() string            { return proto.CompactTextString(m) }
func (*CircleCommentImage) ProtoMessage()               {}
func (*CircleCommentImage) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{3} }

func (m *CircleCommentImage) GetThumbUrl() string {
	if m != nil {
		return m.ThumbUrl
	}
	return ""
}

func (m *CircleCommentImage) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

// 游戏圈评论
type CircleTopicComment struct {
	CommentId      uint32                `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	CircleId       uint32                `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId        uint32                `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Content        string                `protobuf:"bytes,4,req,name=content" json:"content"`
	Creator        *ga.CircleUser        `protobuf:"bytes,5,req,name=creator" json:"creator,omitempty"`
	CreateTime     uint32                `protobuf:"varint,6,req,name=create_time,json=createTime" json:"create_time"`
	Status         uint32                `protobuf:"varint,7,req,name=status" json:"status"`
	RepliedComment *CircleTopicComment   `protobuf:"bytes,8,opt,name=replied_comment,json=repliedComment" json:"replied_comment,omitempty"`
	CreateTimeDesc string                `protobuf:"bytes,9,req,name=create_time_desc,json=createTimeDesc" json:"create_time_desc"`
	ImageList      []*CircleCommentImage `protobuf:"bytes,10,rep,name=image_list,json=imageList" json:"image_list,omitempty"`
	Floor          int32                 `protobuf:"varint,11,opt,name=floor" json:"floor"`
}

func (m *CircleTopicComment) Reset()                    { *m = CircleTopicComment{} }
func (m *CircleTopicComment) String() string            { return proto.CompactTextString(m) }
func (*CircleTopicComment) ProtoMessage()               {}
func (*CircleTopicComment) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{4} }

func (m *CircleTopicComment) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *CircleTopicComment) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleTopicComment) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleTopicComment) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CircleTopicComment) GetCreator() *ga.CircleUser {
	if m != nil {
		return m.Creator
	}
	return nil
}

func (m *CircleTopicComment) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *CircleTopicComment) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CircleTopicComment) GetRepliedComment() *CircleTopicComment {
	if m != nil {
		return m.RepliedComment
	}
	return nil
}

func (m *CircleTopicComment) GetCreateTimeDesc() string {
	if m != nil {
		return m.CreateTimeDesc
	}
	return ""
}

func (m *CircleTopicComment) GetImageList() []*CircleCommentImage {
	if m != nil {
		return m.ImageList
	}
	return nil
}

func (m *CircleTopicComment) GetFloor() int32 {
	if m != nil {
		return m.Floor
	}
	return 0
}

// 描述圈子的一些与用户相关的动态数据, 具有实时性, 作为Circle静态类型的补充
type CircleDynamicData struct {
	CircleId              uint32          `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	FollowerNumber        uint32          `protobuf:"varint,2,req,name=follower_number,json=followerNumber" json:"follower_number"`
	GuildFollowerNumber   uint32          `protobuf:"varint,3,req,name=guild_follower_number,json=guildFollowerNumber" json:"guild_follower_number"`
	IsFollow              bool            `protobuf:"varint,4,req,name=is_follow,json=isFollow" json:"is_follow"`
	KeeperList            []*CircleKeeper `protobuf:"bytes,5,rep,name=keeper_list,json=keeperList" json:"keeper_list,omitempty"`
	CircleName            string          `protobuf:"bytes,6,opt,name=circle_name,json=circleName" json:"circle_name"`
	Icon                  string          `protobuf:"bytes,7,opt,name=icon" json:"icon"`
	GameId                uint32          `protobuf:"varint,8,opt,name=game_id,json=gameId" json:"game_id"`
	TopicCount            uint32          `protobuf:"varint,9,opt,name=topic_count,json=topicCount" json:"topic_count"`
	TodayTopicCount       uint32          `protobuf:"varint,10,opt,name=today_topic_count,json=todayTopicCount" json:"today_topic_count"`
	GameDownloadable      bool            `protobuf:"varint,11,opt,name=game_downloadable,json=gameDownloadable" json:"game_downloadable"`
	ActTag                string          `protobuf:"bytes,12,opt,name=act_tag,json=actTag" json:"act_tag"`
	ActDesc               string          `protobuf:"bytes,13,opt,name=act_desc,json=actDesc" json:"act_desc"`
	HasWelfare            bool            `protobuf:"varint,14,opt,name=has_welfare,json=hasWelfare" json:"has_welfare"`
	IsAnncouncementCircle bool            `protobuf:"varint,15,opt,name=is_anncouncement_circle,json=isAnncouncementCircle" json:"is_anncouncement_circle"`
	GameType              uint32          `protobuf:"varint,16,opt,name=game_type,json=gameType" json:"game_type"`
}

func (m *CircleDynamicData) Reset()                    { *m = CircleDynamicData{} }
func (m *CircleDynamicData) String() string            { return proto.CompactTextString(m) }
func (*CircleDynamicData) ProtoMessage()               {}
func (*CircleDynamicData) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{5} }

func (m *CircleDynamicData) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleDynamicData) GetFollowerNumber() uint32 {
	if m != nil {
		return m.FollowerNumber
	}
	return 0
}

func (m *CircleDynamicData) GetGuildFollowerNumber() uint32 {
	if m != nil {
		return m.GuildFollowerNumber
	}
	return 0
}

func (m *CircleDynamicData) GetIsFollow() bool {
	if m != nil {
		return m.IsFollow
	}
	return false
}

func (m *CircleDynamicData) GetKeeperList() []*CircleKeeper {
	if m != nil {
		return m.KeeperList
	}
	return nil
}

func (m *CircleDynamicData) GetCircleName() string {
	if m != nil {
		return m.CircleName
	}
	return ""
}

func (m *CircleDynamicData) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *CircleDynamicData) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CircleDynamicData) GetTopicCount() uint32 {
	if m != nil {
		return m.TopicCount
	}
	return 0
}

func (m *CircleDynamicData) GetTodayTopicCount() uint32 {
	if m != nil {
		return m.TodayTopicCount
	}
	return 0
}

func (m *CircleDynamicData) GetGameDownloadable() bool {
	if m != nil {
		return m.GameDownloadable
	}
	return false
}

func (m *CircleDynamicData) GetActTag() string {
	if m != nil {
		return m.ActTag
	}
	return ""
}

func (m *CircleDynamicData) GetActDesc() string {
	if m != nil {
		return m.ActDesc
	}
	return ""
}

func (m *CircleDynamicData) GetHasWelfare() bool {
	if m != nil {
		return m.HasWelfare
	}
	return false
}

func (m *CircleDynamicData) GetIsAnncouncementCircle() bool {
	if m != nil {
		return m.IsAnncouncementCircle
	}
	return false
}

func (m *CircleDynamicData) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

type CircleGetListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ReqType uint32      `protobuf:"varint,2,req,name=req_type,json=reqType" json:"req_type"`
}

func (m *CircleGetListReq) Reset()                    { *m = CircleGetListReq{} }
func (m *CircleGetListReq) String() string            { return proto.CompactTextString(m) }
func (*CircleGetListReq) ProtoMessage()               {}
func (*CircleGetListReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{6} }

func (m *CircleGetListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetListReq) GetReqType() uint32 {
	if m != nil {
		return m.ReqType
	}
	return 0
}

type CircleGetListResp struct {
	BaseResp              *ga.BaseResp         `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ReqType               uint32               `protobuf:"varint,2,req,name=req_type,json=reqType" json:"req_type"`
	CircleDynamicDataList []*CircleDynamicData `protobuf:"bytes,3,rep,name=circle_dynamic_data_list,json=circleDynamicDataList" json:"circle_dynamic_data_list,omitempty"`
}

func (m *CircleGetListResp) Reset()                    { *m = CircleGetListResp{} }
func (m *CircleGetListResp) String() string            { return proto.CompactTextString(m) }
func (*CircleGetListResp) ProtoMessage()               {}
func (*CircleGetListResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{7} }

func (m *CircleGetListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetListResp) GetReqType() uint32 {
	if m != nil {
		return m.ReqType
	}
	return 0
}

func (m *CircleGetListResp) GetCircleDynamicDataList() []*CircleDynamicData {
	if m != nil {
		return m.CircleDynamicDataList
	}
	return nil
}

// 加入游戏圈
type CircleJoinReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleIdList []uint32    `protobuf:"varint,2,rep,name=circle_id_list,json=circleIdList" json:"circle_id_list,omitempty"`
}

func (m *CircleJoinReq) Reset()                    { *m = CircleJoinReq{} }
func (m *CircleJoinReq) String() string            { return proto.CompactTextString(m) }
func (*CircleJoinReq) ProtoMessage()               {}
func (*CircleJoinReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{8} }

func (m *CircleJoinReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleJoinReq) GetCircleIdList() []uint32 {
	if m != nil {
		return m.CircleIdList
	}
	return nil
}

type CircleJoinResp struct {
	BaseResp     *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleIdList []uint32     `protobuf:"varint,2,rep,name=circle_id_list,json=circleIdList" json:"circle_id_list,omitempty"`
	FailedIdList []uint32     `protobuf:"varint,3,rep,name=failed_id_list,json=failedIdList" json:"failed_id_list,omitempty"`
}

func (m *CircleJoinResp) Reset()                    { *m = CircleJoinResp{} }
func (m *CircleJoinResp) String() string            { return proto.CompactTextString(m) }
func (*CircleJoinResp) ProtoMessage()               {}
func (*CircleJoinResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{9} }

func (m *CircleJoinResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleJoinResp) GetCircleIdList() []uint32 {
	if m != nil {
		return m.CircleIdList
	}
	return nil
}

func (m *CircleJoinResp) GetFailedIdList() []uint32 {
	if m != nil {
		return m.FailedIdList
	}
	return nil
}

// 退出游戏圈
type CircleQuitReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
}

func (m *CircleQuitReq) Reset()                    { *m = CircleQuitReq{} }
func (m *CircleQuitReq) String() string            { return proto.CompactTextString(m) }
func (*CircleQuitReq) ProtoMessage()               {}
func (*CircleQuitReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{10} }

func (m *CircleQuitReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleQuitReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

type CircleQuitResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
}

func (m *CircleQuitResp) Reset()                    { *m = CircleQuitResp{} }
func (m *CircleQuitResp) String() string            { return proto.CompactTextString(m) }
func (*CircleQuitResp) ProtoMessage()               {}
func (*CircleQuitResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{11} }

func (m *CircleQuitResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleQuitResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

// TODO
type CircleGetTopicListReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId     uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	PageCount    uint32      `protobuf:"varint,3,req,name=page_count,json=pageCount" json:"page_count"`
	PagePosition uint32      `protobuf:"varint,4,req,name=page_position,json=pagePosition" json:"page_position"`
	ReqType      uint32      `protobuf:"varint,5,req,name=req_type,json=reqType" json:"req_type"`
	Userfrom     uint32      `protobuf:"varint,6,req,name=userfrom" json:"userfrom"`
}

func (m *CircleGetTopicListReq) Reset()                    { *m = CircleGetTopicListReq{} }
func (m *CircleGetTopicListReq) String() string            { return proto.CompactTextString(m) }
func (*CircleGetTopicListReq) ProtoMessage()               {}
func (*CircleGetTopicListReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{12} }

func (m *CircleGetTopicListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetTopicListReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetTopicListReq) GetPageCount() uint32 {
	if m != nil {
		return m.PageCount
	}
	return 0
}

func (m *CircleGetTopicListReq) GetPagePosition() uint32 {
	if m != nil {
		return m.PagePosition
	}
	return 0
}

func (m *CircleGetTopicListReq) GetReqType() uint32 {
	if m != nil {
		return m.ReqType
	}
	return 0
}

func (m *CircleGetTopicListReq) GetUserfrom() uint32 {
	if m != nil {
		return m.Userfrom
	}
	return 0
}

// TODO
type CircleGetTopicListResp struct {
	BaseResp      *ga.BaseResp   `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId      uint32         `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	PageCount     uint32         `protobuf:"varint,3,req,name=page_count,json=pageCount" json:"page_count"`
	PagePosition  uint32         `protobuf:"varint,4,req,name=page_position,json=pagePosition" json:"page_position"`
	ReqType       uint32         `protobuf:"varint,5,req,name=req_type,json=reqType" json:"req_type"`
	TopicList     []*CircleTopic `protobuf:"bytes,6,rep,name=topic_list,json=topicList" json:"topic_list,omitempty"`
	NewestTopicId uint32         `protobuf:"varint,7,req,name=newest_topic_id,json=newestTopicId" json:"newest_topic_id"`
}

func (m *CircleGetTopicListResp) Reset()                    { *m = CircleGetTopicListResp{} }
func (m *CircleGetTopicListResp) String() string            { return proto.CompactTextString(m) }
func (*CircleGetTopicListResp) ProtoMessage()               {}
func (*CircleGetTopicListResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{13} }

func (m *CircleGetTopicListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetTopicListResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetTopicListResp) GetPageCount() uint32 {
	if m != nil {
		return m.PageCount
	}
	return 0
}

func (m *CircleGetTopicListResp) GetPagePosition() uint32 {
	if m != nil {
		return m.PagePosition
	}
	return 0
}

func (m *CircleGetTopicListResp) GetReqType() uint32 {
	if m != nil {
		return m.ReqType
	}
	return 0
}

func (m *CircleGetTopicListResp) GetTopicList() []*CircleTopic {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *CircleGetTopicListResp) GetNewestTopicId() uint32 {
	if m != nil {
		return m.NewestTopicId
	}
	return 0
}

// 获取某条topic
type CircleGetTopicReq struct {
	BaseReq         *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId        uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId         uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	LikeUserCount   uint32      `protobuf:"varint,4,opt,name=like_user_count,json=likeUserCount" json:"like_user_count"`
	TopCommentCount uint32      `protobuf:"varint,5,opt,name=top_comment_count,json=topCommentCount" json:"top_comment_count"`
}

func (m *CircleGetTopicReq) Reset()                    { *m = CircleGetTopicReq{} }
func (m *CircleGetTopicReq) String() string            { return proto.CompactTextString(m) }
func (*CircleGetTopicReq) ProtoMessage()               {}
func (*CircleGetTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{14} }

func (m *CircleGetTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetTopicReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetTopicReq) GetLikeUserCount() uint32 {
	if m != nil {
		return m.LikeUserCount
	}
	return 0
}

func (m *CircleGetTopicReq) GetTopCommentCount() uint32 {
	if m != nil {
		return m.TopCommentCount
	}
	return 0
}

type CircleGetTopicResp struct {
	BaseResp       *ga.BaseResp          `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId       uint32                `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId        uint32                `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Topic          *CircleTopic          `protobuf:"bytes,4,req,name=topic" json:"topic,omitempty"`
	LikeUserList   []string              `protobuf:"bytes,5,rep,name=like_user_list,json=likeUserList" json:"like_user_list,omitempty"`
	TopCommentList []*CircleTopicComment `protobuf:"bytes,6,rep,name=top_comment_list,json=topCommentList" json:"top_comment_list,omitempty"`
}

func (m *CircleGetTopicResp) Reset()                    { *m = CircleGetTopicResp{} }
func (m *CircleGetTopicResp) String() string            { return proto.CompactTextString(m) }
func (*CircleGetTopicResp) ProtoMessage()               {}
func (*CircleGetTopicResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{15} }

func (m *CircleGetTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetTopicResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetTopicResp) GetTopic() *CircleTopic {
	if m != nil {
		return m.Topic
	}
	return nil
}

func (m *CircleGetTopicResp) GetLikeUserList() []string {
	if m != nil {
		return m.LikeUserList
	}
	return nil
}

func (m *CircleGetTopicResp) GetTopCommentList() []*CircleTopicComment {
	if m != nil {
		return m.TopCommentList
	}
	return nil
}

// 发表主题
type CirclePostTopicReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId   uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	ClientId   string      `protobuf:"bytes,3,req,name=client_id,json=clientId" json:"client_id"`
	Title      string      `protobuf:"bytes,4,opt,name=title" json:"title"`
	Content    string      `protobuf:"bytes,5,req,name=content" json:"content"`
	ImgKeyList []string    `protobuf:"bytes,6,rep,name=img_key_list,json=imgKeyList" json:"img_key_list,omitempty"`
}

func (m *CirclePostTopicReq) Reset()                    { *m = CirclePostTopicReq{} }
func (m *CirclePostTopicReq) String() string            { return proto.CompactTextString(m) }
func (*CirclePostTopicReq) ProtoMessage()               {}
func (*CirclePostTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{16} }

func (m *CirclePostTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CirclePostTopicReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CirclePostTopicReq) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *CirclePostTopicReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CirclePostTopicReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CirclePostTopicReq) GetImgKeyList() []string {
	if m != nil {
		return m.ImgKeyList
	}
	return nil
}

type CirclePostTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ClientId string       `protobuf:"bytes,2,req,name=client_id,json=clientId" json:"client_id"`
	Topic    *CircleTopic `protobuf:"bytes,3,req,name=topic" json:"topic,omitempty"`
}

func (m *CirclePostTopicResp) Reset()                    { *m = CirclePostTopicResp{} }
func (m *CirclePostTopicResp) String() string            { return proto.CompactTextString(m) }
func (*CirclePostTopicResp) ProtoMessage()               {}
func (*CirclePostTopicResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{17} }

func (m *CirclePostTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CirclePostTopicResp) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *CirclePostTopicResp) GetTopic() *CircleTopic {
	if m != nil {
		return m.Topic
	}
	return nil
}

// 获取评论列表
type CircleGetCommentListReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId       uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId        uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	StartCommentId uint32      `protobuf:"varint,4,req,name=start_comment_id,json=startCommentId" json:"start_comment_id"`
	CommentCount   uint32      `protobuf:"varint,5,req,name=comment_count,json=commentCount" json:"comment_count"`
}

func (m *CircleGetCommentListReq) Reset()                    { *m = CircleGetCommentListReq{} }
func (m *CircleGetCommentListReq) String() string            { return proto.CompactTextString(m) }
func (*CircleGetCommentListReq) ProtoMessage()               {}
func (*CircleGetCommentListReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{18} }

func (m *CircleGetCommentListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetCommentListReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetCommentListReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetCommentListReq) GetStartCommentId() uint32 {
	if m != nil {
		return m.StartCommentId
	}
	return 0
}

func (m *CircleGetCommentListReq) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

type CircleGetCommentListResp struct {
	BaseResp       *ga.BaseResp          `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId       uint32                `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId        uint32                `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	StartCommentId uint32                `protobuf:"varint,4,req,name=start_comment_id,json=startCommentId" json:"start_comment_id"`
	CommentCount   uint32                `protobuf:"varint,5,req,name=comment_count,json=commentCount" json:"comment_count"`
	CommentList    []*CircleTopicComment `protobuf:"bytes,6,rep,name=comment_list,json=commentList" json:"comment_list,omitempty"`
}

func (m *CircleGetCommentListResp) Reset()         { *m = CircleGetCommentListResp{} }
func (m *CircleGetCommentListResp) String() string { return proto.CompactTextString(m) }
func (*CircleGetCommentListResp) ProtoMessage()    {}
func (*CircleGetCommentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{19}
}

func (m *CircleGetCommentListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetCommentListResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetCommentListResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetCommentListResp) GetStartCommentId() uint32 {
	if m != nil {
		return m.StartCommentId
	}
	return 0
}

func (m *CircleGetCommentListResp) GetCommentCount() uint32 {
	if m != nil {
		return m.CommentCount
	}
	return 0
}

func (m *CircleGetCommentListResp) GetCommentList() []*CircleTopicComment {
	if m != nil {
		return m.CommentList
	}
	return nil
}

// 发表评论
type CirclePostCommentReq struct {
	BaseReq          *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId         uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId          uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	RepliedCommentId uint32      `protobuf:"varint,4,req,name=replied_comment_id,json=repliedCommentId" json:"replied_comment_id"`
	Content          string      `protobuf:"bytes,5,req,name=content" json:"content"`
	ImgKeyList       []string    `protobuf:"bytes,6,rep,name=img_key_list,json=imgKeyList" json:"img_key_list,omitempty"`
}

func (m *CirclePostCommentReq) Reset()                    { *m = CirclePostCommentReq{} }
func (m *CirclePostCommentReq) String() string            { return proto.CompactTextString(m) }
func (*CirclePostCommentReq) ProtoMessage()               {}
func (*CirclePostCommentReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{20} }

func (m *CirclePostCommentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CirclePostCommentReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CirclePostCommentReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CirclePostCommentReq) GetRepliedCommentId() uint32 {
	if m != nil {
		return m.RepliedCommentId
	}
	return 0
}

func (m *CirclePostCommentReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *CirclePostCommentReq) GetImgKeyList() []string {
	if m != nil {
		return m.ImgKeyList
	}
	return nil
}

type CirclePostCommentResp struct {
	BaseResp         *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId         uint32              `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId          uint32              `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	RepliedCommentId uint32              `protobuf:"varint,4,req,name=replied_comment_id,json=repliedCommentId" json:"replied_comment_id"`
	Comment          *CircleTopicComment `protobuf:"bytes,5,req,name=comment" json:"comment,omitempty"`
}

func (m *CirclePostCommentResp) Reset()                    { *m = CirclePostCommentResp{} }
func (m *CirclePostCommentResp) String() string            { return proto.CompactTextString(m) }
func (*CirclePostCommentResp) ProtoMessage()               {}
func (*CirclePostCommentResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{21} }

func (m *CirclePostCommentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CirclePostCommentResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CirclePostCommentResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CirclePostCommentResp) GetRepliedCommentId() uint32 {
	if m != nil {
		return m.RepliedCommentId
	}
	return 0
}

func (m *CirclePostCommentResp) GetComment() *CircleTopicComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

// 赞/取消赞 消息  同旧版
type CircleLikeTopicReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	IsLike   bool        `protobuf:"varint,4,req,name=is_like,json=isLike" json:"is_like"`
}

func (m *CircleLikeTopicReq) Reset()                    { *m = CircleLikeTopicReq{} }
func (m *CircleLikeTopicReq) String() string            { return proto.CompactTextString(m) }
func (*CircleLikeTopicReq) ProtoMessage()               {}
func (*CircleLikeTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{22} }

func (m *CircleLikeTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleLikeTopicReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleLikeTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleLikeTopicReq) GetIsLike() bool {
	if m != nil {
		return m.IsLike
	}
	return false
}

type CircleLikeTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	IsLike   bool         `protobuf:"varint,4,req,name=is_like,json=isLike" json:"is_like"`
}

func (m *CircleLikeTopicResp) Reset()                    { *m = CircleLikeTopicResp{} }
func (m *CircleLikeTopicResp) String() string            { return proto.CompactTextString(m) }
func (*CircleLikeTopicResp) ProtoMessage()               {}
func (*CircleLikeTopicResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{23} }

func (m *CircleLikeTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleLikeTopicResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleLikeTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleLikeTopicResp) GetIsLike() bool {
	if m != nil {
		return m.IsLike
	}
	return false
}

// 举报主题 同旧版
type CircleReportTopicReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleReportTopicReq) Reset()                    { *m = CircleReportTopicReq{} }
func (m *CircleReportTopicReq) String() string            { return proto.CompactTextString(m) }
func (*CircleReportTopicReq) ProtoMessage()               {}
func (*CircleReportTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{24} }

func (m *CircleReportTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleReportTopicReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleReportTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type CircleReportTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleReportTopicResp) Reset()                    { *m = CircleReportTopicResp{} }
func (m *CircleReportTopicResp) String() string            { return proto.CompactTextString(m) }
func (*CircleReportTopicResp) ProtoMessage()               {}
func (*CircleReportTopicResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{25} }

func (m *CircleReportTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleReportTopicResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleReportTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

// 删除我发的主题 同旧版
type CircleDeleteTopicReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleDeleteTopicReq) Reset()                    { *m = CircleDeleteTopicReq{} }
func (m *CircleDeleteTopicReq) String() string            { return proto.CompactTextString(m) }
func (*CircleDeleteTopicReq) ProtoMessage()               {}
func (*CircleDeleteTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{26} }

func (m *CircleDeleteTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleDeleteTopicReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleDeleteTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type CircleDeleteTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleDeleteTopicResp) Reset()                    { *m = CircleDeleteTopicResp{} }
func (m *CircleDeleteTopicResp) String() string            { return proto.CompactTextString(m) }
func (*CircleDeleteTopicResp) ProtoMessage()               {}
func (*CircleDeleteTopicResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{27} }

func (m *CircleDeleteTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleDeleteTopicResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleDeleteTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

// 删除我发的评论 同旧版
type CircleDeleteCommentReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId  uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId   uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32      `protobuf:"varint,4,req,name=comment_id,json=commentId" json:"comment_id"`
}

func (m *CircleDeleteCommentReq) Reset()                    { *m = CircleDeleteCommentReq{} }
func (m *CircleDeleteCommentReq) String() string            { return proto.CompactTextString(m) }
func (*CircleDeleteCommentReq) ProtoMessage()               {}
func (*CircleDeleteCommentReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{28} }

func (m *CircleDeleteCommentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleDeleteCommentReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleDeleteCommentReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleDeleteCommentReq) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

type CircleDeleteCommentResp struct {
	BaseResp  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId  uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId   uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32       `protobuf:"varint,4,req,name=comment_id,json=commentId" json:"comment_id"`
}

func (m *CircleDeleteCommentResp) Reset()                    { *m = CircleDeleteCommentResp{} }
func (m *CircleDeleteCommentResp) String() string            { return proto.CompactTextString(m) }
func (*CircleDeleteCommentResp) ProtoMessage()               {}
func (*CircleDeleteCommentResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{29} }

func (m *CircleDeleteCommentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleDeleteCommentResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleDeleteCommentResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleDeleteCommentResp) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

// 单查游戏圈
type CircleGetCircleDetailReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
}

func (m *CircleGetCircleDetailReq) Reset()         { *m = CircleGetCircleDetailReq{} }
func (m *CircleGetCircleDetailReq) String() string { return proto.CompactTextString(m) }
func (*CircleGetCircleDetailReq) ProtoMessage()    {}
func (*CircleGetCircleDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{30}
}

func (m *CircleGetCircleDetailReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetCircleDetailReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

type CircleGetCircleDetailResp struct {
	BaseResp      *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId      uint32             `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	Circle        *ga.Circle         `protobuf:"bytes,3,req,name=circle" json:"circle,omitempty"`
	CircleDynamic *CircleDynamicData `protobuf:"bytes,4,req,name=circle_dynamic,json=circleDynamic" json:"circle_dynamic,omitempty"`
}

func (m *CircleGetCircleDetailResp) Reset()         { *m = CircleGetCircleDetailResp{} }
func (m *CircleGetCircleDetailResp) String() string { return proto.CompactTextString(m) }
func (*CircleGetCircleDetailResp) ProtoMessage()    {}
func (*CircleGetCircleDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{31}
}

func (m *CircleGetCircleDetailResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetCircleDetailResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetCircleDetailResp) GetCircle() *ga.Circle {
	if m != nil {
		return m.Circle
	}
	return nil
}

func (m *CircleGetCircleDetailResp) GetCircleDynamic() *CircleDynamicData {
	if m != nil {
		return m.CircleDynamic
	}
	return nil
}

// 查我发表过的主题
type CircleGetUserTopicReq struct {
	BaseReq       *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	StartCircleId uint32      `protobuf:"varint,2,req,name=start_circle_id,json=startCircleId" json:"start_circle_id"`
	StartTopicId  uint32      `protobuf:"varint,3,req,name=start_topic_id,json=startTopicId" json:"start_topic_id"`
	TopicCount    uint32      `protobuf:"varint,4,req,name=topic_count,json=topicCount" json:"topic_count"`
	Uid           uint32      `protobuf:"varint,5,req,name=uid" json:"uid"`
}

func (m *CircleGetUserTopicReq) Reset()                    { *m = CircleGetUserTopicReq{} }
func (m *CircleGetUserTopicReq) String() string            { return proto.CompactTextString(m) }
func (*CircleGetUserTopicReq) ProtoMessage()               {}
func (*CircleGetUserTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{32} }

func (m *CircleGetUserTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetUserTopicReq) GetStartCircleId() uint32 {
	if m != nil {
		return m.StartCircleId
	}
	return 0
}

func (m *CircleGetUserTopicReq) GetStartTopicId() uint32 {
	if m != nil {
		return m.StartTopicId
	}
	return 0
}

func (m *CircleGetUserTopicReq) GetTopicCount() uint32 {
	if m != nil {
		return m.TopicCount
	}
	return 0
}

func (m *CircleGetUserTopicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CircleGetUserTopicResp struct {
	BaseResp      *ga.BaseResp   `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	StartCircleId uint32         `protobuf:"varint,2,req,name=start_circle_id,json=startCircleId" json:"start_circle_id"`
	StartTopicId  uint32         `protobuf:"varint,3,req,name=start_topic_id,json=startTopicId" json:"start_topic_id"`
	TopicCount    uint32         `protobuf:"varint,4,req,name=topic_count,json=topicCount" json:"topic_count"`
	TopicList     []*CircleTopic `protobuf:"bytes,5,rep,name=topic_list,json=topicList" json:"topic_list,omitempty"`
	Uid           uint32         `protobuf:"varint,6,req,name=uid" json:"uid"`
}

func (m *CircleGetUserTopicResp) Reset()                    { *m = CircleGetUserTopicResp{} }
func (m *CircleGetUserTopicResp) String() string            { return proto.CompactTextString(m) }
func (*CircleGetUserTopicResp) ProtoMessage()               {}
func (*CircleGetUserTopicResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{33} }

func (m *CircleGetUserTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetUserTopicResp) GetStartCircleId() uint32 {
	if m != nil {
		return m.StartCircleId
	}
	return 0
}

func (m *CircleGetUserTopicResp) GetStartTopicId() uint32 {
	if m != nil {
		return m.StartTopicId
	}
	return 0
}

func (m *CircleGetUserTopicResp) GetTopicCount() uint32 {
	if m != nil {
		return m.TopicCount
	}
	return 0
}

func (m *CircleGetUserTopicResp) GetTopicList() []*CircleTopic {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *CircleGetUserTopicResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// 查点赞列表
type CircleGetLikeUserListReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Offset   uint32      `protobuf:"varint,4,req,name=offset" json:"offset"`
	Limit    uint32      `protobuf:"varint,5,req,name=limit" json:"limit"`
}

func (m *CircleGetLikeUserListReq) Reset()         { *m = CircleGetLikeUserListReq{} }
func (m *CircleGetLikeUserListReq) String() string { return proto.CompactTextString(m) }
func (*CircleGetLikeUserListReq) ProtoMessage()    {}
func (*CircleGetLikeUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{34}
}

func (m *CircleGetLikeUserListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetLikeUserListReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetLikeUserListReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetLikeUserListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *CircleGetLikeUserListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type CircleGetLikeUserListResp struct {
	BaseResp     *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId     uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId      uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Offset       uint32       `protobuf:"varint,4,req,name=offset" json:"offset"`
	Limit        uint32       `protobuf:"varint,5,req,name=limit" json:"limit"`
	UserNickList []string     `protobuf:"bytes,6,rep,name=user_nick_list,json=userNickList" json:"user_nick_list,omitempty"`
}

func (m *CircleGetLikeUserListResp) Reset()         { *m = CircleGetLikeUserListResp{} }
func (m *CircleGetLikeUserListResp) String() string { return proto.CompactTextString(m) }
func (*CircleGetLikeUserListResp) ProtoMessage()    {}
func (*CircleGetLikeUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{35}
}

func (m *CircleGetLikeUserListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetLikeUserListResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetLikeUserListResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetLikeUserListResp) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *CircleGetLikeUserListResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *CircleGetLikeUserListResp) GetUserNickList() []string {
	if m != nil {
		return m.UserNickList
	}
	return nil
}

// /------ 以下不变
// 批量设置已读
type CircleMarkReadedReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	SvrMsgId uint32      `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
}

func (m *CircleMarkReadedReq) Reset()                    { *m = CircleMarkReadedReq{} }
func (m *CircleMarkReadedReq) String() string            { return proto.CompactTextString(m) }
func (*CircleMarkReadedReq) ProtoMessage()               {}
func (*CircleMarkReadedReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{36} }

func (m *CircleMarkReadedReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleMarkReadedReq) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

type CircleMarkReadedResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SvrMsgId uint32       `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
}

func (m *CircleMarkReadedResp) Reset()                    { *m = CircleMarkReadedResp{} }
func (m *CircleMarkReadedResp) String() string            { return proto.CompactTextString(m) }
func (*CircleMarkReadedResp) ProtoMessage()               {}
func (*CircleMarkReadedResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{37} }

func (m *CircleMarkReadedResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleMarkReadedResp) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

type CircleMuteUserReq struct {
	BaseReq         *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid             uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
	CircleIdList    []uint32    `protobuf:"varint,3,rep,name=circle_id_list,json=circleIdList" json:"circle_id_list,omitempty"`
	Days            uint32      `protobuf:"varint,4,req,name=days" json:"days"`
	Secs            uint32      `protobuf:"varint,5,opt,name=secs" json:"secs"`
	Reason          string      `protobuf:"bytes,6,opt,name=reason" json:"reason"`
	BecauseCircleId uint32      `protobuf:"varint,7,opt,name=because_circle_id,json=becauseCircleId" json:"because_circle_id"`
	BecauseTopicId  uint32      `protobuf:"varint,8,opt,name=because_topic_id,json=becauseTopicId" json:"because_topic_id"`
}

func (m *CircleMuteUserReq) Reset()                    { *m = CircleMuteUserReq{} }
func (m *CircleMuteUserReq) String() string            { return proto.CompactTextString(m) }
func (*CircleMuteUserReq) ProtoMessage()               {}
func (*CircleMuteUserReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{38} }

func (m *CircleMuteUserReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleMuteUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CircleMuteUserReq) GetCircleIdList() []uint32 {
	if m != nil {
		return m.CircleIdList
	}
	return nil
}

func (m *CircleMuteUserReq) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

func (m *CircleMuteUserReq) GetSecs() uint32 {
	if m != nil {
		return m.Secs
	}
	return 0
}

func (m *CircleMuteUserReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *CircleMuteUserReq) GetBecauseCircleId() uint32 {
	if m != nil {
		return m.BecauseCircleId
	}
	return 0
}

func (m *CircleMuteUserReq) GetBecauseTopicId() uint32 {
	if m != nil {
		return m.BecauseTopicId
	}
	return 0
}

type CircleMuteUserResp struct {
	BaseResp     *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Uid          uint32       `protobuf:"varint,2,req,name=uid" json:"uid"`
	CircleIdList []uint32     `protobuf:"varint,3,rep,name=circle_id_list,json=circleIdList" json:"circle_id_list,omitempty"`
	Days         uint32       `protobuf:"varint,4,req,name=days" json:"days"`
	Secs         uint32       `protobuf:"varint,5,opt,name=secs" json:"secs"`
}

func (m *CircleMuteUserResp) Reset()                    { *m = CircleMuteUserResp{} }
func (m *CircleMuteUserResp) String() string            { return proto.CompactTextString(m) }
func (*CircleMuteUserResp) ProtoMessage()               {}
func (*CircleMuteUserResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{39} }

func (m *CircleMuteUserResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleMuteUserResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CircleMuteUserResp) GetCircleIdList() []uint32 {
	if m != nil {
		return m.CircleIdList
	}
	return nil
}

func (m *CircleMuteUserResp) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

func (m *CircleMuteUserResp) GetSecs() uint32 {
	if m != nil {
		return m.Secs
	}
	return 0
}

type CircleUnmuteUserReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Uid          uint32      `protobuf:"varint,2,req,name=uid" json:"uid"`
	CircleIdList []uint32    `protobuf:"varint,3,rep,name=circle_id_list,json=circleIdList" json:"circle_id_list,omitempty"`
}

func (m *CircleUnmuteUserReq) Reset()                    { *m = CircleUnmuteUserReq{} }
func (m *CircleUnmuteUserReq) String() string            { return proto.CompactTextString(m) }
func (*CircleUnmuteUserReq) ProtoMessage()               {}
func (*CircleUnmuteUserReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{40} }

func (m *CircleUnmuteUserReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleUnmuteUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CircleUnmuteUserReq) GetCircleIdList() []uint32 {
	if m != nil {
		return m.CircleIdList
	}
	return nil
}

type CircleUnmuteUserResp struct {
	BaseResp     *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Uid          uint32       `protobuf:"varint,2,req,name=uid" json:"uid"`
	CircleIdList []uint32     `protobuf:"varint,3,rep,name=circle_id_list,json=circleIdList" json:"circle_id_list,omitempty"`
}

func (m *CircleUnmuteUserResp) Reset()                    { *m = CircleUnmuteUserResp{} }
func (m *CircleUnmuteUserResp) String() string            { return proto.CompactTextString(m) }
func (*CircleUnmuteUserResp) ProtoMessage()               {}
func (*CircleUnmuteUserResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{41} }

func (m *CircleUnmuteUserResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleUnmuteUserResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CircleUnmuteUserResp) GetCircleIdList() []uint32 {
	if m != nil {
		return m.CircleIdList
	}
	return nil
}

// 我的圈子排序 -- v1.5
type MyCircleOrderReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleIdList []uint32    `protobuf:"varint,2,rep,name=Circle_id_list,json=CircleIdList" json:"Circle_id_list,omitempty"`
}

func (m *MyCircleOrderReq) Reset()                    { *m = MyCircleOrderReq{} }
func (m *MyCircleOrderReq) String() string            { return proto.CompactTextString(m) }
func (*MyCircleOrderReq) ProtoMessage()               {}
func (*MyCircleOrderReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{42} }

func (m *MyCircleOrderReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MyCircleOrderReq) GetCircleIdList() []uint32 {
	if m != nil {
		return m.CircleIdList
	}
	return nil
}

type MyCircleOrderResp struct {
	BaseResp     *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleIdList []uint32     `protobuf:"varint,2,rep,name=Circle_id_list,json=CircleIdList" json:"Circle_id_list,omitempty"`
}

func (m *MyCircleOrderResp) Reset()                    { *m = MyCircleOrderResp{} }
func (m *MyCircleOrderResp) String() string            { return proto.CompactTextString(m) }
func (*MyCircleOrderResp) ProtoMessage()               {}
func (*MyCircleOrderResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{43} }

func (m *MyCircleOrderResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MyCircleOrderResp) GetCircleIdList() []uint32 {
	if m != nil {
		return m.CircleIdList
	}
	return nil
}

// 加精 -- v1.5
type CircleHighlightTopicReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleHighlightTopicReq) Reset()                    { *m = CircleHighlightTopicReq{} }
func (m *CircleHighlightTopicReq) String() string            { return proto.CompactTextString(m) }
func (*CircleHighlightTopicReq) ProtoMessage()               {}
func (*CircleHighlightTopicReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{44} }

func (m *CircleHighlightTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleHighlightTopicReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleHighlightTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type CircleHighlightTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleHighlightTopicResp) Reset()         { *m = CircleHighlightTopicResp{} }
func (m *CircleHighlightTopicResp) String() string { return proto.CompactTextString(m) }
func (*CircleHighlightTopicResp) ProtoMessage()    {}
func (*CircleHighlightTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{45}
}

func (m *CircleHighlightTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleHighlightTopicResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleHighlightTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

// 取消加精 -- v1.5
type CircleCancelHighlightTopicReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleCancelHighlightTopicReq) Reset()         { *m = CircleCancelHighlightTopicReq{} }
func (m *CircleCancelHighlightTopicReq) String() string { return proto.CompactTextString(m) }
func (*CircleCancelHighlightTopicReq) ProtoMessage()    {}
func (*CircleCancelHighlightTopicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{46}
}

func (m *CircleCancelHighlightTopicReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleCancelHighlightTopicReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleCancelHighlightTopicReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

type CircleCancelHighlightTopicResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *CircleCancelHighlightTopicResp) Reset()         { *m = CircleCancelHighlightTopicResp{} }
func (m *CircleCancelHighlightTopicResp) String() string { return proto.CompactTextString(m) }
func (*CircleCancelHighlightTopicResp) ProtoMessage()    {}
func (*CircleCancelHighlightTopicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{47}
}

func (m *CircleCancelHighlightTopicResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleCancelHighlightTopicResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleCancelHighlightTopicResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

// 管理员删除评论 -- v1.5
type CircleManagerDeleteCommentReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId  uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId   uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32      `protobuf:"varint,4,req,name=comment_id,json=commentId" json:"comment_id"`
}

func (m *CircleManagerDeleteCommentReq) Reset()         { *m = CircleManagerDeleteCommentReq{} }
func (m *CircleManagerDeleteCommentReq) String() string { return proto.CompactTextString(m) }
func (*CircleManagerDeleteCommentReq) ProtoMessage()    {}
func (*CircleManagerDeleteCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{48}
}

func (m *CircleManagerDeleteCommentReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleManagerDeleteCommentReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleManagerDeleteCommentReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleManagerDeleteCommentReq) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

type CircleManagerDeleteCommentResp struct {
	BaseResp  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId  uint32       `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId   uint32       `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32       `protobuf:"varint,4,req,name=comment_id,json=commentId" json:"comment_id"`
}

func (m *CircleManagerDeleteCommentResp) Reset()         { *m = CircleManagerDeleteCommentResp{} }
func (m *CircleManagerDeleteCommentResp) String() string { return proto.CompactTextString(m) }
func (*CircleManagerDeleteCommentResp) ProtoMessage()    {}
func (*CircleManagerDeleteCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{49}
}

func (m *CircleManagerDeleteCommentResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleManagerDeleteCommentResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleManagerDeleteCommentResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleManagerDeleteCommentResp) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

// 检查圈子更新状态
type CircleCheckUpdateReq struct {
	BaseReq         *ga.BaseReq           `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	TopicUpdateInfo []*ga.TopicUpdateInfo `protobuf:"bytes,2,rep,name=topic_update_info,json=topicUpdateInfo" json:"topic_update_info,omitempty"`
}

func (m *CircleCheckUpdateReq) Reset()                    { *m = CircleCheckUpdateReq{} }
func (m *CircleCheckUpdateReq) String() string            { return proto.CompactTextString(m) }
func (*CircleCheckUpdateReq) ProtoMessage()               {}
func (*CircleCheckUpdateReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{50} }

func (m *CircleCheckUpdateReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleCheckUpdateReq) GetTopicUpdateInfo() []*ga.TopicUpdateInfo {
	if m != nil {
		return m.TopicUpdateInfo
	}
	return nil
}

type CircleCheckUpdateResp struct {
	BaseResp        *ga.BaseResp          `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	TopicUpdateInfo []*ga.TopicUpdateInfo `protobuf:"bytes,2,rep,name=topic_update_info,json=topicUpdateInfo" json:"topic_update_info,omitempty"`
	NewestCircleId  uint32                `protobuf:"varint,3,opt,name=newest_circle_id,json=newestCircleId" json:"newest_circle_id"`
}

func (m *CircleCheckUpdateResp) Reset()                    { *m = CircleCheckUpdateResp{} }
func (m *CircleCheckUpdateResp) String() string            { return proto.CompactTextString(m) }
func (*CircleCheckUpdateResp) ProtoMessage()               {}
func (*CircleCheckUpdateResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{51} }

func (m *CircleCheckUpdateResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleCheckUpdateResp) GetTopicUpdateInfo() []*ga.TopicUpdateInfo {
	if m != nil {
		return m.TopicUpdateInfo
	}
	return nil
}

func (m *CircleCheckUpdateResp) GetNewestCircleId() uint32 {
	if m != nil {
		return m.NewestCircleId
	}
	return 0
}

type CircleActivityDetail struct {
	ActivityId      uint32 `protobuf:"varint,1,req,name=activity_id,json=activityId" json:"activity_id"`
	ActUrl          string `protobuf:"bytes,2,req,name=act_url,json=actUrl" json:"act_url"`
	PicUrl          string `protobuf:"bytes,3,opt,name=pic_url,json=picUrl" json:"pic_url"`
	Title           string `protobuf:"bytes,4,req,name=title" json:"title"`
	WarmedUpTsBegin uint32 `protobuf:"varint,5,req,name=warmed_up_ts_begin,json=warmedUpTsBegin" json:"warmed_up_ts_begin"`
	WarmedUpTsEnd   uint32 `protobuf:"varint,6,req,name=warmed_up_ts_end,json=warmedUpTsEnd" json:"warmed_up_ts_end"`
	ActiveTsBegin   uint32 `protobuf:"varint,7,req,name=active_ts_begin,json=activeTsBegin" json:"active_ts_begin"`
	ActiveTsEnd     uint32 `protobuf:"varint,8,req,name=active_ts_end,json=activeTsEnd" json:"active_ts_end"`
	EndTs           uint32 `protobuf:"varint,9,req,name=end_ts,json=endTs" json:"end_ts"`
}

func (m *CircleActivityDetail) Reset()                    { *m = CircleActivityDetail{} }
func (m *CircleActivityDetail) String() string            { return proto.CompactTextString(m) }
func (*CircleActivityDetail) ProtoMessage()               {}
func (*CircleActivityDetail) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{52} }

func (m *CircleActivityDetail) GetActivityId() uint32 {
	if m != nil {
		return m.ActivityId
	}
	return 0
}

func (m *CircleActivityDetail) GetActUrl() string {
	if m != nil {
		return m.ActUrl
	}
	return ""
}

func (m *CircleActivityDetail) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *CircleActivityDetail) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CircleActivityDetail) GetWarmedUpTsBegin() uint32 {
	if m != nil {
		return m.WarmedUpTsBegin
	}
	return 0
}

func (m *CircleActivityDetail) GetWarmedUpTsEnd() uint32 {
	if m != nil {
		return m.WarmedUpTsEnd
	}
	return 0
}

func (m *CircleActivityDetail) GetActiveTsBegin() uint32 {
	if m != nil {
		return m.ActiveTsBegin
	}
	return 0
}

func (m *CircleActivityDetail) GetActiveTsEnd() uint32 {
	if m != nil {
		return m.ActiveTsEnd
	}
	return 0
}

func (m *CircleActivityDetail) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type CircleGetActivityListReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	IsHomePage uint32      `protobuf:"varint,2,req,name=is_home_page,json=isHomePage" json:"is_home_page"`
	StartIndex uint32      `protobuf:"varint,3,opt,name=start_index,json=startIndex" json:"start_index"`
}

func (m *CircleGetActivityListReq) Reset()         { *m = CircleGetActivityListReq{} }
func (m *CircleGetActivityListReq) String() string { return proto.CompactTextString(m) }
func (*CircleGetActivityListReq) ProtoMessage()    {}
func (*CircleGetActivityListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{53}
}

func (m *CircleGetActivityListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetActivityListReq) GetIsHomePage() uint32 {
	if m != nil {
		return m.IsHomePage
	}
	return 0
}

func (m *CircleGetActivityListReq) GetStartIndex() uint32 {
	if m != nil {
		return m.StartIndex
	}
	return 0
}

type CircleGetActivityListResp struct {
	BaseResp     *ga.BaseResp            `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ActivityList []*CircleActivityDetail `protobuf:"bytes,2,rep,name=activity_list,json=activityList" json:"activity_list,omitempty"`
	IsHomePage   uint32                  `protobuf:"varint,3,req,name=is_home_page,json=isHomePage" json:"is_home_page"`
}

func (m *CircleGetActivityListResp) Reset()         { *m = CircleGetActivityListResp{} }
func (m *CircleGetActivityListResp) String() string { return proto.CompactTextString(m) }
func (*CircleGetActivityListResp) ProtoMessage()    {}
func (*CircleGetActivityListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{54}
}

func (m *CircleGetActivityListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetActivityListResp) GetActivityList() []*CircleActivityDetail {
	if m != nil {
		return m.ActivityList
	}
	return nil
}

func (m *CircleGetActivityListResp) GetIsHomePage() uint32 {
	if m != nil {
		return m.IsHomePage
	}
	return 0
}

type CircleGetHotReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *CircleGetHotReq) Reset()                    { *m = CircleGetHotReq{} }
func (m *CircleGetHotReq) String() string            { return proto.CompactTextString(m) }
func (*CircleGetHotReq) ProtoMessage()               {}
func (*CircleGetHotReq) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{55} }

func (m *CircleGetHotReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type CircleGetHotResp struct {
	BaseResp      *ga.BaseResp         `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	HotCircleList []*CircleDynamicData `protobuf:"bytes,2,rep,name=hot_circle_list,json=hotCircleList" json:"hot_circle_list,omitempty"`
}

func (m *CircleGetHotResp) Reset()                    { *m = CircleGetHotResp{} }
func (m *CircleGetHotResp) String() string            { return proto.CompactTextString(m) }
func (*CircleGetHotResp) ProtoMessage()               {}
func (*CircleGetHotResp) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{56} }

func (m *CircleGetHotResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetHotResp) GetHotCircleList() []*CircleDynamicData {
	if m != nil {
		return m.HotCircleList
	}
	return nil
}

// 评论回复的目标
type StCommentReplayTargetBase struct {
	CommentId      uint32         `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	Creator        *ga.CircleUser `protobuf:"bytes,2,req,name=creator" json:"creator,omitempty"`
	CreateTime     uint32         `protobuf:"varint,3,req,name=create_time,json=createTime" json:"create_time"`
	Status         uint32         `protobuf:"varint,4,req,name=status" json:"status"`
	CreateTimeDesc string         `protobuf:"bytes,5,req,name=create_time_desc,json=createTimeDesc" json:"create_time_desc"`
}

func (m *StCommentReplayTargetBase) Reset()         { *m = StCommentReplayTargetBase{} }
func (m *StCommentReplayTargetBase) String() string { return proto.CompactTextString(m) }
func (*StCommentReplayTargetBase) ProtoMessage()    {}
func (*StCommentReplayTargetBase) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{57}
}

func (m *StCommentReplayTargetBase) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *StCommentReplayTargetBase) GetCreator() *ga.CircleUser {
	if m != nil {
		return m.Creator
	}
	return nil
}

func (m *StCommentReplayTargetBase) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StCommentReplayTargetBase) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StCommentReplayTargetBase) GetCreateTimeDesc() string {
	if m != nil {
		return m.CreateTimeDesc
	}
	return ""
}

// 一条评论的基本数据
type StCommentBase struct {
	CommentId      uint32                     `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	Content        string                     `protobuf:"bytes,2,req,name=content" json:"content"`
	Creator        *ga.CircleUser             `protobuf:"bytes,3,req,name=creator" json:"creator,omitempty"`
	CreateTime     uint32                     `protobuf:"varint,4,req,name=create_time,json=createTime" json:"create_time"`
	Status         uint32                     `protobuf:"varint,5,req,name=status" json:"status"`
	Type           uint32                     `protobuf:"varint,6,req,name=type" json:"type"`
	CreateTimeDesc string                     `protobuf:"bytes,7,req,name=create_time_desc,json=createTimeDesc" json:"create_time_desc"`
	ReplyTarget    *StCommentReplayTargetBase `protobuf:"bytes,8,opt,name=reply_target,json=replyTarget" json:"reply_target,omitempty"`
}

func (m *StCommentBase) Reset()                    { *m = StCommentBase{} }
func (m *StCommentBase) String() string            { return proto.CompactTextString(m) }
func (*StCommentBase) ProtoMessage()               {}
func (*StCommentBase) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{58} }

func (m *StCommentBase) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *StCommentBase) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *StCommentBase) GetCreator() *ga.CircleUser {
	if m != nil {
		return m.Creator
	}
	return nil
}

func (m *StCommentBase) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *StCommentBase) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StCommentBase) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *StCommentBase) GetCreateTimeDesc() string {
	if m != nil {
		return m.CreateTimeDesc
	}
	return ""
}

func (m *StCommentBase) GetReplyTarget() *StCommentReplayTargetBase {
	if m != nil {
		return m.ReplyTarget
	}
	return nil
}

// 普通评论
type CircleTopicNomalComment struct {
	CommentBase      *StCommentBase        `protobuf:"bytes,1,req,name=comment_base,json=commentBase" json:"comment_base,omitempty"`
	Floor            int32                 `protobuf:"varint,2,req,name=floor" json:"floor"`
	ImageList        []*CircleCommentImage `protobuf:"bytes,3,rep,name=image_list,json=imageList" json:"image_list,omitempty"`
	ReplyCommentList []*StCommentBase      `protobuf:"bytes,4,rep,name=reply_comment_list,json=replyCommentList" json:"reply_comment_list,omitempty"`
	ReplyTotalCnt    uint32                `protobuf:"varint,5,opt,name=reply_total_cnt,json=replyTotalCnt" json:"reply_total_cnt"`
}

func (m *CircleTopicNomalComment) Reset()                    { *m = CircleTopicNomalComment{} }
func (m *CircleTopicNomalComment) String() string            { return proto.CompactTextString(m) }
func (*CircleTopicNomalComment) ProtoMessage()               {}
func (*CircleTopicNomalComment) Descriptor() ([]byte, []int) { return fileDescriptorCircle2_, []int{59} }

func (m *CircleTopicNomalComment) GetCommentBase() *StCommentBase {
	if m != nil {
		return m.CommentBase
	}
	return nil
}

func (m *CircleTopicNomalComment) GetFloor() int32 {
	if m != nil {
		return m.Floor
	}
	return 0
}

func (m *CircleTopicNomalComment) GetImageList() []*CircleCommentImage {
	if m != nil {
		return m.ImageList
	}
	return nil
}

func (m *CircleTopicNomalComment) GetReplyCommentList() []*StCommentBase {
	if m != nil {
		return m.ReplyCommentList
	}
	return nil
}

func (m *CircleTopicNomalComment) GetReplyTotalCnt() uint32 {
	if m != nil {
		return m.ReplyTotalCnt
	}
	return 0
}

// 获取普通评论列表
type CircleGetNomalCommentListReq struct {
	BaseReq        *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId       uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId        uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	StartCommentId uint32      `protobuf:"varint,4,req,name=start_comment_id,json=startCommentId" json:"start_comment_id"`
	Count          uint32      `protobuf:"varint,5,req,name=count" json:"count"`
	IncludeStartId bool        `protobuf:"varint,6,opt,name=include_start_id,json=includeStartId" json:"include_start_id"`
	IsDesc         bool        `protobuf:"varint,7,opt,name=is_desc,json=isDesc" json:"is_desc"`
}

func (m *CircleGetNomalCommentListReq) Reset()         { *m = CircleGetNomalCommentListReq{} }
func (m *CircleGetNomalCommentListReq) String() string { return proto.CompactTextString(m) }
func (*CircleGetNomalCommentListReq) ProtoMessage()    {}
func (*CircleGetNomalCommentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{60}
}

func (m *CircleGetNomalCommentListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetNomalCommentListReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetNomalCommentListReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetNomalCommentListReq) GetStartCommentId() uint32 {
	if m != nil {
		return m.StartCommentId
	}
	return 0
}

func (m *CircleGetNomalCommentListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CircleGetNomalCommentListReq) GetIncludeStartId() bool {
	if m != nil {
		return m.IncludeStartId
	}
	return false
}

func (m *CircleGetNomalCommentListReq) GetIsDesc() bool {
	if m != nil {
		return m.IsDesc
	}
	return false
}

type CircleGetNomalCommentListResp struct {
	BaseResp       *ga.BaseResp               `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId       uint32                     `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId        uint32                     `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	StartCommentId uint32                     `protobuf:"varint,4,req,name=start_comment_id,json=startCommentId" json:"start_comment_id"`
	CommentList    []*CircleTopicNomalComment `protobuf:"bytes,5,rep,name=comment_list,json=commentList" json:"comment_list,omitempty"`
	NomalLeftCnt   uint32                     `protobuf:"varint,6,opt,name=nomal_left_cnt,json=nomalLeftCnt" json:"nomal_left_cnt"`
}

func (m *CircleGetNomalCommentListResp) Reset()         { *m = CircleGetNomalCommentListResp{} }
func (m *CircleGetNomalCommentListResp) String() string { return proto.CompactTextString(m) }
func (*CircleGetNomalCommentListResp) ProtoMessage()    {}
func (*CircleGetNomalCommentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{61}
}

func (m *CircleGetNomalCommentListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetNomalCommentListResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetNomalCommentListResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetNomalCommentListResp) GetStartCommentId() uint32 {
	if m != nil {
		return m.StartCommentId
	}
	return 0
}

func (m *CircleGetNomalCommentListResp) GetCommentList() []*CircleTopicNomalComment {
	if m != nil {
		return m.CommentList
	}
	return nil
}

func (m *CircleGetNomalCommentListResp) GetNomalLeftCnt() uint32 {
	if m != nil {
		return m.NomalLeftCnt
	}
	return 0
}

// 获取指定评论的回复列表
type CircleGetCommentReplyListReq struct {
	BaseReq             *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	CircleId            uint32      `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId             uint32      `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	ParentCommentId     uint32      `protobuf:"varint,4,req,name=parent_comment_id,json=parentCommentId" json:"parent_comment_id"`
	StartReplyCommentId uint32      `protobuf:"varint,5,req,name=start_reply_comment_id,json=startReplyCommentId" json:"start_reply_comment_id"`
	Count               uint32      `protobuf:"varint,6,req,name=count" json:"count"`
}

func (m *CircleGetCommentReplyListReq) Reset()         { *m = CircleGetCommentReplyListReq{} }
func (m *CircleGetCommentReplyListReq) String() string { return proto.CompactTextString(m) }
func (*CircleGetCommentReplyListReq) ProtoMessage()    {}
func (*CircleGetCommentReplyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{62}
}

func (m *CircleGetCommentReplyListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CircleGetCommentReplyListReq) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetCommentReplyListReq) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetCommentReplyListReq) GetParentCommentId() uint32 {
	if m != nil {
		return m.ParentCommentId
	}
	return 0
}

func (m *CircleGetCommentReplyListReq) GetStartReplyCommentId() uint32 {
	if m != nil {
		return m.StartReplyCommentId
	}
	return 0
}

func (m *CircleGetCommentReplyListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type CircleGetCommentReplyListResp struct {
	BaseResp            *ga.BaseResp     `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	CircleId            uint32           `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId             uint32           `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	ParentCommentId     uint32           `protobuf:"varint,4,req,name=parent_comment_id,json=parentCommentId" json:"parent_comment_id"`
	StartReplyCommentId uint32           `protobuf:"varint,5,req,name=start_reply_comment_id,json=startReplyCommentId" json:"start_reply_comment_id"`
	ReplyList           []*StCommentBase `protobuf:"bytes,6,rep,name=reply_list,json=replyList" json:"reply_list,omitempty"`
	ReplyTotalCnt       uint32           `protobuf:"varint,7,opt,name=reply_total_cnt,json=replyTotalCnt" json:"reply_total_cnt"`
	ReplyLeftCnt        uint32           `protobuf:"varint,8,opt,name=reply_left_cnt,json=replyLeftCnt" json:"reply_left_cnt"`
}

func (m *CircleGetCommentReplyListResp) Reset()         { *m = CircleGetCommentReplyListResp{} }
func (m *CircleGetCommentReplyListResp) String() string { return proto.CompactTextString(m) }
func (*CircleGetCommentReplyListResp) ProtoMessage()    {}
func (*CircleGetCommentReplyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircle2_, []int{63}
}

func (m *CircleGetCommentReplyListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CircleGetCommentReplyListResp) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleGetCommentReplyListResp) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *CircleGetCommentReplyListResp) GetParentCommentId() uint32 {
	if m != nil {
		return m.ParentCommentId
	}
	return 0
}

func (m *CircleGetCommentReplyListResp) GetStartReplyCommentId() uint32 {
	if m != nil {
		return m.StartReplyCommentId
	}
	return 0
}

func (m *CircleGetCommentReplyListResp) GetReplyList() []*StCommentBase {
	if m != nil {
		return m.ReplyList
	}
	return nil
}

func (m *CircleGetCommentReplyListResp) GetReplyTotalCnt() uint32 {
	if m != nil {
		return m.ReplyTotalCnt
	}
	return 0
}

func (m *CircleGetCommentReplyListResp) GetReplyLeftCnt() uint32 {
	if m != nil {
		return m.ReplyLeftCnt
	}
	return 0
}

func init() {
	proto.RegisterType((*CircleKeeper)(nil), "ga.CircleKeeper")
	proto.RegisterType((*CircleTopicImage)(nil), "ga.CircleTopicImage")
	proto.RegisterType((*CircleTopic)(nil), "ga.CircleTopic")
	proto.RegisterType((*CircleCommentImage)(nil), "ga.CircleCommentImage")
	proto.RegisterType((*CircleTopicComment)(nil), "ga.CircleTopicComment")
	proto.RegisterType((*CircleDynamicData)(nil), "ga.CircleDynamicData")
	proto.RegisterType((*CircleGetListReq)(nil), "ga.CircleGetListReq")
	proto.RegisterType((*CircleGetListResp)(nil), "ga.CircleGetListResp")
	proto.RegisterType((*CircleJoinReq)(nil), "ga.CircleJoinReq")
	proto.RegisterType((*CircleJoinResp)(nil), "ga.CircleJoinResp")
	proto.RegisterType((*CircleQuitReq)(nil), "ga.CircleQuitReq")
	proto.RegisterType((*CircleQuitResp)(nil), "ga.CircleQuitResp")
	proto.RegisterType((*CircleGetTopicListReq)(nil), "ga.CircleGetTopicListReq")
	proto.RegisterType((*CircleGetTopicListResp)(nil), "ga.CircleGetTopicListResp")
	proto.RegisterType((*CircleGetTopicReq)(nil), "ga.CircleGetTopicReq")
	proto.RegisterType((*CircleGetTopicResp)(nil), "ga.CircleGetTopicResp")
	proto.RegisterType((*CirclePostTopicReq)(nil), "ga.CirclePostTopicReq")
	proto.RegisterType((*CirclePostTopicResp)(nil), "ga.CirclePostTopicResp")
	proto.RegisterType((*CircleGetCommentListReq)(nil), "ga.CircleGetCommentListReq")
	proto.RegisterType((*CircleGetCommentListResp)(nil), "ga.CircleGetCommentListResp")
	proto.RegisterType((*CirclePostCommentReq)(nil), "ga.CirclePostCommentReq")
	proto.RegisterType((*CirclePostCommentResp)(nil), "ga.CirclePostCommentResp")
	proto.RegisterType((*CircleLikeTopicReq)(nil), "ga.CircleLikeTopicReq")
	proto.RegisterType((*CircleLikeTopicResp)(nil), "ga.CircleLikeTopicResp")
	proto.RegisterType((*CircleReportTopicReq)(nil), "ga.CircleReportTopicReq")
	proto.RegisterType((*CircleReportTopicResp)(nil), "ga.CircleReportTopicResp")
	proto.RegisterType((*CircleDeleteTopicReq)(nil), "ga.CircleDeleteTopicReq")
	proto.RegisterType((*CircleDeleteTopicResp)(nil), "ga.CircleDeleteTopicResp")
	proto.RegisterType((*CircleDeleteCommentReq)(nil), "ga.CircleDeleteCommentReq")
	proto.RegisterType((*CircleDeleteCommentResp)(nil), "ga.CircleDeleteCommentResp")
	proto.RegisterType((*CircleGetCircleDetailReq)(nil), "ga.CircleGetCircleDetailReq")
	proto.RegisterType((*CircleGetCircleDetailResp)(nil), "ga.CircleGetCircleDetailResp")
	proto.RegisterType((*CircleGetUserTopicReq)(nil), "ga.CircleGetUserTopicReq")
	proto.RegisterType((*CircleGetUserTopicResp)(nil), "ga.CircleGetUserTopicResp")
	proto.RegisterType((*CircleGetLikeUserListReq)(nil), "ga.CircleGetLikeUserListReq")
	proto.RegisterType((*CircleGetLikeUserListResp)(nil), "ga.CircleGetLikeUserListResp")
	proto.RegisterType((*CircleMarkReadedReq)(nil), "ga.CircleMarkReadedReq")
	proto.RegisterType((*CircleMarkReadedResp)(nil), "ga.CircleMarkReadedResp")
	proto.RegisterType((*CircleMuteUserReq)(nil), "ga.CircleMuteUserReq")
	proto.RegisterType((*CircleMuteUserResp)(nil), "ga.CircleMuteUserResp")
	proto.RegisterType((*CircleUnmuteUserReq)(nil), "ga.CircleUnmuteUserReq")
	proto.RegisterType((*CircleUnmuteUserResp)(nil), "ga.CircleUnmuteUserResp")
	proto.RegisterType((*MyCircleOrderReq)(nil), "ga.MyCircleOrderReq")
	proto.RegisterType((*MyCircleOrderResp)(nil), "ga.MyCircleOrderResp")
	proto.RegisterType((*CircleHighlightTopicReq)(nil), "ga.CircleHighlightTopicReq")
	proto.RegisterType((*CircleHighlightTopicResp)(nil), "ga.CircleHighlightTopicResp")
	proto.RegisterType((*CircleCancelHighlightTopicReq)(nil), "ga.CircleCancelHighlightTopicReq")
	proto.RegisterType((*CircleCancelHighlightTopicResp)(nil), "ga.CircleCancelHighlightTopicResp")
	proto.RegisterType((*CircleManagerDeleteCommentReq)(nil), "ga.CircleManagerDeleteCommentReq")
	proto.RegisterType((*CircleManagerDeleteCommentResp)(nil), "ga.CircleManagerDeleteCommentResp")
	proto.RegisterType((*CircleCheckUpdateReq)(nil), "ga.CircleCheckUpdateReq")
	proto.RegisterType((*CircleCheckUpdateResp)(nil), "ga.CircleCheckUpdateResp")
	proto.RegisterType((*CircleActivityDetail)(nil), "ga.CircleActivityDetail")
	proto.RegisterType((*CircleGetActivityListReq)(nil), "ga.CircleGetActivityListReq")
	proto.RegisterType((*CircleGetActivityListResp)(nil), "ga.CircleGetActivityListResp")
	proto.RegisterType((*CircleGetHotReq)(nil), "ga.CircleGetHotReq")
	proto.RegisterType((*CircleGetHotResp)(nil), "ga.CircleGetHotResp")
	proto.RegisterType((*StCommentReplayTargetBase)(nil), "ga.StCommentReplayTargetBase")
	proto.RegisterType((*StCommentBase)(nil), "ga.StCommentBase")
	proto.RegisterType((*CircleTopicNomalComment)(nil), "ga.CircleTopicNomalComment")
	proto.RegisterType((*CircleGetNomalCommentListReq)(nil), "ga.CircleGetNomalCommentListReq")
	proto.RegisterType((*CircleGetNomalCommentListResp)(nil), "ga.CircleGetNomalCommentListResp")
	proto.RegisterType((*CircleGetCommentReplyListReq)(nil), "ga.CircleGetCommentReplyListReq")
	proto.RegisterType((*CircleGetCommentReplyListResp)(nil), "ga.CircleGetCommentReplyListResp")
	proto.RegisterEnum("ga.CircleListRequestType", CircleListRequestType_name, CircleListRequestType_value)
	proto.RegisterEnum("ga.CircleTopicListReqType", CircleTopicListReqType_name, CircleTopicListReqType_value)
	proto.RegisterEnum("ga.CircleTopicCommentStatus", CircleTopicCommentStatus_name, CircleTopicCommentStatus_value)
	proto.RegisterEnum("ga.CircleTopicCommentType", CircleTopicCommentType_name, CircleTopicCommentType_value)
	proto.RegisterEnum("ga.CircleKeeper_KEEPR_TYPE", CircleKeeper_KEEPR_TYPE_name, CircleKeeper_KEEPR_TYPE_value)
	proto.RegisterEnum("ga.CircleTopic_TOPIC_TYPE", CircleTopic_TOPIC_TYPE_name, CircleTopic_TOPIC_TYPE_value)
	proto.RegisterEnum("ga.CircleTopic_TOPIC_STATE", CircleTopic_TOPIC_STATE_name, CircleTopic_TOPIC_STATE_value)
	proto.RegisterEnum("ga.CircleTopicComment_TopicCommentStatus", CircleTopicComment_TopicCommentStatus_name, CircleTopicComment_TopicCommentStatus_value)
}
func (m *CircleKeeper) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleKeeper) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.NickName)))
	i += copy(dAtA[i:], m.NickName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.KeeperType))
	return i, nil
}

func (m *CircleTopicImage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleTopicImage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.ThumbUrl)))
	i += copy(dAtA[i:], m.ThumbUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.ImageUrl)))
	i += copy(dAtA[i:], m.ImageUrl)
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ImageWidth))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ImageHeight))
	return i, nil
}

func (m *CircleTopic) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleTopic) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CreateTime))
	if len(m.ImageList) > 0 {
		for _, msg := range m.ImageList {
			dAtA[i] = 0x3a
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x40
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.LikeCount))
	dAtA[i] = 0x48
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentCount))
	dAtA[i] = 0x50
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.IsLiked))
	dAtA[i] = 0x58
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicState))
	dAtA[i] = 0x60
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.LastCommentTime))
	if m.Creator != nil {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.Creator.Size()))
		n1, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x72
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.CreateTimeDesc)))
	i += copy(dAtA[i:], m.CreateTimeDesc)
	dAtA[i] = 0x7a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.LastCommentTimeDesc)))
	i += copy(dAtA[i:], m.LastCommentTimeDesc)
	if m.DownloadInfo != nil {
		dAtA[i] = 0x82
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.DownloadInfo.Size()))
		n2, err := m.DownloadInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *CircleCommentImage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleCommentImage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.ThumbUrl)))
	i += copy(dAtA[i:], m.ThumbUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.ImageUrl)))
	i += copy(dAtA[i:], m.ImageUrl)
	return i, nil
}

func (m *CircleTopicComment) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleTopicComment) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if m.Creator == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	} else {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.Creator.Size()))
		n3, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x38
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Status))
	if m.RepliedComment != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.RepliedComment.Size()))
		n4, err := m.RepliedComment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x4a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.CreateTimeDesc)))
	i += copy(dAtA[i:], m.CreateTimeDesc)
	if len(m.ImageList) > 0 {
		for _, msg := range m.ImageList {
			dAtA[i] = 0x52
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x58
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Floor))
	return i, nil
}

func (m *CircleDynamicData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleDynamicData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.FollowerNumber))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.GuildFollowerNumber))
	dAtA[i] = 0x20
	i++
	if m.IsFollow {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if len(m.KeeperList) > 0 {
		for _, msg := range m.KeeperList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x32
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.CircleName)))
	i += copy(dAtA[i:], m.CircleName)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Icon)))
	i += copy(dAtA[i:], m.Icon)
	dAtA[i] = 0x40
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x48
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicCount))
	dAtA[i] = 0x50
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TodayTopicCount))
	dAtA[i] = 0x58
	i++
	if m.GameDownloadable {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x62
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.ActTag)))
	i += copy(dAtA[i:], m.ActTag)
	dAtA[i] = 0x6a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.ActDesc)))
	i += copy(dAtA[i:], m.ActDesc)
	dAtA[i] = 0x70
	i++
	if m.HasWelfare {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x78
	i++
	if m.IsAnncouncementCircle {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.GameType))
	return i, nil
}

func (m *CircleGetListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ReqType))
	return i, nil
}

func (m *CircleGetListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ReqType))
	if len(m.CircleDynamicDataList) > 0 {
		for _, msg := range m.CircleDynamicDataList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CircleJoinReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleJoinReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if len(m.CircleIdList) > 0 {
		for _, num := range m.CircleIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CircleJoinResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleJoinResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if len(m.CircleIdList) > 0 {
		for _, num := range m.CircleIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(num))
		}
	}
	if len(m.FailedIdList) > 0 {
		for _, num := range m.FailedIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CircleQuitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleQuitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	return i, nil
}

func (m *CircleQuitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleQuitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	return i, nil
}

func (m *CircleGetTopicListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetTopicListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.PageCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.PagePosition))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ReqType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Userfrom))
	return i, nil
}

func (m *CircleGetTopicListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetTopicListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.PageCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.PagePosition))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ReqType))
	if len(m.TopicList) > 0 {
		for _, msg := range m.TopicList {
			dAtA[i] = 0x32
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.NewestTopicId))
	return i, nil
}

func (m *CircleGetTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n13, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.LikeUserCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopCommentCount))
	return i, nil
}

func (m *CircleGetTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n14, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	if m.Topic == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic")
	} else {
		dAtA[i] = 0x22
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.Topic.Size()))
		n15, err := m.Topic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	if len(m.LikeUserList) > 0 {
		for _, s := range m.LikeUserList {
			dAtA[i] = 0x2a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.TopCommentList) > 0 {
		for _, msg := range m.TopCommentList {
			dAtA[i] = 0x32
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CirclePostTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CirclePostTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n16, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.ClientId)))
	i += copy(dAtA[i:], m.ClientId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			dAtA[i] = 0x32
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *CirclePostTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CirclePostTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n17, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.ClientId)))
	i += copy(dAtA[i:], m.ClientId)
	if m.Topic == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.Topic.Size()))
		n18, err := m.Topic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	return i, nil
}

func (m *CircleGetCommentListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetCommentListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartCommentId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentCount))
	return i, nil
}

func (m *CircleGetCommentListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetCommentListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n20, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartCommentId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentCount))
	if len(m.CommentList) > 0 {
		for _, msg := range m.CommentList {
			dAtA[i] = 0x32
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CirclePostCommentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CirclePostCommentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n21, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.RepliedCommentId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			dAtA[i] = 0x32
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *CirclePostCommentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CirclePostCommentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n22, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.RepliedCommentId))
	if m.Comment == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment")
	} else {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.Comment.Size()))
		n23, err := m.Comment.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	return i, nil
}

func (m *CircleLikeTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleLikeTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n24, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	if m.IsLike {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CircleLikeTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleLikeTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n25, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	if m.IsLike {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CircleReportTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleReportTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n26, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *CircleReportTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleReportTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n27, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *CircleDeleteTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleDeleteTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n28, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *CircleDeleteTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleDeleteTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n29, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *CircleDeleteCommentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleDeleteCommentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n30, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentId))
	return i, nil
}

func (m *CircleDeleteCommentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleDeleteCommentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n31, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentId))
	return i, nil
}

func (m *CircleGetCircleDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetCircleDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n32, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n32
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	return i, nil
}

func (m *CircleGetCircleDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetCircleDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n33, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n33
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	if m.Circle == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.Circle.Size()))
		n34, err := m.Circle.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n34
	}
	if m.CircleDynamic == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle_dynamic")
	} else {
		dAtA[i] = 0x22
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleDynamic.Size()))
		n35, err := m.CircleDynamic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n35
	}
	return i, nil
}

func (m *CircleGetUserTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetUserTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n36, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n36
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartCircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartTopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CircleGetUserTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetUserTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n37, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n37
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartCircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartTopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicCount))
	if len(m.TopicList) > 0 {
		for _, msg := range m.TopicList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CircleGetLikeUserListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetLikeUserListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n38, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n38
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *CircleGetLikeUserListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetLikeUserListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n39, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n39
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Limit))
	if len(m.UserNickList) > 0 {
		for _, s := range m.UserNickList {
			dAtA[i] = 0x32
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *CircleMarkReadedReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleMarkReadedReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n40, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n40
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.SvrMsgId))
	return i, nil
}

func (m *CircleMarkReadedResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleMarkReadedResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n41, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n41
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.SvrMsgId))
	return i, nil
}

func (m *CircleMuteUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleMuteUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n42, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n42
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Uid))
	if len(m.CircleIdList) > 0 {
		for _, num := range m.CircleIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Days))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Secs))
	dAtA[i] = 0x32
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Reason)))
	i += copy(dAtA[i:], m.Reason)
	dAtA[i] = 0x38
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.BecauseCircleId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.BecauseTopicId))
	return i, nil
}

func (m *CircleMuteUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleMuteUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n43, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n43
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Uid))
	if len(m.CircleIdList) > 0 {
		for _, num := range m.CircleIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Days))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Secs))
	return i, nil
}

func (m *CircleUnmuteUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleUnmuteUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n44, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n44
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Uid))
	if len(m.CircleIdList) > 0 {
		for _, num := range m.CircleIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CircleUnmuteUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleUnmuteUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n45, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n45
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Uid))
	if len(m.CircleIdList) > 0 {
		for _, num := range m.CircleIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *MyCircleOrderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MyCircleOrderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n46, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n46
	}
	if len(m.CircleIdList) > 0 {
		for _, num := range m.CircleIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *MyCircleOrderResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MyCircleOrderResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n47, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n47
	}
	if len(m.CircleIdList) > 0 {
		for _, num := range m.CircleIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CircleHighlightTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleHighlightTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n48, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n48
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *CircleHighlightTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleHighlightTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n49, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n49
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *CircleCancelHighlightTopicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleCancelHighlightTopicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n50, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n50
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *CircleCancelHighlightTopicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleCancelHighlightTopicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n51, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n51
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *CircleManagerDeleteCommentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleManagerDeleteCommentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n52, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n52
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentId))
	return i, nil
}

func (m *CircleManagerDeleteCommentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleManagerDeleteCommentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n53, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n53
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentId))
	return i, nil
}

func (m *CircleCheckUpdateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleCheckUpdateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n54, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n54
	}
	if len(m.TopicUpdateInfo) > 0 {
		for _, msg := range m.TopicUpdateInfo {
			dAtA[i] = 0x12
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CircleCheckUpdateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleCheckUpdateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n55, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n55
	}
	if len(m.TopicUpdateInfo) > 0 {
		for _, msg := range m.TopicUpdateInfo {
			dAtA[i] = 0x12
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.NewestCircleId))
	return i, nil
}

func (m *CircleActivityDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleActivityDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ActivityId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.ActUrl)))
	i += copy(dAtA[i:], m.ActUrl)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.WarmedUpTsBegin))
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.WarmedUpTsEnd))
	dAtA[i] = 0x38
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ActiveTsBegin))
	dAtA[i] = 0x40
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ActiveTsEnd))
	dAtA[i] = 0x48
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.EndTs))
	return i, nil
}

func (m *CircleGetActivityListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetActivityListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n56, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n56
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.IsHomePage))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartIndex))
	return i, nil
}

func (m *CircleGetActivityListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetActivityListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n57, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n57
	}
	if len(m.ActivityList) > 0 {
		for _, msg := range m.ActivityList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.IsHomePage))
	return i, nil
}

func (m *CircleGetHotReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetHotReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n58, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n58
	}
	return i, nil
}

func (m *CircleGetHotResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetHotResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n59, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n59
	}
	if len(m.HotCircleList) > 0 {
		for _, msg := range m.HotCircleList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StCommentReplayTargetBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StCommentReplayTargetBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentId))
	if m.Creator == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.Creator.Size()))
		n60, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n60
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.CreateTimeDesc)))
	i += copy(dAtA[i:], m.CreateTimeDesc)
	return i, nil
}

func (m *StCommentBase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StCommentBase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if m.Creator == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("creator")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.Creator.Size()))
		n61, err := m.Creator.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n61
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(len(m.CreateTimeDesc)))
	i += copy(dAtA[i:], m.CreateTimeDesc)
	if m.ReplyTarget != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.ReplyTarget.Size()))
		n62, err := m.ReplyTarget.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n62
	}
	return i, nil
}

func (m *CircleTopicNomalComment) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleTopicNomalComment) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CommentBase == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_base")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.CommentBase.Size()))
		n63, err := m.CommentBase.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n63
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Floor))
	if len(m.ImageList) > 0 {
		for _, msg := range m.ImageList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ReplyCommentList) > 0 {
		for _, msg := range m.ReplyCommentList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ReplyTotalCnt))
	return i, nil
}

func (m *CircleGetNomalCommentListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetNomalCommentListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n64, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n64
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartCommentId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x30
	i++
	if m.IncludeStartId {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	if m.IsDesc {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CircleGetNomalCommentListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetNomalCommentListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n65, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n65
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartCommentId))
	if len(m.CommentList) > 0 {
		for _, msg := range m.CommentList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.NomalLeftCnt))
	return i, nil
}

func (m *CircleGetCommentReplyListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetCommentReplyListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseReq.Size()))
		n66, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n66
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ParentCommentId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartReplyCommentId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *CircleGetCommentReplyListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleGetCommentReplyListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCircle2_(dAtA, i, uint64(m.BaseResp.Size()))
		n67, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n67
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ParentCommentId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.StartReplyCommentId))
	if len(m.ReplyList) > 0 {
		for _, msg := range m.ReplyList {
			dAtA[i] = 0x32
			i++
			i = encodeVarintCircle2_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ReplyTotalCnt))
	dAtA[i] = 0x40
	i++
	i = encodeVarintCircle2_(dAtA, i, uint64(m.ReplyLeftCnt))
	return i, nil
}

func encodeFixed64Circle2_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Circle2_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintCircle2_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CircleKeeper) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircle2_(uint64(m.Uid))
	l = len(m.Account)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.NickName)
	n += 1 + l + sovCircle2_(uint64(l))
	n += 1 + sovCircle2_(uint64(m.Sex))
	n += 1 + sovCircle2_(uint64(m.KeeperType))
	return n
}

func (m *CircleTopicImage) Size() (n int) {
	var l int
	_ = l
	l = len(m.ThumbUrl)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.ImageUrl)
	n += 1 + l + sovCircle2_(uint64(l))
	n += 1 + sovCircle2_(uint64(m.ImageWidth))
	n += 1 + sovCircle2_(uint64(m.ImageHeight))
	return n
}

func (m *CircleTopic) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircle2_(uint64(m.Type))
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	l = len(m.Title)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovCircle2_(uint64(l))
	n += 1 + sovCircle2_(uint64(m.CreateTime))
	if len(m.ImageList) > 0 {
		for _, e := range m.ImageList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	n += 1 + sovCircle2_(uint64(m.LikeCount))
	n += 1 + sovCircle2_(uint64(m.CommentCount))
	n += 1 + sovCircle2_(uint64(m.IsLiked))
	n += 1 + sovCircle2_(uint64(m.TopicState))
	n += 1 + sovCircle2_(uint64(m.LastCommentTime))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	l = len(m.CreateTimeDesc)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.LastCommentTimeDesc)
	n += 1 + l + sovCircle2_(uint64(l))
	if m.DownloadInfo != nil {
		l = m.DownloadInfo.Size()
		n += 2 + l + sovCircle2_(uint64(l))
	}
	return n
}

func (m *CircleCommentImage) Size() (n int) {
	var l int
	_ = l
	l = len(m.ThumbUrl)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.ImageUrl)
	n += 1 + l + sovCircle2_(uint64(l))
	return n
}

func (m *CircleTopicComment) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircle2_(uint64(m.CommentId))
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	l = len(m.Content)
	n += 1 + l + sovCircle2_(uint64(l))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CreateTime))
	n += 1 + sovCircle2_(uint64(m.Status))
	if m.RepliedComment != nil {
		l = m.RepliedComment.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	l = len(m.CreateTimeDesc)
	n += 1 + l + sovCircle2_(uint64(l))
	if len(m.ImageList) > 0 {
		for _, e := range m.ImageList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	n += 1 + sovCircle2_(uint64(m.Floor))
	return n
}

func (m *CircleDynamicData) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.FollowerNumber))
	n += 1 + sovCircle2_(uint64(m.GuildFollowerNumber))
	n += 2
	if len(m.KeeperList) > 0 {
		for _, e := range m.KeeperList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	l = len(m.CircleName)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.Icon)
	n += 1 + l + sovCircle2_(uint64(l))
	n += 1 + sovCircle2_(uint64(m.GameId))
	n += 1 + sovCircle2_(uint64(m.TopicCount))
	n += 1 + sovCircle2_(uint64(m.TodayTopicCount))
	n += 2
	l = len(m.ActTag)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.ActDesc)
	n += 1 + l + sovCircle2_(uint64(l))
	n += 2
	n += 2
	n += 2 + sovCircle2_(uint64(m.GameType))
	return n
}

func (m *CircleGetListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.ReqType))
	return n
}

func (m *CircleGetListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.ReqType))
	if len(m.CircleDynamicDataList) > 0 {
		for _, e := range m.CircleDynamicDataList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	return n
}

func (m *CircleJoinReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if len(m.CircleIdList) > 0 {
		for _, e := range m.CircleIdList {
			n += 1 + sovCircle2_(uint64(e))
		}
	}
	return n
}

func (m *CircleJoinResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if len(m.CircleIdList) > 0 {
		for _, e := range m.CircleIdList {
			n += 1 + sovCircle2_(uint64(e))
		}
	}
	if len(m.FailedIdList) > 0 {
		for _, e := range m.FailedIdList {
			n += 1 + sovCircle2_(uint64(e))
		}
	}
	return n
}

func (m *CircleQuitReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	return n
}

func (m *CircleQuitResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	return n
}

func (m *CircleGetTopicListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.PageCount))
	n += 1 + sovCircle2_(uint64(m.PagePosition))
	n += 1 + sovCircle2_(uint64(m.ReqType))
	n += 1 + sovCircle2_(uint64(m.Userfrom))
	return n
}

func (m *CircleGetTopicListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.PageCount))
	n += 1 + sovCircle2_(uint64(m.PagePosition))
	n += 1 + sovCircle2_(uint64(m.ReqType))
	if len(m.TopicList) > 0 {
		for _, e := range m.TopicList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	n += 1 + sovCircle2_(uint64(m.NewestTopicId))
	return n
}

func (m *CircleGetTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.LikeUserCount))
	n += 1 + sovCircle2_(uint64(m.TopCommentCount))
	return n
}

func (m *CircleGetTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	if m.Topic != nil {
		l = m.Topic.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if len(m.LikeUserList) > 0 {
		for _, s := range m.LikeUserList {
			l = len(s)
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	if len(m.TopCommentList) > 0 {
		for _, e := range m.TopCommentList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	return n
}

func (m *CirclePostTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	l = len(m.ClientId)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.Title)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovCircle2_(uint64(l))
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			l = len(s)
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	return n
}

func (m *CirclePostTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	l = len(m.ClientId)
	n += 1 + l + sovCircle2_(uint64(l))
	if m.Topic != nil {
		l = m.Topic.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	return n
}

func (m *CircleGetCommentListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.StartCommentId))
	n += 1 + sovCircle2_(uint64(m.CommentCount))
	return n
}

func (m *CircleGetCommentListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.StartCommentId))
	n += 1 + sovCircle2_(uint64(m.CommentCount))
	if len(m.CommentList) > 0 {
		for _, e := range m.CommentList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	return n
}

func (m *CirclePostCommentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.RepliedCommentId))
	l = len(m.Content)
	n += 1 + l + sovCircle2_(uint64(l))
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			l = len(s)
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	return n
}

func (m *CirclePostCommentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.RepliedCommentId))
	if m.Comment != nil {
		l = m.Comment.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	return n
}

func (m *CircleLikeTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 2
	return n
}

func (m *CircleLikeTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 2
	return n
}

func (m *CircleReportTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	return n
}

func (m *CircleReportTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	return n
}

func (m *CircleDeleteTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	return n
}

func (m *CircleDeleteTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	return n
}

func (m *CircleDeleteCommentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.CommentId))
	return n
}

func (m *CircleDeleteCommentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.CommentId))
	return n
}

func (m *CircleGetCircleDetailReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	return n
}

func (m *CircleGetCircleDetailResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	if m.Circle != nil {
		l = m.Circle.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if m.CircleDynamic != nil {
		l = m.CircleDynamic.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	return n
}

func (m *CircleGetUserTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.StartCircleId))
	n += 1 + sovCircle2_(uint64(m.StartTopicId))
	n += 1 + sovCircle2_(uint64(m.TopicCount))
	n += 1 + sovCircle2_(uint64(m.Uid))
	return n
}

func (m *CircleGetUserTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.StartCircleId))
	n += 1 + sovCircle2_(uint64(m.StartTopicId))
	n += 1 + sovCircle2_(uint64(m.TopicCount))
	if len(m.TopicList) > 0 {
		for _, e := range m.TopicList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	n += 1 + sovCircle2_(uint64(m.Uid))
	return n
}

func (m *CircleGetLikeUserListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.Offset))
	n += 1 + sovCircle2_(uint64(m.Limit))
	return n
}

func (m *CircleGetLikeUserListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.Offset))
	n += 1 + sovCircle2_(uint64(m.Limit))
	if len(m.UserNickList) > 0 {
		for _, s := range m.UserNickList {
			l = len(s)
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	return n
}

func (m *CircleMarkReadedReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.SvrMsgId))
	return n
}

func (m *CircleMarkReadedResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.SvrMsgId))
	return n
}

func (m *CircleMuteUserReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.Uid))
	if len(m.CircleIdList) > 0 {
		for _, e := range m.CircleIdList {
			n += 1 + sovCircle2_(uint64(e))
		}
	}
	n += 1 + sovCircle2_(uint64(m.Days))
	n += 1 + sovCircle2_(uint64(m.Secs))
	l = len(m.Reason)
	n += 1 + l + sovCircle2_(uint64(l))
	n += 1 + sovCircle2_(uint64(m.BecauseCircleId))
	n += 1 + sovCircle2_(uint64(m.BecauseTopicId))
	return n
}

func (m *CircleMuteUserResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.Uid))
	if len(m.CircleIdList) > 0 {
		for _, e := range m.CircleIdList {
			n += 1 + sovCircle2_(uint64(e))
		}
	}
	n += 1 + sovCircle2_(uint64(m.Days))
	n += 1 + sovCircle2_(uint64(m.Secs))
	return n
}

func (m *CircleUnmuteUserReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.Uid))
	if len(m.CircleIdList) > 0 {
		for _, e := range m.CircleIdList {
			n += 1 + sovCircle2_(uint64(e))
		}
	}
	return n
}

func (m *CircleUnmuteUserResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.Uid))
	if len(m.CircleIdList) > 0 {
		for _, e := range m.CircleIdList {
			n += 1 + sovCircle2_(uint64(e))
		}
	}
	return n
}

func (m *MyCircleOrderReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if len(m.CircleIdList) > 0 {
		for _, e := range m.CircleIdList {
			n += 1 + sovCircle2_(uint64(e))
		}
	}
	return n
}

func (m *MyCircleOrderResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if len(m.CircleIdList) > 0 {
		for _, e := range m.CircleIdList {
			n += 1 + sovCircle2_(uint64(e))
		}
	}
	return n
}

func (m *CircleHighlightTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	return n
}

func (m *CircleHighlightTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	return n
}

func (m *CircleCancelHighlightTopicReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	return n
}

func (m *CircleCancelHighlightTopicResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	return n
}

func (m *CircleManagerDeleteCommentReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.CommentId))
	return n
}

func (m *CircleManagerDeleteCommentResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.CommentId))
	return n
}

func (m *CircleCheckUpdateReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if len(m.TopicUpdateInfo) > 0 {
		for _, e := range m.TopicUpdateInfo {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	return n
}

func (m *CircleCheckUpdateResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if len(m.TopicUpdateInfo) > 0 {
		for _, e := range m.TopicUpdateInfo {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	n += 1 + sovCircle2_(uint64(m.NewestCircleId))
	return n
}

func (m *CircleActivityDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircle2_(uint64(m.ActivityId))
	l = len(m.ActUrl)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.PicUrl)
	n += 1 + l + sovCircle2_(uint64(l))
	l = len(m.Title)
	n += 1 + l + sovCircle2_(uint64(l))
	n += 1 + sovCircle2_(uint64(m.WarmedUpTsBegin))
	n += 1 + sovCircle2_(uint64(m.WarmedUpTsEnd))
	n += 1 + sovCircle2_(uint64(m.ActiveTsBegin))
	n += 1 + sovCircle2_(uint64(m.ActiveTsEnd))
	n += 1 + sovCircle2_(uint64(m.EndTs))
	return n
}

func (m *CircleGetActivityListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.IsHomePage))
	n += 1 + sovCircle2_(uint64(m.StartIndex))
	return n
}

func (m *CircleGetActivityListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if len(m.ActivityList) > 0 {
		for _, e := range m.ActivityList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	n += 1 + sovCircle2_(uint64(m.IsHomePage))
	return n
}

func (m *CircleGetHotReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	return n
}

func (m *CircleGetHotResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	if len(m.HotCircleList) > 0 {
		for _, e := range m.HotCircleList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	return n
}

func (m *StCommentReplayTargetBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircle2_(uint64(m.CommentId))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CreateTime))
	n += 1 + sovCircle2_(uint64(m.Status))
	l = len(m.CreateTimeDesc)
	n += 1 + l + sovCircle2_(uint64(l))
	return n
}

func (m *StCommentBase) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircle2_(uint64(m.CommentId))
	l = len(m.Content)
	n += 1 + l + sovCircle2_(uint64(l))
	if m.Creator != nil {
		l = m.Creator.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CreateTime))
	n += 1 + sovCircle2_(uint64(m.Status))
	n += 1 + sovCircle2_(uint64(m.Type))
	l = len(m.CreateTimeDesc)
	n += 1 + l + sovCircle2_(uint64(l))
	if m.ReplyTarget != nil {
		l = m.ReplyTarget.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	return n
}

func (m *CircleTopicNomalComment) Size() (n int) {
	var l int
	_ = l
	if m.CommentBase != nil {
		l = m.CommentBase.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.Floor))
	if len(m.ImageList) > 0 {
		for _, e := range m.ImageList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	if len(m.ReplyCommentList) > 0 {
		for _, e := range m.ReplyCommentList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	n += 1 + sovCircle2_(uint64(m.ReplyTotalCnt))
	return n
}

func (m *CircleGetNomalCommentListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.StartCommentId))
	n += 1 + sovCircle2_(uint64(m.Count))
	n += 2
	n += 2
	return n
}

func (m *CircleGetNomalCommentListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.StartCommentId))
	if len(m.CommentList) > 0 {
		for _, e := range m.CommentList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	n += 1 + sovCircle2_(uint64(m.NomalLeftCnt))
	return n
}

func (m *CircleGetCommentReplyListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.ParentCommentId))
	n += 1 + sovCircle2_(uint64(m.StartReplyCommentId))
	n += 1 + sovCircle2_(uint64(m.Count))
	return n
}

func (m *CircleGetCommentReplyListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovCircle2_(uint64(l))
	}
	n += 1 + sovCircle2_(uint64(m.CircleId))
	n += 1 + sovCircle2_(uint64(m.TopicId))
	n += 1 + sovCircle2_(uint64(m.ParentCommentId))
	n += 1 + sovCircle2_(uint64(m.StartReplyCommentId))
	if len(m.ReplyList) > 0 {
		for _, e := range m.ReplyList {
			l = e.Size()
			n += 1 + l + sovCircle2_(uint64(l))
		}
	}
	n += 1 + sovCircle2_(uint64(m.ReplyTotalCnt))
	n += 1 + sovCircle2_(uint64(m.ReplyLeftCnt))
	return n
}

func sovCircle2_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozCircle2_(x uint64) (n int) {
	return sovCircle2_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *CircleKeeper) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleKeeper: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleKeeper: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NickName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NickName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KeeperType", wireType)
			}
			m.KeeperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeeperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nick_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("keeper_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleTopicImage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleTopicImage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleTopicImage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageWidth", wireType)
			}
			m.ImageWidth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ImageWidth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageHeight", wireType)
			}
			m.ImageHeight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ImageHeight |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("thumb_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("image_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleTopic) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleTopic: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleTopic: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageList = append(m.ImageList, &CircleTopicImage{})
			if err := m.ImageList[len(m.ImageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeCount", wireType)
			}
			m.LikeCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentCount", wireType)
			}
			m.CommentCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLiked", wireType)
			}
			m.IsLiked = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsLiked |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicState", wireType)
			}
			m.TopicState = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicState |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastCommentTime", wireType)
			}
			m.LastCommentTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastCommentTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &ga.CircleUser{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000800)
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastCommentTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LastCommentTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00001000)
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DownloadInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.DownloadInfo == nil {
				m.DownloadInfo = &ga.CircleTopicGameDownloadInfo{}
			}
			if err := m.DownloadInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("like_count")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_count")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_liked")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_state")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_comment_time")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time_desc")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_comment_time_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleCommentImage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleCommentImage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleCommentImage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThumbUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ThumbUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("thumb_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("image_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleTopicComment) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleTopicComment: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleTopicComment: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &ga.CircleUser{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedComment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RepliedComment == nil {
				m.RepliedComment = &CircleTopicComment{}
			}
			if err := m.RepliedComment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageList = append(m.ImageList, &CircleCommentImage{})
			if err := m.ImageList[len(m.ImageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Floor", wireType)
			}
			m.Floor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Floor |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleDynamicData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleDynamicData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleDynamicData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FollowerNumber", wireType)
			}
			m.FollowerNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FollowerNumber |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildFollowerNumber", wireType)
			}
			m.GuildFollowerNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildFollowerNumber |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsFollow", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsFollow = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KeeperList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KeeperList = append(m.KeeperList, &CircleKeeper{})
			if err := m.KeeperList[len(m.KeeperList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CircleName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Icon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicCount", wireType)
			}
			m.TopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TodayTopicCount", wireType)
			}
			m.TodayTopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TodayTopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameDownloadable", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GameDownloadable = bool(v != 0)
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActTag", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActTag = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HasWelfare", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasWelfare = bool(v != 0)
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAnncouncementCircle", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAnncouncementCircle = bool(v != 0)
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameType", wireType)
			}
			m.GameType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("follower_number")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("guild_follower_number")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_follow")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqType", wireType)
			}
			m.ReqType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("req_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqType", wireType)
			}
			m.ReqType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleDynamicDataList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CircleDynamicDataList = append(m.CircleDynamicDataList, &CircleDynamicData{})
			if err := m.CircleDynamicDataList[len(m.CircleDynamicDataList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("req_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleJoinReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleJoinReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleJoinReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CircleIdList = append(m.CircleIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircle2_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircle2_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CircleIdList = append(m.CircleIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleJoinResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleJoinResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleJoinResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CircleIdList = append(m.CircleIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircle2_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircle2_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CircleIdList = append(m.CircleIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleIdList", wireType)
			}
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.FailedIdList = append(m.FailedIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircle2_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircle2_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.FailedIdList = append(m.FailedIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field FailedIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleQuitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleQuitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleQuitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleQuitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleQuitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleQuitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetTopicListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetTopicListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetTopicListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageCount", wireType)
			}
			m.PageCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PagePosition", wireType)
			}
			m.PagePosition = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PagePosition |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqType", wireType)
			}
			m.ReqType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Userfrom", wireType)
			}
			m.Userfrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Userfrom |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("page_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("page_position")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("req_type")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("userfrom")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetTopicListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetTopicListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetTopicListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageCount", wireType)
			}
			m.PageCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PagePosition", wireType)
			}
			m.PagePosition = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PagePosition |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqType", wireType)
			}
			m.ReqType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicList = append(m.TopicList, &CircleTopic{})
			if err := m.TopicList[len(m.TopicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewestTopicId", wireType)
			}
			m.NewestTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewestTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("page_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("page_position")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("req_type")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("newest_topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUserCount", wireType)
			}
			m.LikeUserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeUserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopCommentCount", wireType)
			}
			m.TopCommentCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopCommentCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Topic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Topic == nil {
				m.Topic = &CircleTopic{}
			}
			if err := m.Topic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUserList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LikeUserList = append(m.LikeUserList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopCommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopCommentList = append(m.TopCommentList, &CircleTopicComment{})
			if err := m.TopCommentList[len(m.TopCommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CirclePostTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CirclePostTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CirclePostTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgKeyList = append(m.ImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("client_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CirclePostTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CirclePostTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CirclePostTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Topic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Topic == nil {
				m.Topic = &CircleTopic{}
			}
			if err := m.Topic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("client_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetCommentListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetCommentListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetCommentListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCommentId", wireType)
			}
			m.StartCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentCount", wireType)
			}
			m.CommentCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetCommentListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetCommentListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetCommentListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCommentId", wireType)
			}
			m.StartCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentCount", wireType)
			}
			m.CommentCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommentList = append(m.CommentList, &CircleTopicComment{})
			if err := m.CommentList[len(m.CommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CirclePostCommentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CirclePostCommentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CirclePostCommentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedCommentId", wireType)
			}
			m.RepliedCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RepliedCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgKeyList = append(m.ImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("replied_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CirclePostCommentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CirclePostCommentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CirclePostCommentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedCommentId", wireType)
			}
			m.RepliedCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RepliedCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Comment", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Comment == nil {
				m.Comment = &CircleTopicComment{}
			}
			if err := m.Comment.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("replied_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleLikeTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleLikeTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleLikeTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLike", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLike = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_like")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleLikeTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleLikeTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleLikeTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLike", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLike = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_like")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleReportTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleReportTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleReportTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleReportTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleReportTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleReportTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleDeleteTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleDeleteTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleDeleteTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleDeleteTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleDeleteTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleDeleteTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleDeleteCommentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleDeleteCommentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleDeleteCommentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleDeleteCommentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleDeleteCommentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleDeleteCommentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetCircleDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetCircleDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetCircleDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetCircleDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetCircleDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetCircleDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Circle", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Circle == nil {
				m.Circle = &ga.Circle{}
			}
			if err := m.Circle.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleDynamic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CircleDynamic == nil {
				m.CircleDynamic = &CircleDynamicData{}
			}
			if err := m.CircleDynamic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_dynamic")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetUserTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetUserTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetUserTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCircleId", wireType)
			}
			m.StartCircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTopicId", wireType)
			}
			m.StartTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicCount", wireType)
			}
			m.TopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_count")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetUserTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetUserTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetUserTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCircleId", wireType)
			}
			m.StartCircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTopicId", wireType)
			}
			m.StartTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicCount", wireType)
			}
			m.TopicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicList = append(m.TopicList, &CircleTopic{})
			if err := m.TopicList[len(m.TopicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_count")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetLikeUserListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetLikeUserListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetLikeUserListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetLikeUserListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetLikeUserListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetLikeUserListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserNickList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserNickList = append(m.UserNickList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("offset")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleMarkReadedReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleMarkReadedReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleMarkReadedReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleMarkReadedResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleMarkReadedResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleMarkReadedResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleMuteUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleMuteUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleMuteUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CircleIdList = append(m.CircleIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircle2_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircle2_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CircleIdList = append(m.CircleIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleIdList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Days", wireType)
			}
			m.Days = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Days |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Secs", wireType)
			}
			m.Secs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Secs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BecauseCircleId", wireType)
			}
			m.BecauseCircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BecauseCircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BecauseTopicId", wireType)
			}
			m.BecauseTopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BecauseTopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("days")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleMuteUserResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleMuteUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleMuteUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CircleIdList = append(m.CircleIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircle2_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircle2_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CircleIdList = append(m.CircleIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleIdList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Days", wireType)
			}
			m.Days = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Days |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Secs", wireType)
			}
			m.Secs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Secs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("days")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleUnmuteUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleUnmuteUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleUnmuteUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CircleIdList = append(m.CircleIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircle2_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircle2_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CircleIdList = append(m.CircleIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleUnmuteUserResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleUnmuteUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleUnmuteUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CircleIdList = append(m.CircleIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircle2_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircle2_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CircleIdList = append(m.CircleIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MyCircleOrderReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MyCircleOrderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MyCircleOrderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CircleIdList = append(m.CircleIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircle2_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircle2_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CircleIdList = append(m.CircleIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MyCircleOrderResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MyCircleOrderResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MyCircleOrderResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CircleIdList = append(m.CircleIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircle2_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircle2_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CircleIdList = append(m.CircleIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleHighlightTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleHighlightTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleHighlightTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleHighlightTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleHighlightTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleHighlightTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleCancelHighlightTopicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleCancelHighlightTopicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleCancelHighlightTopicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleCancelHighlightTopicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleCancelHighlightTopicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleCancelHighlightTopicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleManagerDeleteCommentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleManagerDeleteCommentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleManagerDeleteCommentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleManagerDeleteCommentResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleManagerDeleteCommentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleManagerDeleteCommentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleCheckUpdateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleCheckUpdateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleCheckUpdateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicUpdateInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicUpdateInfo = append(m.TopicUpdateInfo, &ga.TopicUpdateInfo{})
			if err := m.TopicUpdateInfo[len(m.TopicUpdateInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleCheckUpdateResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleCheckUpdateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleCheckUpdateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicUpdateInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopicUpdateInfo = append(m.TopicUpdateInfo, &ga.TopicUpdateInfo{})
			if err := m.TopicUpdateInfo[len(m.TopicUpdateInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewestCircleId", wireType)
			}
			m.NewestCircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewestCircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleActivityDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleActivityDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleActivityDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityId", wireType)
			}
			m.ActivityId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActivityId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WarmedUpTsBegin", wireType)
			}
			m.WarmedUpTsBegin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WarmedUpTsBegin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WarmedUpTsEnd", wireType)
			}
			m.WarmedUpTsEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WarmedUpTsEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveTsBegin", wireType)
			}
			m.ActiveTsBegin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveTsBegin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveTsEnd", wireType)
			}
			m.ActiveTsEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveTsEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("activity_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("act_url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("warmed_up_ts_begin")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("warmed_up_ts_end")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("active_ts_begin")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("active_ts_end")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("end_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetActivityListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetActivityListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetActivityListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsHomePage", wireType)
			}
			m.IsHomePage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsHomePage |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIndex", wireType)
			}
			m.StartIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_home_page")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetActivityListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetActivityListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetActivityListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActivityList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActivityList = append(m.ActivityList, &CircleActivityDetail{})
			if err := m.ActivityList[len(m.ActivityList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsHomePage", wireType)
			}
			m.IsHomePage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsHomePage |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_home_page")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetHotReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetHotReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetHotReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetHotResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetHotResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetHotResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HotCircleList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HotCircleList = append(m.HotCircleList, &CircleDynamicData{})
			if err := m.HotCircleList[len(m.HotCircleList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StCommentReplayTargetBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StCommentReplayTargetBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StCommentReplayTargetBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &ga.CircleUser{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StCommentBase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StCommentBase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StCommentBase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Creator == nil {
				m.Creator = &ga.CircleUser{}
			}
			if err := m.Creator.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTimeDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateTimeDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyTarget", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ReplyTarget == nil {
				m.ReplyTarget = &StCommentReplayTargetBase{}
			}
			if err := m.ReplyTarget.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("creator")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("create_time_desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleTopicNomalComment) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleTopicNomalComment: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleTopicNomalComment: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentBase", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CommentBase == nil {
				m.CommentBase = &StCommentBase{}
			}
			if err := m.CommentBase.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Floor", wireType)
			}
			m.Floor = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Floor |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImageList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImageList = append(m.ImageList, &CircleCommentImage{})
			if err := m.ImageList[len(m.ImageList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyCommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReplyCommentList = append(m.ReplyCommentList, &StCommentBase{})
			if err := m.ReplyCommentList[len(m.ReplyCommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyTotalCnt", wireType)
			}
			m.ReplyTotalCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplyTotalCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("comment_base")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("floor")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetNomalCommentListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetNomalCommentListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetNomalCommentListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCommentId", wireType)
			}
			m.StartCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncludeStartId", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IncludeStartId = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDesc", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDesc = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetNomalCommentListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetNomalCommentListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetNomalCommentListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartCommentId", wireType)
			}
			m.StartCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommentList = append(m.CommentList, &CircleTopicNomalComment{})
			if err := m.CommentList[len(m.CommentList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NomalLeftCnt", wireType)
			}
			m.NomalLeftCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NomalLeftCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetCommentReplyListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetCommentReplyListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetCommentReplyListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentCommentId", wireType)
			}
			m.ParentCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartReplyCommentId", wireType)
			}
			m.StartReplyCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartReplyCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("parent_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_reply_comment_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CircleGetCommentReplyListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleGetCommentReplyListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleGetCommentReplyListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentCommentId", wireType)
			}
			m.ParentCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartReplyCommentId", wireType)
			}
			m.StartReplyCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartReplyCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircle2_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReplyList = append(m.ReplyList, &StCommentBase{})
			if err := m.ReplyList[len(m.ReplyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyTotalCnt", wireType)
			}
			m.ReplyTotalCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplyTotalCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReplyLeftCnt", wireType)
			}
			m.ReplyLeftCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplyLeftCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircle2_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircle2_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("parent_comment_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_reply_comment_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipCircle2_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCircle2_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCircle2_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthCircle2_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowCircle2_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipCircle2_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthCircle2_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCircle2_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/proto/pbfile/circle2_.proto", fileDescriptorCircle2_) }

var fileDescriptorCircle2_ = []byte{
	// 3295 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x5b, 0xdd, 0x6f, 0x24, 0x47,
	0xb5, 0x4f, 0xcf, 0xb7, 0xcf, 0x7c, 0xb5, 0xdb, 0xb1, 0xb7, 0xf7, 0xcb, 0xeb, 0xdb, 0xc9, 0xe6,
	0x3a, 0xab, 0xc4, 0xbb, 0xf1, 0xbd, 0x91, 0xee, 0x4a, 0xb9, 0x24, 0xb3, 0xe3, 0xc9, 0x7a, 0xd8,
	0xf1, 0x47, 0xc6, 0xe3, 0x5d, 0xed, 0x22, 0x68, 0xca, 0xdd, 0xe5, 0x71, 0xcb, 0x3d, 0xdd, 0xb3,
	0xdd, 0x3d, 0xd9, 0x8c, 0x40, 0x8a, 0x84, 0x80, 0x08, 0x82, 0x04, 0x81, 0x3f, 0x20, 0x0f, 0x08,
	0x41, 0x90, 0x22, 0x40, 0xc0, 0x3b, 0x12, 0x2f, 0x91, 0x40, 0x82, 0x47, 0x9e, 0x48, 0x14, 0xde,
	0x10, 0xe2, 0x0d, 0x89, 0x47, 0x54, 0x55, 0x5d, 0x3d, 0xd5, 0x33, 0xe3, 0xb5, 0x27, 0x38, 0xe3,
	0xf0, 0xb2, 0xf2, 0x9e, 0x73, 0xea, 0xe3, 0xfc, 0xce, 0xa9, 0xd3, 0xe7, 0x54, 0x9d, 0x81, 0x2b,
	0xbe, 0x67, 0x5c, 0xef, 0x7a, 0x6e, 0xe0, 0x5e, 0xef, 0xee, 0xed, 0x5b, 0x36, 0xbe, 0x6e, 0x58,
	0x9e, 0x61, 0xe3, 0x55, 0x7d, 0x85, 0x52, 0x95, 0x44, 0x1b, 0x5d, 0x28, 0xb6, 0x91, 0xbe, 0x87,
	0x7c, 0xcc, 0x48, 0xda, 0xef, 0x25, 0x28, 0x54, 0xa9, 0xd4, 0x1d, 0x8c, 0xbb, 0xd8, 0x53, 0x16,
	0x20, 0xd9, 0xb3, 0x4c, 0x55, 0x5a, 0x4a, 0x2c, 0x17, 0x6f, 0xa5, 0x3e, 0xf8, 0xf3, 0x95, 0x27,
	0x9a, 0x84, 0xa0, 0x2c, 0x42, 0x16, 0x19, 0x86, 0xdb, 0x73, 0x02, 0x35, 0xb1, 0x94, 0x58, 0x9e,
	0x09, 0x79, 0x9c, 0xa8, 0xfc, 0x17, 0xcc, 0x38, 0x96, 0x71, 0xa8, 0x3b, 0xa8, 0x83, 0xd5, 0xa4,
	0x20, 0x91, 0x23, 0xe4, 0x4d, 0xd4, 0xc1, 0x64, 0x6a, 0x1f, 0xbf, 0xa1, 0xa6, 0xc4, 0xa9, 0x7d,
	0xfc, 0x86, 0x72, 0x15, 0xf2, 0x87, 0x74, 0x71, 0x3d, 0xe8, 0x77, 0xb1, 0x9a, 0x16, 0xf8, 0xc0,
	0x18, 0xad, 0x7e, 0x17, 0x6b, 0x1a, 0xc0, 0x9d, 0x5a, 0x6d, 0xbb, 0xa9, 0xb7, 0xee, 0x6f, 0xd7,
	0x14, 0x80, 0xcc, 0x46, 0x65, 0xa7, 0x55, 0x6b, 0xca, 0x92, 0x92, 0x83, 0xd4, 0xdd, 0x7a, 0xb5,
	0x26, 0x27, 0xb4, 0x9f, 0x48, 0x20, 0x33, 0x75, 0x5a, 0x6e, 0xd7, 0x32, 0xea, 0x1d, 0xd4, 0xc6,
	0x64, 0x6b, 0xc1, 0x41, 0xaf, 0xb3, 0xa7, 0xf7, 0x3c, 0x9b, 0x2a, 0x16, 0x6d, 0x8d, 0x92, 0x77,
	0x3d, 0x9b, 0x88, 0x58, 0x44, 0x96, 0x8a, 0x88, 0xfa, 0xe5, 0x28, 0x99, 0x88, 0x5c, 0x85, 0x3c,
	0x13, 0x79, 0x64, 0x99, 0xc1, 0x81, 0x9a, 0x5c, 0x92, 0x06, 0xbb, 0xa4, 0x8c, 0x7b, 0x84, 0xae,
	0xfc, 0x37, 0x14, 0x98, 0xd8, 0x01, 0xb6, 0xda, 0x07, 0x81, 0x9a, 0x12, 0xe4, 0xd8, 0x04, 0xeb,
	0x94, 0xa1, 0xfd, 0x36, 0x03, 0x79, 0x61, 0xab, 0x8a, 0x0a, 0x29, 0xaa, 0xbe, 0x88, 0x3c, 0xa5,
	0x90, 0xcd, 0x31, 0x43, 0xea, 0x96, 0x49, 0x37, 0xc7, 0xd9, 0x39, 0x46, 0xae, 0x9b, 0xca, 0x15,
	0xc8, 0x05, 0x64, 0x16, 0x22, 0x91, 0x14, 0x24, 0xb2, 0x94, 0x5a, 0x37, 0x95, 0x0b, 0x90, 0x0e,
	0xac, 0xc0, 0xc6, 0x14, 0x7d, 0xae, 0x1c, 0x23, 0x11, 0xd3, 0x1a, 0xae, 0x13, 0x60, 0x27, 0xa0,
	0xd8, 0x47, 0xa6, 0x0d, 0x89, 0x44, 0x73, 0xc3, 0xc3, 0x28, 0xc0, 0x7a, 0x60, 0x75, 0xb0, 0x9a,
	0x11, 0xed, 0xc3, 0x18, 0x2d, 0xab, 0x83, 0x95, 0xff, 0x01, 0x86, 0x83, 0x6e, 0x5b, 0x7e, 0xa0,
	0x66, 0x97, 0x92, 0xcb, 0xf9, 0xd5, 0x27, 0x57, 0xda, 0x68, 0x65, 0xd8, 0x20, 0x4d, 0x86, 0x75,
	0xc3, 0xf2, 0x03, 0xe5, 0x29, 0x00, 0xdb, 0x3a, 0xc4, 0x3a, 0xf3, 0xac, 0x9c, 0x30, 0xf5, 0x0c,
	0xa1, 0x57, 0xa9, 0x6f, 0x3d, 0x0b, 0x45, 0xc3, 0xed, 0x74, 0xb0, 0x13, 0x84, 0x72, 0x33, 0x82,
	0x5c, 0x21, 0x64, 0x31, 0xd1, 0x2b, 0x90, 0xb3, 0x7c, 0x9d, 0x0c, 0x35, 0x55, 0x10, 0x81, 0xb0,
	0xfc, 0x06, 0x21, 0x12, 0x65, 0x18, 0x52, 0x7e, 0x80, 0x02, 0xac, 0xe6, 0x45, 0x65, 0x28, 0x63,
	0x87, 0xd0, 0x95, 0x1b, 0x30, 0x6b, 0x23, 0x9f, 0xac, 0xc7, 0xd6, 0xa5, 0x9a, 0x17, 0x04, 0xe1,
	0x32, 0x61, 0x57, 0x19, 0x97, 0xaa, 0xbf, 0x0c, 0x59, 0x0a, 0x86, 0xeb, 0xa9, 0xc5, 0x25, 0x69,
	0x39, 0xbf, 0x5a, 0x1a, 0xe8, 0xbe, 0xeb, 0x63, 0xaf, 0xc9, 0xd9, 0xca, 0x0a, 0xc8, 0x02, 0x9e,
	0xba, 0x89, 0x7d, 0x43, 0x2d, 0x09, 0xc0, 0x97, 0x06, 0xa0, 0xae, 0x61, 0xdf, 0x50, 0x6e, 0xc2,
	0xc2, 0xc8, 0x5e, 0xd8, 0xa8, 0xb2, 0x30, 0x6a, 0x6e, 0x68, 0x43, 0x74, 0xe8, 0x1a, 0x14, 0x4d,
	0xf7, 0x91, 0x63, 0xbb, 0xc8, 0xd4, 0x2d, 0x67, 0xdf, 0x55, 0x65, 0xba, 0xb5, 0x2b, 0x43, 0x66,
	0xb9, 0x8d, 0x3a, 0x78, 0x2d, 0x94, 0xab, 0x3b, 0xfb, 0x6e, 0xb3, 0x60, 0x0a, 0xff, 0xd3, 0x5e,
	0x04, 0x68, 0x6d, 0x6d, 0xd7, 0xab, 0xd1, 0xc9, 0xdb, 0xdc, 0x6a, 0x6e, 0x54, 0x1a, 0xf2, 0x13,
	0x4a, 0x11, 0x66, 0xaa, 0xbb, 0x9b, 0x0f, 0x2a, 0xf5, 0xdb, 0x95, 0x4d, 0x59, 0x52, 0x0a, 0x90,
	0xdb, 0x7a, 0xf5, 0xd5, 0x7a, 0xb5, 0x5e, 0x69, 0xc8, 0x09, 0x6d, 0x0f, 0xf2, 0x6c, 0xd8, 0x4e,
	0xab, 0xd2, 0xaa, 0x29, 0xf3, 0x30, 0x4b, 0xff, 0xd0, 0xd7, 0xeb, 0xb7, 0xd7, 0x5b, 0x7a, 0x83,
	0xfc, 0x2b, 0x4b, 0x4a, 0x19, 0xf2, 0x8c, 0xbc, 0xd3, 0xaa, 0x57, 0xef, 0xc8, 0x09, 0x45, 0x81,
	0x12, 0x23, 0x54, 0xaa, 0xad, 0xfa, 0xdd, 0x7a, 0xeb, 0xbe, 0x9c, 0x52, 0xce, 0xc1, 0x1c, 0xa3,
	0xdd, 0xae, 0x6c, 0xd4, 0xf4, 0xb5, 0xad, 0x7b, 0x9b, 0x8d, 0xad, 0xca, 0x9a, 0x9c, 0xd3, 0x1e,
	0x80, 0xc2, 0xf4, 0x08, 0x35, 0x3f, 0xc5, 0x13, 0xaf, 0xbd, 0x9d, 0xe2, 0x93, 0x53, 0x90, 0xc2,
	0x15, 0x88, 0xcb, 0x72, 0x4b, 0x0c, 0x05, 0xca, 0x99, 0x90, 0x5e, 0x37, 0x4f, 0xe5, 0xcc, 0x0a,
	0xe7, 0x32, 0x35, 0xee, 0x5c, 0x0a, 0x1e, 0x47, 0xce, 0xed, 0x63, 0x3c, 0xee, 0x84, 0x27, 0xf8,
	0x12, 0x64, 0xc8, 0xa9, 0xe8, 0xf9, 0x6a, 0x56, 0x90, 0x08, 0x69, 0xca, 0xcb, 0x50, 0xf6, 0x70,
	0xd7, 0xb6, 0xb0, 0xc9, 0x3d, 0x51, 0xcd, 0x51, 0x6f, 0x5a, 0x18, 0xf2, 0xa6, 0x10, 0xa8, 0x66,
	0x29, 0x14, 0xe7, 0xc0, 0x8d, 0xf3, 0xfb, 0x99, 0xc7, 0xf8, 0xfd, 0x8b, 0xb1, 0x80, 0x02, 0x34,
	0xa0, 0x08, 0x6b, 0x89, 0x16, 0x17, 0x43, 0xca, 0x05, 0x48, 0xef, 0xdb, 0xae, 0xeb, 0xa9, 0xf9,
	0x25, 0x69, 0x39, 0xcd, 0x43, 0x1d, 0x25, 0x69, 0x37, 0x41, 0x11, 0xb7, 0xb8, 0xc3, 0x34, 0x13,
	0x3d, 0x3a, 0x0f, 0xd9, 0xb5, 0x5a, 0xa3, 0xd6, 0xaa, 0xad, 0xc9, 0x12, 0x61, 0xec, 0xac, 0xd7,
	0x6b, 0x8d, 0x35, 0x39, 0xa1, 0xbd, 0x97, 0x86, 0x59, 0xb6, 0xf0, 0x5a, 0xdf, 0x41, 0x1d, 0xcb,
	0x58, 0x43, 0x01, 0x8a, 0xdb, 0x59, 0x1a, 0x6b, 0xe7, 0xe7, 0xa1, 0xbc, 0xef, 0xda, 0xb6, 0xfb,
	0x08, 0x7b, 0xba, 0xd3, 0xeb, 0xec, 0x61, 0x2f, 0xe6, 0x10, 0x25, 0xce, 0xdc, 0xa4, 0x3c, 0xe5,
	0xff, 0x60, 0xbe, 0xdd, 0xb3, 0x6c, 0x53, 0x1f, 0x1e, 0x24, 0xfa, 0xc8, 0x1c, 0x15, 0x79, 0x35,
	0x3e, 0x92, 0xb8, 0xb4, 0x1f, 0x0e, 0xa3, 0x1e, 0x93, 0x8b, 0x5c, 0xda, 0x67, 0xa2, 0xca, 0x0b,
	0xd1, 0xa7, 0x96, 0x62, 0x9a, 0xa6, 0x98, 0xca, 0x03, 0x4c, 0x59, 0x12, 0xc0, 0x3f, 0xbb, 0x14,
	0x4e, 0xe2, 0x3b, 0x4c, 0x43, 0xfa, 0x69, 0xcf, 0x2c, 0x49, 0x91, 0xc1, 0x80, 0x31, 0xe8, 0xc7,
	0x5d, 0x85, 0x94, 0x65, 0xb8, 0x8e, 0x9a, 0x15, 0xf8, 0x94, 0xa2, 0x5c, 0x86, 0x6c, 0x1b, 0x75,
	0x28, 0x40, 0x39, 0xe1, 0x63, 0x98, 0x21, 0xc4, 0xba, 0x10, 0x90, 0x79, 0x68, 0x97, 0x86, 0x02,
	0x32, 0x0b, 0xec, 0x37, 0x60, 0x36, 0x70, 0x4d, 0xd4, 0xd7, 0x45, 0x61, 0x10, 0x84, 0xcb, 0x94,
	0xdd, 0x1a, 0x8c, 0x78, 0x01, 0x66, 0xe9, 0xba, 0x3c, 0x94, 0xa1, 0x3d, 0x1b, 0x53, 0x9f, 0xe0,
	0xb0, 0xc8, 0x6d, 0x21, 0xec, 0x11, 0x2e, 0xd9, 0x2a, 0x32, 0x02, 0x3d, 0x40, 0x6d, 0xb5, 0x20,
	0xe8, 0x91, 0x41, 0x46, 0xd0, 0x42, 0x6d, 0x72, 0x62, 0x09, 0x9b, 0x3a, 0x6e, 0x51, 0xe0, 0x93,
	0x41, 0xd4, 0x63, 0xaf, 0x42, 0xfe, 0x00, 0xf9, 0xfa, 0x23, 0x6c, 0xef, 0x23, 0x0f, 0xab, 0x25,
	0x61, 0x31, 0x38, 0x40, 0xfe, 0x3d, 0x46, 0x57, 0x5e, 0x82, 0x73, 0x96, 0xaf, 0x23, 0xc7, 0x21,
	0x5a, 0x18, 0x98, 0x7d, 0xd8, 0x28, 0x94, 0x6a, 0x59, 0x18, 0x32, 0x6f, 0xf9, 0x15, 0x51, 0x86,
	0x99, 0x88, 0x98, 0x99, 0xea, 0x45, 0xb3, 0x05, 0x59, 0x40, 0x20, 0x47, 0xc8, 0x34, 0x55, 0xfa,
	0x02, 0xcf, 0x82, 0x6e, 0xe3, 0x80, 0x18, 0xb1, 0x89, 0x1f, 0x2a, 0xcf, 0x40, 0x8e, 0xe4, 0x7d,
	0xba, 0x87, 0x1f, 0x52, 0x47, 0xcd, 0xaf, 0xe6, 0x89, 0xdd, 0x6f, 0x21, 0x1f, 0x37, 0xf1, 0xc3,
	0x66, 0x76, 0x8f, 0xfd, 0x41, 0x94, 0xf4, 0xf0, 0x43, 0x36, 0xbb, 0xe8, 0xa7, 0x59, 0x0f, 0x3f,
	0xa4, 0x93, 0xff, 0x4c, 0xe2, 0x07, 0x21, 0x9a, 0xdd, 0xef, 0x2a, 0xcf, 0xc2, 0x4c, 0x38, 0xbd,
	0xdf, 0x0d, 0xe7, 0x2f, 0x0c, 0xe6, 0xf7, 0xbb, 0xcd, 0xdc, 0x5e, 0xf8, 0xd7, 0xb1, 0x2b, 0x28,
	0x9b, 0xa0, 0x86, 0x2e, 0x67, 0xb2, 0xa3, 0xa6, 0x9b, 0x28, 0x40, 0xcc, 0x65, 0x93, 0xd4, 0x65,
	0xe7, 0x07, 0x2e, 0x2b, 0x9c, 0xc6, 0xe6, 0xbc, 0x31, 0x4c, 0x22, 0xfb, 0xd3, 0xbe, 0x08, 0x45,
	0x26, 0xfb, 0x79, 0xd7, 0x72, 0x26, 0xc1, 0xe2, 0x69, 0x28, 0x45, 0xa7, 0x9b, 0x2d, 0x9f, 0x58,
	0x4a, 0x2e, 0x17, 0x9b, 0x05, 0x7e, 0xb8, 0xe9, 0xf4, 0x6f, 0x49, 0x50, 0x12, 0xe7, 0x9f, 0x0c,
	0x8d, 0x13, 0xad, 0x41, 0xa4, 0xf6, 0x91, 0x65, 0x63, 0x33, 0x92, 0x4a, 0x32, 0x29, 0x46, 0x0d,
	0x77, 0xf2, 0x80, 0x2b, 0xfa, 0x5a, 0xcf, 0x9a, 0xc8, 0xe8, 0xc7, 0x7f, 0xae, 0xb4, 0x2f, 0x71,
	0x25, 0xd9, 0xdc, 0x93, 0x29, 0x79, 0x82, 0xf9, 0xff, 0x21, 0xc1, 0x7c, 0xe4, 0x56, 0xf4, 0x18,
	0x4f, 0xea, 0xb9, 0x27, 0xf8, 0xe6, 0x3e, 0x05, 0xd0, 0x25, 0x5f, 0x14, 0x16, 0x3e, 0xc4, 0x88,
	0x3a, 0x43, 0xe8, 0x51, 0xba, 0x49, 0x85, 0xba, 0xae, 0x6f, 0x05, 0x96, 0xeb, 0xc4, 0x2a, 0x96,
	0x02, 0x61, 0x6d, 0x87, 0x9c, 0x98, 0x2b, 0xa7, 0xc7, 0xb9, 0xf2, 0x12, 0xe4, 0x7a, 0x3e, 0xf6,
	0xf6, 0x3d, 0xb7, 0x13, 0xfb, 0xec, 0x46, 0x54, 0xed, 0xd7, 0x09, 0x58, 0x18, 0xa7, 0xf7, 0x69,
	0x03, 0x3c, 0x7d, 0xdd, 0x57, 0x80, 0x05, 0x70, 0xe6, 0xaf, 0x19, 0x7a, 0x70, 0xcb, 0x43, 0xb9,
	0x42, 0x73, 0x26, 0xe0, 0x2a, 0x2b, 0xcf, 0x41, 0xd9, 0xc1, 0x8f, 0xb0, 0x1f, 0xe8, 0x51, 0x5e,
	0x24, 0xe6, 0x21, 0x45, 0xc6, 0x64, 0x75, 0x84, 0xa9, 0x7d, 0x28, 0x86, 0x21, 0x36, 0xd7, 0xe9,
	0xfa, 0xca, 0xb1, 0xf9, 0xd9, 0x73, 0x50, 0xa6, 0xb5, 0x0b, 0x31, 0x65, 0x88, 0xaa, 0x58, 0xed,
	0x15, 0x09, 0x93, 0xe4, 0x61, 0xc2, 0x07, 0xac, 0xab, 0xc7, 0x0b, 0x99, 0x74, 0xfc, 0x03, 0xd6,
	0xad, 0x0a, 0xb5, 0x8c, 0xf6, 0xfd, 0x04, 0xcf, 0x3f, 0x07, 0x1a, 0x9e, 0xba, 0x57, 0x1c, 0xab,
	0xe5, 0x55, 0x48, 0xd3, 0x3f, 0xa9, 0x27, 0x8c, 0x31, 0x20, 0xe3, 0x92, 0x00, 0x35, 0x00, 0x23,
	0x4a, 0x2e, 0x66, 0x9a, 0x05, 0x8e, 0x02, 0x35, 0xf1, 0x2b, 0x20, 0x8b, 0x20, 0x08, 0x8e, 0x71,
	0x64, 0x12, 0x39, 0x40, 0x85, 0x86, 0xb8, 0x8f, 0x24, 0x0e, 0xca, 0xb6, 0xeb, 0x7f, 0x2a, 0x76,
	0x27, 0x22, 0xb6, 0x15, 0xa6, 0xf7, 0xb1, 0x9b, 0x0c, 0x46, 0x8e, 0x57, 0xd3, 0xd2, 0xa4, 0xd5,
	0xf4, 0x12, 0x14, 0xac, 0x4e, 0x5b, 0x3f, 0xc4, 0xfd, 0x81, 0xfa, 0x33, 0x4d, 0xb0, 0x3a, 0xed,
	0x3b, 0xb8, 0x4f, 0x55, 0xfc, 0xb6, 0x04, 0x73, 0x23, 0x2a, 0x4e, 0x6e, 0xf8, 0x48, 0x87, 0xc4,
	0x58, 0x1d, 0x22, 0xbb, 0x26, 0x1f, 0x67, 0x57, 0x82, 0xf7, 0xb9, 0xc8, 0x09, 0x05, 0x43, 0x4c,
	0xfb, 0xb0, 0xad, 0x80, 0xec, 0x07, 0xc8, 0x1b, 0x54, 0xc1, 0x96, 0x19, 0x8b, 0x4d, 0x25, 0xca,
	0xad, 0x46, 0x05, 0xd8, 0xc8, 0x9d, 0x41, 0xfa, 0xa8, 0x3b, 0x03, 0xed, 0xdd, 0x04, 0xa8, 0xe3,
	0x55, 0x9c, 0xfe, 0x69, 0xfb, 0xf4, 0xd4, 0x54, 0x6e, 0x42, 0x61, 0x82, 0x73, 0x97, 0x37, 0x84,
	0x43, 0xf7, 0x4f, 0x09, 0x9e, 0x1c, 0x78, 0x24, 0x17, 0x99, 0xb2, 0x07, 0xac, 0x82, 0x32, 0x54,
	0x7f, 0x0e, 0x83, 0x23, 0xc7, 0x0b, 0xce, 0x78, 0x09, 0xfd, 0x09, 0x0f, 0xe3, 0xdf, 0xa2, 0xb4,
	0x24, 0xa6, 0xfa, 0xf4, 0x3d, 0xe3, 0x93, 0xa8, 0x7f, 0x83, 0xa8, 0xcf, 0x4a, 0x75, 0x76, 0x43,
	0x70, 0x94, 0xb5, 0xb9, 0x98, 0xf6, 0x6e, 0x14, 0x5e, 0x1b, 0xd6, 0x21, 0x3e, 0x93, 0xcf, 0xea,
	0x65, 0xc8, 0x86, 0x57, 0x78, 0xb1, 0x22, 0x36, 0xc3, 0x6e, 0xf0, 0xb4, 0x1f, 0x46, 0xd1, 0x51,
	0xd8, 0xe1, 0xf4, 0xcd, 0x71, 0xcc, 0x2e, 0xbf, 0x16, 0x9d, 0x98, 0x26, 0xee, 0xba, 0xde, 0x99,
	0x24, 0x28, 0xda, 0x37, 0x23, 0xdf, 0x8d, 0x6d, 0x62, 0xea, 0x60, 0x09, 0x68, 0xac, 0x61, 0x1b,
	0x07, 0xf8, 0x8c, 0xd1, 0x88, 0x6d, 0x62, 0xfa, 0x68, 0xbc, 0x27, 0xf1, 0x8c, 0x9f, 0x6d, 0xe4,
	0x8c, 0xe2, 0x69, 0xfc, 0x1e, 0x33, 0x35, 0xf6, 0x1e, 0x53, 0x7b, 0x3f, 0xfa, 0xfc, 0x0f, 0xed,
	0x75, 0xfa, 0x27, 0xee, 0x44, 0xfb, 0xc5, 0xe2, 0xa7, 0x3c, 0xdc, 0x77, 0x80, 0x2c, 0xfb, 0x94,
	0x8b, 0xe1, 0xdf, 0x49, 0x70, 0xfe, 0x88, 0x75, 0x4e, 0x1d, 0x18, 0x0d, 0x32, 0xe1, 0xe5, 0x10,
	0xcb, 0xd4, 0x60, 0x10, 0xc3, 0x9b, 0x21, 0x47, 0x79, 0x29, 0xba, 0x44, 0x08, 0x6f, 0x4c, 0xc2,
	0x6c, 0xfd, 0x88, 0x7b, 0x92, 0x62, 0xec, 0x9e, 0x44, 0xfb, 0x93, 0x58, 0x7a, 0x93, 0x5c, 0x7d,
	0xe2, 0xf3, 0xf9, 0x1c, 0x94, 0xc3, 0xb4, 0x65, 0xac, 0x32, 0x45, 0x96, 0xb5, 0x70, 0x8d, 0xae,
	0x01, 0x4b, 0x63, 0xf4, 0xb1, 0x06, 0x2f, 0x50, 0x5e, 0x2b, 0x2a, 0x3f, 0x62, 0xd7, 0x83, 0xa9,
	0x91, 0xf7, 0x1a, 0x96, 0xdc, 0x84, 0xcf, 0x96, 0xe9, 0xa1, 0x67, 0x4b, 0xed, 0x3b, 0x62, 0x75,
	0x2d, 0xa8, 0x36, 0x99, 0x95, 0xce, 0x5c, 0xbd, 0x78, 0x29, 0x9d, 0x3e, 0xb6, 0x94, 0x0e, 0xe1,
	0xc8, 0x0c, 0xc3, 0xf1, 0x1b, 0x49, 0x38, 0x1f, 0x0d, 0xa1, 0x32, 0x9b, 0x76, 0xf0, 0xb9, 0x04,
	0x19, 0x77, 0x7f, 0xdf, 0xc7, 0x71, 0x95, 0x43, 0x1a, 0xa9, 0xaf, 0x6c, 0xab, 0x63, 0xc5, 0xb3,
	0x59, 0x46, 0xd2, 0xfe, 0x2a, 0x1e, 0xbd, 0xb8, 0x0a, 0xd3, 0x8f, 0x49, 0x9f, 0x58, 0x0d, 0x52,
	0x2f, 0xd3, 0x52, 0x99, 0x3e, 0x9a, 0x0b, 0xb9, 0x67, 0x81, 0x50, 0x37, 0x2d, 0xe3, 0x90, 0x66,
	0x9f, 0x88, 0xe7, 0x3a, 0x1b, 0xc8, 0x3b, 0x6c, 0x62, 0x64, 0x62, 0x73, 0x12, 0x4b, 0x69, 0x00,
	0xfe, 0xeb, 0x9e, 0xde, 0xf1, 0xdb, 0x23, 0x3a, 0xfa, 0xaf, 0x7b, 0x1b, 0x7e, 0x9b, 0x46, 0xcc,
	0x27, 0x47, 0x97, 0x98, 0x0c, 0xc9, 0x93, 0x2c, 0xf3, 0x7e, 0x82, 0x5f, 0xd7, 0x6c, 0xf4, 0x02,
	0xf6, 0x3c, 0x35, 0x81, 0x22, 0xa1, 0x3f, 0x27, 0x86, 0xbb, 0x12, 0x46, 0x2f, 0x4f, 0x93, 0x63,
	0x2e, 0x4f, 0x55, 0x48, 0x99, 0xa8, 0xef, 0xc7, 0x6c, 0x44, 0x29, 0x84, 0xe3, 0x63, 0xc3, 0x8f,
	0xdd, 0xc3, 0x50, 0x0a, 0xb1, 0xac, 0x87, 0x91, 0xef, 0x3a, 0xb1, 0x17, 0x8f, 0x90, 0xa6, 0xdc,
	0x80, 0xd9, 0x3d, 0x6c, 0xa0, 0x9e, 0x8f, 0x85, 0x90, 0x90, 0x15, 0x2f, 0x73, 0x42, 0x76, 0x14,
	0x14, 0x56, 0x40, 0xe6, 0x23, 0x22, 0x97, 0x12, 0x9f, 0x43, 0x4a, 0x21, 0x97, 0x5f, 0x6f, 0xfd,
	0x22, 0x4a, 0xc4, 0x07, 0x78, 0x4d, 0x66, 0x95, 0xa9, 0x63, 0xa6, 0x7d, 0x85, 0x7b, 0xeb, 0xae,
	0xd3, 0x99, 0xb6, 0x91, 0xb5, 0x37, 0xb9, 0x1f, 0x8b, 0x8b, 0x4f, 0x11, 0x31, 0xed, 0xcb, 0x20,
	0x6f, 0xf4, 0xd9, 0x16, 0xb6, 0x3c, 0x73, 0x32, 0xd5, 0x9f, 0xe6, 0x97, 0xeb, 0xc3, 0x8f, 0x00,
	0x55, 0x71, 0x05, 0x13, 0x66, 0x87, 0x56, 0x98, 0xf8, 0xa9, 0xe1, 0x04, 0xab, 0x7c, 0x23, 0x4a,
	0xf9, 0xd6, 0xad, 0xf6, 0x81, 0x6d, 0xb5, 0x0f, 0xce, 0xa6, 0x7a, 0xf9, 0x56, 0xf4, 0xad, 0x1a,
	0xde, 0xc7, 0xf4, 0x53, 0xf6, 0xb7, 0x25, 0xb8, 0x1c, 0xbe, 0x3a, 0x23, 0xc7, 0xc0, 0xf6, 0xd9,
	0x22, 0xf3, 0x5d, 0x09, 0x16, 0x1f, 0xb7, 0x9b, 0xe9, 0xe3, 0xf3, 0xf3, 0x08, 0x9f, 0x0d, 0xe4,
	0xa0, 0x36, 0xf6, 0x3e, 0xfb, 0x95, 0xcd, 0xaf, 0x22, 0x10, 0xc7, 0x6f, 0xf9, 0x33, 0x5a, 0xe0,
	0x44, 0x61, 0xae, 0x7a, 0x80, 0x8d, 0xc3, 0xdd, 0xae, 0x89, 0x02, 0x3c, 0x09, 0xbe, 0x2f, 0xd3,
	0x67, 0x08, 0xcb, 0xd0, 0x7b, 0x74, 0x28, 0xeb, 0x0a, 0x4a, 0xd0, 0x84, 0x72, 0x8e, 0x0c, 0xa0,
	0x2e, 0xc4, 0xa6, 0xa5, 0x9d, 0x40, 0xe5, 0x20, 0x4e, 0xd0, 0x7e, 0x19, 0x15, 0x0b, 0xb1, 0x1d,
	0x4c, 0x06, 0xd7, 0xbf, 0xbb, 0x0b, 0xf2, 0x39, 0x0d, 0xdf, 0x8a, 0x06, 0xb0, 0x8b, 0x2d, 0x79,
	0x25, 0xc6, 0xe5, 0x81, 0x4d, 0xfb, 0x7b, 0x82, 0xe3, 0x56, 0x31, 0x02, 0xeb, 0x75, 0x2b, 0xe8,
	0xb3, 0x7a, 0x8d, 0x24, 0xe0, 0x28, 0xa4, 0x0c, 0xb7, 0x70, 0x00, 0x67, 0xb0, 0xeb, 0x1e, 0x64,
	0x04, 0x23, 0xcd, 0x42, 0x19, 0x64, 0x04, 0xbb, 0x9e, 0x4d, 0xd8, 0x54, 0x1b, 0xcf, 0xa6, 0xbb,
	0x88, 0xd8, 0x64, 0xd3, 0x9e, 0xfd, 0xd8, 0xee, 0xbb, 0x17, 0x40, 0x79, 0x84, 0xbc, 0x0e, 0x36,
	0xf5, 0x5e, 0x57, 0x0f, 0x7c, 0x7d, 0x0f, 0xb7, 0x2d, 0x27, 0x96, 0x31, 0x96, 0x19, 0x7f, 0xb7,
	0xdb, 0xf2, 0x6f, 0x11, 0xa6, 0xf2, 0x3c, 0xc8, 0xb1, 0x21, 0xd8, 0x89, 0xa7, 0xfa, 0xc5, 0xc1,
	0x80, 0x9a, 0x43, 0xdf, 0xa9, 0xa8, 0x26, 0x78, 0x30, 0x7d, 0xec, 0x5d, 0x8d, 0x31, 0xf9, 0xe4,
	0xcb, 0x50, 0x1c, 0x48, 0x93, 0x99, 0xc5, 0xa6, 0xbc, 0x3c, 0x97, 0x25, 0xf3, 0x5e, 0x84, 0x0c,
	0x76, 0x4c, 0x3d, 0xf0, 0x63, 0xfd, 0x78, 0x69, 0xec, 0x98, 0x2d, 0x5f, 0x7b, 0x47, 0xac, 0x34,
	0x38, 0xe6, 0x93, 0x56, 0x1a, 0xcf, 0x40, 0xc1, 0xf2, 0xf5, 0x03, 0xb7, 0x83, 0xf5, 0x2e, 0x6a,
	0xc7, 0xbb, 0x05, 0xc0, 0xf2, 0xd7, 0xdd, 0x0e, 0xde, 0x46, 0x6d, 0x4c, 0x8c, 0xc8, 0x2a, 0x2e,
	0xcb, 0x31, 0xf1, 0x1b, 0xf1, 0xde, 0x4c, 0xca, 0xa8, 0x13, 0xba, 0xf6, 0xbe, 0x58, 0x3a, 0xc4,
	0xf7, 0x34, 0x99, 0xfb, 0xfe, 0x7f, 0x88, 0x11, 0x71, 0x9a, 0xe8, 0x3b, 0x9a, 0x5f, 0x55, 0x07,
	0x15, 0x59, 0xdc, 0xcb, 0x9a, 0x05, 0x24, 0xac, 0x36, 0xa2, 0x56, 0x72, 0xbc, 0x5a, 0xda, 0x4d,
	0x28, 0x47, 0xdb, 0x5d, 0x77, 0x27, 0x41, 0x4e, 0xfb, 0xaa, 0xd0, 0x01, 0x42, 0x87, 0x4e, 0xaa,
	0x60, 0xf9, 0xc0, 0x8d, 0xce, 0x96, 0xa0, 0xe2, 0x51, 0x17, 0x0a, 0x07, 0x6e, 0xc0, 0x2f, 0x65,
	0xfd, 0x40, 0xfb, 0x50, 0x82, 0xf3, 0x3b, 0x83, 0xcb, 0xf2, 0xae, 0x8d, 0xfa, 0x2d, 0xe4, 0xb5,
	0x71, 0x40, 0x96, 0x3a, 0x59, 0x03, 0x9d, 0xd0, 0xdc, 0x96, 0x98, 0xa8, 0xb9, 0x2d, 0x79, 0x6c,
	0x73, 0x5b, 0x6a, 0x4c, 0x73, 0xdb, 0xb8, 0xde, 0xb4, 0xf4, 0xd1, 0xbd, 0x69, 0xda, 0x1f, 0x12,
	0x50, 0x8c, 0x34, 0x3c, 0xb9, 0x56, 0xc2, 0x7b, 0x44, 0xe2, 0x98, 0x96, 0xbe, 0xe4, 0x44, 0x5a,
	0xa7, 0x8e, 0xd5, 0x3a, 0x3d, 0x46, 0x6b, 0xde, 0x73, 0x9c, 0x19, 0xe9, 0x39, 0x1e, 0x87, 0x47,
	0xf6, 0x31, 0xbd, 0x7a, 0xaf, 0x40, 0xc1, 0xc3, 0x5d, 0xbb, 0xaf, 0x07, 0xd4, 0xce, 0x61, 0x67,
	0xe0, 0x65, 0xb2, 0xfb, 0x23, 0x1d, 0xa1, 0x99, 0xa7, 0x43, 0x18, 0x41, 0x7b, 0x27, 0xc1, 0xd3,
	0x4e, 0x1a, 0xfc, 0x37, 0xdd, 0x0e, 0xb2, 0x79, 0xe7, 0xe0, 0xff, 0x0e, 0x9e, 0xae, 0x88, 0x8b,
	0x86, 0xce, 0x3b, 0x1b, 0x9b, 0x9d, 0xcd, 0x68, 0x08, 0x16, 0x89, 0x1a, 0x01, 0x09, 0xd4, 0xf1,
	0x46, 0xc0, 0xa1, 0xde, 0xc2, 0xe4, 0x49, 0x7b, 0x0b, 0x5f, 0x66, 0x8f, 0x30, 0xfd, 0xf8, 0x0b,
	0x76, 0x8a, 0x0e, 0x1f, 0xb3, 0x1d, 0xfa, 0x22, 0xd3, 0x17, 0x9e, 0x14, 0x49, 0x2c, 0x0e, 0x71,
	0x72, 0x03, 0x64, 0xeb, 0xc6, 0x50, 0x0f, 0x40, 0x91, 0x21, 0x42, 0x78, 0x55, 0x27, 0xd0, 0x7e,
	0x94, 0x80, 0x4b, 0xd1, 0x31, 0x16, 0x11, 0xf9, 0x4f, 0x78, 0x81, 0xbd, 0x00, 0xe9, 0xd1, 0x27,
	0x49, 0x46, 0x22, 0x73, 0x59, 0x8e, 0x61, 0xf7, 0x4c, 0xac, 0x87, 0x81, 0xdb, 0xa4, 0x75, 0x36,
	0x7f, 0x46, 0x29, 0x85, 0xdc, 0x1d, 0x1a, 0xbc, 0xf9, 0x6b, 0x4b, 0xe8, 0x85, 0x92, 0xf8, 0xda,
	0x42, 0x4f, 0xe3, 0x4f, 0x13, 0x3c, 0xfd, 0x1c, 0x8b, 0xd3, 0x67, 0xff, 0x19, 0xf7, 0x73, 0x43,
	0x6f, 0xb3, 0xec, 0x86, 0xef, 0xe2, 0xd0, 0x0d, 0x9f, 0xa8, 0x59, 0xec, 0x81, 0x56, 0xb9, 0x06,
	0x25, 0x87, 0x30, 0x75, 0x1b, 0xef, 0x07, 0xd4, 0xab, 0x32, 0x82, 0x57, 0x15, 0x28, 0xaf, 0x81,
	0xf7, 0x03, 0xe2, 0x54, 0x3f, 0x10, 0x9d, 0x4a, 0x38, 0x9a, 0xfd, 0xb3, 0x70, 0xaa, 0x1b, 0x30,
	0xdb, 0x45, 0x1e, 0x7b, 0xbe, 0x1e, 0x8b, 0x54, 0x99, 0xb1, 0x07, 0x50, 0xdd, 0x84, 0x05, 0x06,
	0x6d, 0xfc, 0x20, 0x0e, 0x5d, 0xfe, 0xce, 0x51, 0x99, 0xa6, 0x70, 0xfc, 0x44, 0x8f, 0xcc, 0x8c,
	0x78, 0xa4, 0xf6, 0x56, 0x52, 0x70, 0xa1, 0x51, 0x54, 0xa6, 0xef, 0x42, 0x53, 0x45, 0xe6, 0x06,
	0x00, 0x1b, 0x24, 0x74, 0x06, 0x8c, 0x89, 0x67, 0x33, 0x1e, 0x47, 0x64, 0x5c, 0x20, 0xcb, 0x1e,
	0x19, 0xc8, 0x88, 0x7f, 0x86, 0xf3, 0x73, 0xff, 0x14, 0xef, 0xbe, 0xd8, 0xa7, 0x23, 0xf4, 0xcf,
	0x6b, 0x6f, 0xf2, 0xfa, 0x22, 0xf4, 0xc7, 0x1e, 0xf6, 0x03, 0xda, 0x4f, 0xb6, 0x00, 0x4a, 0xb3,
	0xf6, 0x1a, 0xfd, 0x11, 0x82, 0xde, 0xac, 0x55, 0xb7, 0x36, 0x36, 0x6a, 0x9b, 0x6b, 0xb2, 0xa4,
	0x9c, 0x83, 0xb9, 0x88, 0xbe, 0x71, 0x5f, 0xaf, 0xd6, 0x9b, 0xd5, 0x46, 0x6d, 0x47, 0x4e, 0x28,
	0xf3, 0x30, 0x1b, 0x31, 0x36, 0x6b, 0xf7, 0xe8, 0x8f, 0x07, 0xe4, 0xa4, 0xb2, 0x08, 0x17, 0x44,
	0xf9, 0xbb, 0xf5, 0x9d, 0x7a, 0x8b, 0x4c, 0x58, 0xdb, 0x6c, 0x35, 0xee, 0xcb, 0xa9, 0x6b, 0x3f,
	0x8e, 0xde, 0xe7, 0xc4, 0x36, 0x44, 0xba, 0x05, 0x71, 0xa9, 0xf5, 0xad, 0x96, 0x4e, 0x7f, 0xdf,
	0xb0, 0x33, 0xb4, 0x07, 0xb2, 0x54, 0xc8, 0x48, 0x28, 0x17, 0xe1, 0x5c, 0xc4, 0xa8, 0x6e, 0x6d,
	0xb6, 0x2a, 0xd5, 0x68, 0x54, 0x52, 0xb9, 0x02, 0x17, 0x23, 0x66, 0xa3, 0xb2, 0xd3, 0xd2, 0x77,
	0xb7, 0xd7, 0x2a, 0xad, 0x1a, 0x17, 0x48, 0x29, 0x97, 0xe1, 0xfc, 0x60, 0xbd, 0xfa, 0xed, 0x75,
	0xfa, 0xa3, 0x09, 0xce, 0x4e, 0x5f, 0xb3, 0x79, 0x8e, 0x3d, 0xa6, 0xa9, 0xfd, 0x3c, 0xcc, 0x33,
	0x88, 0x5a, 0xf4, 0xf7, 0x17, 0xbb, 0x3b, 0x7a, 0xd4, 0xe3, 0x7e, 0x01, 0x16, 0x86, 0x58, 0x83,
	0x96, 0xf7, 0xd1, 0x61, 0xbc, 0x03, 0xfe, 0xda, 0xd7, 0xe3, 0xb8, 0xf0, 0xdf, 0x9a, 0x84, 0xb8,
	0xf0, 0x51, 0x0c, 0x02, 0xbe, 0xd4, 0x02, 0x28, 0x31, 0x46, 0xb3, 0xb6, 0xdd, 0xb8, 0x2f, 0x4b,
	0xca, 0x25, 0x50, 0x47, 0xe9, 0x21, 0x97, 0x82, 0x16, 0xe3, 0xae, 0x57, 0xee, 0xf2, 0xa1, 0xa9,
	0x5b, 0x5b, 0x1f, 0x7c, 0xbc, 0x28, 0xfd, 0xf1, 0xe3, 0x45, 0xe9, 0xa3, 0x8f, 0x17, 0xa5, 0xef,
	0xfd, 0x65, 0xf1, 0x09, 0x50, 0x0d, 0xb7, 0xb3, 0xd2, 0xb7, 0xfa, 0x6e, 0x8f, 0xb8, 0x6c, 0xc7,
	0x35, 0xb1, 0xcd, 0x7e, 0xde, 0xf6, 0xe0, 0xa9, 0xb6, 0x6b, 0x23, 0xa7, 0xbd, 0xf2, 0xe2, 0x6a,
	0x10, 0xac, 0x18, 0x6e, 0x87, 0xfd, 0x3c, 0xce, 0x70, 0xed, 0xeb, 0xa8, 0xdb, 0x0d, 0x7f, 0x1e,
	0xf7, 0xaf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xa2, 0xdc, 0x9b, 0x69, 0x38, 0x37, 0x00, 0x00,
}
