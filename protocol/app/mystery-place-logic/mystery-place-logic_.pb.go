// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mystery-place-logic_.proto

package mystery_place_logic // import "golang.52tt.com/protocol/app/mystery-place-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GameOrientation int32

const (
	GameOrientation_GAME_ORIENTATION_UNDEFINED  GameOrientation = 0
	GameOrientation_GAME_ORIENTATION_HORIZONTAL GameOrientation = 1
	GameOrientation_GAME_ORIENTATION_VERTICAL   GameOrientation = 2
)

var GameOrientation_name = map[int32]string{
	0: "GAME_ORIENTATION_UNDEFINED",
	1: "GAME_ORIENTATION_HORIZONTAL",
	2: "GAME_ORIENTATION_VERTICAL",
}
var GameOrientation_value = map[string]int32{
	"GAME_ORIENTATION_UNDEFINED":  0,
	"GAME_ORIENTATION_HORIZONTAL": 1,
	"GAME_ORIENTATION_VERTICAL":   2,
}

func (x GameOrientation) String() string {
	return proto.EnumName(GameOrientation_name, int32(x))
}
func (GameOrientation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{0}
}

// 密室逃脱房，组队状态
type MysteryTeamStatus int32

const (
	MysteryTeamStatus_STATUS_NONE      MysteryTeamStatus = 0
	MysteryTeamStatus_OWNER_LEAVE      MysteryTeamStatus = 1
	MysteryTeamStatus_TAB_CHANGE       MysteryTeamStatus = 2
	MysteryTeamStatus_SEAT_AVAILABLE   MysteryTeamStatus = 3
	MysteryTeamStatus_SEAT_UNAVAILABLE MysteryTeamStatus = 4
	MysteryTeamStatus_CHANNEL_LOCKED   MysteryTeamStatus = 5
)

var MysteryTeamStatus_name = map[int32]string{
	0: "STATUS_NONE",
	1: "OWNER_LEAVE",
	2: "TAB_CHANGE",
	3: "SEAT_AVAILABLE",
	4: "SEAT_UNAVAILABLE",
	5: "CHANNEL_LOCKED",
}
var MysteryTeamStatus_value = map[string]int32{
	"STATUS_NONE":      0,
	"OWNER_LEAVE":      1,
	"TAB_CHANGE":       2,
	"SEAT_AVAILABLE":   3,
	"SEAT_UNAVAILABLE": 4,
	"CHANNEL_LOCKED":   5,
}

func (x MysteryTeamStatus) String() string {
	return proto.EnumName(MysteryTeamStatus_name, int32(x))
}
func (MysteryTeamStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{1}
}

// 密室逃脱房，邀请进房结果
type MysteryInviteResult int32

const (
	MysteryInviteResult_NOT_FOUND   MysteryInviteResult = 0
	MysteryInviteResult_NOT_RESPOND MysteryInviteResult = 1
	MysteryInviteResult_ACCEPT      MysteryInviteResult = 2
	MysteryInviteResult_REJECT      MysteryInviteResult = 3
)

var MysteryInviteResult_name = map[int32]string{
	0: "NOT_FOUND",
	1: "NOT_RESPOND",
	2: "ACCEPT",
	3: "REJECT",
}
var MysteryInviteResult_value = map[string]int32{
	"NOT_FOUND":   0,
	"NOT_RESPOND": 1,
	"ACCEPT":      2,
	"REJECT":      3,
}

func (x MysteryInviteResult) String() string {
	return proto.EnumName(MysteryInviteResult_name, int32(x))
}
func (MysteryInviteResult) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{2}
}

type ScenarioTagType int32

const (
	ScenarioTagType_ScenarioTagType_UNDEFINED ScenarioTagType = 0
	ScenarioTagType_ScenarioTagType_SUBSCRIBE ScenarioTagType = 1
	ScenarioTagType_ScenarioTagType_NEW       ScenarioTagType = 2
	ScenarioTagType_ScenarioTagType_HOT       ScenarioTagType = 3
)

var ScenarioTagType_name = map[int32]string{
	0: "ScenarioTagType_UNDEFINED",
	1: "ScenarioTagType_SUBSCRIBE",
	2: "ScenarioTagType_NEW",
	3: "ScenarioTagType_HOT",
}
var ScenarioTagType_value = map[string]int32{
	"ScenarioTagType_UNDEFINED": 0,
	"ScenarioTagType_SUBSCRIBE": 1,
	"ScenarioTagType_NEW":       2,
	"ScenarioTagType_HOT":       3,
}

func (x ScenarioTagType) String() string {
	return proto.EnumName(ScenarioTagType_name, int32(x))
}
func (ScenarioTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{3}
}

type ScenarioTabPlayMode int32

const (
	ScenarioTabPlayMode_None   ScenarioTabPlayMode = 0
	ScenarioTabPlayMode_Single ScenarioTabPlayMode = 1
	ScenarioTabPlayMode_Multi  ScenarioTabPlayMode = 2
)

var ScenarioTabPlayMode_name = map[int32]string{
	0: "None",
	1: "Single",
	2: "Multi",
}
var ScenarioTabPlayMode_value = map[string]int32{
	"None":   0,
	"Single": 1,
	"Multi":  2,
}

func (x ScenarioTabPlayMode) String() string {
	return proto.EnumName(ScenarioTabPlayMode_name, int32(x))
}
func (ScenarioTabPlayMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{4}
}

type ScenarioTabStatus int32

const (
	ScenarioTabStatus_ScenarioTabStatus_UNDEFINED ScenarioTabStatus = 0
	ScenarioTabStatus_ScenarioTabStatus_NEW       ScenarioTabStatus = 1
	ScenarioTabStatus_ScenarioTabStatus_PALYED    ScenarioTabStatus = 2
)

var ScenarioTabStatus_name = map[int32]string{
	0: "ScenarioTabStatus_UNDEFINED",
	1: "ScenarioTabStatus_NEW",
	2: "ScenarioTabStatus_PALYED",
}
var ScenarioTabStatus_value = map[string]int32{
	"ScenarioTabStatus_UNDEFINED": 0,
	"ScenarioTabStatus_NEW":       1,
	"ScenarioTabStatus_PALYED":    2,
}

func (x ScenarioTabStatus) String() string {
	return proto.EnumName(ScenarioTabStatus_name, int32(x))
}
func (ScenarioTabStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{5}
}

type RecommendedScenarioType int32

const (
	RecommendedScenarioType_RecommendedScenarioType_UNDEFINED RecommendedScenarioType = 0
	RecommendedScenarioType_RecommendedScenarioType_ALL       RecommendedScenarioType = 1
	RecommendedScenarioType_RecommendedScenarioType_NEW       RecommendedScenarioType = 2
	RecommendedScenarioType_RecommendedScenarioType_SUBSCRIBE RecommendedScenarioType = 3
)

var RecommendedScenarioType_name = map[int32]string{
	0: "RecommendedScenarioType_UNDEFINED",
	1: "RecommendedScenarioType_ALL",
	2: "RecommendedScenarioType_NEW",
	3: "RecommendedScenarioType_SUBSCRIBE",
}
var RecommendedScenarioType_value = map[string]int32{
	"RecommendedScenarioType_UNDEFINED": 0,
	"RecommendedScenarioType_ALL":       1,
	"RecommendedScenarioType_NEW":       2,
	"RecommendedScenarioType_SUBSCRIBE": 3,
}

func (x RecommendedScenarioType) String() string {
	return proto.EnumName(RecommendedScenarioType_name, int32(x))
}
func (RecommendedScenarioType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{6}
}

// 筛选表头
type MysteryPlaceBlockEnum int32

const (
	MysteryPlaceBlockEnum_RoomMode      MysteryPlaceBlockEnum = 0
	MysteryPlaceBlockEnum_GameCondition MysteryPlaceBlockEnum = 1
)

var MysteryPlaceBlockEnum_name = map[int32]string{
	0: "RoomMode",
	1: "GameCondition",
}
var MysteryPlaceBlockEnum_value = map[string]int32{
	"RoomMode":      0,
	"GameCondition": 1,
}

func (x MysteryPlaceBlockEnum) String() string {
	return proto.EnumName(MysteryPlaceBlockEnum_name, int32(x))
}
func (MysteryPlaceBlockEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{7}
}

// 筛选项
type MysteryPlaceElemEnum int32

const (
	MysteryPlaceElemEnum_NoLimit                    MysteryPlaceElemEnum = 0
	MysteryPlaceElemEnum_MysteryPlaceRoomModeSingle MysteryPlaceElemEnum = 1
	MysteryPlaceElemEnum_MysteryPlaceRoomModeDouble MysteryPlaceElemEnum = 2
	MysteryPlaceElemEnum_Waiting                    MysteryPlaceElemEnum = 3
	MysteryPlaceElemEnum_Started                    MysteryPlaceElemEnum = 4
)

var MysteryPlaceElemEnum_name = map[int32]string{
	0: "NoLimit",
	1: "MysteryPlaceRoomModeSingle",
	2: "MysteryPlaceRoomModeDouble",
	3: "Waiting",
	4: "Started",
}
var MysteryPlaceElemEnum_value = map[string]int32{
	"NoLimit":                    0,
	"MysteryPlaceRoomModeSingle": 1,
	"MysteryPlaceRoomModeDouble": 2,
	"Waiting":                    3,
	"Started":                    4,
}

func (x MysteryPlaceElemEnum) String() string {
	return proto.EnumName(MysteryPlaceElemEnum_name, int32(x))
}
func (MysteryPlaceElemEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{8}
}

type MysteryPlaceRCMDLabelEnum int32

const (
	MysteryPlaceRCMDLabelEnum_RCMDLabel_None                MysteryPlaceRCMDLabelEnum = 0
	MysteryPlaceRCMDLabelEnum_RCMDLabel_GangUpWithHomeOwner MysteryPlaceRCMDLabelEnum = 1
	MysteryPlaceRCMDLabelEnum_RCMDLabel_ChatWithHomeOwner   MysteryPlaceRCMDLabelEnum = 2
)

var MysteryPlaceRCMDLabelEnum_name = map[int32]string{
	0: "RCMDLabel_None",
	1: "RCMDLabel_GangUpWithHomeOwner",
	2: "RCMDLabel_ChatWithHomeOwner",
}
var MysteryPlaceRCMDLabelEnum_value = map[string]int32{
	"RCMDLabel_None":                0,
	"RCMDLabel_GangUpWithHomeOwner": 1,
	"RCMDLabel_ChatWithHomeOwner":   2,
}

func (x MysteryPlaceRCMDLabelEnum) String() string {
	return proto.EnumName(MysteryPlaceRCMDLabelEnum_name, int32(x))
}
func (MysteryPlaceRCMDLabelEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{9}
}

type PlaymateTagType int32

const (
	PlaymateTagType_PlaymateTagType_UNDEFINED PlaymateTagType = 0
	PlaymateTagType_PlaymateTagType_GOOD      PlaymateTagType = 1
	PlaymateTagType_PlaymateTagType_BAD       PlaymateTagType = 2
)

var PlaymateTagType_name = map[int32]string{
	0: "PlaymateTagType_UNDEFINED",
	1: "PlaymateTagType_GOOD",
	2: "PlaymateTagType_BAD",
}
var PlaymateTagType_value = map[string]int32{
	"PlaymateTagType_UNDEFINED": 0,
	"PlaymateTagType_GOOD":      1,
	"PlaymateTagType_BAD":       2,
}

func (x PlaymateTagType) String() string {
	return proto.EnumName(PlaymateTagType_name, int32(x))
}
func (PlaymateTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{10}
}

type NewScenarioTipType int32

const (
	NewScenarioTipType_NewScenarioTipType_UNDEFINED NewScenarioTipType = 0
	NewScenarioTipType_NewScenarioTipType_NEW_TAB   NewScenarioTipType = 1
	NewScenarioTipType_NewScenarioTipType_SUBSCRIBE NewScenarioTipType = 2
	NewScenarioTipType_NewScenarioTipType_NEW       NewScenarioTipType = 3
)

var NewScenarioTipType_name = map[int32]string{
	0: "NewScenarioTipType_UNDEFINED",
	1: "NewScenarioTipType_NEW_TAB",
	2: "NewScenarioTipType_SUBSCRIBE",
	3: "NewScenarioTipType_NEW",
}
var NewScenarioTipType_value = map[string]int32{
	"NewScenarioTipType_UNDEFINED": 0,
	"NewScenarioTipType_NEW_TAB":   1,
	"NewScenarioTipType_SUBSCRIBE": 2,
	"NewScenarioTipType_NEW":       3,
}

func (x NewScenarioTipType) String() string {
	return proto.EnumName(NewScenarioTipType_name, int32(x))
}
func (NewScenarioTipType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{11}
}

type ScenarioInfo_PlayMode int32

const (
	ScenarioInfo_None   ScenarioInfo_PlayMode = 0
	ScenarioInfo_Single ScenarioInfo_PlayMode = 1
	ScenarioInfo_Multi  ScenarioInfo_PlayMode = 2
)

var ScenarioInfo_PlayMode_name = map[int32]string{
	0: "None",
	1: "Single",
	2: "Multi",
}
var ScenarioInfo_PlayMode_value = map[string]int32{
	"None":   0,
	"Single": 1,
	"Multi":  2,
}

func (x ScenarioInfo_PlayMode) String() string {
	return proto.EnumName(ScenarioInfo_PlayMode_name, int32(x))
}
func (ScenarioInfo_PlayMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{0, 0}
}

type LoginTask_State int32

const (
	LoginTask_StateNone       LoginTask_State = 0
	LoginTask_StateUnreceived LoginTask_State = 1
	LoginTask_StateReceived   LoginTask_State = 2
	LoginTask_StateEnded      LoginTask_State = 3
)

var LoginTask_State_name = map[int32]string{
	0: "StateNone",
	1: "StateUnreceived",
	2: "StateReceived",
	3: "StateEnded",
}
var LoginTask_State_value = map[string]int32{
	"StateNone":       0,
	"StateUnreceived": 1,
	"StateReceived":   2,
	"StateEnded":      3,
}

func (x LoginTask_State) String() string {
	return proto.EnumName(LoginTask_State_name, int32(x))
}
func (LoginTask_State) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{5, 0}
}

type MysteryPlaceChannelListReq_Sex int32

const (
	MysteryPlaceChannelListReq_UNDEFINED MysteryPlaceChannelListReq_Sex = 0
	MysteryPlaceChannelListReq_FEMALE    MysteryPlaceChannelListReq_Sex = 1
	MysteryPlaceChannelListReq_MALE      MysteryPlaceChannelListReq_Sex = 2
)

var MysteryPlaceChannelListReq_Sex_name = map[int32]string{
	0: "UNDEFINED",
	1: "FEMALE",
	2: "MALE",
}
var MysteryPlaceChannelListReq_Sex_value = map[string]int32{
	"UNDEFINED": 0,
	"FEMALE":    1,
	"MALE":      2,
}

func (x MysteryPlaceChannelListReq_Sex) String() string {
	return proto.EnumName(MysteryPlaceChannelListReq_Sex_name, int32(x))
}
func (MysteryPlaceChannelListReq_Sex) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{34, 0}
}

type ScenarioInfo struct {
	Id                      uint32                `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PlayMode                ScenarioInfo_PlayMode `protobuf:"varint,2,opt,name=play_mode,json=playMode,proto3,enum=ga.mystery_place_logic.ScenarioInfo_PlayMode" json:"play_mode,omitempty"`
	TabId                   uint32                `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName                 string                `protobuf:"bytes,4,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Link                    string                `protobuf:"bytes,5,opt,name=link,proto3" json:"link,omitempty"`
	Introduction            string                `protobuf:"bytes,6,opt,name=introduction,proto3" json:"introduction,omitempty"`
	Labels                  []string              `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty"`
	NewDisplayPicture       string                `protobuf:"bytes,8,opt,name=new_display_picture,json=newDisplayPicture,proto3" json:"new_display_picture,omitempty"`
	OldDisplayPicture       string                `protobuf:"bytes,9,opt,name=old_display_picture,json=oldDisplayPicture,proto3" json:"old_display_picture,omitempty"`
	BigSharePicture         string                `protobuf:"bytes,10,opt,name=big_share_picture,json=bigSharePicture,proto3" json:"big_share_picture,omitempty"`
	SmallSharePicture       string                `protobuf:"bytes,11,opt,name=small_share_picture,json=smallSharePicture,proto3" json:"small_share_picture,omitempty"`
	Video                   string                `protobuf:"bytes,12,opt,name=video,proto3" json:"video,omitempty"`
	Title                   string                `protobuf:"bytes,13,opt,name=title,proto3" json:"title,omitempty"`
	LabelsColor             string                `protobuf:"bytes,14,opt,name=labels_color,json=labelsColor,proto3" json:"labels_color,omitempty"`
	Rate                    string                `protobuf:"bytes,15,opt,name=rate,proto3" json:"rate,omitempty"`
	TabIds                  []uint32              `protobuf:"varint,16,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	ScenarioSmallPicture    string                `protobuf:"bytes,17,opt,name=scenario_small_picture,json=scenarioSmallPicture,proto3" json:"scenario_small_picture,omitempty"`
	NewScenarioSmallPicture string                `protobuf:"bytes,18,opt,name=new_scenario_small_picture,json=newScenarioSmallPicture,proto3" json:"new_scenario_small_picture,omitempty"`
	CharacterInfo           []*CharacterInfo      `protobuf:"bytes,19,rep,name=character_info,json=characterInfo,proto3" json:"character_info,omitempty"`
	Score                   float32               `protobuf:"fixed32,20,opt,name=score,proto3" json:"score,omitempty"`
	RecommendVideo          string                `protobuf:"bytes,21,opt,name=recommend_video,json=recommendVideo,proto3" json:"recommend_video,omitempty"`
	RecommendPicture        []string              `protobuf:"bytes,22,rep,name=recommend_picture,json=recommendPicture,proto3" json:"recommend_picture,omitempty"`
	MinPlayerNum            uint32                `protobuf:"varint,23,opt,name=min_player_num,json=minPlayerNum,proto3" json:"min_player_num,omitempty"`
	GameDifficulty          string                `protobuf:"bytes,24,opt,name=game_difficulty,json=gameDifficulty,proto3" json:"game_difficulty,omitempty"`
	EstimateFinishTime      string                `protobuf:"bytes,25,opt,name=estimate_finish_time,json=estimateFinishTime,proto3" json:"estimate_finish_time,omitempty"`
	BindTopicIds            []string              `protobuf:"bytes,26,rep,name=bind_topic_ids,json=bindTopicIds,proto3" json:"bind_topic_ids,omitempty"`
	GameOrientation         GameOrientation       `protobuf:"varint,27,opt,name=game_orientation,json=gameOrientation,proto3,enum=ga.mystery_place_logic.GameOrientation" json:"game_orientation,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}              `json:"-"`
	XXX_unrecognized        []byte                `json:"-"`
	XXX_sizecache           int32                 `json:"-"`
}

func (m *ScenarioInfo) Reset()         { *m = ScenarioInfo{} }
func (m *ScenarioInfo) String() string { return proto.CompactTextString(m) }
func (*ScenarioInfo) ProtoMessage()    {}
func (*ScenarioInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{0}
}
func (m *ScenarioInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioInfo.Unmarshal(m, b)
}
func (m *ScenarioInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioInfo.Marshal(b, m, deterministic)
}
func (dst *ScenarioInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioInfo.Merge(dst, src)
}
func (m *ScenarioInfo) XXX_Size() int {
	return xxx_messageInfo_ScenarioInfo.Size(m)
}
func (m *ScenarioInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioInfo proto.InternalMessageInfo

func (m *ScenarioInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ScenarioInfo) GetPlayMode() ScenarioInfo_PlayMode {
	if m != nil {
		return m.PlayMode
	}
	return ScenarioInfo_None
}

func (m *ScenarioInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ScenarioInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *ScenarioInfo) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *ScenarioInfo) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *ScenarioInfo) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *ScenarioInfo) GetNewDisplayPicture() string {
	if m != nil {
		return m.NewDisplayPicture
	}
	return ""
}

func (m *ScenarioInfo) GetOldDisplayPicture() string {
	if m != nil {
		return m.OldDisplayPicture
	}
	return ""
}

func (m *ScenarioInfo) GetBigSharePicture() string {
	if m != nil {
		return m.BigSharePicture
	}
	return ""
}

func (m *ScenarioInfo) GetSmallSharePicture() string {
	if m != nil {
		return m.SmallSharePicture
	}
	return ""
}

func (m *ScenarioInfo) GetVideo() string {
	if m != nil {
		return m.Video
	}
	return ""
}

func (m *ScenarioInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ScenarioInfo) GetLabelsColor() string {
	if m != nil {
		return m.LabelsColor
	}
	return ""
}

func (m *ScenarioInfo) GetRate() string {
	if m != nil {
		return m.Rate
	}
	return ""
}

func (m *ScenarioInfo) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *ScenarioInfo) GetScenarioSmallPicture() string {
	if m != nil {
		return m.ScenarioSmallPicture
	}
	return ""
}

func (m *ScenarioInfo) GetNewScenarioSmallPicture() string {
	if m != nil {
		return m.NewScenarioSmallPicture
	}
	return ""
}

func (m *ScenarioInfo) GetCharacterInfo() []*CharacterInfo {
	if m != nil {
		return m.CharacterInfo
	}
	return nil
}

func (m *ScenarioInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ScenarioInfo) GetRecommendVideo() string {
	if m != nil {
		return m.RecommendVideo
	}
	return ""
}

func (m *ScenarioInfo) GetRecommendPicture() []string {
	if m != nil {
		return m.RecommendPicture
	}
	return nil
}

func (m *ScenarioInfo) GetMinPlayerNum() uint32 {
	if m != nil {
		return m.MinPlayerNum
	}
	return 0
}

func (m *ScenarioInfo) GetGameDifficulty() string {
	if m != nil {
		return m.GameDifficulty
	}
	return ""
}

func (m *ScenarioInfo) GetEstimateFinishTime() string {
	if m != nil {
		return m.EstimateFinishTime
	}
	return ""
}

func (m *ScenarioInfo) GetBindTopicIds() []string {
	if m != nil {
		return m.BindTopicIds
	}
	return nil
}

func (m *ScenarioInfo) GetGameOrientation() GameOrientation {
	if m != nil {
		return m.GameOrientation
	}
	return GameOrientation_GAME_ORIENTATION_UNDEFINED
}

// 获取列表接口
type ListScenarioInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ListScenarioInfoReq) Reset()         { *m = ListScenarioInfoReq{} }
func (m *ListScenarioInfoReq) String() string { return proto.CompactTextString(m) }
func (*ListScenarioInfoReq) ProtoMessage()    {}
func (*ListScenarioInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{1}
}
func (m *ListScenarioInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListScenarioInfoReq.Unmarshal(m, b)
}
func (m *ListScenarioInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListScenarioInfoReq.Marshal(b, m, deterministic)
}
func (dst *ListScenarioInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListScenarioInfoReq.Merge(dst, src)
}
func (m *ListScenarioInfoReq) XXX_Size() int {
	return xxx_messageInfo_ListScenarioInfoReq.Size(m)
}
func (m *ListScenarioInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListScenarioInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListScenarioInfoReq proto.InternalMessageInfo

func (m *ListScenarioInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

// 详情页面
type ListScenarioInfoResp struct {
	BaseResp             *app.BaseResp   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Infos                []*ScenarioInfo `protobuf:"bytes,2,rep,name=infos,proto3" json:"infos,omitempty"`
	OldUser              bool            `protobuf:"varint,3,opt,name=old_user,json=oldUser,proto3" json:"old_user,omitempty"`
	VoucherBalance       uint32          `protobuf:"varint,4,opt,name=voucher_balance,json=voucherBalance,proto3" json:"voucher_balance,omitempty"`
	LastRecordText       string          `protobuf:"bytes,5,opt,name=last_record_text,json=lastRecordText,proto3" json:"last_record_text,omitempty"`
	LastPlayingScenario  *ScenarioInfo   `protobuf:"bytes,6,opt,name=last_playing_scenario,json=lastPlayingScenario,proto3" json:"last_playing_scenario,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListScenarioInfoResp) Reset()         { *m = ListScenarioInfoResp{} }
func (m *ListScenarioInfoResp) String() string { return proto.CompactTextString(m) }
func (*ListScenarioInfoResp) ProtoMessage()    {}
func (*ListScenarioInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{2}
}
func (m *ListScenarioInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListScenarioInfoResp.Unmarshal(m, b)
}
func (m *ListScenarioInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListScenarioInfoResp.Marshal(b, m, deterministic)
}
func (dst *ListScenarioInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListScenarioInfoResp.Merge(dst, src)
}
func (m *ListScenarioInfoResp) XXX_Size() int {
	return xxx_messageInfo_ListScenarioInfoResp.Size(m)
}
func (m *ListScenarioInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListScenarioInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListScenarioInfoResp proto.InternalMessageInfo

func (m *ListScenarioInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListScenarioInfoResp) GetInfos() []*ScenarioInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *ListScenarioInfoResp) GetOldUser() bool {
	if m != nil {
		return m.OldUser
	}
	return false
}

func (m *ListScenarioInfoResp) GetVoucherBalance() uint32 {
	if m != nil {
		return m.VoucherBalance
	}
	return 0
}

func (m *ListScenarioInfoResp) GetLastRecordText() string {
	if m != nil {
		return m.LastRecordText
	}
	return ""
}

func (m *ListScenarioInfoResp) GetLastPlayingScenario() *ScenarioInfo {
	if m != nil {
		return m.LastPlayingScenario
	}
	return nil
}

type GetScenarioInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Id                   uint32       `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetScenarioInfoReq) Reset()         { *m = GetScenarioInfoReq{} }
func (m *GetScenarioInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetScenarioInfoReq) ProtoMessage()    {}
func (*GetScenarioInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{3}
}
func (m *GetScenarioInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioInfoReq.Unmarshal(m, b)
}
func (m *GetScenarioInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetScenarioInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioInfoReq.Merge(dst, src)
}
func (m *GetScenarioInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetScenarioInfoReq.Size(m)
}
func (m *GetScenarioInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioInfoReq proto.InternalMessageInfo

func (m *GetScenarioInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetScenarioInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetScenarioInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ScenarioInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	OldUser              bool          `protobuf:"varint,3,opt,name=old_user,json=oldUser,proto3" json:"old_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetScenarioInfoResp) Reset()         { *m = GetScenarioInfoResp{} }
func (m *GetScenarioInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetScenarioInfoResp) ProtoMessage()    {}
func (*GetScenarioInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{4}
}
func (m *GetScenarioInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioInfoResp.Unmarshal(m, b)
}
func (m *GetScenarioInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetScenarioInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioInfoResp.Merge(dst, src)
}
func (m *GetScenarioInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetScenarioInfoResp.Size(m)
}
func (m *GetScenarioInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioInfoResp proto.InternalMessageInfo

func (m *GetScenarioInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetScenarioInfoResp) GetInfo() *ScenarioInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetScenarioInfoResp) GetOldUser() bool {
	if m != nil {
		return m.OldUser
	}
	return false
}

// 登录任务
type LoginTask struct {
	// 任务标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 任务状态
	State LoginTask_State `protobuf:"varint,2,opt,name=state,proto3,enum=ga.mystery_place_logic.LoginTask_State" json:"state,omitempty"`
	// 任务描述
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	// 任务背景图
	Background string `protobuf:"bytes,4,opt,name=background,proto3" json:"background,omitempty"`
	// 弹窗 ICON 图
	PopupIcon            string   `protobuf:"bytes,5,opt,name=popup_icon,json=popupIcon,proto3" json:"popup_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoginTask) Reset()         { *m = LoginTask{} }
func (m *LoginTask) String() string { return proto.CompactTextString(m) }
func (*LoginTask) ProtoMessage()    {}
func (*LoginTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{5}
}
func (m *LoginTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoginTask.Unmarshal(m, b)
}
func (m *LoginTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoginTask.Marshal(b, m, deterministic)
}
func (dst *LoginTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoginTask.Merge(dst, src)
}
func (m *LoginTask) XXX_Size() int {
	return xxx_messageInfo_LoginTask.Size(m)
}
func (m *LoginTask) XXX_DiscardUnknown() {
	xxx_messageInfo_LoginTask.DiscardUnknown(m)
}

var xxx_messageInfo_LoginTask proto.InternalMessageInfo

func (m *LoginTask) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *LoginTask) GetState() LoginTask_State {
	if m != nil {
		return m.State
	}
	return LoginTask_StateNone
}

func (m *LoginTask) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *LoginTask) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *LoginTask) GetPopupIcon() string {
	if m != nil {
		return m.PopupIcon
	}
	return ""
}

type GetLoginTaskReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetLoginTaskReq) Reset()         { *m = GetLoginTaskReq{} }
func (m *GetLoginTaskReq) String() string { return proto.CompactTextString(m) }
func (*GetLoginTaskReq) ProtoMessage()    {}
func (*GetLoginTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{6}
}
func (m *GetLoginTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLoginTaskReq.Unmarshal(m, b)
}
func (m *GetLoginTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLoginTaskReq.Marshal(b, m, deterministic)
}
func (dst *GetLoginTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLoginTaskReq.Merge(dst, src)
}
func (m *GetLoginTaskReq) XXX_Size() int {
	return xxx_messageInfo_GetLoginTaskReq.Size(m)
}
func (m *GetLoginTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLoginTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLoginTaskReq proto.InternalMessageInfo

func (m *GetLoginTaskReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetLoginTaskResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LoginTask            *LoginTask    `protobuf:"bytes,2,opt,name=login_task,json=loginTask,proto3" json:"login_task,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetLoginTaskResp) Reset()         { *m = GetLoginTaskResp{} }
func (m *GetLoginTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetLoginTaskResp) ProtoMessage()    {}
func (*GetLoginTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{7}
}
func (m *GetLoginTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLoginTaskResp.Unmarshal(m, b)
}
func (m *GetLoginTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLoginTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetLoginTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLoginTaskResp.Merge(dst, src)
}
func (m *GetLoginTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetLoginTaskResp.Size(m)
}
func (m *GetLoginTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLoginTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLoginTaskResp proto.InternalMessageInfo

func (m *GetLoginTaskResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetLoginTaskResp) GetLoginTask() *LoginTask {
	if m != nil {
		return m.LoginTask
	}
	return nil
}

type ReceiveLoginTaskAwardReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ReceiveLoginTaskAwardReq) Reset()         { *m = ReceiveLoginTaskAwardReq{} }
func (m *ReceiveLoginTaskAwardReq) String() string { return proto.CompactTextString(m) }
func (*ReceiveLoginTaskAwardReq) ProtoMessage()    {}
func (*ReceiveLoginTaskAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{8}
}
func (m *ReceiveLoginTaskAwardReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveLoginTaskAwardReq.Unmarshal(m, b)
}
func (m *ReceiveLoginTaskAwardReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveLoginTaskAwardReq.Marshal(b, m, deterministic)
}
func (dst *ReceiveLoginTaskAwardReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveLoginTaskAwardReq.Merge(dst, src)
}
func (m *ReceiveLoginTaskAwardReq) XXX_Size() int {
	return xxx_messageInfo_ReceiveLoginTaskAwardReq.Size(m)
}
func (m *ReceiveLoginTaskAwardReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveLoginTaskAwardReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveLoginTaskAwardReq proto.InternalMessageInfo

func (m *ReceiveLoginTaskAwardReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type ReceiveLoginTaskAwardResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReceiveLoginTaskAwardResp) Reset()         { *m = ReceiveLoginTaskAwardResp{} }
func (m *ReceiveLoginTaskAwardResp) String() string { return proto.CompactTextString(m) }
func (*ReceiveLoginTaskAwardResp) ProtoMessage()    {}
func (*ReceiveLoginTaskAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{9}
}
func (m *ReceiveLoginTaskAwardResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveLoginTaskAwardResp.Unmarshal(m, b)
}
func (m *ReceiveLoginTaskAwardResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveLoginTaskAwardResp.Marshal(b, m, deterministic)
}
func (dst *ReceiveLoginTaskAwardResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveLoginTaskAwardResp.Merge(dst, src)
}
func (m *ReceiveLoginTaskAwardResp) XXX_Size() int {
	return xxx_messageInfo_ReceiveLoginTaskAwardResp.Size(m)
}
func (m *ReceiveLoginTaskAwardResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveLoginTaskAwardResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveLoginTaskAwardResp proto.InternalMessageInfo

func (m *ReceiveLoginTaskAwardResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetBalanceInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetBalanceInfoReq) Reset()         { *m = GetBalanceInfoReq{} }
func (m *GetBalanceInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBalanceInfoReq) ProtoMessage()    {}
func (*GetBalanceInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{10}
}
func (m *GetBalanceInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBalanceInfoReq.Unmarshal(m, b)
}
func (m *GetBalanceInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBalanceInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBalanceInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBalanceInfoReq.Merge(dst, src)
}
func (m *GetBalanceInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBalanceInfoReq.Size(m)
}
func (m *GetBalanceInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBalanceInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBalanceInfoReq proto.InternalMessageInfo

func (m *GetBalanceInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetBalanceInfoResp struct {
	BaseResp *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	// 消费券余额
	VoucherBalance uint32 `protobuf:"varint,2,opt,name=voucher_balance,json=voucherBalance,proto3" json:"voucher_balance,omitempty"`
	// 登录任务信息
	LoginTask            *LoginTask `protobuf:"bytes,3,opt,name=login_task,json=loginTask,proto3" json:"login_task,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetBalanceInfoResp) Reset()         { *m = GetBalanceInfoResp{} }
func (m *GetBalanceInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBalanceInfoResp) ProtoMessage()    {}
func (*GetBalanceInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{11}
}
func (m *GetBalanceInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBalanceInfoResp.Unmarshal(m, b)
}
func (m *GetBalanceInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBalanceInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBalanceInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBalanceInfoResp.Merge(dst, src)
}
func (m *GetBalanceInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBalanceInfoResp.Size(m)
}
func (m *GetBalanceInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBalanceInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBalanceInfoResp proto.InternalMessageInfo

func (m *GetBalanceInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetBalanceInfoResp) GetVoucherBalance() uint32 {
	if m != nil {
		return m.VoucherBalance
	}
	return 0
}

func (m *GetBalanceInfoResp) GetLoginTask() *LoginTask {
	if m != nil {
		return m.LoginTask
	}
	return nil
}

type ShareScenarioMsg struct {
	ScenarioInfo         *ScenarioInfo `protobuf:"bytes,1,opt,name=scenario_info,json=scenarioInfo,proto3" json:"scenario_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ShareScenarioMsg) Reset()         { *m = ShareScenarioMsg{} }
func (m *ShareScenarioMsg) String() string { return proto.CompactTextString(m) }
func (*ShareScenarioMsg) ProtoMessage()    {}
func (*ShareScenarioMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{12}
}
func (m *ShareScenarioMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShareScenarioMsg.Unmarshal(m, b)
}
func (m *ShareScenarioMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShareScenarioMsg.Marshal(b, m, deterministic)
}
func (dst *ShareScenarioMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShareScenarioMsg.Merge(dst, src)
}
func (m *ShareScenarioMsg) XXX_Size() int {
	return xxx_messageInfo_ShareScenarioMsg.Size(m)
}
func (m *ShareScenarioMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ShareScenarioMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ShareScenarioMsg proto.InternalMessageInfo

func (m *ShareScenarioMsg) GetScenarioInfo() *ScenarioInfo {
	if m != nil {
		return m.ScenarioInfo
	}
	return nil
}

type CheckScenarioRoomReq struct {
	BaseReq           *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ToChannel         uint32       `protobuf:"varint,2,opt,name=to_channel,json=toChannel,proto3" json:"to_channel,omitempty"`
	ToChannelOwnerUid uint32       `protobuf:"varint,3,opt,name=to_channel_owner_uid,json=toChannelOwnerUid,proto3" json:"to_channel_owner_uid,omitempty"`
	// 做校验，防止房间已经切换玩法了。首页传剧本下所有tabid，房间内传当前tabid
	TabList              []uint32 `protobuf:"varint,4,rep,packed,name=tab_list,json=tabList,proto3" json:"tab_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckScenarioRoomReq) Reset()         { *m = CheckScenarioRoomReq{} }
func (m *CheckScenarioRoomReq) String() string { return proto.CompactTextString(m) }
func (*CheckScenarioRoomReq) ProtoMessage()    {}
func (*CheckScenarioRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{13}
}
func (m *CheckScenarioRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckScenarioRoomReq.Unmarshal(m, b)
}
func (m *CheckScenarioRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckScenarioRoomReq.Marshal(b, m, deterministic)
}
func (dst *CheckScenarioRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckScenarioRoomReq.Merge(dst, src)
}
func (m *CheckScenarioRoomReq) XXX_Size() int {
	return xxx_messageInfo_CheckScenarioRoomReq.Size(m)
}
func (m *CheckScenarioRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckScenarioRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckScenarioRoomReq proto.InternalMessageInfo

func (m *CheckScenarioRoomReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckScenarioRoomReq) GetToChannel() uint32 {
	if m != nil {
		return m.ToChannel
	}
	return 0
}

func (m *CheckScenarioRoomReq) GetToChannelOwnerUid() uint32 {
	if m != nil {
		return m.ToChannelOwnerUid
	}
	return 0
}

func (m *CheckScenarioRoomReq) GetTabList() []uint32 {
	if m != nil {
		return m.TabList
	}
	return nil
}

type CheckScenarioRoomResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TeamStatus           MysteryTeamStatus `protobuf:"varint,2,opt,name=team_status,json=teamStatus,proto3,enum=ga.mystery_place_logic.MysteryTeamStatus" json:"team_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CheckScenarioRoomResp) Reset()         { *m = CheckScenarioRoomResp{} }
func (m *CheckScenarioRoomResp) String() string { return proto.CompactTextString(m) }
func (*CheckScenarioRoomResp) ProtoMessage()    {}
func (*CheckScenarioRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{14}
}
func (m *CheckScenarioRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckScenarioRoomResp.Unmarshal(m, b)
}
func (m *CheckScenarioRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckScenarioRoomResp.Marshal(b, m, deterministic)
}
func (dst *CheckScenarioRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckScenarioRoomResp.Merge(dst, src)
}
func (m *CheckScenarioRoomResp) XXX_Size() int {
	return xxx_messageInfo_CheckScenarioRoomResp.Size(m)
}
func (m *CheckScenarioRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckScenarioRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckScenarioRoomResp proto.InternalMessageInfo

func (m *CheckScenarioRoomResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckScenarioRoomResp) GetTeamStatus() MysteryTeamStatus {
	if m != nil {
		return m.TeamStatus
	}
	return MysteryTeamStatus_STATUS_NONE
}

type Invite2MyRoomReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ToChannel            uint32       `protobuf:"varint,2,opt,name=to_channel,json=toChannel,proto3" json:"to_channel,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	InviteUid            uint32       `protobuf:"varint,4,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid,omitempty"`
	InviteChannel        uint32       `protobuf:"varint,5,opt,name=invite_channel,json=inviteChannel,proto3" json:"invite_channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *Invite2MyRoomReq) Reset()         { *m = Invite2MyRoomReq{} }
func (m *Invite2MyRoomReq) String() string { return proto.CompactTextString(m) }
func (*Invite2MyRoomReq) ProtoMessage()    {}
func (*Invite2MyRoomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{15}
}
func (m *Invite2MyRoomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Invite2MyRoomReq.Unmarshal(m, b)
}
func (m *Invite2MyRoomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Invite2MyRoomReq.Marshal(b, m, deterministic)
}
func (dst *Invite2MyRoomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Invite2MyRoomReq.Merge(dst, src)
}
func (m *Invite2MyRoomReq) XXX_Size() int {
	return xxx_messageInfo_Invite2MyRoomReq.Size(m)
}
func (m *Invite2MyRoomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_Invite2MyRoomReq.DiscardUnknown(m)
}

var xxx_messageInfo_Invite2MyRoomReq proto.InternalMessageInfo

func (m *Invite2MyRoomReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *Invite2MyRoomReq) GetToChannel() uint32 {
	if m != nil {
		return m.ToChannel
	}
	return 0
}

func (m *Invite2MyRoomReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *Invite2MyRoomReq) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

func (m *Invite2MyRoomReq) GetInviteChannel() uint32 {
	if m != nil {
		return m.InviteChannel
	}
	return 0
}

type Invite2MyRoomResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TeamStatus           MysteryTeamStatus `protobuf:"varint,2,opt,name=team_status,json=teamStatus,proto3,enum=ga.mystery_place_logic.MysteryTeamStatus" json:"team_status,omitempty"`
	InviteId             string            `protobuf:"bytes,3,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Invite2MyRoomResp) Reset()         { *m = Invite2MyRoomResp{} }
func (m *Invite2MyRoomResp) String() string { return proto.CompactTextString(m) }
func (*Invite2MyRoomResp) ProtoMessage()    {}
func (*Invite2MyRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{16}
}
func (m *Invite2MyRoomResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Invite2MyRoomResp.Unmarshal(m, b)
}
func (m *Invite2MyRoomResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Invite2MyRoomResp.Marshal(b, m, deterministic)
}
func (dst *Invite2MyRoomResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Invite2MyRoomResp.Merge(dst, src)
}
func (m *Invite2MyRoomResp) XXX_Size() int {
	return xxx_messageInfo_Invite2MyRoomResp.Size(m)
}
func (m *Invite2MyRoomResp) XXX_DiscardUnknown() {
	xxx_messageInfo_Invite2MyRoomResp.DiscardUnknown(m)
}

var xxx_messageInfo_Invite2MyRoomResp proto.InternalMessageInfo

func (m *Invite2MyRoomResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *Invite2MyRoomResp) GetTeamStatus() MysteryTeamStatus {
	if m != nil {
		return m.TeamStatus
	}
	return MysteryTeamStatus_STATUS_NONE
}

func (m *Invite2MyRoomResp) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

type GetInvite2MyRoomResultReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	InviteId             string       `protobuf:"bytes,2,opt,name=invite_id,json=inviteId,proto3" json:"invite_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetInvite2MyRoomResultReq) Reset()         { *m = GetInvite2MyRoomResultReq{} }
func (m *GetInvite2MyRoomResultReq) String() string { return proto.CompactTextString(m) }
func (*GetInvite2MyRoomResultReq) ProtoMessage()    {}
func (*GetInvite2MyRoomResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{17}
}
func (m *GetInvite2MyRoomResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInvite2MyRoomResultReq.Unmarshal(m, b)
}
func (m *GetInvite2MyRoomResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInvite2MyRoomResultReq.Marshal(b, m, deterministic)
}
func (dst *GetInvite2MyRoomResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInvite2MyRoomResultReq.Merge(dst, src)
}
func (m *GetInvite2MyRoomResultReq) XXX_Size() int {
	return xxx_messageInfo_GetInvite2MyRoomResultReq.Size(m)
}
func (m *GetInvite2MyRoomResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInvite2MyRoomResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInvite2MyRoomResultReq proto.InternalMessageInfo

func (m *GetInvite2MyRoomResultReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetInvite2MyRoomResultReq) GetInviteId() string {
	if m != nil {
		return m.InviteId
	}
	return ""
}

type GetInvite2MyRoomResultResp struct {
	BaseResp             *app.BaseResp       `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	InviteResult         MysteryInviteResult `protobuf:"varint,2,opt,name=invite_result,json=inviteResult,proto3,enum=ga.mystery_place_logic.MysteryInviteResult" json:"invite_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetInvite2MyRoomResultResp) Reset()         { *m = GetInvite2MyRoomResultResp{} }
func (m *GetInvite2MyRoomResultResp) String() string { return proto.CompactTextString(m) }
func (*GetInvite2MyRoomResultResp) ProtoMessage()    {}
func (*GetInvite2MyRoomResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{18}
}
func (m *GetInvite2MyRoomResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInvite2MyRoomResultResp.Unmarshal(m, b)
}
func (m *GetInvite2MyRoomResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInvite2MyRoomResultResp.Marshal(b, m, deterministic)
}
func (dst *GetInvite2MyRoomResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInvite2MyRoomResultResp.Merge(dst, src)
}
func (m *GetInvite2MyRoomResultResp) XXX_Size() int {
	return xxx_messageInfo_GetInvite2MyRoomResultResp.Size(m)
}
func (m *GetInvite2MyRoomResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInvite2MyRoomResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInvite2MyRoomResultResp proto.InternalMessageInfo

func (m *GetInvite2MyRoomResultResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetInvite2MyRoomResultResp) GetInviteResult() MysteryInviteResult {
	if m != nil {
		return m.InviteResult
	}
	return MysteryInviteResult_NOT_FOUND
}

// 邀请别人来我的密逃房，结果通知
type Invite2MyRoomResultPush struct {
	InviteUid            uint32              `protobuf:"varint,1,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid,omitempty"`
	ToChannel            uint32              `protobuf:"varint,2,opt,name=to_channel,json=toChannel,proto3" json:"to_channel,omitempty"`
	InviteResult         MysteryInviteResult `protobuf:"varint,3,opt,name=invite_result,json=inviteResult,proto3,enum=ga.mystery_place_logic.MysteryInviteResult" json:"invite_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *Invite2MyRoomResultPush) Reset()         { *m = Invite2MyRoomResultPush{} }
func (m *Invite2MyRoomResultPush) String() string { return proto.CompactTextString(m) }
func (*Invite2MyRoomResultPush) ProtoMessage()    {}
func (*Invite2MyRoomResultPush) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{19}
}
func (m *Invite2MyRoomResultPush) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Invite2MyRoomResultPush.Unmarshal(m, b)
}
func (m *Invite2MyRoomResultPush) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Invite2MyRoomResultPush.Marshal(b, m, deterministic)
}
func (dst *Invite2MyRoomResultPush) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Invite2MyRoomResultPush.Merge(dst, src)
}
func (m *Invite2MyRoomResultPush) XXX_Size() int {
	return xxx_messageInfo_Invite2MyRoomResultPush.Size(m)
}
func (m *Invite2MyRoomResultPush) XXX_DiscardUnknown() {
	xxx_messageInfo_Invite2MyRoomResultPush.DiscardUnknown(m)
}

var xxx_messageInfo_Invite2MyRoomResultPush proto.InternalMessageInfo

func (m *Invite2MyRoomResultPush) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

func (m *Invite2MyRoomResultPush) GetToChannel() uint32 {
	if m != nil {
		return m.ToChannel
	}
	return 0
}

func (m *Invite2MyRoomResultPush) GetInviteResult() MysteryInviteResult {
	if m != nil {
		return m.InviteResult
	}
	return MysteryInviteResult_NOT_FOUND
}

type ScenarioSimpleInfo struct {
	Id                   uint32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string          `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	ScenarioSmallPicture string          `protobuf:"bytes,3,opt,name=scenario_small_picture,json=scenarioSmallPicture,proto3" json:"scenario_small_picture,omitempty"`
	FirstLabel           string          `protobuf:"bytes,4,opt,name=first_label,json=firstLabel,proto3" json:"first_label,omitempty"`
	LabelsColor          string          `protobuf:"bytes,5,opt,name=labels_color,json=labelsColor,proto3" json:"labels_color,omitempty"`
	TagType              ScenarioTagType `protobuf:"varint,6,opt,name=tag_type,json=tagType,proto3,enum=ga.mystery_place_logic.ScenarioTagType" json:"tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ScenarioSimpleInfo) Reset()         { *m = ScenarioSimpleInfo{} }
func (m *ScenarioSimpleInfo) String() string { return proto.CompactTextString(m) }
func (*ScenarioSimpleInfo) ProtoMessage()    {}
func (*ScenarioSimpleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{20}
}
func (m *ScenarioSimpleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioSimpleInfo.Unmarshal(m, b)
}
func (m *ScenarioSimpleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioSimpleInfo.Marshal(b, m, deterministic)
}
func (dst *ScenarioSimpleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioSimpleInfo.Merge(dst, src)
}
func (m *ScenarioSimpleInfo) XXX_Size() int {
	return xxx_messageInfo_ScenarioSimpleInfo.Size(m)
}
func (m *ScenarioSimpleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioSimpleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioSimpleInfo proto.InternalMessageInfo

func (m *ScenarioSimpleInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ScenarioSimpleInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ScenarioSimpleInfo) GetScenarioSmallPicture() string {
	if m != nil {
		return m.ScenarioSmallPicture
	}
	return ""
}

func (m *ScenarioSimpleInfo) GetFirstLabel() string {
	if m != nil {
		return m.FirstLabel
	}
	return ""
}

func (m *ScenarioSimpleInfo) GetLabelsColor() string {
	if m != nil {
		return m.LabelsColor
	}
	return ""
}

func (m *ScenarioSimpleInfo) GetTagType() ScenarioTagType {
	if m != nil {
		return m.TagType
	}
	return ScenarioTagType_ScenarioTagType_UNDEFINED
}

type RecommendedScenarioDetailInfo struct {
	Id                                 uint32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                              string                  `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	ScenarioSmallPicture               string                  `protobuf:"bytes,3,opt,name=scenario_small_picture,json=scenarioSmallPicture,proto3" json:"scenario_small_picture,omitempty"`
	TabInfos                           []*ScenarioTabInfo      `protobuf:"bytes,4,rep,name=tab_infos,json=tabInfos,proto3" json:"tab_infos,omitempty"`
	Barrage                            []string                `protobuf:"bytes,5,rep,name=barrage,proto3" json:"barrage,omitempty"`
	RecommendVideo                     string                  `protobuf:"bytes,6,opt,name=recommend_video,json=recommendVideo,proto3" json:"recommend_video,omitempty"`
	RecommendPicture                   []string                `protobuf:"bytes,7,rep,name=recommend_picture,json=recommendPicture,proto3" json:"recommend_picture,omitempty"`
	Introduction                       string                  `protobuf:"bytes,8,opt,name=introduction,proto3" json:"introduction,omitempty"`
	EstimateFinishTime                 string                  `protobuf:"bytes,9,opt,name=estimate_finish_time,json=estimateFinishTime,proto3" json:"estimate_finish_time,omitempty"`
	Labels                             []string                `protobuf:"bytes,10,rep,name=labels,proto3" json:"labels,omitempty"`
	LabelsColor                        string                  `protobuf:"bytes,11,opt,name=labels_color,json=labelsColor,proto3" json:"labels_color,omitempty"`
	FirstTabId                         uint32                  `protobuf:"varint,12,opt,name=first_tab_id,json=firstTabId,proto3" json:"first_tab_id,omitempty"`
	CurrentPlayingRoomCount            uint32                  `protobuf:"varint,13,opt,name=current_playing_room_count,json=currentPlayingRoomCount,proto3" json:"current_playing_room_count,omitempty"`
	IsReservation                      bool                    `protobuf:"varint,14,opt,name=is_reservation,json=isReservation,proto3" json:"is_reservation,omitempty"`
	Score                              float32                 `protobuf:"fixed32,15,opt,name=score,proto3" json:"score,omitempty"`
	MinPlayerNum                       uint32                  `protobuf:"varint,16,opt,name=min_player_num,json=minPlayerNum,proto3" json:"min_player_num,omitempty"`
	GameDifficulty                     string                  `protobuf:"bytes,17,opt,name=game_difficulty,json=gameDifficulty,proto3" json:"game_difficulty,omitempty"`
	StartBtn                           string                  `protobuf:"bytes,18,opt,name=start_btn,json=startBtn,proto3" json:"start_btn,omitempty"`
	FindPlaymateBtn                    string                  `protobuf:"bytes,19,opt,name=find_playmate_btn,json=findPlaymateBtn,proto3" json:"find_playmate_btn,omitempty"`
	RolePicture                        string                  `protobuf:"bytes,20,opt,name=role_picture,json=rolePicture,proto3" json:"role_picture,omitempty"`
	NewRecommendPicture                string                  `protobuf:"bytes,21,opt,name=new_recommend_picture,json=newRecommendPicture,proto3" json:"new_recommend_picture,omitempty"`
	InvitePlaymateScenarioSmallPicture string                  `protobuf:"bytes,22,opt,name=invite_playmate_scenario_small_picture,json=invitePlaymateScenarioSmallPicture,proto3" json:"invite_playmate_scenario_small_picture,omitempty"`
	CharacterInfo                      []*CharacterInfo        `protobuf:"bytes,23,rep,name=character_info,json=characterInfo,proto3" json:"character_info,omitempty"`
	IsShowPrice                        bool                    `protobuf:"varint,38,opt,name=is_show_price,json=isShowPrice,proto3" json:"is_show_price,omitempty"`
	PriceNum                           string                  `protobuf:"bytes,39,opt,name=price_num,json=priceNum,proto3" json:"price_num,omitempty"` // Deprecated: Do not use.
	IsTrial                            bool                    `protobuf:"varint,40,opt,name=is_trial,json=isTrial,proto3" json:"is_trial,omitempty"`
	BindTopicIds                       []string                `protobuf:"bytes,41,rep,name=bind_topic_ids,json=bindTopicIds,proto3" json:"bind_topic_ids,omitempty"`
	PriceNumInt                        uint32                  `protobuf:"varint,42,opt,name=price_num_int,json=priceNumInt,proto3" json:"price_num_int,omitempty"`
	Rank                               uint32                  `protobuf:"varint,43,opt,name=rank,proto3" json:"rank,omitempty"`
	RankUrl                            string                  `protobuf:"bytes,44,opt,name=rank_url,json=rankUrl,proto3" json:"rank_url,omitempty"`
	RankIcon                           string                  `protobuf:"bytes,45,opt,name=rank_icon,json=rankIcon,proto3" json:"rank_icon,omitempty"`
	GameOrientation                    GameOrientation         `protobuf:"varint,46,opt,name=game_orientation,json=gameOrientation,proto3,enum=ga.mystery_place_logic.GameOrientation" json:"game_orientation,omitempty"`
	InvitationActivityInfo             *InvitationActivityInfo `protobuf:"bytes,47,opt,name=invitation_activity_info,json=invitationActivityInfo,proto3" json:"invitation_activity_info,omitempty"`
	XXX_NoUnkeyedLiteral               struct{}                `json:"-"`
	XXX_unrecognized                   []byte                  `json:"-"`
	XXX_sizecache                      int32                   `json:"-"`
}

func (m *RecommendedScenarioDetailInfo) Reset()         { *m = RecommendedScenarioDetailInfo{} }
func (m *RecommendedScenarioDetailInfo) String() string { return proto.CompactTextString(m) }
func (*RecommendedScenarioDetailInfo) ProtoMessage()    {}
func (*RecommendedScenarioDetailInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{21}
}
func (m *RecommendedScenarioDetailInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendedScenarioDetailInfo.Unmarshal(m, b)
}
func (m *RecommendedScenarioDetailInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendedScenarioDetailInfo.Marshal(b, m, deterministic)
}
func (dst *RecommendedScenarioDetailInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendedScenarioDetailInfo.Merge(dst, src)
}
func (m *RecommendedScenarioDetailInfo) XXX_Size() int {
	return xxx_messageInfo_RecommendedScenarioDetailInfo.Size(m)
}
func (m *RecommendedScenarioDetailInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendedScenarioDetailInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendedScenarioDetailInfo proto.InternalMessageInfo

func (m *RecommendedScenarioDetailInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecommendedScenarioDetailInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetScenarioSmallPicture() string {
	if m != nil {
		return m.ScenarioSmallPicture
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetTabInfos() []*ScenarioTabInfo {
	if m != nil {
		return m.TabInfos
	}
	return nil
}

func (m *RecommendedScenarioDetailInfo) GetBarrage() []string {
	if m != nil {
		return m.Barrage
	}
	return nil
}

func (m *RecommendedScenarioDetailInfo) GetRecommendVideo() string {
	if m != nil {
		return m.RecommendVideo
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetRecommendPicture() []string {
	if m != nil {
		return m.RecommendPicture
	}
	return nil
}

func (m *RecommendedScenarioDetailInfo) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetEstimateFinishTime() string {
	if m != nil {
		return m.EstimateFinishTime
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *RecommendedScenarioDetailInfo) GetLabelsColor() string {
	if m != nil {
		return m.LabelsColor
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetFirstTabId() uint32 {
	if m != nil {
		return m.FirstTabId
	}
	return 0
}

func (m *RecommendedScenarioDetailInfo) GetCurrentPlayingRoomCount() uint32 {
	if m != nil {
		return m.CurrentPlayingRoomCount
	}
	return 0
}

func (m *RecommendedScenarioDetailInfo) GetIsReservation() bool {
	if m != nil {
		return m.IsReservation
	}
	return false
}

func (m *RecommendedScenarioDetailInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *RecommendedScenarioDetailInfo) GetMinPlayerNum() uint32 {
	if m != nil {
		return m.MinPlayerNum
	}
	return 0
}

func (m *RecommendedScenarioDetailInfo) GetGameDifficulty() string {
	if m != nil {
		return m.GameDifficulty
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetStartBtn() string {
	if m != nil {
		return m.StartBtn
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetFindPlaymateBtn() string {
	if m != nil {
		return m.FindPlaymateBtn
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetRolePicture() string {
	if m != nil {
		return m.RolePicture
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetNewRecommendPicture() string {
	if m != nil {
		return m.NewRecommendPicture
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetInvitePlaymateScenarioSmallPicture() string {
	if m != nil {
		return m.InvitePlaymateScenarioSmallPicture
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetCharacterInfo() []*CharacterInfo {
	if m != nil {
		return m.CharacterInfo
	}
	return nil
}

func (m *RecommendedScenarioDetailInfo) GetIsShowPrice() bool {
	if m != nil {
		return m.IsShowPrice
	}
	return false
}

// Deprecated: Do not use.
func (m *RecommendedScenarioDetailInfo) GetPriceNum() string {
	if m != nil {
		return m.PriceNum
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetIsTrial() bool {
	if m != nil {
		return m.IsTrial
	}
	return false
}

func (m *RecommendedScenarioDetailInfo) GetBindTopicIds() []string {
	if m != nil {
		return m.BindTopicIds
	}
	return nil
}

func (m *RecommendedScenarioDetailInfo) GetPriceNumInt() uint32 {
	if m != nil {
		return m.PriceNumInt
	}
	return 0
}

func (m *RecommendedScenarioDetailInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *RecommendedScenarioDetailInfo) GetRankUrl() string {
	if m != nil {
		return m.RankUrl
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetRankIcon() string {
	if m != nil {
		return m.RankIcon
	}
	return ""
}

func (m *RecommendedScenarioDetailInfo) GetGameOrientation() GameOrientation {
	if m != nil {
		return m.GameOrientation
	}
	return GameOrientation_GAME_ORIENTATION_UNDEFINED
}

func (m *RecommendedScenarioDetailInfo) GetInvitationActivityInfo() *InvitationActivityInfo {
	if m != nil {
		return m.InvitationActivityInfo
	}
	return nil
}

type ScenarioTabInfo struct {
	PlayMode             ScenarioTabPlayMode `protobuf:"varint,2,opt,name=play_mode,json=playMode,proto3,enum=ga.mystery_place_logic.ScenarioTabPlayMode" json:"play_mode,omitempty"`
	TabId                uint32              `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Icon                 string              `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	TabStatus            ScenarioTabStatus   `protobuf:"varint,5,opt,name=tab_status,json=tabStatus,proto3,enum=ga.mystery_place_logic.ScenarioTabStatus" json:"tab_status,omitempty"`
	TabName              string              `protobuf:"bytes,6,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ScenarioTabInfo) Reset()         { *m = ScenarioTabInfo{} }
func (m *ScenarioTabInfo) String() string { return proto.CompactTextString(m) }
func (*ScenarioTabInfo) ProtoMessage()    {}
func (*ScenarioTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{22}
}
func (m *ScenarioTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioTabInfo.Unmarshal(m, b)
}
func (m *ScenarioTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioTabInfo.Marshal(b, m, deterministic)
}
func (dst *ScenarioTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioTabInfo.Merge(dst, src)
}
func (m *ScenarioTabInfo) XXX_Size() int {
	return xxx_messageInfo_ScenarioTabInfo.Size(m)
}
func (m *ScenarioTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioTabInfo proto.InternalMessageInfo

func (m *ScenarioTabInfo) GetPlayMode() ScenarioTabPlayMode {
	if m != nil {
		return m.PlayMode
	}
	return ScenarioTabPlayMode_None
}

func (m *ScenarioTabInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ScenarioTabInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *ScenarioTabInfo) GetTabStatus() ScenarioTabStatus {
	if m != nil {
		return m.TabStatus
	}
	return ScenarioTabStatus_ScenarioTabStatus_UNDEFINED
}

func (m *ScenarioTabInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

// 角色介绍
type CharacterInfo struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,2,opt,name=img,proto3" json:"img,omitempty"`
	Info                 string   `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CharacterInfo) Reset()         { *m = CharacterInfo{} }
func (m *CharacterInfo) String() string { return proto.CompactTextString(m) }
func (*CharacterInfo) ProtoMessage()    {}
func (*CharacterInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{23}
}
func (m *CharacterInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CharacterInfo.Unmarshal(m, b)
}
func (m *CharacterInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CharacterInfo.Marshal(b, m, deterministic)
}
func (dst *CharacterInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CharacterInfo.Merge(dst, src)
}
func (m *CharacterInfo) XXX_Size() int {
	return xxx_messageInfo_CharacterInfo.Size(m)
}
func (m *CharacterInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CharacterInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CharacterInfo proto.InternalMessageInfo

func (m *CharacterInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CharacterInfo) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *CharacterInfo) GetInfo() string {
	if m != nil {
		return m.Info
	}
	return ""
}

// 获取推荐剧本列表缩略信息接口
type ListRecommendedScenarioSimpleInfoReq struct {
	BaseReq              *app.BaseReq                               `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Type                 RecommendedScenarioType                    `protobuf:"varint,2,opt,name=type,proto3,enum=ga.mystery_place_logic.RecommendedScenarioType" json:"type,omitempty"`
	Count                uint32                                     `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ListRecommendedScenarioSimpleInfoLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *ListRecommendedScenarioSimpleInfoReq) Reset()         { *m = ListRecommendedScenarioSimpleInfoReq{} }
func (m *ListRecommendedScenarioSimpleInfoReq) String() string { return proto.CompactTextString(m) }
func (*ListRecommendedScenarioSimpleInfoReq) ProtoMessage()    {}
func (*ListRecommendedScenarioSimpleInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{24}
}
func (m *ListRecommendedScenarioSimpleInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecommendedScenarioSimpleInfoReq.Unmarshal(m, b)
}
func (m *ListRecommendedScenarioSimpleInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecommendedScenarioSimpleInfoReq.Marshal(b, m, deterministic)
}
func (dst *ListRecommendedScenarioSimpleInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecommendedScenarioSimpleInfoReq.Merge(dst, src)
}
func (m *ListRecommendedScenarioSimpleInfoReq) XXX_Size() int {
	return xxx_messageInfo_ListRecommendedScenarioSimpleInfoReq.Size(m)
}
func (m *ListRecommendedScenarioSimpleInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecommendedScenarioSimpleInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecommendedScenarioSimpleInfoReq proto.InternalMessageInfo

func (m *ListRecommendedScenarioSimpleInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListRecommendedScenarioSimpleInfoReq) GetType() RecommendedScenarioType {
	if m != nil {
		return m.Type
	}
	return RecommendedScenarioType_RecommendedScenarioType_UNDEFINED
}

func (m *ListRecommendedScenarioSimpleInfoReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListRecommendedScenarioSimpleInfoReq) GetLoadMore() *ListRecommendedScenarioSimpleInfoLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type ListRecommendedScenarioSimpleInfoResp struct {
	BaseResp             *app.BaseResp                              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Infos                []*ScenarioSimpleInfo                      `protobuf:"bytes,2,rep,name=infos,proto3" json:"infos,omitempty"`
	LoadMore             *ListRecommendedScenarioSimpleInfoLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *ListRecommendedScenarioSimpleInfoResp) Reset()         { *m = ListRecommendedScenarioSimpleInfoResp{} }
func (m *ListRecommendedScenarioSimpleInfoResp) String() string { return proto.CompactTextString(m) }
func (*ListRecommendedScenarioSimpleInfoResp) ProtoMessage()    {}
func (*ListRecommendedScenarioSimpleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{25}
}
func (m *ListRecommendedScenarioSimpleInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecommendedScenarioSimpleInfoResp.Unmarshal(m, b)
}
func (m *ListRecommendedScenarioSimpleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecommendedScenarioSimpleInfoResp.Marshal(b, m, deterministic)
}
func (dst *ListRecommendedScenarioSimpleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecommendedScenarioSimpleInfoResp.Merge(dst, src)
}
func (m *ListRecommendedScenarioSimpleInfoResp) XXX_Size() int {
	return xxx_messageInfo_ListRecommendedScenarioSimpleInfoResp.Size(m)
}
func (m *ListRecommendedScenarioSimpleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecommendedScenarioSimpleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecommendedScenarioSimpleInfoResp proto.InternalMessageInfo

func (m *ListRecommendedScenarioSimpleInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListRecommendedScenarioSimpleInfoResp) GetInfos() []*ScenarioSimpleInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *ListRecommendedScenarioSimpleInfoResp) GetLoadMore() *ListRecommendedScenarioSimpleInfoLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type ListRecommendedScenarioSimpleInfoLoadMore struct {
	LastId               uint32                  `protobuf:"varint,1,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	Type                 RecommendedScenarioType `protobuf:"varint,2,opt,name=type,proto3,enum=ga.mystery_place_logic.RecommendedScenarioType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ListRecommendedScenarioSimpleInfoLoadMore) Reset() {
	*m = ListRecommendedScenarioSimpleInfoLoadMore{}
}
func (m *ListRecommendedScenarioSimpleInfoLoadMore) String() string {
	return proto.CompactTextString(m)
}
func (*ListRecommendedScenarioSimpleInfoLoadMore) ProtoMessage() {}
func (*ListRecommendedScenarioSimpleInfoLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{26}
}
func (m *ListRecommendedScenarioSimpleInfoLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecommendedScenarioSimpleInfoLoadMore.Unmarshal(m, b)
}
func (m *ListRecommendedScenarioSimpleInfoLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecommendedScenarioSimpleInfoLoadMore.Marshal(b, m, deterministic)
}
func (dst *ListRecommendedScenarioSimpleInfoLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecommendedScenarioSimpleInfoLoadMore.Merge(dst, src)
}
func (m *ListRecommendedScenarioSimpleInfoLoadMore) XXX_Size() int {
	return xxx_messageInfo_ListRecommendedScenarioSimpleInfoLoadMore.Size(m)
}
func (m *ListRecommendedScenarioSimpleInfoLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecommendedScenarioSimpleInfoLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecommendedScenarioSimpleInfoLoadMore proto.InternalMessageInfo

func (m *ListRecommendedScenarioSimpleInfoLoadMore) GetLastId() uint32 {
	if m != nil {
		return m.LastId
	}
	return 0
}

func (m *ListRecommendedScenarioSimpleInfoLoadMore) GetType() RecommendedScenarioType {
	if m != nil {
		return m.Type
	}
	return RecommendedScenarioType_RecommendedScenarioType_UNDEFINED
}

// 获取推荐剧本列表详情信息接口
type ListRecommendedScenarioDetailInfoLoadMore struct {
	LastId               uint32                  `protobuf:"varint,1,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	Type                 RecommendedScenarioType `protobuf:"varint,2,opt,name=type,proto3,enum=ga.mystery_place_logic.RecommendedScenarioType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ListRecommendedScenarioDetailInfoLoadMore) Reset() {
	*m = ListRecommendedScenarioDetailInfoLoadMore{}
}
func (m *ListRecommendedScenarioDetailInfoLoadMore) String() string {
	return proto.CompactTextString(m)
}
func (*ListRecommendedScenarioDetailInfoLoadMore) ProtoMessage() {}
func (*ListRecommendedScenarioDetailInfoLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{27}
}
func (m *ListRecommendedScenarioDetailInfoLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecommendedScenarioDetailInfoLoadMore.Unmarshal(m, b)
}
func (m *ListRecommendedScenarioDetailInfoLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecommendedScenarioDetailInfoLoadMore.Marshal(b, m, deterministic)
}
func (dst *ListRecommendedScenarioDetailInfoLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecommendedScenarioDetailInfoLoadMore.Merge(dst, src)
}
func (m *ListRecommendedScenarioDetailInfoLoadMore) XXX_Size() int {
	return xxx_messageInfo_ListRecommendedScenarioDetailInfoLoadMore.Size(m)
}
func (m *ListRecommendedScenarioDetailInfoLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecommendedScenarioDetailInfoLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecommendedScenarioDetailInfoLoadMore proto.InternalMessageInfo

func (m *ListRecommendedScenarioDetailInfoLoadMore) GetLastId() uint32 {
	if m != nil {
		return m.LastId
	}
	return 0
}

func (m *ListRecommendedScenarioDetailInfoLoadMore) GetType() RecommendedScenarioType {
	if m != nil {
		return m.Type
	}
	return RecommendedScenarioType_RecommendedScenarioType_UNDEFINED
}

// 获取推荐剧本列表详情信息接口
type ListRecommendedScenarioDetailInfoReq struct {
	BaseReq              *app.BaseReq                               `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Type                 RecommendedScenarioType                    `protobuf:"varint,2,opt,name=type,proto3,enum=ga.mystery_place_logic.RecommendedScenarioType" json:"type,omitempty"`
	Count                uint32                                     `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *ListRecommendedScenarioDetailInfoLoadMore `protobuf:"bytes,4,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *ListRecommendedScenarioDetailInfoReq) Reset()         { *m = ListRecommendedScenarioDetailInfoReq{} }
func (m *ListRecommendedScenarioDetailInfoReq) String() string { return proto.CompactTextString(m) }
func (*ListRecommendedScenarioDetailInfoReq) ProtoMessage()    {}
func (*ListRecommendedScenarioDetailInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{28}
}
func (m *ListRecommendedScenarioDetailInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecommendedScenarioDetailInfoReq.Unmarshal(m, b)
}
func (m *ListRecommendedScenarioDetailInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecommendedScenarioDetailInfoReq.Marshal(b, m, deterministic)
}
func (dst *ListRecommendedScenarioDetailInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecommendedScenarioDetailInfoReq.Merge(dst, src)
}
func (m *ListRecommendedScenarioDetailInfoReq) XXX_Size() int {
	return xxx_messageInfo_ListRecommendedScenarioDetailInfoReq.Size(m)
}
func (m *ListRecommendedScenarioDetailInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecommendedScenarioDetailInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecommendedScenarioDetailInfoReq proto.InternalMessageInfo

func (m *ListRecommendedScenarioDetailInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ListRecommendedScenarioDetailInfoReq) GetType() RecommendedScenarioType {
	if m != nil {
		return m.Type
	}
	return RecommendedScenarioType_RecommendedScenarioType_UNDEFINED
}

func (m *ListRecommendedScenarioDetailInfoReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ListRecommendedScenarioDetailInfoReq) GetLoadMore() *ListRecommendedScenarioDetailInfoLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type ListRecommendedScenarioDetailInfoResp struct {
	BaseResp             *app.BaseResp                              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Infos                []*RecommendedScenarioDetailInfo           `protobuf:"bytes,2,rep,name=infos,proto3" json:"infos,omitempty"`
	LoadMore             *ListRecommendedScenarioDetailInfoLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *ListRecommendedScenarioDetailInfoResp) Reset()         { *m = ListRecommendedScenarioDetailInfoResp{} }
func (m *ListRecommendedScenarioDetailInfoResp) String() string { return proto.CompactTextString(m) }
func (*ListRecommendedScenarioDetailInfoResp) ProtoMessage()    {}
func (*ListRecommendedScenarioDetailInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{29}
}
func (m *ListRecommendedScenarioDetailInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListRecommendedScenarioDetailInfoResp.Unmarshal(m, b)
}
func (m *ListRecommendedScenarioDetailInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListRecommendedScenarioDetailInfoResp.Marshal(b, m, deterministic)
}
func (dst *ListRecommendedScenarioDetailInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListRecommendedScenarioDetailInfoResp.Merge(dst, src)
}
func (m *ListRecommendedScenarioDetailInfoResp) XXX_Size() int {
	return xxx_messageInfo_ListRecommendedScenarioDetailInfoResp.Size(m)
}
func (m *ListRecommendedScenarioDetailInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListRecommendedScenarioDetailInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListRecommendedScenarioDetailInfoResp proto.InternalMessageInfo

func (m *ListRecommendedScenarioDetailInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ListRecommendedScenarioDetailInfoResp) GetInfos() []*RecommendedScenarioDetailInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *ListRecommendedScenarioDetailInfoResp) GetLoadMore() *ListRecommendedScenarioDetailInfoLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

// 获取推荐剧本列表缩略信息接口
type GetRecommendedScenarioDetailInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ScenarioId           []uint32     `protobuf:"varint,2,rep,packed,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRecommendedScenarioDetailInfoReq) Reset()         { *m = GetRecommendedScenarioDetailInfoReq{} }
func (m *GetRecommendedScenarioDetailInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendedScenarioDetailInfoReq) ProtoMessage()    {}
func (*GetRecommendedScenarioDetailInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{30}
}
func (m *GetRecommendedScenarioDetailInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendedScenarioDetailInfoReq.Unmarshal(m, b)
}
func (m *GetRecommendedScenarioDetailInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendedScenarioDetailInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendedScenarioDetailInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendedScenarioDetailInfoReq.Merge(dst, src)
}
func (m *GetRecommendedScenarioDetailInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendedScenarioDetailInfoReq.Size(m)
}
func (m *GetRecommendedScenarioDetailInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendedScenarioDetailInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendedScenarioDetailInfoReq proto.InternalMessageInfo

func (m *GetRecommendedScenarioDetailInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRecommendedScenarioDetailInfoReq) GetScenarioId() []uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return nil
}

type GetRecommendedScenarioDetailInfoResp struct {
	BaseResp             *app.BaseResp                    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Infos                []*RecommendedScenarioDetailInfo `protobuf:"bytes,2,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *GetRecommendedScenarioDetailInfoResp) Reset()         { *m = GetRecommendedScenarioDetailInfoResp{} }
func (m *GetRecommendedScenarioDetailInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendedScenarioDetailInfoResp) ProtoMessage()    {}
func (*GetRecommendedScenarioDetailInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{31}
}
func (m *GetRecommendedScenarioDetailInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendedScenarioDetailInfoResp.Unmarshal(m, b)
}
func (m *GetRecommendedScenarioDetailInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendedScenarioDetailInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendedScenarioDetailInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendedScenarioDetailInfoResp.Merge(dst, src)
}
func (m *GetRecommendedScenarioDetailInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendedScenarioDetailInfoResp.Size(m)
}
func (m *GetRecommendedScenarioDetailInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendedScenarioDetailInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendedScenarioDetailInfoResp proto.InternalMessageInfo

func (m *GetRecommendedScenarioDetailInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecommendedScenarioDetailInfoResp) GetInfos() []*RecommendedScenarioDetailInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type GetRoomShareLinkByTabIdReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRoomShareLinkByTabIdReq) Reset()         { *m = GetRoomShareLinkByTabIdReq{} }
func (m *GetRoomShareLinkByTabIdReq) String() string { return proto.CompactTextString(m) }
func (*GetRoomShareLinkByTabIdReq) ProtoMessage()    {}
func (*GetRoomShareLinkByTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{32}
}
func (m *GetRoomShareLinkByTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomShareLinkByTabIdReq.Unmarshal(m, b)
}
func (m *GetRoomShareLinkByTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomShareLinkByTabIdReq.Marshal(b, m, deterministic)
}
func (dst *GetRoomShareLinkByTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomShareLinkByTabIdReq.Merge(dst, src)
}
func (m *GetRoomShareLinkByTabIdReq) XXX_Size() int {
	return xxx_messageInfo_GetRoomShareLinkByTabIdReq.Size(m)
}
func (m *GetRoomShareLinkByTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomShareLinkByTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomShareLinkByTabIdReq proto.InternalMessageInfo

func (m *GetRoomShareLinkByTabIdReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRoomShareLinkByTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetRoomShareLinkByTabIdResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Link                 string        `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRoomShareLinkByTabIdResp) Reset()         { *m = GetRoomShareLinkByTabIdResp{} }
func (m *GetRoomShareLinkByTabIdResp) String() string { return proto.CompactTextString(m) }
func (*GetRoomShareLinkByTabIdResp) ProtoMessage()    {}
func (*GetRoomShareLinkByTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{33}
}
func (m *GetRoomShareLinkByTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRoomShareLinkByTabIdResp.Unmarshal(m, b)
}
func (m *GetRoomShareLinkByTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRoomShareLinkByTabIdResp.Marshal(b, m, deterministic)
}
func (dst *GetRoomShareLinkByTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRoomShareLinkByTabIdResp.Merge(dst, src)
}
func (m *GetRoomShareLinkByTabIdResp) XXX_Size() int {
	return xxx_messageInfo_GetRoomShareLinkByTabIdResp.Size(m)
}
func (m *GetRoomShareLinkByTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRoomShareLinkByTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRoomShareLinkByTabIdResp proto.InternalMessageInfo

func (m *GetRoomShareLinkByTabIdResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRoomShareLinkByTabIdResp) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

type MysteryPlaceChannelListReq struct {
	BaseReq              *app.BaseReq                       `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabIds               []uint32                           `protobuf:"varint,2,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	CategoryIds          []uint32                           `protobuf:"varint,3,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	Sex                  MysteryPlaceChannelListReq_Sex     `protobuf:"varint,5,opt,name=sex,proto3,enum=ga.mystery_place_logic.MysteryPlaceChannelListReq_Sex" json:"sex,omitempty"`
	GetMode              uint32                             `protobuf:"varint,6,opt,name=get_mode,json=getMode,proto3" json:"get_mode,omitempty"`
	ChannelPackageId     string                             `protobuf:"bytes,7,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	NoBrowseList         []uint32                           `protobuf:"varint,8,rep,packed,name=no_browse_list,json=noBrowseList,proto3" json:"no_browse_list,omitempty"`
	EnterSource          uint32                             `protobuf:"varint,9,opt,name=enter_source,json=enterSource,proto3" json:"enter_source,omitempty"`
	MysteryPlaceOption   []*MysteryPlaceBusinessBlockOption `protobuf:"bytes,10,rep,name=mystery_place_option,json=mysteryPlaceOption,proto3" json:"mystery_place_option,omitempty"`
	Count                uint32                             `protobuf:"varint,11,opt,name=count,proto3" json:"count,omitempty"`
	ScenarioIds          []uint32                           `protobuf:"varint,12,rep,packed,name=scenario_ids,json=scenarioIds,proto3" json:"scenario_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *MysteryPlaceChannelListReq) Reset()         { *m = MysteryPlaceChannelListReq{} }
func (m *MysteryPlaceChannelListReq) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceChannelListReq) ProtoMessage()    {}
func (*MysteryPlaceChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{34}
}
func (m *MysteryPlaceChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceChannelListReq.Unmarshal(m, b)
}
func (m *MysteryPlaceChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceChannelListReq.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceChannelListReq.Merge(dst, src)
}
func (m *MysteryPlaceChannelListReq) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceChannelListReq.Size(m)
}
func (m *MysteryPlaceChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceChannelListReq proto.InternalMessageInfo

func (m *MysteryPlaceChannelListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MysteryPlaceChannelListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *MysteryPlaceChannelListReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *MysteryPlaceChannelListReq) GetSex() MysteryPlaceChannelListReq_Sex {
	if m != nil {
		return m.Sex
	}
	return MysteryPlaceChannelListReq_UNDEFINED
}

func (m *MysteryPlaceChannelListReq) GetGetMode() uint32 {
	if m != nil {
		return m.GetMode
	}
	return 0
}

func (m *MysteryPlaceChannelListReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

func (m *MysteryPlaceChannelListReq) GetNoBrowseList() []uint32 {
	if m != nil {
		return m.NoBrowseList
	}
	return nil
}

func (m *MysteryPlaceChannelListReq) GetEnterSource() uint32 {
	if m != nil {
		return m.EnterSource
	}
	return 0
}

func (m *MysteryPlaceChannelListReq) GetMysteryPlaceOption() []*MysteryPlaceBusinessBlockOption {
	if m != nil {
		return m.MysteryPlaceOption
	}
	return nil
}

func (m *MysteryPlaceChannelListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *MysteryPlaceChannelListReq) GetScenarioIds() []uint32 {
	if m != nil {
		return m.ScenarioIds
	}
	return nil
}

type MysteryPlaceBusinessBlockOption struct {
	// 筛选表头
	BusinessBlockId uint32 `protobuf:"varint,1,opt,name=business_block_id,json=businessBlockId,proto3" json:"business_block_id,omitempty"`
	// 筛选项
	BusinessElemId       uint32   `protobuf:"varint,2,opt,name=business_elem_id,json=businessElemId,proto3" json:"business_elem_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryPlaceBusinessBlockOption) Reset()         { *m = MysteryPlaceBusinessBlockOption{} }
func (m *MysteryPlaceBusinessBlockOption) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceBusinessBlockOption) ProtoMessage()    {}
func (*MysteryPlaceBusinessBlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{35}
}
func (m *MysteryPlaceBusinessBlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceBusinessBlockOption.Unmarshal(m, b)
}
func (m *MysteryPlaceBusinessBlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceBusinessBlockOption.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceBusinessBlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceBusinessBlockOption.Merge(dst, src)
}
func (m *MysteryPlaceBusinessBlockOption) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceBusinessBlockOption.Size(m)
}
func (m *MysteryPlaceBusinessBlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceBusinessBlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceBusinessBlockOption proto.InternalMessageInfo

func (m *MysteryPlaceBusinessBlockOption) GetBusinessBlockId() uint32 {
	if m != nil {
		return m.BusinessBlockId
	}
	return 0
}

func (m *MysteryPlaceBusinessBlockOption) GetBusinessElemId() uint32 {
	if m != nil {
		return m.BusinessElemId
	}
	return 0
}

type MysteryPlaceChannelListResp struct {
	BaseResp             *app.BaseResp                           `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Items                []*MysteryPlaceChannelItem              `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	LoadFinish           bool                                    `protobuf:"varint,3,opt,name=load_finish,json=loadFinish,proto3" json:"load_finish,omitempty"`
	ReportData           *MysteryPlaceChannelListResp_DataReport `protobuf:"bytes,4,opt,name=report_data,json=reportData,proto3" json:"report_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *MysteryPlaceChannelListResp) Reset()         { *m = MysteryPlaceChannelListResp{} }
func (m *MysteryPlaceChannelListResp) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceChannelListResp) ProtoMessage()    {}
func (*MysteryPlaceChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{36}
}
func (m *MysteryPlaceChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceChannelListResp.Unmarshal(m, b)
}
func (m *MysteryPlaceChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceChannelListResp.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceChannelListResp.Merge(dst, src)
}
func (m *MysteryPlaceChannelListResp) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceChannelListResp.Size(m)
}
func (m *MysteryPlaceChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceChannelListResp proto.InternalMessageInfo

func (m *MysteryPlaceChannelListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MysteryPlaceChannelListResp) GetItems() []*MysteryPlaceChannelItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *MysteryPlaceChannelListResp) GetLoadFinish() bool {
	if m != nil {
		return m.LoadFinish
	}
	return false
}

func (m *MysteryPlaceChannelListResp) GetReportData() *MysteryPlaceChannelListResp_DataReport {
	if m != nil {
		return m.ReportData
	}
	return nil
}

// 仅用于客户端数据上报字段
type MysteryPlaceChannelListResp_DataReport struct {
	Footprint            string   `protobuf:"bytes,1,opt,name=footprint,proto3" json:"footprint,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryPlaceChannelListResp_DataReport) Reset() {
	*m = MysteryPlaceChannelListResp_DataReport{}
}
func (m *MysteryPlaceChannelListResp_DataReport) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceChannelListResp_DataReport) ProtoMessage()    {}
func (*MysteryPlaceChannelListResp_DataReport) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{36, 0}
}
func (m *MysteryPlaceChannelListResp_DataReport) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceChannelListResp_DataReport.Unmarshal(m, b)
}
func (m *MysteryPlaceChannelListResp_DataReport) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceChannelListResp_DataReport.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceChannelListResp_DataReport) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceChannelListResp_DataReport.Merge(dst, src)
}
func (m *MysteryPlaceChannelListResp_DataReport) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceChannelListResp_DataReport.Size(m)
}
func (m *MysteryPlaceChannelListResp_DataReport) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceChannelListResp_DataReport.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceChannelListResp_DataReport proto.InternalMessageInfo

func (m *MysteryPlaceChannelListResp_DataReport) GetFootprint() string {
	if m != nil {
		return m.Footprint
	}
	return ""
}

type MysteryPlaceChannelItem struct {
	ChannelId            uint32                                           `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName          string                                           `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	Owner                *ChannelItemUserInfo                             `protobuf:"bytes,3,opt,name=owner,proto3" json:"owner,omitempty"`
	View                 *MysteryPlaceChannelView                         `protobuf:"bytes,4,opt,name=view,proto3" json:"view,omitempty"`
	TabId                uint32                                           `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string                                           `protobuf:"bytes,6,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	MemberCount          uint32                                           `protobuf:"varint,7,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	RcmdLabel            MysteryPlaceRCMDLabelEnum                        `protobuf:"varint,8,opt,name=rcmd_label,json=rcmdLabel,proto3,enum=ga.mystery_place_logic.MysteryPlaceRCMDLabelEnum" json:"rcmd_label,omitempty"`
	GeoInfo              string                                           `protobuf:"bytes,9,opt,name=geo_info,json=geoInfo,proto3" json:"geo_info,omitempty"`
	LevelId              uint32                                           `protobuf:"varint,10,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	TraceInfo            *MysteryPlaceChannelItem_RecommendationTraceInfo `protobuf:"bytes,11,opt,name=trace_info,json=traceInfo,proto3" json:"trace_info,omitempty"`
	OwnerMismatchVersion bool                                             `protobuf:"varint,12,opt,name=owner_mismatch_version,json=ownerMismatchVersion,proto3" json:"owner_mismatch_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                         `json:"-"`
	XXX_unrecognized     []byte                                           `json:"-"`
	XXX_sizecache        int32                                            `json:"-"`
}

func (m *MysteryPlaceChannelItem) Reset()         { *m = MysteryPlaceChannelItem{} }
func (m *MysteryPlaceChannelItem) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceChannelItem) ProtoMessage()    {}
func (*MysteryPlaceChannelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{37}
}
func (m *MysteryPlaceChannelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceChannelItem.Unmarshal(m, b)
}
func (m *MysteryPlaceChannelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceChannelItem.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceChannelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceChannelItem.Merge(dst, src)
}
func (m *MysteryPlaceChannelItem) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceChannelItem.Size(m)
}
func (m *MysteryPlaceChannelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceChannelItem.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceChannelItem proto.InternalMessageInfo

func (m *MysteryPlaceChannelItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MysteryPlaceChannelItem) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *MysteryPlaceChannelItem) GetOwner() *ChannelItemUserInfo {
	if m != nil {
		return m.Owner
	}
	return nil
}

func (m *MysteryPlaceChannelItem) GetView() *MysteryPlaceChannelView {
	if m != nil {
		return m.View
	}
	return nil
}

func (m *MysteryPlaceChannelItem) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MysteryPlaceChannelItem) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *MysteryPlaceChannelItem) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *MysteryPlaceChannelItem) GetRcmdLabel() MysteryPlaceRCMDLabelEnum {
	if m != nil {
		return m.RcmdLabel
	}
	return MysteryPlaceRCMDLabelEnum_RCMDLabel_None
}

func (m *MysteryPlaceChannelItem) GetGeoInfo() string {
	if m != nil {
		return m.GeoInfo
	}
	return ""
}

func (m *MysteryPlaceChannelItem) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *MysteryPlaceChannelItem) GetTraceInfo() *MysteryPlaceChannelItem_RecommendationTraceInfo {
	if m != nil {
		return m.TraceInfo
	}
	return nil
}

func (m *MysteryPlaceChannelItem) GetOwnerMismatchVersion() bool {
	if m != nil {
		return m.OwnerMismatchVersion
	}
	return false
}

// 用于推荐系统与数据中心跟踪统计用户行为，没具体业务意义
type MysteryPlaceChannelItem_RecommendationTraceInfo struct {
	RecallFlag           uint32   `protobuf:"varint,1,opt,name=recall_flag,json=recallFlag,proto3" json:"recall_flag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryPlaceChannelItem_RecommendationTraceInfo) Reset() {
	*m = MysteryPlaceChannelItem_RecommendationTraceInfo{}
}
func (m *MysteryPlaceChannelItem_RecommendationTraceInfo) String() string {
	return proto.CompactTextString(m)
}
func (*MysteryPlaceChannelItem_RecommendationTraceInfo) ProtoMessage() {}
func (*MysteryPlaceChannelItem_RecommendationTraceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{37, 0}
}
func (m *MysteryPlaceChannelItem_RecommendationTraceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceChannelItem_RecommendationTraceInfo.Unmarshal(m, b)
}
func (m *MysteryPlaceChannelItem_RecommendationTraceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceChannelItem_RecommendationTraceInfo.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceChannelItem_RecommendationTraceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceChannelItem_RecommendationTraceInfo.Merge(dst, src)
}
func (m *MysteryPlaceChannelItem_RecommendationTraceInfo) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceChannelItem_RecommendationTraceInfo.Size(m)
}
func (m *MysteryPlaceChannelItem_RecommendationTraceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceChannelItem_RecommendationTraceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceChannelItem_RecommendationTraceInfo proto.InternalMessageInfo

func (m *MysteryPlaceChannelItem_RecommendationTraceInfo) GetRecallFlag() uint32 {
	if m != nil {
		return m.RecallFlag
	}
	return 0
}

type MysteryPlaceChannelView struct {
	// Types that are valid to be assigned to ChannelView:
	//	*MysteryPlaceChannelView_MysteryViewDefault
	//	*MysteryPlaceChannelView_MysteryViewMusic
	//	*MysteryPlaceChannelView_MysteryViewSimple
	ChannelView          isMysteryPlaceChannelView_ChannelView `protobuf_oneof:"channel_view"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *MysteryPlaceChannelView) Reset()         { *m = MysteryPlaceChannelView{} }
func (m *MysteryPlaceChannelView) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceChannelView) ProtoMessage()    {}
func (*MysteryPlaceChannelView) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{38}
}
func (m *MysteryPlaceChannelView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceChannelView.Unmarshal(m, b)
}
func (m *MysteryPlaceChannelView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceChannelView.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceChannelView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceChannelView.Merge(dst, src)
}
func (m *MysteryPlaceChannelView) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceChannelView.Size(m)
}
func (m *MysteryPlaceChannelView) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceChannelView.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceChannelView proto.InternalMessageInfo

type isMysteryPlaceChannelView_ChannelView interface {
	isMysteryPlaceChannelView_ChannelView()
}

type MysteryPlaceChannelView_MysteryViewDefault struct {
	MysteryViewDefault *MysteryViewDefault `protobuf:"bytes,1,opt,name=mystery_view_default,json=mysteryViewDefault,proto3,oneof"`
}

type MysteryPlaceChannelView_MysteryViewMusic struct {
	MysteryViewMusic *MysteryMusicChannelView `protobuf:"bytes,2,opt,name=mystery_view_music,json=mysteryViewMusic,proto3,oneof"`
}

type MysteryPlaceChannelView_MysteryViewSimple struct {
	MysteryViewSimple *MysterySimpleView `protobuf:"bytes,3,opt,name=mystery_view_simple,json=mysteryViewSimple,proto3,oneof"`
}

func (*MysteryPlaceChannelView_MysteryViewDefault) isMysteryPlaceChannelView_ChannelView() {}

func (*MysteryPlaceChannelView_MysteryViewMusic) isMysteryPlaceChannelView_ChannelView() {}

func (*MysteryPlaceChannelView_MysteryViewSimple) isMysteryPlaceChannelView_ChannelView() {}

func (m *MysteryPlaceChannelView) GetChannelView() isMysteryPlaceChannelView_ChannelView {
	if m != nil {
		return m.ChannelView
	}
	return nil
}

func (m *MysteryPlaceChannelView) GetMysteryViewDefault() *MysteryViewDefault {
	if x, ok := m.GetChannelView().(*MysteryPlaceChannelView_MysteryViewDefault); ok {
		return x.MysteryViewDefault
	}
	return nil
}

func (m *MysteryPlaceChannelView) GetMysteryViewMusic() *MysteryMusicChannelView {
	if x, ok := m.GetChannelView().(*MysteryPlaceChannelView_MysteryViewMusic); ok {
		return x.MysteryViewMusic
	}
	return nil
}

func (m *MysteryPlaceChannelView) GetMysteryViewSimple() *MysterySimpleView {
	if x, ok := m.GetChannelView().(*MysteryPlaceChannelView_MysteryViewSimple); ok {
		return x.MysteryViewSimple
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*MysteryPlaceChannelView) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _MysteryPlaceChannelView_OneofMarshaler, _MysteryPlaceChannelView_OneofUnmarshaler, _MysteryPlaceChannelView_OneofSizer, []interface{}{
		(*MysteryPlaceChannelView_MysteryViewDefault)(nil),
		(*MysteryPlaceChannelView_MysteryViewMusic)(nil),
		(*MysteryPlaceChannelView_MysteryViewSimple)(nil),
	}
}

func _MysteryPlaceChannelView_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*MysteryPlaceChannelView)
	// channel_view
	switch x := m.ChannelView.(type) {
	case *MysteryPlaceChannelView_MysteryViewDefault:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.MysteryViewDefault); err != nil {
			return err
		}
	case *MysteryPlaceChannelView_MysteryViewMusic:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.MysteryViewMusic); err != nil {
			return err
		}
	case *MysteryPlaceChannelView_MysteryViewSimple:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.MysteryViewSimple); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("MysteryPlaceChannelView.ChannelView has unexpected type %T", x)
	}
	return nil
}

func _MysteryPlaceChannelView_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*MysteryPlaceChannelView)
	switch tag {
	case 1: // channel_view.mystery_view_default
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MysteryViewDefault)
		err := b.DecodeMessage(msg)
		m.ChannelView = &MysteryPlaceChannelView_MysteryViewDefault{msg}
		return true, err
	case 2: // channel_view.mystery_view_music
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MysteryMusicChannelView)
		err := b.DecodeMessage(msg)
		m.ChannelView = &MysteryPlaceChannelView_MysteryViewMusic{msg}
		return true, err
	case 3: // channel_view.mystery_view_simple
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(MysterySimpleView)
		err := b.DecodeMessage(msg)
		m.ChannelView = &MysteryPlaceChannelView_MysteryViewSimple{msg}
		return true, err
	default:
		return false, nil
	}
}

func _MysteryPlaceChannelView_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*MysteryPlaceChannelView)
	// channel_view
	switch x := m.ChannelView.(type) {
	case *MysteryPlaceChannelView_MysteryViewDefault:
		s := proto.Size(x.MysteryViewDefault)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MysteryPlaceChannelView_MysteryViewMusic:
		s := proto.Size(x.MysteryViewMusic)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *MysteryPlaceChannelView_MysteryViewSimple:
		s := proto.Size(x.MysteryViewSimple)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 获取评论页参数
type GetCommentPageParamsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	HaveCompleted        bool         `protobuf:"varint,3,opt,name=have_completed,json=haveCompleted,proto3" json:"have_completed,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCommentPageParamsReq) Reset()         { *m = GetCommentPageParamsReq{} }
func (m *GetCommentPageParamsReq) String() string { return proto.CompactTextString(m) }
func (*GetCommentPageParamsReq) ProtoMessage()    {}
func (*GetCommentPageParamsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{39}
}
func (m *GetCommentPageParamsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentPageParamsReq.Unmarshal(m, b)
}
func (m *GetCommentPageParamsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentPageParamsReq.Marshal(b, m, deterministic)
}
func (dst *GetCommentPageParamsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentPageParamsReq.Merge(dst, src)
}
func (m *GetCommentPageParamsReq) XXX_Size() int {
	return xxx_messageInfo_GetCommentPageParamsReq.Size(m)
}
func (m *GetCommentPageParamsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentPageParamsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentPageParamsReq proto.InternalMessageInfo

func (m *GetCommentPageParamsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCommentPageParamsReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetCommentPageParamsReq) GetHaveCompleted() bool {
	if m != nil {
		return m.HaveCompleted
	}
	return false
}

type GetCommentPageParamsResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	AlreadyComment       bool           `protobuf:"varint,2,opt,name=already_comment,json=alreadyComment,proto3" json:"already_comment,omitempty"`
	GoodPlaymateTags     []*PlaymateTag `protobuf:"bytes,3,rep,name=good_playmate_tags,json=goodPlaymateTags,proto3" json:"good_playmate_tags,omitempty"`
	BadPlaymateTags      []*PlaymateTag `protobuf:"bytes,4,rep,name=bad_playmate_tags,json=badPlaymateTags,proto3" json:"bad_playmate_tags,omitempty"`
	Info                 *ScenarioInfo  `protobuf:"bytes,5,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetCommentPageParamsResp) Reset()         { *m = GetCommentPageParamsResp{} }
func (m *GetCommentPageParamsResp) String() string { return proto.CompactTextString(m) }
func (*GetCommentPageParamsResp) ProtoMessage()    {}
func (*GetCommentPageParamsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{40}
}
func (m *GetCommentPageParamsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommentPageParamsResp.Unmarshal(m, b)
}
func (m *GetCommentPageParamsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommentPageParamsResp.Marshal(b, m, deterministic)
}
func (dst *GetCommentPageParamsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommentPageParamsResp.Merge(dst, src)
}
func (m *GetCommentPageParamsResp) XXX_Size() int {
	return xxx_messageInfo_GetCommentPageParamsResp.Size(m)
}
func (m *GetCommentPageParamsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommentPageParamsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommentPageParamsResp proto.InternalMessageInfo

func (m *GetCommentPageParamsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCommentPageParamsResp) GetAlreadyComment() bool {
	if m != nil {
		return m.AlreadyComment
	}
	return false
}

func (m *GetCommentPageParamsResp) GetGoodPlaymateTags() []*PlaymateTag {
	if m != nil {
		return m.GoodPlaymateTags
	}
	return nil
}

func (m *GetCommentPageParamsResp) GetBadPlaymateTags() []*PlaymateTag {
	if m != nil {
		return m.BadPlaymateTags
	}
	return nil
}

func (m *GetCommentPageParamsResp) GetInfo() *ScenarioInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type PlaymateTag struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateTag) Reset()         { *m = PlaymateTag{} }
func (m *PlaymateTag) String() string { return proto.CompactTextString(m) }
func (*PlaymateTag) ProtoMessage()    {}
func (*PlaymateTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{41}
}
func (m *PlaymateTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateTag.Unmarshal(m, b)
}
func (m *PlaymateTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateTag.Marshal(b, m, deterministic)
}
func (dst *PlaymateTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateTag.Merge(dst, src)
}
func (m *PlaymateTag) XXX_Size() int {
	return xxx_messageInfo_PlaymateTag.Size(m)
}
func (m *PlaymateTag) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateTag.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateTag proto.InternalMessageInfo

func (m *PlaymateTag) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *PlaymateTag) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type ScenarioComment struct {
	Score                uint32   `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	HaveCompleted        bool     `protobuf:"varint,3,opt,name=have_completed,json=haveCompleted,proto3" json:"have_completed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScenarioComment) Reset()         { *m = ScenarioComment{} }
func (m *ScenarioComment) String() string { return proto.CompactTextString(m) }
func (*ScenarioComment) ProtoMessage()    {}
func (*ScenarioComment) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{42}
}
func (m *ScenarioComment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioComment.Unmarshal(m, b)
}
func (m *ScenarioComment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioComment.Marshal(b, m, deterministic)
}
func (dst *ScenarioComment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioComment.Merge(dst, src)
}
func (m *ScenarioComment) XXX_Size() int {
	return xxx_messageInfo_ScenarioComment.Size(m)
}
func (m *ScenarioComment) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioComment.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioComment proto.InternalMessageInfo

func (m *ScenarioComment) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ScenarioComment) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ScenarioComment) GetHaveCompleted() bool {
	if m != nil {
		return m.HaveCompleted
	}
	return false
}

// 评论剧本/玩伴
type CommentToScenarioReq struct {
	BaseReq              *app.BaseReq     `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32           `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Comment              *ScenarioComment `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
	TargetUid            uint32           `protobuf:"varint,4,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Type                 PlaymateTagType  `protobuf:"varint,5,opt,name=type,proto3,enum=ga.mystery_place_logic.PlaymateTagType" json:"type,omitempty"`
	PlaymateTags         []*PlaymateTag   `protobuf:"bytes,6,rep,name=playmate_tags,json=playmateTags,proto3" json:"playmate_tags,omitempty"`
	ChannelId            uint32           `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SetId                string           `protobuf:"bytes,8,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	PostId               string           `protobuf:"bytes,9,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *CommentToScenarioReq) Reset()         { *m = CommentToScenarioReq{} }
func (m *CommentToScenarioReq) String() string { return proto.CompactTextString(m) }
func (*CommentToScenarioReq) ProtoMessage()    {}
func (*CommentToScenarioReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{43}
}
func (m *CommentToScenarioReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentToScenarioReq.Unmarshal(m, b)
}
func (m *CommentToScenarioReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentToScenarioReq.Marshal(b, m, deterministic)
}
func (dst *CommentToScenarioReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentToScenarioReq.Merge(dst, src)
}
func (m *CommentToScenarioReq) XXX_Size() int {
	return xxx_messageInfo_CommentToScenarioReq.Size(m)
}
func (m *CommentToScenarioReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentToScenarioReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommentToScenarioReq proto.InternalMessageInfo

func (m *CommentToScenarioReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CommentToScenarioReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CommentToScenarioReq) GetComment() *ScenarioComment {
	if m != nil {
		return m.Comment
	}
	return nil
}

func (m *CommentToScenarioReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *CommentToScenarioReq) GetType() PlaymateTagType {
	if m != nil {
		return m.Type
	}
	return PlaymateTagType_PlaymateTagType_UNDEFINED
}

func (m *CommentToScenarioReq) GetPlaymateTags() []*PlaymateTag {
	if m != nil {
		return m.PlaymateTags
	}
	return nil
}

func (m *CommentToScenarioReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CommentToScenarioReq) GetSetId() string {
	if m != nil {
		return m.SetId
	}
	return ""
}

func (m *CommentToScenarioReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type CommentToScenarioResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CommentToScenarioResp) Reset()         { *m = CommentToScenarioResp{} }
func (m *CommentToScenarioResp) String() string { return proto.CompactTextString(m) }
func (*CommentToScenarioResp) ProtoMessage()    {}
func (*CommentToScenarioResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{44}
}
func (m *CommentToScenarioResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommentToScenarioResp.Unmarshal(m, b)
}
func (m *CommentToScenarioResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommentToScenarioResp.Marshal(b, m, deterministic)
}
func (dst *CommentToScenarioResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommentToScenarioResp.Merge(dst, src)
}
func (m *CommentToScenarioResp) XXX_Size() int {
	return xxx_messageInfo_CommentToScenarioResp.Size(m)
}
func (m *CommentToScenarioResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommentToScenarioResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommentToScenarioResp proto.InternalMessageInfo

func (m *CommentToScenarioResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

// 获取推荐剧本
type GetRcmdScenarioReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRcmdScenarioReq) Reset()         { *m = GetRcmdScenarioReq{} }
func (m *GetRcmdScenarioReq) String() string { return proto.CompactTextString(m) }
func (*GetRcmdScenarioReq) ProtoMessage()    {}
func (*GetRcmdScenarioReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{45}
}
func (m *GetRcmdScenarioReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdScenarioReq.Unmarshal(m, b)
}
func (m *GetRcmdScenarioReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdScenarioReq.Marshal(b, m, deterministic)
}
func (dst *GetRcmdScenarioReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdScenarioReq.Merge(dst, src)
}
func (m *GetRcmdScenarioReq) XXX_Size() int {
	return xxx_messageInfo_GetRcmdScenarioReq.Size(m)
}
func (m *GetRcmdScenarioReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdScenarioReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdScenarioReq proto.InternalMessageInfo

func (m *GetRcmdScenarioReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetRcmdScenarioResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ScenarioInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRcmdScenarioResp) Reset()         { *m = GetRcmdScenarioResp{} }
func (m *GetRcmdScenarioResp) String() string { return proto.CompactTextString(m) }
func (*GetRcmdScenarioResp) ProtoMessage()    {}
func (*GetRcmdScenarioResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{46}
}
func (m *GetRcmdScenarioResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdScenarioResp.Unmarshal(m, b)
}
func (m *GetRcmdScenarioResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdScenarioResp.Marshal(b, m, deterministic)
}
func (dst *GetRcmdScenarioResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdScenarioResp.Merge(dst, src)
}
func (m *GetRcmdScenarioResp) XXX_Size() int {
	return xxx_messageInfo_GetRcmdScenarioResp.Size(m)
}
func (m *GetRcmdScenarioResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdScenarioResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdScenarioResp proto.InternalMessageInfo

func (m *GetRcmdScenarioResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRcmdScenarioResp) GetInfo() *ScenarioInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 邀请房主切换剧本
type RequestToSwitchScenarioReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32       `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RequestToSwitchScenarioReq) Reset()         { *m = RequestToSwitchScenarioReq{} }
func (m *RequestToSwitchScenarioReq) String() string { return proto.CompactTextString(m) }
func (*RequestToSwitchScenarioReq) ProtoMessage()    {}
func (*RequestToSwitchScenarioReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{47}
}
func (m *RequestToSwitchScenarioReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RequestToSwitchScenarioReq.Unmarshal(m, b)
}
func (m *RequestToSwitchScenarioReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RequestToSwitchScenarioReq.Marshal(b, m, deterministic)
}
func (dst *RequestToSwitchScenarioReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestToSwitchScenarioReq.Merge(dst, src)
}
func (m *RequestToSwitchScenarioReq) XXX_Size() int {
	return xxx_messageInfo_RequestToSwitchScenarioReq.Size(m)
}
func (m *RequestToSwitchScenarioReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestToSwitchScenarioReq.DiscardUnknown(m)
}

var xxx_messageInfo_RequestToSwitchScenarioReq proto.InternalMessageInfo

func (m *RequestToSwitchScenarioReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RequestToSwitchScenarioReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RequestToSwitchScenarioReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type RequestToSwitchScenarioResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RequestToSwitchScenarioResp) Reset()         { *m = RequestToSwitchScenarioResp{} }
func (m *RequestToSwitchScenarioResp) String() string { return proto.CompactTextString(m) }
func (*RequestToSwitchScenarioResp) ProtoMessage()    {}
func (*RequestToSwitchScenarioResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{48}
}
func (m *RequestToSwitchScenarioResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RequestToSwitchScenarioResp.Unmarshal(m, b)
}
func (m *RequestToSwitchScenarioResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RequestToSwitchScenarioResp.Marshal(b, m, deterministic)
}
func (dst *RequestToSwitchScenarioResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestToSwitchScenarioResp.Merge(dst, src)
}
func (m *RequestToSwitchScenarioResp) XXX_Size() int {
	return xxx_messageInfo_RequestToSwitchScenarioResp.Size(m)
}
func (m *RequestToSwitchScenarioResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestToSwitchScenarioResp.DiscardUnknown(m)
}

var xxx_messageInfo_RequestToSwitchScenarioResp proto.InternalMessageInfo

func (m *RequestToSwitchScenarioResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetNewScenarioTipReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetNewScenarioTipReq) Reset()         { *m = GetNewScenarioTipReq{} }
func (m *GetNewScenarioTipReq) String() string { return proto.CompactTextString(m) }
func (*GetNewScenarioTipReq) ProtoMessage()    {}
func (*GetNewScenarioTipReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{49}
}
func (m *GetNewScenarioTipReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewScenarioTipReq.Unmarshal(m, b)
}
func (m *GetNewScenarioTipReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewScenarioTipReq.Marshal(b, m, deterministic)
}
func (dst *GetNewScenarioTipReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewScenarioTipReq.Merge(dst, src)
}
func (m *GetNewScenarioTipReq) XXX_Size() int {
	return xxx_messageInfo_GetNewScenarioTipReq.Size(m)
}
func (m *GetNewScenarioTipReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewScenarioTipReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewScenarioTipReq proto.InternalMessageInfo

func (m *GetNewScenarioTipReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetNewScenarioTipResp struct {
	BaseResp             *app.BaseResp     `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Tips                 []*NewScenarioTip `protobuf:"bytes,2,rep,name=tips,proto3" json:"tips,omitempty"`
	SwitchInterval       uint32            `protobuf:"varint,3,opt,name=switch_interval,json=switchInterval,proto3" json:"switch_interval,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetNewScenarioTipResp) Reset()         { *m = GetNewScenarioTipResp{} }
func (m *GetNewScenarioTipResp) String() string { return proto.CompactTextString(m) }
func (*GetNewScenarioTipResp) ProtoMessage()    {}
func (*GetNewScenarioTipResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{50}
}
func (m *GetNewScenarioTipResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewScenarioTipResp.Unmarshal(m, b)
}
func (m *GetNewScenarioTipResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewScenarioTipResp.Marshal(b, m, deterministic)
}
func (dst *GetNewScenarioTipResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewScenarioTipResp.Merge(dst, src)
}
func (m *GetNewScenarioTipResp) XXX_Size() int {
	return xxx_messageInfo_GetNewScenarioTipResp.Size(m)
}
func (m *GetNewScenarioTipResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewScenarioTipResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewScenarioTipResp proto.InternalMessageInfo

func (m *GetNewScenarioTipResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetNewScenarioTipResp) GetTips() []*NewScenarioTip {
	if m != nil {
		return m.Tips
	}
	return nil
}

func (m *GetNewScenarioTipResp) GetSwitchInterval() uint32 {
	if m != nil {
		return m.SwitchInterval
	}
	return 0
}

type NewScenarioTip struct {
	TipId                string             `protobuf:"bytes,1,opt,name=tip_id,json=tipId,proto3" json:"tip_id,omitempty"`
	ScenarioId           uint32             `protobuf:"varint,2,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	TabId                uint32             `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TipType              NewScenarioTipType `protobuf:"varint,4,opt,name=tip_type,json=tipType,proto3,enum=ga.mystery_place_logic.NewScenarioTipType" json:"tip_type,omitempty"`
	Content              string             `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *NewScenarioTip) Reset()         { *m = NewScenarioTip{} }
func (m *NewScenarioTip) String() string { return proto.CompactTextString(m) }
func (*NewScenarioTip) ProtoMessage()    {}
func (*NewScenarioTip) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{51}
}
func (m *NewScenarioTip) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewScenarioTip.Unmarshal(m, b)
}
func (m *NewScenarioTip) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewScenarioTip.Marshal(b, m, deterministic)
}
func (dst *NewScenarioTip) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewScenarioTip.Merge(dst, src)
}
func (m *NewScenarioTip) XXX_Size() int {
	return xxx_messageInfo_NewScenarioTip.Size(m)
}
func (m *NewScenarioTip) XXX_DiscardUnknown() {
	xxx_messageInfo_NewScenarioTip.DiscardUnknown(m)
}

var xxx_messageInfo_NewScenarioTip proto.InternalMessageInfo

func (m *NewScenarioTip) GetTipId() string {
	if m != nil {
		return m.TipId
	}
	return ""
}

func (m *NewScenarioTip) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *NewScenarioTip) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *NewScenarioTip) GetTipType() NewScenarioTipType {
	if m != nil {
		return m.TipType
	}
	return NewScenarioTipType_NewScenarioTipType_UNDEFINED
}

func (m *NewScenarioTip) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type MarkNewScenarioTipReadReq struct {
	BaseReq              *app.BaseReq    `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Tip                  *NewScenarioTip `protobuf:"bytes,2,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MarkNewScenarioTipReadReq) Reset()         { *m = MarkNewScenarioTipReadReq{} }
func (m *MarkNewScenarioTipReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkNewScenarioTipReadReq) ProtoMessage()    {}
func (*MarkNewScenarioTipReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{52}
}
func (m *MarkNewScenarioTipReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkNewScenarioTipReadReq.Unmarshal(m, b)
}
func (m *MarkNewScenarioTipReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkNewScenarioTipReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkNewScenarioTipReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkNewScenarioTipReadReq.Merge(dst, src)
}
func (m *MarkNewScenarioTipReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkNewScenarioTipReadReq.Size(m)
}
func (m *MarkNewScenarioTipReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkNewScenarioTipReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkNewScenarioTipReadReq proto.InternalMessageInfo

func (m *MarkNewScenarioTipReadReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MarkNewScenarioTipReadReq) GetTip() *NewScenarioTip {
	if m != nil {
		return m.Tip
	}
	return nil
}

type MarkNewScenarioTipReadResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MarkNewScenarioTipReadResp) Reset()         { *m = MarkNewScenarioTipReadResp{} }
func (m *MarkNewScenarioTipReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkNewScenarioTipReadResp) ProtoMessage()    {}
func (*MarkNewScenarioTipReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{53}
}
func (m *MarkNewScenarioTipReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkNewScenarioTipReadResp.Unmarshal(m, b)
}
func (m *MarkNewScenarioTipReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkNewScenarioTipReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkNewScenarioTipReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkNewScenarioTipReadResp.Merge(dst, src)
}
func (m *MarkNewScenarioTipReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkNewScenarioTipReadResp.Size(m)
}
func (m *MarkNewScenarioTipReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkNewScenarioTipReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkNewScenarioTipReadResp proto.InternalMessageInfo

func (m *MarkNewScenarioTipReadResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type MysteryPlaceClientConfigReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	VersionName          string       `protobuf:"bytes,2,opt,name=version_name,json=versionName,proto3" json:"version_name,omitempty"`
	ChannelPackageId     string       `protobuf:"bytes,3,opt,name=channel_package_id,json=channelPackageId,proto3" json:"channel_package_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MysteryPlaceClientConfigReq) Reset()         { *m = MysteryPlaceClientConfigReq{} }
func (m *MysteryPlaceClientConfigReq) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceClientConfigReq) ProtoMessage()    {}
func (*MysteryPlaceClientConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{54}
}
func (m *MysteryPlaceClientConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceClientConfigReq.Unmarshal(m, b)
}
func (m *MysteryPlaceClientConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceClientConfigReq.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceClientConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceClientConfigReq.Merge(dst, src)
}
func (m *MysteryPlaceClientConfigReq) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceClientConfigReq.Size(m)
}
func (m *MysteryPlaceClientConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceClientConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceClientConfigReq proto.InternalMessageInfo

func (m *MysteryPlaceClientConfigReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *MysteryPlaceClientConfigReq) GetVersionName() string {
	if m != nil {
		return m.VersionName
	}
	return ""
}

func (m *MysteryPlaceClientConfigReq) GetChannelPackageId() string {
	if m != nil {
		return m.ChannelPackageId
	}
	return ""
}

type MysteryPlaceClientConfigResp struct {
	BaseResp              *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	MjHideNoviceGuide     bool          `protobuf:"varint,2,opt,name=mj_hide_novice_guide,json=mjHideNoviceGuide,proto3" json:"mj_hide_novice_guide,omitempty"`
	IsClose               bool          `protobuf:"varint,3,opt,name=is_close,json=isClose,proto3" json:"is_close,omitempty"`
	IsEffectiveRegulation bool          `protobuf:"varint,4,opt,name=is_effective_regulation,json=isEffectiveRegulation,proto3" json:"is_effective_regulation,omitempty"`
	IsHideScenarioRank    bool          `protobuf:"varint,5,opt,name=is_hide_scenario_rank,json=isHideScenarioRank,proto3" json:"is_hide_scenario_rank,omitempty"`
	MjControlAudit        bool          `protobuf:"varint,6,opt,name=mj_control_audit,json=mjControlAudit,proto3" json:"mj_control_audit,omitempty"`
	ScenarioRankUri       string        `protobuf:"bytes,7,opt,name=scenario_rank_uri,json=scenarioRankUri,proto3" json:"scenario_rank_uri,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}      `json:"-"`
	XXX_unrecognized      []byte        `json:"-"`
	XXX_sizecache         int32         `json:"-"`
}

func (m *MysteryPlaceClientConfigResp) Reset()         { *m = MysteryPlaceClientConfigResp{} }
func (m *MysteryPlaceClientConfigResp) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceClientConfigResp) ProtoMessage()    {}
func (*MysteryPlaceClientConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{55}
}
func (m *MysteryPlaceClientConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceClientConfigResp.Unmarshal(m, b)
}
func (m *MysteryPlaceClientConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceClientConfigResp.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceClientConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceClientConfigResp.Merge(dst, src)
}
func (m *MysteryPlaceClientConfigResp) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceClientConfigResp.Size(m)
}
func (m *MysteryPlaceClientConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceClientConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceClientConfigResp proto.InternalMessageInfo

func (m *MysteryPlaceClientConfigResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *MysteryPlaceClientConfigResp) GetMjHideNoviceGuide() bool {
	if m != nil {
		return m.MjHideNoviceGuide
	}
	return false
}

func (m *MysteryPlaceClientConfigResp) GetIsClose() bool {
	if m != nil {
		return m.IsClose
	}
	return false
}

func (m *MysteryPlaceClientConfigResp) GetIsEffectiveRegulation() bool {
	if m != nil {
		return m.IsEffectiveRegulation
	}
	return false
}

func (m *MysteryPlaceClientConfigResp) GetIsHideScenarioRank() bool {
	if m != nil {
		return m.IsHideScenarioRank
	}
	return false
}

func (m *MysteryPlaceClientConfigResp) GetMjControlAudit() bool {
	if m != nil {
		return m.MjControlAudit
	}
	return false
}

func (m *MysteryPlaceClientConfigResp) GetScenarioRankUri() string {
	if m != nil {
		return m.ScenarioRankUri
	}
	return ""
}

// 获取剧本记录
type GetPlayedScenarioRecordListReq struct {
	BaseReq              *app.BaseReq          `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	LoadMore             *MysteryPlaceLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32                `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Uid                  uint32                `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPlayedScenarioRecordListReq) Reset()         { *m = GetPlayedScenarioRecordListReq{} }
func (m *GetPlayedScenarioRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayedScenarioRecordListReq) ProtoMessage()    {}
func (*GetPlayedScenarioRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{56}
}
func (m *GetPlayedScenarioRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayedScenarioRecordListReq.Unmarshal(m, b)
}
func (m *GetPlayedScenarioRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayedScenarioRecordListReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayedScenarioRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayedScenarioRecordListReq.Merge(dst, src)
}
func (m *GetPlayedScenarioRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayedScenarioRecordListReq.Size(m)
}
func (m *GetPlayedScenarioRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayedScenarioRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayedScenarioRecordListReq proto.InternalMessageInfo

func (m *GetPlayedScenarioRecordListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPlayedScenarioRecordListReq) GetLoadMore() *MysteryPlaceLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetPlayedScenarioRecordListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetPlayedScenarioRecordListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPlayedScenarioRecordListResp struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	LoadMore             *MysteryPlaceLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	PlayedRecords        []*ScenarioRecord     `protobuf:"bytes,3,rep,name=played_records,json=playedRecords,proto3" json:"played_records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPlayedScenarioRecordListResp) Reset()         { *m = GetPlayedScenarioRecordListResp{} }
func (m *GetPlayedScenarioRecordListResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayedScenarioRecordListResp) ProtoMessage()    {}
func (*GetPlayedScenarioRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{57}
}
func (m *GetPlayedScenarioRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayedScenarioRecordListResp.Unmarshal(m, b)
}
func (m *GetPlayedScenarioRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayedScenarioRecordListResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayedScenarioRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayedScenarioRecordListResp.Merge(dst, src)
}
func (m *GetPlayedScenarioRecordListResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayedScenarioRecordListResp.Size(m)
}
func (m *GetPlayedScenarioRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayedScenarioRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayedScenarioRecordListResp proto.InternalMessageInfo

func (m *GetPlayedScenarioRecordListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayedScenarioRecordListResp) GetLoadMore() *MysteryPlaceLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetPlayedScenarioRecordListResp) GetPlayedRecords() []*ScenarioRecord {
	if m != nil {
		return m.PlayedRecords
	}
	return nil
}

type ScenarioRecord struct {
	ScenarioId           uint32   `protobuf:"varint,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	ScenarioName         string   `protobuf:"bytes,2,opt,name=scenario_name,json=scenarioName,proto3" json:"scenario_name,omitempty"`
	Bg                   string   `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	PassedChapters       []uint32 `protobuf:"varint,4,rep,packed,name=passed_chapters,json=passedChapters,proto3" json:"passed_chapters,omitempty"`
	AllChapterNum        uint32   `protobuf:"varint,5,opt,name=all_chapter_num,json=allChapterNum,proto3" json:"all_chapter_num,omitempty"`
	Tip                  string   `protobuf:"bytes,6,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScenarioRecord) Reset()         { *m = ScenarioRecord{} }
func (m *ScenarioRecord) String() string { return proto.CompactTextString(m) }
func (*ScenarioRecord) ProtoMessage()    {}
func (*ScenarioRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{58}
}
func (m *ScenarioRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioRecord.Unmarshal(m, b)
}
func (m *ScenarioRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioRecord.Marshal(b, m, deterministic)
}
func (dst *ScenarioRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioRecord.Merge(dst, src)
}
func (m *ScenarioRecord) XXX_Size() int {
	return xxx_messageInfo_ScenarioRecord.Size(m)
}
func (m *ScenarioRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioRecord proto.InternalMessageInfo

func (m *ScenarioRecord) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *ScenarioRecord) GetScenarioName() string {
	if m != nil {
		return m.ScenarioName
	}
	return ""
}

func (m *ScenarioRecord) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ScenarioRecord) GetPassedChapters() []uint32 {
	if m != nil {
		return m.PassedChapters
	}
	return nil
}

func (m *ScenarioRecord) GetAllChapterNum() uint32 {
	if m != nil {
		return m.AllChapterNum
	}
	return 0
}

func (m *ScenarioRecord) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

type GetPlayedScenarioRecordDetailReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ScenarioId           uint32       `protobuf:"varint,2,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetPlayedScenarioRecordDetailReq) Reset()         { *m = GetPlayedScenarioRecordDetailReq{} }
func (m *GetPlayedScenarioRecordDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayedScenarioRecordDetailReq) ProtoMessage()    {}
func (*GetPlayedScenarioRecordDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{59}
}
func (m *GetPlayedScenarioRecordDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailReq.Unmarshal(m, b)
}
func (m *GetPlayedScenarioRecordDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetPlayedScenarioRecordDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayedScenarioRecordDetailReq.Merge(dst, src)
}
func (m *GetPlayedScenarioRecordDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailReq.Size(m)
}
func (m *GetPlayedScenarioRecordDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayedScenarioRecordDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayedScenarioRecordDetailReq proto.InternalMessageInfo

func (m *GetPlayedScenarioRecordDetailReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetPlayedScenarioRecordDetailReq) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *GetPlayedScenarioRecordDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPlayedScenarioRecordDetailResp struct {
	BaseResp             *app.BaseResp         `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Detail               *ScenarioRecordDetail `protobuf:"bytes,2,opt,name=detail,proto3" json:"detail,omitempty"`
	IsVisible            bool                  `protobuf:"varint,3,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetPlayedScenarioRecordDetailResp) Reset()         { *m = GetPlayedScenarioRecordDetailResp{} }
func (m *GetPlayedScenarioRecordDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayedScenarioRecordDetailResp) ProtoMessage()    {}
func (*GetPlayedScenarioRecordDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{60}
}
func (m *GetPlayedScenarioRecordDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailResp.Unmarshal(m, b)
}
func (m *GetPlayedScenarioRecordDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetPlayedScenarioRecordDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlayedScenarioRecordDetailResp.Merge(dst, src)
}
func (m *GetPlayedScenarioRecordDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetPlayedScenarioRecordDetailResp.Size(m)
}
func (m *GetPlayedScenarioRecordDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlayedScenarioRecordDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlayedScenarioRecordDetailResp proto.InternalMessageInfo

func (m *GetPlayedScenarioRecordDetailResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetPlayedScenarioRecordDetailResp) GetDetail() *ScenarioRecordDetail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *GetPlayedScenarioRecordDetailResp) GetIsVisible() bool {
	if m != nil {
		return m.IsVisible
	}
	return false
}

type ScenarioRecordDetail struct {
	ScenarioId           uint32              `protobuf:"varint,1,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	GameId               uint32              `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	TabId                uint32              `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Chapter              uint32              `protobuf:"varint,4,opt,name=chapter,proto3" json:"chapter,omitempty"`
	Player1              *MysteryPlacePlayer `protobuf:"bytes,5,opt,name=player1,proto3" json:"player1,omitempty"`
	Player2              *MysteryPlacePlayer `protobuf:"bytes,6,opt,name=player2,proto3" json:"player2,omitempty"`
	Summary              string              `protobuf:"bytes,7,opt,name=summary,proto3" json:"summary,omitempty"`
	CreatedAt            uint32              `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ScenarioRecordDetail) Reset()         { *m = ScenarioRecordDetail{} }
func (m *ScenarioRecordDetail) String() string { return proto.CompactTextString(m) }
func (*ScenarioRecordDetail) ProtoMessage()    {}
func (*ScenarioRecordDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{61}
}
func (m *ScenarioRecordDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioRecordDetail.Unmarshal(m, b)
}
func (m *ScenarioRecordDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioRecordDetail.Marshal(b, m, deterministic)
}
func (dst *ScenarioRecordDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioRecordDetail.Merge(dst, src)
}
func (m *ScenarioRecordDetail) XXX_Size() int {
	return xxx_messageInfo_ScenarioRecordDetail.Size(m)
}
func (m *ScenarioRecordDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioRecordDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioRecordDetail proto.InternalMessageInfo

func (m *ScenarioRecordDetail) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

func (m *ScenarioRecordDetail) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ScenarioRecordDetail) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *ScenarioRecordDetail) GetChapter() uint32 {
	if m != nil {
		return m.Chapter
	}
	return 0
}

func (m *ScenarioRecordDetail) GetPlayer1() *MysteryPlacePlayer {
	if m != nil {
		return m.Player1
	}
	return nil
}

func (m *ScenarioRecordDetail) GetPlayer2() *MysteryPlacePlayer {
	if m != nil {
		return m.Player2
	}
	return nil
}

func (m *ScenarioRecordDetail) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

func (m *ScenarioRecordDetail) GetCreatedAt() uint32 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

type MysteryPlacePlayer struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryPlacePlayer) Reset()         { *m = MysteryPlacePlayer{} }
func (m *MysteryPlacePlayer) String() string { return proto.CompactTextString(m) }
func (*MysteryPlacePlayer) ProtoMessage()    {}
func (*MysteryPlacePlayer) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{62}
}
func (m *MysteryPlacePlayer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlacePlayer.Unmarshal(m, b)
}
func (m *MysteryPlacePlayer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlacePlayer.Marshal(b, m, deterministic)
}
func (dst *MysteryPlacePlayer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlacePlayer.Merge(dst, src)
}
func (m *MysteryPlacePlayer) XXX_Size() int {
	return xxx_messageInfo_MysteryPlacePlayer.Size(m)
}
func (m *MysteryPlacePlayer) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlacePlayer.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlacePlayer proto.InternalMessageInfo

func (m *MysteryPlacePlayer) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MysteryPlacePlayer) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *MysteryPlacePlayer) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

type MysteryPlaceLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MysteryPlaceLoadMore) Reset()         { *m = MysteryPlaceLoadMore{} }
func (m *MysteryPlaceLoadMore) String() string { return proto.CompactTextString(m) }
func (*MysteryPlaceLoadMore) ProtoMessage()    {}
func (*MysteryPlaceLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{63}
}
func (m *MysteryPlaceLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MysteryPlaceLoadMore.Unmarshal(m, b)
}
func (m *MysteryPlaceLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MysteryPlaceLoadMore.Marshal(b, m, deterministic)
}
func (dst *MysteryPlaceLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MysteryPlaceLoadMore.Merge(dst, src)
}
func (m *MysteryPlaceLoadMore) XXX_Size() int {
	return xxx_messageInfo_MysteryPlaceLoadMore.Size(m)
}
func (m *MysteryPlaceLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_MysteryPlaceLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_MysteryPlaceLoadMore proto.InternalMessageInfo

func (m *MysteryPlaceLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *MysteryPlaceLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

// 获取剧本章节概要
type GetScenarioChapterSummaryReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GameId               uint32       `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Chapter              uint32       `protobuf:"varint,3,opt,name=chapter,proto3" json:"chapter,omitempty"`
	PlaymateUid          uint32       `protobuf:"varint,4,opt,name=playmateUid,proto3" json:"playmateUid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetScenarioChapterSummaryReq) Reset()         { *m = GetScenarioChapterSummaryReq{} }
func (m *GetScenarioChapterSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetScenarioChapterSummaryReq) ProtoMessage()    {}
func (*GetScenarioChapterSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{64}
}
func (m *GetScenarioChapterSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioChapterSummaryReq.Unmarshal(m, b)
}
func (m *GetScenarioChapterSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioChapterSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetScenarioChapterSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioChapterSummaryReq.Merge(dst, src)
}
func (m *GetScenarioChapterSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetScenarioChapterSummaryReq.Size(m)
}
func (m *GetScenarioChapterSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioChapterSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioChapterSummaryReq proto.InternalMessageInfo

func (m *GetScenarioChapterSummaryReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetScenarioChapterSummaryReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetScenarioChapterSummaryReq) GetChapter() uint32 {
	if m != nil {
		return m.Chapter
	}
	return 0
}

func (m *GetScenarioChapterSummaryReq) GetPlaymateUid() uint32 {
	if m != nil {
		return m.PlaymateUid
	}
	return 0
}

type GetScenarioChapterSummaryResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ChapterFragments     []string      `protobuf:"bytes,2,rep,name=chapter_fragments,json=chapterFragments,proto3" json:"chapter_fragments,omitempty"`
	Summary              string        `protobuf:"bytes,3,opt,name=summary,proto3" json:"summary,omitempty"`
	IsVisible            bool          `protobuf:"varint,4,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetScenarioChapterSummaryResp) Reset()         { *m = GetScenarioChapterSummaryResp{} }
func (m *GetScenarioChapterSummaryResp) String() string { return proto.CompactTextString(m) }
func (*GetScenarioChapterSummaryResp) ProtoMessage()    {}
func (*GetScenarioChapterSummaryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{65}
}
func (m *GetScenarioChapterSummaryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScenarioChapterSummaryResp.Unmarshal(m, b)
}
func (m *GetScenarioChapterSummaryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScenarioChapterSummaryResp.Marshal(b, m, deterministic)
}
func (dst *GetScenarioChapterSummaryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScenarioChapterSummaryResp.Merge(dst, src)
}
func (m *GetScenarioChapterSummaryResp) XXX_Size() int {
	return xxx_messageInfo_GetScenarioChapterSummaryResp.Size(m)
}
func (m *GetScenarioChapterSummaryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScenarioChapterSummaryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScenarioChapterSummaryResp proto.InternalMessageInfo

func (m *GetScenarioChapterSummaryResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetScenarioChapterSummaryResp) GetChapterFragments() []string {
	if m != nil {
		return m.ChapterFragments
	}
	return nil
}

func (m *GetScenarioChapterSummaryResp) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

func (m *GetScenarioChapterSummaryResp) GetIsVisible() bool {
	if m != nil {
		return m.IsVisible
	}
	return false
}

// 设置记录可见
type SetPlayedScenarioRecordVisibilityReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	GameId               uint32       `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	PlaymateUid          uint32       `protobuf:"varint,3,opt,name=playmate_uid,json=playmateUid,proto3" json:"playmate_uid,omitempty"`
	IsVisible            bool         `protobuf:"varint,4,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetPlayedScenarioRecordVisibilityReq) Reset()         { *m = SetPlayedScenarioRecordVisibilityReq{} }
func (m *SetPlayedScenarioRecordVisibilityReq) String() string { return proto.CompactTextString(m) }
func (*SetPlayedScenarioRecordVisibilityReq) ProtoMessage()    {}
func (*SetPlayedScenarioRecordVisibilityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{66}
}
func (m *SetPlayedScenarioRecordVisibilityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.Unmarshal(m, b)
}
func (m *SetPlayedScenarioRecordVisibilityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.Marshal(b, m, deterministic)
}
func (dst *SetPlayedScenarioRecordVisibilityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.Merge(dst, src)
}
func (m *SetPlayedScenarioRecordVisibilityReq) XXX_Size() int {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.Size(m)
}
func (m *SetPlayedScenarioRecordVisibilityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPlayedScenarioRecordVisibilityReq proto.InternalMessageInfo

func (m *SetPlayedScenarioRecordVisibilityReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetPlayedScenarioRecordVisibilityReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetPlayedScenarioRecordVisibilityReq) GetPlaymateUid() uint32 {
	if m != nil {
		return m.PlaymateUid
	}
	return 0
}

func (m *SetPlayedScenarioRecordVisibilityReq) GetIsVisible() bool {
	if m != nil {
		return m.IsVisible
	}
	return false
}

type SetPlayedScenarioRecordVisibilityResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsVisible            bool          `protobuf:"varint,2,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetPlayedScenarioRecordVisibilityResp) Reset()         { *m = SetPlayedScenarioRecordVisibilityResp{} }
func (m *SetPlayedScenarioRecordVisibilityResp) String() string { return proto.CompactTextString(m) }
func (*SetPlayedScenarioRecordVisibilityResp) ProtoMessage()    {}
func (*SetPlayedScenarioRecordVisibilityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{67}
}
func (m *SetPlayedScenarioRecordVisibilityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.Unmarshal(m, b)
}
func (m *SetPlayedScenarioRecordVisibilityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.Marshal(b, m, deterministic)
}
func (dst *SetPlayedScenarioRecordVisibilityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.Merge(dst, src)
}
func (m *SetPlayedScenarioRecordVisibilityResp) XXX_Size() int {
	return xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.Size(m)
}
func (m *SetPlayedScenarioRecordVisibilityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPlayedScenarioRecordVisibilityResp proto.InternalMessageInfo

func (m *SetPlayedScenarioRecordVisibilityResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *SetPlayedScenarioRecordVisibilityResp) GetIsVisible() bool {
	if m != nil {
		return m.IsVisible
	}
	return false
}

// 获取推荐剧本
type GetRcmdMiJingTabReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRcmdMiJingTabReq) Reset()         { *m = GetRcmdMiJingTabReq{} }
func (m *GetRcmdMiJingTabReq) String() string { return proto.CompactTextString(m) }
func (*GetRcmdMiJingTabReq) ProtoMessage()    {}
func (*GetRcmdMiJingTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{68}
}
func (m *GetRcmdMiJingTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdMiJingTabReq.Unmarshal(m, b)
}
func (m *GetRcmdMiJingTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdMiJingTabReq.Marshal(b, m, deterministic)
}
func (dst *GetRcmdMiJingTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdMiJingTabReq.Merge(dst, src)
}
func (m *GetRcmdMiJingTabReq) XXX_Size() int {
	return xxx_messageInfo_GetRcmdMiJingTabReq.Size(m)
}
func (m *GetRcmdMiJingTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdMiJingTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdMiJingTabReq proto.InternalMessageInfo

func (m *GetRcmdMiJingTabReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetRcmdMiJingTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type ScenarioExtraInfo struct {
	Score                float32  `protobuf:"fixed32,1,opt,name=score,proto3" json:"score,omitempty"`
	Min                  string   `protobuf:"bytes,2,opt,name=min,proto3" json:"min,omitempty"`
	Labels               []string `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScenarioExtraInfo) Reset()         { *m = ScenarioExtraInfo{} }
func (m *ScenarioExtraInfo) String() string { return proto.CompactTextString(m) }
func (*ScenarioExtraInfo) ProtoMessage()    {}
func (*ScenarioExtraInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{69}
}
func (m *ScenarioExtraInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScenarioExtraInfo.Unmarshal(m, b)
}
func (m *ScenarioExtraInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScenarioExtraInfo.Marshal(b, m, deterministic)
}
func (dst *ScenarioExtraInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScenarioExtraInfo.Merge(dst, src)
}
func (m *ScenarioExtraInfo) XXX_Size() int {
	return xxx_messageInfo_ScenarioExtraInfo.Size(m)
}
func (m *ScenarioExtraInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ScenarioExtraInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ScenarioExtraInfo proto.InternalMessageInfo

func (m *ScenarioExtraInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ScenarioExtraInfo) GetMin() string {
	if m != nil {
		return m.Min
	}
	return ""
}

func (m *ScenarioExtraInfo) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

type RcmdTabInfo struct {
	TabId                uint32             `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Icon                 string             `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Title                string             `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string             `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	Tip                  string             `protobuf:"bytes,5,opt,name=tip,proto3" json:"tip,omitempty"`
	ExtraInfo            *ScenarioExtraInfo `protobuf:"bytes,6,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *RcmdTabInfo) Reset()         { *m = RcmdTabInfo{} }
func (m *RcmdTabInfo) String() string { return proto.CompactTextString(m) }
func (*RcmdTabInfo) ProtoMessage()    {}
func (*RcmdTabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{70}
}
func (m *RcmdTabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RcmdTabInfo.Unmarshal(m, b)
}
func (m *RcmdTabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RcmdTabInfo.Marshal(b, m, deterministic)
}
func (dst *RcmdTabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RcmdTabInfo.Merge(dst, src)
}
func (m *RcmdTabInfo) XXX_Size() int {
	return xxx_messageInfo_RcmdTabInfo.Size(m)
}
func (m *RcmdTabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RcmdTabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RcmdTabInfo proto.InternalMessageInfo

func (m *RcmdTabInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *RcmdTabInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *RcmdTabInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *RcmdTabInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *RcmdTabInfo) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

func (m *RcmdTabInfo) GetExtraInfo() *ScenarioExtraInfo {
	if m != nil {
		return m.ExtraInfo
	}
	return nil
}

type GetRcmdMiJingTabResp struct {
	BaseResp             *app.BaseResp  `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	TabInfos             []*RcmdTabInfo `protobuf:"bytes,2,rep,name=tab_infos,json=tabInfos,proto3" json:"tab_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetRcmdMiJingTabResp) Reset()         { *m = GetRcmdMiJingTabResp{} }
func (m *GetRcmdMiJingTabResp) String() string { return proto.CompactTextString(m) }
func (*GetRcmdMiJingTabResp) ProtoMessage()    {}
func (*GetRcmdMiJingTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{71}
}
func (m *GetRcmdMiJingTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRcmdMiJingTabResp.Unmarshal(m, b)
}
func (m *GetRcmdMiJingTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRcmdMiJingTabResp.Marshal(b, m, deterministic)
}
func (dst *GetRcmdMiJingTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdMiJingTabResp.Merge(dst, src)
}
func (m *GetRcmdMiJingTabResp) XXX_Size() int {
	return xxx_messageInfo_GetRcmdMiJingTabResp.Size(m)
}
func (m *GetRcmdMiJingTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdMiJingTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdMiJingTabResp proto.InternalMessageInfo

func (m *GetRcmdMiJingTabResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRcmdMiJingTabResp) GetTabInfos() []*RcmdTabInfo {
	if m != nil {
		return m.TabInfos
	}
	return nil
}

// 首页豆腐块
type HomePageBigTofuReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	LastTime             uint32       `protobuf:"varint,2,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HomePageBigTofuReq) Reset()         { *m = HomePageBigTofuReq{} }
func (m *HomePageBigTofuReq) String() string { return proto.CompactTextString(m) }
func (*HomePageBigTofuReq) ProtoMessage()    {}
func (*HomePageBigTofuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{72}
}
func (m *HomePageBigTofuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageBigTofuReq.Unmarshal(m, b)
}
func (m *HomePageBigTofuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageBigTofuReq.Marshal(b, m, deterministic)
}
func (dst *HomePageBigTofuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageBigTofuReq.Merge(dst, src)
}
func (m *HomePageBigTofuReq) XXX_Size() int {
	return xxx_messageInfo_HomePageBigTofuReq.Size(m)
}
func (m *HomePageBigTofuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageBigTofuReq.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageBigTofuReq proto.InternalMessageInfo

func (m *HomePageBigTofuReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *HomePageBigTofuReq) GetLastTime() uint32 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

type HomePageBigTofuResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	SubTitle             string        `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	RollingImg           []string      `protobuf:"bytes,3,rep,name=rolling_img,json=rollingImg,proto3" json:"rolling_img,omitempty"`
	HasNew               bool          `protobuf:"varint,4,opt,name=has_new,json=hasNew,proto3" json:"has_new,omitempty"`
	BackgroundImg        string        `protobuf:"bytes,5,opt,name=background_img,json=backgroundImg,proto3" json:"background_img,omitempty"`
	Title                string        `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HomePageBigTofuResp) Reset()         { *m = HomePageBigTofuResp{} }
func (m *HomePageBigTofuResp) String() string { return proto.CompactTextString(m) }
func (*HomePageBigTofuResp) ProtoMessage()    {}
func (*HomePageBigTofuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{73}
}
func (m *HomePageBigTofuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageBigTofuResp.Unmarshal(m, b)
}
func (m *HomePageBigTofuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageBigTofuResp.Marshal(b, m, deterministic)
}
func (dst *HomePageBigTofuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageBigTofuResp.Merge(dst, src)
}
func (m *HomePageBigTofuResp) XXX_Size() int {
	return xxx_messageInfo_HomePageBigTofuResp.Size(m)
}
func (m *HomePageBigTofuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageBigTofuResp.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageBigTofuResp proto.InternalMessageInfo

func (m *HomePageBigTofuResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *HomePageBigTofuResp) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *HomePageBigTofuResp) GetRollingImg() []string {
	if m != nil {
		return m.RollingImg
	}
	return nil
}

func (m *HomePageBigTofuResp) GetHasNew() bool {
	if m != nil {
		return m.HasNew
	}
	return false
}

func (m *HomePageBigTofuResp) GetBackgroundImg() string {
	if m != nil {
		return m.BackgroundImg
	}
	return ""
}

func (m *HomePageBigTofuResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type HomePageRightTofuReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *HomePageRightTofuReq) Reset()         { *m = HomePageRightTofuReq{} }
func (m *HomePageRightTofuReq) String() string { return proto.CompactTextString(m) }
func (*HomePageRightTofuReq) ProtoMessage()    {}
func (*HomePageRightTofuReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{74}
}
func (m *HomePageRightTofuReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageRightTofuReq.Unmarshal(m, b)
}
func (m *HomePageRightTofuReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageRightTofuReq.Marshal(b, m, deterministic)
}
func (dst *HomePageRightTofuReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageRightTofuReq.Merge(dst, src)
}
func (m *HomePageRightTofuReq) XXX_Size() int {
	return xxx_messageInfo_HomePageRightTofuReq.Size(m)
}
func (m *HomePageRightTofuReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageRightTofuReq.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageRightTofuReq proto.InternalMessageInfo

func (m *HomePageRightTofuReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type TofuItem struct {
	Img                  string   `protobuf:"bytes,1,opt,name=img,proto3" json:"img,omitempty"`
	SubTitle             string   `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Link                 string   `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	Title                string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TofuItem) Reset()         { *m = TofuItem{} }
func (m *TofuItem) String() string { return proto.CompactTextString(m) }
func (*TofuItem) ProtoMessage()    {}
func (*TofuItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{75}
}
func (m *TofuItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TofuItem.Unmarshal(m, b)
}
func (m *TofuItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TofuItem.Marshal(b, m, deterministic)
}
func (dst *TofuItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TofuItem.Merge(dst, src)
}
func (m *TofuItem) XXX_Size() int {
	return xxx_messageInfo_TofuItem.Size(m)
}
func (m *TofuItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TofuItem.DiscardUnknown(m)
}

var xxx_messageInfo_TofuItem proto.InternalMessageInfo

func (m *TofuItem) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *TofuItem) GetSubTitle() string {
	if m != nil {
		return m.SubTitle
	}
	return ""
}

func (m *TofuItem) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *TofuItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

type HomePageRightTofuResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Item                 []*TofuItem   `protobuf:"bytes,2,rep,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *HomePageRightTofuResp) Reset()         { *m = HomePageRightTofuResp{} }
func (m *HomePageRightTofuResp) String() string { return proto.CompactTextString(m) }
func (*HomePageRightTofuResp) ProtoMessage()    {}
func (*HomePageRightTofuResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{76}
}
func (m *HomePageRightTofuResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HomePageRightTofuResp.Unmarshal(m, b)
}
func (m *HomePageRightTofuResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HomePageRightTofuResp.Marshal(b, m, deterministic)
}
func (dst *HomePageRightTofuResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HomePageRightTofuResp.Merge(dst, src)
}
func (m *HomePageRightTofuResp) XXX_Size() int {
	return xxx_messageInfo_HomePageRightTofuResp.Size(m)
}
func (m *HomePageRightTofuResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HomePageRightTofuResp.DiscardUnknown(m)
}

var xxx_messageInfo_HomePageRightTofuResp proto.InternalMessageInfo

func (m *HomePageRightTofuResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *HomePageRightTofuResp) GetItem() []*TofuItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type IsUserHasScenarioFreeCouponsReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ScenarioId           uint32       `protobuf:"varint,3,opt,name=scenario_id,json=scenarioId,proto3" json:"scenario_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *IsUserHasScenarioFreeCouponsReq) Reset()         { *m = IsUserHasScenarioFreeCouponsReq{} }
func (m *IsUserHasScenarioFreeCouponsReq) String() string { return proto.CompactTextString(m) }
func (*IsUserHasScenarioFreeCouponsReq) ProtoMessage()    {}
func (*IsUserHasScenarioFreeCouponsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{77}
}
func (m *IsUserHasScenarioFreeCouponsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserHasScenarioFreeCouponsReq.Unmarshal(m, b)
}
func (m *IsUserHasScenarioFreeCouponsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserHasScenarioFreeCouponsReq.Marshal(b, m, deterministic)
}
func (dst *IsUserHasScenarioFreeCouponsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserHasScenarioFreeCouponsReq.Merge(dst, src)
}
func (m *IsUserHasScenarioFreeCouponsReq) XXX_Size() int {
	return xxx_messageInfo_IsUserHasScenarioFreeCouponsReq.Size(m)
}
func (m *IsUserHasScenarioFreeCouponsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserHasScenarioFreeCouponsReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserHasScenarioFreeCouponsReq proto.InternalMessageInfo

func (m *IsUserHasScenarioFreeCouponsReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *IsUserHasScenarioFreeCouponsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IsUserHasScenarioFreeCouponsReq) GetScenarioId() uint32 {
	if m != nil {
		return m.ScenarioId
	}
	return 0
}

type IsUserHasScenarioFreeCouponsResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	IsHadFreeCoupon      bool          `protobuf:"varint,2,opt,name=is_had_free_coupon,json=isHadFreeCoupon,proto3" json:"is_had_free_coupon,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *IsUserHasScenarioFreeCouponsResp) Reset()         { *m = IsUserHasScenarioFreeCouponsResp{} }
func (m *IsUserHasScenarioFreeCouponsResp) String() string { return proto.CompactTextString(m) }
func (*IsUserHasScenarioFreeCouponsResp) ProtoMessage()    {}
func (*IsUserHasScenarioFreeCouponsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{78}
}
func (m *IsUserHasScenarioFreeCouponsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsUserHasScenarioFreeCouponsResp.Unmarshal(m, b)
}
func (m *IsUserHasScenarioFreeCouponsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsUserHasScenarioFreeCouponsResp.Marshal(b, m, deterministic)
}
func (dst *IsUserHasScenarioFreeCouponsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsUserHasScenarioFreeCouponsResp.Merge(dst, src)
}
func (m *IsUserHasScenarioFreeCouponsResp) XXX_Size() int {
	return xxx_messageInfo_IsUserHasScenarioFreeCouponsResp.Size(m)
}
func (m *IsUserHasScenarioFreeCouponsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsUserHasScenarioFreeCouponsResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsUserHasScenarioFreeCouponsResp proto.InternalMessageInfo

func (m *IsUserHasScenarioFreeCouponsResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *IsUserHasScenarioFreeCouponsResp) GetIsHadFreeCoupon() bool {
	if m != nil {
		return m.IsHadFreeCoupon
	}
	return false
}

// deprecated (写错名但又删不了的)
type GetChannelScenarioInfoReqReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelScenarioInfoReqReq) Reset()         { *m = GetChannelScenarioInfoReqReq{} }
func (m *GetChannelScenarioInfoReqReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelScenarioInfoReqReq) ProtoMessage()    {}
func (*GetChannelScenarioInfoReqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{79}
}
func (m *GetChannelScenarioInfoReqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelScenarioInfoReqReq.Unmarshal(m, b)
}
func (m *GetChannelScenarioInfoReqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelScenarioInfoReqReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelScenarioInfoReqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelScenarioInfoReqReq.Merge(dst, src)
}
func (m *GetChannelScenarioInfoReqReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelScenarioInfoReqReq.Size(m)
}
func (m *GetChannelScenarioInfoReqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelScenarioInfoReqReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelScenarioInfoReqReq proto.InternalMessageInfo

func (m *GetChannelScenarioInfoReqReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelScenarioInfoReqReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

// deprecated (写错名但又删不了的)
type GetChannelScenarioInfoReqResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ScenarioInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelScenarioInfoReqResp) Reset()         { *m = GetChannelScenarioInfoReqResp{} }
func (m *GetChannelScenarioInfoReqResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelScenarioInfoReqResp) ProtoMessage()    {}
func (*GetChannelScenarioInfoReqResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{80}
}
func (m *GetChannelScenarioInfoReqResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelScenarioInfoReqResp.Unmarshal(m, b)
}
func (m *GetChannelScenarioInfoReqResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelScenarioInfoReqResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelScenarioInfoReqResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelScenarioInfoReqResp.Merge(dst, src)
}
func (m *GetChannelScenarioInfoReqResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelScenarioInfoReqResp.Size(m)
}
func (m *GetChannelScenarioInfoReqResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelScenarioInfoReqResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelScenarioInfoReqResp proto.InternalMessageInfo

func (m *GetChannelScenarioInfoReqResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelScenarioInfoReqResp) GetInfo() *ScenarioInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetChannelScenarioInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelScenarioInfoReq) Reset()         { *m = GetChannelScenarioInfoReq{} }
func (m *GetChannelScenarioInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelScenarioInfoReq) ProtoMessage()    {}
func (*GetChannelScenarioInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{81}
}
func (m *GetChannelScenarioInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelScenarioInfoReq.Unmarshal(m, b)
}
func (m *GetChannelScenarioInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelScenarioInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelScenarioInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelScenarioInfoReq.Merge(dst, src)
}
func (m *GetChannelScenarioInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelScenarioInfoReq.Size(m)
}
func (m *GetChannelScenarioInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelScenarioInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelScenarioInfoReq proto.InternalMessageInfo

func (m *GetChannelScenarioInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelScenarioInfoReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetChannelScenarioInfoResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Info                 *ScenarioInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelScenarioInfoResp) Reset()         { *m = GetChannelScenarioInfoResp{} }
func (m *GetChannelScenarioInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelScenarioInfoResp) ProtoMessage()    {}
func (*GetChannelScenarioInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{82}
}
func (m *GetChannelScenarioInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelScenarioInfoResp.Unmarshal(m, b)
}
func (m *GetChannelScenarioInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelScenarioInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelScenarioInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelScenarioInfoResp.Merge(dst, src)
}
func (m *GetChannelScenarioInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelScenarioInfoResp.Size(m)
}
func (m *GetChannelScenarioInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelScenarioInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelScenarioInfoResp proto.InternalMessageInfo

func (m *GetChannelScenarioInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelScenarioInfoResp) GetInfo() *ScenarioInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 邀请玩本的信息
type InvitationActivityInfo struct {
	ActivityId           string   `protobuf:"bytes,1,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Uri                  string   `protobuf:"bytes,3,opt,name=uri,proto3" json:"uri,omitempty"`
	Icon                 string   `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InvitationActivityInfo) Reset()         { *m = InvitationActivityInfo{} }
func (m *InvitationActivityInfo) String() string { return proto.CompactTextString(m) }
func (*InvitationActivityInfo) ProtoMessage()    {}
func (*InvitationActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e, []int{83}
}
func (m *InvitationActivityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InvitationActivityInfo.Unmarshal(m, b)
}
func (m *InvitationActivityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InvitationActivityInfo.Marshal(b, m, deterministic)
}
func (dst *InvitationActivityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InvitationActivityInfo.Merge(dst, src)
}
func (m *InvitationActivityInfo) XXX_Size() int {
	return xxx_messageInfo_InvitationActivityInfo.Size(m)
}
func (m *InvitationActivityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_InvitationActivityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_InvitationActivityInfo proto.InternalMessageInfo

func (m *InvitationActivityInfo) GetActivityId() string {
	if m != nil {
		return m.ActivityId
	}
	return ""
}

func (m *InvitationActivityInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *InvitationActivityInfo) GetUri() string {
	if m != nil {
		return m.Uri
	}
	return ""
}

func (m *InvitationActivityInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func init() {
	proto.RegisterType((*ScenarioInfo)(nil), "ga.mystery_place_logic.ScenarioInfo")
	proto.RegisterType((*ListScenarioInfoReq)(nil), "ga.mystery_place_logic.ListScenarioInfoReq")
	proto.RegisterType((*ListScenarioInfoResp)(nil), "ga.mystery_place_logic.ListScenarioInfoResp")
	proto.RegisterType((*GetScenarioInfoReq)(nil), "ga.mystery_place_logic.GetScenarioInfoReq")
	proto.RegisterType((*GetScenarioInfoResp)(nil), "ga.mystery_place_logic.GetScenarioInfoResp")
	proto.RegisterType((*LoginTask)(nil), "ga.mystery_place_logic.LoginTask")
	proto.RegisterType((*GetLoginTaskReq)(nil), "ga.mystery_place_logic.GetLoginTaskReq")
	proto.RegisterType((*GetLoginTaskResp)(nil), "ga.mystery_place_logic.GetLoginTaskResp")
	proto.RegisterType((*ReceiveLoginTaskAwardReq)(nil), "ga.mystery_place_logic.ReceiveLoginTaskAwardReq")
	proto.RegisterType((*ReceiveLoginTaskAwardResp)(nil), "ga.mystery_place_logic.ReceiveLoginTaskAwardResp")
	proto.RegisterType((*GetBalanceInfoReq)(nil), "ga.mystery_place_logic.GetBalanceInfoReq")
	proto.RegisterType((*GetBalanceInfoResp)(nil), "ga.mystery_place_logic.GetBalanceInfoResp")
	proto.RegisterType((*ShareScenarioMsg)(nil), "ga.mystery_place_logic.ShareScenarioMsg")
	proto.RegisterType((*CheckScenarioRoomReq)(nil), "ga.mystery_place_logic.CheckScenarioRoomReq")
	proto.RegisterType((*CheckScenarioRoomResp)(nil), "ga.mystery_place_logic.CheckScenarioRoomResp")
	proto.RegisterType((*Invite2MyRoomReq)(nil), "ga.mystery_place_logic.Invite2MyRoomReq")
	proto.RegisterType((*Invite2MyRoomResp)(nil), "ga.mystery_place_logic.Invite2MyRoomResp")
	proto.RegisterType((*GetInvite2MyRoomResultReq)(nil), "ga.mystery_place_logic.GetInvite2MyRoomResultReq")
	proto.RegisterType((*GetInvite2MyRoomResultResp)(nil), "ga.mystery_place_logic.GetInvite2MyRoomResultResp")
	proto.RegisterType((*Invite2MyRoomResultPush)(nil), "ga.mystery_place_logic.Invite2MyRoomResultPush")
	proto.RegisterType((*ScenarioSimpleInfo)(nil), "ga.mystery_place_logic.ScenarioSimpleInfo")
	proto.RegisterType((*RecommendedScenarioDetailInfo)(nil), "ga.mystery_place_logic.RecommendedScenarioDetailInfo")
	proto.RegisterType((*ScenarioTabInfo)(nil), "ga.mystery_place_logic.ScenarioTabInfo")
	proto.RegisterType((*CharacterInfo)(nil), "ga.mystery_place_logic.CharacterInfo")
	proto.RegisterType((*ListRecommendedScenarioSimpleInfoReq)(nil), "ga.mystery_place_logic.ListRecommendedScenarioSimpleInfoReq")
	proto.RegisterType((*ListRecommendedScenarioSimpleInfoResp)(nil), "ga.mystery_place_logic.ListRecommendedScenarioSimpleInfoResp")
	proto.RegisterType((*ListRecommendedScenarioSimpleInfoLoadMore)(nil), "ga.mystery_place_logic.ListRecommendedScenarioSimpleInfoLoadMore")
	proto.RegisterType((*ListRecommendedScenarioDetailInfoLoadMore)(nil), "ga.mystery_place_logic.ListRecommendedScenarioDetailInfoLoadMore")
	proto.RegisterType((*ListRecommendedScenarioDetailInfoReq)(nil), "ga.mystery_place_logic.ListRecommendedScenarioDetailInfoReq")
	proto.RegisterType((*ListRecommendedScenarioDetailInfoResp)(nil), "ga.mystery_place_logic.ListRecommendedScenarioDetailInfoResp")
	proto.RegisterType((*GetRecommendedScenarioDetailInfoReq)(nil), "ga.mystery_place_logic.GetRecommendedScenarioDetailInfoReq")
	proto.RegisterType((*GetRecommendedScenarioDetailInfoResp)(nil), "ga.mystery_place_logic.GetRecommendedScenarioDetailInfoResp")
	proto.RegisterType((*GetRoomShareLinkByTabIdReq)(nil), "ga.mystery_place_logic.GetRoomShareLinkByTabIdReq")
	proto.RegisterType((*GetRoomShareLinkByTabIdResp)(nil), "ga.mystery_place_logic.GetRoomShareLinkByTabIdResp")
	proto.RegisterType((*MysteryPlaceChannelListReq)(nil), "ga.mystery_place_logic.MysteryPlaceChannelListReq")
	proto.RegisterType((*MysteryPlaceBusinessBlockOption)(nil), "ga.mystery_place_logic.MysteryPlaceBusinessBlockOption")
	proto.RegisterType((*MysteryPlaceChannelListResp)(nil), "ga.mystery_place_logic.MysteryPlaceChannelListResp")
	proto.RegisterType((*MysteryPlaceChannelListResp_DataReport)(nil), "ga.mystery_place_logic.MysteryPlaceChannelListResp.DataReport")
	proto.RegisterType((*MysteryPlaceChannelItem)(nil), "ga.mystery_place_logic.MysteryPlaceChannelItem")
	proto.RegisterType((*MysteryPlaceChannelItem_RecommendationTraceInfo)(nil), "ga.mystery_place_logic.MysteryPlaceChannelItem.RecommendationTraceInfo")
	proto.RegisterType((*MysteryPlaceChannelView)(nil), "ga.mystery_place_logic.MysteryPlaceChannelView")
	proto.RegisterType((*GetCommentPageParamsReq)(nil), "ga.mystery_place_logic.GetCommentPageParamsReq")
	proto.RegisterType((*GetCommentPageParamsResp)(nil), "ga.mystery_place_logic.GetCommentPageParamsResp")
	proto.RegisterType((*PlaymateTag)(nil), "ga.mystery_place_logic.PlaymateTag")
	proto.RegisterType((*ScenarioComment)(nil), "ga.mystery_place_logic.ScenarioComment")
	proto.RegisterType((*CommentToScenarioReq)(nil), "ga.mystery_place_logic.CommentToScenarioReq")
	proto.RegisterType((*CommentToScenarioResp)(nil), "ga.mystery_place_logic.CommentToScenarioResp")
	proto.RegisterType((*GetRcmdScenarioReq)(nil), "ga.mystery_place_logic.GetRcmdScenarioReq")
	proto.RegisterType((*GetRcmdScenarioResp)(nil), "ga.mystery_place_logic.GetRcmdScenarioResp")
	proto.RegisterType((*RequestToSwitchScenarioReq)(nil), "ga.mystery_place_logic.RequestToSwitchScenarioReq")
	proto.RegisterType((*RequestToSwitchScenarioResp)(nil), "ga.mystery_place_logic.RequestToSwitchScenarioResp")
	proto.RegisterType((*GetNewScenarioTipReq)(nil), "ga.mystery_place_logic.GetNewScenarioTipReq")
	proto.RegisterType((*GetNewScenarioTipResp)(nil), "ga.mystery_place_logic.GetNewScenarioTipResp")
	proto.RegisterType((*NewScenarioTip)(nil), "ga.mystery_place_logic.NewScenarioTip")
	proto.RegisterType((*MarkNewScenarioTipReadReq)(nil), "ga.mystery_place_logic.MarkNewScenarioTipReadReq")
	proto.RegisterType((*MarkNewScenarioTipReadResp)(nil), "ga.mystery_place_logic.MarkNewScenarioTipReadResp")
	proto.RegisterType((*MysteryPlaceClientConfigReq)(nil), "ga.mystery_place_logic.MysteryPlaceClientConfigReq")
	proto.RegisterType((*MysteryPlaceClientConfigResp)(nil), "ga.mystery_place_logic.MysteryPlaceClientConfigResp")
	proto.RegisterType((*GetPlayedScenarioRecordListReq)(nil), "ga.mystery_place_logic.GetPlayedScenarioRecordListReq")
	proto.RegisterType((*GetPlayedScenarioRecordListResp)(nil), "ga.mystery_place_logic.GetPlayedScenarioRecordListResp")
	proto.RegisterType((*ScenarioRecord)(nil), "ga.mystery_place_logic.ScenarioRecord")
	proto.RegisterType((*GetPlayedScenarioRecordDetailReq)(nil), "ga.mystery_place_logic.GetPlayedScenarioRecordDetailReq")
	proto.RegisterType((*GetPlayedScenarioRecordDetailResp)(nil), "ga.mystery_place_logic.GetPlayedScenarioRecordDetailResp")
	proto.RegisterType((*ScenarioRecordDetail)(nil), "ga.mystery_place_logic.ScenarioRecordDetail")
	proto.RegisterType((*MysteryPlacePlayer)(nil), "ga.mystery_place_logic.MysteryPlacePlayer")
	proto.RegisterType((*MysteryPlaceLoadMore)(nil), "ga.mystery_place_logic.MysteryPlaceLoadMore")
	proto.RegisterType((*GetScenarioChapterSummaryReq)(nil), "ga.mystery_place_logic.GetScenarioChapterSummaryReq")
	proto.RegisterType((*GetScenarioChapterSummaryResp)(nil), "ga.mystery_place_logic.GetScenarioChapterSummaryResp")
	proto.RegisterType((*SetPlayedScenarioRecordVisibilityReq)(nil), "ga.mystery_place_logic.SetPlayedScenarioRecordVisibilityReq")
	proto.RegisterType((*SetPlayedScenarioRecordVisibilityResp)(nil), "ga.mystery_place_logic.SetPlayedScenarioRecordVisibilityResp")
	proto.RegisterType((*GetRcmdMiJingTabReq)(nil), "ga.mystery_place_logic.GetRcmdMiJingTabReq")
	proto.RegisterType((*ScenarioExtraInfo)(nil), "ga.mystery_place_logic.ScenarioExtraInfo")
	proto.RegisterType((*RcmdTabInfo)(nil), "ga.mystery_place_logic.RcmdTabInfo")
	proto.RegisterType((*GetRcmdMiJingTabResp)(nil), "ga.mystery_place_logic.GetRcmdMiJingTabResp")
	proto.RegisterType((*HomePageBigTofuReq)(nil), "ga.mystery_place_logic.HomePageBigTofuReq")
	proto.RegisterType((*HomePageBigTofuResp)(nil), "ga.mystery_place_logic.HomePageBigTofuResp")
	proto.RegisterType((*HomePageRightTofuReq)(nil), "ga.mystery_place_logic.HomePageRightTofuReq")
	proto.RegisterType((*TofuItem)(nil), "ga.mystery_place_logic.TofuItem")
	proto.RegisterType((*HomePageRightTofuResp)(nil), "ga.mystery_place_logic.HomePageRightTofuResp")
	proto.RegisterType((*IsUserHasScenarioFreeCouponsReq)(nil), "ga.mystery_place_logic.IsUserHasScenarioFreeCouponsReq")
	proto.RegisterType((*IsUserHasScenarioFreeCouponsResp)(nil), "ga.mystery_place_logic.IsUserHasScenarioFreeCouponsResp")
	proto.RegisterType((*GetChannelScenarioInfoReqReq)(nil), "ga.mystery_place_logic.GetChannelScenarioInfoReqReq")
	proto.RegisterType((*GetChannelScenarioInfoReqResp)(nil), "ga.mystery_place_logic.GetChannelScenarioInfoReqResp")
	proto.RegisterType((*GetChannelScenarioInfoReq)(nil), "ga.mystery_place_logic.GetChannelScenarioInfoReq")
	proto.RegisterType((*GetChannelScenarioInfoResp)(nil), "ga.mystery_place_logic.GetChannelScenarioInfoResp")
	proto.RegisterType((*InvitationActivityInfo)(nil), "ga.mystery_place_logic.InvitationActivityInfo")
	proto.RegisterEnum("ga.mystery_place_logic.GameOrientation", GameOrientation_name, GameOrientation_value)
	proto.RegisterEnum("ga.mystery_place_logic.MysteryTeamStatus", MysteryTeamStatus_name, MysteryTeamStatus_value)
	proto.RegisterEnum("ga.mystery_place_logic.MysteryInviteResult", MysteryInviteResult_name, MysteryInviteResult_value)
	proto.RegisterEnum("ga.mystery_place_logic.ScenarioTagType", ScenarioTagType_name, ScenarioTagType_value)
	proto.RegisterEnum("ga.mystery_place_logic.ScenarioTabPlayMode", ScenarioTabPlayMode_name, ScenarioTabPlayMode_value)
	proto.RegisterEnum("ga.mystery_place_logic.ScenarioTabStatus", ScenarioTabStatus_name, ScenarioTabStatus_value)
	proto.RegisterEnum("ga.mystery_place_logic.RecommendedScenarioType", RecommendedScenarioType_name, RecommendedScenarioType_value)
	proto.RegisterEnum("ga.mystery_place_logic.MysteryPlaceBlockEnum", MysteryPlaceBlockEnum_name, MysteryPlaceBlockEnum_value)
	proto.RegisterEnum("ga.mystery_place_logic.MysteryPlaceElemEnum", MysteryPlaceElemEnum_name, MysteryPlaceElemEnum_value)
	proto.RegisterEnum("ga.mystery_place_logic.MysteryPlaceRCMDLabelEnum", MysteryPlaceRCMDLabelEnum_name, MysteryPlaceRCMDLabelEnum_value)
	proto.RegisterEnum("ga.mystery_place_logic.PlaymateTagType", PlaymateTagType_name, PlaymateTagType_value)
	proto.RegisterEnum("ga.mystery_place_logic.NewScenarioTipType", NewScenarioTipType_name, NewScenarioTipType_value)
	proto.RegisterEnum("ga.mystery_place_logic.ScenarioInfo_PlayMode", ScenarioInfo_PlayMode_name, ScenarioInfo_PlayMode_value)
	proto.RegisterEnum("ga.mystery_place_logic.LoginTask_State", LoginTask_State_name, LoginTask_State_value)
	proto.RegisterEnum("ga.mystery_place_logic.MysteryPlaceChannelListReq_Sex", MysteryPlaceChannelListReq_Sex_name, MysteryPlaceChannelListReq_Sex_value)
}

func init() {
	proto.RegisterFile("mystery-place-logic_.proto", fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e)
}

var fileDescriptor_mystery_place_logic__5f32a1fdc1f8093e = []byte{
	// 5046 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3c, 0x4d, 0x6f, 0x24, 0x49,
	0x56, 0x9d, 0x55, 0xfe, 0xa8, 0x7a, 0xe5, 0x2a, 0x97, 0xc3, 0x76, 0xbb, 0xda, 0x3d, 0x3d, 0xed,
	0xce, 0x9d, 0xee, 0xe9, 0x71, 0xcf, 0xb8, 0x77, 0x9a, 0xd9, 0xd9, 0x61, 0x87, 0x5d, 0x51, 0x2e,
	0x57, 0xdb, 0x35, 0x6b, 0x97, 0x4d, 0xba, 0xdc, 0xc3, 0xce, 0x7e, 0x24, 0x51, 0x99, 0xe1, 0x72,
	0x8c, 0xf3, 0xa3, 0x3a, 0x33, 0xca, 0x6e, 0x8f, 0x18, 0xad, 0x56, 0xc3, 0x65, 0x0f, 0x70, 0x02,
	0x24, 0x40, 0xe2, 0xc2, 0x61, 0x35, 0x88, 0x03, 0x12, 0x12, 0x17, 0x04, 0xe2, 0x02, 0x08, 0x89,
	0x23, 0xe2, 0x27, 0x70, 0xe1, 0xca, 0x01, 0x4e, 0xa0, 0xf8, 0xc8, 0xca, 0xac, 0x2f, 0xdb, 0x39,
	0xdd, 0xcc, 0x4a, 0x9c, 0x9c, 0xf1, 0xe2, 0xc5, 0x8b, 0x17, 0x2f, 0xde, 0x7b, 0x11, 0xef, 0xc5,
	0x2b, 0xc3, 0xaa, 0x7b, 0x11, 0x32, 0x12, 0x5c, 0xbc, 0xd3, 0x75, 0xb0, 0x45, 0xde, 0x71, 0xfc,
	0x0e, 0xb5, 0xcc, 0x8d, 0x6e, 0xe0, 0x33, 0x1f, 0xdd, 0xec, 0xe0, 0x0d, 0xd5, 0x6d, 0x8a, 0x6e,
	0x53, 0x74, 0xaf, 0x16, 0x3b, 0xd8, 0x6c, 0xe3, 0x90, 0x48, 0xb4, 0xd5, 0xb7, 0xc7, 0xe0, 0x3c,
	0x1e, 0x84, 0x9d, 0x51, 0x72, 0x2e, 0xb1, 0xf5, 0xff, 0xce, 0xc1, 0xdc, 0xa1, 0x45, 0x3c, 0x1c,
	0x50, 0xbf, 0xe1, 0x1d, 0xfb, 0xa8, 0x04, 0x19, 0x6a, 0x57, 0xb4, 0x35, 0xed, 0x61, 0xd1, 0xc8,
	0x50, 0x1b, 0x7d, 0x04, 0xf9, 0xae, 0x83, 0x2f, 0x4c, 0xd7, 0xb7, 0x49, 0x25, 0xb3, 0xa6, 0x3d,
	0x2c, 0x3d, 0x79, 0x67, 0x63, 0x3c, 0x27, 0x1b, 0x49, 0x42, 0x1b, 0x07, 0x0e, 0xbe, 0xd8, 0xf3,
	0x6d, 0x62, 0xe4, 0xba, 0xea, 0x0b, 0x2d, 0xc3, 0x0c, 0xc3, 0x6d, 0x93, 0xda, 0x95, 0xac, 0xa0,
	0x3f, 0xcd, 0x70, 0xbb, 0x61, 0xa3, 0x5b, 0x90, 0xe3, 0x60, 0x0f, 0xbb, 0xa4, 0x32, 0xb5, 0xa6,
	0x3d, 0xcc, 0x1b, 0xb3, 0x0c, 0xb7, 0x9b, 0xd8, 0x25, 0x08, 0xc1, 0x94, 0x43, 0xbd, 0xd3, 0xca,
	0xb4, 0x00, 0x8b, 0x6f, 0xa4, 0xc3, 0x1c, 0xf5, 0x58, 0xe0, 0xdb, 0x3d, 0x8b, 0x51, 0xdf, 0xab,
	0xcc, 0x88, 0xbe, 0x01, 0x18, 0xba, 0x09, 0x33, 0x0e, 0x6e, 0x13, 0x27, 0xac, 0xcc, 0xae, 0x65,
	0x1f, 0xe6, 0x0d, 0xd5, 0x42, 0x1b, 0xb0, 0xe8, 0x91, 0x73, 0xd3, 0xa6, 0xa1, 0x58, 0x54, 0x97,
	0x5a, 0xac, 0x17, 0x90, 0x4a, 0x4e, 0x90, 0x58, 0xf0, 0xc8, 0xf9, 0x96, 0xec, 0x39, 0x90, 0x1d,
	0x1c, 0xdf, 0x77, 0xec, 0x11, 0xfc, 0xbc, 0xc4, 0xf7, 0x1d, 0x7b, 0x08, 0x7f, 0x1d, 0x16, 0xda,
	0xb4, 0x63, 0x86, 0x27, 0x38, 0x20, 0x7d, 0x6c, 0x10, 0xd8, 0xf3, 0x6d, 0xda, 0x39, 0xe4, 0xf0,
	0x04, 0xed, 0xd0, 0xc5, 0x8e, 0x33, 0x84, 0x5d, 0x90, 0xb4, 0x45, 0xd7, 0x00, 0xfe, 0x12, 0x4c,
	0x9f, 0x51, 0x9b, 0xf8, 0x95, 0x39, 0x81, 0x21, 0x1b, 0x1c, 0xca, 0x28, 0x73, 0x48, 0xa5, 0x28,
	0xa1, 0xa2, 0x81, 0xee, 0xc1, 0x9c, 0x5c, 0xb1, 0x69, 0xf9, 0x8e, 0x1f, 0x54, 0x4a, 0xa2, 0xb3,
	0x20, 0x61, 0x35, 0x0e, 0xe2, 0xa2, 0x0d, 0x30, 0x23, 0x95, 0x79, 0x29, 0x5a, 0xfe, 0x8d, 0x56,
	0x60, 0x56, 0x6e, 0x50, 0x58, 0x29, 0xaf, 0x65, 0x1f, 0x16, 0x8d, 0x19, 0xb1, 0x43, 0x21, 0x7a,
	0x0f, 0x6e, 0x86, 0x6a, 0x73, 0x4d, 0xc9, 0x74, 0xc4, 0xee, 0x82, 0x18, 0xbe, 0x14, 0xf5, 0x1e,
	0xf2, 0xce, 0x88, 0xe3, 0x0f, 0x61, 0x95, 0x4b, 0x7b, 0xc2, 0x48, 0x24, 0x46, 0xae, 0x78, 0xe4,
	0xfc, 0x70, 0xdc, 0xe0, 0x5d, 0x28, 0x59, 0x27, 0x38, 0xc0, 0x16, 0x23, 0x81, 0x49, 0xbd, 0x63,
	0xbf, 0xb2, 0xb8, 0x96, 0x7d, 0x58, 0x78, 0x72, 0x7f, 0x92, 0xf6, 0xd5, 0x22, 0x6c, 0xae, 0x7e,
	0x46, 0xd1, 0x4a, 0x36, 0xb9, 0x98, 0x42, 0xcb, 0x0f, 0x48, 0x65, 0x69, 0x4d, 0x7b, 0x98, 0x31,
	0x64, 0x03, 0xbd, 0x09, 0xf3, 0x01, 0xb1, 0x7c, 0xd7, 0x25, 0x9e, 0x6d, 0x4a, 0xe1, 0x2e, 0x0b,
	0xae, 0x4a, 0x7d, 0xf0, 0x33, 0x21, 0xe5, 0x47, 0xb0, 0x10, 0x23, 0x46, 0x0b, 0xb8, 0x29, 0x54,
	0xab, 0xdc, 0xef, 0x88, 0x38, 0x7f, 0x03, 0x4a, 0x2e, 0xf5, 0x38, 0x6f, 0x17, 0x24, 0x30, 0xbd,
	0x9e, 0x5b, 0x59, 0x11, 0xea, 0x3e, 0xe7, 0x52, 0xef, 0x40, 0x00, 0x9b, 0x3d, 0x97, 0xcf, 0xdd,
	0xc1, 0x2e, 0x31, 0x6d, 0x7a, 0x7c, 0x4c, 0xad, 0x9e, 0xc3, 0x2e, 0x2a, 0x15, 0x39, 0x37, 0x07,
	0x6f, 0xf5, 0xa1, 0xe8, 0x9b, 0xb0, 0x44, 0x42, 0x46, 0x5d, 0xcc, 0x88, 0x79, 0x4c, 0x3d, 0x1a,
	0x9e, 0x98, 0x8c, 0xba, 0xa4, 0x72, 0x4b, 0x60, 0xa3, 0xa8, 0xef, 0xa9, 0xe8, 0x6a, 0x51, 0x57,
	0x30, 0xd0, 0xa6, 0x9e, 0x6d, 0x32, 0xbf, 0x4b, 0x2d, 0xb1, 0x9b, 0xab, 0x82, 0xd5, 0x39, 0x0e,
	0x6d, 0x71, 0x20, 0xdf, 0x53, 0x03, 0xca, 0x82, 0x01, 0x3f, 0xa0, 0xc4, 0x63, 0x58, 0xd8, 0xd2,
	0x6d, 0x61, 0xe0, 0x6f, 0x4e, 0x12, 0xf1, 0x36, 0x76, 0xc9, 0x7e, 0x8c, 0x6e, 0x88, 0x15, 0x24,
	0x00, 0xfa, 0x23, 0xc8, 0x45, 0x76, 0x8f, 0x72, 0x30, 0xd5, 0xf4, 0x3d, 0x52, 0xbe, 0x81, 0x00,
	0x66, 0x0e, 0xa9, 0xd7, 0x71, 0x48, 0x59, 0x43, 0x79, 0x98, 0xde, 0xeb, 0x39, 0x8c, 0x96, 0x33,
	0xfa, 0x77, 0x61, 0x71, 0x97, 0x86, 0x2c, 0xe9, 0x35, 0x0c, 0xf2, 0x1c, 0x3d, 0x80, 0x1c, 0x77,
	0x67, 0x66, 0x40, 0x9e, 0x0b, 0x3f, 0x54, 0x78, 0x52, 0xe0, 0xfc, 0x6c, 0xe2, 0x90, 0x18, 0xe4,
	0xb9, 0x31, 0xdb, 0x96, 0x1f, 0xfa, 0x3f, 0x64, 0x60, 0x69, 0x74, 0x7c, 0xd8, 0x45, 0x6f, 0x41,
	0x5e, 0x11, 0x08, 0xbb, 0x8a, 0xc2, 0x5c, 0x4c, 0x21, 0xec, 0x1a, 0xb9, 0xb6, 0xfa, 0x42, 0xdf,
	0x81, 0x69, 0xae, 0x5a, 0x61, 0x25, 0x23, 0x74, 0xeb, 0x8d, 0xeb, 0x78, 0x36, 0x43, 0x0e, 0xe1,
	0x6e, 0x8b, 0xfb, 0x86, 0x5e, 0x48, 0x02, 0xe1, 0xcf, 0x72, 0xc6, 0xac, 0xef, 0xd8, 0x47, 0x21,
	0x09, 0xf8, 0xde, 0x9e, 0xf9, 0x3d, 0xeb, 0x84, 0x04, 0x66, 0x1b, 0x3b, 0xd8, 0xb3, 0xa4, 0x63,
	0x2b, 0x1a, 0x25, 0x05, 0xde, 0x94, 0x50, 0xf4, 0x10, 0xca, 0x0e, 0x0e, 0x99, 0xc9, 0x75, 0x28,
	0xb0, 0x4d, 0x46, 0x5e, 0x30, 0xe5, 0xeb, 0x4a, 0x1c, 0x6e, 0x08, 0x70, 0x8b, 0xbc, 0x60, 0xe8,
	0x37, 0x61, 0x59, 0x60, 0x72, 0xad, 0xa2, 0x5e, 0xa7, 0x6f, 0x54, 0xc2, 0xfd, 0x5d, 0x97, 0xf3,
	0x45, 0x4e, 0xe2, 0x40, 0x52, 0x88, 0x3a, 0xf4, 0x5d, 0x40, 0xdb, 0xe4, 0xab, 0xee, 0x82, 0x3a,
	0x2f, 0x32, 0xd1, 0x79, 0xa1, 0xff, 0x81, 0x06, 0x8b, 0x23, 0xe4, 0xd2, 0x6d, 0xca, 0x07, 0x30,
	0x25, 0xec, 0x3d, 0x93, 0x62, 0x65, 0x62, 0xc4, 0x25, 0x5b, 0xa2, 0x7f, 0x91, 0x81, 0xfc, 0xae,
	0xdf, 0xa1, 0x5e, 0x0b, 0x87, 0xa7, 0xb1, 0xd7, 0xd4, 0x92, 0x5e, 0xf3, 0xbb, 0x30, 0x1d, 0x32,
	0xee, 0x13, 0x33, 0x97, 0x9b, 0x41, 0x9f, 0xce, 0xc6, 0x21, 0x47, 0x37, 0xe4, 0x28, 0xee, 0x51,
	0x6d, 0x12, 0x5a, 0x62, 0xe6, 0xbc, 0x21, 0xbe, 0xd1, 0xeb, 0x00, 0x6d, 0x6c, 0x9d, 0x76, 0x02,
	0xbf, 0xe7, 0xd9, 0xea, 0x74, 0x4b, 0x40, 0xd0, 0x1d, 0x80, 0xae, 0xdf, 0xed, 0x75, 0x4d, 0x6a,
	0xf9, 0x9e, 0xda, 0xfa, 0xbc, 0x80, 0x34, 0x2c, 0xdf, 0xd3, 0x9b, 0x30, 0x2d, 0xa6, 0x40, 0x45,
	0xc8, 0x8b, 0x0f, 0x65, 0x51, 0x8b, 0x30, 0x2f, 0x9a, 0x47, 0x5e, 0x40, 0x2c, 0x42, 0xcf, 0x88,
	0x5d, 0xd6, 0xd0, 0x02, 0x14, 0x25, 0x3f, 0x11, 0x28, 0x83, 0x4a, 0x00, 0x02, 0x54, 0xf7, 0x6c,
	0x62, 0x97, 0xb3, 0xfa, 0xaf, 0xc2, 0xfc, 0x36, 0x61, 0x7d, 0xfe, 0xd3, 0x98, 0xdb, 0x4f, 0xa1,
	0x3c, 0x38, 0x34, 0xdd, 0xa6, 0xfe, 0x3a, 0x00, 0x17, 0x9e, 0x67, 0x32, 0x1c, 0x9e, 0xaa, 0xad,
	0xbd, 0x77, 0xa5, 0x80, 0x8d, 0xbc, 0x13, 0x7d, 0xea, 0x9b, 0x50, 0x51, 0x2b, 0xeb, 0x77, 0x57,
	0xcf, 0x71, 0x60, 0xa7, 0x59, 0xc4, 0x53, 0xb8, 0x35, 0x81, 0x46, 0xaa, 0xd5, 0xe8, 0x1f, 0xc2,
	0xc2, 0x36, 0x61, 0xca, 0x8a, 0xd3, 0x3a, 0xae, 0x3f, 0xd7, 0x84, 0xc5, 0x0d, 0x8c, 0x4e, 0x27,
	0xcc, 0x31, 0xfe, 0x25, 0x33, 0xd6, 0xbf, 0x0c, 0x4a, 0x3d, 0xfb, 0x15, 0xa4, 0xfe, 0x63, 0x28,
	0x8b, 0x5b, 0x48, 0x64, 0x6d, 0x7b, 0x61, 0x07, 0x35, 0xa0, 0xd8, 0x3f, 0xd3, 0x85, 0xa5, 0x6a,
	0x29, 0x2c, 0x75, 0x2e, 0x4c, 0xb4, 0xf4, 0x2f, 0x35, 0x58, 0xaa, 0x9d, 0x10, 0xeb, 0x34, 0xc2,
	0x31, 0x7c, 0xdf, 0x4d, 0xe3, 0x7f, 0xee, 0x00, 0x30, 0xdf, 0xb4, 0x4e, 0xb0, 0xe7, 0x11, 0x47,
	0x49, 0x21, 0xcf, 0xfc, 0x9a, 0x04, 0xa0, 0xc7, 0xb0, 0x14, 0x77, 0x9b, 0xfe, 0xb9, 0x47, 0x02,
	0xb3, 0xd7, 0xbf, 0x80, 0x2e, 0xf4, 0x11, 0xf7, 0x79, 0xcf, 0x11, 0xed, 0x5f, 0x46, 0x1d, 0x1a,
	0xb2, 0xca, 0x94, 0xb8, 0x03, 0xf1, 0x2b, 0x11, 0x3f, 0x67, 0xf4, 0xdf, 0xd3, 0x60, 0x79, 0x0c,
	0xaf, 0xe9, 0xb6, 0xee, 0x23, 0x28, 0x30, 0x82, 0x5d, 0x93, 0xbb, 0x8c, 0x5e, 0xa8, 0x3c, 0xcd,
	0x5b, 0x93, 0x24, 0xb7, 0x27, 0x61, 0x2d, 0x82, 0xdd, 0x43, 0x31, 0xc0, 0x00, 0xd6, 0xff, 0xd6,
	0xff, 0x5a, 0x83, 0x72, 0xc3, 0x3b, 0xa3, 0x8c, 0x3c, 0xd9, 0xbb, 0x78, 0xc5, 0x82, 0x9b, 0x70,
	0x57, 0xbf, 0x03, 0x40, 0xc5, 0x8c, 0x42, 0x8a, 0xf2, 0x50, 0xcb, 0x4b, 0x08, 0x97, 0xde, 0x7d,
	0x28, 0xa9, 0xee, 0x88, 0xf0, 0xb4, 0x40, 0x29, 0x4a, 0xa8, 0x22, 0xae, 0xff, 0x42, 0x83, 0x85,
	0x21, 0xc6, 0x7f, 0x69, 0x52, 0x44, 0xb7, 0x41, 0x2d, 0x20, 0x5a, 0x6c, 0xde, 0xc8, 0x49, 0x40,
	0xc3, 0xd6, 0x7f, 0x0b, 0x6e, 0x6d, 0x13, 0x36, 0xcc, 0x6b, 0xcf, 0x61, 0x69, 0x44, 0x3d, 0x30,
	0x43, 0x66, 0x68, 0x86, 0x3f, 0xd2, 0x60, 0x75, 0xd2, 0x14, 0xe9, 0x84, 0x72, 0x00, 0x4a, 0xcc,
	0x1c, 0xb9, 0xe7, 0x30, 0x25, 0x96, 0x47, 0x57, 0x88, 0x45, 0xce, 0xac, 0xa6, 0x9c, 0xa3, 0x89,
	0x16, 0xb7, 0xce, 0x95, 0x31, 0x8c, 0x1d, 0xf4, 0xc2, 0x93, 0x21, 0x4d, 0xd0, 0x86, 0x35, 0xe1,
	0x0a, 0xf5, 0x1a, 0xe1, 0x35, 0xfb, 0xb2, 0xbc, 0xfe, 0x97, 0x06, 0xa8, 0x1f, 0x48, 0x50, 0xb7,
	0xeb, 0x90, 0xb1, 0xf1, 0x6c, 0xff, 0xe4, 0xcf, 0x24, 0x4f, 0xfe, 0xc9, 0xf1, 0x4d, 0xf6, 0x92,
	0xf8, 0xe6, 0x2e, 0x14, 0x8e, 0x69, 0x10, 0x32, 0x53, 0xc4, 0x55, 0xd1, 0xe9, 0x2e, 0x40, 0xbb,
	0x1c, 0x32, 0x12, 0x86, 0x4d, 0x8f, 0x86, 0x61, 0x9b, 0xdc, 0xdf, 0x74, 0x4c, 0x76, 0xd1, 0x25,
	0xe2, 0x2a, 0x77, 0xc9, 0xb5, 0x23, 0x5a, 0x5d, 0x0b, 0x77, 0x5a, 0x17, 0x5d, 0xc2, 0x1d, 0x93,
	0xf8, 0xd0, 0xff, 0xac, 0x00, 0x77, 0x8c, 0x28, 0x0a, 0x21, 0x76, 0x84, 0xb7, 0x45, 0x18, 0xa6,
	0xce, 0xff, 0xb9, 0x14, 0xb6, 0x20, 0x2f, 0x3c, 0x85, 0xb8, 0x47, 0x4f, 0x89, 0x7b, 0xf4, 0x35,
	0x96, 0xd0, 0x16, 0x87, 0x01, 0xf7, 0xb5, 0x0d, 0x71, 0x9b, 0xae, 0xc0, 0x6c, 0x1b, 0x07, 0x01,
	0xee, 0x90, 0xca, 0xb4, 0x08, 0x56, 0xa2, 0xe6, 0xb8, 0x20, 0x6d, 0xe6, 0xfa, 0x41, 0xda, 0xec,
	0x84, 0x20, 0x6d, 0x38, 0x8b, 0x90, 0x1b, 0x93, 0x45, 0x98, 0x14, 0x79, 0xe5, 0x27, 0x46, 0x5e,
	0x71, 0xde, 0x01, 0x06, 0xf2, 0x0e, 0xc3, 0x8a, 0x50, 0x18, 0x55, 0x84, 0x35, 0x98, 0x93, 0xca,
	0xa4, 0xdc, 0xee, 0x9c, 0xd8, 0x2c, 0xa9, 0x4d, 0x2d, 0xe1, 0x7b, 0x3f, 0x84, 0x55, 0xab, 0x17,
	0x04, 0xc4, 0x8b, 0xa3, 0x80, 0xc0, 0xf7, 0x5d, 0xd3, 0xf2, 0x7b, 0x1e, 0x13, 0xf1, 0x7f, 0xd1,
	0x58, 0x51, 0x18, 0xea, 0x92, 0xcf, 0x6d, 0xb6, 0xc6, 0xbb, 0x85, 0x67, 0x0e, 0xb9, 0xb1, 0x91,
	0xe0, 0x4c, 0xc6, 0x7a, 0x25, 0x71, 0x41, 0x2e, 0xd2, 0xd0, 0x88, 0x81, 0x71, 0x9c, 0x3c, 0x9f,
	0x8c, 0x93, 0x47, 0x23, 0xda, 0xf2, 0xf5, 0x22, 0xda, 0x85, 0xb1, 0x11, 0xed, 0x6d, 0xc8, 0x87,
	0x0c, 0x07, 0xcc, 0x6c, 0x33, 0x4f, 0xa5, 0x01, 0x72, 0x02, 0xb0, 0xc9, 0x3c, 0xb4, 0x0e, 0x0b,
	0xc7, 0x3c, 0x78, 0xe5, 0x93, 0x09, 0xc9, 0x73, 0xa4, 0x45, 0x99, 0x42, 0xe1, 0x1d, 0x07, 0x0a,
	0xce, 0x71, 0xef, 0xc1, 0x5c, 0xe0, 0x3b, 0x71, 0xee, 0x64, 0x49, 0x8a, 0x95, 0xc3, 0xa2, 0x7d,
	0x7e, 0x02, 0xcb, 0x1e, 0x39, 0x37, 0x47, 0x15, 0x43, 0x06, 0xfa, 0x8b, 0x1e, 0x39, 0x37, 0x86,
	0x75, 0xc3, 0x80, 0x07, 0xca, 0x39, 0xf5, 0x99, 0x98, 0x60, 0x17, 0x37, 0x05, 0x11, 0x5d, 0x62,
	0x47, 0x9c, 0x5d, 0x33, 0x9d, 0xb1, 0xf2, 0x12, 0xe9, 0x0c, 0x1d, 0x8a, 0x34, 0x34, 0xc3, 0x13,
	0xff, 0xdc, 0xec, 0x06, 0xd4, 0x22, 0x95, 0x07, 0x62, 0x33, 0x0b, 0x34, 0x3c, 0x3c, 0xf1, 0xcf,
	0x0f, 0x38, 0x08, 0xdd, 0x85, 0xbc, 0xe8, 0x13, 0xfb, 0xf5, 0x26, 0x67, 0x74, 0x33, 0x53, 0xd1,
	0x8c, 0x9c, 0x00, 0xf2, 0xfd, 0xba, 0x05, 0x39, 0x1a, 0x9a, 0x2c, 0xa0, 0xd8, 0xa9, 0x3c, 0x94,
	0xd1, 0x12, 0x0d, 0x5b, 0xbc, 0x39, 0x26, 0x83, 0xf0, 0xd6, 0x98, 0x0c, 0x82, 0x0e, 0xc5, 0xfe,
	0x0c, 0x26, 0xf5, 0x58, 0x65, 0x5d, 0x68, 0x45, 0x21, 0x9a, 0xa1, 0xe1, 0x31, 0x99, 0x66, 0xf2,
	0x4e, 0x2b, 0x8f, 0x44, 0x97, 0xf8, 0xe6, 0x13, 0xf3, 0xbf, 0x66, 0x2f, 0x70, 0x2a, 0x6f, 0xcb,
	0x84, 0x1f, 0x6f, 0x1f, 0x05, 0x0e, 0x57, 0x0d, 0xd1, 0x25, 0xc2, 0xa1, 0x77, 0xa4, 0x6a, 0x70,
	0x00, 0x8f, 0x86, 0xc6, 0x66, 0x2c, 0x36, 0x5e, 0x2e, 0x63, 0x81, 0x4e, 0xa0, 0x22, 0x76, 0x4f,
	0xb4, 0x4c, 0x6c, 0x31, 0x7a, 0x46, 0xd9, 0x85, 0xdc, 0xa1, 0xc7, 0xe2, 0xb8, 0xdd, 0x98, 0x44,
	0xbb, 0xd1, 0x1f, 0x57, 0x55, 0xc3, 0xc4, 0x56, 0xdd, 0xa4, 0x63, 0xe1, 0xfa, 0x7f, 0x68, 0x30,
	0x3f, 0xe4, 0xff, 0xd0, 0xce, 0x68, 0x76, 0xf5, 0xd1, 0x35, 0x7c, 0xe7, 0xf5, 0x73, 0xab, 0x08,
	0xa6, 0x84, 0x28, 0xe5, 0xd9, 0x24, 0xbe, 0xd1, 0x0e, 0x00, 0x47, 0x55, 0x77, 0xa7, 0xe9, 0xcb,
	0xef, 0x4e, 0x89, 0x59, 0xd5, 0xdd, 0x89, 0x7b, 0x7b, 0x75, 0x75, 0x4a, 0x66, 0x6e, 0x67, 0x06,
	0x32, 0xb7, 0x7a, 0x03, 0x8a, 0x03, 0x1a, 0xcc, 0x39, 0x11, 0x78, 0x32, 0xe2, 0x16, 0xdf, 0xa8,
	0x0c, 0x59, 0xea, 0x76, 0xd4, 0x21, 0xc4, 0x3f, 0x05, 0xbf, 0x5c, 0xf4, 0x2a, 0x86, 0xe6, 0xdf,
	0xfa, 0xcf, 0x33, 0xf0, 0x06, 0xbf, 0x80, 0x8f, 0x39, 0xe2, 0xe2, 0x83, 0x3e, 0xcd, 0x7d, 0xac,
	0x06, 0x53, 0xe2, 0xbc, 0x95, 0x02, 0x7f, 0x3c, 0x69, 0xe9, 0x63, 0xe6, 0x13, 0xe7, 0xae, 0x18,
	0xcc, 0x3d, 0xa5, 0x74, 0xbc, 0x4a, 0xde, 0xa2, 0x81, 0x7e, 0x02, 0x79, 0xc7, 0xc7, 0xb6, 0xe9,
	0x72, 0x1f, 0x3a, 0x25, 0x78, 0xa8, 0x4e, 0x8c, 0xb7, 0xae, 0x5a, 0xd3, 0xae, 0x8f, 0xed, 0x3d,
	0x3f, 0x20, 0x46, 0xce, 0x51, 0x5f, 0xfa, 0x7f, 0x6a, 0x70, 0xff, 0x1a, 0xb2, 0x48, 0x1b, 0x9b,
	0x0f, 0x64, 0xc1, 0xd6, 0xaf, 0xd2, 0x85, 0xc4, 0x4c, 0x2a, 0x17, 0x36, 0xb0, 0xec, 0xec, 0xab,
	0x5f, 0xf6, 0xcf, 0x35, 0x78, 0xeb, 0xda, 0xe3, 0xd0, 0x0a, 0xcc, 0x8a, 0x5c, 0x59, 0xff, 0xca,
	0x33, 0xc3, 0x9b, 0x0d, 0xfb, 0x95, 0x6c, 0xfc, 0x65, 0xbc, 0xc4, 0x37, 0xae, 0xaf, 0x8b, 0x97,
	0xc9, 0xa6, 0x11, 0xf3, 0xf2, 0xff, 0xc7, 0x34, 0x46, 0xe5, 0x9b, 0xd0, 0x91, 0xff, 0x99, 0x6c,
	0x1a, 0x49, 0x59, 0xa4, 0x33, 0x8d, 0xef, 0x0f, 0x9a, 0xc6, 0xb7, 0x52, 0x08, 0x24, 0x31, 0xe9,
	0xab, 0xb3, 0x92, 0x4b, 0x25, 0xe0, 0xc1, 0x37, 0xb6, 0xc9, 0xab, 0xd3, 0x85, 0xbb, 0x50, 0x88,
	0xd3, 0x3c, 0xb6, 0x90, 0x40, 0xd1, 0x80, 0x7e, 0xfa, 0xc6, 0xd6, 0xff, 0x54, 0x83, 0x37, 0xae,
	0x9e, 0xf0, 0x97, 0x27, 0x70, 0xfd, 0x87, 0x22, 0xb4, 0xe6, 0x97, 0x60, 0x91, 0xc3, 0xda, 0xa5,
	0xde, 0xe9, 0xe6, 0x85, 0xb8, 0x4c, 0xa7, 0x91, 0x43, 0x7c, 0xb4, 0x66, 0x12, 0x47, 0xab, 0xfe,
	0x23, 0xb8, 0x3d, 0x91, 0x78, 0xba, 0x35, 0x47, 0xaf, 0x9c, 0x99, 0xf8, 0x95, 0x53, 0xff, 0x8b,
	0x29, 0x58, 0x55, 0x41, 0xef, 0x01, 0x5f, 0xb6, 0x0a, 0x9c, 0xa5, 0x5a, 0x5c, 0x9f, 0xf7, 0xc4,
	0x8b, 0x5e, 0x66, 0xe0, 0x45, 0xef, 0x1e, 0xcc, 0x59, 0x98, 0x91, 0x8e, 0x1f, 0x5c, 0x88, 0xde,
	0xac, 0xe8, 0x2d, 0x44, 0x30, 0x8e, 0xb2, 0x03, 0xd9, 0x90, 0xbc, 0x50, 0x17, 0x84, 0xf7, 0xaf,
	0x88, 0xcc, 0xc7, 0x30, 0xb9, 0x71, 0x48, 0x5e, 0x18, 0x9c, 0x04, 0xbf, 0x27, 0x74, 0x08, 0x93,
	0xb7, 0x9c, 0x19, 0x21, 0xc3, 0xd9, 0x0e, 0x61, 0xe2, 0xde, 0xf2, 0x36, 0xa0, 0x28, 0x3b, 0xd7,
	0xc5, 0xd6, 0x29, 0xee, 0x88, 0x24, 0xc9, 0xac, 0x90, 0x44, 0x59, 0xf5, 0x1c, 0xc8, 0x8e, 0x86,
	0xcd, 0xef, 0xa5, 0x9e, 0x6f, 0xb6, 0x03, 0xff, 0x3c, 0x24, 0x32, 0x47, 0x97, 0x13, 0x7c, 0xcf,
	0x79, 0xfe, 0xa6, 0x00, 0xf2, 0xa9, 0xf9, 0xda, 0x88, 0xc7, 0xef, 0xd9, 0xa1, 0xdf, 0x0b, 0x2c,
	0x19, 0xaf, 0x15, 0x8d, 0x82, 0x80, 0x1d, 0x0a, 0x10, 0xa2, 0xb0, 0x34, 0xb8, 0x18, 0xbf, 0x2b,
	0xae, 0x93, 0x20, 0xb4, 0xee, 0xdb, 0xd7, 0x59, 0xec, 0x66, 0x2f, 0xa4, 0x1e, 0x09, 0xc3, 0x4d,
	0xc7, 0xb7, 0x4e, 0xf7, 0xc5, 0x70, 0x03, 0xb9, 0x09, 0x04, 0x09, 0x8b, 0xbd, 0x61, 0x21, 0xe9,
	0x0d, 0xef, 0xc1, 0x5c, 0xc2, 0xb8, 0xc2, 0xca, 0x9c, 0x94, 0x7f, 0x6c, 0x5d, 0xa1, 0xbe, 0x0e,
	0xd9, 0x43, 0xf2, 0x02, 0x15, 0x21, 0x7f, 0xd4, 0xdc, 0xaa, 0x3f, 0x6d, 0x34, 0xeb, 0x5b, 0xf2,
	0x31, 0xed, 0x69, 0x7d, 0xaf, 0xba, 0x5b, 0x2f, 0x6b, 0x28, 0x07, 0x53, 0xe2, 0x2b, 0xa3, 0x9f,
	0xc3, 0xdd, 0x2b, 0x78, 0x13, 0x6f, 0xd3, 0x0a, 0x6c, 0xb6, 0x39, 0x3c, 0x3e, 0x93, 0xe6, 0xdb,
	0x49, 0xfc, 0x86, 0x8d, 0x1e, 0x42, 0xb9, 0x8f, 0x4b, 0x1c, 0xe2, 0xc6, 0xca, 0x5f, 0x8a, 0xe0,
	0x75, 0x87, 0xb8, 0x0d, 0x5b, 0xff, 0xdb, 0x0c, 0xdc, 0x9e, 0xa8, 0x02, 0xe9, 0xcc, 0xa0, 0x0e,
	0xd3, 0x94, 0x11, 0x37, 0x32, 0xfd, 0xc7, 0x29, 0x34, 0xae, 0xc1, 0x88, 0x6b, 0xc8, 0xd1, 0xdc,
	0x6d, 0x09, 0x2f, 0x2b, 0x23, 0x76, 0xf5, 0x0e, 0x04, 0x1c, 0x24, 0x03, 0x75, 0x64, 0x42, 0x21,
	0x20, 0x5d, 0x3f, 0x60, 0xa6, 0x8d, 0x19, 0x56, 0x47, 0xd1, 0xf7, 0x52, 0xeb, 0x77, 0xd8, 0xdd,
	0xd8, 0xc2, 0x0c, 0x1b, 0x82, 0x94, 0x01, 0x92, 0x24, 0x87, 0xac, 0xae, 0x03, 0xc4, 0x3d, 0xe8,
	0x35, 0xc8, 0x1f, 0xfb, 0x3e, 0xeb, 0x06, 0x3c, 0x42, 0x92, 0xb7, 0xdf, 0x18, 0xa0, 0xff, 0xf1,
	0x34, 0xac, 0x4c, 0x58, 0x10, 0xba, 0x03, 0x10, 0xd9, 0x46, 0x9c, 0x62, 0x53, 0x90, 0x86, 0x2d,
	0x4c, 0x58, 0x75, 0x8b, 0x9b, 0xb5, 0x74, 0x1f, 0x05, 0x05, 0x13, 0xf5, 0x13, 0x55, 0x98, 0x16,
	0x39, 0x6f, 0x75, 0xda, 0x3c, 0xba, 0x24, 0xd8, 0x8c, 0x66, 0x3d, 0x0a, 0x55, 0xc8, 0x29, 0x47,
	0xf2, 0x1b, 0xc1, 0x19, 0x25, 0xe7, 0x4a, 0x4c, 0x69, 0x36, 0xe5, 0x19, 0x25, 0xe7, 0x86, 0x18,
	0x9c, 0x70, 0xa1, 0xd3, 0x93, 0x2a, 0x3f, 0x06, 0xe3, 0x07, 0xbe, 0x38, 0x97, 0xb8, 0x6d, 0x12,
	0xa8, 0xf4, 0xc6, 0xac, 0xb4, 0x61, 0x09, 0x93, 0x29, 0x8d, 0x03, 0x80, 0xc0, 0x72, 0x6d, 0x95,
	0x7d, 0xcb, 0x09, 0x37, 0xf5, 0xee, 0x75, 0xf8, 0x33, 0x6a, 0x7b, 0x5b, 0x22, 0x41, 0x57, 0xf7,
	0x7a, 0xae, 0x91, 0xe7, 0x44, 0x64, 0xbe, 0x4e, 0xf8, 0x29, 0xf5, 0xa6, 0x21, 0x93, 0x3c, 0xb3,
	0x1d, 0x22, 0xeb, 0x62, 0x6e, 0x41, 0xce, 0x21, 0x67, 0x72, 0x27, 0x40, 0xba, 0x30, 0xd1, 0x6e,
	0xd8, 0xe8, 0x18, 0x80, 0x05, 0x7c, 0x26, 0x31, 0xae, 0x20, 0xe4, 0xb4, 0x9d, 0x52, 0x79, 0xe3,
	0xf3, 0x4c, 0x04, 0x8e, 0x2d, 0x4e, 0x4f, 0xec, 0x42, 0x9e, 0x45, 0x9f, 0xe8, 0x3d, 0xb8, 0x29,
	0x1f, 0x30, 0x5c, 0x1a, 0xba, 0x98, 0x59, 0x27, 0xe6, 0x19, 0x09, 0x42, 0xee, 0xb5, 0xe6, 0x84,
	0x8e, 0x2f, 0x89, 0xde, 0x3d, 0xd5, 0xf9, 0x4c, 0xf6, 0xad, 0x7e, 0x07, 0x56, 0x26, 0xd0, 0xe6,
	0x96, 0x12, 0x10, 0x0b, 0x3b, 0x8e, 0x79, 0xec, 0xe0, 0x8e, 0x52, 0x30, 0x90, 0xa0, 0xa7, 0x0e,
	0xee, 0xe8, 0xff, 0x94, 0x19, 0xab, 0x9c, 0x7c, 0x63, 0xd1, 0x4f, 0x62, 0x0f, 0xca, 0xb7, 0xd8,
	0xb4, 0xc9, 0x31, 0xee, 0x39, 0x4c, 0xd9, 0xf8, 0xfa, 0x15, 0xeb, 0xe7, 0x24, 0xb6, 0xe4, 0x88,
	0x9d, 0x1b, 0x7d, 0xb7, 0x99, 0x80, 0x22, 0x13, 0xd0, 0x00, 0x7d, 0xb7, 0x17, 0x52, 0x4b, 0x3d,
	0x1c, 0x5e, 0xa5, 0x85, 0x7b, 0x1c, 0x37, 0xc1, 0xec, 0xce, 0x0d, 0xa3, 0x9c, 0x98, 0x42, 0x74,
	0xa3, 0x1f, 0xc2, 0xe2, 0xc0, 0x04, 0xa1, 0x08, 0x24, 0x94, 0xa5, 0x5c, 0xf5, 0x96, 0x20, 0xa3,
	0x0e, 0x45, 0x7b, 0x21, 0x41, 0x5b, 0x76, 0x6c, 0x96, 0x62, 0xdb, 0xe4, 0xc4, 0xf5, 0x9f, 0xc2,
	0xca, 0x36, 0x61, 0x35, 0xb1, 0x0d, 0xec, 0x00, 0x77, 0xc8, 0x01, 0x0e, 0xb0, 0x1b, 0xbe, 0xfc,
	0x35, 0x04, 0xdd, 0x87, 0xd2, 0x09, 0x3e, 0x23, 0xa6, 0xe5, 0xf3, 0x89, 0x19, 0xb1, 0x95, 0xc7,
	0x2b, 0x72, 0x68, 0x2d, 0x02, 0xea, 0xff, 0x92, 0x81, 0xca, 0x78, 0x0e, 0x52, 0x3f, 0x3d, 0x62,
	0x27, 0x20, 0xd8, 0xbe, 0x30, 0xa5, 0x52, 0xc9, 0x67, 0x86, 0x9c, 0x51, 0x52, 0x60, 0x35, 0x03,
	0xfa, 0x0d, 0x40, 0x1d, 0xdf, 0x4f, 0xe4, 0xf1, 0x18, 0xee, 0xc8, 0x6b, 0x46, 0xe1, 0xc9, 0x37,
	0x26, 0x49, 0x37, 0x4a, 0xa1, 0xb5, 0x70, 0xc7, 0x28, 0xf3, 0xe1, 0x09, 0x40, 0x88, 0xf6, 0x61,
	0xa1, 0x8d, 0x87, 0x29, 0x4e, 0x5d, 0x9f, 0xe2, 0x7c, 0x1b, 0x0f, 0x12, 0x8c, 0x2a, 0x0d, 0xa6,
	0xd3, 0x56, 0x1a, 0xe8, 0xdf, 0x86, 0x42, 0x82, 0x52, 0x22, 0xbf, 0x9e, 0x17, 0xf9, 0xf5, 0x0a,
	0xcc, 0x5a, 0xbe, 0xc7, 0x22, 0xe9, 0xe4, 0x8d, 0xa8, 0xa9, 0x9f, 0xc4, 0x49, 0xa0, 0x48, 0x52,
	0xfd, 0x9c, 0xab, 0x34, 0x40, 0x95, 0x73, 0x9d, 0x48, 0xe2, 0xba, 0x3b, 0xfe, 0xbb, 0x59, 0x58,
	0x52, 0x53, 0xb4, 0xfc, 0xfe, 0x93, 0xe5, 0xcb, 0x2b, 0x5c, 0x95, 0x33, 0x26, 0x77, 0x5e, 0xda,
	0xca, 0x95, 0xd9, 0x7e, 0xc5, 0x85, 0x11, 0x8d, 0x13, 0x8f, 0x43, 0x38, 0xe0, 0x57, 0xc2, 0xc4,
	0x2b, 0xa2, 0x84, 0x1c, 0x51, 0x1b, 0x7d, 0xa8, 0x82, 0xd0, 0xe9, 0xcb, 0x73, 0x7b, 0x89, 0x0d,
	0x48, 0x04, 0x9f, 0x3b, 0x50, 0x1c, 0x54, 0x90, 0x99, 0xeb, 0x2b, 0xc8, 0x5c, 0x37, 0xa9, 0x1d,
	0x83, 0xc7, 0xef, 0xec, 0xf0, 0xf1, 0xbb, 0x0c, 0x33, 0x21, 0x11, 0x81, 0xbd, 0x7c, 0x3b, 0x98,
	0x0e, 0x09, 0x8f, 0xeb, 0x57, 0x60, 0xb6, 0xeb, 0xcb, 0x80, 0x5f, 0x1e, 0x21, 0x33, 0xbc, 0xd9,
	0xb0, 0xf5, 0x4d, 0x58, 0x1e, 0xb3, 0x1d, 0xe9, 0xea, 0x0e, 0x7e, 0x4d, 0x54, 0x0e, 0x18, 0x96,
	0x6b, 0x7f, 0x85, 0x0d, 0xd5, 0x3f, 0x13, 0xa5, 0x39, 0x83, 0xa3, 0xbf, 0xa6, 0xd2, 0x1c, 0xfd,
	0x33, 0x58, 0x35, 0xc8, 0xf3, 0x1e, 0x09, 0xf9, 0xea, 0xcf, 0x29, 0xb3, 0x4e, 0xbe, 0x8a, 0x4a,
	0x0e, 0x6e, 0x49, 0x66, 0xcc, 0x96, 0x8c, 0x49, 0x82, 0xea, 0x3b, 0x70, 0x7b, 0xe2, 0xdc, 0xe9,
	0xe4, 0xff, 0x3d, 0x58, 0xda, 0x26, 0xac, 0x19, 0x97, 0x2c, 0xb6, 0x68, 0x37, 0xcd, 0x0e, 0xfc,
	0x42, 0x83, 0xe5, 0x31, 0x04, 0xd2, 0x16, 0xad, 0x4d, 0x31, 0xda, 0x8d, 0xae, 0xc9, 0x0f, 0x26,
	0x6d, 0xc2, 0xd0, 0x24, 0x62, 0x0c, 0x77, 0xdf, 0xa1, 0x90, 0x80, 0x49, 0x79, 0x34, 0x74, 0x86,
	0x1d, 0x25, 0xaa, 0x92, 0x04, 0x37, 0x14, 0x54, 0xff, 0x7b, 0x0d, 0x4a, 0x83, 0x14, 0x84, 0x74,
	0x69, 0xd7, 0xec, 0x3b, 0xba, 0x69, 0x46, 0xbb, 0x0d, 0x7b, 0x34, 0x4d, 0xa0, 0x0d, 0xa6, 0x09,
	0x26, 0xa5, 0xa6, 0xeb, 0x90, 0xe3, 0xe4, 0x84, 0xa5, 0x4f, 0x09, 0x4b, 0x5f, 0xbf, 0xde, 0x52,
	0xd4, 0xe3, 0xa7, 0xfc, 0x48, 0xfa, 0xc9, 0xe9, 0x41, 0x57, 0xfb, 0x39, 0xdc, 0xda, 0xc3, 0xc1,
	0xe9, 0xb0, 0xb0, 0x71, 0xaa, 0xe0, 0xff, 0x03, 0xc8, 0x32, 0xda, 0x55, 0x0a, 0x7f, 0x5d, 0x59,
	0xf3, 0x21, 0xfa, 0x36, 0xac, 0x4e, 0x9a, 0x3e, 0x9d, 0xd2, 0xfd, 0xa1, 0x36, 0x14, 0x62, 0x39,
	0x94, 0x78, 0xac, 0xe6, 0x7b, 0xc7, 0xb4, 0x93, 0x66, 0x29, 0xf7, 0x60, 0x4e, 0x5d, 0x18, 0x07,
	0xe2, 0x05, 0x05, 0x13, 0xb7, 0xee, 0xf1, 0xd1, 0x78, 0x76, 0x7c, 0x34, 0xae, 0xff, 0x5b, 0x06,
	0x5e, 0x9b, 0xcc, 0x58, 0x3a, 0xa5, 0x7e, 0x0c, 0x4b, 0xee, 0xa7, 0xe6, 0x09, 0xb5, 0x89, 0xe9,
	0xf9, 0x67, 0xd4, 0x22, 0x66, 0xa7, 0x47, 0xd5, 0xa3, 0x48, 0xce, 0x58, 0x70, 0x3f, 0xdd, 0xa1,
	0x36, 0x69, 0x8a, 0x9e, 0x6d, 0xde, 0xa1, 0x5e, 0xaf, 0x2c, 0xc7, 0x0f, 0x49, 0x54, 0xeb, 0x47,
	0xc3, 0x1a, 0x6f, 0xa2, 0xf7, 0x61, 0x85, 0x86, 0x26, 0x39, 0x3e, 0x26, 0x16, 0xa3, 0x67, 0x7c,
	0xfa, 0x4e, 0xcf, 0x91, 0xcf, 0x45, 0x53, 0x02, 0x73, 0x99, 0x86, 0xf5, 0xa8, 0xd7, 0xe8, 0x77,
	0xa2, 0x77, 0x61, 0x99, 0x86, 0x92, 0x87, 0xbe, 0x46, 0x8b, 0xc7, 0xab, 0x69, 0x31, 0x0a, 0xd1,
	0x90, 0x33, 0xd1, 0xf7, 0x1d, 0xd8, 0x3b, 0xe5, 0x81, 0xb2, 0xfb, 0xa9, 0xc9, 0x35, 0x2e, 0xf0,
	0x1d, 0x13, 0xf7, 0x6c, 0xca, 0x44, 0x24, 0x93, 0x33, 0x4a, 0xee, 0xa7, 0x35, 0x09, 0xae, 0x72,
	0x28, 0x0f, 0xbf, 0x07, 0x88, 0x9a, 0xbd, 0x80, 0xaa, 0x3c, 0xc7, 0x7c, 0x98, 0x20, 0x79, 0x14,
	0x50, 0xfd, 0x6f, 0x34, 0x78, 0x7d, 0x9b, 0x88, 0x47, 0x5c, 0x92, 0xf0, 0xd5, 0x96, 0x1f, 0xd8,
	0x69, 0x13, 0x40, 0x8d, 0x64, 0xce, 0x51, 0x6a, 0xf1, 0xdb, 0xd7, 0x89, 0x4d, 0x46, 0xd3, 0x8b,
	0x13, 0xd2, 0xba, 0x65, 0xc8, 0xc6, 0x87, 0x38, 0xff, 0xd4, 0xff, 0x5d, 0x83, 0xbb, 0x97, 0x72,
	0x9f, 0x4e, 0x33, 0x5e, 0xe1, 0x0a, 0xf6, 0xa0, 0x24, 0xde, 0xb0, 0x6d, 0x55, 0x70, 0x1b, 0xdd,
	0x47, 0x1f, 0x5c, 0x75, 0x90, 0x49, 0xee, 0x8d, 0xa2, 0x1c, 0x2d, 0x5b, 0xa1, 0xfe, 0xcf, 0x1a,
	0x94, 0x06, 0x31, 0x86, 0x9d, 0xa1, 0x36, 0xe2, 0x0c, 0xbf, 0x91, 0xa8, 0x9d, 0x4b, 0x58, 0x61,
	0x3f, 0x19, 0x24, 0xcc, 0xb0, 0x04, 0x99, 0x76, 0x47, 0x99, 0x5d, 0xa6, 0xdd, 0xe1, 0x5e, 0xbb,
	0x8b, 0xc3, 0x90, 0xd8, 0xa6, 0x75, 0x82, 0xbb, 0x8c, 0x04, 0xa1, 0xaa, 0x4d, 0x2b, 0x49, 0x70,
	0x4d, 0x41, 0xd1, 0x03, 0x7e, 0x3b, 0x77, 0x22, 0x2c, 0xf1, 0xf2, 0xab, 0x0a, 0xb0, 0xb0, 0xe3,
	0x28, 0xac, 0x66, 0xcf, 0xe5, 0x9b, 0xc6, 0xbd, 0x9a, 0x8c, 0xb9, 0x85, 0xb7, 0xfa, 0x1c, 0xd6,
	0x26, 0xec, 0x99, 0x4c, 0xab, 0xbe, 0x54, 0xe2, 0x78, 0x58, 0x08, 0x4a, 0x67, 0xb2, 0xb1, 0xce,
	0xfc, 0x95, 0x06, 0xf7, 0xae, 0x98, 0x3f, 0x9d, 0xd6, 0x6c, 0xc1, 0x8c, 0x2d, 0x06, 0x5e, 0xa5,
	0x32, 0x63, 0x27, 0x53, 0x63, 0x45, 0x91, 0x53, 0x68, 0x9e, 0xd1, 0x90, 0xb6, 0x9d, 0xc8, 0xcd,
	0xe4, 0x69, 0xf8, 0x4c, 0x02, 0xf4, 0x7f, 0xcc, 0xc0, 0xd2, 0xb8, 0xf1, 0x57, 0xab, 0xc1, 0x0a,
	0xcc, 0x8a, 0xa7, 0xec, 0xbe, 0x78, 0x66, 0x78, 0x73, 0xf2, 0x61, 0xc9, 0x4f, 0x39, 0xb9, 0x7d,
	0xca, 0xd2, 0xa2, 0x26, 0xda, 0x82, 0x59, 0x59, 0x97, 0xf1, 0xae, 0x0a, 0x63, 0xd6, 0xaf, 0x63,
	0x1c, 0xb2, 0x6a, 0xc3, 0x88, 0x86, 0xc6, 0x54, 0x9e, 0xa8, 0x82, 0xf2, 0xaf, 0x40, 0xe5, 0x09,
	0xe7, 0x32, 0xec, 0xb9, 0x2e, 0x0e, 0x2e, 0x94, 0x67, 0x8b, 0x9a, 0xe2, 0xe2, 0x16, 0x10, 0xcc,
	0x88, 0x6d, 0x62, 0x26, 0x2e, 0xcc, 0xfc, 0xe2, 0x26, 0x21, 0x55, 0xa6, 0xff, 0x08, 0xd0, 0x28,
	0xdd, 0x48, 0x4d, 0xb4, 0xbe, 0x9a, 0xf0, 0x09, 0xb0, 0x25, 0x9d, 0x90, 0x0a, 0x8a, 0x54, 0x13,
	0xad, 0x42, 0xce, 0xa3, 0xd6, 0xa9, 0x30, 0x29, 0x55, 0xc4, 0x17, 0xb5, 0x75, 0x03, 0x96, 0xc6,
	0x39, 0x06, 0x74, 0x1b, 0xf2, 0xb2, 0xa6, 0x1e, 0x77, 0xa2, 0xe0, 0x2b, 0x27, 0x2a, 0xe4, 0x71,
	0x87, 0x70, 0x8e, 0x45, 0x67, 0x3c, 0x5b, 0xd1, 0x10, 0xe8, 0x22, 0xf9, 0xa4, 0xff, 0x89, 0x06,
	0xaf, 0x25, 0xea, 0xdc, 0x95, 0x6d, 0x1d, 0xca, 0xe5, 0xa6, 0xcc, 0xd0, 0x8f, 0xd7, 0x84, 0xc4,
	0x96, 0x67, 0x07, 0xb7, 0x7c, 0x0d, 0x0a, 0x51, 0xa0, 0x72, 0xd4, 0x77, 0xbd, 0x49, 0x90, 0xfe,
	0x97, 0x1a, 0xdc, 0xb9, 0x84, 0xbb, 0x74, 0xa6, 0xf4, 0x08, 0x16, 0x22, 0x87, 0x72, 0x1c, 0xe0,
	0x0e, 0x8f, 0x60, 0xe4, 0xe5, 0x53, 0xde, 0x09, 0x78, 0xc7, 0xd3, 0x08, 0x9e, 0x54, 0x81, 0xec,
	0x88, 0x0a, 0x24, 0x6c, 0x69, 0x6a, 0xd8, 0x96, 0xbe, 0xd4, 0xe0, 0x8d, 0xc3, 0xf1, 0x1e, 0x40,
	0xa0, 0x50, 0x87, 0xb2, 0x57, 0x23, 0xd8, 0x7b, 0xd0, 0x8f, 0xf3, 0x12, 0xb5, 0xc0, 0x49, 0xf9,
	0x5d, 0xc5, 0xeb, 0x73, 0xb8, 0x7f, 0x0d, 0x56, 0xd3, 0x49, 0x79, 0x70, 0xca, 0xcc, 0xf0, 0x94,
	0xad, 0x7e, 0xec, 0xb6, 0x47, 0x3f, 0xa2, 0x5e, 0xa7, 0x85, 0xdb, 0xaf, 0xe0, 0x0d, 0xeb, 0x10,
	0x16, 0x22, 0xfe, 0xeb, 0x2f, 0x58, 0x80, 0x07, 0x7f, 0x2b, 0xa5, 0x25, 0x6b, 0xc0, 0xca, 0x90,
	0x75, 0xa9, 0x17, 0xd5, 0x6a, 0xb8, 0x34, 0xf9, 0x23, 0xbb, 0x6c, 0xb2, 0xd8, 0x4d, 0xff, 0x3b,
	0x0d, 0x0a, 0x9c, 0xd1, 0xa8, 0xc8, 0x25, 0x9e, 0x5b, 0x1b, 0x57, 0x9a, 0x92, 0x49, 0x94, 0xa6,
	0xf4, 0xeb, 0x12, 0xb3, 0xc9, 0xba, 0xc4, 0xe8, 0x87, 0x15, 0x53, 0x89, 0x1f, 0x56, 0xa8, 0x13,
	0x6c, 0xba, 0x7f, 0x82, 0xa1, 0x1d, 0x00, 0xc2, 0xd7, 0x20, 0xd3, 0xb0, 0x33, 0x97, 0xa7, 0xf1,
	0x46, 0x56, 0x6d, 0xe4, 0x49, 0xf4, 0xa9, 0x7f, 0xa1, 0x89, 0x30, 0x6f, 0x48, 0xd8, 0x69, 0x6b,
	0x2a, 0x12, 0x55, 0x91, 0x99, 0xcb, 0x53, 0x10, 0x09, 0x61, 0xc5, 0x15, 0x91, 0xfa, 0x0f, 0x00,
	0xed, 0xf8, 0x2e, 0xe1, 0xce, 0x68, 0x93, 0x76, 0x5a, 0xfe, 0x71, 0x2f, 0x65, 0xcd, 0xb1, 0x70,
	0x5f, 0xa2, 0x60, 0x31, 0x13, 0xfb, 0xb6, 0x16, 0x75, 0x89, 0xfe, 0xaf, 0x1a, 0x2c, 0x8e, 0xd0,
	0x4e, 0xb7, 0xbe, 0xdb, 0x90, 0x0f, 0x7b, 0x6d, 0x33, 0x59, 0x45, 0x9a, 0x0b, 0x7b, 0xed, 0x96,
	0xd8, 0xb0, 0xbb, 0x50, 0x08, 0x7c, 0xc7, 0xa1, 0x5e, 0xc7, 0xa4, 0x6e, 0x47, 0xa9, 0x07, 0x28,
	0x50, 0xc3, 0xed, 0x70, 0xdb, 0x3c, 0xc1, 0xa1, 0xe9, 0xa9, 0x77, 0x85, 0x9c, 0x31, 0x73, 0x82,
	0xc3, 0x26, 0x39, 0x47, 0xf7, 0xa1, 0x14, 0xff, 0x3a, 0x46, 0x0c, 0x96, 0x3b, 0x5c, 0x8c, 0xa1,
	0x7c, 0x7c, 0x5f, 0x4f, 0x66, 0x12, 0x7a, 0xc2, 0xa3, 0xf3, 0x68, 0x55, 0x06, 0xed, 0x9c, 0xb0,
	0x94, 0x32, 0xd3, 0x2d, 0xc8, 0xf1, 0x21, 0xe2, 0xed, 0x45, 0x95, 0x26, 0x69, 0x71, 0x69, 0xd2,
	0xa5, 0x2b, 0x8e, 0x9e, 0x70, 0xb3, 0x89, 0x1f, 0xaa, 0xf6, 0x99, 0x9c, 0x4a, 0x32, 0xf9, 0x02,
	0x96, 0xc7, 0x30, 0x99, 0x4e, 0xf8, 0xef, 0xc1, 0x14, 0x65, 0xc4, 0x55, 0x7a, 0xb5, 0x36, 0x49,
	0xaf, 0xa2, 0xc5, 0x18, 0x02, 0x5b, 0xff, 0x6d, 0xb8, 0xdb, 0x08, 0x8f, 0x42, 0x12, 0xec, 0xe0,
	0x30, 0xd2, 0xff, 0xa7, 0x01, 0x21, 0x35, 0xbf, 0xd7, 0xf5, 0xbd, 0x54, 0xb9, 0x68, 0x75, 0x32,
	0x67, 0xe2, 0x93, 0x79, 0xe8, 0xc6, 0x93, 0x1d, 0xbe, 0xf1, 0xe8, 0x9f, 0xc1, 0xda, 0xe5, 0xb3,
	0xa7, 0x3d, 0x94, 0x10, 0x8f, 0xd5, 0xb0, 0x6d, 0x1e, 0x07, 0x84, 0xf0, 0x53, 0xba, 0xab, 0x7c,
	0x49, 0xce, 0x98, 0xa7, 0xe1, 0x0e, 0xb6, 0x63, 0xe2, 0xfa, 0x8f, 0xc5, 0x59, 0xad, 0x1e, 0x04,
	0x86, 0x7e, 0xe9, 0xf6, 0x0a, 0xbc, 0xe8, 0xef, 0xc8, 0xd3, 0x76, 0x12, 0xfd, 0xaf, 0x2b, 0xc5,
	0xf6, 0x89, 0xf8, 0xad, 0xc2, 0x78, 0x2e, 0x5e, 0x76, 0x89, 0x3f, 0x93, 0xbf, 0x52, 0x18, 0x4b,
	0xfc, 0xeb, 0x5a, 0x5f, 0x0f, 0x6e, 0x8e, 0x2f, 0xb9, 0xe4, 0xca, 0x17, 0x57, 0x6e, 0x46, 0xe9,
	0x29, 0x88, 0x40, 0x8d, 0x49, 0xf5, 0xee, 0x5c, 0x8b, 0x03, 0xaa, 0x6c, 0x96, 0x7f, 0x8e, 0x2b,
	0x97, 0x5c, 0x7f, 0x0e, 0xf3, 0x43, 0x55, 0xa4, 0xe8, 0x75, 0x58, 0xdd, 0xae, 0xee, 0xd5, 0xcd,
	0x7d, 0xa3, 0x51, 0x6f, 0xb6, 0xaa, 0xad, 0xc6, 0x7e, 0xd3, 0x4c, 0xbe, 0xd1, 0xdf, 0x85, 0xdb,
	0x23, 0xfd, 0x3b, 0xfb, 0x46, 0xe3, 0x93, 0xfd, 0x66, 0xab, 0xba, 0x5b, 0xd6, 0xd0, 0x1d, 0xb8,
	0x35, 0x82, 0xf0, 0xac, 0x6e, 0xb4, 0x1a, 0xb5, 0xea, 0x6e, 0x39, 0xb3, 0xfe, 0x85, 0x06, 0x0b,
	0x23, 0x3f, 0x5a, 0x41, 0xf3, 0x50, 0x38, 0x6c, 0x55, 0x5b, 0x47, 0x87, 0x66, 0x73, 0xbf, 0x59,
	0x2f, 0xdf, 0xe0, 0x80, 0xfd, 0x8f, 0x9b, 0x75, 0xc3, 0xdc, 0xad, 0x57, 0x9f, 0xd5, 0xcb, 0x1a,
	0x2a, 0x01, 0xb4, 0xaa, 0x9b, 0x66, 0x6d, 0xa7, 0xda, 0xdc, 0xae, 0x97, 0x33, 0x08, 0x41, 0xe9,
	0xb0, 0x5e, 0x6d, 0x99, 0xd5, 0x67, 0xd5, 0xc6, 0x6e, 0x75, 0x73, 0xb7, 0x5e, 0xce, 0xa2, 0x25,
	0x28, 0x0b, 0xd8, 0x51, 0x33, 0x86, 0x4e, 0x71, 0x4c, 0x3e, 0xaa, 0x59, 0xdf, 0x35, 0x77, 0xf7,
	0x6b, 0xdf, 0xaf, 0x6f, 0x95, 0xa7, 0xd7, 0xf7, 0x60, 0x71, 0xcc, 0xcf, 0x2e, 0x50, 0x11, 0xf2,
	0xcd, 0xfd, 0x96, 0xf9, 0x74, 0xff, 0xa8, 0xb9, 0x25, 0x99, 0xe0, 0x4d, 0xa3, 0x7e, 0x78, 0xb0,
	0xdf, 0xdc, 0x2a, 0x6b, 0x08, 0x60, 0xa6, 0x5a, 0xab, 0xd5, 0x0f, 0x5a, 0xe5, 0x0c, 0xff, 0x36,
	0xea, 0x1f, 0xd5, 0x6b, 0xad, 0x72, 0x76, 0xfd, 0x67, 0x03, 0xf5, 0xaf, 0x22, 0x65, 0xcf, 0xe5,
	0x30, 0x04, 0x1a, 0x90, 0xe3, 0x98, 0xee, 0xc3, 0xa3, 0xcd, 0xc3, 0x9a, 0xd1, 0xd8, 0xe4, 0xcb,
	0x5d, 0x81, 0xc5, 0xe1, 0xee, 0x66, 0xfd, 0xe3, 0x72, 0x66, 0x5c, 0xc7, 0xce, 0x3e, 0xe7, 0xe1,
	0xfd, 0x64, 0x47, 0xfb, 0xda, 0x3f, 0x55, 0x5e, 0x77, 0xe3, 0x7b, 0x52, 0xbf, 0x10, 0x96, 0xef,
	0xf2, 0x08, 0x70, 0x80, 0xfd, 0x5b, 0xb0, 0x3c, 0x8a, 0xc0, 0x39, 0xd4, 0xd0, 0x6b, 0x50, 0x19,
	0xed, 0x3a, 0xa8, 0xee, 0xfe, 0xa0, 0xbe, 0x55, 0xce, 0xac, 0x7f, 0xa9, 0x25, 0x1e, 0x6d, 0x07,
	0x4b, 0xec, 0xd0, 0x7d, 0xb8, 0x37, 0xa1, 0x6b, 0x58, 0x05, 0x27, 0xa1, 0x55, 0x77, 0xb9, 0x0a,
	0x5e, 0x82, 0x20, 0x85, 0x78, 0xc9, 0x44, 0xf1, 0x26, 0x64, 0xd7, 0x3f, 0x80, 0xe5, 0x81, 0xca,
	0x13, 0xc7, 0xb7, 0x4e, 0xeb, 0x5e, 0xcf, 0x45, 0x73, 0x90, 0x33, 0x7c, 0xdf, 0xe5, 0x02, 0x2e,
	0xdf, 0x40, 0x0b, 0x50, 0xe4, 0x56, 0x54, 0xf3, 0x3d, 0x9b, 0x72, 0x1b, 0x2a, 0x6b, 0xeb, 0x9f,
	0x0f, 0x86, 0x65, 0x75, 0x87, 0xb8, 0x62, 0x60, 0x01, 0x66, 0x9b, 0xfe, 0x2e, 0x75, 0x29, 0x2b,
	0xdf, 0xe0, 0xa6, 0x36, 0xf0, 0x74, 0xaf, 0x28, 0xf6, 0x37, 0x69, 0x42, 0xff, 0x96, 0xdf, 0x6b,
	0x3b, 0xa4, 0x9c, 0xe1, 0xc4, 0x3e, 0xc6, 0x94, 0x51, 0xaf, 0x53, 0xce, 0xf2, 0xc6, 0x21, 0xc3,
	0x01, 0x23, 0x76, 0x79, 0x6a, 0x3d, 0x84, 0x5b, 0x13, 0x8b, 0x02, 0xb8, 0x3d, 0xf4, 0x01, 0xa6,
	0xd2, 0x8d, 0x7b, 0x70, 0x27, 0x86, 0x6d, 0x63, 0xaf, 0x73, 0xd4, 0xfd, 0x98, 0xb2, 0x13, 0x7e,
	0x9a, 0x8b, 0x9f, 0x0f, 0x2a, 0xa1, 0xf6, 0x51, 0x6a, 0x27, 0x98, 0x0d, 0x22, 0x64, 0xd6, 0x2d,
	0x98, 0x1f, 0x7a, 0xb6, 0xe2, 0x4a, 0x3e, 0x04, 0x1a, 0xd8, 0xc8, 0x0a, 0x2c, 0x0d, 0x77, 0x6f,
	0xef, 0xef, 0x6f, 0x49, 0xf5, 0x1f, 0xee, 0xd9, 0xac, 0x72, 0xf5, 0xf9, 0x7d, 0x0d, 0xd0, 0x68,
	0xca, 0x1c, 0xad, 0xc1, 0x6b, 0xa3, 0xd0, 0x81, 0xb9, 0x5e, 0x87, 0xd5, 0x31, 0x18, 0xcd, 0xfa,
	0xc7, 0x66, 0xab, 0xba, 0x59, 0xd6, 0x26, 0x50, 0x88, 0xb5, 0x21, 0x83, 0x56, 0xe1, 0xe6, 0x78,
	0x0a, 0xe5, 0xec, 0xe6, 0x2e, 0x54, 0x2c, 0xdf, 0xdd, 0xb8, 0xa0, 0x17, 0x7e, 0x4f, 0xb8, 0x7d,
	0xdf, 0x26, 0x8e, 0xfc, 0x3f, 0x24, 0x9f, 0x7c, 0xb3, 0xe3, 0x3b, 0xd8, 0xeb, 0x6c, 0x7c, 0xeb,
	0x09, 0x63, 0x1b, 0x96, 0xef, 0x3e, 0x16, 0x60, 0xcb, 0x77, 0x1e, 0xe3, 0x6e, 0xf7, 0xf1, 0x98,
	0xff, 0x8a, 0xd2, 0x9e, 0x11, 0x18, 0xbf, 0xf2, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x61, 0x9c,
	0x47, 0x7e, 0x33, 0x45, 0x00, 0x00,
}
