// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse_channel_hot_game_logic/muse_channel_hot_game_logic.proto

package muse_channel_hot_game_logic // import "golang.52tt.com/protocol/app/muse-channel-hot-game-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MuseChannelHotGameJoinStatus int32

const (
	MuseChannelHotGameJoinStatus_MUSE_CHANNEL_HOT_GAME_JOIN_STATUS_CANCEL_UNSPECIFIED MuseChannelHotGameJoinStatus = 0
	MuseChannelHotGameJoinStatus_MUSE_CHANNEL_HOT_GAME_JOIN_STATUS_JOIN               MuseChannelHotGameJoinStatus = 1
)

var MuseChannelHotGameJoinStatus_name = map[int32]string{
	0: "MUSE_CHANNEL_HOT_GAME_JOIN_STATUS_CANCEL_UNSPECIFIED",
	1: "MUSE_CHANNEL_HOT_GAME_JOIN_STATUS_JOIN",
}
var MuseChannelHotGameJoinStatus_value = map[string]int32{
	"MUSE_CHANNEL_HOT_GAME_JOIN_STATUS_CANCEL_UNSPECIFIED": 0,
	"MUSE_CHANNEL_HOT_GAME_JOIN_STATUS_JOIN":               1,
}

func (x MuseChannelHotGameJoinStatus) String() string {
	return proto.EnumName(MuseChannelHotGameJoinStatus_name, int32(x))
}
func (MuseChannelHotGameJoinStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_hot_game_logic_888c1f097190fa82, []int{0}
}

type GetMuseChannelHotGameRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMuseChannelHotGameRequest) Reset()         { *m = GetMuseChannelHotGameRequest{} }
func (m *GetMuseChannelHotGameRequest) String() string { return proto.CompactTextString(m) }
func (*GetMuseChannelHotGameRequest) ProtoMessage()    {}
func (*GetMuseChannelHotGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_hot_game_logic_888c1f097190fa82, []int{0}
}
func (m *GetMuseChannelHotGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseChannelHotGameRequest.Unmarshal(m, b)
}
func (m *GetMuseChannelHotGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseChannelHotGameRequest.Marshal(b, m, deterministic)
}
func (dst *GetMuseChannelHotGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseChannelHotGameRequest.Merge(dst, src)
}
func (m *GetMuseChannelHotGameRequest) XXX_Size() int {
	return xxx_messageInfo_GetMuseChannelHotGameRequest.Size(m)
}
func (m *GetMuseChannelHotGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseChannelHotGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseChannelHotGameRequest proto.InternalMessageInfo

func (m *GetMuseChannelHotGameRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMuseChannelHotGameRequest) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetMuseChannelHotGameResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	HotGameId            string        `protobuf:"bytes,2,opt,name=hot_game_id,json=hotGameId,proto3" json:"hot_game_id,omitempty"`
	Title                string        `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string        `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetMuseChannelHotGameResponse) Reset()         { *m = GetMuseChannelHotGameResponse{} }
func (m *GetMuseChannelHotGameResponse) String() string { return proto.CompactTextString(m) }
func (*GetMuseChannelHotGameResponse) ProtoMessage()    {}
func (*GetMuseChannelHotGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_hot_game_logic_888c1f097190fa82, []int{1}
}
func (m *GetMuseChannelHotGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseChannelHotGameResponse.Unmarshal(m, b)
}
func (m *GetMuseChannelHotGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseChannelHotGameResponse.Marshal(b, m, deterministic)
}
func (dst *GetMuseChannelHotGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseChannelHotGameResponse.Merge(dst, src)
}
func (m *GetMuseChannelHotGameResponse) XXX_Size() int {
	return xxx_messageInfo_GetMuseChannelHotGameResponse.Size(m)
}
func (m *GetMuseChannelHotGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseChannelHotGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseChannelHotGameResponse proto.InternalMessageInfo

func (m *GetMuseChannelHotGameResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMuseChannelHotGameResponse) GetHotGameId() string {
	if m != nil {
		return m.HotGameId
	}
	return ""
}

func (m *GetMuseChannelHotGameResponse) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMuseChannelHotGameResponse) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type GetMuseChannelHotGameFloatRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMuseChannelHotGameFloatRequest) Reset()         { *m = GetMuseChannelHotGameFloatRequest{} }
func (m *GetMuseChannelHotGameFloatRequest) String() string { return proto.CompactTextString(m) }
func (*GetMuseChannelHotGameFloatRequest) ProtoMessage()    {}
func (*GetMuseChannelHotGameFloatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_hot_game_logic_888c1f097190fa82, []int{2}
}
func (m *GetMuseChannelHotGameFloatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseChannelHotGameFloatRequest.Unmarshal(m, b)
}
func (m *GetMuseChannelHotGameFloatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseChannelHotGameFloatRequest.Marshal(b, m, deterministic)
}
func (dst *GetMuseChannelHotGameFloatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseChannelHotGameFloatRequest.Merge(dst, src)
}
func (m *GetMuseChannelHotGameFloatRequest) XXX_Size() int {
	return xxx_messageInfo_GetMuseChannelHotGameFloatRequest.Size(m)
}
func (m *GetMuseChannelHotGameFloatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseChannelHotGameFloatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseChannelHotGameFloatRequest proto.InternalMessageInfo

func (m *GetMuseChannelHotGameFloatRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetMuseChannelHotGameFloatRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMuseChannelHotGameFloatResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Title                string        `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Icon                 string        `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Number               uint32        `protobuf:"varint,4,opt,name=number,proto3" json:"number,omitempty"`
	Desc                 string        `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	HotGameId            string        `protobuf:"bytes,6,opt,name=hot_game_id,json=hotGameId,proto3" json:"hot_game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetMuseChannelHotGameFloatResponse) Reset()         { *m = GetMuseChannelHotGameFloatResponse{} }
func (m *GetMuseChannelHotGameFloatResponse) String() string { return proto.CompactTextString(m) }
func (*GetMuseChannelHotGameFloatResponse) ProtoMessage()    {}
func (*GetMuseChannelHotGameFloatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_hot_game_logic_888c1f097190fa82, []int{3}
}
func (m *GetMuseChannelHotGameFloatResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMuseChannelHotGameFloatResponse.Unmarshal(m, b)
}
func (m *GetMuseChannelHotGameFloatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMuseChannelHotGameFloatResponse.Marshal(b, m, deterministic)
}
func (dst *GetMuseChannelHotGameFloatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMuseChannelHotGameFloatResponse.Merge(dst, src)
}
func (m *GetMuseChannelHotGameFloatResponse) XXX_Size() int {
	return xxx_messageInfo_GetMuseChannelHotGameFloatResponse.Size(m)
}
func (m *GetMuseChannelHotGameFloatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMuseChannelHotGameFloatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMuseChannelHotGameFloatResponse proto.InternalMessageInfo

func (m *GetMuseChannelHotGameFloatResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetMuseChannelHotGameFloatResponse) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetMuseChannelHotGameFloatResponse) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *GetMuseChannelHotGameFloatResponse) GetNumber() uint32 {
	if m != nil {
		return m.Number
	}
	return 0
}

func (m *GetMuseChannelHotGameFloatResponse) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GetMuseChannelHotGameFloatResponse) GetHotGameId() string {
	if m != nil {
		return m.HotGameId
	}
	return ""
}

type SetMuseChannelHotGameJoinStatusRequest struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	HotGameId            string       `protobuf:"bytes,3,opt,name=hot_game_id,json=hotGameId,proto3" json:"hot_game_id,omitempty"`
	JoinStatus           uint32       `protobuf:"varint,4,opt,name=join_status,json=joinStatus,proto3" json:"join_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetMuseChannelHotGameJoinStatusRequest) Reset() {
	*m = SetMuseChannelHotGameJoinStatusRequest{}
}
func (m *SetMuseChannelHotGameJoinStatusRequest) String() string { return proto.CompactTextString(m) }
func (*SetMuseChannelHotGameJoinStatusRequest) ProtoMessage()    {}
func (*SetMuseChannelHotGameJoinStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_hot_game_logic_888c1f097190fa82, []int{4}
}
func (m *SetMuseChannelHotGameJoinStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMuseChannelHotGameJoinStatusRequest.Unmarshal(m, b)
}
func (m *SetMuseChannelHotGameJoinStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMuseChannelHotGameJoinStatusRequest.Marshal(b, m, deterministic)
}
func (dst *SetMuseChannelHotGameJoinStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMuseChannelHotGameJoinStatusRequest.Merge(dst, src)
}
func (m *SetMuseChannelHotGameJoinStatusRequest) XXX_Size() int {
	return xxx_messageInfo_SetMuseChannelHotGameJoinStatusRequest.Size(m)
}
func (m *SetMuseChannelHotGameJoinStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMuseChannelHotGameJoinStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetMuseChannelHotGameJoinStatusRequest proto.InternalMessageInfo

func (m *SetMuseChannelHotGameJoinStatusRequest) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetMuseChannelHotGameJoinStatusRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMuseChannelHotGameJoinStatusRequest) GetHotGameId() string {
	if m != nil {
		return m.HotGameId
	}
	return ""
}

func (m *SetMuseChannelHotGameJoinStatusRequest) GetJoinStatus() uint32 {
	if m != nil {
		return m.JoinStatus
	}
	return 0
}

type SetMuseChannelHotGameJoinStatusResponse struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetMuseChannelHotGameJoinStatusResponse) Reset() {
	*m = SetMuseChannelHotGameJoinStatusResponse{}
}
func (m *SetMuseChannelHotGameJoinStatusResponse) String() string { return proto.CompactTextString(m) }
func (*SetMuseChannelHotGameJoinStatusResponse) ProtoMessage()    {}
func (*SetMuseChannelHotGameJoinStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_channel_hot_game_logic_888c1f097190fa82, []int{5}
}
func (m *SetMuseChannelHotGameJoinStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMuseChannelHotGameJoinStatusResponse.Unmarshal(m, b)
}
func (m *SetMuseChannelHotGameJoinStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMuseChannelHotGameJoinStatusResponse.Marshal(b, m, deterministic)
}
func (dst *SetMuseChannelHotGameJoinStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMuseChannelHotGameJoinStatusResponse.Merge(dst, src)
}
func (m *SetMuseChannelHotGameJoinStatusResponse) XXX_Size() int {
	return xxx_messageInfo_SetMuseChannelHotGameJoinStatusResponse.Size(m)
}
func (m *SetMuseChannelHotGameJoinStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMuseChannelHotGameJoinStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetMuseChannelHotGameJoinStatusResponse proto.InternalMessageInfo

func (m *SetMuseChannelHotGameJoinStatusResponse) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*GetMuseChannelHotGameRequest)(nil), "ga.muse_channel_hot_game_logic.GetMuseChannelHotGameRequest")
	proto.RegisterType((*GetMuseChannelHotGameResponse)(nil), "ga.muse_channel_hot_game_logic.GetMuseChannelHotGameResponse")
	proto.RegisterType((*GetMuseChannelHotGameFloatRequest)(nil), "ga.muse_channel_hot_game_logic.GetMuseChannelHotGameFloatRequest")
	proto.RegisterType((*GetMuseChannelHotGameFloatResponse)(nil), "ga.muse_channel_hot_game_logic.GetMuseChannelHotGameFloatResponse")
	proto.RegisterType((*SetMuseChannelHotGameJoinStatusRequest)(nil), "ga.muse_channel_hot_game_logic.SetMuseChannelHotGameJoinStatusRequest")
	proto.RegisterType((*SetMuseChannelHotGameJoinStatusResponse)(nil), "ga.muse_channel_hot_game_logic.SetMuseChannelHotGameJoinStatusResponse")
	proto.RegisterEnum("ga.muse_channel_hot_game_logic.MuseChannelHotGameJoinStatus", MuseChannelHotGameJoinStatus_name, MuseChannelHotGameJoinStatus_value)
}

func init() {
	proto.RegisterFile("muse_channel_hot_game_logic/muse_channel_hot_game_logic.proto", fileDescriptor_muse_channel_hot_game_logic_888c1f097190fa82)
}

var fileDescriptor_muse_channel_hot_game_logic_888c1f097190fa82 = []byte{
	// 496 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x94, 0xc1, 0x6a, 0xdb, 0x4c,
	0x14, 0x85, 0x7f, 0x25, 0xb1, 0xff, 0xf8, 0xba, 0x86, 0x30, 0xb4, 0x45, 0x94, 0x24, 0x4d, 0xb5,
	0x70, 0xd3, 0x80, 0x65, 0x48, 0x5b, 0xc8, 0xa6, 0x0b, 0x47, 0x55, 0x6c, 0x85, 0x58, 0x29, 0x92,
	0xbc, 0x29, 0x94, 0x61, 0x24, 0x0d, 0xb2, 0x8c, 0xa4, 0x51, 0x3c, 0xa3, 0x45, 0xf6, 0x7d, 0x84,
	0x3e, 0x48, 0x5f, 0xa2, 0xef, 0x55, 0x34, 0x52, 0x15, 0x10, 0xa9, 0x83, 0xa1, 0xbb, 0x3b, 0x77,
	0x8e, 0xef, 0xb9, 0xe7, 0x33, 0x1a, 0xf8, 0x94, 0x16, 0x9c, 0xe2, 0x60, 0x49, 0xb2, 0x8c, 0x26,
	0x78, 0xc9, 0x04, 0x8e, 0x48, 0x4a, 0x71, 0xc2, 0xa2, 0x38, 0x18, 0x6f, 0xb8, 0xd3, 0xf3, 0x35,
	0x13, 0x0c, 0x1d, 0x47, 0x44, 0xdf, 0xa0, 0x7a, 0x35, 0x88, 0x08, 0xf6, 0x09, 0xa7, 0x95, 0x5c,
	0xfb, 0x06, 0x87, 0x53, 0x2a, 0xe6, 0x05, 0xa7, 0x46, 0xa5, 0x9f, 0x31, 0x31, 0x25, 0x29, 0x75,
	0xe8, 0x5d, 0x41, 0xb9, 0x40, 0x43, 0xd8, 0x2f, 0xd5, 0x78, 0x4d, 0xef, 0x54, 0xe5, 0x44, 0x39,
	0xed, 0x9f, 0xf7, 0xf5, 0x88, 0xe8, 0x97, 0x84, 0x97, 0x12, 0xe7, 0x7f, 0xbf, 0x2a, 0xd0, 0x0b,
	0xe8, 0x0a, 0xe2, 0xe3, 0x38, 0x54, 0x77, 0x4e, 0x94, 0xd3, 0x81, 0xd3, 0x11, 0xc4, 0xb7, 0x42,
	0xed, 0x87, 0x02, 0x47, 0x7f, 0x99, 0xcf, 0x73, 0x96, 0x71, 0x8a, 0xde, 0x41, 0xaf, 0x36, 0xe0,
	0x79, 0xed, 0xf0, 0xec, 0xc1, 0x81, 0xe7, 0xce, 0xbe, 0x5f, 0x57, 0xe8, 0x18, 0xfa, 0x4d, 0x98,
	0xda, 0xa8, 0xe7, 0xf4, 0x96, 0xd5, 0x40, 0x2b, 0x44, 0xcf, 0xa1, 0x23, 0x62, 0x91, 0x50, 0x75,
	0x57, 0xde, 0x54, 0x07, 0x84, 0x60, 0x2f, 0xa4, 0x3c, 0x50, 0xf7, 0x64, 0x53, 0xd6, 0xda, 0x0a,
	0xde, 0x3c, 0xba, 0xd5, 0x55, 0xc2, 0x88, 0xd8, 0x36, 0xfa, 0x11, 0xc0, 0x1f, 0xd6, 0x4d, 0xfc,
	0x5e, 0xdd, 0xb1, 0x42, 0xed, 0x97, 0x02, 0xda, 0x26, 0xb3, 0xed, 0x39, 0x34, 0x39, 0x77, 0x5a,
	0x39, 0xe3, 0x80, 0x65, 0x75, 0x78, 0x59, 0xa3, 0x97, 0xd0, 0xcd, 0x8a, 0xd4, 0xa7, 0x6b, 0x99,
	0x7e, 0xe0, 0xd4, 0xa7, 0x86, 0x49, 0xe7, 0x81, 0x49, 0x9b, 0x6e, 0xb7, 0x45, 0x57, 0xfb, 0xa9,
	0xc0, 0xd0, 0x7d, 0x2c, 0xc7, 0x35, 0x8b, 0x33, 0x57, 0x10, 0x51, 0xf0, 0x7f, 0x4b, 0xae, 0xbd,
	0xd1, 0x6e, 0xfb, 0xff, 0x7e, 0x0d, 0xfd, 0x15, 0x8b, 0x33, 0xcc, 0xa5, 0x79, 0x1d, 0x11, 0x56,
	0xcd, 0x3a, 0x9a, 0x07, 0x6f, 0x9f, 0xdc, 0x78, 0x6b, 0xfc, 0x67, 0xdf, 0x15, 0x38, 0xdc, 0x34,
	0x13, 0x5d, 0xc0, 0x87, 0xf9, 0xc2, 0x35, 0xb1, 0x31, 0x9b, 0xd8, 0xb6, 0x79, 0x83, 0x67, 0xb7,
	0x1e, 0x9e, 0x4e, 0xe6, 0x26, 0xbe, 0xbe, 0xb5, 0x6c, 0xec, 0x7a, 0x13, 0x6f, 0xe1, 0x62, 0x63,
	0x62, 0x1b, 0xe6, 0x0d, 0x5e, 0xd8, 0xee, 0x17, 0xd3, 0xb0, 0xae, 0x2c, 0xf3, 0xf3, 0xc1, 0x7f,
	0xe8, 0x0c, 0x86, 0x4f, 0xff, 0xb2, 0xac, 0x0f, 0x94, 0x4b, 0x0f, 0xd4, 0x80, 0xa5, 0xfa, 0x7d,
	0x7c, 0xcf, 0x8a, 0x72, 0xd3, 0x94, 0x85, 0x34, 0xa9, 0xbe, 0xea, 0xaf, 0x17, 0x11, 0x4b, 0x48,
	0x16, 0xe9, 0x1f, 0xcf, 0x85, 0xd0, 0x03, 0x96, 0x8e, 0x65, 0x3b, 0x60, 0xc9, 0x98, 0xe4, 0xb9,
	0x7c, 0x44, 0x46, 0x35, 0xe6, 0xd1, 0x92, 0x89, 0x51, 0x49, 0x78, 0x24, 0x9f, 0x07, 0xbf, 0x2b,
	0x95, 0xef, 0x7f, 0x07, 0x00, 0x00, 0xff, 0xff, 0x29, 0x79, 0xbc, 0x7b, 0x86, 0x04, 0x00, 0x00,
}
