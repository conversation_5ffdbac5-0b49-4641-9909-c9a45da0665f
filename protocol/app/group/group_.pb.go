// Code generated by protoc-gen-gogo.
// source: group_.proto
// DO NOT EDIT!

/*
	Package group is a generated protocol buffer package.

	It is generated from these files:
		group_.proto

	It has these top-level messages:
		GroupAddMemberReq
		GroupAddMemberResp
		GroupRemoveMemberReq
		GroupRemoveMemberResp
		GroupModifyNameReq
		GroupModifyNameResp
		GroupGetMemberListReq
		GroupGetMemberListResp
		GroupPublishBulletinReq
		GroupPublishBulletinResp
		GroupDeleteBulletinReq
		GroupBatchDelBulletinReq
		GroupDeleteBulletinResp
		GroupGetBulletinsReq
		BaseBulletinInfo
		GroupGetBulletinsResp
		GetGroupAnnouncementListReq
		GetGroupAnnouncementListResp
		ImTextWithHighlightUrl
		ImContentExt
		GroupAnnouncementDetail
		PrepareApplyGroupAnnouncementReq
		PrepareApplyGroupAnnouncementResp
		DelGroupAnnouncementReq
		DelGroupAnnouncementResp
		GetGroupAnnouncementCheckReq
		GetGroupAnnouncementCheckResp
		GetGroupAnnouncementByIDReq
		GetGroupAnnouncementByIDResp
		GetGroupAnnouncementEditAuthReq
		GetGroupAnnouncementEditAuthResp
*/
package group

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type GroupAnnouncementSuggestion int32

const (
	GroupAnnouncementSuggestion_GroupAnnouncementSuggestion_PASS     GroupAnnouncementSuggestion = 0
	GroupAnnouncementSuggestion_GroupAnnouncementSuggestion_REJECT   GroupAnnouncementSuggestion = 1
	GroupAnnouncementSuggestion_GroupAnnouncementSuggestion_APPLYING GroupAnnouncementSuggestion = 2
	GroupAnnouncementSuggestion_GroupAnnouncementSuggestion_NONE     GroupAnnouncementSuggestion = 3
)

var GroupAnnouncementSuggestion_name = map[int32]string{
	0: "GroupAnnouncementSuggestion_PASS",
	1: "GroupAnnouncementSuggestion_REJECT",
	2: "GroupAnnouncementSuggestion_APPLYING",
	3: "GroupAnnouncementSuggestion_NONE",
}
var GroupAnnouncementSuggestion_value = map[string]int32{
	"GroupAnnouncementSuggestion_PASS":     0,
	"GroupAnnouncementSuggestion_REJECT":   1,
	"GroupAnnouncementSuggestion_APPLYING": 2,
	"GroupAnnouncementSuggestion_NONE":     3,
}

func (x GroupAnnouncementSuggestion) Enum() *GroupAnnouncementSuggestion {
	p := new(GroupAnnouncementSuggestion)
	*p = x
	return p
}
func (x GroupAnnouncementSuggestion) String() string {
	return proto.EnumName(GroupAnnouncementSuggestion_name, int32(x))
}
func (x *GroupAnnouncementSuggestion) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GroupAnnouncementSuggestion_value, data, "GroupAnnouncementSuggestion")
	if err != nil {
		return err
	}
	*x = GroupAnnouncementSuggestion(value)
	return nil
}
func (GroupAnnouncementSuggestion) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{0}
}

type GroupAnnouncementEditAuth int32

const (
	GroupAnnouncementEditAuth_GroupAnnouncementEditAuth_REJECT GroupAnnouncementEditAuth = 0
	GroupAnnouncementEditAuth_GroupAnnouncementEditAuth_PASS   GroupAnnouncementEditAuth = 1
)

var GroupAnnouncementEditAuth_name = map[int32]string{
	0: "GroupAnnouncementEditAuth_REJECT",
	1: "GroupAnnouncementEditAuth_PASS",
}
var GroupAnnouncementEditAuth_value = map[string]int32{
	"GroupAnnouncementEditAuth_REJECT": 0,
	"GroupAnnouncementEditAuth_PASS":   1,
}

func (x GroupAnnouncementEditAuth) Enum() *GroupAnnouncementEditAuth {
	p := new(GroupAnnouncementEditAuth)
	*p = x
	return p
}
func (x GroupAnnouncementEditAuth) String() string {
	return proto.EnumName(GroupAnnouncementEditAuth_name, int32(x))
}
func (x *GroupAnnouncementEditAuth) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GroupAnnouncementEditAuth_value, data, "GroupAnnouncementEditAuth")
	if err != nil {
		return err
	}
	*x = GroupAnnouncementEditAuth(value)
	return nil
}
func (GroupAnnouncementEditAuth) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{1}
}

// 临时聊天加人/创建临时聊天
type GroupAddMemberReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Accounts     []string    `protobuf:"bytes,2,rep,name=accounts" json:"accounts,omitempty"`
	GroupAccount string      `protobuf:"bytes,3,opt,name=group_account,json=groupAccount" json:"group_account"`
}

func (m *GroupAddMemberReq) Reset()                    { *m = GroupAddMemberReq{} }
func (m *GroupAddMemberReq) String() string            { return proto.CompactTextString(m) }
func (*GroupAddMemberReq) ProtoMessage()               {}
func (*GroupAddMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{0} }

func (m *GroupAddMemberReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GroupAddMemberReq) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *GroupAddMemberReq) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

type GroupAddMemberResp struct {
	BaseResp        *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GroupAccount    string       `protobuf:"bytes,2,req,name=group_account,json=groupAccount" json:"group_account"`
	SuccessAccounts []string     `protobuf:"bytes,3,rep,name=success_accounts,json=successAccounts" json:"success_accounts,omitempty"`
	FailAccounts    []string     `protobuf:"bytes,4,rep,name=fail_accounts,json=failAccounts" json:"fail_accounts,omitempty"`
	GroupName       string       `protobuf:"bytes,5,opt,name=group_name,json=groupName" json:"group_name"`
	CreateMsg       string       `protobuf:"bytes,6,opt,name=create_msg,json=createMsg" json:"create_msg"`
}

func (m *GroupAddMemberResp) Reset()                    { *m = GroupAddMemberResp{} }
func (m *GroupAddMemberResp) String() string            { return proto.CompactTextString(m) }
func (*GroupAddMemberResp) ProtoMessage()               {}
func (*GroupAddMemberResp) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{1} }

func (m *GroupAddMemberResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GroupAddMemberResp) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

func (m *GroupAddMemberResp) GetSuccessAccounts() []string {
	if m != nil {
		return m.SuccessAccounts
	}
	return nil
}

func (m *GroupAddMemberResp) GetFailAccounts() []string {
	if m != nil {
		return m.FailAccounts
	}
	return nil
}

func (m *GroupAddMemberResp) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

func (m *GroupAddMemberResp) GetCreateMsg() string {
	if m != nil {
		return m.CreateMsg
	}
	return ""
}

// 临时聊天踢人
type GroupRemoveMemberReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	MemAccount   string      `protobuf:"bytes,2,req,name=mem_account,json=memAccount" json:"mem_account"`
	GroupAccount string      `protobuf:"bytes,3,req,name=group_account,json=groupAccount" json:"group_account"`
}

func (m *GroupRemoveMemberReq) Reset()                    { *m = GroupRemoveMemberReq{} }
func (m *GroupRemoveMemberReq) String() string            { return proto.CompactTextString(m) }
func (*GroupRemoveMemberReq) ProtoMessage()               {}
func (*GroupRemoveMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{2} }

func (m *GroupRemoveMemberReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GroupRemoveMemberReq) GetMemAccount() string {
	if m != nil {
		return m.MemAccount
	}
	return ""
}

func (m *GroupRemoveMemberReq) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

type GroupRemoveMemberResp struct {
	BaseResp     *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GroupAccount string       `protobuf:"bytes,2,req,name=group_account,json=groupAccount" json:"group_account"`
	MemAccount   string       `protobuf:"bytes,3,req,name=mem_account,json=memAccount" json:"mem_account"`
}

func (m *GroupRemoveMemberResp) Reset()                    { *m = GroupRemoveMemberResp{} }
func (m *GroupRemoveMemberResp) String() string            { return proto.CompactTextString(m) }
func (*GroupRemoveMemberResp) ProtoMessage()               {}
func (*GroupRemoveMemberResp) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{3} }

func (m *GroupRemoveMemberResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GroupRemoveMemberResp) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

func (m *GroupRemoveMemberResp) GetMemAccount() string {
	if m != nil {
		return m.MemAccount
	}
	return ""
}

// 修改群名称
type GroupModifyNameReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupAccount string      `protobuf:"bytes,2,req,name=group_account,json=groupAccount" json:"group_account"`
	GroupName    string      `protobuf:"bytes,3,req,name=group_name,json=groupName" json:"group_name"`
}

func (m *GroupModifyNameReq) Reset()                    { *m = GroupModifyNameReq{} }
func (m *GroupModifyNameReq) String() string            { return proto.CompactTextString(m) }
func (*GroupModifyNameReq) ProtoMessage()               {}
func (*GroupModifyNameReq) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{4} }

func (m *GroupModifyNameReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GroupModifyNameReq) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

func (m *GroupModifyNameReq) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

type GroupModifyNameResp struct {
	BaseResp     *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GroupAccount string       `protobuf:"bytes,2,req,name=group_account,json=groupAccount" json:"group_account"`
	GroupName    string       `protobuf:"bytes,3,req,name=group_name,json=groupName" json:"group_name"`
}

func (m *GroupModifyNameResp) Reset()                    { *m = GroupModifyNameResp{} }
func (m *GroupModifyNameResp) String() string            { return proto.CompactTextString(m) }
func (*GroupModifyNameResp) ProtoMessage()               {}
func (*GroupModifyNameResp) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{5} }

func (m *GroupModifyNameResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GroupModifyNameResp) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

func (m *GroupModifyNameResp) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

// 获取临时群成员列表
type GroupGetMemberListReq struct {
	BaseReq      *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupAccount string      `protobuf:"bytes,2,req,name=group_account,json=groupAccount" json:"group_account"`
}

func (m *GroupGetMemberListReq) Reset()                    { *m = GroupGetMemberListReq{} }
func (m *GroupGetMemberListReq) String() string            { return proto.CompactTextString(m) }
func (*GroupGetMemberListReq) ProtoMessage()               {}
func (*GroupGetMemberListReq) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{6} }

func (m *GroupGetMemberListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GroupGetMemberListReq) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

type GroupGetMemberListResp struct {
	BaseResp     *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	GroupAccount string                   `protobuf:"bytes,2,req,name=group_account,json=groupAccount" json:"group_account"`
	Members      []*ga.GroupMemSimpleInfo `protobuf:"bytes,3,rep,name=members" json:"members,omitempty"`
}

func (m *GroupGetMemberListResp) Reset()                    { *m = GroupGetMemberListResp{} }
func (m *GroupGetMemberListResp) String() string            { return proto.CompactTextString(m) }
func (*GroupGetMemberListResp) ProtoMessage()               {}
func (*GroupGetMemberListResp) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{7} }

func (m *GroupGetMemberListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GroupGetMemberListResp) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

func (m *GroupGetMemberListResp) GetMembers() []*ga.GroupMemSimpleInfo {
	if m != nil {
		return m.Members
	}
	return nil
}

type GroupPublishBulletinReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Title   string      `protobuf:"bytes,2,req,name=title" json:"title"`
	Content string      `protobuf:"bytes,3,req,name=content" json:"content"`
	GroupId uint32      `protobuf:"varint,4,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *GroupPublishBulletinReq) Reset()                    { *m = GroupPublishBulletinReq{} }
func (m *GroupPublishBulletinReq) String() string            { return proto.CompactTextString(m) }
func (*GroupPublishBulletinReq) ProtoMessage()               {}
func (*GroupPublishBulletinReq) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{8} }

func (m *GroupPublishBulletinReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GroupPublishBulletinReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GroupPublishBulletinReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GroupPublishBulletinReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GroupPublishBulletinResp struct {
	BaseResp   *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	BulletinId uint32       `protobuf:"varint,2,req,name=bulletin_id,json=bulletinId" json:"bulletin_id"`
	OpTime     uint32       `protobuf:"varint,3,req,name=op_time,json=opTime" json:"op_time"`
}

func (m *GroupPublishBulletinResp) Reset()                    { *m = GroupPublishBulletinResp{} }
func (m *GroupPublishBulletinResp) String() string            { return proto.CompactTextString(m) }
func (*GroupPublishBulletinResp) ProtoMessage()               {}
func (*GroupPublishBulletinResp) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{9} }

func (m *GroupPublishBulletinResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GroupPublishBulletinResp) GetBulletinId() uint32 {
	if m != nil {
		return m.BulletinId
	}
	return 0
}

func (m *GroupPublishBulletinResp) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

type GroupDeleteBulletinReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupId    uint32      `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
	BulletinId uint32      `protobuf:"varint,3,req,name=bulletin_id,json=bulletinId" json:"bulletin_id"`
}

func (m *GroupDeleteBulletinReq) Reset()                    { *m = GroupDeleteBulletinReq{} }
func (m *GroupDeleteBulletinReq) String() string            { return proto.CompactTextString(m) }
func (*GroupDeleteBulletinReq) ProtoMessage()               {}
func (*GroupDeleteBulletinReq) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{10} }

func (m *GroupDeleteBulletinReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GroupDeleteBulletinReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupDeleteBulletinReq) GetBulletinId() uint32 {
	if m != nil {
		return m.BulletinId
	}
	return 0
}

type GroupBatchDelBulletinReq struct {
	BaseReq     *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupId     uint32      `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
	BulletinIds []uint32    `protobuf:"varint,3,rep,name=bulletin_ids,json=bulletinIds" json:"bulletin_ids,omitempty"`
}

func (m *GroupBatchDelBulletinReq) Reset()                    { *m = GroupBatchDelBulletinReq{} }
func (m *GroupBatchDelBulletinReq) String() string            { return proto.CompactTextString(m) }
func (*GroupBatchDelBulletinReq) ProtoMessage()               {}
func (*GroupBatchDelBulletinReq) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{11} }

func (m *GroupBatchDelBulletinReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GroupBatchDelBulletinReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupBatchDelBulletinReq) GetBulletinIds() []uint32 {
	if m != nil {
		return m.BulletinIds
	}
	return nil
}

type GroupDeleteBulletinResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *GroupDeleteBulletinResp) Reset()                    { *m = GroupDeleteBulletinResp{} }
func (m *GroupDeleteBulletinResp) String() string            { return proto.CompactTextString(m) }
func (*GroupDeleteBulletinResp) ProtoMessage()               {}
func (*GroupDeleteBulletinResp) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{12} }

func (m *GroupDeleteBulletinResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GroupGetBulletinsReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupId uint32      `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *GroupGetBulletinsReq) Reset()                    { *m = GroupGetBulletinsReq{} }
func (m *GroupGetBulletinsReq) String() string            { return proto.CompactTextString(m) }
func (*GroupGetBulletinsReq) ProtoMessage()               {}
func (*GroupGetBulletinsReq) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{13} }

func (m *GroupGetBulletinsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GroupGetBulletinsReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type BaseBulletinInfo struct {
	Title      string `protobuf:"bytes,1,req,name=title" json:"title"`
	Content    string `protobuf:"bytes,2,req,name=content" json:"content"`
	OpTime     uint32 `protobuf:"varint,3,req,name=op_time,json=opTime" json:"op_time"`
	BulletinId uint32 `protobuf:"varint,4,req,name=bulletin_id,json=bulletinId" json:"bulletin_id"`
	Author     string `protobuf:"bytes,5,req,name=author" json:"author"`
}

func (m *BaseBulletinInfo) Reset()                    { *m = BaseBulletinInfo{} }
func (m *BaseBulletinInfo) String() string            { return proto.CompactTextString(m) }
func (*BaseBulletinInfo) ProtoMessage()               {}
func (*BaseBulletinInfo) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{14} }

func (m *BaseBulletinInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *BaseBulletinInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *BaseBulletinInfo) GetOpTime() uint32 {
	if m != nil {
		return m.OpTime
	}
	return 0
}

func (m *BaseBulletinInfo) GetBulletinId() uint32 {
	if m != nil {
		return m.BulletinId
	}
	return 0
}

func (m *BaseBulletinInfo) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

type GroupGetBulletinsResp struct {
	BaseResp         *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	BulletinInfoList []*BaseBulletinInfo `protobuf:"bytes,2,rep,name=bulletin_info_list,json=bulletinInfoList" json:"bulletin_info_list,omitempty"`
}

func (m *GroupGetBulletinsResp) Reset()                    { *m = GroupGetBulletinsResp{} }
func (m *GroupGetBulletinsResp) String() string            { return proto.CompactTextString(m) }
func (*GroupGetBulletinsResp) ProtoMessage()               {}
func (*GroupGetBulletinsResp) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{15} }

func (m *GroupGetBulletinsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GroupGetBulletinsResp) GetBulletinInfoList() []*BaseBulletinInfo {
	if m != nil {
		return m.BulletinInfoList
	}
	return nil
}

// 新公告相关
type GetGroupAnnouncementListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupId uint32      `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
	LastId  uint32      `protobuf:"varint,3,req,name=last_id,json=lastId" json:"last_id"`
	Limit   uint32      `protobuf:"varint,4,req,name=limit" json:"limit"`
}

func (m *GetGroupAnnouncementListReq) Reset()         { *m = GetGroupAnnouncementListReq{} }
func (m *GetGroupAnnouncementListReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupAnnouncementListReq) ProtoMessage()    {}
func (*GetGroupAnnouncementListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{16}
}

func (m *GetGroupAnnouncementListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupAnnouncementListReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupAnnouncementListReq) GetLastId() uint32 {
	if m != nil {
		return m.LastId
	}
	return 0
}

func (m *GetGroupAnnouncementListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGroupAnnouncementListResp struct {
	BaseResp *ga.BaseResp               `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Details  []*GroupAnnouncementDetail `protobuf:"bytes,2,rep,name=details" json:"details,omitempty"`
	Limit    uint32                     `protobuf:"varint,3,req,name=limit" json:"limit"`
	ThisId   uint32                     `protobuf:"varint,4,req,name=this_id,json=thisId" json:"this_id"`
	MaxCount uint32                     `protobuf:"varint,5,req,name=max_count,json=maxCount" json:"max_count"`
}

func (m *GetGroupAnnouncementListResp) Reset()         { *m = GetGroupAnnouncementListResp{} }
func (m *GetGroupAnnouncementListResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupAnnouncementListResp) ProtoMessage()    {}
func (*GetGroupAnnouncementListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{17}
}

func (m *GetGroupAnnouncementListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupAnnouncementListResp) GetDetails() []*GroupAnnouncementDetail {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *GetGroupAnnouncementListResp) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGroupAnnouncementListResp) GetThisId() uint32 {
	if m != nil {
		return m.ThisId
	}
	return 0
}

func (m *GetGroupAnnouncementListResp) GetMaxCount() uint32 {
	if m != nil {
		return m.MaxCount
	}
	return 0
}

// 文字链
type ImTextWithHighlightUrl struct {
	Content string `protobuf:"bytes,1,opt,name=content" json:"content"`
	Url     string `protobuf:"bytes,2,opt,name=url" json:"url"`
	Index   uint32 `protobuf:"varint,3,opt,name=index" json:"index"`
}

func (m *ImTextWithHighlightUrl) Reset()                    { *m = ImTextWithHighlightUrl{} }
func (m *ImTextWithHighlightUrl) String() string            { return proto.CompactTextString(m) }
func (*ImTextWithHighlightUrl) ProtoMessage()               {}
func (*ImTextWithHighlightUrl) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{18} }

func (m *ImTextWithHighlightUrl) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ImTextWithHighlightUrl) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ImTextWithHighlightUrl) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

type ImContentExt struct {
	TextHlUrl []*ImTextWithHighlightUrl `protobuf:"bytes,1,rep,name=text_hl_url,json=textHlUrl" json:"text_hl_url,omitempty"`
	CanJump   bool                      `protobuf:"varint,2,opt,name=can_jump,json=canJump" json:"can_jump"`
	PicUrl    string                    `protobuf:"bytes,3,opt,name=pic_url,json=picUrl" json:"pic_url"`
	Host      string                    `protobuf:"bytes,4,opt,name=host" json:"host"`
}

func (m *ImContentExt) Reset()                    { *m = ImContentExt{} }
func (m *ImContentExt) String() string            { return proto.CompactTextString(m) }
func (*ImContentExt) ProtoMessage()               {}
func (*ImContentExt) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{19} }

func (m *ImContentExt) GetTextHlUrl() []*ImTextWithHighlightUrl {
	if m != nil {
		return m.TextHlUrl
	}
	return nil
}

func (m *ImContentExt) GetCanJump() bool {
	if m != nil {
		return m.CanJump
	}
	return false
}

func (m *ImContentExt) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *ImContentExt) GetHost() string {
	if m != nil {
		return m.Host
	}
	return ""
}

type GroupAnnouncementDetail struct {
	Id         uint32        `protobuf:"varint,1,req,name=id" json:"id"`
	TextNormal string        `protobuf:"bytes,2,req,name=text_normal,json=textNormal" json:"text_normal"`
	Ext        *ImContentExt `protobuf:"bytes,3,opt,name=ext" json:"ext,omitempty"`
	Timestamp  int64         `protobuf:"varint,4,req,name=timestamp" json:"timestamp"`
	Nickname   string        `protobuf:"bytes,5,opt,name=nickname" json:"nickname"`
	Account    string        `protobuf:"bytes,6,opt,name=account" json:"account"`
	IsChecking bool          `protobuf:"varint,7,opt,name=is_checking,json=isChecking" json:"is_checking"`
}

func (m *GroupAnnouncementDetail) Reset()                    { *m = GroupAnnouncementDetail{} }
func (m *GroupAnnouncementDetail) String() string            { return proto.CompactTextString(m) }
func (*GroupAnnouncementDetail) ProtoMessage()               {}
func (*GroupAnnouncementDetail) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{20} }

func (m *GroupAnnouncementDetail) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupAnnouncementDetail) GetTextNormal() string {
	if m != nil {
		return m.TextNormal
	}
	return ""
}

func (m *GroupAnnouncementDetail) GetExt() *ImContentExt {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *GroupAnnouncementDetail) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *GroupAnnouncementDetail) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GroupAnnouncementDetail) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GroupAnnouncementDetail) GetIsChecking() bool {
	if m != nil {
		return m.IsChecking
	}
	return false
}

type PrepareApplyGroupAnnouncementReq struct {
	BaseReq *ga.BaseReq              `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Detail  *GroupAnnouncementDetail `protobuf:"bytes,2,req,name=detail" json:"detail,omitempty"`
	GroupId uint32                   `protobuf:"varint,3,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *PrepareApplyGroupAnnouncementReq) Reset()         { *m = PrepareApplyGroupAnnouncementReq{} }
func (m *PrepareApplyGroupAnnouncementReq) String() string { return proto.CompactTextString(m) }
func (*PrepareApplyGroupAnnouncementReq) ProtoMessage()    {}
func (*PrepareApplyGroupAnnouncementReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{21}
}

func (m *PrepareApplyGroupAnnouncementReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PrepareApplyGroupAnnouncementReq) GetDetail() *GroupAnnouncementDetail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *PrepareApplyGroupAnnouncementReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type PrepareApplyGroupAnnouncementResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *PrepareApplyGroupAnnouncementResp) Reset()         { *m = PrepareApplyGroupAnnouncementResp{} }
func (m *PrepareApplyGroupAnnouncementResp) String() string { return proto.CompactTextString(m) }
func (*PrepareApplyGroupAnnouncementResp) ProtoMessage()    {}
func (*PrepareApplyGroupAnnouncementResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{22}
}

func (m *PrepareApplyGroupAnnouncementResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type DelGroupAnnouncementReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Id      uint32      `protobuf:"varint,2,req,name=id" json:"id"`
	GroupId uint32      `protobuf:"varint,3,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *DelGroupAnnouncementReq) Reset()                    { *m = DelGroupAnnouncementReq{} }
func (m *DelGroupAnnouncementReq) String() string            { return proto.CompactTextString(m) }
func (*DelGroupAnnouncementReq) ProtoMessage()               {}
func (*DelGroupAnnouncementReq) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{23} }

func (m *DelGroupAnnouncementReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DelGroupAnnouncementReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DelGroupAnnouncementReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type DelGroupAnnouncementResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *DelGroupAnnouncementResp) Reset()                    { *m = DelGroupAnnouncementResp{} }
func (m *DelGroupAnnouncementResp) String() string            { return proto.CompactTextString(m) }
func (*DelGroupAnnouncementResp) ProtoMessage()               {}
func (*DelGroupAnnouncementResp) Descriptor() ([]byte, []int) { return fileDescriptorGroup_, []int{24} }

func (m *DelGroupAnnouncementResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetGroupAnnouncementCheckReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupId uint32      `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *GetGroupAnnouncementCheckReq) Reset()         { *m = GetGroupAnnouncementCheckReq{} }
func (m *GetGroupAnnouncementCheckReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupAnnouncementCheckReq) ProtoMessage()    {}
func (*GetGroupAnnouncementCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{25}
}

func (m *GetGroupAnnouncementCheckReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupAnnouncementCheckReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGroupAnnouncementCheckResp struct {
	BaseResp *ga.BaseResp                `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Detail   *GroupAnnouncementDetail    `protobuf:"bytes,2,opt,name=detail" json:"detail,omitempty"`
	Result   GroupAnnouncementSuggestion `protobuf:"varint,3,req,name=result,enum=ga.GroupAnnouncementSuggestion" json:"result"`
}

func (m *GetGroupAnnouncementCheckResp) Reset()         { *m = GetGroupAnnouncementCheckResp{} }
func (m *GetGroupAnnouncementCheckResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupAnnouncementCheckResp) ProtoMessage()    {}
func (*GetGroupAnnouncementCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{26}
}

func (m *GetGroupAnnouncementCheckResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupAnnouncementCheckResp) GetDetail() *GroupAnnouncementDetail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *GetGroupAnnouncementCheckResp) GetResult() GroupAnnouncementSuggestion {
	if m != nil {
		return m.Result
	}
	return GroupAnnouncementSuggestion_GroupAnnouncementSuggestion_PASS
}

type GetGroupAnnouncementByIDReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupId uint32      `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
	Id      uint32      `protobuf:"varint,3,req,name=id" json:"id"`
}

func (m *GetGroupAnnouncementByIDReq) Reset()         { *m = GetGroupAnnouncementByIDReq{} }
func (m *GetGroupAnnouncementByIDReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupAnnouncementByIDReq) ProtoMessage()    {}
func (*GetGroupAnnouncementByIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{27}
}

func (m *GetGroupAnnouncementByIDReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupAnnouncementByIDReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GetGroupAnnouncementByIDReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetGroupAnnouncementByIDResp struct {
	BaseResp *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Details  *GroupAnnouncementDetail `protobuf:"bytes,2,req,name=details" json:"details,omitempty"`
}

func (m *GetGroupAnnouncementByIDResp) Reset()         { *m = GetGroupAnnouncementByIDResp{} }
func (m *GetGroupAnnouncementByIDResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupAnnouncementByIDResp) ProtoMessage()    {}
func (*GetGroupAnnouncementByIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{28}
}

func (m *GetGroupAnnouncementByIDResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupAnnouncementByIDResp) GetDetails() *GroupAnnouncementDetail {
	if m != nil {
		return m.Details
	}
	return nil
}

type GetGroupAnnouncementEditAuthReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GroupId uint32      `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *GetGroupAnnouncementEditAuthReq) Reset()         { *m = GetGroupAnnouncementEditAuthReq{} }
func (m *GetGroupAnnouncementEditAuthReq) String() string { return proto.CompactTextString(m) }
func (*GetGroupAnnouncementEditAuthReq) ProtoMessage()    {}
func (*GetGroupAnnouncementEditAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{29}
}

func (m *GetGroupAnnouncementEditAuthReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGroupAnnouncementEditAuthReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetGroupAnnouncementEditAuthResp struct {
	BaseResp    *ga.BaseResp              `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Auth        GroupAnnouncementEditAuth `protobuf:"varint,2,req,name=auth,enum=ga.GroupAnnouncementEditAuth" json:"auth"`
	EditWordCnt uint32                    `protobuf:"varint,3,req,name=edit_word_cnt,json=editWordCnt" json:"edit_word_cnt"`
}

func (m *GetGroupAnnouncementEditAuthResp) Reset()         { *m = GetGroupAnnouncementEditAuthResp{} }
func (m *GetGroupAnnouncementEditAuthResp) String() string { return proto.CompactTextString(m) }
func (*GetGroupAnnouncementEditAuthResp) ProtoMessage()    {}
func (*GetGroupAnnouncementEditAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGroup_, []int{30}
}

func (m *GetGroupAnnouncementEditAuthResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGroupAnnouncementEditAuthResp) GetAuth() GroupAnnouncementEditAuth {
	if m != nil {
		return m.Auth
	}
	return GroupAnnouncementEditAuth_GroupAnnouncementEditAuth_REJECT
}

func (m *GetGroupAnnouncementEditAuthResp) GetEditWordCnt() uint32 {
	if m != nil {
		return m.EditWordCnt
	}
	return 0
}

func init() {
	proto.RegisterType((*GroupAddMemberReq)(nil), "ga.GroupAddMemberReq")
	proto.RegisterType((*GroupAddMemberResp)(nil), "ga.GroupAddMemberResp")
	proto.RegisterType((*GroupRemoveMemberReq)(nil), "ga.GroupRemoveMemberReq")
	proto.RegisterType((*GroupRemoveMemberResp)(nil), "ga.GroupRemoveMemberResp")
	proto.RegisterType((*GroupModifyNameReq)(nil), "ga.GroupModifyNameReq")
	proto.RegisterType((*GroupModifyNameResp)(nil), "ga.GroupModifyNameResp")
	proto.RegisterType((*GroupGetMemberListReq)(nil), "ga.GroupGetMemberListReq")
	proto.RegisterType((*GroupGetMemberListResp)(nil), "ga.GroupGetMemberListResp")
	proto.RegisterType((*GroupPublishBulletinReq)(nil), "ga.GroupPublishBulletinReq")
	proto.RegisterType((*GroupPublishBulletinResp)(nil), "ga.GroupPublishBulletinResp")
	proto.RegisterType((*GroupDeleteBulletinReq)(nil), "ga.GroupDeleteBulletinReq")
	proto.RegisterType((*GroupBatchDelBulletinReq)(nil), "ga.GroupBatchDelBulletinReq")
	proto.RegisterType((*GroupDeleteBulletinResp)(nil), "ga.GroupDeleteBulletinResp")
	proto.RegisterType((*GroupGetBulletinsReq)(nil), "ga.GroupGetBulletinsReq")
	proto.RegisterType((*BaseBulletinInfo)(nil), "ga.BaseBulletinInfo")
	proto.RegisterType((*GroupGetBulletinsResp)(nil), "ga.GroupGetBulletinsResp")
	proto.RegisterType((*GetGroupAnnouncementListReq)(nil), "ga.GetGroupAnnouncementListReq")
	proto.RegisterType((*GetGroupAnnouncementListResp)(nil), "ga.GetGroupAnnouncementListResp")
	proto.RegisterType((*ImTextWithHighlightUrl)(nil), "ga.ImTextWithHighlightUrl")
	proto.RegisterType((*ImContentExt)(nil), "ga.ImContentExt")
	proto.RegisterType((*GroupAnnouncementDetail)(nil), "ga.GroupAnnouncementDetail")
	proto.RegisterType((*PrepareApplyGroupAnnouncementReq)(nil), "ga.PrepareApplyGroupAnnouncementReq")
	proto.RegisterType((*PrepareApplyGroupAnnouncementResp)(nil), "ga.PrepareApplyGroupAnnouncementResp")
	proto.RegisterType((*DelGroupAnnouncementReq)(nil), "ga.DelGroupAnnouncementReq")
	proto.RegisterType((*DelGroupAnnouncementResp)(nil), "ga.DelGroupAnnouncementResp")
	proto.RegisterType((*GetGroupAnnouncementCheckReq)(nil), "ga.GetGroupAnnouncementCheckReq")
	proto.RegisterType((*GetGroupAnnouncementCheckResp)(nil), "ga.GetGroupAnnouncementCheckResp")
	proto.RegisterType((*GetGroupAnnouncementByIDReq)(nil), "ga.GetGroupAnnouncementByIDReq")
	proto.RegisterType((*GetGroupAnnouncementByIDResp)(nil), "ga.GetGroupAnnouncementByIDResp")
	proto.RegisterType((*GetGroupAnnouncementEditAuthReq)(nil), "ga.GetGroupAnnouncementEditAuthReq")
	proto.RegisterType((*GetGroupAnnouncementEditAuthResp)(nil), "ga.GetGroupAnnouncementEditAuthResp")
	proto.RegisterEnum("ga.GroupAnnouncementSuggestion", GroupAnnouncementSuggestion_name, GroupAnnouncementSuggestion_value)
	proto.RegisterEnum("ga.GroupAnnouncementEditAuth", GroupAnnouncementEditAuth_name, GroupAnnouncementEditAuth_value)
}
func (m *GroupAddMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupAddMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.Accounts) > 0 {
		for _, s := range m.Accounts {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupAccount)))
	i += copy(dAtA[i:], m.GroupAccount)
	return i, nil
}

func (m *GroupAddMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupAddMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupAccount)))
	i += copy(dAtA[i:], m.GroupAccount)
	if len(m.SuccessAccounts) > 0 {
		for _, s := range m.SuccessAccounts {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.FailAccounts) > 0 {
		for _, s := range m.FailAccounts {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupName)))
	i += copy(dAtA[i:], m.GroupName)
	dAtA[i] = 0x32
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.CreateMsg)))
	i += copy(dAtA[i:], m.CreateMsg)
	return i, nil
}

func (m *GroupRemoveMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupRemoveMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.MemAccount)))
	i += copy(dAtA[i:], m.MemAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupAccount)))
	i += copy(dAtA[i:], m.GroupAccount)
	return i, nil
}

func (m *GroupRemoveMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupRemoveMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupAccount)))
	i += copy(dAtA[i:], m.GroupAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.MemAccount)))
	i += copy(dAtA[i:], m.MemAccount)
	return i, nil
}

func (m *GroupModifyNameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupModifyNameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupAccount)))
	i += copy(dAtA[i:], m.GroupAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupName)))
	i += copy(dAtA[i:], m.GroupName)
	return i, nil
}

func (m *GroupModifyNameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupModifyNameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupAccount)))
	i += copy(dAtA[i:], m.GroupAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupName)))
	i += copy(dAtA[i:], m.GroupName)
	return i, nil
}

func (m *GroupGetMemberListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupGetMemberListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupAccount)))
	i += copy(dAtA[i:], m.GroupAccount)
	return i, nil
}

func (m *GroupGetMemberListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupGetMemberListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.GroupAccount)))
	i += copy(dAtA[i:], m.GroupAccount)
	if len(m.Members) > 0 {
		for _, msg := range m.Members {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGroup_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GroupPublishBulletinReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupPublishBulletinReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *GroupPublishBulletinResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupPublishBulletinResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.BulletinId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.OpTime))
	return i, nil
}

func (m *GroupDeleteBulletinReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupDeleteBulletinReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.BulletinId))
	return i, nil
}

func (m *GroupBatchDelBulletinReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupBatchDelBulletinReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n12, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	if len(m.BulletinIds) > 0 {
		for _, num := range m.BulletinIds {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGroup_(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GroupDeleteBulletinResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupDeleteBulletinResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n13, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	return i, nil
}

func (m *GroupGetBulletinsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupGetBulletinsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n14, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *BaseBulletinInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BaseBulletinInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.OpTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.BulletinId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Author)))
	i += copy(dAtA[i:], m.Author)
	return i, nil
}

func (m *GroupGetBulletinsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupGetBulletinsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n15, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	if len(m.BulletinInfoList) > 0 {
		for _, msg := range m.BulletinInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGroup_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetGroupAnnouncementListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupAnnouncementListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n16, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.LastId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetGroupAnnouncementListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupAnnouncementListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n17, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	if len(m.Details) > 0 {
		for _, msg := range m.Details {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGroup_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.ThisId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.MaxCount))
	return i, nil
}

func (m *ImTextWithHighlightUrl) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImTextWithHighlightUrl) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.Index))
	return i, nil
}

func (m *ImContentExt) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImContentExt) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TextHlUrl) > 0 {
		for _, msg := range m.TextHlUrl {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGroup_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	if m.CanJump {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Host)))
	i += copy(dAtA[i:], m.Host)
	return i, nil
}

func (m *GroupAnnouncementDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupAnnouncementDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.TextNormal)))
	i += copy(dAtA[i:], m.TextNormal)
	if m.Ext != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.Ext.Size()))
		n18, err := m.Ext.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x32
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x38
	i++
	if m.IsChecking {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *PrepareApplyGroupAnnouncementReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrepareApplyGroupAnnouncementReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	if m.Detail == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("detail")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.Detail.Size()))
		n20, err := m.Detail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *PrepareApplyGroupAnnouncementResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrepareApplyGroupAnnouncementResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n21, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	return i, nil
}

func (m *DelGroupAnnouncementReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelGroupAnnouncementReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n22, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *DelGroupAnnouncementResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelGroupAnnouncementResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n23, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	return i, nil
}

func (m *GetGroupAnnouncementCheckReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupAnnouncementCheckReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n24, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *GetGroupAnnouncementCheckResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupAnnouncementCheckResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n25, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	if m.Detail != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.Detail.Size()))
		n26, err := m.Detail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.Result))
	return i, nil
}

func (m *GetGroupAnnouncementByIDReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupAnnouncementByIDReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n27, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *GetGroupAnnouncementByIDResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupAnnouncementByIDResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n28, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	if m.Details == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("details")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.Details.Size()))
		n29, err := m.Details.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	return i, nil
}

func (m *GetGroupAnnouncementEditAuthReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupAnnouncementEditAuthReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseReq.Size()))
		n30, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *GetGroupAnnouncementEditAuthResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGroupAnnouncementEditAuthResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGroup_(dAtA, i, uint64(m.BaseResp.Size()))
		n31, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.Auth))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGroup_(dAtA, i, uint64(m.EditWordCnt))
	return i, nil
}

func encodeFixed64Group_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Group_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGroup_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GroupAddMemberReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	if len(m.Accounts) > 0 {
		for _, s := range m.Accounts {
			l = len(s)
			n += 1 + l + sovGroup_(uint64(l))
		}
	}
	l = len(m.GroupAccount)
	n += 1 + l + sovGroup_(uint64(l))
	return n
}

func (m *GroupAddMemberResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	l = len(m.GroupAccount)
	n += 1 + l + sovGroup_(uint64(l))
	if len(m.SuccessAccounts) > 0 {
		for _, s := range m.SuccessAccounts {
			l = len(s)
			n += 1 + l + sovGroup_(uint64(l))
		}
	}
	if len(m.FailAccounts) > 0 {
		for _, s := range m.FailAccounts {
			l = len(s)
			n += 1 + l + sovGroup_(uint64(l))
		}
	}
	l = len(m.GroupName)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.CreateMsg)
	n += 1 + l + sovGroup_(uint64(l))
	return n
}

func (m *GroupRemoveMemberReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	l = len(m.MemAccount)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.GroupAccount)
	n += 1 + l + sovGroup_(uint64(l))
	return n
}

func (m *GroupRemoveMemberResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	l = len(m.GroupAccount)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.MemAccount)
	n += 1 + l + sovGroup_(uint64(l))
	return n
}

func (m *GroupModifyNameReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	l = len(m.GroupAccount)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.GroupName)
	n += 1 + l + sovGroup_(uint64(l))
	return n
}

func (m *GroupModifyNameResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	l = len(m.GroupAccount)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.GroupName)
	n += 1 + l + sovGroup_(uint64(l))
	return n
}

func (m *GroupGetMemberListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	l = len(m.GroupAccount)
	n += 1 + l + sovGroup_(uint64(l))
	return n
}

func (m *GroupGetMemberListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	l = len(m.GroupAccount)
	n += 1 + l + sovGroup_(uint64(l))
	if len(m.Members) > 0 {
		for _, e := range m.Members {
			l = e.Size()
			n += 1 + l + sovGroup_(uint64(l))
		}
	}
	return n
}

func (m *GroupPublishBulletinReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	l = len(m.Title)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovGroup_(uint64(l))
	n += 1 + sovGroup_(uint64(m.GroupId))
	return n
}

func (m *GroupPublishBulletinResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.BulletinId))
	n += 1 + sovGroup_(uint64(m.OpTime))
	return n
}

func (m *GroupDeleteBulletinReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.GroupId))
	n += 1 + sovGroup_(uint64(m.BulletinId))
	return n
}

func (m *GroupBatchDelBulletinReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.GroupId))
	if len(m.BulletinIds) > 0 {
		for _, e := range m.BulletinIds {
			n += 1 + sovGroup_(uint64(e))
		}
	}
	return n
}

func (m *GroupDeleteBulletinResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	return n
}

func (m *GroupGetBulletinsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.GroupId))
	return n
}

func (m *BaseBulletinInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Title)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovGroup_(uint64(l))
	n += 1 + sovGroup_(uint64(m.OpTime))
	n += 1 + sovGroup_(uint64(m.BulletinId))
	l = len(m.Author)
	n += 1 + l + sovGroup_(uint64(l))
	return n
}

func (m *GroupGetBulletinsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	if len(m.BulletinInfoList) > 0 {
		for _, e := range m.BulletinInfoList {
			l = e.Size()
			n += 1 + l + sovGroup_(uint64(l))
		}
	}
	return n
}

func (m *GetGroupAnnouncementListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.GroupId))
	n += 1 + sovGroup_(uint64(m.LastId))
	n += 1 + sovGroup_(uint64(m.Limit))
	return n
}

func (m *GetGroupAnnouncementListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	if len(m.Details) > 0 {
		for _, e := range m.Details {
			l = e.Size()
			n += 1 + l + sovGroup_(uint64(l))
		}
	}
	n += 1 + sovGroup_(uint64(m.Limit))
	n += 1 + sovGroup_(uint64(m.ThisId))
	n += 1 + sovGroup_(uint64(m.MaxCount))
	return n
}

func (m *ImTextWithHighlightUrl) Size() (n int) {
	var l int
	_ = l
	l = len(m.Content)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovGroup_(uint64(l))
	n += 1 + sovGroup_(uint64(m.Index))
	return n
}

func (m *ImContentExt) Size() (n int) {
	var l int
	_ = l
	if len(m.TextHlUrl) > 0 {
		for _, e := range m.TextHlUrl {
			l = e.Size()
			n += 1 + l + sovGroup_(uint64(l))
		}
	}
	n += 2
	l = len(m.PicUrl)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.Host)
	n += 1 + l + sovGroup_(uint64(l))
	return n
}

func (m *GroupAnnouncementDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGroup_(uint64(m.Id))
	l = len(m.TextNormal)
	n += 1 + l + sovGroup_(uint64(l))
	if m.Ext != nil {
		l = m.Ext.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.Timestamp))
	l = len(m.Nickname)
	n += 1 + l + sovGroup_(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovGroup_(uint64(l))
	n += 2
	return n
}

func (m *PrepareApplyGroupAnnouncementReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	if m.Detail != nil {
		l = m.Detail.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.GroupId))
	return n
}

func (m *PrepareApplyGroupAnnouncementResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	return n
}

func (m *DelGroupAnnouncementReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.Id))
	n += 1 + sovGroup_(uint64(m.GroupId))
	return n
}

func (m *DelGroupAnnouncementResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	return n
}

func (m *GetGroupAnnouncementCheckReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.GroupId))
	return n
}

func (m *GetGroupAnnouncementCheckResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	if m.Detail != nil {
		l = m.Detail.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.Result))
	return n
}

func (m *GetGroupAnnouncementByIDReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.GroupId))
	n += 1 + sovGroup_(uint64(m.Id))
	return n
}

func (m *GetGroupAnnouncementByIDResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	if m.Details != nil {
		l = m.Details.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	return n
}

func (m *GetGroupAnnouncementEditAuthReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.GroupId))
	return n
}

func (m *GetGroupAnnouncementEditAuthResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovGroup_(uint64(l))
	}
	n += 1 + sovGroup_(uint64(m.Auth))
	n += 1 + sovGroup_(uint64(m.EditWordCnt))
	return n
}

func sovGroup_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGroup_(x uint64) (n int) {
	return sovGroup_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GroupAddMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupAddMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupAddMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Accounts", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Accounts = append(m.Accounts, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupAddMemberResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupAddMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupAddMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuccessAccounts", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SuccessAccounts = append(m.SuccessAccounts, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FailAccounts", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FailAccounts = append(m.FailAccounts, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CreateMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupRemoveMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupRemoveMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupRemoveMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("mem_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupRemoveMemberResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupRemoveMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupRemoveMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("mem_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupModifyNameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupModifyNameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupModifyNameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupModifyNameResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupModifyNameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupModifyNameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupGetMemberListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupGetMemberListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupGetMemberListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupGetMemberListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupGetMemberListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupGetMemberListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Members", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Members = append(m.Members, &ga.GroupMemSimpleInfo{})
			if err := m.Members[len(m.Members)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupPublishBulletinReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupPublishBulletinReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupPublishBulletinReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupPublishBulletinResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupPublishBulletinResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupPublishBulletinResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BulletinId", wireType)
			}
			m.BulletinId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BulletinId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("bulletin_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("op_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupDeleteBulletinReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupDeleteBulletinReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupDeleteBulletinReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BulletinId", wireType)
			}
			m.BulletinId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BulletinId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("bulletin_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupBatchDelBulletinReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupBatchDelBulletinReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupBatchDelBulletinReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGroup_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.BulletinIds = append(m.BulletinIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGroup_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGroup_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGroup_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.BulletinIds = append(m.BulletinIds, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field BulletinIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupDeleteBulletinResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupDeleteBulletinResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupDeleteBulletinResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupGetBulletinsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupGetBulletinsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupGetBulletinsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BaseBulletinInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BaseBulletinInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BaseBulletinInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTime", wireType)
			}
			m.OpTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BulletinId", wireType)
			}
			m.BulletinId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BulletinId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Author", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Author = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("op_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("bulletin_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("author")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupGetBulletinsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupGetBulletinsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupGetBulletinsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BulletinInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BulletinInfoList = append(m.BulletinInfoList, &BaseBulletinInfo{})
			if err := m.BulletinInfoList[len(m.BulletinInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupAnnouncementListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupAnnouncementListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupAnnouncementListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastId", wireType)
			}
			m.LastId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("last_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupAnnouncementListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupAnnouncementListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupAnnouncementListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Details", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Details = append(m.Details, &GroupAnnouncementDetail{})
			if err := m.Details[len(m.Details)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThisId", wireType)
			}
			m.ThisId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThisId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxCount", wireType)
			}
			m.MaxCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("limit")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("this_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("max_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImTextWithHighlightUrl) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImTextWithHighlightUrl: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImTextWithHighlightUrl: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImContentExt) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImContentExt: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImContentExt: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextHlUrl", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TextHlUrl = append(m.TextHlUrl, &ImTextWithHighlightUrl{})
			if err := m.TextHlUrl[len(m.TextHlUrl)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CanJump", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanJump = bool(v != 0)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Host", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Host = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupAnnouncementDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupAnnouncementDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupAnnouncementDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TextNormal", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TextNormal = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ext", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Ext == nil {
				m.Ext = &ImContentExt{}
			}
			if err := m.Ext.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsChecking", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsChecking = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("text_normal")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrepareApplyGroupAnnouncementReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PrepareApplyGroupAnnouncementReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PrepareApplyGroupAnnouncementReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Detail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Detail == nil {
				m.Detail = &GroupAnnouncementDetail{}
			}
			if err := m.Detail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("detail")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrepareApplyGroupAnnouncementResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PrepareApplyGroupAnnouncementResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PrepareApplyGroupAnnouncementResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelGroupAnnouncementReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelGroupAnnouncementReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelGroupAnnouncementReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelGroupAnnouncementResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelGroupAnnouncementResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelGroupAnnouncementResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupAnnouncementCheckReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupAnnouncementCheckReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupAnnouncementCheckReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupAnnouncementCheckResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupAnnouncementCheckResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupAnnouncementCheckResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Detail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Detail == nil {
				m.Detail = &GroupAnnouncementDetail{}
			}
			if err := m.Detail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			m.Result = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Result |= (GroupAnnouncementSuggestion(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupAnnouncementByIDReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupAnnouncementByIDReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupAnnouncementByIDReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupAnnouncementByIDResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupAnnouncementByIDResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupAnnouncementByIDResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Details", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Details == nil {
				m.Details = &GroupAnnouncementDetail{}
			}
			if err := m.Details.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("details")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupAnnouncementEditAuthReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupAnnouncementEditAuthReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupAnnouncementEditAuthReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGroupAnnouncementEditAuthResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGroupAnnouncementEditAuthResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGroupAnnouncementEditAuthResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGroup_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Auth", wireType)
			}
			m.Auth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Auth |= (GroupAnnouncementEditAuth(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EditWordCnt", wireType)
			}
			m.EditWordCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EditWordCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGroup_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGroup_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("auth")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("edit_word_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGroup_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGroup_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGroup_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGroup_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGroup_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGroup_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGroup_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGroup_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("group_.proto", fileDescriptorGroup_) }

var fileDescriptorGroup_ = []byte{
	// 1364 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0x4f, 0x6f, 0xdb, 0xc6,
	0x12, 0x37, 0x29, 0x5b, 0x7f, 0x46, 0x76, 0x9e, 0x1e, 0x5f, 0xe2, 0xf0, 0x25, 0xb1, 0xad, 0xf0,
	0x25, 0x81, 0x93, 0x83, 0xf3, 0xa0, 0x22, 0x28, 0x50, 0xa0, 0x07, 0xc9, 0x36, 0x1c, 0x05, 0x89,
	0x6a, 0xd0, 0x29, 0x82, 0xf6, 0x42, 0x50, 0xe4, 0x9a, 0xda, 0x84, 0x4b, 0x32, 0xe4, 0xb2, 0x95,
	0x81, 0x1e, 0x8a, 0xa2, 0x68, 0x0b, 0xb4, 0x87, 0x1c, 0x7a, 0x2e, 0x5a, 0xa0, 0x1f, 0xa0, 0xe8,
	0xb5, 0x40, 0xcf, 0x39, 0xf6, 0x13, 0x04, 0x45, 0xfa, 0x45, 0x8a, 0xdd, 0x25, 0x25, 0x4a, 0xa2,
	0xe4, 0xb0, 0xb1, 0x6f, 0xd6, 0xec, 0xec, 0xce, 0x6f, 0x7e, 0xf3, 0xe3, 0xcc, 0xc0, 0xb0, 0xea,
	0x84, 0x7e, 0x1c, 0x18, 0x3b, 0x41, 0xe8, 0x53, 0x5f, 0x91, 0x1d, 0xf3, 0xca, 0x9a, 0x63, 0x1a,
	0x7d, 0x33, 0x42, 0xc2, 0xa4, 0x7d, 0x21, 0xc1, 0xbf, 0x0f, 0x98, 0x4f, 0xdb, 0xb6, 0x1f, 0x21,
	0xd2, 0x47, 0xa1, 0x8e, 0x9e, 0x2b, 0xb7, 0xa0, 0xca, 0x7c, 0x8c, 0x10, 0x3d, 0x57, 0xa5, 0xa6,
	0xbc, 0x5d, 0x6f, 0xd5, 0x77, 0x1c, 0x73, 0xa7, 0x63, 0x46, 0x48, 0x47, 0xcf, 0xf5, 0x4a, 0x5f,
	0xfc, 0xa1, 0x5c, 0x81, 0xaa, 0x69, 0x59, 0x7e, 0xec, 0xd1, 0x48, 0x95, 0x9b, 0xa5, 0xed, 0x9a,
	0x3e, 0xfa, 0xad, 0xdc, 0x86, 0x35, 0x11, 0x3c, 0xb1, 0xa8, 0xa5, 0xa6, 0xb4, 0x5d, 0xeb, 0x2c,
	0xbf, 0x7c, 0xb5, 0xb5, 0xa4, 0x0b, 0x5c, 0x6d, 0x71, 0xa2, 0x7d, 0x29, 0x83, 0x32, 0x0d, 0x22,
	0x0a, 0x94, 0xdb, 0x50, 0x4b, 0x50, 0x44, 0x41, 0x02, 0x63, 0x75, 0x0c, 0x23, 0x0a, 0xf4, 0x6a,
	0x3f, 0xf9, 0x6b, 0x36, 0x98, 0xdc, 0x94, 0xf3, 0x83, 0x29, 0xb7, 0xa1, 0x11, 0xc5, 0x96, 0x85,
	0xa2, 0xc8, 0x18, 0x61, 0x2f, 0x71, 0xec, 0xff, 0x4a, 0xec, 0xed, 0x34, 0x85, 0xff, 0xc1, 0xda,
	0xb1, 0x89, 0xdd, 0xb1, 0xdf, 0x32, 0xf7, 0x5b, 0x65, 0xc6, 0x8c, 0x13, 0x88, 0xd0, 0x9e, 0x49,
	0x90, 0xba, 0x92, 0x49, 0xb2, 0xc6, 0xed, 0x3d, 0x93, 0x20, 0xe6, 0x64, 0x85, 0xc8, 0xa4, 0xc8,
	0x20, 0x91, 0xa3, 0x96, 0xb3, 0x4e, 0xc2, 0xfe, 0x28, 0x72, 0xb4, 0x17, 0x12, 0x5c, 0xe4, 0x34,
	0xe8, 0x88, 0xf8, 0x9f, 0xa0, 0xe2, 0xe5, 0xb8, 0x09, 0x75, 0x82, 0x48, 0x2e, 0x07, 0x40, 0x10,
	0x19, 0x33, 0x30, 0x53, 0x99, 0x39, 0x64, 0x69, 0xdf, 0x4b, 0x70, 0x29, 0x07, 0xd2, 0xb9, 0x15,
	0x67, 0x2a, 0x83, 0x52, 0x7e, 0x06, 0xda, 0x77, 0x52, 0x22, 0x98, 0x47, 0xbe, 0x8d, 0x8f, 0x4f,
	0x18, 0xc5, 0x45, 0x78, 0x2a, 0x00, 0x68, 0xb2, 0xba, 0x59, 0x3c, 0xe3, 0xea, 0xb2, 0xc2, 0xfd,
	0x67, 0x06, 0xce, 0xb9, 0x71, 0xf4, 0x46, 0x90, 0x9e, 0x26, 0x75, 0x3b, 0x40, 0x54, 0x14, 0xed,
	0x21, 0x8e, 0xe8, 0xf9, 0x70, 0xa4, 0xfd, 0x28, 0xc1, 0x7a, 0x5e, 0xb0, 0x73, 0x63, 0xe0, 0xff,
	0x50, 0x21, 0x3c, 0x8e, 0xf8, 0x72, 0xeb, 0xad, 0x75, 0xf6, 0xa6, 0xa8, 0x00, 0x22, 0x47, 0x98,
	0x04, 0x2e, 0xea, 0x7a, 0xc7, 0xbe, 0x9e, 0xba, 0x69, 0x3f, 0x48, 0x70, 0x99, 0x9f, 0x1f, 0xc6,
	0x7d, 0x17, 0x47, 0x83, 0x4e, 0xec, 0xba, 0x88, 0x62, 0xaf, 0x58, 0xb3, 0x5b, 0xa1, 0x98, 0xba,
	0x68, 0x02, 0x98, 0x30, 0x29, 0x9b, 0x50, 0xb1, 0x7c, 0x8f, 0xa2, 0x29, 0xcd, 0xa6, 0x46, 0x65,
	0x0b, 0xaa, 0x22, 0x39, 0x6c, 0xab, 0xcb, 0x4d, 0x79, 0x7b, 0x2d, 0x75, 0xe0, 0xd6, 0xae, 0xad,
	0x7d, 0x2b, 0x81, 0x9a, 0x0f, 0xb0, 0x18, 0x8b, 0x37, 0xa1, 0xde, 0x4f, 0xae, 0xb2, 0x58, 0x72,
	0x26, 0x16, 0xa4, 0x07, 0x5d, 0x5b, 0xd9, 0x80, 0x8a, 0x1f, 0x18, 0x14, 0x27, 0x02, 0x4a, 0x5d,
	0xca, 0x7e, 0xf0, 0x18, 0x13, 0xa4, 0x7d, 0x93, 0x56, 0x74, 0x0f, 0xb9, 0x88, 0xa2, 0x7f, 0xc2,
	0x56, 0x36, 0x63, 0x39, 0x27, 0xe3, 0x69, 0xa4, 0xa5, 0x7c, 0xa4, 0xda, 0x57, 0x29, 0x31, 0x1d,
	0x93, 0x5a, 0x83, 0x3d, 0xe4, 0x9e, 0x0b, 0x98, 0xeb, 0xb0, 0x9a, 0x01, 0x23, 0x64, 0xb5, 0xa6,
	0xd7, 0xc7, 0x38, 0x22, 0x6d, 0x2f, 0x51, 0xd0, 0x34, 0x25, 0x85, 0xea, 0xa3, 0x19, 0x49, 0x8b,
	0x3f, 0x40, 0x34, 0x7d, 0x22, 0x3a, 0xcb, 0x4c, 0xb4, 0x5f, 0x25, 0x68, 0xb0, 0x5b, 0xe9, 0xeb,
	0xec, 0x3b, 0x18, 0x4b, 0x57, 0x5a, 0x28, 0x5d, 0x39, 0x4f, 0xba, 0x8b, 0xa5, 0x32, 0x5d, 0xc6,
	0xe5, 0x39, 0x82, 0xbb, 0x06, 0x65, 0x33, 0xa6, 0x03, 0x3f, 0x54, 0x57, 0x32, 0x41, 0x12, 0x1b,
	0x2b, 0xf2, 0xa5, 0x1c, 0x5a, 0x8a, 0x49, 0xbf, 0x03, 0xca, 0x18, 0x89, 0x77, 0xec, 0x1b, 0x2e,
	0x8e, 0x28, 0x5f, 0x4b, 0xea, 0xad, 0x8b, 0xe9, 0x9d, 0x2c, 0x2d, 0x7a, 0xa3, 0x9f, 0xf9, 0xc5,
	0x7a, 0x96, 0xf6, 0x93, 0x04, 0x57, 0x0f, 0x10, 0x15, 0xcb, 0x88, 0xe7, 0xf9, 0xb1, 0x67, 0x21,
	0x82, 0x3c, 0x5a, 0xb4, 0x7b, 0x9e, 0x2a, 0xb8, 0x0d, 0xa8, 0xb8, 0x66, 0x44, 0xa7, 0x95, 0x5f,
	0x66, 0xc6, 0xae, 0xcd, 0x0a, 0xe6, 0x62, 0x82, 0xe9, 0x04, 0x9f, 0xc2, 0xa4, 0xbd, 0x92, 0xe0,
	0xda, 0x7c, 0x8c, 0xc5, 0x38, 0xbb, 0x07, 0x15, 0x1b, 0x51, 0x13, 0xbb, 0x51, 0x42, 0xd4, 0xd5,
	0x51, 0x27, 0xcd, 0x3e, 0xbd, 0xc7, 0x7d, 0xf4, 0xd4, 0x77, 0x0c, 0xaf, 0x34, 0x03, 0x8f, 0x65,
	0x46, 0x07, 0x38, 0x9a, 0x16, 0x43, 0x99, 0x19, 0xf9, 0x97, 0x56, 0x23, 0xe6, 0xd0, 0x10, 0x2d,
	0x7e, 0x25, 0xe3, 0x50, 0x25, 0xe6, 0x70, 0x97, 0xcf, 0x13, 0x17, 0xd6, 0xbb, 0xe4, 0x31, 0x1a,
	0xd2, 0x27, 0x98, 0x0e, 0xee, 0x63, 0x67, 0xe0, 0x62, 0x67, 0x40, 0x3f, 0x0c, 0xdd, 0xac, 0x56,
	0xa5, 0xcc, 0x0e, 0x35, 0xd2, 0xea, 0x3a, 0x94, 0xe2, 0xd0, 0x55, 0xe5, 0xcc, 0x19, 0x33, 0x30,
	0xbc, 0xd8, 0xb3, 0xd1, 0x90, 0xef, 0xa0, 0x23, 0xbc, 0xdc, 0xa4, 0xfd, 0x2c, 0xc1, 0x6a, 0x97,
	0xec, 0x8a, 0x17, 0xf6, 0x87, 0x54, 0x79, 0x0f, 0xea, 0x14, 0x0d, 0xa9, 0x31, 0x70, 0x0d, 0xf6,
	0x98, 0xc4, 0x79, 0xb9, 0xc2, 0x78, 0xc9, 0x47, 0xa5, 0xd7, 0x98, 0xfb, 0x7d, 0x97, 0x01, 0xdc,
	0x82, 0xaa, 0x65, 0x7a, 0xc6, 0xd3, 0x98, 0x04, 0x1c, 0x45, 0x75, 0x84, 0xd0, 0xf4, 0x1e, 0xc4,
	0x24, 0x60, 0xec, 0x04, 0xd8, 0xe2, 0x0f, 0x67, 0xf7, 0xe1, 0x72, 0x80, 0x2d, 0x76, 0x5f, 0x85,
	0xe5, 0x81, 0x1f, 0xb1, 0xb2, 0x8f, 0xcf, 0xb8, 0x45, 0xfb, 0x5a, 0x4e, 0xfa, 0xcf, 0x6c, 0x5d,
	0x94, 0x8b, 0x20, 0x63, 0x9b, 0x57, 0x3a, 0xcd, 0x4d, 0xc6, 0xbc, 0xc1, 0xf2, 0x3c, 0x3c, 0x3f,
	0x24, 0xa6, 0x3b, 0xb9, 0x0d, 0xb2, 0x83, 0x1e, 0xb7, 0x2b, 0x1a, 0x94, 0xd0, 0x50, 0x6c, 0xe7,
	0xf5, 0x56, 0x43, 0xa4, 0x39, 0x66, 0x43, 0x67, 0x87, 0x8a, 0x06, 0x35, 0xd6, 0x00, 0x22, 0x6a,
	0x92, 0x80, 0x57, 0xb5, 0x94, 0x6e, 0x1c, 0x23, 0xb3, 0xd2, 0x84, 0xaa, 0x87, 0xad, 0x67, 0x33,
	0x5b, 0xf0, 0xc8, 0xca, 0xaa, 0x97, 0xce, 0xf6, 0xec, 0x06, 0x9c, 0x1a, 0x19, 0x60, 0x1c, 0x19,
	0xd6, 0x00, 0x59, 0xcf, 0xb0, 0xe7, 0xa8, 0x95, 0x0c, 0x7f, 0x80, 0xa3, 0xdd, 0xc4, 0xce, 0xd6,
	0x8d, 0xe6, 0x61, 0x88, 0x02, 0x33, 0x44, 0xed, 0x20, 0x70, 0x4f, 0x66, 0x58, 0x29, 0xf2, 0xa1,
	0xbe, 0x03, 0x65, 0x21, 0x6a, 0xce, 0xcf, 0x29, 0xfa, 0x4f, 0x5c, 0x27, 0xbe, 0xee, 0x52, 0x5e,
	0x13, 0xee, 0xc1, 0xf5, 0x53, 0x10, 0x16, 0x9b, 0x1a, 0x43, 0xb8, 0xbc, 0x87, 0xdc, 0xb7, 0x4a,
	0x54, 0x68, 0x44, 0x9e, 0xd2, 0xc8, 0xa9, 0x99, 0xec, 0x83, 0x9a, 0x1f, 0xb9, 0x58, 0x02, 0x4e,
	0x7e, 0xcb, 0xe2, 0x35, 0x3d, 0xd3, 0xf1, 0xf7, 0xbb, 0x04, 0x1b, 0x0b, 0x22, 0x15, 0xeb, 0x8e,
	0x59, 0x71, 0x48, 0x6f, 0x2a, 0x8e, 0xf7, 0xa1, 0x1c, 0xa2, 0x28, 0x76, 0x45, 0x73, 0xbc, 0xd0,
	0xda, 0xca, 0xbd, 0x74, 0x14, 0x3b, 0x0e, 0x8a, 0x28, 0xf6, 0xbd, 0xb4, 0x03, 0x88, 0x4b, 0xda,
	0x67, 0xf9, 0x03, 0xa8, 0x73, 0xd2, 0xdd, 0x3b, 0xd3, 0x01, 0x24, 0xf4, 0x50, 0x9a, 0xd4, 0x83,
	0xf6, 0xf9, 0x9c, 0xd9, 0x22, 0xc2, 0xbf, 0xc5, 0x6c, 0x91, 0xdf, 0x74, 0xb6, 0x68, 0x4f, 0x61,
	0x2b, 0x0f, 0xc1, 0xbe, 0x8d, 0x69, 0x3b, 0xa6, 0x83, 0x33, 0x55, 0xcb, 0x2f, 0x12, 0x34, 0x17,
	0x07, 0x2b, 0x96, 0xf2, 0xbb, 0xb0, 0xcc, 0x36, 0x1a, 0x1e, 0xec, 0x42, 0x6b, 0x23, 0x37, 0xdf,
	0xf4, 0xed, 0xb4, 0xbb, 0xb3, 0x0b, 0xca, 0x36, 0xac, 0x21, 0x1b, 0x53, 0xe3, 0x53, 0x3f, 0xb4,
	0x0d, 0xcb, 0x9b, 0x1c, 0xac, 0x75, 0x76, 0xf4, 0xc4, 0x0f, 0xed, 0x5d, 0x8f, 0xde, 0xf9, 0x8d,
	0x6d, 0x28, 0xf3, 0xd5, 0xa4, 0xdc, 0x80, 0xe6, 0x82, 0x63, 0xe3, 0xb0, 0x7d, 0x74, 0xd4, 0x58,
	0x52, 0x6e, 0x81, 0xb6, 0xc8, 0x4b, 0xdf, 0x7f, 0xb0, 0xbf, 0xfb, 0xb8, 0x21, 0x29, 0xdb, 0x70,
	0x63, 0x91, 0x5f, 0xfb, 0xf0, 0xf0, 0xe1, 0x47, 0xdd, 0xde, 0x41, 0x43, 0x3e, 0x2d, 0x6e, 0xef,
	0x83, 0xde, 0x7e, 0xa3, 0x74, 0x07, 0xc1, 0x7f, 0xe7, 0x12, 0x92, 0xfb, 0x44, 0x7a, 0x98, 0x42,
	0x5a, 0x52, 0x34, 0xd8, 0x9c, 0xef, 0xc5, 0xd3, 0x93, 0x3a, 0xbd, 0x97, 0xaf, 0x37, 0xa5, 0x3f,
	0x5e, 0x6f, 0x4a, 0x7f, 0xbe, 0xde, 0x94, 0x5e, 0xfc, 0xb5, 0xb9, 0x04, 0xaa, 0xe5, 0x93, 0x9d,
	0x13, 0x7c, 0xe2, 0xc7, 0xac, 0x28, 0xc4, 0xb7, 0x91, 0x2b, 0xfe, 0x03, 0xf6, 0xb1, 0xe6, 0xf8,
	0xae, 0xe9, 0x39, 0x3b, 0xf7, 0x5a, 0x94, 0xee, 0x58, 0x3e, 0xb9, 0xcb, 0xcd, 0x96, 0xef, 0xde,
	0x35, 0x83, 0xe0, 0x2e, 0x57, 0xca, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x9e, 0x86, 0xbd, 0xbe,
	0x47, 0x13, 0x00, 0x00,
}
