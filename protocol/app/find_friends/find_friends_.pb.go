// Code generated by protoc-gen-gogo.
// source: find_friends_.proto
// DO NOT EDIT!

/*
	Package find_friends is a generated protocol buffer package.

	It is generated from these files:
		find_friends_.proto

	It has these top-level messages:
		Location2
		FindFriendsSettings
		FindFriendsMyInfo
		FindFriendsGetMyInfoReq
		FindFriendsGetMyInfoResp
		FindFriendsUpdateMyInfoReq
		FindFriendsUpdateMyInfoResp
		FindFriendsUpdatePhotosReq
		FindFriendsUpdatePhotosResp
		UserCard
		FindFriendsGetUserCardsReq
		FindFriendsGetUserCardsResp
		FindFriendsOperateOnCardsReq
		FindFriendsOperateOnCardsResp
		FindFriendsGetGameListReq
		FindFriendsGetGameListResp
		FindFriendsMatedUpMessage
		FindFriendsLikedNotification
		QuickMatchConfiguration
		GetQuickMatchConfigurationsReq
		GetQuickMatchConfigurationsResp
		QuickMatchOption
		QuickMatchGame
		StartQuickMatchReq
		StartQuickMatchResp
		CancelQuickMatchReq
		CancelQuickMatchResp
		QuickMatchKeepAliveReq
		QuickMatchKeepAliveResp
		GetQuickMatchOnlineUsersReq
		GetQuickMatchOnlineUsersResp
		MatchedInfo
		QuickMatchResultNotification
*/
package find_friends

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type FindFriendsSettings_GenderFilter int32

const (
	FindFriendsSettings_UNRESTRICTED FindFriendsSettings_GenderFilter = 0
	FindFriendsSettings_MALE_ONLY    FindFriendsSettings_GenderFilter = 1
	FindFriendsSettings_FEMALE_ONLY  FindFriendsSettings_GenderFilter = 2
)

var FindFriendsSettings_GenderFilter_name = map[int32]string{
	0: "UNRESTRICTED",
	1: "MALE_ONLY",
	2: "FEMALE_ONLY",
}
var FindFriendsSettings_GenderFilter_value = map[string]int32{
	"UNRESTRICTED": 0,
	"MALE_ONLY":    1,
	"FEMALE_ONLY":  2,
}

func (x FindFriendsSettings_GenderFilter) Enum() *FindFriendsSettings_GenderFilter {
	p := new(FindFriendsSettings_GenderFilter)
	*p = x
	return p
}
func (x FindFriendsSettings_GenderFilter) String() string {
	return proto.EnumName(FindFriendsSettings_GenderFilter_name, int32(x))
}
func (x *FindFriendsSettings_GenderFilter) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FindFriendsSettings_GenderFilter_value, data, "FindFriendsSettings_GenderFilter")
	if err != nil {
		return err
	}
	*x = FindFriendsSettings_GenderFilter(value)
	return nil
}
func (FindFriendsSettings_GenderFilter) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{1, 0}
}

type Location2 struct {
	Country  string `protobuf:"bytes,1,opt,name=country" json:"country"`
	Province string `protobuf:"bytes,2,opt,name=province" json:"province"`
	City     string `protobuf:"bytes,3,opt,name=city" json:"city"`
	District string `protobuf:"bytes,4,opt,name=district" json:"district"`
}

func (m *Location2) Reset()                    { *m = Location2{} }
func (m *Location2) String() string            { return proto.CompactTextString(m) }
func (*Location2) ProtoMessage()               {}
func (*Location2) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{0} }

func (m *Location2) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *Location2) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *Location2) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *Location2) GetDistrict() string {
	if m != nil {
		return m.District
	}
	return ""
}

type FindFriendsSettings struct {
	GenderFilter  FindFriendsSettings_GenderFilter `protobuf:"varint,1,req,name=gender_filter,json=genderFilter,enum=ga.FindFriendsSettings_GenderFilter" json:"gender_filter"`
	PlayingGames  []string                         `protobuf:"bytes,2,rep,name=playing_games,json=playingGames" json:"playing_games,omitempty"`
	AutoPlayVoice bool                             `protobuf:"varint,3,req,name=auto_play_voice,json=autoPlayVoice" json:"auto_play_voice"`
}

func (m *FindFriendsSettings) Reset()                    { *m = FindFriendsSettings{} }
func (m *FindFriendsSettings) String() string            { return proto.CompactTextString(m) }
func (*FindFriendsSettings) ProtoMessage()               {}
func (*FindFriendsSettings) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{1} }

func (m *FindFriendsSettings) GetGenderFilter() FindFriendsSettings_GenderFilter {
	if m != nil {
		return m.GenderFilter
	}
	return FindFriendsSettings_UNRESTRICTED
}

func (m *FindFriendsSettings) GetPlayingGames() []string {
	if m != nil {
		return m.PlayingGames
	}
	return nil
}

func (m *FindFriendsSettings) GetAutoPlayVoice() bool {
	if m != nil {
		return m.AutoPlayVoice
	}
	return false
}

type FindFriendsMyInfo struct {
	PhotoUrls     []string             `protobuf:"bytes,1,rep,name=photo_urls,json=photoUrls" json:"photo_urls,omitempty"`
	VoiceUrl      string               `protobuf:"bytes,2,req,name=voice_url,json=voiceUrl" json:"voice_url"`
	VoiceDuration uint32               `protobuf:"varint,3,req,name=voice_duration,json=voiceDuration" json:"voice_duration"`
	Settings      *FindFriendsSettings `protobuf:"bytes,4,req,name=settings" json:"settings,omitempty"`
}

func (m *FindFriendsMyInfo) Reset()                    { *m = FindFriendsMyInfo{} }
func (m *FindFriendsMyInfo) String() string            { return proto.CompactTextString(m) }
func (*FindFriendsMyInfo) ProtoMessage()               {}
func (*FindFriendsMyInfo) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{2} }

func (m *FindFriendsMyInfo) GetPhotoUrls() []string {
	if m != nil {
		return m.PhotoUrls
	}
	return nil
}

func (m *FindFriendsMyInfo) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

func (m *FindFriendsMyInfo) GetVoiceDuration() uint32 {
	if m != nil {
		return m.VoiceDuration
	}
	return 0
}

func (m *FindFriendsMyInfo) GetSettings() *FindFriendsSettings {
	if m != nil {
		return m.Settings
	}
	return nil
}

type FindFriendsGetMyInfoReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *FindFriendsGetMyInfoReq) Reset()         { *m = FindFriendsGetMyInfoReq{} }
func (m *FindFriendsGetMyInfoReq) String() string { return proto.CompactTextString(m) }
func (*FindFriendsGetMyInfoReq) ProtoMessage()    {}
func (*FindFriendsGetMyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{3}
}

func (m *FindFriendsGetMyInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type FindFriendsGetMyInfoResp struct {
	BaseResp      *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	MyInfo        *FindFriendsMyInfo `protobuf:"bytes,2,opt,name=my_info,json=myInfo" json:"my_info,omitempty"`
	FreeLikeQuota uint32             `protobuf:"varint,5,opt,name=free_like_quota,json=freeLikeQuota" json:"free_like_quota"`
}

func (m *FindFriendsGetMyInfoResp) Reset()         { *m = FindFriendsGetMyInfoResp{} }
func (m *FindFriendsGetMyInfoResp) String() string { return proto.CompactTextString(m) }
func (*FindFriendsGetMyInfoResp) ProtoMessage()    {}
func (*FindFriendsGetMyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{4}
}

func (m *FindFriendsGetMyInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FindFriendsGetMyInfoResp) GetMyInfo() *FindFriendsMyInfo {
	if m != nil {
		return m.MyInfo
	}
	return nil
}

func (m *FindFriendsGetMyInfoResp) GetFreeLikeQuota() uint32 {
	if m != nil {
		return m.FreeLikeQuota
	}
	return 0
}

type FindFriendsUpdateMyInfoReq struct {
	BaseReq       *ga.BaseReq          `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Settings      *FindFriendsSettings `protobuf:"bytes,2,opt,name=settings" json:"settings,omitempty"`
	VoiceKey      string               `protobuf:"bytes,3,opt,name=voice_key,json=voiceKey" json:"voice_key"`
	VoiceDuration uint32               `protobuf:"varint,4,opt,name=voice_duration,json=voiceDuration" json:"voice_duration"`
}

func (m *FindFriendsUpdateMyInfoReq) Reset()         { *m = FindFriendsUpdateMyInfoReq{} }
func (m *FindFriendsUpdateMyInfoReq) String() string { return proto.CompactTextString(m) }
func (*FindFriendsUpdateMyInfoReq) ProtoMessage()    {}
func (*FindFriendsUpdateMyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{5}
}

func (m *FindFriendsUpdateMyInfoReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *FindFriendsUpdateMyInfoReq) GetSettings() *FindFriendsSettings {
	if m != nil {
		return m.Settings
	}
	return nil
}

func (m *FindFriendsUpdateMyInfoReq) GetVoiceKey() string {
	if m != nil {
		return m.VoiceKey
	}
	return ""
}

func (m *FindFriendsUpdateMyInfoReq) GetVoiceDuration() uint32 {
	if m != nil {
		return m.VoiceDuration
	}
	return 0
}

type FindFriendsUpdateMyInfoResp struct {
	BaseResp *ga.BaseResp       `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	MyInfo   *FindFriendsMyInfo `protobuf:"bytes,2,opt,name=my_info,json=myInfo" json:"my_info,omitempty"`
}

func (m *FindFriendsUpdateMyInfoResp) Reset()         { *m = FindFriendsUpdateMyInfoResp{} }
func (m *FindFriendsUpdateMyInfoResp) String() string { return proto.CompactTextString(m) }
func (*FindFriendsUpdateMyInfoResp) ProtoMessage()    {}
func (*FindFriendsUpdateMyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{6}
}

func (m *FindFriendsUpdateMyInfoResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FindFriendsUpdateMyInfoResp) GetMyInfo() *FindFriendsMyInfo {
	if m != nil {
		return m.MyInfo
	}
	return nil
}

type FindFriendsUpdatePhotosReq struct {
	BaseReq  *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Index    uint32      `protobuf:"varint,2,req,name=index" json:"index"`
	PhotoKey string      `protobuf:"bytes,3,opt,name=photo_key,json=photoKey" json:"photo_key"`
}

func (m *FindFriendsUpdatePhotosReq) Reset()         { *m = FindFriendsUpdatePhotosReq{} }
func (m *FindFriendsUpdatePhotosReq) String() string { return proto.CompactTextString(m) }
func (*FindFriendsUpdatePhotosReq) ProtoMessage()    {}
func (*FindFriendsUpdatePhotosReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{7}
}

func (m *FindFriendsUpdatePhotosReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *FindFriendsUpdatePhotosReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *FindFriendsUpdatePhotosReq) GetPhotoKey() string {
	if m != nil {
		return m.PhotoKey
	}
	return ""
}

type FindFriendsUpdatePhotosResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	PhotoUrl string       `protobuf:"bytes,2,opt,name=photo_url,json=photoUrl" json:"photo_url"`
}

func (m *FindFriendsUpdatePhotosResp) Reset()         { *m = FindFriendsUpdatePhotosResp{} }
func (m *FindFriendsUpdatePhotosResp) String() string { return proto.CompactTextString(m) }
func (*FindFriendsUpdatePhotosResp) ProtoMessage()    {}
func (*FindFriendsUpdatePhotosResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{8}
}

func (m *FindFriendsUpdatePhotosResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FindFriendsUpdatePhotosResp) GetPhotoUrl() string {
	if m != nil {
		return m.PhotoUrl
	}
	return ""
}

type UserCard struct {
	Uid           uint32     `protobuf:"varint,1,req,name=uid" json:"uid"`
	Username      string     `protobuf:"bytes,2,req,name=username" json:"username"`
	Alias         string     `protobuf:"bytes,3,req,name=alias" json:"alias"`
	Nickname      string     `protobuf:"bytes,4,req,name=nickname" json:"nickname"`
	Gender        uint32     `protobuf:"varint,5,req,name=gender" json:"gender"`
	Location      *Location2 `protobuf:"bytes,6,opt,name=location" json:"location,omitempty"`
	PhotoUrls     []string   `protobuf:"bytes,7,rep,name=photo_urls,json=photoUrls" json:"photo_urls,omitempty"`
	VoiceUrl      string     `protobuf:"bytes,8,req,name=voice_url,json=voiceUrl" json:"voice_url"`
	VoiceDuration uint32     `protobuf:"varint,9,req,name=voice_duration,json=voiceDuration" json:"voice_duration"`
	PlayingGames  []string   `protobuf:"bytes,10,rep,name=playing_games,json=playingGames" json:"playing_games,omitempty"`
	LikedMe       bool       `protobuf:"varint,15,opt,name=liked_me,json=likedMe" json:"liked_me"`
}

func (m *UserCard) Reset()                    { *m = UserCard{} }
func (m *UserCard) String() string            { return proto.CompactTextString(m) }
func (*UserCard) ProtoMessage()               {}
func (*UserCard) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{9} }

func (m *UserCard) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserCard) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *UserCard) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *UserCard) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserCard) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *UserCard) GetLocation() *Location2 {
	if m != nil {
		return m.Location
	}
	return nil
}

func (m *UserCard) GetPhotoUrls() []string {
	if m != nil {
		return m.PhotoUrls
	}
	return nil
}

func (m *UserCard) GetVoiceUrl() string {
	if m != nil {
		return m.VoiceUrl
	}
	return ""
}

func (m *UserCard) GetVoiceDuration() uint32 {
	if m != nil {
		return m.VoiceDuration
	}
	return 0
}

func (m *UserCard) GetPlayingGames() []string {
	if m != nil {
		return m.PlayingGames
	}
	return nil
}

func (m *UserCard) GetLikedMe() bool {
	if m != nil {
		return m.LikedMe
	}
	return false
}

type FindFriendsGetUserCardsReq struct {
	BaseReq    *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	SessionId  string      `protobuf:"bytes,2,opt,name=session_id,json=sessionId" json:"session_id"`
	StartIndex uint32      `protobuf:"varint,3,req,name=start_index,json=startIndex" json:"start_index"`
	Count      uint32      `protobuf:"varint,4,req,name=count" json:"count"`
}

func (m *FindFriendsGetUserCardsReq) Reset()         { *m = FindFriendsGetUserCardsReq{} }
func (m *FindFriendsGetUserCardsReq) String() string { return proto.CompactTextString(m) }
func (*FindFriendsGetUserCardsReq) ProtoMessage()    {}
func (*FindFriendsGetUserCardsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{10}
}

func (m *FindFriendsGetUserCardsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *FindFriendsGetUserCardsReq) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *FindFriendsGetUserCardsReq) GetStartIndex() uint32 {
	if m != nil {
		return m.StartIndex
	}
	return 0
}

func (m *FindFriendsGetUserCardsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type FindFriendsGetUserCardsResp struct {
	BaseResp  *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	SessionId string       `protobuf:"bytes,2,req,name=session_id,json=sessionId" json:"session_id"`
	UserCards []*UserCard  `protobuf:"bytes,3,rep,name=user_cards,json=userCards" json:"user_cards,omitempty"`
}

func (m *FindFriendsGetUserCardsResp) Reset()         { *m = FindFriendsGetUserCardsResp{} }
func (m *FindFriendsGetUserCardsResp) String() string { return proto.CompactTextString(m) }
func (*FindFriendsGetUserCardsResp) ProtoMessage()    {}
func (*FindFriendsGetUserCardsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{11}
}

func (m *FindFriendsGetUserCardsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FindFriendsGetUserCardsResp) GetSessionId() string {
	if m != nil {
		return m.SessionId
	}
	return ""
}

func (m *FindFriendsGetUserCardsResp) GetUserCards() []*UserCard {
	if m != nil {
		return m.UserCards
	}
	return nil
}

type FindFriendsOperateOnCardsReq struct {
	BaseReq   *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	ThrowUids []uint32    `protobuf:"varint,2,rep,name=throw_uids,json=throwUids" json:"throw_uids,omitempty"`
	LikeUid   uint32      `protobuf:"varint,3,opt,name=like_uid,json=likeUid" json:"like_uid"`
}

func (m *FindFriendsOperateOnCardsReq) Reset()         { *m = FindFriendsOperateOnCardsReq{} }
func (m *FindFriendsOperateOnCardsReq) String() string { return proto.CompactTextString(m) }
func (*FindFriendsOperateOnCardsReq) ProtoMessage()    {}
func (*FindFriendsOperateOnCardsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{12}
}

func (m *FindFriendsOperateOnCardsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *FindFriendsOperateOnCardsReq) GetThrowUids() []uint32 {
	if m != nil {
		return m.ThrowUids
	}
	return nil
}

func (m *FindFriendsOperateOnCardsReq) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

type FindFriendsOperateOnCardsResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	// 以下字段仅在FindFriendsOperateOnCardsReq中的like_uid有值时返回
	RedDiamondsCost  uint32 `protobuf:"varint,2,opt,name=red_diamonds_cost,json=redDiamondsCost" json:"red_diamonds_cost"`
	RedDiamondsBonus uint32 `protobuf:"varint,3,opt,name=red_diamonds_bonus,json=redDiamondsBonus" json:"red_diamonds_bonus"`
	MatedUp          bool   `protobuf:"varint,4,opt,name=mated_up,json=matedUp" json:"mated_up"`
	FreeLikeQuota    uint32 `protobuf:"varint,5,opt,name=free_like_quota,json=freeLikeQuota" json:"free_like_quota"`
}

func (m *FindFriendsOperateOnCardsResp) Reset()         { *m = FindFriendsOperateOnCardsResp{} }
func (m *FindFriendsOperateOnCardsResp) String() string { return proto.CompactTextString(m) }
func (*FindFriendsOperateOnCardsResp) ProtoMessage()    {}
func (*FindFriendsOperateOnCardsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{13}
}

func (m *FindFriendsOperateOnCardsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FindFriendsOperateOnCardsResp) GetRedDiamondsCost() uint32 {
	if m != nil {
		return m.RedDiamondsCost
	}
	return 0
}

func (m *FindFriendsOperateOnCardsResp) GetRedDiamondsBonus() uint32 {
	if m != nil {
		return m.RedDiamondsBonus
	}
	return 0
}

func (m *FindFriendsOperateOnCardsResp) GetMatedUp() bool {
	if m != nil {
		return m.MatedUp
	}
	return false
}

func (m *FindFriendsOperateOnCardsResp) GetFreeLikeQuota() uint32 {
	if m != nil {
		return m.FreeLikeQuota
	}
	return 0
}

type FindFriendsGetGameListReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *FindFriendsGetGameListReq) Reset()         { *m = FindFriendsGetGameListReq{} }
func (m *FindFriendsGetGameListReq) String() string { return proto.CompactTextString(m) }
func (*FindFriendsGetGameListReq) ProtoMessage()    {}
func (*FindFriendsGetGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{14}
}

func (m *FindFriendsGetGameListReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type FindFriendsGetGameListResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Games    []string     `protobuf:"bytes,2,rep,name=games" json:"games,omitempty"`
}

func (m *FindFriendsGetGameListResp) Reset()         { *m = FindFriendsGetGameListResp{} }
func (m *FindFriendsGetGameListResp) String() string { return proto.CompactTextString(m) }
func (*FindFriendsGetGameListResp) ProtoMessage()    {}
func (*FindFriendsGetGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{15}
}

func (m *FindFriendsGetGameListResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *FindFriendsGetGameListResp) GetGames() []string {
	if m != nil {
		return m.Games
	}
	return nil
}

// im.proto: IM_MSG_TYPE = FIND_FRIENDS_MATED_UP_MSG
type FindFriendsMatedUpMessage struct {
	UserCard *UserCard `protobuf:"bytes,1,req,name=user_card,json=userCard" json:"user_card,omitempty"`
}

func (m *FindFriendsMatedUpMessage) Reset()         { *m = FindFriendsMatedUpMessage{} }
func (m *FindFriendsMatedUpMessage) String() string { return proto.CompactTextString(m) }
func (*FindFriendsMatedUpMessage) ProtoMessage()    {}
func (*FindFriendsMatedUpMessage) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{16}
}

func (m *FindFriendsMatedUpMessage) GetUserCard() *UserCard {
	if m != nil {
		return m.UserCard
	}
	return nil
}

//
// NOTE:
// 服务器推送一条固定MsgID的消息，每次都设置为未读，客户端覆盖存储，liked_count_during_afk为0时不显示。
// 服务器处理同步时需要对旧版本屏蔽该消息。
type FindFriendsLikedNotification struct {
	LikedCountDuringAfk    uint32   `protobuf:"varint,1,req,name=liked_count_during_afk,json=likedCountDuringAfk" json:"liked_count_during_afk"`
	Message                string   `protobuf:"bytes,2,req,name=message" json:"message"`
	Charm                  uint32   `protobuf:"varint,3,req,name=charm" json:"charm"`
	AvatarsOfRecentLikedMe []string `protobuf:"bytes,4,rep,name=avatars_of_recent_liked_me,json=avatarsOfRecentLikedMe" json:"avatars_of_recent_liked_me,omitempty"`
}

func (m *FindFriendsLikedNotification) Reset()         { *m = FindFriendsLikedNotification{} }
func (m *FindFriendsLikedNotification) String() string { return proto.CompactTextString(m) }
func (*FindFriendsLikedNotification) ProtoMessage()    {}
func (*FindFriendsLikedNotification) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{17}
}

func (m *FindFriendsLikedNotification) GetLikedCountDuringAfk() uint32 {
	if m != nil {
		return m.LikedCountDuringAfk
	}
	return 0
}

func (m *FindFriendsLikedNotification) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *FindFriendsLikedNotification) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *FindFriendsLikedNotification) GetAvatarsOfRecentLikedMe() []string {
	if m != nil {
		return m.AvatarsOfRecentLikedMe
	}
	return nil
}

// 快速匹配配置
type QuickMatchConfiguration struct {
	GameId       uint32                            `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	GameName     string                            `protobuf:"bytes,2,req,name=game_name,json=gameName" json:"game_name"`
	IconUrl      string                            `protobuf:"bytes,3,req,name=icon_url,json=iconUrl" json:"icon_url"`
	Options      []*QuickMatchConfiguration_Option `protobuf:"bytes,4,rep,name=options" json:"options,omitempty"`
	ChannelTagId uint32                            `protobuf:"varint,5,opt,name=channel_tag_id,json=channelTagId" json:"channel_tag_id"`
}

func (m *QuickMatchConfiguration) Reset()         { *m = QuickMatchConfiguration{} }
func (m *QuickMatchConfiguration) String() string { return proto.CompactTextString(m) }
func (*QuickMatchConfiguration) ProtoMessage()    {}
func (*QuickMatchConfiguration) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{18}
}

func (m *QuickMatchConfiguration) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *QuickMatchConfiguration) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *QuickMatchConfiguration) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *QuickMatchConfiguration) GetOptions() []*QuickMatchConfiguration_Option {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *QuickMatchConfiguration) GetChannelTagId() uint32 {
	if m != nil {
		return m.ChannelTagId
	}
	return 0
}

type QuickMatchConfiguration_Value struct {
	Value uint32 `protobuf:"varint,1,req,name=value" json:"value"`
	Name  string `protobuf:"bytes,2,req,name=name" json:"name"`
}

func (m *QuickMatchConfiguration_Value) Reset()         { *m = QuickMatchConfiguration_Value{} }
func (m *QuickMatchConfiguration_Value) String() string { return proto.CompactTextString(m) }
func (*QuickMatchConfiguration_Value) ProtoMessage()    {}
func (*QuickMatchConfiguration_Value) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{18, 0}
}

func (m *QuickMatchConfiguration_Value) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *QuickMatchConfiguration_Value) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type QuickMatchConfiguration_Option struct {
	Key    string                           `protobuf:"bytes,1,req,name=key" json:"key"`
	Title  string                           `protobuf:"bytes,2,req,name=title" json:"title"`
	Values []*QuickMatchConfiguration_Value `protobuf:"bytes,3,rep,name=values" json:"values,omitempty"`
}

func (m *QuickMatchConfiguration_Option) Reset()         { *m = QuickMatchConfiguration_Option{} }
func (m *QuickMatchConfiguration_Option) String() string { return proto.CompactTextString(m) }
func (*QuickMatchConfiguration_Option) ProtoMessage()    {}
func (*QuickMatchConfiguration_Option) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{18, 1}
}

func (m *QuickMatchConfiguration_Option) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *QuickMatchConfiguration_Option) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *QuickMatchConfiguration_Option) GetValues() []*QuickMatchConfiguration_Value {
	if m != nil {
		return m.Values
	}
	return nil
}

type GetQuickMatchConfigurationsReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Version uint32      `protobuf:"varint,2,req,name=version" json:"version"`
}

func (m *GetQuickMatchConfigurationsReq) Reset()         { *m = GetQuickMatchConfigurationsReq{} }
func (m *GetQuickMatchConfigurationsReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchConfigurationsReq) ProtoMessage()    {}
func (*GetQuickMatchConfigurationsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{19}
}

func (m *GetQuickMatchConfigurationsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetQuickMatchConfigurationsReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetQuickMatchConfigurationsResp struct {
	BaseResp       *ga.BaseResp               `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Version        uint32                     `protobuf:"varint,2,req,name=version" json:"version"`
	Configurations []*QuickMatchConfiguration `protobuf:"bytes,3,rep,name=configurations" json:"configurations,omitempty"`
}

func (m *GetQuickMatchConfigurationsResp) Reset()         { *m = GetQuickMatchConfigurationsResp{} }
func (m *GetQuickMatchConfigurationsResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchConfigurationsResp) ProtoMessage()    {}
func (*GetQuickMatchConfigurationsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{20}
}

func (m *GetQuickMatchConfigurationsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetQuickMatchConfigurationsResp) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *GetQuickMatchConfigurationsResp) GetConfigurations() []*QuickMatchConfiguration {
	if m != nil {
		return m.Configurations
	}
	return nil
}

type QuickMatchOption struct {
	Key   string `protobuf:"bytes,1,req,name=key" json:"key"`
	Value uint32 `protobuf:"varint,2,req,name=value" json:"value"`
}

func (m *QuickMatchOption) Reset()                    { *m = QuickMatchOption{} }
func (m *QuickMatchOption) String() string            { return proto.CompactTextString(m) }
func (*QuickMatchOption) ProtoMessage()               {}
func (*QuickMatchOption) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{21} }

func (m *QuickMatchOption) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *QuickMatchOption) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type QuickMatchGame struct {
	GameName string              `protobuf:"bytes,1,req,name=game_name,json=gameName" json:"game_name"`
	Options  []*QuickMatchOption `protobuf:"bytes,2,rep,name=options" json:"options,omitempty"`
}

func (m *QuickMatchGame) Reset()                    { *m = QuickMatchGame{} }
func (m *QuickMatchGame) String() string            { return proto.CompactTextString(m) }
func (*QuickMatchGame) ProtoMessage()               {}
func (*QuickMatchGame) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{22} }

func (m *QuickMatchGame) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *QuickMatchGame) GetOptions() []*QuickMatchOption {
	if m != nil {
		return m.Options
	}
	return nil
}

type StartQuickMatchReq struct {
	BaseReq  *ga.BaseReq         `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	GameName string              `protobuf:"bytes,2,req,name=game_name,json=gameName" json:"game_name"`
	Options  []*QuickMatchOption `protobuf:"bytes,3,rep,name=options" json:"options,omitempty"`
	// 新版本新增字段
	Games          []*QuickMatchGame                  `protobuf:"bytes,4,rep,name=games" json:"games,omitempty"`
	SupplementInfo *StartQuickMatchReq_SupplementInfo `protobuf:"bytes,5,opt,name=supplement_info,json=supplementInfo" json:"supplement_info,omitempty"`
}

func (m *StartQuickMatchReq) Reset()                    { *m = StartQuickMatchReq{} }
func (m *StartQuickMatchReq) String() string            { return proto.CompactTextString(m) }
func (*StartQuickMatchReq) ProtoMessage()               {}
func (*StartQuickMatchReq) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{23} }

func (m *StartQuickMatchReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *StartQuickMatchReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *StartQuickMatchReq) GetOptions() []*QuickMatchOption {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *StartQuickMatchReq) GetGames() []*QuickMatchGame {
	if m != nil {
		return m.Games
	}
	return nil
}

func (m *StartQuickMatchReq) GetSupplementInfo() *StartQuickMatchReq_SupplementInfo {
	if m != nil {
		return m.SupplementInfo
	}
	return nil
}

// 补位信息
type StartQuickMatchReq_SupplementInfo struct {
	ChannelId     uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	SupplementNum uint32 `protobuf:"varint,2,req,name=supplement_num,json=supplementNum" json:"supplement_num"`
}

func (m *StartQuickMatchReq_SupplementInfo) Reset()         { *m = StartQuickMatchReq_SupplementInfo{} }
func (m *StartQuickMatchReq_SupplementInfo) String() string { return proto.CompactTextString(m) }
func (*StartQuickMatchReq_SupplementInfo) ProtoMessage()    {}
func (*StartQuickMatchReq_SupplementInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{23, 0}
}

func (m *StartQuickMatchReq_SupplementInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartQuickMatchReq_SupplementInfo) GetSupplementNum() uint32 {
	if m != nil {
		return m.SupplementNum
	}
	return 0
}

type StartQuickMatchResp struct {
	BaseResp        *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	MaxDuration     uint32       `protobuf:"varint,2,req,name=max_duration,json=maxDuration" json:"max_duration"`
	MatchId         string       `protobuf:"bytes,3,req,name=match_id,json=matchId" json:"match_id"`
	MatchedInfo     *MatchedInfo `protobuf:"bytes,4,opt,name=matched_info,json=matchedInfo" json:"matched_info,omitempty"`
	SequenceInQueue uint32       `protobuf:"varint,5,opt,name=sequence_in_queue,json=sequenceInQueue" json:"sequence_in_queue"`
}

func (m *StartQuickMatchResp) Reset()                    { *m = StartQuickMatchResp{} }
func (m *StartQuickMatchResp) String() string            { return proto.CompactTextString(m) }
func (*StartQuickMatchResp) ProtoMessage()               {}
func (*StartQuickMatchResp) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{24} }

func (m *StartQuickMatchResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *StartQuickMatchResp) GetMaxDuration() uint32 {
	if m != nil {
		return m.MaxDuration
	}
	return 0
}

func (m *StartQuickMatchResp) GetMatchId() string {
	if m != nil {
		return m.MatchId
	}
	return ""
}

func (m *StartQuickMatchResp) GetMatchedInfo() *MatchedInfo {
	if m != nil {
		return m.MatchedInfo
	}
	return nil
}

func (m *StartQuickMatchResp) GetSequenceInQueue() uint32 {
	if m != nil {
		return m.SequenceInQueue
	}
	return 0
}

type CancelQuickMatchReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	MatchId string      `protobuf:"bytes,2,req,name=match_id,json=matchId" json:"match_id"`
}

func (m *CancelQuickMatchReq) Reset()                    { *m = CancelQuickMatchReq{} }
func (m *CancelQuickMatchReq) String() string            { return proto.CompactTextString(m) }
func (*CancelQuickMatchReq) ProtoMessage()               {}
func (*CancelQuickMatchReq) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{25} }

func (m *CancelQuickMatchReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CancelQuickMatchReq) GetMatchId() string {
	if m != nil {
		return m.MatchId
	}
	return ""
}

type CancelQuickMatchResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
}

func (m *CancelQuickMatchResp) Reset()         { *m = CancelQuickMatchResp{} }
func (m *CancelQuickMatchResp) String() string { return proto.CompactTextString(m) }
func (*CancelQuickMatchResp) ProtoMessage()    {}
func (*CancelQuickMatchResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{26}
}

func (m *CancelQuickMatchResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type QuickMatchKeepAliveReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	MatchId string      `protobuf:"bytes,2,req,name=match_id,json=matchId" json:"match_id"`
}

func (m *QuickMatchKeepAliveReq) Reset()         { *m = QuickMatchKeepAliveReq{} }
func (m *QuickMatchKeepAliveReq) String() string { return proto.CompactTextString(m) }
func (*QuickMatchKeepAliveReq) ProtoMessage()    {}
func (*QuickMatchKeepAliveReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{27}
}

func (m *QuickMatchKeepAliveReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *QuickMatchKeepAliveReq) GetMatchId() string {
	if m != nil {
		return m.MatchId
	}
	return ""
}

type QuickMatchKeepAliveResp struct {
	BaseResp        *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	MatchId         string       `protobuf:"bytes,2,opt,name=match_id,json=matchId" json:"match_id"`
	SequenceInQueue uint32       `protobuf:"varint,3,opt,name=sequence_in_queue,json=sequenceInQueue" json:"sequence_in_queue"`
}

func (m *QuickMatchKeepAliveResp) Reset()         { *m = QuickMatchKeepAliveResp{} }
func (m *QuickMatchKeepAliveResp) String() string { return proto.CompactTextString(m) }
func (*QuickMatchKeepAliveResp) ProtoMessage()    {}
func (*QuickMatchKeepAliveResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{28}
}

func (m *QuickMatchKeepAliveResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *QuickMatchKeepAliveResp) GetMatchId() string {
	if m != nil {
		return m.MatchId
	}
	return ""
}

func (m *QuickMatchKeepAliveResp) GetSequenceInQueue() uint32 {
	if m != nil {
		return m.SequenceInQueue
	}
	return 0
}

type GetQuickMatchOnlineUsersReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetQuickMatchOnlineUsersReq) Reset()         { *m = GetQuickMatchOnlineUsersReq{} }
func (m *GetQuickMatchOnlineUsersReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchOnlineUsersReq) ProtoMessage()    {}
func (*GetQuickMatchOnlineUsersReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{29}
}

func (m *GetQuickMatchOnlineUsersReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetQuickMatchOnlineUsersResp struct {
	BaseResp    *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	OnlineUsers uint32       `protobuf:"varint,2,req,name=online_users,json=onlineUsers" json:"online_users"`
}

func (m *GetQuickMatchOnlineUsersResp) Reset()         { *m = GetQuickMatchOnlineUsersResp{} }
func (m *GetQuickMatchOnlineUsersResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchOnlineUsersResp) ProtoMessage()    {}
func (*GetQuickMatchOnlineUsersResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{30}
}

func (m *GetQuickMatchOnlineUsersResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetQuickMatchOnlineUsersResp) GetOnlineUsers() uint32 {
	if m != nil {
		return m.OnlineUsers
	}
	return 0
}

type MatchedInfo struct {
	ChannelId    uint32            `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	PeerUid      uint32            `protobuf:"varint,2,req,name=peer_uid,json=peerUid" json:"peer_uid"`
	PeerAccount  string            `protobuf:"bytes,3,req,name=peer_account,json=peerAccount" json:"peer_account"`
	PeerAlias    string            `protobuf:"bytes,4,req,name=peer_alias,json=peerAlias" json:"peer_alias"`
	PeerNickname string            `protobuf:"bytes,5,req,name=peer_nickname,json=peerNickname" json:"peer_nickname"`
	PeerGameId   uint32            `protobuf:"varint,6,req,name=peer_game_id,json=peerGameId" json:"peer_game_id"`
	PeerGender   uint32            `protobuf:"varint,7,req,name=peer_gender,json=peerGender" json:"peer_gender"`
	PeerGameName string            `protobuf:"bytes,8,req,name=peer_game_name,json=peerGameName" json:"peer_game_name"`
	PeerGames    []*QuickMatchGame `protobuf:"bytes,9,rep,name=peer_games,json=peerGames" json:"peer_games,omitempty"`
}

func (m *MatchedInfo) Reset()                    { *m = MatchedInfo{} }
func (m *MatchedInfo) String() string            { return proto.CompactTextString(m) }
func (*MatchedInfo) ProtoMessage()               {}
func (*MatchedInfo) Descriptor() ([]byte, []int) { return fileDescriptorFindFriends_, []int{31} }

func (m *MatchedInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MatchedInfo) GetPeerUid() uint32 {
	if m != nil {
		return m.PeerUid
	}
	return 0
}

func (m *MatchedInfo) GetPeerAccount() string {
	if m != nil {
		return m.PeerAccount
	}
	return ""
}

func (m *MatchedInfo) GetPeerAlias() string {
	if m != nil {
		return m.PeerAlias
	}
	return ""
}

func (m *MatchedInfo) GetPeerNickname() string {
	if m != nil {
		return m.PeerNickname
	}
	return ""
}

func (m *MatchedInfo) GetPeerGameId() uint32 {
	if m != nil {
		return m.PeerGameId
	}
	return 0
}

func (m *MatchedInfo) GetPeerGender() uint32 {
	if m != nil {
		return m.PeerGender
	}
	return 0
}

func (m *MatchedInfo) GetPeerGameName() string {
	if m != nil {
		return m.PeerGameName
	}
	return ""
}

func (m *MatchedInfo) GetPeerGames() []*QuickMatchGame {
	if m != nil {
		return m.PeerGames
	}
	return nil
}

type QuickMatchResultNotification struct {
	MatchedInfo     *MatchedInfo   `protobuf:"bytes,1,opt,name=matched_info,json=matchedInfo" json:"matched_info,omitempty"`
	MatchId         string         `protobuf:"bytes,2,req,name=match_id,json=matchId" json:"match_id"`
	MatchedInfoList []*MatchedInfo `protobuf:"bytes,3,rep,name=matched_info_list,json=matchedInfoList" json:"matched_info_list,omitempty"`
}

func (m *QuickMatchResultNotification) Reset()         { *m = QuickMatchResultNotification{} }
func (m *QuickMatchResultNotification) String() string { return proto.CompactTextString(m) }
func (*QuickMatchResultNotification) ProtoMessage()    {}
func (*QuickMatchResultNotification) Descriptor() ([]byte, []int) {
	return fileDescriptorFindFriends_, []int{32}
}

func (m *QuickMatchResultNotification) GetMatchedInfo() *MatchedInfo {
	if m != nil {
		return m.MatchedInfo
	}
	return nil
}

func (m *QuickMatchResultNotification) GetMatchId() string {
	if m != nil {
		return m.MatchId
	}
	return ""
}

func (m *QuickMatchResultNotification) GetMatchedInfoList() []*MatchedInfo {
	if m != nil {
		return m.MatchedInfoList
	}
	return nil
}

func init() {
	proto.RegisterType((*Location2)(nil), "ga.Location2")
	proto.RegisterType((*FindFriendsSettings)(nil), "ga.FindFriendsSettings")
	proto.RegisterType((*FindFriendsMyInfo)(nil), "ga.FindFriendsMyInfo")
	proto.RegisterType((*FindFriendsGetMyInfoReq)(nil), "ga.FindFriendsGetMyInfoReq")
	proto.RegisterType((*FindFriendsGetMyInfoResp)(nil), "ga.FindFriendsGetMyInfoResp")
	proto.RegisterType((*FindFriendsUpdateMyInfoReq)(nil), "ga.FindFriendsUpdateMyInfoReq")
	proto.RegisterType((*FindFriendsUpdateMyInfoResp)(nil), "ga.FindFriendsUpdateMyInfoResp")
	proto.RegisterType((*FindFriendsUpdatePhotosReq)(nil), "ga.FindFriendsUpdatePhotosReq")
	proto.RegisterType((*FindFriendsUpdatePhotosResp)(nil), "ga.FindFriendsUpdatePhotosResp")
	proto.RegisterType((*UserCard)(nil), "ga.UserCard")
	proto.RegisterType((*FindFriendsGetUserCardsReq)(nil), "ga.FindFriendsGetUserCardsReq")
	proto.RegisterType((*FindFriendsGetUserCardsResp)(nil), "ga.FindFriendsGetUserCardsResp")
	proto.RegisterType((*FindFriendsOperateOnCardsReq)(nil), "ga.FindFriendsOperateOnCardsReq")
	proto.RegisterType((*FindFriendsOperateOnCardsResp)(nil), "ga.FindFriendsOperateOnCardsResp")
	proto.RegisterType((*FindFriendsGetGameListReq)(nil), "ga.FindFriendsGetGameListReq")
	proto.RegisterType((*FindFriendsGetGameListResp)(nil), "ga.FindFriendsGetGameListResp")
	proto.RegisterType((*FindFriendsMatedUpMessage)(nil), "ga.FindFriendsMatedUpMessage")
	proto.RegisterType((*FindFriendsLikedNotification)(nil), "ga.FindFriendsLikedNotification")
	proto.RegisterType((*QuickMatchConfiguration)(nil), "ga.QuickMatchConfiguration")
	proto.RegisterType((*QuickMatchConfiguration_Value)(nil), "ga.QuickMatchConfiguration.Value")
	proto.RegisterType((*QuickMatchConfiguration_Option)(nil), "ga.QuickMatchConfiguration.Option")
	proto.RegisterType((*GetQuickMatchConfigurationsReq)(nil), "ga.GetQuickMatchConfigurationsReq")
	proto.RegisterType((*GetQuickMatchConfigurationsResp)(nil), "ga.GetQuickMatchConfigurationsResp")
	proto.RegisterType((*QuickMatchOption)(nil), "ga.QuickMatchOption")
	proto.RegisterType((*QuickMatchGame)(nil), "ga.QuickMatchGame")
	proto.RegisterType((*StartQuickMatchReq)(nil), "ga.StartQuickMatchReq")
	proto.RegisterType((*StartQuickMatchReq_SupplementInfo)(nil), "ga.StartQuickMatchReq.SupplementInfo")
	proto.RegisterType((*StartQuickMatchResp)(nil), "ga.StartQuickMatchResp")
	proto.RegisterType((*CancelQuickMatchReq)(nil), "ga.CancelQuickMatchReq")
	proto.RegisterType((*CancelQuickMatchResp)(nil), "ga.CancelQuickMatchResp")
	proto.RegisterType((*QuickMatchKeepAliveReq)(nil), "ga.QuickMatchKeepAliveReq")
	proto.RegisterType((*QuickMatchKeepAliveResp)(nil), "ga.QuickMatchKeepAliveResp")
	proto.RegisterType((*GetQuickMatchOnlineUsersReq)(nil), "ga.GetQuickMatchOnlineUsersReq")
	proto.RegisterType((*GetQuickMatchOnlineUsersResp)(nil), "ga.GetQuickMatchOnlineUsersResp")
	proto.RegisterType((*MatchedInfo)(nil), "ga.MatchedInfo")
	proto.RegisterType((*QuickMatchResultNotification)(nil), "ga.QuickMatchResultNotification")
	proto.RegisterEnum("ga.FindFriendsSettings_GenderFilter", FindFriendsSettings_GenderFilter_name, FindFriendsSettings_GenderFilter_value)
}
func (m *Location2) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Location2) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Country)))
	i += copy(dAtA[i:], m.Country)
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Province)))
	i += copy(dAtA[i:], m.Province)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.City)))
	i += copy(dAtA[i:], m.City)
	dAtA[i] = 0x22
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.District)))
	i += copy(dAtA[i:], m.District)
	return i, nil
}

func (m *FindFriendsSettings) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsSettings) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.GenderFilter))
	if len(m.PlayingGames) > 0 {
		for _, s := range m.PlayingGames {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x18
	i++
	if m.AutoPlayVoice {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *FindFriendsMyInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsMyInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PhotoUrls) > 0 {
		for _, s := range m.PhotoUrls {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.VoiceUrl)))
	i += copy(dAtA[i:], m.VoiceUrl)
	dAtA[i] = 0x18
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.VoiceDuration))
	if m.Settings == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("settings")
	} else {
		dAtA[i] = 0x22
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.Settings.Size()))
		n1, err := m.Settings.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *FindFriendsGetMyInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsGetMyInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n2, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *FindFriendsGetMyInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsGetMyInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n3, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.MyInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.MyInfo.Size()))
		n4, err := m.MyInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.FreeLikeQuota))
	return i, nil
}

func (m *FindFriendsUpdateMyInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsUpdateMyInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.Settings != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.Settings.Size()))
		n6, err := m.Settings.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.VoiceKey)))
	i += copy(dAtA[i:], m.VoiceKey)
	dAtA[i] = 0x20
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.VoiceDuration))
	return i, nil
}

func (m *FindFriendsUpdateMyInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsUpdateMyInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n7, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.MyInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.MyInfo.Size()))
		n8, err := m.MyInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *FindFriendsUpdatePhotosReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsUpdatePhotosReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.PhotoKey)))
	i += copy(dAtA[i:], m.PhotoKey)
	return i, nil
}

func (m *FindFriendsUpdatePhotosResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsUpdatePhotosResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.PhotoUrl)))
	i += copy(dAtA[i:], m.PhotoUrl)
	return i, nil
}

func (m *UserCard) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserCard) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Username)))
	i += copy(dAtA[i:], m.Username)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Alias)))
	i += copy(dAtA[i:], m.Alias)
	dAtA[i] = 0x22
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x28
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.Gender))
	if m.Location != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.Location.Size()))
		n11, err := m.Location.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if len(m.PhotoUrls) > 0 {
		for _, s := range m.PhotoUrls {
			dAtA[i] = 0x3a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x42
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.VoiceUrl)))
	i += copy(dAtA[i:], m.VoiceUrl)
	dAtA[i] = 0x48
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.VoiceDuration))
	if len(m.PlayingGames) > 0 {
		for _, s := range m.PlayingGames {
			dAtA[i] = 0x52
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x78
	i++
	if m.LikedMe {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *FindFriendsGetUserCardsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsGetUserCardsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n12, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	dAtA[i] = 0x18
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.StartIndex))
	dAtA[i] = 0x20
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *FindFriendsGetUserCardsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsGetUserCardsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n13, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.SessionId)))
	i += copy(dAtA[i:], m.SessionId)
	if len(m.UserCards) > 0 {
		for _, msg := range m.UserCards {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FindFriendsOperateOnCardsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsOperateOnCardsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n14, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	if len(m.ThrowUids) > 0 {
		for _, num := range m.ThrowUids {
			dAtA[i] = 0x10
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.LikeUid))
	return i, nil
}

func (m *FindFriendsOperateOnCardsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsOperateOnCardsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n15, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.RedDiamondsCost))
	dAtA[i] = 0x18
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.RedDiamondsBonus))
	dAtA[i] = 0x20
	i++
	if m.MatedUp {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.FreeLikeQuota))
	return i, nil
}

func (m *FindFriendsGetGameListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsGetGameListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n16, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	return i, nil
}

func (m *FindFriendsGetGameListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsGetGameListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n17, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	if len(m.Games) > 0 {
		for _, s := range m.Games {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *FindFriendsMatedUpMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsMatedUpMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserCard == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_card")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.UserCard.Size()))
		n18, err := m.UserCard.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	return i, nil
}

func (m *FindFriendsLikedNotification) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindFriendsLikedNotification) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.LikedCountDuringAfk))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Message)))
	i += copy(dAtA[i:], m.Message)
	dAtA[i] = 0x18
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.Charm))
	if len(m.AvatarsOfRecentLikedMe) > 0 {
		for _, s := range m.AvatarsOfRecentLikedMe {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *QuickMatchConfiguration) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuickMatchConfiguration) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.IconUrl)))
	i += copy(dAtA[i:], m.IconUrl)
	if len(m.Options) > 0 {
		for _, msg := range m.Options {
			dAtA[i] = 0x22
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.ChannelTagId))
	return i, nil
}

func (m *QuickMatchConfiguration_Value) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuickMatchConfiguration_Value) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.Value))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	return i, nil
}

func (m *QuickMatchConfiguration_Option) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuickMatchConfiguration_Option) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	if len(m.Values) > 0 {
		for _, msg := range m.Values {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetQuickMatchConfigurationsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQuickMatchConfigurationsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n19, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.Version))
	return i, nil
}

func (m *GetQuickMatchConfigurationsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQuickMatchConfigurationsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n20, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.Version))
	if len(m.Configurations) > 0 {
		for _, msg := range m.Configurations {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *QuickMatchOption) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuickMatchOption) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.Value))
	return i, nil
}

func (m *QuickMatchGame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuickMatchGame) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	if len(m.Options) > 0 {
		for _, msg := range m.Options {
			dAtA[i] = 0x12
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StartQuickMatchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StartQuickMatchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n21, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	if len(m.Options) > 0 {
		for _, msg := range m.Options {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Games) > 0 {
		for _, msg := range m.Games {
			dAtA[i] = 0x22
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.SupplementInfo != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.SupplementInfo.Size()))
		n22, err := m.SupplementInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	return i, nil
}

func (m *StartQuickMatchReq_SupplementInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StartQuickMatchReq_SupplementInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.SupplementNum))
	return i, nil
}

func (m *StartQuickMatchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StartQuickMatchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n23, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.MaxDuration))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.MatchId)))
	i += copy(dAtA[i:], m.MatchId)
	if m.MatchedInfo != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.MatchedInfo.Size()))
		n24, err := m.MatchedInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.SequenceInQueue))
	return i, nil
}

func (m *CancelQuickMatchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelQuickMatchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n25, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.MatchId)))
	i += copy(dAtA[i:], m.MatchId)
	return i, nil
}

func (m *CancelQuickMatchResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelQuickMatchResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n26, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	return i, nil
}

func (m *QuickMatchKeepAliveReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuickMatchKeepAliveReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n27, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.MatchId)))
	i += copy(dAtA[i:], m.MatchId)
	return i, nil
}

func (m *QuickMatchKeepAliveResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuickMatchKeepAliveResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n28, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.MatchId)))
	i += copy(dAtA[i:], m.MatchId)
	dAtA[i] = 0x18
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.SequenceInQueue))
	return i, nil
}

func (m *GetQuickMatchOnlineUsersReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQuickMatchOnlineUsersReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseReq.Size()))
		n29, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	return i, nil
}

func (m *GetQuickMatchOnlineUsersResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQuickMatchOnlineUsersResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.BaseResp.Size()))
		n30, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.OnlineUsers))
	return i, nil
}

func (m *MatchedInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchedInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.PeerUid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.PeerAccount)))
	i += copy(dAtA[i:], m.PeerAccount)
	dAtA[i] = 0x22
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.PeerAlias)))
	i += copy(dAtA[i:], m.PeerAlias)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.PeerNickname)))
	i += copy(dAtA[i:], m.PeerNickname)
	dAtA[i] = 0x30
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.PeerGameId))
	dAtA[i] = 0x38
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(m.PeerGender))
	dAtA[i] = 0x42
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.PeerGameName)))
	i += copy(dAtA[i:], m.PeerGameName)
	if len(m.PeerGames) > 0 {
		for _, msg := range m.PeerGames {
			dAtA[i] = 0x4a
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *QuickMatchResultNotification) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuickMatchResultNotification) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MatchedInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFindFriends_(dAtA, i, uint64(m.MatchedInfo.Size()))
		n31, err := m.MatchedInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintFindFriends_(dAtA, i, uint64(len(m.MatchId)))
	i += copy(dAtA[i:], m.MatchId)
	if len(m.MatchedInfoList) > 0 {
		for _, msg := range m.MatchedInfoList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintFindFriends_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64FindFriends_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32FindFriends_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintFindFriends_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *Location2) Size() (n int) {
	var l int
	_ = l
	l = len(m.Country)
	n += 1 + l + sovFindFriends_(uint64(l))
	l = len(m.Province)
	n += 1 + l + sovFindFriends_(uint64(l))
	l = len(m.City)
	n += 1 + l + sovFindFriends_(uint64(l))
	l = len(m.District)
	n += 1 + l + sovFindFriends_(uint64(l))
	return n
}

func (m *FindFriendsSettings) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindFriends_(uint64(m.GenderFilter))
	if len(m.PlayingGames) > 0 {
		for _, s := range m.PlayingGames {
			l = len(s)
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *FindFriendsMyInfo) Size() (n int) {
	var l int
	_ = l
	if len(m.PhotoUrls) > 0 {
		for _, s := range m.PhotoUrls {
			l = len(s)
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	l = len(m.VoiceUrl)
	n += 1 + l + sovFindFriends_(uint64(l))
	n += 1 + sovFindFriends_(uint64(m.VoiceDuration))
	if m.Settings != nil {
		l = m.Settings.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	return n
}

func (m *FindFriendsGetMyInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	return n
}

func (m *FindFriendsGetMyInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	if m.MyInfo != nil {
		l = m.MyInfo.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	n += 1 + sovFindFriends_(uint64(m.FreeLikeQuota))
	return n
}

func (m *FindFriendsUpdateMyInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	if m.Settings != nil {
		l = m.Settings.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	l = len(m.VoiceKey)
	n += 1 + l + sovFindFriends_(uint64(l))
	n += 1 + sovFindFriends_(uint64(m.VoiceDuration))
	return n
}

func (m *FindFriendsUpdateMyInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	if m.MyInfo != nil {
		l = m.MyInfo.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	return n
}

func (m *FindFriendsUpdatePhotosReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	n += 1 + sovFindFriends_(uint64(m.Index))
	l = len(m.PhotoKey)
	n += 1 + l + sovFindFriends_(uint64(l))
	return n
}

func (m *FindFriendsUpdatePhotosResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	l = len(m.PhotoUrl)
	n += 1 + l + sovFindFriends_(uint64(l))
	return n
}

func (m *UserCard) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindFriends_(uint64(m.Uid))
	l = len(m.Username)
	n += 1 + l + sovFindFriends_(uint64(l))
	l = len(m.Alias)
	n += 1 + l + sovFindFriends_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovFindFriends_(uint64(l))
	n += 1 + sovFindFriends_(uint64(m.Gender))
	if m.Location != nil {
		l = m.Location.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	if len(m.PhotoUrls) > 0 {
		for _, s := range m.PhotoUrls {
			l = len(s)
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	l = len(m.VoiceUrl)
	n += 1 + l + sovFindFriends_(uint64(l))
	n += 1 + sovFindFriends_(uint64(m.VoiceDuration))
	if len(m.PlayingGames) > 0 {
		for _, s := range m.PlayingGames {
			l = len(s)
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *FindFriendsGetUserCardsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	l = len(m.SessionId)
	n += 1 + l + sovFindFriends_(uint64(l))
	n += 1 + sovFindFriends_(uint64(m.StartIndex))
	n += 1 + sovFindFriends_(uint64(m.Count))
	return n
}

func (m *FindFriendsGetUserCardsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	l = len(m.SessionId)
	n += 1 + l + sovFindFriends_(uint64(l))
	if len(m.UserCards) > 0 {
		for _, e := range m.UserCards {
			l = e.Size()
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	return n
}

func (m *FindFriendsOperateOnCardsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	if len(m.ThrowUids) > 0 {
		for _, e := range m.ThrowUids {
			n += 1 + sovFindFriends_(uint64(e))
		}
	}
	n += 1 + sovFindFriends_(uint64(m.LikeUid))
	return n
}

func (m *FindFriendsOperateOnCardsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	n += 1 + sovFindFriends_(uint64(m.RedDiamondsCost))
	n += 1 + sovFindFriends_(uint64(m.RedDiamondsBonus))
	n += 2
	n += 1 + sovFindFriends_(uint64(m.FreeLikeQuota))
	return n
}

func (m *FindFriendsGetGameListReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	return n
}

func (m *FindFriendsGetGameListResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	if len(m.Games) > 0 {
		for _, s := range m.Games {
			l = len(s)
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	return n
}

func (m *FindFriendsMatedUpMessage) Size() (n int) {
	var l int
	_ = l
	if m.UserCard != nil {
		l = m.UserCard.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	return n
}

func (m *FindFriendsLikedNotification) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindFriends_(uint64(m.LikedCountDuringAfk))
	l = len(m.Message)
	n += 1 + l + sovFindFriends_(uint64(l))
	n += 1 + sovFindFriends_(uint64(m.Charm))
	if len(m.AvatarsOfRecentLikedMe) > 0 {
		for _, s := range m.AvatarsOfRecentLikedMe {
			l = len(s)
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	return n
}

func (m *QuickMatchConfiguration) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindFriends_(uint64(m.GameId))
	l = len(m.GameName)
	n += 1 + l + sovFindFriends_(uint64(l))
	l = len(m.IconUrl)
	n += 1 + l + sovFindFriends_(uint64(l))
	if len(m.Options) > 0 {
		for _, e := range m.Options {
			l = e.Size()
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	n += 1 + sovFindFriends_(uint64(m.ChannelTagId))
	return n
}

func (m *QuickMatchConfiguration_Value) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindFriends_(uint64(m.Value))
	l = len(m.Name)
	n += 1 + l + sovFindFriends_(uint64(l))
	return n
}

func (m *QuickMatchConfiguration_Option) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovFindFriends_(uint64(l))
	l = len(m.Title)
	n += 1 + l + sovFindFriends_(uint64(l))
	if len(m.Values) > 0 {
		for _, e := range m.Values {
			l = e.Size()
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	return n
}

func (m *GetQuickMatchConfigurationsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	n += 1 + sovFindFriends_(uint64(m.Version))
	return n
}

func (m *GetQuickMatchConfigurationsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	n += 1 + sovFindFriends_(uint64(m.Version))
	if len(m.Configurations) > 0 {
		for _, e := range m.Configurations {
			l = e.Size()
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	return n
}

func (m *QuickMatchOption) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovFindFriends_(uint64(l))
	n += 1 + sovFindFriends_(uint64(m.Value))
	return n
}

func (m *QuickMatchGame) Size() (n int) {
	var l int
	_ = l
	l = len(m.GameName)
	n += 1 + l + sovFindFriends_(uint64(l))
	if len(m.Options) > 0 {
		for _, e := range m.Options {
			l = e.Size()
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	return n
}

func (m *StartQuickMatchReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	l = len(m.GameName)
	n += 1 + l + sovFindFriends_(uint64(l))
	if len(m.Options) > 0 {
		for _, e := range m.Options {
			l = e.Size()
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	if len(m.Games) > 0 {
		for _, e := range m.Games {
			l = e.Size()
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	if m.SupplementInfo != nil {
		l = m.SupplementInfo.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	return n
}

func (m *StartQuickMatchReq_SupplementInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindFriends_(uint64(m.ChannelId))
	n += 1 + sovFindFriends_(uint64(m.SupplementNum))
	return n
}

func (m *StartQuickMatchResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	n += 1 + sovFindFriends_(uint64(m.MaxDuration))
	l = len(m.MatchId)
	n += 1 + l + sovFindFriends_(uint64(l))
	if m.MatchedInfo != nil {
		l = m.MatchedInfo.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	n += 1 + sovFindFriends_(uint64(m.SequenceInQueue))
	return n
}

func (m *CancelQuickMatchReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	l = len(m.MatchId)
	n += 1 + l + sovFindFriends_(uint64(l))
	return n
}

func (m *CancelQuickMatchResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	return n
}

func (m *QuickMatchKeepAliveReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	l = len(m.MatchId)
	n += 1 + l + sovFindFriends_(uint64(l))
	return n
}

func (m *QuickMatchKeepAliveResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	l = len(m.MatchId)
	n += 1 + l + sovFindFriends_(uint64(l))
	n += 1 + sovFindFriends_(uint64(m.SequenceInQueue))
	return n
}

func (m *GetQuickMatchOnlineUsersReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	return n
}

func (m *GetQuickMatchOnlineUsersResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	n += 1 + sovFindFriends_(uint64(m.OnlineUsers))
	return n
}

func (m *MatchedInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFindFriends_(uint64(m.ChannelId))
	n += 1 + sovFindFriends_(uint64(m.PeerUid))
	l = len(m.PeerAccount)
	n += 1 + l + sovFindFriends_(uint64(l))
	l = len(m.PeerAlias)
	n += 1 + l + sovFindFriends_(uint64(l))
	l = len(m.PeerNickname)
	n += 1 + l + sovFindFriends_(uint64(l))
	n += 1 + sovFindFriends_(uint64(m.PeerGameId))
	n += 1 + sovFindFriends_(uint64(m.PeerGender))
	l = len(m.PeerGameName)
	n += 1 + l + sovFindFriends_(uint64(l))
	if len(m.PeerGames) > 0 {
		for _, e := range m.PeerGames {
			l = e.Size()
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	return n
}

func (m *QuickMatchResultNotification) Size() (n int) {
	var l int
	_ = l
	if m.MatchedInfo != nil {
		l = m.MatchedInfo.Size()
		n += 1 + l + sovFindFriends_(uint64(l))
	}
	l = len(m.MatchId)
	n += 1 + l + sovFindFriends_(uint64(l))
	if len(m.MatchedInfoList) > 0 {
		for _, e := range m.MatchedInfoList {
			l = e.Size()
			n += 1 + l + sovFindFriends_(uint64(l))
		}
	}
	return n
}

func sovFindFriends_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozFindFriends_(x uint64) (n int) {
	return sovFindFriends_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *Location2) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Location2: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Location2: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Country", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Country = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Province", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Province = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.City = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field District", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.District = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsSettings) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsSettings: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsSettings: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GenderFilter", wireType)
			}
			m.GenderFilter = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GenderFilter |= (FindFriendsSettings_GenderFilter(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlayingGames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PlayingGames = append(m.PlayingGames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AutoPlayVoice", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AutoPlayVoice = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gender_filter")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("auto_play_voice")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsMyInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsMyInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsMyInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhotoUrls = append(m.PhotoUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoiceUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoiceUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoiceDuration", wireType)
			}
			m.VoiceDuration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoiceDuration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Settings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Settings == nil {
				m.Settings = &FindFriendsSettings{}
			}
			if err := m.Settings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("voice_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("voice_duration")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("settings")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsGetMyInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsGetMyInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsGetMyInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsGetMyInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsGetMyInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsGetMyInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MyInfo == nil {
				m.MyInfo = &FindFriendsMyInfo{}
			}
			if err := m.MyInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FreeLikeQuota", wireType)
			}
			m.FreeLikeQuota = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreeLikeQuota |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsUpdateMyInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsUpdateMyInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsUpdateMyInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Settings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Settings == nil {
				m.Settings = &FindFriendsSettings{}
			}
			if err := m.Settings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoiceKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoiceKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoiceDuration", wireType)
			}
			m.VoiceDuration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoiceDuration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsUpdateMyInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsUpdateMyInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsUpdateMyInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MyInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MyInfo == nil {
				m.MyInfo = &FindFriendsMyInfo{}
			}
			if err := m.MyInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsUpdatePhotosReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsUpdatePhotosReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsUpdatePhotosReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhotoKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("index")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsUpdatePhotosResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsUpdatePhotosResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsUpdatePhotosResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhotoUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserCard) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserCard: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserCard: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Username", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Username = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Alias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Alias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Gender", wireType)
			}
			m.Gender = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Gender |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Location", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Location == nil {
				m.Location = &Location2{}
			}
			if err := m.Location.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhotoUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhotoUrls = append(m.PhotoUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoiceUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoiceUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoiceDuration", wireType)
			}
			m.VoiceDuration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoiceDuration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlayingGames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PlayingGames = append(m.PlayingGames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikedMe", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.LikedMe = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("username")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("alias")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("gender")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("voice_url")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("voice_duration")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsGetUserCardsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsGetUserCardsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsGetUserCardsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartIndex", wireType)
			}
			m.StartIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("start_index")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsGetUserCardsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsGetUserCardsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsGetUserCardsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SessionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SessionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserCards", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserCards = append(m.UserCards, &UserCard{})
			if err := m.UserCards[len(m.UserCards)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("session_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsOperateOnCardsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsOperateOnCardsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsOperateOnCardsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindFriends_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ThrowUids = append(m.ThrowUids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFindFriends_
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFindFriends_
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFindFriends_
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ThrowUids = append(m.ThrowUids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ThrowUids", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUid", wireType)
			}
			m.LikeUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsOperateOnCardsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsOperateOnCardsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsOperateOnCardsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamondsCost", wireType)
			}
			m.RedDiamondsCost = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamondsCost |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamondsBonus", wireType)
			}
			m.RedDiamondsBonus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamondsBonus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatedUp", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MatedUp = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FreeLikeQuota", wireType)
			}
			m.FreeLikeQuota = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreeLikeQuota |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsGetGameListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsGetGameListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsGetGameListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsGetGameListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsGetGameListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsGetGameListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Games", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Games = append(m.Games, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsMatedUpMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsMatedUpMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsMatedUpMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserCard", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserCard == nil {
				m.UserCard = &UserCard{}
			}
			if err := m.UserCard.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("user_card")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindFriendsLikedNotification) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FindFriendsLikedNotification: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FindFriendsLikedNotification: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikedCountDuringAfk", wireType)
			}
			m.LikedCountDuringAfk = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikedCountDuringAfk |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Charm", wireType)
			}
			m.Charm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Charm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AvatarsOfRecentLikedMe", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AvatarsOfRecentLikedMe = append(m.AvatarsOfRecentLikedMe, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("liked_count_during_afk")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("message")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("charm")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuickMatchConfiguration) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuickMatchConfiguration: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuickMatchConfiguration: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Options", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Options = append(m.Options, &QuickMatchConfiguration_Option{})
			if err := m.Options[len(m.Options)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelTagId", wireType)
			}
			m.ChannelTagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelTagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("icon_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuickMatchConfiguration_Value) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Value: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Value: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("value")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuickMatchConfiguration_Option) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Option: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Option: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Values = append(m.Values, &QuickMatchConfiguration_Value{})
			if err := m.Values[len(m.Values)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("title")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQuickMatchConfigurationsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQuickMatchConfigurationsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQuickMatchConfigurationsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("version")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQuickMatchConfigurationsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQuickMatchConfigurationsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQuickMatchConfigurationsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Configurations", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Configurations = append(m.Configurations, &QuickMatchConfiguration{})
			if err := m.Configurations[len(m.Configurations)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("version")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuickMatchOption) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuickMatchOption: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuickMatchOption: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuickMatchGame) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuickMatchGame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuickMatchGame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Options", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Options = append(m.Options, &QuickMatchOption{})
			if err := m.Options[len(m.Options)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StartQuickMatchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StartQuickMatchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StartQuickMatchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Options", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Options = append(m.Options, &QuickMatchOption{})
			if err := m.Options[len(m.Options)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Games", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Games = append(m.Games, &QuickMatchGame{})
			if err := m.Games[len(m.Games)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SupplementInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SupplementInfo == nil {
				m.SupplementInfo = &StartQuickMatchReq_SupplementInfo{}
			}
			if err := m.SupplementInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("game_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StartQuickMatchReq_SupplementInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SupplementInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SupplementInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SupplementNum", wireType)
			}
			m.SupplementNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SupplementNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("supplement_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StartQuickMatchResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StartQuickMatchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StartQuickMatchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxDuration", wireType)
			}
			m.MaxDuration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxDuration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MatchId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchedInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MatchedInfo == nil {
				m.MatchedInfo = &MatchedInfo{}
			}
			if err := m.MatchedInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SequenceInQueue", wireType)
			}
			m.SequenceInQueue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SequenceInQueue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("max_duration")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("match_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelQuickMatchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CancelQuickMatchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CancelQuickMatchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MatchId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("match_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelQuickMatchResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CancelQuickMatchResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CancelQuickMatchResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuickMatchKeepAliveReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuickMatchKeepAliveReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuickMatchKeepAliveReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MatchId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("match_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuickMatchKeepAliveResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuickMatchKeepAliveResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuickMatchKeepAliveResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MatchId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SequenceInQueue", wireType)
			}
			m.SequenceInQueue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SequenceInQueue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQuickMatchOnlineUsersReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQuickMatchOnlineUsersReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQuickMatchOnlineUsersReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQuickMatchOnlineUsersResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQuickMatchOnlineUsersResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQuickMatchOnlineUsersResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineUsers", wireType)
			}
			m.OnlineUsers = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineUsers |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("online_users")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchedInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchedInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchedInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerUid", wireType)
			}
			m.PeerUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PeerAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerAlias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PeerAlias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerNickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PeerNickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerGameId", wireType)
			}
			m.PeerGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerGender", wireType)
			}
			m.PeerGender = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerGender |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerGameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PeerGameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerGames", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PeerGames = append(m.PeerGames, &QuickMatchGame{})
			if err := m.PeerGames[len(m.PeerGames)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("peer_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("peer_account")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("peer_alias")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("peer_nickname")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("peer_game_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("peer_gender")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("peer_game_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuickMatchResultNotification) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuickMatchResultNotification: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuickMatchResultNotification: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchedInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MatchedInfo == nil {
				m.MatchedInfo = &MatchedInfo{}
			}
			if err := m.MatchedInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MatchId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchedInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFindFriends_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MatchedInfoList = append(m.MatchedInfoList, &MatchedInfo{})
			if err := m.MatchedInfoList[len(m.MatchedInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFindFriends_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFindFriends_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("match_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipFindFriends_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFindFriends_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFindFriends_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthFindFriends_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowFindFriends_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipFindFriends_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthFindFriends_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFindFriends_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("find_friends_.proto", fileDescriptorFindFriends_) }

var fileDescriptorFindFriends_ = []byte{
	// 1868 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xcd, 0x6f, 0x1c, 0x49,
	0x15, 0x4f, 0xcf, 0xd8, 0x9e, 0x99, 0x37, 0x1e, 0x3b, 0x69, 0x87, 0xa4, 0x71, 0x12, 0x7b, 0xb6,
	0x97, 0x2c, 0xce, 0xee, 0x6a, 0x02, 0x83, 0x38, 0x2c, 0x1f, 0x12, 0x8e, 0x63, 0x5b, 0x56, 0xfc,
	0xb1, 0x99, 0x64, 0x56, 0x02, 0x09, 0x5a, 0xe5, 0xee, 0x9a, 0x71, 0xc9, 0xfd, 0xe5, 0xae, 0x6a,
	0x6f, 0xe6, 0x86, 0x84, 0x84, 0xc4, 0x8d, 0x1b, 0x12, 0x07, 0x2e, 0x5c, 0x39, 0xc0, 0x89, 0x7f,
	0x61, 0xb9, 0xc1, 0x3f, 0x80, 0x50, 0x38, 0x23, 0x71, 0x86, 0x03, 0xe8, 0x55, 0x75, 0xcf, 0x54,
	0xcf, 0x8c, 0x1d, 0x37, 0x62, 0x6f, 0x9e, 0xdf, 0x7b, 0xf5, 0xde, 0xab, 0xf7, 0xf1, 0xab, 0xd7,
	0x86, 0xb5, 0x01, 0x0b, 0x3d, 0x67, 0x90, 0x30, 0x1a, 0x7a, 0xdc, 0xe9, 0xc4, 0x49, 0x24, 0x22,
	0xb3, 0x32, 0x24, 0xeb, 0xad, 0x21, 0x71, 0x4e, 0x09, 0xa7, 0x0a, 0xb2, 0x7f, 0x61, 0x40, 0xe3,
	0x30, 0x72, 0x89, 0x60, 0x51, 0xd8, 0x35, 0x37, 0xa0, 0xe6, 0x46, 0x69, 0x28, 0x92, 0x91, 0x65,
	0xb4, 0x8d, 0xad, 0xc6, 0xb3, 0x85, 0x2f, 0xfe, 0xba, 0x79, 0xab, 0x97, 0x83, 0x66, 0x1b, 0xea,
	0x71, 0x12, 0x5d, 0xb2, 0xd0, 0xa5, 0x56, 0x45, 0x53, 0x18, 0xa3, 0xa6, 0x05, 0x0b, 0x2e, 0x13,
	0x23, 0xab, 0xaa, 0x49, 0x25, 0x82, 0x67, 0x3d, 0xc6, 0x45, 0xc2, 0x5c, 0x61, 0x2d, 0xe8, 0x67,
	0x73, 0xd4, 0xfe, 0xb7, 0x01, 0x6b, 0x7b, 0x2c, 0xf4, 0xf6, 0x54, 0xd4, 0xaf, 0xa8, 0x10, 0x2c,
	0x1c, 0x72, 0xf3, 0x04, 0x5a, 0x43, 0x1a, 0x7a, 0x34, 0x71, 0x06, 0xcc, 0x17, 0x34, 0xb1, 0x8c,
	0x76, 0x65, 0x6b, 0xa5, 0xfb, 0xb5, 0xce, 0x90, 0x74, 0xe6, 0xe8, 0x77, 0xf6, 0xa5, 0xf2, 0x9e,
	0xd4, 0xcd, 0x9c, 0x2c, 0x0f, 0x35, 0xcc, 0x7c, 0x1f, 0x5a, 0xb1, 0x4f, 0x46, 0x2c, 0x1c, 0x3a,
	0x43, 0x12, 0x50, 0x6e, 0x55, 0xda, 0xd5, 0xad, 0x46, 0x6f, 0x39, 0x03, 0xf7, 0x11, 0x33, 0x3f,
	0x86, 0x55, 0x92, 0x8a, 0xc8, 0x41, 0xd0, 0xb9, 0x8c, 0x98, 0x4b, 0xad, 0x6a, 0xbb, 0xb2, 0x55,
	0xcf, 0x2c, 0xb6, 0x50, 0xf8, 0xa9, 0x4f, 0x46, 0x9f, 0xa1, 0xc8, 0xfe, 0x01, 0x2c, 0xeb, 0x6e,
	0xcd, 0xdb, 0xb0, 0xdc, 0x3f, 0xee, 0xed, 0xbe, 0x7a, 0xdd, 0x3b, 0xd8, 0x79, 0xbd, 0xfb, 0xfc,
	0xf6, 0x2d, 0xb3, 0x05, 0x8d, 0xa3, 0xed, 0xc3, 0x5d, 0xe7, 0xe4, 0xf8, 0xf0, 0x87, 0xb7, 0x0d,
	0x73, 0x15, 0x9a, 0x7b, 0xbb, 0x13, 0xa0, 0x62, 0xff, 0xd1, 0x80, 0x3b, 0xda, 0x6d, 0x8e, 0x46,
	0x07, 0xe1, 0x20, 0x32, 0x1f, 0x01, 0xc4, 0x67, 0x91, 0x88, 0x9c, 0x34, 0xf1, 0xb9, 0x65, 0xc8,
	0x38, 0x1b, 0x12, 0xe9, 0x27, 0x3e, 0x37, 0xdf, 0x83, 0x86, 0x0c, 0x0d, 0xc5, 0x56, 0xa5, 0x5d,
	0x99, 0x64, 0x55, 0xc2, 0xfd, 0xc4, 0x37, 0x3f, 0x82, 0x15, 0xa5, 0xe2, 0xa5, 0x89, 0x2c, 0xb3,
	0xbc, 0x46, 0x2b, 0xbf, 0x86, 0x94, 0x3d, 0xcf, 0x44, 0xe6, 0xb7, 0xa0, 0xce, 0xb3, 0x34, 0x5a,
	0x0b, 0xed, 0xca, 0x56, 0xb3, 0x7b, 0xff, 0x8a, 0x2c, 0xf7, 0xc6, 0x8a, 0xf6, 0x36, 0xdc, 0xd7,
	0x14, 0xf6, 0xa9, 0x50, 0xb1, 0xf7, 0xe8, 0x85, 0xf9, 0x01, 0xd4, 0xb1, 0xd9, 0x9c, 0x84, 0x5e,
	0xc8, 0xaa, 0x35, 0xbb, 0x4d, 0xb4, 0xf7, 0x8c, 0x70, 0xda, 0xa3, 0x17, 0xbd, 0xda, 0xa9, 0xfa,
	0xc3, 0xfe, 0xad, 0x01, 0xd6, 0x7c, 0x1b, 0x3c, 0x36, 0x9f, 0x40, 0x23, 0x33, 0xc2, 0xe3, 0xcc,
	0xca, 0xf2, 0xc4, 0x0a, 0x8f, 0x7b, 0xf5, 0xd3, 0xec, 0x2f, 0xb3, 0x03, 0xb5, 0x60, 0xe4, 0xb0,
	0x70, 0x10, 0xc9, 0xfe, 0x6c, 0x76, 0xbf, 0x32, 0x15, 0x7e, 0x66, 0x76, 0x29, 0x50, 0xe9, 0xfd,
	0x18, 0x56, 0x07, 0x09, 0xa5, 0x8e, 0xcf, 0xce, 0xa9, 0x73, 0x91, 0x46, 0x82, 0x58, 0x8b, 0x6d,
	0x63, 0x92, 0x1d, 0x14, 0x1e, 0xb2, 0x73, 0xfa, 0x12, 0x45, 0xf6, 0x9f, 0x0c, 0x58, 0xd7, 0x6c,
	0xf5, 0x63, 0x8f, 0x08, 0x5a, 0xfa, 0xb2, 0x85, 0x24, 0xab, 0x28, 0xdf, 0x9d, 0xe4, 0x49, 0xa5,
	0xcf, 0x69, 0x71, 0xba, 0x54, 0xa5, 0x5f, 0xd0, 0xd1, 0x9c, 0x4a, 0x2f, 0xe8, 0x77, 0x29, 0x54,
	0xda, 0x7e, 0x03, 0x0f, 0xae, 0xbc, 0xca, 0x97, 0x9a, 0x73, 0xfb, 0x67, 0xf3, 0xb2, 0xf8, 0x29,
	0xb6, 0x34, 0x2f, 0x93, 0xc5, 0x75, 0x58, 0x64, 0xa1, 0x47, 0xdf, 0xc8, 0xb6, 0xcf, 0x2f, 0xa9,
	0x20, 0x4c, 0x96, 0x9a, 0x9a, 0x99, 0x64, 0x49, 0xf8, 0x05, 0x1d, 0xd9, 0xe7, 0x73, 0xee, 0x9f,
	0x07, 0x51, 0xee, 0xfe, 0x63, 0x67, 0x6a, 0x06, 0xa7, 0x9d, 0xf5, 0x13, 0xdf, 0xfe, 0x69, 0x15,
	0xea, 0x7d, 0x4e, 0x93, 0x1d, 0x92, 0x78, 0xe6, 0x3d, 0xa8, 0xa6, 0xcc, 0x93, 0x46, 0xf3, 0xb0,
	0x11, 0x40, 0x82, 0x4c, 0x39, 0x4d, 0x42, 0x12, 0xd0, 0xe2, 0x28, 0xe7, 0x28, 0x5e, 0x99, 0xf8,
	0x8c, 0x70, 0x39, 0xc1, 0xb9, 0x58, 0x41, 0x78, 0x3a, 0x64, 0xee, 0xb9, 0x3c, 0xbd, 0xa0, 0x9f,
	0xce, 0x51, 0xf3, 0x21, 0x2c, 0x29, 0x16, 0xb4, 0x16, 0x35, 0xd7, 0x19, 0x66, 0x3e, 0x81, 0xba,
	0x9f, 0xbd, 0x03, 0xd6, 0x92, 0x2c, 0x63, 0x0b, 0xef, 0x3b, 0x7e, 0x1b, 0x7a, 0x63, 0xf1, 0x14,
	0x27, 0xd5, 0xae, 0xe5, 0xa4, 0xfa, 0x0d, 0x39, 0xa9, 0x71, 0x35, 0x27, 0xcd, 0xb0, 0x35, 0xcc,
	0x61, 0xeb, 0x4d, 0xa8, 0xe3, 0x0c, 0x7b, 0x4e, 0x40, 0xad, 0xd5, 0xb6, 0x31, 0xa6, 0xe9, 0x9a,
	0x44, 0x8f, 0xa8, 0xfd, 0xbb, 0x62, 0xd7, 0xed, 0x53, 0x91, 0x17, 0xa4, 0x54, 0xd7, 0xbd, 0x0f,
	0xc0, 0x29, 0xe7, 0x2c, 0x0a, 0x1d, 0xe6, 0x15, 0xaa, 0xdd, 0xc8, 0xf0, 0x03, 0xcf, 0x7c, 0x0c,
	0x4d, 0x2e, 0x48, 0x22, 0x1c, 0xd5, 0xa0, 0x3a, 0xdf, 0x82, 0x14, 0x1c, 0xc8, 0x2e, 0x5d, 0x87,
	0x45, 0xf9, 0xb0, 0xca, 0x7a, 0x8d, 0x3b, 0x58, 0x42, 0xf6, 0xaf, 0x8d, 0x42, 0x7f, 0x16, 0xc3,
	0x2d, 0xd7, 0x9f, 0xd3, 0x21, 0x57, 0xe6, 0x85, 0xfc, 0x11, 0x00, 0xb6, 0x99, 0xe3, 0xa2, 0x07,
	0xab, 0xda, 0xae, 0xe6, 0x06, 0x73, 0xb7, 0xbd, 0x46, 0x9a, 0x07, 0x60, 0xff, 0xdc, 0x80, 0x87,
	0x5a, 0x70, 0x27, 0x31, 0x4d, 0x88, 0xa0, 0x27, 0x61, 0xe9, 0x6c, 0x3e, 0x02, 0x10, 0x67, 0x49,
	0xf4, 0xb9, 0x93, 0x32, 0x4f, 0xbd, 0xc2, 0xad, 0x5e, 0x43, 0x22, 0x7d, 0xe6, 0x8d, 0x8b, 0x8a,
	0x52, 0x39, 0xc5, 0x2d, 0xbd, 0xa8, 0x7d, 0xe6, 0xd9, 0xff, 0x31, 0xe0, 0xd1, 0x35, 0x81, 0x94,
	0xcb, 0xd3, 0x37, 0xe0, 0x4e, 0x42, 0x3d, 0xc7, 0x63, 0x24, 0x88, 0x70, 0x69, 0x72, 0x23, 0x2e,
	0x64, 0x85, 0x73, 0xb7, 0xab, 0x09, 0xf5, 0x9e, 0x67, 0xd2, 0x9d, 0x88, 0x0b, 0xb3, 0x0b, 0x66,
	0xe1, 0xc4, 0x69, 0x14, 0xa6, 0xbc, 0x10, 0xe9, 0x6d, 0xed, 0xc8, 0x33, 0x94, 0xe2, 0x9d, 0x02,
	0x22, 0xa8, 0xe7, 0xa4, 0xb1, 0xa4, 0xe7, 0x71, 0xa3, 0x4a, 0xb4, 0x1f, 0x97, 0x7c, 0x92, 0x76,
	0xe0, 0xab, 0xc5, 0x36, 0xc1, 0x71, 0x38, 0x64, 0x5c, 0x94, 0x79, 0x7d, 0x7f, 0x3c, 0x3d, 0x1a,
	0x13, 0x23, 0xe5, 0x52, 0x78, 0x17, 0x16, 0xf5, 0x85, 0x4a, 0xfd, 0xb0, 0xf7, 0x0a, 0x31, 0x1e,
	0xa9, 0x7b, 0x1e, 0x51, 0xce, 0xc9, 0x90, 0xa2, 0xf5, 0x71, 0xe3, 0xe9, 0xd6, 0xc7, 0x7d, 0x57,
	0xcf, 0xfb, 0xce, 0xfe, 0x4b, 0xb1, 0xed, 0x30, 0x09, 0xde, 0x71, 0x24, 0xd8, 0x80, 0x65, 0xc4,
	0xf4, 0x09, 0xdc, 0x53, 0x24, 0x20, 0x67, 0x08, 0xc9, 0x05, 0x49, 0x83, 0x0c, 0xce, 0x0b, 0x64,
	0xbb, 0x26, 0x75, 0x76, 0x50, 0xe5, 0xb9, 0xd4, 0xd8, 0x1e, 0x9c, 0xe3, 0xe6, 0x1b, 0xa8, 0x88,
	0x0a, 0x13, 0x92, 0x83, 0x72, 0x56, 0xcf, 0x48, 0x12, 0x14, 0x86, 0x59, 0x41, 0xe6, 0x77, 0x60,
	0x9d, 0x5c, 0x12, 0x41, 0x12, 0xee, 0x44, 0x03, 0x27, 0xa1, 0x2e, 0x0d, 0x85, 0x33, 0x66, 0xa3,
	0x05, 0x99, 0x8a, 0x7b, 0x99, 0xc6, 0xc9, 0xa0, 0x27, 0xe5, 0x87, 0x19, 0x2d, 0xfd, 0xa6, 0x0a,
	0xf7, 0x5f, 0xa6, 0xcc, 0x3d, 0x3f, 0x22, 0xc2, 0x3d, 0xdb, 0x89, 0xc2, 0x01, 0x1b, 0xe6, 0xc4,
	0xf7, 0x08, 0x6a, 0x98, 0x40, 0x67, 0xea, 0xb1, 0x58, 0x42, 0xf0, 0xc0, 0x43, 0x9e, 0x95, 0xe2,
	0xd9, 0x07, 0x03, 0xe1, 0x63, 0xa4, 0xfc, 0x4d, 0xa8, 0x33, 0x37, 0x0a, 0x25, 0x13, 0xeb, 0x6f,
	0x46, 0x0d, 0x51, 0x24, 0xe2, 0xef, 0x41, 0x2d, 0x8a, 0xd1, 0x19, 0x97, 0x71, 0x36, 0xbb, 0x36,
	0xe6, 0xfe, 0x8a, 0x80, 0x3a, 0x27, 0x52, 0xb5, 0x97, 0x1f, 0x31, 0x3f, 0x84, 0x15, 0xf7, 0x8c,
	0x84, 0x21, 0xf5, 0x1d, 0x41, 0x86, 0x18, 0xa7, 0xde, 0xa9, 0xcb, 0x99, 0xec, 0x35, 0x19, 0x1e,
	0x78, 0xeb, 0xdf, 0x87, 0xc5, 0xcf, 0x88, 0x9f, 0xca, 0x4c, 0x5e, 0xe2, 0x1f, 0x85, 0x3b, 0x29,
	0x08, 0xbf, 0x1e, 0x66, 0x6e, 0x23, 0x91, 0xf5, 0xcf, 0x61, 0x49, 0x79, 0xc7, 0xe7, 0x13, 0x5f,
	0x75, 0x43, 0x53, 0x41, 0x00, 0xed, 0x0a, 0x26, 0xfc, 0xe2, 0x61, 0x05, 0x99, 0x9f, 0xc0, 0x92,
	0x74, 0x90, 0x33, 0xdb, 0x7b, 0xd7, 0xdd, 0x52, 0x86, 0xd9, 0xcb, 0x0e, 0xd8, 0x67, 0xb0, 0xb1,
	0x4f, 0xc5, 0x15, 0xba, 0xa5, 0xc8, 0x6e, 0x03, 0x6a, 0x97, 0x34, 0x41, 0xbe, 0x2d, 0xac, 0x2c,
	0x39, 0x68, 0xff, 0xc1, 0x80, 0xcd, 0x6b, 0x5d, 0x95, 0x9b, 0xc5, 0x77, 0xb8, 0x33, 0x77, 0x60,
	0xc5, 0x2d, 0x38, 0xc8, 0x72, 0xf3, 0xe0, 0x9a, 0xdc, 0xf4, 0xa6, 0x8e, 0xd8, 0x7b, 0x70, 0x7b,
	0xa2, 0xfa, 0xee, 0x02, 0xa9, 0xc2, 0x57, 0x66, 0x0a, 0x6f, 0xbb, 0xb0, 0x32, 0xb1, 0x83, 0xec,
	0x53, 0xec, 0x6e, 0x63, 0x6e, 0x77, 0x77, 0x26, 0xcd, 0x5b, 0x91, 0xa1, 0xdf, 0x2d, 0x86, 0x3e,
	0xd5, 0xae, 0xf6, 0x3f, 0x2a, 0x60, 0xbe, 0xc2, 0xe7, 0x77, 0xa2, 0x52, 0xa6, 0x7e, 0x37, 0x98,
	0x37, 0x2d, 0xa2, 0xea, 0x0d, 0x22, 0x32, 0xb7, 0x72, 0xbe, 0x54, 0xc3, 0x67, 0x16, 0xb5, 0x31,
	0x0f, 0x19, 0x87, 0x9a, 0xc7, 0xb0, 0xca, 0xd3, 0x38, 0xf6, 0x69, 0x80, 0xe4, 0x22, 0x97, 0xed,
	0x45, 0xb9, 0xa5, 0x3d, 0xc6, 0x33, 0xb3, 0xb7, 0xea, 0xbc, 0x1a, 0x6b, 0xcb, 0xe5, 0x7b, 0x85,
	0x17, 0x7e, 0xaf, 0x9f, 0xc2, 0x4a, 0x51, 0x03, 0xd7, 0x84, 0x7c, 0x98, 0xa7, 0x08, 0xa7, 0x91,
	0xe1, 0x72, 0x4d, 0xd0, 0x0c, 0x39, 0x61, 0x1a, 0x14, 0x8a, 0xd9, 0x9a, 0xc8, 0x8e, 0xd3, 0xc0,
	0xfe, 0xa7, 0x01, 0x6b, 0x33, 0x91, 0x95, 0x6b, 0xe2, 0xaf, 0xc3, 0x72, 0x40, 0xde, 0x4c, 0xd6,
	0x44, 0xdd, 0x5b, 0x33, 0x20, 0x6f, 0xc6, 0x4b, 0xa2, 0x7a, 0x56, 0xdd, 0x33, 0x47, 0xae, 0x0a,
	0x3a, 0x81, 0x23, 0x7a, 0xe0, 0x99, 0x5d, 0xb4, 0x24, 0xdc, 0x33, 0xea, 0xa9, 0xec, 0x2d, 0xc8,
	0xec, 0xad, 0xa2, 0xdf, 0x23, 0x85, 0xcb, 0x3c, 0x35, 0x83, 0xc9, 0x0f, 0xdc, 0x08, 0x38, 0xbd,
	0x48, 0x69, 0xe8, 0x52, 0x87, 0x85, 0xce, 0x45, 0x4a, 0x53, 0x5a, 0xa0, 0xb8, 0xd5, 0x5c, 0x7c,
	0x10, 0xbe, 0x44, 0xa1, 0xfd, 0x13, 0x58, 0xdb, 0x21, 0xa1, 0x4b, 0xfd, 0xff, 0xad, 0xc5, 0xf4,
	0x5b, 0x54, 0xe6, 0xdc, 0xc2, 0xde, 0x86, 0xbb, 0xb3, 0xf6, 0x4b, 0xa5, 0xd4, 0x26, 0x70, 0x6f,
	0x72, 0xf8, 0x05, 0xa5, 0xf1, 0xb6, 0xcf, 0x2e, 0xe9, 0xff, 0x35, 0xca, 0x5f, 0x19, 0xfa, 0xa3,
	0xa6, 0xf9, 0x28, 0x57, 0xfc, 0xa2, 0x1f, 0x63, 0xb6, 0xa6, 0x73, 0xeb, 0x53, 0xbd, 0xae, 0x3e,
	0xbb, 0xf0, 0xa0, 0x40, 0xb1, 0x27, 0xa1, 0xcf, 0x42, 0x8a, 0xbb, 0x46, 0x19, 0x2a, 0xb7, 0x13,
	0x78, 0x78, 0xb5, 0x99, 0xd2, 0x1d, 0x1e, 0xc9, 0xd3, 0x0e, 0xee, 0x39, 0xbc, 0xd8, 0xe1, 0xd1,
	0xc4, 0xae, 0xfd, 0xaf, 0x0a, 0x34, 0xb5, 0x4e, 0xbd, 0xd9, 0xbc, 0x6e, 0x42, 0x3d, 0xa6, 0x34,
	0x91, 0x1b, 0x74, 0xe1, 0x15, 0x40, 0xb4, 0xcf, 0x3c, 0x74, 0x2f, 0x15, 0x88, 0xab, 0x3e, 0x45,
	0xf4, 0xd9, 0x69, 0xa2, 0x64, 0x5b, 0x09, 0xd0, 0x9d, 0x52, 0x94, 0x1f, 0xa0, 0xfa, 0x17, 0x66,
	0x43, 0xaa, 0xc9, 0x8f, 0xd0, 0x27, 0xd0, 0x92, 0x4a, 0xe3, 0x2f, 0xd1, 0x45, 0x4d, 0x4f, 0x3a,
	0x3a, 0xce, 0xbf, 0x46, 0x3f, 0xc8, 0x1c, 0xe7, 0x1b, 0xce, 0x92, 0xfe, 0x91, 0x84, 0x92, 0x7d,
	0xb5, 0xe5, 0x3c, 0x86, 0xa6, 0xd2, 0x53, 0x9f, 0xae, 0xb5, 0x19, 0x35, 0xf5, 0xf9, 0xfa, 0x21,
	0xac, 0x4c, 0xcc, 0x49, 0xd7, 0xf5, 0x69, 0xd7, 0xfb, 0x39, 0x4b, 0x7f, 0x33, 0xbb, 0x8a, 0xa2,
	0xde, 0xc6, 0x95, 0xd4, 0xdb, 0xc8, 0x4f, 0x71, 0xfb, 0xf7, 0x06, 0x3c, 0x2c, 0x8c, 0x5c, 0xea,
	0x8b, 0xc2, 0xea, 0x39, 0x4d, 0x2f, 0xc6, 0x0d, 0xe8, 0xe5, 0x5d, 0x73, 0x64, 0x7e, 0x17, 0xee,
	0xe8, 0x46, 0x1d, 0x9f, 0x71, 0x91, 0x3d, 0x2c, 0x33, 0x96, 0x57, 0x35, 0xcb, 0xb8, 0xba, 0x3f,
	0xeb, 0x7f, 0xf1, 0x76, 0xc3, 0xf8, 0xf3, 0xdb, 0x0d, 0xe3, 0x6f, 0x6f, 0x37, 0x8c, 0x5f, 0xfe,
	0x7d, 0xe3, 0x16, 0x58, 0x6e, 0x14, 0x74, 0x46, 0x6c, 0x14, 0xa5, 0x78, 0x38, 0x88, 0x3c, 0xea,
	0xab, 0xff, 0x02, 0xff, 0xe8, 0xc9, 0x30, 0xf2, 0x49, 0x38, 0xec, 0x7c, 0xbb, 0x2b, 0x44, 0xc7,
	0x8d, 0x82, 0xa7, 0x12, 0x76, 0x23, 0xff, 0x29, 0x89, 0xe3, 0xa7, 0xfa, 0xbf, 0x92, 0xff, 0x1b,
	0x00, 0x00, 0xff, 0xff, 0x06, 0xf0, 0x38, 0x44, 0x59, 0x16, 0x00, 0x00,
}
