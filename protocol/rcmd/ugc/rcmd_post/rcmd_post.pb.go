// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/rcmd_post.proto

package rcmd_post // import "golang.52tt.com/protocol/services/ugc/rcmd_post"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import common "golang.52tt.com/protocol/services/rcmd/common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 来源类型
type PostSourceType int32

const (
	PostSourceType_INVALID PostSourceType = 0
	PostSourceType_KOL     PostSourceType = 1
	PostSourceType_TGL     PostSourceType = 2
)

var PostSourceType_name = map[int32]string{
	0: "INVALID",
	1: "KOL",
	2: "TGL",
}
var PostSourceType_value = map[string]int32{
	"INVALID": 0,
	"KOL":     1,
	"TGL":     2,
}

func (x PostSourceType) String() string {
	return proto.EnumName(PostSourceType_name, int32(x))
}
func (PostSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{0}
}

// 上报10min发布的动态数 //待确认
type HiReportType int32

const (
	HiReportType_WrongType     HiReportType = 0
	HiReportType_NewType       HiReportType = 1
	HiReportType_InteractType  HiReportType = 2
	HiReportType_EnterType     HiReportType = 3
	HiReportType_TotalType     HiReportType = 4
	HiReportType_ExpireType    HiReportType = 5
	HiReportType_EliminateType HiReportType = 6
)

var HiReportType_name = map[int32]string{
	0: "WrongType",
	1: "NewType",
	2: "InteractType",
	3: "EnterType",
	4: "TotalType",
	5: "ExpireType",
	6: "EliminateType",
}
var HiReportType_value = map[string]int32{
	"WrongType":     0,
	"NewType":       1,
	"InteractType":  2,
	"EnterType":     3,
	"TotalType":     4,
	"ExpireType":    5,
	"EliminateType": 6,
}

func (x HiReportType) String() string {
	return proto.EnumName(HiReportType_name, int32(x))
}
func (HiReportType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{1}
}

// 测试用的
type SetWeightForTestReq struct {
	// 设置权重的原因，楼主回复: "reply", 不良标签:"bad_label", 运营单个帖子提权: "operator_single" 运营话题帖子提权:"operator_topic"
	Reason               string   `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicId              string   `protobuf:"bytes,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Value                float32  `protobuf:"fixed32,4,opt,name=value,proto3" json:"value,omitempty"`
	ExpireTime           uint64   `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetWeightForTestReq) Reset()         { *m = SetWeightForTestReq{} }
func (m *SetWeightForTestReq) String() string { return proto.CompactTextString(m) }
func (*SetWeightForTestReq) ProtoMessage()    {}
func (*SetWeightForTestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{0}
}
func (m *SetWeightForTestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetWeightForTestReq.Unmarshal(m, b)
}
func (m *SetWeightForTestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetWeightForTestReq.Marshal(b, m, deterministic)
}
func (dst *SetWeightForTestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetWeightForTestReq.Merge(dst, src)
}
func (m *SetWeightForTestReq) XXX_Size() int {
	return xxx_messageInfo_SetWeightForTestReq.Size(m)
}
func (m *SetWeightForTestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetWeightForTestReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetWeightForTestReq proto.InternalMessageInfo

func (m *SetWeightForTestReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *SetWeightForTestReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *SetWeightForTestReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *SetWeightForTestReq) GetValue() float32 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *SetWeightForTestReq) GetExpireTime() uint64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type SetWeightForTestRsp struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetWeightForTestRsp) Reset()         { *m = SetWeightForTestRsp{} }
func (m *SetWeightForTestRsp) String() string { return proto.CompactTextString(m) }
func (*SetWeightForTestRsp) ProtoMessage()    {}
func (*SetWeightForTestRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{1}
}
func (m *SetWeightForTestRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetWeightForTestRsp.Unmarshal(m, b)
}
func (m *SetWeightForTestRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetWeightForTestRsp.Marshal(b, m, deterministic)
}
func (dst *SetWeightForTestRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetWeightForTestRsp.Merge(dst, src)
}
func (m *SetWeightForTestRsp) XXX_Size() int {
	return xxx_messageInfo_SetWeightForTestRsp.Size(m)
}
func (m *SetWeightForTestRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetWeightForTestRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetWeightForTestRsp proto.InternalMessageInfo

func (m *SetWeightForTestRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GetRecommendStreamReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Tags                 []uint32 `protobuf:"varint,3,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	HintPostId           string   `protobuf:"bytes,4,opt,name=hint_post_id,json=hintPostId,proto3" json:"hint_post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendStreamReq) Reset()         { *m = GetRecommendStreamReq{} }
func (m *GetRecommendStreamReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStreamReq) ProtoMessage()    {}
func (*GetRecommendStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{2}
}
func (m *GetRecommendStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendStreamReq.Unmarshal(m, b)
}
func (m *GetRecommendStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendStreamReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendStreamReq.Merge(dst, src)
}
func (m *GetRecommendStreamReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendStreamReq.Size(m)
}
func (m *GetRecommendStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendStreamReq proto.InternalMessageInfo

func (m *GetRecommendStreamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendStreamReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetRecommendStreamReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GetRecommendStreamReq) GetHintPostId() string {
	if m != nil {
		return m.HintPostId
	}
	return ""
}

type GetRecommendStreamResp struct {
	Items                []*StreamItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	BottomReached        bool          `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetRecommendStreamResp) Reset()         { *m = GetRecommendStreamResp{} }
func (m *GetRecommendStreamResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStreamResp) ProtoMessage()    {}
func (*GetRecommendStreamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{3}
}
func (m *GetRecommendStreamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendStreamResp.Unmarshal(m, b)
}
func (m *GetRecommendStreamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendStreamResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendStreamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendStreamResp.Merge(dst, src)
}
func (m *GetRecommendStreamResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendStreamResp.Size(m)
}
func (m *GetRecommendStreamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendStreamResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendStreamResp proto.InternalMessageInfo

func (m *GetRecommendStreamResp) GetItems() []*StreamItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetRecommendStreamResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

type StreamItem struct {
	PostId               string         `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	SourceType           PostSourceType `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3,enum=ugc.rcmd_post.PostSourceType" json:"source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *StreamItem) Reset()         { *m = StreamItem{} }
func (m *StreamItem) String() string { return proto.CompactTextString(m) }
func (*StreamItem) ProtoMessage()    {}
func (*StreamItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{4}
}
func (m *StreamItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StreamItem.Unmarshal(m, b)
}
func (m *StreamItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StreamItem.Marshal(b, m, deterministic)
}
func (dst *StreamItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StreamItem.Merge(dst, src)
}
func (m *StreamItem) XXX_Size() int {
	return xxx_messageInfo_StreamItem.Size(m)
}
func (m *StreamItem) XXX_DiscardUnknown() {
	xxx_messageInfo_StreamItem.DiscardUnknown(m)
}

var xxx_messageInfo_StreamItem proto.InternalMessageInfo

func (m *StreamItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *StreamItem) GetSourceType() PostSourceType {
	if m != nil {
		return m.SourceType
	}
	return PostSourceType_INVALID
}

type GetOperRecommendStreamReq struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32                 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Tags                 []uint32               `protobuf:"varint,3,rep,packed,name=tags,proto3" json:"tags,omitempty"`
	HintPostId           string                 `protobuf:"bytes,4,opt,name=hint_post_id,json=hintPostId,proto3" json:"hint_post_id,omitempty"`
	BrowseList           *common.RcmdBrowseInfo `protobuf:"bytes,5,opt,name=browse_list,json=browseList,proto3" json:"browse_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetOperRecommendStreamReq) Reset()         { *m = GetOperRecommendStreamReq{} }
func (m *GetOperRecommendStreamReq) String() string { return proto.CompactTextString(m) }
func (*GetOperRecommendStreamReq) ProtoMessage()    {}
func (*GetOperRecommendStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{5}
}
func (m *GetOperRecommendStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOperRecommendStreamReq.Unmarshal(m, b)
}
func (m *GetOperRecommendStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOperRecommendStreamReq.Marshal(b, m, deterministic)
}
func (dst *GetOperRecommendStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperRecommendStreamReq.Merge(dst, src)
}
func (m *GetOperRecommendStreamReq) XXX_Size() int {
	return xxx_messageInfo_GetOperRecommendStreamReq.Size(m)
}
func (m *GetOperRecommendStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperRecommendStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperRecommendStreamReq proto.InternalMessageInfo

func (m *GetOperRecommendStreamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOperRecommendStreamReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetOperRecommendStreamReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *GetOperRecommendStreamReq) GetHintPostId() string {
	if m != nil {
		return m.HintPostId
	}
	return ""
}

func (m *GetOperRecommendStreamReq) GetBrowseList() *common.RcmdBrowseInfo {
	if m != nil {
		return m.BrowseList
	}
	return nil
}

type GetOperRecommendStreamResp struct {
	Items                []*StreamItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	BottomReached        bool          `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetOperRecommendStreamResp) Reset()         { *m = GetOperRecommendStreamResp{} }
func (m *GetOperRecommendStreamResp) String() string { return proto.CompactTextString(m) }
func (*GetOperRecommendStreamResp) ProtoMessage()    {}
func (*GetOperRecommendStreamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{6}
}
func (m *GetOperRecommendStreamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOperRecommendStreamResp.Unmarshal(m, b)
}
func (m *GetOperRecommendStreamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOperRecommendStreamResp.Marshal(b, m, deterministic)
}
func (dst *GetOperRecommendStreamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperRecommendStreamResp.Merge(dst, src)
}
func (m *GetOperRecommendStreamResp) XXX_Size() int {
	return xxx_messageInfo_GetOperRecommendStreamResp.Size(m)
}
func (m *GetOperRecommendStreamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperRecommendStreamResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperRecommendStreamResp proto.InternalMessageInfo

func (m *GetOperRecommendStreamResp) GetItems() []*StreamItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetOperRecommendStreamResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

type SystemRecommendStreamItem struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AgeLayer             uint32   `protobuf:"varint,2,opt,name=age_layer,json=ageLayer,proto3" json:"age_layer,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	City                 string   `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	AttitudeTime         uint32   `protobuf:"varint,5,opt,name=attitude_time,json=attitudeTime,proto3" json:"attitude_time,omitempty"`
	CommentTime          uint32   `protobuf:"varint,6,opt,name=comment_time,json=commentTime,proto3" json:"comment_time,omitempty"`
	PostCreateAt         uint64   `protobuf:"varint,7,opt,name=post_create_at,json=postCreateAt,proto3" json:"post_create_at,omitempty"`
	RecType              uint32   `protobuf:"varint,8,opt,name=rec_type,json=recType,proto3" json:"rec_type,omitempty"`
	StdScore             float32  `protobuf:"fixed32,9,opt,name=std_score,json=stdScore,proto3" json:"std_score,omitempty"`
	TimeFactorScore      float32  `protobuf:"fixed32,10,opt,name=time_factor_score,json=timeFactorScore,proto3" json:"time_factor_score,omitempty"`
	RegScore             float32  `protobuf:"fixed32,11,opt,name=reg_score,json=regScore,proto3" json:"reg_score,omitempty"`
	HiScore              float32  `protobuf:"fixed32,12,opt,name=hi_score,json=hiScore,proto3" json:"hi_score,omitempty"`
	AuthorRegTime        uint64   `protobuf:"varint,13,opt,name=author_reg_time,json=authorRegTime,proto3" json:"author_reg_time,omitempty"`
	TopicScore           float32  `protobuf:"fixed32,14,opt,name=topic_score,json=topicScore,proto3" json:"topic_score,omitempty"`
	ReplyCount           uint32   `protobuf:"varint,15,opt,name=reply_count,json=replyCount,proto3" json:"reply_count,omitempty"`
	ReplyScore           float32  `protobuf:"fixed32,16,opt,name=reply_score,json=replyScore,proto3" json:"reply_score,omitempty"`
	BadLabelScore        float32  `protobuf:"fixed32,17,opt,name=bad_label_score,json=badLabelScore,proto3" json:"bad_label_score,omitempty"`
	OperatorTopicScore   float32  `protobuf:"fixed32,18,opt,name=operator_topic_score,json=operatorTopicScore,proto3" json:"operator_topic_score,omitempty"`
	OperatorPostScore    float32  `protobuf:"fixed32,19,opt,name=operator_post_score,json=operatorPostScore,proto3" json:"operator_post_score,omitempty"`
	MetaId               string   `protobuf:"bytes,20,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SystemRecommendStreamItem) Reset()         { *m = SystemRecommendStreamItem{} }
func (m *SystemRecommendStreamItem) String() string { return proto.CompactTextString(m) }
func (*SystemRecommendStreamItem) ProtoMessage()    {}
func (*SystemRecommendStreamItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{7}
}
func (m *SystemRecommendStreamItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SystemRecommendStreamItem.Unmarshal(m, b)
}
func (m *SystemRecommendStreamItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SystemRecommendStreamItem.Marshal(b, m, deterministic)
}
func (dst *SystemRecommendStreamItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SystemRecommendStreamItem.Merge(dst, src)
}
func (m *SystemRecommendStreamItem) XXX_Size() int {
	return xxx_messageInfo_SystemRecommendStreamItem.Size(m)
}
func (m *SystemRecommendStreamItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SystemRecommendStreamItem.DiscardUnknown(m)
}

var xxx_messageInfo_SystemRecommendStreamItem proto.InternalMessageInfo

func (m *SystemRecommendStreamItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *SystemRecommendStreamItem) GetAgeLayer() uint32 {
	if m != nil {
		return m.AgeLayer
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *SystemRecommendStreamItem) GetAttitudeTime() uint32 {
	if m != nil {
		return m.AttitudeTime
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetCommentTime() uint32 {
	if m != nil {
		return m.CommentTime
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetPostCreateAt() uint64 {
	if m != nil {
		return m.PostCreateAt
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetRecType() uint32 {
	if m != nil {
		return m.RecType
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetStdScore() float32 {
	if m != nil {
		return m.StdScore
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetTimeFactorScore() float32 {
	if m != nil {
		return m.TimeFactorScore
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetRegScore() float32 {
	if m != nil {
		return m.RegScore
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetHiScore() float32 {
	if m != nil {
		return m.HiScore
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetAuthorRegTime() uint64 {
	if m != nil {
		return m.AuthorRegTime
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetTopicScore() float32 {
	if m != nil {
		return m.TopicScore
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetReplyCount() uint32 {
	if m != nil {
		return m.ReplyCount
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetReplyScore() float32 {
	if m != nil {
		return m.ReplyScore
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetBadLabelScore() float32 {
	if m != nil {
		return m.BadLabelScore
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetOperatorTopicScore() float32 {
	if m != nil {
		return m.OperatorTopicScore
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetOperatorPostScore() float32 {
	if m != nil {
		return m.OperatorPostScore
	}
	return 0
}

func (m *SystemRecommendStreamItem) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

type GetSystemRecommendStreamReq struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32                 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	BottomFlush          bool                   `protobuf:"varint,3,opt,name=bottom_flush,json=bottomFlush,proto3" json:"bottom_flush,omitempty"`
	BrowseList           *common.RcmdBrowseInfo `protobuf:"bytes,4,opt,name=browse_list,json=browseList,proto3" json:"browse_list,omitempty"`
	ClientIp             uint32                 `protobuf:"varint,5,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetSystemRecommendStreamReq) Reset()         { *m = GetSystemRecommendStreamReq{} }
func (m *GetSystemRecommendStreamReq) String() string { return proto.CompactTextString(m) }
func (*GetSystemRecommendStreamReq) ProtoMessage()    {}
func (*GetSystemRecommendStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{8}
}
func (m *GetSystemRecommendStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSystemRecommendStreamReq.Unmarshal(m, b)
}
func (m *GetSystemRecommendStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSystemRecommendStreamReq.Marshal(b, m, deterministic)
}
func (dst *GetSystemRecommendStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSystemRecommendStreamReq.Merge(dst, src)
}
func (m *GetSystemRecommendStreamReq) XXX_Size() int {
	return xxx_messageInfo_GetSystemRecommendStreamReq.Size(m)
}
func (m *GetSystemRecommendStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSystemRecommendStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSystemRecommendStreamReq proto.InternalMessageInfo

func (m *GetSystemRecommendStreamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSystemRecommendStreamReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetSystemRecommendStreamReq) GetBottomFlush() bool {
	if m != nil {
		return m.BottomFlush
	}
	return false
}

func (m *GetSystemRecommendStreamReq) GetBrowseList() *common.RcmdBrowseInfo {
	if m != nil {
		return m.BrowseList
	}
	return nil
}

func (m *GetSystemRecommendStreamReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

type GetSystemRecommendStreamResp struct {
	Posts                []*SystemRecommendStreamItem `protobuf:"bytes,1,rep,name=posts,proto3" json:"posts,omitempty"`
	BottomReached        bool                         `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetSystemRecommendStreamResp) Reset()         { *m = GetSystemRecommendStreamResp{} }
func (m *GetSystemRecommendStreamResp) String() string { return proto.CompactTextString(m) }
func (*GetSystemRecommendStreamResp) ProtoMessage()    {}
func (*GetSystemRecommendStreamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{9}
}
func (m *GetSystemRecommendStreamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSystemRecommendStreamResp.Unmarshal(m, b)
}
func (m *GetSystemRecommendStreamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSystemRecommendStreamResp.Marshal(b, m, deterministic)
}
func (dst *GetSystemRecommendStreamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSystemRecommendStreamResp.Merge(dst, src)
}
func (m *GetSystemRecommendStreamResp) XXX_Size() int {
	return xxx_messageInfo_GetSystemRecommendStreamResp.Size(m)
}
func (m *GetSystemRecommendStreamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSystemRecommendStreamResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSystemRecommendStreamResp proto.InternalMessageInfo

func (m *GetSystemRecommendStreamResp) GetPosts() []*SystemRecommendStreamItem {
	if m != nil {
		return m.Posts
	}
	return nil
}

func (m *GetSystemRecommendStreamResp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

type ProfileHighInteractPoolReq struct {
	Age_Layer            uint32   `protobuf:"varint,1,opt,name=age_Layer,json=ageLayer,proto3" json:"age_Layer,omitempty"`
	IsMan                bool     `protobuf:"varint,2,opt,name=is_man,json=isMan,proto3" json:"is_man,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProfileHighInteractPoolReq) Reset()         { *m = ProfileHighInteractPoolReq{} }
func (m *ProfileHighInteractPoolReq) String() string { return proto.CompactTextString(m) }
func (*ProfileHighInteractPoolReq) ProtoMessage()    {}
func (*ProfileHighInteractPoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{10}
}
func (m *ProfileHighInteractPoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProfileHighInteractPoolReq.Unmarshal(m, b)
}
func (m *ProfileHighInteractPoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProfileHighInteractPoolReq.Marshal(b, m, deterministic)
}
func (dst *ProfileHighInteractPoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProfileHighInteractPoolReq.Merge(dst, src)
}
func (m *ProfileHighInteractPoolReq) XXX_Size() int {
	return xxx_messageInfo_ProfileHighInteractPoolReq.Size(m)
}
func (m *ProfileHighInteractPoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProfileHighInteractPoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProfileHighInteractPoolReq proto.InternalMessageInfo

func (m *ProfileHighInteractPoolReq) GetAge_Layer() uint32 {
	if m != nil {
		return m.Age_Layer
	}
	return 0
}

func (m *ProfileHighInteractPoolReq) GetIsMan() bool {
	if m != nil {
		return m.IsMan
	}
	return false
}

func (m *ProfileHighInteractPoolReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ProfileHighInteractPoolRsp struct {
	Posts                []*SystemRecommendStreamItem `protobuf:"bytes,1,rep,name=posts,proto3" json:"posts,omitempty"`
	LastUpdateTime       uint64                       `protobuf:"varint,2,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ProfileHighInteractPoolRsp) Reset()         { *m = ProfileHighInteractPoolRsp{} }
func (m *ProfileHighInteractPoolRsp) String() string { return proto.CompactTextString(m) }
func (*ProfileHighInteractPoolRsp) ProtoMessage()    {}
func (*ProfileHighInteractPoolRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{11}
}
func (m *ProfileHighInteractPoolRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProfileHighInteractPoolRsp.Unmarshal(m, b)
}
func (m *ProfileHighInteractPoolRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProfileHighInteractPoolRsp.Marshal(b, m, deterministic)
}
func (dst *ProfileHighInteractPoolRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProfileHighInteractPoolRsp.Merge(dst, src)
}
func (m *ProfileHighInteractPoolRsp) XXX_Size() int {
	return xxx_messageInfo_ProfileHighInteractPoolRsp.Size(m)
}
func (m *ProfileHighInteractPoolRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProfileHighInteractPoolRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ProfileHighInteractPoolRsp proto.InternalMessageInfo

func (m *ProfileHighInteractPoolRsp) GetPosts() []*SystemRecommendStreamItem {
	if m != nil {
		return m.Posts
	}
	return nil
}

func (m *ProfileHighInteractPoolRsp) GetLastUpdateTime() uint64 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

type GetCityRecommendStreamReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GeoTopicId           string   `protobuf:"bytes,2,opt,name=geo_topic_id,json=geoTopicId,proto3" json:"geo_topic_id,omitempty"`
	PostId               string   `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	BottomFlush          bool     `protobuf:"varint,5,opt,name=bottom_flush,json=bottomFlush,proto3" json:"bottom_flush,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCityRecommendStreamReq) Reset()         { *m = GetCityRecommendStreamReq{} }
func (m *GetCityRecommendStreamReq) String() string { return proto.CompactTextString(m) }
func (*GetCityRecommendStreamReq) ProtoMessage()    {}
func (*GetCityRecommendStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{12}
}
func (m *GetCityRecommendStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCityRecommendStreamReq.Unmarshal(m, b)
}
func (m *GetCityRecommendStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCityRecommendStreamReq.Marshal(b, m, deterministic)
}
func (dst *GetCityRecommendStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCityRecommendStreamReq.Merge(dst, src)
}
func (m *GetCityRecommendStreamReq) XXX_Size() int {
	return xxx_messageInfo_GetCityRecommendStreamReq.Size(m)
}
func (m *GetCityRecommendStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCityRecommendStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCityRecommendStreamReq proto.InternalMessageInfo

func (m *GetCityRecommendStreamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCityRecommendStreamReq) GetGeoTopicId() string {
	if m != nil {
		return m.GeoTopicId
	}
	return ""
}

func (m *GetCityRecommendStreamReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetCityRecommendStreamReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCityRecommendStreamReq) GetBottomFlush() bool {
	if m != nil {
		return m.BottomFlush
	}
	return false
}

type CityRecommendStream struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AgeLayer             uint32   `protobuf:"varint,2,opt,name=age_layer,json=ageLayer,proto3" json:"age_layer,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	City                 string   `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	CityScore            float32  `protobuf:"fixed32,5,opt,name=cityScore,proto3" json:"cityScore,omitempty"`
	InteractScore        float32  `protobuf:"fixed32,6,opt,name=interactScore,proto3" json:"interactScore,omitempty"`
	HotScore             float32  `protobuf:"fixed32,7,opt,name=hotScore,proto3" json:"hotScore,omitempty"`
	SexScore             float32  `protobuf:"fixed32,8,opt,name=sexScore,proto3" json:"sexScore,omitempty"`
	AgeScore             float32  `protobuf:"fixed32,9,opt,name=ageScore,proto3" json:"ageScore,omitempty"`
	PostCreateAt         uint64   `protobuf:"varint,10,opt,name=post_create_at,json=postCreateAt,proto3" json:"post_create_at,omitempty"`
	RecType              uint32   `protobuf:"varint,11,opt,name=rec_type,json=recType,proto3" json:"rec_type,omitempty"`
	AttitudeTime         uint32   `protobuf:"varint,12,opt,name=attitude_time,json=attitudeTime,proto3" json:"attitude_time,omitempty"`
	CommentTime          uint32   `protobuf:"varint,13,opt,name=comment_time,json=commentTime,proto3" json:"comment_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CityRecommendStream) Reset()         { *m = CityRecommendStream{} }
func (m *CityRecommendStream) String() string { return proto.CompactTextString(m) }
func (*CityRecommendStream) ProtoMessage()    {}
func (*CityRecommendStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{13}
}
func (m *CityRecommendStream) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CityRecommendStream.Unmarshal(m, b)
}
func (m *CityRecommendStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CityRecommendStream.Marshal(b, m, deterministic)
}
func (dst *CityRecommendStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CityRecommendStream.Merge(dst, src)
}
func (m *CityRecommendStream) XXX_Size() int {
	return xxx_messageInfo_CityRecommendStream.Size(m)
}
func (m *CityRecommendStream) XXX_DiscardUnknown() {
	xxx_messageInfo_CityRecommendStream.DiscardUnknown(m)
}

var xxx_messageInfo_CityRecommendStream proto.InternalMessageInfo

func (m *CityRecommendStream) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *CityRecommendStream) GetAgeLayer() uint32 {
	if m != nil {
		return m.AgeLayer
	}
	return 0
}

func (m *CityRecommendStream) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *CityRecommendStream) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *CityRecommendStream) GetCityScore() float32 {
	if m != nil {
		return m.CityScore
	}
	return 0
}

func (m *CityRecommendStream) GetInteractScore() float32 {
	if m != nil {
		return m.InteractScore
	}
	return 0
}

func (m *CityRecommendStream) GetHotScore() float32 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

func (m *CityRecommendStream) GetSexScore() float32 {
	if m != nil {
		return m.SexScore
	}
	return 0
}

func (m *CityRecommendStream) GetAgeScore() float32 {
	if m != nil {
		return m.AgeScore
	}
	return 0
}

func (m *CityRecommendStream) GetPostCreateAt() uint64 {
	if m != nil {
		return m.PostCreateAt
	}
	return 0
}

func (m *CityRecommendStream) GetRecType() uint32 {
	if m != nil {
		return m.RecType
	}
	return 0
}

func (m *CityRecommendStream) GetAttitudeTime() uint32 {
	if m != nil {
		return m.AttitudeTime
	}
	return 0
}

func (m *CityRecommendStream) GetCommentTime() uint32 {
	if m != nil {
		return m.CommentTime
	}
	return 0
}

type GetCityRecommendStreamRsp struct {
	Posts                []*CityRecommendStream `protobuf:"bytes,1,rep,name=posts,proto3" json:"posts,omitempty"`
	BottomReached        bool                   `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetCityRecommendStreamRsp) Reset()         { *m = GetCityRecommendStreamRsp{} }
func (m *GetCityRecommendStreamRsp) String() string { return proto.CompactTextString(m) }
func (*GetCityRecommendStreamRsp) ProtoMessage()    {}
func (*GetCityRecommendStreamRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{14}
}
func (m *GetCityRecommendStreamRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCityRecommendStreamRsp.Unmarshal(m, b)
}
func (m *GetCityRecommendStreamRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCityRecommendStreamRsp.Marshal(b, m, deterministic)
}
func (dst *GetCityRecommendStreamRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCityRecommendStreamRsp.Merge(dst, src)
}
func (m *GetCityRecommendStreamRsp) XXX_Size() int {
	return xxx_messageInfo_GetCityRecommendStreamRsp.Size(m)
}
func (m *GetCityRecommendStreamRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCityRecommendStreamRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCityRecommendStreamRsp proto.InternalMessageInfo

func (m *GetCityRecommendStreamRsp) GetPosts() []*CityRecommendStream {
	if m != nil {
		return m.Posts
	}
	return nil
}

func (m *GetCityRecommendStreamRsp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

type GetTopicRecommendStreamReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicId              string   `protobuf:"bytes,3,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	BottomFlush          bool     `protobuf:"varint,5,opt,name=bottom_flush,json=bottomFlush,proto3" json:"bottom_flush,omitempty"`
	IsDebug              bool     `protobuf:"varint,6,opt,name=is_debug,json=isDebug,proto3" json:"is_debug,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicRecommendStreamReq) Reset()         { *m = GetTopicRecommendStreamReq{} }
func (m *GetTopicRecommendStreamReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicRecommendStreamReq) ProtoMessage()    {}
func (*GetTopicRecommendStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{15}
}
func (m *GetTopicRecommendStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicRecommendStreamReq.Unmarshal(m, b)
}
func (m *GetTopicRecommendStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicRecommendStreamReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicRecommendStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicRecommendStreamReq.Merge(dst, src)
}
func (m *GetTopicRecommendStreamReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicRecommendStreamReq.Size(m)
}
func (m *GetTopicRecommendStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicRecommendStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicRecommendStreamReq proto.InternalMessageInfo

func (m *GetTopicRecommendStreamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTopicRecommendStreamReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetTopicRecommendStreamReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetTopicRecommendStreamReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetTopicRecommendStreamReq) GetBottomFlush() bool {
	if m != nil {
		return m.BottomFlush
	}
	return false
}

func (m *GetTopicRecommendStreamReq) GetIsDebug() bool {
	if m != nil {
		return m.IsDebug
	}
	return false
}

type TopicRecommendStream struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AgeLayer             uint32   `protobuf:"varint,2,opt,name=age_layer,json=ageLayer,proto3" json:"age_layer,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	City                 string   `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	TopicScore           float32  `protobuf:"fixed32,5,opt,name=topicScore,proto3" json:"topicScore,omitempty"`
	InteractScore        float32  `protobuf:"fixed32,6,opt,name=interactScore,proto3" json:"interactScore,omitempty"`
	HotScore             float32  `protobuf:"fixed32,7,opt,name=hotScore,proto3" json:"hotScore,omitempty"`
	SexScore             float32  `protobuf:"fixed32,8,opt,name=sexScore,proto3" json:"sexScore,omitempty"`
	AgeScore             float32  `protobuf:"fixed32,9,opt,name=ageScore,proto3" json:"ageScore,omitempty"`
	PostCreateAt         uint64   `protobuf:"varint,10,opt,name=post_create_at,json=postCreateAt,proto3" json:"post_create_at,omitempty"`
	RecType              uint32   `protobuf:"varint,11,opt,name=rec_type,json=recType,proto3" json:"rec_type,omitempty"`
	AttitudeTime         uint32   `protobuf:"varint,12,opt,name=attitude_time,json=attitudeTime,proto3" json:"attitude_time,omitempty"`
	CommentTime          uint32   `protobuf:"varint,13,opt,name=comment_time,json=commentTime,proto3" json:"comment_time,omitempty"`
	OriginScore          float32  `protobuf:"fixed32,14,opt,name=origin_score,json=originScore,proto3" json:"origin_score,omitempty"`
	WeightScore          float32  `protobuf:"fixed32,15,opt,name=weight_score,json=weightScore,proto3" json:"weight_score,omitempty"`
	ScoreFactor          float32  `protobuf:"fixed32,16,opt,name=score_factor,json=scoreFactor,proto3" json:"score_factor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicRecommendStream) Reset()         { *m = TopicRecommendStream{} }
func (m *TopicRecommendStream) String() string { return proto.CompactTextString(m) }
func (*TopicRecommendStream) ProtoMessage()    {}
func (*TopicRecommendStream) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{16}
}
func (m *TopicRecommendStream) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicRecommendStream.Unmarshal(m, b)
}
func (m *TopicRecommendStream) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicRecommendStream.Marshal(b, m, deterministic)
}
func (dst *TopicRecommendStream) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicRecommendStream.Merge(dst, src)
}
func (m *TopicRecommendStream) XXX_Size() int {
	return xxx_messageInfo_TopicRecommendStream.Size(m)
}
func (m *TopicRecommendStream) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicRecommendStream.DiscardUnknown(m)
}

var xxx_messageInfo_TopicRecommendStream proto.InternalMessageInfo

func (m *TopicRecommendStream) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *TopicRecommendStream) GetAgeLayer() uint32 {
	if m != nil {
		return m.AgeLayer
	}
	return 0
}

func (m *TopicRecommendStream) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *TopicRecommendStream) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *TopicRecommendStream) GetTopicScore() float32 {
	if m != nil {
		return m.TopicScore
	}
	return 0
}

func (m *TopicRecommendStream) GetInteractScore() float32 {
	if m != nil {
		return m.InteractScore
	}
	return 0
}

func (m *TopicRecommendStream) GetHotScore() float32 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

func (m *TopicRecommendStream) GetSexScore() float32 {
	if m != nil {
		return m.SexScore
	}
	return 0
}

func (m *TopicRecommendStream) GetAgeScore() float32 {
	if m != nil {
		return m.AgeScore
	}
	return 0
}

func (m *TopicRecommendStream) GetPostCreateAt() uint64 {
	if m != nil {
		return m.PostCreateAt
	}
	return 0
}

func (m *TopicRecommendStream) GetRecType() uint32 {
	if m != nil {
		return m.RecType
	}
	return 0
}

func (m *TopicRecommendStream) GetAttitudeTime() uint32 {
	if m != nil {
		return m.AttitudeTime
	}
	return 0
}

func (m *TopicRecommendStream) GetCommentTime() uint32 {
	if m != nil {
		return m.CommentTime
	}
	return 0
}

func (m *TopicRecommendStream) GetOriginScore() float32 {
	if m != nil {
		return m.OriginScore
	}
	return 0
}

func (m *TopicRecommendStream) GetWeightScore() float32 {
	if m != nil {
		return m.WeightScore
	}
	return 0
}

func (m *TopicRecommendStream) GetScoreFactor() float32 {
	if m != nil {
		return m.ScoreFactor
	}
	return 0
}

type GetTopicRecommendStreamRsp struct {
	Posts                []*TopicRecommendStream `protobuf:"bytes,1,rep,name=posts,proto3" json:"posts,omitempty"`
	BottomReached        bool                    `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetTopicRecommendStreamRsp) Reset()         { *m = GetTopicRecommendStreamRsp{} }
func (m *GetTopicRecommendStreamRsp) String() string { return proto.CompactTextString(m) }
func (*GetTopicRecommendStreamRsp) ProtoMessage()    {}
func (*GetTopicRecommendStreamRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{17}
}
func (m *GetTopicRecommendStreamRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicRecommendStreamRsp.Unmarshal(m, b)
}
func (m *GetTopicRecommendStreamRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicRecommendStreamRsp.Marshal(b, m, deterministic)
}
func (dst *GetTopicRecommendStreamRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicRecommendStreamRsp.Merge(dst, src)
}
func (m *GetTopicRecommendStreamRsp) XXX_Size() int {
	return xxx_messageInfo_GetTopicRecommendStreamRsp.Size(m)
}
func (m *GetTopicRecommendStreamRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicRecommendStreamRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicRecommendStreamRsp proto.InternalMessageInfo

func (m *GetTopicRecommendStreamRsp) GetPosts() []*TopicRecommendStream {
	if m != nil {
		return m.Posts
	}
	return nil
}

func (m *GetTopicRecommendStreamRsp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

// 推荐流redis存储用的pb
type PostItem struct {
	PostId          string  `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AgeLayer        uint32  `protobuf:"varint,2,opt,name=age_layer,json=ageLayer,proto3" json:"age_layer,omitempty"`
	Sex             uint32  `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	City            string  `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	AttitudeTime    uint32  `protobuf:"varint,5,opt,name=attitude_time,json=attitudeTime,proto3" json:"attitude_time,omitempty"`
	CommentTime     uint32  `protobuf:"varint,6,opt,name=comment_time,json=commentTime,proto3" json:"comment_time,omitempty"`
	PostCreateAt    uint64  `protobuf:"varint,7,opt,name=post_create_at,json=postCreateAt,proto3" json:"post_create_at,omitempty"`
	AuthorUid       uint32  `protobuf:"varint,8,opt,name=author_uid,json=authorUid,proto3" json:"author_uid,omitempty"`
	StdScore        float32 `protobuf:"fixed32,9,opt,name=std_score,json=stdScore,proto3" json:"std_score,omitempty"`
	TimeFactorScore float32 `protobuf:"fixed32,10,opt,name=time_factor_score,json=timeFactorScore,proto3" json:"time_factor_score,omitempty"`
	// 城市流
	Delete               bool     `protobuf:"varint,11,opt,name=delete,proto3" json:"delete,omitempty"`
	SortScore            float32  `protobuf:"fixed32,12,opt,name=sort_score,json=sortScore,proto3" json:"sort_score,omitempty"`
	InteractScore        float32  `protobuf:"fixed32,13,opt,name=interact_score,json=interactScore,proto3" json:"interact_score,omitempty"`
	HotScore             float32  `protobuf:"fixed32,14,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	SexScore             float32  `protobuf:"fixed32,15,opt,name=sex_score,json=sexScore,proto3" json:"sex_score,omitempty"`
	AgeScore             float32  `protobuf:"fixed32,16,opt,name=age_score,json=ageScore,proto3" json:"age_score,omitempty"`
	CityScore            float32  `protobuf:"fixed32,17,opt,name=city_score,json=cityScore,proto3" json:"city_score,omitempty"`
	GeoTopicId           string   `protobuf:"bytes,18,opt,name=geo_topic_id,json=geoTopicId,proto3" json:"geo_topic_id,omitempty"`
	TopicId              string   `protobuf:"bytes,19,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	AuthorRegTime        uint32   `protobuf:"varint,20,opt,name=author_reg_time,json=authorRegTime,proto3" json:"author_reg_time,omitempty"`
	DiyTopicIds          []string `protobuf:"bytes,21,rep,name=diy_topic_ids,json=diyTopicIds,proto3" json:"diy_topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostItem) Reset()         { *m = PostItem{} }
func (m *PostItem) String() string { return proto.CompactTextString(m) }
func (*PostItem) ProtoMessage()    {}
func (*PostItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{18}
}
func (m *PostItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostItem.Unmarshal(m, b)
}
func (m *PostItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostItem.Marshal(b, m, deterministic)
}
func (dst *PostItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostItem.Merge(dst, src)
}
func (m *PostItem) XXX_Size() int {
	return xxx_messageInfo_PostItem.Size(m)
}
func (m *PostItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PostItem.DiscardUnknown(m)
}

var xxx_messageInfo_PostItem proto.InternalMessageInfo

func (m *PostItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostItem) GetAgeLayer() uint32 {
	if m != nil {
		return m.AgeLayer
	}
	return 0
}

func (m *PostItem) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *PostItem) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *PostItem) GetAttitudeTime() uint32 {
	if m != nil {
		return m.AttitudeTime
	}
	return 0
}

func (m *PostItem) GetCommentTime() uint32 {
	if m != nil {
		return m.CommentTime
	}
	return 0
}

func (m *PostItem) GetPostCreateAt() uint64 {
	if m != nil {
		return m.PostCreateAt
	}
	return 0
}

func (m *PostItem) GetAuthorUid() uint32 {
	if m != nil {
		return m.AuthorUid
	}
	return 0
}

func (m *PostItem) GetStdScore() float32 {
	if m != nil {
		return m.StdScore
	}
	return 0
}

func (m *PostItem) GetTimeFactorScore() float32 {
	if m != nil {
		return m.TimeFactorScore
	}
	return 0
}

func (m *PostItem) GetDelete() bool {
	if m != nil {
		return m.Delete
	}
	return false
}

func (m *PostItem) GetSortScore() float32 {
	if m != nil {
		return m.SortScore
	}
	return 0
}

func (m *PostItem) GetInteractScore() float32 {
	if m != nil {
		return m.InteractScore
	}
	return 0
}

func (m *PostItem) GetHotScore() float32 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

func (m *PostItem) GetSexScore() float32 {
	if m != nil {
		return m.SexScore
	}
	return 0
}

func (m *PostItem) GetAgeScore() float32 {
	if m != nil {
		return m.AgeScore
	}
	return 0
}

func (m *PostItem) GetCityScore() float32 {
	if m != nil {
		return m.CityScore
	}
	return 0
}

func (m *PostItem) GetGeoTopicId() string {
	if m != nil {
		return m.GeoTopicId
	}
	return ""
}

func (m *PostItem) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *PostItem) GetAuthorRegTime() uint32 {
	if m != nil {
		return m.AuthorRegTime
	}
	return 0
}

func (m *PostItem) GetDiyTopicIds() []string {
	if m != nil {
		return m.DiyTopicIds
	}
	return nil
}

type HighInteractProductItem struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Attitude             bool     `protobuf:"varint,2,opt,name=attitude,proto3" json:"attitude,omitempty"`
	Comment              bool     `protobuf:"varint,3,opt,name=comment,proto3" json:"comment,omitempty"`
	CreateAt             uint64   `protobuf:"varint,4,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	AuthorUid            uint32   `protobuf:"varint,5,opt,name=author_uid,json=authorUid,proto3" json:"author_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HighInteractProductItem) Reset()         { *m = HighInteractProductItem{} }
func (m *HighInteractProductItem) String() string { return proto.CompactTextString(m) }
func (*HighInteractProductItem) ProtoMessage()    {}
func (*HighInteractProductItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{19}
}
func (m *HighInteractProductItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HighInteractProductItem.Unmarshal(m, b)
}
func (m *HighInteractProductItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HighInteractProductItem.Marshal(b, m, deterministic)
}
func (dst *HighInteractProductItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HighInteractProductItem.Merge(dst, src)
}
func (m *HighInteractProductItem) XXX_Size() int {
	return xxx_messageInfo_HighInteractProductItem.Size(m)
}
func (m *HighInteractProductItem) XXX_DiscardUnknown() {
	xxx_messageInfo_HighInteractProductItem.DiscardUnknown(m)
}

var xxx_messageInfo_HighInteractProductItem proto.InternalMessageInfo

func (m *HighInteractProductItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *HighInteractProductItem) GetAttitude() bool {
	if m != nil {
		return m.Attitude
	}
	return false
}

func (m *HighInteractProductItem) GetComment() bool {
	if m != nil {
		return m.Comment
	}
	return false
}

func (m *HighInteractProductItem) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *HighInteractProductItem) GetAuthorUid() uint32 {
	if m != nil {
		return m.AuthorUid
	}
	return 0
}

type PostExtraInfo struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	IpCity               string   `protobuf:"bytes,2,opt,name=ip_city,json=ipCity,proto3" json:"ip_city,omitempty"`
	AuthorUid            uint32   `protobuf:"varint,3,opt,name=author_uid,json=authorUid,proto3" json:"author_uid,omitempty"`
	PostCreateAt         uint64   `protobuf:"varint,4,opt,name=post_create_at,json=postCreateAt,proto3" json:"post_create_at,omitempty"`
	GeoTopicId           string   `protobuf:"bytes,5,opt,name=geo_topic_id,json=geoTopicId,proto3" json:"geo_topic_id,omitempty"`
	TopicId              string   `protobuf:"bytes,6,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	AuthorRegTime        uint64   `protobuf:"varint,7,opt,name=author_reg_time,json=authorRegTime,proto3" json:"author_reg_time,omitempty"`
	Sex                  uint32   `protobuf:"varint,8,opt,name=sex,proto3" json:"sex,omitempty"`
	ReplyCount           uint32   `protobuf:"varint,9,opt,name=reply_count,json=replyCount,proto3" json:"reply_count,omitempty"`
	BadLabelScore        float32  `protobuf:"fixed32,10,opt,name=bad_label_score,json=badLabelScore,proto3" json:"bad_label_score,omitempty"`
	BadLabelUpdateTime   uint64   `protobuf:"varint,11,opt,name=bad_label_update_time,json=badLabelUpdateTime,proto3" json:"bad_label_update_time,omitempty"`
	DiyTopicIds          []string `protobuf:"bytes,12,rep,name=diy_topic_ids,json=diyTopicIds,proto3" json:"diy_topic_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostExtraInfo) Reset()         { *m = PostExtraInfo{} }
func (m *PostExtraInfo) String() string { return proto.CompactTextString(m) }
func (*PostExtraInfo) ProtoMessage()    {}
func (*PostExtraInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{20}
}
func (m *PostExtraInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostExtraInfo.Unmarshal(m, b)
}
func (m *PostExtraInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostExtraInfo.Marshal(b, m, deterministic)
}
func (dst *PostExtraInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostExtraInfo.Merge(dst, src)
}
func (m *PostExtraInfo) XXX_Size() int {
	return xxx_messageInfo_PostExtraInfo.Size(m)
}
func (m *PostExtraInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PostExtraInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PostExtraInfo proto.InternalMessageInfo

func (m *PostExtraInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostExtraInfo) GetIpCity() string {
	if m != nil {
		return m.IpCity
	}
	return ""
}

func (m *PostExtraInfo) GetAuthorUid() uint32 {
	if m != nil {
		return m.AuthorUid
	}
	return 0
}

func (m *PostExtraInfo) GetPostCreateAt() uint64 {
	if m != nil {
		return m.PostCreateAt
	}
	return 0
}

func (m *PostExtraInfo) GetGeoTopicId() string {
	if m != nil {
		return m.GeoTopicId
	}
	return ""
}

func (m *PostExtraInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *PostExtraInfo) GetAuthorRegTime() uint64 {
	if m != nil {
		return m.AuthorRegTime
	}
	return 0
}

func (m *PostExtraInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *PostExtraInfo) GetReplyCount() uint32 {
	if m != nil {
		return m.ReplyCount
	}
	return 0
}

func (m *PostExtraInfo) GetBadLabelScore() float32 {
	if m != nil {
		return m.BadLabelScore
	}
	return 0
}

func (m *PostExtraInfo) GetBadLabelUpdateTime() uint64 {
	if m != nil {
		return m.BadLabelUpdateTime
	}
	return 0
}

func (m *PostExtraInfo) GetDiyTopicIds() []string {
	if m != nil {
		return m.DiyTopicIds
	}
	return nil
}

type Report struct {
	HiReportType         uint32   `protobuf:"varint,1,opt,name=hi_report_type,json=hiReportType,proto3" json:"hi_report_type,omitempty"`
	PostList             []string `protobuf:"bytes,2,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	Len                  uint32   `protobuf:"varint,3,opt,name=len,proto3" json:"len,omitempty"`
	Time                 uint64   `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Report) Reset()         { *m = Report{} }
func (m *Report) String() string { return proto.CompactTextString(m) }
func (*Report) ProtoMessage()    {}
func (*Report) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{21}
}
func (m *Report) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Report.Unmarshal(m, b)
}
func (m *Report) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Report.Marshal(b, m, deterministic)
}
func (dst *Report) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Report.Merge(dst, src)
}
func (m *Report) XXX_Size() int {
	return xxx_messageInfo_Report.Size(m)
}
func (m *Report) XXX_DiscardUnknown() {
	xxx_messageInfo_Report.DiscardUnknown(m)
}

var xxx_messageInfo_Report proto.InternalMessageInfo

func (m *Report) GetHiReportType() uint32 {
	if m != nil {
		return m.HiReportType
	}
	return 0
}

func (m *Report) GetPostList() []string {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *Report) GetLen() uint32 {
	if m != nil {
		return m.Len
	}
	return 0
}

func (m *Report) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

type GetTopicStreamPostListReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Topic                string   `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	PostIdList           []string `protobuf:"bytes,3,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	SortByAlgo           bool     `protobuf:"varint,6,opt,name=sort_by_algo,json=sortByAlgo,proto3" json:"sort_by_algo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicStreamPostListReq) Reset()         { *m = GetTopicStreamPostListReq{} }
func (m *GetTopicStreamPostListReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicStreamPostListReq) ProtoMessage()    {}
func (*GetTopicStreamPostListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{22}
}
func (m *GetTopicStreamPostListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamPostListReq.Unmarshal(m, b)
}
func (m *GetTopicStreamPostListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamPostListReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamPostListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamPostListReq.Merge(dst, src)
}
func (m *GetTopicStreamPostListReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamPostListReq.Size(m)
}
func (m *GetTopicStreamPostListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamPostListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamPostListReq proto.InternalMessageInfo

func (m *GetTopicStreamPostListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetTopicStreamPostListReq) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *GetTopicStreamPostListReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *GetTopicStreamPostListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetTopicStreamPostListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetTopicStreamPostListReq) GetSortByAlgo() bool {
	if m != nil {
		return m.SortByAlgo
	}
	return false
}

type GetTopicStreamPostListRsp struct {
	PostList             []*GetTopicStreamPostListRsp_TopicStreamPost `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	TotalCount           uint32                                       `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetTopicStreamPostListRsp) Reset()         { *m = GetTopicStreamPostListRsp{} }
func (m *GetTopicStreamPostListRsp) String() string { return proto.CompactTextString(m) }
func (*GetTopicStreamPostListRsp) ProtoMessage()    {}
func (*GetTopicStreamPostListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{23}
}
func (m *GetTopicStreamPostListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamPostListRsp.Unmarshal(m, b)
}
func (m *GetTopicStreamPostListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamPostListRsp.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamPostListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamPostListRsp.Merge(dst, src)
}
func (m *GetTopicStreamPostListRsp) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamPostListRsp.Size(m)
}
func (m *GetTopicStreamPostListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamPostListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamPostListRsp proto.InternalMessageInfo

func (m *GetTopicStreamPostListRsp) GetPostList() []*GetTopicStreamPostListRsp_TopicStreamPost {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *GetTopicStreamPostListRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type GetTopicStreamPostListRsp_TopicStreamPost struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	OriginScore          float32  `protobuf:"fixed32,2,opt,name=origin_score,json=originScore,proto3" json:"origin_score,omitempty"`
	WeightScore          float32  `protobuf:"fixed32,3,opt,name=weight_score,json=weightScore,proto3" json:"weight_score,omitempty"`
	WeightScorePosition  float32  `protobuf:"fixed32,4,opt,name=weight_score_position,json=weightScorePosition,proto3" json:"weight_score_position,omitempty"`
	ScoreFactor          float32  `protobuf:"fixed32,5,opt,name=score_factor,json=scoreFactor,proto3" json:"score_factor,omitempty"`
	TopicAlgoScore       float32  `protobuf:"fixed32,6,opt,name=topic_algo_score,json=topicAlgoScore,proto3" json:"topic_algo_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicStreamPostListRsp_TopicStreamPost) Reset() {
	*m = GetTopicStreamPostListRsp_TopicStreamPost{}
}
func (m *GetTopicStreamPostListRsp_TopicStreamPost) String() string {
	return proto.CompactTextString(m)
}
func (*GetTopicStreamPostListRsp_TopicStreamPost) ProtoMessage() {}
func (*GetTopicStreamPostListRsp_TopicStreamPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{23, 0}
}
func (m *GetTopicStreamPostListRsp_TopicStreamPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamPostListRsp_TopicStreamPost.Unmarshal(m, b)
}
func (m *GetTopicStreamPostListRsp_TopicStreamPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamPostListRsp_TopicStreamPost.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamPostListRsp_TopicStreamPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamPostListRsp_TopicStreamPost.Merge(dst, src)
}
func (m *GetTopicStreamPostListRsp_TopicStreamPost) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamPostListRsp_TopicStreamPost.Size(m)
}
func (m *GetTopicStreamPostListRsp_TopicStreamPost) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamPostListRsp_TopicStreamPost.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamPostListRsp_TopicStreamPost proto.InternalMessageInfo

func (m *GetTopicStreamPostListRsp_TopicStreamPost) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetTopicStreamPostListRsp_TopicStreamPost) GetOriginScore() float32 {
	if m != nil {
		return m.OriginScore
	}
	return 0
}

func (m *GetTopicStreamPostListRsp_TopicStreamPost) GetWeightScore() float32 {
	if m != nil {
		return m.WeightScore
	}
	return 0
}

func (m *GetTopicStreamPostListRsp_TopicStreamPost) GetWeightScorePosition() float32 {
	if m != nil {
		return m.WeightScorePosition
	}
	return 0
}

func (m *GetTopicStreamPostListRsp_TopicStreamPost) GetScoreFactor() float32 {
	if m != nil {
		return m.ScoreFactor
	}
	return 0
}

func (m *GetTopicStreamPostListRsp_TopicStreamPost) GetTopicAlgoScore() float32 {
	if m != nil {
		return m.TopicAlgoScore
	}
	return 0
}

//
// 这里的逻辑是，查询高互动帖子, 如果高互动帖子同时又有话题，则再去查话题推荐池中这个帖子的话题推荐打分。
type GetHighInteractPostListReq struct {
	PostIdList           []string `protobuf:"bytes,1,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHighInteractPostListReq) Reset()         { *m = GetHighInteractPostListReq{} }
func (m *GetHighInteractPostListReq) String() string { return proto.CompactTextString(m) }
func (*GetHighInteractPostListReq) ProtoMessage()    {}
func (*GetHighInteractPostListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{24}
}
func (m *GetHighInteractPostListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHighInteractPostListReq.Unmarshal(m, b)
}
func (m *GetHighInteractPostListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHighInteractPostListReq.Marshal(b, m, deterministic)
}
func (dst *GetHighInteractPostListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHighInteractPostListReq.Merge(dst, src)
}
func (m *GetHighInteractPostListReq) XXX_Size() int {
	return xxx_messageInfo_GetHighInteractPostListReq.Size(m)
}
func (m *GetHighInteractPostListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHighInteractPostListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHighInteractPostListReq proto.InternalMessageInfo

func (m *GetHighInteractPostListReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *GetHighInteractPostListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetHighInteractPostListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetHighInteractPostListRsp struct {
	PostList             []*GetHighInteractPostListRsp_HighIteractPost `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	TotalCount           uint32                                        `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *GetHighInteractPostListRsp) Reset()         { *m = GetHighInteractPostListRsp{} }
func (m *GetHighInteractPostListRsp) String() string { return proto.CompactTextString(m) }
func (*GetHighInteractPostListRsp) ProtoMessage()    {}
func (*GetHighInteractPostListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{25}
}
func (m *GetHighInteractPostListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHighInteractPostListRsp.Unmarshal(m, b)
}
func (m *GetHighInteractPostListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHighInteractPostListRsp.Marshal(b, m, deterministic)
}
func (dst *GetHighInteractPostListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHighInteractPostListRsp.Merge(dst, src)
}
func (m *GetHighInteractPostListRsp) XXX_Size() int {
	return xxx_messageInfo_GetHighInteractPostListRsp.Size(m)
}
func (m *GetHighInteractPostListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHighInteractPostListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHighInteractPostListRsp proto.InternalMessageInfo

func (m *GetHighInteractPostListRsp) GetPostList() []*GetHighInteractPostListRsp_HighIteractPost {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *GetHighInteractPostListRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type GetHighInteractPostListRsp_HighIteractPost struct {
	Post                                 *PostItem `protobuf:"bytes,1,opt,name=post,proto3" json:"post,omitempty"`
	SystemRecommendStreamRankingPosition float32   `protobuf:"fixed32,2,opt,name=system_recommend_stream_ranking_position,json=systemRecommendStreamRankingPosition,proto3" json:"system_recommend_stream_ranking_position,omitempty"`
	TopicRecommendStreamRankingPosition  float32   `protobuf:"fixed32,3,opt,name=topic_recommend_stream_ranking_position,json=topicRecommendStreamRankingPosition,proto3" json:"topic_recommend_stream_ranking_position,omitempty"`
	XXX_NoUnkeyedLiteral                 struct{}  `json:"-"`
	XXX_unrecognized                     []byte    `json:"-"`
	XXX_sizecache                        int32     `json:"-"`
}

func (m *GetHighInteractPostListRsp_HighIteractPost) Reset() {
	*m = GetHighInteractPostListRsp_HighIteractPost{}
}
func (m *GetHighInteractPostListRsp_HighIteractPost) String() string {
	return proto.CompactTextString(m)
}
func (*GetHighInteractPostListRsp_HighIteractPost) ProtoMessage() {}
func (*GetHighInteractPostListRsp_HighIteractPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{25, 0}
}
func (m *GetHighInteractPostListRsp_HighIteractPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHighInteractPostListRsp_HighIteractPost.Unmarshal(m, b)
}
func (m *GetHighInteractPostListRsp_HighIteractPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHighInteractPostListRsp_HighIteractPost.Marshal(b, m, deterministic)
}
func (dst *GetHighInteractPostListRsp_HighIteractPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHighInteractPostListRsp_HighIteractPost.Merge(dst, src)
}
func (m *GetHighInteractPostListRsp_HighIteractPost) XXX_Size() int {
	return xxx_messageInfo_GetHighInteractPostListRsp_HighIteractPost.Size(m)
}
func (m *GetHighInteractPostListRsp_HighIteractPost) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHighInteractPostListRsp_HighIteractPost.DiscardUnknown(m)
}

var xxx_messageInfo_GetHighInteractPostListRsp_HighIteractPost proto.InternalMessageInfo

func (m *GetHighInteractPostListRsp_HighIteractPost) GetPost() *PostItem {
	if m != nil {
		return m.Post
	}
	return nil
}

func (m *GetHighInteractPostListRsp_HighIteractPost) GetSystemRecommendStreamRankingPosition() float32 {
	if m != nil {
		return m.SystemRecommendStreamRankingPosition
	}
	return 0
}

func (m *GetHighInteractPostListRsp_HighIteractPost) GetTopicRecommendStreamRankingPosition() float32 {
	if m != nil {
		return m.TopicRecommendStreamRankingPosition
	}
	return 0
}

type GetVoiceControlStreamReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	ClientIp             uint32   `protobuf:"varint,3,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVoiceControlStreamReq) Reset()         { *m = GetVoiceControlStreamReq{} }
func (m *GetVoiceControlStreamReq) String() string { return proto.CompactTextString(m) }
func (*GetVoiceControlStreamReq) ProtoMessage()    {}
func (*GetVoiceControlStreamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{26}
}
func (m *GetVoiceControlStreamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoiceControlStreamReq.Unmarshal(m, b)
}
func (m *GetVoiceControlStreamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoiceControlStreamReq.Marshal(b, m, deterministic)
}
func (dst *GetVoiceControlStreamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoiceControlStreamReq.Merge(dst, src)
}
func (m *GetVoiceControlStreamReq) XXX_Size() int {
	return xxx_messageInfo_GetVoiceControlStreamReq.Size(m)
}
func (m *GetVoiceControlStreamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoiceControlStreamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoiceControlStreamReq proto.InternalMessageInfo

func (m *GetVoiceControlStreamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetVoiceControlStreamReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetVoiceControlStreamReq) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

type VoiceControlStreamItem struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	MetaId               string   `protobuf:"bytes,2,opt,name=meta_id,json=metaId,proto3" json:"meta_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VoiceControlStreamItem) Reset()         { *m = VoiceControlStreamItem{} }
func (m *VoiceControlStreamItem) String() string { return proto.CompactTextString(m) }
func (*VoiceControlStreamItem) ProtoMessage()    {}
func (*VoiceControlStreamItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{27}
}
func (m *VoiceControlStreamItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VoiceControlStreamItem.Unmarshal(m, b)
}
func (m *VoiceControlStreamItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VoiceControlStreamItem.Marshal(b, m, deterministic)
}
func (dst *VoiceControlStreamItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VoiceControlStreamItem.Merge(dst, src)
}
func (m *VoiceControlStreamItem) XXX_Size() int {
	return xxx_messageInfo_VoiceControlStreamItem.Size(m)
}
func (m *VoiceControlStreamItem) XXX_DiscardUnknown() {
	xxx_messageInfo_VoiceControlStreamItem.DiscardUnknown(m)
}

var xxx_messageInfo_VoiceControlStreamItem proto.InternalMessageInfo

func (m *VoiceControlStreamItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *VoiceControlStreamItem) GetMetaId() string {
	if m != nil {
		return m.MetaId
	}
	return ""
}

type GetVoiceControlStreamRsp struct {
	ItemList             []*VoiceControlStreamItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	BottomReached        bool                      `protobuf:"varint,2,opt,name=bottom_reached,json=bottomReached,proto3" json:"bottom_reached,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetVoiceControlStreamRsp) Reset()         { *m = GetVoiceControlStreamRsp{} }
func (m *GetVoiceControlStreamRsp) String() string { return proto.CompactTextString(m) }
func (*GetVoiceControlStreamRsp) ProtoMessage()    {}
func (*GetVoiceControlStreamRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{28}
}
func (m *GetVoiceControlStreamRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVoiceControlStreamRsp.Unmarshal(m, b)
}
func (m *GetVoiceControlStreamRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVoiceControlStreamRsp.Marshal(b, m, deterministic)
}
func (dst *GetVoiceControlStreamRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVoiceControlStreamRsp.Merge(dst, src)
}
func (m *GetVoiceControlStreamRsp) XXX_Size() int {
	return xxx_messageInfo_GetVoiceControlStreamRsp.Size(m)
}
func (m *GetVoiceControlStreamRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVoiceControlStreamRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVoiceControlStreamRsp proto.InternalMessageInfo

func (m *GetVoiceControlStreamRsp) GetItemList() []*VoiceControlStreamItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *GetVoiceControlStreamRsp) GetBottomReached() bool {
	if m != nil {
		return m.BottomReached
	}
	return false
}

type CheckInVoiceControlWhiteReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInVoiceControlWhiteReq) Reset()         { *m = CheckInVoiceControlWhiteReq{} }
func (m *CheckInVoiceControlWhiteReq) String() string { return proto.CompactTextString(m) }
func (*CheckInVoiceControlWhiteReq) ProtoMessage()    {}
func (*CheckInVoiceControlWhiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{29}
}
func (m *CheckInVoiceControlWhiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInVoiceControlWhiteReq.Unmarshal(m, b)
}
func (m *CheckInVoiceControlWhiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInVoiceControlWhiteReq.Marshal(b, m, deterministic)
}
func (dst *CheckInVoiceControlWhiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInVoiceControlWhiteReq.Merge(dst, src)
}
func (m *CheckInVoiceControlWhiteReq) XXX_Size() int {
	return xxx_messageInfo_CheckInVoiceControlWhiteReq.Size(m)
}
func (m *CheckInVoiceControlWhiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInVoiceControlWhiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInVoiceControlWhiteReq proto.InternalMessageInfo

func (m *CheckInVoiceControlWhiteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckInVoiceControlWhiteRsp struct {
	InWhite              bool     `protobuf:"varint,1,opt,name=in_white,json=inWhite,proto3" json:"in_white,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInVoiceControlWhiteRsp) Reset()         { *m = CheckInVoiceControlWhiteRsp{} }
func (m *CheckInVoiceControlWhiteRsp) String() string { return proto.CompactTextString(m) }
func (*CheckInVoiceControlWhiteRsp) ProtoMessage()    {}
func (*CheckInVoiceControlWhiteRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0, []int{30}
}
func (m *CheckInVoiceControlWhiteRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInVoiceControlWhiteRsp.Unmarshal(m, b)
}
func (m *CheckInVoiceControlWhiteRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInVoiceControlWhiteRsp.Marshal(b, m, deterministic)
}
func (dst *CheckInVoiceControlWhiteRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInVoiceControlWhiteRsp.Merge(dst, src)
}
func (m *CheckInVoiceControlWhiteRsp) XXX_Size() int {
	return xxx_messageInfo_CheckInVoiceControlWhiteRsp.Size(m)
}
func (m *CheckInVoiceControlWhiteRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInVoiceControlWhiteRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInVoiceControlWhiteRsp proto.InternalMessageInfo

func (m *CheckInVoiceControlWhiteRsp) GetInWhite() bool {
	if m != nil {
		return m.InWhite
	}
	return false
}

func init() {
	proto.RegisterType((*SetWeightForTestReq)(nil), "ugc.rcmd_post.SetWeightForTestReq")
	proto.RegisterType((*SetWeightForTestRsp)(nil), "ugc.rcmd_post.SetWeightForTestRsp")
	proto.RegisterType((*GetRecommendStreamReq)(nil), "ugc.rcmd_post.GetRecommendStreamReq")
	proto.RegisterType((*GetRecommendStreamResp)(nil), "ugc.rcmd_post.GetRecommendStreamResp")
	proto.RegisterType((*StreamItem)(nil), "ugc.rcmd_post.StreamItem")
	proto.RegisterType((*GetOperRecommendStreamReq)(nil), "ugc.rcmd_post.GetOperRecommendStreamReq")
	proto.RegisterType((*GetOperRecommendStreamResp)(nil), "ugc.rcmd_post.GetOperRecommendStreamResp")
	proto.RegisterType((*SystemRecommendStreamItem)(nil), "ugc.rcmd_post.SystemRecommendStreamItem")
	proto.RegisterType((*GetSystemRecommendStreamReq)(nil), "ugc.rcmd_post.GetSystemRecommendStreamReq")
	proto.RegisterType((*GetSystemRecommendStreamResp)(nil), "ugc.rcmd_post.GetSystemRecommendStreamResp")
	proto.RegisterType((*ProfileHighInteractPoolReq)(nil), "ugc.rcmd_post.ProfileHighInteractPoolReq")
	proto.RegisterType((*ProfileHighInteractPoolRsp)(nil), "ugc.rcmd_post.ProfileHighInteractPoolRsp")
	proto.RegisterType((*GetCityRecommendStreamReq)(nil), "ugc.rcmd_post.GetCityRecommendStreamReq")
	proto.RegisterType((*CityRecommendStream)(nil), "ugc.rcmd_post.CityRecommendStream")
	proto.RegisterType((*GetCityRecommendStreamRsp)(nil), "ugc.rcmd_post.GetCityRecommendStreamRsp")
	proto.RegisterType((*GetTopicRecommendStreamReq)(nil), "ugc.rcmd_post.GetTopicRecommendStreamReq")
	proto.RegisterType((*TopicRecommendStream)(nil), "ugc.rcmd_post.TopicRecommendStream")
	proto.RegisterType((*GetTopicRecommendStreamRsp)(nil), "ugc.rcmd_post.GetTopicRecommendStreamRsp")
	proto.RegisterType((*PostItem)(nil), "ugc.rcmd_post.PostItem")
	proto.RegisterType((*HighInteractProductItem)(nil), "ugc.rcmd_post.HighInteractProductItem")
	proto.RegisterType((*PostExtraInfo)(nil), "ugc.rcmd_post.PostExtraInfo")
	proto.RegisterType((*Report)(nil), "ugc.rcmd_post.Report")
	proto.RegisterType((*GetTopicStreamPostListReq)(nil), "ugc.rcmd_post.GetTopicStreamPostListReq")
	proto.RegisterType((*GetTopicStreamPostListRsp)(nil), "ugc.rcmd_post.GetTopicStreamPostListRsp")
	proto.RegisterType((*GetTopicStreamPostListRsp_TopicStreamPost)(nil), "ugc.rcmd_post.GetTopicStreamPostListRsp.TopicStreamPost")
	proto.RegisterType((*GetHighInteractPostListReq)(nil), "ugc.rcmd_post.GetHighInteractPostListReq")
	proto.RegisterType((*GetHighInteractPostListRsp)(nil), "ugc.rcmd_post.GetHighInteractPostListRsp")
	proto.RegisterType((*GetHighInteractPostListRsp_HighIteractPost)(nil), "ugc.rcmd_post.GetHighInteractPostListRsp.HighIteractPost")
	proto.RegisterType((*GetVoiceControlStreamReq)(nil), "ugc.rcmd_post.GetVoiceControlStreamReq")
	proto.RegisterType((*VoiceControlStreamItem)(nil), "ugc.rcmd_post.VoiceControlStreamItem")
	proto.RegisterType((*GetVoiceControlStreamRsp)(nil), "ugc.rcmd_post.GetVoiceControlStreamRsp")
	proto.RegisterType((*CheckInVoiceControlWhiteReq)(nil), "ugc.rcmd_post.CheckInVoiceControlWhiteReq")
	proto.RegisterType((*CheckInVoiceControlWhiteRsp)(nil), "ugc.rcmd_post.CheckInVoiceControlWhiteRsp")
	proto.RegisterEnum("ugc.rcmd_post.PostSourceType", PostSourceType_name, PostSourceType_value)
	proto.RegisterEnum("ugc.rcmd_post.HiReportType", HiReportType_name, HiReportType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RcmdPostClient is the client API for RcmdPost service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RcmdPostClient interface {
	// 获取推荐列表(deprecated)
	GetRecommendStream(ctx context.Context, in *GetRecommendStreamReq, opts ...grpc.CallOption) (*GetRecommendStreamResp, error)
	// 获取精选列表
	GetOperRecommendStream(ctx context.Context, in *GetOperRecommendStreamReq, opts ...grpc.CallOption) (*GetOperRecommendStreamResp, error)
	// 获取推荐列表
	GetSystemRecommendStream(ctx context.Context, in *GetSystemRecommendStreamReq, opts ...grpc.CallOption) (*GetSystemRecommendStreamResp, error)
	// 推荐流设置权重，给测试用
	SetWeightForTest(ctx context.Context, in *SetWeightForTestReq, opts ...grpc.CallOption) (*SetWeightForTestRsp, error)
	// 城市流
	GetCityRecommendStream(ctx context.Context, in *GetCityRecommendStreamReq, opts ...grpc.CallOption) (*GetCityRecommendStreamRsp, error)
	// 话题流
	GetTopicRecommendStream(ctx context.Context, in *GetTopicRecommendStreamReq, opts ...grpc.CallOption) (*GetTopicRecommendStreamRsp, error)
	// 测试用,profile高互动池
	ProfileHighInteractPool(ctx context.Context, in *ProfileHighInteractPoolReq, opts ...grpc.CallOption) (*ProfileHighInteractPoolRsp, error)
	// 运营后台用, 查询高互动池帖子数据
	GetHighInteractPostList(ctx context.Context, in *GetHighInteractPostListReq, opts ...grpc.CallOption) (*GetHighInteractPostListRsp, error)
	// 运营后台用，查询话题流贴子数据
	GetTopicStreamPostList(ctx context.Context, in *GetTopicStreamPostListReq, opts ...grpc.CallOption) (*GetTopicStreamPostListRsp, error)
	// 声控专区（主播专区）
	GetVoiceControlStream(ctx context.Context, in *GetVoiceControlStreamReq, opts ...grpc.CallOption) (*GetVoiceControlStreamRsp, error)
	// 是否在声控专区用户白名单里面
	CheckInVoiceControlWhite(ctx context.Context, in *CheckInVoiceControlWhiteReq, opts ...grpc.CallOption) (*CheckInVoiceControlWhiteRsp, error)
}

type rcmdPostClient struct {
	cc *grpc.ClientConn
}

func NewRcmdPostClient(cc *grpc.ClientConn) RcmdPostClient {
	return &rcmdPostClient{cc}
}

func (c *rcmdPostClient) GetRecommendStream(ctx context.Context, in *GetRecommendStreamReq, opts ...grpc.CallOption) (*GetRecommendStreamResp, error) {
	out := new(GetRecommendStreamResp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/GetRecommendStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) GetOperRecommendStream(ctx context.Context, in *GetOperRecommendStreamReq, opts ...grpc.CallOption) (*GetOperRecommendStreamResp, error) {
	out := new(GetOperRecommendStreamResp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/GetOperRecommendStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) GetSystemRecommendStream(ctx context.Context, in *GetSystemRecommendStreamReq, opts ...grpc.CallOption) (*GetSystemRecommendStreamResp, error) {
	out := new(GetSystemRecommendStreamResp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/GetSystemRecommendStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) SetWeightForTest(ctx context.Context, in *SetWeightForTestReq, opts ...grpc.CallOption) (*SetWeightForTestRsp, error) {
	out := new(SetWeightForTestRsp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/SetWeightForTest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) GetCityRecommendStream(ctx context.Context, in *GetCityRecommendStreamReq, opts ...grpc.CallOption) (*GetCityRecommendStreamRsp, error) {
	out := new(GetCityRecommendStreamRsp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/GetCityRecommendStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) GetTopicRecommendStream(ctx context.Context, in *GetTopicRecommendStreamReq, opts ...grpc.CallOption) (*GetTopicRecommendStreamRsp, error) {
	out := new(GetTopicRecommendStreamRsp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/GetTopicRecommendStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) ProfileHighInteractPool(ctx context.Context, in *ProfileHighInteractPoolReq, opts ...grpc.CallOption) (*ProfileHighInteractPoolRsp, error) {
	out := new(ProfileHighInteractPoolRsp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/ProfileHighInteractPool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) GetHighInteractPostList(ctx context.Context, in *GetHighInteractPostListReq, opts ...grpc.CallOption) (*GetHighInteractPostListRsp, error) {
	out := new(GetHighInteractPostListRsp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/GetHighInteractPostList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) GetTopicStreamPostList(ctx context.Context, in *GetTopicStreamPostListReq, opts ...grpc.CallOption) (*GetTopicStreamPostListRsp, error) {
	out := new(GetTopicStreamPostListRsp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/GetTopicStreamPostList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) GetVoiceControlStream(ctx context.Context, in *GetVoiceControlStreamReq, opts ...grpc.CallOption) (*GetVoiceControlStreamRsp, error) {
	out := new(GetVoiceControlStreamRsp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/GetVoiceControlStream", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdPostClient) CheckInVoiceControlWhite(ctx context.Context, in *CheckInVoiceControlWhiteReq, opts ...grpc.CallOption) (*CheckInVoiceControlWhiteRsp, error) {
	out := new(CheckInVoiceControlWhiteRsp)
	err := c.cc.Invoke(ctx, "/ugc.rcmd_post.RcmdPost/CheckInVoiceControlWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RcmdPostServer is the server API for RcmdPost service.
type RcmdPostServer interface {
	// 获取推荐列表(deprecated)
	GetRecommendStream(context.Context, *GetRecommendStreamReq) (*GetRecommendStreamResp, error)
	// 获取精选列表
	GetOperRecommendStream(context.Context, *GetOperRecommendStreamReq) (*GetOperRecommendStreamResp, error)
	// 获取推荐列表
	GetSystemRecommendStream(context.Context, *GetSystemRecommendStreamReq) (*GetSystemRecommendStreamResp, error)
	// 推荐流设置权重，给测试用
	SetWeightForTest(context.Context, *SetWeightForTestReq) (*SetWeightForTestRsp, error)
	// 城市流
	GetCityRecommendStream(context.Context, *GetCityRecommendStreamReq) (*GetCityRecommendStreamRsp, error)
	// 话题流
	GetTopicRecommendStream(context.Context, *GetTopicRecommendStreamReq) (*GetTopicRecommendStreamRsp, error)
	// 测试用,profile高互动池
	ProfileHighInteractPool(context.Context, *ProfileHighInteractPoolReq) (*ProfileHighInteractPoolRsp, error)
	// 运营后台用, 查询高互动池帖子数据
	GetHighInteractPostList(context.Context, *GetHighInteractPostListReq) (*GetHighInteractPostListRsp, error)
	// 运营后台用，查询话题流贴子数据
	GetTopicStreamPostList(context.Context, *GetTopicStreamPostListReq) (*GetTopicStreamPostListRsp, error)
	// 声控专区（主播专区）
	GetVoiceControlStream(context.Context, *GetVoiceControlStreamReq) (*GetVoiceControlStreamRsp, error)
	// 是否在声控专区用户白名单里面
	CheckInVoiceControlWhite(context.Context, *CheckInVoiceControlWhiteReq) (*CheckInVoiceControlWhiteRsp, error)
}

func RegisterRcmdPostServer(s *grpc.Server, srv RcmdPostServer) {
	s.RegisterService(&_RcmdPost_serviceDesc, srv)
}

func _RcmdPost_GetRecommendStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).GetRecommendStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/GetRecommendStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).GetRecommendStream(ctx, req.(*GetRecommendStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_GetOperRecommendStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperRecommendStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).GetOperRecommendStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/GetOperRecommendStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).GetOperRecommendStream(ctx, req.(*GetOperRecommendStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_GetSystemRecommendStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSystemRecommendStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).GetSystemRecommendStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/GetSystemRecommendStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).GetSystemRecommendStream(ctx, req.(*GetSystemRecommendStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_SetWeightForTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetWeightForTestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).SetWeightForTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/SetWeightForTest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).SetWeightForTest(ctx, req.(*SetWeightForTestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_GetCityRecommendStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCityRecommendStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).GetCityRecommendStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/GetCityRecommendStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).GetCityRecommendStream(ctx, req.(*GetCityRecommendStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_GetTopicRecommendStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicRecommendStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).GetTopicRecommendStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/GetTopicRecommendStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).GetTopicRecommendStream(ctx, req.(*GetTopicRecommendStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_ProfileHighInteractPool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProfileHighInteractPoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).ProfileHighInteractPool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/ProfileHighInteractPool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).ProfileHighInteractPool(ctx, req.(*ProfileHighInteractPoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_GetHighInteractPostList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHighInteractPostListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).GetHighInteractPostList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/GetHighInteractPostList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).GetHighInteractPostList(ctx, req.(*GetHighInteractPostListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_GetTopicStreamPostList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicStreamPostListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).GetTopicStreamPostList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/GetTopicStreamPostList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).GetTopicStreamPostList(ctx, req.(*GetTopicStreamPostListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_GetVoiceControlStream_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoiceControlStreamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).GetVoiceControlStream(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/GetVoiceControlStream",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).GetVoiceControlStream(ctx, req.(*GetVoiceControlStreamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdPost_CheckInVoiceControlWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckInVoiceControlWhiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdPostServer).CheckInVoiceControlWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.rcmd_post.RcmdPost/CheckInVoiceControlWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdPostServer).CheckInVoiceControlWhite(ctx, req.(*CheckInVoiceControlWhiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RcmdPost_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.rcmd_post.RcmdPost",
	HandlerType: (*RcmdPostServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecommendStream",
			Handler:    _RcmdPost_GetRecommendStream_Handler,
		},
		{
			MethodName: "GetOperRecommendStream",
			Handler:    _RcmdPost_GetOperRecommendStream_Handler,
		},
		{
			MethodName: "GetSystemRecommendStream",
			Handler:    _RcmdPost_GetSystemRecommendStream_Handler,
		},
		{
			MethodName: "SetWeightForTest",
			Handler:    _RcmdPost_SetWeightForTest_Handler,
		},
		{
			MethodName: "GetCityRecommendStream",
			Handler:    _RcmdPost_GetCityRecommendStream_Handler,
		},
		{
			MethodName: "GetTopicRecommendStream",
			Handler:    _RcmdPost_GetTopicRecommendStream_Handler,
		},
		{
			MethodName: "ProfileHighInteractPool",
			Handler:    _RcmdPost_ProfileHighInteractPool_Handler,
		},
		{
			MethodName: "GetHighInteractPostList",
			Handler:    _RcmdPost_GetHighInteractPostList_Handler,
		},
		{
			MethodName: "GetTopicStreamPostList",
			Handler:    _RcmdPost_GetTopicStreamPostList_Handler,
		},
		{
			MethodName: "GetVoiceControlStream",
			Handler:    _RcmdPost_GetVoiceControlStream_Handler,
		},
		{
			MethodName: "CheckInVoiceControlWhite",
			Handler:    _RcmdPost_CheckInVoiceControlWhite_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/rcmd_post.proto",
}

func init() { proto.RegisterFile("ugc/rcmd_post.proto", fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0) }

var fileDescriptor_rcmd_post_4fbeb9f3fbc0bfb0 = []byte{
	// 2243 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0x4f, 0x6f, 0x1b, 0xc7,
	0x15, 0x0f, 0xb5, 0xa4, 0x48, 0x3e, 0x92, 0x12, 0x3d, 0xb2, 0x2d, 0x9a, 0x8a, 0x6b, 0x79, 0x6d,
	0xd7, 0xb4, 0x03, 0x48, 0x8d, 0x8a, 0x02, 0x0e, 0x50, 0x04, 0xb0, 0x1d, 0xdb, 0x51, 0xab, 0x26,
	0xc6, 0x4a, 0xb6, 0x81, 0x5c, 0x16, 0xab, 0xe5, 0x68, 0x39, 0xc8, 0x72, 0x67, 0xb3, 0x33, 0x8c,
	0x4d, 0xa0, 0xed, 0xa9, 0x69, 0xef, 0x3d, 0xb5, 0x87, 0x1e, 0x0a, 0xf4, 0xde, 0x5b, 0x7b, 0xe8,
	0xb1, 0x5f, 0xa7, 0x97, 0x7e, 0x81, 0xa2, 0x98, 0x37, 0xc3, 0x3f, 0xfb, 0x4f, 0xa2, 0x83, 0xb8,
	0x87, 0x22, 0x27, 0xf2, 0xbd, 0xf9, 0xcd, 0xcc, 0x9b, 0x79, 0xbf, 0x37, 0xef, 0xcd, 0x2c, 0x6c,
	0x4d, 0x02, 0x7f, 0x3f, 0xf1, 0xc7, 0x43, 0x37, 0xe6, 0x42, 0xee, 0xc5, 0x09, 0x97, 0x9c, 0x74,
	0x26, 0x81, 0xbf, 0x37, 0x57, 0xf6, 0xb7, 0x7c, 0x3e, 0x1e, 0xf3, 0x68, 0x5f, 0xff, 0x68, 0x8c,
	0xfd, 0x87, 0x0a, 0x6c, 0x1d, 0x53, 0xf9, 0x8a, 0xb2, 0x60, 0x24, 0x9f, 0xf2, 0xe4, 0x84, 0x0a,
	0xe9, 0xd0, 0xaf, 0xc8, 0x55, 0x58, 0x4f, 0xa8, 0x27, 0x78, 0xd4, 0xab, 0xec, 0x56, 0x06, 0x4d,
	0xc7, 0x48, 0x64, 0x1b, 0xea, 0x6a, 0x30, 0x97, 0x0d, 0x7b, 0x6b, 0xba, 0x41, 0x89, 0x87, 0x43,
	0x72, 0x0d, 0x1a, 0x92, 0xc7, 0xcc, 0x57, 0x2d, 0x16, 0xb6, 0xd4, 0x51, 0x3e, 0x1c, 0x92, 0xcb,
	0x50, 0xfb, 0xda, 0x0b, 0x27, 0xb4, 0x57, 0xdd, 0xad, 0x0c, 0xd6, 0x1c, 0x2d, 0x90, 0x1b, 0xd0,
	0xa2, 0x6f, 0x62, 0x96, 0x50, 0x57, 0xb2, 0x31, 0xed, 0xd5, 0x76, 0x2b, 0x83, 0xaa, 0x03, 0x5a,
	0x75, 0xc2, 0xc6, 0xd4, 0xbe, 0x5b, 0x60, 0x99, 0x88, 0x49, 0x17, 0xac, 0xb1, 0x08, 0x8c, 0x59,
	0xea, 0xaf, 0x3d, 0x81, 0x2b, 0xcf, 0xa8, 0x74, 0xa8, 0x5a, 0x18, 0x8d, 0x86, 0xc7, 0x32, 0xa1,
	0xde, 0x58, 0x2d, 0xa2, 0x0b, 0xd6, 0x84, 0x0d, 0x11, 0xda, 0x71, 0xd4, 0x5f, 0x65, 0x4a, 0xc8,
	0xc6, 0x4c, 0xa2, 0xf1, 0x1d, 0x47, 0x0b, 0x84, 0x40, 0x55, 0x7a, 0x81, 0xe8, 0x59, 0xbb, 0xd6,
	0xa0, 0xe3, 0xe0, 0x7f, 0xb2, 0x0b, 0xed, 0x11, 0x8b, 0xa4, 0x3b, 0x5b, 0x6d, 0x15, 0xe7, 0x03,
	0xa5, 0x7b, 0x8e, 0x2b, 0xb6, 0x63, 0xb8, 0x5a, 0x34, 0xad, 0x88, 0xc9, 0x3e, 0xd4, 0x98, 0xa4,
	0x63, 0xd1, 0xab, 0xec, 0x5a, 0x83, 0xd6, 0xc1, 0xb5, 0xbd, 0x94, 0x23, 0xf6, 0x34, 0xf2, 0x50,
	0xd2, 0xb1, 0xa3, 0x71, 0xe4, 0x0e, 0x6c, 0x9c, 0x72, 0x29, 0xf9, 0xd8, 0x4d, 0xa8, 0xe7, 0x8f,
	0xa8, 0xde, 0xdc, 0x86, 0xd3, 0xd1, 0x5a, 0x47, 0x2b, 0x6d, 0x0a, 0xb0, 0xe8, 0xbb, 0xec, 0x8a,
	0x4a, 0xca, 0x15, 0x1f, 0x43, 0x4b, 0xf0, 0x49, 0xe2, 0x53, 0x57, 0x4e, 0x63, 0x8a, 0x43, 0x6d,
	0x1c, 0x5c, 0xcf, 0x18, 0xa1, 0x16, 0x71, 0x8c, 0xa8, 0x93, 0x69, 0x4c, 0x1d, 0x10, 0xf3, 0xff,
	0xf6, 0xdf, 0x2b, 0x70, 0xed, 0x19, 0x95, 0x9f, 0xc7, 0x34, 0xf9, 0x5f, 0x6f, 0x2a, 0xf9, 0x29,
	0xb4, 0x4e, 0x13, 0xfe, 0x5a, 0x50, 0x37, 0x64, 0x42, 0x22, 0x2b, 0x5a, 0x07, 0x3b, 0x68, 0xf7,
	0x9e, 0x21, 0xae, 0xe3, 0x8f, 0x87, 0x8f, 0x10, 0x73, 0x18, 0x9d, 0x71, 0x07, 0x34, 0xfe, 0x88,
	0x09, 0x69, 0x4b, 0xe8, 0x97, 0x19, 0xfe, 0x0e, 0xdd, 0xf2, 0xd7, 0x1a, 0x5c, 0x3b, 0x9e, 0x0a,
	0xd5, 0x31, 0x3d, 0xeb, 0xf9, 0x6e, 0xda, 0x81, 0xa6, 0x17, 0x50, 0x37, 0xf4, 0xa6, 0x34, 0x31,
	0x5b, 0xd7, 0xf0, 0x02, 0x7a, 0xa4, 0x64, 0xb5, 0xcb, 0x82, 0xbe, 0xc1, 0x48, 0xea, 0x38, 0xea,
	0xaf, 0xda, 0x4f, 0x9f, 0xc9, 0xa9, 0xd9, 0x33, 0xfc, 0x4f, 0x6e, 0x41, 0xc7, 0x93, 0x92, 0xc9,
	0xc9, 0x70, 0x29, 0x8a, 0x3a, 0x4e, 0x7b, 0xa6, 0x54, 0x71, 0x44, 0x6e, 0x42, 0x5b, 0x5b, 0x25,
	0x35, 0x66, 0x1d, 0x31, 0x2d, 0xa3, 0x43, 0xc8, 0x6d, 0xd8, 0x40, 0x1b, 0xfd, 0x84, 0x7a, 0x92,
	0xba, 0x9e, 0xec, 0xd5, 0x31, 0x1c, 0xdb, 0x4a, 0xfb, 0x18, 0x95, 0x0f, 0xa5, 0x0a, 0xf1, 0x84,
	0xfa, 0x9a, 0x54, 0x0d, 0x1c, 0xa4, 0x9e, 0x50, 0x5f, 0x51, 0x46, 0xad, 0x45, 0xc8, 0xa1, 0x2b,
	0x7c, 0x9e, 0xd0, 0x5e, 0x13, 0xc3, 0xbc, 0x21, 0xe4, 0xf0, 0x58, 0xc9, 0xe4, 0x3e, 0x5c, 0x52,
	0x13, 0xbb, 0x67, 0x9e, 0x2f, 0x79, 0x62, 0x40, 0x80, 0xa0, 0x4d, 0xd5, 0xf0, 0x14, 0xf5, 0x1a,
	0xbb, 0x03, 0xcd, 0x84, 0x06, 0x06, 0xd3, 0xd2, 0x03, 0x25, 0x34, 0xd0, 0x8d, 0xd7, 0xa0, 0x31,
	0x62, 0xa6, 0xad, 0x8d, 0x6d, 0xf5, 0x11, 0xd3, 0x4d, 0x3f, 0x84, 0x4d, 0x6f, 0x22, 0x47, 0x3c,
	0x71, 0x55, 0x77, 0x5c, 0x67, 0x07, 0x97, 0xd0, 0xd1, 0x6a, 0x87, 0x06, 0xb8, 0xd2, 0x1b, 0xd0,
	0xd2, 0xc7, 0x94, 0x1e, 0x65, 0x03, 0x47, 0x01, 0x54, 0xe9, 0x81, 0x6e, 0x40, 0x2b, 0xa1, 0x71,
	0x38, 0x75, 0x7d, 0x3e, 0x89, 0x64, 0x6f, 0x13, 0xd7, 0x09, 0xa8, 0x7a, 0xac, 0x34, 0x0b, 0x80,
	0x1e, 0xa1, 0xab, 0x47, 0x40, 0xd5, 0xdc, 0x94, 0x53, 0x6f, 0xe8, 0x86, 0xde, 0x29, 0x0d, 0x0d,
	0xe8, 0x12, 0x82, 0x3a, 0xa7, 0xde, 0xf0, 0x48, 0x69, 0x35, 0xee, 0x47, 0x70, 0x99, 0xc7, 0x34,
	0xf1, 0xd4, 0x9e, 0x2c, 0xdb, 0x44, 0x10, 0x4c, 0x66, 0x6d, 0x27, 0x0b, 0xdb, 0xf6, 0x60, 0x6b,
	0xde, 0x03, 0xfd, 0xa5, 0x3b, 0x6c, 0x61, 0x87, 0x4b, 0xb3, 0x26, 0x8c, 0x6c, 0xc4, 0x6f, 0x43,
	0x7d, 0x4c, 0xa5, 0xa7, 0xa8, 0x77, 0x59, 0x53, 0x4f, 0x89, 0x87, 0x43, 0xfb, 0x9f, 0x15, 0xd8,
	0x79, 0x46, 0x65, 0x21, 0x69, 0xdf, 0x26, 0xc6, 0x6f, 0x42, 0xdb, 0x04, 0xc8, 0x59, 0x38, 0x11,
	0x23, 0xa4, 0x6b, 0xc3, 0x69, 0x69, 0xdd, 0x53, 0xa5, 0xca, 0x06, 0x74, 0xf5, 0xad, 0x02, 0x5a,
	0xd1, 0xc1, 0x0f, 0x99, 0xa2, 0x2e, 0x8b, 0x0d, 0xb9, 0x1b, 0x5a, 0x71, 0x18, 0xdb, 0xdf, 0x54,
	0xe0, 0xfd, 0xf2, 0x55, 0x88, 0x98, 0x7c, 0x0c, 0x35, 0xb5, 0x4d, 0xb3, 0x80, 0x1f, 0x64, 0x03,
	0xbe, 0x2c, 0x66, 0x1d, 0xdd, 0x6d, 0xd5, 0xf8, 0x3f, 0x85, 0xfe, 0xf3, 0x84, 0x9f, 0xb1, 0x90,
	0x7e, 0xca, 0x82, 0xd1, 0x61, 0x24, 0x69, 0xe2, 0xf9, 0xf2, 0x39, 0xe7, 0xa1, 0xda, 0x4b, 0x13,
	0xe6, 0x18, 0xd6, 0x66, 0x47, 0x17, 0x61, 0x7e, 0x05, 0xd6, 0x99, 0x70, 0xc7, 0x5e, 0x64, 0x46,
	0xae, 0x31, 0xf1, 0x0b, 0x2f, 0x9a, 0xed, 0xbf, 0x35, 0xdf, 0x7f, 0xfb, 0xb7, 0x95, 0xf2, 0x49,
	0xbe, 0x83, 0x95, 0x0e, 0xa0, 0x1b, 0x7a, 0x42, 0xba, 0x93, 0x78, 0xa8, 0x0e, 0x00, 0x8c, 0x9f,
	0x35, 0x8c, 0x9f, 0x0d, 0xa5, 0x7f, 0x81, 0x6a, 0xcc, 0xca, 0x7f, 0xd6, 0xc9, 0xe1, 0x31, 0x93,
	0xd3, 0x95, 0x88, 0xb3, 0x0b, 0xed, 0x80, 0x72, 0x77, 0x5e, 0x1b, 0xe8, 0xaa, 0x01, 0x02, 0xca,
	0x4f, 0x4c, 0x79, 0xb0, 0x74, 0x40, 0x5a, 0xa9, 0x03, 0x72, 0xce, 0xb9, 0xea, 0x79, 0x9c, 0xab,
	0xe5, 0x38, 0x67, 0xff, 0xde, 0x82, 0xad, 0x02, 0x03, 0xdf, 0xe9, 0x51, 0xfc, 0x3e, 0x34, 0xd5,
	0x2f, 0x06, 0x1e, 0xda, 0xb4, 0xe6, 0x2c, 0x14, 0xe4, 0x36, 0x74, 0x98, 0x71, 0x99, 0x46, 0xac,
	0xeb, 0x13, 0x21, 0xa5, 0x24, 0x7d, 0x68, 0x8c, 0xb8, 0x01, 0xd4, 0xf5, 0xd9, 0x37, 0x93, 0x55,
	0x9b, 0xa0, 0x6f, 0x74, 0x5b, 0xc3, 0x1c, 0xb0, 0x46, 0x56, 0x6d, 0x5e, 0x40, 0x8f, 0x97, 0x0f,
	0xdf, 0x99, 0x5c, 0x70, 0xb4, 0xc3, 0x05, 0x47, 0x7b, 0x2b, 0x7d, 0xb4, 0xe7, 0x72, 0x4c, 0x7b,
	0x85, 0x1c, 0xd3, 0xc9, 0xe5, 0x18, 0xfb, 0x97, 0xa5, 0xbc, 0x11, 0x31, 0x79, 0x90, 0xe6, 0xaf,
	0x9d, 0xe1, 0x6f, 0x51, 0xaf, 0xb7, 0x8b, 0xd1, 0xbf, 0x55, 0xb0, 0x34, 0x40, 0xce, 0xad, 0xc4,
	0xdb, 0x6f, 0x59, 0xe8, 0x7e, 0x2b, 0xc2, 0xaa, 0x31, 0x99, 0x70, 0x87, 0xf4, 0x74, 0x12, 0x20,
	0x33, 0x1a, 0x4e, 0x9d, 0x89, 0x4f, 0x94, 0x68, 0xff, 0xc7, 0x82, 0xcb, 0x45, 0x56, 0xbf, 0x53,
	0x32, 0xff, 0x00, 0x96, 0x52, 0xa2, 0x61, 0xf3, 0x72, 0x92, 0xfc, 0x9e, 0xce, 0x0a, 0xc2, 0x13,
	0x16, 0xb0, 0x28, 0x55, 0x49, 0xb4, 0xb4, 0x4e, 0xdb, 0x7a, 0x13, 0xda, 0xaf, 0xf1, 0xf6, 0x62,
	0x20, 0x9b, 0x1a, 0xa2, 0x75, 0x73, 0x08, 0xb6, 0x99, 0xda, 0xc8, 0x54, 0x13, 0x2d, 0xd4, 0xe9,
	0xb2, 0xc8, 0xfe, 0x75, 0x39, 0x71, 0x45, 0x4c, 0x3e, 0x4a, 0x07, 0xce, 0xad, 0x4c, 0xe0, 0x14,
	0x76, 0x7b, 0xbb, 0xc8, 0xf9, 0x63, 0x0d, 0x1a, 0x58, 0x9c, 0xff, 0x7f, 0x15, 0xb3, 0xd7, 0x01,
	0x4c, 0xc1, 0xa8, 0x02, 0x5f, 0x97, 0xb3, 0x4d, 0xad, 0x79, 0xc1, 0x86, 0xdf, 0x5d, 0x41, 0x7b,
	0x15, 0xd6, 0x87, 0x34, 0xa4, 0x52, 0x13, 0xb1, 0xe1, 0x18, 0x49, 0xcd, 0x2f, 0x78, 0x22, 0x53,
	0xd5, 0x6c, 0x53, 0x69, 0x74, 0xb7, 0x3b, 0xb0, 0x31, 0x0b, 0x26, 0x03, 0xe9, 0x14, 0x85, 0xd8,
	0x0e, 0x34, 0x47, 0x5c, 0xa6, 0x28, 0xb8, 0x88, 0x31, 0xb5, 0x06, 0xfa, 0x26, 0x45, 0xbe, 0x45,
	0x90, 0x19, 0x87, 0x2d, 0x17, 0xb1, 0x8b, 0x28, 0xbb, 0x0e, 0xa0, 0x5c, 0x92, 0xaa, 0x5e, 0x97,
	0xb2, 0x59, 0x36, 0xa7, 0x93, 0x5c, 0x4e, 0x5f, 0x3e, 0x24, 0xb7, 0xd2, 0x87, 0x64, 0x41, 0xa5,
	0x7e, 0x19, 0x77, 0x3f, 0x53, 0xa9, 0xdb, 0xd0, 0x19, 0xb2, 0xe9, 0x7c, 0x12, 0xd1, 0xbb, 0xb2,
	0x6b, 0x0d, 0x9a, 0x4e, 0x6b, 0xc8, 0xa6, 0x66, 0x16, 0x61, 0xff, 0xa5, 0x02, 0xdb, 0xa9, 0x72,
	0x28, 0xe1, 0xc3, 0x89, 0x7f, 0x01, 0x55, 0xd5, 0xf1, 0x62, 0x28, 0x65, 0x18, 0x3f, 0x97, 0x49,
	0x0f, 0xea, 0x86, 0x4a, 0xa6, 0x96, 0x9d, 0x89, 0x58, 0x89, 0xce, 0x09, 0x55, 0x45, 0x42, 0x35,
	0xfc, 0x62, 0x32, 0xd5, 0x32, 0x64, 0xb2, 0xff, 0x64, 0x41, 0x47, 0x85, 0xd0, 0x93, 0x37, 0x32,
	0xf1, 0x54, 0x8d, 0x5b, 0x6e, 0xdc, 0x36, 0xd4, 0x59, 0xec, 0x62, 0x6c, 0x98, 0xb4, 0xc3, 0x62,
	0x95, 0xfd, 0x32, 0x53, 0x58, 0x59, 0xbe, 0xe6, 0x49, 0x5f, 0x2d, 0x20, 0x7d, 0xd6, 0x71, 0xb5,
	0x73, 0x1d, 0xb7, 0x7e, 0xa1, 0xe3, 0xea, 0x45, 0x57, 0x2c, 0x13, 0xed, 0x8d, 0x45, 0xb4, 0x67,
	0xee, 0x54, 0xcd, 0xdc, 0x9d, 0xaa, 0xe0, 0xca, 0x04, 0x45, 0x57, 0xa6, 0x0f, 0xe1, 0xca, 0x02,
	0xb7, 0x5c, 0xab, 0xb6, 0xd0, 0x10, 0x32, 0x43, 0x2f, 0xea, 0xd5, 0x3c, 0x8d, 0xda, 0x79, 0x1a,
	0x7d, 0x05, 0xeb, 0x0e, 0x8d, 0x79, 0x22, 0xd5, 0x36, 0x8e, 0x98, 0x9b, 0xa0, 0xa0, 0xd3, 0x87,
	0x2e, 0x09, 0xda, 0x23, 0xa6, 0x11, 0xb3, 0xdb, 0x2e, 0x6e, 0x36, 0xde, 0x68, 0xd6, 0x70, 0xbc,
	0x86, 0x52, 0xe0, 0x95, 0xa5, 0x0b, 0x56, 0x48, 0xa3, 0xd9, 0x61, 0x17, 0xd2, 0x08, 0x5f, 0x42,
	0x94, 0x91, 0xda, 0x23, 0xf8, 0x7f, 0xf6, 0xc6, 0xa2, 0x2f, 0x77, 0x78, 0x2c, 0x3f, 0x37, 0x03,
	0xa8, 0x72, 0x44, 0xf5, 0x58, 0x4c, 0x8e, 0xff, 0x55, 0x71, 0x81, 0x8b, 0x30, 0xbc, 0xd0, 0x82,
	0xf2, 0xa8, 0x21, 0x92, 0xb6, 0xc6, 0x42, 0x6b, 0x40, 0xb3, 0x09, 0xed, 0xb9, 0x0a, 0xeb, 0xfc,
	0xec, 0x4c, 0xd0, 0x59, 0x55, 0x62, 0xa4, 0x45, 0xb1, 0x52, 0x5b, 0x2e, 0x56, 0x76, 0xa1, 0x8d,
	0xc7, 0xd2, 0xe9, 0xd4, 0xf5, 0xc2, 0x80, 0x9b, 0x6a, 0x04, 0x8f, 0xaa, 0x47, 0xd3, 0x87, 0x61,
	0xc0, 0xed, 0xdf, 0x59, 0xa5, 0x96, 0x8b, 0x98, 0xbc, 0x58, 0xde, 0x1a, 0x9d, 0x93, 0x1e, 0x64,
	0x72, 0x52, 0x69, 0xe7, 0xbd, 0x8c, 0x7a, 0x69, 0x53, 0xf1, 0xda, 0x2e, 0xbd, 0xd0, 0x30, 0x48,
	0x27, 0x18, 0x40, 0x15, 0x32, 0xa8, 0xff, 0xef, 0x0a, 0x6c, 0x66, 0xba, 0x97, 0x07, 0x59, 0x36,
	0x77, 0xaf, 0x5d, 0x9c, 0xbb, 0xad, 0x7c, 0xee, 0x3e, 0x80, 0x2b, 0xcb, 0x10, 0xb5, 0x38, 0x26,
	0x19, 0x8f, 0xcc, 0x33, 0xe7, 0xd6, 0x12, 0xf6, 0xb9, 0x69, 0xca, 0xe5, 0xfb, 0x5a, 0x2e, 0xdf,
	0xab, 0xab, 0x98, 0x26, 0xab, 0xda, 0x7f, 0x33, 0xbb, 0x2e, 0xaf, 0x36, 0x50, 0xaf, 0x9c, 0x80,
	0x83, 0xda, 0x21, 0x56, 0x06, 0xe9, 0xeb, 0xe0, 0x82, 0x43, 0x59, 0x66, 0x54, 0xce, 0x61, 0xc6,
	0x5a, 0x31, 0x33, 0xac, 0x25, 0x66, 0xd8, 0xbf, 0xb1, 0xca, 0xa7, 0x13, 0x31, 0x79, 0x99, 0x77,
	0xfc, 0x47, 0x79, 0xc7, 0x97, 0xf4, 0xde, 0x43, 0xfd, 0x42, 0x5d, 0xee, 0xf9, 0x6a, 0xce, 0xf3,
	0xff, 0xaa, 0xc0, 0x66, 0xa6, 0x3b, 0xf9, 0x00, 0xaa, 0x6a, 0x00, 0x74, 0x7b, 0xeb, 0x60, 0xbb,
	0xe0, 0xe9, 0x13, 0x2f, 0xbf, 0x08, 0x22, 0x2f, 0x61, 0x20, 0xf0, 0x7e, 0xec, 0x26, 0xb3, 0x42,
	0xc9, 0x15, 0xc8, 0x22, 0x37, 0xf1, 0xa2, 0x2f, 0x59, 0x14, 0x2c, 0x5c, 0xab, 0x99, 0x72, 0x5b,
	0x14, 0x3e, 0x39, 0x68, 0xf0, 0xdc, 0xd7, 0x27, 0x70, 0x57, 0x3b, 0xf2, 0xe2, 0x61, 0x35, 0xbb,
	0x6e, 0xc9, 0xa2, 0x6a, 0x2d, 0x3d, 0xaa, 0xed, 0x42, 0xef, 0x19, 0x95, 0x2f, 0x39, 0xf3, 0xe9,
	0x63, 0x1e, 0xc9, 0x84, 0x87, 0x6f, 0xff, 0x6c, 0x93, 0x7a, 0x55, 0xb1, 0x32, 0xaf, 0x2a, 0x3f,
	0x83, 0xab, 0xf9, 0xd1, 0xcf, 0xcf, 0xa8, 0x4b, 0xef, 0x4c, 0x6b, 0xa9, 0x77, 0xa6, 0x6f, 0x2a,
	0x65, 0xd6, 0x8a, 0x98, 0x3c, 0x82, 0x26, 0x53, 0xbb, 0xbe, 0xc4, 0x98, 0x3b, 0x19, 0x4f, 0x15,
	0x1b, 0xe2, 0x34, 0x54, 0x3f, 0x64, 0xc7, 0x8a, 0x35, 0xec, 0x3e, 0xec, 0x3c, 0x1e, 0x51, 0xff,
	0xcb, 0xc3, 0x68, 0x79, 0xc4, 0x57, 0x23, 0x26, 0x69, 0xe1, 0xbe, 0xd9, 0x0f, 0xce, 0xe9, 0x20,
	0x62, 0xbc, 0xaf, 0x45, 0xee, 0x6b, 0x25, 0x62, 0x2f, 0x75, 0x5f, 0x8b, 0xb0, 0xf5, 0xfe, 0x3e,
	0x6c, 0xa4, 0x9f, 0xd6, 0x49, 0x0b, 0xea, 0x87, 0x9f, 0xbd, 0x7c, 0x78, 0x74, 0xf8, 0x49, 0xf7,
	0x3d, 0x52, 0x07, 0xeb, 0xe7, 0x9f, 0x1f, 0x75, 0x2b, 0xea, 0xcf, 0xc9, 0xb3, 0xa3, 0xee, 0xda,
	0xfd, 0x5f, 0x41, 0xfb, 0xd3, 0xe5, 0xe4, 0xd2, 0x81, 0xe6, 0xab, 0x84, 0x47, 0x81, 0x12, 0xba,
	0xef, 0xa9, 0xde, 0x9f, 0xd1, 0xd7, 0x28, 0x54, 0x48, 0x17, 0xda, 0xb3, 0xe8, 0x41, 0xcd, 0x9a,
	0x42, 0x3f, 0x51, 0x1a, 0x14, 0x2d, 0x25, 0x9e, 0xa8, 0xd0, 0x40, 0xb1, 0x4a, 0x36, 0x00, 0x9e,
	0xe8, 0x0f, 0x2a, 0x4a, 0xae, 0x91, 0x4b, 0xd0, 0x79, 0xa2, 0x28, 0x10, 0xa9, 0xec, 0xa8, 0x54,
	0xeb, 0x07, 0xff, 0x68, 0x42, 0xc3, 0xf1, 0xc7, 0x43, 0x8c, 0x1b, 0x0f, 0x48, 0xfe, 0x93, 0x06,
	0xb9, 0x9d, 0x8f, 0xe3, 0xfc, 0x15, 0xba, 0x7f, 0x67, 0x05, 0x94, 0x88, 0xc9, 0x18, 0xbf, 0x9a,
	0x14, 0x3c, 0xd1, 0x93, 0x41, 0x7e, 0x80, 0xe2, 0x4f, 0x10, 0xfd, 0x7b, 0x2b, 0x22, 0x45, 0x4c,
	0x04, 0x12, 0xb0, 0xf0, 0xfd, 0x8b, 0xdc, 0xcf, 0x0f, 0x53, 0xf6, 0x22, 0xda, 0xff, 0x60, 0x65,
	0xac, 0x88, 0xc9, 0x17, 0xd0, 0xcd, 0x7e, 0xb9, 0x22, 0xd9, 0x27, 0x8d, 0x82, 0x8f, 0x6e, 0xfd,
	0x0b, 0x31, 0x22, 0x26, 0x21, 0xee, 0x5f, 0xd1, 0xeb, 0x56, 0xc1, 0xfe, 0x15, 0xbf, 0xd2, 0xf5,
	0x57, 0x44, 0x8a, 0x98, 0x70, 0xd8, 0x2e, 0xb9, 0x7c, 0x92, 0x7b, 0x25, 0x69, 0x7d, 0x35, 0x7f,
	0x95, 0xdd, 0x67, 0x39, 0x6c, 0x97, 0x3c, 0x73, 0xe6, 0x26, 0x2c, 0x7f, 0x73, 0xed, 0xaf, 0x0a,
	0x9d, 0xaf, 0xb0, 0x28, 0x2f, 0x15, 0xad, 0xb0, 0x24, 0xd9, 0xf6, 0xef, 0xad, 0x9c, 0xea, 0x8c,
	0x03, 0x0b, 0x2a, 0xa0, 0x22, 0x07, 0x16, 0xd7, 0x87, 0xfd, 0xc1, 0xaa, 0x25, 0x15, 0x61, 0xf8,
	0x6d, 0x34, 0x7f, 0x8e, 0x92, 0xbb, 0xf9, 0x21, 0x0a, 0x93, 0x4a, 0x7f, 0x35, 0xa0, 0x88, 0x49,
	0x02, 0xbd, 0xb2, 0x33, 0x33, 0x17, 0x6a, 0xe7, 0x9c, 0xc6, 0xfd, 0x95, 0xb1, 0x22, 0x7e, 0xf4,
	0xe1, 0x17, 0xfb, 0x01, 0x0f, 0xbd, 0x28, 0xd8, 0xfb, 0xc9, 0x81, 0x94, 0x7b, 0x3e, 0x1f, 0xef,
	0xe3, 0x77, 0x6d, 0x9f, 0x87, 0xfb, 0x82, 0x26, 0x5f, 0x33, 0x9f, 0x8a, 0xfd, 0xd4, 0xb7, 0xf1,
	0xd3, 0x75, 0x04, 0xfc, 0xf8, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xab, 0x66, 0x9d, 0xed, 0x33,
	0x1f, 0x00, 0x00,
}
