// Code generated by protoc-gen-gogo.
// source: services/sdkmsg/sdkmsg.proto
// DO NOT EDIT!

/*
	Package Sdkmsg is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/sdkmsg/sdkmsg.proto

	It has these top-level messages:
		SdkMsgInfo
		GetSdkMsgsReq
		GetSdkMsgsResp
		DelSdkMsgReq
		DelSdkMsgResp
		UpdateSdkMsgReq
		UpdateSdkMsgResp
		GetSdkMsgByIdReq
		GetSdkMsgByIdResp
*/
package Sdkmsg

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type SdkMsgInfo struct {
	// 消息内容
	MsgId      uint32 `protobuf:"varint,1,req,name=msg_id,json=msgId" json:"msg_id"`
	Title      string `protobuf:"bytes,2,req,name=title" json:"title"`
	Url        string `protobuf:"bytes,3,req,name=url" json:"url"`
	BeginTime  string `protobuf:"bytes,4,req,name=begin_time,json=beginTime" json:"begin_time"`
	EndTime    string `protobuf:"bytes,5,req,name=end_time,json=endTime" json:"end_time"`
	MsgStyle   uint32 `protobuf:"varint,6,req,name=msg_style,json=msgStyle" json:"msg_style"`
	LimitTimes uint32 `protobuf:"varint,7,req,name=limit_times,json=limitTimes" json:"limit_times"`
	LimitType  uint32 `protobuf:"varint,8,req,name=limit_type,json=limitType" json:"limit_type"`
	// 匹配条件
	PullType      uint32 `protobuf:"varint,9,req,name=pull_type,json=pullType" json:"pull_type"`
	GameIdList    string `protobuf:"bytes,10,req,name=game_id_list,json=gameIdList" json:"game_id_list"`
	ChannelIdList string `protobuf:"bytes,11,req,name=channel_id_list,json=channelIdList" json:"channel_id_list"`
	UserIdList    string `protobuf:"bytes,12,req,name=user_id_list,json=userIdList" json:"user_id_list"`
	// 数据库状态
	Rank        uint32 `protobuf:"varint,13,req,name=rank" json:"rank"`
	Status      uint32 `protobuf:"varint,14,req,name=status" json:"status"`
	ContentText string `protobuf:"bytes,15,opt,name=content_text,json=contentText" json:"content_text"`
}

func (m *SdkMsgInfo) Reset()                    { *m = SdkMsgInfo{} }
func (m *SdkMsgInfo) String() string            { return proto.CompactTextString(m) }
func (*SdkMsgInfo) ProtoMessage()               {}
func (*SdkMsgInfo) Descriptor() ([]byte, []int) { return fileDescriptorSdkmsg, []int{0} }

func (m *SdkMsgInfo) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *SdkMsgInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SdkMsgInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *SdkMsgInfo) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *SdkMsgInfo) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *SdkMsgInfo) GetMsgStyle() uint32 {
	if m != nil {
		return m.MsgStyle
	}
	return 0
}

func (m *SdkMsgInfo) GetLimitTimes() uint32 {
	if m != nil {
		return m.LimitTimes
	}
	return 0
}

func (m *SdkMsgInfo) GetLimitType() uint32 {
	if m != nil {
		return m.LimitType
	}
	return 0
}

func (m *SdkMsgInfo) GetPullType() uint32 {
	if m != nil {
		return m.PullType
	}
	return 0
}

func (m *SdkMsgInfo) GetGameIdList() string {
	if m != nil {
		return m.GameIdList
	}
	return ""
}

func (m *SdkMsgInfo) GetChannelIdList() string {
	if m != nil {
		return m.ChannelIdList
	}
	return ""
}

func (m *SdkMsgInfo) GetUserIdList() string {
	if m != nil {
		return m.UserIdList
	}
	return ""
}

func (m *SdkMsgInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SdkMsgInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *SdkMsgInfo) GetContentText() string {
	if m != nil {
		return m.ContentText
	}
	return ""
}

type GetSdkMsgsReq struct {
	Type uint32 `protobuf:"varint,1,req,name=type" json:"type"`
}

func (m *GetSdkMsgsReq) Reset()                    { *m = GetSdkMsgsReq{} }
func (m *GetSdkMsgsReq) String() string            { return proto.CompactTextString(m) }
func (*GetSdkMsgsReq) ProtoMessage()               {}
func (*GetSdkMsgsReq) Descriptor() ([]byte, []int) { return fileDescriptorSdkmsg, []int{1} }

func (m *GetSdkMsgsReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetSdkMsgsResp struct {
	MsgList []*SdkMsgInfo `protobuf:"bytes,1,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
}

func (m *GetSdkMsgsResp) Reset()                    { *m = GetSdkMsgsResp{} }
func (m *GetSdkMsgsResp) String() string            { return proto.CompactTextString(m) }
func (*GetSdkMsgsResp) ProtoMessage()               {}
func (*GetSdkMsgsResp) Descriptor() ([]byte, []int) { return fileDescriptorSdkmsg, []int{2} }

func (m *GetSdkMsgsResp) GetMsgList() []*SdkMsgInfo {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type DelSdkMsgReq struct {
	MsgId uint32 `protobuf:"varint,1,req,name=msg_id,json=msgId" json:"msg_id"`
}

func (m *DelSdkMsgReq) Reset()                    { *m = DelSdkMsgReq{} }
func (m *DelSdkMsgReq) String() string            { return proto.CompactTextString(m) }
func (*DelSdkMsgReq) ProtoMessage()               {}
func (*DelSdkMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorSdkmsg, []int{3} }

func (m *DelSdkMsgReq) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

type DelSdkMsgResp struct {
}

func (m *DelSdkMsgResp) Reset()                    { *m = DelSdkMsgResp{} }
func (m *DelSdkMsgResp) String() string            { return proto.CompactTextString(m) }
func (*DelSdkMsgResp) ProtoMessage()               {}
func (*DelSdkMsgResp) Descriptor() ([]byte, []int) { return fileDescriptorSdkmsg, []int{4} }

type UpdateSdkMsgReq struct {
	Msg *SdkMsgInfo `protobuf:"bytes,1,req,name=msg" json:"msg,omitempty"`
}

func (m *UpdateSdkMsgReq) Reset()                    { *m = UpdateSdkMsgReq{} }
func (m *UpdateSdkMsgReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateSdkMsgReq) ProtoMessage()               {}
func (*UpdateSdkMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorSdkmsg, []int{5} }

func (m *UpdateSdkMsgReq) GetMsg() *SdkMsgInfo {
	if m != nil {
		return m.Msg
	}
	return nil
}

type UpdateSdkMsgResp struct {
	MsgId uint32 `protobuf:"varint,1,req,name=msg_id,json=msgId" json:"msg_id"`
}

func (m *UpdateSdkMsgResp) Reset()                    { *m = UpdateSdkMsgResp{} }
func (m *UpdateSdkMsgResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateSdkMsgResp) ProtoMessage()               {}
func (*UpdateSdkMsgResp) Descriptor() ([]byte, []int) { return fileDescriptorSdkmsg, []int{6} }

func (m *UpdateSdkMsgResp) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

type GetSdkMsgByIdReq struct {
	MsgId uint32 `protobuf:"varint,1,req,name=msg_id,json=msgId" json:"msg_id"`
}

func (m *GetSdkMsgByIdReq) Reset()                    { *m = GetSdkMsgByIdReq{} }
func (m *GetSdkMsgByIdReq) String() string            { return proto.CompactTextString(m) }
func (*GetSdkMsgByIdReq) ProtoMessage()               {}
func (*GetSdkMsgByIdReq) Descriptor() ([]byte, []int) { return fileDescriptorSdkmsg, []int{7} }

func (m *GetSdkMsgByIdReq) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

type GetSdkMsgByIdResp struct {
	Msg *SdkMsgInfo `protobuf:"bytes,2,req,name=msg" json:"msg,omitempty"`
}

func (m *GetSdkMsgByIdResp) Reset()                    { *m = GetSdkMsgByIdResp{} }
func (m *GetSdkMsgByIdResp) String() string            { return proto.CompactTextString(m) }
func (*GetSdkMsgByIdResp) ProtoMessage()               {}
func (*GetSdkMsgByIdResp) Descriptor() ([]byte, []int) { return fileDescriptorSdkmsg, []int{8} }

func (m *GetSdkMsgByIdResp) GetMsg() *SdkMsgInfo {
	if m != nil {
		return m.Msg
	}
	return nil
}

func init() {
	proto.RegisterType((*SdkMsgInfo)(nil), "Sdkmsg.SdkMsgInfo")
	proto.RegisterType((*GetSdkMsgsReq)(nil), "Sdkmsg.GetSdkMsgsReq")
	proto.RegisterType((*GetSdkMsgsResp)(nil), "Sdkmsg.GetSdkMsgsResp")
	proto.RegisterType((*DelSdkMsgReq)(nil), "Sdkmsg.DelSdkMsgReq")
	proto.RegisterType((*DelSdkMsgResp)(nil), "Sdkmsg.DelSdkMsgResp")
	proto.RegisterType((*UpdateSdkMsgReq)(nil), "Sdkmsg.UpdateSdkMsgReq")
	proto.RegisterType((*UpdateSdkMsgResp)(nil), "Sdkmsg.UpdateSdkMsgResp")
	proto.RegisterType((*GetSdkMsgByIdReq)(nil), "Sdkmsg.GetSdkMsgByIdReq")
	proto.RegisterType((*GetSdkMsgByIdResp)(nil), "Sdkmsg.GetSdkMsgByIdResp")
}
func (m *SdkMsgInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SdkMsgInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.MsgId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x22
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(len(m.BeginTime)))
	i += copy(dAtA[i:], m.BeginTime)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(len(m.EndTime)))
	i += copy(dAtA[i:], m.EndTime)
	dAtA[i] = 0x30
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.MsgStyle))
	dAtA[i] = 0x38
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.LimitTimes))
	dAtA[i] = 0x40
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.LimitType))
	dAtA[i] = 0x48
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.PullType))
	dAtA[i] = 0x52
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(len(m.GameIdList)))
	i += copy(dAtA[i:], m.GameIdList)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(len(m.ChannelIdList)))
	i += copy(dAtA[i:], m.ChannelIdList)
	dAtA[i] = 0x62
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(len(m.UserIdList)))
	i += copy(dAtA[i:], m.UserIdList)
	dAtA[i] = 0x68
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.Rank))
	dAtA[i] = 0x70
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x7a
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(len(m.ContentText)))
	i += copy(dAtA[i:], m.ContentText)
	return i, nil
}

func (m *GetSdkMsgsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSdkMsgsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *GetSdkMsgsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSdkMsgsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintSdkmsg(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *DelSdkMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelSdkMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.MsgId))
	return i, nil
}

func (m *DelSdkMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelSdkMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UpdateSdkMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateSdkMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSdkmsg(dAtA, i, uint64(m.Msg.Size()))
		n1, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *UpdateSdkMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateSdkMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.MsgId))
	return i, nil
}

func (m *GetSdkMsgByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSdkMsgByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintSdkmsg(dAtA, i, uint64(m.MsgId))
	return i, nil
}

func (m *GetSdkMsgByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSdkMsgByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSdkmsg(dAtA, i, uint64(m.Msg.Size()))
		n2, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func encodeFixed64Sdkmsg(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Sdkmsg(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintSdkmsg(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *SdkMsgInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSdkmsg(uint64(m.MsgId))
	l = len(m.Title)
	n += 1 + l + sovSdkmsg(uint64(l))
	l = len(m.Url)
	n += 1 + l + sovSdkmsg(uint64(l))
	l = len(m.BeginTime)
	n += 1 + l + sovSdkmsg(uint64(l))
	l = len(m.EndTime)
	n += 1 + l + sovSdkmsg(uint64(l))
	n += 1 + sovSdkmsg(uint64(m.MsgStyle))
	n += 1 + sovSdkmsg(uint64(m.LimitTimes))
	n += 1 + sovSdkmsg(uint64(m.LimitType))
	n += 1 + sovSdkmsg(uint64(m.PullType))
	l = len(m.GameIdList)
	n += 1 + l + sovSdkmsg(uint64(l))
	l = len(m.ChannelIdList)
	n += 1 + l + sovSdkmsg(uint64(l))
	l = len(m.UserIdList)
	n += 1 + l + sovSdkmsg(uint64(l))
	n += 1 + sovSdkmsg(uint64(m.Rank))
	n += 1 + sovSdkmsg(uint64(m.Status))
	l = len(m.ContentText)
	n += 1 + l + sovSdkmsg(uint64(l))
	return n
}

func (m *GetSdkMsgsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSdkmsg(uint64(m.Type))
	return n
}

func (m *GetSdkMsgsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovSdkmsg(uint64(l))
		}
	}
	return n
}

func (m *DelSdkMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSdkmsg(uint64(m.MsgId))
	return n
}

func (m *DelSdkMsgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UpdateSdkMsgReq) Size() (n int) {
	var l int
	_ = l
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovSdkmsg(uint64(l))
	}
	return n
}

func (m *UpdateSdkMsgResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSdkmsg(uint64(m.MsgId))
	return n
}

func (m *GetSdkMsgByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovSdkmsg(uint64(m.MsgId))
	return n
}

func (m *GetSdkMsgByIdResp) Size() (n int) {
	var l int
	_ = l
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovSdkmsg(uint64(l))
	}
	return n
}

func sovSdkmsg(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozSdkmsg(x uint64) (n int) {
	return sovSdkmsg(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *SdkMsgInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SdkMsgInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SdkMsgInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgId", wireType)
			}
			m.MsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BeginTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.EndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgStyle", wireType)
			}
			m.MsgStyle = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgStyle |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitTimes", wireType)
			}
			m.LimitTimes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitTimes |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitType", wireType)
			}
			m.LimitType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PullType", wireType)
			}
			m.PullType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PullType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIdList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameIdList = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelIdList = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserIdList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserIdList = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rank", wireType)
			}
			m.Rank = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rank |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00001000)
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00002000)
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContentText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ContentText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSdkmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSdkmsg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("begin_time")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_style")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_times")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_type")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pull_type")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id_list")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id_list")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_id_list")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rank")
	}
	if hasFields[0]&uint64(0x00002000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSdkMsgsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSdkMsgsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSdkMsgsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSdkmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSdkmsg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSdkMsgsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSdkMsgsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSdkMsgsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &SdkMsgInfo{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSdkmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSdkmsg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelSdkMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelSdkMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelSdkMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgId", wireType)
			}
			m.MsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSdkmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSdkmsg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelSdkMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelSdkMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelSdkMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSdkmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSdkmsg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateSdkMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateSdkMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateSdkMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &SdkMsgInfo{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSdkmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSdkmsg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateSdkMsgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateSdkMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateSdkMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgId", wireType)
			}
			m.MsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSdkmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSdkmsg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSdkMsgByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSdkMsgByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSdkMsgByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgId", wireType)
			}
			m.MsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSdkmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSdkmsg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSdkMsgByIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSdkMsgByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSdkMsgByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSdkmsg
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &SdkMsgInfo{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipSdkmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSdkmsg
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipSdkmsg(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSdkmsg
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSdkmsg
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthSdkmsg
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowSdkmsg
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipSdkmsg(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthSdkmsg = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSdkmsg   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/sdkmsg/sdkmsg.proto", fileDescriptorSdkmsg) }

var fileDescriptorSdkmsg = []byte{
	// 672 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x53, 0x41, 0x4f, 0x13, 0x5d,
	0x14, 0x65, 0x3a, 0xa5, 0xd0, 0xdb, 0x29, 0xe5, 0x7b, 0xf9, 0xc0, 0xe7, 0x48, 0xca, 0x38, 0x22,
	0x62, 0x14, 0x9a, 0xe0, 0xc2, 0xd8, 0x10, 0x4c, 0x88, 0x89, 0x69, 0x82, 0x1b, 0x40, 0xb7, 0x4d,
	0xe9, 0x3c, 0xc7, 0x97, 0xce, 0x9b, 0x3e, 0x7b, 0xdf, 0x10, 0xba, 0x73, 0x69, 0x5c, 0x19, 0xe2,
	0x4f, 0xe0, 0xc7, 0xb0, 0xf4, 0x17, 0x18, 0x83, 0x1b, 0x56, 0xfe, 0x06, 0xf3, 0x66, 0xa6, 0xe5,
	0x81, 0x05, 0x37, 0x90, 0x9e, 0x73, 0xee, 0x39, 0xf7, 0xde, 0x77, 0x07, 0x96, 0x90, 0x0d, 0x8e,
	0x78, 0x97, 0x61, 0x03, 0x83, 0x9e, 0xc0, 0x30, 0xff, 0xb7, 0x21, 0x07, 0x7d, 0xd5, 0x27, 0xa5,
	0xfd, 0xf4, 0x97, 0xbb, 0xd2, 0xed, 0x0b, 0xd1, 0x8f, 0x1b, 0x2a, 0x3a, 0x92, 0xbc, 0xdb, 0x8b,
	0x58, 0x03, 0x7b, 0x87, 0x09, 0x8f, 0x14, 0x8f, 0xd5, 0x50, 0xb2, 0x4c, 0xed, 0x7f, 0x2b, 0x02,
	0xec, 0x07, 0xbd, 0x37, 0x18, 0xb6, 0xe2, 0xf7, 0x7d, 0x72, 0x0f, 0x4a, 0x02, 0xc3, 0x36, 0x0f,
	0xa8, 0xe5, 0x15, 0xd6, 0xaa, 0x3b, 0xc5, 0xb3, 0x1f, 0xcb, 0x53, 0x7b, 0xd3, 0x02, 0xc3, 0x56,
	0x40, 0x5c, 0x98, 0x56, 0x5c, 0x45, 0x8c, 0x16, 0xbc, 0xc2, 0x5a, 0x79, 0xc4, 0xa5, 0x10, 0x59,
	0x04, 0x3b, 0x19, 0x44, 0xd4, 0x36, 0x18, 0x0d, 0x90, 0x07, 0x00, 0x87, 0x2c, 0xe4, 0x71, 0x5b,
	0x71, 0xc1, 0x68, 0xd1, 0xa0, 0xcb, 0x29, 0x7e, 0xc0, 0x05, 0x23, 0xcb, 0x30, 0xcb, 0xe2, 0x20,
	0x93, 0x4c, 0x1b, 0x92, 0x19, 0x16, 0x07, 0xa9, 0xe0, 0x3e, 0x94, 0x75, 0x5b, 0xa8, 0x86, 0x11,
	0xa3, 0x25, 0xa3, 0xb3, 0x59, 0x81, 0xe1, 0xbe, 0x46, 0xc9, 0x43, 0xa8, 0x44, 0x5c, 0x70, 0x95,
	0xba, 0x20, 0x9d, 0x31, 0x44, 0x90, 0x12, 0xda, 0x08, 0x75, 0x3f, 0xb9, 0x6c, 0x28, 0x19, 0x9d,
	0x35, 0x54, 0xe5, 0x4c, 0x35, 0x94, 0x69, 0x9c, 0x4c, 0xa2, 0x28, 0xd3, 0x94, 0xcd, 0x38, 0x0d,
	0xa7, 0x92, 0x55, 0x70, 0xc2, 0x8e, 0x60, 0x6d, 0x1e, 0xb4, 0x23, 0x8e, 0x8a, 0x82, 0xd1, 0x36,
	0x68, 0xa6, 0x15, 0xec, 0x72, 0x54, 0xe4, 0x29, 0xd4, 0xba, 0x1f, 0x3a, 0x71, 0xcc, 0xa2, 0xb1,
	0xb4, 0x62, 0x48, 0xab, 0x39, 0x99, 0xab, 0x57, 0xc1, 0x49, 0x90, 0x0d, 0xc6, 0x52, 0xc7, 0x74,
	0xd5, 0x4c, 0xae, 0xa3, 0x50, 0x1c, 0x74, 0xe2, 0x1e, 0xad, 0x1a, 0xbd, 0xa5, 0x08, 0x59, 0x82,
	0x12, 0xaa, 0x8e, 0x4a, 0x90, 0xce, 0x19, 0x5c, 0x8e, 0x91, 0x47, 0xe0, 0x74, 0xfb, 0xb1, 0x62,
	0xb1, 0x6a, 0x2b, 0x76, 0xac, 0x68, 0xcd, 0xb3, 0xc6, 0xfe, 0x95, 0x9c, 0x39, 0x60, 0xc7, 0xca,
	0x7f, 0x0c, 0xd5, 0xd7, 0x4c, 0x65, 0x87, 0x81, 0x7b, 0xec, 0xa3, 0x4e, 0x4c, 0xb7, 0x61, 0x9e,
	0x45, 0x8a, 0xf8, 0x2f, 0x61, 0xce, 0x94, 0xa2, 0x24, 0xeb, 0xa0, 0x9f, 0x25, 0x9b, 0xc0, 0xf2,
	0xec, 0xb5, 0xca, 0x26, 0xd9, 0xc8, 0x8e, 0x72, 0xe3, 0xf2, 0xd4, 0xf6, 0x66, 0x04, 0x86, 0x7a,
	0x18, 0xff, 0x09, 0x38, 0xaf, 0x58, 0x94, 0x31, 0x3a, 0xea, 0xb6, 0x1b, 0xf4, 0x6b, 0x50, 0x35,
	0xc4, 0x28, 0xfd, 0xe7, 0x50, 0x7b, 0x2b, 0x83, 0x8e, 0x62, 0x97, 0x06, 0x2b, 0x60, 0x0b, 0x0c,
	0xd3, 0xea, 0xc9, 0xd1, 0x9a, 0xf6, 0x1b, 0x30, 0x7f, 0xb5, 0x10, 0xe5, 0xed, 0xd1, 0x0d, 0x98,
	0x1f, 0x0f, 0xba, 0x33, 0x6c, 0x05, 0xff, 0xec, 0xf5, 0x05, 0xfc, 0x77, 0xad, 0x00, 0xe5, 0xa8,
	0xb9, 0xc2, 0xad, 0xcd, 0x6d, 0x9e, 0xd8, 0x90, 0x7f, 0xc7, 0x64, 0x17, 0xe0, 0x72, 0xbf, 0x64,
	0x61, 0x54, 0x71, 0xe5, 0x79, 0xdc, 0xc5, 0x49, 0x30, 0x4a, 0xbf, 0xf6, 0xe9, 0xf4, 0xc2, 0xb6,
	0xbe, 0x9c, 0x5e, 0xd8, 0x53, 0x27, 0xfa, 0x0f, 0x79, 0x07, 0xe5, 0xf1, 0xfe, 0xc8, 0xff, 0xa3,
	0x2a, 0x73, 0xff, 0xee, 0xc2, 0x04, 0x14, 0xa5, 0xbf, 0xa4, 0xad, 0x0a, 0xda, 0xaa, 0x20, 0x9a,
	0xda, 0xac, 0xb2, 0x2e, 0xbc, 0xad, 0x6c, 0xfa, 0x6d, 0x72, 0x04, 0x8e, 0xb9, 0x4d, 0x72, 0x67,
	0x64, 0x72, 0xed, 0x71, 0x5c, 0x3a, 0x99, 0x40, 0xe9, 0x3f, 0xd3, 0x01, 0xb6, 0x0e, 0x70, 0x44,
	0x53, 0x35, 0xc3, 0x26, 0x36, 0xa3, 0x66, 0x92, 0x46, 0x51, 0x23, 0xca, 0x5b, 0x4f, 0xbc, 0xad,
	0xfc, 0x2b, 0xd9, 0x26, 0x87, 0xc6, 0xa1, 0xea, 0x1d, 0x13, 0xfa, 0xd7, 0x26, 0xf2, 0xb7, 0x72,
	0xef, 0xde, 0xc0, 0x8c, 0x66, 0x2b, 0xde, 0x30, 0x9b, 0x5b, 0xfa, 0x7c, 0x7a, 0x61, 0xff, 0x3e,
	0xde, 0x99, 0x3f, 0x3b, 0xaf, 0x5b, 0xdf, 0xcf, 0xeb, 0xd6, 0xcf, 0xf3, 0xba, 0xf5, 0xf5, 0x57,
	0x7d, 0xea, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x75, 0x3f, 0x59, 0xb0, 0x8a, 0x05, 0x00, 0x00,
}
