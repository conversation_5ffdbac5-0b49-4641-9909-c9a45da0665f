package channelgamematch

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/channelgamematch/channelgamematch.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for ChannelGameMatch service
const ChannelGameMatchMagic = uint16(15594)

// Client API for ChannelGameMatch service

type ChannelGameMatchClientInterface interface {
	AddChannel(ctx context.Context, uin uint32, in *AddChannelReq, opts ...svrkit.CallOption) (*AddChannelResp, error)
	UserEnterChannel(ctx context.Context, uin uint32, in *UserEnterChannelReq, opts ...svrkit.CallOption) (*UserEnterChannelResp, error)
	HandleChannelEvent(ctx context.Context, uin uint32, in *HandleChannelEventReq, opts ...svrkit.CallOption) (*HandleChannelEventResp, error)
	GetChannelList(ctx context.Context, uin uint32, in *GetChannelListReq, opts ...svrkit.CallOption) (*GetChannelListResp, error)
	GetGameMatchOptions(ctx context.Context, uin uint32, in *GetGameMatchOptionsReq, opts ...svrkit.CallOption) (*GetGameMatchOptionsResp, error)
	GetGameMatchUserCount(ctx context.Context, uin uint32, in *GetGameMatchUserCountReq, opts ...svrkit.CallOption) (*GetGameMatchUserCountResp, error)
	AddNoviceRecommendChannel(ctx context.Context, uin uint32, in *AddNoviceRecommendChannelReq, opts ...svrkit.CallOption) (*AddNoviceRecommendChannelResp, error)
	RemoveNoviceRecommendChannel(ctx context.Context, uin uint32, in *RemoveNoviceRecommendChannelReq, opts ...svrkit.CallOption) (*RemoveNoviceRecommendChannelResp, error)
	GetNoviceRecommendChannel(ctx context.Context, uin uint32, in *GetNoviceRecommendChannelReq, opts ...svrkit.CallOption) (*GetNoviceRecommendChannelResp, error)
	UpdateNoviceRecommendChannelStatus(ctx context.Context, uin uint32, in *UpdateNoviceRecommendChannelStatusReq, opts ...svrkit.CallOption) (*UpdateNoviceRecommendChannelStatusResp, error)
	GetRecommendChannelStatus(ctx context.Context, uin uint32, in *GetRecommendChannelStatusReq, opts ...svrkit.CallOption) (*GetRecommendChannelStatusResp, error)
}

type ChannelGameMatchClient struct {
	cc *svrkit.ClientConn
}

func NewChannelGameMatchClient(cc *svrkit.ClientConn) ChannelGameMatchClientInterface {
	return &ChannelGameMatchClient{cc}
}

const (
	commandChannelGameMatchGetSelfSvnInfo                     = 9995
	commandChannelGameMatchEcho                               = 9999
	commandChannelGameMatchAddChannel                         = 1
	commandChannelGameMatchUserEnterChannel                   = 2
	commandChannelGameMatchHandleChannelEvent                 = 3
	commandChannelGameMatchGetChannelList                     = 4
	commandChannelGameMatchGetGameMatchOptions                = 5
	commandChannelGameMatchGetGameMatchUserCount              = 6
	commandChannelGameMatchAddNoviceRecommendChannel          = 7
	commandChannelGameMatchRemoveNoviceRecommendChannel       = 8
	commandChannelGameMatchGetNoviceRecommendChannel          = 9
	commandChannelGameMatchUpdateNoviceRecommendChannelStatus = 10
	commandChannelGameMatchGetRecommendChannelStatus          = 11
)

func (c *ChannelGameMatchClient) AddChannel(ctx context.Context, uin uint32, in *AddChannelReq, opts ...svrkit.CallOption) (*AddChannelResp, error) {
	out := new(AddChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchAddChannel, "/channelgamematch.ChannelGameMatch/AddChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) UserEnterChannel(ctx context.Context, uin uint32, in *UserEnterChannelReq, opts ...svrkit.CallOption) (*UserEnterChannelResp, error) {
	out := new(UserEnterChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchUserEnterChannel, "/channelgamematch.ChannelGameMatch/UserEnterChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) HandleChannelEvent(ctx context.Context, uin uint32, in *HandleChannelEventReq, opts ...svrkit.CallOption) (*HandleChannelEventResp, error) {
	out := new(HandleChannelEventResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchHandleChannelEvent, "/channelgamematch.ChannelGameMatch/HandleChannelEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) GetChannelList(ctx context.Context, uin uint32, in *GetChannelListReq, opts ...svrkit.CallOption) (*GetChannelListResp, error) {
	out := new(GetChannelListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchGetChannelList, "/channelgamematch.ChannelGameMatch/GetChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) GetGameMatchOptions(ctx context.Context, uin uint32, in *GetGameMatchOptionsReq, opts ...svrkit.CallOption) (*GetGameMatchOptionsResp, error) {
	out := new(GetGameMatchOptionsResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchGetGameMatchOptions, "/channelgamematch.ChannelGameMatch/GetGameMatchOptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) GetGameMatchUserCount(ctx context.Context, uin uint32, in *GetGameMatchUserCountReq, opts ...svrkit.CallOption) (*GetGameMatchUserCountResp, error) {
	out := new(GetGameMatchUserCountResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchGetGameMatchUserCount, "/channelgamematch.ChannelGameMatch/GetGameMatchUserCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) AddNoviceRecommendChannel(ctx context.Context, uin uint32, in *AddNoviceRecommendChannelReq, opts ...svrkit.CallOption) (*AddNoviceRecommendChannelResp, error) {
	out := new(AddNoviceRecommendChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchAddNoviceRecommendChannel, "/channelgamematch.ChannelGameMatch/AddNoviceRecommendChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) RemoveNoviceRecommendChannel(ctx context.Context, uin uint32, in *RemoveNoviceRecommendChannelReq, opts ...svrkit.CallOption) (*RemoveNoviceRecommendChannelResp, error) {
	out := new(RemoveNoviceRecommendChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchRemoveNoviceRecommendChannel, "/channelgamematch.ChannelGameMatch/RemoveNoviceRecommendChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) GetNoviceRecommendChannel(ctx context.Context, uin uint32, in *GetNoviceRecommendChannelReq, opts ...svrkit.CallOption) (*GetNoviceRecommendChannelResp, error) {
	out := new(GetNoviceRecommendChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchGetNoviceRecommendChannel, "/channelgamematch.ChannelGameMatch/GetNoviceRecommendChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) UpdateNoviceRecommendChannelStatus(ctx context.Context, uin uint32, in *UpdateNoviceRecommendChannelStatusReq, opts ...svrkit.CallOption) (*UpdateNoviceRecommendChannelStatusResp, error) {
	out := new(UpdateNoviceRecommendChannelStatusResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchUpdateNoviceRecommendChannelStatus, "/channelgamematch.ChannelGameMatch/UpdateNoviceRecommendChannelStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGameMatchClient) GetRecommendChannelStatus(ctx context.Context, uin uint32, in *GetRecommendChannelStatusReq, opts ...svrkit.CallOption) (*GetRecommendChannelStatusResp, error) {
	out := new(GetRecommendChannelStatusResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGameMatchGetRecommendChannelStatus, "/channelgamematch.ChannelGameMatch/GetRecommendChannelStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
