// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/tt-rev-audit/tt-rev-audit.proto

package tt_rev_audit // import "golang.52tt.com/protocol/services/tt-rev-audit"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EchoRequest struct {
	Message              string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EchoRequest) Reset()         { *m = EchoRequest{} }
func (m *EchoRequest) String() string { return proto.CompactTextString(m) }
func (*EchoRequest) ProtoMessage()    {}
func (*EchoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_audit_97c94a4850fd2462, []int{0}
}
func (m *EchoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EchoRequest.Unmarshal(m, b)
}
func (m *EchoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EchoRequest.Marshal(b, m, deterministic)
}
func (dst *EchoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EchoRequest.Merge(dst, src)
}
func (m *EchoRequest) XXX_Size() int {
	return xxx_messageInfo_EchoRequest.Size(m)
}
func (m *EchoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EchoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EchoRequest proto.InternalMessageInfo

func (m *EchoRequest) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type EchoResponse struct {
	Message              string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EchoResponse) Reset()         { *m = EchoResponse{} }
func (m *EchoResponse) String() string { return proto.CompactTextString(m) }
func (*EchoResponse) ProtoMessage()    {}
func (*EchoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_audit_97c94a4850fd2462, []int{1}
}
func (m *EchoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EchoResponse.Unmarshal(m, b)
}
func (m *EchoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EchoResponse.Marshal(b, m, deterministic)
}
func (dst *EchoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EchoResponse.Merge(dst, src)
}
func (m *EchoResponse) XXX_Size() int {
	return xxx_messageInfo_EchoResponse.Size(m)
}
func (m *EchoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EchoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EchoResponse proto.InternalMessageInfo

func (m *EchoResponse) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func init() {
	proto.RegisterType((*EchoRequest)(nil), "tt_rev_audit.EchoRequest")
	proto.RegisterType((*EchoResponse)(nil), "tt_rev_audit.EchoResponse")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TTRevAuditClient is the client API for TTRevAudit service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TTRevAuditClient interface {
	Echo(ctx context.Context, in *EchoRequest, opts ...grpc.CallOption) (*EchoResponse, error)
}

type tTRevAuditClient struct {
	cc *grpc.ClientConn
}

func NewTTRevAuditClient(cc *grpc.ClientConn) TTRevAuditClient {
	return &tTRevAuditClient{cc}
}

func (c *tTRevAuditClient) Echo(ctx context.Context, in *EchoRequest, opts ...grpc.CallOption) (*EchoResponse, error) {
	out := new(EchoResponse)
	err := c.cc.Invoke(ctx, "/tt_rev_audit.TTRevAudit/Echo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TTRevAuditServer is the server API for TTRevAudit service.
type TTRevAuditServer interface {
	Echo(context.Context, *EchoRequest) (*EchoResponse, error)
}

func RegisterTTRevAuditServer(s *grpc.Server, srv TTRevAuditServer) {
	s.RegisterService(&_TTRevAudit_serviceDesc, srv)
}

func _TTRevAudit_Echo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EchoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TTRevAuditServer).Echo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_audit.TTRevAudit/Echo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TTRevAuditServer).Echo(ctx, req.(*EchoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _TTRevAudit_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tt_rev_audit.TTRevAudit",
	HandlerType: (*TTRevAuditServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Echo",
			Handler:    _TTRevAudit_Echo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/tt-rev-audit/tt-rev-audit.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/tt-rev-audit/tt-rev-audit.proto", fileDescriptor_tt_rev_audit_97c94a4850fd2462)
}

var fileDescriptor_tt_rev_audit_97c94a4850fd2462 = []byte{
	// 195 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xd2, 0x2b, 0x29, 0xd1, 0x2f,
	0x2c, 0xcd, 0x4c, 0xce, 0x2e, 0xce, 0xcc, 0x29, 0x4b, 0x2d, 0xd2, 0x2f, 0x29, 0xd1, 0x2d, 0x4a,
	0x2d, 0xd3, 0x4d, 0x2c, 0x4d, 0xc9, 0x2c, 0x41, 0xe1, 0xe8, 0x15, 0x14, 0xe5, 0x97, 0xe4, 0x0b,
	0xf1, 0x94, 0x94, 0xc4, 0x17, 0xa5, 0x96, 0xc5, 0x83, 0xc5, 0x94, 0xd4, 0xb9, 0xb8, 0x5d, 0x93,
	0x33, 0xf2, 0x83, 0x52, 0x0b, 0x4b, 0x53, 0x8b, 0x4b, 0x84, 0x24, 0xb8, 0xd8, 0x73, 0x53, 0x8b,
	0x8b, 0x13, 0xd3, 0x53, 0x25, 0x18, 0x15, 0x18, 0x35, 0x38, 0x83, 0x60, 0x5c, 0x25, 0x0d, 0x2e,
	0x1e, 0x88, 0xc2, 0xe2, 0x82, 0xfc, 0xbc, 0xe2, 0x54, 0xdc, 0x2a, 0x8d, 0xbc, 0xb9, 0xb8, 0x42,
	0x42, 0x82, 0x52, 0xcb, 0x1c, 0x41, 0x16, 0x08, 0xd9, 0x72, 0xb1, 0x80, 0xf4, 0x09, 0x49, 0xea,
	0x21, 0xdb, 0xab, 0x87, 0x64, 0xa9, 0x94, 0x14, 0x36, 0x29, 0x88, 0x35, 0x4e, 0x06, 0x51, 0x7a,
	0xe9, 0xf9, 0x39, 0x89, 0x79, 0xe9, 0x7a, 0xa6, 0x46, 0x25, 0x25, 0x7a, 0xc9, 0xf9, 0xb9, 0xfa,
	0x60, 0x6f, 0x24, 0xe7, 0xe7, 0xe8, 0x17, 0xa7, 0x16, 0x95, 0x65, 0x26, 0xa7, 0x16, 0xa3, 0xf8,
	0x32, 0x89, 0x0d, 0x2c, 0x6f, 0x0c, 0x08, 0x00, 0x00, 0xff, 0xff, 0xb6, 0x35, 0x15, 0xca, 0x18,
	0x01, 0x00, 0x00,
}
