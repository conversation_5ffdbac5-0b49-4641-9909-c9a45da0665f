// Code generated by protoc-gen-gogo.
// source: services/gamelotto/awardcurrency/awardcurrency.proto
// DO NOT EDIT!

/*
	Package awardcurrency is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/gamelotto/awardcurrency/awardcurrency.proto

	It has these top-level messages:
		GetUserCurrencyReq
		GetUserCurrencyResp
		UserCurrency
		GetUserConsumeLogResp
		AddUserCurrencyReq
		GetConsumeLogReq
		FreezeUserCurrencyReq
		ConfirmFrozenOrderReq
*/
package awardcurrency

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type AwardCurrencyType int32

const (
	AwardCurrencyType_CurrencyNone           AwardCurrencyType = 0
	AwardCurrencyType_WangzherongyaoFragment AwardCurrencyType = 1
)

var AwardCurrencyType_name = map[int32]string{
	0: "CurrencyNone",
	1: "WangzherongyaoFragment",
}
var AwardCurrencyType_value = map[string]int32{
	"CurrencyNone":           0,
	"WangzherongyaoFragment": 1,
}

func (x AwardCurrencyType) String() string {
	return proto.EnumName(AwardCurrencyType_name, int32(x))
}
func (AwardCurrencyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAwardcurrency, []int{0}
}

type ConfirmFrozenOrderReq_OP int32

const (
	ConfirmFrozenOrderReq_NOP      ConfirmFrozenOrderReq_OP = 0
	ConfirmFrozenOrderReq_COMMIT   ConfirmFrozenOrderReq_OP = 1
	ConfirmFrozenOrderReq_ROLLBACK ConfirmFrozenOrderReq_OP = 2
)

var ConfirmFrozenOrderReq_OP_name = map[int32]string{
	0: "NOP",
	1: "COMMIT",
	2: "ROLLBACK",
}
var ConfirmFrozenOrderReq_OP_value = map[string]int32{
	"NOP":      0,
	"COMMIT":   1,
	"ROLLBACK": 2,
}

func (x ConfirmFrozenOrderReq_OP) String() string {
	return proto.EnumName(ConfirmFrozenOrderReq_OP_name, int32(x))
}
func (ConfirmFrozenOrderReq_OP) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorAwardcurrency, []int{7, 0}
}

type GetUserCurrencyReq struct {
	Uid  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type AwardCurrencyType `protobuf:"varint,2,opt,name=type,proto3,enum=awardcurrency.AwardCurrencyType" json:"type,omitempty"`
}

func (m *GetUserCurrencyReq) Reset()                    { *m = GetUserCurrencyReq{} }
func (m *GetUserCurrencyReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserCurrencyReq) ProtoMessage()               {}
func (*GetUserCurrencyReq) Descriptor() ([]byte, []int) { return fileDescriptorAwardcurrency, []int{0} }

func (m *GetUserCurrencyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCurrencyReq) GetType() AwardCurrencyType {
	if m != nil {
		return m.Type
	}
	return AwardCurrencyType_CurrencyNone
}

type GetUserCurrencyResp struct {
	Amount int32 `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (m *GetUserCurrencyResp) Reset()                    { *m = GetUserCurrencyResp{} }
func (m *GetUserCurrencyResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserCurrencyResp) ProtoMessage()               {}
func (*GetUserCurrencyResp) Descriptor() ([]byte, []int) { return fileDescriptorAwardcurrency, []int{1} }

func (m *GetUserCurrencyResp) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type UserCurrency struct {
	Uid        uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type       AwardCurrencyType `protobuf:"varint,2,opt,name=type,proto3,enum=awardcurrency.AwardCurrencyType" json:"type,omitempty"`
	Amount     int32             `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	OrderId    string            `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	OrderDesc  string            `protobuf:"bytes,5,opt,name=order_desc,json=orderDesc,proto3" json:"order_desc,omitempty"`
	OpUid      uint32            `protobuf:"varint,6,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	UpdateTime uint64            `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (m *UserCurrency) Reset()                    { *m = UserCurrency{} }
func (m *UserCurrency) String() string            { return proto.CompactTextString(m) }
func (*UserCurrency) ProtoMessage()               {}
func (*UserCurrency) Descriptor() ([]byte, []int) { return fileDescriptorAwardcurrency, []int{2} }

func (m *UserCurrency) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserCurrency) GetType() AwardCurrencyType {
	if m != nil {
		return m.Type
	}
	return AwardCurrencyType_CurrencyNone
}

func (m *UserCurrency) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *UserCurrency) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UserCurrency) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *UserCurrency) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *UserCurrency) GetUpdateTime() uint64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetUserConsumeLogResp struct {
	ConsumeList []*UserCurrency `protobuf:"bytes,1,rep,name=consume_list,json=consumeList" json:"consume_list,omitempty"`
}

func (m *GetUserConsumeLogResp) Reset()         { *m = GetUserConsumeLogResp{} }
func (m *GetUserConsumeLogResp) String() string { return proto.CompactTextString(m) }
func (*GetUserConsumeLogResp) ProtoMessage()    {}
func (*GetUserConsumeLogResp) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardcurrency, []int{3}
}

func (m *GetUserConsumeLogResp) GetConsumeList() []*UserCurrency {
	if m != nil {
		return m.ConsumeList
	}
	return nil
}

type AddUserCurrencyReq struct {
	Uid       uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type      AwardCurrencyType `protobuf:"varint,2,opt,name=type,proto3,enum=awardcurrency.AwardCurrencyType" json:"type,omitempty"`
	Amount    int32             `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	OrderId   string            `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	OrderDesc string            `protobuf:"bytes,5,opt,name=order_desc,json=orderDesc,proto3" json:"order_desc,omitempty"`
	OpUid     uint32            `protobuf:"varint,6,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
}

func (m *AddUserCurrencyReq) Reset()                    { *m = AddUserCurrencyReq{} }
func (m *AddUserCurrencyReq) String() string            { return proto.CompactTextString(m) }
func (*AddUserCurrencyReq) ProtoMessage()               {}
func (*AddUserCurrencyReq) Descriptor() ([]byte, []int) { return fileDescriptorAwardcurrency, []int{4} }

func (m *AddUserCurrencyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserCurrencyReq) GetType() AwardCurrencyType {
	if m != nil {
		return m.Type
	}
	return AwardCurrencyType_CurrencyNone
}

func (m *AddUserCurrencyReq) GetAmount() int32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *AddUserCurrencyReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddUserCurrencyReq) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *AddUserCurrencyReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type GetConsumeLogReq struct {
	Type AwardCurrencyType `protobuf:"varint,1,opt,name=type,proto3,enum=awardcurrency.AwardCurrencyType" json:"type,omitempty"`
}

func (m *GetConsumeLogReq) Reset()                    { *m = GetConsumeLogReq{} }
func (m *GetConsumeLogReq) String() string            { return proto.CompactTextString(m) }
func (*GetConsumeLogReq) ProtoMessage()               {}
func (*GetConsumeLogReq) Descriptor() ([]byte, []int) { return fileDescriptorAwardcurrency, []int{5} }

func (m *GetConsumeLogReq) GetType() AwardCurrencyType {
	if m != nil {
		return m.Type
	}
	return AwardCurrencyType_CurrencyNone
}

type FreezeUserCurrencyReq struct {
	Uid       uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type      AwardCurrencyType `protobuf:"varint,2,opt,name=type,proto3,enum=awardcurrency.AwardCurrencyType" json:"type,omitempty"`
	Amount    uint32            `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	OrderId   string            `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	OrderDesc string            `protobuf:"bytes,5,opt,name=order_desc,json=orderDesc,proto3" json:"order_desc,omitempty"`
	OpUid     uint32            `protobuf:"varint,6,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
}

func (m *FreezeUserCurrencyReq) Reset()         { *m = FreezeUserCurrencyReq{} }
func (m *FreezeUserCurrencyReq) String() string { return proto.CompactTextString(m) }
func (*FreezeUserCurrencyReq) ProtoMessage()    {}
func (*FreezeUserCurrencyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardcurrency, []int{6}
}

func (m *FreezeUserCurrencyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FreezeUserCurrencyReq) GetType() AwardCurrencyType {
	if m != nil {
		return m.Type
	}
	return AwardCurrencyType_CurrencyNone
}

func (m *FreezeUserCurrencyReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *FreezeUserCurrencyReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *FreezeUserCurrencyReq) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *FreezeUserCurrencyReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type ConfirmFrozenOrderReq struct {
	Uid     uint32                   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId string                   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Op      ConfirmFrozenOrderReq_OP `protobuf:"varint,3,opt,name=op,proto3,enum=awardcurrency.ConfirmFrozenOrderReq_OP" json:"op,omitempty"`
}

func (m *ConfirmFrozenOrderReq) Reset()         { *m = ConfirmFrozenOrderReq{} }
func (m *ConfirmFrozenOrderReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmFrozenOrderReq) ProtoMessage()    {}
func (*ConfirmFrozenOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptorAwardcurrency, []int{7}
}

func (m *ConfirmFrozenOrderReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConfirmFrozenOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ConfirmFrozenOrderReq) GetOp() ConfirmFrozenOrderReq_OP {
	if m != nil {
		return m.Op
	}
	return ConfirmFrozenOrderReq_NOP
}

func init() {
	proto.RegisterType((*GetUserCurrencyReq)(nil), "awardcurrency.GetUserCurrencyReq")
	proto.RegisterType((*GetUserCurrencyResp)(nil), "awardcurrency.GetUserCurrencyResp")
	proto.RegisterType((*UserCurrency)(nil), "awardcurrency.UserCurrency")
	proto.RegisterType((*GetUserConsumeLogResp)(nil), "awardcurrency.GetUserConsumeLogResp")
	proto.RegisterType((*AddUserCurrencyReq)(nil), "awardcurrency.AddUserCurrencyReq")
	proto.RegisterType((*GetConsumeLogReq)(nil), "awardcurrency.GetConsumeLogReq")
	proto.RegisterType((*FreezeUserCurrencyReq)(nil), "awardcurrency.FreezeUserCurrencyReq")
	proto.RegisterType((*ConfirmFrozenOrderReq)(nil), "awardcurrency.ConfirmFrozenOrderReq")
	proto.RegisterEnum("awardcurrency.AwardCurrencyType", AwardCurrencyType_name, AwardCurrencyType_value)
	proto.RegisterEnum("awardcurrency.ConfirmFrozenOrderReq_OP", ConfirmFrozenOrderReq_OP_name, ConfirmFrozenOrderReq_OP_value)
}
func (m *GetUserCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Uid))
	}
	if m.Type != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Type))
	}
	return i, nil
}

func (m *GetUserCurrencyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCurrencyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Amount != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Amount))
	}
	return i, nil
}

func (m *UserCurrency) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserCurrency) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Uid))
	}
	if m.Type != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Type))
	}
	if m.Amount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Amount))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if len(m.OrderDesc) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(len(m.OrderDesc)))
		i += copy(dAtA[i:], m.OrderDesc)
	}
	if m.OpUid != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.OpUid))
	}
	if m.UpdateTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.UpdateTime))
	}
	return i, nil
}

func (m *GetUserConsumeLogResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserConsumeLogResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ConsumeList) > 0 {
		for _, msg := range m.ConsumeList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintAwardcurrency(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddUserCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Uid))
	}
	if m.Type != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Type))
	}
	if m.Amount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Amount))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if len(m.OrderDesc) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(len(m.OrderDesc)))
		i += copy(dAtA[i:], m.OrderDesc)
	}
	if m.OpUid != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.OpUid))
	}
	return i, nil
}

func (m *GetConsumeLogReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConsumeLogReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Type != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Type))
	}
	return i, nil
}

func (m *FreezeUserCurrencyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FreezeUserCurrencyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Uid))
	}
	if m.Type != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Type))
	}
	if m.Amount != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Amount))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if len(m.OrderDesc) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(len(m.OrderDesc)))
		i += copy(dAtA[i:], m.OrderDesc)
	}
	if m.OpUid != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.OpUid))
	}
	return i, nil
}

func (m *ConfirmFrozenOrderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfirmFrozenOrderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Uid))
	}
	if len(m.OrderId) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(len(m.OrderId)))
		i += copy(dAtA[i:], m.OrderId)
	}
	if m.Op != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintAwardcurrency(dAtA, i, uint64(m.Op))
	}
	return i, nil
}

func encodeFixed64Awardcurrency(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Awardcurrency(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintAwardcurrency(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetUserCurrencyReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Uid))
	}
	if m.Type != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Type))
	}
	return n
}

func (m *GetUserCurrencyResp) Size() (n int) {
	var l int
	_ = l
	if m.Amount != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Amount))
	}
	return n
}

func (m *UserCurrency) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Uid))
	}
	if m.Type != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Type))
	}
	if m.Amount != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Amount))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovAwardcurrency(uint64(l))
	}
	l = len(m.OrderDesc)
	if l > 0 {
		n += 1 + l + sovAwardcurrency(uint64(l))
	}
	if m.OpUid != 0 {
		n += 1 + sovAwardcurrency(uint64(m.OpUid))
	}
	if m.UpdateTime != 0 {
		n += 1 + sovAwardcurrency(uint64(m.UpdateTime))
	}
	return n
}

func (m *GetUserConsumeLogResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ConsumeList) > 0 {
		for _, e := range m.ConsumeList {
			l = e.Size()
			n += 1 + l + sovAwardcurrency(uint64(l))
		}
	}
	return n
}

func (m *AddUserCurrencyReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Uid))
	}
	if m.Type != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Type))
	}
	if m.Amount != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Amount))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovAwardcurrency(uint64(l))
	}
	l = len(m.OrderDesc)
	if l > 0 {
		n += 1 + l + sovAwardcurrency(uint64(l))
	}
	if m.OpUid != 0 {
		n += 1 + sovAwardcurrency(uint64(m.OpUid))
	}
	return n
}

func (m *GetConsumeLogReq) Size() (n int) {
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Type))
	}
	return n
}

func (m *FreezeUserCurrencyReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Uid))
	}
	if m.Type != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Type))
	}
	if m.Amount != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Amount))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovAwardcurrency(uint64(l))
	}
	l = len(m.OrderDesc)
	if l > 0 {
		n += 1 + l + sovAwardcurrency(uint64(l))
	}
	if m.OpUid != 0 {
		n += 1 + sovAwardcurrency(uint64(m.OpUid))
	}
	return n
}

func (m *ConfirmFrozenOrderReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Uid))
	}
	l = len(m.OrderId)
	if l > 0 {
		n += 1 + l + sovAwardcurrency(uint64(l))
	}
	if m.Op != 0 {
		n += 1 + sovAwardcurrency(uint64(m.Op))
	}
	return n
}

func sovAwardcurrency(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozAwardcurrency(x uint64) (n int) {
	return sovAwardcurrency(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *GetUserCurrencyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardcurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (AwardCurrencyType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardcurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCurrencyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardcurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCurrencyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCurrencyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardcurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserCurrency) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardcurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserCurrency: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserCurrency: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (AwardCurrencyType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardcurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserConsumeLogResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardcurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserConsumeLogResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserConsumeLogResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ConsumeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ConsumeList = append(m.ConsumeList, &UserCurrency{})
			if err := m.ConsumeList[len(m.ConsumeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAwardcurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserCurrencyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardcurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (AwardCurrencyType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardcurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConsumeLogReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardcurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConsumeLogReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConsumeLogReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (AwardCurrencyType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardcurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FreezeUserCurrencyReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardcurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FreezeUserCurrencyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FreezeUserCurrencyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (AwardCurrencyType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardcurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfirmFrozenOrderReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAwardcurrency
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfirmFrozenOrderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfirmFrozenOrderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Op", wireType)
			}
			m.Op = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Op |= (ConfirmFrozenOrderReq_OP(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAwardcurrency(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthAwardcurrency
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipAwardcurrency(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAwardcurrency
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAwardcurrency
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthAwardcurrency
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowAwardcurrency
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipAwardcurrency(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthAwardcurrency = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAwardcurrency   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/gamelotto/awardcurrency/awardcurrency.proto", fileDescriptorAwardcurrency)
}

var fileDescriptorAwardcurrency = []byte{
	// 770 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x56, 0x5f, 0x6b, 0xc3, 0x54,
	0x14, 0xdf, 0x4d, 0xfa, 0x67, 0x3b, 0x6b, 0xb7, 0xec, 0x6a, 0x47, 0xad, 0xda, 0xc5, 0x38, 0x58,
	0x11, 0xda, 0x42, 0x1d, 0x08, 0xa5, 0x14, 0xda, 0xea, 0xe6, 0x58, 0xb7, 0x96, 0xb8, 0xb1, 0x17,
	0xa5, 0x64, 0xc9, 0xb5, 0x86, 0x36, 0xb9, 0x59, 0x72, 0x33, 0xe9, 0x40, 0x98, 0x6f, 0xe2, 0x93,
	0xf8, 0x19, 0x8a, 0xef, 0x3e, 0xf9, 0x09, 0x84, 0x3d, 0xfa, 0x11, 0x46, 0x7d, 0xe9, 0xc7, 0x90,
	0x24, 0xed, 0x68, 0x9a, 0xca, 0x26, 0xb8, 0xbd, 0xf5, 0xdc, 0x9c, 0x73, 0x7e, 0xbf, 0xfb, 0x3b,
	0xf7, 0xfc, 0x28, 0x1c, 0x3a, 0xc4, 0xbe, 0xd5, 0x55, 0xe2, 0x94, 0xfb, 0x8a, 0x41, 0x86, 0x94,
	0x31, 0x5a, 0x56, 0xbe, 0x57, 0x6c, 0x4d, 0x75, 0x6d, 0x9b, 0x98, 0xea, 0x28, 0x1c, 0x95, 0x2c,
	0x9b, 0x32, 0x8a, 0xd3, 0xa1, 0xc3, 0xdc, 0xbe, 0x4a, 0x0d, 0x83, 0x9a, 0x65, 0x36, 0xbc, 0xb5,
	0x74, 0x75, 0x30, 0x24, 0x65, 0x67, 0x70, 0xed, 0xea, 0x43, 0xa6, 0x9b, 0x6c, 0x64, 0x91, 0xa0,
	0x48, 0xfa, 0x1a, 0xf0, 0x31, 0x61, 0x97, 0x0e, 0xb1, 0x5b, 0xb3, 0x42, 0x99, 0xdc, 0x60, 0x01,
	0x78, 0x57, 0xd7, 0xb2, 0x48, 0x44, 0x85, 0xb4, 0xec, 0xfd, 0xc4, 0x87, 0x10, 0xf3, 0xaa, 0xb2,
	0x9c, 0x88, 0x0a, 0x5b, 0x15, 0xb1, 0x14, 0x26, 0xd0, 0xf0, 0xa2, 0x79, 0x83, 0x8b, 0x91, 0x45,
	0x64, 0x3f, 0x5b, 0x2a, 0xc2, 0x3b, 0x91, 0xee, 0x8e, 0x85, 0x77, 0x21, 0xa1, 0x18, 0xd4, 0x35,
	0x99, 0x8f, 0x10, 0x97, 0x67, 0x91, 0xf4, 0x88, 0x20, 0xb5, 0x98, 0xfc, 0x7f, 0xf1, 0x58, 0x00,
	0xe4, 0x17, 0x01, 0xf1, 0x7b, 0xb0, 0x4e, 0x6d, 0x8d, 0xd8, 0x3d, 0x5d, 0xcb, 0xc6, 0x44, 0x54,
	0xd8, 0x90, 0x93, 0x7e, 0x7c, 0xa2, 0xe1, 0x0f, 0x01, 0x82, 0x4f, 0x1a, 0x71, 0xd4, 0x6c, 0xdc,
	0xff, 0xb8, 0xe1, 0x9f, 0x7c, 0x4e, 0x1c, 0x15, 0x67, 0x20, 0x41, 0xad, 0x9e, 0x47, 0x2e, 0xe1,
	0x93, 0x8b, 0x53, 0xeb, 0x52, 0xd7, 0xf0, 0x1e, 0x6c, 0xba, 0x96, 0xa6, 0x30, 0xd2, 0x63, 0xba,
	0x41, 0xb2, 0x49, 0x11, 0x15, 0x62, 0x32, 0x04, 0x47, 0x17, 0xba, 0x41, 0xa4, 0x2b, 0xc8, 0xcc,
	0x15, 0xa1, 0xa6, 0xe3, 0x1a, 0xa4, 0x4d, 0xfb, 0xbe, 0x26, 0x75, 0x48, 0xa9, 0xc1, 0x49, 0x6f,
	0xa8, 0x3b, 0x9e, 0x32, 0x7c, 0x61, 0xb3, 0xf2, 0xfe, 0xd2, 0x05, 0x43, 0x52, 0x6e, 0xce, 0x0a,
	0xda, 0xba, 0xc3, 0xa4, 0x3f, 0x11, 0xe0, 0x86, 0xa6, 0xbd, 0xd2, 0x24, 0xdf, 0x4c, 0x41, 0xe9,
	0x4b, 0x10, 0x8e, 0x09, 0x5b, 0x14, 0xe7, 0xe6, 0x89, 0x32, 0xfa, 0x4f, 0x8f, 0xef, 0x01, 0x41,
	0xe6, 0xc8, 0x26, 0xe4, 0x8e, 0xbc, 0x8d, 0x28, 0xe9, 0xd7, 0x13, 0xe5, 0x37, 0x04, 0x99, 0x16,
	0x35, 0xbf, 0xd5, 0x6d, 0xe3, 0xc8, 0xa6, 0x77, 0xc4, 0xec, 0x78, 0x15, 0xab, 0xaf, 0xb2, 0x08,
	0xce, 0x85, 0xc1, 0x3f, 0x03, 0x8e, 0x5a, 0x3e, 0xd7, 0xad, 0xca, 0xc1, 0xd2, 0x1d, 0x57, 0xb6,
	0x2f, 0x75, 0xba, 0x32, 0x47, 0x2d, 0xe9, 0x00, 0xb8, 0x4e, 0x17, 0x27, 0x81, 0x3f, 0xef, 0x74,
	0x85, 0x35, 0x0c, 0x90, 0x68, 0x75, 0xce, 0xce, 0x4e, 0x2e, 0x04, 0x84, 0x53, 0xb0, 0x2e, 0x77,
	0xda, 0xed, 0x66, 0xa3, 0x75, 0x2a, 0x70, 0x9f, 0x34, 0x60, 0x27, 0x22, 0x16, 0x16, 0x20, 0x35,
	0x8f, 0xcf, 0xa9, 0x49, 0x84, 0x35, 0x9c, 0x83, 0xdd, 0x2b, 0xc5, 0xec, 0xdf, 0x7d, 0x47, 0x6c,
	0x6a, 0xf6, 0x47, 0x0a, 0x3d, 0xb2, 0x95, 0xbe, 0x41, 0x4c, 0x26, 0xa0, 0xca, 0x7d, 0x12, 0xd2,
	0xa1, 0x1e, 0xf8, 0x07, 0xd8, 0x5e, 0x72, 0x11, 0xfc, 0xd1, 0x12, 0xfb, 0xa8, 0x87, 0xe5, 0xa4,
	0xe7, 0x52, 0x1c, 0x4b, 0xfa, 0xf8, 0x7e, 0x3c, 0xe5, 0xd1, 0xcf, 0xe3, 0x29, 0x1f, 0x73, 0xab,
	0xac, 0xfa, 0xeb, 0x78, 0xca, 0x0b, 0x45, 0xb7, 0xe6, 0xea, 0x5a, 0x5d, 0x2c, 0xb2, 0x9a, 0x37,
	0xe4, 0x3a, 0xfe, 0x1d, 0xc1, 0xf6, 0xd2, 0x66, 0x45, 0xf0, 0xa3, 0x9b, 0x97, 0xfb, 0xa0, 0xf4,
	0xe4, 0xbc, 0xa5, 0xaf, 0x4e, 0x9b, 0x81, 0xf3, 0x7e, 0x61, 0x58, 0x6c, 0xd4, 0xeb, 0x36, 0xa5,
	0x6f, 0x3c, 0x64, 0xce, 0x43, 0x4e, 0x79, 0xc8, 0x4a, 0x75, 0x50, 0xd5, 0xaa, 0xd4, 0x67, 0xd0,
	0x5c, 0x66, 0x20, 0x16, 0x95, 0x5a, 0xf0, 0xb2, 0xea, 0x62, 0x71, 0x50, 0xf3, 0xa7, 0x29, 0xfa,
	0x09, 0xda, 0x2c, 0xf0, 0x1e, 0x53, 0x5d, 0x2c, 0xd2, 0x5a, 0xf0, 0x78, 0xea, 0xf8, 0x47, 0x04,
	0x3b, 0x11, 0x9f, 0x79, 0x89, 0x6a, 0xfb, 0xff, 0x92, 0x12, 0x32, 0xab, 0x40, 0x37, 0xfe, 0x19,
	0xdd, 0x6c, 0x48, 0x87, 0x36, 0x19, 0xef, 0x45, 0x7b, 0x87, 0xf6, 0xfc, 0x85, 0xe0, 0x39, 0x0f,
	0x3c, 0xe6, 0x81, 0x73, 0x01, 0xf4, 0x46, 0x91, 0x89, 0x33, 0xcc, 0x3f, 0x10, 0xe0, 0xe8, 0xce,
	0xe3, 0xe5, 0xc6, 0x2b, 0x6d, 0xe1, 0x25, 0x13, 0x8b, 0xbf, 0xe6, 0xc4, 0x70, 0x74, 0x07, 0x23,
	0xcc, 0x57, 0xae, 0xe9, 0x33, 0xcc, 0x0f, 0x3c, 0xe6, 0x89, 0xd9, 0xb4, 0x02, 0xc6, 0xef, 0x3e,
	0x31, 0xa6, 0xb5, 0xb9, 0x5b, 0xd4, 0x73, 0x89, 0x9f, 0xc6, 0x53, 0x7e, 0x32, 0x6a, 0x0a, 0x0f,
	0x93, 0x3c, 0xfa, 0x6b, 0x92, 0x47, 0x8f, 0x93, 0x3c, 0xfa, 0xe5, 0xef, 0xfc, 0xda, 0x75, 0xc2,
	0xff, 0xb7, 0xf0, 0xe9, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x21, 0x8d, 0x92, 0xc0, 0x9a, 0x08,
	0x00, 0x00,
}
