// Code generated by protoc-gen-gogo.
// source: services/gamelotto/lotto/gamelotto.proto
// DO NOT EDIT!

/*
	Package gamelotto is a generated protocol buffer package.

	It is generated from these files:
		services/gamelotto/lotto/gamelotto.proto

	It has these top-level messages:
		ReportGameLoginReq
		LottoChance
		GetUserLottoChancesReq
		GetUserLottoChancesResp
		UserDoLottryReq
		LottoResult
		UserDoLottryResp
		AddLottryReq
		GetLottryItemReq
		LottoItemExtend
		LottoItem
		LottoItemSet
		GetLottryItemResp
		GetContinousLoginDaysReq
		GetContinousLoginDaysResp
		BatchGetLottoChanceReq
		LottoGameChance
		LottoChanceSet
		BatchGetLottoChanceResp
		GetUserContinuousLoginDaysReq
		GetUserContinuousLoginDaysResp
		LoginLottoConfig
		AddLoginLotttoConfigReq
		GetLoginLotttoConfigReq
		GetLoginLotttoConfigResp
		GetLottoIdReq
		GetLottoIdResp
		AddVoucherReq
		UpdateVoucherReq
		LottoPreorderActInfo
		GetLottoPreorderActReq
		VoucherItem
		CreateLottoReq
		CreateLottoResp
		EnableLottoReq
		AddFirstLottoItemReq
		GetLottoInfoByNameReq
		BaseLottoInfo
		GetLottoInfoByNameResp
		GetHasFirstLoginLottoReq
		GetHasFirstLoginLottoResp
		SetLottoTypeChanceReq
		GetLottoTypeChanceReq
		GetLottoTypeChanceResp
		AddLottoChanceReq
		SetLottoTypeChanceV2Req
		GetLottoTypeChanceV2Req
		LottoTypeWithChance
		LottoTypeWithStep
		GetLottoTypeChanceV2Resp
		AwardLottoTypeChanceReq
		SetTypeChanceConfigReq
		AddUserLottoItemReq
		GetUserLottoItemByUidReq
		GetUserLottoItemByDeviceReq
		GetUserLottoItemResp
		GetLottoItemHistoryReq
		LottoHistoryItem
		GetLottoItemHistoryResp
		GetLottoActiveStatusReq
		GetLottoActiveStatusResp
		GetUserCpIdByLyGameIdReq
		GetUserCpIdByLyGameIdResp
		LottoChanceV2
		UserLottoChanceV2
		BatchGetUserRechargeLottoChancesReq
		BatchGetUserRechargeLottoChancesResp
		GetUserRechargeLottoChanceReq
		GetUserRechargeLottoChanceResp
		CleanExpiredRecordsReq
		RechargeLottoParam
		UpdateRechargeLottoParamReq
		GetRechargeLottoParamReq
		GetRechargeLottoParamResp
		BatchGetRechargeLottoParamReq
		BatchGetRechargeLottoParamResp
		AddLottoRechargeFeeReq
		GetPublicLottoRecordReq
		RewardRecord
		GetPublicLottoRecordResp
		GetWinningListReq
		GetWinningListResp
		DelPublicLottryReq
		LottryInfo
		GetPublicLottryResp
		SubmitPreLottoItemReq
*/
package gamelotto

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import math3 "math"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type LottoItemType int32

const (
	LottoItemType_voucher          LottoItemType = 1
	LottoItemType_giftpkg          LottoItemType = 2
	LottoItemType_reddiamond       LottoItemType = 3
	LottoItemType_rechargcard      LottoItemType = 4
	LottoItemType_medal            LottoItemType = 5
	LottoItemType_precharge        LottoItemType = 6
	LottoItemType_activecode       LottoItemType = 7
	LottoItemType_combine          LottoItemType = 8
	LottoItemType_virtualitem      LottoItemType = 9
	LottoItemType_realityitem      LottoItemType = 10
	LottoItemType_fragment_wangzhe LottoItemType = 11
	LottoItemType_empty_item       LottoItemType = 12
	LottoItemType_updateitem       LottoItemType = 101
)

var LottoItemType_name = map[int32]string{
	1:   "voucher",
	2:   "giftpkg",
	3:   "reddiamond",
	4:   "rechargcard",
	5:   "medal",
	6:   "precharge",
	7:   "activecode",
	8:   "combine",
	9:   "virtualitem",
	10:  "realityitem",
	11:  "fragment_wangzhe",
	12:  "empty_item",
	101: "updateitem",
}
var LottoItemType_value = map[string]int32{
	"voucher":          1,
	"giftpkg":          2,
	"reddiamond":       3,
	"rechargcard":      4,
	"medal":            5,
	"precharge":        6,
	"activecode":       7,
	"combine":          8,
	"virtualitem":      9,
	"realityitem":      10,
	"fragment_wangzhe": 11,
	"empty_item":       12,
	"updateitem":       101,
}

func (x LottoItemType) Enum() *LottoItemType {
	p := new(LottoItemType)
	*p = x
	return p
}
func (x LottoItemType) String() string {
	return proto.EnumName(LottoItemType_name, int32(x))
}
func (x *LottoItemType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(LottoItemType_value, data, "LottoItemType")
	if err != nil {
		return err
	}
	*x = LottoItemType(value)
	return nil
}
func (LottoItemType) EnumDescriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{0} }

type LottoChanceType int32

const (
	LottoChanceType_follow_cle LottoChanceType = 1
	LottoChanceType_share_act  LottoChanceType = 2
	LottoChanceType_visit_act  LottoChanceType = 3
)

var LottoChanceType_name = map[int32]string{
	1: "follow_cle",
	2: "share_act",
	3: "visit_act",
}
var LottoChanceType_value = map[string]int32{
	"follow_cle": 1,
	"share_act":  2,
	"visit_act":  3,
}

func (x LottoChanceType) Enum() *LottoChanceType {
	p := new(LottoChanceType)
	*p = x
	return p
}
func (x LottoChanceType) String() string {
	return proto.EnumName(LottoChanceType_name, int32(x))
}
func (x *LottoChanceType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(LottoChanceType_value, data, "LottoChanceType")
	if err != nil {
		return err
	}
	*x = LottoChanceType(value)
	return nil
}
func (LottoChanceType) EnumDescriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{1} }

// // config
type DailyLimitType int32

const (
	DailyLimitType_EVERYDAY_TYPE   DailyLimitType = 0
	DailyLimitType_KH_CHANNEL_TYPE DailyLimitType = 1
	DailyLimitType_SHARE_TYPE      DailyLimitType = 2
	DailyLimitType_INVITE_FRIEND   DailyLimitType = 3
	DailyLimitType_ONLINE_TIME     DailyLimitType = 4
)

var DailyLimitType_name = map[int32]string{
	0: "EVERYDAY_TYPE",
	1: "KH_CHANNEL_TYPE",
	2: "SHARE_TYPE",
	3: "INVITE_FRIEND",
	4: "ONLINE_TIME",
}
var DailyLimitType_value = map[string]int32{
	"EVERYDAY_TYPE":   0,
	"KH_CHANNEL_TYPE": 1,
	"SHARE_TYPE":      2,
	"INVITE_FRIEND":   3,
	"ONLINE_TIME":     4,
}

func (x DailyLimitType) Enum() *DailyLimitType {
	p := new(DailyLimitType)
	*p = x
	return p
}
func (x DailyLimitType) String() string {
	return proto.EnumName(DailyLimitType_name, int32(x))
}
func (x *DailyLimitType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(DailyLimitType_value, data, "DailyLimitType")
	if err != nil {
		return err
	}
	*x = DailyLimitType(value)
	return nil
}
func (DailyLimitType) EnumDescriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{2} }

type ReportGameLoginReq struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LianyunGameId uint32 `protobuf:"varint,2,req,name=lianyun_game_id,json=lianyunGameId" json:"lianyun_game_id"`
}

func (m *ReportGameLoginReq) Reset()                    { *m = ReportGameLoginReq{} }
func (m *ReportGameLoginReq) String() string            { return proto.CompactTextString(m) }
func (*ReportGameLoginReq) ProtoMessage()               {}
func (*ReportGameLoginReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{0} }

func (m *ReportGameLoginReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportGameLoginReq) GetLianyunGameId() uint32 {
	if m != nil {
		return m.LianyunGameId
	}
	return 0
}

type LottoChance struct {
	LottoId           uint32 `protobuf:"varint,1,req,name=lotto_id,json=lottoId" json:"lotto_id"`
	LottoName         string `protobuf:"bytes,2,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Chances           uint32 `protobuf:"varint,3,req,name=chances" json:"chances"`
	CpId              string `protobuf:"bytes,4,opt,name=cp_id,json=cpId" json:"cp_id"`
	ChanceUsed        uint32 `protobuf:"varint,5,opt,name=chance_used,json=chanceUsed" json:"chance_used"`
	FreeChances       uint32 `protobuf:"varint,6,opt,name=free_chances,json=freeChances" json:"free_chances"`
	ReddiamondChances uint32 `protobuf:"varint,7,opt,name=reddiamond_chances,json=reddiamondChances" json:"reddiamond_chances"`
	UpdateTime        uint32 `protobuf:"varint,8,opt,name=update_time,json=updateTime" json:"update_time"`
}

func (m *LottoChance) Reset()                    { *m = LottoChance{} }
func (m *LottoChance) String() string            { return proto.CompactTextString(m) }
func (*LottoChance) ProtoMessage()               {}
func (*LottoChance) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{1} }

func (m *LottoChance) GetLottoId() uint32 {
	if m != nil {
		return m.LottoId
	}
	return 0
}

func (m *LottoChance) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *LottoChance) GetChances() uint32 {
	if m != nil {
		return m.Chances
	}
	return 0
}

func (m *LottoChance) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *LottoChance) GetChanceUsed() uint32 {
	if m != nil {
		return m.ChanceUsed
	}
	return 0
}

func (m *LottoChance) GetFreeChances() uint32 {
	if m != nil {
		return m.FreeChances
	}
	return 0
}

func (m *LottoChance) GetReddiamondChances() uint32 {
	if m != nil {
		return m.ReddiamondChances
	}
	return 0
}

func (m *LottoChance) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetUserLottoChancesReq struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LianyunGameId uint32 `protobuf:"varint,2,req,name=lianyun_game_id,json=lianyunGameId" json:"lianyun_game_id"`
	LottoName     string `protobuf:"bytes,3,opt,name=lotto_name,json=lottoName" json:"lotto_name"`
	DeviceId      string `protobuf:"bytes,4,opt,name=device_id,json=deviceId" json:"device_id"`
}

func (m *GetUserLottoChancesReq) Reset()                    { *m = GetUserLottoChancesReq{} }
func (m *GetUserLottoChancesReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserLottoChancesReq) ProtoMessage()               {}
func (*GetUserLottoChancesReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{2} }

func (m *GetUserLottoChancesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLottoChancesReq) GetLianyunGameId() uint32 {
	if m != nil {
		return m.LianyunGameId
	}
	return 0
}

func (m *GetUserLottoChancesReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *GetUserLottoChancesReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetUserLottoChancesResp struct {
	ChanceList    []*LottoChance `protobuf:"bytes,1,rep,name=chance_list,json=chanceList" json:"chance_list,omitempty"`
	IsLottoActive bool           `protobuf:"varint,2,opt,name=is_lotto_active,json=isLottoActive" json:"is_lotto_active"`
}

func (m *GetUserLottoChancesResp) Reset()                    { *m = GetUserLottoChancesResp{} }
func (m *GetUserLottoChancesResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLottoChancesResp) ProtoMessage()               {}
func (*GetUserLottoChancesResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{3} }

func (m *GetUserLottoChancesResp) GetChanceList() []*LottoChance {
	if m != nil {
		return m.ChanceList
	}
	return nil
}

func (m *GetUserLottoChancesResp) GetIsLottoActive() bool {
	if m != nil {
		return m.IsLottoActive
	}
	return false
}

type UserDoLottryReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LottoId     uint32 `protobuf:"varint,2,opt,name=lotto_id,json=lottoId" json:"lotto_id"`
	LyGameId    uint32 `protobuf:"varint,3,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	LottoName   string `protobuf:"bytes,4,opt,name=lotto_name,json=lottoName" json:"lotto_name"`
	LottoCount  uint32 `protobuf:"varint,5,req,name=lotto_count,json=lottoCount" json:"lotto_count"`
	DeviceId    string `protobuf:"bytes,6,opt,name=device_id,json=deviceId" json:"device_id"`
	TtChannelId string `protobuf:"bytes,7,opt,name=tt_channel_id,json=ttChannelId" json:"tt_channel_id"`
	PayChance   bool   `protobuf:"varint,8,opt,name=pay_chance,json=payChance" json:"pay_chance"`
	IpAddr      uint32 `protobuf:"varint,9,opt,name=ip_addr,json=ipAddr" json:"ip_addr"`
	PayUid      uint32 `protobuf:"varint,10,opt,name=pay_uid,json=payUid" json:"pay_uid"`
	SdkDeviceId string `protobuf:"bytes,11,opt,name=sdk_device_id,json=sdkDeviceId" json:"sdk_device_id"`
}

func (m *UserDoLottryReq) Reset()                    { *m = UserDoLottryReq{} }
func (m *UserDoLottryReq) String() string            { return proto.CompactTextString(m) }
func (*UserDoLottryReq) ProtoMessage()               {}
func (*UserDoLottryReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{4} }

func (m *UserDoLottryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDoLottryReq) GetLottoId() uint32 {
	if m != nil {
		return m.LottoId
	}
	return 0
}

func (m *UserDoLottryReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *UserDoLottryReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *UserDoLottryReq) GetLottoCount() uint32 {
	if m != nil {
		return m.LottoCount
	}
	return 0
}

func (m *UserDoLottryReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserDoLottryReq) GetTtChannelId() string {
	if m != nil {
		return m.TtChannelId
	}
	return ""
}

func (m *UserDoLottryReq) GetPayChance() bool {
	if m != nil {
		return m.PayChance
	}
	return false
}

func (m *UserDoLottryReq) GetIpAddr() uint32 {
	if m != nil {
		return m.IpAddr
	}
	return 0
}

func (m *UserDoLottryReq) GetPayUid() uint32 {
	if m != nil {
		return m.PayUid
	}
	return 0
}

func (m *UserDoLottryReq) GetSdkDeviceId() string {
	if m != nil {
		return m.SdkDeviceId
	}
	return ""
}

type LottoResult struct {
	ItemId    uint32 `protobuf:"varint,1,req,name=item_id,json=itemId" json:"item_id"`
	ItemCount uint32 `protobuf:"varint,2,req,name=item_count,json=itemCount" json:"item_count"`
	ItemType  uint32 `protobuf:"varint,3,req,name=item_type,json=itemType" json:"item_type"`
	Detail    uint32 `protobuf:"varint,4,req,name=detail" json:"detail"`
	ItemName  string `protobuf:"bytes,5,opt,name=item_name,json=itemName" json:"item_name"`
	PicUrl    string `protobuf:"bytes,6,opt,name=pic_url,json=picUrl" json:"pic_url"`
	LottoTime uint32 `protobuf:"varint,7,opt,name=lotto_time,json=lottoTime" json:"lotto_time"`
}

func (m *LottoResult) Reset()                    { *m = LottoResult{} }
func (m *LottoResult) String() string            { return proto.CompactTextString(m) }
func (*LottoResult) ProtoMessage()               {}
func (*LottoResult) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{5} }

func (m *LottoResult) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LottoResult) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *LottoResult) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *LottoResult) GetDetail() uint32 {
	if m != nil {
		return m.Detail
	}
	return 0
}

func (m *LottoResult) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *LottoResult) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *LottoResult) GetLottoTime() uint32 {
	if m != nil {
		return m.LottoTime
	}
	return 0
}

type UserDoLottryResp struct {
	LottoResultList   []*LottoResult `protobuf:"bytes,1,rep,name=lotto_result_list,json=lottoResultList" json:"lotto_result_list,omitempty"`
	Chances           uint32         `protobuf:"varint,2,opt,name=chances" json:"chances"`
	NeedRedDiamond    uint32         `protobuf:"varint,3,opt,name=need_red_diamond,json=needRedDiamond" json:"need_red_diamond"`
	ReddiamondChances uint32         `protobuf:"varint,4,opt,name=reddiamond_chances,json=reddiamondChances" json:"reddiamond_chances"`
}

func (m *UserDoLottryResp) Reset()                    { *m = UserDoLottryResp{} }
func (m *UserDoLottryResp) String() string            { return proto.CompactTextString(m) }
func (*UserDoLottryResp) ProtoMessage()               {}
func (*UserDoLottryResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{6} }

func (m *UserDoLottryResp) GetLottoResultList() []*LottoResult {
	if m != nil {
		return m.LottoResultList
	}
	return nil
}

func (m *UserDoLottryResp) GetChances() uint32 {
	if m != nil {
		return m.Chances
	}
	return 0
}

func (m *UserDoLottryResp) GetNeedRedDiamond() uint32 {
	if m != nil {
		return m.NeedRedDiamond
	}
	return 0
}

func (m *UserDoLottryResp) GetReddiamondChances() uint32 {
	if m != nil {
		return m.ReddiamondChances
	}
	return 0
}

type AddLottryReq struct {
	LottoId   uint32  `protobuf:"varint,1,opt,name=lotto_id,json=lottoId" json:"lotto_id"`
	ItemId    uint32  `protobuf:"varint,2,req,name=item_id,json=itemId" json:"item_id"`
	ItemType  uint32  `protobuf:"varint,3,req,name=item_type,json=itemType" json:"item_type"`
	ItemCount int32   `protobuf:"varint,4,opt,name=item_count,json=itemCount" json:"item_count"`
	HitRate   float64 `protobuf:"fixed64,5,opt,name=hit_rate,json=hitRate" json:"hit_rate"`
	Amount    uint32  `protobuf:"varint,6,opt,name=amount" json:"amount"`
	LyGameId  uint32  `protobuf:"varint,7,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	LottoName string  `protobuf:"bytes,8,opt,name=lotto_name,json=lottoName" json:"lotto_name"`
	ItemName  string  `protobuf:"bytes,9,opt,name=item_name,json=itemName" json:"item_name"`
	PicUrl    string  `protobuf:"bytes,10,opt,name=pic_url,json=picUrl" json:"pic_url"`
}

func (m *AddLottryReq) Reset()                    { *m = AddLottryReq{} }
func (m *AddLottryReq) String() string            { return proto.CompactTextString(m) }
func (*AddLottryReq) ProtoMessage()               {}
func (*AddLottryReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{7} }

func (m *AddLottryReq) GetLottoId() uint32 {
	if m != nil {
		return m.LottoId
	}
	return 0
}

func (m *AddLottryReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *AddLottryReq) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *AddLottryReq) GetItemCount() int32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *AddLottryReq) GetHitRate() float64 {
	if m != nil {
		return m.HitRate
	}
	return 0
}

func (m *AddLottryReq) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *AddLottryReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *AddLottryReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *AddLottryReq) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *AddLottryReq) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

type GetLottryItemReq struct {
	LianyunGameId uint32 `protobuf:"varint,1,req,name=lianyun_game_id,json=lianyunGameId" json:"lianyun_game_id"`
	LottoName     string `protobuf:"bytes,2,opt,name=lotto_name,json=lottoName" json:"lotto_name"`
	CpId          string `protobuf:"bytes,3,opt,name=cp_id,json=cpId" json:"cp_id"`
}

func (m *GetLottryItemReq) Reset()                    { *m = GetLottryItemReq{} }
func (m *GetLottryItemReq) String() string            { return proto.CompactTextString(m) }
func (*GetLottryItemReq) ProtoMessage()               {}
func (*GetLottryItemReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{8} }

func (m *GetLottryItemReq) GetLianyunGameId() uint32 {
	if m != nil {
		return m.LianyunGameId
	}
	return 0
}

func (m *GetLottryItemReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *GetLottryItemReq) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

type LottoItemExtend struct {
	ItemName string `protobuf:"bytes,1,opt,name=item_name,json=itemName" json:"item_name"`
	PicUrl   string `protobuf:"bytes,2,opt,name=pic_url,json=picUrl" json:"pic_url"`
}

func (m *LottoItemExtend) Reset()                    { *m = LottoItemExtend{} }
func (m *LottoItemExtend) String() string            { return proto.CompactTextString(m) }
func (*LottoItemExtend) ProtoMessage()               {}
func (*LottoItemExtend) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{9} }

func (m *LottoItemExtend) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *LottoItemExtend) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

type LottoItem struct {
	ItemId     uint32  `protobuf:"varint,1,req,name=item_id,json=itemId" json:"item_id"`
	ItemCount  int32   `protobuf:"varint,2,req,name=item_count,json=itemCount" json:"item_count"`
	ItemType   int32   `protobuf:"varint,3,req,name=item_type,json=itemType" json:"item_type"`
	ItemDetail uint32  `protobuf:"varint,4,req,name=item_detail,json=itemDetail" json:"item_detail"`
	ItemName   string  `protobuf:"bytes,5,opt,name=item_name,json=itemName" json:"item_name"`
	PicUrl     string  `protobuf:"bytes,6,opt,name=pic_url,json=picUrl" json:"pic_url"`
	HitRate    float64 `protobuf:"fixed64,7,opt,name=hit_rate,json=hitRate" json:"hit_rate"`
	Valid      uint32  `protobuf:"varint,8,opt,name=valid" json:"valid"`
}

func (m *LottoItem) Reset()                    { *m = LottoItem{} }
func (m *LottoItem) String() string            { return proto.CompactTextString(m) }
func (*LottoItem) ProtoMessage()               {}
func (*LottoItem) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{10} }

func (m *LottoItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LottoItem) GetItemCount() int32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *LottoItem) GetItemType() int32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *LottoItem) GetItemDetail() uint32 {
	if m != nil {
		return m.ItemDetail
	}
	return 0
}

func (m *LottoItem) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *LottoItem) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *LottoItem) GetHitRate() float64 {
	if m != nil {
		return m.HitRate
	}
	return 0
}

func (m *LottoItem) GetValid() uint32 {
	if m != nil {
		return m.Valid
	}
	return 0
}

type LottoItemSet struct {
	LottoId       uint32       `protobuf:"varint,1,req,name=lotto_id,json=lottoId" json:"lotto_id"`
	LottoName     string       `protobuf:"bytes,2,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	LottoItemList []*LottoItem `protobuf:"bytes,3,rep,name=lotto_item_list,json=lottoItemList" json:"lotto_item_list,omitempty"`
}

func (m *LottoItemSet) Reset()                    { *m = LottoItemSet{} }
func (m *LottoItemSet) String() string            { return proto.CompactTextString(m) }
func (*LottoItemSet) ProtoMessage()               {}
func (*LottoItemSet) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{11} }

func (m *LottoItemSet) GetLottoId() uint32 {
	if m != nil {
		return m.LottoId
	}
	return 0
}

func (m *LottoItemSet) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *LottoItemSet) GetLottoItemList() []*LottoItem {
	if m != nil {
		return m.LottoItemList
	}
	return nil
}

type GetLottryItemResp struct {
	LottomItemSetList []*LottoItemSet `protobuf:"bytes,1,rep,name=lottom_item_set_list,json=lottomItemSetList" json:"lottom_item_set_list,omitempty"`
}

func (m *GetLottryItemResp) Reset()                    { *m = GetLottryItemResp{} }
func (m *GetLottryItemResp) String() string            { return proto.CompactTextString(m) }
func (*GetLottryItemResp) ProtoMessage()               {}
func (*GetLottryItemResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{12} }

func (m *GetLottryItemResp) GetLottomItemSetList() []*LottoItemSet {
	if m != nil {
		return m.LottomItemSetList
	}
	return nil
}

type GetContinousLoginDaysReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LyGameId uint32 `protobuf:"varint,2,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
}

func (m *GetContinousLoginDaysReq) Reset()         { *m = GetContinousLoginDaysReq{} }
func (m *GetContinousLoginDaysReq) String() string { return proto.CompactTextString(m) }
func (*GetContinousLoginDaysReq) ProtoMessage()    {}
func (*GetContinousLoginDaysReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{13}
}

func (m *GetContinousLoginDaysReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetContinousLoginDaysReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

type GetContinousLoginDaysResp struct {
	ContinousLoginDays uint32 `protobuf:"varint,1,req,name=continous_login_days,json=continousLoginDays" json:"continous_login_days"`
}

func (m *GetContinousLoginDaysResp) Reset()         { *m = GetContinousLoginDaysResp{} }
func (m *GetContinousLoginDaysResp) String() string { return proto.CompactTextString(m) }
func (*GetContinousLoginDaysResp) ProtoMessage()    {}
func (*GetContinousLoginDaysResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{14}
}

func (m *GetContinousLoginDaysResp) GetContinousLoginDays() uint32 {
	if m != nil {
		return m.ContinousLoginDays
	}
	return 0
}

type BatchGetLottoChanceReq struct {
	UidList    []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	LottoNames []string `protobuf:"bytes,2,rep,name=lotto_names,json=lottoNames" json:"lotto_names,omitempty"`
}

func (m *BatchGetLottoChanceReq) Reset()                    { *m = BatchGetLottoChanceReq{} }
func (m *BatchGetLottoChanceReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetLottoChanceReq) ProtoMessage()               {}
func (*BatchGetLottoChanceReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{15} }

func (m *BatchGetLottoChanceReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetLottoChanceReq) GetLottoNames() []string {
	if m != nil {
		return m.LottoNames
	}
	return nil
}

type LottoGameChance struct {
	LyGameId        uint32         `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	LottoChanceList []*LottoChance `protobuf:"bytes,2,rep,name=lotto_chance_list,json=lottoChanceList" json:"lotto_chance_list,omitempty"`
}

func (m *LottoGameChance) Reset()                    { *m = LottoGameChance{} }
func (m *LottoGameChance) String() string            { return proto.CompactTextString(m) }
func (*LottoGameChance) ProtoMessage()               {}
func (*LottoGameChance) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{16} }

func (m *LottoGameChance) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *LottoGameChance) GetLottoChanceList() []*LottoChance {
	if m != nil {
		return m.LottoChanceList
	}
	return nil
}

type LottoChanceSet struct {
	Uid                 uint32             `protobuf:"varint,1,req,name=uid" json:"uid"`
	LottoGameChanceList []*LottoGameChance `protobuf:"bytes,2,rep,name=lotto_game_chance_list,json=lottoGameChanceList" json:"lotto_game_chance_list,omitempty"`
}

func (m *LottoChanceSet) Reset()                    { *m = LottoChanceSet{} }
func (m *LottoChanceSet) String() string            { return proto.CompactTextString(m) }
func (*LottoChanceSet) ProtoMessage()               {}
func (*LottoChanceSet) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{17} }

func (m *LottoChanceSet) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LottoChanceSet) GetLottoGameChanceList() []*LottoGameChance {
	if m != nil {
		return m.LottoGameChanceList
	}
	return nil
}

type BatchGetLottoChanceResp struct {
	LottoChanceSetList []*LottoChanceSet `protobuf:"bytes,1,rep,name=lotto_chance_set_list,json=lottoChanceSetList" json:"lotto_chance_set_list,omitempty"`
}

func (m *BatchGetLottoChanceResp) Reset()         { *m = BatchGetLottoChanceResp{} }
func (m *BatchGetLottoChanceResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetLottoChanceResp) ProtoMessage()    {}
func (*BatchGetLottoChanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{18}
}

func (m *BatchGetLottoChanceResp) GetLottoChanceSetList() []*LottoChanceSet {
	if m != nil {
		return m.LottoChanceSetList
	}
	return nil
}

type GetUserContinuousLoginDaysReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LyGameId uint32 `protobuf:"varint,2,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
}

func (m *GetUserContinuousLoginDaysReq) Reset()         { *m = GetUserContinuousLoginDaysReq{} }
func (m *GetUserContinuousLoginDaysReq) String() string { return proto.CompactTextString(m) }
func (*GetUserContinuousLoginDaysReq) ProtoMessage()    {}
func (*GetUserContinuousLoginDaysReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{19}
}

func (m *GetUserContinuousLoginDaysReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserContinuousLoginDaysReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

type GetUserContinuousLoginDaysResp struct {
	Days uint32 `protobuf:"varint,1,req,name=days" json:"days"`
}

func (m *GetUserContinuousLoginDaysResp) Reset()         { *m = GetUserContinuousLoginDaysResp{} }
func (m *GetUserContinuousLoginDaysResp) String() string { return proto.CompactTextString(m) }
func (*GetUserContinuousLoginDaysResp) ProtoMessage()    {}
func (*GetUserContinuousLoginDaysResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{20}
}

func (m *GetUserContinuousLoginDaysResp) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

type LoginLottoConfig struct {
	MaxAccumulateChance uint32 `protobuf:"varint,1,opt,name=max_accumulate_chance,json=maxAccumulateChance" json:"max_accumulate_chance"`
	DefaultChance       uint32 `protobuf:"varint,2,opt,name=default_chance,json=defaultChance" json:"default_chance"`
	PayChance           uint32 `protobuf:"varint,3,opt,name=pay_chance,json=payChance" json:"pay_chance"`
	RedDiamondConsume   uint32 `protobuf:"varint,4,opt,name=red_diamond_consume,json=redDiamondConsume" json:"red_diamond_consume"`
}

func (m *LoginLottoConfig) Reset()                    { *m = LoginLottoConfig{} }
func (m *LoginLottoConfig) String() string            { return proto.CompactTextString(m) }
func (*LoginLottoConfig) ProtoMessage()               {}
func (*LoginLottoConfig) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{21} }

func (m *LoginLottoConfig) GetMaxAccumulateChance() uint32 {
	if m != nil {
		return m.MaxAccumulateChance
	}
	return 0
}

func (m *LoginLottoConfig) GetDefaultChance() uint32 {
	if m != nil {
		return m.DefaultChance
	}
	return 0
}

func (m *LoginLottoConfig) GetPayChance() uint32 {
	if m != nil {
		return m.PayChance
	}
	return 0
}

func (m *LoginLottoConfig) GetRedDiamondConsume() uint32 {
	if m != nil {
		return m.RedDiamondConsume
	}
	return 0
}

type AddLoginLotttoConfigReq struct {
	LyGameId uint32            `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	CpId     string            `protobuf:"bytes,2,req,name=cp_id,json=cpId" json:"cp_id"`
	Config   *LoginLottoConfig `protobuf:"bytes,3,req,name=config" json:"config,omitempty"`
}

func (m *AddLoginLotttoConfigReq) Reset()         { *m = AddLoginLotttoConfigReq{} }
func (m *AddLoginLotttoConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddLoginLotttoConfigReq) ProtoMessage()    {}
func (*AddLoginLotttoConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{22}
}

func (m *AddLoginLotttoConfigReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *AddLoginLotttoConfigReq) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *AddLoginLotttoConfigReq) GetConfig() *LoginLottoConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type GetLoginLotttoConfigReq struct {
	LyGameId uint32 `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	CpId     string `protobuf:"bytes,2,req,name=cp_id,json=cpId" json:"cp_id"`
}

func (m *GetLoginLotttoConfigReq) Reset()         { *m = GetLoginLotttoConfigReq{} }
func (m *GetLoginLotttoConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetLoginLotttoConfigReq) ProtoMessage()    {}
func (*GetLoginLotttoConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{23}
}

func (m *GetLoginLotttoConfigReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GetLoginLotttoConfigReq) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

type GetLoginLotttoConfigResp struct {
	Config *LoginLottoConfig `protobuf:"bytes,1,req,name=config" json:"config,omitempty"`
}

func (m *GetLoginLotttoConfigResp) Reset()         { *m = GetLoginLotttoConfigResp{} }
func (m *GetLoginLotttoConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetLoginLotttoConfigResp) ProtoMessage()    {}
func (*GetLoginLotttoConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{24}
}

func (m *GetLoginLotttoConfigResp) GetConfig() *LoginLottoConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type GetLottoIdReq struct {
	LyGameId  uint32 `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	ChannelId string `protobuf:"bytes,2,opt,name=channel_id,json=channelId" json:"channel_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
}

func (m *GetLottoIdReq) Reset()                    { *m = GetLottoIdReq{} }
func (m *GetLottoIdReq) String() string            { return proto.CompactTextString(m) }
func (*GetLottoIdReq) ProtoMessage()               {}
func (*GetLottoIdReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{25} }

func (m *GetLottoIdReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GetLottoIdReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *GetLottoIdReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

type GetLottoIdResp struct {
	LottoId uint32 `protobuf:"varint,1,opt,name=lotto_id,json=lottoId" json:"lotto_id"`
}

func (m *GetLottoIdResp) Reset()                    { *m = GetLottoIdResp{} }
func (m *GetLottoIdResp) String() string            { return proto.CompactTextString(m) }
func (*GetLottoIdResp) ProtoMessage()               {}
func (*GetLottoIdResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{26} }

func (m *GetLottoIdResp) GetLottoId() uint32 {
	if m != nil {
		return m.LottoId
	}
	return 0
}

type AddVoucherReq struct {
	VoucherId string `protobuf:"bytes,1,req,name=voucher_id,json=voucherId" json:"voucher_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	LyGameId  uint32 `protobuf:"varint,3,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	SendTime  uint32 `protobuf:"varint,4,req,name=send_time,json=sendTime" json:"send_time"`
}

func (m *AddVoucherReq) Reset()                    { *m = AddVoucherReq{} }
func (m *AddVoucherReq) String() string            { return proto.CompactTextString(m) }
func (*AddVoucherReq) ProtoMessage()               {}
func (*AddVoucherReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{27} }

func (m *AddVoucherReq) GetVoucherId() string {
	if m != nil {
		return m.VoucherId
	}
	return ""
}

func (m *AddVoucherReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddVoucherReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *AddVoucherReq) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type UpdateVoucherReq struct {
	VoucherId string `protobuf:"bytes,1,req,name=voucher_id,json=voucherId" json:"voucher_id"`
	UseTime   uint32 `protobuf:"varint,2,req,name=use_time,json=useTime" json:"use_time"`
	OrderId   string `protobuf:"bytes,3,req,name=order_id,json=orderId" json:"order_id"`
}

func (m *UpdateVoucherReq) Reset()                    { *m = UpdateVoucherReq{} }
func (m *UpdateVoucherReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateVoucherReq) ProtoMessage()               {}
func (*UpdateVoucherReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{28} }

func (m *UpdateVoucherReq) GetVoucherId() string {
	if m != nil {
		return m.VoucherId
	}
	return ""
}

func (m *UpdateVoucherReq) GetUseTime() uint32 {
	if m != nil {
		return m.UseTime
	}
	return 0
}

func (m *UpdateVoucherReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type LottoPreorderActInfo struct {
	GameId     uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	LottoName  string `protobuf:"bytes,2,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	BannerUrl  string `protobuf:"bytes,3,req,name=banner_url,json=bannerUrl" json:"banner_url"`
	Rules      string `protobuf:"bytes,4,req,name=rules" json:"rules"`
	Roulette   string `protobuf:"bytes,5,opt,name=roulette" json:"roulette"`
	BtnFollow  string `protobuf:"bytes,6,opt,name=btn_follow,json=btnFollow" json:"btn_follow"`
	BgColor    string `protobuf:"bytes,7,opt,name=bg_color,json=bgColor" json:"bg_color"`
	AwardIntro string `protobuf:"bytes,8,opt,name=award_intro,json=awardIntro" json:"award_intro"`
}

func (m *LottoPreorderActInfo) Reset()                    { *m = LottoPreorderActInfo{} }
func (m *LottoPreorderActInfo) String() string            { return proto.CompactTextString(m) }
func (*LottoPreorderActInfo) ProtoMessage()               {}
func (*LottoPreorderActInfo) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{29} }

func (m *LottoPreorderActInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *LottoPreorderActInfo) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *LottoPreorderActInfo) GetBannerUrl() string {
	if m != nil {
		return m.BannerUrl
	}
	return ""
}

func (m *LottoPreorderActInfo) GetRules() string {
	if m != nil {
		return m.Rules
	}
	return ""
}

func (m *LottoPreorderActInfo) GetRoulette() string {
	if m != nil {
		return m.Roulette
	}
	return ""
}

func (m *LottoPreorderActInfo) GetBtnFollow() string {
	if m != nil {
		return m.BtnFollow
	}
	return ""
}

func (m *LottoPreorderActInfo) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

func (m *LottoPreorderActInfo) GetAwardIntro() string {
	if m != nil {
		return m.AwardIntro
	}
	return ""
}

type GetLottoPreorderActReq struct {
	GameId    uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,2,req,name=lotto_name,json=lottoName" json:"lotto_name"`
}

func (m *GetLottoPreorderActReq) Reset()                    { *m = GetLottoPreorderActReq{} }
func (m *GetLottoPreorderActReq) String() string            { return proto.CompactTextString(m) }
func (*GetLottoPreorderActReq) ProtoMessage()               {}
func (*GetLottoPreorderActReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{30} }

func (m *GetLottoPreorderActReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetLottoPreorderActReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

type VoucherItem struct {
	Amount uint32 `protobuf:"varint,1,req,name=amount" json:"amount"`
	Count  uint32 `protobuf:"varint,2,req,name=count" json:"count"`
}

func (m *VoucherItem) Reset()                    { *m = VoucherItem{} }
func (m *VoucherItem) String() string            { return proto.CompactTextString(m) }
func (*VoucherItem) ProtoMessage()               {}
func (*VoucherItem) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{31} }

func (m *VoucherItem) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *VoucherItem) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type CreateLottoReq struct {
	GameId    uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,2,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	CpId      string `protobuf:"bytes,3,opt,name=cp_id,json=cpId" json:"cp_id"`
	Classic   uint32 `protobuf:"varint,4,opt,name=classic" json:"classic"`
	// 充值抽奖才需要
	RechargeFeeThreshold  uint32 `protobuf:"varint,5,opt,name=recharge_fee_threshold,json=rechargeFeeThreshold" json:"recharge_fee_threshold"`
	RebateRatioThousandth uint32 `protobuf:"varint,6,opt,name=rebate_ratio_thousandth,json=rebateRatioThousandth" json:"rebate_ratio_thousandth"`
	MinLotteryGiftValue   uint32 `protobuf:"varint,7,opt,name=min_lottery_gift_value,json=minLotteryGiftValue" json:"min_lottery_gift_value"`
}

func (m *CreateLottoReq) Reset()                    { *m = CreateLottoReq{} }
func (m *CreateLottoReq) String() string            { return proto.CompactTextString(m) }
func (*CreateLottoReq) ProtoMessage()               {}
func (*CreateLottoReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{32} }

func (m *CreateLottoReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CreateLottoReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *CreateLottoReq) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *CreateLottoReq) GetClassic() uint32 {
	if m != nil {
		return m.Classic
	}
	return 0
}

func (m *CreateLottoReq) GetRechargeFeeThreshold() uint32 {
	if m != nil {
		return m.RechargeFeeThreshold
	}
	return 0
}

func (m *CreateLottoReq) GetRebateRatioThousandth() uint32 {
	if m != nil {
		return m.RebateRatioThousandth
	}
	return 0
}

func (m *CreateLottoReq) GetMinLotteryGiftValue() uint32 {
	if m != nil {
		return m.MinLotteryGiftValue
	}
	return 0
}

type CreateLottoResp struct {
	LottoId uint32 `protobuf:"varint,1,req,name=lotto_id,json=lottoId" json:"lotto_id"`
}

func (m *CreateLottoResp) Reset()                    { *m = CreateLottoResp{} }
func (m *CreateLottoResp) String() string            { return proto.CompactTextString(m) }
func (*CreateLottoResp) ProtoMessage()               {}
func (*CreateLottoResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{33} }

func (m *CreateLottoResp) GetLottoId() uint32 {
	if m != nil {
		return m.LottoId
	}
	return 0
}

type EnableLottoReq struct {
	GameId    uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	Enable    bool   `protobuf:"varint,2,req,name=enable" json:"enable"`
	LottoName string `protobuf:"bytes,3,opt,name=lotto_name,json=lottoName" json:"lotto_name"`
	CpId      string `protobuf:"bytes,4,opt,name=cp_id,json=cpId" json:"cp_id"`
}

func (m *EnableLottoReq) Reset()                    { *m = EnableLottoReq{} }
func (m *EnableLottoReq) String() string            { return proto.CompactTextString(m) }
func (*EnableLottoReq) ProtoMessage()               {}
func (*EnableLottoReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{34} }

func (m *EnableLottoReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *EnableLottoReq) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *EnableLottoReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *EnableLottoReq) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

type AddFirstLottoItemReq struct {
	LyGameId    uint32         `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	CpId        string         `protobuf:"bytes,2,req,name=cp_id,json=cpId" json:"cp_id"`
	VoucherList []*VoucherItem `protobuf:"bytes,3,rep,name=voucher_list,json=voucherList" json:"voucher_list,omitempty"`
}

func (m *AddFirstLottoItemReq) Reset()                    { *m = AddFirstLottoItemReq{} }
func (m *AddFirstLottoItemReq) String() string            { return proto.CompactTextString(m) }
func (*AddFirstLottoItemReq) ProtoMessage()               {}
func (*AddFirstLottoItemReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{35} }

func (m *AddFirstLottoItemReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *AddFirstLottoItemReq) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *AddFirstLottoItemReq) GetVoucherList() []*VoucherItem {
	if m != nil {
		return m.VoucherList
	}
	return nil
}

type GetLottoInfoByNameReq struct {
	LottoName string `protobuf:"bytes,1,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Classic   uint32 `protobuf:"varint,2,req,name=classic" json:"classic"`
}

func (m *GetLottoInfoByNameReq) Reset()                    { *m = GetLottoInfoByNameReq{} }
func (m *GetLottoInfoByNameReq) String() string            { return proto.CompactTextString(m) }
func (*GetLottoInfoByNameReq) ProtoMessage()               {}
func (*GetLottoInfoByNameReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{36} }

func (m *GetLottoInfoByNameReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *GetLottoInfoByNameReq) GetClassic() uint32 {
	if m != nil {
		return m.Classic
	}
	return 0
}

type BaseLottoInfo struct {
	LyGameId   uint32 `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	CpId       string `protobuf:"bytes,2,req,name=cp_id,json=cpId" json:"cp_id"`
	CreateTime uint32 `protobuf:"varint,3,req,name=create_time,json=createTime" json:"create_time"`
	Active     bool   `protobuf:"varint,4,req,name=active" json:"active"`
	GameName   string `protobuf:"bytes,5,opt,name=game_name,json=gameName" json:"game_name"`
}

func (m *BaseLottoInfo) Reset()                    { *m = BaseLottoInfo{} }
func (m *BaseLottoInfo) String() string            { return proto.CompactTextString(m) }
func (*BaseLottoInfo) ProtoMessage()               {}
func (*BaseLottoInfo) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{37} }

func (m *BaseLottoInfo) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *BaseLottoInfo) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *BaseLottoInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *BaseLottoInfo) GetActive() bool {
	if m != nil {
		return m.Active
	}
	return false
}

func (m *BaseLottoInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GetLottoInfoByNameResp struct {
	LottoInfoList []*BaseLottoInfo `protobuf:"bytes,1,rep,name=lotto_info_list,json=lottoInfoList" json:"lotto_info_list,omitempty"`
}

func (m *GetLottoInfoByNameResp) Reset()                    { *m = GetLottoInfoByNameResp{} }
func (m *GetLottoInfoByNameResp) String() string            { return proto.CompactTextString(m) }
func (*GetLottoInfoByNameResp) ProtoMessage()               {}
func (*GetLottoInfoByNameResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{38} }

func (m *GetLottoInfoByNameResp) GetLottoInfoList() []*BaseLottoInfo {
	if m != nil {
		return m.LottoInfoList
	}
	return nil
}

type GetHasFirstLoginLottoReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LyGameId    uint32 `protobuf:"varint,2,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	IpAddr      uint32 `protobuf:"varint,3,req,name=ip_addr,json=ipAddr" json:"ip_addr"`
	DeviceId    string `protobuf:"bytes,4,req,name=device_id,json=deviceId" json:"device_id"`
	SdkDeviceId string `protobuf:"bytes,5,opt,name=sdk_device_id,json=sdkDeviceId" json:"sdk_device_id"`
}

func (m *GetHasFirstLoginLottoReq) Reset()         { *m = GetHasFirstLoginLottoReq{} }
func (m *GetHasFirstLoginLottoReq) String() string { return proto.CompactTextString(m) }
func (*GetHasFirstLoginLottoReq) ProtoMessage()    {}
func (*GetHasFirstLoginLottoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{39}
}

func (m *GetHasFirstLoginLottoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetHasFirstLoginLottoReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GetHasFirstLoginLottoReq) GetIpAddr() uint32 {
	if m != nil {
		return m.IpAddr
	}
	return 0
}

func (m *GetHasFirstLoginLottoReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetHasFirstLoginLottoReq) GetSdkDeviceId() string {
	if m != nil {
		return m.SdkDeviceId
	}
	return ""
}

type GetHasFirstLoginLottoResp struct {
	HasChance   bool           `protobuf:"varint,1,req,name=has_chance,json=hasChance" json:"has_chance"`
	VoucherList []*VoucherItem `protobuf:"bytes,2,rep,name=voucher_list,json=voucherList" json:"voucher_list,omitempty"`
}

func (m *GetHasFirstLoginLottoResp) Reset()         { *m = GetHasFirstLoginLottoResp{} }
func (m *GetHasFirstLoginLottoResp) String() string { return proto.CompactTextString(m) }
func (*GetHasFirstLoginLottoResp) ProtoMessage()    {}
func (*GetHasFirstLoginLottoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{40}
}

func (m *GetHasFirstLoginLottoResp) GetHasChance() bool {
	if m != nil {
		return m.HasChance
	}
	return false
}

func (m *GetHasFirstLoginLottoResp) GetVoucherList() []*VoucherItem {
	if m != nil {
		return m.VoucherList
	}
	return nil
}

type SetLottoTypeChanceReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Type      uint32 `protobuf:"varint,4,req,name=type" json:"type"`
	Chance    uint32 `protobuf:"varint,5,req,name=chance" json:"chance"`
}

func (m *SetLottoTypeChanceReq) Reset()                    { *m = SetLottoTypeChanceReq{} }
func (m *SetLottoTypeChanceReq) String() string            { return proto.CompactTextString(m) }
func (*SetLottoTypeChanceReq) ProtoMessage()               {}
func (*SetLottoTypeChanceReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{41} }

func (m *SetLottoTypeChanceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetLottoTypeChanceReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetLottoTypeChanceReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *SetLottoTypeChanceReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SetLottoTypeChanceReq) GetChance() uint32 {
	if m != nil {
		return m.Chance
	}
	return 0
}

type GetLottoTypeChanceReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Type      uint32 `protobuf:"varint,4,req,name=type" json:"type"`
}

func (m *GetLottoTypeChanceReq) Reset()                    { *m = GetLottoTypeChanceReq{} }
func (m *GetLottoTypeChanceReq) String() string            { return proto.CompactTextString(m) }
func (*GetLottoTypeChanceReq) ProtoMessage()               {}
func (*GetLottoTypeChanceReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{42} }

func (m *GetLottoTypeChanceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLottoTypeChanceReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetLottoTypeChanceReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *GetLottoTypeChanceReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetLottoTypeChanceResp struct {
	Chance     uint32 `protobuf:"varint,1,req,name=chance" json:"chance"`
	UpdateTime uint32 `protobuf:"varint,2,opt,name=update_time,json=updateTime" json:"update_time"`
}

func (m *GetLottoTypeChanceResp) Reset()                    { *m = GetLottoTypeChanceResp{} }
func (m *GetLottoTypeChanceResp) String() string            { return proto.CompactTextString(m) }
func (*GetLottoTypeChanceResp) ProtoMessage()               {}
func (*GetLottoTypeChanceResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{43} }

func (m *GetLottoTypeChanceResp) GetChance() uint32 {
	if m != nil {
		return m.Chance
	}
	return 0
}

func (m *GetLottoTypeChanceResp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type AddLottoChanceReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Chance    uint32 `protobuf:"varint,4,req,name=chance" json:"chance"`
	Force     bool   `protobuf:"varint,5,opt,name=force" json:"force"`
}

func (m *AddLottoChanceReq) Reset()                    { *m = AddLottoChanceReq{} }
func (m *AddLottoChanceReq) String() string            { return proto.CompactTextString(m) }
func (*AddLottoChanceReq) ProtoMessage()               {}
func (*AddLottoChanceReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{44} }

func (m *AddLottoChanceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddLottoChanceReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *AddLottoChanceReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *AddLottoChanceReq) GetChance() uint32 {
	if m != nil {
		return m.Chance
	}
	return 0
}

func (m *AddLottoChanceReq) GetForce() bool {
	if m != nil {
		return m.Force
	}
	return false
}

// 设置用户剩余抽奖次数
type SetLottoTypeChanceV2Req struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Type      uint32 `protobuf:"varint,4,req,name=type" json:"type"`
	Chance    uint32 `protobuf:"varint,5,req,name=chance" json:"chance"`
	IsReset   bool   `protobuf:"varint,6,opt,name=isReset" json:"isReset"`
}

func (m *SetLottoTypeChanceV2Req) Reset()         { *m = SetLottoTypeChanceV2Req{} }
func (m *SetLottoTypeChanceV2Req) String() string { return proto.CompactTextString(m) }
func (*SetLottoTypeChanceV2Req) ProtoMessage()    {}
func (*SetLottoTypeChanceV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{45}
}

func (m *SetLottoTypeChanceV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetLottoTypeChanceV2Req) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetLottoTypeChanceV2Req) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *SetLottoTypeChanceV2Req) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SetLottoTypeChanceV2Req) GetChance() uint32 {
	if m != nil {
		return m.Chance
	}
	return 0
}

func (m *SetLottoTypeChanceV2Req) GetIsReset() bool {
	if m != nil {
		return m.IsReset
	}
	return false
}

type GetLottoTypeChanceV2Req struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
}

func (m *GetLottoTypeChanceV2Req) Reset()         { *m = GetLottoTypeChanceV2Req{} }
func (m *GetLottoTypeChanceV2Req) String() string { return proto.CompactTextString(m) }
func (*GetLottoTypeChanceV2Req) ProtoMessage()    {}
func (*GetLottoTypeChanceV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{46}
}

func (m *GetLottoTypeChanceV2Req) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLottoTypeChanceV2Req) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetLottoTypeChanceV2Req) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

type LottoTypeWithChance struct {
	Type   uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Chance uint32 `protobuf:"varint,2,req,name=chance" json:"chance"`
}

func (m *LottoTypeWithChance) Reset()                    { *m = LottoTypeWithChance{} }
func (m *LottoTypeWithChance) String() string            { return proto.CompactTextString(m) }
func (*LottoTypeWithChance) ProtoMessage()               {}
func (*LottoTypeWithChance) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{47} }

func (m *LottoTypeWithChance) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *LottoTypeWithChance) GetChance() uint32 {
	if m != nil {
		return m.Chance
	}
	return 0
}

type LottoTypeWithStep struct {
	Type uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Step uint32 `protobuf:"varint,2,req,name=step" json:"step"`
}

func (m *LottoTypeWithStep) Reset()                    { *m = LottoTypeWithStep{} }
func (m *LottoTypeWithStep) String() string            { return proto.CompactTextString(m) }
func (*LottoTypeWithStep) ProtoMessage()               {}
func (*LottoTypeWithStep) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{48} }

func (m *LottoTypeWithStep) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *LottoTypeWithStep) GetStep() uint32 {
	if m != nil {
		return m.Step
	}
	return 0
}

type GetLottoTypeChanceV2Resp struct {
	TypeChanceList []*LottoTypeWithChance `protobuf:"bytes,1,rep,name=type_chance_list,json=typeChanceList" json:"type_chance_list,omitempty"`
	TypeStepList   []*LottoTypeWithStep   `protobuf:"bytes,2,rep,name=type_step_list,json=typeStepList" json:"type_step_list,omitempty"`
	UpdateTime     uint32                 `protobuf:"varint,3,opt,name=update_time,json=updateTime" json:"update_time"`
}

func (m *GetLottoTypeChanceV2Resp) Reset()         { *m = GetLottoTypeChanceV2Resp{} }
func (m *GetLottoTypeChanceV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetLottoTypeChanceV2Resp) ProtoMessage()    {}
func (*GetLottoTypeChanceV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{49}
}

func (m *GetLottoTypeChanceV2Resp) GetTypeChanceList() []*LottoTypeWithChance {
	if m != nil {
		return m.TypeChanceList
	}
	return nil
}

func (m *GetLottoTypeChanceV2Resp) GetTypeStepList() []*LottoTypeWithStep {
	if m != nil {
		return m.TypeStepList
	}
	return nil
}

func (m *GetLottoTypeChanceV2Resp) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 给特定类型加抽奖次数
type AwardLottoTypeChanceReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Type      uint32 `protobuf:"varint,4,req,name=type" json:"type"`
	Chance    uint32 `protobuf:"varint,5,req,name=chance" json:"chance"`
}

func (m *AwardLottoTypeChanceReq) Reset()         { *m = AwardLottoTypeChanceReq{} }
func (m *AwardLottoTypeChanceReq) String() string { return proto.CompactTextString(m) }
func (*AwardLottoTypeChanceReq) ProtoMessage()    {}
func (*AwardLottoTypeChanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{50}
}

func (m *AwardLottoTypeChanceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AwardLottoTypeChanceReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *AwardLottoTypeChanceReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *AwardLottoTypeChanceReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AwardLottoTypeChanceReq) GetChance() uint32 {
	if m != nil {
		return m.Chance
	}
	return 0
}

type SetTypeChanceConfigReq struct {
	GameId      uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	LottoName   string `protobuf:"bytes,2,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Type        uint32 `protobuf:"varint,3,req,name=type" json:"type"`
	LimitChance uint32 `protobuf:"varint,4,req,name=limitChance" json:"limitChance"`
}

func (m *SetTypeChanceConfigReq) Reset()                    { *m = SetTypeChanceConfigReq{} }
func (m *SetTypeChanceConfigReq) String() string            { return proto.CompactTextString(m) }
func (*SetTypeChanceConfigReq) ProtoMessage()               {}
func (*SetTypeChanceConfigReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{51} }

func (m *SetTypeChanceConfigReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetTypeChanceConfigReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *SetTypeChanceConfigReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SetTypeChanceConfigReq) GetLimitChance() uint32 {
	if m != nil {
		return m.LimitChance
	}
	return 0
}

type AddUserLottoItemReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	DeviceId  string `protobuf:"bytes,4,req,name=device_id,json=deviceId" json:"device_id"`
	ItemId    uint32 `protobuf:"varint,5,req,name=item_id,json=itemId" json:"item_id"`
	JustAdd   bool   `protobuf:"varint,6,opt,name=just_add,json=justAdd" json:"just_add"`
}

func (m *AddUserLottoItemReq) Reset()                    { *m = AddUserLottoItemReq{} }
func (m *AddUserLottoItemReq) String() string            { return proto.CompactTextString(m) }
func (*AddUserLottoItemReq) ProtoMessage()               {}
func (*AddUserLottoItemReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{52} }

func (m *AddUserLottoItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddUserLottoItemReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *AddUserLottoItemReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *AddUserLottoItemReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *AddUserLottoItemReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *AddUserLottoItemReq) GetJustAdd() bool {
	if m != nil {
		return m.JustAdd
	}
	return false
}

type GetUserLottoItemByUidReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Desc      uint32 `protobuf:"varint,4,opt,name=desc" json:"desc"`
}

func (m *GetUserLottoItemByUidReq) Reset()         { *m = GetUserLottoItemByUidReq{} }
func (m *GetUserLottoItemByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLottoItemByUidReq) ProtoMessage()    {}
func (*GetUserLottoItemByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{53}
}

func (m *GetUserLottoItemByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLottoItemByUidReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetUserLottoItemByUidReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *GetUserLottoItemByUidReq) GetDesc() uint32 {
	if m != nil {
		return m.Desc
	}
	return 0
}

type GetUserLottoItemByDeviceReq struct {
	DeviceId  string `protobuf:"bytes,1,req,name=device_id,json=deviceId" json:"device_id"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
}

func (m *GetUserLottoItemByDeviceReq) Reset()         { *m = GetUserLottoItemByDeviceReq{} }
func (m *GetUserLottoItemByDeviceReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLottoItemByDeviceReq) ProtoMessage()    {}
func (*GetUserLottoItemByDeviceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{54}
}

func (m *GetUserLottoItemByDeviceReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetUserLottoItemByDeviceReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetUserLottoItemByDeviceReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

type GetUserLottoItemResp struct {
	LottoResultList []*LottoResult `protobuf:"bytes,1,rep,name=lotto_result_list,json=lottoResultList" json:"lotto_result_list,omitempty"`
}

func (m *GetUserLottoItemResp) Reset()                    { *m = GetUserLottoItemResp{} }
func (m *GetUserLottoItemResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserLottoItemResp) ProtoMessage()               {}
func (*GetUserLottoItemResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{55} }

func (m *GetUserLottoItemResp) GetLottoResultList() []*LottoResult {
	if m != nil {
		return m.LottoResultList
	}
	return nil
}

type GetLottoItemHistoryReq struct {
	GameId    uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,2,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	Limit     uint32 `protobuf:"varint,3,req,name=limit" json:"limit"`
}

func (m *GetLottoItemHistoryReq) Reset()                    { *m = GetLottoItemHistoryReq{} }
func (m *GetLottoItemHistoryReq) String() string            { return proto.CompactTextString(m) }
func (*GetLottoItemHistoryReq) ProtoMessage()               {}
func (*GetLottoItemHistoryReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{56} }

func (m *GetLottoItemHistoryReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetLottoItemHistoryReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *GetLottoItemHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type LottoHistoryItem struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ItemId   uint32 `protobuf:"varint,2,req,name=item_id,json=itemId" json:"item_id"`
	ItemName string `protobuf:"bytes,3,req,name=item_name,json=itemName" json:"item_name"`
}

func (m *LottoHistoryItem) Reset()                    { *m = LottoHistoryItem{} }
func (m *LottoHistoryItem) String() string            { return proto.CompactTextString(m) }
func (*LottoHistoryItem) ProtoMessage()               {}
func (*LottoHistoryItem) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{57} }

func (m *LottoHistoryItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LottoHistoryItem) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LottoHistoryItem) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

type GetLottoItemHistoryResp struct {
	ItemList []*LottoHistoryItem `protobuf:"bytes,1,rep,name=item_list,json=itemList" json:"item_list,omitempty"`
}

func (m *GetLottoItemHistoryResp) Reset()         { *m = GetLottoItemHistoryResp{} }
func (m *GetLottoItemHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetLottoItemHistoryResp) ProtoMessage()    {}
func (*GetLottoItemHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{58}
}

func (m *GetLottoItemHistoryResp) GetItemList() []*LottoHistoryItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type GetLottoActiveStatusReq struct {
	LyGameId  uint32 `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	CpId      string `protobuf:"bytes,2,req,name=cp_id,json=cpId" json:"cp_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
}

func (m *GetLottoActiveStatusReq) Reset()         { *m = GetLottoActiveStatusReq{} }
func (m *GetLottoActiveStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetLottoActiveStatusReq) ProtoMessage()    {}
func (*GetLottoActiveStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{59}
}

func (m *GetLottoActiveStatusReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GetLottoActiveStatusReq) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *GetLottoActiveStatusReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

type GetLottoActiveStatusResp struct {
	ActiveStatus uint32 `protobuf:"varint,1,req,name=active_status,json=activeStatus" json:"active_status"`
	ActiveTs     uint32 `protobuf:"varint,2,opt,name=active_ts,json=activeTs" json:"active_ts"`
}

func (m *GetLottoActiveStatusResp) Reset()         { *m = GetLottoActiveStatusResp{} }
func (m *GetLottoActiveStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetLottoActiveStatusResp) ProtoMessage()    {}
func (*GetLottoActiveStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{60}
}

func (m *GetLottoActiveStatusResp) GetActiveStatus() uint32 {
	if m != nil {
		return m.ActiveStatus
	}
	return 0
}

func (m *GetLottoActiveStatusResp) GetActiveTs() uint32 {
	if m != nil {
		return m.ActiveTs
	}
	return 0
}

type GetUserCpIdByLyGameIdReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LyGameId uint32 `protobuf:"varint,2,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
}

func (m *GetUserCpIdByLyGameIdReq) Reset()         { *m = GetUserCpIdByLyGameIdReq{} }
func (m *GetUserCpIdByLyGameIdReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCpIdByLyGameIdReq) ProtoMessage()    {}
func (*GetUserCpIdByLyGameIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{61}
}

func (m *GetUserCpIdByLyGameIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCpIdByLyGameIdReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

type GetUserCpIdByLyGameIdResp struct {
	CpId string `protobuf:"bytes,1,req,name=cp_id,json=cpId" json:"cp_id"`
}

func (m *GetUserCpIdByLyGameIdResp) Reset()         { *m = GetUserCpIdByLyGameIdResp{} }
func (m *GetUserCpIdByLyGameIdResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCpIdByLyGameIdResp) ProtoMessage()    {}
func (*GetUserCpIdByLyGameIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{62}
}

func (m *GetUserCpIdByLyGameIdResp) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

type LottoChanceV2 struct {
	LyGameId             uint32   `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	PktChannel           string   `protobuf:"bytes,2,req,name=pkt_channel,json=pktChannel" json:"pkt_channel"`
	OverdueTsList        []uint32 `protobuf:"varint,3,rep,name=overdue_ts_list,json=overdueTsList" json:"overdue_ts_list,omitempty"`
	AccumulateFee        uint32   `protobuf:"varint,4,opt,name=accumulate_fee,json=accumulateFee" json:"accumulate_fee"`
	RechargeFeeThreshold uint32   `protobuf:"varint,5,opt,name=recharge_fee_threshold,json=rechargeFeeThreshold" json:"recharge_fee_threshold"`
	ActiveStatus         bool     `protobuf:"varint,6,req,name=active_status,json=activeStatus" json:"active_status"`
	MaxVoucherValue      uint32   `protobuf:"varint,7,opt,name=max_voucher_value,json=maxVoucherValue" json:"max_voucher_value"`
}

func (m *LottoChanceV2) Reset()                    { *m = LottoChanceV2{} }
func (m *LottoChanceV2) String() string            { return proto.CompactTextString(m) }
func (*LottoChanceV2) ProtoMessage()               {}
func (*LottoChanceV2) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{63} }

func (m *LottoChanceV2) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *LottoChanceV2) GetPktChannel() string {
	if m != nil {
		return m.PktChannel
	}
	return ""
}

func (m *LottoChanceV2) GetOverdueTsList() []uint32 {
	if m != nil {
		return m.OverdueTsList
	}
	return nil
}

func (m *LottoChanceV2) GetAccumulateFee() uint32 {
	if m != nil {
		return m.AccumulateFee
	}
	return 0
}

func (m *LottoChanceV2) GetRechargeFeeThreshold() uint32 {
	if m != nil {
		return m.RechargeFeeThreshold
	}
	return 0
}

func (m *LottoChanceV2) GetActiveStatus() bool {
	if m != nil {
		return m.ActiveStatus
	}
	return false
}

func (m *LottoChanceV2) GetMaxVoucherValue() uint32 {
	if m != nil {
		return m.MaxVoucherValue
	}
	return 0
}

type UserLottoChanceV2 struct {
	Uid             uint32           `protobuf:"varint,1,req,name=uid" json:"uid"`
	LottoChanceList []*LottoChanceV2 `protobuf:"bytes,2,rep,name=lotto_chance_list,json=lottoChanceList" json:"lotto_chance_list,omitempty"`
}

func (m *UserLottoChanceV2) Reset()                    { *m = UserLottoChanceV2{} }
func (m *UserLottoChanceV2) String() string            { return proto.CompactTextString(m) }
func (*UserLottoChanceV2) ProtoMessage()               {}
func (*UserLottoChanceV2) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{64} }

func (m *UserLottoChanceV2) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLottoChanceV2) GetLottoChanceList() []*LottoChanceV2 {
	if m != nil {
		return m.LottoChanceList
	}
	return nil
}

type BatchGetUserRechargeLottoChancesReq struct {
	UidList            []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	AllRechargeChances bool     `protobuf:"varint,2,opt,name=all_recharge_chances,json=allRechargeChances" json:"all_recharge_chances"`
}

func (m *BatchGetUserRechargeLottoChancesReq) Reset()         { *m = BatchGetUserRechargeLottoChancesReq{} }
func (m *BatchGetUserRechargeLottoChancesReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRechargeLottoChancesReq) ProtoMessage()    {}
func (*BatchGetUserRechargeLottoChancesReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{65}
}

func (m *BatchGetUserRechargeLottoChancesReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetUserRechargeLottoChancesReq) GetAllRechargeChances() bool {
	if m != nil {
		return m.AllRechargeChances
	}
	return false
}

type BatchGetUserRechargeLottoChancesResp struct {
	ChanceList []*UserLottoChanceV2 `protobuf:"bytes,1,rep,name=chance_list,json=chanceList" json:"chance_list,omitempty"`
}

func (m *BatchGetUserRechargeLottoChancesResp) Reset()         { *m = BatchGetUserRechargeLottoChancesResp{} }
func (m *BatchGetUserRechargeLottoChancesResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserRechargeLottoChancesResp) ProtoMessage()    {}
func (*BatchGetUserRechargeLottoChancesResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{66}
}

func (m *BatchGetUserRechargeLottoChancesResp) GetChanceList() []*UserLottoChanceV2 {
	if m != nil {
		return m.ChanceList
	}
	return nil
}

type GetUserRechargeLottoChanceReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LyGameId uint32 `protobuf:"varint,2,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
}

func (m *GetUserRechargeLottoChanceReq) Reset()         { *m = GetUserRechargeLottoChanceReq{} }
func (m *GetUserRechargeLottoChanceReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRechargeLottoChanceReq) ProtoMessage()    {}
func (*GetUserRechargeLottoChanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{67}
}

func (m *GetUserRechargeLottoChanceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRechargeLottoChanceReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

type GetUserRechargeLottoChanceResp struct {
	LottoChance *LottoChanceV2 `protobuf:"bytes,1,req,name=lotto_chance,json=lottoChance" json:"lotto_chance,omitempty"`
}

func (m *GetUserRechargeLottoChanceResp) Reset()         { *m = GetUserRechargeLottoChanceResp{} }
func (m *GetUserRechargeLottoChanceResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRechargeLottoChanceResp) ProtoMessage()    {}
func (*GetUserRechargeLottoChanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{68}
}

func (m *GetUserRechargeLottoChanceResp) GetLottoChance() *LottoChanceV2 {
	if m != nil {
		return m.LottoChance
	}
	return nil
}

// 清理mysql的旧数据
type CleanExpiredRecordsReq struct {
	BeforeTimestamp uint32 `protobuf:"varint,1,req,name=before_timestamp,json=beforeTimestamp" json:"before_timestamp"`
}

func (m *CleanExpiredRecordsReq) Reset()                    { *m = CleanExpiredRecordsReq{} }
func (m *CleanExpiredRecordsReq) String() string            { return proto.CompactTextString(m) }
func (*CleanExpiredRecordsReq) ProtoMessage()               {}
func (*CleanExpiredRecordsReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{69} }

func (m *CleanExpiredRecordsReq) GetBeforeTimestamp() uint32 {
	if m != nil {
		return m.BeforeTimestamp
	}
	return 0
}

type RechargeLottoParam struct {
	LyGameId              uint32 `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	PktChannel            string `protobuf:"bytes,2,req,name=pkt_channel,json=pktChannel" json:"pkt_channel"`
	RechargeFeeThreshold  uint32 `protobuf:"varint,3,opt,name=recharge_fee_threshold,json=rechargeFeeThreshold" json:"recharge_fee_threshold"`
	RebateRatioThousandth uint32 `protobuf:"varint,4,opt,name=rebate_ratio_thousandth,json=rebateRatioThousandth" json:"rebate_ratio_thousandth"`
	MinLotteryGiftValue   uint32 `protobuf:"varint,5,opt,name=min_lottery_gift_value,json=minLotteryGiftValue" json:"min_lottery_gift_value"`
}

func (m *RechargeLottoParam) Reset()                    { *m = RechargeLottoParam{} }
func (m *RechargeLottoParam) String() string            { return proto.CompactTextString(m) }
func (*RechargeLottoParam) ProtoMessage()               {}
func (*RechargeLottoParam) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{70} }

func (m *RechargeLottoParam) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *RechargeLottoParam) GetPktChannel() string {
	if m != nil {
		return m.PktChannel
	}
	return ""
}

func (m *RechargeLottoParam) GetRechargeFeeThreshold() uint32 {
	if m != nil {
		return m.RechargeFeeThreshold
	}
	return 0
}

func (m *RechargeLottoParam) GetRebateRatioThousandth() uint32 {
	if m != nil {
		return m.RebateRatioThousandth
	}
	return 0
}

func (m *RechargeLottoParam) GetMinLotteryGiftValue() uint32 {
	if m != nil {
		return m.MinLotteryGiftValue
	}
	return 0
}

type UpdateRechargeLottoParamReq struct {
	LyGameId              uint32 `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	PktChannel            string `protobuf:"bytes,2,req,name=pkt_channel,json=pktChannel" json:"pkt_channel"`
	RechargeFeeThreshold  uint32 `protobuf:"varint,3,opt,name=recharge_fee_threshold,json=rechargeFeeThreshold" json:"recharge_fee_threshold"`
	RebateRatioThousandth uint32 `protobuf:"varint,4,opt,name=rebate_ratio_thousandth,json=rebateRatioThousandth" json:"rebate_ratio_thousandth"`
	MinLotteryGiftValue   uint32 `protobuf:"varint,5,opt,name=min_lottery_gift_value,json=minLotteryGiftValue" json:"min_lottery_gift_value"`
}

func (m *UpdateRechargeLottoParamReq) Reset()         { *m = UpdateRechargeLottoParamReq{} }
func (m *UpdateRechargeLottoParamReq) String() string { return proto.CompactTextString(m) }
func (*UpdateRechargeLottoParamReq) ProtoMessage()    {}
func (*UpdateRechargeLottoParamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{71}
}

func (m *UpdateRechargeLottoParamReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *UpdateRechargeLottoParamReq) GetPktChannel() string {
	if m != nil {
		return m.PktChannel
	}
	return ""
}

func (m *UpdateRechargeLottoParamReq) GetRechargeFeeThreshold() uint32 {
	if m != nil {
		return m.RechargeFeeThreshold
	}
	return 0
}

func (m *UpdateRechargeLottoParamReq) GetRebateRatioThousandth() uint32 {
	if m != nil {
		return m.RebateRatioThousandth
	}
	return 0
}

func (m *UpdateRechargeLottoParamReq) GetMinLotteryGiftValue() uint32 {
	if m != nil {
		return m.MinLotteryGiftValue
	}
	return 0
}

type GetRechargeLottoParamReq struct {
	LyGameId   uint32 `protobuf:"varint,1,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	PktChannel string `protobuf:"bytes,2,opt,name=pkt_channel,json=pktChannel" json:"pkt_channel"`
}

func (m *GetRechargeLottoParamReq) Reset()         { *m = GetRechargeLottoParamReq{} }
func (m *GetRechargeLottoParamReq) String() string { return proto.CompactTextString(m) }
func (*GetRechargeLottoParamReq) ProtoMessage()    {}
func (*GetRechargeLottoParamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{72}
}

func (m *GetRechargeLottoParamReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *GetRechargeLottoParamReq) GetPktChannel() string {
	if m != nil {
		return m.PktChannel
	}
	return ""
}

type GetRechargeLottoParamResp struct {
	ParamList []*RechargeLottoParam `protobuf:"bytes,1,rep,name=param_list,json=paramList" json:"param_list,omitempty"`
}

func (m *GetRechargeLottoParamResp) Reset()         { *m = GetRechargeLottoParamResp{} }
func (m *GetRechargeLottoParamResp) String() string { return proto.CompactTextString(m) }
func (*GetRechargeLottoParamResp) ProtoMessage()    {}
func (*GetRechargeLottoParamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{73}
}

func (m *GetRechargeLottoParamResp) GetParamList() []*RechargeLottoParam {
	if m != nil {
		return m.ParamList
	}
	return nil
}

type BatchGetRechargeLottoParamReq struct {
	LyGameIdList []uint32 `protobuf:"varint,1,rep,name=ly_game_id_list,json=lyGameIdList" json:"ly_game_id_list,omitempty"`
}

func (m *BatchGetRechargeLottoParamReq) Reset()         { *m = BatchGetRechargeLottoParamReq{} }
func (m *BatchGetRechargeLottoParamReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetRechargeLottoParamReq) ProtoMessage()    {}
func (*BatchGetRechargeLottoParamReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{74}
}

func (m *BatchGetRechargeLottoParamReq) GetLyGameIdList() []uint32 {
	if m != nil {
		return m.LyGameIdList
	}
	return nil
}

type BatchGetRechargeLottoParamResp struct {
	ParamList []*RechargeLottoParam `protobuf:"bytes,1,rep,name=param_list,json=paramList" json:"param_list,omitempty"`
}

func (m *BatchGetRechargeLottoParamResp) Reset()         { *m = BatchGetRechargeLottoParamResp{} }
func (m *BatchGetRechargeLottoParamResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetRechargeLottoParamResp) ProtoMessage()    {}
func (*BatchGetRechargeLottoParamResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{75}
}

func (m *BatchGetRechargeLottoParamResp) GetParamList() []*RechargeLottoParam {
	if m != nil {
		return m.ParamList
	}
	return nil
}

type AddLottoRechargeFeeReq struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LyGameId      uint32 `protobuf:"varint,2,req,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	PktChannel    string `protobuf:"bytes,3,req,name=pkt_channel,json=pktChannel" json:"pkt_channel"`
	OrderId       string `protobuf:"bytes,4,req,name=order_id,json=orderId" json:"order_id"`
	RechargeValue uint32 `protobuf:"varint,5,req,name=recharge_value,json=rechargeValue" json:"recharge_value"`
}

func (m *AddLottoRechargeFeeReq) Reset()                    { *m = AddLottoRechargeFeeReq{} }
func (m *AddLottoRechargeFeeReq) String() string            { return proto.CompactTextString(m) }
func (*AddLottoRechargeFeeReq) ProtoMessage()               {}
func (*AddLottoRechargeFeeReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{76} }

func (m *AddLottoRechargeFeeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddLottoRechargeFeeReq) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *AddLottoRechargeFeeReq) GetPktChannel() string {
	if m != nil {
		return m.PktChannel
	}
	return ""
}

func (m *AddLottoRechargeFeeReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AddLottoRechargeFeeReq) GetRechargeValue() uint32 {
	if m != nil {
		return m.RechargeValue
	}
	return 0
}

type GetPublicLottoRecordReq struct {
	Limit uint32 `protobuf:"varint,1,req,name=limit" json:"limit"`
}

func (m *GetPublicLottoRecordReq) Reset()         { *m = GetPublicLottoRecordReq{} }
func (m *GetPublicLottoRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetPublicLottoRecordReq) ProtoMessage()    {}
func (*GetPublicLottoRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{77}
}

func (m *GetPublicLottoRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type RewardRecord struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Amount    uint32 `protobuf:"varint,2,req,name=amount" json:"amount"`
	Timestamp uint32 `protobuf:"varint,3,opt,name=timestamp" json:"timestamp"`
}

func (m *RewardRecord) Reset()                    { *m = RewardRecord{} }
func (m *RewardRecord) String() string            { return proto.CompactTextString(m) }
func (*RewardRecord) ProtoMessage()               {}
func (*RewardRecord) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{78} }

func (m *RewardRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RewardRecord) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *RewardRecord) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type GetPublicLottoRecordResp struct {
	RecordList []*RewardRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList" json:"record_list,omitempty"`
}

func (m *GetPublicLottoRecordResp) Reset()         { *m = GetPublicLottoRecordResp{} }
func (m *GetPublicLottoRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetPublicLottoRecordResp) ProtoMessage()    {}
func (*GetPublicLottoRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGamelotto, []int{79}
}

func (m *GetPublicLottoRecordResp) GetRecordList() []*RewardRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

// ***********获取中奖纪录***********
type GetWinningListReq struct {
	LottoId     int32  `protobuf:"varint,1,req,name=lotto_id,json=lottoId" json:"lotto_id"`
	Limit       uint32 `protobuf:"varint,2,req,name=limit" json:"limit"`
	FillByOther bool   `protobuf:"varint,3,req,name=fill_by_other,json=fillByOther" json:"fill_by_other"`
}

func (m *GetWinningListReq) Reset()                    { *m = GetWinningListReq{} }
func (m *GetWinningListReq) String() string            { return proto.CompactTextString(m) }
func (*GetWinningListReq) ProtoMessage()               {}
func (*GetWinningListReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{80} }

func (m *GetWinningListReq) GetLottoId() int32 {
	if m != nil {
		return m.LottoId
	}
	return 0
}

func (m *GetWinningListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetWinningListReq) GetFillByOther() bool {
	if m != nil {
		return m.FillByOther
	}
	return false
}

type GetWinningListResp struct {
	Winner   uint32 `protobuf:"varint,1,req,name=winner" json:"winner"`
	ItemName string `protobuf:"bytes,2,req,name=item_name,json=itemName" json:"item_name"`
}

func (m *GetWinningListResp) Reset()                    { *m = GetWinningListResp{} }
func (m *GetWinningListResp) String() string            { return proto.CompactTextString(m) }
func (*GetWinningListResp) ProtoMessage()               {}
func (*GetWinningListResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{81} }

func (m *GetWinningListResp) GetWinner() uint32 {
	if m != nil {
		return m.Winner
	}
	return 0
}

func (m *GetWinningListResp) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

// ***********公共奖池***********
type DelPublicLottryReq struct {
	LottoId int32 `protobuf:"varint,1,req,name=lotto_id,json=lottoId" json:"lotto_id"`
	ItemId  int32 `protobuf:"varint,2,req,name=item_id,json=itemId" json:"item_id"`
}

func (m *DelPublicLottryReq) Reset()                    { *m = DelPublicLottryReq{} }
func (m *DelPublicLottryReq) String() string            { return proto.CompactTextString(m) }
func (*DelPublicLottryReq) ProtoMessage()               {}
func (*DelPublicLottryReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{82} }

func (m *DelPublicLottryReq) GetLottoId() int32 {
	if m != nil {
		return m.LottoId
	}
	return 0
}

func (m *DelPublicLottryReq) GetItemId() int32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type LottryInfo struct {
	ItemId    uint32  `protobuf:"varint,1,req,name=item_id,json=itemId" json:"item_id"`
	ItemType  uint32  `protobuf:"varint,2,req,name=item_type,json=itemType" json:"item_type"`
	ItemCount int32   `protobuf:"varint,3,opt,name=item_count,json=itemCount" json:"item_count"`
	HitRate   float64 `protobuf:"fixed64,4,opt,name=hit_rate,json=hitRate" json:"hit_rate"`
	Amount    uint32  `protobuf:"varint,5,opt,name=amount" json:"amount"`
	LyGameId  uint32  `protobuf:"varint,6,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	LottoName string  `protobuf:"bytes,7,opt,name=lotto_name,json=lottoName" json:"lotto_name"`
	ItemName  string  `protobuf:"bytes,8,opt,name=item_name,json=itemName" json:"item_name"`
	PicUrl    string  `protobuf:"bytes,9,opt,name=pic_url,json=picUrl" json:"pic_url"`
	ChannelId string  `protobuf:"bytes,10,opt,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *LottryInfo) Reset()                    { *m = LottryInfo{} }
func (m *LottryInfo) String() string            { return proto.CompactTextString(m) }
func (*LottryInfo) ProtoMessage()               {}
func (*LottryInfo) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{83} }

func (m *LottryInfo) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *LottryInfo) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *LottryInfo) GetItemCount() int32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *LottryInfo) GetHitRate() float64 {
	if m != nil {
		return m.HitRate
	}
	return 0
}

func (m *LottryInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *LottryInfo) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *LottryInfo) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *LottryInfo) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *LottryInfo) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *LottryInfo) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

type GetPublicLottryResp struct {
	LottryInfoList []*LottryInfo `protobuf:"bytes,1,rep,name=lottry_info_list,json=lottryInfoList" json:"lottry_info_list,omitempty"`
}

func (m *GetPublicLottryResp) Reset()                    { *m = GetPublicLottryResp{} }
func (m *GetPublicLottryResp) String() string            { return proto.CompactTextString(m) }
func (*GetPublicLottryResp) ProtoMessage()               {}
func (*GetPublicLottryResp) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{84} }

func (m *GetPublicLottryResp) GetLottryInfoList() []*LottryInfo {
	if m != nil {
		return m.LottryInfoList
	}
	return nil
}

type SubmitPreLottoItemReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GameId    uint32 `protobuf:"varint,2,req,name=game_id,json=gameId" json:"game_id"`
	LottoName string `protobuf:"bytes,3,req,name=lotto_name,json=lottoName" json:"lotto_name"`
	ItemId    uint32 `protobuf:"varint,4,req,name=item_id,json=itemId" json:"item_id"`
	DeviceId  string `protobuf:"bytes,5,req,name=device_id,json=deviceId" json:"device_id"`
	Valid     uint32 `protobuf:"varint,6,req,name=valid" json:"valid"`
}

func (m *SubmitPreLottoItemReq) Reset()                    { *m = SubmitPreLottoItemReq{} }
func (m *SubmitPreLottoItemReq) String() string            { return proto.CompactTextString(m) }
func (*SubmitPreLottoItemReq) ProtoMessage()               {}
func (*SubmitPreLottoItemReq) Descriptor() ([]byte, []int) { return fileDescriptorGamelotto, []int{85} }

func (m *SubmitPreLottoItemReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubmitPreLottoItemReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SubmitPreLottoItemReq) GetLottoName() string {
	if m != nil {
		return m.LottoName
	}
	return ""
}

func (m *SubmitPreLottoItemReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *SubmitPreLottoItemReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *SubmitPreLottoItemReq) GetValid() uint32 {
	if m != nil {
		return m.Valid
	}
	return 0
}

func init() {
	proto.RegisterType((*ReportGameLoginReq)(nil), "gamelotto.ReportGameLoginReq")
	proto.RegisterType((*LottoChance)(nil), "gamelotto.LottoChance")
	proto.RegisterType((*GetUserLottoChancesReq)(nil), "gamelotto.GetUserLottoChancesReq")
	proto.RegisterType((*GetUserLottoChancesResp)(nil), "gamelotto.GetUserLottoChancesResp")
	proto.RegisterType((*UserDoLottryReq)(nil), "gamelotto.UserDoLottryReq")
	proto.RegisterType((*LottoResult)(nil), "gamelotto.LottoResult")
	proto.RegisterType((*UserDoLottryResp)(nil), "gamelotto.UserDoLottryResp")
	proto.RegisterType((*AddLottryReq)(nil), "gamelotto.AddLottryReq")
	proto.RegisterType((*GetLottryItemReq)(nil), "gamelotto.GetLottryItemReq")
	proto.RegisterType((*LottoItemExtend)(nil), "gamelotto.LottoItemExtend")
	proto.RegisterType((*LottoItem)(nil), "gamelotto.LottoItem")
	proto.RegisterType((*LottoItemSet)(nil), "gamelotto.LottoItemSet")
	proto.RegisterType((*GetLottryItemResp)(nil), "gamelotto.GetLottryItemResp")
	proto.RegisterType((*GetContinousLoginDaysReq)(nil), "gamelotto.GetContinousLoginDaysReq")
	proto.RegisterType((*GetContinousLoginDaysResp)(nil), "gamelotto.GetContinousLoginDaysResp")
	proto.RegisterType((*BatchGetLottoChanceReq)(nil), "gamelotto.BatchGetLottoChanceReq")
	proto.RegisterType((*LottoGameChance)(nil), "gamelotto.LottoGameChance")
	proto.RegisterType((*LottoChanceSet)(nil), "gamelotto.LottoChanceSet")
	proto.RegisterType((*BatchGetLottoChanceResp)(nil), "gamelotto.BatchGetLottoChanceResp")
	proto.RegisterType((*GetUserContinuousLoginDaysReq)(nil), "gamelotto.GetUserContinuousLoginDaysReq")
	proto.RegisterType((*GetUserContinuousLoginDaysResp)(nil), "gamelotto.GetUserContinuousLoginDaysResp")
	proto.RegisterType((*LoginLottoConfig)(nil), "gamelotto.LoginLottoConfig")
	proto.RegisterType((*AddLoginLotttoConfigReq)(nil), "gamelotto.AddLoginLotttoConfigReq")
	proto.RegisterType((*GetLoginLotttoConfigReq)(nil), "gamelotto.GetLoginLotttoConfigReq")
	proto.RegisterType((*GetLoginLotttoConfigResp)(nil), "gamelotto.GetLoginLotttoConfigResp")
	proto.RegisterType((*GetLottoIdReq)(nil), "gamelotto.GetLottoIdReq")
	proto.RegisterType((*GetLottoIdResp)(nil), "gamelotto.GetLottoIdResp")
	proto.RegisterType((*AddVoucherReq)(nil), "gamelotto.AddVoucherReq")
	proto.RegisterType((*UpdateVoucherReq)(nil), "gamelotto.UpdateVoucherReq")
	proto.RegisterType((*LottoPreorderActInfo)(nil), "gamelotto.LottoPreorderActInfo")
	proto.RegisterType((*GetLottoPreorderActReq)(nil), "gamelotto.GetLottoPreorderActReq")
	proto.RegisterType((*VoucherItem)(nil), "gamelotto.VoucherItem")
	proto.RegisterType((*CreateLottoReq)(nil), "gamelotto.CreateLottoReq")
	proto.RegisterType((*CreateLottoResp)(nil), "gamelotto.CreateLottoResp")
	proto.RegisterType((*EnableLottoReq)(nil), "gamelotto.EnableLottoReq")
	proto.RegisterType((*AddFirstLottoItemReq)(nil), "gamelotto.AddFirstLottoItemReq")
	proto.RegisterType((*GetLottoInfoByNameReq)(nil), "gamelotto.GetLottoInfoByNameReq")
	proto.RegisterType((*BaseLottoInfo)(nil), "gamelotto.BaseLottoInfo")
	proto.RegisterType((*GetLottoInfoByNameResp)(nil), "gamelotto.GetLottoInfoByNameResp")
	proto.RegisterType((*GetHasFirstLoginLottoReq)(nil), "gamelotto.GetHasFirstLoginLottoReq")
	proto.RegisterType((*GetHasFirstLoginLottoResp)(nil), "gamelotto.GetHasFirstLoginLottoResp")
	proto.RegisterType((*SetLottoTypeChanceReq)(nil), "gamelotto.SetLottoTypeChanceReq")
	proto.RegisterType((*GetLottoTypeChanceReq)(nil), "gamelotto.GetLottoTypeChanceReq")
	proto.RegisterType((*GetLottoTypeChanceResp)(nil), "gamelotto.GetLottoTypeChanceResp")
	proto.RegisterType((*AddLottoChanceReq)(nil), "gamelotto.AddLottoChanceReq")
	proto.RegisterType((*SetLottoTypeChanceV2Req)(nil), "gamelotto.SetLottoTypeChanceV2Req")
	proto.RegisterType((*GetLottoTypeChanceV2Req)(nil), "gamelotto.GetLottoTypeChanceV2Req")
	proto.RegisterType((*LottoTypeWithChance)(nil), "gamelotto.LottoTypeWithChance")
	proto.RegisterType((*LottoTypeWithStep)(nil), "gamelotto.LottoTypeWithStep")
	proto.RegisterType((*GetLottoTypeChanceV2Resp)(nil), "gamelotto.GetLottoTypeChanceV2Resp")
	proto.RegisterType((*AwardLottoTypeChanceReq)(nil), "gamelotto.AwardLottoTypeChanceReq")
	proto.RegisterType((*SetTypeChanceConfigReq)(nil), "gamelotto.SetTypeChanceConfigReq")
	proto.RegisterType((*AddUserLottoItemReq)(nil), "gamelotto.AddUserLottoItemReq")
	proto.RegisterType((*GetUserLottoItemByUidReq)(nil), "gamelotto.GetUserLottoItemByUidReq")
	proto.RegisterType((*GetUserLottoItemByDeviceReq)(nil), "gamelotto.GetUserLottoItemByDeviceReq")
	proto.RegisterType((*GetUserLottoItemResp)(nil), "gamelotto.GetUserLottoItemResp")
	proto.RegisterType((*GetLottoItemHistoryReq)(nil), "gamelotto.GetLottoItemHistoryReq")
	proto.RegisterType((*LottoHistoryItem)(nil), "gamelotto.LottoHistoryItem")
	proto.RegisterType((*GetLottoItemHistoryResp)(nil), "gamelotto.GetLottoItemHistoryResp")
	proto.RegisterType((*GetLottoActiveStatusReq)(nil), "gamelotto.GetLottoActiveStatusReq")
	proto.RegisterType((*GetLottoActiveStatusResp)(nil), "gamelotto.GetLottoActiveStatusResp")
	proto.RegisterType((*GetUserCpIdByLyGameIdReq)(nil), "gamelotto.GetUserCpIdByLyGameIdReq")
	proto.RegisterType((*GetUserCpIdByLyGameIdResp)(nil), "gamelotto.GetUserCpIdByLyGameIdResp")
	proto.RegisterType((*LottoChanceV2)(nil), "gamelotto.LottoChanceV2")
	proto.RegisterType((*UserLottoChanceV2)(nil), "gamelotto.UserLottoChanceV2")
	proto.RegisterType((*BatchGetUserRechargeLottoChancesReq)(nil), "gamelotto.BatchGetUserRechargeLottoChancesReq")
	proto.RegisterType((*BatchGetUserRechargeLottoChancesResp)(nil), "gamelotto.BatchGetUserRechargeLottoChancesResp")
	proto.RegisterType((*GetUserRechargeLottoChanceReq)(nil), "gamelotto.GetUserRechargeLottoChanceReq")
	proto.RegisterType((*GetUserRechargeLottoChanceResp)(nil), "gamelotto.GetUserRechargeLottoChanceResp")
	proto.RegisterType((*CleanExpiredRecordsReq)(nil), "gamelotto.CleanExpiredRecordsReq")
	proto.RegisterType((*RechargeLottoParam)(nil), "gamelotto.RechargeLottoParam")
	proto.RegisterType((*UpdateRechargeLottoParamReq)(nil), "gamelotto.UpdateRechargeLottoParamReq")
	proto.RegisterType((*GetRechargeLottoParamReq)(nil), "gamelotto.GetRechargeLottoParamReq")
	proto.RegisterType((*GetRechargeLottoParamResp)(nil), "gamelotto.GetRechargeLottoParamResp")
	proto.RegisterType((*BatchGetRechargeLottoParamReq)(nil), "gamelotto.BatchGetRechargeLottoParamReq")
	proto.RegisterType((*BatchGetRechargeLottoParamResp)(nil), "gamelotto.BatchGetRechargeLottoParamResp")
	proto.RegisterType((*AddLottoRechargeFeeReq)(nil), "gamelotto.AddLottoRechargeFeeReq")
	proto.RegisterType((*GetPublicLottoRecordReq)(nil), "gamelotto.GetPublicLottoRecordReq")
	proto.RegisterType((*RewardRecord)(nil), "gamelotto.RewardRecord")
	proto.RegisterType((*GetPublicLottoRecordResp)(nil), "gamelotto.GetPublicLottoRecordResp")
	proto.RegisterType((*GetWinningListReq)(nil), "gamelotto.GetWinningListReq")
	proto.RegisterType((*GetWinningListResp)(nil), "gamelotto.GetWinningListResp")
	proto.RegisterType((*DelPublicLottryReq)(nil), "gamelotto.DelPublicLottryReq")
	proto.RegisterType((*LottryInfo)(nil), "gamelotto.LottryInfo")
	proto.RegisterType((*GetPublicLottryResp)(nil), "gamelotto.GetPublicLottryResp")
	proto.RegisterType((*SubmitPreLottoItemReq)(nil), "gamelotto.SubmitPreLottoItemReq")
	proto.RegisterEnum("gamelotto.LottoItemType", LottoItemType_name, LottoItemType_value)
	proto.RegisterEnum("gamelotto.LottoChanceType", LottoChanceType_name, LottoChanceType_value)
	proto.RegisterEnum("gamelotto.DailyLimitType", DailyLimitType_name, DailyLimitType_value)
}
func (m *ReportGameLoginReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportGameLoginReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LianyunGameId))
	return i, nil
}

func (m *LottoChance) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoChance) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Chances))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ChanceUsed))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.FreeChances))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ReddiamondChances))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.UpdateTime))
	return i, nil
}

func (m *GetUserLottoChancesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLottoChancesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LianyunGameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	return i, nil
}

func (m *GetUserLottoChancesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLottoChancesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChanceList) > 0 {
		for _, msg := range m.ChanceList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	if m.IsLottoActive {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *UserDoLottryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserDoLottryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoCount))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.TtChannelId)))
	i += copy(dAtA[i:], m.TtChannelId)
	dAtA[i] = 0x40
	i++
	if m.PayChance {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.IpAddr))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.PayUid))
	dAtA[i] = 0x5a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.SdkDeviceId)))
	i += copy(dAtA[i:], m.SdkDeviceId)
	return i, nil
}

func (m *LottoResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Detail))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.ItemName)))
	i += copy(dAtA[i:], m.ItemName)
	dAtA[i] = 0x32
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x38
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoTime))
	return i, nil
}

func (m *UserDoLottryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserDoLottryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LottoResultList) > 0 {
		for _, msg := range m.LottoResultList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Chances))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.NeedRedDiamond))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ReddiamondChances))
	return i, nil
}

func (m *AddLottryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddLottryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemCount))
	dAtA[i] = 0x29
	i++
	i = encodeFixed64Gamelotto(dAtA, i, uint64(math3.Float64bits(float64(m.HitRate))))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x42
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.ItemName)))
	i += copy(dAtA[i:], m.ItemName)
	dAtA[i] = 0x52
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	return i, nil
}

func (m *GetLottryItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottryItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LianyunGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	return i, nil
}

func (m *LottoItemExtend) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoItemExtend) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.ItemName)))
	i += copy(dAtA[i:], m.ItemName)
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	return i, nil
}

func (m *LottoItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemDetail))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.ItemName)))
	i += copy(dAtA[i:], m.ItemName)
	dAtA[i] = 0x32
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x39
	i++
	i = encodeFixed64Gamelotto(dAtA, i, uint64(math3.Float64bits(float64(m.HitRate))))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Valid))
	return i, nil
}

func (m *LottoItemSet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoItemSet) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	if len(m.LottoItemList) > 0 {
		for _, msg := range m.LottoItemList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetLottryItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottryItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LottomItemSetList) > 0 {
		for _, msg := range m.LottomItemSetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetContinousLoginDaysReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetContinousLoginDaysReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	return i, nil
}

func (m *GetContinousLoginDaysResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetContinousLoginDaysResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ContinousLoginDays))
	return i, nil
}

func (m *BatchGetLottoChanceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetLottoChanceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(num))
		}
	}
	if len(m.LottoNames) > 0 {
		for _, s := range m.LottoNames {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *LottoGameChance) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoGameChance) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	if len(m.LottoChanceList) > 0 {
		for _, msg := range m.LottoChanceList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *LottoChanceSet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoChanceSet) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	if len(m.LottoGameChanceList) > 0 {
		for _, msg := range m.LottoGameChanceList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetLottoChanceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetLottoChanceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LottoChanceSetList) > 0 {
		for _, msg := range m.LottoChanceSetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserContinuousLoginDaysReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserContinuousLoginDaysReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	return i, nil
}

func (m *GetUserContinuousLoginDaysResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserContinuousLoginDaysResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Days))
	return i, nil
}

func (m *LoginLottoConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LoginLottoConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.MaxAccumulateChance))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.DefaultChance))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.PayChance))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.RedDiamondConsume))
	return i, nil
}

func (m *AddLoginLotttoConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddLoginLotttoConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	if m.Config == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("config")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGamelotto(dAtA, i, uint64(m.Config.Size()))
		n1, err := m.Config.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetLoginLotttoConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLoginLotttoConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	return i, nil
}

func (m *GetLoginLotttoConfigResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLoginLotttoConfigResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Config == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("config")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGamelotto(dAtA, i, uint64(m.Config.Size()))
		n2, err := m.Config.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *GetLottoIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.ChannelId)))
	i += copy(dAtA[i:], m.ChannelId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	return i, nil
}

func (m *GetLottoIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoId))
	return i, nil
}

func (m *AddVoucherReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddVoucherReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.VoucherId)))
	i += copy(dAtA[i:], m.VoucherId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.SendTime))
	return i, nil
}

func (m *UpdateVoucherReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateVoucherReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.VoucherId)))
	i += copy(dAtA[i:], m.VoucherId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.UseTime))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	return i, nil
}

func (m *LottoPreorderActInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoPreorderActInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.BannerUrl)))
	i += copy(dAtA[i:], m.BannerUrl)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.Rules)))
	i += copy(dAtA[i:], m.Rules)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.Roulette)))
	i += copy(dAtA[i:], m.Roulette)
	dAtA[i] = 0x32
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.BtnFollow)))
	i += copy(dAtA[i:], m.BtnFollow)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.BgColor)))
	i += copy(dAtA[i:], m.BgColor)
	dAtA[i] = 0x42
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.AwardIntro)))
	i += copy(dAtA[i:], m.AwardIntro)
	return i, nil
}

func (m *GetLottoPreorderActReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoPreorderActReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	return i, nil
}

func (m *VoucherItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VoucherItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *CreateLottoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateLottoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Classic))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.RechargeFeeThreshold))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.RebateRatioThousandth))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.MinLotteryGiftValue))
	return i, nil
}

func (m *CreateLottoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateLottoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoId))
	return i, nil
}

func (m *EnableLottoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EnableLottoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	if m.Enable {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	return i, nil
}

func (m *AddFirstLottoItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFirstLottoItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	if len(m.VoucherList) > 0 {
		for _, msg := range m.VoucherList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetLottoInfoByNameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoInfoByNameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Classic))
	return i, nil
}

func (m *BaseLottoInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BaseLottoInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.CreateTime))
	dAtA[i] = 0x20
	i++
	if m.Active {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	return i, nil
}

func (m *GetLottoInfoByNameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoInfoByNameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LottoInfoList) > 0 {
		for _, msg := range m.LottoInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetHasFirstLoginLottoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHasFirstLoginLottoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.IpAddr))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.SdkDeviceId)))
	i += copy(dAtA[i:], m.SdkDeviceId)
	return i, nil
}

func (m *GetHasFirstLoginLottoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHasFirstLoginLottoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.HasChance {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if len(m.VoucherList) > 0 {
		for _, msg := range m.VoucherList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetLottoTypeChanceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetLottoTypeChanceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Chance))
	return i, nil
}

func (m *GetLottoTypeChanceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoTypeChanceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *GetLottoTypeChanceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoTypeChanceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Chance))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.UpdateTime))
	return i, nil
}

func (m *AddLottoChanceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddLottoChanceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Chance))
	dAtA[i] = 0x28
	i++
	if m.Force {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetLottoTypeChanceV2Req) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetLottoTypeChanceV2Req) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Chance))
	dAtA[i] = 0x30
	i++
	if m.IsReset {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetLottoTypeChanceV2Req) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoTypeChanceV2Req) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	return i, nil
}

func (m *LottoTypeWithChance) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoTypeWithChance) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Chance))
	return i, nil
}

func (m *LottoTypeWithStep) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoTypeWithStep) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Step))
	return i, nil
}

func (m *GetLottoTypeChanceV2Resp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoTypeChanceV2Resp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TypeChanceList) > 0 {
		for _, msg := range m.TypeChanceList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.TypeStepList) > 0 {
		for _, msg := range m.TypeStepList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.UpdateTime))
	return i, nil
}

func (m *AwardLottoTypeChanceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardLottoTypeChanceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Chance))
	return i, nil
}

func (m *SetTypeChanceConfigReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetTypeChanceConfigReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LimitChance))
	return i, nil
}

func (m *AddUserLottoItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserLottoItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x30
	i++
	if m.JustAdd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserLottoItemByUidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLottoItemByUidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Desc))
	return i, nil
}

func (m *GetUserLottoItemByDeviceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLottoItemByDeviceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	return i, nil
}

func (m *GetUserLottoItemResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLottoItemResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LottoResultList) > 0 {
		for _, msg := range m.LottoResultList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetLottoItemHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoItemHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *LottoHistoryItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoHistoryItem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.ItemName)))
	i += copy(dAtA[i:], m.ItemName)
	return i, nil
}

func (m *GetLottoItemHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoItemHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ItemList) > 0 {
		for _, msg := range m.ItemList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetLottoActiveStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoActiveStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	return i, nil
}

func (m *GetLottoActiveStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLottoActiveStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ActiveStatus))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ActiveTs))
	return i, nil
}

func (m *GetUserCpIdByLyGameIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCpIdByLyGameIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	return i, nil
}

func (m *GetUserCpIdByLyGameIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCpIdByLyGameIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.CpId)))
	i += copy(dAtA[i:], m.CpId)
	return i, nil
}

func (m *LottoChanceV2) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottoChanceV2) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PktChannel)))
	i += copy(dAtA[i:], m.PktChannel)
	if len(m.OverdueTsList) > 0 {
		for _, num := range m.OverdueTsList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.AccumulateFee))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.RechargeFeeThreshold))
	dAtA[i] = 0x30
	i++
	if m.ActiveStatus {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.MaxVoucherValue))
	return i, nil
}

func (m *UserLottoChanceV2) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLottoChanceV2) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	if len(m.LottoChanceList) > 0 {
		for _, msg := range m.LottoChanceList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetUserRechargeLottoChancesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserRechargeLottoChancesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.AllRechargeChances {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BatchGetUserRechargeLottoChancesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetUserRechargeLottoChancesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChanceList) > 0 {
		for _, msg := range m.ChanceList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserRechargeLottoChanceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRechargeLottoChanceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	return i, nil
}

func (m *GetUserRechargeLottoChanceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRechargeLottoChanceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.LottoChance == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("lotto_chance")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoChance.Size()))
		n3, err := m.LottoChance.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *CleanExpiredRecordsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanExpiredRecordsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.BeforeTimestamp))
	return i, nil
}

func (m *RechargeLottoParam) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RechargeLottoParam) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PktChannel)))
	i += copy(dAtA[i:], m.PktChannel)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.RechargeFeeThreshold))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.RebateRatioThousandth))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.MinLotteryGiftValue))
	return i, nil
}

func (m *UpdateRechargeLottoParamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateRechargeLottoParamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PktChannel)))
	i += copy(dAtA[i:], m.PktChannel)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.RechargeFeeThreshold))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.RebateRatioThousandth))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.MinLotteryGiftValue))
	return i, nil
}

func (m *GetRechargeLottoParamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRechargeLottoParamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PktChannel)))
	i += copy(dAtA[i:], m.PktChannel)
	return i, nil
}

func (m *GetRechargeLottoParamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRechargeLottoParamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ParamList) > 0 {
		for _, msg := range m.ParamList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetRechargeLottoParamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetRechargeLottoParamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LyGameIdList) > 0 {
		for _, num := range m.LyGameIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetRechargeLottoParamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetRechargeLottoParamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ParamList) > 0 {
		for _, msg := range m.ParamList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddLottoRechargeFeeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddLottoRechargeFeeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PktChannel)))
	i += copy(dAtA[i:], m.PktChannel)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.RechargeValue))
	return i, nil
}

func (m *GetPublicLottoRecordReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPublicLottoRecordReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *RewardRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RewardRecord) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Timestamp))
	return i, nil
}

func (m *GetPublicLottoRecordResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPublicLottoRecordResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecordList) > 0 {
		for _, msg := range m.RecordList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetWinningListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetWinningListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x18
	i++
	if m.FillByOther {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetWinningListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetWinningListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Winner))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.ItemName)))
	i += copy(dAtA[i:], m.ItemName)
	return i, nil
}

func (m *DelPublicLottryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelPublicLottryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LottoId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemId))
	return i, nil
}

func (m *LottryInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LottryInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemCount))
	dAtA[i] = 0x21
	i++
	i = encodeFixed64Gamelotto(dAtA, i, uint64(math3.Float64bits(float64(m.HitRate))))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x42
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.ItemName)))
	i += copy(dAtA[i:], m.ItemName)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x52
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.ChannelId)))
	i += copy(dAtA[i:], m.ChannelId)
	return i, nil
}

func (m *GetPublicLottryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPublicLottryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LottryInfoList) > 0 {
		for _, msg := range m.LottryInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGamelotto(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SubmitPreLottoItemReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SubmitPreLottoItemReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.LottoName)))
	i += copy(dAtA[i:], m.LottoName)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.ItemId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(len(m.DeviceId)))
	i += copy(dAtA[i:], m.DeviceId)
	dAtA[i] = 0x30
	i++
	i = encodeVarintGamelotto(dAtA, i, uint64(m.Valid))
	return i, nil
}

func encodeFixed64Gamelotto(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Gamelotto(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGamelotto(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ReportGameLoginReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LianyunGameId))
	return n
}

func (m *LottoChance) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LottoId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Chances))
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.ChanceUsed))
	n += 1 + sovGamelotto(uint64(m.FreeChances))
	n += 1 + sovGamelotto(uint64(m.ReddiamondChances))
	n += 1 + sovGamelotto(uint64(m.UpdateTime))
	return n
}

func (m *GetUserLottoChancesReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LianyunGameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.DeviceId)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetUserLottoChancesResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChanceList) > 0 {
		for _, e := range m.ChanceList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *UserDoLottryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LottoId))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.LottoCount))
	l = len(m.DeviceId)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.TtChannelId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 2
	n += 1 + sovGamelotto(uint64(m.IpAddr))
	n += 1 + sovGamelotto(uint64(m.PayUid))
	l = len(m.SdkDeviceId)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *LottoResult) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.ItemId))
	n += 1 + sovGamelotto(uint64(m.ItemCount))
	n += 1 + sovGamelotto(uint64(m.ItemType))
	n += 1 + sovGamelotto(uint64(m.Detail))
	l = len(m.ItemName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.PicUrl)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.LottoTime))
	return n
}

func (m *UserDoLottryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LottoResultList) > 0 {
		for _, e := range m.LottoResultList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	n += 1 + sovGamelotto(uint64(m.Chances))
	n += 1 + sovGamelotto(uint64(m.NeedRedDiamond))
	n += 1 + sovGamelotto(uint64(m.ReddiamondChances))
	return n
}

func (m *AddLottryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LottoId))
	n += 1 + sovGamelotto(uint64(m.ItemId))
	n += 1 + sovGamelotto(uint64(m.ItemType))
	n += 1 + sovGamelotto(uint64(m.ItemCount))
	n += 9
	n += 1 + sovGamelotto(uint64(m.Amount))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.ItemName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.PicUrl)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetLottryItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LianyunGameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *LottoItemExtend) Size() (n int) {
	var l int
	_ = l
	l = len(m.ItemName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.PicUrl)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *LottoItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.ItemId))
	n += 1 + sovGamelotto(uint64(m.ItemCount))
	n += 1 + sovGamelotto(uint64(m.ItemType))
	n += 1 + sovGamelotto(uint64(m.ItemDetail))
	l = len(m.ItemName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.PicUrl)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 9
	n += 1 + sovGamelotto(uint64(m.Valid))
	return n
}

func (m *LottoItemSet) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LottoId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	if len(m.LottoItemList) > 0 {
		for _, e := range m.LottoItemList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *GetLottryItemResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LottomItemSetList) > 0 {
		for _, e := range m.LottomItemSetList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *GetContinousLoginDaysReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	return n
}

func (m *GetContinousLoginDaysResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.ContinousLoginDays))
	return n
}

func (m *BatchGetLottoChanceReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovGamelotto(uint64(e))
		}
	}
	if len(m.LottoNames) > 0 {
		for _, s := range m.LottoNames {
			l = len(s)
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *LottoGameChance) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	if len(m.LottoChanceList) > 0 {
		for _, e := range m.LottoChanceList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *LottoChanceSet) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	if len(m.LottoGameChanceList) > 0 {
		for _, e := range m.LottoGameChanceList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *BatchGetLottoChanceResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LottoChanceSetList) > 0 {
		for _, e := range m.LottoChanceSetList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *GetUserContinuousLoginDaysReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	return n
}

func (m *GetUserContinuousLoginDaysResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Days))
	return n
}

func (m *LoginLottoConfig) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.MaxAccumulateChance))
	n += 1 + sovGamelotto(uint64(m.DefaultChance))
	n += 1 + sovGamelotto(uint64(m.PayChance))
	n += 1 + sovGamelotto(uint64(m.RedDiamondConsume))
	return n
}

func (m *AddLoginLotttoConfigReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	if m.Config != nil {
		l = m.Config.Size()
		n += 1 + l + sovGamelotto(uint64(l))
	}
	return n
}

func (m *GetLoginLotttoConfigReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetLoginLotttoConfigResp) Size() (n int) {
	var l int
	_ = l
	if m.Config != nil {
		l = m.Config.Size()
		n += 1 + l + sovGamelotto(uint64(l))
	}
	return n
}

func (m *GetLottoIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.ChannelId)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetLottoIdResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LottoId))
	return n
}

func (m *AddVoucherReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.VoucherId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	n += 1 + sovGamelotto(uint64(m.SendTime))
	return n
}

func (m *UpdateVoucherReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.VoucherId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.UseTime))
	l = len(m.OrderId)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *LottoPreorderActInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.BannerUrl)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.Rules)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.Roulette)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.BtnFollow)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.BgColor)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.AwardIntro)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetLottoPreorderActReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *VoucherItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Amount))
	n += 1 + sovGamelotto(uint64(m.Count))
	return n
}

func (m *CreateLottoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Classic))
	n += 1 + sovGamelotto(uint64(m.RechargeFeeThreshold))
	n += 1 + sovGamelotto(uint64(m.RebateRatioThousandth))
	n += 1 + sovGamelotto(uint64(m.MinLotteryGiftValue))
	return n
}

func (m *CreateLottoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LottoId))
	return n
}

func (m *EnableLottoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.GameId))
	n += 2
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *AddFirstLottoItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	if len(m.VoucherList) > 0 {
		for _, e := range m.VoucherList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *GetLottoInfoByNameReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Classic))
	return n
}

func (m *BaseLottoInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.CreateTime))
	n += 2
	l = len(m.GameName)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetLottoInfoByNameResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LottoInfoList) > 0 {
		for _, e := range m.LottoInfoList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *GetHasFirstLoginLottoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	n += 1 + sovGamelotto(uint64(m.IpAddr))
	l = len(m.DeviceId)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.SdkDeviceId)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetHasFirstLoginLottoResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	if len(m.VoucherList) > 0 {
		for _, e := range m.VoucherList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *SetLottoTypeChanceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Type))
	n += 1 + sovGamelotto(uint64(m.Chance))
	return n
}

func (m *GetLottoTypeChanceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Type))
	return n
}

func (m *GetLottoTypeChanceResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Chance))
	n += 1 + sovGamelotto(uint64(m.UpdateTime))
	return n
}

func (m *AddLottoChanceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Chance))
	n += 2
	return n
}

func (m *SetLottoTypeChanceV2Req) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Type))
	n += 1 + sovGamelotto(uint64(m.Chance))
	n += 2
	return n
}

func (m *GetLottoTypeChanceV2Req) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *LottoTypeWithChance) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Type))
	n += 1 + sovGamelotto(uint64(m.Chance))
	return n
}

func (m *LottoTypeWithStep) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Type))
	n += 1 + sovGamelotto(uint64(m.Step))
	return n
}

func (m *GetLottoTypeChanceV2Resp) Size() (n int) {
	var l int
	_ = l
	if len(m.TypeChanceList) > 0 {
		for _, e := range m.TypeChanceList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	if len(m.TypeStepList) > 0 {
		for _, e := range m.TypeStepList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	n += 1 + sovGamelotto(uint64(m.UpdateTime))
	return n
}

func (m *AwardLottoTypeChanceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Type))
	n += 1 + sovGamelotto(uint64(m.Chance))
	return n
}

func (m *SetTypeChanceConfigReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Type))
	n += 1 + sovGamelotto(uint64(m.LimitChance))
	return n
}

func (m *AddUserLottoItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.DeviceId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.ItemId))
	n += 2
	return n
}

func (m *GetUserLottoItemByUidReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Desc))
	return n
}

func (m *GetUserLottoItemByDeviceReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetUserLottoItemResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LottoResultList) > 0 {
		for _, e := range m.LottoResultList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *GetLottoItemHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Limit))
	return n
}

func (m *LottoHistoryItem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.ItemId))
	l = len(m.ItemName)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetLottoItemHistoryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ItemList) > 0 {
		for _, e := range m.ItemList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *GetLottoActiveStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetLottoActiveStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.ActiveStatus))
	n += 1 + sovGamelotto(uint64(m.ActiveTs))
	return n
}

func (m *GetUserCpIdByLyGameIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	return n
}

func (m *GetUserCpIdByLyGameIdResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.CpId)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *LottoChanceV2) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.PktChannel)
	n += 1 + l + sovGamelotto(uint64(l))
	if len(m.OverdueTsList) > 0 {
		for _, e := range m.OverdueTsList {
			n += 1 + sovGamelotto(uint64(e))
		}
	}
	n += 1 + sovGamelotto(uint64(m.AccumulateFee))
	n += 1 + sovGamelotto(uint64(m.RechargeFeeThreshold))
	n += 2
	n += 1 + sovGamelotto(uint64(m.MaxVoucherValue))
	return n
}

func (m *UserLottoChanceV2) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	if len(m.LottoChanceList) > 0 {
		for _, e := range m.LottoChanceList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *BatchGetUserRechargeLottoChancesReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovGamelotto(uint64(e))
		}
	}
	n += 2
	return n
}

func (m *BatchGetUserRechargeLottoChancesResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChanceList) > 0 {
		for _, e := range m.ChanceList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *GetUserRechargeLottoChanceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	return n
}

func (m *GetUserRechargeLottoChanceResp) Size() (n int) {
	var l int
	_ = l
	if m.LottoChance != nil {
		l = m.LottoChance.Size()
		n += 1 + l + sovGamelotto(uint64(l))
	}
	return n
}

func (m *CleanExpiredRecordsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.BeforeTimestamp))
	return n
}

func (m *RechargeLottoParam) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.PktChannel)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.RechargeFeeThreshold))
	n += 1 + sovGamelotto(uint64(m.RebateRatioThousandth))
	n += 1 + sovGamelotto(uint64(m.MinLotteryGiftValue))
	return n
}

func (m *UpdateRechargeLottoParamReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.PktChannel)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.RechargeFeeThreshold))
	n += 1 + sovGamelotto(uint64(m.RebateRatioThousandth))
	n += 1 + sovGamelotto(uint64(m.MinLotteryGiftValue))
	return n
}

func (m *GetRechargeLottoParamReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.PktChannel)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetRechargeLottoParamResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ParamList) > 0 {
		for _, e := range m.ParamList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *BatchGetRechargeLottoParamReq) Size() (n int) {
	var l int
	_ = l
	if len(m.LyGameIdList) > 0 {
		for _, e := range m.LyGameIdList {
			n += 1 + sovGamelotto(uint64(e))
		}
	}
	return n
}

func (m *BatchGetRechargeLottoParamResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ParamList) > 0 {
		for _, e := range m.ParamList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *AddLottoRechargeFeeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.PktChannel)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.OrderId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.RechargeValue))
	return n
}

func (m *GetPublicLottoRecordReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Limit))
	return n
}

func (m *RewardRecord) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.Amount))
	n += 1 + sovGamelotto(uint64(m.Timestamp))
	return n
}

func (m *GetPublicLottoRecordResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecordList) > 0 {
		for _, e := range m.RecordList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *GetWinningListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LottoId))
	n += 1 + sovGamelotto(uint64(m.Limit))
	n += 2
	return n
}

func (m *GetWinningListResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Winner))
	l = len(m.ItemName)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *DelPublicLottryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.LottoId))
	n += 1 + sovGamelotto(uint64(m.ItemId))
	return n
}

func (m *LottryInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.ItemId))
	n += 1 + sovGamelotto(uint64(m.ItemType))
	n += 1 + sovGamelotto(uint64(m.ItemCount))
	n += 9
	n += 1 + sovGamelotto(uint64(m.Amount))
	n += 1 + sovGamelotto(uint64(m.LyGameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.ItemName)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.PicUrl)
	n += 1 + l + sovGamelotto(uint64(l))
	l = len(m.ChannelId)
	n += 1 + l + sovGamelotto(uint64(l))
	return n
}

func (m *GetPublicLottryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LottryInfoList) > 0 {
		for _, e := range m.LottryInfoList {
			l = e.Size()
			n += 1 + l + sovGamelotto(uint64(l))
		}
	}
	return n
}

func (m *SubmitPreLottoItemReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGamelotto(uint64(m.Uid))
	n += 1 + sovGamelotto(uint64(m.GameId))
	l = len(m.LottoName)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.ItemId))
	l = len(m.DeviceId)
	n += 1 + l + sovGamelotto(uint64(l))
	n += 1 + sovGamelotto(uint64(m.Valid))
	return n
}

func sovGamelotto(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGamelotto(x uint64) (n int) {
	return sovGamelotto(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ReportGameLoginReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportGameLoginReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportGameLoginReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LianyunGameId", wireType)
			}
			m.LianyunGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LianyunGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lianyun_game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoChance) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoChance: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoChance: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoId", wireType)
			}
			m.LottoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Chances", wireType)
			}
			m.Chances = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chances |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChanceUsed", wireType)
			}
			m.ChanceUsed = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChanceUsed |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FreeChances", wireType)
			}
			m.FreeChances = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FreeChances |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReddiamondChances", wireType)
			}
			m.ReddiamondChances = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReddiamondChances |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chances")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLottoChancesReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLottoChancesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLottoChancesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LianyunGameId", wireType)
			}
			m.LianyunGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LianyunGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lianyun_game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLottoChancesResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLottoChancesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLottoChancesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChanceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChanceList = append(m.ChanceList, &LottoChance{})
			if err := m.ChanceList[len(m.ChanceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLottoActive", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLottoActive = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserDoLottryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserDoLottryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserDoLottryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoId", wireType)
			}
			m.LottoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoCount", wireType)
			}
			m.LottoCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TtChannelId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TtChannelId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PayChance", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PayChance = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IpAddr", wireType)
			}
			m.IpAddr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IpAddr |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PayUid", wireType)
			}
			m.PayUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PayUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SdkDeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SdkDeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Detail", wireType)
			}
			m.Detail = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Detail |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoTime", wireType)
			}
			m.LottoTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserDoLottryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserDoLottryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserDoLottryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoResultList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoResultList = append(m.LottoResultList, &LottoResult{})
			if err := m.LottoResultList[len(m.LottoResultList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Chances", wireType)
			}
			m.Chances = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chances |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NeedRedDiamond", wireType)
			}
			m.NeedRedDiamond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NeedRedDiamond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReddiamondChances", wireType)
			}
			m.ReddiamondChances = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReddiamondChances |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddLottryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddLottryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddLottryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoId", wireType)
			}
			m.LottoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HitRate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.HitRate = float64(math4.Float64frombits(v))
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottryItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottryItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottryItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LianyunGameId", wireType)
			}
			m.LianyunGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LianyunGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lianyun_game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoItemExtend) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoItemExtend: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoItemExtend: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemDetail", wireType)
			}
			m.ItemDetail = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemDetail |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HitRate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.HitRate = float64(math4.Float64frombits(v))
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Valid", wireType)
			}
			m.Valid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Valid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoItemSet) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoItemSet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoItemSet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoId", wireType)
			}
			m.LottoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoItemList = append(m.LottoItemList, &LottoItem{})
			if err := m.LottoItemList[len(m.LottoItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottryItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottryItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottryItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottomItemSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottomItemSetList = append(m.LottomItemSetList, &LottoItemSet{})
			if err := m.LottomItemSetList[len(m.LottomItemSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetContinousLoginDaysReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetContinousLoginDaysReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetContinousLoginDaysReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetContinousLoginDaysResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetContinousLoginDaysResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetContinousLoginDaysResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContinousLoginDays", wireType)
			}
			m.ContinousLoginDays = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContinousLoginDays |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("continous_login_days")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetLottoChanceReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetLottoChanceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetLottoChanceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamelotto
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamelotto
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamelotto
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamelotto
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoNames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoNames = append(m.LottoNames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoGameChance) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoGameChance: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoGameChance: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoChanceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoChanceList = append(m.LottoChanceList, &LottoChance{})
			if err := m.LottoChanceList[len(m.LottoChanceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoChanceSet) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoChanceSet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoChanceSet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoGameChanceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoGameChanceList = append(m.LottoGameChanceList, &LottoGameChance{})
			if err := m.LottoGameChanceList[len(m.LottoGameChanceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetLottoChanceResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetLottoChanceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetLottoChanceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoChanceSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoChanceSetList = append(m.LottoChanceSetList, &LottoChanceSet{})
			if err := m.LottoChanceSetList[len(m.LottoChanceSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserContinuousLoginDaysReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserContinuousLoginDaysReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserContinuousLoginDaysReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserContinuousLoginDaysResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserContinuousLoginDaysResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserContinuousLoginDaysResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Days", wireType)
			}
			m.Days = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Days |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("days")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LoginLottoConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LoginLottoConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LoginLottoConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxAccumulateChance", wireType)
			}
			m.MaxAccumulateChance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxAccumulateChance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DefaultChance", wireType)
			}
			m.DefaultChance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DefaultChance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PayChance", wireType)
			}
			m.PayChance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PayChance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamondConsume", wireType)
			}
			m.RedDiamondConsume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamondConsume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddLoginLotttoConfigReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddLoginLotttoConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddLoginLotttoConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Config", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Config == nil {
				m.Config = &LoginLottoConfig{}
			}
			if err := m.Config.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cp_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("config")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLoginLotttoConfigReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLoginLotttoConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLoginLotttoConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cp_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLoginLotttoConfigResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLoginLotttoConfigResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLoginLotttoConfigResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Config", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Config == nil {
				m.Config = &LoginLottoConfig{}
			}
			if err := m.Config.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("config")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoId", wireType)
			}
			m.LottoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddVoucherReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddVoucherReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddVoucherReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoucherId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("voucher_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("send_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateVoucherReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateVoucherReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateVoucherReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoucherId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UseTime", wireType)
			}
			m.UseTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UseTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("voucher_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("use_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoPreorderActInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoPreorderActInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoPreorderActInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannerUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BannerUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rules", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Rules = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Roulette", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Roulette = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BtnFollow", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BtnFollow = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BgColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BgColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardIntro", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardIntro = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("banner_url")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rules")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoPreorderActReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoPreorderActReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoPreorderActReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VoucherItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VoucherItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VoucherItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateLottoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateLottoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateLottoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Classic", wireType)
			}
			m.Classic = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Classic |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeFeeThreshold", wireType)
			}
			m.RechargeFeeThreshold = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeFeeThreshold |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RebateRatioThousandth", wireType)
			}
			m.RebateRatioThousandth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RebateRatioThousandth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinLotteryGiftValue", wireType)
			}
			m.MinLotteryGiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinLotteryGiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateLottoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateLottoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateLottoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoId", wireType)
			}
			m.LottoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *EnableLottoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: EnableLottoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: EnableLottoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Enable", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Enable = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("enable")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFirstLottoItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFirstLottoItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFirstLottoItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoucherList = append(m.VoucherList, &VoucherItem{})
			if err := m.VoucherList[len(m.VoucherList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cp_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoInfoByNameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoInfoByNameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoInfoByNameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Classic", wireType)
			}
			m.Classic = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Classic |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("classic")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BaseLottoInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BaseLottoInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BaseLottoInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Active", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Active = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cp_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("active")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoInfoByNameResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoInfoByNameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoInfoByNameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoInfoList = append(m.LottoInfoList, &BaseLottoInfo{})
			if err := m.LottoInfoList[len(m.LottoInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHasFirstLoginLottoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHasFirstLoginLottoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHasFirstLoginLottoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IpAddr", wireType)
			}
			m.IpAddr = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IpAddr |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SdkDeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SdkDeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ip_addr")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHasFirstLoginLottoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHasFirstLoginLottoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHasFirstLoginLottoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HasChance", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasChance = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoucherList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VoucherList = append(m.VoucherList, &VoucherItem{})
			if err := m.VoucherList[len(m.VoucherList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("has_chance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetLottoTypeChanceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetLottoTypeChanceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetLottoTypeChanceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Chance", wireType)
			}
			m.Chance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoTypeChanceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoTypeChanceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoTypeChanceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoTypeChanceResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoTypeChanceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoTypeChanceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Chance", wireType)
			}
			m.Chance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddLottoChanceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddLottoChanceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddLottoChanceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Chance", wireType)
			}
			m.Chance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Force", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Force = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetLottoTypeChanceV2Req) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetLottoTypeChanceV2Req: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetLottoTypeChanceV2Req: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Chance", wireType)
			}
			m.Chance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsReset", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsReset = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoTypeChanceV2Req) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoTypeChanceV2Req: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoTypeChanceV2Req: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoTypeWithChance) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoTypeWithChance: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoTypeWithChance: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Chance", wireType)
			}
			m.Chance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoTypeWithStep) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoTypeWithStep: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoTypeWithStep: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Step", wireType)
			}
			m.Step = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Step |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("step")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoTypeChanceV2Resp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoTypeChanceV2Resp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoTypeChanceV2Resp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TypeChanceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TypeChanceList = append(m.TypeChanceList, &LottoTypeWithChance{})
			if err := m.TypeChanceList[len(m.TypeChanceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TypeStepList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TypeStepList = append(m.TypeStepList, &LottoTypeWithStep{})
			if err := m.TypeStepList[len(m.TypeStepList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwardLottoTypeChanceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardLottoTypeChanceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardLottoTypeChanceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Chance", wireType)
			}
			m.Chance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Chance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("chance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetTypeChanceConfigReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetTypeChanceConfigReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetTypeChanceConfigReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitChance", wireType)
			}
			m.LimitChance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitChance |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limitChance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserLottoItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserLottoItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserLottoItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JustAdd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.JustAdd = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLottoItemByUidReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLottoItemByUidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLottoItemByUidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			m.Desc = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Desc |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLottoItemByDeviceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLottoItemByDeviceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLottoItemByDeviceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLottoItemResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLottoItemResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLottoItemResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoResultList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoResultList = append(m.LottoResultList, &LottoResult{})
			if err := m.LottoResultList[len(m.LottoResultList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoItemHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoItemHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoItemHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoHistoryItem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoHistoryItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoHistoryItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoItemHistoryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoItemHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoItemHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemList = append(m.ItemList, &LottoHistoryItem{})
			if err := m.ItemList[len(m.ItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoActiveStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoActiveStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoActiveStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cp_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLottoActiveStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLottoActiveStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLottoActiveStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveStatus", wireType)
			}
			m.ActiveStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveTs", wireType)
			}
			m.ActiveTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("active_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCpIdByLyGameIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCpIdByLyGameIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCpIdByLyGameIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCpIdByLyGameIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCpIdByLyGameIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCpIdByLyGameIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cp_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottoChanceV2) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottoChanceV2: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottoChanceV2: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PktChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PktChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamelotto
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.OverdueTsList = append(m.OverdueTsList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamelotto
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamelotto
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamelotto
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.OverdueTsList = append(m.OverdueTsList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field OverdueTsList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccumulateFee", wireType)
			}
			m.AccumulateFee = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AccumulateFee |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeFeeThreshold", wireType)
			}
			m.RechargeFeeThreshold = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeFeeThreshold |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveStatus", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ActiveStatus = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxVoucherValue", wireType)
			}
			m.MaxVoucherValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxVoucherValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkt_channel")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("active_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLottoChanceV2) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLottoChanceV2: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLottoChanceV2: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoChanceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoChanceList = append(m.LottoChanceList, &LottoChanceV2{})
			if err := m.LottoChanceList[len(m.LottoChanceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserRechargeLottoChancesReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserRechargeLottoChancesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserRechargeLottoChancesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamelotto
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamelotto
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamelotto
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamelotto
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllRechargeChances", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AllRechargeChances = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetUserRechargeLottoChancesResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetUserRechargeLottoChancesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetUserRechargeLottoChancesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChanceList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChanceList = append(m.ChanceList, &UserLottoChanceV2{})
			if err := m.ChanceList[len(m.ChanceList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRechargeLottoChanceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRechargeLottoChanceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRechargeLottoChanceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRechargeLottoChanceResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRechargeLottoChanceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRechargeLottoChanceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoChance", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.LottoChance == nil {
				m.LottoChance = &LottoChanceV2{}
			}
			if err := m.LottoChance.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_chance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanExpiredRecordsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanExpiredRecordsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanExpiredRecordsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeforeTimestamp", wireType)
			}
			m.BeforeTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeforeTimestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("before_timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RechargeLottoParam) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RechargeLottoParam: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RechargeLottoParam: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PktChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PktChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeFeeThreshold", wireType)
			}
			m.RechargeFeeThreshold = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeFeeThreshold |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RebateRatioThousandth", wireType)
			}
			m.RebateRatioThousandth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RebateRatioThousandth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinLotteryGiftValue", wireType)
			}
			m.MinLotteryGiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinLotteryGiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkt_channel")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateRechargeLottoParamReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateRechargeLottoParamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateRechargeLottoParamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PktChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PktChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeFeeThreshold", wireType)
			}
			m.RechargeFeeThreshold = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeFeeThreshold |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RebateRatioThousandth", wireType)
			}
			m.RebateRatioThousandth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RebateRatioThousandth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MinLotteryGiftValue", wireType)
			}
			m.MinLotteryGiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinLotteryGiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkt_channel")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRechargeLottoParamReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRechargeLottoParamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRechargeLottoParamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PktChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PktChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRechargeLottoParamResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRechargeLottoParamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRechargeLottoParamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParamList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParamList = append(m.ParamList, &RechargeLottoParam{})
			if err := m.ParamList[len(m.ParamList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetRechargeLottoParamReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetRechargeLottoParamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetRechargeLottoParamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamelotto
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.LyGameIdList = append(m.LyGameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGamelotto
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGamelotto
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGamelotto
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.LyGameIdList = append(m.LyGameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetRechargeLottoParamResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetRechargeLottoParamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetRechargeLottoParamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParamList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ParamList = append(m.ParamList, &RechargeLottoParam{})
			if err := m.ParamList[len(m.ParamList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddLottoRechargeFeeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddLottoRechargeFeeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddLottoRechargeFeeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PktChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PktChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeValue", wireType)
			}
			m.RechargeValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ly_game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pkt_channel")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPublicLottoRecordReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPublicLottoRecordReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPublicLottoRecordReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RewardRecord) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RewardRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RewardRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPublicLottoRecordResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPublicLottoRecordResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPublicLottoRecordResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecordList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecordList = append(m.RecordList, &RewardRecord{})
			if err := m.RecordList[len(m.RecordList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetWinningListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetWinningListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetWinningListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoId", wireType)
			}
			m.LottoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoId |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FillByOther", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.FillByOther = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fill_by_other")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetWinningListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetWinningListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetWinningListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Winner", wireType)
			}
			m.Winner = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Winner |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("winner")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_name")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelPublicLottryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelPublicLottryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelPublicLottryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoId", wireType)
			}
			m.LottoId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LottoId |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LottryInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LottryInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LottryInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemType", wireType)
			}
			m.ItemType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemCount", wireType)
			}
			m.ItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemCount |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HitRate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.HitRate = float64(math4.Float64frombits(v))
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ItemName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPublicLottryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPublicLottryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPublicLottryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottryInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottryInfoList = append(m.LottryInfoList, &LottryInfo{})
			if err := m.LottryInfoList[len(m.LottryInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SubmitPreLottoItemReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SubmitPreLottoItemReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SubmitPreLottoItemReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LottoName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LottoName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			m.ItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGamelotto
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Valid", wireType)
			}
			m.Valid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Valid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipGamelotto(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGamelotto
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lotto_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("item_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("valid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGamelotto(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGamelotto
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGamelotto
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGamelotto
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGamelotto
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGamelotto(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGamelotto = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGamelotto   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/gamelotto/lotto/gamelotto.proto", fileDescriptorGamelotto) }

var fileDescriptorGamelotto = []byte{
	// 5311 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x5c, 0xcb, 0x8f, 0x1c, 0xc7,
	0x79, 0x57, 0xcf, 0xec, 0x6b, 0xbe, 0xd9, 0xc7, 0xb0, 0x76, 0xb9, 0x5c, 0x0e, 0xc9, 0xe5, 0xa8,
	0x29, 0xca, 0xd4, 0x63, 0x49, 0x79, 0x25, 0x53, 0xd2, 0x64, 0x3c, 0xf2, 0xbe, 0x48, 0x6e, 0x44,
	0x51, 0xc4, 0xec, 0x72, 0x1d, 0x29, 0x56, 0x26, 0xbd, 0xd3, 0xb5, 0xbb, 0x6d, 0xf6, 0x74, 0xb7,
	0xba, 0x7b, 0x96, 0x3b, 0x32, 0x84, 0x28, 0x89, 0x02, 0xd8, 0x46, 0x1e, 0x0e, 0x23, 0xc7, 0x46,
	0x10, 0x07, 0x86, 0xa1, 0xd8, 0x71, 0x82, 0x1c, 0x72, 0x49, 0x80, 0x1c, 0x72, 0x08, 0x72, 0x50,
	0x0e, 0x41, 0x12, 0xc4, 0x97, 0x20, 0x48, 0x60, 0x28, 0x17, 0x1d, 0x92, 0x3f, 0x20, 0xb7, 0xa0,
	0x1e, 0xdd, 0x5d, 0xd5, 0x5d, 0xf3, 0xa0, 0x77, 0x2d, 0x38, 0xb9, 0x10, 0x3b, 0x55, 0xd5, 0x55,
	0xbf, 0xfa, 0xea, 0xfb, 0xbe, 0xfa, 0x5e, 0x45, 0xb8, 0x12, 0x60, 0xff, 0xd0, 0x6a, 0xe1, 0xe0,
	0xda, 0xbe, 0xd1, 0xc6, 0xb6, 0x1b, 0x86, 0xee, 0x35, 0xf6, 0x6f, 0xfc, 0xfb, 0xaa, 0xe7, 0xbb,
	0xa1, 0x8b, 0x0a, 0x71, 0x43, 0xf9, 0x89, 0x96, 0xdb, 0x6e, 0xbb, 0xce, 0xb5, 0xd0, 0x3e, 0xf4,
	0xac, 0xd6, 0x7d, 0x1b, 0x5f, 0x0b, 0xee, 0xef, 0x76, 0x2c, 0x3b, 0xb4, 0x9c, 0xb0, 0xeb, 0x61,
	0xf6, 0x81, 0xfe, 0x26, 0xa0, 0x06, 0xf6, 0x5c, 0x3f, 0xbc, 0x69, 0xb4, 0xf1, 0x6d, 0x77, 0xdf,
	0x72, 0x1a, 0xf8, 0x6d, 0x34, 0x0f, 0xf9, 0x8e, 0x65, 0x2e, 0x68, 0x95, 0xdc, 0x95, 0xa9, 0xd5,
	0x91, 0x8f, 0xfe, 0xe3, 0xe2, 0x63, 0x0d, 0xd2, 0x80, 0x9e, 0x85, 0x19, 0xdb, 0x32, 0x9c, 0x6e,
	0xc7, 0x69, 0x92, 0x85, 0x9a, 0x96, 0xb9, 0x90, 0x13, 0xc6, 0x4c, 0xf1, 0x4e, 0x32, 0xd7, 0xa6,
	0xa9, 0xff, 0x75, 0x0e, 0x8a, 0xb7, 0x09, 0x96, 0xb5, 0x03, 0xc3, 0x69, 0x61, 0x74, 0x11, 0x26,
	0x28, 0xb4, 0x66, 0x6a, 0xea, 0x71, 0xda, 0xba, 0x69, 0xa2, 0x4b, 0x00, 0x6c, 0x80, 0x63, 0xb4,
	0x31, 0x9d, 0xb9, 0xc0, 0x87, 0x14, 0x68, 0xfb, 0x1d, 0xa3, 0x8d, 0xd1, 0x22, 0x8c, 0xb7, 0xe8,
	0x7c, 0xc1, 0x42, 0x5e, 0x9c, 0x84, 0x37, 0xa2, 0xb3, 0x30, 0xda, 0xf2, 0xc8, 0x12, 0x23, 0x15,
	0x2d, 0xfe, 0x7e, 0xa4, 0xe5, 0x6d, 0x9a, 0xe8, 0x32, 0x14, 0xd9, 0xa8, 0x66, 0x27, 0xc0, 0xe6,
	0xc2, 0x68, 0x45, 0x8b, 0x3f, 0x07, 0xd6, 0x71, 0x2f, 0xc0, 0x26, 0xfa, 0x0c, 0x4c, 0xee, 0xf9,
	0x18, 0x37, 0xa3, 0x65, 0xc6, 0x84, 0x71, 0x45, 0xd2, 0xb3, 0xc6, 0x97, 0x7a, 0x1e, 0x90, 0x8f,
	0x4d, 0xd3, 0x32, 0xda, 0xae, 0x63, 0xc6, 0xc3, 0xc7, 0x85, 0xe1, 0xa7, 0x92, 0xfe, 0xe8, 0xa3,
	0xcb, 0x50, 0xec, 0x78, 0xa6, 0x11, 0xe2, 0x66, 0x68, 0xb5, 0xf1, 0xc2, 0x84, 0x08, 0x82, 0x75,
	0x6c, 0x5b, 0x6d, 0xac, 0x7f, 0x5f, 0x83, 0xf9, 0x9b, 0x38, 0xbc, 0x17, 0x60, 0x5f, 0xa0, 0x61,
	0x70, 0x62, 0xa7, 0x93, 0x22, 0x76, 0x5e, 0x20, 0x96, 0x40, 0xec, 0xc7, 0xa1, 0x60, 0x62, 0xc2,
	0x7a, 0x69, 0x82, 0x4e, 0xb0, 0xe6, 0x4d, 0x53, 0x7f, 0x4f, 0x83, 0x33, 0x4a, 0xa0, 0x81, 0x87,
	0x5e, 0x8c, 0x09, 0x6e, 0x5b, 0x41, 0xb8, 0xa0, 0x55, 0xf2, 0x57, 0x8a, 0xcb, 0xf3, 0x57, 0x13,
	0xae, 0x15, 0xbe, 0x88, 0x8e, 0xe0, 0xb6, 0x15, 0x84, 0x64, 0x2b, 0x56, 0xd0, 0x64, 0xf8, 0x8c,
	0x56, 0x68, 0x1d, 0x12, 0x76, 0xd0, 0xae, 0x4c, 0x44, 0x5b, 0xb1, 0x02, 0xfa, 0xe5, 0x0a, 0xed,
	0xd2, 0x3f, 0xc8, 0xc3, 0x0c, 0x59, 0x7f, 0xdd, 0x25, 0xad, 0x7e, 0xb7, 0x1f, 0x91, 0x44, 0x26,
	0xcc, 0x09, 0xb4, 0x8f, 0x99, 0x50, 0x07, 0xb0, 0xbb, 0x31, 0x01, 0xf3, 0xc2, 0x90, 0x09, 0xbb,
	0xab, 0xa4, 0xdd, 0x88, 0x9a, 0x76, 0x97, 0xa1, 0xc8, 0x06, 0xb5, 0xdc, 0x8e, 0x13, 0x2e, 0x8c,
	0x0a, 0x48, 0xd8, 0xd7, 0x6b, 0xa4, 0x5d, 0x26, 0xf1, 0x98, 0x8a, 0xc4, 0xe8, 0x0a, 0x4c, 0x85,
	0x21, 0xe5, 0x2f, 0x07, 0xdb, 0x64, 0xd8, 0xb8, 0x30, 0xac, 0x18, 0x86, 0x6b, 0xac, 0x87, 0x01,
	0xf3, 0x8c, 0x2e, 0x67, 0x45, 0xca, 0x5b, 0x11, 0xc9, 0x0a, 0x9e, 0xd1, 0xe5, 0x72, 0x78, 0x01,
	0xc6, 0x2d, 0xaf, 0x69, 0x98, 0xa6, 0xbf, 0x50, 0x10, 0xb6, 0x37, 0x66, 0x79, 0x2b, 0xa6, 0xe9,
	0x93, 0x6e, 0x32, 0x07, 0xa1, 0x1e, 0x88, 0xdd, 0x9e, 0xd1, 0xbd, 0x67, 0x51, 0x30, 0x81, 0x79,
	0xbf, 0x99, 0x60, 0x2e, 0x8a, 0x60, 0x02, 0xf3, 0xfe, 0x7a, 0xc4, 0x19, 0xff, 0xa3, 0x71, 0xf9,
	0x6f, 0xe0, 0xa0, 0x63, 0x87, 0x74, 0xdd, 0x10, 0xb7, 0xd3, 0xe2, 0x3f, 0x46, 0x1a, 0x19, 0x76,
	0xda, 0xcd, 0xc8, 0x25, 0x72, 0x6e, 0x81, 0xb4, 0xc7, 0xd4, 0xa2, 0x83, 0x88, 0x0a, 0x93, 0xe4,
	0x7f, 0x82, 0x34, 0x6f, 0x77, 0x3d, 0x8c, 0xce, 0xc3, 0x98, 0x89, 0x43, 0xc3, 0xb2, 0x17, 0x46,
	0xc4, 0x55, 0x58, 0x5b, 0x3c, 0x01, 0x3d, 0xb9, 0x51, 0x91, 0xdc, 0xa4, 0x99, 0x1e, 0x1c, 0x21,
	0x80, 0xd5, 0x6a, 0x76, 0x7c, 0x5b, 0x3a, 0x8f, 0x31, 0xcf, 0x6a, 0xdd, 0xf3, 0xed, 0xe4, 0xf0,
	0xa9, 0xfc, 0x8a, 0xd2, 0xce, 0x0e, 0x9f, 0x8a, 0xef, 0xbf, 0x69, 0x50, 0x92, 0x59, 0x32, 0xf0,
	0xd0, 0x2a, 0x9c, 0x62, 0x5f, 0xfa, 0x94, 0x20, 0x7d, 0x85, 0x82, 0xd1, 0xac, 0x31, 0x63, 0x27,
	0x3f, 0xa8, 0x64, 0x08, 0xea, 0x4f, 0x62, 0xdf, 0x48, 0xfd, 0x5d, 0x85, 0x92, 0x83, 0xb1, 0xd9,
	0xf4, 0xb1, 0xd9, 0xe4, 0x9a, 0x47, 0x62, 0xe2, 0x69, 0xd2, 0xdb, 0xc0, 0xe6, 0x3a, 0xeb, 0xeb,
	0xa1, 0xc3, 0x46, 0xfa, 0xea, 0x30, 0xfd, 0xc7, 0x39, 0x98, 0x5c, 0x31, 0xcd, 0x44, 0xda, 0x64,
	0xd5, 0xae, 0x90, 0x2a, 0xe1, 0xec, 0x73, 0x8a, 0xb3, 0x1f, 0xe2, 0x58, 0x65, 0xf6, 0x20, 0x00,
	0x47, 0xb3, 0xec, 0x71, 0x11, 0x26, 0x0e, 0xac, 0xb0, 0xe9, 0x1b, 0x21, 0x3b, 0x5c, 0x2d, 0xc2,
	0x71, 0x60, 0x85, 0x0d, 0x23, 0xa4, 0xcc, 0x61, 0xb4, 0xe9, 0x0c, 0xa2, 0x56, 0xe7, 0x6d, 0x29,
	0xd9, 0x1f, 0x1f, 0x42, 0xf6, 0x27, 0x7a, 0xea, 0xcd, 0x84, 0xcb, 0x0a, 0x83, 0xb8, 0x0c, 0xb2,
	0x5c, 0x46, 0xd4, 0x6a, 0xe9, 0x26, 0x0e, 0x19, 0x89, 0x37, 0x43, 0xdc, 0x26, 0x64, 0x56, 0x68,
	0x78, 0x6d, 0x58, 0x0d, 0x9f, 0x53, 0x23, 0x8d, 0xaf, 0xcb, 0x7c, 0xfa, 0xba, 0xd4, 0xb7, 0x60,
	0x86, 0xb2, 0x22, 0x59, 0x7d, 0xe3, 0x28, 0xc4, 0x8e, 0x29, 0xef, 0x4b, 0x1b, 0xb4, 0xaf, 0x9c,
	0x62, 0x5f, 0xdf, 0xce, 0x41, 0x21, 0x9e, 0xf5, 0xd1, 0x55, 0xc2, 0xe8, 0x10, 0x2a, 0x61, 0x34,
	0xc3, 0x3b, 0x97, 0xa1, 0x48, 0x87, 0x28, 0xf4, 0x02, 0x5d, 0x60, 0xfd, 0xa4, 0x74, 0x83, 0xc8,
	0x7f, 0xe3, 0x2a, 0xfe, 0x2b, 0xc3, 0xe8, 0xa1, 0x61, 0x5b, 0xa6, 0x74, 0xef, 0xb3, 0x26, 0xfd,
	0xa1, 0x06, 0x93, 0x31, 0x69, 0xb6, 0x70, 0x78, 0x42, 0x06, 0x53, 0x0d, 0x66, 0xf8, 0x2c, 0x64,
	0x6f, 0x54, 0xe7, 0xe4, 0xa9, 0xce, 0x99, 0x4b, 0xeb, 0x1c, 0xca, 0x66, 0x53, 0x76, 0xf4, 0x27,
	0xd1, 0x37, 0xfa, 0x5b, 0x70, 0x2a, 0xc5, 0x86, 0x81, 0x87, 0x6e, 0xc1, 0x1c, 0x1d, 0xd5, 0x66,
	0x73, 0x06, 0x58, 0xd2, 0x65, 0x67, 0x54, 0xf3, 0x6e, 0xe1, 0xb0, 0xc1, 0xb4, 0x5f, 0x9b, 0xff,
	0xa4, 0xd3, 0xef, 0xc0, 0xc2, 0x4d, 0x1c, 0xae, 0xb9, 0x4e, 0x68, 0x39, 0x6e, 0x27, 0xa0, 0x16,
	0xe8, 0xba, 0xd1, 0xed, 0x6b, 0xe7, 0xc8, 0x52, 0x2a, 0xaa, 0x93, 0x58, 0x4a, 0xf5, 0x2d, 0x38,
	0xdb, 0x63, 0xde, 0xc0, 0x43, 0xd7, 0x61, 0xae, 0x15, 0xf5, 0x34, 0x6d, 0xd2, 0xd5, 0x34, 0x8d,
	0x6e, 0x20, 0xad, 0x84, 0x5a, 0x99, 0x6f, 0xf5, 0x6d, 0x98, 0x5f, 0x35, 0xc2, 0xd6, 0x01, 0x27,
	0x48, 0x64, 0xb8, 0xe0, 0xb7, 0xd1, 0x59, 0x98, 0xe8, 0x58, 0x66, 0x42, 0x84, 0xa9, 0xc6, 0x78,
	0xc7, 0x32, 0xa9, 0xc2, 0xbe, 0x18, 0x99, 0x01, 0xe4, 0x8c, 0x88, 0xd2, 0xce, 0x5f, 0x29, 0x70,
	0x03, 0x80, 0x1c, 0x4f, 0xa0, 0x77, 0xb9, 0x98, 0x11, 0xe4, 0xfc, 0x86, 0x96, 0x77, 0xa8, 0xa9,
	0x76, 0x98, 0x5c, 0x26, 0xa2, 0x85, 0x95, 0xeb, 0x6b, 0x61, 0x31, 0x3e, 0x58, 0x8b, 0xcd, 0x2c,
	0xbd, 0x0b, 0xd3, 0x42, 0x3f, 0x61, 0xb9, 0x5e, 0x34, 0x7f, 0x1d, 0xe6, 0xd9, 0x6a, 0x14, 0x54,
	0x76, 0xc9, 0x72, 0x7a, 0xc9, 0x64, 0x37, 0x8d, 0x59, 0x5b, 0x6e, 0xa0, 0x4b, 0xef, 0xc3, 0x19,
	0x25, 0x2d, 0x03, 0x0f, 0xdd, 0x86, 0xd3, 0xd2, 0xce, 0x52, 0xec, 0x75, 0x56, 0xbd, 0x3b, 0xc2,
	0x60, 0xc8, 0x96, 0x7e, 0xd3, 0x85, 0x7e, 0x11, 0x2e, 0x70, 0xf3, 0x94, 0x71, 0x43, 0xe7, 0x24,
	0xd9, 0xac, 0x0a, 0x8b, 0xfd, 0x26, 0x0f, 0x3c, 0xb4, 0x00, 0x23, 0x19, 0xde, 0xa2, 0x2d, 0xfa,
	0x8f, 0x34, 0x28, 0xd1, 0xb1, 0x6c, 0x13, 0xae, 0xb3, 0x67, 0xed, 0xa3, 0x97, 0xe0, 0x74, 0xdb,
	0x38, 0x6a, 0x1a, 0xad, 0x56, 0xa7, 0xdd, 0xb1, 0x89, 0x97, 0xc0, 0x6d, 0x39, 0xf1, 0x56, 0x9d,
	0x6d, 0x1b, 0x47, 0x2b, 0xf1, 0x08, 0xce, 0x33, 0xcf, 0xc0, 0xb4, 0x89, 0xf7, 0x0c, 0x62, 0x57,
	0xf0, 0x4f, 0x44, 0xfb, 0x60, 0x8a, 0xf7, 0xf1, 0xc1, 0xb2, 0x9d, 0x28, 0xda, 0x07, 0x82, 0x9d,
	0xf8, 0x02, 0xcc, 0x0a, 0x56, 0x44, 0xb3, 0xe5, 0x3a, 0x41, 0x87, 0x9b, 0xbb, 0xa2, 0x6d, 0xc0,
	0x2d, 0x89, 0x35, 0xd6, 0xad, 0xff, 0x96, 0x06, 0x67, 0xa8, 0x6d, 0xc0, 0x77, 0x16, 0x6d, 0x8d,
	0x90, 0x7a, 0x18, 0xbe, 0x8e, 0x2f, 0x24, 0x51, 0x9d, 0x31, 0xff, 0xed, 0x79, 0x18, 0x6b, 0xd1,
	0xb9, 0xa8, 0x9a, 0x2f, 0x2e, 0x9f, 0x93, 0x38, 0x41, 0xa6, 0x64, 0x83, 0x0f, 0xd5, 0x7f, 0x81,
	0xba, 0x27, 0x3f, 0x05, 0x38, 0xfa, 0xeb, 0x54, 0x77, 0x29, 0x66, 0x0e, 0x3c, 0x01, 0xaa, 0x36,
	0x3c, 0xd4, 0x77, 0x61, 0x2a, 0x12, 0x87, 0x4d, 0x73, 0x58, 0x80, 0x97, 0x00, 0x04, 0xcf, 0x40,
	0xba, 0xe5, 0x5b, 0xa2, 0x5f, 0x20, 0x39, 0x7b, 0xaa, 0x8b, 0x42, 0xff, 0x2c, 0x4c, 0x8b, 0xcb,
	0x07, 0xde, 0x40, 0xb3, 0x4e, 0xff, 0x3d, 0x0d, 0xa6, 0x56, 0x4c, 0x73, 0xc7, 0xed, 0xb4, 0x0e,
	0xb0, 0x4f, 0x20, 0x5f, 0x02, 0x38, 0x64, 0xbf, 0x22, 0xc8, 0xf1, 0x4a, 0xbc, 0x7d, 0xd3, 0x8c,
	0x44, 0x2e, 0xd7, 0x5f, 0xe4, 0xf2, 0xca, 0xfd, 0x3e, 0x0e, 0x85, 0x00, 0x3b, 0x26, 0xb3, 0xbe,
	0xc5, 0x9b, 0x7c, 0x82, 0x34, 0x53, 0xe3, 0xbb, 0x0b, 0xa5, 0x7b, 0xd4, 0x93, 0x7e, 0x54, 0x5c,
	0x17, 0x61, 0xa2, 0x13, 0x70, 0xc7, 0x5c, 0x04, 0x37, 0xde, 0x09, 0xa8, 0x57, 0x4e, 0x06, 0xb8,
	0xbe, 0xc9, 0xe6, 0x10, 0xa9, 0x38, 0x4e, 0x5b, 0x37, 0x4d, 0xfd, 0x4f, 0x73, 0x30, 0x47, 0x29,
	0x78, 0xd7, 0xc7, 0xb4, 0x6d, 0xa5, 0x15, 0x6e, 0x3a, 0x7b, 0x2e, 0x31, 0x1c, 0x54, 0xe7, 0x38,
	0xb6, 0xaf, 0xb6, 0xd5, 0x94, 0x37, 0xf9, 0x25, 0x80, 0x5d, 0x72, 0xa2, 0x3e, 0xb5, 0x3f, 0xa4,
	0x53, 0x64, 0xed, 0xc4, 0x04, 0x29, 0xc3, 0xa8, 0xdf, 0xb1, 0xa9, 0x0d, 0x9f, 0xf4, 0xb3, 0x26,
	0x54, 0x81, 0x09, 0xdf, 0xed, 0xd8, 0x38, 0x0c, 0x53, 0xf6, 0x4d, 0xd4, 0x4a, 0x97, 0x08, 0x9d,
	0xe6, 0x9e, 0x6b, 0xdb, 0xee, 0x03, 0xc9, 0xc4, 0x29, 0xec, 0x86, 0xce, 0x0d, 0xda, 0x4c, 0xa8,
	0xb0, 0xbb, 0xdf, 0x6c, 0xb9, 0xb6, 0xeb, 0x4b, 0xae, 0xe8, 0xf8, 0xee, 0xfe, 0x1a, 0x69, 0x24,
	0xf6, 0x96, 0xf1, 0xc0, 0xf0, 0xcd, 0xa6, 0xe5, 0x84, 0xbe, 0x2b, 0x19, 0xc9, 0x40, 0x3b, 0x36,
	0x49, 0xbb, 0xfe, 0x25, 0x1a, 0xe2, 0x48, 0x93, 0x8b, 0x9c, 0xd6, 0x09, 0x50, 0x4b, 0xbf, 0x09,
	0x45, 0x7e, 0xfe, 0xd4, 0xd4, 0x4c, 0x2c, 0x7f, 0x69, 0x46, 0x6e, 0xf9, 0x97, 0x61, 0x34, 0xeb,
	0x77, 0xb2, 0x26, 0xfd, 0xef, 0x72, 0x30, 0xbd, 0xe6, 0x63, 0x23, 0xc4, 0xdc, 0x33, 0x3b, 0x11,
	0x7c, 0x7d, 0x2c, 0x6f, 0xea, 0xe4, 0xd9, 0x46, 0x10, 0x58, 0x2d, 0x49, 0xdb, 0x46, 0x8d, 0xa8,
	0x0a, 0xf3, 0x3e, 0x6e, 0x1d, 0x18, 0xfe, 0x3e, 0x6e, 0xee, 0x61, 0xdc, 0x0c, 0x0f, 0x7c, 0x1c,
	0x1c, 0xb8, 0xb6, 0x1c, 0xd3, 0x9a, 0x8b, 0xc6, 0xdc, 0xc0, 0x78, 0x3b, 0x1a, 0x81, 0x6a, 0x70,
	0xc6, 0xc7, 0xbb, 0xe4, 0x66, 0xf1, 0x8d, 0xd0, 0x72, 0x9b, 0xe1, 0x81, 0xdb, 0x09, 0x0c, 0xc7,
	0x0c, 0x0f, 0x24, 0x97, 0xe8, 0x34, 0x1b, 0xd4, 0x20, 0x63, 0xb6, 0xe3, 0x21, 0xe8, 0x65, 0x98,
	0x6f, 0x5b, 0x0e, 0x8d, 0xcc, 0x60, 0xbf, 0xdb, 0xdc, 0xb7, 0xf6, 0xc2, 0xe6, 0xa1, 0x61, 0x77,
	0x64, 0x47, 0x78, 0xb6, 0xcd, 0x14, 0x1c, 0xf6, 0xbb, 0x37, 0xad, 0xbd, 0x70, 0x87, 0x0c, 0xd0,
	0x97, 0x61, 0x46, 0xa2, 0x62, 0x46, 0xbf, 0x64, 0x0d, 0x5c, 0x72, 0x99, 0x4c, 0x6f, 0x38, 0xc6,
	0xae, 0x3d, 0x34, 0xe9, 0xcf, 0xc3, 0x18, 0xa6, 0x1f, 0x50, 0xb2, 0x47, 0xd1, 0x0f, 0xde, 0x36,
	0x5c, 0xd0, 0xab, 0x77, 0x04, 0x51, 0xff, 0x86, 0x06, 0x73, 0x2b, 0xa6, 0x79, 0xc3, 0xf2, 0x83,
	0x30, 0x31, 0x99, 0x8f, 0x7f, 0xb3, 0xbd, 0x0c, 0x93, 0x91, 0x76, 0x12, 0x0c, 0x74, 0xd1, 0x8e,
	0x13, 0x58, 0xb9, 0x51, 0xe4, 0x63, 0xa9, 0x7d, 0xf3, 0x25, 0x38, 0x1d, 0x6b, 0x6d, 0x67, 0xcf,
	0x5d, 0xed, 0x92, 0x3d, 0x70, 0x8d, 0x27, 0xec, 0x55, 0xeb, 0x1d, 0x4d, 0xe5, 0x9c, 0x26, 0x29,
	0x3c, 0xde, 0xa8, 0xff, 0xa5, 0x06, 0x53, 0xab, 0x46, 0x80, 0xe3, 0xf9, 0x8f, 0xbb, 0xd3, 0xcb,
	0x50, 0x6c, 0x51, 0x2e, 0x60, 0x5a, 0x56, 0xd4, 0xf1, 0xc0, 0x3a, 0xa8, 0xa2, 0x25, 0xd2, 0xca,
	0xe2, 0x7e, 0x23, 0xe2, 0x31, 0xb2, 0x36, 0x72, 0x07, 0x50, 0x00, 0x59, 0x47, 0x8d, 0x34, 0x53,
	0xe9, 0x7f, 0x33, 0xd1, 0x2d, 0x22, 0x59, 0x02, 0x0f, 0x7d, 0x21, 0xf6, 0x87, 0x9c, 0x3d, 0x57,
	0x34, 0x2c, 0x17, 0x04, 0x72, 0x4b, 0x7b, 0x8e, 0x7c, 0x22, 0x67, 0xcf, 0xa5, 0x24, 0xff, 0x5b,
	0x8d, 0xde, 0xfc, 0xb7, 0x8c, 0x80, 0x33, 0x42, 0x74, 0x9f, 0x1f, 0xd3, 0x9c, 0x14, 0x23, 0x73,
	0x79, 0xc9, 0x1d, 0x66, 0x91, 0xb9, 0x54, 0x34, 0x36, 0xa7, 0x0e, 0x15, 0xca, 0xd1, 0xb9, 0xd1,
	0x5e, 0xd1, 0xb9, 0xaf, 0x50, 0x0f, 0x49, 0xb5, 0x87, 0xc0, 0x23, 0xbc, 0x73, 0x60, 0x04, 0x89,
	0xed, 0x99, 0x1c, 0x41, 0xe1, 0xc0, 0x08, 0xb8, 0x7d, 0x98, 0x66, 0xda, 0xdc, 0xf0, 0x4c, 0xfb,
	0x67, 0x1a, 0x9c, 0xde, 0xe2, 0xc7, 0x43, 0x3c, 0xf4, 0xc4, 0x93, 0xea, 0x45, 0x3e, 0x41, 0xec,
	0x73, 0x03, 0x35, 0xae, 0xda, 0xc0, 0x21, 0xb6, 0x38, 0x0d, 0x12, 0x88, 0x56, 0x03, 0x6d, 0x21,
	0xec, 0xc6, 0xf7, 0x2a, 0x86, 0x69, 0x79, 0x1b, 0xd1, 0x42, 0xb1, 0x8c, 0xfd, 0x0c, 0xa0, 0xd5,
	0xdf, 0x4a, 0x78, 0x5b, 0x84, 0x13, 0x78, 0xc2, 0x3e, 0xb4, 0xec, 0x3e, 0xd2, 0xa9, 0x87, 0x5c,
	0x8f, 0xd4, 0xc3, 0x0f, 0x35, 0x38, 0xc5, 0xa3, 0x7b, 0xee, 0xa7, 0xb3, 0xd5, 0x04, 0xf6, 0x88,
	0x02, 0x76, 0x19, 0x46, 0xf7, 0x5c, 0xbf, 0xc5, 0x24, 0x3d, 0xe2, 0x43, 0xd6, 0xa4, 0xff, 0x83,
	0x06, 0x67, 0xb2, 0x8c, 0xb4, 0xb3, 0xfc, 0x33, 0xca, 0x4a, 0x44, 0xdf, 0x5a, 0xc4, 0x31, 0xc4,
	0x2c, 0x00, 0x19, 0xed, 0x26, 0x6a, 0xd4, 0x3b, 0xdc, 0x5b, 0xf9, 0x74, 0xb7, 0xa3, 0xbf, 0x06,
	0xb3, 0xf1, 0x9a, 0x5f, 0xb4, 0xc2, 0x03, 0x2e, 0xe1, 0xd1, 0x2e, 0xb5, 0x3e, 0xbb, 0xcc, 0x29,
	0x04, 0xe6, 0x26, 0x9c, 0x92, 0xa6, 0xdb, 0x0a, 0xb1, 0xd7, 0x67, 0xb2, 0x05, 0x18, 0x09, 0x42,
	0xec, 0x49, 0x53, 0xd1, 0x16, 0xfd, 0x1f, 0x35, 0xee, 0x63, 0x65, 0xe8, 0x41, 0xa3, 0x50, 0x25,
	0xf2, 0x79, 0x33, 0x9b, 0x62, 0x5a, 0x4c, 0x87, 0x08, 0xe4, 0x7d, 0x35, 0xa6, 0xc3, 0x78, 0x2e,
	0x1a, 0xa3, 0x59, 0x05, 0xda, 0xd2, 0x24, 0x6b, 0x8a, 0xba, 0xec, 0x7c, 0xaf, 0x79, 0xc8, 0x86,
	0x1a, 0x93, 0xe4, 0x1b, 0xf2, 0x17, 0x9d, 0x23, 0x25, 0x5c, 0xf9, 0x1e, 0xc2, 0xf5, 0xe7, 0xc4,
	0x3d, 0x26, 0x26, 0xf0, 0xff, 0x11, 0xdd, 0xf7, 0x47, 0x1a, 0xcc, 0x6f, 0xe1, 0x30, 0x01, 0x9a,
	0xb8, 0xcf, 0x27, 0x61, 0x04, 0x47, 0xb0, 0xf2, 0x19, 0x58, 0x4f, 0x42, 0xd1, 0xb6, 0xda, 0x16,
	0x8f, 0x58, 0x48, 0xb8, 0xc5, 0x0e, 0xfd, 0x47, 0x1a, 0xcc, 0xae, 0x98, 0x66, 0x9c, 0x7f, 0x8c,
	0x2c, 0xb2, 0x9f, 0x26, 0x31, 0x87, 0xb8, 0x88, 0x85, 0xc8, 0xf6, 0xa8, 0x22, 0xb2, 0x7d, 0x11,
	0x26, 0xbe, 0xdc, 0x09, 0x42, 0x72, 0xd7, 0xcb, 0x8a, 0x80, 0xb4, 0xae, 0x98, 0xd4, 0xd2, 0x5c,
	0x10, 0xd3, 0xaa, 0x64, 0x5b, 0xab, 0xdd, 0x7b, 0x96, 0xf9, 0x29, 0x30, 0x8a, 0x89, 0x03, 0xd9,
	0xf1, 0xa0, 0x2d, 0xfa, 0xfb, 0x1a, 0x9c, 0xcb, 0x42, 0x62, 0x06, 0x05, 0x41, 0x25, 0x51, 0x45,
	0xeb, 0x45, 0x95, 0x63, 0xeb, 0xaa, 0x37, 0x61, 0x2e, 0x8d, 0xe2, 0xa4, 0xb2, 0x6b, 0xfa, 0x91,
	0x60, 0x35, 0x86, 0xb8, 0x7d, 0xcb, 0x0a, 0x42, 0x97, 0x65, 0xb8, 0x4e, 0x82, 0xd9, 0xcb, 0x30,
	0x4a, 0x39, 0x57, 0xe2, 0x76, 0xd6, 0xa4, 0xdb, 0x50, 0xa2, 0xcb, 0xf2, 0x25, 0xa9, 0xcb, 0xda,
	0xe7, 0x98, 0x87, 0x49, 0xa6, 0x65, 0x88, 0x18, 0xa7, 0x31, 0xf4, 0xad, 0xe4, 0x9a, 0x91, 0xf6,
	0x19, 0x78, 0xe8, 0x25, 0xfe, 0xb5, 0x40, 0xbe, 0x73, 0x69, 0xf2, 0x09, 0x20, 0xd9, 0xa4, 0x94,
	0x78, 0xef, 0x26, 0x93, 0xb2, 0xc4, 0xfc, 0x56, 0x68, 0x84, 0x9d, 0xe0, 0x04, 0xdc, 0xa3, 0xa1,
	0xf8, 0xe2, 0x20, 0xb9, 0x2a, 0xe4, 0xe5, 0x03, 0x0f, 0x3d, 0x05, 0x53, 0xcc, 0x75, 0x68, 0x06,
	0xb4, 0x51, 0x82, 0x30, 0x69, 0x08, 0xc3, 0x09, 0xf5, 0xf8, 0xd0, 0x50, 0x4e, 0xb1, 0x4e, 0xb0,
	0xe6, 0xed, 0x80, 0x27, 0x2d, 0x68, 0xd4, 0xd7, 0xdb, 0x34, 0x57, 0xbb, 0xb7, 0xf9, 0x16, 0x8e,
	0x1b, 0x4d, 0xbe, 0x4e, 0x4d, 0x72, 0xd5, 0xbc, 0x81, 0x97, 0x90, 0x47, 0xcb, 0x04, 0x22, 0x3f,
	0xca, 0xc1, 0x94, 0x60, 0xad, 0xed, 0x2c, 0x0f, 0x45, 0xef, 0xcb, 0x50, 0xf4, 0xee, 0xc7, 0x65,
	0x05, 0x12, 0xd5, 0xc1, 0xbb, 0x1f, 0x15, 0x15, 0xa0, 0x27, 0x61, 0xc6, 0x3d, 0xc4, 0xbe, 0xd9,
	0x21, 0x04, 0x49, 0xbc, 0xd3, 0xa9, 0xc6, 0x14, 0x6f, 0xde, 0x0e, 0xe8, 0xfd, 0xf7, 0x0c, 0x4c,
	0x0b, 0x51, 0xeb, 0x3d, 0x2c, 0x07, 0x8a, 0xa7, 0x92, 0xbe, 0x1b, 0x18, 0x1f, 0x2b, 0x80, 0x91,
	0x39, 0xcb, 0x31, 0xc1, 0x3d, 0x91, 0xcf, 0xf2, 0x39, 0x38, 0xd5, 0x36, 0x8e, 0x9a, 0x91, 0x97,
	0x92, 0x0d, 0x54, 0xcc, 0xb4, 0x8d, 0x23, 0xee, 0xa4, 0xb0, 0x20, 0xc5, 0xdb, 0x70, 0x2a, 0x55,
	0xc9, 0xb2, 0xb3, 0xdc, 0xf3, 0x4c, 0xd7, 0x7b, 0xa7, 0x60, 0x16, 0xd4, 0x49, 0x8a, 0x9d, 0xe5,
	0x6c, 0x12, 0xe6, 0x08, 0x2e, 0x45, 0x99, 0x10, 0xb2, 0x74, 0x83, 0xef, 0x39, 0x5d, 0xf5, 0xd3,
	0x27, 0xc5, 0x74, 0x1d, 0xe6, 0x0c, 0xdb, 0x6e, 0xc6, 0x14, 0x15, 0x0b, 0x04, 0x22, 0xc2, 0x20,
	0xc3, 0xb6, 0xa3, 0xa9, 0xa3, 0x34, 0x3e, 0x86, 0x27, 0x06, 0xaf, 0x1c, 0x78, 0xe8, 0xf3, 0xaa,
	0x32, 0x1e, 0xd1, 0x36, 0xca, 0x90, 0x4c, 0x2c, 0xe6, 0x11, 0x32, 0x30, 0x8a, 0x15, 0x8e, 0x2b,
	0x33, 0x6f, 0xc5, 0x19, 0x18, 0xe5, 0xe4, 0x81, 0x87, 0x7e, 0x0e, 0x26, 0xc5, 0x53, 0xe2, 0x01,
	0xf9, 0xde, 0x07, 0x54, 0x14, 0x0e, 0x48, 0xdf, 0x84, 0xf9, 0x35, 0x1b, 0x1b, 0xce, 0xc6, 0x91,
	0x67, 0xf9, 0xd8, 0x6c, 0xe0, 0x96, 0xeb, 0x9b, 0xf4, 0x3c, 0xae, 0x41, 0x69, 0x17, 0xef, 0xb9,
	0x3e, 0xb3, 0xf7, 0x82, 0xd0, 0x68, 0x7b, 0xd2, 0x0e, 0x66, 0x58, 0xef, 0x76, 0xd4, 0xa9, 0x7f,
	0x90, 0x03, 0x24, 0x61, 0xbc, 0x6b, 0xf8, 0x46, 0xfb, 0x24, 0x45, 0xb5, 0xb7, 0x54, 0xe5, 0x8f,
	0x13, 0x16, 0x1c, 0x39, 0x4e, 0x58, 0x70, 0x74, 0x50, 0x58, 0xf0, 0x3b, 0x39, 0x38, 0xc7, 0xa2,
	0xf5, 0x59, 0xe2, 0x0c, 0x7b, 0x75, 0xfc, 0x7f, 0xa6, 0x0f, 0xa6, 0x97, 0xcd, 0x09, 0xd2, 0x46,
	0x53, 0xd1, 0x46, 0x7f, 0x83, 0xde, 0x3d, 0xaa, 0x65, 0x02, 0x0f, 0xd5, 0x00, 0x3c, 0xf2, 0x43,
	0x94, 0xff, 0x0b, 0x82, 0x00, 0x29, 0x3e, 0x2b, 0xd0, 0x0f, 0xa8, 0xfc, 0xdf, 0x80, 0x0b, 0x91,
	0x9a, 0x51, 0x6f, 0xe3, 0x32, 0xcc, 0x24, 0xdb, 0x10, 0x35, 0xdc, 0x64, 0xb4, 0x0b, 0x3a, 0xcf,
	0x2f, 0xc1, 0x62, 0xbf, 0x79, 0x8e, 0x8d, 0xf3, 0xef, 0x35, 0x98, 0x8f, 0xe2, 0x1e, 0x8d, 0x84,
	0x07, 0x8e, 0x1b, 0xd4, 0x4b, 0x1d, 0x40, 0xbe, 0x07, 0x73, 0x8a, 0xa9, 0xa5, 0x11, 0x45, 0x6a,
	0x89, 0x5c, 0xb0, 0x31, 0xf7, 0x46, 0xbc, 0x23, 0xd4, 0xfe, 0x44, 0x7d, 0x8c, 0x6b, 0x3e, 0x47,
	0x6d, 0xb1, 0xbb, 0x9d, 0x5d, 0xdb, 0x6a, 0x45, 0x1b, 0x72, 0x7d, 0x6a, 0xa1, 0xc4, 0x56, 0xa8,
	0x96, 0xb5, 0x42, 0x0f, 0x60, 0xb2, 0x81, 0x89, 0x77, 0xca, 0x86, 0xf7, 0xdc, 0x77, 0x92, 0x4c,
	0xc9, 0x29, 0x92, 0x29, 0x3a, 0x14, 0x12, 0x9d, 0x28, 0x25, 0x97, 0xe3, 0x66, 0x7d, 0x9b, 0xb2,
	0xb5, 0x02, 0x20, 0x35, 0x41, 0x8b, 0x3e, 0xfd, 0xd5, 0xab, 0xaa, 0x44, 0xc4, 0xd8, 0x00, 0x36,
	0x96, 0x1e, 0xe1, 0x3b, 0xb4, 0x5a, 0xe5, 0x8b, 0x96, 0xe3, 0x58, 0xce, 0x3e, 0x69, 0xc9, 0x16,
	0xa7, 0x25, 0xf5, 0x41, 0x71, 0x19, 0x4d, 0x4c, 0x91, 0x5c, 0x86, 0x22, 0xe8, 0x0a, 0x4c, 0xed,
	0x59, 0xb6, 0xdd, 0xdc, 0xed, 0x36, 0xdd, 0xf0, 0x00, 0xb3, 0xc0, 0xec, 0x44, 0x5c, 0x0d, 0x6c,
	0xd9, 0xf6, 0x6a, 0xf7, 0x75, 0xd2, 0xa1, 0xdf, 0x03, 0x94, 0x5e, 0x9b, 0x45, 0xe4, 0x1e, 0x58,
	0x8e, 0x83, 0x7d, 0xd9, 0x6d, 0x60, 0x6d, 0xb2, 0xa9, 0x9e, 0x53, 0x9a, 0xea, 0xdb, 0x80, 0xd6,
	0xb1, 0x9d, 0x10, 0x4a, 0x55, 0x70, 0xa7, 0xd8, 0x53, 0xca, 0x47, 0x18, 0x95, 0x7d, 0x04, 0xfd,
	0xe3, 0x1c, 0x00, 0x2f, 0xea, 0xe1, 0xd9, 0xc9, 0x7e, 0x75, 0x58, 0x52, 0x89, 0x55, 0x6e, 0x88,
	0xf2, 0xbc, 0xfc, 0xe0, 0xf2, 0xbc, 0x91, 0xfe, 0xe5, 0x79, 0xa3, 0x03, 0xcb, 0xf3, 0xc6, 0x86,
	0x28, 0xcf, 0x1b, 0x1f, 0xa2, 0x3c, 0x6f, 0x62, 0x50, 0xa1, 0x57, 0x41, 0x5d, 0x04, 0x2a, 0x64,
	0xdd, 0x41, 0x99, 0x75, 0xd7, 0x77, 0x60, 0x56, 0xe2, 0x71, 0xee, 0x61, 0xbd, 0x02, 0x25, 0x9b,
	0xfe, 0xca, 0x64, 0x20, 0x4e, 0xa7, 0x8c, 0x12, 0x76, 0x3a, 0x8d, 0x69, 0x3b, 0xfe, 0x9b, 0x72,
	0xf9, 0x3f, 0x6b, 0x70, 0x7a, 0xab, 0xb3, 0xdb, 0xb6, 0xc2, 0xbb, 0x3e, 0xfe, 0xd4, 0x82, 0x1e,
	0x02, 0x8f, 0x8c, 0xa8, 0x79, 0x44, 0xcc, 0x3a, 0xa8, 0xbc, 0xff, 0xb8, 0xf8, 0x6d, 0x4c, 0x94,
	0x33, 0xda, 0xf4, 0xf4, 0xbf, 0x6a, 0xdc, 0x87, 0xd9, 0x8c, 0x38, 0xaa, 0x08, 0xe3, 0xdc, 0x70,
	0x2f, 0x69, 0xe4, 0x07, 0xb9, 0x34, 0xbd, 0xfb, 0xfb, 0xa5, 0x1c, 0x9a, 0x06, 0x48, 0x6a, 0x52,
	0x4b, 0x79, 0x34, 0x43, 0xf5, 0x05, 0xd1, 0x7e, 0x2d, 0xc3, 0x37, 0x4b, 0x23, 0xa8, 0x00, 0xa3,
	0x6d, 0x6c, 0x1a, 0x76, 0x69, 0x14, 0x4d, 0x41, 0xc1, 0x8b, 0x54, 0x63, 0x69, 0x8c, 0x7c, 0xca,
	0x3c, 0x84, 0x96, 0x6b, 0xe2, 0xd2, 0x38, 0x99, 0xb7, 0xe5, 0xb6, 0x77, 0x2d, 0x07, 0x97, 0x26,
	0xc8, 0x3c, 0x87, 0x96, 0x1f, 0x76, 0x0c, 0x9b, 0xec, 0xa9, 0x54, 0x60, 0x13, 0x93, 0x5f, 0x5d,
	0xda, 0x00, 0x68, 0x0e, 0x4a, 0x7b, 0xbe, 0xb1, 0xdf, 0xc6, 0x4e, 0xd8, 0x7c, 0x60, 0x38, 0xfb,
	0xef, 0x1c, 0xe0, 0x52, 0x91, 0x4c, 0x8a, 0xdb, 0x5e, 0xd8, 0xa5, 0xd5, 0x70, 0xa5, 0x49, 0xf2,
	0x9b, 0x45, 0xfc, 0xe8, 0x6f, 0xfc, 0xf4, 0x2b, 0xbc, 0xc2, 0x8b, 0xd9, 0x94, 0x74, 0x73, 0xd3,
	0x00, 0x2c, 0xc7, 0xde, 0x6c, 0xd9, 0xb8, 0xa4, 0x11, 0x98, 0xc1, 0x81, 0xe1, 0xe3, 0xa6, 0xd1,
	0x0a, 0x4b, 0x39, 0xf2, 0xf3, 0xd0, 0x0a, 0xac, 0x90, 0xfe, 0xcc, 0x3f, 0x6d, 0xc3, 0xf4, 0xba,
	0x61, 0xd9, 0xdd, 0xdb, 0x44, 0x25, 0xd1, 0xef, 0x4f, 0xc1, 0xd4, 0xc6, 0xce, 0x46, 0xe3, 0x8d,
	0xf5, 0x95, 0x37, 0x9a, 0xdb, 0x6f, 0xdc, 0xdd, 0x28, 0x3d, 0x86, 0x66, 0x61, 0xe6, 0xd5, 0x5b,
	0xcd, 0xb5, 0x5b, 0x2b, 0x77, 0xee, 0x6c, 0xdc, 0x66, 0x8d, 0x1a, 0x59, 0x67, 0xeb, 0xd6, 0x4a,
	0x63, 0x83, 0xfd, 0xce, 0x91, 0xef, 0x36, 0xef, 0xec, 0x6c, 0x6e, 0x6f, 0x34, 0x6f, 0x34, 0x36,
	0x37, 0xee, 0xac, 0x33, 0xea, 0xbd, 0x7e, 0xe7, 0xf6, 0xe6, 0x9d, 0x8d, 0xe6, 0xf6, 0xe6, 0x6b,
	0x1b, 0xa5, 0x91, 0xe5, 0xdf, 0xf8, 0x02, 0x24, 0xef, 0x48, 0xd0, 0x7b, 0x1a, 0xcc, 0xa4, 0x9e,
	0x88, 0x20, 0xf9, 0x4e, 0x4d, 0x3f, 0x1f, 0x29, 0x9f, 0xbf, 0x1a, 0x3f, 0x3a, 0xb9, 0xba, 0xf5,
	0xea, 0x2a, 0x7b, 0x74, 0xb2, 0x41, 0x29, 0x74, 0x77, 0x55, 0x7f, 0xee, 0xbd, 0x0f, 0x3f, 0xc9,
	0x6b, 0x5f, 0xff, 0xf0, 0x93, 0xfc, 0x48, 0xa7, 0xba, 0x5f, 0x7d, 0xf8, 0xe1, 0x27, 0xf9, 0x0b,
	0x4b, 0x9d, 0x4a, 0xad, 0x63, 0x99, 0xf5, 0xca, 0xd2, 0x7e, 0xa5, 0x96, 0x2a, 0x71, 0xad, 0xa3,
	0xef, 0x6a, 0x54, 0x90, 0xd2, 0x4f, 0x0c, 0xd0, 0xe3, 0x02, 0x0c, 0xf5, 0x5b, 0x89, 0xb2, 0x3e,
	0x68, 0x48, 0xe0, 0xe9, 0xab, 0x04, 0x50, 0x8e, 0x00, 0x1a, 0x23, 0x80, 0x1c, 0x0a, 0x69, 0xa9,
	0x2f, 0xa4, 0xca, 0x92, 0x53, 0xa9, 0x25, 0x22, 0x53, 0x47, 0xdf, 0xd2, 0x60, 0x52, 0xac, 0xf7,
	0x46, 0xe5, 0x94, 0x7b, 0x24, 0xbc, 0x4d, 0x28, 0x9f, 0xeb, 0xd9, 0x17, 0x78, 0xfa, 0x1d, 0x82,
	0x26, 0x4f, 0xd0, 0x4c, 0x30, 0x34, 0x98, 0xe2, 0x79, 0x51, 0xc6, 0xd3, 0xed, 0x09, 0xa5, 0xb2,
	0x84, 0x2b, 0xb5, 0x58, 0x12, 0xeb, 0xe8, 0xbf, 0x35, 0x28, 0xc4, 0xb5, 0xda, 0x48, 0xbc, 0x45,
	0xc5, 0x0a, 0xee, 0x01, 0x67, 0xf6, 0x27, 0x1a, 0x41, 0x35, 0x42, 0x50, 0xcd, 0xd9, 0x55, 0xab,
	0x1a, 0x56, 0xdd, 0xaa, 0x5f, 0x6d, 0x53, 0x7c, 0x66, 0xd5, 0xa3, 0x08, 0xdf, 0xd7, 0x96, 0xec,
	0x08, 0x09, 0xc5, 0x65, 0x55, 0x6a, 0x5c, 0x61, 0xd4, 0x2b, 0x4b, 0x61, 0x2d, 0xbe, 0x41, 0xea,
	0x95, 0x25, 0xb7, 0x96, 0x5c, 0x16, 0xf5, 0xca, 0x92, 0x5f, 0x23, 0x57, 0x42, 0xbd, 0xb2, 0xd4,
	0xae, 0x31, 0x55, 0x4f, 0x76, 0x99, 0xda, 0xa4, 0xbc, 0x47, 0xb3, 0xc6, 0x0b, 0x7a, 0x83, 0x56,
	0xbd, 0xb2, 0xe4, 0xb1, 0x5f, 0x9e, 0xd5, 0xaa, 0xa3, 0xaf, 0x6a, 0x71, 0x15, 0x15, 0x0f, 0xa3,
	0x9d, 0x93, 0x79, 0x40, 0x2a, 0xa9, 0x2e, 0x9f, 0xef, 0xdd, 0x19, 0x78, 0x7a, 0x8d, 0x6c, 0x7b,
	0x94, 0xb2, 0x06, 0xd9, 0xea, 0x11, 0xdd, 0xe8, 0x67, 0x06, 0x60, 0x3b, 0xaa, 0xd1, 0x40, 0x4d,
	0x1d, 0x7d, 0xc0, 0xf2, 0x86, 0xd9, 0x2a, 0x54, 0x74, 0x49, 0x5e, 0x55, 0x59, 0xff, 0x5a, 0x7e,
	0x62, 0xf0, 0xa0, 0xc0, 0x63, 0xe2, 0x34, 0xf6, 0x28, 0xe2, 0xf4, 0x00, 0x66, 0x15, 0xa5, 0x97,
	0x92, 0x34, 0xa9, 0xcb, 0x5c, 0x25, 0x69, 0xea, 0x51, 0xbd, 0xa9, 0x9f, 0x25, 0x78, 0xc6, 0x09,
	0x9e, 0x5c, 0x87, 0xa2, 0x99, 0x88, 0xd0, 0xa0, 0xf7, 0x35, 0x80, 0xa4, 0xc2, 0x0c, 0x2d, 0x64,
	0x49, 0xcf, 0xea, 0xde, 0xca, 0x67, 0x7b, 0xf4, 0x04, 0x9e, 0xbe, 0x42, 0xa6, 0x9f, 0xe0, 0x27,
	0x72, 0x9f, 0x0b, 0xeb, 0xb3, 0x64, 0x9f, 0xc9, 0x79, 0xdc, 0xaf, 0xd4, 0x92, 0xcb, 0x39, 0x2b,
	0xab, 0xbf, 0xa3, 0x01, 0x24, 0x45, 0x6b, 0x12, 0x0c, 0xa9, 0x96, 0x6d, 0x80, 0x4c, 0x6c, 0x12,
	0x24, 0x05, 0x2a, 0xa8, 0x87, 0x55, 0x42, 0xfa, 0x90, 0x62, 0x59, 0x5e, 0x3a, 0xac, 0x25, 0x95,
	0x66, 0xf5, 0xca, 0x52, 0x27, 0x39, 0x8a, 0x04, 0x62, 0x58, 0xa9, 0xc5, 0x55, 0x6c, 0x75, 0xf4,
	0x35, 0x0d, 0xa6, 0xa4, 0x8a, 0x35, 0x89, 0x67, 0xd3, 0xb5, 0x6c, 0x03, 0x70, 0x7d, 0x9e, 0xe0,
	0x02, 0x4a, 0xa1, 0x43, 0x2a, 0xa8, 0x04, 0xd5, 0x95, 0x34, 0xaa, 0xb0, 0x16, 0x55, 0xba, 0x51,
	0x71, 0x8c, 0x5c, 0x8f, 0x3a, 0xfa, 0x17, 0x96, 0x4f, 0x49, 0x57, 0x65, 0xa1, 0x8b, 0xe9, 0x80,
	0x49, 0xaa, 0xc2, 0x6d, 0x00, 0xaa, 0x77, 0x09, 0xaa, 0x22, 0x41, 0x35, 0x4d, 0x24, 0x69, 0xb7,
	0xea, 0x57, 0x1f, 0x54, 0xed, 0xaa, 0x41, 0xd1, 0xfd, 0xf2, 0xd2, 0x7e, 0xad, 0xa7, 0x38, 0xed,
	0xd6, 0x92, 0xa2, 0x37, 0xa6, 0x2b, 0x3a, 0x36, 0x0e, 0xf8, 0xaf, 0x07, 0xb5, 0xa4, 0x5c, 0xad,
	0x5e, 0x59, 0xb2, 0x6b, 0xb4, 0x2a, 0xad, 0x5e, 0x59, 0x32, 0x6a, 0x42, 0x05, 0x5a, 0x1d, 0x7d,
	0x9d, 0x5d, 0x21, 0x99, 0x5d, 0x3d, 0xae, 0xe0, 0x34, 0xb9, 0x16, 0xad, 0x3c, 0x68, 0xe3, 0xfa,
	0x35, 0xb2, 0xb5, 0x49, 0x2a, 0x81, 0xd1, 0xed, 0x71, 0x5e, 0x3e, 0xed, 0x14, 0x03, 0xfe, 0xae,
	0x06, 0x45, 0xa1, 0x14, 0x0a, 0x89, 0xec, 0x2e, 0x17, 0x9a, 0x95, 0xcb, 0xbd, 0xba, 0x02, 0x4f,
	0x7f, 0x95, 0xac, 0x3b, 0x45, 0x19, 0x90, 0x29, 0x27, 0xc6, 0x80, 0x2f, 0xf4, 0x5b, 0xbb, 0xb2,
	0x74, 0x54, 0xe1, 0xea, 0x89, 0x32, 0x21, 0xaf, 0xf3, 0xa9, 0xa3, 0x3f, 0xd6, 0x00, 0x65, 0x13,
	0xe9, 0xa8, 0x22, 0xac, 0xaf, 0x2c, 0xd8, 0x18, 0x70, 0xec, 0xaf, 0x13, 0x8c, 0xd3, 0x04, 0x23,
	0xb0, 0xdb, 0x2c, 0xe4, 0x47, 0x7e, 0x3d, 0x91, 0x8b, 0xde, 0x47, 0x1f, 0xd6, 0xf8, 0x7d, 0x61,
	0xd4, 0x58, 0xf0, 0xae, 0x8e, 0xbe, 0xa3, 0x51, 0x37, 0xab, 0x1f, 0x4e, 0x65, 0xa9, 0x46, 0xf9,
	0xf1, 0x01, 0x23, 0x22, 0x43, 0x60, 0x46, 0xb8, 0x7a, 0x19, 0x41, 0x9f, 0x79, 0x04, 0xa8, 0xe8,
	0xa1, 0x06, 0xd3, 0x72, 0xf1, 0x04, 0x3a, 0x9f, 0xbd, 0x73, 0xdd, 0x61, 0xe9, 0xb7, 0x4e, 0x20,
	0x95, 0x04, 0x48, 0x46, 0x64, 0x9d, 0x0c, 0x86, 0x94, 0x10, 0xed, 0x0f, 0x34, 0x98, 0x63, 0x86,
	0x46, 0xc4, 0xbb, 0xc7, 0xb5, 0x52, 0xa8, 0xf2, 0x3b, 0x95, 0xb1, 0x52, 0x96, 0x85, 0x9b, 0xa7,
	0x37, 0x30, 0xd9, 0x40, 0xf9, 0x81, 0x06, 0xa5, 0x74, 0x02, 0x17, 0x2d, 0xca, 0x34, 0x4b, 0x67,
	0x77, 0x07, 0x50, 0xed, 0x1e, 0x41, 0x87, 0x04, 0xae, 0xb3, 0xaa, 0x26, 0xc5, 0x57, 0x1b, 0x86,
	0x6e, 0x56, 0xcd, 0x0a, 0xa3, 0x2e, 0x53, 0x44, 0xfa, 0xfb, 0xec, 0x3e, 0xcf, 0xe6, 0x64, 0xd3,
	0xf7, 0xb9, 0x32, 0x6b, 0x2b, 0x29, 0x12, 0x55, 0x06, 0x53, 0x7f, 0x91, 0xc0, 0x9e, 0x4d, 0x19,
	0xa2, 0xfa, 0x60, 0xc8, 0xe8, 0x7b, 0xca, 0x64, 0x31, 0xcb, 0xcc, 0xa2, 0x27, 0xfb, 0x62, 0x8b,
	0xd3, 0xb7, 0x83, 0xe1, 0xd1, 0x8b, 0x65, 0x8e, 0xc2, 0x33, 0x63, 0x78, 0x57, 0x24, 0x12, 0xf5,
	0x03, 0xf9, 0x2d, 0x41, 0x05, 0x0b, 0x49, 0x47, 0xa5, 0x0a, 0x96, 0x93, 0xaf, 0x69, 0x2b, 0x5e,
	0x95, 0xb7, 0xd4, 0x5f, 0x26, 0xe8, 0x4e, 0xc7, 0xa6, 0x9a, 0x4d, 0xd1, 0x3d, 0xd1, 0xef, 0x9c,
	0xed, 0x1a, 0x0d, 0xf1, 0xd4, 0xd1, 0xaf, 0x08, 0x4f, 0xed, 0x56, 0x6c, 0xfb, 0xb8, 0x46, 0x23,
	0xb5, 0xc8, 0xe6, 0xa5, 0xfb, 0xe0, 0x42, 0x5f, 0x93, 0x91, 0x28, 0xb5, 0xca, 0xa0, 0x4c, 0x0c,
	0xba, 0xaa, 0x30, 0xbe, 0xfa, 0x24, 0x8c, 0xca, 0xd7, 0x1e, 0x69, 0x7c, 0xe0, 0xe9, 0x0b, 0x04,
	0xf7, 0x19, 0xc1, 0x72, 0x1b, 0xe7, 0xac, 0x47, 0x2e, 0xac, 0x59, 0x45, 0x1a, 0x44, 0x3a, 0x3a,
	0x75, 0x9a, 0x64, 0x80, 0xa0, 0xd2, 0x43, 0x5b, 0x20, 0x4b, 0x6a, 0x21, 0x59, 0xf1, 0x29, 0x72,
	0x27, 0xc5, 0x41, 0xc2, 0x67, 0x2b, 0xcf, 0x55, 0xda, 0xd8, 0x70, 0x82, 0x4a, 0xab, 0xe3, 0xfb,
	0xc4, 0xe7, 0x8e, 0xbb, 0xea, 0xe8, 0xdf, 0x59, 0x29, 0x6e, 0xe6, 0xf9, 0x05, 0xd2, 0xd3, 0xea,
	0x36, 0xfb, 0xf2, 0x63, 0x00, 0xaa, 0x5f, 0xa3, 0xde, 0xce, 0x59, 0x02, 0x6b, 0x72, 0xbf, 0x7a,
	0x54, 0x6d, 0x53, 0x1f, 0xc7, 0xa7, 0x34, 0xd9, 0xcf, 0xb8, 0x5f, 0xc2, 0x85, 0xda, 0xae, 0xd4,
	0xda, 0xc6, 0x11, 0x4f, 0x34, 0x11, 0xed, 0x41, 0x14, 0x9d, 0xf8, 0x26, 0x87, 0xf8, 0x2a, 0x95,
	0x5a, 0xf2, 0xf4, 0x86, 0xd8, 0x33, 0x95, 0x9a, 0xe2, 0x95, 0x4d, 0x1d, 0xfd, 0xa1, 0x06, 0xe5,
	0xde, 0x27, 0x86, 0xae, 0x64, 0xa5, 0x55, 0x9d, 0x5d, 0x2b, 0x3f, 0x35, 0xe4, 0xc8, 0xc0, 0xd3,
	0x9f, 0x22, 0xfb, 0x2e, 0x4b, 0xbe, 0xc4, 0xbc, 0xa0, 0x7e, 0x84, 0x6d, 0xa3, 0xff, 0xd2, 0x60,
	0xa1, 0x57, 0xda, 0x46, 0x52, 0x39, 0x7d, 0x72, 0x3b, 0x03, 0x8e, 0xe1, 0x6b, 0xf4, 0x18, 0xce,
	0x51, 0x35, 0xbe, 0x5f, 0xf5, 0xaa, 0x06, 0x75, 0x39, 0x09, 0x28, 0x2f, 0x2d, 0x4e, 0x5e, 0x4d,
	0x08, 0xb1, 0xd3, 0xab, 0x4f, 0x9d, 0xe8, 0x61, 0xf6, 0xa3, 0x3a, 0x8f, 0x43, 0xdd, 0x4f, 0x75,
	0x96, 0xa6, 0x8e, 0x7e, 0x9d, 0xa9, 0x7e, 0xc5, 0x5e, 0x53, 0xaa, 0x5f, 0xbd, 0xd1, 0x27, 0x06,
	0x0f, 0x0a, 0x3c, 0xfd, 0x22, 0xd9, 0xef, 0x05, 0x2a, 0x80, 0x8c, 0xf8, 0xd3, 0x29, 0xa2, 0x3f,
	0xd4, 0xa0, 0xdc, 0x3b, 0x05, 0x22, 0xf1, 0x44, 0xdf, 0x8c, 0x8b, 0xc4, 0x13, 0xfd, 0x73, 0x2a,
	0x0c, 0xd4, 0x62, 0x1f, 0x50, 0x7f, 0x23, 0x38, 0x0c, 0x42, 0xda, 0x44, 0x52, 0x0e, 0xea, 0xb4,
	0xca, 0x80, 0xf3, 0xdf, 0x23, 0x2b, 0x5f, 0x8c, 0x6f, 0x71, 0xaf, 0xea, 0x72, 0x6d, 0xfa, 0x9a,
	0x68, 0x65, 0xf4, 0xe5, 0x03, 0xc1, 0xb1, 0x21, 0x3a, 0x57, 0xce, 0x9f, 0x34, 0x3d, 0xec, 0x38,
	0x5d, 0xea, 0x0f, 0x9e, 0x56, 0xd6, 0x5d, 0xa8, 0xae, 0xf5, 0x4c, 0xc5, 0x47, 0xfa, 0x6c, 0xd5,
	0xe5, 0x1b, 0xfa, 0x33, 0x64, 0x33, 0xcf, 0x49, 0xa2, 0xb5, 0xd0, 0x2b, 0xa4, 0x83, 0xbe, 0xab,
	0xd1, 0x1a, 0xa7, 0x4c, 0x2d, 0x0b, 0x52, 0x5d, 0x84, 0xa9, 0x5a, 0x9b, 0xf2, 0xa5, 0x81, 0x63,
	0x02, 0x4f, 0x7f, 0x85, 0xc0, 0xf9, 0x2c, 0xbf, 0x2d, 0x8f, 0x38, 0x5d, 0x9f, 0xee, 0xa7, 0xdb,
	0x52, 0x3e, 0xcc, 0x6f, 0x47, 0x10, 0xfb, 0xa9, 0xdf, 0x1e, 0x0f, 0xef, 0xb2, 0x10, 0x15, 0x4f,
	0xe8, 0xf4, 0x25, 0x02, 0x71, 0x99, 0x5f, 0xa3, 0x2c, 0xf2, 0x52, 0xee, 0x0d, 0x10, 0x7d, 0x53,
	0x83, 0xa2, 0xf0, 0x54, 0x44, 0x72, 0xaa, 0xe4, 0x27, 0x24, 0x03, 0xd8, 0xee, 0x35, 0xb2, 0xee,
	0xf3, 0xdc, 0xad, 0xc2, 0x71, 0xd4, 0xe7, 0x7a, 0x66, 0x6d, 0x5c, 0xa9, 0xb1, 0x67, 0x25, 0xfd,
	0xbc, 0x2c, 0x62, 0x7b, 0x9f, 0xca, 0x3c, 0x19, 0x91, 0xbc, 0x69, 0xd5, 0x83, 0x92, 0x01, 0x18,
	0x6f, 0x10, 0x8c, 0x2f, 0x70, 0x8c, 0x47, 0x55, 0x83, 0x1f, 0xe0, 0xb5, 0x7e, 0x07, 0x68, 0x54,
	0x92, 0xe0, 0x9a, 0x53, 0xa9, 0x39, 0x9d, 0x36, 0x8d, 0xc8, 0xa0, 0xec, 0x33, 0x09, 0xa5, 0x37,
	0x25, 0x3d, 0x2e, 0x51, 0x7a, 0x53, 0xf2, 0x3b, 0x0b, 0x76, 0x99, 0x7c, 0x8e, 0x9e, 0x5f, 0x44,
	0xc3, 0x79, 0xba, 0x70, 0x42, 0xa1, 0xc8, 0xf9, 0xfc, 0x01, 0x93, 0xc0, 0xec, 0x63, 0x84, 0xb4,
	0x04, 0x2a, 0x9f, 0x5c, 0xa4, 0x25, 0x50, 0xfd, 0xa6, 0x41, 0x5f, 0x23, 0x78, 0xae, 0xc7, 0x2e,
	0x4b, 0xe4, 0x12, 0x3c, 0xab, 0xb4, 0xaf, 0xad, 0x9a, 0xe5, 0x19, 0xa6, 0xe9, 0xa7, 0x5d, 0x80,
	0xaf, 0x50, 0xae, 0xcf, 0xa4, 0x2d, 0xd3, 0x5c, 0xaf, 0x4a, 0xbc, 0xa6, 0xb9, 0x5e, 0x99, 0xfb,
	0xd4, 0xcb, 0x04, 0xe5, 0x8b, 0x54, 0xdd, 0x32, 0x13, 0xb6, 0x90, 0xd8, 0xa9, 0xdf, 0xd4, 0xa2,
	0xff, 0xe7, 0x66, 0xa5, 0x15, 0x9e, 0x88, 0x07, 0xf7, 0xd2, 0x89, 0x78, 0x70, 0xbf, 0xa9, 0xd1,
	0x97, 0xa3, 0x42, 0xea, 0x13, 0xa5, 0x2c, 0x64, 0x39, 0x23, 0x5b, 0xbe, 0xd0, 0xa7, 0x37, 0x72,
	0x34, 0x4c, 0xaa, 0x9c, 0xec, 0x6a, 0x50, 0xdd, 0xe3, 0x8e, 0x86, 0x2d, 0x06, 0x97, 0x03, 0x4e,
	0x94, 0xca, 0xd2, 0x5e, 0x4d, 0x4a, 0xd0, 0xd6, 0xd1, 0x3b, 0x30, 0x93, 0xca, 0x98, 0x4a, 0x09,
	0x8b, 0x6c, 0x36, 0x75, 0x80, 0xb0, 0x3d, 0x4d, 0xe0, 0x60, 0x02, 0x27, 0x6f, 0x57, 0x2d, 0xa6,
	0xb9, 0x25, 0x2c, 0x56, 0x1c, 0xe7, 0x46, 0x0e, 0xcc, 0xa4, 0x52, 0x7e, 0xa8, 0xef, 0xe4, 0xe5,
	0xc5, 0x5e, 0x4c, 0xc1, 0x8f, 0xe9, 0x02, 0x59, 0x7c, 0x4f, 0xe0, 0x87, 0x49, 0x71, 0x6d, 0xe2,
	0x39, 0x9c, 0x21, 0x47, 0x7b, 0xd7, 0xc7, 0x27, 0xec, 0xdc, 0xef, 0x9f, 0x08, 0x6b, 0x7c, 0x5f,
	0x03, 0x94, 0x4d, 0x55, 0xca, 0x61, 0x25, 0x55, 0x26, 0x73, 0xc0, 0x91, 0xdc, 0x25, 0x08, 0x0f,
	0x08, 0xc2, 0x42, 0x14, 0xa9, 0xa1, 0x07, 0xf3, 0x52, 0x0f, 0x67, 0x39, 0x0e, 0xd2, 0x24, 0x09,
	0x09, 0xab, 0x46, 0x13, 0x8f, 0xac, 0x88, 0x50, 0x94, 0xad, 0xbb, 0x46, 0xf7, 0x44, 0x08, 0x68,
	0x9d, 0x08, 0x01, 0x1f, 0x6a, 0x30, 0xbd, 0x25, 0x07, 0xea, 0x8f, 0x1d, 0x4f, 0xfa, 0xf2, 0x71,
	0xe3, 0x49, 0xbf, 0x2a, 0x5c, 0x1b, 0x3b, 0x84, 0x8a, 0xe4, 0xd4, 0x82, 0x63, 0xfb, 0xcc, 0xf7,
	0x1f, 0xc5, 0x67, 0xfe, 0x2b, 0x0d, 0xe6, 0x54, 0x2f, 0x7f, 0x24, 0x55, 0xdc, 0xe3, 0x69, 0xd0,
	0x00, 0x22, 0xbd, 0x45, 0xc0, 0xd8, 0xd4, 0xfd, 0x4b, 0x82, 0x96, 0xcc, 0xfd, 0x5b, 0xfd, 0xc9,
	0xc2, 0x96, 0xc4, 0xed, 0xe0, 0x4f, 0x7c, 0xea, 0xe8, 0xdb, 0x82, 0x75, 0xd7, 0x13, 0x79, 0x8f,
	0x57, 0x40, 0x4a, 0xeb, 0x2e, 0xfd, 0x32, 0x86, 0x05, 0x92, 0xda, 0x3f, 0x41, 0x20, 0xe9, 0x87,
	0xc4, 0xa9, 0x56, 0xbc, 0x4e, 0x91, 0x9d, 0x6a, 0xf5, 0xf3, 0x95, 0x61, 0x22, 0xc1, 0xce, 0x09,
	0x46, 0x82, 0xbf, 0xa7, 0xc1, 0xac, 0xe2, 0x65, 0x8a, 0xe4, 0x77, 0xa8, 0x5f, 0xae, 0x0c, 0x40,
	0xfa, 0xf3, 0x04, 0xa9, 0x1b, 0xc7, 0xd5, 0x23, 0x9c, 0xcf, 0x0f, 0x89, 0x4f, 0x78, 0x9c, 0x52,
	0x47, 0x7f, 0xa1, 0xc1, 0x79, 0x15, 0xbd, 0x36, 0x9d, 0x10, 0xfb, 0x8e, 0x61, 0x9f, 0x14, 0x61,
	0xbd, 0x93, 0x23, 0x6c, 0x79, 0xec, 0xab, 0x1f, 0x7e, 0x92, 0xff, 0xe0, 0x68, 0xb5, 0xf4, 0xd1,
	0xc7, 0x8b, 0xda, 0x3f, 0x7d, 0xbc, 0xa8, 0xfd, 0xf8, 0xe3, 0x45, 0xed, 0x1b, 0xff, 0xb9, 0xf8,
	0xd8, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0xb0, 0xe6, 0x4c, 0xb1, 0x89, 0x52, 0x00, 0x00,
}
