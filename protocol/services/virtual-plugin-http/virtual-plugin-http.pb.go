// Code generated by protoc-gen-go. DO NOT EDIT.
// source: virtual-plugin-http/virtual-plugin-http.proto

package virtual_plugin_http // import "golang.52tt.com/protocol/services/virtual-plugin-http"

/*
namespace
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// API-获取礼物配置
type GetGiftConfsReq struct {
	ChnSecret            string   `protobuf:"bytes,1,opt,name=chn_secret,json=chnSecret,proto3" json:"chn_secret"`
	ReqTime              string   `protobuf:"bytes,2,opt,name=req_time,json=reqTime,proto3" json:"req_time"`
	Sign                 string   `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGiftConfsReq) Reset()         { *m = GetGiftConfsReq{} }
func (m *GetGiftConfsReq) String() string { return proto.CompactTextString(m) }
func (*GetGiftConfsReq) ProtoMessage()    {}
func (*GetGiftConfsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{0}
}
func (m *GetGiftConfsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftConfsReq.Unmarshal(m, b)
}
func (m *GetGiftConfsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftConfsReq.Marshal(b, m, deterministic)
}
func (dst *GetGiftConfsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftConfsReq.Merge(dst, src)
}
func (m *GetGiftConfsReq) XXX_Size() int {
	return xxx_messageInfo_GetGiftConfsReq.Size(m)
}
func (m *GetGiftConfsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftConfsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftConfsReq proto.InternalMessageInfo

func (m *GetGiftConfsReq) GetChnSecret() string {
	if m != nil {
		return m.ChnSecret
	}
	return ""
}

func (m *GetGiftConfsReq) GetReqTime() string {
	if m != nil {
		return m.ReqTime
	}
	return ""
}

func (m *GetGiftConfsReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type GiftConf struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name"`
	Price                uint32   `protobuf:"varint,3,opt,name=price,proto3" json:"price"`
	GiftUrl              string   `protobuf:"bytes,4,opt,name=gift_url,json=giftUrl,proto3" json:"gift_url"`
	Type                 uint32   `protobuf:"varint,5,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftConf) Reset()         { *m = GiftConf{} }
func (m *GiftConf) String() string { return proto.CompactTextString(m) }
func (*GiftConf) ProtoMessage()    {}
func (*GiftConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{1}
}
func (m *GiftConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftConf.Unmarshal(m, b)
}
func (m *GiftConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftConf.Marshal(b, m, deterministic)
}
func (dst *GiftConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftConf.Merge(dst, src)
}
func (m *GiftConf) XXX_Size() int {
	return xxx_messageInfo_GiftConf.Size(m)
}
func (m *GiftConf) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftConf.DiscardUnknown(m)
}

var xxx_messageInfo_GiftConf proto.InternalMessageInfo

func (m *GiftConf) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *GiftConf) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *GiftConf) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GiftConf) GetGiftUrl() string {
	if m != nil {
		return m.GiftUrl
	}
	return ""
}

func (m *GiftConf) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetGiftConfsRsp struct {
	ErrCode              uint32      `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code"`
	ErrMsg               string      `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg"`
	Data                 []*GiftConf `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGiftConfsRsp) Reset()         { *m = GetGiftConfsRsp{} }
func (m *GetGiftConfsRsp) String() string { return proto.CompactTextString(m) }
func (*GetGiftConfsRsp) ProtoMessage()    {}
func (*GetGiftConfsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{2}
}
func (m *GetGiftConfsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftConfsRsp.Unmarshal(m, b)
}
func (m *GetGiftConfsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftConfsRsp.Marshal(b, m, deterministic)
}
func (dst *GetGiftConfsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftConfsRsp.Merge(dst, src)
}
func (m *GetGiftConfsRsp) XXX_Size() int {
	return xxx_messageInfo_GetGiftConfsRsp.Size(m)
}
func (m *GetGiftConfsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftConfsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftConfsRsp proto.InternalMessageInfo

func (m *GetGiftConfsRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetGiftConfsRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetGiftConfsRsp) GetData() []*GiftConf {
	if m != nil {
		return m.Data
	}
	return nil
}

// API -获取主播信息
type GetAnchorReq struct {
	ChnSecret            string   `protobuf:"bytes,1,opt,name=chn_secret,json=chnSecret,proto3" json:"chn_secret"`
	ReqTime              string   `protobuf:"bytes,2,opt,name=req_time,json=reqTime,proto3" json:"req_time"`
	Sign                 string   `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorReq) Reset()         { *m = GetAnchorReq{} }
func (m *GetAnchorReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorReq) ProtoMessage()    {}
func (*GetAnchorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{3}
}
func (m *GetAnchorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorReq.Unmarshal(m, b)
}
func (m *GetAnchorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorReq.Merge(dst, src)
}
func (m *GetAnchorReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorReq.Size(m)
}
func (m *GetAnchorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorReq proto.InternalMessageInfo

func (m *GetAnchorReq) GetChnSecret() string {
	if m != nil {
		return m.ChnSecret
	}
	return ""
}

func (m *GetAnchorReq) GetReqTime() string {
	if m != nil {
		return m.ReqTime
	}
	return ""
}

func (m *GetAnchorReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type AnchorInfo struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	HeadUrl              string   `protobuf:"bytes,3,opt,name=head_url,json=headUrl,proto3" json:"head_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorInfo) Reset()         { *m = AnchorInfo{} }
func (m *AnchorInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorInfo) ProtoMessage()    {}
func (*AnchorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{4}
}
func (m *AnchorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorInfo.Unmarshal(m, b)
}
func (m *AnchorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorInfo.Merge(dst, src)
}
func (m *AnchorInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorInfo.Size(m)
}
func (m *AnchorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorInfo proto.InternalMessageInfo

func (m *AnchorInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AnchorInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AnchorInfo) GetHeadUrl() string {
	if m != nil {
		return m.HeadUrl
	}
	return ""
}

type GetAnchorRsp struct {
	ErrCode              uint32      `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code"`
	ErrMsg               string      `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg"`
	Data                 *AnchorInfo `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAnchorRsp) Reset()         { *m = GetAnchorRsp{} }
func (m *GetAnchorRsp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorRsp) ProtoMessage()    {}
func (*GetAnchorRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{5}
}
func (m *GetAnchorRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorRsp.Unmarshal(m, b)
}
func (m *GetAnchorRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorRsp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorRsp.Merge(dst, src)
}
func (m *GetAnchorRsp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorRsp.Size(m)
}
func (m *GetAnchorRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorRsp proto.InternalMessageInfo

func (m *GetAnchorRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetAnchorRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetAnchorRsp) GetData() *AnchorInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

// API -获取普通表情
type GetEmojiReq struct {
	ChnSecret            string   `protobuf:"bytes,1,opt,name=chn_secret,json=chnSecret,proto3" json:"chn_secret"`
	ReqTime              string   `protobuf:"bytes,2,opt,name=req_time,json=reqTime,proto3" json:"req_time"`
	Sign                 string   `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEmojiReq) Reset()         { *m = GetEmojiReq{} }
func (m *GetEmojiReq) String() string { return proto.CompactTextString(m) }
func (*GetEmojiReq) ProtoMessage()    {}
func (*GetEmojiReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{6}
}
func (m *GetEmojiReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmojiReq.Unmarshal(m, b)
}
func (m *GetEmojiReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmojiReq.Marshal(b, m, deterministic)
}
func (dst *GetEmojiReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmojiReq.Merge(dst, src)
}
func (m *GetEmojiReq) XXX_Size() int {
	return xxx_messageInfo_GetEmojiReq.Size(m)
}
func (m *GetEmojiReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmojiReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmojiReq proto.InternalMessageInfo

func (m *GetEmojiReq) GetChnSecret() string {
	if m != nil {
		return m.ChnSecret
	}
	return ""
}

func (m *GetEmojiReq) GetReqTime() string {
	if m != nil {
		return m.ReqTime
	}
	return ""
}

func (m *GetEmojiReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type EmojiConf struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmojiConf) Reset()         { *m = EmojiConf{} }
func (m *EmojiConf) String() string { return proto.CompactTextString(m) }
func (*EmojiConf) ProtoMessage()    {}
func (*EmojiConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{7}
}
func (m *EmojiConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmojiConf.Unmarshal(m, b)
}
func (m *EmojiConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmojiConf.Marshal(b, m, deterministic)
}
func (dst *EmojiConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmojiConf.Merge(dst, src)
}
func (m *EmojiConf) XXX_Size() int {
	return xxx_messageInfo_EmojiConf.Size(m)
}
func (m *EmojiConf) XXX_DiscardUnknown() {
	xxx_messageInfo_EmojiConf.DiscardUnknown(m)
}

var xxx_messageInfo_EmojiConf proto.InternalMessageInfo

func (m *EmojiConf) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *EmojiConf) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type GetEmojiRsp struct {
	ErrCode              uint32       `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code"`
	ErrMsg               string       `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg"`
	Data                 []*EmojiConf `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetEmojiRsp) Reset()         { *m = GetEmojiRsp{} }
func (m *GetEmojiRsp) String() string { return proto.CompactTextString(m) }
func (*GetEmojiRsp) ProtoMessage()    {}
func (*GetEmojiRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{8}
}
func (m *GetEmojiRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEmojiRsp.Unmarshal(m, b)
}
func (m *GetEmojiRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEmojiRsp.Marshal(b, m, deterministic)
}
func (dst *GetEmojiRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEmojiRsp.Merge(dst, src)
}
func (m *GetEmojiRsp) XXX_Size() int {
	return xxx_messageInfo_GetEmojiRsp.Size(m)
}
func (m *GetEmojiRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEmojiRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEmojiRsp proto.InternalMessageInfo

func (m *GetEmojiRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetEmojiRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetEmojiRsp) GetData() []*EmojiConf {
	if m != nil {
		return m.Data
	}
	return nil
}

// API -定时轮询房间消息
type ChannelMsg struct {
	SenderId             string   `protobuf:"bytes,1,opt,name=sender_id,json=senderId,proto3" json:"sender_id"`
	SenderName           string   `protobuf:"bytes,2,opt,name=sender_name,json=senderName,proto3" json:"sender_name"`
	SenderHead           string   `protobuf:"bytes,3,opt,name=sender_head,json=senderHead,proto3" json:"sender_head"`
	MsgType              uint32   `protobuf:"varint,4,opt,name=msg_type,json=msgType,proto3" json:"msg_type"`
	Content              string   `protobuf:"bytes,5,opt,name=content,proto3" json:"content"`
	SendTime             uint32   `protobuf:"varint,6,opt,name=send_time,json=sendTime,proto3" json:"send_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelMsg) Reset()         { *m = ChannelMsg{} }
func (m *ChannelMsg) String() string { return proto.CompactTextString(m) }
func (*ChannelMsg) ProtoMessage()    {}
func (*ChannelMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{9}
}
func (m *ChannelMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMsg.Unmarshal(m, b)
}
func (m *ChannelMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMsg.Marshal(b, m, deterministic)
}
func (dst *ChannelMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMsg.Merge(dst, src)
}
func (m *ChannelMsg) XXX_Size() int {
	return xxx_messageInfo_ChannelMsg.Size(m)
}
func (m *ChannelMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMsg proto.InternalMessageInfo

func (m *ChannelMsg) GetSenderId() string {
	if m != nil {
		return m.SenderId
	}
	return ""
}

func (m *ChannelMsg) GetSenderName() string {
	if m != nil {
		return m.SenderName
	}
	return ""
}

func (m *ChannelMsg) GetSenderHead() string {
	if m != nil {
		return m.SenderHead
	}
	return ""
}

func (m *ChannelMsg) GetMsgType() uint32 {
	if m != nil {
		return m.MsgType
	}
	return 0
}

func (m *ChannelMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ChannelMsg) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type ChannelMsgData struct {
	LastSeq              uint32        `protobuf:"varint,1,opt,name=last_seq,json=lastSeq,proto3" json:"last_seq"`
	MsgList              []*ChannelMsg `protobuf:"bytes,2,rep,name=msg_list,json=msgList,proto3" json:"msg_list"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChannelMsgData) Reset()         { *m = ChannelMsgData{} }
func (m *ChannelMsgData) String() string { return proto.CompactTextString(m) }
func (*ChannelMsgData) ProtoMessage()    {}
func (*ChannelMsgData) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{10}
}
func (m *ChannelMsgData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelMsgData.Unmarshal(m, b)
}
func (m *ChannelMsgData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelMsgData.Marshal(b, m, deterministic)
}
func (dst *ChannelMsgData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelMsgData.Merge(dst, src)
}
func (m *ChannelMsgData) XXX_Size() int {
	return xxx_messageInfo_ChannelMsgData.Size(m)
}
func (m *ChannelMsgData) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelMsgData.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelMsgData proto.InternalMessageInfo

func (m *ChannelMsgData) GetLastSeq() uint32 {
	if m != nil {
		return m.LastSeq
	}
	return 0
}

func (m *ChannelMsgData) GetMsgList() []*ChannelMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type GetChannelMsgReq struct {
	ChnSecret            string   `protobuf:"bytes,1,opt,name=chn_secret,json=chnSecret,proto3" json:"chn_secret"`
	ReqTime              string   `protobuf:"bytes,2,opt,name=req_time,json=reqTime,proto3" json:"req_time"`
	Sign                 string   `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign"`
	LastSeq              uint32   `protobuf:"varint,4,opt,name=last_seq,json=lastSeq,proto3" json:"last_seq"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelMsgReq) Reset()         { *m = GetChannelMsgReq{} }
func (m *GetChannelMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMsgReq) ProtoMessage()    {}
func (*GetChannelMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{11}
}
func (m *GetChannelMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMsgReq.Unmarshal(m, b)
}
func (m *GetChannelMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMsgReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMsgReq.Merge(dst, src)
}
func (m *GetChannelMsgReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMsgReq.Size(m)
}
func (m *GetChannelMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMsgReq proto.InternalMessageInfo

func (m *GetChannelMsgReq) GetChnSecret() string {
	if m != nil {
		return m.ChnSecret
	}
	return ""
}

func (m *GetChannelMsgReq) GetReqTime() string {
	if m != nil {
		return m.ReqTime
	}
	return ""
}

func (m *GetChannelMsgReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func (m *GetChannelMsgReq) GetLastSeq() uint32 {
	if m != nil {
		return m.LastSeq
	}
	return 0
}

type GetChannelMsgRsp struct {
	ErrCode              uint32          `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code"`
	ErrMsg               string          `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg"`
	Data                 *ChannelMsgData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetChannelMsgRsp) Reset()         { *m = GetChannelMsgRsp{} }
func (m *GetChannelMsgRsp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMsgRsp) ProtoMessage()    {}
func (*GetChannelMsgRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{12}
}
func (m *GetChannelMsgRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMsgRsp.Unmarshal(m, b)
}
func (m *GetChannelMsgRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMsgRsp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMsgRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMsgRsp.Merge(dst, src)
}
func (m *GetChannelMsgRsp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMsgRsp.Size(m)
}
func (m *GetChannelMsgRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMsgRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMsgRsp proto.InternalMessageInfo

func (m *GetChannelMsgRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetChannelMsgRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetChannelMsgRsp) GetData() *ChannelMsgData {
	if m != nil {
		return m.Data
	}
	return nil
}

// API -定时轮询送礼消息
type SendGiftMsg struct {
	SenderId             string   `protobuf:"bytes,1,opt,name=sender_id,json=senderId,proto3" json:"sender_id"`
	SenderName           string   `protobuf:"bytes,2,opt,name=sender_name,json=senderName,proto3" json:"sender_name"`
	SenderHead           string   `protobuf:"bytes,3,opt,name=sender_head,json=senderHead,proto3" json:"sender_head"`
	ReceiverId           string   `protobuf:"bytes,4,opt,name=receiver_id,json=receiverId,proto3" json:"receiver_id"`
	ReceiverName         string   `protobuf:"bytes,5,opt,name=receiver_name,json=receiverName,proto3" json:"receiver_name"`
	ReceiverHead         string   `protobuf:"bytes,6,opt,name=receiver_head,json=receiverHead,proto3" json:"receiver_head"`
	GiftId               uint32   `protobuf:"varint,7,opt,name=gift_id,json=giftId,proto3" json:"gift_id"`
	GiftNum              uint32   `protobuf:"varint,8,opt,name=gift_num,json=giftNum,proto3" json:"gift_num"`
	SendTime             uint32   `protobuf:"varint,9,opt,name=send_time,json=sendTime,proto3" json:"send_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGiftMsg) Reset()         { *m = SendGiftMsg{} }
func (m *SendGiftMsg) String() string { return proto.CompactTextString(m) }
func (*SendGiftMsg) ProtoMessage()    {}
func (*SendGiftMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{13}
}
func (m *SendGiftMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGiftMsg.Unmarshal(m, b)
}
func (m *SendGiftMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGiftMsg.Marshal(b, m, deterministic)
}
func (dst *SendGiftMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGiftMsg.Merge(dst, src)
}
func (m *SendGiftMsg) XXX_Size() int {
	return xxx_messageInfo_SendGiftMsg.Size(m)
}
func (m *SendGiftMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGiftMsg.DiscardUnknown(m)
}

var xxx_messageInfo_SendGiftMsg proto.InternalMessageInfo

func (m *SendGiftMsg) GetSenderId() string {
	if m != nil {
		return m.SenderId
	}
	return ""
}

func (m *SendGiftMsg) GetSenderName() string {
	if m != nil {
		return m.SenderName
	}
	return ""
}

func (m *SendGiftMsg) GetSenderHead() string {
	if m != nil {
		return m.SenderHead
	}
	return ""
}

func (m *SendGiftMsg) GetReceiverId() string {
	if m != nil {
		return m.ReceiverId
	}
	return ""
}

func (m *SendGiftMsg) GetReceiverName() string {
	if m != nil {
		return m.ReceiverName
	}
	return ""
}

func (m *SendGiftMsg) GetReceiverHead() string {
	if m != nil {
		return m.ReceiverHead
	}
	return ""
}

func (m *SendGiftMsg) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *SendGiftMsg) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *SendGiftMsg) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

type SendGiftData struct {
	LastTime             uint32         `protobuf:"varint,1,opt,name=last_time,json=lastTime,proto3" json:"last_time"`
	MsgList              []*SendGiftMsg `protobuf:"bytes,2,rep,name=msg_list,json=msgList,proto3" json:"msg_list"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SendGiftData) Reset()         { *m = SendGiftData{} }
func (m *SendGiftData) String() string { return proto.CompactTextString(m) }
func (*SendGiftData) ProtoMessage()    {}
func (*SendGiftData) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{14}
}
func (m *SendGiftData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGiftData.Unmarshal(m, b)
}
func (m *SendGiftData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGiftData.Marshal(b, m, deterministic)
}
func (dst *SendGiftData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGiftData.Merge(dst, src)
}
func (m *SendGiftData) XXX_Size() int {
	return xxx_messageInfo_SendGiftData.Size(m)
}
func (m *SendGiftData) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGiftData.DiscardUnknown(m)
}

var xxx_messageInfo_SendGiftData proto.InternalMessageInfo

func (m *SendGiftData) GetLastTime() uint32 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

func (m *SendGiftData) GetMsgList() []*SendGiftMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type GetGiftMsgReq struct {
	ChnSecret            string   `protobuf:"bytes,1,opt,name=chn_secret,json=chnSecret,proto3" json:"chn_secret"`
	ReqTime              string   `protobuf:"bytes,2,opt,name=req_time,json=reqTime,proto3" json:"req_time"`
	Sign                 string   `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign"`
	LastTime             uint32   `protobuf:"varint,4,opt,name=last_time,json=lastTime,proto3" json:"last_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGiftMsgReq) Reset()         { *m = GetGiftMsgReq{} }
func (m *GetGiftMsgReq) String() string { return proto.CompactTextString(m) }
func (*GetGiftMsgReq) ProtoMessage()    {}
func (*GetGiftMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{15}
}
func (m *GetGiftMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftMsgReq.Unmarshal(m, b)
}
func (m *GetGiftMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftMsgReq.Marshal(b, m, deterministic)
}
func (dst *GetGiftMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftMsgReq.Merge(dst, src)
}
func (m *GetGiftMsgReq) XXX_Size() int {
	return xxx_messageInfo_GetGiftMsgReq.Size(m)
}
func (m *GetGiftMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftMsgReq proto.InternalMessageInfo

func (m *GetGiftMsgReq) GetChnSecret() string {
	if m != nil {
		return m.ChnSecret
	}
	return ""
}

func (m *GetGiftMsgReq) GetReqTime() string {
	if m != nil {
		return m.ReqTime
	}
	return ""
}

func (m *GetGiftMsgReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

func (m *GetGiftMsgReq) GetLastTime() uint32 {
	if m != nil {
		return m.LastTime
	}
	return 0
}

type GetGiftMsgRsp struct {
	ErrCode              uint32        `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code"`
	ErrMsg               string        `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg"`
	Data                 *SendGiftData `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetGiftMsgRsp) Reset()         { *m = GetGiftMsgRsp{} }
func (m *GetGiftMsgRsp) String() string { return proto.CompactTextString(m) }
func (*GetGiftMsgRsp) ProtoMessage()    {}
func (*GetGiftMsgRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{16}
}
func (m *GetGiftMsgRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGiftMsgRsp.Unmarshal(m, b)
}
func (m *GetGiftMsgRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGiftMsgRsp.Marshal(b, m, deterministic)
}
func (dst *GetGiftMsgRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGiftMsgRsp.Merge(dst, src)
}
func (m *GetGiftMsgRsp) XXX_Size() int {
	return xxx_messageInfo_GetGiftMsgRsp.Size(m)
}
func (m *GetGiftMsgRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGiftMsgRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGiftMsgRsp proto.InternalMessageInfo

func (m *GetGiftMsgRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetGiftMsgRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetGiftMsgRsp) GetData() *SendGiftData {
	if m != nil {
		return m.Data
	}
	return nil
}

// API-定时轮询房间热度值
type ChannelHot struct {
	Hot                  uint64   `protobuf:"varint,1,opt,name=hot,proto3" json:"hot"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelHot) Reset()         { *m = ChannelHot{} }
func (m *ChannelHot) String() string { return proto.CompactTextString(m) }
func (*ChannelHot) ProtoMessage()    {}
func (*ChannelHot) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{17}
}
func (m *ChannelHot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelHot.Unmarshal(m, b)
}
func (m *ChannelHot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelHot.Marshal(b, m, deterministic)
}
func (dst *ChannelHot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelHot.Merge(dst, src)
}
func (m *ChannelHot) XXX_Size() int {
	return xxx_messageInfo_ChannelHot.Size(m)
}
func (m *ChannelHot) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelHot.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelHot proto.InternalMessageInfo

func (m *ChannelHot) GetHot() uint64 {
	if m != nil {
		return m.Hot
	}
	return 0
}

type GetChannelHotReq struct {
	ChnSecret            string   `protobuf:"bytes,1,opt,name=chn_secret,json=chnSecret,proto3" json:"chn_secret"`
	ReqTime              string   `protobuf:"bytes,2,opt,name=req_time,json=reqTime,proto3" json:"req_time"`
	Sign                 string   `protobuf:"bytes,3,opt,name=sign,proto3" json:"sign"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelHotReq) Reset()         { *m = GetChannelHotReq{} }
func (m *GetChannelHotReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelHotReq) ProtoMessage()    {}
func (*GetChannelHotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{18}
}
func (m *GetChannelHotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelHotReq.Unmarshal(m, b)
}
func (m *GetChannelHotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelHotReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelHotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelHotReq.Merge(dst, src)
}
func (m *GetChannelHotReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelHotReq.Size(m)
}
func (m *GetChannelHotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelHotReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelHotReq proto.InternalMessageInfo

func (m *GetChannelHotReq) GetChnSecret() string {
	if m != nil {
		return m.ChnSecret
	}
	return ""
}

func (m *GetChannelHotReq) GetReqTime() string {
	if m != nil {
		return m.ReqTime
	}
	return ""
}

func (m *GetChannelHotReq) GetSign() string {
	if m != nil {
		return m.Sign
	}
	return ""
}

type GetChannelHotRsp struct {
	ErrCode              uint32      `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code"`
	ErrMsg               string      `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg"`
	Data                 *ChannelHot `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetChannelHotRsp) Reset()         { *m = GetChannelHotRsp{} }
func (m *GetChannelHotRsp) String() string { return proto.CompactTextString(m) }
func (*GetChannelHotRsp) ProtoMessage()    {}
func (*GetChannelHotRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571, []int{19}
}
func (m *GetChannelHotRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelHotRsp.Unmarshal(m, b)
}
func (m *GetChannelHotRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelHotRsp.Marshal(b, m, deterministic)
}
func (dst *GetChannelHotRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelHotRsp.Merge(dst, src)
}
func (m *GetChannelHotRsp) XXX_Size() int {
	return xxx_messageInfo_GetChannelHotRsp.Size(m)
}
func (m *GetChannelHotRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelHotRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelHotRsp proto.InternalMessageInfo

func (m *GetChannelHotRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetChannelHotRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetChannelHotRsp) GetData() *ChannelHot {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*GetGiftConfsReq)(nil), "virtual_plugin_http.GetGiftConfsReq")
	proto.RegisterType((*GiftConf)(nil), "virtual_plugin_http.GiftConf")
	proto.RegisterType((*GetGiftConfsRsp)(nil), "virtual_plugin_http.GetGiftConfsRsp")
	proto.RegisterType((*GetAnchorReq)(nil), "virtual_plugin_http.GetAnchorReq")
	proto.RegisterType((*AnchorInfo)(nil), "virtual_plugin_http.AnchorInfo")
	proto.RegisterType((*GetAnchorRsp)(nil), "virtual_plugin_http.GetAnchorRsp")
	proto.RegisterType((*GetEmojiReq)(nil), "virtual_plugin_http.GetEmojiReq")
	proto.RegisterType((*EmojiConf)(nil), "virtual_plugin_http.EmojiConf")
	proto.RegisterType((*GetEmojiRsp)(nil), "virtual_plugin_http.GetEmojiRsp")
	proto.RegisterType((*ChannelMsg)(nil), "virtual_plugin_http.ChannelMsg")
	proto.RegisterType((*ChannelMsgData)(nil), "virtual_plugin_http.ChannelMsgData")
	proto.RegisterType((*GetChannelMsgReq)(nil), "virtual_plugin_http.GetChannelMsgReq")
	proto.RegisterType((*GetChannelMsgRsp)(nil), "virtual_plugin_http.GetChannelMsgRsp")
	proto.RegisterType((*SendGiftMsg)(nil), "virtual_plugin_http.SendGiftMsg")
	proto.RegisterType((*SendGiftData)(nil), "virtual_plugin_http.SendGiftData")
	proto.RegisterType((*GetGiftMsgReq)(nil), "virtual_plugin_http.GetGiftMsgReq")
	proto.RegisterType((*GetGiftMsgRsp)(nil), "virtual_plugin_http.GetGiftMsgRsp")
	proto.RegisterType((*ChannelHot)(nil), "virtual_plugin_http.ChannelHot")
	proto.RegisterType((*GetChannelHotReq)(nil), "virtual_plugin_http.GetChannelHotReq")
	proto.RegisterType((*GetChannelHotRsp)(nil), "virtual_plugin_http.GetChannelHotRsp")
}

func init() {
	proto.RegisterFile("virtual-plugin-http/virtual-plugin-http.proto", fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571)
}

var fileDescriptor_virtual_plugin_http_1c8b17c35a0ad571 = []byte{
	// 785 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x56, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0x86, 0x2c, 0x59, 0x12, 0x47, 0x56, 0x6b, 0xb0, 0x05, 0xca, 0xc2, 0xf0, 0x4f, 0xe9, 0x8b,
	0x2f, 0x96, 0x61, 0x19, 0x86, 0x81, 0xf6, 0xd4, 0xba, 0x85, 0x2c, 0xa0, 0x49, 0x00, 0xda, 0xbe,
	0x24, 0x01, 0x18, 0x86, 0x1c, 0x91, 0x0c, 0xc8, 0xa5, 0xb4, 0xbb, 0x72, 0xe2, 0xf8, 0x90, 0x53,
	0xde, 0x25, 0x2f, 0x91, 0x77, 0x0b, 0x76, 0x96, 0x94, 0x48, 0x45, 0x48, 0x00, 0x41, 0xce, 0x6d,
	0x77, 0x66, 0x38, 0xf3, 0xcd, 0x37, 0xdf, 0xee, 0x12, 0x8e, 0xef, 0x62, 0x2e, 0xa7, 0x5e, 0x72,
	0x3c, 0x4e, 0xa6, 0x61, 0xcc, 0x8e, 0x23, 0x29, 0xc7, 0x27, 0x4b, 0x6c, 0xbd, 0x31, 0xcf, 0x64,
	0x66, 0xfe, 0x92, 0xbb, 0x5c, 0xed, 0x72, 0x95, 0xcb, 0x76, 0xe1, 0xe7, 0x01, 0xca, 0x41, 0x3c,
	0x92, 0x97, 0x19, 0x1b, 0x09, 0x07, 0x27, 0xe6, 0x2e, 0x80, 0x1f, 0x31, 0x57, 0xa0, 0xcf, 0x51,
	0x5a, 0xb5, 0x83, 0xda, 0x91, 0xe1, 0x18, 0x7e, 0xc4, 0xae, 0xc9, 0x60, 0xfe, 0x0e, 0x6d, 0x8e,
	0x13, 0x57, 0xc6, 0x29, 0x5a, 0x1b, 0xe4, 0x6c, 0x71, 0x9c, 0xdc, 0xc4, 0x29, 0x9a, 0x26, 0x34,
	0x44, 0x1c, 0x32, 0xab, 0x4e, 0x66, 0x5a, 0xdb, 0x1f, 0x6b, 0xd0, 0x2e, 0xd2, 0x9b, 0xbf, 0x41,
	0x2b, 0x8c, 0x47, 0xd2, 0x8d, 0x03, 0xca, 0xdb, 0x75, 0x9a, 0x6a, 0x3b, 0x0c, 0xcc, 0x1d, 0x30,
	0xc8, 0xc1, 0xbc, 0x59, 0xd6, 0xb6, 0x32, 0x3c, 0xf5, 0x52, 0x34, 0x7f, 0x85, 0xcd, 0x31, 0x8f,
	0x7d, 0xa4, 0xbc, 0x5d, 0x47, 0x6f, 0x14, 0x0e, 0xfa, 0x64, 0xca, 0x13, 0xab, 0xa1, 0x71, 0xa8,
	0xfd, 0x2d, 0x4f, 0x14, 0x0e, 0x79, 0x3f, 0x46, 0x6b, 0x93, 0xe2, 0x69, 0x6d, 0xbf, 0x5b, 0x68,
	0x54, 0x8c, 0x55, 0x06, 0xe4, 0xdc, 0xf5, 0xb3, 0x00, 0x73, 0x38, 0x2d, 0xe4, 0xfc, 0x32, 0x0b,
	0x50, 0x01, 0x55, 0xae, 0x54, 0x84, 0x39, 0x9a, 0x26, 0x72, 0xfe, 0x44, 0x84, 0xe6, 0x29, 0x34,
	0x02, 0x4f, 0x7a, 0x56, 0xfd, 0xa0, 0x7e, 0xd4, 0xe9, 0xef, 0xf6, 0x96, 0x70, 0xda, 0x2b, 0x8a,
	0x38, 0x14, 0x6a, 0xbf, 0x84, 0xad, 0x01, 0xca, 0xbf, 0x99, 0x1f, 0x65, 0x7c, 0xfd, 0xfc, 0x3e,
	0x03, 0xd0, 0xa9, 0x87, 0x6c, 0x94, 0x51, 0xe7, 0x32, 0x67, 0xd7, 0x70, 0x68, 0xad, 0x6c, 0x25,
	0x5a, 0x69, 0xad, 0x8a, 0x44, 0xe8, 0x05, 0x44, 0x9e, 0xce, 0xd6, 0x52, 0xfb, 0x5b, 0x9e, 0xd8,
	0x6f, 0xcb, 0x70, 0x57, 0x64, 0xe9, 0x6c, 0xc6, 0x52, 0xed, 0xa8, 0xd3, 0xdf, 0x5f, 0xca, 0xd2,
	0x1c, 0x75, 0xce, 0xd3, 0x0b, 0xe8, 0x0c, 0x50, 0xfe, 0x97, 0x66, 0x6f, 0xe2, 0xf5, 0xd3, 0x74,
	0x0a, 0x06, 0x65, 0x26, 0x19, 0x16, 0x8c, 0xd4, 0x4a, 0x8c, 0x6c, 0x43, 0x5d, 0x91, 0xa1, 0x53,
	0xa9, 0xa5, 0x3d, 0x2d, 0xe1, 0x59, 0x91, 0x87, 0x7e, 0x45, 0x2d, 0x7b, 0x4b, 0x79, 0x98, 0xc1,
	0xca, 0x69, 0xf8, 0x5c, 0x03, 0xb8, 0x8c, 0x3c, 0xc6, 0x30, 0x51, 0x29, 0x76, 0xc0, 0x10, 0xc8,
	0x02, 0xe4, 0xee, 0x6c, 0xac, 0x6d, 0x6d, 0x18, 0x06, 0xe6, 0x3e, 0x74, 0x72, 0x67, 0x69, 0xc2,
	0xa0, 0x4d, 0x74, 0x74, 0xe6, 0x01, 0x6a, 0xbc, 0x39, 0x23, 0x79, 0xc0, 0x15, 0x7a, 0x81, 0xea,
	0x2a, 0x15, 0xa1, 0x4b, 0xc7, 0xa5, 0xa1, 0xbb, 0x4a, 0x45, 0x78, 0x73, 0x3f, 0x46, 0xd3, 0x82,
	0x96, 0x9f, 0x31, 0x89, 0x4c, 0xd2, 0x41, 0x32, 0x9c, 0x62, 0x5b, 0x60, 0xd2, 0xe4, 0x37, 0xe9,
	0x2b, 0xc2, 0xa4, 0xd8, 0xb7, 0x43, 0xf8, 0x69, 0x0e, 0xff, 0x5f, 0x4f, 0x7a, 0xaa, 0x46, 0xe2,
	0x09, 0xe9, 0x0a, 0x9c, 0x14, 0xcc, 0xa9, 0xfd, 0x35, 0x4e, 0xcc, 0x3f, 0x75, 0xf9, 0x24, 0x16,
	0xd2, 0xda, 0x20, 0x92, 0x96, 0x8b, 0x65, 0x9e, 0x91, 0xf0, 0xfd, 0x1f, 0x0b, 0x69, 0x3f, 0xc0,
	0xf6, 0x00, 0x65, 0xc9, 0xb3, 0x6e, 0xd1, 0x54, 0x80, 0x37, 0x2a, 0xc0, 0xed, 0x0f, 0x8b, 0xc5,
	0x57, 0x54, 0xc8, 0x45, 0xe5, 0xa4, 0x1c, 0x7e, 0xa7, 0x79, 0x45, 0x67, 0x2e, 0x93, 0x4f, 0x1b,
	0xd0, 0xb9, 0x46, 0x16, 0xa8, 0xcb, 0xe6, 0x07, 0xe8, 0x64, 0x1f, 0x3a, 0x1c, 0x7d, 0x8c, 0xef,
	0x74, 0x01, 0x7d, 0xe1, 0x42, 0x61, 0x1a, 0x06, 0xe6, 0x21, 0x74, 0x67, 0x01, 0x54, 0x44, 0x6b,
	0x66, 0xab, 0x30, 0x52, 0x99, 0x72, 0x10, 0x15, 0x6a, 0x56, 0x83, 0xa8, 0x54, 0xe9, 0x91, 0x68,
	0x55, 0x1e, 0x89, 0xe2, 0xc6, 0x67, 0xd3, 0xd4, 0x6a, 0x6b, 0x7e, 0xe9, 0x8d, 0x98, 0xa6, 0x55,
	0x45, 0x1a, 0x0b, 0x8a, 0x8c, 0x60, 0xab, 0x60, 0x8a, 0xf4, 0xb8, 0x03, 0x06, 0x8d, 0x95, 0x82,
	0xf5, 0xa0, 0x68, 0xce, 0xa4, 0x83, 0xbf, 0xbe, 0x52, 0xe4, 0xc1, 0xd2, 0xa1, 0x94, 0xb8, 0x9f,
	0x4b, 0xf2, 0x3d, 0x74, 0xf3, 0x47, 0xe6, 0x51, 0xf4, 0x58, 0x01, 0xde, 0xa8, 0x02, 0x5f, 0xa8,
	0xbd, 0xa2, 0x1c, 0xcf, 0x2b, 0x72, 0xfc, 0xe3, 0x9b, 0x9d, 0x97, 0xc4, 0xb8, 0x37, 0xbb, 0xb2,
	0xae, 0x32, 0xa9, 0xae, 0xd2, 0x28, 0xd3, 0xdd, 0x36, 0x1c, 0xb5, 0xb4, 0x5f, 0x95, 0x4f, 0xcb,
	0x55, 0x26, 0xd7, 0x7f, 0xbf, 0x3f, 0x2c, 0x56, 0x78, 0xc4, 0x97, 0xab, 0x54, 0x85, 0x82, 0xff,
	0xb9, 0x78, 0x7e, 0x1e, 0x66, 0x89, 0xc7, 0xc2, 0xde, 0x79, 0x5f, 0xca, 0x9e, 0x9f, 0xa5, 0x27,
	0xf4, 0xcb, 0xe5, 0x67, 0xc9, 0x89, 0x40, 0x7e, 0x17, 0xfb, 0x28, 0x96, 0xfd, 0x98, 0xbd, 0x6e,
	0x52, 0xd8, 0xd9, 0x97, 0x00, 0x00, 0x00, 0xff, 0xff, 0x71, 0xb1, 0x97, 0x8c, 0xca, 0x09, 0x00,
	0x00,
}
