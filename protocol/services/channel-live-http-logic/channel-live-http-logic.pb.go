// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-live-http-logic.proto

package channel_live_http_logic // import "golang.52tt.com/protocol/services/channel-live-http-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AnchorCheckUpgradeType int32

const (
	AnchorCheckUpgradeType_AnchorCheckUpgradeType_Disable AnchorCheckUpgradeType = 0
	AnchorCheckUpgradeType_AnchorCheckUpgradeType_Enable  AnchorCheckUpgradeType = 1
	AnchorCheckUpgradeType_AnchorCheckUpgradeType_Used    AnchorCheckUpgradeType = 2
)

var AnchorCheckUpgradeType_name = map[int32]string{
	0: "AnchorCheckUpgradeType_Disable",
	1: "AnchorCheckUpgradeType_Enable",
	2: "AnchorCheckUpgradeType_Used",
}
var AnchorCheckUpgradeType_value = map[string]int32{
	"AnchorCheckUpgradeType_Disable": 0,
	"AnchorCheckUpgradeType_Enable":  1,
	"AnchorCheckUpgradeType_Used":    2,
}

func (x AnchorCheckUpgradeType) String() string {
	return proto.EnumName(AnchorCheckUpgradeType_name, int32(x))
}
func (AnchorCheckUpgradeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{0}
}

// 报名状态类型
type RegisterStatus int32

const (
	RegisterStatus_RegisterStatusInvalid      RegisterStatus = 0
	RegisterStatus_RegisterStatusCan          RegisterStatus = 1
	RegisterStatus_RegisterStatusSuccess      RegisterStatus = 2
	RegisterStatus_RegisterStatusAlreadyOther RegisterStatus = 3
	RegisterStatus_RegisterStatusNoAct        RegisterStatus = 4
	RegisterStatus_RegisterStatusCancel       RegisterStatus = 5
	RegisterStatus_RegisterStatusNoOnTimeLive RegisterStatus = 6
	RegisterStatus_RegisterStatusReview       RegisterStatus = 7
)

var RegisterStatus_name = map[int32]string{
	0: "RegisterStatusInvalid",
	1: "RegisterStatusCan",
	2: "RegisterStatusSuccess",
	3: "RegisterStatusAlreadyOther",
	4: "RegisterStatusNoAct",
	5: "RegisterStatusCancel",
	6: "RegisterStatusNoOnTimeLive",
	7: "RegisterStatusReview",
}
var RegisterStatus_value = map[string]int32{
	"RegisterStatusInvalid":      0,
	"RegisterStatusCan":          1,
	"RegisterStatusSuccess":      2,
	"RegisterStatusAlreadyOther": 3,
	"RegisterStatusNoAct":        4,
	"RegisterStatusCancel":       5,
	"RegisterStatusNoOnTimeLive": 6,
	"RegisterStatusReview":       7,
}

func (x RegisterStatus) String() string {
	return proto.EnumName(RegisterStatus_name, int32(x))
}
func (RegisterStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{1}
}

// 获取主页信息 POST /channel-live/anchor-cert/anchorCertInit
type AnchorCertInitInfo struct {
	LastCheckLevel       string   `protobuf:"bytes,1,opt,name=last_check_level,json=lastCheckLevel,proto3" json:"last_check_level"`
	IsShowCertInfo       bool     `protobuf:"varint,2,opt,name=is_show_cert_info,json=isShowCertInfo,proto3" json:"is_show_cert_info"`
	IsCertKeepUpgrade    bool     `protobuf:"varint,3,opt,name=is_cert_keep_upgrade,json=isCertKeepUpgrade,proto3" json:"is_cert_keep_upgrade"`
	ItemName             string   `protobuf:"bytes,4,opt,name=item_name,json=itemName,proto3" json:"item_name"`
	BaseImgurl           string   `protobuf:"bytes,5,opt,name=base_imgurl,json=baseImgurl,proto3" json:"base_imgurl"`
	ShadowColor          string   `protobuf:"bytes,6,opt,name=shadow_color,json=shadowColor,proto3" json:"shadow_color"`
	AnchorTag            string   `protobuf:"bytes,7,opt,name=anchor_tag,json=anchorTag,proto3" json:"anchor_tag"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCertInitInfo) Reset()         { *m = AnchorCertInitInfo{} }
func (m *AnchorCertInitInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCertInitInfo) ProtoMessage()    {}
func (*AnchorCertInitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{0}
}
func (m *AnchorCertInitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCertInitInfo.Unmarshal(m, b)
}
func (m *AnchorCertInitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCertInitInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCertInitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCertInitInfo.Merge(dst, src)
}
func (m *AnchorCertInitInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCertInitInfo.Size(m)
}
func (m *AnchorCertInitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCertInitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCertInitInfo proto.InternalMessageInfo

func (m *AnchorCertInitInfo) GetLastCheckLevel() string {
	if m != nil {
		return m.LastCheckLevel
	}
	return ""
}

func (m *AnchorCertInitInfo) GetIsShowCertInfo() bool {
	if m != nil {
		return m.IsShowCertInfo
	}
	return false
}

func (m *AnchorCertInitInfo) GetIsCertKeepUpgrade() bool {
	if m != nil {
		return m.IsCertKeepUpgrade
	}
	return false
}

func (m *AnchorCertInitInfo) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *AnchorCertInitInfo) GetBaseImgurl() string {
	if m != nil {
		return m.BaseImgurl
	}
	return ""
}

func (m *AnchorCertInitInfo) GetShadowColor() string {
	if m != nil {
		return m.ShadowColor
	}
	return ""
}

func (m *AnchorCertInitInfo) GetAnchorTag() string {
	if m != nil {
		return m.AnchorTag
	}
	return ""
}

// 获取考核信息 POST /channel-live/anchor-cert/getAnchorCheckInfo
type AnchorCheckInfo struct {
	CheckInfoList        []*AnchorCheckInfo_CheckData `protobuf:"bytes,1,rep,name=check_info_list,json=checkInfoList,proto3" json:"check_info_list"`
	CheckUpgradeStatus   uint32                       `protobuf:"varint,2,opt,name=check_upgrade_status,json=checkUpgradeStatus,proto3" json:"check_upgrade_status"`
	RemainDay            uint32                       `protobuf:"varint,3,opt,name=remain_day,json=remainDay,proto3" json:"remain_day"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AnchorCheckInfo) Reset()         { *m = AnchorCheckInfo{} }
func (m *AnchorCheckInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckInfo) ProtoMessage()    {}
func (*AnchorCheckInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{1}
}
func (m *AnchorCheckInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckInfo.Unmarshal(m, b)
}
func (m *AnchorCheckInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckInfo.Merge(dst, src)
}
func (m *AnchorCheckInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckInfo.Size(m)
}
func (m *AnchorCheckInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckInfo proto.InternalMessageInfo

func (m *AnchorCheckInfo) GetCheckInfoList() []*AnchorCheckInfo_CheckData {
	if m != nil {
		return m.CheckInfoList
	}
	return nil
}

func (m *AnchorCheckInfo) GetCheckUpgradeStatus() uint32 {
	if m != nil {
		return m.CheckUpgradeStatus
	}
	return 0
}

func (m *AnchorCheckInfo) GetRemainDay() uint32 {
	if m != nil {
		return m.RemainDay
	}
	return 0
}

type AnchorCheckInfo_CheckData struct {
	CheckLevel           string   `protobuf:"bytes,1,opt,name=check_level,json=checkLevel,proto3" json:"check_level"`
	CreateTime           string   `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCheckInfo_CheckData) Reset()         { *m = AnchorCheckInfo_CheckData{} }
func (m *AnchorCheckInfo_CheckData) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckInfo_CheckData) ProtoMessage()    {}
func (*AnchorCheckInfo_CheckData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{1, 0}
}
func (m *AnchorCheckInfo_CheckData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckInfo_CheckData.Unmarshal(m, b)
}
func (m *AnchorCheckInfo_CheckData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckInfo_CheckData.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckInfo_CheckData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckInfo_CheckData.Merge(dst, src)
}
func (m *AnchorCheckInfo_CheckData) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckInfo_CheckData.Size(m)
}
func (m *AnchorCheckInfo_CheckData) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckInfo_CheckData.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckInfo_CheckData proto.InternalMessageInfo

func (m *AnchorCheckInfo_CheckData) GetCheckLevel() string {
	if m != nil {
		return m.CheckLevel
	}
	return ""
}

func (m *AnchorCheckInfo_CheckData) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

// 申请考核升级 POST /channel-live/anchor-cert/applyAnchorCheckUpgrade
type AnchorCheckUpgradeInfo struct {
	RemainDay            uint32   `protobuf:"varint,1,opt,name=remain_day,json=remainDay,proto3" json:"remain_day"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCheckUpgradeInfo) Reset()         { *m = AnchorCheckUpgradeInfo{} }
func (m *AnchorCheckUpgradeInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCheckUpgradeInfo) ProtoMessage()    {}
func (*AnchorCheckUpgradeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{2}
}
func (m *AnchorCheckUpgradeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCheckUpgradeInfo.Unmarshal(m, b)
}
func (m *AnchorCheckUpgradeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCheckUpgradeInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCheckUpgradeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCheckUpgradeInfo.Merge(dst, src)
}
func (m *AnchorCheckUpgradeInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCheckUpgradeInfo.Size(m)
}
func (m *AnchorCheckUpgradeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCheckUpgradeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCheckUpgradeInfo proto.InternalMessageInfo

func (m *AnchorCheckUpgradeInfo) GetRemainDay() uint32 {
	if m != nil {
		return m.RemainDay
	}
	return 0
}

// 获取认证等级信息 POST /channel-live/anchor-cert/getAnchorCertInfo
type AnchorCertInfo struct {
	AnchorTag             string                 `protobuf:"bytes,1,opt,name=anchor_tag,json=anchorTag,proto3" json:"anchor_tag"`
	ItemName              string                 `protobuf:"bytes,2,opt,name=item_name,json=itemName,proto3" json:"item_name"`
	BaseImgurl            string                 `protobuf:"bytes,3,opt,name=base_imgurl,json=baseImgurl,proto3" json:"base_imgurl"`
	ShadowColor           string                 `protobuf:"bytes,4,opt,name=shadow_color,json=shadowColor,proto3" json:"shadow_color"`
	MonthStats            *MonthStats            `protobuf:"bytes,5,opt,name=month_stats,json=monthStats,proto3" json:"month_stats"`
	AnchorCertUpgradeInfo *AnchorCertUpgradeInfo `protobuf:"bytes,6,opt,name=anchor_cert_upgrade_info,json=anchorCertUpgradeInfo,proto3" json:"anchor_cert_upgrade_info"`
	XXX_NoUnkeyedLiteral  struct{}               `json:"-"`
	XXX_unrecognized      []byte                 `json:"-"`
	XXX_sizecache         int32                  `json:"-"`
}

func (m *AnchorCertInfo) Reset()         { *m = AnchorCertInfo{} }
func (m *AnchorCertInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCertInfo) ProtoMessage()    {}
func (*AnchorCertInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{3}
}
func (m *AnchorCertInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCertInfo.Unmarshal(m, b)
}
func (m *AnchorCertInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCertInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCertInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCertInfo.Merge(dst, src)
}
func (m *AnchorCertInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCertInfo.Size(m)
}
func (m *AnchorCertInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCertInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCertInfo proto.InternalMessageInfo

func (m *AnchorCertInfo) GetAnchorTag() string {
	if m != nil {
		return m.AnchorTag
	}
	return ""
}

func (m *AnchorCertInfo) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *AnchorCertInfo) GetBaseImgurl() string {
	if m != nil {
		return m.BaseImgurl
	}
	return ""
}

func (m *AnchorCertInfo) GetShadowColor() string {
	if m != nil {
		return m.ShadowColor
	}
	return ""
}

func (m *AnchorCertInfo) GetMonthStats() *MonthStats {
	if m != nil {
		return m.MonthStats
	}
	return nil
}

func (m *AnchorCertInfo) GetAnchorCertUpgradeInfo() *AnchorCertUpgradeInfo {
	if m != nil {
		return m.AnchorCertUpgradeInfo
	}
	return nil
}

type MonthStats struct {
	MonthActiveDays      uint32   `protobuf:"varint,1,opt,name=month_active_days,json=monthActiveDays,proto3" json:"month_active_days"`
	MonthPlatformDays    uint32   `protobuf:"varint,2,opt,name=month_platform_days,json=monthPlatformDays,proto3" json:"month_platform_days"`
	MonthNewFansCnt      uint32   `protobuf:"varint,3,opt,name=month_new_fans_cnt,json=monthNewFansCnt,proto3" json:"month_new_fans_cnt"`
	MonthConsumerCnt     uint32   `protobuf:"varint,4,opt,name=month_consumer_cnt,json=monthConsumerCnt,proto3" json:"month_consumer_cnt"`
	MonthGiftValue       uint32   `protobuf:"varint,5,opt,name=month_gift_value,json=monthGiftValue,proto3" json:"month_gift_value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthStats) Reset()         { *m = MonthStats{} }
func (m *MonthStats) String() string { return proto.CompactTextString(m) }
func (*MonthStats) ProtoMessage()    {}
func (*MonthStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{4}
}
func (m *MonthStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthStats.Unmarshal(m, b)
}
func (m *MonthStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthStats.Marshal(b, m, deterministic)
}
func (dst *MonthStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthStats.Merge(dst, src)
}
func (m *MonthStats) XXX_Size() int {
	return xxx_messageInfo_MonthStats.Size(m)
}
func (m *MonthStats) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthStats.DiscardUnknown(m)
}

var xxx_messageInfo_MonthStats proto.InternalMessageInfo

func (m *MonthStats) GetMonthActiveDays() uint32 {
	if m != nil {
		return m.MonthActiveDays
	}
	return 0
}

func (m *MonthStats) GetMonthPlatformDays() uint32 {
	if m != nil {
		return m.MonthPlatformDays
	}
	return 0
}

func (m *MonthStats) GetMonthNewFansCnt() uint32 {
	if m != nil {
		return m.MonthNewFansCnt
	}
	return 0
}

func (m *MonthStats) GetMonthConsumerCnt() uint32 {
	if m != nil {
		return m.MonthConsumerCnt
	}
	return 0
}

func (m *MonthStats) GetMonthGiftValue() uint32 {
	if m != nil {
		return m.MonthGiftValue
	}
	return 0
}

type AnchorCertUpgradeInfo struct {
	TaskImgUrl           string   `protobuf:"bytes,1,opt,name=task_img_url,json=taskImgUrl,proto3" json:"task_img_url"`
	TaskName             string   `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name"`
	TaskPlace            string   `protobuf:"bytes,3,opt,name=task_place,json=taskPlace,proto3" json:"task_place"`
	TaskInitialLevel     string   `protobuf:"bytes,4,opt,name=task_initial_level,json=taskInitialLevel,proto3" json:"task_initial_level"`
	TaskCheckType        string   `protobuf:"bytes,5,opt,name=task_check_type,json=taskCheckType,proto3" json:"task_check_type"`
	TaskCheckTime        string   `protobuf:"bytes,6,opt,name=task_check_time,json=taskCheckTime,proto3" json:"task_check_time"`
	TaskJumpUrl          string   `protobuf:"bytes,7,opt,name=task_jump_url,json=taskJumpUrl,proto3" json:"task_jump_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorCertUpgradeInfo) Reset()         { *m = AnchorCertUpgradeInfo{} }
func (m *AnchorCertUpgradeInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorCertUpgradeInfo) ProtoMessage()    {}
func (*AnchorCertUpgradeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{5}
}
func (m *AnchorCertUpgradeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorCertUpgradeInfo.Unmarshal(m, b)
}
func (m *AnchorCertUpgradeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorCertUpgradeInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorCertUpgradeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorCertUpgradeInfo.Merge(dst, src)
}
func (m *AnchorCertUpgradeInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorCertUpgradeInfo.Size(m)
}
func (m *AnchorCertUpgradeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorCertUpgradeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorCertUpgradeInfo proto.InternalMessageInfo

func (m *AnchorCertUpgradeInfo) GetTaskImgUrl() string {
	if m != nil {
		return m.TaskImgUrl
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskPlace() string {
	if m != nil {
		return m.TaskPlace
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskInitialLevel() string {
	if m != nil {
		return m.TaskInitialLevel
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskCheckType() string {
	if m != nil {
		return m.TaskCheckType
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskCheckTime() string {
	if m != nil {
		return m.TaskCheckTime
	}
	return ""
}

func (m *AnchorCertUpgradeInfo) GetTaskJumpUrl() string {
	if m != nil {
		return m.TaskJumpUrl
	}
	return ""
}

// *****  官频报名 *****//
// 检查用户的报名入口信息
type CheckUserRegisterEntryReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserRegisterEntryReq) Reset()         { *m = CheckUserRegisterEntryReq{} }
func (m *CheckUserRegisterEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserRegisterEntryReq) ProtoMessage()    {}
func (*CheckUserRegisterEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{6}
}
func (m *CheckUserRegisterEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserRegisterEntryReq.Unmarshal(m, b)
}
func (m *CheckUserRegisterEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserRegisterEntryReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserRegisterEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserRegisterEntryReq.Merge(dst, src)
}
func (m *CheckUserRegisterEntryReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserRegisterEntryReq.Size(m)
}
func (m *CheckUserRegisterEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserRegisterEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserRegisterEntryReq proto.InternalMessageInfo

type CheckUserRegisterEntryResp struct {
	IsHasRegisEntry      bool     `protobuf:"varint,1,opt,name=isHasRegisEntry,proto3" json:"isHasRegisEntry"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserRegisterEntryResp) Reset()         { *m = CheckUserRegisterEntryResp{} }
func (m *CheckUserRegisterEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserRegisterEntryResp) ProtoMessage()    {}
func (*CheckUserRegisterEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{7}
}
func (m *CheckUserRegisterEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserRegisterEntryResp.Unmarshal(m, b)
}
func (m *CheckUserRegisterEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserRegisterEntryResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserRegisterEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserRegisterEntryResp.Merge(dst, src)
}
func (m *CheckUserRegisterEntryResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserRegisterEntryResp.Size(m)
}
func (m *CheckUserRegisterEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserRegisterEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserRegisterEntryResp proto.InternalMessageInfo

func (m *CheckUserRegisterEntryResp) GetIsHasRegisEntry() bool {
	if m != nil {
		return m.IsHasRegisEntry
	}
	return false
}

// 场次排班信息
type MatchSchedule struct {
	BeginTs              uint32   `protobuf:"varint,1,opt,name=beginTs,proto3" json:"beginTs"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=endTs,proto3" json:"endTs"`
	IsCanRegister        bool     `protobuf:"varint,3,opt,name=isCanRegister,proto3" json:"isCanRegister"`
	IsAlreadyRegister    bool     `protobuf:"varint,4,opt,name=isAlreadyRegister,proto3" json:"isAlreadyRegister"`
	ScheduleId           uint32   `protobuf:"varint,5,opt,name=scheduleId,proto3" json:"scheduleId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchSchedule) Reset()         { *m = MatchSchedule{} }
func (m *MatchSchedule) String() string { return proto.CompactTextString(m) }
func (*MatchSchedule) ProtoMessage()    {}
func (*MatchSchedule) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{8}
}
func (m *MatchSchedule) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchSchedule.Unmarshal(m, b)
}
func (m *MatchSchedule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchSchedule.Marshal(b, m, deterministic)
}
func (dst *MatchSchedule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchSchedule.Merge(dst, src)
}
func (m *MatchSchedule) XXX_Size() int {
	return xxx_messageInfo_MatchSchedule.Size(m)
}
func (m *MatchSchedule) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchSchedule.DiscardUnknown(m)
}

var xxx_messageInfo_MatchSchedule proto.InternalMessageInfo

func (m *MatchSchedule) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *MatchSchedule) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *MatchSchedule) GetIsCanRegister() bool {
	if m != nil {
		return m.IsCanRegister
	}
	return false
}

func (m *MatchSchedule) GetIsAlreadyRegister() bool {
	if m != nil {
		return m.IsAlreadyRegister
	}
	return false
}

func (m *MatchSchedule) GetScheduleId() uint32 {
	if m != nil {
		return m.ScheduleId
	}
	return 0
}

// 场次信息
type MatchInfo struct {
	MatchTs              uint32           `protobuf:"varint,1,opt,name=matchTs,proto3" json:"matchTs"`
	MatchName            string           `protobuf:"bytes,2,opt,name=matchName,proto3" json:"matchName"`
	ScheduleList         []*MatchSchedule `protobuf:"bytes,3,rep,name=schedule_list,json=scheduleList,proto3" json:"schedule_list"`
	MatchId              uint32           `protobuf:"varint,4,opt,name=matchId,proto3" json:"matchId"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MatchInfo) Reset()         { *m = MatchInfo{} }
func (m *MatchInfo) String() string { return proto.CompactTextString(m) }
func (*MatchInfo) ProtoMessage()    {}
func (*MatchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{9}
}
func (m *MatchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchInfo.Unmarshal(m, b)
}
func (m *MatchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchInfo.Marshal(b, m, deterministic)
}
func (dst *MatchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchInfo.Merge(dst, src)
}
func (m *MatchInfo) XXX_Size() int {
	return xxx_messageInfo_MatchInfo.Size(m)
}
func (m *MatchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MatchInfo proto.InternalMessageInfo

func (m *MatchInfo) GetMatchTs() uint32 {
	if m != nil {
		return m.MatchTs
	}
	return 0
}

func (m *MatchInfo) GetMatchName() string {
	if m != nil {
		return m.MatchName
	}
	return ""
}

func (m *MatchInfo) GetScheduleList() []*MatchSchedule {
	if m != nil {
		return m.ScheduleList
	}
	return nil
}

func (m *MatchInfo) GetMatchId() uint32 {
	if m != nil {
		return m.MatchId
	}
	return 0
}

// 官频信息
type OfficialChannelInfo struct {
	ChannelId            uint32     `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId"`
	ActName              string     `protobuf:"bytes,2,opt,name=actName,proto3" json:"actName"`
	ActTime              string     `protobuf:"bytes,3,opt,name=actTime,proto3" json:"actTime"`
	RegisterTs           int64      `protobuf:"varint,4,opt,name=registerTs,proto3" json:"registerTs"`
	RegisterCond         string     `protobuf:"bytes,5,opt,name=registerCond,proto3" json:"registerCond"`
	RegisterStatus       uint32     `protobuf:"varint,6,opt,name=registerStatus,proto3" json:"registerStatus"`
	RegisterMatch        *MatchInfo `protobuf:"bytes,7,opt,name=registerMatch,proto3" json:"registerMatch"`
	ActionExample        string     `protobuf:"bytes,8,opt,name=actionExample,proto3" json:"actionExample"`
	AudioExample         string     `protobuf:"bytes,9,opt,name=audioExample,proto3" json:"audioExample"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *OfficialChannelInfo) Reset()         { *m = OfficialChannelInfo{} }
func (m *OfficialChannelInfo) String() string { return proto.CompactTextString(m) }
func (*OfficialChannelInfo) ProtoMessage()    {}
func (*OfficialChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{10}
}
func (m *OfficialChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialChannelInfo.Unmarshal(m, b)
}
func (m *OfficialChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialChannelInfo.Marshal(b, m, deterministic)
}
func (dst *OfficialChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialChannelInfo.Merge(dst, src)
}
func (m *OfficialChannelInfo) XXX_Size() int {
	return xxx_messageInfo_OfficialChannelInfo.Size(m)
}
func (m *OfficialChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialChannelInfo proto.InternalMessageInfo

func (m *OfficialChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OfficialChannelInfo) GetActName() string {
	if m != nil {
		return m.ActName
	}
	return ""
}

func (m *OfficialChannelInfo) GetActTime() string {
	if m != nil {
		return m.ActTime
	}
	return ""
}

func (m *OfficialChannelInfo) GetRegisterTs() int64 {
	if m != nil {
		return m.RegisterTs
	}
	return 0
}

func (m *OfficialChannelInfo) GetRegisterCond() string {
	if m != nil {
		return m.RegisterCond
	}
	return ""
}

func (m *OfficialChannelInfo) GetRegisterStatus() uint32 {
	if m != nil {
		return m.RegisterStatus
	}
	return 0
}

func (m *OfficialChannelInfo) GetRegisterMatch() *MatchInfo {
	if m != nil {
		return m.RegisterMatch
	}
	return nil
}

func (m *OfficialChannelInfo) GetActionExample() string {
	if m != nil {
		return m.ActionExample
	}
	return ""
}

func (m *OfficialChannelInfo) GetAudioExample() string {
	if m != nil {
		return m.AudioExample
	}
	return ""
}

// 获取报名页的官频列表
type GetRegisterOfficialChListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRegisterOfficialChListReq) Reset()         { *m = GetRegisterOfficialChListReq{} }
func (m *GetRegisterOfficialChListReq) String() string { return proto.CompactTextString(m) }
func (*GetRegisterOfficialChListReq) ProtoMessage()    {}
func (*GetRegisterOfficialChListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{11}
}
func (m *GetRegisterOfficialChListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRegisterOfficialChListReq.Unmarshal(m, b)
}
func (m *GetRegisterOfficialChListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRegisterOfficialChListReq.Marshal(b, m, deterministic)
}
func (dst *GetRegisterOfficialChListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegisterOfficialChListReq.Merge(dst, src)
}
func (m *GetRegisterOfficialChListReq) XXX_Size() int {
	return xxx_messageInfo_GetRegisterOfficialChListReq.Size(m)
}
func (m *GetRegisterOfficialChListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegisterOfficialChListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegisterOfficialChListReq proto.InternalMessageInfo

type GetRegisterOfficialChListResp struct {
	InfoList             []*OfficialChannelInfo `protobuf:"bytes,1,rep,name=infoList,proto3" json:"infoList"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetRegisterOfficialChListResp) Reset()         { *m = GetRegisterOfficialChListResp{} }
func (m *GetRegisterOfficialChListResp) String() string { return proto.CompactTextString(m) }
func (*GetRegisterOfficialChListResp) ProtoMessage()    {}
func (*GetRegisterOfficialChListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{12}
}
func (m *GetRegisterOfficialChListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRegisterOfficialChListResp.Unmarshal(m, b)
}
func (m *GetRegisterOfficialChListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRegisterOfficialChListResp.Marshal(b, m, deterministic)
}
func (dst *GetRegisterOfficialChListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegisterOfficialChListResp.Merge(dst, src)
}
func (m *GetRegisterOfficialChListResp) XXX_Size() int {
	return xxx_messageInfo_GetRegisterOfficialChListResp.Size(m)
}
func (m *GetRegisterOfficialChListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegisterOfficialChListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegisterOfficialChListResp proto.InternalMessageInfo

func (m *GetRegisterOfficialChListResp) GetInfoList() []*OfficialChannelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 获取官频的场次信息
type GetOfficialChMatchInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOfficialChMatchInfoReq) Reset()         { *m = GetOfficialChMatchInfoReq{} }
func (m *GetOfficialChMatchInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetOfficialChMatchInfoReq) ProtoMessage()    {}
func (*GetOfficialChMatchInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{13}
}
func (m *GetOfficialChMatchInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfficialChMatchInfoReq.Unmarshal(m, b)
}
func (m *GetOfficialChMatchInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfficialChMatchInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetOfficialChMatchInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfficialChMatchInfoReq.Merge(dst, src)
}
func (m *GetOfficialChMatchInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetOfficialChMatchInfoReq.Size(m)
}
func (m *GetOfficialChMatchInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfficialChMatchInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfficialChMatchInfoReq proto.InternalMessageInfo

func (m *GetOfficialChMatchInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetOfficialChMatchInfoResp struct {
	MatchInfo            []*MatchInfo `protobuf:"bytes,1,rep,name=matchInfo,proto3" json:"matchInfo"`
	ActionExample        string       `protobuf:"bytes,2,opt,name=actionExample,proto3" json:"actionExample"`
	AudioExample         string       `protobuf:"bytes,3,opt,name=audioExample,proto3" json:"audioExample"`
	IntroExemple         string       `protobuf:"bytes,4,opt,name=introExemple,proto3" json:"introExemple"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOfficialChMatchInfoResp) Reset()         { *m = GetOfficialChMatchInfoResp{} }
func (m *GetOfficialChMatchInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetOfficialChMatchInfoResp) ProtoMessage()    {}
func (*GetOfficialChMatchInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{14}
}
func (m *GetOfficialChMatchInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfficialChMatchInfoResp.Unmarshal(m, b)
}
func (m *GetOfficialChMatchInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfficialChMatchInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetOfficialChMatchInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfficialChMatchInfoResp.Merge(dst, src)
}
func (m *GetOfficialChMatchInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetOfficialChMatchInfoResp.Size(m)
}
func (m *GetOfficialChMatchInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfficialChMatchInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfficialChMatchInfoResp proto.InternalMessageInfo

func (m *GetOfficialChMatchInfoResp) GetMatchInfo() []*MatchInfo {
	if m != nil {
		return m.MatchInfo
	}
	return nil
}

func (m *GetOfficialChMatchInfoResp) GetActionExample() string {
	if m != nil {
		return m.ActionExample
	}
	return ""
}

func (m *GetOfficialChMatchInfoResp) GetAudioExample() string {
	if m != nil {
		return m.AudioExample
	}
	return ""
}

func (m *GetOfficialChMatchInfoResp) GetIntroExemple() string {
	if m != nil {
		return m.IntroExemple
	}
	return ""
}

// 报名官频
type RegisterOfficialChannelReq struct {
	ChannelId            uint32     `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId"`
	Introduction         string     `protobuf:"bytes,2,opt,name=introduction,proto3" json:"introduction"`
	MatchInfo            *MatchInfo `protobuf:"bytes,3,opt,name=matchInfo,proto3" json:"matchInfo"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *RegisterOfficialChannelReq) Reset()         { *m = RegisterOfficialChannelReq{} }
func (m *RegisterOfficialChannelReq) String() string { return proto.CompactTextString(m) }
func (*RegisterOfficialChannelReq) ProtoMessage()    {}
func (*RegisterOfficialChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{15}
}
func (m *RegisterOfficialChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegisterOfficialChannelReq.Unmarshal(m, b)
}
func (m *RegisterOfficialChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegisterOfficialChannelReq.Marshal(b, m, deterministic)
}
func (dst *RegisterOfficialChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterOfficialChannelReq.Merge(dst, src)
}
func (m *RegisterOfficialChannelReq) XXX_Size() int {
	return xxx_messageInfo_RegisterOfficialChannelReq.Size(m)
}
func (m *RegisterOfficialChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterOfficialChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterOfficialChannelReq proto.InternalMessageInfo

func (m *RegisterOfficialChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RegisterOfficialChannelReq) GetIntroduction() string {
	if m != nil {
		return m.Introduction
	}
	return ""
}

func (m *RegisterOfficialChannelReq) GetMatchInfo() *MatchInfo {
	if m != nil {
		return m.MatchInfo
	}
	return nil
}

type RegisterOfficialChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegisterOfficialChannelResp) Reset()         { *m = RegisterOfficialChannelResp{} }
func (m *RegisterOfficialChannelResp) String() string { return proto.CompactTextString(m) }
func (*RegisterOfficialChannelResp) ProtoMessage()    {}
func (*RegisterOfficialChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{16}
}
func (m *RegisterOfficialChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegisterOfficialChannelResp.Unmarshal(m, b)
}
func (m *RegisterOfficialChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegisterOfficialChannelResp.Marshal(b, m, deterministic)
}
func (dst *RegisterOfficialChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterOfficialChannelResp.Merge(dst, src)
}
func (m *RegisterOfficialChannelResp) XXX_Size() int {
	return xxx_messageInfo_RegisterOfficialChannelResp.Size(m)
}
func (m *RegisterOfficialChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterOfficialChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterOfficialChannelResp proto.InternalMessageInfo

// 取消报名
type CancelRegisterOfficialChReq struct {
	ChannelId            uint32     `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId"`
	RegisterTs           int64      `protobuf:"varint,2,opt,name=registerTs,proto3" json:"registerTs"`
	MatchInfo            *MatchInfo `protobuf:"bytes,3,opt,name=matchInfo,proto3" json:"matchInfo"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CancelRegisterOfficialChReq) Reset()         { *m = CancelRegisterOfficialChReq{} }
func (m *CancelRegisterOfficialChReq) String() string { return proto.CompactTextString(m) }
func (*CancelRegisterOfficialChReq) ProtoMessage()    {}
func (*CancelRegisterOfficialChReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{17}
}
func (m *CancelRegisterOfficialChReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelRegisterOfficialChReq.Unmarshal(m, b)
}
func (m *CancelRegisterOfficialChReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelRegisterOfficialChReq.Marshal(b, m, deterministic)
}
func (dst *CancelRegisterOfficialChReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelRegisterOfficialChReq.Merge(dst, src)
}
func (m *CancelRegisterOfficialChReq) XXX_Size() int {
	return xxx_messageInfo_CancelRegisterOfficialChReq.Size(m)
}
func (m *CancelRegisterOfficialChReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelRegisterOfficialChReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelRegisterOfficialChReq proto.InternalMessageInfo

func (m *CancelRegisterOfficialChReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CancelRegisterOfficialChReq) GetRegisterTs() int64 {
	if m != nil {
		return m.RegisterTs
	}
	return 0
}

func (m *CancelRegisterOfficialChReq) GetMatchInfo() *MatchInfo {
	if m != nil {
		return m.MatchInfo
	}
	return nil
}

type CancelRegisterOfficialChResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelRegisterOfficialChResp) Reset()         { *m = CancelRegisterOfficialChResp{} }
func (m *CancelRegisterOfficialChResp) String() string { return proto.CompactTextString(m) }
func (*CancelRegisterOfficialChResp) ProtoMessage()    {}
func (*CancelRegisterOfficialChResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_http_logic_925ede533acabd13, []int{18}
}
func (m *CancelRegisterOfficialChResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelRegisterOfficialChResp.Unmarshal(m, b)
}
func (m *CancelRegisterOfficialChResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelRegisterOfficialChResp.Marshal(b, m, deterministic)
}
func (dst *CancelRegisterOfficialChResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelRegisterOfficialChResp.Merge(dst, src)
}
func (m *CancelRegisterOfficialChResp) XXX_Size() int {
	return xxx_messageInfo_CancelRegisterOfficialChResp.Size(m)
}
func (m *CancelRegisterOfficialChResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelRegisterOfficialChResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelRegisterOfficialChResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*AnchorCertInitInfo)(nil), "channel_live_http_logic.AnchorCertInitInfo")
	proto.RegisterType((*AnchorCheckInfo)(nil), "channel_live_http_logic.AnchorCheckInfo")
	proto.RegisterType((*AnchorCheckInfo_CheckData)(nil), "channel_live_http_logic.AnchorCheckInfo.CheckData")
	proto.RegisterType((*AnchorCheckUpgradeInfo)(nil), "channel_live_http_logic.AnchorCheckUpgradeInfo")
	proto.RegisterType((*AnchorCertInfo)(nil), "channel_live_http_logic.AnchorCertInfo")
	proto.RegisterType((*MonthStats)(nil), "channel_live_http_logic.MonthStats")
	proto.RegisterType((*AnchorCertUpgradeInfo)(nil), "channel_live_http_logic.AnchorCertUpgradeInfo")
	proto.RegisterType((*CheckUserRegisterEntryReq)(nil), "channel_live_http_logic.CheckUserRegisterEntryReq")
	proto.RegisterType((*CheckUserRegisterEntryResp)(nil), "channel_live_http_logic.CheckUserRegisterEntryResp")
	proto.RegisterType((*MatchSchedule)(nil), "channel_live_http_logic.MatchSchedule")
	proto.RegisterType((*MatchInfo)(nil), "channel_live_http_logic.MatchInfo")
	proto.RegisterType((*OfficialChannelInfo)(nil), "channel_live_http_logic.OfficialChannelInfo")
	proto.RegisterType((*GetRegisterOfficialChListReq)(nil), "channel_live_http_logic.GetRegisterOfficialChListReq")
	proto.RegisterType((*GetRegisterOfficialChListResp)(nil), "channel_live_http_logic.GetRegisterOfficialChListResp")
	proto.RegisterType((*GetOfficialChMatchInfoReq)(nil), "channel_live_http_logic.GetOfficialChMatchInfoReq")
	proto.RegisterType((*GetOfficialChMatchInfoResp)(nil), "channel_live_http_logic.GetOfficialChMatchInfoResp")
	proto.RegisterType((*RegisterOfficialChannelReq)(nil), "channel_live_http_logic.RegisterOfficialChannelReq")
	proto.RegisterType((*RegisterOfficialChannelResp)(nil), "channel_live_http_logic.RegisterOfficialChannelResp")
	proto.RegisterType((*CancelRegisterOfficialChReq)(nil), "channel_live_http_logic.CancelRegisterOfficialChReq")
	proto.RegisterType((*CancelRegisterOfficialChResp)(nil), "channel_live_http_logic.CancelRegisterOfficialChResp")
	proto.RegisterEnum("channel_live_http_logic.AnchorCheckUpgradeType", AnchorCheckUpgradeType_name, AnchorCheckUpgradeType_value)
	proto.RegisterEnum("channel_live_http_logic.RegisterStatus", RegisterStatus_name, RegisterStatus_value)
}

func init() {
	proto.RegisterFile("channel-live-http-logic.proto", fileDescriptor_channel_live_http_logic_925ede533acabd13)
}

var fileDescriptor_channel_live_http_logic_925ede533acabd13 = []byte{
	// 1377 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x57, 0xdd, 0x6e, 0xdb, 0x36,
	0x14, 0xae, 0xec, 0x34, 0x89, 0x4f, 0xe2, 0xc4, 0x61, 0x93, 0xd5, 0xcd, 0x5f, 0x53, 0x6d, 0x28,
	0xb2, 0xae, 0x75, 0x86, 0x0c, 0xc3, 0x50, 0xec, 0x66, 0x99, 0x93, 0xb6, 0x5e, 0xdb, 0xb4, 0x50,
	0x92, 0x5d, 0xf4, 0x46, 0x60, 0x24, 0xda, 0xe6, 0x2a, 0x51, 0xaa, 0x48, 0x27, 0xf5, 0xdd, 0x5e,
	0x60, 0xd7, 0x03, 0x06, 0x0c, 0xd8, 0xf5, 0x6e, 0xf7, 0x00, 0x7b, 0x81, 0xbd, 0xc7, 0x80, 0x01,
	0x7b, 0x87, 0x81, 0x87, 0x54, 0x6c, 0xd9, 0x71, 0x52, 0x60, 0x77, 0xe2, 0x77, 0x3e, 0x92, 0x87,
	0xdf, 0xf9, 0x78, 0x68, 0xc3, 0x46, 0xd0, 0xa5, 0x42, 0xb0, 0xe8, 0x51, 0xc4, 0xcf, 0xd8, 0xa3,
	0xae, 0x52, 0xe9, 0xa3, 0x28, 0xe9, 0xf0, 0xa0, 0x91, 0x66, 0x89, 0x4a, 0xc8, 0x6d, 0x1b, 0xf6,
	0x75, 0xd8, 0xd7, 0x61, 0x1f, 0xc3, 0xee, 0x2f, 0x25, 0x20, 0x7b, 0x22, 0xe8, 0x26, 0x59, 0x93,
	0x65, 0xaa, 0x25, 0xb8, 0x6a, 0x89, 0x76, 0x42, 0xb6, 0xa1, 0x16, 0x51, 0xa9, 0xfc, 0xa0, 0xcb,
	0x82, 0xb7, 0x7e, 0xc4, 0xce, 0x58, 0x54, 0x77, 0xb6, 0x9c, 0xed, 0x8a, 0xb7, 0xa0, 0xf1, 0xa6,
	0x86, 0x5f, 0x68, 0x94, 0x7c, 0x0a, 0x4b, 0x5c, 0xfa, 0xb2, 0x9b, 0x9c, 0xfb, 0x01, 0xcb, 0x94,
	0xcf, 0x45, 0x3b, 0xa9, 0x97, 0xb6, 0x9c, 0xed, 0x59, 0x6f, 0x81, 0xcb, 0xa3, 0x6e, 0x72, 0x6e,
	0x16, 0x6e, 0x27, 0x64, 0x07, 0x96, 0xb9, 0x34, 0xac, 0xb7, 0x8c, 0xa5, 0x7e, 0x2f, 0xed, 0x64,
	0x34, 0x64, 0xf5, 0x32, 0xb2, 0x97, 0xb8, 0xd4, 0xcc, 0xe7, 0x8c, 0xa5, 0x27, 0x26, 0x40, 0xd6,
	0xa0, 0xc2, 0x15, 0x8b, 0x7d, 0x41, 0x63, 0x56, 0x9f, 0xc2, 0xed, 0x67, 0x35, 0x70, 0x48, 0x63,
	0x46, 0xee, 0xc2, 0xdc, 0x29, 0x95, 0xcc, 0xe7, 0x71, 0xa7, 0x97, 0x45, 0xf5, 0x9b, 0x18, 0x06,
	0x0d, 0xb5, 0x10, 0x21, 0xf7, 0x60, 0x5e, 0x76, 0x69, 0xa8, 0x13, 0x4b, 0xa2, 0x24, 0xab, 0x4f,
	0x23, 0x63, 0xce, 0x60, 0x4d, 0x0d, 0x91, 0x0d, 0x00, 0x8a, 0x87, 0xf7, 0x15, 0xed, 0xd4, 0x67,
	0x90, 0x50, 0x31, 0xc8, 0x31, 0xed, 0xb8, 0x3f, 0x95, 0x60, 0xd1, 0x8a, 0xa3, 0x0f, 0x8c, 0x87,
	0x78, 0x03, 0x8b, 0x46, 0x14, 0x7d, 0x50, 0x3f, 0xe2, 0x52, 0xd5, 0x9d, 0xad, 0xf2, 0xf6, 0xdc,
	0xee, 0x6e, 0x63, 0x82, 0xc6, 0x8d, 0x91, 0x25, 0x1a, 0xf8, 0xb5, 0x4f, 0x15, 0xf5, 0xaa, 0x41,
	0x0e, 0xbe, 0xe0, 0x52, 0x91, 0xcf, 0x61, 0xd9, 0xac, 0x6d, 0x95, 0xf1, 0xa5, 0xa2, 0xaa, 0x27,
	0x51, 0xce, 0xaa, 0x47, 0x30, 0x66, 0xb5, 0x39, 0xc2, 0x88, 0x3e, 0x40, 0xc6, 0x62, 0xca, 0x85,
	0x1f, 0xd2, 0x3e, 0x0a, 0x59, 0xf5, 0x2a, 0x06, 0xd9, 0xa7, 0xfd, 0xd5, 0x97, 0x50, 0xb9, 0xd8,
	0x4c, 0x0b, 0x36, 0x5e, 0x4e, 0x08, 0x06, 0xa5, 0xd4, 0x84, 0x8c, 0x51, 0xc5, 0x7c, 0xc5, 0x63,
	0x86, 0xbb, 0x6a, 0x02, 0x42, 0xc7, 0x3c, 0x66, 0xee, 0x57, 0xf0, 0xd1, 0xd0, 0x59, 0x6c, 0x26,
	0xa8, 0x4a, 0x31, 0x0f, 0x67, 0x24, 0x0f, 0xf7, 0xcf, 0x12, 0x2c, 0x0c, 0xbb, 0xcc, 0xcc, 0x18,
	0x92, 0xde, 0x19, 0x91, 0xbe, 0x58, 0xfa, 0xd2, 0xd5, 0xa5, 0x2f, 0x5f, 0x5b, 0xfa, 0xa9, 0xf1,
	0xd2, 0xef, 0xc3, 0x5c, 0x9c, 0x08, 0xd5, 0x45, 0x8d, 0x25, 0xda, 0x67, 0x6e, 0xf7, 0xe3, 0x89,
	0x35, 0x7c, 0xa9, 0xb9, 0x5a, 0x74, 0xe9, 0x41, 0x7c, 0xf1, 0x4d, 0x3a, 0x50, 0xb7, 0xa7, 0x40,
	0x5b, 0xe7, 0x75, 0xc3, 0x4b, 0x30, 0x8d, 0x4b, 0x36, 0xae, 0xb3, 0x05, 0xcb, 0xd4, 0x90, 0x92,
	0xde, 0x0a, 0xbd, 0x0c, 0x76, 0xff, 0x75, 0x00, 0x06, 0x39, 0x90, 0x07, 0xb0, 0x64, 0xb2, 0xa7,
	0x81, 0xd2, 0xcb, 0x86, 0xb4, 0x2f, 0xad, 0xec, 0x8b, 0x18, 0xd8, 0x43, 0x7c, 0x9f, 0xf6, 0x25,
	0x69, 0xc0, 0x2d, 0xc3, 0x4d, 0x23, 0xaa, 0xda, 0x49, 0x16, 0x1b, 0xb6, 0x31, 0x95, 0x59, 0xe6,
	0xb5, 0x8d, 0x20, 0xff, 0x33, 0x20, 0x86, 0x2f, 0xd8, 0xb9, 0xdf, 0xa6, 0x42, 0xfa, 0x81, 0x50,
	0xd6, 0x5b, 0x66, 0xf1, 0x43, 0x76, 0xfe, 0x84, 0x0a, 0xd9, 0x14, 0x8a, 0x3c, 0xcc, 0xc9, 0x41,
	0x22, 0x64, 0x2f, 0x66, 0x19, 0x92, 0xa7, 0x90, 0x5c, 0xc3, 0x48, 0xd3, 0x06, 0x34, 0x7b, 0x1b,
	0x0c, 0xe6, 0x77, 0x78, 0x5b, 0xf9, 0x67, 0x34, 0xea, 0x31, 0x54, 0xbe, 0xea, 0x2d, 0x20, 0xfe,
	0x94, 0xb7, 0xd5, 0xf7, 0x1a, 0x75, 0x7f, 0x2e, 0xc1, 0xca, 0xa5, 0x02, 0x91, 0x2d, 0x98, 0x57,
	0x54, 0xbe, 0xd5, 0xc5, 0xf7, 0x75, 0xf5, 0xad, 0x8f, 0x35, 0xd6, 0x8a, 0x3b, 0x27, 0x59, 0xa4,
	0xbd, 0x83, 0x8c, 0x61, 0xef, 0x68, 0x00, 0xbd, 0xb3, 0x01, 0x48, 0xd5, 0x62, 0x04, 0xcc, 0x5a,
	0x07, 0xe9, 0xaf, 0x35, 0xa0, 0xcf, 0x63, 0x56, 0x17, 0x5c, 0x71, 0x1a, 0xd9, 0xbb, 0x62, 0xfc,
	0x53, 0xc3, 0x3d, 0x4c, 0xc0, 0xdc, 0x98, 0xfb, 0xb0, 0x88, 0x6c, 0x73, 0xaf, 0x54, 0x3f, 0x65,
	0xb6, 0x0f, 0x55, 0x35, 0x8c, 0xb7, 0xe4, 0xb8, 0x9f, 0xb2, 0x51, 0x9e, 0xbe, 0x5d, 0xd3, 0xa3,
	0x3c, 0x1e, 0x33, 0xe2, 0x02, 0x02, 0xfe, 0x0f, 0xbd, 0x38, 0xc5, 0xc3, 0x99, 0x96, 0x34, 0xa7,
	0xc1, 0xef, 0x7a, 0x71, 0x7a, 0x92, 0x45, 0xee, 0x1a, 0xdc, 0x31, 0xd7, 0x4f, 0xb2, 0xcc, 0x63,
	0x1d, 0x2e, 0x15, 0xcb, 0x0e, 0x84, 0xca, 0xfa, 0x1e, 0x7b, 0xe7, 0x3e, 0x81, 0xd5, 0x49, 0x41,
	0x99, 0x92, 0x6d, 0x58, 0xe4, 0xf2, 0x19, 0x95, 0x18, 0x41, 0x18, 0xd5, 0x9b, 0xf5, 0x46, 0x61,
	0xf7, 0x0f, 0x07, 0xaa, 0x2f, 0xa9, 0x0a, 0xba, 0x47, 0x41, 0x97, 0x85, 0xbd, 0x88, 0x91, 0x3a,
	0xcc, 0x9c, 0xb2, 0x0e, 0x17, 0xc7, 0xb9, 0xcf, 0xf2, 0x21, 0x59, 0x86, 0x9b, 0x4c, 0x84, 0xc7,
	0xb9, 0xa3, 0xcc, 0x80, 0x7c, 0x02, 0x55, 0x2e, 0x9b, 0x54, 0xe4, 0x59, 0xd8, 0x2e, 0x5f, 0x04,
	0xc9, 0x43, 0xfd, 0x7a, 0xec, 0x45, 0x19, 0xa3, 0x61, 0xff, 0x82, 0x39, 0x95, 0xbf, 0x07, 0x23,
	0x01, 0xb2, 0x09, 0x20, 0x6d, 0x3e, 0xad, 0xd0, 0x1a, 0x67, 0x08, 0x71, 0x7f, 0x77, 0xa0, 0x82,
	0x59, 0xa3, 0x51, 0xea, 0x30, 0x13, 0xeb, 0xc1, 0x20, 0x63, 0x3b, 0x24, 0xeb, 0x50, 0xc1, 0xcf,
	0xc3, 0x81, 0x41, 0x06, 0x00, 0x79, 0x0e, 0xd5, 0x7c, 0x4d, 0xd3, 0xdf, 0xcb, 0xd8, 0xdf, 0xef,
	0x4f, 0xee, 0x0d, 0xc3, 0x42, 0x79, 0xf3, 0xf9, 0x64, 0x6c, 0xe9, 0x79, 0x12, 0xad, 0xd0, 0x5e,
	0x8a, 0x7c, 0xe8, 0xfe, 0x5d, 0x82, 0x5b, 0xaf, 0xda, 0x6d, 0x1e, 0x70, 0x1a, 0x35, 0xcd, 0xca,
	0x98, 0xf6, 0x3a, 0x54, 0xec, 0x46, 0xad, 0x30, 0xef, 0xa4, 0x17, 0x80, 0x5e, 0x8f, 0x06, 0x6a,
	0x28, 0xf1, 0x7c, 0x68, 0x23, 0xda, 0x46, 0xd6, 0xd5, 0xf9, 0x50, 0xcb, 0x96, 0x59, 0x09, 0x8f,
	0x25, 0xa6, 0x51, 0xf6, 0x86, 0x10, 0xe2, 0xc2, 0x7c, 0x3e, 0x6a, 0x26, 0x22, 0xb4, 0x16, 0x2e,
	0x60, 0xe4, 0x3e, 0x2c, 0xe4, 0x63, 0xf3, 0xf4, 0xa0, 0x81, 0xab, 0xde, 0x08, 0x4a, 0x9e, 0x41,
	0x35, 0x47, 0x50, 0x16, 0x74, 0xf0, 0xdc, 0xae, 0x7b, 0xb5, 0x78, 0xd8, 0xf9, 0x8a, 0x13, 0xb5,
	0x81, 0x74, 0x73, 0x4b, 0xc4, 0xc1, 0x7b, 0x1a, 0xa7, 0x11, 0xab, 0xcf, 0x9a, 0x1b, 0x53, 0x00,
	0x75, 0xee, 0xb4, 0x17, 0xf2, 0x24, 0x27, 0x55, 0x4c, 0xee, 0xc3, 0x98, 0xbb, 0x09, 0xeb, 0x4f,
	0x99, 0xca, 0x5d, 0x34, 0xd0, 0x5c, 0x17, 0x48, 0x5f, 0x1a, 0x0e, 0x1b, 0x57, 0xc4, 0x65, 0x4a,
	0x9e, 0xc1, 0x2c, 0xb7, 0x6f, 0xb4, 0x7d, 0xec, 0x1f, 0x4e, 0x3c, 0xcf, 0x25, 0x25, 0xf5, 0x2e,
	0x66, 0xbb, 0x8f, 0xe1, 0xce, 0x53, 0xa6, 0x06, 0x9c, 0xc1, 0xe9, 0xd9, 0xbb, 0xab, 0x2b, 0xef,
	0xfe, 0xe5, 0xc0, 0xea, 0xa4, 0xb9, 0x32, 0x25, 0xdf, 0x58, 0x4f, 0x6b, 0xc0, 0x26, 0xf9, 0x21,
	0xa2, 0x0f, 0x26, 0x8d, 0x0b, 0x5e, 0xfa, 0x10, 0xc1, 0xcb, 0xe3, 0x82, 0x6b, 0x0e, 0x17, 0x2a,
	0x4b, 0x0e, 0xde, 0x33, 0xe4, 0x98, 0xf6, 0x59, 0xc0, 0xdc, 0xdf, 0x1c, 0x58, 0x1d, 0x97, 0x1c,
	0xd3, 0xbd, 0x56, 0x8b, 0x8b, 0x0d, 0xc2, 0x1e, 0xe6, 0x66, 0x33, 0x2d, 0x60, 0x45, 0x41, 0xca,
	0x1f, 0xec, 0xc2, 0xc1, 0x24, 0x77, 0x03, 0xd6, 0x26, 0x66, 0x28, 0x53, 0xf7, 0x57, 0x07, 0xd6,
	0x9a, 0x54, 0x04, 0x7a, 0x38, 0xca, 0xba, 0xfe, 0x08, 0xc5, 0x4b, 0x59, 0x1a, 0xbb, 0x94, 0xff,
	0x3f, 0xfd, 0x4d, 0x58, 0x9f, 0x9c, 0x9e, 0x4c, 0x1f, 0xfc, 0xe8, 0x5c, 0xf6, 0x73, 0x0e, 0xdf,
	0x2b, 0x17, 0x36, 0x2f, 0x8f, 0xf8, 0xfb, 0x5c, 0xd2, 0xd3, 0x88, 0xd5, 0x6e, 0x90, 0x7b, 0xb0,
	0x31, 0x81, 0x73, 0x20, 0x90, 0xe2, 0x90, 0xbb, 0xb0, 0x36, 0x81, 0x72, 0x22, 0x59, 0x58, 0x2b,
	0x3d, 0xf8, 0xc7, 0x81, 0x05, 0xaf, 0xd8, 0x40, 0xee, 0xc0, 0x4a, 0x11, 0x69, 0x89, 0x33, 0x1a,
	0xf1, 0xb0, 0x76, 0x83, 0xac, 0xc0, 0x52, 0x31, 0xd4, 0xa4, 0xa2, 0xe6, 0x8c, 0xcf, 0x38, 0xea,
	0x05, 0x01, 0x93, 0xb2, 0x56, 0x22, 0x9b, 0x03, 0x8f, 0x99, 0x90, 0x7d, 0x51, 0x5e, 0xa9, 0x2e,
	0xcb, 0x6a, 0x65, 0x72, 0x1b, 0x6e, 0x15, 0xe3, 0x87, 0xc9, 0x5e, 0xa0, 0x6a, 0x53, 0xa4, 0x0e,
	0xcb, 0x63, 0x5b, 0x05, 0x2c, 0xaa, 0xdd, 0x1c, 0x5f, 0xf2, 0x30, 0x79, 0x25, 0x74, 0x9b, 0x7d,
	0xc1, 0xcf, 0x58, 0x6d, 0x7a, 0x7c, 0xa6, 0xc7, 0xce, 0x38, 0x3b, 0xaf, 0xcd, 0x7c, 0xfb, 0xf5,
	0x9b, 0xc7, 0x9d, 0x24, 0xa2, 0xa2, 0xd3, 0xf8, 0x72, 0x57, 0xa9, 0x46, 0x90, 0xc4, 0x3b, 0xf8,
	0xe7, 0x2c, 0x48, 0xa2, 0x1d, 0xc9, 0xb2, 0x33, 0x1e, 0x30, 0xb9, 0x33, 0xe1, 0x6f, 0xdc, 0xe9,
	0x34, 0x52, 0xbf, 0xf8, 0x2f, 0x00, 0x00, 0xff, 0xff, 0x05, 0x93, 0xc3, 0x67, 0xe8, 0x0d, 0x00,
	0x00,
}
