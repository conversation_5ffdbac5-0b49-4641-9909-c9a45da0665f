// Code generated by protoc-gen-go. DO NOT EDIT.
// source: audit-post/audit-post.proto

package audit_post // import "golang.52tt.com/protocol/services/audit-post"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UpsertReq_UpsertCU int32

const (
	UpsertReq_INSERT             UpsertReq_UpsertCU = 0
	UpsertReq_UPDATEURL          UpsertReq_UpsertCU = 1
	UpsertReq_UPDATECB           UpsertReq_UpsertCU = 2
	UpsertReq_UPDATAAUDIOCONTENT UpsertReq_UpsertCU = 3
	UpsertReq_INSERTWITHURL      UpsertReq_UpsertCU = 4
)

var UpsertReq_UpsertCU_name = map[int32]string{
	0: "INSERT",
	1: "UPDATEURL",
	2: "UPDATECB",
	3: "UPDATAAUDIOCONTENT",
	4: "INSERTWITHURL",
}
var UpsertReq_UpsertCU_value = map[string]int32{
	"INSERT":             0,
	"UPDATEURL":          1,
	"UPDATECB":           2,
	"UPDATAAUDIOCONTENT": 3,
	"INSERTWITHURL":      4,
}

func (x UpsertReq_UpsertCU) String() string {
	return proto.EnumName(UpsertReq_UpsertCU_name, int32(x))
}
func (UpsertReq_UpsertCU) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{12, 0}
}

type TypeResp_PostType int32

const (
	// NONE 意味着消费到的数据并不是帖子的审核结果
	TypeResp_NONE  TypeResp_PostType = 0
	TypeResp_TEXT  TypeResp_PostType = 1
	TypeResp_IMAGE TypeResp_PostType = 2
	TypeResp_AUDIO TypeResp_PostType = 3
	TypeResp_VIDEO TypeResp_PostType = 4
)

var TypeResp_PostType_name = map[int32]string{
	0: "NONE",
	1: "TEXT",
	2: "IMAGE",
	3: "AUDIO",
	4: "VIDEO",
}
var TypeResp_PostType_value = map[string]int32{
	"NONE":  0,
	"TEXT":  1,
	"IMAGE": 2,
	"AUDIO": 3,
	"VIDEO": 4,
}

func (x TypeResp_PostType) String() string {
	return proto.EnumName(TypeResp_PostType_name, int32(x))
}
func (TypeResp_PostType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{15, 0}
}

type UpsertCommentReq_UpsertCU int32

const (
	UpsertCommentReq_INSERT    UpsertCommentReq_UpsertCU = 0
	UpsertCommentReq_UPDATEURL UpsertCommentReq_UpsertCU = 1
	UpsertCommentReq_UPDATECB  UpsertCommentReq_UpsertCU = 2
)

var UpsertCommentReq_UpsertCU_name = map[int32]string{
	0: "INSERT",
	1: "UPDATEURL",
	2: "UPDATECB",
}
var UpsertCommentReq_UpsertCU_value = map[string]int32{
	"INSERT":    0,
	"UPDATEURL": 1,
	"UPDATECB":  2,
}

func (x UpsertCommentReq_UpsertCU) String() string {
	return proto.EnumName(UpsertCommentReq_UpsertCU_name, int32(x))
}
func (UpsertCommentReq_UpsertCU) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{22, 0}
}

type TypeCommentResp_CommentType int32

const (
	// NONE 意味着消费到的数据并不是帖子的审核结果
	TypeCommentResp_NONE  TypeCommentResp_CommentType = 0
	TypeCommentResp_TEXT  TypeCommentResp_CommentType = 1
	TypeCommentResp_IMAGE TypeCommentResp_CommentType = 2
)

var TypeCommentResp_CommentType_name = map[int32]string{
	0: "NONE",
	1: "TEXT",
	2: "IMAGE",
}
var TypeCommentResp_CommentType_value = map[string]int32{
	"NONE":  0,
	"TEXT":  1,
	"IMAGE": 2,
}

func (x TypeCommentResp_CommentType) String() string {
	return proto.EnumName(TypeCommentResp_CommentType_name, int32(x))
}
func (TypeCommentResp_CommentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{25, 0}
}

type BacklogReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Limit                int64    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BacklogReq) Reset()         { *m = BacklogReq{} }
func (m *BacklogReq) String() string { return proto.CompactTextString(m) }
func (*BacklogReq) ProtoMessage()    {}
func (*BacklogReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{0}
}
func (m *BacklogReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BacklogReq.Unmarshal(m, b)
}
func (m *BacklogReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BacklogReq.Marshal(b, m, deterministic)
}
func (dst *BacklogReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BacklogReq.Merge(dst, src)
}
func (m *BacklogReq) XXX_Size() int {
	return xxx_messageInfo_BacklogReq.Size(m)
}
func (m *BacklogReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BacklogReq.DiscardUnknown(m)
}

var xxx_messageInfo_BacklogReq proto.InternalMessageInfo

func (m *BacklogReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *BacklogReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BacklogReq) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type BacklogResp struct {
	Posts                []*BacklogResp_PostInfo `protobuf:"bytes,1,rep,name=posts,proto3" json:"posts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *BacklogResp) Reset()         { *m = BacklogResp{} }
func (m *BacklogResp) String() string { return proto.CompactTextString(m) }
func (*BacklogResp) ProtoMessage()    {}
func (*BacklogResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{1}
}
func (m *BacklogResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BacklogResp.Unmarshal(m, b)
}
func (m *BacklogResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BacklogResp.Marshal(b, m, deterministic)
}
func (dst *BacklogResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BacklogResp.Merge(dst, src)
}
func (m *BacklogResp) XXX_Size() int {
	return xxx_messageInfo_BacklogResp.Size(m)
}
func (m *BacklogResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BacklogResp.DiscardUnknown(m)
}

var xxx_messageInfo_BacklogResp proto.InternalMessageInfo

func (m *BacklogResp) GetPosts() []*BacklogResp_PostInfo {
	if m != nil {
		return m.Posts
	}
	return nil
}

type BacklogResp_PostInfo struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostTime             int64    `protobuf:"varint,3,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	PostText             string   `protobuf:"bytes,4,opt,name=post_text,json=postText,proto3" json:"post_text,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	TextCheckStatus      uint32   `protobuf:"varint,6,opt,name=text_check_status,json=textCheckStatus,proto3" json:"text_check_status,omitempty"`
	VideoCheckStatus     uint32   `protobuf:"varint,7,opt,name=video_check_status,json=videoCheckStatus,proto3" json:"video_check_status,omitempty"`
	AudioCheckStatus     uint32   `protobuf:"varint,8,opt,name=audio_check_status,json=audioCheckStatus,proto3" json:"audio_check_status,omitempty"`
	ImageCheckStatus     uint32   `protobuf:"varint,9,opt,name=image_check_status,json=imageCheckStatus,proto3" json:"image_check_status,omitempty"`
	TopicId              string   `protobuf:"bytes,10,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ImageUrl             []string `protobuf:"bytes,11,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	VideoUrl             []string `protobuf:"bytes,12,rep,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	AudioUrl             []string `protobuf:"bytes,13,rep,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	VideoFrameUrl        []string `protobuf:"bytes,14,rep,name=video_frame_url,json=videoFrameUrl,proto3" json:"video_frame_url,omitempty"`
	AudioText            []string `protobuf:"bytes,15,rep,name=audio_text,json=audioText,proto3" json:"audio_text,omitempty"`
	TextCheckLabel       string   `protobuf:"bytes,16,opt,name=text_check_label,json=textCheckLabel,proto3" json:"text_check_label,omitempty"`
	VideoCheckLabel      string   `protobuf:"bytes,17,opt,name=video_check_label,json=videoCheckLabel,proto3" json:"video_check_label,omitempty"`
	AudioCheckLabel      string   `protobuf:"bytes,18,opt,name=audio_check_label,json=audioCheckLabel,proto3" json:"audio_check_label,omitempty"`
	ImageCheckLabel      string   `protobuf:"bytes,19,opt,name=image_check_label,json=imageCheckLabel,proto3" json:"image_check_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BacklogResp_PostInfo) Reset()         { *m = BacklogResp_PostInfo{} }
func (m *BacklogResp_PostInfo) String() string { return proto.CompactTextString(m) }
func (*BacklogResp_PostInfo) ProtoMessage()    {}
func (*BacklogResp_PostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{1, 0}
}
func (m *BacklogResp_PostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BacklogResp_PostInfo.Unmarshal(m, b)
}
func (m *BacklogResp_PostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BacklogResp_PostInfo.Marshal(b, m, deterministic)
}
func (dst *BacklogResp_PostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BacklogResp_PostInfo.Merge(dst, src)
}
func (m *BacklogResp_PostInfo) XXX_Size() int {
	return xxx_messageInfo_BacklogResp_PostInfo.Size(m)
}
func (m *BacklogResp_PostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BacklogResp_PostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BacklogResp_PostInfo proto.InternalMessageInfo

func (m *BacklogResp_PostInfo) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BacklogResp_PostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *BacklogResp_PostInfo) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *BacklogResp_PostInfo) GetPostText() string {
	if m != nil {
		return m.PostText
	}
	return ""
}

func (m *BacklogResp_PostInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BacklogResp_PostInfo) GetTextCheckStatus() uint32 {
	if m != nil {
		return m.TextCheckStatus
	}
	return 0
}

func (m *BacklogResp_PostInfo) GetVideoCheckStatus() uint32 {
	if m != nil {
		return m.VideoCheckStatus
	}
	return 0
}

func (m *BacklogResp_PostInfo) GetAudioCheckStatus() uint32 {
	if m != nil {
		return m.AudioCheckStatus
	}
	return 0
}

func (m *BacklogResp_PostInfo) GetImageCheckStatus() uint32 {
	if m != nil {
		return m.ImageCheckStatus
	}
	return 0
}

func (m *BacklogResp_PostInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *BacklogResp_PostInfo) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func (m *BacklogResp_PostInfo) GetVideoUrl() []string {
	if m != nil {
		return m.VideoUrl
	}
	return nil
}

func (m *BacklogResp_PostInfo) GetAudioUrl() []string {
	if m != nil {
		return m.AudioUrl
	}
	return nil
}

func (m *BacklogResp_PostInfo) GetVideoFrameUrl() []string {
	if m != nil {
		return m.VideoFrameUrl
	}
	return nil
}

func (m *BacklogResp_PostInfo) GetAudioText() []string {
	if m != nil {
		return m.AudioText
	}
	return nil
}

func (m *BacklogResp_PostInfo) GetTextCheckLabel() string {
	if m != nil {
		return m.TextCheckLabel
	}
	return ""
}

func (m *BacklogResp_PostInfo) GetVideoCheckLabel() string {
	if m != nil {
		return m.VideoCheckLabel
	}
	return ""
}

func (m *BacklogResp_PostInfo) GetAudioCheckLabel() string {
	if m != nil {
		return m.AudioCheckLabel
	}
	return ""
}

func (m *BacklogResp_PostInfo) GetImageCheckLabel() string {
	if m != nil {
		return m.ImageCheckLabel
	}
	return ""
}

type SelectExpiredReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelectExpiredReq) Reset()         { *m = SelectExpiredReq{} }
func (m *SelectExpiredReq) String() string { return proto.CompactTextString(m) }
func (*SelectExpiredReq) ProtoMessage()    {}
func (*SelectExpiredReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{2}
}
func (m *SelectExpiredReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectExpiredReq.Unmarshal(m, b)
}
func (m *SelectExpiredReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectExpiredReq.Marshal(b, m, deterministic)
}
func (dst *SelectExpiredReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectExpiredReq.Merge(dst, src)
}
func (m *SelectExpiredReq) XXX_Size() int {
	return xxx_messageInfo_SelectExpiredReq.Size(m)
}
func (m *SelectExpiredReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectExpiredReq.DiscardUnknown(m)
}

var xxx_messageInfo_SelectExpiredReq proto.InternalMessageInfo

type SelectExpiredResp struct {
	Posts                []*SelectExpiredResp_PostInfo `protobuf:"bytes,1,rep,name=posts,proto3" json:"posts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *SelectExpiredResp) Reset()         { *m = SelectExpiredResp{} }
func (m *SelectExpiredResp) String() string { return proto.CompactTextString(m) }
func (*SelectExpiredResp) ProtoMessage()    {}
func (*SelectExpiredResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{3}
}
func (m *SelectExpiredResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectExpiredResp.Unmarshal(m, b)
}
func (m *SelectExpiredResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectExpiredResp.Marshal(b, m, deterministic)
}
func (dst *SelectExpiredResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectExpiredResp.Merge(dst, src)
}
func (m *SelectExpiredResp) XXX_Size() int {
	return xxx_messageInfo_SelectExpiredResp.Size(m)
}
func (m *SelectExpiredResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectExpiredResp.DiscardUnknown(m)
}

var xxx_messageInfo_SelectExpiredResp proto.InternalMessageInfo

func (m *SelectExpiredResp) GetPosts() []*SelectExpiredResp_PostInfo {
	if m != nil {
		return m.Posts
	}
	return nil
}

type SelectExpiredResp_PostInfo struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostTime             int64    `protobuf:"varint,3,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	PostText             string   `protobuf:"bytes,4,opt,name=post_text,json=postText,proto3" json:"post_text,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	TextCheckStatus      uint32   `protobuf:"varint,6,opt,name=text_check_status,json=textCheckStatus,proto3" json:"text_check_status,omitempty"`
	VideoCheckStatus     uint32   `protobuf:"varint,7,opt,name=video_check_status,json=videoCheckStatus,proto3" json:"video_check_status,omitempty"`
	AudioCheckStatus     uint32   `protobuf:"varint,8,opt,name=audio_check_status,json=audioCheckStatus,proto3" json:"audio_check_status,omitempty"`
	ImageCheckStatus     uint32   `protobuf:"varint,9,opt,name=image_check_status,json=imageCheckStatus,proto3" json:"image_check_status,omitempty"`
	TopicId              string   `protobuf:"bytes,10,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ImageUrl             []string `protobuf:"bytes,11,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	VideoUrl             []string `protobuf:"bytes,12,rep,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	AudioUrl             []string `protobuf:"bytes,13,rep,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	VideoFrameUrl        []string `protobuf:"bytes,14,rep,name=video_frame_url,json=videoFrameUrl,proto3" json:"video_frame_url,omitempty"`
	AudioText            []string `protobuf:"bytes,15,rep,name=audio_text,json=audioText,proto3" json:"audio_text,omitempty"`
	TextCheckLabel       string   `protobuf:"bytes,16,opt,name=text_check_label,json=textCheckLabel,proto3" json:"text_check_label,omitempty"`
	VideoCheckLabel      string   `protobuf:"bytes,17,opt,name=video_check_label,json=videoCheckLabel,proto3" json:"video_check_label,omitempty"`
	AudioCheckLabel      string   `protobuf:"bytes,18,opt,name=audio_check_label,json=audioCheckLabel,proto3" json:"audio_check_label,omitempty"`
	ImageCheckLabel      string   `protobuf:"bytes,19,opt,name=image_check_label,json=imageCheckLabel,proto3" json:"image_check_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelectExpiredResp_PostInfo) Reset()         { *m = SelectExpiredResp_PostInfo{} }
func (m *SelectExpiredResp_PostInfo) String() string { return proto.CompactTextString(m) }
func (*SelectExpiredResp_PostInfo) ProtoMessage()    {}
func (*SelectExpiredResp_PostInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{3, 0}
}
func (m *SelectExpiredResp_PostInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectExpiredResp_PostInfo.Unmarshal(m, b)
}
func (m *SelectExpiredResp_PostInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectExpiredResp_PostInfo.Marshal(b, m, deterministic)
}
func (dst *SelectExpiredResp_PostInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectExpiredResp_PostInfo.Merge(dst, src)
}
func (m *SelectExpiredResp_PostInfo) XXX_Size() int {
	return xxx_messageInfo_SelectExpiredResp_PostInfo.Size(m)
}
func (m *SelectExpiredResp_PostInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectExpiredResp_PostInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SelectExpiredResp_PostInfo proto.InternalMessageInfo

func (m *SelectExpiredResp_PostInfo) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SelectExpiredResp_PostInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *SelectExpiredResp_PostInfo) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *SelectExpiredResp_PostInfo) GetPostText() string {
	if m != nil {
		return m.PostText
	}
	return ""
}

func (m *SelectExpiredResp_PostInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SelectExpiredResp_PostInfo) GetTextCheckStatus() uint32 {
	if m != nil {
		return m.TextCheckStatus
	}
	return 0
}

func (m *SelectExpiredResp_PostInfo) GetVideoCheckStatus() uint32 {
	if m != nil {
		return m.VideoCheckStatus
	}
	return 0
}

func (m *SelectExpiredResp_PostInfo) GetAudioCheckStatus() uint32 {
	if m != nil {
		return m.AudioCheckStatus
	}
	return 0
}

func (m *SelectExpiredResp_PostInfo) GetImageCheckStatus() uint32 {
	if m != nil {
		return m.ImageCheckStatus
	}
	return 0
}

func (m *SelectExpiredResp_PostInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *SelectExpiredResp_PostInfo) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func (m *SelectExpiredResp_PostInfo) GetVideoUrl() []string {
	if m != nil {
		return m.VideoUrl
	}
	return nil
}

func (m *SelectExpiredResp_PostInfo) GetAudioUrl() []string {
	if m != nil {
		return m.AudioUrl
	}
	return nil
}

func (m *SelectExpiredResp_PostInfo) GetVideoFrameUrl() []string {
	if m != nil {
		return m.VideoFrameUrl
	}
	return nil
}

func (m *SelectExpiredResp_PostInfo) GetAudioText() []string {
	if m != nil {
		return m.AudioText
	}
	return nil
}

func (m *SelectExpiredResp_PostInfo) GetTextCheckLabel() string {
	if m != nil {
		return m.TextCheckLabel
	}
	return ""
}

func (m *SelectExpiredResp_PostInfo) GetVideoCheckLabel() string {
	if m != nil {
		return m.VideoCheckLabel
	}
	return ""
}

func (m *SelectExpiredResp_PostInfo) GetAudioCheckLabel() string {
	if m != nil {
		return m.AudioCheckLabel
	}
	return ""
}

func (m *SelectExpiredResp_PostInfo) GetImageCheckLabel() string {
	if m != nil {
		return m.ImageCheckLabel
	}
	return ""
}

type SelectExpiredCommentReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelectExpiredCommentReq) Reset()         { *m = SelectExpiredCommentReq{} }
func (m *SelectExpiredCommentReq) String() string { return proto.CompactTextString(m) }
func (*SelectExpiredCommentReq) ProtoMessage()    {}
func (*SelectExpiredCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{4}
}
func (m *SelectExpiredCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectExpiredCommentReq.Unmarshal(m, b)
}
func (m *SelectExpiredCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectExpiredCommentReq.Marshal(b, m, deterministic)
}
func (dst *SelectExpiredCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectExpiredCommentReq.Merge(dst, src)
}
func (m *SelectExpiredCommentReq) XXX_Size() int {
	return xxx_messageInfo_SelectExpiredCommentReq.Size(m)
}
func (m *SelectExpiredCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectExpiredCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SelectExpiredCommentReq proto.InternalMessageInfo

type SelectExpiredCommentResp struct {
	Comments             []*SelectExpiredCommentResp_CommentInfo `protobuf:"bytes,1,rep,name=comments,proto3" json:"comments,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *SelectExpiredCommentResp) Reset()         { *m = SelectExpiredCommentResp{} }
func (m *SelectExpiredCommentResp) String() string { return proto.CompactTextString(m) }
func (*SelectExpiredCommentResp) ProtoMessage()    {}
func (*SelectExpiredCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{5}
}
func (m *SelectExpiredCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectExpiredCommentResp.Unmarshal(m, b)
}
func (m *SelectExpiredCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectExpiredCommentResp.Marshal(b, m, deterministic)
}
func (dst *SelectExpiredCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectExpiredCommentResp.Merge(dst, src)
}
func (m *SelectExpiredCommentResp) XXX_Size() int {
	return xxx_messageInfo_SelectExpiredCommentResp.Size(m)
}
func (m *SelectExpiredCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectExpiredCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SelectExpiredCommentResp proto.InternalMessageInfo

func (m *SelectExpiredCommentResp) GetComments() []*SelectExpiredCommentResp_CommentInfo {
	if m != nil {
		return m.Comments
	}
	return nil
}

type SelectExpiredCommentResp_CommentInfo struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CommentId            string   `protobuf:"bytes,2,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	CommentTime          int64    `protobuf:"varint,3,opt,name=comment_time,json=commentTime,proto3" json:"comment_time,omitempty"`
	CommentText          string   `protobuf:"bytes,4,opt,name=comment_text,json=commentText,proto3" json:"comment_text,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	TextCheckStatus      uint32   `protobuf:"varint,6,opt,name=text_check_status,json=textCheckStatus,proto3" json:"text_check_status,omitempty"`
	ImageCheckStatus     uint32   `protobuf:"varint,7,opt,name=image_check_status,json=imageCheckStatus,proto3" json:"image_check_status,omitempty"`
	TopicId              string   `protobuf:"bytes,8,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ImageUrl             []string `protobuf:"bytes,9,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	PostId               string   `protobuf:"bytes,10,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TextCheckLabel       string   `protobuf:"bytes,11,opt,name=text_check_label,json=textCheckLabel,proto3" json:"text_check_label,omitempty"`
	ImageCheckLabel      string   `protobuf:"bytes,12,opt,name=image_check_label,json=imageCheckLabel,proto3" json:"image_check_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelectExpiredCommentResp_CommentInfo) Reset()         { *m = SelectExpiredCommentResp_CommentInfo{} }
func (m *SelectExpiredCommentResp_CommentInfo) String() string { return proto.CompactTextString(m) }
func (*SelectExpiredCommentResp_CommentInfo) ProtoMessage()    {}
func (*SelectExpiredCommentResp_CommentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{5, 0}
}
func (m *SelectExpiredCommentResp_CommentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectExpiredCommentResp_CommentInfo.Unmarshal(m, b)
}
func (m *SelectExpiredCommentResp_CommentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectExpiredCommentResp_CommentInfo.Marshal(b, m, deterministic)
}
func (dst *SelectExpiredCommentResp_CommentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectExpiredCommentResp_CommentInfo.Merge(dst, src)
}
func (m *SelectExpiredCommentResp_CommentInfo) XXX_Size() int {
	return xxx_messageInfo_SelectExpiredCommentResp_CommentInfo.Size(m)
}
func (m *SelectExpiredCommentResp_CommentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectExpiredCommentResp_CommentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SelectExpiredCommentResp_CommentInfo proto.InternalMessageInfo

func (m *SelectExpiredCommentResp_CommentInfo) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SelectExpiredCommentResp_CommentInfo) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *SelectExpiredCommentResp_CommentInfo) GetCommentTime() int64 {
	if m != nil {
		return m.CommentTime
	}
	return 0
}

func (m *SelectExpiredCommentResp_CommentInfo) GetCommentText() string {
	if m != nil {
		return m.CommentText
	}
	return ""
}

func (m *SelectExpiredCommentResp_CommentInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SelectExpiredCommentResp_CommentInfo) GetTextCheckStatus() uint32 {
	if m != nil {
		return m.TextCheckStatus
	}
	return 0
}

func (m *SelectExpiredCommentResp_CommentInfo) GetImageCheckStatus() uint32 {
	if m != nil {
		return m.ImageCheckStatus
	}
	return 0
}

func (m *SelectExpiredCommentResp_CommentInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *SelectExpiredCommentResp_CommentInfo) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func (m *SelectExpiredCommentResp_CommentInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *SelectExpiredCommentResp_CommentInfo) GetTextCheckLabel() string {
	if m != nil {
		return m.TextCheckLabel
	}
	return ""
}

func (m *SelectExpiredCommentResp_CommentInfo) GetImageCheckLabel() string {
	if m != nil {
		return m.ImageCheckLabel
	}
	return ""
}

type DeleteReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteReq) Reset()         { *m = DeleteReq{} }
func (m *DeleteReq) String() string { return proto.CompactTextString(m) }
func (*DeleteReq) ProtoMessage()    {}
func (*DeleteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{6}
}
func (m *DeleteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteReq.Unmarshal(m, b)
}
func (m *DeleteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteReq.Marshal(b, m, deterministic)
}
func (dst *DeleteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteReq.Merge(dst, src)
}
func (m *DeleteReq) XXX_Size() int {
	return xxx_messageInfo_DeleteReq.Size(m)
}
func (m *DeleteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteReq proto.InternalMessageInfo

func (m *DeleteReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type DeleteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteResp) Reset()         { *m = DeleteResp{} }
func (m *DeleteResp) String() string { return proto.CompactTextString(m) }
func (*DeleteResp) ProtoMessage()    {}
func (*DeleteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{7}
}
func (m *DeleteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteResp.Unmarshal(m, b)
}
func (m *DeleteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteResp.Marshal(b, m, deterministic)
}
func (dst *DeleteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteResp.Merge(dst, src)
}
func (m *DeleteResp) XXX_Size() int {
	return xxx_messageInfo_DeleteResp.Size(m)
}
func (m *DeleteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteResp proto.InternalMessageInfo

type SelectReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelectReq) Reset()         { *m = SelectReq{} }
func (m *SelectReq) String() string { return proto.CompactTextString(m) }
func (*SelectReq) ProtoMessage()    {}
func (*SelectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{8}
}
func (m *SelectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectReq.Unmarshal(m, b)
}
func (m *SelectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectReq.Marshal(b, m, deterministic)
}
func (dst *SelectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectReq.Merge(dst, src)
}
func (m *SelectReq) XXX_Size() int {
	return xxx_messageInfo_SelectReq.Size(m)
}
func (m *SelectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectReq.DiscardUnknown(m)
}

var xxx_messageInfo_SelectReq proto.InternalMessageInfo

func (m *SelectReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type SelectResp struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostTime             int64    `protobuf:"varint,2,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	PostText             string   `protobuf:"bytes,3,opt,name=post_text,json=postText,proto3" json:"post_text,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	TextCheckStatus      uint32   `protobuf:"varint,5,opt,name=text_check_status,json=textCheckStatus,proto3" json:"text_check_status,omitempty"`
	VideoCheckStatus     uint32   `protobuf:"varint,6,opt,name=video_check_status,json=videoCheckStatus,proto3" json:"video_check_status,omitempty"`
	AudioCheckStatus     uint32   `protobuf:"varint,7,opt,name=audio_check_status,json=audioCheckStatus,proto3" json:"audio_check_status,omitempty"`
	ImageCheckStatus     uint32   `protobuf:"varint,8,opt,name=image_check_status,json=imageCheckStatus,proto3" json:"image_check_status,omitempty"`
	TopicId              string   `protobuf:"bytes,9,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ImageUrl             []string `protobuf:"bytes,10,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	VideoUrl             []string `protobuf:"bytes,11,rep,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	AudioUrl             []string `protobuf:"bytes,12,rep,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	VideoFrameUrl        []string `protobuf:"bytes,13,rep,name=video_frame_url,json=videoFrameUrl,proto3" json:"video_frame_url,omitempty"`
	AudioText            []string `protobuf:"bytes,14,rep,name=audio_text,json=audioText,proto3" json:"audio_text,omitempty"`
	TextCheckLabel       string   `protobuf:"bytes,15,opt,name=text_check_label,json=textCheckLabel,proto3" json:"text_check_label,omitempty"`
	VideoCheckLabel      string   `protobuf:"bytes,16,opt,name=video_check_label,json=videoCheckLabel,proto3" json:"video_check_label,omitempty"`
	AudioCheckLabel      string   `protobuf:"bytes,17,opt,name=audio_check_label,json=audioCheckLabel,proto3" json:"audio_check_label,omitempty"`
	ImageCheckLabel      string   `protobuf:"bytes,18,opt,name=image_check_label,json=imageCheckLabel,proto3" json:"image_check_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelectResp) Reset()         { *m = SelectResp{} }
func (m *SelectResp) String() string { return proto.CompactTextString(m) }
func (*SelectResp) ProtoMessage()    {}
func (*SelectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{9}
}
func (m *SelectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectResp.Unmarshal(m, b)
}
func (m *SelectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectResp.Marshal(b, m, deterministic)
}
func (dst *SelectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectResp.Merge(dst, src)
}
func (m *SelectResp) XXX_Size() int {
	return xxx_messageInfo_SelectResp.Size(m)
}
func (m *SelectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectResp.DiscardUnknown(m)
}

var xxx_messageInfo_SelectResp proto.InternalMessageInfo

func (m *SelectResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *SelectResp) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *SelectResp) GetPostText() string {
	if m != nil {
		return m.PostText
	}
	return ""
}

func (m *SelectResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SelectResp) GetTextCheckStatus() uint32 {
	if m != nil {
		return m.TextCheckStatus
	}
	return 0
}

func (m *SelectResp) GetVideoCheckStatus() uint32 {
	if m != nil {
		return m.VideoCheckStatus
	}
	return 0
}

func (m *SelectResp) GetAudioCheckStatus() uint32 {
	if m != nil {
		return m.AudioCheckStatus
	}
	return 0
}

func (m *SelectResp) GetImageCheckStatus() uint32 {
	if m != nil {
		return m.ImageCheckStatus
	}
	return 0
}

func (m *SelectResp) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *SelectResp) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func (m *SelectResp) GetVideoUrl() []string {
	if m != nil {
		return m.VideoUrl
	}
	return nil
}

func (m *SelectResp) GetAudioUrl() []string {
	if m != nil {
		return m.AudioUrl
	}
	return nil
}

func (m *SelectResp) GetVideoFrameUrl() []string {
	if m != nil {
		return m.VideoFrameUrl
	}
	return nil
}

func (m *SelectResp) GetAudioText() []string {
	if m != nil {
		return m.AudioText
	}
	return nil
}

func (m *SelectResp) GetTextCheckLabel() string {
	if m != nil {
		return m.TextCheckLabel
	}
	return ""
}

func (m *SelectResp) GetVideoCheckLabel() string {
	if m != nil {
		return m.VideoCheckLabel
	}
	return ""
}

func (m *SelectResp) GetAudioCheckLabel() string {
	if m != nil {
		return m.AudioCheckLabel
	}
	return ""
}

func (m *SelectResp) GetImageCheckLabel() string {
	if m != nil {
		return m.ImageCheckLabel
	}
	return ""
}

type ProduceReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	PostText             string   `protobuf:"bytes,5,opt,name=post_text,json=postText,proto3" json:"post_text,omitempty"`
	PostTime             int64    `protobuf:"varint,6,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	VideoUrl             string   `protobuf:"bytes,7,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	VideoFrameUrl        []string `protobuf:"bytes,8,rep,name=video_frame_url,json=videoFrameUrl,proto3" json:"video_frame_url,omitempty"`
	ImageUrl             []string `protobuf:"bytes,9,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	AudioUrl             string   `protobuf:"bytes,10,opt,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	AudioText            string   `protobuf:"bytes,11,opt,name=audio_text,json=audioText,proto3" json:"audio_text,omitempty"`
	Title                string   `protobuf:"bytes,12,opt,name=title,proto3" json:"title,omitempty"`
	PostStatus           uint32   `protobuf:"varint,13,opt,name=post_status,json=postStatus,proto3" json:"post_status,omitempty"`
	CheckLabel           string   `protobuf:"bytes,14,opt,name=check_label,json=checkLabel,proto3" json:"check_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProduceReq) Reset()         { *m = ProduceReq{} }
func (m *ProduceReq) String() string { return proto.CompactTextString(m) }
func (*ProduceReq) ProtoMessage()    {}
func (*ProduceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{10}
}
func (m *ProduceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProduceReq.Unmarshal(m, b)
}
func (m *ProduceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProduceReq.Marshal(b, m, deterministic)
}
func (dst *ProduceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProduceReq.Merge(dst, src)
}
func (m *ProduceReq) XXX_Size() int {
	return xxx_messageInfo_ProduceReq.Size(m)
}
func (m *ProduceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProduceReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProduceReq proto.InternalMessageInfo

func (m *ProduceReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *ProduceReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ProduceReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ProduceReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ProduceReq) GetPostText() string {
	if m != nil {
		return m.PostText
	}
	return ""
}

func (m *ProduceReq) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *ProduceReq) GetVideoUrl() string {
	if m != nil {
		return m.VideoUrl
	}
	return ""
}

func (m *ProduceReq) GetVideoFrameUrl() []string {
	if m != nil {
		return m.VideoFrameUrl
	}
	return nil
}

func (m *ProduceReq) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func (m *ProduceReq) GetAudioUrl() string {
	if m != nil {
		return m.AudioUrl
	}
	return ""
}

func (m *ProduceReq) GetAudioText() string {
	if m != nil {
		return m.AudioText
	}
	return ""
}

func (m *ProduceReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ProduceReq) GetPostStatus() uint32 {
	if m != nil {
		return m.PostStatus
	}
	return 0
}

func (m *ProduceReq) GetCheckLabel() string {
	if m != nil {
		return m.CheckLabel
	}
	return ""
}

type ProduceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProduceResp) Reset()         { *m = ProduceResp{} }
func (m *ProduceResp) String() string { return proto.CompactTextString(m) }
func (*ProduceResp) ProtoMessage()    {}
func (*ProduceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{11}
}
func (m *ProduceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProduceResp.Unmarshal(m, b)
}
func (m *ProduceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProduceResp.Marshal(b, m, deterministic)
}
func (dst *ProduceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProduceResp.Merge(dst, src)
}
func (m *ProduceResp) XXX_Size() int {
	return xxx_messageInfo_ProduceResp.Size(m)
}
func (m *ProduceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProduceResp.DiscardUnknown(m)
}

var xxx_messageInfo_ProduceResp proto.InternalMessageInfo

type UpsertReq struct {
	PostId               string             `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostTime             int64              `protobuf:"varint,2,opt,name=post_time,json=postTime,proto3" json:"post_time,omitempty"`
	PostText             string             `protobuf:"bytes,3,opt,name=post_text,json=postText,proto3" json:"post_text,omitempty"`
	Uid                  uint32             `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	TextCheckStatus      uint32             `protobuf:"varint,5,opt,name=text_check_status,json=textCheckStatus,proto3" json:"text_check_status,omitempty"`
	VideoCheckStatus     uint32             `protobuf:"varint,6,opt,name=video_check_status,json=videoCheckStatus,proto3" json:"video_check_status,omitempty"`
	AudioCheckStatus     uint32             `protobuf:"varint,7,opt,name=audio_check_status,json=audioCheckStatus,proto3" json:"audio_check_status,omitempty"`
	ImageCheckStatus     uint32             `protobuf:"varint,8,opt,name=image_check_status,json=imageCheckStatus,proto3" json:"image_check_status,omitempty"`
	TopicId              string             `protobuf:"bytes,9,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ImageUrl             []string           `protobuf:"bytes,10,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	VideoUrl             []string           `protobuf:"bytes,11,rep,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	AudioUrl             []string           `protobuf:"bytes,12,rep,name=audio_url,json=audioUrl,proto3" json:"audio_url,omitempty"`
	VideoFrameUrl        []string           `protobuf:"bytes,13,rep,name=video_frame_url,json=videoFrameUrl,proto3" json:"video_frame_url,omitempty"`
	AudioText            []string           `protobuf:"bytes,14,rep,name=audio_text,json=audioText,proto3" json:"audio_text,omitempty"`
	UpsertType           UpsertReq_UpsertCU `protobuf:"varint,15,opt,name=upsert_type,json=upsertType,proto3,enum=audio_stream.UpsertReq_UpsertCU" json:"upsert_type,omitempty"`
	TextCheckLabel       string             `protobuf:"bytes,16,opt,name=text_check_label,json=textCheckLabel,proto3" json:"text_check_label,omitempty"`
	VideoCheckLabel      string             `protobuf:"bytes,17,opt,name=video_check_label,json=videoCheckLabel,proto3" json:"video_check_label,omitempty"`
	AudioCheckLabel      string             `protobuf:"bytes,18,opt,name=audio_check_label,json=audioCheckLabel,proto3" json:"audio_check_label,omitempty"`
	ImageCheckLabel      string             `protobuf:"bytes,19,opt,name=image_check_label,json=imageCheckLabel,proto3" json:"image_check_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpsertReq) Reset()         { *m = UpsertReq{} }
func (m *UpsertReq) String() string { return proto.CompactTextString(m) }
func (*UpsertReq) ProtoMessage()    {}
func (*UpsertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{12}
}
func (m *UpsertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertReq.Unmarshal(m, b)
}
func (m *UpsertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertReq.Marshal(b, m, deterministic)
}
func (dst *UpsertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertReq.Merge(dst, src)
}
func (m *UpsertReq) XXX_Size() int {
	return xxx_messageInfo_UpsertReq.Size(m)
}
func (m *UpsertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertReq proto.InternalMessageInfo

func (m *UpsertReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpsertReq) GetPostTime() int64 {
	if m != nil {
		return m.PostTime
	}
	return 0
}

func (m *UpsertReq) GetPostText() string {
	if m != nil {
		return m.PostText
	}
	return ""
}

func (m *UpsertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpsertReq) GetTextCheckStatus() uint32 {
	if m != nil {
		return m.TextCheckStatus
	}
	return 0
}

func (m *UpsertReq) GetVideoCheckStatus() uint32 {
	if m != nil {
		return m.VideoCheckStatus
	}
	return 0
}

func (m *UpsertReq) GetAudioCheckStatus() uint32 {
	if m != nil {
		return m.AudioCheckStatus
	}
	return 0
}

func (m *UpsertReq) GetImageCheckStatus() uint32 {
	if m != nil {
		return m.ImageCheckStatus
	}
	return 0
}

func (m *UpsertReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *UpsertReq) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func (m *UpsertReq) GetVideoUrl() []string {
	if m != nil {
		return m.VideoUrl
	}
	return nil
}

func (m *UpsertReq) GetAudioUrl() []string {
	if m != nil {
		return m.AudioUrl
	}
	return nil
}

func (m *UpsertReq) GetVideoFrameUrl() []string {
	if m != nil {
		return m.VideoFrameUrl
	}
	return nil
}

func (m *UpsertReq) GetAudioText() []string {
	if m != nil {
		return m.AudioText
	}
	return nil
}

func (m *UpsertReq) GetUpsertType() UpsertReq_UpsertCU {
	if m != nil {
		return m.UpsertType
	}
	return UpsertReq_INSERT
}

func (m *UpsertReq) GetTextCheckLabel() string {
	if m != nil {
		return m.TextCheckLabel
	}
	return ""
}

func (m *UpsertReq) GetVideoCheckLabel() string {
	if m != nil {
		return m.VideoCheckLabel
	}
	return ""
}

func (m *UpsertReq) GetAudioCheckLabel() string {
	if m != nil {
		return m.AudioCheckLabel
	}
	return ""
}

func (m *UpsertReq) GetImageCheckLabel() string {
	if m != nil {
		return m.ImageCheckLabel
	}
	return ""
}

type UpsertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertResp) Reset()         { *m = UpsertResp{} }
func (m *UpsertResp) String() string { return proto.CompactTextString(m) }
func (*UpsertResp) ProtoMessage()    {}
func (*UpsertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{13}
}
func (m *UpsertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertResp.Unmarshal(m, b)
}
func (m *UpsertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertResp.Marshal(b, m, deterministic)
}
func (dst *UpsertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertResp.Merge(dst, src)
}
func (m *UpsertResp) XXX_Size() int {
	return xxx_messageInfo_UpsertResp.Size(m)
}
func (m *UpsertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertResp proto.InternalMessageInfo

type TypeReq struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TypeReq) Reset()         { *m = TypeReq{} }
func (m *TypeReq) String() string { return proto.CompactTextString(m) }
func (*TypeReq) ProtoMessage()    {}
func (*TypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{14}
}
func (m *TypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TypeReq.Unmarshal(m, b)
}
func (m *TypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TypeReq.Marshal(b, m, deterministic)
}
func (dst *TypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TypeReq.Merge(dst, src)
}
func (m *TypeReq) XXX_Size() int {
	return xxx_messageInfo_TypeReq.Size(m)
}
func (m *TypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TypeReq proto.InternalMessageInfo

func (m *TypeReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

type TypeResp struct {
	PostType             TypeResp_PostType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=audio_stream.TypeResp_PostType" json:"post_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TypeResp) Reset()         { *m = TypeResp{} }
func (m *TypeResp) String() string { return proto.CompactTextString(m) }
func (*TypeResp) ProtoMessage()    {}
func (*TypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{15}
}
func (m *TypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TypeResp.Unmarshal(m, b)
}
func (m *TypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TypeResp.Marshal(b, m, deterministic)
}
func (dst *TypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TypeResp.Merge(dst, src)
}
func (m *TypeResp) XXX_Size() int {
	return xxx_messageInfo_TypeResp.Size(m)
}
func (m *TypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_TypeResp proto.InternalMessageInfo

func (m *TypeResp) GetPostType() TypeResp_PostType {
	if m != nil {
		return m.PostType
	}
	return TypeResp_NONE
}

type DeleteCommentReq struct {
	CommentId            string   `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteCommentReq) Reset()         { *m = DeleteCommentReq{} }
func (m *DeleteCommentReq) String() string { return proto.CompactTextString(m) }
func (*DeleteCommentReq) ProtoMessage()    {}
func (*DeleteCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{16}
}
func (m *DeleteCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteCommentReq.Unmarshal(m, b)
}
func (m *DeleteCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteCommentReq.Marshal(b, m, deterministic)
}
func (dst *DeleteCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteCommentReq.Merge(dst, src)
}
func (m *DeleteCommentReq) XXX_Size() int {
	return xxx_messageInfo_DeleteCommentReq.Size(m)
}
func (m *DeleteCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteCommentReq proto.InternalMessageInfo

func (m *DeleteCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type DeleteCommentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteCommentResp) Reset()         { *m = DeleteCommentResp{} }
func (m *DeleteCommentResp) String() string { return proto.CompactTextString(m) }
func (*DeleteCommentResp) ProtoMessage()    {}
func (*DeleteCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{17}
}
func (m *DeleteCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteCommentResp.Unmarshal(m, b)
}
func (m *DeleteCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteCommentResp.Marshal(b, m, deterministic)
}
func (dst *DeleteCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteCommentResp.Merge(dst, src)
}
func (m *DeleteCommentResp) XXX_Size() int {
	return xxx_messageInfo_DeleteCommentResp.Size(m)
}
func (m *DeleteCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteCommentResp proto.InternalMessageInfo

type SelectCommentReq struct {
	CommentId            string   `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelectCommentReq) Reset()         { *m = SelectCommentReq{} }
func (m *SelectCommentReq) String() string { return proto.CompactTextString(m) }
func (*SelectCommentReq) ProtoMessage()    {}
func (*SelectCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{18}
}
func (m *SelectCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectCommentReq.Unmarshal(m, b)
}
func (m *SelectCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectCommentReq.Marshal(b, m, deterministic)
}
func (dst *SelectCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectCommentReq.Merge(dst, src)
}
func (m *SelectCommentReq) XXX_Size() int {
	return xxx_messageInfo_SelectCommentReq.Size(m)
}
func (m *SelectCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SelectCommentReq proto.InternalMessageInfo

func (m *SelectCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type SelectCommentResp struct {
	CommentId            string   `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	CommentTime          int64    `protobuf:"varint,2,opt,name=comment_time,json=commentTime,proto3" json:"comment_time,omitempty"`
	CommentText          string   `protobuf:"bytes,3,opt,name=comment_text,json=commentText,proto3" json:"comment_text,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	TextCheckStatus      uint32   `protobuf:"varint,5,opt,name=text_check_status,json=textCheckStatus,proto3" json:"text_check_status,omitempty"`
	ImageCheckStatus     uint32   `protobuf:"varint,6,opt,name=image_check_status,json=imageCheckStatus,proto3" json:"image_check_status,omitempty"`
	TopicId              string   `protobuf:"bytes,7,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ImageUrl             []string `protobuf:"bytes,8,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	PostId               string   `protobuf:"bytes,9,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TextCheckLabel       string   `protobuf:"bytes,10,opt,name=text_check_label,json=textCheckLabel,proto3" json:"text_check_label,omitempty"`
	ImageCheckLabel      string   `protobuf:"bytes,11,opt,name=image_check_label,json=imageCheckLabel,proto3" json:"image_check_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SelectCommentResp) Reset()         { *m = SelectCommentResp{} }
func (m *SelectCommentResp) String() string { return proto.CompactTextString(m) }
func (*SelectCommentResp) ProtoMessage()    {}
func (*SelectCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{19}
}
func (m *SelectCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SelectCommentResp.Unmarshal(m, b)
}
func (m *SelectCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SelectCommentResp.Marshal(b, m, deterministic)
}
func (dst *SelectCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SelectCommentResp.Merge(dst, src)
}
func (m *SelectCommentResp) XXX_Size() int {
	return xxx_messageInfo_SelectCommentResp.Size(m)
}
func (m *SelectCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SelectCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SelectCommentResp proto.InternalMessageInfo

func (m *SelectCommentResp) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *SelectCommentResp) GetCommentTime() int64 {
	if m != nil {
		return m.CommentTime
	}
	return 0
}

func (m *SelectCommentResp) GetCommentText() string {
	if m != nil {
		return m.CommentText
	}
	return ""
}

func (m *SelectCommentResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SelectCommentResp) GetTextCheckStatus() uint32 {
	if m != nil {
		return m.TextCheckStatus
	}
	return 0
}

func (m *SelectCommentResp) GetImageCheckStatus() uint32 {
	if m != nil {
		return m.ImageCheckStatus
	}
	return 0
}

func (m *SelectCommentResp) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *SelectCommentResp) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func (m *SelectCommentResp) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *SelectCommentResp) GetTextCheckLabel() string {
	if m != nil {
		return m.TextCheckLabel
	}
	return ""
}

func (m *SelectCommentResp) GetImageCheckLabel() string {
	if m != nil {
		return m.ImageCheckLabel
	}
	return ""
}

type ProduceCommentReq struct {
	CommentId            string   `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	CommentText          string   `protobuf:"bytes,5,opt,name=comment_text,json=commentText,proto3" json:"comment_text,omitempty"`
	CommentTime          int64    `protobuf:"varint,6,opt,name=comment_time,json=commentTime,proto3" json:"comment_time,omitempty"`
	ImageUrl             []string `protobuf:"bytes,7,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Title                string   `protobuf:"bytes,8,opt,name=title,proto3" json:"title,omitempty"`
	PostId               string   `protobuf:"bytes,9,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentStatus        uint32   `protobuf:"varint,10,opt,name=comment_status,json=commentStatus,proto3" json:"comment_status,omitempty"`
	CheckLabel           string   `protobuf:"bytes,11,opt,name=check_label,json=checkLabel,proto3" json:"check_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProduceCommentReq) Reset()         { *m = ProduceCommentReq{} }
func (m *ProduceCommentReq) String() string { return proto.CompactTextString(m) }
func (*ProduceCommentReq) ProtoMessage()    {}
func (*ProduceCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{20}
}
func (m *ProduceCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProduceCommentReq.Unmarshal(m, b)
}
func (m *ProduceCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProduceCommentReq.Marshal(b, m, deterministic)
}
func (dst *ProduceCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProduceCommentReq.Merge(dst, src)
}
func (m *ProduceCommentReq) XXX_Size() int {
	return xxx_messageInfo_ProduceCommentReq.Size(m)
}
func (m *ProduceCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProduceCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProduceCommentReq proto.InternalMessageInfo

func (m *ProduceCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *ProduceCommentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ProduceCommentReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ProduceCommentReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ProduceCommentReq) GetCommentText() string {
	if m != nil {
		return m.CommentText
	}
	return ""
}

func (m *ProduceCommentReq) GetCommentTime() int64 {
	if m != nil {
		return m.CommentTime
	}
	return 0
}

func (m *ProduceCommentReq) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func (m *ProduceCommentReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ProduceCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *ProduceCommentReq) GetCommentStatus() uint32 {
	if m != nil {
		return m.CommentStatus
	}
	return 0
}

func (m *ProduceCommentReq) GetCheckLabel() string {
	if m != nil {
		return m.CheckLabel
	}
	return ""
}

type ProduceCommentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProduceCommentResp) Reset()         { *m = ProduceCommentResp{} }
func (m *ProduceCommentResp) String() string { return proto.CompactTextString(m) }
func (*ProduceCommentResp) ProtoMessage()    {}
func (*ProduceCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{21}
}
func (m *ProduceCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProduceCommentResp.Unmarshal(m, b)
}
func (m *ProduceCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProduceCommentResp.Marshal(b, m, deterministic)
}
func (dst *ProduceCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProduceCommentResp.Merge(dst, src)
}
func (m *ProduceCommentResp) XXX_Size() int {
	return xxx_messageInfo_ProduceCommentResp.Size(m)
}
func (m *ProduceCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProduceCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_ProduceCommentResp proto.InternalMessageInfo

type UpsertCommentReq struct {
	CommentId            string                    `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	CommentTime          int64                     `protobuf:"varint,2,opt,name=comment_time,json=commentTime,proto3" json:"comment_time,omitempty"`
	CommentText          string                    `protobuf:"bytes,3,opt,name=comment_text,json=commentText,proto3" json:"comment_text,omitempty"`
	Uid                  uint32                    `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	TextCheckStatus      uint32                    `protobuf:"varint,5,opt,name=text_check_status,json=textCheckStatus,proto3" json:"text_check_status,omitempty"`
	ImageCheckStatus     uint32                    `protobuf:"varint,6,opt,name=image_check_status,json=imageCheckStatus,proto3" json:"image_check_status,omitempty"`
	TopicId              string                    `protobuf:"bytes,7,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	ImageUrl             []string                  `protobuf:"bytes,8,rep,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	UpsertType           UpsertCommentReq_UpsertCU `protobuf:"varint,9,opt,name=upsert_type,json=upsertType,proto3,enum=audio_stream.UpsertCommentReq_UpsertCU" json:"upsert_type,omitempty"`
	PostId               string                    `protobuf:"bytes,10,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TextCheckLabel       string                    `protobuf:"bytes,11,opt,name=text_check_label,json=textCheckLabel,proto3" json:"text_check_label,omitempty"`
	ImageCheckLabel      string                    `protobuf:"bytes,12,opt,name=image_check_label,json=imageCheckLabel,proto3" json:"image_check_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UpsertCommentReq) Reset()         { *m = UpsertCommentReq{} }
func (m *UpsertCommentReq) String() string { return proto.CompactTextString(m) }
func (*UpsertCommentReq) ProtoMessage()    {}
func (*UpsertCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{22}
}
func (m *UpsertCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertCommentReq.Unmarshal(m, b)
}
func (m *UpsertCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertCommentReq.Marshal(b, m, deterministic)
}
func (dst *UpsertCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertCommentReq.Merge(dst, src)
}
func (m *UpsertCommentReq) XXX_Size() int {
	return xxx_messageInfo_UpsertCommentReq.Size(m)
}
func (m *UpsertCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertCommentReq proto.InternalMessageInfo

func (m *UpsertCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

func (m *UpsertCommentReq) GetCommentTime() int64 {
	if m != nil {
		return m.CommentTime
	}
	return 0
}

func (m *UpsertCommentReq) GetCommentText() string {
	if m != nil {
		return m.CommentText
	}
	return ""
}

func (m *UpsertCommentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpsertCommentReq) GetTextCheckStatus() uint32 {
	if m != nil {
		return m.TextCheckStatus
	}
	return 0
}

func (m *UpsertCommentReq) GetImageCheckStatus() uint32 {
	if m != nil {
		return m.ImageCheckStatus
	}
	return 0
}

func (m *UpsertCommentReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *UpsertCommentReq) GetImageUrl() []string {
	if m != nil {
		return m.ImageUrl
	}
	return nil
}

func (m *UpsertCommentReq) GetUpsertType() UpsertCommentReq_UpsertCU {
	if m != nil {
		return m.UpsertType
	}
	return UpsertCommentReq_INSERT
}

func (m *UpsertCommentReq) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *UpsertCommentReq) GetTextCheckLabel() string {
	if m != nil {
		return m.TextCheckLabel
	}
	return ""
}

func (m *UpsertCommentReq) GetImageCheckLabel() string {
	if m != nil {
		return m.ImageCheckLabel
	}
	return ""
}

type UpsertCommentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertCommentResp) Reset()         { *m = UpsertCommentResp{} }
func (m *UpsertCommentResp) String() string { return proto.CompactTextString(m) }
func (*UpsertCommentResp) ProtoMessage()    {}
func (*UpsertCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{23}
}
func (m *UpsertCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertCommentResp.Unmarshal(m, b)
}
func (m *UpsertCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertCommentResp.Marshal(b, m, deterministic)
}
func (dst *UpsertCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertCommentResp.Merge(dst, src)
}
func (m *UpsertCommentResp) XXX_Size() int {
	return xxx_messageInfo_UpsertCommentResp.Size(m)
}
func (m *UpsertCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertCommentResp proto.InternalMessageInfo

type TypeCommentReq struct {
	CommentId            string   `protobuf:"bytes,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TypeCommentReq) Reset()         { *m = TypeCommentReq{} }
func (m *TypeCommentReq) String() string { return proto.CompactTextString(m) }
func (*TypeCommentReq) ProtoMessage()    {}
func (*TypeCommentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{24}
}
func (m *TypeCommentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TypeCommentReq.Unmarshal(m, b)
}
func (m *TypeCommentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TypeCommentReq.Marshal(b, m, deterministic)
}
func (dst *TypeCommentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TypeCommentReq.Merge(dst, src)
}
func (m *TypeCommentReq) XXX_Size() int {
	return xxx_messageInfo_TypeCommentReq.Size(m)
}
func (m *TypeCommentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TypeCommentReq.DiscardUnknown(m)
}

var xxx_messageInfo_TypeCommentReq proto.InternalMessageInfo

func (m *TypeCommentReq) GetCommentId() string {
	if m != nil {
		return m.CommentId
	}
	return ""
}

type TypeCommentResp struct {
	CommentType          TypeCommentResp_CommentType `protobuf:"varint,1,opt,name=comment_type,json=commentType,proto3,enum=audio_stream.TypeCommentResp_CommentType" json:"comment_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *TypeCommentResp) Reset()         { *m = TypeCommentResp{} }
func (m *TypeCommentResp) String() string { return proto.CompactTextString(m) }
func (*TypeCommentResp) ProtoMessage()    {}
func (*TypeCommentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audit_post_e28a564dbae8647b, []int{25}
}
func (m *TypeCommentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TypeCommentResp.Unmarshal(m, b)
}
func (m *TypeCommentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TypeCommentResp.Marshal(b, m, deterministic)
}
func (dst *TypeCommentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TypeCommentResp.Merge(dst, src)
}
func (m *TypeCommentResp) XXX_Size() int {
	return xxx_messageInfo_TypeCommentResp.Size(m)
}
func (m *TypeCommentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TypeCommentResp.DiscardUnknown(m)
}

var xxx_messageInfo_TypeCommentResp proto.InternalMessageInfo

func (m *TypeCommentResp) GetCommentType() TypeCommentResp_CommentType {
	if m != nil {
		return m.CommentType
	}
	return TypeCommentResp_NONE
}

func init() {
	proto.RegisterType((*BacklogReq)(nil), "audio_stream.BacklogReq")
	proto.RegisterType((*BacklogResp)(nil), "audio_stream.BacklogResp")
	proto.RegisterType((*BacklogResp_PostInfo)(nil), "audio_stream.BacklogResp.PostInfo")
	proto.RegisterType((*SelectExpiredReq)(nil), "audio_stream.SelectExpiredReq")
	proto.RegisterType((*SelectExpiredResp)(nil), "audio_stream.SelectExpiredResp")
	proto.RegisterType((*SelectExpiredResp_PostInfo)(nil), "audio_stream.SelectExpiredResp.PostInfo")
	proto.RegisterType((*SelectExpiredCommentReq)(nil), "audio_stream.SelectExpiredCommentReq")
	proto.RegisterType((*SelectExpiredCommentResp)(nil), "audio_stream.SelectExpiredCommentResp")
	proto.RegisterType((*SelectExpiredCommentResp_CommentInfo)(nil), "audio_stream.SelectExpiredCommentResp.CommentInfo")
	proto.RegisterType((*DeleteReq)(nil), "audio_stream.DeleteReq")
	proto.RegisterType((*DeleteResp)(nil), "audio_stream.DeleteResp")
	proto.RegisterType((*SelectReq)(nil), "audio_stream.SelectReq")
	proto.RegisterType((*SelectResp)(nil), "audio_stream.SelectResp")
	proto.RegisterType((*ProduceReq)(nil), "audio_stream.ProduceReq")
	proto.RegisterType((*ProduceResp)(nil), "audio_stream.ProduceResp")
	proto.RegisterType((*UpsertReq)(nil), "audio_stream.UpsertReq")
	proto.RegisterType((*UpsertResp)(nil), "audio_stream.UpsertResp")
	proto.RegisterType((*TypeReq)(nil), "audio_stream.TypeReq")
	proto.RegisterType((*TypeResp)(nil), "audio_stream.TypeResp")
	proto.RegisterType((*DeleteCommentReq)(nil), "audio_stream.DeleteCommentReq")
	proto.RegisterType((*DeleteCommentResp)(nil), "audio_stream.DeleteCommentResp")
	proto.RegisterType((*SelectCommentReq)(nil), "audio_stream.SelectCommentReq")
	proto.RegisterType((*SelectCommentResp)(nil), "audio_stream.SelectCommentResp")
	proto.RegisterType((*ProduceCommentReq)(nil), "audio_stream.ProduceCommentReq")
	proto.RegisterType((*ProduceCommentResp)(nil), "audio_stream.ProduceCommentResp")
	proto.RegisterType((*UpsertCommentReq)(nil), "audio_stream.UpsertCommentReq")
	proto.RegisterType((*UpsertCommentResp)(nil), "audio_stream.UpsertCommentResp")
	proto.RegisterType((*TypeCommentReq)(nil), "audio_stream.TypeCommentReq")
	proto.RegisterType((*TypeCommentResp)(nil), "audio_stream.TypeCommentResp")
	proto.RegisterEnum("audio_stream.UpsertReq_UpsertCU", UpsertReq_UpsertCU_name, UpsertReq_UpsertCU_value)
	proto.RegisterEnum("audio_stream.TypeResp_PostType", TypeResp_PostType_name, TypeResp_PostType_value)
	proto.RegisterEnum("audio_stream.UpsertCommentReq_UpsertCU", UpsertCommentReq_UpsertCU_name, UpsertCommentReq_UpsertCU_value)
	proto.RegisterEnum("audio_stream.TypeCommentResp_CommentType", TypeCommentResp_CommentType_name, TypeCommentResp_CommentType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AuditPostClient is the client API for AuditPost service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AuditPostClient interface {
	// Post
	Delete(ctx context.Context, in *DeleteReq, opts ...grpc.CallOption) (*DeleteResp, error)
	Select(ctx context.Context, in *SelectReq, opts ...grpc.CallOption) (*SelectResp, error)
	Produce(ctx context.Context, in *ProduceReq, opts ...grpc.CallOption) (*ProduceResp, error)
	Upsert(ctx context.Context, in *UpsertReq, opts ...grpc.CallOption) (*UpsertResp, error)
	Type(ctx context.Context, in *TypeReq, opts ...grpc.CallOption) (*TypeResp, error)
	SelectExpired(ctx context.Context, in *SelectExpiredReq, opts ...grpc.CallOption) (*SelectExpiredResp, error)
	Backlog(ctx context.Context, in *BacklogReq, opts ...grpc.CallOption) (*BacklogResp, error)
	// Comment
	DeleteComment(ctx context.Context, in *DeleteCommentReq, opts ...grpc.CallOption) (*DeleteCommentResp, error)
	SelectComment(ctx context.Context, in *SelectCommentReq, opts ...grpc.CallOption) (*SelectCommentResp, error)
	ProduceComment(ctx context.Context, in *ProduceCommentReq, opts ...grpc.CallOption) (*ProduceCommentResp, error)
	UpsertComment(ctx context.Context, in *UpsertCommentReq, opts ...grpc.CallOption) (*UpsertCommentResp, error)
	TypeComment(ctx context.Context, in *TypeCommentReq, opts ...grpc.CallOption) (*TypeCommentResp, error)
	SelectExpiredComment(ctx context.Context, in *SelectExpiredCommentReq, opts ...grpc.CallOption) (*SelectExpiredCommentResp, error)
}

type auditPostClient struct {
	cc *grpc.ClientConn
}

func NewAuditPostClient(cc *grpc.ClientConn) AuditPostClient {
	return &auditPostClient{cc}
}

func (c *auditPostClient) Delete(ctx context.Context, in *DeleteReq, opts ...grpc.CallOption) (*DeleteResp, error) {
	out := new(DeleteResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) Select(ctx context.Context, in *SelectReq, opts ...grpc.CallOption) (*SelectResp, error) {
	out := new(SelectResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/Select", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) Produce(ctx context.Context, in *ProduceReq, opts ...grpc.CallOption) (*ProduceResp, error) {
	out := new(ProduceResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/Produce", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) Upsert(ctx context.Context, in *UpsertReq, opts ...grpc.CallOption) (*UpsertResp, error) {
	out := new(UpsertResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/Upsert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) Type(ctx context.Context, in *TypeReq, opts ...grpc.CallOption) (*TypeResp, error) {
	out := new(TypeResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/Type", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) SelectExpired(ctx context.Context, in *SelectExpiredReq, opts ...grpc.CallOption) (*SelectExpiredResp, error) {
	out := new(SelectExpiredResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/SelectExpired", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) Backlog(ctx context.Context, in *BacklogReq, opts ...grpc.CallOption) (*BacklogResp, error) {
	out := new(BacklogResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/Backlog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) DeleteComment(ctx context.Context, in *DeleteCommentReq, opts ...grpc.CallOption) (*DeleteCommentResp, error) {
	out := new(DeleteCommentResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/DeleteComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) SelectComment(ctx context.Context, in *SelectCommentReq, opts ...grpc.CallOption) (*SelectCommentResp, error) {
	out := new(SelectCommentResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/SelectComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) ProduceComment(ctx context.Context, in *ProduceCommentReq, opts ...grpc.CallOption) (*ProduceCommentResp, error) {
	out := new(ProduceCommentResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/ProduceComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) UpsertComment(ctx context.Context, in *UpsertCommentReq, opts ...grpc.CallOption) (*UpsertCommentResp, error) {
	out := new(UpsertCommentResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/UpsertComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) TypeComment(ctx context.Context, in *TypeCommentReq, opts ...grpc.CallOption) (*TypeCommentResp, error) {
	out := new(TypeCommentResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/TypeComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditPostClient) SelectExpiredComment(ctx context.Context, in *SelectExpiredCommentReq, opts ...grpc.CallOption) (*SelectExpiredCommentResp, error) {
	out := new(SelectExpiredCommentResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AuditPost/SelectExpiredComment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuditPostServer is the server API for AuditPost service.
type AuditPostServer interface {
	// Post
	Delete(context.Context, *DeleteReq) (*DeleteResp, error)
	Select(context.Context, *SelectReq) (*SelectResp, error)
	Produce(context.Context, *ProduceReq) (*ProduceResp, error)
	Upsert(context.Context, *UpsertReq) (*UpsertResp, error)
	Type(context.Context, *TypeReq) (*TypeResp, error)
	SelectExpired(context.Context, *SelectExpiredReq) (*SelectExpiredResp, error)
	Backlog(context.Context, *BacklogReq) (*BacklogResp, error)
	// Comment
	DeleteComment(context.Context, *DeleteCommentReq) (*DeleteCommentResp, error)
	SelectComment(context.Context, *SelectCommentReq) (*SelectCommentResp, error)
	ProduceComment(context.Context, *ProduceCommentReq) (*ProduceCommentResp, error)
	UpsertComment(context.Context, *UpsertCommentReq) (*UpsertCommentResp, error)
	TypeComment(context.Context, *TypeCommentReq) (*TypeCommentResp, error)
	SelectExpiredComment(context.Context, *SelectExpiredCommentReq) (*SelectExpiredCommentResp, error)
}

func RegisterAuditPostServer(s *grpc.Server, srv AuditPostServer) {
	s.RegisterService(&_AuditPost_serviceDesc, srv)
}

func _AuditPost_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).Delete(ctx, req.(*DeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_Select_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SelectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).Select(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/Select",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).Select(ctx, req.(*SelectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_Produce_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProduceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).Produce(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/Produce",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).Produce(ctx, req.(*ProduceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_Upsert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).Upsert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/Upsert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).Upsert(ctx, req.(*UpsertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_Type_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).Type(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/Type",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).Type(ctx, req.(*TypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_SelectExpired_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SelectExpiredReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).SelectExpired(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/SelectExpired",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).SelectExpired(ctx, req.(*SelectExpiredReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_Backlog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BacklogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).Backlog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/Backlog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).Backlog(ctx, req.(*BacklogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_DeleteComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).DeleteComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/DeleteComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).DeleteComment(ctx, req.(*DeleteCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_SelectComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SelectCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).SelectComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/SelectComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).SelectComment(ctx, req.(*SelectCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_ProduceComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProduceCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).ProduceComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/ProduceComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).ProduceComment(ctx, req.(*ProduceCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_UpsertComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).UpsertComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/UpsertComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).UpsertComment(ctx, req.(*UpsertCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_TypeComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TypeCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).TypeComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/TypeComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).TypeComment(ctx, req.(*TypeCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditPost_SelectExpiredComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SelectExpiredCommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditPostServer).SelectExpiredComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AuditPost/SelectExpiredComment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditPostServer).SelectExpiredComment(ctx, req.(*SelectExpiredCommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AuditPost_serviceDesc = grpc.ServiceDesc{
	ServiceName: "audio_stream.AuditPost",
	HandlerType: (*AuditPostServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Delete",
			Handler:    _AuditPost_Delete_Handler,
		},
		{
			MethodName: "Select",
			Handler:    _AuditPost_Select_Handler,
		},
		{
			MethodName: "Produce",
			Handler:    _AuditPost_Produce_Handler,
		},
		{
			MethodName: "Upsert",
			Handler:    _AuditPost_Upsert_Handler,
		},
		{
			MethodName: "Type",
			Handler:    _AuditPost_Type_Handler,
		},
		{
			MethodName: "SelectExpired",
			Handler:    _AuditPost_SelectExpired_Handler,
		},
		{
			MethodName: "Backlog",
			Handler:    _AuditPost_Backlog_Handler,
		},
		{
			MethodName: "DeleteComment",
			Handler:    _AuditPost_DeleteComment_Handler,
		},
		{
			MethodName: "SelectComment",
			Handler:    _AuditPost_SelectComment_Handler,
		},
		{
			MethodName: "ProduceComment",
			Handler:    _AuditPost_ProduceComment_Handler,
		},
		{
			MethodName: "UpsertComment",
			Handler:    _AuditPost_UpsertComment_Handler,
		},
		{
			MethodName: "TypeComment",
			Handler:    _AuditPost_TypeComment_Handler,
		},
		{
			MethodName: "SelectExpiredComment",
			Handler:    _AuditPost_SelectExpiredComment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "audit-post/audit-post.proto",
}

func init() {
	proto.RegisterFile("audit-post/audit-post.proto", fileDescriptor_audit_post_e28a564dbae8647b)
}

var fileDescriptor_audit_post_e28a564dbae8647b = []byte{
	// 1511 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0x6f, 0x6f, 0xdb, 0x44,
	0x18, 0x5f, 0xe2, 0x38, 0xb6, 0x1f, 0x27, 0xa9, 0x73, 0x2b, 0x5b, 0x9a, 0x51, 0x5a, 0x2c, 0x36,
	0x0a, 0x1a, 0xa9, 0xe8, 0x34, 0x09, 0x09, 0x34, 0xd4, 0xb5, 0x81, 0x05, 0x8d, 0xb6, 0x4a, 0x53,
	0x40, 0x08, 0x51, 0x65, 0xf6, 0xad, 0x58, 0x4b, 0x62, 0x37, 0x76, 0xa6, 0xed, 0x2d, 0xef, 0x26,
	0x04, 0xef, 0x78, 0x07, 0xdf, 0x81, 0xaf, 0xc1, 0x7b, 0x3e, 0x03, 0x1f, 0x80, 0x4f, 0x80, 0xee,
	0xce, 0x7f, 0xce, 0x17, 0xc7, 0x71, 0xa7, 0x09, 0x69, 0xa2, 0xef, 0x7c, 0xcf, 0xf3, 0xbb, 0xf3,
	0xdd, 0xf3, 0xe7, 0xf7, 0x3c, 0x3e, 0xc3, 0x8d, 0xe1, 0xcc, 0x76, 0x82, 0x0f, 0x3c, 0xd7, 0x0f,
	0xb6, 0x93, 0xc7, 0x8e, 0x37, 0x75, 0x03, 0x17, 0xd5, 0x88, 0xc4, 0x3d, 0xf5, 0x83, 0x29, 0x1e,
	0x8e, 0xcd, 0xef, 0x00, 0xee, 0x0f, 0xad, 0x27, 0x23, 0xf7, 0xac, 0x8f, 0xcf, 0xd1, 0x3a, 0xc0,
	0x23, 0x7c, 0xe6, 0x4c, 0x4e, 0x03, 0x67, 0x8c, 0x5b, 0xa5, 0xcd, 0xd2, 0x96, 0xd4, 0xd7, 0xa8,
	0x64, 0xe0, 0x8c, 0x31, 0x5a, 0x03, 0x15, 0x4f, 0x6c, 0xa6, 0x2c, 0x53, 0xa5, 0x82, 0x27, 0x36,
	0x55, 0xad, 0x82, 0x3c, 0x72, 0xc6, 0x4e, 0xd0, 0x92, 0xa8, 0x9c, 0x0d, 0xcc, 0xbf, 0x65, 0xd0,
	0xe3, 0xe5, 0x7d, 0x0f, 0x7d, 0x04, 0x32, 0xd9, 0x89, 0xdf, 0x2a, 0x6d, 0x4a, 0x5b, 0xfa, 0x8e,
	0xd9, 0xe1, 0xf7, 0xd2, 0xe1, 0x90, 0x9d, 0x23, 0xd7, 0x0f, 0x7a, 0x93, 0xc7, 0x6e, 0x9f, 0x4d,
	0x68, 0xff, 0x26, 0x83, 0x1a, 0xc9, 0x50, 0x03, 0xca, 0x8e, 0x1d, 0x6e, 0xaf, 0xec, 0xd8, 0xe8,
	0x3a, 0x28, 0x04, 0x75, 0xea, 0xd8, 0x74, 0x5b, 0x5a, 0xbf, 0x4a, 0x86, 0x3d, 0x1b, 0xdd, 0x00,
	0x8d, 0x2a, 0xe8, 0x8e, 0xd9, 0xce, 0x54, 0x22, 0xa0, 0x5b, 0x8e, 0x95, 0xf8, 0x59, 0xd0, 0xaa,
	0xd0, 0x79, 0x4c, 0x89, 0x9f, 0x05, 0xc8, 0x00, 0x69, 0xe6, 0xd8, 0x2d, 0x79, 0xb3, 0xb4, 0x55,
	0xef, 0x93, 0x47, 0xf4, 0x3e, 0x34, 0x09, 0xf2, 0xd4, 0xfa, 0x01, 0x5b, 0x4f, 0x4e, 0xfd, 0x60,
	0x18, 0xcc, 0xfc, 0x56, 0x95, 0xea, 0x57, 0x88, 0x62, 0x8f, 0xc8, 0x8f, 0xa9, 0x18, 0xdd, 0x06,
	0xf4, 0xd4, 0xb1, 0xb1, 0x9b, 0x06, 0x2b, 0x14, 0x6c, 0x50, 0x8d, 0x80, 0x66, 0x76, 0x48, 0xa1,
	0x55, 0x86, 0xa6, 0x1a, 0x01, 0xed, 0x8c, 0x87, 0x67, 0x38, 0x8d, 0xd6, 0x18, 0x9a, 0x6a, 0x78,
	0xf4, 0x1a, 0xa8, 0x81, 0xeb, 0x39, 0x16, 0xb1, 0x0d, 0xd0, 0x33, 0x2a, 0x74, 0xcc, 0x8c, 0xc3,
	0x16, 0x9a, 0x4d, 0x47, 0x2d, 0x7d, 0x53, 0x22, 0xe7, 0xa7, 0x82, 0x93, 0xe9, 0x88, 0x28, 0xd9,
	0x09, 0x88, 0xb2, 0xc6, 0x94, 0x54, 0x10, 0x2a, 0xd9, 0x86, 0x89, 0xb2, 0xce, 0x94, 0x54, 0x40,
	0x94, 0xb7, 0x60, 0x85, 0xcd, 0x7c, 0x3c, 0x1d, 0x8e, 0xd9, 0xe2, 0x0d, 0x0a, 0xa9, 0x53, 0xf1,
	0x67, 0x44, 0x4a, 0x70, 0xeb, 0x00, 0x6c, 0x11, 0x6a, 0xff, 0x15, 0x0a, 0x61, 0xcb, 0x52, 0x07,
	0x6c, 0x81, 0xc1, 0x99, 0x7b, 0x34, 0x7c, 0x84, 0x47, 0x2d, 0x83, 0x1e, 0xa0, 0x11, 0x5b, 0xfb,
	0x21, 0x91, 0x12, 0xc7, 0xf0, 0xc6, 0x66, 0xd0, 0x26, 0x85, 0xae, 0x24, 0xb6, 0x8e, 0xb1, 0xbc,
	0xa9, 0x19, 0x16, 0x31, 0x6c, 0x62, 0xe9, 0x18, 0xcb, 0x1b, 0x9a, 0x61, 0xaf, 0x32, 0x6c, 0x62,
	0x67, 0x8a, 0x35, 0x11, 0x18, 0xc7, 0x78, 0x84, 0xad, 0xa0, 0xfb, 0xcc, 0x73, 0xa6, 0xd8, 0xee,
	0xe3, 0x73, 0xf3, 0x1f, 0x19, 0x9a, 0x82, 0xd0, 0xf7, 0xd0, 0xbd, 0x74, 0x0a, 0x6c, 0xa5, 0x53,
	0x60, 0x0e, 0x7f, 0x99, 0x08, 0x97, 0x89, 0xf0, 0x3a, 0x25, 0xc2, 0x1a, 0x5c, 0x4f, 0xc5, 0xf0,
	0x9e, 0x3b, 0x1e, 0xe3, 0x49, 0x40, 0xf2, 0xe1, 0xc7, 0x0a, 0xb4, 0xb2, 0x75, 0xbe, 0x87, 0x0e,
	0x40, 0xb5, 0xd8, 0x30, 0xca, 0x8c, 0x9d, 0x9c, 0xcc, 0xe0, 0x66, 0x76, 0xc2, 0x67, 0x9a, 0x23,
	0xf1, 0x1a, 0xed, 0x5f, 0x25, 0xd0, 0x39, 0xcd, 0x5c, 0xa6, 0xac, 0x03, 0x84, 0xd8, 0x24, 0x59,
	0xb4, 0x50, 0xd2, 0xb3, 0xd1, 0xdb, 0x50, 0x8b, 0xd4, 0x5c, 0xca, 0xe8, 0xa1, 0x8c, 0x66, 0x0d,
	0x0f, 0x49, 0x12, 0x27, 0x86, 0xbc, 0x92, 0xdc, 0xc9, 0x88, 0x6f, 0xa5, 0x40, 0x7c, 0xab, 0x39,
	0xf1, 0xad, 0x09, 0xf1, 0xcd, 0x51, 0x06, 0xa4, 0x28, 0x23, 0x2b, 0xee, 0xf4, 0x45, 0x71, 0x37,
	0x1f, 0x1f, 0xb5, 0xec, 0xf8, 0x78, 0x07, 0xb4, 0x7d, 0x3c, 0xc2, 0x01, 0x26, 0xed, 0x06, 0xf7,
	0xee, 0x12, 0xff, 0x6e, 0xb3, 0x06, 0x10, 0xa1, 0x7c, 0x8f, 0xcc, 0x61, 0xde, 0xcf, 0x9d, 0xf3,
	0x8b, 0x0c, 0x10, 0xc1, 0x7c, 0x6f, 0x21, 0x2e, 0x4d, 0x85, 0xe5, 0x3c, 0x2a, 0x94, 0xb2, 0xa9,
	0xb0, 0xb2, 0xc4, 0x9d, 0xf2, 0x45, 0xa8, 0xb0, 0x7a, 0x21, 0x2a, 0x54, 0x2e, 0x44, 0x85, 0x6a,
	0x81, 0x50, 0xd1, 0x72, 0x42, 0x05, 0xf2, 0xa8, 0x50, 0xcf, 0xa3, 0xc2, 0xda, 0x72, 0x2a, 0xac,
	0x2f, 0xa7, 0xc2, 0x46, 0x11, 0x2a, 0x5c, 0x29, 0x4e, 0x85, 0xc6, 0x05, 0xa8, 0xb0, 0x79, 0x01,
	0x2a, 0x44, 0xd9, 0xa1, 0xfe, 0xbb, 0x04, 0x70, 0x34, 0x75, 0xed, 0x99, 0x95, 0x1b, 0xec, 0x51,
	0x58, 0x95, 0x93, 0xb0, 0x6a, 0x81, 0x32, 0xb4, 0x2c, 0x77, 0x36, 0x89, 0x62, 0x30, 0x1a, 0xa2,
	0x36, 0xa8, 0x13, 0xc7, 0x7a, 0x32, 0x19, 0x8e, 0x71, 0x54, 0xa9, 0xa3, 0x71, 0x3a, 0x76, 0x65,
	0x21, 0x76, 0x53, 0x51, 0x5f, 0x9d, 0x8f, 0xfa, 0xc4, 0xb1, 0x0a, 0x9b, 0x19, 0x3b, 0x36, 0xc3,
	0x77, 0x6a, 0x96, 0xef, 0x72, 0x59, 0x26, 0x15, 0x1d, 0x8c, 0x67, 0x92, 0xe8, 0x48, 0x7b, 0x9d,
	0x71, 0x0c, 0xe7, 0xf5, 0x55, 0x90, 0x03, 0x27, 0x18, 0xe1, 0x90, 0x52, 0xd8, 0x00, 0x6d, 0x80,
	0x4e, 0x0f, 0x14, 0xc6, 0x7a, 0x9d, 0x5a, 0x0f, 0x88, 0x28, 0x8c, 0xf2, 0x0d, 0xd0, 0x79, 0x27,
	0x35, 0xe8, 0x64, 0xb0, 0x12, 0xff, 0xd4, 0x41, 0x8f, 0xdd, 0xe3, 0x7b, 0xe6, 0x1f, 0x55, 0xd0,
	0x4e, 0x3c, 0x1f, 0x4f, 0x73, 0x69, 0xe6, 0x92, 0x3e, 0x5e, 0x5f, 0xfa, 0xd8, 0x05, 0x7d, 0x46,
	0x1d, 0x7c, 0x1a, 0x3c, 0xf7, 0x30, 0x65, 0x8e, 0xc6, 0xce, 0x66, 0xba, 0xcd, 0x88, 0x23, 0x20,
	0x7c, 0xda, 0x3b, 0xe9, 0x03, 0x9b, 0x34, 0x78, 0xee, 0xe1, 0xd7, 0xac, 0x19, 0xfb, 0x1e, 0xd4,
	0xe8, 0x14, 0x08, 0xa0, 0xda, 0x3b, 0x38, 0xee, 0xf6, 0x07, 0xc6, 0x15, 0x54, 0x07, 0xed, 0xe4,
	0x68, 0x7f, 0x77, 0xd0, 0x3d, 0xe9, 0x3f, 0x34, 0x4a, 0xa8, 0x06, 0x2a, 0x1b, 0xee, 0xdd, 0x37,
	0xca, 0xe8, 0x1a, 0x20, 0x3a, 0xda, 0xdd, 0x3d, 0xd9, 0xef, 0x1d, 0xee, 0x1d, 0x1e, 0x0c, 0xba,
	0x07, 0x03, 0x43, 0x42, 0x4d, 0xa8, 0xb3, 0x05, 0xbe, 0xee, 0x0d, 0x1e, 0x90, 0x89, 0x15, 0x52,
	0xa6, 0x23, 0x7b, 0xf9, 0x9e, 0x69, 0x82, 0x42, 0x6c, 0x94, 0x5b, 0xa4, 0x5f, 0x94, 0x40, 0x65,
	0x20, 0xdf, 0x43, 0x9f, 0x44, 0xd9, 0x42, 0xbc, 0x51, 0xa2, 0xde, 0xd8, 0x48, 0x7b, 0x23, 0x82,
	0xd2, 0xaf, 0x20, 0x3a, 0x60, 0xe9, 0xf4, 0xdc, 0xc3, 0xe6, 0xa7, 0xec, 0x3b, 0x88, 0xba, 0x45,
	0x85, 0xca, 0xc1, 0xe1, 0x41, 0xd7, 0xb8, 0x42, 0x9e, 0x06, 0xdd, 0x6f, 0x06, 0x46, 0x09, 0x69,
	0x20, 0xf7, 0xbe, 0xdc, 0xfd, 0xbc, 0x6b, 0x94, 0xc9, 0x23, 0x3d, 0x8c, 0x21, 0x91, 0xc7, 0xaf,
	0x7a, 0xfb, 0xdd, 0x43, 0xa3, 0x62, 0x7e, 0x08, 0x06, 0x6b, 0x32, 0x92, 0x1e, 0x55, 0x68, 0x0b,
	0x4b, 0x42, 0x5b, 0x68, 0x5e, 0x85, 0xa6, 0x30, 0xc5, 0xf7, 0xc8, 0x3a, 0xac, 0xef, 0x28, 0xbe,
	0xce, 0x4f, 0x52, 0xf4, 0x69, 0xc8, 0xf7, 0xc0, 0xf9, 0x93, 0xe6, 0x7a, 0xd2, 0xf2, 0xf2, 0x9e,
	0x54, 0x5a, 0xd8, 0x93, 0xbe, 0x3c, 0x0b, 0x65, 0x30, 0x45, 0xb5, 0x00, 0x53, 0x28, 0x39, 0x4c,
	0xa1, 0x2e, 0xee, 0x49, 0xb5, 0xa5, 0x3d, 0x29, 0x14, 0xef, 0x49, 0xf5, 0xec, 0x34, 0xf9, 0xab,
	0x0c, 0xcd, 0xb0, 0x12, 0x14, 0x76, 0xe1, 0x2b, 0xab, 0xda, 0xa2, 0xcb, 0xe4, 0x79, 0x97, 0x89,
	0x8e, 0xaf, 0xce, 0x3b, 0x3e, 0x65, 0x4e, 0x45, 0x30, 0x67, 0x5c, 0x40, 0x55, 0xbe, 0x80, 0x2e,
	0x34, 0xf2, 0x4d, 0x68, 0x44, 0xaf, 0x0b, 0xfd, 0x0b, 0xf4, 0x90, 0xf5, 0x50, 0x9a, 0x5d, 0x5f,
	0xf5, 0xb9, 0xfa, 0xba, 0x0a, 0x48, 0xb4, 0xaa, 0xef, 0x99, 0x2f, 0x2a, 0x60, 0x84, 0xa4, 0x54,
	0xd8, 0xd6, 0xff, 0x9f, 0xc8, 0x7f, 0x90, 0x2e, 0x51, 0x1a, 0x25, 0xc5, 0x77, 0xb3, 0x4a, 0x54,
	0x62, 0xbd, 0xec, 0x4a, 0xf5, 0x1f, 0x7f, 0xd7, 0xdd, 0x79, 0x89, 0x52, 0x43, 0xe8, 0x54, 0x38,
	0x8c, 0xef, 0x99, 0xdb, 0xd0, 0x20, 0x07, 0x28, 0x4e, 0xa6, 0x3f, 0x97, 0x60, 0x25, 0x35, 0xc3,
	0xf7, 0xd0, 0x43, 0x2e, 0x1c, 0x92, 0xea, 0xf2, 0xde, 0x7c, 0x75, 0xc9, 0xb8, 0x49, 0xa0, 0x75,
	0x26, 0x8e, 0x1c, 0x52, 0x6a, 0x6e, 0xc7, 0x77, 0x09, 0x05, 0xaa, 0xcd, 0xce, 0x9f, 0x0a, 0x68,
	0xbb, 0x33, 0xdb, 0x09, 0x48, 0x79, 0x42, 0x1f, 0x43, 0x95, 0x95, 0x0c, 0x74, 0x3d, 0xfd, 0xf6,
	0xf8, 0x33, 0xb8, 0xdd, 0xca, 0x56, 0xf8, 0x1e, 0x99, 0xcc, 0xca, 0x84, 0x38, 0x39, 0xfe, 0x1e,
	0x16, 0x27, 0x73, 0x5f, 0xc0, 0xf7, 0x40, 0x09, 0xf3, 0x0f, 0x09, 0xa0, 0xe4, 0xab, 0xa4, 0xbd,
	0xb6, 0x40, 0xc3, 0x5e, 0xce, 0xbc, 0x23, 0xbe, 0x3c, 0xee, 0x91, 0xc4, 0x97, 0x27, 0xcd, 0x00,
	0xba, 0x0b, 0x15, 0x6a, 0xab, 0x37, 0xb2, 0x0a, 0xfa, 0x79, 0xfb, 0x5a, 0x76, 0x9d, 0x47, 0x47,
	0x50, 0x4f, 0x5d, 0xf4, 0xa0, 0xb7, 0x72, 0xef, 0x47, 0xcf, 0xdb, 0x1b, 0x4b, 0xee, 0x4f, 0x89,
	0x15, 0xc2, 0xff, 0x0a, 0xa2, 0x15, 0x92, 0xff, 0x1e, 0xa2, 0x15, 0xf8, 0x5f, 0x16, 0x47, 0x50,
	0x4f, 0x95, 0x7c, 0x71, 0x47, 0x62, 0x0b, 0x21, 0xee, 0x68, 0xae, 0x5f, 0x48, 0xce, 0xb8, 0x60,
	0x45, 0xb1, 0x99, 0xc8, 0x3e, 0x23, 0xbf, 0xe2, 0x31, 0x34, 0xd2, 0x4c, 0x8b, 0x36, 0x32, 0xdd,
	0xca, 0xad, 0xb9, 0x99, 0x0f, 0x60, 0xdb, 0x4c, 0x25, 0xa7, 0xb8, 0x4d, 0x91, 0x86, 0xc4, 0x6d,
	0xce, 0x65, 0x36, 0xfa, 0x02, 0x74, 0x2e, 0xe5, 0xd0, 0x9b, 0x39, 0xd9, 0x78, 0xde, 0x5e, 0xcf,
	0xcd, 0x55, 0x84, 0x61, 0x35, 0xeb, 0x46, 0x10, 0xdd, 0x2c, 0x72, 0x6b, 0x78, 0xde, 0xbe, 0x55,
	0xec, 0x72, 0xf1, 0x7e, 0xe7, 0xdb, 0xdb, 0x67, 0xee, 0x68, 0x38, 0x39, 0xeb, 0xdc, 0xdd, 0x09,
	0x82, 0x8e, 0xe5, 0x8e, 0xb7, 0xe9, 0x5f, 0x34, 0xcb, 0x1d, 0x6d, 0xfb, 0x78, 0xfa, 0xd4, 0xb1,
	0xb0, 0xcf, 0xfd, 0x62, 0x7b, 0x54, 0xa5, 0xda, 0x3b, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0xb6,
	0x75, 0xb5, 0x68, 0x82, 0x1b, 0x00, 0x00,
}
