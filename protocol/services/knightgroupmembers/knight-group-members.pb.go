// Code generated by protoc-gen-go. DO NOT EDIT.
// source: knight-group-members/knight-group-members.proto

package knightgroupmembers // import "golang.52tt.com/protocol/services/knightgroupmembers"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type JoinKnightGroupSource int32

const (
	JoinKnightGroupSource_NORMAL    JoinKnightGroupSource = 0
	JoinKnightGroupSource_FREE_CARD JoinKnightGroupSource = 1
)

var JoinKnightGroupSource_name = map[int32]string{
	0: "NORMAL",
	1: "FREE_CARD",
}
var JoinKnightGroupSource_value = map[string]int32{
	"NORMAL":    0,
	"FREE_CARD": 1,
}

func (x JoinKnightGroupSource) String() string {
	return proto.EnumName(JoinKnightGroupSource_name, int32(x))
}
func (JoinKnightGroupSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{0}
}

type GetJoinOrderReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetJoinOrderReq) Reset()         { *m = GetJoinOrderReq{} }
func (m *GetJoinOrderReq) String() string { return proto.CompactTextString(m) }
func (*GetJoinOrderReq) ProtoMessage()    {}
func (*GetJoinOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{0}
}
func (m *GetJoinOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetJoinOrderReq.Unmarshal(m, b)
}
func (m *GetJoinOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetJoinOrderReq.Marshal(b, m, deterministic)
}
func (dst *GetJoinOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetJoinOrderReq.Merge(dst, src)
}
func (m *GetJoinOrderReq) XXX_Size() int {
	return xxx_messageInfo_GetJoinOrderReq.Size(m)
}
func (m *GetJoinOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetJoinOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetJoinOrderReq proto.InternalMessageInfo

func (m *GetJoinOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetJoinOrderResp struct {
	OrderInfo            *JoinKnightOrder `protobuf:"bytes,1,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	DealToken            string           `protobuf:"bytes,2,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetJoinOrderResp) Reset()         { *m = GetJoinOrderResp{} }
func (m *GetJoinOrderResp) String() string { return proto.CompactTextString(m) }
func (*GetJoinOrderResp) ProtoMessage()    {}
func (*GetJoinOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{1}
}
func (m *GetJoinOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetJoinOrderResp.Unmarshal(m, b)
}
func (m *GetJoinOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetJoinOrderResp.Marshal(b, m, deterministic)
}
func (dst *GetJoinOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetJoinOrderResp.Merge(dst, src)
}
func (m *GetJoinOrderResp) XXX_Size() int {
	return xxx_messageInfo_GetJoinOrderResp.Size(m)
}
func (m *GetJoinOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetJoinOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetJoinOrderResp proto.InternalMessageInfo

func (m *GetJoinOrderResp) GetOrderInfo() *JoinKnightOrder {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

func (m *GetJoinOrderResp) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type JoinKnightOrder struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	KnightUid            uint32   `protobuf:"varint,2,opt,name=knight_uid,json=knightUid,proto3" json:"knight_uid,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	ChannelType          uint32   `protobuf:"varint,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelId            uint32   `protobuf:"varint,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GuildId              uint32   `protobuf:"varint,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ServerTime           uint32   `protobuf:"varint,7,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	Price                uint32   `protobuf:"varint,8,opt,name=price,proto3" json:"price,omitempty"`
	BeginTime            uint32   `protobuf:"varint,9,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,10,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Source               uint32   `protobuf:"varint,11,opt,name=source,proto3" json:"source,omitempty"`
	Param                uint32   `protobuf:"varint,12,opt,name=param,proto3" json:"param,omitempty"`
	ParamStr             string   `protobuf:"bytes,13,opt,name=param_str,json=paramStr,proto3" json:"param_str,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinKnightOrder) Reset()         { *m = JoinKnightOrder{} }
func (m *JoinKnightOrder) String() string { return proto.CompactTextString(m) }
func (*JoinKnightOrder) ProtoMessage()    {}
func (*JoinKnightOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{2}
}
func (m *JoinKnightOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinKnightOrder.Unmarshal(m, b)
}
func (m *JoinKnightOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinKnightOrder.Marshal(b, m, deterministic)
}
func (dst *JoinKnightOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinKnightOrder.Merge(dst, src)
}
func (m *JoinKnightOrder) XXX_Size() int {
	return xxx_messageInfo_JoinKnightOrder.Size(m)
}
func (m *JoinKnightOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinKnightOrder.DiscardUnknown(m)
}

var xxx_messageInfo_JoinKnightOrder proto.InternalMessageInfo

func (m *JoinKnightOrder) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JoinKnightOrder) GetKnightUid() uint32 {
	if m != nil {
		return m.KnightUid
	}
	return 0
}

func (m *JoinKnightOrder) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *JoinKnightOrder) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *JoinKnightOrder) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinKnightOrder) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *JoinKnightOrder) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *JoinKnightOrder) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *JoinKnightOrder) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *JoinKnightOrder) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *JoinKnightOrder) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *JoinKnightOrder) GetParam() uint32 {
	if m != nil {
		return m.Param
	}
	return 0
}

func (m *JoinKnightOrder) GetParamStr() string {
	if m != nil {
		return m.ParamStr
	}
	return ""
}

// 查自己在当前房间的骑士信息
type GetKnightInfoReq struct {
	KnightUid            uint32   `protobuf:"varint,1,opt,name=knight_uid,json=knightUid,proto3" json:"knight_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetKnightInfoReq) Reset()         { *m = GetKnightInfoReq{} }
func (m *GetKnightInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightInfoReq) ProtoMessage()    {}
func (*GetKnightInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{3}
}
func (m *GetKnightInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightInfoReq.Unmarshal(m, b)
}
func (m *GetKnightInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetKnightInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightInfoReq.Merge(dst, src)
}
func (m *GetKnightInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightInfoReq.Size(m)
}
func (m *GetKnightInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightInfoReq proto.InternalMessageInfo

func (m *GetKnightInfoReq) GetKnightUid() uint32 {
	if m != nil {
		return m.KnightUid
	}
	return 0
}

func (m *GetKnightInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type KnightInfo struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KnightInfo) Reset()         { *m = KnightInfo{} }
func (m *KnightInfo) String() string { return proto.CompactTextString(m) }
func (*KnightInfo) ProtoMessage()    {}
func (*KnightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{4}
}
func (m *KnightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightInfo.Unmarshal(m, b)
}
func (m *KnightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightInfo.Marshal(b, m, deterministic)
}
func (dst *KnightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightInfo.Merge(dst, src)
}
func (m *KnightInfo) XXX_Size() int {
	return xxx_messageInfo_KnightInfo.Size(m)
}
func (m *KnightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_KnightInfo proto.InternalMessageInfo

func (m *KnightInfo) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *KnightInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *KnightInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetKnightInfoResp struct {
	FirstChief           bool          `protobuf:"varint,1,opt,name=first_chief,json=firstChief,proto3" json:"first_chief,omitempty"`
	KnightInfoList       []*KnightInfo `protobuf:"bytes,2,rep,name=knight_info_list,json=knightInfoList,proto3" json:"knight_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetKnightInfoResp) Reset()         { *m = GetKnightInfoResp{} }
func (m *GetKnightInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightInfoResp) ProtoMessage()    {}
func (*GetKnightInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{5}
}
func (m *GetKnightInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightInfoResp.Unmarshal(m, b)
}
func (m *GetKnightInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetKnightInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightInfoResp.Merge(dst, src)
}
func (m *GetKnightInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightInfoResp.Size(m)
}
func (m *GetKnightInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightInfoResp proto.InternalMessageInfo

func (m *GetKnightInfoResp) GetFirstChief() bool {
	if m != nil {
		return m.FirstChief
	}
	return false
}

func (m *GetKnightInfoResp) GetKnightInfoList() []*KnightInfo {
	if m != nil {
		return m.KnightInfoList
	}
	return nil
}

type KnightMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	TotalDay             uint32   `protobuf:"varint,6,opt,name=total_day,json=totalDay,proto3" json:"total_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KnightMember) Reset()         { *m = KnightMember{} }
func (m *KnightMember) String() string { return proto.CompactTextString(m) }
func (*KnightMember) ProtoMessage()    {}
func (*KnightMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{6}
}
func (m *KnightMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightMember.Unmarshal(m, b)
}
func (m *KnightMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightMember.Marshal(b, m, deterministic)
}
func (dst *KnightMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightMember.Merge(dst, src)
}
func (m *KnightMember) XXX_Size() int {
	return xxx_messageInfo_KnightMember.Size(m)
}
func (m *KnightMember) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightMember.DiscardUnknown(m)
}

var xxx_messageInfo_KnightMember proto.InternalMessageInfo

func (m *KnightMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *KnightMember) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *KnightMember) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *KnightMember) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *KnightMember) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *KnightMember) GetTotalDay() uint32 {
	if m != nil {
		return m.TotalDay
	}
	return 0
}

// 主播取自己骑士团成员列表,需要分页？
type GetKnightGroupMemberReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	WithMemberList       bool     `protobuf:"varint,3,opt,name=with_member_list,json=withMemberList,proto3" json:"with_member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetKnightGroupMemberReq) Reset()         { *m = GetKnightGroupMemberReq{} }
func (m *GetKnightGroupMemberReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightGroupMemberReq) ProtoMessage()    {}
func (*GetKnightGroupMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{7}
}
func (m *GetKnightGroupMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightGroupMemberReq.Unmarshal(m, b)
}
func (m *GetKnightGroupMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightGroupMemberReq.Marshal(b, m, deterministic)
}
func (dst *GetKnightGroupMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightGroupMemberReq.Merge(dst, src)
}
func (m *GetKnightGroupMemberReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightGroupMemberReq.Size(m)
}
func (m *GetKnightGroupMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightGroupMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightGroupMemberReq proto.InternalMessageInfo

func (m *GetKnightGroupMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetKnightGroupMemberReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetKnightGroupMemberReq) GetWithMemberList() bool {
	if m != nil {
		return m.WithMemberList
	}
	return false
}

type GetKnightGroupMemberResp struct {
	TotalMemberCnt       uint32          `protobuf:"varint,1,opt,name=total_member_cnt,json=totalMemberCnt,proto3" json:"total_member_cnt,omitempty"`
	KnightMemberList     []*KnightMember `protobuf:"bytes,2,rep,name=knight_member_list,json=knightMemberList,proto3" json:"knight_member_list,omitempty"`
	ChiefUid             uint32          `protobuf:"varint,3,opt,name=chief_uid,json=chiefUid,proto3" json:"chief_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetKnightGroupMemberResp) Reset()         { *m = GetKnightGroupMemberResp{} }
func (m *GetKnightGroupMemberResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightGroupMemberResp) ProtoMessage()    {}
func (*GetKnightGroupMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{8}
}
func (m *GetKnightGroupMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightGroupMemberResp.Unmarshal(m, b)
}
func (m *GetKnightGroupMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightGroupMemberResp.Marshal(b, m, deterministic)
}
func (dst *GetKnightGroupMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightGroupMemberResp.Merge(dst, src)
}
func (m *GetKnightGroupMemberResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightGroupMemberResp.Size(m)
}
func (m *GetKnightGroupMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightGroupMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightGroupMemberResp proto.InternalMessageInfo

func (m *GetKnightGroupMemberResp) GetTotalMemberCnt() uint32 {
	if m != nil {
		return m.TotalMemberCnt
	}
	return 0
}

func (m *GetKnightGroupMemberResp) GetKnightMemberList() []*KnightMember {
	if m != nil {
		return m.KnightMemberList
	}
	return nil
}

func (m *GetKnightGroupMemberResp) GetChiefUid() uint32 {
	if m != nil {
		return m.ChiefUid
	}
	return 0
}

// 加入骑士团
type JoinKnightGroupReq struct {
	JoinOrder            *JoinKnightOrder `protobuf:"bytes,1,opt,name=join_order,json=joinOrder,proto3" json:"join_order,omitempty"`
	DealToken            string           `protobuf:"bytes,2,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *JoinKnightGroupReq) Reset()         { *m = JoinKnightGroupReq{} }
func (m *JoinKnightGroupReq) String() string { return proto.CompactTextString(m) }
func (*JoinKnightGroupReq) ProtoMessage()    {}
func (*JoinKnightGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{9}
}
func (m *JoinKnightGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinKnightGroupReq.Unmarshal(m, b)
}
func (m *JoinKnightGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinKnightGroupReq.Marshal(b, m, deterministic)
}
func (dst *JoinKnightGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinKnightGroupReq.Merge(dst, src)
}
func (m *JoinKnightGroupReq) XXX_Size() int {
	return xxx_messageInfo_JoinKnightGroupReq.Size(m)
}
func (m *JoinKnightGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinKnightGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinKnightGroupReq proto.InternalMessageInfo

func (m *JoinKnightGroupReq) GetJoinOrder() *JoinKnightOrder {
	if m != nil {
		return m.JoinOrder
	}
	return nil
}

func (m *JoinKnightGroupReq) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

type JoinKnightGroupResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinKnightGroupResp) Reset()         { *m = JoinKnightGroupResp{} }
func (m *JoinKnightGroupResp) String() string { return proto.CompactTextString(m) }
func (*JoinKnightGroupResp) ProtoMessage()    {}
func (*JoinKnightGroupResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{10}
}
func (m *JoinKnightGroupResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinKnightGroupResp.Unmarshal(m, b)
}
func (m *JoinKnightGroupResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinKnightGroupResp.Marshal(b, m, deterministic)
}
func (dst *JoinKnightGroupResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinKnightGroupResp.Merge(dst, src)
}
func (m *JoinKnightGroupResp) XXX_Size() int {
	return xxx_messageInfo_JoinKnightGroupResp.Size(m)
}
func (m *JoinKnightGroupResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinKnightGroupResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinKnightGroupResp proto.InternalMessageInfo

type JoinKnightGroupEvent struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	KnightUid            uint32   `protobuf:"varint,3,opt,name=knight_uid,json=knightUid,proto3" json:"knight_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	GuildId              uint32   `protobuf:"varint,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Price                uint32   `protobuf:"varint,7,opt,name=price,proto3" json:"price,omitempty"`
	CreateTime           uint32   `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	BeginTime            uint32   `protobuf:"varint,9,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,10,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	DealToken            string   `protobuf:"bytes,11,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	Source               uint32   `protobuf:"varint,12,opt,name=source,proto3" json:"source,omitempty"`
	TotalDay             uint32   `protobuf:"varint,13,opt,name=total_day,json=totalDay,proto3" json:"total_day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinKnightGroupEvent) Reset()         { *m = JoinKnightGroupEvent{} }
func (m *JoinKnightGroupEvent) String() string { return proto.CompactTextString(m) }
func (*JoinKnightGroupEvent) ProtoMessage()    {}
func (*JoinKnightGroupEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{11}
}
func (m *JoinKnightGroupEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinKnightGroupEvent.Unmarshal(m, b)
}
func (m *JoinKnightGroupEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinKnightGroupEvent.Marshal(b, m, deterministic)
}
func (dst *JoinKnightGroupEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinKnightGroupEvent.Merge(dst, src)
}
func (m *JoinKnightGroupEvent) XXX_Size() int {
	return xxx_messageInfo_JoinKnightGroupEvent.Size(m)
}
func (m *JoinKnightGroupEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinKnightGroupEvent.DiscardUnknown(m)
}

var xxx_messageInfo_JoinKnightGroupEvent proto.InternalMessageInfo

func (m *JoinKnightGroupEvent) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *JoinKnightGroupEvent) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetKnightUid() uint32 {
	if m != nil {
		return m.KnightUid
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *JoinKnightGroupEvent) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *JoinKnightGroupEvent) GetTotalDay() uint32 {
	if m != nil {
		return m.TotalDay
	}
	return 0
}

type SetFirstChiefReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	KnightUid            uint32   `protobuf:"varint,3,opt,name=knight_uid,json=knightUid,proto3" json:"knight_uid,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	ForceSendMsg         bool     `protobuf:"varint,5,opt,name=force_send_msg,json=forceSendMsg,proto3" json:"force_send_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFirstChiefReq) Reset()         { *m = SetFirstChiefReq{} }
func (m *SetFirstChiefReq) String() string { return proto.CompactTextString(m) }
func (*SetFirstChiefReq) ProtoMessage()    {}
func (*SetFirstChiefReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{12}
}
func (m *SetFirstChiefReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFirstChiefReq.Unmarshal(m, b)
}
func (m *SetFirstChiefReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFirstChiefReq.Marshal(b, m, deterministic)
}
func (dst *SetFirstChiefReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFirstChiefReq.Merge(dst, src)
}
func (m *SetFirstChiefReq) XXX_Size() int {
	return xxx_messageInfo_SetFirstChiefReq.Size(m)
}
func (m *SetFirstChiefReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFirstChiefReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetFirstChiefReq proto.InternalMessageInfo

func (m *SetFirstChiefReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetFirstChiefReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *SetFirstChiefReq) GetKnightUid() uint32 {
	if m != nil {
		return m.KnightUid
	}
	return 0
}

func (m *SetFirstChiefReq) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *SetFirstChiefReq) GetForceSendMsg() bool {
	if m != nil {
		return m.ForceSendMsg
	}
	return false
}

type SetFirstChiefResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetFirstChiefResp) Reset()         { *m = SetFirstChiefResp{} }
func (m *SetFirstChiefResp) String() string { return proto.CompactTextString(m) }
func (*SetFirstChiefResp) ProtoMessage()    {}
func (*SetFirstChiefResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{13}
}
func (m *SetFirstChiefResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetFirstChiefResp.Unmarshal(m, b)
}
func (m *SetFirstChiefResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetFirstChiefResp.Marshal(b, m, deterministic)
}
func (dst *SetFirstChiefResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetFirstChiefResp.Merge(dst, src)
}
func (m *SetFirstChiefResp) XXX_Size() int {
	return xxx_messageInfo_SetFirstChiefResp.Size(m)
}
func (m *SetFirstChiefResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetFirstChiefResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetFirstChiefResp proto.InternalMessageInfo

type BatchGetMemberReq struct {
	Index                uint32   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Off                  uint32   `protobuf:"varint,2,opt,name=off,proto3" json:"off,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetMemberReq) Reset()         { *m = BatchGetMemberReq{} }
func (m *BatchGetMemberReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetMemberReq) ProtoMessage()    {}
func (*BatchGetMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{14}
}
func (m *BatchGetMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMemberReq.Unmarshal(m, b)
}
func (m *BatchGetMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMemberReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMemberReq.Merge(dst, src)
}
func (m *BatchGetMemberReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetMemberReq.Size(m)
}
func (m *BatchGetMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMemberReq proto.InternalMessageInfo

func (m *BatchGetMemberReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *BatchGetMemberReq) GetOff() uint32 {
	if m != nil {
		return m.Off
	}
	return 0
}

func (m *BatchGetMemberReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type BatchGetMemberResp struct {
	KnightMemberList     []*KnightMember `protobuf:"bytes,1,rep,name=knight_member_list,json=knightMemberList,proto3" json:"knight_member_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetMemberResp) Reset()         { *m = BatchGetMemberResp{} }
func (m *BatchGetMemberResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetMemberResp) ProtoMessage()    {}
func (*BatchGetMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{15}
}
func (m *BatchGetMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMemberResp.Unmarshal(m, b)
}
func (m *BatchGetMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMemberResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMemberResp.Merge(dst, src)
}
func (m *BatchGetMemberResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetMemberResp.Size(m)
}
func (m *BatchGetMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMemberResp proto.InternalMessageInfo

func (m *BatchGetMemberResp) GetKnightMemberList() []*KnightMember {
	if m != nil {
		return m.KnightMemberList
	}
	return nil
}

// 检查是否能使用开通骑士团
type CheckCanJoinKnightGroupReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	KnightUid            uint32   `protobuf:"varint,3,opt,name=knight_uid,json=knightUid,proto3" json:"knight_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCanJoinKnightGroupReq) Reset()         { *m = CheckCanJoinKnightGroupReq{} }
func (m *CheckCanJoinKnightGroupReq) String() string { return proto.CompactTextString(m) }
func (*CheckCanJoinKnightGroupReq) ProtoMessage()    {}
func (*CheckCanJoinKnightGroupReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{16}
}
func (m *CheckCanJoinKnightGroupReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanJoinKnightGroupReq.Unmarshal(m, b)
}
func (m *CheckCanJoinKnightGroupReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanJoinKnightGroupReq.Marshal(b, m, deterministic)
}
func (dst *CheckCanJoinKnightGroupReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanJoinKnightGroupReq.Merge(dst, src)
}
func (m *CheckCanJoinKnightGroupReq) XXX_Size() int {
	return xxx_messageInfo_CheckCanJoinKnightGroupReq.Size(m)
}
func (m *CheckCanJoinKnightGroupReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanJoinKnightGroupReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanJoinKnightGroupReq proto.InternalMessageInfo

func (m *CheckCanJoinKnightGroupReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckCanJoinKnightGroupReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *CheckCanJoinKnightGroupReq) GetKnightUid() uint32 {
	if m != nil {
		return m.KnightUid
	}
	return 0
}

type CheckCanJoinKnightGroupRsp struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCanJoinKnightGroupRsp) Reset()         { *m = CheckCanJoinKnightGroupRsp{} }
func (m *CheckCanJoinKnightGroupRsp) String() string { return proto.CompactTextString(m) }
func (*CheckCanJoinKnightGroupRsp) ProtoMessage()    {}
func (*CheckCanJoinKnightGroupRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{17}
}
func (m *CheckCanJoinKnightGroupRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCanJoinKnightGroupRsp.Unmarshal(m, b)
}
func (m *CheckCanJoinKnightGroupRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCanJoinKnightGroupRsp.Marshal(b, m, deterministic)
}
func (dst *CheckCanJoinKnightGroupRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCanJoinKnightGroupRsp.Merge(dst, src)
}
func (m *CheckCanJoinKnightGroupRsp) XXX_Size() int {
	return xxx_messageInfo_CheckCanJoinKnightGroupRsp.Size(m)
}
func (m *CheckCanJoinKnightGroupRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCanJoinKnightGroupRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCanJoinKnightGroupRsp proto.InternalMessageInfo

func (m *CheckCanJoinKnightGroupRsp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CheckCanJoinKnightGroupRsp) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

// 对账接口
// 获得订单数据
type TimeRangeReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Params               string   `protobuf:"bytes,3,opt,name=params,proto3" json:"params,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRangeReq) Reset()         { *m = TimeRangeReq{} }
func (m *TimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*TimeRangeReq) ProtoMessage()    {}
func (*TimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{18}
}
func (m *TimeRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRangeReq.Unmarshal(m, b)
}
func (m *TimeRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRangeReq.Marshal(b, m, deterministic)
}
func (dst *TimeRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRangeReq.Merge(dst, src)
}
func (m *TimeRangeReq) XXX_Size() int {
	return xxx_messageInfo_TimeRangeReq.Size(m)
}
func (m *TimeRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRangeReq proto.InternalMessageInfo

func (m *TimeRangeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeRangeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TimeRangeReq) GetParams() string {
	if m != nil {
		return m.Params
	}
	return ""
}

// 响应order_id个数
type CountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountResp) Reset()         { *m = CountResp{} }
func (m *CountResp) String() string { return proto.CompactTextString(m) }
func (*CountResp) ProtoMessage()    {}
func (*CountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{19}
}
func (m *CountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountResp.Unmarshal(m, b)
}
func (m *CountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountResp.Marshal(b, m, deterministic)
}
func (dst *CountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountResp.Merge(dst, src)
}
func (m *CountResp) XXX_Size() int {
	return xxx_messageInfo_CountResp.Size(m)
}
func (m *CountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CountResp.DiscardUnknown(m)
}

var xxx_messageInfo_CountResp proto.InternalMessageInfo

func (m *CountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CountResp) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

// 响应orderId详情
type OrderIdsResp struct {
	OrderIds             []string `protobuf:"bytes,1,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderIdsResp) Reset()         { *m = OrderIdsResp{} }
func (m *OrderIdsResp) String() string { return proto.CompactTextString(m) }
func (*OrderIdsResp) ProtoMessage()    {}
func (*OrderIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{20}
}
func (m *OrderIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderIdsResp.Unmarshal(m, b)
}
func (m *OrderIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderIdsResp.Marshal(b, m, deterministic)
}
func (dst *OrderIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderIdsResp.Merge(dst, src)
}
func (m *OrderIdsResp) XXX_Size() int {
	return xxx_messageInfo_OrderIdsResp.Size(m)
}
func (m *OrderIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_OrderIdsResp proto.InternalMessageInfo

func (m *OrderIdsResp) GetOrderIds() []string {
	if m != nil {
		return m.OrderIds
	}
	return nil
}

// for test
type ModifyMemberReq struct {
	KnightUid            uint32   `protobuf:"varint,1,opt,name=knight_uid,json=knightUid,proto3" json:"knight_uid,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,4,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	BeginTime            uint32   `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyMemberReq) Reset()         { *m = ModifyMemberReq{} }
func (m *ModifyMemberReq) String() string { return proto.CompactTextString(m) }
func (*ModifyMemberReq) ProtoMessage()    {}
func (*ModifyMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{21}
}
func (m *ModifyMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyMemberReq.Unmarshal(m, b)
}
func (m *ModifyMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyMemberReq.Marshal(b, m, deterministic)
}
func (dst *ModifyMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyMemberReq.Merge(dst, src)
}
func (m *ModifyMemberReq) XXX_Size() int {
	return xxx_messageInfo_ModifyMemberReq.Size(m)
}
func (m *ModifyMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyMemberReq proto.InternalMessageInfo

func (m *ModifyMemberReq) GetKnightUid() uint32 {
	if m != nil {
		return m.KnightUid
	}
	return 0
}

func (m *ModifyMemberReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *ModifyMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ModifyMemberReq) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *ModifyMemberReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

type ModifyMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyMemberResp) Reset()         { *m = ModifyMemberResp{} }
func (m *ModifyMemberResp) String() string { return proto.CompactTextString(m) }
func (*ModifyMemberResp) ProtoMessage()    {}
func (*ModifyMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{22}
}
func (m *ModifyMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyMemberResp.Unmarshal(m, b)
}
func (m *ModifyMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyMemberResp.Marshal(b, m, deterministic)
}
func (dst *ModifyMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyMemberResp.Merge(dst, src)
}
func (m *ModifyMemberResp) XXX_Size() int {
	return xxx_messageInfo_ModifyMemberResp.Size(m)
}
func (m *ModifyMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyMemberResp proto.InternalMessageInfo

// 查询用户开通的所有骑士信息
type GetKnightAllAnchorInfosReq struct {
	KnightUid            uint32   `protobuf:"varint,1,opt,name=knight_uid,json=knightUid,proto3" json:"knight_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetKnightAllAnchorInfosReq) Reset()         { *m = GetKnightAllAnchorInfosReq{} }
func (m *GetKnightAllAnchorInfosReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightAllAnchorInfosReq) ProtoMessage()    {}
func (*GetKnightAllAnchorInfosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{23}
}
func (m *GetKnightAllAnchorInfosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightAllAnchorInfosReq.Unmarshal(m, b)
}
func (m *GetKnightAllAnchorInfosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightAllAnchorInfosReq.Marshal(b, m, deterministic)
}
func (dst *GetKnightAllAnchorInfosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightAllAnchorInfosReq.Merge(dst, src)
}
func (m *GetKnightAllAnchorInfosReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightAllAnchorInfosReq.Size(m)
}
func (m *GetKnightAllAnchorInfosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightAllAnchorInfosReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightAllAnchorInfosReq proto.InternalMessageInfo

func (m *GetKnightAllAnchorInfosReq) GetKnightUid() uint32 {
	if m != nil {
		return m.KnightUid
	}
	return 0
}

type GetKnightAllAnchorInfosRsp struct {
	InfoList             []*KnightMember `protobuf:"bytes,2,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetKnightAllAnchorInfosRsp) Reset()         { *m = GetKnightAllAnchorInfosRsp{} }
func (m *GetKnightAllAnchorInfosRsp) String() string { return proto.CompactTextString(m) }
func (*GetKnightAllAnchorInfosRsp) ProtoMessage()    {}
func (*GetKnightAllAnchorInfosRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{24}
}
func (m *GetKnightAllAnchorInfosRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightAllAnchorInfosRsp.Unmarshal(m, b)
}
func (m *GetKnightAllAnchorInfosRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightAllAnchorInfosRsp.Marshal(b, m, deterministic)
}
func (dst *GetKnightAllAnchorInfosRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightAllAnchorInfosRsp.Merge(dst, src)
}
func (m *GetKnightAllAnchorInfosRsp) XXX_Size() int {
	return xxx_messageInfo_GetKnightAllAnchorInfosRsp.Size(m)
}
func (m *GetKnightAllAnchorInfosRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightAllAnchorInfosRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightAllAnchorInfosRsp proto.InternalMessageInfo

func (m *GetKnightAllAnchorInfosRsp) GetInfoList() []*KnightMember {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 查询所有有骑士的主播信息
type GetAllAnchorHasKnightReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllAnchorHasKnightReq) Reset()         { *m = GetAllAnchorHasKnightReq{} }
func (m *GetAllAnchorHasKnightReq) String() string { return proto.CompactTextString(m) }
func (*GetAllAnchorHasKnightReq) ProtoMessage()    {}
func (*GetAllAnchorHasKnightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{25}
}
func (m *GetAllAnchorHasKnightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllAnchorHasKnightReq.Unmarshal(m, b)
}
func (m *GetAllAnchorHasKnightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllAnchorHasKnightReq.Marshal(b, m, deterministic)
}
func (dst *GetAllAnchorHasKnightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllAnchorHasKnightReq.Merge(dst, src)
}
func (m *GetAllAnchorHasKnightReq) XXX_Size() int {
	return xxx_messageInfo_GetAllAnchorHasKnightReq.Size(m)
}
func (m *GetAllAnchorHasKnightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllAnchorHasKnightReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllAnchorHasKnightReq proto.InternalMessageInfo

type GetAllAnchorHasKnightRsp struct {
	AnchorUids           []uint32 `protobuf:"varint,1,rep,packed,name=anchor_uids,json=anchorUids,proto3" json:"anchor_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllAnchorHasKnightRsp) Reset()         { *m = GetAllAnchorHasKnightRsp{} }
func (m *GetAllAnchorHasKnightRsp) String() string { return proto.CompactTextString(m) }
func (*GetAllAnchorHasKnightRsp) ProtoMessage()    {}
func (*GetAllAnchorHasKnightRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_group_members_829cecea79a560db, []int{26}
}
func (m *GetAllAnchorHasKnightRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllAnchorHasKnightRsp.Unmarshal(m, b)
}
func (m *GetAllAnchorHasKnightRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllAnchorHasKnightRsp.Marshal(b, m, deterministic)
}
func (dst *GetAllAnchorHasKnightRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllAnchorHasKnightRsp.Merge(dst, src)
}
func (m *GetAllAnchorHasKnightRsp) XXX_Size() int {
	return xxx_messageInfo_GetAllAnchorHasKnightRsp.Size(m)
}
func (m *GetAllAnchorHasKnightRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllAnchorHasKnightRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllAnchorHasKnightRsp proto.InternalMessageInfo

func (m *GetAllAnchorHasKnightRsp) GetAnchorUids() []uint32 {
	if m != nil {
		return m.AnchorUids
	}
	return nil
}

func init() {
	proto.RegisterType((*GetJoinOrderReq)(nil), "knightgroupmembers.GetJoinOrderReq")
	proto.RegisterType((*GetJoinOrderResp)(nil), "knightgroupmembers.GetJoinOrderResp")
	proto.RegisterType((*JoinKnightOrder)(nil), "knightgroupmembers.JoinKnightOrder")
	proto.RegisterType((*GetKnightInfoReq)(nil), "knightgroupmembers.GetKnightInfoReq")
	proto.RegisterType((*KnightInfo)(nil), "knightgroupmembers.KnightInfo")
	proto.RegisterType((*GetKnightInfoResp)(nil), "knightgroupmembers.GetKnightInfoResp")
	proto.RegisterType((*KnightMember)(nil), "knightgroupmembers.KnightMember")
	proto.RegisterType((*GetKnightGroupMemberReq)(nil), "knightgroupmembers.GetKnightGroupMemberReq")
	proto.RegisterType((*GetKnightGroupMemberResp)(nil), "knightgroupmembers.GetKnightGroupMemberResp")
	proto.RegisterType((*JoinKnightGroupReq)(nil), "knightgroupmembers.JoinKnightGroupReq")
	proto.RegisterType((*JoinKnightGroupResp)(nil), "knightgroupmembers.JoinKnightGroupResp")
	proto.RegisterType((*JoinKnightGroupEvent)(nil), "knightgroupmembers.JoinKnightGroupEvent")
	proto.RegisterType((*SetFirstChiefReq)(nil), "knightgroupmembers.SetFirstChiefReq")
	proto.RegisterType((*SetFirstChiefResp)(nil), "knightgroupmembers.SetFirstChiefResp")
	proto.RegisterType((*BatchGetMemberReq)(nil), "knightgroupmembers.BatchGetMemberReq")
	proto.RegisterType((*BatchGetMemberResp)(nil), "knightgroupmembers.BatchGetMemberResp")
	proto.RegisterType((*CheckCanJoinKnightGroupReq)(nil), "knightgroupmembers.CheckCanJoinKnightGroupReq")
	proto.RegisterType((*CheckCanJoinKnightGroupRsp)(nil), "knightgroupmembers.CheckCanJoinKnightGroupRsp")
	proto.RegisterType((*TimeRangeReq)(nil), "knightgroupmembers.TimeRangeReq")
	proto.RegisterType((*CountResp)(nil), "knightgroupmembers.CountResp")
	proto.RegisterType((*OrderIdsResp)(nil), "knightgroupmembers.OrderIdsResp")
	proto.RegisterType((*ModifyMemberReq)(nil), "knightgroupmembers.ModifyMemberReq")
	proto.RegisterType((*ModifyMemberResp)(nil), "knightgroupmembers.ModifyMemberResp")
	proto.RegisterType((*GetKnightAllAnchorInfosReq)(nil), "knightgroupmembers.GetKnightAllAnchorInfosReq")
	proto.RegisterType((*GetKnightAllAnchorInfosRsp)(nil), "knightgroupmembers.GetKnightAllAnchorInfosRsp")
	proto.RegisterType((*GetAllAnchorHasKnightReq)(nil), "knightgroupmembers.GetAllAnchorHasKnightReq")
	proto.RegisterType((*GetAllAnchorHasKnightRsp)(nil), "knightgroupmembers.GetAllAnchorHasKnightRsp")
	proto.RegisterEnum("knightgroupmembers.JoinKnightGroupSource", JoinKnightGroupSource_name, JoinKnightGroupSource_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// KnightGroupMembersClient is the client API for KnightGroupMembers service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type KnightGroupMembersClient interface {
	JoinKnightGroup(ctx context.Context, in *JoinKnightGroupReq, opts ...grpc.CallOption) (*JoinKnightGroupResp, error)
	// //骑士查自己在当前房间的信息
	GetKnightInfo(ctx context.Context, in *GetKnightInfoReq, opts ...grpc.CallOption) (*GetKnightInfoResp, error)
	GetKnightGroupMember(ctx context.Context, in *GetKnightGroupMemberReq, opts ...grpc.CallOption) (*GetKnightGroupMemberResp, error)
	TimeRangeCount(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error)
	TimeRangeOrderIds(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*OrderIdsResp, error)
	// 补单-主播积分
	FixKnighAnchorScoreOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
	SetFirstChief(ctx context.Context, in *SetFirstChiefReq, opts ...grpc.CallOption) (*SetFirstChiefResp, error)
	BatchGetMember(ctx context.Context, in *BatchGetMemberReq, opts ...grpc.CallOption) (*BatchGetMemberResp, error)
	ModifyMember(ctx context.Context, in *ModifyMemberReq, opts ...grpc.CallOption) (*ModifyMemberResp, error)
	GetJoinOrder(ctx context.Context, in *GetJoinOrderReq, opts ...grpc.CallOption) (*GetJoinOrderResp, error)
	CheckCanJoinKnightGroup(ctx context.Context, in *CheckCanJoinKnightGroupReq, opts ...grpc.CallOption) (*CheckCanJoinKnightGroupRsp, error)
	// 查询用户开通的所有骑士信息
	GetKnightAllAnchorInfos(ctx context.Context, in *GetKnightAllAnchorInfosReq, opts ...grpc.CallOption) (*GetKnightAllAnchorInfosRsp, error)
	// 查询所有有骑士的主播信息
	GetAllAnchorHasKnight(ctx context.Context, in *GetAllAnchorHasKnightReq, opts ...grpc.CallOption) (*GetAllAnchorHasKnightRsp, error)
}

type knightGroupMembersClient struct {
	cc *grpc.ClientConn
}

func NewKnightGroupMembersClient(cc *grpc.ClientConn) KnightGroupMembersClient {
	return &knightGroupMembersClient{cc}
}

func (c *knightGroupMembersClient) JoinKnightGroup(ctx context.Context, in *JoinKnightGroupReq, opts ...grpc.CallOption) (*JoinKnightGroupResp, error) {
	out := new(JoinKnightGroupResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/JoinKnightGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) GetKnightInfo(ctx context.Context, in *GetKnightInfoReq, opts ...grpc.CallOption) (*GetKnightInfoResp, error) {
	out := new(GetKnightInfoResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/GetKnightInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) GetKnightGroupMember(ctx context.Context, in *GetKnightGroupMemberReq, opts ...grpc.CallOption) (*GetKnightGroupMemberResp, error) {
	out := new(GetKnightGroupMemberResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/GetKnightGroupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) TimeRangeCount(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error) {
	out := new(CountResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/TimeRangeCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) TimeRangeOrderIds(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*OrderIdsResp, error) {
	out := new(OrderIdsResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/TimeRangeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) FixKnighAnchorScoreOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/FixKnighAnchorScoreOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) SetFirstChief(ctx context.Context, in *SetFirstChiefReq, opts ...grpc.CallOption) (*SetFirstChiefResp, error) {
	out := new(SetFirstChiefResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/SetFirstChief", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) BatchGetMember(ctx context.Context, in *BatchGetMemberReq, opts ...grpc.CallOption) (*BatchGetMemberResp, error) {
	out := new(BatchGetMemberResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/BatchGetMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) ModifyMember(ctx context.Context, in *ModifyMemberReq, opts ...grpc.CallOption) (*ModifyMemberResp, error) {
	out := new(ModifyMemberResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/ModifyMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) GetJoinOrder(ctx context.Context, in *GetJoinOrderReq, opts ...grpc.CallOption) (*GetJoinOrderResp, error) {
	out := new(GetJoinOrderResp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/GetJoinOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) CheckCanJoinKnightGroup(ctx context.Context, in *CheckCanJoinKnightGroupReq, opts ...grpc.CallOption) (*CheckCanJoinKnightGroupRsp, error) {
	out := new(CheckCanJoinKnightGroupRsp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/CheckCanJoinKnightGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) GetKnightAllAnchorInfos(ctx context.Context, in *GetKnightAllAnchorInfosReq, opts ...grpc.CallOption) (*GetKnightAllAnchorInfosRsp, error) {
	out := new(GetKnightAllAnchorInfosRsp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/GetKnightAllAnchorInfos", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightGroupMembersClient) GetAllAnchorHasKnight(ctx context.Context, in *GetAllAnchorHasKnightReq, opts ...grpc.CallOption) (*GetAllAnchorHasKnightRsp, error) {
	out := new(GetAllAnchorHasKnightRsp)
	err := c.cc.Invoke(ctx, "/knightgroupmembers.KnightGroupMembers/GetAllAnchorHasKnight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KnightGroupMembersServer is the server API for KnightGroupMembers service.
type KnightGroupMembersServer interface {
	JoinKnightGroup(context.Context, *JoinKnightGroupReq) (*JoinKnightGroupResp, error)
	// //骑士查自己在当前房间的信息
	GetKnightInfo(context.Context, *GetKnightInfoReq) (*GetKnightInfoResp, error)
	GetKnightGroupMember(context.Context, *GetKnightGroupMemberReq) (*GetKnightGroupMemberResp, error)
	TimeRangeCount(context.Context, *TimeRangeReq) (*CountResp, error)
	TimeRangeOrderIds(context.Context, *TimeRangeReq) (*OrderIdsResp, error)
	// 补单-主播积分
	FixKnighAnchorScoreOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
	SetFirstChief(context.Context, *SetFirstChiefReq) (*SetFirstChiefResp, error)
	BatchGetMember(context.Context, *BatchGetMemberReq) (*BatchGetMemberResp, error)
	ModifyMember(context.Context, *ModifyMemberReq) (*ModifyMemberResp, error)
	GetJoinOrder(context.Context, *GetJoinOrderReq) (*GetJoinOrderResp, error)
	CheckCanJoinKnightGroup(context.Context, *CheckCanJoinKnightGroupReq) (*CheckCanJoinKnightGroupRsp, error)
	// 查询用户开通的所有骑士信息
	GetKnightAllAnchorInfos(context.Context, *GetKnightAllAnchorInfosReq) (*GetKnightAllAnchorInfosRsp, error)
	// 查询所有有骑士的主播信息
	GetAllAnchorHasKnight(context.Context, *GetAllAnchorHasKnightReq) (*GetAllAnchorHasKnightRsp, error)
}

func RegisterKnightGroupMembersServer(s *grpc.Server, srv KnightGroupMembersServer) {
	s.RegisterService(&_KnightGroupMembers_serviceDesc, srv)
}

func _KnightGroupMembers_JoinKnightGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinKnightGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).JoinKnightGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/JoinKnightGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).JoinKnightGroup(ctx, req.(*JoinKnightGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_GetKnightInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnightInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).GetKnightInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/GetKnightInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).GetKnightInfo(ctx, req.(*GetKnightInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_GetKnightGroupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnightGroupMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).GetKnightGroupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/GetKnightGroupMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).GetKnightGroupMember(ctx, req.(*GetKnightGroupMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_TimeRangeCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).TimeRangeCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/TimeRangeCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).TimeRangeCount(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_TimeRangeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).TimeRangeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/TimeRangeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).TimeRangeOrderIds(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_FixKnighAnchorScoreOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).FixKnighAnchorScoreOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/FixKnighAnchorScoreOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).FixKnighAnchorScoreOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_SetFirstChief_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetFirstChiefReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).SetFirstChief(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/SetFirstChief",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).SetFirstChief(ctx, req.(*SetFirstChiefReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_BatchGetMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).BatchGetMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/BatchGetMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).BatchGetMember(ctx, req.(*BatchGetMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_ModifyMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).ModifyMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/ModifyMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).ModifyMember(ctx, req.(*ModifyMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_GetJoinOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJoinOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).GetJoinOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/GetJoinOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).GetJoinOrder(ctx, req.(*GetJoinOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_CheckCanJoinKnightGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCanJoinKnightGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).CheckCanJoinKnightGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/CheckCanJoinKnightGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).CheckCanJoinKnightGroup(ctx, req.(*CheckCanJoinKnightGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_GetKnightAllAnchorInfos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnightAllAnchorInfosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).GetKnightAllAnchorInfos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/GetKnightAllAnchorInfos",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).GetKnightAllAnchorInfos(ctx, req.(*GetKnightAllAnchorInfosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightGroupMembers_GetAllAnchorHasKnight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllAnchorHasKnightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightGroupMembersServer).GetAllAnchorHasKnight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knightgroupmembers.KnightGroupMembers/GetAllAnchorHasKnight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightGroupMembersServer).GetAllAnchorHasKnight(ctx, req.(*GetAllAnchorHasKnightReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _KnightGroupMembers_serviceDesc = grpc.ServiceDesc{
	ServiceName: "knightgroupmembers.KnightGroupMembers",
	HandlerType: (*KnightGroupMembersServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "JoinKnightGroup",
			Handler:    _KnightGroupMembers_JoinKnightGroup_Handler,
		},
		{
			MethodName: "GetKnightInfo",
			Handler:    _KnightGroupMembers_GetKnightInfo_Handler,
		},
		{
			MethodName: "GetKnightGroupMember",
			Handler:    _KnightGroupMembers_GetKnightGroupMember_Handler,
		},
		{
			MethodName: "TimeRangeCount",
			Handler:    _KnightGroupMembers_TimeRangeCount_Handler,
		},
		{
			MethodName: "TimeRangeOrderIds",
			Handler:    _KnightGroupMembers_TimeRangeOrderIds_Handler,
		},
		{
			MethodName: "FixKnighAnchorScoreOrder",
			Handler:    _KnightGroupMembers_FixKnighAnchorScoreOrder_Handler,
		},
		{
			MethodName: "SetFirstChief",
			Handler:    _KnightGroupMembers_SetFirstChief_Handler,
		},
		{
			MethodName: "BatchGetMember",
			Handler:    _KnightGroupMembers_BatchGetMember_Handler,
		},
		{
			MethodName: "ModifyMember",
			Handler:    _KnightGroupMembers_ModifyMember_Handler,
		},
		{
			MethodName: "GetJoinOrder",
			Handler:    _KnightGroupMembers_GetJoinOrder_Handler,
		},
		{
			MethodName: "CheckCanJoinKnightGroup",
			Handler:    _KnightGroupMembers_CheckCanJoinKnightGroup_Handler,
		},
		{
			MethodName: "GetKnightAllAnchorInfos",
			Handler:    _KnightGroupMembers_GetKnightAllAnchorInfos_Handler,
		},
		{
			MethodName: "GetAllAnchorHasKnight",
			Handler:    _KnightGroupMembers_GetAllAnchorHasKnight_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "knight-group-members/knight-group-members.proto",
}

func init() {
	proto.RegisterFile("knight-group-members/knight-group-members.proto", fileDescriptor_knight_group_members_829cecea79a560db)
}

var fileDescriptor_knight_group_members_829cecea79a560db = []byte{
	// 1391 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0x36, 0x25, 0xff, 0x48, 0x23, 0xd9, 0x91, 0x37, 0x4e, 0xa2, 0x30, 0x4d, 0x9d, 0x6e, 0xdd,
	0x34, 0x68, 0x12, 0x19, 0x50, 0xff, 0x0e, 0x41, 0x0f, 0xb6, 0xe3, 0xfc, 0xb4, 0x71, 0xd2, 0x52,
	0x49, 0x81, 0x26, 0x40, 0x59, 0x86, 0x5c, 0x49, 0x1b, 0x4b, 0x24, 0xc3, 0x5d, 0x39, 0x11, 0x0a,
	0x14, 0xe8, 0xb1, 0x4f, 0xd0, 0xb7, 0x28, 0xd0, 0x43, 0x7b, 0xef, 0x9b, 0x15, 0x3b, 0x4b, 0x49,
	0x24, 0x45, 0x49, 0x76, 0x83, 0xde, 0xb8, 0x33, 0xb3, 0x33, 0xb3, 0xf3, 0xed, 0x7c, 0xb3, 0x12,
	0xec, 0x1e, 0xfb, 0xbc, 0xd3, 0x95, 0xb7, 0x3b, 0x51, 0x30, 0x08, 0x6f, 0xf7, 0x59, 0xff, 0x25,
	0x8b, 0x44, 0xae, 0xb0, 0x11, 0x46, 0x81, 0x0c, 0x08, 0xd1, 0x3a, 0x54, 0xc5, 0x1a, 0x73, 0x3b,
	0x62, 0x6e, 0xe0, 0xbb, 0xbc, 0xc7, 0x6e, 0x9f, 0x34, 0x77, 0x93, 0x0b, 0xbd, 0x89, 0xde, 0x82,
	0x73, 0xf7, 0x99, 0xfc, 0x3a, 0xe0, 0xfe, 0x93, 0xc8, 0x63, 0x91, 0xc5, 0x5e, 0x93, 0xcb, 0x50,
	0x0a, 0xd4, 0xb7, 0xcd, 0xbd, 0xba, 0x71, 0xcd, 0xb8, 0x51, 0xb6, 0xd6, 0x70, 0xfd, 0xd0, 0xa3,
	0x03, 0xa8, 0xa5, 0xad, 0x45, 0x48, 0xf6, 0x01, 0x62, 0x73, 0xbf, 0x1d, 0xe0, 0x86, 0x4a, 0xf3,
	0xc3, 0xc6, 0x74, 0x2e, 0x0d, 0xb5, 0xed, 0x1b, 0x14, 0xeb, 0xcd, 0x65, 0xed, 0xd5, 0x6f, 0x07,
	0xe4, 0x2a, 0x80, 0xc7, 0x9c, 0x9e, 0x2d, 0x83, 0x63, 0xe6, 0xd7, 0x0b, 0x18, 0xb4, 0xac, 0x24,
	0x4f, 0x95, 0x80, 0xfe, 0x56, 0x84, 0x73, 0x99, 0xdd, 0x73, 0xb2, 0x54, 0xde, 0x74, 0x78, 0x7b,
	0xc0, 0x3d, 0xf4, 0xb6, 0x6e, 0x95, 0xb5, 0xe4, 0x19, 0x47, 0xb5, 0xe3, 0xbb, 0xdd, 0x20, 0x42,
	0x75, 0x51, 0xab, 0xb5, 0x44, 0xa9, 0x3f, 0x80, 0xaa, 0xdb, 0x75, 0x7c, 0x9f, 0xf5, 0x6c, 0x39,
	0x0c, 0x59, 0x7d, 0x19, 0x0d, 0x2a, 0xb1, 0xec, 0xe9, 0x30, 0x64, 0xca, 0xc3, 0xc8, 0x84, 0x7b,
	0xf5, 0x15, 0xed, 0x21, 0x96, 0x3c, 0xf4, 0x54, 0x6a, 0x9d, 0x01, 0xef, 0x79, 0x4a, 0xb9, 0x8a,
	0xca, 0x35, 0x5c, 0x3f, 0xf4, 0xc8, 0x36, 0x54, 0x04, 0x8b, 0x4e, 0x58, 0x64, 0x4b, 0xde, 0x67,
	0xf5, 0x35, 0xd4, 0x82, 0x16, 0x3d, 0xe5, 0x7d, 0x46, 0xb6, 0x60, 0x25, 0x8c, 0xb8, 0xcb, 0xea,
	0x25, 0x54, 0xe9, 0x85, 0x0a, 0xf8, 0x92, 0x75, 0xb8, 0xaf, 0x77, 0x95, 0x75, 0x40, 0x94, 0xe0,
	0xa6, 0x6d, 0xa8, 0xb0, 0xb7, 0x21, 0x8f, 0x98, 0xd6, 0x83, 0xf6, 0xaa, 0x45, 0x68, 0x70, 0x11,
	0x56, 0x45, 0x30, 0x88, 0x5c, 0x56, 0xaf, 0xa0, 0x2e, 0x5e, 0x61, 0x34, 0x27, 0x72, 0xfa, 0xf5,
	0x6a, 0x1c, 0x4d, 0x2d, 0xc8, 0x15, 0x28, 0xe3, 0x87, 0x2d, 0x64, 0x54, 0x5f, 0xc7, 0xda, 0x96,
	0x50, 0xd0, 0x92, 0x11, 0xfd, 0x16, 0xaf, 0x80, 0x46, 0x42, 0x61, 0xa7, 0x6e, 0x4c, 0xba, 0xe0,
	0x46, 0x4e, 0xc1, 0x13, 0xe5, 0x2a, 0x64, 0xca, 0x45, 0x19, 0xc0, 0xc4, 0x5d, 0x06, 0x1d, 0x23,
	0x8b, 0x4e, 0xba, 0x12, 0x85, 0x6c, 0x25, 0x2e, 0x43, 0x89, 0xf9, 0x9e, 0x56, 0x6a, 0x64, 0xd7,
	0x98, 0xef, 0x29, 0x15, 0xfd, 0x05, 0x36, 0x33, 0x89, 0x8b, 0x50, 0x55, 0xae, 0xcd, 0x23, 0x21,
	0x6d, 0xb7, 0xcb, 0x59, 0x1b, 0xc3, 0x95, 0x2c, 0x40, 0xd1, 0x81, 0x92, 0x90, 0x07, 0x50, 0x8b,
	0x8f, 0xa6, 0xae, 0xb7, 0xdd, 0xe3, 0x42, 0xd6, 0x0b, 0xd7, 0x8a, 0x37, 0x2a, 0xcd, 0xf7, 0xf3,
	0xee, 0x78, 0xc2, 0xfd, 0xc6, 0xf1, 0xf8, 0xfb, 0x11, 0x17, 0x92, 0xfe, 0x65, 0x40, 0x55, 0xab,
	0x8f, 0xd0, 0x98, 0xd4, 0xa0, 0x38, 0x39, 0xa2, 0xfa, 0x5c, 0x50, 0xa8, 0x45, 0x17, 0x37, 0x5d,
	0x9a, 0xe5, 0x79, 0xa5, 0x59, 0x49, 0x95, 0x46, 0x01, 0x2e, 0x03, 0xe9, 0xf4, 0x6c, 0xcf, 0x19,
	0xc6, 0x37, 0xb6, 0x84, 0x82, 0xbb, 0xce, 0x90, 0xfe, 0x6a, 0xc0, 0xa5, 0x71, 0xe1, 0xee, 0xab,
	0xc3, 0xea, 0xfc, 0x63, 0xe0, 0x13, 0x09, 0x1b, 0xf3, 0x13, 0x2e, 0x64, 0x13, 0xbe, 0x01, 0xb5,
	0x37, 0x5c, 0x76, 0x6d, 0x5d, 0x3c, 0x5d, 0xdb, 0x22, 0x22, 0xb0, 0xa1, 0xe4, 0x3a, 0x0c, 0xd6,
	0xee, 0x4f, 0x03, 0xea, 0xf9, 0x39, 0x88, 0x50, 0xb9, 0xd1, 0xd9, 0xc7, 0x7e, 0x5c, 0x5f, 0xc6,
	0xa9, 0x6c, 0xa0, 0x5c, 0x9b, 0x1e, 0xf8, 0x92, 0x3c, 0x86, 0x98, 0x23, 0x53, 0x21, 0x35, 0x9c,
	0xd7, 0x66, 0xc3, 0x19, 0xc7, 0x8a, 0x2f, 0xc2, 0x24, 0x2d, 0x55, 0x37, 0xbc, 0x37, 0x09, 0x3c,
	0x4a, 0x28, 0x78, 0xc6, 0x3d, 0xfa, 0x06, 0xc8, 0x84, 0xb3, 0x30, 0x67, 0x55, 0xb1, 0x7d, 0x80,
	0x57, 0x01, 0xf7, 0x6d, 0xe4, 0xaa, 0x33, 0xb1, 0xe5, 0xab, 0x11, 0xeb, 0x2e, 0x62, 0xcb, 0x0b,
	0x70, 0x7e, 0x2a, 0xb0, 0x08, 0xe9, 0xef, 0x45, 0xd8, 0xca, 0xc8, 0x0f, 0x4f, 0x98, 0x2f, 0x17,
	0x30, 0xe9, 0x3c, 0x00, 0xd3, 0x7d, 0x5f, 0x9c, 0xdf, 0xf7, 0xcb, 0xd9, 0xdb, 0x91, 0x25, 0xda,
	0x95, 0x69, 0xa2, 0x9d, 0xc3, 0xa4, 0x63, 0xa2, 0x5c, 0x4b, 0x12, 0xe5, 0x36, 0x54, 0xdc, 0x88,
	0x39, 0x32, 0x66, 0x42, 0x4d, 0xa2, 0xa0, 0x45, 0x78, 0xd5, 0xdf, 0x95, 0x49, 0xd3, 0xb5, 0xaf,
	0x64, 0x6a, 0x9f, 0x20, 0xda, 0x6a, 0x8a, 0x68, 0x53, 0x1d, 0xb6, 0x9e, 0xe9, 0xb0, 0xbf, 0x0d,
	0xa8, 0xb5, 0x98, 0xbc, 0x37, 0x66, 0x9d, 0x77, 0x6f, 0xad, 0x05, 0xc8, 0x64, 0x8e, 0xb9, 0x3c,
	0x75, 0xcc, 0x1d, 0xd8, 0x68, 0x07, 0x91, 0xcb, 0x6c, 0xa1, 0x38, 0xa3, 0x2f, 0x3a, 0x88, 0x4e,
	0xc9, 0xaa, 0xa2, 0xb4, 0xc5, 0x7c, 0xef, 0x48, 0x74, 0xe8, 0x79, 0xd8, 0xcc, 0xe4, 0x2d, 0x42,
	0xfa, 0x1d, 0x6c, 0xee, 0x3b, 0xd2, 0xed, 0xde, 0x67, 0x72, 0x42, 0x14, 0x5b, 0xb0, 0xc2, 0x7d,
	0x8f, 0xbd, 0x8d, 0x0f, 0xa2, 0x17, 0x8a, 0x01, 0x83, 0x76, 0x3b, 0xce, 0x5e, 0x7d, 0x2a, 0x3b,
	0x37, 0x18, 0xf8, 0x32, 0x4e, 0x59, 0x2f, 0xa8, 0x07, 0x24, 0xeb, 0x52, 0x84, 0x33, 0xba, 0xd9,
	0xf8, 0xaf, 0xdd, 0x4c, 0x87, 0x60, 0x1e, 0x74, 0x99, 0x7b, 0x7c, 0xe0, 0xf8, 0x39, 0x8d, 0xfb,
	0x7f, 0xe2, 0x41, 0x9f, 0xcf, 0x0e, 0x2d, 0xc2, 0x54, 0x17, 0x18, 0xe9, 0x2e, 0xc8, 0xf6, 0x50,
	0x61, 0xaa, 0x87, 0xe8, 0x4f, 0x50, 0x55, 0x90, 0x5a, 0x8e, 0xdf, 0x61, 0xf1, 0x41, 0x12, 0x1d,
	0xa0, 0xfc, 0x15, 0x67, 0x8d, 0x89, 0x02, 0x2a, 0xc7, 0x63, 0xe2, 0x22, 0xac, 0xe2, 0x33, 0x40,
	0xe0, 0x01, 0xca, 0x56, 0xbc, 0xa2, 0x5f, 0x42, 0xf9, 0x40, 0xe1, 0x84, 0xa8, 0x8c, 0x11, 0x34,
	0x12, 0x08, 0x2a, 0xe9, 0x89, 0xd3, 0x1b, 0x8c, 0x12, 0xd4, 0x0b, 0x7a, 0x13, 0xaa, 0x4f, 0x34,
	0xd3, 0x08, 0xdc, 0x7b, 0x05, 0xca, 0x23, 0x26, 0x12, 0x08, 0x64, 0xd9, 0x2a, 0xc5, 0x54, 0x24,
	0xe8, 0x1f, 0x06, 0x9c, 0x3b, 0x0a, 0x3c, 0xde, 0x1e, 0xa6, 0xe6, 0xcf, 0x82, 0x87, 0xc7, 0x02,
	0x50, 0x12, 0x90, 0x16, 0xb3, 0x90, 0x2e, 0x6c, 0x92, 0x74, 0x25, 0x57, 0x32, 0x5c, 0x42, 0x09,
	0xd4, 0xd2, 0xf9, 0x8a, 0x90, 0xde, 0x01, 0x73, 0x3c, 0xc7, 0xf6, 0x7a, 0xbd, 0x3d, 0xcc, 0x45,
	0x3d, 0x11, 0xc4, 0xe2, 0xe3, 0xd0, 0x17, 0xb3, 0x37, 0x8b, 0x90, 0x7c, 0x05, 0xe5, 0xec, 0x13,
	0x65, 0x71, 0x17, 0x94, 0xf8, 0xe8, 0x79, 0x62, 0xe2, 0x84, 0x1d, 0xbb, 0x7d, 0xe0, 0x08, 0x6d,
	0x68, 0xb1, 0xd7, 0xf4, 0xce, 0x2c, 0x9d, 0x7e, 0x41, 0x4d, 0x6a, 0xac, 0x51, 0x5b, 0xb7, 0x60,
	0x5c, 0x64, 0xf1, 0x49, 0x13, 0x2e, 0x64, 0xee, 0x74, 0x4b, 0x73, 0x22, 0xc0, 0xea, 0xe3, 0x27,
	0xd6, 0xd1, 0xde, 0xa3, 0xda, 0x12, 0x59, 0x87, 0xf2, 0x3d, 0xeb, 0xf0, 0xd0, 0x3e, 0xd8, 0xb3,
	0xee, 0xd6, 0x8c, 0xe6, 0x3f, 0x00, 0x64, 0x6a, 0xd8, 0x0b, 0xe2, 0x25, 0x7f, 0x06, 0xa0, 0x86,
	0x5c, 0x9f, 0x3f, 0x3b, 0x47, 0xed, 0x6b, 0x7e, 0x7c, 0x2a, 0x3b, 0x11, 0xd2, 0x25, 0xf2, 0x23,
	0xac, 0xa7, 0x1e, 0x8a, 0x64, 0x27, 0x6f, 0x6f, 0xf6, 0x11, 0x6c, 0x7e, 0x74, 0x0a, 0x2b, 0xf4,
	0x2f, 0x60, 0x2b, 0xef, 0x2d, 0x43, 0x6e, 0xce, 0x75, 0x90, 0x7e, 0x79, 0x99, 0xb7, 0x4e, 0x6f,
	0x8c, 0x41, 0x5b, 0xb0, 0x31, 0x66, 0x01, 0x6c, 0x56, 0x92, 0x7b, 0x39, 0x92, 0x4c, 0x61, 0x5e,
	0xcd, 0xb3, 0x18, 0x77, 0x3a, 0x5d, 0x22, 0x3f, 0xc0, 0xe6, 0x78, 0xc3, 0xa8, 0x91, 0x4f, 0xe1,
	0x37, 0xd7, 0x22, 0x49, 0x04, 0x74, 0x89, 0x58, 0x50, 0xbf, 0xc7, 0xdf, 0xe2, 0x69, 0xf4, 0xa5,
	0x6b, 0xb9, 0x41, 0xa4, 0x83, 0x90, 0xf7, 0x1a, 0xd6, 0xe8, 0x87, 0xec, 0xf7, 0xcd, 0x86, 0xc5,
	0xc2, 0x9e, 0xe3, 0xb2, 0xd1, 0xcf, 0x57, 0xf3, 0x62, 0x4a, 0x7b, 0xd8, 0x0f, 0xe5, 0x70, 0x02,
	0x6c, 0x6a, 0x5c, 0xe5, 0x03, 0x9b, 0x9d, 0xc4, 0xf9, 0xc0, 0x4e, 0xcf, 0xbd, 0x25, 0xe2, 0xc0,
	0x46, 0x7a, 0x4c, 0x91, 0xdc, 0xad, 0x53, 0xd3, 0xd1, 0xbc, 0x7e, 0x1a, 0x33, 0x0c, 0xf1, 0x02,
	0xaa, 0x49, 0x4e, 0x21, 0xb9, 0x4f, 0xc7, 0x0c, 0x4b, 0x9a, 0x3b, 0x8b, 0x8d, 0x46, 0xce, 0x93,
	0xbf, 0xee, 0xf3, 0x9d, 0x67, 0xfe, 0x2d, 0x30, 0x77, 0x16, 0x1b, 0xa1, 0xf3, 0x9f, 0xe1, 0xd2,
	0x8c, 0x11, 0x47, 0x1a, 0xb9, 0xf7, 0x6c, 0xe6, 0x28, 0x36, 0xcf, 0x64, 0x3f, 0x0a, 0x3e, 0x83,
	0x39, 0xf3, 0x83, 0xcf, 0xe6, 0x68, 0xf3, 0x4c, 0xf6, 0x18, 0x7c, 0x00, 0x17, 0x72, 0xd9, 0x93,
	0xcc, 0xea, 0xe1, 0x5c, 0x12, 0x36, 0xcf, 0x60, 0xad, 0xc2, 0xee, 0x7f, 0xf1, 0xfc, 0xb3, 0x4e,
	0xd0, 0x73, 0xfc, 0x4e, 0xe3, 0xf3, 0xa6, 0x94, 0x0d, 0x37, 0xe8, 0xef, 0xe2, 0x5f, 0x3e, 0x6e,
	0xd0, 0xdb, 0x15, 0x2c, 0x3a, 0xe1, 0x2e, 0x1b, 0xfd, 0x9d, 0x94, 0x74, 0xf9, 0x72, 0x15, 0xad,
	0x3e, 0xfd, 0x37, 0x00, 0x00, 0xff, 0xff, 0x50, 0x0c, 0x40, 0x1d, 0x80, 0x12, 0x00, 0x00,
}
