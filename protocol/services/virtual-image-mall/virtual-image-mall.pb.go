// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/virtual-image-mall/virtual-image-mall.proto

package virtual_image_mall // import "golang.52tt.com/protocol/services/virtual-image-mall"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
import virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CommodityGainPath int32

const (
	CommodityGainPath_COMMODITY_GAIN_PATH_UNKNOWN              CommodityGainPath = 0
	CommodityGainPath_COMMODITY_GAIN_PATH_PURCHASE             CommodityGainPath = 1
	CommodityGainPath_COMMODITY_GAIN_PATH_GIFT                 CommodityGainPath = 2
	CommodityGainPath_COMMODITY_GAIN_PATH_ACTIVITY             CommodityGainPath = 3
	CommodityGainPath_COMMODITY_GAIN_PATH_REWARD               CommodityGainPath = 4
	CommodityGainPath_COMMODITY_GAIN_PATH_EXCHANGE             CommodityGainPath = 5
	CommodityGainPath_COMMODITY_GAIN_PATH_OTHER                CommodityGainPath = 6
	CommodityGainPath_COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD CommodityGainPath = 7
)

var CommodityGainPath_name = map[int32]string{
	0: "COMMODITY_GAIN_PATH_UNKNOWN",
	1: "COMMODITY_GAIN_PATH_PURCHASE",
	2: "COMMODITY_GAIN_PATH_GIFT",
	3: "COMMODITY_GAIN_PATH_ACTIVITY",
	4: "COMMODITY_GAIN_PATH_REWARD",
	5: "COMMODITY_GAIN_PATH_EXCHANGE",
	6: "COMMODITY_GAIN_PATH_OTHER",
	7: "COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD",
}
var CommodityGainPath_value = map[string]int32{
	"COMMODITY_GAIN_PATH_UNKNOWN":              0,
	"COMMODITY_GAIN_PATH_PURCHASE":             1,
	"COMMODITY_GAIN_PATH_GIFT":                 2,
	"COMMODITY_GAIN_PATH_ACTIVITY":             3,
	"COMMODITY_GAIN_PATH_REWARD":               4,
	"COMMODITY_GAIN_PATH_EXCHANGE":             5,
	"COMMODITY_GAIN_PATH_OTHER":                6,
	"COMMODITY_GAIN_PATH_INFINITE_CHANGE_CARD": 7,
}

func (x CommodityGainPath) String() string {
	return proto.EnumName(CommodityGainPath_name, int32(x))
}
func (CommodityGainPath) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{0}
}

// 上架状态
type ShelfStatus int32

const (
	ShelfStatus_SHELF_STATUS_UNKNOWN ShelfStatus = 0
	ShelfStatus_SHELF_STATUS_FUTURE  ShelfStatus = 1
	ShelfStatus_SHELF_STATUS_NOW     ShelfStatus = 2
	ShelfStatus_SHELF_STATUS_EXPIRE  ShelfStatus = 3
)

var ShelfStatus_name = map[int32]string{
	0: "SHELF_STATUS_UNKNOWN",
	1: "SHELF_STATUS_FUTURE",
	2: "SHELF_STATUS_NOW",
	3: "SHELF_STATUS_EXPIRE",
}
var ShelfStatus_value = map[string]int32{
	"SHELF_STATUS_UNKNOWN": 0,
	"SHELF_STATUS_FUTURE":  1,
	"SHELF_STATUS_NOW":     2,
	"SHELF_STATUS_EXPIRE":  3,
}

func (x ShelfStatus) String() string {
	return proto.EnumName(ShelfStatus_name, int32(x))
}
func (ShelfStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{1}
}

type CommodityType int32

const (
	CommodityType_COMMODITY_TYPE_UNSPECIFIED CommodityType = 0
	CommodityType_COMMODITY_TYPE_SINGLE      CommodityType = 1
	CommodityType_COMMODITY_TYPE_SUIT        CommodityType = 2
)

var CommodityType_name = map[int32]string{
	0: "COMMODITY_TYPE_UNSPECIFIED",
	1: "COMMODITY_TYPE_SINGLE",
	2: "COMMODITY_TYPE_SUIT",
}
var CommodityType_value = map[string]int32{
	"COMMODITY_TYPE_UNSPECIFIED": 0,
	"COMMODITY_TYPE_SINGLE":      1,
	"COMMODITY_TYPE_SUIT":        2,
}

func (x CommodityType) String() string {
	return proto.EnumName(CommodityType_name, int32(x))
}
func (CommodityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{2}
}

type RightsType int32

const (
	RightsType_RIGHTS_TYPE_UNKNOWN        RightsType = 0
	RightsType_RIGHTS_TYPE_UNLIMITED_CARD RightsType = 1
)

var RightsType_name = map[int32]string{
	0: "RIGHTS_TYPE_UNKNOWN",
	1: "RIGHTS_TYPE_UNLIMITED_CARD",
}
var RightsType_value = map[string]int32{
	"RIGHTS_TYPE_UNKNOWN":        0,
	"RIGHTS_TYPE_UNLIMITED_CARD": 1,
}

func (x RightsType) String() string {
	return proto.EnumName(RightsType_name, int32(x))
}
func (RightsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{3}
}

type SortType int32

const (
	SortType_SORT_TYPE_UNKNOWN SortType = 0
	SortType_SORT_TYPE_ASC     SortType = 1
	SortType_SORT_TYPE_DESC    SortType = 2
)

var SortType_name = map[int32]string{
	0: "SORT_TYPE_UNKNOWN",
	1: "SORT_TYPE_ASC",
	2: "SORT_TYPE_DESC",
}
var SortType_value = map[string]int32{
	"SORT_TYPE_UNKNOWN": 0,
	"SORT_TYPE_ASC":     1,
	"SORT_TYPE_DESC":    2,
}

func (x SortType) String() string {
	return proto.EnumName(SortType_name, int32(x))
}
func (SortType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{4}
}

type CommodityPayStatus int32

const (
	CommodityPayStatus_COMMODITY_PAY_STATUS_UNKNOWN     CommodityPayStatus = 0
	CommodityPayStatus_COMMODITY_PAY_STATUS_PAYING      CommodityPayStatus = 1
	CommodityPayStatus_COMMODITY_PAY_STATUS_FREEZE      CommodityPayStatus = 2
	CommodityPayStatus_COMMODITY_PAY_STATUS_FREEZE_FAIL CommodityPayStatus = 3
	CommodityPayStatus_COMMODITY_PAY_STATUS_SHIPPED     CommodityPayStatus = 4
	CommodityPayStatus_COMMODITY_PAY_STATUS_REFUND      CommodityPayStatus = 5
)

var CommodityPayStatus_name = map[int32]string{
	0: "COMMODITY_PAY_STATUS_UNKNOWN",
	1: "COMMODITY_PAY_STATUS_PAYING",
	2: "COMMODITY_PAY_STATUS_FREEZE",
	3: "COMMODITY_PAY_STATUS_FREEZE_FAIL",
	4: "COMMODITY_PAY_STATUS_SHIPPED",
	5: "COMMODITY_PAY_STATUS_REFUND",
}
var CommodityPayStatus_value = map[string]int32{
	"COMMODITY_PAY_STATUS_UNKNOWN":     0,
	"COMMODITY_PAY_STATUS_PAYING":      1,
	"COMMODITY_PAY_STATUS_FREEZE":      2,
	"COMMODITY_PAY_STATUS_FREEZE_FAIL": 3,
	"COMMODITY_PAY_STATUS_SHIPPED":     4,
	"COMMODITY_PAY_STATUS_REFUND":      5,
}

func (x CommodityPayStatus) String() string {
	return proto.EnumName(CommodityPayStatus_name, int32(x))
}
func (CommodityPayStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{5}
}

// 价格套餐
type CommodityDataPackage struct {
	PackageId            uint32   `protobuf:"varint,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	Price                uint32   `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	DiscountPrice        uint32   `protobuf:"varint,3,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,omitempty"`
	DiscountRate         uint32   `protobuf:"varint,4,opt,name=discount_rate,json=discountRate,proto3" json:"discount_rate,omitempty"`
	EffectiveDay         uint32   `protobuf:"varint,5,opt,name=effective_day,json=effectiveDay,proto3" json:"effective_day,omitempty"`
	ShelfTime            uint32   `protobuf:"varint,6,opt,name=shelf_time,json=shelfTime,proto3" json:"shelf_time,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,7,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	CommodityId          uint32   `protobuf:"varint,8,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	ShelfStatus          uint32   `protobuf:"varint,9,opt,name=shelf_status,json=shelfStatus,proto3" json:"shelf_status,omitempty"`
	ExpireTimeShow       bool     `protobuf:"varint,10,opt,name=expire_time_show,json=expireTimeShow,proto3" json:"expire_time_show,omitempty"`
	IsRefreshedRedDot    bool     `protobuf:"varint,11,opt,name=is_refreshed_red_dot,json=isRefreshedRedDot,proto3" json:"is_refreshed_red_dot,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommodityDataPackage) Reset()         { *m = CommodityDataPackage{} }
func (m *CommodityDataPackage) String() string { return proto.CompactTextString(m) }
func (*CommodityDataPackage) ProtoMessage()    {}
func (*CommodityDataPackage) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{0}
}
func (m *CommodityDataPackage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityDataPackage.Unmarshal(m, b)
}
func (m *CommodityDataPackage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityDataPackage.Marshal(b, m, deterministic)
}
func (dst *CommodityDataPackage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityDataPackage.Merge(dst, src)
}
func (m *CommodityDataPackage) XXX_Size() int {
	return xxx_messageInfo_CommodityDataPackage.Size(m)
}
func (m *CommodityDataPackage) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityDataPackage.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityDataPackage proto.InternalMessageInfo

func (m *CommodityDataPackage) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *CommodityDataPackage) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *CommodityDataPackage) GetDiscountPrice() uint32 {
	if m != nil {
		return m.DiscountPrice
	}
	return 0
}

func (m *CommodityDataPackage) GetDiscountRate() uint32 {
	if m != nil {
		return m.DiscountRate
	}
	return 0
}

func (m *CommodityDataPackage) GetEffectiveDay() uint32 {
	if m != nil {
		return m.EffectiveDay
	}
	return 0
}

func (m *CommodityDataPackage) GetShelfTime() uint32 {
	if m != nil {
		return m.ShelfTime
	}
	return 0
}

func (m *CommodityDataPackage) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *CommodityDataPackage) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

func (m *CommodityDataPackage) GetShelfStatus() uint32 {
	if m != nil {
		return m.ShelfStatus
	}
	return 0
}

func (m *CommodityDataPackage) GetExpireTimeShow() bool {
	if m != nil {
		return m.ExpireTimeShow
	}
	return false
}

func (m *CommodityDataPackage) GetIsRefreshedRedDot() bool {
	if m != nil {
		return m.IsRefreshedRedDot
	}
	return false
}

type CustomizeLogotype struct {
	Logotype             string   `protobuf:"bytes,1,opt,name=logotype,proto3" json:"logotype,omitempty"`
	ShelfTime            uint32   `protobuf:"varint,2,opt,name=shelf_time,json=shelfTime,proto3" json:"shelf_time,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomizeLogotype) Reset()         { *m = CustomizeLogotype{} }
func (m *CustomizeLogotype) String() string { return proto.CompactTextString(m) }
func (*CustomizeLogotype) ProtoMessage()    {}
func (*CustomizeLogotype) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{1}
}
func (m *CustomizeLogotype) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomizeLogotype.Unmarshal(m, b)
}
func (m *CustomizeLogotype) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomizeLogotype.Marshal(b, m, deterministic)
}
func (dst *CustomizeLogotype) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomizeLogotype.Merge(dst, src)
}
func (m *CustomizeLogotype) XXX_Size() int {
	return xxx_messageInfo_CustomizeLogotype.Size(m)
}
func (m *CustomizeLogotype) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomizeLogotype.DiscardUnknown(m)
}

var xxx_messageInfo_CustomizeLogotype proto.InternalMessageInfo

func (m *CustomizeLogotype) GetLogotype() string {
	if m != nil {
		return m.Logotype
	}
	return ""
}

func (m *CustomizeLogotype) GetShelfTime() uint32 {
	if m != nil {
		return m.ShelfTime
	}
	return 0
}

func (m *CustomizeLogotype) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// 活动链接
type ActUrl struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	MarketId             uint32   `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ClientType           uint32   `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActUrl) Reset()         { *m = ActUrl{} }
func (m *ActUrl) String() string { return proto.CompactTextString(m) }
func (*ActUrl) ProtoMessage()    {}
func (*ActUrl) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{2}
}
func (m *ActUrl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActUrl.Unmarshal(m, b)
}
func (m *ActUrl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActUrl.Marshal(b, m, deterministic)
}
func (dst *ActUrl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActUrl.Merge(dst, src)
}
func (m *ActUrl) XXX_Size() int {
	return xxx_messageInfo_ActUrl.Size(m)
}
func (m *ActUrl) XXX_DiscardUnknown() {
	xxx_messageInfo_ActUrl.DiscardUnknown(m)
}

var xxx_messageInfo_ActUrl proto.InternalMessageInfo

func (m *ActUrl) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ActUrl) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *ActUrl) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

type ActivityInfo struct {
	Desc                 string    `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc,omitempty"`
	DescColor            string    `protobuf:"bytes,2,opt,name=desc_color,json=descColor,proto3" json:"desc_color,omitempty"`
	UrlList              []*ActUrl `protobuf:"bytes,3,rep,name=url_list,json=urlList,proto3" json:"url_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ActivityInfo) Reset()         { *m = ActivityInfo{} }
func (m *ActivityInfo) String() string { return proto.CompactTextString(m) }
func (*ActivityInfo) ProtoMessage()    {}
func (*ActivityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{3}
}
func (m *ActivityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityInfo.Unmarshal(m, b)
}
func (m *ActivityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityInfo.Marshal(b, m, deterministic)
}
func (dst *ActivityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityInfo.Merge(dst, src)
}
func (m *ActivityInfo) XXX_Size() int {
	return xxx_messageInfo_ActivityInfo.Size(m)
}
func (m *ActivityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityInfo proto.InternalMessageInfo

func (m *ActivityInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ActivityInfo) GetDescColor() string {
	if m != nil {
		return m.DescColor
	}
	return ""
}

func (m *ActivityInfo) GetUrlList() []*ActUrl {
	if m != nil {
		return m.UrlList
	}
	return nil
}

type CommodityData struct {
	CommodityId          uint32                  `protobuf:"varint,1,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	LevelIcon            string                  `protobuf:"bytes,2,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon,omitempty"`
	Level                uint32                  `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`
	CustomizeIcon        *CustomizeLogotype      `protobuf:"bytes,4,opt,name=customize_icon,json=customizeIcon,proto3" json:"customize_icon,omitempty"`
	CommodityIcon        string                  `protobuf:"bytes,5,opt,name=commodity_icon,json=commodityIcon,proto3" json:"commodity_icon,omitempty"`
	CommodityAnimation   string                  `protobuf:"bytes,6,opt,name=commodity_animation,json=commodityAnimation,proto3" json:"commodity_animation,omitempty"`
	CommodityName        string                  `protobuf:"bytes,7,opt,name=commodity_name,json=commodityName,proto3" json:"commodity_name,omitempty"`
	GainPath             uint32                  `protobuf:"varint,8,opt,name=gain_path,json=gainPath,proto3" json:"gain_path,omitempty"`
	ResourceIdList       []uint32                `protobuf:"varint,9,rep,packed,name=resource_id_list,json=resourceIdList,proto3" json:"resource_id_list,omitempty"`
	Category             uint32                  `protobuf:"varint,10,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32                  `protobuf:"varint,11,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	CommodityType        uint32                  `protobuf:"varint,12,opt,name=commodity_type,json=commodityType,proto3" json:"commodity_type,omitempty"`
	Rank                 uint32                  `protobuf:"varint,13,opt,name=rank,proto3" json:"rank,omitempty"`
	CreateTime           uint32                  `protobuf:"varint,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32                  `protobuf:"varint,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	ResourceSex          uint32                  `protobuf:"varint,16,opt,name=resource_sex,json=resourceSex,proto3" json:"resource_sex,omitempty"`
	PricePackageList     []*CommodityDataPackage `protobuf:"bytes,17,rep,name=price_package_list,json=pricePackageList,proto3" json:"price_package_list,omitempty"`
	ActInfo              *ActivityInfo           `protobuf:"bytes,18,opt,name=act_info,json=actInfo,proto3" json:"act_info,omitempty"`
	SpineAnimation       string                  `protobuf:"bytes,19,opt,name=spine_animation,json=spineAnimation,proto3" json:"spine_animation,omitempty"`
	ShelfTime            uint32                  `protobuf:"varint,20,opt,name=shelf_time,json=shelfTime,proto3" json:"shelf_time,omitempty"`
	ExpireTime           uint32                  `protobuf:"varint,21,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	RedDotVersion        uint32                  `protobuf:"varint,22,opt,name=red_dot_version,json=redDotVersion,proto3" json:"red_dot_version,omitempty"`
	PromotionalVideoId   uint32                  `protobuf:"varint,23,opt,name=promotional_video_id,json=promotionalVideoId,proto3" json:"promotional_video_id,omitempty"`
	SupportRights        uint32                  `protobuf:"varint,24,opt,name=support_rights,json=supportRights,proto3" json:"support_rights,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *CommodityData) Reset()         { *m = CommodityData{} }
func (m *CommodityData) String() string { return proto.CompactTextString(m) }
func (*CommodityData) ProtoMessage()    {}
func (*CommodityData) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{4}
}
func (m *CommodityData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityData.Unmarshal(m, b)
}
func (m *CommodityData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityData.Marshal(b, m, deterministic)
}
func (dst *CommodityData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityData.Merge(dst, src)
}
func (m *CommodityData) XXX_Size() int {
	return xxx_messageInfo_CommodityData.Size(m)
}
func (m *CommodityData) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityData.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityData proto.InternalMessageInfo

func (m *CommodityData) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

func (m *CommodityData) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

func (m *CommodityData) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *CommodityData) GetCustomizeIcon() *CustomizeLogotype {
	if m != nil {
		return m.CustomizeIcon
	}
	return nil
}

func (m *CommodityData) GetCommodityIcon() string {
	if m != nil {
		return m.CommodityIcon
	}
	return ""
}

func (m *CommodityData) GetCommodityAnimation() string {
	if m != nil {
		return m.CommodityAnimation
	}
	return ""
}

func (m *CommodityData) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *CommodityData) GetGainPath() uint32 {
	if m != nil {
		return m.GainPath
	}
	return 0
}

func (m *CommodityData) GetResourceIdList() []uint32 {
	if m != nil {
		return m.ResourceIdList
	}
	return nil
}

func (m *CommodityData) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *CommodityData) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *CommodityData) GetCommodityType() uint32 {
	if m != nil {
		return m.CommodityType
	}
	return 0
}

func (m *CommodityData) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *CommodityData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *CommodityData) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *CommodityData) GetResourceSex() uint32 {
	if m != nil {
		return m.ResourceSex
	}
	return 0
}

func (m *CommodityData) GetPricePackageList() []*CommodityDataPackage {
	if m != nil {
		return m.PricePackageList
	}
	return nil
}

func (m *CommodityData) GetActInfo() *ActivityInfo {
	if m != nil {
		return m.ActInfo
	}
	return nil
}

func (m *CommodityData) GetSpineAnimation() string {
	if m != nil {
		return m.SpineAnimation
	}
	return ""
}

func (m *CommodityData) GetShelfTime() uint32 {
	if m != nil {
		return m.ShelfTime
	}
	return 0
}

func (m *CommodityData) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *CommodityData) GetRedDotVersion() uint32 {
	if m != nil {
		return m.RedDotVersion
	}
	return 0
}

func (m *CommodityData) GetPromotionalVideoId() uint32 {
	if m != nil {
		return m.PromotionalVideoId
	}
	return 0
}

func (m *CommodityData) GetSupportRights() uint32 {
	if m != nil {
		return m.SupportRights
	}
	return 0
}

type SortData struct {
	Field                string   `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	SortType             uint32   `protobuf:"varint,2,opt,name=sort_type,json=sortType,proto3" json:"sort_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortData) Reset()         { *m = SortData{} }
func (m *SortData) String() string { return proto.CompactTextString(m) }
func (*SortData) ProtoMessage()    {}
func (*SortData) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{5}
}
func (m *SortData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortData.Unmarshal(m, b)
}
func (m *SortData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortData.Marshal(b, m, deterministic)
}
func (dst *SortData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortData.Merge(dst, src)
}
func (m *SortData) XXX_Size() int {
	return xxx_messageInfo_SortData.Size(m)
}
func (m *SortData) XXX_DiscardUnknown() {
	xxx_messageInfo_SortData.DiscardUnknown(m)
}

var xxx_messageInfo_SortData proto.InternalMessageInfo

func (m *SortData) GetField() string {
	if m != nil {
		return m.Field
	}
	return ""
}

func (m *SortData) GetSortType() uint32 {
	if m != nil {
		return m.SortType
	}
	return 0
}

// 获取商品数据列表
type GetCommodityDataListRequest struct {
	Category             uint32    `protobuf:"varint,1,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32    `protobuf:"varint,2,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	CommodityIdList      []uint32  `protobuf:"varint,3,rep,packed,name=commodity_id_list,json=commodityIdList,proto3" json:"commodity_id_list,omitempty"`
	ResourceIdList       []uint32  `protobuf:"varint,4,rep,packed,name=resource_id_list,json=resourceIdList,proto3" json:"resource_id_list,omitempty"`
	CommodityType        uint32    `protobuf:"varint,5,opt,name=commodity_type,json=commodityType,proto3" json:"commodity_type,omitempty"`
	CommodityName        string    `protobuf:"bytes,6,opt,name=commodity_name,json=commodityName,proto3" json:"commodity_name,omitempty"`
	ResourceSex          uint32    `protobuf:"varint,7,opt,name=resource_sex,json=resourceSex,proto3" json:"resource_sex,omitempty"`
	Level                uint32    `protobuf:"varint,8,opt,name=level,proto3" json:"level,omitempty"`
	GainPath             uint32    `protobuf:"varint,9,opt,name=gain_path,json=gainPath,proto3" json:"gain_path,omitempty"`
	ShelfStatus          uint32    `protobuf:"varint,10,opt,name=shelf_status,json=shelfStatus,proto3" json:"shelf_status,omitempty"`
	Page                 uint32    `protobuf:"varint,11,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32    `protobuf:"varint,12,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	IsSexSelect          bool      `protobuf:"varint,13,opt,name=is_sex_select,json=isSexSelect,proto3" json:"is_sex_select,omitempty"`
	ResourceSexList      []uint32  `protobuf:"varint,14,rep,packed,name=resource_sex_list,json=resourceSexList,proto3" json:"resource_sex_list,omitempty"`
	RankSort             uint32    `protobuf:"varint,15,opt,name=rank_sort,json=rankSort,proto3" json:"rank_sort,omitempty"`
	MinPrice             uint32    `protobuf:"varint,16,opt,name=min_price,json=minPrice,proto3" json:"min_price,omitempty"`
	MaxPrice             uint32    `protobuf:"varint,17,opt,name=max_price,json=maxPrice,proto3" json:"max_price,omitempty"`
	MinDiscountPrice     uint32    `protobuf:"varint,18,opt,name=min_discount_price,json=minDiscountPrice,proto3" json:"min_discount_price,omitempty"`
	MaxDiscountPrice     uint32    `protobuf:"varint,19,opt,name=max_discount_price,json=maxDiscountPrice,proto3" json:"max_discount_price,omitempty"`
	SortData             *SortData `protobuf:"bytes,20,opt,name=sort_data,json=sortData,proto3" json:"sort_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetCommodityDataListRequest) Reset()         { *m = GetCommodityDataListRequest{} }
func (m *GetCommodityDataListRequest) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataListRequest) ProtoMessage()    {}
func (*GetCommodityDataListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{6}
}
func (m *GetCommodityDataListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataListRequest.Unmarshal(m, b)
}
func (m *GetCommodityDataListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataListRequest.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataListRequest.Merge(dst, src)
}
func (m *GetCommodityDataListRequest) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataListRequest.Size(m)
}
func (m *GetCommodityDataListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataListRequest proto.InternalMessageInfo

func (m *GetCommodityDataListRequest) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetCommodityIdList() []uint32 {
	if m != nil {
		return m.CommodityIdList
	}
	return nil
}

func (m *GetCommodityDataListRequest) GetResourceIdList() []uint32 {
	if m != nil {
		return m.ResourceIdList
	}
	return nil
}

func (m *GetCommodityDataListRequest) GetCommodityType() uint32 {
	if m != nil {
		return m.CommodityType
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *GetCommodityDataListRequest) GetResourceSex() uint32 {
	if m != nil {
		return m.ResourceSex
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetGainPath() uint32 {
	if m != nil {
		return m.GainPath
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetShelfStatus() uint32 {
	if m != nil {
		return m.ShelfStatus
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetIsSexSelect() bool {
	if m != nil {
		return m.IsSexSelect
	}
	return false
}

func (m *GetCommodityDataListRequest) GetResourceSexList() []uint32 {
	if m != nil {
		return m.ResourceSexList
	}
	return nil
}

func (m *GetCommodityDataListRequest) GetRankSort() uint32 {
	if m != nil {
		return m.RankSort
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetMinPrice() uint32 {
	if m != nil {
		return m.MinPrice
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetMaxPrice() uint32 {
	if m != nil {
		return m.MaxPrice
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetMinDiscountPrice() uint32 {
	if m != nil {
		return m.MinDiscountPrice
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetMaxDiscountPrice() uint32 {
	if m != nil {
		return m.MaxDiscountPrice
	}
	return 0
}

func (m *GetCommodityDataListRequest) GetSortData() *SortData {
	if m != nil {
		return m.SortData
	}
	return nil
}

type GetCommodityDataListResponse struct {
	CommodityDataList    []*CommodityData `protobuf:"bytes,1,rep,name=commodity_data_list,json=commodityDataList,proto3" json:"commodity_data_list,omitempty"`
	TotalCnt             uint32           `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCommodityDataListResponse) Reset()         { *m = GetCommodityDataListResponse{} }
func (m *GetCommodityDataListResponse) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataListResponse) ProtoMessage()    {}
func (*GetCommodityDataListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{7}
}
func (m *GetCommodityDataListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataListResponse.Unmarshal(m, b)
}
func (m *GetCommodityDataListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataListResponse.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataListResponse.Merge(dst, src)
}
func (m *GetCommodityDataListResponse) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataListResponse.Size(m)
}
func (m *GetCommodityDataListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataListResponse proto.InternalMessageInfo

func (m *GetCommodityDataListResponse) GetCommodityDataList() []*CommodityData {
	if m != nil {
		return m.CommodityDataList
	}
	return nil
}

func (m *GetCommodityDataListResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 根据价格获取商品数据列表
type GetCommodityDataListByPriceRequest struct {
	Price                uint32   `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	ResourceSexList      []uint32 `protobuf:"varint,2,rep,packed,name=resource_sex_list,json=resourceSexList,proto3" json:"resource_sex_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCommodityDataListByPriceRequest) Reset()         { *m = GetCommodityDataListByPriceRequest{} }
func (m *GetCommodityDataListByPriceRequest) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataListByPriceRequest) ProtoMessage()    {}
func (*GetCommodityDataListByPriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{8}
}
func (m *GetCommodityDataListByPriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataListByPriceRequest.Unmarshal(m, b)
}
func (m *GetCommodityDataListByPriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataListByPriceRequest.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataListByPriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataListByPriceRequest.Merge(dst, src)
}
func (m *GetCommodityDataListByPriceRequest) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataListByPriceRequest.Size(m)
}
func (m *GetCommodityDataListByPriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataListByPriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataListByPriceRequest proto.InternalMessageInfo

func (m *GetCommodityDataListByPriceRequest) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GetCommodityDataListByPriceRequest) GetResourceSexList() []uint32 {
	if m != nil {
		return m.ResourceSexList
	}
	return nil
}

type GetCommodityDataListByPriceResponse struct {
	CommodityDataList    []*CommodityData `protobuf:"bytes,1,rep,name=commodity_data_list,json=commodityDataList,proto3" json:"commodity_data_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCommodityDataListByPriceResponse) Reset()         { *m = GetCommodityDataListByPriceResponse{} }
func (m *GetCommodityDataListByPriceResponse) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataListByPriceResponse) ProtoMessage()    {}
func (*GetCommodityDataListByPriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{9}
}
func (m *GetCommodityDataListByPriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataListByPriceResponse.Unmarshal(m, b)
}
func (m *GetCommodityDataListByPriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataListByPriceResponse.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataListByPriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataListByPriceResponse.Merge(dst, src)
}
func (m *GetCommodityDataListByPriceResponse) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataListByPriceResponse.Size(m)
}
func (m *GetCommodityDataListByPriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataListByPriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataListByPriceResponse proto.InternalMessageInfo

func (m *GetCommodityDataListByPriceResponse) GetCommodityDataList() []*CommodityData {
	if m != nil {
		return m.CommodityDataList
	}
	return nil
}

// 批量添加商品
type BatAddCommodityReq struct {
	DataList             []*CommodityData `protobuf:"bytes,1,rep,name=data_list,json=dataList,proto3" json:"data_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatAddCommodityReq) Reset()         { *m = BatAddCommodityReq{} }
func (m *BatAddCommodityReq) String() string { return proto.CompactTextString(m) }
func (*BatAddCommodityReq) ProtoMessage()    {}
func (*BatAddCommodityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{10}
}
func (m *BatAddCommodityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatAddCommodityReq.Unmarshal(m, b)
}
func (m *BatAddCommodityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatAddCommodityReq.Marshal(b, m, deterministic)
}
func (dst *BatAddCommodityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatAddCommodityReq.Merge(dst, src)
}
func (m *BatAddCommodityReq) XXX_Size() int {
	return xxx_messageInfo_BatAddCommodityReq.Size(m)
}
func (m *BatAddCommodityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatAddCommodityReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatAddCommodityReq proto.InternalMessageInfo

func (m *BatAddCommodityReq) GetDataList() []*CommodityData {
	if m != nil {
		return m.DataList
	}
	return nil
}

type BatAddCommodityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatAddCommodityResp) Reset()         { *m = BatAddCommodityResp{} }
func (m *BatAddCommodityResp) String() string { return proto.CompactTextString(m) }
func (*BatAddCommodityResp) ProtoMessage()    {}
func (*BatAddCommodityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{11}
}
func (m *BatAddCommodityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatAddCommodityResp.Unmarshal(m, b)
}
func (m *BatAddCommodityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatAddCommodityResp.Marshal(b, m, deterministic)
}
func (dst *BatAddCommodityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatAddCommodityResp.Merge(dst, src)
}
func (m *BatAddCommodityResp) XXX_Size() int {
	return xxx_messageInfo_BatAddCommodityResp.Size(m)
}
func (m *BatAddCommodityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatAddCommodityResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatAddCommodityResp proto.InternalMessageInfo

// 更新商品信息
type UpdateCommodityReq struct {
	Data                 *CommodityData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateCommodityReq) Reset()         { *m = UpdateCommodityReq{} }
func (m *UpdateCommodityReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityReq) ProtoMessage()    {}
func (*UpdateCommodityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{12}
}
func (m *UpdateCommodityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityReq.Unmarshal(m, b)
}
func (m *UpdateCommodityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityReq.Merge(dst, src)
}
func (m *UpdateCommodityReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityReq.Size(m)
}
func (m *UpdateCommodityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityReq proto.InternalMessageInfo

func (m *UpdateCommodityReq) GetData() *CommodityData {
	if m != nil {
		return m.Data
	}
	return nil
}

type UpdateCommodityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCommodityResp) Reset()         { *m = UpdateCommodityResp{} }
func (m *UpdateCommodityResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityResp) ProtoMessage()    {}
func (*UpdateCommodityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{13}
}
func (m *UpdateCommodityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityResp.Unmarshal(m, b)
}
func (m *UpdateCommodityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityResp.Merge(dst, src)
}
func (m *UpdateCommodityResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityResp.Size(m)
}
func (m *UpdateCommodityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityResp proto.InternalMessageInfo

// 更新商品的物品信息
type UpdateCommodityResourceReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Sex                  uint32   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCommodityResourceReq) Reset()         { *m = UpdateCommodityResourceReq{} }
func (m *UpdateCommodityResourceReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityResourceReq) ProtoMessage()    {}
func (*UpdateCommodityResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{14}
}
func (m *UpdateCommodityResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityResourceReq.Unmarshal(m, b)
}
func (m *UpdateCommodityResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityResourceReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityResourceReq.Merge(dst, src)
}
func (m *UpdateCommodityResourceReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityResourceReq.Size(m)
}
func (m *UpdateCommodityResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityResourceReq proto.InternalMessageInfo

func (m *UpdateCommodityResourceReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateCommodityResourceReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpdateCommodityResourceReq) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *UpdateCommodityResourceReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type UpdateCommodityResourceResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCommodityResourceResp) Reset()         { *m = UpdateCommodityResourceResp{} }
func (m *UpdateCommodityResourceResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityResourceResp) ProtoMessage()    {}
func (*UpdateCommodityResourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{15}
}
func (m *UpdateCommodityResourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityResourceResp.Unmarshal(m, b)
}
func (m *UpdateCommodityResourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityResourceResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityResourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityResourceResp.Merge(dst, src)
}
func (m *UpdateCommodityResourceResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityResourceResp.Size(m)
}
func (m *UpdateCommodityResourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityResourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityResourceResp proto.InternalMessageInfo

type CommodityRecommendInfo struct {
	Data                 *CommodityData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	RecRank              uint32         `protobuf:"varint,2,opt,name=rec_rank,json=recRank,proto3" json:"rec_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CommodityRecommendInfo) Reset()         { *m = CommodityRecommendInfo{} }
func (m *CommodityRecommendInfo) String() string { return proto.CompactTextString(m) }
func (*CommodityRecommendInfo) ProtoMessage()    {}
func (*CommodityRecommendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{16}
}
func (m *CommodityRecommendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityRecommendInfo.Unmarshal(m, b)
}
func (m *CommodityRecommendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityRecommendInfo.Marshal(b, m, deterministic)
}
func (dst *CommodityRecommendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityRecommendInfo.Merge(dst, src)
}
func (m *CommodityRecommendInfo) XXX_Size() int {
	return xxx_messageInfo_CommodityRecommendInfo.Size(m)
}
func (m *CommodityRecommendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityRecommendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityRecommendInfo proto.InternalMessageInfo

func (m *CommodityRecommendInfo) GetData() *CommodityData {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *CommodityRecommendInfo) GetRecRank() uint32 {
	if m != nil {
		return m.RecRank
	}
	return 0
}

// 获取推荐商品列表
type GetCommodityRecommendListRequest struct {
	Category             uint32    `protobuf:"varint,1,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32    `protobuf:"varint,2,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	CommodityIdList      []uint32  `protobuf:"varint,3,rep,packed,name=commodity_id_list,json=commodityIdList,proto3" json:"commodity_id_list,omitempty"`
	ResourceIdList       []uint32  `protobuf:"varint,4,rep,packed,name=resource_id_list,json=resourceIdList,proto3" json:"resource_id_list,omitempty"`
	CommodityType        uint32    `protobuf:"varint,5,opt,name=commodity_type,json=commodityType,proto3" json:"commodity_type,omitempty"`
	CommodityName        string    `protobuf:"bytes,6,opt,name=commodity_name,json=commodityName,proto3" json:"commodity_name,omitempty"`
	ResourceSex          uint32    `protobuf:"varint,7,opt,name=resource_sex,json=resourceSex,proto3" json:"resource_sex,omitempty"`
	Level                uint32    `protobuf:"varint,8,opt,name=level,proto3" json:"level,omitempty"`
	GainPath             uint32    `protobuf:"varint,9,opt,name=gain_path,json=gainPath,proto3" json:"gain_path,omitempty"`
	ShelfStatus          uint32    `protobuf:"varint,10,opt,name=shelf_status,json=shelfStatus,proto3" json:"shelf_status,omitempty"`
	Page                 uint32    `protobuf:"varint,11,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32    `protobuf:"varint,12,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	IsSexSelect          bool      `protobuf:"varint,13,opt,name=is_sex_select,json=isSexSelect,proto3" json:"is_sex_select,omitempty"`
	ResourceSexList      []uint32  `protobuf:"varint,14,rep,packed,name=resource_sex_list,json=resourceSexList,proto3" json:"resource_sex_list,omitempty"`
	SortData             *SortData `protobuf:"bytes,15,opt,name=sort_data,json=sortData,proto3" json:"sort_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetCommodityRecommendListRequest) Reset()         { *m = GetCommodityRecommendListRequest{} }
func (m *GetCommodityRecommendListRequest) String() string { return proto.CompactTextString(m) }
func (*GetCommodityRecommendListRequest) ProtoMessage()    {}
func (*GetCommodityRecommendListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{17}
}
func (m *GetCommodityRecommendListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityRecommendListRequest.Unmarshal(m, b)
}
func (m *GetCommodityRecommendListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityRecommendListRequest.Marshal(b, m, deterministic)
}
func (dst *GetCommodityRecommendListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityRecommendListRequest.Merge(dst, src)
}
func (m *GetCommodityRecommendListRequest) XXX_Size() int {
	return xxx_messageInfo_GetCommodityRecommendListRequest.Size(m)
}
func (m *GetCommodityRecommendListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityRecommendListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityRecommendListRequest proto.InternalMessageInfo

func (m *GetCommodityRecommendListRequest) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *GetCommodityRecommendListRequest) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *GetCommodityRecommendListRequest) GetCommodityIdList() []uint32 {
	if m != nil {
		return m.CommodityIdList
	}
	return nil
}

func (m *GetCommodityRecommendListRequest) GetResourceIdList() []uint32 {
	if m != nil {
		return m.ResourceIdList
	}
	return nil
}

func (m *GetCommodityRecommendListRequest) GetCommodityType() uint32 {
	if m != nil {
		return m.CommodityType
	}
	return 0
}

func (m *GetCommodityRecommendListRequest) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *GetCommodityRecommendListRequest) GetResourceSex() uint32 {
	if m != nil {
		return m.ResourceSex
	}
	return 0
}

func (m *GetCommodityRecommendListRequest) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetCommodityRecommendListRequest) GetGainPath() uint32 {
	if m != nil {
		return m.GainPath
	}
	return 0
}

func (m *GetCommodityRecommendListRequest) GetShelfStatus() uint32 {
	if m != nil {
		return m.ShelfStatus
	}
	return 0
}

func (m *GetCommodityRecommendListRequest) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetCommodityRecommendListRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetCommodityRecommendListRequest) GetIsSexSelect() bool {
	if m != nil {
		return m.IsSexSelect
	}
	return false
}

func (m *GetCommodityRecommendListRequest) GetResourceSexList() []uint32 {
	if m != nil {
		return m.ResourceSexList
	}
	return nil
}

func (m *GetCommodityRecommendListRequest) GetSortData() *SortData {
	if m != nil {
		return m.SortData
	}
	return nil
}

type GetCommodityRecommendListResponse struct {
	RecList              []*CommodityRecommendInfo `protobuf:"bytes,1,rep,name=rec_list,json=recList,proto3" json:"rec_list,omitempty"`
	TotalCnt             uint32                    `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetCommodityRecommendListResponse) Reset()         { *m = GetCommodityRecommendListResponse{} }
func (m *GetCommodityRecommendListResponse) String() string { return proto.CompactTextString(m) }
func (*GetCommodityRecommendListResponse) ProtoMessage()    {}
func (*GetCommodityRecommendListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{18}
}
func (m *GetCommodityRecommendListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityRecommendListResponse.Unmarshal(m, b)
}
func (m *GetCommodityRecommendListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityRecommendListResponse.Marshal(b, m, deterministic)
}
func (dst *GetCommodityRecommendListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityRecommendListResponse.Merge(dst, src)
}
func (m *GetCommodityRecommendListResponse) XXX_Size() int {
	return xxx_messageInfo_GetCommodityRecommendListResponse.Size(m)
}
func (m *GetCommodityRecommendListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityRecommendListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityRecommendListResponse proto.InternalMessageInfo

func (m *GetCommodityRecommendListResponse) GetRecList() []*CommodityRecommendInfo {
	if m != nil {
		return m.RecList
	}
	return nil
}

func (m *GetCommodityRecommendListResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 批量增加推荐商品
type BatAddCommodityRecommendReq struct {
	InfoList             []*CommodityRecommendInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *BatAddCommodityRecommendReq) Reset()         { *m = BatAddCommodityRecommendReq{} }
func (m *BatAddCommodityRecommendReq) String() string { return proto.CompactTextString(m) }
func (*BatAddCommodityRecommendReq) ProtoMessage()    {}
func (*BatAddCommodityRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{19}
}
func (m *BatAddCommodityRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatAddCommodityRecommendReq.Unmarshal(m, b)
}
func (m *BatAddCommodityRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatAddCommodityRecommendReq.Marshal(b, m, deterministic)
}
func (dst *BatAddCommodityRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatAddCommodityRecommendReq.Merge(dst, src)
}
func (m *BatAddCommodityRecommendReq) XXX_Size() int {
	return xxx_messageInfo_BatAddCommodityRecommendReq.Size(m)
}
func (m *BatAddCommodityRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatAddCommodityRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatAddCommodityRecommendReq proto.InternalMessageInfo

func (m *BatAddCommodityRecommendReq) GetInfoList() []*CommodityRecommendInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type BatAddCommodityRecommendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatAddCommodityRecommendResp) Reset()         { *m = BatAddCommodityRecommendResp{} }
func (m *BatAddCommodityRecommendResp) String() string { return proto.CompactTextString(m) }
func (*BatAddCommodityRecommendResp) ProtoMessage()    {}
func (*BatAddCommodityRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{20}
}
func (m *BatAddCommodityRecommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatAddCommodityRecommendResp.Unmarshal(m, b)
}
func (m *BatAddCommodityRecommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatAddCommodityRecommendResp.Marshal(b, m, deterministic)
}
func (dst *BatAddCommodityRecommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatAddCommodityRecommendResp.Merge(dst, src)
}
func (m *BatAddCommodityRecommendResp) XXX_Size() int {
	return xxx_messageInfo_BatAddCommodityRecommendResp.Size(m)
}
func (m *BatAddCommodityRecommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatAddCommodityRecommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatAddCommodityRecommendResp proto.InternalMessageInfo

// 编辑推荐商品
type UpdateCommodityRecommendReq struct {
	Info                 *CommodityRecommendInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpdateCommodityRecommendReq) Reset()         { *m = UpdateCommodityRecommendReq{} }
func (m *UpdateCommodityRecommendReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityRecommendReq) ProtoMessage()    {}
func (*UpdateCommodityRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{21}
}
func (m *UpdateCommodityRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityRecommendReq.Unmarshal(m, b)
}
func (m *UpdateCommodityRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityRecommendReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityRecommendReq.Merge(dst, src)
}
func (m *UpdateCommodityRecommendReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityRecommendReq.Size(m)
}
func (m *UpdateCommodityRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityRecommendReq proto.InternalMessageInfo

func (m *UpdateCommodityRecommendReq) GetInfo() *CommodityRecommendInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type UpdateCommodityRecommendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCommodityRecommendResp) Reset()         { *m = UpdateCommodityRecommendResp{} }
func (m *UpdateCommodityRecommendResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityRecommendResp) ProtoMessage()    {}
func (*UpdateCommodityRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{22}
}
func (m *UpdateCommodityRecommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityRecommendResp.Unmarshal(m, b)
}
func (m *UpdateCommodityRecommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityRecommendResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityRecommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityRecommendResp.Merge(dst, src)
}
func (m *UpdateCommodityRecommendResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityRecommendResp.Size(m)
}
func (m *UpdateCommodityRecommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityRecommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityRecommendResp proto.InternalMessageInfo

// 删除推荐商品信息
type DelCommodityRecommendReq struct {
	CommodityId          uint32   `protobuf:"varint,1,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	CommodityIdList      []uint32 `protobuf:"varint,2,rep,packed,name=commodity_id_list,json=commodityIdList,proto3" json:"commodity_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCommodityRecommendReq) Reset()         { *m = DelCommodityRecommendReq{} }
func (m *DelCommodityRecommendReq) String() string { return proto.CompactTextString(m) }
func (*DelCommodityRecommendReq) ProtoMessage()    {}
func (*DelCommodityRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{23}
}
func (m *DelCommodityRecommendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCommodityRecommendReq.Unmarshal(m, b)
}
func (m *DelCommodityRecommendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCommodityRecommendReq.Marshal(b, m, deterministic)
}
func (dst *DelCommodityRecommendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCommodityRecommendReq.Merge(dst, src)
}
func (m *DelCommodityRecommendReq) XXX_Size() int {
	return xxx_messageInfo_DelCommodityRecommendReq.Size(m)
}
func (m *DelCommodityRecommendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCommodityRecommendReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCommodityRecommendReq proto.InternalMessageInfo

func (m *DelCommodityRecommendReq) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

func (m *DelCommodityRecommendReq) GetCommodityIdList() []uint32 {
	if m != nil {
		return m.CommodityIdList
	}
	return nil
}

type DelCommodityRecommendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCommodityRecommendResp) Reset()         { *m = DelCommodityRecommendResp{} }
func (m *DelCommodityRecommendResp) String() string { return proto.CompactTextString(m) }
func (*DelCommodityRecommendResp) ProtoMessage()    {}
func (*DelCommodityRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{24}
}
func (m *DelCommodityRecommendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCommodityRecommendResp.Unmarshal(m, b)
}
func (m *DelCommodityRecommendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCommodityRecommendResp.Marshal(b, m, deterministic)
}
func (dst *DelCommodityRecommendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCommodityRecommendResp.Merge(dst, src)
}
func (m *DelCommodityRecommendResp) XXX_Size() int {
	return xxx_messageInfo_DelCommodityRecommendResp.Size(m)
}
func (m *DelCommodityRecommendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCommodityRecommendResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCommodityRecommendResp proto.InternalMessageInfo

// 商品订单信息
type CommodityDataOrders struct {
	DataOrderId          string   `protobuf:"bytes,1,opt,name=data_order_id,json=dataOrderId,proto3" json:"data_order_id,omitempty"`
	CommodityId          uint32   `protobuf:"varint,2,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	PackageId            uint32   `protobuf:"varint,3,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,5,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	AvgPrice             uint32   `protobuf:"varint,6,opt,name=avg_price,json=avgPrice,proto3" json:"avg_price,omitempty"`
	CreateTime           uint32   `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	PayStatus            uint32   `protobuf:"varint,8,opt,name=pay_status,json=payStatus,proto3" json:"pay_status,omitempty"`
	Uid                  uint32   `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	ResourceIdList       []uint32 `protobuf:"varint,11,rep,packed,name=resource_id_list,json=resourceIdList,proto3" json:"resource_id_list,omitempty"`
	CommodityName        string   `protobuf:"bytes,12,opt,name=commodity_name,json=commodityName,proto3" json:"commodity_name,omitempty"`
	EffectiveDay         uint32   `protobuf:"varint,13,opt,name=effective_day,json=effectiveDay,proto3" json:"effective_day,omitempty"`
	CommodityType        uint32   `protobuf:"varint,14,opt,name=commodity_type,json=commodityType,proto3" json:"commodity_type,omitempty"`
	Category             uint32   `protobuf:"varint,15,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32   `protobuf:"varint,16,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	BigTradeNo           string   `protobuf:"bytes,17,opt,name=big_trade_no,json=bigTradeNo,proto3" json:"big_trade_no,omitempty"`
	CommodityIcon        string   `protobuf:"bytes,18,opt,name=commodity_icon,json=commodityIcon,proto3" json:"commodity_icon,omitempty"`
	LevelIcon            string   `protobuf:"bytes,19,opt,name=level_icon,json=levelIcon,proto3" json:"level_icon,omitempty"`
	PromotionalVideoId   uint32   `protobuf:"varint,21,opt,name=promotional_video_id,json=promotionalVideoId,proto3" json:"promotional_video_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommodityDataOrders) Reset()         { *m = CommodityDataOrders{} }
func (m *CommodityDataOrders) String() string { return proto.CompactTextString(m) }
func (*CommodityDataOrders) ProtoMessage()    {}
func (*CommodityDataOrders) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{25}
}
func (m *CommodityDataOrders) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityDataOrders.Unmarshal(m, b)
}
func (m *CommodityDataOrders) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityDataOrders.Marshal(b, m, deterministic)
}
func (dst *CommodityDataOrders) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityDataOrders.Merge(dst, src)
}
func (m *CommodityDataOrders) XXX_Size() int {
	return xxx_messageInfo_CommodityDataOrders.Size(m)
}
func (m *CommodityDataOrders) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityDataOrders.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityDataOrders proto.InternalMessageInfo

func (m *CommodityDataOrders) GetDataOrderId() string {
	if m != nil {
		return m.DataOrderId
	}
	return ""
}

func (m *CommodityDataOrders) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

func (m *CommodityDataOrders) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *CommodityDataOrders) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CommodityDataOrders) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *CommodityDataOrders) GetAvgPrice() uint32 {
	if m != nil {
		return m.AvgPrice
	}
	return 0
}

func (m *CommodityDataOrders) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *CommodityDataOrders) GetPayStatus() uint32 {
	if m != nil {
		return m.PayStatus
	}
	return 0
}

func (m *CommodityDataOrders) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommodityDataOrders) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *CommodityDataOrders) GetResourceIdList() []uint32 {
	if m != nil {
		return m.ResourceIdList
	}
	return nil
}

func (m *CommodityDataOrders) GetCommodityName() string {
	if m != nil {
		return m.CommodityName
	}
	return ""
}

func (m *CommodityDataOrders) GetEffectiveDay() uint32 {
	if m != nil {
		return m.EffectiveDay
	}
	return 0
}

func (m *CommodityDataOrders) GetCommodityType() uint32 {
	if m != nil {
		return m.CommodityType
	}
	return 0
}

func (m *CommodityDataOrders) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *CommodityDataOrders) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *CommodityDataOrders) GetBigTradeNo() string {
	if m != nil {
		return m.BigTradeNo
	}
	return ""
}

func (m *CommodityDataOrders) GetCommodityIcon() string {
	if m != nil {
		return m.CommodityIcon
	}
	return ""
}

func (m *CommodityDataOrders) GetLevelIcon() string {
	if m != nil {
		return m.LevelIcon
	}
	return ""
}

func (m *CommodityDataOrders) GetPromotionalVideoId() uint32 {
	if m != nil {
		return m.PromotionalVideoId
	}
	return 0
}

// 购物车商品基础数据
type ShoppingItemBasic struct {
	ShoppingItemId       uint32   `protobuf:"varint,1,opt,name=shopping_item_id,json=shoppingItemId,proto3" json:"shopping_item_id,omitempty"`
	CommodityId          uint32   `protobuf:"varint,2,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	PackageId            uint32   `protobuf:"varint,3,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,5,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	AvgPrice             uint32   `protobuf:"varint,6,opt,name=avg_price,json=avgPrice,proto3" json:"avg_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShoppingItemBasic) Reset()         { *m = ShoppingItemBasic{} }
func (m *ShoppingItemBasic) String() string { return proto.CompactTextString(m) }
func (*ShoppingItemBasic) ProtoMessage()    {}
func (*ShoppingItemBasic) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{26}
}
func (m *ShoppingItemBasic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShoppingItemBasic.Unmarshal(m, b)
}
func (m *ShoppingItemBasic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShoppingItemBasic.Marshal(b, m, deterministic)
}
func (dst *ShoppingItemBasic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShoppingItemBasic.Merge(dst, src)
}
func (m *ShoppingItemBasic) XXX_Size() int {
	return xxx_messageInfo_ShoppingItemBasic.Size(m)
}
func (m *ShoppingItemBasic) XXX_DiscardUnknown() {
	xxx_messageInfo_ShoppingItemBasic.DiscardUnknown(m)
}

var xxx_messageInfo_ShoppingItemBasic proto.InternalMessageInfo

func (m *ShoppingItemBasic) GetShoppingItemId() uint32 {
	if m != nil {
		return m.ShoppingItemId
	}
	return 0
}

func (m *ShoppingItemBasic) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

func (m *ShoppingItemBasic) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *ShoppingItemBasic) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ShoppingItemBasic) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *ShoppingItemBasic) GetAvgPrice() uint32 {
	if m != nil {
		return m.AvgPrice
	}
	return 0
}

type BuyCommodityDataRequest struct {
	Orders               []*CommodityDataOrders `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BuyCommodityDataRequest) Reset()         { *m = BuyCommodityDataRequest{} }
func (m *BuyCommodityDataRequest) String() string { return proto.CompactTextString(m) }
func (*BuyCommodityDataRequest) ProtoMessage()    {}
func (*BuyCommodityDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{27}
}
func (m *BuyCommodityDataRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyCommodityDataRequest.Unmarshal(m, b)
}
func (m *BuyCommodityDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyCommodityDataRequest.Marshal(b, m, deterministic)
}
func (dst *BuyCommodityDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyCommodityDataRequest.Merge(dst, src)
}
func (m *BuyCommodityDataRequest) XXX_Size() int {
	return xxx_messageInfo_BuyCommodityDataRequest.Size(m)
}
func (m *BuyCommodityDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyCommodityDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BuyCommodityDataRequest proto.InternalMessageInfo

func (m *BuyCommodityDataRequest) GetOrders() []*CommodityDataOrders {
	if m != nil {
		return m.Orders
	}
	return nil
}

type BuyCommodityDataResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyCommodityDataResponse) Reset()         { *m = BuyCommodityDataResponse{} }
func (m *BuyCommodityDataResponse) String() string { return proto.CompactTextString(m) }
func (*BuyCommodityDataResponse) ProtoMessage()    {}
func (*BuyCommodityDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{28}
}
func (m *BuyCommodityDataResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyCommodityDataResponse.Unmarshal(m, b)
}
func (m *BuyCommodityDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyCommodityDataResponse.Marshal(b, m, deterministic)
}
func (dst *BuyCommodityDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyCommodityDataResponse.Merge(dst, src)
}
func (m *BuyCommodityDataResponse) XXX_Size() int {
	return xxx_messageInfo_BuyCommodityDataResponse.Size(m)
}
func (m *BuyCommodityDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyCommodityDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BuyCommodityDataResponse proto.InternalMessageInfo

type UpdateCommodityDataOrdersStatusRequest struct {
	DataOrderId          string   `protobuf:"bytes,1,opt,name=data_order_id,json=dataOrderId,proto3" json:"data_order_id,omitempty"`
	PayStatus            uint32   `protobuf:"varint,2,opt,name=pay_status,json=payStatus,proto3" json:"pay_status,omitempty"`
	BigTradeNo           string   `protobuf:"bytes,3,opt,name=big_trade_no,json=bigTradeNo,proto3" json:"big_trade_no,omitempty"`
	IsCommit             bool     `protobuf:"varint,4,opt,name=is_commit,json=isCommit,proto3" json:"is_commit,omitempty"`
	Uid                  uint32   `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	TotalPrice           uint32   `protobuf:"varint,7,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCommodityDataOrdersStatusRequest) Reset() {
	*m = UpdateCommodityDataOrdersStatusRequest{}
}
func (m *UpdateCommodityDataOrdersStatusRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityDataOrdersStatusRequest) ProtoMessage()    {}
func (*UpdateCommodityDataOrdersStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{29}
}
func (m *UpdateCommodityDataOrdersStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityDataOrdersStatusRequest.Unmarshal(m, b)
}
func (m *UpdateCommodityDataOrdersStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityDataOrdersStatusRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityDataOrdersStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityDataOrdersStatusRequest.Merge(dst, src)
}
func (m *UpdateCommodityDataOrdersStatusRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityDataOrdersStatusRequest.Size(m)
}
func (m *UpdateCommodityDataOrdersStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityDataOrdersStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityDataOrdersStatusRequest proto.InternalMessageInfo

func (m *UpdateCommodityDataOrdersStatusRequest) GetDataOrderId() string {
	if m != nil {
		return m.DataOrderId
	}
	return ""
}

func (m *UpdateCommodityDataOrdersStatusRequest) GetPayStatus() uint32 {
	if m != nil {
		return m.PayStatus
	}
	return 0
}

func (m *UpdateCommodityDataOrdersStatusRequest) GetBigTradeNo() string {
	if m != nil {
		return m.BigTradeNo
	}
	return ""
}

func (m *UpdateCommodityDataOrdersStatusRequest) GetIsCommit() bool {
	if m != nil {
		return m.IsCommit
	}
	return false
}

func (m *UpdateCommodityDataOrdersStatusRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateCommodityDataOrdersStatusRequest) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *UpdateCommodityDataOrdersStatusRequest) GetTotalPrice() uint32 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

type UpdateCommodityDataOrdersStatusResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCommodityDataOrdersStatusResponse) Reset() {
	*m = UpdateCommodityDataOrdersStatusResponse{}
}
func (m *UpdateCommodityDataOrdersStatusResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityDataOrdersStatusResponse) ProtoMessage()    {}
func (*UpdateCommodityDataOrdersStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{30}
}
func (m *UpdateCommodityDataOrdersStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityDataOrdersStatusResponse.Unmarshal(m, b)
}
func (m *UpdateCommodityDataOrdersStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityDataOrdersStatusResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityDataOrdersStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityDataOrdersStatusResponse.Merge(dst, src)
}
func (m *UpdateCommodityDataOrdersStatusResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityDataOrdersStatusResponse.Size(m)
}
func (m *UpdateCommodityDataOrdersStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityDataOrdersStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityDataOrdersStatusResponse proto.InternalMessageInfo

type BatchGetCommodityDataOrdersRequest struct {
	DataOrderId          []string `protobuf:"bytes,1,rep,name=data_order_id,json=dataOrderId,proto3" json:"data_order_id,omitempty"`
	IsBigTradeNo         bool     `protobuf:"varint,2,opt,name=is_big_trade_no,json=isBigTradeNo,proto3" json:"is_big_trade_no,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCommodityDataOrdersRequest) Reset()         { *m = BatchGetCommodityDataOrdersRequest{} }
func (m *BatchGetCommodityDataOrdersRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetCommodityDataOrdersRequest) ProtoMessage()    {}
func (*BatchGetCommodityDataOrdersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{31}
}
func (m *BatchGetCommodityDataOrdersRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCommodityDataOrdersRequest.Unmarshal(m, b)
}
func (m *BatchGetCommodityDataOrdersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCommodityDataOrdersRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetCommodityDataOrdersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCommodityDataOrdersRequest.Merge(dst, src)
}
func (m *BatchGetCommodityDataOrdersRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetCommodityDataOrdersRequest.Size(m)
}
func (m *BatchGetCommodityDataOrdersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCommodityDataOrdersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCommodityDataOrdersRequest proto.InternalMessageInfo

func (m *BatchGetCommodityDataOrdersRequest) GetDataOrderId() []string {
	if m != nil {
		return m.DataOrderId
	}
	return nil
}

func (m *BatchGetCommodityDataOrdersRequest) GetIsBigTradeNo() bool {
	if m != nil {
		return m.IsBigTradeNo
	}
	return false
}

type BatchGetCommodityDataOrdersResponse struct {
	Orders               []*CommodityDataOrders `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchGetCommodityDataOrdersResponse) Reset()         { *m = BatchGetCommodityDataOrdersResponse{} }
func (m *BatchGetCommodityDataOrdersResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetCommodityDataOrdersResponse) ProtoMessage()    {}
func (*BatchGetCommodityDataOrdersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{32}
}
func (m *BatchGetCommodityDataOrdersResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCommodityDataOrdersResponse.Unmarshal(m, b)
}
func (m *BatchGetCommodityDataOrdersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCommodityDataOrdersResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetCommodityDataOrdersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCommodityDataOrdersResponse.Merge(dst, src)
}
func (m *BatchGetCommodityDataOrdersResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetCommodityDataOrdersResponse.Size(m)
}
func (m *BatchGetCommodityDataOrdersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCommodityDataOrdersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCommodityDataOrdersResponse proto.InternalMessageInfo

func (m *BatchGetCommodityDataOrdersResponse) GetOrders() []*CommodityDataOrders {
	if m != nil {
		return m.Orders
	}
	return nil
}

// 获取已冻结订单
type GetCommodityDataOrdersPayingRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCommodityDataOrdersPayingRequest) Reset()         { *m = GetCommodityDataOrdersPayingRequest{} }
func (m *GetCommodityDataOrdersPayingRequest) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataOrdersPayingRequest) ProtoMessage()    {}
func (*GetCommodityDataOrdersPayingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{33}
}
func (m *GetCommodityDataOrdersPayingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataOrdersPayingRequest.Unmarshal(m, b)
}
func (m *GetCommodityDataOrdersPayingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataOrdersPayingRequest.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataOrdersPayingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataOrdersPayingRequest.Merge(dst, src)
}
func (m *GetCommodityDataOrdersPayingRequest) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataOrdersPayingRequest.Size(m)
}
func (m *GetCommodityDataOrdersPayingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataOrdersPayingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataOrdersPayingRequest proto.InternalMessageInfo

type GetCommodityDataOrdersPayingResponse struct {
	Orders               []*CommodityDataOrders `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetCommodityDataOrdersPayingResponse) Reset()         { *m = GetCommodityDataOrdersPayingResponse{} }
func (m *GetCommodityDataOrdersPayingResponse) String() string { return proto.CompactTextString(m) }
func (*GetCommodityDataOrdersPayingResponse) ProtoMessage()    {}
func (*GetCommodityDataOrdersPayingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{34}
}
func (m *GetCommodityDataOrdersPayingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCommodityDataOrdersPayingResponse.Unmarshal(m, b)
}
func (m *GetCommodityDataOrdersPayingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCommodityDataOrdersPayingResponse.Marshal(b, m, deterministic)
}
func (dst *GetCommodityDataOrdersPayingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommodityDataOrdersPayingResponse.Merge(dst, src)
}
func (m *GetCommodityDataOrdersPayingResponse) XXX_Size() int {
	return xxx_messageInfo_GetCommodityDataOrdersPayingResponse.Size(m)
}
func (m *GetCommodityDataOrdersPayingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommodityDataOrdersPayingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommodityDataOrdersPayingResponse proto.InternalMessageInfo

func (m *GetCommodityDataOrdersPayingResponse) GetOrders() []*CommodityDataOrders {
	if m != nil {
		return m.Orders
	}
	return nil
}

type AddShoppingCarRequest struct {
	CommodityItem        *ShoppingItemBasic `protobuf:"bytes,2,opt,name=commodity_item,json=commodityItem,proto3" json:"commodity_item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddShoppingCarRequest) Reset()         { *m = AddShoppingCarRequest{} }
func (m *AddShoppingCarRequest) String() string { return proto.CompactTextString(m) }
func (*AddShoppingCarRequest) ProtoMessage()    {}
func (*AddShoppingCarRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{35}
}
func (m *AddShoppingCarRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddShoppingCarRequest.Unmarshal(m, b)
}
func (m *AddShoppingCarRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddShoppingCarRequest.Marshal(b, m, deterministic)
}
func (dst *AddShoppingCarRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddShoppingCarRequest.Merge(dst, src)
}
func (m *AddShoppingCarRequest) XXX_Size() int {
	return xxx_messageInfo_AddShoppingCarRequest.Size(m)
}
func (m *AddShoppingCarRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddShoppingCarRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddShoppingCarRequest proto.InternalMessageInfo

func (m *AddShoppingCarRequest) GetCommodityItem() *ShoppingItemBasic {
	if m != nil {
		return m.CommodityItem
	}
	return nil
}

type AddShoppingCarResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddShoppingCarResponse) Reset()         { *m = AddShoppingCarResponse{} }
func (m *AddShoppingCarResponse) String() string { return proto.CompactTextString(m) }
func (*AddShoppingCarResponse) ProtoMessage()    {}
func (*AddShoppingCarResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{36}
}
func (m *AddShoppingCarResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddShoppingCarResponse.Unmarshal(m, b)
}
func (m *AddShoppingCarResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddShoppingCarResponse.Marshal(b, m, deterministic)
}
func (dst *AddShoppingCarResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddShoppingCarResponse.Merge(dst, src)
}
func (m *AddShoppingCarResponse) XXX_Size() int {
	return xxx_messageInfo_AddShoppingCarResponse.Size(m)
}
func (m *AddShoppingCarResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddShoppingCarResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddShoppingCarResponse proto.InternalMessageInfo

type DelShoppingCarRequest struct {
	ShoppingItemId       []uint32 `protobuf:"varint,1,rep,packed,name=shopping_item_id,json=shoppingItemId,proto3" json:"shopping_item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelShoppingCarRequest) Reset()         { *m = DelShoppingCarRequest{} }
func (m *DelShoppingCarRequest) String() string { return proto.CompactTextString(m) }
func (*DelShoppingCarRequest) ProtoMessage()    {}
func (*DelShoppingCarRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{37}
}
func (m *DelShoppingCarRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelShoppingCarRequest.Unmarshal(m, b)
}
func (m *DelShoppingCarRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelShoppingCarRequest.Marshal(b, m, deterministic)
}
func (dst *DelShoppingCarRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelShoppingCarRequest.Merge(dst, src)
}
func (m *DelShoppingCarRequest) XXX_Size() int {
	return xxx_messageInfo_DelShoppingCarRequest.Size(m)
}
func (m *DelShoppingCarRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelShoppingCarRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelShoppingCarRequest proto.InternalMessageInfo

func (m *DelShoppingCarRequest) GetShoppingItemId() []uint32 {
	if m != nil {
		return m.ShoppingItemId
	}
	return nil
}

type DelShoppingCarResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelShoppingCarResponse) Reset()         { *m = DelShoppingCarResponse{} }
func (m *DelShoppingCarResponse) String() string { return proto.CompactTextString(m) }
func (*DelShoppingCarResponse) ProtoMessage()    {}
func (*DelShoppingCarResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{38}
}
func (m *DelShoppingCarResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelShoppingCarResponse.Unmarshal(m, b)
}
func (m *DelShoppingCarResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelShoppingCarResponse.Marshal(b, m, deterministic)
}
func (dst *DelShoppingCarResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelShoppingCarResponse.Merge(dst, src)
}
func (m *DelShoppingCarResponse) XXX_Size() int {
	return xxx_messageInfo_DelShoppingCarResponse.Size(m)
}
func (m *DelShoppingCarResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelShoppingCarResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelShoppingCarResponse proto.InternalMessageInfo

type BathGetShoppingCarRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BathGetShoppingCarRequest) Reset()         { *m = BathGetShoppingCarRequest{} }
func (m *BathGetShoppingCarRequest) String() string { return proto.CompactTextString(m) }
func (*BathGetShoppingCarRequest) ProtoMessage()    {}
func (*BathGetShoppingCarRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{39}
}
func (m *BathGetShoppingCarRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BathGetShoppingCarRequest.Unmarshal(m, b)
}
func (m *BathGetShoppingCarRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BathGetShoppingCarRequest.Marshal(b, m, deterministic)
}
func (dst *BathGetShoppingCarRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BathGetShoppingCarRequest.Merge(dst, src)
}
func (m *BathGetShoppingCarRequest) XXX_Size() int {
	return xxx_messageInfo_BathGetShoppingCarRequest.Size(m)
}
func (m *BathGetShoppingCarRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BathGetShoppingCarRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BathGetShoppingCarRequest proto.InternalMessageInfo

type BathGetShoppingCarResponse struct {
	ShoppingItemList     []*ShoppingItemBasic `protobuf:"bytes,1,rep,name=shopping_item_list,json=shoppingItemList,proto3" json:"shopping_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BathGetShoppingCarResponse) Reset()         { *m = BathGetShoppingCarResponse{} }
func (m *BathGetShoppingCarResponse) String() string { return proto.CompactTextString(m) }
func (*BathGetShoppingCarResponse) ProtoMessage()    {}
func (*BathGetShoppingCarResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{40}
}
func (m *BathGetShoppingCarResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BathGetShoppingCarResponse.Unmarshal(m, b)
}
func (m *BathGetShoppingCarResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BathGetShoppingCarResponse.Marshal(b, m, deterministic)
}
func (dst *BathGetShoppingCarResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BathGetShoppingCarResponse.Merge(dst, src)
}
func (m *BathGetShoppingCarResponse) XXX_Size() int {
	return xxx_messageInfo_BathGetShoppingCarResponse.Size(m)
}
func (m *BathGetShoppingCarResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BathGetShoppingCarResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BathGetShoppingCarResponse proto.InternalMessageInfo

func (m *BathGetShoppingCarResponse) GetShoppingItemList() []*ShoppingItemBasic {
	if m != nil {
		return m.ShoppingItemList
	}
	return nil
}

type UpdateCommodityPackageRedDotReq struct {
	PackageId            uint32   `protobuf:"varint,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	IsRefreshedRedDot    bool     `protobuf:"varint,2,opt,name=is_refreshed_red_dot,json=isRefreshedRedDot,proto3" json:"is_refreshed_red_dot,omitempty"`
	Category             uint32   `protobuf:"varint,3,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32   `protobuf:"varint,4,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	CommodityId          uint32   `protobuf:"varint,5,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	ShelfTime            uint32   `protobuf:"varint,6,opt,name=shelf_time,json=shelfTime,proto3" json:"shelf_time,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,7,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	IsContinue           bool     `protobuf:"varint,8,opt,name=is_continue,json=isContinue,proto3" json:"is_continue,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCommodityPackageRedDotReq) Reset()         { *m = UpdateCommodityPackageRedDotReq{} }
func (m *UpdateCommodityPackageRedDotReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityPackageRedDotReq) ProtoMessage()    {}
func (*UpdateCommodityPackageRedDotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{41}
}
func (m *UpdateCommodityPackageRedDotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityPackageRedDotReq.Unmarshal(m, b)
}
func (m *UpdateCommodityPackageRedDotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityPackageRedDotReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityPackageRedDotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityPackageRedDotReq.Merge(dst, src)
}
func (m *UpdateCommodityPackageRedDotReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityPackageRedDotReq.Size(m)
}
func (m *UpdateCommodityPackageRedDotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityPackageRedDotReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityPackageRedDotReq proto.InternalMessageInfo

func (m *UpdateCommodityPackageRedDotReq) GetPackageId() uint32 {
	if m != nil {
		return m.PackageId
	}
	return 0
}

func (m *UpdateCommodityPackageRedDotReq) GetIsRefreshedRedDot() bool {
	if m != nil {
		return m.IsRefreshedRedDot
	}
	return false
}

func (m *UpdateCommodityPackageRedDotReq) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *UpdateCommodityPackageRedDotReq) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *UpdateCommodityPackageRedDotReq) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

func (m *UpdateCommodityPackageRedDotReq) GetShelfTime() uint32 {
	if m != nil {
		return m.ShelfTime
	}
	return 0
}

func (m *UpdateCommodityPackageRedDotReq) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *UpdateCommodityPackageRedDotReq) GetIsContinue() bool {
	if m != nil {
		return m.IsContinue
	}
	return false
}

type UpdateCommodityPackageRedDotResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCommodityPackageRedDotResp) Reset()         { *m = UpdateCommodityPackageRedDotResp{} }
func (m *UpdateCommodityPackageRedDotResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCommodityPackageRedDotResp) ProtoMessage()    {}
func (*UpdateCommodityPackageRedDotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{42}
}
func (m *UpdateCommodityPackageRedDotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCommodityPackageRedDotResp.Unmarshal(m, b)
}
func (m *UpdateCommodityPackageRedDotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCommodityPackageRedDotResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCommodityPackageRedDotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCommodityPackageRedDotResp.Merge(dst, src)
}
func (m *UpdateCommodityPackageRedDotResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCommodityPackageRedDotResp.Size(m)
}
func (m *UpdateCommodityPackageRedDotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCommodityPackageRedDotResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCommodityPackageRedDotResp proto.InternalMessageInfo

type GetUnRefreshedRedDotPackageDataReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUnRefreshedRedDotPackageDataReq) Reset()         { *m = GetUnRefreshedRedDotPackageDataReq{} }
func (m *GetUnRefreshedRedDotPackageDataReq) String() string { return proto.CompactTextString(m) }
func (*GetUnRefreshedRedDotPackageDataReq) ProtoMessage()    {}
func (*GetUnRefreshedRedDotPackageDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{43}
}
func (m *GetUnRefreshedRedDotPackageDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUnRefreshedRedDotPackageDataReq.Unmarshal(m, b)
}
func (m *GetUnRefreshedRedDotPackageDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUnRefreshedRedDotPackageDataReq.Marshal(b, m, deterministic)
}
func (dst *GetUnRefreshedRedDotPackageDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUnRefreshedRedDotPackageDataReq.Merge(dst, src)
}
func (m *GetUnRefreshedRedDotPackageDataReq) XXX_Size() int {
	return xxx_messageInfo_GetUnRefreshedRedDotPackageDataReq.Size(m)
}
func (m *GetUnRefreshedRedDotPackageDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUnRefreshedRedDotPackageDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUnRefreshedRedDotPackageDataReq proto.InternalMessageInfo

type GetUnRefreshedRedDotPackageDataResp struct {
	PackageList          []*CommodityDataPackage `protobuf:"bytes,1,rep,name=package_list,json=packageList,proto3" json:"package_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetUnRefreshedRedDotPackageDataResp) Reset()         { *m = GetUnRefreshedRedDotPackageDataResp{} }
func (m *GetUnRefreshedRedDotPackageDataResp) String() string { return proto.CompactTextString(m) }
func (*GetUnRefreshedRedDotPackageDataResp) ProtoMessage()    {}
func (*GetUnRefreshedRedDotPackageDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{44}
}
func (m *GetUnRefreshedRedDotPackageDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUnRefreshedRedDotPackageDataResp.Unmarshal(m, b)
}
func (m *GetUnRefreshedRedDotPackageDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUnRefreshedRedDotPackageDataResp.Marshal(b, m, deterministic)
}
func (dst *GetUnRefreshedRedDotPackageDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUnRefreshedRedDotPackageDataResp.Merge(dst, src)
}
func (m *GetUnRefreshedRedDotPackageDataResp) XXX_Size() int {
	return xxx_messageInfo_GetUnRefreshedRedDotPackageDataResp.Size(m)
}
func (m *GetUnRefreshedRedDotPackageDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUnRefreshedRedDotPackageDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUnRefreshedRedDotPackageDataResp proto.InternalMessageInfo

func (m *GetUnRefreshedRedDotPackageDataResp) GetPackageList() []*CommodityDataPackage {
	if m != nil {
		return m.PackageList
	}
	return nil
}

type CommodityUserReadRedDotReq struct {
	Category             uint32   `protobuf:"varint,1,opt,name=category,proto3" json:"category,omitempty"`
	SubCategory          uint32   `protobuf:"varint,2,opt,name=sub_category,json=subCategory,proto3" json:"sub_category,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	RedDotAlerType       uint32   `protobuf:"varint,4,opt,name=red_dot_aler_type,json=redDotAlerType,proto3" json:"red_dot_aler_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommodityUserReadRedDotReq) Reset()         { *m = CommodityUserReadRedDotReq{} }
func (m *CommodityUserReadRedDotReq) String() string { return proto.CompactTextString(m) }
func (*CommodityUserReadRedDotReq) ProtoMessage()    {}
func (*CommodityUserReadRedDotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{45}
}
func (m *CommodityUserReadRedDotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityUserReadRedDotReq.Unmarshal(m, b)
}
func (m *CommodityUserReadRedDotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityUserReadRedDotReq.Marshal(b, m, deterministic)
}
func (dst *CommodityUserReadRedDotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityUserReadRedDotReq.Merge(dst, src)
}
func (m *CommodityUserReadRedDotReq) XXX_Size() int {
	return xxx_messageInfo_CommodityUserReadRedDotReq.Size(m)
}
func (m *CommodityUserReadRedDotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityUserReadRedDotReq.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityUserReadRedDotReq proto.InternalMessageInfo

func (m *CommodityUserReadRedDotReq) GetCategory() uint32 {
	if m != nil {
		return m.Category
	}
	return 0
}

func (m *CommodityUserReadRedDotReq) GetSubCategory() uint32 {
	if m != nil {
		return m.SubCategory
	}
	return 0
}

func (m *CommodityUserReadRedDotReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommodityUserReadRedDotReq) GetRedDotAlerType() uint32 {
	if m != nil {
		return m.RedDotAlerType
	}
	return 0
}

type CommodityUserReadRedDotResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommodityUserReadRedDotResp) Reset()         { *m = CommodityUserReadRedDotResp{} }
func (m *CommodityUserReadRedDotResp) String() string { return proto.CompactTextString(m) }
func (*CommodityUserReadRedDotResp) ProtoMessage()    {}
func (*CommodityUserReadRedDotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{46}
}
func (m *CommodityUserReadRedDotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommodityUserReadRedDotResp.Unmarshal(m, b)
}
func (m *CommodityUserReadRedDotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommodityUserReadRedDotResp.Marshal(b, m, deterministic)
}
func (dst *CommodityUserReadRedDotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommodityUserReadRedDotResp.Merge(dst, src)
}
func (m *CommodityUserReadRedDotResp) XXX_Size() int {
	return xxx_messageInfo_CommodityUserReadRedDotResp.Size(m)
}
func (m *CommodityUserReadRedDotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CommodityUserReadRedDotResp.DiscardUnknown(m)
}

var xxx_messageInfo_CommodityUserReadRedDotResp proto.InternalMessageInfo

type GetUserUnReadCommodityRedDotReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RedDotAlerType       uint32   `protobuf:"varint,2,opt,name=red_dot_aler_type,json=redDotAlerType,proto3" json:"red_dot_aler_type,omitempty"`
	CategoryList         []uint32 `protobuf:"varint,3,rep,packed,name=category_list,json=categoryList,proto3" json:"category_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserUnReadCommodityRedDotReq) Reset()         { *m = GetUserUnReadCommodityRedDotReq{} }
func (m *GetUserUnReadCommodityRedDotReq) String() string { return proto.CompactTextString(m) }
func (*GetUserUnReadCommodityRedDotReq) ProtoMessage()    {}
func (*GetUserUnReadCommodityRedDotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{47}
}
func (m *GetUserUnReadCommodityRedDotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserUnReadCommodityRedDotReq.Unmarshal(m, b)
}
func (m *GetUserUnReadCommodityRedDotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserUnReadCommodityRedDotReq.Marshal(b, m, deterministic)
}
func (dst *GetUserUnReadCommodityRedDotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserUnReadCommodityRedDotReq.Merge(dst, src)
}
func (m *GetUserUnReadCommodityRedDotReq) XXX_Size() int {
	return xxx_messageInfo_GetUserUnReadCommodityRedDotReq.Size(m)
}
func (m *GetUserUnReadCommodityRedDotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserUnReadCommodityRedDotReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserUnReadCommodityRedDotReq proto.InternalMessageInfo

func (m *GetUserUnReadCommodityRedDotReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserUnReadCommodityRedDotReq) GetRedDotAlerType() uint32 {
	if m != nil {
		return m.RedDotAlerType
	}
	return 0
}

func (m *GetUserUnReadCommodityRedDotReq) GetCategoryList() []uint32 {
	if m != nil {
		return m.CategoryList
	}
	return nil
}

type GetUserUnReadCommodityRedDotResp struct {
	RedDotAlerType       uint32                                                     `protobuf:"varint,1,opt,name=red_dot_aler_type,json=redDotAlerType,proto3" json:"red_dot_aler_type,omitempty"`
	HasRedDot            bool                                                       `protobuf:"varint,2,opt,name=has_red_dot,json=hasRedDot,proto3" json:"has_red_dot,omitempty"`
	CategoryList         []*virtual_image_resource.VirtualImageResourceCategoryInfo `protobuf:"bytes,3,rep,name=category_list,json=categoryList,proto3" json:"category_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                                   `json:"-"`
	XXX_unrecognized     []byte                                                     `json:"-"`
	XXX_sizecache        int32                                                      `json:"-"`
}

func (m *GetUserUnReadCommodityRedDotResp) Reset()         { *m = GetUserUnReadCommodityRedDotResp{} }
func (m *GetUserUnReadCommodityRedDotResp) String() string { return proto.CompactTextString(m) }
func (*GetUserUnReadCommodityRedDotResp) ProtoMessage()    {}
func (*GetUserUnReadCommodityRedDotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{48}
}
func (m *GetUserUnReadCommodityRedDotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserUnReadCommodityRedDotResp.Unmarshal(m, b)
}
func (m *GetUserUnReadCommodityRedDotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserUnReadCommodityRedDotResp.Marshal(b, m, deterministic)
}
func (dst *GetUserUnReadCommodityRedDotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserUnReadCommodityRedDotResp.Merge(dst, src)
}
func (m *GetUserUnReadCommodityRedDotResp) XXX_Size() int {
	return xxx_messageInfo_GetUserUnReadCommodityRedDotResp.Size(m)
}
func (m *GetUserUnReadCommodityRedDotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserUnReadCommodityRedDotResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserUnReadCommodityRedDotResp proto.InternalMessageInfo

func (m *GetUserUnReadCommodityRedDotResp) GetRedDotAlerType() uint32 {
	if m != nil {
		return m.RedDotAlerType
	}
	return 0
}

func (m *GetUserUnReadCommodityRedDotResp) GetHasRedDot() bool {
	if m != nil {
		return m.HasRedDot
	}
	return false
}

func (m *GetUserUnReadCommodityRedDotResp) GetCategoryList() []*virtual_image_resource.VirtualImageResourceCategoryInfo {
	if m != nil {
		return m.CategoryList
	}
	return nil
}

type BatchGetCommodityRedDotReq struct {
	IsGetDetailedData    bool     `protobuf:"varint,1,opt,name=is_get_detailed_data,json=isGetDetailedData,proto3" json:"is_get_detailed_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCommodityRedDotReq) Reset()         { *m = BatchGetCommodityRedDotReq{} }
func (m *BatchGetCommodityRedDotReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetCommodityRedDotReq) ProtoMessage()    {}
func (*BatchGetCommodityRedDotReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{49}
}
func (m *BatchGetCommodityRedDotReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCommodityRedDotReq.Unmarshal(m, b)
}
func (m *BatchGetCommodityRedDotReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCommodityRedDotReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetCommodityRedDotReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCommodityRedDotReq.Merge(dst, src)
}
func (m *BatchGetCommodityRedDotReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetCommodityRedDotReq.Size(m)
}
func (m *BatchGetCommodityRedDotReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCommodityRedDotReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCommodityRedDotReq proto.InternalMessageInfo

func (m *BatchGetCommodityRedDotReq) GetIsGetDetailedData() bool {
	if m != nil {
		return m.IsGetDetailedData
	}
	return false
}

type BatchGetCommodityRedDotResp struct {
	GlobalVersion        uint32            `protobuf:"varint,1,opt,name=global_version,json=globalVersion,proto3" json:"global_version,omitempty"`
	CommodityIdVersion   map[uint32]uint32 `protobuf:"bytes,2,rep,name=commodity_id_version,json=commodityIdVersion,proto3" json:"commodity_id_version,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetCommodityRedDotResp) Reset()         { *m = BatchGetCommodityRedDotResp{} }
func (m *BatchGetCommodityRedDotResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetCommodityRedDotResp) ProtoMessage()    {}
func (*BatchGetCommodityRedDotResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{50}
}
func (m *BatchGetCommodityRedDotResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCommodityRedDotResp.Unmarshal(m, b)
}
func (m *BatchGetCommodityRedDotResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCommodityRedDotResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetCommodityRedDotResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCommodityRedDotResp.Merge(dst, src)
}
func (m *BatchGetCommodityRedDotResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetCommodityRedDotResp.Size(m)
}
func (m *BatchGetCommodityRedDotResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCommodityRedDotResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCommodityRedDotResp proto.InternalMessageInfo

func (m *BatchGetCommodityRedDotResp) GetGlobalVersion() uint32 {
	if m != nil {
		return m.GlobalVersion
	}
	return 0
}

func (m *BatchGetCommodityRedDotResp) GetCommodityIdVersion() map[uint32]uint32 {
	if m != nil {
		return m.CommodityIdVersion
	}
	return nil
}

type DelCommodityDataReq struct {
	CommodityId          uint32   `protobuf:"varint,1,opt,name=commodity_id,json=commodityId,proto3" json:"commodity_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCommodityDataReq) Reset()         { *m = DelCommodityDataReq{} }
func (m *DelCommodityDataReq) String() string { return proto.CompactTextString(m) }
func (*DelCommodityDataReq) ProtoMessage()    {}
func (*DelCommodityDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{51}
}
func (m *DelCommodityDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCommodityDataReq.Unmarshal(m, b)
}
func (m *DelCommodityDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCommodityDataReq.Marshal(b, m, deterministic)
}
func (dst *DelCommodityDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCommodityDataReq.Merge(dst, src)
}
func (m *DelCommodityDataReq) XXX_Size() int {
	return xxx_messageInfo_DelCommodityDataReq.Size(m)
}
func (m *DelCommodityDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCommodityDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCommodityDataReq proto.InternalMessageInfo

func (m *DelCommodityDataReq) GetCommodityId() uint32 {
	if m != nil {
		return m.CommodityId
	}
	return 0
}

type DelCommodityDataResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCommodityDataResp) Reset()         { *m = DelCommodityDataResp{} }
func (m *DelCommodityDataResp) String() string { return proto.CompactTextString(m) }
func (*DelCommodityDataResp) ProtoMessage()    {}
func (*DelCommodityDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{52}
}
func (m *DelCommodityDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCommodityDataResp.Unmarshal(m, b)
}
func (m *DelCommodityDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCommodityDataResp.Marshal(b, m, deterministic)
}
func (dst *DelCommodityDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCommodityDataResp.Merge(dst, src)
}
func (m *DelCommodityDataResp) XXX_Size() int {
	return xxx_messageInfo_DelCommodityDataResp.Size(m)
}
func (m *DelCommodityDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCommodityDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCommodityDataResp proto.InternalMessageInfo

// 根据时间范围返回订单统计数据
type GetOrderDataByTimeRangeRequest struct {
	StartTime            int64    `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderDataByTimeRangeRequest) Reset()         { *m = GetOrderDataByTimeRangeRequest{} }
func (m *GetOrderDataByTimeRangeRequest) String() string { return proto.CompactTextString(m) }
func (*GetOrderDataByTimeRangeRequest) ProtoMessage()    {}
func (*GetOrderDataByTimeRangeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{53}
}
func (m *GetOrderDataByTimeRangeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderDataByTimeRangeRequest.Unmarshal(m, b)
}
func (m *GetOrderDataByTimeRangeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderDataByTimeRangeRequest.Marshal(b, m, deterministic)
}
func (dst *GetOrderDataByTimeRangeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderDataByTimeRangeRequest.Merge(dst, src)
}
func (m *GetOrderDataByTimeRangeRequest) XXX_Size() int {
	return xxx_messageInfo_GetOrderDataByTimeRangeRequest.Size(m)
}
func (m *GetOrderDataByTimeRangeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderDataByTimeRangeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderDataByTimeRangeRequest proto.InternalMessageInfo

func (m *GetOrderDataByTimeRangeRequest) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetOrderDataByTimeRangeRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetOrderDataByTimeRangeResponse struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	TotalPrice           int64    `protobuf:"varint,2,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`
	TotalCount           int64    `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderDataByTimeRangeResponse) Reset()         { *m = GetOrderDataByTimeRangeResponse{} }
func (m *GetOrderDataByTimeRangeResponse) String() string { return proto.CompactTextString(m) }
func (*GetOrderDataByTimeRangeResponse) ProtoMessage()    {}
func (*GetOrderDataByTimeRangeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_virtual_image_mall_7243ff8390503a42, []int{54}
}
func (m *GetOrderDataByTimeRangeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderDataByTimeRangeResponse.Unmarshal(m, b)
}
func (m *GetOrderDataByTimeRangeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderDataByTimeRangeResponse.Marshal(b, m, deterministic)
}
func (dst *GetOrderDataByTimeRangeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderDataByTimeRangeResponse.Merge(dst, src)
}
func (m *GetOrderDataByTimeRangeResponse) XXX_Size() int {
	return xxx_messageInfo_GetOrderDataByTimeRangeResponse.Size(m)
}
func (m *GetOrderDataByTimeRangeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderDataByTimeRangeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderDataByTimeRangeResponse proto.InternalMessageInfo

func (m *GetOrderDataByTimeRangeResponse) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetOrderDataByTimeRangeResponse) GetTotalPrice() int64 {
	if m != nil {
		return m.TotalPrice
	}
	return 0
}

func (m *GetOrderDataByTimeRangeResponse) GetTotalCount() int64 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func init() {
	proto.RegisterType((*CommodityDataPackage)(nil), "virtual_image_mall.CommodityDataPackage")
	proto.RegisterType((*CustomizeLogotype)(nil), "virtual_image_mall.CustomizeLogotype")
	proto.RegisterType((*ActUrl)(nil), "virtual_image_mall.ActUrl")
	proto.RegisterType((*ActivityInfo)(nil), "virtual_image_mall.ActivityInfo")
	proto.RegisterType((*CommodityData)(nil), "virtual_image_mall.CommodityData")
	proto.RegisterType((*SortData)(nil), "virtual_image_mall.SortData")
	proto.RegisterType((*GetCommodityDataListRequest)(nil), "virtual_image_mall.GetCommodityDataListRequest")
	proto.RegisterType((*GetCommodityDataListResponse)(nil), "virtual_image_mall.GetCommodityDataListResponse")
	proto.RegisterType((*GetCommodityDataListByPriceRequest)(nil), "virtual_image_mall.GetCommodityDataListByPriceRequest")
	proto.RegisterType((*GetCommodityDataListByPriceResponse)(nil), "virtual_image_mall.GetCommodityDataListByPriceResponse")
	proto.RegisterType((*BatAddCommodityReq)(nil), "virtual_image_mall.BatAddCommodityReq")
	proto.RegisterType((*BatAddCommodityResp)(nil), "virtual_image_mall.BatAddCommodityResp")
	proto.RegisterType((*UpdateCommodityReq)(nil), "virtual_image_mall.UpdateCommodityReq")
	proto.RegisterType((*UpdateCommodityResp)(nil), "virtual_image_mall.UpdateCommodityResp")
	proto.RegisterType((*UpdateCommodityResourceReq)(nil), "virtual_image_mall.UpdateCommodityResourceReq")
	proto.RegisterType((*UpdateCommodityResourceResp)(nil), "virtual_image_mall.UpdateCommodityResourceResp")
	proto.RegisterType((*CommodityRecommendInfo)(nil), "virtual_image_mall.CommodityRecommendInfo")
	proto.RegisterType((*GetCommodityRecommendListRequest)(nil), "virtual_image_mall.GetCommodityRecommendListRequest")
	proto.RegisterType((*GetCommodityRecommendListResponse)(nil), "virtual_image_mall.GetCommodityRecommendListResponse")
	proto.RegisterType((*BatAddCommodityRecommendReq)(nil), "virtual_image_mall.BatAddCommodityRecommendReq")
	proto.RegisterType((*BatAddCommodityRecommendResp)(nil), "virtual_image_mall.BatAddCommodityRecommendResp")
	proto.RegisterType((*UpdateCommodityRecommendReq)(nil), "virtual_image_mall.UpdateCommodityRecommendReq")
	proto.RegisterType((*UpdateCommodityRecommendResp)(nil), "virtual_image_mall.UpdateCommodityRecommendResp")
	proto.RegisterType((*DelCommodityRecommendReq)(nil), "virtual_image_mall.DelCommodityRecommendReq")
	proto.RegisterType((*DelCommodityRecommendResp)(nil), "virtual_image_mall.DelCommodityRecommendResp")
	proto.RegisterType((*CommodityDataOrders)(nil), "virtual_image_mall.CommodityDataOrders")
	proto.RegisterType((*ShoppingItemBasic)(nil), "virtual_image_mall.ShoppingItemBasic")
	proto.RegisterType((*BuyCommodityDataRequest)(nil), "virtual_image_mall.BuyCommodityDataRequest")
	proto.RegisterType((*BuyCommodityDataResponse)(nil), "virtual_image_mall.BuyCommodityDataResponse")
	proto.RegisterType((*UpdateCommodityDataOrdersStatusRequest)(nil), "virtual_image_mall.UpdateCommodityDataOrdersStatusRequest")
	proto.RegisterType((*UpdateCommodityDataOrdersStatusResponse)(nil), "virtual_image_mall.UpdateCommodityDataOrdersStatusResponse")
	proto.RegisterType((*BatchGetCommodityDataOrdersRequest)(nil), "virtual_image_mall.BatchGetCommodityDataOrdersRequest")
	proto.RegisterType((*BatchGetCommodityDataOrdersResponse)(nil), "virtual_image_mall.BatchGetCommodityDataOrdersResponse")
	proto.RegisterType((*GetCommodityDataOrdersPayingRequest)(nil), "virtual_image_mall.GetCommodityDataOrdersPayingRequest")
	proto.RegisterType((*GetCommodityDataOrdersPayingResponse)(nil), "virtual_image_mall.GetCommodityDataOrdersPayingResponse")
	proto.RegisterType((*AddShoppingCarRequest)(nil), "virtual_image_mall.AddShoppingCarRequest")
	proto.RegisterType((*AddShoppingCarResponse)(nil), "virtual_image_mall.AddShoppingCarResponse")
	proto.RegisterType((*DelShoppingCarRequest)(nil), "virtual_image_mall.DelShoppingCarRequest")
	proto.RegisterType((*DelShoppingCarResponse)(nil), "virtual_image_mall.DelShoppingCarResponse")
	proto.RegisterType((*BathGetShoppingCarRequest)(nil), "virtual_image_mall.BathGetShoppingCarRequest")
	proto.RegisterType((*BathGetShoppingCarResponse)(nil), "virtual_image_mall.BathGetShoppingCarResponse")
	proto.RegisterType((*UpdateCommodityPackageRedDotReq)(nil), "virtual_image_mall.UpdateCommodityPackageRedDotReq")
	proto.RegisterType((*UpdateCommodityPackageRedDotResp)(nil), "virtual_image_mall.UpdateCommodityPackageRedDotResp")
	proto.RegisterType((*GetUnRefreshedRedDotPackageDataReq)(nil), "virtual_image_mall.GetUnRefreshedRedDotPackageDataReq")
	proto.RegisterType((*GetUnRefreshedRedDotPackageDataResp)(nil), "virtual_image_mall.GetUnRefreshedRedDotPackageDataResp")
	proto.RegisterType((*CommodityUserReadRedDotReq)(nil), "virtual_image_mall.CommodityUserReadRedDotReq")
	proto.RegisterType((*CommodityUserReadRedDotResp)(nil), "virtual_image_mall.CommodityUserReadRedDotResp")
	proto.RegisterType((*GetUserUnReadCommodityRedDotReq)(nil), "virtual_image_mall.GetUserUnReadCommodityRedDotReq")
	proto.RegisterType((*GetUserUnReadCommodityRedDotResp)(nil), "virtual_image_mall.GetUserUnReadCommodityRedDotResp")
	proto.RegisterType((*BatchGetCommodityRedDotReq)(nil), "virtual_image_mall.BatchGetCommodityRedDotReq")
	proto.RegisterType((*BatchGetCommodityRedDotResp)(nil), "virtual_image_mall.BatchGetCommodityRedDotResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "virtual_image_mall.BatchGetCommodityRedDotResp.CommodityIdVersionEntry")
	proto.RegisterType((*DelCommodityDataReq)(nil), "virtual_image_mall.DelCommodityDataReq")
	proto.RegisterType((*DelCommodityDataResp)(nil), "virtual_image_mall.DelCommodityDataResp")
	proto.RegisterType((*GetOrderDataByTimeRangeRequest)(nil), "virtual_image_mall.GetOrderDataByTimeRangeRequest")
	proto.RegisterType((*GetOrderDataByTimeRangeResponse)(nil), "virtual_image_mall.GetOrderDataByTimeRangeResponse")
	proto.RegisterEnum("virtual_image_mall.CommodityGainPath", CommodityGainPath_name, CommodityGainPath_value)
	proto.RegisterEnum("virtual_image_mall.ShelfStatus", ShelfStatus_name, ShelfStatus_value)
	proto.RegisterEnum("virtual_image_mall.CommodityType", CommodityType_name, CommodityType_value)
	proto.RegisterEnum("virtual_image_mall.RightsType", RightsType_name, RightsType_value)
	proto.RegisterEnum("virtual_image_mall.SortType", SortType_name, SortType_value)
	proto.RegisterEnum("virtual_image_mall.CommodityPayStatus", CommodityPayStatus_name, CommodityPayStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// VirtualImageMallClient is the client API for VirtualImageMall service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VirtualImageMallClient interface {
	// 获取商品数据列表
	GetCommodityDataList(ctx context.Context, in *GetCommodityDataListRequest, opts ...grpc.CallOption) (*GetCommodityDataListResponse, error)
	GetCommodityRecommendList(ctx context.Context, in *GetCommodityRecommendListRequest, opts ...grpc.CallOption) (*GetCommodityRecommendListResponse, error)
	// 根据价格获取商品数据列表
	GetCommodityDataListByPrice(ctx context.Context, in *GetCommodityDataListByPriceRequest, opts ...grpc.CallOption) (*GetCommodityDataListByPriceResponse, error)
	// 批量添加商品
	BatAddCommodity(ctx context.Context, in *BatAddCommodityReq, opts ...grpc.CallOption) (*BatAddCommodityResp, error)
	// 更新商品信息
	UpdateCommodity(ctx context.Context, in *UpdateCommodityReq, opts ...grpc.CallOption) (*UpdateCommodityResp, error)
	// 批量增加推荐商品
	BatAddCommodityRecommend(ctx context.Context, in *BatAddCommodityRecommendReq, opts ...grpc.CallOption) (*BatAddCommodityRecommendResp, error)
	// 编辑推荐商品
	UpdateCommodityRecommend(ctx context.Context, in *UpdateCommodityRecommendReq, opts ...grpc.CallOption) (*UpdateCommodityRecommendResp, error)
	// 删除推荐商品信息
	DelCommodityRecommend(ctx context.Context, in *DelCommodityRecommendReq, opts ...grpc.CallOption) (*DelCommodityRecommendResp, error)
	// 记录购买订单
	BuyCommodityData(ctx context.Context, in *BuyCommodityDataRequest, opts ...grpc.CallOption) (*BuyCommodityDataResponse, error)
	// 更新购买订单状态
	UpdateCommodityDataOrdersStatus(ctx context.Context, in *UpdateCommodityDataOrdersStatusRequest, opts ...grpc.CallOption) (*UpdateCommodityDataOrdersStatusResponse, error)
	// 获取购买中订单
	GetCommodityDataOrdersPaying(ctx context.Context, in *GetCommodityDataOrdersPayingRequest, opts ...grpc.CallOption) (*GetCommodityDataOrdersPayingResponse, error)
	// 批量获取购买订单
	BatchGetCommodityDataOrders(ctx context.Context, in *BatchGetCommodityDataOrdersRequest, opts ...grpc.CallOption) (*BatchGetCommodityDataOrdersResponse, error)
	// 添加购物车
	AddShoppingCar(ctx context.Context, in *AddShoppingCarRequest, opts ...grpc.CallOption) (*AddShoppingCarResponse, error)
	// 删除购物车
	DelShoppingCar(ctx context.Context, in *DelShoppingCarRequest, opts ...grpc.CallOption) (*DelShoppingCarResponse, error)
	// 批量获取购物车
	BathGetShoppingCar(ctx context.Context, in *BathGetShoppingCarRequest, opts ...grpc.CallOption) (*BathGetShoppingCarResponse, error)
	// 更新商品的物品信息
	UpdateCommodityResource(ctx context.Context, in *UpdateCommodityResourceReq, opts ...grpc.CallOption) (*UpdateCommodityResourceResp, error)
	// 更新商品套餐红点
	UpdateCommodityPackageRedDot(ctx context.Context, in *UpdateCommodityPackageRedDotReq, opts ...grpc.CallOption) (*UpdateCommodityPackageRedDotResp, error)
	// 获取未刷新红点的商品套餐数据
	GetUnRefreshedRedDotPackageData(ctx context.Context, in *GetUnRefreshedRedDotPackageDataReq, opts ...grpc.CallOption) (*GetUnRefreshedRedDotPackageDataResp, error)
	// 品类用户已读
	CommodityUserReadRedDot(ctx context.Context, in *CommodityUserReadRedDotReq, opts ...grpc.CallOption) (*CommodityUserReadRedDotResp, error)
	// 获取用户未读品类
	GetUserUnReadCommodityRedDot(ctx context.Context, in *GetUserUnReadCommodityRedDotReq, opts ...grpc.CallOption) (*GetUserUnReadCommodityRedDotResp, error)
	BatchGetCommodityRedDot(ctx context.Context, in *BatchGetCommodityRedDotReq, opts ...grpc.CallOption) (*BatchGetCommodityRedDotResp, error)
	DelCommodityData(ctx context.Context, in *DelCommodityDataReq, opts ...grpc.CallOption) (*DelCommodityDataResp, error)
	// 根据时间范围返回订单统计数据
	GetOrderDataByTimeRange(ctx context.Context, in *GetOrderDataByTimeRangeRequest, opts ...grpc.CallOption) (*GetOrderDataByTimeRangeResponse, error)
	// T豆消费数据对账
	GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
}

type virtualImageMallClient struct {
	cc *grpc.ClientConn
}

func NewVirtualImageMallClient(cc *grpc.ClientConn) VirtualImageMallClient {
	return &virtualImageMallClient{cc}
}

func (c *virtualImageMallClient) GetCommodityDataList(ctx context.Context, in *GetCommodityDataListRequest, opts ...grpc.CallOption) (*GetCommodityDataListResponse, error) {
	out := new(GetCommodityDataListResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/GetCommodityDataList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) GetCommodityRecommendList(ctx context.Context, in *GetCommodityRecommendListRequest, opts ...grpc.CallOption) (*GetCommodityRecommendListResponse, error) {
	out := new(GetCommodityRecommendListResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/GetCommodityRecommendList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) GetCommodityDataListByPrice(ctx context.Context, in *GetCommodityDataListByPriceRequest, opts ...grpc.CallOption) (*GetCommodityDataListByPriceResponse, error) {
	out := new(GetCommodityDataListByPriceResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/GetCommodityDataListByPrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) BatAddCommodity(ctx context.Context, in *BatAddCommodityReq, opts ...grpc.CallOption) (*BatAddCommodityResp, error) {
	out := new(BatAddCommodityResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/BatAddCommodity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) UpdateCommodity(ctx context.Context, in *UpdateCommodityReq, opts ...grpc.CallOption) (*UpdateCommodityResp, error) {
	out := new(UpdateCommodityResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/UpdateCommodity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) BatAddCommodityRecommend(ctx context.Context, in *BatAddCommodityRecommendReq, opts ...grpc.CallOption) (*BatAddCommodityRecommendResp, error) {
	out := new(BatAddCommodityRecommendResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/BatAddCommodityRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) UpdateCommodityRecommend(ctx context.Context, in *UpdateCommodityRecommendReq, opts ...grpc.CallOption) (*UpdateCommodityRecommendResp, error) {
	out := new(UpdateCommodityRecommendResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/UpdateCommodityRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) DelCommodityRecommend(ctx context.Context, in *DelCommodityRecommendReq, opts ...grpc.CallOption) (*DelCommodityRecommendResp, error) {
	out := new(DelCommodityRecommendResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/DelCommodityRecommend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) BuyCommodityData(ctx context.Context, in *BuyCommodityDataRequest, opts ...grpc.CallOption) (*BuyCommodityDataResponse, error) {
	out := new(BuyCommodityDataResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/BuyCommodityData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) UpdateCommodityDataOrdersStatus(ctx context.Context, in *UpdateCommodityDataOrdersStatusRequest, opts ...grpc.CallOption) (*UpdateCommodityDataOrdersStatusResponse, error) {
	out := new(UpdateCommodityDataOrdersStatusResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/UpdateCommodityDataOrdersStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) GetCommodityDataOrdersPaying(ctx context.Context, in *GetCommodityDataOrdersPayingRequest, opts ...grpc.CallOption) (*GetCommodityDataOrdersPayingResponse, error) {
	out := new(GetCommodityDataOrdersPayingResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/GetCommodityDataOrdersPaying", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) BatchGetCommodityDataOrders(ctx context.Context, in *BatchGetCommodityDataOrdersRequest, opts ...grpc.CallOption) (*BatchGetCommodityDataOrdersResponse, error) {
	out := new(BatchGetCommodityDataOrdersResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/BatchGetCommodityDataOrders", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) AddShoppingCar(ctx context.Context, in *AddShoppingCarRequest, opts ...grpc.CallOption) (*AddShoppingCarResponse, error) {
	out := new(AddShoppingCarResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/AddShoppingCar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) DelShoppingCar(ctx context.Context, in *DelShoppingCarRequest, opts ...grpc.CallOption) (*DelShoppingCarResponse, error) {
	out := new(DelShoppingCarResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/DelShoppingCar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) BathGetShoppingCar(ctx context.Context, in *BathGetShoppingCarRequest, opts ...grpc.CallOption) (*BathGetShoppingCarResponse, error) {
	out := new(BathGetShoppingCarResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/BathGetShoppingCar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) UpdateCommodityResource(ctx context.Context, in *UpdateCommodityResourceReq, opts ...grpc.CallOption) (*UpdateCommodityResourceResp, error) {
	out := new(UpdateCommodityResourceResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/UpdateCommodityResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) UpdateCommodityPackageRedDot(ctx context.Context, in *UpdateCommodityPackageRedDotReq, opts ...grpc.CallOption) (*UpdateCommodityPackageRedDotResp, error) {
	out := new(UpdateCommodityPackageRedDotResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/UpdateCommodityPackageRedDot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) GetUnRefreshedRedDotPackageData(ctx context.Context, in *GetUnRefreshedRedDotPackageDataReq, opts ...grpc.CallOption) (*GetUnRefreshedRedDotPackageDataResp, error) {
	out := new(GetUnRefreshedRedDotPackageDataResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/GetUnRefreshedRedDotPackageData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) CommodityUserReadRedDot(ctx context.Context, in *CommodityUserReadRedDotReq, opts ...grpc.CallOption) (*CommodityUserReadRedDotResp, error) {
	out := new(CommodityUserReadRedDotResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/CommodityUserReadRedDot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) GetUserUnReadCommodityRedDot(ctx context.Context, in *GetUserUnReadCommodityRedDotReq, opts ...grpc.CallOption) (*GetUserUnReadCommodityRedDotResp, error) {
	out := new(GetUserUnReadCommodityRedDotResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/GetUserUnReadCommodityRedDot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) BatchGetCommodityRedDot(ctx context.Context, in *BatchGetCommodityRedDotReq, opts ...grpc.CallOption) (*BatchGetCommodityRedDotResp, error) {
	out := new(BatchGetCommodityRedDotResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/BatchGetCommodityRedDot", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) DelCommodityData(ctx context.Context, in *DelCommodityDataReq, opts ...grpc.CallOption) (*DelCommodityDataResp, error) {
	out := new(DelCommodityDataResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/DelCommodityData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) GetOrderDataByTimeRange(ctx context.Context, in *GetOrderDataByTimeRangeRequest, opts ...grpc.CallOption) (*GetOrderDataByTimeRangeResponse, error) {
	out := new(GetOrderDataByTimeRangeResponse)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/GetOrderDataByTimeRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/GetConsumeTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *virtualImageMallClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/virtual_image_mall.VirtualImageMall/GetConsumeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VirtualImageMallServer is the server API for VirtualImageMall service.
type VirtualImageMallServer interface {
	// 获取商品数据列表
	GetCommodityDataList(context.Context, *GetCommodityDataListRequest) (*GetCommodityDataListResponse, error)
	GetCommodityRecommendList(context.Context, *GetCommodityRecommendListRequest) (*GetCommodityRecommendListResponse, error)
	// 根据价格获取商品数据列表
	GetCommodityDataListByPrice(context.Context, *GetCommodityDataListByPriceRequest) (*GetCommodityDataListByPriceResponse, error)
	// 批量添加商品
	BatAddCommodity(context.Context, *BatAddCommodityReq) (*BatAddCommodityResp, error)
	// 更新商品信息
	UpdateCommodity(context.Context, *UpdateCommodityReq) (*UpdateCommodityResp, error)
	// 批量增加推荐商品
	BatAddCommodityRecommend(context.Context, *BatAddCommodityRecommendReq) (*BatAddCommodityRecommendResp, error)
	// 编辑推荐商品
	UpdateCommodityRecommend(context.Context, *UpdateCommodityRecommendReq) (*UpdateCommodityRecommendResp, error)
	// 删除推荐商品信息
	DelCommodityRecommend(context.Context, *DelCommodityRecommendReq) (*DelCommodityRecommendResp, error)
	// 记录购买订单
	BuyCommodityData(context.Context, *BuyCommodityDataRequest) (*BuyCommodityDataResponse, error)
	// 更新购买订单状态
	UpdateCommodityDataOrdersStatus(context.Context, *UpdateCommodityDataOrdersStatusRequest) (*UpdateCommodityDataOrdersStatusResponse, error)
	// 获取购买中订单
	GetCommodityDataOrdersPaying(context.Context, *GetCommodityDataOrdersPayingRequest) (*GetCommodityDataOrdersPayingResponse, error)
	// 批量获取购买订单
	BatchGetCommodityDataOrders(context.Context, *BatchGetCommodityDataOrdersRequest) (*BatchGetCommodityDataOrdersResponse, error)
	// 添加购物车
	AddShoppingCar(context.Context, *AddShoppingCarRequest) (*AddShoppingCarResponse, error)
	// 删除购物车
	DelShoppingCar(context.Context, *DelShoppingCarRequest) (*DelShoppingCarResponse, error)
	// 批量获取购物车
	BathGetShoppingCar(context.Context, *BathGetShoppingCarRequest) (*BathGetShoppingCarResponse, error)
	// 更新商品的物品信息
	UpdateCommodityResource(context.Context, *UpdateCommodityResourceReq) (*UpdateCommodityResourceResp, error)
	// 更新商品套餐红点
	UpdateCommodityPackageRedDot(context.Context, *UpdateCommodityPackageRedDotReq) (*UpdateCommodityPackageRedDotResp, error)
	// 获取未刷新红点的商品套餐数据
	GetUnRefreshedRedDotPackageData(context.Context, *GetUnRefreshedRedDotPackageDataReq) (*GetUnRefreshedRedDotPackageDataResp, error)
	// 品类用户已读
	CommodityUserReadRedDot(context.Context, *CommodityUserReadRedDotReq) (*CommodityUserReadRedDotResp, error)
	// 获取用户未读品类
	GetUserUnReadCommodityRedDot(context.Context, *GetUserUnReadCommodityRedDotReq) (*GetUserUnReadCommodityRedDotResp, error)
	BatchGetCommodityRedDot(context.Context, *BatchGetCommodityRedDotReq) (*BatchGetCommodityRedDotResp, error)
	DelCommodityData(context.Context, *DelCommodityDataReq) (*DelCommodityDataResp, error)
	// 根据时间范围返回订单统计数据
	GetOrderDataByTimeRange(context.Context, *GetOrderDataByTimeRangeRequest) (*GetOrderDataByTimeRangeResponse, error)
	// T豆消费数据对账
	GetConsumeTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
}

func RegisterVirtualImageMallServer(s *grpc.Server, srv VirtualImageMallServer) {
	s.RegisterService(&_VirtualImageMall_serviceDesc, srv)
}

func _VirtualImageMall_GetCommodityDataList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommodityDataListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).GetCommodityDataList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/GetCommodityDataList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).GetCommodityDataList(ctx, req.(*GetCommodityDataListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_GetCommodityRecommendList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommodityRecommendListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).GetCommodityRecommendList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/GetCommodityRecommendList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).GetCommodityRecommendList(ctx, req.(*GetCommodityRecommendListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_GetCommodityDataListByPrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommodityDataListByPriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).GetCommodityDataListByPrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/GetCommodityDataListByPrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).GetCommodityDataListByPrice(ctx, req.(*GetCommodityDataListByPriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_BatAddCommodity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatAddCommodityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).BatAddCommodity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/BatAddCommodity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).BatAddCommodity(ctx, req.(*BatAddCommodityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_UpdateCommodity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCommodityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).UpdateCommodity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/UpdateCommodity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).UpdateCommodity(ctx, req.(*UpdateCommodityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_BatAddCommodityRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatAddCommodityRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).BatAddCommodityRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/BatAddCommodityRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).BatAddCommodityRecommend(ctx, req.(*BatAddCommodityRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_UpdateCommodityRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCommodityRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).UpdateCommodityRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/UpdateCommodityRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).UpdateCommodityRecommend(ctx, req.(*UpdateCommodityRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_DelCommodityRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCommodityRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).DelCommodityRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/DelCommodityRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).DelCommodityRecommend(ctx, req.(*DelCommodityRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_BuyCommodityData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyCommodityDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).BuyCommodityData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/BuyCommodityData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).BuyCommodityData(ctx, req.(*BuyCommodityDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_UpdateCommodityDataOrdersStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCommodityDataOrdersStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).UpdateCommodityDataOrdersStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/UpdateCommodityDataOrdersStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).UpdateCommodityDataOrdersStatus(ctx, req.(*UpdateCommodityDataOrdersStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_GetCommodityDataOrdersPaying_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommodityDataOrdersPayingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).GetCommodityDataOrdersPaying(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/GetCommodityDataOrdersPaying",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).GetCommodityDataOrdersPaying(ctx, req.(*GetCommodityDataOrdersPayingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_BatchGetCommodityDataOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCommodityDataOrdersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).BatchGetCommodityDataOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/BatchGetCommodityDataOrders",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).BatchGetCommodityDataOrders(ctx, req.(*BatchGetCommodityDataOrdersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_AddShoppingCar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddShoppingCarRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).AddShoppingCar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/AddShoppingCar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).AddShoppingCar(ctx, req.(*AddShoppingCarRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_DelShoppingCar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelShoppingCarRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).DelShoppingCar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/DelShoppingCar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).DelShoppingCar(ctx, req.(*DelShoppingCarRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_BathGetShoppingCar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BathGetShoppingCarRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).BathGetShoppingCar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/BathGetShoppingCar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).BathGetShoppingCar(ctx, req.(*BathGetShoppingCarRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_UpdateCommodityResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCommodityResourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).UpdateCommodityResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/UpdateCommodityResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).UpdateCommodityResource(ctx, req.(*UpdateCommodityResourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_UpdateCommodityPackageRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCommodityPackageRedDotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).UpdateCommodityPackageRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/UpdateCommodityPackageRedDot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).UpdateCommodityPackageRedDot(ctx, req.(*UpdateCommodityPackageRedDotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_GetUnRefreshedRedDotPackageData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnRefreshedRedDotPackageDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).GetUnRefreshedRedDotPackageData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/GetUnRefreshedRedDotPackageData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).GetUnRefreshedRedDotPackageData(ctx, req.(*GetUnRefreshedRedDotPackageDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_CommodityUserReadRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommodityUserReadRedDotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).CommodityUserReadRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/CommodityUserReadRedDot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).CommodityUserReadRedDot(ctx, req.(*CommodityUserReadRedDotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_GetUserUnReadCommodityRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserUnReadCommodityRedDotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).GetUserUnReadCommodityRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/GetUserUnReadCommodityRedDot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).GetUserUnReadCommodityRedDot(ctx, req.(*GetUserUnReadCommodityRedDotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_BatchGetCommodityRedDot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCommodityRedDotReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).BatchGetCommodityRedDot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/BatchGetCommodityRedDot",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).BatchGetCommodityRedDot(ctx, req.(*BatchGetCommodityRedDotReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_DelCommodityData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCommodityDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).DelCommodityData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/DelCommodityData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).DelCommodityData(ctx, req.(*DelCommodityDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_GetOrderDataByTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderDataByTimeRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).GetOrderDataByTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/GetOrderDataByTimeRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).GetOrderDataByTimeRange(ctx, req.(*GetOrderDataByTimeRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_GetConsumeTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).GetConsumeTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/GetConsumeTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).GetConsumeTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VirtualImageMall_GetConsumeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VirtualImageMallServer).GetConsumeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/virtual_image_mall.VirtualImageMall/GetConsumeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VirtualImageMallServer).GetConsumeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _VirtualImageMall_serviceDesc = grpc.ServiceDesc{
	ServiceName: "virtual_image_mall.VirtualImageMall",
	HandlerType: (*VirtualImageMallServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCommodityDataList",
			Handler:    _VirtualImageMall_GetCommodityDataList_Handler,
		},
		{
			MethodName: "GetCommodityRecommendList",
			Handler:    _VirtualImageMall_GetCommodityRecommendList_Handler,
		},
		{
			MethodName: "GetCommodityDataListByPrice",
			Handler:    _VirtualImageMall_GetCommodityDataListByPrice_Handler,
		},
		{
			MethodName: "BatAddCommodity",
			Handler:    _VirtualImageMall_BatAddCommodity_Handler,
		},
		{
			MethodName: "UpdateCommodity",
			Handler:    _VirtualImageMall_UpdateCommodity_Handler,
		},
		{
			MethodName: "BatAddCommodityRecommend",
			Handler:    _VirtualImageMall_BatAddCommodityRecommend_Handler,
		},
		{
			MethodName: "UpdateCommodityRecommend",
			Handler:    _VirtualImageMall_UpdateCommodityRecommend_Handler,
		},
		{
			MethodName: "DelCommodityRecommend",
			Handler:    _VirtualImageMall_DelCommodityRecommend_Handler,
		},
		{
			MethodName: "BuyCommodityData",
			Handler:    _VirtualImageMall_BuyCommodityData_Handler,
		},
		{
			MethodName: "UpdateCommodityDataOrdersStatus",
			Handler:    _VirtualImageMall_UpdateCommodityDataOrdersStatus_Handler,
		},
		{
			MethodName: "GetCommodityDataOrdersPaying",
			Handler:    _VirtualImageMall_GetCommodityDataOrdersPaying_Handler,
		},
		{
			MethodName: "BatchGetCommodityDataOrders",
			Handler:    _VirtualImageMall_BatchGetCommodityDataOrders_Handler,
		},
		{
			MethodName: "AddShoppingCar",
			Handler:    _VirtualImageMall_AddShoppingCar_Handler,
		},
		{
			MethodName: "DelShoppingCar",
			Handler:    _VirtualImageMall_DelShoppingCar_Handler,
		},
		{
			MethodName: "BathGetShoppingCar",
			Handler:    _VirtualImageMall_BathGetShoppingCar_Handler,
		},
		{
			MethodName: "UpdateCommodityResource",
			Handler:    _VirtualImageMall_UpdateCommodityResource_Handler,
		},
		{
			MethodName: "UpdateCommodityPackageRedDot",
			Handler:    _VirtualImageMall_UpdateCommodityPackageRedDot_Handler,
		},
		{
			MethodName: "GetUnRefreshedRedDotPackageData",
			Handler:    _VirtualImageMall_GetUnRefreshedRedDotPackageData_Handler,
		},
		{
			MethodName: "CommodityUserReadRedDot",
			Handler:    _VirtualImageMall_CommodityUserReadRedDot_Handler,
		},
		{
			MethodName: "GetUserUnReadCommodityRedDot",
			Handler:    _VirtualImageMall_GetUserUnReadCommodityRedDot_Handler,
		},
		{
			MethodName: "BatchGetCommodityRedDot",
			Handler:    _VirtualImageMall_BatchGetCommodityRedDot_Handler,
		},
		{
			MethodName: "DelCommodityData",
			Handler:    _VirtualImageMall_DelCommodityData_Handler,
		},
		{
			MethodName: "GetOrderDataByTimeRange",
			Handler:    _VirtualImageMall_GetOrderDataByTimeRange_Handler,
		},
		{
			MethodName: "GetConsumeTotalCount",
			Handler:    _VirtualImageMall_GetConsumeTotalCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderIds",
			Handler:    _VirtualImageMall_GetConsumeOrderIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/virtual-image-mall/virtual-image-mall.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/virtual-image-mall/virtual-image-mall.proto", fileDescriptor_virtual_image_mall_7243ff8390503a42)
}

var fileDescriptor_virtual_image_mall_7243ff8390503a42 = []byte{
	// 3358 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5b, 0x5f, 0x6f, 0xdb, 0xc8,
	0xb5, 0x37, 0x25, 0x3b, 0x96, 0x8e, 0xfc, 0x87, 0x1e, 0x3b, 0xb1, 0x2c, 0xe7, 0x8f, 0x97, 0xbb,
	0xd9, 0x78, 0x7d, 0x13, 0x7b, 0xe1, 0x4d, 0xf6, 0xe6, 0xee, 0xe2, 0xee, 0x85, 0x2c, 0xcb, 0xb6,
	0x10, 0xc7, 0xf1, 0xa5, 0xe4, 0xec, 0x26, 0xc0, 0x82, 0x60, 0xc8, 0xb1, 0xcc, 0x86, 0x22, 0x19,
	0x92, 0xf2, 0xda, 0xdb, 0xa2, 0x7d, 0x68, 0xd1, 0x02, 0x2d, 0x50, 0x14, 0x68, 0x1f, 0xfa, 0xd0,
	0x3e, 0xf6, 0x13, 0xb4, 0x0f, 0x45, 0x3f, 0x43, 0xdf, 0xfa, 0xd4, 0xc7, 0xbe, 0xf5, 0x53, 0x14,
	0xc5, 0xcc, 0x90, 0x14, 0xff, 0x0c, 0x25, 0x39, 0xdb, 0x02, 0x05, 0xda, 0x27, 0x93, 0x67, 0x0e,
	0xcf, 0x39, 0x73, 0xe6, 0x9c, 0xdf, 0x9c, 0x39, 0x23, 0xc3, 0x27, 0xbe, 0xbf, 0xf5, 0xa6, 0x6f,
	0x68, 0xaf, 0x3d, 0xc3, 0x3c, 0xc7, 0xee, 0xd6, 0xb9, 0xe1, 0xfa, 0x7d, 0xd5, 0x7c, 0x60, 0xf4,
	0xd4, 0x2e, 0x7e, 0xd0, 0x53, 0x4d, 0x93, 0x43, 0xda, 0x74, 0x5c, 0xdb, 0xb7, 0x11, 0x0a, 0x46,
	0x14, 0x3a, 0xa2, 0x90, 0x91, 0xda, 0xce, 0x50, 0x79, 0x2e, 0xf6, 0xec, 0xbe, 0xab, 0xe1, 0x1c,
	0x32, 0x93, 0x5b, 0xdb, 0x4c, 0xc9, 0x70, 0xb1, 0x66, 0x5b, 0x9a, 0x61, 0xe2, 0x07, 0xe7, 0xdb,
	0x89, 0x17, 0xc6, 0x2f, 0xfd, 0xba, 0x08, 0x4b, 0x0d, 0xbb, 0xd7, 0xb3, 0x75, 0xc3, 0xbf, 0xdc,
	0x55, 0x7d, 0xf5, 0x58, 0xd5, 0x5e, 0xab, 0x5d, 0x8c, 0x6e, 0x01, 0x38, 0xec, 0x51, 0x31, 0xf4,
	0xaa, 0xb0, 0x26, 0xac, 0xcf, 0xca, 0xe5, 0x80, 0xd2, 0xd2, 0xd1, 0x12, 0x4c, 0x39, 0xae, 0xa1,
	0xe1, 0x6a, 0x81, 0x8e, 0xb0, 0x17, 0x74, 0x17, 0xe6, 0x74, 0xc3, 0xd3, 0xec, 0xbe, 0xe5, 0x2b,
	0x6c, 0xb8, 0x48, 0x87, 0x67, 0x43, 0xea, 0x31, 0x65, 0x7b, 0x17, 0x22, 0x82, 0xe2, 0xaa, 0x3e,
	0xae, 0x4e, 0x52, 0xae, 0x99, 0x90, 0x28, 0xab, 0x3e, 0x65, 0xc2, 0xa7, 0xa7, 0x58, 0xf3, 0x8d,
	0x73, 0xac, 0xe8, 0xea, 0x65, 0x75, 0x8a, 0x31, 0x45, 0xc4, 0x5d, 0xf5, 0x92, 0x58, 0xe9, 0x9d,
	0x61, 0xf3, 0x54, 0xf1, 0x8d, 0x1e, 0xae, 0x5e, 0x63, 0x56, 0x52, 0x4a, 0xc7, 0xe8, 0x61, 0x74,
	0x07, 0x2a, 0xf8, 0xc2, 0x31, 0x5c, 0xcc, 0xc6, 0xa7, 0xe9, 0x38, 0x30, 0x12, 0x65, 0x78, 0x07,
	0x66, 0xb4, 0x70, 0xf6, 0x64, 0x9e, 0x25, 0xca, 0x51, 0x89, 0x68, 0x2d, 0x9d, 0xb0, 0x30, 0x15,
	0x9e, 0xaf, 0xfa, 0x7d, 0xaf, 0x5a, 0x66, 0x2c, 0x94, 0xd6, 0xa6, 0x24, 0xb4, 0x0e, 0x62, 0x4c,
	0x8d, 0xe2, 0x9d, 0xd9, 0x5f, 0x55, 0x61, 0x4d, 0x58, 0x2f, 0xc9, 0x73, 0x03, 0x5d, 0xed, 0x33,
	0xfb, 0x2b, 0xb4, 0x05, 0x4b, 0x86, 0xa7, 0xb8, 0xf8, 0xd4, 0xc5, 0xde, 0x19, 0xd6, 0x15, 0x17,
	0xeb, 0x8a, 0x6e, 0xfb, 0xd5, 0x0a, 0xe5, 0x5e, 0x30, 0x3c, 0x39, 0x1c, 0x92, 0xb1, 0xbe, 0x6b,
	0xfb, 0x92, 0x0d, 0x0b, 0x8d, 0xbe, 0xe7, 0xdb, 0x3d, 0xe3, 0x6b, 0x7c, 0x68, 0x77, 0x6d, 0xff,
	0xd2, 0xc1, 0xa8, 0x06, 0x25, 0x33, 0x78, 0xa6, 0x2b, 0x53, 0x96, 0xa3, 0xf7, 0x94, 0x47, 0x0a,
	0x23, 0x3c, 0x52, 0x4c, 0x7b, 0x44, 0xfa, 0x02, 0xae, 0xd5, 0x35, 0xff, 0xc4, 0x35, 0x91, 0x08,
	0xc5, 0xbe, 0x6b, 0x06, 0x0a, 0xc8, 0x23, 0x5a, 0x85, 0x72, 0x4f, 0x75, 0x5f, 0x63, 0x9f, 0xb8,
	0x8a, 0x89, 0x2e, 0x31, 0x42, 0x4b, 0x27, 0x92, 0x35, 0xd3, 0xc0, 0x96, 0xaf, 0x50, 0xbb, 0x02,
	0xc9, 0x8c, 0xd4, 0xb9, 0x74, 0xb0, 0x74, 0x01, 0x33, 0x75, 0xb2, 0x70, 0xc4, 0xad, 0xd6, 0xa9,
	0x8d, 0x10, 0x4c, 0xea, 0xd8, 0xd3, 0x02, 0x05, 0xf4, 0x99, 0x58, 0x4f, 0xfe, 0x2a, 0x9a, 0x6d,
	0xda, 0x2e, 0x55, 0x51, 0x96, 0xcb, 0x84, 0xd2, 0x20, 0x04, 0xf4, 0x08, 0x4a, 0x7d, 0xd7, 0x54,
	0x4c, 0xc3, 0xf3, 0xab, 0xc5, 0xb5, 0xe2, 0x7a, 0x65, 0xbb, 0xb6, 0x99, 0x4d, 0xa4, 0x4d, 0x36,
	0x01, 0x79, 0xba, 0xef, 0x9a, 0x87, 0x86, 0xe7, 0x4b, 0x7f, 0x9e, 0x86, 0xd9, 0x44, 0x90, 0x67,
	0xd6, 0x5d, 0xc8, 0xae, 0xfb, 0x2d, 0x00, 0x13, 0x9f, 0x63, 0x53, 0x31, 0x34, 0xdb, 0x0a, 0x4d,
	0xa1, 0x94, 0x96, 0x66, 0x5b, 0x24, 0x01, 0xe8, 0x4b, 0x30, 0x51, 0xf6, 0x82, 0x0e, 0x61, 0x4e,
	0x0b, 0x97, 0x8b, 0x7d, 0x48, 0x42, 0xbb, 0xb2, 0x7d, 0x97, 0x67, 0x66, 0x66, 0x61, 0xe5, 0xd9,
	0xe8, 0x63, 0xaa, 0xe3, 0x2e, 0xcc, 0xc5, 0xac, 0x24, 0xd2, 0xa6, 0xa8, 0x19, 0xb3, 0x03, 0x3b,
	0x09, 0xdb, 0x16, 0x2c, 0x0e, 0xd8, 0x54, 0xcb, 0xe8, 0xa9, 0xbe, 0x61, 0x5b, 0x34, 0x1b, 0xca,
	0x32, 0x8a, 0x86, 0xea, 0xe1, 0x48, 0x52, 0xae, 0xa5, 0x06, 0x99, 0x11, 0x97, 0x7b, 0xa4, 0xf6,
	0x30, 0x59, 0xee, 0xae, 0x6a, 0x58, 0x8a, 0xa3, 0xfa, 0x67, 0x41, 0x66, 0x94, 0x08, 0xe1, 0x58,
	0xf5, 0xcf, 0x48, 0xcc, 0x87, 0xd0, 0xa3, 0x18, 0x3a, 0x5b, 0x92, 0xf2, 0x5a, 0x71, 0x7d, 0x56,
	0x9e, 0x0b, 0xe9, 0x2d, 0x9d, 0x78, 0x9f, 0x44, 0xab, 0xa6, 0xfa, 0xb8, 0x6b, 0xbb, 0x97, 0x34,
	0x2b, 0x66, 0xe5, 0xe8, 0x9d, 0x26, 0x57, 0xff, 0x95, 0x12, 0x8d, 0x57, 0x82, 0xe4, 0xea, 0xbf,
	0x6a, 0x84, 0x2c, 0x09, 0x63, 0x69, 0x68, 0xcd, 0x30, 0x4c, 0x89, 0xa8, 0x24, 0xba, 0x48, 0x34,
	0xb9, 0xaa, 0xf5, 0xba, 0x3a, 0x4b, 0x07, 0xe9, 0x33, 0x0d, 0x49, 0x17, 0xab, 0x7e, 0x10, 0xec,
	0x73, 0x41, 0x48, 0x52, 0x52, 0x98, 0x0d, 0x7d, 0x47, 0x8f, 0x18, 0xe6, 0x19, 0x03, 0x23, 0x85,
	0xf8, 0x10, 0xcd, 0xd2, 0xc3, 0x17, 0x55, 0x91, 0xd9, 0x17, 0xd2, 0xda, 0xf8, 0x02, 0x3d, 0x07,
	0x44, 0xa1, 0x4e, 0x09, 0xe1, 0x92, 0xba, 0x62, 0x81, 0x46, 0xe7, 0x3a, 0x77, 0xd9, 0x39, 0x70,
	0x2b, 0x8b, 0x54, 0x46, 0xf0, 0x46, 0xdd, 0xf6, 0x29, 0x94, 0x54, 0xcd, 0x57, 0x0c, 0xeb, 0xd4,
	0xae, 0x22, 0x1a, 0x44, 0x6b, 0x39, 0xb1, 0x1e, 0xa5, 0x94, 0x3c, 0xad, 0x6a, 0x3e, 0xcd, 0xad,
	0x7b, 0x30, 0xef, 0x39, 0x86, 0x85, 0x63, 0xe1, 0xb0, 0x48, 0x97, 0x78, 0x8e, 0x92, 0x07, 0xa1,
	0x90, 0x84, 0x8b, 0xa5, 0x11, 0x70, 0x71, 0x3d, 0x03, 0xa0, 0xef, 0xc3, 0x7c, 0x80, 0x61, 0xca,
	0x39, 0x76, 0x3d, 0xa2, 0xe8, 0x06, 0x5b, 0x1e, 0x97, 0x02, 0xd8, 0x73, 0x46, 0x44, 0x1f, 0xc2,
	0x92, 0xe3, 0xda, 0x3d, 0x9b, 0x28, 0x55, 0x4d, 0xe5, 0xdc, 0xd0, 0xb1, 0x4d, 0x12, 0x6f, 0x99,
	0x32, 0xa3, 0xd8, 0xd8, 0x73, 0x32, 0xd4, 0xd2, 0xc9, 0xba, 0x7b, 0x7d, 0xc7, 0xb1, 0x5d, 0x5f,
	0x71, 0x8d, 0xee, 0x99, 0xef, 0x55, 0xab, 0x4c, 0x70, 0x40, 0x95, 0x29, 0x51, 0xfa, 0x5f, 0x28,
	0xb5, 0x6d, 0xd7, 0xa7, 0x59, 0xbd, 0x04, 0x53, 0xa7, 0x06, 0x36, 0xf5, 0x00, 0x52, 0xd8, 0x0b,
	0x09, 0x63, 0x8f, 0x48, 0xa1, 0xb1, 0x13, 0xa0, 0x16, 0x21, 0x50, 0x50, 0xfa, 0xeb, 0x14, 0xac,
	0xee, 0x63, 0x3f, 0xb1, 0x26, 0xc4, 0xfd, 0x32, 0x7e, 0xd3, 0xc7, 0xa9, 0xe0, 0x15, 0x46, 0x04,
	0x6f, 0x21, 0x1b, 0xbc, 0x1b, 0xb0, 0x10, 0xc7, 0x99, 0x01, 0x72, 0xcd, 0xca, 0xf3, 0x31, 0xb0,
	0xa1, 0x0b, 0xce, 0xcb, 0xa8, 0x49, 0x6e, 0x46, 0x65, 0x53, 0x62, 0x8a, 0x97, 0x12, 0xd9, 0x34,
	0xbf, 0xc6, 0x4b, 0xf3, 0x74, 0x8c, 0x4f, 0x67, 0x63, 0x3c, 0x02, 0xbb, 0x52, 0x1c, 0xec, 0x12,
	0xf8, 0x50, 0x4e, 0xe1, 0x43, 0x7a, 0xdb, 0x84, 0xec, 0xb6, 0x89, 0x60, 0xd2, 0x51, 0xbb, 0x38,
	0x48, 0x7a, 0xfa, 0x4c, 0x64, 0x92, 0xbf, 0x8a, 0x67, 0x7c, 0x1d, 0x26, 0x7a, 0x89, 0x10, 0xda,
	0xc6, 0xd7, 0x18, 0x49, 0x30, 0x6b, 0x78, 0xc4, 0x46, 0xc5, 0xc3, 0x26, 0xd6, 0x7c, 0x9a, 0xec,
	0x25, 0xb9, 0x62, 0x78, 0x6d, 0x7c, 0xd1, 0xa6, 0x24, 0xe2, 0xf1, 0xf8, 0x6c, 0x98, 0x1b, 0xe7,
	0x98, 0xc7, 0x63, 0x53, 0xa2, 0x7e, 0x5c, 0x85, 0x32, 0xc1, 0x09, 0x85, 0x44, 0x43, 0x90, 0xfc,
	0x25, 0x42, 0x20, 0x01, 0x45, 0x37, 0x3b, 0x32, 0x39, 0x5a, 0xc6, 0x88, 0xc1, 0x66, 0x67, 0x58,
	0xac, 0x82, 0xa1, 0x3b, 0xe1, 0x45, 0x30, 0xb8, 0x10, 0xee, 0x84, 0x17, 0x6c, 0xf0, 0x3e, 0x20,
	0xf2, 0x65, 0xaa, 0x12, 0x42, 0x94, 0x4b, 0xec, 0x19, 0xd6, 0x6e, 0xa2, 0x18, 0x22, 0xdc, 0xea,
	0x45, 0x9a, 0x7b, 0x31, 0xe0, 0x56, 0x2f, 0x92, 0xdc, 0xff, 0x13, 0x04, 0xb3, 0xae, 0xfa, 0x2a,
	0x4d, 0xd7, 0xca, 0xf6, 0x4d, 0x1e, 0x2c, 0x84, 0x39, 0xc1, 0x42, 0x9d, 0x3c, 0x49, 0x3f, 0x15,
	0xe0, 0x26, 0x3f, 0xd4, 0x3d, 0xc7, 0xb6, 0x3c, 0x8c, 0xfe, 0x3f, 0xbe, 0x8f, 0x10, 0x05, 0xcc,
	0x79, 0x02, 0x85, 0xb2, 0x77, 0x46, 0x42, 0x99, 0x3c, 0x08, 0xf5, 0x50, 0x34, 0xf1, 0x93, 0x6f,
	0xfb, 0xaa, 0xa9, 0x68, 0x96, 0x1f, 0xe6, 0x1e, 0x25, 0x34, 0x2c, 0x5f, 0x3a, 0x05, 0x89, 0x67,
	0xcf, 0xce, 0x25, 0x9d, 0x6a, 0x98, 0x81, 0x51, 0xa5, 0x29, 0xc4, 0x2b, 0x4d, 0xee, 0x32, 0x17,
	0xb8, 0xcb, 0x2c, 0x5d, 0xc0, 0xbb, 0x43, 0xf5, 0xfc, 0xd3, 0xa6, 0x2f, 0x75, 0x00, 0xed, 0xa8,
	0x7e, 0x5d, 0xd7, 0x23, 0x4e, 0x19, 0xbf, 0x41, 0x9f, 0x41, 0xf9, 0x2d, 0xc4, 0x97, 0xf4, 0x50,
	0xea, 0x75, 0x58, 0xcc, 0x48, 0xf5, 0x1c, 0xe9, 0x09, 0xa0, 0x13, 0xba, 0x73, 0x25, 0x94, 0x3d,
	0x82, 0x49, 0x1a, 0x2b, 0x02, 0x8d, 0x95, 0x31, 0xf4, 0x50, 0x76, 0xa2, 0x23, 0x23, 0xcc, 0x73,
	0xa4, 0x53, 0xa8, 0x65, 0xc9, 0xd4, 0xd9, 0x44, 0xd7, 0x1c, 0x14, 0xa2, 0x5a, 0xaa, 0x60, 0xe8,
	0x24, 0xc1, 0x29, 0xec, 0xb0, 0xe2, 0x89, 0x3e, 0x13, 0x1a, 0xad, 0x64, 0x8a, 0x8c, 0x46, 0x9e,
	0x49, 0xa5, 0x49, 0x80, 0x87, 0x9d, 0x02, 0xc8, 0xa3, 0x74, 0x0b, 0x56, 0x73, 0xf5, 0x78, 0x8e,
	0xf4, 0x2d, 0xb8, 0x11, 0x1b, 0x20, 0x7e, 0xc7, 0x96, 0x4e, 0x37, 0xbe, 0xb7, 0x9b, 0x2e, 0x5a,
	0x81, 0x92, 0x8b, 0x35, 0x85, 0x56, 0x10, 0x2c, 0x4c, 0xa7, 0x5d, 0xac, 0xc9, 0xaa, 0xf5, 0x5a,
	0xfa, 0xc3, 0x24, 0xac, 0xc5, 0xc3, 0x27, 0xd2, 0xf7, 0x9f, 0x6d, 0xe2, 0xdf, 0x64, 0x9b, 0x48,
	0x60, 0xee, 0xfc, 0x95, 0x30, 0xf7, 0x47, 0x02, 0xbc, 0x33, 0x24, 0x78, 0x02, 0xe4, 0x69, 0xb2,
	0xe8, 0x8b, 0xe1, 0xc1, 0xc6, 0xd0, 0xc0, 0x4d, 0x84, 0x3c, 0x8d, 0xd4, 0x71, 0xc0, 0x76, 0x35,
	0x03, 0x1a, 0x81, 0x14, 0x92, 0xba, 0xfb, 0x50, 0x26, 0x95, 0xe6, 0xdb, 0xda, 0x50, 0x22, 0x1f,
	0x53, 0x70, 0xba, 0x0d, 0x37, 0xf3, 0xf5, 0x78, 0x8e, 0xf4, 0x25, 0x27, 0xb3, 0x63, 0x76, 0x7c,
	0x06, 0x93, 0xb4, 0xe2, 0x65, 0xf9, 0x7b, 0x15, 0x13, 0xe8, 0x77, 0x44, 0x7d, 0xbe, 0x78, 0xcf,
	0x91, 0x0c, 0xa8, 0xee, 0x62, 0x93, 0xaf, 0x7b, 0x8c, 0x43, 0x21, 0x37, 0x51, 0x0b, 0xdc, 0x44,
	0x95, 0x56, 0x61, 0x25, 0x47, 0x95, 0xe7, 0x48, 0xbf, 0x9f, 0x82, 0xc5, 0x04, 0x10, 0x3d, 0x73,
	0x75, 0xec, 0x7a, 0x24, 0x76, 0xe9, 0xde, 0x60, 0x93, 0xd7, 0xd0, 0x88, 0xb2, 0x5c, 0xd1, 0x43,
	0x16, 0xd6, 0x91, 0x48, 0xd8, 0x59, 0xe0, 0x1e, 0x5e, 0x63, 0xdd, 0x9b, 0x22, 0xa7, 0x7b, 0x43,
	0x6b, 0x8a, 0x00, 0x72, 0xd9, 0x0b, 0x29, 0xf6, 0x59, 0xfc, 0xb0, 0xfd, 0x96, 0x81, 0x05, 0x50,
	0x52, 0x54, 0xf5, 0xa8, 0xe7, 0xdd, 0x60, 0x98, 0x35, 0x5b, 0x4a, 0xea, 0x79, 0x97, 0x0d, 0xa6,
	0x0e, 0x5b, 0xd3, 0x99, 0xc3, 0x16, 0xb5, 0xe9, 0x32, 0x4c, 0xf4, 0x52, 0x68, 0xd3, 0x65, 0x90,
	0xe6, 0x22, 0x14, 0xfb, 0x86, 0x1e, 0x00, 0x04, 0x79, 0x4c, 0x9f, 0xce, 0x20, 0x73, 0x3a, 0xe3,
	0x41, 0x61, 0x65, 0x34, 0x14, 0x52, 0x8c, 0x9b, 0xe1, 0x61, 0x5c, 0xa6, 0xe7, 0x34, 0xcb, 0xe9,
	0x39, 0x65, 0x61, 0x75, 0x8e, 0x07, 0xab, 0xf1, 0x2d, 0x61, 0x7e, 0xc4, 0x96, 0x20, 0x66, 0xb7,
	0x84, 0x35, 0x98, 0x79, 0x65, 0x74, 0x15, 0xdf, 0x55, 0x75, 0xac, 0x58, 0x36, 0x2d, 0x32, 0xcb,
	0x32, 0xbc, 0x32, 0xba, 0x1d, 0x42, 0x3a, 0xb2, 0x39, 0xdd, 0x01, 0xc4, 0xeb, 0x0e, 0x24, 0xfb,
	0x18, 0x8b, 0xe9, 0x3e, 0x46, 0xde, 0xc1, 0xec, 0x7a, 0xde, 0xc1, 0x4c, 0xfa, 0x93, 0x00, 0x0b,
	0xed, 0x33, 0xdb, 0x71, 0x0c, 0xab, 0xdb, 0xf2, 0x71, 0x6f, 0x47, 0xf5, 0x0c, 0x8d, 0xac, 0x85,
	0x17, 0x10, 0x15, 0xc3, 0xc7, 0xbd, 0x41, 0x02, 0xcd, 0x79, 0x31, 0xe6, 0x7f, 0xd5, 0xf0, 0x95,
	0x5e, 0xc2, 0xf2, 0x4e, 0xff, 0x32, 0x59, 0x1b, 0x04, 0x9b, 0xfb, 0xff, 0xc1, 0x35, 0x9a, 0x8e,
	0x5e, 0x00, 0x8c, 0xf7, 0x46, 0x56, 0x15, 0x2c, 0x99, 0xe5, 0xe0, 0x33, 0xa9, 0x06, 0xd5, 0xac,
	0x6c, 0x86, 0xfd, 0xd2, 0xdf, 0x04, 0x78, 0x3f, 0x85, 0x58, 0x03, 0x09, 0x2c, 0x35, 0x42, 0x3b,
	0xc6, 0xc1, 0x86, 0x64, 0x92, 0x15, 0xd2, 0x49, 0x96, 0x8e, 0xaa, 0x62, 0x26, 0xaa, 0x56, 0xa1,
	0x6c, 0x78, 0x0a, 0x59, 0x0c, 0x83, 0xf9, 0xb7, 0x24, 0x97, 0x0c, 0xaf, 0x41, 0xdf, 0xc3, 0x1c,
	0x9d, 0x4a, 0xe4, 0x68, 0x3c, 0xeb, 0xaf, 0xf1, 0x5a, 0x2c, 0xf1, 0x55, 0x99, 0x4e, 0xaf, 0x8a,
	0xf4, 0x01, 0xdc, 0x1b, 0x39, 0xff, 0xc0, 0x57, 0x36, 0x48, 0x3b, 0xaa, 0xaf, 0x9d, 0xa5, 0xab,
	0xf9, 0xc0, 0xdd, 0xf9, 0x6e, 0x2a, 0xa6, 0xdd, 0x74, 0x17, 0xe6, 0x0d, 0x4f, 0x49, 0xb8, 0xa2,
	0x40, 0xe7, 0x3a, 0x63, 0x78, 0x3b, 0x91, 0x33, 0xa4, 0x53, 0x78, 0x77, 0xa8, 0xc2, 0x60, 0xff,
	0xfe, 0xc6, 0x01, 0x72, 0x37, 0x7b, 0x42, 0x61, 0x1c, 0xc7, 0xea, 0xa5, 0x61, 0x75, 0x83, 0x99,
	0x49, 0x5d, 0x78, 0x6f, 0x38, 0xdb, 0x3f, 0xca, 0x1e, 0x0c, 0xd7, 0xeb, 0xba, 0x1e, 0x26, 0x79,
	0x43, 0x75, 0x43, 0xdf, 0x1e, 0x26, 0x30, 0xc7, 0xc7, 0x3d, 0xea, 0xb6, 0x9c, 0xfe, 0x66, 0x06,
	0x24, 0xe2, 0xd0, 0xe4, 0xe3, 0x9e, 0x54, 0x85, 0x1b, 0x69, 0x35, 0xc1, 0x4a, 0xd7, 0xe1, 0xfa,
	0x2e, 0x36, 0x39, 0x06, 0xf0, 0x61, 0xa6, 0x98, 0x85, 0x19, 0x22, 0x3c, 0x2d, 0x22, 0x10, 0xbe,
	0x0a, 0x2b, 0x3b, 0xaa, 0x4f, 0x16, 0x35, 0xab, 0x40, 0x7a, 0x03, 0x35, 0xde, 0x60, 0xe0, 0xd9,
	0x36, 0xa0, 0xa4, 0xfa, 0x58, 0xbd, 0x34, 0xa6, 0x0f, 0xc4, 0xb8, 0x9d, 0xb4, 0x50, 0xf8, 0x5d,
	0x01, 0xee, 0xa4, 0x52, 0x20, 0x6c, 0x0b, 0xd2, 0x1e, 0x1a, 0xa9, 0x4d, 0x46, 0x5c, 0xc7, 0xe4,
	0xdd, 0x2b, 0x14, 0x72, 0xee, 0x15, 0x12, 0xbb, 0x53, 0x71, 0xc4, 0xee, 0x34, 0x99, 0xdd, 0x9d,
	0xd2, 0x18, 0x3e, 0xc5, 0xc5, 0xf0, 0x6f, 0x74, 0x35, 0x73, 0x07, 0x2a, 0x14, 0x88, 0x2c, 0xdf,
	0xb0, 0xfa, 0x98, 0xd6, 0x0b, 0x25, 0x19, 0x08, 0x14, 0x31, 0x8a, 0x24, 0xc1, 0xda, 0x70, 0xaf,
	0x79, 0x8e, 0xf4, 0x1e, 0x6d, 0x31, 0x9c, 0x58, 0xa9, 0xe9, 0x07, 0x8c, 0x01, 0xc8, 0x4b, 0x2e,
	0x4d, 0xbf, 0xe1, 0x5c, 0x9e, 0x83, 0x9e, 0xc0, 0x4c, 0xa2, 0xc7, 0x2b, 0x5c, 0xb1, 0xc7, 0x5b,
	0x71, 0x06, 0xed, 0x5d, 0xe9, 0x97, 0x02, 0xd4, 0x22, 0xae, 0x13, 0x0f, 0xbb, 0x32, 0x56, 0xf5,
	0xc1, 0x7a, 0x7f, 0xc3, 0x03, 0x65, 0x00, 0xd4, 0xc5, 0x01, 0x50, 0x7f, 0x40, 0x0e, 0x3c, 0xac,
	0x51, 0xab, 0x9a, 0xd8, 0x65, 0x85, 0x0b, 0x5b, 0xd9, 0x39, 0xd6, 0xaa, 0xad, 0x9b, 0xd8, 0xa5,
	0x3d, 0xd1, 0x5b, 0xb0, 0x9a, 0x6b, 0x99, 0xe7, 0x48, 0xdf, 0x17, 0xe0, 0x0e, 0x71, 0x97, 0x87,
	0x5d, 0xe2, 0x32, 0x35, 0x5e, 0xe9, 0x87, 0xe6, 0x07, 0xfa, 0x85, 0x11, 0xfa, 0x0b, 0x3c, 0xfd,
	0xa4, 0x0a, 0x0b, 0xe7, 0x16, 0x3f, 0x09, 0xcf, 0x84, 0x44, 0xea, 0xbf, 0x3f, 0x0a, 0xf4, 0x58,
	0x3e, 0xc4, 0x0a, 0xcf, 0xe1, 0x2b, 0x15, 0xb8, 0x4a, 0x6f, 0x43, 0xe5, 0x4c, 0xf5, 0x52, 0x89,
	0x53, 0x3e, 0x53, 0xbd, 0x20, 0x61, 0xbe, 0xe4, 0x19, 0x55, 0xd9, 0x7e, 0x9c, 0x5a, 0xfd, 0xe8,
	0x3a, 0xf6, 0x39, 0x23, 0xb7, 0x7a, 0x34, 0x20, 0x19, 0x31, 0x5c, 0x1e, 0x7a, 0x5e, 0x49, 0x4e,
	0xe7, 0x29, 0x85, 0x9d, 0xe4, 0x4e, 0x33, 0x70, 0x27, 0x4b, 0xef, 0x2e, 0xf6, 0x15, 0x1d, 0xfb,
	0xaa, 0x61, 0x12, 0x23, 0xc3, 0x2e, 0x07, 0x4d, 0xef, 0x7d, 0xec, 0xef, 0x06, 0x23, 0xf4, 0xdc,
	0xf9, 0xc3, 0x02, 0x3d, 0xee, 0xf1, 0xe5, 0x79, 0x0e, 0xa9, 0x1d, 0xbb, 0xa6, 0xfd, 0x8a, 0x14,
	0x7c, 0x41, 0xd7, 0x9e, 0x79, 0x65, 0x96, 0x51, 0xc3, 0xae, 0xfd, 0x25, 0x2c, 0x25, 0x8e, 0x3b,
	0x21, 0x73, 0x81, 0xce, 0x7d, 0x9f, 0x17, 0xf9, 0x43, 0xb4, 0x0e, 0xb2, 0xa2, 0xa5, 0x07, 0x1a,
	0x9a, 0x96, 0xef, 0x5e, 0xc6, 0xee, 0xa8, 0xa2, 0x81, 0x5a, 0x13, 0x96, 0x73, 0xd8, 0x49, 0x70,
	0xbd, 0xc6, 0x61, 0x5a, 0x90, 0x47, 0x52, 0x10, 0x9e, 0xab, 0x66, 0x3f, 0xba, 0x8d, 0xa6, 0x2f,
	0x9f, 0x14, 0x1e, 0x0b, 0xd2, 0x63, 0x58, 0x8c, 0x1f, 0xc2, 0x82, 0x8c, 0x1f, 0xe3, 0xa8, 0x27,
	0xdd, 0x80, 0xa5, 0xec, 0x97, 0x9e, 0x23, 0xbd, 0x84, 0xdb, 0xfb, 0xd8, 0xa7, 0x1b, 0x26, 0xa1,
	0xed, 0x5c, 0x12, 0xb4, 0x92, 0x55, 0xab, 0x1b, 0x75, 0x2c, 0x09, 0xf2, 0xf9, 0xaa, 0xeb, 0x33,
	0x64, 0x23, 0xa2, 0x8b, 0x72, 0x99, 0x52, 0x28, 0xb0, 0xad, 0x40, 0x09, 0x5b, 0xfa, 0xe0, 0x7e,
	0xb6, 0x28, 0x4f, 0x63, 0x4b, 0xa7, 0x97, 0xaf, 0xdf, 0xa5, 0x99, 0xc5, 0x97, 0x1d, 0xec, 0x40,
	0x2b, 0x50, 0xea, 0x87, 0x67, 0x1d, 0xb6, 0xf1, 0x4d, 0xf7, 0x0d, 0x76, 0xc8, 0x49, 0x95, 0x5a,
	0x4c, 0x76, 0xbc, 0x00, 0x8e, 0x18, 0x58, 0xf5, 0x5c, 0x8c, 0x31, 0x34, 0x08, 0x65, 0xe3, 0x37,
	0x05, 0x58, 0x88, 0x66, 0xbc, 0x1f, 0xf6, 0x68, 0xee, 0xc0, 0x6a, 0xe3, 0xd9, 0xd3, 0xa7, 0xcf,
	0x76, 0x5b, 0x9d, 0x17, 0xca, 0x7e, 0xbd, 0x75, 0xa4, 0x1c, 0xd7, 0x3b, 0x07, 0xca, 0xc9, 0xd1,
	0x93, 0xa3, 0x67, 0x9f, 0x1f, 0x89, 0x13, 0x68, 0x0d, 0x6e, 0xf2, 0x18, 0x8e, 0x4f, 0xe4, 0xc6,
	0x41, 0xbd, 0xdd, 0x14, 0x05, 0x74, 0x13, 0xaa, 0x3c, 0x8e, 0xfd, 0xd6, 0x5e, 0x47, 0x2c, 0xe4,
	0x7d, 0x5f, 0x6f, 0x74, 0x5a, 0xcf, 0x5b, 0x9d, 0x17, 0x62, 0x11, 0xdd, 0x86, 0x1a, 0x8f, 0x43,
	0x6e, 0x7e, 0x5e, 0x97, 0x77, 0xc5, 0xc9, 0x3c, 0x09, 0xcd, 0x2f, 0x1a, 0x07, 0xf5, 0xa3, 0xfd,
	0xa6, 0x38, 0x85, 0x6e, 0xc1, 0x0a, 0x8f, 0xe3, 0x59, 0xe7, 0xa0, 0x29, 0x8b, 0xd7, 0xd0, 0x7d,
	0x58, 0xe7, 0x0d, 0xb7, 0x8e, 0xf6, 0x5a, 0x47, 0xad, 0x4e, 0x53, 0x61, 0x72, 0x94, 0x06, 0x51,
	0x37, 0xbd, 0x61, 0x43, 0xa5, 0x1d, 0xeb, 0x50, 0x55, 0x61, 0xa9, 0x7d, 0xd0, 0x3c, 0xdc, 0x53,
	0xda, 0x9d, 0x7a, 0xe7, 0xa4, 0x1d, 0xf3, 0xcc, 0x32, 0x2c, 0x26, 0x46, 0xf6, 0x4e, 0x3a, 0x27,
	0x32, 0x71, 0xc8, 0x12, 0x88, 0x89, 0x81, 0xa3, 0x67, 0x9f, 0x8b, 0x85, 0x0c, 0x7b, 0xf3, 0x8b,
	0xe3, 0x96, 0xdc, 0x14, 0x8b, 0x1b, 0x5a, 0xec, 0x02, 0x3b, 0x80, 0xab, 0x98, 0x43, 0x3a, 0x2f,
	0x8e, 0x9b, 0xca, 0xc9, 0x51, 0xfb, 0xb8, 0xd9, 0x68, 0xed, 0xb5, 0x9a, 0xbb, 0xe2, 0x04, 0x5a,
	0x81, 0xeb, 0xa9, 0xf1, 0x76, 0xeb, 0x68, 0xff, 0x90, 0xa8, 0x5e, 0x86, 0xc5, 0xf4, 0xd0, 0x49,
	0xab, 0x23, 0x16, 0x36, 0x9a, 0x00, 0xec, 0x52, 0x8d, 0x6a, 0x58, 0x86, 0x45, 0xb9, 0xb5, 0x7f,
	0xd0, 0x69, 0x87, 0xe2, 0xc3, 0x39, 0xdd, 0x86, 0x5a, 0x72, 0xe0, 0xb0, 0xf5, 0xb4, 0xd5, 0x69,
	0xee, 0x32, 0xe7, 0x08, 0x1b, 0x07, 0xec, 0x46, 0x8e, 0x0a, 0xb9, 0x0e, 0x0b, 0xed, 0x67, 0x72,
	0x27, 0x2d, 0x62, 0x01, 0x66, 0x07, 0xe4, 0x7a, 0xbb, 0x21, 0x0a, 0x08, 0xc1, 0xdc, 0x80, 0xb4,
	0xdb, 0x6c, 0x37, 0xc4, 0xc2, 0xc6, 0x5f, 0x04, 0x40, 0xb1, 0xcd, 0x7d, 0x70, 0x88, 0x89, 0x2d,
	0xf6, 0x71, 0xfd, 0x45, 0xd6, 0xed, 0x89, 0x88, 0x8d, 0x71, 0x1c, 0xd7, 0x5f, 0xb4, 0x8e, 0xf6,
	0x45, 0x21, 0x97, 0x61, 0x4f, 0x6e, 0x36, 0x5f, 0x36, 0xc5, 0x02, 0x7a, 0x0f, 0xd6, 0x86, 0x30,
	0x28, 0x7b, 0xf5, 0xd6, 0xa1, 0x58, 0xcc, 0xb5, 0xa4, 0x7d, 0xd0, 0x3a, 0x3e, 0x6e, 0x92, 0xc0,
	0xcc, 0x53, 0x24, 0x37, 0xf7, 0x4e, 0x8e, 0x76, 0xc5, 0xa9, 0xed, 0xdf, 0x56, 0x41, 0x8c, 0xef,
	0x15, 0x4f, 0x55, 0xd3, 0x44, 0xdf, 0x86, 0x25, 0xde, 0x8d, 0x05, 0xda, 0xe2, 0x21, 0xee, 0x90,
	0xeb, 0xcb, 0xda, 0x87, 0xe3, 0x7f, 0x10, 0x14, 0xc7, 0x13, 0xe8, 0xc7, 0x02, 0xac, 0xe4, 0xf6,
	0x2c, 0xd1, 0xc3, 0x51, 0x12, 0x79, 0xfd, 0xf1, 0xda, 0xa3, 0x2b, 0x7e, 0x15, 0x19, 0xf3, 0x33,
	0x81, 0x7f, 0x3f, 0x1b, 0x5c, 0xde, 0xa0, 0x8f, 0xc7, 0x9d, 0x60, 0xf2, 0x56, 0xa9, 0xf6, 0xdf,
	0x57, 0xfe, 0x2e, 0x32, 0x49, 0x87, 0xf9, 0x54, 0x87, 0x13, 0xbd, 0x9f, 0xb3, 0x13, 0xa6, 0x6e,
	0x7e, 0x6a, 0xf7, 0xc6, 0xe2, 0xf3, 0x1c, 0xa6, 0x25, 0x55, 0xdd, 0xf2, 0xb5, 0x64, 0xaf, 0x7c,
	0xf8, 0x5a, 0x78, 0xb7, 0x39, 0x13, 0xe8, 0x7b, 0x50, 0xcd, 0xeb, 0xd6, 0xf2, 0x83, 0x6d, 0x48,
	0x0f, 0x99, 0x1f, 0x6c, 0x43, 0x9b, 0xc1, 0xd4, 0x80, 0xbc, 0x7e, 0x2d, 0xdf, 0x80, 0x21, 0xcd,
	0x63, 0xbe, 0x01, 0x43, 0xdb, 0xc1, 0x13, 0xe8, 0x9c, 0x9e, 0x34, 0x39, 0xda, 0xef, 0xf3, 0x84,
	0xe5, 0xf5, 0x8e, 0x6b, 0x0f, 0xae, 0xc0, 0x4d, 0xf5, 0xda, 0x20, 0xa6, 0x7b, 0x42, 0xe8, 0xbf,
	0xb8, 0x0e, 0xe4, 0x77, 0xa5, 0x6a, 0xf7, 0xc7, 0x63, 0x8e, 0xc2, 0xf6, 0x57, 0x42, 0xe6, 0x94,
	0x99, 0x6e, 0xb4, 0xa0, 0x4f, 0xc6, 0x70, 0x60, 0x4e, 0x77, 0xaa, 0xf6, 0xe9, 0x5b, 0x7d, 0x1b,
	0x99, 0xf7, 0x73, 0xce, 0xed, 0x74, 0xbc, 0xb9, 0x81, 0xc6, 0xca, 0x58, 0x4e, 0xd7, 0xa4, 0xf6,
	0xf8, 0xea, 0x1f, 0x26, 0xe0, 0x67, 0x48, 0x07, 0x88, 0x0f, 0x3f, 0xa3, 0x7b, 0x54, 0x7c, 0xf8,
	0x19, 0xa3, 0xd5, 0x24, 0x4d, 0x20, 0x03, 0xe6, 0x92, 0x4d, 0x13, 0xf4, 0x01, 0xf7, 0x77, 0x41,
	0xbc, 0xfe, 0x4d, 0x6d, 0x63, 0x1c, 0xd6, 0xb8, 0xaa, 0x64, 0x0b, 0x85, 0xaf, 0x8a, 0xdb, 0xa9,
	0xe1, 0xab, 0xca, 0xe9, 0xc8, 0x4c, 0xa0, 0x3e, 0xbd, 0x29, 0x4f, 0xb5, 0x5d, 0xd0, 0x83, 0x1c,
	0x37, 0xf1, 0x7b, 0x37, 0xb5, 0xcd, 0x71, 0xd9, 0x23, 0xb5, 0xdf, 0x81, 0xe5, 0x9c, 0x7b, 0x66,
	0xb4, 0x39, 0x1e, 0x8a, 0x86, 0x97, 0xdf, 0xb5, 0xad, 0x2b, 0xf1, 0x53, 0x0c, 0xf8, 0x89, 0x90,
	0xb9, 0xad, 0x4a, 0xb4, 0x30, 0xd0, 0x47, 0x63, 0xc8, 0x4c, 0xb7, 0x8a, 0x6a, 0x0f, 0xaf, 0xfe,
	0x11, 0xb5, 0xe6, 0x17, 0xc1, 0xb9, 0x7e, 0x48, 0x1b, 0x24, 0x77, 0xbb, 0x1d, 0xd1, 0x61, 0xc9,
	0xdd, 0x6e, 0x47, 0xf5, 0x5c, 0xd8, 0x12, 0xe5, 0x74, 0x23, 0xf8, 0x4b, 0x94, 0xdf, 0x54, 0xe1,
	0x2f, 0xd1, 0xb0, 0x56, 0x07, 0x5b, 0xa2, 0x61, 0x6d, 0x06, 0xfe, 0x12, 0x8d, 0x68, 0x8f, 0xd4,
	0x1e, 0x5e, 0xfd, 0xa3, 0xd0, 0x17, 0x39, 0xe7, 0x6b, 0xb4, 0x79, 0xa5, 0xc3, 0x78, 0x8e, 0x2f,
	0x86, 0x1c, 0xde, 0xa5, 0x09, 0xd4, 0x05, 0x31, 0x7d, 0x22, 0x46, 0xf7, 0x46, 0xed, 0x7b, 0x61,
	0x04, 0xac, 0x8f, 0xc7, 0x48, 0x15, 0xfd, 0x40, 0x80, 0xe5, 0x9c, 0x73, 0x30, 0xda, 0xce, 0x71,
	0xdd, 0x90, 0x03, 0x79, 0xed, 0xa3, 0x2b, 0x7d, 0x13, 0x81, 0xc3, 0x93, 0xa0, 0x0a, 0xb7, 0xbc,
	0x7e, 0x0f, 0x77, 0xa2, 0x53, 0x32, 0x5a, 0xd9, 0x94, 0xc3, 0x1f, 0xd2, 0x3f, 0xdf, 0xde, 0x8c,
	0x6b, 0xab, 0xdd, 0x48, 0x0c, 0x51, 0xf6, 0x60, 0x4e, 0x87, 0x80, 0x06, 0xc2, 0x82, 0x6b, 0x08,
	0x6f, 0x98, 0xa8, 0xe4, 0x50, 0xf8, 0x05, 0x93, 0xb6, 0xf3, 0xf1, 0xcb, 0x87, 0x5d, 0xdb, 0x54,
	0xad, 0xee, 0xe6, 0xa3, 0x6d, 0xdf, 0xdf, 0xd4, 0xec, 0xde, 0x16, 0xfd, 0x3d, 0xbf, 0x66, 0x9b,
	0x5b, 0x1e, 0x76, 0xcf, 0x0d, 0x0d, 0x7b, 0x9c, 0x7f, 0x3e, 0x78, 0x75, 0x8d, 0x72, 0x7d, 0xf4,
	0xf7, 0x00, 0x00, 0x00, 0xff, 0xff, 0x2b, 0x38, 0x06, 0x27, 0xbb, 0x30, 0x00, 0x00,
}
