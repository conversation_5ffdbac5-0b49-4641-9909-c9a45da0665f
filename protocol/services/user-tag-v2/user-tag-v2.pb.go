// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user-tag-v2/user-tag-v2.proto

package user_tag_v2 // import "golang.52tt.com/protocol/services/user-tag-v2"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetGameTypeListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameTypeListReq) Reset()         { *m = GetGameTypeListReq{} }
func (m *GetGameTypeListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTypeListReq) ProtoMessage()    {}
func (*GetGameTypeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{0}
}
func (m *GetGameTypeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTypeListReq.Unmarshal(m, b)
}
func (m *GetGameTypeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTypeListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameTypeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTypeListReq.Merge(dst, src)
}
func (m *GetGameTypeListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameTypeListReq.Size(m)
}
func (m *GetGameTypeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTypeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTypeListReq proto.InternalMessageInfo

type GameType struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameType) Reset()         { *m = GameType{} }
func (m *GameType) String() string { return proto.CompactTextString(m) }
func (*GameType) ProtoMessage()    {}
func (*GameType) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{1}
}
func (m *GameType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameType.Unmarshal(m, b)
}
func (m *GameType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameType.Marshal(b, m, deterministic)
}
func (dst *GameType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameType.Merge(dst, src)
}
func (m *GameType) XXX_Size() int {
	return xxx_messageInfo_GameType.Size(m)
}
func (m *GameType) XXX_DiscardUnknown() {
	xxx_messageInfo_GameType.DiscardUnknown(m)
}

var xxx_messageInfo_GameType proto.InternalMessageInfo

func (m *GameType) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

func (m *GameType) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GetGameTypeListResp struct {
	GameTypeList         []*GameType `protobuf:"bytes,1,rep,name=game_type_list,json=gameTypeList,proto3" json:"game_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGameTypeListResp) Reset()         { *m = GetGameTypeListResp{} }
func (m *GetGameTypeListResp) String() string { return proto.CompactTextString(m) }
func (*GetGameTypeListResp) ProtoMessage()    {}
func (*GetGameTypeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{2}
}
func (m *GetGameTypeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTypeListResp.Unmarshal(m, b)
}
func (m *GetGameTypeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTypeListResp.Marshal(b, m, deterministic)
}
func (dst *GetGameTypeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTypeListResp.Merge(dst, src)
}
func (m *GetGameTypeListResp) XXX_Size() int {
	return xxx_messageInfo_GetGameTypeListResp.Size(m)
}
func (m *GetGameTypeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTypeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTypeListResp proto.InternalMessageInfo

func (m *GetGameTypeListResp) GetGameTypeList() []*GameType {
	if m != nil {
		return m.GameTypeList
	}
	return nil
}

type AddNotGameCardTagConfReq struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	MaxSelectCnt         uint32   `protobuf:"varint,3,opt,name=max_select_cnt,json=maxSelectCnt,proto3" json:"max_select_cnt,omitempty"`
	TagList              []string `protobuf:"bytes,4,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNotGameCardTagConfReq) Reset()         { *m = AddNotGameCardTagConfReq{} }
func (m *AddNotGameCardTagConfReq) String() string { return proto.CompactTextString(m) }
func (*AddNotGameCardTagConfReq) ProtoMessage()    {}
func (*AddNotGameCardTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{3}
}
func (m *AddNotGameCardTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNotGameCardTagConfReq.Unmarshal(m, b)
}
func (m *AddNotGameCardTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNotGameCardTagConfReq.Marshal(b, m, deterministic)
}
func (dst *AddNotGameCardTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNotGameCardTagConfReq.Merge(dst, src)
}
func (m *AddNotGameCardTagConfReq) XXX_Size() int {
	return xxx_messageInfo_AddNotGameCardTagConfReq.Size(m)
}
func (m *AddNotGameCardTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNotGameCardTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddNotGameCardTagConfReq proto.InternalMessageInfo

func (m *AddNotGameCardTagConfReq) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

func (m *AddNotGameCardTagConfReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AddNotGameCardTagConfReq) GetMaxSelectCnt() uint32 {
	if m != nil {
		return m.MaxSelectCnt
	}
	return 0
}

func (m *AddNotGameCardTagConfReq) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type AddNotGameCardTagConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddNotGameCardTagConfResp) Reset()         { *m = AddNotGameCardTagConfResp{} }
func (m *AddNotGameCardTagConfResp) String() string { return proto.CompactTextString(m) }
func (*AddNotGameCardTagConfResp) ProtoMessage()    {}
func (*AddNotGameCardTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{4}
}
func (m *AddNotGameCardTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddNotGameCardTagConfResp.Unmarshal(m, b)
}
func (m *AddNotGameCardTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddNotGameCardTagConfResp.Marshal(b, m, deterministic)
}
func (dst *AddNotGameCardTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddNotGameCardTagConfResp.Merge(dst, src)
}
func (m *AddNotGameCardTagConfResp) XXX_Size() int {
	return xxx_messageInfo_AddNotGameCardTagConfResp.Size(m)
}
func (m *AddNotGameCardTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddNotGameCardTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddNotGameCardTagConfResp proto.InternalMessageInfo

type DeleteNotGameCardTagConfReq struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteNotGameCardTagConfReq) Reset()         { *m = DeleteNotGameCardTagConfReq{} }
func (m *DeleteNotGameCardTagConfReq) String() string { return proto.CompactTextString(m) }
func (*DeleteNotGameCardTagConfReq) ProtoMessage()    {}
func (*DeleteNotGameCardTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{5}
}
func (m *DeleteNotGameCardTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteNotGameCardTagConfReq.Unmarshal(m, b)
}
func (m *DeleteNotGameCardTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteNotGameCardTagConfReq.Marshal(b, m, deterministic)
}
func (dst *DeleteNotGameCardTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteNotGameCardTagConfReq.Merge(dst, src)
}
func (m *DeleteNotGameCardTagConfReq) XXX_Size() int {
	return xxx_messageInfo_DeleteNotGameCardTagConfReq.Size(m)
}
func (m *DeleteNotGameCardTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteNotGameCardTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteNotGameCardTagConfReq proto.InternalMessageInfo

func (m *DeleteNotGameCardTagConfReq) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

type DeleteNotGameCardTagConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteNotGameCardTagConfResp) Reset()         { *m = DeleteNotGameCardTagConfResp{} }
func (m *DeleteNotGameCardTagConfResp) String() string { return proto.CompactTextString(m) }
func (*DeleteNotGameCardTagConfResp) ProtoMessage()    {}
func (*DeleteNotGameCardTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{6}
}
func (m *DeleteNotGameCardTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteNotGameCardTagConfResp.Unmarshal(m, b)
}
func (m *DeleteNotGameCardTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteNotGameCardTagConfResp.Marshal(b, m, deterministic)
}
func (dst *DeleteNotGameCardTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteNotGameCardTagConfResp.Merge(dst, src)
}
func (m *DeleteNotGameCardTagConfResp) XXX_Size() int {
	return xxx_messageInfo_DeleteNotGameCardTagConfResp.Size(m)
}
func (m *DeleteNotGameCardTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteNotGameCardTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteNotGameCardTagConfResp proto.InternalMessageInfo

type GetNotGameCardTagConfReq struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNotGameCardTagConfReq) Reset()         { *m = GetNotGameCardTagConfReq{} }
func (m *GetNotGameCardTagConfReq) String() string { return proto.CompactTextString(m) }
func (*GetNotGameCardTagConfReq) ProtoMessage()    {}
func (*GetNotGameCardTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{7}
}
func (m *GetNotGameCardTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNotGameCardTagConfReq.Unmarshal(m, b)
}
func (m *GetNotGameCardTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNotGameCardTagConfReq.Marshal(b, m, deterministic)
}
func (dst *GetNotGameCardTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNotGameCardTagConfReq.Merge(dst, src)
}
func (m *GetNotGameCardTagConfReq) XXX_Size() int {
	return xxx_messageInfo_GetNotGameCardTagConfReq.Size(m)
}
func (m *GetNotGameCardTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNotGameCardTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNotGameCardTagConfReq proto.InternalMessageInfo

func (m *GetNotGameCardTagConfReq) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

type GetNotGameCardTagConfResp struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	MaxSelectCnt         uint32   `protobuf:"varint,3,opt,name=max_select_cnt,json=maxSelectCnt,proto3" json:"max_select_cnt,omitempty"`
	TagList              []string `protobuf:"bytes,4,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNotGameCardTagConfResp) Reset()         { *m = GetNotGameCardTagConfResp{} }
func (m *GetNotGameCardTagConfResp) String() string { return proto.CompactTextString(m) }
func (*GetNotGameCardTagConfResp) ProtoMessage()    {}
func (*GetNotGameCardTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{8}
}
func (m *GetNotGameCardTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNotGameCardTagConfResp.Unmarshal(m, b)
}
func (m *GetNotGameCardTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNotGameCardTagConfResp.Marshal(b, m, deterministic)
}
func (dst *GetNotGameCardTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNotGameCardTagConfResp.Merge(dst, src)
}
func (m *GetNotGameCardTagConfResp) XXX_Size() int {
	return xxx_messageInfo_GetNotGameCardTagConfResp.Size(m)
}
func (m *GetNotGameCardTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNotGameCardTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNotGameCardTagConfResp proto.InternalMessageInfo

func (m *GetNotGameCardTagConfResp) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

func (m *GetNotGameCardTagConfResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetNotGameCardTagConfResp) GetMaxSelectCnt() uint32 {
	if m != nil {
		return m.MaxSelectCnt
	}
	return 0
}

func (m *GetNotGameCardTagConfResp) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type GetAllNotGameCardTagConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllNotGameCardTagConfReq) Reset()         { *m = GetAllNotGameCardTagConfReq{} }
func (m *GetAllNotGameCardTagConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllNotGameCardTagConfReq) ProtoMessage()    {}
func (*GetAllNotGameCardTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{9}
}
func (m *GetAllNotGameCardTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllNotGameCardTagConfReq.Unmarshal(m, b)
}
func (m *GetAllNotGameCardTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllNotGameCardTagConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllNotGameCardTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllNotGameCardTagConfReq.Merge(dst, src)
}
func (m *GetAllNotGameCardTagConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllNotGameCardTagConfReq.Size(m)
}
func (m *GetAllNotGameCardTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllNotGameCardTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllNotGameCardTagConfReq proto.InternalMessageInfo

type NotGameCardTagConf struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	TagList              []string `protobuf:"bytes,2,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotGameCardTagConf) Reset()         { *m = NotGameCardTagConf{} }
func (m *NotGameCardTagConf) String() string { return proto.CompactTextString(m) }
func (*NotGameCardTagConf) ProtoMessage()    {}
func (*NotGameCardTagConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{10}
}
func (m *NotGameCardTagConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotGameCardTagConf.Unmarshal(m, b)
}
func (m *NotGameCardTagConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotGameCardTagConf.Marshal(b, m, deterministic)
}
func (dst *NotGameCardTagConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotGameCardTagConf.Merge(dst, src)
}
func (m *NotGameCardTagConf) XXX_Size() int {
	return xxx_messageInfo_NotGameCardTagConf.Size(m)
}
func (m *NotGameCardTagConf) XXX_DiscardUnknown() {
	xxx_messageInfo_NotGameCardTagConf.DiscardUnknown(m)
}

var xxx_messageInfo_NotGameCardTagConf proto.InternalMessageInfo

func (m *NotGameCardTagConf) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

func (m *NotGameCardTagConf) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type GetAllNotGameCardTagConfResp struct {
	AllConfList          []*NotGameCardTagConf `protobuf:"bytes,1,rep,name=all_conf_list,json=allConfList,proto3" json:"all_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetAllNotGameCardTagConfResp) Reset()         { *m = GetAllNotGameCardTagConfResp{} }
func (m *GetAllNotGameCardTagConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllNotGameCardTagConfResp) ProtoMessage()    {}
func (*GetAllNotGameCardTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{11}
}
func (m *GetAllNotGameCardTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllNotGameCardTagConfResp.Unmarshal(m, b)
}
func (m *GetAllNotGameCardTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllNotGameCardTagConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllNotGameCardTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllNotGameCardTagConfResp.Merge(dst, src)
}
func (m *GetAllNotGameCardTagConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllNotGameCardTagConfResp.Size(m)
}
func (m *GetAllNotGameCardTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllNotGameCardTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllNotGameCardTagConfResp proto.InternalMessageInfo

func (m *GetAllNotGameCardTagConfResp) GetAllConfList() []*NotGameCardTagConf {
	if m != nil {
		return m.AllConfList
	}
	return nil
}

type GetClassifyListReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetClassifyListReq) Reset()         { *m = GetClassifyListReq{} }
func (m *GetClassifyListReq) String() string { return proto.CompactTextString(m) }
func (*GetClassifyListReq) ProtoMessage()    {}
func (*GetClassifyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{12}
}
func (m *GetClassifyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetClassifyListReq.Unmarshal(m, b)
}
func (m *GetClassifyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetClassifyListReq.Marshal(b, m, deterministic)
}
func (dst *GetClassifyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClassifyListReq.Merge(dst, src)
}
func (m *GetClassifyListReq) XXX_Size() int {
	return xxx_messageInfo_GetClassifyListReq.Size(m)
}
func (m *GetClassifyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClassifyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetClassifyListReq proto.InternalMessageInfo

func (m *GetClassifyListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type ClassifyInfo struct {
	ClassifyId           uint32   `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	ClassifyName         string   `protobuf:"bytes,2,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClassifyInfo) Reset()         { *m = ClassifyInfo{} }
func (m *ClassifyInfo) String() string { return proto.CompactTextString(m) }
func (*ClassifyInfo) ProtoMessage()    {}
func (*ClassifyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{13}
}
func (m *ClassifyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClassifyInfo.Unmarshal(m, b)
}
func (m *ClassifyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClassifyInfo.Marshal(b, m, deterministic)
}
func (dst *ClassifyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClassifyInfo.Merge(dst, src)
}
func (m *ClassifyInfo) XXX_Size() int {
	return xxx_messageInfo_ClassifyInfo.Size(m)
}
func (m *ClassifyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ClassifyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ClassifyInfo proto.InternalMessageInfo

func (m *ClassifyInfo) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *ClassifyInfo) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

type GetClassifyListResp struct {
	Type                 uint32          `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ClassifyList         []*ClassifyInfo `protobuf:"bytes,2,rep,name=classify_list,json=classifyList,proto3" json:"classify_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetClassifyListResp) Reset()         { *m = GetClassifyListResp{} }
func (m *GetClassifyListResp) String() string { return proto.CompactTextString(m) }
func (*GetClassifyListResp) ProtoMessage()    {}
func (*GetClassifyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{14}
}
func (m *GetClassifyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetClassifyListResp.Unmarshal(m, b)
}
func (m *GetClassifyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetClassifyListResp.Marshal(b, m, deterministic)
}
func (dst *GetClassifyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClassifyListResp.Merge(dst, src)
}
func (m *GetClassifyListResp) XXX_Size() int {
	return xxx_messageInfo_GetClassifyListResp.Size(m)
}
func (m *GetClassifyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClassifyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetClassifyListResp proto.InternalMessageInfo

func (m *GetClassifyListResp) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetClassifyListResp) GetClassifyList() []*ClassifyInfo {
	if m != nil {
		return m.ClassifyList
	}
	return nil
}

type AddTagConfReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ClassifyId           uint32   `protobuf:"varint,2,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	TagList              []string `protobuf:"bytes,3,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTagConfReq) Reset()         { *m = AddTagConfReq{} }
func (m *AddTagConfReq) String() string { return proto.CompactTextString(m) }
func (*AddTagConfReq) ProtoMessage()    {}
func (*AddTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{15}
}
func (m *AddTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTagConfReq.Unmarshal(m, b)
}
func (m *AddTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTagConfReq.Marshal(b, m, deterministic)
}
func (dst *AddTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTagConfReq.Merge(dst, src)
}
func (m *AddTagConfReq) XXX_Size() int {
	return xxx_messageInfo_AddTagConfReq.Size(m)
}
func (m *AddTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTagConfReq proto.InternalMessageInfo

func (m *AddTagConfReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AddTagConfReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *AddTagConfReq) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type AddTagConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTagConfResp) Reset()         { *m = AddTagConfResp{} }
func (m *AddTagConfResp) String() string { return proto.CompactTextString(m) }
func (*AddTagConfResp) ProtoMessage()    {}
func (*AddTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{16}
}
func (m *AddTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTagConfResp.Unmarshal(m, b)
}
func (m *AddTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTagConfResp.Marshal(b, m, deterministic)
}
func (dst *AddTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTagConfResp.Merge(dst, src)
}
func (m *AddTagConfResp) XXX_Size() int {
	return xxx_messageInfo_AddTagConfResp.Size(m)
}
func (m *AddTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTagConfResp proto.InternalMessageInfo

type GetTagConfReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ClassifyId           uint32   `protobuf:"varint,2,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTagConfReq) Reset()         { *m = GetTagConfReq{} }
func (m *GetTagConfReq) String() string { return proto.CompactTextString(m) }
func (*GetTagConfReq) ProtoMessage()    {}
func (*GetTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{17}
}
func (m *GetTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTagConfReq.Unmarshal(m, b)
}
func (m *GetTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTagConfReq.Marshal(b, m, deterministic)
}
func (dst *GetTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagConfReq.Merge(dst, src)
}
func (m *GetTagConfReq) XXX_Size() int {
	return xxx_messageInfo_GetTagConfReq.Size(m)
}
func (m *GetTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagConfReq proto.InternalMessageInfo

func (m *GetTagConfReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetTagConfReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

type GetTagConfResp struct {
	TagList              []string `protobuf:"bytes,1,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTagConfResp) Reset()         { *m = GetTagConfResp{} }
func (m *GetTagConfResp) String() string { return proto.CompactTextString(m) }
func (*GetTagConfResp) ProtoMessage()    {}
func (*GetTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{18}
}
func (m *GetTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTagConfResp.Unmarshal(m, b)
}
func (m *GetTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTagConfResp.Marshal(b, m, deterministic)
}
func (dst *GetTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagConfResp.Merge(dst, src)
}
func (m *GetTagConfResp) XXX_Size() int {
	return xxx_messageInfo_GetTagConfResp.Size(m)
}
func (m *GetTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagConfResp proto.InternalMessageInfo

func (m *GetTagConfResp) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type SetUserTagReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserTagReq) Reset()         { *m = SetUserTagReq{} }
func (m *SetUserTagReq) String() string { return proto.CompactTextString(m) }
func (*SetUserTagReq) ProtoMessage()    {}
func (*SetUserTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{19}
}
func (m *SetUserTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserTagReq.Unmarshal(m, b)
}
func (m *SetUserTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserTagReq.Marshal(b, m, deterministic)
}
func (dst *SetUserTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserTagReq.Merge(dst, src)
}
func (m *SetUserTagReq) XXX_Size() int {
	return xxx_messageInfo_SetUserTagReq.Size(m)
}
func (m *SetUserTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserTagReq proto.InternalMessageInfo

type SetUserTagResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserTagResp) Reset()         { *m = SetUserTagResp{} }
func (m *SetUserTagResp) String() string { return proto.CompactTextString(m) }
func (*SetUserTagResp) ProtoMessage()    {}
func (*SetUserTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_v2_5dbc57627d8b5253, []int{20}
}
func (m *SetUserTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserTagResp.Unmarshal(m, b)
}
func (m *SetUserTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserTagResp.Marshal(b, m, deterministic)
}
func (dst *SetUserTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserTagResp.Merge(dst, src)
}
func (m *SetUserTagResp) XXX_Size() int {
	return xxx_messageInfo_SetUserTagResp.Size(m)
}
func (m *SetUserTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserTagResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetGameTypeListReq)(nil), "user_tag_v2.GetGameTypeListReq")
	proto.RegisterType((*GameType)(nil), "user_tag_v2.GameType")
	proto.RegisterType((*GetGameTypeListResp)(nil), "user_tag_v2.GetGameTypeListResp")
	proto.RegisterType((*AddNotGameCardTagConfReq)(nil), "user_tag_v2.AddNotGameCardTagConfReq")
	proto.RegisterType((*AddNotGameCardTagConfResp)(nil), "user_tag_v2.AddNotGameCardTagConfResp")
	proto.RegisterType((*DeleteNotGameCardTagConfReq)(nil), "user_tag_v2.DeleteNotGameCardTagConfReq")
	proto.RegisterType((*DeleteNotGameCardTagConfResp)(nil), "user_tag_v2.DeleteNotGameCardTagConfResp")
	proto.RegisterType((*GetNotGameCardTagConfReq)(nil), "user_tag_v2.GetNotGameCardTagConfReq")
	proto.RegisterType((*GetNotGameCardTagConfResp)(nil), "user_tag_v2.GetNotGameCardTagConfResp")
	proto.RegisterType((*GetAllNotGameCardTagConfReq)(nil), "user_tag_v2.GetAllNotGameCardTagConfReq")
	proto.RegisterType((*NotGameCardTagConf)(nil), "user_tag_v2.NotGameCardTagConf")
	proto.RegisterType((*GetAllNotGameCardTagConfResp)(nil), "user_tag_v2.GetAllNotGameCardTagConfResp")
	proto.RegisterType((*GetClassifyListReq)(nil), "user_tag_v2.GetClassifyListReq")
	proto.RegisterType((*ClassifyInfo)(nil), "user_tag_v2.ClassifyInfo")
	proto.RegisterType((*GetClassifyListResp)(nil), "user_tag_v2.GetClassifyListResp")
	proto.RegisterType((*AddTagConfReq)(nil), "user_tag_v2.AddTagConfReq")
	proto.RegisterType((*AddTagConfResp)(nil), "user_tag_v2.AddTagConfResp")
	proto.RegisterType((*GetTagConfReq)(nil), "user_tag_v2.GetTagConfReq")
	proto.RegisterType((*GetTagConfResp)(nil), "user_tag_v2.GetTagConfResp")
	proto.RegisterType((*SetUserTagReq)(nil), "user_tag_v2.SetUserTagReq")
	proto.RegisterType((*SetUserTagResp)(nil), "user_tag_v2.SetUserTagResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserTagConfMgrClient is the client API for UserTagConfMgr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserTagConfMgrClient interface {
	// 和游戏关联但又不属于游戏卡的游戏标签(不展示在端内游戏卡上)........实在拗口........
	GetGameTypeList(ctx context.Context, in *GetGameTypeListReq, opts ...grpc.CallOption) (*GetGameTypeListResp, error)
	// Add/modify接口,没有则创建，有则覆盖
	AddNotGameCardTagConf(ctx context.Context, in *AddNotGameCardTagConfReq, opts ...grpc.CallOption) (*AddNotGameCardTagConfResp, error)
	DeleteNotGameCardTagConf(ctx context.Context, in *DeleteNotGameCardTagConfReq, opts ...grpc.CallOption) (*DeleteNotGameCardTagConfResp, error)
	GetNotGameCardTagConf(ctx context.Context, in *GetNotGameCardTagConfReq, opts ...grpc.CallOption) (*GetNotGameCardTagConfResp, error)
	GetAllNotGameCardTagConf(ctx context.Context, in *GetAllNotGameCardTagConfReq, opts ...grpc.CallOption) (*GetAllNotGameCardTagConfResp, error)
	GetClassifyList(ctx context.Context, in *GetClassifyListReq, opts ...grpc.CallOption) (*GetClassifyListResp, error)
	// Add/modify接口,没有则创建，有则覆盖
	AddTagConf(ctx context.Context, in *AddTagConfReq, opts ...grpc.CallOption) (*AddTagConfResp, error)
	GetTagConf(ctx context.Context, in *GetTagConfReq, opts ...grpc.CallOption) (*GetTagConfResp, error)
}

type userTagConfMgrClient struct {
	cc *grpc.ClientConn
}

func NewUserTagConfMgrClient(cc *grpc.ClientConn) UserTagConfMgrClient {
	return &userTagConfMgrClient{cc}
}

func (c *userTagConfMgrClient) GetGameTypeList(ctx context.Context, in *GetGameTypeListReq, opts ...grpc.CallOption) (*GetGameTypeListResp, error) {
	out := new(GetGameTypeListResp)
	err := c.cc.Invoke(ctx, "/user_tag_v2.UserTagConfMgr/GetGameTypeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) AddNotGameCardTagConf(ctx context.Context, in *AddNotGameCardTagConfReq, opts ...grpc.CallOption) (*AddNotGameCardTagConfResp, error) {
	out := new(AddNotGameCardTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_v2.UserTagConfMgr/AddNotGameCardTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) DeleteNotGameCardTagConf(ctx context.Context, in *DeleteNotGameCardTagConfReq, opts ...grpc.CallOption) (*DeleteNotGameCardTagConfResp, error) {
	out := new(DeleteNotGameCardTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_v2.UserTagConfMgr/DeleteNotGameCardTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetNotGameCardTagConf(ctx context.Context, in *GetNotGameCardTagConfReq, opts ...grpc.CallOption) (*GetNotGameCardTagConfResp, error) {
	out := new(GetNotGameCardTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_v2.UserTagConfMgr/GetNotGameCardTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetAllNotGameCardTagConf(ctx context.Context, in *GetAllNotGameCardTagConfReq, opts ...grpc.CallOption) (*GetAllNotGameCardTagConfResp, error) {
	out := new(GetAllNotGameCardTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_v2.UserTagConfMgr/GetAllNotGameCardTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetClassifyList(ctx context.Context, in *GetClassifyListReq, opts ...grpc.CallOption) (*GetClassifyListResp, error) {
	out := new(GetClassifyListResp)
	err := c.cc.Invoke(ctx, "/user_tag_v2.UserTagConfMgr/GetClassifyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) AddTagConf(ctx context.Context, in *AddTagConfReq, opts ...grpc.CallOption) (*AddTagConfResp, error) {
	out := new(AddTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_v2.UserTagConfMgr/AddTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetTagConf(ctx context.Context, in *GetTagConfReq, opts ...grpc.CallOption) (*GetTagConfResp, error) {
	out := new(GetTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_v2.UserTagConfMgr/GetTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserTagConfMgrServer is the server API for UserTagConfMgr service.
type UserTagConfMgrServer interface {
	// 和游戏关联但又不属于游戏卡的游戏标签(不展示在端内游戏卡上)........实在拗口........
	GetGameTypeList(context.Context, *GetGameTypeListReq) (*GetGameTypeListResp, error)
	// Add/modify接口,没有则创建，有则覆盖
	AddNotGameCardTagConf(context.Context, *AddNotGameCardTagConfReq) (*AddNotGameCardTagConfResp, error)
	DeleteNotGameCardTagConf(context.Context, *DeleteNotGameCardTagConfReq) (*DeleteNotGameCardTagConfResp, error)
	GetNotGameCardTagConf(context.Context, *GetNotGameCardTagConfReq) (*GetNotGameCardTagConfResp, error)
	GetAllNotGameCardTagConf(context.Context, *GetAllNotGameCardTagConfReq) (*GetAllNotGameCardTagConfResp, error)
	GetClassifyList(context.Context, *GetClassifyListReq) (*GetClassifyListResp, error)
	// Add/modify接口,没有则创建，有则覆盖
	AddTagConf(context.Context, *AddTagConfReq) (*AddTagConfResp, error)
	GetTagConf(context.Context, *GetTagConfReq) (*GetTagConfResp, error)
}

func RegisterUserTagConfMgrServer(s *grpc.Server, srv UserTagConfMgrServer) {
	s.RegisterService(&_UserTagConfMgr_serviceDesc, srv)
}

func _UserTagConfMgr_GetGameTypeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameTypeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetGameTypeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_v2.UserTagConfMgr/GetGameTypeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetGameTypeList(ctx, req.(*GetGameTypeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_AddNotGameCardTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddNotGameCardTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).AddNotGameCardTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_v2.UserTagConfMgr/AddNotGameCardTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).AddNotGameCardTagConf(ctx, req.(*AddNotGameCardTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_DeleteNotGameCardTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNotGameCardTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).DeleteNotGameCardTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_v2.UserTagConfMgr/DeleteNotGameCardTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).DeleteNotGameCardTagConf(ctx, req.(*DeleteNotGameCardTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetNotGameCardTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotGameCardTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetNotGameCardTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_v2.UserTagConfMgr/GetNotGameCardTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetNotGameCardTagConf(ctx, req.(*GetNotGameCardTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetAllNotGameCardTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllNotGameCardTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetAllNotGameCardTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_v2.UserTagConfMgr/GetAllNotGameCardTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetAllNotGameCardTagConf(ctx, req.(*GetAllNotGameCardTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetClassifyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClassifyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetClassifyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_v2.UserTagConfMgr/GetClassifyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetClassifyList(ctx, req.(*GetClassifyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_AddTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).AddTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_v2.UserTagConfMgr/AddTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).AddTagConf(ctx, req.(*AddTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_v2.UserTagConfMgr/GetTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetTagConf(ctx, req.(*GetTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserTagConfMgr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user_tag_v2.UserTagConfMgr",
	HandlerType: (*UserTagConfMgrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGameTypeList",
			Handler:    _UserTagConfMgr_GetGameTypeList_Handler,
		},
		{
			MethodName: "AddNotGameCardTagConf",
			Handler:    _UserTagConfMgr_AddNotGameCardTagConf_Handler,
		},
		{
			MethodName: "DeleteNotGameCardTagConf",
			Handler:    _UserTagConfMgr_DeleteNotGameCardTagConf_Handler,
		},
		{
			MethodName: "GetNotGameCardTagConf",
			Handler:    _UserTagConfMgr_GetNotGameCardTagConf_Handler,
		},
		{
			MethodName: "GetAllNotGameCardTagConf",
			Handler:    _UserTagConfMgr_GetAllNotGameCardTagConf_Handler,
		},
		{
			MethodName: "GetClassifyList",
			Handler:    _UserTagConfMgr_GetClassifyList_Handler,
		},
		{
			MethodName: "AddTagConf",
			Handler:    _UserTagConfMgr_AddTagConf_Handler,
		},
		{
			MethodName: "GetTagConf",
			Handler:    _UserTagConfMgr_GetTagConf_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user-tag-v2/user-tag-v2.proto",
}

// UserTagClient is the client API for UserTag service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserTagClient interface {
	SetUserTag(ctx context.Context, in *SetUserTagReq, opts ...grpc.CallOption) (*SetUserTagResp, error)
}

type userTagClient struct {
	cc *grpc.ClientConn
}

func NewUserTagClient(cc *grpc.ClientConn) UserTagClient {
	return &userTagClient{cc}
}

func (c *userTagClient) SetUserTag(ctx context.Context, in *SetUserTagReq, opts ...grpc.CallOption) (*SetUserTagResp, error) {
	out := new(SetUserTagResp)
	err := c.cc.Invoke(ctx, "/user_tag_v2.UserTag/SetUserTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserTagServer is the server API for UserTag service.
type UserTagServer interface {
	SetUserTag(context.Context, *SetUserTagReq) (*SetUserTagResp, error)
}

func RegisterUserTagServer(s *grpc.Server, srv UserTagServer) {
	s.RegisterService(&_UserTag_serviceDesc, srv)
}

func _UserTag_SetUserTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagServer).SetUserTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_v2.UserTag/SetUserTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagServer).SetUserTag(ctx, req.(*SetUserTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserTag_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user_tag_v2.UserTag",
	HandlerType: (*UserTagServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetUserTag",
			Handler:    _UserTag_SetUserTag_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user-tag-v2/user-tag-v2.proto",
}

func init() {
	proto.RegisterFile("user-tag-v2/user-tag-v2.proto", fileDescriptor_user_tag_v2_5dbc57627d8b5253)
}

var fileDescriptor_user_tag_v2_5dbc57627d8b5253 = []byte{
	// 702 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x56, 0x5b, 0x6f, 0xd3, 0x30,
	0x14, 0x6e, 0xd6, 0xb1, 0xcb, 0xe9, 0x65, 0x93, 0xd9, 0xa4, 0xb4, 0xdd, 0xa5, 0x32, 0x17, 0x15,
	0xa1, 0xb5, 0x52, 0x10, 0x2f, 0x20, 0x90, 0x46, 0x27, 0x45, 0x93, 0x60, 0x48, 0x59, 0xe1, 0x81,
	0x97, 0xc8, 0x24, 0x6e, 0x88, 0x94, 0xdb, 0x6a, 0x33, 0x6d, 0xff, 0x81, 0x17, 0xf8, 0xc5, 0x28,
	0x5e, 0xb3, 0xda, 0x6e, 0xd3, 0x4d, 0xe3, 0x81, 0x37, 0xfb, 0xd8, 0xe7, 0x3b, 0xdf, 0x89, 0xcf,
	0xf7, 0x29, 0xb0, 0xff, 0x93, 0xd1, 0xc9, 0x11, 0x27, 0xc1, 0xd1, 0xa5, 0x35, 0x90, 0xd6, 0xfd,
	0x6c, 0x92, 0xf2, 0x14, 0xd5, 0xf2, 0x90, 0xcb, 0x49, 0xe0, 0x5e, 0x5a, 0x78, 0x07, 0x90, 0x4d,
	0xb9, 0x4d, 0x62, 0x3a, 0xba, 0xce, 0xe8, 0xc7, 0x90, 0x71, 0x87, 0x5e, 0x60, 0x1b, 0x36, 0x8a,
	0x10, 0x3a, 0x80, 0x5a, 0x40, 0x62, 0x2a, 0x12, 0x42, 0xdf, 0x34, 0xba, 0x46, 0xaf, 0xe1, 0x6c,
	0xe6, 0xa1, 0x11, 0x09, 0x4e, 0x7d, 0xd4, 0x01, 0xb1, 0x71, 0x13, 0x12, 0x53, 0x73, 0xa5, 0x6b,
	0xf4, 0x36, 0x9d, 0x8d, 0x3c, 0x70, 0x46, 0x62, 0x8a, 0x1d, 0x78, 0x3c, 0x07, 0xcf, 0x32, 0xf4,
	0x16, 0x9a, 0x37, 0x98, 0xd7, 0x19, 0x75, 0xa3, 0x90, 0x71, 0xd3, 0xe8, 0x56, 0x7b, 0x35, 0x6b,
	0xb7, 0x2f, 0x71, 0xeb, 0x17, 0x69, 0x4e, 0x3d, 0x90, 0x00, 0xf0, 0x6f, 0x03, 0xcc, 0x63, 0xdf,
	0x3f, 0x4b, 0x05, 0xee, 0x90, 0x4c, 0xfc, 0x11, 0x09, 0x86, 0x69, 0x32, 0x76, 0xe8, 0xc5, 0x9d,
	0x6c, 0x77, 0xe0, 0x11, 0x0f, 0x79, 0x54, 0x30, 0xbd, 0xd9, 0xa0, 0xa7, 0xd0, 0x8c, 0xc9, 0x95,
	0xcb, 0x68, 0x44, 0x3d, 0xee, 0x7a, 0x09, 0x37, 0xab, 0x22, 0xb1, 0x1e, 0x93, 0xab, 0x73, 0x11,
	0x1c, 0x26, 0x1c, 0xb5, 0x60, 0x23, 0x87, 0x15, 0x7c, 0x57, 0xbb, 0xd5, 0xde, 0xa6, 0xb3, 0xce,
	0x49, 0x20, 0x38, 0x75, 0xa0, 0x55, 0x42, 0x89, 0x65, 0xf8, 0x1d, 0x74, 0x4e, 0x68, 0x44, 0x39,
	0x7d, 0x10, 0x65, 0x7c, 0x00, 0x7b, 0xe5, 0xe9, 0x2c, 0xc3, 0x6f, 0xc0, 0xb4, 0x29, 0x7f, 0x18,
	0xf6, 0x1f, 0x03, 0x5a, 0x25, 0xc9, 0x2c, 0xfb, 0x5f, 0x1f, 0x73, 0x1f, 0x3a, 0x36, 0xe5, 0xc7,
	0x51, 0xb4, 0xb0, 0x27, 0xfc, 0x19, 0xd0, 0xfc, 0xc1, 0x9d, 0x5c, 0xe5, 0x7a, 0x2b, 0x6a, 0x3d,
	0x0f, 0xf6, 0xca, 0xeb, 0xb1, 0x0c, 0x0d, 0xa1, 0x41, 0xa2, 0xc8, 0xf5, 0xd2, 0x64, 0x2c, 0x0f,
	0xeb, 0xa1, 0x32, 0xac, 0x0b, 0x72, 0x6b, 0x24, 0x8a, 0xf2, 0x85, 0x28, 0xd2, 0x13, 0x42, 0x1b,
	0x46, 0x84, 0xb1, 0x70, 0x7c, 0x3d, 0x15, 0x1a, 0x42, 0xb0, 0x9a, 0x6b, 0x60, 0x4a, 0x57, 0xac,
	0xf1, 0x08, 0xea, 0xc5, 0xb5, 0xd3, 0x64, 0x9c, 0xa2, 0x43, 0xa8, 0x79, 0xd3, 0xfd, 0xac, 0x33,
	0x28, 0x42, 0xa7, 0x3e, 0x7a, 0x02, 0x8d, 0xdb, 0x0b, 0x92, 0x0a, 0xeb, 0x45, 0x50, 0x28, 0x31,
	0x14, 0x4a, 0x54, 0xeb, 0xb3, 0x6c, 0x11, 0x01, 0xf4, 0x5e, 0xc2, 0xbb, 0xfd, 0x5e, 0x35, 0xab,
	0xa5, 0xf4, 0x2b, 0x53, 0x9c, 0x95, 0x12, 0xad, 0xba, 0xd0, 0x38, 0xf6, 0xe5, 0x29, 0x5c, 0x54,
	0x44, 0xeb, 0x6a, 0x65, 0xae, 0x2b, 0xf9, 0xc1, 0xaa, 0xea, 0x83, 0x6d, 0x43, 0x53, 0x2e, 0xc0,
	0x32, 0x7c, 0x02, 0x0d, 0x9b, 0xf2, 0x7f, 0x2c, 0x89, 0x5f, 0x42, 0x53, 0x46, 0x61, 0x99, 0x42,
	0xc2, 0x50, 0x49, 0x6c, 0x41, 0xe3, 0x9c, 0xf2, 0x2f, 0x8c, 0x4e, 0x46, 0x24, 0xc8, 0xe7, 0x72,
	0x1b, 0x9a, 0x72, 0x80, 0x65, 0xd6, 0xaf, 0x35, 0x68, 0x4e, 0xf7, 0x39, 0xe2, 0xa7, 0x60, 0x82,
	0xbe, 0xc2, 0x96, 0x66, 0x88, 0x48, 0x9d, 0xa3, 0x79, 0x37, 0x6e, 0x77, 0x97, 0x5f, 0x60, 0x19,
	0xae, 0xa0, 0x1f, 0xb0, 0xbb, 0xd0, 0x80, 0xd0, 0x33, 0x25, 0xb9, 0xcc, 0x37, 0xdb, 0xcf, 0xef,
	0x73, 0x4d, 0x54, 0xba, 0x00, 0xb3, 0xcc, 0x8e, 0x50, 0x4f, 0x41, 0x59, 0x62, 0x7a, 0xed, 0x17,
	0xf7, 0xbc, 0x59, 0x34, 0xb7, 0xd0, 0xa4, 0xb4, 0xe6, 0xca, 0x5c, 0x50, 0x6b, 0xae, 0xd4, 0xef,
	0x6e, 0x9a, 0x2b, 0xb3, 0x02, 0xad, 0xb9, 0x25, 0x0e, 0xa5, 0x35, 0xb7, 0xcc, 0x5b, 0x70, 0x65,
	0x3a, 0x11, 0xb2, 0x30, 0xe7, 0x27, 0x42, 0xb3, 0x8d, 0xf9, 0x89, 0xd0, 0x75, 0x8d, 0x2b, 0xc8,
	0x06, 0x98, 0x89, 0x04, 0xb5, 0xf5, 0xf7, 0x95, 0xe8, 0x76, 0x4a, 0xcf, 0x0a, 0xa0, 0x99, 0x2a,
	0x34, 0x20, 0x45, 0x74, 0x1a, 0x90, 0x2a, 0x25, 0x5c, 0xb1, 0x1c, 0x58, 0x9f, 0xaa, 0x21, 0xc7,
	0x9c, 0x69, 0x45, 0xc3, 0x54, 0x54, 0xa5, 0x61, 0xaa, 0x02, 0xc3, 0x95, 0x0f, 0x83, 0x6f, 0x47,
	0x41, 0x1a, 0x91, 0x24, 0xe8, 0xbf, 0xb6, 0x38, 0xef, 0x7b, 0x69, 0x3c, 0x10, 0x7f, 0x39, 0x5e,
	0x1a, 0x0d, 0x18, 0x9d, 0x5c, 0x86, 0x1e, 0x65, 0xf2, 0x3f, 0xd0, 0xf7, 0x35, 0x71, 0xfc, 0xea,
	0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x12, 0x24, 0x2c, 0x6d, 0x25, 0x09, 0x00, 0x00,
}
