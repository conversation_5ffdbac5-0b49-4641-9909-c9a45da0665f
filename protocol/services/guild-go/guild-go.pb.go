// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/guild/guild-go.proto

package guild_go // import "golang.52tt.com/protocol/services/guild-go"

/*
buf:lint:ignore DIRECTORY_SAME_PACKAGE
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// buf:lint:ignore ENUM_PASCAL_CASE
type E_OPT_INVALID_TYPE int32

const (
	E_OPT_INVALID_TYPE_NIL      E_OPT_INVALID_TYPE = 0
	E_OPT_INVALID_TYPE_CHECK_IN E_OPT_INVALID_TYPE = 1
	E_OPT_INVALID_TYPE_DONATE   E_OPT_INVALID_TYPE = 2
)

var E_OPT_INVALID_TYPE_name = map[int32]string{
	0: "NIL",
	1: "CHECK_IN",
	2: "DONATE",
}
var E_OPT_INVALID_TYPE_value = map[string]int32{
	"NIL":      0,
	"CHECK_IN": 1,
	"DONATE":   2,
}

func (x E_OPT_INVALID_TYPE) String() string {
	return proto.EnumName(E_OPT_INVALID_TYPE_name, int32(x))
}
func (E_OPT_INVALID_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{0}
}

// buf:lint:ignore ENUM_PASCAL_CASE
type E_GUILD_CHECK_IN_RANK_TYPE int32

const (
	E_GUILD_CHECK_IN_RANK_TYPE_CHECK_IN_TIME  E_GUILD_CHECK_IN_RANK_TYPE = 0
	E_GUILD_CHECK_IN_RANK_TYPE_CHECK_IN_COUNT E_GUILD_CHECK_IN_RANK_TYPE = 1
)

var E_GUILD_CHECK_IN_RANK_TYPE_name = map[int32]string{
	0: "CHECK_IN_TIME",
	1: "CHECK_IN_COUNT",
}
var E_GUILD_CHECK_IN_RANK_TYPE_value = map[string]int32{
	"CHECK_IN_TIME":  0,
	"CHECK_IN_COUNT": 1,
}

func (x E_GUILD_CHECK_IN_RANK_TYPE) String() string {
	return proto.EnumName(E_GUILD_CHECK_IN_RANK_TYPE_name, int32(x))
}
func (E_GUILD_CHECK_IN_RANK_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{1}
}

type GuildInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ShortId              uint32   `protobuf:"varint,2,opt,name=short_id,json=shortId,proto3" json:"short_id,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Intro                string   `protobuf:"bytes,4,opt,name=intro,proto3" json:"intro,omitempty"`
	MemberCount          uint32   `protobuf:"varint,5,opt,name=member_count,json=memberCount,proto3" json:"member_count,omitempty"`
	MainGroup            uint32   `protobuf:"varint,6,opt,name=main_group,json=mainGroup,proto3" json:"main_group,omitempty"`
	Creator              uint64   `protobuf:"varint,7,opt,name=creator,proto3" json:"creator,omitempty"`
	Owner                uint64   `protobuf:"varint,8,opt,name=owner,proto3" json:"owner,omitempty"`
	Business             bool     `protobuf:"varint,9,opt,name=business,proto3" json:"business,omitempty"`
	CreatedAt            int64    `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	GameCount            uint32   `protobuf:"varint,11,opt,name=game_count,json=gameCount,proto3" json:"game_count,omitempty"`
	Prefix               string   `protobuf:"bytes,12,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Manifesto            string   `protobuf:"bytes,13,opt,name=manifesto,proto3" json:"manifesto,omitempty"`
	NeedVerify           bool     `protobuf:"varint,14,opt,name=need_verify,json=needVerify,proto3" json:"need_verify,omitempty"`
	GuildGameLv          uint32   `protobuf:"varint,15,opt,name=guild_game_lv,json=guildGameLv,proto3" json:"guild_game_lv,omitempty"`
	IsDelete             uint32   `protobuf:"varint,16,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	NeedVerifyV2         uint32   `protobuf:"varint,17,opt,name=need_verify_v2,json=needVerifyV2,proto3" json:"need_verify_v2,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildInfo) Reset()         { *m = GuildInfo{} }
func (m *GuildInfo) String() string { return proto.CompactTextString(m) }
func (*GuildInfo) ProtoMessage()    {}
func (*GuildInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{0}
}
func (m *GuildInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInfo.Unmarshal(m, b)
}
func (m *GuildInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInfo.Marshal(b, m, deterministic)
}
func (dst *GuildInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInfo.Merge(dst, src)
}
func (m *GuildInfo) XXX_Size() int {
	return xxx_messageInfo_GuildInfo.Size(m)
}
func (m *GuildInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInfo proto.InternalMessageInfo

func (m *GuildInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildInfo) GetShortId() uint32 {
	if m != nil {
		return m.ShortId
	}
	return 0
}

func (m *GuildInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GuildInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *GuildInfo) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

func (m *GuildInfo) GetMainGroup() uint32 {
	if m != nil {
		return m.MainGroup
	}
	return 0
}

func (m *GuildInfo) GetCreator() uint64 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *GuildInfo) GetOwner() uint64 {
	if m != nil {
		return m.Owner
	}
	return 0
}

func (m *GuildInfo) GetBusiness() bool {
	if m != nil {
		return m.Business
	}
	return false
}

func (m *GuildInfo) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *GuildInfo) GetGameCount() uint32 {
	if m != nil {
		return m.GameCount
	}
	return 0
}

func (m *GuildInfo) GetPrefix() string {
	if m != nil {
		return m.Prefix
	}
	return ""
}

func (m *GuildInfo) GetManifesto() string {
	if m != nil {
		return m.Manifesto
	}
	return ""
}

func (m *GuildInfo) GetNeedVerify() bool {
	if m != nil {
		return m.NeedVerify
	}
	return false
}

func (m *GuildInfo) GetGuildGameLv() uint32 {
	if m != nil {
		return m.GuildGameLv
	}
	return 0
}

func (m *GuildInfo) GetIsDelete() uint32 {
	if m != nil {
		return m.IsDelete
	}
	return 0
}

func (m *GuildInfo) GetNeedVerifyV2() uint32 {
	if m != nil {
		return m.NeedVerifyV2
	}
	return 0
}

type GetGuildByIdReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GetAll               bool     `protobuf:"varint,2,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildByIdReq) Reset()         { *m = GetGuildByIdReq{} }
func (m *GetGuildByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildByIdReq) ProtoMessage()    {}
func (*GetGuildByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{1}
}
func (m *GetGuildByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildByIdReq.Unmarshal(m, b)
}
func (m *GetGuildByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildByIdReq.Merge(dst, src)
}
func (m *GetGuildByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildByIdReq.Size(m)
}
func (m *GetGuildByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildByIdReq proto.InternalMessageInfo

func (m *GetGuildByIdReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildByIdReq) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

type GetGuildByIdResp struct {
	Info                 *GuildInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetGuildByIdResp) Reset()         { *m = GetGuildByIdResp{} }
func (m *GetGuildByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildByIdResp) ProtoMessage()    {}
func (*GetGuildByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{2}
}
func (m *GetGuildByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildByIdResp.Unmarshal(m, b)
}
func (m *GetGuildByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildByIdResp.Merge(dst, src)
}
func (m *GetGuildByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildByIdResp.Size(m)
}
func (m *GetGuildByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildByIdResp proto.InternalMessageInfo

func (m *GetGuildByIdResp) GetInfo() *GuildInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchGetGuildByIdReq struct {
	GuildIds             []uint32 `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	GetAll               bool     `protobuf:"varint,2,opt,name=get_all,json=getAll,proto3" json:"get_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGuildByIdReq) Reset()         { *m = BatchGetGuildByIdReq{} }
func (m *BatchGetGuildByIdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildByIdReq) ProtoMessage()    {}
func (*BatchGetGuildByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{3}
}
func (m *BatchGetGuildByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGuildByIdReq.Unmarshal(m, b)
}
func (m *BatchGetGuildByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGuildByIdReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGuildByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGuildByIdReq.Merge(dst, src)
}
func (m *BatchGetGuildByIdReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGuildByIdReq.Size(m)
}
func (m *BatchGetGuildByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGuildByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGuildByIdReq proto.InternalMessageInfo

func (m *BatchGetGuildByIdReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *BatchGetGuildByIdReq) GetGetAll() bool {
	if m != nil {
		return m.GetAll
	}
	return false
}

type BatchGetGuildByIdResp struct {
	List                 []*GuildInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	NotExist             []uint32     `protobuf:"varint,2,rep,packed,name=not_exist,json=notExist,proto3" json:"not_exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetGuildByIdResp) Reset()         { *m = BatchGetGuildByIdResp{} }
func (m *BatchGetGuildByIdResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildByIdResp) ProtoMessage()    {}
func (*BatchGetGuildByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{4}
}
func (m *BatchGetGuildByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGuildByIdResp.Unmarshal(m, b)
}
func (m *BatchGetGuildByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGuildByIdResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGuildByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGuildByIdResp.Merge(dst, src)
}
func (m *BatchGetGuildByIdResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGuildByIdResp.Size(m)
}
func (m *BatchGetGuildByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGuildByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGuildByIdResp proto.InternalMessageInfo

func (m *BatchGetGuildByIdResp) GetList() []*GuildInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *BatchGetGuildByIdResp) GetNotExist() []uint32 {
	if m != nil {
		return m.NotExist
	}
	return nil
}

type GetGuildMemberReq struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMemberReq) Reset()         { *m = GetGuildMemberReq{} }
func (m *GetGuildMemberReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMemberReq) ProtoMessage()    {}
func (*GetGuildMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{5}
}
func (m *GetGuildMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMemberReq.Unmarshal(m, b)
}
func (m *GetGuildMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMemberReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMemberReq.Merge(dst, src)
}
func (m *GetGuildMemberReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildMemberReq.Size(m)
}
func (m *GetGuildMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMemberReq proto.InternalMessageInfo

func (m *GetGuildMemberReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGuildMemberReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GuildMemberResp struct {
	Uid                  uint64   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 uint32   `protobuf:"varint,2,opt,name=role,proto3" json:"role,omitempty"`
	Remark               string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	Weight               uint32   `protobuf:"varint,4,opt,name=weight,proto3" json:"weight,omitempty"`
	Permission           uint32   `protobuf:"varint,5,opt,name=permission,proto3" json:"permission,omitempty"`
	JoinAt               int64    `protobuf:"varint,6,opt,name=join_at,json=joinAt,proto3" json:"join_at,omitempty"`
	Title                string   `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	OptInvalid           uint32   `protobuf:"varint,8,opt,name=opt_invalid,json=optInvalid,proto3" json:"opt_invalid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildMemberResp) Reset()         { *m = GuildMemberResp{} }
func (m *GuildMemberResp) String() string { return proto.CompactTextString(m) }
func (*GuildMemberResp) ProtoMessage()    {}
func (*GuildMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{6}
}
func (m *GuildMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildMemberResp.Unmarshal(m, b)
}
func (m *GuildMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildMemberResp.Marshal(b, m, deterministic)
}
func (dst *GuildMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildMemberResp.Merge(dst, src)
}
func (m *GuildMemberResp) XXX_Size() int {
	return xxx_messageInfo_GuildMemberResp.Size(m)
}
func (m *GuildMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildMemberResp proto.InternalMessageInfo

func (m *GuildMemberResp) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildMemberResp) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *GuildMemberResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *GuildMemberResp) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *GuildMemberResp) GetPermission() uint32 {
	if m != nil {
		return m.Permission
	}
	return 0
}

func (m *GuildMemberResp) GetJoinAt() int64 {
	if m != nil {
		return m.JoinAt
	}
	return 0
}

func (m *GuildMemberResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GuildMemberResp) GetOptInvalid() uint32 {
	if m != nil {
		return m.OptInvalid
	}
	return 0
}

type GetGuildBulletinListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildBulletinListReq) Reset()         { *m = GetGuildBulletinListReq{} }
func (m *GetGuildBulletinListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildBulletinListReq) ProtoMessage()    {}
func (*GetGuildBulletinListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{7}
}
func (m *GetGuildBulletinListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildBulletinListReq.Unmarshal(m, b)
}
func (m *GetGuildBulletinListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildBulletinListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildBulletinListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildBulletinListReq.Merge(dst, src)
}
func (m *GetGuildBulletinListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildBulletinListReq.Size(m)
}
func (m *GetGuildBulletinListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildBulletinListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildBulletinListReq proto.InternalMessageInfo

func (m *GetGuildBulletinListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildBulletinListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildBulletinListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GuildBulletin struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BulletinId           uint32   `protobuf:"varint,2,opt,name=bulletin_id,json=bulletinId,proto3" json:"bulletin_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Title                string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Content              string   `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	BulletinType         uint32   `protobuf:"varint,6,opt,name=bulletin_type,json=bulletinType,proto3" json:"bulletin_type,omitempty"`
	CreatedAt            int64    `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            int64    `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildBulletin) Reset()         { *m = GuildBulletin{} }
func (m *GuildBulletin) String() string { return proto.CompactTextString(m) }
func (*GuildBulletin) ProtoMessage()    {}
func (*GuildBulletin) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{8}
}
func (m *GuildBulletin) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildBulletin.Unmarshal(m, b)
}
func (m *GuildBulletin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildBulletin.Marshal(b, m, deterministic)
}
func (dst *GuildBulletin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildBulletin.Merge(dst, src)
}
func (m *GuildBulletin) XXX_Size() int {
	return xxx_messageInfo_GuildBulletin.Size(m)
}
func (m *GuildBulletin) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildBulletin.DiscardUnknown(m)
}

var xxx_messageInfo_GuildBulletin proto.InternalMessageInfo

func (m *GuildBulletin) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildBulletin) GetBulletinId() uint32 {
	if m != nil {
		return m.BulletinId
	}
	return 0
}

func (m *GuildBulletin) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildBulletin) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GuildBulletin) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *GuildBulletin) GetBulletinType() uint32 {
	if m != nil {
		return m.BulletinType
	}
	return 0
}

func (m *GuildBulletin) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *GuildBulletin) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

type GetGuildBulletinListResp struct {
	List                 []*GuildBulletin `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGuildBulletinListResp) Reset()         { *m = GetGuildBulletinListResp{} }
func (m *GetGuildBulletinListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildBulletinListResp) ProtoMessage()    {}
func (*GetGuildBulletinListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{9}
}
func (m *GetGuildBulletinListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildBulletinListResp.Unmarshal(m, b)
}
func (m *GetGuildBulletinListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildBulletinListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildBulletinListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildBulletinListResp.Merge(dst, src)
}
func (m *GetGuildBulletinListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildBulletinListResp.Size(m)
}
func (m *GetGuildBulletinListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildBulletinListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildBulletinListResp proto.InternalMessageInfo

func (m *GetGuildBulletinListResp) GetList() []*GuildBulletin {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetGuildBulletinListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type HasGuildPermissionReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Permission           uint32   `protobuf:"varint,3,opt,name=permission,proto3" json:"permission,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HasGuildPermissionReq) Reset()         { *m = HasGuildPermissionReq{} }
func (m *HasGuildPermissionReq) String() string { return proto.CompactTextString(m) }
func (*HasGuildPermissionReq) ProtoMessage()    {}
func (*HasGuildPermissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{10}
}
func (m *HasGuildPermissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HasGuildPermissionReq.Unmarshal(m, b)
}
func (m *HasGuildPermissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HasGuildPermissionReq.Marshal(b, m, deterministic)
}
func (dst *HasGuildPermissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HasGuildPermissionReq.Merge(dst, src)
}
func (m *HasGuildPermissionReq) XXX_Size() int {
	return xxx_messageInfo_HasGuildPermissionReq.Size(m)
}
func (m *HasGuildPermissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HasGuildPermissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_HasGuildPermissionReq proto.InternalMessageInfo

func (m *HasGuildPermissionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *HasGuildPermissionReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HasGuildPermissionReq) GetPermission() uint32 {
	if m != nil {
		return m.Permission
	}
	return 0
}

type HasGuildPermissionResp struct {
	Has                  bool     `protobuf:"varint,1,opt,name=has,proto3" json:"has,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HasGuildPermissionResp) Reset()         { *m = HasGuildPermissionResp{} }
func (m *HasGuildPermissionResp) String() string { return proto.CompactTextString(m) }
func (*HasGuildPermissionResp) ProtoMessage()    {}
func (*HasGuildPermissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{11}
}
func (m *HasGuildPermissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HasGuildPermissionResp.Unmarshal(m, b)
}
func (m *HasGuildPermissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HasGuildPermissionResp.Marshal(b, m, deterministic)
}
func (dst *HasGuildPermissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HasGuildPermissionResp.Merge(dst, src)
}
func (m *HasGuildPermissionResp) XXX_Size() int {
	return xxx_messageInfo_HasGuildPermissionResp.Size(m)
}
func (m *HasGuildPermissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HasGuildPermissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_HasGuildPermissionResp proto.InternalMessageInfo

func (m *HasGuildPermissionResp) GetHas() bool {
	if m != nil {
		return m.Has
	}
	return false
}

// 官员职位信息
type GuildOfficialInfo struct {
	OfficialId           uint32   `protobuf:"varint,1,opt,name=official_id,json=officialId,proto3" json:"official_id,omitempty"`
	OfficialName         string   `protobuf:"bytes,2,opt,name=official_name,json=officialName,proto3" json:"official_name,omitempty"`
	Permission           uint32   `protobuf:"varint,3,opt,name=permission,proto3" json:"permission,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildOfficialInfo) Reset()         { *m = GuildOfficialInfo{} }
func (m *GuildOfficialInfo) String() string { return proto.CompactTextString(m) }
func (*GuildOfficialInfo) ProtoMessage()    {}
func (*GuildOfficialInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{12}
}
func (m *GuildOfficialInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildOfficialInfo.Unmarshal(m, b)
}
func (m *GuildOfficialInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildOfficialInfo.Marshal(b, m, deterministic)
}
func (dst *GuildOfficialInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildOfficialInfo.Merge(dst, src)
}
func (m *GuildOfficialInfo) XXX_Size() int {
	return xxx_messageInfo_GuildOfficialInfo.Size(m)
}
func (m *GuildOfficialInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildOfficialInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildOfficialInfo proto.InternalMessageInfo

func (m *GuildOfficialInfo) GetOfficialId() uint32 {
	if m != nil {
		return m.OfficialId
	}
	return 0
}

func (m *GuildOfficialInfo) GetOfficialName() string {
	if m != nil {
		return m.OfficialName
	}
	return ""
}

func (m *GuildOfficialInfo) GetPermission() uint32 {
	if m != nil {
		return m.Permission
	}
	return 0
}

type GetGuildOfficialByUidReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildOfficialByUidReq) Reset()         { *m = GetGuildOfficialByUidReq{} }
func (m *GetGuildOfficialByUidReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildOfficialByUidReq) ProtoMessage()    {}
func (*GetGuildOfficialByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{13}
}
func (m *GetGuildOfficialByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildOfficialByUidReq.Unmarshal(m, b)
}
func (m *GetGuildOfficialByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildOfficialByUidReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildOfficialByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildOfficialByUidReq.Merge(dst, src)
}
func (m *GetGuildOfficialByUidReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildOfficialByUidReq.Size(m)
}
func (m *GetGuildOfficialByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildOfficialByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildOfficialByUidReq proto.InternalMessageInfo

func (m *GetGuildOfficialByUidReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildOfficialByUidReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGuildOfficialByUidResp struct {
	Info                 *GuildOfficialInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGuildOfficialByUidResp) Reset()         { *m = GetGuildOfficialByUidResp{} }
func (m *GetGuildOfficialByUidResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildOfficialByUidResp) ProtoMessage()    {}
func (*GetGuildOfficialByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{14}
}
func (m *GetGuildOfficialByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildOfficialByUidResp.Unmarshal(m, b)
}
func (m *GetGuildOfficialByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildOfficialByUidResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildOfficialByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildOfficialByUidResp.Merge(dst, src)
}
func (m *GetGuildOfficialByUidResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildOfficialByUidResp.Size(m)
}
func (m *GetGuildOfficialByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildOfficialByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildOfficialByUidResp proto.InternalMessageInfo

func (m *GetGuildOfficialByUidResp) GetInfo() *GuildOfficialInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchUpdateGameGuildStarLevelReq struct {
	GuildLevel           map[uint32]uint32 `protobuf:"bytes,1,rep,name=guild_level,json=guildLevel,proto3" json:"guild_level,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchUpdateGameGuildStarLevelReq) Reset()         { *m = BatchUpdateGameGuildStarLevelReq{} }
func (m *BatchUpdateGameGuildStarLevelReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateGameGuildStarLevelReq) ProtoMessage()    {}
func (*BatchUpdateGameGuildStarLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{15}
}
func (m *BatchUpdateGameGuildStarLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateGameGuildStarLevelReq.Unmarshal(m, b)
}
func (m *BatchUpdateGameGuildStarLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateGameGuildStarLevelReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateGameGuildStarLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateGameGuildStarLevelReq.Merge(dst, src)
}
func (m *BatchUpdateGameGuildStarLevelReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateGameGuildStarLevelReq.Size(m)
}
func (m *BatchUpdateGameGuildStarLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateGameGuildStarLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateGameGuildStarLevelReq proto.InternalMessageInfo

func (m *BatchUpdateGameGuildStarLevelReq) GetGuildLevel() map[uint32]uint32 {
	if m != nil {
		return m.GuildLevel
	}
	return nil
}

type BatchUpdateGameGuildStarLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateGameGuildStarLevelResp) Reset()         { *m = BatchUpdateGameGuildStarLevelResp{} }
func (m *BatchUpdateGameGuildStarLevelResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateGameGuildStarLevelResp) ProtoMessage()    {}
func (*BatchUpdateGameGuildStarLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{16}
}
func (m *BatchUpdateGameGuildStarLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateGameGuildStarLevelResp.Unmarshal(m, b)
}
func (m *BatchUpdateGameGuildStarLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateGameGuildStarLevelResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateGameGuildStarLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateGameGuildStarLevelResp.Merge(dst, src)
}
func (m *BatchUpdateGameGuildStarLevelResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateGameGuildStarLevelResp.Size(m)
}
func (m *BatchUpdateGameGuildStarLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateGameGuildStarLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateGameGuildStarLevelResp proto.InternalMessageInfo

type GetGuildGameCountConfigReq struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildGameCountConfigReq) Reset()         { *m = GetGuildGameCountConfigReq{} }
func (m *GetGuildGameCountConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildGameCountConfigReq) ProtoMessage()    {}
func (*GetGuildGameCountConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{17}
}
func (m *GetGuildGameCountConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildGameCountConfigReq.Unmarshal(m, b)
}
func (m *GetGuildGameCountConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildGameCountConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildGameCountConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildGameCountConfigReq.Merge(dst, src)
}
func (m *GetGuildGameCountConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildGameCountConfigReq.Size(m)
}
func (m *GetGuildGameCountConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildGameCountConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildGameCountConfigReq proto.InternalMessageInfo

func (m *GetGuildGameCountConfigReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type GetGuildGameCountConfigResp struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Extra                uint32   `protobuf:"varint,3,opt,name=extra,proto3" json:"extra,omitempty"`
	Cost                 uint32   `protobuf:"varint,4,opt,name=cost,proto3" json:"cost,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildGameCountConfigResp) Reset()         { *m = GetGuildGameCountConfigResp{} }
func (m *GetGuildGameCountConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildGameCountConfigResp) ProtoMessage()    {}
func (*GetGuildGameCountConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{18}
}
func (m *GetGuildGameCountConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildGameCountConfigResp.Unmarshal(m, b)
}
func (m *GetGuildGameCountConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildGameCountConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildGameCountConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildGameCountConfigResp.Merge(dst, src)
}
func (m *GetGuildGameCountConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildGameCountConfigResp.Size(m)
}
func (m *GetGuildGameCountConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildGameCountConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildGameCountConfigResp proto.InternalMessageInfo

func (m *GetGuildGameCountConfigResp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetGuildGameCountConfigResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetGuildGameCountConfigResp) GetExtra() uint32 {
	if m != nil {
		return m.Extra
	}
	return 0
}

func (m *GetGuildGameCountConfigResp) GetCost() uint32 {
	if m != nil {
		return m.Cost
	}
	return 0
}

type GetGuildExtraGameCountReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildExtraGameCountReq) Reset()         { *m = GetGuildExtraGameCountReq{} }
func (m *GetGuildExtraGameCountReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildExtraGameCountReq) ProtoMessage()    {}
func (*GetGuildExtraGameCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{19}
}
func (m *GetGuildExtraGameCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildExtraGameCountReq.Unmarshal(m, b)
}
func (m *GetGuildExtraGameCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildExtraGameCountReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildExtraGameCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildExtraGameCountReq.Merge(dst, src)
}
func (m *GetGuildExtraGameCountReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildExtraGameCountReq.Size(m)
}
func (m *GetGuildExtraGameCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildExtraGameCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildExtraGameCountReq proto.InternalMessageInfo

func (m *GetGuildExtraGameCountReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildExtraGameCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildExtraGameCountResp) Reset()         { *m = GetGuildExtraGameCountResp{} }
func (m *GetGuildExtraGameCountResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildExtraGameCountResp) ProtoMessage()    {}
func (*GetGuildExtraGameCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{20}
}
func (m *GetGuildExtraGameCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildExtraGameCountResp.Unmarshal(m, b)
}
func (m *GetGuildExtraGameCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildExtraGameCountResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildExtraGameCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildExtraGameCountResp.Merge(dst, src)
}
func (m *GetGuildExtraGameCountResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildExtraGameCountResp.Size(m)
}
func (m *GetGuildExtraGameCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildExtraGameCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildExtraGameCountResp proto.InternalMessageInfo

func (m *GetGuildExtraGameCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetMemberRecentGameLoginReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMemberRecentGameLoginReq) Reset()         { *m = GetMemberRecentGameLoginReq{} }
func (m *GetMemberRecentGameLoginReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberRecentGameLoginReq) ProtoMessage()    {}
func (*GetMemberRecentGameLoginReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{21}
}
func (m *GetMemberRecentGameLoginReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberRecentGameLoginReq.Unmarshal(m, b)
}
func (m *GetMemberRecentGameLoginReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberRecentGameLoginReq.Marshal(b, m, deterministic)
}
func (dst *GetMemberRecentGameLoginReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberRecentGameLoginReq.Merge(dst, src)
}
func (m *GetMemberRecentGameLoginReq) XXX_Size() int {
	return xxx_messageInfo_GetMemberRecentGameLoginReq.Size(m)
}
func (m *GetMemberRecentGameLoginReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberRecentGameLoginReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberRecentGameLoginReq proto.InternalMessageInfo

func (m *GetMemberRecentGameLoginReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMemberRecentGameLoginReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type StMemberRecentLoginLyGame struct {
	LyGameId             uint32   `protobuf:"varint,1,opt,name=ly_game_id,json=lyGameId,proto3" json:"ly_game_id,omitempty"`
	LoginTs              int64    `protobuf:"varint,2,opt,name=login_ts,json=loginTs,proto3" json:"login_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StMemberRecentLoginLyGame) Reset()         { *m = StMemberRecentLoginLyGame{} }
func (m *StMemberRecentLoginLyGame) String() string { return proto.CompactTextString(m) }
func (*StMemberRecentLoginLyGame) ProtoMessage()    {}
func (*StMemberRecentLoginLyGame) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{22}
}
func (m *StMemberRecentLoginLyGame) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StMemberRecentLoginLyGame.Unmarshal(m, b)
}
func (m *StMemberRecentLoginLyGame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StMemberRecentLoginLyGame.Marshal(b, m, deterministic)
}
func (dst *StMemberRecentLoginLyGame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StMemberRecentLoginLyGame.Merge(dst, src)
}
func (m *StMemberRecentLoginLyGame) XXX_Size() int {
	return xxx_messageInfo_StMemberRecentLoginLyGame.Size(m)
}
func (m *StMemberRecentLoginLyGame) XXX_DiscardUnknown() {
	xxx_messageInfo_StMemberRecentLoginLyGame.DiscardUnknown(m)
}

var xxx_messageInfo_StMemberRecentLoginLyGame proto.InternalMessageInfo

func (m *StMemberRecentLoginLyGame) GetLyGameId() uint32 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *StMemberRecentLoginLyGame) GetLoginTs() int64 {
	if m != nil {
		return m.LoginTs
	}
	return 0
}

type GetMemberRecentGameLoginResp struct {
	LoginGame            *StMemberRecentLoginLyGame `protobuf:"bytes,1,opt,name=login_game,json=loginGame,proto3" json:"login_game,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetMemberRecentGameLoginResp) Reset()         { *m = GetMemberRecentGameLoginResp{} }
func (m *GetMemberRecentGameLoginResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberRecentGameLoginResp) ProtoMessage()    {}
func (*GetMemberRecentGameLoginResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{23}
}
func (m *GetMemberRecentGameLoginResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMemberRecentGameLoginResp.Unmarshal(m, b)
}
func (m *GetMemberRecentGameLoginResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMemberRecentGameLoginResp.Marshal(b, m, deterministic)
}
func (dst *GetMemberRecentGameLoginResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMemberRecentGameLoginResp.Merge(dst, src)
}
func (m *GetMemberRecentGameLoginResp) XXX_Size() int {
	return xxx_messageInfo_GetMemberRecentGameLoginResp.Size(m)
}
func (m *GetMemberRecentGameLoginResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMemberRecentGameLoginResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMemberRecentGameLoginResp proto.InternalMessageInfo

func (m *GetMemberRecentGameLoginResp) GetLoginGame() *StMemberRecentLoginLyGame {
	if m != nil {
		return m.LoginGame
	}
	return nil
}

type GuildCheckInInfo struct {
	GuildId                uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                    uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	LastCheckInAt          int64    `protobuf:"varint,3,opt,name=last_check_in_at,json=lastCheckInAt,proto3" json:"last_check_in_at,omitempty"`
	CheckInCount           uint32   `protobuf:"varint,4,opt,name=check_in_count,json=checkInCount,proto3" json:"check_in_count,omitempty"`
	SupplementDays         uint32   `protobuf:"varint,5,opt,name=supplement_days,json=supplementDays,proto3" json:"supplement_days,omitempty"`
	SupplementPrice        uint32   `protobuf:"varint,6,opt,name=supplement_price,json=supplementPrice,proto3" json:"supplement_price,omitempty"`
	SupplementedDays       uint32   `protobuf:"varint,7,opt,name=supplemented_days,json=supplementedDays,proto3" json:"supplemented_days,omitempty"`
	AccumulateCheckInCount uint32   `protobuf:"varint,8,opt,name=accumulate_check_in_count,json=accumulateCheckInCount,proto3" json:"accumulate_check_in_count,omitempty"`
	SupplementDayList      []uint32 `protobuf:"varint,9,rep,packed,name=supplement_day_list,json=supplementDayList,proto3" json:"supplement_day_list,omitempty"`
	RealLastCheckInAt      int64    `protobuf:"varint,10,opt,name=real_last_check_in_at,json=realLastCheckInAt,proto3" json:"real_last_check_in_at,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GuildCheckInInfo) Reset()         { *m = GuildCheckInInfo{} }
func (m *GuildCheckInInfo) String() string { return proto.CompactTextString(m) }
func (*GuildCheckInInfo) ProtoMessage()    {}
func (*GuildCheckInInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{24}
}
func (m *GuildCheckInInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildCheckInInfo.Unmarshal(m, b)
}
func (m *GuildCheckInInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildCheckInInfo.Marshal(b, m, deterministic)
}
func (dst *GuildCheckInInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildCheckInInfo.Merge(dst, src)
}
func (m *GuildCheckInInfo) XXX_Size() int {
	return xxx_messageInfo_GuildCheckInInfo.Size(m)
}
func (m *GuildCheckInInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildCheckInInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildCheckInInfo proto.InternalMessageInfo

func (m *GuildCheckInInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildCheckInInfo) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildCheckInInfo) GetLastCheckInAt() int64 {
	if m != nil {
		return m.LastCheckInAt
	}
	return 0
}

func (m *GuildCheckInInfo) GetCheckInCount() uint32 {
	if m != nil {
		return m.CheckInCount
	}
	return 0
}

func (m *GuildCheckInInfo) GetSupplementDays() uint32 {
	if m != nil {
		return m.SupplementDays
	}
	return 0
}

func (m *GuildCheckInInfo) GetSupplementPrice() uint32 {
	if m != nil {
		return m.SupplementPrice
	}
	return 0
}

func (m *GuildCheckInInfo) GetSupplementedDays() uint32 {
	if m != nil {
		return m.SupplementedDays
	}
	return 0
}

func (m *GuildCheckInInfo) GetAccumulateCheckInCount() uint32 {
	if m != nil {
		return m.AccumulateCheckInCount
	}
	return 0
}

func (m *GuildCheckInInfo) GetSupplementDayList() []uint32 {
	if m != nil {
		return m.SupplementDayList
	}
	return nil
}

func (m *GuildCheckInInfo) GetRealLastCheckInAt() int64 {
	if m != nil {
		return m.RealLastCheckInAt
	}
	return 0
}

type GetGuildCheckInReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCheckInReq) Reset()         { *m = GetGuildCheckInReq{} }
func (m *GetGuildCheckInReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildCheckInReq) ProtoMessage()    {}
func (*GetGuildCheckInReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{25}
}
func (m *GetGuildCheckInReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCheckInReq.Unmarshal(m, b)
}
func (m *GetGuildCheckInReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCheckInReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildCheckInReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCheckInReq.Merge(dst, src)
}
func (m *GetGuildCheckInReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildCheckInReq.Size(m)
}
func (m *GetGuildCheckInReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCheckInReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCheckInReq proto.InternalMessageInfo

func (m *GetGuildCheckInReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildCheckInReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGuildCheckInResp struct {
	Info                 *GuildCheckInInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetGuildCheckInResp) Reset()         { *m = GetGuildCheckInResp{} }
func (m *GetGuildCheckInResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildCheckInResp) ProtoMessage()    {}
func (*GetGuildCheckInResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{26}
}
func (m *GetGuildCheckInResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCheckInResp.Unmarshal(m, b)
}
func (m *GetGuildCheckInResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCheckInResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildCheckInResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCheckInResp.Merge(dst, src)
}
func (m *GetGuildCheckInResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildCheckInResp.Size(m)
}
func (m *GetGuildCheckInResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCheckInResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCheckInResp proto.InternalMessageInfo

func (m *GetGuildCheckInResp) GetInfo() *GuildCheckInInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetGuildCheckInListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	RankType             uint32   `protobuf:"varint,4,opt,name=rank_type,json=rankType,proto3" json:"rank_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCheckInListReq) Reset()         { *m = GetGuildCheckInListReq{} }
func (m *GetGuildCheckInListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildCheckInListReq) ProtoMessage()    {}
func (*GetGuildCheckInListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{27}
}
func (m *GetGuildCheckInListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCheckInListReq.Unmarshal(m, b)
}
func (m *GetGuildCheckInListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCheckInListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildCheckInListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCheckInListReq.Merge(dst, src)
}
func (m *GetGuildCheckInListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildCheckInListReq.Size(m)
}
func (m *GetGuildCheckInListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCheckInListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCheckInListReq proto.InternalMessageInfo

func (m *GetGuildCheckInListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildCheckInListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGuildCheckInListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetGuildCheckInListReq) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

type GetGuildCheckInListResp struct {
	List                 []*GuildCheckInInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGuildCheckInListResp) Reset()         { *m = GetGuildCheckInListResp{} }
func (m *GetGuildCheckInListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildCheckInListResp) ProtoMessage()    {}
func (*GetGuildCheckInListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{28}
}
func (m *GetGuildCheckInListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCheckInListResp.Unmarshal(m, b)
}
func (m *GetGuildCheckInListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCheckInListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildCheckInListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCheckInListResp.Merge(dst, src)
}
func (m *GetGuildCheckInListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildCheckInListResp.Size(m)
}
func (m *GetGuildCheckInListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCheckInListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCheckInListResp proto.InternalMessageInfo

func (m *GetGuildCheckInListResp) GetList() []*GuildCheckInInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type GetGuildCheckInCountReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCheckInCountReq) Reset()         { *m = GetGuildCheckInCountReq{} }
func (m *GetGuildCheckInCountReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildCheckInCountReq) ProtoMessage()    {}
func (*GetGuildCheckInCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{29}
}
func (m *GetGuildCheckInCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCheckInCountReq.Unmarshal(m, b)
}
func (m *GetGuildCheckInCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCheckInCountReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildCheckInCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCheckInCountReq.Merge(dst, src)
}
func (m *GetGuildCheckInCountReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildCheckInCountReq.Size(m)
}
func (m *GetGuildCheckInCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCheckInCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCheckInCountReq proto.InternalMessageInfo

func (m *GetGuildCheckInCountReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildCheckInCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildCheckInCountResp) Reset()         { *m = GetGuildCheckInCountResp{} }
func (m *GetGuildCheckInCountResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildCheckInCountResp) ProtoMessage()    {}
func (*GetGuildCheckInCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{30}
}
func (m *GetGuildCheckInCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildCheckInCountResp.Unmarshal(m, b)
}
func (m *GetGuildCheckInCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildCheckInCountResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildCheckInCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildCheckInCountResp.Merge(dst, src)
}
func (m *GetGuildCheckInCountResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildCheckInCountResp.Size(m)
}
func (m *GetGuildCheckInCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildCheckInCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildCheckInCountResp proto.InternalMessageInfo

func (m *GetGuildCheckInCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type BatchGetGuildYesterdayCheckInCountReq struct {
	GuildIdList          []uint32 `protobuf:"varint,1,rep,packed,name=guild_id_list,json=guildIdList,proto3" json:"guild_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGuildYesterdayCheckInCountReq) Reset()         { *m = BatchGetGuildYesterdayCheckInCountReq{} }
func (m *BatchGetGuildYesterdayCheckInCountReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildYesterdayCheckInCountReq) ProtoMessage()    {}
func (*BatchGetGuildYesterdayCheckInCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{31}
}
func (m *BatchGetGuildYesterdayCheckInCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGuildYesterdayCheckInCountReq.Unmarshal(m, b)
}
func (m *BatchGetGuildYesterdayCheckInCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGuildYesterdayCheckInCountReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGuildYesterdayCheckInCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGuildYesterdayCheckInCountReq.Merge(dst, src)
}
func (m *BatchGetGuildYesterdayCheckInCountReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGuildYesterdayCheckInCountReq.Size(m)
}
func (m *BatchGetGuildYesterdayCheckInCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGuildYesterdayCheckInCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGuildYesterdayCheckInCountReq proto.InternalMessageInfo

func (m *BatchGetGuildYesterdayCheckInCountReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

type BatchGetGuildYesterdayCheckInCountResp struct {
	GuildCount           map[uint32]uint32 `protobuf:"bytes,1,rep,name=guild_count,json=guildCount,proto3" json:"guild_count,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetGuildYesterdayCheckInCountResp) Reset() {
	*m = BatchGetGuildYesterdayCheckInCountResp{}
}
func (m *BatchGetGuildYesterdayCheckInCountResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildYesterdayCheckInCountResp) ProtoMessage()    {}
func (*BatchGetGuildYesterdayCheckInCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{32}
}
func (m *BatchGetGuildYesterdayCheckInCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGuildYesterdayCheckInCountResp.Unmarshal(m, b)
}
func (m *BatchGetGuildYesterdayCheckInCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGuildYesterdayCheckInCountResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGuildYesterdayCheckInCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGuildYesterdayCheckInCountResp.Merge(dst, src)
}
func (m *BatchGetGuildYesterdayCheckInCountResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGuildYesterdayCheckInCountResp.Size(m)
}
func (m *BatchGetGuildYesterdayCheckInCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGuildYesterdayCheckInCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGuildYesterdayCheckInCountResp proto.InternalMessageInfo

func (m *BatchGetGuildYesterdayCheckInCountResp) GetGuildCount() map[uint32]uint32 {
	if m != nil {
		return m.GuildCount
	}
	return nil
}

type GetGuildDonateReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDonateReq) Reset()         { *m = GetGuildDonateReq{} }
func (m *GetGuildDonateReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildDonateReq) ProtoMessage()    {}
func (*GetGuildDonateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{33}
}
func (m *GetGuildDonateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDonateReq.Unmarshal(m, b)
}
func (m *GetGuildDonateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDonateReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildDonateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDonateReq.Merge(dst, src)
}
func (m *GetGuildDonateReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildDonateReq.Size(m)
}
func (m *GetGuildDonateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDonateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDonateReq proto.InternalMessageInfo

func (m *GetGuildDonateReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildDonateReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGuildDonateResp struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint64   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	LastDonateAt         int64    `protobuf:"varint,3,opt,name=last_donate_at,json=lastDonateAt,proto3" json:"last_donate_at,omitempty"`
	DonateCount          uint32   `protobuf:"varint,4,opt,name=donate_count,json=donateCount,proto3" json:"donate_count,omitempty"`
	AccumDonateValue     uint32   `protobuf:"varint,5,opt,name=accum_donate_value,json=accumDonateValue,proto3" json:"accum_donate_value,omitempty"`
	LastDonateValue      uint32   `protobuf:"varint,6,opt,name=last_donate_value,json=lastDonateValue,proto3" json:"last_donate_value,omitempty"`
	RealLastDonateAt     int64    `protobuf:"varint,7,opt,name=real_last_donate_at,json=realLastDonateAt,proto3" json:"real_last_donate_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDonateResp) Reset()         { *m = GetGuildDonateResp{} }
func (m *GetGuildDonateResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildDonateResp) ProtoMessage()    {}
func (*GetGuildDonateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{34}
}
func (m *GetGuildDonateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDonateResp.Unmarshal(m, b)
}
func (m *GetGuildDonateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDonateResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildDonateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDonateResp.Merge(dst, src)
}
func (m *GetGuildDonateResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildDonateResp.Size(m)
}
func (m *GetGuildDonateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDonateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDonateResp proto.InternalMessageInfo

func (m *GetGuildDonateResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildDonateResp) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGuildDonateResp) GetLastDonateAt() int64 {
	if m != nil {
		return m.LastDonateAt
	}
	return 0
}

func (m *GetGuildDonateResp) GetDonateCount() uint32 {
	if m != nil {
		return m.DonateCount
	}
	return 0
}

func (m *GetGuildDonateResp) GetAccumDonateValue() uint32 {
	if m != nil {
		return m.AccumDonateValue
	}
	return 0
}

func (m *GetGuildDonateResp) GetLastDonateValue() uint32 {
	if m != nil {
		return m.LastDonateValue
	}
	return 0
}

func (m *GetGuildDonateResp) GetRealLastDonateAt() int64 {
	if m != nil {
		return m.RealLastDonateAt
	}
	return 0
}

type GetGuildDonateCountReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDonateCountReq) Reset()         { *m = GetGuildDonateCountReq{} }
func (m *GetGuildDonateCountReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildDonateCountReq) ProtoMessage()    {}
func (*GetGuildDonateCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{35}
}
func (m *GetGuildDonateCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDonateCountReq.Unmarshal(m, b)
}
func (m *GetGuildDonateCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDonateCountReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildDonateCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDonateCountReq.Merge(dst, src)
}
func (m *GetGuildDonateCountReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildDonateCountReq.Size(m)
}
func (m *GetGuildDonateCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDonateCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDonateCountReq proto.InternalMessageInfo

func (m *GetGuildDonateCountReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildDonateCountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDonateCountResp) Reset()         { *m = GetGuildDonateCountResp{} }
func (m *GetGuildDonateCountResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildDonateCountResp) ProtoMessage()    {}
func (*GetGuildDonateCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_go_2abc163ba3835b19, []int{36}
}
func (m *GetGuildDonateCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDonateCountResp.Unmarshal(m, b)
}
func (m *GetGuildDonateCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDonateCountResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildDonateCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDonateCountResp.Merge(dst, src)
}
func (m *GetGuildDonateCountResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildDonateCountResp.Size(m)
}
func (m *GetGuildDonateCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDonateCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDonateCountResp proto.InternalMessageInfo

func (m *GetGuildDonateCountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func init() {
	proto.RegisterType((*GuildInfo)(nil), "guild_go.GuildInfo")
	proto.RegisterType((*GetGuildByIdReq)(nil), "guild_go.GetGuildByIdReq")
	proto.RegisterType((*GetGuildByIdResp)(nil), "guild_go.GetGuildByIdResp")
	proto.RegisterType((*BatchGetGuildByIdReq)(nil), "guild_go.BatchGetGuildByIdReq")
	proto.RegisterType((*BatchGetGuildByIdResp)(nil), "guild_go.BatchGetGuildByIdResp")
	proto.RegisterType((*GetGuildMemberReq)(nil), "guild_go.GetGuildMemberReq")
	proto.RegisterType((*GuildMemberResp)(nil), "guild_go.GuildMemberResp")
	proto.RegisterType((*GetGuildBulletinListReq)(nil), "guild_go.GetGuildBulletinListReq")
	proto.RegisterType((*GuildBulletin)(nil), "guild_go.GuildBulletin")
	proto.RegisterType((*GetGuildBulletinListResp)(nil), "guild_go.GetGuildBulletinListResp")
	proto.RegisterType((*HasGuildPermissionReq)(nil), "guild_go.HasGuildPermissionReq")
	proto.RegisterType((*HasGuildPermissionResp)(nil), "guild_go.HasGuildPermissionResp")
	proto.RegisterType((*GuildOfficialInfo)(nil), "guild_go.GuildOfficialInfo")
	proto.RegisterType((*GetGuildOfficialByUidReq)(nil), "guild_go.GetGuildOfficialByUidReq")
	proto.RegisterType((*GetGuildOfficialByUidResp)(nil), "guild_go.GetGuildOfficialByUidResp")
	proto.RegisterType((*BatchUpdateGameGuildStarLevelReq)(nil), "guild_go.BatchUpdateGameGuildStarLevelReq")
	proto.RegisterMapType((map[uint32]uint32)(nil), "guild_go.BatchUpdateGameGuildStarLevelReq.GuildLevelEntry")
	proto.RegisterType((*BatchUpdateGameGuildStarLevelResp)(nil), "guild_go.BatchUpdateGameGuildStarLevelResp")
	proto.RegisterType((*GetGuildGameCountConfigReq)(nil), "guild_go.GetGuildGameCountConfigReq")
	proto.RegisterType((*GetGuildGameCountConfigResp)(nil), "guild_go.GetGuildGameCountConfigResp")
	proto.RegisterType((*GetGuildExtraGameCountReq)(nil), "guild_go.GetGuildExtraGameCountReq")
	proto.RegisterType((*GetGuildExtraGameCountResp)(nil), "guild_go.GetGuildExtraGameCountResp")
	proto.RegisterType((*GetMemberRecentGameLoginReq)(nil), "guild_go.GetMemberRecentGameLoginReq")
	proto.RegisterType((*StMemberRecentLoginLyGame)(nil), "guild_go.StMemberRecentLoginLyGame")
	proto.RegisterType((*GetMemberRecentGameLoginResp)(nil), "guild_go.GetMemberRecentGameLoginResp")
	proto.RegisterType((*GuildCheckInInfo)(nil), "guild_go.GuildCheckInInfo")
	proto.RegisterType((*GetGuildCheckInReq)(nil), "guild_go.GetGuildCheckInReq")
	proto.RegisterType((*GetGuildCheckInResp)(nil), "guild_go.GetGuildCheckInResp")
	proto.RegisterType((*GetGuildCheckInListReq)(nil), "guild_go.GetGuildCheckInListReq")
	proto.RegisterType((*GetGuildCheckInListResp)(nil), "guild_go.GetGuildCheckInListResp")
	proto.RegisterType((*GetGuildCheckInCountReq)(nil), "guild_go.GetGuildCheckInCountReq")
	proto.RegisterType((*GetGuildCheckInCountResp)(nil), "guild_go.GetGuildCheckInCountResp")
	proto.RegisterType((*BatchGetGuildYesterdayCheckInCountReq)(nil), "guild_go.BatchGetGuildYesterdayCheckInCountReq")
	proto.RegisterType((*BatchGetGuildYesterdayCheckInCountResp)(nil), "guild_go.BatchGetGuildYesterdayCheckInCountResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "guild_go.BatchGetGuildYesterdayCheckInCountResp.GuildCountEntry")
	proto.RegisterType((*GetGuildDonateReq)(nil), "guild_go.GetGuildDonateReq")
	proto.RegisterType((*GetGuildDonateResp)(nil), "guild_go.GetGuildDonateResp")
	proto.RegisterType((*GetGuildDonateCountReq)(nil), "guild_go.GetGuildDonateCountReq")
	proto.RegisterType((*GetGuildDonateCountResp)(nil), "guild_go.GetGuildDonateCountResp")
	proto.RegisterEnum("guild_go.E_OPT_INVALID_TYPE", E_OPT_INVALID_TYPE_name, E_OPT_INVALID_TYPE_value)
	proto.RegisterEnum("guild_go.E_GUILD_CHECK_IN_RANK_TYPE", E_GUILD_CHECK_IN_RANK_TYPE_name, E_GUILD_CHECK_IN_RANK_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GuildGoClient is the client API for GuildGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GuildGoClient interface {
	GetGuildById(ctx context.Context, in *GetGuildByIdReq, opts ...grpc.CallOption) (*GetGuildByIdResp, error)
	BatchGetGuildById(ctx context.Context, in *BatchGetGuildByIdReq, opts ...grpc.CallOption) (*BatchGetGuildByIdResp, error)
	GetGuildMember(ctx context.Context, in *GetGuildMemberReq, opts ...grpc.CallOption) (*GuildMemberResp, error)
	GetGuildBulletinList(ctx context.Context, in *GetGuildBulletinListReq, opts ...grpc.CallOption) (*GetGuildBulletinListResp, error)
	HasGuildPermission(ctx context.Context, in *HasGuildPermissionReq, opts ...grpc.CallOption) (*HasGuildPermissionResp, error)
	// *** 官员相关 ***
	GetGuildOfficialByUid(ctx context.Context, in *GetGuildOfficialByUidReq, opts ...grpc.CallOption) (*GetGuildOfficialByUidResp, error)
	// *** 贡献相关 ***
	GetGuildCheckInList(ctx context.Context, in *GetGuildCheckInListReq, opts ...grpc.CallOption) (*GetGuildCheckInListResp, error)
	GetGuildCheckIn(ctx context.Context, in *GetGuildCheckInReq, opts ...grpc.CallOption) (*GetGuildCheckInResp, error)
	GetGuildCheckInCount(ctx context.Context, in *GetGuildCheckInCountReq, opts ...grpc.CallOption) (*GetGuildCheckInCountResp, error)
	BatchGetGuildYesterdayCheckInCount(ctx context.Context, in *BatchGetGuildYesterdayCheckInCountReq, opts ...grpc.CallOption) (*BatchGetGuildYesterdayCheckInCountResp, error)
	// *** 捐赠 ***
	GetGuildDonate(ctx context.Context, in *GetGuildDonateReq, opts ...grpc.CallOption) (*GetGuildDonateResp, error)
	GetGuildDonateCount(ctx context.Context, in *GetGuildDonateCountReq, opts ...grpc.CallOption) (*GetGuildDonateCountResp, error)
	// *** 游戏相关 ***
	BatchUpdateGameGuildStarLevel(ctx context.Context, in *BatchUpdateGameGuildStarLevelReq, opts ...grpc.CallOption) (*BatchUpdateGameGuildStarLevelResp, error)
	// 获取公会等级对应主打游戏数
	GetGuildGameCountConfig(ctx context.Context, in *GetGuildGameCountConfigReq, opts ...grpc.CallOption) (*GetGuildGameCountConfigResp, error)
	// 获取公会已扩充的游戏数
	GetGuildExtraGameCount(ctx context.Context, in *GetGuildExtraGameCountReq, opts ...grpc.CallOption) (*GetGuildExtraGameCountResp, error)
	GetMemberRecentGameLogin(ctx context.Context, in *GetMemberRecentGameLoginReq, opts ...grpc.CallOption) (*GetMemberRecentGameLoginResp, error)
}

type guildGoClient struct {
	cc *grpc.ClientConn
}

func NewGuildGoClient(cc *grpc.ClientConn) GuildGoClient {
	return &guildGoClient{cc}
}

func (c *guildGoClient) GetGuildById(ctx context.Context, in *GetGuildByIdReq, opts ...grpc.CallOption) (*GetGuildByIdResp, error) {
	out := new(GetGuildByIdResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) BatchGetGuildById(ctx context.Context, in *BatchGetGuildByIdReq, opts ...grpc.CallOption) (*BatchGetGuildByIdResp, error) {
	out := new(BatchGetGuildByIdResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/BatchGetGuildById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildMember(ctx context.Context, in *GetGuildMemberReq, opts ...grpc.CallOption) (*GuildMemberResp, error) {
	out := new(GuildMemberResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildBulletinList(ctx context.Context, in *GetGuildBulletinListReq, opts ...grpc.CallOption) (*GetGuildBulletinListResp, error) {
	out := new(GetGuildBulletinListResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildBulletinList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) HasGuildPermission(ctx context.Context, in *HasGuildPermissionReq, opts ...grpc.CallOption) (*HasGuildPermissionResp, error) {
	out := new(HasGuildPermissionResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/HasGuildPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildOfficialByUid(ctx context.Context, in *GetGuildOfficialByUidReq, opts ...grpc.CallOption) (*GetGuildOfficialByUidResp, error) {
	out := new(GetGuildOfficialByUidResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildOfficialByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildCheckInList(ctx context.Context, in *GetGuildCheckInListReq, opts ...grpc.CallOption) (*GetGuildCheckInListResp, error) {
	out := new(GetGuildCheckInListResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildCheckInList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildCheckIn(ctx context.Context, in *GetGuildCheckInReq, opts ...grpc.CallOption) (*GetGuildCheckInResp, error) {
	out := new(GetGuildCheckInResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildCheckInCount(ctx context.Context, in *GetGuildCheckInCountReq, opts ...grpc.CallOption) (*GetGuildCheckInCountResp, error) {
	out := new(GetGuildCheckInCountResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildCheckInCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) BatchGetGuildYesterdayCheckInCount(ctx context.Context, in *BatchGetGuildYesterdayCheckInCountReq, opts ...grpc.CallOption) (*BatchGetGuildYesterdayCheckInCountResp, error) {
	out := new(BatchGetGuildYesterdayCheckInCountResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/BatchGetGuildYesterdayCheckInCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildDonate(ctx context.Context, in *GetGuildDonateReq, opts ...grpc.CallOption) (*GetGuildDonateResp, error) {
	out := new(GetGuildDonateResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildDonate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildDonateCount(ctx context.Context, in *GetGuildDonateCountReq, opts ...grpc.CallOption) (*GetGuildDonateCountResp, error) {
	out := new(GetGuildDonateCountResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildDonateCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) BatchUpdateGameGuildStarLevel(ctx context.Context, in *BatchUpdateGameGuildStarLevelReq, opts ...grpc.CallOption) (*BatchUpdateGameGuildStarLevelResp, error) {
	out := new(BatchUpdateGameGuildStarLevelResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/BatchUpdateGameGuildStarLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildGameCountConfig(ctx context.Context, in *GetGuildGameCountConfigReq, opts ...grpc.CallOption) (*GetGuildGameCountConfigResp, error) {
	out := new(GetGuildGameCountConfigResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildGameCountConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetGuildExtraGameCount(ctx context.Context, in *GetGuildExtraGameCountReq, opts ...grpc.CallOption) (*GetGuildExtraGameCountResp, error) {
	out := new(GetGuildExtraGameCountResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetGuildExtraGameCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildGoClient) GetMemberRecentGameLogin(ctx context.Context, in *GetMemberRecentGameLoginReq, opts ...grpc.CallOption) (*GetMemberRecentGameLoginResp, error) {
	out := new(GetMemberRecentGameLoginResp)
	err := c.cc.Invoke(ctx, "/guild_go.GuildGo/GetMemberRecentGameLogin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GuildGoServer is the server API for GuildGo service.
type GuildGoServer interface {
	GetGuildById(context.Context, *GetGuildByIdReq) (*GetGuildByIdResp, error)
	BatchGetGuildById(context.Context, *BatchGetGuildByIdReq) (*BatchGetGuildByIdResp, error)
	GetGuildMember(context.Context, *GetGuildMemberReq) (*GuildMemberResp, error)
	GetGuildBulletinList(context.Context, *GetGuildBulletinListReq) (*GetGuildBulletinListResp, error)
	HasGuildPermission(context.Context, *HasGuildPermissionReq) (*HasGuildPermissionResp, error)
	// *** 官员相关 ***
	GetGuildOfficialByUid(context.Context, *GetGuildOfficialByUidReq) (*GetGuildOfficialByUidResp, error)
	// *** 贡献相关 ***
	GetGuildCheckInList(context.Context, *GetGuildCheckInListReq) (*GetGuildCheckInListResp, error)
	GetGuildCheckIn(context.Context, *GetGuildCheckInReq) (*GetGuildCheckInResp, error)
	GetGuildCheckInCount(context.Context, *GetGuildCheckInCountReq) (*GetGuildCheckInCountResp, error)
	BatchGetGuildYesterdayCheckInCount(context.Context, *BatchGetGuildYesterdayCheckInCountReq) (*BatchGetGuildYesterdayCheckInCountResp, error)
	// *** 捐赠 ***
	GetGuildDonate(context.Context, *GetGuildDonateReq) (*GetGuildDonateResp, error)
	GetGuildDonateCount(context.Context, *GetGuildDonateCountReq) (*GetGuildDonateCountResp, error)
	// *** 游戏相关 ***
	BatchUpdateGameGuildStarLevel(context.Context, *BatchUpdateGameGuildStarLevelReq) (*BatchUpdateGameGuildStarLevelResp, error)
	// 获取公会等级对应主打游戏数
	GetGuildGameCountConfig(context.Context, *GetGuildGameCountConfigReq) (*GetGuildGameCountConfigResp, error)
	// 获取公会已扩充的游戏数
	GetGuildExtraGameCount(context.Context, *GetGuildExtraGameCountReq) (*GetGuildExtraGameCountResp, error)
	GetMemberRecentGameLogin(context.Context, *GetMemberRecentGameLoginReq) (*GetMemberRecentGameLoginResp, error)
}

func RegisterGuildGoServer(s *grpc.Server, srv GuildGoServer) {
	s.RegisterService(&_GuildGo_serviceDesc, srv)
}

func _GuildGo_GetGuildById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildById(ctx, req.(*GetGuildByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_BatchGetGuildById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGuildByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).BatchGetGuildById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/BatchGetGuildById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).BatchGetGuildById(ctx, req.(*BatchGetGuildByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildMember(ctx, req.(*GetGuildMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildBulletinList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildBulletinListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildBulletinList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildBulletinList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildBulletinList(ctx, req.(*GetGuildBulletinListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_HasGuildPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HasGuildPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).HasGuildPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/HasGuildPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).HasGuildPermission(ctx, req.(*HasGuildPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildOfficialByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildOfficialByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildOfficialByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildOfficialByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildOfficialByUid(ctx, req.(*GetGuildOfficialByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildCheckInList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildCheckInListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildCheckInList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildCheckInList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildCheckInList(ctx, req.(*GetGuildCheckInListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildCheckInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildCheckIn(ctx, req.(*GetGuildCheckInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildCheckInCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildCheckInCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildCheckInCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildCheckInCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildCheckInCount(ctx, req.(*GetGuildCheckInCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_BatchGetGuildYesterdayCheckInCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGuildYesterdayCheckInCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).BatchGetGuildYesterdayCheckInCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/BatchGetGuildYesterdayCheckInCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).BatchGetGuildYesterdayCheckInCount(ctx, req.(*BatchGetGuildYesterdayCheckInCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildDonate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDonateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildDonate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildDonate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildDonate(ctx, req.(*GetGuildDonateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildDonateCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDonateCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildDonateCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildDonateCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildDonateCount(ctx, req.(*GetGuildDonateCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_BatchUpdateGameGuildStarLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpdateGameGuildStarLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).BatchUpdateGameGuildStarLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/BatchUpdateGameGuildStarLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).BatchUpdateGameGuildStarLevel(ctx, req.(*BatchUpdateGameGuildStarLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildGameCountConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildGameCountConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildGameCountConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildGameCountConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildGameCountConfig(ctx, req.(*GetGuildGameCountConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetGuildExtraGameCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildExtraGameCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetGuildExtraGameCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetGuildExtraGameCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetGuildExtraGameCount(ctx, req.(*GetGuildExtraGameCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildGo_GetMemberRecentGameLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberRecentGameLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildGoServer).GetMemberRecentGameLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_go.GuildGo/GetMemberRecentGameLogin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildGoServer).GetMemberRecentGameLogin(ctx, req.(*GetMemberRecentGameLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GuildGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "guild_go.GuildGo",
	HandlerType: (*GuildGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGuildById",
			Handler:    _GuildGo_GetGuildById_Handler,
		},
		{
			MethodName: "BatchGetGuildById",
			Handler:    _GuildGo_BatchGetGuildById_Handler,
		},
		{
			MethodName: "GetGuildMember",
			Handler:    _GuildGo_GetGuildMember_Handler,
		},
		{
			MethodName: "GetGuildBulletinList",
			Handler:    _GuildGo_GetGuildBulletinList_Handler,
		},
		{
			MethodName: "HasGuildPermission",
			Handler:    _GuildGo_HasGuildPermission_Handler,
		},
		{
			MethodName: "GetGuildOfficialByUid",
			Handler:    _GuildGo_GetGuildOfficialByUid_Handler,
		},
		{
			MethodName: "GetGuildCheckInList",
			Handler:    _GuildGo_GetGuildCheckInList_Handler,
		},
		{
			MethodName: "GetGuildCheckIn",
			Handler:    _GuildGo_GetGuildCheckIn_Handler,
		},
		{
			MethodName: "GetGuildCheckInCount",
			Handler:    _GuildGo_GetGuildCheckInCount_Handler,
		},
		{
			MethodName: "BatchGetGuildYesterdayCheckInCount",
			Handler:    _GuildGo_BatchGetGuildYesterdayCheckInCount_Handler,
		},
		{
			MethodName: "GetGuildDonate",
			Handler:    _GuildGo_GetGuildDonate_Handler,
		},
		{
			MethodName: "GetGuildDonateCount",
			Handler:    _GuildGo_GetGuildDonateCount_Handler,
		},
		{
			MethodName: "BatchUpdateGameGuildStarLevel",
			Handler:    _GuildGo_BatchUpdateGameGuildStarLevel_Handler,
		},
		{
			MethodName: "GetGuildGameCountConfig",
			Handler:    _GuildGo_GetGuildGameCountConfig_Handler,
		},
		{
			MethodName: "GetGuildExtraGameCount",
			Handler:    _GuildGo_GetGuildExtraGameCount_Handler,
		},
		{
			MethodName: "GetMemberRecentGameLogin",
			Handler:    _GuildGo_GetMemberRecentGameLogin_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/guild/guild-go.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/guild/guild-go.proto", fileDescriptor_guild_go_2abc163ba3835b19)
}

var fileDescriptor_guild_go_2abc163ba3835b19 = []byte{
	// 1979 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x38, 0xdd, 0x52, 0x1b, 0xc9,
	0xd5, 0x08, 0xb1, 0x20, 0x1d, 0x24, 0x2c, 0xda, 0x36, 0x1e, 0x06, 0xbc, 0xc6, 0x83, 0x77, 0xcd,
	0x87, 0x77, 0xc1, 0xc5, 0x7e, 0xf9, 0xdb, 0x54, 0xaa, 0x56, 0x80, 0x8a, 0xd5, 0x5a, 0xc6, 0xd4,
	0x2c, 0xa6, 0xca, 0x49, 0xb6, 0xa6, 0x06, 0x4d, 0x4b, 0x4c, 0x18, 0xcd, 0x8c, 0xa7, 0x5b, 0xac,
	0x95, 0x8b, 0xdc, 0xe7, 0x3a, 0x77, 0x79, 0x86, 0x3c, 0x43, 0x1e, 0x20, 0x2f, 0x90, 0xd7, 0xc8,
	0x4d, 0xee, 0x53, 0x7d, 0xba, 0xe7, 0x57, 0x12, 0x82, 0x1b, 0xd5, 0x9c, 0x9f, 0x3e, 0x7f, 0x7d,
	0xfa, 0xfc, 0x08, 0xb6, 0x39, 0xdf, 0xff, 0x38, 0x74, 0xbb, 0xd7, 0xcc, 0xf5, 0x6e, 0x68, 0xb4,
	0xdf, 0x1f, 0xba, 0x9e, 0x23, 0x7f, 0xbf, 0xee, 0x07, 0x7b, 0x61, 0x14, 0xf0, 0x80, 0x54, 0x10,
	0xb6, 0xfa, 0x81, 0xf1, 0xdf, 0x32, 0x54, 0x4f, 0x04, 0xd0, 0xf6, 0x7b, 0x01, 0x59, 0x07, 0x45,
	0x71, 0x1d, 0xad, 0xb4, 0x55, 0xda, 0xa9, 0x9b, 0x4b, 0x08, 0xb7, 0x1d, 0x41, 0x62, 0x57, 0x41,
	0xc4, 0x05, 0x69, 0x5e, 0x92, 0x10, 0x6e, 0x3b, 0x84, 0xc0, 0x82, 0x6f, 0x0f, 0xa8, 0x56, 0xde,
	0x2a, 0xed, 0x54, 0x4d, 0xfc, 0x26, 0x8f, 0xe0, 0x33, 0xd7, 0xe7, 0x51, 0xa0, 0x2d, 0x20, 0x52,
	0x02, 0xe4, 0x39, 0xd4, 0x06, 0x74, 0x70, 0x49, 0x23, 0xab, 0x1b, 0x0c, 0x7d, 0xae, 0x7d, 0x86,
	0x82, 0x96, 0x25, 0xee, 0x48, 0xa0, 0xc8, 0x53, 0x80, 0x81, 0xed, 0xfa, 0x56, 0x3f, 0x0a, 0x86,
	0xa1, 0xb6, 0x88, 0x0c, 0x55, 0x81, 0x39, 0x11, 0x08, 0xa2, 0xc1, 0x52, 0x37, 0xa2, 0x36, 0x0f,
	0x22, 0x6d, 0x69, 0xab, 0xb4, 0xb3, 0x60, 0xc6, 0xa0, 0xd0, 0x18, 0xfc, 0xec, 0xd3, 0x48, 0xab,
	0x20, 0x5e, 0x02, 0x44, 0x87, 0xca, 0xe5, 0x90, 0xb9, 0x3e, 0x65, 0x4c, 0xab, 0x6e, 0x95, 0x76,
	0x2a, 0x66, 0x02, 0x0b, 0x55, 0x78, 0x98, 0x3a, 0x96, 0xcd, 0x35, 0xd8, 0x2a, 0xed, 0x94, 0xcd,
	0xaa, 0xc2, 0x34, 0xd1, 0x92, 0xbe, 0x3d, 0xa0, 0xca, 0xd4, 0x65, 0x69, 0x89, 0xc0, 0x48, 0x43,
	0xd7, 0x60, 0x31, 0x8c, 0x68, 0xcf, 0xfd, 0xa4, 0xd5, 0xd0, 0x45, 0x05, 0x91, 0x4d, 0xa8, 0x0e,
	0x6c, 0xdf, 0xed, 0x51, 0xc6, 0x03, 0xad, 0x8e, 0xa4, 0x14, 0x41, 0x9e, 0xc1, 0xb2, 0x4f, 0xa9,
	0x63, 0xdd, 0xd0, 0xc8, 0xed, 0x8d, 0xb4, 0x15, 0x34, 0x09, 0x04, 0xea, 0x02, 0x31, 0xc4, 0x80,
	0xba, 0xba, 0x1c, 0xa1, 0xdb, 0xbb, 0xd1, 0x1e, 0xc8, 0x18, 0x21, 0xf2, 0xc4, 0x1e, 0xd0, 0xce,
	0x0d, 0xd9, 0x80, 0xaa, 0xcb, 0x2c, 0x87, 0x7a, 0x94, 0x53, 0xad, 0x81, 0xf4, 0x8a, 0xcb, 0x8e,
	0x11, 0x26, 0x2f, 0x60, 0x25, 0xa3, 0xc1, 0xba, 0x39, 0xd0, 0x56, 0x91, 0xa3, 0x96, 0x2a, 0xb9,
	0x38, 0x30, 0x5a, 0xf0, 0xe0, 0x84, 0x72, 0xbc, 0xf9, 0xc3, 0x51, 0xdb, 0x31, 0xe9, 0xc7, 0xdb,
	0x2e, 0xff, 0x09, 0x2c, 0xf5, 0x29, 0xb7, 0x6c, 0xcf, 0xc3, 0xbb, 0xaf, 0x98, 0x8b, 0x7d, 0xca,
	0x9b, 0x9e, 0x67, 0xfc, 0x16, 0x1a, 0x79, 0x31, 0x2c, 0x24, 0x2f, 0x61, 0xc1, 0xf5, 0x7b, 0x01,
	0xca, 0x58, 0x3e, 0x78, 0xb8, 0x17, 0xe7, 0xda, 0x5e, 0x92, 0x67, 0x26, 0x32, 0x18, 0x1d, 0x78,
	0x74, 0x68, 0xf3, 0xee, 0x55, 0xd1, 0x90, 0x0d, 0xa8, 0xc6, 0x86, 0x30, 0xad, 0xb4, 0x55, 0x16,
	0xee, 0x29, 0x4b, 0xd8, 0x74, 0x53, 0x7e, 0x82, 0xc7, 0x13, 0xa4, 0x49, 0x7b, 0x3c, 0x97, 0x71,
	0x94, 0x34, 0xcd, 0x1e, 0xc1, 0x20, 0xf4, 0xfa, 0x01, 0xb7, 0xe8, 0x27, 0xc1, 0x3d, 0x2f, 0xf5,
	0xfa, 0x01, 0x6f, 0x09, 0xd8, 0xf8, 0x0e, 0x56, 0x63, 0xc9, 0x6f, 0x31, 0x5d, 0x85, 0xa5, 0x0d,
	0x28, 0x0f, 0x55, 0xb4, 0x16, 0x4c, 0xf1, 0x99, 0x0b, 0xe2, 0x7c, 0x2e, 0x88, 0xc6, 0xbf, 0x4b,
	0xf0, 0x20, 0x77, 0x9e, 0x85, 0x13, 0x04, 0x10, 0x58, 0x88, 0x02, 0x8f, 0xaa, 0xc3, 0xf8, 0x2d,
	0x52, 0x2d, 0xa2, 0x03, 0x3b, 0xba, 0x56, 0x4f, 0x4c, 0x41, 0x02, 0xff, 0x33, 0x75, 0xfb, 0x57,
	0x1c, 0x5f, 0x59, 0xdd, 0x54, 0x10, 0xf9, 0x1c, 0x20, 0xa4, 0xd1, 0xc0, 0x65, 0xcc, 0x0d, 0x7c,
	0xf5, 0xc8, 0x32, 0x18, 0x11, 0xc3, 0x3f, 0x05, 0xae, 0x2f, 0xb2, 0x7e, 0x11, 0xb3, 0x7e, 0x51,
	0x80, 0x4d, 0x2e, 0xde, 0x10, 0x77, 0xb9, 0x47, 0xf1, 0x6d, 0x55, 0x4d, 0x09, 0x88, 0x9c, 0x0d,
	0x42, 0x6e, 0xb9, 0xfe, 0x8d, 0xed, 0xb9, 0x0e, 0xbe, 0xaf, 0xba, 0x09, 0x41, 0xc8, 0xdb, 0x12,
	0x63, 0x5c, 0xc2, 0x93, 0x24, 0xea, 0x43, 0xcf, 0xa3, 0xdc, 0xf5, 0x3b, 0x2e, 0xe3, 0x33, 0x92,
	0x6a, 0x0d, 0x16, 0x83, 0x5e, 0x8f, 0x51, 0xae, 0x7c, 0x55, 0x90, 0x30, 0xc2, 0x73, 0x07, 0x2e,
	0x47, 0x67, 0xeb, 0xa6, 0x04, 0x8c, 0xff, 0x94, 0xa0, 0x9e, 0xd3, 0x70, 0x9b, 0xe8, 0x67, 0xb0,
	0x7c, 0xa9, 0xd8, 0xd2, 0x8b, 0x80, 0x18, 0xd5, 0x76, 0xe2, 0xb8, 0x97, 0xd3, 0xb8, 0x27, 0xae,
	0x2f, 0x64, 0x5d, 0x17, 0xe5, 0x26, 0xf0, 0x39, 0x55, 0xb5, 0xaa, 0x6a, 0xc6, 0x20, 0xd9, 0x86,
	0x7a, 0xa2, 0x82, 0x8f, 0x42, 0xaa, 0x4a, 0x55, 0x2d, 0x46, 0x9e, 0x8f, 0x42, 0x5a, 0xa8, 0x30,
	0x4b, 0x13, 0x2a, 0xcc, 0x30, 0x74, 0x62, 0x72, 0x45, 0x92, 0x15, 0xa6, 0xc9, 0x8d, 0x9f, 0x40,
	0x9b, 0x1c, 0x56, 0x16, 0x92, 0x57, 0xb9, 0xa4, 0x7e, 0x52, 0x48, 0xea, 0x98, 0x5d, 0x25, 0xb6,
	0xf0, 0x2d, 0xe0, 0xb6, 0xa7, 0x02, 0x21, 0x01, 0xc3, 0x81, 0xc7, 0xdf, 0xdb, 0x0c, 0xf9, 0xcf,
	0x92, 0xdc, 0x98, 0x71, 0x67, 0x2a, 0x6e, 0xf3, 0x69, 0xdc, 0xf2, 0xb9, 0x56, 0x2e, 0xe6, 0x9a,
	0xb1, 0x0b, 0x6b, 0x93, 0xb4, 0xc8, 0xdc, 0xbf, 0xb2, 0x19, 0x6a, 0xa8, 0x98, 0xe2, 0xd3, 0x18,
	0xc1, 0x2a, 0x32, 0xbe, 0xeb, 0xf5, 0xdc, 0xae, 0x6b, 0x7b, 0xd8, 0x93, 0x44, 0xf6, 0x29, 0x38,
	0x35, 0x08, 0x62, 0x54, 0xdb, 0x11, 0x37, 0x91, 0x30, 0x60, 0x1f, 0x9a, 0xc7, 0x9b, 0xaa, 0xc5,
	0xc8, 0x53, 0xd1, 0x8f, 0x66, 0x99, 0x79, 0x92, 0xc6, 0x3a, 0xd6, 0x7e, 0x38, 0x7a, 0xef, 0x3a,
	0xf7, 0x8d, 0x87, 0xd1, 0x81, 0xf5, 0x29, 0x82, 0x58, 0x48, 0xf6, 0x73, 0xa5, 0x71, 0xa3, 0x70,
	0x6b, 0x59, 0xb7, 0x55, 0x89, 0xfc, 0x67, 0x09, 0xb6, 0xb0, 0xaa, 0xbd, 0xc7, 0xac, 0x10, 0xf5,
	0x1f, 0x59, 0x7f, 0xe4, 0x76, 0xd4, 0xa1, 0x37, 0xd4, 0x13, 0xf6, 0xfd, 0x01, 0x64, 0x77, 0xb0,
	0x3c, 0x81, 0x51, 0x29, 0xf1, 0x6d, 0x2a, 0x7c, 0x96, 0x00, 0xa9, 0x1d, 0xa1, 0x96, 0xcf, 0xa3,
	0x91, 0x09, 0xfd, 0x04, 0xa1, 0xff, 0x4e, 0x15, 0xad, 0x94, 0x2c, 0x9c, 0xbe, 0xa6, 0x23, 0x15,
	0x0a, 0xf1, 0x29, 0x12, 0xec, 0xc6, 0xf6, 0x86, 0x71, 0xd5, 0x92, 0xc0, 0xb7, 0xf3, 0xbf, 0x2e,
	0x19, 0xdb, 0xf0, 0x7c, 0x86, 0x7a, 0x16, 0x1a, 0x07, 0xa0, 0xc7, 0x31, 0x3b, 0x89, 0xfb, 0xeb,
	0x51, 0xe0, 0xf7, 0xdc, 0xbe, 0x70, 0x4f, 0xd4, 0x03, 0xe5, 0x98, 0xac, 0x07, 0x02, 0x30, 0x18,
	0x6c, 0x4c, 0x3d, 0xc3, 0xc2, 0xc9, 0x87, 0x04, 0x56, 0x76, 0x73, 0x65, 0x27, 0x02, 0x02, 0x4b,
	0x3f, 0xf1, 0xc8, 0x8e, 0x0b, 0x0e, 0x02, 0xa2, 0x10, 0x77, 0x03, 0x16, 0x97, 0x56, 0xfc, 0x36,
	0x7e, 0x99, 0x5e, 0x6e, 0x4b, 0x30, 0x25, 0x9a, 0x6f, 0x4f, 0x93, 0xac, 0x83, 0xc5, 0x73, 0xd2,
	0x56, 0x69, 0x55, 0x29, 0x63, 0x95, 0xf1, 0x03, 0x3a, 0x18, 0xf7, 0x8a, 0x2e, 0xf5, 0x39, 0x76,
	0xff, 0xa0, 0xef, 0xde, 0xfb, 0x91, 0x1a, 0xe7, 0xb0, 0xfe, 0x63, 0x4e, 0x14, 0x8a, 0xe9, 0x8c,
	0x84, 0x44, 0xb2, 0x09, 0xe0, 0x8d, 0xe4, 0xb8, 0x91, 0xc8, 0xaa, 0x78, 0x48, 0x93, 0x73, 0x9f,
	0x27, 0x98, 0x2d, 0xce, 0x50, 0x62, 0xd9, 0x5c, 0x42, 0xf8, 0x9c, 0x19, 0x97, 0xb0, 0x39, 0xdd,
	0x42, 0x16, 0x92, 0x43, 0x00, 0x79, 0x54, 0xc8, 0x56, 0x39, 0xbf, 0x9d, 0xa6, 0xe5, 0x54, 0x8b,
	0xcc, 0x2a, 0x1e, 0x13, 0x9f, 0xc6, 0x3f, 0xca, 0xd0, 0xc0, 0xb8, 0x1d, 0x5d, 0xd1, 0xee, 0x75,
	0xdb, 0x9f, 0x35, 0xa6, 0x8e, 0x17, 0xa8, 0x97, 0xd0, 0xf0, 0x6c, 0xc6, 0xad, 0xae, 0x10, 0x60,
	0xc9, 0xae, 0x57, 0x46, 0x47, 0xea, 0x02, 0xaf, 0xe4, 0x36, 0xb9, 0x18, 0x9c, 0x12, 0x1e, 0x79,
	0x1f, 0xf2, 0xea, 0x6b, 0x5d, 0xc9, 0x22, 0xc7, 0xbe, 0x97, 0xf0, 0x80, 0x0d, 0xc3, 0xd0, 0xa3,
	0x03, 0xea, 0x73, 0xcb, 0xb1, 0x47, 0x4c, 0x35, 0xd8, 0x95, 0x14, 0x7d, 0x6c, 0x8f, 0x18, 0xf9,
	0x3f, 0x68, 0x64, 0x18, 0xc3, 0xc8, 0xed, 0xc6, 0x3d, 0x22, 0x23, 0xe0, 0x4c, 0xa0, 0xc9, 0x2b,
	0x58, 0x4d, 0x51, 0xd4, 0x91, 0x52, 0x97, 0x90, 0xb7, 0x91, 0x25, 0xa0, 0xdc, 0xdf, 0xc0, 0xba,
	0xdd, 0xed, 0x0e, 0x07, 0x43, 0xcf, 0xe6, 0xd4, 0x2a, 0x58, 0x2c, 0x7b, 0xf3, 0x5a, 0xca, 0x70,
	0x94, 0xb5, 0x7d, 0x0f, 0x1e, 0xe6, 0x6d, 0xb7, 0xb0, 0x87, 0x54, 0x71, 0xd4, 0x59, 0xcd, 0xd9,
	0x2f, 0x1a, 0x0d, 0x79, 0x0d, 0x8f, 0x23, 0x6a, 0x7b, 0xd6, 0x58, 0xfc, 0xe4, 0xac, 0xbc, 0x2a,
	0x88, 0x9d, 0x6c, 0x0c, 0x8d, 0x26, 0x90, 0x38, 0xd1, 0x15, 0xf2, 0xde, 0xb9, 0xda, 0x82, 0x87,
	0x63, 0x22, 0x58, 0x48, 0xf6, 0x72, 0xa5, 0x53, 0x2f, 0x94, 0xce, 0x4c, 0x76, 0xa8, 0xca, 0xf9,
	0x17, 0x58, 0x2b, 0x88, 0xb9, 0xc3, 0x48, 0x92, 0x8c, 0x1e, 0xf3, 0x99, 0xd1, 0x23, 0x33, 0xa8,
	0x94, 0x73, 0x83, 0xca, 0x06, 0x54, 0x23, 0xdb, 0xbf, 0x96, 0xed, 0x5f, 0xe6, 0x4a, 0x45, 0x20,
	0x44, 0xeb, 0x37, 0xda, 0xe9, 0x4c, 0x94, 0xd3, 0x2f, 0x5d, 0xc9, 0xf4, 0xee, 0x5b, 0x5d, 0x11,
	0x7c, 0xc6, 0xff, 0x8f, 0x89, 0xba, 0x4b, 0xcd, 0x79, 0x9d, 0x76, 0xb4, 0xfc, 0xa9, 0xa9, 0x15,
	0xe7, 0x0d, 0x7c, 0x91, 0x9b, 0xa0, 0x3f, 0x50, 0xc6, 0x69, 0xe4, 0xd8, 0xa3, 0xa2, 0xd6, 0x64,
	0x47, 0x71, 0x1d, 0x2b, 0xf1, 0x24, 0xde, 0x51, 0xda, 0x8e, 0x70, 0xd4, 0xf8, 0x57, 0x09, 0xbe,
	0xbc, 0x8b, 0x34, 0x16, 0x12, 0x3b, 0xee, 0x5f, 0xb1, 0x4d, 0x22, 0x2c, 0xdf, 0x15, 0xfa, 0xd7,
	0x4c, 0x31, 0x2a, 0x7a, 0x02, 0xcc, 0x76, 0x31, 0x44, 0x24, 0x5d, 0x2c, 0x25, 0xdf, 0xab, 0x8b,
	0x65, 0x86, 0xff, 0xe3, 0xc0, 0xb7, 0x39, 0xbd, 0x77, 0x56, 0xff, 0x6d, 0x3e, 0x7d, 0x19, 0xb1,
	0x08, 0x16, 0xde, 0xaf, 0x92, 0xbd, 0x80, 0x15, 0x7c, 0x89, 0x0e, 0x9e, 0x4f, 0xeb, 0x58, 0x4d,
	0x60, 0xa5, 0xd0, 0x26, 0x17, 0x3b, 0xb6, 0x62, 0xc8, 0x16, 0xb1, 0x65, 0x89, 0x93, 0x75, 0xe0,
	0x2b, 0x20, 0x58, 0x21, 0x62, 0x49, 0xd2, 0x6b, 0x59, 0xc6, 0x1a, 0x48, 0x91, 0xd2, 0x2e, 0x04,
	0x9e, 0xec, 0xc2, 0x6a, 0x56, 0xad, 0x64, 0x56, 0x95, 0x2c, 0xd5, 0x2c, 0x79, 0xbf, 0x86, 0x87,
	0x69, 0xc5, 0x48, 0xed, 0x94, 0x93, 0x6f, 0x23, 0xae, 0x17, 0xb1, 0xad, 0xc6, 0x37, 0xe9, 0x23,
	0x3d, 0x4e, 0xed, 0x9b, 0x91, 0xd8, 0xfb, 0xe9, 0x73, 0xc8, 0x1d, 0x9a, 0x96, 0xd7, 0xbb, 0xbf,
	0x02, 0xd2, 0xb2, 0xde, 0x9d, 0x9d, 0x5b, 0xed, 0xd3, 0x8b, 0x66, 0xa7, 0x7d, 0x6c, 0x9d, 0x7f,
	0x38, 0x6b, 0x91, 0x25, 0x28, 0x9f, 0xb6, 0x3b, 0x8d, 0x39, 0x52, 0x83, 0xca, 0xd1, 0xf7, 0xad,
	0xa3, 0x37, 0x56, 0xfb, 0xb4, 0x51, 0x22, 0x00, 0x8b, 0xc7, 0xef, 0x4e, 0x9b, 0xe7, 0xad, 0xc6,
	0xfc, 0xee, 0x11, 0xe8, 0x2d, 0xeb, 0xe4, 0x7d, 0xbb, 0x73, 0x6c, 0xc5, 0x1c, 0x96, 0xd9, 0x3c,
	0x7d, 0x23, 0x05, 0xac, 0x42, 0x3d, 0xc1, 0x9e, 0xb7, 0xdf, 0xb6, 0x1a, 0x73, 0x84, 0xc0, 0x4a,
	0x82, 0x3a, 0x7a, 0xf7, 0xfe, 0xf4, 0xbc, 0x51, 0x3a, 0xf8, 0x7b, 0x0d, 0x96, 0xe4, 0x98, 0x12,
	0x90, 0x13, 0xa8, 0x65, 0xd7, 0x53, 0xb2, 0x9e, 0x79, 0xfb, 0xf9, 0x25, 0x58, 0xd7, 0xa7, 0x91,
	0x58, 0x68, 0xcc, 0x91, 0x0b, 0x58, 0x1d, 0x5b, 0x76, 0xc9, 0xe7, 0x53, 0x9e, 0x4c, 0x2c, 0xf2,
	0xd9, 0xad, 0x74, 0x94, 0xfb, 0x03, 0xac, 0xe4, 0xb7, 0x5c, 0xb2, 0x31, 0x6e, 0x47, 0xb2, 0xff,
	0xea, 0xeb, 0x85, 0xda, 0x95, 0x6e, 0xb6, 0xc6, 0x1c, 0xb1, 0xe0, 0xd1, 0xa4, 0xf5, 0x85, 0x3c,
	0x9f, 0xe0, 0x59, 0x7e, 0x6b, 0xd4, 0x8d, 0x59, 0x2c, 0xa8, 0xe0, 0x03, 0x90, 0xf1, 0xd5, 0x82,
	0x64, 0xbc, 0x9c, 0xb8, 0xde, 0xe8, 0x5b, 0xb7, 0x33, 0xa0, 0xe8, 0x4b, 0x78, 0x3c, 0x71, 0x8a,
	0x27, 0x13, 0x2c, 0x2b, 0xee, 0x0b, 0xfa, 0xf6, 0x4c, 0x1e, 0xd4, 0xf1, 0xc7, 0xb1, 0x46, 0x87,
	0xe1, 0xd9, 0x1a, 0x3f, 0x9d, 0x6f, 0x60, 0xfa, 0xf3, 0x19, 0x1c, 0x28, 0xfd, 0x2c, 0xfd, 0x83,
	0x47, 0x11, 0xc9, 0xe6, 0xd4, 0x73, 0x42, 0xea, 0xd3, 0x5b, 0xa8, 0xc5, 0xfb, 0xcc, 0x4d, 0x15,
	0xd3, 0xcd, 0x89, 0x5f, 0xf3, 0xa4, 0xfb, 0x2c, 0x96, 0x6f, 0x63, 0x8e, 0xfc, 0xb5, 0x04, 0xc6,
	0xec, 0x5a, 0x4f, 0xf6, 0xef, 0xd7, 0x19, 0x3e, 0xea, 0xaf, 0xef, 0xdb, 0x4a, 0x8c, 0x39, 0xf2,
	0x36, 0x7d, 0x08, 0xb2, 0xc8, 0x4c, 0x7a, 0x08, 0x49, 0x2f, 0xd0, 0x37, 0xa7, 0x13, 0x8b, 0x77,
	0x9d, 0xa9, 0x59, 0x93, 0xee, 0x3a, 0x5f, 0x07, 0x27, 0xdd, 0x75, 0xa1, 0xe8, 0x19, 0x73, 0xe4,
	0xcf, 0xf0, 0xf4, 0xd6, 0x25, 0x8b, 0xec, 0xde, 0x7d, 0x19, 0xd4, 0x5f, 0xdd, 0x99, 0x17, 0x75,
	0x5f, 0xa5, 0xd5, 0xb8, 0xb0, 0x87, 0x91, 0x17, 0xe3, 0xb6, 0x8f, 0xaf, 0x77, 0xfa, 0x17, 0x77,
	0xe0, 0x42, 0x4d, 0x34, 0x6d, 0x16, 0xf9, 0x25, 0x8a, 0x4c, 0x78, 0x70, 0x63, 0xeb, 0x99, 0xfe,
	0x62, 0x36, 0x13, 0xaa, 0xb9, 0xc6, 0xb9, 0x69, 0xe2, 0x56, 0x43, 0xf2, 0xb6, 0x4e, 0xdb, 0xcd,
	0xf4, 0x2f, 0xef, 0xc2, 0x26, 0x94, 0x1d, 0x7e, 0xf5, 0xfb, 0xdd, 0x7e, 0xe0, 0xd9, 0x7e, 0x7f,
	0xef, 0x17, 0x07, 0x9c, 0xef, 0x75, 0x83, 0xc1, 0x3e, 0xfe, 0x43, 0xdf, 0x0d, 0xbc, 0x7d, 0x46,
	0xa3, 0x1b, 0xb7, 0x4b, 0x59, 0xf2, 0xe7, 0xfd, 0xe5, 0x22, 0xd2, 0xbe, 0xf9, 0x5f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0xb0, 0x8c, 0xb9, 0x08, 0xe4, 0x17, 0x00, 0x00,
}
