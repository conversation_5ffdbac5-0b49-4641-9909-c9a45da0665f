// Code generated by protoc-gen-go. DO NOT EDIT.
// source: token-v2/token-v2.proto

package token_v2 // import "golang.52tt.com/protocol/services/token.v2"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TokenType int32

const (
	TokenType_UNDEFINED     TokenType = 0
	TokenType_ACCESS_TOKEN  TokenType = 1
	TokenType_REFRESH_TOKEN TokenType = 2
)

var TokenType_name = map[int32]string{
	0: "UNDEFINED",
	1: "ACCESS_TOKEN",
	2: "REFRESH_TOKEN",
}
var TokenType_value = map[string]int32{
	"UNDEFINED":     0,
	"ACCESS_TOKEN":  1,
	"REFRESH_TOKEN": 2,
}

func (x TokenType) String() string {
	return proto.EnumName(TokenType_name, int32(x))
}
func (TokenType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{0}
}

type TokenBizType int32

const (
	TokenBizType_TokenBiz_UNKNOWN TokenBizType = 0
	TokenBizType_TokenBiz_SDK     TokenBizType = 1
	TokenBizType_TokenBiz_TT_WEB  TokenBizType = 2
)

var TokenBizType_name = map[int32]string{
	0: "TokenBiz_UNKNOWN",
	1: "TokenBiz_SDK",
	2: "TokenBiz_TT_WEB",
}
var TokenBizType_value = map[string]int32{
	"TokenBiz_UNKNOWN": 0,
	"TokenBiz_SDK":     1,
	"TokenBiz_TT_WEB":  2,
}

func (x TokenBizType) String() string {
	return proto.EnumName(TokenBizType_name, int32(x))
}
func (TokenBizType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{1}
}

type WechatTokenReq struct {
	WxAppId              string   `protobuf:"bytes,1,opt,name=wx_app_id,json=wxAppId,proto3" json:"wx_app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WechatTokenReq) Reset()         { *m = WechatTokenReq{} }
func (m *WechatTokenReq) String() string { return proto.CompactTextString(m) }
func (*WechatTokenReq) ProtoMessage()    {}
func (*WechatTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{0}
}
func (m *WechatTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WechatTokenReq.Unmarshal(m, b)
}
func (m *WechatTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WechatTokenReq.Marshal(b, m, deterministic)
}
func (dst *WechatTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WechatTokenReq.Merge(dst, src)
}
func (m *WechatTokenReq) XXX_Size() int {
	return xxx_messageInfo_WechatTokenReq.Size(m)
}
func (m *WechatTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_WechatTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_WechatTokenReq proto.InternalMessageInfo

func (m *WechatTokenReq) GetWxAppId() string {
	if m != nil {
		return m.WxAppId
	}
	return ""
}

type WechatTokenResp struct {
	WxToken              string   `protobuf:"bytes,1,opt,name=wx_token,json=wxToken,proto3" json:"wx_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WechatTokenResp) Reset()         { *m = WechatTokenResp{} }
func (m *WechatTokenResp) String() string { return proto.CompactTextString(m) }
func (*WechatTokenResp) ProtoMessage()    {}
func (*WechatTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{1}
}
func (m *WechatTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WechatTokenResp.Unmarshal(m, b)
}
func (m *WechatTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WechatTokenResp.Marshal(b, m, deterministic)
}
func (dst *WechatTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WechatTokenResp.Merge(dst, src)
}
func (m *WechatTokenResp) XXX_Size() int {
	return xxx_messageInfo_WechatTokenResp.Size(m)
}
func (m *WechatTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_WechatTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_WechatTokenResp proto.InternalMessageInfo

func (m *WechatTokenResp) GetWxToken() string {
	if m != nil {
		return m.WxToken
	}
	return ""
}

type TokenInfo struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	TerminalType         uint32   `protobuf:"varint,2,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	ExpiresAt            uint64   `protobuf:"varint,3,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	UserName             string   `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Session              string   `protobuf:"bytes,5,opt,name=session,proto3" json:"session,omitempty"`
	Scopes               []string `protobuf:"bytes,6,rep,name=scopes,proto3" json:"scopes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenInfo) Reset()         { *m = TokenInfo{} }
func (m *TokenInfo) String() string { return proto.CompactTextString(m) }
func (*TokenInfo) ProtoMessage()    {}
func (*TokenInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{2}
}
func (m *TokenInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenInfo.Unmarshal(m, b)
}
func (m *TokenInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenInfo.Marshal(b, m, deterministic)
}
func (dst *TokenInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenInfo.Merge(dst, src)
}
func (m *TokenInfo) XXX_Size() int {
	return xxx_messageInfo_TokenInfo.Size(m)
}
func (m *TokenInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TokenInfo proto.InternalMessageInfo

func (m *TokenInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *TokenInfo) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *TokenInfo) GetExpiresAt() uint64 {
	if m != nil {
		return m.ExpiresAt
	}
	return 0
}

func (m *TokenInfo) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *TokenInfo) GetSession() string {
	if m != nil {
		return m.Session
	}
	return ""
}

func (m *TokenInfo) GetScopes() []string {
	if m != nil {
		return m.Scopes
	}
	return nil
}

type DecodeReq struct {
	AppId                uint32   `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Token                string   `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecodeReq) Reset()         { *m = DecodeReq{} }
func (m *DecodeReq) String() string { return proto.CompactTextString(m) }
func (*DecodeReq) ProtoMessage()    {}
func (*DecodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{3}
}
func (m *DecodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecodeReq.Unmarshal(m, b)
}
func (m *DecodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecodeReq.Marshal(b, m, deterministic)
}
func (dst *DecodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecodeReq.Merge(dst, src)
}
func (m *DecodeReq) XXX_Size() int {
	return xxx_messageInfo_DecodeReq.Size(m)
}
func (m *DecodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DecodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_DecodeReq proto.InternalMessageInfo

func (m *DecodeReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *DecodeReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type DecodeResp struct {
	TokenInfo            *TokenInfo `protobuf:"bytes,1,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *DecodeResp) Reset()         { *m = DecodeResp{} }
func (m *DecodeResp) String() string { return proto.CompactTextString(m) }
func (*DecodeResp) ProtoMessage()    {}
func (*DecodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{4}
}
func (m *DecodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecodeResp.Unmarshal(m, b)
}
func (m *DecodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecodeResp.Marshal(b, m, deterministic)
}
func (dst *DecodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecodeResp.Merge(dst, src)
}
func (m *DecodeResp) XXX_Size() int {
	return xxx_messageInfo_DecodeResp.Size(m)
}
func (m *DecodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DecodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_DecodeResp proto.InternalMessageInfo

func (m *DecodeResp) GetTokenInfo() *TokenInfo {
	if m != nil {
		return m.TokenInfo
	}
	return nil
}

type TokenInfoV2 struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeviceId             []byte            `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ClientType           uint32            `protobuf:"varint,3,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	TerminalType         uint32            `protobuf:"varint,4,opt,name=terminal_type,json=terminalType,proto3" json:"terminal_type,omitempty"`
	Scopes               []string          `protobuf:"bytes,5,rep,name=scopes,proto3" json:"scopes,omitempty"`
	Extra                map[string]string `protobuf:"bytes,6,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Aud                  []string          `protobuf:"bytes,7,rep,name=aud,proto3" json:"aud,omitempty"`
	AccTtl               uint32            `protobuf:"varint,8,opt,name=acc_ttl,json=accTtl,proto3" json:"acc_ttl,omitempty"`
	RefTtl               uint32            `protobuf:"varint,9,opt,name=ref_ttl,json=refTtl,proto3" json:"ref_ttl,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TokenInfoV2) Reset()         { *m = TokenInfoV2{} }
func (m *TokenInfoV2) String() string { return proto.CompactTextString(m) }
func (*TokenInfoV2) ProtoMessage()    {}
func (*TokenInfoV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{5}
}
func (m *TokenInfoV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenInfoV2.Unmarshal(m, b)
}
func (m *TokenInfoV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenInfoV2.Marshal(b, m, deterministic)
}
func (dst *TokenInfoV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenInfoV2.Merge(dst, src)
}
func (m *TokenInfoV2) XXX_Size() int {
	return xxx_messageInfo_TokenInfoV2.Size(m)
}
func (m *TokenInfoV2) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenInfoV2.DiscardUnknown(m)
}

var xxx_messageInfo_TokenInfoV2 proto.InternalMessageInfo

func (m *TokenInfoV2) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TokenInfoV2) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *TokenInfoV2) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *TokenInfoV2) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *TokenInfoV2) GetScopes() []string {
	if m != nil {
		return m.Scopes
	}
	return nil
}

func (m *TokenInfoV2) GetExtra() map[string]string {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (m *TokenInfoV2) GetAud() []string {
	if m != nil {
		return m.Aud
	}
	return nil
}

func (m *TokenInfoV2) GetAccTtl() uint32 {
	if m != nil {
		return m.AccTtl
	}
	return 0
}

func (m *TokenInfoV2) GetRefTtl() uint32 {
	if m != nil {
		return m.RefTtl
	}
	return 0
}

type GrantedToken struct {
	Type                 TokenType `protobuf:"varint,1,opt,name=type,proto3,enum=token.v2.TokenType" json:"type,omitempty"`
	Token                string    `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	ExpiresIn            int64     `protobuf:"varint,3,opt,name=expires_in,json=expiresIn,proto3" json:"expires_in,omitempty"`
	TokenId              string    `protobuf:"bytes,4,opt,name=token_id,json=tokenId,proto3" json:"token_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GrantedToken) Reset()         { *m = GrantedToken{} }
func (m *GrantedToken) String() string { return proto.CompactTextString(m) }
func (*GrantedToken) ProtoMessage()    {}
func (*GrantedToken) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{6}
}
func (m *GrantedToken) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantedToken.Unmarshal(m, b)
}
func (m *GrantedToken) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantedToken.Marshal(b, m, deterministic)
}
func (dst *GrantedToken) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantedToken.Merge(dst, src)
}
func (m *GrantedToken) XXX_Size() int {
	return xxx_messageInfo_GrantedToken.Size(m)
}
func (m *GrantedToken) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantedToken.DiscardUnknown(m)
}

var xxx_messageInfo_GrantedToken proto.InternalMessageInfo

func (m *GrantedToken) GetType() TokenType {
	if m != nil {
		return m.Type
	}
	return TokenType_UNDEFINED
}

func (m *GrantedToken) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *GrantedToken) GetExpiresIn() int64 {
	if m != nil {
		return m.ExpiresIn
	}
	return 0
}

func (m *GrantedToken) GetTokenId() string {
	if m != nil {
		return m.TokenId
	}
	return ""
}

type GrantTokenReq struct {
	TokenInfo            *TokenInfoV2 `protobuf:"bytes,1,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GrantTokenReq) Reset()         { *m = GrantTokenReq{} }
func (m *GrantTokenReq) String() string { return proto.CompactTextString(m) }
func (*GrantTokenReq) ProtoMessage()    {}
func (*GrantTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{7}
}
func (m *GrantTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantTokenReq.Unmarshal(m, b)
}
func (m *GrantTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantTokenReq.Marshal(b, m, deterministic)
}
func (dst *GrantTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantTokenReq.Merge(dst, src)
}
func (m *GrantTokenReq) XXX_Size() int {
	return xxx_messageInfo_GrantTokenReq.Size(m)
}
func (m *GrantTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GrantTokenReq proto.InternalMessageInfo

func (m *GrantTokenReq) GetTokenInfo() *TokenInfoV2 {
	if m != nil {
		return m.TokenInfo
	}
	return nil
}

type GrantTokenResp struct {
	AccessToken          *GrantedToken `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken         *GrantedToken `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GrantTokenResp) Reset()         { *m = GrantTokenResp{} }
func (m *GrantTokenResp) String() string { return proto.CompactTextString(m) }
func (*GrantTokenResp) ProtoMessage()    {}
func (*GrantTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{8}
}
func (m *GrantTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrantTokenResp.Unmarshal(m, b)
}
func (m *GrantTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrantTokenResp.Marshal(b, m, deterministic)
}
func (dst *GrantTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantTokenResp.Merge(dst, src)
}
func (m *GrantTokenResp) XXX_Size() int {
	return xxx_messageInfo_GrantTokenResp.Size(m)
}
func (m *GrantTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_GrantTokenResp proto.InternalMessageInfo

func (m *GrantTokenResp) GetAccessToken() *GrantedToken {
	if m != nil {
		return m.AccessToken
	}
	return nil
}

func (m *GrantTokenResp) GetRefreshToken() *GrantedToken {
	if m != nil {
		return m.RefreshToken
	}
	return nil
}

type ValidateTokenReq struct {
	Type                 TokenType `protobuf:"varint,1,opt,name=type,proto3,enum=token.v2.TokenType" json:"type,omitempty"`
	Token                string    `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ValidateTokenReq) Reset()         { *m = ValidateTokenReq{} }
func (m *ValidateTokenReq) String() string { return proto.CompactTextString(m) }
func (*ValidateTokenReq) ProtoMessage()    {}
func (*ValidateTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{9}
}
func (m *ValidateTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateTokenReq.Unmarshal(m, b)
}
func (m *ValidateTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateTokenReq.Marshal(b, m, deterministic)
}
func (dst *ValidateTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateTokenReq.Merge(dst, src)
}
func (m *ValidateTokenReq) XXX_Size() int {
	return xxx_messageInfo_ValidateTokenReq.Size(m)
}
func (m *ValidateTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateTokenReq proto.InternalMessageInfo

func (m *ValidateTokenReq) GetType() TokenType {
	if m != nil {
		return m.Type
	}
	return TokenType_UNDEFINED
}

func (m *ValidateTokenReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type ValidateTokenResp struct {
	TokenInfo            *TokenInfoV2 `protobuf:"bytes,1,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ValidateTokenResp) Reset()         { *m = ValidateTokenResp{} }
func (m *ValidateTokenResp) String() string { return proto.CompactTextString(m) }
func (*ValidateTokenResp) ProtoMessage()    {}
func (*ValidateTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{10}
}
func (m *ValidateTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidateTokenResp.Unmarshal(m, b)
}
func (m *ValidateTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidateTokenResp.Marshal(b, m, deterministic)
}
func (dst *ValidateTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidateTokenResp.Merge(dst, src)
}
func (m *ValidateTokenResp) XXX_Size() int {
	return xxx_messageInfo_ValidateTokenResp.Size(m)
}
func (m *ValidateTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidateTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_ValidateTokenResp proto.InternalMessageInfo

func (m *ValidateTokenResp) GetTokenInfo() *TokenInfoV2 {
	if m != nil {
		return m.TokenInfo
	}
	return nil
}

type RevokeOptions struct {
	ExcludeAccessTokens  []string `protobuf:"bytes,1,rep,name=exclude_access_tokens,json=excludeAccessTokens,proto3" json:"exclude_access_tokens,omitempty"`
	ExcludeRefreshTokens []string `protobuf:"bytes,2,rep,name=exclude_refresh_tokens,json=excludeRefreshTokens,proto3" json:"exclude_refresh_tokens,omitempty"`
	DeviceId             string   `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeOptions) Reset()         { *m = RevokeOptions{} }
func (m *RevokeOptions) String() string { return proto.CompactTextString(m) }
func (*RevokeOptions) ProtoMessage()    {}
func (*RevokeOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{11}
}
func (m *RevokeOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeOptions.Unmarshal(m, b)
}
func (m *RevokeOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeOptions.Marshal(b, m, deterministic)
}
func (dst *RevokeOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeOptions.Merge(dst, src)
}
func (m *RevokeOptions) XXX_Size() int {
	return xxx_messageInfo_RevokeOptions.Size(m)
}
func (m *RevokeOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeOptions.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeOptions proto.InternalMessageInfo

func (m *RevokeOptions) GetExcludeAccessTokens() []string {
	if m != nil {
		return m.ExcludeAccessTokens
	}
	return nil
}

func (m *RevokeOptions) GetExcludeRefreshTokens() []string {
	if m != nil {
		return m.ExcludeRefreshTokens
	}
	return nil
}

func (m *RevokeOptions) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type RevokeAllTokensReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Opts                 *RevokeOptions `protobuf:"bytes,2,opt,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RevokeAllTokensReq) Reset()         { *m = RevokeAllTokensReq{} }
func (m *RevokeAllTokensReq) String() string { return proto.CompactTextString(m) }
func (*RevokeAllTokensReq) ProtoMessage()    {}
func (*RevokeAllTokensReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{12}
}
func (m *RevokeAllTokensReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeAllTokensReq.Unmarshal(m, b)
}
func (m *RevokeAllTokensReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeAllTokensReq.Marshal(b, m, deterministic)
}
func (dst *RevokeAllTokensReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeAllTokensReq.Merge(dst, src)
}
func (m *RevokeAllTokensReq) XXX_Size() int {
	return xxx_messageInfo_RevokeAllTokensReq.Size(m)
}
func (m *RevokeAllTokensReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeAllTokensReq.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeAllTokensReq proto.InternalMessageInfo

func (m *RevokeAllTokensReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RevokeAllTokensReq) GetOpts() *RevokeOptions {
	if m != nil {
		return m.Opts
	}
	return nil
}

type RevokeAllTokensResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RevokeAllTokensResp) Reset()         { *m = RevokeAllTokensResp{} }
func (m *RevokeAllTokensResp) String() string { return proto.CompactTextString(m) }
func (*RevokeAllTokensResp) ProtoMessage()    {}
func (*RevokeAllTokensResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{13}
}
func (m *RevokeAllTokensResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RevokeAllTokensResp.Unmarshal(m, b)
}
func (m *RevokeAllTokensResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RevokeAllTokensResp.Marshal(b, m, deterministic)
}
func (dst *RevokeAllTokensResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RevokeAllTokensResp.Merge(dst, src)
}
func (m *RevokeAllTokensResp) XXX_Size() int {
	return xxx_messageInfo_RevokeAllTokensResp.Size(m)
}
func (m *RevokeAllTokensResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RevokeAllTokensResp.DiscardUnknown(m)
}

var xxx_messageInfo_RevokeAllTokensResp proto.InternalMessageInfo

type GetAccessTokenPublicKeysReq struct {
	KeyId                string   `protobuf:"bytes,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccessTokenPublicKeysReq) Reset()         { *m = GetAccessTokenPublicKeysReq{} }
func (m *GetAccessTokenPublicKeysReq) String() string { return proto.CompactTextString(m) }
func (*GetAccessTokenPublicKeysReq) ProtoMessage()    {}
func (*GetAccessTokenPublicKeysReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{14}
}
func (m *GetAccessTokenPublicKeysReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccessTokenPublicKeysReq.Unmarshal(m, b)
}
func (m *GetAccessTokenPublicKeysReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccessTokenPublicKeysReq.Marshal(b, m, deterministic)
}
func (dst *GetAccessTokenPublicKeysReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccessTokenPublicKeysReq.Merge(dst, src)
}
func (m *GetAccessTokenPublicKeysReq) XXX_Size() int {
	return xxx_messageInfo_GetAccessTokenPublicKeysReq.Size(m)
}
func (m *GetAccessTokenPublicKeysReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccessTokenPublicKeysReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccessTokenPublicKeysReq proto.InternalMessageInfo

func (m *GetAccessTokenPublicKeysReq) GetKeyId() string {
	if m != nil {
		return m.KeyId
	}
	return ""
}

type GetAccessTokenPublicKeysResp struct {
	JwkFormatKeys        string   `protobuf:"bytes,1,opt,name=jwk_format_keys,json=jwkFormatKeys,proto3" json:"jwk_format_keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccessTokenPublicKeysResp) Reset()         { *m = GetAccessTokenPublicKeysResp{} }
func (m *GetAccessTokenPublicKeysResp) String() string { return proto.CompactTextString(m) }
func (*GetAccessTokenPublicKeysResp) ProtoMessage()    {}
func (*GetAccessTokenPublicKeysResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{15}
}
func (m *GetAccessTokenPublicKeysResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAccessTokenPublicKeysResp.Unmarshal(m, b)
}
func (m *GetAccessTokenPublicKeysResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAccessTokenPublicKeysResp.Marshal(b, m, deterministic)
}
func (dst *GetAccessTokenPublicKeysResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccessTokenPublicKeysResp.Merge(dst, src)
}
func (m *GetAccessTokenPublicKeysResp) XXX_Size() int {
	return xxx_messageInfo_GetAccessTokenPublicKeysResp.Size(m)
}
func (m *GetAccessTokenPublicKeysResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccessTokenPublicKeysResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccessTokenPublicKeysResp proto.InternalMessageInfo

func (m *GetAccessTokenPublicKeysResp) GetJwkFormatKeys() string {
	if m != nil {
		return m.JwkFormatKeys
	}
	return ""
}

type TokenSeed struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Seed                 string   `protobuf:"bytes,2,opt,name=seed,proto3" json:"seed,omitempty"`
	ExpireTime           uint32   `protobuf:"varint,3,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenSeed) Reset()         { *m = TokenSeed{} }
func (m *TokenSeed) String() string { return proto.CompactTextString(m) }
func (*TokenSeed) ProtoMessage()    {}
func (*TokenSeed) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{16}
}
func (m *TokenSeed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenSeed.Unmarshal(m, b)
}
func (m *TokenSeed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenSeed.Marshal(b, m, deterministic)
}
func (dst *TokenSeed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenSeed.Merge(dst, src)
}
func (m *TokenSeed) XXX_Size() int {
	return xxx_messageInfo_TokenSeed.Size(m)
}
func (m *TokenSeed) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenSeed.DiscardUnknown(m)
}

var xxx_messageInfo_TokenSeed proto.InternalMessageInfo

func (m *TokenSeed) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TokenSeed) GetSeed() string {
	if m != nil {
		return m.Seed
	}
	return ""
}

func (m *TokenSeed) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

type CreateTokenSeedReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Biz                  uint32   `protobuf:"varint,2,opt,name=biz,proto3" json:"biz,omitempty"`
	ReplaceExisted       bool     `protobuf:"varint,3,opt,name=replace_existed,json=replaceExisted,proto3" json:"replace_existed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTokenSeedReq) Reset()         { *m = CreateTokenSeedReq{} }
func (m *CreateTokenSeedReq) String() string { return proto.CompactTextString(m) }
func (*CreateTokenSeedReq) ProtoMessage()    {}
func (*CreateTokenSeedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{17}
}
func (m *CreateTokenSeedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTokenSeedReq.Unmarshal(m, b)
}
func (m *CreateTokenSeedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTokenSeedReq.Marshal(b, m, deterministic)
}
func (dst *CreateTokenSeedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTokenSeedReq.Merge(dst, src)
}
func (m *CreateTokenSeedReq) XXX_Size() int {
	return xxx_messageInfo_CreateTokenSeedReq.Size(m)
}
func (m *CreateTokenSeedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTokenSeedReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTokenSeedReq proto.InternalMessageInfo

func (m *CreateTokenSeedReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateTokenSeedReq) GetBiz() uint32 {
	if m != nil {
		return m.Biz
	}
	return 0
}

func (m *CreateTokenSeedReq) GetReplaceExisted() bool {
	if m != nil {
		return m.ReplaceExisted
	}
	return false
}

type CreateTokenSeedResp struct {
	TokenSeed            *TokenSeed `protobuf:"bytes,1,opt,name=token_seed,json=tokenSeed,proto3" json:"token_seed,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CreateTokenSeedResp) Reset()         { *m = CreateTokenSeedResp{} }
func (m *CreateTokenSeedResp) String() string { return proto.CompactTextString(m) }
func (*CreateTokenSeedResp) ProtoMessage()    {}
func (*CreateTokenSeedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{18}
}
func (m *CreateTokenSeedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTokenSeedResp.Unmarshal(m, b)
}
func (m *CreateTokenSeedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTokenSeedResp.Marshal(b, m, deterministic)
}
func (dst *CreateTokenSeedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTokenSeedResp.Merge(dst, src)
}
func (m *CreateTokenSeedResp) XXX_Size() int {
	return xxx_messageInfo_CreateTokenSeedResp.Size(m)
}
func (m *CreateTokenSeedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTokenSeedResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTokenSeedResp proto.InternalMessageInfo

func (m *CreateTokenSeedResp) GetTokenSeed() *TokenSeed {
	if m != nil {
		return m.TokenSeed
	}
	return nil
}

type DecodeTokenReq struct {
	Token                []byte   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Biz                  uint32   `protobuf:"varint,2,opt,name=biz,proto3" json:"biz,omitempty"`
	RefreshExpire        bool     `protobuf:"varint,3,opt,name=refresh_expire,json=refreshExpire,proto3" json:"refresh_expire,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecodeTokenReq) Reset()         { *m = DecodeTokenReq{} }
func (m *DecodeTokenReq) String() string { return proto.CompactTextString(m) }
func (*DecodeTokenReq) ProtoMessage()    {}
func (*DecodeTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{19}
}
func (m *DecodeTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecodeTokenReq.Unmarshal(m, b)
}
func (m *DecodeTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecodeTokenReq.Marshal(b, m, deterministic)
}
func (dst *DecodeTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecodeTokenReq.Merge(dst, src)
}
func (m *DecodeTokenReq) XXX_Size() int {
	return xxx_messageInfo_DecodeTokenReq.Size(m)
}
func (m *DecodeTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DecodeTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_DecodeTokenReq proto.InternalMessageInfo

func (m *DecodeTokenReq) GetToken() []byte {
	if m != nil {
		return m.Token
	}
	return nil
}

func (m *DecodeTokenReq) GetBiz() uint32 {
	if m != nil {
		return m.Biz
	}
	return 0
}

func (m *DecodeTokenReq) GetRefreshExpire() bool {
	if m != nil {
		return m.RefreshExpire
	}
	return false
}

type DecodeTokenResp struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecodeTokenResp) Reset()         { *m = DecodeTokenResp{} }
func (m *DecodeTokenResp) String() string { return proto.CompactTextString(m) }
func (*DecodeTokenResp) ProtoMessage()    {}
func (*DecodeTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{20}
}
func (m *DecodeTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecodeTokenResp.Unmarshal(m, b)
}
func (m *DecodeTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecodeTokenResp.Marshal(b, m, deterministic)
}
func (dst *DecodeTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecodeTokenResp.Merge(dst, src)
}
func (m *DecodeTokenResp) XXX_Size() int {
	return xxx_messageInfo_DecodeTokenResp.Size(m)
}
func (m *DecodeTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DecodeTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_DecodeTokenResp proto.InternalMessageInfo

func (m *DecodeTokenResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ClearTokenReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearTokenReq) Reset()         { *m = ClearTokenReq{} }
func (m *ClearTokenReq) String() string { return proto.CompactTextString(m) }
func (*ClearTokenReq) ProtoMessage()    {}
func (*ClearTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{21}
}
func (m *ClearTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearTokenReq.Unmarshal(m, b)
}
func (m *ClearTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearTokenReq.Marshal(b, m, deterministic)
}
func (dst *ClearTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearTokenReq.Merge(dst, src)
}
func (m *ClearTokenReq) XXX_Size() int {
	return xxx_messageInfo_ClearTokenReq.Size(m)
}
func (m *ClearTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearTokenReq proto.InternalMessageInfo

func (m *ClearTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ClearTokenResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearTokenResp) Reset()         { *m = ClearTokenResp{} }
func (m *ClearTokenResp) String() string { return proto.CompactTextString(m) }
func (*ClearTokenResp) ProtoMessage()    {}
func (*ClearTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_token_v2_f38f229d170fdd4a, []int{22}
}
func (m *ClearTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearTokenResp.Unmarshal(m, b)
}
func (m *ClearTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearTokenResp.Marshal(b, m, deterministic)
}
func (dst *ClearTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearTokenResp.Merge(dst, src)
}
func (m *ClearTokenResp) XXX_Size() int {
	return xxx_messageInfo_ClearTokenResp.Size(m)
}
func (m *ClearTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearTokenResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*WechatTokenReq)(nil), "token.v2.WechatTokenReq")
	proto.RegisterType((*WechatTokenResp)(nil), "token.v2.WechatTokenResp")
	proto.RegisterType((*TokenInfo)(nil), "token.v2.TokenInfo")
	proto.RegisterType((*DecodeReq)(nil), "token.v2.DecodeReq")
	proto.RegisterType((*DecodeResp)(nil), "token.v2.DecodeResp")
	proto.RegisterType((*TokenInfoV2)(nil), "token.v2.TokenInfoV2")
	proto.RegisterMapType((map[string]string)(nil), "token.v2.TokenInfoV2.ExtraEntry")
	proto.RegisterType((*GrantedToken)(nil), "token.v2.GrantedToken")
	proto.RegisterType((*GrantTokenReq)(nil), "token.v2.GrantTokenReq")
	proto.RegisterType((*GrantTokenResp)(nil), "token.v2.GrantTokenResp")
	proto.RegisterType((*ValidateTokenReq)(nil), "token.v2.ValidateTokenReq")
	proto.RegisterType((*ValidateTokenResp)(nil), "token.v2.ValidateTokenResp")
	proto.RegisterType((*RevokeOptions)(nil), "token.v2.RevokeOptions")
	proto.RegisterType((*RevokeAllTokensReq)(nil), "token.v2.RevokeAllTokensReq")
	proto.RegisterType((*RevokeAllTokensResp)(nil), "token.v2.RevokeAllTokensResp")
	proto.RegisterType((*GetAccessTokenPublicKeysReq)(nil), "token.v2.GetAccessTokenPublicKeysReq")
	proto.RegisterType((*GetAccessTokenPublicKeysResp)(nil), "token.v2.GetAccessTokenPublicKeysResp")
	proto.RegisterType((*TokenSeed)(nil), "token.v2.TokenSeed")
	proto.RegisterType((*CreateTokenSeedReq)(nil), "token.v2.CreateTokenSeedReq")
	proto.RegisterType((*CreateTokenSeedResp)(nil), "token.v2.CreateTokenSeedResp")
	proto.RegisterType((*DecodeTokenReq)(nil), "token.v2.DecodeTokenReq")
	proto.RegisterType((*DecodeTokenResp)(nil), "token.v2.DecodeTokenResp")
	proto.RegisterType((*ClearTokenReq)(nil), "token.v2.ClearTokenReq")
	proto.RegisterType((*ClearTokenResp)(nil), "token.v2.ClearTokenResp")
	proto.RegisterEnum("token.v2.TokenType", TokenType_name, TokenType_value)
	proto.RegisterEnum("token.v2.TokenBizType", TokenBizType_name, TokenBizType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TokenClient is the client API for Token service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TokenClient interface {
	Decode(ctx context.Context, in *DecodeReq, opts ...grpc.CallOption) (*DecodeResp, error)
	GetWechatAccessToken(ctx context.Context, in *WechatTokenReq, opts ...grpc.CallOption) (*WechatTokenResp, error)
	GetWechatApiTicket(ctx context.Context, in *WechatTokenReq, opts ...grpc.CallOption) (*WechatTokenResp, error)
	GrantToken(ctx context.Context, in *GrantTokenReq, opts ...grpc.CallOption) (*GrantTokenResp, error)
	ValidateToken(ctx context.Context, in *ValidateTokenReq, opts ...grpc.CallOption) (*ValidateTokenResp, error)
	RevokeAllTokens(ctx context.Context, in *RevokeAllTokensReq, opts ...grpc.CallOption) (*RevokeAllTokensResp, error)
	GetAccessTokenPublicKeys(ctx context.Context, in *GetAccessTokenPublicKeysReq, opts ...grpc.CallOption) (*GetAccessTokenPublicKeysResp, error)
	// token-cpp
	CreateTokenSeed(ctx context.Context, in *CreateTokenSeedReq, opts ...grpc.CallOption) (*CreateTokenSeedResp, error)
	DecodeToken(ctx context.Context, in *DecodeTokenReq, opts ...grpc.CallOption) (*DecodeTokenResp, error)
	ClearToken(ctx context.Context, in *ClearTokenReq, opts ...grpc.CallOption) (*ClearTokenResp, error)
}

type tokenClient struct {
	cc *grpc.ClientConn
}

func NewTokenClient(cc *grpc.ClientConn) TokenClient {
	return &tokenClient{cc}
}

func (c *tokenClient) Decode(ctx context.Context, in *DecodeReq, opts ...grpc.CallOption) (*DecodeResp, error) {
	out := new(DecodeResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/Decode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenClient) GetWechatAccessToken(ctx context.Context, in *WechatTokenReq, opts ...grpc.CallOption) (*WechatTokenResp, error) {
	out := new(WechatTokenResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/GetWechatAccessToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenClient) GetWechatApiTicket(ctx context.Context, in *WechatTokenReq, opts ...grpc.CallOption) (*WechatTokenResp, error) {
	out := new(WechatTokenResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/GetWechatApiTicket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenClient) GrantToken(ctx context.Context, in *GrantTokenReq, opts ...grpc.CallOption) (*GrantTokenResp, error) {
	out := new(GrantTokenResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/GrantToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenClient) ValidateToken(ctx context.Context, in *ValidateTokenReq, opts ...grpc.CallOption) (*ValidateTokenResp, error) {
	out := new(ValidateTokenResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/ValidateToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenClient) RevokeAllTokens(ctx context.Context, in *RevokeAllTokensReq, opts ...grpc.CallOption) (*RevokeAllTokensResp, error) {
	out := new(RevokeAllTokensResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/RevokeAllTokens", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenClient) GetAccessTokenPublicKeys(ctx context.Context, in *GetAccessTokenPublicKeysReq, opts ...grpc.CallOption) (*GetAccessTokenPublicKeysResp, error) {
	out := new(GetAccessTokenPublicKeysResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/GetAccessTokenPublicKeys", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenClient) CreateTokenSeed(ctx context.Context, in *CreateTokenSeedReq, opts ...grpc.CallOption) (*CreateTokenSeedResp, error) {
	out := new(CreateTokenSeedResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/CreateTokenSeed", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenClient) DecodeToken(ctx context.Context, in *DecodeTokenReq, opts ...grpc.CallOption) (*DecodeTokenResp, error) {
	out := new(DecodeTokenResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/DecodeToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenClient) ClearToken(ctx context.Context, in *ClearTokenReq, opts ...grpc.CallOption) (*ClearTokenResp, error) {
	out := new(ClearTokenResp)
	err := c.cc.Invoke(ctx, "/token.v2.Token/ClearToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TokenServer is the server API for Token service.
type TokenServer interface {
	Decode(context.Context, *DecodeReq) (*DecodeResp, error)
	GetWechatAccessToken(context.Context, *WechatTokenReq) (*WechatTokenResp, error)
	GetWechatApiTicket(context.Context, *WechatTokenReq) (*WechatTokenResp, error)
	GrantToken(context.Context, *GrantTokenReq) (*GrantTokenResp, error)
	ValidateToken(context.Context, *ValidateTokenReq) (*ValidateTokenResp, error)
	RevokeAllTokens(context.Context, *RevokeAllTokensReq) (*RevokeAllTokensResp, error)
	GetAccessTokenPublicKeys(context.Context, *GetAccessTokenPublicKeysReq) (*GetAccessTokenPublicKeysResp, error)
	// token-cpp
	CreateTokenSeed(context.Context, *CreateTokenSeedReq) (*CreateTokenSeedResp, error)
	DecodeToken(context.Context, *DecodeTokenReq) (*DecodeTokenResp, error)
	ClearToken(context.Context, *ClearTokenReq) (*ClearTokenResp, error)
}

func RegisterTokenServer(s *grpc.Server, srv TokenServer) {
	s.RegisterService(&_Token_serviceDesc, srv)
}

func _Token_Decode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).Decode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/Decode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).Decode(ctx, req.(*DecodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Token_GetWechatAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).GetWechatAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/GetWechatAccessToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).GetWechatAccessToken(ctx, req.(*WechatTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Token_GetWechatApiTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WechatTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).GetWechatApiTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/GetWechatApiTicket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).GetWechatApiTicket(ctx, req.(*WechatTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Token_GrantToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrantTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).GrantToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/GrantToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).GrantToken(ctx, req.(*GrantTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Token_ValidateToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).ValidateToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/ValidateToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).ValidateToken(ctx, req.(*ValidateTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Token_RevokeAllTokens_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeAllTokensReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).RevokeAllTokens(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/RevokeAllTokens",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).RevokeAllTokens(ctx, req.(*RevokeAllTokensReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Token_GetAccessTokenPublicKeys_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccessTokenPublicKeysReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).GetAccessTokenPublicKeys(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/GetAccessTokenPublicKeys",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).GetAccessTokenPublicKeys(ctx, req.(*GetAccessTokenPublicKeysReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Token_CreateTokenSeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTokenSeedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).CreateTokenSeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/CreateTokenSeed",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).CreateTokenSeed(ctx, req.(*CreateTokenSeedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Token_DecodeToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecodeTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).DecodeToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/DecodeToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).DecodeToken(ctx, req.(*DecodeTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Token_ClearToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenServer).ClearToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/token.v2.Token/ClearToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenServer).ClearToken(ctx, req.(*ClearTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Token_serviceDesc = grpc.ServiceDesc{
	ServiceName: "token.v2.Token",
	HandlerType: (*TokenServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Decode",
			Handler:    _Token_Decode_Handler,
		},
		{
			MethodName: "GetWechatAccessToken",
			Handler:    _Token_GetWechatAccessToken_Handler,
		},
		{
			MethodName: "GetWechatApiTicket",
			Handler:    _Token_GetWechatApiTicket_Handler,
		},
		{
			MethodName: "GrantToken",
			Handler:    _Token_GrantToken_Handler,
		},
		{
			MethodName: "ValidateToken",
			Handler:    _Token_ValidateToken_Handler,
		},
		{
			MethodName: "RevokeAllTokens",
			Handler:    _Token_RevokeAllTokens_Handler,
		},
		{
			MethodName: "GetAccessTokenPublicKeys",
			Handler:    _Token_GetAccessTokenPublicKeys_Handler,
		},
		{
			MethodName: "CreateTokenSeed",
			Handler:    _Token_CreateTokenSeed_Handler,
		},
		{
			MethodName: "DecodeToken",
			Handler:    _Token_DecodeToken_Handler,
		},
		{
			MethodName: "ClearToken",
			Handler:    _Token_ClearToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "token-v2/token-v2.proto",
}

func init() { proto.RegisterFile("token-v2/token-v2.proto", fileDescriptor_token_v2_f38f229d170fdd4a) }

var fileDescriptor_token_v2_f38f229d170fdd4a = []byte{
	// 1216 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xd9, 0x6e, 0xdb, 0x46,
	0x17, 0x36, 0x25, 0x4b, 0xb6, 0x8e, 0x16, 0x2b, 0x63, 0x3b, 0x66, 0x94, 0x04, 0xbf, 0x7f, 0x06,
	0x59, 0x90, 0xa6, 0x32, 0xa0, 0x26, 0x45, 0xda, 0x5e, 0xb4, 0x8e, 0x4d, 0xa7, 0xaa, 0x50, 0x25,
	0xa5, 0x94, 0x04, 0xe8, 0x0d, 0xc1, 0x90, 0x47, 0x09, 0x23, 0x8a, 0x64, 0x39, 0x23, 0x5b, 0xca,
	0x6d, 0x2f, 0xda, 0x27, 0xe8, 0xab, 0xf4, 0x35, 0xfa, 0x48, 0xc5, 0xcc, 0x70, 0xd3, 0x56, 0x14,
	0xe9, 0xdd, 0xcc, 0x59, 0x3e, 0x9e, 0xf3, 0x9d, 0x65, 0x08, 0x47, 0x2c, 0x18, 0xa3, 0xff, 0xf9,
	0x65, 0xe7, 0x24, 0x39, 0xb4, 0xc3, 0x28, 0x60, 0x01, 0xd9, 0x15, 0xf7, 0xf6, 0x65, 0x47, 0x7b,
	0x04, 0x8d, 0x37, 0x68, 0xbf, 0xb7, 0xd8, 0x90, 0x4b, 0x0c, 0xfc, 0x85, 0xb4, 0xa0, 0x72, 0x35,
	0x33, 0xad, 0x30, 0x34, 0x5d, 0x47, 0x55, 0x8e, 0x95, 0x07, 0x15, 0x63, 0xe7, 0x6a, 0x76, 0x1a,
	0x86, 0x5d, 0x47, 0x7b, 0x04, 0x7b, 0x0b, 0xd6, 0x34, 0x24, 0x37, 0x60, 0xf7, 0x6a, 0x66, 0x0a,
	0xbc, 0xcc, 0x5a, 0xa8, 0xb5, 0x3f, 0x15, 0xa8, 0x88, 0x53, 0xd7, 0x1f, 0x05, 0xe4, 0x08, 0x76,
	0xa6, 0x14, 0xa3, 0x04, 0xb5, 0x6e, 0x94, 0xf9, 0xb5, 0xeb, 0x90, 0x3b, 0x50, 0x67, 0x18, 0x4d,
	0x5c, 0xdf, 0xf2, 0x4c, 0x36, 0x0f, 0x51, 0x2d, 0x08, 0x75, 0x2d, 0x11, 0x0e, 0xe7, 0x21, 0x92,
	0xdb, 0x00, 0x38, 0x0b, 0xdd, 0x08, 0xa9, 0x69, 0x31, 0xb5, 0x78, 0xac, 0x3c, 0xd8, 0x36, 0x2a,
	0xb1, 0xe4, 0x94, 0x91, 0x9b, 0x50, 0x11, 0xe0, 0xbe, 0x35, 0x41, 0x75, 0x5b, 0x84, 0xb1, 0xcb,
	0x05, 0x7d, 0x6b, 0x82, 0x44, 0x85, 0x1d, 0x8a, 0x94, 0xba, 0x81, 0xaf, 0x96, 0x64, 0x84, 0xf1,
	0x95, 0x5c, 0x87, 0x32, 0xb5, 0x83, 0x10, 0xa9, 0x5a, 0x3e, 0x2e, 0x3e, 0xa8, 0x18, 0xf1, 0x4d,
	0x7b, 0x0a, 0x95, 0x73, 0xb4, 0x03, 0x07, 0x39, 0x21, 0x87, 0x50, 0xce, 0xb1, 0x51, 0x37, 0x4a,
	0x16, 0xe7, 0x82, 0x1c, 0x40, 0x49, 0x66, 0x5d, 0x10, 0x98, 0xf2, 0xa2, 0x7d, 0x07, 0x90, 0x78,
	0xd2, 0x90, 0x74, 0x00, 0x84, 0xd8, 0x74, 0xfd, 0x51, 0x20, 0xdc, 0xab, 0x9d, 0xfd, 0x76, 0x42,
	0x7e, 0x3b, 0x25, 0xc7, 0xa8, 0xb0, 0xe4, 0xa8, 0xfd, 0x55, 0x80, 0x6a, 0xaa, 0x78, 0xdd, 0x21,
	0x4d, 0x28, 0x4e, 0xd3, 0x6f, 0xf3, 0x23, 0x4f, 0xd6, 0xc1, 0x4b, 0xd7, 0x46, 0x1e, 0x13, 0xff,
	0x7a, 0xcd, 0xd8, 0x95, 0x82, 0xae, 0x43, 0xfe, 0x07, 0x55, 0xdb, 0x73, 0xd1, 0x67, 0x92, 0xcb,
	0xa2, 0x70, 0x03, 0x29, 0x12, 0x4c, 0xae, 0xd0, 0xbd, 0xbd, 0x86, 0xee, 0x8c, 0x98, 0x52, 0x9e,
	0x18, 0xf2, 0x25, 0x94, 0x70, 0xc6, 0x22, 0x4b, 0xf0, 0x55, 0xed, 0x1c, 0xaf, 0xc9, 0xe5, 0x75,
	0xa7, 0xad, 0x73, 0x13, 0xdd, 0x67, 0xd1, 0xdc, 0x90, 0xe6, 0x3c, 0x09, 0x6b, 0xea, 0xa8, 0x3b,
	0x02, 0x8c, 0x1f, 0x79, 0x3b, 0x58, 0xb6, 0x6d, 0x32, 0xe6, 0xa9, 0xbb, 0xb2, 0x1d, 0x2c, 0xdb,
	0x1e, 0x32, 0x8f, 0x2b, 0x22, 0x1c, 0x09, 0x45, 0x45, 0x2a, 0x22, 0x1c, 0x0d, 0x99, 0xd7, 0x7a,
	0x0a, 0x90, 0x01, 0x73, 0xc4, 0x31, 0xce, 0xe3, 0x96, 0xe3, 0x47, 0x5e, 0x90, 0x4b, 0xcb, 0x9b,
	0x62, 0x52, 0x10, 0x71, 0xf9, 0xba, 0xf0, 0x54, 0xd1, 0x7e, 0x53, 0xa0, 0xf6, 0x3c, 0xb2, 0x7c,
	0x86, 0x8e, 0x08, 0x93, 0xdc, 0x87, 0x6d, 0x91, 0x3a, 0xf7, 0x6e, 0xac, 0x54, 0x84, 0x33, 0x60,
	0x08, 0x83, 0xf5, 0x45, 0xce, 0x37, 0xa3, 0xeb, 0x0b, 0x8a, 0x8b, 0x69, 0x33, 0x76, 0x7d, 0x3e,
	0x12, 0x71, 0xd5, 0x9d, 0xb8, 0x17, 0x77, 0x64, 0x79, 0x1d, 0x4d, 0x87, 0xba, 0x08, 0x24, 0x9d,
	0xb6, 0xc7, 0x6b, 0x3a, 0xe4, 0x70, 0x2d, 0xab, 0xf9, 0x1e, 0xf9, 0x5d, 0x81, 0x46, 0x1e, 0x87,
	0x86, 0xe4, 0x2b, 0xa8, 0x59, 0xb6, 0x8d, 0x94, 0xe6, 0x66, 0xb1, 0xda, 0xb9, 0x9e, 0x41, 0xe5,
	0x09, 0x30, 0xaa, 0xd2, 0x56, 0xb2, 0xf1, 0x0d, 0xd4, 0x23, 0x1c, 0x45, 0x48, 0xdf, 0x9b, 0x59,
	0xb2, 0x9b, 0x7d, 0x6b, 0xb1, 0xb1, 0x1c, 0xf2, 0x9f, 0xa0, 0xf9, 0xda, 0xf2, 0x5c, 0xc7, 0x62,
	0x98, 0x26, 0xf5, 0xdf, 0xe8, 0xd5, 0xba, 0x70, 0x6d, 0x09, 0x92, 0x86, 0x9f, 0x48, 0xd4, 0x1f,
	0x0a, 0xd4, 0x0d, 0xbc, 0x0c, 0xc6, 0xf8, 0x22, 0x64, 0x6e, 0xe0, 0x53, 0xd2, 0x81, 0x43, 0x9c,
	0xd9, 0xde, 0xd4, 0x41, 0x33, 0xcf, 0x17, 0x55, 0x15, 0xd1, 0x9b, 0xfb, 0xb1, 0xf2, 0x34, 0xe3,
	0x87, 0x92, 0xc7, 0x70, 0x3d, 0xf1, 0x59, 0x20, 0x8a, 0xaa, 0x05, 0xe1, 0x74, 0x10, 0x6b, 0x8d,
	0x1c, 0x31, 0x74, 0x71, 0x4c, 0x8b, 0x72, 0x27, 0x25, 0x63, 0xaa, 0x0d, 0x80, 0xc8, 0xb8, 0x4e,
	0x3d, 0x4f, 0xda, 0x73, 0xe2, 0x56, 0x67, 0xfd, 0x33, 0xd8, 0x0e, 0x42, 0x46, 0xe3, 0x92, 0x1c,
	0x65, 0x09, 0x2f, 0x64, 0x65, 0x08, 0x23, 0xed, 0x10, 0xf6, 0x57, 0x40, 0x69, 0xa8, 0x3d, 0x86,
	0x9b, 0xcf, 0x91, 0xe5, 0x32, 0x7a, 0x39, 0x7d, 0xeb, 0xb9, 0x76, 0x0f, 0xe7, 0x34, 0xde, 0x6f,
	0x63, 0x9c, 0x67, 0xdb, 0xbe, 0x34, 0xc6, 0x79, 0xd7, 0xd1, 0x2e, 0xe0, 0xd6, 0x66, 0x2f, 0x1a,
	0x92, 0x7b, 0xb0, 0xf7, 0xe1, 0x6a, 0x6c, 0x8e, 0x82, 0x68, 0x62, 0x31, 0x73, 0x8c, 0x73, 0x1a,
	0xfb, 0xd7, 0x3f, 0x5c, 0x8d, 0x2f, 0x84, 0x94, 0xdb, 0x6a, 0x46, 0xfc, 0x08, 0x0c, 0x10, 0x9d,
	0x35, 0x09, 0x12, 0xd8, 0xa6, 0x88, 0x4e, 0xdc, 0x01, 0xe2, 0xcc, 0x77, 0x98, 0x9c, 0x26, 0x93,
	0xb9, 0x93, 0x74, 0x87, 0x49, 0xd1, 0xd0, 0x9d, 0xa0, 0x66, 0x01, 0x39, 0x8b, 0x30, 0xe9, 0x0f,
	0x8e, 0xbc, 0x9e, 0xbd, 0x26, 0x14, 0xdf, 0xba, 0x1f, 0xe3, 0x07, 0x85, 0x1f, 0xc9, 0x7d, 0xd8,
	0x8b, 0x30, 0xf4, 0x2c, 0x1b, 0x4d, 0x9c, 0xb9, 0x94, 0xa1, 0x2c, 0xcd, 0xae, 0xd1, 0x88, 0xc5,
	0xba, 0x94, 0x6a, 0x5d, 0xd8, 0x5f, 0xf9, 0x44, 0x7e, 0xa3, 0x8b, 0xa0, 0xd7, 0x6f, 0x74, 0x61,
	0x2c, 0x9b, 0x90, 0x1f, 0x35, 0x13, 0x1a, 0xf2, 0x4d, 0x48, 0x07, 0x24, 0xed, 0x7b, 0x45, 0x6c,
	0xef, 0x78, 0xad, 0xac, 0x46, 0x7b, 0x17, 0x1a, 0x49, 0xc3, 0xc9, 0xec, 0xe3, 0x60, 0x93, 0x79,
	0xd5, 0x85, 0x50, 0xbb, 0x03, 0x7b, 0x0b, 0x1f, 0xa0, 0xe1, 0x2a, 0x17, 0xda, 0xff, 0xa1, 0x7e,
	0xe6, 0xa1, 0x15, 0xa5, 0x41, 0xac, 0x9a, 0x34, 0xa1, 0x91, 0x37, 0xa1, 0xe1, 0xc3, 0x6f, 0xe3,
	0xe2, 0x89, 0x47, 0xa1, 0x0e, 0x95, 0x57, 0xfd, 0x73, 0xfd, 0xa2, 0xdb, 0xd7, 0xcf, 0x9b, 0x5b,
	0xa4, 0x09, 0xb5, 0xd3, 0xb3, 0x33, 0x7d, 0x30, 0x30, 0x87, 0x2f, 0x7a, 0x7a, 0xbf, 0xa9, 0x90,
	0x6b, 0x50, 0x37, 0xf4, 0x0b, 0x43, 0x1f, 0x7c, 0x1f, 0x8b, 0x0a, 0x0f, 0x7b, 0x50, 0x13, 0x00,
	0xcf, 0xdc, 0x8f, 0x43, 0x39, 0xf1, 0xcd, 0xe4, 0x6e, 0xbe, 0xea, 0xf7, 0xfa, 0x2f, 0xde, 0xf4,
	0x25, 0x54, 0x2a, 0x1d, 0x9c, 0xf7, 0x9a, 0x0a, 0xd9, 0x87, 0xbd, 0x54, 0x32, 0x1c, 0x9a, 0x6f,
	0xf4, 0x67, 0xcd, 0x42, 0xe7, 0xd7, 0x32, 0x94, 0xe4, 0xca, 0x7a, 0x02, 0x65, 0x99, 0x31, 0xc9,
	0x91, 0x9f, 0x3e, 0xd9, 0xad, 0x83, 0x55, 0x21, 0x0d, 0xb5, 0x2d, 0xf2, 0x23, 0x1c, 0x3c, 0x47,
	0x26, 0x7f, 0x61, 0x72, 0x9d, 0x4d, 0xd4, 0xcc, 0x7e, 0xf1, 0x6f, 0xa8, 0x75, 0x63, 0x83, 0x46,
	0xc0, 0xf5, 0x80, 0x64, 0x70, 0xa1, 0x3b, 0x74, 0xed, 0x31, 0xb2, 0x4f, 0x05, 0x3b, 0x05, 0xc8,
	0x56, 0x3a, 0x39, 0x5a, 0x5a, 0xbe, 0x29, 0x86, 0xba, 0x5e, 0x21, 0x20, 0x7e, 0x80, 0xfa, 0xc2,
	0xe2, 0x24, 0xad, 0xcc, 0x78, 0x79, 0x49, 0xb7, 0x6e, 0x6e, 0xd4, 0x09, 0xac, 0x97, 0xb0, 0xb7,
	0xb4, 0x4b, 0xc8, 0xad, 0xe5, 0xed, 0x93, 0xdf, 0x5d, 0xad, 0xdb, 0xff, 0xa0, 0x15, 0x88, 0x63,
	0x50, 0x37, 0x2d, 0x14, 0x72, 0x37, 0x97, 0xd5, 0xe6, 0x55, 0xd5, 0xba, 0xf7, 0x6f, 0xcc, 0x92,
	0xf0, 0x97, 0xc6, 0x37, 0x1f, 0xfe, 0xea, 0xf2, 0xc8, 0x87, 0xbf, 0x66, 0xee, 0xb5, 0x2d, 0x72,
	0x0e, 0xd5, 0xdc, 0x90, 0xe5, 0xab, 0xbc, 0x38, 0xdc, 0xf9, 0x2a, 0x2f, 0x4d, 0xa5, 0xac, 0x72,
	0x36, 0x62, 0xf9, 0x2a, 0x2f, 0xcc, 0x66, 0xbe, 0xca, 0x8b, 0x13, 0xa9, 0x6d, 0x3d, 0x7b, 0xf4,
	0xf3, 0xc3, 0x77, 0x81, 0x67, 0xf9, 0xef, 0xda, 0x4f, 0x3a, 0x8c, 0xb5, 0xed, 0x60, 0x72, 0x22,
	0xfe, 0xea, 0xed, 0xc0, 0x3b, 0xa1, 0x18, 0xf1, 0x07, 0x86, 0x9e, 0x24, 0xee, 0x6f, 0xcb, 0x42,
	0xf7, 0xc5, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x91, 0x47, 0x82, 0x4d, 0x0c, 0x0c, 0x00, 0x00,
}
